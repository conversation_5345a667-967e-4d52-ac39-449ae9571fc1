{"/funnel/apprenant/proposition/": {"target": "https://localhost/funnel/apprenant/proposition", "pathRewrite": {"^/funnel/apprenant/proposition": ""}, "secure": false, "logLevel": "debug"}, "/funnel/api/": {"target": "https://localhost/funnel/api", "pathRewrite": {"^/funnel/api": ""}, "secure": false, "logLevel": "debug"}, "/f/p/": {"target": "https://localhost:8000/f/p", "pathRewrite": {"^/f/p": ""}, "secure": false, "logLevel": "debug"}, "/api/": {"target": "https://localhost:8000/api", "pathRewrite": {"^/api": ""}, "secure": false, "logLevel": "debug"}, "/app/": {"target": "https://localhost:8000/app", "pathRewrite": {"^/app": ""}, "secure": false, "logLevel": "debug"}, "/files/publicFile/": {"target": "http://localhost/data/publicFiles", "pathRewrite": {"^/files/publicFile": ""}, "secure": false, "logLevel": "debug"}}