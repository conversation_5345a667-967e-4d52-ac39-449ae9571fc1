<html>
<body>
<h1>Wedof lib</h1>
<!--<wedof-passport-button certifier="18750002000073"
                       forceCertifierAccess="true"
                       data="eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.CXOuTeyVbQQNXg56A7R4aiTrendFliQmb4JApFxSDvs"></wedof-passport-button>-->
<p>
    <span>Wedof reseller connector button</span>
    <wedof-reseller-connector-button reseller="79758345700024" connection="cpf" data="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************.7MYBeyvIxbhKYUElsafrMpPFKqsm20H7rAurjymbP8c">
    </wedof-reseller-connector-button></p>
<p>
    <span>Wedof resellerCustomerStatus function result</span>
    <span id="resellerCustomerStatus"></span>
</p>
<script type="application/javascript" id="wedof-script" src="https://www.wedof.fr/assets/cli/wedof-v1.js"></script>
</body>
<!--<script type="application/javascript">
    Wedof.passportCandidateStatus("79758345700024", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************.Ok-eP0bDb7Om2pNTsIr0eZ0QdKgbUP8x0_L-8lLVfd8").then(
        result => {
            console.log(result);
            document.getElementById("resellerCustomerStatus").innerText = JSON.stringify(result);
        }
    );
</script>-->
</html>

<html>
<body>
<h1>Wedof lib</h1>
<wedof-passport-button certifier="12345678901234" data="jwtvalue"></wedof-passport-button>
<script type="application/javascript" id="wedof-script" src="https://www.wedof.fr/assets/cli/wedof-v1.js"></script>
<p>
    <span>Wedof passportCandidateStatus function result</span>
    <span id="result"></span>
</p>
</body>
<script type="application/javascript">
    Wedof.passportCandidateStatus("12345678901234" /*siret*/, "jwtvalue").then(
        result => {
            console.log(result);
            document.getElementById("result").innerText = JSON.stringify(result);
        }
    );
</script>
</html>
