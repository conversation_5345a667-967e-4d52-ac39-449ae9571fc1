{"name": "@treo/starter", "version": "1.0.2", "license": "https://themeforest.net/licenses/standard", "scripts": {"ng": "ng", "start:old": "node --max_old_space_size=6144 ./node_modules/@angular/cli/bin/ng serve", "start:mem": "node --openssl-legacy-provider --max_old_space_size=6144 ./node_modules/@angular/cli/bin/ng serve", "start:dev": "npm run start:mem -- --ssl --proxy-config proxy.conf.dev.json --open", "start:local": "npm run start:mem -- --ssl --proxy-config proxy.conf.local.json --open", "start:subdomain": "npm run start:mem -- --live-reload=true --host kagilum.localhost --disableHostCheck true --ssl --proxy-config proxy.conf.subdomain.json --open", "build": "ng build", "build:prod": "ng build --prod --output-hashing all", "build:staging": "ng build --prod --configuration=staging", "postbuild": "mv ./dist/wedof-v1.min.js  ./dist/assets/cli/", "postbuild:prod": "npm run postbuild", "postbuild:staging": "npm run postbuild", "lint": "ng lint", "tw": "npm run tw:build && npm run tw:export", "tw:build": "./node_modules/.bin/tailwind build src/tailwind/main.css -c src/tailwind/config.js -o src/styles/tailwind.scss", "tw:export": "npm run tw:export:js && npm run tw:export:scss", "tw:export:js": "node src/@treo/tailwind/export.js -c src/tailwind/config.js -o src/@treo/tailwind/exported/variables.ts", "tw:export:scss": "./node_modules/.bin/tailwind build src/@treo/tailwind/export.css -c src/tailwind/config.js -o src/@treo/tailwind/exported/_variables.scss", "tsc": "tsc -p tsconfig.app.json --noEmit --skipLib<PERSON><PERSON>ck"}, "private": true, "dependencies": {"@angular-material-components/datetime-picker": "^4.0.7", "@angular-material-components/moment-adapter": "^4.0.0", "@angular/animations": "10.2.5", "@angular/cdk": "10.2.7", "@angular/common": "10.2.5", "@angular/compiler": "10.2.5", "@angular/core": "10.2.5", "@angular/forms": "10.2.5", "@angular/material": "10.2.7", "@angular/material-moment-adapter": "10.2.7", "@angular/platform-browser": "10.2.5", "@angular/platform-browser-dynamic": "10.2.5", "@angular/service-worker": "10.2.5", "@angular/router": "10.2.5", "@material-extended/mde": "^3.0.3", "@ngx-translate/core": "^13.0.0", "@ngx-translate/http-loader": "^6.0.0", "@ngxs/logger-plugin": "3.7.6", "@ngxs/store": "3.7.6", "@placeme/ngx-geo-api-gouv-address": "^1.0.2", "angular-code-input": "^1.6.0", "apexcharts": "3.24.0", "crypto-js": "3.3.0", "file-saver": "^2.0.5", "geojson": "^0.5.0", "highlight.js": "10.4.1", "lodash-es": "4.17.21", "moment": "2.29.4", "moment-business": "3.0.1", "ng-apexcharts": "1.5.12", "ng-mat-select-infinite-scroll": "^2.1.1", "ngx-clipboard": "^14.0.1", "ngx-extended-pdf-viewer": "^10.0.0-alpha.1", "ngx-markdown": "^10.1.1", "ngx-mat-select-search": "^3.3.0", "ngx-quill": "12.0.1", "perfect-scrollbar": "1.5.0", "quill": "1.3.7", "rxjs": "6.5.5", "ts-md5": "^1.2.9", "tslib": "2.6.2", "web-animations-js": "2.3.2", "zone.js": "0.10.3"}, "devDependencies": {"@angular-devkit/build-angular": "^0.1002.4", "@angular/cli": "^10.2.4", "@angular/compiler-cli": "10.2.5", "@angular/language-service": "10.2.5", "@types/crypto-js": "3.1.47", "@types/highlight.js": "9.12.4", "@types/lodash-es": "4.17.3", "@types/node": "17.0.21", "codelyzer": "6.0.2", "husky": "^4.3.8", "lint-staged": "14.0.1", "lodash": "4.17.21", "ngx-infinite-scroll": "10.0.0", "tailwindcss": "3.3.3", "ts-node": "10.9.1", "tslint": "6.1.3", "typescript": "4.0.5"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "scarfSettings": {"enabled": false}}