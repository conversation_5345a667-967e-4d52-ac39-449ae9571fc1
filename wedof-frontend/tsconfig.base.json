{"compileOnSave": false, "compilerOptions": {"resolveJsonModule": true, "allowSyntheticDefaultImports": true, "baseUrl": "./src", "outDir": "./dist/out-tsc", "sourceMap": true, "declaration": false, "downlevelIteration": true, "experimentalDecorators": true, "module": "esnext", "moduleResolution": "node", "importHelpers": true, "strict": false, "target": "es2015", "typeRoots": ["node_modules/@types"], "lib": ["es2018", "dom"]}, "angularCompilerOptions": {"fullTemplateTypeCheck": true, "strictInjectionParameters": true}}