# Fonctionnement du Multiselect
## Structure du kanban
![](kanbanMultiselect.png)

## Positionnement des checkboxes
Les checckboxs des cards sont dans le component `certification-folder-kanban-card` tandis que les checkboxs des colonnes se situent dans le composant générique `kanban-board` et n'est affiché que si l'input `withMultiselect = true`

## Gestion des hover
La gestion se fait à deux niveaux :
- D'abord sur chaque colonne dans le composant `kanban-board` en mettant directement à jour un tableau de checkboxs visibles par statut.
- Ensuite sur le `kanban-card-wrapper`, avec les deux events `mouseenter` et `mouseleave`, pour que les objets métier génériques (les CertificationFolders ici) soient immédiatement renvoyés en output pour alimenter des Observables vers un slot dans le composant `certification-folder-kanban-card` via le composant `kanban-board`. 

![](multiselecthover.drawio.png)

## Gestion de la visibilité des checkboxes

Un Subject global à la visibilité de toutes les checkbox est créé dans le composant `kanban-certification-folders` et est basé sur la somme de tous les totaux d'éléments sélectionnés par colonne.

A chaque mise à jour de cette valeur, les écouteurs dans les composants `certification-folder-kanban-card` et `kanban-board` vont mettre à jour une propriété locale de visibilité (`checkboxVisible` ou `allCheckboxesVisible`) qui va permettre d'afficher la checkbox en toute circonstance quand c'est `true`.

Si le `allVisible` est déterminé à `false`, alors la condition va être définie par la combinaison du mousehover/mouseleave et la value de la checkbox (`[(ngModel)]`).

## Gestion des fenêtres de dialogue de mise à jour multiple

Le menu d'une colonne est affiché dans le cas d'une sélection totale ou partielle de celle-ci et transmise du composant `kanban-certification-folder` au composant `kanban-board` par le biais du slot `kanbanHeaderMenuTemplate`.

Le menu est repris de la vue tableau et agit sur le kanban par le biais des events `processedFolder` et `allFoldersProcessed`.

/!\ - La gestion des update massif par filtres est encore à gérer.

## Les `Subject` vs Les `BehaviorSubject`

Les deux types d'observables ont des comportements différents et le choix de ceux-ci a été déterminé pour les différentes souscriptions sur le multiselect.

Le BehaviorSubject a la particularité supplémentaire de garder le dernier enregistrement pour les nouvelles inscriptions.

### Les `Subject`

- **<u>cardEnter & cardLeaver</u>**: On veut que lorsqu'une nouvelle carte est créée ou remontée de l'infinite scroll, que la checkbox soit automatique considérée comme non hover, donc on évite d'avoir un comportement de BehaviorSubject.
- **<u>selectFullColumnAction & unselectFullColumnAction</u>**: De la même manière, si on utilise un BehaviorSubject, des comportements non souhaités peuvent survenir au scroll.
- **<u>emptyColumn</u>**: Cet event fait un reset de la sélection donc on n'a pas besoin de mémoriser le dernier comportement.

### Les `BehaviorSubject`

- **<u>allVisible</u>**: Le fait de l'avoir en BehaviorSubject permet de garder l'affichage des checkbox lors des différentes mises à jour des cartes
