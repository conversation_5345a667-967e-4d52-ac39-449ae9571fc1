pipeline {
    agent any
    environment {
        BRANCH = 'master'
        CREDENTIALS = 'f4f41eac-73e5-4d5c-9264-5ef5eb93e8eb'
    }
    stages {
        stage('Checkout') {
            steps {
                git branch: "$BRANCH", credentialsId: "$CREDENTIALS", url: '**************:w-edof/wedof-frontend.git'
            }
        }
        stage('Install') {
            steps {
                nodejs(nodeJSInstallationName: 'NodeJS') {
                    sh 'npm ci'
                }
            }
        }
        stage('Lint') {
            steps {
                nodejs(nodeJSInstallationName: 'NodeJS') {
                    sh 'npm run lint'
                }
            }
        }
        stage('Build') {
            steps {
                nodejs(nodeJSInstallationName: 'NodeJS') {
                    sh 'npm run build:prod'
                }
            }
        }
    }
    post {
        success {
            archiveArtifacts artifacts: 'dist/**/*.*'
        }
    }
}
