pipeline {
    agent any
    environment {
        IP = '**************'
        DIRECTORY = '/home/<USER>/wedof.fr/wedof-frontend'
        DIRECTORY_BACKEND = '/home/<USER>/wedof.fr/wedof-backend'
        FILE_INDEX_ANGULAR = '/home/<USER>/wedof.fr/wedof-frontend/dist/index.html';
        FILE_INDEX_SSR = '/home/<USER>/wedof.fr/wedof-backend/templates/funnel/index.html.twig';
    }
    stages {
        stage('Get dist from build') {
            steps {
                copyArtifacts(projectName: 'front-prod-build');
            }
        }
        stage('deploy') {
            steps {
                sh """ssh root@$IP 'cd $DIRECTORY; rm -rf dist.new'"""
                sh """ssh root@$IP 'cd $DIRECTORY; su web -c "mkdir dist.new"'"""
                sh "scp -r dist/* root@$IP:$DIRECTORY/dist.new"
                sh """ssh root@$IP 'cd $DIRECTORY; chown -R web:www-data .'"""
                sh """ssh root@$IP 'cd $DIRECTORY; rm -rf dist.bak'"""
                sh """ssh root@$IP 'cd $DIRECTORY; su web -c "cp -r dist dist.bak"'"""
                sh """ssh root@$IP 'cd $DIRECTORY; rm -rf dist'"""
                sh """ssh root@$IP 'cd $DIRECTORY; su web -c "mv dist.new dist"'"""
                sh """ssh root@$IP 'service nginx restart'"""
            }
        }
        stage('SSR Proposal index') {
            steps {
                sh """ssh root@$IP bash << EOF
                cp $FILE_INDEX_ANGULAR $FILE_INDEX_SSR
                sed -i "s/Wedof/{{ data.title }}/g" $FILE_INDEX_SSR
                sed -i "s/<\\/title>/<\\/title>\\n   {\\% include \\'shared\\/_meta.html.twig' \\%}/g" $FILE_INDEX_SSR
                cd $DIRECTORY_BACKEND
                su web -c "php bin/console cache:clear --env=prod"
                chmod 777 $DIRECTORY_BACKEND/var -R
                service php7.4-fpm restart
EOF""" // do not INDENT it will fail and you will lost your mind
            }
        }
    }
}
