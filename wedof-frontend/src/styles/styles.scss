// -----------------------------------------------------------------------------------------------------
// @ You can use this file to import custom styles.
//
// @ Styles from this file will override anything from 'vendors.scss' file allowing customizations and
//  modifications of third party libraries.
// -----------------------------------------------------------------------------------------------------
@import '~@angular/cdk/overlay-prebuilt.css';
@import '../@treo/tailwind/exported/variables'; // Ugly but I like it
@import '../@treo/styles/utilities/breakpoints'; // Ugly but I like it

a {
    text-decoration: underline;
}

header {
    h1 {
        margin: 0;

        & + p {
            color: #64748b;
            margin-left: 2px;
            font-weight: 500;
        }
    }
}

.subtitle {
    color: #64748b;
    margin-left: 2px;
    font-weight: 500;
}

mat-form-field.mat-form-field.table-search {
    display: block;
    width: 100%;

    .mat-form-field-wrapper {
        margin: 0;

        .mat-form-field-flex {
            border: none;
            border-radius: 0;
        }
    }
}

mat-drawer-container.mat-drawer-container {
    height: 100%;

    mat-drawer.mat-drawer {
        height: 100%;
        width: 100%;
        max-width: 600px;
        transition: width 0.2s ease-in-out;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        &.full-width {
            max-width: calc(100% - 60px);
        }

        .mat-drawer-inner-container {
            overflow: hidden;
            display: flex;
            flex-direction: column;
            border-left: 1px solid rgba(151, 166, 186, 0.38);
        }

        aside {
            flex: 1 1 auto;
            overflow: auto;
            display: flex;
            flex-direction: column;
        }
    }

    mat-drawer-content.mat-drawer-content {
        height: 100%;
        overflow: hidden;
        display: flex;
        flex-direction: column;

        main {
            display: flex;
            overflow: auto;
            flex: 1 1 auto;
        }
    }
}

[mat-table] {
    width: 100%;
    border-collapse: separate;

    th {
        font-size: 14px;
    }

    tr.mat-row.selected {
        background-color: #E5EDFF;
    }

    .mat-cell {
        padding-right: 16px;
    }

    tr.mat-row {
        height: 3.5rem;
    }

    tr.mat-row:not(.selected):not(.mat-row-static):hover {
        cursor: pointer;
        background-color: #f3f5f7;
    }

    tr:nth-child(even) {
        background-color: #fafafa;
    }

    .synchro-icons {
        height: 35px;
        width: 35px;
    }

    .sticky-section-title {
        z-index: 200;
        position: sticky;
        top: 0;
        background-color: #f0f0f0;
    }

    .sticky-searchbar {
        top: 0;
        position: sticky;
        height: 50px;
        z-index: 50;
        border-width: 1px;
    }

    .sticky-tableheader {
        top: 50px;
        position: sticky;
        z-index: 50;
        height: 3.5rem;
    }

    .collapsable-row:nth-child(odd) {
        background-color: #f5f5f5 !important;
    }

    .collapsable-row:nth-child(even) {
        background-color: #ffffff !important;
    }
}

app-paginator {
    .mat-paginator-range-label {
        width: 100px;
    }
}

treo-message {
    .treo-message-container {
        min-height: 40px !important;
        padding: 0 24px !important;
    }
}

@media (max-width: 700px) {
    .table {
        th,
        td,
        .mat-expansion-panel-header,
        .mat-accordion .mat-expansion-panel .mat-expansion-panel-header {
            font-size: 0.75rem;
        }
    }
}

//Specific class for a modal without padding
.no-padding-panel {
    .mat-dialog-container {
        padding: 0 !important;
    }
}

@mixin full-page-scroll($width) {
    overflow-y: scroll !important;
    width: 100% !important;
    max-width: none !important;
    height: 100% !important;
    max-height: unset !important;
    .mat-dialog-container {
        @include treo-breakpoint('gt-md') {
            max-width: $width !important;
        }
        @include treo-breakpoint('md') {
            max-width: ($width + ((100vw - $width) * 0.3)) !important;
        }
        @include treo-breakpoint('sm') {
            max-width: ($width + ((100vw - $width) * 0.6)) !important;
        }
        @include treo-breakpoint('xs') {
            max-width: 100vw !important;
        }
        margin: auto !important;
        overflow-y: hidden !important;
        height: auto !important;
        min-height: auto !important;
    }
}

.full-page-scroll-30 {
    @include full-page-scroll(30vw);
}

.full-page-scroll-40 {
    @include full-page-scroll(40vw);
}

.full-page-scroll-50 {
    @include full-page-scroll(50vw);
}

.full-page-scroll-80 {
    @include full-page-scroll(80vw);
}

.toolbar-title {
    width: 425px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.toolbar-title-small {
    width: 425px;
    overflow: hidden;
    white-space: normal;
    font-size: 1rem;
    line-height: normal;
}

.no-table {
    border-top: 1px solid #cfd8e7;
    border-bottom: 1px solid #cfd8e7;
    font-weight: 400;
    text-align: center;
}

.border-like-button {
    border: 1px solid #e2e8f0;
    border-radius: 5px;
    background-color: white;
}

button.open-panel:hover, button.close-panel:hover {
    background-color: #e2e8f0;
}

.card-loading {
    &.pb-0, &.pt-3 {
        padding-top: 20px !important;
        padding-bottom: 20px !important;
    }

    .card-loading-hidden, .grid > app-form-field-static .app-form-field-static-loading-hidden, .flex-col > app-form-field-static .app-form-field-static-loading-hidden {
        display: none !important;
    }

    .card-loading-show, .grid > app-form-field-static .app-form-field-static-loading-show, .flex-col > app-form-field-static .app-form-field-static-loading-show {
        position: relative;
        overflow: hidden;
        background-color: #e2e8f0 !important;
        border-radius: 4px;
        border-width: 0 !important;
        color: transparent;
        pointer-events: none;

        * {
            color: transparent !important;
            pointer-events: none;
        }

        &.mat-icon {
            color: transparent !important;
        }

        .mat-icon-button svg {
            visibility: hidden !important;
        }

        .treo-message-container {
            background: transparent !important;
            color: transparent !important;
            box-shadow: inset 0 0 0 1px transparent !important;
        }

        &::after {
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            transform: translateX(-100%);
            background-image: linear-gradient(
                    90deg,
                    rgba(255, 255, 255, 0) 0,
                    rgba(255, 255, 255, 0.2) 20%,
                    rgba(255, 255, 255, 0.5) 60%,
                    rgba(255, 255, 255, 0)
            );
            animation: shimmer 3s infinite;
            content: '';
        }
    }

    @keyframes shimmer {
        100% {
            transform: translateX(100%);
        }
    }
}

.mat-optgroup .mat-option.mat-option-multiple {
    padding-left: 32px;
}

.mat-tooltip {
    font-size: 13px;
}

.mat-menu-panel.large-menu {
    max-width: 700px !important;
}

// Dropdowns: show full options content in multiline instead of default truncating + ellipsis
.mat-select-panel mat-option.mat-option {
    height: unset;
    padding-top: 0.7em;
    padding-bottom: 0.7em;

    .mat-option-text.mat-option-text {
        white-space: normal;
        line-height: 1.5em;
    }
}

.mat-checkbox.checkbox-dropdown .mat-checkbox-layout .mat-checkbox-inner-container {
    margin-right: 2px;
}

$mat-icon-small-size: 20px;
.mat-icon.icon-small {
    width: $mat-icon-small-size;
    height: $mat-icon-small-size;
    min-width: $mat-icon-small-size;
    min-height: $mat-icon-small-size;
    font-size: $mat-icon-small-size;
    line-height: $mat-icon-small-size;

    svg {
        width: $mat-icon-small-size;
        height: $mat-icon-small-size;
    }
}

.treo-message-wedof .treo-message-container {
    flex: none;
    width: 100%;
    border-radius: 0 !important;
    box-shadow: none !important;
}

.treo-theme-light .mat-badge-small.mat-badge-below .mat-badge-content {
    bottom: -6px !important;
}

button.grey-button {
    background-color: rgba(151, 166, 186, 0.38) !important;
}

.mat-flat-button {
    &.button-actions {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        border-right: none;
    }

    &.button-actionsOnly {
        border-top-right-radius: 5px;
        border-bottom-right-radius: 5px;
    }

    &.button-arrow {
        border-radius: 0 5px 5px 0;
        padding-left: 2px;
        padding-right: 3px;
        width: 30.09px !important;
        min-width: unset !important;

        &[disabled] {
            background-color: rgba(151, 166, 186, 0.38);
            border-left: 1px solid #97a6ba;
        }

        &.button-arrow-default {
            border-left: 1px solid #D2D6DC; // gray-300
        }

        &.button-arrow-primary {
            border-left: 1px solid #362F78; // Indigo 900
        }

        &.button-arrow-orange {
            border-left: 1px solid #D03801; // orange-600
        }

        &.button-arrow-warn {
            border-left: 1px solid #a71818;
        }

        &.button-arrow-orange, &.button-arrow-primary, &.button-arrow-warn {
            .icon {
                color: white !important;
            }
        }
    }
}

.sticky-header {
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: white;
}
