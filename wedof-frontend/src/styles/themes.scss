// @formatter:off

// -----------------------------------------------------------------------------------------------------
// @ Treo themes map. Do NOT rename the '$treo-themes' map name!!!
//   This file meant to be imported more than once by @treo, do NOT put anything heavy here!
//   The map contains Angular Material themes that will be generated for your app.
//
// @ Each value of the map is an Angular Material theme, generated and modified by using
//   'treo-light-theme' and/or 'treo-dark-theme' functions which uses 'mat-light-theme' and
//   'mat-dark-theme' functions respectively to generate the themes.
//
// @ 'treo-palette' values are coming from '$treo-color' map which can be found in
//   '@treo/styles/utilities/_colors.scss'.
// -----------------------------------------------------------------------------------------------------
$treo-themes: (

    // -----------------------------------------------------------------------------------------------------
    // @ User themes - DO NOT REMOVE or RENAME
    //   These are main color themes for Treo. You can modify the palette colors for your own needs and
    //   likings.
    // -----------------------------------------------------------------------------------------------------

    // Dark theme - DO NOT REMOVE or RENAME
    'treo-theme-dark': treo-dark-theme(
        treo-palette('teal'),
        treo-palette('pink', 500),
        treo-palette('red', 400)
    ),

    // Light theme - DO NOT REMOVE or RENAME
    'treo-theme-light': treo-light-theme(
        treo-palette('indigo', 600),
        treo-palette('cool-gray', 800),
        treo-palette('red', 700)
    ),

    // -----------------------------------------------------------------------------------------------------
    // @ Dark & Light themes - DO NOT REMOVE or RENAME
    //   These are generic themes which are useful if you want to adjust the theming of certain container
    //   or an element independently from the app's global theme. You can modify the palette colors for your
    //   own needs and likings.
    //
    // @ Example: If you want to have a dark toolbar in your light themed app, adding '.theme-dark' class
    //   to the toolbar will apply a dark theme to every element that stays inside it without needing any
    //   extra work. Without the '.theme-dark' class, adding just a dark background color won't automatically
    //   make the toolbar elements colored correctly, which will break the styling of the toolbar.
    //
    // @ Important note: '.theme-light' classes will always override '.theme-dark' classes because how CSS
    //   works. Since 'theme-light' definition comes after the 'theme-dark' definition, CSS classes and
    //   helpers will also be generated with that order which will cause that behavior. To overcome this
    //   issue, never nest '.theme-light' and '.theme-dark' classes. Use them as siblings!
    // -----------------------------------------------------------------------------------------------------

    // Dark theme - DO NOT REMOVE or RENAME
    'theme-dark': treo-dark-theme(
        treo-palette('white'),
        treo-palette('gray', 800),
        treo-palette('red', 800)
    ),

    // Light theme - DO NOT REMOVE or RENAME
    'theme-light': treo-light-theme(
        treo-palette('black'),
        treo-palette('gray', 800),
        treo-palette('red', 800)
    )
);
