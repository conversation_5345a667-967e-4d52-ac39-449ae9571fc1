<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 250.31 250.28">
  <defs>
    <style>
      .cls-1 {
        fill: url(#linear-gradient-3);
      }

      .cls-1, .cls-2, .cls-3, .cls-4 {
        mix-blend-mode: multiply;
        opacity: .2;
      }

      .cls-5 {
        isolation: isolate;
      }

      .cls-6 {
        fill: #404bb2;
      }

      .cls-7 {
        fill: #4af7ff;
      }

      .cls-2 {
        fill: url(#linear-gradient-2);
      }

      .cls-8 {
        fill: #5a63ff;
      }

      .cls-9 {
        fill: #4aadff;
      }

      .cls-3 {
        fill: url(#linear-gradient);
      }

      .cls-4 {
        fill: url(#linear-gradient-4);
      }
    </style>
    <linearGradient id="linear-gradient" x1="126.05" y1="66.92" x2="180.23" y2="11.02" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#fff" stop-opacity="0"/>
      <stop offset="1" stop-color="#231f20" stop-opacity=".8"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="1360.45" y1="-2552.55" x2="1414.63" y2="-2608.45" gradientTransform="translate(2619.48 1484.7) rotate(-90)" xlink:href="#linear-gradient"/>
    <linearGradient id="linear-gradient-3" x1="3979.92" y1="-1318.15" x2="4034.1" y2="-1374.05" gradientTransform="translate(4104.18 -1134.79) rotate(-180)" xlink:href="#linear-gradient"/>
    <linearGradient id="linear-gradient-4" x1="2745.52" y1="1301.31" x2="2799.7" y2="1245.42" gradientTransform="translate(1484.7 -2619.48) rotate(90)" xlink:href="#linear-gradient"/>
  </defs>
  <g class="cls-5">
    <g id="Layer_1" data-name="Layer 1">
      <g>
        <g>
          <path class="cls-9" d="M227.55,110.99c-2.96,1.17-6.05,1.76-9.16,1.76-8.96,0-17.03-4.74-21.41-12.32-.61-1.04-1.15-2.15-1.61-3.3-11.51-28.92-39.12-47.61-70.3-47.61-9.56,0-18.92,1.81-27.87,5.36,6.14-2.44,10.97-7.14,13.58-13.21,2.62-6.08,2.7-12.8,.26-18.95-3.76-9.48-12.79-15.61-23.01-15.61-3.13,0-6.2,.59-9.16,1.76,1.58-.63,3.16-1.22,4.76-1.77C96.99,2.38,110.89,0,125.02,0c24.76,0,48.81,7.28,69.53,21.05,21.22,14.1,37.41,34.07,46.85,57.77,5.05,12.68-1.15,27.11-13.84,32.17Z"/>
          <path class="cls-7" d="M110.79,41.67c-2.61,6.08-7.44,10.77-13.58,13.22-18.75,7.47-33.48,21.79-41.46,40.33-7.97,18.54-8.26,39.09-.79,57.85-3.78-9.49-12.81-15.61-23.02-15.61-3.13,0-6.21,.59-9.15,1.76-12.67,5.06-18.88,19.47-13.83,32.17h0c-12.36-31.05-11.9-65.06,1.3-95.75C23.34,45.25,47.4,21.67,78.01,9.24c.21-.1,.48-.21,.74-.31l.12-.05c2.94-1.17,6.02-1.76,9.15-1.76,10.21,0,19.24,6.12,23.01,15.61,2.45,6.14,2.36,12.87-.26,18.94Z"/>
          <path class="cls-6" d="M171.08,241.47c-14.79,5.85-30.25,8.81-45.94,8.81-16.97,0-33.6-3.46-49.42-10.26-30.36-13.07-53.93-37.1-66.38-67.69-.04-.07-.08-.15-.11-.26-.09-.22-.17-.43-.26-.65,0,0,0-.02,0-.03-5.05-12.71,1.16-27.12,13.83-32.17,2.94-1.17,6.02-1.76,9.15-1.76,10.21,0,19.24,6.12,23.02,15.61,7.48,18.75,21.8,33.48,40.34,41.45,9.54,4.11,19.58,6.2,29.84,6.2,9.56,0,18.98-1.82,27.99-5.41-6.14,2.45-10.97,7.14-13.58,13.22-2.61,6.08-2.7,12.8-.26,18.95,3.78,9.48,12.81,15.61,23.01,15.61,3,0,5.94-.55,8.77-1.63Z"/>
          <path class="cls-8" d="M240.03,174.53c-13.22,30.72-37.57,54.45-68.57,66.79l-.39-.87,.35,.88c-.09,.04-.17,.08-.27,.1-.03,0-.05,.02-.08,.03-2.83,1.08-5.77,1.63-8.77,1.63-10.2,0-19.23-6.13-23.01-15.61-2.45-6.15-2.35-12.88,.26-18.95,2.61-6.08,7.44-10.77,13.58-13.22,38.72-15.42,57.67-59.46,42.25-98.18,3.77,9.48,12.81,15.61,23.01,15.61,3.11,0,6.2-.59,9.16-1.76,12.69-5.06,18.9-19.49,13.84-32.17,12.34,30.99,11.87,64.98-1.36,95.72Z"/>
          <path class="cls-8" d="M171.46,241.33s-.03,0-.04,0l-.35-.88,.39,.87Z"/>
        </g>
        <path class="cls-3" d="M196.99,100.44c-.61-1.04-1.15-2.15-1.61-3.3-11.51-28.92-39.12-47.61-70.3-47.61-9.56,0-18.92,1.81-27.87,5.36,6.14-2.44,10.97-7.14,13.58-13.21,2.62-6.08,2.7-12.8,.26-18.95-3.76-9.48-12.79-15.61-23.01-15.61-3.13,0-6.2,.59-9.16,1.76,1.58-.63,3.16-1.22,4.76-1.77,79.29-18.2,107.38,70.07,113.35,93.33Z"/>
        <path class="cls-2" d="M100.45,53.31c-1.04,.61-2.15,1.15-3.3,1.61-28.92,11.51-47.61,39.12-47.61,70.3,0,9.56,1.81,18.92,5.36,27.87-2.44-6.14-7.14-10.97-13.21-13.58-6.08-2.62-12.8-2.7-18.95-.26-9.48,3.76-15.61,12.79-15.61,23.01,0,3.13,.59,6.2,1.76,9.16-.63-1.58-1.22-3.16-1.77-4.76C-11.08,87.37,77.2,59.28,100.45,53.31Z"/>
        <path class="cls-1" d="M53.33,149.84c.61,1.04,1.15,2.15,1.61,3.3,11.51,28.92,39.12,47.61,70.3,47.61,9.56,0,18.92-1.81,27.87-5.36-6.14,2.44-10.97,7.14-13.58,13.21-2.62,6.08-2.7,12.8-.26,18.95,3.76,9.48,12.79,15.61,23.01,15.61,3.13,0,6.2-.59,9.16-1.76-1.58,.63-3.16,1.22-4.76,1.77-79.29,18.2-107.38-70.07-113.35-93.33Z"/>
        <path class="cls-4" d="M149.86,196.97c1.04-.61,2.15-1.15,3.3-1.61,28.92-11.51,47.61-39.12,47.61-70.3,0-9.56-1.81-18.92-5.36-27.87,2.44,6.14,7.14,10.97,13.21,13.58,6.08,2.62,12.8,2.7,18.95,.26,9.48-3.76,15.61-12.79,15.61-23.01,0-3.13-.59-6.2-1.76-9.16,.63,1.58,1.22,3.16,1.77,4.76,18.2,79.29-70.07,107.38-93.33,113.35Z"/>
      </g>
    </g>
  </g>
</svg>