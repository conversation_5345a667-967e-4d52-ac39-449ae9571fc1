class WedofButton extends HTMLElement {
    config = null;
    uniquid = WedofButton._randomId();
    attrToProp = {}; // camelCase cache

    constructor() {
        super();
    }

    connectedCallback() {
        this.retrieveSettings()
            .then(settings => {
                if (settings) {
                    this.displayButton(settings);
                }
            })
            .catch(error => {
                console.log(error);
            });
    }

    retrieveSettings() {
        //overrided
    }

    attributeChangedCallback(property, oldValue, newValue) {
        if (oldValue === newValue) return;
        this[this.camelCase(property)] = newValue;
        this.retrieveSettings().then((json) => this.displayButton(json));
    }

    displayButton(config) {
        if (!this.shadowRoot) {
            this.attachShadow({mode: 'open'});
        }
        this.shadowRoot.innerHTML = config.component;
        if (config.url) {
            const baseUrl = config.baseUrl ?? Wedof.origin;
            this.onClick(config.url.startsWith('http') ? config.url : baseUrl + config.url);
        }
    }

    onClick(url) {
        const onclickEvent = (e) => {
            const windowFind = this.config.windows.find(openedWindow => openedWindow.toString() === this.uniquid)
            if (!windowFind) {
                const currentWindow = window.open(url, this.uniquid, this.config.windowFeatures);
                this.config.windows.push(this.uniquid);
                let timer = setInterval(() => {
                    if (currentWindow.closed === true) {
                        this.config.windows.splice(this.config.windows.indexOf(this.uniquid), 1);
                        this.retrieveSettings().then((json) => this.displayButton(json));
                        clearInterval(timer)
                    }
                }, 500)
            } else {
                //HACK with an url empty, reset the focus
                window.open('', this.uniquid, this.config.windowFeatures);
            }
        };
        this.shadowRoot.removeEventListener("click", onclickEvent);
        this.shadowRoot.addEventListener("click", onclickEvent);
    }

    camelCase(attr) {
        let prop = this.attrToProp[attr];
        if (!prop) {
            let np = attr.split('-');
            prop = [np.shift(), ...np.map(n => n[0].toUpperCase() + n.slice(1))].join('');
            this.attrToProp[attr] = prop;
        }
        return prop;
    }

    static _randomId(length = 6) {
        return Math.random().toString(36).substring(2, length + 2).toString();
    };
}

/** <wedof-passport-button [external-id] [data*] [certifier*]></wedof-passport-button> **/
class WedofPassportButton extends WedofButton {

    constructor() {
        super()
        this.config = Wedof.components["wedof-passport-button"];
        //Ensure font style is loaded only one time.
        if (Wedof.hasFont === false) {
            const font = document.createElement("link");
            font.href = Wedof.origin + "/assets/cli/fonts/fonts.css";
            font.rel = "stylesheet"
            document.head.appendChild(font);
            Wedof.hasFont = true;
        }
    }

    static get observedAttributes() {
        return ['id', 'external-id', 'registration-folder-external-id', 'inverted', 'data', 'certifier', 'forceCertifierAccess'];
    }

    retrieveSettings() {
        return Wedof.passportCandidateStatus(this.certifier, this.externalId ?? (this.data ?? this.id), this.registrationFolderExternalId, {
            "component": true,
            "inverted": this.hasAttribute('inverted'),
            "forceCertifierAccess": this.hasAttribute('forceCertifierAccess') ? this.getAttribute('forceCertifierAccess') : false
        });
    }
}

/** <wedof-connector-button [data*] [reseller*] [connection*]></wedof-passport-button>**/
class WedofResellerConnectorButton extends WedofButton {

    constructor() {
        super()
        this.config = Wedof.components["wedof-reseller-connector-button"];
        //Ensure font style is loaded only one time.
        if (Wedof.hasFont === false) {
            const font = document.createElement("link");
            font.href = Wedof.origin + "/assets/cli/fonts/fonts.css";
            font.rel = "stylesheet"
            document.head.appendChild(font);
            Wedof.hasFont = true;
        }
    }

    static get observedAttributes() {
        return ['reseller', 'data', 'inverted', 'connection'];
    }

    retrieveSettings() {
        return Wedof.resellerCustomerStatus(this.reseller, this.data, {
            "component": true,
            "connection": this.hasAttribute('connection') ? this.connection : "cpf",
            "inverted": this.hasAttribute('inverted')
        });
    }
}

const Wedof = {
    hasFont: false,
    origin: document.getElementById('wedof-script').getAttribute('src').startsWith('https') ?
        (new URL(document.getElementById('wedof-script').getAttribute('src'))).origin :
        'https://www.wedof.fr' + document.getElementById('wedof-script').getAttribute('src'),
    components: {
        "wedof-passport-button": {
            className: WedofPassportButton,
            windowFeatures: "popup,left=100,top=100,width=680,height=880",
            windows: []
        },
        "wedof-reseller-connector-button": {
            className: WedofResellerConnectorButton,
            windowFeatures: "popup,left=100,top=100,width=680,height=880",
            windows: []
        }
    },
    resellerCustomerStatus: (reseller, data, params = {}) => {
        let url = new URL(Wedof.origin);
        if (data && reseller) {
            url.pathname = `/app/public/resellers/${reseller}/connect/${data}`;
        } else {
            return Promise.resolve(false);
        }
        Object.keys(params).forEach(key => {
            let value = params[key];
            value = value === true ? "true" : ((value === false) ? "false" : value);
            url.searchParams.append(key, value);
        });
        return fetch(url)
            .then((res) => {
                if (res.status !== 200) {
                    throw new Error(`${data} ${reseller}: status:${res.status} text: ${res.statusText}`)
                }
                return res.json();
            });
    },
    passportCandidateStatus: (certifier, data, registrationFolderExternalId, params = {}) => {
        let url = new URL(Wedof.origin);
        if (data && data.startsWith('ey') && certifier) { //data is jwt
            url.pathname = `/app/public/certificationFolders/passport/${certifier}/${data}`;
        } else if (data) {
            url.pathname = `/app/public/certificationFolders/${data}/passport`;
        } else if (registrationFolderExternalId) {
            url.pathname = `/app/public/registrationFolders/${registrationFolderExternalId}/passport`;
        } else {
            return Promise.resolve(false);
        }
        Object.keys(params).forEach(key => {
            let value = params[key];
            value = value === true ? "true" : ((value === false) ? "false" : value);
            url.searchParams.append(key, value);
        });
        return fetch(url)
            .then((res) => {
                if (res.status !== 200) {
                    throw new Error(`${certifier} ${data}: status:${res.status} text: ${res.statusText}`)
                }
                return res.json();
            });
    }
}

Object.keys(Wedof.components).forEach(key => {
    const component = Wedof.components[key];
    customElements.define(key, component.className);
});
