Cette documentation décrit la mise à jour des dossiers de certification depuis un fichier Excel, découvrez les [autres méthodes de mises à jour](/assistance/guides/certificateurs/maj-dossiers-certification).

## Mise à jour des dossiers de certification depuis un fichier Excel

### Limitations

Attention, chaque ligne du fichier représente un dossier de certification sur lequel Wedof doit effectuer un traitement important : vérifier si les données ont changé, vérifier les droits d'accès, vérifier la cohérence des données, et les mettre à jour le cas échéant. Ainsi :

* Limitez le fichier aux lignes que vous souhaitez réellement mettre à jour,
* Limitez le fichier à **quelques centaines de lignes au maximum**.

L'export / import Excel n'est pas conçu pour éviter d'utiliser Wedof, mais plutôt pour ponctuellement mettre à jour des données en lot pour des lots d'au maximum quelques centaines de dossiers, potentiellement pour rattraper un historique.

Vous pouvez choisir de déclencher ou non les évènements liés aux changements d'état des dossiers comme **la génération de parchemin, l'envoi de messages et notifications ou encore les webhooks**. Attention, si vous souhaitez mettre à jour et déclencer tous les évènements, les traitements pourront être lents et lourds. 
Soyez vigilents si vous souhaitez déclencher ces évènements, si un message automatique est déclenché lors du passage à l'état "Réussi", les mails pourront être envoyés à vos candidats même si ces derniers ont passés l'examen il y a plusieurs mois / années.

### Utilité

Ces dossiers de certification sont créés et mis à jour automatiquement vers un certain état en fonction de l'avancée du dossier de formation associé. Cependant, à partir des données du dossier de formation, Wedof ne peut pas déterminer si le candidat a passé l'examen, à quelle date, avec quel résultat, etc.

Ainsi, à partir de l'état "Prêt à passer", c'est au certificateur (et à l'organisme de formation s'il réalise l'examen) de mettre à jour sur les dossiers de certification les informations liées à l'examen et à son résultat.

Cette mise à jour permet :

* de **réaliser l'accrochage à l'aide Wedof** (cf. [la documentation de l'accrochage](/assistance/guides/certificateurs/accrochage)),
* d'obtenir des statistiques importantes (taux de réussite, taux de passage) notamment pour contrôler les partenaires,
* de participer à l'effort de transparence réciproque avec le partenaire au travers des données partagées dans Wedof.

### Option A. : Depuis un export Excel Wedof

Depuis la vue "Dossiers" de votre espace certification, vous pouvez exporter les dossiers de certification au même format que celui attendu pour leur mise à jour. Cela vous permet donc de préremplir les lignes du fichier et vous n'avez plus qu'à les compléter.

De plus, vous pouvez utiliser la fonctionnalité de recherche avancée afin de n'exporter que certains dossiers.

![export-excel](/assets/images/help-center/guides/certification-state-workflow/export-excel.png "export excel des dossiers de certification")

Attention :

* **les champs avec un en-tête grise sont en lecture seule**, ils sont là pour vous aider à identifier chaque passage de certification.

### Option B. : Depuis un modèle de fichier vierge

Nous vous mettons à disposition un modèle de fichier :

**[TÉLÉCHARGER LE MODÈLE EXCEL À REMPLIR](/assets/files/wedof-modele-import-dossiers-certification.xlsx)**

Attention : Le dossier de certification **doit déjà exister dans Wedof**.

### Remplir ou compléter le fichier

Sur chaque en-tête, une annotation donne les détails sur le format attendu, si le champ est obligatoire ou non, s'il sera inclus dans l'accrochage, quelle est sa valeur par défaut...

![import-excel](/assets/images/help-center/guides/certification-state-workflow/import-excel.png "import excel des dossiers de certification")

Attention, cet import ne fonctionnera que sous certaines conditions :

* Ne modifiez pas les en-têtes, les règles de validation et les valeurs possibles pour les champs énumérés.
* Le dossier de certification doit déjà exister dans Wedof,
* L'état cible du dossier de certification doit être renseigné,
* L'état cible renseigné dans le fichier doit être supérieur ou égal à l'état du dossier actuel dans Wedof,
* L'état "À repasser" n'est pas supporté pour le moment (qu'il soit l'état actuel du dossier ou l'état visé).

La mise à jour sera effectuée même s'il manque des documents requis.

### Soumettre le fichier

Une fois rempli, vous pouvez déposer le fichier depuis le menu d'export / import des dossiers de certification, ainsi que depuis la boîte de dialogue d'accrochage dans votre profil.

Cette opération peut prendre jusqu'à plusieurs minutes selon le nombre de dossiers mis à jour.

![import-excel-envoyer](/assets/images/help-center/guides/certification-state-workflow/import-excel-envoyer.png "soumettre le fichier des dossiers de certification")

Si le traitement échoue, vérifiez la taille du fichier. S'il fait plus de quelques centaines de lignes, Wedof ne pourra pas le traiter, il faudra le découper.

À l'issue du traitement, vous obtiendrez en retour un fichier de rapport d'import **CSV** qui indique sous un format technique le résultat du traitement ligne par ligne.

En cas d'erreur sur un dossier, le fichier indique la cause de l'erreur et l'état atteint (il est possible que le dossier ait avancé de plusieurs états sans arriver à l'état cible).

En cas d'incompréhension, vous pouvez nous faire parvenir ce fichier ainsi que votre fichier Excel afin que nous les analysions.
