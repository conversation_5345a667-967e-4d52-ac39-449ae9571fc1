## Principes de l'accrochage

En qualité d'Organisme Certificateur, vous êtes tenus de fournir à la Caisse des dépôts et consignations (CDC) la liste
des personnes que vous avez
certifiées ([Arrêté du 21 mai 2021 relatif à la transmission au système d'information du compte personnel de formation des informations relatives aux titulaires des certifications enregistrées aux répertoires nationaux"](https://www.legifrance.gouv.fr/jorf/id/JORFTEXT000043623708))
.

Pour remplir cette obligation, il faut déposer sur l'espace dédié de la plateforme Mon Compte Formation un fichier XML
contenant le détail des certifications délivrées d'après les règles communiquées
sur https://certificateurs.moncompteformation.gouv.fr/page-guide et plus particulièrement dans le "Dictionnaire des
données".

Wedof modélise les données de passage d'un examen de certification et de son obtention dans
les [Dossiers de Certification](/assistance/guides/certificateurs/cycle-vie-certification-apprenant). Ces dossiers
peuvent être exportés depuis Wedof sous la forme d'un fichier XML compatible avec le format attendu par Mon Compte
Formation. L'accrochage sert à informer la CDC de la liste des titulaires de votre certification, ainsi seuls les dossiers de certification à l'état **Réussi** dans Wedof sont concernés par l'accrochage.

Grâce à Wedof, vous n'avez pas besoin d'effectuer une double saisie dans un autre référentiel, ce sont vos données de travail
qui sont directement utilisées pour générer le XML (données elles-mêmes en partie remplies automatiquement par la
synchronisation avec les dossiers de vos partenaires).

De plus, Wedof bénéficie d'une donnée d'une grande importance : le numéro de dossier dans le cas d'un dossier CPF, qui évite la collecte de données personnelles et assure un accrochage réussi.

Par rapport à des approches concurrentes, cela vous assure plus
de fiabilité et de simplicité dans le processus d'accrochage, mais aussi plus de pérénité. En effet, après le rattrapage
des données historiques, les données nécessaires pour le fichier se rempliront "toutes seules" par votre collaboration
avec vos partenaires sur Wedof.

Si vous activez l'accrochage automatique avec Wedof alors votre travail s'arrête là, l'accrochage est réalisé quotidiennement pour tous vos nouveaux dossiers réussis.
Sinon, vous devrez générer un fichier XML, le déposer sur le portail de la CDC, et déposer l'accusé de traitement correspondant dans Wedof.

## Saisie des données dans Wedof

Afin de générer ce fichier XML via Wedof, vous devez au préalable vous assurer d'avoir rempli certaines informations.

### Compte utilisateur Wedof : à réaliser une fois pour toute

Rendez vous sur le profil de votre organisme ["Mon compte"](/profil/organisme) et renseignez les données
obligatoires suivantes dans l'encart "Accrochage" :

- Le _numéro de contrat_ de certificateur attestant de votre rôle de certificateur auprès de la CDC,
- Le _matricule_ (ou BCR) permettant d'identifier le certificateur au sein du SI CPF.

Le numéro de contrat débute toujours par « MCFCER » suivi de 6 chiffres. Ex : MCFCER123456.

Le matricule est composé de 2 chiffres puis 3 lettres puis 3 chiffres. Ex : 12ABC123.

Ces données vous ont été communiquées par la Caisse des dépôts lors de la préparation du processus d'accrochage.

### Certification : à réaliser une fois par certification

Sur le profil de votre organisme ["Mon compte"](/profil/organisme) dans l'encart "Accrochage", vous pouvez indiquer pour chacune de vos certifications si vous souhaitez procéder à l'accrochage automatique.

Au niveau de la certification, voici la donnée obligatoire :

- Le _mode d'obtention_ de la certification : par scoring ou par admission. Dans le XML, cette donnée est associée à
  chaque passage et non à la certification, mais nous avons fait l'hypothèse simplificatrice que pour une certification
  donnée ce système n'évolue pas (les modalités d'évaluation faisant partie du dossier d'inscription à France
  Compétences). L'avantage pour vous c'est que vous n'avez à le saisir qu'une fois.

C'est la seule donnée obligatoire, mais il est recommandé de saisir autant de données que possible.

Si la modalité de passage de l'examen est la même pour tous les examens, vous pouvez la saisir au niveau de la
certification et elle sera héritée par les dossiers de certification lors de leur création. Elle peut être saisie et
modifiée individuellement sur chaque dossier.

La durée de validité concerne la durée de validité de la certification pour le candidat (soit après combien de temps la
certification de son niveau de compétence expire). C'est totalement indépendant de la durée d'inscription de votre
certification sur France Compétences. Si elle est renseignée, cette durée permet de remplir automatiquement la _
dateFinValidite_ de la certification d'un titulaire si elle n'est pas remplie directement sur le dossier de
certification.

Pour mettre à jour les données d'une certification, rendez-vous dans
l'espace ["Partenariats"](/certification/partenariats/) et ouvrez le volet de détails de la certification concernée (
flêche à droite du titre de la certification).

![accrochage-update-certification](/assets/images/help-center/guides/certificateurs/accrochage-update-certification.png "Détails d'une certification")

Aujourd'hui tous les attributs qu'il est possible de fournir dans le XML ne sont pas encore disponibles via l'interface
utilisateur de la Certification dans Wedof, cependant la majorité peut d'ores et déjà être renseignées via
l'[API Wedof](/api/doc).

![accrochage-certification-form](/assets/images/help-center/guides/certificateurs/accrochage-certification-form.png "Mettre à jour une certification")

Les Certifications avec niveau numérique Européen **ne sont pas encore** supportées pour l'accrochage via Wedof.

### Dossier de certification : à réaliser pour chaque dossier

Dans la majorité des cas il n'y a pas de données obligatoires, mais il est recommandé de saisir autant de données que
possible.

La correspondante entre les données
des dossiers de certification et celles intégrées dans le XML est détaillée dans
un [tableau de correspondance](/assistance/guides/certificateurs/correspondance-wedof-dictionnaire).

Aujourd'hui tous les attributs qu'il est possible de fournir dans le XML ne sont pas encore disponibles via l'interface
utilisateur des dossiers de certification Wedof, cependant la majorité peut d'ores et déjà être renseignées via
l'[API Wedof](/api/doc).

Dans le cas d'une certification de langue, des attributs supplémentaires sont disponibles. En cas de doutes, vous pouvez
vous référer au dictionnaire des données.

Afin de faciliter la saisie des données d'obtention de certification et de vous aider à assurer leur conformité, Wedof
effectue des contrôles sur la cohérence des données saisies, notamment sur les dates ('examinationDate, '
examinationEndDate', 'expirationDate', 'issueDate', 'enrollmentDate'). Wedof vérifie par exemple que la date de début
d'examen doit être antérieure à la date de fin ou encore que la date d'obtention de la certification est bien ultérieure
à la date d'inscription à la certification.

### Titulaire : à réaliser pour chaque dossier

Pour les dossiers de certification liés à un dossier de formation provenant du CPF, le numéro de dossier de formation
EDOF ainsi que le prénom et nom du titulaire sont suffisants.

Pour les dossiers de certification hors parcours CPF, les données suivantes sont obligatoires :

- La _date de naissance_ du titulaire certifié,
- Le _code INSEE de la commune de naissance_ du titulaire ainsi que la _ville de naissance_ si il est né en France, si le titulaire est né à l'étranger le _code COG_ ainsi que le nom du _pays de naissance_ sont requis.

Aujourd'hui les données du titulaire ne peuvent être modifiés que via l'API via le
endpoint `/api/attendees/{emailTitulaire}`.

Ces informations sont essentielles car ce sont elles qui permettront à la CDC d'associer l'obtention de la certification
à la bonne personne.

### Les étapes suivantes s'adressent aux certificateurs n'ayant pas activé l'accrochage automatique de leurs certifications.

## Génération du fichier

Pour générer le fichier, rendez-vous sur la fiche d'une certification dans l'espace "Partenariats" et cliquez sur le
bouton dédié.

![accrochage-download-button](/assets/images/help-center/guides/certificateurs/accrochage-download-button.png "Générer le fichier")

Le fichier sera envoyé dans les minutes qui suivent par email à l'adresse email définie sur votre compte.

L'envoi par email évite l'attente dans l'interface et vous permet de garder une trace du fichier.

Afin de vous garantir un fichier sans erreurs dans la construction du fichier XML, ce dernier est soumis en interne à
une validation XSD avant de vous parvenir, la même validation qui est effectuée par la CDC à l'aide d'un format
technique appelé XSD (https://certificateurs.moncompteformation.gouv.fr/espace-public/page-guide#ctg16). Si Wedof
détecte une ou plusieurs anomalies, vous serez prévenu par mail avec l'erreur ou les erreurs associée(s) et vous pourrez
ainsi en prendre connaissance.

## Déposer le fichier

![cdcWebsite1](/assets/images/help-center/guides/certificateurs/cdcWebsite1.png "Accrochage Déposer un fichier")

Ce fichier devra être déposé sur la plateforme de production du "Portail des responsables de diplômes et de
certifications". Connectez vous avec vos identifiants à la plateforme et rendez vous dans l'onglet "Déposer un fichier".

**Tout dépôt et les données associées demeurent l'entière responsabilité du Certificateur ou de ses représentants.**

![cdcWebsite2](/assets/images/help-center/guides/certificateurs/cdcWebsite2.png "Accrochage Consulter un fichier")

Vous retrouverez les fichiers que vous avez déposés dans l'onglet "Consulter un fichier". Chaque fichier peut avoir 2
status : "Traité avec succès" ou "Traité avec erreurs". Vous devez nous faire parvenir l'Accusé de Traitement du fichier afin que nous puissons le traiter.

**Attention, vous ne pourrez plus regénérer de fichier XML si vous n'avez pas transmis l'Accusé de traitement du
précédent fichier généré.**

## Déposer un accusé de traitement

Une fois votre **Accusé de Traitement** récupéré depuis la plateforme de production du "Portail des responsables de diplômes et de
certifications", vous devez le fournir à Wedof depuis votre profil Wedof dans l'encart Caisse des Dépôts.

![accrochage-download-treatmentFile](/assets/images/help-center/guides/certificateurs/accrochage-download-treatmentFile.png "Déposer un accusé de traitement")

Sélectionnez votre fichier XML en cliquant sur "Déposer l'accusé de traitement".

Dès lors que l'accusé aura été traité par Wedof, vous pourrez voir dans votre espace "Dossiers de certification" les dossiers déposés avec succès et ceux comportant des erreurs.
Comprendre le [statut d'accrochage](/assistance/guides/certificateurs/statut-accrochage) sur un dossier de certification.
