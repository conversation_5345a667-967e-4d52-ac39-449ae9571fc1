## Avant-propos

Les certifications RNCP sont divisées en blocs de compétences, eux-mêmes divisés en compétences.

Wedof permet de gérer :
* les blocs de compétences (le plus souvent récupérés automatiquement depuis les données publiques),
* les compétences associées (gestion manuelle).

Dans certains cas, ces données sont seulement informatives.

Dans d'autres cas, elles sont essentielles quand le parcours de formation peut être divisé par bloc de compétences. En effet, la conséquence de cette division est qu'un parcours de formation peut aboutir mais ne pas être certifiant, contrairement aux parcours habituellement gérés dans Wedof.

Cette documentation explique comment gérer ces cas-là dans Wedof.


## Gestion des blocs de compétences

### Configuration sur la certification

Les certifications RNCP disposent d'une rubrique "Référentiel de compétences" sur Wedof qui liste les blocs de compétences associés.

Sur la certification elle-même, vous pouvez configurer le fait que son enseignement peut être divisé en blocs :

![options](/assets/images/help-center/guides/certificateurs/blocs-option.png "Option sur la certification")

La première conséquence est que tout nouveau dossier de certification qui sera créé (manuellement ou automatiquement, depuis un partenariat ou non) dans Wedof sur cette certification sera automatiquement exclu de l'accrochage. Si vous souhaitez que cela soit le cas aussi pour vos anciens dossiers, contactez-nous.

La seconde conséquence est que cela vous permet de définir une liste de blocs sur :
* les partenariats de cette certification,
* les dossiers de cette certification.

Les sections suivantes expliquent comment faire.


### Définition des blocs sur les partenariats

Sur un partenariat, vous pouvez définir le fait qu'il ne porte que sur un sous-ensemble des blocs de la certification. 

Vous pouvez choisir un ou plusieurs blocs.

![blocs-partenariat](/assets/images/help-center/guides/certificateurs/blocs-gestion.png "Blocs sur le partenariat")

La conséquence est que sur les dossiers issus d'un partenariat, seuls les blocs présents sur ce partenariat pourront être choisis.

Cette fonctionnalité est optionnelle. Elle permet :
* d'affiner ce qu'il est possible ou non de faire au niveau des dossiers,
* de matérialiser une contrainte que vous avez pu contractualiser sur un partenariat,
* de définir des critères d'audit.

Si vous ne définissez aucun bloc sur le partenariat, alors tous les blocs sont autorisés pour les dossiers associés.

### Définition des blocs sur les dossiers de certification

Sur un dossier de certification, vous pouvez définir les blocs sur lesquels il porte. Vous pouvez choisir un, plusieurs ou tous les blocs.

![blocs-partenariat](/assets/images/help-center/guides/certificateurs/blocs-gestion.png "Blocs sur les dossiers")

Par défaut, tous les blocs de la certification sont disponibles dans la liste, sauf si cela a été restreint au niveau du partenariat si le dossier est issu d'un partenariat.

Si vous définissez explicitement qu'il porte sur tous les blocs, alors le dossier sera inclus dans l'accrochage.

Il est très fortement recommandé de vous assurer que les blocs soient renseignés sur tous les dossiers issus de la certification concernée. 

Il y a une gestion différente selon les cas :

#### Hors dossier de formation

Pour tout nouveau dossier de certification créé hors dossier de formation, il faudra renseigner les blocs associés (manuellement, via l'API, via un automatisme, etc.). En effet, Wedof ne peut pas les déduire automatiquement.

#### Avec dossier de formation

Pour un dossier de certification associé à un dossier de formation, il suffit de renseigner l'information une seule fois par formation.

En effet, une formation ne peut porter que sur certains blocs. Par conséquent, si on connaît les blocs pour un dossier de certification de cette formation, on connaît les blocs pour tous les autres dossiers de cette formation.

Ainsi, les blocs seront propagés rétroactivement aux autres dossiers de la même formation et affectés automatiquement à tout nouveau dossier créé depuis cette formation.
