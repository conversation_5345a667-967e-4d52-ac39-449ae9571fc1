## Laissez vous guider par ce tutoriel vidéo :
<br>
<br>

https://www.youtube.com/embed/HQbJzau2Tr0
<br>
<br>

## Ou préférez une documentation détaillée :
<br>

#### Avant-propos

Auditer vos partenaires vous permet de contrôler régulièrement la conformité de votre réseau de partenaires.

Depuis chacune de vos certifications, activez notre modèle de document destiné à l'Audit de vos partenaires.

#### Créer un modèle d'audit et définir les critères d'exigences

![audit](/assets/images/help-center/guides/certificateurs/audit1.png "liste des audits")

![création audit](/assets/images/help-center/guides/certificateurs/audit2.png "création audit")

Pour chaque audit créé, vous devez spécifier si celui ci sera visible pour votre partenaire. Vous devez également préciser si le résultat de l'audit devra être affecté au partenaire.

Pour chacun de vos audits, vous devez définir des critères d'exigences pour lesquels vos partenaires seront audités.

Chaque critère se compose de la manière suivante : 
- Donnée auditée,
- Intitulé,
- Identifiant unique, 
- Exigence,
- Sévérité (majeure ou mineure),
- Conseils de mise en conformité à destination du partenaire.

Les conseils de mise en conformité sont destinés à vos partenaires et leur permettront de se mettre en conformité en suivant vos recommendations.

La sévérité d'un critère peut influer sur la conformité de l'audit du partenaire en cas de non-respect du critère en question.

![création critère](/assets/images/help-center/guides/certificateurs/audit-criterias.png "création de critère sur un audit")

##### Exemple de création de critère

![exemple critère](/assets/images/help-center/guides/certificateurs/audit-criterias-exemple1.png "exemple de création de critère")

![exemple2 critère](/assets/images/help-center/guides/certificateurs/audit-criterias-exemple2.png "exemple de création de critère")

##### Critère évalués par IA (bêta)

Ces critères utilisent l'intelligence artificielle pour évaluer automatiquement la conformité. Un formulaire vous permet ensuite d'entraîner l'ia.

![critere_ia](/assets/images/help-center/guides/certificateurs/critere_ia.png "exemple critère ia")

**Entraînement :**

- Cliquez sur **« Entraîner l’IA »** en bas de la fenêtre.
- Dans le formulaire **« Entraîner l’IA sur les intitulés de formations »**, répondez aux exemples proposés :
    - Pour chaque intitulé, sélectionnez **Oui** ou **Non**, puis **justifiez votre choix** dans le champ prévu à cet effet. Cette étape est essentielle pour permettre à l’IA d’identifier les règles de l’audit.

![formulaire_entrainement](/assets/images/help-center/guides/certificateurs/formulaire_entrainement.png "exemple formulaire entrainement")

- Vous pouvez également ajouter d’autres exemples (au minimum 10) via le champ **« Ajouter un nouvel intitulé de formation »** afin d’améliorer l’apprentissage de l’IA.

![ajouter_titre](/assets/images/help-center/guides/certificateurs/ajouter_titre.png "exemple ajouter titre")

- Cliquez sur **« Confirmer et entraîner l’IA »** pour lancer l'entraînement.

Une **jauge de précision** vous indique le taux de fiabilité estimé de l’IA. Plus vous ajouter d’exemples, plus la précision augmente.

![jauge_precision](/assets/images/help-center/guides/certificateurs/jauge_precision.png "exemple jauge precision")

#### Non-conformité d'un critère

Vous pouvez définir la sévérité pour chaque critère d'audit.

La sévérité peut être :
- Majeure,
- Mineure,
- Aucune.

Pour chacun des critères évalués sur un audit :
- si une **non-conformité majeure** est appliquée, le résultat de l'audit sera alors **Non conforme**
- si une **non-conformité mineure** est appliquée et en l'absence de non-conformité majeure, le résultat de l'audit sera alors **Partiellement conforme**
- si aucune non-conformité n'est appliquée, le résultat de l'audit sera alors **Conforme**

Cela peut servir à suspendre les partenaires basé sur la criticité des non-conformités relevées (cf. section "Suspension les partenaires").

#### Modifier le modèle de rapport d'audit

Dès que le modèle d'audit sera créé, vous pourrez visualiser et modifier le modèle de document selon vos envies avec les variables du [Dictionnaire des variables](/assistance/guides/applications/dictionnaire). 
N'oubliez pas d'intégrer dans le document les critères que vous aurez créés dans l'audit.

#### Génerer l'audit d'un partenaire

Afin d'auditer un partenaire, vous devez vous rendre sur le partenariat choisi dans Wedof et générer un nouvel audit. 

![audit partner](/assets/images/help-center/guides/certificateurs/auditPartner.png "audit d'un partenaire")

L'audit sera alors généré en fonction des critères d'exigences que vous aurez au préalable prédéfinis dans le modèle d'audit.

Une fois l'audit finalisé, le résultat pourra être affecté sur le partenariat de certification et le partenaire aura alors connaissance du résultat de l'audit ainsi que des critères sur lesquels il aura été évalué.

Le partenaire peut retrouver le résultat d'un audit depuis Wedof dans l'_Espace Formation_ dans l'onglet _Certifications et Partenariats_ puis choisir la certification en question. 
Pour chaque audit réalisé, le partenaire peut visualiser :
- le détail de l'audit avec l'ensemble des critères ainsi que les valeurs enregistrées,
- le rapport d'audit (si le certificateur a généré dans le document).

_Les résultats de l’audit des partenaires sont fournis à titre informatif. Wedof ne peut pas être tenu pour responsable de l'exactitude des données collectées et des conséquences sur le statut d'un partenariat._

#### Audit général d'un ensemble de partenaires

Il est possible de lancer un audit sur tous les partenaires d'une certification et d'automatiser certains comportements :
* Clôturer les audits,
* Mettre à jour la conformité des partenaires,
* Suspendre les partenaires en cas de non-conformité majeure.

Il est possible de restreindre l'audit général aux partenaires avec un certain niveau de conformité, par exemple pour réaliser un audit suspensif sur les partenaires non conformes suite à un audit de contrôle.

#### Résultat de l'audit

Par défaut un audit doit être clôturé manuellement, auquel cas, c'est le certificateur qui détermine le résultat de l'audit.

Dans le cas d'un audit général, l'audit peut être clôturé automatiquement. Le certificateur peut alors décider que le résultat soit déterminé automatiquement d'après la conformité des critères évalués.

#### Conformité des partenaires

Dans le cas d'un audit général, le résultat de l'audit peut être reporté sur le partenaire pour définir son niveau de conformité.

Si le partenaire est suspendu ou révoqué et qu'il est plus tard réactivé, sa conformité est remise à 0 car la précédente évaluation est très certainement obsolète.

#### Suspension les partenaires

En cas d'audit avec le résultat **Non conforme**, le certificateur peut décider de suspendre le partenariat sur France Compétences.
