## Dossier de certification

Pour chaque dossier de certification exporté vers la CDC, plusieurs icônes matérialisent le statut d'accrochage.

![accrochage-certification-folders-icons](/assets/images/help-center/guides/certificateurs/accrochage-certification-folders-icons.png "Icones statut accrochage")

Chaque icône vous informe d'un statut différent :

- Le dossier de certification a été exporté dans un fichier XML mais l'accusé de traitement n'a pas encore été fourni à Wedof,
- Le dossier a été déposé avec succès auprès de la CDC et la certification est disponible dans le Passeport de Compétences du candidat,
- Le dossier a été déposé auprès de la CDC mais n'a pas été pris en compte dû à une erreur,
- Le dossier ne peut être exporté du fait d'informations manquantes telle que la ville de naissance ou la date de naissance du titulaire par exemple,
- Le dossier doit être transmis sous les 3 mois obligatoires requis par la CDC.

Ces informations sont également indiquées directement sur le dossier de certification.

![accrochage-certification-folder-message](/assets/images/help-center/guides/certificateurs/accrochage-certification-folder-message.png "Dossier de certification message statut CDC")

## Activités

Afin de centraliser et traçer les informations nécessaires des dossiers exportés, une activité est créée automatiquement sur ces dossiers lors de la génération d'un fichier XML depuis Wedof.

![accrochage-activity-exported](/assets/images/help-center/guides/certificateurs/accrochage-activity-exported.png "Activité dossier exporté dans un fichier XML")

De la même manière, lorsqu'un accusé de traitement est déposé dans Wedof, des activités sont créées pour chaque dossier exporté. Deux cas peuvent alors se présenter :

- En cas de succès, l'activité créée est la suivante :

![accrochage-activity-success](/assets/images/help-center/guides/certificateurs/accrochage-activity-success.png "Activité dossier exporté avec succés vers la CDC")

Le dossier a donc bien été exporté avec succès vers la CDC et la certification est disponible dans le Passeport de Compétences du candidat.

- En cas d'échec, l'activité créée comporte le message d'erreur provenant de la CDC :

![accrochage-activity-fail](/assets/images/help-center/guides/certificateurs/accrochage-activity-fail.png "Activité dossier exporté avec erreur vers la CDC")

En fonction de l'erreur indiquée, une action est potentiellement requise de votre part par exemple si la date de naissance et/ou la ville de naissance du titulaire ne correspondent pas.

Afin de les différencier des autres activités créées et/ou générées par Wedof, celles créées dans le cadre de l'export XML sont signalées comme type **cdc**.

## Erreurs

Si une erreur se présente, cela signifie que la Caisse des dépôts n'est pas parvenue à trouver la personne à qui attribuer la certification. Cela peut arriver sur les dossiers hors CPF, pour qui l'identification est réalisée à partir de données personnelles.

Il existe différents types d'erreur pour un dossier :

- "Le passage certification avec le bcr émetteur XXXXX et l'id technique partenaire XXX existe déjà en base", signifie que le dossier a déjà été transmis à la CDC
- "Aucun titulaire ne correspond aux critères obligatoires", signifie qu'aucun titulaire n’a été identifié avec les données transmises : nom de naissance, prénom, sexe et année de naissance
- "Aucun titulaire ne correspond aux critères obligatoires enrichis du jour et du mois de naissance", signifie de mauvaises données sur le jour et mois de naissance

Pour résoudre ces problèmes, la solution consiste à vérifier les données saisies et au besoin collecter à nouveau les données personnelles auprès des candidats en question, par exemple en leur demandant par email.

Wedof propose une solution innovante pour vous permettre de collecter ces données auprès des candidats : les candidats peuvent se connecter à un espace en ligne avec leur Identité Numérique et les données correspondantes sont alors récupérées pour maximiser les chances d'un accrochage réussi. [Consulter la documentation de l'Espace Candidat](/assistance/guides/certificateurs/espace-apprenant)

## Erreurs après avoir mis à jour l'aprennant avec les bonnes informations

Si vous ne parvenez toujours pas à accrocher ce dossier après avoir inséré les bonnes données du candidat, il se peut que celui-ci n'ait pas de numéro de sécurité sociale ce qui empêche son accrochage du côté de la CDC.

