Wedof permet de récupérer des données de certification depuis de nombreuses sources mais certaines informations ne peuvent être communiquées que par le certificateur.

Cette documentation explique les différentes manières pour réaliser ces mises à jour.

## Avant-propos

Il est recommandé d'utiliser au quotidien soit l'interface utilisateur soit l'API. 
Pour de gros volumes ou pour rattraper un historique, il est privilégié d'utiliser l'import via le fichier Excel.
Si un accrochage a déjà été réalisé hors Wedof et que vous souhaitez rapatrier ces données dans Wedof, nous vous conseillons de transmettre via l'interface votre fichier XML ainsi que son accusé de traitement pour que Wedof puisse intégrer ces résultats (valable uniquement pour les dossiers "Réussi").

## L'interface utilisateur Wedof 

L'interface permet de mettre à jour manuellement les dossiers de certification au compte-gouttes. 
Cette pratique est donc idéale si vous souhaitez modifier des dossiers de certification en faible quantité.

Attention, cette méthode ne permet pas de mettre à jour simultanément plusieurs dossiers de certification, il faudra ainsi effectuer les mises à jour dossier par dossier ou privilégier une autre méthode de mise à jour. 

## L'API

Facile d'utilisation, rapide et adapté à de gros volumes, l'API est recommandé si vous souhaitez automatiser la mise à jour de vos dossiers de certification depuis une source de données informatisée extérieure à Wedof (cf. [la documentation de l'API](/api/doc/)). 
La mise à jour par API permet l'accès à des données supplémentaires. 

## Excel 

La mise à jour des dossiers de certifications depuis Excel permet le traitement d'un plus grand nombre de dossiers et de données. 

Lors de l'import Excel, il est possible de déclencher des évènements liés au changement d'état comme l'envoi de messages et notifications, génération de documents, webhooks.

Une documentation détaillée sur la mise à jour des dossiers de certification est disponible [ici](/assistance/guides/certificateurs/maj-dossiers-certification-excel).


## XML 

Le fichier XML permet d'importer dans Wedof le résultat de l'accrochage réalisé hors Wedof et permet ainsi le traitement d'un plus gros volume de dossiers de certification.
Attention, cette méthode ne concerne que les dossiers de certification à l'état "Réussi" accrochés sur le portail Certificateur. 

L'import d'un fichier XML doit être réalisé directement depuis votre profil Wedof dans la configuration de votre organisme. Afin d'éviter des traitements lents et lourds, cette méthode ne permet pas de déclencher les évènements liés au changement d'état (envoi de messages et notifications, génération de documents, webhooks, etc).


## Bientôt disponible ! 

Dans les prochains mois, il sera possible de mettre à jour les dossiers de certification via l'application Zapier. 

Pour chaque passage Réussi et accroché sur le site de la CDC, Wedof pourra automatiquement récupérer chaque dossier ainsi que ces données d'accrochage et l'intégrer dans Wedof. Vous n'aurez donc plus a communiquer à Wedof tous vos fichiers XML et les accusés de traitement. 



