Cette documentation décrit l'import historique des dossiers de certification hors CPF depuis un fichier Excel.

## Création et mise à jour des dossiers de certification hors CPF depuis un fichier Excel

### Limitations

Attention, chaque ligne du fichier représente un dossier de certification sur lequel Wedof doit effectuer un traitement important, à savoir : 
* soit la création du dossier de certification si celui-ci n'est pas présent dans Wedof,
* soit la mise à jour du dossier de certification si celui-ci est présent dans Wedof. 
  
Dans le cas de la mise à jour d'un dossier de certification, pensez donc à vérifier si les données ont changé, vérifier les droits d'accès, vérifier la cohérence des données et les mettre à jour le cas échéant.

* Limitez le fichier aux lignes que vous souhaitez réellement mettre à jour,
* Limitez le fichier à **10000 lignes au maximum**.
* **<span style="color: #a71818">N'importez pas de dossiers issus du CPF<span>**, ils doivent être importés depuis la [synchronisation avec vos partenaires](/assistance/guides/certificateurs/demander-autorisation-partenaire)

### Utilité

Cette mise à jour permet :

* de déterminer l'état du **Passeport de Compétences**,
* d'obtenir des statistiques importantes (taux de réussite, taux de passage) notamment pour contrôler les partenaires,
* de participer à l'effort de transparence réciproque avec le partenaire au travers des données partagées dans Wedof.

## Remplir ou compléter le fichier

Sur chaque en-tête, une annotation donne les détails sur le format attendu, si le champ est obligatoire ou non, quelle est sa valeur par défaut...

**[TÉLÉCHARGER LE MODÈLE IMPORT HISTORIQUE EXCEL À REMPLIR](/assets/files/modele-import-historical-certification-folders.xlsx)**

Attention, cet import ne fonctionnera que sous certaines conditions :

* Ne modifiez pas les en-têtes, les règles de validation et les valeurs possibles pour les champs énumérés.
* L'état cible du dossier de certification doit être renseigné,
* Les données relatives aux candidats doivent être renseignées (email, prénom et nom de famille du candidat),

La mise à jour sera effectuée même s'il manque des documents requis.


| Champs | Valeurs / Explication | Obligatoire | 
|--------|:---------------------:|:-----------:|
|Email du candidat | | Oui |
|Téléphone portable du candidat | | Non |
|Prénom du candidat | | Oui |
|Nom de famille du candidat | | Oui |
|2eme prénom du candidat | | Non |
|Nom de naissance du candidat | | Non |
|Genre du candidat | F ou M (Féminin ou Masculin) | Non |
|Code Insee de la ville de naissance du candidat  | Remplir le code Insee de la ville de naissance **uniquement si le candidat est né en France** | Non |
|Code COG du pays naissance du candidat | Remplir le code COG du pays de naissance **uniquement si le candidat est né à l'étranger**| Non |
|Date de naissance du candidat | Date au format JJ/MM/AAAA | Oui |
|Identifiant de la certification | RS ou RNCP suivi de chiffres (ex: RS1234) | Oui |
|État du dossier de certification | Réussi, Échoué, À repasser, Abandonné, À contrôler, Prêt à passer, Enregistré, Refusé, À enregistrer | Oui |
|Date d'inscription à la certification | Date au format JJ/MM/AAAA | Non |
|Date d'obtention de la certification | Date au format JJ/MM/AAAA | Oui si l'état du dossier est "Réussi" sinon non |
|Identifiant du parchemin | | Oui si l'état du dossier est "Réussi" et que vous avez un numéro de parchemin sinon non |
|Identifiant technique | Identifiant utilisé pour accrocher le candidat | |
|Siret de l'organisme partenaire | | Applicable si il y a un partenaire |


Le **code COG d'un pays** est composé du chiffre **"99" puis de 3 chiffres**, par exemple :
- Espagne : 99134
- États-Unis : 99404
- Maroc : 99350

Le **code Insee d'une ville** est composé de **5 chiffres**, par exemple:
- Brest : 29019
- Nice : 06088
- Strasbourg : 67482
Attention : le code Insee est différent du code postal. 

### Soumettre le fichier

Une fois rempli, vous pouvez déposer le fichier depuis le profil de votre organisme ["Mon compte"](/profil/organisme) "Importer mon historique de dossiers de certification".

Cette opération peut prendre jusqu'à plusieurs minutes selon le nombre de dossiers mis à jour. Si le traitement échoue, vérifiez la taille du fichier. S'il fait plus de quelques centaines de lignes, Wedof ne pourra pas le traiter, il faudra alors le découper.

Vous serez informé au début et à la fin du traitement de votre fichier par mail à l'adresse de l'utilisateur courant. À l'issue du traitement, vous obtiendrez en retour un fichier de rapport d'import.

En cas d'incompréhension, vous pouvez nous faire parvenir ce fichier ainsi que votre fichier Excel afin que nous les analysions.

Attention, si il existe plusieurs dossiers de certification associés à l'email du candidat et sur une certification, le traitement ne sera pas effectué. 
