## Tableau de correspondance Wedof <-> dictionnaire XML

Ce tableau présente la correspondance entre certains attributs présents sur les différents objets Wedof et l'attribut correspondant dans le XML.

Il complète la [documentation](/assistance/guides/certificateurs/accrochage) qui explique comment Wedof vous aide à l'accrochage des données de certification.

Il fait référence aux attributs XML définis dans le "Dictionnaire des données" fourni par la CDC : https://certificateurs.moncompteformation.gouv.fr/page-guide

À ce jour, ce document présente principalement les données obligatoires et il sera mis à jour prochainement avec plus de détails.

|  Entité | Attributs | Nom fichier xml CDC | Description et Valeurs possibles | Obligatoire |
| -------------- |:---------------:| :-----------:| :-----------:| :-----------:|
| Organism | cdcClientId | idClient | Numéro de la fiche client Certificateur | Oui |
| Organism | cdcContractId| idContrat | Numéro de contrat spécifique Certificateur| Oui |
| Organism |  applicantType | natureDeposant | CERTIFICATEUR |  Oui |
| Certification | type | type | Type de la certification (RNCP ou RS) |    Oui |
| Certification | code | code | External id de la certification composé du type et du code|  Oui |
| Certification | obtentionSystem | obtentionCertification | Le mode d'obtention de la certification. Si rempli au niveau de la certification, les dossiers héritent du mode d'obtention |  Oui |
| CertificationFolder | id | idTechnique | L'id technique du dossier de certification | Oui |
| CertificationFolder | digitalProofLink | urlPreuve | Lien vers la preuve numérique de l'obtention | Non |
| CertificationFolder | optionName | libelleOption | Nom de l'option / mention associé au dossier de certification si applicable|  Non |
| CertificationFolder | certifiedData | obtentionCertification | True |  Oui |
| CertificationFolder | examinationDate | dateDebutExamen | Date de début de passage de l'examen | Non |
| CertificationFolder | examinationEndDate | dateFinExamen | Date de fin de passage de l'examen, par défault la date de début de passage| Non |
| CertificationFolder | examinationType | modalitePassageExamen | Modalité de passage de l'examen | Non |
| CertificationFolder | examinationCenter ZipCode | codePostalCentreExamen | Code postal du centre d'examen principal | Non |
| CertificationFolder | issueDate | dateDebutValidite | Date de début de validité | Oui |
| CertificationFolder | | dateFinValidite | Date de fin de validité selon la durée de validité de la certification si applicable | Oui * |
| CertificationFolder | europeanLanguageLevel | niveauLangueEuropeen | Niveau de langue Européen si applicable | Non |
| CertificationFolder | | presenceNiveau LangueEuropeen | Si europeanLanguageLevel est renseigné alors true sinon false |  Oui
| CertificationFolder | detailedResult | scoring | Score ou notation obtenu à l'issu de l'examen si applicable  |  Oui * |
| CertificationFolder | gradePass | mentionValidee | Mention obtenue si applicable | Oui * |
| CertificationFolder | verbatim | verbatim | |   Non |
| CertificationFolder | accessModality| modaliteAcces |  Modalités d'accès à la certification |  Oui * |
| CertificationFolder | accessModalityVae | voieAccessVAE | Voie d'accès VAE | Oui ** |
| CertificationFolder | type | initiativeInscription | L'initiative de l'inscription à la certification |  Oui * |
| CertificationFolder | enrollmentDate | dateInscription | Date d'inscription à la certification |  Non |
| Attendee | birthName | nomNaissance |  Nom de naissance, si non renseigné nom de famille | Oui *** |
| Attendee | lastName | nomUsage |  Nom de famille |  Oui *** |
| Attendee | firstName | prenom1 |  Prénom |  Oui *** |
| Attendee | firstName2 | prenom2 | Deuxième prénom | Non *** |
| Attendee | firstName3 | prenom3 |  Troisième prénom | Non *** |
| Attendee | dateOfBirth | anneeNaissance & moisNaissance & jourNaissance | Date de naissance | Oui *** |
| Attendee | gender | sexe |  Sexe | Oui *** |
| Attendee | codeCityOfBirth | codeInsee | Code Insee de naissance si né(e) en France|  Oui *** |
| Attendee | nameCityOfBirth | libelleCommuneNaissance | Lieu de naissance si né(e) en France |  Oui *** |
| Attendee | codeCountryOfBirth | codePaysNaissance | Code COG du pays de naissance |  Oui *** |
| Attendee | nameCountryOfBirth  | libellePaysNaissance | Nom du pays de naissance |  Oui *** |


*Les données sont dites obligatoires mais sont renvoyées en tant que null si non valorisées.

**Le champ 'accessModalityVae' est obligatoire si 'accessModality' est 'VAE'.

***Les données relatives à l'apprenant ne concernent que les dossiers de certification hors parcours CPF.

## Valeurs acceptées

### obtentionSystem
<ul><li>PAR_SCORING</li><li>PAR_ADMISSION</li></ul>

### examinationType
<ul><li>A_DISTANCE</li><li>EN_PRESENTIEL</li><li>MIXTE</li></ul>

### europeanLanguageLevel
<ul><li>C2</li><li>C1</li><li>B2</li><li>B1</li><li>A2</li><li>A1</li><li>INSUFFISANT</li></ul>

### gradePass
<ul><li>SANS_MENTION</li><li>MENTION_ASSEZ_BIEN</li><li>MENTION_BIEN</li><li>MENTION_TRES_BIEN</li><li>MENTION_TRES_BIEN_AVEC_FELICITATIONS_DU_JURY</li></ul>

### accessModality
<ul><li>FORMATION_INITIALE_HORS_APPRENTISSAGE</li><li>FORMATION_INITIALE_APPRENTISSAGE</li><li>FORMATION_CONTINUE_HORS_CONTRAT_DE_PROFESSIONNALISATION</li><li>FORMATION_CONTINUE_CONTRAT_DE_PROFESSIONNALISATION</li><li>VAE</li><li>EQUIVALENCE_(DIPLOME_ETRANGER)</li><li>CANDIDAT_LIBRE</li></ul>

### accessModalityVae
<ul><li>CONGES_VAE</li><li>VAE_CLASSIQUE</li></ul>

### type
<ul><li>CERTIFIE</li><li>OF</li><li>POLE_EMPLOI</li><li>EMPLOYEUR</li><li>AUTRE</li></ul>

### gender
<ul><li>M</li><li>F</li></ul>

