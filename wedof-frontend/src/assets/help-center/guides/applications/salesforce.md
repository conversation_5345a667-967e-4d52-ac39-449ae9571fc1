## Principes

Cette application connecte Wedof et Salesforce afin de vous faire gagner du temps et de la fiabilité dans le suivi commercial de vos dossiers de formation.

Elle permet la mise à jour automatique des données depuis le Dossier de formation d'un apprenant vers Salesforce. Le Dossier de formation Wedof est associé à l'objet Opportunité de Salesforce, et les données métier EDOF sont sauvegardées et mises à jour dans des *Champs personnalisés* de l'Opportunité. Vous pouvez également recevoir les données de paiement.
 
L'Application permet aussi d'automatiser depuis Salesforce les actions qui font évoluer le Dossier de formation dans Wedof et chez le financeur (EDOF, Kairos etc.).

La philosophie derrière cette Application est d'être aussi peu intrusive que possible. Côté Salesforce, c'est à vous de "brancher" vos automatisations (*Process* ou *Flow*) sur les *Champs personnalisés* et *Actions* inclus dans l'Application d'après vos règles de travail. Cela vous permet d'adapter l'intégration à vos besoins.

![Intégration Salesforce / Wedof](/assets/images/help-center/guides/applications/salesforce/salesforce-widget.png "Intégration Salesforce / Wedof")

## Prérequis

Pour utiliser l'application, vous devez avoir une instance Salesforce avec les accès Apex et API, ainsi que les "Comptes Personnels" ("Person Accounts") pour une utilisation B2C activés.

## Fonctionnalités

### Fonctionnalités disponibles
* Créer automatiquement une Opportunité la première fois qu'un Dossier de formation parvient à Salesforce (Optionnel, désactivé par défaut)
    * Avec création automatique du *Compte* personnel si un Compte n'est pas trouvé
    * Avec reprise d'un *Compte* personnel existant si trouvé d'après son adresse email ou numéro de téléphone portable (les données existantes sont conservées)
    * (Optionnel) Avec reprise et conversion d'une *Piste* d'après son adresse email ou numéro de téléphone portable (les données existantes sont reprises) 
* Mettre à jour automatiquement une Opportunité depuis un Dossier de formation (requiert une Opportunité préalablement créée automatiquement par l'Application ou le remplissage préalable du champ *WedofRegistrationFolderId__c*)
* Mise à jour des dossiers de formation depuis Salesforce via des Actions dans les Flux (Flows)
* Récupération des paiements dans un objet personnalisé
* Afficher un widget Wedof dans l'Opportunité (Optionnel, à ajouter à la page de l'Opportunité)
    * Affiche les *Champs personnalisés* principaux issus de Wedof
    * Affiche un bouton pour accéder au Dossier de formation EDOF depuis l'Opportunité (inclus dans le widget)
    * Affiche un bouton pour Valider un Dossier de formation s'il est à l'état "À traiter" (*notProcessed*)

### Relation entre Dossier de formations et Comptes personnels

Lorsqu'un *Compte personnel* (*PersonAccount*) ou une *Piste* (*Lead*) sont trouvés, leurs données sont reprises sans être modifiées.

Dans le cas contraire, un *Compte* personnel est créé et les champs suivants sont remplis à partir des données de l'apprenant issues de son Dossier de formation :
* `FirstName`: Prénom
* `LastName`: Nom
* `Phone`: Numéro de téléphone fixe
* `PersonMobilePhone`: Numéro de téléphone mobile
* `PersonEmail`: Adresse email
* `BillingStreet`: Numéro de rue et rue de l'adresse de l'apprenant
* `BillingCity`: Ville de l'adresse de l'apprenant
* `BillingPostalCode`: Code postal de l'adresse de l'apprenant
* `BillingCountry`: Pays de l'adresse de l'apprenant, **ATTENTION ce champ peut être vide si le pays est la France**

## Relation entre Dossier de formation et Opportunités

Le lien entre le Dossier de formation et l'Opportunité correspondante est établi à partir de l'identifiant de dossier, stocké dans un champ personnalisé de l'Opportunité :
* `WedofRegistrationFolderId__c`: Identifiant de dossier 

Dans le cadre d'un financement CPF, cet identifiant est l'identifiant de dossier EDOF.

Si l'Opportunité est créée par l'intégration, outre les *Champs personnalisés* de l'Application, seuls les champs obligatoires sont remplis par l'Application. La date de clôture est définie à la date de début de Session de formation, ou à défaut à un mois après la date de création de l'opportunité. Par défaut, la première étape est utilisée, mais vous pouvez choisir une autre étape dans la configuration de l'Application.

Lors d'une mise à jour d'un Dossier de formation, l'intégration recherche une Opportunité avec l'*Identifiant de dossier* correspondant et la met à jour. Pour les données historiques, il est donc possible de lier des Opportunités existantes aux dossiers correspondants en remplissant ce champ.

D'autres champs personnalisés sont remplis par l'intégration à la création ou à la mise à jour de l'Opportunité :
* `WedofActionId__c`: Identifiant de l'action de formation
* `WedofAmountToInvoice__c`: Montant total à facturer
* `WedofBillingState__c`: État de facturation du dossier
* `WedofBillNumber__c`: Numéro de facture
* `WedofParentProposalCode__c`: Code de la proposition générique parente
* `WedofProposalCode__c`: Code de la proposition individuelle
* `WedofSessionEndDate__c`: Date de fin de session
* `WedofSessionId__c`: Identifiant de session de formation
* `WedofSessionStartDate__c`: Date de début de session
* `WedofOrganismSiret__c`: SIRET de l'organisme de formation
* `WedofState__c`: État du dossier
* `WedofTotalIncl__c`: Montant total du dossier
* `WedofTrainingId__c`: Identifiant de formation
* `WedofType__c`: Type de financement du dossier

Ces champs ne doivent pas être modifiés depuis Salesforce car leur valeur pourrait être écrasée depuis une mise à jour du Dossier de formation. En revanche, vous pouvez vous baser dessus afin de consolider vos propres champs et automatisations depuis les *Process* / *Flows*.

Lors de la mise à jour d'une Opportunité par l'Application, seuls les *Champs personnalisés* fournis par l'Application sont modifiés.

## Evolution de l'état du dossier

Voici le Workflow avec les valeurs que peut prendre le champ *État du dossier* :
![Widget Wedof](/assets/images/help-center/guides/applications/salesforce/wedof-states-workflow.png "Wedof states workflow")

À titre indicatif, le contexte commercial correspondant est fourni : "Opportunité gagnée", "Opportunité perdue" etc., cependant l'Application ne modifie pas l'Étape de l'opportunité, c'est à vous de le faire via un *Process* ou un *Flow*.

Attention, lorsque la création d'Opportunité est activée, l'Opportunité est créée la première fois que le Dossier de formation parvient à Salesforce. Si le dossier est mis à jour juste après l'activation de l'Application, l'Opportunité peut être créée depuis un dossier déjà avancé dans le Workflow, **une Opportunité peut donc être créée dans Salesforce avec n'importe quel état dans le champ État du dossier**.

**L'Application garantit que le sens du Workflow est respecté**, à savoir qu'une mise à jour apportera nécessairement soit le même *État du dossier*, soit un *État du dossier* plus avancé dans le Workflow.

En revanche, attention : l'**Application ne garantit pas que tous les État du dossier consécutifs seront parcourus**, vous pouvez donc observer qu'une Opportunité saute un ou plusieurs états d'un coup.

## Mise à jour des dossiers de formation depuis Salesforce (Actions / Process / Flows)

Des opérations de Wedof sont intégrées directement dans Salesforce sous la forme d'Actions Apex.

![Actions Apex](/assets/images/help-center/guides/applications/salesforce/salesforce-action-list.png "Actions Apex")

Cela signifie que vous pouvez les utiliser dans les Flows et les Process afin d'agir sur les dossiers de formation depuis Salesforce.

![Exemple de Flow](/assets/images/help-center/guides/applications/salesforce/salesforce-flow-example.png "Exemple de Flow")

Pour fonctionner, afin de s'authentifier auprès de Wedof ces actions nécessitent de renseigner un Jeton d'API Wedof (cf. section "Configurer l'Application dans Salesforce").

### Action de consultation de données

L'intégration propose une action de consultation de données :
* Récupération des dates minimum d'entrée en formation

### Actions de mise à jour

L'intégration propose 3 catégories d'action de mise à jour sur les dossiers de formation
* **Changement d'état** du dossier de formation vers 'Validé', 'En Formation', 'Sortie de formation', 'Service Fait déclaré', 'Annulé' ou 'Refusé'. Ces actions disposent de paramètres spécifiques à chaque état cible.
* **Changement de l'état de facturation** du dossier de formation vers l'état "Facturé" avec un numéro de facture.
* **Mise à jour des données** du dossier de formation, notamment le prix et les dates de session en vue de le valider.

En plus de leurs paramètres spécifiques, ces actions prennent comme paramètre obligatoire l'Opportunité associée au dossier de formation (elle doit donc être liée à Wedof via le champ `WedofRegistrationFolderId__c`).

Ces actions retournent comme résultat l'Opportunité mise à jour.

Attention, ces actions ne sont pas optimisées pour un traitement par lot et peuvent être soumises à des "rate limits", il est donc recommandé de limiter le nombre de dossiers manipulés simultanément.

## Récupération des paiements dans un objet personnalisé

Wedof dispose des informations de paiements liées aux dossiers de formation, notamment pour le financeur EDOF.

Vous pouvez activer une option dans la configuration de l'App Salesforce dans Wedof afin de recevoir ces paiements dans Salesforce sous la forme d'un objet personnalisé.

### Objet personnalisé

Pour vous donner le plus de flexibilité possible dans le traitement que vous ferez de ces informations, elles sont stockées dans l'objet personnalisé `WedofPayment__c`.

Voici les champs de cet objet :
* `WedofId__c` : Id technique Wedof du paiement
* `WedofRegistrationFolderId__c` : Identifiant du dossier de formation
* `WedofAmount__c` : Montant du paiement
* `WedofState__c` : État du paiement
* `WedofType__c` : Type du paiement
* `WedofBillNumber__c` : Le numéro de facture associé au paiement
* `WedofScheduledDate__c` : Date prévue du paiement

Lors de sa réception dans Salesforce, si l'objet n'existe pas alors il est créé, sinon il est mis à jour.

Il n'y a pas de lien fort avec une Opportunité, c'est à vous d'établir la relation via l'égalité du champ `WedofRegistrationFolderId__c`.

## Installation et configuration

### 1. Installation du Package sur votre instance Salesforce

Obtenez le lien d'installation en vous connectant à Wedof et en activant l'Application Salesforce :
![Mes applications](/assets/images/help-center/guides/applications/salesforce/mes-applications.png "Mes applications")
![Activer l'Application Salesforce](/assets/images/help-center/guides/applications/salesforce/activer-app-salesforce.png "Activer l'Application Salesforce")
![Copier le lien d'installation](/assets/images/help-center/guides/applications/salesforce/configurer-app-salesforce.png "Copier le lien d'installation")
Puis installez l'Application sur votre instance depuis un compte administrateur (l'installation peut prendre quelques minutes).

![Installation](/assets/images/help-center/guides/applications/salesforce/installation.png "Installation")

Pour installer l'Application sur une "Sandbox" Salesforce, il faut modifier l'URL d'installation en remplaçant le sous domaine `login` par `test` : https://test.salesforce.com/packaging/installPackage.apexp?...

### 2. (Optionnel) Récupération de votre token d'API Wedof

Si vous souhaitez utiliser les Actions de mise à jour des dossiers, Salesforce devra avoir un accès authentifié à Wedof.

Pour cela, dans Wedof, récupérez votre Token d'API et mettez le côté : il vous servira lors de la configuration Salesforce.

![Mon compte](/assets/images/help-center/guides/applications/salesforce/mon-compte.png "Mon compte")

![Jeton d'API](/assets/images/help-center/guides/applications/salesforce/jeton.png "Jeton d'API")

### 3. Configurer l'Application dans Salesforce

Afin de pouvoir sauvegarder la configuration de l'Application dans Salesforce, il faut d'abord activer la gestion des métadonnées via Apex :
Configuration > Code personnalisé > Paramètres Apex > Cochez "Déployer les métadonnées de versions de packages non certifiés via Apex" et cliquez sur "Enregistrer"
![Activez la gestion des métadonnées via Apex](/assets/images/help-center/guides/applications/salesforce/salesforce-activer-metadata.png "Activez la gestion des métadonnées via Apex")

Ensuite, ouvrez l'application Wedof depuis le lanceur d'application.
![Ouvrez l'App Wedof](/assets/images/help-center/guides/applications/salesforce/salesforce-app-wedof.png "Ouvrez l'App Wedof")

Si vous souhaitez mettre à jour les dossiers depuis Salesforce, saisissez le Jeton d'API Wedof dans le champ "Clé d'API Wedof".

![Configurez l'App Wedof](/assets/images/help-center/guides/applications/salesforce/salesforce-configuration.png "Configurez l'App Wedof")

Vous pouvez également activer les options que vous souhaitez utiliser (création d'Opportunité, conversion de Piste...).

N'oubliez pas de cliquer sur "Sauvegarder".

### 4. Connecter Wedof à Salesforce

Retournez dans Wedof / Mes applications / Salesforce :

Configurez ensuite l'adresse de votre instance Salesforce :
![Configurer l'Application Salesforce](/assets/images/help-center/guides/applications/salesforce/configurer-app-salesforce.png "Configurer l'Application Salesforce")

Connectez-vous à Salesforce et donnez l'accès à Wedof :
![Donnez l'accès](/assets/images/help-center/guides/applications/salesforce/salesforce-acces.png "Donnez l'accès")

Le compte Salesforce choisi sera l'auteur des modifications qui seront effectuées dans Salesforce, et devra donc avoir les permissions nécessaires.

### 5. Configurer l'accès aux champs personnalisés

Dans Salesforce, afin de les utiliser dans l'interface, les Flow ou les Process, vous devez configurer les permissions d'accès aux champs personnalisés ajoutés par l'Application.

Les droits d'accès à ces champs peuvent être configuré depuis l'Administration Salesforce : 
* soit depuis la gestion des Profils : *Utilisateurs > Profils > profil concerné > Paramètres d'objet > Opportunités > Modifier > cochez "Accès en lecture"*
* soit depuis chaque champ : *Objets et champs > Gestionnaire d'objet > Opportunité > Champs et relations > champ concerné > Définir la sécurité au niveau du champ > cochez "Visible"*

Il faudra faire de même pour les champs personnalisés de WedofPayment__c si vous les utilisez.

### 6. (Optionnel) Ajoutez le widget "Wedof" aux Opportunités

L'Application propose un Widget dédié à la page de l'Opportunité qui affiche certains champs personnalisés.

![Ajoutez le widget à la page d'Opportunité](/assets/images/help-center/guides/applications/salesforce/salesforce-add-widget.png "Ajoutez le widget à la page d'Opportunité")

Attention, pour que ces données soient visibles, la sécurité au niveau des champs doit être en "Visible" pour les profils concernés.
![Widget Wedof](/assets/images/help-center/guides/applications/salesforce/salesforce-widget.png "Widget Wedof")

Ce widget est un moyen de valider visuellement que les données remontent bien, mais il n'est pas adapté à une utilisation en "Production".

Il est ainsi préférable intégrer ces champs dans la page des Opportunités d'une façon plus fine pour répondre à vos besoins.

### 7. (Optionnel) Configuration des actions dans les Flux (Flows)

Les actions de mise à jour de dossiers réalisent deux opérations :
1. Envoi de l'action à Wedof (et EDOF le cas échéant),
2. En cas de réussite, mise à jour de l'opportunité correspondante, qui est ensuite retournée par l'action.

Pour que cela fonctionne, quand cette option est disponible il est nécessaire d'indiquer à Salesforce sur l'action qu'il faut lancer une nouvelle transaction :

![Configurez les transactions](/assets/images/help-center/guides/applications/salesforce/salesforce-action-new-transation.png "Configurez les transactions")

Les champs disponibles dans les différentes actions correspondent aux endpoints proposés par Wedof sur les dossiers de formation (Registration Folders). Pour plus de détails sur ces champs, consultez [la documentation de l'API](https://www.wedof.fr/api/doc).

![Configurez l'action](/assets/images/help-center/guides/applications/salesforce/salesforce-action-fields.png "Configurez l'action")

Le seul champ particulier est le champ "Jeton d'API". Il est facultatif et constitue une alternative au fait de configurer le Jeton au niveau du package (section 3.).

### Historique des versions

* `V2.4`  (03/02/2025) : Ajout de la date de sortie de formation sur l'action "Déclarer le service fait".
* `V2.3`  (03/02/2025) : Correction d'un bug sur l'action "Déclarer le service fait".
* `V2.2`  (03/02/2025) : Support de l'état EDOF 'rejectedWithoutOfSuite'.
* `V2.1`  (08/10/2024) : Actions pour mettre à jour les dossiers dans les Flow + récupération des paiements dans un objet personnalisé.
* `V1.22` (12/06/2024) : Support des états "rejectedWithoutCdcSuite" et "canceledByFinancer"
* `V1.21` (16/04/2024) : Support de l'état EDOF "rejected" (vieux dossiers "Validés" annulés automatiquement par EDOF après un long délai)
* `V1.20` (19/02/2024) : Ajout du type de financement (`WedofType__c`) + Support des dossiers Kairos AIF
* `V1.16` (15/02/2023) : Ajout du code individuel de proposition (`WedofProposalCode__c`)
* `V1.15` (19/01/2023) : Ajout du SIRET de l'organisme de formation (`WedofOrganismSiret__c`)
* `V1.13` (16/12/2022) : Ajout de l'état de facturation du dossier (`WedofBillingState__c`) + Ajout du numéro de facture (`WedofBillNumber__c`) + Si création de Compte Personnel, écriture de l'adresse de facturation d'après l'adresse EDOF
* `V1.8`  (21/06/2022) : Correction d'un bug bloquant empêchant la création d'opportunité sur certaines instances
* `V1.5`  (25/08/2021) : Ajout de l'option de conversion de piste + Ajouter l'option de création d'opportunité + Ajout du code de proposition générique (`WedofParentProposalCode__c`)
* `V1.0`  (01/06/2021) : Version initiale
