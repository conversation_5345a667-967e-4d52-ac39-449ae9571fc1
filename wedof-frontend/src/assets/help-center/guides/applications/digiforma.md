## Synchronisation des données avec Digiforma

La synchronisation des données est un processus pour alimenter Digiforma à partir d'événement Wedof. Cette documentation
explique comment les données sont synchronisées de manière automatique, de Wedof vers Digiforma.

### Synchronisation automatique

La synchronisation des données est automatisée grâce à l’utilisation de l’API Graphql Digiforma. Elle s’effectue
exclusivement dans un seul sens, c'est-à-dire de Wedof vers Digiforma.

### Prérequis

Avant de pouvoir activer les processus de synchronisation, assurez-vous que l’application Digiforma est activée sur
Wedof et que le token API soit valide. Vous pouvez suivre les étapes détaillées ci-dessous pour appliquer la
configuration requise.

#### Activation de l’application Digiforma dans Wedof

- Se rendre sur Wedof.
- Aller dans le menu application.
  ![](/assets/images/help-center/guides/applications/digiforma/menu-application.png)
- Cliquer sur le bouton activer de la ligne Digiforma.
  ![](/assets/images/help-center/guides/applications/digiforma/menu-application-digiforma.png)

Vous serez ensuite redirigé vers la page de configuration de l’application Digiforma.

#### Activation du token dans Digiforma

Depuis la page de configuration de l’application Digiforma :

- Cliquer sur le lien récupérer le token sur Digiforma.
  ![](/assets/images/help-center/guides/applications/digiforma/lien-digiforma.png)
- Si le token n’est pas présent, demander au support Digiforma pour obtenir un token d’interconnexion (cliquer sur "
  Contactez-nous" en bas à gauche et ouvrir une nouvelle conversation).

Une fois le token actif et généré, il sera présent dans la zone violette ci-dessous.
![](/assets/images/help-center/guides/applications/digiforma/digiforma-token.png)

#### Ajout des identifiants Digiforma dans Wedof

Depuis la page de l’application Digiforma, renseigner vos identifiants et cliquer sur valider. Un message
s'affichera via une pop-up et une validation de formulaire vous confirmera si la connexion a été établie avec Digiforma.

#### Vérification de la connexion avec Digiforma

Une fois vos identifiants renseignés, un message indiquera si la connexion est toujours active. Cette vérification s'effectue
automatiquement chaque fois que vous accédez à la page de configuration de l'application Digiforma ou qu'un événement de
création ou de mise à jour de dossier est déclenché.

### Activation des processus

Les processus de synchronisation ne sont activés que lors de la création et de la modification d’un dossier de formation
dans Wedof et uniquement pour les dossiers de formation de type CPF.

#### Processus des apprenants

Récupération de la liste des apprenants Digiforma en filtrant avec l’adresse email d’un apprenant lié au dossier de
formation.

- Si aucun résultat n’est présent dans la base de données Digiforma, l’apprenant sera créé.
- Sinon, l’apprenant sera modifié dans Digiforma.

Note : Un apprenant Digiforma peut être lié à plusieurs sessions et dossiers commerciaux Digiforma.

#### Processus des sessions

Récupération de la liste des sessions Digiforma en filtrant avec le titre de l’action de formation et le numéro de
dossier CPF lié à la session du dossier de formation.

- Si aucun résultat n’est présent dans la base de données Digiforma, la session sera créée.
- Sinon, la session sera modifiée dans Digiforma.

Note : Une session est unique et peut être liée à plusieurs dossiers commerciaux.

#### Processus de la gestion commerciale

Récupération de la liste des dossiers commerciaux Digiforma en filtrant avec le nom de l’apprenant lié au dossier de
formation.

- Si des dossiers commerciaux existent déjà, une correspondance est recherchée avec l'apprenant, la session et le numéro
  de dossier CPF du dossier de formation.
- Si aucune correspondance n’est trouvée dans la base de données de Digiforma, un nouveau dossier commercial est créé.
- Sinon, le dossier sera modifié dans Digiforma.

Note : Un dossier commercial est unique et est lié à un apprenant, à une session et à un numéro de dossier CPF.

#### Configuration de la correspondance des statuts Digiforma et Wedof

Pour personnaliser la correspondance des statuts entre Digiforma et Wedof, qui sera appliquée lors de l'exécution des processus, vous avez la possibilité de modifier le statut par défaut Digiforma qui est associé à un statut spécifique Wedof.

La liste des status de correspondance par défaut entre les statuts Digiforma et Wedof est la suivante :

- **Entrant** : À traiter
- **Discussion** : Annulé (par l'organisme), Annulé (par le financeur), Refus titulaire, Refusé (par l'organisme), Annulé sans suite
- **Devis** : Validé, Validé (En cours d'instruction Pôle emploi)
- **Convention** : Accepté, En formation, Sortie de formation
- **Facture** : Service fait déclaré, Service fait validé, Annulé (par le titulaire), Annulation titulaire (non réalisé)

Pour modifier le statut Digiforma, veuillez cliquer sur la liste déroulante correspondant au statut Wedof que vous souhaitez modifier. Les options disponibles sont les suivantes : Entrant, Discussion, Devis, Convention, Facture (et tout autre statut Digiforma créé depuis l'application Digiforma).

![](/assets/images/help-center/guides/applications/digiforma/mapping-wedof-digiforma.png)

#### Activation des processus de facturation et paiements

Pour activer les processus de facturation et paiements, il faut se rendre sur la page de configuration de
l'application Digiforma et cocher _Activation de la facturation Digiforma_.
![](/assets/images/help-center/guides/applications/digiforma/process-invoice.png)

#### Processus de facturation

Le processus de facturation n’est activé que pour les dossiers en statut de facturation : à facturer, facturé, payé.

Récupération des factures depuis la session Digiforma en filtrant par rapport au dossier commercial Digiforma.

- Si aucune facture correspondante n’est trouvée, une nouvelle facture est créée.
- Sinon, la facture sera modifiée dans Digiforma.

#### Processus des paiements

Le processus de paiement n’est activé que pour les factures en statut “payé” et pour les paiements en statut “émis”.

Récupération des paiements depuis la facture qui est liée au dossier commercial et à la session.

- Vérification que le paiement n’a pas déjà été renseigné dans Digiforma, en se basant sur la date d’émission du
  paiement et le montant de celui-ci.
- Si aucune correspondance n’est trouvée dans la base de données Digiforma, un nouveau paiement est créé.

### Activités Wedof

Lors de la création d'un dossier de formation dans digiforma une activité est ajouté au dossier Wedof. Cette activité
est un lien qui permet d'accéder à la fiche client de l'apprenant sur Digiforma.
![](/assets/images/help-center/guides/applications/digiforma/activity-link.png)
