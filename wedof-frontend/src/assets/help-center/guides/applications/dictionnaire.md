Retrouvez ici l'ensemble des variables Wedof :

## Variables communes

| Variable            |      Description      |      Exemple       | 
|---------------------|:---------------------:|:------------------:|
| {{common.date}}     |     Date du jour      |     31/05/2024     |
| {{common.datetime}} | Date et heure du jour | 31/05/2024 à 16h26 |

## Candidat / Apprenant

| Variable                 |       Description        |             Exemple             | 
|--------------------------|:------------------------:|:-------------------------------:|
| {{attendee.firstName}}   |          Prénom          |              Jane               |
| {{attendee.lastName}}    |           Nom            |               Doe               |
| {{attendee.email}}       |          Email           |        <EMAIL>         |
| {{attendee.dateOfBirth}} |    Date de naissance     |           18/04/1993            |
| {{attendee.placeOfBirth}}|    Lieu de naissance     |          Toulouse (31)          |
| {{attendee.phoneNumber}} |   Numéro de téléphone    |           0650505050            |
| {{attendee.phoneFixed}}  | Numéro de téléphone fixe |           0987654321            |
| {{attendee.gender}}      |          Genre           |                M                |
| {{attendee.degreeTitle}} | Titre du diplôme actuel  |     niveau Bac+3 (Licence)      |
| {{attendee.fullAddress}} |     Adresse complète     | 8 Impasse Bonnet 31500 Toulouse |

## Formation

| Variable                                    |           Description            |                    Exemple                    | 
|---------------------------------------------|:--------------------------------:|:---------------------------------------------:|
| {{training.title}}                          |       Nom de la formation        |            Formation en marketing             |
| {{training.expectedResult}}                 | Résultat attendu de la formation |       Maitrise des notions de marketing       |
| {{training.teachingFees}}                   |      Tarif de la formation       |                     1200                      |
| {{training.conditionsPrerequisitesDetails}} |    Prérequis de la formation     |             Maitrise de l'anglais             |
| {{training.sessionStartDate}}               |     Date de début de session     |                  10/12/2023                   |
| {{training.sessionEndDate}}                 |      Date de fin de session      |                  15/12/2023                   |
| {{training.indicativeDuration}}             |      Durée de la formation       |                      24                       |
| {{training.trainingGoal}}                   |       Objectif pédagogique       | Savoir [...] Comprendre [...] Appliquer [...] |

## Proposition Commerciale

| Variable                        |          Description           |                          Exemple                          | 
|---------------------------------|:------------------------------:|:---------------------------------------------------------:|
| {{proposal.state}}              |     État de la proposition     |                            Vue                            |
| {{proposal.code}}               |     Code de la proposition     |                          H123AB                           |
| {{proposal.permalink}}          |       Lien interne Wedof       |          https://www.wedof.fr/proposition-XXXXXX          |
| {{proposal.link}}               |         Lien Apprenant         | https://www.wedof.fr/funnel/apprenant/proposition/XXXXXX  |
| {{proposal.link_commercial}}    |        Lien Commercial         | https://www.wedof.fr/funnel/commercial/proposition/XXXXXX |
| {{proposal.email}}              |             Email              |                       <EMAIL>                        |
| {{proposal.firstName}}          |             Prénom             |                           John                            |
| {{proposal.lastName}}           |         Nom de famille         |                            Doe                            |
| {{proposal.phoneNumber}}        |      Numéro de téléphone       |                        0650505050                         |
| {{proposal.sessionStartDate}}   |    Date de début de session    |                       12 Juin 2024                        |
| {{proposal.sessionEndDate}}     |     Date de fin de session     |                       15 Juin 2024                        |
| {{proposal.expire}}             |       Date d'expiration        |                        09/06/2024                         |
| {{proposal.indicativeDuration}} | Durée de formation (en heures) |                            14                             |
| {{proposal.amountDiscount}}     |            Montant             |                  Montant modifié de 15%                   |
| {{proposal.trainingActions}}    |      Actions de formation      |         valable pour toutes actions de formation          |
| {{proposal.sales}}              |        Agent Commercial        |                       <EMAIL>                        |

## Dossier de Formation

| Variable                                |          Description          |                      Exemple                       | 
|-----------------------------------------|:-----------------------------:|:--------------------------------------------------:|
| {{registrationFolder.attendeeLink}}     |       Espace apprenant        |     https://www.wedof.fr/apprenant-XXXXXXXXXXX     |
| {{registrationFolder.state}}            |        État du dossier        |                      Accepté                       |
| {{registrationFolder.externalId}}       |       Numéro de dossier       |                     123456789                      |
| {{registrationFolder.permalink}}        |      Lien interne Wedof       | https://www.wedof.fr/dossier-formation-XXXXXXXXXXX |
| {{registrationFolder.billingState}}     |      État de facturation      |                   Non facturable                   |
| {{registrationFolder.billNumber}}       |       Numéro de facture       |                     FAC-0123"                      |
| {{registrationFolder.amountTtc}}        |          Montant TTC          |                        990                         |
| {{registrationFolder.notes}}            |         Notes privées         |                    Texte libre                     |
| {{registrationFolder.completionRate}}   |       Taux d'avancement       |                         50                         |
| {{registrationFolder.terminatedReason}} | Raison de sortie de formation |                       Malade                       |
| {{registrationFolder.description}}      |          Description          |                    description                     |
| {{registrationFolder.amountToInvoice}}  |      Montant à facturer       |                        1200                        |
| {{registrationFolder.sessionCity}}      |     Lieu de la formation      |                     À distance                     |
| {{registrationFolder.completedHours}}   |       Heures attestées        |                        12.5                        |
| {{registrationFolder.surveyLink}}       |         Lien enquête          | https://www.wedof.fr/apprenant-XXXXXXXXXXX-enquete |

## Dossier de certification

| Variable                                      |                 Description                 |                        Exemple                         | 
|-----------------------------------------------|:-------------------------------------------:|:------------------------------------------------------:|
| {{certificationFolder.addToPassportLink}}     |     Ajouter au Passeport de Compétences     | https://www.wedof.fr/candidat-XXXX-XXXXXXXXX-passeport |
| {{certificationFolder.attendeeLink}}          |               Espace candidat               |      https://www.wedof.fr/candidat-XXXX-XXXXXXXXX      |
| {{certificationFolder.state}}                 |               État du dossier               |                       Enregistré                       |
| {{certificationFolder.externalId}}            |              Numéro de dossier              |                        1234567                         |
| {{certificationFolder.permalink}}             |             Lien interne Wedof              |   https://www.wedof.fr/dossier-certification-XXXXXX    |
| {{certificationFolder.certification}}         |          Titre de la certification          |              Animer des soirées dansantes              |
| {{certificationFolder.certifier}}             |            Nom du certificateur             |                    Wedof Certificateur                 |
| {{certificationFolder.codeReconnaissance}}    |           Code de reconnaissance            |                         RSXXXX                         |
| {{certificationFolder.franceCompetenceUrl}}   |           URL de la certification           |  https://www.francecompetences.fr/recherche/rs/R5XXXX  |
| {{certificationFolder.europeanLanguageLevel}} |          Niveau de langue Européen          |                           B1                           |
| {{certificationFolder.examinationDate}}       |           Date de début d'examen            |                       23/12/2023                       |
| {{certificationFolder.examinationTime}}       |           Heure du début d'examen           |                          15:00                         |
| {{certificationFolder.examinationType}}       |                Type d'examen                |                       A distance                       |
| {{certificationFolder.examinationEndDate}}    |            Date de fin d'examen             |                       30/12/2023                       |
| {{certificationFolder.examinationEndTime}}    |            Heure de fin d'examen            |                          17:00                         |
| {{certificationFolder.examinationPlace}}      |           Lieu de passage de l'examen       |                         Toulouse                       |
| {{certificationFolder.detailedResult}}        |       Détail du résultat de l'examen        |                        80 / 100                        |
| {{certificationFolder.comment}}               | Commentaire sur le dossier de certification |                      Texte libre                       |
| {{certificationFolder.digitalProofLink}}      |         Lien de la preuve numérique         |          https://proofExample.com/check/12345          |
| {{certificationFolder.gradePass}}             |               Mention obtenue               |                           B                            |
| {{certificationFolder.surveyLink}}            |                 Lien enquête                |  https://www.wedof.fr/candidat-XXXX-XXXXXXXXX-enquete  |
| {{certificationFolder.skills}}                |              Blocs de compétences           | BC01 Analyser les enjeux, BC02 Identifier les impacts  |

## Enquête de suivi d'insertion professionnelle

| Variable                                                     |                            Description                             |  Exemple   | 
|--------------------------------------------------------------|:------------------------------------------------------------------:|:----------:|
| {{certificationFolderSurvey.state}}                          |                         État de l'enquête                          |  En cours  |
| {{certificationFolderSurvey.initialExperienceAnsweredDate}}  |           Date de réponse à l'enquête en début de cursus           | 18/01/2024 |
| {{certificationFolderSurvey.sixMonthExperienceAnsweredDate}} |     Date de réponse à l'enquête 6 mois après la certification      | 18/07/2024 |
| {{certificationFolderSurvey.longTermExperienceAnsweredDate}} |      Date de réponse à l'enquête un an après la certification      | 18/01/2025 |
| {{certificationFolderSurvey.sixMonthExperienceStartDate}}    | Date de début de réponse à l'enquête 6 mois après la certification | 18/07/2024 |
| {{certificationFolderSurvey.longTermExperienceStartDate}}    | Date de début de réponse à l'enquête un an après la certification  | 18/01/2025 |

## Partenariat de certification

| Variable                                     |            Description             |              Exemple               | 
|----------------------------------------------|:----------------------------------:|:----------------------------------:|
| {{certificationPartner.certification}}       |     Titre de la certification      |    Animer des soirées dansantes    |
| {{certificationPartner.codeReconnaissance}}  |       Code de reconnaissance       |               RSXXXX               |
| {{certificationPartner.certifier}}           |        Nom du certificateur        |        Super Certificateur         |
| {{certificationPartner.certifierOwner}}      | Nom d'utilisateur du certificateur |            Jane Lennon             |
| {{certificationPartner.certifierSiret}}      |       Siret du certificateur       |           96245783546564           |
| {{certificationPartner.certifierCity}}       |       Ville du certificateur       |               Toulouse             |
| {{certificationPartner.certifierPostalCode}} |    Code postal du certificateur    |                 31500              |
| {{certificationPartner.certifierAddress}}    |      Adresse du certificateur      |           8 impasse Bonnet         |
| {{certificationPartner.certifierFullAddress}}|  Adresse complète du certificateur |  8 impasse Bonnet 31500 Toulouse   |
| {{certificationPartner.partner}}             |         Nom du partenaire          |     Les Meilleures Formations      |
| {{certificationPartner.partnerOwner}}        |  Nom de l'utilisateur partenaire   |              John Doe              |
| {{certificationPartner.partnerSiret}}        |        Siret du partenaire         |           31568768464768           |
| {{certificationPartner.partnerCity}}         |          Ville du partenaire       |               Toulouse             |
| {{certificationPartner.partnerPostalCode}}   |      Code postal du partenaire     |                 31500              |
| {{certificationPartner.partnerAddress}}      |       Adresse du partenaire        |           8 impasse Bonnet         |
| {{certificationPartner.partnerFullAddress}}  |   Adresse complète du partenaire   |  8 impasse Bonnet 31500 Toulouse   |
| {{certificationPartner.email}}               |         Mail du partenaire         |  <EMAIL>  |
| {{certificationPartner.state}}               |        État du partenariat         |               Actif                |
| {{certificationPartner.habilitation}}        |    Habilitation du partenariat     |      Habilitation pour former      |
| {{certificationPartner.certifierLink}}       |      Lien Wedof Certificateur      |            www.wedof.fr            |
| {{certificationPartner.partnerLink}}         |       Lien Wedof Partenaire        |            www.wedof.fr            |
| {{certificationPartner.comment}}             |     Commentaire du partenariat     |            Texte libre             |
| {{certificationPartner.amountHt}}            |     Prix HT par certification      |                100                 |

## Mon Organisme

| Variable                      |    Description       |             Exemple             | 
|-------------------------------|:--------------------:|:-------------------------------:|
| {{myOrganism.name}}           | Nom de mon organisme |          Wedof Organisme        |
| {{myOrganism.siren}}          |       Siren          |            332999924            |
| {{myOrganism.siret}}          |       Siret          |         33299992400012          |
| {{myOrganism.email}}          |       Email          |      <EMAIL>       |
| {{myOrganism.city}}           |       Ville          |            Toulouse             |
| {{myOrganism.postalCode}}     |     Code postal      |              31500              |
| {{myOrganism.address}}        |       Adresse        |         8 impasse Bonnet        |
| {{myOrganism.fullAddress}}    |  Adresse complète    | 8 impasse Bonnet 31500 Toulouse |

## Organisme certificateur

| Variable                             |    Description     |             Exemple             | 
|--------------------------------------|:------------------:|:-------------------------------:|
| {{certifierOrganism.name}}           | Nom de l'organisme |       Wedof Certificateur       |
| {{certifierOrganism.siren}}          |       Siren        |            332999924            |
| {{certifierOrganism.siret}}          |       Siret        |         33299992400012          |
| {{certifierOrganism.email}}          |       Email        |      <EMAIL>       |
| {{certifierOrganism.city}}           |       Ville        |            Toulouse             |
| {{certifierOrganism.postalCode}}     |     Code postal    |              31500              |
| {{certifierOrganism.address}}        |       Adresse      |         8 impasse Bonnet        |
| {{certifierOrganism.fullAddress}}    |  Adresse complète  | 8 impasse Bonnet 31500 Toulouse |

## Organisme de formation

| Variable                           |    Description     |             Exemple             | 
|------------------------------------|:------------------:|:-------------------------------:|
| {{trainingOrganism.name}}          | Nom de l'organisme |         Wedof Formation         |
| {{trainingOrganism.siren}}         |       Siren        |            332999924            |
| {{trainingOrganism.siret}}         |       Siret        |         33299992400012          |
| {{trainingOrganism.email}}         |       Email        |      <EMAIL>       |
| {{trainingOrganism.city}}          |       Ville        |            Toulouse             |
| {{trainingOrganism.postalCode}}    |     Code postal    |              31500              |
| {{trainingOrganism.address}}       |       Adresse      |         8 impasse Bonnet        |
| {{trainingOrganism.fullAddress}}   |  Adresse complète  | 8 impasse Bonnet 31500 Toulouse |

## Facture

| Variable                |        Description         |                    Exemple                     | 
|-------------------------|:--------------------------:|:----------------------------------------------:|
| {{invoice.externalId}}  |     Numéro de facture      |                  INV-EXT-1A2                   |
| {{invoice.state}}       |     Etat de la facture     |                     Payée                      |
| {{invoice.type}}        |      Type de facture       |               Facture d'acompte                |
| {{invoice.paymentLink}} |      Lien de paiement      |  https://www.stripe.com/invoices/INV-EXT-1A2   |
| {{invoice.description}} |        Description         | Facture Juin 2024 partenariat de certification |
| {{invoice.dueDate}}     | Date d'échéance de paiment |                31 Juillet 2024                 |

## Document

| Variable                 |                     Description                      |                           Exemple                            | 
|--------------------------|:----------------------------------------------------:|:------------------------------------------------------------:|
| {{document.name}}        |                    Nom du fichier                    |                    grille-évaluation.pdf                     |
| {{document.state}}       |                   Etat du document                   |                            Validé                            |
| {{document.comment}}     |             Commentaire sur le document              |                   complète et bien remplie                   |
| {{document.type}}        |               Type de document attendu               |                    La Grille d'évaluation                    |
| {{document.description}} |           Description du document attendu            | Le document attendu est une grille d'évaluation du candidat  |
| {{document.permalink}}   | Lien vers le document (pour le candidat / apprenant / partenaire) | https://www.wedof.fr/candidat-XXXX-XXXXXXXXX-document-XXXXXX |

## Audit

#### Document

Pour chaque critère renseigné pour une certification donnée

| Variable                                  |     Description     |
|-------------------------------------------|:-------------------:|
| {{audit.**criteriaCode**_title}}          |        Titre        |
| {{audit.**criteriaCode**_complianceIcon}} | Icône de conformité |
| {{audit.**criteriaCode**_compliance}}     |     Conformité      |
| {{audit.**criteriaCode**_requirement}}    |      Exigence       |
| {{audit.**criteriaCode**_value}}          | Valeur enregistrée  |

#### Message

| Variable                |           Description           |        Exemple        | 
|-------------------------|:-------------------------------:|:---------------------:|
| {{audit.state}}         |         État de l'audit         |       En cours        |
| {{audit.startDate}}     |         Date de l'audit         |      12/11/2024       |
| {{audit.endDate}}       |   Date de clôture de l'audit    |      13/11/2024       |
| {{audit.result}}        |       Résultat de l'audit       |       Conforme        |
| {{audit.notes}}         |        Notes de l'audit         |                       |
| {{audit.certifierLink}} | Lien de l'audit (certificateur) | https://www.wedof.fr/ |
| {{audit.partnerLink}}   |  Lien de l'audit (partenaire)   | https://www.wedof.fr/ |

## Fonctions pour modifier les valeurs obtenues

Le dictionnaire permet d'obtenir une valeur dynamique à partir d'une variable.

À l'aide de fonctions prédéfinies, il est également possible de demander à Wedof de réaliser des traitements sur cette
valeur avant de l'intégrer à vos documents / messages.

Le nom de la fonction à appliquer est ansi séparée du nom de la variable par une barre verticale (ou "pipe") `|` :
`{{xxx.yyy|fonction}}`

Si cette fonction accepte un paramètre, alors vous pouvez le fournir après le signe deux-points `:`:
`{{xxx.yyy|fonction:parametre}}`

Plusieurs fonctions peuvent être combinées en les séparant par des barres verticales `|` :
`{{xxx.yyy|fonction1:parametre1|fonction2|fonction3:parametre3}}`

#### Tronquer un texte : truncate

Il est possible de raccourcir le nombre de caractères d'une variable du dictionnaire grâce à la fonction `truncate` qui
prend **1 paramètre** définissant la taille souhaitée :
`{{xxx.yyy|truncate:taille}}`

Cela peut être utile lorsque vous souhaitez éviter un champ trop long, par exemple le contenu d'un SMS ou l'objet d'un
email.

Prenons un exemple concret : vous souhaitez ajouter le nom d'un organisme dans l'objet d'un email à l'aide du
dictionnaire, mais vous souhaitez éviter un objet trop long en limitant la taille à 14 caractères :
`{{trainingOrganism.name|truncate:14}}`

Voici la valeur calculée à partir d'un organisme appelé `SUPER FORMATIONS`

* `{{trainingOrganism.name}}` donnera le nom en entier `SUPER FORMATIONS`
* `{{trainingOrganism.name|truncate:14}}` donnera le nom tronqué de manière à obtenir 14 caractères en
  tout : `SUPER FORMA...`

Si en revanche le nom de l'organisme est inférieur ou égal à 14 caractères, alors il sera conservé tel quel.

#### Passer un texte en minuscules / majuscules

Il est possible de passer un texte en minuscules grâce à la fonction `lowercase` qui ne prend pas de paramètre :
`{{xxx.yyy|lowercase}}`

Il est possible de passer un texte en majuscules grâce à la fonction `uppercase` qui ne prend pas de paramètre :
`{{xxx.yyy|uppercase}}`

Par exemple, voici la valeur calculée à partir d'un organisme appelé `SUPER FORMATIONS`

* `{{trainingOrganism.name}}` donnera le nom tel quel (ici en majuscules) `SUPER FORMATIONS`
* `{{trainingOrganism.name|lowercase}}` donnera le nom en minuscules : `super formations`
