
## Mise en place d'une automatisation
<br>

- **Activer l'option "Activepieces" dans vos applications Wedof :** https://www.wedof.fr/mes-applications
  ![Activation](/assets/images/help-center/guides/applications/activepieces/activation2.png "Activation")
- **Créer un compte Activepieces** : www.activepieces.com
  ![Inscription](/assets/images/help-center/guides/applications/activepieces/inscription.png "Inscription")
- **Créer un nouveau Flow**
  ![Nouveau](/assets/images/help-center/guides/applications/activepieces/nouveau.png "Nouveau")
- **Sélectionner un déclencheur** : rechercher "Wedof" dans la barre de recherche, puis choisissez votre évènement déclencheur.
  ![Trigger](/assets/images/help-center/guides/applications/activepieces/trigger.png "Trigger")
- **Lier son compte Wedof** : copier son jeton API Wedof (https://www.wedof.fr/profil) sur Activepieces
  ![API](/assets/images/help-center/guides/applications/activepieces/api.png "Api")
  ![Liaison](/assets/images/help-center/guides/applications/activepieces/liaison.png "Liaison")
- **Tester la liaison** : Tester la liaison en déclenchant la tâche choisie, pour faire remonter les variables.
  ![Test](/assets/images/help-center/guides/applications/activepieces/test.png "Test")
- **Ajouter différentes actions** : Utiliser les variables de Wedof remontées dans Activepieces pour effectuer des tâches ciblées (Exemples : créer un nouveau dossier, le mettre à jour, le valider, le facturer...)
  ![Variables](/assets/images/help-center/guides/applications/activepieces/variables.png "Variables")
- **Activer votre configuration !** : Cliquer sur le bouton *"Publish"*

<br>
<br>

## Exemples d'automatisations
<br>
<br>

- ### Le cycle de vie d'un dossier
*Objectifs : Détecter un nouveau dossier, faire évoluer son statut, y ajouter une note, puis envoyer un email à l'apprenant en question.*
<br>
<br>

1. Sélectionner le trigger Wedof **"Nouveau dossier de formation"**, qui se déclenchera à chaque création d'un nouveau dossier dans votre espace Wedof, et démarrera le processus.


![Exemple Trigger](/assets/images/help-center/guides/applications/activepieces/exemple-trigger.png "Exemple Trigger")

2. Etablir la connexion API *Wedof* et tester la connexion (voir plus haut la rubrique *"Mise en place d'une automatisation"*), ce qui fera remonter toutes les variables de *Wedof*.

3. Choisir une action qui s'appliquera lors de la création d'un nouveau dossier. Dans notre cas : **"Valider le dossier de formation"**. Pour retrouver le dossier en question, ajouter la variable *Wedof* **"externalid"** dans l'encart *"N° du dossier de formation"*. Le nouveau dossier passera alors automatiquement à l'état validé.

![Exemple 1ere Action](/assets/images/help-center/guides/applications/activepieces/exemple-action1.png "Exemple 1ere Action")

4. Choisir une nouvelle action qui s'appliquera à la suite. Pour l'exemple : **"Mettre à jour un dossier de formation"**. Encore une fois, ajouter la variable *Wedof* **"externalid"** dans l'encart *"N° du dossier de formation"*. Puis modifier le contenu du dossier en ajoutant par exemple une note dans l'encart *"Notes"*.

![Exemple 2eme Action](/assets/images/help-center/guides/applications/activepieces/exemple-action2.png "Exemple 2eme Action")
![Exemple 2eme Action B](/assets/images/help-center/guides/applications/activepieces/exemple-action2B.png "Exemple 2eme Action B")

5. Dernière action : Sélectionner *"Gmail"* puis choisir l'action **"Send Email"**. Connecter votre compte Gmail, choisir le destinataire (sélectionner la variable **"attendee email"** pour cibler l'adresse email de l'apprenant en question), puis configurer le contenu du mail avec les variables de Wedof.

![Exemple 3eme Action](/assets/images/help-center/guides/applications/activepieces/exemple-action3.png "Exemple 3eme Action")
![Exemple 3eme Action B](/assets/images/help-center/guides/applications/activepieces/exemple-action3B.png "Exemple 3eme Action B")

6. Cliquer sur le bouton *"Publish"* pour activer le process.

**Résumé du process d'automatisation** : Ce process se déclenchera lorsqu'un nouveau dossier sera détecté sur Wedof. Il migrera alors ce dossier automatiquement à l'état *"validé"*, tout y ajoutant une note personnelle. Enfin, le process se chargera d'envoyer un email personnalisé à l'apprenant.
<br>
<br>
<br>

- ### La facturation d'un dossier
*Objectifs : Détecter un dossier à l'état final, créer une facture à partir des données du dossier, envoyer la facture au financeur.*
<br>
<br>

1. Sélectionner le trigger Wedof **"Dossier de formation à facturer"**, qui se déclenchera à chaque état de facturation d'un dossier dans votre espace Wedof, et démarrera le processus.

![Exemple Trigger](/assets/images/help-center/guides/applications/activepieces/exemple2-trigger.png "Exemple Trigger")

2. Etablir la connexion API *Wedof* et tester la connexion (voir plus haut la rubrique *"Mise en place d'une automatisation"*), ce qui fera remonter toutes les variables de *Wedof*.

3. Choisir une action qui s'appliquera lorsqu'un dossier arrive à l'état de facturation. Dans notre cas, nous choisirons d'utiliser *Stripe* comme logiciel de facturation externe. Tout d'abord, créer l'utilisateur sur *Stripe*.

![Exemple 1ere Action](/assets/images/help-center/guides/applications/activepieces/exemple2-action1.png "Exemple 1ere Action")

Etablir la connexion API *Stripe* et tester la connexion (voir plus haut la rubrique *"Mise en place d'une automatisation"*), ce qui fera remonter toutes les variables de *Stripe*.
Renseigner les données de l'utilisateur grâce aux variables de Wedof (**"attendee email"**, **"attendee firstName"**, **"attendee lastName"**, **"attendee id"**...).

![Exemple 1ere Action B](/assets/images/help-center/guides/applications/activepieces/exemple2-action1B.png "Exemple 1ere Action B")

4. Créer la facture à partir de l'utilisateur *Stripe* précédemment créé. Sélectionner **"Create Invoice"**, puis renseigner l'ID de l'utilisateur avec les variables *Stripe*.

![Exemple 2eme Action](/assets/images/help-center/guides/applications/activepieces/exemple2-action2.png "Exemple 2eme Action")

![Exemple 2eme Action](/assets/images/help-center/guides/applications/activepieces/exemple2-action2B.png "Exemple 2eme Action")

5. Facturer le dossier de formation avec Wedof en choisisant l'action **"Facturer le dossier de formation"**. Ajouter la variable *Wedof* **"externalid"** dans l'encart *"N° du dossier de formation"*. Ajouter la variable *Stripe* **"invoiceid"** dans l'encart *"N° de facture"*.

![Exemple 3eme Action](/assets/images/help-center/guides/applications/activepieces/exemple2-action3.png "Exemple 3eme Action")

6. Cliquer sur le bouton *"Publish"* pour activer le process.

**Résumé du process d'automatisation :** Ce process se déclenchera lorsqu'un dossier Wedof arrivera à l'état final de facturation. Il créera un nouvel utilisateur sur *Stripe* (logiciel de facturation) en copiant les données de l'apprenant. Puis, créera la facture avant de la transmettre à Wedof. Enfin, Wedof enverra la facture au financeur du dossier.
