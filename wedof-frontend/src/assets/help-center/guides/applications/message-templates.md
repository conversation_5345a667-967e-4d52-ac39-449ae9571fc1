## Laissez vous guider par ce tutoriel vidéo :
<br>
<br>

https://www.youtube.com/embed/Mkxb7ZSMH8c?si=bvLULTH076gnup_s
<br>
<br>

## Ou préférez une documentation détaillée : 
<br>

## Principe

L'application "Messages et notifications" permet de **programmer l'envoi automatique de messages et des notifications** dans Wedof (emails et SMS).

Une fois configurés, ces messages sont envoyés automatiquement quand il se passe quelque chose dans Wedof, par exemple lors du changement d'état d'un Dossier de formation ou d'un Dossier de certification.

Vous pouvez ainsi automatiser l'envoi de message à vos apprenants lors du parcours de formation et/ou de certification, mais également programmer des messages à destination du formateur, du certificateur, de partenaires...

## Création / Modification d'un message

Afin de créer un modèle message, il est nécessaire de :

- configurer ce qui déclenche l'envoi du message,
- rédiger le message en lui-même.

Attention, lors de la création, le message est par défaut **"Inactif"**. N'oubliez pas d'**activer** votre message afin qu'il soit envoyé.

### Envoi de SMS

Les messages de type **SMS** se configurent de la même façon qu'un email à la différence du destinataire qui sera un numéro de téléphone au lieu d'une adresse email.

Par contre, l'envoi d'un SMS n'est pas gratuit, aussi il vous sera facturé **0,06€ HT / SMS**. 

Un seul message peut nécessiter l'envoi de plusieurs SMS du fait de sa longueur. Wedof indique alors une estimation de ce nombre.

Afin de limiter la longueur du message, Wedof raccourcira automatiquement les liens que vous fournirez dans le contenu du SMS et fournira à la place un lien de la forme https://s.wedof.fr/XXXXXX

### Configuration de l'envoi du message

![Configuration](/assets/images/help-center/guides/applications/message-templates/configuration.png "Configuration")

- **Nom (obligatoire)** : choisissez un nom pour reconnaître le message facilement,
- **Type (obligatoire)** : choisissez s'il s'agit d'un email, ou d'un SMS
- **Autoriser l'envoi multiple** : permettre l'envoi de ce message plusieurs fois à une même entité.
- **Entité (obligatoire)** : choisissez _Dossier de formation_ ou _Dossier de certification_ ou _Proposition commerciale_,
- **Événements (obligatoire)** : choisissez l'événement qui déclenche l'envoi du message,
- **Liste des certifications** : limitez optionnellement l'envoi de messages aux dossiers liés à une des certifications choisies,
- **Tags** : limitez optionnellement l'envoi de messages aux dossiers possédant un des tags choisis (ne propose que des tags déjà utilisés sur vos dossiers),

#### Exemple de configuration de message sur un dossier de certification avec un délai de 10 minutes lors du changement d'état à l'état "Réussi" et associé au tag "mail"

![Exemple certification](/assets/images/help-center/guides/applications/message-templates/exemple-certification1.png "Exemple de configuration d'envoi de message pour les dossiers de certification")
![Exemple certification2](/assets/images/help-center/guides/applications/message-templates/exemple-certification2.png "Exemple d'options d'envoi de message pour les dossiers de certification")

#### Exemple de configuration de message sur un dossier de formation lors du changement d'état à l'état "Accepté" et associé à une certification

![Exemple formation](/assets/images/help-center/guides/applications/message-templates/exemple-formation.png "Exemple de configuration d'envoi de message pour les dossiers de formation")

### Choisir l'expéditeur (email)

Lors de la configuration d'un modèle de message (email uniquement), vous avez la possibilité de choisir l'adresse mail d'expédition pour l'envoi de messages.

![sentAsConfiguration](/assets/images/help-center/guides/applications/message-templates/sentAsConfiguration.png "Configuration")

La configuration de l'adresse d'expédition peut être activée depuis votre espace personnel Wedof depuis la [configuration de votre organisme](/profil/organisme) par le propriétaire du compte Wedof uniquement.
L'adresse mail proposée par Wedof est l'adresse mail utilisée pour se connecter à Wedof.

![sentAs](/assets/images/help-center/guides/applications/message-templates/sentAs.png "Configuration de l'adresse d'expédition")

Une fois configurée et si vous le souhaitez, vous pouvez choisir d'utiliser cette adresse mail comme expéditeur pour l'envoi de messages. 

### Rédaction d'un message (email)

![Redaction](/assets/images/help-center/guides/applications/message-templates/redaction.png "Redaction")

#### Champs (pour emails)

- **Répondre à (obligatoire)** : par défaut l'adresse email utilisé sera l'adresse de votre organisme,
- **Destinataire (obligatoire)** : adresse(s) email(s) destinataire(s),
- **Destinataire en cc** : adresse(s) email(s) en copie,
- **Destinataire en cci** : adresse(s) email(s) en copie cachée,
- **Sujet (obligatoire)** : sujet / objet de l'email,
- **Contenu (obligatoire)** : le corps du message.

**Attention**, une fois envoyés, les messages ne sont plus modifiables. En revanche, les messages programmés avec un délai sont modifiables jusqu'à l'expiration du délai précédant leur envoi. Le changement de délai n'est par contre pas rétroactif : les messages déjà programmés seront envoyé après le délai défini au moment de leur envoi.

Avant d'activer le message, assurez vous que tous les champs soient corrects et que la mise en forme vous convienne. Pour cela vous pouvez tester votre modèle de message autant de fois que possible en vous l'envoyant à vous-même.

#### Variables dynamiques

Vous pouvez rédiger l'email ou le SMS de façon **dynamique**. Le dictionnaire des variables est là pour **personnaliser** le message que vous souhaitez envoyer.

Vous pouvez utiliser les variables mises à disposition pour **personnaliser les adresses email, le numéro de téléphone, le sujet ou encore le contenu du message**.

Pour cela, cliquez sur une des valeurs présentes dans le dictionnaire des variables pour la copier puis collez-la dans le champ souhaité.

La liste des variables disponibles est affichée dans à côté du modèle. Vous pouvez également les retrouver dans le [Dictionnaire des variables](/assistance/guides/applications/dictionnaire).

#### Destinataires

Pour les 3 champs de destinataires, vous pouvez spécifier plusieurs destinataires en les séparant par une virgule.

Vous pouvez saisir des adresses email "en dur" ou utiliser les variables dynamiques, par exemple l'adresse email de l'apprenant.

En saisissant votre propre adresse email, l'envoi de message devient alors un envoi de notifications qui vous permettent d'être prévenu par email de changements dans Wedof.

#### Tester l'email ou le SMS

Une fois votre message créé, vous pourrez tester votre message en cliquant sur "Recevoir un message de test". Vous recevrez un email à l'adresse email de votre compte utilisateur Wedof, ou un SMS au numéro de téléphone de ce même compte.

Pour les variables dynamiques des données de test seront envoyées (potentiellement vides pour certaines).

#### Exemple de rédaction d'un message à destination d'un apprenant

![Exemple apprenant](/assets/images/help-center/guides/applications/message-templates/exemple-apprenant.png "Exemple de configuration d'envoi de message à destination d'un apprenant")

#### Exemple de rédaction d'une notification à destination de **mon** organisme

![Exemple notification](/assets/images/help-center/guides/applications/message-templates/exemple-notification.png "Exemple de notification de message")


### Options de l'envoi du message

- **Délai (obligatoire)** : permet d'envoyer le message après un certain délai suivant l'événement déclencheur (X minutes / jours / semaines / mois), par ex. 6 mois après la sortie de formation pour le questionnaire d'évaluation à froid,
- **Re-vérifier les conditions avant l'envoi** : limitez l'envoi aux dossiers respectant toujours les conditions au moment de l'envoi,
- **Indicateur Qualiopi**: indiquez les indicateurs Qualiopi associés aux messages afin de démontrer la conformité aux exigences qualité et réglementaires,

![Exemple options](/assets/images/help-center/guides/applications/message-templates/options.png "Exemple d'options de message")


### Envoi d'un message de manière individuelle

#### Configuration du modèle de message

Si vous souhaitez envoyer via Wedof un message à une population spécifique (exemple : organisme partenaire ou apprenant / candidat), vous devrez configurer votre message vie l'utilisation du système de tags.

Pour définir ce fonctionnement, il faudra **définir un déclencheur** d'envoi de message : soit un changement d'état soit toute modification de l'entité ("Tous les événements sur le dossier"),

Attention, si vous choisissez le déclencheur "Tous les événements sur le dossier" et afin d'éviter que le même message soit envoyé à chaque modification de l'entité, l'objectif est d'envoyer le message à une personne et une seule fois. 
Vous devez donc ne pas autoriser l'envoi multiple. 

Enfin, il faudra **ajouter un tag** pour que le message ne s'envoie uniquement quand ce tag sera positionné sur l'entité. 

#### Configuration de l'entité 

Le tag pour lequel vous souhaitez l'envoi du message devra être reporté sur l'entité.

### Exemple 

Dans cet exemple, le message sera envoyé pour le dossier comprenant le tag "relance" (si le dossier satisfait les autres critères sélectionnés, par exemple une certification donnée).

![Exemple envoi d'un message individuel](/assets/images/help-center/guides/applications/message-templates/exemple-message-individuel.png "Exemple d'envoi d'un message individuel")
