## Avant-propos

La fonctionnalité première de Wedof concernant EDOF est la gestion des dossiers de formation. Néanmoins, un pré-requis à leur existence est la publication d'une offre de formation sur Mon Compte Formation.

La publication du catalogue de formation se fait sur EDOF :
* Soit via des formulaires, élément par élément,
* Soit via l'import dans EDOF d'un fichier XML contenant l'ensemble du catalogue.

Ce catalogue est alors également visible dans Wedof, notamment sur les dossiers de formation, dans les outils de recherche avancée, dans les propositions commerciales et auprès des certificateurs.

Wedof propose maintenant de modifier et gérer votre catalogue sans avoir besoin d'aller dans EDOF. C'est l'objet de cette documentation.

En effet, depuis ["la configuration de votre organisme"](/profil/organisme), vous pouvez :
* Exporter votre catalogue vers un fichier XML : `EDOF -> Fichier XML`
* Importer votre catalogue dans EDOF : `Fichier XML -> EDOF`

## Export du catalogue (Bêta)

Cette fonctionnalité permet de créer un fichier XML à partir des données de votre catalogue EDOF.

C'est une fonctionnalité particulièrement innovante, car il n'existait jusqu'à présent aucun autre moyen d'obtenir ce fichier à partir d'un catalogue EDOF existant.

Le fichier obtenu contient l'ensemble votre offre de formation publique Mon Compte Formation au moment de l'export : 
* Formations,
* Actions de formation,
* Sessions.

Ce fichier peut servir à :
* Alimenter un traitement automatisé,
* Migrer votre catalogue d'un SIRET à un autre,
* Intégrer votre catalogue dans un outil tiers (LMS etc.),
* Être modifié pour être importé et ainsi mettre à jour votre catalogue dans EDOF / MCF.

Ce fichier est au format <a rel="noopener noreferrer" href="http://lheo.gouv.fr/">Lhéo</a> compatible EDOF.

Le traitement de récupération de votre catalogue peut durer jusqu'à plusieurs heures. Vous recevrez un email avec le fichier XML et un rapport au format CSV listant les données incluses / exclues de l'export.

Cette fonctionnalité est également disponible via [l'API](https://www.wedof.fr/api/doc).

## Import du catalogue

Le fichier doit respecter certaines règles définies par EDOF :
* Le fichier doit faire moins de 100 Mo,
* Le fichier doit être au format XML <a rel="noopener noreferrer" href="http://lheo.gouv.fr/">Lhéo</a> compatible EDOF,
* Le fichier doit être encodé en `ISO-8859-1`,
* Le nom du fichier doit être au format suivant `<SIRET>_<nomfichier>.xml` et <nomfichier> ne doit pas contenir de point.

Attention : suite au dépôt, l'ensemble de votre catalogue sera automatiquement mis à jour selon une logique "Annule et remplace" d'après le contenu du fichier XML.

Après le dépôt et le traitement du fichier par EDOF, vous pourrez télécharger et consulter le "Compte-rendu d'intégration" afin de vérifier le résultat de l'import.

Cette fonctionnalité est également disponible via [l'API](https://www.wedof.fr/api/doc). Un Webhook est également disponible pour écouter et réagir quand l'import est terminé.
