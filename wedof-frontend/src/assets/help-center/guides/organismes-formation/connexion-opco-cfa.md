## Avant-propos

Cette connexion permet la synchronisation entre Wedof et un OPCO de façon officielle et sécurisée dans le cadre des contrats d'apprentissage d'un CFA.

ATTENTION : ces fonctionnalités **sont réservées aux Organismes de formations qui sont CFA et ne couvrent que les contrats d'apprentissage.**

## Ce qu'implique la connexion

En établissant cette connexion, vous autorisez Wedof à :

- Lire les informations de votre organisme,
- Lire vos dossiers d'apprentissage.

Cependant, Wedof n'agira sur vos données que sur demande de votre part.

En aucun cas, Wedof ne réalisera d'action sur vos données de façon autonome.

De la même façon que l'habilitation à EDOF, cette connexion va permettre la remontée d'informations dans Wedof :

- Dans le cadre d'un partenariat auprès d'un certificateur client de Wedof, pour que les données remontent sous forme de dossiers de certification
- Dans le cadre de l'utilisation de Wedof en tant qu'organisme de formation, pour que les données remontent sous forme de dossiers de formation

Attention dans le second cas, la connexion va créer de nouveaux dossiers formation de type "opcoCfa". Si vous avez des automatisations (Webhooks, Zapier, Salesforce, validation via API etc.), **il faudra donc bien faire la distinction sur la provenance des dossiers entre ceux du CPF et ceux des OPCOs via le champ `type` des dossiers reçus.**

## Récupération de la clé API (CFA_KEY)

Chaque organisme de formation dispose d'une clé d'API pour chaque OPCO permettant de l'identifier auprès de l'OPCO afin de garantir son identité lors d'échanges de données.

Chaque OPCO dispose de sa propre façon de récupérer la clé API. Cette documentation détaille comment récupérer les clés API en fonction des OPCO traités par Wedof.

La clé API est commune aux organismes d'un même SIREN pour un OPCO donné. Si vous souhaitez connecter plusieurs comptes Wedof associés au même SIREN, vous devrez donc **renseigner la même clé sur chacun des comptes du même SIREN**.

Attention, cette clé a durée de validité prédéfinie et limitée. Elle sera valide au moins 6 mois à compter de sa première récupération. Cela signifie qu'**après son expiration, il faudra renouveler la procédure** de récupération et saisie dans Wedof.

La connexion n'est disponible que pour les OPCOs suivants, mais les autres arriveront bientôt.

En cas de problème pour récupérer la clé d'API d'un OPCO, vous pouvez également vous référer à la <a target="_blank" rel="noopener noreferrer" href="https://www.cfadock.fr/portail_developpeur/doc/API%20Convergence%20-%20Mode%20Operatoire%20Recuperation%20CFA%20Key%20-%20v6.4.pdf">documentation officielle</a>.

#### OPCO Atlas

1. Connectez vous au portail <a target="_blank" rel="noopener noreferrer" href="https://myatlas.opco-atlas.fr/">OPCO Atlas</a>
2. Cliquez sur le menu "Clé CFA" à gauche
3. Copiez la clé CFA depuis l'interface OPCO Atlas et collez-la dans l'interface Wedof (dans votre profil Wedof, bouton "Activer la synchronisation" pour l'OPCO concerné).

#### OPCO Mobilités

1. Connectez-vous au portail <a target="_blank" rel="noopener noreferrer" href="https://sso.opcomobilites.fr/?redirect=https://mgestion.opcomobilites.fr/login-check">OPCO Mobilités</a>
2. Cliquez sur le menu "Mes habilitations" à gauche
3. Cliquez sur le sous-menu "Clé d'authentification"
4. Choisissez votre organisme de formation puis cliquez sur "Générer ma clé d'authentification"
5. Copiez la clé d'authentification depuis l'interface OPCO Mobilités et collez-la dans l'interface Wedof (dans votre profil Wedof, bouton "Activer la synchronisation" pour l'OPCO concerné).

#### OPCO Afdas

1. Connectez-vous au portail <a target="_blank" rel="noopener noreferrer" href="https://afdas.my.site.com/Prestataire/login?startURL=%2FPrestataire%2Fs%2F%3Ft%3D1735291505215">OPCO Afdas</a>
2. Cliquez sur le menu "Mon Compte" en haut à gauche
3. Il y a ensuite deux cas de figure :
    * S'il y a déjà une valeur dans le champ "Identifiant API CFA", passez à l'étape suivante
    * Sinon, cliquez sur le bouton noir en haut à droite "Générer un identifiant API CFA"
4. Copiez la clé d'authentification depuis le champ "Identifiant API CFA" et collez-la dans l'interface Wedof (dans votre profil Wedof, bouton "Activer la synchronisation" pour l'OPCO concerné).

#### OPCO Opcommerce

1. Connectez-vous au portail <a target="_blank" rel="noopener noreferrer" href="https://partenaire.lopcommerce.com/">OPCO Opcommerce</a>
2. Cliquez sur le menu en haut à droite et sur "Accrochage API"
3. Cliquez sur le bouton "Générer une nouvelle clé d'API"
4. Copiez la clé d'API depuis le champ "Votre clé d'API" et collez-la dans l'interface Wedof (dans votre profil Wedof, bouton "Activer la synchronisation" pour l'OPCO concerné).

#### OPCO Ocapiat

1. Connectez-vous au portail <a target="_blank" rel="noopener noreferrer" href="https://partnet.ocapiat.fr">OPCO Ocapiat</a>
2. Cliquez sur l'onglet "Interfaçage OCAPIAT" pour accéder à la gestion de votre clé CFA
3.
    * Si la clé est déjà générée, vous pouvez la consulter en cliquant sur "Afficher ma clé CFA"
    * Si la clé n'est pas encore générée, sélectionnez une durée de vie en mois pour cette clé (la plus longue possible) et cliquez sur "Générer ma clé CFA"
4. Copiez la clé d'API et collez-la dans l'interface Wedof (dans votre profil Wedof, bouton "Activer la synchronisation" pour l'OPCO concerné).

#### OPCO EP

1. Connectez-vous au portail <a target="_blank" rel="noopener noreferrer" href="https://cfa.opcoep.fr/">OPCO EP</a>
2. Cliquez sur le menu "Mon CFA" puis le sous-menu "Sécurisation des imports de contrat d’apprentissage"
3.
    * Si le token est déjà généré et s'il est toujours valide, passez à l'étape suivante
    * Si le token n'est pas encore généré, cliquez sur le bouton "Generation du token" à droite
4. Copiez le token de sécurité et collez-le dans l'interface Wedof (dans votre profil Wedof, bouton "Activer la synchronisation" pour l'OPCO concerné).

#### OPCO Akto

Contactez-vous et nous ferons la demande pour vous, la procédure est rapide.

#### OPCO Uniformation

1. Connectez-vous au portail <a target="_blank" rel="noopener noreferrer" href="https://www.uniformation.fr/user/login">OPCO Uniformation</a>
2. Cliquez sur le bouton "Espace privé"
3. Cliquez sur le bouton "Mes dossiers"
4. Cliquez sur le menu "Développeur / API"
5.
    * Si la clé est déjà générée et si elle est toujours valide, passez à l'étape suivante
    * Si la clé n'est pas encore générée, cliquez sur le bouton "Générer une nouvelle clé"
6. Copiez la clé API et collez-le dans l'interface Wedof (dans votre profil Wedof, bouton "Activer la synchronisation" pour l'OPCO concerné).

#### OPCO 2i

1. Ouvrez <a target="_blank" rel="noopener noreferrer" href="https://forms.office.com/Pages/ResponsePage.aspx?id=wqaAWP-QDE6_eTZRRqu8qpT6DSZ-s0hIsIJC1plKrnNUQThGN05XT1hBTUlZODBYVjlNRlBBOUVIUS4u">le formulaire de demande de clé d'API</a>
2. Remplissez le formulaire en donnant :
    1. Le N° SIREN de votre CFA
    2. Le nom de votre CFA
    3. Le nom et le prénom de la personne destinataire de la clé d'activation dans votre CFA
    4. L'adresse mail de la personne destinataire de la clé d'activation dans votre CFA
    5. Le téléphone de la personne destinataire de la clé d'activation dans votre CFA
    6. Quel logiciel de gestion est utilisé par votre CFA : `Wedof`
3. La personne destinataire devrait recevoir un email de la part d'OPCO 2i avec un lien pour obtenir la clé CFA
4. Copiez la clé d'API ainsi obtenue et collez-la dans l'interface Wedof (dans votre profil Wedof, bouton "Activer la synchronisation" pour l'OPCO concerné).

#### OPCO Constructys

1. Ouvrez <a target="_blank" rel="noopener noreferrer" href="https://forms.office.com/pages/responsepage.aspx?id=LR1JWOhgok2QzE-nJGJbrmFBjDGPAzJJtHYrgxtMUaxUN0xEMzBQQVVKTUc5OTBKS05DWDQ4TEpXMS4u">le formulaire de demande de clé d'activation</a>
2. Remplissez le formulaire en donnant :
    1. Les données d'identification de votre organisme (SIRET, UAI...)
    2. Les coordonnées du contact technique dans votre CFA
    3. L'outil utilisé : Wedof
    4. Le périmètre de l'accrochage :
        * À partir du `01/01/2022`
        * Pour : `Consulter les dossiers` uniquement pour le moment
3. Le contact technique devrait recevoir un email pour finaliser l'accrochage
4. À l'issue de cette finalisation, le contact devrait recevoir la clé d'activation
5. Copiez la clé d'activation ainsi obtenue et collez-la dans l'interface Wedof (dans votre profil Wedof, bouton "Activer la synchronisation" pour l'OPCO concerné).

## Fonctionnalités à venir / à l'étude

La récupération des contrats d'apprentissage est la première étape dans la connexion avec les OPCOs.

Une fois que les 11 OPCOs seront supportés, nous étudierons l'ajout des fonctionnalités suivantes :

* Gestion avancée des contrats côté OF / CFA (webhooks, recherche avancée, affichage dédié...)
* Création de contrats depuis Wedof
* Suivi des factures / règlements
* Connexion OPCOs hors contrats d'apprentissage

Si l'une d'entre elles vous intéresse tout particulièrement, ou même une autre, contactez-nous !

