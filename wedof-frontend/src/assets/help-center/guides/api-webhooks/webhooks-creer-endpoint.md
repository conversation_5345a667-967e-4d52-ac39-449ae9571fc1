Lors de l'occurrence d'un événément qui fait l'objet d'un Webhook, Wedof vous envoie une requête HTTP.

Afin de recevoir un Webhook, il faut donc créer une URL (un _endpoint_) accessible sur internet qui sera prête à recevoir les requêtes HTTP correspondantes.

## Requête HTTP envoyée

Lors de l'occurrence d'un événement sur un objet Wedof qui fait l'objet d'un Webhook, Wedof envoie une requête HTTP avec la méthode `POST` à l'URL fournie.

Cette requête contient 4 informations :
* Dans l'en-tête (ou _header_) HTTP `X-Wedof-Event` : l'identifiant de l'événement qui a déclenché l'envoi, par ex. `certificationFolder.toRegister`, `registrationFolderBilling.toBill`, `registrationFolder.created`
* Dans l'en-tête HTTP `X-Wedof-Signature` : le code d'authentification de message, cf. section "Étape optionnelle : vérifier la signature",
* Dans l'en-tête HTTP `X-Wedof-Delivery` : l'identifiant de l'envoi (_delivery_),
* Dans le corps (ou _body_) : l'objet qui a fait l'objet de l'événement sérialisé en JSON. Pour connaître la structure de cet objet, référez-vous à la [documentation de l'API](/api/doc) qui définit les structures de données JSON des objets de Wedof.

## Étape 1. Choisir une approche

Pour définir le _endpoint_ qui recevra ces requêtes, il existe deux approches principales : 
* **Code personnalisé** : par ex. écrire un fichier PHP sur un serveur adapté, définir l'action d'un contrôleur dans un framework backend (Symfony, Node, Spring Boot, Django...)...
* **No code** : par ex. créer un Zap "Catch hook" sur [Zapier](https://zapier.com/), ou un noeud "Webhook" sur [n8n](https://n8n.io/).

Le code personnalisé offre plus de flexibilité, mais requiert un serveur HTTP et nécessite d'avoir des compétences en développement logiciel. 

L'approche "No code" peut être plus rapide et accessible avec moins de compétences techniques, en revanche c'est parfois un service payant et moins versatile que le code.

## Étape 2. Traiter la requête

### Considérations techniques

D'un point de vue technique, le traitement d'une requête HTTP de Webhook nécessite de :
* Lire les en-têtes HTTP et le contenu JSON du corps de la requête, actions possibles dans une très grande variété d'environnements compte tenu de la prédominance des protocoles HTTP et du format JSON.
* Assurer que le traitement ne soit pas trop long pour éviter un _timeout_.
* Renvoyer un code HTTP adapté, notamment de succès (200) si le traitement a réussi.

### Robustesse du traitement

En dehors des considérations techniques, c'est vous qui choisissez comment traiter l'événement et les données reçues. Cette flexibilité est la force des Webhooks !

Cependant, elle a également un coût : c'est à vous de vous assurer du traitement correct des données reçues.

Les Webhooks offre une communication réseau entre deux systèmes, et dans un tel contexte des anomalies sont possibles : le Webhook peut ne pas être envoyé, être envoyé deux fois, ne pas parvenir jusqu'à votre _endpoint_ à cause d'un problème réseau ou de certificat, ne pas être traité correctement à cause d'une erreur dans votre _endpoint_ ou d'une erreur dans un service tiers appelé par votre _endpoint_, des Webhooks peuvent arriver dans le désordre, etc..

Afin d'utiliser le Webhook en production, il est donc nécessaire d'assurer la robustesse de votre _endpoint_ face à ces anomalies.

Le traitement doit ainsi prendre en compte non seulement les cas nominaux, mais aussi prévoir les cas aux limites, des cas d'erreur, etc.. Nous vous conseillons donc d'avoir une trace de ces traitements, un système d'alerte en cas d'anomalie, de valider les données...

Attention, afin d'éviter de surcharger la plateforme en cas de Webhook dysfonctionnel, un Webhook sera *désactivé automatiquement après 10 erreurs d'envoi consécutives*. Vous recevrez alors un email pour vous en informer. Il faudra alors identifier et corriger le problème, puis réactiver le Webhook manuellement. 

## Étape 3. Récupérer l'URL et créer le Webhook dans Wedof

Une fois votre _endpoint_ créé et le comportement souhaité défini, vous pouvez aller dans Wedof et [ajouter le Webhook à votre compte](/assistance/guides/api-webhooks/webhooks-ajouter).

## Étape optionnelle : vérifier la signature

Si cette étape est optionnelle, elle est cependant fortement recommandée afin d'assurer l'authenticité et l'intégrité du message reçu.

Lors de l'ajout du Webhook dans Wedof, vous pouvez choisir un "Secret", c'est-à-dire une sorte de mot de passe qui ne sera connu que de Wedof et de votre _endpoint_ (veillez donc à ne pas le divulguer).

Ce _secret_ combiné avec le contenu du message au travers d'une fonction de hashage (SHA512) permet à Wedof de générer un _code d'authentification de message_ qui accompagnera le message. Plus précisément, ce code est envoyé dans l'en-tête HTTP `X-Wedof-Signature` de la requête.

Lors de la réception du Webhook, vous pouvez alors générer vous aussi le code à partir du corps de la requête et du _secret_. S'il est identique au code reçu dans la requête, alors le message vient bien de Wedof et n'a pas été altéré (par exemple par une attaque "Man-in-the-middle"), le message peut donc être traité.

L'algorithme de hashage utilisé est _SHA512_ et le résultat est converti au format hexadecimal. Voici un exemple de pseudo-code PHP qui génère le code d'authentification à partir du _secret_ et du corps (par défaut hash_hmac renvoie de l'hexadecimal) :
 
`$localCode = hash_hmac('sha512', $requestBody, $secret)` 

Et voici du pseudo code qui compare ce code généré localement avec celui reçu pour vérifier la validité du message avant de le traiter :

`if ($localCode === $receivedCode) { doAwesomeThings... }`
