## Endpoints d'API

Les _endpoints_ d'API font l'objet d'une [spécification]((/api/doc)) au format [OpenAPI 3.0](https://spec.openapis.org/oas/v3.0.0).

La documentation interactive correspondante est disponible [ici](/api/doc).

## Authentification

Tous les appels d'API nécessitent une authentification.

Pour vous reconnaître, un token dédié est disponible dans votre compte Wedof (menu "Mon compte", rubrique "Jetons d'API")

Ce token confègre un accès complet à vos données Wedof, il doit donc être tenu confidentiel.

Pour utiliser un token pour authentifier une requête HTTP, ajoutez l'en-tête `X-Api-Key` à la requête.

## Format de données

Les _endpoints_ de Wedof fournissent et consomment des données au format JSON, vous devez donc :
* définir l'en-tête HTTP `Accept` à `application/json` pour toutes vos requêtes.
* définir l'en-tête HTTP `Content-Type` à `application/json` pour les requêtes qui requièrent un corps (`body`) HTTP (par ex. POST ou PUT) et fournir dans le corps les données attendues au format JSON.

Vous trouverez plus de détails sur ce que vous pouvez envoyer et recevoir pour chaque _endpoint_ dans la [documentation](/api/doc) correspondante.

## REST et HATEOAS

L'API de Wedof respecte est une API REST, où chaque ressource (objet manipulé) est identifiée par une URL. C'est le verbe HTTP (`GET`, `POST`, `PUT`, `DELETE`) qui détermine l'action à effectuer.

De plus, l'API est "découvrable" grâce au HATEOAS : l'objet JSON retourné par Wedof comporte un attribut `_links` qui listes d'autres _endpoints_ connexes. Ils peuvent permettre d'intéragir différement avec la même ressource ou pointer vers des ressources liées. Par exemple, lors de la récupération d'un dossier de formation, Wedof peut fournir un lien pour l'accepter, un autre pour le refuser, et un lien vers l'action de formation, ces liens étant d'autres _endpoints_ qui peuvent à leur tour être appelés.

## URLs

Les endpoints d'API doivent être précédés de l'URL de Wedof, ainsi le endpoint `/api/registrationFolders` est accessible à l'URL complète :
`https://www.wedof.fr/api/registrationFolders`

Les parties variables des endpoints entourées d'accolades (par ex. `{externalId}`). Vous devez les remplacer par les valeurs réelles que vous manipulez.

## Exemples

Voici des exemples d'appels à l'API en utilisant l'outil cURL, mais ils peuvent bien sûr être transposés à d'autres outils.

Récupérer le dossier de formation d'identifiant EDOF 4321123456789, _endpoint_ `GET /api/registrationFolders/{externalId}`
> curl -X GET -H "Accept: application/json" -H "X-Api-Key: 3481997635b211ec8d3d0242" https://www.wedof/fr/api/registrationFolders/4321123456789'

L'objet JSON retourné par Wedof comporte dans l'attribut `_links` l'URL `https://www.wedof/fr/api/registrationFolders/4321123456789/validate`.

Le dossier peut ainsi être validé :
> curl -X POST -H "Accept: application/json" -H "X-Api-Key: 3481997635b211ec8d3d0242" -H "Content-Type: application/json" https://www.wedof/fr/api/registrationFolders/4321123456789/validate -d '{"weeklyDuration": 35, "indicativeDuration": 70}'

