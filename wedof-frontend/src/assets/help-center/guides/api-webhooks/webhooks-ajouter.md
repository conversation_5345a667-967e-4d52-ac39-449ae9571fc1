Une fois votre [endpoint créé](/assistance/guides/api-webhooks/webhooks-creer-endpoint), vous pouvez ajouter le Webhook correspondant à Wedof.

## Accéder à la création de Webhook

Pour cela, rendez-vous sur votre compte Wedof et ouvrez le menu "Mes applications" :
![Mes applications](/assets/images/help-center/guides/api-webhooks/webhooks-ajouter/mes-applications.png "Mes applications")

Activez ensuite l'Application "Webhooks" :
![Activer l'Application Webhooks](/assets/images/help-center/guides/api-webhooks/webhooks-ajouter/activer-app-webhooks.png "Activer l'Application Webhooks")

Cliquez sur "Nouveau" pour ouvrir le formulaire de création de Webhook :
![Nouveau Webhook](/assets/images/help-center/guides/api-webhooks/webhooks-ajouter/nouveau-webhook.png "Nouveau Webhook")

## Ajouter le Webhook

![Formulaire de création de Webhook](/assets/images/help-center/guides/api-webhooks/webhooks-ajouter/formulaire-creation-webhook.png "Formulaire de création de Webhook")

Voici les champs à remplir pour ajouter le Webhook :

* **URL** : (obligatoire) URL complète du _endpoint_.

* **Secret** : (optionnel) mot de passe connu de Wedof et du _endpoint_ qui sert à générer le code d'authentification de message.

* **Ignorer SSL** : (optionnel) ignorer les problèmes de certificat SSL (par ex. si votre certificat est auto-signé).

* **Événements** : (obligatoire) événements qui déclencheront l'appel du _endpoint_.

Il est également possible de créer un Webhook depuis [l'API](/api/doc#operations-tag-Webhook).

## Gestion du Webhook

Les Webhooks peuvent être gérés dans l'interface de Wedof depuis l'Application Webhooks ainsi que depuis [l'API](/api/doc#operations-tag-Webhook).

![Gestion des Webhooks](/assets/images/help-center/guides/api-webhooks/webhooks-ajouter/liste-webhooks.png "Gestion des Webhooks")

### Activation / désactivation

Depuis la liste des Webhooks, vous pouvez activer / désactiver chaque Webhook. Un Webhook désactivé n'envoie aucune requête au _endpoint_ quand l'événement associé survient.

Par défaut le Webhook est activé dès sa création.

Attention, pour éviter la surcharge des services, un Webhook peut être désactivé automatiquement par Wedof après un trop grand nombre d'erreurs (par ex. le _endpoint_ est injoignable ou renvoie un code d'erreur HTTP).

Vous pouvez désactiver un Webhook depuis le _endpoint_ en renvoyant le code HTTP 410.

### Modification et suppression

Vous pouvez accéder au formulaire de modification depuis la liste des Webhooks. Tous les attributs sont modifiables sauf l'URL, en cas de changement d'URL il faudra donc en créer un nouveau et désactiver l'ancien.

Un Webhook peut également être supprimé, mais attention c'est irreversible. Aussi, dans le doute vous pouvez juste le désactiver.

### Deliveries

Chaque envoi de données à votre _endpoint_ au travers d'un Webhook est tracé dans Wedof sous la forme d'un _delivery_.

La liste des _deliveries_ est disponibles dans le détail du Webhook.

Chaque _delivery_ possède un identifiant unique (envoyé au _endpoint_ dans un en-tête HTTP) et une date. 

Il est possible de renvoyer un _delivery_, par exemple dans le cas où il n'aurait pas été traité correctement.
