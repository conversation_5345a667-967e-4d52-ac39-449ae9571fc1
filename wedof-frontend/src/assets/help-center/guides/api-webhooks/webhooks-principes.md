## Réagissez automatiquement à des événements grâce aux Webhooks

Les Webhooks sont une manière de réagir automatiquement à des événements qui surviennent dans Wedof ou transitent par Wedof (par exemple venant de EDOF).

C'est notamment très utile pour réagir à des actions externes, par exemple :
* Réagir à l'**acceptation d'un dossier** CPF par un apprenant pour l'**inscrire automatiquement dans une plateforme LMS**,
* Réagir au **passage à "À enregistrer" d'un dossier de certification** par le partenaire pour l'**inscrire automatiquement à une session d'examen**,
* Réagir au **passage à "À facturer" d'un dossier CPF** pour **créer automatiquement la facture** correspondante adressée à la Caisse des Dépôts.

Au lieu de devoir aller vérifier régulièrement si une donnée a changé, que ce soit manuellement (ce qui prend du temps et peut être source d'erreur ou d'oubli) ou automatiquement (par exemple "polling" régulier via l'API), vous manifestez votre intérêt pour l'événement souhaité et le système de Webhooks vous informe immédiatement quand il survient !

Vous pouvez même créer des traitements en cascade en combinaison avec l'[API](/api/doc) : une action déclenche un webhook, qui appelle une action via l'API, qui déclenche un autre webhook... 

Au travers de ces fonctionnalités, Wedof vous permet d'améliorer la cohérence, la fiabilité et la "fraîcheur" des données au sein des multiples systèmes d'informations que vous utilisez pour gérer vos formations et vos certifications !

Notez que certaines Apps et intégrations (par ex. Slack, Zapier ou Salesforce) envoient les événéments pour vous, dans ce cas vous n'avez pas besoin des Webhooks !

## Webhooks disponibles

Wedof permet de créer des Webhooks sur les événéments liés aux objets suivants :
* **Certifications** : mises à jour, partenariats et organismes suspicieux.
* **Dossiers de certification** : changements d'état.
* **Dossiers de formation** :
    * Mises à jour et changements d'état,
    * Alertes sur problème,
    * Facturation.
* **Evaluations** : mise à jour des notes de satisfaction des apprenants dans EDOF.

## Utilisation

Pour utiliser les Webhooks il y a deux grandes étapes :

1. [Créez un "endpoint" de Webhook sur votre serveur](/assistance/guides/api-webhooks/webhooks-creer-endpoint) : une URL qui écoutera l'arrivée de l'événément. C'est la partie "compliquée" car c'est à vous de lire l'événement et de déclencher les bonnes actions en conséquence.
2. [Ajoutez le Webhook dans Wedof](/assistance/guides/api-webhooks/webhooks-ajouter) en choisissant l'événément sur lequel il devra être déclenché, et pointant sur votre endpoint. C'est la partie "facile", remplissez le formulaire et le Webhook est prêt !
