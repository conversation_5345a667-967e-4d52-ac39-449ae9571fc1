## Utilisation de l'API

[Comprendre l'utilisation de l'API Wedof](/api/doc/).

## Récupération d'un candidat

*Endpoint API* :`https://www.wedof.fr/api/attendees/find?query={params}`

*Méthode* : GET

*Réponse Status* : 200

*Paramètre* : "{params}" est soit le numéro de téléphone soit l'email d'un candidat.

*Exemple* de la requête avec un numéro de téléphone : `https://www.wedof.fr/api/attendees/find?query=0600000001`
*Exemple* de la requête avec un email : `https://www.wedof.fr/api/attendees/find?query=<EMAIL>`

*Résultat* : Un json avec les informations du candidat s'il y a un résultat

Si la récupération d'un candidat ne retourne aucun résultat, vous devez créer le candidat, dans le cas contraire pensez à récupérer l'id du candidat et passez directement à l'étape de récupération du certifInfo d'une certification.

Autant que possible, il est préférable d'éviter de créer des doublons.

## Création d'un candidat

*Endpoint API* : `https://www.wedof.fr/api/attendees`

*Méthode* : POST

*Réponse Status* : 201

*Champs* :

| Champs             |                Description                |  Type  |                                        Description et Valeurs possibles                                        |                Obligatoire                 |
|--------------------|:-----------------------------------------:|:------:|:--------------------------------------------------------------------------------------------------------------:|:------------------------------------------:|
| lastName           |        Nom de famille du candidat         | string |                                                                                                                |                    Oui                     |
| firstName          |            Prénom du candidat             | string |                                                                                                                |                    Oui                     |
| email              |             Email du candidat             | string |                                                                                                                |                    Oui                     |
| gender             |             Sexe du candidat              | string |                                               'male' ou 'female'                                               |                    Oui                     |
| phoneNumber        | Numéro de téléphone portable du candidat  | string |                                            Minimum de 10 caractères                                            | Oui si 'phoneFixed' n'est pas renseigné *  |
| phoneFixed         |   Numéro de téléphone fixe du candidat    | string |                                            Minimum de 10 caractères                                            | Oui si 'phoneNumber' n'est pas renseigné * |
| birthName          |       Nom de naissance du candidat        | string |                                                                                                                |                    Non                     |
| firstName2         |        Deuxième prénom du candidat        | string |                                                                                                                |                    Non                     |
| firstName3         |       Troisième prénom du candidat        | string |                                                                                                                |                    Non                     |
| dateOfBirth        |       Date de naissance du candidat       | string |                                            Date (ex: '18/04/1993')                                             |                    Non                     |
| nameCityOfBirth    |        Ville naissance du candidat        | string |                                         Pour un candidat né en France                                          |                   Non **                   |
| codeCityOfBirth    | Code de la ville de naissance du candidat | string | Pour un candidat né en France - Code INSEE composé de 5 chiffres (https://www.insee.fr/fr/information/6800675) |                   Non **                   |
| nameCountryOfBirth |       Pays de naissance du candidat       | string |                                        Pour un candidat né à l'étranger                                        |                   Non **                   |
| codeCountryOfBirth |    Code du pays naissance du candidat     | string |                       Pour un candidat né à l'étranger - Code COG composé de 3 chiffres                        |                   Non **                   |

* Un des deux numéros de téléphone est requis pour la création d'un candidat, soit le numéro de téléphone portable soit le numéro de téléphone fixe.

** La ville 'nameCityOfBirth' ainsi que le code 'codeCityOfBirth' ne doivent être remplies que pour un candidat né en France.
Pour un candidat né à l'étranger, c'est le pays de naissance 'nameCountryOfBirth' ainsi que le code 'codeCountryOfBirth' qui doivent être remplis.

*Exemple de body* :

```
{ 
    "lastName": "Doe",
    "firstName": "Jane",
    "email": "<EMAIL>",
    "gender" : "female",
    "phoneNumber": "0600000001",
    "firstName2": "Louise",
    "dateOfBirth" : "18/06/1985",
    "nameCityOfBirth" : "Toulouse",
    "codeCityOfBirth": "31555"
}
```

*Résultat* : un json avec les informations du candidat créé

Pensez à récupérer l'id du candidat et passez directement à l'étape de récupération du certifInfo d'une certification.

## Récupérer le certifInfo d'une certification

Si vous connaissez déjà le certifInfo de la certification sur laquelle vous souhaitez créer un dossier de certification, passez directement à l'étape de création d'un dossier de certification.

*Endpoint API* : `https://www.wedof.fr/api/certifications?{params}`

*Méthode* : POST

*Réponse Status* : 200

*Paramètre* :

- organismType
  Valeurs possibles : "all", "certifier", "partner"
  Permet de n'obtenir que les certifications pour lesquelles l'organisme est certificateur ou celles pour lesquelles l'organisme est partenaire ou toutes les certifications

- siret
  Permet de n'obtenir que les certifications appartenant à l'organisme de siret considéré - par défaut l'organisme de l'utilisateur courant

- query
  Permet d'effectuer une recherche libre sur les champs 'intitulé de la certification', 'certif info', 'rs code' et 'rncp code'

- enabled
  Valeurs possibles : "false", "true"
  Permet de filtrer les certifications actives / inactives

- type
  Valeurs possibles : "RS", "RNCP"
  Permet de filtrer les certifications par type (RS,RNCP)

- certificationPartnerState
  Valeurs possible : "processing", "active", "aborted", "refused", "revoked"
  Permet de filter sur le statut du partenaire lié à la certification.

*Exemple* de la requête : `https://www.wedof.fr/api/certifications?organismType?certifier&enabled=true&type=RS`

*Résultat* : un json avec les informations des certifications retournées en fonction des paramètres

## Création d'un dossier de certification

*Endpoint API* : `https://www.wedof.fr/api/certificationFolders`

*Méthode* : POST

*Réponse Status* : 201

*Champs* :

| Champs         |                 Description                 |   Type   |           Description et Valeurs possibles            | Obligatoire |
|----------------|:-------------------------------------------:|:--------:|:-----------------------------------------------------:|:-----------:|
| attendeeId     |               Id du candidat                | integer  |                                                       |     Oui     |
| certifInfo     |       CertifInfo de la certification        |  string  |                                                       |     Oui     |
| enrollmentDate |    Date d'inscription à la certification    | datetime |                                                       |     Non     |
| type           | Indique le type du dossier de certification |  string  | 'CERTIFIE', 'OF', 'POLE_EMPLOI', 'AUTRE', 'EMPLOYEUR' |     Non     |
| partner        |           Siret du partenaire ***           |  string  |                     14 caractères                     |     Non     |

*** Permet de spécifier l'organisme de formation partenaire associé au dossier de certification

*Exemple de body* :

```
{
    "attendeeId": 1,
    "certifInfo": "10000"
}
```
