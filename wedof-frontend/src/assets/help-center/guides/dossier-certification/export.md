## Export de dossiers de certification

Obtenez rapidement un fichier au format .csv avec vos dossiers de certificationf.
Rendez-vous dans l'onglet _Espace Certification_ puis dans l'onglet _Dossiers_ puis cliquez sur exporter.

![exportDossierCertification](/assets/images/help-center/guides/exportCsv/exportDossierCertification.png "export de dossiers")

Par dé<PERSON>ult, l'export du fichier est effectué en fonction des filtres et des query du tableau.

Ce fichier comprend les champs suivants :

- **CERTIFICATION** : Identification de la certification, 
- **OBTENTION** : Mode d'obtention de la certification : PAR_SCORING ou PAR_ADMISSION, 
- **ORGANISME** : Organisme du dossier,
- **ORGANISME_SIRET** : Siret de l'organisme du dossier,
- **NUMERO_DOSSIER** : Identifiant interne du dossier,
- **SEXE** : Sexe du candidat,
- **ESPACE_CANDIDAT** : Lien url de l'Espace Candidat dans Wedof,
- **CANDIDAT** : Nom de famille et prénom du candidat,
- **EMAIL** : Email du candidat,
- **TELEPHONE** : Téléphone du candidat,
- **STATUT** : État du dossier,
- **DATE_INSCRIPTION** : Date d'inscription du candidat à la certification,
- **EXAMINATION_TYPE** : Type d'examen du dossier,
- **EXAMINATION_PLACE** : Nom du lieu de l'examination,
- **EXAMINATION_CENTER_CODE_POSTAL** : Code postal du lieu de l'examination,
- **EXAMINATION_DATE** : Date de l'examination,
- **EXPIRATION_DATE** : Date d'expiration de la certification obtenue,
- **COMMENTAIRE** : Commentaire du dossier,
- **PREUVE_NUMERIQUE** : Url de la preuve numérique,
- **RESULTAT** : Résultat obtenu à l'exmane,
- **OPTION** : Option du dossier,
- **NIVEAU_LANGUE_EUROPEEN** : Niveau Langue Européen obtenu à l'exmane,
- **MENTION** : Mention obtenu à l'exmane,
- **VERBATIM** : Information complémentaire sur la certification,
- **MODALITE_ACCESS** : Modalités d'accès à la certification,
- **MODALITE_ACCESS_VAE** : Voie d'accès VAE,
- **TYPE** : L'initiative de l'inscription à la certification,
- **DATE_DE_NAISSANCE** : Date de naissance du candidat,
- **CODE_INSEE_VILLE_NAISSANCE** : Code INSEE de la ville de naissance du candidat,
- **VILLE_NAISSANCE** : Ville de naissance du candidat,
- **CODE_PAYS_NAISSANCE** : Code COG du pays de naissance du candidat,
- **PAYS_NAISSANCE** : Pays de naissance du candidat,
- **DOSSIER_COMPLET** : Dossier complet pour l'export la CDC,
- **STATUT_EXPORTABILITE** : Staut pour connaitre l'état d'export du dossier vers la CDC.

Si vous souhaitez utiliser l'api, l'endpoint à utiliser est */api/certificationFolders*



