# Offre

<details>
  <summary>Quelles fonctionnalités sont incluses dans les différentes offres de Wedof ?</summary>
<hr>
Wedof propose 5 offres à destination des organismes de formation ainsi que 2 offres pour les organismes certificateurs (c'est-à-dire les organismes ayant enregistré une certification auprès de France Compétences).

1. Les offres pour organismes de formation
* Offre Access - 49€ HT/mois

Cette offre inclut les fonctionnalités disponibles dans l'Offre Standard mais est limitée à 50 dossiers de formation / an.

* Offre API - 199€ HT/mois

Cette offre permet de gérer l'ensemble du cycle de vie des dossiers de formation au travers de [l'API](https://www.wedof.fr/api/doc).  
Elle inclut également le système d'événements par [webhooks](https://www.wedof.fr/assistance/guides/api-webhooks/webhooks-creer-endpoint) **(hors outils no-code)**.

* Offre Essentiel - 249€ HT/mois

Cette offre permet de gérer l'ensemble du cycle de vie des dossiers de formation au travers de l'interface graphique Wedof ainsi que de [l'API](https://www.wedof.fr/api/doc).  
Elle inclut également le système d'évènements par [webhooks](https://www.wedof.fr/assistance/guides/api-webhooks/webhooks-creer-endpoint) *(outils no-code non compris)* et vous permet d'associer 5 comptes utilisateurs sur un même organisme.

* Offre Standard - 399€ HT/mois

Pour la gestion des dossiers de formation, cette offre contient l'ensemble des fonctionnalités proposées par l'offre "Essentiel" auxquels s'ajoutent les applications comme Slack, SalesForce, Zapier et d'autres.  
De plus, vous pourrez suivre vos statistiques financières et avoir la possibilité d'associer autant de comptes utilistateurs que vous le souhaitez sur votre organisme.  

* Offre Premium - 699€ HT/mois

Cette offre inclut toutes les fonctionnalités de l'offre "Standard" ainsi que le système de propositions commerciales et inscriptions automatisées.  
Il s'agit d'un funnel commercial dans lequel le futur apprenant est accompagné dans la création de son dossier de formation avec validation automatique de son dossier.  
Ces propositions peuvent être paramétrées pour donner lieu à des changements tarifaires, de la durée de la formation ou des dates de formation, eux aussi automatiques.

2. Offre pour les organismes certificateurs  

Au travers de ces offres, Wedof facilite la relation entre les certificateurs et leurs partenaires.  
Lorsqu'un dossier de formation est accepté pour une certification dont il est propriétaire, un dossier de certification est créé entre le certificateur et son partenaire.  
Ce dossier de certification va permettre de suivre les différentes étapes de la certification d'un apprenant ainsi qu'un espace d'échange de documents entre le certificateur et son partenaire. [Plus d'informations...](https://www.wedof.fr/assistance/guides/certificateurs/cycle-vie-certification-apprenant)  

* Offre Standard - 9,90€ HT/dossier (limité à 300 dossiers par mois)

Cette offre permet de gérer l'ensemble du cycle de vie des dossiers de certification au travers de [l'API](https://www.wedof.fr/api/doc).  
Elle inclut également le système d'évènements par [webhooks](https://www.wedof.fr/assistance/guides/api-webhooks/webhooks-creer-endpoint) **(hors outils no-code)** ainsi que les applications comme Slack, SalesForce, Zapier et d'autres.

* Offre Premium - 8,90€ HT/dossier + 199€ HT/mois (limité à 500 dossiers par mois)

L'Offre Premium inclut les fonctionnalités disponibles dans l'Offre Standard ainsi que des fonctionnalités avancées comme l'accrochage automatique, la synchronisation France Compétences, un Espace Candidat, la génération automatique des parchemins de certification et bien d'autres encore. 

<hr>
</details>  
<br>

<details>
  <summary>Quels sont les workflows disponibles avec l’abonnement standard ou premium ? </summary>
<hr>

Avec à minima l'abonnement standard pour les OF, et le premium pour les OC, vous pourrez créer des webhooks à partir de vos outils *no-code* tels qu’**Activepieces**, **Make**, **n8n** ou encore **Zapier**. 

En outre, Wedof met à disposition ses connecteurs API et ses variables pour ces outils dits *"no-code"* : qui disposent d'une interface utilisateur intuitive.

L'objectif de ces workflows est de mettre en place des procédures d'automatisations, qui agissent directement sur votre espace Wedof et/ou avec d'autres logiciels externes.

<hr>
</details>  
<br>

<details>
  <summary>A quoi sert l’option Processus Métiers Automatisés ? </summary>
<hr>

Pour 49 € HT/mois supplémentaire, l'option Processus Métiers Automatisés vous aide à programmer via une interface utilisateur *No-code* et intuitive toutes vos automatisations pour vos processus métiers.

Et si besoin, notre équipe vous accompagnera dans la création de vos processus métiers (service supplémentaire).

Découvrez comment mettre en place une automatisation à partir de l'application [Processus Métiers Automatisés](/assistance/guides/applications/workflow).


<hr>
</details>  
<br>

<details>
  <summary>Quel est le délai de synchronisation avec EDOF de mes dossiers de formation ?</summary>
<hr>

Les actions effectuées sur vos dossiers depuis Wedof (API ou UI) sont répercutées immédiatement dans Wedof et EDOF.

Les actions effectuées dans EDOF sont répercutées dans Wedof (API, Webhooks et UI) après un court délai :

* Abonnements payants: en général moins de 1 minute pour recevoir un nouveau dossier "À traiter" ou moins de 2 minutes pour un dossier passé à "Accepté",
* en général moins de 5 minutes pour recevoir les autres mises à jour.

<hr>
</details>
<br>

<details>
  <summary>J'ai plusieurs organismes de formation, un abonnement Wedof suffit-il ?</summary>
<hr>

Aujourd'hui, Wedof ne permet de n'avoir qu'un seul compte/abonnement par SIRET. Il sera bientôt possible d'avoir plusieurs SIRET pour un seul abonnement.
En attendant si vous souhaitez intégrer un nouvel organisme, il vous faudra créer un nouveau compte Wedof et souscrire à un autre abonnement.  
Dans le cas où vous prendriez plusieurs abonnements et selon le nombre d'abonnements souscrits, vous pouvez être éligible à une réduction. Contactez [l'équipe Wedof](mailto:<EMAIL>) pour plus d'informations.

<hr>
</details>  
<br>

<details>
  <summary>Comment m'abonner ou modifier mon abonnement ?</summary>
<hr>

Pour gérer son abonnement il suffit de se rendre dans "Mon abonnement"
<div class="flex flex-row">
<img src="/assets/images/help-center/faqs/subscription.png">
<img src="/assets/images/help-center/faqs/subscription2.png">
</div>

Puis sélectionner son abonnement dans les offres proposées
![subscription](/assets/images/help-center/faqs/subscription3.png "Accéder aux abonnements")

Une fois abonné, vous pouvez modifier votre souscription en allant dans "Gérer mon abonnement"
![subscription](/assets/images/help-center/faqs/subscription4.png "Accéder aux abonnements")
![subscription](/assets/images/help-center/faqs/subscription5.png "Accéder aux abonnements")

Ou éventuellement l'annuler
![subscription](/assets/images/help-center/faqs/subscription6.png "Accéder aux abonnements")

<hr>
</details>  
<br>

<details>
  <summary>Puis-je arrêter mon abonnement ?</summary>
<hr>

Si votre abonnement est mensuel, il est sans engagement. Il est donc possible de le résilier à la fin du mois. En revanche, si votre abonnement est annuel il n'est possible de le résilier qu'à partir du 12ième mois.

<hr>
</details>  
<br>

<details>
  <summary>Existe-t-il des abonnements annuels ?</summary>
<hr>

Wedof propose des abonnements mensuels et également annuels pour les différentes offres présentes ci dessus. Si vous souhaitez opter pour un abonnement annuel plutôt qu'un abonnement mensuel merci de contacter notre équipe commerciale.
<hr>
</details>  
<br>

<details>
  <summary>Est ce que Wedof réalise du développement spécifique ?</summary>
<hr>

Notre équipe est en contact avec les différents organismes de formation client Wedof afin de comprendre et d'analyser au mieux les besoins dans le but d'y répondre. Si vous souhaitez cependant une fonctionnalité spécifique, merci de contacter notre équipe commerciale.
<hr>
</details>  
<br>


# Utilisateur

<details>
  <summary>Puis-je avoir plusieurs utilisateurs pour mon organisme de formation ?</summary>
<hr>

Oui, il est possible d'inviter d'autres utilisateurs pour un même organisme sur Wedof. Rendez-vous dans votre espace "Profil" puis dans l'espace "Organisme" et vous pourrez inviter d'autres utilisateurs en renseignant leur mail. 

<hr>
</details>  
<br>


# Dossiers de formation

<details>
  <summary>Je vois que je peux créer un dossier de formation sur Wedof. Sera-t-il créé sur EDOF ?</summary>
<hr>

Non, la fonctionnalité de création de dossier de formation ne créera pas de dossier dans EDOF.  
Cette option est proposée afin de faire entrer dans un workflow unique tous dossiers de formations quelle qu'en soit l'origine.  
Ainsi, les automatisations mises en place pour les dossiers financés par le CPF pourront s'appliquer aux dossiers créés sur Wedof.

<hr>
</details>  
<br>

# Dossiers de certification

<details>
  <summary>En tant que certificateur, puis-je consulter les dossiers d'un de mes partenaires qui a été déréférencé ?</summary>
<hr>

À partir du moment où un partenaire n'est plus référencé sur France Compétences il ne vous sera plus possible de consulter ses dossiers sur Wedof.

<hr>
</details>  
<br>

<details>
    <summary>Pourquoi est ce que le dossier de certification est à l'état "Prêt à passer", alors que mon candidat a déjà passé son examen ? </summary>
<hr>

Les dossiers de certifications peuvent être dans un état non conforme à la réalité ("prêt à passer" au lieu de "réussi") car le certificateur n'a pas encore effectué le déclaratif pour que Wedof les mettent dans le bon état.

Cette situation devrait se résoudre au fil des semaines et des rattrapages.

<hr>
</details>
<br>

# Apprenant / Candidat

<details>
    <summary>J'ai inséré de mauvaises données sur un apprenant / candidat, comment les corriger ?</summary>
<hr>

Il existe plusieurs moyens de mettre à jour les données d'un apprenant :
* modification par l'organisme de façon manuelle,
* modification par l'organisme avec une pièce d'identité
* modification par l'apprenant / candidat avec une carte d'identité via son espace apprenant / candidat
* modification par le candidat avec une carte d'identité via l'ajout au passeport de compétences : https://www.wedof.fr/assistance/guides/certificateurs/passeport-de-competences

Selon le cas de figure, certains de ces moyens peuvent ne pas être disponibles.

<hr>
</details>
<br>

<details>
    <summary>Dans quelles conditions les données d'un apprenant / candidat peuvent être modifiées ?</summary>
<hr>

De façon à optimiser la qualité des données, Wedof détermine de façon intelligente si les données déjà saisies sont valides :
* Si les données ont des chances d'être invalides, alors elles pourront être modifiées manuellement,
* Si les données ont peu de chances d'être invalides, alors il faudra une pièce d'identité,
* Si les données ont été validées (par ex. accrochage réussi), alors elles ne pourront pas être modifiées.

<hr>
</details>
<br>

# Propositions commerciales

<details>
  <summary>Les propositions commerciales ne sont pas activées, que faire ?</summary>
<hr>

Pour utiliser les propositions commerciales, il faut envoyer un courriel à [l'équipe support de Wedof](mailto:<EMAIL>) demandant l'activation des propositions accompagné du logo de votre organisme de formation au **format vectoriel .SVG**.

<hr>
</details>  
<br>

# Webhook

<details>
  <summary>J'ai l'abonnement API qui inclut les webhooks pourtant je ne peux pas en créer en allant dans "Mes applications".</summary>
<hr>

L'abonnement API ne permet de créer des webhooks que par des appels API. Pour créer des webhooks par "Mes applications"
il faut souscrire à l'offre "Essentiel".

Voici des ressources pour vous aider à créer les webhooks par l'API :

* [documentation de l'API](https://www.wedof.fr/api/doc) section "Webhook"
* [guide des webhooks](https://www.wedof.fr/assistance/guides/api-webhooks/webhooks-creer-endpoint)
<hr>
</details>  
<br>

# API

<details>
  <summary>Y-a-t-il une limite dans l'utilisation de l'API ?</summary>
<hr>

Non, il n'y a pas de limite en nombre de requêtes que vous effectuez sur l'API.  
Cependant, afin d'assurer un service performant pour tous, nous pourrons être amenés à limiter les organismes produisant des requêtes continues et non justifiées.  
L'utilisation de webhooks ou de connecteurs pour suivre les changements de vos dossiers est préférable à interroger l'API à intervalles très rapprochés. [Plus d'informations à ce sujet ici.](https://www.wedof.fr/assistance/guides/api-webhooks/webhooks-principes)

<hr>
</details>
<br>

<details>
  <summary>Je n'arrive pas à valider un dossier de formation par le endpoint <strong>/api/registrationFolders/validate</strong>.</summary>
<hr>

La validation d'un dossier ne peut se faire que dans certaines conditions prévues par EDOF.

* La date de début de session du dossier :  
  Un dossier ne peut être validé a moins de 11 jours ouvrés du début de la formation.  
  Vous pouvez retrouver les dates minimales d'entrée en formation en appelant le endpoint GET : **
  /api/registrationFolders/utils/sessionMinDates**


* Dossiers ayant une aide au financement par Pôle Emploi.  
  Ces dossiers nécessitent que la durée de la formation soit renseignée.


* Augmentation du tarif de la formation  
  En cas d'augmentation du tarif d'un dossier, le tarif ne peut pas excéder 15% du tarif de base de la formation.

Dans tous les cas, vous pouvez modifier le dossier via le endpoint PUT : **/api/registrationFolders/{externalId}**  
Voir la [documentation API](www.wedof.fr/api/doc) pour plus d'infos.
<hr>
</details>
<br>

<details>
  <summary>Lors de la modification d'un dossier de formation par le endpoint <strong>/api/registrationFolders PUT</strong>, faut-il que je renvoie tout le body ?</summary>
<hr>

Non, il n'est pas nécessaire de renvoyer l'ensemble du body pour modifier un dossier.  
Seuls les champs qui présents dans le body mettront à jour les propriétés du dossier.  
Ainsi, si vous souhaitez modifier uniquement la date de début de session du dossier, le body suivant est suffisant : **{ "trainingActionInfo" : { "sessionStartDate": "2019-08-24" } }**  
Les autres propriétés du dossier resteront inchangées.

<hr>
</details>
<br>

<details>
    <summary>Je n'arrive pas à modifier un dossier de formation par le endpoint <strong>/api/registrationFolders PUT</strong>.</summary>
<hr>

Voici quelques astuces qui peuvent vous aider :

* Il n'est pas nécessaire de renvoyer l'ensemble du body. Si vous souhaitez modifier les dates de session uniquement,
  vous pouvez ne renvoyer que les dates.
* Faites attention à la syntaxe du body : par exemple **sessionStartDate** et **sessionEndDate** sont des éléments du
  tableau **trainingActionInfo**.
* Faites attention au format des données dans le body (entier, string et date) : par exemple **sessionStartDate** et **
  sessionEndDate** sont attendus au format **AAAA-MM-JJ** (Année-Mois-Jour).
<hr>
</details>
<br>

<details>
    <summary>Pourquoi est ce que je n'arrive plus à gérer mes dossiers de formation ? </summary>
<hr>

Ceci signifie que votre organisme est limité à un nombre précis de dossiers de formation. Dans le cadre d'un abonnement Access, la limite des dossiers de formation est de 50 dossiers / an, passé ce nombre il ne vous sera pas possible d'agir sur vos dossiers. 

Pour reprendre l'utilisation de Wedof, il faut souscrire à une de nos offres payantes sans limite de dossiers de formation en allant dans "Mon abonnement".

![subscription](/assets/images/help-center/faqs/subscription.png "Accéder aux abonnements")

<hr>
</details>
<br>
