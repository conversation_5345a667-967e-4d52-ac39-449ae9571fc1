import {CUSTOM_ELEMENTS_SCHEMA, NgModule} from '@angular/core';
import {AttendeeRegistrationFolderComponent} from './registration-folder/attendee-registration-folder.component';
import {AttendeeRoutingModule} from './attendee-routing.module';
import {FileModule} from '../shared/file/file.module';
import {SharedModule} from '../shared/shared.module';
import {MatCardModule} from '@angular/material/card';
import {TranslateModule} from '@ngx-translate/core';

@NgModule({
    schemas: [CUSTOM_ELEMENTS_SCHEMA],
    declarations: [
        AttendeeRegistrationFolderComponent,
    ],
    imports: [
        AttendeeRoutingModule,
        FileModule,
        SharedModule,
        MatCardModule,
        TranslateModule
    ]
})
export class AttendeeModule {
}
