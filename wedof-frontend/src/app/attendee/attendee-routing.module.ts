import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {AttendeeRegistrationFolderComponent} from './registration-folder/attendee-registration-folder.component';
import {AttendeeRegistrationFolderDataResolver} from '../core/attendee/resolvers/attendee-registration-folder-data.resolver';

const routes: Routes = [{
    path: '',
    children: [
        {
            path: 'formation/dossier/:code',
            resolve: {
                data: AttendeeRegistrationFolderDataResolver,
            },
            component: AttendeeRegistrationFolderComponent
        },
        {
            path: 'formation/dossier/:code/files/:typeFileId',
            resolve: {
                data: AttendeeRegistrationFolderDataResolver,
            },
            component: AttendeeRegistrationFolderComponent
        },
        {
            path: 'formation/dossier/:code/enquete',
            resolve: {
                data: AttendeeRegistrationFolderDataResolver,
            },
            component: AttendeeRegistrationFolderComponent
        }
    ]
}];

@NgModule({
    imports: [
        RouterModule.forChild(routes)
    ],
    exports: [RouterModule]
})
export class AttendeeRoutingModule {
}
