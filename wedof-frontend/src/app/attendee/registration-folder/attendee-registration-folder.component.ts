import {Component, OnInit, ViewChild} from '@angular/core';
import {ActivatedRoute, Router} from '@angular/router';
import {RegistrationFolder, RegistrationFolderStates} from '../../shared/api/models/registration-folder';
import {Select} from '@ngxs/store';
import {AttendeeState} from '../../shared/api/state/attendee.state';
import {Observable} from 'rxjs';
import {Attendee} from '../../shared/api/models/attendee';
import {FileListComponent} from '../../shared/file/file-list/file-list.component';
import {Organism} from '../../shared/api/models/organism';
import {AuthService} from '../../core/auth/auth.service';
import {CertificationFolder} from '../../shared/api/models/certification-folder';
import {CertificationFolderService} from '../../shared/api/services/certification-folder.service';
import {RegistrationFolderService} from '../../shared/api/services/registration-folder.service';
import {FileType} from '../../shared/api/models/file';

@Component({
    selector: 'app-attendee-registration-folder',
    templateUrl: './attendee-registration-folder.component.html',
    styleUrls: ['./attendee-registration-folder.component.scss']
})
export class AttendeeRegistrationFolderComponent implements OnInit {

    @Select(AttendeeState.attendee) attendee$: Observable<Attendee>;
    @ViewChild('appFileListAttendee') appFileListAttendee: FileListComponent<RegistrationFolder, RegistrationFolderStates>;

    attendee: Attendee;
    registrationFolder: RegistrationFolder;
    organism: Organism;
    typeFileId: number;
    attendeeLink: string;
    orderedRegistrationFolderStates: RegistrationFolderStates[] = Object.values(RegistrationFolderStates);
    certificationFolder: CertificationFolder;
    showSurvey = false;
    fileTypes: FileType<RegistrationFolderStates>[];

    constructor(
        private _activatedRoute: ActivatedRoute,
        private _authService: AuthService,
        private _router: Router,
        private _certificationFolderService: CertificationFolderService,
        private _registrationFolderService: RegistrationFolderService,
    ) {
    }

    ngOnInit(): void {
        this.attendee$.subscribe(attendee => this.attendee = attendee);
        this.registrationFolder = this._activatedRoute.snapshot.data?.data[0];
        this.organism = this._activatedRoute.snapshot.data?.data[1];
        this.typeFileId = this._activatedRoute.snapshot.data?.data[2];
        this.attendeeLink = this._activatedRoute.snapshot.data?.data[3];
        this.showSurvey = this._activatedRoute.snapshot.data?.data[4];
        if (this.registrationFolder._links?.certificationFolder?.href) {
            this._certificationFolderService.getByUrl(this.registrationFolder._links.certificationFolder.href).subscribe((certificationFolder) => {
                this.certificationFolder = certificationFolder;
            });
        }
        this._registrationFolderService.listAllFiles(this.registrationFolder.externalId).subscribe((filesOrFileTypes) => {
            const allowedFileTypeIds = filesOrFileTypes.map(fileOrFileType => fileOrFileType.typeId);
            this.fileTypes = this.organism.registrationFolderFileTypes.filter(fileType => allowedFileTypeIds.includes(fileType.id));
        });
    }

    signOut(): void {
        this._authService.signOut();
        window.location.reload(); // will trigger auth then redirect to connection page
    }

    switchToCandidateSpace(): void {
        this._router.navigateByUrl(this.attendeeLink);
    }
}
