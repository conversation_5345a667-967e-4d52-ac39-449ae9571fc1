<div class="mx-auto my-auto fullwidth-basic-normal-scroll">
    <div class="flex flex-row justify-between text-sm pb-2 pt-5">
        <div>
            <b>{{ 'private.attendee.spaces.registrationFolderLink' | translate }}</b><span
            *ngIf="attendeeLink"> | </span>
            <button *ngIf="attendeeLink" class="underline" type="button" (click)="switchToCandidateSpace()">
                {{ 'private.attendee.spaces.certificationFolderLink' | translate }}
            </button>
        </div>
        <div class="text-right">
            <button class="underline" type="button" (click)="signOut()">{{ 'common.actions.signOut' | translate }}
            </button>
        </div>
    </div>
    <div class="content flex flex-wrap">
        <div class="p-2 w-full gt-md:w-1/2 ">
            <app-attendee-card-attendee class="cardAttendee m-auto p-4 pb-0"
                                        [entityContext]="{class : 'RegistrationFolder', id: registrationFolder.externalId}"
                                        [attendee]="attendee"></app-attendee-card-attendee>
            <app-registration-folder-card-attendee class="cardAttendee m-auto p-4 pb-0"
                                                   [registrationFolder]="registrationFolder"></app-registration-folder-card-attendee>
            <app-file-list-attendee class="cardAttendee m-auto p-4 pb-0"
                                    #appFileListAttendee
                                    *ngIf="fileTypes"
                                    [entity]="registrationFolder"
                                    [fileTypes]="fileTypes"
                                    [typeFileId]="typeFileId"
                                    [entityParentId]="organism.siret"
                                    entityApiPath="attendees/registrationFolders"
                                    entityIdProperty="externalId"
                                    [orderedStates]="orderedRegistrationFolderStates">
            </app-file-list-attendee>
        </div>
        <div class="p-2 w-full gt-md:w-1/2">
            <div class="flex justify-center mt-4 mb-2">
                <wedof-passport-button id="{{registrationFolder._links.certificationFolder.externalId}}"
                                       *ngIf="registrationFolder._links.certificationFolder"></wedof-passport-button>
            </div>
            <app-certification-folder-survey-card *ngIf="certificationFolder"
                                                  class="cardAttendee m-auto max-h-100 p-4 pb-0"
                                                  [isCandidate]="true"
                                                  [candidateId]="attendee.id"
                                                  [certificationFolder]="certificationFolder"
                                                  [showSurvey]="showSurvey">
            </app-certification-folder-survey-card>
        </div>
    </div>
</div>
