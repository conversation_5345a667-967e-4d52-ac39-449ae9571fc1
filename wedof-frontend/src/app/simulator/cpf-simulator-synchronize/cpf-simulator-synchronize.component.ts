import { Component, OnInit } from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Router} from '@angular/router';

@Component({
  selector: 'app-synchronize-registration-folders',
  templateUrl: './cpf-simulator-synchronize.component.html',
  styleUrls: ['./cpf-simulator-synchronize.component.scss']
})
export class CpfSimulatorSynchronizeComponent implements OnInit {

    constructor(private http: HttpClient, private router: Router) {
    }

  ngOnInit(): void {
      this.synchroniseFolders().then(r => console.log('synchronisation complete'));

  }
    async synchroniseFolders(): Promise<void> {
        try {
            await this.http.post('/app/cpfSimulator/synchronizeFolders', {}).toPromise();
            console.log('syncing folders successfully');
            await this.router.navigate(['/formation/dossiers/kanban/']);
        } catch (error) {
            console.error('Error during syncing folders:', error);
        }
    }
}
