<!-- app.component.html -->

<div class="min-w-full bg-white content-layout fullwidth-standard-inner-scroll">
    <mat-card class="card">
        <div class="min-w-full px-5 my-5">
            <mat-card>
                <mat-card-title class="mb-5">Simulateur CPF</mat-card-title>
                <mat-card-content class="mb-5">
                    <a [routerLink]="['cpf-simulator-initialize']" color="primary" mat-flat-button>
                        Initialiser / Réinitialiser le jeu de données
                    </a>
                </mat-card-content>
                <mat-card-content>
                    <h5>Actions apprenant</h5>
                    <form [formGroup]="actionAttendeeForm"
                          (ngSubmit)="acceptFolderByAttendee(externalIdAttendee)">
                        <mat-progress-spinner *ngIf="loadingAttendee; else submitAttendeeLabel"
                                              [diameter]="48" mode="indeterminate">
                        </mat-progress-spinner>
                        <ng-template #submitAttendeeLabel>
                            <div class="flex space-x-2">
                                <mat-form-field>
                                    <input type="text"
                                           placeholder="Saisir un id, prénom, nom"
                                           aria-label="select"
                                           matInput
                                           formControlName="registrationFolderExternalId"
                                           [matAutocomplete]="autocompleteAttendee">
                                    <mat-autocomplete #autocompleteAttendee="matAutocomplete">
                                        <mat-option *ngFor="let registrationFolderLite of filteredOptionsAttendee | async"
                                                    [value]="registrationFolderLite.externalId">
                                            {{ registrationFolderLite.name }} - CPF-{{ registrationFolderLite.externalId}}
                                        </mat-option>
                                    </mat-autocomplete>
                                </mat-form-field>
                                <div [ngSwitch]="getStateFromExternalId(externalIdAttendee)" class="mat-form-field flex items-center space-x-2">
                                    <button *ngSwitchCase="registrationFolderStates.VALIDATED"
                                            type="submit"
                                            mat-flat-button color="primary">
                                        Accepter le dossier
                                    </button>
                                    <button *ngSwitchDefault
                                            type="button"
                                            mat-flat-button color="primary"
                                            disabled>
                                        Aucune action
                                    </button>
                                </div>
                            </div>
                            <div *ngIf="errorMessageAttendee.length > 0"
                                 class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded" role="alert">
                                <strong class="font-bold">Erreur</strong>
                                <span class="block sm:inline">{{ errorMessageAttendee }}</span>
                            </div>
                        </ng-template>
                    </form>
                </mat-card-content>
                <mat-card-content>
                    <h5>Actions CDC</h5>
                    <form [formGroup]="actionCdcForm"
                          (ngSubmit)="validateServiceDoneByCdc(externalIdCdc)">
                        <mat-progress-spinner *ngIf="loadingCdc; else submitCdcLabel"
                                              [diameter]="48" mode="indeterminate">
                        </mat-progress-spinner>
                        <ng-template #submitCdcLabel>
                            <div class="flex space-x-2">
                                <mat-form-field>
                                    <input type="text"
                                           placeholder="Saisir un id, prénom, nom"
                                           aria-label="select"
                                           matInput
                                           formControlName="registrationFolderExternalId"
                                           [matAutocomplete]="autocompleteCdc">
                                    <mat-autocomplete #autocompleteCdc="matAutocomplete">
                                        <mat-option *ngFor="let registrationFolderLite of filteredOptionsCdc | async"
                                                    [value]="registrationFolderLite.externalId">
                                            {{ registrationFolderLite.name }} - CPF-{{ registrationFolderLite.externalId }}
                                        </mat-option>
                                    </mat-autocomplete>
                                </mat-form-field>
                                <div [ngSwitch]="getStateFromExternalId(externalIdCdc)"
                                     class="mat-form-field flex items-center space-x-2">
                                    <button *ngSwitchCase="registrationFolderStates.SERVICE_DONE_DECLARED"
                                            type="submit"
                                            mat-flat-button color="primary">
                                        Valider le service fait
                                    </button>
                                    <button *ngSwitchDefault
                                            type="button"
                                            mat-flat-button color="primary"
                                            disabled>
                                        Aucune action
                                    </button>
                                </div>
                            </div>
                            <div *ngIf="errorMessageCdc.length > 0"
                                 class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded" role="alert">
                                <strong class="font-bold">Erreur</strong>
                                <span class="block sm:inline">{{ errorMessageCdc }}</span>
                            </div>
                        </ng-template>
                    </form>
                </mat-card-content>
            </mat-card>
        </div>
    </mat-card>
</div>
