import {Component, OnD<PERSON>roy, OnInit} from '@angular/core';
import {Observable, Subject} from 'rxjs';
import {FormBuilder, FormGroup} from '@angular/forms';
import {map, startWith, takeUntil} from 'rxjs/operators';
import {HttpClient} from '@angular/common/http';
import {Router} from '@angular/router';
import {RegistrationFolder, RegistrationFolderStates} from '../../shared/api/models/registration-folder';

interface RegistrationFolderLite {
    externalId: string;
    name: string;
}

@Component({
    selector: 'app-test-ui',
    templateUrl: './cpf-simulator-ui.component.html',
    styleUrls: ['./cpf-simulator-ui.component.scss']
})


export class CpfSimulatorUiComponent implements OnInit, OnDestroy {

    actionAttendeeForm: FormGroup;
    actionCdcForm: FormGroup;
    filteredOptionsAttendee: Observable<RegistrationFolderLite[]>;
    filteredOptionsCdc: Observable<RegistrationFolderLite[]>;
    externalIdAttendee: string;
    externalIdCdc: string;
    loadingAttendee = false;
    loadingCdc = false;
    errorMessageAttendee = '';
    errorMessageCdc = '';

    registrationFoldersLite: RegistrationFolderLite[] = [];
    registrationFolders: RegistrationFolder[] = [];
    registrationFolderStates = RegistrationFolderStates;

    private _unsubscribeAll: Subject<void> = new Subject<void>();

    constructor(private http: HttpClient, private router: Router) {
    }

    async acceptFolderByAttendee(externalId: string): Promise<void> {
        this.loadingAttendee = true;
        this.errorMessageAttendee = '';
        const requestBody = {id: externalId};
        try {
            await this.http.post('/app/cpfSimulator/acceptFolderByAttendee', requestBody).toPromise();
            await this.http.post('/app/cpfSimulator/synchronizeFolders', {}).toPromise();
            await this.router.navigate(['/formation/dossiers/kanban/']);
        } catch (error) {
            this.errorMessageAttendee = error?.error?.detail;
        }
        this.loadingAttendee = false;
    }

    async validateServiceDoneByCdc(externalId: string): Promise<void> {
        this.loadingCdc = true;
        this.errorMessageCdc = '';
        const requestBody = {id: externalId};
        try {
            await this.http.post('/app/cpfSimulator/validateServiceDoneByCdc', requestBody).toPromise();
            await this.http.post('/app/cpfSimulator/synchronizeFolders', {}).toPromise();
            await this.router.navigate(['/formation/dossiers/kanban/']);
        } catch (error) {
            this.errorMessageCdc = error?.error?.detail;
        }
        this.loadingCdc = false;
    }

    getFolders(): Observable<RegistrationFolder[]> {
        return this.http.get<RegistrationFolder[]>('/api/registrationFolders?siret=53222292400039&type=cpf');
    }

    ngOnInit(): void {
        this.initForm();
        this.getFolders().subscribe(registrationFolders => {
            this.registrationFoldersLite = registrationFolders.map(registrationFolder => {
                return {
                    'externalId': registrationFolder.externalId,
                    'name': registrationFolder.attendee.lastName + ' ' + registrationFolder.attendee.firstName
                };
            });
            this.registrationFolders = registrationFolders;
            this.filteredOptionsAttendee = this.actionAttendeeForm.get('registrationFolderExternalId').valueChanges.pipe(
                takeUntil(this._unsubscribeAll),
                startWith(''),
                map(value => this._filterRegistrationFolder(value)),
            );
            this.filteredOptionsCdc = this.actionCdcForm.get('registrationFolderExternalId').valueChanges.pipe(
                takeUntil(this._unsubscribeAll),
                startWith(''),
                map(value => this._filterRegistrationFolder(value)),
            );
            this.actionAttendeeForm.get('registrationFolderExternalId').valueChanges.subscribe(value => {
                this.externalIdAttendee = value;
            });
            this.actionCdcForm.get('registrationFolderExternalId').valueChanges.subscribe(value => {
                this.externalIdCdc = value;
            });
        });
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    initForm(): void {
        this.actionAttendeeForm = new FormBuilder().group({
            registrationFolderExternalId: ['']
        });
        this.actionCdcForm = new FormBuilder().group({
            registrationFolderExternalId: ['']
        });
    }

    private _filterRegistrationFolder(value: string): RegistrationFolderLite[] {
        const filterValue = value ? value.toLowerCase() : '';
        return this.registrationFoldersLite.filter(option => option.externalId.toLowerCase().includes(filterValue) || option.name.toLowerCase().includes(filterValue));
    }

    getStateFromExternalId(externalId: string): RegistrationFolderStates {
        const registrationFolder = this.registrationFolders.find(currentRegistrationFolder => currentRegistrationFolder.externalId === externalId);
        return registrationFolder?.state;
    }
}
