.content-layout {
    align-items: center;
    justify-content: center;

    .title {
        margin-top: 40px !important;
    }
}

.loading-animation {
    width: 40px;
    height: 40px;
    border: 4px solid #007bff;
    border-top: 4px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
