import {Component, OnInit} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Router} from '@angular/router';

@Component({
    selector: 'app-cpf-simulator-initializer',
    templateUrl: './cpf-simulator-initializer.component.html',
    styleUrls: ['./cpf-simulator-initializer.component.scss']
})
export class CpfSimulatorInitializerComponent implements OnInit {

    constructor(private http: HttpClient, private router: Router) {
    }

    ngOnInit(): void {
        this.initialize().then(r => console.log('Initialization complete'));
    }

    async eraseDatabase(): Promise<void> {
        try {
            await this.http.post('/app/cpfSimulator/eraseDatabase', {}).toPromise();
            console.log('Deleting data rows successfully');
        } catch (error) {
            console.error('Error during Deleting data rows:', error);
        }
    }

    async resetAutomatorData(): Promise<void> {
        try {
            await this.http.post('/app/cpfSimulator/resetAutomatorData', {}).toPromise();
            console.log('reset N8N Data successfully');
        } catch (error) {
            console.error('Error during N8N reset:', error);
        }
    }

    async initializeOrganism(): Promise<void> {
        try {
            await this.http.post('/app/cpfSimulator/initializeOrganism', {}).toPromise();
            console.log('initialize organism successfully');
        } catch (error) {
            console.error('Error during Initialization of organism:', error);
        }
    }

    async initialize(): Promise<void> {
        await this.eraseDatabase();
        await this.resetAutomatorData();
        await this.initializeOrganism();
        await this.router.navigate(['/formation/dossiers/kanban/']);
    }
}
