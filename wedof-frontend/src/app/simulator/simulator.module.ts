import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {SimulatorRoutingModule} from './simulator-routing.module';
import {CpfSimulatorInitializerComponent} from './cpf-simulator-initializer/cpf-simulator-initializer.component';
import { CpfSimulatorSynchronizeComponent } from './cpf-simulator-synchronize/cpf-simulator-synchronize.component';
import { CpfSimulatorUiComponent } from './cpf-simulator-ui/cpf-simulator-ui.component';
import {MatButtonModule} from '@angular/material/button';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {MatCardModule} from '@angular/material/card';
import {MatFormFieldModule} from '@angular/material/form-field';
import {MatInputModule} from '@angular/material/input';
import {MatProgressSpinnerModule} from '@angular/material/progress-spinner';
import {TranslateModule} from '@ngx-translate/core';
import {MatAutocompleteModule} from '@angular/material/autocomplete';


@NgModule({
    declarations: [CpfSimulatorInitializerComponent, CpfSimulatorSynchronizeComponent, CpfSimulatorUiComponent],
    imports: [
        CommonModule,
        SimulatorRoutingModule,
        MatButtonModule,
        FormsModule,
        MatCardModule,
        MatFormFieldModule,
        MatInputModule,
        MatProgressSpinnerModule,
        TranslateModule,
        MatAutocompleteModule,
        ReactiveFormsModule,
    ],
    providers: []
})
export class SimulatorModule {
}
