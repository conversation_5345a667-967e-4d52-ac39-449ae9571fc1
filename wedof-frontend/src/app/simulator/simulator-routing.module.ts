import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {CpfSimulatorInitializerComponent} from './cpf-simulator-initializer/cpf-simulator-initializer.component';
import {CpfSimulatorSynchronizeComponent} from './cpf-simulator-synchronize/cpf-simulator-synchronize.component';
import { CpfSimulatorUiComponent } from './cpf-simulator-ui/cpf-simulator-ui.component';
export const routes: Routes = [
    {
        path: '',
        children: [
            // UI test actions
            {
                path: '',
                component: CpfSimulatorUiComponent
            },
            // initialise Database
            {
                path: 'cpf-simulator-initialize',
                component: CpfSimulatorInitializerComponent
            },
            // sync Database
            {
                path: 'cpf-simulator-synchronize',
                component: CpfSimulatorSynchronizeComponent
            },
        ],
    }

];

@NgModule({
    imports: [
        RouterModule.forChild(routes)
    ],
    exports: [RouterModule],
})
export class SimulatorRoutingModule {
}
