import {NgModule, SecurityContext} from '@angular/core';
import {NgxsModule} from '@ngxs/store';
import {NgxsLoggerPluginModule} from '@ngxs/logger-plugin';
import {BrowserModule} from '@angular/platform-browser';
import {BrowserAnimationsModule} from '@angular/platform-browser/animations';
import {TreoModule} from '@treo';
import {TreoConfigModule} from '@treo/services/config';
import {GeoApiGouvAddressModule} from '@placeme/ngx-geo-api-gouv-address';
import {MarkdownModule, MarkedOptions, MarkedRenderer} from 'ngx-markdown';

import {AppRoutingModule} from './app-routing.module';
import {AppRedirectRoutingModule} from './app-redirect-routing.module';
import {AppComponent} from './app.component';
import {CoreModule} from './core/core.module';
import {appConfig} from './core/config/app.config';
import {LayoutModule} from './layout/layout.module';
import {I18nModule} from './shared/i18n/i18n.module';
import {ErrorsModule} from './shared/errors/errors.module';
import {ApiModule} from './shared/api/api.module';
import {EnvironmentModule} from './shared/environment/environment.module';
import {MatSelectInfiniteScrollModule} from 'ng-mat-select-infinite-scroll';
import {environment} from '../environments/environment';
import {UserState} from './shared/api/state/user.state';
import {AttendeeState} from './shared/api/state/attendee.state';
import {SubscriptionState} from './shared/api/state/subscription.state';
import {OrganismState} from './shared/api/state/organism.state';
import {ConnectionsState} from './shared/api/state/connections.state';
import {HttpClient} from '@angular/common/http';
import {TranslateHttpLoader} from '@ngx-translate/http-loader';
import {TranslateLoader, TranslateModule} from '@ngx-translate/core';
import {ServiceWorkerModule} from '@angular/service-worker';

export function httpLoaderFactory(http: HttpClient): TranslateHttpLoader {
    return new TranslateHttpLoader(http, 'assets/i18n/', '.json');
}

export function markedOptionsFactory(): MarkedOptions {
    const renderer = new MarkedRenderer();
    renderer.link = (href, title, text) => {
        if (href.startsWith('https://www.youtube.com/embed/')) {
            return `<iframe width="704" height="378" src="${href}" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" allowfullscreen></iframe>`;
        } else {
            return `<a href=${href} title="${title ?? ''}" target="_blank" rel="noopener noreferrer">${text}</a>`;
        }
    };
    return {
        renderer: renderer,
        headerIds: true
    };
}

@NgModule({
    declarations: [
        AppComponent
    ],
    imports: [
        BrowserModule,
        BrowserAnimationsModule,
        I18nModule,
        // Treo & Treo Mock API
        TreoModule,
        TreoConfigModule.forRoot(appConfig),
        GeoApiGouvAddressModule.forRoot(),
        // Core
        CoreModule,
        // Layout
        LayoutModule,
        // 3rd party modules
        MarkdownModule.forRoot({
            // "sanitize: SecurityContext.NONE" SHOULD BE REMOVED when updating to v15 :
            // sanitization should be enabled by default and only removed when we specifically tell it in the directive, but it's only available in ngx-markdown >= 15.1.0
            //  In the meantime USE ONLY [innerHtml] and pipe "markdown", as innerHtml will sanitize the content for you
            sanitize: SecurityContext.NONE,
            markedOptions: {
                provide: MarkedOptions,
                useFactory: markedOptionsFactory
            }
        }),
        MatSelectInfiniteScrollModule,
        TranslateModule.forRoot({
            defaultLanguage: 'fr',
            loader: {
                provide: TranslateLoader,
                useFactory: httpLoaderFactory,
                deps: [HttpClient]
            }
        }),
        // Store
        NgxsModule.forRoot([UserState, AttendeeState, SubscriptionState, OrganismState, ConnectionsState], {
            developmentMode: !environment.production,
            selectorOptions: {
                suppressErrors: false,
                injectContainerState: false
            },
        }),
        NgxsLoggerPluginModule.forRoot({
            disabled: true // Change to the following in order to debug store: environment.production,
        }),
        // Transversal component modules
        ErrorsModule,
        ApiModule.forRoot(),
        EnvironmentModule,
        // Functionnal modules
        AppRoutingModule,
        AppRedirectRoutingModule,
        ServiceWorkerModule.register('ngsw-worker.js', {enabled: environment.production})
    ],
    bootstrap: [
        AppComponent
    ]
})
export class AppModule {
}
