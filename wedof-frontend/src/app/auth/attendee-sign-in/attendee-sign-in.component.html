<div *ngIf="!token" class="content-layout fullwidth-basic-normal-scroll">

    <treo-card class="auth-card content-layout">
        <!-- Form container -->
        <div class="form-container">
            <div class="form">
                <!-- Logo -->
                <div class="logo">
                    <img [src]="logo" alt="Logo Organisme">
                </div>
                <!-- Title -->
                <div class="title">
                    {{ 'auth.sign-in.titleAttendee' | translate }} {{ ('private.attendee.type.' + (domain === 'registrationFolders' ? 'attendee' : 'candidate')) | translate }}
                    <a href="/assistance/guides/apprenant-candidat/mon-espace" target="_blank">
                        <mat-icon svgIcon="help"></mat-icon>
                    </a>
                </div>
                <!-- UserMessage -->
                <treo-message *ngIf="message"
                              [appearance]="message.appearance"
                              [showIcon]="message.showIcon"
                              [type]="message.type"
                              [@shake]="message.shake">
                    {{ message.content }}
                </treo-message>
                <form [formGroup]="folderForm" (submit)="signIn()">
                    <mat-form-field>
                        <mat-label>
                            {{ 'auth.sign-in.attendee.form.folder.label' | translate : {domain: domainName} }}
                        </mat-label>
                        <input id="folderId"
                               matInput
                               autofocus
                               readonly="readonly"
                               [formControlName]="'folderId'">
                    </mat-form-field>
                    <!-- Submit button -->
                    <div class="text-center" *ngIf="authMethod == 'id360'">
                        <button type="submit"
                                class="button-lin"
                                [disabled]="loading">
                            <div class="flex items-center justify-center">
                                <img alt="L'Identité Numérique" *ngIf="!loading"
                                     src="/assets/images/lin/marqueur.svg" style="width:32px;" class="mr-2">
                                <p *ngIf="!loading">Utiliser L'Identité Numérique</p>
                                <mat-progress-spinner [color]="'primary'" *ngIf="loading" [diameter]="24"
                                                      [mode]="'indeterminate'"></mat-progress-spinner>
                            </div>
                        </button>
                        <a href="https://lidentitenumerique.laposte.fr"
                           target="_blank"
                           rel="noopener noreferrer"
                           class="discover-lin flex items-center justify-center">
                            <img alt="Découvrir L'Identité Numérique" src="/assets/images/lin/help.svg">
                            <p>Découvrir L'Identité Numérique</p>
                        </a>
                    </div>
                    <div class="text-center" *ngIf="authMethod == 'magicLink'">
                        <button mat-flat-button
                                type="submit"
                                [color]="'primary'"
                                class="w-full"
                                [disabled]="loading || pendingDelay">
                            <div class="flex items-center justify-center">
                                <p *ngIf="!loading && !pendingDelay">Recevoir un lien magique par email</p>
                                <p *ngIf="!loading && pendingDelay">Cliquez sur le lien reçu par email</p>
                                <mat-progress-spinner [color]="'primary'" *ngIf="loading" [diameter]="24"
                                                      [mode]="'indeterminate'"></mat-progress-spinner>
                            </div>
                        </button>
                    </div>
                    <button type="button" class="mt-2" style="width:100%;" mat-flat-button color="primary" *ngIf="domain !== 'registrationFolders'">
                        <a [routerLink]="[passportLink]" target="_blank" class="no-underline">Compléter mon identité</a>
                    </button>
                </form>
            </div>
        </div>
    </treo-card>
</div>
