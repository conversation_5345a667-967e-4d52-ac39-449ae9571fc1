import {Component, OnInit} from '@angular/core';
import {ActivatedRoute, Router} from '@angular/router';
import {AuthService} from '../../core/auth/auth.service';
import {HttpErrorResponse, HttpParams} from '@angular/common/http';
import {ApiError} from '../../shared/errors/errors.types';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {TreoSplashScreenService} from '../../../@treo/services/splash-screen';
import {SnackBarComponent} from '../../shared/material/snack-bar/snack-bar.component';
import {displayTextSnackBar} from '../../shared/utils/displayTextSnackBar';
import {MatSnackBar} from '@angular/material/snack-bar';
import {TranslateService} from '@ngx-translate/core';
import {DomSanitizer, SafeUrl} from '@angular/platform-browser';
import {Select} from '@ngxs/store';
import {OrganismState} from '../../shared/api/state/organism.state';
import {Observable} from 'rxjs';
import {Organism} from '../../shared/api/models/organism';

@Component({
    selector: 'app-attendee-sign-in',
    templateUrl: './attendee-sign-in.component.html',
    styleUrls: ['./attendee-sign-in.component.scss']
})
export class AttendeeSignInComponent implements OnInit {
    message: any;
    space: 'candidat' | 'apprenant';
    domain: 'registrationFolders' | 'certificationFolders';
    domainName: string;
    authMethod: 'id360' | 'magicLink';
    folderId: string;
    folderForm: FormGroup;
    errorMessage: string;
    token: string;
    loading: boolean;
    pendingDelay: boolean;
    logo: SafeUrl;
    passportLink: string;

    private _redirectURL: string;
    @Select(OrganismState.organism) organism$: Observable<Organism>;

    /**
     * Constructor
     *
     * @param {ActivatedRoute} _activatedRoute
     * @param {AuthService} _authService
     * @param _formBuilder
     * @param {Router} _router
     * @param {MatSnackBar} _snackBar
     * @param {TranslateService} _translateService
     * @param _splashScreenService
     * @param _sanitizer
     */
    constructor(
        private _activatedRoute: ActivatedRoute,
        private _authService: AuthService,
        private _formBuilder: FormBuilder,
        private _router: Router,
        private _snackBar: MatSnackBar,
        private _translateService: TranslateService,
        private _splashScreenService: TreoSplashScreenService,
        private _sanitizer: DomSanitizer
    ) {
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    ngOnInit(): void {
        this.loading = true;
        this._redirectURL = this._activatedRoute.snapshot.queryParamMap.get('redirectURL') ?? '';
        let trueRedirectURL = this._redirectURL;
        let linkExpired = false;
        if (this._redirectURL.includes('?')) {
            const [baseUrl, queryString] = this._redirectURL.split('?');
            const queryParams = new HttpParams({fromString: queryString});
            const EXPIRED_PARAMETER = 'expired';
            linkExpired = queryParams.has(EXPIRED_PARAMETER);
            queryParams.delete(EXPIRED_PARAMETER);
            const newQueryString = queryParams.toString();
            trueRedirectURL = newQueryString ? baseUrl + '?' + newQueryString : baseUrl;
        }
        const domainUrl = trueRedirectURL.split('/').length > 1 ? trueRedirectURL.split('/')[2] : '';
        this.space = (domainUrl === 'formation' ? 'apprenant' : 'candidat');
        this.domain = (domainUrl === 'formation' ? 'registrationFolders' : 'certificationFolders');
        this.domainName = this.domain === 'registrationFolders' ? ' de formation' : 'de certification';
        // permet de récupérer l'id du dossier requêté : /apprenant/formation/dossier/toto => toto
        this.folderId = trueRedirectURL.split('/').length > 3 ? trueRedirectURL.split('/')[4] : '';
        this.folderId = this.folderId.split('?')[0] ?? '';
        this.token = this._activatedRoute.snapshot.queryParams['token'];
        this.errorMessage = this._activatedRoute.snapshot.queryParams['errorMessage'] ?? (linkExpired ? 'Votre lien magique a expiré. Cliquez sur "Recevoir un lien magique par email" pour en obtenir un nouveau.' : null);
        this.folderForm = this._formBuilder.group({
            folderId: [this.folderId ?? '', [Validators.required]]
        });
        if (this._redirectURL.includes('enquete')) {
            this.passportLink = this._redirectURL.replace('enquete', 'passeport');
        } else if (this._redirectURL.includes('files')) {
            const redirectUrl = this._redirectURL.substring(0, this._redirectURL.indexOf('files'));
            this.passportLink = redirectUrl + 'passeport';
        } else {
            this.passportLink = this._redirectURL + '/passeport';
        }
        if (this.token) {
            this._splashScreenService.show();
            this._authService.iD360SignIn(this.token)
                .subscribe((response) => {
                    if (response === false) {
                        window.location.href = '/auth/' + this.space
                            + '/connexion'
                            + '?errorMessage=Identification incorrecte'
                            + (this._redirectURL ? '&redirectURL=' + this._redirectURL : '');
                    } else if (this._redirectURL) {
                        this._router.navigateByUrl(this._redirectURL);
                    }
                }, (httpErrorResponse: HttpErrorResponse) => {
                    this.message = {
                        appearance: 'outline',
                        content: (httpErrorResponse.error as ApiError).detail,
                        shake: true,
                        showIcon: false,
                        type: 'error'
                    };
                });
        } else if (this.folderId && this.domain) {
            this._authService.authMethodForAttendee(this.domain, this.folderId).subscribe((response) => {
                this.authMethod = response.method;
                this.loading = false;
            });
        }
        if (this.errorMessage) {
            this.message = {
                appearance: 'outline',
                content: this.errorMessage,
                shake: true,
                showIcon: false,
                type: 'error'
            };
        }
        this.organism$.subscribe((organism: Organism) => {
            const logo: any = organism && organism.logo ? organism.logo : 'assets/images/logo/logo-text.svg';
            this.logo = this._sanitizer.bypassSecurityTrustUrl(logo);
        });
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    /**
     * Sign in
     */
    signIn(): void {
        if (this.folderForm.valid) {
            this.loading = true;
            this.message = null;
            if (this.folderForm.value.folderId !== this.folderId) {
                this._redirectURL = '/' + this.space + '/' + this.domain + '/dossier/' + this.folderForm.value.folderId;
            }
            if (this.authMethod === 'id360') {
                this._authService.iD360SignInUrl('login', this._redirectURL, this.folderForm.value.folderId).subscribe(response => {
                    if (response.url) {
                        window.location.href = response.url;
                    }
                });
            } else {
                this._authService.sendMagicLinkForAttendee(this.domain, this.folderForm.value.folderId, this._redirectURL).subscribe(
                    () => {
                        this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(this._translateService.instant('common.actions.magicLinkSent')));
                        this.pendingDelay = true;
                        this.loading = false;
                        setTimeout(() => {
                            this.pendingDelay = false;
                        }, 60000); // Wait 1 min
                    },
                    () => {
                        this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(this._translateService.instant('common.actions.magicLinkError'), 5000, 'red'));
                    }
                );
            }
        } else {
            this.loading = false;
        }
    }
}

