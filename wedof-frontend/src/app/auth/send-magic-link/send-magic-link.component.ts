import {Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit} from '@angular/core';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {Observable, Subject} from 'rxjs';
import {TreoAnimations} from '@treo/animations';
import {FormValidators} from '../../shared/api/shared/form-validators';
import {TranslateService} from '@ngx-translate/core';
import {HttpErrorResponse} from '@angular/common/http';
import {UserService} from '../../shared/api/services/user.service';
import {Select} from '@ngxs/store';
import {OrganismState} from '../../shared/api/state/organism.state';
import {Organism} from '../../shared/api/models/organism';

@Component({
    selector: 'send-magic-link',
    templateUrl: './send-magic-link.component.html',
    styleUrls: ['./send-magic-link.component.scss'],
    animations: TreoAnimations
})
export class SendMagicLinkComponent implements OnInit, OnDestroy {
    organism: Organism;
    message: any;
    sendMagicLinkForm: FormGroup;
    loading = false;
    @Select(OrganismState.organism) organism$: Observable<Organism>;

    // Private
    private _unsubscribeAll: Subject<any>;

    /**
     * Constructor
     *
     * @param {FormBuilder} _formBuilder
     * @param _userService
     * @param _translateService
     */
    constructor(
        private _formBuilder: FormBuilder,
        private _userService: UserService,
        private _translateService: TranslateService
    ) {
        // Set the defaults
        this.message = null;

        // Set the private defaults
        this._unsubscribeAll = new Subject();
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    ngOnInit(): void {
        this.organism$.subscribe(organism => {
            this.organism = organism;
        });
        // Create the form
        this.sendMagicLinkForm = this._formBuilder.group({
                email: ['', [Validators.required, Validators.pattern(FormValidators.EMAIL_PATTERN)]]
            }
        );
    }

    /**
     * On destroy
     */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    /**
     * Reset password
     */
    sendMagicLink(): void {
        this.loading = true;
        if (this.sendMagicLinkForm.invalid) {
            return;
        }
        this.sendMagicLinkForm.disable();
        this.message = null;
        const email = this.sendMagicLinkForm.get('email').value;
        this._userService.sendMagicLink(email).subscribe({
            next: () => {
                this.loading = false;
                this.sendMagicLinkForm.reset({});
                this.sendMagicLinkForm.enable();
                this.message = {
                    appearance: 'outline',
                    content: this._translateService.instant('auth.send-magic-link.magic-link-sent'),
                    shake: true,
                    showIcon: false,
                    type: 'success'
                };
            }, error: (httpErrorResponse: HttpErrorResponse) => {
                this.loading = false;
                this.sendMagicLinkForm.enable();
                this.message = {
                    appearance: 'outline',
                    content: this._translateService.instant('auth.send-magic-link.magic-link-fail'),
                    shake: true,
                    showIcon: false,
                    type: 'error'
                };
            }
        });
    }
}
