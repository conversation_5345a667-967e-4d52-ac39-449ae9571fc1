import {Component, OnDestroy, OnInit} from '@angular/core';
import {ActivatedRoute, Router} from '@angular/router';
import {Subject} from 'rxjs';
import {AuthService} from 'app/core/auth/auth.service';

@Component({
    selector: 'auth-sign-out',
    templateUrl: './sign-out.component.html',
    styleUrls: ['./sign-out.component.scss'],
})
export class AuthSignOutComponent implements OnInit, OnDestroy {
    countdown: number;
    countdownMapping: any;

    private _unsubscribeAll: Subject<any>;
    private redirectURL: any;

    /**
     * Constructor
     *
     * @param {AuthService} _authService
     * @param {Router} _router
     */
    constructor(
        private _authService: AuthService,
        private _router: Router,
        private route: ActivatedRoute
    ) {

        this._unsubscribeAll = new Subject();

        this.countdown = 5;
        this.countdownMapping = {
            '=1': '# seconde',
            'other': '# secondes'
        };
        this.route.queryParams.subscribe(params => {
            this.redirectURL = params['redirectURL'] ?? '';
        });
    }

    /**
     * On init
     */
    ngOnInit(): void {
        this._authService.signOut();
        if (this.redirectURL) {
            window.location.href = this.redirectURL;
        } else {
            this._router.navigate(['/']);
        }
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }
}
