import {Component, Inject, OnInit} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material/dialog';
import {DataProviders} from '../../shared/api/models/connection';
import {ConnectionService} from '../../shared/api/services/connection.service';
import {TranslateService} from '@ngx-translate/core';

@Component({
    selector: 'app-dialog-habilitation',
    templateUrl: './dialog-habilitation.component.html',
    styleUrls: ['./dialog-habilitation.component.scss']
})
export class DialogHabilitationComponent implements OnInit {
    public dialogContent: string;

    constructor(
        private _translateService: TranslateService,
        public dialogRef: MatDialogRef<DialogHabilitationComponent>,
        private _connectionService: ConnectionService,
        @Inject(MAT_DIALOG_DATA) public dialogData: {
            organism: string,
            dataProvider: DataProviders
        }
    ) {
    }

    ngOnInit(): void {
        this.dialogContent = this._translateService.instant('auth.' + this.dialogData.dataProvider + '.confirmHabilitationText');
    }

    close(): void {
        this.dialogRef.close();
    }

    submit(): void {
        this.dialogRef.close({result: true});
    }
}
