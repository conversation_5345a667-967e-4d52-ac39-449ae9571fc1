import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {AuthConfirmationRequiredComponent} from './confirmation-required/confirmation-required.component';
import {AuthForgotPasswordComponent} from './forgot-password/forgot-password.component';
import {AuthSignInComponent} from './sign-in/sign-in.component';
import {AuthSignOutComponent} from './sign-out/sign-out.component';
import {SignUpWizardComponent} from './sign-up/sign-up-wizard.component';
import {AuthGuard} from '../core/auth/guards/auth.guard';
import {NoAuthGuard} from '../core/auth/guards/noAuth.guard';
import {InvitationResolver} from '../core/auth/resolvers/invitation.resolver';
import {AttendeeSignInComponent} from './attendee-sign-in/attendee-sign-in.component';
import {MagicLinkSignInComponent} from './magic-link-sign-in/magic-link-sign-in.component';
import {SendMagicLinkComponent} from './send-magic-link/send-magic-link.component';

export const routes: Routes = [
    {
        path: '',
        children: [
            // guest routes
            {
                path: 'magic-link-auth',
                canActivate: [NoAuthGuard],
                component: MagicLinkSignInComponent
            },
            {
                path: 'envoyer-lien-magique',
                canActivate: [NoAuthGuard],
                component: SendMagicLinkComponent
            },
            {
                path: 'confirmation-requise',
                canActivate: [NoAuthGuard],
                component: AuthConfirmationRequiredComponent,
            },
            {
                path: 'mot-de-passe-oublie',
                canActivate: [NoAuthGuard],
                component: AuthForgotPasswordComponent
            },
            {
                path: 'connexion',
                canActivate: [NoAuthGuard],
                component: AuthSignInComponent
            },
            {
                path: 'inscription',
                canActivate: [NoAuthGuard],
                component: SignUpWizardComponent
            },
            {
                path: 'apprenant/connexion',
                canActivate: [NoAuthGuard],
                component: AttendeeSignInComponent
            },
            {
                path: 'candidat/connexion',
                canActivate: [NoAuthGuard],
                component: AttendeeSignInComponent
            },
            // invitation routes
            {
                path: 'invitation',
                children: [
                    {
                        path: '',
                        resolve: {
                            data: InvitationResolver,
                        },
                        component: SignUpWizardComponent
                    },
                    {
                        path: ':token',
                        resolve: {
                            data: InvitationResolver,
                        },
                        component: SignUpWizardComponent
                    }
                ]
            },
            // authenticated routes
            {
                path: 'deconnexion',
                canActivate: [AuthGuard],
                component: AuthSignOutComponent
            }
        ],
    }
];

@NgModule({
    imports: [
        RouterModule.forChild(routes)
    ],
    exports: [RouterModule]
})
export class AuthRoutingModule {
}
