import {NgModule} from '@angular/core';
import {SharedModule} from '../shared/shared.module';
import {AuthRoutingModule} from './auth-routing.module';
import {AuthConfirmationRequiredComponent} from './confirmation-required/confirmation-required.component';
import {AuthForgotPasswordComponent} from './forgot-password/forgot-password.component';
import {AuthSignInComponent} from './sign-in/sign-in.component';
import {AuthSignOutComponent} from './sign-out/sign-out.component';
import {SignUpWizardComponent} from './sign-up/sign-up-wizard.component';
import {InvitationModule} from '../shared/invitation/invitation.module';
import {AttendeeSignInComponent} from './attendee-sign-in/attendee-sign-in.component';
import {MatTooltipModule} from '@angular/material/tooltip';
import {DialogHabilitationComponent} from './dialog-habilitation-edof/dialog-habilitation.component';
import {MagicLinkSignInComponent} from './magic-link-sign-in/magic-link-sign-in.component';
import {SendMagicLinkComponent} from './send-magic-link/send-magic-link.component';

@NgModule({
    declarations: [
        AuthConfirmationRequiredComponent,
        AuthForgotPasswordComponent,
        AuthSignInComponent,
        AuthSignOutComponent,
        SignUpWizardComponent,
        AttendeeSignInComponent,
        MagicLinkSignInComponent,
        SendMagicLinkComponent,
        DialogHabilitationComponent
    ],
    imports: [
        AuthRoutingModule,
        SharedModule,
        InvitationModule,
        MatTooltipModule,
    ],
    providers: []
})
export class AuthModule {
}
