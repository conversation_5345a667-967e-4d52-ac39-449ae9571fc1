import {Component, OnInit} from '@angular/core';
import {ActivatedRoute} from '@angular/router';
import {AuthService} from '../../core/auth/auth.service';
import {TreoSplashScreenService} from '../../../@treo/services/splash-screen';

@Component({
    selector: 'magic-link-sign-in',
    templateUrl: './magic-link-sign-in.component.html',
    styleUrls: ['./magic-link-sign-in.component.scss']
})
export class MagicLinkSignInComponent implements OnInit {

    private _redirectURL: string;

    /**
     * Constructor
     *
     * @param {ActivatedRoute} _activatedRoute
     * @param {AuthService} _authService
     * @param _splashScreenService
     */
    constructor(
        private _activatedRoute: ActivatedRoute,
        private _authService: AuthService,
        private _splashScreenService: TreoSplashScreenService
    ) {
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    ngOnInit(): void {
        this._splashScreenService.show();
        this._redirectURL = this._activatedRoute.snapshot.queryParamMap.get('redirectURL') ?? '';
        const impersonate = !!this._activatedRoute.snapshot.queryParamMap.get('impersonate');
        const user = this._activatedRoute.snapshot.queryParamMap.get('user') ?? null;
        const expires = Number(this._activatedRoute.snapshot.queryParamMap.get('expires') ?? null);
        const hash = this._activatedRoute.snapshot.queryParamMap.get('hash') ?? null;
        if (!user || !expires || !hash) {
            window.location.href = '/';
        } else {
            this._authService.signInUsingMagicLink({user, hash, expires, impersonate}).subscribe(response => {
                if (response.url) {
                    window.location.href = response.url;
                } else if (this._redirectURL) {
                    window.location.href = this._redirectURL + (response === false ? '?expired=true' : '');
                } else {
                    window.location.href = '/accueil';
                }
            });
        }
    }
}

