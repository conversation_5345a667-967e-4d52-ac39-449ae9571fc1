import {Component, OnInit} from '@angular/core';
import {FormBuilder, FormGroup} from '@angular/forms';
import {TreoAnimations} from '@treo/animations';
import {AuthService} from 'app/core/auth/auth.service';
import {ActivatedRoute, Router} from '@angular/router';
import {TranslateService} from '@ngx-translate/core';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../shared/errors/errors.types';
import {Select} from '@ngxs/store';
import {OrganismState} from '../../shared/api/state/organism.state';
import {Observable} from 'rxjs';
import {Organism} from '../../shared/api/models/organism';

@Component({
    selector: 'auth-sign-in',
    templateUrl: './sign-in.component.html',
    styleUrls: ['./sign-in.component.scss'],
    animations: TreoAnimations
})
export class AuthSignInComponent implements OnInit {

    organism: Organism;
    signInForm: FormGroup;
    message: any;
    email: string;

    @Select(OrganismState.organism) organism$: Observable<Organism>;

    constructor(
        private _activatedRoute: ActivatedRoute,
        private _authService: AuthService,
        private _formBuilder: FormBuilder,
        private _translateService: TranslateService,
        private _router: Router
    ) {
        this.message = null;
        this.email = null;
    }

    ngOnInit(): void {
        this.organism$.subscribe(organism => {
            if (organism && window.location.hostname.startsWith(organism.subDomain)) {
                this.organism = organism;
            }
        });
        this.checkQueryParams();
        this.signInForm = this._formBuilder.group({
            email: [this.email ?? ''],
            password: [''],
            rememberMe: [false]
        });
        if (this._activatedRoute.snapshot.queryParamMap.get('refreshToken')) {
            this.signIn(this._activatedRoute.snapshot.queryParamMap.get('refreshToken'));
        }
    }

    signIn(refreshToken: string = null): void {
        this.signInForm.disable();
        this.message = null;
        const credentials = this.signInForm.value;
        let signInMethod;
        if (refreshToken) {
            signInMethod = this._authService.signInUsingToken(refreshToken);
        } else {
            signInMethod = this._authService.signIn(credentials);
        }
        signInMethod.subscribe(() => {
            // Set the redirect url.
            // The '/signed-in-redirect' is a dummy url to catch the request and redirect the user
            // to the correct page after a successful sign in. This way, that url can be set via
            // routing file and we don't have to touch here.
            const redirectURL = this._activatedRoute.snapshot.queryParamMap.get('redirectURL') || '/signed-in-redirect';
            const urlParser = document.createElement('a');
            urlParser.href = redirectURL;
            if (urlParser.pathname.startsWith('/oauth2')) {
                window.location.href = redirectURL + '&token=' + this._authService.accessToken;
            } else {
                // Navigate to the redirect url
                this._router.navigateByUrl(redirectURL);
            }
        }, (httpErrorResponse: HttpErrorResponse) => {
            this.signInForm.enable();
            this.message = {
                appearance: 'outline',
                content: (httpErrorResponse.error as ApiError).detail,
                shake: true,
                showIcon: false,
                type: 'error'
            };
        });
    }

    private checkQueryParams(): void {
        this.email = this._activatedRoute.snapshot.queryParamMap.get('email');
        if (this._activatedRoute.snapshot.queryParamMap.get('message')) {
            this.message = {
                appearance: 'outline',
                content: this._translateService.instant('auth.forgot-password.password-sent'),
                shake: false,
                showIcon: false,
                type: 'success'
            };
        }
    }
}
