<div class="content-layout fullwidth-basic-normal-scroll">

    <treo-card class="auth-card fullscreen">

        <!-- Form container -->
        <div class="form-container">

            <div class="form">

                <!-- Logo -->
                <div class="logo" *ngIf="!organism">
                    <img src="assets/images/logo/logo-text.svg">
                </div>

                <!-- Title -->
                <div class="title">
                    {{ 'auth.sign-in.title' | translate }}
                </div>
                <div class="subtitle" *ngIf="!organism">
                    <span>
                        {{ 'auth.sign-in.subtitle.no-account' | translate }}
                    </span>
                    <a class="link" [routerLink]="['/auth/inscription']">
                        {{ 'auth.sign-in.subtitle.registration' | translate }}
                    </a>
                </div>

                <!-- UserMessage -->
                <treo-message *ngIf="message"
                              [appearance]="message.appearance"
                              [showIcon]="message.showIcon"
                              [type]="message.type"
                              [@shake]="message.shake">
                    {{ message.content }}
                </treo-message>

                <!-- Sign in form -->
                <form [formGroup]="signInForm" (submit)="signIn()">

                    <!-- Email field -->
                    <mat-form-field>
                        <mat-label>
                            {{ 'auth.sign-in.form.email.label' | translate }}
                        </mat-label>
                        <input id="email"
                               type="email"
                               matInput
                               autofocus
                               name="email"
                               autocomplete="email"
                               [formControlName]="'email'">
                    </mat-form-field>

                    <!-- Password field -->
                    <mat-form-field>
                        <mat-label>
                            {{ 'auth.sign-in.form.password.label' | translate }}
                        </mat-label>
                        <input id="password"
                               matInput
                               type="password"
                               name="password"
                               autocomplete="current-password"
                               [formControlName]="'password'"
                               #passwordField>
                        <button mat-icon-button
                                (click)="passwordField.type === 'password' ? passwordField.type = 'text' : passwordField.type = 'password'"
                                tabindex="-1"
                                type="button"
                                matSuffix>
                            <mat-icon *ngIf="passwordField.type === 'password'"
                                      svgIcon="visibility"></mat-icon>
                            <mat-icon *ngIf="passwordField.type === 'text'"
                                      svgIcon="visibility_off"></mat-icon>
                        </button>
                    </mat-form-field>

                    <!-- Field footer -->
                    <div class="field-footer">
                        <!-- TODO
                        <div class="remember-me">
                            <mat-checkbox [color]="'primary'"
                                          [formControlName]="'rememberMe'">
                                {{ 'auth.sign-in.form.remember-me' | translate }}
                            </mat-checkbox>
                        </div>-->
                        <a class="link ml-auto"
                           [routerLink]="['/auth/mot-de-passe-oublie']">
                            {{ 'auth.sign-in.form.forgot-password' | translate }}
                        </a>
                    </div>

                    <!-- Submit button -->
                    <button class="submit-button treo-mat-button-large"
                            mat-flat-button
                            type="submit"
                            [color]="'primary'"
                            [disabled]="signInForm.disabled">
                        <span *ngIf="!signInForm.disabled">
                            {{ 'auth.sign-in.form.submit.label' | translate }}
                        </span>
                        <mat-progress-spinner class="mr-4" *ngIf="signInForm.disabled"
                                              [diameter]="24"
                                              [mode]="'indeterminate'"></mat-progress-spinner>
                    </button>
                    <div class="flex mt-2">
                        <a class="link m-auto"
                           [routerLink]="['/auth/envoyer-lien-magique']">
                            {{ 'auth.sign-in.form.send-magic-link' | translate }}
                        </a>
                    </div>
                    <!--TODO QUAND SERVER DOWN -->
                    <!--<treo-message type="error"
                                  [showIcon]="false"
                                  appearance="outline">
                        <div class="mt-3 mb-3">
                            La synchronisation avec EDOF est perturbée par des pannes intermittentes de la plateforme :
                            <a href="https://status.wedof.fr" target="_blank">https://status.wedof.fr</a>
                        </div>
                    </treo-message>-->
                </form>

            </div>

        </div>

        <!-- Content container -->
        <div class="content-container" *ngIf="!organism; else contentWithOrganism">

            <!-- Background - Inline SVG for easy customizations -->
            <svg class="background"
                 viewBox="0 0 1531 891"
                 preserveAspectRatio="xMidYMax slice"
                 xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M0 182c144.327 47.973 534.462 219.712 440.509 369.87C346.555 702.028 79.877 662.846 0 638V182z"></path>
                <path
                    d="M1405 848c-424.366 158.009-437-164-437-272s102-425 563-576v769c-21.333 29.333-63.333 55.667-126 79z"></path>
                <path
                    d="M1531 162c-122.914-17.284-377.96 33.191-543.433 206.414C822.095 541.636 797.342 648.75 835.842 731.622c38.5 82.871 198.243 134.841 400.555 92.053C1438.71 780.886 1492.752 775.894 1531 768V162z"></path>
            </svg>

            <div class="content">
                <div class="title">
                    <span>
                        {{ 'auth.sign-in.welcome.title' | translate }}
                    </span>
                    <span>
                        {{ 'auth.sign-in.welcome.subtitle' | translate }}
                    </span>
                </div>
                <div class="mt-5 mb-5">
                    <h4>
                        <!--<a href="https://www.of.moncompteformation.gouv.fr/espace-public/actualites/laureats-de-lappel-projets-cpf-interface-de-gestion-automatisee-edof"
                           target="_blank">-->{{ 'auth.sign-up.title.subtitle' | translate }} <!--</a>-->
                    </h4>
                </div>
                <div class="description"
                     [innerHTML]="'auth.sign-in.welcome.description' | translate | markdown">
                </div>

                <button class="learn-more-button" onclick="window.location.href = 'https://www.wedof.fr/public-demo';"
                        mat-stroked-button>
                    {{ 'auth.sign-in.welcome.learn-more' | translate }}
                </button>
            </div>

        </div>

        <ng-template #contentWithOrganism>
            <div class="content-container" style="background:{{organism.customColorScheme}}60;">
                <div class="flex flex-col">
                    <div class="m-auto w-80" *ngIf="organism.logo else onlyTitle">
                        <img [src]="organism.logo"/>
                    </div>
                    <ng-template #onlyTitle>
                        <h1 class="text-center">{{ organism.name }}</h1>
                    </ng-template>
                    <!-- Logo -->
                    <div class="logo mt-8 w-20 mx-auto">
                        <img src="assets/images/logo/logo-text.svg">
                    </div>
                </div>
                <!-- Background - Inline SVG for easy customizations -->
                <svg class="background"
                     viewBox="0 0 1531 891"
                     preserveAspectRatio="xMidYMax slice"
                     xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M0 182c144.327 47.973 534.462 219.712 440.509 369.87C346.555 702.028 79.877 662.846 0 638V182z"></path>
                    <path
                        d="M1405 848c-424.366 158.009-437-164-437-272s102-425 563-576v769c-21.333 29.333-63.333 55.667-126 79z"></path>
                    <path
                        d="M1531 162c-122.914-17.284-377.96 33.191-543.433 206.414C822.095 541.636 797.342 648.75 835.842 731.622c38.5 82.871 198.243 134.841 400.555 92.053C1438.71 780.886 1492.752 775.894 1531 768V162z"></path>
                </svg>
            </div>
        </ng-template>

    </treo-card>

</div>
