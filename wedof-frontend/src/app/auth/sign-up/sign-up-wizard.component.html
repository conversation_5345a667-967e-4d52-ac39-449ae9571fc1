<div class="content-layout fullwidth-standard-normal-scroll">
    <div class="main">
        <div class="max-w-5xl m-auto signup-wizard">
            <!-- Horizontal stepper -->
            <div class="logo flex flex-row mt-16 mb-8">
                <img style="width: 48px" src="{{'auth.sign-up.image.src' | translate}}"
                     alt="{{'auth.sign-up.image.alt' | translate}}">
                <h3 class="pl-3 m-0 self-center">{{ 'auth.sign-up.title.text' | translate }}<span
                    class="wedof-name">&nbsp;{{ 'auth.sign-up.title.span' | translate }}</span></h3>
            </div>
            <div class="xs:p-1 p-4 bg-card shadow rounded overflow-hidden">
                <treo-message type="primary" class="m-5" appearance="outline"
                              *ngIf="signUpForm.get('user.partnership').value"
                              [showIcon]="false">
                    <div class="m-3"><span
                        [innerHTML]="'auth.sign-up.partnership.text' | translate: {partnership:signUpForm.get('user.partnership').value}"></span>
                        <br/><br/>
                        <p [innerHTML]="'auth.sign-up.partnership.text2' | translate"></p>
                    </div>
                </treo-message>
                <div class="m-5" *ngIf="!isInvited">
                    <div class="flex flex-row justify-center">
                        <div class="px-5 text-center">
                            <mat-icon [svgIcon]="'check_circle'" color="primary"></mat-icon>
                            <div class="my-2 text-xl"
                                 [innerHTML]="'auth.sign-up.insights.insights-1' | translate"></div>
                        </div>
                        <div class="px-5 text-center">
                            <mat-icon [svgIcon]="'check_circle'" color="primary"></mat-icon>
                            <div class="my-2 text-xl">{{ 'auth.sign-up.insights.insights-2' | translate }}</div>
                        </div>
                        <div class="px-5 text-center">
                            <mat-icon [svgIcon]="'check_circle'" color="primary"></mat-icon>
                            <div class="my-2 text-xl">{{ 'auth.sign-up.insights.insights-3' | translate }}</div>
                        </div>
                    </div>
                </div>
                <mat-horizontal-stepper [linear]="true" (animationDone)="setFocus()"
                                        (selectionChange)="setTargetId($event)" #SignUpWizard>
                    <mat-step #SignUpWizardStepInvitation *ngIf="isInvited">
                        <ng-template matStepLabel>{{ 'auth.sign-up.stepInvitation.title' | translate }}</ng-template>
                        <app-invitation [invitingOrganism]="invitingOrganism">
                        </app-invitation>
                        <div class="flex justify-end">
                            <button type="button" class="px-4 mr-2" mat-flat-button (click)="refuseAccess()"
                                    color="warn">
                                {{ 'common.invitation.button.signup.refuse' | translate }}
                            </button>
                            <button type="button" class="px-4" mat-flat-button (click)="next()" color="primary">
                                {{ 'common.invitation.button.signup.authorize' | translate }}
                            </button>
                        </div>
                    </mat-step>
                    <mat-step [stepControl]="signUpForm.get('user')">
                        <ng-template matStepLabel>{{ 'auth.sign-up.step1.title' | translate }}</ng-template>
                        <p class="my-6 font-medium">
                            {{ 'auth.sign-up.step1.subtitle' | translate }}
                        </p>
                        <form [formGroup]="signUpForm.get('user')" (ngSubmit)="registerUser()" #ngFormStep1="ngForm">
                            <div class="flex flex-col gt-xs:flex-row">
                                <mat-form-field class="flex-auto gt-xs:pr-3 input-line">
                                    <mat-label>{{ 'auth.sign-up.step1.labels.first-name' | translate }}</mat-label>
                                    <input matInput formControlName="firstName"
                                           [placeholder]="'auth.sign-up.step1.placeholders.first-name' | translate"
                                           id="inputFocus0" required>
                                    <mat-icon matPrefix svgIcon="account_circle"></mat-icon>
                                    <mat-error class="flex-auto gt-xs:pr-3">
                                        {{ 'auth.sign-up.step1.errors.first-name' | translate }}
                                    </mat-error>
                                </mat-form-field>

                                <mat-form-field class="flex-auto gt-xs:pl-3 input-line">
                                    <mat-label>{{ 'auth.sign-up.step1.labels.last-name' | translate }}</mat-label>
                                    <input matInput formControlName="lastName"
                                           [placeholder]="'auth.sign-up.step1.placeholders.last-name' | translate"
                                           required>
                                    <mat-icon matPrefix svgIcon="account_circle"></mat-icon>
                                    <mat-error class="flex-auto gt-xs:pr-3">
                                        {{ 'auth.sign-up.step1.errors.last-name' | translate }}
                                    </mat-error>
                                </mat-form-field>
                            </div>

                            <div class="flex flex-col gt-xs:flex-row">
                                <mat-form-field class="flex-auto gt-xs:pr-3 input-line">
                                    <mat-label>{{ 'auth.sign-up.step1.labels.email' | translate }}</mat-label>
                                    <input matInput formControlName="email"
                                           [placeholder]="'auth.sign-up.step1.placeholders.email' | translate" required>
                                    <mat-icon matPrefix svgIcon="mail"></mat-icon>
                                    <mat-error class="flex-auto gt-xs:pr-3">
                                        {{ 'auth.sign-up.step1.errors.email' | translate }}
                                    </mat-error>
                                </mat-form-field>

                                <mat-form-field class="flex-auto gt-xs:pl-3 input-line">
                                    <mat-label>
                                        {{ 'auth.sign-up.step1.labels.phone' | translate }}
                                    </mat-label>
                                    <input matInput formControlName="phoneNumber" type="tel"
                                           [placeholder]="'auth.sign-up.step1.placeholders.phone' | translate" required>
                                    <mat-icon matPrefix svgIcon="phone"></mat-icon>
                                    <mat-error class="flex-auto gt-xs:pr-3">
                                        {{ 'auth.sign-up.step1.errors.phone' | translate }}
                                    </mat-error>
                                </mat-form-field>
                            </div>

                            <div class="flex flex-col gt-xs:flex-row">
                                <mat-form-field class="flex-auto gt-xs:pr-3 xl:w-50 input-line">
                                    <mat-label>{{ 'auth.sign-up.step1.labels.password' | translate }}</mat-label>
                                    <input matInput type="password" formControlName="password"
                                           [placeholder]="'auth.sign-up.step1.placeholders.password' | translate"
                                           required
                                           #password>
                                    <mat-icon matPrefix svgIcon="lock"></mat-icon>
                                    <button type="button"
                                            mat-icon-button
                                            (click)="password.type === 'password' ? password.type = 'text' : password.type = 'password'"
                                            tabindex="-1" matSuffix>
                                        <mat-icon *ngIf="password.type === 'password'" svgIcon="visibility"></mat-icon>
                                        <mat-icon *ngIf="password.type === 'text'" svgIcon="visibility_off"></mat-icon>
                                    </button>
                                    <mat-error class="flex-auto">
                                        {{ 'auth.sign-up.step1.errors.password' | translate }}
                                    </mat-error>
                                </mat-form-field>

                                <mat-form-field class="flex-auto gt-xs:pl-3 xl:w-50 input-line">
                                    <mat-label>{{ 'auth.sign-up.step1.labels.password-verification' | translate }}
                                    </mat-label>
                                    <input matInput type="password" formControlName="passwordCheck"
                                           [placeholder]="'auth.sign-up.step1.placeholders.password-verification' | translate"
                                           required #passwordCheck>
                                    <mat-icon matPrefix svgIcon="lock"></mat-icon>
                                    <button type="button"
                                            mat-icon-button
                                            (click)="passwordCheck.type === 'password' ? passwordCheck.type = 'text' : passwordCheck.type = 'password'"
                                            tabindex="-1" matSuffix>
                                        <mat-icon *ngIf="passwordCheck.type === 'password'" svgIcon="visibility">
                                        </mat-icon>
                                        <mat-icon *ngIf="passwordCheck.type === 'text'" svgIcon="visibility_off">
                                        </mat-icon>
                                    </button>
                                    <mat-error class="flex-auto gt-xs:pr-3">
                                        {{ 'common.errors.password.verification' | translate }}
                                    </mat-error>
                                </mat-form-field>
                            </div>
                            <div class="flex justify-end items-center">
                                <div class="mr-3">
                                    <treo-message *ngIf="bottomMessage" [appearance]="bottomMessage.appearance"
                                                  [showIcon]="bottomMessage.showIcon" [type]="bottomMessage.type"
                                                  [@shake]="bottomMessage.shake">
                                        {{ bottomMessage.content }}
                                    </treo-message>
                                </div>
                                <button type="button" class="px-8 mr-2" *ngIf="isInvited" (click)="previous()"
                                        mat-flat-button color="accent">
                                    {{ 'auth.sign-up.step1.button.back' | translate }}
                                </button>
                                <button type="submit" class="pl-8 pr-4"
                                        [disabled]="ngFormStep1.invalid || signUpForm.disabled" mat-flat-button
                                        color="primary">
                                    <mat-progress-spinner class="mr-4" *ngIf="signUpForm.disabled" [diameter]="24"
                                                          mode="indeterminate"></mat-progress-spinner>
                                    <ng-container *ngIf="!signUpForm.disabled">
                                        {{ 'auth.sign-up.step1.button.next' | translate }}
                                        <mat-icon [inline]="true" svgIcon="chevron_right" class="pl-2"></mat-icon>
                                    </ng-container>
                                </button>
                            </div>
                        </form>
                    </mat-step>
                    <mat-step [stepControl]="signUpForm.get('organism')">
                        <ng-template matStepLabel>
                            {{ 'auth.sign-up.step2.title' | translate }}
                        </ng-template>
                        <div class="my-6 font-medium ql-bg-black">
                            <p *ngIf="!isInvited else invitationStepLabel">{{ 'auth.sign-up.step2.subtitle' | translate }}
                            </p>
                            <ng-template #invitationStepLabel>{{ 'auth.sign-up.step2.subtitleInvitation' | translate }}
                            </ng-template>
                        </div>
                        <form [formGroup]="signUpForm.get('organism')" (ngSubmit)="associateOrganism()"
                              #ngFormStep2="ngForm">
                            <div class="flex flex-col gt-xs:flex-row border-b-2 border-primary my-3">
                                <mat-form-field class="flex-auto gt-xs:pr-3 xl:w-50">
                                    <mat-label>{{ 'auth.sign-up.step2.labels.siret' | translate }}</mat-label>
                                    <input matInput formControlName="siret"
                                           [placeholder]="'auth.sign-up.step2.placeholders.siret' | translate"
                                           [readonly]="isInvited" maxlength="14" pattern="^[0-9]*$" id="inputFocus1"
                                           [matTooltip]="'auth.sign-up.step2.labels.siretTooltip' | translate"
                                           required #siret>
                                    <mat-hint class="pl-4" align="end">{{ siret.value.length }} / 14</mat-hint>
                                    <mat-icon svgIcon="dialpad" matPrefix></mat-icon>
                                    <mat-error class="gt-xs:pr-3">
                                        {{ 'auth.sign-up.step2.errors.siret' | translate }}
                                    </mat-error>
                                </mat-form-field>
                                <mat-form-field class="flex-auto gt-xs:pl-3 xl:w-50">
                                    <mat-label>{{ 'auth.sign-up.step2.labels.agreement' | translate }}</mat-label>
                                    <input matInput formControlName="agreement"
                                           [placeholder]="'auth.sign-up.step2.placeholders.agreement' | translate"
                                           [value]="organism?.agreement"
                                           minlength="10" maxlength="11" pattern="^[0-9A-Za-z]*$"
                                           id="inputFocus4"
                                           [required]="organism && !organism.isCertifierOrganism && !organism.isTrainingOrganism"
                                           #siret>
                                    <mat-icon svgIcon="tag" matPrefix></mat-icon>
                                    <mat-error class="gt-xs:pr-3">
                                        {{ 'auth.sign-up.step2.errors.agreement' | translate }}
                                    </mat-error>
                                </mat-form-field>
                            </div>
                            <div class="flex flex-col gt-xs:flex-row">
                                <mat-form-field class="flex-auto disabled-input-bg-color">
                                    <mat-label>{{ 'auth.sign-up.step2.labels.organism' | translate }}</mat-label>
                                    <input matInput
                                           [placeholder]="'auth.sign-up.step2.placeholders.organism' | translate"
                                           [readonly]="true" [value]="organism?.name">
                                    <mat-icon matPrefix svgIcon="business"></mat-icon>
                                </mat-form-field>
                            </div>
                            <div class="flex flex-col gt-xs:flex-row">
                                <mat-form-field class="flex-auto gt-xs:pr-3 xl:w-50 disabled-input-bg-color">
                                    <mat-label>{{ 'auth.sign-up.step2.labels.address' | translate }}</mat-label>
                                    <input matInput
                                           [placeholder]="'auth.sign-up.step2.placeholders.address' | translate"
                                           [readonly]="true" [value]="organism?.address">
                                    <mat-icon matPrefix svgIcon="home_work"></mat-icon>
                                </mat-form-field>
                                <mat-form-field class="flex-auto gt-xs:pl-3 xl:w-50 disabled-input-bg-color">
                                    <mat-label>{{ 'auth.sign-up.step2.labels.phone' | translate }}</mat-label>
                                    <input matInput [placeholder]="'auth.sign-up.step2.placeholders.phone' | translate"
                                           [readonly]="true"
                                           [value]="organism?.phones?.length ? organism.phones[0] : ''">
                                    <mat-icon matPrefix svgIcon="phone"></mat-icon>
                                </mat-form-field>
                            </div>
                            <div class="flex flex-col gt-xs:flex-row">
                                <mat-form-field class="flex-auto gt-xs:pr-3 xl:w-50 disabled-input-bg-color">
                                    <mat-label>{{ 'auth.sign-up.step2.labels.city' | translate }}</mat-label>
                                    <input matInput [placeholder]="'auth.sign-up.step2.placeholders.city' | translate"
                                           [readonly]="true" [value]="organism?.city">
                                    <mat-icon matPrefix svgIcon="location_city"></mat-icon>
                                </mat-form-field>
                                <mat-form-field class="flex-auto gt-xs:pl-3 xl:w-50 disabled-input-bg-color">
                                    <mat-label>{{ 'auth.sign-up.step2.labels.postcode' | translate }}</mat-label>
                                    <input matInput
                                           [placeholder]="'auth.sign-up.step2.placeholders.postcode' | translate"
                                           [readonly]="true" [value]="organism?.postalCode">
                                    <mat-icon matPrefix svgIcon="mail"></mat-icon>
                                </mat-form-field>
                            </div>
                            <div class="flex flex-row gt-xs:flex-row justify-around mb-4">
                                <div class="flex items-center">
                                    <mat-icon class="icon-small"
                                              [color]="organism?.isCertifierOrganism ? 'primary' : 'warn'"
                                              [svgIcon]="organism?.isCertifierOrganism ? 'done' : 'close'">
                                    </mat-icon>
                                    {{ 'auth.sign-up.step2.checkboxes.is-certifier-organism' | translate }}
                                </div>
                                <div class="flex items-center">
                                    <mat-icon class="icon-small"
                                              [color]="organism?.isTrainingOrganism || (signUpForm.get('organism.agreement').value && signUpForm.get('organism.agreement').valid) ? 'primary' : 'warn'"
                                              [svgIcon]="organism?.isTrainingOrganism || (signUpForm.get('organism.agreement').value && signUpForm.get('organism.agreement').valid) ? 'done' : 'close'">
                                    </mat-icon>
                                    {{ 'auth.sign-up.step2.checkboxes.is-training-organism' | translate }}
                                </div>
                            </div>
                            <div class="flex flex-col gt-xs:flex-row">
                                <mat-form-field class="flex-auto gt-xs:pr-3 xl:w-50">
                                    <mat-label>{{ 'private.profile.organism.form.fields.tools.billingSoftware' | translate }}</mat-label>
                                    <mat-select class="flex flex-col" formControlName="billingSoftware">
                                        <mat-option *ngFor="let choice of choicesFacturation"
                                                    [value]="choice.value"> {{ choice.key }}
                                        </mat-option>
                                    </mat-select>
                                    <button type="button"
                                            *ngIf="signUpForm.get('organism').get('billingSoftware')?.value" mat-button
                                            matSuffix
                                            mat-icon-button aria-label="Clear"
                                            (click)=" signUpForm.get('organism').get('billingSoftware').setValue(null);">
                                        <mat-icon svgIcon="close"></mat-icon>
                                    </button>
                                </mat-form-field>
                                <mat-form-field class="flex-auto gt-xs:pl-3 xl:w-50"
                                                *ngIf="signUpForm.get('organism').get('billingSoftware')?.value === 'other' ">
                                    <mat-label>{{ 'private.profile.organism.form.fields.tools.billingSoftware' | translate }} - {{'common.actions.other' | translate}}</mat-label>
                                    <input matInput
                                           [required]="true"
                                           formControlName="billingSoftwareOther"
                                           [placeholder]="'private.profile.organism.form.fields.tools.billingSoftware' | translate">
                                </mat-form-field>
                            </div>
                            <div class="flex flex-col gt-xs:flex-row">
                                <mat-form-field class="flex-auto gt-xs:pr-3 xl:w-50">
                                    <mat-label>{{ 'private.profile.organism.form.fields.tools.crm' | translate }}</mat-label>
                                    <mat-select class="flex flex-col" formControlName="crm">
                                        <mat-option *ngFor="let choice of choicesCrm"
                                                    [value]="choice.value"> {{ choice.key }}
                                        </mat-option>
                                    </mat-select>
                                    <button type="button" *ngIf="signUpForm.get('organism').get('crm')?.value"
                                            mat-button matSuffix
                                            mat-icon-button aria-label="Clear"
                                            (click)=" signUpForm.get('organism').get('crm').setValue(null);">
                                        <mat-icon svgIcon="close"></mat-icon>
                                    </button>
                                </mat-form-field>
                                <mat-form-field class="flex-auto gt-xs:pl-3 xl:w-50"
                                                *ngIf="signUpForm.get('organism').get('crm')?.value === 'other' ">
                                    <mat-label>{{ 'private.profile.organism.form.fields.tools.crm' | translate }} - {{'common.actions.other' | translate}}</mat-label>
                                    <input matInput
                                           [required]="true"
                                           formControlName="crmOther"
                                           [placeholder]="'private.profile.organism.form.fields.tools.crm' | translate">
                                </mat-form-field>
                            </div>
                            <div>
                                <treo-message *ngIf="centerMessage"
                                              [ngSwitch]="centerMessage.action"
                                              [appearance]="centerMessage.appearance"
                                              [showIcon]="centerMessage.showIcon" [type]="centerMessage.type"
                                              [@shake]="centerMessage.shake">
                                    <a *ngSwitchCase="'redirect'"
                                       class="no-underline"
                                       (click)="redirectToLogin()" [innerHTML]="centerMessage.content"></a>

                                    <p *ngSwitchCase="'resend'">
                                            <span
                                                [innerHtml]="'auth.sign-up.step2.resendInvitation.start' | translate"></span>
                                        <button class="inline underline cursor-pointer whitespace-pre-wrap"
                                                (click)="resendInvitationEmail()">{{ 'auth.sign-up.step2.resendInvitation.link' | translate }}
                                        </button>
                                        <span
                                            [innerHtml]="'auth.sign-up.step2.resendInvitation.end' | translate"></span>
                                    </p>

                                    <p *ngSwitchDefault [innerHTML]="centerMessage.content"></p>
                                </treo-message>
                            </div>
                            <div class="mt-10 mb-10 flex flex-col items-end">
                                <mat-checkbox [color]="'primary'" [formControlName]="'msa'" class=""
                                              id="inputFocus3">
                                    {{ 'auth.sign-up.step3.checkboxes.msa.text' | translate }} (<a
                                    class="link font-medium" href="/public-cgu"
                                    target="_blank">{{
                                    'auth.sign-up.step3.checkboxes.msa.link' |
                                        translate
                                    }}</a>)
                                </mat-checkbox>
                                <mat-checkbox [color]="'primary'" [formControlName]="'rgpd'" class="">
                                    {{ 'auth.sign-up.step3.checkboxes.rgpd.text' | translate }} (<a
                                    class="link font-medium" href="/public-confidentialite"
                                    target="_blank">{{
                                    'auth.sign-up.step3.checkboxes.rgpd.link' |
                                        translate
                                    }}</a>)
                                </mat-checkbox>
                            </div>
                            <div class="flex justify-end items-center">
                                <div class="mr-3">
                                    <treo-message *ngIf="bottomMessage" [appearance]="bottomMessage.appearance"
                                                  [showIcon]="bottomMessage.showIcon" [type]="bottomMessage.type"
                                                  [@shake]="bottomMessage.shake">
                                        <p [innerHTML]="bottomMessage.content"></p>
                                    </treo-message>
                                </div>
                                <button [disabled]="signUpForm.disabled" type="button" class="px-8 mr-2"
                                        (click)="previous()" mat-flat-button
                                        color="accent">
                                    {{ 'auth.sign-up.step2.button.back' | translate }}
                                </button>
                                <button type="submit" class="px-8"
                                        [disabled]="ngFormStep2.invalid || !organism || organism?.hasUsers || signUpForm.disabled"
                                        mat-flat-button
                                        color="primary">
                                    <mat-progress-spinner class="mr-4" *ngIf="signUpForm.disabled" [diameter]="24"
                                                          mode="indeterminate"></mat-progress-spinner>
                                    <ng-container *ngIf="!signUpForm.disabled">
                                        {{ 'auth.sign-up.step2.button.next' | translate }}
                                    </ng-container>
                                </button>
                            </div>
                        </form>
                    </mat-step>
                    <mat-step>
                        <ng-template matStepLabel>{{ 'auth.sign-up.step3.title'| translate }}</ng-template>
                        <form (ngSubmit)="finalize()" #ngFormStep3="ngForm">
                            <div class="flex flex-col xs:flex-col pb-12">
                                <p *ngIf="!isInvited; else subtitleInvitation" class="my-6 font-medium mx-4">
                                    {{ 'auth.sign-up.step3.subtitle'| translate }}
                                </p>
                                <ng-template #subtitleInvitation class="my-6 font-medium mx-4">
                                    <p>
                                        {{ 'auth.sign-up.step3.subtitle-invited' | translate }}
                                    </p>
                                </ng-template>

                                <img src="{{'auth.sign-up.step3.image.src'| translate}}" style="width: 450px"
                                     class="m-auto" (click)="openEdofDialog()"
                                     alt="{{'auth.sign-up.step3.image.alt'| translate}}">
                                <div class="w-auto flex flex-auto justify-center">
                                    <button mat-flat-button type="button"
                                            class="pl-8 pr-8 space-center flex flex-row edof-button"
                                            id="inputFocus2" [disabled]="edofConnectionOk">
                                        <a class="no-underline" target="_blank"
                                           href="/assistance/guides/organismes-formation/deleguer-habilitation">
                                            <i *ngIf="edofConnectionOk" matPrefix id="associertext"
                                               class="material-icons icon pr-2 self-center">check_circle</i>
                                            <span id="associericon" class="self-center">
                                            {{ (edofConnectionOk ? 'auth.sign-up.step3.button.associationDone' : 'auth.sign-up.step3.button.associate') | translate }}
                                        </span>
                                        </a>
                                    </button>
                                </div>
                                <p class="text-center font-medium my-6">{{ 'auth.sign-up.title.subtitle'| translate }}</p>
                            </div>
                            <div
                                class="flex xs:flex-col xs:justify-center sm:justify-end md:justify-end lg:justify-end xl:justify-end xs:text-center">
                                <div class="mr-3">
                                    <treo-message *ngIf="bottomMessage" [appearance]="bottomMessage.appearance"
                                                  [showIcon]="bottomMessage.showIcon" [type]="bottomMessage.type"
                                                  [@shake]="bottomMessage.shake">
                                        {{ bottomMessage.content }}
                                    </treo-message>
                                </div>
                                <button type="submit" class="px-8" mat-flat-button color="accent"
                                        [disabled]="signUpForm.disabled">
                                    <mat-progress-spinner class="mr-4" *ngIf="signUpForm.disabled" [diameter]="24"
                                                          mode="indeterminate"></mat-progress-spinner>
                                    <ng-container *ngIf="!signUpForm.disabled">
                                        {{ 'auth.sign-up.step3.button.next'| translate }}
                                    </ng-container>
                                </button>
                            </div>
                        </form>
                    </mat-step>
                </mat-horizontal-stepper>
            </div>
        </div>
    </div>
</div>
