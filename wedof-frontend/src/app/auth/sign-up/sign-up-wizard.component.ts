import {HttpErrorResponse} from '@angular/common/http';
import {Component, OnDestroy, OnInit, ViewChild} from '@angular/core';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {ActivatedRoute, Router} from '@angular/router';
import {MatDialog} from '@angular/material/dialog';
import {MatStepper} from '@angular/material/stepper';
import {TreoAnimations} from '@treo/animations';
import {Subject} from 'rxjs';
import {distinctUntilChanged, mergeMap, switchMap, takeUntil} from 'rxjs/operators';
import {AuthService} from '../../core/auth/auth.service';
import {User} from '../../shared/api/models/user';
import {Organism} from '../../shared/api/models/organism';
import {CertifierAccess} from '../../shared/api/models/certifier-access';
import {MustMatch} from './helper/must-match.validator';
import {CertifierAccessService} from '../../shared/api/services/certifier-access.service';
import {TranslateService} from '@ngx-translate/core';
import {FormValidators} from 'app/shared/api/shared/form-validators';
import {ApiError} from '../../shared/errors/errors.types';
import {SubscriptionCertifierTypes, SubscriptionTrainingTypes} from '../../shared/api/models/subscription';
import {SubscriptionCreateParams} from '../../shared/api/services/subscription.service';
import {DialogConnectionAuthComponent} from '../../shared/connection/dialog-connection-auth/dialog-connection-auth.component';
import {DataProviderConfig, DataProviders} from '../../shared/api/models/connection';
import {CreateUser, UpdateUser, UserState} from '../../shared/api/state/user.state';
import {Store} from '@ngxs/store';
import {CreateSubscription} from '../../shared/api/state/subscription.state';
import {CreateOrganism, OrganismState, UpdateOrganism} from '../../shared/api/state/organism.state';
import {OrganismService} from '../../shared/api/services/organism.service';
import {
    choicesCrm,
    choicesFacturation
} from '../../private/profile/organism-form-page/dialog-organism-collect-informations/dialog-organism-collect-informations.component';

@Component({
    selector: 'sign-up-wizard',
    templateUrl: './sign-up-wizard.component.html',
    styleUrls: ['./sign-up-wizard.component.scss'],
    animations: [TreoAnimations]
})
export class SignUpWizardComponent implements OnInit, OnDestroy {

    private _targetId = 'inputFocus0';
    // en minuscule pour éviter la casse
    // en cas de modif de partnership, penser à le reporter dans le back
    private _trainingPartnership = ['dendreo'];
    private _certifierPartnership = [];

    organism: Organism;
    signUpForm: FormGroup;
    invitingOrganism: Organism;
    certifierAccess: CertifierAccess;
    user: User;

    bottomMessage = null;
    centerMessage = null;
    isInvited: boolean;
    edofConnectionOk = false;

    private _unsubscribeAll: Subject<any>;

    @ViewChild('SignUpWizard') private signUpWizard: MatStepper;

    choicesFacturation = choicesFacturation;
    choicesCrm = choicesCrm;

    constructor(
        private _activatedRoute: ActivatedRoute,
        private _formBuilder: FormBuilder,
        private _authService: AuthService,
        private _organismService: OrganismService,
        private _certifierAccessService: CertifierAccessService,
        private _translateService: TranslateService,
        private _router: Router,
        private _dialog: MatDialog,
        private _store: Store
    ) {
    }

    ngOnInit(): void {
        if (this._authService.check()) { // TODO OULA c'est un observable ça, on renvoit tjrs un truc donc on signOut systématiquement
            this._authService.signOut();
        }
        this._unsubscribeAll = new Subject();
        const routeData = this._activatedRoute.snapshot.data?.data;
        this.isInvited = !!routeData;
        this.certifierAccess = routeData?.certifierAccess;
        this.organism = routeData?.organism;
        this.invitingOrganism = routeData?.invitingOrganism;
        const queryParams = this._activatedRoute.snapshot.queryParams;

        // Horizontal stepper form
        this.signUpForm = this._formBuilder.group({
            user: this._formBuilder.group({
                    email: [queryParams?.email, [Validators.required, Validators.pattern(FormValidators.EMAIL_PATTERN)]],
                    phoneNumber: [queryParams?.phoneNumber, [Validators.required, Validators.pattern(FormValidators.PHONE_PATTERN)]],
                    password: ['', [Validators.required, FormValidators.PASSWORD]],
                    passwordCheck: ['', Validators.required],
                    lastName: [queryParams?.lastName, Validators.required],
                    firstName: [queryParams?.firstName, Validators.required],
                    partnership: [null],
                    subscription: [null]
                },
                {validator: MustMatch('password', 'passwordCheck')}
            ),
            organism: this._formBuilder.group({
                siret: [this.organism?.siret ?? (queryParams?.siret ?? ''),
                    [Validators.required, Validators.pattern('^[0-9]*$'), Validators.minLength(14), Validators.maxLength(14)]],
                agreement: [this.organism?.agreement ?? null],
                rgpd: [false, Validators.requiredTrue],
                msa: [false, Validators.requiredTrue],
                billingSoftware: [],
                billingSoftwareOther: [], // Careful if add validator here, code below won't work
                crm: [],
                crmOther: [] // Careful if add validator here, code below won't work
            })
        });

        const organismForm = this.signUpForm.get('organism');
        organismForm.get('crm').valueChanges.subscribe(value => {
            const other = organismForm.get('crmOther');
            if (value === 'other') {
                other.setValidators(Validators.required);
            } else {
                other.clearValidators(); // Required otherwise validator will be triggered even if the field is not in html
            }
            other.updateValueAndValidity();
        });
        organismForm.get('billingSoftware').valueChanges.subscribe(value => {
            const other = organismForm.get('billingSoftwareOther');
            if (value === 'other') {
                other.setValidators(Validators.required);
            } else {
                other.clearValidators(); // Required otherwise validator will be triggered even if the field is not in html
            }
            other.updateValueAndValidity();
        });

        if (queryParams?.siret && queryParams.siret === 14) {
            this.checkSiret(queryParams.siret);
        }
        if (!this.isInvited) {
            this.signUpForm.get('organism.siret').valueChanges.pipe(takeUntil(this._unsubscribeAll), distinctUntilChanged()).subscribe((siret: string) => {
                if (siret.length === 14) {
                    this.checkSiret(siret);
                }
            });
        }

        this._activatedRoute.queryParams.pipe(takeUntil(this._unsubscribeAll)).subscribe((params) => {
            this.signUpForm.get('user.partnership').setValue(params['partnership'] ?? null);
            this.signUpForm.get('user.subscription').setValue(params['subscriptionType'] ?? null);
        });
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    previous(): void {
        this.bottomMessage = null;
        this.signUpWizard.previous();
    }

    next(): void {
        this.bottomMessage = null;
        this.signUpWizard.next();
    }

    registerUser(): void {
        this.bottomMessage = null;
        this.signUpForm.disable();

        const userInfos = this.signUpForm.get('user').value;
        const newUser: User = {
            firstName: userInfos.firstName,
            lastName: userInfos.lastName,
            phoneNumber: (userInfos.phoneNumber !== '' ? userInfos.phoneNumber : null),
            email: userInfos.email,
            password: userInfos.password
        };

        this._store.dispatch(new CreateUser(newUser)).pipe(
            mergeMap(() => this._store.selectOnce(UserState.user)),
            switchMap(user => {
                this.user = user;
                return this._authService.signIn({email: user.email, password: userInfos.password});
            })
        ).subscribe({
            next: () => {
                this.signUpForm.enable();
                this.bottomMessage = null;
                this.signUpWizard.selected.completed = true;
                this.signUpWizard.next();
            }, error: (httpErrorResponse: HttpErrorResponse) => {
                this.signUpForm.enable();
                this.showBottomMessage((httpErrorResponse.error as ApiError).detail);
            }
        });
    }

    checkSiret(siret): void {
        this.organism = null;
        this.bottomMessage = null;
        this.centerMessage = null;
        this._store.dispatch(new CreateOrganism(siret, true)).pipe(
            mergeMap(() => this._store.selectOnce(OrganismState.organism)),
        ).subscribe({
            next: (organism: Organism) => {
                this.organism = organism;
                if (this.organism.hasUsers) {
                    this._organismService.getOwnerEmail(this.organism.siret).subscribe(({email: ownerEmail}) => {
                        this.showCenterMessage(this._translateService.instant('auth.sign-up.step2.errors.user-exists-for-organism', {ownerEmail}), 'error', 'redirect');
                    });
                } else if (this.organism.hasCertifierAccess && !this.isInvited) {
                    this.showCenterMessage('', 'info', 'resend');
                }
                const agreementValidators = [Validators.pattern('^[0-9]*$'), Validators.minLength(10), Validators.maxLength(11)];
                if (!this.organism.isTrainingOrganism && !this.organism.isCertifierOrganism) {
                    agreementValidators.push(Validators.required);
                }
                this.signUpForm.get('organism.agreement').setValidators(agreementValidators);
            },
            error: () => {
                this.organism = null;
                this.showBottomMessage(this._translateService.instant('auth.sign-up.step2.errors.organism-unknown'));
            }
        });
    }

    associateOrganism(): void {
        if (this.signUpForm.get('organism').valid && this.organism) {
            this.bottomMessage = null;
            this.centerMessage = null;
            this.signUpForm.disable();
            const updatedOrganism: Organism = {
                siret: this.organism.siret,
                billingSoftware: this.signUpForm.get('organism.billingSoftwareOther') ? this.signUpForm.get('organism.billingSoftwareOther').value : this.signUpForm.get('organism.billingSoftware').value,
                crm: this.signUpForm.get('organism.crmOther') ? this.signUpForm.get('organism.crmOther').value : this.signUpForm.get('organism.crm').value
            };
            if (this.signUpForm.get('organism.agreement').value) {
                updatedOrganism.agreement = this.signUpForm.get('organism.agreement').value;
            }
            const partnership = this.signUpForm.get('user.partnership').value;
            const subscriptionType = this.signUpForm.get('user.subscription').value;
            const userEmail = this.user.email;
            const updateOrganismUser: User = {
                email: userEmail,
                mainOrganism: {
                    siret: updatedOrganism.siret
                }
            };
            const updateFinalizeUser: User = {
                email: userEmail,
                rgpdMsa: new Date()
            };
            this._store.dispatch(new UpdateUser(userEmail, updateOrganismUser)).pipe(
                mergeMap(() => this._store.selectOnce(UserState.user)),
                switchMap(updatedUser => {
                    this.user = updatedUser;
                    // Required to create connection even if no actual data to update
                    return this._store.dispatch(new UpdateOrganism(updatedOrganism.siret, updatedOrganism)).pipe(
                        mergeMap(() => this._store.selectOnce(OrganismState.organism)),
                    );
                }),
                switchMap(organism => {
                    this.organism = organism;
                    const subscriptionCreateParams: SubscriptionCreateParams = {};
                    if (partnership) {
                        if (this.organism.isTrainingOrganism && this._trainingPartnership.includes(partnership.toLowerCase())) {
                            subscriptionCreateParams.training = {partnership: partnership};
                            if (subscriptionType) {
                                subscriptionCreateParams.training.type = subscriptionType;
                            }
                        }
                        if (this.organism.isCertifierOrganism && this._certifierPartnership.includes(partnership.toLowerCase())) {
                            subscriptionCreateParams.certifier = {partnership: partnership};
                            if (subscriptionType) {
                                subscriptionCreateParams.certifier.type = subscriptionType;
                            }
                        }
                    } else if (this.isInvited) {
                        subscriptionCreateParams.training = {type: SubscriptionTrainingTypes.NONE};
                        subscriptionCreateParams.certifier = {type: SubscriptionCertifierTypes.PARTNER};
                    }
                    // tous les autres cas sont gérés par le back
                    return this._store.dispatch(new CreateSubscription(subscriptionCreateParams));
                }),
                switchMap(() => {
                    return this._store.dispatch(new UpdateUser(userEmail, updateFinalizeUser));
                }),
                mergeMap(() => this._store.selectOnce(UserState.user)),
            ).subscribe({
                next: (updatedUser) => {
                    this.user = updatedUser;
                    if (this.isInvited) {
                        this._certifierAccessService.accept(this.certifierAccess).subscribe();
                    }
                    this.bottomMessage = null;
                    this.signUpWizard.selected.completed = true;
                    this.signUpForm.enable();
                    this.signUpWizard.next();
                },
                error: (httpErrorResponse: HttpErrorResponse) => {
                    this.signUpForm.enable();
                    this.showBottomMessage((httpErrorResponse.error as ApiError).detail);
                }
            });
        }
    }

    finalize(): void {
        if (this.signUpForm.invalid) {
            return;
        }

        this.signUpForm.disable();

        if (this.isInvited) {
            this._router.navigateByUrl('/formation/certifications/catalogue');
        } else {
            this._router.navigateByUrl(this._activatedRoute.snapshot.queryParamMap.get('redirectURL') || '/signed-in-redirect');
        }
    }

    private showBottomMessage(message: string, type: string = 'error'): void {
        this.bottomMessage = {
            appearance: 'outline',
            content: message,
            shake: true,
            showIcon: false,
            type: type
        };
    }

    private showCenterMessage(message: string, type: string, action: string = 'default'): void {
        this.centerMessage = {
            appearance: 'outline',
            content: message,
            shake: true,
            showIcon: false,
            type: type,
            action: action,
        };
    }

    openEdofDialog(): void {
        const dialogRef = this._dialog.open(DialogConnectionAuthComponent, {
            disableClose: true,
            panelClass: ['no-padding-panel', 'full-page-scroll-40'],
            height: 'auto',
            data: {
                dataProvider: DataProviders.CPF,
                authorizations: DataProviderConfig[DataProviders.CPF].authorizations,
                limitedUsage: this.isInvited,
                organism: this.organism
            }
        });

        dialogRef.afterClosed().subscribe(connectionOk => {
            this.edofConnectionOk = connectionOk;
        });
    }

    setFocus(): void {
        const targetElem = document.getElementById(this._targetId);
        targetElem.focus();
    }

    setTargetId(event: any): void {
        this._targetId = `inputFocus${event.selectedIndex}`;
    }

    refuseAccess(): void {
        this._certifierAccessService.refuse(this.certifierAccess).subscribe(
            () => {
                this._router.navigateByUrl('/');
            }
        );
    }

    redirectToLogin(): void {
        this._authService.signOut();
        this._authService.redirectToAuth();
    }

    resendInvitationEmail(): void {
        this.signUpForm.disable();

        this._certifierAccessService.resendInvitationEmail(this.organism.siret).subscribe({
                next: (obfuscatedEmail: string) => {
                    this.showCenterMessage(this._translateService.instant('auth.sign-up.step2.resendInvitation.invitation-resent', {obfuscatedEmail}), 'info');
                    setTimeout(() => {
                        this.redirectToLogin();
                    }, 10000);
                },
                error: (httpErrorResponse: HttpErrorResponse) => {
                    this.showBottomMessage((httpErrorResponse.error as ApiError).detail);
                }
            }
        );
    }
}
