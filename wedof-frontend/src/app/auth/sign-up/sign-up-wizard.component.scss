:host{
    ::ng-deep{
        .disabled-input-bg-color .mat-form-field-flex {
            background-color: #ececec8f !important;
        }
        .wedof-name {
            font-variant: small-caps;
        }

        .main .mat-checkbox-label {
            color: black !important
        }

        #inputFocus2:disabled {
            background: linear-gradient(180deg, #28a745, #28a742de);
        }

        .edof-button {
            color: #fff !important;
            background: linear-gradient(180deg, #008af6, #0077d4);
            line-height: 1em;
            letter-spacing: .5px;
        }


        #associertext, #associericon {
            color: #fff !important;
        }

        .mat-step-header {
            pointer-events: none !important;
        }

        button .mat-icon.mat-icon-inline {
            width: 38px !important;
        }

        .input-line {
            min-height: 112px;
        }
    }
}

@media (max-width: 700px) {
    .cdk-global-overlay-wrapper {
        align-items: initial !important;
    }
}


