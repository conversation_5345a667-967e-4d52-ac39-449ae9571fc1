import { Component, OnInit} from '@angular/core';
import { TreoAnimations } from '@treo/animations';

@Component({
    selector     : 'auth-confirmation-required',
    templateUrl  : './confirmation-required.component.html',
    styleUrls    : ['./confirmation-required.component.scss'],
    animations   : TreoAnimations
})
export class AuthConfirmationRequiredComponent implements OnInit
{
    email: string;

    /**
     * Constructor
     */
    constructor()
    {
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    ngOnInit(): void
    {
        // Get the user's email
        this.email = '<EMAIL>';
    }
}
