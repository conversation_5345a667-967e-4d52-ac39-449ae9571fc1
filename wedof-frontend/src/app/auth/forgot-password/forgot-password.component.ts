import {Component, OnInit} from '@angular/core';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {TreoAnimations} from '@treo/animations';
import {UserService} from '../../shared/api/services/user.service';
import {Router} from '@angular/router';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../shared/errors/errors.types';
import {FormValidators} from '../../shared/api/shared/form-validators';
import {Organism} from '../../shared/api/models/organism';
import {Select} from '@ngxs/store';
import {OrganismState} from '../../shared/api/state/organism.state';
import {Observable} from 'rxjs';
import {TranslateService} from '@ngx-translate/core';

@Component({
    selector: 'auth-forgot-password',
    templateUrl: './forgot-password.component.html',
    styleUrls: ['./forgot-password.component.scss'],
    animations: TreoAnimations
})
export class AuthForgotPasswordComponent implements OnInit {
    organism: Organism;
    forgotPasswordForm: FormGroup;
    message: any;
    loading = false;
    @Select(OrganismState.organism) organism$: Observable<Organism>;

    constructor(
        private _router: Router,
        private _formBuilder: FormBuilder,
        private _userService: UserService,
        private _translateService: TranslateService,
    ) {
        this.message = null;
        this.loading = false;
    }

    ngOnInit(): void {
        this.organism$.subscribe(organism => {
            this.organism = organism;
        });
        // Create the form
        this.forgotPasswordForm = this._formBuilder.group({
            email: ['', [Validators.required, Validators.pattern(FormValidators.EMAIL_PATTERN)]]
        });
    }

    askNewPassword(): void {
        this.loading = true;
        if (this.forgotPasswordForm.invalid) {
            return;
        }
        this.forgotPasswordForm.disable();
        this.message = null;
        const email = this.forgotPasswordForm.get('email').value;
        this._userService.forgotPassword(email).subscribe({
            next: () => {
                this.loading = false;
                this.forgotPasswordForm.reset({});
                this.forgotPasswordForm.enable();
                this._router.navigate(['/', 'auth', 'connexion'], {
                    queryParams: {
                        email: email,
                        message: true
                    }
                });
            }, error: (httpErrorResponse: HttpErrorResponse) => {
                this.loading = false;
                this.forgotPasswordForm.enable();
                this.message = {
                    appearance: 'outline',
                    content: this._translateService.instant('auth.forgot-password.password-sent-fail'),
                    shake: true,
                    showIcon: false,
                    type: 'error'
                };
            }
        });
    }
}
