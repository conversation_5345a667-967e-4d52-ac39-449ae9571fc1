<div class="content-layout fullwidth-basic-normal-scroll">

    <treo-card class="auth-card fullscreen">

        <!-- Form container -->
        <div class="form-container">

            <div class="form">

                <!-- Logo -->
                <div class="logo" *ngIf="!organism">
                    <img src="assets/images/logo/logo-text.svg">
                </div>

                <!-- Title -->
                <div class="title">{{ 'auth.forgot-password.title' | translate }}</div>
                <div class="subtitle">{{ 'auth.forgot-password.subtitle' | translate }}</div>

                <!-- UserMessage -->
                <treo-message *ngIf="message"
                              [appearance]="message.appearance"
                              [showIcon]="message.showIcon"
                              [type]="message.type"
                              [@shake]="message.shake">
                    {{ message.content }}
                </treo-message>

                <!-- Forgot password form -->
                <form [formGroup]="forgotPasswordForm">

                    <!-- Email field -->
                    <mat-form-field>
                        <mat-label>{{ 'auth.forgot-password.email' | translate }}</mat-label>
                        <input id="email"
                               matInput
                               required
                               type="email"
                               [formControlName]="'email'">
                        <mat-error *ngIf="forgotPasswordForm.get('email').hasError('required')">
                            {{ 'auth.forgot-password.errors.email-required' | translate }}
                        </mat-error>
                        <mat-error *ngIf="forgotPasswordForm.get('email').hasError('pattern')">
                            {{ 'auth.forgot-password.errors.email-invalid' | translate }}
                        </mat-error>
                    </mat-form-field>
                    <!-- Submit button -->
                    <button class="submit-button treo-mat-button-large"
                            mat-flat-button
                            type="submit"
                            color="primary"
                            [disabled]="loading || forgotPasswordForm.get('email').invalid"
                            (click)="askNewPassword()">
                        <span *ngIf="!loading">
                            {{ 'auth.forgot-password.reset-password-button' | translate }}
                        </span>
                        <mat-progress-spinner class="mr-4" *ngIf="loading"
                                              [diameter]="24"
                                              mode="indeterminate"></mat-progress-spinner>
                    </button>

                    <!-- Form footer -->
                    <div class="form-footer">
                        <span>{{ 'auth.forgot-password.return-to-caption' | translate }}</span>
                        <a class="link"
                           [routerLink]="['/auth/connexion']">{{ 'auth.forgot-password.return-to-button' | translate }}</a>
                    </div>

                </form>

            </div>

        </div>

        <!-- Content container -->
        <div class="content-container" *ngIf="!organism; else contentWithOrganism">

            <!-- Background - Inline SVG for easy customizations -->
            <svg class="background"
                 viewBox="0 0 1531 891"
                 preserveAspectRatio="xMidYMax slice"
                 xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M0 182c144.327 47.973 534.462 219.712 440.509 369.87C346.555 702.028 79.877 662.846 0 638V182z"></path>
                <path
                    d="M1405 848c-424.366 158.009-437-164-437-272s102-425 563-576v769c-21.333 29.333-63.333 55.667-126 79z"></path>
                <path
                    d="M1531 162c-122.914-17.284-377.96 33.191-543.433 206.414C822.095 541.636 797.342 648.75 835.842 731.622c38.5 82.871 198.243 134.841 400.555 92.053C1438.71 780.886 1492.752 775.894 1531 768V162z"></path>
            </svg>

            <div class="content">
                <div class="title">
                    <span>
                        {{ 'auth.sign-in.welcome.title' | translate }}
                    </span>
                    <span>
                        {{ 'auth.sign-in.welcome.subtitle' | translate }}
                    </span>
                </div>
                <div class="mt-5 mb-5">
                    <h4>
                        <!--<a href="https://www.of.moncompteformation.gouv.fr/espace-public/actualites/laureats-de-lappel-projets-cpf-interface-de-gestion-automatisee-edof"
                           target="_blank">-->{{ 'auth.sign-up.title.subtitle' | translate }}<!--</a>-->
                    </h4>
                </div>
                <div class="description"
                     [innerHTML]="'auth.sign-in.welcome.description' | translate | markdown">
                </div>
                <button class="learn-more-button"
                        mat-stroked-button>
                    {{ 'auth.sign-in.welcome.learn-more' | translate }}
                </button>
            </div>

        </div>

        <ng-template #contentWithOrganism>
            <div class="content-container" style="background:{{organism.customColorScheme}}60;">
                <div class="flex flex-col">
                    <div class="m-auto w-80" *ngIf="organism.logo else onlyTitle">
                        <img [src]="organism.logo"/>
                    </div>
                    <ng-template #onlyTitle>
                        <h1 class="text-center">{{ organism.name }}</h1>
                    </ng-template>
                    <!-- Logo -->
                    <div class="logo mt-8 w-20 mx-auto">
                        <img src="assets/images/logo/logo-text.svg">
                    </div>
                </div>
                <!-- Background - Inline SVG for easy customizations -->
                <svg class="background"
                     viewBox="0 0 1531 891"
                     preserveAspectRatio="xMidYMax slice"
                     xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M0 182c144.327 47.973 534.462 219.712 440.509 369.87C346.555 702.028 79.877 662.846 0 638V182z"></path>
                    <path
                        d="M1405 848c-424.366 158.009-437-164-437-272s102-425 563-576v769c-21.333 29.333-63.333 55.667-126 79z"></path>
                    <path
                        d="M1531 162c-122.914-17.284-377.96 33.191-543.433 206.414C822.095 541.636 797.342 648.75 835.842 731.622c38.5 82.871 198.243 134.841 400.555 92.053C1438.71 780.886 1492.752 775.894 1531 768V162z"></path>
                </svg>
            </div>
        </ng-template>
    </treo-card>

</div>
