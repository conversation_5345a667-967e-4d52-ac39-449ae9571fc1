import {Injectable} from '@angular/core';
import {ActivatedRouteSnapshot, CanActivate, RouterStateSnapshot} from '@angular/router';
import {Observable} from 'rxjs';
import {mapTo} from 'rxjs/operators';
import {TranslateService} from '@ngx-translate/core';

@Injectable({
    providedIn: 'root'
})
export class ResellerGuard implements CanActivate {

    constructor(
        private _translateService: TranslateService
    ) {
    }

    canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> {
        // We load data here rather than in a resolver because all canActivate guards are called before all resolve are called
        // See https://angular.io/api/router/Resolve#usage-notes
        // This way, data is ensured to be loaded as early as possible
        return this._translateService.use('fr').pipe(
            mapTo(true)
        );
    }
}
