import {Injectable} from '@angular/core';
import {ActivatedRouteSnapshot, Resolve, RouterStateSnapshot} from '@angular/router';
import {forkJoin, Observable} from 'rxjs';
import {switchMap} from 'rxjs/operators';
import {NavigationService} from '../../../shared/navigation/navigation.service';
import {Store} from '@ngxs/store';
import {SubscriptionState} from '../../../shared/api/state/subscription.state';
import {TreoNavigationItem} from '../../../../@treo/components/navigation';
import {OrganismState} from '../../../shared/api/state/organism.state';
import {OrganismApplicationService} from '../../../shared/api/services/organism-application.service';

@Injectable({
    providedIn: 'root'
})
export class PrivateNavigationResolver implements Resolve<any> {

    constructor(
        private _navigation: NavigationService,
        private _store: Store,
        private _organismApplicationService: OrganismApplicationService
    ) {
    }

    resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<TreoNavigationItem[]> {
        return forkJoin([
            this._store.selectOnce(OrganismState.organism),
            this._store.selectOnce(SubscriptionState.subscription),
            this._organismApplicationService.findAll()
        ]).pipe(
            switchMap(([mainOrganism, subscription, organismApplications]) =>
                this._navigation.getNavigation(mainOrganism, subscription, organismApplications)
            )
        );
    }
}
