import {ActivatedRouteSnapshot, Resolve, Router, RouterStateSnapshot} from '@angular/router';
import {Injectable} from '@angular/core';
import {OrganismService} from '../../../shared/api/services/organism.service';
import {RegistrationFolderService} from '../../../shared/api/services/registration-folder.service';
import {catchError, switchMap} from 'rxjs/operators';
import {RegistrationFolder} from '../../../shared/api/models/registration-folder';
import {EMPTY, forkJoin, Observable, of} from 'rxjs';
import {Organism} from '../../../shared/api/models/organism';
import {HttpErrorResponse} from '@angular/common/http';
import {CertificationFolderService} from '../../../shared/api/services/certification-folder.service';

@Injectable(
    {providedIn: 'root'}
)
export class AttendeeRegistrationFolderDataResolver implements Resolve<any> {

    constructor(private _router: Router,
                private _organismService: OrganismService,
                private _registrationFolderService: RegistrationFolderService,
                private _certificationFolderService: CertificationFolderService) {
    }

    resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): any {
        const code = route.queryParamMap.get('code') ?? route.paramMap.get('code');
        const typeFileId = route.queryParamMap.get('typeFileId') ?? route.paramMap.get('typeFileId') ?? null;
        return this._registrationFolderService.getForAttendee(code).pipe(
            switchMap((registrationFolder: RegistrationFolder): Observable<[RegistrationFolder, Organism, number, string, boolean]> => {
                const certificationFolderExternalId = registrationFolder._links.certificationFolder ? registrationFolder._links.certificationFolder.externalId : '';
                return forkJoin([
                    of(registrationFolder),
                    this._organismService.getForAttendee(registrationFolder._links.organism.siret),
                    of(Number(typeFileId)),
                    certificationFolderExternalId !== '' ? this._certificationFolderService.getInternalAttendeeLink(certificationFolderExternalId) : of(''),
                    of(!!route.url.find(segment => segment.path === 'enquete'))
                ]);
            }),
            catchError((e: HttpErrorResponse) => {
                if (e.status === 403) {
                    window.location.href = '403-wrong-attendee?redirectURL=' + encodeURI('/apprenant/formation/dossier/' + code);
                } else {
                    this._router.navigate(['/404-not-found']);
                    return EMPTY;
                }
            })
        );
    }
}
