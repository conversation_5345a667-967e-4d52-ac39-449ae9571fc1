import {ActivatedRouteSnapshot, Resolve, Router, RouterStateSnapshot} from '@angular/router';
import {Injectable} from '@angular/core';
import {catchError, switchMap} from 'rxjs/operators';
import {EMPTY, Observable, of} from 'rxjs';
import {CertificationFolderService} from '../../../shared/api/services/certification-folder.service';
import {PassportDataDetails} from '../../../shared/api/models/certification-folder';

@Injectable(
    {providedIn: 'root'}
)
export class PassportDataResolver implements Resolve<any> {

    constructor(private _router: Router,
                private _certificationFolderService: CertificationFolderService) {
    }

    resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): any {
        const code = route.queryParamMap.get('code') ?? route.paramMap.get('code');
        return this._certificationFolderService.passportDataDetails(code).pipe(
            switchMap((passportDataDetails: PassportDataDetails): Observable<[string, PassportDataDetails]> => {
                return of([code, passportDataDetails]);
            }),
            catchError(() => {
                this._router.navigate(['/404-not-found']);
                return EMPTY;
            })
        );
    }
}
