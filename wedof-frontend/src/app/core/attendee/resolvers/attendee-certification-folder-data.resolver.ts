import {ActivatedRouteSnapshot, Resolve, Router, RouterStateSnapshot} from '@angular/router';
import {Injectable} from '@angular/core';
import {CertificationService} from '../../../shared/api/services/certification.service';
import {CertificationFolderService} from '../../../shared/api/services/certification-folder.service';
import {catchError, switchMap} from 'rxjs/operators';
import {EMPTY, forkJoin, Observable, of} from 'rxjs';
import {CertificationFolder} from '../../../shared/api/models/certification-folder';
import {Certification} from '../../../shared/api/models/certification';
import {RegistrationFolderService} from '../../../shared/api/services/registration-folder.service';

@Injectable(
    {providedIn: 'root'}
)
export class AttendeeCertificationFolderDataResolver implements Resolve<any> {

    constructor(private _router: Router,
                private _certificationService: CertificationService,
                private _registrationFolderService: RegistrationFolderService,
                private _certificationFolderService: CertificationFolderService) {
    }

    resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): any {
        const code = route.queryParamMap.get('code') ?? route.paramMap.get('code');
        const typeFileId = route.queryParamMap.get('typeFileId') ?? route.paramMap.get('typeFileId') ?? null;
        return this._certificationFolderService.getForAttendee(code).pipe(
            switchMap((certificationFolder: CertificationFolder): Observable<[CertificationFolder, Certification, number, string, boolean]> => {
                const registrationFolderExternalId = certificationFolder._links.registrationFolder ? certificationFolder._links.registrationFolder.externalId : '';
                return forkJoin([
                    of(certificationFolder),
                    this._certificationService.getForAttendee(certificationFolder._links.certification.certifInfo),
                    of(Number(typeFileId)),
                    registrationFolderExternalId !== '' ? this._registrationFolderService.getInternalAttendeeLink(registrationFolderExternalId) : of(''),
                    of(!!route.url.find(segment => segment.path === 'enquete'))
                ]);
            }),
            catchError((e) => {
                if (e.status === 403) {
                    window.location.href = '403-wrong-attendee?redirectURL=' + encodeURI('/apprenant/certification/dossier/' + code);
                } else {
                    this._router.navigate(['/404-not-found']);
                }
                return EMPTY;
            })
        );
    }
}
