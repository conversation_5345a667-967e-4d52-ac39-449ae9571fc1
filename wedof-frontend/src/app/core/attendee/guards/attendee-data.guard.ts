import {Injectable} from '@angular/core';
import {ActivatedRouteSnapshot, CanActivate, RouterStateSnapshot} from '@angular/router';
import {forkJoin, Observable} from 'rxjs';
import {Store} from '@ngxs/store';
import {mapTo} from 'rxjs/operators';
import {FetchAttendee} from '../../../shared/api/state/attendee.state';
import {TranslateService} from '@ngx-translate/core';

@Injectable({
    providedIn: 'root'
})
export class AttendeeDataGuard implements CanActivate {

    constructor(
        private _store: Store,
        private _translateService: TranslateService
    ) {
    }

    canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> {
        // We load data here rather than in a resolver because all canActivate guards are called before all resolve are called
        // See https://angular.io/api/router/Resolve#usage-notes
        // This way, data is ensured to be loaded as early as possible
        return forkJoin([
            this._store.dispatch(new FetchAttendee()),
            this._translateService.use('fr'),
        ]).pipe(
            mapTo(true)
        );
    }
}
