import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable, of, throwError} from 'rxjs';
import {catchError, mapTo, tap} from 'rxjs/operators';
import {AuthUtils} from 'app/core/auth/auth.utils';
import {APIEndpoint} from 'app/shared/api/services/api-endpoint.enum';
import {CandidatePassportBody, CandidateSocialSecurityBody} from '../../shared/api/models/attendee';

@Injectable()
export class AuthService {

    constructor(
        private _httpClient: HttpClient
    ) {
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Accessors
    // -----------------------------------------------------------------------------------------------------

    set accessToken(token: string) {
        localStorage.setItem('access_token', token);
    }

    get accessToken(): string {
        return localStorage.getItem('access_token');
    }

    set refreshToken(token: string) {
        localStorage.setItem('refresh_token', token);
    }

    get refreshToken(): string {
        return localStorage.getItem('refresh_token');
    }

    set isAttendee(value: boolean) {
        localStorage.setItem('is_attendee', JSON.stringify(value));
    }

    get isAttendee(): boolean {
        return JSON.parse(localStorage.getItem('is_attendee'));
    }

    set isSales(value: boolean) {
        localStorage.setItem('is_sales', JSON.stringify(value));
    }

    get isSales(): boolean {
        return JSON.parse(localStorage.getItem('is_sales'));
    }

    get switchUser(): string {
        return localStorage.getItem('switch_user');
    }

    set switchUser(username: string) {
        localStorage.setItem('switch_user', username);
    }

    get adminView(): string {
        return localStorage.getItem('admin_view') === null ? String(false) : localStorage.getItem('admin_view');
    }

    set adminView(value: string) {
        localStorage.setItem('admin_view', value);
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    signIn(credentials: { email: string, password: string }): Observable<any> {
        return this._httpClient.post(`${APIEndpoint.API}/auth`, credentials).pipe(
            tap((response: any) => {
                this.setTokenData(response, true);
            })
        );
    }

    signInUsingMagicLink(credentials: {
        user: string,
        hash: string,
        expires: number,
        impersonate: boolean
    }): Observable<any> {
        return this._httpClient.get(`${APIEndpoint.APP}/magic-link-auth/?` + `user=${credentials.user}&expires=${credentials.expires}&hash=${credentials.hash}&impersonate=${credentials.impersonate}`).pipe(
            catchError(() => {
                // Return false
                return of(false);
            }),
            tap((response: any) => {
                this.setTokenData(response, true);
            })
        );
    }

    signInUsingToken(refreshToken: string = null): Observable<boolean> {
        // Renew token
        return this._httpClient.post(`${APIEndpoint.API}/refresh`, {
            refresh_token: refreshToken ?? this.refreshToken
        }).pipe(
            catchError(() => {
                // Return false
                return of(false);
            }),
            tap((response: any) => {
                this.setTokenData(response, false);
            }),
            mapTo(true)
        );
    }

    signOut(): Observable<boolean> {
        this.removeAuthLocalStorage();
        return of(true);
    }

    check(): Observable<boolean> {
        if (!this.accessToken) {
            return of(false);
        } else {
            const isTokenExpired = AuthUtils.isTokenExpired(this.accessToken);
            if (isTokenExpired) {
                return this.signInUsingToken(this.refreshToken);
            } else {
                return of(true);
            }
        }
    }

    impersonate(username: string): boolean {
        if (username == null) {
            this.switchUser = null;
            localStorage.removeItem('switch_user');
            localStorage.removeItem('admin_view');
        } else {
            this.switchUser = username;
        }
        return true;
    }

    toggleViewAsAdmin(): boolean {
        if (this.adminView === null) {
            this.adminView = String(true);
        } else if (this.adminView === String(true)) {
            this.adminView = String(false);
        } else {
            this.adminView = String(true);
        }
        return true;
    }

    iD360SignInUrl(parcours: 'login' | 'passport', redirectURL: string, folderId: string, browserCallbackUrl: string = null): Observable<any> {
        return this._httpClient.post<any>(`${APIEndpoint.APP}/public/identities/id360/signInUrl/${parcours}`, {
            id: folderId,
            redirectURL: redirectURL,
            browserCallbackUrl: browserCallbackUrl
        });
    }

    iD360UserData(parcours: string, token: string): Observable<CandidatePassportBody> {
        return this._httpClient.post(`${APIEndpoint.APP}/public/identities/id360/userData/${parcours}`, {token: token}).pipe(
            catchError(() => {
                // Return false
                return of(false);
            }),
            tap((response: any) => {
                // this.setTokenData(response, true);
            })
        );
    }

    iD360SignIn(token: string): Observable<any> {
        return this._httpClient.post(`${APIEndpoint.APP}/identities/id360/auth`, {token: token}).pipe(
            catchError(() => {
                // Return false
                return of(false);
            }),
            tap((response: any) => {
                this.setTokenData(response, true);
            })
        );
    }

    authMethodForAttendee(domain: string, folderId: string): Observable<any> {
        return this._httpClient.get(`${APIEndpoint.APP}/public/` + domain + '/' + folderId + '/authMethodForAttendee').pipe(
            catchError(() => {
                // Return false
                return of(false);
            })
        );
    }

    sendMagicLinkForAttendee(domain: string, folderId: string, redirectURL: string): Observable<any> {
        return this._httpClient.get(`${APIEndpoint.APP}/public/` + domain + '/' + folderId + '/sendMagicLinkForAttendee/' + redirectURL);
    }

    private setTokenData(response: any, resetImpersonate: boolean = true): void {
        if (!response.token) {
            // Required because sometimes a failed auth does not return an error (e.g. magic link)
            // in such case we risk putting the string "undefined" in the local storage
            // which cause refreshes doomed to fail
            this.removeAuthLocalStorage();
        } else {
            this.accessToken = response.token;
            this.refreshToken = response.refresh_token;
            // Store the access token in the local storage
            this.isAttendee = AuthUtils.getRoles(this.accessToken).includes('ROLE_ATTENDEE');
            this.isSales = AuthUtils.getRoles(this.accessToken).includes('ROLE_SALES');
            if (resetImpersonate) {
                this.impersonate(null);
            }
        }
    }

    private removeAuthLocalStorage(): void {
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        localStorage.removeItem('is_attendee');
        localStorage.removeItem('is_sales');
        localStorage.removeItem('switch_user');
    }

    redirectToAuth(requestedURL?: string, email?: string): boolean {
        const _requestedURL = 'redirectURL=' + encodeURIComponent(requestedURL);
        if (email) {
            email = (_requestedURL ? '&' : '') + 'email=' + encodeURIComponent(email);
        }
        if (requestedURL.includes('/apprenant')) {
            window.location.href = '/auth/apprenant/connexion?' + _requestedURL;
        } else if (requestedURL.includes('/candidat')) {
            window.location.href = '/auth/candidat/connexion?' + _requestedURL;
        } else {
            window.location.href = '/auth/connexion?' + _requestedURL + (email ?? '');
        }
        return true;
    }


    authWithIdDocument(file: any): Observable<CandidatePassportBody> {
        const formData: FormData = new FormData();
        formData.append('idDocument', file);
        return this._httpClient.post(`${APIEndpoint.APP}/public/identities/idDocument/upload`, formData).pipe(
            catchError((err) => {
                return throwError(err);
            }),
            tap((response: any) => {
            })
        );
    }

    authWithSocialSecurityCardDocument(file: any): Observable<CandidateSocialSecurityBody> {
        const formData: FormData = new FormData();
        formData.append('socialSecurityCard', file);
        return this._httpClient.post(`${APIEndpoint.APP}/public/identities/socialSecurityCard/upload`, formData).pipe(
            catchError((err) => {
                return throwError(err);
            }),
            tap((response: any) => {
            })
        );
    }
}
