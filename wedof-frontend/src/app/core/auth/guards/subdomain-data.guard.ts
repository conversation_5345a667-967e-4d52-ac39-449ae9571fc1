import {Injectable} from '@angular/core';
import {ActivatedRouteSnapshot, CanActivate, RouterStateSnapshot} from '@angular/router';
import {Observable, of} from 'rxjs';
import {Store} from '@ngxs/store';
import {mapTo} from 'rxjs/operators';
import {FetchSubdomainOrganism} from '../../../shared/api/state/organism.state';
import {TranslateService} from '@ngx-translate/core';
import {environment} from '../../../../environments/environment';

@Injectable({
    providedIn: 'root'
})
export class SubdomainDataGuard implements CanActivate {

    constructor(
        private _store: Store,
        private _translateService: TranslateService
    ) {
    }

    canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> {
        if (window.location.href.startsWith(environment.wedofBaseUri)) {
            return of(true);
        } else {
            return this._store.dispatch(new FetchSubdomainOrganism()).pipe(
                mapTo(true)
            );
        }
    }
}
