import {Injectable} from '@angular/core';
import {ActivatedRouteSnapshot, CanActivate, CanActivateChild, CanLoad, Route, Router, RouterStateSnapshot, UrlSegment, UrlTree} from '@angular/router';
import {Observable} from 'rxjs';
import {AuthService} from 'app/core/auth/auth.service';
import {map} from 'rxjs/operators';

@Injectable({
    providedIn: 'root'
})
export class NoAuthGuard implements CanActivate, CanActivateChild, CanLoad {

    constructor(
        private _authService: AuthService,
        private _router: Router
    ) {
    }

    canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> | Promise<boolean> | boolean {
        const requestedURL = route.queryParamMap.get('redirectURL');
        return this._check(requestedURL);
    }

    canActivateChild(childRoute: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
        return this._check(null);
    }

    canLoad(route: Route, segments: UrlSegment[]): Observable<boolean> | Promise<boolean> | boolean {
        return this._check(null);
    }

    private _check(requestedURL): Observable<boolean> {
        return this._authService.check()
            .pipe(
                map((isAuthenticated) => {
                    if (isAuthenticated) {
                        if (requestedURL) {
                            const urlParser = document.createElement('a');
                            urlParser.href = requestedURL;
                            if (urlParser.pathname.startsWith('/oauth2')) {
                                window.location.href = requestedURL + '&token=' + this._authService.accessToken;
                            } else {
                                window.location.href  = requestedURL;
                            }
                        } else {
                            this._router.navigate(['/', 'accueil']);
                        }
                        return false; // Prevent access
                    }
                    return true; // Allow access
                })
            );
    }
}
