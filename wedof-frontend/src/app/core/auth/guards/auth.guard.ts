import {Injectable} from '@angular/core';
import {
    ActivatedRouteSnapshot,
    CanActivate,
    CanActivateChild,
    CanLoad,
    Route,
    RouterStateSnapshot,
    UrlSegment,
    UrlTree
} from '@angular/router';
import {Observable} from 'rxjs';
import {AuthService} from 'app/core/auth/auth.service';
import {map} from 'rxjs/operators';

@Injectable({
    providedIn: 'root'
})
export class AuthGuard implements CanActivate, CanActivateChild, CanLoad {

    constructor(
        private _authService: AuthService
    ) {
    }

    canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> | Promise<boolean> | boolean {
        let requestedUrl = state.url;
        const email = route.queryParams['email'];
        if (requestedUrl.endsWith('deconnexion')) {
            requestedUrl = '/';
        }
        return this._check(requestedUrl, email);
    }

    canActivateChild(childRoute: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
        let requestedUrl = state.url;
        if (requestedUrl.endsWith('deconnexion')) {
            requestedUrl = '/';
        }
        return this._check(requestedUrl);
    }

    canLoad(route: Route, segments: UrlSegment[]): Observable<boolean> | Promise<boolean> | boolean {
        return this._check('/');
    }

    private _check(requestedURL: string, email?: string): Observable<boolean> {
        return this._authService.check()
            .pipe(
                map((isAuthenticated) => {
                    if (!isAuthenticated) {
                        return !this._authService.redirectToAuth(requestedURL, email);
                    }
                    return true; // Allow access
                })
            );
    }
}
