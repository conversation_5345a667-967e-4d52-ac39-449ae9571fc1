import {Injectable} from '@angular/core';
import {HttpErrorResponse, HttpEvent, HttpHandler, HttpInterceptor, HttpRequest} from '@angular/common/http';
import {BehaviorSubject, Observable, throwError} from 'rxjs';
import {catchError, concatMap, filter, finalize, take} from 'rxjs/operators';
import {AuthService} from 'app/core/auth/auth.service';
import {AuthUtils} from 'app/core/auth/auth.utils';
import {APIEndpoint} from 'app/shared/api/services/api-endpoint.enum';
import {isArray} from 'lodash-es';
import {ApiError} from '../../shared/errors/errors.types';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {

    isRefreshingToken = false;

    tokenRefreshed$ = new BehaviorSubject<boolean>(false);

    constructor(
        private _authService: AuthService
    ) {
    }

    intercept(req: HttpRequest<any>, next: <PERSON>ttpHand<PERSON>): Observable<HttpEvent<any>> {
        return next.handle(this.addToken(req)).pipe(
            catchError((error) => {
                if (error instanceof HttpErrorResponse) {
                    if (error.status === 401) {
                        return this.handle401Error(req, next);
                    } else if (error.status === 403
                        && this._authService.accessToken
                        && !this._authService.isAttendee
                        && error.url.endsWith('attendees/me')) {
                        this.handleSpecial403Error();
                    } else {
                        let details;
                        let errorMessages;
                        if (error.status === 0) {
                            details = 'Erreur, Wedof est inaccessible. Merci de vérifier votre connexion Internet.';
                        } else if (error.status > 499) {
                            details = 'Erreur serveur inattendue (statut ' + error.status + '). Merci de contacter nos équipes de support si elle persiste.';
                        } else {
                            details = error.error?.detail ?? 'Erreur inconnue (statut ' + error.status + '). Merci de contacter nos équipes de support si elle persiste.';
                        }
                        if (isArray(error.error?.violations) && error.error.violations.length) {
                            errorMessages = error.error.violations.map(violation => (`Erreur de validation sur le champ ${violation.propertyPath}: ${violation.title}`));
                        } else {
                            errorMessages = [details];
                        }
                        const apiError: ApiError = {
                            detail: details,
                            type: error.error?.type ?? 'unknown',
                            status: error.error?.status ?? error.status,
                            title: error.error?.title ?? error.statusText,
                            violations: error.error?.violations,
                            errorMessages: errorMessages
                        };
                        const newErrorResponse = new HttpErrorResponse({...error, error: apiError}); // Replace the error part of the HttpErrorResponse by an ApiError
                        console.error('HTTP ERROR', apiError, newErrorResponse);
                        return throwError(newErrorResponse);
                    }
                } else {
                    console.error('NOT HTTP ERROR', error);
                    return throwError(error);
                }
            })
        );
    }

    private isAuthentifiedUrl(url: string): boolean {
        return Object
            .keys(APIEndpoint)
            .map(k => `^${APIEndpoint[k]}/.*`)
            .some(exp => url.match(exp));
    }

    private addToken(req: HttpRequest<any>): HttpRequest<any> {
        if (this._authService.accessToken && !AuthUtils.isTokenExpired(this._authService.accessToken) && this.isAuthentifiedUrl(req.url)) {
            let newReq = req.clone({
                headers: req.headers.set('Authorization-JWT', 'Bearer ' + this._authService.accessToken)
            });
            if (this._authService.switchUser) {
                newReq = newReq.clone({
                    headers: newReq.headers.set('_SWITCH_USER', this._authService.switchUser)
                });
                newReq = newReq.clone({
                    headers: newReq.headers.set('Admin-View', this._authService.adminView)
                });
            }
            return newReq;
        }
        return req;
    }

    private handle401Error(req: HttpRequest<any>, next: HttpHandler): Observable<any> {
        if (this.isRefreshingToken) {
            if (req.url === '/api/refresh') {
                // Last resort hack to break the loop if while refreshing the token a call to refresh still fails (may happen with corrupted refresh token)
                // Sign out and force loading/reloading the connexion page
                this._authService.signOut().subscribe(() => {
                    this._authService.redirectToAuth();
                });
            } else {
                // Retry requests with proper auth
                return this.tokenRefreshed$.pipe(
                    filter(Boolean),
                    take(1),
                    concatMap(() => next.handle(this.addToken(req)))
                );
            }
        }
        this.isRefreshingToken = true;
        this.tokenRefreshed$.next(false);
        return this._authService.check().pipe(
            concatMap((isAuthenticated: boolean) => {
                if (isAuthenticated) {
                    this.tokenRefreshed$.next(true);
                    return next.handle(this.addToken(req));
                } else {
                    this.signOut();
                }
            }),
            catchError((err) => {
                this.signOut();
                return throwError(err);
            }),
            finalize(() => {
                this.isRefreshingToken = false;
            })
        );
    }

    private signOut(): void {
        this._authService.signOut().subscribe(() => {
            const path = location.pathname;
            if (!path.includes('/auth/connexion')) {
                this._authService.redirectToAuth(path);
            }
        });
    }

    private handleSpecial403Error(): void {
        window.location.href = '/403-authenticated-to-attendee';
    }
}
