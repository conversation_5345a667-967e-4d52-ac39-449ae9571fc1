import {Injectable} from '@angular/core';
import {ActivatedRouteSnapshot, Resolve, Router, RouterStateSnapshot} from '@angular/router';
import {EMPTY, forkJoin, of, throwError} from 'rxjs';
import {catchError, mapTo, switchMap} from 'rxjs/operators';

import {CertifierAccess} from '../../../shared/api/models/certifier-access';
import {Organism} from '../../../shared/api/models/organism';
import {CertifierAccessService} from '../../../shared/api/services/certifier-access.service';
import {OrganismService} from '../../../shared/api/services/organism.service';
import {AuthService} from '../auth.service';
import {HttpErrorResponse} from '@angular/common/http';

@Injectable(
    {providedIn: 'root'}
)
export class InvitationResolver implements Resolve<any> {

    constructor(
        private _router: Router,
        private _certifierAccessService: CertifierAccessService,
        private _organismService: OrganismService,
        private _authService: AuthService) {
    }

    resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): any {
        const token = route.queryParamMap.get('token') ?? route.paramMap.get('token');

        return this._certifierAccessService.getByToken(token).pipe(
            switchMap((certifierAccess: CertifierAccess) =>
                forkJoin([
                    // TODO optimize?
                    of(certifierAccess),
                    this._organismService.getPublic(certifierAccess._links.certifier.siret),
                    this._organismService.getPublic(certifierAccess._links.partner.siret)
                ])
            ),
            switchMap(([certifierAccess, invitingOrganism, organism]: [CertifierAccess, Organism, Organism]) => {
                if (organism.hasUsers) {
                    this._router.navigate(['/', 'formation', 'certifications'], {queryParams: {certifierAccess: certifierAccess.id, email: certifierAccess.email}});
                    return EMPTY;
                } else {
                    return this._authService.signOut().pipe(
                        mapTo({certifierAccess: certifierAccess, invitingOrganism, organism})
                    );
                }
            }),
            catchError((httpErrorResponse: HttpErrorResponse) => {
                if (httpErrorResponse.status === 404) {
                    this._router.navigate(['/', 'auth', 'connexion']);
                    return EMPTY;
                }
                return throwError(httpErrorResponse);
            })
        );
    }
}
