import {Injectable} from '@angular/core';
import {ActivatedRouteSnapshot, Resolve, Router, RouterStateSnapshot} from '@angular/router';
import {EMPTY, forkJoin, of} from 'rxjs';
import {catchError, switchMap} from 'rxjs/operators';
import {Organism} from '../../../shared/api/models/organism';
import {OrganismService} from '../../../shared/api/services/organism.service';
import {ProposalService} from '../../../shared/api/services/proposal.service';
import {FunnelService} from '../../../shared/api/services/funnel.service';

@Injectable(
    {providedIn: 'root'}
)
export class FunnelDataAuthResolver implements Resolve<any> {

    constructor(
        private _router: Router,
        private _proposalService: ProposalService,
        private _funnelService: FunnelService,
        private _organismService: OrganismService) {
    }

    resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): any {
        const code = route.queryParamMap.get('code') ?? route.paramMap.get('code');
        return this._organismService.getPublicBySubDomain().pipe(
            switchMap((organism: Organism) => {
                return forkJoin([
                    of(organism),
                    code ? this._proposalService.getThroughFunnel(code) : of({})
                ]);
            }),
            catchError(() => {
                this._router.navigate(['/404-not-found']);
                return EMPTY;
            })
        );
    }

    getOrganismName(): string {
        const domain = window.location.hostname;
        let subDomain = domain.split('.')[0];
        if (domain.indexOf('.') < 0 || ['localhost', 'wedof', 'www', 'staging.wedof'].includes(subDomain)) {
            subDomain = null;
        }
        return subDomain;
    }
}
