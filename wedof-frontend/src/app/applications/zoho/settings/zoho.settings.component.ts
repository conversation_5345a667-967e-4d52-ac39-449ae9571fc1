import {Component, Injector} from '@angular/core';
import {HttpErrorResponse} from '@angular/common/http';
import {AbstractApplicationSettingsComponent} from '../../shared/settings/abstract-application-settings.component';
import {N8nMetadata} from '../../shared/n8n-stuff';
import {OrganismApplication} from '../../../shared/api/models/organism-application';
import {Router} from '@angular/router';

@Component({
    selector: 'app-zoho-settings',
    templateUrl: './zoho.settings.component.html'
})
export class ZohoSettingsComponent extends AbstractApplicationSettingsComponent<N8nMetadata> {

    public authUrl: string = null;

    constructor(
        injector: Injector,
        _router: Router,
    ) {
        super('zoho', _router, injector);
    }

    onSuccess(organismApplication: OrganismApplication): void {
    }

    onError(httpErrorResponse: HttpErrorResponse): void {
    }

    protected initForm(metadata: N8nMetadata): void {
        if (metadata && !metadata.connected) {
            this._organismApplicationService.postData(this.application.appId(), 'authUrl', {}).subscribe(data => {
                this.authUrl = data.url;
            });
        }
    }

    setup(): void {
        this._organismApplicationService.postData(this.application.appId(), 'setup', {}).subscribe();
    }

    connect(): void {
        let popup;
        const service = this._organismApplicationService;
        const appId = this.application.appId();

        function resultOauth2Event(event): void {
            if (event.data && event.data === 'success') {
                window.removeEventListener('message', resultOauth2Event);
                popup.close();
                service.postData(appId, 'setup', {}).subscribe();
            }
        }

        window.removeEventListener('message', resultOauth2Event);
        window.addEventListener('message', resultOauth2Event, false);
        popup = window.open(this.authUrl, 'zoho_oauth2', 'menubar=no, status=no, menubar=no, width=500, height=600');
    }
}

