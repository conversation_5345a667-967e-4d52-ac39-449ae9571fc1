import {Application, staticImplements} from '../shared/application.interface';
import {application} from '../shared/application.decorator';
import {ZohoSettingsComponent} from './settings/zoho.settings.component';

@application()
@staticImplements<Application>()
export class ZohoApplication {
    public static appName(): string {
        return 'Zoho';
    }

    public static appId(): string {
        return 'zoho';
    }

    public static beta(): boolean {
        return true;
    }

    public static isSubscriptionOption(): boolean {
        return false;
    }

    public static hasTrialAvailable(): boolean {
        return false;
    }

    public static components(): any {
        return [
            ZohoSettingsComponent
        ];
    }

    public static routing(): any {
        return {
            path: ZohoApplication.appId(),
            children: [{
                path: 'reglages',
                component: ZohoSettingsComponent
            }]
        };
    }

    public static entryPoints(): any {
        return {};
    }

}
