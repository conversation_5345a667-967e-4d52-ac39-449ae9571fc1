import {Component, Injector} from '@angular/core';
import {Validators} from '@angular/forms';
import {Router} from '@angular/router';
import {HttpErrorResponse} from '@angular/common/http';
import {AbstractApplicationSettingsComponent} from '../../shared/settings/abstract-application-settings.component';
import {OrganismApplication} from '../../../shared/api/models/organism-application';
import {ZapierMetadata} from '../zapier.application';

@Component({
    selector: 'zapier-settings',
    templateUrl: './zapier.settings.component.html',
    styleUrls: ['./zapier.settings.component.scss']
})
export class ZapierSettingsComponent extends AbstractApplicationSettingsComponent<ZapierMetadata> {

    LINK_ZAPIER = 'https://zapier.com/developer/public-invite/115151/5d22201993587527ff5ecaba1a9fcff8/';

    constructor(
        _router: Router,
        injector: Injector,
    ) {
        super('zapier', _router, injector);
    }

    onSuccess(organismApplication: OrganismApplication): void {
        this._router.navigate(['mes-applications']);
    }

    onError(httpErrorResponse: HttpErrorResponse): void {
    }

    protected initForm(metadata: ZapierMetadata): void {
        this.formGroup = this._fb.group({
            name: [metadata?.name, [Validators.required]]
        });
    }
}
