import {Application, staticImplements} from '../shared/application.interface';
import {ZapierSettingsComponent} from './settings/zapier.settings.component';
import {application} from '../shared/application.decorator';

@application()
@staticImplements<Application>()
export class ZapierApplication {
    public static appName(): string {
        return 'Zapier';
    }

    public static appId(): string {
        return 'zapier';
    }


    public static isSubscriptionOption(): boolean {
        return false;
    }

    public static hasTrialAvailable(): boolean {
        return false;
    }

    public static beta(): boolean {
        return false;
    }

    public static components(): any {
        return [
            ZapierSettingsComponent
        ];
    }

    public static priority(): number {
        return 801;
    }

    public static routing(): any {
        return {
            path: ZapierApplication.appId(),
            children: [{
                path: 'reglages',
                component: ZapierSettingsComponent
            }]
        };
    }

    public static entryPoints(): any {
        return {};
    }

}

export interface ZapierMetadata {
    name: string;
}
