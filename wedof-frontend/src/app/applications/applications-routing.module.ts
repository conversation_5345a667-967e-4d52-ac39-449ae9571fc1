import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {ApplicationsListComponent} from '../private/application/applications-list.component';
import {applicationsRouting} from './shared/application.decorator';
import {OauthCodeManagementComponent} from './shared/oauth-management/oauth-code-management.component';

const routes: Routes = [{
    path: '',
    component: ApplicationsListComponent
}, {
    path: 'oauth/:id',
    component: OauthCodeManagementComponent
},
    ...applicationsRouting()
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule],
})
export class ApplicationsRoutingModule {
}

