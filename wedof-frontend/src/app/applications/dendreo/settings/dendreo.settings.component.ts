import {Component, Injector} from '@angular/core';
import {HttpErrorResponse} from '@angular/common/http';
import {AbstractApplicationSettingsComponent} from '../../shared/settings/abstract-application-settings.component';
import {N8nMetadata} from '../../shared/n8n-stuff';
import {OrganismApplication} from '../../../shared/api/models/organism-application';
import {Router} from '@angular/router';

@Component({
    selector: 'app-dendreo-settings',
    templateUrl: './dendreo.settings.component.html'
})
export class DendreoSettingsComponent extends AbstractApplicationSettingsComponent<N8nMetadata> {

    constructor(
        injector: Injector,
        _router: Router,
    ) {
        super('dendreo', _router, injector);
    }

    onSuccess(organismApplication: OrganismApplication): void {
    }

    onError(httpErrorResponse: HttpErrorResponse): void {
    }

    protected initForm(metadata: N8nMetadata): void {
    }
}

