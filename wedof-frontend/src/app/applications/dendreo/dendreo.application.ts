import {Application, staticImplements} from '../shared/application.interface';
import {application} from '../shared/application.decorator';
import {DendreoSettingsComponent} from './settings/dendreo.settings.component';

@application()
@staticImplements<Application>()
export class DendreoApplication {
    public static appName(): string {
        return 'Dendreo';
    }

    public static appId(): string {
        return 'dendreo';
    }

    public static beta(): boolean {
        return false;
    }

    public static isSubscriptionOption(): boolean {
        return false;
    }

    public static hasTrialAvailable(): boolean {
        return false;
    }

    public static components(): any {
        return [
            DendreoSettingsComponent
        ];
    }

    public static priority(): number {
        return 700;
    }

    public static routing(): any {
        return {
            path: DendreoApplication.appId(),
            children: [{
                path: 'reglages',
                component: DendreoSettingsComponent
            }]
        };
    }

    public static entryPoints(): any {
        return {};
    }

}
