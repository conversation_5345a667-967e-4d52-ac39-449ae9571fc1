import {Application, staticImplements} from '../shared/application.interface';
import {application} from '../shared/application.decorator';
import {HubspotSettingsComponent} from './settings/hubspot.settings.component';

@application()
@staticImplements<Application>()
export class HubspotApplication {
    public static appName(): string {
        return 'Hubspot';
    }

    public static appId(): string {
        return 'hubspot';
    }

    public static beta(): boolean {
        return true;
    }

    public static isSubscriptionOption(): boolean {
        return false;
    }

    public static hasTrialAvailable(): boolean {
        return false;
    }

    public static components(): any {
        return [
            HubspotSettingsComponent
        ];
    }

    public static routing(): any {
        return {
            path: HubspotApplication.appId(),
            children: [{
                path: 'reglages',
                component: HubspotSettingsComponent
            }]
        };
    }

    public static entryPoints(): any {
        return {};
    }

}
