import {Component, Injector} from '@angular/core';
import {Validators} from '@angular/forms';
import {HttpErrorResponse} from '@angular/common/http';
import {OrganismApplication} from '../../../shared/api/models/organism-application';
import {AbstractApplicationSettingsComponent} from '../../shared/settings/abstract-application-settings.component';
import {Router} from '@angular/router';

@Component({
    selector: 'app-ringover-settings',
    templateUrl: './ringover-settings.component.html'
})

export class RingoverSettingsComponent extends AbstractApplicationSettingsComponent<RingoverMetadata> {

    private static appId = 'ringover';
    public numbers: [];
    public status = null;

    constructor(
        injector: Injector,
        _router: Router,
    ) {
        super(RingoverSettingsComponent.appId, _router, injector);
    }

    onSuccess(organismApplication: OrganismApplication): void {
    }

    onError(httpErrorResponse: HttpErrorResponse): void {
    }

    protected initForm(metadata: RingoverMetadata): void {
        this.formGroup = this._fb.group({
            apiKey: ['', [Validators.required, Validators.minLength(40), Validators.maxLength(40)]],
            phoneNumber: [metadata?.phoneNumber, [Validators.required]]
        });
        this.formGroup.get('apiKey').valueChanges.subscribe(apiKey => {
            if (apiKey && apiKey.length === 40) {
                this._organismApplicationService.postData(RingoverSettingsComponent.appId, 'phoneNumbers', {apiKey: apiKey}).subscribe(
                    (data) => {
                        this.status = true;
                        this.numbers = data;
                    },
                    () => {
                        this.status = false;
                    }
                );
            }
        });
        if (metadata?.apiKey) {
            this.formGroup.get('apiKey').setValue(metadata.apiKey);
        }
    }

    test(): void {
        const to = prompt('Enter un numéro de portable au format +33612345678', '');
        if (to) {
            this._organismApplicationService.postData(RingoverSettingsComponent.appId, 'test', {to: to}).subscribe();
        }
    }
}

export interface RingoverMetadata {
    apiKey: string;
    phoneNumber: string;
}
