<app-application-settings (formSubmitted)="submit()" [application]="application">
    <div [formGroup]="formGroup">
        <mat-form-field class="w-1/2 pr-2">
            <mat-label>{{ 'private.application.ringover.form.apikey.label' | translate}}</mat-label>
            <input formControlName="apiKey"
                   matInput
                   [type]="formGroup.get('phoneNumber') ? 'password' : 'text'"
                   #passwordField>
            <button mat-icon-button
                    (click)="passwordField.type === 'password' ? passwordField.type = 'text' : passwordField.type = 'password'"
                    tabindex="-1"
                    type="button"
                    matSuffix>
                <mat-icon *ngIf="passwordField.type === 'password'" svgIcon="visibility"></mat-icon>
                <mat-icon *ngIf="passwordField.type === 'text'" svgIcon="visibility_off"></mat-icon>
            </button>
        </mat-form-field>
        <ng-container *ngIf="status else invalidApiOrRights">
            <mat-form-field [formGroup]="formGroup" appearance="fill" class="pl-2 w-1/2">
                <mat-label>{{ 'private.application.ringover.form.phoneNumber.label' | translate}}</mat-label>
                <mat-select formControlName="phoneNumber"
                            (closed)="submit()">
                    <mat-option *ngFor="let number of numbers" [value]="number">
                        {{number}}
                    </mat-option>
                </mat-select>
            </mat-form-field>
        </ng-container>
    </div>
</app-application-settings>
<ng-template #invalidApiOrRights>
    <treo-message type="warning" [showIcon]="false" class="mb-4 w-1/2 pr-2" appearance="outline" *ngIf="status != null">
        <div class="flex m-2">
            <mat-icon class="mr-4 self-center" [svgIcon]="'warning'"></mat-icon>
            <div>{{ 'private.application.ringover.apikey.invalid' }}</div>
        </div>
    </treo-message>
</ng-template>
