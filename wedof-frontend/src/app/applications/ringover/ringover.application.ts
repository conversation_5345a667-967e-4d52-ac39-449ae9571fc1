import {Application, staticImplements} from '../shared/application.interface';
import {application} from '../shared/application.decorator';
import {RingoverSettingsComponent} from './settings/ringover-settings.component';

@application()
@staticImplements<Application>()
export class RingoverApplication {
    public static appName(): string {
        return 'Ringover';
    }

    public static appId(): string {
        return 'ringover';
    }

    public static beta(): boolean {
        return true;
    }

    public static isSubscriptionOption(): boolean {
        return false;
    }

    public static hasTrialAvailable(): boolean {
        return false;
    }

    public static components(): any {
        return [
            RingoverSettingsComponent
        ];
    }

    public static routing(): any {
        return {
            path: RingoverApplication.appId(),
            children: [{
                path: 'reglages',
                component: RingoverSettingsComponent
            }]
        };
    }

    public static entryPoints(): any {
        return {};
    }

}
