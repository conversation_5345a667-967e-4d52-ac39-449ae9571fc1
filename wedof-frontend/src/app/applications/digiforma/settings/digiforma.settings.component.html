<app-application-settings (formSubmitted)="submit()" *ngIf="metadata | async"
                          [loading]="loading"
                          [application]="application"
                          [submitButton]="getSubmitButton()">
    <div *ngIf="metadata | async" class="flex flex-row items-center mb-4">
        <div class="font-medium leading-tight">
            {{ (this.status ? 'private.application.digiforma.synchronization.status.success' : 'private.application.digiforma.synchronization.status.error') | translate:metadata.value }}
        </div>
        <mat-icon [ngClass]="{'text-green':status, 'text-red':!status}" class="ml-1"
                  svgIcon="{{ status ? 'check_circle' : 'cancel' }}"></mat-icon>
    </div>
    <div [formGroup]="formGroup">
        <p class="text-lg font-medium mb-3" *ngIf="!status">{{ 'private.application.digiforma.description' | translate}}
            <a
                [href]="LINK_DIGIFORMA" rel="noopener noreferrer"
                target="_blank">{{'private.application.digiforma.link' | translate}}</a></p>
        <app-form-fields class="grid grid-cols-6 gap-2"
                         formGroupName="digiformaMetadata"
                         [entity]="digiformaMetadata"
                         [appFormFieldsData]="appFormFieldsData[0]"
                         [formGroup]="formGroup"></app-form-fields>
        <p *ngIf="status && isReadyPipeline" class="text-lg font-medium mb-3">Configuration de la facturation</p>
        <app-form-fields class="grid grid-cols-6 gap-2"
                         *ngIf="status && isReadyPipeline"
                         formGroupName="digiformaMetadata"
                         [entity]="digiformaMetadata"
                         [appFormFieldsData]="appFormFieldsData[1]"
                         [formGroup]="formGroup"></app-form-fields>
        <p *ngIf="status && isReadyPipeline" class="text-lg font-medium my-3">Configuration des statuts</p>
        <app-form-fields class="grid grid-cols-6 gap-2"
                         *ngIf="status && isReadyPipeline"
                         formGroupName="digiformaMetadata"
                         [entity]="digiformaMetadata"
                         [appFormFieldsData]="appFormFieldsData[2]"
                         [formGroup]="formGroup"></app-form-fields>
    </div>
</app-application-settings>
