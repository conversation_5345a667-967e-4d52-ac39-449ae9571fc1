import {Component, Injector} from '@angular/core';
import {HttpErrorResponse} from '@angular/common/http';
import {AbstractApplicationSettingsComponent} from '../../shared/settings/abstract-application-settings.component';
import {OrganismApplication} from '../../../shared/api/models/organism-application';
import {DigiformaMetadata, DigiformaMetadataResponse, DigiformaPipelineStateCustomer} from '../digiforma.application';
import {SubmitSettingsButton} from '../../shared/settings/application-settings.component';
import {SnackBarComponent} from '../../../shared/material/snack-bar/snack-bar.component';
import {displayTextSnackBar} from '../../../shared/utils/displayTextSnackBar';
import {MatSnackBar} from '@angular/material/snack-bar';
import {TranslateService} from '@ngx-translate/core';
import {AppFormFieldData} from '../../../shared/material/app-form-field/app-form-field.component';
import {getRegistrationFolderAllStates} from '../../../shared/utils/states-utils';
import {ApiError} from '../../../shared/errors/errors.types';
import {Router} from '@angular/router';

@Component({
    selector: 'app-digiforma-settings',
    templateUrl: './digiforma.settings.component.html'
})
export class DigiformaSettingsComponent extends AbstractApplicationSettingsComponent<DigiformaMetadataResponse> {

    LINK_DIGIFORMA = 'https://app.digiforma.com/academy/interconnection/zapier/';

    status: boolean;
    ready: boolean;
    digiformaMetadata: DigiformaMetadata;
    appFormFieldsData: AppFormFieldData[][];
    pipelineStates: DigiformaPipelineStateCustomer[];
    isReadyPipeline: boolean;

    constructor(
        injector: Injector,
        _router: Router,
        private _snackBar: MatSnackBar,
        private _translateService: TranslateService
    ) {
        super('digiforma', _router, injector);
        this.isReadyPipeline = false;
        this.status = false;
    }

    onSuccess(organismApplication: OrganismApplication): void {
        const digiformaMetadataResponse: DigiformaMetadataResponse = organismApplication.metadata as DigiformaMetadataResponse;
        if (!this.isReadyPipeline) {
            this.initForm(digiformaMetadataResponse);
        }
    }

    submit(): void {
        this.loading = true;
        const isPasswordDirty: boolean = this.formGroup.get('digiformaMetadata.password').dirty;

        this._organismApplicationService.postData(this.application.appId(), 'checkCredentials', {
            ...this.formGroup.get('digiformaMetadata').value,
            isPasswordDirty
        }).subscribe((data) => {
            if (data) {
                super.submit();
            }
            const text = 'private.application.digiforma.valid-credentials';
            this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(this._translateService.instant(text), 5000, 'green'));
            this.loading = false;
        }, (httpErrorResponse: HttpErrorResponse) => {
            this.status = false;
            this.isReadyPipeline = false;
            this.loading = false;
            const errorCode = (httpErrorResponse.error as ApiError).detail;
            if (errorCode === 'start-api-trial' || errorCode === 'no-api') {
                const text = 'private.application.digiforma.' + errorCode;
                this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(this._translateService.instant(text), 5000, 'red'));
            } else {
                const text = 'private.application.digiforma.invalid-credentials';
                this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(this._translateService.instant(text), 5000, 'red'));
            }
        });
    }

    onError(httpErrorResponse: HttpErrorResponse): void {
    }

    getSubmitButton(): SubmitSettingsButton {
        return {
            color: 'primary',
            value: 'Valider',
            disabled: !this.formGroup.valid || !this.formGroup.dirty
        };
    }

    protected initForm(metadata: DigiformaMetadataResponse): void {
        if (!metadata) {
            return;
        }

        this.digiformaMetadata = metadata.digiformaMetadata;

        this.formGroup = this._fb.group({
            digiformaMetadata: this._fb.group({})
        });

        const appFormFieldsData: AppFormFieldData[][] = [];

        appFormFieldsData[0] = [
            {
                controlName: 'username',
                required: true,
                label: 'private.application.digiforma.username',
                type: 'text',
                colSpan: 3,
                placeholder: 'private.application.digiforma.placeholder.username'
            },
            {
                controlName: 'password',
                required: true,
                label: 'private.application.digiforma.password',
                type: 'password',
                colSpan: 3,
                placeholder: 'private.application.digiforma.placeholder.password'
            },
        ];

        const registrationFolderAllStates = getRegistrationFolderAllStates();

        if (this.digiformaMetadata) {
            this._organismApplicationService.postData(this.application.appId(), 'checkState', {}).subscribe((data) => {
                this.status = data;
                if (this.status) {
                    this._organismApplicationService.postData(this.application.appId(), 'getPipelineStates', {}).subscribe((result) => {
                        this.pipelineStates = result.data.pipelineStates;
                        appFormFieldsData[1] = [
                            {
                                controlName: 'process_invoice',
                                // removed: !this.status, avec this.formGroup.valid peutêtre ?
                                // required: this.status,
                                label: 'private.application.digiforma.process_invoice',
                                type: 'checkbox',
                            },
                        ];
                        appFormFieldsData[2] = [];
                        registrationFolderAllStates.forEach(registrationFolderState => {
                            appFormFieldsData[2].push(
                                {
                                    controlName: registrationFolderState.value,
                                    removable: true,
                                    placeholder: this._translateService.instant('private.application.digiforma.default_configuration') + ' : '
                                        + this.getPlaceholderRegistrationFolderStates(registrationFolderState.value, this.pipelineStates),
                                    label: this._translateService.instant(registrationFolderState.label),
                                    type: 'select',
                                    colSpan: 2,
                                    choices: Object.values(this.pipelineStates).map((pipelineState) => ({
                                        key: pipelineState.state,
                                        value: pipelineState.position
                                    }))
                                }
                            );
                        });
                        this.isReadyPipeline = true;
                    });
                }
            });
        }

        this.ready = true;
        this.appFormFieldsData = appFormFieldsData;
    }

    getPlaceholderRegistrationFolderStates(registrationFolderState: string, pipelinesState: DigiformaPipelineStateCustomer[]): string {
        switch (registrationFolderState) {
            case 'validated' :
            case 'waitingAcceptation' :
                return pipelinesState[2].state;
            case 'accepted' :
            case 'inTraining' :
            case 'terminated' :
                return pipelinesState[3].state;
            case 'serviceDoneDeclared' :
            case 'serviceDoneValidated' :
            case 'canceledByAttendee' :
            case 'canceledByAttendeeNotRealized' :
                return pipelinesState[4].state;
            case 'notProcessed' :
                return pipelinesState[0].state;
            case 'canceledByOrganism' :
            case 'refusedByAttendee' :
            case 'refusedByOrganism' :
            case 'rejectedWithoutTitulaireSuite' :
            case 'rejected' :
            default :
                return pipelinesState[1].state;
        }
    }
}

