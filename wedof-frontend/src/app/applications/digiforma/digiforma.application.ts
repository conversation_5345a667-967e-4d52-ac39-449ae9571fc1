import {Application, staticImplements} from '../shared/application.interface';
import {application} from '../shared/application.decorator';
import {DigiformaSettingsComponent} from './settings/digiforma.settings.component';

@application()
@staticImplements<Application>()
export class DigiformaApplication {
    public static appName(): string {
        return 'Digiforma';
    }

    public static appId(): string {
        return 'digiforma';
    }

    public static beta(): boolean {
        return false;
    }

    public static isSubscriptionOption(): boolean {
        return false;
    }

    public static hasTrialAvailable(): boolean {
        return false;
    }

    public static components(): any {
        return [
            DigiformaSettingsComponent
        ];
    }

    public static routing(): any {
        return {
            path: DigiformaApplication.appId(),
            children: [{
                path: 'reglages',
                component: DigiformaSettingsComponent
            }]
        };
    }

    public static entryPoints(): any {
        return {};
    }

}

export interface DigiformaMetadata {
    token: string;
    process_invoice: boolean;
    username: string;
    password: string;
}

export interface DigiformaMetadataResponse {
    digiformaMetadata: DigiformaMetadata;
}

export interface DigiformaPipelineStateCustomer {
    position: string;
    state: string;
}
