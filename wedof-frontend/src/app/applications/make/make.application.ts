import {Application, staticImplements} from '../shared/application.interface';
import {MakeSettingsComponent} from './settings/make.settings.component';
import {application} from '../shared/application.decorator';

@application()
@staticImplements<Application>()
export class MakeApplication {
    public static appName(): string {
        return 'Make';
    }

    public static appId(): string {
        return 'make';
    }

    public static beta(): boolean {
        return false;
    }

    public static isSubscriptionOption(): boolean {
        return false;
    }
    public static components(): any {
        return [
            MakeSettingsComponent
        ];
    }

    public static hasTrialAvailable(): boolean {
        return false;
    }

    public static priority(): number {
        return 802;
    }

    public static routing(): any {
        return {
            path: MakeApplication.appId(),
            children: [{
                path: 'reglages',
                component: MakeSettingsComponent
            }]
        };
    }

    public static entryPoints(): any {
        return {};
    }
}

export interface MakeMetadata {
    name: string;
}
