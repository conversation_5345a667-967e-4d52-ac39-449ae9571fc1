import {Component, Injector} from '@angular/core';
import {Validators} from '@angular/forms';
import {Router} from '@angular/router';
import {HttpErrorResponse} from '@angular/common/http';
import {AbstractApplicationSettingsComponent} from '../../shared/settings/abstract-application-settings.component';
import {OrganismApplication} from '../../../shared/api/models/organism-application';
import {MakeMetadata} from '../make.application';

@Component({
    selector: 'make-settings',
    templateUrl: './make.settings.component.html',
    styleUrls: ['./make.settings.component.scss']
})
export class MakeSettingsComponent extends AbstractApplicationSettingsComponent<MakeMetadata> {

    LINK_MAKE = 'https://www.make.com/en/hq/app-invitation/4b78a093f2f13a3057e64bc23522c79d';

    constructor(
        _router: Router,
        injector: Injector,
    ) {
        super('make', _router, injector);
    }

    onSuccess(organismApplication: OrganismApplication): void {
        this._router.navigate(['mes-applications']);
    }

    onError(httpErrorResponse: HttpErrorResponse): void {
    }

    protected initForm(metadata: MakeMetadata): void {
        this.formGroup = this._fb.group({
            name: [metadata?.name, [Validators.required]]
        });
    }
}
