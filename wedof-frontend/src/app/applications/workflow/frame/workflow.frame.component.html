<ng-container
    *ngIf="workflowApplication && workflowApplicationAuth && workflowApplicationAuth.jwt && workflowApplication.enabled else notEnabled">
    <iframe [src]="getFrameUrl()" class="w-full"></iframe>
</ng-container>
<ng-template #notEnabled>
    <ng-container *ngIf="(displayShowEnableApp == null || displayShowTakeSubscription == null) else subscription">
        <div class="m-auto py-24">
            <mat-progress-spinner class="mr-4" [diameter]="75" mode="indeterminate"></mat-progress-spinner>
        </div>
    </ng-container>
    <ng-template #subscription>
        <ng-container *ngIf="displayShowEnableApp">
            <app-subscription-card
                [subtitle]="'private.layout.user.subscription.apps.workflow.subtitle'"
                [explanation]="'private.layout.user.subscription.apps.workflow.explanation'"
                [description1]="'private.layout.user.subscription.apps.workflow.description.description1'"
                [description2]="'private.layout.user.subscription.apps.workflow.description.description2' "
                [description3]="'private.layout.user.subscription.apps.workflow.description.description3'"
                [description4]="'private.layout.user.subscription.apps.workflow.description.description4'"
                [description5]="'private.layout.user.subscription.apps.workflow.description.description5'"
                [image]="'assets/images/subscription/workflow.png'"
                [certifierSubscription]="false"
                [trainingSubscription]="false"
                [enableAppId]="'workflow'"
            ></app-subscription-card>
        </ng-container>
        <ng-container *ngIf="displayShowTakeSubscription">
            <app-subscription-card
                [subtitle]="'private.layout.user.subscription.apps.workflow.subtitle'"
                [explanation]="'private.layout.user.subscription.apps.workflow.explanation'"
                [description1]="'private.layout.user.subscription.apps.workflow.description.description1'"
                [description2]="'private.layout.user.subscription.apps.workflow.description.description2' "
                [description3]="'private.layout.user.subscription.apps.workflow.description.description3'"
                [description4]="'private.layout.user.subscription.apps.workflow.description.description4'"
                [description5]="'private.layout.user.subscription.apps.workflow.description.description5'"
                [image]="'assets/images/subscription/workflow.png'"
                [certifierSubscription]="organism.isCertifierOrganism"
                [trainingSubscription]="organism.isTrainingOrganism"
            ></app-subscription-card>
        </ng-container>
    </ng-template>
</ng-template>
