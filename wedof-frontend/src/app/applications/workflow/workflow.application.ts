import {Application, staticImplements} from '../shared/application.interface';
import {WorkflowSettingsComponent} from './settings/workflow.settings.component';
import {application} from '../shared/application.decorator';
import {WorkflowFrameComponent} from './frame/workflow.frame.component';
import {WorkflowRunsCardComponent} from '../../shared/sides/workflow-runs-card/workflow-runs-card.component';

@application()
@staticImplements<Application>()
export class WorkflowApplication {
    public static appName(): string {
        return 'Processus Métiers Automatisés';
    }

    public static appId(): string {
        return 'workflow';
    }

    public static beta(): boolean {
        return false;
    }

    public static isSubscriptionOption(): boolean {
        return true;
    }

    public static hasTrialAvailable(): boolean {
        return true;
    }

    public static components(): any {
        return [
            WorkflowSettingsComponent,
            WorkflowFrameComponent,
            WorkflowRunsCardComponent
        ];
    }

    public static priority(): number {
        return 998;
    }

    public static routing(): any {
        return {
            path: WorkflowApplication.appId(),
            children: [{
                path: 'reglages',
                component: WorkflowSettingsComponent
            },
                {
                    path: 'app',
                    component: WorkflowFrameComponent
                }]
        };
    }

    public static entryPoints(): any {
        return {
            'card-registration-folder-side': [{
                tpl: 'tpl-workflow-runs-card-registration-folder-side',
                title: 'private.common.shortcut.workflowRuns',
                shortcut: {
                    title: 'private.common.shortcut.workflowRuns',
                    target: WorkflowRunsCardComponent.COMPONENT_ID,
                    icon: 'play_arrow',
                    visible: true
                }
            }],
            'card-certification-folder-side': [{
                tpl: 'tpl-workflow-runs-card-certification-folder-side',
                title: 'private.common.shortcut.workflowRuns',
                shortcut: {
                    title: 'private.common.shortcut.workflowRuns',
                    target: WorkflowRunsCardComponent.COMPONENT_ID,
                    icon: 'play_arrow',
                    visible: true
                }
            }]
        };
    }

}

export interface WorkflowMetadata {
    name: string;
}
