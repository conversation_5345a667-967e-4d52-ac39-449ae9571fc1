import {Component, Injector} from '@angular/core';
import {Validators} from '@angular/forms';
import {Router} from '@angular/router';
import {HttpErrorResponse} from '@angular/common/http';
import {AbstractApplicationSettingsComponent} from '../../shared/settings/abstract-application-settings.component';
import {OrganismApplication} from '../../../shared/api/models/organism-application';
import {WorkflowMetadata} from '../workflow.application';
import {Select, Store} from '@ngxs/store';
import {combineLatest, Observable, Subject} from 'rxjs';
import {takeUntil} from 'rxjs/operators';
import {OrganismState} from '../../../shared/api/state/organism.state';
import {Organism} from '../../../shared/api/models/organism';

@Component({
    selector: 'workflow-settings',
    templateUrl: './workflow.settings.component.html',
    styleUrls: ['./workflow.settings.component.scss']
})
export class WorkflowSettingsComponent extends AbstractApplicationSettingsComponent<WorkflowMetadata> {

    constructor(
        _router: Router,
        injector: Injector,
        _store: Store,
    ) {
        super(WorkflowSettingsComponent.appId, _router, injector);
        this._store = _store;
    }
    private static appId = 'workflow';

    organism: Organism = null;

    @Select(OrganismState.organism) organism$: Observable<Organism>;
    private _unsubscribeAll = new Subject<void>();
    private _store: Store;

    onSuccess(organismApplication: OrganismApplication): void {
        this._router.navigate(['mes-applications']);
    }

    onError(httpErrorResponse: HttpErrorResponse): void {
    }

    protected initForm(metadata: WorkflowMetadata): void {
        combineLatest([
            this.organism$,
        ]).pipe(takeUntil(this._unsubscribeAll)).subscribe(([organism]) => {
            this.loading = false;
            this.organism = organism;
            this.formGroup = this._fb.group({
                name: [metadata?.name, [Validators.required]]
            });
        });
    }
}
