import {Application, staticImplements} from '../shared/application.interface';
import {application} from '../shared/application.decorator';
import {StatusSettingsComponent} from './settings/status.settings.component';

@application()
@staticImplements<Application>()
export class StatusApplication {
    public static appName(): string {
        return 'Suivi des incidents';
    }

    public static appId(): string {
        return 'status';
    }

    public static beta(): boolean {
        return false;
    }

    public static isSubscriptionOption(): boolean {
        return false;
    }

    public static hasTrialAvailable(): boolean {
        return false;
    }

    public static components(): any {
        return [
            StatusSettingsComponent
        ];
    }

    public static routing(): any {
        return {
            path: StatusApplication.appId(),
            children: [{
                path: 'reglages',
                component: StatusSettingsComponent
            }]
        };
    }

    public static entryPoints(): any {
        return {};
    }

}
