import {Component, Injector, OnInit} from '@angular/core';
import {HttpErrorResponse} from '@angular/common/http';
import {AbstractApplicationSettingsComponent} from '../../shared/settings/abstract-application-settings.component';
import {OrganismApplication} from '../../../shared/api/models/organism-application';
import {AppFormFieldData} from '../../../shared/material/app-form-field/app-form-field.component';
import {SubmitSettingsButton} from '../../shared/settings/application-settings.component';
import {Select} from '@ngxs/store';
import {UserState} from '../../../shared/api/state/user.state';
import {Observable} from 'rxjs';
import {User} from '../../../shared/api/models/user';
import {TranslateService} from '@ngx-translate/core';
import {Router} from '@angular/router';

@Component({
    selector: 'app-status-settings',
    templateUrl: './status.settings.component.html',
    styleUrls: ['./status.settings.component.scss']
})
export class StatusSettingsComponent extends AbstractApplicationSettingsComponent<StatusMetadata> implements OnInit {

    components: any;
    appFormFieldsGroupData: AppFormFieldData[][];
    @Select(UserState.user) user$: Observable<User>;
    user: User;

    constructor(
        private injector: Injector,
        _router: Router,
        private _translateService: TranslateService
    ) {
        super('status', _router, injector);
    }

    onSuccess(organismApplication: OrganismApplication): void {
    }

    onError(httpErrorResponse: HttpErrorResponse): void {
    }

    ngOnInit(): void {
        super.ngOnInit();
        this.user$.subscribe(user => this.user = user);
    }

    protected initForm(metadata: StatusMetadata): void {
        this._organismApplicationService.postData(this.appId, 'components', {}).subscribe((components) => {
            this.formGroup = this._fb.group({
                settings: this._fb.group({})
            });
            const settings = metadata?.settings ?? {email: null};
            const appFormFieldsData: AppFormFieldData[][] = [];

            components.forEach(component => {
                const name = component.group_name ?? 'no_group';
                if (!appFormFieldsData[name]) {
                    appFormFieldsData[name] = [];
                }
                const status = component.status === 1 ? 'green' : (component.status === 2 || component.status === 3 ? 'orange' : 'red');
                const statusText = component.status_name;
                appFormFieldsData[name].push({
                    controlName: 'component-' + component.id,
                    label: this._translateService.instant('private.application.status.badge', {
                        status: status,
                        statusText: statusText,
                        component: component.name
                    }),
                    type: 'checkbox',
                    value: settings['component-' + component.id] ?? false,
                    colSpan: 3
                });
            });

            appFormFieldsData['form'] = [
                {
                    controlName: 'email',
                    type: 'email',
                    required: true,
                    icon: 'email',
                    value: settings?.email ?? this.user.email,
                    colSpan: 3
                }
            ];
            this.appFormFieldsGroupData = appFormFieldsData; // Hack: do it in two steps to avoid complex typescript error
        });
    }

    getSubmitButton(): SubmitSettingsButton {
        return {
            color: 'primary',
            value: 'Sauvegarder',
            disabled: !this.formGroup.valid
        };
    }

    filter(appFormFieldsGroupData: AppFormFieldData[][]): any {
        const keys = Object.keys(appFormFieldsGroupData);
        return Object.keys(appFormFieldsGroupData)
            .filter((key) => !['form', 'no_group'].includes(key))
            .reduce((cur, key) => {
                return Object.assign(cur, {[key]: appFormFieldsGroupData[key]});
            }, {});
    }
}

export interface StatusMetadata {
    settings: {
        email: string;
        components: StatusComponent[]
    };
}

interface StatusComponent {
    id: number;
    name: string;
    status: string;
    enabled: boolean;
    group_name?: string;
    status_name: string;
}
