<app-application-settings
    [application]="application"
    (formSubmitted)="submit()"
    [submitButton]="getSubmitButton()"
    [loading]="loading"
    *ngIf="metadata | async">
    <div [formGroup]="formGroup" *ngIf="appFormFieldsGroupData">
        <app-form-fields *ngIf="appFormFieldsGroupData['no_group']"
                         formGroupName="settings"
                         class="grid grid-cols-6 gap-2"
                         [appFormFieldsData]="appFormFieldsGroupData['no_group']"
                         [formGroup]="formGroup">
        </app-form-fields>
        <div *ngFor="let appFormFieldsGroup of filter(appFormFieldsGroupData) | keyvalue">
            <h5>{{ appFormFieldsGroup.key }}</h5>
            <app-form-fields formGroupName="settings"
                             class="grid grid-cols-6 gap-2"
                             [appFormFieldsData]="appFormFieldsGroup.value"
                             [formGroup]="formGroup">
            </app-form-fields>
        </div>
        <div>
            <h5>{{ 'private.application.status.form.email.label' | translate}}</h5>
            <app-form-fields formGroupName="settings"
                             class="grid grid-cols-6 gap-2"
                             [appFormFieldsData]="appFormFieldsGroupData['form']"
                             [formGroup]="formGroup">
            </app-form-fields>
        </div>
    </div>
</app-application-settings>
