import {Component, Inject, OnInit} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material/dialog';
import {Training} from '../../../shared/api/models/training';
import {Application} from '../../shared/application.interface';
import {IciFormationCode, IciFormationMapping} from '../ici-formation.application';

@Component({
    selector: 'dialog-ici-formation',
    templateUrl: './dialog-ici-formation.html',
    styleUrls: ['./dialog-ici-formation.scss'],
})
export class DialogIciFormationComponent implements OnInit {

    constructor(
        public dialogRef: MatDialogRef<DialogIciFormationComponent>,
        @Inject(MAT_DIALOG_DATA) public dialogData: {
            mapping: IciFormationMapping[],
            trainings: Training[],
            iciFormationCode: IciFormationCode,
            app: Application,
            saveMapping(body): void
        },
    ) {
    }

    ngOnInit(): void {
        this.dialogData.trainings.sort((a, b) => {
            const aExportable = this.dialogData.mapping[a.externalId].domaine1 && this.dialogData.mapping[a.externalId].sous_domaine1;
            const bExportable = this.dialogData.mapping[b.externalId].domaine1 && this.dialogData.mapping[b.externalId].sous_domaine1;
            return (aExportable === bExportable) ? 0 : aExportable ? 1 : -1;
        });
    }

    isUpdateDisabled(): boolean {
        let isDisabled = true;
        Object.values(this.dialogData.mapping).forEach((trainingsDomainsEntry) => {
            if (trainingsDomainsEntry.domaine1 !== '' && typeof trainingsDomainsEntry.domaine1 === 'string' &&
                trainingsDomainsEntry.sous_domaine1 !== '' && typeof trainingsDomainsEntry.sous_domaine1 === 'string'
            ) {
                isDisabled = false;
            }
        });
        return isDisabled;
    }

    save(): void {
        const trainingsDomainsEntriesToExport = {};
        Object.entries(this.dialogData.mapping).forEach(([trainingId, trainingDomainEntry]) => {
            if (trainingDomainEntry.domaine1 !== '' && typeof trainingDomainEntry.domaine1 === 'string' &&
                trainingDomainEntry.sous_domaine1 !== '' && typeof trainingDomainEntry.sous_domaine1 === 'string'
            ) {
                trainingDomainEntry.isRequired = true;
                trainingsDomainsEntriesToExport[trainingId] = trainingDomainEntry;
            }
        });
        this.dialogData.saveMapping(trainingsDomainsEntriesToExport);
        this.dialogRef.close();
    }

    closeModal(): void {
        this.dialogRef.close();
    }

    removeTraining(trainingId): void {
        this.dialogData.mapping[trainingId] = {
            'domaine1': '',
            'sous_domaine1': '',
            'sanction': 0,
            'diplome': 6,
            'publicFormation': '',
            'accroche': '',
            'prerequis': '',
            'isRequired': false,
        };
    }

}
