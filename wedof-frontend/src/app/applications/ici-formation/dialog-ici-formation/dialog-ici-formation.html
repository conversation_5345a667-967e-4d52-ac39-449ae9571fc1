<app-dialog-layout
    [title]="'private.application.iciFormation.form.title' | translate"
    [actions]="actions"
    (dialogClose)="closeModal()">
    <p>{{ 'private.application.iciFormation.form.subtitle' | translate }}
        <a href="https://www.iciformation.fr/domaines.php" target="_blank">{{ dialogData.app?.name }}</a>. {{ 'private.application.iciFormation.form.subtitle2' | translate }}{{ 'private.application.iciFormation.form.subtitleExplanation' | translate }}
    </p>
    <ng-template #noTrainings>
        <p>{{ 'private.application.iciFormation.form.noTraining' | translate }}</p>
    </ng-template>

    <table class="table mt-5 mb-10" *ngIf="dialogData.trainings; else noTrainings" mat-table
           [dataSource]="dialogData.trainings">
        <ng-container matColumnDef="formation">
            <th mat-header-cell *matHeaderCellDef class="text-left">
                {{ 'private.application.iciFormation.form.table.header.trainings' | translate }}
            </th>
            <td *matCellDef="let element" mat-cell>
                <p>{{element.title}}</p>
                <mat-form-field appearance="standard">
                    <div class="flex items-baseline flex-grow">
                        <input matInput type="text"
                               [matTooltip]="'private.application.iciFormation.form.tooltip' | translate"
                               [matTooltipPosition]="'above'"
                               [matTooltipShowDelay]="500"
                               [(ngModel)]="dialogData.mapping[element.externalId].accroche"
                               [placeholder]="'private.application.iciFormation.form.table.label.accroche' | translate"
                               maxlength="255"
                        >
                    </div>
                </mat-form-field>
            </td>
        </ng-container>

        <ng-container matColumnDef="domaine1">
            <th mat-header-cell class="text-left"
                *matHeaderCellDef>{{ 'private.application.iciFormation.form.table.header.domain' | translate }} *
            </th>
            <td *matCellDef="let element" mat-cell>
                <mat-form-field appearance="standard" class="flex-auto">
                    <mat-select [(ngModel)]="dialogData.mapping[element.externalId].domaine1"
                                [required]="dialogData.mapping[element.externalId].isRequired">
                        <mat-option *ngFor="let domain of dialogData.iciFormationCode | keyvalue"
                                    [value]="domain.key">
                            {{domain.value.domain}}
                        </mat-option>
                    </mat-select>
                </mat-form-field>
            </td>
        </ng-container>

        <ng-container matColumnDef="sous_domaine1">
            <th mat-header-cell class="text-left"
                *matHeaderCellDef>{{ 'private.application.iciFormation.form.table.header.subDomain' | translate }} *
            </th>
            <td *matCellDef="let element" mat-cell>
                <mat-form-field *ngIf="dialogData.mapping[element.externalId].domaine1" appearance="standard">
                    <mat-select [(ngModel)]="dialogData.mapping[element.externalId].sous_domaine1"
                                [required]="dialogData.mapping[element.externalId].isRequired">
                        <mat-option
                            *ngFor="let subdomain of dialogData.iciFormationCode[dialogData.mapping[element.externalId].domaine1].subdomains | keyvalue"
                            [value]="subdomain.key">
                            {{subdomain.value}}
                        </mat-option>
                    </mat-select>
                </mat-form-field>
            </td>
        </ng-container>

        <ng-container matColumnDef="sanction">
            <th mat-header-cell class="text-left"
                *matHeaderCellDef>{{ 'private.application.iciFormation.form.table.header.sanction' | translate }} *
            </th>
            <td *matCellDef="let element" mat-cell>
                <mat-form-field appearance="standard">
                    <mat-select [(ngModel)]="dialogData.mapping[element.externalId].sanction"
                                [required]="dialogData.mapping[element.externalId].isRequired">
                        <mat-option
                            value="1">{{ 'private.application.iciFormation.form.table.attestation.attestation' | translate }}</mat-option>
                        <mat-option
                            value="2">{{ 'private.application.iciFormation.form.table.attestation.habilitation' | translate }}</mat-option>
                        <mat-option
                            value="3">{{ 'private.application.iciFormation.form.table.attestation.certification' | translate }}</mat-option>
                        <mat-option
                            value="4">{{ 'private.application.iciFormation.form.table.attestation.qualification' | translate }}</mat-option>
                        <mat-option
                            value="5">{{ 'private.application.iciFormation.form.table.attestation.titre' | translate }}</mat-option>
                        <mat-option
                            value="6">{{ 'private.application.iciFormation.form.table.attestation.diplome' | translate }}</mat-option>
                    </mat-select>
                </mat-form-field>
            </td>

        </ng-container>

        <ng-container matColumnDef="diplome">
            <th mat-header-cell class="text-left"
                *matHeaderCellDef>{{ 'private.application.iciFormation.form.table.header.diplome' | translate }} *
            </th>
            <td *matCellDef="let element" mat-cell>
                <mat-form-field appearance="standard">
                    <mat-select [(ngModel)]="dialogData.mapping[element.externalId].diplome"
                                [required]="dialogData.mapping[element.externalId].isRequired">
                        <mat-option
                            value="0">{{ 'private.application.iciFormation.form.table.diplome.pasDiplome' | translate }}</mat-option>
                        <mat-option
                            value="1">{{ 'private.application.iciFormation.form.table.diplome.niveau5' | translate }}</mat-option>
                        <mat-option
                            value="2">{{ 'private.application.iciFormation.form.table.diplome.niveau4' | translate }}</mat-option>
                        <mat-option
                            value="3">{{ 'private.application.iciFormation.form.table.diplome.niveau3' | translate }}</mat-option>
                        <mat-option
                            value="4">{{ 'private.application.iciFormation.form.table.diplome.niveau2' | translate }}</mat-option>
                        <mat-option
                            value="5">{{ 'private.application.iciFormation.form.table.diplome.niveau1' | translate }}</mat-option>
                    </mat-select>
                </mat-form-field>
            </td>
        </ng-container>

        <ng-container matColumnDef="publicFormation">
            <th mat-header-cell class="text-left"
                *matHeaderCellDef>{{ 'private.application.iciFormation.form.table.header.publicFormation' | translate }}
                *
            </th>
            <td *matCellDef="let element" mat-cell>
                <mat-form-field appearance="standard">
                    <mat-label>{{'private.application.iciFormation.form.table.label.publicFormation' | translate }}</mat-label>
                    <input matInput type="text" [required]="dialogData.mapping[element.externalId].isRequired"
                           [(ngModel)]="dialogData.mapping[element.externalId].publicFormation"
                           [placeholder]="'private.application.iciFormation.form.table.header.publicFormation' | translate"
                    >
                </mat-form-field>
            </td>
        </ng-container>

        <ng-container matColumnDef="prerequis">
            <th mat-header-cell class="text-left"
                *matHeaderCellDef>{{ 'private.application.iciFormation.form.table.header.prerequis' | translate }}
            </th>
            <td *matCellDef="let element" mat-cell>
                <mat-form-field appearance="standard">
                    <mat-label>{{'private.application.iciFormation.form.table.label.prerequis' | translate }}</mat-label>
                    <input matInput type="text"
                           maxlength="255"
                           [(ngModel)]="dialogData.mapping[element.externalId].prerequis"
                           [placeholder]="'private.application.iciFormation.form.table.header.prerequis' | translate"
                    >
                </mat-form-field>
            </td>
        </ng-container>

        <ng-container matColumnDef="exportable">
            <th mat-header-cell class="text-left"
                *matHeaderCellDef>{{ 'private.application.iciFormation.form.table.header.export' | translate }}</th>
            <td *matCellDef="let element" mat-cell class="text-center">
                <button mat-icon-button (click)="removeTraining(element.externalId)"
                        *ngIf="dialogData.mapping[element.externalId].domaine1 &&
                        dialogData.mapping[element.externalId].sous_domaine1
                       ; else noIdDomainNorIdSubDomain">
                    <mat-icon svgIcon="check_circle_outline" class="text-green"></mat-icon>
                </button>
                <ng-template #noIdDomainNorIdSubDomain>
                    <button mat-icon-button (click)="removeTraining(element.externalId)">
                        <mat-icon svgIcon="highlight_off" class="text-gray"></mat-icon>
                    </button>
                </ng-template>
            </td>
        </ng-container>

        <tr *matRowDef="let row; columns:['formation', 'domaine1', 'sous_domaine1', 'exportable']"
            mat-row></tr>
        <tr mat-header-row
            *matHeaderRowDef="['formation', 'domaine1', 'sous_domaine1', 'exportable']"></tr>
    </table>

    <ng-template #actions>
        <button
            [disabled]="isUpdateDisabled()"
            (click)="save()"
            color="primary"
            mat-flat-button>{{ 'private.application.iciFormation.form.save' | translate }}</button>
    </ng-template>
</app-dialog-layout>
