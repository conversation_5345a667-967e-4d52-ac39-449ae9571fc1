<app-application-settings [application]="application" (formSubmitted)="download()" [submitButton]="getSubmitButton()">
    <p>{{ 'private.application.iciFormation.title' | translate }} <a
        href="https://www.iciformation.fr/index_if.php?module_if=client&action_if=catalogue" target="_blank">lien</a>
    </p>
    <div class="file mb-2 mt-4">
        <button type="button" (click)="openDialog()" color="primary"
                mat-flat-button>{{ 'private.application.iciFormation.actions.settings' | translate }} {{this.totalTraining}} {{ 'private.application.iciFormation.trainings' | translate }}</button>
    </div>
    <mat-progress-bar *ngIf="download$ | async as downloadd"
                      [mode]="downloadd.state == 'PENDING' ? 'buffer' : 'determinate'"
                      [value]="downloadd.progress">
    </mat-progress-bar>
</app-application-settings>
