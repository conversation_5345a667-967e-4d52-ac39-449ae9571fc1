import {Component, Injector, OnInit} from '@angular/core';
import {Router} from '@angular/router';
import {Observable} from 'rxjs';
import {MatDialog} from '@angular/material/dialog';
import {AbstractApplicationSettingsComponent} from '../../shared/settings/abstract-application-settings.component';
import {FileService} from '../../../shared/api/services/file.service';
import {IciFormationMetadata} from '../ici-formation.application';
import {TrainingService} from '../../../shared/api/services/training.service';
import {Download} from '../../../shared/download/download';
import {SubmitSettingsButton} from '../../shared/settings/application-settings.component';
import {APIEndpoint} from '../../../shared/api/services/api-endpoint.enum';
import {OrganismApplication} from '../../../shared/api/models/organism-application';
import {HttpErrorResponse} from '@angular/common/http';
import {DialogIciFormationComponent} from '../dialog-ici-formation/dialog-ici-formation';
import iciFormationCode from '../ici-formation-code.json';


@Component({
    selector: 'app-ici-formation-settings',
    templateUrl: './ici-formation.settings.component.html',
    styleUrls: ['./ici-formation.settings.component.scss'],
})
export class IciFormationSettingsComponent extends AbstractApplicationSettingsComponent<IciFormationMetadata> implements OnInit {

    constructor(
        private _fileService: FileService,
        _router: Router,
        injector: Injector,
        private _dialog: MatDialog,
        private _trainingService: TrainingService
    ) {
        super('ici-formation', _router, injector);
    }

    totalTraining: number;
    totalTrainingDomain: number;
    text: string;
    download$: Observable<Download<Blob>>;
    iciFormationMetadatas: IciFormationMetadata;

    ngOnInit(): void {
        super.ngOnInit();
        this.metadata.subscribe((metadata) => {
            this.iciFormationMetadatas = metadata as IciFormationMetadata;
            if (this.iciFormationMetadatas?.mapping) {
                this.totalTrainingDomain = Object.keys(this.iciFormationMetadatas.mapping).length ? Object.keys(this.iciFormationMetadatas.mapping).length : 0;
            } else {
                this.totalTrainingDomain = 0;
            }
            this.text = 'Exporter ' + this.totalTrainingDomain + ' formation(s)';
        });
        this._trainingService.list({state: 'published'}).subscribe((paginatedResponse) => {
            this.totalTraining = paginatedResponse.total;
        });

    }

    getSubmitButton(): SubmitSettingsButton {
        return {
            value: this.text,
            color: 'primary',
            disabled: this.isUpdateDisabled(),
        };
    }

    download(): void {
        this.download$ = this._fileService.download(`${APIEndpoint.APP}/applications/ici-formation/data/download`, 'Export.xml');
    }

    onSuccess(organismApplication: OrganismApplication): void {
        this._router.navigate(['mes-applications']);
    }

    onError(httpErrorResponse: HttpErrorResponse): void {
    }

    protected initForm(metadata: IciFormationMetadata): void {
    }

    isUpdateDisabled(): boolean {
        let isDisabled = true;
        if (this.metadata.value?.mapping) {
            isDisabled = false;
        }
        return isDisabled;
    }

    openDialog(): void {
        let defaultMapping = {};
        this._trainingService.list({state: 'published'}).subscribe((paginatedResponse) => {
            paginatedResponse.payload.forEach((training) => {
                defaultMapping[training.externalId] = {
                    'domaine1': '',
                    'sous_domaine1': '',
                    'sanction': '',
                    'diplome': '',
                    'publicFormation': '',
                    'accroche': '',
                    'prerequis': '',
                    'isRequired': ''
                };
            });
            defaultMapping = {...defaultMapping, ...this.iciFormationMetadatas.mapping};
            this._dialog.open(DialogIciFormationComponent, {
                panelClass: ['full-page-scroll-80'],
                data: {
                    iciFormationCode: iciFormationCode,
                    saveMapping: (mappingTrainingsDomainsEntries) => {
                        this._organismApplicationService.setSettings(this.application.appId(),
                            {mapping: mappingTrainingsDomainsEntries}).subscribe((applicationIciFormation) => {
                            this.metadata.next(applicationIciFormation.metadata as IciFormationMetadata);
                        });
                    },
                    trainings: paginatedResponse.payload,
                    mapping: defaultMapping,
                    app: this.application
                }
            });
        });
    }

}
