import {application} from '../shared/application.decorator';
import {Application, staticImplements} from '../shared/application.interface';
import {IciFormationSettingsComponent} from './settings/ici-formation.settings.component';
import {DialogIciFormationComponent} from './dialog-ici-formation/dialog-ici-formation';

@application()
@staticImplements<Application>()
export class IciFormationApplication {
    public static appName(): string {
        return 'ICI Formation';
    }

    public static appId(): string {
        return 'ici-formation';
    }

    public static beta(): boolean {
        return false;
    }

    public static isSubscriptionOption(): boolean {
        return false;
    }

    public static hasTrialAvailable(): boolean {
        return false;
    }

    public static components(): any {
        return [
            IciFormationSettingsComponent,
            DialogIciFormationComponent
        ];
    }

    public static routing(): any {
        return {
            path: IciFormationApplication.appId(),
            children: [{
                path: 'reglages',
                component: IciFormationSettingsComponent
            }]
        };
    }

    public static entryPoints(): any {
        return {};
    }

}

export interface IciFormationMetadata {
    mapping: any;
}

export interface IciFormationCode {
    [id: string]: {
        domain: string;
        subdomains: { [key: string]: string }
    };
}

export interface IciFormationMapping {
    domaine1: string;
    sous_domaine1: string;
    sanction: number;
    diplome: number;
    publicFormation: string;
    accroche: string;
    prerequis: string;
    isRequired: boolean;
}
