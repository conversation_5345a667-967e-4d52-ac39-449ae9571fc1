import {Application, staticImplements} from '../shared/application.interface';
import {application} from '../shared/application.decorator';
import {OnlineformaproSettingsComponent} from './settings/onlineformapro.settings.component';

@application()
@staticImplements<Application>()
export class OnlineformaproApplication {
    public static appName(): string {
        return 'Onlineformapro';
    }

    public static appId(): string {
        return 'onlineformapro';
    }

    public static isSubscriptionOption(): boolean {
        return false;
    }

    public static hasTrialAvailable(): boolean {
        return false;
    }

    public static beta(): boolean {
        return false;
    }

    public static components(): any {
        return [
            OnlineformaproSettingsComponent
        ];
    }

    public static priority(): number {
        return 700;
    }

    public static routing(): any {
        return {
            path: OnlineformaproApplication.appId(),
            children: [{
                path: 'reglages',
                component: OnlineformaproSettingsComponent
            }]
        };
    }

    public static entryPoints(): any {
        return {};
    }

}


export interface OnlineformaproMetadata {
    apiKey: string;
    username: string;
    password: string;
    baseUrl: string;
}

export interface OnlineformaproMetadataResponse {
    onlineformaproMetadata: OnlineformaproMetadata;
}
