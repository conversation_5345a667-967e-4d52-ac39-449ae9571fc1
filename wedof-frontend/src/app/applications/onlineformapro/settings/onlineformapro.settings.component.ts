import {Component, Injector} from '@angular/core';
import {HttpErrorResponse} from '@angular/common/http';
import {AbstractApplicationSettingsComponent} from '../../shared/settings/abstract-application-settings.component';
import {OrganismApplication} from '../../../shared/api/models/organism-application';
import {
    OnlineformaproMetadata,
    OnlineformaproMetadataResponse
} from '../onlineformapro.application';
import {SubmitSettingsButton} from '../../shared/settings/application-settings.component';
import {SnackBarComponent} from '../../../shared/material/snack-bar/snack-bar.component';
import {displayTextSnackBar} from '../../../shared/utils/displayTextSnackBar';
import {MatSnackBar} from '@angular/material/snack-bar';
import {TranslateService} from '@ngx-translate/core';
import {AppFormFieldData} from '../../../shared/material/app-form-field/app-form-field.component';
import {ApiError} from '../../../shared/errors/errors.types';
import {Router} from '@angular/router';

@Component({
    selector: 'app-onlineformapro-settings',
    templateUrl: './onlineformapro.settings.component.html'
})
export class OnlineformaproSettingsComponent extends AbstractApplicationSettingsComponent<OnlineformaproMetadataResponse> {

    status: boolean = null;
    ready: boolean;
    onlineformaproMetadata: OnlineformaproMetadata;
    appFormFieldsData: AppFormFieldData[][];

    constructor(
        injector: Injector,
        _router: Router,
        private _snackBar: MatSnackBar,
        private _translateService: TranslateService
    ) {
        super('onlineformapro', _router, injector);
    }

    onSuccess(organismApplication: OrganismApplication): void {
        const onlineformaproMetadataResponse: OnlineformaproMetadataResponse = organismApplication.metadata as OnlineformaproMetadataResponse;
        if (!this.status) {
            this.initForm(onlineformaproMetadataResponse);
        }
    }

    submit(): void {
        this.loading = true;
        const isPasswordDirty: boolean = this.formGroup.get('onlineformaproMetadata.password').dirty;

        this._organismApplicationService.postData(this.application.appId(), 'checkCredentials', {
            ...this.formGroup.get('onlineformaproMetadata').value,
            isPasswordDirty
        }).subscribe((data) => {
            if (data) {
                super.submit();
            }
            const text = 'private.application.onlineformapro.valid-credentials';
            this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(this._translateService.instant(text), 5000, 'green'));
            this.loading = false;
        }, (httpErrorResponse: HttpErrorResponse) => {
            this.status = false;
            this.loading = false;
            const errorCode = (httpErrorResponse.error as ApiError).detail;
            if (errorCode === 'no-api') {
                const text = 'private.application.onlineformapro.' + errorCode;
                this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(this._translateService.instant(text), 5000, 'red'));
            } else {
                const text = 'private.application.onlineformapro.invalid-credentials';
                this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(this._translateService.instant(text), 5000, 'red'));
            }
        });
    }

    onError(httpErrorResponse: HttpErrorResponse): void {
    }

    getSubmitButton(): SubmitSettingsButton {
        return {
            color: 'primary',
            value: 'Valider',
            disabled: !this.formGroup.valid || !this.formGroup.dirty
        };
    }

    protected initForm(metadata: OnlineformaproMetadataResponse): void {
        if (!metadata) {
            return;
        }

        this.onlineformaproMetadata = metadata.onlineformaproMetadata;

        this.formGroup = this._fb.group({
            onlineformaproMetadata: this._fb.group({})
        });

        const appFormFieldsData: AppFormFieldData[][] = [];

        appFormFieldsData[0] = [
            {
                controlName: 'username',
                required: true,
                label: 'private.application.onlineformapro.username',
                type: 'text',
                colSpan: 3,
                placeholder: 'private.application.onlineformapro.placeholder.username'
            },
            {
                controlName: 'password',
                required: true,
                label: 'private.application.onlineformapro.password',
                type: 'password',
                colSpan: 3,
                placeholder: 'private.application.onlineformapro.placeholder.password'
            },
            {
                controlName: 'baseUrl',
                required: true,
                label: 'private.application.onlineformapro.baseUrl',
                type: 'url',
                colSpan: 3,
                placeholder: 'private.application.onlineformapro.placeholder.baseUrl'
            },
            {
                controlName: 'apiKey',
                required: true,
                label: 'private.application.onlineformapro.apiKey',
                type: 'password',
                colSpan: 3,
                placeholder: 'private.application.onlineformapro.placeholder.apiKey'
            }
        ];

        if (this.onlineformaproMetadata) {
            this._organismApplicationService.postData(this.application.appId(), 'checkState', {}).subscribe((data) => {
                this.status = data;
            });
        }

        this.ready = true;
        this.appFormFieldsData = appFormFieldsData;
    }
}

