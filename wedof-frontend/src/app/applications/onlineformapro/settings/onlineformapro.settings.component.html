<app-application-settings (formSubmitted)="submit()" *ngIf="metadata | async"
                          [loading]="loading"
                          [application]="application"
                          [submitButton]="getSubmitButton()">
    <div *ngIf="metadata | async" class="flex flex-row items-center mb-4">
        <div class="font-medium leading-tight">
            {{ (this.status === true ? 'private.application.onlineformapro.synchronization.status.success' :
            (this.status === false ? 'private.application.onlineformapro.synchronization.status.success' :
                'private.application.onlineformapro.synchronization.status.loading')) | translate:metadata.value }}
        </div>
        <mat-icon [ngClass]="{'text-green':status, 'text-red':status === false}" class="ml-1"
                  svgIcon="{{ status === true ? 'check_circle' : (status === false ? 'cancel' : 'sync') }}"></mat-icon>
    </div>
    <div [formGroup]="formGroup">
        <p class="text-lg font-medium mb-3"
           *ngIf="status === false">{{ 'private.application.onlineformapro.description' | translate}}</p>
        <app-form-fields class="grid grid-cols-6 gap-2"
                         formGroupName="onlineformaproMetadata"
                         [entity]="onlineformaproMetadata"
                         [appFormFieldsData]="appFormFieldsData[0]"
                         [formGroup]="formGroup"></app-form-fields>
    </div>
</app-application-settings>
