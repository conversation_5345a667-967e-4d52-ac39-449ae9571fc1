import {Application, staticImplements} from '../shared/application.interface';
import {application} from '../shared/application.decorator';
import {DocumentSettingsComponent} from './settings/document.settings.component';

@application()
@staticImplements<Application>()
export class DocumentApplication {
    public static appName(): string {
        return 'Génération de documents';
    }

    public static appId(): string {
        return 'document';
    }

    public static beta(): boolean {
        return false;
    }

    public static isSubscriptionOption(): boolean {
        return false;
    }

    public static hasTrialAvailable(): boolean {
        return false;
    }

    public static components(): any {
        return [
            DocumentSettingsComponent
        ];
    }

    public static priority(): number {
        return 998;
    }

    public static routing(): any {
        return {
            path: DocumentApplication.appId(),
            children: [{
                path: 'reglages',
                component: DocumentSettingsComponent
            }]
        };
    }

    public static entryPoints(): any {
        return {};
    }
}

export interface DocumentMetadata {
    dummy: string; // propriété uniquement pour passer le lint à remplacer plus tard
}
