import {Component, Injector} from '@angular/core';
import {Router} from '@angular/router';
import {HttpErrorResponse} from '@angular/common/http';
import {AbstractApplicationSettingsComponent} from '../../shared/settings/abstract-application-settings.component';
import {OrganismApplication} from '../../../shared/api/models/organism-application';
import {DocumentMetadata} from '../document.application';

@Component({
    selector: 'document-settings',
    templateUrl: './document.settings.component.html',
    styleUrls: ['./document.settings.component.scss']
})
export class DocumentSettingsComponent extends AbstractApplicationSettingsComponent<DocumentMetadata> {

    private static appId = 'document';

    constructor(
        _router: Router,
        injector: Injector,
    ) {
        super(DocumentSettingsComponent.appId, _router, injector);
    }

    onSuccess(organismApplication: OrganismApplication): void {
        this._router.navigate(['mes-applications']);
    }

    onError(httpErrorResponse: HttpErrorResponse): void {
    }

    protected initForm(): void {

    }
}
