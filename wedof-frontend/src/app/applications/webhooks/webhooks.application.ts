import {Application, staticImplements} from '../shared/application.interface';
import {WebhookFormPageComponent} from './settings/webhook-form-page/webhook-form-page.component';
import {WebhooksSettingsComponent} from './settings/webhooks.settings.component';
import {DeliveryListComponent} from './settings/webhook-form-page/delivery-list/delivery-list.component';
import {application} from '../shared/application.decorator';
import {WebhookEventTypesResolver} from './settings/webhook-form-page/webhook-events-type-resolver';
import {WebhookResolver} from './settings/webhook-form-page/webhook-resolver';
import {DeliveryMenuComponent} from './delivery-menu/delivery-menu.component';
import {DeliveryTableComponent} from './delivery-table/delivery-table.component';
import {DeliveryCardComponent} from './delivery-card/delivery-card.component';

@application()
@staticImplements<Application>()
export class WebhooksApplication {
    public static appName(): string {
        return 'Webhooks';
    }

    public static appId(): string {
        return 'webhook';
    }

    public static isSubscriptionOption(): boolean {
        return false;
    }

    public static hasTrialAvailable(): boolean {
        return false;
    }

    public static beta(): boolean {
        return false;
    }

    public static components(): any {
        return [
            WebhookFormPageComponent,
            WebhooksSettingsComponent,
            DeliveryListComponent,
            DeliveryTableComponent,
            DeliveryCardComponent,
            DeliveryMenuComponent
        ];
    }

    public static priority(): number {
        return 997;
    }

    public static routing(): any {
        return {
            path: WebhooksApplication.appId(),
            children: [{
                path: 'reglages',
                component: WebhooksSettingsComponent
            }, {
                path: 'creer-webhooks',
                component: WebhookFormPageComponent,
                resolve: {
                    eventsType: WebhookEventTypesResolver
                }
            }, {
                path: 'editer-webhooks/:id',
                component: WebhookFormPageComponent,
                resolve: {
                    webhook: WebhookResolver,
                    eventsType: WebhookEventTypesResolver
                }
            }]
        };
    }

    public static entryPoints(): any {
        return {
            'card-registration-folder-side': [{
                tpl: 'tpl-webhooks-card-registration-folder-side',
                title: 'private.common.shortcut.webhook',
                shortcut: {
                    title: 'private.common.shortcut.webhook',
                    target: DeliveryCardComponent.COMPONENT_ID,
                    icon: 'webhook',
                    visible: true
                }
            }],
            'card-certification-folder-side': [{
                tpl: 'tpl-webhooks-card-certification-folder-side',
                title: 'private.common.shortcut.webhook',
                shortcut: {
                    title: 'private.common.shortcut.webhook',
                    target: DeliveryCardComponent.COMPONENT_ID,
                    icon: 'webhook',
                    visible: true
                }
            }],
            'card-certification-partner-side': [{
                tpl: 'tpl-webhooks-card-certification-partner-side',
                title: 'private.common.shortcut.webhook',
                shortcut: {
                    title: 'private.common.shortcut.webhook',
                    target: DeliveryCardComponent.COMPONENT_ID,
                    icon: 'webhook',
                    visible: true
                }
            }],
        };
    }
}

export interface WebhooksEventsResponse {
    [key: string]: string[];
}
