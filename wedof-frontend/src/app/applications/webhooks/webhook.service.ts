import {HttpClient} from '@angular/common/http';
import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {WebhooksEventsResponse} from './webhooks.application';
import {APIEndpoint} from '../../shared/api/services/api-endpoint.enum';
import {Webhook} from './webhook';
import {Delivery} from './delivery';
import {
    AbstractPaginatedService,
    BaseHttpParams,
    PaginatedResponse
} from '../../shared/api/services/abstract-paginated.service';

@Injectable({
    providedIn: 'root'
})
export class WebhookService extends AbstractPaginatedService<Delivery> {

    constructor(private httpClient: HttpClient) {
        super(httpClient);
    }

    list(): Observable<Webhook[]> {
        return this.httpClient.get<Webhook[]>(`${APIEndpoint.API}/webhooks?type=webhook`);
    }

    get(webhookId: number): Observable<Webhook> {
        return this.httpClient.get<Webhook>(`${APIEndpoint.API}/webhooks/${webhookId}`);
    }

    create(webhook: Webhook): Observable<Webhook> {
        return this.httpClient.post<Webhook>(`${APIEndpoint.API}/webhooks`, webhook);
    }

    update(webhookId: number, webhook: Webhook): Observable<Webhook> {
        return this.httpClient.put<Webhook>(`${APIEndpoint.API}/webhooks/${webhookId}`, webhook);
    }

    delete(webhookId: number): Observable<Webhook> {
        return this.httpClient.delete<Webhook>(`${APIEndpoint.API}/webhooks/${webhookId}`);
    }

    getEvents(): Observable<WebhooksEventsResponse> {
        return this.httpClient.get<WebhooksEventsResponse>(`${APIEndpoint.API}/webhooks-events`);
    }

    toggle(webhook: Webhook): Observable<Webhook> {
        return this.httpClient.post<Webhook>(`${APIEndpoint.API}/webhooks/${webhook.id}/${webhook.enabled ? 'enable' : 'disable'}`, {});
    }

    retryDelivery(webhook: Webhook, delivery: Delivery): Observable<void> {
        return this.httpClient.post<void>(`${APIEndpoint.API}/webhooks/${webhook.id}/delivery/${delivery.id}/retry`, {});
    }

    getDeliveries(webhook: Webhook, params: BaseHttpParams): Observable<PaginatedResponse<Delivery>> {
        return this.findAllPaginatedSorted(`${APIEndpoint.API}/webhooks/${webhook.id}/deliveries`, params);
    }

    listDeliveriesByEntity(entityClass: string, entityId: any, params: BaseHttpParams): Observable<PaginatedResponse<Delivery>> {
        return this.findAllPaginatedSorted(`${APIEndpoint.API}/deliveries/${entityClass}/${entityId}`, params);
    }

    retryDeliveryForEntity(delivery: Delivery): Observable<void> {
        return this.httpClient.post<void>(`${APIEndpoint.API}/deliveries/${delivery.id}/retry`, {});
    }
}
