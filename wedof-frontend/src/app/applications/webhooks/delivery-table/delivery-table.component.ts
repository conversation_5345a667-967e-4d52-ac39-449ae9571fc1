import {Component, EventEmitter, Injector, Input, Output} from '@angular/core';
import {AbstractTableComponent} from '../../../shared/material/table/abstract-table.component';
import {Delivery} from '../delivery';
import {WebhookService} from '../webhook.service';
import {Observable} from 'rxjs';
import {PaginatedResponse} from '../../../shared/api/services/abstract-paginated.service';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../../shared/errors/errors.types';

export type RefreshDeliveries = (limit: number, page: number) => Observable<PaginatedResponse<Delivery>>;

@Component({
    selector: 'app-delivery-table',
    templateUrl: './delivery-table.component.html',
    styleUrls: ['./delivery-table.component.scss']
})
export class DeliveryTableComponent extends AbstractTableComponent<Delivery> {

    loading = false;

    @Input() entityClass: string;
    @Input() refreshDeliveries: RefreshDeliveries;
    @Output() errorMessages?: EventEmitter<string[]> = new EventEmitter<[]>();

    constructor(
        private _webhookService: WebhookService,
        injector: Injector,
    ) {
       super(injector);
       this.displayedColumns = ['status', 'guid', 'actions'];
    }

    retryDelivery(delivery: Delivery): void {
        this.loading = true;
        this.errorMessages.emit([]);
        this._webhookService.retryDeliveryForEntity(delivery).subscribe({
            next: () => {
                this.forceRefresh();
                this.loading = false;
            },
            error: (httpErrorResponse: HttpErrorResponse) => {
                this.loading = false;
                this.errorMessages.emit((httpErrorResponse.error as ApiError).errorMessages);
            }
        });
    }

    protected refreshData(): Observable<PaginatedResponse<Delivery>> {
        return this.refreshDeliveries(this.paginator.pageSize, this.paginator.pageIndex + 1);
    }
}
