<table [dataSource]="displayedData" mat-table class="w-full">
    <ng-container matColumnDef="status">
        <td *matCellDef="let element" class="text-left w-3" mat-cell>
            <mat-icon *ngIf="element.statusCode >= 300 || element.statusCode < 0"
                      [matTooltip]="'Status Code HTTP : ' + element.statusCode"
                      [title]="element.statusCode"
                      color="warn">warning
            </mat-icon>
            <mat-icon *ngIf=" element.statusCode < 300 && element.statusCode >= 200"
                      [title]="element.statusCode"
                      class="text-green">check
            </mat-icon>
        </td>
    </ng-container>

    <ng-container matColumnDef="guid">
        <td mat-cell *matCellDef="let element">
            <span [matTooltip]="element.guid">{{element.guid.slice(0, 7)}}... </span>
            <br/>
            <span matTooltipPosition="above" [matTooltip]="element.event" matTooltipClass="break-all"
                  class="text-disabled">{{ 'private.application.webhooks.events.' + element.event | translate }}</span>
            <br/>
            <span class="text-disabled">{{ element.date | date:'short' }}</span>
        </td>
    </ng-container>

    <ng-container matColumnDef="actions">
        <td *matCellDef="let row; let element;" class="text-right w-3" mat-cell>
            <button mat-icon-button (click)="retryDelivery(row);" [disabled]="loading"
                    type="button">
                <mat-icon color="warn">refresh</mat-icon>
            </button>
        </td>
    </ng-container>

    <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

</table>
<app-paginator [length]="total"
               [pageSizeOptions]="pageSizeOptions"
               [scrollTopOnPageChange]="false"
               (page)="onPageEvent($event)">
</app-paginator>

