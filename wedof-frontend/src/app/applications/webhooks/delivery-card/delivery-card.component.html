<mat-card class="mat-card flex h-full flex-auto flex-col mt-3 p-5 pb-0 pt-3 border shadow-none sm"
          [ngClass]="{'card-loading':cardLoading}"
          *ngIf="context.organismApplication.enabled">

    <div class="flex items-center mb-2">
        <mat-icon
            [matBadge]="total > 0 ? total.toString() : null"
            matBadgePosition="below after"
            matBadgeSize="small"
            class="mr-3 card-loading-show text-4xl"
            color="primary">webhook
        </mat-icon>
        <div class="flex items-center">
            <span
                class="text-xl font-semibold card-loading-show">{{ context.params.title | translate }}</span>
        </div>
    </div>

    <div *ngIf="!cardLoading" class="flex flex-col mt-3 mb-3 overflow-y-auto"
         infiniteScroll
         [infiniteScrollDistance]="2"
         [infiniteScrollThrottle]="50"
         (scrolled)="onScroll()"
         [scrollWindow]="false">
        <div *ngIf="deliveries?.length; else noResult">
            <div *ngFor="let delivery of deliveries" class="mb-2">
                <div class="flex flex-row justify-between">
                    <div class="flex items-center">
                        <mat-icon *ngIf="delivery.statusCode >= 300 || delivery.statusCode < 0"
                                  [matTooltip]="'Status Code HTTP : ' + delivery.statusCode + ' ' + delivery.errorMessage"
                                  [title]="delivery.statusCode" class="icon-size-18"
                                  color="warn">warning
                        </mat-icon>
                        <mat-icon *ngIf=" delivery.statusCode < 300 && delivery.statusCode >= 200"
                                  [title]="delivery.statusCode" class="icon-size-18 text-green">check
                        </mat-icon>
                        <div class="pl-1">
                            <div class="flex"><p
                                *ngIf="delivery._links.webhook.name"> {{ extractTextFromHTML(delivery._links.webhook.name) }}
                                - </p>
                                <span [matTooltip]="delivery.guid">{{ delivery.guid.slice(0, 7) }}... </span>
                            </div>
                            <span matTooltipPosition="above" [matTooltip]="delivery.event" matTooltipClass="break-all"
                                  class="text-disabled">{{ 'private.application.webhooks.events.' + delivery.event | translate }}</span>
                            <br/>
                            <span class="text-disabled"
                                  [matTooltip]="delivery.date | date:'short'">{{ daysAgo(delivery.date) }}</span>
                        </div>
                    </div>

                    <button mat-icon-button (click)="retryDelivery(delivery);" [disabled]="loading"
                            type="button">
                        <mat-icon color="warn">refresh</mat-icon>
                    </button>
                </div>
            </div>
        </div>
        <ng-template #noResult>
            <p class="mt-3 mb-3 text-center m-auto">
                {{ 'private.application.webhooks.deliveries.nodata' | translate }}
            </p>
        </ng-template>
    </div>


    <div *ngIf="errorMessages.length" class="flex items-center mt-2 mb-2">
        <treo-message class="w-full" appearance="outline" [showIcon]="false" type="error">
            <ul>
                <li *ngFor="let errorMessage of errorMessages" class="break-all">
                    {{ errorMessage }}
                </li>
            </ul>
        </treo-message>
    </div>
</mat-card>
