import {
    Component,
    ElementRef,
    Input,
    <PERSON><PERSON><PERSON><PERSON>,
    OnDestroy,
    SimpleChang<PERSON>
} from '@angular/core';
import {Delivery} from '../delivery';
import {OrganismApplicationEntryPoint} from '../../../shared/api/models/applications';
import {WebhookService} from '../webhook.service';
import {Subject} from 'rxjs';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../../shared/errors/errors.types';
import {takeUntil} from 'rxjs/operators';
import {daysAgo} from '../../../shared/utils/date-utils';
import {BaseCardComponentDirective, RequiredCallSuper} from '../../../shared/utils/base-card/base-card.directive';
import {ApplicationsService} from '../../shared/applications.service';

@Component({
    selector: 'app-delivery-card',
    templateUrl: './delivery-card.component.html',
    styleUrls: ['./delivery-card.component.scss']
})
export class DeliveryCardComponent extends BaseCardComponentDirective implements OnChanges, OnDestroy {
    static COMPONENT_ID = 'webhooks';
    loading = false;
    total = 0;
    deliveries: Delivery[];
    errorMessages: string[] = [];
    deliveriesLimit = 6;
    @Input() context: OrganismApplicationEntryPoint;
    @Input() entityId: number | string;
    @Input() entityClass: string;
    private _unsubscribeAll: Subject<void> = new Subject();

    constructor(private _applicationService: ApplicationsService, private _webhookService: WebhookService, private _el: ElementRef) {
        super(DeliveryCardComponent.COMPONENT_ID, _el);
    }

    ngOnChanges(changes: SimpleChanges): void {
        this.panelLoading();
        if (this.context.organismApplication.enabled) {
            this.initForm();
        } else {
            this.panelLoaded();
        }
    }

    initForm(): void {
        this.errorMessages = [];
        this._webhookService.listDeliveriesByEntity(this.entityClass, this.entityId, {limit: this.deliveriesLimit}).subscribe((deliveriesResponse) => {
            this.deliveries = deliveriesResponse.payload;
            this.total = deliveriesResponse.total;
            this.deliveries.map((delivery) => {
                try {
                    delivery.errorMessage = JSON.parse(delivery.errorMessage).hint ?? '';
                } catch (error) {
                    delivery.errorMessage = '';
                }
            });

            this.panelLoaded();
        });
    }

    onScroll(): void {
        const displayedPages = this.deliveries.length / this.deliveriesLimit;
        if (Number.isInteger(displayedPages)) {
            this._webhookService.listDeliveriesByEntity(this.entityClass, this.entityId, {
                limit: this.deliveriesLimit,
                page: displayedPages + 1
            })
                .pipe(takeUntil(this._unsubscribeAll))
                .subscribe((response) => {
                    response.payload.map((delivery) => {
                        try {
                            delivery.errorMessage = JSON.parse(delivery.errorMessage).hint ?? '';
                        } catch (error) {
                            delivery.errorMessage = '';
                        }
                    });
                    this.deliveries.push(...response.payload);
                });
        }
    }

    retryDelivery(delivery: Delivery): void {
        this.loading = true;
        this._webhookService.retryDeliveryForEntity(delivery).subscribe({
            next: () => {
                this.initForm();
                this.loading = false;
            },
            error: (httpErrorResponse: HttpErrorResponse) => {
                this.loading = false;
                this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
            }
        });
    }

    ngOnDestroy(): RequiredCallSuper {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
        return super.ngOnDestroy();
    }

    daysAgo(time: Date): string {
        const getDays = daysAgo(time);
        return getDays === 0 ? 'Aujourd\'hui' : getDays > 0 ? 'Il y a ' + getDays + ' jours' : 'Dans ' + -getDays + ' jours';
    }

    extractTextFromHTML(htmlTitle: string): string {
        const textMatch = htmlTitle.match(/<a[^>]*>(.*?)<\/a>/i);
        return textMatch ? textMatch[1] : htmlTitle;
    }
}
