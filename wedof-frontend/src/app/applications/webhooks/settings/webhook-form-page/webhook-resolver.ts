import {Injectable, } from '@angular/core';
import {ActivatedRouteSnapshot, Resolve, Router, RouterStateSnapshot} from '@angular/router';
import {Observable} from 'rxjs';

import {catchError} from 'rxjs/operators';
import {WebhookService} from '../../webhook.service';
import {Webhook} from '../../webhook';


@Injectable()
export class WebhookResolver implements Resolve<Webhook> {
    constructor(
        private webhookService: WebhookService,
        private router: Router,
    ) {
    }

    resolve(
        route: ActivatedRouteSnapshot,
        state: RouterStateSnapshot
    ): Observable<any> {

        return this.webhookService.get(route.params.id)
            .pipe(catchError(() => this.router.navigateByUrl('/')));
    }
}
