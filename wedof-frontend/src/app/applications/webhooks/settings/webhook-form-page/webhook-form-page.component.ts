import {Component, OnInit} from '@angular/core';
import {FormArray, FormBuilder, FormControl, Validators} from '@angular/forms';
import {MatCheckboxChange} from '@angular/material/checkbox';
import {MatDialog} from '@angular/material/dialog';
import {ActivatedRoute, Router} from '@angular/router';
import {filter, finalize, switchMap, tap} from 'rxjs/operators';
import {Webhook} from '../../webhook';
import {WebhooksEventsResponse} from '../../webhooks.application';
import {FormValidators} from '../../../../shared/api/shared/form-validators';
import {
    DeletionConfirmationComponent
} from '../../../../shared/material/action-confirmation/deletion-confirmation.component';
import {WebhookService} from '../../webhook.service';
import {TranslateService} from '@ngx-translate/core';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../../../shared/errors/errors.types';

@Component({
    selector: 'app-webhook-form-page',
    templateUrl: './webhook-form-page.component.html',
    styleUrls: ['./webhook-form-page.component.scss']
})
export class WebhookFormPageComponent implements OnInit {

    webhook: Webhook;
    eventsType: WebhooksEventsResponse;
    errorMessages: string[] = [];

    webhookForm = this._formBuilder.group({
        id: [],
        url: ['', [Validators.required, Validators.pattern(FormValidators.URL_WEHBOOK_PATTERN)]],
        secret: [''],
        ignoreSsl: [''],
        sendEverything: [false],
        events: new FormArray([], [Validators.required]),
        name: ['']
    });

    loading = false;

    readonly sendEverything = this.webhookForm.controls.sendEverything as FormControl;
    readonly webhookId = this.webhookForm.controls.id as FormControl;
    readonly events = this.webhookForm.controls.events as FormArray;

    constructor(
        private _formBuilder: FormBuilder,
        private _webhookService: WebhookService,
        private _router: Router,
        private _translateService: TranslateService,
        private _route: ActivatedRoute,
        private _dialog: MatDialog,
    ) {
    }

    ngOnInit(): void {
        this._route.data.subscribe(
            (data: { webhook: Webhook, eventsType: WebhooksEventsResponse }) => {
                if (data.webhook) {
                    this.webhook = data.webhook;
                    this.webhookForm.patchValue(this.webhook);
                    this.webhookForm.get('url').disable();
                    const events = this.webhookForm.get('events') as FormArray;
                    this.webhook.events.map((event) => {
                        // TODO : must improve this special case ['*']
                        if (Array.isArray(event)) {
                            this.webhookForm.get('sendEverything').setValue(true);
                            events.clearValidators();
                            events.updateValueAndValidity();
                        } else {
                            events.push(new FormControl(event));
                        }
                    });
                }
                this.eventsType = data.eventsType;
            }
        );
    }

    onChange(event): void {
        const events = this.webhookForm.get('events') as FormArray;

        if (event.checked) {
            events.push(new FormControl(event.source.value));
        } else {
            const i = events.controls.findIndex(x => x.value === event.source.value);
            events.removeAt(i);
        }
    }

    delete(): void {
        const dialogRef = this._dialog.open(DeletionConfirmationComponent, {
            panelClass: 'full-page-scroll-40',
            height: 'auto',
            data: {
                messageKey: 'private.application.webhooks.actions.delete'
            }
        });

        dialogRef.componentInstance.actionValue$.pipe(
            filter(confirmation => confirmation),
            tap(() => this.loading = true),
            switchMap(() => this._webhookService.delete(this.webhook.id)),
            finalize(() => {
                dialogRef.componentInstance.close();
                this.loading = false;
            })
        ).subscribe(() => {
            this._router.navigate(['../../reglages'], {relativeTo: this._route});
        });
    }

    handleSendEverything(event: MatCheckboxChange): void {
        const events = this.webhookForm.get('events') as FormArray;

        if (event.checked) {
            events.clearValidators();
        } else {
            events.setValidators(Validators.required);
        }

        events.updateValueAndValidity();
    }

    saveWebhook(): void {
        this.webhookForm.markAllAsTouched();

        if (!this.webhookForm.valid) {
            this.loading = false;
            return;
        }

        const {id, url, secret, ignoreSsl, sendEverything, events, name} = this.webhookForm.value;

        const webhook = {
            url,
            secret: secret ? secret : null,
            ignoreSsl: !!ignoreSsl,
            events: sendEverything ? ['*'] : events,
            name: name ? name : null
        } as Webhook;
        if (url?.includes('hooks.zapier')) {
            this.loading = false;
            this.errorMessages.push(this._translateService.instant('private.application.webhooks.form.errors.errorZapier'));
        } else {
            if (id) {
                // TODO : muste improve this
                delete webhook.url;
                this._webhookService.update(id, webhook).pipe(tap(() => {
                    this.loading = true;
                })).subscribe({
                    next: () => {
                        this._router.navigate(['../../reglages'], {relativeTo: this._route});
                    },
                    error: (httpErrorResponse: HttpErrorResponse) => {
                        this.loading = false;
                        this.errorMessages = ((httpErrorResponse.error as ApiError).errorMessages);
                    }
                });
            } else {
                this._webhookService.create(webhook).pipe(tap(() => {
                    this.loading = true;
                })).subscribe({
                    next: () => {
                        this._router.navigate(['../reglages'], {relativeTo: this._route});
                    },
                    error: (httpErrorResponse: HttpErrorResponse) => {
                        this.loading = false;
                        this.errorMessages = ((httpErrorResponse.error as ApiError).errorMessages);
                    }
                });
            }
        }
    }

    toggleEvents($event: MatCheckboxChange, eventValues: string[]): void {
        const events = this.webhookForm.get('events') as FormArray;
        if ($event.checked) {
            eventValues.forEach((event) => {
                events.push(new FormControl(event));
            });
        } else {
            eventValues.forEach((event) => {
                const i = events.controls.findIndex(x => x.value === event);
                events.removeAt(i);
            });
        }
    }

    checkedValuesLength(eventValues: string[]): number {
        const events = this.webhookForm.get('events') as FormArray;
        let checkedEvents = 0;
        eventValues.forEach((event) => {
            if (events.controls.findIndex(x => x.value === event) !== -1) {
                checkedEvents += 1;
            }
        });
        return checkedEvents;
    }
}
