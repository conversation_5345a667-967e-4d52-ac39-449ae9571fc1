<treo-card class="flex flex-col p-8 pb-0 w-3/4">

    <div class="breadcrumb mb-2">
        <div routerLink="/mes-applications" class="cursor-pointer path">
            {{ 'private.application.common.title' | translate }}
        </div>
        <span class="separator">/</span>
        <div routerLink="/mes-applications/webhook/reglages" class="cursor-pointer path">
            {{ 'private.application.webhooks.title' | translate }}
        </div>
        <span class="separator">/</span>
        <div class="path">
            {{ webhookId.value ? 'Edition' : 'Création' }}
        </div>
    </div>

    <div class="flex flex-row items-center justify-between">
        <div class="flex flex-row items-center">
            <img class="w-14 h-14 mr-4 rounded object-cover" alt="Webhooks" src="/assets/applications/webhook.png"/>
            <div class="text-2xl font-semibold leading-tight">
                {{ 'private.application.webhooks.title' | translate }}
            </div>
        </div>
    </div>

    <div class="flex flex-col mt-5">
        <form (submit)="saveWebhook()" [formGroup]="webhookForm" class="flex flex-col w-full">

            <mat-form-field>
                <mat-label>
                    {{ 'private.application.webhooks.form.fields.name.label' | translate }}
                </mat-label>
                <input formControlName="name" matInput type="text">
            </mat-form-field>

            <mat-form-field>
                <mat-label>
                    {{ 'private.application.webhooks.form.fields.url.label' | translate }}
                </mat-label>
                <input formControlName="url" matInput required type="text">
                <mat-icon matPrefix svgIcon="link"></mat-icon>
                <mat-error class="pb-1">
                    {{ 'private.application.webhooks.form.fields.url.error' | translate }}
                </mat-error>
            </mat-form-field>

            <mat-form-field>
                <mat-label>
                    {{ 'private.application.webhooks.form.fields.secret.label' | translate }}
                </mat-label>
                <input formControlName="secret" matInput type="text">
            </mat-form-field>

            <div class="mt-2">
                <mat-label>{{ 'private.application.webhooks.form.fields.ignoreSsl.label' | translate }}</mat-label>
                <mat-radio-group class="flex" formControlName="ignoreSsl">
                    <mat-radio-button [value]="true"
                                      class="mat-radio-button">{{ 'common.actions.yes' | translate }}
                    </mat-radio-button>
                    <mat-radio-button [value]="false"
                                      class="mat-radio-button ml-2">{{ 'common.actions.no'  | translate }}
                    </mat-radio-button>
                </mat-radio-group>
            </div>

            <mat-label class="mt-2">
                {{ 'private.application.webhooks.form.fields.sendEverything.label' | translate }}
            </mat-label>
            <mat-checkbox (change)="handleSendEverything($event)" class="mt-2" formControlName="sendEverything">
                {{ 'private.application.webhooks.form.fields.sendEverything.value' | translate }}
            </mat-checkbox>

            <div *ngIf="!sendEverything.value" class="grid grid-cols-3">
                <div *ngFor="let eventType of  eventsType | keyvalue">
                    <h5>

                        <mat-checkbox (change)="toggleEvents($event, eventType.value)"
                                      [checked]="checkedValuesLength(eventType.value) == eventType.value.length"
                                      [indeterminate]="checkedValuesLength(eventType.value) > 0 &&  checkedValuesLength(eventType.value) < eventType.value.length">{{ 'private.application.webhooks.events.' + eventType.key + '.title' | translate }}
                        </mat-checkbox>
                    </h5>
                    <div *ngFor="let event of eventType.value">
                        <mat-checkbox (change)="onChange($event)"
                                      [checked]="events.value.indexOf(event) !== -1"
                                      [value]="event">{{ 'private.application.webhooks.events.' + event | translate }}
                        </mat-checkbox>

                    </div>
                </div>
                <mat-error *ngIf="(events.dirty || events.touched) && events.errors?.required" class="pb-1">
                    {{ 'private.application.webhooks.form.fields.events.error' | translate }}
                </mat-error>
            </div>

            <treo-message class="flex-auto justify-center items-center mt-3" appearance="outline" [showIcon]="false"
                          type="error" *ngIf="errorMessages?.length">
                {{ errorMessages }}
            </treo-message>

            <div
                class="flex {{ webhookId.value ? 'border-b justify-between' : 'border-t justify-end' }} items-center -mx-8 mt-5 px-8 py-5 light:bg-cool-gray-50 dark:bg-cool-gray-700">
                <button (click)="delete()" *ngIf="webhookId.value" color="warn" mat-flat-button type="button">
                    {{ 'common.actions.delete' | translate }}
                </button>
                <div>
                    <a [routerLink]="[webhookId.value ? '../../reglages' : '../reglages']" mat-button>
                        {{ 'private.application.common.actions.back' | translate }}
                    </a>
                    <button [disabled]="loading" class="px-6 ml-3" color="primary" mat-flat-button type="submit">
                        <mat-progress-spinner *ngIf="loading" [diameter]="24" [mode]="'indeterminate'" class="mr-4">
                        </mat-progress-spinner>
                        {{ 'common.actions.update' | translate }}
                    </button>
                </div>
            </div>

            <app-delivery-list *ngIf="webhook" [webhook]="webhook"></app-delivery-list>

        </form>

    </div>
</treo-card>
