import { Injectable, } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, Router, RouterStateSnapshot } from '@angular/router';
import { Observable } from 'rxjs';

import { catchError } from 'rxjs/operators';
import {WebhooksEventsResponse} from '../../webhooks.application';
import {WebhookService} from '../../webhook.service';


@Injectable()
export class WebhookEventTypesResolver implements Resolve<WebhooksEventsResponse> {
    constructor(
        private webhookService: WebhookService,
        private router: Router,
    ) { }

    resolve(
        route: ActivatedRouteSnapshot,
        state: RouterStateSnapshot
    ): Observable<any> {

        return this.webhookService.getEvents()
            .pipe(catchError(() => this.router.navigateByUrl('/')));
    }
}
