<h5>
    {{ 'private.application.webhooks.deliveries.title' | translate}}
</h5>

<ng-template #noData>
    <p class="bg-white p-6 cursor-auto text-center m-auto">
        {{ 'private.application.webhooks.deliveries.table.noData' | translate }}
    </p>
</ng-template>

<ng-container *ngIf="isLoading || displayedData?.length ; else noData">

    <table *ngIf="displayedData?.length" [dataSource]="displayedData" mat-table>

        <ng-container matColumnDef="status">
            <td *matCellDef="let element" class="text-left w-5" mat-cell>
                <mat-icon *ngIf="element.statusCode < 300 && element.statusCode >= 200" class="text-green">check
                </mat-icon>
                <mat-icon *ngIf="element.statusCode >= 300 || element.statusCode < 0" [matTooltip]="'Status Code HTTP : ' + element.statusCode"
                          color="warn">warning
                </mat-icon>
            </td>
        </ng-container>

        <ng-container matColumnDef="guid">
            <td mat-cell *matCellDef="let element">
                {{element.guid}}
                <br/>
                <span class="text-disabled">{{element.event}}</span>
            </td>
        </ng-container>

        <ng-container matColumnDef="date">
            <td *matCellDef="let element" class="text-right" mat-cell>
                <span class="text-disabled">{{ element.date | date:'short' }}</span>
            </td>
        </ng-container>

        <ng-container matColumnDef="actions">
            <td *matCellDef="let row; let element;" class="text-right w-5" mat-cell>
                <button (click)="retryDelivery(row);" [disabled]="loading" mat-icon-button
                        type="button">
                    <mat-icon color="warn">refresh</mat-icon>
                </button>
            </td>
        </ng-container>

        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

    </table>
    <app-paginator [length]="total"
                   [pageSizeOptions]="pageSizeOptions"
                   [scrollTopOnPageChange]="true"
                   (page)="onPageEvent($event)">
    </app-paginator>
</ng-container>
