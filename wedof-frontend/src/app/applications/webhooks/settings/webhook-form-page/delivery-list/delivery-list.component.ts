import {Component, Injector, Input} from '@angular/core';
import {Observable} from 'rxjs';
import {AbstractTableComponent} from '../../../../../shared/material/table/abstract-table.component';
import {Delivery} from '../../../delivery';
import {Webhook} from '../../../webhook';
import {PaginatedResponse} from '../../../../../shared/api/services/abstract-paginated.service';
import {WebhookService} from '../../../webhook.service';

@Component({
    selector: 'app-delivery-list',
    templateUrl: './delivery-list.component.html',
    styleUrls: ['./delivery-list.component.scss']
})
export class DeliveryListComponent extends AbstractTableComponent<Delivery> {

    @Input() webhook: Webhook;
    loading = false;

    constructor(
        private _webhookService: WebhookService,
        injector: Injector,
    ) {
        super(injector);
        this.displayedColumns = ['status', 'guid', 'date', 'actions'];
    }

    retryDelivery(delivery: Delivery): void {
        this.loading = true;
        this._webhookService.retryDelivery(this.webhook, delivery).subscribe(() => {
            this.loading = false;
        });
    }

    protected refreshData(): Observable<PaginatedResponse<Delivery>> {
        return this._webhookService.getDeliveries(this.webhook, {
            limit: this.paginator.pageSize,
            page: this.paginator.pageIndex + 1
        });
    }
}
