import {Component, EventEmitter, Injector} from '@angular/core';
import {Validators} from '@angular/forms';
import {Router} from '@angular/router';
import {finalize} from 'rxjs/operators';
import {HttpErrorResponse} from '@angular/common/http';
import {AbstractApplicationSettingsComponent} from '../../shared/settings/abstract-application-settings.component';
import {SubmitSettingsButton} from '../../shared/settings/application-settings.component';
import {OrganismApplication} from '../../../shared/api/models/organism-application';
import {WebhookService} from '../webhook.service';
import {Webhook} from '../webhook';
import {WorkflowApplication} from '../../workflow/workflow.application';

@Component({
    selector: 'app-webhooks.settings',
    templateUrl: './webhooks.settings.component.html',
    styleUrls: ['./webhooks.settings.component.scss']
})
export class WebhooksSettingsComponent extends AbstractApplicationSettingsComponent<WebhooksMetadata> {

    private static appId = 'webhook';
    isLoading = true;

    webhooks = this._webhookService.list().pipe(
        finalize(() => {
            this.isLoading = false;
        })
    );

    displayedColumns: string[] = ['url', 'actions'];

    constructor(
        private _webhookService: WebhookService,
        _router: Router,
        injector: Injector,
    ) {
        super(WebhooksSettingsComponent.appId, _router, injector);
    }

    getSubmitButton(): SubmitSettingsButton {
        return null;
    }

    onSuccess(organismApplication: OrganismApplication): void {
        this._router.navigate(['mes-applications']);
    }

    onError(httpErrorResponse: HttpErrorResponse): void {
    }

    toggleWebhook(webhook: Webhook): void {
        webhook.enabled = !webhook.enabled;
        this._webhookService.toggle(webhook).subscribe(() => {

        });
    }

    displayFormWebhook(event: EventEmitter<any>): void {
        this._router.navigate(['mes-applications', this.application.appId(), 'creer-webhooks']);
    }

    editWebhook(webhook: Webhook): void {
        if (this.haveLink(webhook)) {
            const linkMatch = webhook.name.match(/<a\s+href=["']([^"']+)["']/i);
            if (linkMatch && this.isWorkflowWebhook(webhook)) {
                const workflowPath = linkMatch[1].split('.processus.wedof.fr')[1];
                this._router.navigate(['mes-applications', WorkflowApplication.appId(), 'app'],
                    {state: {workflowPath: workflowPath}});
                return;
            }
        }
        this._router.navigate(['mes-applications', this.application.appId(), 'editer-webhooks', webhook.id]);
    }

    protected initForm(metadata: WebhooksMetadata): void {
        this.formGroup = this._fb.group({
            name: [metadata?.name, [Validators.required]]
        });
    }

    isWorkflowWebhook(webhook: Webhook): boolean {
        return webhook.url.includes('.processus.wedof.fr');
    }

    haveLink(webhook: Webhook): boolean {
        return !!webhook.name && /href\s*=\s*["']/i.test(webhook.name);
    }

    isCellDisabled(webhook: Webhook): boolean {
        return this.isWorkflowWebhook(webhook) && !this.haveLink(webhook);
    }

    cleanName(webhook: Webhook): string {
        if (this.isWorkflowWebhook(webhook)) {
            const linkMatch = webhook.name.match(/<a\s+href=["']([^"']+)["']>(.*)<\/a>/i);
            return linkMatch[2];
        } else {
            return webhook.name;
        }
    }
}

export interface WebhooksMetadata {
    name: string;
}
