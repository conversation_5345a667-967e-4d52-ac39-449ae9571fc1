<app-application-settings (addButtonClicked)="displayFormWebhook($event)" [addButton]="'Nouveau'"
                          [application]="application" [submitButton]="getSubmitButton()">
    <app-wrapper-spinner [active]="isLoading" class="bg-white">
        <table [dataSource]="webhooks" class="pt-8 table-fixed" mat-table>
            <ng-container matColumnDef="url">
                <td *matCellDef="let webhook" mat-cell class="w-60" [class.cursor-disabled]="isCellDisabled(webhook)"
                    (click)="!isCellDisabled(webhook) && editWebhook(webhook)">
                    <div class="flex">
                        <p *ngIf="webhook.name" class="mr-1 font-bold" [innerHTML]="cleanName(webhook)"
                           title="{{cleanName(webhook)}}"></p>
                        <p *ngIf="!webhook.name" class="truncate" title="{{webhook.url}}">{{webhook.url}}</p>
                    </div>
                    <div class="truncate text-secondary">{{webhook.events.join(', ') }}</div>
                </td>
            </ng-container>
            <ng-container matColumnDef="actions">
                <td *matCellDef="let webhook" class="w-40 text-right" mat-cell>
                    <button (click)="toggleWebhook(webhook)"
                            mat-flat-button
                            color="{{ webhook.enabled ? 'warn' : 'primary' }}"
                            [disabled]="isWorkflowWebhook(webhook)">
                        {{ ('private.application.webhooks.actions.' + (webhook.enabled ? 'disable' : 'enable')) | translate }}
                    </button>
                </td>
            </ng-container>
            <tr *matRowDef="let row; columns: displayedColumns;" mat-row></tr>
        </table>
    </app-wrapper-spinner>
</app-application-settings>
