import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';

@Component({
  selector: 'app-delivery-menu',
  templateUrl: './delivery-menu.component.html',
  styleUrls: ['./delivery-menu.component.scss']
})
export class DeliveryMenuComponent implements OnInit {

    hasOpenEvent: boolean;
    @Input() panelOpenState: boolean;
    @Output() openEvent = new EventEmitter<void>();
    @Output() closeEvent = new EventEmitter<void>();

  constructor() { }

    ngOnInit(): void {
        this.hasOpenEvent = this.openEvent.observers.length > 0;
    }

    openPanel(): void {
        this.openEvent.emit();
    }

    closePanel(): void {
        this.closeEvent.emit();
    }

}
