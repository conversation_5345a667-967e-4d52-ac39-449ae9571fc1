<button mat-icon-button
        (click)="$event.stopPropagation()"
        [matMenuTriggerFor]="actionsMenu"
        title="Actions">
    <mat-icon svgIcon="more_vert"></mat-icon>
</button>
<mat-menu #actionsMenu="matMenu">
    <ng-template matMenuContent>
        <button mat-menu-item (click)="openPanel()" *ngIf="hasOpenEvent && !panelOpenState">
            <mat-icon svgIcon="keyboard_arrow_down"></mat-icon>
            <span>{{ 'private.common.menu.open' | translate}}</span>
        </button>
        <button mat-menu-item (click)="closePanel()" *ngIf="hasOpenEvent && panelOpenState">
            <mat-icon svgIcon="keyboard_arrow_up"></mat-icon>
            <span>{{ 'private.common.menu.close' | translate}}</span>
        </button>
    </ng-template>
</mat-menu>

