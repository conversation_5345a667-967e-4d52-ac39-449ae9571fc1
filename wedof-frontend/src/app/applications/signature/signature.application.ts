import {Application, staticImplements} from '../shared/application.interface';
import {application} from '../shared/application.decorator';
import {SignatureSettingsComponent} from './settings/signature.settings.component';

@application()
@staticImplements<Application>()
export class SignatureApplication {
    public static appName(): string {
        return 'Signature de documents';
    }

    public static appId(): string {
        return 'signature';
    }

    public static beta(): boolean {
        return false;
    }

    public static isSubscriptionOption(): boolean {
        return true;
    }

    public static hasTrialAvailable(): boolean {
        return true;
    }

    public static components(): any {
        return [
            SignatureSettingsComponent
        ];
    }

    public static priority(): number {
        return 998;
    }

    public static routing(): any {
        return {
            path: SignatureApplication.appId(),
            children: [{
                path: 'reglages',
                component: SignatureSettingsComponent
            }]
        };
    }

    public static entryPoints(): any {
        return {};
    }
}

export interface SignatureMetadata {
    stamp?: string;
    signature?: string;
    initials?: string;
}
