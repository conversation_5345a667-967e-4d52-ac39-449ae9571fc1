import {Component, Injector} from '@angular/core';
import {Router} from '@angular/router';
import {HttpErrorResponse} from '@angular/common/http';
import {AbstractApplicationSettingsComponent} from '../../shared/settings/abstract-application-settings.component';
import {OrganismApplication} from '../../../shared/api/models/organism-application';
import {SignatureMetadata} from '../signature.application';
import {ViewFileDialogComponent} from '../../../shared/file/dialogs/view-file-dialog/view-file-dialog.component';
import {DocumentSignedStates, File} from '../../../shared/api/models/file';
import {MatDialog} from '@angular/material/dialog';
import {APIEndpoint} from '../../../shared/api/services/api-endpoint.enum';
import {TranslateService} from '@ngx-translate/core';
import {finalize, map} from 'rxjs/operators';

@Component({
    selector: 'signature-settings',
    templateUrl: './signature.settings.component.html',
    styleUrls: ['./signature.settings.component.scss']
})
export class SignatureSettingsComponent extends AbstractApplicationSettingsComponent<SignatureMetadata> {

    private static appId = 'signature';

    constructor(
        _router: Router,
        injector: Injector,
        private _dialog: MatDialog,
        private _translateService: TranslateService
    ) {
        super(SignatureSettingsComponent.appId, _router, injector);
    }

    onSuccess(organismApplication: OrganismApplication): void {
        this._router.navigate(['mes-applications']);
    }

    onError(httpErrorResponse: HttpErrorResponse): void {
    }

    loadSignatureSettingsForm(): void {
        const file = {
            fileName: this._translateService.instant('private.application.signature.signatureSettingsDialog'),
            fileType: 'sign',
            link: '/',
            signedState: DocumentSignedStates.NONE
        };
        this._dialog.open(ViewFileDialogComponent, {
            width: '80%',
            height: '90%',
            data: {
                file: file,
                auth: true,
                title: ((_file: File) => _file.fileName),
                src: (_file: File) =>
                    `${APIEndpoint.APP}/applications/${this.appId}/data/showSignatureSettingsForm`,
                files: [file],
                canUpdateFileState: (_ => false),
                updateFile: (fileId, body) => {
                    return this._organismApplicationService.postData(this.appId, 'saveSignatureSettingsData', {
                        id: fileId,
                        values: body['values']
                    }).pipe(
                        map((application) => this.initForm(application.metadata)),
                        finalize(() => this._dialog.closeAll())
                    );
                }
            }
        });
    }

    protected initForm(metadata: SignatureMetadata): void {
        this.metadata.next(metadata);
    }
}
