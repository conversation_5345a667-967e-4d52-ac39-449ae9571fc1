<app-application-settings [application]="application" *ngIf="metadata | async">
    <p class="text-lg font-medium mb-3">Faites signer vos documents générés automatiquement via Wedof</p>
    <p class="text-secondary mb-6">À partir de vos modèles de documents dans Wedof ajouter des signataires</p>
    <p class="text-xl font-medium mb-3">Configuration</p>
    <div class="mt-4 mb-2" *ngIf="metadata.value">
        <div>
            <div class="mb-1">
                <label
                    class="text-base font-medium">{{ 'private.application.signature.signatureTitle' | translate: '' }}</label>
            </div>
            <div class="flex items-center">
                <img src="{{metadata.value.signature}}" *ngIf="metadata.value.signature != null"
                     style="max-width:150px;">
                <p *ngIf="metadata.value.signature == null"
                   class="italic">{{ 'private.application.signature.configure' | translate: '' }}</p>
            </div>
        </div>
    </div>
    <div class="mt-4 mb-2" *ngIf="metadata.value">
        <div>
            <div class="mb-1">
                <label
                    class="text-base font-medium">{{ 'private.application.signature.initialsTitle' | translate: "" }}</label>
            </div>
            <div class="flex items-center">
                <img src="{{metadata.value.initials}}" *ngIf="metadata.value.initials != null"
                     style="max-width:150px;">
                <p *ngIf="metadata.value.initials == null"
                   class="italic">{{ 'private.application.signature.configure' | translate: '' }}</p>
            </div>
        </div>
    </div>
    <div class="mt-4 mb-2" *ngIf="metadata.value">
        <div>
            <div class="mb-1">
                <label
                    class="text-base font-medium">{{ 'private.application.signature.stampTitle' | translate: '' }}</label>
            </div>
            <div class="flex items-center">
                <img src="{{metadata.value.stamp}}" *ngIf="metadata.value.stamp != null" style="max-width: 150px">
                <p *ngIf="metadata.value.stamp == null"
                   class="italic">{{ 'private.application.signature.configure' | translate: '' }}</p>
            </div>
        </div>
    </div>
    <div class="mt-4 mb-3">
        <button mat-flat-button
                [color]="metadata.value.signature == null ? 'primary': null"
                [ngClass]="metadata.value.signature != null ? 'bg-gray-200': null"
                (click)="loadSignatureSettingsForm()">
            Configurer vos signatures
        </button>
    </div>
    <p class="text-xl font-medium mb-3 mt-6">Documentation</p>
    <p class="mb-3">Pour les <b>dossiers de formation</b>, ces documents peuvent être configurés au niveau des <a
        class="link" [routerLink]="['/profil/organisme']">paramètres de votre organisme</a></p>
    <p class="mb-3">Pour les <b>dossiers de certification</b>, ces documents peuvent être configurés au niveau des <a
        class="link" [routerLink]="['/certification/partenariats']">paramètres de chaque certification.</a></p>
    <p class="mb-3">Pour les <b>partenariats de certification</b>, ces documents peuvent être configurés au niveau des
        <a class="link" [routerLink]="['/certification/partenariats']">paramètres de chaque certification.</a></p>
    <p class="mb-3">Pour en savoir plus, <a class="mt-2" href="/aide/guides/applications/documents" target="_blank">consultez
        la documentation</a></p>
</app-application-settings>
