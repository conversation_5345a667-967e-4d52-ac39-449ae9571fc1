<ng-template #loadingBlock>
    <div class="m-auto py-24">
        <mat-progress-spinner class="mr-4" [diameter]="24" mode="indeterminate"></mat-progress-spinner>
    </div>
</ng-template>

<treo-card class="flex flex-col p-8 pb-0 w-3/4">

    <div class="breadcrumb mb-2">
        <div routerLink="../../" class="path cursor-pointer">
            {{ 'private.application.common.title' | translate }}
        </div>
        <span class="separator">/</span>
        <div class="path">
            {{ application.appName() }}
        </div>
    </div>

    <div class="flex flex-row items-center justify-between" *ngIf="application && organismApplication">
        <div class="flex flex-row items-center">
            <img [src]="'/assets/applications/'+application.appId()+'.png'" alt="{{application.appName()}}"
                 class="w-14 h-14 mr-4 rounded object-cover"/>
            <div class="text-2xl font-semibold leading-tight">
                {{ application.appName() }}
                <span class="text-sm"
                      *ngIf="organismApplication.endDate && isPendingDisable(organismApplication)">
                    {{ 'private.application.common.subtitle.pendingDisable' | translate: {endDate: organismApplication.endDate| date : 'dd/MM/yyyy'} }}
                </span>
                <span class="text-sm"
                      *ngIf="organismApplication.endDate && organismApplication.state == OrganismApplicationStates.TRIAL">
                    {{ 'private.application.common.subtitle.trial' | translate: {endDate: organismApplication.endDate| date : 'dd/MM/yyyy'} }}
                </span>
            </div>
        </div>
        <button *ngIf="addButton"
                (click)="addButtonClick()"
                class="ml-2"
                color="primary"
                type="button"
                mat-flat-button>
            {{ addButton | translate }}
        </button>
    </div>
    <div class="flex flex-col mt-5" *ngIf="application && organismApplication else loadingBlock">
        <form (submit)="submit()" class="flex flex-col">
            <ng-content></ng-content>
            <div
                class="flex items-center justify-between border-t -mx-8 mt-5 px-8 py-5 light:bg-cool-gray-50 dark:bg-cool-gray-700">
                <button (click)="disable(application)"
                        *ngIf="organismApplication.enabled && !isPendingDisable(organismApplication)" color="warn"
                        mat-flat-button type="button">
                    {{ 'private.application.common.actions.disable' | translate }}
                </button>
                <button (click)="cancelPendingDisable(application)"
                        *ngIf="organismApplication.enabled && isPendingDisable(organismApplication)" color="warn"
                        mat-flat-button type="button">
                    {{ 'private.application.common.actions.cancelPendingDisable' | translate }}
                </button>
                <div>
                    <a [routerLink]="['../..']" mat-button>
                        {{ 'private.application.common.actions.back' | translate }}
                    </a>
                    <button *ngIf="submitButton" [disabled]="submitButton.disabled || loading"
                            class="px-6 ml-3" color="{{submitButton.color ? submitButton.color : 'primary'}}"
                            mat-flat-button type="submit">
                        <span
                            *ngIf="!loading">{{ submitButton.value ? submitButton.value : ('private.application.common.actions.submit' | translate) }}</span>
                        <mat-progress-spinner class="m-auto" *ngIf="loading"
                                              [diameter]="24"
                                              mode="indeterminate"></mat-progress-spinner>
                    </button>
                </div>
            </div>
        </form>
    </div>
</treo-card>
