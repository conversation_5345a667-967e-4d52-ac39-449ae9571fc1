import {Directive, Injector, OnInit} from '@angular/core';
import {finalize} from 'rxjs/operators';
import {BehaviorSubject} from 'rxjs';
import {HttpErrorResponse} from '@angular/common/http';
import {SubmitSettingsButton} from './application-settings.component';
import {AbstractFormComponent} from '../../../shared/material/form/abstract-form.component';
import {OrganismApplicationService} from '../../../shared/api/services/organism-application.service';
import {Application} from '../application.interface';
import {OrganismApplication} from '../../../shared/api/models/organism-application';
import {ApplicationsService} from '../applications.service';
import {Router} from '@angular/router';

@Directive()
// tslint:disable-next-line: directive-class-suffix
export abstract class AbstractApplicationSettingsComponent<T> extends AbstractFormComponent<T> implements OnInit {

    application: Application;
    metadata: BehaviorSubject<T>;
    protected _applicationsService: ApplicationsService;
    protected _organismApplicationService: OrganismApplicationService;
    protected _router: Router;

    constructor(
        public appId: string,
        _router: Router,
        injector: Injector
    ) {
        super(injector);
        this.metadata = new BehaviorSubject<T>(null);
        this._applicationsService = injector.get(ApplicationsService);
        this._organismApplicationService = injector.get(OrganismApplicationService);
        this.application = this._applicationsService.get(appId);
        this._router = _router;
    }

    ngOnInit(): void {
        this.initForm(null);
        this._organismApplicationService.get(this.application.appId())
            .subscribe((organismApplication) => {
                if (!organismApplication.enabled) {
                    this._router.navigate(['mes-applications']);
                }
                this.metadata.next(organismApplication?.metadata as T);
                this.initForm(this.metadata.value);
            });
    }

    submit(): void {
        const metadata = this.formGroup.getRawValue();
        this.loading = true;
        this._organismApplicationService.setSettings(this.application.appId(), metadata)
            .pipe(
                finalize(() => this.loading = false)
            )
            .subscribe(
                (organismApplication) => this.onSuccess(organismApplication),
                (httpErrorResponse: HttpErrorResponse) => this.onError(httpErrorResponse)
            );
    }

    getSubmitButton(): SubmitSettingsButton {
        return {
            value: 'Enregistrer',
            color: 'primary',
            disabled: false
        };
    }

    protected abstract initForm(metadata: T): void;

    protected abstract onSuccess(organismApplication: OrganismApplication): void;

    protected abstract onError(httpErrorResponse: HttpErrorResponse): void;
}

