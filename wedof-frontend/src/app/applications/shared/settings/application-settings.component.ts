import {Component, EventEmitter, Input, On<PERSON><PERSON>roy, OnInit, Output} from '@angular/core';
import {Router} from '@angular/router';
import {Application} from '../application.interface';
import {OrganismApplicationService} from '../../../shared/api/services/organism-application.service';
import {SalesforceApplication} from '../../salesforce/salesforce.application';
import {SlackApplication} from '../../slack/slack.application';
import {ZohoApplication} from '../../zoho/zoho.application';
import {ZapierApplication} from '../../zapier/zapier.application';
import {WebhooksApplication} from '../../webhooks/webhooks.application';
import {HubspotApplication} from '../../hubspot/hubspot.application';
import {IciFormationApplication} from '../../ici-formation/ici-formation.application';
import {DendreoApplication} from '../../dendreo/dendreo.application';
import {DigiformaApplication} from '../../digiforma/digiforma.application';
import {MessageTemplateApplication} from '../../message-template/message-template.application';
import {StatusApplication} from '../../status/status.application';
import {MakeApplication} from '../../make/make.application';
import {ActivepiecesApplication} from '../../activepieces/activepieces.application';
import {DocumentApplication} from '../../document/document.application';
import {N8nApplication} from '../../n8n/n8n.application';
import {SignatureApplication} from '../../signature/signature.application';
import {OnlineformaproApplication} from '../../onlineformapro/onlineformapro.application';
import {WorkflowApplication} from '../../workflow/workflow.application';
import {FetchSubscription, SubscriptionState} from '../../../shared/api/state/subscription.state';
import {finalize, mergeMap, takeUntil} from 'rxjs/operators';
import {Select, Store} from '@ngxs/store';
import {Observable, Subject} from 'rxjs';
import {Subscription} from '../../../shared/api/models/subscription';
import {OrganismApplication, OrganismApplicationStates} from '../../../shared/api/models/organism-application';

@Component({
    selector: 'app-application-settings',
    templateUrl: './application-settings.component.html',
    styleUrls: ['./application-settings.component.scss']
})
export class ApplicationSettingsComponent implements OnInit, OnDestroy {

    OrganismApplicationStates = OrganismApplicationStates;

    subscription: Subscription = null;
    organismApplication: OrganismApplication = null;

    @Select(SubscriptionState.subscription) subscription$: Observable<Subscription>;
    @Input() addButton: string;
    @Input() submitButton: SubmitSettingsButton;
    @Input() application: Application;
    @Input() loading: boolean;
    @Output() formSubmitted = new EventEmitter<void>();
    @Output() addButtonClicked = new EventEmitter<void>();

    private _unsubscribeAll: Subject<any>;

    constructor(
        private _router: Router,
        private _store: Store,
        private _organismApplicationService: OrganismApplicationService
    ) {
        this.loading = false;
        this._unsubscribeAll = new Subject();

        // HACK DE C MORT
        // A priori, ça sert juste à faire croire à Angular qu'on se sert du module et qu'il faut
        // pas le supprimer dans les phases d'optimisation
        [
            ZohoApplication,
            ActivepiecesApplication,
            MakeApplication,
            ZapierApplication,
            N8nApplication,
            SlackApplication,
            SalesforceApplication,
            DendreoApplication,
            WebhooksApplication,
            HubspotApplication,
            IciFormationApplication,
            DigiformaApplication,
            MessageTemplateApplication,
            StatusApplication,
            DocumentApplication,
            SignatureApplication,
            OnlineformaproApplication,
            WorkflowApplication
        ].forEach(app => {
            app.appName();
        });
    }

    ngOnInit(): void {
        this.subscription$.pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe((subscription) => {
            this.subscription = subscription;
            this._organismApplicationService.get(this.application.appId()).subscribe((organismApplication) => {
                this.organismApplication = organismApplication;
            });
        });
    }

    disable(application: Application): void {
        this._organismApplicationService.disable(application.appId()).subscribe((organismApplication: OrganismApplication) => {
            this.organismApplication = organismApplication;
            if (application.isSubscriptionOption()) {
                this._store.dispatch(new FetchSubscription()).pipe(
                    mergeMap(() => {
                        return this._store.selectOnce(SubscriptionState.subscription);
                    }),
                    finalize(() => {
                        if (organismApplication.state === OrganismApplicationStates.DISABLED) {
                            this._router.navigate(['mes-applications']);
                        }
                    })
                );
            } else {
                if (organismApplication.state === OrganismApplicationStates.DISABLED) {
                    this._router.navigate(['mes-applications']);
                }
            }
        });
    }

    cancelPendingDisable(application: Application): void {
        this._organismApplicationService.enable(application.appId()).subscribe((organismApplication: OrganismApplication) => {
            this.organismApplication = organismApplication;
            if (application.isSubscriptionOption()) {
                this._store.dispatch(new FetchSubscription()).pipe(
                    mergeMap(() => {
                        return this._store.selectOnce(SubscriptionState.subscription);
                    })
                );
            }
        });
    }

    addButtonClick(): void {
        this.addButtonClicked.emit();
    }

    submit(): void {
        this.formSubmitted.emit();
    }

    /**
     * On destroy
     */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    isPendingDisable(organismApplication: OrganismApplication): boolean {
        const states = [OrganismApplicationStates.PENDING_DISABLE, OrganismApplicationStates.PENDING_DISABLE_TRIAL];
        return states.includes(organismApplication.state);
    }
}


export interface SubmitSettingsButton {
    value?: string;
    color?: string;
    disabled?: boolean;
}
