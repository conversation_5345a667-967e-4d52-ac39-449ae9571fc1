import {Component, OnInit} from '@angular/core';
import {ActivatedRoute, Router} from '@angular/router';
import {delay, tap} from 'rxjs/operators';

import {OrganismApplicationService} from '../../../shared/api/services/organism-application.service';
import {NavigationService} from '../../../shared/navigation/navigation.service';
import {Application} from '../application.interface';
import {ApplicationsService} from '../applications.service';

@Component({
    selector: 'app-oauth-code-management',
    templateUrl: './oauth-code-management.component.html',
    styleUrls: ['./oauth-code-management.component.scss']
})
export class OauthCodeManagementComponent implements OnInit {

    application: Application;
    loading = true;

    constructor(
        private _router: Router,
        private _activatedRoute: ActivatedRoute,
        private _organismApplicationService: OrganismApplicationService,
        private _applicationsService: ApplicationsService,
        private _navigationService: NavigationService
    ) {
    }

    ngOnInit(): void {
        const applicationId = this._activatedRoute.snapshot.params.id;
        const queryParams = this._activatedRoute.snapshot.queryParams;
        if (!queryParams.code) {
            this._router.navigate(['mes-applications']);
            return;
        }
        this._navigationService.seDisplayLayout(false);
        this.application = this._applicationsService.get(applicationId);
        this._organismApplicationService
            .validateOAuth2(applicationId, queryParams)
            .pipe(
                delay(2500),
                tap((organismApplication) => {
                    if (organismApplication.enabled) {
                        this.loading = false;
                        this._navigationService.seDisplayLayout(true);
                        this._router.navigate(['mes-applications', applicationId, 'reglages']);
                    }
                })
            )
            .subscribe();
    }

}
