<div class="content-layout fullwidth-basic-normal-scroll">
    <div class="main">

        <div class="content flex flex-wrap w-full items-center align-middle">

            <treo-card class="flex flex-col items-center max-w-80 w-full p-8 m-auto">
                <div class="flex items-center justify-center p-5 rounded-full">
                    <img [src]="'/assets/applications/' + application.appId() + '.png'"
                         alt="{{ application.appName() }}"
                         class="w-32 h-32 rounded-full">
                </div>
                <div class="text-2xl font-semibold leading-tight text-center mt-6">
                    {{ application.appName() }}
                </div>
                <div class="text-center text-secondary mt-3">
                    {{ 'private.application.oauth.description' | translate: {name: application.appName()} }}
                </div>
                <div class="text-center text-secondary mt-3">
                    <mat-progress-spinner *ngIf="loading; else okBlock" [diameter]="50"
                                          mode="indeterminate"></mat-progress-spinner>
                    <ng-template #okBlock>
                        <mat-icon color="primary" svgIcon="check"></mat-icon>
                    </ng-template>
                </div>

            </treo-card>

        </div>

    </div>
</div>
