{{ applicationsService.addTemplate('tpl-webhooks-card-registration-folder-side', template3) }}
<ng-template #template3 let-registrationFolder="registrationFolder" let-isDataLoaded="isDataLoaded"
             let-context="context" let-activatedRoute="activatedRoute"
             let-goToShortCutWhenLoaded="goToShortCutWhenLoaded">
    <app-delivery-card id="webhooks" class="max-h-80 mb-3"
                       [entityId]="registrationFolder.externalId"
                       [entityClass]="EntityClass.REGISTRATION_FOLDER"
                       [context]="context"
                       (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"></app-delivery-card>
</ng-template>

{{ applicationsService.addTemplate('tpl-webhooks-card-certification-folder-side', template4) }}
<ng-template #template4 let-certificationFolder="certificationFolder" let-isDataLoaded="isDataLoaded"
             let-context="context" let-activatedRoute="activatedRoute"
             let-goToShortCutWhenLoaded="goToShortCutWhenLoaded">
    <app-delivery-card id="webhooks" class="max-h-80 mb-3"
                       [entityId]="certificationFolder.externalId"
                       [entityClass]="EntityClass.CERTIFICATION_FOLDER"
                       [context]="context"
                       (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"></app-delivery-card>
</ng-template>

{{ applicationsService.addTemplate('tpl-messages-card-registration-folder-side', template5) }}

<ng-template #template5 let-registrationFolder="registrationFolder" let-isDataLoaded="isDataLoaded"
             let-context="context" let-activatedRoute="activatedRoute"
             let-goToShortCutWhenLoaded="goToShortCutWhenLoaded">
    <app-message-card id="messages" class="max-h-80 mb-3"
                      [entityId]="registrationFolder.externalId"
                      [entityClass]="EntityClass.REGISTRATION_FOLDER"
                      [context]="context"
                      [isOwner]="true"
                      (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"></app-message-card>
</ng-template>

{{ applicationsService.addTemplate('tpl-messages-card-certification-folder-side', template6) }}
<ng-template #template6 let-certificationFolder="certificationFolder" let-isDataLoaded="isDataLoaded"
             let-context="context" let-activatedRoute="activatedRoute" let-isOwner="isOwner"
             let-goToShortCutWhenLoaded="goToShortCutWhenLoaded">
    <app-message-card id="messages" class="max-h-80 mb-3"
                      [entityId]="certificationFolder.externalId"
                      [entityClass]="EntityClass.CERTIFICATION_FOLDER"
                      [context]="context"
                      [isOwner]="isOwner"
                      (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"></app-message-card>
</ng-template>

{{ applicationsService.addTemplate('tpl-message-templates-card-certification-side', template7) }}
<ng-template #template7 let-certification="certification" let-isDataLoaded="isDataLoaded"
             let-context="context" let-activatedRoute="activatedRoute"
             let-goToShortCutWhenLoaded="goToShortCutWhenLoaded">
    <app-message-template-card id="messages" class="mb-3"
                               [certification]="certification"
                               [context]="context"
                               (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)">
    </app-message-template-card>
</ng-template>

{{ applicationsService.addTemplate('tpl-messages-card-proposal-side', template8) }}
<ng-template #template8 let-proposal="proposal" let-isDataLoaded="isDataLoaded"
             let-context="context" let-activatedRoute="activatedRoute"
             let-goToShortCutWhenLoaded="goToShortCutWhenLoaded">
    <app-message-card *ngIf="proposal.isIndividual" id="messages" class="mb-3"
                      [entityId]="proposal.code"
                      [entityClass]="EntityClass.PROPOSAL"
                      [context]="context"
                      [isOwner]="true"
                      (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"></app-message-card>
</ng-template>

{{ applicationsService.addTemplate('tpl-messages-card-certification-partner-side', template9) }}
<ng-template #template9 let-certificationPartner="certificationPartner" let-isDataLoaded="isDataLoaded"
             let-context="context" let-activatedRoute="activatedRoute"
             let-goToShortCutWhenLoaded="goToShortCutWhenLoaded">
    <app-message-card id="messages" class="mb-3"
                      [entityId]="certificationPartner.id"
                      [entityClass]="EntityClass.CERTIFICATIONS_PARTNER"
                      [context]="context"
                      [isOwner]="true"
                      (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"></app-message-card>
</ng-template>


{{ applicationsService.addTemplate('tpl-webhooks-card-certification-partner-side', template10) }}
<ng-template #template10 let-certificationPartner="certificationPartner" let-isDataLoaded="isDataLoaded"
             let-context="context" let-activatedRoute="activatedRoute"
             let-goToShortCutWhenLoaded="goToShortCutWhenLoaded">
    <app-delivery-card id="webhooks" class="max-h-80 mb-3"
                       [entityId]="certificationPartner.id"
                       [entityClass]="EntityClass.CERTIFICATIONS_PARTNER"
                       [context]="context"
                       (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"></app-delivery-card>
</ng-template>

{{ applicationsService.addTemplate('tpl-workflow-runs-card-registration-folder-side', template11) }}
<ng-template #template11 let-registrationFolder="registrationFolder" let-isDataLoaded="isDataLoaded"
             let-context="context" let-activatedRoute="activatedRoute"
             let-goToShortCutWhenLoaded="goToShortCutWhenLoaded">
    <app-workflow-runs-card id="workflowRuns" class="max-h-80 mb-3"
                            [entityId]="registrationFolder.externalId"
                            [entityClass]="EntityClass.REGISTRATION_FOLDER"
                            [context]="context"
                            (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"></app-workflow-runs-card>
</ng-template>

{{ applicationsService.addTemplate('tpl-workflow-runs-card-certification-folder-side', template12) }}
<ng-template #template12 let-certificationFolder="certificationFolder" let-isDataLoaded="isDataLoaded"
             let-context="context" let-activatedRoute="activatedRoute"
             let-goToShortCutWhenLoaded="goToShortCutWhenLoaded">
    <app-workflow-runs-card id="workflowRuns" class="max-h-80 mb-3"
                            [entityId]="certificationFolder.externalId"
                            [entityClass]="EntityClass.CERTIFICATION_FOLDER"
                            [context]="context"
                            (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"></app-workflow-runs-card>
</ng-template>
