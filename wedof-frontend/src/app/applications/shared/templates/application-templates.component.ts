import {Component} from '@angular/core';
import {ApplicationsService} from '../applications.service';
import {EntityClass} from '../../../shared/utils/enums/entity-class';

@Component({
    selector: 'application-templates',
    templateUrl: './application-templates.component.html',
    styleUrls: ['./application-templates.component.scss'],
})
export class ApplicationTemplatesComponent {
    constructor(
        public applicationsService: ApplicationsService
    ) {
    }

    readonly EntityClass = EntityClass;
}
