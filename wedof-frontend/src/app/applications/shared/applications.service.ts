import {Injectable, TemplateRef} from '@angular/core';
import {combineLatest, forkJoin, Observable, of} from 'rxjs';
import {map, mergeMap} from 'rxjs/operators';
import {registeredApplications} from './application.decorator';
import {EnvironmentService} from '../../shared/environment/environment.service';
import {OrganismApplicationService} from '../../shared/api/services/organism-application.service';
import {Application} from './application.interface';
import {OrganismApplicationEntryPoint} from '../../shared/api/models/applications';
import {Select} from '@ngxs/store';
import {SubscriptionState} from '../../shared/api/state/subscription.state';
import {Subscription} from '../../shared/api/models/subscription';
import {sortBy} from 'lodash-es';

@Injectable()
export class ApplicationsService {

    templates = {};

    @Select(SubscriptionState.subscription) subscription$: Observable<Subscription>;

    constructor(
        public environmentService: EnvironmentService,
        private _organismApplicationService: OrganismApplicationService,
    ) {

    }

    findAll(): Application[] {
        const availableApplications = registeredApplications.filter((application) => {
            return this.environmentService.isEnableBetaFeatures() ? true : application.beta() === false;
        });
        return sortBy(availableApplications, application => application.priority ? (-application.priority()) : 0);
    }

    get(appId: string): Application {
        return this.findAll().find(app => app.appId() === appId);
    }

    findAllWithEntryPoint(entryPoint: string): Observable<Application[]> {
        return of(registeredApplications.filter((application) => {
            return (this.environmentService.isEnableBetaFeatures() ? true : application.beta() === false) && application.entryPoints() && application.entryPoints()[entryPoint];
        }));
    }

    addTemplate(name: string, ref): any {
        this.templates[name] = ref;
        return null;
    }

    getTemplate(name: string): TemplateRef<any> {
        return this.templates[name];
    }

    getOrganismApplicationEntryPoints(entryPoint: string): Observable<OrganismApplicationEntryPoint[]> {
        const applications$ = this.findAllWithEntryPoint(entryPoint);
        const organismApplications$ = this._organismApplicationService.findAll();
        return combineLatest([
            organismApplications$,
            applications$,
            this.subscription$
        ]).pipe(
            map(([organismApplications, applications, subscription]) => {
                organismApplications = organismApplications.filter(organismApplication => {
                    return applications.some(app => app.appId() === organismApplication.appId);
                });
                return {organismApplications, subscription};
            })
        ).pipe(
            mergeMap(applicationsAndSubscription => {
                const organismApplications = applicationsAndSubscription.organismApplications;
                const subscription = applicationsAndSubscription.subscription;
                const apps = organismApplications
                    .sort((applicationA, applicationB) => {
                        const appA = this.get(applicationA.appId);
                        const appB = this.get(applicationB.appId);
                        const priorityA = appA?.priority ? appA.priority() : 0;
                        const priorityB = appB?.priority ? appB.priority() : 0;
                        return priorityB - priorityA; // Higher priority first
                    })
                    .map(organismApplication => {
                        return of(this.get(organismApplication.appId)).pipe(
                            map((application: Application) => {
                                return application.entryPoints()[entryPoint].map(data => {
                                    return {
                                        params: data,
                                        allowedInSubscription: subscription.allowedApps.includes(application.appId()),
                                        entryPointResolved: entryPoint,
                                        organismApplication: organismApplication
                                    };
                                });
                            })
                        );
                    });
                return apps.length ? forkJoin(apps) : of([]);
            }),
            map((entries: OrganismApplicationEntryPoint[][]) => {
                return entries.length ? entries.reduce((arr: OrganismApplicationEntryPoint[], curr: OrganismApplicationEntryPoint[]) => [...arr, ...curr]) : [];
            })
        );
    }
}
