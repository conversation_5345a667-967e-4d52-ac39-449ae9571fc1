import {Application} from './application.interface';
import {Routes} from '@angular/router';

export const registeredApplications = [];

export function application(): any {
    return (target: Application): void => {
        registeredApplications.push(target);
    };
}

export function applicationsRouting(): Routes {
    const routing = [];
    registeredApplications.forEach((app: Application) => {
        if (app.routing()) {
            routing.push(app.routing());
        }
    });
    return routing;
}
