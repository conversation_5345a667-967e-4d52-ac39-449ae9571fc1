/* tslint:disable */

import {Route} from "@angular/router";

interface ApplicationInterfaceInstance {
    //instanceMethod();
}

export interface Application {
    //static declarations
    new(): ApplicationInterfaceInstance;

    appName(): string;

    appId(): string;

    beta(): boolean;

    routing(): Route;

    entryPoints(): any;

    components(): [];

    priority(): number;

    isSubscriptionOption(): boolean;

    hasTrialAvailable(): boolean;
}

/* class decorator */
export function staticImplements<T>(): any {
    return <U extends T>(constructor: U) => {
        constructor
    };
}

/* tslint:enable */
