import {Application, staticImplements} from '../shared/application.interface';
import {application} from '../shared/application.decorator';
import {SalesforceSettingsComponent} from './settings/salesforce.settings.component';

@application()
@staticImplements<Application>()
export class SalesforceApplication {
    public static appName(): string {
        return 'Salesforce';
    }

    public static appId(): string {
        return 'salesforce';
    }

    public static beta(): boolean {
        return false;
    }

    public static isSubscriptionOption(): boolean {
        return false;
    }

    public static hasTrialAvailable(): boolean {
        return false;
    }

    public static components(): any {
        return [
            SalesforceSettingsComponent
        ];
    }

    public static priority(): number {
        return 699;
    }

    public static routing(): any {
        return {
            path: SalesforceApplication.appId(),
            children: [{
                path: 'reglages',
                component: SalesforceSettingsComponent
            }]
        };
    }

    public static entryPoints(): any {
        return {};
    }

}

export interface SalesforceMetadata {
    domain: string;
    ready: boolean;
    sendPayments: boolean;
}
