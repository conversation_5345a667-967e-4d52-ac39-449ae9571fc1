import {Component, Injector} from '@angular/core';
import {HttpErrorResponse} from '@angular/common/http';
import {AbstractApplicationSettingsComponent} from '../../shared/settings/abstract-application-settings.component';
import {SalesforceMetadata} from '../salesforce.application';
import {OrganismApplication} from '../../../shared/api/models/organism-application';
import {SubmitSettingsButton} from '../../shared/settings/application-settings.component';
import {Router} from '@angular/router';
import {MatCheckboxChange} from '@angular/material/checkbox';
import {FormControl} from '@angular/forms';
import {ApiError} from '../../../shared/errors/errors.types';
import {SnackBarComponent} from '../../../shared/material/snack-bar/snack-bar.component';
import {displayTextSnackBar} from '../../../shared/utils/displayTextSnackBar';
import {MatSnackBar} from '@angular/material/snack-bar';
import {TranslateService} from '@ngx-translate/core';

@Component({
    selector: 'app-salesforce-settings',
    templateUrl: './salesforce.settings.component.html',
    styleUrls: ['./salesforce.settings.component.scss']
})
export class SalesforceSettingsComponent extends AbstractApplicationSettingsComponent<SalesforceMetadata> {

    status: boolean;
    ready: boolean;
    showSendPaymentOption = false;

    constructor(
        injector: Injector,
        _router: Router,
        private _snackBar: MatSnackBar,
        private _translateService: TranslateService
    ) {
        super('salesforce', _router, injector);
        this.ready = false;
        this.status = false;
        this._organismApplicationService.getData(this.application.appId(), 'status').subscribe((data) => {
            this.status = data.status;
            this.ready = true;
            this.initForm(this.metadata.value);
        });
    }

    onSuccess(organismApplication: OrganismApplication): void {
        if (!this.status) {
            this._organismApplicationService.getOAuth2Authorization(organismApplication.appId).subscribe(() => {
                this.status = true;
                this.initForm(this.metadata.value);
            });
        }
    }

    handleSendPayment(event: MatCheckboxChange): void {
        this.loading = true;
        this._organismApplicationService.postData(this.application.appId(), 'updatePayment', {sendPayments: event.checked}).subscribe(
            (data) => {
                this.loading = false;
                this.formGroup.get('sendPayments').setValue(data.sendPayments);
                this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(this._translateService.instant('private.application.salesforce.form.sendPayments.snack')));
            },
            (httpErrorResponse: HttpErrorResponse) => {
                this.loading = false;
                this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar((httpErrorResponse.error as ApiError).detail, 5000, 'red'));
            });
    }

    onError(httpErrorResponse: HttpErrorResponse): void {
    }

    getSubmitButton(): SubmitSettingsButton {
        return {
            color: 'primary',
            value: 'Connecter',
            disabled: this.status
        };
    }

    protected initForm(metadata: SalesforceMetadata): void {
        this.formGroup = this._fb.group({
            domain: {value: metadata?.domain, disabled: this.status}
        });
        if (metadata?.domain && this.ready) {
            this.showSendPaymentOption = true;
            this.formGroup.addControl('sendPayments', new FormControl(metadata.sendPayments));
        }
    }
}

