<app-application-settings (formSubmitted)="submit()"
                          [application]="application"
                          [submitButton]="getSubmitButton()"
                          [loading]="loading"
                          *ngIf="this.ready">
    <treo-message type="primary" [showIcon]="false" class="mt-2 flex-auto" appearance="outline">
        {{ 'private.application.salesforce.information' | translate }}
    </treo-message>
    <div class="mt-4">
        <app-form-field-static class="col-span-6"
                               icon="install_desktop"
                               [copy]="true"
                               [label]="'private.application.salesforce.installation.label' | translate"
                               [value]="'private.application.salesforce.installation.link' | translate">
        </app-form-field-static>
        <app-form-field-static class="col-span-6"
                               icon="info"
                               [type]="'text-html'"
                               [label]="'private.application.salesforce.documentation.label' | translate"
                               [value]="'private.application.salesforce.documentation.link' | translate">
        </app-form-field-static>
    </div>
    <div  [formGroup]="formGroup">
        <div *ngIf="!status"
             class="mt-4">
            <mat-form-field class="w-full">
                <mat-label>{{ 'private.application.salesforce.form.domain.label' | translate }}</mat-label>
                <span matPrefix>https://</span>
                <input formControlName="domain" matInput placeholder="domaine" type="text">
                <span matSuffix>.my.salesforce.com</span>
            </mat-form-field>
        </div>
        <mat-checkbox *ngIf="showSendPaymentOption" (change)="handleSendPayment($event)" class="mt-2"
                      formControlName="sendPayments">
            {{'private.application.salesforce.form.sendPayments.label' | translate}}
        </mat-checkbox>
    </div>

    <div *ngIf="metadata | async"
         class="flex flex-row items-center mt-4">
        <div class="font-medium leading-tight">
            {{ (this.status ? 'private.application.salesforce.synchronization.status.success' : 'private.application.salesforce.synchronization.status.error') | translate:metadata.value }}
        </div>
        <mat-icon [ngClass]="{'text-green':status, 'text-red':!status}" class="ml-1"
                  svgIcon="{{ status ? 'check_circle' : 'cancel' }}"></mat-icon>
    </div>
</app-application-settings>
