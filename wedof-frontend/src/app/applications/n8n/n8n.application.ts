import {Application, staticImplements} from '../shared/application.interface';
import {N8nSettingsComponent} from './settings/n8n.settings.component';
import {application} from '../shared/application.decorator';

@application()
@staticImplements<Application>()
export class N8nApplication {
    public static appName(): string {
        return 'n8n';
    }

    public static appId(): string {
        return 'n8n';
    }

    public static beta(): boolean {
        return false;
    }

    public static isSubscriptionOption(): boolean {
        return false;
    }

    public static components(): any {
        return [
            N8nSettingsComponent
        ];
    }

    public static hasTrialAvailable(): boolean {
        return false;
    }

    public static priority(): number {
        return 800;
    }

    public static routing(): any {
        return {
            path: N8nApplication.appId(),
            children: [{
                path: 'reglages',
                component: N8nSettingsComponent
            }]
        };
    }

    public static entryPoints(): any {
        return {};
    }
}

export interface N8nMetadata {
    name: string;
}
