import {Component, Injector} from '@angular/core';
import {Validators} from '@angular/forms';
import {Router} from '@angular/router';
import {HttpErrorResponse} from '@angular/common/http';
import {AbstractApplicationSettingsComponent} from '../../shared/settings/abstract-application-settings.component';
import {OrganismApplication} from '../../../shared/api/models/organism-application';
import {N8nMetadata} from '../n8n.application';

@Component({
    selector: 'n8n-settings',
    templateUrl: './n8n.settings.component.html',
    styleUrls: ['./n8n.settings.component.scss']
})
export class N8nSettingsComponent extends AbstractApplicationSettingsComponent<N8nMetadata> {

    LINK_N8N = 'https://www.npmjs.com/package/n8n-nodes-wedof';

    constructor(
        _router: Router,
        injector: Injector,
    ) {
        super('n8n', _router, injector);
    }

    onSuccess(organismApplication: OrganismApplication): void {
        this._router.navigate(['mes-applications']);
    }

    onError(httpErrorResponse: HttpErrorResponse): void {
    }

    protected initForm(metadata: N8nMetadata): void {
        this.formGroup = this._fb.group({
            name: [metadata?.name, [Validators.required]]
        });
    }
}
