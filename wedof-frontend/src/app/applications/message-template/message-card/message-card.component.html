<ng-container *ngIf="!context.allowedInSubscription else showCard">
    <app-subscription-applications-card [type]="'messageTemplates'"
                                        [fromPage]="entityClass === 'RegistrationFolder' ? 'messageTemplatesTraining' :
                                         entityClass === 'CertificationFolder' ? 'messageTemplatesCertifier' : ''"
                                        [organism]="organism"
                                        [subscription]="subscription"
                                        [subscriptionType]="entityClass"
    >
    </app-subscription-applications-card>
</ng-container>

<ng-template #showCard>
    <mat-card class="mat-card flex h-full flex-auto flex-col mt-3 p-5 pb-0 pt-3 border shadow-none sm"
              [ngClass]="{'card-loading':cardLoading}">
        <div class="flex items-center mb-2">
            <mat-icon [matBadge]="total > 0 ? total.toString() : null"
                      matBadgePosition="below after"
                      matBadgeSize="small"
                      class="mr-3 card-loading-show text-4xl"
                      color="primary">email
            </mat-icon>
            <div class="flex items-center">
            <span class="text-xl font-semibold card-loading-show">
                {{ context.params.title | translate }}
            </span>
            </div>
            <div class="ml-auto -mr-4 card-loading-hidden">
                <button type="button" mat-icon-button
                        (click)="messageTemplateSettings()">
                    <mat-icon [matTooltip]="'private.common.message-templates.settings' | translate" color="text-gray">
                        settings
                    </mat-icon>
                </button>
                <button type="button" mat-icon-button *ngIf="hasAccessToApplication && isOwner"
                        (click)="sendMessageHidden()">
                    <mat-icon [matTooltip]="'common.actions.sendNew' | translate" color="text-gray" svgIcon="add_circle">
                    </mat-icon>
                </button>
            </div>
        </div>
        <div *ngIf="!cardLoading"
             class="flex flex-col mt-3 mb-3 overflow-y-auto"
             infiniteScroll
             [infiniteScrollDistance]="2"
             [infiniteScrollThrottle]="50"
             (scrolled)="onScroll()"
             [scrollWindow]="false">
            <div *ngIf="messages?.length; else noResult">
                <div *ngFor="let message of messages"
                     class="mb-2">
                    <div class="flex flex-row justify-between">
                        <div class="flex items-center">
                            <mat-icon class="mr-1 icon-size-18"
                                      *ngIf="message.state === messageState.FAILED || messageStateNotSent.includes(message.state) ; else showState"
                                      color="warn"
                                      [title]="'private.common.message-templates.messages.state.' + message.state | translate">
                                {{ message.state === messageState.FAILED ? 'warning' : 'cancel' }}
                            </mat-icon>
                            <ng-template #showState>
                                <mat-icon class="mr-1 icon-size-18" color="primary"
                                          [title]="'private.common.message-templates.messages.state.' + message.state | translate">
                                    {{ message.state === messageState.SCHEDULED ? 'schedule_send' : message.state === messageState.SENT ? 'send' : 'outgoing_mail' }}
                                </mat-icon>
                            </ng-template>
                            <div class="pl-1">
                                <p>
                                    <a href="/app/public/message/html/{{message.uuid}}"
                                      target="_blank">
                                        <ng-container *ngIf="message._links.messageTemplate.title?.length; else showSubject">
                                                      {{ message._links.messageTemplate.title }}
                                        </ng-container>
                                        <ng-template #showSubject>
                                            {{ message.type === messageTemplateType.EMAIL ? message.subject : 'SMS' }}
                                        </ng-template>
                                    </a>
                                </p>
                                <p *ngIf="message.state === messageState.SCHEDULED || message.state === messageState.FAILED"
                                   class="text-disabled text-sm"
                                   [matTooltip]="message.scheduledAt | date:'dd/MM/yyyy HH:mm'">
                                    {{
                                    'private.common.message-templates.messages.' + message.state | translate: {
                                        type: message.type,
                                        date: daysAgo(message.scheduledAt),
                                        email: message.to
                                    }
                                    }}
                                </p>
                                <p *ngIf="message.state === messageState.SENT" class="text-disabled text-sm"
                                   [matTooltip]="message.sentAt | date:'dd/MM/yyyy HH:mm'">
                                    {{
                                    'private.common.message-templates.messages.sent' | translate: {
                                        type: message.type,
                                        date: daysAgo(message.sentAt),
                                        email: message.to
                                    }
                                    }}
                                </p>
                                <p *ngIf="messageStateNotSent.includes(message.state)" class="text-disabled text-sm"
                                   [matTooltip]="message.scheduledAt | date:'dd/MM/yyyy HH:mm'">
                                    {{ 'private.common.message-templates.messages.' + message.state | translate }}
                                </p>
                            </div>
                        </div>
                        <app-message-menu *ngIf="!isLoading; else showLoading" [message]="message"
                                          [hasAccessToApplication]="hasAccessToApplication"
                                          [isOwner]="isOwner"
                                          (forceResend)="forceResend($event)"
                                          (delete)="delete($event)">
                        </app-message-menu>
                        <ng-template #showLoading>
                            <mat-progress-spinner class="mr-4" [diameter]="24"
                                                  mode="indeterminate"></mat-progress-spinner>
                        </ng-template>
                    </div>
                </div>
            </div>
            <ng-template #noResult>
                <p class="mt-3 mb-3 text-center m-auto">
                    {{ 'private.common.message-templates.messages.noData' | translate }}
                </p>
            </ng-template>
        </div>
        <div *ngIf="errorMessages.length" class="flex items-center mt-2 mb-2">
            <treo-message class="w-full" appearance="outline" [showIcon]="false" type="error">
                <ul>
                    <li *ngFor="let errorMessage of errorMessages" class="break-all">
                        {{ errorMessage }}
                    </li>
                </ul>
            </treo-message>
        </div>
    </mat-card>
</ng-template>
