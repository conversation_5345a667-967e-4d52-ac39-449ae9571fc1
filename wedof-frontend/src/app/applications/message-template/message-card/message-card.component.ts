import {Component, ElementRef, Input, <PERSON><PERSON><PERSON><PERSON>, On<PERSON>estroy, SimpleChanges} from '@angular/core';
import {OrganismApplicationEntryPoint} from '../../../shared/api/models/applications';
import {filter, finalize, switchMap, takeUntil} from 'rxjs/operators';
import {combineLatest, Observable, Subject} from 'rxjs';
import {Message, MessageState} from '../../../shared/api/models/message';
import {MessageService} from '../../../shared/api/services/message.service';
import {Select} from '@ngxs/store';
import {OrganismState} from '../../../shared/api/state/organism.state';
import {Organism} from '../../../shared/api/models/organism';
import {Subscription} from '../../../shared/api/models/subscription';
import {SubscriptionState} from '../../../shared/api/state/subscription.state';
import {Router} from '@angular/router';
import {DeletionConfirmationComponent} from '../../../shared/material/action-confirmation/deletion-confirmation.component';
import {MatDialog} from '@angular/material/dialog';
import {daysAgo} from '../../../shared/utils/date-utils';
import {BaseCardComponentDirective, RequiredCallSuper} from '../../../shared/utils/base-card/base-card.directive';
import {OrganismApplicationService} from '../../../shared/api/services/organism-application.service';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../../shared/errors/errors.types';
import {CustomizeMessageComponent} from '../customize-message/customize-message.component';
import {MessageTemplateType} from '../../../shared/api/models/message-template';

@Component({
    selector: 'app-message-card',
    templateUrl: './message-card.component.html',
    styleUrls: ['./message-card.component.scss']
})
export class MessageCardComponent extends BaseCardComponentDirective implements OnChanges, OnDestroy {

    static COMPONENT_ID = 'messages';
    total = 0;
    messages: Message[];
    messagesLimit = 10;
    messageState = MessageState;
    messageStateNotSent: MessageState[];
    organism: Organism;
    subscription: Subscription;
    hasAccessToApplication = false;
    isLoading = false;
    errorMessages: string[] = [];
    messageTemplateType = MessageTemplateType;

    @Input() context: OrganismApplicationEntryPoint;
    @Input() entityId: number | string;
    @Input() entityClass: string;
    @Input() isOwner: boolean;

    private _unsubscribeAll: Subject<void> = new Subject();

    @Select(OrganismState.organism) organism$: Observable<Organism>;
    @Select(SubscriptionState.subscription) subscription$: Observable<Subscription>;

    constructor(private _organismApplicationService: OrganismApplicationService,
                private _messageService: MessageService,
                private _router: Router,
                private _dialog: MatDialog,
                private _el: ElementRef) {
        super(MessageCardComponent.COMPONENT_ID, _el);
        this.messageStateNotSent = [
            this.messageState.NOT_SENT,
            this.messageState.NOT_SENT_UNAUTHORIZED,
            this.messageState.NOT_SENT_ENFORCED_CONDITIONS,
            this.messageState.NOT_SENT_MISSING_DATA
        ];
    }

    ngOnChanges(changes: SimpleChanges): void {
        this.panelLoading();
        this.initForm();
    }

    initForm(): void {
        combineLatest([
            this.organism$,
            this.subscription$
        ]).pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe(([organism, subscription]) => {
            this.organism = organism;
            this.subscription = subscription;
            if (this.context.allowedInSubscription) {
                this.hasAccessToApplication = this.context.organismApplication.enabled;
                this._messageService.listByEntity(this.entityClass, this.entityId, {limit: this.messagesLimit}).subscribe((messagesResponse) => {
                    this.messages = messagesResponse.payload;
                    this.total = messagesResponse.total;
                    this.panelLoaded();
                });
            } else {
                this.panelLoaded();
            }
        });
    }

    onScroll(): void {
        const displayedPages = this.messages.length / this.messagesLimit;
        if (Number.isInteger(displayedPages)) {
            this._messageService.listByEntity(this.entityClass, this.entityId, {
                limit: this.messagesLimit,
                page: displayedPages + 1
            })
                .pipe(takeUntil(this._unsubscribeAll))
                .subscribe((response) => {
                    this.messages.push(...response.payload);
                });
        }
    }


    ngOnDestroy(): RequiredCallSuper {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
        return super.ngOnDestroy();
    }

    messageTemplateSettings(): void {
        this._router.navigate(['mes-applications', 'message-templates', 'reglages']);
    }

    delete(messageToDelete: Message): void {
        const dialogRef = this._dialog.open(DeletionConfirmationComponent, {
            panelClass: 'full-page-scroll-40',
            height: 'auto',
            data: {
                messageKey: 'private.common.message-templates.messages.menu.confirmDeletion',
                data: messageToDelete
            }
        });
        dialogRef.componentInstance.actionValue$.pipe(
            filter((confirmation: boolean) => confirmation),
            switchMap(() => this._messageService.delete(messageToDelete.id)),
            finalize(() => {
                dialogRef.componentInstance.close();
            })
        ).subscribe(() => {
            this.messages = this.messages.filter((message) => {
                return message.id !== messageToDelete.id;
            });
        });
    }

    daysAgo(time: Date): string {
        const getDays = daysAgo(time);
        return getDays === 0 ? 'aujourd\'hui' : getDays > 0 ? 'il y a ' + getDays + ' jours' : 'dans ' + -getDays + ' jours';
    }

    forceResend(messageToSend: Message): void {
        this.errorMessages = [];
        this.isLoading = true;
        this._messageService.forceResend(messageToSend.id).subscribe({
            next: (messageUpdated) => {
                if (this.messages.find(message => message.id === messageUpdated.id)) {
                    this.messages = this.messages.map((message) => {
                        return message.id === messageUpdated.id ? messageUpdated : message;
                    });
                } else {
                    this.messages.unshift(messageUpdated); // cas où un message a été dupliqué parce qu'il avait déjà été envoyé
                }
                this.isLoading = false;
                return this.messages;
            }, error: (httpErrorResponse: HttpErrorResponse) => {
                this.isLoading = false;
                this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
            }
        });
    }

    sendMessageHidden(): void {
        const dialogRef = this._dialog.open(CustomizeMessageComponent, {
            panelClass: 'full-page-scroll-50',
            height: 'auto',
            data: {
                organism: this.organism,
                canSendSms : this.subscription.allowPaidUsage,
                entityId: this.entityId,
                entityClass: this.entityClass
            }
        });
        dialogRef.afterClosed().subscribe(res => {
            if (res.messageCreated) {
                this.messages.unshift(res.messageCreated);
            }
        });
    }
}
