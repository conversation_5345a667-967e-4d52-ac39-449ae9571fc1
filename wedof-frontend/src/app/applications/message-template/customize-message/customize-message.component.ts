import {Component, Inject, OnInit} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material/dialog';
import {FormBuilder, FormGroup} from '@angular/forms';
import {AppFormFieldData} from '../../../shared/material/app-form-field/app-form-field.component';
import {Organism} from '../../../shared/api/models/organism';
import {MessageTemplateType} from '../../../shared/api/models/message-template';
import {TranslateService} from '@ngx-translate/core';
import {MessageCreateBody} from '../../../shared/api/models/message';
import {MessageService} from '../../../shared/api/services/message.service';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../../shared/errors/errors.types';

@Component({
    selector: 'app-customize-message',
    templateUrl: './customize-message.component.html',
    styleUrls: ['./customize-message.component.scss']
})
export class CustomizeMessageComponent implements OnInit {

    loading = false;
    formGroup: FormGroup;
    errorMessages: string[] = [];
    appFormFieldsData: AppFormFieldData[];
    messageTemplateTypes = MessageTemplateType;

    constructor(private _formBuilder: FormBuilder,
                private _messageService: MessageService,
                private _translateService: TranslateService,
                public dialogRef: MatDialogRef<CustomizeMessageComponent>,
                @Inject(MAT_DIALOG_DATA) public dialogData: {
                    organism: Organism,
                    canSendSms: boolean,
                    entityId: number | string,
                    entityClass: string
                }
    ) {
    }

    ngOnInit(): void {
        this.formGroup = this._formBuilder.group({
            message: this._formBuilder.group({})
        });
        const appFormFieldsData: AppFormFieldData[] = [
            {
                controlName: 'type',
                value: null,
                required: true,
                label: 'private.common.message-templates.type.title',
                type: 'select',
                choices: [
                    {
                        key: this._translateService.instant('private.common.message-templates.type.value.' + MessageTemplateType.EMAIL),
                        value: MessageTemplateType.EMAIL
                    },
                    {
                        key: this._translateService.instant('private.common.message-templates.type.value.' + MessageTemplateType.SMS),
                        value: MessageTemplateType.SMS,
                        disabled: !this.dialogData.canSendSms,
                        tooltip: this.dialogData.canSendSms ? '' : this._translateService.instant('common.actions.subscriptionNotAllowedSms')
                    }
                ],
                change: (controlName, newValue, formData) => {
                    const appFormFieldSubject = formData.find(field => field.controlName === 'subject');
                    const appFormFieldReplyTo = formData.find(field => field.controlName === 'replyTo');
                    const appFormFieldCc = formData.find(field => field.controlName === 'cc');
                    const appFormFieldCci = formData.find(field => field.controlName === 'cci');
                    const isEmailType = newValue === MessageTemplateType.EMAIL;
                    appFormFieldSubject.removed = !isEmailType;
                    appFormFieldSubject.required = isEmailType;
                    appFormFieldReplyTo.removed = !isEmailType;
                    appFormFieldReplyTo.value = isEmailType && this.dialogData.organism.sendAs ? this.dialogData.organism.sendAs[0].email : null;
                    appFormFieldCc.removed = !isEmailType;
                    appFormFieldCci.removed = !isEmailType;
                    return [appFormFieldSubject, appFormFieldReplyTo, appFormFieldCc, appFormFieldCci];
                },
                colSpan: 3,
            },
            {
                controlName: 'to',
                required: true,
                label: 'private.common.message-templates.to.title',
                type: 'text',
                colSpan: 3,
            },
            {
                controlName: 'replyTo',
                removed: true,
                label: 'private.common.message-templates.replyTo.title',
                help: 'private.common.message-templates.replyTo.matTooltip',
                type: 'text',
                colSpan: 3,
            },
            {
                controlName: 'cc',
                removed: true,
                label: 'private.common.message-templates.cc',
                help: 'private.common.message-templates.replyTo.matTooltip',
                type: 'text',
                colSpan: 3,
            },
            {
                controlName: 'cci',
                removed: true,
                label: 'private.common.message-templates.cci',
                help: 'private.common.message-templates.replyTo.matTooltip',
                type: 'text',
                colSpan: 3,
            },
            {
                controlName: 'subject',
                removed: true,
                label: 'private.common.message-templates.subject',
                type: 'text',
            },
            {
                controlName: 'body',
                required: true,
                label: 'private.common.message-templates.body.title',
                type: 'templateEditor',
                templateEditorOptions: {
                    type: 'message',
                    scope: this.dialogData.entityClass[0].toLowerCase() + this.dialogData.entityClass.slice(1)
                }
            }
        ];
        this.appFormFieldsData = appFormFieldsData.filter(value => value != null);
    }

    closeModal(): void {
        this.dialogRef.close();
    }

    submit(): void {
        this.loading = true;
        const formGroup = this.formGroup.getRawValue().message;
        const body: MessageCreateBody = {
            type: formGroup.type.toLowerCase(),
            replyTo: formGroup.replyTo?.length ? this.convertStringToArray(formGroup.replyTo) : [],
            cc: formGroup.cc?.length ? this.convertStringToArray(formGroup.cc) : [],
            cci: formGroup.cci?.length ? this.convertStringToArray(formGroup.cci) : [],
            to: formGroup.to?.length ? this.convertStringToArray(formGroup.to) : [],
            subject: formGroup.subject,
            body: formGroup.body,
            sendAs: formGroup.type === this.messageTemplateTypes.EMAIL && this.dialogData.organism.sendAs ?
                this.dialogData.organism.sendAs[0].email : null,
            entityId: this.dialogData.entityId.toString(),
            entityClass: this.dialogData.entityClass,
            siret: this.dialogData.organism.siret
        };
        this._messageService.create(body).subscribe({
            next: (messageCreated) => {
                this.loading = false;
                this.dialogRef.close({messageCreated: messageCreated});
            }, error: (httpErrorResponse: HttpErrorResponse) => {
                this.loading = false;
                this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
            }
        });
    }

    getSMSCount(): number {
        const bodyLength = this.formGroup?.get('message')?.get('body')?.value?.length;
        return bodyLength <= 70 ? 1 : Math.ceil((bodyLength - 70) / 67) + 1;
    }

    convertStringToArray(param: string): string[] {
        return param.replace(/\s/g, '').split(',');
    }
}
