<app-dialog-layout
    [title]=" 'common.actions.sendNew' | translate"
    [actions]="actions"
    (dialogClose)="closeModal()">

    <form [formGroup]="formGroup" class="flex flex-col">
        <app-form-fields class="grid grid-cols-6 gap-2"
                         formGroupName="message"
                         [appFormFieldsData]="appFormFieldsData"
                         [formGroup]="formGroup"></app-form-fields>


        <p *ngIf="formGroup?.get('message')?.get('type')?.value === messageTemplateTypes.SMS && getSMSCount()">
            {{'private.common.message-templates.type.countSms' | translate : {count: getSMSCount()}  }}
        </p>

        <div *ngIf="errorMessages?.length" class="flex items-center">
            <treo-message class="flex-auto" appearance="outline" [showIcon]="false" type="error">
                <ul>
                    <li *ngFor="let errorMessage of errorMessages">
                        {{ errorMessage }}
                    </li>
                </ul>
            </treo-message>
        </div>
    </form>

    <ng-template #actions>
        <button type="submit" class="flex align-center" mat-flat-button color="primary"
                (click)="submit()"
                [disabled]="loading || formGroup?.invalid">
            <mat-progress-spinner class="mr-4" *ngIf="loading" [diameter]="24"
                                  mode="indeterminate"></mat-progress-spinner>
            <ng-container
                *ngIf="!loading">{{ 'common.actions.createAndSend' | translate }}
            </ng-container>
        </button>
    </ng-template>
</app-dialog-layout>
