import {Component, Injector, OnD<PERSON>roy} from '@angular/core';
import {AbstractApplicationSettingsComponent} from '../../shared/settings/abstract-application-settings.component';
import {OrganismApplication} from '../../../shared/api/models/organism-application';
import {HttpErrorResponse} from '@angular/common/http';
import {Router} from '@angular/router';
import {MessageTemplate, MessageTemplateType} from '../../../shared/api/models/message-template';
import {MessageTemplateService} from '../../../shared/api/services/message-template.service';
import {PickerCards, PickerComponent} from '../../../shared/material/picker/picker.component';
import {MatDialog} from '@angular/material/dialog';
import {combineLatest, Observable, of, Subject} from 'rxjs';
import {Select} from '@ngxs/store';
import {SubscriptionState} from '../../../shared/api/state/subscription.state';
import {Subscription} from '../../../shared/api/models/subscription';
import {takeUntil} from 'rxjs/operators';
import {Organism} from '../../../shared/api/models/organism';
import {OrganismState} from '../../../shared/api/state/organism.state';

@Component({
    selector: 'app-message-template.settings',
    templateUrl: './message-template.settings.component.html',
    styleUrls: ['./message-template.settings.component.scss']
})
export class MessageTemplateSettingsComponent extends AbstractApplicationSettingsComponent<MessageTemplateMetadata> implements OnDestroy {

    private static appId = 'message-templates';

    isAllowCertifier = false;
    isTrainingOrganism = false;
    isAllowProposals = false;
    @Select(SubscriptionState.subscription) subscription$: Observable<Subscription>;
    @Select(OrganismState.organism) organism$: Observable<Organism>;
    private _unsubscribeAll = new Subject<void>();

    constructor(injector: Injector,
                private _dialog: MatDialog,
                private _messageTemplateService: MessageTemplateService,
                _router: Router) {
        super(MessageTemplateSettingsComponent.appId, _router, injector);
    }

    protected initForm(metadata: MessageTemplateMetadata): void {
        combineLatest([
            this.subscription$,
            this.organism$,
        ]).pipe(takeUntil(this._unsubscribeAll)).subscribe(([subscription, organism]) => {
            this.isAllowCertifier = subscription.allowCertifiers;
            this.isTrainingOrganism = organism.isTrainingOrganism;
            this.isAllowProposals = subscription.allowProposals;
        });
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    onSuccess(organismApplication: OrganismApplication): void {
    }

    onError(httpErrorResponse: HttpErrorResponse): void {
    }

    create(): void {
        this._router.navigate(['mes-applications', this.application.appId(), 'creer-message-template']);
    }

    edit(messageTemplate: MessageTemplate): void {
        this._router.navigate(['mes-applications', this.application.appId(), 'editer-message-template', messageTemplate.id]);
    }

    openTemplatePicker(): void {
        const dialogRef = this._dialog.open(PickerComponent, {
            panelClass: ['full-page-scroll-50'],
            data: {
                cards: this.getMessageTemplateConfig(),
                selectTemplate: (templateName: string) => {
                    if (templateName === 'New') {
                        return of({});
                    } else {
                        return this._messageTemplateService.createFromTemplate(templateName);
                    }
                }
            }
        });
        dialogRef.afterClosed().subscribe(result => {
            if (result?.data) {
                const messageTemplate = result.data;
                if (messageTemplate?.id) {
                    this.edit(messageTemplate);
                } else {
                    this.create();
                }
            }
        });
    }

    getMessageTemplateConfig(): PickerCards[] {
        return [
            {
                title: 'Démarrer à partir de zéro',
                description: 'Nouveau modèle de message vierge',
                tags: ['email', 'sms'],
                templateName: 'New',
                hasAccess: true,
                display: true
            },
            {
                title: 'Données manquantes pour l\'accrochage',
                description: 'Collecter les données manquantes du candidat pour le Passeport de Compétences',
                tags: ['Certification'],
                templateName: 'AccrochageMissingData',
                hasAccess: this.isAllowCertifier,
                additionalInformations: {
                    type: MessageTemplateType.EMAIL.toString()
                },
                display: this.isAllowCertifier,
            },
            {
                title: 'Dossier de certification Réussi',
                description: 'Informer le candidat de la réussite de la certification',
                tags: ['Certification'],
                templateName: 'CertificationSuccess',
                hasAccess: this.isAllowCertifier,
                additionalInformations: {
                    type: MessageTemplateType.EMAIL.toString()
                },
                display: this.isAllowCertifier,
            },
            {
                title: 'Dossier de certification échoué',
                description: 'Informer le candidat de l\'échec de la certification',
                tags: ['Certification'],
                templateName: 'CertificationFail',
                hasAccess: this.isAllowCertifier,
                additionalInformations: {
                    type: MessageTemplateType.EMAIL.toString()
                },
                display: this.isAllowCertifier,
            },
            {
                title: 'Enquête de suivi professionnel',
                description: 'Envoyer un mail au candidat pour répondre à la 1ere enquête',
                tags: ['Certification'],
                templateName: 'CertificationFolderSurveyCreated',
                hasAccess: this.isAllowCertifier,
                additionalInformations: {
                    type: MessageTemplateType.EMAIL.toString()
                },
                display: this.isAllowCertifier,
            },
            {
                title: 'Audit finalisé',
                description: 'Envoyer un mail au partenaire dès qu\'un audit a été finalisé',
                tags: ['CertificationPartner'],
                templateName: 'CertificationPartnerAuditCompleted',
                hasAccess: this.isAllowCertifier,
                additionalInformations: {
                    type: MessageTemplateType.EMAIL.toString()
                },
                display: this.isAllowCertifier,
            },
            {
                title: 'Notification de changement d\'état',
                description: 'Recevoir une notification lors d\'un changement d\'état d\'un dossier de formation',
                tags: ['Formation'],
                templateName: 'TrainingNotification',
                hasAccess: this.isTrainingOrganism,
                additionalInformations: {
                    type: MessageTemplateType.EMAIL.toString()
                },
                display: this.isTrainingOrganism,
            },
            {
                title: 'Dossier de formation Accepté',
                description: 'Envoyer un mail à l\'apprenant à l\'état Accepté du dossier de formation',
                tags: ['Formation'],
                templateName: 'TrainingAccepted',
                hasAccess: this.isTrainingOrganism,
                additionalInformations: {
                    type: MessageTemplateType.EMAIL.toString()
                },
                display: this.isTrainingOrganism,
            },
            {
                title: 'Propositions',
                description: 'Envoyer un mail de rappel à l\'apprenant pour une proposition',
                tags: ['Commercial'],
                templateName: 'Proposal',
                hasAccess: this.isAllowProposals,
                additionalInformations: {
                    type: MessageTemplateType.EMAIL.toString()
                },
                display: this.isAllowProposals,
            },
            {
                title: 'Partenariat de certification',
                description: 'Envoyer un mail à l\'activation d\'un partenariat de certification',
                tags: ['Partenariat'],
                templateName: 'CertificationPartner',
                hasAccess: this.isAllowCertifier,
                additionalInformations: {
                    type: MessageTemplateType.EMAIL.toString()
                },
                display: this.isAllowCertifier,
            }
        ];
    }
}

export interface MessageTemplateMetadata {
    title: string;
}
