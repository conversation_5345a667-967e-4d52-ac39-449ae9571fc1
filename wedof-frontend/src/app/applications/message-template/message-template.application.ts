import {application} from '../shared/application.decorator';
import {Application, staticImplements} from '../shared/application.interface';
import {MessageTemplateSettingsComponent} from './settings/message-template.settings.component';
import {MessageTemplateFormComponent} from './message-template-form/message-template-form.component';
import {MessageTemplateResolver} from './message-template-resolver.service';
import {MessageTemplateMenuComponent} from './message-template-menu/message-template-menu.component';
import {MessageTemplateTableComponent} from './message-template-table/message-template-table.component';
import {MessageCardComponent} from './message-card/message-card.component';
import {CanDeactivateGuard} from '../../shared/utils/can-deactivate/can-deactivate.guard';
import {MessageTemplateCardComponent} from './message-template-card/message-template-card.component';
import {MessageMenuComponent} from './message-menu/message-menu.component';
import {CustomizeMessageComponent} from './customize-message/customize-message.component';

@application()
@staticImplements<Application>()
export class MessageTemplateApplication {
    public static appName(): string {
        return 'Messages et notifications';
    }

    public static appId(): string {
        return 'message-templates';
    }

    public static beta(): boolean {
        return false;
    }

    public static isSubscriptionOption(): boolean {
        return false;
    }

    public static hasTrialAvailable(): boolean {
        return false;
    }

    public static components(): any {
        return [
            MessageTemplateSettingsComponent,
            MessageTemplateFormComponent,
            MessageTemplateMenuComponent,
            MessageTemplateTableComponent,
            MessageTemplateCardComponent,
            MessageCardComponent,
            MessageMenuComponent,
            CustomizeMessageComponent
        ];
    }

    public static priority(): number {
        return 999;
    }

    public static routing(): any {
        return {
            path: MessageTemplateApplication.appId(),
            children: [
                {
                    path: 'reglages',
                    component: MessageTemplateSettingsComponent
                },
                {
                    path: 'creer-message-template',
                    component: MessageTemplateFormComponent,
                    canDeactivate: [CanDeactivateGuard]
                },
                {
                    path: 'editer-message-template/:id',
                    component: MessageTemplateFormComponent,
                    resolve: {
                        messageTemplate: MessageTemplateResolver
                    },
                    canDeactivate: [CanDeactivateGuard]
                }
            ]
        };
    }

    public static entryPoints(): any {
        return {
            'card-registration-folder-side': [{
                tpl: 'tpl-messages-card-registration-folder-side',
                icon: 'email',
                title: 'private.common.shortcut.messageTemplates',
                shortcut: {
                    title: 'private.common.shortcut.messageTemplates',
                    target: MessageCardComponent.COMPONENT_ID,
                    icon: 'email',
                    visible: true
                }
            }],
            'card-certification-folder-side': [{
                tpl: 'tpl-messages-card-certification-folder-side',
                title: 'private.common.shortcut.messageTemplates',
                shortcut: {
                    title: 'private.common.shortcut.messageTemplates',
                    target: MessageCardComponent.COMPONENT_ID,
                    icon: 'email',
                    visible: true
                }
            }],
            'card-certification-side': [{
                tpl: 'tpl-message-templates-card-certification-side',
                title: 'private.common.shortcut.messageTemplates',
                shortcut: {
                    title: 'private.common.shortcut.messageTemplates',
                    target: MessageCardComponent.COMPONENT_ID,
                    icon: 'email',
                    visible: true
                }
            }],
            'card-proposal-side': [{
                tpl: 'tpl-messages-card-proposal-side',
                icon: 'email',
                title: 'private.common.shortcut.messageTemplates',
                shortcut: {
                    title: 'private.common.shortcut.messageTemplates',
                    target: MessageCardComponent.COMPONENT_ID,
                    icon: 'email',
                    visible: true
                }
            }],
            'card-certification-partner-side': [{
                tpl: 'tpl-messages-card-certification-partner-side',
                icon: 'email',
                title: 'private.common.shortcut.messageTemplates',
                shortcut: {
                    title: 'private.common.shortcut.messageTemplates',
                    target: MessageCardComponent.COMPONENT_ID,
                    icon: 'email',
                    visible: true
                }
            }]
        };
    }
}
