<button mat-icon-button
        (click)="$event.stopPropagation()"
        [matMenuTriggerFor]="actionsMenu"
        title="Actions">
    <mat-icon svgIcon="more_vert"></mat-icon>
</button>
<mat-menu #actionsMenu="matMenu">
    <ng-template matMenuContent>
        <button mat-menu-item>
            <mat-icon color="primary" svgIcon="visibility"></mat-icon>
            <span><a class="no-underline"
                     href="/app/public/message/html/{{message.uuid}}"
                     target="_blank">{{ 'private.common.message-templates.messages.menu.show' | translate }}</a></span>
        </button>
        <ng-container *ngIf="canResend()">
            <button mat-menu-item (click)="resend()">
                <mat-icon color="primary" svgIcon="send"></mat-icon>
                <span>{{'private.common.message-templates.messages.menu.state.' + message.state | translate}}</span>
            </button>
        </ng-container>
        <button *ngIf="message.state === messageState.SCHEDULED && messageTemplate && isOwner" mat-menu-item
                (click)="deleteMessage()">
            <mat-icon color="warn" svgIcon="delete"></mat-icon>
            <span>{{ 'private.common.message-templates.messages.menu.cancel' | translate }}</span>
        </button>
    </ng-template>
</mat-menu>
