import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {Message, MessageState} from '../../../shared/api/models/message';
import {MessageTemplateService} from '../../../shared/api/services/message-template.service';
import {MessageTemplate, MessageTemplateState} from '../../../shared/api/models/message-template';

@Component({
    selector: 'app-message-menu',
    templateUrl: './message-menu.component.html',
    styleUrls: ['./message-menu.component.scss']
})
export class MessageMenuComponent implements OnInit {

    messageState = MessageState;
    messageTemplate: MessageTemplate;

    @Input() isOwner: boolean;
    @Input() message: Message;
    @Input() hasAccessToApplication: boolean;
    @Output() delete: EventEmitter<Message> = new EventEmitter<Message>();
    @Output() forceResend: EventEmitter<Message> = new EventEmitter<Message>();

    constructor(private _messageTemplateService: MessageTemplateService) {
    }

    ngOnInit(): void {
        if (this.isOwner) {
            this._messageTemplateService.getByUrl(this.message._links.messageTemplate.href).subscribe((messageTemplate) => {
                this.messageTemplate = messageTemplate;
            });
        } else {
            this.messageTemplate = null;
        }
    }

    deleteMessage(): void {
        this.delete.emit(this.message);
    }

    canResend(): boolean {
        return this.isOwner && this.hasAccessToApplication
            && [MessageState.SCHEDULED,
                MessageState.SENT,
                MessageState.NOT_SENT_ENFORCED_CONDITIONS,
                MessageState.NOT_SENT_MISSING_DATA,
                MessageState.FAILED].includes(this.message.state)
            && this.messageTemplate && this.messageTemplate.state === MessageTemplateState.ACTIVE;
    }

    resend(): void {
        this.forceResend.emit(this.message);
    }
}
