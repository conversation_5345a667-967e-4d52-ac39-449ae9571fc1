import {Component, EventEmitter, Input, Output} from '@angular/core';
import {MatDialog} from '@angular/material/dialog';
import {filter, finalize, switchMap} from 'rxjs/operators';
import {FormGroup} from '@angular/forms';
import {HttpErrorResponse} from '@angular/common/http';
import {
    MessageTemplate,
    MessageTemplateState,
    MessageTemplateUpdate
} from '../../../shared/api/models/message-template';
import {MessageTemplateService} from '../../../shared/api/services/message-template.service';
import {ApiError} from '../../../shared/errors/errors.types';
import {
    DeletionConfirmationComponent
} from '../../../shared/material/action-confirmation/deletion-confirmation.component';
import {SnackBarComponent} from '../../../shared/material/snack-bar/snack-bar.component';
import {displayTextSnackBar} from '../../../shared/utils/displayTextSnackBar';
import {MatSnackBar} from '@angular/material/snack-bar';

@Component({
    selector: 'app-message-template-menu',
    templateUrl: './message-template-menu.component.html',
    styleUrls: ['./message-template-menu.component.scss']
})
export class MessageTemplateMenuComponent {

    isLoading = false;
    state = MessageTemplateState;

    @Input() withButton = false;
    @Input() formGroup?: FormGroup;
    @Input() messageTemplate: MessageTemplate;
    @Output() errorMessages: EventEmitter<string[]> = new EventEmitter<[]>();
    @Output() processedMessageTemplate: EventEmitter<MessageTemplate> = new EventEmitter<MessageTemplate>();
    @Output() initMessageTemplate?: EventEmitter<MessageTemplate> = new EventEmitter<MessageTemplate>();
    @Output() duplicateMessageTemplate?: EventEmitter<MessageTemplate> = new EventEmitter<MessageTemplate>();
    @Output() deleteMessageTemplate?: EventEmitter<any> = new EventEmitter<any>();
    @Output() loading?: EventEmitter<boolean> = new EventEmitter<boolean>();

    constructor(private _dialog: MatDialog,
                private _snackBar: MatSnackBar,
                private _messageTemplateService: MessageTemplateService) {
    }


    toState(state: MessageTemplateState): void {
        this.isLoading = true;
        this.errorMessages.emit([]);
        this._messageTemplateService.state(this.messageTemplate, state).pipe(
            finalize(() => this.isLoading = false)
        ).subscribe(
            (messageTemplateUpdated: MessageTemplate) => {
                this.processedMessageTemplate.emit(messageTemplateUpdated);
            }, (httpErrorResponse: HttpErrorResponse) => {
                this.errorMessages.emit((httpErrorResponse.error as ApiError).errorMessages);
            });
    }

    update(stateToUpdate?: MessageTemplateState): void {
        this.isLoading = true;
        this.errorMessages.emit([]);
        const messageTemplateValue = this.formGroup.getRawValue().messageTemplate;
        const messageTemplate: MessageTemplateUpdate = {
            title: messageTemplateValue.title,
            events: messageTemplateValue.events,
            subject: messageTemplateValue.subject,
            entityClass: messageTemplateValue.entityClass,
            body: messageTemplateValue.body,
            certifInfos: messageTemplateValue.certifInfos ?? [],
            tags: messageTemplateValue.tags,
            sendAs: messageTemplateValue.sendAs,
            type: messageTemplateValue.type.toLowerCase(),
            replyTo: messageTemplateValue.replyTo?.length ? this.convertStringToArray(messageTemplateValue.replyTo) : [],
            cc: messageTemplateValue.cc?.length ? this.convertStringToArray(messageTemplateValue.cc) : [],
            cci: messageTemplateValue.cci?.length ? this.convertStringToArray(messageTemplateValue.cci) : [],
            to: messageTemplateValue.to?.length ? this.convertStringToArray(messageTemplateValue.to) : [],
            delay: '+' + messageTemplateValue.delayQuantity + messageTemplateValue.delayUnit,
            enforceConditions: messageTemplateValue.enforceConditions ?? false,
            qualiopiIndicators: messageTemplateValue.qualiopiIndicators,
            allowResend: messageTemplateValue.allowResend ?? false
        };
        this._messageTemplateService.update(this.messageTemplate.id, messageTemplate).subscribe(
            (messageTemplateUpdated) => {
                if (stateToUpdate) {
                    this.toState(stateToUpdate);
                } else {
                    this.isLoading = false;
                    this.initMessageTemplate.emit(messageTemplateUpdated);
                }
            }, (httpErrorResponse: HttpErrorResponse) => {
                this.isLoading = false;
                this.errorMessages.emit((httpErrorResponse.error as ApiError).errorMessages);
            }
        );
    }

    convertStringToArray(param: string): string[] {
        return param.replace(/\s/g, '').split(',');
    }

    duplicate(): void {
        this.loading.emit(true);
        this.errorMessages.emit([]);
        this._messageTemplateService.duplicate(this.messageTemplate.id).subscribe({
            next: (messageTemplateDuplicated) => {
                this.duplicateMessageTemplate.emit(messageTemplateDuplicated);
            },
            error: (httpErrorResponse: HttpErrorResponse) => {
                this.loading.emit(false);
                this.errorMessages.emit((httpErrorResponse.error as ApiError).errorMessages);
            }
        });
    }

    delete(): void {
        const dialogRef = this._dialog.open(DeletionConfirmationComponent, {
            panelClass: 'full-page-scroll-40',
            height: 'auto',
            data: {
                messageKey: 'private.common.message-templates.menu.confirmDeletion',
                data: this.messageTemplate
            }
        });
        dialogRef.componentInstance.actionValue$.pipe(
            filter((confirmation: boolean) => confirmation),
            switchMap(() => this._messageTemplateService.delete(this.messageTemplate.id)),
            finalize(() => {
                dialogRef.componentInstance.close();
            })
        ).subscribe({
            next: () => {
                this.deleteMessageTemplate.emit();
            },
            error: (httpErrorResponse: HttpErrorResponse) => {
                this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar((httpErrorResponse.error as ApiError).errorMessages.toString(), 10000, 'red'));
            }
        });
    }
}
