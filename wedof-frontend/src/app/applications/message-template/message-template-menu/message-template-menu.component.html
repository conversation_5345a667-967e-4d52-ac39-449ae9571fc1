<div class="flex align-center" *ngIf="withButton else onlyMenu">
    <div *ngIf="!formGroup?.dirty">
        <div [ngSwitch]="messageTemplate?.state">
            <button *ngSwitchCase="state.INACTIVE"
                    type="button" mat-flat-button
                    color="primary"
                    class="flex justify-center items-center"
                    (click)="toState(state.ACTIVE)">
                {{ 'private.common.message-templates.menu.activate' | translate}}
            </button>
            <button *ngSwitchCase="state.ACTIVE"
                    type="button" mat-flat-button
                    color="warn"
                    class="flex justify-center items-center"
                    (click)="toState(state.INACTIVE)">
                {{ 'private.common.message-templates.menu.deactivate' | translate}}
            </button>
        </div>
    </div>
    <div *ngIf="formGroup?.dirty" class="flex align-center">
        <button class="flex justify-center items-center button-actions"
                type="button" mat-flat-button
                color="primary"
                [disabled]="!formGroup?.dirty || isLoading || formGroup.invalid"
                (click)="update()">
            <mat-progress-spinner class="mr-4" *ngIf="isLoading" [diameter]="24"
                                  mode="indeterminate"></mat-progress-spinner>
            <ng-container *ngIf="!isLoading"> {{ 'common.actions.update' | translate}}</ng-container>
        </button>
        <button class="flex justify-center items-center button-arrow button-arrow-primary"
                color="primary"
                (click)="$event.stopPropagation()"
                mat-flat-button
                [disabled]="isLoading || formGroup.invalid"
                [matMenuTriggerFor]="actionsMenuUpdateState"
                title="Actions" type="button">
            <mat-icon class="icon" svgIcon="arrow_drop_down"></mat-icon>
        </button>
    </div>
</div>

<ng-template #onlyMenu>
    <button mat-icon-button
            (click)="$event.stopPropagation()"
            [matMenuTriggerFor]="actionsMenu"
            title="Actions">
        <mat-icon svgIcon="more_vert"></mat-icon>
    </button>
</ng-template>

<mat-menu #actionsMenu="matMenu">
    <ng-template matMenuContent>
        <button mat-menu-item (click)="duplicate()">
            <mat-icon color="primary" svgIcon="content_copy"></mat-icon>
            <span>{{ 'private.common.message-templates.menu.duplicate' | translate}}</span>
        </button>
        <button mat-menu-item (click)="toState(state.ACTIVE)" *ngIf="messageTemplate.state === state.INACTIVE ">
            <mat-icon color="primary" svgIcon="check_circle"></mat-icon>
            <span>{{ 'private.common.message-templates.menu.activate' | translate}}</span>
        </button>
        <button mat-menu-item (click)="toState(state.INACTIVE)" *ngIf="messageTemplate.state === state.ACTIVE ">
            <mat-icon color="warn" svgIcon="cancel"></mat-icon>
            <span>{{ 'private.common.message-templates.menu.deactivate' | translate}}</span>
        </button>
        <button mat-menu-item (click)="delete()">
            <mat-icon color="warn" svgIcon="delete"></mat-icon>
            <span>{{ 'private.common.message-templates.menu.delete' | translate}}</span>
        </button>
    </ng-template>
</mat-menu>

<mat-menu #actionsMenuUpdateState="matMenu">
    <ng-template matMenuContent>
        <button mat-menu-item (click)="update(state.ACTIVE)" *ngIf="messageTemplate.state === state.INACTIVE ">
            <mat-icon color="primary" svgIcon="check_circle"></mat-icon>
            <span>{{'common.actions.updateAnd' | translate}} {{ 'private.common.message-templates.menu.activate' | translate}}</span>
        </button>
        <button mat-menu-item (click)="update(state.INACTIVE)" *ngIf="messageTemplate.state === state.ACTIVE ">
            <mat-icon color="warn" svgIcon="cancel"></mat-icon>
            <span>{{'common.actions.updateAnd' | translate}} {{ 'private.common.message-templates.menu.deactivate' | translate}}</span>
        </button>
    </ng-template>
</mat-menu>
