import {
    AfterViewInit,
    Component,
    ElementRef,
    EventEmitter,
    Input,
    OnChanges,
    OnDestroy,
    OnInit,
    Output,
    SimpleChanges
} from '@angular/core';
import {Certification} from '../../../shared/api/models/certification';
import {OrganismApplicationEntryPoint} from '../../../shared/api/models/applications';
import {Select} from '@ngxs/store';
import {SubscriptionState} from '../../../shared/api/state/subscription.state';
import {combineLatest, Observable, Subject} from 'rxjs';
import {Subscription} from '../../../shared/api/models/subscription';
import {takeUntil} from 'rxjs/operators';
import {OrganismState} from '../../../shared/api/state/organism.state';
import {Organism} from '../../../shared/api/models/organism';
import {MessageTemplate} from '../../../shared/api/models/message-template';
import {Router} from '@angular/router';
import {ShortcutLoadingSidePanel} from '../../../shared/utils/interface/shortcut-interfaces';
import {setUpIntersectionObserver} from '../../../shared/utils/shortcut-side-panel-utils';
import {CardIntersection} from '../../../shared/utils/interface/cards-intersection-interfaces';
import {EntityClass} from '../../../shared/utils/enums/entity-class';

@Component({
    selector: 'app-message-template-card',
    templateUrl: './message-template-card.component.html',
    styleUrls: ['./message-template-card.component.scss']
})
export class MessageTemplateCardComponent implements OnInit, OnChanges, OnDestroy, AfterViewInit {

    subscription: Subscription;
    organism: Organism;
    columns: string[] = ['title', 'state', 'menu'];
    entityClassChoice = EntityClass;

    @Input() certification: Certification;
    @Input() entityClass: string;
    @Input() context: OrganismApplicationEntryPoint;
    @Output() shortcutSidePanelLoaded: EventEmitter<ShortcutLoadingSidePanel> = new EventEmitter<ShortcutLoadingSidePanel>();
    @Output() elementIntersection: EventEmitter<CardIntersection> = new EventEmitter<CardIntersection>();

    private _intersectionObserver: IntersectionObserver;
    private _unsubscribeAll: Subject<void> = new Subject();

    @Select(OrganismState.organism) organism$: Observable<Organism>;
    @Select(SubscriptionState.subscription) subscription$: Observable<Subscription>;

    constructor(private _router: Router, private _el: ElementRef) {
    }

    ngOnInit(): void {
        this.shortcutSidePanelLoaded.emit({name: 'messages', loaded: false});
        combineLatest([
            this.organism$,
            this.subscription$
        ]).pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe(([organism, subscription]) => {
            this.subscription = subscription;
            this.organism = organism;
            this.shortcutSidePanelLoaded.emit({name: 'messages', loaded: true});
        });
    }

    ngOnChanges(changes: SimpleChanges): void {
        this.certification = changes.certification.currentValue;
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
        if (this._intersectionObserver) {
            this._intersectionObserver.disconnect();
        }
    }

    ngAfterViewInit(): void {
        this._intersectionObserver = setUpIntersectionObserver('messages', this.elementIntersection);
        this._intersectionObserver.observe(this._el.nativeElement as HTMLElement);
    }

    edit(messageTemplate: MessageTemplate): void {
        this._router.navigate(['mes-applications', 'message-templates', 'editer-message-template', messageTemplate.id]);
    }

    addMessageTemplate(): void {
        this._router.navigate(['mes-applications', 'message-templates', 'creer-message-template']);
    }

    messageTemplateSettings(): void {
        this._router.navigate(['mes-applications', 'message-templates', 'reglages']);
    }
}
