<ng-container *ngIf="!context.allowedInSubscription else showCard">
    <app-subscription-applications-card [type]="'messageTemplates'"
                                        [fromPage]="'messageTemplatesCertifier'"
                                        [organism]="organism"
                                        [subscription]="subscription"
                                        [subscriptionType]="'CertificationFolder'"
    >
    </app-subscription-applications-card>
</ng-container>

<ng-template #showCard>
    <mat-card class="mat-card flex h-full flex-auto flex-col mt-3 p-5 pb-0 pt-3 border shadow-none sm">
        <div class="flex items-center mb-2">
            <mat-icon class="mr-3 card-loading-show text-4xl" color="primary">email</mat-icon>
            <div
                class="text-xl font-semibold card-loading-show">{{context.params.title | translate}}</div>
            <div class="ml-auto -mr-4 card-loading-hidden">
                <button type="button" mat-icon-button
                        (click)="addMessageTemplate()">
                    <mat-icon [matTooltip]="'private.common.message-templates.add' | translate" svgIcon="add_circle">
                    </mat-icon>
                </button>
                <button type="button" mat-icon-button
                        (click)="messageTemplateSettings()">
                    <mat-icon [matTooltip]="'private.common.message-templates.settings' | translate" color="text-gray">
                        settings
                    </mat-icon>
                </button>
            </div>
        </div>
        <app-message-template-table [columns]="columns"
                                    [limit]="10"
                                    [certification]="certification"
                                    [entityClass]="[entityClassChoice.CERTIFICATION_FOLDER, entityClassChoice.CERTIFICATIONS_PARTNER, entityClassChoice.CERTIFICATION_FOLDER_SURVEY]"
                                    (edit)="edit($event)"></app-message-template-table>
    </mat-card>
</ng-template>
