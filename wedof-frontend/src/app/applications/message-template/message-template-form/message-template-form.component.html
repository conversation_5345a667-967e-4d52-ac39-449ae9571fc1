<treo-card class="flex flex-col p-8 pb-0 w-3/4">
    <div class="breadcrumb mb-2">
        <div routerLink="/mes-applications" class="cursor-pointer path">
            {{ 'private.application.common.title' | translate }}
        </div>
        <span class="separator">/</span>
        <div routerLink="/mes-applications/message-templates/reglages" class="cursor-pointer path">
            {{ 'private.application.message-templates.title' | translate }}
        </div>
        <span class="separator">/</span>
        <div class="path">
            {{ isNew ? 'Création' : 'Edition'   }}
        </div>
    </div>
    <div class="flex flex-row items-center justify-between">
        <div class="flex flex-row items-center">
            <img class="w-14 h-14 mr-4 rounded object-cover" alt="Message"
                 src="/assets/applications/message-templates.png"/>
            <div class="text-2xl font-semibold leading-tight">
                {{ 'private.application.message-templates.title' | translate }}
            </div>
        </div>
    </div>
    <div class="flex flex-row justify-between">
        <a class="mt-2" href="/aide/guides/applications/message-templates" target="_blank">Voir la documentation</a>
        <button
            *ngIf="messageTemplate?.entityClass === EntityClass.CERTIFICATION_FOLDER || messageTemplate?.entityClass === EntityClass.REGISTRATION_FOLDER"
            (click)="seeFolders()">
            <b>Voir les dossiers</b>
        </button>
    </div>
    <form #form="ngForm" [formGroup]="formGroup" class="flex flex-col mt-4">
        <h5 class="mt-2">{{'private.common.message-templates.formTitle.configuration' | translate}} </h5>
        <app-form-fields class="grid grid-cols-6 gap-2"
                         formGroupName="messageTemplate"
                         [entity]="messageTemplate"
                         [appFormFieldsData]="appFormFieldsGroupData['configuration']"
                         [formGroup]="formGroup"></app-form-fields>

        <treo-message appearance="outline" [showIcon]="false" type="info" class=" mt-4 mb-10">
            <p [innerHTML]="getMessageTemplateSummary()"></p>
        </treo-message>
        <h5>{{'private.common.message-templates.formTitle.writing' | translate}} </h5>
        <app-form-fields class="grid grid-cols-6 gap-2"
                         formGroupName="messageTemplate"
                         [entity]="messageTemplate"
                         [appFormFieldsData]="appFormFieldsGroupData['redaction']"
                         [appFormFieldData$]="appFormFieldDataRedaction$"
                         [formGroup]="formGroup"></app-form-fields>
        <div class="flex items-center">
            <button mat-flat-button type="button" class="w-2/6 whitespace-pre-wrap mr-4" color="primary"
                    [matTooltipPosition]="'above'"
                    [matTooltip]="(formGroup.dirty || formGroup.invalid ? 'private.common.message-templates.actions.matToolTip' : '') | translate"
                    [disabled]="disabledTest || formGroup.dirty || formGroup.invalid" (click)="testTemplate()">
                {{'private.common.message-templates.actions.send'| translate}}
            </button>
            <p *ngIf="formGroup?.get('messageTemplate')?.get('type')?.value === messageTemplateTypes.SMS && getSMSCount()">
                {{'private.common.message-templates.type.countSms' | translate : {count: getSMSCount()}  }}
            </p>
        </div>
        <h5 class="mt-10">{{'private.common.message-templates.formTitle.options' | translate}}</h5>
        <app-form-fields class="grid grid-cols-6 gap-2"
                         formGroupName="messageTemplate"
                         [entity]="messageTemplate"
                         [appFormFieldsData]="appFormFieldsGroupData['options']"
                         [formGroup]="formGroup"></app-form-fields>

        <div
            class="flex items-center justify-end -mx-8 mt-5 px-8 py-5 light:bg-cool-gray-50 dark:bg-cool-gray-700">
            <mat-error *ngIf="errorMessages.length">
                <ul>
                    <li *ngFor="let errorMessage of errorMessages">{{ errorMessage }}</li>
                </ul>
            </mat-error>
            <a [routerLink]="[isNew ?   '../reglages' : '../../reglages' ]" mat-button>
                {{ 'private.application.common.actions.back' | translate }}
            </a>
            <ng-container *ngIf="!isNew; else showCreate">
                <app-message-template-menu [withButton]="true"
                                           [formGroup]="formGroup"
                                           (errorMessages)="setErrorMessages($event)"
                                           [messageTemplate]="messageTemplate"
                                           (initMessageTemplate)="initForm($event)"
                                           (processedMessageTemplate)="updateState($event)"
                >
                </app-message-template-menu>
            </ng-container>
            <ng-template #showCreate>
                <button type="submit" (click)="create()"
                        class="px-6 ml-3"
                        mat-flat-button color="primary"
                        [disabled]="loading || !formGroup.dirty || formGroup.invalid">
                    <mat-progress-spinner class="mr-4" *ngIf="loading" [diameter]="24"
                                          mode="indeterminate"></mat-progress-spinner>
                    <ng-container
                        *ngIf="!loading">
                        {{  'common.actions.create' | translate }}
                    </ng-container>
                </button>
            </ng-template>
        </div>
    </form>
</treo-card>
