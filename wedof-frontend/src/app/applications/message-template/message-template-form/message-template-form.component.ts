import {<PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild} from '@angular/core';
import {FormBuilder, FormGroup, FormGroupDirective} from '@angular/forms';
import {Select} from '@ngxs/store';
import {combineLatest, Observable, ReplaySubject, Subject} from 'rxjs';
import {takeUntil} from 'rxjs/operators';
import {TranslateService} from '@ngx-translate/core';
import {ActivatedRoute, Router} from '@angular/router';
import {Organism} from '../../../shared/api/models/organism';
import {MessageTemplate, MessageTemplateState, MessageTemplateType} from '../../../shared/api/models/message-template';
import {AppFormFieldData, Choices} from '../../../shared/material/app-form-field/app-form-field.component';
import {OrganismState} from '../../../shared/api/state/organism.state';
import {CertificationHttpParams, CertificationService} from '../../../shared/api/services/certification.service';
import {MessageTemplateService} from '../../../shared/api/services/message-template.service';
import {EntityClass} from '../../../shared/utils/enums/entity-class';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../../shared/errors/errors.types';
import {Certification} from '../../../shared/api/models/certification';
import {SnackBarComponent} from '../../../shared/material/snack-bar/snack-bar.component';
import {displayTextSnackBar} from '../../../shared/utils/displayTextSnackBar';
import {UserState} from '../../../shared/api/state/user.state';
import {User} from '../../../shared/api/models/user';
import {MatSnackBar} from '@angular/material/snack-bar';
import {
    getCertificationFolderAccrochageStates,
    getCertificationFolderAllStates,
    getCertificationPartnerAllStates,
    getEventFileStatesChoices,
    getProposalAllStates,
    getRegistrationFolderAllStates
} from '../../../shared/utils/states-utils';
import {CanDeactivateComponent} from '../../../shared/utils/can-deactivate/can-deactivate.component';
import {SubscriptionState} from '../../../shared/api/state/subscription.state';
import {
    Subscription,
    SubscriptionCertifierTypes,
    SubscriptionTrainingTypes
} from '../../../shared/api/models/subscription';
import QualiopiIndicators from '../../../../assets/qualiopi/indicateur.json';
import {ProposalStates} from '../../../shared/api/models/proposal';


@Component({
    selector: 'app-message-template-form',
    templateUrl: './message-template-form.component.html',
    styleUrls: ['./message-template-form.component.scss']
})
export class MessageTemplateFormComponent extends CanDeactivateComponent implements OnInit, OnDestroy {

    user: User;
    organism: Organism;
    loading = false;
    showDeactivate = true;
    messageTemplate: MessageTemplate;
    isNew = true;
    formGroup: FormGroup;
    errorMessages: string[] = [];
    appFormFieldsGroupData: AppFormFieldData[][];
    appFormFieldDataRedaction$?: ReplaySubject<AppFormFieldData> = new ReplaySubject<AppFormFieldData>();
    disabledTest = false;
    messageTypeChoices: Choices = [];
    entityClassChoices: Choices = [];
    sendAsEmailChoices: Choices = [];
    qualiopiIndicators: typeof QualiopiIndicators;
    eventsByClass: { [key: string]: { name: string; value: { value: string; key: string; }[] }[] };
    messageTemplateTypes = MessageTemplateType;

    readonly EntityClass = EntityClass;

    @ViewChild('form') form: FormGroupDirective;
    @Select(UserState.user) user$: Observable<User>;
    @Select(OrganismState.organism) organism$: Observable<Organism>;
    @Select(SubscriptionState.subscription) subscription$: Observable<Subscription>;

    private _unsubscribeAll = new Subject<void>();

    constructor(private _formBuilder: FormBuilder,
                private _route: ActivatedRoute,
                private _router: Router,
                private _snackBar: MatSnackBar,
                private _certificationService: CertificationService,
                private _messageTemplateService: MessageTemplateService,
                private _translateService: TranslateService
    ) {
        super();
        this.qualiopiIndicators = QualiopiIndicators;
    }

    ngOnInit(): void {
        combineLatest([
            this.user$,
            this.organism$,
            this.subscription$
        ]).pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe(([user, organism, subscription]) => {
            this.user = user;
            this.organism = organism;
            this.eventsByClass = {
                [EntityClass.CERTIFICATION_FOLDER]: this.getCertificationFolderEventChoice(),
                [EntityClass.REGISTRATION_FOLDER]: this.getRegistrationFolderEventChoice(),
                [EntityClass.PROPOSAL]: this.getProposalEventChoice(),
                [EntityClass.CERTIFICATIONS_PARTNER]: this.getCertificationPartnerEventChoice(),
                [EntityClass.CERTIFICATION_FOLDER_SURVEY]: this.getCertificationFolderSurveyEventChoice()
            };
            if (this.organism.sendAs?.length) {
                const sendAsEmails = [this.organism.sendAs[0].email]; // TODO sendAs multiple emails
                this.sendAsEmailChoices = sendAsEmails.map(sendAsEmail => {
                    return {key: sendAsEmail, value: sendAsEmail};
                });
            }
            this.entityClassChoices = [{
                key: this._translateService.instant('private.common.message-templates.entityClass.value.CertificationFolder'),
                value: EntityClass.CERTIFICATION_FOLDER
            }, {
                key: this._translateService.instant('private.common.message-templates.entityClass.value.RegistrationFolder'),
                value: EntityClass.REGISTRATION_FOLDER
            }];
            this.messageTypeChoices = [
                {
                    key: this._translateService.instant('private.common.message-templates.type.value.' + MessageTemplateType.EMAIL),
                    value: MessageTemplateType.EMAIL
                }];
            if (subscription.allowPaidUsage) {
                this.messageTypeChoices.push({
                    key: this._translateService.instant('private.common.message-templates.type.value.' + MessageTemplateType.SMS),
                    value: MessageTemplateType.SMS
                });
            }
            const isTrainingType = [SubscriptionTrainingTypes.TRIAL, SubscriptionTrainingTypes.ACCESS, SubscriptionTrainingTypes.ACCESS_PLUS, SubscriptionTrainingTypes.STANDARD,
                SubscriptionTrainingTypes.PREMIUM].includes(subscription.trainingType);
            const certifierTypeAllowed = [SubscriptionCertifierTypes.TRIAL, SubscriptionCertifierTypes.UNLIMITED, SubscriptionCertifierTypes.ACCESS];
            if (subscription.allowCertifierPlus) {
                certifierTypeAllowed.push(SubscriptionCertifierTypes.USAGE);
            }
            const isCertifierType = certifierTypeAllowed.includes(subscription.certifierType);
            if (isTrainingType && !isCertifierType) {
                this.entityClassChoices = this.entityClassChoices.filter((choice) => choice.value !== EntityClass.CERTIFICATION_FOLDER);
            } else if (!isTrainingType && isCertifierType) {
                this.entityClassChoices = this.entityClassChoices.filter((choice) => choice.value !== EntityClass.REGISTRATION_FOLDER);
            }
            if (isCertifierType) {
                this.entityClassChoices.push({
                    key: this._translateService.instant('private.common.message-templates.entityClass.value.CertificationPartner'),
                    value: EntityClass.CERTIFICATIONS_PARTNER
                });
                this.entityClassChoices.push({
                    key: this._translateService.instant('private.common.message-templates.entityClass.value.CertificationFolderSurvey'),
                    value: EntityClass.CERTIFICATION_FOLDER_SURVEY
                });
            }
            if (isTrainingType && [SubscriptionTrainingTypes.TRIAL, SubscriptionTrainingTypes.PREMIUM].includes(subscription.trainingType)) {
                this.entityClassChoices.push({
                    key: this._translateService.instant('private.common.message-templates.entityClass.value.Proposal'),
                    value: EntityClass.PROPOSAL
                });
            }
            this._route.data.subscribe((data: { messageTemplate: MessageTemplate }) => {
                let messageTemplate: MessageTemplate;
                if (data.messageTemplate) {
                    this.isNew = false;
                    messageTemplate = data.messageTemplate;
                } else {
                    messageTemplate = {
                        type: MessageTemplateType.EMAIL,
                        state: MessageTemplateState.INACTIVE,
                        tags: [],
                        events: [],
                        delay: '+0minutes',
                        enforceConditions: false,
                        allowResend: true
                    } as MessageTemplate;
                }
                this.initForm(messageTemplate);
            });
        });
    }

    getMessageTemplateSummary(): string {
        const messageTemplate = this.formGroup.getRawValue().messageTemplate;
        const type = this._translateService.instant('private.common.message-templates.type.value.' + (messageTemplate.type).toLowerCase()).toLowerCase();
        const state = this._translateService.instant('private.common.message-templates.state.value.' + this.messageTemplate.state.toLowerCase()).toLowerCase();
        const message = this._translateService.instant('private.common.message-templates.descriptionParts.typeStateOnly', {
            type: type,
            state: state
        });
        // TOUJOURS LE CAS ?
        if (messageTemplate.entityClass == null || !messageTemplate.events?.length) {
            return message;
        }

        const entityClass = (messageTemplate.entityClass === EntityClass.PROPOSAL || messageTemplate.entityClass === EntityClass.CERTIFICATION_FOLDER_SURVEY ? ' d\'une ' : ' d\'un ') + '<b>' + this._translateService.instant('private.common.message-templates.entityClass.value.' + messageTemplate.entityClass).toLowerCase() + '</b>';

        const certification = messageTemplate.entityClass === EntityClass.PROPOSAL ? '' :
            !messageTemplate.certifInfos?.length ? this._translateService.instant('private.common.message-templates.descriptionParts.certification.all') :
                this._translateService.instant('private.common.message-templates.descriptionParts.certification.multiple', {count: messageTemplate.certifInfos?.length});

        const events = this.eventsByClass[messageTemplate.entityClass];
        let eventsType: string = messageTemplate.events.map(event => {
            const eventChoice = events.find(currentEventChoice =>
                currentEventChoice.value.some(item => item.value === event)
            );
            if (eventChoice) {
                const key = eventChoice.value.find(item => item.value === event)?.key;
                return key || '';
            }
            return '';
        }).join(', ');
        eventsType += ' ';


        let delay = '';
        if (messageTemplate.delayQuantity) {
            const delayUnitQuantity = messageTemplate.delayQuantity.toString().concat('', messageTemplate.delayUnit);
            if (delayUnitQuantity !== '0minutes') {
                const delayUnit = this.getDelayUnitChoices().find((unit) => unit.value === messageTemplate.delayUnit);
                delay = this._translateService.instant('private.common.message-templates.descriptionParts.delay.withDelay', {
                    delayQuantity: messageTemplate.delayQuantity,
                    delayUnit: delayUnit.key
                });
            } else {
                delay = this._translateService.instant('private.common.message-templates.descriptionParts.delay.instant');
            }
        }
        let enforceConditionsLabel = '';
        if (messageTemplate.enforceConditions) {
            enforceConditionsLabel = this._translateService.instant('private.common.message-templates.descriptionParts.enforceConditionLabel');
        }
        let allowResend: string;
        if (messageTemplate.allowResend) {
            allowResend = this._translateService.instant('private.common.message-templates.descriptionParts.allowResend.resend');
        } else {
            allowResend = this._translateService.instant('private.common.message-templates.descriptionParts.allowResend.onetime');
        }

        return this._translateService.instant('private.common.message-templates.description', {
            type: type,
            state: state,
            delay: delay,
            events: eventsType,
            entityClass: entityClass,
            certification: certification,
            hasTag: !messageTemplate.tags?.length ? '' : this._translateService.instant('private.common.message-templates.descriptionParts.withTags'),
            tags: !messageTemplate.tags?.length ? '' : messageTemplate.tags.join(', '),
            hasQualiopi: !messageTemplate.qualiopiIndicators?.length ? '' : this._translateService.instant('private.common.message-templates.descriptionParts.withQualiopi'),
            resend: allowResend,
            enforceConditionsLabel: enforceConditionsLabel
        });
    }

    initForm(messageTemplate: MessageTemplate): void {
        this.messageTemplate = messageTemplate;
        this.formGroup = this._formBuilder.group({
            messageTemplate: this._formBuilder.group({})
        });
        const appFormFieldsData: AppFormFieldData[][] = [];
        appFormFieldsData['configuration'] = [
            {
                controlName: 'title',
                required: true,
                label: 'private.common.message-templates.title',
                type: 'text',
                colSpan: 3,
            },
            {
                controlName: 'type',
                value: this.messageTemplate?.type ?? null,
                label: 'private.common.message-templates.type.title',
                type: 'select',
                choices: this.messageTypeChoices,
                colSpan: 1,
                change: (controlName, newValue, formData) => {
                    const appFormFields = appFormFieldsData['redaction'].filter(field => ['subject', 'cci', 'cc', 'replyTo', 'sendAs'].includes(field.controlName));
                    appFormFields.forEach(field => {
                        field.removed = newValue === MessageTemplateType.SMS;
                        this.appFormFieldDataRedaction$.next(field);
                    });
                }
            },
            {
                controlName: 'allowResend',
                label: 'private.common.message-templates.allowResend.title',
                help: 'private.common.message-templates.allowResend.help',
                type: 'radio',
                inline: true,
                colSpan: 2,
                choices: [
                    {key: this._translateService.instant('common.actions.yes'), value: true},
                    {key: this._translateService.instant('common.actions.no'), value: false}
                ],
            },
            {
                controlName: 'entityClass',
                required: true,
                label: 'private.common.message-templates.entityClass.title',
                type: 'select',
                choices: this.entityClassChoices,
                colSpan: 3,
                change: (controlName, newValue, formData) => {
                    const appFormFieldEvents = formData.find(field => field.controlName === 'events');
                    const appFormFieldCertifications = formData.find(field => field.controlName === 'certifInfos');
                    const appFormFieldTags = formData.find(field => field.controlName === 'tags');
                    appFormFieldEvents.listChoices = this.eventsByClass[newValue];
                    appFormFieldEvents.disabled = false;
                    appFormFieldEvents.value = [];
                    this.messageTemplate.events = [];
                    const updatedFields = [appFormFieldEvents];
                    // VERY BIG HACK if we return appFormFieldEventsCertifications directly, removed certifications field does not work
                    const newCertificationRemoved = newValue === EntityClass.PROPOSAL;
                    if (appFormFieldCertifications.removed !== newCertificationRemoved) {
                        appFormFieldCertifications.removed = newCertificationRemoved;
                        updatedFields.push(appFormFieldCertifications);
                    }
                    const appFormFieldsRedaction = appFormFieldsData['redaction'].filter(field => ['body'].includes(field.controlName));
                    if (appFormFieldsRedaction) {
                        appFormFieldsRedaction[0].templateEditorOptions.scope = newValue[0].toLowerCase() + newValue.slice(1);
                        updatedFields.push(appFormFieldsRedaction);
                    }
                    return updatedFields;
                },
            },
            {
                controlName: 'events',
                label: 'private.common.message-templates.events.title',
                type: 'select',
                colSpan: 3,
                disabled: !this.messageTemplate?.events?.length,
                required: true,
                searchMultiple: true,
                placeholder: 'private.common.message-templates.events.placeholder',
                value: this.messageTemplate?.events,
                listChoices: this.messageTemplate?.entityClass ? this.eventsByClass[this.messageTemplate.entityClass] : [],
                change: (controlName, newValue, formData) => {
                    if (controlName === 'events') {
                        const appFormField = formData.find(field => field.controlName === 'events');
                        const updateValue = (entity: string) => {
                            if (newValue.includes(`${entity}.*`)) {
                                if (appFormField.value.includes(`${entity}.*`)) {
                                    appFormField.value = appFormField.value.filter((e: string) => e !== `${entity}.*`);
                                }
                                const tempValues = appFormField.value.filter((e: string) => !e.includes(`${entity}.`));
                                appFormField.value = [`${entity}.*`, ...tempValues];
                            } else {
                                if (appFormField.value.includes(`${entity}.*`)) {
                                    appFormField.value = appFormField.value.filter((e: string) => !e.includes(`${entity}.`));
                                }
                                appFormField.value = newValue;
                            }
                        };
                        appFormField.listChoices.forEach(choice => {
                            switch (true) {
                                case choice.value.some(e => e.value === 'registrationFolder.*'):
                                    updateValue('registrationFolder');
                                    break;
                                case choice.value.some(e => e.value === 'registrationFolderFile.*'):
                                    updateValue('registrationFolderFile');
                                    break;
                                case choice.value.some(e => e.value === 'certificationFolder.*'):
                                    updateValue('certificationFolder');
                                    break;
                                case choice.value.some(e => e.value === 'certificationFolderFile.*'):
                                    updateValue('certificationFolderFile');
                                    break;
                                case choice.value.some(e => e.value === 'proposal.*'):
                                    updateValue('proposal');
                                    break;
                                case choice.value.some(e => e.value === 'certificationPartner.*'):
                                    updateValue('certificationPartner');
                                    break;
                                case choice.value.some(e => e.value === 'certificationPartnerFile.*'):
                                    updateValue('certificationPartnerFile');
                                    break;
                                case choice.value.some(e => e.value === 'certificationPartnerInvoice.*'):
                                    updateValue('certificationPartnerInvoice');
                                    break;
                                case choice.value.some(e => e.value === 'certificationPartnerAudit.*'):
                                    updateValue('certificationPartnerAudit');
                                    break;
                                case choice.value.some(e => e.value === 'certificationFolderSurvey.*'):
                                    updateValue('certificationFolderSurvey');
                                    break;
                            }
                        });

                        return [appFormField];
                    }
                },
            },
            {
                controlName: 'certifInfos',
                type: 'infiniteSearch',
                label: 'private.common.message-templates.certifInfos.title',
                placeholder: 'private.common.message-templates.certifInfos.placeholder',
                help: 'private.common.message-templates.certifInfos.tooltip',
                removable: true,
                searchMultiple: true,
                removed: this.messageTemplate?.entityClass === EntityClass.PROPOSAL,
                searchNoEntriesFoundLabel: 'private.common.message-templates.certifInfos.noEntries',
                searchMethodPaginated: (params: CertificationHttpParams) => this._certificationService.listLite(params),
                parameters: {limit: 15},
                searchComparisonProperty: 'certifInfo',
                searchAdvanced: true,
                searchResultFormatter: (certification: Certification) => certification.externalId + ' ' + certification.name,
                colSpan: 3,
            },
            {
                controlName: 'tags',
                label: 'common.tags.label',
                type: 'tags',
                help: 'private.common.message-templates.tags.tooltip',
                isCreateAvailable: true,
                colSpan: 3
            }
        ];
        appFormFieldsData['redaction'] = [
            {
                controlName: 'to',
                value: this.messageTemplate?.to ? this.messageTemplate.to.join(', ') : '',
                required: true,
                label: 'private.common.message-templates.to.title',
                help: 'private.common.message-templates.to.matTooltip.' + this.messageTemplate?.type,
                type: 'text',
                colSpan: 6,
            },
            {
                controlName: 'sendAs',
                removed: this.messageTemplate?.type === MessageTemplateType.SMS,
                required: false,
                removable: false,
                value: this.messageTemplate?.sendAs ? this.messageTemplate.sendAs : (this.sendAsEmailChoices.length ? this.sendAsEmailChoices[0].value : null),
                disabled: this.sendAsEmailChoices.length === 0,
                label: 'private.common.message-templates.sendAs.title',
                help: 'private.common.message-templates.sendAs.matTooltip',
                placeholder: this.sendAsEmailChoices.length === 0 ? 'private.common.message-templates.sendAs.placeholder' : null,
                type: 'select',
                choices: this.sendAsEmailChoices,
                colSpan: 3,
            },
            {
                controlName: 'replyTo',
                removed: this.messageTemplate?.type === MessageTemplateType.SMS,
                required: true,
                value: this.messageTemplate?.replyTo ? this.messageTemplate.replyTo.join(', ') : this.organism.emails[0],
                label: 'private.common.message-templates.replyTo.title',
                help: 'private.common.message-templates.replyTo.matTooltip',
                type: 'text',
                colSpan: 3,
            },
            {
                controlName: 'cc',
                value: this.messageTemplate?.cc ? this.messageTemplate.cc.join(', ') : '',
                removed: this.messageTemplate?.type === MessageTemplateType.SMS,
                label: 'private.common.message-templates.cc',
                help: 'private.common.message-templates.replyTo.matTooltip',
                type: 'text',
                colSpan: 3,
            },
            {
                controlName: 'cci',
                value: this.messageTemplate?.cci ? this.messageTemplate.cci.join(', ') : '',
                removed: this.messageTemplate?.type === MessageTemplateType.SMS,
                label: 'private.common.message-templates.cci',
                help: 'private.common.message-templates.replyTo.matTooltip',
                type: 'text',
                colSpan: 3,
            },
            {
                controlName: 'subject',
                removed: this.messageTemplate?.type === MessageTemplateType.SMS,
                required: true,
                label: 'private.common.message-templates.subject',
                type: 'text',
                colSpan: 4
            },
            {
                controlName: 'body',
                required: true,
                label: 'private.common.message-templates.body.title',
                type: 'templateEditor',
                templateEditorOptions: {
                    type: 'message',
                    scope: this.messageTemplate?.entityClass ? (this.messageTemplate.entityClass[0].toLowerCase() + this.messageTemplate.entityClass.slice(1)) : ''
                }
            }
        ];
        appFormFieldsData['options'] = [
            {
                controlName: 'delayQuantity',
                label: 'private.common.message-templates.delay',
                type: 'number',
                required: true,
                colSpan: 2,
                min: 0,
                value: this.getDelayInfos().quantity,
                change: (controlName, newValue, formData) => {
                    const appFormField1 = formData.find(field => field.controlName === 'delayQuantity');
                    appFormField1.value = newValue;

                    return [this.enforceConditionsAppFormField(newValue)];
                }
            },
            {
                controlName: 'delayUnit',
                hideLabel: true,
                type: 'select',
                required: true,
                choices: this.getDelayUnitChoices(),
                colSpan: 1,
                value: this.getDelayInfos().unit
            },
            {
                controlName: 'enforceConditions',
                label: 'private.common.message-templates.enforceConditions',
                type: 'radio',
                colSpan: 2,
                inline: true,
                removed: this.isEnforceConditionsRemoved(this.getDelayInfos().quantity),
                choices: [
                    {key: this._translateService.instant('common.actions.yes'), value: true},
                    {key: this._translateService.instant('common.actions.no'), value: false}
                ],
            },
            {
                controlName: 'qualiopiIndicators',
                label: 'private.common.message-templates.qualiopiIndicators',
                type: 'select',
                removable: true,
                searchMultiple: true,
                colSpan: 3,
                listChoices: this.qualiopiIndicators,
                placeholder: 'private.training-organism.common.activities.form.placeholder.qualiopi',
                help: 'private.common.message-templates.qualiopiIndicatorsTooltip'
            },
        ];
        this.appFormFieldsGroupData = appFormFieldsData;
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    testTemplate(): void {
        this.disabledTest = true;
        this._messageTemplateService.testTemplate(this.messageTemplate).subscribe({
                next: () => {
                    this.loading = false;
                    const isEmail = this.messageTemplate.type === MessageTemplateType.EMAIL;
                    this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(
                        this._translateService.instant('private.common.message-templates.actions.testSend', {
                            type: isEmail ? 'email' : 'SMS',
                            sendTo: isEmail ? this.user.email : this.user.phoneNumber
                        })));
                    this.disabledTest = false;
                },
                error: (httpErrorResponse: HttpErrorResponse) => {
                    this.loading = false;
                    this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
                    this.disabledTest = false;
                }
            }
        );
    }

    updateState(messageTemplateStateToUpdate: MessageTemplate): void {
        if (messageTemplateStateToUpdate.state !== this.messageTemplate.state) {
            this.showDeactivate = false;
            this._router.navigate(['../../reglages'], {relativeTo: this._route});
        }
    }

    create(): void {
        this.loading = true;
        this.errorMessages = [];
        const messageTemplateValue = this.formGroup.getRawValue().messageTemplate;
        this._messageTemplateService.create({
            title: messageTemplateValue.title,
            subject: messageTemplateValue.subject,
            type: messageTemplateValue.type.toLowerCase(),
            body: messageTemplateValue.body,
            entityClass: messageTemplateValue.entityClass,
            events: messageTemplateValue.events,
            certifInfos: messageTemplateValue.certifInfos,
            tags: messageTemplateValue.tags,
            sendAs: messageTemplateValue.sendAs,
            replyTo: messageTemplateValue.replyTo?.length ? this.convertStringToArray(messageTemplateValue.replyTo) : [],
            cc: messageTemplateValue.cc?.length ? this.convertStringToArray(messageTemplateValue.cc) : [],
            cci: messageTemplateValue.cci?.length ? this.convertStringToArray(messageTemplateValue.cci) : [],
            to: messageTemplateValue.to?.length ? this.convertStringToArray(messageTemplateValue.to) : [],
            delay: '+' + messageTemplateValue.delayQuantity + messageTemplateValue.delayUnit,
            enforceConditions: messageTemplateValue.enforceConditions ?? false,
            qualiopiIndicators: messageTemplateValue.qualiopiIndicators,
            allowResend: messageTemplateValue.allowResend ?? false
        }).subscribe({
            next: (messageTemplateCreated) => {
                this.showDeactivate = false;
                this.loading = false;
                this.messageTemplate = messageTemplateCreated;
                this._router.navigate(['../editer-message-template/' + messageTemplateCreated.id], {relativeTo: this._route});
            },
            error: (httpErrorResponse: HttpErrorResponse) => {
                this.loading = false;
                this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
            }
        });
    }

    convertStringToArray(param: string): string[] {
        return param.replace(/\s/g, '').split(',');
    }

    getCertificationFolderEventChoice(): { name: string; value: { value: string; key: string; }[] }[] {

        const mappedFolderStates = getCertificationFolderAllStates().map(stateEntry => {
            return {
                key: this._translateService.instant(stateEntry.label),
                value: 'certificationFolder.' + stateEntry.value
            };
        });
        const mappedAccrochageStates = getCertificationFolderAccrochageStates().map(stateEntry => {
            return {
                key: this._translateService.instant(stateEntry.label),
                value: 'certificationFolder.' + stateEntry.value
            };
        });
        const folderStates = [
            {
                key: this._translateService.instant('private.common.message-templates.events.allFolderEvent'),
                value: 'certificationFolder.*'
            },
            ...mappedAccrochageStates,
            ...mappedFolderStates
        ];
        return [{
            name: this._translateService.instant('private.application.webhooks.events.certificationFolder.label'),
            value: folderStates
        },
            ...this.getFileEventChoice('certificationFolderFile')
        ];

    }

    getRegistrationFolderEventChoice(): { name: string; value: { value: string; key: string; }[] }[] {
        const mappedFolderStates = getRegistrationFolderAllStates().map(stateEntry => {
            return {
                key: this._translateService.instant(stateEntry.label),
                value: 'registrationFolder.' + stateEntry.value,
            };
        });
        const folderStates = [
            {
                key: this._translateService.instant('private.common.message-templates.events.allFolderEvent'),
                value: 'registrationFolder.*'
            },
            {
                key: this._translateService.instant('private.common.message-templates.events.proposalApplied'),
                value: 'registrationFolder.proposalApplied'
            },
            ...mappedFolderStates
        ];
        return [
            {
                name: this._translateService.instant('private.application.webhooks.events.registrationFolder.label'),
                value: folderStates
            },
            ...this.getFileEventChoice('registrationFolderFile')
        ];

    }

    getCertificationPartnerEventChoice(): { name: string; value: { value: string; key: string; }[] }[] {
        const mappedStates = getCertificationPartnerAllStates().map(stateEntry => {
            return {
                key: this._translateService.instant(stateEntry.label),
                value: 'certificationPartner.' + stateEntry.value,
            };
        });
        const states = [
            {
                key: this._translateService.instant('private.common.message-templates.events.allCertificationPartnerEvent'),
                value: 'certificationPartner.*'
            },
            ...mappedStates
        ];
        return [
            {
                name: this._translateService.instant('private.application.webhooks.events.certificationPartner.label'),
                value: states
            },
            ...this.getFileEventChoice('certificationPartnerFile'),
            ...this.getInvoiceEventChoice('certificationPartnerInvoice'),
            ...this.getAuditEventChoice()
        ];
    }

    getProposalEventChoice(): { name: string; value: { value: string; key: string; }[] }[] {
        const mappedStates = getProposalAllStates().filter((mappedState) => mappedState.value !== ProposalStates.TEMPLATE).map(stateEntry => {
            return {
                key: this._translateService.instant(stateEntry.label),
                value: 'proposal.' + stateEntry.value
            };
        });
        const folderStates = [
            {
                key: this._translateService.instant('private.common.message-templates.events.allProposalEvent'),
                value: 'proposal.*'
            },
            {
                key: this._translateService.instant('private.common.message-templates.events.proposalCreated'),
                value: 'proposal.created'
            },
            {
                key: this._translateService.instant('private.common.message-templates.events.proposalUpdated'),
                value: 'proposal.updated'
            },
            ...mappedStates,
            {
                key: this._translateService.instant('private.common.message-templates.events.proposalDeleted'),
                value: 'proposal.deleted'
            }
        ];
        return [
            {
                name: this._translateService.instant('private.application.webhooks.events.proposal.label'),
                value: folderStates
            }
        ];
    }

    getCertificationFolderSurveyEventChoice(): { name: string; value: { value: string; key: string; }[] }[] {
        const mappedEvents = [
            {
                key: this._translateService.instant('private.common.certificationFolderSurvey.created'),
                value: 'certificationFolderSurvey.created'
            },
            {
                key: this._translateService.instant('private.common.certificationFolderSurvey.sixMonthExperienceAvailable'),
                value: 'certificationFolderSurvey.sixMonthExperienceAvailable'
            },
            {
                key: this._translateService.instant('private.common.certificationFolderSurvey.longTermExperienceAvailable'),
                value: 'certificationFolderSurvey.longTermExperienceAvailable'
            },
            {
                key: this._translateService.instant('private.common.certificationFolderSurvey.initialExperienceAnswered'),
                value: 'certificationFolderSurvey.initialExperienceAnswered'
            },
            {
                key: this._translateService.instant('private.common.certificationFolderSurvey.sixMonthExperienceAnswered'),
                value: 'certificationFolderSurvey.sixMonthExperienceAnswered'
            },
            {
                key: this._translateService.instant('private.common.certificationFolderSurvey.longTermExperienceAnswered'),
                value: 'certificationFolderSurvey.longTermExperienceAnswered'
            }
        ];

        const folderStates = [
            {
                key: this._translateService.instant('private.common.message-templates.events.allSurveyEvent'),
                value: 'certificationFolderSurvey.*'
            },
            ...mappedEvents
        ];
        return [{
            name: 'Événements sur les enquêtes',
            value: folderStates
        }];
    }

    setErrorMessages(error: string[]): void {
        this.errorMessages = error;
    }

    canDeactivate(): boolean {
        return !this.loading && (this.form?.submitted || !this.formGroup?.dirty || !this.showDeactivate);
    }

    getDelayUnitChoices(): { value: string, key: string }[] {
        return [
            {value: 'minutes', key: 'minutes'},
            {value: 'hours', key: 'heures'},
            {value: 'days', key: 'jours'},
            {value: 'weeks', key: 'semaines'},
            {value: 'months', key: 'mois'}
        ];
    }

    getDelayInfos(): { quantity: number, unit: string } {
        const delay = this.messageTemplate.delay;
        if (delay) {
            const regExp = new RegExp('^\\+([0-9]+)([a-z]+)$');
            const results = delay.match(regExp);
            return {quantity: Number(results[1]), unit: results[2]};
        } else {
            return {quantity: 0, unit: 'minutes'};
        }
    }

    isEnforceConditionsRemoved(delay: number): boolean {
        return delay === 0;
    }

    enforceConditionsAppFormField(delay: number): AppFormFieldData {
        const appFormFieldEvents = this.appFormFieldsGroupData['options'].find(field => field.controlName === 'enforceConditions');

        if (this.isEnforceConditionsRemoved(delay)) {
            appFormFieldEvents.removed = true;
            appFormFieldEvents.value = false;
        } else {
            appFormFieldEvents.removed = false;
        }

        return appFormFieldEvents;
    }

    seeFolders(): void {
        if (this.messageTemplate?.entityClass === EntityClass.CERTIFICATION_FOLDER) {
            this._router.navigate(['/', 'certification', 'dossiers', 'kanban'], {queryParams: {messageTemplate: this.messageTemplate.id}});
        } else if (this.messageTemplate?.entityClass === EntityClass.REGISTRATION_FOLDER) {
            this._router.navigate(['/', 'formation', 'dossiers', 'kanban'], {queryParams: {messageTemplate: this.messageTemplate.id}});
        }
    }

    getFileEventChoice(entityClassFile: string): any {
        const fileStates = getEventFileStatesChoices(entityClassFile).map(stateEntry => {
            return {
                key: this._translateService.instant(stateEntry.label),
                value: stateEntry.value
            };
        });
        return [
            {
                name: this._translateService.instant('private.application.webhooks.events.' + entityClassFile + '.label'),
                value: fileStates
            }
        ];
    }

    getInvoiceEventChoice(entityClass: string): any {
        return [
            {
                name: this._translateService.instant('private.application.webhooks.events.' + entityClass + '.label'),
                value: [
                    {
                        key: this._translateService.instant('private.application.webhooks.events.certificationPartnerInvoice.invoice'),
                        value: entityClass + '.*'
                    },
                    {
                        key: this._translateService.instant('private.application.webhooks.events.certificationPartnerInvoice.created'),
                        value: entityClass + '.created'
                    },
                    {
                        key: this._translateService.instant('private.application.webhooks.events.certificationPartnerInvoice.updated'),
                        value: entityClass + '.updated'
                    },
                    {
                        key: this._translateService.instant('private.application.webhooks.events.certificationPartnerInvoice.paid'),
                        value: entityClass + '.paid'
                    },
                    {
                        key: this._translateService.instant('private.application.webhooks.events.certificationPartnerInvoice.deleted'),
                        value: entityClass + '.deleted'
                    }
                ]
            }
        ];
    }

    getAuditEventChoice(): any {
        return [
            {
                name: this._translateService.instant('private.application.webhooks.events.certificationPartnerAudit.label'),
                value: [
                    {
                        key: this._translateService.instant('private.application.webhooks.events.certificationPartnerAudit.audit'),
                        value: 'certificationPartnerAudit.*'
                    },
                    {
                        key: this._translateService.instant('private.application.webhooks.events.certificationPartnerAudit.pendingComputation'),
                        value: 'certificationPartnerAudit.pendingComputation'
                    },
                    {
                        key: this._translateService.instant('private.application.webhooks.events.certificationPartnerAudit.computing'),
                        value: 'certificationPartnerAudit.computing'
                    },
                    {
                        key: this._translateService.instant('private.application.webhooks.events.certificationPartnerAudit.inProgress'),
                        value: 'certificationPartnerAudit.inProgress'
                    },
                    {
                        key: this._translateService.instant('private.application.webhooks.events.certificationPartnerAudit.completed'),
                        value: 'certificationPartnerAudit.completed'
                    },
                    {
                        key: this._translateService.instant('private.application.webhooks.events.certificationPartnerAudit.compliant'),
                        value: 'certificationPartnerAudit.compliant'
                    },
                    {
                        key: this._translateService.instant('private.application.webhooks.events.certificationPartnerAudit.nonCompliant'),
                        value: 'certificationPartnerAudit.nonCompliant'
                    },
                    {
                        key: this._translateService.instant('private.application.webhooks.events.certificationPartnerAudit.partiallyCompliant'),
                        value: 'certificationPartnerAudit.partiallyCompliant'
                    },
                ]
            }
        ];
    }

    getSMSCount(): number {
        const bodyLength = this.formGroup?.get('messageTemplate')?.get('body')?.value?.length;
        return bodyLength <= 70 ? 1 : Math.ceil((bodyLength - 70) / 67) + 1;
    }
}
