import {Injectable, } from '@angular/core';
import {ActivatedRouteSnapshot, Resolve, Router, RouterStateSnapshot} from '@angular/router';
import {Observable} from 'rxjs';
import {catchError} from 'rxjs/operators';
import {MessageTemplate} from '../../shared/api/models/message-template';
import {MessageTemplateService} from '../../shared/api/services/message-template.service';

@Injectable()
export class MessageTemplateResolver implements Resolve<MessageTemplate> {
    constructor(
        private _messageTemplateService: MessageTemplateService,
        private router: Router,
    ) {
    }

    resolve(
        route: ActivatedRouteSnapshot,
        state: RouterStateSnapshot
    ): Observable<any> {
        return this._messageTemplateService.get(route.params.id)
            .pipe(catchError(() => this.router.navigateByUrl('/')));
    }
}
