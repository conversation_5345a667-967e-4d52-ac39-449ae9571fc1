<app-wrapper-spinner class="bg-white" [active]="isLoading">

    <table [dataSource]="displayedData" mat-table
           *ngIf=" isLoading || displayedData?.length || activeFilters ; else noMessage">
        <ng-container matColumnDef="title">
            <th mat-header-cell *matHeaderCellDef>
                {{'private.common.message-templates.column.title' | translate}}
            </th>
            <td *matCellDef="let messageTemplate" class="text-left" [ngClass]="columns ? 'w-3/4' : 'w-2/6'" mat-cell
                [matTooltip]="getMessageTemplateSummary(messageTemplate)">
                <p> {{messageTemplate.title}}</p>
                <p class="text-secondary" *ngIf="!displayedColumns.includes('type')">
                {{'private.common.message-templates.entityClass.value.' + messageTemplate.entityClass | translate  }} - {{ 'private.common.message-templates.type.value.' + messageTemplate.type | translate}}
                </p>
            </td>
        </ng-container>
        <ng-container matColumnDef="type" *ngIf="displayedColumns.includes('type')">
            <th mat-header-cell *matHeaderCellDef class="pl-0">
                {{'private.common.message-templates.column.type' | translate}}
            </th>
            <td *matCellDef="let messageTemplate" class="text-left w-1/6" mat-cell>
                {{ 'private.common.message-templates.type.value.' + messageTemplate.type | translate}}
            </td>
        </ng-container>
        <ng-container matColumnDef="state">
            <th mat-header-cell *matHeaderCellDef class="pl-0">
                <app-table-filter [filters]="stateFilters" (selectFilter)="onApplyFilter('state', $event)" [defaultValue]="defaultValue">
                    {{'private.common.message-templates.state.title' | translate}}
                </app-table-filter>
            </th>
            <td *matCellDef="let messageTemplate" class="text-left" [ngClass]="columns ? 'w-1/4' : 'w-1/6'" mat-cell>
                {{ 'private.common.message-templates.state.value.' + messageTemplate.state | translate}}
            </td>
        </ng-container>
        <ng-container matColumnDef="entityClass" *ngIf="displayedColumns.includes('entityClass')">
            <th mat-header-cell *matHeaderCellDef class="pl-0">
                <app-table-filter [filters]="entityClassFilters" (selectFilter)="onApplyFilter('entityClass', $event)">
                    {{'private.common.message-templates.entityClass.title' | translate}}
                </app-table-filter>
            </th>
            <td *matCellDef="let messageTemplate" class="text-left w-1/6" mat-cell>
                {{'private.common.message-templates.entityClass.value.' + messageTemplate.entityClass | translate}}
            </td>
        </ng-container>
        <ng-container matColumnDef="menu">
            <th mat-header-cell *matHeaderCellDef class="pl-0">
            </th>
            <td *matCellDef="let messageTemplate" class="text-right" mat-cell>
                <app-message-template-menu [messageTemplate]="messageTemplate"
                                           (processedMessageTemplate)="processedMessageTemplate($event)"
                                           (deleteMessageTemplate)="deleteMessageTemplate()"
                                           (duplicateMessageTemplate)="duplicateMessageTemplate($event)"
                                           (errorMessages)="setErrorMessages($event)"
                                           (loading)="setLoading($event)">
                </app-message-template-menu>
            </td>
        </ng-container>

        <ng-container matColumnDef="noDataForFilters">
            <td mat-footer-cell *matFooterCellDef [attr.colspan]="displayedColumns.length">
                {{'common.table.filters.no-data' | translate }}
            </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row (click)="onRowClick(row)" *matRowDef="let row; columns: displayedColumns;"></tr>
        <tr mat-footer-row *matFooterRowDef="['noDataForFilters']" [hidden]="isLoading || displayedData?.length"></tr>
    </table>

    <app-paginator [length]="total"
                   [pageSizeOptions]="pageSizeOptions"
                   [scrollTopOnPageChange]="false"
                   (page)="onPageEvent($event)">
    </app-paginator>

    <mat-error *ngIf="errorMessages.length">
        <ul>
            <li *ngFor="let errorMessage of errorMessages">{{ errorMessage }}</li>
        </ul>
    </mat-error>

</app-wrapper-spinner>

<ng-template #noMessage>
    {{'private.common.message-templates.noData' | translate}}
</ng-template>
