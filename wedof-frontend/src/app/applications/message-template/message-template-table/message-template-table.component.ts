import {Component, EventEmitter, Injector, Input, OnChanges, Output} from '@angular/core';
import {Observable} from 'rxjs';
import {TableFilter} from '../../../shared/material/table/table-filter/table-filter.component';
import {MessageTemplate, MessageTemplateState} from '../../../shared/api/models/message-template';
import {AbstractTableComponent} from '../../../shared/material/table/abstract-table.component';
import {MessageTemplateService} from '../../../shared/api/services/message-template.service';
import {EntityClass} from '../../../shared/utils/enums/entity-class';
import {PaginatedResponse} from '../../../shared/api/services/abstract-paginated.service';
import {TranslateService} from '@ngx-translate/core';
import {
    getCertificationFolderAllStates,
    getCertificationPartnerAllStates,
    getEventFileStatesChoices,
    getProposalAllStates,
    getRegistrationFolderAllStates
} from '../../../shared/utils/states-utils';
import {Certification} from '../../../shared/api/models/certification';
import {ProposalStates} from '../../../shared/api/models/proposal';

@Component({
    selector: 'app-message-template-table',
    templateUrl: './message-template-table.component.html',
    styleUrls: ['./message-template-table.component.scss']
})
export class MessageTemplateTableComponent extends AbstractTableComponent<MessageTemplate> implements OnChanges {

    stateFilters: TableFilter[];
    entityClassFilters: TableFilter[];
    errorMessages: string[] = [];
    defaultValue: TableFilter = {label: 'state', value: MessageTemplateState.ACTIVE};

    @Input() limit?: number;
    @Input() columns?: string[];
    @Input() certification?: Certification;
    @Input() entityClass?: string[];
    @Output() edit: EventEmitter<MessageTemplate> = new EventEmitter<MessageTemplate>();

    constructor(injector: Injector,
                private _translateService: TranslateService,
                private _messageTemplateService: MessageTemplateService) {
        super(injector);
        this.displayedColumns = ['title', 'type', 'state', 'entityClass', 'menu'];
        this.stateFilters = Object.values(MessageTemplateState).map(value => ({
            label: `private.common.message-templates.state.value.${value}`,
            value
        }));
        this.entityClassFilters = Object.values(EntityClass).filter((value) =>
            [EntityClass.REGISTRATION_FOLDER, EntityClass.CERTIFICATION_FOLDER, EntityClass.PROPOSAL,
                EntityClass.CERTIFICATIONS_PARTNER, EntityClass.CERTIFICATION_FOLDER_SURVEY].includes(value)).map(value => ({
            label: `private.common.message-templates.entityClass.value.${value}`,
            value
        }));
    }

    ngOnChanges(): void {
        this.displayedColumns = this.columns ?? this.displayedColumns;
        this.onApplyFilter('state', [MessageTemplateState.ACTIVE]);
        if (this.certification) {
            this.onApplyFilter('certifInfo', [this.certification.certifInfo]);
        }
        if (this.entityClass && this.displayedColumns.includes('type')) {
            this.onApplyFilter('entityClass', this.entityClass);
        }
        if (this.limit) {
            this.pageSizeOptions = [this.limit];
        }
    }

    protected refreshData(): Observable<PaginatedResponse<MessageTemplate>> {
        return this._messageTemplateService.list({
            ...this._filters$.value,
            limit: this.paginator.pageSize,
            page: this.paginator.pageIndex + 1
        });
    }

    onRowClick(messageTemplate: MessageTemplate): void {
        this.edit.emit(messageTemplate);
    }

    onApplyFilter(name: string, value: string[]): void {
        this.applyFilter({name: name, value: value});
    }

    processedMessageTemplate(messageTemplateToUpdate: MessageTemplate): void {
        this.displayedData = this.displayedData.map((messageTemplate) => {
            return messageTemplate.id === messageTemplateToUpdate.id ? messageTemplateToUpdate : messageTemplate;
        });
    }

    deleteMessageTemplate(): void {
        this.forceRefresh();
    }

    duplicateMessageTemplate(messageTemplate: MessageTemplate): void {
        this.edit.emit(messageTemplate);
    }

    setErrorMessages(error: string[]): void {
        this.errorMessages = error;
    }

    setLoading(isLoading: boolean): void {
        this.isLoading = isLoading;
    }

    getMessageTemplateSummary(messageTemplate: MessageTemplate): string {
        const type = this._translateService.instant('private.common.message-templates.type.value.' + messageTemplate.type).toLowerCase();
        const state = this._translateService.instant('private.common.message-templates.state.value.' + messageTemplate.state).toLowerCase();
        const entityClass = (messageTemplate.entityClass === EntityClass.PROPOSAL || messageTemplate.entityClass === EntityClass.CERTIFICATION_FOLDER_SURVEY ? ' d\'une ' : ' d\'un ') + this._translateService.instant('private.common.message-templates.entityClass.value.' + messageTemplate.entityClass).toLowerCase();
        const certification = messageTemplate.entityClass === EntityClass.PROPOSAL ? '' :
            'et associé à ' + (!messageTemplate.certifInfos?.length ? 'n\'importe quelle certification' :
            (messageTemplate.certifInfos?.length + (messageTemplate.certifInfos.length === 1 ? ' certification ' : ' certifications')));
        const eventsByClass = {
            [EntityClass.CERTIFICATION_FOLDER]: this.getCertificationFolderEventChoice(),
            [EntityClass.REGISTRATION_FOLDER]: this.getRegistrationFolderEventChoice(),
            [EntityClass.PROPOSAL]: this.getProposalEventChoice(),
            [EntityClass.CERTIFICATIONS_PARTNER]: this.getCertificationPartnerEventChoice(),
            [EntityClass.CERTIFICATION_FOLDER_SURVEY]: this.getCertificationFolderSurveyEventChoice()
        };
        const events = eventsByClass[messageTemplate.entityClass];
        let eventsType: string = messageTemplate.events.map(event => {
            const eventChoice = events.find(currentEventChoice =>
                currentEventChoice.value.some(item => item.value === event)
            );
            if (eventChoice) {
                const key = eventChoice.value.find(item => item.value === event)?.key;
                return key || '';
            }
            return '';
        }).join(', ');
        eventsType += ' ';
        return this._translateService.instant('private.common.message-templates.summary', {
            type: type,
            state: state,
            delay: this.getDelayInfos(messageTemplate.delay),
            events: eventsType,
            entityClass: entityClass,
            certification: certification,
            hasTag: !messageTemplate.tags?.length ? '' : 'et avec le(s) tag(s)',
            tags: !messageTemplate.tags?.length ? '' : messageTemplate.tags.join(', '),
        });
    }

    getCertificationFolderEventChoice(): { name: string; value: { value: string; key: string; }[] }[] {
        const mappedStates = getCertificationFolderAllStates().map(stateEntry => {
            return {
                key: this._translateService.instant(stateEntry.label),
                value: 'certificationFolder.' + stateEntry.value
            };
        });
        const folderStates = [
            {
                value: 'certificationFolder.*',
                key: this._translateService.instant('private.common.message-templates.events.allFolderEvent')
            },
            ...mappedStates
        ];
        return [{
            name: this._translateService.instant('private.application.webhooks.events.certificationFolder.label'),
            value: folderStates
        },
            ...this.getFileEventChoice('certificationFolderFile')
        ];
    }

    getRegistrationFolderEventChoice(): { name: string; value: { value: string; key: string; }[] }[] {
        const mappedStates = getRegistrationFolderAllStates().map(stateEntry => {
            return {
                key: this._translateService.instant(stateEntry.label),
                value: 'registrationFolder.' + stateEntry.value,
                disabled: false
            };
        });
        return [
            {
                name: this._translateService.instant('private.application.webhooks.events.registrationFolder.label'),
                value: mappedStates
            },
            ...this.getFileEventChoice('registrationFolderFile')
        ];
    }

    getCertificationPartnerEventChoice(): { name: string; value: { value: string; key: string; }[] }[] {
        const mappedStates = getCertificationPartnerAllStates().map(stateEntry => {
            return {
                key: this._translateService.instant(stateEntry.label),
                value: 'certificationPartner.' + stateEntry.value,
                disabled: false
            };
        });
        return [
            {
                name: this._translateService.instant('private.application.webhooks.events.certificationPartner.title'),
                value: mappedStates
            },
            ...this.getFileEventChoice('certificationPartnerFile'),
            ...this.getInvoiceEventChoice('certificationPartnerInvoice'),
            ...this.getAuditEventChoice()
        ];
    }

    getProposalEventChoice(): { name: string; value: { value: string; key: string; }[] }[] {
        const mappedStates = getProposalAllStates().filter((mappedState) => mappedState.value !== ProposalStates.TEMPLATE).map(stateEntry => {
            return {
                key: this._translateService.instant(stateEntry.label),
                value: 'proposal.' + stateEntry.value
            };
        });
        const folderStates = [
            {value: 'proposal.*', key: 'Tous les événements sur la proposition commerciale'},
            {value: 'proposal.created', key: 'Créé'},
            {value: 'proposal.updated', key: 'Mis à jour'},
            ...mappedStates,
            {value: 'proposal.deleted', key: 'Supprimée'}
        ];
        return [
            {
                name: this._translateService.instant('private.application.webhooks.events.proposal.label'),
                value: folderStates
            }
        ];
    }

    getCertificationFolderSurveyEventChoice(): { name: string; value: { value: string; key: string; }[] }[] {
        const events = [
            {value: 'certificationFolderSurvey.*', key: 'Tous les événements sur une enquête'},
            {
                value: 'certificationFolderSurvey.created',
                key: this._translateService.instant('private.common.certificationFolderSurvey.created')
            },
            {
                value: 'certificationFolderSurvey.sixMonthExperienceAvailable',
                key: this._translateService.instant('private.common.certificationFolderSurvey.sixMonthExperienceAvailable')
            },
            {
                value: 'certificationFolderSurvey.longTermExperienceAvailable',
                key: this._translateService.instant('private.common.certificationFolderSurvey.longTermExperienceAvailable')
            },
            {
                value: 'certificationFolderSurvey.initialExperienceAnswered',
                key: this._translateService.instant('private.common.certificationFolderSurvey.initialExperienceAnswered')
            },
            {
                value: 'certificationFolderSurvey.sixMonthExperienceAnswered',
                key: this._translateService.instant('private.common.certificationFolderSurvey.sixMonthExperienceAnswered')
            },
            {
                value: 'certificationFolderSurvey.longTermExperienceAnswered',
                key: this._translateService.instant('private.common.certificationFolderSurvey.longTermExperienceAnswered')
            }
        ];
        return [{
            name: this._translateService.instant('private.common.message-templates.events.allSurveyEvent'),
            value: events
        }];
    }

    getFileEventChoice(entityClassFile: string): any {
        const fileStates = getEventFileStatesChoices(entityClassFile).map(stateEntry => {
            return {
                key: this._translateService.instant(stateEntry.label),
                value: stateEntry.value
            };
        });
        return [
            {
                name: this._translateService.instant('private.application.webhooks.events.' + entityClassFile + '.label'),
                value: fileStates
            }
        ];
    }

    getInvoiceEventChoice(entityClass: string): any {
        return [
            {
                name: this._translateService.instant('private.application.webhooks.events.' + entityClass + '.label'),
                value: [
                    {
                        key: this._translateService.instant('private.application.webhooks.events.certificationPartnerInvoice.invoice'),
                        value: entityClass + '.*'
                    },
                    {
                        key: this._translateService.instant('private.application.webhooks.events.certificationPartnerInvoice.created'),
                        value: entityClass + '.created'
                    },
                    {
                        key: this._translateService.instant('private.application.webhooks.events.certificationPartnerInvoice.updated'),
                        value: entityClass + '.updated'
                    },
                    {
                        key: this._translateService.instant('private.application.webhooks.events.certificationPartnerInvoice.paid'),
                        value: entityClass + '.paid'
                    },
                    {
                        key: this._translateService.instant('private.application.webhooks.events.certificationPartnerInvoice.deleted'),
                        value: entityClass + '.deleted'
                    }
                ]
            }
        ];
    }

    getAuditEventChoice(): any {
        return [
            {
                name: this._translateService.instant('private.application.webhooks.events.certificationPartnerAudit.label'),
                value: [
                    {
                        key: this._translateService.instant('private.application.webhooks.events.certificationPartnerAudit.audit'),
                        value: 'certificationPartnerAudit.*'
                    },
                    {
                        key: this._translateService.instant('private.application.webhooks.events.certificationPartnerAudit.pendingComputation'),
                        value: 'certificationPartnerAudit.pendingComputation'
                    },
                    {
                        key: this._translateService.instant('private.application.webhooks.events.certificationPartnerAudit.computing'),
                        value: 'certificationPartnerAudit.computing'
                    },
                    {
                        key: this._translateService.instant('private.application.webhooks.events.certificationPartnerAudit.inProgress'),
                        value: 'certificationPartnerAudit.inProgress'
                    },
                    {
                        key: this._translateService.instant('private.application.webhooks.events.certificationPartnerAudit.completed'),
                        value: 'certificationPartnerAudit.completed'
                    },
                    {
                        key: this._translateService.instant('private.application.webhooks.events.certificationPartnerAudit.compliant'),
                        value: 'certificationPartnerAudit.compliant'
                    },
                    {
                        key: this._translateService.instant('private.application.webhooks.events.certificationPartnerAudit.nonCompliant'),
                        value: 'certificationPartnerAudit.nonCompliant'
                    },
                    {
                        key: this._translateService.instant('private.application.webhooks.events.certificationPartnerAudit.partiallyCompliant'),
                        value: 'certificationPartnerAudit.partiallyCompliant'
                    },
                ]
            }
        ];
    }

    getDelayInfos(delay): string {
        if (delay && delay !== '+0minutes') {
            const regExp = new RegExp('^\\+([0-9]+)([a-z]+)$');
            const results = delay.match(regExp);
            const unit = results[2] === 'hours' ? 'heures' : results[2] === 'days' ? 'jours' : results[2] === 'weeks' ? 'semaines' :
                results[2] === 'months' ? 'mois' : results[2];
            return 'avec un délai de ' + Number(results[1]) + ' ' + unit;
        } else {
            return 'immédiatement';
        }
    }
}
