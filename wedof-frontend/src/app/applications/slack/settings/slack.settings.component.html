<app-application-settings [application]="application" [submitButton]="getSubmitButton()">
    <ng-template #noChannels>Content to render when condition is false.</ng-template>
    <table *ngIf="channelsDataSource; else noChannels" [dataSource]="channelsDataSource" class="table" mat-table>
        <!-- Name Column -->
        <ng-container matColumnDef="name">
            <td *matCellDef="let channel" mat-cell> {{channel.name}} </td>
        </ng-container>
        <ng-container matColumnDef="events">
            <td *matCellDef="let channel" mat-cell>
                <mat-form-field  [formGroup]="formGroup" appearance="standard">
                    <mat-select (closed)="updateEventsChannel(channel.id)"
                                panelClass="mat-select-panel"
                                (selectionChange)="eventsSelectionChange(channel.id, $event)"
                                [(value)]="channel.events"
                                multiple
                                required>
                        <mat-option  value="*">Tous les événements</mat-option>
                        <mat-optgroup  *ngFor="let group of GroupsEvents" [label]="group.name">
                            <mat-option *ngFor="let namespace of group.namespace" [value]="namespace.value">
                                {{namespace.viewValue}}
                            </mat-option>
                        </mat-optgroup>
                    </mat-select>
                </mat-form-field>
            </td>
        </ng-container>
        <ng-container matColumnDef="RGPD">
            <td *matCellDef="let channel" mat-cell>
                <mat-slide-toggle
                    (change)="updateRgpdChannel(channel.id, $event)"
                    [checked]="channel.RGPD"
                    color="primary">
                    Compatibilité RGPD
                </mat-slide-toggle>
            </td>
        </ng-container>
        <ng-container matColumnDef="actions">
            <td *matCellDef="let channel" class="text-right" mat-cell>
                <button (click)="removeChannel(channel.id)" mat-icon-button type="button">
                    <mat-icon color="warn" svgIcon="delete"></mat-icon>
                </button>
            </td>
        </ng-container>
        <tr *matRowDef="let row; columns:['name', 'events', 'RGPD', 'actions'];" mat-row></tr>
    </table>
    <button (click)="linkToSlack()" class="px-6 ml-3 mt-3" color="primary" mat-flat-button type="button">
        {{ 'Associer à un channel' | translate }}
    </button>
</app-application-settings>
