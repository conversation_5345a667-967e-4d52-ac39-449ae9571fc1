import {Component, Injector} from '@angular/core';
import {Router} from '@angular/router';
import {MatTableDataSource} from '@angular/material/table';
import {HttpErrorResponse} from '@angular/common/http';
import {AbstractApplicationSettingsComponent} from '../../shared/settings/abstract-application-settings.component';
import {SlackChannel, SlackMetadata} from '../slack.application';
import {SubmitSettingsButton} from '../../shared/settings/application-settings.component';
import {OrganismApplication} from '../../../shared/api/models/organism-application';
import {MatSelectChange} from '@angular/material/select/select';
import {MatSlideToggleChange} from '@angular/material/slide-toggle';

@Component({
    selector: 'app-slack-settings',
    templateUrl: './slack.settings.component.html',
    styleUrls: ['./slack.settings.component.scss']
})
export class SlackSettingsComponent extends AbstractApplicationSettingsComponent<SlackMetadata> {

    private static appId = 'slack';
    channelsDataSource = new MatTableDataSource<SlackChannel>();
    GroupsEvents = [
        {
            name: 'Événements sur les dossiers de formation',
            namespace: [
                {value: 'registrationFolder.*', viewValue: 'Tous les événements sur les dossiers de formation'},
                {value: 'registrationFolder.notProcessed', viewValue: 'À traiter'},
                {value: 'registrationFolder.proposalApplied', viewValue: 'Proposition appliquée'},
                {value: 'registrationFolder.validated', viewValue: 'Validé'},
                {
                    value: 'registrationFolder.waitingAcceptation',
                    viewValue: 'Validé (En cours d\'instruction Pôle emploi)'
                },
                {value: 'registrationFolder.accepted', viewValue: 'Accepté'},
                {value: 'registrationFolder.inTraining', viewValue: 'En formation'},
                {value: 'registrationFolder.terminated', viewValue: 'Sortie de formation'},
                {value: 'registrationFolder.serviceDoneDeclared', viewValue: 'Service fait déclaré'},
                {value: 'registrationFolder.serviceDoneValidated', viewValue: 'Service fait validé'},
                {value: 'registrationFolder.canceledByAttendee', viewValue: 'Annulé (par le titulaire)'},
                {
                    value: 'registrationFolder.canceledByAttendeeNotRealized',
                    viewValue: 'Annulation titulaire (non réalisé)'
                },
                {value: 'registrationFolder.canceledByOrganism', viewValue: 'Annulé (par l\'organisme)'},
                {value: 'registrationFolder.refusedByAttendee', viewValue: 'Refus titulaire'},
                {value: 'registrationFolder.refusedByOrganism', viewValue: 'Refusé (par l\'organisme)'},
                {value: 'registrationFolder.rejectedWithoutTitulaireSuite', viewValue: 'Annulé sans suite'},
                {value: 'registrationFolderFile.added', viewValue: 'Fichier ajouté'},
                {value: 'registrationFolderFile.updated', viewValue: 'Fichier mis à jour'},
                {value: 'registrationFolderFile.deleted', viewValue: 'Fichier supprimé'},
                {value: 'registrationFolderFile.toReview', viewValue: 'Fichier à vérifier'},
                {value: 'registrationFolderFile.valid', viewValue: 'Fichier validé'},
                {value: 'registrationFolderFile.refused', viewValue: 'Fichier refusé'}
            ]
        },
        {
            name: 'Événements sur le contrôle des dossiers de formation',
            namespace: [
                {
                    value: 'registrationFolderControl.*',
                    viewValue: 'Tous les événements sur le contrôle des dossiers de formation'
                },
                {value: 'registrationFolderControl.inControl', viewValue: 'En cours de contrôle'},
                {value: 'registrationFolderControl.released', viewValue: 'Contrôle terminé'}
            ]
        },
        {
            name: 'Événements sur les dossiers de certification',
            namespace: [
                {value: 'certificationFolder.*', viewValue: 'Tous les événements sur les dossiers de certification'},
                {value: 'certificationFolder.toRegister', viewValue: 'À enregistrer'},
                {value: 'certificationFolder.refused', viewValue: 'Refusé'},
                {value: 'certificationFolder.registered', viewValue: 'Enregistré'},
                {value: 'certificationFolder.toTake', viewValue: 'Prêt à passer'},
                {value: 'certificationFolder.toControl', viewValue: 'À contrôler'},
                {value: 'certificationFolder.toRetake', viewValue: 'À repasser'},
                {value: 'certificationFolder.failed', viewValue: 'Échoué'},
                {value: 'certificationFolder.aborted', viewValue: 'Abandonné'},
                {value: 'certificationFolder.success', viewValue: 'Réussi'},
                {value: 'certificationFolderFile.added', viewValue: 'Fichier ajouté'},
                {value: 'certificationFolderFile.updated', viewValue: 'Fichier mis à jour'},
                {value: 'certificationFolderFile.deleted', viewValue: 'Fichier supprimé'},
                {value: 'certificationFolderFile.toReview', viewValue: 'Fichier à vérifier'},
                {value: 'certificationFolderFile.valid', viewValue: 'Fichier validé'},
                {value: 'certificationFolderFile.refused', viewValue: 'Fichier refusé'}
            ]
        },
        {
            name: 'Événements sur les enquêtes',
            namespace: [
                {
                    value: 'certificationFolderSurvey.*',
                    viewValue: 'Tous les événements sur les enquêtes'
                },
                {value: 'certificationFolderSurvey.created', viewValue: 'Questionnaire "Situation professionnelle en début de cursus" est accessible (Enquête créée)'},
                {value: 'certificationFolderSurvey.sixMonthExperienceAvailable', viewValue: 'Questionnaire "Situation professionnelle de 6 mois" est accessible'},
                {value: 'certificationFolderSurvey.longTermExperienceAvailable', viewValue: 'Questionnaire "Situation professionnelle au moins un an" est accessible'},
                {value: 'certificationFolderSurvey.initialExperienceAnswered', viewValue: 'Questionnaire "Situation professionnelle en début de cursus" répondu'},
                {value: 'certificationFolderSurvey.sixMonthExperienceAnswered', viewValue: 'Questionnaire "Situation professionnelle de 6 mois" répondu'},
                {value: 'certificationFolderSurvey.longTermExperienceAnswered', viewValue: 'Questionnaire "Situation professionnelle au moins un an" répondu'}
            ]
        },
        {
            name: 'Événements sur les partenariats de certification',
            namespace: [
                {
                    value: 'certificationPartner.*',
                    viewValue: 'Tous les événements sur les partenariats de certification'
                },
                {value: 'certificationPartner.processing', viewValue: 'Demande en traitement'},
                {value: 'certificationPartner.active', viewValue: 'Partenariat actif'},
                {value: 'certificationPartner.aborted', viewValue: 'Demande abandonnée'},
                {value: 'certificationPartner.refused', viewValue: 'Demande refusée'},
                {value: 'certificationPartner.revoked', viewValue: 'Partenariat révoqué'},
                {value: 'certificationPartnerFile.added', viewValue: 'Fichier ajouté'},
                {value: 'certificationPartnerFile.updated', viewValue: 'Fichier mis à jour'},
                {value: 'certificationPartnerFile.deleted', viewValue: 'Fichier supprimé'},
                {value: 'certificationPartnerFile.toReview', viewValue: 'Fichier à vérifier'},
                {value: 'certificationPartnerFile.valid', viewValue: 'Fichier validé'},
                {value: 'certificationPartnerFile.refused', viewValue: 'Fichier refusé'},
                {value: 'certificationPartnerInvoice.created', viewValue: 'Facture créée'},
                {value: 'certificationPartnerInvoice.updated', viewValue: 'Facture mise à jour'},
                {value: 'certificationPartnerInvoice.paid', viewValue: 'Facture payée'},
                {value: 'certificationPartnerInvoice.deleted', viewValue: 'Facture supprimée'},
                {value: 'certificationPartnerAudit.pendingComputation', viewValue: 'Audit en préparation (collecte des données en cours)'},
                {value: 'certificationPartnerAudit.computing', viewValue: 'Audit analyse des données en cours'},
                {value: 'certificationPartnerAudit.inProgress', viewValue: 'Audit en cours'},
                {value: 'certificationPartnerAudit.completed', viewValue: 'Audit finalisé'},
                {value: 'certificationPartnerAudit.compliant', viewValue: 'Audit conforme'},
                {value: 'certificationPartnerAudit.nonCompliant', viewValue: 'Audit non conforme'},
                {value: 'certificationPartnerAudit.partiallyCompliant', viewValue: 'Audit partiellement conforme'},
            ]
        },
        {
            name: 'Événements sur la facturation',
            namespace: [
                {value: 'registrationFolderBilling.*', viewValue: 'Tous les événements sur la facturation'},
                {value: 'registrationFolderBilling.depositWait', viewValue: 'Acompte en attente de versement'},
                {value: 'registrationFolderBilling.depositPaid', viewValue: 'Acompte versé'},
                {value: 'registrationFolderBilling.toBill', viewValue: 'À facturer'},
                {value: 'registrationFolderBilling.billed', viewValue: 'Facturé'},
                {value: 'registrationFolderBilling.paid', viewValue: 'Payé'},
            ]
        },
        {
            name: 'Alerte sur les dossiers',
            namespace: [
                {value: 'registrationFolderAlert.*', viewValue: 'Toutes les alertes'},
                {value: 'registrationFolderAlert.notAccepted', viewValue: 'Pas accepté par l\'apprenant'},
                {value: 'registrationFolderAlert.notInTraining', viewValue: 'Pas en formation'},
                {value: 'registrationFolderAlert.notServiceDoneDeclared', viewValue: 'Service fait non déclaré'},
                {value: 'registrationFolderAlert.notValidated', viewValue: 'Pas validé par l\'organisme'},
            ]
        },
        {
            name: 'Autres',
            namespace: [
                {value: 'evaluation.*', viewValue: 'Tous les événements d\'évaluation'},
                {value: 'certification.*', viewValue: 'Tous les événements de certification'},
            ]
        }
    ];
    private channelsWithStars: { [id: string]: boolean } = {};

    constructor(
        _router: Router,
        injector: Injector,
    ) {
        super(SlackSettingsComponent.appId, _router, injector);
        this.channelsDataSource.connect().subscribe(channels => {
            this.channelsWithStars = this.getChannelsWithStars(channels);
        });
        this.metadata.subscribe((slackMetadata) => {
            if (slackMetadata) {
                const channels: SlackChannel[] = [];
                Object.keys(slackMetadata.channels).forEach((key) => {
                    const channelFromBack = slackMetadata.channels[key];
                    const eventsForFront = this.convertEventsFromBackToFront(channelFromBack.events);
                    channels.push({
                        id: key,
                        name: channelFromBack.name,
                        events: eventsForFront,
                        RGPD: channelFromBack.RGPD
                    });
                });
                this.channelsDataSource.connect().next(channels);
            }
        });
    }

    eventsSelectionChange(id: string, $event: MatSelectChange): void {
        const channels = this.channelsDataSource.connect().value;
        const newEvents: string[] = $event.value;
        // If new events has "*" and it had it before          => we have just added another event so we remove "*"
        // If new events has "*" and it did not have it before => we have just added "*" so we remove everything else
        if (newEvents.includes('*')) {
            const channel = channels.find(currentChannel => currentChannel.id === id);
            channel.events = this.channelsWithStars[id] ? channel.events.filter(event => event !== '*') : channel.events.filter(event => event === '*');
            this.channelsDataSource.connect().next(channels);
        }
    }

    updateEventsChannel(id: string): void {
        const channel = this.channelsDataSource.connect().value.find(chan => chan.id === id);
        this.updateChannel(channel);
    }

    updateRgpdChannel(id: string, $event: MatSlideToggleChange): void {
        const channel = this.channelsDataSource.connect().value.find(chan => chan.id === id);
        channel.RGPD = $event.checked;
        this.updateChannel(channel);
    }

    removeChannel(id: string): void {
        this._organismApplicationService.postData(SlackSettingsComponent.appId, 'removeChannel', {id: id}).subscribe((metadata) => {
            this.metadata.next(metadata);
        });
    }

    onSuccess(organismApplication: OrganismApplication): void {
        this._router.navigate(['mes-applications']);
    }

    linkToSlack(): void {
        this._organismApplicationService.getOAuth2Authorization('slack').subscribe();
    }

    getSubmitButton(): SubmitSettingsButton {
        return null;
    }

    onError(httpErrorResponse: HttpErrorResponse): void {
    }

    protected initForm(metadata: SlackMetadata): void {
        this.formGroup = this._fb.group({
            events: []
        });
    }

    private getChannelsWithStars(channels: SlackChannel[]): { [id: string]: boolean } {
        const channelsWithStars: { [id: string]: boolean } = {};
        channels.forEach(channel => {
            channelsWithStars[channel.id] = channel.events.filter(event => event === '*').length > 0;
        });
        return channelsWithStars;
    }

    private updateChannel(channel: SlackChannel): void {
        const eventsForBack = this.convertEventsFromFrontToBack(channel.events);
        const channelForBack = {...channel, events: eventsForBack};
        this._organismApplicationService.postData(SlackSettingsComponent.appId, 'updateChannel', {channel: channelForBack}).subscribe((metadata) => {
            this.metadata.next(metadata);
        });
    }

    private convertEventsFromBackToFront(eventsFromBack: string[]): string[] {
        const eventsForFront = [];
        const fileEventsForFront = new Set<string>(); // Use a set to avoid duplicate if we add the event several times
        eventsFromBack.forEach((eventFromBack) => {
            if (eventFromBack.indexOf('file') !== -1) {
                const eventEntity = eventFromBack.substring(0, eventFromBack.indexOf('.'));
                fileEventsForFront.add(eventEntity + '.file');
            } else {
                eventsForFront.push(eventFromBack);
            }
        });
        fileEventsForFront.forEach((fileEventForFront) => {
            eventsForFront.push(fileEventForFront);
        });
        return eventsForFront;
    }

    private convertEventsFromFrontToBack(eventsFromFront: string[]): string[] {
        const eventsForBack = [];
        eventsFromFront.forEach((eventFromFront) => {
            if (eventFromFront.indexOf('file') !== -1) {
                const eventEntity = eventFromFront.substring(0, eventFromFront.indexOf('.'));
                eventsForBack.push(eventEntity + '.fileAdded', eventEntity + '.fileUpdated', eventEntity + '.fileDeleted');
            } else {
                eventsForBack.push(eventFromFront);
            }
        });
        return eventsForBack;
    }
}
