import {application} from '../shared/application.decorator';
import {Application, staticImplements} from '../shared/application.interface';
import {SlackSettingsComponent} from './settings/slack.settings.component';

@application()
@staticImplements<Application>()
export class SlackApplication {
    public static appName(): string {
        return 'Slack';
    }

    public static appId(): string {
        return 'slack';
    }

    public static beta(): boolean {
        return false;
    }


    public static isSubscriptionOption(): boolean {
        return false;
    }

    public static hasTrialAvailable(): boolean {
        return false;
    }

    public static components(): any {
        return [
            SlackSettingsComponent
        ];
    }

    public static routing(): any {
        return {
            path: SlackApplication.appId(),
            children: [{
                path: 'reglages',
                component: SlackSettingsComponent
            }]
        };
    }

    public static entryPoints(): any {
        return {};
    }
}

export interface SlackChannel {
    id: string;
    name: string;
    events: string[];
    RGPD: boolean;
}

export interface SlackMetadata {
    channels: {[id: number]: Array<SlackChannel>};
}
