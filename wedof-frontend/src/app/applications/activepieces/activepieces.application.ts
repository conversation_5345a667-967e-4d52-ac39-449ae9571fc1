import {Application, staticImplements} from '../shared/application.interface';
import {ActivepiecesSettingsComponent} from './settings/activepieces.settings.component';
import {application} from '../shared/application.decorator';

@application()
@staticImplements<Application>()
export class ActivepiecesApplication {
    public static appName(): string {
        return 'Activepieces';
    }

    public static appId(): string {
        return 'activepieces';
    }

    public static beta(): boolean {
        return false;
    }

    public static isSubscriptionOption(): boolean {
        return false;
    }

    public static hasTrialAvailable(): boolean {
        return false;
    }

    public static components(): any {
        return [
            ActivepiecesSettingsComponent
        ];
    }

    public static priority(): number {
        return 1000;
    }

    public static routing(): any {
        return {
            path: ActivepiecesApplication.appId(),
            children: [{
                path: 'reglages',
                component: ActivepiecesSettingsComponent
            }]
        };
    }

    public static entryPoints(): any {
        return {};
    }

}

export interface ActivepiecesMetadata {
    name: string;
    domain?: string;
    apiKey?: string;
}
