<app-application-settings (formSubmitted)="submit()"
                          [application]="application"
                          [submitButton]="getSubmitButton()"
                          [loading]="loading">
    <p class="mt-2 mb-6"><b>Activepieces</b> est un <b>outil d'automatisation de processus open-source et gratuit</b>
        vous permettant de gagner du temps en automatisant vos tâches et en interconnectant vos outils. Profitez de
        l'API Wedof directement dans Activepieces avec une collection de <b>déclencheurs et d'actions</b> pour vos <b>processus</b>.
        Pour en savoir plus, <b><a class="mt-2" href="/aide/guides/applications/activepieces" target="_blank">consultez
            la documentation</a></b>.
    </p>
    <p class="mb-4">
        Vous avez <b>3 solutions</b> pour mettre en place Activepieces :
    </p>
    <li class="mb-3 ml-3">A) Utilisez la plateforme Cloud de l'éditeur Activepieces : <a target="_blank"
                                                                                         href="https://www.activepieces.com/">https://www.activepieces.com</a>.<br/>
        Vous déléguez l'hébergement et de la maintenance. Leurs serveurs sont hébergés aux Etats Unis.
    </li>
    <li class="mb-3 ml-3">B) Installez un serveur Activepieces (avec Docker par exemple).<br/>
        Vous vous occupez ainsi de l'hébergement et de la maintenance.
    </li>
    <li class="mb-3 ml-3">C) Activez <a href="/mes-applications/processus/reglages"><b>l'application Processus Métiers Automatisés de Wedof</b></a> sans engagement.<br/>
        Vous disposerez alors d'une version d'Activepièces hébergée par nos soins (en France) et nous nous occupons de la
        maintenance.
    </li>
    <p class="mb-4">Quelque soit la solution choisie, le connecteur <a target="_blank"
                                                          href="https://www.activepieces.com/pieces/wedof"><b>@activepieces/piece-wedof</b></a>
        est directement disponible dans Activepieces, facilitant encore plus l'intégration de vos outils.</p>

    <div [formGroup]="formGroup" class="mt-6">
        <h3 class="text-lg font-medium mb-4">Configuration</h3>
        <div class="grid grid-cols-1 gap-4">
            <mat-form-field class="w-full">
                <mat-label>URL du domaine (optionnel)</mat-label>
                <input formControlName="domain" matInput placeholder="https://mon-activepieces.com" type="url">
                <mat-hint>URL de votre serveur Activepieces</mat-hint>
            </mat-form-field>

            <mat-form-field class="w-full">
                <mat-label>Clé API (optionnel)</mat-label>
                <input formControlName="apiKey" matInput placeholder="ap_..." type="password">
                <mat-hint>Clé API pour l'authentification avec votre serveur</mat-hint>
            </mat-form-field>
        </div>
    </div>
</app-application-settings>
