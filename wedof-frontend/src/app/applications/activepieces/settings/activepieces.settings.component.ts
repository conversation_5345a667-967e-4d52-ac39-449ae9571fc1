import {Component, Injector} from '@angular/core';
import {Validators} from '@angular/forms';
import {Router} from '@angular/router';
import {HttpErrorResponse} from '@angular/common/http';
import {AbstractApplicationSettingsComponent} from '../../shared/settings/abstract-application-settings.component';
import {OrganismApplication} from '../../../shared/api/models/organism-application';
import {ActivepiecesMetadata} from '../activepieces.application';
import {SubmitSettingsButton} from '../../shared/settings/application-settings.component';

@Component({
    selector: 'activepieces-settings',
    templateUrl: './activepieces.settings.component.html',
    styleUrls: ['./activepieces.settings.component.scss']
})
export class ActivepiecesSettingsComponent extends AbstractApplicationSettingsComponent<ActivepiecesMetadata> {

    private static appId = 'activepieces';

    constructor(
        _router: Router,
        injector: Injector,
    ) {
        super(ActivepiecesSettingsComponent.appId, _router, injector);
    }

    onSuccess(organismApplication: OrganismApplication): void {
        this._router.navigate(['mes-applications']);
    }

    onError(httpErrorResponse: HttpErrorResponse): void {
    }

    getSubmitButton(): SubmitSettingsButton {
        return {
            color: 'primary',
            value: 'Sauvegarder',
            disabled: false
        };
    }

    protected initForm(metadata: ActivepiecesMetadata): void {
        this.formGroup = this._fb.group({
            name: [metadata?.name, [Validators.required]],
            domain: [metadata?.domain],
            apiKey: [metadata?.apiKey]
        });
    }
}
