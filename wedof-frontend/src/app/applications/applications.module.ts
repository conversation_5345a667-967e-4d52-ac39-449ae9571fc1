import {NgModule} from '@angular/core';
import {SharedModule} from '../shared/shared.module';
import {RouterModule} from '@angular/router';
import {SubscriptionModule} from '../shared/subscription/subscription.module';
import {ApplicationsListComponent} from '../private/application/applications-list.component';
import {MaterialModule} from '../shared/material/material.module';
import {CommonModule} from '@angular/common';
import {MatTooltipModule} from '@angular/material/tooltip';
import {MatProgressBarModule} from '@angular/material/progress-bar';
import {ApplicationSettingsComponent} from './shared/settings/application-settings.component';
import {ApplicationTemplatesComponent} from './shared/templates/application-templates.component';
import {ClipboardModule} from '@angular/cdk/clipboard';
import {WebhookResolver} from './webhooks/settings/webhook-form-page/webhook-resolver';
import {WebhookEventTypesResolver} from './webhooks/settings/webhook-form-page/webhook-events-type-resolver';
import {ZapierApplication} from './zapier/zapier.application';
import {WebhooksApplication} from './webhooks/webhooks.application';
import {SalesforceApplication} from './salesforce/salesforce.application';
import {SlackApplication} from './slack/slack.application';
import {IciFormationApplication} from './ici-formation/ici-formation.application';
import {HubspotApplication} from './hubspot/hubspot.application';
import {ZohoApplication} from './zoho/zoho.application';
import {OauthCodeManagementComponent} from './shared/oauth-management/oauth-code-management.component';
import {InfiniteScrollModule} from 'ngx-infinite-scroll';
import {RingoverApplication} from './ringover/ringover.application';
import {DendreoApplication} from './dendreo/dendreo.application';
import {DigiformaApplication} from './digiforma/digiforma.application';
import {MessageTemplateApplication} from './message-template/message-template.application';
import {MessageTemplateResolver} from './message-template/message-template-resolver.service';
import {StatusApplication} from './status/status.application';
import {MatCardModule} from '@angular/material/card';
import {MakeApplication} from './make/make.application';
import {ActivepiecesApplication} from './activepieces/activepieces.application';
import {DocumentApplication} from './document/document.application';
import {N8nApplication} from './n8n/n8n.application';
import {SignatureApplication} from './signature/signature.application';
import {OnlineformaproApplication} from './onlineformapro/onlineformapro.application';
import {WorkflowApplication} from './workflow/workflow.application';

@NgModule({
    declarations: [
        ApplicationsListComponent,
        ApplicationSettingsComponent,
        OauthCodeManagementComponent,
        ApplicationTemplatesComponent,
        // CAUTION! When adding app here
        // IT IS NOT ENOUGH!!!
        // For prod, you MUST add it too in application-settings.component.ts
        ...ActivepiecesApplication.components(),
        ...N8nApplication.components(),
        ...MakeApplication.components(),
        ...DendreoApplication.components(),
        ...SlackApplication.components(),
        ...ZapierApplication.components(),
        ...WebhooksApplication.components(),
        ...SalesforceApplication.components(),
        ...IciFormationApplication.components(),
        ...HubspotApplication.components(),
        ...RingoverApplication.components(),
        ...ZohoApplication.components(),
        ...MessageTemplateApplication.components(),
        ...DigiformaApplication.components(),
        ...StatusApplication.components(),
        ...DocumentApplication.components(),
        ...SignatureApplication.components(),
        ...OnlineformaproApplication.components(),
        ...WorkflowApplication.components()
        // CAUTION! read the message above!!
    ],
    exports: [
        ApplicationTemplatesComponent
    ],
    imports: [
        CommonModule,
        RouterModule,
        SharedModule,
        MaterialModule,
        MatCardModule,
        ClipboardModule,
        MatTooltipModule,
        SubscriptionModule,
        MatProgressBarModule,
        ClipboardModule,
        InfiniteScrollModule
    ],
    providers: [
        WebhookResolver,
        WebhookEventTypesResolver,
        MessageTemplateResolver
    ]
})
export class ApplicationsModule {
}
