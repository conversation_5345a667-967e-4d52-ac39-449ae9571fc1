import {Component, ElementRef, Input, OnChanges, OnInit, SimpleChanges} from '@angular/core';

@Component({
    selector: 'app-tag',
    templateUrl: './tag.component.html',
    styleUrls: ['./tag.component.scss']
})
export class TagComponent implements OnChanges, OnInit {

    @Input() color = 'primary';

    constructor(
        private _elementRef: ElementRef<HTMLElement>
    ) {
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes.color) {
            this.updateColor(changes.color.currentValue);
        }
    }

    ngOnInit(): void {
        // TODO check
        if (this.color) {
            this.updateColor(this.color);
        }
    }

    private updateColor(color: string): void {
        this._elementRef.nativeElement.classList.remove('primary', 'accent', 'warn');
        if (['primary', 'accent', 'warn'].includes(color)) {
            this._elementRef.nativeElement.classList.add(color);
        } else {
            this._elementRef.nativeElement.style.backgroundColor = color;
        }
    }
}
