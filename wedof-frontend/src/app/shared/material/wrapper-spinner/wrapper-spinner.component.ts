import {Component, ElementRef, Input} from '@angular/core';

@Component({
    selector: 'app-wrapper-spinner',
    templateUrl: './wrapper-spinner.component.html',
    styleUrls: ['./wrapper-spinner.component.scss']
})
export class WrapperSpinnerComponent {
    @Input() active: boolean;
    @Input() loadingClasses = '';

    constructor(private el: ElementRef) {
    }

    getHeight(): number {
        return this.el.nativeElement.offsetHeight;
    }
}
