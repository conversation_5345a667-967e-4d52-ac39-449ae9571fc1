import {CommonModule} from '@angular/common';
import {NgModule} from '@angular/core';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {MatMomentDateModule} from '@angular/material-moment-adapter';
import {MatAutocompleteModule} from '@angular/material/autocomplete';
import {MatBadgeModule} from '@angular/material/badge';
import {MatButtonModule} from '@angular/material/button';
import {MatCheckboxModule} from '@angular/material/checkbox';
import {MatDatepickerModule} from '@angular/material/datepicker';
import {MatDialogModule} from '@angular/material/dialog';
import {MatDividerModule} from '@angular/material/divider';
import {MatExpansionModule} from '@angular/material/expansion';
import {MatFormFieldModule} from '@angular/material/form-field';
import {MatIconModule} from '@angular/material/icon';
import {MatInputModule} from '@angular/material/input';
import {MatListModule} from '@angular/material/list';
import {MatMenuModule} from '@angular/material/menu';
import {MatPaginatorIntl, MatPaginatorModule} from '@angular/material/paginator';
import {MatProgressSpinnerModule} from '@angular/material/progress-spinner';
import {MatProgressBarModule} from '@angular/material/progress-bar';
import {MatRadioModule} from '@angular/material/radio';
import {MatSelectModule} from '@angular/material/select';
import {MatSidenavModule} from '@angular/material/sidenav';
import {MatSlideToggleModule} from '@angular/material/slide-toggle';
import {MatSnackBarModule} from '@angular/material/snack-bar';
import {MatSortModule} from '@angular/material/sort';
import {MatStepperModule} from '@angular/material/stepper';
import {MatTableModule} from '@angular/material/table';
import {MatToolbarModule} from '@angular/material/toolbar';
import {TranslateModule, TranslateService} from '@ngx-translate/core';
import {TreoCardModule} from '@treo/components/card';
import {TreoMessageModule} from '@treo/components/message';
import {MarkdownModule} from 'ngx-markdown';
import {PaginatorI18n} from '../i18n/paginator-i18n';
import {DeletionConfirmationComponent} from './action-confirmation/deletion-confirmation.component';
import {DialogLayoutComponent} from './dialog-layout/dialog-layout.component';
import {PaginatorComponent} from './table/paginator/paginator.component';
import {TableFilterComponent} from './table/table-filter/table-filter.component';
import {TableMultipleFilterComponent} from './table/table-multiple-filter/table-multiple-filter.component';
import {TagComponent} from './tag/tag.component';
import {WrapperSpinnerComponent} from './wrapper-spinner/wrapper-spinner.component';
import {StatusComponent} from './status/status.component';
import {PrecisionGaugeComponent} from '../certification/certification-audit-template/precision-gauge/precision-gauge.component';
import {AppFormFieldComponent} from './app-form-field/app-form-field.component';
import {PipesModule} from '../pipes/pipes.module';
import {MAT_DATE_FORMATS, MatDateFormats} from '@angular/material/core';
import {ClipboardModule} from 'ngx-clipboard';
import {MatTooltipModule} from '@angular/material/tooltip';
import {AppFormFieldStaticComponent} from './app-form-field-static/app-form-field-static.component';
import {AppFormFieldsComponent} from './app-form-fields/app-form-fields.component';
import {MatChipsModule} from '@angular/material/chips';
import {AppTagInputComponent} from './app-tag-input/app-tag-input.component';
import {NgxMatSelectSearchModule} from 'ngx-mat-select-search';
import {SnackBarComponent} from './snack-bar/snack-bar.component';
import {InfiniteScrollComponent} from './infinite-scroll/infinite-scroll.component';
import {MatSelectInfiniteScrollModule} from 'ng-mat-select-infinite-scroll';
import {
    NgxMatDatetimePickerModule,
    NgxMatNativeDateModule,
    NgxMatTimepickerModule
} from '@angular-material-components/datetime-picker';
import {NgxMatMomentModule} from '@angular-material-components/moment-adapter';
import {ActionConfirmationComponent} from './action-confirmation/action-confirmation.component';
import {ShortcutComponent} from './shortcut/shortcut.component';
import {TemplateEditorComponent} from './template-editor/template-editor.component';
import {PickerComponent} from './picker/picker.component';
import {MatCardModule} from '@angular/material/card';
import {DisclaimerComponent} from './disclaimer/disclaimer.component';

export const MAT_CUSTOM_DATE_FORMAT: MatDateFormats = {
    parse: {
        dateInput: 'L', // Must be the same as display.dateInput - by default was l so leading zeros were not shown
    },
    display: {
        dateInput: 'L', // Must be the same as parse.dateInput - by default was l so leading zeros were not shown
        monthYearLabel: 'MMM YYYY',
        dateA11yLabel: 'LL',
        monthYearA11yLabel: 'MMMM YYYY',
    },
};

const materialModules = [
    MatAutocompleteModule,
    MatBadgeModule,
    MatButtonModule,
    MatCheckboxModule,
    MatDatepickerModule,
    NgxMatDatetimePickerModule,
    NgxMatTimepickerModule,
    NgxMatMomentModule,
    MatDialogModule,
    MatDividerModule,
    MatExpansionModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatListModule,
    MatMenuModule,
    MatMomentDateModule,
    MatPaginatorModule,
    MatProgressBarModule,
    MatProgressSpinnerModule,
    MatRadioModule,
    MatSelectModule,
    MatSidenavModule,
    MatSlideToggleModule,
    MatSnackBarModule,
    MatSortModule,
    MatStepperModule,
    MatTableModule,
    MatToolbarModule,
    TreoCardModule,
    TreoMessageModule,
    NgxMatSelectSearchModule
];

@NgModule({
    declarations: [
        ActionConfirmationComponent,
        DeletionConfirmationComponent,
        AppFormFieldComponent,
        AppFormFieldStaticComponent,
        AppFormFieldsComponent,
        AppTagInputComponent,
        DialogLayoutComponent,
        PaginatorComponent,
        TableFilterComponent,
        TableMultipleFilterComponent,
        TagComponent,
        StatusComponent,
        PrecisionGaugeComponent,
        WrapperSpinnerComponent,
        SnackBarComponent,
        InfiniteScrollComponent,
        ShortcutComponent,
        TemplateEditorComponent,
        PickerComponent,
        DisclaimerComponent
    ],

    imports: [
        CommonModule,
        FormsModule,
        MarkdownModule,
        TranslateModule,
        ...materialModules,
        ReactiveFormsModule,
        PipesModule,
        ClipboardModule,
        MatTooltipModule,
        MatChipsModule,
        MatSelectInfiniteScrollModule,
        NgxMatDatetimePickerModule,
        NgxMatNativeDateModule,
        MatCardModule
    ],
    exports: [
        ...materialModules,
        ActionConfirmationComponent,
        DeletionConfirmationComponent,
        AppFormFieldComponent,
        AppFormFieldStaticComponent,
        AppFormFieldsComponent,
        AppTagInputComponent,
        DialogLayoutComponent,
        PaginatorComponent,
        TableFilterComponent,
        TableMultipleFilterComponent,
        TagComponent,
        WrapperSpinnerComponent,
        SnackBarComponent,
        StatusComponent,
        PrecisionGaugeComponent,
        InfiniteScrollComponent,
        ShortcutComponent,
        TemplateEditorComponent,
        DisclaimerComponent
    ],
    providers: [
        {
            provide: MatPaginatorIntl,
            deps: [TranslateService],
            useFactory: (translateService: TranslateService) => new PaginatorI18n(translateService).getPaginatorIntl()
        },
        {provide: MAT_DATE_FORMATS, useValue: MAT_CUSTOM_DATE_FORMAT},
    ]
})
export class MaterialModule {
}
