import { Directive } from '@angular/core';
import { Subject } from 'rxjs';
import { AbstractDialogFormComponent } from './abstract-dialog-form.component';

export interface DialogStepSubmission<T> {
    step: number;
    data: T;
}

@Directive()
// tslint:disable-next-line: directive-class-suffix
export abstract class AbstractDialogStepFormComponent<T> extends AbstractDialogFormComponent<T, DialogStepSubmission<T>> {
    submitted$: Subject<DialogStepSubmission<T>>;

    abstract next(): void;

    abstract submit(): void;

    protected abstract initForm(model?: T): void;

}
