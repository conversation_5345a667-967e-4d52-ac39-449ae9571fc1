import {Directive, Injector} from '@angular/core';
import {MatDialogRef} from '@angular/material/dialog';
import {Subject} from 'rxjs';
import {AbstractFormComponent} from './abstract-form.component';

@Directive()
// tslint:disable-next-line: directive-class-suffix
export abstract class AbstractDialogFormComponent<T, S = T> extends AbstractFormComponent<T> {
    submitted$: Subject<S>;
    errorMessages: string[] = [];
    warningMessage: string;
    feedbackMessage: string;
    allProcessed = false;

    protected _dialogRef: MatDialogRef<AbstractDialogFormComponent<T>>;

    constructor(injector: Injector) {
        super(injector);
        this._dialogRef = injector.get(MatDialogRef);
        this.submitted$ = new Subject();
        this.warningMessage = '';
        this.feedbackMessage = '';
    }

    close(): void {
        this.submitted$.complete();
        this._dialogRef.close();
    }

    lastProcessedData(processedData: any): void {
    }

    abstract submit(): void;

    protected abstract initForm(model?: T): void;

}
