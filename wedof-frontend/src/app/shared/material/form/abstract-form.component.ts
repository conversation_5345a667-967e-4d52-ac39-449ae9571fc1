import { Directive, Injector } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';

@Directive()
// tslint:disable-next-line: directive-class-suffix
export abstract class AbstractFormComponent<T> {
    formGroup: FormGroup;
    loading = false;

    protected _fb: FormBuilder;

    constructor(injector: Injector) {
        this._fb = injector.get(FormBuilder);
        this.formGroup = this._fb.group([]);
    }

    abstract submit(): void;

    protected abstract initForm(model?: T): void;

}
