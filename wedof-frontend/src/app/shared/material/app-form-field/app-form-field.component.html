<!--suppress XmlDuplicatedId -->
<ng-container
    *ngIf="type !== 'tags' && type !== 'radio' && type !== 'fieldset' && type !== 'checkbox' && type !== 'infiniteSearch' && type !== 'file' && type !== 'action' && type !== 'image' && type !== 'templateEditor'">
    <mat-form-field [floatLabel]="floatLabel"
                    *ngIf="!formGroup.get(formGroupName).get(controlName).disabled else readonly"
                    [formGroup]="formGroup.get(formGroupName)" id="form-field-wrapper-{{formGroupName}}-{{controlName}}"
                    class="w-full">
        <mat-label *ngIf="label && !hideLabel">{{ label | translate }}</mat-label>
        <ng-container *ngIf="type === 'date'">
            <input formControlName="{{controlName}}" matInput
                   id="form-field-{{formGroupName}}-{{controlName}}"
                   [required]="required"
                   [max]="max"
                   [min]="min"
                   (click)="datePicker.open()"
                   (dateChange)="changeAndUpdatedFormFields($event.value, $event)"
                   [placeholder]="placeholder | translate"
                   [matDatepicker]="datePicker">
            <mat-datepicker-toggle matSuffix [for]="datePicker"></mat-datepicker-toggle>
            <mat-datepicker #datePicker></mat-datepicker>
        </ng-container>
        <ng-container *ngIf="type === 'datetime-local'">
            <input formControlName="{{controlName}}" matInput
                   id="form-field-{{formGroupName}}-{{controlName}}"
                   [ngxMatDatetimePicker]="picker"
                   [required]="required"
                   [max]="max"
                   [min]="min"
                   readonly="readonly"
                   (click)="picker.open()"
                   (dateInput)="formatDateWithoutSeconds($event)"
                   (dateChange)="changeAndUpdatedFormFields($event.value, $event)"
                   [placeholder]="placeholder | translate">
            <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
            <ngx-mat-datetime-picker #picker [enableMeridian]="false"></ngx-mat-datetime-picker>
        </ng-container>
        <ng-container *ngIf="type === 'magicText'">
            <input formControlName="{{controlName}}"
                   matInput
                   (change)="changeAndUpdatedFormFields($event.target.value, $event)"
                   [required]="required"
                   [autocomplete]="'off'"
                   [placeholder]="placeholder | translate"
                   id="form-field-{{formGroupName}}-{{controlName}}"
                   (keyup)="keyup($event)"
                   (paste)="pasteMethod($event)"
                   type="text"
                   [minlength]="minLength"
                   [maxlength]="maxLength"/>
        </ng-container>
        <ng-container
            *ngIf="type === 'text' || type === 'url' || type === 'email' || type === 'text-html' || type === 'password'">
            <span *ngIf="prefix" class="text-secondary mr-3" matPrefix>{{prefix}}</span>
            <input formControlName="{{controlName}}" matInput
                   [required]="required"
                   [autocomplete]="'off'"
                   (change)="changeAndUpdatedFormFields($event.target.value, $event)"
                   [placeholder]="placeholder | translate"
                   [maxlength]="maxLength ? maxLength : 255"
                   [type]="type"/>
            <mat-icon *ngIf="type !== 'url' && icon" [svgIcon]="icon" [ngClass]="iconClass"
                      [matTooltip]="iconTooltip | translate" matSuffix></mat-icon>
            <a *ngIf="type === 'url' && !icon && formGroup.get(formGroupName).get(controlName).value"
               href="{{formGroup.get(formGroupName).get(controlName).value}}"
               target="_blank">
                <mat-icon class="ml-4" [svgIcon]="'open_in_new'" matSuffix></mat-icon>
            </a>
            <a *ngIf="type === 'url' && icon && formGroup.get(formGroupName).get(controlName).value"
               href="{{formGroup.get(formGroupName).get(controlName).value}}"
               target="_blank">
                <mat-icon class="ml-4" [svgIcon]="icon" [ngClass]="iconClass" matSuffix></mat-icon>
            </a>
            <a *ngIf="type === 'email' && folderEntityContext"
               href="mailto:{{formGroup.get(formGroupName).get(controlName).value}}"
               (click)="addActivity(folderEntityContext,'email', 'Nouvel email')"
               target="_blank">
                <mat-icon class="ml-4" [svgIcon]="'email'" matSuffix></mat-icon>
            </a>
        </ng-container>
        <ng-container *ngIf="type === 'color'">
            <input formControlName="{{controlName}}" matInput
                   [required]="required"
                   (change)="changeAndUpdatedFormFields($event.target.value, $event)"
                   [placeholder]="placeholder | translate"
                   type="color" class="p-0"/>
        </ng-container>
        <ng-container *ngIf="type === 'tel'">
            <input formControlName="{{controlName}}" matInput
                   [required]="required"
                   (change)="changeAndUpdatedFormFields($event.target.value, $event)"
                   [placeholder]="placeholder | translate"
                   [type]="type"/>
            <a href="tel:{{formGroup.get(formGroupName).get(controlName).value}}"
               (click)="addActivity(folderEntityContext,'phone', 'Nouvel appel')">
                <mat-icon class="ml-4" [svgIcon]="'phone'" matSuffix></mat-icon>
            </a>
        </ng-container>
        <ng-container *ngIf="type === 'textarea'">
            <textarea [rows]="rows ? rows : 8" formControlName="{{controlName}}"
                      [required]="required"
                      [maxlength]="maxLength ? maxLength : 1000"
                      [placeholder]="placeholder | translate"
                      (change)="changeAndUpdatedFormFields($event.target.value, $event)"
                      matInput></textarea>
            <mat-icon *ngIf="icon" [svgIcon]="icon" [ngClass]="iconClass" matSuffix></mat-icon>
        </ng-container>
        <ng-container *ngIf="type === 'percent'">
            <input formControlName="{{controlName}}" matInput
                   [required]="required"
                   (change)="changeAndUpdatedFormFields($event.target.valueAsNumber, $event)"
                   [placeholder]="placeholder | translate"
                   type="number"/>
            <div *ngIf="suffix" matSuffix>{{ suffix | translate }}</div>
        </ng-container>
        <ng-container *ngIf="type === 'number'">
            <input formControlName="{{controlName}}" matInput
                   [required]="required"
                   [min]="min"
                   (change)="changeAndUpdatedFormFields($event.target.valueAsNumber, $event)"
                   [placeholder]="placeholder | translate"
                   type="number"/>
            <mat-icon *ngIf="icon" [svgIcon]="icon" [ngClass]="iconClass" matSuffix></mat-icon>
            <div *ngIf="suffix" matSuffix>{{ suffix | translate }}</div>
        </ng-container>
        <ng-container *ngIf="type === 'hours'">
            <input formControlName="{{controlName}}" matInput
                   id="form-field-{{formGroupName}}-{{controlName}}"
                   [required]="required"
                   (change)="changeAndUpdatedFormFields($event.target.valueAsNumber, $event)"
                   [placeholder]="placeholder | translate"
                   [min]="min"
                   [max]="max"
                   type="number"/>
            <div matSuffix>Heures</div>
        </ng-container>
        <ng-container *ngIf="type === 'price'">
            <input formControlName="{{controlName}}" matInput
                   (change)="changeAndUpdatedFormFields($event.target.valueAsNumber, $event)"
                   [required]="required"
                   step="any"
                   [max]="max"
                   [min]="min"
                   [placeholder]="placeholder | translate"
                   type="number"/>
            <mat-icon *ngIf="icon" [svgIcon]="icon" [ngClass]="iconClass" matSuffix></mat-icon>
            <div *ngIf="suffix" matSuffix>{{ suffix | translate }}</div>
        </ng-container>
        <ng-container *ngIf="type === 'select'">
            <mat-select formControlName="{{controlName}}" aria-labelledby="{{label}}" [multiple]="searchMultiple"
                        [panelClass]="'mat-select-panel-' + formGroupName + ' ' + 'mat-select-panel-' + controlName"
                        [placeholder]="placeholder | translate"
                        [required]="required"
                        (selectionChange)="changeAndUpdatedFormFields($event.value, $event)">
                <mat-form-field *ngIf="placeholderSearchInList && listChoices" class="w-full">
                    <input matInput class="px-2"
                           [placeholder]="placeholderSearchInList | translate"
                           (keyup)="searchInListChoices($event.target)"/>
                </mat-form-field>
                <ng-container *ngIf="choices">
                    <mat-option *ngFor="let choiceOption of choices "
                                [matTooltipPosition]="'above'"
                                [matTooltip]="choiceOption.tooltip | translate"
                                [disabled]="choiceOption.disabled && choiceOption.disabled === true"
                                [value]="choiceOption.value">
                        <mat-icon *ngIf="choiceOption.icon"
                                  [color]="choiceOption.color"
                                  [class]="choiceOption.iconClass"
                                  [svgIcon]="choiceOption.icon"></mat-icon>
                        {{ choiceOption.key }}
                    </mat-option>
                </ng-container>
                <ng-container *ngIf="listChoices">
                    <mat-optgroup *ngFor="let listChoiceOption of listChoices"
                                  [label]="listChoiceOption.name | translate">
                        <mat-option *ngFor="let choiceOption of listChoiceOption.value" [value]="choiceOption.value">
                            <mat-icon *ngIf="choiceOption.icon" [color]="choiceOption.color"
                                      [svgIcon]="choiceOption.icon"></mat-icon>
                            {{ choiceOption.key }}
                            <mat-icon *ngIf="choiceOption.tooltip"
                                      [matTooltipPosition]="'above'"
                                      [matTooltip]="choiceOption.tooltip | translate"
                                      [ngClass]="helpIconClass"
                                      [svgIcon]="'help_outline'"
                                      matSuffix></mat-icon>
                        </mat-option>
                    </mat-optgroup>
                </ng-container>
            </mat-select>
        </ng-container>
        <!-- aria-labelledby required to avoid error ExpressionChangedAfterItHasBeenCheckedError: Expression has changed after it was checked. -->
        <ng-container *ngIf="type === 'search'">
            <mat-select formControlName="{{controlName}}"
                        id="{{controlName}}"
                        [panelClass]="'mat-select-panel-' + formGroupName + ' ' + 'mat-select-panel-' + controlName"
                        [required]="required"
                        (selectionChange)="changeAndUpdatedFormFields($event.value, $event)"
                        [compareWith]="compareSearchResult"
                        aria-labelledby="{{label}}"
                        (opened)="initSearch()"
                        [placeholder]="placeholder | translate"
                        [multiple]="searchMultiple">
                <mat-option>
                    <ngx-mat-select-search [formControl]="formControlFilteringCtrl"
                                           [noEntriesFoundLabel]="searchNoEntriesFoundLabel | translate"
                                           [placeholderLabel]="placeholder | translate"
                                           [clearSearchInput]="false"
                                           [searching]="searching">
                    </ngx-mat-select-search>
                </mat-option>
                <mat-option *ngIf="showCreateButton && !searching && searchingValue.length > 3">
                    <button (click)="openDialogCreate(searchingValue)" type="button"
                            class="min-w-full">
                        {{ showCreateText | translate }} {{ searchingValue }}
                    </button>
                </mat-option>
                <mat-option *ngIf="showCreateButton && searching" disabled="disabled">
                    {{ showSearchingText | translate }} {{ searchingValue }}
                </mat-option>
                <mat-option
                    *ngFor="let searchResult of searchResults | async"
                    [value]="searchResult">
                    {{ searchResultFormatter ? searchResultFormatter(searchResult) : searchResult }}
                </mat-option>
            </mat-select>
        </ng-container>
        <button type="button"
                class="ml-1"
                (click)="useConfirmDeleteDialog ? removeValueWithConfirm($event) : removeValue($event); $event.preventDefault(); $event.stopPropagation();"
                *ngIf="removable && hasValue()"
                aria-label="Supprimer la valeur sélectionnée"
                mat-button mat-icon-button matSuffix>
            <mat-icon *ngIf="!iconRemovable && !iconClassRemovable" svgIcon="close"></mat-icon>
            <mat-icon *ngIf="iconRemovable" [class]="iconClassRemovable" [svgIcon]="iconRemovable"></mat-icon>
        </button>
        <mat-icon
            *ngIf="help"
            [matTooltipPosition]="'above'"
            [matTooltip]="help | translate"
            [ngClass]="helpIconClass"
            matSuffix>{{ helpIcon ? helpIcon : "help_outline" }}
        </mat-icon>
    </mat-form-field>
    <!-- [id]="''" => https://github.com/angular/components/issues/16209 -->
    <mat-error class="matErrorFormField pb-1" [id]="''" *ngIf="!formGroup.get(formGroupName).get(controlName).disabled">
        <ng-container *ngFor="let validatorMessage of validatorsMessages | keyvalue">
            <ng-container
                *ngIf="formGroup.get(formGroupName).get(controlName)?.errors && formGroup.get(formGroupName).get(controlName).errors[validatorMessage.key]">
                {{ validatorMessage.value.toString() | translate }}
            </ng-container>
            <ng-container
                *ngIf="(type === 'date' || type === 'datetime-local') && formGroup.get(formGroupName).get(controlName)?.errors && (formGroup.get(formGroupName).get(controlName).errors[keyForDates(validatorMessage.key)]
                || formGroup.get(formGroupName).get(controlName).errors[keyForDateTime(validatorMessage.key)])">
                {{ validatorMessage.value.toString() | translate }}
            </ng-container>
        </ng-container>
        <ng-container *ngIf="required && (!validatorsMessages || !validatorsMessages['required'])">
            <ng-container
                *ngIf="formGroup.get(formGroupName).get(controlName)?.errors && formGroup.get(formGroupName).get(controlName).errors.required">
                {{ 'common.errors.required' | translate }}
            </ng-container>
        </ng-container>
        <ng-container *ngIf="error">
            {{ error | translate }}
        </ng-container>
    </mat-error>
</ng-container>

<ng-container *ngIf="type === 'radio'">
    <div [formGroup]="formGroup.get(formGroupName)" class="mb-4">
        <a *ngIf="href else withoutHref" href="{{href}}" class="flex flex-row mb-1 no-underline">
            <mat-icon *ngIf="icon" [svgIcon]="icon" class="mr-1 text-green" [ngClass]="iconClass" matPrefix></mat-icon>
            <mat-label class="self-center font-medium underline" *ngIf="label && !hideLabel">
                {{ label | translate }}
            </mat-label>
            <mat-icon
                class="ml-1"
                *ngIf="help"
                [ngClass]="helpIconClass"
                [matTooltipPosition]="'above'"
                [matTooltip]="help | translate"
                matSuffix>{{ helpIcon ? helpIcon : "help_outline" }}
            </mat-icon>
        </a>
        <ng-template #withoutHref>
            <div class="flex flex-row mb-1">
                <mat-icon *ngIf="icon" [svgIcon]="icon" class="mr-1 text-green" [ngClass]="iconClass"
                          matPrefix></mat-icon>
                <mat-label class="self-center font-medium" *ngIf="label && !hideLabel">
                    {{ label | translate }}
                </mat-label>
                <mat-icon
                    class="ml-1"
                    *ngIf="help"
                    [ngClass]="helpIconClass"
                    [matTooltipPosition]="'above'"
                    [matTooltip]="help | translate"
                    matSuffix>{{ helpIcon ? helpIcon : "help_outline" }}
                </mat-icon>
            </div>
        </ng-template>
        <mat-radio-group [required]="required" class="flex" [ngClass]="!inline ? 'flex-col w-1/4' : 'mt-3'"
                         formControlName="{{controlName}}"
                         id="form-field-wrapper-{{formGroupName}}-{{controlName}}"
                         (change)="changeAndUpdatedFormFields($event.value, $event)">
            <mat-radio-button class="mat-radio-button {{inline && !first ? 'ml-2 ' : ''}}"
                              *ngFor="let choiceOption of choices; let first = first;"
                              [checked]="formGroup.get(formGroupName).get(controlName).value === choiceOption.value"
                              [value]="choiceOption.value">
                {{ choiceOption.key }}
            </mat-radio-button>
        </mat-radio-group>
        <mat-error
            class="matErrorFormField pb-1 {{formGroup.get(formGroupName).get(controlName)?.errors && formGroup.get(formGroupName).get(controlName).errors.required ? 'mt-1' : ''}}"
            [id]="''"
            *ngIf="!formGroup.get(formGroupName).get(controlName).disabled">
            <ng-container
                *ngIf="formGroup.get(formGroupName).get(controlName)?.errors && formGroup.get(formGroupName).get(controlName).errors.required">
                {{ 'common.errors.required' | translate }}
            </ng-container>
        </mat-error>
    </div>
</ng-container>

<ng-container *ngIf="type === 'fieldset'">
    <div [formGroup]="formGroup.get(formGroupName)">
        <fieldset class="p-2 border-2 border-green-500">
            <legend class="px-1 self-center font-medium text-green"
                    *ngIf="label && !hideLabel">{{ label | translate }}
            </legend>
            <div class="flex items-center mb-2">
                <mat-icon *ngIf="icon" class="mr-2 text-green" [svgIcon]="icon" [ngClass]="iconClass"
                          matPrefix></mat-icon>
                <mat-radio-group [required]="required" class="flex w-1/4" formControlName="{{controlName}}"
                                 id="form-field-wrapper-{{formGroupName}}-{{controlName}}"
                                 (change)="changeAndUpdatedFormFields($event.value, $event)">
                    <mat-radio-button class="mat-radio-button mr-2" *ngFor="let choiceOption of choices"
                                      [checked]="formGroup.get(formGroupName).get(controlName).value === choiceOption.value"
                                      [value]="choiceOption.value">
                        {{ choiceOption.key }}
                    </mat-radio-button>
                </mat-radio-group>
            </div>
        </fieldset>
    </div>
</ng-container>

<ng-container *ngIf="type === 'checkbox'">
    <div [formGroup]="formGroup.get(formGroupName)">
        <mat-checkbox (change)="changeAndUpdatedFormFields($event.checked, $event)"
                      formControlName="{{controlName}}">
            <ng-container *ngIf="href; else showText">
                <a class="link font-medium" target="_blank" [href]="href"> <span [innerHTML]="label | translate"></span></a>
            </ng-container>
            <ng-template #showText>
                <span [innerHTML]="label | translate"></span><!-- for html text content -->
            </ng-template>
        </mat-checkbox>
    </div>
</ng-container>

<ng-container *ngIf="type === 'file'">
    <div class="flex gt-xs:flex-row xs:flex-col mb-4" id="form-field-wrapper-{{formGroupName}}-{{controlName}}"
         *ngIf="!formGroup.get(formGroupName).get(controlName).disabled else readonly">
        <div [formGroup]="formGroup.get(formGroupName)">
            <div class="mb-1">
                <label class="text-base font-medium" for="form-field-file-{{controlName}}">
                    {{ label | translate }}
                </label>
            </div>
            <div class="flex items-center justify-between">
                <button type="button" mat-flat-button
                        class="bg-gray-200"
                        (click)="upload.click()">
                    <mat-icon
                        *ngIf="formGroup.get(formGroupName).get(controlName).dirty && formGroup.get(formGroupName).get(controlName).value"
                        [svgIcon]="'check_circle_outline'"
                        class="mr-3"></mat-icon>
                    <mat-icon
                        *ngIf="icon && !(formGroup.get(formGroupName).get(controlName).dirty && formGroup.get(formGroupName).get(controlName).value)"
                        [svgIcon]="icon" [ngClass]="iconClass"
                        class="mr-3"></mat-icon>
                    {{ formGroup.get(formGroupName).get(controlName).value?.name ? formGroup.get(formGroupName).get(controlName).value.name : (chooseLabel | translate) }}
                </button>
                <button type="button"
                        mat-icon-button
                        class="align-top ml-1"
                        *ngIf="formGroup.get(formGroupName).get(controlName).value && removable"
                        (click)="removeValue($event)"
                        title="{{ removeLabel | translate }}" matSuffix>
                    <mat-icon svgIcon="mat_outline:delete"></mat-icon>
                </button>
                <treo-message *ngIf="secondaryText" class="ml-1"
                              [ngClass]="formGroup.get(formGroupName).get(controlName).value ? 'max-w-3/4' : ''"
                              type="info" [showIcon]="false" appearance="outline">
                    <a *ngIf="href; else showAction" target="_blank" href="{{href}}">{{ secondaryText | translate }}</a>
                    <ng-template #showAction>
                        <button mat-button class="whitespace-pre-wrap" type="button" (click)="actionMethod()">
                            <mat-icon *ngIf="helpIcon" [svgIcon]="helpIcon" class="pr-1"></mat-icon>
                            <p class="underline">{{ secondaryText | translate }}</p></button>
                    </ng-template>
                </treo-message>
            </div>
            <input #upload (change)="updateFileToFormGroup($event, formGroup.get(formGroupName), controlName)"
                   type="file"
                   accept="{{ fileTypesAccept ? fileTypesAccept.join(', ') : 'application/pdf'}}" style="display:none"
                   name="{{controlName}}"
                   id="form-field-file-{{controlName}}"/>
        </div>
        <div class="flex justify-center items-center flex-col xs:mt-8 ml-2" *ngIf="showFilePreview else viewAsDownload">
            <div *ngIf="!!actionMethod; else noActionMethod">
                <img
                    *ngIf="thumbnailFile ? thumbnailFile : (thumbnailFile !== false && formGroup.get(formGroupName).get(controlName).value)"
                    [src]="displayFilePreview(thumbnailFile ? thumbnailFile : formGroup.get(formGroupName).get(controlName).value)"
                    (click)="actionMethod()"
                    class="ml-4 w-40 cursor-pointer">
            </div>
            <ng-template #noActionMethod>
                <img
                    *ngIf="thumbnailFile ? thumbnailFile : (thumbnailFile !== false && formGroup.get(formGroupName).get(controlName).value)"
                    [src]="displayFilePreview(thumbnailFile ? thumbnailFile : formGroup.get(formGroupName).get(controlName).value)"
                    class="ml-4 w-40 cursor-pointer">
            </ng-template>
        </div>
        <ng-template #viewAsDownload>
            <div class="flex justify-center items-center flex-col xs:mt-8 ml-2">
                <a href="" (click)="downloadFile(formGroup.get(formGroupName).get(controlName).value)"
                   target="_blank">{{ viewLabel }}</a>
            </div>
        </ng-template>
    </div>
    <mat-error class="matErrorFormField pb-1" [id]="''" *ngIf="!formGroup.get(formGroupName).get(controlName).disabled">
        <ng-container *ngIf="required && (!validatorsMessages || !validatorsMessages['required'])">
            <ng-container
                *ngIf="formGroup.get(formGroupName).get(controlName)?.errors && formGroup.get(formGroupName).get(controlName).errors.required">
                {{ 'common.errors.required' | translate }}
            </ng-container>
        </ng-container>
    </mat-error>
</ng-container>

<ng-container *ngIf="type === 'action'">
    <div class="flex flex-col" [class]="hideLabel ? 'mt-6' : ''">
        <mat-label class="font-medium" *ngIf="label && !hideLabel">{{ label | translate }}</mat-label>
        <button class="w-3/4" *ngIf="!formGroup.get(formGroupName).get(controlName).disabled; else showDisableText"
                mat-flat-button type="button"
                color="primary" (click)="actionMethod()">
            {{ actionText | translate }}
        </button>
        <ng-template #showDisableText>
            <p>{{ actionText | translate }}</p>
        </ng-template>
    </div>
</ng-container>

<ng-container *ngIf="type === 'templateEditor'">
    <app-template-editor *ngIf="!formGroup.get(formGroupName).get(controlName).disabled else readonly"
                         [label]="label | translate"
                         [required]="required"
                         [placeholder]="placeholder"
                         [maxLength]="maxLength"
                         [hideLabel]="hideLabel"
                         [formGroup]="formGroup.get(formGroupName)"
                         [controlName]="controlName"
                         [type]="templateEditorOptions.type"
                         [scope]="templateEditorOptions.scope"
                         [contextId]="templateEditorOptions.contextId"
    >
    </app-template-editor>
</ng-container>

<ng-container *ngIf="type === 'tags'">
    <app-tag-input *ngIf="!formGroup.get(formGroupName).get(controlName).disabled else readonly"
                   [label]="label"
                   [hideLabel]="hideLabel"
                   [creatingAvailable]="isCreateAvailable"
                   [tooltip]="help | translate"
                   [placeholder]="placeholder"
                   [control]="formGroup.get(formGroupName).get(controlName)">
    </app-tag-input>
</ng-container>

<ng-container *ngIf="type === 'infiniteSearch'">
    <app-infinite-scroll *ngIf="!formGroup.get(formGroupName).get(controlName).disabled else readonly"
                         id="form-field-wrapper-{{formGroupName}}-{{controlName}}"
                         [controlName]="controlName"
                         [noEntriesFoundLabel]="searchNoEntriesFoundLabel | translate"
                         [placeholderLabel]="placeholder | translate"
                         [placeholder]="placeholder | translate"
                         [label]="label | translate "
                         [tooltip]="help | translate"
                         [formGroup]="formGroup.get(formGroupName)"
                         [parameters]="parameters"
                         [resultFormatter]="searchResultFormatter"
                         [searchMethod]="searchMethodPaginated"
                         [multiple]="searchMultiple"
                         [required]="required"
                         [searchAdvanced]="searchAdvanced"
                         [searchComparisonProperty]="searchComparisonProperty"
                         (changeSelection)="changeAndUpdatedFormFields(formGroup.get(formGroupName).get(controlName).value, $event)"
    ></app-infinite-scroll>
</ng-container>

<ng-container *ngIf="type ==='image'">
    <mat-label class="font-medium" *ngIf="label && !hideLabel">{{ label | translate }}</mat-label>
        <a *ngIf="formGroup.get(formGroupName).get(controlName).value"
           target="_blank" class="mt-1"
           [href]="href">
            <img class="mt-2"
                 [src]="formGroup.get(formGroupName).get(controlName).value"
                 [alt]="formGroup.get(formGroupName).get(controlName).value"
                 style="width: 50%; margin:auto">
        </a>
</ng-container>

<ng-template #readonly>
    <app-form-field-static [placeholder]="placeholder"
                           [type]="type"
                           [copy]="copy"
                           [copyValue]="copyValue"
                           [icon]="icon"
                           [iconClass]="iconClass"
                           [iconColor]="iconColor"
                           [label]="label"
                           [hideLabel]="hideLabel"
                           [href]="href"
                           [suffix]="suffix"
                           [help]="help"
                           [searchResultFormatter]="searchResultFormatter"
                           [searchMultiple]="searchMultiple"
                           [value]="getStaticValue()"
                           [folderEntityContext]="folderEntityContext"
                           [viewLabel]="viewLabel"
                           [navigateCommands]="navigateCommands"
                           [navigateQueryParams]="navigateQueryParams"
                           [timezone]="timezone"
                           [iconTooltip]="iconTooltip">
    </app-form-field-static>
</ng-template>
