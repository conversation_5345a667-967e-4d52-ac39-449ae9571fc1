import {AfterViewInit, Component, EventEmitter, Inject, Input, OnD<PERSON>roy, OnInit, Output} from '@angular/core';
import {FormControl, FormGroup, ValidatorFn} from '@angular/forms';
import {Observable, ReplaySubject, Subject} from 'rxjs';
import _, {isEqual} from 'lodash';
import {debounceTime, filter, finalize, first, map, switchMap, takeUntil, tap} from 'rxjs/operators';
import {PaginatedResponse} from '../../api/services/abstract-paginated.service';
import {MatDatetimePickerInputEvent} from '@angular-material-components/datetime-picker';
import {EntityContext} from '../../api/models/entity-context';
import {ActivityFormComponent} from '../../activities/activity-form/activity-form.component';
import {MatDialog} from '@angular/material/dialog';
import {DomSanitizer, SafeUrl} from '@angular/platform-browser';
import {DOCUMENT} from '@angular/common';
import {FileService} from '../../api/services/file.service';
import {AuthService} from '../../../core/auth/auth.service';
import {flatten} from 'lodash-es';
import {ThemePalette} from '@angular/material/core';
import {TemplateEditorOptions} from '../template-editor/template-editor.component';
import {DeletionConfirmationComponent} from '../action-confirmation/deletion-confirmation.component';

export interface AppFormFieldData {
    // Used above the component
    disabled?: boolean;
    removed?: boolean;
    value?: any;
    colSpan?: number;
    class?: string;
    // Used inside the component
    type: 'radio' | 'fieldset' | 'text' | 'text-html' | 'textarea' | 'date' | 'datetime-local' | 'url' | 'email' | 'tel' | 'number' | 'tags' | 'image' | 'magicText' |
        'percent' | 'price' | 'hours' | 'select' | 'search' | 'checkbox' | 'infiniteSearch' | 'file' | 'color' | 'password' | 'action' | 'templateEditor' | 'url-html';
    controlName: string;
    icon?: string;
    label?: string;
    // Always show the placeholder if set to 'always'. src: https://material.angular.io/components/form-field/overview#floating-label
    floatLabel?: 'auto' | 'always' | 'never';
    viewLabel?: string;
    chooseLabel?: string;
    removeLabel?: string;
    hideLabel?: boolean;
    placeholder?: string;
    required?: boolean;
    validators?: ValidatorFn[];
    validatorsMessages?: ValidatorMessages;
    copy?: boolean;
    copyValue?: any;
    removable?: boolean;
    href?: string;
    error?: string;
    min?: any;
    max?: any;
    suffix?: string;
    prefix?: string | number;
    help?: string;
    helpIcon?: string;
    helpIconClass?: string;
    change?: AppFormFieldChange;
    paste?: PasteMethod;
    choices?: Choices;
    listChoices?: ListChoices;
    searchNoEntriesFoundLabel?: string;
    searchMultiple?: boolean;
    searchMethod?: SearchMethod;
    searchMethodPaginated?: SearchMethodPaginated;
    searchResultFormatter?: SearchResultFormatter;
    searchComparisonProperty?: string;
    showCreateText?: string;
    showSearchingText?: string;
    openDialogCreate?: OpenDialogCreate;
    isCreateAvailable?: boolean;
    parameters?: { [key: string]: any };
    folderEntityContext?: EntityContext;
    maxLength?: number;
    minLength?: number;
    inline?: boolean;
    fileTypesAccept?: Array<string>;
    thumbnailFile?: boolean | string;
    showFilePreview?: boolean;
    actionMethod?: () => any;
    actionText?: string;
    secondaryText?: string;
    searchAdvanced?: boolean;
    iconClass?: string;
    iconColor?: string;
    rows?: number;
    timezone?: string;
    navigateCommands?: any;
    navigateQueryParams?: any;
    iconRemovable?: string;
    iconClassRemovable?: string;
    templateEditorOptions?: TemplateEditorOptions;
    useConfirmDeleteDialog?: boolean;
    iconTooltip?: string;
    placeholderSearchInList?: string;
}

export type OpenDialogCreate = (searchingValue?: string) => any;
export type SearchResultFormatter = (result: any) => string;
export type SearchMethod = (searchTerm: string) => Observable<any[]>;
export type SearchMethodPaginated = (params: any) => Observable<PaginatedResponse<any>>;
export type AppFormFieldChange = (controlName: string, newValue: any, formData: AppFormFieldData[], formGroup: FormGroup, formGroupName: string, $event: any) => any[];
export type PasteMethod = ($event: any, controlName: string, formGroup: FormGroup, formGroupName: string, ) => any;
export type Choices = { key: string, value: any, icon?: any, color?: ThemePalette, iconClass?: string, tooltip?: string, disabled?: boolean }[];
type ValidatorMessages = { [key: string]: string };
type ListChoices = { name: string, value: Choices }[];

@Component({
    selector: 'app-form-field',
    templateUrl: './app-form-field.component.html',
    styleUrls: ['./app-form-field.component.scss']
})
export class AppFormFieldComponent implements AfterViewInit, OnDestroy, OnInit {

    @Input() formData: AppFormFieldData[];
    @Input() formGroupName: string;
    @Input() formGroup: FormGroup;
    @Input() type: AppFormFieldData['type'];
    @Input() controlName: string;
    @Input() icon?: string;
    @Input() label?: string;
    @Input() floatLabel?: 'auto' | 'always' | 'never';
    @Input() viewLabel?: string;
    @Input() removeLabel?: string;
    @Input() chooseLabel?: string;
    @Input() hideLabel?: boolean;
    @Input() placeholder?: string;
    @Input() required?: boolean;
    @Input() validators?: ValidatorFn[];
    @Input() validatorsMessages?: ValidatorMessages;
    @Input() copy?: boolean;
    @Input() copyValue?: any;
    @Input() removable?: boolean;
    @Input() href?: string;
    @Input() error?: string;
    @Input() min?: any;
    @Input() max?: any;
    @Input() suffix?: string;
    @Input() prefix?: string | number;
    @Input() help?: string;
    @Input() helpIcon?: string;
    @Input() helpIconClass?: string;
    @Input() change?: AppFormFieldChange;
    @Input() paste?: PasteMethod;
    @Input() choices?: Choices;
    @Input() listChoices?: ListChoices;
    @Input() searchNoEntriesFoundLabel?: string;
    @Input() searchMultiple?: boolean;
    @Input() searchMethod?: SearchMethod;
    @Input() searchMethodPaginated?: SearchMethodPaginated;
    @Input() searchResultFormatter?: SearchResultFormatter;
    @Input() searchComparisonProperty?: string;
    @Input() showCreateText?: string;
    @Input() showSearchingText?: string;
    @Input() openDialogCreate?: OpenDialogCreate;
    @Input() isCreateAvailable?: boolean;
    @Input() parameters?: { [key: string]: any } = {};
    @Input() folderEntityContext?: EntityContext;
    @Input() maxLength?: number;
    @Input() minLength?: number;
    @Input() inline?: boolean;
    @Input() fileTypesAccept?: Array<string>;
    @Input() showFilePreview?: boolean;
    @Input() thumbnailFile?: boolean | string;
    @Input() actionMethod?: () => any;
    @Input() actionText?: string;
    @Input() secondaryText?: string;
    @Input() searchAdvanced?: boolean;
    @Input() iconClass?: string;
    @Input() iconColor?: string;
    @Input() rows?: number;
    @Input() timezone?: string;
    @Input() navigateCommands?: any;
    @Input() navigateQueryParams?: any;
    @Input() iconRemovable?: string;
    @Input() iconClassRemovable?: string;
    @Input() templateEditorOptions?: TemplateEditorOptions;
    @Input() useConfirmDeleteDialog?: boolean;
    @Input() iconTooltip?: string;
    @Input() placeholderSearchInList?: string;

    @Output() updatedFormFields = new EventEmitter();
    searchingValue = '';
    showCreateButton: boolean;

    private _unsubscribeAll: Subject<any> = new Subject();
    formControlFilteringCtrl?: FormControl = new FormControl();
    searching?: boolean;
    searchResults?: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
    listChoicesCloned: ListChoices = [];

    constructor(private _dialog: MatDialog,
                private _authService: AuthService,
                private _fileService: FileService,
                private _sanitizer: DomSanitizer,
                @Inject(DOCUMENT) private document: Document) {
        this.showCreateButton = false;
    }

    changeAndUpdatedFormFields(newValue: any, $event: any): void {
        if (this.change) {
            // Be careful, an appFormField cannot return itself!
            const otherUpdatedFormFields = this.change(this.controlName, newValue, this.formData, this.formGroup, this.formGroupName, $event);
            if (otherUpdatedFormFields) {
                this.updatedFormFields.emit(otherUpdatedFormFields.map(a => a.controlName));
            }
        }
    }

    removeValue($event: any): void {
        const formControl = this.formGroup.get(this.formGroupName).get(this.controlName);
        formControl.setValue(null);
        formControl.markAsDirty();
        if (this.type === 'file') {
            const elem = (this.document.getElementById('form-field-file-' + this.controlName) as HTMLInputElement);
            elem.type = '';
            elem.type = 'file';
        }
        this.changeAndUpdatedFormFields(null, $event);
    }

    removeValueWithConfirm($event: any): void {
        const formControl = this.formGroup.get(this.formGroupName).get(this.controlName);
        const dialogRef = this._dialog.open(DeletionConfirmationComponent, {
            panelClass: ['full-page-scroll-30'],
            data: {
                messageKey: 'common.metadatas.confirmDeletion',
                data: formControl
            }
        });
        dialogRef.componentInstance.actionValue$.pipe(
            filter((confirmation: boolean) => confirmation),
            finalize(() => {
                dialogRef.componentInstance.close();
            })
        ).subscribe(() => {
            this.removeValue($event);
        });
    }

    keyForDates(key: string): string {
        return 'matDatepicker' + _.capitalize(key);
    }

    keyForDateTime(key: string): string {
        return 'matDatetimePicker' + _.capitalize(key);
    }

    initSearchResults(value): void {
        this.searchResults.next(value ? (this.searchMultiple ? value : [value]) : []);
    }

    ngOnInit(): void {
        this.listChoicesCloned = this.listChoices;
        if (this.type === 'search') {
            const value = this.formGroup.get(this.formGroupName).get(this.controlName).value;
            // The value will appear only if it is present in the select options, which are populated from the search results
            this.initSearchResults(value);
        }
    }

    ngAfterViewInit(): void {
        if (this.formControlFilteringCtrl && this.type !== 'infiniteSearch') {

            this.formControlFilteringCtrl.valueChanges.pipe(
                map(search => search?.trim() ?? ''),
                filter(search => !search || search.length >= 3),
                tap(() => this.searching = true),
                takeUntil(this._unsubscribeAll),
                debounceTime(500),
                switchMap(search => {
                    this.searchingValue = search;
                    return this.searchMethod(search);
                }),
            ).subscribe(searchResultsResponse => {
                this.searching = false;
                this.showCreateButton = this.isCreateAvailable && searchResultsResponse.length === 0;
                const value = this.formGroup.get(this.formGroupName).get(this.controlName).value;
                if (value && this.searchingValue === '') {
                    if (typeof value === 'object' && value.length) {
                        value.forEach((val) => {
                            if (!searchResultsResponse.find(result => result[this.searchComparisonProperty] === val[this.searchComparisonProperty])) {
                                searchResultsResponse.push(val);
                            }
                        });
                    } else if (!searchResultsResponse.find(result => result[this.searchComparisonProperty] === value[this.searchComparisonProperty])) {
                        searchResultsResponse.push(value);
                    }
                }
                this.searchResults.next(searchResultsResponse);
            });
        }
        if (this.type === 'search' && this.isCreateAvailable) {
            // When create available, the value can be changed from the outside
            // The value will appear only if it is present in the select options, which are populated from the search results
            // so we add it if it is not present
            this.formGroup.get(this.formGroupName).get(this.controlName).valueChanges.subscribe(selectedValue => {
                if (selectedValue) {
                    // First because we actually don't want to subscribe to every searchResults, we only want the current value
                    this.searchResults.pipe(first()).subscribe((searchResultData) => {
                        if (!searchResultData.find((searchResult) => this.compareSearchResult(searchResult, selectedValue))) {
                            this.initSearchResults(selectedValue);
                            this.showCreateButton = false;
                        }
                    });
                }
            });
        }
    }

    initSearch(): void {
        this.formControlFilteringCtrl.updateValueAndValidity({onlySelf: false, emitEvent: true});
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    // Required so that search results are matched with existing current local value
    // Needs to be an arrow function so that "this" is known (otherwise this is the select, not the app form field)
    compareSearchResult = (result1: any, result2: any): boolean => {
        return this.searchComparisonProperty && result1?.hasOwnProperty(this.searchComparisonProperty) && result2?.hasOwnProperty(this.searchComparisonProperty) ?
            result1[this.searchComparisonProperty] === result2[this.searchComparisonProperty] :
            result1 === result2;
    }

    hasValue(): boolean {
        const value = this.formGroup.get(this.formGroupName).get(this.controlName).value;
        if (this.type === 'tags' || (this.type === 'search' && this.searchMultiple)) {
            return value && value.length > 0;
        } else {
            return value !== undefined && value !== null;
        }
    }

    formatDateWithoutSeconds($event: MatDatetimePickerInputEvent<any>): void {
        if ($event.value) {
            $event.value.setSeconds(0);
        }
    }

    addActivity(folderEntityContext: EntityContext, type: string, title?: string, description?: string): void {
        if (folderEntityContext) {
            const dialogRef = this._dialog.open(ActivityFormComponent, {
                panelClass: ['full-page-scroll-50'],
                data: {
                    entityClass: folderEntityContext.class,
                    entityId: folderEntityContext.id,
                    type: type,
                    title: title,
                    description: description
                }
            });
            dialogRef.afterClosed().subscribe(res => {
                if (res) {
                    // how to refresh activities?
                }
            });
        }
    }


    updateFileToFormGroup($event: any, formGroup: any, key: string): void {
        if ($event && $event.target.files.length > 0) {
            formGroup.get(key).setValue($event.target.files[0]);
            formGroup.get(key).markAsDirty();
            this.changeAndUpdatedFormFields($event.target.files[0], $event);
        }
    }

    displayFilePreview(value: any): SafeUrl {
        if (value instanceof File) {
            return this._sanitizer.bypassSecurityTrustUrl(URL.createObjectURL(value));
        } else if (value.length > 0) {
            if (value.startsWith('/app')) { // secured access
                const url = new URL(value, document.location.href);
                url.searchParams.set('token', this._authService.accessToken);
                value = url.href;
            }
            return this._sanitizer.bypassSecurityTrustUrl(value);
        }
    }

    downloadFile(value: any): boolean {
        if (value.startsWith('files/publicFiles')) {
            window.open(value, '_blank');
        } else {
            this._fileService.download(value).subscribe();
        }
        return false;
    }

    getStaticValue(): any {
        const formValue = this.formGroup.get(this.formGroupName).get(this.controlName).value;
        let staticValue = formValue;
        if (this.type === 'select') {
            let choices: Choices;
            if (this.choices?.length) {
                choices = this.choices;
            } else if (this.listChoices?.length) {
                choices = flatten(this.listChoices.map(({value}) => value));
            }
            const choice = choices?.find(({value}) => isEqual(value, formValue));
            staticValue = choice?.key;
        }
        return staticValue;
    }

    searchInListChoices(eventTarget: any): any[] {
        const value = eventTarget.value;
        if (!value) {
            return this.listChoices;
        }
        this.listChoices = this.searchAndUpdateListChoices(value);
    }

    searchAndUpdateListChoices(value: string): any[] {
        return this.listChoicesCloned.map(criteria => {
            const filteredValues = criteria.value.filter((criteriaValue) =>
                [criteriaValue.key, criteriaValue.value].some(field => field.toLowerCase().includes(value.toLowerCase()))
            );
            const isGroupMatch = criteria.name === value.toLowerCase();
            return isGroupMatch || filteredValues.length > 0
                ? {...criteria, values: filteredValues.length > 0 ? filteredValues : criteria.value}
                : null;
        }).filter((option) => option !== null);
    }

    keyup(event): void {
        if (this.parameters && this.parameters['current'] && this.parameters['length']) {
            const previousFormField = document.getElementById('form-field-' + this.formGroupName + '-'  + this.parameters['previous']);
            const nextFormField = document.getElementById('form-field-' + this.formGroupName + '-' + this.parameters['next']);
            const formValue = this.formGroup.get(this.formGroupName).get(this.parameters['current']).value;
            if (event.code === 'Backspace' && !formValue) {
                if (previousFormField) {
                    previousFormField.focus();
                }
            } else if (nextFormField && formValue?.length === this.parameters['length']) {
                nextFormField.focus();
            }
        }
    }

    pasteMethod(event): void {
        if (this.paste) {
            this.paste(event, this.controlName,  this.formGroup, this.formGroupName);
        }
    }
}
