<ng-container *ngFor="let control of appFormFieldsData; let i = index;" [formGroup]="formGroup">
    <app-form-field *ngIf="formGroup.get(formGroupName).get(control.controlName)"
                    (updatedFormFields)="updatedFormFields($event)"
                    [id]="control.controlName +'-'+ i"
                    [class]="(control.class ? control.class : '') + ' col-span-' + (control.colSpan ? control.colSpan : '6')"
                    [formData]="appFormFieldsData"
                    [formGroupName]="formGroupName"
                    [formGroup]="formGroup"
                    [type]="control.type"
                    [floatLabel]="control.floatLabel"
                    [controlName]="control.controlName"
                    [icon]="control.icon"
                    [iconClass]="control.iconClass"
                    [iconColor]="control.iconColor"
                    [label]="control.label"
                    [chooseLabel]="control.chooseLabel"
                    [removeLabel]="control.removeLabel"
                    [hideLabel]="control.hideLabel"
                    [placeholder]="control.placeholder"
                    [required]="control.required"
                    [validators]="control.validators"
                    [validatorsMessages]="control.validatorsMessages"
                    [copy]="control.copy"
                    [copyValue]="control.copyValue"
                    [removable]="control.removable"
                    [href]="control.href"
                    [error]="control.error"
                    [min]="control.min"
                    [max]="control.max"
                    [suffix]="control.suffix"
                    [prefix]="control.prefix"
                    [help]="control.help"
                    [helpIcon]="control.helpIcon"
                    [helpIconClass]="control.helpIconClass"
                    [change]="control.change"
                    [paste]="control.paste"
                    [choices]="control.choices"
                    [searchNoEntriesFoundLabel]="control.searchNoEntriesFoundLabel"
                    [searchMultiple]="control.searchMultiple"
                    [searchMethod]="control.searchMethod"
                    [searchResultFormatter]="control.searchResultFormatter"
                    [searchComparisonProperty]="control.searchComparisonProperty"
                    [showCreateText]="control.showCreateText"
                    [showSearchingText]="control.showSearchingText"
                    [openDialogCreate]="control.openDialogCreate"
                    [isCreateAvailable]="control.isCreateAvailable"
                    [searchMethodPaginated]="control.searchMethodPaginated"
                    [parameters]="control.parameters"
                    [folderEntityContext]="control.folderEntityContext"
                    [maxLength]="control.maxLength"
                    [minLength]="control.minLength"
                    [inline]="control.inline"
                    [fileTypesAccept]="control.fileTypesAccept"
                    [thumbnailFile]="control.thumbnailFile"
                    [showFilePreview]="control.showFilePreview"
                    [viewLabel]="control.viewLabel"
                    [actionMethod]="control.actionMethod"
                    [actionText]="control.actionText"
                    [secondaryText]="control.secondaryText"
                    [searchAdvanced]="control.searchAdvanced"
                    [rows]="control.rows"
                    [listChoices]="control.listChoices"
                    [navigateCommands]="control.navigateCommands"
                    [navigateQueryParams]="control.navigateQueryParams"
                    [iconClassRemovable]="control.iconClassRemovable"
                    [iconRemovable]="control.iconRemovable"
                    [timezone]="control.timezone"
                    [templateEditorOptions]="control.templateEditorOptions"
                    [useConfirmDeleteDialog]="control.useConfirmDeleteDialog"
                    [iconTooltip]="control.iconTooltip"
                    [placeholderSearchInList]="control.placeholderSearchInList">
    </app-form-field>
</ng-container>
