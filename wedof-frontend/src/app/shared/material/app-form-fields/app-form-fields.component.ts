import {Component, Input, OnChanges, SimpleChanges} from '@angular/core';
import {FormControl, FormGroup, Validators} from '@angular/forms';
import {AppFormFieldData} from '../app-form-field/app-form-field.component';
import {isEqual} from 'lodash';
import {ReplaySubject} from 'rxjs';

@Component({
    selector: 'app-form-fields',
    templateUrl: './app-form-fields.component.html',
    styleUrls: ['./app-form-fields.component.scss']
})
export class AppFormFieldsComponent implements OnChanges {

    @Input() formGroupName: string;
    @Input() appFormFieldsData: AppFormFieldData[];
    @Input() formGroup: FormGroup;
    @Input() entity: any;
    @Input() appFormFieldData$?: ReplaySubject<AppFormFieldData>;

    constructor() {
    }

    ngOnChanges(changes: SimpleChanges): void {
        const entityFormGroup = this.formGroup.get(this.formGroupName) as FormGroup;
        this.appFormFieldsData.forEach((appFormFieldData) => {
            const isFirstChange = changes.appFormFieldsData?.isFirstChange();
            this.createOrUpdateFormControl(entityFormGroup, appFormFieldData, isFirstChange);
        });
        this.appFormFieldData$?.subscribe(appFormFieldData => {
            this.createOrUpdateFormControl(entityFormGroup, appFormFieldData);
        });
    }

    updatedFormFields(formControlNames): void {
        const entityFormGroup = this.formGroup.get(this.formGroupName) as FormGroup;
        this.appFormFieldsData.forEach((appFormFieldData) => {
            if (formControlNames.includes(appFormFieldData.controlName)) {
                this.createOrUpdateFormControl(entityFormGroup, appFormFieldData);
            }
        });
    }

    private createOrUpdateFormControl(entityFormGroup: FormGroup, appFormFieldData: AppFormFieldData, firstChange: boolean = false): void {
        if (appFormFieldData.required) {
            if (!appFormFieldData.validators) {
                appFormFieldData.validators = [];
            }
            if (!appFormFieldData.validators.includes(Validators.required)) {
                appFormFieldData.validators.push(Validators.required);
            }
        } else if (appFormFieldData.validators?.includes(Validators.required)) {
            appFormFieldData.validators.splice(appFormFieldData.validators.indexOf(Validators.required), 1);
        }
        const newValue = appFormFieldData.value !== undefined ? appFormFieldData.value : (this.entity ? this.entity[appFormFieldData.controlName] : null);
        let isUpdateValue = false;
        const existingControl = entityFormGroup.controls[appFormFieldData.controlName];
        if (existingControl) {
            isUpdateValue = !isEqual(newValue, existingControl.value);
            entityFormGroup.removeControl(appFormFieldData.controlName);
        }
        if (!appFormFieldData.removed) {
            if (isUpdateValue) {
                // A timeout is required here so that reactive forms REALLY update the value
                // Otherwise, sometimes it keeps an old value despite setting explicitly a new value in the new appFormFieldData
                // It seems to me that it does some kind of diff on the before / after and if it has not changed enough, decides to keep the old one
                // We force it to clear it entirely by doing it in a callback so that first it thinks that the formControl has disappeared
                // Then adds it again as it's a new one, rather than trying to "update" it internally
                // We tried several things with not luck: entityFormGroup.controls[appFormFieldData.controlName].reset(), entityFormGroup.setControl...
                // There is a downside to this solution: in a "change" method an app form field cannot return itself
                setTimeout(() => {
                    this.addControl(entityFormGroup, appFormFieldData, firstChange, newValue);
                });
            } else {
                this.addControl(entityFormGroup, appFormFieldData, firstChange, newValue);
            }
        }
    }

    private addControl(entityFormGroup: FormGroup, appFormFieldData: AppFormFieldData, firstChange: boolean, newValue: any): void {
        entityFormGroup.addControl(appFormFieldData.controlName, new FormControl({
                value: newValue,
                disabled: !!appFormFieldData.disabled,
            },
            appFormFieldData.validators ?? null
        ));
        if (!firstChange) {
            setTimeout(() => {
                const control = entityFormGroup.controls[appFormFieldData.controlName];
                control.markAsTouched({onlySelf: true});
                control.updateValueAndValidity();
                const formFieldWrapperElement = document.getElementById('form-field-wrapper-' + this.formGroupName + '-' + appFormFieldData.controlName);
                if (control.errors) {
                    formFieldWrapperElement.classList.add('mat-form-field-invalid');
                } else {
                    formFieldWrapperElement?.classList?.remove('mat-form-field-invalid');
                }
            }, 1);
        }
    }
}
