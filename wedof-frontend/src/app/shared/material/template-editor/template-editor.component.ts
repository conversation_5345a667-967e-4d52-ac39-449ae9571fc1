import {Component, Input, OnChanges, OnInit, TemplateRef, ViewChild} from '@angular/core';
import {FormBuilder, FormGroup} from '@angular/forms';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {MatSnackBar} from '@angular/material/snack-bar';
import {APIEndpoint} from '../../api/services/api-endpoint.enum';

export interface TemplateEditorOptions {
    templateGid?: string; // used in dialog should be refactored
    layout?: string; // used in dialog should be refactored
    type: string;
    scope: string;
    contextId?: string;
    additionalInformation?: string;
}

@Component({
    selector: 'app-template-editor',
    templateUrl: './template-editor.component.html',
    styleUrls: ['./template-editor.component.scss']
})

export class TemplateEditorComponent implements OnInit, OnChanges {

    @Input() formGroup?: FormGroup;
    @Input() formGroupName?: string;
    @Input() controlName?: string;
    @Input() label?: string;
    @Input() required?: boolean;
    @Input() placeholder?: string;
    @Input() maxLength?: number;
    @Input() hideLabel?: boolean;
    @Input() type: string;
    @Input() scope: string;
    @Input() contextId?: string;
    @Input() additionalInformation?: string;
    @ViewChild('copySuccess') copySuccessTemplate: TemplateRef<any>;
    copiedValue: string;
    dictionary: any;
    search: string;

    constructor(private _snackBar: MatSnackBar, private _fb: FormBuilder, private http: HttpClient) {
    }

    ngOnInit(): void {
        this.getVariables().subscribe((res: any) => {
            this.dictionary = res;
        });
    }

    ngOnChanges(): void {
        this.getVariables().subscribe((res: any) => {
            this.dictionary = res;
        });
    }

    getCopy(value: string): any {
        this.copiedValue = value;
        this._snackBar.openFromTemplate(this.copySuccessTemplate, {
            verticalPosition: 'top',
            horizontalPosition: 'center',
            duration: 2500,
        });
        return value;
    }

    private getVariables(): Observable<any> {
        return this.http.get<any>(`${APIEndpoint.APP}/utils/dictionary?`, {
            params: {
                type: this.type ?? 'document',
                scope: this.scope ? this.scope : '',
                contextId: this.contextId ?? null,
            }
        });
    }

    filterDictionary(dictionary: any[]): any[] {
        if (!this.search) {
            return dictionary;
        }
        return dictionary.map(option => {
            const filteredValues = option.values.filter((options: { name: any; key: any; }) =>
                [options.name, options.key].some(field => field.toLowerCase().includes(this.search.toLowerCase()))
            );

            const isGroupMatch = [option.name, option.key].some(field =>
                field.toLowerCase().includes(this.search.toLowerCase())
            );

            return isGroupMatch || filteredValues.length > 0
                ? {...option, values: filteredValues.length > 0 ? filteredValues : option.values}
                : null;
        }).filter((option) => option !== null);
    }

    trackByFn(index: number, item: any): any {
        return item.key;
    }

}
