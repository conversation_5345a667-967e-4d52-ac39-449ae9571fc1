<div class="w-full flex" [style]="formGroup ? 'position: relative' : ''">
    <div class="w-2/3 mr-2" *ngIf="formGroup">
        <mat-form-field [formGroup]="formGroup" id="form-field-wrapper-{{formGroupName}}-{{controlName}}"
                        class="w-full">
            <mat-label class="mb-2" *ngIf="label && !hideLabel">{{ label | translate }}</mat-label>
            <mat-icon
                [matTooltipPosition]="'above'"
                [matTooltip]="'private.common.message-templates.body.tooltip' | translate"
                matSuffix>help_outline
            </mat-icon>
            <textarea id="textarea" rows="30" formControlName="{{controlName}}"
                      [required]="required"
                      [placeholder]="placeholder | translate"
                      matInput
            ></textarea>
        </mat-form-field>
    </div>

    <div class="flex flex-col" [style]="formGroup ? 'position: absolute; right: 0; bottom: 0; top: 0' : ''"
         [class]="formGroup ? 'w-1/3' : 'w-5/6'">
        <mat-form-field class="{{formGroup ? '' : 'mt-4'}} ml-2">
            <mat-label class="flex items-center mb-4">
                Variables
            </mat-label>
            <input type="text" matInput
                   [placeholder]="'private.common.message-templates.search' | translate"
                   [(ngModel)]="search">
            <mat-icon svgIcon="search"></mat-icon>
        </mat-form-field>
        <div class="overflow-x-hidden overflow-y-auto ml-2 flex flex-col justify-start" [class]="formGroup ? '' : 'dynamic-height'">
            <treo-message *ngIf="additionalInformation"
                          [showIcon]="false"
                          appearance="outline"
                          type="info">
                {{ additionalInformation | translate }}
            </treo-message>
            <div *ngFor="let listChoiceOption of filterDictionary(dictionary); trackBy: trackByFn">
                <div class="sticky-header font-bold label px-4 py-2 z-10">
                    {{ listChoiceOption.name | translate }}
                </div>
                <div *ngFor="let choiceOption of listChoiceOption.values"
                     class="flex items-center justify-between px-4 py-2 cursor-pointer choice"
                     ngxClipboard
                     [cbContent]="'{' + '{' + (listChoiceOption.key ? listChoiceOption.key + '.' : '') + choiceOption.key + '}' + '}'"
                     (cbOnSuccess)="getCopy('{' + '{' + (listChoiceOption.key ? listChoiceOption.key + '.' : '') + choiceOption.key + '}' + '}')">
                    <span class="text-sm flex-grow">
                        {{ choiceOption.name }}
                        : {{ '{' + '{' + (listChoiceOption.key ? listChoiceOption.key + '.' : '') + choiceOption.key + '}' + '}' }}
                    </span>
                </div>
            </div>
        </div>
        <a class="pt-2 text-secondary text-sm text-center" target="_blank"
           href="/aide/guides/applications/dictionnaire">{{ 'private.common.message-templates.body.seeDictionnary' | translate }}</a>
        <a class="no-underline pt-2 text-secondary text-sm text-center"
           [href]="'mailto:' + '<EMAIL>?subject=Ajout de données dans le dictionnaire des variables'">{{ 'private.common.message-templates.body.addNewData' | translate }}</a>
    </div>
</div>

<ng-template #copySuccess>
    {{ 'Variable copiée' | translate : {value: copiedValue} }}
</ng-template>
