<div [formGroup]="formGroup">
    <mat-form-field [class]=" containerClass ? containerClass : ''">
        <mat-label [class]="labelClass ? labelClass : ''">{{label}}</mat-label>
        <mat-select (opened)="initSearch(formControlFilteringCtrl)"
                    (closed)="clearSearch()"
                    formControlName="{{controlName}}"
                    [required]="required"
                    [placeholder]="placeholder"
                    [multiple]="multiple"
                    (selectionChange)="selectionChange()"
                    msInfiniteScroll
                    (infiniteScroll)="getNextBatch()"
                    [compareWith]="compareSearchResult"
                    [complete]="totalPages != null && currentPage >= totalPages"
        >
            <mat-option>
                <ngx-mat-select-search
                    [formControl]="formControlFilteringCtrl"
                    [clearSearchInput]="false"
                    [noEntriesFoundLabel]="searchingLabel && searching ? searchingLabel : noEntriesFoundLabel"
                    [placeholderLabel]="placeholderLabel"
                    [disableScrollToActiveOnOptionsChanged]="true"
                    [searching]="searching"></ngx-mat-select-search>
            </mat-option>
            <mat-option
                *ngFor="let result of searchResults$ | async"
                [value]=" searchAdvanced && searchComparisonProperty ? result[searchComparisonProperty] : result">
                {{ resultFormatter ? resultFormatter(result) : result}}
            </mat-option>
        </mat-select>
        <button type="button" *ngIf="hasValue()"
                mat-button matSuffix
                mat-icon-button aria-label="Clear" (click)="clear($event)">
            <mat-icon svgIcon="close"></mat-icon>
        </button>
        <mat-icon
            *ngIf="tooltip"
            [matTooltipPosition]="'above'"
            [matTooltip]="tooltip | translate"
            matSuffix>help_outline
        </mat-icon>
        <mat-error class="pb-1" *ngIf="errorMessage">
            <ng-container *ngIf="required">
                {{ errorMessage }}
            </ng-container>
        </mat-error>
    </mat-form-field>
</div>
