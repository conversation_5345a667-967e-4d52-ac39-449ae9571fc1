import {AfterViewInit, Component, EventEmitter, Input, OnDestroy, OnInit, Output} from '@angular/core';
import {Observable, ReplaySubject, Subject, Subscription} from 'rxjs';
import {FormControl, FormGroup} from '@angular/forms';
import {debounceTime, distinctUntilChanged, filter, map, scan, takeUntil, tap} from 'rxjs/operators';
import {BaseHttpParams, PaginatedResponse} from '../../api/services/abstract-paginated.service';

export type SearchMethod = (params: any) => Observable<PaginatedResponse<any>>;

export interface AdditionalParams extends BaseHttpParams {
    status?: string;
    eligible?: boolean;
}

@Component({
    selector: 'app-infinite-scroll',
    templateUrl: './infinite-scroll.component.html',
    styleUrls: ['./infinite-scroll.component.scss']
})
export class InfiniteScrollComponent implements OnInit, OnDestroy, AfterViewInit {

    totalPages: number;
    currentPage: number;
    searching: boolean;

    formControlFilteringCtrl: FormControl = new FormControl();
    searchResults: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
    searchResults$: Observable<any[]>;
    searchSubscription: Subscription;

    @Input() controlName: string;
    @Input() label: string;
    @Input() placeholder: string;
    @Input() searchingLabel?: string;
    @Input() noEntriesFoundLabel: string;
    @Input() placeholderLabel: string;
    @Input() formGroup: FormGroup;
    @Input() searchComparisonProperty ?: string;
    @Input() addToSearchResult ?: any[];
    @Input() resultFormatter: (result: any) => string;
    @Input() searchMethod: SearchMethod;
    @Input() value?: string;
    @Input() tooltip?: string;
    @Input() required?: boolean;
    @Input() containerClass?: string;
    @Input() labelClass?: string;
    @Input() errorMessage?: string;
    @Output() changeSelection?: EventEmitter<void> = new EventEmitter<void>();
    @Input() multiple?: boolean;
    @Input() searchAdvanced?: boolean;
    @Input() parameters?: { [key: string]: any } = {};

    protected _unsubscribeAll = new Subject<void>();

    constructor() {
    }

    selectionChange(): void {
        if (this.changeSelection && this.formGroup.get(this.controlName).value) {
            this.changeSelection.emit();
        }
    }

    ngOnInit(): void {
        const value = this.formGroup.get(this.controlName).value;
        this.initSearchResults(value);
    }

    clear(event: MouseEvent): void {
        this.formGroup.get(this.controlName).setValue(null);
        this.searchResults.next([]);
        event.stopPropagation();
        this.formGroup.markAsDirty(); // needed when component is used in appFormFields
        if (this.changeSelection) {
            this.changeSelection.emit();
        }
        this.formGroup.get(this.controlName).markAsTouched();
    }

    initSearchResults(value): void {
        this.searchResults.next(value ? (this.multiple ? value : [value]) : []);
    }

    compareSearchResult = (result1: any, result2: any): boolean => {
        if (this.searchComparisonProperty) {
            return this.searchComparisonProperty && result1?.hasOwnProperty(this.searchComparisonProperty) && result2?.hasOwnProperty(this.searchComparisonProperty) ?
                result1[this.searchComparisonProperty] === result2[this.searchComparisonProperty] :
                result1 === result2;
        }
    }

    ngAfterViewInit(): void {
        this.formControlFilteringCtrl.valueChanges
            .pipe(
                filter(search => !!search),
                distinctUntilChanged(),
                debounceTime(200),
                takeUntil(this._unsubscribeAll),
            )
            .subscribe(search => {
                this.firstBatch(search);
            });

        setTimeout(() => {
            this.formControlFilteringCtrl.patchValue(' ');
            this.formControlFilteringCtrl.patchValue('');
        });
    }

    initSearch(formControl: FormControl): void {
        formControl.updateValueAndValidity({onlySelf: false, emitEvent: true});
    }

    firstBatch(search: string): void {
        this.currentPage = 1;
        if (this.searchSubscription) {
            this.searchSubscription.unsubscribe();
        }
        this.searchResults.complete();
        this.searchResults = new ReplaySubject<any[]>(1); // New search, so create a new empty "cache" for our search results
        this.searchResults$ = this.searchResults.asObservable().pipe(
            scan((currentOptions, newOptions) => {
                return [...currentOptions, ...newOptions]; // Create an observable that accumulates values over time
            })
        );
        this.searchSubscription = this.list(search).pipe(
            map(filteredValues => {
                this.addToSearchResult?.forEach(item => {
                    filteredValues.payload.unshift(item);
                });
                return filteredValues;
            })
        ).subscribe(filteredValues => {
            this.totalPages = Math.ceil((filteredValues?.total - this.addToSearchResult?.length ?? 0) / filteredValues?.itemsPerPage);
            this.searchResults.next(filteredValues?.payload);
        });
    }

    getNextBatch(): void {
        this.currentPage++;
        this.list(this.formControlFilteringCtrl.value).subscribe(newFilteredValues => {
            this.searchResults.next(newFilteredValues.payload);
        });
    }

    list(search?: string): Observable<PaginatedResponse<any>> {
        const params: AdditionalParams = {
            page: this.currentPage,
            ...this.parameters
        };
        if (search) {
            params.query = search;
        }
        this.searching = true;
        return this.searchMethod(params).pipe(
            tap(() => this.searching = false),
        );
    }

    hasValue(): boolean {
        const value = this.formGroup.get(this.controlName).value;
        const typeOfValue = typeof value;
        if (typeOfValue === 'object') {
            return value !== null && Object.keys(value).length >= 1;
        } else if (typeOfValue === 'string') {
            return value && value.length >= 1;
        } else {
            return value !== undefined && value !== null;
        }
    }

    clearSearch(): void {
        this.formControlFilteringCtrl.setValue('');
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }
}
