<app-dialog-layout
    [title]="dialogData.title | translate"
    [showCancel]="false"
    [actions]="actions"
    (dialogClose)="closeModal()">

    <p class="mb-3" *ngIf="dialogData.subtitle" [innerHTML]="dialogData.subtitle | translate"></p>
    <p class="mb-4" *ngIf="dialogData.subtitle2" [innerHTML]="dialogData.subtitle2 | translate"></p>
    <p class="mb-2" *ngIf="dialogData.subtitle3" [innerHTML]="dialogData.subtitle3 | translate"></p>

    <ng-template #actions>
        <div class="flex flex-row py-2">
            <button *ngIf="dialogData.mainButton"
                    type="button"
                    mat-flat-button
                    color="primary"
                    mat-button
                    (click)="mainAction()">
                {{ dialogData.mainButton.title | translate }}
            </button>
            <button *ngIf="dialogData.secondaryButton"
                    type="button"
                    mat-flat-button
                    class="ml-3 bg-gray-200"
                    mat-button
                    (click)="secondaryAction()">
                {{ dialogData.secondaryButton.title | translate }}
            </button>
        </div>
    </ng-template>

</app-dialog-layout>
