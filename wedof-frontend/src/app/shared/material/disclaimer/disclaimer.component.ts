import {Component, Inject} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material/dialog';

interface ButtonTypeAction {
    title: string;
    method(): void;
}

@Component({
    selector: 'app-disclaimer',
    templateUrl: './disclaimer.component.html',
    styleUrls: ['./disclaimer.component.scss']
})
export class DisclaimerComponent {

    constructor(
        public dialogRef: MatDialogRef<DisclaimerComponent>,
        @Inject(MAT_DIALOG_DATA) public dialogData: {
            title: string;
            subtitle: string;
            subtitle2?: string;
            subtitle3?: string;
            mainButton?: ButtonTypeAction
            secondaryButton?: ButtonTypeAction
        },
    ) {
    }

    closeModal(): void {
        this.dialogRef.close();
    }

    mainAction(): void {
        this.closeModal();
        this.dialogData.mainButton.method();
    }

    secondaryAction(): void {
        this.closeModal();
        this.dialogData.secondaryButton.method();
    }

}
