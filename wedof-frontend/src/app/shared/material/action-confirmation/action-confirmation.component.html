<app-dialog-layout [title]="getTitle() | translate" cancelText="common.actions.no"
                   [actions]="actions" (dialogClose)="no()">

    <treo-message type="info" *ngIf="dialogData.treoMessage"
                  [showIcon]="false" class="mb-4 mt-2 text-center" appearance="outline">
        <p [innerHTML]="dialogData.treoMessage"></p>
    </treo-message>

    <div [innerHTML]="dialogData.messageKey | translate:dialogData.data | markdown"></div>

    <ng-template #actions>
        <button mat-flat-button [color]="getColor()" (click)="yes()">
            <mat-progress-spinner *ngIf="isWaiting ; else label" [diameter]="24" mode="indeterminate">
            </mat-progress-spinner>
            <ng-template #label>
                <ng-container *ngIf="dialogData.submissionText; else noLabel">
                    {{ dialogData.submissionText | translate }}
                </ng-container>
                <ng-template #noLabel>
                    {{ 'common.actions.yes' | translate }}
                </ng-template>
            </ng-template>
        </button>
    </ng-template>
</app-dialog-layout>
