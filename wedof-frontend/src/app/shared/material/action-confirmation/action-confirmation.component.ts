import {Component, Inject} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material/dialog';
import {Subject} from 'rxjs';
import {ThemePalette} from '@angular/material/core';

export interface ActionDialogData {
    messageKey: string;
    data: any;
    title?: string;
    color?: ThemePalette;
    treoMessage?: string;
    submissionText?: string;
}

@Component({
    selector: 'app-action-confirmation',
    templateUrl: './action-confirmation.component.html',
    styleUrls: ['./action-confirmation.component.scss']
})
export class ActionConfirmationComponent {
    actionValue$: Subject<boolean>;
    isWaiting: boolean;

    constructor(
        public _dialogRef: MatDialogRef<ActionConfirmationComponent>,
        @Inject(MAT_DIALOG_DATA) public dialogData: ActionDialogData,
    ) {
        this.actionValue$ = new Subject();
    }

    private completeActionValue(value: boolean): void {
        this.actionValue$.next(value);
        this.actionValue$.complete();
    }

    getTitle(): string {
        return this.dialogData.title ?? 'common.confirmation.action.title';
    }

    getColor(): ThemePalette {
        return this.dialogData.color ?? 'primary';
    }

    close(): void {
        this._dialogRef.close();
    }

    no(): void {
        this.completeActionValue(false);
        this.close();
    }

    yes(): void {
        if (!this.isWaiting) {
            this.isWaiting = true;
            this.completeActionValue(true);
        }
    }
}
