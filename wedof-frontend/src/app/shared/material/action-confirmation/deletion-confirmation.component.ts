import {Component, Inject} from '@angular/core';
import {ActionConfirmationComponent, ActionDialogData} from './action-confirmation.component';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material/dialog';

@Component({
    // Hack : the easiest way to "inherit" the template seems to be referencing it here
    templateUrl: './action-confirmation.component.html'
})
// DeletionConfirmationComponent is just a wrapper that "fixes" two parameters
export class DeletionConfirmationComponent extends  ActionConfirmationComponent{
    constructor(
        public _dialogRef: MatDialogRef<DeletionConfirmationComponent>,
        @Inject(MAT_DIALOG_DATA) public dialogData: ActionDialogData,
    ) {
        super(_dialogRef, dialogData);
        if (!dialogData.title) {
            dialogData.title = 'common.confirmation.deletion.title';
        }
        dialogData.color = 'warn';
    }
}
