import {Component, EventEmitter, Input, Output, TemplateRef} from '@angular/core';
import {ThemePalette} from '@angular/material/core';

@Component({
    selector: 'app-dialog-layout',
    templateUrl: './dialog-layout.component.html',
    styleUrls: ['./dialog-layout.component.scss']
})
export class DialogLayoutComponent {
    @Input() topActions: TemplateRef<any>;
    @Input() actions: TemplateRef<any>;
    @Input() showCancel = true;
    @Input() cancelText = 'common.actions.cancel';
    @Output() dialogClose = new EventEmitter<void>();
    @Input() disabled: boolean;
    @Input() title: string;
    @Input() title2?: string;
    @Input() errorMessages: string[];
    @Input() warningMessage: string;
    @Input() feedbackMessage: string;
    @Input() configurationIcon?: { icon: string, color: ThemePalette, class: string, tooltip: string };

    onClose(): void {
        this.dialogClose.emit();
    }

}
