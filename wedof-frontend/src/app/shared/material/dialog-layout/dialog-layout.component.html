<header class="flex flex-row">
    <mat-icon *ngIf="configurationIcon" [color]="configurationIcon['color']"
              [matTooltip]="configurationIcon['tooltip']"
              [svgIcon]="configurationIcon['icon']" [class]="configurationIcon['class']">
    </mat-icon>
    <div *ngIf="title && title2; else showTitle" class="flex items-baseline flex-grow mt-0 mb-4">
        <h3 *ngIf="title" class="font-normal">{{ title }}</h3>
        <p class="text-sm ml-2"> {{ title2 }}</p>
    </div>
    <ng-template #showTitle>
        <h3 *ngIf="title" class="font-normal mt-0 mb-4 flex-grow">
            {{ title }}
        </h3>
    </ng-template>
    <ng-container *ngTemplateOutlet="topActions"></ng-container>
    <button type="button" class="self-start ml-4" (click)="onClose()" [disabled]="disabled">
        <mat-icon>close</mat-icon>
    </button>
</header>

<ng-content></ng-content>

<treo-message type="warning" [showIcon]="false" class="mb-4" appearance="outline" *ngIf="warningMessage">
    <div class="flex m-2">
        <mat-icon class="mr-4 self-center" [svgIcon]="'warning'"></mat-icon>
        <div>{{ warningMessage }}</div>
    </div>
</treo-message>

<div class="flex justify-end items-center dialog-actions" [ngClass]="{'mt-2': errorMessages?.length || (feedbackMessage && disabled) || showCancel || actions}">
    <div *ngIf="errorMessages?.length" class="flex items-center">
        <treo-message class="flex-auto" appearance="outline" [showIcon]="false" type="error">
            <ul>
                <li *ngFor="let errorMessage of errorMessages">
                    {{ errorMessage }}
                </li>
            </ul>
        </treo-message>
    </div>

    <treo-message type="info" [showIcon]="false" appearance="outline" *ngIf="feedbackMessage && disabled">
        <p>{{ feedbackMessage }}</p>
    </treo-message>

    <button *ngIf="showCancel" type="button" mat-button (click)="onClose()" [disabled]="disabled">
        {{ cancelText | translate }}
    </button>
    <ng-container *ngTemplateOutlet="actions"></ng-container>
</div>
