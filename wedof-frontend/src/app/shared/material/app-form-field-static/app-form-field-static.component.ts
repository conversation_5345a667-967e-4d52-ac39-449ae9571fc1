import {Component, EventEmitter, Input, Output, TemplateRef, ViewChild} from '@angular/core';
import {MatSnackBar} from '@angular/material/snack-bar';
import {AppFormFieldData} from '../app-form-field/app-form-field.component';
import {ActivityFormComponent} from '../../activities/activity-form/activity-form.component';
import {MatDialog} from '@angular/material/dialog';
import {EntityContext} from '../../api/models/entity-context';
import {FileService} from '../../api/services/file.service';
import {ViewFileDialogComponent} from '../../file/dialogs/view-file-dialog/view-file-dialog.component';
import {File} from '../../api/models/file';
import {Router} from '@angular/router';

@Component({
    selector: 'app-form-field-static',
    templateUrl: './app-form-field-static.component.html',
    styleUrls: ['./app-form-field-static.component.scss']
})
export class AppFormFieldStaticComponent {

    @Input() type: AppFormFieldData['type'];
    @Input() icon?: string;
    @Input() label?: string;
    @Input() viewLabel?: string;
    @Input() hideLabel?: boolean;
    @Input() placeholder?: string;
    @Input() copy?: boolean;
    @Input() copyValue?: any;
    @Input() href?: string;
    @Input() value: any;
    @Input() suffix?: string;
    @Input() help?: string;
    @Input() lengthLimit?: number;
    @Input() searchResultFormatter?: (value: any) => string;
    @Input() searchMultiple?: boolean;
    @Input() folderEntityContext?: EntityContext;
    @Input() timezone?: string;
    @Output() customClick = new EventEmitter<any>();
    @Input() navigateCommands?: any;
    @Input() navigateQueryParams?: any;
    @Input() iconClass?: string;
    @Input() iconColor?: string;
    @Input() iconTooltip?: string;

    @ViewChild('copySuccess') copySuccessTemplate: TemplateRef<any>;

    constructor(private _snackBar: MatSnackBar,
                private _fileService: FileService,
                private _router: Router,
                private _dialog: MatDialog) {
    }

    getCopy(value: string): any {
        this._snackBar.openFromTemplate(this.copySuccessTemplate, {
            verticalPosition: 'top',
            horizontalPosition: 'center',
            duration: 2500,
        });
        return value;
    }

    getCopiedValue(): any {
        if (this.copyValue) {
            return this.copyValue;
        } else if (this.href) {
            return this.href;
        } else {
            return this.value;
        }
    }

    hasValue(): boolean {
        if (this.type === 'tags' || (this.type === 'search' && this.searchMultiple)) {
            return this.value && this.value.length > 0;
        } else {
            return this.value !== undefined && this.value !== null;
        }
    }

    addActivity(entityContext: EntityContext, type: string, title?: string, description?: string): void {
        if (entityContext) {
            const dialogRef = this._dialog.open(ActivityFormComponent, {
                panelClass: ['full-page-scroll-50'],
                data: {
                    entityClass: entityContext.class,
                    entityId: entityContext.id,
                    type: type,
                    title: title,
                    description: description
                }
            });
            dialogRef.afterClosed().subscribe(res => {
                if (res) {
                    // how to rpassefresh activities?
                }
            });
        }
    }

    downloadFile(file: File | string): boolean {
        if (typeof file !== 'string') {
            this._dialog.open(ViewFileDialogComponent, {
                width: '80%',
                height: '90%',
                data: {
                    file: file,
                    auth: true,
                    title: (_file: File) => _file.fileName,
                    src: (_file: File) => {
                        return file.url?.startsWith('/') ? window.origin + file.url : file.url;
                    }
                }
            });
        } else {
            if (file.startsWith('files/publicFiles')) {
                window.open(file, '_blank');
            } else {
                this._fileService.download(file).subscribe();
            }
        }
        return false;
    }

    navigate(navigateQueryParams): void {
        this._router.navigate(this.navigateCommands, {queryParams: navigateQueryParams});
    }
}
