<div class="flex-auto mb-1">
    <div [ngStyle]="{'margin-bottom':'10px'}" class="flex items-center font-medium app-form-field-static-loading-hidden"
         *ngIf="label && !hideLabel">
        {{ label | translate }}
        <mat-icon *ngIf="help"
                  class="icon-help"
                  [matTooltipPosition]="'above'"
                  [matTooltip]="help | translate"
                  matSuffix>help_outline
        </mat-icon>
    </div>
    <div class="flex">
        <mat-icon class="self-center app-form-field-static-loading-show icon-size-18"
                  [ngClass]="iconClass"
                  [color]="iconColor"
                  [matTooltip]="iconTooltip ? (iconTooltip | translate) : label ? (label | translate) : ''"
                  *ngIf="icon">{{ icon }}
        </mat-icon>
        <div class="flex items-center app-form-field-static-loading-show" [ngClass]="{'ml-2': icon}">
            <ng-container *ngIf="hasValue() else noValue"
                          [ngSwitch]="type">
                <div *ngSwitchCase="'date'">
                    {{ timezone ? (value | date:'dd/MM/yyyy':timezone) : (value | date:'dd/MM/yyyy') }}
                </div>
                <div *ngSwitchCase="'datetime-local'">
                    {{ value | date:'dd/MM/yyyy (HH:mm)' }}
                </div>
                <div *ngSwitchCase="'url'">
                    <a [href]="href ? href : value" target="_blank" rel="noopener noreferrer" class="flex items-center">
                      {{value.length <= 60 ? value : value.slice(0, 60) + " ..."}}
                        <mat-icon svgIcon="open_in_new" class="icon-copy"></mat-icon>
                    </a>
                </div>
                <div *ngSwitchCase="'email'">
                    <a [href]="'mailto:' + value"
                       (click)="addActivity(folderEntityContext,'email', 'Nouvel email')">{{ value }}</a>
                </div>
                <div *ngSwitchCase="'tel'">
                    <a [href]="'tel:' + value"
                       (click)="addActivity(folderEntityContext, 'phone', 'Nouvel appel')">{{ value }}</a>
                </div>
                <div *ngSwitchCase="'tags'" class="flex flex-row items-end">
                    <mat-chip-list>
                        <mat-chip *ngFor="let tag of (lengthLimit != null ? value.slice(0, lengthLimit) : value)"
                                  [class.cursor-pointer]="customClick.observers.length"
                                  (click)="customClick.emit(tag)">
                            {{ tag }}
                        </mat-chip>
                        <mat-chip *ngIf="lengthLimit != null && value.length > lengthLimit"
                                  [matTooltipPosition]="'above'"
                                  [matTooltip]="value.slice(lengthLimit, value.size).join(', ')">
                            <mat-icon>
                                more_horiz
                            </mat-icon>
                        </mat-chip>
                    </mat-chip-list>
                </div>
                <div *ngSwitchCase="'search'">
                    <ng-container *ngIf="searchMultiple">
                        <ul class="list-disc list-inside">
                            <li *ngFor="let item of value">{{ searchResultFormatter(item) }}</li>
                        </ul>
                    </ng-container>
                    <ng-container *ngIf="!searchMultiple">
                        {{ searchResultFormatter(value) }}
                    </ng-container>
                </div>
                <div *ngSwitchCase="'infiniteSearch'">
                    <ng-container *ngIf="searchMultiple">
                        <ul class="list-disc list-inside">
                            <li *ngFor="let item of value">{{ searchResultFormatter(item) }}</li>
                        </ul>
                    </ng-container>
                    <ng-container *ngIf="!searchMultiple">
                        {{ searchResultFormatter(value) }}
                    </ng-container>
                </div>
                <div *ngSwitchCase="'percent'">
                    {{ value }}%
                </div>
                <div *ngSwitchCase="'price'">
                    {{ value }}€
                </div>
                <div *ngSwitchCase="'hours'">
                    {{ value }}h
                </div>
                <div *ngSwitchCase="'text-html'"
                     [innerHTML]="value">
                </div>
                <div *ngSwitchCase="'url-html'" class="flex">
                    <a class="link"
                       target="_blank"
                       (click)="navigate(navigateQueryParams[0])">
                        {{value[0]}}
                    </a>
                    <ng-container *ngIf="navigateQueryParams[1]">
                        <span class="ml-1 mr-1">/</span>
                        <a class="link"
                           target="_blank"
                           (click)="navigate(navigateQueryParams[1])">
                            {{value[1]}}
                        </a>
                    </ng-container>
                </div>
                <div class="textarea overflow-auto" *ngSwitchCase="'textarea'">
                    {{ value }}
                </div>
                <div *ngSwitchCase="'color'">
                    <span style="color:{{ value }}">{{ value }}</span>
                </div>
                <div *ngSwitchCase="'file'">
                    <a href="" (click)="downloadFile(value)" target="_blank">{{ viewLabel | translate }}</a>
                </div>
                <div *ngSwitchCase="'password'">
                    *********
                </div>
                <div *ngSwitchDefault>
                    {{ value }}
                </div>
                <span *ngIf="suffix">&nbsp;{{ suffix }}</span>
                <mat-icon *ngIf="(!label || hideLabel) && help"
                          class="icon-help"
                          [matTooltipPosition]="'above'"
                          [matTooltip]="help | translate"
                          matSuffix>help_outline
                </mat-icon>
                <button *ngIf="copy"
                        type="button"
                        ngxClipboard [cbContent]="getCopiedValue()"
                        (cbOnSuccess)="getCopy(getCopiedValue())"
                        [attr.aria-label]="'Copier'"
                        class="ml-1">
                    <mat-icon svgIcon="file_copy" class="icon-copy">
                    </mat-icon>
                </button>
            </ng-container>
        </div>
    </div>
</div>

<ng-template #noValue>
    <span
        class="text-secondary">{{ placeholder ? (placeholder | translate) : ('private.common.form.placeholder' | translate) }}</span>
</ng-template>

<ng-template #copySuccess>
    {{ 'private.common.registrationFolder.attendee.copy' | translate : {value: getCopiedValue()} }}
</ng-template>
