<div>
    <ng-content></ng-content>
</div>
<button mat-icon-button (click)="$event.stopPropagation()" [matMenuTriggerFor]="accessFilters">
    <mat-icon
        [matBadge]="selectedFilters?.length ? selectedFilters?.length+'' : null"
        matBadgePosition="below after"
        matBadgeSize="small"
        [color]="selectedFilters?.length ? 'primary' : undefined"
        [svgIcon]="selectedFilters?.length ? 'filter_alt' : 'mat_outline:filter_alt'"></mat-icon>
</button>
<mat-menu class="large-menu" [class]="'menu-filters'" #accessFilters="matMenu" (close)="onApplyFilters()">
    <div mat-menu-item class="justify-between cursor-pointer" (click)="onClearFilter($event)"
         [disabled]="selectedFilters.length === 0">
        <div *ngIf="selectedFilters.length === 0; else filterActive"> Selectionnez un {{ title }}</div>
        <ng-template #filterActive> {{ selectedFilters.length }} {{ title }}(s) selectionné(s)</ng-template>
        <mat-icon *ngIf="selectedFilters.length > 0" class="cursor-pointer cursor-pointer">close</mat-icon>
    </div>
    <mat-divider></mat-divider>
    <div class="menu-items-filters">
        <ng-container *ngFor="let filter of filters">
            <p *ngIf="filter.type == 'header' else regularFilter" class="font-semibold ml-4">
                {{ filter.label | translate }}
            </p>
            <ng-template #regularFilter>
                <div mat-menu-item *ngIf="getTypeOfValue( filter.value); else noCheckAllow">
                    <ng-template [ngTemplateOutlet]="displayCheckbox"
                                 [ngTemplateOutletContext]="{filter: filter}">
                    </ng-template>
                </div>
            </ng-template>
            <ng-template #noCheckAllow>
                <button mat-menu-item class="w-full" [matMenuTriggerFor]="subMenu"
                        [matMenuTriggerData]="{filtersSubMenu : filter.value}">
                    <mat-checkbox disabled class="-mt-8"
                                  (click)="$event.stopPropagation(); $event.preventDefault()"
                                  [checked]="checkedValuesLength(filter.value) == filter.value.length"
                                  [indeterminate]="checkedValuesLength(filter.value) > 0 && checkedValuesLength(filter.value) < filter.value.length">
                    </mat-checkbox>
                    <mat-icon *ngIf="filter.icon" class="ml-1" matPrefix [class]="filter.class"
                              [color]="filter.color">{{ filter.icon }}
                    </mat-icon>
                    {{ filter.label | translate }}
                </button>
            </ng-template>
        </ng-container>
    </div>
</mat-menu>

<mat-menu class="large-menu" [class]="'menu-filters'" #subMenu="matMenu">
    <ng-template matMenuContent let-filtersSubMenu="filtersSubMenu">
        <ng-container *ngFor="let filter of filtersSubMenu">
            <div mat-menu-item>
                <ng-template [ngTemplateOutlet]="displayCheckbox"
                             [ngTemplateOutletContext]="{filter: filter}">
                </ng-template>
            </div>
        </ng-container>
    </ng-template>
</mat-menu>

<ng-template #displayCheckbox let-filter="filter">
    <mat-checkbox class="w-full" [value]="filter.value"
                  (change)="onSelectFilter(filter.value)"
                  (click)="$event.stopPropagation()" [checked]="selectedFilters?.includes(filter.value)">
        <mat-icon *ngIf="filter.icon" class="ml-1" matPrefix [class]="filter.class"
                  [color]="filter.color">{{ filter.icon }}
        </mat-icon>
        {{ filter.label | translate }}
    </mat-checkbox>
</ng-template>
