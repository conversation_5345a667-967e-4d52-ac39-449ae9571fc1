import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {clone} from 'lodash';
import {ThemePalette} from '@angular/material/core';

export type TableFilter = {
    label: string;
    value: string | TableFilter[];
    color?: ThemePalette;
    icon?: string;
    headerName?: string;
    type?: 'filter',
    class?: string
} | {
    label: string;
    type: 'header'
};

@Component({
    selector: 'app-table-multiple-filter',
    templateUrl: './table-multiple-filter.component.html',
    styleUrls: ['./table-multiple-filter.component.scss']
})
export class TableMultipleFilterComponent implements OnInit {
    @Input() filters: TableFilter[];
    @Input() title: string;
    @Output() selectFilters: EventEmitter<string[]>;
    @Input() defaultValueState: string[];
    selectedFilters: string[] = [];
    currentFilters: string[] = [];

    constructor() {
        this.selectFilters = new EventEmitter();
    }

    ngOnInit(): void {
        if (this.defaultValueState) {
            this.selectedFilters = this.defaultValueState;
            this.onApplyFilters();
        }
    }

    onClearFilter(event: MouseEvent): void {
        this.selectedFilters = [];
        event.stopPropagation();
    }

    onSelectFilter(value: string): void {
        const index = this.selectedFilters.indexOf(value);
        if (index >= 0) {
            this.selectedFilters.splice(index, 1);
        } else {
            this.selectedFilters.push(value);
        }
    }

    onApplyFilters(): void {
        if (this.selectedFilters.length !== this.currentFilters.length || !this.selectedFilters.every((value) => this.currentFilters.includes(value))) {
            this.currentFilters = clone(this.selectedFilters);
            this.selectFilters.emit(this.selectedFilters);
        }
    }

    updateCurrentFiltersData(newFilters: string[]): void {
        this.currentFilters = clone(newFilters);
        this.selectedFilters = clone(newFilters);
    }

    getTypeOfValue(value: any): boolean {
        return typeof value === 'string';
    }

    checkedValuesLength(filterValues: TableFilter[]): number {
        let checkedEvents = 0;
        this.selectedFilters.forEach((filter) => {
            filterValues.forEach((filterValue) => {
                if (filterValue.type !== 'header' && filter === filterValue.value) {
                    checkedEvents += 1;
                }
            });
        });
        return checkedEvents;
    }

}
