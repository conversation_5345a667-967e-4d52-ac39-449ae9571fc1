import {SelectionModel} from '@angular/cdk/collections';
import {AfterViewInit, Directive, Injector} from '@angular/core';
import {Observable} from 'rxjs';
import {PaginatedResponse} from '../../api/services/abstract-paginated.service';
import {AbstractTableComponent} from './abstract-table.component';
import {remove} from 'lodash-es';


@Directive()
// tslint:disable-next-line: directive-class-suffix
export abstract class AbstractSelectableTableComponent<T> extends AbstractTableComponent<T> implements AfterViewInit {
    isTotalRowsSelected = false;
    selection = new SelectionModel<T>(true, []);

    protected constructor(injector: Injector) {
        super(injector);
    }

    checkboxAllLabelKey(): string {
        return `common.table.selection.all.${this.isAllSelected() ? 'deselect' : 'select'}`;
    }

    checkboxRowLabelKey(row: T): string {
        return `common.table.selection.row.${this.isRowSelected(row) ? 'deselect' : 'select'}`;
    }

    currentSelectionCount(): number {
        return this.isTotalRowsSelected ? this.total : this.selection.selected.length;
    }

    isAllSelected(): boolean {
        return this.displayedData?.length
            && (this.isTotalRowsSelected
                || this.selection.selected.length === this.displayedData?.filter(row => this.isSelectable(row)).length);
    }

    isRowSelected(row: T): boolean {
        return this.isTotalRowsSelected || this.selection.isSelected(row);
    }

    isSelectable(row: T): boolean {
        return true;
    }

    ngAfterViewInit(): void {
        super.ngAfterViewInit();

        this.paginator?.page.subscribe(() => {
            this.clearSelection();
        });
    }

    selectAllTotalLine(): void {
        this.isTotalRowsSelected = true;
    }

    toggleAll(): void {
        if (this.isAllSelected()) {
            this.clearSelection();
        } else {
            this.selectAll();
        }
    }

    selectAll(): void {
        this.displayedData.filter(row => this.isSelectable(row)).forEach(row => this.selection.select(row));
        this.isTotalRowsSelected = this.selection.selected.length === this.total;
    }

    toggleRow(row: T): void {
        if (this.isSelectable(row)) {
            this.selection.toggle(row);
            this.isTotalRowsSelected = this.selection.selected.length === this.total;
        }
    }

    protected onContextChange(): void {
        super.onContextChange();
        this.clearSelection();
    }

    protected abstract refreshData(pageIndex?: number): Observable<PaginatedResponse<T>>;

    public clearSelection(): void {
        this.selection.clear();
        this.isTotalRowsSelected = false;
    }

    refreshRow($event: T): void {
        // Map to generate new array because the dumb CDK component does not take into account changes to datasource unless a new array is provided or renderRows is called...
        this.displayedData = this.displayedData.map(row => {
            // @ts-ignore
            if ((row.hasOwnProperty('id') && row.id === $event.id)
                // @ts-ignore
                || (row.hasOwnProperty('externalId') && row.externalId === $event.externalId) || (row.hasOwnProperty('code') && row.code === $event.code)) {
                return $event;
            } else {
                return row;
            }
        });
        // @ts-ignore
        if (this.selectedRow && ((this.selectedRow.hasOwnProperty('id') && this.selectedRow.id === $event.id)
            // @ts-ignore
            || (this.selectedRow.hasOwnProperty('externalId') && this.selectedRow.externalId === $event.externalId) ||
            // @ts-ignore
            (this.selectedRow.hasOwnProperty('code') && this.selectedRow.code === $event.code))) {
            this.onRowClick($event);
        }
        this._changeDetectorRef.detectChanges();
    }

    insertRow($newRow): void {
        // Don't mutate existing array because the dumb CDK component does not take into account changes to datasource unless a new array is provided or renderRows is called...
        this.displayedData = [$newRow, ...this.displayedData];
        this.onRowClick($newRow);
        this._changeDetectorRef.detectChanges();
    }

    deleteRow($row): void {
        remove(this.displayedData, $row);
        this._changeDetectorRef.detectChanges();
    }
}
