<div>
    <ng-content></ng-content>
</div>
<button mat-icon-button (click)="$event.stopPropagation()" [matMenuTriggerFor]="accessFilters">
    <mat-icon [color]="selectedFilter ? 'primary' : undefined">filter_alt</mat-icon>
</button>
<mat-menu #accessFilters="matMenu" class="large-menu">
    <button mat-menu-item (click)="onClearFilter()" *ngIf="selectedFilter">
        <mat-icon>close</mat-icon>
        {{ 'private.common.table.filter.clear' | translate }}
    </button>
    <ng-container *ngFor="let filter of filters; let i = index">
        <button mat-menu-item (click)="onSelectFilter(filter)">
            <input class="cursor-pointer" [id]="getId(i)" type="radio" [name]="name" [value]="filter.value"
                   [(ngModel)]="selectedFilter"/>
            <mat-icon *ngIf="filter.icon" class="ml-1" matPrefix
                      [color]="filter.color" [class]="filter.class">{{filter.icon}}</mat-icon>
            <label class="cursor-pointer ml-1" [for]="getId(i)">
                {{ filter.label | translate }}
            </label>
        </button>
    </ng-container>
</mat-menu>
