import {Component, EventEmitter, Inject, Input, OnInit, Optional, Output} from '@angular/core';

export interface TableFilter {
    label: string;
    value: string;
    color?: string;
    icon?: string;
    class?: string;
}

@Component({
    selector: 'app-table-filter',
    templateUrl: './table-filter.component.html',
    styleUrls: ['./table-filter.component.scss']
})
export class TableFilterComponent implements OnInit {
    @Input() filters: TableFilter[];
    @Output() selectFilter: EventEmitter<string>;
    @Output() isSelectionFiltered: EventEmitter<boolean>;
    @Input() defaultValue?: TableFilter;
    selectedFilter: string;

    get name(): string {
        return this._columnDef?.name || 'filter';
    }

    ngOnInit(): void {
        if (this.defaultValue) {
            this.onSelectFilter(this.defaultValue);
        }
    }

    constructor(
        @Inject('MAT_SORT_HEADER_COLUMN_DEF') @Optional()
        private _columnDef: { name: string; }
    ) {
        this.selectFilter = new EventEmitter();
        this.isSelectionFiltered = new EventEmitter<boolean>();
    }

    getId(index: number): string {
        return `filter-${this.name}-${index}`;
    }

    onClearFilter(): void {
        this.selectedFilter = null;
        this.isSelectionFiltered.emit(false);
        this.selectFilter.emit(this.selectedFilter);
    }

    onSelectFilter(filter: TableFilter): void {
        this.selectedFilter = filter.value;
        this.isSelectionFiltered.emit(true);
        this.selectFilter.emit(this.selectedFilter);
    }

}
