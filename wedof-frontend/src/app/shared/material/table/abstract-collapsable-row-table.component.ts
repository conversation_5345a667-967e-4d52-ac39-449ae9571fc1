import {AfterViewInit, Directive, Injector} from '@angular/core';
import {Observable} from 'rxjs';
import {PaginatedResponse} from '../../api/services/abstract-paginated.service';
import {AbstractTableComponent} from './abstract-table.component';

@Directive()
// tslint:disable-next-line: directive-class-suffix
export abstract class AbstractCollapsableRowTableComponent<T> extends AbstractTableComponent<T> implements AfterViewInit {
    elementTagNameWithScroll = 'MAIN';
    rowStates = new Map<string | number, { fetched: boolean, opened: boolean }>();

    constructor(injector: Injector) {
        super(injector);
    }

    closeAllRows(): void {
        this.rowStates.forEach(value => {
            value.opened = false;
        });
    }

    isFetched(row: T): boolean {
        return this.rowStates.get(this.getRowIdentifier(row))?.fetched;
    }

    isOpened(row: T): boolean {
        return this.rowStates.get(this.getRowIdentifier(row))?.opened;
    }

    openRow(row: T, event?: MouseEvent): void {
        if (!row) {
            return;
        }
        this.closeAllRows();
        this.rowStates.set(this.getRowIdentifier(row), {
            fetched: true,
            opened: true
        });

        this.scrollToRow(event);
    }

    toggleRow(row: T, event?: MouseEvent): void {
        if (!row) {
            return;
        }
        if (this.isOpened(row)) {
            this.rowStates.get(this.getRowIdentifier(row)).opened = false;
        } else {
            this.openRow(row, event);
        }
    }

    refreshRow(newRow: T): void {
        // Don't update selectedRow as it is not used
        if (this.displayedData?.length && this.displayedData.findIndex(currentRow => this.isRowEqual(currentRow, newRow)) !== -1) {
            // Map to generate new array because the dumb CDK component does not take into account changes to datasource unless a new array is provided or renderRows is called...
            this.displayedData = this.displayedData.map(currentRow => this.isRowEqual(currentRow, newRow) ? newRow : currentRow);
        }
    }

    protected isRowEqual(row1: T, row2: T): boolean {
        return this.getRowIdentifier(row1) === this.getRowIdentifier(row2);
    }

    protected abstract getRowIdentifier(row: T): string | number;

    protected abstract refreshData(): Observable<PaginatedResponse<T>>;

    protected refreshTableData(): Observable<PaginatedResponse<T>> {
        this.closeAllRows();
        return super.refreshTableData();
    }

    private getParentElementByTag(element: HTMLElement, tagName: string): HTMLElement {
        if (element.tagName?.toLocaleUpperCase() === tagName.toLocaleUpperCase()) {
            return element;
        }
        return this.getParentElementByTag(element.parentElement, tagName);
    }

    private scrollToRow(event: MouseEvent): void {
        if (!event?.target) {
            return;
        }
        const rowElement = this.getParentElementByTag(event.target as HTMLElement, 'TR');
        const scrollElement = this.getParentElementByTag(rowElement, this.elementTagNameWithScroll);
        this._changeDetectorRef.detectChanges();
        const { x, y } = {
            x: rowElement.getBoundingClientRect().x - scrollElement.getBoundingClientRect().x,
            y: rowElement.getBoundingClientRect().y - scrollElement.getBoundingClientRect().y
        };
        scrollElement.scrollBy(x, y);
    }

    insertRow($newRow): void {
        this.displayedData = [$newRow, ...this.displayedData];
        this.onRowClick($newRow);
        this._changeDetectorRef.detectChanges();
    }

}
