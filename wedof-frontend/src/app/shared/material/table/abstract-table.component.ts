import {
    AfterViewInit,
    ChangeDetectorRef,
    Directive,
    EventEmitter,
    Injector,
    Input,
    OnDestroy,
    Output,
    ViewChild
} from '@angular/core';
import {FormControl} from '@angular/forms';
import {MatPaginator, PageEvent} from '@angular/material/paginator';
import {MatSort} from '@angular/material/sort';
import {BehaviorSubject, EMPTY, merge, Observable, of, Subject} from 'rxjs';
import {catchError, debounceTime, distinctUntilChanged, filter, switchMap, takeUntil, tap} from 'rxjs/operators';
import {PaginatedResponse} from '../../api/services/abstract-paginated.service';
import {PaginatorComponent} from './paginator/paginator.component';

export interface DataQuery {
    next?: boolean;
    previous?: boolean;
    keepShortcutSide?: boolean;
}

export interface DataResponse<T> {
    row: T;
    keepShortcutSide: boolean;
}

@Directive()
// tslint:disable-next-line: directive-class-suffix
export abstract class AbstractTableComponent<T, D extends DataQuery = DataQuery> implements AfterViewInit, OnDestroy {
    @Input() selectedRow: T = null;
    @Input() externalSelectionChange: boolean; // Set to true when external selection change (e.g. state change). Requires selectedRow to know when selection changes
    @Input() dataQuery$: Observable<D>;
    @Output() followingRow = new EventEmitter<DataResponse<T>>();
    @Output() totalRows = new EventEmitter<number>();
    displayedColumns: string[];
    displayedData: T[];
    isLoading = true;
    pageSizeOptions: number[] = [25];
    queryCtrl = new FormControl();
    total = 0;

    @ViewChild(PaginatorComponent) private appPaginator: PaginatorComponent;
    @ViewChild(MatSort) protected sort: MatSort;

    get paginator(): MatPaginator {
        return this.appPaginator?.matPaginator;
    }

    get activeFilters(): { query?: string, [param: string]: string | string[] } {
        return this._filters$.value;
    }

    get hasActiveFilters(): boolean {
        return Object.keys(this._filters$.value).length > 0;
    }

    protected _filters$: BehaviorSubject<{ query?: string, [param: string]: string | string[] }>;
    protected _unsubscribeAll: Subject<void>;
    protected _changeDetectorRef: ChangeDetectorRef;

    protected constructor(injector: Injector) {
        this._changeDetectorRef = injector.get(ChangeDetectorRef);
        this._filters$ = new BehaviorSubject({});
        this._unsubscribeAll = new Subject();
    }

    clearQueryFilter(): void {
        this.queryCtrl.setValue(null);
    }

    forceRefresh(): void {
        this.refreshTableData().subscribe(data => this.assignTableData(data));
    }

    ngAfterViewInit(): void {
        // Listen to optional query for previous/next data
        this.dataQuery$?.pipe(
            filter(query => this.filterDataQuery(query)),
            takeUntil(this._unsubscribeAll),
        ).subscribe(query => {
            const currentDisplayIndex = this.displayedData.findIndex((row) => this.findRowWithDataQuery(row, query));
            const newIndex = query.next ? currentDisplayIndex + 1 : currentDisplayIndex - 1;
            if (0 <= newIndex && newIndex < this.displayedData.length) {
                this.emitRow(this.displayedData[newIndex], newIndex, query.keepShortcutSide);
            }
        });

        // Listen to form control value changes for global text search
        this.queryCtrl.valueChanges.pipe(
            distinctUntilChanged(),
            debounceTime(500),
            takeUntil(this._unsubscribeAll),
        ).subscribe(value => {
            if (value) {
                this.applyFilter({name: 'query', value});
            } else {
                this.applyFilter({name: 'query', value: null});
            }
        });

        this.sort?.sortChange.pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe(() => {
            this.onContextChange();
        });

        const filters$ = this._filters$.pipe(
            distinctUntilChanged(),
            takeUntil(this._unsubscribeAll),
        );
        const observable = this.sort ? merge(this.sort.sortChange, filters$) : filters$;
        observable.pipe(
            debounceTime(100),
            switchMap(() => this.refreshTableData()),
            takeUntil(this._unsubscribeAll),
        ).subscribe(data => this.assignTableData(data));
    }

    ngOnDestroy(): void {
        this._filters$.complete();
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    onRowClick(row: T): void {
        this.emitRow(row, this.displayedData.indexOf(row));
    }

    onPageEvent($event: PageEvent): void {
        this.refreshTableData().subscribe(data => this.assignTableData(data));
    }

    protected applyFilter({name, value}: { name: string, value: string | string[] }): void {
        const filters = {...this._filters$.value};
        if (Array.isArray(value) ? value.length : value) {
            if (Array.isArray(value)) {
                value = value.join(',');
            }
            filters[name] = value;
        } else {
            delete filters[name];
        }
        this._filters$.next(filters);

        this.onContextChange();
    }

    protected emitRow(row: T, index: number, keepShortcutSide: boolean = false): void {
        // If a click triggers a state change, we do not want to change the "selectedRow" unless the state change is completed,
        // so the state change has the responsibility of updating the selectRow when it is completed
        if (!this.externalSelectionChange) {
            this.selectedRow = row;
        }
        this.followingRow.emit({
            row,
            keepShortcutSide
        });
    }

    hasPrevious(row?: T, rowIdentifier = 'id'): boolean {
        if (!row || !this.displayedData) {
            return false;
        }
        // There is also equality management on a child class of this component : AbstractCollapsableRowTableComponent.isRowEqual
        // Here we duplicates code for the same information, which is bad
        // However moving equality mgmt from AbstractCollapsableRowTableComponent to AbstractTableComponent was too much work for the scope of this task
        const itemIndex = this.displayedData.findIndex(currentRow => row[rowIdentifier] === currentRow[rowIdentifier]);
        return itemIndex > 0;
    }

    hasNext(row?: T, rowIdentifier = 'id'): boolean {
        if (!row || !this.displayedData) {
            return false;
        }
        // There is also equality management on a child class of this component : AbstractCollapsableRowTableComponent.isRowEqual
        // Here we duplicates code for the same information, which is bad
        // However moving equality mgmt from AbstractCollapsableRowTableComponent to AbstractTableComponent was too much work for the scope of this task
        const itemIndex = this.displayedData.findIndex(currentRow => row[rowIdentifier] === currentRow[rowIdentifier]);
        return itemIndex >= 0 && itemIndex < this.displayedData.length - 1;
    }

    clearSelectedRow(): void {
        this.selectedRow = null;
    }

    /**
     * Has to be overrided if the previous/next query observable is shared with many tables.
     */
    protected filterDataQuery(query: D): boolean {
        return true;
    }

    /**
     * Has to be overrided if the previous/next is used, to find the current row and so the previous/next one.
     */
    protected findRowWithDataQuery(row: T, query: D): boolean {
        return true;
    }

    /**
     * Should be called when context (sort, filter) changes.
     */
    protected onContextChange(): void {
        if (this.paginator) {
            this.paginator.firstPage();
        }
    }

    protected abstract refreshData(): Observable<PaginatedResponse<T>>;

    protected refreshTableData(): Observable<PaginatedResponse<T>> {
        return of(null).pipe(
            tap(() => {
                this.isLoading = true;
                this._changeDetectorRef.detectChanges();
            }),
            switchMap(() => this.refreshData()),
            tap(() => {
                this.isLoading = false;
            }),
            catchError(() => {
                this.isLoading = false;
                return EMPTY;
            }));
    }

    private assignTableData(data: PaginatedResponse<T>): void {
        this.displayedData = data?.payload ?? [];
        this.total = data?.total ?? 0;
        this.totalRows.emit(this.total);
    }

}
