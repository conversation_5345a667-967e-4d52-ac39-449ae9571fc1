import {AfterViewInit, ChangeDetectorRef, Component, ElementRef, EventEmitter, Input, OnChanges, OnDestroy, Output, ViewChild} from '@angular/core';
import {MatPaginator, PageEvent} from '@angular/material/paginator';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
    selector: 'app-paginator',
    templateUrl: './paginator.component.html',
    styleUrls: ['./paginator.component.scss']
})
export class PaginatorComponent implements AfterViewInit, OnChanges, OnDestroy {

    hidden: boolean;

    @Input() length: number;
    @Input() pageSizeOptions: number[];
    @Input() scrollTopOnPageChange = false;

    @Output() page = new EventEmitter<PageEvent>();

    private unsubscribeAll = new Subject();

    @ViewChild(MatPaginator) matPaginator: MatPaginator;

    constructor(
        private _elementRef: ElementRef<HTMLElement>,
        private _changeDetectorRef: ChangeDetectorRef,
    ) { }

    ngOnChanges(): void {
        this.setHidden();
    }

    ngAfterViewInit(): void {
        this.matPaginator.page.pipe(
            takeUntil(this.unsubscribeAll)
        ).subscribe(() => {
            if (this.scrollTopOnPageChange) {
                const scrollableElement = this.getScrollableParentElement(this._elementRef.nativeElement);
                if (scrollableElement) {
                    this._changeDetectorRef.detectChanges();
                    scrollableElement.scroll(0, 0);
                    scrollableElement?.parentElement.scroll(0, 0);
                }
            }
        });
    }

    ngOnDestroy(): void {
        this.unsubscribeAll.next();
        this.unsubscribeAll.complete();
    }

    private getScrollableParentElement(element: HTMLElement): HTMLElement {
        if (element) {
            if (element.scrollHeight > element.clientHeight) {
                return element;
            } else {
                return this.getScrollableParentElement(element.parentElement);
            }
        } else {
            return null;
        }
    }

    private setHidden(): void {
        this.hidden = !this.matPaginator || this.length < this.matPaginator.pageSize;
    }

}
