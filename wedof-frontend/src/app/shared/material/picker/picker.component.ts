import {Component, Inject} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material/dialog';
import {Observable} from 'rxjs';
import {finalize} from 'rxjs/operators';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../errors/errors.types';

export type PickerCards = {
    title: string;
    description: any;
    tags: string[];
    templateName: string;
    hasAccess: boolean;
    additionalInformations?: {
        format?: string,
        type?: string,
        id?: number,
        colorTags?: string
    },
    display: boolean
};

@Component({
    selector: 'app-picker',
    templateUrl: './picker.component.html',
    styleUrls: ['./picker.component.scss']
})
export class PickerComponent {

    errorMessages: string[] = [];
    infoMessages: string[] = [];
    loading = false;

    constructor(
        public _dialogRef: MatDialogRef<PickerComponent>,
        @Inject(MAT_DIALOG_DATA) public dialogData: {
            cards: PickerCards[],
            feedback?: string,
            selectTemplate(templateName: string): Observable<void>
        },
    ) {
    }

    close(): void {
        this._dialogRef.close();
    }

    selectTemplate(templateName: string): void {
        this.loading = true;
        this.errorMessages = [];
        this.infoMessages = this.dialogData.feedback ? [this.dialogData.feedback] : null;
        this.dialogData.selectTemplate(templateName).pipe(
            finalize(() => {
                this.loading = false;
                this.infoMessages = [];
            })
        ).subscribe({
            next: (result) => {
                this._dialogRef.close({data: result});
            },
            error: (httpErrorResponse: HttpErrorResponse) => {
                this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
            }
        });
    }

}
