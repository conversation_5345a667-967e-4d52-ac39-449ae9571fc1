<app-dialog-layout [title]="'common.actions.picker.title' | translate"
                   [cancelText]="'common.actions.close' | translate"
                   [disabled]="loading"
                   (dialogClose)="close()">

    <div class="flex flex-wrap justify-start picker-card-list">
        <ng-container *ngFor="let card of dialogData.cards">
            <mat-card *ngIf="card.display"
                      (click)="card.hasAccess && selectTemplate(card.templateName)"
                      class="w-60 h-60 mr-3 mb-3 p-4 flex flex-col justify-around border no-shadow-card">
                <p class="font-bold text-lg mb-2 h-12">{{ card.title }}</p>
                <mat-chip-list class="mb-1 h-10">
                    <mat-chip class="text-sm" *ngIf="card.additionalInformations?.type">
                        {{ card.additionalInformations.type }}
                    </mat-chip>
                    <app-tag class="text-sm font-semibold"
                             [color]="card.additionalInformations?.colorTags ? card.additionalInformations.colorTags: '#f2e5f2'"
                             *ngFor="let tag of card.tags">
                        {{ tag }}
                    </app-tag>
                </mat-chip-list>
                <p class="text-secondary h-16" [innerHTML]="card.description"></p>
                <button class="w-full mt-2" mat-flat-button color="primary" [disabled]="loading || !card.hasAccess"
                        (click)="selectTemplate(card.templateName); $event.preventDefault(); $event.stopPropagation();">{{ 'common.actions.picker.select' | translate }}
                </button>
            </mat-card>
        </ng-container>
    </div>
    <div *ngIf="infoMessages?.length" class="flex mt-3 items-center">
        <treo-message class="ml-auto mr-auto" appearance="outline" [showIcon]="false" type="info">
            <ul>
                <li *ngFor="let infoMessage of infoMessages">
                    {{ infoMessage }}
                </li>
            </ul>
        </treo-message>
    </div>

    <div *ngIf="errorMessages?.length" class="flex mt-3 items-center">
        <treo-message class="flex-auto" appearance="outline" [showIcon]="false" type="error">
            <ul>
                <li *ngFor="let errorMessage of errorMessages">
                    {{ errorMessage }}
                </li>
            </ul>
        </treo-message>
    </div>

</app-dialog-layout>
