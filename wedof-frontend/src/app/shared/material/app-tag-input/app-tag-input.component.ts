import {Component, ElementRef, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild} from '@angular/core';
import {AbstractControl, FormControl, FormGroup} from '@angular/forms';
import {COMMA, ENTER} from '@angular/cdk/keycodes';
import {MatChipInputEvent} from '@angular/material/chips';
import {MatAutocompleteSelectedEvent} from '@angular/material/autocomplete';
import {debounceTime, distinctUntilChanged, switchMap, takeUntil, tap} from 'rxjs/operators';
import {TagService} from '../../api/services/tag.service';
import {Subject} from 'rxjs';
import {TranslateService} from '@ngx-translate/core';


@Component({
    selector: 'app-tag-input',
    templateUrl: './app-tag-input.component.html',
    styleUrls: ['./app-tag-input.component.scss']
})
export class AppTagInputComponent implements OnInit, OnDestroy {

    @Input() placeholder: string;
    @Input() control: AbstractControl;
    @Input() label?: string;
    @Input() hideLabel?: boolean;
    @Input() selectable = true;
    @Input() removable = true;
    @Input() separatorKeysCodes: number[];
    @Input() creatingAvailable: boolean;
    @Input() tooltip?: string;
    @Input() labelClass?: string;
    @Input() clearValueIcon = false;
    @Output() clearValue?: EventEmitter<any> = new EventEmitter<any>();

    CREATE_TAG = this._translateService.instant('common.tags.create');
    filteredTags: string[];
    searchedTag: string = null;
    tagFormGroup: FormGroup = new FormGroup({
        newTagCtrl: new FormControl('')
    });

    @ViewChild('newTagInput') newTagInput: ElementRef<HTMLInputElement>;

    protected _unsubscribeAll = new Subject<void>();

    constructor(private _tagService: TagService, private _translateService: TranslateService) {
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    ngOnInit(): void {
            this.tagFormGroup.get('newTagCtrl').valueChanges.pipe(
                debounceTime(150),
                distinctUntilChanged(),
                takeUntil(this._unsubscribeAll),
                tap((searchedTag: string | null) => {
                    this.searchedTag = searchedTag?.toLowerCase().trim() ?? '';
                }),
                switchMap((searchedTag: string | null) => {
                    return this._tagService.listTags(searchedTag);
                }),
            ).subscribe(serverTags => {
                const availableServerTags = serverTags.map(serverTag => serverTag.toLowerCase().trim()).filter(serverTag => {
                    return !this.tagAlreadySet(serverTag);
                });
                if (this.searchedTag) {
                    const tagAlreadyExists = availableServerTags.findIndex(availableServerTag => this.searchedTag === availableServerTag) !== -1
                        || this.tagAlreadySet(this.searchedTag);
                    if (!tagAlreadyExists && this.creatingAvailable) {
                        availableServerTags.unshift(this.CREATE_TAG + ' ' + this.searchedTag);
                    }
                }
                this.filteredTags = availableServerTags;
            });
            this.separatorKeysCodes = this.creatingAvailable ? [COMMA, ENTER] : null;
    }

    tagAlreadySet(tag: string): boolean {
        return this.control.value.findIndex(item => tag.toLowerCase().trim() === item.toLowerCase().trim()) !== -1;
    }

    add(event: MatChipInputEvent): void {
        if (!this.tagAlreadySet(event.value)) {
            event.input.value = ''; // Required because of autocomplete bug
            this.internalAddTag(event.value.toLowerCase().trim());
        }
    }

    selected(event: MatAutocompleteSelectedEvent): void {
        this.internalAddTag(event.option.viewValue.toLowerCase().trim());
        this.newTagInput.nativeElement.value = ''; // Required because of autocomplete bug
    }

    remove(tag: string): void {
        const tagIndex = this.control.value.indexOf(tag);
        if (tagIndex >= 0) {
            this.control.value.splice(tagIndex, 1);
            this.control.markAsDirty();
        }
        if (this.clearValueIcon && this.control.value.length === 0) {
            this.clearValue.emit();
        }
    }

    internalAddTag(newTag): void {
        newTag = newTag.replace(this.CREATE_TAG.toLowerCase(), '').trim();
        if (newTag) {
            this.control.value.push(newTag.trim());
            this.control.markAsDirty();
        }
        this.tagFormGroup.get('newTagCtrl').setValue(null);
        this.searchedTag = '';
        this.filteredTags = [];
    }
}
