<mat-form-field
    [formGroup]="tagFormGroup" class="w-full">
    <mat-label [class]="labelClass" *ngIf="label && !hideLabel">{{label | translate}}</mat-label>
    <mat-chip-list #tagsList class="p-1">
        <mat-chip
            *ngFor="let tag of control.value"
            [selectable]="selectable"
            [removable]="removable"
            (removed)="remove(tag)">
            {{tag}}
            <mat-icon matChipRemove *ngIf="removable">cancel</mat-icon>
        </mat-chip>
        <input
            #newTagInput
            [placeholder]="placeholder | translate"
            formControlName="newTagCtrl"
            [matAutocomplete]="auto"
            [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
            [matChipInputFor]="tagsList"
            minlength="2" (matChipInputTokenEnd)="add($event)"
            matInput type="text">
    </mat-chip-list>
    <mat-autocomplete #auto="matAutocomplete" (optionSelected)="selected($event)">
        <mat-option *ngFor="let tag of filteredTags" [value]="tag">
            {{tag}}
        </mat-option>
    </mat-autocomplete>
    <mat-icon
        *ngIf="tooltip"
        [matTooltipPosition]="'above'"
        [matTooltip]="tooltip | translate"
        matSuffix>help_outline
    </mat-icon>
    <button type="button" *ngIf="clearValueIcon && control.value?.length " mat-button matSuffix
            mat-icon-button aria-label="Clear" (click)="clearValue.emit()">
        <mat-icon svgIcon="close"></mat-icon>
    </button>
</mat-form-field>
