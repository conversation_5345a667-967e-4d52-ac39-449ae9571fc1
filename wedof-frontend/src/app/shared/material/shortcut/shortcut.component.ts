import {
    <PERSON>mpo<PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    EventEmitter,
    Input,
    On<PERSON>estroy,
    OnInit,
    Output
} from '@angular/core';
import {Router} from '@angular/router';
import {Subject} from 'rxjs';
import {takeUntil} from 'rxjs/operators';


@Component({
    selector: 'app-shortcut',
    templateUrl: './shortcut.component.html',
    styleUrls: ['./shortcut.component.scss']
})
export class ShortcutComponent implements OnInit, OnDestroy, DoCheck {

    active: boolean;

    private _unsubscribeAll = new Subject<void>();
    @Input() title: string;
    @Input() icon: string;
    @Input() target: string;
    @Output() eventScrolling: EventEmitter<boolean> = new EventEmitter<boolean>();

    constructor(private _router: Router) {
    }

    scrollTo(target: string): void {
        this.eventScrolling.emit(true);
        document.getElementById(target).scrollIntoView({
            behavior: 'smooth',
            block: 'start',
            inline: 'end'
        });

        // Hack if no scrolling apply
        setTimeout(() => {
            this.eventScrolling.emit(false);
        }, 1000);
    }

    getAnchor(): string {
        const urlWithoutQueryParams = window.location.href.split('?')[0];
        return (urlWithoutQueryParams.split('/').reverse())[0];
    }

    checkIsActive(): void {
        this.active = this.target === this.getAnchor();
    }

    ngOnInit(): void {
        this._router.events.pipe(takeUntil(this._unsubscribeAll)).subscribe((event) => {
            this.checkIsActive();
        });
    }

    ngDoCheck(): void {
        this.checkIsActive();
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }
}
