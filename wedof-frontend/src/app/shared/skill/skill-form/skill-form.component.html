<app-dialog-layout
    [title]="dialogData.title | translate"
    [actions]="actions"
    (dialogClose)="closeModal()">

    <form [formGroup]="formGroup" class="flex flex-col">
        <app-form-fields class="grid grid-cols-6 gap-2"
                         formGroupName="skillForm"
                         [entity]="dialogData.skill ? skill : newSkill"
                         [appFormFieldsData]="appFormFieldsData"
                         [formGroup]="formGroup"></app-form-fields>

        <div *ngIf="errorMessages?.length" class="flex items-center">
            <treo-message class="flex-auto" appearance="outline" [showIcon]="false" type="error">
                <ul>
                    <li *ngFor="let errorMessage of errorMessages">
                        {{ errorMessage }}
                    </li>
                </ul>
            </treo-message>
        </div>
        <ng-template #actions>
            <button type="submit" class="flex align-center" mat-flat-button color="primary"
                    (click)="dialogData.skill ? update() : create()"
                    [disabled]="loading || formGroup.invalid || !formGroup.dirty">
                <mat-progress-spinner class="mr-4" *ngIf="loading" [diameter]="24"
                                      mode="indeterminate"></mat-progress-spinner>
                <ng-container
                    *ngIf="!loading">{{ (dialogData.skill ? 'common.actions.update' : 'common.actions.create') | translate}}</ng-container>
            </button>
        </ng-template>
    </form>
</app-dialog-layout>
