import {Component, Inject, OnInit} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material/dialog';
import {Skill, SkillBody} from '../../api/models/skill';
import {FormBuilder, FormGroup} from '@angular/forms';
import {AppFormFieldData} from '../../material/app-form-field/app-form-field.component';
import {SkillService} from '../../api/services/skill.service';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../errors/errors.types';

@Component({
    selector: 'app-skill-form',
    templateUrl: './skill-form.component.html',
    styleUrls: ['./skill-form.component.scss']
})
export class SkillFormComponent implements OnInit {

    skill: Skill;
    newSkill: Skill;
    loading = false;
    formGroup: FormGroup;
    createParentSkill: boolean;
    errorMessages: string[] = [];
    appFormFieldsData: AppFormFieldData[];

    constructor(public dialogRef: MatDialogRef<SkillFormComponent>,
                private _skillService: SkillService,
                private _formBuilder: FormBuilder,
                @Inject(MAT_DIALOG_DATA) public dialogData: {
                    skill?: Skill,
                    title: string,
                    certifInfo: string
                    parentSkill?: Skill,
                    nextCode?: string,
                    createParentSkill?: boolean
                }
    ) {
    }

    ngOnInit(): void {
        const skill = this.dialogData.skill ?? null;
        this.createParentSkill = this.dialogData.createParentSkill ?? false;
        this.formGroup = this._formBuilder.group({
            skillForm: this._formBuilder.group({})
        });
        const appFormFieldsData: AppFormFieldData[] = [
            {
                controlName: 'parentSkill',
                disabled: true,
                removed: !this.dialogData.parentSkill,
                value: this.dialogData.parentSkill?.fullCode + ' ' + this.dialogData.parentSkill?.label,
                label: 'private.common.skills.form.skillSet',
                type: 'text',
                colSpan: 3
            },
            {
                controlName: 'fullCode',
                disabled: true,
                removed: !skill,
                value: skill?.fullCode ?? null,
                label: 'private.common.skills.form.code',
                type: 'text',
                colSpan: 3
            },
            {
                controlName: 'label',
                required: true,
                value: skill?.label ?? null,
                prefix: skill ? null : this.dialogData.nextCode,
                label: 'private.common.skills.form.label',
                type: 'text',
            },
            {
                controlName: 'description',
                value: skill?.description ?? null,
                label: 'private.common.skills.form.description',
                type: 'textarea',
                maxLength: 5000
            },
            {
                controlName: 'modalities',
                value: skill?.modalities ?? null,
                label: 'private.common.skills.form.modalities',
                type: 'textarea',
                maxLength: 5000
            },
        ];
        this.appFormFieldsData = appFormFieldsData.filter(value => value != null);
    }

    closeModal(): void {
        this.dialogRef.close();
    }

    update(): void {
        this.loading = true;
        this.errorMessages = [];
        const skillValue = this.formGroup.getRawValue().skillForm;
        const updateBody: SkillBody = {
            label: skillValue.label,
            description: skillValue.description,
            modalities: skillValue.modalities
        };
        if (this.dialogData.parentSkill) {
            updateBody.parentSkillId = this.dialogData.parentSkill.id;
        }
        this._skillService.update(this.dialogData.skill.id, updateBody).subscribe({
            next: (skillUpdated) => {
                this.loading = false;
                this.dialogRef.close({data: skillUpdated});
            },
            error: (httpErrorResponse: HttpErrorResponse) => {
                this.loading = false;
                this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
            }
        });
    }

    create(): void {
        this.loading = true;
        this.errorMessages = [];
        const skillValue = this.formGroup.getRawValue().skillForm;
        const createdBody: SkillBody = {
            certifInfo: this.dialogData.certifInfo,
            label: skillValue.label,
            description: skillValue.description,
            modalities: skillValue.modalities,
            createParentSkill: this.createParentSkill
        };
        if (this.dialogData.parentSkill) {
            createdBody.parentSkillId = this.dialogData.parentSkill.id;
        }
        this._skillService.create(createdBody).subscribe({
            next: (skillCreated) => {
                this.loading = false;
                this.dialogRef.close({data: skillCreated});
            },
            error: (httpErrorResponse: HttpErrorResponse) => {
                this.loading = false;
                this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
            }
        });
    }
}
