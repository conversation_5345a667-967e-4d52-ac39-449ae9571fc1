import {Component, EventEmitter, Input, Output} from '@angular/core';
import {Skill} from '../../api/models/skill';

@Component({
    selector: 'app-skill-menu',
    templateUrl: './skill-menu.component.html',
    styleUrls: ['./skill-menu.component.scss']
})
export class SkillMenuComponent {

    @Input() skill: Skill;
    @Output() processedSkill: EventEmitter<{ skill: Skill}> = new EventEmitter<{ skill: Skill }>();
    @Output() deleteSkill: EventEmitter<Skill> = new EventEmitter<Skill>();

    constructor() {}

    update(): void {
        this.processedSkill.emit({skill: this.skill});
    }

    delete(): void {
        this.deleteSkill.emit(this.skill);
    }

}
