import { Component, Input, OnChanges } from '@angular/core';
import { ApexOptions } from 'ng-apexcharts';

@Component({
  selector: 'app-donut-chart',
  templateUrl: './donut-chart.component.html',
  styleUrls: ['./donut-chart.component.scss']
})
export class DonutChartComponent implements OnChanges {

  @Input() title: string;
  @Input() subTitle: string;
  @Input() data;
  options: ApexOptions;
  
  constructor() { }

  ngOnChanges(): void {
    this.options = {
        chart      : {
            animations: {
                speed           : 400,
                animateGradually: {
                    enabled: false
                }
            },
            fontFamily: 'inherit',
            foreColor : 'inherit',
            height    : '100%',
            type      : 'donut',
            sparkline : {
                enabled: true
            }
        },
        colors     : ['#3182CE', '#63B3ED'],
        labels     : this.data?.labels,
        plotOptions: {
            pie: {
                expandOnClick: false,
                donut        : {
                    size: '70%'
                }
            }
        },
        series     : this.data?.series,
        states     : {
            hover : {
                filter: {
                    type: 'none'
                }
            },
            active: {
                filter: {
                    type: 'none'
                }
            }
        },
        tooltip    : {
            enabled        : true,
            fillSeriesColor: false,
            theme          : 'dark',
            custom         : ({seriesIndex, w}) => {
                return `<div class="flex items-center h-8 min-h-8 max-h-8 px-3">
                            <div class="w-3 h-3 rounded-full" style="background-color: ${w.config.colors[seriesIndex]};"></div>
                            <div class="ml-2 text-md leading-none">${w.config.labels[seriesIndex]}:</div>
                            <div class="ml-2 text-md font-bold leading-none">${w.config.series[seriesIndex]}%</div>
                        </div>`;
            }
        }
    };
  }

}
