<div class="flex items-center justify-between">
    <div class="flex flex-col">
        <div class="font-bold text-md text-secondary uppercase tracking-wider">
            {{ title }}
        </div>
        <div class="text-sm text-hint font-medium">
            {{ subTitle }}
        </div>
    </div>
</div>
<div class="flex items-center h-20 mt-auto">
    <div class="flex flex-col">
        <div class="font-semibold text-5xl tracking-tighter leading-tight">{{data?.amount | number:'1.0-0'}}</div>
        <div class="flex items-center" [ngClass]="{'text-red': data?.variation < 0, 'text-green': data?.variation > 0}">
            <mat-icon [ngClass]="{'text-red': data?.variation < 0, 'text-green': data?.variation > 0}" class="mr-1"
                      svgIcon="{{ data?.variation < 0 ? 'trending_down' : 'trending_up'}}"></mat-icon>
            <div class="font-medium text-sm text-secondary leading-none whitespace-no-wrap">
                <span>{{data?.variation}}%</span>
            </div>
        </div>
    </div>
    <div class="flex flex-col flex-auto h-full ml-6">
        <apx-chart
            *ngIf="data"
            class="flex-auto w-full h-full"
            [chart]="options.chart"
            [colors]="options.colors"
            [series]="options.series"
            [stroke]="options.stroke"
            [tooltip]="options.tooltip"
            [xaxis]="options.xaxis"
            [yaxis]="options.yaxis"></apx-chart>
    </div>
</div>
