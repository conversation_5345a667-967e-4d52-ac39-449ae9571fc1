import {Component, Input, OnChanges} from '@angular/core';
import {ApexOptions} from 'ng-apexcharts';

@Component({
    selector: 'app-indicator',
    templateUrl: './indicator.component.html',
    styleUrls: ['./indicator.component.scss']
})
export class IndicatorComponent implements OnChanges {

    @Input() title: string;
    @Input() serieTitle: string;
    @Input() subTitle: string;
    @Input() data;
    options: ApexOptions;

    constructor() {
    }

    ngOnChanges(): void {

        this.options = {
            chart: {
                animations: {
                    enabled: false
                },
                fontFamily: 'inherit',
                foreColor: 'inherit',
                height: '100%',
                type: 'line',
                sparkline: {
                    enabled: true
                }
            },
            colors: ['#8DA2FB'],
            series: [
                {
                    name: this.serieTitle ?? this.title,
                    data: this.data?.values
                }
            ],
            stroke: {
                curve: 'smooth'
            },
            tooltip: {
                theme: 'dark'
            },
            xaxis: {
                type: 'category',
                categories: this.data?.labels
            },
            yaxis: {
                labels: {
                    formatter: (val) => {
                        return val.toString();
                    }
                }
            }
        };
    }

}
