import { AfterViewInit, Component, ElementRef, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { defaultsDeep } from 'lodash';
import { ChartData } from '../chart-data';
import { ChartOptions, DEFAULT_CHART_OPTIONS } from '../chart-options';

@Component({
  selector: 'app-line-chart',
  templateUrl: './line-chart.component.html',
  styleUrls: ['./line-chart.component.scss']
})
export class LineChartComponent implements AfterViewInit, OnChanges {
  @Input() data: ChartData[];
  @Input() title: ChartData[];
  options: Partial<ChartOptions>;

  constructor(private _element: ElementRef<HTMLElement>) { }

  ngAfterViewInit(): void {
    this.options = defaultsDeep({
      chart: {
        height: this._element.nativeElement.clientHeight,
        type: 'line'
      },
      series: [
        {
          name: '',
          data: this.data.map(r => r.value)
        }
      ],
      title: {
        text: this.title,
      },
      xaxis: {
        categories: this.data.map(r => r.label),
      }
    }, DEFAULT_CHART_OPTIONS);
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (!this.options) {
      return;
    }
    if (changes.title) {
      this.options.series = this.options.series.map(serie => ({
        ...serie,
        name: changes.title.currentValue
      }));
      this.options.title = {
        ...this.options.title,
        text: changes.title.currentValue
      };
    }
    if (changes.data) {
      this.options.series = this.options.series.map(serie => ({
        ...serie,
        data: changes.data.currentValue.map(r => r.value)
      }));
      this.options.xaxis = {
        ...this.options.xaxis,
        categories: changes.data.currentValue.map(r => r.label)
      };
    }
  }

}
