import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { NgApexchartsModule } from 'ng-apexcharts';
import { MaterialModule } from '../material/material.module';
import { LineChartComponent } from './line-chart/line-chart.component';
import { IndicatorComponent } from './indicator/indicator.component';
import { TableChartComponent } from './table-chart/table-chart.component';
import { DonutChartComponent } from './donut-chart/donut-chart.component';

@NgModule({
  declarations: [
    LineChartComponent,
    IndicatorComponent,
    TableChartComponent,
    DonutChartComponent
  ],
  imports: [
    CommonModule,
    NgApexchartsModule,
    MaterialModule
  ],
  exports: [
    LineChartComponent,
    IndicatorComponent,
    TableChartComponent,
    DonutChartComponent
  ]
})
export class ChartModule { }
