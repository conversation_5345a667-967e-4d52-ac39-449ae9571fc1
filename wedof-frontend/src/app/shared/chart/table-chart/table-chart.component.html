<div class="flex flex-col p-4 pl-6">
    <div class="flex items-center justify-between">
        <div class="font-bold text-md text-secondary uppercase tracking-wider">
          {{ title }}
        </div>
    </div>
    <div class="mt-6 font-semibold text-2xl leading-tight">
        {{ subTitle }}
    </div>
</div>
<div class="flex flex-auto items-center px-6">
    <apx-chart
      *ngIf="data"
      class="w-full h-3"
      [chart]="options.chart"
      [colors]="options.colors"
      [grid]="options.grid"
      [plotOptions]="options.plotOptions"
      [series]="options.series"
      [states]="options.states"
      [tooltip]="options.tooltip"
      [xaxis]="options.xaxis"
      [yaxis]="options.yaxis"></apx-chart>
</div>
<div class="flex flex-col flex-auto justify-end text-md px-6 pb-3">
    <div class="flex items-center justify-between py-3 border-b last:border-b-0"
         *ngFor="let dataset of data?.series; let i = index">
        <div class="flex items-center lt-md:w-1/3 gt-md:w-3/5">
            <div class="flex-shrink-0 w-2 h-2 mr-3 rounded-full"
                 [style.backgroundColor]="options.colors[i]"></div>
            <div class="truncate">{{dataset.name}}</div>
        </div>
        <div class="lt-md:w-1/3 gt-md:w-1/5 font-medium text-right">{{dataset.data[0] | number:'1.0-0'}}</div>
        <div
            class="lt-md:w-1/3 gt-md:w-1/5 text-right text-secondary">{{dataset.data[0] * 100 / data.amount | number:'1.0-0'}}
            %
        </div>
    </div>
</div>
