import { Component, Input, OnChanges } from '@angular/core';
import { ApexOptions } from 'ng-apexcharts';

@Component({
  selector: 'app-table-chart',
  templateUrl: './table-chart.component.html',
  styleUrls: ['./table-chart.component.scss']
})
export class TableChartComponent implements OnChanges {

  @Input() title: string;
  @Input() subTitle: string;
  @Input() data;
  options: ApexOptions;
  
  constructor() { }

  ngOnChanges(): void {
    this.options = {
      chart: {
          animations: {
              speed           : 400,
              animateGradually: {
                  enabled: false
              }
          },
          fontFamily: 'inherit',
          foreColor : 'inherit',
          height    : '100%',
          type      : 'bar',
          stacked   : true,
          stackType : '100%',
          sparkline : {
              enabled: true
          }
      },
      colors     : [
          '#6B46C1',
          '#805AD5',
          '#9F7AEA',
          '#B794F4'
      ],
      plotOptions: {
          bar: {
              barHeight : '100%',
              horizontal: true
          }
      },
      series     : this.data?.series,
      states     : {
          hover: {
              filter: {
                  type: 'none'
              }
          }
      },
      tooltip    : {
          theme : 'dark',
          x     : {
              show: false
          },
          custom: ({seriesIndex, w}) => {
              return `<div class="flex items-center h-8 min-h-8 max-h-8 px-3">
                          <div class="w-3 h-3 rounded-full" style="background-color: ${w.config.colors[seriesIndex]};"></div>
                          <div class="ml-2 text-md leading-none">${w.config.series[seriesIndex].name}:</div>
                          <div class="ml-2 text-md font-bold leading-none">${w.config.series[seriesIndex].data[0]}</div>
                      </div>`;
          }
      },
      yaxis      : {
          labels: {
              formatter: (val) => {
                  return val.toString();
              }
          }
      }
  };
  }

}
