<mat-card class="flex-col mt-3 p-5 pb-0 pt-3 border shadow-none sm w-full " [ngClass]="{'card-loading':cardLoading}">
    <div class="flex items-center mb-2">
        <mat-icon class="mr-3 card-loading-show text-4xl"
                  [color]="includes(errorStates, certificationFolder?.state) ? 'warn' : 'primary'"
                  title="{{ 'private.certification.folders.actions.' + certificationFolder?.state | translate}}">{{ certificationFolder?.state | certificationFolderStateToIcon}}</mat-icon>
        <div>
            <span
                class="text-xl font-semibold card-loading-show">{{ 'private.common.certificationFolder.title' | translate }}</span>
            <div class="text-secondary text-md truncate card-loading-show"
                 title="{{ 'private.certification.folders.actions.' + certificationFolder?.state | translate}}">
                {{ 'private.certification.folders.actions.' + certificationFolder?.state | translate}} - <span
                title="{{ 'private.common.certificationFolder.stateLastUpdate' | translate}}">
                    {{ certificationFolder?.stateLastUpdate | date: 'dd/MM/yyyy' }}
                </span><span
                *ngIf="(certificationFolder.state == states.REGISTERED || certificationFolder.state == states.TO_REGISTER) && certificationFolder.inTraining"
                title="{{ 'private.certification.folders.actions.inTrainingStarted' | translate}} : {{ certificationFolder.history.inTrainingStartedDate | date: 'dd/MM/yyyy' }}">
                - {{ 'private.certification.folders.actions.inTrainingStarted' | translate}}
                </span>
                <span
                    *ngIf="(certificationFolder.state == states.REGISTERED || certificationFolder.state == states.TO_REGISTER) && certificationFolder.history.inTrainingEndedDate"
                    title="{{ 'private.certification.folders.actions.inTrainingEnded' | translate}} : {{ certificationFolder.history.inTrainingEndedDate | date: 'dd/MM/yyyy' }}">
                    - {{ 'private.certification.folders.actions.inTrainingEnded' | translate}}
                </span>
            </div>
        </div>
    </div>
    <div class="flex flex-col" *ngIf="!panelOpenState || cardLoading ">
        <div class="flex">
            <div class="w-2/3">
                <app-form-field-static class="font-bold col-span-6"
                                       [value]="certificationName"
                                       icon="fact_check"
                                       [href]="certificationLink"
                                       type="url">
                </app-form-field-static>
                <app-form-field-static class="font-bold" icon="business" type="text" [value]="certifierName">
                </app-form-field-static>
                <app-form-field-static type="text" icon="event" hideLabel=true
                                       *ngIf="certificationFolder?.examinationDate"
                                       label="private.common.certificationFolder.examinationDate"
                                       [value]="'private.common.certificationFolder.date' | translate: {
                date: certificationFolder?.examinationDate | date : 'dd/MM/yyyy à hh:mm'
            }"></app-form-field-static>

                <app-form-field-static icon="loyalty" type="text" *ngIf="certificationFolder?.detailedResult"
                                       [value]="certificationFolder?.detailedResult">
                </app-form-field-static>

                <app-form-field-static icon="location_on" type="text" *ngIf="examinationInformations &&
            (certificationFolder?.state === states.SUCCESS ||  certificationFolder?.state === states.FAILED || certificationFolder?.state === states.TO_CONTROL
            || certificationFolder?.state === states.TO_RETAKE)"
                                       [value]="examinationInformations">
                </app-form-field-static>
                <app-form-field-static icon="spoke" type="text" *ngIf="skillSets"
                                       [value]="skillSets">
                </app-form-field-static>
            </div>
            <a class="w-1/3" *ngIf="certificationFolder.badgeAssertion && badgeAssertionImageUrl" target="_blank" [href]="certificationFolder.badgeAssertion">
                <img [src]="badgeAssertionImageUrl" alt="badge"
                     style="width: 50%; margin-left: auto; margin-right: auto">
            </a>
        </div>

        <button type="button" class="flex justify-center mt-2 -mx-5 pt-2 pb-2 open-panel card-loading-hidden"
                (click)="openPanel()">
            <mat-icon svgIcon="keyboard_arrow_down"></mat-icon>
        </button>
    </div>
    <form [formGroup]="formGroup" class="flex flex-col" *ngIf="panelOpenState && !cardLoading">

        <app-form-fields class="grid grid-cols-6 gap-2"
                         formGroupName="certificationFolder"
                         [entity]="certificationFolder"
                         [appFormFieldsData]="appFormFieldsData"
                         [formGroup]="formGroup">
        </app-form-fields>

        <button type="button" class="flex justify-center -mx-5 pt-2 pb-2 close-panel" (click)="closePanel()">
            <mat-icon svgIcon="keyboard_arrow_up"></mat-icon>
        </button>
    </form>

</mat-card>
