import {Component, Input, OnInit} from '@angular/core';
import {
    CertificationFolder,
    CertificationFolderErrorStates,
    CertificationFolderStates,
    CertificationFolderType,
    EuropeanLanguageLevel
} from '../../api/models/certification-folder';
import {FormBuilder, FormGroup} from '@angular/forms';
import {Certification, CertificationExaminationType, CertificationTypes} from '../../api/models/certification';
import {AppFormFieldData} from '../../material/app-form-field/app-form-field.component';
import moment from 'moment/moment';
import {TranslateService} from '@ngx-translate/core';
import {enumFromString} from '../../utils/object-utils';
import {lowerCase, upperFirst} from 'lodash-es';
import {displaySkillSetTitle} from '../../api/models/skill';
import {CertificationFolderService} from '../../api/services/certification-folder.service';

@Component({
    selector: 'app-certification-folder-card-attendee',
    templateUrl: './certification-folder-card-attendee.component.html',
    styleUrls: ['./certification-folder-card-attendee.component.scss']
})
export class CertificationFolderCardAttendeeComponent implements OnInit {

    cardLoading = true;
    panelOpenState = false;
    certificationName: string;
    certificationLink: string;
    examinationInformations: string;
    examinationDates: string;
    certificationFolderGradePass: string;
    certificationFolderType: string;
    certifierName: string;

    formGroup: FormGroup;
    states = CertificationFolderStates;
    appFormFieldsData: AppFormFieldData[];
    errorStates = CertificationFolderErrorStates;
    skillSets = null;
    badgeAssertionImageUrl: string;

    @Input() certificationFolder: CertificationFolder;
    @Input() certification: Certification;

    constructor(
        private _fb: FormBuilder,
        private _translateService: TranslateService,
        private _certificationFolderService: CertificationFolderService
    ) {
    }

    ngOnInit(): void {
        if (this.certificationFolder.badgeAssertion) {
            this._certificationFolderService.getBadgeAssertionData(this.certificationFolder.externalId).subscribe(badgeData => {
                this.badgeAssertionImageUrl = badgeData ? badgeData['imageUrl'] : null;
                this.initForm(this.certificationFolder);
            });
        } else {
            this.initForm(this.certificationFolder);
        }
    }

    openPanel(): void {
        this.panelOpenState = true;
    }

    closePanel(): void {
        this.panelOpenState = false;
    }

    initForm(certificationFolder: CertificationFolder): void {
        if (!certificationFolder) {
            return;
        }
        this.cardLoading = true;
        this.formGroup = this._fb.group({
            certificationFolder: this._fb.group({})
        });
        const state = enumFromString(CertificationFolderStates, this.certificationFolder.state);
        const type = certificationFolder.type === CertificationFolderType.POLE_EMPLOI ? 'poleEmploi' : certificationFolder.type === CertificationFolderType.CERTIFIE ? 'certifie' :
            certificationFolder.type === CertificationFolderType.OF ? 'of' : certificationFolder.type === CertificationFolderType.EMPLOYEUR ? 'employeur' : 'autre';
        const isLanguageCertification = this.certification.nsf?.find(codeNsf => codeNsf.code === '136');
        const isCertificationResult = [CertificationFolderStates.SUCCESS, CertificationFolderStates.FAILED, CertificationFolderStates.TO_RETAKE].indexOf(state) >= 0;

        this.examinationInformations = certificationFolder.examinationType && certificationFolder.examinationPlace ?
            certificationFolder.examinationType === CertificationExaminationType.A_DISTANCE ?
                this._translateService.instant('private.common.certification.examinationType.A_DISTANCE') :
                this._translateService.instant('private.common.certification.examinationType.' + certificationFolder.examinationType) +
                ' (' + certificationFolder.examinationPlace + ')' :
            certificationFolder.examinationType && !certificationFolder.examinationPlace ?
                this._translateService.instant('private.common.certification.examinationType.' + certificationFolder.examinationType) :
                !certificationFolder.examinationType && certificationFolder.examinationPlace && certificationFolder.examinationPlace;

        this.certificationFolderGradePass = certificationFolder.gradePass &&
            this._translateService.instant('private.common.certificationFolder.gradesPass.' + certificationFolder.gradePass);
        this.certificationFolderType = certificationFolder.type === CertificationFolderType.POLE_EMPLOI ?
            this._translateService.instant('private.certification.folders.createFolder.form.type.types.poleEmploi') : certificationFolder.type ?
                this._translateService.instant('private.certification.folders.createFolder.form.type.types.' + certificationFolder.type.toLowerCase()) : null;

        this.certifierName = this.certificationFolder._links.certifier.name;
        this.certificationLink = this.certification.link ?? ([CertificationTypes.RS, CertificationTypes.RNCP].includes(this.certification.type)) ? 'https://www.francecompetences.fr/recherche/' + this.certification.type.toLowerCase() + '/' + this.certification.code : '';
        this.certificationName = (this.certification.externalId ? this.certification.externalId + ' - ' : '') + this.certification.name;
        if (certificationFolder.examinationDate) {
            this.examinationDates = !certificationFolder.examinationEndDate || certificationFolder.examinationDate === certificationFolder.examinationEndDate ?
                this._translateService.instant('private.common.dates.fromToSameDay', {date: moment(certificationFolder.examinationDate).format('DD/MM/YYYY')}) :
                this._translateService.instant('private.common.dates.fromTo', {
                        startDate: moment(certificationFolder.examinationDate).format('DD/MM/YYYY'),
                        endDate: moment(certificationFolder.examinationEndDate).format('DD/MM/YYYY')
                    }
                );
        } else {
            this.examinationDates = null;
        }

        if (this.certificationFolder._links.certification.allowPartialSkillSets) {
            this.skillSets = this.certificationFolder.fullCertification && this.certificationFolder.skillSets?.length > 0 ? 'Tous les blocs de compétences' :
                this.certificationFolder.skillSets.map((skill) => displaySkillSetTitle(skill)).join(', ');
        }

        this.appFormFieldsData = [
            {
                controlName: 'type',
                disabled: true,
                label: 'private.certification.folders.createFolder.form.type.label',
                type: 'text',
                icon: 'folder',
                value: certificationFolder.type && this._translateService.instant('private.certification.folders.createFolder.form.type.types.' + type)
            },
            {
                controlName: 'certifier',
                disabled: true,
                type: 'text',
                label: 'private.common.certificationFolder.certifier',
                icon: 'business',
                value: this.certifierName
            },
            {
                controlName: 'enrollmentDate',
                disabled: true,
                type: 'date',
                label: 'private.common.certificationFolder.enrollmentDate',
                icon: 'event'
            },
            {
                controlName: 'examinationDates',
                disabled: true,
                type: 'text',
                label: 'private.common.certificationFolder.examinationDates',
                icon: 'event',
                value: this.examinationDates
            },
            {
                controlName: 'examinationInfos',
                disabled: true,
                value: this.examinationInformations,
                type: 'text',
                icon: 'school',
                label: 'private.common.certificationFolder.examinationDetails'
            },
            {
                controlName: 'detailedResult',
                removed: !isCertificationResult,
                disabled: true,
                type: 'textarea',
                label: 'private.common.certificationFolder.detailedResult',
                icon: 'loyalty'
            },
            {
                controlName: 'europeanLanguageLevel',
                value: certificationFolder.europeanLanguageLevel === EuropeanLanguageLevel.INSUFFISANT ?
                    upperFirst(lowerCase(EuropeanLanguageLevel.INSUFFISANT)) : certificationFolder.europeanLanguageLevel,
                removed: !isLanguageCertification || !isCertificationResult,
                disabled: true,
                type: 'text',
                label: 'private.common.certificationFolder.europeanLanguageLevel',
                icon: 'grading'
            },
            {
                controlName: 'gradePass',
                removed: [CertificationFolderStates.SUCCESS].indexOf(state) === -1,
                disabled: true,
                type: 'text',
                label: 'private.common.certificationFolder.gradePassLabel',
                icon: 'school',
                value: this.certificationFolderGradePass,
            },
            {
                controlName: 'issueDate',
                removed: [CertificationFolderStates.SUCCESS].indexOf(state) === -1,
                disabled: true,
                type: 'date',
                label: 'private.common.certificationFolder.issueDate',
                icon: 'event'
            },
            {
                controlName: 'digitalProofLink',
                removed: [CertificationFolderStates.SUCCESS].indexOf(state) === -1,
                disabled: true,
                type: 'url',
                label: 'private.common.certificationFolder.digitalProofLink',
                icon: 'badge',
                copy: true
            },
            {
                controlName: 'expirationDate',
                removed: [CertificationFolderStates.SUCCESS].indexOf(state) === -1 || this.certificationFolder.expirationDate == null,
                disabled: true,
                type: 'date',
                label: 'private.common.certificationFolder.expirationDate',
                icon: 'event'
            },
            {
                controlName: 'skillSets',
                removed: !this.skillSets,
                disabled: true,
                type: 'text',
                icon: 'spoke',
                label: 'private.certification.folders.createFolder.form.skillsSet.title',
                value: this.skillSets
            }
        ];
        this.cardLoading = false;
    }

    includes(canceledOrRefusedStates: any, state: CertificationFolderStates): boolean {
        return Object.values(canceledOrRefusedStates).includes(state);
    }
}
