import {Component, EventEmitter, Injector, Input, OnInit, Output, ViewChild} from '@angular/core';
import {AbstractSelectableTableComponent} from '../../material/table/abstract-selectable-table.component';
import {CertificationFolder, CertificationFolderStates} from '../../api/models/certification-folder';
import {Observable} from 'rxjs';
import {PaginatedResponse} from '../../api/services/abstract-paginated.service';
import {
    CertificationFolderHttpParams,
    CertificationFolderService
} from '../../api/services/certification-folder.service';
import {debounceTime, takeUntil, tap} from 'rxjs/operators';
import {TableFilter} from '../../material/table/table-filter/table-filter.component';
import {Certification} from '../../api/models/certification';
import {Organism} from '../../api/models/organism';
import {CertificationFolderTableColumns} from './certification-folder-table-params';
import {DataQuery} from '../../material/table/abstract-table.component';
import {CertificationFolderStateToIconPipe} from '../../pipes/certification-folder-state-to-icon.pipe';
import {CertificationFolderStateToColorPipe} from '../../pipes/certification-folder-state-to-color.pipe';
import {TableMultipleFilterComponent} from '../../material/table/table-multiple-filter/table-multiple-filter.component';
import {ActivatedRoute, Params, Router} from '@angular/router';
import {Attendee} from '../../api/models/attendee';

export interface CertificationFolderFilters {
    query?: string;
    certifInfo?: string;
    siret?: string;
    state?: string;
    period?: string;
    until?: string;
    since?: string;
    filterOnStateDate?: string;
    registrationFolderState?: string;
    registrationFolderType?: string;
    cdcState?: string;
    cdcCompliant?: boolean;
    cdcToExport?: boolean;
    cdcExcluded?: boolean;
    messageTemplate?: string;
    messageState?: string;
    survey?: string;
    skillSets?: string;
    tags?: string;
}

export interface CertificationFolderQuery extends DataQuery {
    certificationFolderExternalId: string;
    certifInfo: string;
}

@Component({
    selector: 'app-certification-folder-table',
    templateUrl: './certification-folder-table.component.html',
    styleUrls: ['./certification-folder-table.component.scss']
})
export class CertificationFolderTableComponent extends AbstractSelectableTableComponent<CertificationFolder> implements OnInit {
    @Input() certification?: Certification;
    @Input() organism?: Organism;
    @Input() displayedColumns: CertificationFolderTableColumns[];
    @Input() certificationFolderFilters$: Observable<CertificationFolderFilters>;
    @Input() certificationFolderQuery$: Observable<CertificationFolderQuery>;
    @Output() searchTag = new EventEmitter<string>();
    @Output() certificationFolderDeleted: EventEmitter<any> = new EventEmitter<any>();
    @ViewChild('stateFilterSelector') private stateTableMultipleFilterComponentState: TableMultipleFilterComponent;
    stateFilters: TableFilter[];
    states = CertificationFolderStates;
    pendingSelectAll: boolean;

    constructor(
        injector: Injector,
        private _router: Router,
        private _activatedRoute: ActivatedRoute,
        private _certificationFolderService: CertificationFolderService,
        private _certificationFolderStateToIconPipe: CertificationFolderStateToIconPipe,
        private _certificationFolderStateToColorPipe: CertificationFolderStateToColorPipe
    ) {
        super(injector);
        this.stateFilters = Object.values(this.states).map(value => ({
            label: `private.common.certificationFolder.state.${value}`,
            value: value,
            icon: this._certificationFolderStateToIconPipe.transform(value),
            color: this._certificationFolderStateToColorPipe.transform(value),
        }));
    }

    ngOnInit(): void {
        if (this.organism) {
            this.applyFilter({name: 'siret', value: this.organism.siret});
        } else {
            this.applyFilter({name: 'siret', value: 'all'});
        }
        if (this.certification) {
            this.applyFilter({name: 'certifInfo', value: this.certification.certifInfo});
        }

        this.certificationFolderFilters$?.pipe(
            takeUntil(this._unsubscribeAll),
            debounceTime(500)
        ).subscribe(filters => {
            Object.entries(filters).forEach(([paramTitle]) => {
                const valueState = filters['state'] ? filters['state'].split(',') : [];
                this.stateTableMultipleFilterComponentState.updateCurrentFiltersData(valueState);
                this.applyFilter({name: paramTitle, value: filters[paramTitle]});
            });
            this._changeDetectorRef.detectChanges();
        });
    }

    onStateFilterChange(accessStates: string[], filterName: string): void {
        if (this.certification) {
            this.applyFilter({name: 'state', value: accessStates});
        } else {
            const queryParams: Params = {};
            queryParams[filterName] = accessStates.join(',');
            this._router.navigate(
                [],
                {
                    relativeTo: this._activatedRoute,
                    queryParams: queryParams,
                    queryParamsHandling: 'merge'
                });
        }
    }

    selectAllForState(stateValue?: string): void {
        this.onStateFilterChange([stateValue], 'state');
        this.stateTableMultipleFilterComponentState.updateCurrentFiltersData(stateValue === undefined ? [] : [stateValue]);
        this.pendingSelectAll = true; // We cannot subscribe to the refresh from here, so we record that we want to select All
    }

    protected filterDataQuery(query: CertificationFolderQuery): boolean {
        return !query.certifInfo || query.certifInfo === this.certification.certifInfo;
    }

    protected findRowWithDataQuery(row: CertificationFolder, query: CertificationFolderQuery): boolean {
        return row.externalId === query.certificationFolderExternalId;
    }

    setSearchTag(tag: string): void {
        this.searchTag.emit(tag);
    }

    refreshData(event: any, pageIndex = this.paginator.pageIndex): Observable<PaginatedResponse<CertificationFolder>> {
        this.clearSelection();
        const params: CertificationFolderHttpParams = {
            ...this._filters$.value,
            order: this.sort?.direction,
            sort: this.sort?.active,
            limit: this.paginator.pageSize,
            page: pageIndex + 1
        };
        if (params.siret == null) {
            params.siret = 'all'; // Hack
        }
        return this._certificationFolderService.list(params).pipe(tap(() => {
            if (this.pendingSelectAll === true) {
                setTimeout(() => { // Doesn't work if no timeout
                    this.selectAll();
                    this.pendingSelectAll = false;
                });
            }
        }));
    }

    delete($event: CertificationFolder): void {
        this.certificationFolderDeleted.emit($event);
    }

    refreshAttendee(attendee: Attendee): void {
        this.displayedData.forEach(certificationFolder => {
            if (certificationFolder.attendee.id === attendee.id) {
                certificationFolder.attendee = attendee;
                this.refreshRow(certificationFolder);
            }
        });
    }
}
