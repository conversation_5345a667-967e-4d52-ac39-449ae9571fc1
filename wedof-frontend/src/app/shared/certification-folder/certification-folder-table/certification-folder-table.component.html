<ng-template #noData>
    <p class="bg-white p-6 cursor-auto text-center m-auto">
        {{ 'private.certification.folders.no-data' | translate }}
    </p>
</ng-template>

<app-wrapper-spinner [active]="isLoading">
    <ng-container *ngIf="isLoading || displayedData?.length || activeFilters ; else noData">
        <table class="table-fixed" mat-table [dataSource]="displayedData" matSort matSortActive="stateLastUpdate"
               matSortDisableClear
               multiTemplateDataRows
               matSortDirection="desc">
            <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
            <tr *matRowDef="let row; let rowIndex = dataIndex; columns:['selectAll'];"
                [hidden]="rowIndex > 0 || total <= paginator.pageSize || !isAllSelected()"
                mat-row></tr>
            <tr mat-row (click)="onRowClick(row)" *matRowDef="let row; columns: displayedColumns;"
                [ngClass]="{'selected':this.selectedRow?.id === row?.id}"></tr>
            <tr mat-footer-row *matFooterRowDef="['noDataForFilters']" [hidden]="isLoading || displayedData?.length">
            </tr>

            <ng-container matColumnDef="selectAll">
                <td *matCellDef="let element" [attr.colspan]="displayedColumns.length" class="text-center" mat-cell>
                    <div *ngIf="!isTotalRowsSelected">
                        <span class="text-warn font-semibold">{{ 'common.table.selection.current' | translate:{currentSelectionCount: currentSelectionCount()} }}</span>
                        <button (click)="selectAllTotalLine(); $event.stopPropagation()" class="font-semibold pr-2 underline"
                                mat-button>
                            {{ 'common.table.selection.selectAll' | translate:{total: total} }}
                        </button>
                    </div>
                    <div *ngIf="isTotalRowsSelected">
                        {{ 'common.table.selection.currentAll' | translate:{currentSelectionCount: currentSelectionCount()} }}
                        <button mat-button class="font-semibold pr-2 underline" (click)="toggleAll(); $event.stopPropagation()">
                            {{ 'common.table.selection.deselectAll' | translate }}
                        </button>
                    </div>
                </td>
            </ng-container>

            <ng-container matColumnDef="noDataForFilters">
                <td mat-footer-cell *matFooterCellDef [attr.colspan]="displayedColumns.length">
                    {{ 'common.table.filters.no-data' | translate }}
                </td>
            </ng-container>

            <ng-container matColumnDef="actions">
                <th class="text-left w-26 pl-4 border-t" mat-header-cell *matHeaderCellDef>
                    <div class="flex justify-between pr-2">
                        <div class="flex flex-row items-center cursor-pointer">
                            <mat-checkbox class="self-center checkbox-dropdown"
                                          (change)="$event ? toggleAll() : null"
                                          [checked]="(selection.hasValue() && isAllSelected()) || isTotalRowsSelected"
                                          [indeterminate]="selection.hasValue() && !isAllSelected()"
                                          [aria-label]="checkboxAllLabelKey() | translate"
                            >
                            </mat-checkbox>
                            <mat-icon class="icon-small" svgIcon="arrow_drop_down" [matMenuTriggerFor]="actionsMenu"></mat-icon>
                        </div>
                        <mat-menu #actionsMenu="matMenu" class="large-menu">
                            <button class="mat-menu-item"
                                    (click)="selectAllForState()">
                                {{ 'common.table.selection.all.select' | translate }}
                            </button>
                            <button *ngFor="let stateFilter of stateFilters" class="mat-menu-item"
                                    (click)="selectAllForState(stateFilter.value)">
                                <mat-icon class="ml-1"
                                          matPrefix
                                          [color]="stateFilter.color">{{ stateFilter.icon }}
                                </mat-icon>
                                {{ stateFilter.label | translate }}
                            </button>
                        </mat-menu>
                        <app-certification-folder-menu
                            [folders]="selection.selected"
                            (processedFolder)="refreshRow($event)"
                            (allFoldersProcessed)="clearSelection()"
                            [isTotalRowsSelected]="isTotalRowsSelected"
                            [totalFolderCount]="total"
                            [filters]="activeFilters"
                        >
                        </app-certification-folder-menu>
                    </div>
                </th>
                <td class="text-left w-26 pl-4" mat-cell *matCellDef="let folder;">
                    <div class="flex flex-direction:row">
                        <mat-checkbox (change)="$event ? toggleRow(folder) : null" (click)="$event.stopPropagation()"
                                      class="self-center"
                                      [checked]="isRowSelected(folder)" [disabled]="!isSelectable(folder)"
                                      [aria-label]="checkboxRowLabelKey(folder) | translate:{ name: folder.attendee.firstName + ' ' + folder.attendee.lastName }">
                        </mat-checkbox>
                        <app-certification-folder-menu
                            class="ml-4"
                            [folders]="[folder]"
                            (deleteFolder)="delete($event)"
                            (allFoldersProcessed)="clearSelection()"
                            (processedFolder)="refreshRow($event)">
                        </app-certification-folder-menu>
                    </div>
                </td>
            </ng-container>

            <ng-container matColumnDef="candidate">
                <th class="border-t" mat-header-cell *matHeaderCellDef>
                    {{ 'private.certification.folders.candidate' | translate }}
                </th>
                <td mat-cell *matCellDef="let folder">
                    <p class="truncate font-semibold">{{ folder.attendee.firstName | titlecase }} {{ folder.attendee.lastName | uppercase }}</p>
                    <p class="truncate text-secondary">{{ folder.attendee.email }}</p>
                </td>
            </ng-container>

            <ng-container matColumnDef="stateLastUpdate">
                <th class="border-t" mat-header-cell *matHeaderCellDef mat-sort-header>
                    <app-table-multiple-filter #stateFilterSelector
                                               (selectFilters)="onStateFilterChange($event, 'state')"
                                               [title]="'état'"
                                               [filters]="stateFilters">
                        {{ 'private.training-organism.folders.table.certificationState' | translate }}
                    </app-table-multiple-filter>
                </th>
                <td mat-cell class="truncate" *matCellDef="let folder">
                    <p>{{ ('private.common.certificationFolder.state.' + folder['state']) | translate }}</p>
                    <div class="text-secondary" title="{{ 'private.common.certificationFolder.stateLastUpdate' | translate}}">{{ folder.stateLastUpdate | date: 'dd/MM/yy' }}</div>
                </td>
            </ng-container>

            <ng-container matColumnDef="organism">
                <th class="border-t" mat-header-cell *matHeaderCellDef>
                    {{ 'private.training-organism.folders.table.organism' | translate }}
                </th>
                <td class="truncate" *matCellDef="let folder" mat-cell>
                    {{ folder._links.partner?.name }}
                </td>
            </ng-container>

            <ng-container matColumnDef="certification">
                <th class="border-t w-1/3" mat-header-cell *matHeaderCellDef>
                    {{ 'private.training-organism.folders.table.certification' | translate }}
                </th>
                <td class="truncate" *matCellDef="let folder" mat-cell>
                    {{ folder._links.certification.externalId }} - {{ folder._links.certification.name }}
                </td>
            </ng-container>

            <ng-container matColumnDef="tags">
                <th class="border-t" mat-header-cell *matHeaderCellDef>
                    {{ 'private.training-organism.folders.table.tags' | translate }}
                </th>
                <td *matCellDef="let folder" mat-cell>
                    <app-form-field-static *ngIf="folder.tags?.length"
                                           [lengthLimit]="2"
                                           [value]="folder.tags"
                                           (customClick)="setSearchTag($event)"
                                           type="tags"></app-form-field-static>
                </td>
            </ng-container>

        </table>
        <app-paginator [length]="total"
                       [pageSizeOptions]="pageSizeOptions"
                       [scrollTopOnPageChange]="true"
                       (page)="onPageEvent($event)">
        </app-paginator>
    </ng-container>
</app-wrapper-spinner>
