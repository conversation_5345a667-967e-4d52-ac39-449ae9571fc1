import {Compo<PERSON>, <PERSON><PERSON><PERSON>ter, OnDestroy, OnInit, Output, ViewChild} from '@angular/core';
import {
    CertificationFolder,
    CertificationFolderCreate,
    CertificationFolderType
} from '../../api/models/certification-folder';
import {FormBuilder, FormGroup, FormGroupDirective} from '@angular/forms';
import {AppFormFieldData, Choices} from '../../material/app-form-field/app-form-field.component';
import {CertificationFolderService} from '../../api/services/certification-folder.service';
import {CertificationService} from '../../api/services/certification.service';
import {AttendeeService} from '../../api/services/attendee.service';
import {CanDeactivateComponent} from '../../utils/can-deactivate/can-deactivate.component';
import {Attendee} from '../../api/models/attendee';
import {Certification, CertificationTypes} from '../../api/models/certification';
import {AttendeeCreateComponent} from '../../attendee/attendee-create/attendee-create.component';
import {MatDialog} from '@angular/material/dialog';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../errors/errors.types';
import {TranslateService} from '@ngx-translate/core';
import {takeUntil} from 'rxjs/operators';
import {toLower} from 'lodash-es';
import {Select} from '@ngxs/store';
import {OrganismState} from '../../api/state/organism.state';
import {Observable, of, ReplaySubject, Subject} from 'rxjs';
import {Organism} from '../../api/models/organism';
import {EntityClass} from '../../utils/enums/entity-class';
import {OrganismService} from '../../api/services/organism.service';
import {CertificationPartnerStates} from '../../api/models/certification-partner';
import {CertificationPartnerService} from '../../api/services/certification-partner.service';
import {SkillService} from '../../api/services/skill.service';
import {displaySkillSetTitle, Skill} from '../../api/models/skill';

@Component({
    selector: 'app-certification-folder-create',
    templateUrl: './certification-folder-create.component.html',
    styleUrls: ['./certification-folder-create.component.scss']
})
export class CertificationFolderCreateComponent extends CanDeactivateComponent implements OnInit, OnDestroy {

    errorMessages: string[] = [];
    loading = false;
    formGroup: FormGroup;
    newCreatedAttendee: Attendee;
    appFormFieldsData: AppFormFieldData[];
    newCertificationFolder: CertificationFolderCreate;
    type: string = null;
    currentOrganism: Organism = null;
    appFormFieldData$?: ReplaySubject<AppFormFieldData> = new ReplaySubject<AppFormFieldData>();
    allSkillSetIds: number[] = null;

    @Output() certificationFolderCreate = new EventEmitter<CertificationFolder>();
    @ViewChild('form') form: FormGroupDirective;

    @Select(OrganismState.organism) organism$: Observable<Organism>;

    private _unsubscribeAll = new Subject<void>();

    constructor(private _formBuilder: FormBuilder,
                private _certificationFolderService: CertificationFolderService,
                private _attendeeService: AttendeeService,
                private _certificationService: CertificationService,
                private _organismService: OrganismService,
                private _skillService: SkillService,
                private _certificationPartnerService: CertificationPartnerService,
                private _dialog: MatDialog,
                private _translateService: TranslateService) {
        super();
    }

    canDeactivate(): boolean {
        return !this.loading && (this.form?.submitted || !this.formGroup.dirty);
    }

    ngOnInit(): void {
        this.organism$.pipe(takeUntil(this._unsubscribeAll)).subscribe(organism => {
            this.currentOrganism = organism;
            if (organism.isTrainingOrganism) {
                this.type = 'of';
            }
        });
        this.formGroup = this._formBuilder.group({
            newCertificationFolder: this._formBuilder.group({})
        });
        const displayCertificationStatus = (enabled: boolean) => {
            return enabled ? '' : this._translateService.instant('private.certification.folders.createFolder.form.certification.archived');
        };
        let chosenCertification = null;
        const appFormFieldsData: AppFormFieldData[] = [
            {
                controlName: 'attendee',
                required: true,
                value: null,
                label: 'private.certification.folders.createFolder.form.attendee.label',
                type: 'search',
                placeholder: 'private.certification.folders.createFolder.form.attendee.attendee',
                searchNoEntriesFoundLabel: 'private.certification.folders.createFolder.form.attendee.noAttendee',
                searchMethod: (searchTerm) => {
                    return searchTerm ? this._attendeeService.find(searchTerm) : of([]);
                },
                searchResultFormatter: (attendee: Attendee) => attendee.firstName + ' ' + attendee.lastName,
                isCreateAvailable: true,
                showCreateText: 'private.certification.folders.createFolder.form.attendees.button',
                showSearchingText: 'private.certification.folders.createFolder.form.attendees.searching',
                openDialogCreate: (searchingValue?: string) => {
                    const dialogRef = this._dialog.open(AttendeeCreateComponent, {
                        disableClose: true,
                        panelClass: ['full-page-scroll-80'],
                        data: {
                            attendeeSearchValue: searchingValue,
                            newEmailCreateAttendee: this.newCreatedAttendee,
                            contextEntity: EntityClass.CERTIFICATION_FOLDER
                        }
                    });
                    dialogRef.afterClosed().subscribe(res => {
                        if (res) {
                            this.newCreatedAttendee = res.data;
                            const attendeeFormControl = this.formGroup.get('newCertificationFolder').get('attendee');
                            attendeeFormControl.setValue(this.newCreatedAttendee);
                            attendeeFormControl.markAsDirty();
                        }
                    });
                },
            },
            {
                controlName: 'certification',
                required: true,
                value: null,
                label: 'private.certification.folders.createFolder.form.certification.certification',
                type: 'infiniteSearch',
                placeholder: 'private.certification.folders.createFolder.form.certification.placeholderCertification',
                help: 'private.certification.folders.createFolder.form.certification.tooltip',
                searchNoEntriesFoundLabel: 'private.certification.folders.createFolder.form.certification.noCertification',
                searchMethodPaginated: (params) => {
                    return this._certificationService.listLite(params);
                },
                parameters: {
                    limit: 15,
                    sort: 'cpfDateEnd',
                    certifierCustomer: true,
                    certificationPartnerState: [CertificationPartnerStates.ACTIVE, CertificationPartnerStates.REVOKED, CertificationPartnerStates.SUSPENDED].join(','),
                    order: 'desc'
                },
                searchResultFormatter: (certification: Certification) => certification.externalId + ' - ' + certification.name + displayCertificationStatus(certification.enabled),
                colSpan: 3,
                change: (controlName, newValue, appFormFieldsDatas) => {
                    const appFormFieldPartner = appFormFieldsDatas.find(field => field.controlName === 'partner');
                    const appFormFieldSkillSets = appFormFieldsDatas.find(field => field.controlName === 'skillSets');
                    chosenCertification = null;
                    if (newValue !== null) {
                        chosenCertification = newValue;
                        this._organismService.find({
                            certifInfo: newValue.certifInfo,
                            organismType: 'certifier'
                        }).subscribe((response) => {
                            appFormFieldPartner.disabled = !response.payload.some(certifier => certifier.siret === this.currentOrganism.siret);
                            if (appFormFieldPartner.disabled) {
                                appFormFieldPartner.value = this.currentOrganism;
                                appFormFieldPartner.placeholder = null;
                                if (chosenCertification.type === CertificationTypes.RNCP && chosenCertification.allowPartialSkillSets) {
                                    this._certificationPartnerService.get({
                                        siret: this.currentOrganism.siret,
                                        certifInfo: chosenCertification.certifInfo
                                    }).subscribe((certificationPartner) => {
                                        appFormFieldSkillSets.removed = false;
                                        appFormFieldSkillSets.removable = true;
                                        if (certificationPartner.skillSets?.length) {
                                            this.allSkillSetIds = certificationPartner.skillSets.map((cpSkillSet) => cpSkillSet.id);
                                            appFormFieldSkillSets.choices = this.getSkillSetChoices(certificationPartner.skillSets, 'du partenariat');
                                        } else {
                                            this._skillService.list({certifInfo: chosenCertification.certifInfo}).subscribe((skill) => {
                                                this.allSkillSetIds = skill.payload.map((cpSkillSet) => cpSkillSet.id);
                                                appFormFieldSkillSets.choices = this.getSkillSetChoices(skill.payload, 'de la certification');
                                            });
                                        }
                                        this.appFormFieldData$.next(appFormFieldSkillSets);
                                    });
                                } else {
                                    appFormFieldSkillSets.removed = true;
                                    this.appFormFieldData$.next(appFormFieldSkillSets);
                                }
                            } else {
                                appFormFieldSkillSets.removed = true;
                                appFormFieldPartner.value = null;
                                appFormFieldPartner.placeholder = 'private.certification.folders.createFolder.form.partner.placeholderPartner';
                            }
                            this.appFormFieldData$.next(appFormFieldPartner);
                            this.appFormFieldData$.next(appFormFieldSkillSets);
                        });
                        return [];
                    } else {
                        appFormFieldSkillSets.removed = true;
                        appFormFieldPartner.disabled = true;
                        appFormFieldPartner.value = null;
                        appFormFieldPartner.placeholder = 'private.certification.folders.createFolder.form.partner.placeholderPartnerDisabled';
                        return [appFormFieldPartner, appFormFieldSkillSets];
                    }
                }
            },
            {
                controlName: 'partner',
                required: false,
                disabled: true,
                value: null,
                label: 'private.certification.folders.createFolder.form.partner.partner',
                type: 'infiniteSearch',
                placeholder: 'private.certification.folders.createFolder.form.partner.placeholderPartnerDisabled',
                help: 'private.certification.folders.createFolder.form.partner.tooltip',
                searchNoEntriesFoundLabel: 'private.certification.folders.createFolder.form.partner.noPartner',
                searchMethodPaginated: (params) => {
                    params.certifInfo = this.formGroup.get('newCertificationFolder')?.get('certification')?.value?.certifInfo ?? null;
                    if (params.certifInfo) {
                        params.isTrainingOrganism = true;
                        return this._organismService.find(params);
                    } else {
                        return of({payload: [], total: 0});
                    }
                },
                parameters: {
                    limit: 15,
                    certifInfo: null, // dynamic
                },
                searchResultFormatter: (partner: Organism) => {
                    return partner.name + ' (...' + partner.siret.substr(partner.siret.length - 5) + ')';
                },
                colSpan: 3,
                change: (controlName, newValue, appFormFieldsDatas) => {
                    const appFormFieldSkillSets = appFormFieldsDatas.find(field => field.controlName === 'skillSets');
                    if (chosenCertification.type === CertificationTypes.RNCP && chosenCertification.allowPartialSkillSets) {
                        if (newValue) {
                            this._certificationPartnerService.get({
                                siret: newValue.siret,
                                certifInfo: chosenCertification.certifInfo
                            }).subscribe((certificationPartner) => {
                                appFormFieldSkillSets.removed = false;
                                appFormFieldSkillSets.removable = true;
                                if (certificationPartner.skillSets?.length) {
                                    this.allSkillSetIds = certificationPartner?.skillSets.map((cpSkillSet) => cpSkillSet.id);
                                    appFormFieldSkillSets.choices = this.getSkillSetChoices(certificationPartner?.skillSets, 'du partenariat');
                                } else {
                                    this._skillService.list({certifInfo: chosenCertification.certifInfo}).subscribe((skill) => {
                                        this.allSkillSetIds = skill.payload.map((cpSkillSet) => cpSkillSet.id);
                                        appFormFieldSkillSets.choices = this.getSkillSetChoices(skill.payload, 'de la certification');
                                    });
                                }
                                this.appFormFieldData$.next(appFormFieldSkillSets);
                            });
                        } else {
                            appFormFieldSkillSets.removed = true;
                            return [appFormFieldSkillSets];
                        }
                    } else {
                        appFormFieldSkillSets.removed = true;
                        return [appFormFieldSkillSets];
                    }
                }
            },
            {
                // TODO(skillset) required / removable ?
                controlName: 'skillSets',
                removed: true,
                value: null,
                label: 'private.certification.folders.createFolder.form.skillsSet.title',
                help: 'private.certification.folders.createFolder.form.skillsSet.help',
                type: 'select',
                placeholder: 'private.certification.folders.createFolder.form.skillsSet.placeholder',
                searchMultiple: true,
                change: (controlName, newValue, formData) => {
                    const appFormFieldSkillSets = formData.find(field => field.controlName === 'skillSets');
                    if (newValue) {
                        if (newValue.includes('*') || newValue.sort().toString() === this.allSkillSetIds.sort().toString()) {
                            appFormFieldSkillSets.choices.forEach((choice) => {
                                if (choice.value !== '*') {
                                    choice.disabled = true;
                                }
                            });
                            appFormFieldSkillSets.value = ['*'];
                        } else {
                            appFormFieldSkillSets.choices.forEach((choice) => choice.disabled = false);
                            appFormFieldSkillSets.value = newValue;
                        }
                    } else {
                        appFormFieldSkillSets.value = null;
                    }
                    return [appFormFieldSkillSets];
                }
            },
            {
                controlName: 'type',
                value: this.type ?? null,
                label: 'private.certification.folders.createFolder.form.type.label',
                help: 'private.certification.folders.createFolder.form.type.help',
                type: 'select',
                removable: true,
                choices: [
                    // needed to convert value of CertificationFolderType toLowerCase in order to set type 'of' if organism.isTrainingOrganism
                    // because doesn't recognize 'private.certification.folders.createFolder.form.type.types.OF'
                    {
                        key: this._translateService.instant('private.certification.folders.createFolder.form.type.types.certifie'),
                        value: toLower(CertificationFolderType.CERTIFIE)
                    },
                    {
                        key: this._translateService.instant('private.certification.folders.createFolder.form.type.types.of'),
                        value: toLower(CertificationFolderType.OF)
                    },
                    {
                        key: this._translateService.instant('private.certification.folders.createFolder.form.type.types.poleEmploi'),
                        value: toLower(CertificationFolderType.POLE_EMPLOI)
                    },
                    {
                        key: this._translateService.instant('private.certification.folders.createFolder.form.type.types.employeur'),
                        value: toLower(CertificationFolderType.EMPLOYEUR)
                    },
                    {
                        key: this._translateService.instant('private.certification.folders.createFolder.form.type.types.autre'),
                        value: toLower(CertificationFolderType.AUTRE)
                    }
                ],
                colSpan: 3,
            },
            {
                controlName: 'enrollmentDate',
                value: null,
                label: 'private.certification.folders.createFolder.form.enrollmentDate',
                type: 'date',
                colSpan: 3,
            }
        ];
        this.appFormFieldsData = appFormFieldsData.filter(value => value != null);
    }

    submit(): void {
        this.loading = true;
        this.errorMessages = [];
        const certificationFolderFormValue = this.formGroup.getRawValue().newCertificationFolder;
        let skillSets = null;
        if (certificationFolderFormValue.skillSets) {
            skillSets = certificationFolderFormValue.skillSets.includes('*') ? this.allSkillSetIds : certificationFolderFormValue.skillSets;
        }
        this._certificationFolderService.create({
            attendeeId: certificationFolderFormValue.attendee.id,
            certifInfo: certificationFolderFormValue.certification.certifInfo,
            type: certificationFolderFormValue.type.toUpperCase(),
            enrollmentDate: certificationFolderFormValue.enrollmentDate,
            partner: certificationFolderFormValue.partner ? certificationFolderFormValue.partner.siret : null,
            skillSets: skillSets
        }).subscribe({
            next: (certificationFolder) => {
                this.loading = false;
                this.certificationFolderCreate.emit(certificationFolder);
            },
            error: (httpErrorResponse: HttpErrorResponse) => {
                this.loading = false;
                this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
            }
        });
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    getSkillSetChoices(skillSets: Skill[], type: string): Choices {
        const choices: Choices = [{
            key: 'Tous les blocs de compétences ' + type,
            value: '*'
        }];
        skillSets.forEach((skillSet) => {
            choices.push({
                value: skillSet.id,
                key: displaySkillSetTitle(skillSet)
            });
        });
        return choices;
    }
}
