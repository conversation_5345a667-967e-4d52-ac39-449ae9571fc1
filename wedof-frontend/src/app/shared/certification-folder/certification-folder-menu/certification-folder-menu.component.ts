import {Component, EventEmitter, Input, On<PERSON><PERSON><PERSON>, On<PERSON><PERSON>roy, OnInit, Output, ViewChild} from '@angular/core';
import {
    CertificationFolder,
    CertificationFolderActionType,
    CertificationFolderConfig,
    CertificationFolderDialogExamFailed,
    CertificationFolderDialogExamSuccess,
    CertificationFolderExamAborted,
    CertificationFolderExamRefused,
    CertificationFolderExamRegistered,
    CertificationFolderExamRetake,
    CertificationFolderExamToControl,
    CertificationFolderExamToTake,
    CertificationFolderStates,
    CertificationFolderUpdate
} from '../../api/models/certification-folder';
import {CertificationFolderToRegisterDialogComponent} from '../dialogs/certification-folder-to-register-dialog/certification-folder-to-register-dialog.component';
import {CertificationFolderRegisteredDialogComponent} from '../dialogs/certification-folder-registered-dialog/certification-folder-registered-dialog.component';
import {CertificationFolderRefusedDialogComponent} from '../dialogs/certification-folder-refused-dialog/certification-folder-refused-dialog.component';
import {CertificationFolderToTakeDialogComponent} from '../dialogs/certification-folder-to-take-dialog/certification-folder-to-take-dialog.component';
import {CertificationFolderToControlDialogComponent} from '../dialogs/certification-folder-to-control-dialog/certification-folder-to-control-dialog.component';
import {CertificationFolderToRetakeDialogComponent} from '../dialogs/certification-folder-to-retake-dialog/certification-folder-to-retake-dialog.component';
import {CertificationFolderSuccessDialogComponent} from '../dialogs/certification-folder-success-dialog/certification-folder-success-dialog.component';
import {filter, finalize, map, switchMap, takeUntil, tap} from 'rxjs/operators';
import {CertificationFolderFailedDialogComponent} from '../dialogs/certification-folder-failed-dialog/certification-folder-failed-dialog.component';
import {combineLatest, concat, iif, Observable, Subject} from 'rxjs';
import {CertificationFolderAbortedDialogComponent} from '../dialogs/certification-folder-aborted-dialog/certification-folder-aborted-dialog.component';
import {ComponentType} from '@angular/cdk/portal';
import {AbstractDialogFormComponent} from '../../material/form/abstract-dialog-form.component';
import {HttpErrorResponse} from '@angular/common/http';
import {CertificationFolderService} from '../../api/services/certification-folder.service';
import {MatDialog} from '@angular/material/dialog';
import {MatMenuTrigger} from '@angular/material/menu';
import {TranslateService} from '@ngx-translate/core';
import {ApiError} from '../../errors/errors.types';
import {FormGroup} from '@angular/forms';
import {SnackBarComponent} from '../../material/snack-bar/snack-bar.component';
import {displayTextSnackBar} from '../../utils/displayTextSnackBar';
import {MatSnackBar} from '@angular/material/snack-bar';
import {Select} from '@ngxs/store';
import {OrganismState} from '../../api/state/organism.state';
import {Organism} from '../../api/models/organism';
import {CertificationFolderStateToIconPipe} from '../../pipes/certification-folder-state-to-icon.pipe';
import {CertificationFolderStateToColorPipe} from '../../pipes/certification-folder-state-to-color.pipe';
import {ThemePalette} from '@angular/material/core';
import {UserState} from '../../api/state/user.state';
import {User} from '../../api/models/user';
import {DeletionConfirmationComponent} from '../../material/action-confirmation/deletion-confirmation.component';
import {CertificationFolderUpdateDialogComponent} from '../dialogs/certification-folder-update-dialog/certification-folder-update-dialog.component';
import {DisclaimerComponent} from '../../material/disclaimer/disclaimer.component';
import {Router} from '@angular/router';

export interface CertificationFolderConfigEntry {
    color: ThemePalette;
    state: CertificationFolderStates;
    actionType: CertificationFolderActionType;
    icon: string;
    iconColor: ThemePalette;
    disabled: boolean;
    tooltip?: string;
    method: (folders: CertificationFolder[]) => void;
}

export interface CertificationFolderActionCondition {
    allowAction: boolean;
    tooltip: string;
    type: string;
}

@Component({
    selector: 'app-certification-folder-menu',
    templateUrl: './certification-folder-menu.component.html',
    styleUrls: ['./certification-folder-menu.component.scss']
})
export class CertificationFolderMenuComponent implements OnInit, OnChanges, OnDestroy {
    @Input() disabled = false;
    @Input() withButton = false;
    @Input() formGroup?: FormGroup;
    @Input() folders: CertificationFolder[];
    @Output() processedFolder: EventEmitter<CertificationFolder> = new EventEmitter<CertificationFolder>();
    @Output() initCertificationFolder: EventEmitter<CertificationFolder> = new EventEmitter<CertificationFolder>();
    @Input() isTotalRowsSelected: boolean;
    @Input() totalFolderCount: number;
    @Input() filters: { query?: string, [param: string]: string | string[] | boolean };
    @ViewChild('menuTrigger') menuTrigger: MatMenuTrigger;
    @Input() panelOpenState = false;
    @Output() openEvent: EventEmitter<any> = new EventEmitter();
    @Output() closeEvent: EventEmitter<any> = new EventEmitter();
    @Output() errorMessages?: EventEmitter<string[]> = new EventEmitter<[]>();
    @Output() loading?: EventEmitter<boolean> = new EventEmitter<boolean>();
    @Output() allFoldersProcessed: EventEmitter<boolean> = new EventEmitter<boolean>();
    @Output() deleteFolder: EventEmitter<any> = new EventEmitter<any>();
    @Input() allSkillSetIds?: number[];

    @Select(OrganismState.organism) organism$: Observable<Organism>;
    @Select(UserState.user) user$: Observable<User>;

    isLoading = false;
    canEvaluate: boolean;
    states = CertificationFolderStates;
    hasOpenEvent: boolean;
    isLoadingProcessedCertificationFolder: boolean;
    errors: string[] = [];
    actionsAvailable: CertificationFolderConfigEntry[] = [];
    isImpersonator = false;
    organism: Organism = null;

    private _unsubscribeAll = new Subject<void>();

    constructor(private _certificationFolderService: CertificationFolderService,
                private _certificationFolderStateToIconPipe: CertificationFolderStateToIconPipe,
                private _certificationFolderStateToColorPipe: CertificationFolderStateToColorPipe,
                private _dialog: MatDialog,
                private _translateService: TranslateService,
                private _router: Router,
                private _snackBar: MatSnackBar) {
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    ngOnInit(): void {
        combineLatest([this.organism$, this.user$]).pipe(takeUntil(this._unsubscribeAll)).subscribe(([organism, user]) => {
            this.initMenu();
            this.organism = organism;
            this.isImpersonator = user.is_impersonator;
        });
    }

    ngOnChanges(): void {
        this.initMenu();
    }

    private initMenu(): void {
        this.errors = [];
        this.actionsAvailable = [];
        this.hasOpenEvent = this.openEvent.observers.length > 0;
        if (this.withButton) {
            this.retrievePermissions();
        }
    }

    submit(targetState?: CertificationFolderStates): void {
        const certificationFolderToUpdate = this.folders[0];
        this.errors = [];
        this.errorMessages.emit([]);
        this.isLoadingProcessedCertificationFolder = true;
        this.loading.emit(true);
        const nestedFormGroup = this.formGroup.get('certificationFolder') as FormGroup;
        const changedProperties = Object.entries(nestedFormGroup.controls).filter(([propertyName, formControl]) => {
                return !formControl.disabled && formControl.touched;
            }
        ).reduce((result, [propertyName, formControl]) => {
            result[propertyName] = formControl.value;
            return result;
        }, {});
        const certificationFolder: CertificationFolder = {
            ...certificationFolderToUpdate, ...changedProperties
        };
        let updatedSkillSets = false;
        if (changedProperties['skillSets'] || changedProperties['skillSets'] === null) {
            updatedSkillSets = true;
            // needed because incompatibility of type : certificationFolder.skillSets is object of skills meanwhile changedProperties['skillSets'] is array of skillId
            certificationFolder.skillSets = changedProperties['skillSets']?.includes('*') ? this.allSkillSetIds : changedProperties['skillSets'];
        } else {
            delete certificationFolder.skillSets;
        }
        if (updatedSkillSets && certificationFolder._links?.registrationFolder != null) {
            const dialogRef = this._dialog.open(DisclaimerComponent, {
                panelClass: ['full-page-scroll-30'],
                disableClose: true,
                data: {
                    title: 'common.actions.update',
                    subtitle: 'private.certification.folders.skillSets.warningUpdate',
                    mainButton: {
                        title: 'private.certification.folders.skillSets.confirmUpdate',
                        method: () => this.submitFolder(certificationFolderToUpdate, certificationFolder, targetState)
                    },
                    secondaryButton: {
                        title: 'common.actions.cancel',
                        method: () => {
                            this.loading.emit(false);
                            this.isLoadingProcessedCertificationFolder = false;
                        }
                    }
                }
            });
            dialogRef.afterClosed().subscribe(() => {
                this.loading.emit(false);
                this.isLoadingProcessedCertificationFolder = false;
            });
        } else {
            this.submitFolder(certificationFolderToUpdate, certificationFolder, targetState);
        }
    }

    submitFolder(certificationFolderToUpdate: CertificationFolder, certificationFolder: CertificationFolder, targetState?: CertificationFolderStates): void {
        this._certificationFolderService.update(certificationFolder).pipe(
            finalize(() => {
                this.isLoadingProcessedCertificationFolder = false;
                this.loading.emit(false);
            })
        ).subscribe(
            (updatedFolder) => {
                certificationFolderToUpdate = {...certificationFolderToUpdate, ...updatedFolder};
                this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(this._translateService.instant('common.actions.certificationFolderUpdatedSuccessfully', {
                        folderNumber: updatedFolder.externalId
                    },
                )));
                this.processedFolder.emit(updatedFolder);
                this.initCertificationFolder.emit(updatedFolder);
                this.callChangeStateMethod([certificationFolderToUpdate], targetState);
            },
            (httpErrorResponse: HttpErrorResponse) => {
                this.errors = (httpErrorResponse.error as ApiError).errorMessages;
                this.errorMessages.emit((httpErrorResponse.error as ApiError).errorMessages);
            }
        );
    }

    updateAndStateAction(): any {
        return this.formGroup?.dirty && this.panelOpenState;
    }

    updateAndStateActionText(): any {
        return this.formGroup?.dirty && this.panelOpenState ? this._translateService.instant('common.actions.updateAnd') : ' ';
    }

    openPanel(): void {
        this.openEvent.emit();
    }

    closePanel(): void {
        this.closeEvent.emit();
    }

    retrievePermissions(): void {
        if (this.isTotalRowsSelected || (this.folders && this.folders.length)) {
            this.isLoading = true;
            this.retrieveActions();
        } else if (this.menuTrigger) {
            this.menuTrigger.openMenu();
        }
    }

    abort(folders: CertificationFolder[]): void {
        const row = folders.length === 1 ? folders[0] : null;
        const abortableStates = [
            CertificationFolderStates.TO_REGISTER.toString(),
            CertificationFolderStates.REGISTERED.toString(),
            CertificationFolderStates.TO_TAKE.toString(),
            CertificationFolderStates.TO_RETAKE.toString()
        ];
        this.openActionDialog<CertificationFolderExamAborted>(
            CertificationFolderAbortedDialogComponent,
            (data) => this.iterateOverSelection({
                rowMapper: folder => this._certificationFolderService.abort(folder.externalId, data),
                rowFilter: folder => abortableStates.includes(folder.state),
                row
            }),
            {
                folder: folders[0],
                plural: this.isPlural(row),
                folderCount: this.currentSelectionCount(),
                prefixAction: 'examAborted',
                commentUpdatable: this.isCommentUpdatable(folders),
                canEvaluate: this.canEvaluate
            }
        );
    }

    fail(folders: CertificationFolder[]): void {
        const row = folders.length === 1 ? folders[0] : null;
        const skipToControl = folders[0].state === CertificationFolderStates.TO_TAKE;
        this.openActionDialog<CertificationFolderDialogExamFailed>(
            CertificationFolderFailedDialogComponent,
            (data) => this.iterateOverSelection({
                rowMapper:
                    folder => skipToControl ? this._certificationFolderService.toControl(folder.externalId, data.toControl).pipe(
                            switchMap(() => iif(() => data.canRetake,
                                this._certificationFolderService.toRetake(folder.externalId, data.retake),
                                this._certificationFolderService.fail(folder.externalId, data.failed))))
                        : data.canRetake ?
                            this._certificationFolderService.toRetake(folder.externalId, data.retake)
                            : this._certificationFolderService.fail(folder.externalId, data.failed),
                rowFilter: folder => folder.state === CertificationFolderStates.TO_CONTROL,
                row,
            }),
            {
                folder: folders[0],
                plural: this.isPlural(row),
                folderCount: this.currentSelectionCount(),
                skipToControl: skipToControl,
                prefixAction: 'examFailed',
                commentUpdatable: this.isCommentUpdatable(folders),
                canEvaluate: this.canEvaluate
            }
        );
    }

    refuse(folders: CertificationFolder[]): void {
        const row = folders.length === 1 ? folders[0] : null;
        this.openActionDialog<CertificationFolderExamRefused>(
            CertificationFolderRefusedDialogComponent,
            (data) => this.iterateOverSelection({
                rowMapper: folder => this._certificationFolderService.refuse(folder.externalId, data),
                rowFilter: folder => folder.state === CertificationFolderStates.TO_REGISTER,
                row
            }),
            {
                folder: folders[0],
                plural: this.isPlural(row),
                folderCount: this.currentSelectionCount(),
                prefixAction: 'examRefused',
                commentUpdatable: this.isCommentUpdatable(folders),
                canEvaluate: this.canEvaluate
            }
        );
    }

    registered(folders: CertificationFolder[]): void {
        const row = folders.length === 1 ? folders[0] : null;
        this.openActionDialog<CertificationFolderExamRegistered>(
            CertificationFolderRegisteredDialogComponent,
            (data) => this.iterateOverSelection({
                rowMapper: folder => this._certificationFolderService.registered(folder.externalId, data),
                rowFilter: folder => folder.state === CertificationFolderStates.TO_REGISTER || folder.state === CertificationFolderStates.TO_TAKE,
                row
            }),
            {
                folder: folders[0],
                plural: this.isPlural(row),
                folderCount: this.currentSelectionCount(),
                prefixAction: 'examRegistered',
                commentUpdatable: this.isCommentUpdatable(folders),
                canEvaluate: this.canEvaluate
            }
        );
    }

    toControl(folders: CertificationFolder[]): void {
        const row = folders.length === 1 ? folders[0] : null;
        const toControlStates = [
            CertificationFolderStates.TO_TAKE.toString(),
            CertificationFolderStates.TO_RETAKE.toString(),
            CertificationFolderStates.FAILED.toString(),
            CertificationFolderStates.SUCCESS.toString()
        ];
        this.openActionDialog<CertificationFolderExamToControl>(
            CertificationFolderToControlDialogComponent,
            (data) => this.iterateOverSelection({
                rowMapper: folder => this._certificationFolderService.toControl(folder.externalId, data),
                rowFilter: folder => toControlStates.includes(folder.state),
                row
            }),
            {
                folder: folders[0],
                plural: this.isPlural(row),
                folderCount: this.currentSelectionCount(),
                prefixAction: 'examToControl',
                commentUpdatable: this.isCommentUpdatable(folders),
                canEvaluate: this.canEvaluate
            }
        );
    }

    toRegister(folders: CertificationFolder[]): void {
        const row = folders.length === 1 ? folders[0] : null;
        this.openActionDialog<void>(
            CertificationFolderToRegisterDialogComponent,
            () => this.iterateOverSelection({
                rowMapper: folder => this._certificationFolderService.toRegister(folder.externalId),
                rowFilter: folder => folder.state === CertificationFolderStates.REFUSED || folder.state === CertificationFolderStates.REGISTERED,
                row
            }),
            {
                folder: folders[0],
                plural: this.isPlural(row),
                folderCount: this.currentSelectionCount(),
                prefixAction: 'examToRegister',
                commentUpdatable: this.isCommentUpdatable(folders),
                canEvaluate: this.canEvaluate
            }
        );
    }

    toRetake(folders: CertificationFolder[]): void {
        const row = folders.length === 1 ? folders[0] : null;
        this.openActionDialog<CertificationFolderExamRetake>(
            CertificationFolderToRetakeDialogComponent,
            (data) => this.iterateOverSelection({
                rowMapper: folder => this._certificationFolderService.toRetake(folder.externalId, data),
                rowFilter: folder => folder.state === CertificationFolderStates.TO_CONTROL || folder.state === CertificationFolderStates.FAILED,
                row
            }),
            {
                folder: folders[0],
                plural: this.isPlural(row),
                folderCount: this.currentSelectionCount(),
                prefixAction: 'examToRetake',
                commentUpdatable: this.isCommentUpdatable(folders),
                canEvaluate: this.canEvaluate
            }
        );
    }

    toTake(folders: CertificationFolder[]): void {
        const row = folders.length === 1 ? folders[0] : null;
        this.openActionDialog<CertificationFolderExamToTake>(
            CertificationFolderToTakeDialogComponent,
            (data) => this.iterateOverSelection({
                rowMapper: folder => this._certificationFolderService.toTake(folder.externalId, data),
                rowFilter: folder => folder.state === CertificationFolderStates.REGISTERED || folder.state === CertificationFolderStates.TO_CONTROL ||
                    folder.state === CertificationFolderStates.ABORTED,
                row
            }),
            {
                folder: folders[0],
                plural: this.isPlural(row),
                folderCount: this.currentSelectionCount(),
                prefixAction: 'examToTake',
                commentUpdatable: this.isCommentUpdatable(folders),
                canEvaluate: this.canEvaluate
            }
        );
    }

    success(folders: CertificationFolder[]): void {
        const row = folders.length === 1 ? folders[0] : null;
        const skipToControl = folders[0].state === CertificationFolderStates.TO_TAKE;
        this.openActionDialog<CertificationFolderDialogExamSuccess>(
            CertificationFolderSuccessDialogComponent,
            (data) => this.iterateOverSelection({
                rowMapper: folder => skipToControl ? this._certificationFolderService.toControl(folder.externalId, data.toControl).pipe(
                    switchMap(updatedFolder => this._certificationFolderService.success(updatedFolder.externalId, data.success))
                ) : this._certificationFolderService.success(folder.externalId, data.success),
                rowFilter: folder => folder.state === CertificationFolderStates.TO_CONTROL,
                row
            }),
            {
                folder: folders[0],
                plural: this.isPlural(row),
                folderCount: this.currentSelectionCount(),
                skipToControl: skipToControl,
                prefixAction: 'examSuccess',
                commentUpdatable: this.isCommentUpdatable(folders),
                canEvaluate: this.canEvaluate
            }
        );
    }

    update(folders: CertificationFolder[]): void {
        if (folders.length === 1) {
            const viewName = this._router.url.includes('/kanban/') ? 'kanban' : 'liste';
            this._router.navigate(['/', 'certification', 'dossiers', viewName, folders[0].externalId, 'dossierCertification']);
            this.menuTrigger.closeMenu();
        } else {
            this.openActionDialog<CertificationFolderUpdate>(
                CertificationFolderUpdateDialogComponent,
                (data) => this.iterateOverSelection({
                    rowMapper: folder => this._certificationFolderService.update({...folder, ...data}),
                    rowFilter: folder => true
                }),
                {
                    folder: folders[0],
                    plural: this.isPlural(),
                    folderCount: this.currentSelectionCount(),
                    prefixAction: 'update',
                    commentUpdatable: this.isCommentUpdatable(folders),
                    canEvaluate: this.canEvaluate
                }
            );
        }
    }

    private currentSelectionCount(): number {
        return this.isTotalRowsSelected ? this.totalFolderCount : this.folders.length;
    }

    private isPlural(row?: CertificationFolder): boolean {
        return !row || this.folders.length > 1;
    }

    private isCommentUpdatable(folders: CertificationFolder[]): boolean {
        return folders.every((folder) => folder.comment === folders[0].comment);
    }

    private iterateOverSelection({rowMapper, rowFilter, row}: {
        rowMapper: (row: CertificationFolder) => Observable<CertificationFolder>,
        rowFilter?: (row: CertificationFolder) => boolean,
        row?: CertificationFolder
    }): Observable<CertificationFolder> {
        let observable;
        if (row) {
            observable = rowMapper(row);
        } else if (this.isTotalRowsSelected && this.totalFolderCount !== this.folders.length) {
            observable = this._certificationFolderService.list({
                siret: 'all',
                ...this.filters,
                limit: this.totalFolderCount
            }).pipe(
                takeUntil(this._unsubscribeAll),
                map(data => data.payload),
                switchMap((folders: CertificationFolder[]): Observable<CertificationFolder> => {
                    const data = rowFilter ? folders.filter(rowFilter) : folders;
                    return concat(...data.map(rowMapper));
                }),
            );
        } else {
            observable = concat(...this.folders.map(rowMapper));
        }
        return observable;
    }

    private openActionDialog<T>(
        componentType: ComponentType<AbstractDialogFormComponent<T>>,
        submit: (data: T) => Observable<CertificationFolder>,
        dialogData: {
            folder?: CertificationFolder,
            plural: boolean,
            folderCount: number,
            skipToControl?: boolean,
            prefixAction: string,
            commentUpdatable: boolean,
            canEvaluate: boolean,
            warningMessage?: string
        },
    ): void {
        const dialogRef = this._dialog.open(componentType, {
            panelClass: 'full-page-scroll-40',
            height: 'auto',
            data: dialogData
        });

        dialogRef.componentInstance.submitted$.pipe(
            takeUntil(this._unsubscribeAll),
            switchMap((data: T) =>
                submit(data).pipe(
                    takeUntil(this._unsubscribeAll),
                    tap({
                        next: (certificationFolder: CertificationFolder) => {
                            dialogRef.componentInstance.lastProcessedData(certificationFolder);
                            if (dialogRef.componentInstance.allProcessed) {
                                this.allFoldersProcessed.emit(true);
                            }
                            this.processedFolder.emit(certificationFolder);
                            this.initCertificationFolder.emit(certificationFolder);
                        },
                        complete: () => {
                            dialogRef.componentInstance.close();
                        }
                    })
                ))
        ).subscribe({
            error: (httpErrorResponse: HttpErrorResponse) => {
                dialogRef.componentInstance.loading = false;
                dialogRef.componentInstance.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
            }
        });
    }

    getSingleAction(): CertificationFolderConfigEntry {
        return this.actionsAvailable?.length ? this.actionsAvailable[0] : null;
    }

    retrieveActions(): void {
        const actionsAvailable: CertificationFolderConfigEntry[] = [];
        const firstCertificationFolder = this.folders[0];
        if (this.areAllFoldersInSameState(this.folders)) {
            this._certificationFolderService.getActionsFolder(firstCertificationFolder.externalId).subscribe({
                next: actions => {
                    Object.values(CertificationFolderConfig[firstCertificationFolder.state]).forEach((certificationFolderConfigEntry) => {
                        const conditions = actions[certificationFolderConfigEntry.state];
                        if (conditions?.length) {
                            const allConditionsFulfilled = conditions.every(condition => condition.allowAction);
                            if (
                                allConditionsFulfilled ||
                                (!conditions.some(condition => !condition.allowAction && (condition.type === 'canEvaluate' || condition.type === 'isCertifier')))
                            ) {
                                const actionType = certificationFolderConfigEntry.actionType === CertificationFolderActionType.GO_BACK ?
                                    CertificationFolderActionType.GO_BACK :
                                    certificationFolderConfigEntry.state;
                                const certificationFolderConfigEntryCopy = {
                                    ...certificationFolderConfigEntry, // Important ! don't mutate config object
                                    icon: this._certificationFolderStateToIconPipe.transform(actionType),
                                    iconColor: this._certificationFolderStateToColorPipe.transform(actionType),
                                    disabled: !allConditionsFulfilled,
                                    tooltip: this.getTooltip(conditions),
                                    method: (certificationFolders: CertificationFolder[]) => {
                                        this.callChangeStateMethod(certificationFolders, certificationFolderConfigEntry.state);
                                    }
                                };
                                actionsAvailable.push(certificationFolderConfigEntryCopy);
                            }
                        }
                        this.canEvaluate = !Object.values(actions).some(
                            cfActionConditions => cfActionConditions.some(condition => condition.type === 'canEvaluate' && !condition.allowAction));
                    });
                },
                complete: () => this.addUpdateAction(actionsAvailable)
            });
        } else {
            this.addUpdateAction(actionsAvailable);
        }
    }

    getTooltip(conditions: CertificationFolderActionCondition[]): string {
        return conditions.filter(condition => condition.tooltip).map(condition => condition.tooltip).join(' ');
    }

    areAllFoldersInSameState(folders: CertificationFolder[]): boolean {
        return folders.every(folder => folder.state === folders[0].state);
    }

    private callChangeStateMethod(certificationFolders: CertificationFolder[], targetState: CertificationFolderStates): void {
        if (targetState === CertificationFolderStates.TO_REGISTER) {
            this.toRegister(certificationFolders);
        } else if (targetState === CertificationFolderStates.REGISTERED) {
            this.registered(certificationFolders);
        } else if (targetState === CertificationFolderStates.TO_TAKE) {
            this.toTake(certificationFolders);
        } else if (targetState === CertificationFolderStates.TO_CONTROL) {
            this.toControl(certificationFolders);
        } else if (targetState === CertificationFolderStates.SUCCESS) {
            this.success(certificationFolders);
        } else if (targetState === CertificationFolderStates.TO_RETAKE) {
            this.toRetake(certificationFolders);
        } else if (targetState === CertificationFolderStates.FAILED) {
            this.fail(certificationFolders);
        } else if (targetState === CertificationFolderStates.REFUSED) {
            this.refuse(certificationFolders);
        } else if (targetState === CertificationFolderStates.ABORTED) {
            this.abort(certificationFolders);
        }
    }

    canDelete(): boolean {
        return this.isImpersonator && this.folders.length === 1 && !this.folders[0]?._links.registrationFolder?.externalId;
    }

    delete(folder: CertificationFolder): void {
        const dialogRef = this._dialog.open(DeletionConfirmationComponent, {
            panelClass: 'full-page-scroll-40',
            height: 'auto',
            data: {
                messageKey: 'private.common.certificationFolder.deletion',
                data: folder
            }
        });
        dialogRef.componentInstance.actionValue$.pipe(
            filter(confirmation => confirmation),
            tap(() => this.isLoading = true),
            switchMap(() => this._certificationFolderService.delete(folder.externalId)),
            finalize(() => {
                dialogRef.componentInstance.close();
                this.isLoading = false;
            })
        ).subscribe(
            () => {
                this.deleteFolder.emit(folder);
            },
            (httpErrorResponse: HttpErrorResponse) => {
                this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar((httpErrorResponse.error as ApiError).errorMessages.toString(), 5000, 'red'));
            }
        );
    }

    private addUpdateAction(actionsAvailable: CertificationFolderConfigEntry[]): void {
        if (this.folders.every(folder => folder._links.certifier.siret === this.organism.siret) && !this.withButton) {
            actionsAvailable.unshift({
                icon: this._certificationFolderStateToIconPipe.transform('update'),
                iconColor: this._certificationFolderStateToColorPipe.transform('update'),
                disabled: false,
                state: null,
                method: (certificationFolders: CertificationFolder[]) => {
                    this.update(certificationFolders);
                },
                color: 'primary',
                actionType: CertificationFolderActionType.UPDATE
            });
        }

        this.actionsAvailable = actionsAvailable;
        this.isLoading = false;
        if (this.menuTrigger) {
            this.menuTrigger.openMenu();
        }
    }
}
