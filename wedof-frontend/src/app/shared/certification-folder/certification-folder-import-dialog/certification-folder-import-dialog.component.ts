import {Component, OnInit} from '@angular/core';
import {MatDialogRef} from '@angular/material/dialog';
import {FileService} from '../../api/services/file.service';
import {ApiError} from '../../errors/errors.types';
import {HttpErrorResponse, HttpParams, HttpResponse} from '@angular/common/http';
import {downloadCSVData} from '../../utils/object-utils';
import {FormBuilder, FormGroup} from '@angular/forms';
import {AppFormFieldData} from '../../material/app-form-field/app-form-field.component';

@Component({
    selector: 'app-export-folders',
    templateUrl: './certification-folder-import-dialog.component.html',
    styleUrls: ['./certification-folder-import-dialog.component.scss']
})
export class CertificationFolderImportDialogComponent implements OnInit {

    loading = false;
    success = false;
    formGroup: FormGroup;
    errorMessages: string[] = [];
    appFormFieldsData: AppFormFieldData[];

    constructor(
        public dialogRef: MatDialogRef<CertificationFolderImportDialogComponent>,
        private _formBuilder: FormBuilder,
        private _fileService: FileService,
    ) {
    }

    ngOnInit(): void {
        this.formGroup = this._formBuilder.group({
            file: this._formBuilder.group({})
        });
        this.appFormFieldsData = [
            {
                controlName: 'raiseEvents',
                label: 'private.common.certificationFolder.import.raiseEvents',
                value: false,
                type: 'checkbox'
            }
        ];
    }

    closeModal(): void {
        this.dialogRef.close();
    }

    onSpreadsheetUploaded(event): void {
        if (event.target.files.length > 0) {
            this.loading = true;
            const file = event.target.files[0];
            const formData: FormData = new FormData();
            formData.append('fileKey', file, file.name);
            let params = new HttpParams();
            params = params.set('raiseEvents', this.formGroup.getRawValue().file.raiseEvents);
            this._fileService.upload('certificationFolders', 'updateFromSpreadsheet', file, params).subscribe(
                (e) => {
                    if (e instanceof HttpResponse && e?.status === 200) {
                        this.loading = false;
                        this.success = true;
                        const json = e.body;
                        let result = json.ok?.map(id => {
                            return {
                                'ExternalId Dossier Certification': id,
                                'result': 'ok'
                            };
                        });
                        if (json.ko) {
                            result = result.concat(json.ko?.map(obj => {
                                return {
                                    'ExternalId Dossier Certification': Object.keys(obj)[0],
                                    'result': obj[Object.keys(obj)[0]]
                                };
                            }));
                        }
                        const replacer = (key, value) => value === null ? '' : value;
                        const header = Object.keys(result[0]);
                        const csv = [
                            header.join(','), // header row first
                            ...result.map(row => header.map(fieldName => JSON.stringify(row[fieldName], replacer)).join(','))
                        ].join('\r\n');
                        downloadCSVData('report.csv', csv);
                    }
                },
                (httpErrorResponse: HttpErrorResponse) => {
                    this.loading = false;
                    this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
                }
            );
        }
    }
}
