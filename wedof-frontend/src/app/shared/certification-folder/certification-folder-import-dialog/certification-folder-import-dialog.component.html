<app-dialog-layout [title]="'private.common.certificationFolder.import.title' | translate"
                   [errorMessages]="errorMessages"
                   [cancelText]="'common.actions.close'"
                   (dialogClose)="closeModal()">
    <treo-message type="primary" [showIcon]="false" class="mt-3 mb-6" appearance="outline">
        <p>
            {{'private.common.certificationFolder.import.information' | translate}}
            <a [routerLink]="['/assistance/guides/certificateurs/maj-dossiers-certification-excel']"
               target="_blank"
               (click)="closeModal()">(documentation)</a>
        </p>
        <p>
            {{'private.common.certificationFolder.import.information3' | translate}}
        </p>
    </treo-message>

    <form [formGroup]="formGroup" class="mb-5">
        <app-form-fields formGroupName="file"
                         [appFormFieldsData]="appFormFieldsData"
                         [formGroup]="formGroup">
        </app-form-fields>
    </form>
    <div *ngIf="!success else spreadsheetSuccess">
        <input type="file"
               [disabled]="loading"
               id="spreadsheetFile"
               accept=".xlsx, .xls, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
               (change)="onSpreadsheetUploaded($event)"/>
    </div>
    <ng-template #spreadsheetSuccess>
        <div>
            {{'private.common.certificationFolder.import.success' | translate}}
        </div>
    </ng-template>
    <mat-progress-spinner class="mr-4" *ngIf="loading" [diameter]="24"
                          mode="indeterminate"></mat-progress-spinner>
</app-dialog-layout>
