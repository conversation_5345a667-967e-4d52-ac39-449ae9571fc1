<mat-card class="flex-col mt-3 p-5 pb-0 pt-3 border shadow-none sm" [ngClass]="{'card-loading':cardLoading}">
    <div class="flex items-center mb-2">
        <mat-icon class="mr-3 card-loading-show text-4xl"
                  [color]="includes(errorStates, certificationFolder?.state) ? 'warn' : 'primary'"
                  title="{{ 'private.certification.folders.actions.' + certificationFolder?.state | translate}}">{{ certificationFolder?.state | certificationFolderStateToIcon }}
        </mat-icon>
        <div>
            <span
                class="text-xl font-semibold card-loading-show">{{ 'private.common.certificationFolder.title' | translate }}</span>
            <div class="text-secondary text-md truncate card-loading-show"
                 title="{{ 'private.certification.folders.actions.' + certificationFolder?.state | translate}}">
                {{ 'private.certification.folders.actions.' + certificationFolder?.state | translate }} - <span
                title="{{ 'private.common.certificationFolder.stateLastUpdate' | translate}}">
                    {{ certificationFolder?.stateLastUpdate | date: 'dd/MM/yyyy' }}
                </span><span
                *ngIf="(certificationFolder.state == states.REGISTERED || certificationFolder.state == states.TO_REGISTER) && certificationFolder.inTraining"
                title="{{ 'private.certification.folders.actions.inTrainingStarted' | translate}} : {{ certificationFolder.history.inTrainingStartedDate | date: 'dd/MM/yyyy' }}">
                - {{ 'private.certification.folders.actions.inTrainingStarted' | translate }}
                </span>
                <span
                    *ngIf="(certificationFolder.state == states.REGISTERED || certificationFolder.state == states.TO_REGISTER) && certificationFolder.history.inTrainingEndedDate"
                    title="{{ 'private.certification.folders.actions.inTrainingEnded' | translate}} : {{ certificationFolder.history.inTrainingEndedDate | date: 'dd/MM/yyyy' }}">
                    - {{ 'private.certification.folders.actions.inTrainingEnded' | translate }}
                </span>
            </div>
        </div>
        <div class="ml-auto flex -mr-4 card-loading-show">
            <app-attendee-menu-folders [attendeeLink]="certificationFolder.attendeeLink"
                                       [addToPassportLink]="certification?.type === certificationTypes.INTERNAL ? null : certificationFolder.addToPassportLink"
                                       [surveyLink]="certificationFolder.surveyLink"
                                       [subscription]="subscription"
                                       [organism]="mainOrganism"
                                       [attendee]="certificationFolder.attendee"
                                       [permalink]="certificationFolder.permalink"
                                       [externalId]="certificationFolder.externalId"
                                       attendeeType="candidate">
            </app-attendee-menu-folders>

            <app-activity-form-menu
                *ngIf="isCertifier"
                [entityClass]="EntityClass.CERTIFICATION_FOLDER"
                [entityId]="certificationFolder.externalId"
                (createdActivity)="createdActivity($event)">
            </app-activity-form-menu>
            <app-certification-folder-menu
                (processedFolder)="refresh($event)"
                (deleteFolder)="delete($event)"
                [folders]="[certificationFolder]"
                [panelOpenState]="panelOpenState"
                (openEvent)="openPanel()"
                (closeEvent)="closePanel()"></app-certification-folder-menu>
        </div>
    </div>
    <div class="grid grid-cols-6 gap-2" *ngIf="!panelOpenState || cardLoading">
        <ng-container *ngTemplateOutlet="treomessages">
        </ng-container>
        <app-form-field-static class="col-span-6"
                               icon="folder"
                               type="text"
                               [copy]="true"
                               value="{{certificationFolder.externalId}}">
        </app-form-field-static>
        <app-form-field-static *ngIf="certificationLink"
                               class="font-bold col-span-6"
                               [value]="certificationName"
                               icon="fact_check"
                               [href]="certificationLink"
                               type="url">
        </app-form-field-static>
        <app-form-field-static *ngIf="!isCertifier" class="font-bold col-span-6" icon="business" type="text"
                               [value]="certifierName">
        </app-form-field-static>

        <app-form-field-static *ngIf="isCertifier && partnerName" class="font-bold col-span-6" icon="business" type="text"
                               [value]="partnerName">
        </app-form-field-static>

        <app-form-field-static type="text" icon="event" hideLabel=true
                               class="col-span-6"
                               *ngIf="certificationFolder?.examinationDate"
                               label="private.common.certificationFolder.examinationDate"
                               [value]="'private.common.certificationFolder.date' | translate: {
                date: certificationFolder?.examinationDate | date : 'dd/MM/yyyy à hh:mm'
            }"></app-form-field-static>

        <app-form-field-static icon="loyalty" class="col-span-6" type="text" *ngIf="certificationFolder?.detailedResult"
                               [value]="certificationFolder?.detailedResult">
        </app-form-field-static>

        <app-form-field-static icon="location_on" type="text"  class="col-span-6"*ngIf="examinationInformations &&
            (certificationFolder?.state === states.SUCCESS ||  certificationFolder?.state === states.FAILED || certificationFolder?.state === states.TO_CONTROL
            || certificationFolder?.state === states.TO_RETAKE)"
                               [value]="examinationInformations">
        </app-form-field-static>
        <app-form-field-static icon="text_snippet" class="col-span-6"
                               *ngIf="certificationFolder?.comment?.length >= 1"
                               [value]="certificationFolder?.comment"
                               type="textarea"></app-form-field-static>
        <app-form-field-static class="col-span-6 mt-1"
                               *ngIf="certificationFolder?.tags?.length"
                               [value]="certificationFolder?.tags"
                               type="tags"></app-form-field-static>
        <button type="button" class="col-span-6 flex justify-center mt-2 -mx-5 pt-2 pb-2 open-panel card-loading-hidden"
                (click)="openPanel()">
            <mat-icon svgIcon="keyboard_arrow_down"></mat-icon>
        </button>
    </div>
    <form #form="ngForm" [formGroup]="formGroup" class="flex flex-col" *ngIf="panelOpenState && !cardLoading">
        <ng-container *ngTemplateOutlet="treomessages">
        </ng-container>
        <app-form-fields class="grid grid-cols-6 gap-2"
                         formGroupName="certificationFolder"
                         [entity]="certificationFolder"
                         [appFormFieldsData]="appFormFieldsData"
                         [formGroup]="formGroup">
        </app-form-fields>
        <app-file-list #appFileList
                       [isOwner]="isCertifier"
                       [showManageFile]="isCertifier"
                       [linkManageFile]="'/certification/partenariats/' + certification.certifInfo"
                       [entity]="certificationFolder"
                       [fileTypes]="certificationFolderFileTypes"
                       [orderedStates]="orderedCertificationFolderStates"
                       [entityParentId]="certification.id.toString()"
                       entityApiPath="certificationFolders"
                       entityIdProperty="externalId"
                       class="mt-3"
                       titleStateChange="private.certification.folders.actions.">
        </app-file-list>
        <button type="button" class="flex justify-center -mx-5 pt-2 pb-2 close-panel" (click)="closePanel()">
            <mat-icon svgIcon="keyboard_arrow_up"></mat-icon>
        </button>
    </form>
    <div
        class="flex items-center justify-end border-t pl-5 pr-10 -mx-5 py-3 light:bg-cool-gray-50 dark:bg-cool-gray-700 card-loading-hidden">
        <div class="flex">
            <mat-error *ngIf="errorMessages.length">
                <ul>
                    <li *ngFor="let errorMessage of errorMessages">{{ errorMessage }}</li>
                </ul>
            </mat-error>
            <app-certification-folder-menu class="-mr-4"
                                           (processedFolder)="refresh($event)"
                                           (deleteFolder)="delete($event)"
                                           [folders]="[certificationFolder]"
                                           [withButton]="true"
                                           [panelOpenState]="panelOpenState"
                                           (openEvent)="openPanel()"
                                           (closeEvent)="closePanel()"
                                           (initCertificationFolder)="initForm($event)"
                                           [formGroup]="formGroup"
                                           (errorMessages)="setErrorMessages($event)"
                                           (loading)="setLoading($event)"
                                           [allSkillSetIds]="allSkillSetIds"></app-certification-folder-menu>
        </div>
    </div>
</mat-card>

<ng-template #treomessages>
    <treo-message
        *ngIf="isCertifier && certificationFolder._links.certification.allowPartialSkillSets && !certificationFolder.skillSets.length "
        type="error" [showIcon]="false"
        class="my-2 card-loading-show" appearance="outline">
        <app-form-field-static icon="notification_important"
                               type="text"
                               [value]="'private.common.certificationFolder.skillSets' | translate">
        </app-form-field-static>
    </treo-message>
    <treo-message
        *ngIf="certificationFolder.cdcToExport && !certificationFolder.cdcCompliant && certificationFolder.state === states.SUCCESS && currentCertificationFoldersCdcFiles === null"
        type="error"
        [showIcon]="false"
        class="my-2 card-loading-show" appearance="outline">
        <app-form-field-static icon="{{certificationFolderCdcStates.NOT_EXPORTED | certificationFolderCdcStateToIcon}}"
                               type="text"
                               [value]="'private.common.certificationFolder.cdcExport.no' | translate: {
                        result : isCertifier ? 'Passeport (accrochage)' : 'Passeport de Compétences'  }">
        </app-form-field-static>
    </treo-message>
    <treo-message type="info"
                  [showIcon]="false"
                  *ngIf="!certificationFolder.fullCertification && certificationFolder.state === states.SUCCESS"
                  class="my-2 card-loading-show" appearance="outline">
        <div class="flex items-center">
            <mat-icon svgIcon="info"></mat-icon>
            <div class="ml-2"
                 [innerHTML]="isCertifier ?
                     ('private.common.certificationFolder.state.isCertifier.successSkillSets' | translate : { skillSets: getSkillSetTitle() })
                     :
                     ('private.common.certificationFolder.state.isTrainer.successSkillSets' | translate : { skillSets: getSkillSetTitle() })"></div>
        </div>
    </treo-message>
    <treo-message
        *ngIf="isCertifier && !certification.allowGenerateXmlAutomatically && !certificationFolder.cdcExcluded && certificationFolder.cdcToExport && certificationFolder.state === states.SUCCESS && currentCertificationFoldersCdcFiles === null"
        type="info"
        [showIcon]="false"
        class="my-2 card-loading-show" appearance="outline">
        <app-form-field-static icon="error"
                               type="text"
                               [value]="'private.common.certificationFolder.cdcExport.maxDateForExport' | translate : { maxDate : maxDateForExport }">
        </app-form-field-static>
    </treo-message>
    <ng-container *ngIf="currentCertificationFoldersCdcFiles && !certificationFolder.cdcExcluded">
        <treo-message
            *ngIf="isCertifier && currentCertificationFoldersCdcFiles.state === certificationFoldersCdcFilesState.PROCESSED_KO"
            type="error"
            [showIcon]="false"
            class="my-2 card-loading-show" appearance="outline">
            <app-form-field-static
                icon="{{certificationFolderCdcStates.PROCESSED_KO | certificationFolderCdcStateToIcon}}"
                type="text-html"
                [value]="'private.common.certificationFolder.cdcExport.processedKoInformations' | translate : { date : currentCertificationFoldersCdcFiles.cdcFile.submissionDate | date: 'dd/MM/yyyy', errorMessage : currentCertificationFoldersCdcFiles.errorMessage }">
            </app-form-field-static>
        </treo-message>
        <treo-message
            *ngIf="currentCertificationFoldersCdcFiles.state === certificationFoldersCdcFilesState.EXPORTED &&
            (!isCertifier || (isCertifier && !certification.allowGenerateXmlAutomatically ))"
            type="error"
            [showIcon]="false"
            class="my-2 card-loading-show" appearance="outline">
            <app-form-field-static
                *ngIf="isCertifier && !certification.allowGenerateXmlAutomatically"
                icon="{{certificationFolderCdcStates.EXPORTED | certificationFolderCdcStateToIcon}}"
                type="text-html"
                [value]="'private.common.certificationFolder.cdcExport.exportedInformations' | translate : { date : currentCertificationFoldersCdcFiles.cdcFile.stateLastUpdate | date: 'dd/MM/yyyy' }">
            </app-form-field-static>
            <app-form-field-static
                *ngIf="!isCertifier"
                icon="{{certificationFolderCdcStates.EXPORTED | certificationFolderCdcStateToIcon}}"
                type="text-html"
                [value]="'private.common.certificationFolder.cdcExport.exportedPartner' | translate : { date : currentCertificationFoldersCdcFiles.cdcFile.stateLastUpdate | date: 'dd/MM/yyyy' }">
            </app-form-field-static>
        </treo-message>
    </ng-container>
</ng-template>
