import {
    AfterViewInit,
    Component,
    ElementRef,
    EventEmitter,
    Input,
    OnChanges,
    OnDestroy,
    Output,
    SimpleChanges,
    ViewChild
} from '@angular/core';
import {catchError, map, switchMap, takeUntil} from 'rxjs/operators';
import {
    CertificationFolder,
    CertificationFolderCdcStates,
    CertificationFolderErrorStates,
    CertificationFoldersCdcFiles,
    CertificationFoldersCdcFilesState,
    CertificationFolderStates,
    CertificationFolderType,
    EuropeanLanguageLevel
} from '../../api/models/certification-folder';
import {CertificationService} from '../../api/services/certification.service';
import {
    Certification,
    CertificationExaminationType,
    CertificationFolderFileType,
    CertificationTypes
} from '../../api/models/certification';
import {CertificationPartner, CertificationPartnerHabilitations} from '../../api/models/certification-partner';
import {CertificationPartnerService} from '../../api/services/certification-partner.service';
import {CertificationFolderService} from '../../api/services/certification-folder.service';
import {combineLatest, forkJoin, Observable, of, Subject, Subscription} from 'rxjs';
import {enumFromString} from '../../utils/object-utils';
import {AppFormFieldData, Choices} from '../../material/app-form-field/app-form-field.component';
import {TranslateService} from '@ngx-translate/core';
import {FormBuilder, FormGroup, FormGroupDirective, Validators} from '@angular/forms';
import moment from 'moment/moment';
import {FileListComponent} from '../../file/file-list/file-list.component';
import {Select} from '@ngxs/store';
import {OrganismState} from '../../api/state/organism.state';
import {Organism} from '../../api/models/organism';
import {CertificationFoldersCdcFilesService} from '../../api/services/certification-folders-cdc-files.service';
import {lowerCase, upperFirst} from 'lodash-es';
import {SubscriptionState} from '../../api/state/subscription.state';
import {Subscription as WedofSubscription} from '../../api/models/subscription';
import {EntityClass} from '../../utils/enums/entity-class';
import {Activity} from '../../api/models/activity';
import {setUpIntersectionObserver} from '../../utils/shortcut-side-panel-utils';
import {BaseCardCanDeactivateDirective} from '../../utils/base-card/base-card-can-deactivate.directive';
import {RequiredCallSuper} from '../../utils/base-card/base-card.directive';
import {OrganismService} from '../../api/services/organism.service';
import {CERTIFICATE_FILE_TYPE_ID} from '../../api/models/file';
import {displaySkillSetTitle, Skill, SkillType} from '../../api/models/skill';
import {SkillService} from '../../api/services/skill.service';
import {FormValidators} from '../../api/shared/form-validators';

@Component({
    selector: 'app-certification-folder-card',
    templateUrl: './certification-folder-card.component.html',
    styleUrls: ['./certification-folder-card.component.scss']
})
export class CertificationFolderCardComponent extends BaseCardCanDeactivateDirective implements OnChanges, OnDestroy, AfterViewInit {
    static COMPONENT_ID = 'dossierCertification';
    loading = false;
    isCertifier = false;
    certifierName: string;
    partnerName: string = null;
    panelOpenState = false;
    errorMessages: string[] = [];
    certificationName: string;
    certificationLink: string;
    isTrainingOrganism = false;
    examinationInformations: string;
    examinationDates: string;
    certificationFolderGradePass: string;
    certificationFolderType: string;
    maxDateForExport: string;
    subscription: WedofSubscription;
    currentCertificationFoldersCdcFiles: CertificationFoldersCdcFiles;
    formGroup: FormGroup;
    mainOrganism: Organism;
    certification: Certification;
    detailsSubscription$: Subscription;
    states = CertificationFolderStates;
    appFormFieldsData: AppFormFieldData[];
    errorStates = CertificationFolderErrorStates;
    certificationPartner: CertificationPartner = null;
    certificationFoldersCdcFilesState = CertificationFoldersCdcFilesState;
    certificationFolderCdcStates = CertificationFolderCdcStates;
    orderedCertificationFolderStates: CertificationFolderStates[] = Object.values(CertificationFolderStates);
    certificationTypes = CertificationTypes;
    certificationFolderFileTypes: CertificationFolderFileType[] = null;
    allSkillSetIds: number[] = null;
    certificationSkillSets: Skill[] = null;

    @Input() certificationFolder: CertificationFolder;
    @Input() openCard = false;
    @Output() certificationFolderChange: EventEmitter<CertificationFolder> = new EventEmitter<CertificationFolder>();
    @Output() certificationFolderDeleted: EventEmitter<any> = new EventEmitter<any>();
    @ViewChild('form') form: FormGroupDirective;
    @ViewChild('appFileList') appFileList: FileListComponent<CertificationFolder, CertificationFolderStates>;
    @Select(OrganismState.organism) organism$: Observable<Organism>;
    @Select(SubscriptionState.subscription) subscription$: Observable<WedofSubscription>;

    private _unsubscribeAll: Subject<void> = new Subject<void>();

    readonly EntityClass = EntityClass;

    constructor(
        private _fb: FormBuilder,
        private _skillService: SkillService,
        private _translateService: TranslateService,
        private _certificationService: CertificationService,
        private _organismService: OrganismService,
        private _certificationFolderService: CertificationFolderService,
        private _certificationPartnerService: CertificationPartnerService,
        private _certificationFoldersCdcFilesService: CertificationFoldersCdcFilesService,
        private _el: ElementRef
    ) {
        super(CertificationFolderCardComponent.COMPONENT_ID, _el);
    }


    canDeactivate(): boolean {
        return !this.loading && (this.form?.submitted || !this.formGroup?.dirty);
    }

    ngOnChanges(changes: SimpleChanges): void {
        this.panelLoading();
        this.initForm(this.certificationFolder);
        if (this.openCard) {
            this.openPanel();
        }
    }

    openPanel(): void {
        this.panelOpenState = true;
    }

    closePanel(): void {
        this.panelOpenState = false;
    }

    initForm(certificationFolder: CertificationFolder): void {
        this.formGroup = this._fb.group({});
        this.errorMessages = [];
        this.shortcutSidePanelLoaded.emit({name: 'dossierCertification', loaded: false});
        this.cardLoading = true;
        if (this.detailsSubscription$) {
            this.detailsSubscription$.unsubscribe(); // Cancel the previous loading if it was still pending (is that really necessary? no other way?)
        }
        if (!certificationFolder) {
            return;
        }

        this.detailsSubscription$ = combineLatest([this.organism$, this.subscription$]).pipe(
            takeUntil(this._unsubscribeAll),
            switchMap(([organism, subscription]: [Organism, WedofSubscription]):
            Observable<{
                certificationFolder2: CertificationFolder,
                certification: Certification,
                certificationPartner: CertificationPartner,
                organism: Organism,
                subscription: WedofSubscription,
                certificationSkillSets: Skill[],
                badgeAssertion: string
            }> => {
                return forkJoin({
                    certificationFolder2: certificationFolder.externalId !== this.certificationFolder?.externalId ?
                        this._certificationFolderService.get(certificationFolder.externalId) : of(certificationFolder),
                    certification: certificationFolder._links.certification.certifInfo !== this.certification?.certifInfo ?
                        this._certificationService.get(certificationFolder._links.certification.certifInfo) : of(this.certification),
                    certificationPartner: certificationFolder._links.partner?.siret ?
                        ((certificationFolder._links.partner.siret !== this.certificationPartner?._links?.partner?.siret) ?
                            this._certificationPartnerService.get({
                                certifInfo: certificationFolder._links.certification.certifInfo,
                                siret: certificationFolder._links.partner.siret
                            }).pipe(catchError(_ => of<CertificationPartner>(null)))
                            : of(this.certificationPartner)) : of<CertificationPartner>(null),
                    organism: of(organism),
                    subscription: of(subscription),
                    certificationSkillSets: certificationFolder._links.certification.certifInfo !== this.certification?.certifInfo ?
                        (
                            certificationFolder._links.certification.allowPartialSkillSets ?
                                this._skillService.list({certifInfo: certificationFolder._links.certification.certifInfo})
                                    .pipe(map(result => result.payload.filter((skillSet) => skillSet.type === SkillType.SKILL_SET)))
                                : of<Skill[]>(null)
                        )
                        : of(this.certificationSkillSets),
                    badgeAssertion: certificationFolder.badgeAssertion ?
                        this._certificationFolderService.getBadgeAssertionData(certificationFolder.externalId)
                        : of<string>(null)
                });
            })
        ).subscribe(({
                         certificationFolder2,
                         certification,
                         certificationPartner,
                         organism,
                         subscription,
                         certificationSkillSets,
                         badgeAssertion
                     }) => {
            this.certificationFolder = certificationFolder2;
            this.certification = certification;
            this.certificationPartner = certificationPartner;
            this.mainOrganism = organism;
            this.subscription = subscription;
            this.certificationSkillSets = certificationSkillSets;
            const imageUrlBadgeAssertion = badgeAssertion && badgeAssertion['imageUrl'] ? badgeAssertion['imageUrl'] : null;

            if (this.certificationFolder.state === CertificationFolderStates.SUCCESS) {
                this.currentCertificationFoldersCdcFiles = undefined;
                this._certificationFoldersCdcFilesService.getCurrentByCertificationFolder(this.certificationFolder.externalId).subscribe(currentCertificationFoldersCdcFiles => {
                    this.currentCertificationFoldersCdcFiles = currentCertificationFoldersCdcFiles;
                });
            } else {
                this.currentCertificationFoldersCdcFiles = null;
            }

            this.formGroup = this._fb.group({
                certificationFolder: this._fb.group({
                    comment: [],
                    files: this._fb.group({})
                })
            });

            const state = enumFromString(CertificationFolderStates, this.certificationFolder.state);
            const habilitation = this.certificationPartner != null ? enumFromString(CertificationPartnerHabilitations, this.certificationPartner.habilitation) : null;

            this.isCertifier = this.certificationFolder._links.certifier.siret === this.mainOrganism.siret;
            this.isTrainingOrganism = this.certificationPartner ? this.certificationFolder._links.partner.siret === this.mainOrganism.siret : false;
            const canEvaluate = this.isCertifier ||
                habilitation && [CertificationPartnerHabilitations.EVALUATE, CertificationPartnerHabilitations.TRAIN_EVALUATE].indexOf(habilitation) >= 0;
            const isToTakeOrLower = [CertificationFolderStates.TO_REGISTER, CertificationFolderStates.REGISTERED, CertificationFolderStates.TO_TAKE].indexOf(state) >= 0;
            const isToControlOrLower = [CertificationFolderStates.TO_REGISTER, CertificationFolderStates.REGISTERED,
                CertificationFolderStates.TO_TAKE, CertificationFolderStates.TO_RETAKE, CertificationFolderStates.TO_CONTROL].indexOf(state) >= 0;
            const isCertificationResult = [CertificationFolderStates.SUCCESS, CertificationFolderStates.FAILED, CertificationFolderStates.TO_RETAKE].indexOf(state) >= 0;
            const canUpdateExamination = canEvaluate && isToControlOrLower;
            const canUpdateComment = this.isCertifier || isToTakeOrLower || (canEvaluate && isToControlOrLower);
            const isLanguageCertification = this.certification.nsf?.find(codeNsf => codeNsf.code === '136');

            if (this.formGroup.controls['certificationFolder']) {
                this.formGroup.removeControl('certificationFolder');
            }
            this.formGroup.addControl('certificationFolder',
                this._fb.group({
                    files: this._fb.group({})
                })
            );

            this.examinationInformations = certificationFolder.examinationType && certificationFolder.examinationPlace ?
                certificationFolder.examinationType === CertificationExaminationType.A_DISTANCE ?
                    this._translateService.instant('private.common.certification.examinationType.A_DISTANCE') :
                    this._translateService.instant('private.common.certification.examinationType.' + certificationFolder.examinationType) +
                    ' (' + certificationFolder.examinationPlace + ')' :
                certificationFolder.examinationType && !certificationFolder.examinationPlace ?
                    this._translateService.instant('private.common.certification.examinationType.' + certificationFolder.examinationType) :
                    !certificationFolder.examinationType && certificationFolder.examinationPlace && certificationFolder.examinationPlace;

            this.certificationFolderGradePass = certificationFolder.gradePass &&
                this._translateService.instant('private.common.certificationFolder.gradesPass.' + certificationFolder.gradePass);
            this.certificationFolderType = certificationFolder.type === CertificationFolderType.POLE_EMPLOI ?
                this._translateService.instant('private.certification.folders.createFolder.form.type.types.poleEmploi') : certificationFolder.type ?
                    this._translateService.instant('private.certification.folders.createFolder.form.type.types.' + certificationFolder.type.toLowerCase()) : null;

            this.certifierName = this.certificationFolder._links.certifier.name;
            this.certificationLink = this.certification.link ?? ([CertificationTypes.RS, CertificationTypes.RNCP].includes(this.certification.type)) ? 'https://www.francecompetences.fr/recherche/' + this.certification.type.toLowerCase() + '/' + this.certification.code : null;
            this.partnerName = this.certificationPartner ? this.certificationFolder._links.partner.name : null;
            if (this.partnerName === '[ND]') {
                this.partnerName = this.certificationFolder._links.partner.siret;
            }
            this.certificationName = (this.certification.externalId ? this.certification.externalId + ' - ' : '') + this.certification.name
                + (!certification.enabled ? (' (' + this._translateService.instant('private.certification.partners.enabled.' + certification.enabled) + ')') : '');
            this.maxDateForExport = certificationFolder.state === CertificationFolderStates.SUCCESS && moment(certificationFolder.issueDate).add(3, 'months').format('DD/MM/YYYY');
            if (certificationFolder.examinationDate) {
                this.examinationDates = !certificationFolder.examinationEndDate || certificationFolder.examinationDate === certificationFolder.examinationEndDate ?
                    this._translateService.instant('private.common.dates.fromToSameDay', {date: moment(certificationFolder.examinationDate).format('DD/MM/YYYY')}) :
                    this._translateService.instant('private.common.dates.fromTo', {
                            startDate: moment(certificationFolder.examinationDate).format('DD/MM/YYYY'),
                            endDate: moment(certificationFolder.examinationEndDate).format('DD/MM/YYYY')
                        }
                    );
            } else {
                this.examinationDates = null;
            }

            this.certificationFolderFileTypes = (certificationPartner?.skillSets.length || certificationFolder.skillSets.length) ?
                certification.certificationFolderFileTypes.filter(fileType => fileType.id !== CERTIFICATE_FILE_TYPE_ID) : certification.certificationFolderFileTypes;

            let skillSetValues = null;
            let skillSetChoices: Choices = [];
            this.allSkillSetIds = this.certificationPartner?.skillSets?.length ?
                this.certificationPartner.skillSets.map((skillSet) => skillSet.id) : this.certificationSkillSets?.map((skillSet) => skillSet.id);
            const readOnlySkillSets = !this.isCertifier || (certificationFolder._links?.registrationFolder != null && certificationFolder.skillSets.length > 0);
            if (this.certificationSkillSets) {
                // set choices
                if (this.certificationPartner?.skillSets?.length) {
                    skillSetChoices = this.getSkillSetChoices(this.certificationPartner.skillSets, ' du partenariat');
                } else {
                    skillSetChoices = this.getSkillSetChoices(this.certificationSkillSets, ' de la certification');
                }
                // set value
                if (readOnlySkillSets) {
                    if (this.isCertifier) {
                        skillSetValues = certificationFolder.skillSets.length === this.certificationSkillSets.length ? 'Tous les blocs de compétences' :
                            certificationFolder.skillSets.map((skill) => displaySkillSetTitle(skill)).join(', ');
                    } else {
                        skillSetValues = null;
                    }
                } else if (!certificationFolder.skillSets.length) {
                    skillSetValues = null;
                } else if (certificationFolder.skillSets.length === this.certificationSkillSets.length ||
                    (certificationPartner && (certificationFolder.skillSets.length === certificationPartner?.skillSets?.length))
                ) {
                    skillSetValues = ['*'];
                } else {
                    skillSetValues = certificationFolder.skillSets.map((skill) => skill.id);
                }
            }

            this.appFormFieldsData = [
                {
                    controlName: 'externalId',
                    copy: true,
                    disabled: true,
                    label: 'private.training-organism.folders.common.externalId',
                    type: 'text',
                    icon: 'folder_open',
                    value: certificationFolder.externalId,
                    colSpan: (this.isCertifier && certification.type === CertificationTypes.RNCP && certification.allowPartialSkillSets) ? 6 : 3
                },
                {
                    controlName: 'certifier',
                    removed: this.isCertifier,
                    disabled: true,
                    type: 'text',
                    label: 'private.common.certificationFolder.certifier',
                    icon: 'business',
                    value: this.certifierName,
                    colSpan: 3
                },
                {
                    controlName: 'certification',
                    disabled: true,
                    type: this.certificationLink ? 'url' : 'text',
                    label: 'private.certification.folders.createFolder.form.certification.certification',
                    icon: 'fact_check',
                    value: this.certificationName,
                    href: this.certificationLink,
                    colSpan: (this.isCertifier && certification.type === CertificationTypes.RNCP && certification.allowPartialSkillSets) || !this.isCertifier ? 3 : 6
                },
                {
                    // TODO(skillset) required / removable ?
                    controlName: 'skillSets',
                    disabled: readOnlySkillSets,
                    type: readOnlySkillSets ? 'text' : 'select',
                    removed: certification.allowPartialSkillSets === false,
                    removable: true,
                    value: skillSetValues,
                    label: 'private.certification.folders.createFolder.form.skillsSet.title',
                    help: 'private.certification.folders.createFolder.form.skillsSet.help',
                    colSpan: 3,
                    placeholder: readOnlySkillSets ? '' : 'private.certification.folders.createFolder.form.skillsSet.placeholder',
                    searchMultiple: true,
                    choices: skillSetChoices,
                    change: (controlName, newValue, formData) => {
                        const appFormFieldSkillSets = formData.find(field => field.controlName === 'skillSets');
                        if (newValue) {
                            if (newValue.includes('*') || newValue.sort().toString() === this.allSkillSetIds.sort().toString()) {
                                appFormFieldSkillSets.choices.forEach((choice) => {
                                    if (choice.value !== '*') {
                                        choice.disabled = true;
                                    }
                                });
                                appFormFieldSkillSets.value = ['*'];
                            } else {
                                appFormFieldSkillSets.choices.forEach((choice) => choice.disabled = false);
                                appFormFieldSkillSets.value = newValue;
                            }
                        } else {
                            appFormFieldSkillSets.value = null;
                        }
                        return [appFormFieldSkillSets];
                    }
                },
                {
                    controlName: 'type',
                    disabled: !this.isCertifier || isCertificationResult,
                    label: 'private.certification.folders.createFolder.form.type.label',
                    help: 'private.certification.folders.createFolder.form.type.help',
                    type: 'select',
                    icon: 'folder',
                    value: certificationFolder.type,
                    colSpan: 3,
                    choices: [
                        {
                            key: this._translateService.instant('private.certification.folders.createFolder.form.type.types.certifie'),
                            value: CertificationFolderType.CERTIFIE
                        },
                        {
                            key: this._translateService.instant('private.certification.folders.createFolder.form.type.types.of'),
                            value: CertificationFolderType.OF
                        },
                        {
                            key: this._translateService.instant('private.certification.folders.createFolder.form.type.types.poleEmploi'),
                            value: CertificationFolderType.POLE_EMPLOI
                        },
                        {
                            key: this._translateService.instant('private.certification.folders.createFolder.form.type.types.employeur'),
                            value: CertificationFolderType.EMPLOYEUR
                        },
                        {
                            key: this._translateService.instant('private.certification.folders.createFolder.form.type.types.autre'),
                            value: CertificationFolderType.AUTRE
                        }
                    ]
                },
                {
                    controlName: 'partner',
                    disabled: !this.isCertifier || !!this.certificationFolder._links.registrationFolder,
                    required: false,
                    removable: true,
                    value: this.certificationFolder._links.partner ? {
                        name: this.certificationFolder._links.partner.name,
                        siret: this.certificationFolder._links.partner.siret
                    } : null,
                    label: 'private.certification.folders.createFolder.form.partner.partner',
                    type: 'search',
                    placeholder: 'private.certification.folders.createFolder.form.partner.placeholderPartner',
                    help: (!this.isCertifier || !!this.certificationFolder._links.registrationFolder) ? null : 'private.certification.folders.createFolder.form.partner.tooltip',
                    searchNoEntriesFoundLabel: 'private.certification.folders.createFolder.form.partner.noPartner',
                    searchMethod: searchTerm => {
                        if (searchTerm) {
                            return this._organismService.find({
                                query: searchTerm,
                                limit: 15,
                                certifInfo: this.certification.certifInfo
                            }).pipe(
                                map(result => result.payload)
                            );
                        } else {
                            return of([]);
                        }
                    },
                    searchComparisonProperty: 'siret',
                    searchResultFormatter: (partner: Organism) => {
                        return partner.name + ' (...' + partner.siret.substr(partner.siret.length - 5) + ')';
                    },
                    colSpan: 3,
                },
                {
                    controlName: 'enrollmentDate',
                    disabled: !canUpdateExamination,
                    type: 'date',
                    label: 'private.common.certificationFolder.enrollmentDate',
                    icon: 'event',
                    max: certificationFolder?.examinationDate,
                    change: this.checkExaminationDates,
                    colSpan: this.isCertifier && this.isTrainingOrganism ? 6 : 3
                },
                {
                    controlName: 'amountHt',
                    placeholder: this.certificationPartner?.amountHt ? this.certificationPartner.amountHt.toString() :
                        this.certification.amountHt ? this.certification.amountHt.toString() : null,
                    disabled: !this.isCertifier,
                    removed: this.isCertifier && this.isTrainingOrganism,
                    type: 'price',
                    label: 'private.common.certificationFolder.amountHt',
                    help: 'private.common.certificationFolder.amountHtToolTip',
                    icon: 'euro_symbol',
                    min: 0,
                    colSpan: 3
                },
                {
                    controlName: 'examinationDate',
                    removed: !canUpdateExamination,
                    required: state === CertificationFolderStates.TO_CONTROL,
                    removable: true,
                    type: 'datetime-local',
                    label: 'private.common.certificationFolder.examinationDate',
                    icon: 'event',
                    validatorsMessages: {
                        min: 'common.errors.date',
                    },
                    min: moment(new Date(certificationFolder?.examinationDate)),
                    change: this.checkExaminationDates,
                    colSpan: 3,
                },
                {
                    controlName: 'examinationEndDate',
                    removed: !canUpdateExamination,
                    removable: true,
                    type: 'datetime-local',
                    label: 'private.common.certificationFolder.examinationEndDate',
                    icon: 'event',
                    validatorsMessages: {
                        min: 'common.errors.date',
                    },
                    min: moment(new Date(certificationFolder?.examinationDate)),
                    change: this.checkExaminationDates,
                    colSpan: 3,
                },
                {
                    controlName: 'examinationDates',
                    removed: canUpdateExamination,
                    disabled: true,
                    type: 'text',
                    label: 'private.common.certificationFolder.examinationDates',
                    icon: 'event',
                    value: this.examinationDates
                },
                {
                    controlName: 'examinationInfos',
                    removed: canUpdateExamination,
                    disabled: true,
                    value: this.examinationInformations,
                    type: 'text',
                    icon: 'school',
                    label: 'private.common.certificationFolder.examinationDetails'
                },
                {
                    controlName: 'examinationType',
                    removed: !canUpdateExamination,
                    required: state === CertificationFolderStates.TO_CONTROL,
                    type: 'select',
                    icon: 'location_city',
                    label: 'private.common.certification.examinationType.title',
                    choices: [
                        {
                            key: this._translateService.instant('private.common.certification.examinationType.A_DISTANCE'),
                            value: CertificationExaminationType.A_DISTANCE
                        },
                        {
                            key: this._translateService.instant('private.common.certification.examinationType.EN_PRESENTIEL'),
                            value: CertificationExaminationType.EN_PRESENTIEL
                        },
                        {
                            key: this._translateService.instant('private.common.certification.examinationType.MIXTE'),
                            value: CertificationExaminationType.MIXTE
                        },
                    ],
                    colSpan: 3,
                },
                {
                    controlName: 'examinationPlace',
                    removed: !canUpdateExamination,
                    type: this.certificationFolder.examinationPlace && this.certificationFolder.examinationPlace.startsWith('http') ? 'url' : 'text',
                    label: 'private.common.certificationFolder.examinationPlace',
                    icon: 'school',
                    colSpan: 3,
                },
                {
                    controlName: 'tiersTemps',
                    removed: !canEvaluate,
                    disabled: !canUpdateExamination,
                    class: 'app-form-field-radio-inline',
                    inline: true,
                    type: canUpdateExamination ? 'radio' : 'text',
                    value: canUpdateExamination ? this.certificationFolder.tiersTemps : this._translateService.instant('common.actions.' + this.certificationFolder.tiersTemps),
                    label: 'private.common.certificationFolder.tiersTemps',
                    choices: [
                        {key: this._translateService.instant('common.actions.yes'), value: true},
                        {key: this._translateService.instant('common.actions.no'), value: false}
                    ],
                    colSpan: 3,
                },
                {
                    controlName: 'detailedResult',
                    removed: !isCertificationResult,
                    disabled: true,
                    type: 'textarea',
                    label: 'private.common.certificationFolder.detailedResult',
                    icon: 'loyalty',
                    maxLength: 5000
                },
                {
                    controlName: 'europeanLanguageLevel',
                    value: certificationFolder.europeanLanguageLevel === EuropeanLanguageLevel.INSUFFISANT ?
                        upperFirst(lowerCase(EuropeanLanguageLevel.INSUFFISANT)) : certificationFolder.europeanLanguageLevel,
                    removed: !isLanguageCertification || !isCertificationResult,
                    disabled: true,
                    type: 'text',
                    label: 'private.common.certificationFolder.europeanLanguageLevel',
                    icon: 'grading'
                },
                {
                    controlName: 'issueDate',
                    removed: [CertificationFolderStates.SUCCESS].indexOf(state) === -1,
                    disabled: true,
                    type: 'date',
                    label: certificationFolder.certificateId ? this._translateService.instant('private.common.certificationFolder.issueDateCertificate',
                        {certificateId: certificationFolder.certificateId}) : 'private.common.certificationFolder.issueDate',
                    icon: 'event',
                    colSpan: 3
                },
                {
                    controlName: 'gradePass',
                    removed: [CertificationFolderStates.SUCCESS].indexOf(state) === -1,
                    disabled: true,
                    type: 'text',
                    label: 'private.common.certificationFolder.gradePassLabel',
                    icon: 'school',
                    value: this.certificationFolderGradePass,
                    colSpan: 3
                },
                {
                    controlName: 'digitalProofLink',
                    removed: [CertificationFolderStates.SUCCESS].indexOf(state) === -1,
                    disabled: true,
                    type: 'url',
                    label: 'private.common.certificationFolder.digitalProofLink',
                    icon: 'badge',
                    copy: true
                },
                {
                    controlName: 'imageBadge',
                    removed: !imageUrlBadgeAssertion,
                    value: imageUrlBadgeAssertion,
                    type: 'image',
                    label: 'private.common.certificationFolder.badgeAssertion',
                    colSpan: 1,
                    href: certificationFolder.badgeAssertion
                },
                {
                    controlName: 'badgeAssertion',
                    removed: [CertificationFolderStates.SUCCESS].indexOf(state) === -1,
                    disabled: !this.isCertifier,
                    removable: true,
                    required: false,
                    type: 'url',
                    colSpan: certificationFolder.badgeAssertion && imageUrlBadgeAssertion ? 5 : 6,
                    label: certificationFolder.badgeAssertion && imageUrlBadgeAssertion ? ' ' : 'private.common.certificationFolder.badgeAssertion',
                    validators: [Validators.pattern(FormValidators.URL_PATTERN)],
                    validatorsMessages: {
                        pattern: 'common.errors.url'
                    },
                    change: (controlName, newValue, formData) => {
                        const appFormFieldImageBadge = formData.find(field => field.controlName === 'imageBadge');
                        appFormFieldImageBadge.removed = true;
                        const appFormFieldBadgeAssertion = formData.find(field => field.controlName === 'badgeAssertion');
                        appFormFieldBadgeAssertion.value = newValue;
                        appFormFieldBadgeAssertion.colSpan = 6;
                        appFormFieldBadgeAssertion.label = this._translateService.instant('private.common.certificationFolder.badgeAssertion');
                        return [appFormFieldImageBadge, appFormFieldBadgeAssertion];
                    }
                },
                {
                    controlName: 'expirationDate',
                    removed: [CertificationFolderStates.SUCCESS].indexOf(state) === -1 || this.certificationFolder.expirationDate == null,
                    disabled: true,
                    type: 'date',
                    label: 'private.common.certificationFolder.expirationDate',
                    icon: 'event'
                },
                {
                    controlName: 'comment',
                    disabled: !canUpdateComment,
                    type: 'textarea',
                    label: 'private.common.certificationFolder.comment',
                    icon: 'text',
                    maxLength: 5000
                },
                {
                    controlName: 'tags',
                    disabled: !this.isCertifier,
                    label: 'common.tags.label',
                    placeholder: this.isCertifier ? 'common.tags.placeholder' : 'private.common.form.placeholder',
                    type: 'tags',
                    isCreateAvailable: true,
                },
                {
                    controlName: 'cdcExcluded',
                    help: 'private.common.certificationFolder.cdcAccrochageHelp',
                    removed: this.certification.type === CertificationTypes.INTERNAL ||
                        !this.isCertifier ||
                        this.certificationFolder.cdcState === CertificationFolderCdcStates.PROCESSED_OK ||
                        [CertificationFolderStates.FAILED, CertificationFolderStates.ABORTED, CertificationFolderStates.REFUSED].indexOf(state) !== -1 ||
                        !certificationFolder.fullCertification,
                    label: 'private.common.certificationFolder.cdcExcluded',
                    type: 'radio',
                    inline: true,
                    required: true,
                    choices: [
                        {key: this._translateService.instant('common.actions.yes'), value: false},
                        {key: this._translateService.instant('common.actions.no'), value: true},
                    ],
                }
            ];

            this.panelLoaded();
        });

    }

    refresh(certificationFolder: CertificationFolder): void {
        this.certificationFolder = certificationFolder;
        this.certificationFolderChange.emit(certificationFolder);
    }

    delete(certificationFolder: CertificationFolder): void {
        this.certificationFolder = certificationFolder;
        this.certificationFolderDeleted.emit(certificationFolder);
        this.certificationFolder = null;
    }

    includes(canceledOrRefusedStates: any, state: CertificationFolderStates): boolean {
        return Object.values(canceledOrRefusedStates).includes(state);
    }

    setLoading(loading: boolean): void {
        this.loading = loading;
    }

    setErrorMessages(error: string[]): void {
        this.errorMessages = error;
    }

    checkExaminationDates(controlName, newValue, formData, formGroup): any[] {
        // Be careful, can't use "this" here as it is called a lambda from another class
        const today = moment();
        const appFormFieldExaminationDate = formData.find(field => field.controlName === 'examinationDate');
        const appFormFieldExaminationEndDate = formData.find(field => field.controlName === 'examinationEndDate');
        const appFormFieldEnrollmentDate = formData.find(field => field.controlName === 'enrollmentDate');

        const examinationDate = controlName === 'examinationDate' ? newValue : formGroup.get('certificationFolder.examinationDate')?.value;
        const examinationEndDate = controlName === 'examinationEndDate' ? newValue : formGroup.get('certificationFolder.examinationEndDate')?.value;
        const enrollmentDate = controlName === 'enrollmentDate' ? newValue : formGroup.get('certificationFolder.enrollmentDate')?.value;

        appFormFieldEnrollmentDate.value = enrollmentDate ? moment(new Date(enrollmentDate)).toISOString() : today.toISOString();
        appFormFieldEnrollmentDate.max = today < moment(examinationDate) ? today : moment(examinationDate);
        appFormFieldExaminationDate.value = examinationDate ? moment(new Date(examinationDate)).toISOString() : appFormFieldExaminationDate.value;
        appFormFieldExaminationDate.min = moment(new Date(appFormFieldEnrollmentDate.value));
        appFormFieldExaminationEndDate.value = examinationEndDate ? moment(new Date(examinationEndDate)).toISOString() : appFormFieldExaminationDate.value;
        appFormFieldExaminationEndDate.min = moment(new Date(appFormFieldExaminationDate.value));
        return;
    }

    createdActivity(activity: Activity): void {
        this.certificationFolderChange.emit({...this.certificationFolder});
    }

    ngAfterViewInit(): void {
        this._intersectionObserver = setUpIntersectionObserver('dossierCertification', this.elementIntersection);
        this._intersectionObserver.observe(this._el.nativeElement as HTMLElement);
    }

    ngOnDestroy(): RequiredCallSuper {
        return super.ngOnDestroy();
    }

    getSkillSetChoices(skillSets: Skill[], type: string): Choices {
        const choices: Choices = [{
            key: 'Tous les blocs de compétences ' + type,
            value: '*'
        }];
        skillSets.forEach((skillSet) => {
            choices.push({
                value: skillSet.id,
                key: displaySkillSetTitle(skillSet)
            });
        });
        return choices;
    }

    getSkillSetTitle(): string {
        return this.certificationFolder.skillSets.map((skill) => displaySkillSetTitle(skill)).join(', ');
    }
}
