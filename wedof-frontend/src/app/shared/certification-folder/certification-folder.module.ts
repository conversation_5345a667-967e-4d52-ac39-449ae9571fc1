import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {CertificationFolderTableComponent} from './certification-folder-table/certification-folder-table.component';
import {MaterialModule} from '../material/material.module';
import {MarkdownModule} from 'ngx-markdown';
import {TranslateModule} from '@ngx-translate/core';
import {ReactiveFormsModule} from '@angular/forms';
import {TreoMessageModule} from '../../../@treo/components/message';
import {CertificationFolderRegisteredDialogComponent} from './dialogs/certification-folder-registered-dialog/certification-folder-registered-dialog.component';
import {CertificationFolderToTakeDialogComponent} from './dialogs/certification-folder-to-take-dialog/certification-folder-to-take-dialog.component';
import {CertificationFolderRefusedDialogComponent} from './dialogs/certification-folder-refused-dialog/certification-folder-refused-dialog.component';
import {CertificationFolderSuccessDialogComponent} from './dialogs/certification-folder-success-dialog/certification-folder-success-dialog.component';
import {CertificationFolderFailedDialogComponent} from './dialogs/certification-folder-failed-dialog/certification-folder-failed-dialog.component';
import {CertificationFolderAbortedDialogComponent} from './dialogs/certification-folder-aborted-dialog/certification-folder-aborted-dialog.component';
import {CertificationFolderToControlDialogComponent} from './dialogs/certification-folder-to-control-dialog/certification-folder-to-control-dialog.component';
import {CertificationFolderToRegisterDialogComponent} from './dialogs/certification-folder-to-register-dialog/certification-folder-to-register-dialog.component';
import {CertificationFolderToRetakeDialogComponent} from './dialogs/certification-folder-to-retake-dialog/certification-folder-to-retake-dialog.component';
import {CertificationFolderCardComponent} from './certification-folder-card/certification-folder-card.component';
import {CertificationFolderMenuComponent} from './certification-folder-menu/certification-folder-menu.component';
import {MatCardModule} from '@angular/material/card';
import {DndModule} from '../directives/DndModule';
import {PipesModule} from '../pipes/pipes.module';
import {RouterModule} from '@angular/router';
import {AttendeeModule} from '../attendee/attendee.module';
import {RegistrationFolderModule} from '../registration-folder/registration-folder.module';
import {MatTooltipModule} from '@angular/material/tooltip';
import {CertificationFolderCreateComponent} from './certification-folder-create/certification-folder-create.component';
import {CertificationFolderAdvancedSearchComponent} from './certification-folder-advanced-search/certification-folder-advanced-search.component';
import {FileModule} from '../file/file.module';
import {ActivitiesModule} from '../activities/activities.module';
import {CertificationFolderCardAttendeeComponent} from './certification-folder-card-attendee/certification-folder-card-attendee.component';
import {CertificationFolderImportDialogComponent} from './certification-folder-import-dialog/certification-folder-import-dialog.component';
import {
    CertificationFolderUpdateDialogComponent
} from './dialogs/certification-folder-update-dialog/certification-folder-update-dialog.component';


@NgModule({
    declarations: [
        CertificationFolderTableComponent,
        CertificationFolderCardComponent,
        CertificationFolderRegisteredDialogComponent,
        CertificationFolderToTakeDialogComponent,
        CertificationFolderRefusedDialogComponent,
        CertificationFolderSuccessDialogComponent,
        CertificationFolderFailedDialogComponent,
        CertificationFolderAbortedDialogComponent,
        CertificationFolderToControlDialogComponent,
        CertificationFolderToRegisterDialogComponent,
        CertificationFolderToRetakeDialogComponent,
        CertificationFolderMenuComponent,
        CertificationFolderCreateComponent,
        CertificationFolderAdvancedSearchComponent,
        CertificationFolderCardAttendeeComponent,
        CertificationFolderImportDialogComponent,
        CertificationFolderUpdateDialogComponent
    ],
    imports: [
        CommonModule,
        MaterialModule,
        MarkdownModule,
        TranslateModule,
        ReactiveFormsModule,
        TreoMessageModule,
        MatCardModule,
        DndModule,
        PipesModule,
        RouterModule,
        AttendeeModule,
        RegistrationFolderModule,
        MatTooltipModule,
        FileModule,
        ActivitiesModule
    ],
    exports: [
        CertificationFolderTableComponent,
        CertificationFolderCardComponent,
        CertificationFolderCreateComponent,
        CertificationFolderAdvancedSearchComponent,
        CertificationFolderMenuComponent,
        CertificationFolderCardAttendeeComponent
    ]
})
export class CertificationFolderModule {
}
