<form (ngSubmit)="submit()" [formGroup]="formGroup">
    <app-dialog-layout [title]="'private.training-organism.folders.dialog.title.' + action | translate"
                       [actions]="actions"
                       [cancelText]="'common.actions.no'"
                       [disabled]="loading"
                       [errorMessages]="errorMessages"
                       [warningMessage]="warningMessage"
                       (dialogClose)="close()"
                       [feedbackMessage]="feedbackMessage">

        <p class="pb-4">
            {{ "private.training-organism.folders.dialog.content." + action | translate }}
        </p>

        <ng-container *ngIf="dialogData.canEvaluate">
            <mat-form-field class="flex-auto" appearance="fill">
                <mat-label>
                    {{ 'private.common.certificationFolder.enrollmentDate' | translate}}
                </mat-label>
                <input formControlName="enrollmentDate" matInput
                       [max]="today < formGroup.get('examinationDate').value ? today : formGroup.get('examinationDate').value"
                       [matDatepicker]="enrollmentDatePicker"
                       [placeholder]="'private.common.certificationFolder.enrollmentDate' | translate">
                <mat-icon [matTooltipPosition]="'above'"
                          [matTooltip]="'private.common.certificationFolder.tooltip.enrollmentDate' | translate"
                          matPrefix
                          svgIcon="help_outline"></mat-icon>
                <mat-datepicker-toggle matSuffix [for]="enrollmentDatePicker"></mat-datepicker-toggle>
                <mat-datepicker #enrollmentDatePicker></mat-datepicker>
                <mat-error class="flex-auto gt-xs:pr-3" *ngIf="enrollmentDatePicker._getMaxDate() ">
                    {{'common.errors.date' | translate }}
                </mat-error>
            </mat-form-field>

            <mat-form-field class="flex-auto" appearance="fill">
                <mat-label>
                    {{ 'private.common.certificationFolder.examinationDate' | translate}}
                </mat-label>
                <input formControlName="examinationDate" matInput
                       [min]="formGroup.get('enrollmentDate')?.value"
                       (dateInput)="formatDateWithoutSeconds($event)"
                       (click)="examinationDatePicker.open()"
                       [ngxMatDatetimePicker]="examinationDatePicker"
                       [placeholder]="'private.common.certificationFolder.examinationDate' | translate">
                <mat-icon [matTooltipPosition]="'above'"
                          [matTooltip]="'private.common.certificationFolder.tooltip.examinationDate' | translate"
                          matPrefix
                          svgIcon="help_outline"></mat-icon>
                <mat-datepicker-toggle matSuffix [for]="examinationDatePicker"></mat-datepicker-toggle>
                <ngx-mat-datetime-picker #examinationDatePicker [enableMeridian]="false"></ngx-mat-datetime-picker>
                <mat-error class="flex-auto gt-xs:pr-3" *ngIf="examinationDatePicker._minDate">
                    {{'common.errors.date' | translate }}
                </mat-error>
            </mat-form-field>

            <mat-form-field class="flex-auto" appearance="fill">
                <mat-label>
                    {{ 'private.common.certificationFolder.examinationEndDate' | translate}}
                </mat-label>
                <input formControlName="examinationEndDate" matInput [min]="formGroup.get('examinationDate')?.value"
                       (click)="examinationEndDatePicker.open()"
                       (dateInput)="formatDateWithoutSeconds($event)"
                       [ngxMatDatetimePicker]="examinationEndDatePicker"
                       [placeholder]="'private.common.certificationFolder.examinationEndDate' | translate">
                <mat-icon [matTooltipPosition]="'above'"
                          [matTooltip]="'private.common.certificationFolder.tooltip.examinationEndDate' | translate"
                          matPrefix
                          svgIcon="help_outline"></mat-icon>
                <mat-datepicker-toggle matSuffix [for]="examinationEndDatePicker"></mat-datepicker-toggle>
                <ngx-mat-datetime-picker #examinationEndDatePicker [enableMeridian]="false"></ngx-mat-datetime-picker>
                <mat-error class="flex-auto gt-xs:pr-3" *ngIf="examinationEndDatePicker._minDate">
                    {{'common.errors.date' | translate }}
                </mat-error>
            </mat-form-field>

            <mat-form-field class="flex-auto">
                <mat-label>
                    {{ 'private.common.certification.examinationType.title' | translate}}
                </mat-label>
                <mat-select class="flex flex-col" formControlName="examinationType"
                            (change)="changeExaminationType($event)"
                >
                    <mat-option *ngFor="let certificationExaminationType of certificationExaminationTypes"
                                [value]="certificationExaminationType.value">
                        {{certificationExaminationType.label | translate}}
                    </mat-option>
                </mat-select>
            </mat-form-field>

            <mat-form-field class="flex-auto">
                <mat-label>
                    {{ 'private.common.certificationFolder.examinationPlace' | translate}}
                </mat-label>
                <input formControlName="examinationPlace"
                       matInput
                       type="text"/>
                <mat-icon matPrefix svgIcon="school"></mat-icon>
            </mat-form-field>

            <mat-form-field class="flex-auto">
                <mat-label>
                    {{ 'private.common.certificationFolder.tiersTemps' | translate}}
                </mat-label>
                <mat-select formControlName="tiersTemps" [value]="formGroup.get('tiersTemps').value">
                    <mat-option [value]="true">{{ 'common.actions.yes' | translate }}
                    </mat-option>
                    <mat-option [value]="false" class="ml-2">{{ 'common.actions.no'  | translate }}
                    </mat-option>
                </mat-select>
            </mat-form-field>
        </ng-container>

        <mat-form-field class="flex-auto" *ngIf="dialogData.commentUpdatable">
            <mat-label>
                {{ 'private.common.certificationFolder.comment' | translate}}
            </mat-label>
            <textarea formControlName="comment" matInput></textarea>
            <mat-icon matPrefix svgIcon="loyalty"></mat-icon>
        </mat-form-field>

        <ng-template #actions>
            <button type="submit" mat-flat-button color="primary" (click)="submit()"
                    [disabled]="loading || !formGroup.valid">
                <mat-progress-spinner *ngIf="loading; else submitLabel" [diameter]="24" mode="indeterminate">
                </mat-progress-spinner>
                <ng-template #submitLabel>
                    {{ 'common.actions.yes' | translate }}
                </ng-template>
            </button>
        </ng-template>

    </app-dialog-layout>
</form>
