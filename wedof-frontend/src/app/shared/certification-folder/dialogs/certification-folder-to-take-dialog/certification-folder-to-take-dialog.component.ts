import {Component, Inject, Injector, OnInit} from '@angular/core';
import {MAT_DIALOG_DATA} from '@angular/material/dialog';
import {CertificationFolder, CertificationFolderExamToTake} from '../../../api/models/certification-folder';
import {CertificationFolderDialogComponent} from '../certification-folder-dialog-form-component';
import moment, {Moment} from 'moment';
import {MatDatetimePickerInputEvent} from '@angular-material-components/datetime-picker';

@Component({
    selector: 'app-registration-folder-to-take-dialog',
    templateUrl: './certification-folder-to-take-dialog.component.html',
    styleUrls: ['./certification-folder-to-take-dialog.component.scss']
})
export class CertificationFolderToTakeDialogComponent extends CertificationFolderDialogComponent<CertificationFolderExamToTake> implements OnInit {

    today: Moment;

    constructor(
        injector: Injector,
        @Inject(MAT_DIALOG_DATA) private _dialogData: {
            folder: CertificationFolder,
            plural: boolean,
            folderCount: number,
            prefixAction: string,
            commentUpdatable: boolean,
            canEvaluate: boolean
        },
    ) {
        super(injector, _dialogData);
        this.today = moment();
    }

    protected initForm(): void {
        this.formGroup = this._fb.group({
            enrollmentDate: [this._dialogData.folder.enrollmentDate ?? new Date()],
            examinationPlace: [this._dialogData.folder.examinationPlace],
            examinationType: [this._dialogData.folder.examinationType],
            examinationDate: [this._dialogData.folder.examinationDate],
            examinationEndDate: [this._dialogData.folder.examinationEndDate],
            comment: [this._dialogData.folder.comment],
            tiersTemps: [this._dialogData.folder.tiersTemps]
        });
    }

    submit(): void {
        this.errorMessages = [];
        if (this.formGroup.valid) {
            this.loading = true;
            const data: CertificationFolderExamToTake = {
                comment: this.formGroup.value.comment,
                examinationPlace: this.formGroup.value.examinationPlace,
                examinationType: this.formGroup.value.examinationType,
                examinationDate: this.formGroup.value.examinationDate,
                examinationEndDate: this.formGroup.value.examinationEndDate,
                enrollmentDate: this.formGroup.value.enrollmentDate,
                tiersTemps: this.formGroup.value.tiersTemps
            };
            this.submitted$.next(data);
        }
    }

    changeExaminationType($event): void {
        this.formGroup.get('examinationType').setValue($event.value);
    }

    formatDateWithoutSeconds($event: MatDatetimePickerInputEvent<any>): void {
        if ($event.value) {
            $event.value.setSeconds(0);
        }
    }
}
