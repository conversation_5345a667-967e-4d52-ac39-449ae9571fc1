import {Component, Inject, Injector, OnInit} from '@angular/core';
import {MAT_DIALOG_DATA} from '@angular/material/dialog';
import {CertificationFolder, CertificationFolderExamToControl} from '../../../api/models/certification-folder';
import {Validators} from '@angular/forms';
import moment, {Moment} from 'moment';
import {CertificationFolderDialogComponent} from '../certification-folder-dialog-form-component';
import {MatDatetimePickerInputEvent} from '@angular-material-components/datetime-picker';

@Component({
    selector: 'app-registration-folder-to-retake-dialog',
    templateUrl: './certification-folder-to-control-dialog.component.html',
    styleUrls: ['./certification-folder-to-control-dialog.component.scss']
})
export class CertificationFolderToControlDialogComponent extends  CertificationFolderDialogComponent<CertificationFolderExamToControl> implements OnInit {
    today: Moment;

    constructor(
        injector: Injector,
        @Inject(MAT_DIALOG_DATA) private _dialogData: {
            folder: CertificationFolder,
            skipToControl: boolean,
            plural: boolean,
            folderCount: number,
            prefixAction: string,
            commentUpdatable: boolean,
            canEvaluate: boolean,
            warningMessage: string
        },
    ) {
        super(injector, _dialogData);
        this.today = moment();
    }

    submit(): void {
        this.errorMessages = [];
        if (this.formGroup.valid) {
            this.loading = true;
            const data: CertificationFolderExamToControl = {
                comment: this.formGroup.value.comment,
                examinationPlace: this.formGroup.value.examinationPlace,
                examinationType: this.formGroup.value.examinationType,
                examinationDate: this.formGroup.value.examinationDate,
                examinationEndDate: this.formGroup.value.examinationEndDate,
                enrollmentDate: this.formGroup.value.enrollmentDate
            };
            this.submitted$.next(data);
        }
    }

    changeExaminationType($event): void {
        this.formGroup.get('examinationType').setValue($event.value);
    }

    protected initForm(): void {
        this.formGroup = this._fb.group({
            examinationPlace: [this._dialogData.folder.examinationPlace ?? 'À distance' ],
            examinationType: [this._dialogData.folder.examinationType ?? 'A_DISTANCE', Validators.required ],
            examinationDate: [this._dialogData.folder.examinationDate ?? this.today, Validators.required],
            examinationEndDate: [this._dialogData.folder.examinationEndDate],
            enrollmentDate: [this._dialogData.folder.enrollmentDate],
            comment: [this._dialogData.folder.comment]
        });
    }

    formatDateWithoutSeconds($event: MatDatetimePickerInputEvent<any>): void {
        if ($event.value) {
            $event.value.setSeconds(0);
        }
    }
}
