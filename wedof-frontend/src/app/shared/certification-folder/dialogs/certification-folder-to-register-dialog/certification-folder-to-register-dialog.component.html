<form (ngSubmit)="submit()" [formGroup]="formGroup">
    <app-dialog-layout [title]="'private.training-organism.folders.dialog.title.' + action | translate"
                       [actions]="actions"
                       [cancelText]="'common.actions.no'" [disabled]="loading" [errorMessages]="errorMessages"
                       (dialogClose)="close()" [feedbackMessage]="feedbackMessage">

        <p class="pb-4">
            {{ "private.training-organism.folders.dialog.content." + action | translate }}
        </p>

        <mat-form-field class="flex-auto" appearance="fill">
            <mat-label>
                {{ 'private.common.certificationFolder.enrollmentDate' | translate}}
            </mat-label>
            <input formControlName="enrollmentDate" matInput
                   [matDatepicker]="enrollmentDatePicker"
                   [placeholder]="'private.common.certificationFolder.enrollmentDate' | translate">
            <mat-icon [matTooltipPosition]="'above'"
                      [matTooltip]="'private.common.certificationFolder.tooltip.enrollmentDate' | translate"
                      matPrefix
                      svgIcon="help_outline"></mat-icon>
            <mat-datepicker-toggle matSuffix [for]="enrollmentDatePicker"></mat-datepicker-toggle>
            <mat-datepicker #enrollmentDatePicker></mat-datepicker>
        </mat-form-field>

        <mat-form-field class="flex-auto" *ngIf="dialogData.commentUpdatable">
            <mat-label>
                {{ 'private.common.certificationFolder.comment' | translate}}
            </mat-label>
            <textarea formControlName="comment" matInput></textarea>
            <mat-icon matPrefix svgIcon="loyalty"></mat-icon>
        </mat-form-field>

        <ng-template #actions>
            <button type="submit" mat-flat-button color="primary" (click)="submit()" [disabled]="loading">
                <mat-progress-spinner *ngIf="loading; else submitLabel" [diameter]="24" mode="indeterminate">
                </mat-progress-spinner>
                <ng-template #submitLabel>
                    {{ 'common.actions.yes' | translate }}
                </ng-template>
            </button>
        </ng-template>

    </app-dialog-layout>
</form>
