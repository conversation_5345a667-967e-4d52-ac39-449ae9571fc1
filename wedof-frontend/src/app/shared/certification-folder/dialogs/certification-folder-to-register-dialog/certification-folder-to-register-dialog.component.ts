import {Component, Inject, Injector} from '@angular/core';
import {MAT_DIALOG_DATA} from '@angular/material/dialog';
import {CertificationFolderDialogComponent} from '../certification-folder-dialog-form-component';
import {CertificationFolder} from '../../../api/models/certification-folder';

@Component({
    selector: 'app-registration-folder-to-register-dialog',
    templateUrl: './certification-folder-to-register-dialog.component.html',
    styleUrls: ['./certification-folder-to-register-dialog.component.scss']
})
export class CertificationFolderToRegisterDialogComponent extends CertificationFolderDialogComponent<void> {

    constructor(
        injector: Injector,
        @Inject(MAT_DIALOG_DATA) private _dialogData: {
            folder: CertificationFolder,
            plural: boolean,
            folderCount: number,
            prefixAction: string,
            commentUpdatable: boolean,
            canEvaluate: boolean
        },
    ) {
        super(injector, _dialogData);
    }

    protected initForm(): void {
        this.formGroup = this._fb.group({
            comment: [this._dialogData.folder.comment],
            enrollmentDate: [this._dialogData.folder.enrollmentDate]
        });
    }

    submit(): void {
        this.errorMessages = [];
        if (this.formGroup.valid) {
            this.loading = true;
            this.submitted$.next(this.formGroup.value);
        }
    }
}
