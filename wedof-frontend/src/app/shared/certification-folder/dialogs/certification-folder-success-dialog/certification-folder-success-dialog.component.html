<form (ngSubmit)="submit()" [formGroup]="formGroup" >
    <app-dialog-layout [ngClass]="{'card-loading':cardLoading}" [title]="'private.training-organism.folders.dialog.title.' + action | translate"
                       [actions]="actions"
                       [cancelText]="'common.actions.no'"
                       [disabled]="loading"
                       [warningMessage]="warningMessage"
                       [errorMessages]="errorMessages"
                       (dialogClose)="close()"
                       [feedbackMessage]="feedbackMessage">

        <p class="pb-4">
            {{ "private.training-organism.folders.dialog.content." + action | translate }}
        </p>

        <treo-message [showIcon]="false" appearance="outline" type="info" class="mb-2"
                      *ngIf="!cardLoading && certification.allowGenerateCertificate && subscription.allowCertifierPlus ">
            {{'private.common.certificationFolder.certificateGeneratedAutomatically' | translate}}
        </treo-message>

        <app-form-fields *ngIf="!cardLoading" class="grid grid-cols-6 gap-2"
                         formGroupName="certificationFolderSuccess"
                         [entity]="folder"
                         [appFormFieldsData]="appFormFieldsData"
                         [formGroup]="formGroup">
        </app-form-fields>

        <ng-template #actions>
            <button type="submit" mat-flat-button color="primary" (click)="submit()"
                    [disabled]="loading || !formGroup?.valid">
                <mat-progress-spinner *ngIf="loading; else submitLabel" [diameter]="24" mode="indeterminate">
                </mat-progress-spinner>
                <ng-template #submitLabel>
                    {{ 'common.actions.yes' | translate }}
                </ng-template>
            </button>
        </ng-template>

    </app-dialog-layout>
</form>
