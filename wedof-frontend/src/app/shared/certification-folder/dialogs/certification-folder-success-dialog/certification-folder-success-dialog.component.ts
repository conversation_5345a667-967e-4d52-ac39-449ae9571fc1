import {Component, Inject, Injector, <PERSON><PERSON><PERSON>roy, OnInit} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialog} from '@angular/material/dialog';
import {
    CertificationFolder,
    CertificationFolderDialogExamSuccess,
    CertificationFolderGradePass,
    EuropeanLanguageLevel
} from '../../../api/models/certification-folder';
import moment from 'moment';
import {CertificationFolderDialogComponent} from '../certification-folder-dialog-form-component';
import {AppFormFieldData} from '../../../material/app-form-field/app-form-field.component';
import {CertificationService} from '../../../api/services/certification.service';
import {Certification, CertificationExaminationType} from '../../../api/models/certification';
import {lowerCase, upperFirst} from 'lodash-es';
import {Select} from '@ngxs/store';
import {SubscriptionState} from '../../../api/state/subscription.state';
import {combineLatest, Observable, Subject} from 'rxjs';
import {Subscription, SubscriptionTypes} from '../../../api/models/subscription';
import {takeUntil} from 'rxjs/operators';
import {DialogUpgradeSubscriptionComponent} from '../../../subscription/dialog-upgrade-subscription/dialog-upgrade-subscription.component';
import {OrganismState} from '../../../api/state/organism.state';
import {Organism} from '../../../api/models/organism';
import {Validators} from '@angular/forms';
import {FormValidators} from '../../../api/shared/form-validators';

@Component({
    selector: 'app-registration-folder-success-dialog',
    templateUrl: './certification-folder-success-dialog.component.html',
    styleUrls: ['./certification-folder-success-dialog.component.scss']
})
export class CertificationFolderSuccessDialogComponent extends CertificationFolderDialogComponent<CertificationFolderDialogExamSuccess> implements OnInit, OnDestroy {

    @Select(SubscriptionState.subscription) subscription$: Observable<Subscription>;
    @Select(OrganismState.organism) organism$: Observable<Organism>;

    skipToControl: boolean;
    appFormFieldsData: AppFormFieldData[] = [];
    folder: CertificationFolder;
    subscription: Subscription;
    organism: Organism;
    certification: Certification;
    cardLoading = true;

    private _unsubscribeAll = new Subject<void>();

    constructor(
        injector: Injector,
        private _certificationService: CertificationService,
        private _dialog: MatDialog,
        @Inject(MAT_DIALOG_DATA) private _dialogData: {
            folder: CertificationFolder,
            plural: boolean,
            skipToControl: boolean,
            folderCount: number,
            prefixAction: string,
            commentUpdatable: boolean,
            canEvaluate: boolean,
            warningMessage: string,
        },
    ) {
        super(injector, _dialogData);
        this.skipToControl = this._dialogData.skipToControl;
        this.folder = this._dialogData.folder;
    }

    ngOnInit(): void {
        combineLatest([
            this._certificationService.get(this.folder._links.certification.certifInfo),
            this.subscription$,
            this.organism$
        ]).pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe(([certification, subscription, organism]) => {
            this.certification = certification;
            this.subscription = subscription;
            this.organism = organism;
            super.ngOnInit();
        });
    }

    submit(): void {
        this.errorMessages = [];
        if (this.formGroup.valid) {
            this.loading = true;
            const certificationFolderSuccessBody = this.formGroup.getRawValue().certificationFolderSuccess;
            const data: CertificationFolderDialogExamSuccess = {
                toControl: this.skipToControl ? {
                    examinationPlace: certificationFolderSuccessBody.examinationPlace,
                    examinationType: certificationFolderSuccessBody.examinationType,
                    examinationDate: certificationFolderSuccessBody.examinationDate,
                    examinationEndDate: certificationFolderSuccessBody.examinationEndDate,
                    comment: null
                } : null,
                success: {
                    digitalProofLink: certificationFolderSuccessBody.digitalProofLink,
                    issueDate: certificationFolderSuccessBody.issueDate,
                    detailedResult: certificationFolderSuccessBody.detailedResult,
                    badgeAssertion: certificationFolderSuccessBody.badgeAssertion,
                    comment: certificationFolderSuccessBody.comment,
                    gradePass: certificationFolderSuccessBody.gradePass,
                    certificate: certificationFolderSuccessBody.certificate
                }
            };
            this.submitted$.next(data);
        }
    }

    protected initForm(): void {
        const validityPeriod = this.certification.validityPeriod;
        const isLanguageCertification = this.certification.nsf?.find(codeNsf => codeNsf.code === '136');
        // Needs to be a lambda to "capture" the validityPeriod
        const checkDates = (controlName, newValue, formData, formGroup) => {
            const appFormFieldExaminationDate = formData.find(field => field.controlName === 'examinationDate');
            const appFormFieldExaminationEndDate = formData.find(field => field.controlName === 'examinationEndDate');
            const appFormFieldIssueDate = formData.find(field => field.controlName === 'issueDate');
            const appFormFieldExpirationDate = formData.find(field => field.controlName === 'expirationDate');

            const examinationDate = controlName === 'examinationDate' ? newValue : formGroup.get('certificationFolderSuccess.examinationDate')?.value;
            const examinationEndDate = controlName === 'examinationEndDate' ? newValue : formGroup.get('certificationFolderSuccess.examinationEndDate')?.value;
            const issueDate = controlName === 'issueDate' ? newValue : formGroup.get('certificationFolderSuccess.issueDate')?.value;

            appFormFieldExaminationDate.value = examinationDate ? moment(new Date(examinationDate)).toISOString() : appFormFieldExaminationDate.value;
            appFormFieldExaminationDate.min = this.folder?.enrollmentDate ? moment(new Date(this.folder.enrollmentDate)).toISOString() : null;

            appFormFieldExaminationEndDate.value = examinationEndDate ? moment(new Date(examinationEndDate)).toISOString() : appFormFieldExaminationDate.value;
            appFormFieldExaminationEndDate.min = moment(new Date(appFormFieldExaminationDate.value)).toISOString();

            appFormFieldIssueDate.value = issueDate ? moment(new Date(issueDate)).toISOString() : appFormFieldIssueDate.value;
            appFormFieldIssueDate.min = moment(new Date(appFormFieldExaminationEndDate.value)).toISOString();

            const changes = [];
            if (validityPeriod) {
                appFormFieldExpirationDate.value = moment(new Date(appFormFieldIssueDate.value)).add(this.certification.validityPeriod, 'years');
                changes.push(appFormFieldExpirationDate);
            }
            return changes;
        };

        this.formGroup = this._fb.group({
            certificationFolderSuccess: this._fb.group({})
        });
        const defaultIssueDate = new Date();
        const appFormFieldsData: AppFormFieldData[] = [
            {
                controlName: 'examinationDate',
                removed: !this.skipToControl,
                label: 'private.common.certificationFolder.examinationDate',
                required: true,
                type: 'datetime-local',
                help: 'private.common.certificationFolder.tooltip.examinationDate',
                placeholder: 'private.common.certificationFolder.examinationDate',
                validatorsMessages: {
                    min: 'common.errors.date',
                    max: 'common.errors.date',
                },
                max: moment().toISOString(),
                min: this._dialogData.folder.enrollmentDate ? moment(new Date(this._dialogData.folder.enrollmentDate)).toISOString() : null,
                change: checkDates,
            },
            {
                controlName: 'examinationEndDate',
                removed: !this.skipToControl,
                label: 'private.common.certificationFolder.examinationEndDate',
                type: 'datetime-local',
                help: 'private.common.certificationFolder.tooltip.examinationEndDate',
                placeholder: 'private.common.certificationFolder.examinationEndDate',
                validatorsMessages: {
                    min: 'common.errors.date',
                    max: 'common.errors.date'
                },
                max: moment().toISOString(),
                min: moment(new Date(this._dialogData.folder.examinationDate)).toISOString(),
                change: checkDates,
            },
            {
                controlName: 'examinationType',
                label: 'private.common.certification.examinationType.title',
                removed: !this.skipToControl,
                value: this._dialogData.folder.examinationType ?? CertificationExaminationType.A_DISTANCE,
                type: 'select',
                required: true,
                choices: [
                    {
                        key: this._translateService.instant('private.common.certification.examinationType.A_DISTANCE'),
                        value: CertificationExaminationType.A_DISTANCE
                    },
                    {
                        key: this._translateService.instant('private.common.certification.examinationType.EN_PRESENTIEL'),
                        value: CertificationExaminationType.EN_PRESENTIEL
                    },
                    {
                        key: this._translateService.instant('private.common.certification.examinationType.MIXTE'),
                        value: CertificationExaminationType.MIXTE
                    }
                ]
            },
            {
                controlName: 'examinationPlace',
                type: 'text',
                value: this._dialogData.folder.examinationPlace ?? 'À distance',
                removed: !this.skipToControl,
                label: 'private.common.certificationFolder.examinationPlace'
            },
            {
                controlName: 'issueDate',
                label: 'private.common.certificationFolder.issueDate',
                value: defaultIssueDate,
                required: true,
                type: 'date',
                placeholder: 'private.common.certificationFolder.issueDate',
                validatorsMessages: {
                    min: 'common.errors.date',
                    max: 'common.errors.date'
                },
                max: moment(defaultIssueDate).toISOString(),
                min: moment(new Date(this._dialogData.folder.examinationEndDate)),
                change: checkDates,
            },
            {
                controlName: 'expirationDate',
                removed: !validityPeriod,
                disabled: true,
                label: 'private.common.certificationFolder.expirationDate',
                type: 'date',
                value: moment(defaultIssueDate).add(validityPeriod, 'years'),
                help: this._translateService.instant('private.common.certificationFolder.tooltip.expirationDate', {validityPeriod: validityPeriod}),
            },
            {
                controlName: 'detailedResult',
                type: 'text',
                label: 'private.common.certificationFolder.detailedResult',
                help: 'private.common.certificationFolder.tooltip.detailedResult',
            },
            {
                controlName: 'badgeAssertion',
                removable: true,
                required: false,
                type: 'url',
                label: 'private.common.certificationFolder.badgeAssertion',
                validators: [Validators.pattern(FormValidators.URL_PATTERN)],
                validatorsMessages: {
                    pattern: 'common.errors.url'
                }
            },
            {
                controlName: 'europeanLanguageLevel',
                label: 'private.common.certificationFolder.europeanLanguageLevel',
                type: 'select',
                choices: [
                    {key: EuropeanLanguageLevel.C2, value: EuropeanLanguageLevel.C2},
                    {key: EuropeanLanguageLevel.C1, value: EuropeanLanguageLevel.C1},
                    {key: EuropeanLanguageLevel.B2, value: EuropeanLanguageLevel.B2},
                    {key: EuropeanLanguageLevel.B1, value: EuropeanLanguageLevel.B1},
                    {key: EuropeanLanguageLevel.A2, value: EuropeanLanguageLevel.A2},
                    {key: EuropeanLanguageLevel.A1, value: EuropeanLanguageLevel.A1},
                    {
                        key: upperFirst(lowerCase(EuropeanLanguageLevel.INSUFFISANT)),
                        value: EuropeanLanguageLevel.INSUFFISANT
                    }
                ],
                removed: !isLanguageCertification
            },
            {
                controlName: 'digitalProofLink',
                type: 'url',
                removed: this.dialogData.folderCount !== 1,
                label: 'private.common.certificationFolder.digitalProofLink',
                help: 'private.common.certificationFolder.tooltip.digitalProofLink',
                validators: [Validators.pattern(FormValidators.URL_PATTERN)],
                validatorsMessages: {
                    pattern: 'common.errors.url'
                }
            },
            {
                controlName: 'digitalProofLinkUnupdatable',
                type: 'text',
                disabled: true,
                removed: this.dialogData.folderCount === 1,
                value: this._translateService.instant('private.common.certificationFolder.validation.noDigitalProofLinkAlert')
            },
            {
                controlName: 'certificate',
                removed: this.subscription.allowCertifierPlus && this.certification.allowGenerateCertificate,
                label: 'common.actions.file.label.certificate',
                chooseLabel: this._translateService.instant('common.actions.file.choose', {format: '.pdf'}),
                removeLabel: this._translateService.instant('common.actions.file.remove', {file: 'parchemin'}),
                type: 'file',
                icon: 'image',
                removable: true,
                showFilePreview: false,
                fileTypesAccept: ['application/pdf'],
                secondaryText: 'private.common.certificationFolder.certificate.info',
                href: this.subscription.allowCertifierPlus && '/certification/partenariats/' + this.certification.certifInfo,
                actionMethod: () => this.openDialogSubscription(),
            },
            {
                controlName: 'gradePass',
                label: 'private.common.certificationFolder.gradePassLabel',
                type: 'select',
                removable: true,
                placeholder: 'private.common.certificationFolder.gradePassPlaceholder',
                choices: [
                    {
                        key: this._translateService.instant('private.common.certificationFolder.gradesPass.SANS_MENTION'),
                        value: CertificationFolderGradePass.SANS_MENTION
                    },
                    {
                        key: this._translateService.instant('private.common.certificationFolder.gradesPass.MENTION_ASSEZ_BIEN'),
                        value: CertificationFolderGradePass.MENTION_ASSEZ_BIEN
                    },
                    {
                        key: this._translateService.instant('private.common.certificationFolder.gradesPass.MENTION_BIEN'),
                        value: CertificationFolderGradePass.MENTION_BIEN
                    },
                    {
                        key: this._translateService.instant('private.common.certificationFolder.gradesPass.MENTION_TRES_BIEN'),
                        value: CertificationFolderGradePass.MENTION_TRES_BIEN
                    },
                    {
                        key: this._translateService.instant('private.common.certificationFolder.gradesPass.MENTION_TRES_BIEN_AVEC_FELICITATIONS_DU_JURY'),
                        value: CertificationFolderGradePass.MENTION_TRES_BIEN_AVEC_FELICITATIONS_DU_JURY
                    },
                ],
            },
            {
                controlName: 'comment',
                label: 'private.common.certificationFolder.comment',
                removed: !this._dialogData.commentUpdatable,
                type: 'textarea',
                maxLength: 5000
            }
        ];
        this.appFormFieldsData = appFormFieldsData.filter(value => value != null);
        this.cardLoading = false;
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    openDialogSubscription(): void {
        this._dialog.open(DialogUpgradeSubscriptionComponent, {
            panelClass: ['full-page-scroll-50'],
            data: {
                organism: this.organism,
                subscription: this.subscription,
                fromPage: 'generateCertificate',
                subscriptionTypeToShow: SubscriptionTypes.CERTIFIER
            }
        });
    }
}
