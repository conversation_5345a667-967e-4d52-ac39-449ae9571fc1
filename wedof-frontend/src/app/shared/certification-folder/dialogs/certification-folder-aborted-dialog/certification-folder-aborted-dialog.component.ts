import {Component, Inject, Injector, OnInit} from '@angular/core';
import {MAT_DIALOG_DATA} from '@angular/material/dialog';
import {CertificationFolder, CertificationFolderExamAborted} from '../../../api/models/certification-folder';
import {CertificationFolderDialogComponent} from '../certification-folder-dialog-form-component';

@Component({
    selector: 'app-registration-folder-aborted-dialog',
    templateUrl: './certification-folder-aborted-dialog.component.html',
    styleUrls: ['./certification-folder-aborted-dialog.component.scss']
})
export class CertificationFolderAbortedDialogComponent extends CertificationFolderDialogComponent<CertificationFolderExamAborted> implements OnInit {

    constructor(
        injector: Injector,
        @Inject(MAT_DIALOG_DATA) private _dialogData: {
            folder: CertificationFolder,
            plural: boolean,
            folderCount: number,
            prefixAction: string,
            commentUpdatable: boolean,
            canEvaluate: boolean
        },
    ) {
        super(injector, _dialogData);
    }

    submit(): void {
        this.errorMessages = [];
        if (this.formGroup.valid) {
            this.loading = true;
            const data: CertificationFolderExamAborted = {
                comment: this.formGroup.value.comment
            };
            this.submitted$.next(data);
        }
    }

    protected initForm(): void {
        this.formGroup = this._fb.group({
            comment: [this._dialogData.folder.comment]
        });
    }

}
