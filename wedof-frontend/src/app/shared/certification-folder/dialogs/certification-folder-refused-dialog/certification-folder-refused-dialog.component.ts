import {Component, Inject, Injector, OnInit} from '@angular/core';
import {MAT_DIALOG_DATA} from '@angular/material/dialog';
import {CertificationFolder, CertificationFolderExamRefused} from '../../../api/models/certification-folder';
import {CertificationFolderDialogComponent} from '../certification-folder-dialog-form-component';

@Component({
    selector: 'app-registration-folder-to-retake-dialog',
    templateUrl: './certification-folder-refused-dialog.component.html',
    styleUrls: ['./certification-folder-refused-dialog.component.scss']
})
export class CertificationFolderRefusedDialogComponent extends CertificationFolderDialogComponent<CertificationFolderExamRefused> implements OnInit {


    constructor(
        injector: Injector,
        @Inject(MAT_DIALOG_DATA) private _dialogData: {
            folder: CertificationFolder,
            plural: boolean,
            folderCount: number,
            prefixAction: string,
            commentUpdatable: boolean,
            canEvaluate: boolean
        },
    ) {
        super(injector, _dialogData);
    }

    protected initForm(): void {
        this.formGroup = this._fb.group({
            comment: [this._dialogData.folder.comment]
        });
    }

    submit(): void {
        this.errorMessages = [];
        if (this.formGroup.valid) {
            this.loading = true;
            const data: CertificationFolderExamRefused = {
                comment: this.formGroup.value.comment
            };
            this.submitted$.next(data);
        }
    }

}
