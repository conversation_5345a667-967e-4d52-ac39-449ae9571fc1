import {Directive, Injector, OnInit} from '@angular/core';
import {AbstractDialogFormComponent} from '../../material/form/abstract-dialog-form.component';
import {CertificationFolder, EuropeanLanguageLevel} from '../../api/models/certification-folder';
import {TranslateService} from '@ngx-translate/core';
import {CertificationExaminationType} from '../../api/models/certification';
import {lowerCase, upperFirst} from 'lodash-es';

@Directive()
// tslint:disable-next-line: directive-class-suffix
export abstract class CertificationFolderDialogComponent<T> extends AbstractDialogFormComponent<T> implements OnInit {
    processedFolderCount: number;
    certificationExaminationTypes = [];
    europeanLanguageLevels = [];
    dialogData: {
        plural: boolean,
        folderCount: number,
        prefixAction: string,
        commentUpdatable: boolean,
        canEvaluate: boolean,
        warningMessage?: string
    };
    protected _translateService: TranslateService;


    get action(): string {
        let prefix = '';
        if (this.dialogData.plural) {
            prefix = 'plural.';
        }
        return prefix + this.dialogData.prefixAction;
    }

    get actionFeedback(): string {
        let prefix = '';
        if (this.dialogData.plural) {
            prefix = 'plural.';
        }
        return prefix + 'feedback';
    }

    protected constructor(
        injector: Injector,
        dialogData: {
            plural: boolean,
            folderCount: number,
            prefixAction: string,
            commentUpdatable: boolean
            canEvaluate: boolean
        },
    ) {
        super(injector);
        this.dialogData = dialogData;
        this.processedFolderCount = 1;
        this._translateService = injector.get(TranslateService);
        this.warningMessage = this.dialogData.warningMessage ? this.dialogData.warningMessage : null;
        this.certificationExaminationTypes = Object.values(CertificationExaminationType).map(value => ({
            label: `private.common.certification.examinationType.${value}`,
            value: value
        }));
        this.europeanLanguageLevels = Object.values(EuropeanLanguageLevel).map(value => ({
            label: value === EuropeanLanguageLevel.INSUFFISANT ? upperFirst(lowerCase(value)) : value,
            value: value
        }));
    }

    lastProcessedData(certificationFolder: CertificationFolder): void {
        if (this.processedFolderCount < this.dialogData.folderCount) {
            this.processedFolderCount++;
        } else {
            this.allProcessed = true;
        }
        this.feedbackMessage = this._translateService.instant(`private.common.certificationFolder.${this.actionFeedback}`,
            {processedFolderCount: this.processedFolderCount, folderDialogCount: this.dialogData.folderCount});
    }

    markFormControlsAsTouched(formControls: Array<string>): void {
        formControls.forEach((value) => {
            this.formGroup.get(value).markAsTouched();
        });
    }

    submit(): void {
        this.errorMessages = [];
        this.loading = true;
        this.submitted$.next();
    }

    ngOnInit(): void {
        this.initForm();
        this.feedbackMessage = this._translateService.instant(`private.common.certificationFolder.${this.actionFeedback}`,
            {processedFolderCount: this.processedFolderCount, folderDialogCount: this.dialogData.folderCount});
    }

    protected initForm(model?: T): void {
    }
}
