import {Component, Inject, Injector, OnInit} from '@angular/core';
import {MAT_DIALOG_DATA} from '@angular/material/dialog';
import {CertificationFolder, CertificationFolderExamRetake} from '../../../api/models/certification-folder';
import {CertificationFolderDialogComponent} from '../certification-folder-dialog-form-component';
import {CertificationService} from '../../../api/services/certification.service';
import {FormControl} from '@angular/forms';
import {MatDatetimePickerInputEvent} from '@angular-material-components/datetime-picker';

@Component({
    selector: 'app-registration-folder-to-retake-dialog',
    templateUrl: './certification-folder-to-retake-dialog.component.html',
    styleUrls: ['./certification-folder-to-retake-dialog.component.scss']
})
export class CertificationFolderToRetakeDialogComponent extends CertificationFolderDialogComponent<CertificationFolderExamRetake> implements OnInit {

    showEuropeanLanguage = false;

    constructor(
        injector: Injector,
        private _certificationService: CertificationService,
        @Inject(MAT_DIALOG_DATA) private _dialogData: {
            folder: CertificationFolder,
            plural: boolean,
            folderCount: number,
            prefixAction: string,
            commentUpdatable: boolean,
            canEvaluate: boolean
        },
    ) {
        super(injector, _dialogData);
    }

    submit(): void {
        this.errorMessages = [];
        this.loading = true;
        const data: CertificationFolderExamRetake = {
            examinationDate: this.formGroup.value.examinationDate,
            examinationEndDate: this.formGroup.value.examinationEndDate,
            examinationPlace: this.formGroup.value.examinationPlace,
            examinationType: this.formGroup.value.examinationType,
            comment: this.formGroup.value.comment,
            detailedResult: this.formGroup.value.detailedResult,
            tiersTemps: this.formGroup.value.tiersTemps
        };
        this.submitted$.next(data);
    }

    protected initForm(): void {
        this.formGroup = this._fb.group({
            detailedResult: [this._dialogData.folder.detailedResult],
            examinationPlace: [this._dialogData.folder.examinationPlace],
            examinationType: [this._dialogData.folder.examinationType],
            examinationDate: [this._dialogData.folder.examinationDate],
            examinationEndDate: [this._dialogData.folder.examinationEndDate],
            comment: [this._dialogData.folder.comment],
            tiersTemps: [this._dialogData.folder.tiersTemps]
        });
        this._certificationService.get(this._dialogData.folder._links.certification.certifInfo).subscribe((certification) => {
            const isLanguageCertification = certification.nsf?.find(codeNsf => codeNsf.code === '136');
            if (isLanguageCertification) {
                this.showEuropeanLanguage = true;
                this.formGroup.addControl('europeanLanguageLevel', new FormControl(this._dialogData.folder.europeanLanguageLevel));
            }
        });
    }

    changeType($event, formControlName: string): void {
        this.formGroup.get(formControlName).setValue($event.value);
    }

    formatDateWithoutSeconds($event: MatDatetimePickerInputEvent<any>): void {
        if ($event.value) {
            $event.value.setSeconds(0);
        }
    }
}
