import {Component, Inject, Injector, OnInit} from '@angular/core';
import {MAT_DIALOG_DATA} from '@angular/material/dialog';
import {CertificationFolder, CertificationFolderExamRegistered} from '../../../api/models/certification-folder';
import {CertificationFolderDialogComponent} from '../certification-folder-dialog-form-component';
import {Moment} from 'moment';
import {MatDatetimePickerInputEvent} from '@angular-material-components/datetime-picker';

@Component({
    selector: 'app-registration-folder-registered-dialog',
    templateUrl: './certification-folder-registered-dialog.component.html',
    styleUrls: ['./certification-folder-registered-dialog.component.scss']
})
export class CertificationFolderRegisteredDialogComponent extends CertificationFolderDialogComponent<CertificationFolderExamRegistered> implements OnInit {
    today: Moment;

    constructor(
        injector: Injector,
        @Inject(MAT_DIALOG_DATA) private _dialogData: {
            folder: CertificationFolder,
            plural: boolean,
            folderCount: number,
            prefixAction: string,
            commentUpdatable: boolean,
            canEvaluate: boolean
        },
    ) {
        super(injector, _dialogData);
    }

    protected initForm(): void {
        this.formGroup = this._fb.group({
            examinationPlace: [this._dialogData.folder.examinationPlace],
            enrollmentDate: [this._dialogData.folder.enrollmentDate],
            examinationDate: [this._dialogData.folder.examinationDate],
            examinationType: [this._dialogData.folder.examinationType],
            examinationEndDate: [this._dialogData.folder.examinationEndDate],
            comment: [this._dialogData.folder.comment]
        });
    }

    changeExaminationType($event): void {
        this.formGroup.get('examinationType').setValue($event.value);
    }

    submit(): void {
        this.errorMessages = [];
        if (this.formGroup.valid) {
            this.loading = true;
            const data: CertificationFolderExamRegistered = {
                enrollmentDate: this.formGroup.value.enrollmentDate,
                comment: this.formGroup.value.comment,
                examinationPlace: this.formGroup.value.examinationPlace,
                examinationType: this.formGroup.value.examinationType,
                examinationDate: this.formGroup.value.examinationDate,
                examinationEndDate: this.formGroup.value.examinationEndDate
            };
            this.submitted$.next(data);
        }
    }

    formatDateWithoutSeconds($event: MatDatetimePickerInputEvent<any>): void {
        if ($event.value) {
            $event.value.setSeconds(0);
        }
    }
}
