<form (ngSubmit)="submit()" [formGroup]="formGroup">
    <app-dialog-layout [title]="'private.training-organism.folders.dialog.title.' + action | translate"
                       [actions]="actions"
                       [cancelText]="'common.actions.no'" [disabled]="loading" [errorMessages]="errorMessages"
                       (dialogClose)="close()" [feedbackMessage]="feedbackMessage">

        <p class="pb-4">
            {{ "private.training-organism.folders.dialog.content." + action | translate }}
        </p>

        <mat-form-field *ngIf="skipToControl" class="flex-auto" appearance="fill">
            <mat-label>
                {{ 'private.common.certificationFolder.examinationDate' | translate}}
            </mat-label>
            <input formControlName="examinationDate" matInput required
                   (dateChange)="markFormControlsAsTouched(['examinationEndDate'])"
                   [min]="formGroup.get('enrollmentDate')?.value"
                   [max]="today"
                   (dateInput)="formatDateWithoutSeconds($event)"
                   (click)="examinationDatePicker.open()"
                   [ngxMatDatetimePicker]="examinationDatePicker"
                   [placeholder]="'private.common.certificationFolder.examinationDate' | translate">
            <mat-icon [matTooltipPosition]="'above'"
                      [matTooltip]="'private.common.certificationFolder.tooltip.examinationDate' | translate"
                      matPrefix
                      svgIcon="help_outline"></mat-icon>
            <mat-datepicker-toggle matSuffix [for]="examinationDatePicker"></mat-datepicker-toggle>
            <ngx-mat-datetime-picker #examinationDatePicker [enableMeridian]="false"></ngx-mat-datetime-picker>
            <mat-error class="flex-auto gt-xs:pr-3">
                <ng-container *ngIf="formGroup.get('examinationDate').errors?.required; else showOtherError">
                    {{'common.errors.required' | translate }}
                </ng-container>
                <ng-template #showOtherError>
                    <ng-container *ngIf="examinationDatePicker._minDate || examinationDatePicker._maxDate">
                        {{'common.errors.date' | translate }}
                    </ng-container>
                </ng-template>
            </mat-error>
        </mat-form-field>

        <mat-form-field *ngIf="skipToControl" class="flex-auto" appearance="fill">
            <mat-label>
                {{ 'private.common.certificationFolder.examinationEndDate' | translate}}
            </mat-label>
            <input formControlName="examinationEndDate" matInput
                   [min]="formGroup.get('examinationDate').value"
                   [max]="today"
                   (dateInput)="formatDateWithoutSeconds($event)"
                   (click)="examinationEndDatePicker.open()"
                   [ngxMatDatetimePicker]="examinationEndDatePicker"
                   [placeholder]="'private.common.certificationFolder.examinationEndDate' | translate">
            <mat-icon [matTooltipPosition]="'above'"
                      [matTooltip]="'private.common.certificationFolder.tooltip.examinationEndDate' | translate"
                      matPrefix
                      svgIcon="help_outline"></mat-icon>
            <mat-datepicker-toggle matSuffix [for]="examinationEndDatePicker"></mat-datepicker-toggle>
            <ngx-mat-datetime-picker #examinationEndDatePicker [enableMeridian]="false"></ngx-mat-datetime-picker>
            <mat-error class="flex-auto gt-xs:pr-3"
                       *ngIf="examinationEndDatePicker._minDate || examinationEndDatePicker._maxDate ">
                {{'common.errors.date' | translate }}
            </mat-error>
        </mat-form-field>

        <mat-form-field *ngIf="skipToControl" class="flex-auto">
            <mat-label>
                {{ 'private.common.certification.examinationType.title' | translate}}
            </mat-label>
            <mat-select class="flex flex-col" formControlName="examinationType"
                        (change)="changeType($event, 'examinationType')"
            >
                <mat-option *ngFor="let certificationExaminationType of certificationExaminationTypes"
                            [value]="certificationExaminationType.value">
                    {{certificationExaminationType.label | translate}}
                </mat-option>
            </mat-select>
            <mat-error class="flex-auto gt-xs:pr-3">
                {{'private.common.certificationFolder.validation.examinationTypeError' | translate }}
            </mat-error>
        </mat-form-field>

        <mat-form-field *ngIf="skipToControl" class="flex-auto">
            <mat-label>
                {{ 'private.common.certificationFolder.examinationPlace' | translate}}
            </mat-label>
            <input formControlName="examinationPlace" matInput type="text"/>
            <mat-icon matPrefix svgIcon="school"></mat-icon>
        </mat-form-field>

        <mat-form-field class="flex-auto">
            <mat-label>
                {{ 'private.common.certificationFolder.detailedResult' | translate}}
            </mat-label>
            <input formControlName="detailedResult" matInput type="text"/>
            <mat-icon [matTooltipPosition]="'above'"
                      [matTooltip]="'private.common.certificationFolder.tooltip.detailedResult' | translate"
                      matPrefix
                      svgIcon="help_outline"></mat-icon>
            <mat-icon matPrefix svgIcon="school"></mat-icon>
        </mat-form-field>

        <mat-form-field class="flex-auto" *ngIf="showEuropeanLanguage">
            <mat-label>
                {{'private.common.certificationFolder.europeanLanguageLevel' | translate }}
            </mat-label>
            <mat-select class="flex flex-col" formControlName="europeanLanguageLevel"
                        (change)="changeType($event, 'europeanLanguageLevel')"
            >
                <mat-option *ngFor="let languageLevel of europeanLanguageLevels"
                            [value]="languageLevel.value">
                    {{languageLevel.label | translate}}
                </mat-option>
            </mat-select>
        </mat-form-field>

        <mat-form-field class="flex-auto" *ngIf="dialogData.commentUpdatable">
            <mat-label>
                {{ 'private.common.certificationFolder.comment' | translate}}
            </mat-label>
            <textarea formControlName="comment" matInput></textarea>
            <mat-icon matPrefix svgIcon="loyalty"></mat-icon>
        </mat-form-field>

        <ng-template #actions>
            <button type="submit" mat-flat-button color="primary" (click)="submit()"
                    [disabled]="loading || !formGroup.valid">
                <mat-progress-spinner *ngIf="loading; else submitLabel" [diameter]="24" mode="indeterminate">
                </mat-progress-spinner>
                <ng-template #submitLabel>
                    {{ 'common.actions.yes' | translate }}
                </ng-template>
            </button>
        </ng-template>

        <div class="pb-2">
            <mat-label>{{"private.training-organism.folders.dialog.content.canRetake" | translate}}</mat-label>
            <mat-radio-group class="flex" formControlName="canRetake">
                <mat-radio-button [value]="true"
                                  class="mat-radio-button">{{'common.actions.yes' | translate}}</mat-radio-button>
                <mat-radio-button [value]="false"
                                  class="mat-radio-button ml-2">{{'common.actions.no'  | translate}}</mat-radio-button>
            </mat-radio-group>
        </div>

        <mat-form-field *ngIf="formGroup.get('canRetake')?.value === true && !skipToControl" class="flex-auto"
                        appearance="fill">
            <mat-label>
                {{ 'private.common.certificationFolder.examinationDate' | translate}}
            </mat-label>
            <input formControlName="examinationDate" matInput
                   (dateChange)="markFormControlsAsTouched(['examinationEndDate'])"
                   [min]="formGroup.get('enrollmentDate')?.value"
                   [max]="today"
                   (dateInput)="formatDateWithoutSeconds($event)"
                   (click)="examinationDatePicker.open()"
                   [ngxMatDatetimePicker]="examinationDatePicker"
                   [placeholder]="'private.common.certificationFolder.examinationDate' | translate">
            <mat-icon [matTooltipPosition]="'above'"
                      [matTooltip]="'private.common.certificationFolder.tooltip.examinationDate' | translate"
                      matPrefix
                      svgIcon="help_outline"></mat-icon>
            <mat-datepicker-toggle matSuffix [for]="examinationDatePicker"></mat-datepicker-toggle>
            <ngx-mat-datetime-picker #examinationDatePicker [enableMeridian]="false"></ngx-mat-datetime-picker>
            <mat-error class="flex-auto gt-xs:pr-3"
                       *ngIf="examinationDatePicker._minDate || examinationDatePicker._maxDate">
                {{'common.errors.date' | translate }}
            </mat-error>
        </mat-form-field>

        <mat-form-field *ngIf="formGroup.get('canRetake')?.value === true && !skipToControl" class="flex-auto"
                        appearance="fill">
            <mat-label>
                {{ 'private.common.certificationFolder.examinationEndDate' | translate}}
            </mat-label>
            <input formControlName="examinationEndDate" matInput
                   [min]="formGroup.get('examinationDate').value"
                   [max]="today"
                   (dateInput)="formatDateWithoutSeconds($event)"
                   (click)="examinationEndDatePicker.open()"
                   [ngxMatDatetimePicker]="examinationEndDatePicker"
                   [placeholder]="'private.common.certificationFolder.examinationEndDate' | translate">
            <mat-icon [matTooltipPosition]="'above'"
                      [matTooltip]="'private.common.certificationFolder.tooltip.examinationEndDate' | translate"
                      matPrefix
                      svgIcon="help_outline"></mat-icon>
            <mat-datepicker-toggle matSuffix [for]="examinationEndDatePicker"></mat-datepicker-toggle>
            <ngx-mat-datetime-picker #examinationEndDatePicker [enableMeridian]="false"></ngx-mat-datetime-picker>
            <mat-error class="flex-auto gt-xs:pr-3"
                       *ngIf="examinationEndDatePicker._minDate || examinationEndDatePicker._maxDate">
                {{'common.errors.date' | translate }}
            </mat-error>
        </mat-form-field>

        <mat-form-field *ngIf="formGroup.get('canRetake')?.value === true && !skipToControl" class="flex-auto">
            <mat-label>
                {{ 'private.common.certification.examinationType.title' | translate}}
            </mat-label>
            <mat-select class="flex flex-col" formControlName="examinationType"
                        (change)="changeType($event, 'examinationType' )"
            >
                <mat-option *ngFor="let certificationExaminationType of certificationExaminationTypes"
                            [value]="certificationExaminationType.value">
                    {{certificationExaminationType.label | translate}}
                </mat-option>
            </mat-select>
        </mat-form-field>

        <mat-form-field *ngIf="formGroup.get('canRetake')?.value === true && !skipToControl" class="flex-auto">
            <mat-label>
                {{ 'private.common.certificationFolder.examinationPlace' | translate}}
            </mat-label>
            <input formControlName="examinationPlace" matInput type="text"/>
            <mat-icon matPrefix svgIcon="school"></mat-icon>
        </mat-form-field>

    </app-dialog-layout>
</form>
