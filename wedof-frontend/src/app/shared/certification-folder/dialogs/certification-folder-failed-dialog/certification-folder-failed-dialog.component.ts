import {Component, Inject, Injector, OnInit} from '@angular/core';
import {MAT_DIALOG_DATA} from '@angular/material/dialog';
import {CertificationFolder, CertificationFolderDialogExamFailed} from '../../../api/models/certification-folder';
import moment, {Moment} from 'moment';
import {FormControl, Validators} from '@angular/forms';
import {CertificationFolderDialogComponent} from '../certification-folder-dialog-form-component';
import {CertificationService} from '../../../api/services/certification.service';
import {MatDatetimePickerInputEvent} from '@angular-material-components/datetime-picker';

@Component({
    selector: 'app-registration-folder-failed-dialog',
    templateUrl: './certification-folder-failed-dialog.component.html',
    styleUrls: ['./certification-folder-failed-dialog.component.scss']
})
export class CertificationFolderFailedDialogComponent extends CertificationFolderDialogComponent<CertificationFolderDialogExamFailed> implements OnInit {
    today: Moment;
    skipToControl: boolean;
    showEuropeanLanguage = false;

    constructor(
        injector: Injector,
        private _certificationService: CertificationService,
        @Inject(MAT_DIALOG_DATA) private _dialogData: {
            folder: CertificationFolder,
            plural: boolean,
            skipToControl: boolean,
            folderCount: number,
            prefixAction: string,
            commentUpdatable: boolean,
            canEvaluate: boolean
        },
    ) {
        super(injector, _dialogData);
        this.today = moment();
        this.skipToControl = this._dialogData.skipToControl;
    }

    submit(): void {
        this.errorMessages = [];
        if (this.formGroup.valid) {
            this.loading = true;
            const canRetake = this.formGroup.value.canRetake;
            const data: CertificationFolderDialogExamFailed = {
                canRetake: canRetake,
                toControl: this.skipToControl ? {
                    examinationPlace: this.formGroup.value.examinationPlace,
                    examinationType: this.formGroup.value.examinationType,
                    examinationDate: this.formGroup.value.examinationDate,
                    examinationEndDate: this.formGroup.value.examinationEndDate,
                    comment: null
                } : null,
                failed: !canRetake ? {
                    comment: this.formGroup.value.comment,
                    detailedResult: this.formGroup.value.detailedResult,
                    europeanLanguageLevel: this.formGroup.value.europeanLanguageLevel
                } : null,
                retake: canRetake ? {
                    examinationDate: this.formGroup.value.examinationDate,
                    examinationEndDate: this.formGroup.value.examinationEndDate,
                    examinationPlace: this.formGroup.value.examinationPlace,
                    examinationType: this.formGroup.value.examinationType,
                    comment: this.formGroup.value.comment,
                    detailedResult: this.formGroup.value.detailedResult,
                    europeanLanguageLevel: this.formGroup.value.europeanLanguageLevel
                } : null
            };
            this.submitted$.next(data);
        }
    }

    protected initForm(): void {
        this.formGroup = this._fb.group({
            detailedResult: [this._dialogData.folder.detailedResult],
            comment: [this._dialogData.folder.comment],
            canRetake: [false]
        });

        this._certificationService.get(this._dialogData.folder._links.certification.certifInfo).subscribe((certification) => {
            const isLanguageCertification = certification.nsf?.find(codeNsf => codeNsf.code === '136');
            if (isLanguageCertification) {
                this.showEuropeanLanguage = true;
                this.formGroup.addControl('europeanLanguageLevel', new FormControl(this._dialogData.folder.europeanLanguageLevel));
            }
        });

        if (this.skipToControl) {
            this.formGroup.addControl('examinationPlace',
                new FormControl(this._dialogData.folder.examinationPlace ?? 'À distance'));
            this.formGroup.addControl('examinationType',
                new FormControl(this._dialogData.folder.examinationType ?? 'A_DISTANCE', Validators.required));
            this.formGroup.addControl('examinationDate', new FormControl(this._dialogData.folder.examinationDate ?? this.today, Validators.required));
            this.formGroup.addControl('examinationEndDate', new FormControl(this._dialogData.folder.examinationEndDate));
            this.formGroup.addControl('enrollmentDate', new FormControl(this._dialogData.folder.enrollmentDate));
        } else {
            this.formGroup.addControl('examinationPlace', new FormControl(this._dialogData.folder.examinationPlace));
            this.formGroup.addControl('examinationType', new FormControl(this._dialogData.folder.examinationType));
            this.formGroup.addControl('examinationDate', new FormControl(this._dialogData.folder.examinationDate));
            this.formGroup.addControl('examinationEndDate', new FormControl(this._dialogData.folder.examinationEndDate));
            this.formGroup.addControl('enrollmentDate', new FormControl(this._dialogData.folder.enrollmentDate));
        }
    }

    changeType($event, formControlName): void {
        this.formGroup.get(formControlName).setValue($event.value);
    }

    formatDateWithoutSeconds($event: MatDatetimePickerInputEvent<any>): void {
        if ($event.value) {
            $event.value.setSeconds(0);
        }
    }

}
