import {Component, Inject, Injector} from '@angular/core';
import {MAT_DIALOG_DATA} from '@angular/material/dialog';
import {
    CertificationFolder,
    CertificationFolderUpdate,
    CertificationFolderUpdateBody
} from '../../../api/models/certification-folder';
import {CertificationFolderDialogComponent} from '../certification-folder-dialog-form-component';
import {AppFormFieldData} from '../../../material/app-form-field/app-form-field.component';
import {cloneDeep} from 'lodash-es';

@Component({
    selector: 'app-certification-folder-update-dialog',
    templateUrl: './certification-folder-update-dialog.component.html',
    styleUrls: ['./certification-folder-update-dialog.component.scss']
})
export class CertificationFolderUpdateDialogComponent extends CertificationFolderDialogComponent<CertificationFolderUpdate> {
    appFormFieldsData: AppFormFieldData[];
    certificationFolder: CertificationFolder;

    constructor(
        injector: Injector,
        @Inject(MAT_DIALOG_DATA) private _dialogData: {
            folder: CertificationFolderUpdateBody,
            plural: boolean,
            folderCount: number,
            prefixAction: string,
            commentUpdatable: boolean,
            canEvaluate: boolean
        }
    ) {
        super(injector, _dialogData);
    }

    protected initForm(): void {
        this.certificationFolder = cloneDeep(this._dialogData.folder);
        this.formGroup = this._fb.group({
            certificationFolder: this._fb.group({})
        });

        this.appFormFieldsData = [
            {
                controlName: 'amountHt',
                type: 'price',
                label: 'private.common.certificationFolder.amountHt',
                help: 'private.common.certificationFolder.amountHtToolTip',
                icon: 'euro_symbol',
                min: 0
            },
            {
                controlName: 'addedTags',
                label: 'common.tags.added.label',
                placeholder: 'common.tags.added.placeholder',
                type: 'tags',
                value: [],
                isCreateAvailable: true
            },
            {
                controlName: 'removedTags',
                label: 'common.tags.removed.label',
                placeholder: 'common.tags.removed.placeholder',
                type: 'tags',
                value: [],
                isCreateAvailable: true
            }
        ];
    }

    close(): void {
        this._dialogRef.close();
    }

    submit(): void {
        this.errorMessages = [];
        if (this.formGroup.valid) {
            this.loading = true;
            const data: CertificationFolderUpdate = {
                amountHt: this.formGroup.value.certificationFolder.amountHt,
                addedTags: this.formGroup.value.certificationFolder.addedTags,
                removedTags: this.formGroup.value.certificationFolder.removedTags
            };
            this.submitted$.next(data);
        }
    }
}
