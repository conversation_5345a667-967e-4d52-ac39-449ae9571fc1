import {Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output} from '@angular/core';
import {FormGroup} from '@angular/forms';
import {Certification} from '../../api/models/certification';
import {CertificationService} from '../../api/services/certification.service';
import {Organism} from '../../api/models/organism';
import {formatMomentToIsoDate} from '../../utils/date-utils';
import {cloneDeep} from 'lodash-es';
import {
    getCdcStates,
    getCertificationFolderAllStates,
    getMessageStates,
    getPeriodTypes,
    getRegistrationFolderAllDataProviders,
    getRegistrationFolderAllStates,
    getSurveys
} from '../../utils/states-utils';
import {Params, Router} from '@angular/router';
import {SearchMethod} from '../../material/infinite-scroll/infinite-scroll.component';
import {OrganismService} from '../../api/services/organism.service';
import {pairwise, takeUntil} from 'rxjs/operators';
import {Observable, Subject} from 'rxjs';
import {CertificationFolderFilters} from '../certification-folder-table/certification-folder-table.component';
import {EntityClass} from '../../utils/enums/entity-class';
import {Select, Store} from '@ngxs/store';
import {AddUserFilter, UserState} from '../../api/state/user.state';
import {DialogManageFiltersComponent} from '../../filters/dialog-manage-filters/dialog-manage-filters.component';
import {MatDialog} from '@angular/material/dialog';
import {MatSnackBar} from '@angular/material/snack-bar';
import {SnackBarComponent} from '../../material/snack-bar/snack-bar.component';
import {displayTextSnackBar} from '../../utils/displayTextSnackBar';
import {TranslateService} from '@ngx-translate/core';
import {Filter} from '../../api/models/user';
import {generateColor} from '../../utils/color-utils';
import {OrganismState} from '../../api/state/organism.state';
import {PeriodTypes} from '../../api/models/period-types';
import {RegistrationFolderFilters} from '../../registration-folder/registration-folder-table/registration-folder-table.component';
import {CertificationPartnerStates} from '../../api/models/certification-partner';
import {MessageTemplateService} from '../../api/services/message-template.service';
import {MessageTemplateState} from '../../api/models/message-template';
import {SkillService} from '../../api/services/skill.service';
import {displaySkillSetTitle, SkillType} from '../../api/models/skill';
import {Choices} from '../../material/app-form-field/app-form-field.component';
import {MatSelectChange} from '@angular/material/select/select';

@Component({
    selector: 'app-certification-folder-advanced-search',
    templateUrl: './certification-folder-advanced-search.component.html',
    styleUrls: ['./certification-folder-advanced-search.component.scss']
})

export class CertificationFolderAdvancedSearchComponent implements OnInit, OnDestroy {

    @Input() searchAdvanced: FormGroup;
    @Output() closePopOver: EventEmitter<any> = new EventEmitter<any>();

    certificationFolderStates = [];
    filterOnStates = [];
    registrationFolderStates = [];
    cdcStates = [];
    periods = [];
    dataProviders = [];
    registrationFolderCompletionRates = [];
    messageTemplateChoices = [];
    messageStates = [];
    surveys = [];
    hasMessageTemplate: boolean;
    activeFiltersCount = 0;
    filters: Filter[];
    filters$: Observable<Filter[]>;
    skillsSetsChoices: Choices;
    maxDate: Date;

    resultFormatterCertification: any;
    searchMethodCertification: SearchMethod;
    addToSearchResultCertification: Certification[];

    resultFormatterOrganism: any;
    searchMethodOrganism: SearchMethod;

    private _unsubscribeAll = new Subject<void>();
    private entityClass: EntityClass = EntityClass.CERTIFICATION_FOLDER;
    private entityClassSurvey: EntityClass = EntityClass.CERTIFICATION_FOLDER_SURVEY;

    readonly PeriodTypes = PeriodTypes;

    @Select(OrganismState.organism) organism$: Observable<Organism>;

    constructor(
        private _certificationService: CertificationService,
        private _organismService: OrganismService,
        private _router: Router,
        private _store: Store,
        private _dialog: MatDialog,
        private _snackBar: MatSnackBar,
        private _skillService: SkillService,
        private _translateService: TranslateService,
        private _messageTemplateService: MessageTemplateService
    ) {
    }

    ngOnInit(): void {
        this.resultFormatterOrganism = (organism: Organism) => organism.name + ' (...' + organism.siret.substr(organism.siret.length - 5) + ')';
        this.resultFormatterCertification = (certification: Certification) => {
            return certification.externalId ? (certification.externalId + ' - ' + certification.name) : certification.name;
        };
        this.organism$.pipe(takeUntil(this._unsubscribeAll)).subscribe(organism => {
            if (organism.isCertifierOrganism) {
                this.addToSearchResultCertification = [{
                    name: this._translateService.instant('common.actions.search.myCertifications'),
                    certifInfo: 'certifier',
                }] as Certification[];
            } else {
                this.addToSearchResultCertification = [];
            }
        });

        this.searchMethodCertification = (params: any) => {
            params.certificationPartnerState = [CertificationPartnerStates.ACTIVE, CertificationPartnerStates.REVOKED, CertificationPartnerStates.SUSPENDED].join(',');
            return this._certificationService.listLite(params);
        };
        this.searchMethodOrganism = (params: any) => this._organismService.findPotentialCertificationFolderHolders(params);

        this.maxDate = new Date();
        this.certificationFolderStates = getCertificationFolderAllStates();
        this.registrationFolderStates = getRegistrationFolderAllStates();
        this.dataProviders = getRegistrationFolderAllDataProviders();
        this.cdcStates = getCdcStates();
        this.periods = getPeriodTypes(true);
        this.registrationFolderCompletionRates = [
            {
                label: this._translateService.instant('common.actions.search.completionRate.nullOr80lt'),
                value: '<80'
            },
            {
                label: this._translateService.instant('common.actions.search.completionRate.80gt'),
                value: '>80'
            }
        ];
        this.surveys = getSurveys();
        this._messageTemplateService.list({
            entityClass: [this.entityClass, this.entityClassSurvey].join(',')
        }).subscribe((messageTemplates) => {
            this.hasMessageTemplate = messageTemplates.payload?.length > 0;
            if (this.hasMessageTemplate) {
                messageTemplates.payload.forEach((messageTemplate) => {
                    const state = messageTemplate.state === MessageTemplateState.ACTIVE ? '(Actif)' : '(Inactif)';
                    const label = messageTemplate.title + ' ' + state;
                    this.messageTemplateChoices.push({
                        label: label,
                        value: messageTemplate.id.toString()
                    });
                });
                this.messageStates = getMessageStates();
            }
        });
        this.refreshFilterOnStateDates();

        this.searchAdvanced.valueChanges
            .pipe(takeUntil(this._unsubscribeAll))
            .pipe(pairwise<RegistrationFolderFilters>())
            .subscribe(([previousCertificationFolderFilters, certificationFoldersFilters]) => {
                this.activeFiltersCount = Object.keys(certificationFoldersFilters).filter(key => {
                    if (key === 'filterOnStateDate' && certificationFoldersFilters[key] === 'stateLastUpdate') {
                        return false;
                    } else if (Array.isArray(certificationFoldersFilters[key])) {
                        return certificationFoldersFilters[key].length > 0;
                    } else {
                        return certificationFoldersFilters[key];
                    }
                }).length;
                if (certificationFoldersFilters.period !== PeriodTypes.CUSTOM) {
                    certificationFoldersFilters.since = null;
                    certificationFoldersFilters.until = null;
                }
                this.refreshFilterOnStateDates(certificationFoldersFilters, previousCertificationFolderFilters);
            });
        this.filters$ = this._store.select(UserState.userFilteredFilters(this.entityClass));
        this.filters$.pipe(takeUntil(this._unsubscribeAll)).subscribe((filters) => {
            this.filters = filters;
        });
        this.searchAdvanced.get('certifInfo').valueChanges.pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe((value) => {
            this.getSkillSets(value);
        });
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    clearValue(valueToRemoved: string, event?: MouseEvent): void {
        this.searchAdvanced.get(valueToRemoved).setValue(null);
        event?.stopPropagation();
    }

    resetUrl(): void {
        this._router.navigate([], {queryParams: null});
        this.close();
    }

    cancel(): void {
        this.searchAdvanced.reset();
        this.close();
    }

    close(): void {
        this.closePopOver.emit();
    }

    searchButton(): void {
        this._router.navigate([], {queryParams: this.getQueryParams()});
        this.close();
    }

    saveAsFilter(): void {
        this._router.navigate([], {queryParams: this.getQueryParams()}).then(() => {
            const link = this._router.url.split('?')[1];
            const existingFilter = this.filters.find((filter) => filter.link === link);
            if (!existingFilter) {
                this._store.dispatch(new AddUserFilter({
                    name: 'Nouveau filtre',
                    entityClass: this.entityClass,
                    link: link,
                    color: generateColor()
                }));
                this.openManageFilterDialog();
            } else {
                this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(
                    this._translateService.instant('private.common.filters.existingFilterAlert', {filterName: existingFilter.name})
                    , 5000, 'red'));
            }
        });
    }

    openManageFilterDialog(): void {
        this._dialog.open(DialogManageFiltersComponent, {
            data: {
                filters$: this.filters$,
                entityClass: this.entityClass
            }
        });
    }

    private getQueryParams(): Params {
        const queryParams = {};
        Object.entries(this.searchAdvanced.value).map(([key, value]: [string, any]) => {
            if (key === 'tags') {
                if (value && value.length) {
                    queryParams[key] = value.join(',');
                } else {
                    delete queryParams[key];
                }
            } else if (value) {
                if (Array.isArray(value)) {
                    queryParams[key] = value.join(',');
                } else if ((key === 'since' || key === 'until') && typeof value !== 'string') {
                    queryParams[key] = formatMomentToIsoDate(key === 'since' ? value.startOf('day') : value.endOf('day'));
                } else {
                    queryParams[key] = value;
                }
            }
        });
        return queryParams;
    }

    private refreshFilterOnStateDates(currentFilters: CertificationFolderFilters = null, previousFilters: CertificationFolderFilters = null): void {
        const currentPeriodInFuture = currentFilters && currentFilters.period ? (
            currentFilters.period.toLowerCase().startsWith('next')
            || currentFilters.period.toLowerCase().startsWith('tomorrow')
            || currentFilters.period.toLowerCase().endsWith('future')
        ) : false;
        const previousPeriodInFuture = previousFilters && previousFilters.period ? (
            previousFilters.period.toLowerCase().startsWith('next')
            || previousFilters.period.toLowerCase().startsWith('tomorrow')
            || previousFilters.period.toLowerCase().endsWith('future')
        ) : false;
        if (previousPeriodInFuture !== currentPeriodInFuture || currentFilters == null) {
            if (currentFilters) {
                currentFilters.filterOnStateDate = null;
            }
            this.filterOnStates = [];
            if (!currentPeriodInFuture) {
                this.filterOnStates.push({
                    'name': `common.actions.search.updateFilterOnStateDate.certificationFolderStateDate.title`,
                    'values': [{
                        label: `common.actions.search.stateLastUpdate`,
                        value: 'stateLastUpdate'
                    }, {
                        label: `common.actions.search.wedofInvoice`,
                        value: 'wedofInvoice'
                    }]
                });
                cloneDeep(this.certificationFolderStates).forEach((state) =>
                    this.filterOnStates[0]['values'].push({
                        label: `common.actions.search.updateFilterOnStateDate.certificationFolderStateDate.${state['value']}`,
                        value: state['value'] + 'Date'
                    })
                );
                this.filterOnStates.push({
                    'name': `common.actions.search.updateFilterOnStateDate.registrationFolderStateDate.title`,
                    'values': []
                });
                cloneDeep(this.registrationFolderStates).forEach((state) =>
                    this.filterOnStates[1]['values'].push({
                        label: `common.actions.search.updateFilterOnStateDate.registrationFolderStateDate.${state['value']}`,
                        value: state['value'] + 'RegistrationFolderStateDate'
                    })
                );
            } else {
                this.filterOnStates.push({
                    'name': `common.actions.search.updateFilterOnStateDate.certificationFolderStateDate.title`,
                    'values': []
                });
                /*                this.filterOnStates.push({
                                    'name': `common.actions.search.updateFilterOnStateDate.registrationFolderStateDate.title`,
                                    'values': []
                                });*/
            }
            ['examinationDate', 'examinationEndDate', 'enrollmentDate'].forEach(dateField => {
                this.filterOnStates[0]['values'].unshift({
                    label: `common.actions.search.updateFilterOnStateDate.certificationFolderStateDate.${dateField}`,
                    value: dateField
                });
            });
            /*['sessionStartDate', 'sessionEndDate'].forEach(dateField => {
                this.filterOnStates[1]['values'].push({
                    label: `common.actions.search.updateFilterOnStateDate.registrationFolderStateDate.${dateField}`,
                    value: dateField + 'RegistrationFolderDate'
                });
            });*/
        }
    }

    getSkillSets(certifInfo: string): any[] {
        this.skillsSetsChoices = null;
        if (certifInfo?.length === 1 && !certifInfo.includes('certifier')) {
            this._skillService.list({certifInfo: certifInfo}).subscribe((skillResponse) => {
                const skillSets = skillResponse.payload.filter((skillSet) => skillSet.type === SkillType.SKILL_SET);
                if (skillSets.length) {
                    const choices: Choices = [{
                        key: this._translateService.instant('private.common.skills.placeholderAll'),
                        value: 'all'
                    }];
                    skillResponse.payload.filter((skillSet) => skillSet.type === SkillType.SKILL_SET).forEach((skillSet) => {
                        choices.push({
                            value: skillSet.id.toString(),
                            key: displaySkillSetTitle(skillSet),
                        });
                    });
                    this.skillsSetsChoices = choices;
                }
            });
        }
        return this.skillsSetsChoices;
    }

    selectionChangeSkillSets($event: MatSelectChange): void {
        const newValue = $event.value;
        if (newValue.includes('all') ||
            newValue.sort().toString() === this.skillsSetsChoices
                .filter((skillSet => skillSet.value !== 'all'))
                .map((skillSet) => skillSet.value).sort().toString()) {
            this.skillsSetsChoices.forEach((choice) => {
                if (choice.value !== 'all') {
                    choice.disabled = true;
                }
            });
            this.searchAdvanced.get('skillSets').setValue(['all']);
        } else {
            this.skillsSetsChoices.forEach((choice) => choice.disabled = false);
            this.searchAdvanced.get('skillSets').setValue(newValue);
        }
    }

}
