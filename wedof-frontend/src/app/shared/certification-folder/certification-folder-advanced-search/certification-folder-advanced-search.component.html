<div class="popover mat-elevation-z4">
    <form [formGroup]="searchAdvanced">
        <div class="flex flex-col overflow-y-auto" style="max-height: 66vh;">

            <div class="flex">
                <mat-form-field class="flex-auto gt-xs:pr-3">
                    <mat-label class="font-normal">{{ 'common.actions.search.includes' | translate }}</mat-label>
                    <input matInput formControlName="query" class="input"
                           [placeholder]="'common.table.filters.globalSearch' | translate"
                           maxlength="50">
                    <button type="button" *ngIf="searchAdvanced.get('query').value" mat-button matSuffix
                            mat-icon-button aria-label="Clear" (click)="clearValue('query', $event)">
                        <mat-icon svgIcon="close"></mat-icon>
                    </button>
                </mat-form-field>

                <mat-form-field class="flex-auto gt-xs:pl-3">
                    <mat-label
                        class="font-normal">{{ 'common.actions.search.certificationFolderState' | translate }}
                        {{ searchAdvanced.get('state').value?.length >= 1 ? '(' + searchAdvanced.get('state').value.length + ')' : '' }}
                    </mat-label>
                    <mat-select multiple formControlName="state">
                        <mat-option *ngFor="let state of certificationFolderStates" [value]="state.value">
                            {{ state.label | translate }}
                        </mat-option>
                    </mat-select>
                    <button type="button" *ngIf="searchAdvanced.get('state').value?.length > 0" mat-button matSuffix
                            mat-icon-button aria-label="Clear" (click)="clearValue('state', $event)">
                        <mat-icon svgIcon="close"></mat-icon>
                    </button>
                </mat-form-field>
            </div>


            <app-tag-input [labelClass]="'font-normal'"
                           [label]="'Tags'"
                           [creatingAvailable]="false"
                           [clearValueIcon]="true"
                           (clearValue)="clearValue('tags')"
                           [control]="searchAdvanced.get('tags')"></app-tag-input>

            <mat-label class="font-bold mb-1 mt-2">{{'private.common.registrationFolder.title' | translate}}</mat-label>
            <div class="flex">
                <mat-form-field class="flex-auto gt-xs:pr-3">
                    <mat-label
                        class="font-normal">{{ 'common.actions.search.state' | translate }}
                        {{ searchAdvanced.get('registrationFolderState').value?.length >= 1 ? '(' + searchAdvanced.get('registrationFolderState').value.length + ')' : '' }}
                    </mat-label>
                    <mat-select multiple formControlName="registrationFolderState">
                        <mat-option *ngFor="let registrationFolderState of registrationFolderStates"
                                    [value]="registrationFolderState.value">
                            {{ registrationFolderState.label | translate }}
                        </mat-option>
                    </mat-select>
                    <button type="button" *ngIf="searchAdvanced.get('registrationFolderState').value?.length > 0"
                            mat-button
                            matSuffix
                            mat-icon-button aria-label="Clear" (click)="clearValue('registrationFolderState', $event)">
                        <mat-icon svgIcon="close"></mat-icon>
                    </button>
                </mat-form-field>
                <mat-form-field class="flex-auto gt-xs:pr-3">
                    <mat-label class="font-normal">{{'common.actions.search.funding' | translate}}
                        {{searchAdvanced.get('registrationFolderType').value?.length >= 1 ? '(' + searchAdvanced.get('registrationFolderType').value.length + ')' : ''  }}</mat-label>
                    <mat-select multiple formControlName="registrationFolderType">
                        <mat-option *ngFor="let type of dataProviders" [value]="type.value">
                            {{type.label | translate}}
                        </mat-option>
                    </mat-select>
                    <button type="button" *ngIf="searchAdvanced.get('registrationFolderType').value?.length > 0" mat-button matSuffix
                            mat-icon-button aria-label="Clear" (click)="clearValue('registrationFolderType', $event)">
                        <mat-icon svgIcon="close"></mat-icon>
                    </button>
                </mat-form-field>
                <mat-form-field class="flex-auto gt-xs:pl-3">
                    <mat-label
                        class="font-normal">{{'common.actions.search.completionRate.onCertificationFolder' | translate}}</mat-label>
                    <mat-select formControlName="registrationFolderCompletionRate">
                        <mat-option *ngFor="let completionRate of registrationFolderCompletionRates"
                                    [value]="completionRate.value">
                            {{completionRate.label | translate}}
                        </mat-option>
                    </mat-select>
                    <button type="button" *ngIf="searchAdvanced.get('registrationFolderCompletionRate').value"
                            mat-button
                            matSuffix
                            mat-icon-button aria-label="Clear"
                            (click)="clearValue('registrationFolderCompletionRate', $event)">
                        <mat-icon svgIcon="close"></mat-icon>
                    </button>
                </mat-form-field>
            </div>

            <div class="flex">
                <div class="flex-auto gt-xs:pr-3">
                    <app-infinite-scroll [controlName]="'certifInfo'"
                                         [noEntriesFoundLabel]="'private.certification.folders.createFolder.form.certification.noCertification' | translate"
                                         [placeholderLabel]="'common.actions.search.select' | translate : {choice: 'une ou plusieurs certifications'}"
                                         [placeholder]="'common.actions.search.select' | translate : {choice: 'une ou plusieurs certification'}"
                                         [label]="'common.actions.search.certification' | translate "
                                         [formGroup]="searchAdvanced"
                                         [parameters]="{'limit' : 5}"
                                         [resultFormatter]="resultFormatterCertification"
                                         [searchMethod]="searchMethodCertification"
                                         [addToSearchResult]="addToSearchResultCertification"
                                         [containerClass]="'flex-auto '"
                                         [labelClass]="'font-normal'"
                                         [searchComparisonProperty]="'certifInfo'"
                                         [searchAdvanced]="true"
                                         [multiple]="true"
                                         (changeSelection)="getSkillSets(searchAdvanced.get('certifInfo').value)"
                                         [searchingLabel]="'common.actions.search.wait' | translate"
                    ></app-infinite-scroll>
                </div>
                <div class="flex-auto gt-xs:pl-3">
                    <app-infinite-scroll [controlName]="'siret'"
                                         [noEntriesFoundLabel]="'private.certification.partners.addPartner.partner.noOrganismFound' | translate"
                                         [placeholderLabel]="'common.actions.search.select' | translate : {choice: 'un ou plusieurs organismes'}"
                                         [placeholder]="'common.actions.search.select' | translate : {choice: 'un ou plusieurs organismes'}"
                                         [label]="'common.actions.search.organism' | translate "
                                         [formGroup]="searchAdvanced"
                                         [resultFormatter]="resultFormatterOrganism"
                                         [searchMethod]="searchMethodOrganism"
                                         [parameters]="{'sort' : 'name', 'order' : 'asc', 'limit' : 20}"
                                         [labelClass]="'font-normal'"
                                         [containerClass]="'flex-auto '"
                                         [searchComparisonProperty]="'siret'"
                                         [searchAdvanced]="true"
                                         [multiple]="true"
                                         [searchingLabel]="'common.actions.search.wait' | translate"
                    ></app-infinite-scroll>
                </div>
            </div>

            <mat-form-field class="flex-auto input-line"
                            *ngIf="searchAdvanced.get('certifInfo')?.value && (skillsSetsChoices?.length || searchAdvanced.get('skillSets')?.value)">
                <mat-label class="font-normal">{{'private.common.skills.form.skillSet' | translate}}
                    {{ searchAdvanced.get('skillSets').value?.length >= 1 ? '(' + searchAdvanced.get('skillSets').value.length + ')' : '' }}</mat-label>
                <mat-select formControlName="skillSets"
                            multiple="true"
                            (selectionChange)="selectionChangeSkillSets($event)">
                    <mat-option *ngFor="let skillSet of skillsSetsChoices"
                                [disabled]="skillSet.disabled"
                                [value]="skillSet.value">
                        {{ skillSet.key }}
                    </mat-option>
                </mat-select>
                <button type="button" *ngIf="searchAdvanced.get('skillSets').value" mat-button matSuffix
                        mat-icon-button aria-label="Clear" (click)="clearValue('skillSets', $event)">
                    <mat-icon svgIcon="close"></mat-icon>
                </button>
            </mat-form-field>

            <div class="flex">
                <mat-form-field
                    [class]="searchAdvanced.get('period')?.value ? 'flex-auto gt-xs:pr-3' : 'flex-auto input-line'">
                    <mat-label class="font-normal">{{'common.actions.search.period.title' | translate}}</mat-label>
                    <mat-select formControlName="period">
                        <mat-option *ngFor="let period of periods" [value]="period.value">
                            {{period.label | translate}}
                        </mat-option>
                    </mat-select>
                    <button type="button" *ngIf="searchAdvanced.get('period').value" mat-button matSuffix
                            mat-icon-button aria-label="Clear" (click)="clearValue('period', $event)">
                        <mat-icon svgIcon="close"></mat-icon>
                    </button>
                </mat-form-field>

                <mat-form-field class="flex-auto gt-xs:pr-3"
                                *ngIf="searchAdvanced.get('period')?.value === PeriodTypes.CUSTOM">
                    <mat-label class="font-normal">{{ 'common.actions.search.since' | translate }}</mat-label>
                    <input formControlName="since" matInput
                           [required]="searchAdvanced.get('period')?.value === PeriodTypes.CUSTOM"
                           [max]="searchAdvanced.get('until')?.value ? searchAdvanced.get('until')?.value : maxDate"
                           [matDatepicker]="datePickerSince">
                    <button type="button" class="mr-2" *ngIf="searchAdvanced.get('since').value" mat-button matSuffix
                            mat-icon-button aria-label="Clear" (click)="clearValue('since', $event)">
                        <mat-icon svgIcon="close"></mat-icon>
                    </button>
                    <mat-datepicker-toggle matSuffix [for]="datePickerSince"></mat-datepicker-toggle>
                    <mat-datepicker #datePickerSince></mat-datepicker>
                </mat-form-field>

                <mat-form-field class="flex-auto gt-xs:pl-3"
                                *ngIf="searchAdvanced.get('period')?.value === PeriodTypes.CUSTOM">
                    <mat-label class="font-normal">{{ 'common.actions.search.until' | translate }}</mat-label>
                    <input formControlName="until" matInput [max]="maxDate"
                           [required]="searchAdvanced.get('period')?.value === PeriodTypes.CUSTOM"
                           [min]="searchAdvanced.get('since')?.value"
                           [matDatepicker]="datePickerUntil">
                    <button type="button" class="mr-2" *ngIf="searchAdvanced.get('until').value" mat-button matSuffix
                            mat-icon-button aria-label="Clear" (click)="clearValue('until', $event)">
                        <mat-icon svgIcon="close"></mat-icon>
                    </button>
                    <mat-datepicker-toggle matSuffix [for]="datePickerUntil"></mat-datepicker-toggle>
                    <mat-datepicker #datePickerUntil></mat-datepicker>
                </mat-form-field>
            </div>

            <mat-form-field class="flex-auto"
                            *ngIf="searchAdvanced.get('period')?.value">
                <mat-label class="font-normal">{{ 'common.actions.search.filterOnStateDate' | translate }}</mat-label>
                <mat-select formControlName="filterOnStateDate">
                    <mat-optgroup *ngFor="let group of filterOnStates" [label]="group.name | translate">
                        <mat-option *ngFor="let filterOnState of group.values" [value]="filterOnState.value">
                            {{ filterOnState.label | translate }}
                        </mat-option>
                    </mat-optgroup>
                </mat-select>
                <button type="button" *ngIf="searchAdvanced.get('filterOnStateDate').value" mat-button matSuffix
                        mat-icon-button aria-label="Clear" (click)="clearValue('filterOnStateDate', $event)">
                    <mat-icon svgIcon="close"></mat-icon>
                </button>
            </mat-form-field>

            <mat-form-field class="flex-auto">
                <mat-label
                    class="font-normal">{{'common.actions.search.survey' | translate}}
                </mat-label>
                <mat-select multiple formControlName="survey">
                    <mat-option *ngFor="let survey of surveys"
                                [value]="survey.value">
                        {{ survey.label | translate }}
                    </mat-option>
                </mat-select>
                <button type="button" *ngIf="searchAdvanced.get('survey').value" mat-button matSuffix
                        mat-icon-button aria-label="Clear" (click)="clearValue('survey', $event)">
                    <mat-icon svgIcon="close"></mat-icon>
                </button>
            </mat-form-field>

            <div class="flex">
                <mat-form-field class="flex-auto gt-xs:pr-3">
                    <mat-label
                        class="font-normal">{{'common.actions.search.messageTemplate.title' | translate}}
                    </mat-label>
                    <mat-select multiple
                                [matTooltip]="(!hasMessageTemplate ? 'common.actions.search.messageTemplate.tooltip' : '') | translate"
                                formControlName="messageTemplate">
                        <mat-option [disabled]="!hasMessageTemplate"
                                    *ngFor="let messageTemplate of messageTemplateChoices"
                                    [value]="messageTemplate.value">
                            {{ messageTemplate.label | translate }}
                        </mat-option>
                    </mat-select>
                    <button type="button" *ngIf="searchAdvanced.get('messageTemplate').value" mat-button matSuffix
                            mat-icon-button aria-label="Clear" (click)="clearValue('messageTemplate', $event)">
                        <mat-icon svgIcon="close"></mat-icon>
                    </button>
                </mat-form-field>
                <mat-form-field class="flex-auto gt-xs:pl-3">
                    <mat-label
                        class="font-normal">{{'common.actions.search.messageState' | translate}}
                    </mat-label>
                    <mat-select multiple
                                [matTooltip]="(!hasMessageTemplate ? 'common.actions.search.messageTemplate.tooltip' : '') | translate"
                                formControlName="messageState">
                        <mat-option [disabled]="!hasMessageTemplate"
                                    *ngFor="let messageState of messageStates"
                                    [value]="messageState.value">
                            {{ messageState.label | translate }}
                        </mat-option>
                    </mat-select>
                    <button type="button" *ngIf="searchAdvanced.get('messageState').value" mat-button matSuffix
                            mat-icon-button aria-label="Clear" (click)="clearValue('messageState', $event)">
                        <mat-icon svgIcon="close"></mat-icon>
                    </button>
                </mat-form-field>
            </div>

            <div class="flex">
                <mat-form-field class="flex-auto gt-xs:pr-3 ">
                    <mat-label
                        class="font-normal">{{ 'common.actions.search.certificationFolderCdcState.title' | translate }}
                    </mat-label>
                    <mat-icon [matTooltip]="'common.actions.search.certificationFolderCdcState.tooltip' | translate"
                              [matTooltipPosition]="'above'" class="pr-2"
                              [matTooltipShowDelay]="500"
                              svgIcon="help_outline"></mat-icon>
                    <mat-select multiple formControlName="cdcState">
                        <mat-option *ngFor="let state of cdcStates"
                                    [value]="state.value">
                            {{ state.label | translate }}
                        </mat-option>
                    </mat-select>
                    <button type="button" *ngIf="searchAdvanced.get('cdcState').value?.length > 0" mat-button matSuffix
                            mat-icon-button aria-label="Clear" (click)="clearValue('cdcState', $event)">
                        <mat-icon svgIcon="close"></mat-icon>
                    </button>
                </mat-form-field>
                <mat-form-field class="flex-auto gt-xs:pl-3">
                    <mat-label
                        class="font-normal">{{ 'common.actions.search.cdcExcluded.title' | translate }}
                    </mat-label>
                    <mat-icon [matTooltip]="'common.actions.search.cdcExcluded.tooltip' | translate"
                              [matTooltipPosition]="'above'" class="pr-2"
                              [matTooltipShowDelay]="500"
                              svgIcon="help_outline"></mat-icon>
                    <mat-select formControlName="cdcExcluded">
                        <mat-option value=true>{{ 'common.actions.yes' | translate }}</mat-option>
                        <mat-option value=false>{{ 'common.actions.no' | translate }}</mat-option>
                    </mat-select>
                    <button type="button" *ngIf="searchAdvanced.get('cdcExcluded').value" mat-button matSuffix
                            mat-icon-button aria-label="Clear" (click)="clearValue('cdcExcluded', $event)">
                        <mat-icon svgIcon="close"></mat-icon>
                    </button>
                </mat-form-field>
            </div>
            <div class="flex">
                <mat-form-field class="flex-auto gt-xs:pr-3 ">
                    <mat-label
                        class="font-normal">{{ 'common.actions.search.cdcToExport.title' | translate }}
                    </mat-label>
                    <mat-icon [matTooltip]="'common.actions.search.cdcToExport.tooltip' | translate"
                              [matTooltipPosition]="'above'" class="pr-2"
                              [matTooltipShowDelay]="500"
                              svgIcon="help_outline"></mat-icon>
                    <mat-select formControlName="cdcToExport">
                        <mat-option value=true>{{ 'common.actions.yes' | translate }}</mat-option>
                        <mat-option value=false>{{ 'common.actions.no' | translate }}</mat-option>
                    </mat-select>
                    <button type="button" *ngIf="searchAdvanced.get('cdcToExport').value" mat-button matSuffix
                            mat-icon-button aria-label="Clear" (click)="clearValue('cdcToExport', $event)">
                        <mat-icon svgIcon="close"></mat-icon>
                    </button>
                </mat-form-field>
                <mat-form-field class="flex-auto gt-xs:pl-3">
                    <mat-label
                        class="font-normal">{{ 'common.actions.search.cdcCompliant.title' | translate }}
                    </mat-label>
                    <mat-icon [matTooltip]="'common.actions.search.cdcCompliant.tooltip' | translate"
                              [matTooltipPosition]="'above'" class="pr-2"
                              [matTooltipShowDelay]="500"
                              svgIcon="help_outline"></mat-icon>
                    <mat-select formControlName="cdcCompliant">
                        <mat-option value=true>{{ 'common.actions.yes' | translate }}</mat-option>
                        <mat-option value=false>{{ 'common.actions.no' | translate }}</mat-option>
                    </mat-select>
                    <button type="button" *ngIf="searchAdvanced.get('cdcCompliant').value" mat-button matSuffix
                            mat-icon-button aria-label="Clear" (click)="clearValue('cdcCompliant', $event)">
                        <mat-icon svgIcon="close"></mat-icon>
                    </button>
                </mat-form-field>
            </div>

        </div>

        <div class="flex mt-8 justify-between">
            <div class="flex items-center">
                <button mat-icon-button
                        type="button"
                        (click)="openManageFilterDialog()">
                    <mat-icon svgIcon="settings"></mat-icon>
                </button>
                <button type="button" mat-button
                        [disabled]="activeFiltersCount ===  0"
                        (click)="saveAsFilter()">{{ 'private.common.filters.saveAsFilter' | translate }}
                </button>
            </div>
            <div class="flex justify-end">
                <button type="button" class="opacity-54 mr-5"
                        (click)="resetUrl()">{{ 'common.actions.search.deleteAllFilters' | translate }}
                </button>
                <button type="submit" color="primary"
                        mat-flat-button
                        [disabled]="!searchAdvanced.valid"
                        (click)="searchButton()">{{ 'common.table.filters.globalSearch' | translate }}
                </button>
            </div>
        </div>
    </form>
</div>
