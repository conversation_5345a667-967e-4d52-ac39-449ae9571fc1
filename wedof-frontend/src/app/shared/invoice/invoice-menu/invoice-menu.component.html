<div class="flex align-center" *ngIf="withButton else onlyMenu">
</div>

<ng-template #onlyMenu>
    <button mat-icon-button
            (click)="$event.stopPropagation()"
            [matMenuTriggerFor]="actionsMenu"
            #menuTrigger="matMenuTrigger"
            title="Actions">
        <mat-icon svgIcon="more_vert"></mat-icon>
    </button>
</ng-template>

<mat-menu #actionsMenu="matMenu">
    <ng-template matMenuContent>
       <button type="button"  [disabled]="!subscription.allowCertifierPlus" mat-menu-item (click)="editInvoice()">
            <mat-icon color="primary" svgIcon="edit"></mat-icon>
            <span>{{ 'common.actions.update' | translate}}</span>
        </button>
        <a mat-menu-item href="{{ invoice.paymentLink }}" target="_blank"
           *ngIf="canPay && invoice.state === state.WAITING_PAYMENT && invoice.paymentLink">
            <mat-icon color="primary" svgIcon="credit_card"></mat-icon>
            <span>{{ 'private.common.invoice.menu.makePayment' | translate}}</span>
        </a>
        <button type="button" [disabled]="!subscription.allowCertifierPlus" mat-menu-item (click)="toState(state.PAID)"
                *ngIf="canMarkAsPaid && invoice.state !== state.PAID">
            <mat-icon color="primary" svgIcon="credit_card"></mat-icon>
            <span>{{ 'private.common.invoice.menu.markAsPaid' | translate}}</span>
        </button>
        <button type="button" [disabled]="!subscription.allowCertifierPlus" mat-menu-item (click)="toState(state.CANCELED)"
                *ngIf="canMarkAsCanceled && invoice.state !== state.CANCELED">
            <mat-icon color="primary" svgIcon="credit_card"></mat-icon>
            <span>{{ 'private.common.invoice.menu.markAsCanceled' | translate}}</span>
        </button>
        <button type="button" [disabled]="!subscription.allowCertifierPlus" mat-menu-item (click)="toState(state.WAITING_PAYMENT)"
                *ngIf="canMarkAsWaitingPayment && invoice.state !== state.WAITING_PAYMENT">
            <mat-icon color="primary" svgIcon="credit_card"></mat-icon>
            <span>{{ 'private.common.invoice.menu.markAsWaitingPayment' | translate}}</span>
        </button>
        <button type="button" mat-menu-item (click)="openInvoice()">
            <mat-icon svgIcon="mat_outline:open_in_new"></mat-icon>
            <span>{{ 'private.common.invoice.menu.download' | translate}}</span>
        </button>
        <button type="button" mat-menu-item [disabled]="!subscription.allowCertifierPlus" (click)="delete()">
            <mat-icon color="warn" svgIcon="delete"></mat-icon>
            <span>{{'common.actions.delete' | translate}}</span>
        </button>
    </ng-template>
</mat-menu>
