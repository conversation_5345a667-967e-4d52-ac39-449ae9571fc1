import {Component, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild} from '@angular/core';
import {MatDialog} from '@angular/material/dialog';
import {Invoice, InvoiceStates} from '../../api/models/invoice';
import {InvoiceService} from '../../api/services/invoice.service';
import {MatMenuTrigger} from '@angular/material/menu';
import {FileService} from '../../api/services/file.service';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../errors/errors.types';
import {filter, finalize, switchMap, takeUntil} from 'rxjs/operators';
import {DeletionConfirmationComponent} from '../../material/action-confirmation/deletion-confirmation.component';
import {ViewFileDialogComponent} from '../../file/dialogs/view-file-dialog/view-file-dialog.component';
import {File} from '../../api/models/file';
import {Select} from '@ngxs/store';
import {SubscriptionState} from '../../api/state/subscription.state';
import {Observable, Subject} from 'rxjs';
import {Subscription} from '../../api/models/subscription';
import {InvoiceDialogComponent} from '../invoice-dialog/invoice-dialog.component';

@Component({
    selector: 'app-invoice-menu',
    templateUrl: './invoice-menu.component.html',
    styleUrls: ['./invoice-menu.component.scss']
})
export class InvoiceMenuComponent implements OnInit, OnDestroy {

    isLoading = false;
    state = InvoiceStates;
    subscription: Subscription;

    @Input() withButton = false;
    @Input() invoice: Invoice;

    @Input() canPay: boolean;
    @Input() canMarkAsPaid: boolean;
    @Input() canMarkAsCanceled: boolean;
    @Input() canMarkAsWaitingPayment: boolean;

    @Output() errorMessages: EventEmitter<string[]> = new EventEmitter<[]>();
    @Output() processedInvoice: EventEmitter<Invoice> = new EventEmitter<Invoice>();
    @Output() deleteInvoice?: EventEmitter<any> = new EventEmitter<any>();
    @Output() loading?: EventEmitter<boolean> = new EventEmitter<boolean>();
    @ViewChild('menuTrigger') menuTrigger: MatMenuTrigger;

    @Select(SubscriptionState.subscription) subscription$: Observable<Subscription>;
    private _unsubscribeAll: Subject<void> = new Subject();

    constructor(private _dialog: MatDialog,
                private _fileService: FileService,
                private _invoiceService: InvoiceService) {
    }

    ngOnInit(): void {
        this.subscription$.pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe((subscription) => {
            this.subscription = subscription;
        });
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    openInvoice(): void {
        const file = this.invoice.fileType === 'application/pdf' ? {
            url: this.invoice.link,
            fileName: this.invoice.externalId + '.pdf',
            fileType: this.invoice.fileType
        } : {
            fileName: this.invoice.externalId,
            fileType: this.invoice.fileType,
            link: this.invoice.link
        };
        this._dialog.open(ViewFileDialogComponent, {
            width: '80%',
            height: '90%',
            data: {
                file: file,
                auth: true,
                title: ((_file: File) => _file.fileName),
                src: (_file: File) => '/api/invoices/' + this.invoice.id + '/file' ,
                files: [file]
            }
        });
        this.menuTrigger.closeMenu();
    }

    toState(state: InvoiceStates): void {
        this.isLoading = true;
        this.errorMessages.emit([]);
        this._invoiceService.state(this.invoice, state).pipe(
            finalize(() => this.isLoading = false)
        ).subscribe(
            (invoiceUpdated: Invoice) => {
                this.processedInvoice.emit(invoiceUpdated);
            }, (httpErrorResponse: HttpErrorResponse) => {
                this.errorMessages.emit((httpErrorResponse.error as ApiError).errorMessages);
            });
    }

    delete(): void {
        const dialogRef = this._dialog.open(DeletionConfirmationComponent, {
            panelClass: 'full-page-scroll-40',
            height: 'auto',
            data: {
                messageKey: 'private.common.invoice.menu.confirmDeletion',
                data: this.invoice
            }
        });
        dialogRef.componentInstance.actionValue$.pipe(
            filter((confirmation: boolean) => confirmation),
            switchMap(() => this._invoiceService.delete(this.invoice.id)),
            finalize(() => {
                dialogRef.componentInstance.close();
            })
        ).subscribe(() => {
            this.deleteInvoice.emit();
        });
    }

   editInvoice(): void {
        const dialogRef = this._dialog.open(InvoiceDialogComponent, {
            panelClass: ['full-page-scroll-50'],
            data: {
                invoice: this.invoice
            }
        });
        dialogRef.afterClosed().subscribe(res => {
            if (res?.data) {
                this.processedInvoice.emit(res?.data);
            }
        });
    }
}
