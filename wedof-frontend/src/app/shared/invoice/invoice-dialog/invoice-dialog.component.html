<app-dialog-layout
    [title]="(dialogData.invoice ? 'private.common.invoice.form.update' :'private.common.invoice.form.create') | translate"
    [actions]="actions"
    [disabled]="loading"
    (dialogClose)="closeModal()">

    <form [formGroup]="formGroup" class="flex flex-col">
        <app-form-fields formGroupName="invoiceForm"
                         [entity]="invoice"
                         class="grid grid-cols-6 gap-4"
                         [appFormFieldsData]="appFormFieldsData"
                         [formGroup]="formGroup"></app-form-fields>

        <div *ngIf="errorMessages?.length" class="flex items-center">
            <treo-message class="flex-auto" appearance="outline" [showIcon]="false" type="error">
                <ul>
                    <li *ngFor="let errorMessage of errorMessages">
                        {{ errorMessage }}
                    </li>
                </ul>
            </treo-message>
        </div>
        <ng-template #actions>
            <button type="button" [disabled]="loading || !formGroup.dirty || formGroup.invalid" (click)="dialogData.invoice ? update() : create()"
                    mat-flat-button color="primary">
                <mat-progress-spinner *ngIf="loading; else submitLabel" [diameter]="24" mode="indeterminate">
                </mat-progress-spinner>
                <ng-template #submitLabel>
                    {{ (dialogData.invoice ? 'common.actions.update' : 'common.actions.create') | translate }}
                </ng-template>
            </button>
        </ng-template>
    </form>
</app-dialog-layout>

