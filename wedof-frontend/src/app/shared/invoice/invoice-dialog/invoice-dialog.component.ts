import {Component, Inject, OnInit} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material/dialog';
import {AppFormFieldData} from '../../material/app-form-field/app-form-field.component';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {TranslateService} from '@ngx-translate/core';
import {Invoice, InvoiceStates, InvoiceTypes} from '../../api/models/invoice';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../errors/errors.types';
import {InvoiceService} from '../../api/services/invoice.service';
import {FormValidators} from '../../api/shared/form-validators';

@Component({
    selector: 'app-invoice-dialog',
    templateUrl: './invoice-dialog.component.html',
    styleUrls: ['./invoice-dialog.component.scss']
})
export class InvoiceDialogComponent implements OnInit {

    loading = false;
    errorMessages: string[] = [];
    formGroup: FormGroup;
    appFormFieldsData: AppFormFieldData[];
    invoice: Invoice;

    constructor(public dialogRef: MatDialogRef<InvoiceDialogComponent>,
                private _translateService: TranslateService,
                private _formBuilder: FormBuilder,
                private _invoiceService: InvoiceService,
                @Inject(MAT_DIALOG_DATA) public dialogData: {
                    entityClass?: string,
                    entityId?: string | number,
                    invoice: Invoice
                },
    ) {
    }

    ngOnInit(): void {
        this.invoice = this.dialogData.invoice;
        this.formGroup = this._formBuilder.group({
            invoiceForm: this._formBuilder.group({})
        });
        const invoiceFile = this.invoice?.fileType === 'application/pdf' ? {
            url: this.invoice.link,
            fileName: this.invoice.externalId + '.pdf',
            fileType: this.invoice.fileType
        } : null;


        const appFormFieldsData: AppFormFieldData[] = [
            {
                controlName: 'externalId',
                required: true,
                value: this.invoice?.externalId ?? null,
                label: 'private.common.invoice.form.externalId',
                type: 'text',
                colSpan: 3
            },
            {
                controlName: 'dueDate',
                removable: true,
                value: this.invoice?.dueDate ?? null,
                label: 'private.common.invoice.form.dueDate',
                type: 'datetime-local',
                colSpan: 3
            },
            {
                controlName: 'state',
                required: true,
                value: this.invoice?.state ?? null,
                label: 'private.common.invoice.state.title',
                type: 'select',
                choices: [
                    {
                        key: this._translateService.instant('private.common.invoice.state.value.waitingPayment'),
                        value: InvoiceStates.WAITING_PAYMENT
                    },
                    {
                        key: this._translateService.instant('private.common.invoice.state.value.paid'),
                        value: InvoiceStates.PAID
                    },
                    {
                        key: this._translateService.instant('private.common.invoice.state.value.canceled'),
                        value: InvoiceStates.CANCELED
                    }
                ],
                colSpan: 3
            },
            {
                controlName: 'type',
                required: true,
                value: this.invoice?.type ?? null,
                label: 'private.common.invoice.type.title',
                type: 'select',
                choices: [
                    {
                        key: this._translateService.instant('private.common.invoice.type.value.invoice'),
                        value: InvoiceTypes.INVOICE
                    },
                    {
                        key: this._translateService.instant('private.common.invoice.type.value.deposit'),
                        value: InvoiceTypes.DEPOSIT_INVOICE
                    },
                    {
                        key: this._translateService.instant('private.common.invoice.type.value.creditNote'),
                        value: InvoiceTypes.CREDIT_NOTE
                    }
                ],
                colSpan: 3
            },
            {
                controlName: 'paymentLink',
                value: this.invoice?.paymentLink ?? null,
                label: 'private.common.invoice.form.paymentLink.title',
                placeholder: 'private.common.invoice.form.paymentLink.placeholder',
                type: 'url',
                validators: [Validators.pattern(FormValidators.URL_PATTERN)],
                validatorsMessages: {
                    pattern: 'common.errors.url'
                }
            },
            {
                controlName: 'description',
                value: this.invoice?.description ?? null,
                label: 'private.common.invoice.form.description',
                type: 'textarea',
                maxLength: 5000
            },
            {
                controlName: 'chooseFileOrUrl',
                value: this.invoice?.fileType === 'link' ? 'url' : 'file',
                label: 'private.common.invoice.form.chooseFileOrUrl.title',
                type: 'radio',
                inline: true,
                choices: [
                    {
                        key: this._translateService.instant('private.common.invoice.form.chooseFileOrUrl.file'),
                        value: 'file'
                    },
                    {
                        key: this._translateService.instant('private.common.invoice.form.chooseFileOrUrl.link.title'),
                        value: 'url'
                    }
                ],
                change: (controlName, newValue, formData) => {
                    const appFormFieldUrl = formData.find(field => field.controlName === 'url');
                    const appFormFieldFile = formData.find(field => field.controlName === 'file');
                    appFormFieldFile.removed = newValue !== 'file';
                    appFormFieldFile.required = newValue === 'file';
                    appFormFieldUrl.removed = newValue === 'file';
                    appFormFieldUrl.required = newValue !== 'file';
                    return [appFormFieldFile, appFormFieldUrl];
                },
                colSpan: 3
            },
            {
                controlName: 'file',
                value: invoiceFile,
                removed: this.invoice && this.invoice?.fileType !== 'application/pdf',
                required: true,
                label: 'private.common.invoice.form.chooseFileOrUrl.file',
                type: 'file',
                chooseLabel: 'Choisir',
                removeLabel: 'Remplacer',
                fileTypesAccept: ['application/pdf'],
                removable: true,
                colSpan: 3
            },
            {
                controlName: 'url',
                value: this.invoice?.fileType === 'link' ? this.invoice?.link : null,
                required: true,
                removed: !this.invoice || this.invoice?.fileType === 'application/pdf',
                removable: true,
                placeholder: 'private.common.invoice.form.chooseFileOrUrl.link.placeholder',
                label: 'private.common.invoice.form.chooseFileOrUrl.link.title',
                type: 'text',
                validators: [Validators.pattern(FormValidators.URL_PATTERN)],
                validatorsMessages: {
                    pattern: 'common.errors.url'
                },
                colSpan: 3
            }
        ];
        this.appFormFieldsData = appFormFieldsData.filter(value => value != null);
    }

    closeModal(): void {
        this.dialogRef.close();
    }

    create(): void {
        this.loading = true;
        this.errorMessages = [];
        const invoiceFormGroup = this.formGroup.getRawValue().invoiceForm;
        this._invoiceService.create({
            externalId: invoiceFormGroup.externalId,
            type: invoiceFormGroup.type,
            state: invoiceFormGroup.state,
            link: invoiceFormGroup.link,
            paymentLink: invoiceFormGroup.paymentLink,
            description: invoiceFormGroup.description,
            dueDate: invoiceFormGroup.dueDate,
            file: invoiceFormGroup.url ?? invoiceFormGroup.file
        }, this.dialogData.entityClass, this.dialogData.entityId).subscribe({
            next: (invoiceCreated) => {
                this.loading = false;
                this.dialogRef.close({data: invoiceCreated});
            },
            error: (httpErrorResponse: HttpErrorResponse) => {
                this.loading = false;
                this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
            }
        });
    }


    update(): void {
        this.loading = true;
        this.errorMessages = [];
        const invoiceFormGroup = this.formGroup.getRawValue().invoiceForm;
        this._invoiceService.update({
            externalId: invoiceFormGroup.externalId,
            type: invoiceFormGroup.type,
            state: invoiceFormGroup.state,
            link: invoiceFormGroup.link,
            paymentLink: invoiceFormGroup.paymentLink,
            description: invoiceFormGroup.description,
            dueDate: invoiceFormGroup.dueDate,
            file: invoiceFormGroup.url ?? invoiceFormGroup.file
        }, this.invoice.id).subscribe({
            next: (invoiceUpdated) => {
                this.loading = false;
                this.dialogRef.close({data: invoiceUpdated});
            },
            error: (httpErrorResponse: HttpErrorResponse) => {
                this.loading = false;
                this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
            }
        });
    }

}
