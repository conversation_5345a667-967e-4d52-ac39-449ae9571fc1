import {Component, EventEmitter, Injector, Input, OnChanges, Output} from '@angular/core';
import {Observable} from 'rxjs';
import {TableFilter} from '../../material/table/table-filter/table-filter.component';
import {AbstractTableComponent} from '../../material/table/abstract-table.component';
import {PaginatedResponse} from '../../api/services/abstract-paginated.service';
import {TranslateService} from '@ngx-translate/core';
import {Invoice, InvoiceStates, InvoiceTypes} from '../../api/models/invoice';
import {InvoiceService} from '../../api/services/invoice.service';

@Component({
    selector: 'app-invoice-table',
    templateUrl: './invoice-table.component.html',
    styleUrls: ['./invoice-table.component.scss']
})
export class InvoiceTableComponent extends AbstractTableComponent<Invoice> implements OnChanges {

    public readonly InvoiceStates = InvoiceStates;

    constructor(injector: Injector,
                private _translateService: TranslateService,
                private _invoiceService: InvoiceService) {
        super(injector);
        this.displayedColumns = ['title', 'type', 'state', 'menu'];
        this.stateFilters = Object.values(InvoiceStates).map(value => ({
            label: `private.common.invoice.state.value.${value}`,
            value
        }));
        this.typeFilters = Object.values(InvoiceTypes).map(value => ({
            label: `private.common.invoice.type.value.${value}`,
            value
        }));
    }

    typeFilters: TableFilter[];
    stateFilters: TableFilter[];
    errorMessages: string[] = [];

    @Input() limit: number;
    @Input() columns: string[];
    @Input() entityId: number | string;
    @Input() entityClass: string;

    @Input() canPay: boolean;
    @Input() canMarkAsPaid: boolean;
    @Input() canMarkAsCanceled: boolean;
    @Input() canMarkAsWaitingPayment: boolean;

    @Output() edit: EventEmitter<Invoice> = new EventEmitter<Invoice>();

    ngOnChanges($event): void {
        this.displayedColumns = this.columns ?? this.displayedColumns;
        if (this.limit) {
            this.pageSizeOptions = [this.limit];
        }
        if ($event.entityId.currentValue !== $event.entityId.previousValue) {
            this.forceRefresh();
        }
    }

    protected refreshData(): Observable<PaginatedResponse<Invoice>> {
        return this._invoiceService.listByEntity(this.entityClass, this.entityId, {
            ...this._filters$.value,
            limit: this.paginator.pageSize,
            page: this.paginator.pageIndex + 1
        });
    }

    onRowClick(invoice: Invoice): void {
        this.edit.emit(invoice);
    }

    onApplyFilter(name: string, value: string): void {
        this.applyFilter({name: name, value: value});
    }

    processedInvoice(invoiceToUpdate: Invoice): void {
        this.displayedData = this.displayedData.map((invoice) => {
            return invoice.id === invoiceToUpdate.id ? invoiceToUpdate : invoice;
        });
    }

    deleteInvoice(): void {
        this.forceRefresh();
    }

    insertInvoice(): void {
        this.forceRefresh();
    }

    setErrorMessages(error: string[]): void {
        this.errorMessages = error;
    }

    setLoading(isLoading: boolean): void {
        this.isLoading = isLoading;
    }
}
