<app-wrapper-spinner class="bg-white" [active]="isLoading">
    <table [dataSource]="displayedData" mat-table
           *ngIf="(!isLoading && displayedData?.length > 0) || hasActiveFilters; else noInvoice">
        <ng-container matColumnDef="id">
            <th mat-header-cell *matHeaderCellDef>
                {{'private.common.invoice.column.id' | translate}}
            </th>
            <td *matCellDef="let invoice" class="text-left" mat-cell [ngClass]="'w-3/12'">
                <p [matTooltip]="invoice.externalId">
                    {{invoice.externalId.length > 11 ? invoice.externalId.substring(0, 8) + '...' : invoice.externalId}}
                </p>
                <p class="text-secondary">
                    {{ 'private.common.invoice.type.value.' + invoice.type | translate}}
                </p>
            </td>
        </ng-container>
        <ng-container matColumnDef="description">
            <th mat-header-cell *matHeaderCellDef>
                {{'private.common.invoice.column.description' | translate}}
            </th>
            <td *matCellDef="let invoice" class="text-left" [ngClass]="'w-6/12'" mat-cell>
                <p>{{invoice.description ? invoice.description : " "}}</p>
                <p class="text-secondary" *ngIf="invoice.dueDate">
                    {{'private.common.invoice.dueDate' | translate}} {{invoice.dueDate | date: 'dd/MM/yyyy'}}
                </p>
            </td>
        </ng-container>
        <ng-container matColumnDef="state">
            <th mat-header-cell *matHeaderCellDef class="pl-0">
                <app-table-filter [filters]="stateFilters" (selectFilter)="onApplyFilter('state', $event)">
                    {{'private.common.invoice.state.title' | translate}}
                </app-table-filter>
            </th>
            <td *matCellDef="let invoice" class="text-left" [ngClass]="'w-2/12'" mat-cell>
                <a href="{{invoice.paymentLink}}"
                   target="_blank"
                   *ngIf="invoice.state === InvoiceStates.WAITING_PAYMENT && invoice.paymentLink; else noLink">
                    {{ 'private.common.invoice.state.value.' + invoice.state | translate}}
                </a>
                <ng-template #noLink>
                    {{ 'private.common.invoice.state.value.' + invoice.state | translate}}
                </ng-template>
            </td>
        </ng-container>
        <ng-container matColumnDef="menu">
            <th mat-header-cell *matHeaderCellDef class="pl-0">
            </th>
            <td *matCellDef="let invoice" class="text-right" [ngClass]="'w-1/12'" mat-cell>
                <app-invoice-menu [invoice]="invoice"
                                  [canPay]="canPay"
                                  [canMarkAsPaid]="canMarkAsPaid"
                                  [canMarkAsCanceled]="canMarkAsCanceled"
                                  [canMarkAsWaitingPayment]="canMarkAsWaitingPayment"
                                  (errorMessages)="setErrorMessages($event)"
                                  (processedInvoice)="processedInvoice($event)"
                                  (deleteInvoice)="deleteInvoice()"
                                  (loading)="setLoading($event)">
                </app-invoice-menu>
            </td>
        </ng-container>

        <ng-container matColumnDef="noDataForFilters">
            <td mat-footer-cell *matFooterCellDef [attr.colspan]="displayedColumns.length">
                {{'common.table.filters.no-data' | translate }}
            </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row (click)="onRowClick(row)" *matRowDef="let row; columns: displayedColumns;"></tr>
        <tr mat-footer-row *matFooterRowDef="['noDataForFilters']"
            [hidden]="isLoading || displayedData?.length || !hasActiveFilters"></tr>
    </table>

    <app-paginator [length]="total"
                   [pageSizeOptions]="pageSizeOptions"
                   [scrollTopOnPageChange]="false"
                   (page)="onPageEvent($event)">
    </app-paginator>

    <mat-error *ngIf="errorMessages.length">
        <ul>
            <li *ngFor="let errorMessage of errorMessages">{{ errorMessage }}</li>
        </ul>
    </mat-error>

</app-wrapper-spinner>

<ng-template #noInvoice>
    <div class="text-center py-5" *ngIf="!isLoading">
        {{'private.common.invoice.noData' | translate}}
    </div>
</ng-template>
