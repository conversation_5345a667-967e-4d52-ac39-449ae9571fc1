import {CommonModule} from '@angular/common';
import {NgModule} from '@angular/core';
import {TranslateModule} from '@ngx-translate/core';
import {MaterialModule} from '../material/material.module';
import {MatCardModule} from '@angular/material/card';
import {PipesModule} from '../pipes/pipes.module';
import {MatTooltipModule} from '@angular/material/tooltip';
import {InfiniteScrollModule} from 'ngx-infinite-scroll';
import {InvoiceCardComponent} from './invoice-card/invoice-card.component';
import {InvoiceTableComponent} from './invoice-table/invoice-table.component';
import {InvoiceMenuComponent} from './invoice-menu/invoice-menu.component';
import {InvoiceDialogComponent} from './invoice-dialog/invoice-dialog.component';
import {ReactiveFormsModule} from '@angular/forms';

@NgModule({
    declarations: [
        InvoiceMenuComponent,
        InvoiceCardComponent,
        InvoiceTableComponent,
        InvoiceDialogComponent
    ],
    imports: [
        CommonModule,
        InfiniteScrollModule,
        TranslateModule,
        MaterialModule,
        MatCardModule,
        PipesModule,
        MatTooltipModule,
        ReactiveFormsModule
    ],
    exports: [
        InvoiceMenuComponent,
        InvoiceCardComponent,
        InvoiceTableComponent,
        InvoiceDialogComponent
    ]
})
export class InvoicesModule {
}
