<mat-card
    class="mat-card flex flex-auto flex-col mt-3 pl-0 pr-0 pb-0 pt-3 border shadow-none sm border-b-0"
    [ngClass]="{'card-loading':cardLoading}">
    <div class="flex items-center mb-2 pl-5 pr-5">
        <mat-icon
            [matBadge]="total > 0 ? total.toString() : null"
            matBadgePosition="below after"
            matBadgeSize="small"
            class="mr-3 card-loading-show text-4xl"
            color="primary"
            svgIcon="format_list_bulleted"></mat-icon>
        <div class="flex items-center">
            <span
                class="text-xl font-semibold card-loading-show">{{ 'private.common.invoice.title' | translate }}  </span>
        </div>
        <div class="ml-auto -mr-4 card-loading-show flex">
            <button *ngIf="subscription.allowCertifierPlus" type="button" mat-icon-button (click)="create()">
                <mat-icon svgIcon="add_circle"></mat-icon>
            </button>
        </div>
    </div>
    <app-invoice-table #invoiceTableComponent
                       [ngClass]="'card-loading-show'"
                       [columns]="columns"
                       [limit]="limit"
                       [entityId]="entityId"
                       [entityClass]="entityClass"
                       (edit)="openInvoice($event)"
                       [canPay]="canPay"
                       [canMarkAsPaid]="canMarkAsPaid"
                       [canMarkAsCanceled]="canMarkAsCanceled"
                       [canMarkAsWaitingPayment]="canMarkAsWaitingPayment"
                       (totalRows)="totalRowsFired($event)">
    </app-invoice-table>
</mat-card>
