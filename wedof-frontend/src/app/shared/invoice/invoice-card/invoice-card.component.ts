import {Component, ElementRef, Input, OnChanges, OnDestroy, OnInit, ViewChild} from '@angular/core';
import {MatDialog} from '@angular/material/dialog';
import {Observable, Subject} from 'rxjs';
import {Select} from '@ngxs/store';
import {Invoice} from '../../api/models/invoice';
import {BaseCardComponentDirective, RequiredCallSuper} from '../../utils/base-card/base-card.directive';
import {File} from '../../api/models/file';
import {ViewFileDialogComponent} from '../../file/dialogs/view-file-dialog/view-file-dialog.component';
import {FileService} from '../../api/services/file.service';
import {SubscriptionState} from '../../api/state/subscription.state';
import {Subscription} from '../../api/models/subscription';
import {takeUntil} from 'rxjs/operators';
import {InvoiceTableComponent} from '../invoice-table/invoice-table.component';
import {InvoiceDialogComponent} from '../invoice-dialog/invoice-dialog.component';

@Component({
    selector: 'app-invoice-card',
    templateUrl: './invoice-card.component.html',
    styleUrls: ['./invoice-card.component.scss']
})
export class InvoiceCardComponent extends BaseCardComponentDirective implements OnInit, OnDestroy, OnChanges {

    static COMPONENT_ID = 'factures';

    total: number = null;
    columns: string[] = ['id', 'description', 'state', 'menu'];
    limit = 8;

    private _unsubscribeAll: Subject<void> = new Subject();

    @Input() canPay: boolean;
    @Input() canMarkAsPaid: boolean;
    @Input() canMarkAsCanceled: boolean;
    @Input() canMarkAsWaitingPayment: boolean;

    @Input() entityId: number | string;
    @Input() entityClass: string;
    @Input() entity: object;

    @ViewChild('invoiceTableComponent') private invoiceTableComponent: InvoiceTableComponent;

    subscription: Subscription;
    @Select(SubscriptionState.subscription) subscription$: Observable<Subscription>;

    constructor(private _fileService: FileService,
                private _dialog: MatDialog,
                private _el: ElementRef
    ) {
        super(InvoiceCardComponent.COMPONENT_ID, _el);
        this.panelLoading();
    }

    ngOnInit(): void {
        this.subscription$.pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe((subscription) => {
            this.subscription = subscription;
        });
    }

    totalRowsFired($event: number): void {
        if (this.total === null || $event !== this.total) {
            this.panelLoaded();
        }
        this.total = $event;
    }

    ngOnChanges(): void {
        this.total = null;
        this.panelLoading();
    }

    openInvoice(invoice: Invoice): void {
        const file = invoice.fileType === 'application/pdf' ? {
            url: invoice.link,
            fileName: invoice.externalId + '.pdf',
            fileType: invoice.fileType
        } : {
            fileName: invoice.externalId,
            fileType: invoice.fileType,
            link: invoice.link
        };
        this._dialog.open(ViewFileDialogComponent, {
            width: '80%',
            height: '90%',
            data: {
                file: file,
                auth: true,
                title: ((_file: File) => _file.fileName),
                src: (_file: File) => '/api/invoices/' + invoice.id + '/file' ,
                files: [file]
            }
        });
    }

    ngOnDestroy(): RequiredCallSuper {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
        return super.ngOnDestroy();
    }

    create(): void {
        const dialogRef = this._dialog.open(InvoiceDialogComponent, {
            panelClass: ['full-page-scroll-50'],
            data: {
                entityClass: this.entityClass,
                entityId: this.entityId,
            }
        });
        dialogRef.afterClosed().subscribe(res => {
            if (res?.data) {
                this.total = this.total + 1;
                this.invoiceTableComponent.insertInvoice();
            }
        });
    }
}
