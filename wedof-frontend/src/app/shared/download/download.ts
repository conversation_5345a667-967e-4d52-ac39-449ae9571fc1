import {HttpEvent, HttpEventType, HttpProgressEvent, HttpResponse} from '@angular/common/http';
import {Observable} from 'rxjs';
import {distinctUntilChanged, scan} from 'rxjs/operators';

function isHttpResponse<T>(event: HttpEvent<T>): event is HttpResponse<T> {
    return event.type === HttpEventType.Response;
}

function isHttpProgressEvent(
    event: HttpEvent<unknown>
): event is HttpProgressEvent {
    return (
        event.type === HttpEventType.DownloadProgress ||
        event.type === HttpEventType.UploadProgress
    );
}

export interface Download<T> {
    content: T | null;
    progress: number;
    state: 'PENDING' | 'IN_PROGRESS' | 'DONE';
}

export function download(
    saver?: (b: Blob, f?: string) => void
): (source: Observable<HttpEvent<Blob>>) => Observable<Download<Blob>> {
    return (source: Observable<HttpEvent<Blob>>) =>
        source.pipe(
            scan(
                (_download: Download<Blob>, event): Download<Blob> => {
                    if (isHttpProgressEvent(event)) {
                        return {
                            progress: event.total
                                ? Math.round((100 * event.loaded) / event.total)
                                : _download.progress,
                            state: 'IN_PROGRESS',
                            content: null
                        };
                    }
                    if (isHttpResponse(event)) {
                        const contentD = event.headers.get('content-disposition');
                        const filename = contentD && contentD.split('filename=').length > 0 ? contentD.split('filename=')[1] : null;
                        if (saver) {
                            saver(event.body, filename);
                        }
                        return {
                            progress: 100,
                            state: 'DONE',
                            content: event.body
                        };
                    }
                    return _download;
                },
                {state: 'PENDING', progress: 0, content: null}
            ),
            distinctUntilChanged((a, b) => a.state === b.state
                && a.progress === b.progress
                && a.content === b.content
            )
        );
}
