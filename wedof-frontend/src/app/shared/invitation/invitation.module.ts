import { NgModule } from '@angular/core';
import { InvitationComponent } from './invitation.component';
import {TranslateModule} from '@ngx-translate/core';
import {MarkdownModule} from 'ngx-markdown';
import {MaterialModule} from '../material/material.module';

@NgModule({
    declarations: [
        InvitationComponent
    ],
    imports: [
        MaterialModule,
        TranslateModule,
        MarkdownModule
    ],
    exports: [
        InvitationComponent,
    ]
})
export class InvitationModule { }
