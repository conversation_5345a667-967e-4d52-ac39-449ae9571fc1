<div class="my-6 font-medium ql-bg-black">
    <p [innerHTML]="'common.invitation.' + (terminate ? 'subtitleTerminate' : 'subtitle') | translate:{ organism: invitingOrganism.name } | markdown">
    </p>
</div>
<div class="d-flex justify-content-center">
    <p>
        {{ ('common.invitation.content.' + (terminate ? 'titleTerminate' : 'title')) | translate }}
    </p>
    <ul class="pb-6 pl-20 pr-5">
        <li class="py-4 flex flex-row border-b mr-30">
            <mat-icon svgIcon="playlist_add_check" class="pr-2"></mat-icon>
            <p class="self-center"
               [innerHTML]="'common.invitation.content.condition1' | translate | markdown">
            </p>
        </li>
        <li class="py-4 flex flex-row border-b mr-30">
            <mat-icon svgIcon="insert_drive_file" class="pr-2"></mat-icon>
            <p class="self-center"
               [innerHTML]="'common.invitation.content.condition2' | translate | markdown">
            </p>
        </li>
        <li class="py-4 flex flex-row border-b mr-30">
            <mat-icon svgIcon="person_outline" class="pr-2"></mat-icon>
            <p class="self-center"
               [innerHTML]="'common.invitation.content.condition3' | translate:{organism: invitingOrganism.name} | markdown">
            </p>
        </li>
        <li class="py-4 flex flex-row border-b mr-30">
            <mat-icon svgIcon="tv" class="pr-2"></mat-icon>
            <p class="self-center"
               [innerHTML]="'common.invitation.content.condition4' | translate:{organism: invitingOrganism.name} | markdown">
            </p>
        </li>
        <li class="py-4 flex flex-row border-b mr-30">
            <mat-icon svgIcon="people_outline" class="pr-2"></mat-icon>
            <p class="self-center"
               [innerHTML]="'common.invitation.content.condition5' | translate:{organism: invitingOrganism.name} | markdown">
            </p>
        </li>
    </ul>
</div>
