import {registerLocaleData} from '@angular/common';
import localeFr from '@angular/common/locales/fr';
import {LOCALE_ID, NgModule} from '@angular/core';

@NgModule({
    providers: [
        {provide: LOCALE_ID, useValue: 'fr'}, // used by i18n pipes (DatePipe, I18nPluralPipe, CurrencyPipe, DecimalPipe and PercentPipe)
    ]
})
export class I18nModule {
    constructor() {
        registerLocaleData(localeFr, 'fr'); // load fr locale
    }
}
