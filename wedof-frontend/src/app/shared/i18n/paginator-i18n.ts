import { MatPaginatorIntl } from '@angular/material/paginator';
import { TranslateService } from '@ngx-translate/core';
import { first } from 'rxjs/operators';

export class PaginatorI18n {

    constructor(private readonly _translateService: TranslateService) { }

    getPaginatorIntl(): MatPaginatorIntl {
        const paginatorIntl = new MatPaginatorIntl();
        paginatorIntl.itemsPerPageLabel = '';
        paginatorIntl.firstPageLabel = '';
        paginatorIntl.nextPageLabel = '';
        paginatorIntl.previousPageLabel = '';
        paginatorIntl.lastPageLabel = '';
        paginatorIntl.getRangeLabel = () => '';
        this._translateService.get('common.table.pagination').pipe(first()).subscribe(texts => {
            paginatorIntl.itemsPerPageLabel = texts.itemsPerPage;
            paginatorIntl.firstPageLabel = texts.firstPage;
            paginatorIntl.nextPageLabel = texts.nextPage;
            paginatorIntl.previousPageLabel = texts.previousPage;
            paginatorIntl.lastPageLabel = texts.lastPage;
            paginatorIntl.getRangeLabel = this.getRangeLabel.bind(this);
        });
        return paginatorIntl;
    }

    private getRangeLabel(page: number, pageSize: number, length: number): string {
        const _length = Math.max(length, 0);
        if (!_length || !pageSize) {
            return this._translateService.instant('common.table.pagination.rangePageNoResult', { length: _length });
        }
        const start = page * pageSize + 1;
        const end = Math.min(start + pageSize - 1, _length);
        return this._translateService.instant('common.table.pagination.rangePage', { start, end, length: _length });
    }
}
