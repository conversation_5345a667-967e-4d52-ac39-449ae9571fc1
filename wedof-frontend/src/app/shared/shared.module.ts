import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {TranslateModule} from '@ngx-translate/core';
import {MarkdownModule} from 'ngx-markdown';

import {CertificationModule} from './certification/certification.module';
import {ChartModule} from './chart/chart.module';
import {MaterialModule} from './material/material.module';
import {OrganismModule} from './organism/organism.module';
import {RegistrationFolderModule} from './registration-folder/registration-folder.module';
import {MatButtonModule} from '@angular/material/button';
import {NgxMatSelectSearchModule} from 'ngx-mat-select-search';
import {getSaver, SAVER} from './download/saver.provider';
import {CertificationFolderModule} from './certification-folder/certification-folder.module';
import {AttendeeModule} from './attendee/attendee.module';
import {ProposalModule} from './proposal/proposal.module';
import {SidesModule} from './sides/sides.module';
import {PipesModule} from './pipes/pipes.module';
import {ExportCsvModule} from './exportCsv/export-csv.module';
import {CdcFileModule} from './cdc-files/cdc-file.module';
import {NotificationMessageComponent} from './notification-message/notification-message.component';
import {ConnectionModule} from './connection/connection.module';
import {FiltersModule} from './filters/filters.module';
import {AttendeeExperienceModule} from './attendee-experience/attendee-experience.module';
import {CertificationFolderSurveyModule} from './certification-folder-survey/certification-folder-survey.module';
import {MetadataModule} from './metadata/metadata.module';
import {WorkingContractModule} from './working-contract/working-contract.module';

@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        MaterialModule,
        ChartModule,
        TranslateModule,
        MarkdownModule,
        CertificationModule,
        MatButtonModule,
    ],
    exports: [
        FormsModule,
        CommonModule,
        MaterialModule,
        ReactiveFormsModule,
        PipesModule,
        NgxMatSelectSearchModule,
        SidesModule,
        ChartModule,
        AttendeeModule,
        MarkdownModule,
        OrganismModule,
        ProposalModule,
        TranslateModule,
        CertificationModule,
        RegistrationFolderModule,
        CertificationFolderModule,
        ExportCsvModule,
        CdcFileModule,
        NotificationMessageComponent,
        ConnectionModule,
        FiltersModule,
        AttendeeExperienceModule,
        CertificationFolderSurveyModule,
        MetadataModule,
        WorkingContractModule
    ],
    providers: [
        {provide: SAVER, useFactory: getSaver}
    ],
    declarations: [
        NotificationMessageComponent
    ]
})
export class SharedModule {
}
