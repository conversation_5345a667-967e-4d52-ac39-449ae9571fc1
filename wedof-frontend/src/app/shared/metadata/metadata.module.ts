import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {MetadataCardComponent} from './metadata-card/metadata-card.component';
import {MaterialModule} from '../material/material.module';
import {TranslateModule} from '@ngx-translate/core';
import {MatCardModule} from '@angular/material/card';
import {ReactiveFormsModule} from '@angular/forms';


@NgModule({
    declarations: [MetadataCardComponent],
    imports: [
        CommonModule,
        MaterialModule,
        TranslateModule,
        MatCardModule,
        ReactiveFormsModule
    ],
    exports: [MetadataCardComponent]
})
export class MetadataModule {
}
