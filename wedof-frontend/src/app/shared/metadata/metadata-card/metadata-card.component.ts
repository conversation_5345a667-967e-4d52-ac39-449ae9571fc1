import {Component, ElementRef, EventEmitter, Input, OnChanges, Output, SimpleChanges} from '@angular/core';
import {BaseCardComponentDirective} from '../../utils/base-card/base-card.directive';
import {EntityClass} from '../../utils/enums/entity-class';
import {CertificationFolder} from '../../api/models/certification-folder';
import {RegistrationFolderService} from '../../api/services/registration-folder.service';
import {RegistrationFolder} from '../../api/models/registration-folder';
import {Proposal} from '../../api/models/proposal';
import {Organism} from '../../api/models/organism';
import {FormBuilder, FormGroup} from '@angular/forms';
import {ProposalService} from '../../api/services/proposal.service';
import {MetadataService} from '../../api/services/metadata.service';
import {CertificationFolderService} from '../../api/services/certification-folder.service';
import {AppFormFieldData} from '../../material/app-form-field/app-form-field.component';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../errors/errors.types';
import {Store} from '@ngxs/store';
import {OrganismState, UpdateOrganism} from '../../api/state/organism.state';
import {mergeMap} from 'rxjs/operators';
import {ReplaySubject} from 'rxjs';
import {CertificationPartner} from '../../api/models/certification-partner';
import {CertificationPartnerService} from '../../api/services/certification-partner.service';

@Component({
    selector: 'app-metadata-card',
    templateUrl: './metadata-card.component.html',
    styleUrls: ['./metadata-card.component.scss']
})
export class MetadataCardComponent extends BaseCardComponentDirective implements OnChanges {
    static COMPONENT_ID = 'metadonnees';

    loading = false;
    cardLoading = false;
    formGroup: FormGroup;
    errorMessages: string[];
    appFormFieldsData: AppFormFieldData[];
    appFormFieldData$?: ReplaySubject<AppFormFieldData> = new ReplaySubject<AppFormFieldData>();
    countCreate = 0;
    creatingAvailable = true;

    @Input() showInSide: boolean;
    @Input() entity: CertificationFolder | RegistrationFolder | Proposal | Organism | CertificationPartner;
    @Input() entityClass: EntityClass;
    @Input() organismSiret?: string;
    @Input() certifInfo?: string;
    @Output() entityChange = new EventEmitter<CertificationFolder | RegistrationFolder | Proposal | Organism | CertificationPartner>();

    constructor(private _el: ElementRef,
                private _certificationFolderService: CertificationFolderService,
                private _registrationFolderService: RegistrationFolderService,
                private _proposalService: ProposalService,
                private _certificationPartnerService: CertificationPartnerService,
                private _metadataService: MetadataService,
                private _store: Store,
                private _formBuilder: FormBuilder) {
        super(MetadataCardComponent.COMPONENT_ID, _el);
    }

    ngOnChanges(changes: SimpleChanges): void {
        this.panelLoading();
        this.initForm(this.entity.metadata);
    }

    initForm(metadata): void {
        this.cardLoading = true;
        this.errorMessages = [];
        this.formGroup = this._formBuilder.group({
            metadata: this._formBuilder.group({})
        });
        const appFormFieldsData: AppFormFieldData[] = [];
        const totalMetadata = metadata ? Object.keys(metadata).length : 0;
        if (totalMetadata > 0) {
            Object.keys(metadata).forEach((metadataEntry, index) => {
                const controlNameKey = metadataEntry + '-' + index.toString() + '-key';
                const controlNameValue = metadataEntry + '-' + index.toString() + '-value';
                appFormFieldsData.push({
                    controlName: controlNameKey,
                    value: metadataEntry,
                    placeholder: 'common.metadatas.placeholder',
                    type: 'search',
                    searchMethod: (searchTerm) => {
                        return this._metadataService.find(searchTerm, this.entityClass);
                    },
                    isCreateAvailable: this.creatingAvailable,
                    showCreateText: 'common.metadatas.showCreateText',
                    showSearchingText: 'common.metadatas.showSearchingText',
                    openDialogCreate: (searchingValue?: string) => {
                        const formControlKey = this.formGroup.get('metadata').get(controlNameKey);
                        formControlKey.setValue(searchingValue);
                        formControlKey.markAsDirty();
                        // ugly hack hide search after select
                        const overlay = document.getElementsByClassName('cdk-overlay-backdrop-showing')[0] as HTMLElement;
                        overlay.click();
                    },
                    colSpan: 2,
                });
                appFormFieldsData.push({
                    controlName: controlNameValue,
                    value: metadata[metadataEntry],
                    type: typeof metadata[metadataEntry] === 'string' && metadata[metadataEntry].includes('http') ? 'url' : 'text',
                    href: metadata[metadataEntry],
                    colSpan: 4,
                    required: true,
                    removable: true,
                    useConfirmDeleteDialog: true,
                    iconRemovable: 'delete',
                    iconClassRemovable: 'text-red',
                    change: (controlName, newValue, formData) => {
                        if (newValue === null) {
                            const appFormFieldKey = formData.find(field => field.controlName === controlNameKey);
                            const appFormFieldValue = formData.find(field => field.controlName === controlNameValue);
                            appFormFieldKey.removed = true;
                            appFormFieldValue.removed = true;
                            return [appFormFieldKey, appFormFieldValue];
                        }
                    }
                });
            });

            this.appFormFieldsData = appFormFieldsData;
            this.cardLoading = false;
        } else {
            this.countCreate = 1;
            const controlNameKey = 'newEntryMetadata-' + this.countCreate.toString() + '-key';
            const controlNameValue = 'newEntryMetadata-' + this.countCreate.toString() + '-value';
            appFormFieldsData.push({
                controlName: controlNameKey,
                placeholder: 'common.metadatas.placeholder',
                value: null,
                type: 'search',
                searchMethod: (searchTerm) => {
                    return this._metadataService.find(searchTerm, this.entityClass);
                },
                isCreateAvailable: this.creatingAvailable,
                showCreateText: 'common.metadatas.showCreateText',
                showSearchingText: 'common.metadatas.showSearchingText',
                openDialogCreate: (searchingValue?: string) => {
                    const formControlKey = this.formGroup.get('metadata').get(controlNameKey);
                    formControlKey.setValue(searchingValue);
                    formControlKey.markAsDirty();
                    // ugly hack hide search after select
                    const overlay = document.getElementsByClassName('cdk-overlay-backdrop-showing')[0] as HTMLElement;
                    overlay.click();
                },
                colSpan: 2,
            });
            appFormFieldsData.push({
                controlName: controlNameValue,
                placeholder: 'Saisir une valeur',
                value: null,
                type: 'text',
                colSpan: 4,
            });
            this.appFormFieldsData = appFormFieldsData;
            this.cardLoading = false;
        }
    }

    create(): void {
        this.countCreate += 1;
        const controlNameKey = 'newEntryMetadata-' + this.countCreate.toString() + '-key';
        const controlNameValue = 'newEntryMetadata-' + this.countCreate.toString() + '-value';
        const keyAppFormField = {
            controlName: controlNameKey,
            placeholder: 'common.metadatas.placeholder',
            value: null,
            type: 'search',
            searchMethod: (searchTerm) => {
                return this._metadataService.find(searchTerm, this.entityClass);
            },
            isCreateAvailable: this.creatingAvailable,
            showCreateText: 'common.metadatas.showCreateText',
            showSearchingText: 'common.metadatas.showSearchingText',
            openDialogCreate: (searchingValue?: string) => {
                const formControlKey = this.formGroup.get('metadata').get(controlNameKey);
                formControlKey.setValue(searchingValue);
                formControlKey.markAsDirty();
                // ugly hack hide search after select
                const overlay = document.getElementsByClassName('cdk-overlay-backdrop-showing')[0] as HTMLElement;
                overlay.click();
            },
            colSpan: 2,
        } as AppFormFieldData;
        const valueAppFormField = {
            controlName: controlNameValue,
            placeholder: 'Saisir une valeur',
            value: null,
            type: 'text',
            colSpan: 4,
            class: '-mb-3',
        } as AppFormFieldData;
        this.appFormFieldData$.next(keyAppFormField);
        this.appFormFieldData$.next(valueAppFormField);
        this.appFormFieldsData.push(keyAppFormField);
        this.appFormFieldsData.push(valueAppFormField);
    }

    update(): void {
        this.errorMessages = [];
        this.loading = true;
        const metadataNew = {};
        Object.keys(this.formGroup.getRawValue().metadata).forEach(key => {
            if (key.endsWith('-key')) {
                const metadataKey = this.formGroup.getRawValue().metadata[key];
                const metadataValue = this.formGroup.getRawValue().metadata[key.replace('-key', '-value')];
                if (metadataKey && metadataValue) {
                    metadataNew[metadataKey] = metadataValue;
                }
            }
        });
        if (this.entityClass === EntityClass.ORGANISM) {
            this._store.dispatch(new UpdateOrganism(this.organismSiret, {
                siret: this.organismSiret,
                metadata: JSON.stringify(metadataNew)
            }))
                .pipe(mergeMap(() => this._store.selectOnce(OrganismState.organism)))
                .subscribe({
                    next: (entityUpdated) => {
                        this.loading = false;
                        this.initForm(entityUpdated.metadata);
                    },
                    error: (httpErrorResponse: HttpErrorResponse) => {
                        this.loading = false;
                        this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
                    }
                });
        } else {
            const entityUpdated = {...this.entity, metadata: JSON.stringify(metadataNew)};
            this.updateEntity(entityUpdated);
        }
    }

    updateEntity(entityUpdated): void {
        if (this.entityClass === EntityClass.CERTIFICATION_FOLDER) {
            this._certificationFolderService.update(entityUpdated).subscribe({
                next: (entity) => {
                    this.loading = false;
                    this.entityChange.emit(entity);
                    this.initForm(entity.metadata);
                },
                error: (httpErrorResponse: HttpErrorResponse) => {
                    this.loading = false;
                    this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
                }
            });
        } else if (this.entityClass === EntityClass.REGISTRATION_FOLDER) {
            this._registrationFolderService.update(entityUpdated).subscribe({
                next: (entity) => {
                    this.loading = false;
                    this.entityChange.emit(entity);
                    this.initForm(entity.metadata);
                },
                error: (httpErrorResponse: HttpErrorResponse) => {
                    this.loading = false;
                    this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
                }
            });
        } else if (this.entityClass === EntityClass.PROPOSAL) {
            this._proposalService.update(entityUpdated).subscribe({
                next: (entity) => {
                    this.loading = false;
                    this.entityChange.emit(entity);
                    this.initForm(entity.metadata);
                },
                error: (httpErrorResponse: HttpErrorResponse) => {
                    this.loading = false;
                    this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
                }
            });
        } else {
            this._certificationPartnerService.update(this.certifInfo, this.organismSiret, {metadata: entityUpdated.metadata}).subscribe({
                next: (entity) => {
                    this.loading = false;
                    this.entityChange.emit(entity);
                    this.initForm(entity.metadata);
                },
                error: (httpErrorResponse: HttpErrorResponse) => {
                    this.loading = false;
                    this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
                }
            });
        }
    }

    getSubtitleText(): string {
        return this.entityClass === EntityClass.CERTIFICATIONS_PARTNER ? ' ce partenariat de certification' :
            this.entityClass === EntityClass.ORGANISM ? ' votre organisme' :
                this.entityClass === EntityClass.PROPOSAL ? ' cette proposition' :
                    this.entityClass === EntityClass.REGISTRATION_FOLDER ? ' ce dossier de formation' : ' ce dossier de certification';
    }
}
