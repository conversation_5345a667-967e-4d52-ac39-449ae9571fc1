<mat-card class="mat-card flex h-full flex-auto flex-col mt-3 p-5 pb-0 pt-3 border shadow-none sm"
          *ngIf="showInSide; else showOtherTemplate">
    <ng-template [ngTemplateOutlet]="metadataTemplate"></ng-template>
</mat-card>

<ng-template #showOtherTemplate>
    <treo-card class="p-8 w-full flex flex-col">
        <ng-template [ngTemplateOutlet]="metadataTemplate"></ng-template>
    </treo-card>
</ng-template>


<ng-template #metadataTemplate>
    <div class="flex items-center mb-4">
        <mat-icon class="mr-3 card-loading-show text-4xl" color="primary">dataset</mat-icon>
        <div
            class="text-xl font-semibold card-loading-show">{{"common.metadatas.title" | translate}}</div>
        <div class="ml-auto -mr-4 card-loading-show">
            <button type="button" mat-icon-button (click)="create()">
                <mat-icon svgIcon="add_circle"></mat-icon>
            </button>
        </div>
    </div>
    <div *ngIf="!cardLoading" class="card-loading-show">
        <p class="mb-4 card-loading-show">{{'common.metadatas.subtitle' | translate : {entity: getSubtitleText()} }}</p>
        <form [formGroup]="formGroup">
            <div class="flex mb-2">
                <p class="font-semibold w-1/3">{{'common.metadatas.label.key' | translate}}</p>
                <p class="font-semibold w-2/3 ml-2">{{'common.metadatas.label.value' | translate}}</p>
            </div>
            <app-form-fields class="grid grid-cols-6 gap-2 "
                             formGroupName="metadata"
                             [appFormFieldsData]="appFormFieldsData"
                             [appFormFieldData$]="appFormFieldData$"
                             [formGroup]="formGroup"></app-form-fields>
        </form>

        <div class=" flex align-center mt-2 justify-center">
            <button type="button" (click)="create()"
                    class="bg-gray-200 "
                    mat-button>
                {{ 'common.metadatas.create' | translate }}
            </button>
        </div>

        <div
            class="flex items-center mt-2 justify-end border-t pl-5 pr-10 -mx-5 py-3 light:bg-cool-gray-50 dark:bg-cool-gray-700 card-loading-hidden">
            <div class="flex">
                <mat-error *ngIf="errorMessages?.length">
                    <ul>
                        <li *ngFor="let errorMessage of errorMessages">{{ errorMessage }}</li>
                    </ul>
                </mat-error>
                <button type="button" (click)="update()" class="flex align-center" mat-flat-button color="primary"
                        [disabled]="loading || formGroup.invalid || !formGroup.dirty">
                    <mat-progress-spinner class="mr-4" *ngIf="loading" [diameter]="24"
                                          mode="indeterminate"></mat-progress-spinner>
                    <ng-container
                        *ngIf="!loading">{{ 'common.actions.update' | translate }}
                    </ng-container>
                </button>
            </div>
        </div>
    </div>
</ng-template>
