import {Component, EventEmitter, Input, Output} from '@angular/core';
import {RegistrationFolder, RegistrationFolderControlStates} from '../../api/models/registration-folder';
import {DataProviders} from '../../api/models/connection';
import {finalize} from 'rxjs/operators';
import {RegistrationFolderService} from '../../api/services/registration-folder.service';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../errors/errors.types';

@Component({
    selector: 'app-registration-folder-control',
    templateUrl: './registration-folder-control.component.html',
    styleUrls: ['./registration-folder-control.component.scss']
})
export class RegistrationFolderControlComponent {

    @Input() folder: RegistrationFolder;
    @Output() errorMessages: EventEmitter<string[]> = new EventEmitter<[]>();
    @Output() loading: EventEmitter<boolean> = new EventEmitter<boolean>();
    @Output() processedFolder: EventEmitter<RegistrationFolder> = new EventEmitter<RegistrationFolder>();
    @Output() initRegistrationFolder: EventEmitter<RegistrationFolder> = new EventEmitter<RegistrationFolder>();

    controlStates = RegistrationFolderControlStates;
    dataProviders = DataProviders;

    constructor(private _registrationFolderService: RegistrationFolderService) {
    }

    updateControlState(controlState: RegistrationFolderControlStates): void {
        this.errorMessages.emit([]);
        this.loading.emit(true);
        const registrationFolder: RegistrationFolder = {
            ...this.folder, ...{
                controlState: controlState
            }
        };
        this._registrationFolderService.update(registrationFolder).pipe(
            finalize(() => {
                this.loading.emit(false);
            })
        ).subscribe((updatedFolder) => {
                this.folder = {...this.folder, ...updatedFolder};
                this.processedFolder.emit(updatedFolder);
                this.initRegistrationFolder.emit(updatedFolder);
            },
            (httpErrorResponse: HttpErrorResponse) => {
                this.errorMessages.emit((httpErrorResponse.error as ApiError).errorMessages);
            });
    }

}
