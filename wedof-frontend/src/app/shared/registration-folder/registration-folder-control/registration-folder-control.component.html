<button mat-icon-button
        (click)="$event.stopPropagation()"
        *ngIf="folder.type !== dataProviders.CPF; else showCpfButton"
        [matMenuTriggerFor]="actionsMenu"
        [matTooltipPosition]="'above'"
        [matTooltip]="'private.common.registrationFolder.controlState.update' | translate">
    <mat-icon [matTooltip]="'private.common.registrationFolder.controlState.tooltip.' + folder.controlState | translate" [color]="folder.controlState === controlStates.IN_CONTROL ? 'warn' : 'primary'">{{folder.controlState | registrationFolderControlStateToIcon}}</mat-icon>
</button>

<ng-template #showCpfButton>
    <mat-icon class="h-10" [title]="'private.common.registrationFolder.controlState.tooltip.' + folder.controlState | translate"
              [color]="folder.controlState === controlStates.IN_CONTROL ? 'warn' : 'primary'">
        {{folder.controlState | registrationFolderControlStateToIcon}}
    </mat-icon>
</ng-template>

<mat-menu #actionsMenu="matMenu" class="large-menu">
    <ng-template matMenuContent [ngSwitch]="folder?.controlState">
        <ng-container *ngSwitchCase="controlStates.NOT_IN_CONTROL">
            <button type="button" mat-menu-item (click)="updateControlState(controlStates.IN_CONTROL)">
                <mat-icon
                    color="{{controlStates.IN_CONTROL | registrationFolderControlStateToColor}}">{{controlStates.IN_CONTROL | registrationFolderControlStateToIcon}}</mat-icon>
                <span>{{'private.common.registrationFolder.controlState.actions.inControl' | translate}}</span>
            </button>
        </ng-container>
        <ng-container *ngSwitchCase="controlStates.IN_CONTROL">
            <button type="button" mat-menu-item (click)="updateControlState(controlStates.RELEASED)">
                <mat-icon
                    color="{{controlStates.RELEASED | registrationFolderControlStateToColor}}">{{controlStates.RELEASED | registrationFolderControlStateToIcon}}</mat-icon>
                <span>{{'private.common.registrationFolder.controlState.actions.released' | translate}}</span>
            </button>
        </ng-container>
        <ng-container *ngSwitchCase="controlStates.RELEASED">
            <button type="button" mat-menu-item (click)="updateControlState(controlStates.IN_CONTROL)">
                <mat-icon
                    color="{{controlStates.IN_CONTROL | registrationFolderControlStateToColor}}">{{controlStates.IN_CONTROL | registrationFolderControlStateToIcon}}</mat-icon>
                <span>{{'private.common.registrationFolder.controlState.actions.inControl' | translate}}</span>
            </button>
        </ng-container>
    </ng-template>
</mat-menu>
