import {Component, EventEmitter, Injector, Input, OnInit, Output, ViewChild} from '@angular/core';
import {Observable} from 'rxjs';
import {debounceTime, filter, takeUntil, tap} from 'rxjs/operators';
import {Organism} from '../../api/models/organism';
import {RegistrationFolder, RegistrationFolderStates} from '../../api/models/registration-folder';
import {Session} from '../../api/models/session';
import {PaginatedResponse} from '../../api/services/abstract-paginated.service';
import {RegistrationFolderService} from '../../api/services/registration-folder.service';
import {AbstractSelectableTableComponent} from '../../material/table/abstract-selectable-table.component';
import {
    TableFilter,
    TableMultipleFilterComponent
} from '../../material/table/table-multiple-filter/table-multiple-filter.component';
import {Certification} from '../../api/models/certification';
import {DataQuery} from '../../material/table/abstract-table.component';
import {RegistrationFolderTableColumns} from './registration-folder-table-params';
import {RegistrationFolderStateToIconPipe} from '../../pipes/registration-folder-state-to-icon.pipe';
import {RegistrationFolderStateToColorPipe} from '../../pipes/registration-folder-state-to-color.pipe';
import {getRegistrationFolderAllDataProviders} from '../../utils/states-utils';
import {ActivatedRoute, Params, Router} from '@angular/router';
import {Attendee} from '../../api/models/attendee';
import {ThemePalette} from '@angular/material/core';
import {expectedCompletionRateColor} from '../../utils/color-utils';

export interface RegistrationFolderFilters {
    sessionId?: string;
    query?: string;
    type?: string;
    state?: string;
    billingState?: string;
    certificationFolderState?: string;
    certifInfo?: string;
    period?: string;
    since?: string;
    until?: string;
    filterOnStateDate?: string;
    proposalCode?: string;
    trainingActionId?: string;
    trainingId?: string;
    controlState?: string;
    completionRate?: string;
    daysSinceLastUpdatedCompletionRate?: number;
    messageTemplate?: string;
    messageState?: string;
    tags?: string;
}

export interface RegistrationFolderQuery extends DataQuery {
    sessionId: string;
    regFolderId: string;
}

@Component({
    selector: 'app-registration-folder-table',
    templateUrl: './registration-folder-table.component.html',
    styleUrls: ['./registration-folder-table.component.scss']
})

export class RegistrationFolderTableComponent extends AbstractSelectableTableComponent<RegistrationFolder> implements OnInit {

    constructor(
        injector: Injector,
        private _registrationFolderService: RegistrationFolderService,
        private _registrationFolderStateToIconPipe: RegistrationFolderStateToIconPipe,
        private _registrationFolderStateToColorPipe: RegistrationFolderStateToColorPipe,
        private _activatedRoute: ActivatedRoute,
        private _router: Router,
    ) {
        super(injector);
        this.stateFilters = Object.values(this.states).map(value => ({
            label: `private.training-organism.folders.common.state.${value}`,
            value: value,
            icon: this._registrationFolderStateToIconPipe.transform(value),
            color: this._registrationFolderStateToColorPipe.transform(value),
        }));
        this.dataProviderFilters = getRegistrationFolderAllDataProviders();
    }

    stateFilters: TableFilter[];
    states = RegistrationFolderStates;
    dataProviderFilters: TableFilter[];
    pendingSelectAll: boolean;
    readonly expectedCompletionRateColor = expectedCompletionRateColor;

    @Input() certification?: Certification;
    @Input() organism?: Organism;
    @Input() displayedColumns: RegistrationFolderTableColumns[];
    @Input() regFolderFilters$: Observable<RegistrationFolderFilters>;
    @Input() session?: Session;
    @Input() emptyMessage?: string;
    @Output() searchTag = new EventEmitter<string>();
    @ViewChild('stateFilterSelector') private stateTableMultipleFilterComponentState: TableMultipleFilterComponent;
    @ViewChild('fundingFilterSelector') private fundingTableMultipleFilterComponentState: TableMultipleFilterComponent;

    ngOnInit(): void {
        if (this.organism) {
            this.applyFilter({name: 'siret', value: this.organism.siret});
        }
        if (this.session) {
            this.applyFilter({name: 'sessionId', value: this.session.externalId});
        }
        if (this.certification) {
            this.applyFilter({name: 'certifInfo', value: this.certification.certifInfo});
        }

        this.regFolderFilters$?.pipe(
            filter(query => !this.session || query.sessionId === this.session.externalId),
            takeUntil(this._unsubscribeAll),
            debounceTime(500)
        ).subscribe(filters => {
            Object.entries(filters).forEach(([paramTitle]) => {
                const valueState = filters['state'] ? filters['state'].split(',') : [];
                this.stateTableMultipleFilterComponentState.updateCurrentFiltersData(valueState);
                const valueFunding = filters['type'] ? filters['type'].split(',') : [];
                this.fundingTableMultipleFilterComponentState.updateCurrentFiltersData(valueFunding);
                this.applyFilter({name: paramTitle, value: filters[paramTitle]});
            });
            this._changeDetectorRef.detectChanges();
        });
    }

    onFilterChange(accessStates: string[], filterName: string): void {
        if (this.session) {
            this.applyFilter({name: filterName, value: accessStates});
        } else {
            const queryParams: Params = {};
            queryParams[filterName] = accessStates.join(',');
            this._router.navigate(
                [],
                {
                    relativeTo: this._activatedRoute,
                    queryParams: queryParams,
                    queryParamsHandling: 'merge'
                });
        }
    }

    setSearchTag(tag: string): void {
        if (!this.session) {
            this.searchTag.emit(tag);
        }
    }

    selectAllForState(stateValue?: string): void {
        this.onFilterChange([stateValue], 'state');
        this.stateTableMultipleFilterComponentState.updateCurrentFiltersData(stateValue === undefined ? [] : [stateValue]);
        this.pendingSelectAll = true; // We cannot subscribe to the refresh from here, so we record that we want to select All
    }

    protected filterDataQuery(query: RegistrationFolderQuery): boolean {
        return !query.sessionId || query.sessionId === this.session.externalId;
    }

    protected findRowWithDataQuery(row: RegistrationFolder, query: RegistrationFolderQuery): boolean {
        return row.externalId === query.regFolderId;
    }

    protected refreshData(pageIndex = this.paginator.pageIndex): Observable<PaginatedResponse<RegistrationFolder>> {
        this.clearSelection();
        return this._registrationFolderService.list({
            ...this._filters$.value,
            order: this.sort?.direction,
            sort: this.sort?.active,
            limit: this.paginator.pageSize,
            page: pageIndex + 1
        }).pipe(tap(() => {
            if (this.pendingSelectAll === true) {
                setTimeout(() => { // Doesn't work if no timeout
                    this.selectAll();
                    this.pendingSelectAll = false;
                });
            }
        }));
    }

    refreshAttendee(attendee: Attendee): void {
        this.displayedData.forEach(registrationFolder => {
            if (registrationFolder.attendee.id === attendee.id) {
                registrationFolder.attendee = attendee;
                this.refreshRow(registrationFolder);
            }
        });
    }
}
