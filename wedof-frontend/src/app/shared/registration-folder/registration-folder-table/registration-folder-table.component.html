<ng-template #noData>
    <p class="bg-white p-6 cursor-auto text-center m-auto">
        {{ emptyMessage }}
    </p>
</ng-template>

<app-wrapper-spinner [active]="isLoading">
    <ng-container *ngIf="isLoading || displayedData?.length || activeFilters ; else noData">
        <table class="table-fixed" mat-table [dataSource]="displayedData" matSort matSortActive="lastUpdate"
               matSortDisableClear
               multiTemplateDataRows
               matSortDirection="desc">
            <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
            <tr *matRowDef="let row; let rowIndex = dataIndex; columns:['selectAll'];"
                [hidden]="rowIndex > 0 || total <= paginator.pageSize || !isAllSelected()"
                mat-row></tr>
            <tr mat-row (click)="onRowClick(row)" [ngClass]="{'selected':selectedRow?.externalId === row?.externalId}"
                *matRowDef="let row; columns: displayedColumns;"></tr>
            <tr mat-footer-row *matFooterRowDef="['noDataForFilters']" [hidden]="isLoading || displayedData?.length">
            </tr>

            <ng-container matColumnDef="selectAll">
                <td *matCellDef="let element" [attr.colspan]="displayedColumns.length" class="text-center" mat-cell>
                    <div *ngIf="!isTotalRowsSelected">
                        <span
                            class="text-warn font-semibold">{{ 'common.table.selection.current' | translate:{currentSelectionCount: currentSelectionCount()} }}</span>
                        <button (click)="selectAllTotalLine(); $event.stopPropagation()"
                                class="font-semibold pr-2 underline"
                                mat-button>
                            {{ 'common.table.selection.selectAll' | translate:{total: total} }}
                        </button>
                    </div>
                    <div *ngIf="isTotalRowsSelected">
                        {{ 'common.table.selection.currentAll' | translate:{currentSelectionCount: currentSelectionCount()} }}
                        <button mat-button class="font-semibold pr-2 underline"
                                (click)="toggleAll(); $event.stopPropagation()">
                            {{ 'common.table.selection.deselectAll' | translate }}
                        </button>
                    </div>
                </td>
            </ng-container>

            <ng-container matColumnDef="noDataForFilters">
                <td mat-footer-cell *matFooterCellDef [attr.colspan]="displayedColumns.length">
                    {{ 'common.table.filters.no-data' | translate }}
                </td>
            </ng-container>

            <ng-container matColumnDef="actions">
                <th class="text-left w-26 pl-4 border-t" mat-header-cell *matHeaderCellDef>
                    <div class="flex justify-between pr-2">
                        <div class="flex flex-row items-center cursor-pointer">
                            <mat-checkbox class="self-center checkbox-dropdown"
                                          (change)="$event ? toggleAll() : null"
                                          [checked]="(selection.hasValue() && isAllSelected()) || isTotalRowsSelected"
                                          [indeterminate]="selection.hasValue() && !isAllSelected()"
                                          [aria-label]="checkboxAllLabelKey() | translate">
                            </mat-checkbox>
                            <mat-icon class="icon-small" svgIcon="arrow_drop_down"
                                      [matMenuTriggerFor]="actionsMenu"></mat-icon>
                        </div>
                        <mat-menu #actionsMenu="matMenu" class="large-menu">
                            <button class="mat-menu-item"
                                    (click)="selectAllForState()">
                                {{ 'common.table.selection.all.select' | translate }}
                            </button>
                            <button *ngFor="let stateFilter of stateFilters" class="mat-menu-item"
                                    (click)="selectAllForState(stateFilter.value)">
                                <mat-icon class="ml-1"
                                          matPrefix
                                          [color]="stateFilter.color">{{ stateFilter.icon }}
                                </mat-icon>
                                {{ stateFilter.label | translate }}
                            </button>
                        </mat-menu>
                        <app-registration-folder-menu
                            [folders]="selection.selected"
                            (processedFolder)="refreshRow($event)"
                            [isTotalRowsSelected]="isTotalRowsSelected"
                            [totalFolderCount]="total"
                            [filters]="activeFilters">
                        </app-registration-folder-menu>
                    </div>
                </th>
                <td class="text-left w-26 pl-4" mat-cell *matCellDef="let row; let folder;">
                    <div class="flex">
                        <mat-checkbox (change)="$event ? toggleRow(row) : null" (click)="$event.stopPropagation()"
                                      class="self-center"
                                      [checked]="isRowSelected(row)" [disabled]="!isSelectable(row)"
                                      [aria-label]="checkboxRowLabelKey(row) | translate:{ name: row.attendee.firstName + ' ' + row.attendee.lastName }">
                        </mat-checkbox>
                        <app-registration-folder-menu class="ml-4"
                                                      [folders]="[folder]"
                                                      (processedFolder)="refreshRow($event)">
                        </app-registration-folder-menu>
                    </div>
                </td>
            </ng-container>

            <ng-container matColumnDef="lastName">
                <th class="border-t" mat-header-cell *matHeaderCellDef mat-sort-header disableClear>
                    {{ 'private.training-organism.folders.table.attendee' | translate }}
                </th>
                <td mat-cell *matCellDef="let folder">
                    <p class="truncate font-semibold">{{ folder.attendee.firstName | titlecase }} {{ folder.attendee.lastName | uppercase }}</p>
                    <div class="truncate text-secondary">{{ folder.attendee.email }}</div>
                </td>
            </ng-container>

            <ng-container matColumnDef="lastUpdate">
                <th class="border-t" mat-header-cell *matHeaderCellDef mat-sort-header>
                    <app-table-multiple-filter #stateFilterSelector
                                               [filters]="stateFilters"
                                               [title]='"état"'
                                               (selectFilters)="onFilterChange($event, 'state')">
                        {{ 'private.training-organism.folders.table.state'| translate }}
                    </app-table-multiple-filter>
                </th>
                <td mat-cell *matCellDef="let folder">
                    <p>{{ ('private.training-organism.folders.common.state.' + folder['state']) | translate }}</p>
                    <div class="text-secondary"
                         title="{{ 'private.common.registrationFolder.lastUpdate' | translate}}">{{ folder.lastUpdate | date: 'dd/MM/yy' }}
                    </div>
                </td>
            </ng-container>

            <ng-container matColumnDef="training-infos">
                <th class="border-t w-1/3" mat-header-cell *matHeaderCellDef>
                    {{ 'private.training-organism.folders.table.trainingName' | translate }}
                </th>
                <td *matCellDef="let folder" mat-cell [ngClass]="{'py-1':folder.completionRate}">
                    <p class="truncate font-semibold">{{ folder.trainingActionInfo.title }}</p>
                    <p class="truncate text-secondary">
                        Du {{ folder.trainingActionInfo.sessionStartDate | date:'mediumDate' }}
                        au {{ folder.trainingActionInfo.sessionEndDate | date:'mediumDate' }}</p>
                    <mat-progress-bar
                        class="mt-1 mb-1 card-loading-hidden"
                        mode="determinate"
                        matTooltipPosition="above"
                        *ngIf="folder.completionRate"
                        [matTooltip]="(folder.history.completionRateLastUpdate ? 'private.training-organism.folders.common.completionRateDoneDate' : 'private.training-organism.folders.common.completionRateDone') |translate: {
                        'completionRate':folder.completionRate,
                        'expectedCompletionRate': folder.completionRate === 100 ? '' : (' / ' + folder.expectedCompletionRate + ' % attendu'),
                        'date' : folder.history.completionRateLastUpdate | date : 'dd/MM/yyyy à hh:mm' }"
                        [color]="expectedCompletionRateColor(folder)"
                        [value]="folder.completionRate">
                    </mat-progress-bar>
                </td>
            </ng-container>

            <ng-container matColumnDef="funding">
                <th class="border-t" *matHeaderCellDef mat-header-cell>
                    <app-table-multiple-filter #fundingFilterSelector
                                               [filters]="dataProviderFilters"
                                               [title]="'financement'"
                                               (selectFilters)="onFilterChange($event, 'type')">
                        {{ 'private.training-organism.folders.table.funding' | translate }}
                    </app-table-multiple-filter>
                </th>
                <td *matCellDef="let folder" mat-cell>
                    <p>{{ folder.trainingActionInfo.totalExcl }} €</p>
                    <div class="truncate text-secondary">
                        {{ 'private.training-organism.folders.funding.' + folder.type | translate }}
                    </div>
                </td>
            </ng-container>

            <ng-container matColumnDef="tags">
                <th class="border-t" mat-header-cell *matHeaderCellDef>
                    {{ 'private.training-organism.folders.table.tags' | translate }}
                </th>
                <td *matCellDef="let folder" mat-cell>
                    <app-form-field-static *ngIf="folder.tags?.length"
                                           [lengthLimit]="2"
                                           [value]="folder.tags"
                                           (customClick)="setSearchTag($event)"
                                           type="tags"></app-form-field-static>
                </td>
            </ng-container>
        </table>
        <app-paginator [length]="total"
                       [pageSizeOptions]="pageSizeOptions"
                       [scrollTopOnPageChange]="true"
                       (page)="onPageEvent($event)">
        </app-paginator>
    </ng-container>
</app-wrapper-spinner>
