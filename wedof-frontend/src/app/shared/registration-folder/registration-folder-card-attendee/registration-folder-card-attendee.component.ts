import {Component, Input, OnInit} from '@angular/core';
import {
    RegistrationFolder,
    RegistrationFolderErrorStates,
    RegistrationFolderStates
} from '../../api/models/registration-folder';
import {FormBuilder, FormGroup} from '@angular/forms';
import {AppFormFieldData} from '../../material/app-form-field/app-form-field.component';
import {DataProviders} from '../../api/models/connection';
import {TranslateService} from '@ngx-translate/core';
import {SessionService} from '../../api/services/session.service';
import {startCase, toLower} from 'lodash-es';
import {expectedCompletionRateColor} from '../../utils/color-utils';

@Component({
    selector: 'app-registration-folder-card-attendee',
    templateUrl: './registration-folder-card-attendee.component.html',
    styleUrls: ['./registration-folder-card-attendee.component.scss']
})
export class RegistrationFolderCardAttendeeComponent implements OnInit {

    constructor(
        private _formBuilder: FormBuilder,
        private _translateService: TranslateService,
        private _sessionService: SessionService
    ) {
    }

    cardLoading = true;
    panelOpenState = false;
    certificationName: string;
    certificationLink: string;

    formGroup: FormGroup;
    appFormFieldsData: AppFormFieldData[];
    errorStates = RegistrationFolderErrorStates;
    dataProviders = DataProviders;
    readonly expectedCompletionRateColor = expectedCompletionRateColor;

    @Input() registrationFolder: RegistrationFolder;

    ngOnInit(): void {
        this.initForm(this.registrationFolder);
    }

    openPanel(): void {
        this.panelOpenState = true;
    }

    closePanel(): void {
        this.panelOpenState = false;
    }

    getRegistrationFolderLink(registrationFolder: RegistrationFolder): string | undefined {
        return registrationFolder?.type === DataProviders.CPF ?
            'https://www.moncompteformation.gouv.fr/espace-prive/html/#/dossiers/v2/' + registrationFolder.externalId :
            undefined;
    }

    initForm(registrationFolder: RegistrationFolder): void {
        if (!registrationFolder) {
            return;
        }
        this.cardLoading = true;
        const type = this._translateService.instant('private.training-organism.folders.common.type.' + registrationFolder.type);
        const startDate = new Date(registrationFolder.trainingActionInfo.sessionStartDate).toLocaleDateString();
        const endDate = new Date(registrationFolder.trainingActionInfo.sessionEndDate).toLocaleDateString();
        const completionRate = registrationFolder.completionRate;
        const expectedCompletionRate = registrationFolder.expectedCompletionRate;
        const sessionDate = registrationFolder.completionRate && registrationFolder.completionRate === 100 ?
            this._translateService.instant('private.training-organism.folders.common.dateWithCompletionRateWithNoExpectedCompletionRate', {
                startDate, endDate, completionRate
            }) : registrationFolder.completionRate ? this._translateService.instant('private.training-organism.folders.common.dateWithCompletionRate', {
            startDate, endDate, completionRate, expectedCompletionRate
        }) : registrationFolder.trainingActionInfo.sessionStartDate ? this._translateService.instant('private.training-organism.folders.common.date', {
            startDate, endDate
        }) : this._translateService.instant('private.training-organism.folders.common.noSessionDate');
        this._sessionService.getByHref(registrationFolder._links.session.href).subscribe((session) => {
            this.formGroup = this._formBuilder.group({
                registrationFolder: this._formBuilder.group({})
            });
            this.certificationLink = 'https://www.francecompetences.fr/recherche/';
            const externalId = registrationFolder?._links?.certification?.externalId;
            if (externalId) {
                const certificationType = externalId.startsWith('RS') ? 'RS' : 'RNCP';
                const certificationCode = externalId.split(certificationType)[1];
                this.certificationName = externalId + ' - ' + registrationFolder?._links?.certification?.name;
                this.certificationLink += (certificationType.toLowerCase() + '/' + certificationCode);
            } else {
                this.certificationName = registrationFolder?._links?.certification?.name;
            }

            const teachingModality = registrationFolder.trainingActionInfo.teachingModalities ?
                registrationFolder.trainingActionInfo.teachingModalities === '0' ?
                    this._translateService.instant('private.training-organism.folders.common.teachingModality.presentiel') :
                    registrationFolder.trainingActionInfo.teachingModalities === '1' ?
                        this._translateService.instant('private.training-organism.folders.common.teachingModality.mixte') :
                        registrationFolder.trainingActionInfo.teachingModalities === '2' ?
                            this._translateService.instant('private.training-organism.folders.common.teachingModality.distanciel') :
                            this._translateService.instant('private.common.form.placeholder') : session.sessionInfo.city.includes('distance') ?
                    this._translateService.instant('private.training-organism.folders.common.teachingModality.distanciel') : this._translateService.instant('private.common.form.placeholder');

            const city = registrationFolder.trainingActionInfo.teachingModalities && registrationFolder.trainingActionInfo.teachingModalities !== '2' ?
                (startCase(toLower(session.sessionInfo.city)) + ' (' + teachingModality + ')') : teachingModality;

            const appFormFieldsData: AppFormFieldData[] = [
                {
                    controlName: 'externalId',
                    disabled: true,
                    label: 'private.training-organism.folders.common.externalId',
                    type: registrationFolder.type === DataProviders.CPF ? 'url' : 'text',
                    icon: 'folder_open',
                    href: this.getRegistrationFolderLink(registrationFolder),
                    value: registrationFolder.externalId,
                    colSpan: 3,
                },
                {
                    controlName: 'type',
                    value: type,
                    disabled: true,
                    label: 'private.training-organism.folders.common.funding',
                    type: 'text',
                    icon: 'folder',
                    colSpan: 3,
                },
                {
                    controlName: 'title',
                    value: registrationFolder.trainingActionInfo.title,
                    disabled: true,
                    label: 'private.training-organism.folders.common.title',
                    type: 'text',
                    icon: 'title',
                },
                {
                    controlName: 'sessionDate',
                    value: sessionDate,
                    disabled: true,
                    label: 'private.training-organism.folders.common.sessionDate',
                    type: 'text',
                    icon: 'event'
                },
                {
                    removed: !registrationFolder.completionRate,
                    controlName: 'completionRate',
                    value: registrationFolder.completionRate + '%',
                    disabled: true,
                    label: 'private.training-organism.folders.common.completionRate',
                    type: 'text',
                    icon: 'trending_up',
                    colSpan: 2
                },
                {
                    controlName: 'indicativeDuration',
                    value: registrationFolder.trainingActionInfo.indicativeDuration ? registrationFolder.trainingActionInfo.indicativeDuration : null,
                    disabled: true,
                    label: 'private.training-organism.folders.common.duration',
                    type: 'hours',
                    icon: 'schedule',
                    colSpan: 2
                },
                {
                    controlName: 'hoursInCenter',
                    value: registrationFolder.trainingActionInfo.hoursInCenter,
                    disabled: true,
                    label: 'private.training-organism.folders.common.hoursInCenter',
                    type: 'hours',
                    icon: 'schedule',
                    colSpan: 2
                },
                {
                    controlName: 'hoursInCompany',
                    value: registrationFolder.trainingActionInfo.hoursInCompany,
                    disabled: true,
                    label: 'private.training-organism.folders.common.hoursInCompany',
                    type: 'hours',
                    icon: 'schedule',
                    colSpan: 2
                },
                {
                    controlName: 'city',
                    value: city,
                    disabled: true,
                    label: 'private.training-organism.folders.common.city',
                    type: 'text',
                    icon: 'explore',
                },
                {
                    controlName: 'totalExcl',
                    value: registrationFolder.trainingActionInfo.totalExcl,
                    disabled: true,
                    required: true,
                    label: 'private.training-organism.folders.common.amount',
                    type: 'price',
                    icon: 'euro_symbol',
                    colSpan: 2
                }
            ];
            this.appFormFieldsData = appFormFieldsData.filter(value => value != null); // Hack: do it in two steps to avoid complex typescript error
            this.cardLoading = false;
        });
    }

    includes(canceledOrRefusedStates: any, state: RegistrationFolderStates): boolean {
        return Object.values(canceledOrRefusedStates).includes(state);
    }

    isFranceTravailFunding(registrationFolder: RegistrationFolder): boolean {
        return registrationFolder?.trainingActionInfo.solicitations?.some(solicitation => solicitation.fundingType === 9);
    }
}
