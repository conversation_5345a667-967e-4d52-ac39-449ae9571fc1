<mat-card class="flex-col mt-3 p-5 pb-0 pt-3 border shadow-none sm w-full" [ngClass]="{'card-loading':cardLoading}">
    <div class="flex items-center mb-2">
        <mat-icon class="mr-3 card-loading-show text-4xl"
                  [color]="includes(errorStates, registrationFolder?.state) ? 'warn' : 'primary'"
                  title="{{ 'private.training-organism.folders.common.state.' + registrationFolder?.state | translate}}">
            {{ registrationFolder?.state | registrationFolderStateToIcon }}
        </mat-icon>
        <div class="truncate">
            <span class="text-xl font-semibold card-loading-show">
                {{ 'private.common.registrationFolder.title' | translate }}
                <img *ngIf="isFranceTravailFunding(registrationFolder)"
                     alt="{{'private.common.registrationFolder.franceTravailFunding' | translate}}"
                     style="display: inline; width: 20px;" class="ml-1" src="assets/icons/pe.svg">
            </span>
            <div class="text-secondary text-md truncate card-loading-show"
                 title="{{ 'private.training-organism.folders.common.state.' + registrationFolder?.state | translate}}">
                {{ 'private.training-organism.folders.common.state.' + registrationFolder?.state | translate }} -
                <span
                    title="{{ 'private.common.registrationFolder.lastUpdate' | translate}}">{{ registrationFolder?.lastUpdate | date: 'dd/MM/yyyy' }}</span>
            </div>
        </div>
    </div>
    <mat-progress-bar
        class="mt-2 mb-3 card-loading-hidden"
        mode="determinate"
        matTooltipPosition="above"
        *ngIf="registrationFolder.completionRate"
        [matTooltip]="(registrationFolder.history.completionRateLastUpdate ? 'private.training-organism.folders.common.completionRateDoneDate' : 'private.training-organism.folders.common.completionRateDone') |translate: {
        'completionRate':registrationFolder.completionRate,
        'expectedCompletionRate': registrationFolder.completionRate === 100 ? '' : (' / ' + registrationFolder.expectedCompletionRate + ' % attendu'),
        'date' : registrationFolder.history.completionRateLastUpdate | date : 'dd/MM/yyyy à hh:mm' }"
        [value]="registrationFolder.completionRate"
        [color]="expectedCompletionRateColor(registrationFolder)">
    </mat-progress-bar>
    <div class="grid grid-cols-6 gap-2" *ngIf="!panelOpenState || cardLoading">
        <app-form-field-static class="col-span-6"
                               icon="folder"
                               [type]="registrationFolder?.type === dataProviders.CPF ? 'url' : 'text'"
                               [value]="registrationFolder | displayRegistrationFolderExternalId"
                               [href]="getRegistrationFolderLink(registrationFolder)">
        </app-form-field-static>
        <app-form-field-static class="font-bold col-span-6" icon="title" type="text"
                               [value]="registrationFolder?.trainingActionInfo?.title">
        </app-form-field-static>
        <app-form-field-static class="font-bold col-span-6"
                               [value]="certificationName"
                               icon="fact_check"
                               [href]="certificationLink"
                               type="url">
        </app-form-field-static>
        <app-form-field-static class="font-bold col-span-6" icon="business" type="text"
                               [value]="registrationFolder?._links?.organism?.name">
        </app-form-field-static>
        <app-form-field-static class="col-span-6" type="text" icon="event"
                               *ngIf="registrationFolder?.trainingActionInfo?.sessionStartDate && registrationFolder?.completionRate"
                               [value]="(registrationFolder?.completionRate === 100 ? 'private.training-organism.folders.common.dateWithCompletionRateWithNoExpectedCompletionRate' :
                                'private.training-organism.folders.common.dateWithCompletionRate')| translate: {
                startDate: registrationFolder?.trainingActionInfo?.sessionStartDate | date : 'dd/MM/yyyy',
                endDate: registrationFolder?.trainingActionInfo?.sessionEndDate | date : 'dd/MM/yyyy',
                completionRate: registrationFolder?.completionRate,
                expectedCompletionRate: registrationFolder?.expectedCompletionRate
            }">
        </app-form-field-static>
        <app-form-field-static class="col-span-6" type="text" icon="event"
                               *ngIf="registrationFolder?.trainingActionInfo?.sessionStartDate && !registrationFolder?.completionRate else noSessionDate"
                               [value]="'private.training-organism.folders.common.date' | translate: {
            startDate: registrationFolder?.trainingActionInfo?.sessionStartDate | date : 'dd/MM/yyyy',
            endDate: registrationFolder?.trainingActionInfo?.sessionEndDate | date : 'dd/MM/yyyy'
        }">
        </app-form-field-static>
        <ng-template #noSessionDate>
            <app-form-field-static *ngIf="!registrationFolder?.trainingActionInfo?.sessionStartDate" class="col-span-6"
                                   type="text" icon="event"
                                   [value]="'private.training-organism.folders.common.noSessionDate' | translate"></app-form-field-static>
        </ng-template>

        <treo-message [type]="includes(errorStates, registrationFolder?.state)  ? 'error' : 'info'"
                      [showIcon]="false" class="col-span-6 my-2 card-loading-show" appearance="outline">
            <app-form-field-static icon="info" type="text"
                                   [value]="'private.attendee.registrationFolder.state.' + registrationFolder?.state | translate : {
                                        completionRate: registrationFolder?.completionRate || ''
                                   }"
            >
            </app-form-field-static>
        </treo-message>

        <button type="button" class="col-span-6 flex justify-center mt-2 -mx-5 pt-2 pb-2 open-panel card-loading-hidden"
                (click)="openPanel()">
            <mat-icon svgIcon="keyboard_arrow_down"></mat-icon>
        </button>
    </div>

    <form [formGroup]="formGroup" *ngIf="panelOpenState && !cardLoading"
          class="flex flex-col">
        <app-form-fields formGroupName="registrationFolder"
                         class="grid grid-cols-6 gap-2"
                         [entity]="registrationFolder"
                         [appFormFieldsData]="appFormFieldsData"
                         [formGroup]="formGroup">
        </app-form-fields>

        <button type="button" class="flex justify-center -mx-5 pt-2 pb-2 close-panel" (click)="closePanel()">
            <mat-icon svgIcon="keyboard_arrow_up"></mat-icon>
        </button>
    </form>


</mat-card>
