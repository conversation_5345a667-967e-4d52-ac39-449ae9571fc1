import {
    AfterViewChecked,
    AfterViewInit,
    ChangeDetectorRef,
    Component,
    ElementRef,
    EventEmitter,
    Input,
    OnChanges,
    OnDestroy,
    OnInit,
    Output,
    SimpleChanges,
    ViewChild
} from '@angular/core';
import {
    RegistrationFolder,
    RegistrationFolderControlStates,
    RegistrationFolderErrorStates,
    RegistrationFolderStates
} from '../../api/models/registration-folder';
import {FormBuilder, FormGroup, FormGroupDirective, Validators} from '@angular/forms';
import {TranslateService} from '@ngx-translate/core';
import {AppFormFieldData} from '../../material/app-form-field/app-form-field.component';
import {SessionService} from '../../api/services/session.service';
import {startCase, toLower} from 'lodash-es';
import {combineLatest, Observable, Subject} from 'rxjs';
import {RegistrationFolderService} from '../../api/services/registration-folder.service';
import {first, takeUntil} from 'rxjs/operators';
import {Organism} from '../../api/models/organism';
import moment from 'moment';
import business from 'moment-business';
import {OrganismService} from '../../api/services/organism.service';
import {RegistrationFolderBillingStateToIconPipe} from '../../pipes/registration-folder-billing-state-to-icon.pipe';
import {FileListComponent} from '../../file/file-list/file-list.component';
import {DataProviders} from '../../api/models/connection';
import {MatDialog} from '@angular/material/dialog';
import {Select} from '@ngxs/store';
import {OrganismState} from '../../api/state/organism.state';
import {Subscription} from '../../api/models/subscription';
import {SubscriptionState} from '../../api/state/subscription.state';
import {EntityClass} from '../../utils/enums/entity-class';
import {Activity} from '../../api/models/activity';
import {setUpIntersectionObserver} from '../../utils/shortcut-side-panel-utils';
import {DisplayRegistrationFolderExternalIdPipe} from '../../pipes/display-registration-folder-external-id.pipe';
import {BaseCardCanDeactivateDirective} from '../../utils/base-card/base-card-can-deactivate.directive';
import {RequiredCallSuper} from '../../utils/base-card/base-card.directive';
import {expectedCompletionRateColor} from '../../utils/color-utils';

@Component({
    selector: 'app-registration-folder-card',
    templateUrl: './registration-folder-card.component.html',
    styleUrls: ['./registration-folder-card.component.scss']
})
export class RegistrationFolderCardComponent extends BaseCardCanDeactivateDirective implements OnInit, OnChanges, AfterViewChecked, OnDestroy, AfterViewInit {

    constructor(
        private _formBuilder: FormBuilder,
        private _translateService: TranslateService,
        private _registrationFolderBillingStateToIconPipe: RegistrationFolderBillingStateToIconPipe,
        private _sessionService: SessionService,
        private _registrationFolderService: RegistrationFolderService,
        private _organismService: OrganismService,
        private _displayRegistrationFolderExternalIdPipe: DisplayRegistrationFolderExternalIdPipe,
        readonly changeDetectorRef: ChangeDetectorRef,
        private _dialog: MatDialog,
        private _el: ElementRef
    ) {
        super(RegistrationFolderCardComponent.COMPONENT_ID, _el);
    }

    public static COMPONENT_ID = 'dossierFormation';
    isOwner = false;
    cardLoading = true;
    loading = false;
    validationNotAllowed = false;
    panelOpenState = false;
    errorMessages: string[] = [];
    sessionDate = '';
    city = '';
    certificationName: string;
    certificationLink: string;

    organism: Organism;
    subscription: Subscription;
    cpfSessionMinDate: Date = null;
    formGroup: FormGroup;
    appFormFieldsData: AppFormFieldData[];
    errorStates = RegistrationFolderErrorStates;
    dataProviders = DataProviders;
    RegistrationFolderStates = RegistrationFolderStates;
    orderedRegistrationFolderStates: RegistrationFolderStates[] = Object.values(RegistrationFolderStates);
    readonly EntityClass = EntityClass;
    readonly expectedCompletionRateColor = expectedCompletionRateColor;

    @Input() registrationFolder: RegistrationFolder;
    @Input() openCard = false;
    @Output() registrationFolderChange: EventEmitter<RegistrationFolder> = new EventEmitter<RegistrationFolder>();

    @ViewChild('form') form: FormGroupDirective;
    @ViewChild('appFileList') appFileList: FileListComponent<RegistrationFolder, RegistrationFolderStates>;

    @Select(OrganismState.organism) organism$: Observable<Organism>;
    @Select(SubscriptionState.subscription) subscription$: Observable<Subscription>;

    private _unsubscribeAll: Subject<void> = new Subject<void>();

    ngOnChanges(changes: SimpleChanges): void {
        this.panelLoading();
        this.initForm(this.registrationFolder);
        if (this.openCard) {
            this.openPanel();
        }
    }

    ngOnInit(): void {
        combineLatest([
            this.organism$,
            this.subscription$
        ]).pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe(([organism, subscription]) => {
            this.organism = organism;
            this.subscription = subscription;
        });
        this._registrationFolderService.sessionMinDates$.pipe(first()).subscribe((sessionMinDates) => {
            this.cpfSessionMinDate = new Date(sessionMinDates.cpfSessionMinDate);
        });

    }

    openPanel(): void {
        this.panelOpenState = true;
    }

    closePanel(): void {
        this.panelOpenState = false;
    }

    initForm(registrationFolder: RegistrationFolder): void {
        this.errorMessages = [];
        this.shortcutSidePanelLoaded.emit({name: 'dossierFormation', loaded: false});
        this.cardLoading = true;
        if (!this.registrationFolder) {
            return;
        }
        const billingState = registrationFolder.billingState ?
            this._translateService.instant('private.training-organism.folders.common.billingState.' + registrationFolder.billingState) :
            undefined;
        const startDate = new Date(registrationFolder.trainingActionInfo.sessionStartDate).toLocaleDateString();
        const endDate = new Date(registrationFolder.trainingActionInfo.sessionEndDate).toLocaleDateString();
        const completionRate = registrationFolder.completionRate;
        const expectedCompletionRate = registrationFolder.expectedCompletionRate;
        const sessionDate = registrationFolder.completionRate && registrationFolder.completionRate === 100 ?
            this._translateService.instant('private.training-organism.folders.common.dateWithCompletionRateWithNoExpectedCompletionRate', {
                startDate, endDate, completionRate
            }) : registrationFolder.completionRate ? this._translateService.instant('private.training-organism.folders.common.dateWithCompletionRate', {
            startDate, endDate, completionRate, expectedCompletionRate
        }) : this._translateService.instant('private.training-organism.folders.common.date', {
            startDate, endDate
        });
        // In case data are obfuscated
        if (!registrationFolder?._links?.session?.href) {
            return;
        }
        this._sessionService.getByHref(registrationFolder._links.session.href).subscribe((session) => {
            this.formGroup = this._formBuilder.group({
                registrationFolder: this._formBuilder.group({})
            });
            this.isOwner = this.organism.siret === registrationFolder._links.organism.siret;
            this.validationNotAllowed = this.registrationFolder.state === RegistrationFolderStates.NOT_PROCESSED
                && this.registrationFolder.type === DataProviders.CPF
                && this.cpfSessionMinDate > new Date(this.registrationFolder.trainingActionInfo.sessionStartDate);

            this.certificationLink = 'https://www.francecompetences.fr/recherche/';
            const externalId = registrationFolder?._links?.certification?.externalId;
            if (externalId) {
                const certificationType = externalId.startsWith('RS') ? 'RS' : 'RNCP';
                const certificationCode = externalId.split(certificationType)[1];
                this.certificationName = externalId + ' - ' + registrationFolder?._links?.certification?.name;
                this.certificationLink += (certificationType.toLowerCase() + '/' + certificationCode);
            } else {
                this.certificationName = registrationFolder?._links?.certification?.name;
            }

            const teachingModality = registrationFolder.trainingActionInfo.teachingModalities ?
                registrationFolder.trainingActionInfo.teachingModalities === '0' ?
                    this._translateService.instant('private.training-organism.folders.common.teachingModality.presentiel') :
                    registrationFolder.trainingActionInfo.teachingModalities === '1' ?
                        this._translateService.instant('private.training-organism.folders.common.teachingModality.mixte') :
                        registrationFolder.trainingActionInfo.teachingModalities === '2' ?
                            this._translateService.instant('private.training-organism.folders.common.teachingModality.distanciel') :
                            this._translateService.instant('private.common.form.placeholder') : session.sessionInfo.city.includes('distance') ?
                    this._translateService.instant('private.training-organism.folders.common.teachingModality.distanciel') : this._translateService.instant('private.common.form.placeholder');

            const city = registrationFolder.trainingActionInfo.teachingModalities && registrationFolder.trainingActionInfo.teachingModalities !== '2' ?
                (startCase(toLower(session.sessionInfo.city)) + ' (' + teachingModality + ')') : teachingModality;

            let type;
            if (registrationFolder.type === DataProviders.OPCO_CFA && registrationFolder._links?.currentWorkingContract?.financer) {
                const currentWorkingLink = registrationFolder._links?.currentWorkingContract;
                type = this._translateService.instant('auth.' + currentWorkingLink?.financer + '.name');
                if (this.isOwner && currentWorkingLink?.idTrainingOrganism) {
                    type += ' (' + currentWorkingLink?.idTrainingOrganism + ')';
                } else if (!this.isOwner && currentWorkingLink?.idDeca) {
                    type += ' (' + currentWorkingLink?.idDeca + ')';
                }
            } else {
                type = this._translateService.instant('private.training-organism.folders.common.type.' + registrationFolder.type);
            }

            const appFormFieldsData: AppFormFieldData[] = [
                {
                    controlName: 'externalId',
                    disabled: true,
                    label: 'private.training-organism.folders.common.externalId',
                    type: registrationFolder.externalLink ? 'url' : 'text',
                    icon: 'folder_open',
                    href: registrationFolder.externalLink,
                    copy: true,
                    copyValue: registrationFolder.dataProviderId ?? registrationFolder.externalId,
                    value: this._displayRegistrationFolderExternalIdPipe.transform(registrationFolder),
                    colSpan: 3,
                },
                {
                    controlName: 'type',
                    value: type,
                    disabled: true,
                    label: 'private.training-organism.folders.common.funding',
                    type: 'text',
                    icon: 'folder',
                    colSpan: 3,
                },
                {
                    controlName: 'controlState',
                    removed: !this.isOwner || !registrationFolder.controlState || registrationFolder.controlState === RegistrationFolderControlStates.NOT_IN_CONTROL,
                    value: this._translateService.instant('private.common.registrationFolder.controlState.state.' + registrationFolder.controlState),
                    disabled: true,
                    label: 'common.actions.search.controlState',
                    type: 'text',
                    icon: 'rule_folder'
                },
                {
                    controlName: 'certification',
                    disabled: true,
                    type: 'url',
                    label: 'private.certification.folders.createFolder.form.certification.certification',
                    icon: 'fact_check',
                    value: this.certificationName,
                    href: this.certificationLink,
                    colSpan: this.isOwner ? 6 : 3
                },
                {
                    controlName: 'owner',
                    removed: this.isOwner,
                    disabled: true,
                    type: 'text',
                    label: 'private.training-organism.folders.table.organism',
                    icon: 'business',
                    value: registrationFolder?._links?.organism?.name,
                    colSpan: 3
                },
                {
                    controlName: 'title',
                    value: registrationFolder.trainingActionInfo.title,
                    disabled: true,
                    label: 'private.training-organism.folders.common.title',
                    type: registrationFolder.trainingActionInfo.externalLink ? 'url' : 'text',
                    href: registrationFolder.trainingActionInfo.externalLink,
                    icon: 'title',
                },
                {
                    removed: !this.isOwner ||
                        registrationFolder.state !== RegistrationFolderStates.NOT_PROCESSED && registrationFolder.state !== RegistrationFolderStates.VALIDATED &&
                        !registrationFolder._links.inPartnershipWith,
                    controlName: 'inPartnershipWith',
                    value: registrationFolder._links.inPartnershipWith,
                    disabled: registrationFolder.state !== RegistrationFolderStates.NOT_PROCESSED && registrationFolder.state !== RegistrationFolderStates.VALIDATED,
                    label: 'private.training-organism.folders.createFolder.form.partnership.label',
                    type: 'infiniteSearch',
                    help: (registrationFolder.state === RegistrationFolderStates.NOT_PROCESSED || registrationFolder.state === RegistrationFolderStates.VALIDATED) ? 'private.training-organism.folders.createFolder.form.partnership.toolTip' : null,
                    icon: 'cases',
                    placeholder: 'private.training-organism.folders.createFolder.form.partnership.placeholder',
                    searchNoEntriesFoundLabel: 'private.training-organism.folders.createFolder.form.partnership.noOrganismFound',
                    searchMethodPaginated: (params) => params.query?.length >= 2 ? this._organismService.find(params) : this._organismService.findInPartnershipWith(params),
                    parameters: {limit: 20, order: 'asc', sort: 'name'},
                    searchResultFormatter: (organismPartner: Organism) => organismPartner.name + ' (' + organismPartner.siret + ')',
                    searchComparisonProperty: 'siret',
                    removable: true,
                },
                {
                    removed: this.isOwner,
                    controlName: 'sessionDate',
                    value: sessionDate,
                    disabled: true,
                    label: 'private.training-organism.folders.common.sessionDate',
                    type: 'text',
                    icon: 'event'
                },
                {
                    removed: !this.isOwner,
                    controlName: 'sessionStartDate',
                    value: registrationFolder.trainingActionInfo?.sessionStartDate,
                    min: registrationFolder.type === DataProviders.CPF ? moment(this.cpfSessionMinDate) : null,
                    required: true,
                    disabled: registrationFolder.state !== RegistrationFolderStates.NOT_PROCESSED,
                    label: 'private.training-organism.folders.common.startDate',
                    type: 'date',
                    icon: 'event',
                    change: this.checkSessionDates,
                    validatorsMessages: {
                        min: 'common.errors.date',
                    },
                    colSpan: !registrationFolder.completionRate ? 3 : 2
                },
                {
                    removed: !this.isOwner,
                    controlName: 'sessionEndDate',
                    value: registrationFolder.trainingActionInfo?.sessionEndDate,
                    min: moment(new Date(registrationFolder.trainingActionInfo?.sessionStartDate)),
                    required: true,
                    disabled: registrationFolder.state !== RegistrationFolderStates.NOT_PROCESSED,
                    label: 'private.training-organism.folders.common.endDate',
                    type: 'date',
                    icon: 'event',
                    change: this.checkSessionDates,
                    validatorsMessages: {
                        min: 'common.errors.date',
                    },
                    colSpan: !registrationFolder.completionRate ? 3 : 2
                },
                {
                    removed: !this.isOwner || !registrationFolder.completionRate,
                    controlName: 'completionRate',
                    value: completionRate + '%'  + (completionRate === 100 ? '' : ' / ' + registrationFolder.expectedCompletionRate + '% attendu' ),
                    disabled: true,
                    label: 'private.training-organism.folders.common.completionRate',
                    type: 'text',
                    icon: 'trending_up',
                    colSpan: 2
                },
                {
                    controlName: 'indicativeDuration',
                    value: !this.isOwner ? registrationFolder.trainingActionInfo.indicativeDuration ? registrationFolder.trainingActionInfo.indicativeDuration : null :
                        registrationFolder.trainingActionInfo.indicativeDuration,
                    disabled: !this.isOwner ? true : registrationFolder.state !== RegistrationFolderStates.NOT_PROCESSED,
                    label: 'private.training-organism.folders.common.duration',
                    required: true,
                    min: registrationFolder.trainingActionInfo.hoursInCenter + registrationFolder.trainingActionInfo.hoursInCompany,
                    validators: [Validators.min(registrationFolder.trainingActionInfo.hoursInCenter + registrationFolder.trainingActionInfo.hoursInCompany)],
                    validatorsMessages: {
                        min: 'private.training-organism.folders.dialog.errors.indicativeDuration'
                    },
                    type: 'hours',
                    icon: 'schedule',
                    change: this.checkIndicativeTraining,
                    colSpan: 2
                },
                {
                    controlName: 'hoursInCenter',
                    value: registrationFolder.trainingActionInfo.hoursInCenter,
                    disabled: !this.isOwner ? true : registrationFolder.state !== RegistrationFolderStates.NOT_PROCESSED,
                    label: 'private.training-organism.folders.common.hoursInCenter',
                    type: 'hours',
                    icon: 'schedule',
                    max: registrationFolder.trainingActionInfo.indicativeDuration,
                    min: 0,
                    validators: [Validators.min(0), Validators.max(registrationFolder.trainingActionInfo.indicativeDuration)],
                    change: this.checkIndicativeTraining,
                    validatorsMessages: {
                        max: 'private.training-organism.folders.dialog.errors.hoursInCenter',
                    },
                    colSpan: 2
                },
                {
                    controlName: 'hoursInCompany',
                    value: registrationFolder.trainingActionInfo.hoursInCompany,
                    disabled: !this.isOwner ? true : registrationFolder.state !== RegistrationFolderStates.NOT_PROCESSED,
                    label: 'private.training-organism.folders.common.hoursInCompany',
                    type: 'hours',
                    icon: 'schedule',
                    min: 0,
                    max: registrationFolder.trainingActionInfo.indicativeDuration,
                    validators: [Validators.min(0), Validators.max(registrationFolder.trainingActionInfo.indicativeDuration)],
                    validatorsMessages: {
                        max: 'private.training-organism.folders.dialog.errors.hoursInCompany',
                    },
                    change: this.checkIndicativeTraining,
                    colSpan: 2
                },
                {
                    controlName: 'city',
                    value: city,
                    disabled: true,
                    label: 'private.training-organism.folders.common.city',
                    type: 'text',
                    icon: 'explore',
                },
                {
                    controlName: 'discountType',
                    removed: !(this.isOwner && registrationFolder.state === RegistrationFolderStates.NOT_PROCESSED),
                    label: 'private.training-organism.common.discountType.title',
                    type: 'select',
                    colSpan: 2,
                    value: 'none',
                    choices: [{
                        key: this._translateService.instant('private.training-organism.common.discountType.none.radio'),
                        value: 'none'
                    },
                        {
                            key: this._translateService.instant('private.training-organism.common.discountType.fixed.radio'),
                            value: 'fixed'
                        },
                        {
                            key: this._translateService.instant('private.training-organism.common.discountType.percent.radio'),
                            value: 'percent'
                        },
                        {
                            key: this._translateService.instant('private.training-organism.common.discountType.amount.radio'),
                            value: 'amount'
                        }],
                    change: (controlName, newValue, appFormFieldsDatas) => {
                        const appFormFieldTotalExcl = appFormFieldsDatas.find(field => field.controlName === 'totalExcl');
                        const appFormFieldPrice = appFormFieldsDatas.find(field => ['amount', 'percent', 'price'].indexOf(field.controlName) !== -1);
                        appFormFieldPrice.value = null;
                        appFormFieldTotalExcl.value = registrationFolder.trainingActionInfo.totalExcl;
                        if (newValue === 'none') {
                            appFormFieldPrice.disabled = true;
                            appFormFieldPrice.removed = true;
                            appFormFieldTotalExcl.colSpan = null;
                        } else {
                            appFormFieldTotalExcl.colSpan = 2;
                            appFormFieldPrice.removed = false;
                            appFormFieldPrice.disabled = false;
                            appFormFieldPrice.required = true;
                            appFormFieldPrice.label = 'private.training-organism.common.discountType.' + newValue + '.label';
                            appFormFieldPrice.help = 'private.training-organism.common.discountType.' + newValue + '.placeholder';
                            appFormFieldPrice.suffix = 'private.training-organism.common.discountType.' + newValue + '.unit';
                            if (newValue === 'amount') {
                                const minValidPrice = -registrationFolder.trainingActionInfo.totalIncl;
                                appFormFieldPrice.type = 'price';
                                appFormFieldPrice.controlName = 'amount';
                                appFormFieldPrice.min = minValidPrice;
                                appFormFieldPrice.validators = [Validators.min(minValidPrice)];
                                appFormFieldPrice.validatorsMessages = {
                                    min: this._translateService.instant('private.training-organism.common.discountType.errors.minAmount', {value: minValidPrice}),
                                };
                                if (registrationFolder.type === DataProviders.CPF) {
                                    const maxValidPrice = Number((registrationFolder.trainingActionInfo.totalIncl * 0.15).toFixed(2));
                                    appFormFieldPrice.max = maxValidPrice;
                                    appFormFieldPrice.validators.push(Validators.max(maxValidPrice));
                                    appFormFieldPrice.validatorsMessages.max = this._translateService
                                        .instant('private.training-organism.common.discountType.errors.maxAmount', {value: maxValidPrice});
                                }
                            } else if (newValue === 'percent') {
                                appFormFieldPrice.type = 'percent';
                                appFormFieldPrice.controlName = 'percent';
                                appFormFieldPrice.min = -100;
                                appFormFieldPrice.validators = [Validators.min(-100)];
                                appFormFieldPrice.validatorsMessages = {
                                    min: 'common.errors.discountType.percent',
                                };
                                if (registrationFolder.type === DataProviders.CPF) {
                                    appFormFieldPrice.max = 15;
                                    appFormFieldPrice.validators.push(Validators.max(15));
                                    appFormFieldPrice.validatorsMessages.max = 'common.errors.discountType.percent';
                                }
                            } else if (newValue === 'fixed') {
                                appFormFieldPrice.type = 'price';
                                appFormFieldPrice.controlName = 'price';
                                appFormFieldPrice.min = 0;
                                appFormFieldPrice.validators = [Validators.min(0)];
                                appFormFieldPrice.validatorsMessages = {
                                    min: this._translateService.instant('private.training-organism.common.discountType.errors.min', {value: 0})
                                };
                                if (registrationFolder.type === DataProviders.CPF) {
                                    const validPrice = Number((registrationFolder.trainingActionInfo.totalIncl * 1.15).toFixed(2));
                                    appFormFieldPrice.max = validPrice;
                                    appFormFieldPrice.validators.push(Validators.max(validPrice));
                                    appFormFieldPrice.validatorsMessages.max = this._translateService
                                        .instant('private.training-organism.common.discountType.errors.max', {value: validPrice});
                                }
                            }
                        }
                        return [appFormFieldPrice, appFormFieldTotalExcl];
                    }
                },
                {
                    removed: true,
                    controlName: 'price',
                    disabled: true,
                    label: 'private.training-organism.folders.common.updatePrice',
                    type: 'price',
                    required: true,
                    placeholder: 'private.training-organism.folders.dialog.placeholders.priceChange',
                    colSpan: 2,
                    change: (controlName, newValue, appFormFieldsDatas) => {
                        const appFormFieldPrice = appFormFieldsDatas.find(field => ['amount', 'percent', 'price'].indexOf(field.controlName) !== -1);
                        const appFormFieldTotalExcl = appFormFieldsDatas.find(field => field.controlName === 'totalExcl');
                        appFormFieldPrice.value = newValue;
                        if (this.organism.vat !== null) { // TODO What if no VAT ? looks wrong that we dont copy appFormFieldPrice into appFormFieldTotalExcl
                            if (appFormFieldPrice.controlName === 'price') {
                                this.convertPrice(this.organism.vat, appFormFieldTotalExcl, appFormFieldPrice);
                            } else if (appFormFieldPrice.controlName === 'percent') {
                                const value = appFormFieldTotalExcl.value = Math.round(
                                    registrationFolder.trainingActionInfo.totalIncl * (100 + appFormFieldPrice.value)
                                ) / 100;
                                this.convertPrice(this.organism.vat, appFormFieldTotalExcl, null, value);
                            } else if (appFormFieldPrice.controlName === 'amount') {
                                registrationFolder.trainingActionInfo.totalIncl = registrationFolder.trainingActionInfo.totalIncl + appFormFieldPrice.value;
                                this.convertPrice(this.organism.vat, appFormFieldTotalExcl, null, registrationFolder.trainingActionInfo.totalIncl);
                            }
                        }
                        return [appFormFieldPrice, appFormFieldTotalExcl];
                    }
                },
                {
                    removed: !this.isOwner,
                    controlName: 'totalExcl',
                    value: registrationFolder.trainingActionInfo.totalExcl,
                    disabled: true,
                    required: true,
                    label: 'private.training-organism.folders.common.amount',
                    type: 'price',
                    icon: 'euro_symbol',
                    colSpan: 2
                },
                {
                    removed: !this.isOwner,
                    controlName: 'billingState',
                    value: billingState,
                    disabled: true,
                    required: true,
                    label: 'common.actions.search.billingState',
                    type: 'text',
                    help: registrationFolder.paymentDate ? ('Prévu le ' + moment(registrationFolder.paymentDate).format('DD/MM/YYYY')) : null,
                    icon: this._registrationFolderBillingStateToIconPipe.transform(registrationFolder.billingState),
                    colSpan: 3
                },
                {
                    removed: !this.isOwner,
                    controlName: 'billNumber',
                    value: registrationFolder.billNumber,
                    disabled: true,
                    required: true,
                    label: 'common.actions.search.billNumber',
                    type: 'text',
                    icon: 'pin',
                    colSpan: 3
                },
                {
                    removed: !this.isOwner,
                    controlName: 'tags',
                    label: 'common.tags.label',
                    placeholder: 'common.tags.placeholder',
                    type: 'tags',
                    isCreateAvailable: true,
                },
                {
                    controlName: 'description',
                    removed: !this.isOwner,
                    disabled: registrationFolder.type === DataProviders.CPF && registrationFolder.state !== RegistrationFolderStates.NOT_PROCESSED,
                    placeholder: 'private.training-organism.folders.dialog.placeholders.description',
                    label: 'private.training-organism.folders.dialog.labels.description',
                    type: 'textarea',
                    maxLength: 255,
                },
                {
                    removed: !this.isOwner,
                    controlName: 'notes',
                    label: 'private.training-organism.folders.common.notes.label',
                    placeholder: 'private.training-organism.folders.common.notes.placeholder',
                    type: 'textarea',
                    maxLength: 255,
                    icon: 'text_snippet',
                }
            ];
            this.appFormFieldsData = appFormFieldsData.filter(value => value != null); // Hack: do it in two steps to avoid complex typescript error
            this.panelLoaded();
        });
    }

    refresh(registrationFolder: RegistrationFolder): void {
        this.registrationFolder = registrationFolder;
        this.registrationFolderChange.emit(registrationFolder);
    }

    setLoading(loading: boolean): void {
        this.loading = loading;
    }

    setErrorMessages(error: string[]): void {
        this.errorMessages = error;
    }

    includes(canceledOrRefusedStates: any, state: RegistrationFolderStates): boolean {
        return Object.values(canceledOrRefusedStates).includes(state);
    }

    canDeactivate(): boolean {
        return !this.loading && (this.form?.submitted || !this.formGroup?.dirty);
    }

    checkSessionDates(controlName, newValue, formData, formGroup): any[] {
        const appFormFieldSessionStartDate = formData.find(field => field.controlName === 'sessionStartDate');
        const appFormFieldSessionEndDate = formData.find(field => field.controlName === 'sessionEndDate');
        const appFormFieldType = formData.find(field => field.controlName === 'type');
        const sessionStartDate = controlName === 'sessionStartDate' ? newValue : formGroup.get('registrationFolder.sessionStartDate').value;
        const sessionEndDate = controlName === 'sessionEndDate' ? newValue : formGroup.get('registrationFolder.sessionEndDate').value;
        appFormFieldSessionStartDate.value = sessionStartDate ? moment(new Date(sessionStartDate)).toISOString() :
            appFormFieldType.value === DataProviders.CPF ? moment(this.cpfSessionMinDate).toISOString() :
                business.addWeekDays(moment(new Date()), 1).toDate(); // TODO CHECK
        appFormFieldSessionEndDate.value = sessionEndDate ? moment(new Date(sessionEndDate)).toISOString() : appFormFieldSessionStartDate.value;
        appFormFieldSessionEndDate.min = moment(new Date(appFormFieldSessionStartDate.value));
        return;
    }

    convertPrice(tvaRate: number, appFormFieldTotalExcl: any, appFormFieldPrice?: any, price?: number): any {
        const num = appFormFieldPrice ? appFormFieldPrice.value : price;
        if (tvaRate === 0) {
            appFormFieldTotalExcl.value = num;
        } else if (tvaRate === 20) {
            const convertNumber = num / 1.2;
            appFormFieldTotalExcl.value = Math.round(convertNumber * 100) / 100;
        } else if (tvaRate === 5.5) {
            const convertNumber = num * 100 / 1.055;
            appFormFieldTotalExcl.value = Math.round(convertNumber) / 100;
        }
    }

    checkIndicativeTraining(controlName, newValue, formData, formGroup): any[] {
        const appFormFieldIndicativeDuration = formData.find(field => field.controlName === 'indicativeDuration');
        const appFormFieldHoursCenter = formData.find(field => field.controlName === 'hoursInCenter');
        const appFormFieldHoursCompany = formData.find(field => field.controlName === 'hoursInCompany');
        const indicativeDuration = controlName === 'indicativeDuration' ? newValue : formGroup.get('registrationFolder.indicativeDuration')?.value;
        const hoursInCenter = controlName === 'hoursInCenter' ? newValue : formGroup.get('registrationFolder.hoursInCenter')?.value;
        const hoursInCompany = controlName === 'hoursInCompany' ? newValue : formGroup.get('registrationFolder.hoursInCompany')?.value;
        appFormFieldHoursCenter.value = hoursInCenter;
        appFormFieldIndicativeDuration.value = indicativeDuration;
        appFormFieldHoursCompany.value = hoursInCompany;
        appFormFieldHoursCenter.max = indicativeDuration;
        appFormFieldHoursCenter.validators = [Validators.min(0), Validators.max(indicativeDuration)];
        appFormFieldHoursCompany.max = indicativeDuration;
        appFormFieldHoursCompany.validators = [Validators.min(0), Validators.max(indicativeDuration)];
        appFormFieldIndicativeDuration.min = hoursInCenter + hoursInCompany;
        appFormFieldIndicativeDuration.validators = [Validators.min(hoursInCenter + hoursInCompany)];
        return [appFormFieldIndicativeDuration, appFormFieldHoursCompany, appFormFieldHoursCenter];
    }

    ngAfterViewChecked(): void {
        this.changeDetectorRef.detectChanges();
    }

    ngAfterViewInit(): void {
        this._intersectionObserver = setUpIntersectionObserver('dossierFormation', this.elementIntersection);
        this._intersectionObserver.observe(this._el.nativeElement as HTMLElement);
    }

    ngOnDestroy(): RequiredCallSuper {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
        return super.ngOnDestroy();
    }

    createdActivity(activity: Activity): void {
        this.registrationFolderChange.emit({...this.registrationFolder});
    }

    isFranceTravailFunding(registrationFolder: RegistrationFolder): boolean {
        return registrationFolder?.trainingActionInfo.solicitations?.some(solicitation => solicitation.fundingType === 9);
    }
}
