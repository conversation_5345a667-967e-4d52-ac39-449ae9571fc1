<mat-card class="flex-col mt-3 p-5 pb-0 pt-3 border shadow-none sm" [ngClass]="{'card-loading':cardLoading}">
    <div class="flex items-center mb-2">
        <mat-icon class="mr-3 card-loading-show text-4xl"
                  [color]="includes(errorStates, registrationFolder?.state) ? 'warn' : 'primary'"
                  title="{{ 'private.training-organism.folders.common.state.' + registrationFolder?.state | translate}}">
            {{ registrationFolder?.state | registrationFolderStateToIcon }}
        </mat-icon>
        <div class="truncate">
            <span class="text-xl font-semibold card-loading-show">
                {{ 'private.common.registrationFolder.title' | translate }}
                <img *ngIf="isFranceTravailFunding(registrationFolder)"
                     alt="{{'private.common.registrationFolder.franceTravailFunding' | translate}}"
                     style="display: inline; width: 20px;" class="ml-1" src="assets/icons/pe.svg">
            </span>
            <div class="text-secondary text-md truncate card-loading-show"
                 title="{{ 'private.training-organism.folders.common.state.' + registrationFolder?.state | translate}}">
                {{ 'private.training-organism.folders.common.state.' + registrationFolder?.state | translate }} -
                <span
                    title="{{ 'private.common.registrationFolder.lastUpdate' | translate}}">{{ registrationFolder?.lastUpdate | date: 'dd/MM/yyyy' }}</span>
            </div>
        </div>
        <div class="ml-auto flex -mr-4 card-loading-show">
            <app-attendee-menu-folders *ngIf="registrationFolder?.isAllowActions"
                                       [attendeeLink]="registrationFolder.attendeeLink"
                                       [organism]="organism"
                                       [subscription]="subscription"
                                       [attendee]="registrationFolder.attendee"
                                       [permalink]="registrationFolder.permalink"
                                       [surveyLink]="registrationFolder.surveyLink"
                                       [externalId]="registrationFolder.externalId"
                                       attendeeType="attendee">
            </app-attendee-menu-folders>
            <app-registration-folder-control *ngIf="isOwner"
                                             [folder]="registrationFolder"
                                             (errorMessages)="setErrorMessages($event)"
                                             (loading)="setLoading($event)"
                                             (processedFolder)="refresh($event)"
                                             (initRegistrationFolder)="initForm($event)"
            ></app-registration-folder-control>
            <app-activity-form-menu
                *ngIf="isOwner"
                [entityClass]="EntityClass.REGISTRATION_FOLDER"
                [entityId]="registrationFolder.externalId"
                (createdActivity)="createdActivity($event)">
            </app-activity-form-menu>
            <app-registration-folder-menu [folders]="[registrationFolder]"
                                          (processedFolder)="refresh($event)"
                                          [panelOpenState]="panelOpenState"
                                          (openEvent)="openPanel()"
                                          (closeEvent)="closePanel()"
            ></app-registration-folder-menu>
        </div>
    </div>
    <mat-progress-bar
        class="mt-2 mb-3 card-loading-hidden"
        mode="determinate"
        matTooltipPosition="above"
        *ngIf="registrationFolder.completionRate"
        [matTooltip]="(registrationFolder.history.completionRateLastUpdate ? 'private.training-organism.folders.common.completionRateDoneDate' : 'private.training-organism.folders.common.completionRateDone') |translate: {
            'completionRate':registrationFolder.completionRate,
            'expectedCompletionRate': registrationFolder.completionRate === 100 ? '' : (' / ' + registrationFolder.expectedCompletionRate + ' % attendu'),
            'date' : registrationFolder.history.completionRateLastUpdate | date : 'dd/MM/yyyy à hh:mm' }"
        [color]="expectedCompletionRateColor(registrationFolder)"
        [value]="registrationFolder.completionRate">
    </mat-progress-bar>
    <div class="grid grid-cols-6 gap-2" *ngIf="!panelOpenState || cardLoading">
        <ng-container *ngTemplateOutlet="treomessages">
        </ng-container>
        <app-form-field-static class="col-span-6"
                               icon="folder"
                               [copyValue]="registrationFolder?.dataProviderId ? registrationFolder.dataProviderId : registrationFolder?.externalId"
                               [copy]="true"
                               [type]="registrationFolder?.externalLink ? 'url' : 'text'"
                               [value]="registrationFolder | displayRegistrationFolderExternalId"
                               [href]="registrationFolder?.externalLink">
        </app-form-field-static>
        <app-form-field-static class="font-bold col-span-6"
                               *ngIf="registrationFolder?.trainingActionInfo?.externalLink; else notUrl"
                               [value]="registrationFolder?.trainingActionInfo?.title"
                               icon="title"
                               [href]="registrationFolder?.trainingActionInfo?.externalLink"
                               type="url">
        </app-form-field-static>
        <ng-template #notUrl>
            <app-form-field-static class="font-bold col-span-6" icon="title" type="text"
                                   [value]="registrationFolder?.trainingActionInfo?.title">
            </app-form-field-static>
        </ng-template>
        <app-form-field-static class="font-bold col-span-6"
                               [value]="certificationName"
                               icon="fact_check"
                               [href]="certificationLink"
                               type="url">
        </app-form-field-static>
        <app-form-field-static *ngIf="!isOwner" class="font-bold col-span-6" icon="business" type="text"
                               [value]="registrationFolder?._links?.organism?.name">
        </app-form-field-static>
        <app-form-field-static
            *ngIf="registrationFolder?._links?.inPartnershipWith?.name"
            class="col-span-6"
            icon="cases" type="text"
            [value]="'private.training-organism.folders.common.partnership' | translate : {
                               partner: registrationFolder?._links?.inPartnershipWith?.name
                               }">
        </app-form-field-static>
        <app-form-field-static class="col-span-6" type="text" icon="event"
                               *ngIf="registrationFolder?.trainingActionInfo?.sessionStartDate && registrationFolder?.completionRate"
                               [value]="(registrationFolder?.completionRate === 100 ? 'private.training-organism.folders.common.dateWithCompletionRateWithNoExpectedCompletionRate' :
                                'private.training-organism.folders.common.dateWithCompletionRate') | translate: {
                startDate: registrationFolder?.trainingActionInfo?.sessionStartDate | date : 'dd/MM/yyyy',
                endDate: registrationFolder?.trainingActionInfo?.sessionEndDate | date : 'dd/MM/yyyy',
                completionRate: registrationFolder?.completionRate,
                expectedCompletionRate: registrationFolder?.expectedCompletionRate
            }">
        </app-form-field-static>
        <app-form-field-static class="col-span-6" type="text" icon="event"
                               *ngIf="registrationFolder?.trainingActionInfo?.sessionStartDate && !registrationFolder?.completionRate else noSessionDate"
                               [value]="'private.training-organism.folders.common.date' | translate: {
            startDate: registrationFolder?.trainingActionInfo?.sessionStartDate | date : 'dd/MM/yyyy',
            endDate: registrationFolder?.trainingActionInfo?.sessionEndDate | date : 'dd/MM/yyyy'
        }">
        </app-form-field-static>
        <ng-template #noSessionDate>
            <app-form-field-static *ngIf="!registrationFolder?.trainingActionInfo?.sessionStartDate" class="col-span-6"
                                   type="text" icon="event"
                                   [value]="'private.training-organism.folders.common.noSessionDate' | translate"></app-form-field-static>
        </ng-template>
        <app-form-field-static class="col-span-6 mt-1"
                               *ngIf="registrationFolder?.tags?.length"
                               [value]="registrationFolder?.tags"
                               type="tags"></app-form-field-static>
        <app-form-field-static class="col-span-6 mt-1"
                               icon="text_snippet"
                               *ngIf="registrationFolder?.notes?.length >= 1 && isOwner"
                               [value]="registrationFolder?.notes"
                               type="textarea"></app-form-field-static>
        <button type="button" class="col-span-6 flex justify-center mt-2 -mx-5 pt-2 pb-2 open-panel card-loading-hidden"
                (click)="openPanel()">
            <mat-icon svgIcon="keyboard_arrow_down"></mat-icon>
        </button>
    </div>

    <form #form="ngForm" [formGroup]="formGroup" *ngIf="panelOpenState && !cardLoading"
          class="flex flex-col">
        <ng-container *ngTemplateOutlet="treomessages">
        </ng-container>
        <app-form-fields formGroupName="registrationFolder"
                         class="grid grid-cols-6 gap-2"
                         [entity]="registrationFolder"
                         [appFormFieldsData]="appFormFieldsData"
                         [formGroup]="formGroup">
        </app-form-fields>
        <app-file-list #appFileList
                       *ngIf="isOwner"
                       [isOwner]="true"
                       [showManageFile]="isOwner"
                       [linkManageFile]="'/profil/organisme'"
                       [entity]="registrationFolder"
                       [fileTypes]="organism?.registrationFolderFileTypes"
                       [orderedStates]="orderedRegistrationFolderStates"
                       [entityParentId]="organism.siret"
                       entityApiPath="registrationFolders"
                       entityIdProperty="externalId"
                       titleStateChange="private.training-organism.folders.common.state.">
        </app-file-list>
        <button type="button" class="flex justify-center -mx-5 pt-2 pb-2 close-panel" (click)="closePanel()">
            <mat-icon svgIcon="keyboard_arrow_up"></mat-icon>
        </button>
    </form>

    <div
        class="flex  items-center justify-end border-t pl-5 pr-10 -mx-5 py-3 light:bg-cool-gray-50 dark:bg-cool-gray-700 card-loading-hidden"
        *ngIf="isOwner">
        <div class="flex">
            <mat-error *ngIf="errorMessages.length">
                <ul>
                    <li *ngFor="let errorMessage of errorMessages">{{ errorMessage }}</li>
                </ul>
            </mat-error>
            <app-registration-folder-menu class="-mr-4"
                                          [folders]="[registrationFolder]"
                                          (processedFolder)="refresh($event)"
                                          [withButton]="true"
                                          [panelOpenState]="panelOpenState"
                                          (openEvent)="openPanel()"
                                          (closeEvent)="closePanel()"
                                          (initRegistrationFolder)="initForm($event)"
                                          [formGroup]="formGroup"
                                          (errorMessages)="setErrorMessages($event)"
                                          (loading)="setLoading($event)"></app-registration-folder-menu>
        </div>
    </div>

</mat-card>

<ng-template #treomessages>
    <treo-message *ngIf="isOwner" [type]="includes(errorStates, registrationFolder?.state)  ? 'error' : 'info'"
                  [showIcon]="false" class="col-span-6 my-2 card-loading-show" appearance="outline">
        <app-form-field-static icon="info" type="text"
                               [value]="'private.common.registrationFolder.state.' + registrationFolder?.state | translate : {
                                        completionRate: registrationFolder?.completionRate || ''
                                   }"
        >
        </app-form-field-static>
    </treo-message>
    <treo-message *ngIf="isOwner && validationNotAllowed" [type]="'error'"
                  [showIcon]="false" class="col-span-6 my-2 card-loading-show" appearance="outline">
        <app-form-field-static icon="warning" type="text"
                               [value]="'private.common.registrationFolder.state.notProcessedAlert' | translate : { cpfSessionMinDate : (cpfSessionMinDate | dateZToDayString) }"
        >
        </app-form-field-static>
    </treo-message>
</ng-template>
