import {Component, Inject, Injector, OnInit} from '@angular/core';
import {Validators} from '@angular/forms';
import {MAT_DIALOG_DATA} from '@angular/material/dialog';
import {
    RegistrationFolder,
    RegistrationFolderTerminate,
    TrainingActionInfo
} from '../../../api/models/registration-folder';
import {RegistrationFolderReason} from '../../../api/models/registration-folder-reason';
import {RegistrationFolderReasonService} from '../../../api/services/registration-folder-reason.service';
import {RegistrationFolderDialogComponent} from '../registration-folder-dialog-form-component';

@Component({
    selector: 'app-registration-folder-terminate-dialog',
    templateUrl: './registration-folder-terminate-dialog.component.html',
    styleUrls: ['./registration-folder-terminate-dialog.component.scss']
})
export class RegistrationFolderTerminateDialogComponent extends RegistrationFolderDialogComponent<RegistrationFolderTerminate> implements OnInit {

    reasons: RegistrationFolderReason[];
    reasonNoAbsence = '8';

    get training(): TrainingActionInfo {
        return this._dialogData.folder.trainingActionInfo;
    }

    constructor(
        injector: Injector,
        @Inject(MAT_DIALOG_DATA) private _dialogData: { folder: RegistrationFolder, plural: boolean, folderCount: number, prefixAction: string },
        private _registrationFolderReasonService: RegistrationFolderReasonService
    ) {
        super(injector, _dialogData);
    }

    ngOnInit(): void {
        this.initForm();
        this._registrationFolderReasonService.find('terminated').subscribe((reasons) => {
            this.reasons = reasons;
        });
    }

    submit(): void {
        this.errorMessages = [];
        if (this.formGroup.valid) {
            this.loading = true;
            this.submitted$.next({...this.formGroup.value});
        }
    }

    protected initForm(): void {
        this.formGroup = this._fb.group({
            code: [this.reasonNoAbsence, Validators.required],
            date: [this.getEndDate(), Validators.required],
        });
    }

    private getEndDate(): string {
        return this.training.sessionEndDate;
    }

}
