<form (ngSubmit)="submit()" [formGroup]="formGroup">
    <app-dialog-layout [title]="'private.training-organism.folders.dialog.title.' + action | translate"
                       [actions]="actions" [disabled]="loading" [errorMessages]="errorMessages" (dialogClose)="close()"
                       [feedbackMessage]="feedbackMessage">

        <treo-message type="info" [showIcon]="false" class="mb-5" appearance="outline">
            <div class="flex items-center py-2">
                <mat-icon svgIcon="info"></mat-icon>
                <div class="ml-2" [innerHTML]="'private.training-organism.folders.dialog.content.' + action | translate"></div>
            </div>
        </treo-message>

        <mat-form-field class="mt-2 pb-2 input-line">
            <mat-label>{{ "private.training-organism.folders.dialog.labels.billNumber" | translate}}</mat-label>

            <input type="text" required matInput formControlName="billNumber"
                   [placeholder]="'private.training-organism.folders.dialog.placeholders.billNumber' | translate">
            <mat-error class="flex-auto gt-xs:pr-3">
                <ng-container *ngIf="formGroup.get('billNumber').errors?.required">
                    {{'common.errors.required' | translate }}
                </ng-container>
            </mat-error>
        </mat-form-field>

        <ng-template #actions>
            <button type="submit" mat-flat-button color="primary" [disabled]="loading">
                <mat-progress-spinner *ngIf="loading; else submitLabel" [diameter]="24" mode="indeterminate">
                </mat-progress-spinner>
                <ng-template #submitLabel>
                    {{ 'common.actions.submit' | translate }}
                </ng-template>
            </button>
        </ng-template>

    </app-dialog-layout>
</form>

