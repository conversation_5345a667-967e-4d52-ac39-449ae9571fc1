import {Component, Inject, Injector, OnInit} from '@angular/core';
import {MAT_DIALOG_DATA} from '@angular/material/dialog';
import {RegistrationFolderDialogComponent} from '../registration-folder-dialog-form-component';
import {RegistrationFolderToBill} from '../../../api/models/registration-folder';
import {Validators} from '@angular/forms';

@Component({
    selector: 'app-registration-folder-to-bill-dialog',
    templateUrl: './registration-folder-to-bill-dialog.component.html',
    styleUrls: ['./registration-folder-to-bill-dialog.component.scss']
})
export class RegistrationFolderToBillDialogComponent extends RegistrationFolderDialogComponent<RegistrationFolderToBill> implements OnInit {

    constructor(
        injector: Injector,
        @Inject(MAT_DIALOG_DATA) private _dialogData: { plural: boolean, folderCount: number, prefixAction: string },
    ) {
        super(injector, _dialogData);
    }

    protected initForm(): void {
        this.formGroup = this._fb.group({
            billNumber: ['', Validators.required],
        });
    }

    submit(): void {
        this.errorMessages = [];
        if (this.formGroup.valid) {
            this.loading = true;
            this.submitted$.next(this.formGroup.value);
        }
    }

}
