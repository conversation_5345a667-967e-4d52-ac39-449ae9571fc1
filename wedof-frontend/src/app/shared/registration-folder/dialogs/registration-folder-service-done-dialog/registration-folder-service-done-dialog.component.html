<form (ngSubmit)="submit()" [formGroup]="formGroup">
    <app-dialog-layout
            [ngClass]="{'card-loading':cardLoading}"
            [title]="'private.training-organism.folders.dialog.title.' + action | translate"
            [actions]="actions"
            [disabled]="loading"
            [errorMessages]="errorMessages"
            (dialogClose)="close()"
            [feedbackMessage]="feedbackMessage">


        <app-form-fields *ngIf="!cardLoading" class="grid grid-cols-6 gap-2"
                         formGroupName="registrationFolderServiceDone"
                         [entity]="folder"
                         [appFormFieldsData]="appFormFieldsData"
                         [formGroup]="formGroup"
        ></app-form-fields>

        <ng-template #actions>
            <button type="submit" mat-flat-button color="primary" [disabled]="loading || !formGroup?.valid" (click)="submit()">
                <mat-progress-spinner *ngIf="loading; else submitLabel" [diameter]="24" mode="indeterminate">
                </mat-progress-spinner>
                <ng-template #submitLabel>
                    {{ 'common.actions.submit' | translate }}
                </ng-template>
            </button>
        </ng-template>

    </app-dialog-layout>
</form>
