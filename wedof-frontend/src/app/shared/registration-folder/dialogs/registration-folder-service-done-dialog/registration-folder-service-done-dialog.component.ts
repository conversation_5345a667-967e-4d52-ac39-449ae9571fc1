import {Component, Inject, Injector, OnInit} from '@angular/core';
import {Validators} from '@angular/forms';
import {MAT_DIALOG_DATA} from '@angular/material/dialog';
import {
    RegistrationFolder,
    RegistrationFolderDialogServiceDone,
    TrainingActionInfo
} from '../../../api/models/registration-folder';
import {RegistrationFolderDialogComponent} from '../registration-folder-dialog-form-component';
import {RegistrationFolderReasonService} from '../../../api/services/registration-folder-reason.service';
import {AppFormFieldData} from '../../../material/app-form-field/app-form-field.component';

@Component({
    selector: 'app-registration-folder-service-done-dialog',
    templateUrl: './registration-folder-service-done-dialog.component.html',
    styleUrls: ['./registration-folder-service-done-dialog.component.scss']
})
export class RegistrationFolderServiceDoneDialogComponent extends RegistrationFolderDialogComponent<RegistrationFolderDialogServiceDone> implements OnInit {

    folder: RegistrationFolder;
    reasonsFormatted: { key: string; value: string; }[];
    appFormFieldsData: AppFormFieldData[] = [];
    cardLoading = true;
    reasonNoAbsence = '8';

    get training(): TrainingActionInfo {
        return this._dialogData.folder.trainingActionInfo;
    }

    constructor(
        injector: Injector,
        @Inject(MAT_DIALOG_DATA) private _dialogData: {
            folder: RegistrationFolder,
            plural: boolean,
            folderCount: number,
            prefixAction: string,
            skipToServiceDone: boolean,
        },
        private _registrationFolderReasonService: RegistrationFolderReasonService
    ) {
        super(injector, _dialogData);
        this.folder = this._dialogData.folder;
    }

    ngOnInit(): void {
        this._registrationFolderReasonService.find('terminated').subscribe((reasons) => {
            this.reasonsFormatted = reasons.map((reason) => {
                return {key: reason.label, value: reason.code};
            });
            this.initForm();
        });
    }

    submit(): void {
        this.errorMessages = [];
        if (this.formGroup.valid) {
            this.loading = true;
            const registrationFolderServiceDoneBody = this.formGroup.getRawValue().registrationFolderServiceDone;
            const quitReason = registrationFolderServiceDoneBody.code ?? this.training['quitReason'];
            const data: RegistrationFolderDialogServiceDone = {
                absenceDuration: registrationFolderServiceDoneBody.absenceDuration,
                forceMajeureAbsence: quitReason === '18',
            };
            if (!this._dialogData.skipToServiceDone) {
                data.date = registrationFolderServiceDoneBody.date;
                data.code = registrationFolderServiceDoneBody.code;
            }
            this.submitted$.next(data);
        }
    }

    protected initForm(): void {
        this.formGroup = this._fb.group({
            registrationFolderServiceDone: this._fb.group({})
        });
        const appFormFieldsData: AppFormFieldData[] = [
            {
                controlName: 'code',
                type: 'select',
                removed: this._dialogData.skipToServiceDone,
                label: 'private.training-organism.folders.dialog.labels.code',
                required: true,
                placeholder: 'private.training-organism.folders.dialog.placeholders.code',
                choices: this.reasonsFormatted,
                value: this.training['quitReason'] ?? null,
                change: (controlName, newValue, formData) => {
                    const appFormFieldAbsenceDuration = formData.find(field => field.controlName === 'absenceDuration');
                    const appFormFieldDate = formData.find(field => field.controlName === 'date');
                    if (newValue === this.reasonNoAbsence) {
                        appFormFieldAbsenceDuration.value = 0;
                        appFormFieldAbsenceDuration.removed = true;
                        appFormFieldDate.colSpan = 6;
                    } else {
                        appFormFieldAbsenceDuration.min = 1;
                        appFormFieldAbsenceDuration.validators = [Validators.min(1)];
                        appFormFieldAbsenceDuration.validatorsMessages = {min: this._translateService.instant('common.errors.min', {value: 1})};
                        appFormFieldAbsenceDuration.value = null;
                        appFormFieldAbsenceDuration.removed = false;
                        appFormFieldAbsenceDuration.required = true;
                        appFormFieldAbsenceDuration.colSpan = 3;
                        appFormFieldDate.colSpan = 3;
                    }
                    return [appFormFieldDate, appFormFieldAbsenceDuration];
                },
            },
            {
                controlName: 'date',
                type: 'date',
                removed: this._dialogData.skipToServiceDone,
                label: 'private.training-organism.folders.dialog.labels.date',
                placeholder: 'private.training-organism.folders.dialog.placeholders.date',
                required: true,
                validatorsMessages: {
                    min: 'common.errors.date',
                    max: 'common.errors.date'
                },
                max: this.training.sessionEndDate,
                min: this.training.sessionStartDate,
                value: this.training.sessionEndDate,
            },
            {
                controlName: 'absenceDuration',
                type: 'number',
                label: 'private.training-organism.folders.dialog.labels.absenceDuration',
                placeholder: 'private.training-organism.folders.dialog.placeholders.absenceDuration',
                removed: true
            }
        ];
        this.appFormFieldsData = appFormFieldsData.filter(value => value != null);
        this.cardLoading = false;
    }
}
