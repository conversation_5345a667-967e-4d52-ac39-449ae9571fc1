import {Component, Inject, Injector, OnInit} from '@angular/core';
import {Validators} from '@angular/forms';
import {MAT_DIALOG_DATA} from '@angular/material/dialog';
import {
    RegistrationFolder,
    RegistrationFolderInTraining,
    TrainingActionInfo
} from '../../../api/models/registration-folder';
import {RegistrationFolderDialogComponent} from '../registration-folder-dialog-form-component';

@Component({
    selector: 'app-registration-folder-in-training-dialog',
    templateUrl: './registration-folder-in-training-dialog.component.html',
    styleUrls: ['./registration-folder-in-training-dialog.component.scss']
})
export class RegistrationFolderInTrainingDialogComponent extends RegistrationFolderDialogComponent<RegistrationFolderInTraining> implements OnInit {

    get training(): TrainingActionInfo {
        return this._dialogData.folder.trainingActionInfo;
    }

    constructor(
        injector: Injector,
        @Inject(MAT_DIALOG_DATA) private _dialogData: { folder: RegistrationFolder, plural: boolean, folderCount: number, prefixAction: string  },
    ) {
        super(injector, _dialogData);
    }

    submit(): void {
        this.errorMessages = [];
        if (this.formGroup.valid) {
            this.loading = true;
            this.submitted$.next(this.formGroup.value);
        }
    }

    protected initForm(): void {
        this.formGroup = this._fb.group({
            date: [this.getInitialDate(), Validators.required],
        });
    }

    private getInitialDate(): string {
        const today = new Date().toISOString();
        if (today < this.training.sessionStartDate) {
            return this.training.sessionStartDate;
        }
        if (today > this.training.sessionEndDate) {
            return this.training.sessionStartDate;
        }
        return today;
    }

}
