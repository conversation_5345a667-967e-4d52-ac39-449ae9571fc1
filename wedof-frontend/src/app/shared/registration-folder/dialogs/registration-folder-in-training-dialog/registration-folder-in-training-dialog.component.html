<form (ngSubmit)="submit()" [formGroup]="formGroup">
    <app-dialog-layout [title]="'private.training-organism.folders.dialog.title.' + action | translate"
        [actions]="actions" [disabled]="loading" [errorMessages]="errorMessages" (dialogClose)="close()" [feedbackMessage]="feedbackMessage">

        <mat-form-field class="pb-2 input-line">
            <mat-label>{{ "private.training-organism.folders.dialog.labels.date" | translate}}</mat-label>
            <input id="date" formControlName="date" matInput [matDatepicker]="picker"
                [placeholder]="'private.training-organism.folders.dialog.placeholders.date' | translate"
                [min]="training.sessionStartDate" [max]="training.sessionEndDate" required>
            <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
            <mat-error>
                <ng-container *ngIf="formGroup.get('date').errors?.matDatepickerMin">
                    {{'private.training-organism.folders.dialog.errors.minDate' | translate }}
                </ng-container>
                <ng-container *ngIf="formGroup.get('date').errors?.matDatepickerMax">
                    {{'private.training-organism.folders.dialog.errors.maxDate' | translate }}
                </ng-container>
                <ng-container *ngIf="formGroup.get('date').errors?.required">
                    {{'common.errors.required' | translate }}
                </ng-container>
            </mat-error>
        </mat-form-field>

        <ng-template #actions>
            <button type="submit" mat-flat-button color="primary" [disabled]="loading">
                <mat-progress-spinner *ngIf="loading; else submitLabel" [diameter]="24" mode="indeterminate">
                </mat-progress-spinner>
                <ng-template #submitLabel>
                    {{ 'common.actions.submit' | translate }}
                </ng-template>
            </button>
        </ng-template>

    </app-dialog-layout>
</form>
