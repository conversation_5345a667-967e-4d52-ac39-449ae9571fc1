<form (ngSubmit)="submit()" [formGroup]="formGroup">
    <app-dialog-layout [title]="'private.training-organism.folders.dialog.title.' + action | translate"
                       [actions]="actions"
                       [cancelText]="'common.actions.no'"
                       [disabled]="loading"
                       [errorMessages]="errorMessages"
                       (dialogClose)="close()">

        <p class="pb-4">
            {{ "private.training-organism.folders.dialog.content." + action | translate }}
        </p>

        <app-form-fields class="grid grid-cols-6 gap-2"
                         formGroupName="registrationFolder"
                         [entity]="registrationFolder"
                         [appFormFieldsData]="appFormFieldsData"
                         [formGroup]="formGroup">
        </app-form-fields>

        <div *ngIf="errorMessages?.length" class="flex items-center">
            <treo-message class="flex-auto" appearance="outline" [showIcon]="false" type="error">
                <ul>
                    <li *ngFor="let errorMessage of errorMessages">
                        {{ errorMessage }}
                    </li>
                </ul>
            </treo-message>
        </div>

        <ng-template #actions>
            <button type="submit" mat-flat-button color="primary" (click)="submit()" [disabled]="loading || !formGroup.valid">
                <mat-progress-spinner *ngIf="loading; else submitLabel" [diameter]="24" mode="indeterminate">
                </mat-progress-spinner>
                <ng-template #submitLabel>
                    {{ 'common.actions.yes' | translate }}
                </ng-template>
            </button>
        </ng-template>

    </app-dialog-layout>
</form>
