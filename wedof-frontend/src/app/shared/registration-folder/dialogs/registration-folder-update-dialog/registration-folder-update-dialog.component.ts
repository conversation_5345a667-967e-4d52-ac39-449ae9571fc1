import {Component, Inject, Injector} from '@angular/core';
import {RegistrationFolderDialogComponent} from '../registration-folder-dialog-form-component';
import {
    RegistrationFolder,
    RegistrationFolderUpdate,
    RegistrationFolderUpdateBody
} from '../../../api/models/registration-folder';
import {MAT_DIALOG_DATA} from '@angular/material/dialog';
import {AppFormFieldData} from '../../../material/app-form-field/app-form-field.component';
import {cloneDeep} from 'lodash-es';

@Component({
    selector: 'app-registration-folder-update-dialog',
    templateUrl: './registration-folder-update-dialog.component.html',
    styleUrls: ['./registration-folder-update-dialog.component.scss']
})
export class RegistrationFolderUpdateDialogComponent extends RegistrationFolderDialogComponent<RegistrationFolderUpdate> {
    appFormFieldsData: AppFormFieldData[];
    registrationFolder: RegistrationFolderUpdateBody;

    constructor(
        injector: Injector,
        @Inject(MAT_DIALOG_DATA) private _dialogData: {
            folder: RegistrationFolder,
            plural: boolean,
            folderCount: number,
            prefixAction: string
        }
    ) {
        super(injector, _dialogData);
    }

    protected initForm(): void {
        this.registrationFolder = cloneDeep(this._dialogData.folder);
        this.formGroup = this._fb.group({
            registrationFolder: this._fb.group({})
        });

        this.appFormFieldsData = [
            {
                controlName: 'addedTags',
                label: 'common.tags.added.label',
                placeholder: 'common.tags.added.placeholder',
                type: 'tags',
                value: [],
                isCreateAvailable: true
            },
            {
                controlName: 'removedTags',
                label: 'common.tags.removed.label',
                placeholder: 'common.tags.removed.placeholder',
                type: 'tags',
                value: [],
                isCreateAvailable: true
            }
        ];
    }

    close(): void {
        this._dialogRef.close();
    }

    submit(): void {
        this.errorMessages = [];
        if (this.formGroup.valid) {
            this.loading = true;
            const data: RegistrationFolderUpdate = {
                addedTags: this.formGroup.value.registrationFolder.addedTags,
                removedTags: this.formGroup.value.registrationFolder.removedTags
            };
            this.submitted$.next(data);
        }
    }
}
