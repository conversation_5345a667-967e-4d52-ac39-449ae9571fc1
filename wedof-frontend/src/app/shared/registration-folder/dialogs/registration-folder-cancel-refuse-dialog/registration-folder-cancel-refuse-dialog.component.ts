import {Component, Inject, Injector, OnInit} from '@angular/core';
import {Validators} from '@angular/forms';
import {MAT_DIALOG_DATA} from '@angular/material/dialog';
import {RegistrationFolderCancelRefuse} from '../../../api/models/registration-folder';
import {RegistrationFolderReason} from '../../../api/models/registration-folder-reason';
import {RegistrationFolderReasonService} from '../../../api/services/registration-folder-reason.service';
import {RegistrationFolderDialogComponent} from '../registration-folder-dialog-form-component';

@Component({
    selector: 'app-registration-folder-cancel-refuse-dialog',
    templateUrl: './registration-folder-cancel-refuse-dialog.component.html',
    styleUrls: ['./registration-folder-cancel-refuse-dialog.component.scss']
})
export class RegistrationFolderCancelRefuseDialogComponent extends RegistrationFolderDialogComponent<RegistrationFolderCancelRefuse> implements OnInit {

    reasons: RegistrationFolderReason[];
    attendeeDidNotCome: boolean;
    codeReasonAttendeeDidNotCome = '4';

    constructor(
        injector: Injector,
        @Inject(MAT_DIALOG_DATA) private _dialogData: { type: 'canceled' | 'refused', plural: boolean, folderCount: number, prefixAction: string, attendeeDidNotCome: boolean  },
        private _registrationFolderReasonService: RegistrationFolderReasonService
    ) {
        super(injector, _dialogData);
    }

    ngOnInit(): void {
        this.attendeeDidNotCome = this._dialogData.attendeeDidNotCome;
        this.initForm();
        this._registrationFolderReasonService.find(this._dialogData.type).subscribe((reasons) => {
            this.reasons = reasons;
        });
    }

    submit(): void {
        this.errorMessages = [];
        if (this.formGroup.valid) {
            this.loading = true;
            const formValue: RegistrationFolderCancelRefuse = this.formGroup.value;
            if (!formValue.description) {
                delete formValue.description;
            }
            this.submitted$.next(formValue);
        }
    }

    protected initForm(): void {
        this.formGroup = this._fb.group({
            code: [this.attendeeDidNotCome ? this.codeReasonAttendeeDidNotCome : null, Validators.required],
            description: [''],
        });
    }

}
