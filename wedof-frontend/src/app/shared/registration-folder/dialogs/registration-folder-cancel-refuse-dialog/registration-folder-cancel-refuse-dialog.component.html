<form (ngSubmit)="submit()" [formGroup]="formGroup">
    <app-dialog-layout [title]="'private.training-organism.folders.dialog.title.' + action | translate"
        [actions]="actions" [disabled]="loading" [errorMessages]="errorMessages" (dialogClose)="close()" [feedbackMessage]="feedbackMessage">

        <div *ngIf="attendeeDidNotCome, else attendeeDidCome">
            <div class="mat-form-field pb-2 input-line">
                <div>{{'private.training-organism.folders.dialog.labels.attendeeDidNotCome' | translate}}</div>
            </div>
        </div>
        <ng-template #attendeeDidCome>
            <mat-form-field class="pb-2 input-line" >
                <mat-label>{{ "private.training-organism.folders.dialog.labels.code" | translate}}</mat-label>
                <mat-select formControlName="code" required
                            [placeholder]="'private.training-organism.folders.dialog.placeholders.code' | translate">
                    <mat-option *ngFor="let reason of reasons" [value]="reason.code">
                        {{reason.label}}
                    </mat-option>
                </mat-select>
                <mat-error class="flex-auto gt-xs:pr-3">
                    {{'common.errors.required' | translate }}
                </mat-error>
            </mat-form-field>

            <mat-form-field class="pb-2 input-line">
                <mat-label>{{ "private.training-organism.folders.dialog.labels.description" | translate}}</mat-label>
                <textarea id="description" matInput formControlName="description"
                          [placeholder]="'private.training-organism.folders.dialog.placeholders.description' | translate"></textarea>
            </mat-form-field>
        </ng-template>

        <ng-template #actions>
            <button type="submit" mat-flat-button color="primary" [disabled]="loading">
                <mat-progress-spinner *ngIf="loading; else submitLabel" [diameter]="24" mode="indeterminate">
                </mat-progress-spinner>
                <ng-template #submitLabel>
                    {{ 'common.actions.submit' | translate }}
                </ng-template>
            </button>
        </ng-template>

    </app-dialog-layout>
</form>
