import {Component, Inject, Injector} from '@angular/core';
import {MAT_DIALOG_DATA} from '@angular/material/dialog';
import {RegistrationFolderDialogComponent} from '../registration-folder-dialog-form-component';

@Component({
    selector: 'app-registration-folder-validate-dialog',
    templateUrl: './registration-folder-validate-dialog.component.html',
    styleUrls: ['./registration-folder-validate-dialog.component.scss']
})
export class RegistrationFolderValidateDialogComponent extends RegistrationFolderDialogComponent<void> {


    constructor(
        injector: Injector,
        @Inject(MAT_DIALOG_DATA) private _dialogData: { plural: boolean, folderCount: number, prefixAction: string  },
    ) {
        super(injector, _dialogData);
    }

}
