<app-dialog-layout [title]="'private.training-organism.folders.dialog.title.' + action | translate" [actions]="actions"
    [cancelText]="'common.actions.no'" [disabled]="loading" [errorMessages]="errorMessages" (dialogClose)="close()" [feedbackMessage]="feedbackMessage">

    <p>
        {{ "private.training-organism.folders.dialog.content." + action | translate }}
    </p>

    <ng-template #actions>
        <button type="submit" mat-flat-button color="primary" (click)="submit()" [disabled]="loading">
            <mat-progress-spinner *ngIf="loading; else submitLabel" [diameter]="24" mode="indeterminate">
            </mat-progress-spinner>
            <ng-template #submitLabel>
                {{ 'common.actions.yes' | translate }}
            </ng-template>
        </button>
    </ng-template>

</app-dialog-layout>
