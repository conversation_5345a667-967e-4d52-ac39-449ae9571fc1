import {Component, Inject, Injector} from '@angular/core';
import {MAT_DIALOG_DATA} from '@angular/material/dialog';
import {RegistrationFolderDialogComponent} from '../registration-folder-dialog-form-component';

@Component({
    selector: 'app-registration-folder-paid-dialog',
    templateUrl: './registration-folder-paid-dialog.component.html',
    styleUrls: ['./registration-folder-paid-dialog.component.scss']
})
export class RegistrationFolderPaidDialogComponent extends RegistrationFolderDialogComponent<void> {

    constructor(
        injector: Injector,
        @Inject(MAT_DIALOG_DATA) private _dialogData: { plural: boolean, folderCount: number, prefixAction: string },
    ) {
        super(injector, _dialogData);
    }

}
