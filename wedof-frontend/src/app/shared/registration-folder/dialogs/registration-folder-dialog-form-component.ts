import {Directive, Injector, OnInit} from '@angular/core';
import {AbstractDialogFormComponent} from '../../material/form/abstract-dialog-form.component';
import {RegistrationFolder} from '../../api/models/registration-folder';
import {TranslateService} from '@ngx-translate/core';

@Directive()
// tslint:disable-next-line: directive-class-suffix
export abstract class RegistrationFolderDialogComponent<T> extends AbstractDialogFormComponent<T> implements OnInit {

    folderDialogCount: number;
    processedFolderCount: number;
    private dialogData: { plural: boolean, folderCount: number, prefixAction: string };
    protected _translateService: TranslateService;

    get action(): string {
        let prefix = '';
        if (this.dialogData.plural) {
            prefix = 'plural.';
        }
        return prefix + this.dialogData.prefixAction;
    }

    get actionFeedback(): string {
        let prefix = '';
        if (this.dialogData.plural) {
            prefix = 'plural.';
        }
        return prefix + 'feedback';
    }

    protected constructor(
        injector: Injector,
        _dialogData: { plural: boolean, folderCount: number, prefixAction: string},
    ) {
        super(injector);
        this.dialogData = _dialogData;
        this.folderDialogCount = this.dialogData.folderCount;
        this.processedFolderCount = 1;
        this._translateService = injector.get(TranslateService);
    }

    lastProcessedData(registrationFolder: RegistrationFolder): void {
        if (this.processedFolderCount < this.folderDialogCount) {
            this.processedFolderCount++;
        }
        this.feedbackMessage = this._translateService.instant(`private.common.certificationFolder.${this.actionFeedback}`,
            {processedFolderCount: this.processedFolderCount, folderDialogCount: this.folderDialogCount});
    }

    submit(): void {
        this.errorMessages = [];
        this.loading = true;
        this.submitted$.next();
    }

    ngOnInit(): void {
        this.initForm();
        this.feedbackMessage = this._translateService.instant(`private.common.certificationFolder.${this.actionFeedback}`,
            {processedFolderCount: this.processedFolderCount, folderDialogCount: this.folderDialogCount});
    }

    protected initForm(model?: T): void {}
}
