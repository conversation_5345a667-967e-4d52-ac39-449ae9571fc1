import {Component, <PERSON><PERSON><PERSON>roy, OnInit} from '@angular/core';
import {Connection, ConnectionState, DataProviderConfig, DataProviders} from '../../api/models/connection';
import {MatDialog} from '@angular/material/dialog';
import {combineLatest, Observable, Subject} from 'rxjs';
import {takeUntil} from 'rxjs/operators';
import {Organism} from '../../api/models/organism';
import {Select} from '@ngxs/store';
import {ConnectionsState} from '../../api/state/connections.state';
import {OrganismState} from '../../api/state/organism.state';


@Component({
    selector: 'app-registration-folder-connection-check',
    templateUrl: './registration-folder-connection-check.component.html',
    styleUrls: ['./registration-folder-connection-check.component.scss']
})
export class RegistrationFolderConnectionCheckComponent implements OnInit, OnDestroy {

    @Select(ConnectionsState.connections) connections$: Observable<Connection[]>;
    @Select(OrganismState.organism) organism$: Observable<Organism>;

    initializingDataProviders: string[] = [];
    currentOrganism: Organism;

    private _unsubscribeAll: Subject<void> = new Subject();

    constructor(
        private _dialog: MatDialog
    ) {
    }

    ngOnInit(): void {
        combineLatest([
            this.connections$,
            this.organism$
        ]).pipe(takeUntil(this._unsubscribeAll)).subscribe(([connections, organism]) => {
            this.initializingDataProviders = connections.filter(
                connection => {
                    const config = DataProviderConfig[connection.dataProvider];
                    return config && config.manageRegistrationFolders
                        && connection.existAtDataProvider
                        && connection.state === ConnectionState.ACTIVE
                        && !connection.isInitialized;
                })
                .map(connection => DataProviderConfig[connection.dataProvider].name);
            this.currentOrganism = organism;
        });
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }
}
