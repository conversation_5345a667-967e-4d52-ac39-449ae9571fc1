<ng-container *ngIf="withButton else onlyMenu">
    <div class="flex align-center" [ngClass]="{'card-loading':isLoading}">
        <ng-container>
            <div *ngIf="!formGroup?.dirty || !panelOpenState else update">
                <button *ngIf="getSingleAction()" type="button" mat-flat-button
                        [disabled]="getSingleAction().disabled"
                        [color]="getSingleAction().color"
                        (click)="$event.stopPropagation(); getSingleAction().method(folders)"
                        [matTooltip]="getSingleAction().tooltip"
                        class="card-loading-show button-actions flex justify-center items-center">
                    <span>
                        {{ 'private.training-organism.folders.actions2.' + getSingleAction().state  | translate: {isUpdatable: updateAndStateActionText()} }}
                    </span>
                    <mat-icon *ngIf="getSingleAction().tooltip" class="mr-0 ml-3" [ngClass]="{'opacity-25' : getSingleAction().disabled}">error</mat-icon>
                </button>
                <div *ngIf="!getSingleAction()" class="card-loading-show text-disabled mt-2">
                    {{ 'common.actions.noAction' | translate }}
                </div>
            </div>
            <ng-template #update>
                <button type="button"
                        mat-flat-button
                        [class]="getSingleAction() ? 'button-actions flex justify-center items-center' : 'button-actionsOnly flex justify-center items-center' "
                        (click)="$event.stopPropagation(); submit(folders[0], $event)"
                        [disabled]="isLoadingProcessedRegistrationFolder || formGroup?.invalid "
                        color="primary">
                    <mat-progress-spinner class="mr-4"
                                          *ngIf="isLoadingProcessedRegistrationFolder" [diameter]="24"
                                          mode="indeterminate"></mat-progress-spinner>
                    <ng-container *ngIf="!isLoadingProcessedRegistrationFolder">
                        {{ 'common.actions.update' | translate }}
                    </ng-container>
                </button>
            </ng-template>
        </ng-container>
        <ng-container *ngIf="actionsAvailable.length > 0">
            <button
                class="button-arrow flex justify-center items-center"
                [ngClass]="updateAndStateAction() ? 'button-arrow-primary' : 'button-arrow-' + getSingleAction().color"
                [color]="updateAndStateAction() ? 'primary' : getSingleAction().color"
                (click)="$event.stopPropagation()"
                mat-flat-button
                [disabled]="updateAndStateAction() && (isLoadingProcessedRegistrationFolder || formGroup?.invalid)"
                [matMenuTriggerFor]="actionsMenuUpdateState"
                title="Actions" type="button">
                <mat-icon class="icon" svgIcon="arrow_drop_down"></mat-icon>
            </button>
            <mat-menu #actionsMenuUpdateState="matMenu" class="large-menu">
                <ng-template matMenuContent>
                    <div *ngFor="let action of actionsAvailable" [matTooltip]="action.tooltip">
                        <button type="button"
                                mat-menu-item
                                [disabled]="action.disabled"
                                (click)="$event.stopPropagation(); updateAndStateAction() ? submit(folders[0], $event, action.state) : action.method(folders)">
                            <mat-icon [color]="action.iconColor">{{ action.icon }}</mat-icon>
                            <span>{{ 'private.training-organism.folders.actions2.' + action.state  | translate: {isUpdatable: updateAndStateActionText()} }}
                            </span>
                            <mat-icon *ngIf="action.tooltip" class="mr-0 ml-3" [ngClass]="{'opacity-25' : action.disabled}">error</mat-icon>
                        </button>
                    </div>
                </ng-template>
            </mat-menu>
        </ng-container>
    </div>
</ng-container>

<ng-template #onlyMenu>
    <button type="button" mat-icon-button
            [disabled]="!folders[0]"
            (click)="$event.stopPropagation(); retrievePermissions()"
            title="Actions">
        <mat-icon svgIcon="more_vert" *ngIf="!isLoading"></mat-icon>
        <mat-progress-spinner *ngIf="isLoading"
                              [diameter]="24"
                              [mode]="'indeterminate'"></mat-progress-spinner>
    </button>
    <button type="button"
            #menuTrigger="matMenuTrigger"
            [matMenuTriggerFor]="actionsMenu">
    </button>
    <mat-menu #actionsMenu="matMenu" class="large-menu">
        <ng-template matMenuContent>
            <button type="button" mat-menu-item disabled="disabled"
                    *ngIf="(actionsAvailable.length === 0); else actions">
                <span>{{ 'common.actions.noAction' | translate }}</span>
            </button>
            <button type="button" mat-menu-item (click)="openPanel()" *ngIf="hasOpenEvent && !panelOpenState">
                <mat-icon svgIcon="keyboard_arrow_down"></mat-icon>
                <span>{{ 'private.common.menu.open' | translate }}</span>
            </button>
            <button type="button" mat-menu-item (click)="closePanel()" *ngIf="hasOpenEvent && panelOpenState">
                <mat-icon svgIcon="keyboard_arrow_up"></mat-icon>
                <span>{{ 'private.common.menu.close' | translate }}</span>
            </button>
            <ng-template #actions>
                <div *ngFor="let action of actionsAvailable" [matTooltip]="action.tooltip">
                    <button [disabled]="action.disabled"
                            (click)="$event.stopPropagation(); action.method(folders)"
                            type="button"
                            mat-menu-item>
                        <mat-icon [color]="action?.iconColor">{{ action?.icon }}</mat-icon>
                        <span>{{ 'private.training-organism.folders.actions2.' + action.state  | translate: {isUpdatable: updateAndStateActionText()} }}
                        </span>
                        <mat-icon *ngIf="action.tooltip" class="mr-0 ml-3" [ngClass]="{'opacity-25' : action.disabled}">error</mat-icon>
                    </button>
                </div>
            </ng-template>
        </ng-template>
    </mat-menu>
</ng-template>
