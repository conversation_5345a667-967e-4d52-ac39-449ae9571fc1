import {Component, EventEmitter, Input, On<PERSON><PERSON><PERSON>, OnD<PERSON>roy, OnInit, Output, ViewChild} from '@angular/core';
import {
    RegistrationFolder,
    RegistrationFolderActions,
    RegistrationFolderActionsAndStates,
    RegistrationFolderCancelRefuse,
    RegistrationFolderConfig,
    RegistrationFolderDialogServiceDone,
    RegistrationFolderInTraining,
    RegistrationFolderStates,
    RegistrationFolderTerminate,
    RegistrationFolderToBill,
    RegistrationFolderUpdate,
    TrainingActionInfo
} from '../../api/models/registration-folder';
import {RegistrationFolderCancelRefuseDialogComponent} from '../dialogs/registration-folder-cancel-refuse-dialog/registration-folder-cancel-refuse-dialog.component';
import {RegistrationFolderInTrainingDialogComponent} from '../dialogs/registration-folder-in-training-dialog/registration-folder-in-training-dialog.component';
import {RegistrationFolderServiceDoneDialogComponent} from '../dialogs/registration-folder-service-done-dialog/registration-folder-service-done-dialog.component';
import {RegistrationFolderTerminateDialogComponent} from '../dialogs/registration-folder-terminate-dialog/registration-folder-terminate-dialog.component';
import {RegistrationFolderService} from '../../api/services/registration-folder.service';
import {combineLatest, concat, Observable, of, Subject} from 'rxjs';
import {catchError, every, finalize, map, switchMap, takeUntil, tap} from 'rxjs/operators';
import {ComponentType} from '@angular/cdk/portal';
import {AbstractDialogFormComponent} from '../../material/form/abstract-dialog-form.component';
import {HttpErrorResponse} from '@angular/common/http';
import {MatDialog} from '@angular/material/dialog';
import {RegistrationFolderValidateDialogComponent} from '../dialogs/registration-folder-validate-dialog/registration-folder-validate-dialog.component';
import {RegistrationFolderStateChangeComponent} from '../dialogs/registration-folder-state-change/registration-folder-state-change.component';
import {RegistrationFolderToBillDialogComponent} from '../dialogs/registration-folder-to-bill-dialog/registration-folder-to-bill-dialog.component';
import {ApiError} from '../../errors/errors.types';
import {FormGroup} from '@angular/forms';
import {DiscountType} from '../../api/models/proposal';
import {SnackBarComponent} from '../../material/snack-bar/snack-bar.component';
import {displayTextSnackBar} from '../../utils/displayTextSnackBar';
import {TranslateService} from '@ngx-translate/core';
import {MatSnackBar} from '@angular/material/snack-bar';
import {Subscription} from '../../api/models/subscription';
import {Organism} from '../../api/models/organism';
import {DialogUpgradeSubscriptionComponent} from '../../subscription/dialog-upgrade-subscription/dialog-upgrade-subscription.component';
import {Connection, ConnectionState, DataProviders} from '../../api/models/connection';
import {DialogConnectionAuthComponent} from '../../connection/dialog-connection-auth/dialog-connection-auth.component';
import {uniq} from 'lodash-es';
import {Select} from '@ngxs/store';
import {SubscriptionState} from '../../api/state/subscription.state';
import {ConnectionsState} from '../../api/state/connections.state';
import {OrganismState} from '../../api/state/organism.state';
import {MatMenuTrigger} from '@angular/material/menu';
import {ThemePalette} from '@angular/material/core';
import {RegistrationFolderStateToIconPipe} from '../../pipes/registration-folder-state-to-icon.pipe';
import {RegistrationFolderStateToColorPipe} from '../../pipes/registration-folder-state-to-color.pipe';
import {RegistrationFolderPaidDialogComponent} from '../dialogs/registration-folder-paid-dialog/registration-folder-paid-dialog.component';
import {RegistrationFolderUpdateDialogComponent} from '../dialogs/registration-folder-update-dialog/registration-folder-update-dialog.component';
import {Router} from '@angular/router';

export interface RegistrationFolderConfigEntry {
    color: ThemePalette;
    state: RegistrationFolderActionsAndStates;
    icon: string;
    iconColor: ThemePalette;
    disabled: boolean;
    tooltip?: string;
    method: (folders: RegistrationFolder[]) => void;
}

export interface RegistrationFolderActionCondition {
    allowAction: boolean;
    tooltip: string;
    type: string;
}

@Component({
    selector: 'app-registration-folder-menu',
    templateUrl: './registration-folder-menu.component.html',
    styleUrls: ['./registration-folder-menu.component.scss']
})
export class RegistrationFolderMenuComponent implements OnDestroy, OnInit, OnChanges {

    @Input() withButton = false;
    @Input() folders: RegistrationFolder[];
    @Input() isTotalRowsSelected: boolean;
    @Input() totalFolderCount: number;
    @Input() filters: { query?: string, [param: string]: string | string[] };
    @Input() panelOpenState ? = false;
    @Input() formGroup?: FormGroup;

    @Output() errorMessages?: EventEmitter<string[]> = new EventEmitter<[]>();
    @Output() loading?: EventEmitter<boolean> = new EventEmitter<boolean>();
    @Output() processedFolder: EventEmitter<RegistrationFolder>;
    @Output() initRegistrationFolder: EventEmitter<RegistrationFolder>;
    @Output() openEvent: EventEmitter<any> = new EventEmitter();
    @Output() closeEvent: EventEmitter<any> = new EventEmitter();

    @Select(SubscriptionState.subscription) subscription$: Observable<Subscription>;
    @Select(OrganismState.organism) organism$: Observable<Organism>;
    @Select(ConnectionsState.connections) connections$: Observable<Connection[]>;

    @ViewChild('menuTrigger') menuTrigger: MatMenuTrigger;

    subscription: Subscription;
    organism: Organism;
    connections: Connection[];
    hasOpenEvent: boolean;
    isLoading: boolean;
    actionsAvailable: RegistrationFolderConfigEntry[] = [];
    isLoadingProcessedRegistrationFolder: boolean;
    dataProviders = DataProviders;
    states = RegistrationFolderStates;
    errors: string[] = [];

    private _unsubscribeAll = new Subject<void>();

    constructor(private _dialog: MatDialog,
                private _translateService: TranslateService,
                private _dialogSubscription: MatDialog,
                private _snackBar: MatSnackBar,
                private _router: Router,
                private _registrationFolderStateToIconPipe: RegistrationFolderStateToIconPipe,
                private _registrationFolderStateToColorPipe: RegistrationFolderStateToColorPipe,
                private _registrationFolderService: RegistrationFolderService) {
        this.processedFolder = new EventEmitter<RegistrationFolder>();
        this.initRegistrationFolder = new EventEmitter<RegistrationFolder>();
    }

    ngOnInit(): void {
        this.hasOpenEvent = this.openEvent.observers.length > 0;
        combineLatest([
            this.organism$,
            this.subscription$,
            this.connections$,
        ]).pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe(([organism, subscription, connections]) => {
            this.subscription = subscription;
            this.organism = organism;
            this.connections = connections;
            this.initMenu();
        });
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    ngOnChanges(): void {
        this.initMenu();
    }

    private initMenu(): void {
        this.errors = [];
        this.actionsAvailable = [];
        this.hasOpenEvent = this.openEvent.observers.length > 0;
        if (this.withButton) {
            this.retrievePermissions();
        }
    }

    openPanel(): void {
        this.openEvent.emit();
    }

    closePanel(): void {
        this.closeEvent.emit();
    }

    submit(folder: RegistrationFolder, event: MouseEvent, nextState?: RegistrationFolderActionsAndStates): void {
        this.errors = [];
        this.errorMessages.emit([]);
        this.isLoading = true;
        this.loading.emit(true);
        this.isLoadingProcessedRegistrationFolder = true;
        const registrationFolderFormValue = this.formGroup.getRawValue().registrationFolder;
        const trainingActionInfo: TrainingActionInfo = {
            ...folder.trainingActionInfo, ...{
                indicativeDuration: registrationFolderFormValue.indicativeDuration,
                hoursInCompany: registrationFolderFormValue.hoursInCompany,
                hoursInCenter: registrationFolderFormValue.hoursInCenter,
                sessionStartDate: registrationFolderFormValue.sessionStartDate,
                sessionEndDate: registrationFolderFormValue.sessionEndDate
            }
        };
        const priceChange = registrationFolderFormValue.discountType === DiscountType.DISCOUNT_TYPE_NONE ? null :
            (registrationFolderFormValue.percent ? {
                percent: registrationFolderFormValue.percent,
            } : registrationFolderFormValue.amount ? {
                amount: registrationFolderFormValue.amount,
            } : registrationFolderFormValue.price && {
                price: registrationFolderFormValue.price,
            });
        const registrationFolder: RegistrationFolder = {
            ...folder, ...{
                description: registrationFolderFormValue.description,
                notes: registrationFolderFormValue.notes,
                trainingActionInfo: trainingActionInfo,
                tags: registrationFolderFormValue.tags,
                priceChange: priceChange,
                inPartnershipWith: registrationFolderFormValue.inPartnershipWith ? registrationFolderFormValue.inPartnershipWith.siret : null
            }
        };
        if (registrationFolder.state !== RegistrationFolderStates.NOT_PROCESSED) {
            // we don't want to update  trainingActionInfo / priceChange when RF is not inProcessed, we cannot update it after
            delete registrationFolder.trainingActionInfo;
            delete registrationFolder.priceChange;
        }
        this._registrationFolderService.update(registrationFolder).pipe(
            finalize(() => {
                this.isLoading = false;
                this.isLoadingProcessedRegistrationFolder = false;
                this.loading.emit(false);
            })
        ).subscribe(
            (updatedFolder) => {
                folder = {...folder, ...updatedFolder};
                this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(this._translateService.instant('common.actions.registrationFolderUpdatedSuccessfully', {
                        folderNumber: updatedFolder.externalId
                    },
                )));
                this.processedFolder.emit(updatedFolder);
                this.initRegistrationFolder.emit(updatedFolder);
                if (nextState === RegistrationFolderStates.VALIDATED) {
                    this.validate([folder], event);
                } else if (nextState === RegistrationFolderStates.REFUSED_BY_ORGANISM) {
                    this.refuse([folder], event);
                } else if (nextState === RegistrationFolderStates.ACCEPTED) {
                    this.accept([folder], event);
                } else if (nextState === RegistrationFolderStates.REJECTED_WITHOUT_TITULAIRE_SUITE) {
                    this.rejectedWithoutTitulaire([folder], event);
                } else if (nextState === RegistrationFolderStates.REFUSED_BY_ATTENDEE) {
                    this.refusedByAttendee([folder], event);
                } else if (nextState === RegistrationFolderStates.WAITING_ACCEPTATION) {
                    this.waitingAcceptation([folder], event);
                } else if (nextState === RegistrationFolderStates.IN_TRAINING) {
                    this.inTraining([folder], event);
                } else if (nextState === RegistrationFolderStates.CANCELED_BY_ORGANISM) {
                    this.cancel([folder], event);
                } else if (nextState === RegistrationFolderStates.CANCELED_BY_ATTENDEE_NOT_REALIZED) {
                    this.canceledByAttendeeNotRealized([folder], event);
                } else if (nextState === RegistrationFolderStates.TERMINATED) {
                    this.terminate([folder], event);
                } else if (nextState === RegistrationFolderStates.SERVICE_DONE_DECLARED) {
                    this.serviceDone([folder], event);
                } else if (nextState === RegistrationFolderStates.SERVICE_DONE_VALIDATED) {
                    this.toBill([folder], event);
                } else if (nextState === RegistrationFolderActions.PAID) {
                    this.paid([folder], event);
                }
            },
            (httpErrorResponse: HttpErrorResponse) => {
                this.errors = (httpErrorResponse.error as ApiError).errorMessages;
                this.errorMessages.emit((httpErrorResponse.error as ApiError).errorMessages);
            }
        );
    }

    openDialogSubscription(): void {
        this._dialogSubscription.open(DialogUpgradeSubscriptionComponent, {
            panelClass: ['full-page-scroll-50'],
            data: {
                organism: this.organism,
                subscription: this.subscription,
                action: true
            }
        });
    }

    cancel(folders: RegistrationFolder[], event: MouseEvent): void {
        if (this.checkSubscription()) {
            this.checkConnections(folders).subscribe((isConnectionOk) => {
                if (isConnectionOk) {
                    const row = folders.length === 1 ? folders[0] : null;
                    this.openActionDialog<RegistrationFolderCancelRefuse>(
                        RegistrationFolderCancelRefuseDialogComponent,
                        (data) => this.iterateOverSelection({
                            rowMapper: folder => this._registrationFolderService.cancel(folder.externalId, data),
                            rowFilter: folder => folder.state === RegistrationFolderStates.ACCEPTED,
                            row
                        }),
                        {
                            folder: folders[0],
                            type: 'canceled', plural: this.isPlural(row),
                            folderCount: this.currentSelectionCount(),
                            prefixAction: 'cancel'
                        }
                    );
                    event.stopPropagation();
                }
            });
        }
    }

    cancelAttendeeDidNotCome(folders: RegistrationFolder[], event: MouseEvent): void {
        if (this.checkSubscription()) {
            this.checkConnections(folders).subscribe((isConnectionOk) => {
                if (isConnectionOk) {
                    const row = folders.length === 1 ? folders[0] : null;
                    this.openActionDialog<RegistrationFolderCancelRefuse>(
                        RegistrationFolderCancelRefuseDialogComponent,
                        (data) => this.iterateOverSelection({
                            rowMapper: folder => this._registrationFolderService.cancel(folder.externalId, data),
                            rowFilter: folder => folder.state === RegistrationFolderStates.ACCEPTED,
                            row
                        }),
                        {
                            folder: folders[0],
                            type: 'canceled', plural: this.isPlural(row),
                            folderCount: this.currentSelectionCount(),
                            prefixAction: 'cancel',
                            attendeeDidNotCome: true
                        }
                    );
                    event.stopPropagation();
                }
            });
        }
    }


    canceledByAttendeeNotRealized(folders: RegistrationFolder[], event: MouseEvent): void {
        if (this.checkSubscription()) {
            this.checkConnections(folders).subscribe((isConnectionOk) => {
                if (isConnectionOk) {
                    const row = folders.length === 1 ? folders[0] : null;
                    this.openActionDialog<void>(
                        RegistrationFolderStateChangeComponent,
                        () => this.iterateOverSelection({
                            rowMapper: folder => this._registrationFolderService.canceledByAttendeeNotRealized(folder.externalId),
                            rowFilter: folder => folder.state === RegistrationFolderStates.ACCEPTED && folder.type !== this.dataProviders.CPF,
                            row
                        }),
                        {
                            folder: folders[0],
                            plural: this.isPlural(row),
                            folderCount: this.currentSelectionCount(),
                            prefixAction: 'canceledByAttendeeNotRealized'
                        }
                    );
                    event.stopPropagation();
                }
            });
        }
    }

    refusedByAttendee(folders: RegistrationFolder[], event: MouseEvent): void {
        if (this.checkSubscription()) {
            this.checkConnections(folders).subscribe((isConnectionOk) => {
                if (isConnectionOk) {
                    const row = folders.length === 1 ? folders[0] : null;
                    this.openActionDialog<void>(
                        RegistrationFolderStateChangeComponent,
                        () => this.iterateOverSelection({
                            rowMapper: folder => this._registrationFolderService.refusedByAttendee(folder.externalId),
                            rowFilter: folder => folder.state === RegistrationFolderStates.VALIDATED && folder.type !== this.dataProviders.CPF,
                            row
                        }),
                        {
                            folder: folders[0],
                            plural: this.isPlural(row),
                            folderCount: this.currentSelectionCount(),
                            prefixAction: 'refusedByAttendee'
                        }
                    );
                    event.stopPropagation();
                }
            });
        }
    }

    waitingAcceptation(folders: RegistrationFolder[], event: MouseEvent): void {
        if (this.checkSubscription()) {
            this.checkConnections(folders).subscribe((isConnectionOk) => {
                if (isConnectionOk) {
                    const row = folders.length === 1 ? folders[0] : null;
                    this.openActionDialog<void>(
                        RegistrationFolderStateChangeComponent,
                        () => this.iterateOverSelection({
                            rowMapper: folder => this._registrationFolderService.waitingAcceptation(folder.externalId),
                            rowFilter: folder => folder.state === RegistrationFolderStates.VALIDATED && folder.type !== this.dataProviders.CPF,
                            row
                        }),
                        {
                            folder: folders[0],
                            plural: this.isPlural(row),
                            folderCount: this.currentSelectionCount(),
                            prefixAction: 'waitingAcceptation'
                        }
                    );
                    event.stopPropagation();
                }
            });
        }
    }

    rejectedWithoutTitulaire(folders: RegistrationFolder[], event: MouseEvent): void {
        if (this.checkSubscription()) {
            this.checkConnections(folders).subscribe((isConnectionOk) => {
                if (isConnectionOk) {
                    const row = folders.length === 1 ? folders[0] : null;
                    this.openActionDialog<void>(
                        RegistrationFolderStateChangeComponent,
                        () => this.iterateOverSelection({
                            rowMapper: folder => this._registrationFolderService.rejectedWithoutTitulaire(folder.externalId),
                            rowFilter: folder => folder.state === RegistrationFolderStates.VALIDATED && folder.type !== this.dataProviders.CPF,
                            row
                        }),
                        {
                            folder: folders[0],
                            plural: this.isPlural(row),
                            folderCount: this.currentSelectionCount(),
                            prefixAction: 'rejectedWithoutTitulaire'
                        }
                    );
                    event.stopPropagation();
                }
            });
        }
    }

    inTraining(folders: RegistrationFolder[], event: MouseEvent): void {
        if (this.checkSubscription()) {
            this.checkConnections(folders).subscribe((isConnectionOk) => {
                if (isConnectionOk) {
                    const row = folders.length === 1 ? folders[0] : null;
                    this.openActionDialog<RegistrationFolderInTraining>(
                        RegistrationFolderInTrainingDialogComponent,
                        (data) => this.iterateOverSelection({
                            rowMapper: folder => this._registrationFolderService.inTraining(folder.externalId, data),
                            rowFilter: folder => folder.state === RegistrationFolderStates.ACCEPTED,
                            row
                        }),
                        {
                            folder: folders[0], plural: this.isPlural(row),
                            folderCount: this.currentSelectionCount(),
                            prefixAction: 'inTraining'
                        }
                    );
                    event.stopPropagation();
                }
            });
        }
    }

    refuse(folders: RegistrationFolder[], event: MouseEvent): void {
        if (this.checkSubscription()) {
            this.checkConnections(folders).subscribe((isConnectionOk) => {
                if (isConnectionOk) {
                    const row = folders.length === 1 ? folders[0] : null;
                    this.openActionDialog<RegistrationFolderCancelRefuse>(
                        RegistrationFolderCancelRefuseDialogComponent,
                        (data) => this.iterateOverSelection({
                            rowMapper: folder => this._registrationFolderService.refuse(folder.externalId, data),
                            rowFilter: folder => folder.state === RegistrationFolderStates.NOT_PROCESSED,
                            row
                        }),
                        {
                            folder: folders[0],
                            type: 'refused', plural: this.isPlural(row),
                            folderCount: this.currentSelectionCount(),
                            prefixAction: 'refuse'
                        }
                    );
                    event.stopPropagation();
                }
            });
        }
    }

    serviceDone(folders: RegistrationFolder[], event: MouseEvent): void {
        if (this.checkSubscription()) {
            this.checkConnections(folders).subscribe((isConnectionOk) => {
                const skipToServiceDone = folders[0].state === RegistrationFolderStates.TERMINATED;
                if (isConnectionOk) {
                    const row = folders.length === 1 ? folders[0] : null;
                    this.openActionDialog<RegistrationFolderDialogServiceDone>(
                        RegistrationFolderServiceDoneDialogComponent,
                        (data) => this.iterateOverSelection({
                            rowMapper: folder => this._registrationFolderService.serviceDone(folder.externalId, data),
                            rowFilter: folder => folder.state === RegistrationFolderStates.TERMINATED,
                            row
                        }),
                        {
                            folder: folders[0],
                            plural: this.isPlural(row),
                            folderCount: this.currentSelectionCount(),
                            prefixAction: 'serviceDone',
                            skipToServiceDone: skipToServiceDone,
                        }
                    );
                    event.stopPropagation();
                }
            });
        }
    }

    toBill(folders: RegistrationFolder[], event: MouseEvent): void {
        if (this.checkSubscription()) {
            this.checkConnections(folders).subscribe((isConnectionOk) => {
                if (isConnectionOk) {
                    const row = folders.length === 1 ? folders[0] : null;
                    this.openActionDialog<RegistrationFolderToBill>(
                        RegistrationFolderToBillDialogComponent,
                        (data) => this.iterateOverSelection({
                            rowMapper: folder => this._registrationFolderService.toBill(folder.externalId, data),
                            rowFilter: folder => folder.state === RegistrationFolderStates.SERVICE_DONE_VALIDATED && folder.type !== this.dataProviders.CPF,
                            row
                        }),
                        {
                            folder: folders[0],
                            plural: this.isPlural(row),
                            folderCount: this.currentSelectionCount(),
                            prefixAction: 'toBill'
                        }
                    );
                    event.stopPropagation();
                }
            });
        }
    }

    paid(folders: RegistrationFolder[], event: MouseEvent): void {
        if (this.checkSubscription()) {
            this.checkConnections(folders).subscribe((isConnectionOk) => {
                if (isConnectionOk) {
                    const row = folders.length === 1 ? folders[0] : null;
                    this.openActionDialog<void>(
                        RegistrationFolderPaidDialogComponent,
                        () => this.iterateOverSelection({
                            rowMapper: folder => this._registrationFolderService.paid(folder.externalId),
                            rowFilter: folder => folder.state === RegistrationFolderStates.SERVICE_DONE_VALIDATED && folder.type !== this.dataProviders.CPF,
                            row
                        }),
                        {
                            folder: folders[0],
                            plural: this.isPlural(row),
                            folderCount: this.currentSelectionCount(),
                            prefixAction: 'paid'
                        }
                    );
                    event.stopPropagation();
                }
            });
        }
    }

    terminate(folders: RegistrationFolder[], event: MouseEvent): void {
        if (this.checkSubscription()) {
            this.checkConnections(folders).subscribe((isConnectionOk) => {
                if (isConnectionOk) {
                    const row = folders.length === 1 ? folders[0] : null;
                    this.openActionDialog<RegistrationFolderTerminate>(
                        RegistrationFolderTerminateDialogComponent,
                        (data) => this.iterateOverSelection({
                            rowMapper: folder => this._registrationFolderService.terminate(folder.externalId, data),
                            rowFilter: folder => folder.state === RegistrationFolderStates.IN_TRAINING,
                            row
                        }),
                        {
                            folder: folders[0],
                            plural: this.isPlural(row),
                            folderCount: this.currentSelectionCount(),
                            prefixAction: 'terminate'
                        }
                    );
                    event.stopPropagation();
                }
            });
        }
    }

    validate(folders: RegistrationFolder[], event: MouseEvent): void {
        if (this.checkSubscription()) {
            this.checkConnections(folders).subscribe((isConnectionOk) => {
                if (isConnectionOk) {
                    const row = folders.length === 1 ? folders[0] : null;
                    this.openActionDialog<void>(
                        RegistrationFolderValidateDialogComponent,
                        () => this.iterateOverSelection({
                            rowMapper: folder => this._registrationFolderService.validate(folder.externalId),
                            rowFilter: folder => folder.state === RegistrationFolderStates.NOT_PROCESSED ||
                                (folder.state === RegistrationFolderStates.WAITING_ACCEPTATION && folder.type !== this.dataProviders.CPF),
                            row
                        }),
                        {
                            folder: folders[0],
                            plural: this.isPlural(row),
                            folderCount: this.currentSelectionCount(),
                            prefixAction: 'validate'
                        }
                    );
                    event.stopPropagation();
                }
            });
        }
    }

    accept(folders: RegistrationFolder[], event: MouseEvent): void {
        if (this.checkSubscription()) {
            this.checkConnections(folders).subscribe((isConnectionOk) => {
                if (isConnectionOk) {
                    const row = folders.length === 1 ? folders[0] : null;
                    this.openActionDialog<void>(
                        RegistrationFolderStateChangeComponent,
                        () => this.iterateOverSelection({
                            rowMapper: folder => this._registrationFolderService.accept(folder.externalId),
                            rowFilter: folder => (folder.state === RegistrationFolderStates.VALIDATED || folder.state === RegistrationFolderStates.WAITING_ACCEPTATION)
                                && folder.type !== this.dataProviders.CPF,
                            row
                        }),
                        {
                            folder: folders[0],
                            plural: this.isPlural(row),
                            folderCount: this.currentSelectionCount(),
                            prefixAction: 'accept'
                        }
                    );
                    event.stopPropagation();
                }
            });
        }
    }

    update(folders: RegistrationFolder[]): void {
        if (this.checkSubscription()) {
            if (folders.length === 1) {
                const viewName = this._router.url.includes('/kanban/') ? 'kanban' : 'liste';
                this._router.navigate(['/', 'formation', 'dossiers', viewName, folders[0].externalId, 'dossierFormation']);
                this.menuTrigger.closeMenu();
            } else {
                this.openActionDialog<RegistrationFolderUpdate>(
                    RegistrationFolderUpdateDialogComponent,
                    (data) => this.iterateOverSelection({
                        rowMapper: folder => this._registrationFolderService.update({...folder, ...data}),
                        rowFilter: folder => true
                    }),
                    {
                        folder: folders[0],
                        plural: this.isPlural(),
                        folderCount: this.currentSelectionCount(),
                        prefixAction: 'update'
                    }
                );
            }
        }
    }

    updateAndStateAction(): any {
        return this.formGroup?.dirty && this.panelOpenState;
    }

    updateAndStateActionText(): any {
        return this.formGroup?.dirty && this.panelOpenState ? this._translateService.instant('common.actions.updateAnd') : ' ';
    }

    private currentSelectionCount(): number {
        return this.isTotalRowsSelected ? this.totalFolderCount : this.folders.length;
    }

    private isPlural(row?: RegistrationFolder): boolean {
        return !row || this.folders.length > 1;
    }

    private iterateOverSelection({rowMapper, rowFilter, row}: {
        rowMapper: (row: RegistrationFolder) => Observable<RegistrationFolder>,
        rowFilter?: (row: RegistrationFolder) => boolean,
        row?: RegistrationFolder
    }): Observable<RegistrationFolder> {
        let observable;
        if (row) {
            observable = rowMapper(row);
        } else if (this.isTotalRowsSelected && this.totalFolderCount !== this.folders.length) {
            observable = this._registrationFolderService.list({
                ...this.filters,
                limit: this.totalFolderCount
            }).pipe(
                takeUntil(this._unsubscribeAll),
                map(data => data.payload),
                switchMap((folders: RegistrationFolder[]): Observable<RegistrationFolder> => {
                    const data = rowFilter ? folders.filter(rowFilter) : folders;
                    return concat(...data.map(rowMapper));
                }),
            );
        } else {
            observable = concat(...this.folders.map(rowMapper));
        }
        return observable;
    }

    private openActionDialog<T>(
        componentType: ComponentType<AbstractDialogFormComponent<T>>,
        submit: (data: T) => Observable<RegistrationFolder>,
        dialogData: {
            folder?: RegistrationFolder,
            type?: string,
            plural: boolean,
            folderCount: number,
            prefixAction: string,
            skipToServiceDone?: boolean,
            attendeeDidNotCome?: boolean
        },
    ): void {
        const dialogRef = this._dialog.open(componentType, {
            panelClass: 'full-page-scroll-40',
            height: 'auto',
            data: dialogData
        });
        dialogRef.componentInstance.submitted$.pipe(
            takeUntil(this._unsubscribeAll),
            switchMap((data: T) =>
                submit(data).pipe(
                    tap({
                        next: (registrationFolder: RegistrationFolder) => {
                            dialogRef.componentInstance.lastProcessedData(registrationFolder);
                            this.processedFolder.emit(registrationFolder);
                        },
                        complete: () => {
                            dialogRef.componentInstance.close();
                        }
                    }),
                    catchError((err) => {
                        dialogRef.componentInstance.loading = false;
                        dialogRef.componentInstance.errorMessages = (err.error as ApiError).errorMessages;
                        return null;
                    }),
                ))
        ).subscribe({
            error: (httpErrorResponse: HttpErrorResponse) => {
                dialogRef.componentInstance.loading = false;
                dialogRef.componentInstance.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
            }
        });
    }

    checkSubscription(): boolean {
        if (!this.folders[0].isAllowActions || !this.subscription.allowRegistrationFolders) {
            this.openDialogSubscription();
            return false;
        }
        return true;
    }

    checkConnections(registrationFolders: RegistrationFolder[]): Observable<boolean> {
        // List distinct connection types
        const types = uniq(registrationFolders.map((registrationFolder) => registrationFolder.type));
        // Concat waits in sequence for each data provider connection to be ok (either dialog ok or already ok)
        return concat(...types.map((type) => {
            let observable$ = of(true); // Default to "ok" if there is no connection or a connection without issue
            const dataProviderType = Object.keys(DataProviders).find(value => DataProviders[value] === type);
            if (dataProviderType) {
                const connectionFromProviderType = this.connections?.find(connection => connection.dataProvider === DataProviders[dataProviderType]);
                if (connectionFromProviderType && connectionFromProviderType.state !== ConnectionState.ACTIVE) {
                    // Piping allows triggering the dialog open only after the observable has been subscribed (= the previous connection is ok)
                    observable$ = observable$.pipe(
                        switchMap(() => {
                            const dialogRef = this._dialog.open(DialogConnectionAuthComponent, {
                                disableClose: true,
                                panelClass: ['no-padding-panel', 'full-page-scroll-40'],
                                height: 'auto',
                                data: {
                                    dataProvider: DataProviders[dataProviderType],
                                    organism: this.organism
                                }
                            });
                            return dialogRef.afterClosed();
                        })
                    );
                }
            }
            return observable$;
        })).pipe(
            // Very important! It stops the observable chain and returns if any result is "false", otherwise it returns true at the end of the chain
            every(isConnectionOk => isConnectionOk)
        );
    }

    retrievePermissions(): void {
        if (this.isTotalRowsSelected || (this.folders && this.folders.length)) {
            this.isLoading = true;
            this.retrieveActions();
        } else if (this.menuTrigger) {
            this.menuTrigger.openMenu();
        }
    }

    retrieveActions(): void {
        const actionsAvailable: RegistrationFolderConfigEntry[] = [];
        const firstRegistrationFolder = this.folders[0];
        if (this.areAllFoldersInSameState(this.folders)) {
            this._registrationFolderService.getActionsRegistrationFolder(firstRegistrationFolder.externalId).subscribe({
                next: (actions) => {
                    Object.values(RegistrationFolderConfig[firstRegistrationFolder.state]).forEach((registrationFolderConfigEntry) => {
                        const conditions: RegistrationFolderActionCondition[] = actions[registrationFolderConfigEntry.state];
                        if (conditions?.length) {
                            const allConditionsFulfilled = conditions.every(condition => condition.allowAction);
                            const registrationFolderConfigEntryCopy = {
                                ...registrationFolderConfigEntry, // Important ! don't mutate config object
                                icon: this._registrationFolderStateToIconPipe.transform(registrationFolderConfigEntry.state),
                                iconColor: this._registrationFolderStateToColorPipe.transform(registrationFolderConfigEntry.state),
                                disabled: !allConditionsFulfilled,
                                tooltip: this.getTooltip(conditions),
                                method: (registrationFolders: RegistrationFolder[]) => {
                                    this.callChangeStateMethod(registrationFolders, registrationFolderConfigEntry.state, event);
                                }
                            };
                            actionsAvailable.push(registrationFolderConfigEntryCopy);
                        }
                    });
                },
                complete: () => this.addUpdateAction(actionsAvailable)
            });
        } else {
            this.addUpdateAction(actionsAvailable);
        }
    }

    getTooltip(conditions: RegistrationFolderActionCondition[]): string {
        return conditions.filter(condition => condition.tooltip).map(condition => condition.tooltip).join(' ');
    }

    private callChangeStateMethod(registrationFolders: RegistrationFolder[], targetState: RegistrationFolderActionsAndStates, $event): void {
        if (targetState === RegistrationFolderStates.VALIDATED) {
            this.validate(registrationFolders, $event);
        } else if (targetState === RegistrationFolderStates.REFUSED_BY_ORGANISM) {
            this.refuse(registrationFolders, $event);
        } else if (targetState === RegistrationFolderStates.ACCEPTED) {
            this.accept(registrationFolders, $event);
        } else if (targetState === RegistrationFolderStates.REJECTED_WITHOUT_TITULAIRE_SUITE) {
            this.rejectedWithoutTitulaire(registrationFolders, $event);
        } else if (targetState === RegistrationFolderStates.REFUSED_BY_ATTENDEE) {
            this.refusedByAttendee(registrationFolders, $event);
        } else if (targetState === RegistrationFolderStates.WAITING_ACCEPTATION) {
            this.waitingAcceptation(registrationFolders, $event);
        } else if (targetState === RegistrationFolderStates.IN_TRAINING) {
            this.inTraining(registrationFolders, $event);
        } else if (targetState === RegistrationFolderStates.CANCELED_BY_ORGANISM) {
            this.cancel(registrationFolders, $event);
        } else if (targetState === RegistrationFolderStates.CANCELED_BY_ATTENDEE_NOT_REALIZED) {
            this.canceledByAttendeeNotRealized(registrationFolders, $event);
        } else if (targetState === RegistrationFolderStates.TERMINATED) {
            this.terminate(registrationFolders, $event);
        } else if (targetState === RegistrationFolderStates.SERVICE_DONE_DECLARED) {
            this.serviceDone(registrationFolders, $event);
        } else if (targetState === RegistrationFolderStates.SERVICE_DONE_VALIDATED || targetState === RegistrationFolderActions.TO_BILL) {
            this.toBill(registrationFolders, $event);
        } else if (targetState === RegistrationFolderActions.PAID) {
            this.paid(registrationFolders, $event);
        }
    }

    areAllFoldersInSameState(folders: RegistrationFolder[]): boolean {
        return folders.every(folder => folder.state === folders[0].state);
    }

    getSingleAction(): RegistrationFolderConfigEntry {
        if (this.actionsAvailable?.length) {
            let action = this.actionsAvailable[0];
            if (action.disabled && this.actionsAvailable.length > 1 && !this.actionsAvailable[1].disabled) {
                // hack for state = serviceDoneValidated => two possibilities (toBill and paid)
                // if billingState is billed we don't want to display firstAction => Marquer comme facturé // want do display "Marquer comme payé"
                action = this.actionsAvailable[1];
            }
            return action;
        } else {
            return null;
        }
    }

    private addUpdateAction(actionsAvailable: RegistrationFolderConfigEntry[]): void {
        // ajout de l’action "update" en dehors de tout état
        if (!this.withButton) {
            actionsAvailable.unshift({
                icon: this._registrationFolderStateToIconPipe.transform('update'),
                iconColor: this._registrationFolderStateToColorPipe.transform('update'),
                disabled: false,
                state: null,
                tooltip: '',
                method: (registrationFolders: RegistrationFolder[]) => {
                    this.update(registrationFolders);
                },
                color: 'primary'
            });
        }

        this.actionsAvailable = actionsAvailable;
        this.isLoading = false;
        if (this.menuTrigger) {
            this.menuTrigger.openMenu();
        }
    }
}
