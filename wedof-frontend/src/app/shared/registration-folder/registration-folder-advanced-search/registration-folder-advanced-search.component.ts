import {Component, EventEmitter, Input, OnDestroy, OnInit, Output} from '@angular/core';
import {FormGroup} from '@angular/forms';
import {Certification} from '../../api/models/certification';
import {CertificationService} from '../../api/services/certification.service';
import {formatMomentToIsoDate} from '../../utils/date-utils';
import {TrainingService} from '../../api/services/training.service';
import {Training} from '../../api/models/training';
import {TrainingActionService} from '../../api/services/training-action.service';
import {TrainingAction} from '../../api/models/training-action';
import {Session} from '../../api/models/session';
import {SessionService} from '../../api/services/session.service';
import {
    getCertificationFolderAllStates,
    getMessageStates,
    getPeriodTypes,
    getRegistrationFolderAllBillingStates,
    getRegistrationFolderAllControlStates,
    getRegistrationFolderAllDataProviders,
    getRegistrationFolderAllStates
} from '../../utils/states-utils';
import {Params, Router} from '@angular/router';
import {TranslateService} from '@ngx-translate/core';
import {DatePipe} from '@angular/common';
import {cloneDeep} from 'lodash-es';
import {DisplayCatalogExternalIdPipe} from '../../pipes/display-catalog-external-id.pipe';
import {SearchMethod} from '../../material/infinite-scroll/infinite-scroll.component';
import {RegistrationFolderFilters} from '../registration-folder-table/registration-folder-table.component';
import {EntityClass} from '../../utils/enums/entity-class';
import {pairwise, takeUntil} from 'rxjs/operators';
import {Observable, Subject} from 'rxjs';
import {Store} from '@ngxs/store';
import {AddUserFilter, UserState} from '../../api/state/user.state';
import {MatDialog} from '@angular/material/dialog';
import {DialogManageFiltersComponent} from '../../filters/dialog-manage-filters/dialog-manage-filters.component';
import {Filter} from '../../api/models/user';
import {SnackBarComponent} from '../../material/snack-bar/snack-bar.component';
import {displayTextSnackBar} from '../../utils/displayTextSnackBar';
import {MatSnackBar} from '@angular/material/snack-bar';
import {generateColor} from '../../utils/color-utils';
import {PeriodTypes} from '../../api/models/period-types';
import {CertificationPartnerStates} from '../../api/models/certification-partner';
import {MessageTemplateService} from '../../api/services/message-template.service';
import {MessageTemplateState} from '../../api/models/message-template';

@Component({
    selector: 'app-registration-folder-advanced-search',
    templateUrl: './registration-folder-advanced-search.component.html',
    styleUrls: ['./registration-folder-advanced-search.component.scss']
})

export class RegistrationFolderAdvancedSearchComponent implements OnInit, OnDestroy {

    maxDate: Date;
    certificationFolderStates = [];
    registrationFolderStates = [];
    dataProviders = [];
    periods = [];
    billingStates = [];
    filterOnStates = [];
    controlStates = [];
    completionRates = [];
    messageTemplateChoices = [];
    messageStates = [];
    hasMessageTemplate: boolean;
    activeFiltersCount = 0;
    filters: Filter[];
    filters$: Observable<Filter[]>;

    resultFormatterCertification: any;
    searchMethodCertification: SearchMethod;

    resultFormatterTraining: any;
    searchMethodTraining: SearchMethod;

    resultFormatterTrainingAction: any;
    searchMethodTrainingAction: SearchMethod;

    resultFormatterSession: any;
    searchMethodSession: SearchMethod;

    private _unsubscribeAll = new Subject<void>();
    private entityClass: EntityClass = EntityClass.REGISTRATION_FOLDER;

    readonly PeriodTypes = PeriodTypes;

    @Input() searchAdvanced: FormGroup;
    @Output() closePopOver: EventEmitter<any> = new EventEmitter<any>();

    constructor(
        private _certificationService: CertificationService,
        private _trainingService: TrainingService,
        private _trainingActionService: TrainingActionService,
        private _sessionService: SessionService,
        private _router: Router,
        private _translateService: TranslateService,
        private _datePipe: DatePipe,
        private _displayCatalogExternalIdPipe: DisplayCatalogExternalIdPipe,
        private _store: Store,
        private _dialog: MatDialog,
        private _snackBar: MatSnackBar,
        private _messageTemplateService: MessageTemplateService
    ) {
        this.maxDate = new Date();
    }

    ngOnInit(): void {
        this.resultFormatterSession = (session: Session) => this._translateService.instant('private.training-organism.folders.createFolder.form.session.sessionResult', {
            startDate: this.isDateDefined(session.startDate),
            endDate: this.isDateDefined(session.endDate),
            city: session.sessionInfo.city,
            trainingTitle: session.sessionInfo.trainingTitle
        });
        this.resultFormatterCertification = (certification: Certification) => certification.externalId + ' - ' + certification.name;
        this.resultFormatterTraining = (training: Training) => this._displayCatalogExternalIdPipe.transform(training.externalId) + ' - ' + training.title;
        this.resultFormatterTrainingAction = (trainingAction: TrainingAction) => this._displayCatalogExternalIdPipe.transform(trainingAction.externalId);

        this.searchMethodCertification = (params: any) => {
            params.certificationPartnerState = [CertificationPartnerStates.ACTIVE, CertificationPartnerStates.REVOKED, CertificationPartnerStates.SUSPENDED].join(',');
            return this._certificationService.listLite(params);
        };
        this.searchMethodSession = (params: any) => this._sessionService.list(params);
        this.searchMethodTraining = (params: any) => this._trainingService.list(params);
        this.searchMethodTrainingAction = (params: any) => this._trainingActionService.list(params);

        this.registrationFolderStates = getRegistrationFolderAllStates();
        this.certificationFolderStates = getCertificationFolderAllStates();
        this.dataProviders = getRegistrationFolderAllDataProviders();
        this.billingStates = getRegistrationFolderAllBillingStates();
        this.controlStates = getRegistrationFolderAllControlStates();
        this.periods = getPeriodTypes();
        this.completionRates = [
            {
                label: this._translateService.instant('common.actions.search.completionRate.0eq'),
                value: '0'
            },
            {
                label: this._translateService.instant('common.actions.search.completionRate.25lt'),
                value: '25<'
            },
            {
                label: this._translateService.instant('common.actions.search.completionRate.25gte80lte'),
                value: '25<>80'
            },
            {
                label: this._translateService.instant('common.actions.search.completionRate.80gt'),
                value: '>80'
            },
            {
                label: this._translateService.instant('common.actions.search.completionRate.100eq'),
                value: '100'
            }
        ];
        this._messageTemplateService.list({
            entityClass: this.entityClass
        }).subscribe((messageTemplates) => {
            this.hasMessageTemplate = messageTemplates.payload?.length > 0;
            if (this.hasMessageTemplate) {
                messageTemplates.payload.forEach((messageTemplate) => {
                    const state = messageTemplate.state === MessageTemplateState.ACTIVE ? '(Actif)' : '(Inactif)';
                    const label = messageTemplate.title + ' ' + state;
                    this.messageTemplateChoices.push({
                        label: label,
                        value: messageTemplate.id.toString()
                    });
                });
                this.messageStates = getMessageStates();
            }
        });
        this.refreshFilterOnStateDates();

        this.searchAdvanced.valueChanges
            .pipe(takeUntil(this._unsubscribeAll))
            .pipe(pairwise<RegistrationFolderFilters>())
            .subscribe(([previousRegistrationFolderFilters, registrationFolderFilters]) => {
                this.activeFiltersCount = Object.keys(registrationFolderFilters).filter(key => {
                    if (key === 'filterOnStateDate' && registrationFolderFilters[key] === 'lastUpdate') {
                        return false;
                    } else if (Array.isArray(registrationFolderFilters[key])) {
                        return registrationFolderFilters[key].length > 0;
                    } else {
                        return registrationFolderFilters[key];
                    }
                }).length;
                if (registrationFolderFilters.period !== PeriodTypes.CUSTOM) {
                    registrationFolderFilters.since = null;
                    registrationFolderFilters.until = null;
                }
                this.refreshFilterOnStateDates(registrationFolderFilters, previousRegistrationFolderFilters);
            });

        this.filters$ = this._store.select(UserState.userFilteredFilters(this.entityClass));
        this.filters$.pipe(takeUntil(this._unsubscribeAll)).subscribe((filters) => {
            this.filters = filters;
        });
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    clearValue(valueToRemoved: string, event?: MouseEvent): void {
        this.searchAdvanced.get(valueToRemoved).setValue(null);
        event?.stopPropagation();
    }

    resetUrl(): void {
        this._router.navigate([], {queryParams: null});
        this.close();
    }

    cancel(): void {
        this.searchAdvanced.reset();
        this.close();
    }

    close(): void {
        this.closePopOver.emit();
    }

    isDateDefined(date: string): string {
        return date ? this._datePipe.transform(date, 'mediumDate') : this._translateService.instant('private.common.form.placeholder');
    }

    searchButton(): void {
        this._router.navigate([], {queryParams: this.getQueryParams()});
        this.close();
    }

    saveAsFilter(): void {
        this._router.navigate([], {queryParams: this.getQueryParams()}).then(() => {
            const link = this._router.url.split('?')[1];
            const existingFilter = this.filters.find((filter) => filter.link === link);
            if (!existingFilter) {
                this._store.dispatch(new AddUserFilter({
                    name: 'Nouveau filtre',
                    entityClass: this.entityClass,
                    link: link,
                    color: generateColor()
                }));
                this.openManageFiltersDialog();
            } else {
                this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(
                    this._translateService.instant('private.common.filters.existingFilterAlert', {filterName: existingFilter.name})
                    , 5000, 'red'));

            }
        });
    }

    openManageFiltersDialog(): void {
        this._dialog.open(DialogManageFiltersComponent, {
            data: {
                filters$: this.filters$,
                entityClass: this.entityClass
            }
        });
    }

    private getQueryParams(): Params {
        const queryParams = {};
        Object.entries(this.searchAdvanced.value).map(([key, value]: [string, any]) => {
            if (key === 'tags') {
                if (value && value.length) {
                    queryParams[key] = value.join(',');
                } else {
                    delete queryParams[key];
                }
            } else if (value) {
                if (key === 'state' || key === 'type' || key === 'billingState' || key === 'certificationFolderState' || key === 'controlState' || key === 'messageState' || key === 'messageTemplate') {
                    queryParams[key] = value.join(',');
                } else if ((key === 'since' || key === 'until') && typeof value !== 'string') {
                    queryParams[key] = formatMomentToIsoDate(key === 'since' ? value.startOf('day') : value.endOf('day'));
                } else {
                    queryParams[key] = value;
                }
            }
        });
        return queryParams;
    }

    private refreshFilterOnStateDates(currentFilters: RegistrationFolderFilters = null, previousFilters: RegistrationFolderFilters = null): void {
        const currentPeriodInFuture = currentFilters && currentFilters.period ? (
            currentFilters.period.toLowerCase().startsWith('next')
            || currentFilters.period.toLowerCase().startsWith('tomorrow')
            || currentFilters.period.toLowerCase().endsWith('future')
        ) : false;
        const previousPeriodInFuture = previousFilters && previousFilters.period ? (
            previousFilters.period.toLowerCase().startsWith('next')
            || previousFilters.period.toLowerCase().startsWith('tomorrow')
            || previousFilters.period.toLowerCase().endsWith('future')
        ) : false;
        if (previousPeriodInFuture !== currentPeriodInFuture || currentFilters == null) {
            if (currentFilters) {
                currentFilters.filterOnStateDate = null;
            }
            this.filterOnStates = [];
            ['sessionStartDate', 'sessionEndDate', 'paymentScheduledDate'].forEach(dateField => {
                this.filterOnStates.push({
                    label: `common.actions.search.updateFilterOnStateDate.registrationFolderStateDate.${dateField}`,
                    value: dateField
                });
            });
            if (!currentPeriodInFuture) {
                const stateFilters = cloneDeep(this.registrationFolderStates).filter(state =>
                    state.value !== 'waitingAcceptation'
                ).map(state => {
                    return {
                        label: `common.actions.search.updateFilterOnStateDate.registrationFolderStateDate.${state.value}`,
                        value: state.value + 'Date'
                    };
                });
                this.filterOnStates.push(...stateFilters);
                this.filterOnStates.unshift({
                    label: `common.actions.search.lastUpdate`,
                    value: 'lastUpdate'
                });
            }
        }
    }
}
