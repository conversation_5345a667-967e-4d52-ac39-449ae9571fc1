<div class="popover mat-elevation-z4">
    <form [formGroup]="searchAdvanced">
        <div class="flex flex-col overflow-y-auto" style="max-height: 66vh;">

            <div class="flex">
                <mat-form-field class="flex-auto gt-xs:pr-3">
                    <mat-label class="font-normal">{{'common.actions.search.includes' | translate}}</mat-label>
                    <input matInput formControlName="query" class="input"
                           [placeholder]="'common.table.filters.globalSearch' | translate"
                           maxlength="50">
                    <button type="button" *ngIf="searchAdvanced.get('query').value" mat-button matSuffix
                            mat-icon-button aria-label="Clear" (click)="clearValue('query', $event)">
                        <mat-icon svgIcon="close"></mat-icon>
                    </button>
                </mat-form-field>

                <mat-form-field class="flex-auto gt-xs:pl-3">
                    <mat-label
                        class="font-normal">{{'common.actions.search.registrationFolderState' | translate}}
                        {{searchAdvanced.get('state').value?.length >= 1 ? '(' + searchAdvanced.get('state').value.length + ')' : '' }}</mat-label>
                    <mat-select multiple formControlName="state">
                        <mat-option *ngFor="let registrationFolderState of registrationFolderStates"
                                    [value]="registrationFolderState.value">
                            {{registrationFolderState.label | translate}}
                        </mat-option>
                    </mat-select>
                    <button type="button"
                            *ngIf="searchAdvanced.get('state').value?.length > 0"
                            mat-button matSuffix
                            mat-icon-button aria-label="Clear" (click)="clearValue('state', $event)">
                        <mat-icon svgIcon="close"></mat-icon>
                    </button>
                </mat-form-field>
            </div>

            <app-tag-input [labelClass]="'font-normal'"
                           [label]="'Tags'"
                           [creatingAvailable]="false"
                           [clearValueIcon]="true"
                           (clearValue)="clearValue('tags')"
                           [control]="searchAdvanced.get('tags')"></app-tag-input>

            <div class="flex ">
                <mat-form-field class="flex-auto gt-xs:pr-3">
                    <mat-label
                        class="font-normal">{{'common.actions.search.controlState' | translate}}
                        {{searchAdvanced.get('controlState').value?.length >= 1 ? '(' + searchAdvanced.get('controlState').value.length + ')' : '' }}</mat-label>
                    <mat-select multiple formControlName="controlState">
                        <mat-option *ngFor="let controlState of controlStates"
                                    [value]="controlState.value">
                            {{controlState.label | translate}}
                        </mat-option>
                    </mat-select>
                    <button type="button"
                            *ngIf="searchAdvanced.get('state').value?.length > 0"
                            mat-button matSuffix
                            mat-icon-button aria-label="Clear" (click)="clearValue('state', $event)">
                        <mat-icon svgIcon="close"></mat-icon>
                    </button>
                </mat-form-field>

                <mat-form-field class="flex-auto gt-xs:pl-3">
                    <mat-label
                        class="font-normal">{{'common.actions.search.certificationFolderState' | translate}}
                        {{searchAdvanced.get('certificationFolderState').value?.length >= 1 ? '(' + searchAdvanced.get('certificationFolderState').value?.length + ')' : '' }}</mat-label>
                    <mat-select multiple formControlName="certificationFolderState">
                        <mat-option *ngFor="let certificationFolderState of certificationFolderStates"
                                    [value]="certificationFolderState.value">
                            {{certificationFolderState.label | translate}}
                        </mat-option>
                    </mat-select>
                    <button type="button"
                            *ngIf="searchAdvanced.get('certificationFolderState').value?.length > 0"
                            mat-button
                            matSuffix
                            mat-icon-button aria-label="Clear" (click)="clearValue('certificationFolderState', $event)">
                        <mat-icon svgIcon="close"></mat-icon>
                    </button>
                </mat-form-field>
            </div>

            <div class="flex">
                <mat-form-field class="flex-auto gt-xs:pr-3">
                    <mat-label class="font-normal">{{'common.actions.search.billingState' | translate}}
                        {{searchAdvanced.get('billingState').value?.length >= 1 ? '(' + searchAdvanced.get('billingState').value?.length + ')' : '' }}</mat-label>
                    <mat-select multiple formControlName="billingState">
                        <mat-option *ngFor="let billing of billingStates" [value]="billing.value">
                            {{billing.label | translate}}
                        </mat-option>
                    </mat-select>
                    <button type="button" *ngIf="searchAdvanced.get('billingState').value?.length > 0" mat-button
                            matSuffix
                            mat-icon-button aria-label="Clear" (click)="clearValue('billingState', $event)">
                        <mat-icon svgIcon="close"></mat-icon>
                    </button>
                </mat-form-field>

                <mat-form-field class="flex-auto gt-xs:pl-3">
                    <mat-label class="font-normal">{{'common.actions.search.funding' | translate}}
                        {{searchAdvanced.get('type').value?.length >= 1 ? '(' + searchAdvanced.get('type').value.length + ')' : ''  }}</mat-label>
                    <mat-select multiple formControlName="type">
                        <mat-option *ngFor="let type of dataProviders" [value]="type.value">
                            {{type.label | translate}}
                        </mat-option>
                    </mat-select>
                    <button type="button" *ngIf="searchAdvanced.get('type').value?.length > 0" mat-button matSuffix
                            mat-icon-button aria-label="Clear" (click)="clearValue('type', $event)">
                        <mat-icon svgIcon="close"></mat-icon>
                    </button>
                </mat-form-field>
            </div>

            <div class="flex">
                <mat-form-field
                    [class]="searchAdvanced.get('period')?.value ? 'flex-auto gt-xs:pr-3' : 'flex-auto input-line'">
                    <mat-label class="font-normal">{{'common.actions.search.period.title' | translate}}</mat-label>
                    <mat-select formControlName="period">
                        <mat-option *ngFor="let period of periods" [value]="period.value">
                            {{period.label | translate}}
                        </mat-option>
                    </mat-select>
                    <button type="button" *ngIf="searchAdvanced.get('period').value" mat-button matSuffix
                            mat-icon-button aria-label="Clear" (click)="clearValue('period', $event)">
                        <mat-icon svgIcon="close"></mat-icon>
                    </button>
                </mat-form-field>

                <mat-form-field class="flex-auto gt-xs:pr-3"
                                *ngIf="searchAdvanced.get('period')?.value === PeriodTypes.CUSTOM">
                    <mat-label class="font-normal">{{'common.actions.search.since' | translate}}</mat-label>
                    <input formControlName="since" matInput
                           [required]="searchAdvanced.get('period')?.value === PeriodTypes.CUSTOM"
                           [max]="searchAdvanced.get('until')?.value ? searchAdvanced.get('until')?.value : maxDate"
                           [matDatepicker]="datePickerSince">
                    <button type="button" class="mr-2" *ngIf="searchAdvanced.get('since').value" mat-button matSuffix
                            mat-icon-button aria-label="Clear" (click)="clearValue('since', $event)">
                        <mat-icon svgIcon="close"></mat-icon>
                    </button>
                    <mat-datepicker-toggle matSuffix [for]="datePickerSince"></mat-datepicker-toggle>
                    <mat-datepicker #datePickerSince></mat-datepicker>
                </mat-form-field>

                <mat-form-field class="flex-auto gt-xs:pl-3"
                                *ngIf="searchAdvanced.get('period')?.value === PeriodTypes.CUSTOM">
                    <mat-label class="font-normal">{{'common.actions.search.until' | translate}}</mat-label>
                    <input formControlName="until" matInput [max]="maxDate"
                           [required]="searchAdvanced.get('period')?.value === PeriodTypes.CUSTOM"
                           [min]="searchAdvanced.get('since')?.value"
                           [matDatepicker]="datePickerUntil">
                    <button type="button" class="mr-2" *ngIf="searchAdvanced.get('until').value" mat-button matSuffix
                            mat-icon-button aria-label="Clear" (click)="clearValue('until', $event)">
                        <mat-icon svgIcon="close"></mat-icon>
                    </button>
                    <mat-datepicker-toggle matSuffix [for]="datePickerUntil"></mat-datepicker-toggle>
                    <mat-datepicker #datePickerUntil></mat-datepicker>
                </mat-form-field>

            </div>

            <mat-form-field class="flex-auto"
                            *ngIf="searchAdvanced.get('period')?.value">
                <mat-label class="font-normal">{{'common.actions.search.filterOnStateDate' | translate}}</mat-label>
                <mat-select formControlName="filterOnStateDate">
                    <mat-option *ngFor="let filterOnState of filterOnStates" [value]="filterOnState.value">
                        {{filterOnState.label | translate}}
                    </mat-option>
                </mat-select>
                <button type="button" *ngIf="searchAdvanced.get('filterOnStateDate').value" mat-button matSuffix
                        mat-icon-button aria-label="Clear" (click)="clearValue('filterOnStateDate', $event)">
                    <mat-icon svgIcon="close"></mat-icon>
                </button>
            </mat-form-field>

            <div class="flex">
                <mat-form-field class="flex-auto gt-xs:pr-3">
                    <mat-label
                        class="font-normal">{{'common.actions.search.messageTemplate.title' | translate}}
                    </mat-label>
                    <mat-select multiple
                                [matTooltip]="(!hasMessageTemplate ? 'common.actions.search.messageTemplate.tooltip' : '') | translate"
                                formControlName="messageTemplate">
                        <mat-option [disabled]="!hasMessageTemplate"
                                    *ngFor="let messageTemplate of messageTemplateChoices"
                                    [value]="messageTemplate.value">
                            {{ messageTemplate.label | translate }}
                        </mat-option>
                    </mat-select>
                    <button type="button" *ngIf="searchAdvanced.get('messageTemplate').value" mat-button matSuffix
                            mat-icon-button aria-label="Clear" (click)="clearValue('messageTemplate', $event)">
                        <mat-icon svgIcon="close"></mat-icon>
                    </button>
                </mat-form-field>
                <mat-form-field class="flex-auto gt-xs:pl-3">
                    <mat-label
                        class="font-normal">{{'common.actions.search.messageState' | translate}}
                    </mat-label>
                    <mat-select multiple
                                [matTooltip]="(!hasMessageTemplate ? 'common.actions.search.messageTemplate.tooltip' : '') | translate"
                                formControlName="messageState">
                        <mat-option [disabled]="!hasMessageTemplate"
                                    *ngFor="let messageState of messageStates"
                                    [value]="messageState.value">
                            {{ messageState.label | translate }}
                        </mat-option>
                    </mat-select>
                    <button type="button" *ngIf="searchAdvanced.get('messageState').value" mat-button matSuffix
                            mat-icon-button aria-label="Clear" (click)="clearValue('messageState', $event)">
                        <mat-icon svgIcon="close"></mat-icon>
                    </button>
                </mat-form-field>
            </div>

            <app-infinite-scroll [controlName]="'trainingId'"
                                 [noEntriesFoundLabel]="'common.actions.search.noResult' | translate "
                                 [placeholderLabel]="'common.actions.search.select' | translate : {choice: 'une formation'}"
                                 [placeholder]="'common.actions.search.select' | translate : {choice: 'une formation'}"
                                 [label]="'common.actions.search.training' | translate "
                                 [formGroup]="searchAdvanced"
                                 [parameters]="{'limit' : 25}"
                                 [resultFormatter]="resultFormatterTraining"
                                 [searchMethod]="searchMethodTraining"
                                 [containerClass]="'flex-auto '"
                                 [labelClass]="'font-normal'"
                                 [searchComparisonProperty]="'externalId'"
                                 [searchAdvanced]="true"
            ></app-infinite-scroll>
            <div class="flex">
                <div class="flex-auto gt-xs:pr-3">
                    <app-infinite-scroll [controlName]="'trainingActionId'"
                                         [noEntriesFoundLabel]="'common.actions.search.noResult' | translate "
                                         [placeholderLabel]="'common.actions.search.select' | translate : {choice: 'une action de formation'}"
                                         [placeholder]="'common.actions.search.select' | translate : {choice: 'une action de formation'}"
                                         [label]="'common.actions.search.trainingAction' | translate "
                                         [formGroup]="searchAdvanced"
                                         [parameters]="{'limit' : 25}"
                                         [resultFormatter]="resultFormatterTrainingAction"
                                         [searchMethod]="searchMethodTrainingAction"
                                         [containerClass]="'flex-auto '"
                                         [labelClass]="'font-normal'"
                                         [searchComparisonProperty]="'externalId'"
                                         [searchAdvanced]="true"
                    ></app-infinite-scroll>
                </div>
                <div class="flex-auto gt-xs:pl-3">
                    <app-infinite-scroll [controlName]="'sessionId'"
                                         [noEntriesFoundLabel]="'common.actions.search.noResult' | translate "
                                         [placeholderLabel]="'common.actions.search.select' | translate : {choice: 'une session'}"
                                         [placeholder]="'common.actions.search.select' | translate : {choice: 'une session'}"
                                         [label]="'common.actions.search.session' | translate "
                                         [formGroup]="searchAdvanced"
                                         [resultFormatter]="resultFormatterSession"
                                         [searchMethod]="searchMethodSession"
                                         [parameters]="{'sort' : 'startDate', 'order' : 'asc', 'limit' : 100}"
                                         [containerClass]="'flex-auto '"
                                         [labelClass]="'font-normal'"
                                         [searchComparisonProperty]="'externalId'"
                                         [searchAdvanced]="true"
                    ></app-infinite-scroll>
                </div>
            </div>
            <app-infinite-scroll [controlName]="'certifInfo'"
                                 [noEntriesFoundLabel]="'private.certification.folders.createFolder.form.certification.noCertification' | translate"
                                 [placeholderLabel]="'common.actions.search.select' | translate : {choice: 'une certification'}"
                                 [placeholder]="'common.actions.search.select' | translate : {choice: 'une certification'}"
                                 [label]="'common.actions.search.certification' | translate "
                                 [formGroup]="searchAdvanced"
                                 [parameters]="{'limit' : 15}"
                                 [resultFormatter]="resultFormatterCertification"
                                 [searchMethod]="searchMethodCertification"
                                 [containerClass]="'flex-auto '"
                                 [labelClass]="'font-normal'"
                                 [searchComparisonProperty]="'certifInfo'"
                                 [searchAdvanced]="true"
            ></app-infinite-scroll>

            <div class="flex">
                <mat-form-field class="flex-auto gt-xs:pr-3">
                    <mat-label
                        class="font-normal">{{'common.actions.search.completionRate.title' | translate}}</mat-label>
                    <mat-select formControlName="completionRate">
                        <mat-option *ngFor="let completionRate of completionRates" [value]="completionRate.value">
                            {{completionRate.label | translate}}
                        </mat-option>
                    </mat-select>
                    <button type="button" *ngIf="searchAdvanced.get('completionRate').value" mat-button
                            matSuffix
                            mat-icon-button aria-label="Clear" (click)="clearValue('completionRate', $event)">
                        <mat-icon svgIcon="close"></mat-icon>
                    </button>
                </mat-form-field>

                <mat-form-field class="flex-auto gt-xs:pl-3">
                    <mat-label
                        class="font-normal">{{'common.actions.search.completionRate.days' | translate}}</mat-label>
                    <input matInput formControlName="daysSinceLastUpdatedCompletionRate" class="input" type="number"
                           placeholder="3">
                    <span matSuffix>{{'common.actions.search.completionRate.unit' | translate}}</span>
                    <mat-icon [matTooltipPosition]="'above'"
                              [matTooltip]="'common.actions.search.completionRate.tooltip' | translate"
                              matSuffix
                              svgIcon="help_outline"></mat-icon>

                </mat-form-field>
            </div>

            <mat-form-field class="flex-auto input-line">
                <mat-label class="font-normal">{{'common.actions.search.proposalCode' | translate}}</mat-label>
                <input matInput formControlName="proposalCode" class="input">
                <button type="button" *ngIf="searchAdvanced.get('proposalCode').value" mat-button matSuffix
                        mat-icon-button aria-label="Clear" (click)="clearValue('proposalCode', $event)">
                    <mat-icon svgIcon="close"></mat-icon>
                </button>
            </mat-form-field>
        </div>

        <div class="flex mt-8 justify-between">
            <div class="flex items-center">
                <button mat-icon-button
                        type="button"
                        (click)="openManageFiltersDialog()">
                    <mat-icon svgIcon="settings"></mat-icon>
                </button>
                <button type="button" mat-button
                        [disabled]="activeFiltersCount ===  0"
                        (click)="saveAsFilter()">{{'private.common.filters.saveAsFilter' | translate}}
                </button>
            </div>
            <div class="flex justify-end">
                <button type="button" class="opacity-54 mr-5"
                        (click)="resetUrl()">{{ 'common.actions.search.deleteAllFilters' | translate }}</button>
                <button type="submit" color="primary"
                        mat-flat-button
                        (click)="searchButton()">{{ 'common.table.filters.globalSearch' | translate }}
                </button>
            </div>
        </div>
    </form>
</div>
