import {NgModule} from '@angular/core';
import {CommonModule, DatePipe} from '@angular/common';
import {TranslateModule} from '@ngx-translate/core';
import {MarkdownModule} from 'ngx-markdown';
import {MaterialModule} from '../material/material.module';
import {RegistrationFolderCardComponent} from './registration-folder-card/registration-folder-card.component';
import {RegistrationFolderTableComponent} from './registration-folder-table/registration-folder-table.component';
import {ReactiveFormsModule} from '@angular/forms';
import {TreoMessageModule} from '../../../@treo/components/message';
import {DndModule} from '../directives/DndModule';
import {FileModule} from '../file/file.module';
import {MatTooltipModule} from '@angular/material/tooltip';
import {RegistrationFolderCancelRefuseDialogComponent} from './dialogs/registration-folder-cancel-refuse-dialog/registration-folder-cancel-refuse-dialog.component';
import {RegistrationFolderInTrainingDialogComponent} from './dialogs/registration-folder-in-training-dialog/registration-folder-in-training-dialog.component';
import {RegistrationFolderServiceDoneDialogComponent} from './dialogs/registration-folder-service-done-dialog/registration-folder-service-done-dialog.component';
import {RegistrationFolderTerminateDialogComponent} from './dialogs/registration-folder-terminate-dialog/registration-folder-terminate-dialog.component';
import {RegistrationFolderValidateDialogComponent} from './dialogs/registration-folder-validate-dialog/registration-folder-validate-dialog.component';
import {RegistrationFolderToBillDialogComponent} from './dialogs/registration-folder-to-bill-dialog/registration-folder-to-bill-dialog.component';
import {RegistrationFolderMenuComponent} from './registration-folder-menu/registration-folder-menu.component';
import {PipesModule} from '../pipes/pipes.module';
import {MatCardModule} from '@angular/material/card';
import {RegistrationFolderStateChangeComponent} from './dialogs/registration-folder-state-change/registration-folder-state-change.component';
import {RegistrationFolderCreateComponent} from './registration-folder-create/registration-folder-create.component';
import {NgxMatSelectSearchModule} from 'ngx-mat-select-search';
import {RegistrationFolderAdvancedSearchComponent} from './registration-folder-advanced-search/registration-folder-advanced-search.component';
import {MatSelectInfiniteScrollModule} from 'ng-mat-select-infinite-scroll';
import {MatProgressBarModule} from '@angular/material/progress-bar';
import {RegistrationFolderConnectionCheckComponent} from './registration-folder-connection-check/registration-folder-connection-check.component';
import {ActivitiesModule} from '../activities/activities.module';
import {RegistrationFolderCardAttendeeComponent} from './registration-folder-card-attendee/registration-folder-card-attendee.component';
import {RegistrationFolderControlComponent} from './registration-folder-control/registration-folder-control.component';
import {AttendeeModule} from '../attendee/attendee.module';
import {RegistrationFolderPaidDialogComponent} from './dialogs/registration-folder-paid-dialog/registration-folder-paid-dialog.component';
import { RegistrationFolderUpdateDialogComponent } from './dialogs/registration-folder-update-dialog/registration-folder-update-dialog.component';

@NgModule({
    declarations: [
        RegistrationFolderCardComponent,
        RegistrationFolderTableComponent,
        RegistrationFolderCreateComponent,
        RegistrationFolderCancelRefuseDialogComponent,
        RegistrationFolderConnectionCheckComponent,
        RegistrationFolderInTrainingDialogComponent,
        RegistrationFolderServiceDoneDialogComponent,
        RegistrationFolderTerminateDialogComponent,
        RegistrationFolderValidateDialogComponent,
        RegistrationFolderStateChangeComponent,
        RegistrationFolderMenuComponent,
        RegistrationFolderToBillDialogComponent,
        RegistrationFolderAdvancedSearchComponent,
        RegistrationFolderCardAttendeeComponent,
        RegistrationFolderControlComponent,
        RegistrationFolderPaidDialogComponent,
        RegistrationFolderUpdateDialogComponent
    ],
    imports: [
        CommonModule,
        MaterialModule,
        MarkdownModule,
        TranslateModule,
        ReactiveFormsModule,
        TreoMessageModule,
        DndModule,
        FileModule,
        MatCardModule,
        PipesModule,
        MatTooltipModule,
        NgxMatSelectSearchModule,
        MatSelectInfiniteScrollModule,
        MatProgressBarModule,
        ActivitiesModule,
        AttendeeModule
    ],
    exports: [
        RegistrationFolderMenuComponent,
        RegistrationFolderCardComponent,
        RegistrationFolderTableComponent,
        RegistrationFolderCreateComponent,
        RegistrationFolderAdvancedSearchComponent,
        RegistrationFolderConnectionCheckComponent,
        RegistrationFolderCardAttendeeComponent
    ],
    providers: [
        DatePipe
    ]
})
export class RegistrationFolderModule {
}
