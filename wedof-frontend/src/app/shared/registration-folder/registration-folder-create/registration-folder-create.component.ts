import {Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy, OnInit, Output, ViewChild} from '@angular/core';
import {SessionService} from '../../api/services/session.service';
import {Session} from '../../api/models/session';
import {FormBuilder, FormGroup, FormGroupDirective, Validators} from '@angular/forms';
import {map, takeUntil} from 'rxjs/operators';
import {Observable, of, Subject} from 'rxjs';
import {RegistrationFolderService} from '../../api/services/registration-folder.service';
import {Attendee} from '../../api/models/attendee';
import {AttendeeService} from '../../api/services/attendee.service';
import {MatDialog} from '@angular/material/dialog';
import {AttendeeCreateComponent} from '../../attendee/attendee-create/attendee-create.component';
import {RegistrationFolder} from '../../api/models/registration-folder';
import {CanDeactivateComponent} from '../../utils/can-deactivate/can-deactivate.component';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../errors/errors.types';
import {Organism} from '../../api/models/organism';
import {OrganismService} from '../../api/services/organism.service';
import {TranslateService} from '@ngx-translate/core';
import {DatePipe} from '@angular/common';
import {SearchMethod} from '../../material/infinite-scroll/infinite-scroll.component';
import moment from 'moment';
import RegionCodes from '../../../../assets/countriesAndCitiesList/regionCode.json';
import {DataProviders} from '../../api/models/connection';
import {Select} from '@ngxs/store';
import {OrganismState} from '../../api/state/organism.state';
import {AppFormFieldData} from '../../material/app-form-field/app-form-field.component';
import {EntityClass} from '../../utils/enums/entity-class';

@Component({
    selector: 'app-registration-folder-create',
    templateUrl: './registration-folder-create.component.html',
    styleUrls: ['./registration-folder-create.component.scss']
})
export class RegistrationFolderCreateComponent extends CanDeactivateComponent implements OnDestroy, OnInit {

    private _unsubscribeAll = new Subject<void>();
    regionCodes: typeof RegionCodes;

    loading = false;
    errorMessages: string[] = [];

    vatOrganism: number;
    today: Date = new Date();
    oneMonthBefore: Date;
    dataProviders = DataProviders;

    formGroup: FormGroup;
    appFormFieldsData: AppFormFieldData[];
    newCreatedAttendee: Attendee;

    resultFormatterOrganism: any;
    searchMethodOrganism: SearchMethod;

    resultFormatterSession: any;
    searchMethodSession: SearchMethod;

    @Output() registrationFolderCreate = new EventEmitter<RegistrationFolder>();
    @ViewChild('form') form: FormGroupDirective;
    @Select(OrganismState.organism) organism$: Observable<Organism>;

    constructor(
        private _sessionService: SessionService,
        private _formBuilder: FormBuilder,
        private _organismService: OrganismService,
        private _registrationFolderService: RegistrationFolderService,
        private _attendeeService: AttendeeService,
        private _dialog: MatDialog,
        private _translateService: TranslateService,
        private _datePipe: DatePipe
    ) {
        super();
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    ngOnInit(): void {
        this.regionCodes = RegionCodes;
        this.resultFormatterOrganism = (organism: Organism) => organism.name + ' (...' + organism.siret.substr(organism.siret.length - 5) + ')';
        this.searchMethodOrganism = (params: any) => params.query?.length >= 2 ? this._organismService.find(params) : this._organismService.findInPartnershipWith(params);

        this.resultFormatterSession = (session: Session) => this._translateService.instant('private.training-organism.folders.createFolder.form.session.sessionResult', {
            startDate: this.isDateDefined(session.startDate),
            endDate: this.isDateDefined(session.endDate),
            city: session.sessionInfo.city,
            trainingTitle: session.sessionInfo.trainingTitle
        });
        this.oneMonthBefore = moment(this.today).subtract(1, 'month').toDate();

        this.searchMethodSession = (params: any) => this._sessionService.list(params).pipe(map((response) => {
            const newPayload = response.payload.filter((session) => {
                return !session.startDate || new Date(session.startDate) > this.oneMonthBefore;
            });
            return {...response, payload: newPayload};
        }));
        this.organism$.pipe(takeUntil(this._unsubscribeAll)).subscribe((organism) => {
            this.vatOrganism = organism.vat;
            this.initForm();
        });
    }

    initForm(): void {
        this.formGroup = this._formBuilder.group({
            formCreateFolder: this._formBuilder.group({})
        });

        const appFormFieldsData: AppFormFieldData[] = [
            {
                controlName: 'attendee',
                required: true,
                value: null,
                label: 'private.training-organism.folders.createFolder.form.attendee.attendee',
                type: 'search',
                placeholder: 'private.training-organism.folders.createFolder.form.attendee.label',
                searchNoEntriesFoundLabel: 'private.training-organism.folders.createFolder.form.attendee.noAttendee',
                searchMethod: (searchTerm) => {
                    return searchTerm ? this._attendeeService.find(searchTerm) : of([]);
                },
                searchResultFormatter: (attendee: Attendee) => attendee.firstName + ' ' + attendee.lastName,
                isCreateAvailable: true,
                showCreateText: 'private.training-organism.attendees.button',
                showSearchingText: 'private.certification.folders.createFolder.form.attendees.searching',
                openDialogCreate: (searchingValue?: string) => {
                    const dialogRef = this._dialog.open(AttendeeCreateComponent, {
                        disableClose: true,
                        panelClass: ['full-page-scroll-80'],
                        data: {
                            attendeeSearchValue: searchingValue,
                            newEmailCreateAttendee: this.newCreatedAttendee,
                            contextEntity: EntityClass.REGISTRATION_FOLDER
                        }
                    });
                    dialogRef.afterClosed().subscribe(res => {
                        if (res) {
                            this.newCreatedAttendee = res.data;
                            const attendeeFormControl = this.formGroup.get('formCreateFolder').get('attendee');
                            attendeeFormControl.setValue(this.newCreatedAttendee);
                            attendeeFormControl.markAsDirty();
                        }
                    });
                },
            },
            {
                controlName: 'session',
                label: 'private.training-organism.folders.createFolder.form.session.session',
                required: true,
                placeholder: 'private.training-organism.folders.createFolder.form.session.label',
                type: 'infiniteSearch',
                searchMethodPaginated: this.searchMethodSession,
                searchResultFormatter: this.resultFormatterSession,
                parameters: {sort: 'startDate', order: 'asc', state: 'published', limit: 50},
                searchNoEntriesFoundLabel: 'private.training-organism.folders.createFolder.form.session.noSession',
                error: 'private.training-organism.folders.createFolder.form.errors.session',
                change: (controlName, newValue, formData) => {
                    const appFormFieldSession = formData.find(field => field.controlName === 'session');
                    const appFormFieldTotalTTC = formData.find(field => field.controlName === 'totalTTC');
                    const appFormFieldTotalHT = formData.find(field => field.controlName === 'totalHT');
                    appFormFieldSession.value = newValue;
                    const totalTTC = appFormFieldSession.value.sessionInfo.totalTvaTTC;

                    appFormFieldTotalTTC.value = totalTTC;
                    appFormFieldTotalHT.value = this.convertTtcToHt(totalTTC);

                    return [appFormFieldTotalTTC, appFormFieldTotalHT, appFormFieldSession];
                }
            },
            {
                controlName: 'totalTTC',
                label: 'private.training-organism.folders.createFolder.form.tarif.ttc.tarif',
                type: 'number',
                validators: [Validators.min(0)],
                value: null,
                icon: 'euro_symbol',
                help: 'private.training-organism.folders.createFolder.form.tarif.tooltip.ttc',
                placeholder: 'private.training-organism.folders.createFolder.form.tarif.ttc.placeholderTarif',
                required: true,
                colSpan: 3,
                change: (controlName, newValue, formData) => {
                    const appFormFieldTotalTTC = formData.find(field => field.controlName === 'totalTTC');
                    const appFormFieldTotalHT = formData.find(field => field.controlName === 'totalHT');
                    appFormFieldTotalTTC.value = newValue;
                    appFormFieldTotalHT.value = this.convertTtcToHt(appFormFieldTotalTTC.value);
                    return [appFormFieldTotalTTC, appFormFieldTotalHT];
                },
            },
            {
                controlName: 'totalHT',
                label: 'private.training-organism.folders.createFolder.form.tarif.ht.tarif',
                type: 'number',
                value: null,
                placeholder: 'private.training-organism.folders.createFolder.form.tarif.ht.placeholderTarif',
                required: false,
                removed: this.vatOrganism === null,
                icon: 'euro_symbol',
                helpIcon: '',
                colSpan: 3,
                change: (controlName, newValue, formData) => {
                    const appFormFieldTotalTTC = formData.find(field => field.controlName === 'totalTTC');
                    const appFormFieldTotalHT = formData.find(field => field.controlName === 'totalHT');
                    appFormFieldTotalHT.value = newValue;
                    appFormFieldTotalTTC.value = this.convertHtToTtc(appFormFieldTotalHT.value);
                    return [appFormFieldTotalTTC, appFormFieldTotalHT];
                },
            },
            {
                controlName: 'noTva',
                label: 'private.training-organism.folders.createFolder.form.tarif.ht.tarif',
                type: 'url',
                disabled: true,
                icon: 'link',
                href: '/profil/organisme',
                value: this._translateService.instant('private.training-organism.folders.createFolder.form.tarif.noTva'),
                removed: this.vatOrganism !== null,
                colSpan: 3
            },
            {
                controlName: 'type',
                label: 'private.training-organism.folders.createFolder.form.type.type',
                type: 'radio',
                required: true,
                value: this.dataProviders.INDIVIDUAL,
                choices: [
                    {
                        key: this._translateService.instant('private.training-organism.folders.createFolder.form.type.individual'),
                        value: this.dataProviders.INDIVIDUAL
                    },
                    {
                        key: this._translateService.instant('private.training-organism.folders.createFolder.form.type.polemploi'),
                        value: this.dataProviders.POLE_EMPLOI
                    },
                    {
                        key: this._translateService.instant('private.training-organism.folders.createFolder.form.type.company'),
                        value: this.dataProviders.COMPANY
                    },
                    {
                        key: this._translateService.instant('private.training-organism.folders.createFolder.form.type.opco'),
                        value: this.dataProviders.OPCO
                    },
                ],
                change: (controlName, newValue, formData) => {
                    const appFormFieldType = formData.find(field => field.controlName === 'type');
                    const appFormFieldPoleEmploiDevis = formData.find(field => field.controlName === 'poleEmploiDevis');
                    const appFormFieldPoleEmploiId = formData.find(field => field.controlName === 'poleEmploiId');
                    const appFormFieldPoleEmploiRegionCode = formData.find(field => field.controlName === 'poleEmploiRegionCode');

                    appFormFieldType.value = newValue;
                    if (appFormFieldType.value === this.dataProviders.POLE_EMPLOI) {
                        appFormFieldPoleEmploiDevis.removed = false;
                        appFormFieldPoleEmploiId.removed = false;
                        appFormFieldPoleEmploiRegionCode.removed = false;
                    } else {
                        appFormFieldPoleEmploiDevis.removed = true;
                        appFormFieldPoleEmploiId.removed = true;
                        appFormFieldPoleEmploiRegionCode.removed = true;
                    }
                    return [appFormFieldPoleEmploiDevis, appFormFieldPoleEmploiId, appFormFieldPoleEmploiRegionCode, appFormFieldType];
                },
            },
            {
                controlName: 'poleEmploiDevis',
                label: 'private.training-organism.folders.createFolder.form.poleEmploi.poleEmploiDevis',
                placeholder: 'private.training-organism.folders.createFolder.form.poleEmploi.poleEmploiDevis',
                removed: true,
                required: true,
                type: 'text',
                colSpan: 2
            },
            {
                controlName: 'poleEmploiId',
                label: 'private.training-organism.folders.createFolder.form.poleEmploi.poleEmploiId',
                placeholder: 'private.training-organism.folders.createFolder.form.poleEmploi.poleEmploiId',
                removed: true,
                required: true,
                type: 'text',
                colSpan: 2
            },
            {
                controlName: 'poleEmploiRegionCode',
                label: 'private.training-organism.folders.createFolder.form.poleEmploi.poleEmploiRegionCode',
                required: true,
                removed: true,
                type: 'select',
                choices: Object.values(this.regionCodes).map((regionCode) => ({
                    key: regionCode.name,
                    value: regionCode.code
                })),
                colSpan: 2
            },
            {
                controlName: 'inPartnershipWith',
                label: 'private.training-organism.folders.createFolder.form.partnership.label',
                required: false,
                placeholder: 'private.training-organism.folders.createFolder.form.partnership.placeholder',
                type: 'infiniteSearch',
                searchMethodPaginated: this.searchMethodOrganism,
                searchResultFormatter: this.resultFormatterOrganism,
                help: 'private.training-organism.folders.createFolder.form.partnership.toolTip',
                parameters: {sort: 'name', order: 'asc', limit: 20},
                searchNoEntriesFoundLabel: 'private.training-organism.folders.createFolder.form.partnership.noOrganismFound',
            },
        ];
        this.appFormFieldsData = appFormFieldsData.filter(value => value != null);
    }

    convertTtcToHt(num: number): number {
        let priceHT: number;
        if (this.vatOrganism === 0) {
            priceHT = num;
        } else if (this.vatOrganism === 20) {
            const convertNumber = num / 1.2;
            priceHT = Math.round(convertNumber * 100) / 100;
        } else if (this.vatOrganism === 5.5) {
            const convertNumber = (num * 100) / 1.055;
            priceHT = Math.round(convertNumber) / 100;
        } else {
            priceHT = 0;
        }
        const formValue = this.formGroup.getRawValue().formCreateFolder;
        return formValue.totalHT !== priceHT ? priceHT : formValue.totalHT;
    }

    convertHtToTtc(num: number): number {
        let priceTTC: number;
        if (this.vatOrganism === 0) {
            priceTTC = num;
        } else if (this.vatOrganism === 20) {
            const convertNumber = num * 1.2;
            priceTTC = Math.round(convertNumber * 100) / 100;
        } else if (this.vatOrganism === 5.5) {
            const convertNumber = (num * 100) * 1.055;
            priceTTC = Math.round(convertNumber) / 100;
        }
        const formValue = this.formGroup.getRawValue().formCreateFolder;
        return formValue.totalTTC !== priceTTC ? priceTTC : formValue.totalTTC;
    }

    disabled(): boolean {
        const formValue = this.formGroup.getRawValue().formCreateFolder;
        if (formValue.type === DataProviders.POLE_EMPLOI &&
            (formValue.poleEmploiDevis === '' ||
                formValue.poleEmploiId === '' ||
                formValue.poleEmploiRegionCode === ''
            )) {
            return true;
        }
        return this.loading || !this.formGroup.dirty || this.formGroup.invalid;
    }

    submit(): void {
        this.loading = true;
        this.errorMessages = [];
        const formValue = this.formGroup.getRawValue().formCreateFolder;

        if (this.formGroup.valid) {
            this._registrationFolderService.create(
                formValue.type === DataProviders.POLE_EMPLOI ? {
                    attendeeId: formValue.attendee.id,
                    sessionId: formValue.session.id,
                    type: formValue.type,
                    totalTTC: formValue.totalTTC,
                    poleEmploiId: formValue.poleEmploiId,
                    poleEmploiRegionCode: formValue.poleEmploiRegionCode,
                    poleEmploiDevis: formValue.poleEmploiDevis,
                    inPartnershipWith: formValue.inPartnershipWith ? formValue.inPartnershipWith.siret : null
                } : {
                    attendeeId: formValue.attendee.id,
                    sessionId: formValue.session.id,
                    type: formValue.type,
                    totalTTC: formValue.totalTTC,
                    inPartnershipWith: formValue.inPartnershipWith ? formValue.inPartnershipWith.siret : null
                }).subscribe({
                    next: (registrationFolder) => {
                        this.loading = false;
                        this.registrationFolderCreate.emit(registrationFolder);
                    },
                    error: (httpErrorResponse: HttpErrorResponse) => {
                        this.loading = false;
                        this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
                    }
                }
            );
        }
    }

    isDateDefined(date: string): string {
        return date ? this._datePipe.transform(date, 'mediumDate') : this._translateService.instant('private.common.form.placeholder');
    }

    canDeactivate(): boolean {
        return !this.loading && (this.form?.submitted || !this.formGroup.dirty);
    }
}
