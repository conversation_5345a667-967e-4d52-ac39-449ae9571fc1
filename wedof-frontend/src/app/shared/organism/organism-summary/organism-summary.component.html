<div class="flex flex-row pt-5">
    <mat-icon title="Numéro de Siret" svgIcon="dialpad"></mat-icon>
    <p class="pl-8 self-center">
        {{ "private.common.organism.siret" | translate: organism }}
    </p>
</div>

<div class="flex flex-row pt-5" *ngIf="organism.address && organism.postalCode && organism.city">
    <mat-icon title="Adresse" svgIcon="room"></mat-icon>
    <p class="pl-8 self-center">
        {{ organism.address | titlecase }}, {{ organism.postalCode }},
        {{ organism.city | titlecase }}
    </p>
</div>

<div class="pt-5 flex flex-row" *ngIf="organism?.emails?.length">
    <mat-icon title="Email" class="self-center" svgIcon="mail"></mat-icon>
    <div class="flex flex-col">
        <mat-list *ngFor="let email of organism.emails">
            <a href="mailto:{{ email }}" class="pl-8 self-center">{{ email }}</a>
        </mat-list>
    </div>
</div>

<div class="pt-5 flex flex-row" *ngIf="organism?.phones?.length">
    <mat-icon title="Téléphone" class="self-center" svgIcon="phone"></mat-icon>
    <div class="flex flex-col">
        <mat-list *ngFor="let phone of organism.phones">
            <p class="pl-8 self-end">{{ phone }}</p>
        </mat-list>
    </div>
</div>

<div class="pt-5 flex flex-row" *ngIf="organism?.urls?.length">
    <mat-icon title="Site internet" class="self-center" svgIcon="link"></mat-icon>
    <div class="flex flex-col">
        <mat-list *ngFor="let url of organism.urls">
            <a href="{{ url }}" class="pl-8 self-end underline">{{ url }}</a>
        </mat-list>
    </div>
</div>

<div class="pt-5 flex flex-row">
    <mat-icon title="Evaluation moyenne" class="self-center" svgIcon="star"></mat-icon>
    <mat-spinner *ngIf="isLoading; else showEvaluation" class="flex flex-col pl-8 self-end bg-white">
    </mat-spinner>
    <ng-template #showEvaluation>
        <p class="flex flex-col pl-8 self-end" *ngIf="shortEvaluation.averageRating; else noRating">
            {{ shortEvaluation.averageRating | number : '1.2-2' }} / 5 pour {{ shortEvaluation.reviewCount }} avis
        </p>
    </ng-template>
    <ng-template #noRating>
        <p class="flex flex-col pl-8 self-end">
            {{ 'private.common.evaluation.noEvaluation' | translate }}
        </p>
    </ng-template>
</div>
