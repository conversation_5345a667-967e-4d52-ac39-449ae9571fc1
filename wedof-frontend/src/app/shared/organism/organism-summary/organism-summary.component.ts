import {Component, Input, OnChanges, SimpleChanges} from '@angular/core';
import {Organism} from '../../api/models/organism';
import {Certification} from '../../api/models/certification';
import {ShortEvaluation} from '../../api/models/evaluation';
import {StatisticService} from '../../api/services/statistic.service';

@Component({
    selector: 'app-organism-summary',
    templateUrl: './organism-summary.component.html',
    styleUrls: ['./organism-summary.component.scss']
})
export class OrganismSummaryComponent implements OnChanges {
    @Input() organism: Organism;
    @Input() certification: Certification;

    shortEvaluation: ShortEvaluation;
    isLoading: boolean;

    constructor(private _statisticService: StatisticService) {
        this.isLoading = true;
    }

    ngOnChanges(changes: SimpleChanges): void {
        this.isLoading = true;
        // TODO
        if (changes.organism.currentValue !== changes.organism.previousValue) {
            this._statisticService.getShortEvaluationForOrganismAndCertification(changes.organism.currentValue, this.certification).subscribe(
                (shortEvaluation) => {
                    this.shortEvaluation = shortEvaluation;
                },
                () => {
                },
                () => {
                    this.isLoading = false;
                }
            );
        }
    }
}
