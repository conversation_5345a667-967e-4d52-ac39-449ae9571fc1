import {CommonModule} from '@angular/common';
import {NgModule} from '@angular/core';
import {TranslateModule} from '@ngx-translate/core';
import {MarkdownModule} from 'ngx-markdown';

import {MaterialModule} from '../material/material.module';
import {OrganismSummaryComponent} from './organism-summary/organism-summary.component';
import {CertificationFolderModule} from '../certification-folder/certification-folder.module';
import {RouterModule} from '@angular/router';
import {OrganismCardComponent} from './organism-card/organism-card.component';
import {MatCardModule} from '@angular/material/card';
import {ReactiveFormsModule} from '@angular/forms';

@NgModule({
    declarations: [
        OrganismCardComponent,
        OrganismSummaryComponent
    ],
    imports: [
        CommonModule,
        MaterialModule,
        TranslateModule,
        MarkdownModule,
        CertificationFolderModule,
        RouterModule,
        MatCardModule,
        ReactiveFormsModule
    ],
    exports: [
        OrganismCardComponent,
        OrganismSummaryComponent
    ]
})
export class OrganismModule {
}
