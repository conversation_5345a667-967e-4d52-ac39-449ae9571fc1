import {Component, ElementRef, Input, OnC<PERSON><PERSON>, OnDestroy, SimpleChanges, TemplateRef, ViewChild} from '@angular/core';
import {FormGroup} from '@angular/forms';
import {Organism} from '../../api/models/organism';
import {BaseCardComponentDirective, RequiredCallSuper} from '../../utils/base-card/base-card.directive';
import {ThemePalette} from '@angular/material/core';

@Component({
    selector: 'app-organism-card',
    templateUrl: './organism-card.component.html',
    styleUrls: ['./organism-card.component.scss']
})
export class OrganismCardComponent extends BaseCardComponentDirective implements OnChanges, OnDestroy {
    static COMPONENT_ID = 'organisme';
    cardLoading = true;
    panelOpenState = false;
    formGroup: FormGroup;
    hasQualiopi = false;
    today: Date = new Date();
    qualiopiDatas: { title: string, color: ThemePalette, icon: string; } [] = [];
    @Input() organism: Organism;

    @ViewChild('copySuccess') copySuccessTemplate: TemplateRef<any>;


    constructor(
        private _el: ElementRef
    ) {
        super(OrganismCardComponent.COMPONENT_ID, _el);
    }

    ngOnChanges(changes: SimpleChanges): void {
        this.panelLoading();
        this.initForm(this.organism);
    }


    ngOnDestroy(): RequiredCallSuper {
        return super.ngOnDestroy();
    }

    openPanel(): void {
        this.panelOpenState = true;
    }

    closePanel(): void {
        this.panelOpenState = false;
    }

    protected initForm(organism: Organism): void {
        if (!organism) {
            return;
        }
        this.hasQualiopi = organism.qualiopiVAE || organism.qualiopiFormationApprentissage || organism.qualiopiBilanCompetences || organism.qualiopiTrainingAction;
        if (this.hasQualiopi) {
            this.qualiopiDatas = [
                {
                    title: 'private.common.organism.qualiopi.trainingAction',
                    color: organism.qualiopiTrainingAction ? 'primary' : 'warn',
                    icon: organism.qualiopiTrainingAction ? 'check_circle' : 'cancel',
                },
                {
                    title: 'private.common.organism.qualiopi.bilanCompetences',
                    color: organism.qualiopiBilanCompetences ? 'primary' : 'warn',
                    icon: organism.qualiopiBilanCompetences ? 'check_circle' : 'cancel',
                },
                {
                    title: 'private.common.organism.qualiopi.vae',
                    color: organism.qualiopiVAE ? 'primary' : 'warn',
                    icon: organism.qualiopiVAE ? 'check_circle' : 'cancel',
                },
                {
                    title: 'private.common.organism.qualiopi.formationApprentissage',
                    color: organism.qualiopiFormationApprentissage ? 'primary' : 'warn',
                    icon: organism.qualiopiFormationApprentissage ? 'check_circle' : 'cancel',
                }
            ];
        }

        this.panelLoaded();
    }
}
