<mat-card class="flex-col mt-3 p-5 pb-0 pt-3 border shadow-none sm" [ngClass]="{'card-loading':cardLoading}">
    <div class="flex justify-between mb-2">
        <div class="flex items-center mb-2">
            <mat-icon color="primary" class="mr-3 card-loading-show text-4xl">business</mat-icon>
            <div>
                <div class="text-xl font-semibold card-loading-show">{{organism?.name}}</div>
                <div class="text-secondary text-md card-loading-show">
                <span *ngIf="organism?.isCertifierOrganism && organism?.isTrainingOrganism">
                    {{ 'private.common.organism.certifierAndTraining' | translate}}
                </span>
                    <span *ngIf="!organism?.isCertifierOrganism && organism?.isTrainingOrganism">
                    {{ 'private.common.organism.training' | translate}}
                </span>
                    <span *ngIf="organism?.isCertifierOrganism && !organism?.isTrainingOrganism">
                    {{ 'private.common.organism.certifier' | translate}}
                </span>
                </div>
            </div>
        </div>
        <img class="image cursor-pointer" *ngIf="hasQualiopi" (click)="openPanel()"
             src="assets/images/qualiopi/qualiopi.png" style="width:150px">
    </div>
    <treo-message *ngIf="organism?.isNonDiffusible"
                  type="warning"
                  [showIcon]="false"
                  class="my-2 card-loading-show" appearance="outline">
        <app-form-field-static icon="info" type="text"
                               [value]="'private.common.organism.nondiffusible' | translate">
        </app-form-field-static>
    </treo-message>
    <div class="flex flex-col" *ngIf="!panelOpenState || cardLoading">
        <app-form-field-static [value]="organism?.siret" type="text" class="pb-1" [copy]="true"
                               icon="fact_check"></app-form-field-static>
        <app-form-field-static [value]="organism?.phones[0]" type="tel" class="pb-1" [copy]="true"
                               icon="phone"></app-form-field-static>
        <app-form-field-static [value]="organism?.emails[0]" type="email" class="pb-1" [copy]="true"
                               icon="email"></app-form-field-static>
        <button *ngIf="hasQualiopi" class="flex justify-center mt-2 -mx-5 pt-2 pb-2 open-panel card-loading-hidden" (click)="openPanel()">
            <mat-icon svgIcon="keyboard_arrow_down"></mat-icon>
        </button>
    </div>

    <div class="flex flex-col" *ngIf="panelOpenState && !cardLoading && hasQualiopi">
        <app-form-field-static [value]="organism?.siret" type="text" class="pb-1"
                               icon="fact_check"></app-form-field-static>
        <app-form-field-static [value]="organism?.phones[0]" type="tel" class="pb-1" [copy]="true"
                               icon="phone"></app-form-field-static>
        <app-form-field-static [value]="organism?.emails[0]" type="email" class="pb-1" [copy]="true"
                               icon="email"></app-form-field-static>
        <table class="w-full mt-2 mb-2 table">
            <tbody>
            <tr *ngFor="let qualiopiData of qualiopiDatas">
                <td class="font-semibold">{{qualiopiData.title | translate}}
                </td>
                <td class="text-center">
                    <mat-icon [color]="qualiopiData.color">
                        {{qualiopiData.icon}}</mat-icon>
                </td>
            </tr>
            </tbody>
        </table>
        <p class="text-secondary text-sm mb-2">{{'private.common.organism.qualiopi.subtitle' | translate : {today: today | date: 'dd/MM/yyyy'} }}</p>
        <button class="flex justify-center -mx-5 pt-2 pb-2 close-panel" (click)="closePanel()">
            <mat-icon svgIcon="keyboard_arrow_up"></mat-icon>
        </button>
    </div>
</mat-card>
