import {Component, Input, OnChanges} from '@angular/core';
import {
    CERTIFICATE_FILE_TYPE_ID,
    DocumentGenerationStates,
    File,
    FILE_TYPE_LINK,
    FileHolder,
    FileSlot,
    FileType,
    FREE_FILE_TYPE_NAME
} from '../../api/models/file';
import {ViewFileDialogComponent} from '../dialogs/view-file-dialog/view-file-dialog.component';
import {sortBy} from 'lodash-es';
import {MatDialog} from '@angular/material/dialog';
import {removeAfterLastDot} from '../../utils/string-utils';
import {FileService} from '../../api/services/file.service';
import {APIEndpoint} from '../../api/services/api-endpoint.enum';

@Component({
    selector: 'app-file-list',
    templateUrl: './file-list.component.html',
    styleUrls: ['./file-list.component.scss']
})
export class FileListComponent<T extends FileHolder<S>, S> implements OnChanges {

    fileSlots: FileSlot<S>[] = [];
    filesUpdated = [];

    @Input() entity: T;
    @Input() entityApiPath: string;
    @Input() entityIdProperty: string;
    @Input() titleStateChange: string;
    @Input() fileTypes: FileType<S>[];
    @Input() orderedStates: S[];
    @Input() subtitle?: string;
    @Input() showManageFile ? = false;
    @Input() isOwner ? = false;
    @Input() removeVisibilityForPartner ? = false;
    @Input() manageFile ? = true;
    @Input() linkManageFile?: string;
    @Input() getSecondaryText?: (fileType: FileType<T>) => string;
    @Input() entityParentId?: string;

    constructor(private _dialog: MatDialog, private _fileService: FileService) {
    }

    ngOnChanges(): void {
        this.refreshFileSlots();
    }

    onFilesChanged(): void {
        this.refreshFileSlots();
    }

    onFileSelected(file: File): void {
        this.filesUpdated = [];
        const manageFile = file.typeId === CERTIFICATE_FILE_TYPE_ID && file.generationState !== DocumentGenerationStates.NOT_GENERATED ? false : this.manageFile;
        const dialogRef = this._dialog.open(ViewFileDialogComponent, {
            width: '80%',
            height: '90%',
            data: {
                file: file,
                auth: true,
                title: ((_file: File) => {
                    const fileType = this.fileTypes.find(currentFileType => currentFileType.id === _file.typeId);
                    return (fileType ? (fileType.name + ' - ') : '') + _file.fileName;
                }),
                entityApiPath: this.entityApiPath,
                src: (_file: File) => '/api/' + this.entityApiPath + '/' + this.entity[this.entityIdProperty] + '/files/' + _file.id,
                files: sortBy(this.entity.files, _file => _file.typeId),
                isOwner: this.isOwner,
                manageFile: manageFile,
                canUpdateFileState: ((_file: File) => {
                    return _file.generationState === DocumentGenerationStates.NOT_GENERATED;
                }),
                updateFile: (fileId, body) => {
                    this.filesUpdated.push({
                        fileId: fileId,
                        state: body['state'] ?? file.state,
                        comment: body['comment'] ?? file.comment
                    });
                    return this._fileService.updateState(this.entityApiPath, this.entity[this.entityIdProperty], fileId, {
                        state: body['state'] ?? file.state,
                        comment: body['comment']
                    });
                }
            }
        });
        dialogRef.afterClosed().subscribe(res => {
            if (this.filesUpdated.length) {
                this.filesUpdated.forEach((fileUpdated) => {
                    return {...this.entity.files.find((entityFile) => entityFile.id === fileUpdated.fileId), ...fileUpdated};
                });
            }
        });
    }

    hasFile(fileType: FileType<S>): boolean {
        return this.entity.files?.find(file => file.typeId === fileType.id) != null;
    }

    isAllDocumentsCompleted(): boolean {
        return this.fileTypes?.every(fileType => {
            return fileType.allowMultiple || this.hasFile(fileType);
        }) ?? false;
    }

    isRequiredDocumentsMissingForNextState(entity: FileHolder<S>): boolean {
        let missingRequiredFiles = [];
        const indexOfCurrentState = this.orderedStates.indexOf(entity.state);
        const indexOfNextState = indexOfCurrentState + 1;
        const filesTypeUploaded = entity.files != null ? entity.files.map(file => file.typeId) : [];
        const notLastState = indexOfNextState < this.orderedStates.length;
        if (this.fileTypes && notLastState) {
            missingRequiredFiles = this.fileTypes?.filter(fileType => {
                const nextState = this.orderedStates[indexOfNextState];
                return !fileType.generated && fileType.toState === nextState && !filesTypeUploaded.includes(fileType.id);
            });
        }
        return missingRequiredFiles?.length > 0;
    }

    refreshFileSlots(): void {
        const predefinedTypes = this.fileTypes ?? [];
        const fileSlots: FileSlot<S>[] = [];
        this.entity.files?.forEach(file => {
            let fileType = predefinedTypes.find(type => type.id === file.typeId);
            if (!fileType) {
                fileType = {
                    name: removeAfterLastDot(file.fileName),
                    id: file.typeId,
                    toState: null,
                    accept: '',
                    description: null,
                    allowMultiple: false,
                    tags: [],
                    certifications: [],
                    // Default permission on old deleted FileTypes
                    allowUploadAttendee: false,
                    allowVisibilityAttendee: false,
                    allowSignAttendee: false,
                    allowUploadPartner: true,
                    allowVisibilityPartner: true,
                    allowSignPartner: false,
                    generated: false,
                    enabled: false,
                    templateFile: null,
                    allowRegenerate: false
                };
            }
            fileSlots.push({fileType, file});
        });
        const availableFileTypes = predefinedTypes.filter(fileType => {
            if (!this.isOwner && this.removeVisibilityForPartner) {
                // don't display the disabled button for add a document as the partner would not be authorized to upload a document on certification
                return !fileType.allowMultiple;
            } else if (this.hasFile(fileType)) {
                return fileType.allowMultiple;
            } else {
                return fileType.generated ? fileType.enabled : true;
            }
        });
        availableFileTypes.forEach(fileType => {
            fileSlots.push({fileType});
        });

        const fileSlotsFilter = fileSlots.filter((fileSlot) => {
            const fileTags = fileSlot.fileType.tags || [];
            const entityTags = this.entity['tags'] || [];
            const fileCertifications: any[] = fileSlot.fileType.certifications || [];
            const entityCertifications = this.entity['_links'].certification?.certifInfo ? [this.entity['_links'].certification.certifInfo] : [];

            if (fileSlot.file) {
                return true;
            }

            // Il y a des tags et une certification
            if (fileTags.length > 0 && entityTags.length > 0
                && fileCertifications.length > 0 && entityCertifications.length > 0) {
                return fileTags.some(tag => entityTags.includes(tag))
                    && fileCertifications.some(certification => entityCertifications.includes(certification));
            }
            // Pas de tags, mais il y a une certification
            if (fileTags.length <= 0 && fileCertifications.length > 0 && entityCertifications.length > 0) {
                return fileCertifications.some(certification => entityCertifications.includes(certification));
            }
            // Pas de certifications, mais il y a des tags
            if (fileCertifications.length <= 0 && fileTags.length > 0 && entityTags.length > 0) {
                return fileTags.some(tag => entityTags.includes(tag));
            }
            // Pas de tags et pas de certification, garder les fichiers sans tags et certifications
            return fileTags.length <= 0 && fileCertifications.length <= 0;
        });

        this.fileSlots = sortBy(fileSlotsFilter, fileSlot => {
            const fileType = fileSlot.fileType;
            if (fileType.id === CERTIFICATE_FILE_TYPE_ID) {
                return -1;
            } else if (fileType.allowMultiple && fileType.name === FREE_FILE_TYPE_NAME) {
                return 1000000 + fileType.id + (this.hasFile(fileType) ? 0 : 10000);
            } else {
                return fileType.id;
            }
        });
    }

    downloadFile(file: File): void {
        if (file.fileType !== FILE_TYPE_LINK) {
            this._fileService.download(this.entityApiPath, this.entity[this.entityIdProperty], file, null, APIEndpoint.API).subscribe();
        } else {
            window.open(file.link, '_blank');
        }
    }
}
