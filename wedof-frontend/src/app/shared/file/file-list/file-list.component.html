<div>
    <div class="flex flex-col">
        <div class="flex justify-between">
            <div class="flex items-center">
                <mat-icon [color]="isAllDocumentsCompleted() ? 'primary' : 'accent'"
                          class="mr-3">picture_as_pdf
                </mat-icon>
                <p class="text-lg font-medium">{{ ('private.common.files.title' | translate)  }}</p>
            </div>
            <a *ngIf="showManageFile && linkManageFile"
               [href]="linkManageFile"
               mat-icon-button>
                <mat-icon [matTooltip]="'private.common.certification.files.noData' | translate" color="text-gray">
                    settings
                </mat-icon>
            </a>
        </div>
        <p *ngIf="subtitle" class="text-secondary">{{subtitle | translate}}</p>
    </div>
    <div class="px-5 relative">
        <mat-divider></mat-divider>
    </div>
    <ng-template #noFileSlots>
        <div class="flex flex-auto flex-col mb-4 mt-3">
            <button type="button" class="bg-gray-200 m-auto w-1/2" mat-flat-button
                    *ngIf="showManageFile && linkManageFile; else showPartner">
                <a class="no-underline" [routerLink]="[linkManageFile]">
                    {{ 'private.common.certification.files.noData' | translate }}
                </a>
            </button>
            <ng-template #showPartner>
                <p class="text-secondary text-center">{{ 'private.common.files.noDataPartner' | translate }}</p>
            </ng-template>
        </div>
    </ng-template>
    <div class="flex flex-auto flex-col mb-4" *ngIf="entity && fileSlots?.length > 0; else noFileSlots">
        <mat-action-list>
            <app-file-list-slot *ngFor="let fileSlot of fileSlots"
                                [fileType]="fileSlot.fileType"
                                [fileSlot]="fileSlot"
                                [file]="fileSlot.file"
                                [orderedStates]="orderedStates"
                                [entity]="entity"
                                [isAttendee]="false"
                                [manageFile]="manageFile"
                                [isOwner]="showManageFile"
                                [isPartner]="!showManageFile"
                                [entityApiPath]="entityApiPath"
                                [entityIdProperty]="entityIdProperty"
                                [titleStateChange]="titleStateChange"
                                [secondaryText]="getSecondaryText ? getSecondaryText(fileSlot.fileType) : null"
                                [entityParentId]="entityParentId"
                                (fileDownload)="downloadFile($event)"
                                (fileSelected)="onFileSelected($event)"
                                (filesChanged)="onFilesChanged()">
            </app-file-list-slot>
        </mat-action-list>
    </div>
</div>
