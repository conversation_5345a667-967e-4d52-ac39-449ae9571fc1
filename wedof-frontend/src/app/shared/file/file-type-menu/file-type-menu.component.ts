import {Component, EventEmitter, Input, Output, ViewChild} from '@angular/core';
import {FormGroup} from '@angular/forms';
import {CERTIFICATE_FILE_TYPE_ID, FileType} from '../../api/models/file';
import {MatMenuTrigger} from '@angular/material/menu';

@Component({
    selector: 'app-file-type-menu',
    templateUrl: './file-type-menu.component.html',
    styleUrls: ['./file-type-menu.component.scss']
})
export class FileTypeMenuComponent<T> {

    @Input() withButton = true;
    @Input() formGroup?: FormGroup;
    @Input() fileType: FileType<T>;
    @Output() updateFileType?: EventEmitter<void> = new EventEmitter<void>();
    @Output() delete?: EventEmitter<FileType<T>> = new EventEmitter<FileType<T>>();
    @Output() openUpdateForm?: EventEmitter<FileType<T>> = new EventEmitter<FileType<T>>();
    @Output() downloadTemplateExemple?: EventEmitter<FileType<T>> = new EventEmitter<FileType<T>>();
    @Output() updateState?: EventEmitter<boolean> = new EventEmitter<boolean>();
    @Output() updateStateFromTable?: EventEmitter<{ fileTypeToUpdate: FileType<T>, enabled: boolean }> = new EventEmitter<{ fileTypeToUpdate: FileType<T>, enabled: boolean }>();
    @Input() loading: boolean;

    @ViewChild('menuTrigger') menuTrigger: MatMenuTrigger;

    constructor() {
    }

    update(): void {
        this.updateFileType.emit();
    }

    updateBodyOrAndState(enabled: boolean): void {
        this.updateState.emit(enabled);
    }

    updateFromTable(fileTypeToUpdate: FileType<T>, enabled: boolean): void {
        this.updateStateFromTable.emit({fileTypeToUpdate, enabled});
    }

    openForm(): void {
        this.openUpdateForm.emit(this.fileType);
    }

    deleteFileType(): void {
        this.delete.emit(this.fileType);
    }

    downloadExample(): void {
        this.downloadTemplateExemple.emit(this.fileType);
    }

    isFileTypeCertificate(): boolean {
        return this.fileType.id === CERTIFICATE_FILE_TYPE_ID;
    }

}
