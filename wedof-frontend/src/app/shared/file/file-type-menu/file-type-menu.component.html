<ng-container *ngIf="withButton; else showOtherMenu">
    <div class="flex align-center">
        <div *ngIf="!formGroup?.dirty && fileType?.generated && !isFileTypeCertificate()">
            <button *ngIf="!fileType.enabled"
                    type="button" mat-flat-button
                    color="primary"
                    [disabled]="loading"
                    class="flex justify-center items-center"
                    (click)="updateBodyOrAndState(true)">
                {{ 'common.actions.activate' | translate}}
            </button>
            <button *ngIf="fileType.enabled"
                    type="button" mat-flat-button
                    color="warn"
                    [disabled]="loading"
                    class="flex justify-center items-center"
                    (click)="updateBodyOrAndState(false)">
                {{ 'common.actions.deactivate' | translate}}
            </button>
        </div>
        <button *ngIf="!fileType.generated || formGroup?.dirty"
                class="flex justify-center items-center"
                [ngClass]="fileType.generated && !isFileTypeCertificate() ? 'button-actions' : ''"
                type="button" mat-flat-button
                color="primary"
                [disabled]="!formGroup?.dirty || loading || formGroup.invalid"
                (click)="update()">
            <mat-progress-spinner class="mr-4" *ngIf="loading" [diameter]="24"
                                  mode="indeterminate"></mat-progress-spinner>
            <ng-container
                *ngIf="!loading"> {{ 'common.actions.update' | translate}}
            </ng-container>
        </button>
        <button *ngIf="fileType.generated && formGroup?.dirty && !isFileTypeCertificate()"
                class="flex justify-center items-center button-arrow button-arrow-primary"
                (click)="$event.stopPropagation()"
                color="primary"
                mat-flat-button
                [disabled]="loading || formGroup.invalid"
                [matMenuTriggerFor]="actionsMenuUpdateState"
                title="Actions" type="button">
            <mat-icon class="icon" svgIcon="arrow_drop_down"></mat-icon>
        </button>
    </div>
    <mat-menu #actionsMenuUpdateState="matMenu">
        <ng-template matMenuContent>
            <button mat-menu-item (click)="updateBodyOrAndState(true)" *ngIf="!fileType.enabled">
                <mat-icon color="primary" svgIcon="check_circle"></mat-icon>
                <span>{{'common.actions.updateAnd' | translate}} {{ 'common.actions.activate' | translate}}</span>
            </button>
            <button mat-menu-item (click)="updateBodyOrAndState(false)" *ngIf="fileType.enabled">
                <mat-icon color="warn" svgIcon="cancel"></mat-icon>
                <span>{{'common.actions.updateAnd' | translate}} {{ 'common.actions.deactivate' | translate}}</span>
            </button>
        </ng-template>
    </mat-menu>
</ng-container>

<ng-template #showOtherMenu>
    <button type="button" mat-icon-button
            (click)="menuTrigger.openMenu(); $event.stopPropagation()"
            title="Actions">
        <mat-icon svgIcon="more_vert" *ngIf="!loading"></mat-icon>
        <mat-progress-spinner *ngIf="loading"
                              [diameter]="24"
                              [mode]="'indeterminate'"></mat-progress-spinner>
    </button>
    <button type="button"
            #menuTrigger="matMenuTrigger"
            [matMenuTriggerFor]="actionsMenu">
    </button>
    <mat-menu #actionsMenu="matMenu" class="large-menu">
        <ng-template matMenuContent>
            <ng-container *ngIf="fileType.generated && !isFileTypeCertificate()">
                <button type="button" mat-menu-item
                        (click)="downloadExample()">
                    <mat-icon color="primary" svgIcon="visibility"></mat-icon>
                    <span>{{ 'common.actions.file.preview' | translate }}</span>
                </button>
                <button type="button" mat-menu-item *ngIf="!fileType.enabled"
                        (click)="updateFromTable( this.fileType, true)">
                    <mat-icon color="primary" svgIcon="check_circle"></mat-icon>
                    <span>{{ 'common.actions.activate' | translate }}</span>
                </button>
            </ng-container>
            <button type="button" mat-menu-item (click)="openForm()">
                <mat-icon color="primary" svgIcon="edit"></mat-icon>
                <span>{{ 'common.actions.update' | translate}}</span>
            </button>
            <button *ngIf="fileType.generated && fileType.enabled && !isFileTypeCertificate()"
                    type="button" mat-menu-item
                    (click)="updateFromTable(this.fileType, false)">
                <mat-icon color="warn" svgIcon="cancel"></mat-icon>
                <span>{{ 'common.actions.deactivate' | translate }}</span>
            </button>
            <button *ngIf="!isFileTypeCertificate()" type="button" mat-menu-item
                    (click)="deleteFileType()">
                <mat-icon color="warn" svgIcon="delete"></mat-icon>
                <span>{{ 'common.actions.delete' | translate}}</span>
            </button>
        </ng-template>
    </mat-menu>
</ng-template>
