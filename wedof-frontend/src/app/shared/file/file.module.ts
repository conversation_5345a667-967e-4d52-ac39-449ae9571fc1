import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {TranslateModule} from '@ngx-translate/core';
import {MaterialModule} from '../material/material.module';

import {ReactiveFormsModule} from '@angular/forms';
import {TreoMessageModule} from '../../../@treo/components/message';
import {ViewFileDialogComponent} from './dialogs/view-file-dialog/view-file-dialog.component';
import {NgxExtendedPdfViewerModule} from 'ngx-extended-pdf-viewer';
import {AddFileLinkDialogComponent} from './dialogs/add-file-link-dialog/add-file-link-dialog.component';
import {FileMenuComponent} from './dialogs/file-menu/file-menu.component';
import {DialogFileTypeComponent} from './dialogs/dialog-file-type/dialog-file-type.component';
import {FileListComponent} from './file-list/file-list.component';
import {MatTooltipModule} from '@angular/material/tooltip';
import {FileTypeListComponent} from './file-type-list/file-type-list.component';
import {MatCardModule} from '@angular/material/card';
import {FileListAttendeeComponent} from './file-list-attendee/file-list-attendee.component';
import {ClipboardModule} from 'ngx-clipboard';
import {RouterModule} from '@angular/router';
import {FileListSlotComponent} from './file-list-slot/file-list-slot.component';
import {SignDocumentDialogComponent} from './dialogs/sign-document-dialog/sign-document-dialog.component';
import {CodeInputModule} from 'angular-code-input';
import {FileTypeMenuComponent} from './file-type-menu/file-type-menu.component';
import {FileIframeComponent} from './dialogs/file-iframe/file-iframe.component';
import {FileListPartnerPartnershipComponent} from './file-list-partner-partnership/file-list-partner-partnership.component';
import {DndModule} from '../directives/DndModule';

@NgModule({
    declarations: [
        ViewFileDialogComponent,
        AddFileLinkDialogComponent,
        DialogFileTypeComponent,
        FileMenuComponent,
        FileListComponent,
        FileListSlotComponent,
        FileTypeListComponent,
        FileListAttendeeComponent,
        SignDocumentDialogComponent,
        FileTypeMenuComponent,
        FileIframeComponent,
        FileListPartnerPartnershipComponent
    ],
    imports: [
        CommonModule,
        MaterialModule,
        TranslateModule,
        ReactiveFormsModule,
        TreoMessageModule,
        NgxExtendedPdfViewerModule,
        MatTooltipModule,
        MatCardModule,
        ClipboardModule,
        RouterModule,
        CodeInputModule,
        DndModule
    ],
    exports: [
        FileMenuComponent,
        FileListComponent,
        FileTypeListComponent,
        FileListAttendeeComponent,
        FileListPartnerPartnershipComponent
    ]
})
export class FileModule {
}
