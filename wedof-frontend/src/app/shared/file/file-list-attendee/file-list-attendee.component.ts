import {Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges} from '@angular/core';
import {
    CERTIFICATE_FILE_TYPE_ID,
    File,
    FILE_TYPE_LINK,
    FileHolder,
    FileSlot,
    FileType,
    FREE_FILE_TYPE_NAME
} from '../../api/models/file';
import {FileService} from '../../api/services/file.service';
import {MatDialog} from '@angular/material/dialog';
import {ViewFileDialogComponent} from '../dialogs/view-file-dialog/view-file-dialog.component';
import {APIEndpoint} from '../../api/services/api-endpoint.enum';
import {sortBy} from 'lodash-es';
import {CertificationFolderSurvey} from '../../api/models/certification-folder-survey';
import {CertificationFolderSurveyService} from '../../api/services/certification-folder-survey.service';
import {Certification} from '../../api/models/certification';
import {DisclaimerComponent} from '../../material/disclaimer/disclaimer.component';

@Component({
    selector: 'app-file-list-attendee',
    templateUrl: './file-list-attendee.component.html',
    styleUrls: ['./file-list-attendee.component.scss']
})
export class FileListAttendeeComponent<T extends FileHolder<S>, S> implements OnInit, OnChanges {

    fileSlots: FileSlot<S>[] = [];
    certificationFolderSurvey: CertificationFolderSurvey;

    @Input() entity: T;
    @Input() entityApiPath: string;
    @Input() entityIdProperty: string;
    @Input() fileTypes: FileType<S>[];
    @Input() typeFileId: number;
    @Input() orderedStates: S[];
    @Input() surveyHref?: string;
    @Input() certification?: Certification;
    @Input() shouldRefreshCertificateAccess?: boolean;
    @Output() openSurvey?: EventEmitter<any> = new EventEmitter();
    @Input() entityParentId: string;

    constructor(
        private _fileService: FileService,
        private _certificationFolderSurveyService: CertificationFolderSurveyService,
        private _dialog: MatDialog) {
    }

    ngOnInit(): void {
        if (this.surveyHref) {
            this.initCertificationFolderSurvey();
        }
        if (this.typeFileId) {
            const file = this.entity.files?.find(currentFile => currentFile.typeId === this.typeFileId);
            this.showFile(file);
        }
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes.shouldRefreshCertificateAccess) {
            this.initCertificationFolderSurvey();
        }
        this.refreshFileSlots();
    }

    initCertificationFolderSurvey(): void {
        this._certificationFolderSurveyService.getByUrl(this.surveyHref).subscribe((certificationFolderSurvey) => {
            this.certificationFolderSurvey = certificationFolderSurvey;
        });
    }

    onFilesChanged(): void {
        this.refreshFileSlots();
    }

    findFileTypeById(typeId): FileType<S> {
        return this.fileTypes?.find(fileType => fileType.id === typeId);
    }

    showFile(file: File): void {
        if (file.typeId !== CERTIFICATE_FILE_TYPE_ID || this.canAttendeeDownloadOrViewCertificate()) {
            this.viewFile(file);
        } else {
            this.showSurveyDialog(file, 'view');
        }
    }

    canAttendeeDownloadOrViewCertificate(): boolean {
        const hasInitialExperience = this.certificationFolderSurvey?.initialExperience != null;
        const hasSixMonthExperience = this.certificationFolderSurvey?.sixMonthExperience != null;
        const hasLongTermExperience = this.certificationFolderSurvey?.longTermExperience != null;
        const canAnswerSixMonthExperience = new Date() >= new Date(this.certificationFolderSurvey?.sixMonthExperienceStartDate);
        const canAnswerLongTermExperience = new Date() >= new Date(this.certificationFolderSurvey?.longTermExperienceStartDate);
        return hasInitialExperience &&
            (!canAnswerSixMonthExperience || hasSixMonthExperience) &&
            (!canAnswerLongTermExperience || hasLongTermExperience);
    }

    showSurveyDialog(file: File, action: 'view' | 'download'): void {
        this._dialog.open(DisclaimerComponent, {
            panelClass: ['full-page-scroll-30'],
            data: {
                title: 'common.actions.file.label.certificate',
                subtitle: 'private.common.files.candidate.' + (this.certification.surveyOptional ? 'surveyOptional' : 'surveyNotOptional'),
                mainButton: {
                    title: 'private.common.files.candidate.answerSurvey',
                    method: () => {
                        this.openSurvey.emit();
                    }
                },
                secondaryButton: this.certification.surveyOptional ? {
                    title: 'private.common.files.candidate.downloadCertificate',
                    method: () => {
                        if (action === 'view') {
                            this.viewFile(file);
                        } else {
                            this._fileService.download(this.entityApiPath, this.entity[this.entityIdProperty], file, null, APIEndpoint.APP).subscribe();
                        }
                    }
                } : null
            }
        });
    }

    viewFile(file: File): void {
        this._dialog.open(ViewFileDialogComponent, {
            width: '80%',
            height: '90%',
            data: {
                file: file,
                auth: true,
                title: ((_file: File) => this.findFileTypeById(_file.typeId).name + ' - ' + _file.fileName),
                src: (_file: File) => '/app/' + this.entityApiPath + '/' + this.entity[this.entityIdProperty] + '/files/' + _file.id,
                files: this.entity.files,
                updateFile: (fileId, body) => {
                    return this._fileService.updateState(this.entityApiPath, this.entity[this.entityIdProperty], fileId, {}, APIEndpoint.APP);
                }
            }
        });
    }

    downloadFile(file: File): void {
        if (file.fileType !== FILE_TYPE_LINK) {
            if (file.typeId !== CERTIFICATE_FILE_TYPE_ID || this.canAttendeeDownloadOrViewCertificate()) {
                this._fileService.download(this.entityApiPath, this.entity[this.entityIdProperty], file, null, APIEndpoint.APP).subscribe();
            } else {
                this.showSurveyDialog(file, 'download');
            }
        } else {
            window.open(file.link, '_blank');
        }
    }

    hasFile(fileType: FileType<S>): boolean {
        return this.entity.files?.find(file => file.typeId === fileType.id) != null;
    }

    refreshFileSlots(): void {
        const predefinedTypes = this.fileTypes ?? [];
        const fileSlots: FileSlot<S>[] = [];
        this.entity.files?.forEach(file => {
            const fileType = predefinedTypes.find(type => type.id === file.typeId);
            // only file with fileType should be visible for an attendee (has it needs a visibility permission)
            if (fileType) {
                fileSlots.push({fileType, file});
            }
        });
        const availableFileTypes = predefinedTypes.filter((fileType) => {
            if (fileType.allowUploadAttendee && !this.hasFile(fileType)) {
                fileSlots.push({fileType});
            } else if (fileType.generated && fileType.enabled && !this.hasFile(fileType)) {
                fileSlots.push({fileType});
            }
        });
        availableFileTypes.forEach(fileType => {
            fileSlots.push({fileType});
        });
        this.fileSlots = sortBy(fileSlots, fileSlot => {
            const fileType = fileSlot.fileType;
            if (fileType.id === CERTIFICATE_FILE_TYPE_ID) {
                return -1;
            } else if (fileType.allowMultiple && fileType.name === FREE_FILE_TYPE_NAME) {
                return 1000000 + fileType.id + (this.hasFile(fileType) ? 0 : 10000);
            } else {
                return fileType.id;
            }
        });
    }
}
