<mat-card class="flex-col mt-3 p-5 pb-0 pt-3 border shadow-none sm w-full">
    <div class="flex items-center mb-2">
        <mat-icon class="mr-3 card-loading-show text-4xl" color="primary">
            picture_as_pdf
        </mat-icon>
        <div class="text-xl font-semibold card-loading-show">
            {{ 'private.common.files.title' | translate }}
        </div>
    </div>
    <div class="flex flex-auto flex-col mb-2"
         *ngIf="entity && fileSlots?.length > 0; else noFile">
        <mat-action-list>
            <app-file-list-slot *ngFor="let fileSlot of fileSlots"
                                [fileSlot]="fileSlot"
                                [fileType]="fileSlot.fileType"
                                [file]="fileSlot.file"
                                [entity]="entity"
                                [entityApiPath]="entityApiPath"
                                [entityIdProperty]="entityIdProperty"
                                [isAttendee]="true"
                                [orderedStates]="orderedStates"
                                [isOwner]="false"
                                [isPartner]="false"
                                [entityParentId]="entityParentId"
                                (fileDownload)="downloadFile($event)"
                                (fileSelected)="showFile($event)"
                                (filesChanged)="onFilesChanged()">
            </app-file-list-slot>
        </mat-action-list>
    </div>
    <ng-template #noFile>
        <div class="flex flex-col mb-2">
            <p class="text-center text-disabled">{{'private.attendee.filesCard.nofile' | translate}}</p>
        </div>
    </ng-template>
</mat-card>
