import {Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild} from '@angular/core';
import {HttpErrorResponse, HttpParams, HttpResponse} from '@angular/common/http';
import {AddFileLinkDialogComponent} from '../dialogs/add-file-link-dialog/add-file-link-dialog.component';
import {GoogleDrivePickerService} from '../../api/services/google-drive-picker.service';
import {FileService} from '../../api/services/file.service';
import {MatDialog} from '@angular/material/dialog';
import {
    ALL_FILE_TYPES,
    CERTIFICATE_FILE_TYPE_ID,
    CERTIFICATE_FILE_TYPE_STATE,
    DocumentGenerationStates,
    DocumentSignedStates,
    File,
    FILE_TYPE_LINK,
    FileSlot,
    FileStates,
    FileType,
} from '../../api/models/file';
import {DeletionConfirmationComponent} from '../../material/action-confirmation/deletion-confirmation.component';
import {filter, finalize, switchMap} from 'rxjs/operators';
import {MatSnackBar} from '@angular/material/snack-bar';
import {SnackBarComponent} from '../../material/snack-bar/snack-bar.component';
import {displayTextSnackBar} from '../../utils/displayTextSnackBar';
import {TranslateService} from '@ngx-translate/core';
import {MatMenuTrigger} from '@angular/material/menu';
import {ApiError} from '../../errors/errors.types';
import {removeAfterLastDot} from '../../utils/string-utils';
import {APIEndpoint} from '../../api/services/api-endpoint.enum';
import {CertificationFolderStatesGeneratedConfig} from '../../api/models/certification-folder';
import {RegistrationFolderStatesGeneratedConfig} from '../../api/models/registration-folder';
import {CertificationPartnerStatesGeneratedConfig} from '../../api/models/certification-partner';

type FileHolder<S> = { files?: File[], state: S };

@Component({
    selector: 'app-file-list-slot',
    templateUrl: './file-list-slot.component.html',
    styleUrls: ['./file-list-slot.component.scss']
})
export class FileListSlotComponent<T extends FileHolder<S>, S> implements OnInit {

    fileStates = FileStates;
    fileGeneratingStates = DocumentGenerationStates;
    fileSignedStates = DocumentSignedStates;
    retrievedOrSimulatedFile: File = null;

    @Input() orderedStates: S[];
    @Input() entity: T & { attendeeLink?: string };
    @Input() entityApiPath: string;
    @Input() entityIdProperty: string;
    @Input() titleStateChange?: string;
    @Input() secondaryText?: string;
    @Input() fileType: FileType<S>;
    @Input() file: File;
    @Input() isAttendee = false;
    @Input() isPartner = false;
    @Input() isOwner = false;
    @Input() manageFile = true;
    @Input() entityParentId?: string;
    @Input() fileSlot: FileSlot<S>;

    @Output() fileSelected = new EventEmitter<File>();
    @Output() fileDownload = new EventEmitter<File>();
    @Output() filesChanged = new EventEmitter<void>();

    @ViewChild('menuTrigger') menuTrigger: MatMenuTrigger;
    @ViewChild('fileInput') fileInput: ElementRef<HTMLInputElement>;


    constructor(
        private googleDrivePickerService: GoogleDrivePickerService,
        private _fileService: FileService,
        private _snackBar: MatSnackBar,
        private _translateService: TranslateService,
        private _dialog: MatDialog) {
    }

    onClick(): void {
        if (this.file) {
            this.fileSelected.emit(this.file);
        } else if (this.canGenerate()) {
            this.generate('generate');
        } else if (this.isUploadAccepted()) {
            this.openFileBrowser();
        } else if (this.isLinkAccepted()) {
            this.openLinkDialog();
        }
    }

    ngOnInit(): void {
        if (this.file) {
            this.retrievedOrSimulatedFile = this.file;
        } else {
            if (this.canGenerate()) {
                this.retrievedOrSimulatedFile = {  // force simulate "file" so
                    id: 0,
                    fileName: this.fileType.name,
                    fileType: '',
                    permalink: '',
                    state: FileStates.VALID,
                    generationState: DocumentGenerationStates.GENERATED
                };
            } else {
                this.retrievedOrSimulatedFile = null;
            }
        }
    }

    isClickable(): boolean {
        if (this.fileSlot.generating) {
            return false;
        } else {
            return this.file != null || this.isUploadAccepted() || this.isLinkAccepted() || this.canGenerate();
        }
    }

    getApiEndPoint(): string {
        return this.isAttendee ? APIEndpoint.APP : APIEndpoint.API;
    }

    uploadFile(files: FileList, fileType: FileType<S>): void {
        if (files) {
            // tslint:disable-next-line:prefer-for-of
            for (let i = 0; i < files.length; i++) {
                let params = new HttpParams();
                params = params.set('typeId', fileType.id.toString());
                this._fileService.upload(this.entityApiPath, this.entity[this.entityIdProperty], files[i], params, this.getApiEndPoint()).subscribe(
                    (event) => {
                        if (event instanceof HttpResponse) {
                            this.updateFiles(event.body);
                        } else {
                            return event;
                        }
                    },
                    (httpErrorResponse: HttpErrorResponse) => {
                        this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar((httpErrorResponse.error as ApiError).errorMessages.toString(), 5000, 'red'));
                    }
                );
            }
        }
    }

    deleteFile(file: File): void {
        const dialogRef = this._dialog.open(DeletionConfirmationComponent, {
            panelClass: 'full-page-scroll-30',
            height: 'auto',
            data: {
                messageKey: file.comment ? 'private.common.files.confirmDeletionComment' : 'private.common.files.confirmDeletion',
                data: file
            }
        });
        dialogRef.componentInstance.actionValue$.pipe(
            filter(confirmation => confirmation),
            switchMap(() => this._fileService.delete(this.entityApiPath, this.entity[this.entityIdProperty], file, this.getApiEndPoint())),
            finalize(() => {
                dialogRef.componentInstance.close();
            })
        ).subscribe(
            () => {
                const newFiles = this.entity.files.filter((_file) => {
                    return _file.id !== file.id;
                });
                this.updateFiles(newFiles);
            },
            (httpErrorResponse: HttpErrorResponse) => {
                this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar((httpErrorResponse.error as ApiError).errorMessages.toString(), 5000, 'red'));
            }
        );
        this.menuTrigger.closeMenu();
    }

    downloadFile(file: File): void {
        this.fileDownload.emit(file);
        this.menuTrigger.closeMenu();
    }

    openFileBrowser(): void {
        if (this.fileInput) {
            this.fileInput.nativeElement.click();
        }
        this.menuTrigger.closeMenu();
    }

    openLinkDialog(downloadFile: boolean = false): void {
        const dialogRef = this._dialog.open(AddFileLinkDialogComponent, {
            panelClass: 'full-page-scroll-40',
            data: {
                title: this.fileType.name,
                downloadFile: downloadFile
            }
        });
        dialogRef.componentInstance.submitted$.subscribe((data) => {
            if (data.link != null) {
                let params = new HttpParams();
                params = params.set('typeId', this.fileType.id.toString());
                if (downloadFile) {
                    params = params.set('fileToDownload', data.link);
                }
                this._fileService.upload(this.entityApiPath, this.entity[this.entityIdProperty], data.link, params, this.getApiEndPoint()).subscribe(
                    (files: File[]) => {
                        this.updateFiles(files);
                    },
                    (httpErrorResponse: HttpErrorResponse) => {
                        this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar((httpErrorResponse.error as ApiError).errorMessages.toString(), 5000, 'red'));
                    }
                );
            }
        });
        this.menuTrigger.closeMenu();
    }

    openGoogleDrive(): void {
        this.menuTrigger.closeMenu();
        this.googleDrivePickerService.open((data) => {
            if (data.action === 'picked') {
                let params = new HttpParams();
                params = params.set('typeId', this.fileType.id.toString());
                params = params.set('title', data.docs[0].name);
                this._fileService.upload(this.entityApiPath, this.entity[this.entityIdProperty], data.docs[0].url, params, this.getApiEndPoint()).subscribe(
                    (files: File[]) => {
                        this.updateFiles(files);
                    },
                    (httpErrorResponse: HttpErrorResponse) => {
                        this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar((httpErrorResponse.error as ApiError).errorMessages.toString(), 5000, 'red'));
                    }
                );
            }
        });
    }

    getFilteredAcceptForInputFile(fileType: FileType<S>): string {
        const acceptType = fileType.accept ? fileType.accept.split(',').filter(a => {
            return a !== FILE_TYPE_LINK;
        }).join(',') : null;
        return acceptType === '.*' ? '*' : acceptType;
    }

    isUploadAccepted(hasFile: boolean = false): boolean {
        if (this.fileType.generated) {
            return false;
        }
        return (hasFile ? this.canReplaceFile() : this.canAddFile()) && this.fileType.accept !== FILE_TYPE_LINK;
    }

    isLinkAccepted(hasFile: boolean = false): boolean {
        if (this.fileType.generated) {
            return false;
        }
        return (hasFile ? this.canReplaceFile() : this.canAddFile())
            && (this.fileType.accept?.split(',').includes(FILE_TYPE_LINK)
                || this.fileType.accept?.split(',').includes(ALL_FILE_TYPES)
            );
    }

    getAttendeeLink(): string {
        return this.file ? this.file.permalink : this.entity.attendeeLink;
    }

    copy(): void {
        this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(this._translateService.instant('private.common.files.copySuccessAttendee'), 2500));
    }

    copyAttendee(): void {
        this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(this._translateService.instant('private.common.files.copySuccessAttendeeUpload'), 2500));
    }

    getFileTypeName(): string {
        let name = this.fileType.name;
        if (this.fileType.allowMultiple) {
            if (this.file) {
                name = removeAfterLastDot(this.file.fileName);
            } else {
                name = this._translateService.instant('private.common.files.addFile') + (this.fileType.name).toLowerCase();
            }
        }
        if (this.fileType.toState && !this.fileType.generated && !this.isAttendee) {
            name += '*';
        }
        return name;
    }

    canAddFile(): boolean {
        return !this.file && !this.fileType.generated && (
            this.isOwner ||
            (this.isAttendee && this.fileType.allowUploadAttendee) ||
            (this.isPartner && this.fileType.allowUploadPartner)
        );
    }

    canReplaceFile(): boolean {
        return this.file && !this.fileType.generated && !this.fileType.allowMultiple && (
            this.isOwner ||
            (this.isAttendee && this.fileType.allowUploadAttendee && (this.file.state !== FileStates.VALID)) ||
            (this.isPartner && this.fileType.allowUploadPartner && (this.file.state !== FileStates.VALID))
        );
    }

    canGenerate(): boolean {
        if (!this.fileType.generated || !this.fileType.enabled || !this.fileType.templateFile) {
            return false;
        }
        if (!this.fileType.toState) {
            return true;
        }
        const fileTypeToState = this.fileType.toState.toString();
        const stateGeneratedConfig = this.entityApiPath.includes('certificationFolders') ? CertificationFolderStatesGeneratedConfig[fileTypeToState] :
            this.entityApiPath.includes('registrationFolders') ?
                RegistrationFolderStatesGeneratedConfig[fileTypeToState] : CertificationPartnerStatesGeneratedConfig[fileTypeToState];
        return stateGeneratedConfig.includes(this.entity['state']);
    }

    getAcceptI18n(fileType: FileType<T>): string {
        const accept = fileType.accept.toLowerCase();
        if (accept.includes(ALL_FILE_TYPES)) {
            return '';
        } else {
            const acceptTypes: string[] = accept.split(',').map((acceptType: string) => {
                if (acceptType === FILE_TYPE_LINK) {
                    acceptType = this._translateService.instant('private.common.fileTypes.types.link');
                } else if (this.file) {
                    acceptType = acceptType.replace('.', '');
                }
                return acceptType;
            });
            return '(' + acceptTypes.join(',') + ')';
        }
    }

    private updateFiles(newFiles: File[]): void {
        this.entity.files = newFiles;
        this.filesChanged.emit();
    }

    canDeleteFile(): boolean {
        if (this.fileType.id === CERTIFICATE_FILE_TYPE_ID) {
            return this.file && this.isOwner && this.entity.state.toString() !== CERTIFICATE_FILE_TYPE_STATE;
        } else {
            return this.file && (
                this.isOwner ||
                (this.isAttendee && !this.isEntityRequiredStatePassed(this.fileType, this.entity) &&
                    this.fileType.allowUploadAttendee && this.file.state !== FileStates.VALID) ||  // need to check state
                (this.isPartner && !this.isEntityRequiredStatePassed(this.fileType, this.entity) &&
                    this.fileType.allowUploadPartner && this.file.state !== FileStates.VALID) // need to check state
            );
        }
    }

    updateFileState(fileId: number, state: FileStates): void {
        this._fileService.updateState(this.entityApiPath, this.entity[this.entityIdProperty], fileId, {state: state})
            .subscribe((file) => {
                this.file = file;
            });
    }

    getToolTipFile(fileState: string): string {
        let tooltip = '';
        if (fileState === FileStates.REFUSED || fileState === FileStates.TO_REVIEW) {
            tooltip = this.isAttendee ? this._translateService.instant('private.common.files.state.attendeeTooltip.' + fileState, {owner: this.entityApiPath.includes('certificationFolders') ? 'certificateur' : 'de formation'}) :
                this._translateService.instant('private.common.files.state.tooltip.' + fileState);
        }
        return tooltip;
    }

    getVisibilityToolTip(fileType: FileType<T>): string {
        let toolTip = '';
        if (fileType.allowVisibilityAttendee) {
            toolTip = this._translateService.instant(
                'private.common.fileTypes.form.' + (fileType.allowUploadAttendee ? 'allowUploadAttendee' : 'allowVisibilityAttendee') + '.help'
            );
        }
        if (fileType.allowVisibilityPartner) {
            toolTip += this._translateService.instant(
                'private.common.fileTypes.form.' + (fileType.allowUploadPartner ? 'allowUploadPartner' : 'allowVisibilityPartner') + '.help'
            );
        }
        if (fileType.allowVisibilityPublic) {
            toolTip += this._translateService.instant('private.common.fileTypes.form.allowVisibilityPublic.help');
        }
        return toolTip;
    }

    private isEntityRequiredStatePassed(fileType: FileType<S>, entity: FileHolder<S>): boolean {
        if (fileType.toState) {
            const indexOfToState = this.orderedStates.indexOf(fileType.toState);
            return this.orderedStates.indexOf(entity.state) > indexOfToState; // tous les états précédents
        } else {
            return false;
        }
    }

    getSecondaryText(): string {
        let secondaryText = this.file?.fileName;
        const state = this.fileType?.toState ? this._translateService.instant(this.titleStateChange + this.fileType?.toState) : null;
        if (!this.file) {
            if (state) {
                if (this.fileType.generated) {
                    if (this.fileType.toState === this.entity.state || this.isEntityRequiredStatePassed(this.fileType, this.entity)) {
                        secondaryText = this.fileType.name + '.pdf';
                    } else {
                        secondaryText = this._translateService.instant('private.common.files.autogenerated', {toState: state});
                    }
                } else {
                    secondaryText = this._translateService.instant('private.common.files.required', {toState: state});
                }
            } else {
                if (this.fileType.generated) {
                    secondaryText = this._translateService.instant('private.common.files.onRequestGenerated');
                } else {
                    secondaryText = this.secondaryText ?? this._translateService.instant('private.common.files.upload');
                }
            }
        }
        return secondaryText;
    }

    generate(action: 'generate' | 'regenerate'): void {
        const entityClass = this.entityApiPath.includes('registrationFolders') ? 'Organism' : 'Certification';
        const field = this.entityApiPath.includes('certificationFolders') ? 'certificationFolderFileTypes' :
            this.entityApiPath.includes('registrationFolders') ? 'registrationFolderFileTypes' : 'certificationPartnerFileTypes';
        const targetEntityClass = this.entityApiPath.includes('certificationFolders') ? 'CertificationFolder' :
            this.entityApiPath.includes('registrationFolders') ? 'RegistrationFolder' : 'CertificationPartner';
        this.fileSlot.generating = true;
        this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(this._translateService.instant('common.actions.file.generatingInProgress'), 5000));
        this._fileService.generate(entityClass, this.entityParentId, field, this.fileType.id, targetEntityClass, this.entity[this.entityIdProperty], action).subscribe(
            (event) => {
                if (event instanceof HttpResponse) {
                    if (action === 'generate') {
                        this.entity.files.push(event.body);
                    } else {
                        this.entity.files = this.entity.files.map((file) => {
                            return file.typeId === this.fileType.id ? event.body : file;
                        });
                    }
                    this.fileSlot.generating = false;
                    this.fileSlot.file = event.body;
                    this.fileSelected.emit(this.fileSlot.file);
                } else {
                    return event;
                }
            },
            (httpErrorResponse: HttpErrorResponse) => {
                this.fileSlot.generating = false;
                this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar((httpErrorResponse.error as ApiError).errorMessages.toString(), 5000, 'red'));
            }
        );
    }
}
