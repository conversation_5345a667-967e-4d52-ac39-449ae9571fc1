<mat-list-item (click)="onClick()"
               class="flex-auto {{ !isClickable() ? 'cursor-default pointer-events-none opacity-25' : '' }}">
    <input *ngIf="isUploadAccepted()"
           #fileInput
           (change)="uploadFile($event.target['files'], fileType)" hidden
           id="file-{{fileType.id}}-{{file?.id}}"
           accept="{{ getFilteredAcceptForInputFile(fileType) }}"
           type="file"/>
    <mat-icon *ngIf="file && file.state === fileStates.REFUSED && file.comment; else showOther" color="warn"
              svgIcon="priority_high"
              [matTooltip]="file.comment"
              mat-list-icon></mat-icon>
    <ng-template #showOther>
        <mat-icon *ngIf="retrievedOrSimulatedFile; else noFile"
                  [color]="retrievedOrSimulatedFile.state === fileStates.REFUSED ? 'warn' : undefined "
                  [class]="retrievedOrSimulatedFile.state ===  fileStates.VALID ? 'text-green' : retrievedOrSimulatedFile.state ===  fileStates.TO_REVIEW ? 'text-orange' : ''"
                  [svgIcon]="retrievedOrSimulatedFile.state === fileStates.VALID ? 'feather:check-circle' : retrievedOrSimulatedFile.state === fileStates.TO_REVIEW ? 'feather:alert-circle' : 'feather:x-circle'"
                  [matTooltip]="getToolTipFile(retrievedOrSimulatedFile.state)"
                  mat-list-icon></mat-icon>
        <ng-template #noFile>
            <mat-icon color="primary" [svgIcon]="'feather:plus-circle'" mat-list-icon></mat-icon>
        </ng-template>
    </ng-template>
    <div mat-line class="flex items-center"
         [matTooltip]="fileType.description ? fileType.description : null">
        <mat-icon *ngIf="isOwner && (fileType.allowVisibilityPartner || fileType.allowVisibilityAttendee)"
                  [matTooltip]="getVisibilityToolTip(fileType)"
                  class="mr-1 icon-small"
                  [svgIcon]="fileType.allowVisibilityPartner && fileType.allowVisibilityAttendee ? 'feather:users' : 'feather:user'">
        </mat-icon>
        <mat-icon *ngIf="fileType.allowVisibilityPublic && isOwner"
                  class="icon-small mr-1"
                  [svgIcon]="'visibility'"
                  [matTooltip]="getVisibilityToolTip(fileType)">
        </mat-icon>
        <mat-icon *ngIf="fileType.generated && !isAttendee"
                  class="icon-small mr-1"
                  [svgIcon]="'bolt'">
        </mat-icon>
        <mat-icon *ngIf="file && file.signedState && file.signedState !== fileSignedStates.NOT_REQUIRED && !(isAttendee && file.signedState === fileSignedStates.COMPLETED)"
                  [class]="file.signedState === fileSignedStates.NONE ? 'text-gray icon-small' : file.signedState === fileSignedStates.PARTIALLY ? 'text-blue icon-small' : 'text-green icon-small' "
                  svgIcon="draw"
                  [matTooltip]="'private.common.files.signedState.' + file.signedState  | translate"
                  mat-list-icon></mat-icon>
        {{ getFileTypeName() }}
    </div>
    <div *ngIf="!isAttendee"
         mat-line
         class="text-secondary text-sm">
        {{ getSecondaryText() }}
        <span *ngIf="!fileType.generated">{{ getAcceptI18n(fileType) }}</span>
    </div>
    <div mat-line *ngIf="isAttendee && retrievedOrSimulatedFile"
         class="text-secondary">{{ retrievedOrSimulatedFile.fileName }}
    </div>
    <mat-progress-spinner *ngIf="fileSlot.generating" class="mr-1"
                          [matTooltip]="'common.actions.file.generatingInProgress' | translate"
                          [diameter]="24"
                          mode="indeterminate"></mat-progress-spinner>
    <button mat-icon-button
            type="button"
            (click)="$event.stopPropagation()"
            #menuTrigger="matMenuTrigger"
            [matMenuTriggerFor]="actionsMenu"
            class="-mr-4"
            title="Actions">
        <mat-icon svgIcon="more_vert"></mat-icon>
    </button>
    <mat-menu #actionsMenu="matMenu" class="large-menu">
        <ng-container *ngIf="file else newActions">
            <button *ngIf="file.link && file.signedState === fileSignedStates.NOT_REQUIRED" type="button"
                    (click)="downloadFile(file)"
                    mat-menu-item>
                <mat-icon svgIcon="mat_outline:open_in_new"></mat-icon>
                {{ 'private.common.files.menu.open' | translate }}
            </button>
            <button *ngIf="!file.link" type="button"
                    [disabled]="file.signedState === fileSignedStates.NONE || file.signedState === fileSignedStates.PARTIALLY"
                    (click)="downloadFile(file)"
                    mat-menu-item>
                <mat-icon
                    svgIcon="mat_outline:open_in_new"></mat-icon>
                {{ 'private.common.files.menu.download' | translate }}
            </button>
            <button *ngIf="canGenerate() && !isAttendee"
                    type="button"
                    (click)="generate('regenerate')"
                    mat-menu-item>
                <mat-icon>bolt</mat-icon>
                <span>{{ 'common.actions.file.regenerate' | translate }}</span>
            </button>
            <ng-template class="mb-2"
                         [ngTemplateOutlet]="addFileLink"
                         [ngTemplateOutletContext]="{hasFile: true}">
            </ng-template>
            <ng-template [ngTemplateOutlet]="addDownloadFile"
                         [ngTemplateOutletContext]="{hasFile: true}">
            </ng-template>
            <button *ngIf="isUploadAccepted(true)"
                    type="button"
                    (click)="openFileBrowser()"
                    mat-menu-item>
                <input #fileInput
                       (change)="uploadFile($event.target['files'], fileType)" hidden
                       id="fileReplace-{{fileType.id}}-{{file?.id}}"
                       accept="{{ getFilteredAcceptForInputFile(fileType) }}"
                       type="file"/>
                <mat-icon svgIcon="restart_alt"></mat-icon>
                <span>{{ 'private.common.files.menu.replaceUpload' | translate }}</span>
            </button>
            <button
                *ngIf="fileType.allowVisibilityAttendee && !isAttendee"
                ngxClipboard
                [cbContent]="getAttendeeLink()"
                (cbOnSuccess)="copy()"
                [attr.aria-label]="'Copier'"
                mat-menu-item>
                <mat-icon svgIcon="person"></mat-icon>
                <span>{{ 'common.actions.attendeeLink' | translate }}</span>
            </button>
            <ng-container *ngIf="isOwner && manageFile && file.generationState === fileGeneratingStates.NOT_GENERATED">
                <button *ngIf=" file.state === fileStates.REFUSED || file.state === fileStates.TO_REVIEW"
                        (click)="updateFileState(file.id, fileStates.VALID)" type="button" mat-menu-item>
                    <mat-icon [svgIcon]="'feather:check-circle'" [color]="undefined" class="text-green"></mat-icon>
                    <span>{{ 'private.common.files.state.fileState.valid' | translate }}</span>
                </button>
                <button *ngIf="file.state === fileStates.REFUSED || file.state === fileStates.VALID"
                        (click)="updateFileState(file.id, fileStates.TO_REVIEW)" type="button" mat-menu-item>
                    <mat-icon [svgIcon]="'feather:alert-circle'" [color]="undefined" class="text-orange"></mat-icon>
                    <span>{{ 'private.common.files.state.fileState.toReview' | translate }}</span>
                </button>
                <button *ngIf="file.state === fileStates.VALID || file.state === fileStates.TO_REVIEW"
                        (click)="updateFileState(file.id, fileStates.REFUSED)" type="button" mat-menu-item>
                    <mat-icon [svgIcon]="'feather:x-circle'" color="warn"></mat-icon>
                    <span>{{ 'private.common.files.state.fileState.refused' | translate }}</span>
                </button>
            </ng-container>
            <button *ngIf="canDeleteFile()"
                    type="button"
                    (click)="deleteFile(file)"
                    mat-menu-item>
                <mat-icon svgIcon="mat_outline:delete" color="warn"></mat-icon>
                <span>{{ 'common.actions.delete' | translate }}</span>
            </button>
        </ng-container>
        <ng-template #newActions>
            <ng-template class="mb-2"
                         [ngTemplateOutlet]="addFileLink"
                         [ngTemplateOutletContext]="{hasFile: false}">
            </ng-template>
            <ng-template [ngTemplateOutlet]="addDownloadFile"
                         [ngTemplateOutletContext]="{hasFile: false}">
            </ng-template>
            <button *ngIf="isUploadAccepted()"
                    type="button"
                    (click)="openFileBrowser()"
                    mat-menu-item>
                <mat-icon>file_upload</mat-icon>
                <span>{{ 'private.common.files.menu.upload' | translate }}</span>
            </button>
            <button *ngIf="canGenerate()"
                    type="button"
                    (click)="generate('generate')"
                    mat-menu-item>
                <mat-icon *ngIf="fileType.toState" svgIcon="mat_outline:open_in_new"></mat-icon>
                <mat-icon *ngIf="!fileType.toState">bolt</mat-icon>
                <span>{{ (fileType.toState ? 'private.common.files.menu.download' : 'common.actions.file.generate')| translate }}</span>
            </button>
            <button
                *ngIf="fileType.allowUploadAttendee && !isAttendee"
                ngxClipboard
                [cbContent]="getAttendeeLink()"
                (cbOnSuccess)="copyAttendee()"
                [attr.aria-label]="'Copier'"
                mat-menu-item>
                <mat-icon svgIcon="person"></mat-icon>
                <span>{{ 'common.actions.attendeeLink' | translate }}</span>
            </button>
        </ng-template>
    </mat-menu>
</mat-list-item>

<ng-template #addFileLink let-hasFile="hasFile">
    <button *ngIf="isLinkAccepted(hasFile)"
            type="button"
            (click)="openLinkDialog()"
            mat-menu-item>
        <mat-icon svgIcon="mat_outline:link"></mat-icon>
        <span> {{ 'private.common.files.menu.' + (hasFile ? 'replaceLink' : 'link') | translate }}</span>
    </button>
    <button *ngIf="isLinkAccepted(hasFile)"
            type="button"
            (click)="openGoogleDrive()"
            mat-menu-item>
        <mat-icon>add_to_drive</mat-icon>
        <span> {{ 'private.common.files.menu.' + (hasFile ? 'replaceDrive' : 'drive')  | translate }}</span>
    </button>
</ng-template>

<ng-template #addDownloadFile let-hasFile="hasFile">
    <button *ngIf="isUploadAccepted(!!hasFile)"
            type="button"
            (click)="openLinkDialog(true)"
            mat-menu-item>
        <mat-icon>attach_file</mat-icon>
        <span> {{ 'private.common.files.menu.' + (hasFile ? 'reUploadViaLink' : 'uploadViaLink')  | translate }}</span>
    </button>
</ng-template>
