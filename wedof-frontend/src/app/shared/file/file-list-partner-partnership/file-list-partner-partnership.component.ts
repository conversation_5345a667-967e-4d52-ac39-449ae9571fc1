import {Component, Input, OnChanges} from '@angular/core';
import {File, FILE_TYPE_LINK, FileHolder, FileSlot, FileType, FREE_FILE_TYPE_NAME} from '../../api/models/file';
import {FileService} from '../../api/services/file.service';
import {MatDialog} from '@angular/material/dialog';
import {ViewFileDialogComponent} from '../dialogs/view-file-dialog/view-file-dialog.component';
import {sortBy} from 'lodash-es';
import {CertificationFolderSurveyService} from '../../api/services/certification-folder-survey.service';
import {APIEndpoint} from '../../api/services/api-endpoint.enum';

@Component({
    selector: 'app-file-list-partner-partnership',
    templateUrl: './file-list-partner-partnership.component.html',
    styleUrls: ['./file-list-partner-partnership.component.scss']
})
export class FileListPartnerPartnershipComponent<T extends FileHolder<S>, S> implements OnChanges {

    fileSlots: FileSlot<S>[] = [];
    filesUpdated = [];

    @Input() entity: T;
    @Input() entityApiPath: string;
    @Input() entityIdProperty: string;
    @Input() fileTypes: FileType<S>[];
    @Input() orderedStates: S[];
    @Input() titleStateChange: string;
    @Input() subtitle: string;
    @Input() entityParentId: string;

    constructor(
        private _fileService: FileService,
        private _certificationFolderSurveyService: CertificationFolderSurveyService,
        private _dialog: MatDialog) {
    }

    ngOnChanges(): void {
        this.refreshFileSlots();
    }

    onFilesChanged(): void {
        this.refreshFileSlots();
    }

    onFileSelected(file: File): void {
        this._dialog.open(ViewFileDialogComponent, {
            width: '80%',
            height: '90%',
            data: {
                file: file,
                auth: true,
                title: ((_file: File) => {
                    const fileType = this.fileTypes.find(currentFileType => currentFileType.id === _file.typeId);
                    return (fileType ? (fileType.name + ' - ') : '') + _file.fileName;
                }),
                src: (_file: File) => '/api/' + this.entityApiPath + '/' + this.entity[this.entityIdProperty] + '/files/' + _file.id,
                files: sortBy(this.entity.files, _file => _file.typeId),
            }
        });
    }

    hasFile(fileType: FileType<S>): boolean {
        return this.entity.files?.find(file => file.typeId === fileType.id) != null;
    }

    isAllDocumentsCompleted(): boolean {
        return this.fileTypes?.every(fileType => {
            return fileType.allowMultiple || this.hasFile(fileType);
        }) ?? false;
    }

    refreshFileSlots(): void {
        const predefinedTypes = this.fileTypes ?? [];
        const fileSlots: FileSlot<S>[] = [];
        this.entity.files?.forEach(file => {
            const fileType = predefinedTypes.find(type => type.id === file.typeId);
            // only file with fileType should be visible for a partner on partnership (has it needs a visibility permission)
            if (fileType) {
                fileSlots.push({fileType, file});
            }
        });
        const availableFileTypes = predefinedTypes.filter(fileType => {
            if (this.hasFile(fileType)) {
                return fileType.allowMultiple;
            } else {
                return fileType.generated ? fileType.enabled : true;
            }
        });
        availableFileTypes.forEach(fileType => {
            fileSlots.push({fileType});
        });
        this.fileSlots = sortBy(fileSlots, fileSlot => {
            const fileType = fileSlot.fileType;
            if (fileType.allowMultiple && fileType.name === FREE_FILE_TYPE_NAME) {
                return 1000000 + fileType.id + (this.hasFile(fileType) ? 0 : 10000);
            } else {
                return fileType.id;
            }
        });
    }

    isRequiredDocumentsMissingForNextState(entity: FileHolder<S>): boolean {
        let missingRequiredFiles = [];
        const indexOfCurrentState = this.orderedStates.indexOf(entity.state);
        const indexOfNextState = indexOfCurrentState + 1;
        const filesTypeUploaded = entity.files != null ? entity.files.map(file => file.typeId) : [];
        const notLastState = indexOfNextState < this.orderedStates.length;
        if (this.fileTypes && notLastState) {
            missingRequiredFiles = this.fileTypes?.filter(fileType => {
                const nextState = this.orderedStates[indexOfNextState];
                return !fileType.generated && fileType.toState === nextState && !filesTypeUploaded.includes(fileType.id);
            });
        }
        return missingRequiredFiles?.length > 0;
    }

    downloadFile(file: File): void {
        if (file.fileType !== FILE_TYPE_LINK) {
            this._fileService.download(this.entityApiPath, this.entity[this.entityIdProperty], file, null, APIEndpoint.API).subscribe();
        } else {
            window.open(file.link, '_blank');
        }
    }
}
