<div class="flex flex-col">
    <div class="flex items-center">
        <mat-icon [color]="isAllDocumentsCompleted() ? 'primary' : 'accent'"
                  class="mr-3">picture_as_pdf
        </mat-icon>
        <p class="text-lg font-medium">{{ 'private.common.files.title' | translate }}</p>
    </div>
    <p class="text-secondary">{{subtitle | translate}}</p>
</div>
<div class="px-5 relative">
    <mat-divider></mat-divider>
</div>
<ng-template #noFileSlots>
    <p class="text-secondary text-center mb-4 mt-3">{{ 'private.common.files.noDataPartner' | translate }}</p>
</ng-template>
<div class="flex flex-auto flex-col mb-4" *ngIf="entity && fileSlots?.length > 0; else noFileSlots">
    <mat-action-list>
        <app-file-list-slot *ngFor="let fileSlot of fileSlots"
                            [fileType]="fileSlot.fileType"
                            [file]="fileSlot.file"
                            [fileSlot]="fileSlot"
                            [orderedStates]="orderedStates"
                            [entity]="entity"
                            [isAttendee]="false"
                            [isOwner]="false"
                            [isPartner]="true"
                            [entityApiPath]="entityApiPath"
                            [entityIdProperty]="entityIdProperty"
                            [titleStateChange]="titleStateChange"
                            (fileDownload)="downloadFile($event)"
                            (fileSelected)="onFileSelected($event)"
                            (filesChanged)="onFilesChanged()"
                            [entityParentId]="entityParentId"
                            >
        </app-file-list-slot>
    </mat-action-list>
</div>
