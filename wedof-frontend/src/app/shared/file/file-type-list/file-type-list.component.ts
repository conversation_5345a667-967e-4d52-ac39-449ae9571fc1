import {Component, EventEmitter, Input, OnChang<PERSON>, OnDestroy, Output, SimpleChanges} from '@angular/core';
import {DeletionConfirmationComponent} from '../../material/action-confirmation/deletion-confirmation.component';
import {filter, finalize, switchMap, takeUntil, tap} from 'rxjs/operators';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../errors/errors.types';
import {FileTypeService} from '../../api/services/file-type.service';
import {TranslateService} from '@ngx-translate/core';
import {MatDialog} from '@angular/material/dialog';
import {CERTIFICATE_FILE_TYPE_ID, FileType, FREE_FILE_TYPE_NAME} from '../../api/models/file';
import {DialogFileTypeComponent} from '../dialogs/dialog-file-type/dialog-file-type.component';
import {ThemePalette} from '@angular/material/core';
import {Select} from '@ngxs/store';
import {SubscriptionState} from '../../api/state/subscription.state';
import {Observable, of, Subject} from 'rxjs';
import {Subscription} from '../../api/models/subscription';
import {DocumentApplication} from '../../../applications/document/document.application';
import {OrganismApplicationService} from '../../api/services/organism-application.service';
import {APIEndpoint} from '../../api/services/api-endpoint.enum';
import {SnackBarComponent} from '../../material/snack-bar/snack-bar.component';
import {displayTextSnackBar} from '../../utils/displayTextSnackBar';
import {MatSnackBar} from '@angular/material/snack-bar';
import {FileService} from '../../api/services/file.service';
import {Download} from '../../download/download';
import {PickerComponent} from '../../material/picker/picker.component';
import {Certification} from '../../api/models/certification';
import {SignatureApplication} from '../../../applications/signature/signature.application';

export type StateChoices = { key: any, icon?: string, color?: ThemePalette, value: any }[];

@Component({
    selector: 'app-file-type-list',
    templateUrl: './file-type-list.component.html',
    styleUrls: ['./file-type-list.component.scss']
})
export class FileTypeListComponent<T> implements OnChanges, OnDestroy {

    loadingManageFile: boolean;
    errorMessagesFreeFiles: string[] = [];
    loading = false;
    displayedFileTypes: FileType<T>[];
    allowFreeFiles = false;

    isDocumentApplicationAllowed: boolean;
    isDocumentApplicationEnabled: boolean;
    isSignatureApplicationEnabled: boolean;

    loadingManageFileType = false;

    @Input() isCard ? = true;
    @Input() isDefaultCertifierOrOrganism: boolean;
    @Input() title: string;
    @Input() icon: string;
    @Input() certifications: Certification[];
    @Input() subtitle?: string;
    @Input() displayedColumns: string[];
    @Input() entityId: number;
    @Input() entityClass: string;
    @Input() entityField: string;
    @Input() fileTypes: FileType<T>[];
    @Input() stateChoices?: StateChoices;
    @Input() stateChoicesGenerated?: StateChoices;
    @Input() toStateLabel: string;
    @Input() allowExtraFields: boolean;
    @Output() fileTypeListUpdated = new EventEmitter<FileType<T>[]>();

    @Select(SubscriptionState.subscription) subscription$: Observable<Subscription>;

    private _unsubscribeAll: Subject<void> = new Subject();

    constructor(
        private _fileTypeService: FileTypeService<T>,
        private _translateService: TranslateService,
        private _dialog: MatDialog,
        private _snackBar: MatSnackBar,
        private _fileService: FileService,
        private _organismApplicationService: OrganismApplicationService
    ) {
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes.entityId || changes.entityField) {
            this.initForm();
        }
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    initForm(): void {
        this.subscription$.pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe((subscription) => {
            this.isDocumentApplicationAllowed = subscription.allowedApps?.includes(DocumentApplication.appId());
            this._organismApplicationService.get(DocumentApplication.appId()).subscribe((documentApplication) => {
                this.isDocumentApplicationEnabled = documentApplication.enabled;
            });
            this._organismApplicationService.get(SignatureApplication.appId()).subscribe((signatureApplication) => {
                this.isSignatureApplicationEnabled = signatureApplication.enabled;
            });
        });
        this.allowFreeFiles = this.getFreeFileType() != null;
        this.errorMessagesFreeFiles = [];
        this.refreshDisplayedFileTypes();
    }

    toggleAllowFreeFiles(): void {
        this.loading = true;
        const allowFreeFilesNewValue = this.allowFreeFiles !== true;
        this.errorMessagesFreeFiles = [];

        const FREE_FILE_TYPE = {
            name: FREE_FILE_TYPE_NAME,
            accept: 'link,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.png,.jpg,.zip,.rar,.7z,.ace,.tar.gz,.*',
            description: null,
            toState: null,
            allowMultiple: true,
            allowVisibilityAttendee: false,
            allowUploadAttendee: false,
            allowSignAttendee: false,
            allowVisibilityPartner: true,
            allowUploadPartner: true,
            allowSignPartner: false,
            generated: false,
            templateFile: null,
            allowRegenerate: false,
            enabled: false,
        } as const;

        const existingFreeFileType = this.getFreeFileType();
        if (allowFreeFilesNewValue && existingFreeFileType) {
            this.errorMessagesFreeFiles = [this._translateService.instant('private.common.fileTypes.freeFile.errors.activated')];
            this.loading = false;
            return;
        } else if (!allowFreeFilesNewValue && !existingFreeFileType) {
            this.errorMessagesFreeFiles = [this._translateService.instant('private.common.fileTypes.freeFile.errors.deactivated')];
            this.loading = false;
            return;
        }

        if (allowFreeFilesNewValue) {
            this._fileTypeService.create(this.entityClass, this.entityId, this.entityField, FREE_FILE_TYPE).subscribe({
                next: (fileType) => {
                    this.allowFreeFiles = allowFreeFilesNewValue;
                    const newFileTypes = this.fileTypes ? [...this.fileTypes, fileType] : [fileType];
                    this.setNewFileTypes(newFileTypes);
                    this.loading = false;
                },
                error: (httpErrorResponse: HttpErrorResponse) => {
                    this.errorMessagesFreeFiles = (httpErrorResponse.error as ApiError).errorMessages;
                    this.loading = false;
                }
            });
        } else {
            this._fileTypeService.delete(this.entityClass, this.entityId, this.entityField, existingFreeFileType.id).subscribe({
                next: () => {
                    this.allowFreeFiles = allowFreeFilesNewValue;
                    const newFileTypes = this.fileTypes.filter(currentFileType => currentFileType.id !== existingFreeFileType.id);
                    this.setNewFileTypes(newFileTypes);
                    this.loading = false;
                },
                error: (httpErrorResponse: HttpErrorResponse) => {
                    this.errorMessagesFreeFiles = (httpErrorResponse.error as ApiError).errorMessages;
                    this.loading = false;
                }
            });
        }
    }

    openTemplatePicker(): void {
        this.loadingManageFileType = true;
        this._fileTypeService.listAllModels(this.entityField).subscribe((templateFileTypes) => {
            this.loadingManageFileType = false;
            const cards = templateFileTypes.filter((template) => template.additionalInformations.format !== '.pptx');
            const dialogRef = this._dialog.open(PickerComponent, {
                disableClose: true, // necessary => avoid creating template that could be loss
                panelClass: ['full-page-scroll-40'],
                data: {
                    cards: cards,
                    feedback: this._translateService.instant('common.actions.file.pendingCreation'),
                    selectTemplate: (templateName: string) => {
                        if (templateName === 'New') {
                            return of('New');
                        } else {
                            const templateCard = cards.find((cardTemplate) => cardTemplate.templateName === templateName);
                            const bodyTemplateName = templateCard.templateName === 'TemplateInWedof' ? null : templateCard.templateName;
                            return this._fileTypeService.createTemplate(this.entityClass, this.entityId, this.entityField,
                                templateCard.additionalInformations.format, bodyTemplateName, true);
                        }
                    }
                }
            });
            dialogRef.afterClosed().subscribe(result => {
                if (result?.data?.id) {
                    // creation of fileType is done in FileTypeController
                  this.updateFileType(result?.data);
                } else if (result?.data === 'New') {
                    this.addFileType(true);
                }
            });
        });
    }

    addFileType(generated: boolean): void {
        const dialogRef = this._dialog.open(DialogFileTypeComponent, {
            disableClose: true, // necessary => avoid creating template that could be loss
            panelClass: ['full-page-scroll-50'],
            data: {
                stateChoices: generated ? this.stateChoicesGenerated : this.stateChoices,
                allowExtraFields: this.allowExtraFields,
                entityField: this.entityField,
                entityClass: this.entityClass,
                entityId: this.entityId,
                certifications: this.certifications,
                generated: generated,
                submitFileType: (newFileType: FileType<T>) => {
                    if (typeof (newFileType['toState']) === 'boolean' && this.stateChoices.length === 1) {
                        newFileType['toState'] = newFileType['toState'] ? this.stateChoices[0].value : null;
                    }
                    return this._fileTypeService.create(this.entityClass, this.entityId, this.entityField, newFileType);
                }
            }
        });
        dialogRef.afterClosed().subscribe(res => {
            const newFileType: FileType<T> = res?.fileType;
            if (newFileType) {
                // New ref otherwise f*cking CDK table does not re-render
                const newFileTypes = this.fileTypes ? [...this.fileTypes, newFileType] : [newFileType];
                this.setNewFileTypes(newFileTypes);
            }
        });
    }

    updateFileType(fileType: FileType<T>): void {
        const dialogRef = this._dialog.open(DialogFileTypeComponent, {
            disableClose: true, // necessary => avoid creating template that could be loss
            panelClass: 'full-page-scroll-50',
            height: 'auto',
            data: {
                stateChoices: fileType.generated ? this.stateChoicesGenerated : this.stateChoices,
                fileType: fileType,
                entityField: this.entityField,
                entityClass: this.entityClass,
                entityId: this.entityId,
                allowExtraFields: this.allowExtraFields,
                certifications: this.certifications,
                downloadTemplateExample: (fileTypeToDownload: FileType<T>) => {
                    return this.downloadTemplateExampleInternal(fileTypeToDownload);
                },
                submitFileType: (fileTypeUpdated: FileType<T>) => {
                    if (typeof (fileTypeUpdated['toState']) === 'boolean' && this.stateChoices.length === 1) {
                        fileTypeUpdated['toState'] = fileTypeUpdated['toState'] ? this.stateChoices[0].value : null;
                    }
                    if (Array.isArray(fileTypeUpdated['accept'])) {
                        fileTypeUpdated['accept'] = fileTypeUpdated['accept'].join();
                    }
                    if (fileTypeUpdated['googleId']) {
                        delete fileTypeUpdated['googleId'];
                    }
                    return this._fileTypeService.update(this.entityClass, this.entityId, this.entityField, fileType['id'], fileTypeUpdated);
                }
            }
        });
        dialogRef.afterClosed().subscribe(res => {
            const updatedFileType = res?.fileType;
            if (updatedFileType) {
                // New ref otherwise f*cking CDK table does not re-render
                const newFileTypes = this.fileTypes.map(currentFileType => currentFileType.id === updatedFileType.id ? updatedFileType : currentFileType);
                this.setNewFileTypes(newFileTypes);
            }
        });
    }

    enabled(fileType: FileType<T>, enabled: boolean): any {
        this.loadingManageFile = true;
        const fileTypeToUpdate = {...fileType, enabled: enabled};
        delete fileTypeToUpdate['googleId'];
        delete fileTypeToUpdate.id;
        return this._fileTypeService.update(this.entityClass, this.entityId, this.entityField, fileType.id, fileTypeToUpdate).pipe(
            finalize(() => {
                this.loadingManageFile = false;
            })
        ).subscribe(
            (updatedFileType) => {
                const newFileTypes = this.fileTypes.map(currentFileType => currentFileType.id === updatedFileType.id ? updatedFileType : currentFileType);
                this.setNewFileTypes(newFileTypes);
            }, (httpErrorResponse: HttpErrorResponse) => {
                const error = (httpErrorResponse.error as ApiError).detail;
                this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(error, 5000, 'red'));
            }
        );
    }

    removeFileType(fileType: FileType<T>): void {
        const dialogRef = this._dialog.open(DeletionConfirmationComponent, {
            panelClass: 'full-page-scroll-40',
            height: 'auto',
            data: {
                messageKey: 'private.common.certification.files.table.confirmDeletion',
                data: fileType
            }
        });
        dialogRef.componentInstance.actionValue$.pipe(
            filter(confirmation => confirmation),
            tap(() => this.loadingManageFile = true),
            switchMap(() => this._fileTypeService.delete(this.entityClass, this.entityId, this.entityField, fileType.id)),
            finalize(() => {
                this.loadingManageFile = false;
            })
        ).subscribe(
            () => {
                // New ref otherwise f*cking CDK table does not re-render
                const newFileTypes = this.fileTypes.filter(currentFileType => currentFileType.id !== fileType.id);
                this.setNewFileTypes(newFileTypes);
                dialogRef.componentInstance.close();
            },
            (httpErrorResponse: HttpErrorResponse) => {
                dialogRef.componentInstance.close();
                const error = (httpErrorResponse.error as ApiError).detail;
                this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(error, 5000, 'red'));
            }
        );
    }

    setNewFileTypes(newFileTypes: FileType<T>[]): void {
        this.fileTypes = newFileTypes;
        this.fileTypeListUpdated.emit(this.fileTypes);
        this.refreshDisplayedFileTypes();
    }

    refreshDisplayedFileTypes(): void {
        this.displayedFileTypes = this.fileTypes
            .filter(fileType => !(fileType.allowMultiple && fileType.name === FREE_FILE_TYPE_NAME))
            .sort((fileType) => {
                return fileType.id === CERTIFICATE_FILE_TYPE_ID ? -1 : 0;
            });
    }

    getAcceptI18n(fileType: FileType<T>): string {
        const accept = fileType.accept.toLowerCase();
        return accept.includes('link') ? accept.replace('link', this._translateService.instant('private.common.fileTypes.types.link')) : accept;
    }

    getFreeFileType(): FileType<T> {
        return this.fileTypes?.find((fileType) => fileType.allowMultiple && fileType.name === FREE_FILE_TYPE_NAME);
    }

    downloadTemplateExample(fileType: FileType<T>): void {
        this.loadingManageFile = true;
        this.downloadTemplateExampleInternal(fileType).subscribe({
                next: (blob) => {
                    if (blob.state === 'PENDING') {
                        this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(
                            this._translateService.instant('common.actions.file.generatingInProgress'), 10000));
                    }
                    if (blob.state === 'DONE') {
                        this.loadingManageFile = false;
                    }
                }, error: (httpErrorResponse: HttpErrorResponse) => {
                    this.loadingManageFile = false;
                    const error = (httpErrorResponse.error as ApiError).detail;
                    this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(error, 5000, 'red'));
                }
            }
        );
    }

    private downloadTemplateExampleInternal(fileType: FileType<T>): Observable<Download<Blob>> {
        const url = APIEndpoint.APP + '/fileTypes/' + this.entityClass + '/' + this.entityId + '/' + this.entityField + '/' + fileType.id;
        return this._fileService.download(url, 'model');
    }

    getLimitedDocumentInfo(fileType: FileType<T>): string {
        let text = '';
        if (fileType.tags?.length > 0 && fileType.certifications?.length > 0) {
            text += ' tags et certifications';
        } else if (fileType.tags?.length > 0) {
            text += ' tags';
        } else if (fileType.certifications?.length > 0) {
            text += ' certifications';
        }
        return text;
    }

    getToolTip(fileType: FileType<T>): string {
        let tooltip = '';
        let toState = '';
        if (fileType.toState) {
            toState = this._translateService.instant(this.toStateLabel + fileType.toState);
        }
        if (fileType.generated) {
            tooltip = fileType.toState ? this._translateService.instant('private.common.files.autogenerated', {
                toState: toState
            }) : this._translateService.instant('private.common.files.onRequestGenerated');
        } else if (fileType.toState) {
            tooltip = this._translateService.instant('private.common.files.required', {
                toState: toState
            });
        }
        if (fileType.tags?.length > 0 || fileType.certifications?.length > 0) {
            if (tooltip.length > 1) {
                tooltip += '. ';
            }
            tooltip += this._translateService.instant('private.common.files.limitedDocument');
            tooltip += this.getLimitedDocumentInfo(fileType);
        }
        return tooltip;
    }

    getTagsAndCertificationsTooltips(fileType: FileType<T>): string {
        let text = '';
        if (fileType.tags?.length > 0 && fileType.certifications?.length > 0) {
            text += `Tags : ${fileType.tags} / Certifications : ${fileType.certifications}`;
        } else if (fileType.tags?.length > 0) {
            text += `Tags : ${fileType.tags}`;
        } else if (fileType.certifications?.length > 0) {
            text += `Certifications : ${fileType.certifications}`;
        }
        return text;
    }

}
