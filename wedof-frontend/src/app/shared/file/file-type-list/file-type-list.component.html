<mat-card class="flex-col mt-3 p-5 pb-0 pt-3 border shadow-none sm" *ngIf="isCard; else showComponentInCard">
    <div class="flex items-center mb-2">
        <mat-icon class="mr-3 card-loading-show text-4xl" [svgIcon]="icon" color="primary">
        </mat-icon>
        <div>
            <div class="text-xl font-semibold card-loading-show">{{ title }}</div>
            <div *ngIf="subtitle"
                 class="text-secondary text-md card-loading-show">{{ subtitle }}
            </div>
        </div>
    </div>
    <ng-container *ngTemplateOutlet="fileTypesTable">
    </ng-container>
</mat-card>

<ng-template #showComponentInCard>
    <div class="flex flex-col">
        <div class="flex items-center">
            <mat-icon [svgIcon]="icon" color="accent" class="mr-3"></mat-icon>
            <div class="text-lg font-medium card-loading-show">{{ title }}</div>
        </div>
        <p *ngIf="subtitle" class="text-secondary">{{ subtitle | translate }}</p>
    </div>
    <ng-container *ngTemplateOutlet="fileTypesTable">
    </ng-container>
</ng-template>

<ng-template #fileTypesTable>
    <div *ngIf="isDefaultCertifierOrOrganism" class="flex flex-col mt-2">
        <mat-label class="font-medium">{{ 'private.common.fileTypes.freeFile.label' | translate }}</mat-label>
        <div class="flex items-center mt-2">
            <button [disabled]="loading" class="bg-gray-200 w-1/6" mat-flat-button
                    (click)="toggleAllowFreeFiles()">
                {{ 'private.common.fileTypes.freeFile.' + (allowFreeFiles ? 'deactivate' : 'activate') | translate }}
            </button>
            <mat-progress-spinner class="ml-2" *ngIf="loading" [diameter]="24"
                                  mode="indeterminate"></mat-progress-spinner>
            <ng-container
                *ngIf="!loading">
                <p [innerHTML]="'private.common.fileTypes.freeFile.authorizeOrNot' | translate: {state: allowFreeFiles ? 'activé' : 'désactivé'}"
                   class="ml-2"></p>
            </ng-container>
        </div>
        <div class="flex my-2" *ngIf="errorMessagesFreeFiles?.length">
            <mat-error>
                <ul>
                    <li *ngFor="let errorMessageExtraFiles of errorMessagesFreeFiles">{{ errorMessageExtraFiles }}</li>
                </ul>
            </mat-error>
        </div>
    </div>
    <ng-container *ngIf="displayedFileTypes?.length">
        <div class="flex items-center mt-2 mb-1">
            <p class="font-medium">{{ 'private.common.fileTypes.table' | translate }}</p>
            <div *ngIf="isDefaultCertifierOrOrganism"
                 class="ml-auto -mr-4 card-loading-hidden">
                <button [matTooltip]="'private.common.fileTypes.add' | translate"
                        type="button"
                        mat-icon-button
                        [matMenuTriggerFor]="actionsMenu"
                        (click)="$event.stopPropagation()">
                    <mat-icon svgIcon="add_circle"></mat-icon>
                </button>
            </div>
            <mat-menu #actionsMenu="matMenu">
                <ng-template matMenuContent>
                    <button mat-menu-item [disabled]="loadingManageFileType"
                            (click)="addFileType(false)">
                        <span>{{ 'private.common.fileTypes.notGenerated' | translate }}</span>
                    </button>
                    <button mat-menu-item
                            [disabled]="!isDocumentApplicationAllowed || !isDocumentApplicationEnabled || loadingManageFileType"
                            (click)="openTemplatePicker()">
                        <span>{{ 'private.common.fileTypes.generated' | translate }}</span>
                    </button>
                </ng-template>
            </mat-menu>
        </div>
        <div class="max-h-100 overflow-auto">
            <table [dataSource]="displayedFileTypes"
                   class="table-fixed mb-5 "
                   mat-table>
                <ng-container matColumnDef="allowAttendeeOrPartner">
                    <th mat-header-cell
                        class="w-1/12"
                        *matHeaderCellDef></th>
                    <td *matCellDef="let fileType" mat-cell class="text-center relative">
                        <mat-icon [matTooltipPosition]="'above'"
                                  class="text-green {{ fileType.allowVisibilityPartner ? 'ico-attendee-both absolute': 'align-middle'}}"
                                  [matTooltip]="'private.common.fileTypes.form.' + (fileType.allowUploadAttendee ? 'allowUploadAttendee' : 'allowVisibilityAttendee') + '.help' | translate "
                                  *ngIf="fileType.allowVisibilityAttendee"
                                  [svgIcon]="fileType.allowUploadAttendee ? 'person_add' : 'person'"></mat-icon>
                        <mat-icon [matTooltipPosition]="'above'" color="primary"
                                  class="{{ fileType.allowVisibilityAttendee ? 'ico-partner-both absolute': 'align-middle'}}"
                                  [matTooltip]="'private.common.fileTypes.form.' + (fileType.allowUploadPartner ? 'allowUploadPartner' : 'allowVisibilityPartner') + '.help' | translate "
                                  *ngIf="fileType.allowVisibilityPartner"
                                  [svgIcon]="fileType.allowUploadPartner ? 'person_add' : 'person'"></mat-icon>
                    </td>
                </ng-container>
                <ng-container matColumnDef="name">
                    <th mat-header-cell
                        class="w-6/12"
                        *matHeaderCellDef>{{ 'private.common.certification.files.table.nom' | translate }}
                    </th>
                    <td *matCellDef="let fileType"
                        mat-cell
                        [matTooltip]="fileType.description">
                        <p class="truncate flex items-center">
                            <mat-icon [matTooltipPosition]="'above'" color="primary" class="mr-1"
                                      [matTooltip]="'private.common.fileTypes.isAutoGenerate'| translate"
                                      *ngIf="fileType.generated"
                                      [svgIcon]="'bolt'"></mat-icon>
                            <mat-icon [matTooltipPosition]="'above'" color="primary" class="mr-1"
                                      [matTooltip]="'private.common.files.signedState.title'| translate"
                                      *ngIf="isSignatureApplicationEnabled && (fileType.allowSignAttendee || fileType.allowSignPartner)"
                                      [svgIcon]="'draw'"></mat-icon>
                            {{ fileType.name }}
                        </p>
                        <p *ngIf="fileType.accept && !fileType.generated"
                           class="truncate text-secondary">
                            ({{ getAcceptI18n(fileType) }})
                        </p>
                    </td>
                </ng-container>
                <ng-container matColumnDef="toState">
                    <th mat-header-cell
                        class="w-4/12"
                        *matHeaderCellDef>{{ 'common.actions.search.state' | translate }}
                    </th>
                    <td *matCellDef="let fileType"
                        mat-cell>
                        <ng-container *ngIf="fileType.generated; else fileTypeNotGenerated">
                            <p [matTooltip]="getToolTip(fileType)"
                               class="truncate flex items-center">
                                {{(fileType.toState ? (toStateLabel + fileType.toState) : ('private.common.files.onRequestGenerated')) | translate }}
                            </p>
                        </ng-container>
                        <ng-template #fileTypeNotGenerated>
                            <p [matTooltip]="getToolTip(fileType)"
                               class="truncate flex items-center">
                                <ng-container *ngIf="fileType.toState; else noState">
                                    {{ toStateLabel + fileType.toState | translate }}
                                </ng-container>
                                <ng-template #noState>
                                    {{getLimitedDocumentInfo(fileType) ? '*' : '' }}
                                </ng-template>
                            </p>
                        </ng-template>
                        <p *ngIf="!fileType.enabled && fileType.generated"
                           class="text-secondary text-sm">{{'common.actions.inactif' | translate}}</p>
                    </td>
                </ng-container>
                <ng-container matColumnDef="actions">
                    <th mat-header-cell
                        class="w-1/12"
                        *matHeaderCellDef></th>
                    <td *matCellDef="let fileType"
                        mat-cell>
                        <mat-progress-spinner *ngIf="loadingManageFile; else showActions" [diameter]="20"
                                              mode="indeterminate">
                        </mat-progress-spinner>
                        <ng-template #showActions>
                            <app-file-type-menu *ngIf="isDefaultCertifierOrOrganism"
                                                [fileType]="fileType"
                                                [withButton]="false"
                                                [loading]="loadingManageFile"
                                                (openUpdateForm)="updateFileType($event)"
                                                (delete)="removeFileType($event)"
                                                (updateStateFromTable)="enabled($event.fileTypeToUpdate, $event.enabled)"
                                                (downloadTemplateExemple)="downloadTemplateExample($event)"
                            >
                            </app-file-type-menu>
                        </ng-template>
                    </td>
                </ng-container>
                <tr mat-row (click)="isDefaultCertifierOrOrganism && updateFileType(row)"
                    *matRowDef="let row; columns:displayedColumns"></tr>
                <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
            </table>
        </div>
    </ng-container>

    <div *ngIf="isDefaultCertifierOrOrganism" class="flex align-center mb-5 justify-center">
        <button type="button"
                class="bg-gray-200 flex ml-2"
                [matMenuTriggerFor]="otherOptions"
                mat-flat-button>
            {{ 'private.common.fileTypes.add' | translate }}
            <mat-icon class="icon ml-2" svgIcon="arrow_drop_down"></mat-icon>
        </button>
        <mat-menu #otherOptions="matMenu" xPosition="before">
            <ng-template matMenuContent>
                <button type="button"
                        [disabled]="loadingManageFileType"
                        mat-menu-item
                        (click)="addFileType(false)">
                    {{ 'private.common.fileTypes.notGenerated' | translate }}
                </button>
                <button type="button"
                        mat-menu-item
                        [disabled]="!isDocumentApplicationAllowed || !isDocumentApplicationEnabled || loadingManageFileType"
                        (click)="openTemplatePicker()">
                    {{ 'private.common.fileTypes.generated' | translate }}
                </button>
            </ng-template>
        </mat-menu>
    </div>

    <treo-message class="mt-3 mb-3"
                  *ngIf="isDocumentApplicationAllowed && !isDocumentApplicationEnabled"
                  type="info" [showIcon]="false" appearance="outline">
        <a href="/mes-applications">{{ 'private.common.fileTypes.form.generated.infoApplication' | translate }}</a>
    </treo-message>
</ng-template>
