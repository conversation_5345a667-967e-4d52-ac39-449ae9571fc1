import {Component, Inject, <PERSON><PERSON><PERSON><PERSON>, OnInit} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialog, MatDialogRef} from '@angular/material/dialog';
import {FormBuilder, FormGroup} from '@angular/forms';
import {AppFormFieldData} from '../../../material/app-form-field/app-form-field.component';
import {finalize, takeUntil} from 'rxjs/operators';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../../errors/errors.types';
import {TranslateService} from '@ngx-translate/core';
import {combineLatest, Observable, ReplaySubject, Subject} from 'rxjs';
import {CERTIFICATE_FILE_TYPE_ID, FileType} from '../../../api/models/file';
import {StateChoices} from '../../file-type-list/file-type-list.component';
import QualiopiIndicators from '../../../../../assets/qualiopi/indicateur.json';
import {Select} from '@ngxs/store';
import {SubscriptionState} from '../../../api/state/subscription.state';
import {Subscription, SubscriptionTypes} from '../../../api/models/subscription';
import {DocumentApplication} from '../../../../applications/document/document.application';
import {DialogUpgradeSubscriptionComponent} from '../../../subscription/dialog-upgrade-subscription/dialog-upgrade-subscription.component';
import {Organism} from '../../../api/models/organism';
import {OrganismState} from '../../../api/state/organism.state';
import {Download} from '../../../download/download';
import {FileIframeComponent} from '../file-iframe/file-iframe.component';
import {FileTypeService} from '../../../api/services/file-type.service';
import {Certification} from '../../../api/models/certification';
import {EnvironmentService} from '../../../environment/environment.service';
import {SignatureApplication} from '../../../../applications/signature/signature.application';
import {OrganismApplicationService} from '../../../api/services/organism-application.service';


const GENERATED_ACCEPT = ['.doc,.docx,.pdf'];

@Component({
    selector: 'app-dialog-file-type',
    templateUrl: './dialog-file-type.component.html',
    styleUrls: ['./dialog-file-type.component.scss']
})
export class DialogFileTypeComponent<T> implements OnInit, OnDestroy {

    loading = false;
    loadingExample = false;
    formGroup: FormGroup;
    errorMessages: string[] = [];
    appFormFieldsData: AppFormFieldData[][];
    appFormFieldsData$: ReplaySubject<AppFormFieldData> = new ReplaySubject<AppFormFieldData>();
    title: string;
    fileType: FileType<T>;
    qualiopiIndicators: typeof QualiopiIndicators;

    isDocumentApplicationAllowed: boolean;
    isSignatureApplicationEnabled: boolean;
    subscription: Subscription;
    organism: Organism;
    @Select(SubscriptionState.subscription) subscription$: Observable<Subscription>;
    @Select(OrganismState.organism) organism$: Observable<Organism>;

    private _unsubscribeAll: Subject<void> = new Subject();

    constructor(
        public dialogRef: MatDialogRef<DialogFileTypeComponent<T>>,
        public _formBuilder: FormBuilder,
        private _dialog: MatDialog,
        private _fileTypeService: FileTypeService<T>,
        private _translateService: TranslateService,
        private _environmentService: EnvironmentService,
        private _organismApplicationService: OrganismApplicationService,
        @Inject(MAT_DIALOG_DATA) public dialogData: {
            stateChoices?: StateChoices;
            fileType: FileType<T>;
            generated: boolean;
            allowExtraFields: boolean;
            entityField: string;
            entityClass?: string;
            entityId?: string;
            googleId?: string;
            tags?: string[];
            certifications?: Certification[];
            submitFileType(body: FileType<T>): Observable<FileType<T>>;
            downloadTemplateExample(fileType: FileType<T>): Observable<Download<Blob>>;
        },
    ) {
        this.qualiopiIndicators = QualiopiIndicators;
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    ngOnInit(): void {
        this.fileType = this.dialogData.fileType;
        const googleId = this.dialogData.googleId ?? this.fileType?.googleId;
        this.title = this._translateService.instant(this.fileType ? 'private.common.fileTypes.update' : 'private.common.fileTypes.add');
        this.title += ' - ' + this._translateService.instant('private.common.fileTypes.' + (this.isGenerated() ? 'generated' : 'notGenerated'));
        combineLatest([this.subscription$, this.organism$, this._organismApplicationService.get(SignatureApplication.appId())]).pipe(
            takeUntil(this._unsubscribeAll),
        ).subscribe(([subscription, organism, signatureApplication]) => {
            this.subscription = subscription;
            this.organism = organism;
            this.isDocumentApplicationAllowed = subscription.allowedApps?.includes(DocumentApplication.appId());
            this.isSignatureApplicationEnabled = signatureApplication.enabled;
            this.formGroup = this._formBuilder.group({
                fileType: this._formBuilder.group({})
            });

            const fileTypeAcceptChoices = [
                {key: this._translateService.instant('private.common.fileTypes.types.all'), value: '.*'},
                {key: this._translateService.instant('private.common.fileTypes.types.link'), value: 'link'},
                {key: this._translateService.instant('private.common.fileTypes.types.pdf'), value: '.pdf'},
                {key: this._translateService.instant('private.common.fileTypes.types.doc'), value: '.doc,.docx'},
                {key: this._translateService.instant('private.common.fileTypes.types.xls'), value: '.xls,.xlsx'},
                {key: this._translateService.instant('private.common.fileTypes.types.ppt'), value: '.ppt,.pptx'},
                {key: this._translateService.instant('private.common.fileTypes.types.txt'), value: '.txt'},
                {key: this._translateService.instant('private.common.fileTypes.types.img'), value: '.png,.jpg'},
                {
                    key: this._translateService.instant('private.common.fileTypes.types.archive'),
                    value: '.zip,.rar,.7z,.ace,.tar.gz'
                }
            ];

            let acceptArray = null;
            if (this.fileType) { // IS UPDATE
                acceptArray = [];
                // Convert a string to a split of values which can be paired (.ppt, .pptx) or single (.pdf)
                fileTypeAcceptChoices.forEach(choice => {
                    if (this.fileType.accept.indexOf(choice.value) !== -1) {
                        acceptArray.push(choice.value);
                    }
                });
            }
            const generated = this.isGenerated();
            const checkSignAttendee = (controlName, newValue, formData, formGroup) => {
                const appFormFieldAllowSignAttendee = formData.find(field => field.controlName === 'allowSignAttendee');
                const appFormFieldAllowVisibilityAttendee = formData.find(field => field.controlName === 'allowVisibilityAttendee');
                const appFormFieldAllowUploadAttendee = formData.find(field => field.controlName === 'allowUploadAttendee');
                const appFormFieldAllowAccept = appFormFieldsData['fileType'].find(appFormField => appFormField.controlName === 'accept');
                const changes = [];

                appFormFieldAllowSignAttendee.value = newValue;
                changes.push(appFormFieldAllowSignAttendee);
                if (newValue === true && appFormFieldAllowVisibilityAttendee.value !== true) {
                    appFormFieldAllowVisibilityAttendee.value = true;
                    changes.push(appFormFieldAllowVisibilityAttendee);
                }
                if (newValue === true && appFormFieldAllowUploadAttendee && appFormFieldAllowUploadAttendee.value === true) {
                    appFormFieldAllowUploadAttendee.value = false;
                    changes.push(appFormFieldAllowUploadAttendee);
                }
                if (newValue === true) {
                    appFormFieldAllowAccept.value = ['.doc,.docx', '.pdf'];
                    this.appFormFieldsData$.next(appFormFieldAllowAccept);
                } else {
                    if (!generated && !this.isFileTypeCertificate()) {
                        appFormFieldAllowAccept.removed = false;
                        appFormFieldAllowAccept.value = acceptArray;
                        this.appFormFieldsData$.next(appFormFieldAllowAccept);
                    }
                }
                return changes;
            };
            const checkSignPartner = (controlName, newValue, formData, formGroup) => {
                const appFormFieldAllowSignPartner = formData.find(field => field.controlName === 'allowSignPartner');
                const appFormFieldAllowVisibilityPartner = formData.find(field => field.controlName === 'allowVisibilityPartner');
                const appFormFieldAllowUploadPartner = formData.find(field => field.controlName === 'allowUploadPartner');
                const appFormFieldAllowAccept = appFormFieldsData['fileType'].find(appFormField => appFormField.controlName === 'accept');
                const changes = [];

                appFormFieldAllowSignPartner.value = newValue;
                changes.push(appFormFieldAllowSignPartner);
                if (newValue === true && appFormFieldAllowVisibilityPartner.value !== true) {
                    appFormFieldAllowVisibilityPartner.value = true;
                    changes.push(appFormFieldAllowVisibilityPartner);
                }
                if (newValue === true && appFormFieldAllowUploadPartner && appFormFieldAllowUploadPartner.value === true) {
                    appFormFieldAllowUploadPartner.value = false;
                    changes.push(appFormFieldAllowUploadPartner);
                }
                if (newValue === true) {
                    appFormFieldAllowAccept.value = ['.doc,.docx', '.pdf'];
                    this.appFormFieldsData$.next(appFormFieldAllowAccept);
                } else {
                    if (!generated && !this.isFileTypeCertificate()) {
                        appFormFieldAllowAccept.removed = false;
                        appFormFieldAllowAccept.value = acceptArray;
                        this.appFormFieldsData$.next(appFormFieldAllowAccept);
                    }
                }
                return changes;
            };
            const appFormFieldsData: AppFormFieldData[][] = [];
            appFormFieldsData['fileType'] = [
                {
                    controlName: 'googleId',
                    colSpan: 3,
                    type: 'action',
                    hideLabel: true,
                    actionText: 'common.actions.updateDocument',
                    removed: !googleId && !this.fileType?.generated,
                    actionMethod: () => this.openDocumentIframe(googleId)
                },
                {
                    controlName: 'name',
                    required: true,
                    floatLabel: 'always',
                    value: this.fileType?.name ?? null,
                    label: 'private.common.fileTypes.form.name.title',
                    placeholder: 'private.common.fileTypes.form.name.placeholder',
                    type: 'text',
                    disabled: this.isFileTypeCertificate(),
                    colSpan: 3
                },
                {
                    controlName: 'templateFile',
                    value: this.fileType?.templateFile ?? null,
                    label: 'private.common.fileTypes.form.template.title',
                    colSpan: 3,
                    type: 'file',
                    required: generated,
                    removed: this.isFileTypeCertificate() || this.fileType?.generated || !this.dialogData.generated || googleId,
                    chooseLabel: this._translateService.instant('common.actions.file.choose', {format: '.doc, .docx'}),
                    removeLabel: this._translateService.instant('common.actions.file.remove', {file: 'modèle'}),
                    removable: true,
                    showFilePreview: false,
                    fileTypesAccept: GENERATED_ACCEPT
                },
                {
                    controlName: 'accept',
                    value: acceptArray,
                    required: true,
                    label: 'private.common.fileTypes.form.accept.title',
                    placeholder: 'private.common.fileTypes.form.accept.placeholder',
                    type: 'select',
                    searchMultiple: true,
                    removed: generated || this.isFileTypeCertificate() || this.fileType?.allowSignAttendee || this.fileType?.allowSignPartner,
                    colSpan: 3,
                    choices: fileTypeAcceptChoices
                },
                {
                    controlName: 'description',
                    floatLabel: 'always',
                    value: this.fileType?.description ?? null,
                    label: 'private.common.fileTypes.form.description.title',
                    placeholder: 'private.common.fileTypes.form.description.placeholder',
                    type: 'textarea',
                    maxLength: 1000
                },
                {
                    controlName: 'toState',
                    removed: !this.dialogData.stateChoices || this.isFileTypeCertificate(),
                    value: this.fileType?.toState ?? null,
                    label: generated ? 'private.common.fileTypes.form.toState.generateTitle' : this._translateService.instant('private.common.fileTypes.form.toState.title', {
                            toState: this.dialogData.stateChoices.length === 1 ? this.dialogData.stateChoices[0].key : ''
                        }
                    ),
                    placeholder: generated ? 'private.common.fileTypes.form.toState.mandatoryState' : '',
                    type: 'text',
                    colSpan: 3
                },
                {
                    controlName: 'qualiopiIndicators',
                    label: 'private.common.fileTypes.form.qualiopiIndicators.title',
                    type: 'select',
                    removed: this.dialogData.entityField === 'certificationPartnerFileTypes',
                    searchMultiple: true,
                    colSpan: 3,
                    listChoices: this.qualiopiIndicators,
                    placeholder: 'private.common.fileTypes.form.qualiopiIndicators.placeholder',
                    help: 'private.common.fileTypes.form.qualiopiIndicators.help'
                },
                {
                    controlName: 'tags',
                    value: this.fileType?.tags ?? [],
                    label: 'common.tags.label',
                    type: 'tags',
                    help: 'private.common.fileTypes.form.tags.help',
                    colSpan: 3,
                    isCreateAvailable: true
                },
                {
                    removed: ['certificationPartnerFileTypes', 'certificationFolderFileTypes'].includes(this.dialogData.entityField),
                    controlName: 'certifications',
                    choices: this.dialogData.certifications ? this.dialogData.certifications.map((certification) => ({
                        key: certification.externalId + ' - ' + certification.name,
                        value: certification.certifInfo
                    })) : [],
                    searchMultiple: true,
                    label: 'private.common.fileTypes.form.certifications.title',
                    type: 'select',
                    colSpan: 3,
                    help: 'private.common.fileTypes.form.certifications.help',
                }
            ];
            const checkVisibilityPartner = (controlName, newValue, formData, formGroup) => {
                const appFormFieldAllowUploadPartner = formData.find(field => field.controlName === 'allowUploadPartner');
                const appFormFieldAllowVisibilityPartner = formData.find(field => field.controlName === 'allowVisibilityPartner');
                const appFormFieldAllowSignPartner = formData.find(field => field.controlName === 'allowSignPartner');
                const changes = [];

                appFormFieldAllowVisibilityPartner.value = newValue;
                changes.push(appFormFieldAllowVisibilityPartner);
                if (newValue !== true) {
                    appFormFieldAllowUploadPartner.value = false;
                    appFormFieldAllowSignPartner.value = false;
                    changes.push(appFormFieldAllowUploadPartner, appFormFieldAllowSignPartner);
                }
                return changes;
            };
            const checkUploadPartner = (controlName, newValue, formData, formGroup) => {
                const appFormFieldAllowUploadPartner = formData.find(field => field.controlName === 'allowUploadPartner');
                const appFormFieldAllowVisibilityPartner = formData.find(field => field.controlName === 'allowVisibilityPartner');
                const appFormFieldAllowSignPartner = formData.find(field => field.controlName === 'allowSignPartner');
                const changes = [];

                appFormFieldAllowUploadPartner.value = newValue;
                changes.push(appFormFieldAllowUploadPartner);
                if (newValue === true && appFormFieldAllowVisibilityPartner.value !== true) {
                    appFormFieldAllowVisibilityPartner.value = true;
                    changes.push(appFormFieldAllowVisibilityPartner);
                }
                if (newValue === true && appFormFieldAllowSignPartner && appFormFieldAllowSignPartner.value !== false) {
                    appFormFieldAllowSignPartner.value = false;
                    changes.push(appFormFieldAllowSignPartner);
                }
                return changes;
            };
            appFormFieldsData['visibilityPartner'] = [
                {
                    controlName: 'allowVisibilityPartner',
                    icon: 'person',
                    iconClass: 'text-primary',
                    removed: this.dialogData.entityClass !== 'Certification',
                    inline: true,
                    value: this.fileType?.allowVisibilityPartner ?? true,
                    label: 'private.common.fileTypes.form.allowVisibilityPartner.title',
                    help: 'private.common.fileTypes.form.allowVisibilityPartner.help',
                    type: 'radio',
                    colSpan: this.isSignatureApplicationEnabled ? 2 : 3,
                    change: checkVisibilityPartner,
                    choices: [
                        {key: this._translateService.instant('common.actions.yes'), value: true},
                        {key: this._translateService.instant('common.actions.no'), value: false}
                    ],
                },
                {
                    controlName: 'allowSignPartner',
                    icon: 'person',
                    iconClass: 'text-primary',
                    removed: this.dialogData.entityClass !== 'Certification' || !this.isSignatureApplicationEnabled,
                    inline: true,
                    value: this.fileType?.allowSignPartner ?? false,
                    label: 'private.common.fileTypes.form.allowSignPartner.title',
                    help: 'private.common.fileTypes.form.allowSignPartner.help',
                    type: 'radio',
                    colSpan: this.isSignatureApplicationEnabled ? 2 : 3,
                    change: checkSignPartner,
                    choices: [
                        {key: this._translateService.instant('common.actions.yes'), value: true},
                        {key: this._translateService.instant('common.actions.no'), value: false}
                    ],
                },
                {
                    controlName: 'allowUploadPartner',
                    type: 'radio',
                    icon: 'person_add',
                    iconClass: 'text-primary',
                    label: 'private.common.fileTypes.form.allowUploadPartner.title',
                    removed: generated || this.dialogData.entityClass !== 'Certification' || this.isFileTypeCertificate(),
                    colSpan: this.isSignatureApplicationEnabled ? 2 : 3,
                    value: this.fileType?.allowUploadPartner ?? true,
                    inline: true,
                    help: 'private.common.fileTypes.form.allowUploadPartner.help',
                    change: checkUploadPartner,
                    choices: [
                        {key: this._translateService.instant('common.actions.yes'), value: true},
                        {key: this._translateService.instant('common.actions.no'), value: false}
                    ]
                }
            ];
            const checkVisibilityAttendee = (controlName, newValue, formData, formGroup) => {
                const appFormFieldAllowUploadAttendee = formData.find(field => field.controlName === 'allowUploadAttendee');
                const appFormFieldAllowVisibilityAttendee = formData.find(field => field.controlName === 'allowVisibilityAttendee');
                const appFormFieldAllowSignAttendee = formData.find(field => field.controlName === 'allowSignAttendee');
                const changes = [];

                appFormFieldAllowVisibilityAttendee.value = newValue;
                changes.push(appFormFieldAllowVisibilityAttendee);
                if (newValue !== true) {
                    appFormFieldAllowUploadAttendee.value = false;
                    appFormFieldAllowSignAttendee.value = false;
                    changes.push(appFormFieldAllowUploadAttendee, appFormFieldAllowSignAttendee);
                }
                return changes;
            };
            const checkUploadAttendee = (controlName, newValue, formData, formGroup) => {
                const appFormFieldAllowUploadAttendee = formData.find(field => field.controlName === 'allowUploadAttendee');
                const appFormFieldAllowVisibilityAttendee = formData.find(field => field.controlName === 'allowVisibilityAttendee');
                const appFormFieldAllowSignAttendee = formData.find(field => field.controlName === 'allowSignAttendee');
                const changes = [];

                appFormFieldAllowUploadAttendee.value = newValue;
                changes.push(appFormFieldAllowUploadAttendee);
                if (newValue === true && appFormFieldAllowVisibilityAttendee.value !== true) {
                    appFormFieldAllowVisibilityAttendee.value = true;
                    changes.push(appFormFieldAllowVisibilityAttendee);
                }
                if (newValue === true && appFormFieldAllowSignAttendee && appFormFieldAllowSignAttendee.value !== false) {
                    appFormFieldAllowSignAttendee.value = false;
                    changes.push(appFormFieldAllowSignAttendee);
                }
                return changes;
            };
            appFormFieldsData['visibilityAttendee'] = [
                {
                    controlName: 'allowVisibilityAttendee',
                    type: 'radio',
                    icon: 'person',
                    label: 'private.common.fileTypes.form.allowVisibilityAttendee.title',
                    colSpan: this.isSignatureApplicationEnabled ? 2 : 3,
                    value: this.fileType?.allowVisibilityAttendee ?? false,
                    inline: true,
                    help: 'private.common.fileTypes.form.allowVisibilityAttendee.help',
                    change: checkVisibilityAttendee,
                    choices: [
                        {key: this._translateService.instant('common.actions.yes'), value: true},
                        {key: this._translateService.instant('common.actions.no'), value: false}
                    ]
                },
                {
                    controlName: 'allowSignAttendee',
                    type: 'radio',
                    icon: 'person',
                    label: 'private.common.fileTypes.form.allowSignAttendee.title',
                    colSpan: this.isSignatureApplicationEnabled ? 2 : 3,
                    value: this.fileType?.allowSignAttendee ?? false,
                    inline: true,
                    help: 'private.common.fileTypes.form.allowSignAttendee.help',
                    change: checkSignAttendee,
                    choices: [
                        {key: this._translateService.instant('common.actions.yes'), value: true},
                        {key: this._translateService.instant('common.actions.no'), value: false}
                    ],
                    removed: !this.isSignatureApplicationEnabled
                },
                {
                    controlName: 'allowUploadAttendee',
                    type: 'radio',
                    icon: 'person_add',
                    label: 'private.common.fileTypes.form.allowUploadAttendee.title',
                    colSpan: this.isSignatureApplicationEnabled ? 2 : 3,
                    removed: generated || this.isFileTypeCertificate(),
                    value: this.fileType?.allowUploadAttendee ?? false,
                    inline: true,
                    help: 'private.common.fileTypes.form.allowUploadAttendee.help',
                    change: checkUploadAttendee,
                    choices: [
                        {key: this._translateService.instant('common.actions.yes'), value: true},
                        {key: this._translateService.instant('common.actions.no'), value: false}
                    ]
                }
            ];

            const appFormFieldToStateIndex = appFormFieldsData['fileType'].findIndex((appFormFieldData) => appFormFieldData.controlName === 'toState');
            const appFormFieldToState = appFormFieldsData['fileType'][appFormFieldToStateIndex];

            if (this.dialogData.stateChoices.length === 1) {
                appFormFieldToState.type = 'checkbox';
            } else if (this.dialogData.stateChoices.length > 1) {
                appFormFieldToState.type = 'select';
                appFormFieldToState.choices = this.dialogData.stateChoices;
                appFormFieldToState.removable = true;
            }
            this.appFormFieldsData = appFormFieldsData;
        });
    }

    closeModal(): void {
        this.dialogRef.close();
        if (this.dialogData.googleId) {
            this._fileTypeService.deleteTemplate(this.dialogData.entityClass, this.dialogData.entityField).subscribe();
        }
    }

    updateState(enabled: boolean): void {
        this.submit(true, enabled);
    }

    submit(updateEnabledBody: boolean = false, enabled?: boolean, closeDialog: boolean = true): void {
        this.loading = true;
        this.errorMessages = [];
        let updatedFileType = this.formGroup.getRawValue().fileType;
        if (!this.isFileTypeCertificate()) {
            const generated = this.isGenerated();
            const accept = generated ? GENERATED_ACCEPT : updatedFileType?.accept;
            updatedFileType.accept = accept.join();
            updatedFileType = {...updatedFileType, generated: generated};
            if (updateEnabledBody) {
                updatedFileType.enabled = enabled;
            }

        }
        updatedFileType.tags = updatedFileType.tags ?? null;
        updatedFileType.certifications = updatedFileType.certifications ?? null;
        this.dialogData.submitFileType(updatedFileType).subscribe({
            next: (fileType) => {
                if (closeDialog) {
                    this.dialogRef.close({fileType: fileType});
                }
            },
            error: (httpErrorResponse: HttpErrorResponse) => {
                this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
            }
        }).add(() => this.loading = false);
    }

    downloadExample(): void {
        if (this.formGroup.dirty && !this.formGroup.invalid) {
            this.submit(false, null, false);
        }
        this.loading = true;
        this.loadingExample = true;
        this.dialogData.downloadTemplateExample(this.fileType).pipe(
            finalize(() => {
                this.loading = false;
                this.loadingExample = false;
            })
        ).subscribe({
            error: (httpErrorResponse: HttpErrorResponse) => {
                this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
            }
        });
    }

    openDialogSubscription(): void {
        const isTraining = this.dialogData.entityField === 'registrationFolderFileTypes';
        this._dialog.open(DialogUpgradeSubscriptionComponent, {
            panelClass: ['full-page-scroll-50'],
            data: {
                organism: this.organism,
                subscription: this.subscription,
                fromPage: isTraining ? 'documentTraining' : 'documentCertifier',
                subscriptionTypeToShow: isTraining ? SubscriptionTypes.TRAINING : SubscriptionTypes.CERTIFIER
            }
        });
    }

    showPreview(): boolean {
        return this.isGenerated() && (this.formGroup.getRawValue().fileType.templateFile || this.fileType?.templateFile);
    }

    isGenerated(): boolean {
        return this.fileType ? this.fileType.generated : this.dialogData.generated;
    }

    isFileTypeCertificate(): boolean {
        return this.fileType?.id === CERTIFICATE_FILE_TYPE_ID;
    }

    getTooltipPreview(): string {
        if (this.formGroup.invalid) {
            return 'private.common.fileTypes.form.toState.preview.formInvalid';
        } else if (!this.fileType) {
            return 'private.common.fileTypes.form.toState.preview.create';
        }
        return '';
    }

    openDocumentIframe(googleId: string): void {
        // TODO dirty we should use a promise
        if (this.formGroup.dirty && !this.formGroup.invalid) {
            this.submit(false, null, false);
        }

        let scope = this.isFileTypeCertificate() ? 'certificate' : null;
        if (!scope) {
            scope = this.dialogData.entityField.replace('FileTypes', '');
        }
        this._dialog.open(FileIframeComponent, {
            disableClose: true,
            width: '80%',
            data: {
                entityId: this.dialogData.entityId,
                templateEditor: {
                    templateGid: googleId,
                    layout: this.isFileTypeCertificate() ? 'presentation' : 'document',
                    type: 'document',
                    scope: scope,
                    additionalInformation: this.isFileTypeCertificate() ? 'private.certification.common.certificate.mandatory' : null,
                    contextId: this.isSignatureApplicationEnabled ? this.dialogData.entityClass + '_' + this.dialogData.entityId + '_' + this.fileType.id : null
                }
            }
        });
    }
}
