<app-dialog-layout
    [title]="title"
    [showCancel]="false"
    (dialogClose)="closeModal()">
    <form class="flex flex-col" *ngIf="formGroup" [formGroup]="formGroup">

        <ng-container *ngIf="!isFileTypeCertificate()">
            <treo-message class="mb-3 underline"
                          *ngIf="!isDocumentApplicationAllowed"
                          (click)="openDialogSubscription()"
                          type="info" [showIcon]="false" appearance="outline">
                {{ 'private.common.fileTypes.form.generated.infoSubscribe' | translate }}
            </treo-message>

            <treo-message class="mb-3"
                          *ngIf="isGenerated()"
                          type="info" [showIcon]="false" appearance="outline">
                <a href="/aide/guides/applications/documents"
                   target="_blank"> {{'private.common.fileTypes.documentation' | translate}}</a>
            </treo-message>
        </ng-container>

        <app-form-fields formGroupName="fileType"
                         class="grid grid-cols-6 gap-4"
                         [entity]="fileType"
                         [appFormFieldsData]="appFormFieldsData['fileType']"
                         [formGroup]="formGroup"></app-form-fields>

        <fieldset class="p-2 fieldset fieldset-primary"
                  *ngIf="dialogData.allowExtraFields && dialogData.entityClass === 'Certification'">
            <legend
                class="px-1 self-center font-medium text-primary"
            >
                {{ 'private.common.fileTypes.form.partnerTitle' | translate }}
            </legend>
            <app-form-fields formGroupName="fileType"
                             class="grid grid-cols-6 gap-4"
                             [entity]="fileType"
                             [appFormFieldsData]="appFormFieldsData['visibilityPartner']"
                             [appFormFieldData$]="appFormFieldsData$"
                             [formGroup]="formGroup"></app-form-fields>
        </fieldset>
        <fieldset class="p-2 mb-4 fieldset fieldset-green" *ngIf="dialogData.allowExtraFields && dialogData.entityField !== 'certificationPartnerFileTypes'">
            <legend
                class="px-1 self-center font-medium text-green">
                {{
                (dialogData.entityField == 'certificationFolderFileTypes'
                    ? 'private.common.fileTypes.form.attendeeTitleCertificationEntity'
                    : 'private.common.fileTypes.form.attendeeTitleRegistrationEntity') | translate
                }}
            </legend>
            <app-form-fields formGroupName="fileType"
                             class="grid grid-cols-6 gap-4"
                             [entity]="fileType"
                             [appFormFieldsData]="appFormFieldsData['visibilityAttendee']"
                             [appFormFieldData$]="appFormFieldsData$"
                             [formGroup]="formGroup"></app-form-fields>
        </fieldset>

        <div *ngIf="errorMessages?.length" class="mt-3 flex items-center">
            <treo-message class="flex-auto" appearance="outline" [showIcon]="false" type="error">
                <ul>
                    <li *ngFor="let errorMessage of errorMessages">
                        {{ errorMessage }}
                    </li>
                </ul>
            </treo-message>
        </div>
        <div class="flex flex-row py-2 mb-2" [class]="showPreview() ? 'justify-between' : 'justify-end'">
            <button *ngIf="showPreview()"
                    [disabled]="formGroup.invalid || loading || !fileType"
                    [matTooltip]="getTooltipPreview() | translate"
                    (click)="downloadExample()"
                    mat-flat-button
                    class="ml-3 bg-gray-200"
                    type="button">
                <ng-container *ngIf="!loadingExample">
                    <mat-icon svgIcon="visibility" class="pr-1"></mat-icon>
                    {{ 'common.actions.file.preview' | translate }}
                </ng-container>
                <mat-progress-spinner class="mr-4"
                                      *ngIf="loadingExample" [diameter]="24"
                                      mode="indeterminate"></mat-progress-spinner>
                <mat-icon *ngIf="fileType && formGroup.dirty"
                          svgIcon="help_outline"
                          [matTooltip]="'private.common.fileTypes.form.toState.preview.update' | translate"
                          class="pl-1"></mat-icon>
            </button>
            <div class="flex flex-row">
                <button type="button" mat-button (click)="closeModal()">
                    {{ 'common.actions.cancel' | translate }}
                </button>
                <ng-container *ngIf="fileType; else showCreate">
                    <app-file-type-menu [formGroup]="formGroup"
                                        [fileType]="fileType"
                                        [loading]="loading"
                                        (updateFileType)="submit()"
                                        (updateState)="updateState($event)"
                                        class="ml-3"
                    ></app-file-type-menu>
                </ng-container>
                <ng-template #showCreate>
                    <button (click)="submit()"
                            [disabled]="loading || !formGroup.dirty || formGroup.invalid"
                            type="submit"
                            class="ml-3"
                            color="primary"
                            mat-flat-button>
                        <mat-progress-spinner class="mr-4" *ngIf="loading" [diameter]="24"
                                              mode="indeterminate"></mat-progress-spinner>
                        <ng-container
                            *ngIf="!loading">{{ 'common.actions.add' | translate }}
                        </ng-container>
                    </button>
                </ng-template>
            </div>
        </div>
    </form>
</app-dialog-layout>
