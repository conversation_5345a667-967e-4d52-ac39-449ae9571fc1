import {Component, EventEmitter, Input, Output} from '@angular/core';
import {FormGroup} from '@angular/forms';
import {File, FileStates} from '../../../api/models/file';

@Component({
    selector: 'app-file-menu',
    templateUrl: './file-menu.component.html',
    styleUrls: ['./file-menu.component.scss']
})
export class FileMenuComponent {

    fileStates = FileStates;

    @Input() file: File;
    @Input() formGroup: FormGroup;
    @Input() canUpdateFileState?: boolean;
    @Output() processedFile: EventEmitter<{ fileId: number, state?: FileStates, comment?: string }> = new EventEmitter<{ fileId: number, state?: FileStates, comment?: string }>();

    constructor() {
    }

    updateFile(fileId: number, state?: FileStates, update: boolean = false): void {
        const comment = this.formGroup.getRawValue().file.comment;
        this.processedFile.emit(update ? {fileId, state, comment} : {fileId, state});
    }


}
