<div class="flex align-center">
    <div *ngIf="!formGroup?.dirty && canUpdateFileState">
        <ng-container *ngIf="file.state === fileStates.VALID">
            <button (click)="updateFile(file.id, fileStates.TO_REVIEW)"
                    type="button"
                    class="bg-orange button-actions"
                    mat-flat-button>
                <mat-icon [svgIcon]="'feather:alert-circle'" class="white-icon mr-2"></mat-icon>
                <span class="text-white">{{'private.common.files.state.toFileState.toReview' | translate}}</span>
            </button>
        </ng-container>
        <ng-container *ngIf="file.state === fileStates.REFUSED || file.state === fileStates.TO_REVIEW">
            <button (click)="updateFile(file.id, fileStates.VALID)"
                    type="button"
                    color="primary"
                    class="button-actions"
                    mat-flat-button>
                <mat-icon class="mr-2" [svgIcon]="'feather:check-circle'"></mat-icon>
                <span>{{'private.common.files.state.toFileState.valid' | translate}}</span>
            </button>
        </ng-container>
    </div>

    <button *ngIf="formGroup?.dirty"
            (click)="updateFile(file.id, null, true)"
            type="button"
            color="primary"
            [class]="canUpdateFileState ? 'button-actions' : 'button-actionsOnly'"
            mat-flat-button>
        <span>{{ 'common.actions.update' | translate}}</span>
    </button>

    <button *ngIf="canUpdateFileState"
            [class]="file.state === fileStates.VALID && !formGroup.dirty ? 'bg-orange button-arrow button-arrow-orange flex justify-center items-center' : 'button-arrow button-arrow-primary flex justify-center items-center' "
            color="primary"
            (click)="$event.stopPropagation()"
            mat-flat-button
            [matMenuTriggerFor]="actionsMenu"
            title="Actions"
            type="button">
        <mat-icon class="icon" svgIcon="arrow_drop_down"></mat-icon>
    </button>
</div>

<mat-menu #actionsMenu="matMenu" class="large-menu">
    <ng-template matMenuContent>
        <ng-container *ngIf="canUpdateFileState">
            <button
                *ngIf="!formGroup?.dirty && (file.state === fileStates.REFUSED || file.state === fileStates.TO_REVIEW)"
                (click)="updateFile(file.id, fileStates.VALID)" type="button"
                mat-menu-item>
                <mat-icon color="primary" [svgIcon]="'feather:check-circle'"></mat-icon>
                <span>{{'private.common.files.state.toFileState.valid' | translate}}</span>
            </button>
            <button
                *ngIf="formGroup?.dirty && (file.state === fileStates.REFUSED || file.state === fileStates.TO_REVIEW)"
                (click)="updateFile(file.id, fileStates.VALID, true)" type="button"
                mat-menu-item>
                <mat-icon color="primary" [svgIcon]="'feather:check-circle'"></mat-icon>
                <span>{{'common.actions.updateAnd' | translate}} {{'private.common.files.state.toFileState.valid' | translate}}</span>
            </button>
            <button *ngIf="!formGroup?.dirty && (file.state === fileStates.VALID || file.state === fileStates.REFUSED)"
                    (click)="updateFile(file.id, fileStates.TO_REVIEW)" type="button"
                    mat-menu-item>
                <mat-icon class="text-orange" [svgIcon]="'feather:alert-circle'"></mat-icon>
                <span>{{'private.common.files.state.toFileState.toReview' | translate}}</span>
            </button>
            <button *ngIf="formGroup?.dirty && (file.state === fileStates.VALID || file.state === fileStates.REFUSED)"
                    (click)="updateFile(file.id, fileStates.TO_REVIEW, true)" type="button"
                    mat-menu-item>
                <mat-icon class="text-orange" [svgIcon]="'feather:alert-circle'"></mat-icon>
                <span>{{'common.actions.updateAnd' | translate}} {{'private.common.files.state.toFileState.toReview' | translate}}</span>
            </button>
            <button
                *ngIf="!formGroup?.dirty && (file.state === fileStates.VALID || file.state === fileStates.TO_REVIEW)"
                (click)="updateFile(file.id, fileStates.REFUSED) " type="button"
                mat-menu-item>
                <mat-icon color="warn" [svgIcon]="'feather:x-circle'"></mat-icon>
                <span>{{'private.common.files.state.toFileState.refused' | translate}}</span>
            </button>
            <button *ngIf="formGroup?.dirty && (file.state === fileStates.VALID || file.state === fileStates.TO_REVIEW)"
                    (click)="updateFile(file.id, fileStates.REFUSED, true)" type="button"
                    mat-menu-item>
                <mat-icon color="warn" [svgIcon]="'feather:x-circle'"></mat-icon>
                <span>{{'common.actions.updateAnd' | translate}} {{'private.common.files.state.toFileState.refused' | translate}}</span>
            </button>
        </ng-container>
    </ng-template>
</mat-menu>
