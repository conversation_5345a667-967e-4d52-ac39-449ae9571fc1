<app-dialog-layout (dialogClose)="closeModal()"
                   [cancelText]="'common.actions.close' | translate"
                   [title]=" dialogData.title ? dialogData.title : 'common.actions.file.updateOnline' | translate"
                   [title2]="'common.actions.file.autoUpdate' | translate"
                   style="height: 100%;">
    <div class="flex flex-1 flex-row flex-wrap">
        <div
            [class]="dialogData.templateEditor ? 'flex w-full pr-2 pb-2 md:w-3/4 lg:w-3/4 xl:w-3/4 2xl:w-3/4' : 'flex w-full pr-2 pb-2'">
            <div class="flex flex-1 file-link flex-wrap content-center"></div>
            <iframe [src]="src" class="w-full"></iframe>
        </div>

        <div class="flex flex-col w-full md:w-1/4 lg:w-1/4 xl:w-1/4 2xl:w-1/4 pl-2 pb-2"
             *ngIf="dialogData.templateEditor">
            <app-template-editor [type]="dialogData.templateEditor.type"
                                 [scope]="dialogData.templateEditor.scope"
                                 [contextId]="dialogData.templateEditor.contextId"
                                 [additionalInformation]="dialogData.templateEditor.additionalInformation"
            ></app-template-editor>
        </div>
    </div>
</app-dialog-layout>
