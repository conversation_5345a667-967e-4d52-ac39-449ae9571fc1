import {Component, Inject, Injector} from '@angular/core';
import {AbstractDialogFormComponent} from '../../../material/form/abstract-dialog-form.component';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material/dialog';
import {DomSanitizer, SafeUrl} from '@angular/platform-browser';
import {TemplateEditorOptions} from '../../../material/template-editor/template-editor.component';

@Component({
    selector: 'app-file-iframe',
    templateUrl: './file-iframe.component.html',
    styleUrls: ['./file-iframe.component.scss']
})
export class FileIframeComponent<T> extends AbstractDialogFormComponent<void> {

    src: SafeUrl;

    constructor(injector: Injector,
                sanitizer: DomSanitizer,
                public dialogRef: MatDialogRef<FileIframeComponent<T>>,
                @Inject(MAT_DIALOG_DATA) public dialogData: {
                    url: string,
                    templateEditor?: TemplateEditorOptions,
                    entityId: number,
                    title?: string
                }) {
        super(injector);
        if (this.dialogData.templateEditor) {
            this.src = sanitizer.bypassSecurityTrustResourceUrl(
                'https://docs.google.com/' + (this.dialogData.templateEditor.layout ?? 'document') + '/d/' + this.dialogData.templateEditor.templateGid + '/edit?rm=demo'
            );
        } else {
            this.src = sanitizer.bypassSecurityTrustResourceUrl(this.dialogData.url);
        }
    }

    protected initForm(model: void | undefined): void {
    }

    submit(): void {
    }

    closeModal(): void {
        this.dialogRef.close();
    }

}
