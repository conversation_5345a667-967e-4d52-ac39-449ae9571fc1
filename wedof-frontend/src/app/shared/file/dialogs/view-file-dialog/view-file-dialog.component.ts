import {Component, Inject, Injector} from '@angular/core';
import {MAT_DIALOG_DATA} from '@angular/material/dialog';
import {FileService} from '../../../api/services/file.service';
import {AbstractDialogFormComponent} from '../../../material/form/abstract-dialog-form.component';
import {AuthService} from '../../../../core/auth/auth.service';
import {DocumentSignedStates, File, FileStates} from '../../../api/models/file';
import {HttpClient} from '@angular/common/http';
import {DomSanitizer, SafeHtml} from '@angular/platform-browser';
import {Observable} from 'rxjs';
import {TranslateService} from '@ngx-translate/core';
import {ThemePalette} from '@angular/material/core';
import {LinkTarget, pdfDefaultOptions} from 'ngx-extended-pdf-viewer';
import {AppFormFieldData} from '../../../material/app-form-field/app-form-field.component';
import {FormBuilder, FormGroup} from '@angular/forms';

interface FileDialogData {
    file: File;
    auth: true;
    files: File[];
    src: (File) => string;
    title?: (File) => string;
    callbackSign?: (File) => string;
    isOwner?: boolean;
    entityApiPath?: string;
    manageFile?: boolean;
    canUpdateFileState?: (File) => boolean;

    updateFile(fileId, body): Observable<File>;
}

@Component({
    selector: 'view-file-dialog',
    templateUrl: './view-file-dialog.component.html',
    styleUrls: ['./view-file-dialog.component.scss']
})
export class ViewFileDialogComponent extends AbstractDialogFormComponent<void> {

    file: File;
    files: File[];
    title: string;
    nextFile: File;
    fileSrc: string;
    html: SafeHtml;
    httpHeaders: {};
    isIframe = false;
    previousFile: File;
    appFormFieldsData: AppFormFieldData[];
    formGroup: FormGroup;
    showValidationSide = false;
    canUpdateFileState: boolean;
    fileSignedStates = DocumentSignedStates;

    constructor(
        injector: Injector,
        private sanitizer: DomSanitizer,
        private _httpClient: HttpClient,
        private _formBuilder: FormBuilder,
        private _authService: AuthService,
        private _fileService: FileService,
        private _translateService: TranslateService,
        @Inject(MAT_DIALOG_DATA) public dialogData: FileDialogData,
    ) {
        super(injector);
        this.setFile(dialogData.file);
        this.initFormComment();
        pdfDefaultOptions.externalLinkTarget = LinkTarget.BLANK;
    }

    submit(): void {
    }

    initFormComment(): void {
        if (this.dialogData.isOwner) {
            this.formGroup = this._formBuilder.group({
                file: this._formBuilder.group({})
            });
            const roles = this.dialogData.entityApiPath === 'registrationFolders' ? 'de l\'apprenant' : this.dialogData.entityApiPath === 'certificationFolders' ? 'du candidat et/ou du partenaire ' : 'du partenaire';
            this.appFormFieldsData = [
                {
                    controlName: 'comment',
                    label: 'private.common.files.form.label',
                    placeholder: this._translateService.instant('private.common.files.form.comment', {roles: roles}),
                    type: 'textarea',
                    rows: 4
                }
            ];
        }
    }

    setFile(file: File): void {
        if (this.dialogData.isOwner) {
            this.formGroup.reset();
        }
        if (file.link && (this.isLink(file) || this.isSign(file))) {
            this._httpClient.get(this.getFileSrc(file)).subscribe(data => {
                if (data) {
                    // important to setup showSignatureSettingsForm
                    if (!file.id && data['id']) {
                        file.id = data['id'];
                    }
                    this.html = this.sanitizer.bypassSecurityTrustHtml(data['html']);
                    this.isIframe = data['html'].includes('<iframe');
                    this.refreshDialogData(file);
                }
            });
        } else {
            this.refreshDialogData(file);
        }
    }

    downloadFile(): void {
        this._fileService.download(this.fileSrc, this.file.fileName).subscribe();
    }

    isImage(file: File): boolean {
        return file?.fileType.startsWith('image/');
    }

    isVideo(file: File): boolean {
        return file?.fileType.startsWith('video/');
    }

    isPdf(file: File): boolean {
        return file?.fileType === 'application/pdf';
    }

    isSign(file: File): boolean {
        return file?.fileType === 'sign';
    }

    isLink(file: File): boolean {
        return file?.fileType === 'link';
    }

    protected initForm(model: void | undefined): void {
    }

    private refreshDialogData(file: File): void {
        this.dialogData.file = file;
        this.file = this.dialogData.file;
        this.files = this.dialogData.files;
        this.title = this.dialogData.title ? this.dialogData.title(this.file) : this.file.fileName;
        this.fileSrc = this.file.link ? null : this.getFileSrc(this.file);
        this.canUpdateFileState = this.dialogData.canUpdateFileState ? this.dialogData.canUpdateFileState(this.file) : true;
        this.nextFile = this.files && typeof this.files[this.files.indexOf(this.file) + 1] !== 'undefined' ? this.files[this.files.indexOf(this.file) + 1] : null;
        this.previousFile = this.files && typeof this.files[this.files.indexOf(this.file) - 1] !== 'undefined' ? this.files[this.files.indexOf(this.file) - 1] : null;
        this.showValidationSide = this.dialogData.manageFile ?? !(typeof this.file.state === 'undefined' || this.file.state === null);
    }

    private getFileSrc(file): string {
        const link = this.dialogData.src(file);
        const url = new URL(link.startsWith('http') ? link : (window.location.origin + link));
        if (this.dialogData.auth) {
            url.searchParams.append('token', this._authService.accessToken);
        }
        if (this._authService.switchUser) {
            url.searchParams.append('_switch_user', this._authService.switchUser);
        }
        return url.toString();
    }

    updateFileState(fileId: number, state?: FileStates, comment?: string): void {
        this.dialogData.updateFile(fileId, {state: state, comment: comment}).subscribe((file) => {
                this.dialogData.file = file;
                this.file = this.dialogData.file;
                const fileUpdated = this.dialogData.files.find((dialogDataFile) => dialogDataFile.id === file.id);
                fileUpdated.state = file.state;
                fileUpdated.comment = file.comment;
                this.files = this.dialogData.files;
                this.initFormComment();
                if (this.nextFile && state) { // don't call setFile if we only changed the comment
                    this.setFile(this.nextFile);
                }
            }
        );
    }

    getConfigurationIcon(fileState: string): { icon: string, color: ThemePalette, class: string, tooltip: string } {
        return {
            icon: fileState === FileStates.VALID ? 'feather:check-circle' : fileState === FileStates.TO_REVIEW ? 'feather:alert-circle' : 'feather:x-circle',
            tooltip: this._translateService.instant('private.common.files.state.tooltip.' + fileState),
            color: fileState === FileStates.REFUSED ? 'warn' : undefined,
            class: fileState === FileStates.VALID ? 'mr-2 text-green' : fileState === FileStates.TO_REVIEW ? 'mr-2 text-orange' : 'mr-2',
        };
    }

    setupSignCallbacks(): void {
        const selector = document.querySelector('#signForm');
        if (selector) {
            selector.addEventListener('completed', (e: any) => {
                // e.detail = data from sign
                this.dialogData.updateFile(this.file.id, e.detail).subscribe((file) => {
                    if (file) {
                        this.dialogData.file = file;
                        this.file = this.dialogData.file;
                        const fileToUpdate = this.dialogData.files.find((dialogDataFile) => dialogDataFile.id === file.id);
                        Object.assign(fileToUpdate, file);
                        this.files = this.dialogData.files;
                        this.setFile(file);
                    }
                });
            });
        }
    }
}
