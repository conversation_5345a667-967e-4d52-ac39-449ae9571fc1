<app-dialog-layout (dialogClose)="close()"
                   [cancelText]="'common.actions.close' | translate"
                   [title]="title"
                   [configurationIcon]="file?.state && getConfigurationIcon(file.state)"
                   [actions]="actions"
                   style="height: 100%;">
    <div class="flex flex-1 flex-row flex-wrap" [ngClass]="{'sign-content':isSign(file)}">
        <div class="flex w-full pr-2 pb-2"
             [ngClass]="{'md:w-3/4 lg:w-3/4 xl:w-3/4 2xl:w-3/4':showValidationSide && !isSign(file)}">
            <div *ngIf="isSign(file)" appInnerHtmlRendered (innerHtmlRendered)="setupSignCallbacks()" [innerHTML]="html"
                 class="flex-1 file"></div>
            <div *ngIf="isLink(file)" [innerHTML]="html"
                 class="flex flex-1 file-link flex-wrap content-center"
                 [ngClass]="{'center-button':!isIframe, 'mb-4':isIframe}"></div>
            <div *ngIf="isPdf(file)" class="flex-1 file">
                <ngx-extended-pdf-viewer
                    [delayFirstView]="1000"
                    [enableDragAndDrop]="false"
                    [httpHeaders]="httpHeaders"
                    [language]="'fr-FR'"
                    [showBookmarkButton]="false"
                    [showDownloadButton]="false"
                    [showOpenFileButton]="false"
                    [showPrintButton]="true"
                    [showSecondaryToolbarButton]="false"
                    [src]="fileSrc"
                    [height]="'88%'"
                    [zoom]="'page-fit'">
                </ngx-extended-pdf-viewer>
            </div>
            <div *ngIf="isImage(file)" class="flex-1 file">
                <img [src]="fileSrc" [alt]="file.fileName" class="img-file"/>
            </div>
            <div *ngIf="isVideo(file)" class="flex flex-1 file-link flex-wrap content-center">
                <video [src]="fileSrc" controls height="100%" class="m-auto">
                    <source [src]="fileSrc" [type]="file.fileType">
                </video>
            </div>
            <div
                *ngIf="file && !isLink(file) && !isPdf(file) && !isImage(file) && !isVideo(file) && !isSign(file)"
                class="flex file center-button flex-wrap content-center">
                <button mat-flat-button color="primary"
                        (click)="downloadFile()">{{ 'private.common.files.download' | translate }}
                </button>
            </div>
        </div>
        <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4 2xl:w-1/4 pl-2 pb-2" *ngIf="showValidationSide">
            <div class="flex flex-col">
                <p *ngIf="file?.state && canUpdateFileState" class="mb-4"
                   [innerHTML]="'private.common.files.state.explanation.' + file.state | translate">
                </p>
                <treo-message *ngIf="!dialogData.isOwner && file.comment" class="mb-2"
                              appearance="outline" [showIcon]="false" type="error">
                    {{ file.comment }}
                </treo-message>

                <ng-container *ngIf="dialogData.isOwner && file">
                    <form [formGroup]="formGroup" class="flex flex-col mb-2">
                        <app-form-fields formGroupName="file"
                                         [entity]="file"
                                         [appFormFieldsData]="appFormFieldsData"
                                         [formGroup]="formGroup">
                        </app-form-fields>
                    </form>
                    <div class="flex justify-between">
                        <button (click)="setFile(previousFile)"
                                [matTooltip]="previousFile ? ('private.common.files.previous' | translate) : '' "
                                [disabled]="!previousFile" class="self-start mr-4" mat-icon-button
                                type="button">
                            <mat-icon svgIcon="chevron_left"></mat-icon>
                        </button>
                        <app-file-menu [formGroup]="formGroup" [file]="file"
                                       [canUpdateFileState]="canUpdateFileState"
                                       (processedFile)="updateFileState($event.fileId, $event.state, $event.comment)"></app-file-menu>
                        <button (click)="setFile(nextFile)"
                                [matTooltip]="nextFile ? ('private.common.files.next' | translate) : '' "
                                [disabled]="!nextFile" class="self-start ml-4" mat-icon-button
                                type="button">
                            <mat-icon svgIcon="chevron_right"></mat-icon>
                        </button>
                    </div>
                </ng-container>
            </div>
        </div>
    </div>
    <ng-template #actions>
        <a mat-flat-button color="primary" *ngIf="file && isLink(file)" [href]="file.link"
           target="_blank">{{ 'private.common.files.open' | translate }}</a>
        <button mat-flat-button color="primary" *ngIf="file && !isLink(file)"
                [disabled]="file.signedState === fileSignedStates.PARTIALLY || file.signedState === fileSignedStates.NONE"
                [matTooltip]="(file.signedState === fileSignedStates.PARTIALLY || file.signedState === fileSignedStates.NONE ? 'private.common.files.menu.downloadUnavailable' : '') | translate"
                (click)="downloadFile()">{{ 'private.common.files.download' | translate }}
        </button>
    </ng-template>
</app-dialog-layout>
