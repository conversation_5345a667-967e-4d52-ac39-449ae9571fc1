:host {
    ngx-extended-pdf-viewer {
        position: absolute;
        top: 0;
        right: 0;
        left: 0;
        bottom: 0;
    }

    .img-file {
        max-height: 100%;
        align-self: center;
        width: auto;
        margin-left: auto;
        margin-right: auto;
    }

    .file {
        position: relative;
        min-height: 0;
    }

    .center-button {
        margin-left: auto;
        margin-right: auto;
    }

    ::ng-deep {
        .zoom, {
            position: absolute;
            top: 0;
            right: 0;
            left: 0;
            bottom: 0;
        }

        code-input {
            --item-spacing: 10px;
            --item-height: 5em;
            --item-border: 2px solid #dddddd;
            --item-border-has-value: 2px solid #dddddd;
            --item-border-focused: 2px solid #76a9fa;
            --item-border-bottom-focused: 2px solid #76a9fa;
            --item-border-radius: 10px;
            --item-shadow-focused: none;

            input {
                &.has-value {
                    text-transform: uppercase;
                }
            }
        }
    }

    ::ng-deep {
        code-input {
            --item-spacing: 10px;
            --item-height: 3em;
            --item-border: none;
            --item-border-bottom: 2px solid #dddddd;
            --item-border-has-value: none;
            --item-border-bottom-has-value: 2px solid #5850ec;
            --item-border-focused: none;
            --item-border-bottom-focused: 2px solid #5850ec;
            --item-shadow-focused: none;
            --item-border-radius: 0px;
        }
    }
}
