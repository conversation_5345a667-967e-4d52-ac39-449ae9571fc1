import {Component, Inject} from '@angular/core';
import {File} from '../../../api/models/file';
import {AuthService} from '../../../../core/auth/auth.service';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material/dialog';
import {SnackBarComponent} from '../../../material/snack-bar/snack-bar.component';
import {displayTextSnackBar} from '../../../utils/displayTextSnackBar';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../../errors/errors.types';
import {MatSnackBar} from '@angular/material/snack-bar';
import {TranslateService} from '@ngx-translate/core';
import {SignDocumentService} from '../../../api/services/sign-document.service';
import {LinkTarget, pdfDefaultOptions} from 'ngx-extended-pdf-viewer';

@Component({
    selector: 'app-sign-document-dialog',
    templateUrl: './sign-document-dialog.component.html',
    styleUrls: ['./sign-document-dialog.component.scss']
})
export class SignDocumentDialogComponent {

    messages: Array<string>;
    file: File;
    fileSrc: Blob;
    errorMessages: string[] = [];
    showInputCode = false;
    showMessage: string = null;
    signStatus = false;

    constructor(
        private _authService: AuthService,
        private _snackBar: MatSnackBar,
        private _translateService: TranslateService,
        private _signDocumentService: SignDocumentService,
        public dialogRef: MatDialogRef<SignDocumentDialogComponent>,
        @Inject(MAT_DIALOG_DATA) public dialogData: {
            documentName: string,
            documentId: string,
            params: any
        }
    ) {
        pdfDefaultOptions.externalLinkTarget = LinkTarget.BLANK;
        this._signDocumentService.preview(this.dialogData.documentId, this.dialogData.params).subscribe({
            next: (response) => {
                this.refreshDialogData(response.body, true, response.body);
            }
        });
    }

    generateCode(): void {
        this.errorMessages = [];
        this._signDocumentService.generateCode(this.dialogData.documentId, this.dialogData.params).subscribe({
            next: () => {
                this._snackBar.openFromComponent(
                    SnackBarComponent,
                    displayTextSnackBar(this._translateService.instant('common.actions.signDocument.emailSent'))
                );
            },
            error: (httpErrorResponse: HttpErrorResponse) => {
                this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
            }
        });
    }

    private refreshDialogData(file: File, askForCode: boolean, fileSrc?: Blob): void {
        this.file = file;
        this.fileSrc = fileSrc;
        if (askForCode) {
            this.showInputCode = true;
            this.generateCode();
        }
    }

    onCodeCompleted(code: string): void {
        this.errorMessages = [];
        this.showInputCode = false;
        this.showMessage = 'common.actions.signDocument.validation';
        this._signDocumentService.sign(this.dialogData.documentId, {...this.dialogData.params, ...{'code': code}}).subscribe({
            next: (result) => {
                if (result) {
                    this.file = null;
                    this.fileSrc = null;
                    this.signStatus = true;
                    this.showMessage = 'common.actions.signDocument.success';
                    this.dialogRef.close({result: this.signStatus});
                } else {
                    this.showMessage = null;
                    this.showInputCode = true;
                    this.errorMessages = ['Erreur, le code ne correspond pas'];
                }
            },
            error: (httpErrorResponse: HttpErrorResponse) => {
                this.showInputCode = false;
                this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
            }
        });
    }
}
