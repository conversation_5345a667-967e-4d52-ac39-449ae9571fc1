<app-dialog-layout (dialogClose)="dialogRef.close({result: signStatus})"
                   [showCancel]="showInputCode"
                   [cancelText]="'common.actions.close' | translate"
                   [title]="'common.actions.signDocument.title' | translate: {name: dialogData.documentName}"
                   style="height: 100%;">

    <div class="flex-grow mb-4 file" *ngIf="file; else loadingFile">
        <ngx-extended-pdf-viewer
            [delayFirstView]="1000"
            [enableDragAndDrop]="false"
            [language]="'fr-FR'"
            [showBookmarkButton]="false"
            [showDownloadButton]="false"
            [showOpenFileButton]="false"
            [showPrintButton]="true"
            [showSecondaryToolbarButton]="false"
            [src]="fileSrc"
            [zoom]="'page-width'">
        </ngx-extended-pdf-viewer>
    </div>

    <ng-template #loadingFile>
        <div class="flex justify-center m-auto w-auto">
            <mat-spinner role="progressbar" mode="indeterminate"
                         class="mat-spinner mat-progress-spinner mat-primary mat-progress-spinner-indeterminate-animation"
                         style="width: 100px; height: 100px;">
            </mat-spinner>
        </div>
    </ng-template>

    <div class="flex flex-col items-center mb-2 mt-2" *ngIf="file && showInputCode">
        <treo-message [showIcon]="false" appearance="outline" type="info" class="mb-2">
            {{'common.actions.signDocument.code' | translate: {name: this.dialogData.documentName} }}
        </treo-message>

        <code-input class="w-1/2"
                    [isCodeHidden]="false"
                    [isCharsCode]="true"
                    [codeLength]="6"
                    (codeCompleted)="onCodeCompleted($event)">
        </code-input>
    </div>

    <div *ngIf="messages?.length" class="flex flex-col items-center mt-2 mb-2">
        <treo-message appearance="outline" [showIcon]="false" type="info">
            <ul>
                <li *ngFor="let message of messages">
                    {{ message }}
                </li>
            </ul>
        </treo-message>
    </div>

    <div class="flex flex-col items-center mb-2 mt-2" *ngIf="showMessage">
        <treo-message [showIcon]="false" appearance="outline" type="info" class="mb-2">
            {{showMessage | translate}}
        </treo-message>
    </div>

    <div *ngIf="errorMessages?.length" class="flex flex-col items-center mb-2">
        <treo-message appearance="outline" [showIcon]="false" type="error">
            <ul>
                <li *ngFor="let errorMessage of errorMessages">
                    {{ errorMessage }}
                </li>
            </ul>
        </treo-message>
    </div>
</app-dialog-layout>
