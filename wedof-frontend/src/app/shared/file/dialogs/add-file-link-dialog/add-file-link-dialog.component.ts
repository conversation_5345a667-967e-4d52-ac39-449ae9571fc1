import {Component, Inject, Injector, OnInit} from '@angular/core';
import {MAT_DIALOG_DATA} from '@angular/material/dialog';
import {AbstractDialogFormComponent} from '../../../material/form/abstract-dialog-form.component';
import {Validators} from '@angular/forms';
import {FormValidators} from '../../../api/shared/form-validators';

interface AddFileLinkData {
    link: string;
    title?: string;
}

@Component({
    selector: 'add-file-link-dialog',
    templateUrl: './add-file-link-dialog.component.html',
    styleUrls: ['./add-file-link-dialog.component.scss']
})
export class AddFileLinkDialogComponent extends AbstractDialogFormComponent<string, AddFileLinkData> implements OnInit {
    constructor(
        injector: Injector,
        @Inject(MAT_DIALOG_DATA) public _dialogData: { title: string, downloadFile: boolean },
    ) {
        super(injector);
    }

    submit(): void {
        this.errorMessages = [];
        if (this.formGroup.valid) {
            this.loading = true;
            const link = this.formGroup.get('link').value;
            this.submitted$.next({link: link});
            this.close();
        }
    }

    ngOnInit(): void {
        this.initForm();
    }

    protected initForm(): void {
        const validators = [Validators.required];
        if (!this._dialogData.downloadFile) {
            validators.push(Validators.pattern(FormValidators.URL_PATTERN));
        }
        this.formGroup = this._fb.group({
            link: ['', validators]
        });
    }
}

