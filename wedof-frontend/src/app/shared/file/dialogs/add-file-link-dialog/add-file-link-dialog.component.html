<form (ngSubmit)="submit()" [formGroup]="formGroup">
    <app-dialog-layout (dialogClose)="close()" [actions]="actions"
                       [cancelText]="'common.actions.close' | translate"
                       [title]="_dialogData.title">
        <mat-form-field class="pb-2 input-line">
            <mat-label>{{ "private.common.files.link.label" | translate}}</mat-label>
            <input id="link"
                   matInput
                   formControlName="link"
                   [placeholder]="'private.common.files.link.placeholder' | translate"
                   required
                   type="url"/>
            <mat-error *ngIf="formGroup.get('link').hasError('required')">
                {{'common.errors.required' | translate}}
            </mat-error>
            <mat-error *ngIf="formGroup.get('link').hasError('pattern')">
                {{'common.errors.url' | translate}}
            </mat-error>
        </mat-form-field>
        <ng-template #actions>
            <button type="submit"
                    [disabled]="!formGroup.valid || !formGroup.dirty"
                    mat-flat-button
                    color="primary">
                <mat-progress-spinner *ngIf="loading; else submitLabel" [diameter]="24" mode="indeterminate">
                </mat-progress-spinner>
                <ng-template #submitLabel>
                    {{ 'common.actions.add' | translate }}
                </ng-template>
            </button>
        </ng-template>
    </app-dialog-layout>
</form>
