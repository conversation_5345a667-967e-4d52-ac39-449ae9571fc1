<mat-card class="flex-col mt-3 p-5 pb-0 pt-3 border shadow-none sm" [ngClass]="{'card-loading':cardLoading}">
    <div class="flex items-center mb-2">
        <mat-icon color="primary" class="mr-3 card-loading-show text-4xl">ballot</mat-icon>
        <div class="text-xl font-semibold card-loading-show">
            {{ 'private.training-organism.certifications.trainingsActives.title' | translate }}
        </div>
    </div>
    <div class="mt-8 mb-6 text-center" *ngIf="!certifierAccess || certifierAccess.state === stateCertifierAccess.WAITING || certifierAccess.state === stateCertifierAccess.NONE; else showTrainings">
        <p *ngIf="certifierAccess; else noCertifierAccess">
            {{ 'private.certification.partners.table.accessStateHelp.' + certifierAccess.state | translate }}
        </p>
        <ng-template #noCertifierAccess>
            {{ 'private.certification.partners.table.accessStateHelp.none' | translate }}
        </ng-template>
    </div>
    <ng-template #showTrainings>
        <app-certification-partner-trainings-table class="flex flex-col card-loading-show mt-2 mb-4"
                                                   [refreshTrainings]="refreshTrainings"
                                                   [partnerName]="certificationPartner._links.partner.name"
                                                   [certification]="certification"
        ></app-certification-partner-trainings-table>
    </ng-template>
</mat-card>
