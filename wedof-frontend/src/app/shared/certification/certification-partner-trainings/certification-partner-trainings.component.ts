import {
    Component,
    ElementRef,
    Input,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>hang<PERSON>
} from '@angular/core';
import {
    RefreshCertificationPartnerTrainings
} from '../certification-partner-trainings-table/certification-partner-trainings-table.component';
import {TrainingService} from '../../api/services/training.service';
import {CertificationPartner} from '../../api/models/certification-partner';
import {Certification} from '../../api/models/certification';
import {CertifierAccess, CertifierAccessState} from '../../api/models/certifier-access';
import {BaseCardComponentDirective, RequiredCallSuper} from '../../utils/base-card/base-card.directive';
import {tap} from 'rxjs/operators';
import {DataProviders} from '../../api/models/connection';

@Component({
    selector: 'app-certification-partner-trainings',
    templateUrl: './certification-partner-trainings.component.html',
    styleUrls: ['./certification-partner-trainings.component.scss']
})
export class CertificationPartnerTrainingsComponent extends BaseCardComponentDirective implements OnChang<PERSON>, <PERSON><PERSON><PERSON>roy {
    static COMPONENT_ID = 'formations';
    refreshTrainings: RefreshCertificationPartnerTrainings;
    stateCertifierAccess = CertifierAccessState;


    @Input() certificationPartner: CertificationPartner;
    @Input() certification: Certification;
    @Input() certifierAccess?: CertifierAccess;

    constructor(private _trainingService: TrainingService, private _el: ElementRef) {
        super(CertificationPartnerTrainingsComponent.COMPONENT_ID, _el);
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (!this.certifierAccess || [CertifierAccessState.WAITING, CertifierAccessState.NONE].includes(this.certifierAccess.state)) {
            return;
        }
        const siret = this.certificationPartner._links.partner.siret;
        const certifInfo = this.certificationPartner._links.certification.certifInfo;
        this.panelLoading();
        this.refreshTrainings = (limit: number, page: number) => {
            return this._trainingService.list({
                certifInfo: certifInfo,
                siret: siret,
                state: 'published',
                dataProvider: DataProviders.CPF,
                limit: limit,
                page: page
            }).pipe(tap(() => {
                if (this.cardLoading) {
                    this.panelLoaded();
                }
            }));
        };
    }

    ngOnDestroy(): RequiredCallSuper {
        return super.ngOnDestroy();
    }
}
