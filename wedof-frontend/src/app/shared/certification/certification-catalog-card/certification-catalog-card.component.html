<mat-card [class]="isSelected ? 'selectedCard' : ''"
          class="w-80 h-60 p-4 flex-row justify-between no-shadow-card border"
          (click)="open()">
    <div class="flex flex-col h-full justify-between">
        <div class="mat-card-content">
            <div class="flex justify-between items-center">
                <div>{{ certification.externalId }}</div>
                <div class="flex items-center">
                    <mat-icon *ngIf="certificationPartner && certificationPartner.compliance !== certificationPartnerAuditResults.NONE"
                              aria-hidden="true"
                              [class]="certificationPartner.compliance === certificationPartnerAuditResults.COMPLIANT ? 'text-green mr-1 icon-size-14' :
                       certificationPartner.compliance === certificationPartnerAuditResults.PARTIALLY_COMPLIANT ? 'text-orange mr-1 icon-size-14' : 'mr-1 icon-size-14'"
                              [color]="certificationPartner.compliance === certificationPartnerAuditResults.NON_COMPLIANT ? 'warn' : undefined"
                              [matTooltip]="'private.certification.audit.result.' + certificationPartner.compliance | translate">
                        {{ certificationPartner.compliance | certificationPartnerAuditResultToIcon }}
                    </mat-icon>
                    <mat-icon *ngIf="certifierAccess"
                              [ngClass]="certifierAccess.state | certifierAccessStateToColor"
                              class="icon-size-14 mr-1"
                              [matTooltip]="('private.certification.partners.table.access' | translate) + ' ' + ('private.certification.partners.table.accessState.' + certifierAccess.state | translate | lowercase)">
                        {{ certifierAccess.state | certifierAccessStateToIcon }}
                    </mat-icon>
                    <mat-icon
                        *ngIf="isExpired(certification.cpfDateEnd) || expireBeforeNextYear(certification.cpfDateEnd)"
                        [matTooltip]="'private.common.certification.managePartnership.tooltip.' + (isExpired(certification.cpfDateEnd) ? 'expired' : 'oneYear') | translate"
                        class="icon-size-14 mr-1" aria-hidden="true">
                        schedule
                    </mat-icon>
                    <p [matTooltip]="'private.common.certification.managePartnership.tooltip.expirationDate' | translate">
                        {{ certification.cpfDateEnd | date:'dd/MM/yyyy' }}
                    </p>
                </div>
            </div>
            <app-ellipsis-text class="font-bold break-words min-h-10 block" [text]="certification.name"
                               [maxLength]="50"></app-ellipsis-text>
            <div class="mt-2">

                <mat-chip-list>
                    <mat-chip class="cursor-pointer"
                              *ngFor="let code of codes.slice(0, displayedCodesNumber)" (click)="filterCode(code)"
                              [matTooltip]="code.name">
                        {{ code.code }}
                    </mat-chip>
                    <mat-chip *ngIf="codes.length > displayedCodesNumber"
                              [matTooltipPosition]="'above'"
                              [matTooltip]="getAdditionalCodes()">
                        <mat-icon>
                            more_horiz
                        </mat-icon>
                    </mat-chip>
                </mat-chip-list>
            </div>
            <mat-icon
                [matTooltip]="(certification.partnerCount || 0) | i18nPlural: partnerCountTextMapping | translate: {count: (certification.partnerCount || 0)}"
                [matBadge]="getPartnerCountBadge(certification.partnerCount )"
                matBadgePosition="below after"
                matBadgeSize="medium"
                class="mt-2"
                svgIcon="group"></mat-icon>
        </div>
        <div *ngIf="isCertifier else isTrainingOrganism"
             class="font-medium justify-center flex items-center text-center" style="height: 40px;">
            {{ 'private.certification.partners.table.habilitation.self' | translate }}
        </div>
        <ng-template #isTrainingOrganism>
            <ng-container *ngIf="!certificationPartner; else hasCertificationPartner">
                <app-certification-catalog-button-new class="w-full flex-none"
                                                      *ngIf="certification.enabled else certificationInactive"
                                                      [certification]="certification"
                                                      (certificationPartnerCreated)="onCertificationPartnerCreatedOrUpdated($event)">
                </app-certification-catalog-button-new>
                <ng-template #certificationInactive>
                    <div class="font-medium justify-center flex items-center text-center" style="height: 40px;">
                        {{ 'private.certification.inactive' | translate }}</div>
                </ng-template>
            </ng-container>
            <ng-template #hasCertificationPartner>
                <ng-container *ngIf="certificationPartner.state === certificationPartnerStates.ACTIVE">
                    <div
                        *ngIf="!certificationPartner._links.certifier || certificationPartner._links.certifier.hasOwner; else noOwner"
                        class="font-medium justify-center flex items-center text-center" style="height: 40px;">
                        {{ ('private.certification.partners.table.habilitation.' + certificationPartner.habilitation) | translate }}
                    </div>
                    <ng-template #noOwner>
                        <app-certification-catalog-button-invite
                            class="w-full flex-none"
                            [certification]="certification">
                        </app-certification-catalog-button-invite>
                    </ng-template>
                </ng-container>
                <div *ngIf="[certificationPartnerStates.DRAFT, certificationPartnerStates.PROCESSING, certificationPartnerStates.REFUSED, certificationPartnerStates.REVOKED, certificationPartnerStates.SUSPENDED].indexOf(certificationPartner.state) !== -1"
                    class="font-medium justify-center flex items-center text-center" style="height: 40px;">
                    {{ certificationPartner | certificationPartnerStateName }}
                </div>
                <app-certification-catalog-button-update-state
                    *ngIf="certificationPartner.state == certificationPartnerStates.ABORTED"
                    [certificationPartner]="certificationPartner"
                    [certification]="certification"
                    [targetState]="certificationPartnerStates.PROCESSING"
                    [label]="'private.common.certification.managePartnership.menu.reopenShort' | translate"
                    (certificationPartnerUpdated)="onCertificationPartnerCreatedOrUpdated($event)"
                    color="primary"
                    class="w-full flex-none">
                </app-certification-catalog-button-update-state>
            </ng-template>
        </ng-template>
    </div>
</mat-card>
