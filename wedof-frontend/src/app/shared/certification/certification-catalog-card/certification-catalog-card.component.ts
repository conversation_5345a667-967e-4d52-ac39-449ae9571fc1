import {Component, ElementRef, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {Certification} from '../../api/models/certification';
import {CertifierAccess, CertifierAccessState} from '../../api/models/certifier-access';
import {CertificationPartner, CertificationPartnerStates} from '../../api/models/certification-partner';
import {CertificationPartnerAuditResults} from '../../api/models/certification-partner-audit';

@Component({
    selector: 'app-certification-catalog-card',
    templateUrl: './certification-catalog-card.component.html',
    styleUrls: ['./certification-catalog-card.component.scss']
})
export class CertificationCatalogCardComponent implements OnInit {

    @Input() isSelected = false;
    @Input() isCertifier: boolean;
    @Input() certification: Certification;
    @Input() certifierAccess: CertifierAccess;
    @Input() certificationPartner?: CertificationPartner;

    @Output() openCard = new EventEmitter<Certification>();
    @Output() searchCode = new EventEmitter<string>();
    @Output() certificationPartnerUpdated = new EventEmitter<CertificationPartner>();

    partnerCountTextMapping: any = {
        '=0': 'private.common.certification.managePartnership.partnerCount.none',
        '=1': 'private.common.certification.managePartnership.partnerCount.singular',
        'other': 'private.common.certification.managePartnership.partnerCount.plural',
    };
    displayedCodesNumber = 3;
    codes: Array<{ name: string, code: string, link?: string }> = [];
    certificationPartnerStates = CertificationPartnerStates;
    states = CertifierAccessState;
    certificationPartnerAuditResults = CertificationPartnerAuditResults;

    constructor(private _el: ElementRef
    ) {
    }

    ngOnInit(): void {
        this.codes = this.getCodes();
    }

    open(): void {
        this.openCard.emit(
            this.certification
        );
    }

    scrollToTop(): void {
        setTimeout(() => {
            this._el.nativeElement.scrollIntoView({
                behavior: 'smooth',
                block: 'start',
                inline: 'nearest'
            });
        });
    }

    expireBeforeNextYear(cpfDateEnd: string): boolean {
        if (cpfDateEnd) {
            const nextYearDate = new Date();
            nextYearDate.setFullYear(nextYearDate.getFullYear() + 1);
            return (new Date(cpfDateEnd)) < nextYearDate;
        } else {
            return false;
        }
    }

    isExpired(cpfDateEnd: string): boolean {
        if (cpfDateEnd) {
            return (new Date(cpfDateEnd)) < new Date();
        } else {
            return false;
        }
    }

    getCodes(): Array<{ name: string, code: string, link?: string }> {
        return [
            ...this.certification.rome?.map(rome => {
                return {
                    ...rome,
                    code: rome.code.replace(/(<([^>]+)>)/ig, '')
                };
            }),
            ...this.certification.domains
        ];
    }

    getAdditionalCodes(): string {
        return this.codes
            .slice(this.displayedCodesNumber, this.codes.length)
            .map(code => code.name + ' (' + code.code + ')')
            .join(', ');
    }

    filterCode(code: { name: string; code: string; link?: string }): void {
        this.searchCode.emit(code.code);
    }

    onCertificationPartnerCreatedOrUpdated(certificationPartner: CertificationPartner): void {
        this.certificationPartnerUpdated.emit(certificationPartner);
        this.open();
    }

    getPartnerCountBadge(partnerCount: number): string {
        return partnerCount > 999 ? '+1k' : partnerCount > 0 ? partnerCount.toString() : null;
    }
}
