<ng-template #noData>
    <p class="bg-white p-6 cursor-auto text-center m-auto"
       [innerHTML]="'private.certification.partners.table.no-data' | translate:{name: certification.name, link: getCertificationLink(certification)} | markdown">
    </p>
</ng-template>

<app-wrapper-spinner class="bg-white" [active]="isLoading">
    <ng-container *ngIf="isLoading || displayedData?.length || activeFilters ; else noData">
        <mat-form-field class="table-search sticky-searchbar">
            <input matInput [formControl]="queryCtrl"
                   [placeholder]="'common.table.filters.searchbar.organism' | translate"
                   maxlength="50">
            <mat-icon matPrefix svgIcon="search"></mat-icon>
            <button mat-button *ngIf="queryCtrl.value" matSuffix mat-icon-button aria-label="Clear"
                    (click)="clearQueryFilter()">
                <mat-icon svgIcon="close"></mat-icon>
            </button>
        </mat-form-field>

        <table class="table-fixed" [dataSource]="displayedData" mat-table matSort
               [matSortActive]="getMatSort().active"
               [matSortDirection]="getMatSort().direction"
               matSortDisableClear>
            <tr mat-header-row *matHeaderRowDef="displayedColumns" class="sticky-tableheader"></tr>
            <tr mat-row (click)="onRowClick(row)" *matRowDef="let row; columns: displayedColumns;"
                [ngClass]="{'selected':this.selectedRow?._links?.partner?.siret === row?._links?.partner?.siret, 'disabled': !isCertifierForCertificationPartner(row)}"></tr>
            <tr mat-footer-row *matFooterRowDef="['noDataForFilters']" [hidden]="isLoading || displayedData?.length">
            </tr>

            <ng-container matColumnDef="noDataForFilters">
                <td mat-footer-cell *matFooterCellDef [attr.colspan]="displayedColumns.length">
                    {{ 'common.table.filters.no-data' | translate }}
                </td>
            </ng-container>

            <ng-container matColumnDef="name">
                <th class="w-4/12" mat-header-cell *matHeaderCellDef mat-sort-header disableClear>
                    {{ 'private.certification.partners.table.organism' | translate }}
                </th>
                <td mat-cell *matCellDef="let certificationPartner">
                    <p class="truncate font-bold">{{ certificationPartner._links.partner.name }}</p>
                    <p class="truncate text-secondary">{{ certificationPartner._links.partner.siret }}</p>
                </td>
            </ng-container>

            <ng-container matColumnDef="stateLastUpdate">
                <th class="w-4/12" mat-header-cell *matHeaderCellDef mat-sort-header disableClear>
                    <app-table-multiple-filter *ngIf="hasStatesFilter; else noStatesFilter"
                                               [filters]="stateFilters"
                                               [title]="'état'"
                                               (selectFilters)="onStateFilter($event)"
                                               [defaultValueState]="defaultState">
                        {{ 'private.certification.partners.table.partnership.stateLabel' | translate }}
                    </app-table-multiple-filter>
                    <ng-template #noStatesFilter>
                        {{ 'private.certification.partners.table.partnership.stateLabel' | translate }}
                    </ng-template>
                </th>
                <td mat-cell *matCellDef="let certificationPartner">
                    <div>
                        {{ 'private.certification.partners.table.partnership.state.' + certificationPartner.state | translate }}
                    </div>
                    <div *ngIf="certificationPartner.pendingActivation"
                         class="text-disabled">
                        {{ 'private.certification.partners.table.partnership.pendingActivation' | translate }}
                    </div>
                    <div *ngIf="certificationPartner.pendingRevocation"
                         class="text-disabled">
                        {{ 'private.certification.partners.table.partnership.pendingRevocation' | translate }}
                    </div>
                    <div *ngIf="certificationPartner.pendingSuspension"
                         class="text-disabled">
                        {{ 'private.certification.partners.table.partnership.pendingSuspension' | translate }}
                    </div>
                    <div *ngIf="certificationPartner.stateLastUpdate"
                         class="text-secondary"
                         title="{{ 'private.certification.partners.table.stateLastUpdate' | translate}}">
                        {{ certificationPartner.stateLastUpdate | date: 'dd/MM/yy' }}
                    </div>
                </td>
            </ng-container>

            <ng-container matColumnDef="compliance">
                <th class="w-4/12" mat-header-cell *matHeaderCellDef>
                    <app-table-multiple-filter *ngIf="hasComplianceFilter; else noAuditFilter"
                                               [filters]="complianceFilters"
                                               [title]="'conformité'"
                                               (selectFilters)="complianceFilter($event)">
                        {{ 'private.certification.partners.table.partnership.auditComplianceLabel' | translate }}
                    </app-table-multiple-filter>
                    <ng-template #noAuditFilter>
                        {{ 'private.certification.partners.table.partnership.auditComplianceLabel' | translate }}
                    </ng-template>
                </th>
                <td mat-cell *matCellDef="let certificationPartner">
                    <ng-container
                        *ngIf="isCertifierForCertificationPartner(certificationPartner); else certifierIsNotCertificationPartnerCertifier">
                        <div
                            *ngIf="certification.allowAudits && certificationPartner.compliance !== certificationPartnerAuditResults.NONE; else noCompliance">
                            <div class="flex items-center">
                                <mat-icon [class]="certificationPartner.compliance === certificationPartnerAuditResults.COMPLIANT ? 'text-green mr-1' :
                       certificationPartner.compliance === certificationPartnerAuditResults.PARTIALLY_COMPLIANT ? 'text-orange mr-1' : 'mr-1'"
                                          [color]="certificationPartner.compliance === certificationPartnerAuditResults.NON_COMPLIANT ? 'warn' : undefined"
                                          title=" {{'private.certification.audit.result.' + certificationPartner.compliance | translate}}">
                                    {{ certificationPartner.compliance | certificationPartnerAuditResultToIcon }}
                                </mat-icon>
                                {{'private.certification.audit.result.' + certificationPartner.compliance | translate}}

                            </div>
                            <div *ngIf="certificationPartner.complianceLastUpdate" class="text-secondary"
                                 title="{{ 'private.certification.partners.table.complianceLastUpdate' | translate}}">{{ certificationPartner.complianceLastUpdate | date: 'dd/MM/yy' }}</div>
                        </div>
                        <ng-template #noCompliance>
                            <a [href]="'/certification/partenariats/' + certification.certifInfo + '/' + certificationPartner._links.partner.siret + '/audit'">{{'private.common.form.placeholder' | translate }}</a>
                        </ng-template>
                    </ng-container>
                </td>
            </ng-container>

            <ng-container matColumnDef="access">
                <th class="w-4/12 text-center" mat-header-cell *matHeaderCellDef>
                    <app-table-filter [filters]="accessFilters" (selectFilter)="onAccessFilter($event)">
                        {{ 'private.certification.partners.table.access' | translate }}
                    </app-table-filter>
                </th>
                <td mat-cell *matCellDef="let certificationPartner" class="text-center">
                    <ng-container class="flex flex-row items-center"
                                  *ngIf="isCertifierForCertificationPartner(certificationPartner); else certifierIsNotCertificationPartnerCertifier">
                        <mat-icon
                            [matTooltip]="'private.certification.partners.table.accessStateHelp.' + certificationPartner.certifierAccessState | translate"
                            [matTooltipPosition]="'above'"
                            [matTooltipShowDelay]="500"
                            class="{{certificationPartner.certifierAccessState | certifierAccessStateToColor}}">
                            {{ certificationPartner.certifierAccessState | certifierAccessStateToIcon }}
                        </mat-icon>
                    </ng-container>
                </td>
            </ng-container>

            <ng-container matColumnDef="connections">
                <th class="w-4/12 text-center" mat-header-cell *matHeaderCellDef>
                    <app-table-filter [filters]="connectionIssueFilters"
                                      (selectFilter)="onConnectionIssueFilter($event)">
                        {{ 'private.certification.partners.table.connections' | translate }}
                    </app-table-filter>
                </th>
                <td mat-cell *matCellDef="let certificationPartner" class="text-center">
                    <ng-container
                        *ngIf="isCertifierForCertificationPartner(certificationPartner); else certifierIsNotCertificationPartnerCertifier">
                        <mat-icon
                            *ngIf="certificationPartner.certifierAccessState === certifierAccessState.ACCEPTED else dataRequiresCertifierAccess"
                            [class]="getColorIconConnection(getAggregateConnectionState(certificationPartner.connections))"
                            [matTooltip]="'private.certification.partners.table.connectionStateHelp.' + getAggregateConnectionState(certificationPartner.connections) | translate"
                            [matTooltipPosition]="'above'"
                            [matTooltipShowDelay]="500">cloud_sync
                        </mat-icon>
                    </ng-container>
                </td>
            </ng-container>

            <ng-container matColumnDef="trainings" *ngIf="displayedColumns.includes('trainings')">
                <th class="w-4/12 text-center" mat-header-cell *matHeaderCellDef>
                    {{ 'private.certification.partners.table.trainings' | translate }}
                </th>
                <td mat-cell *matCellDef="let certificationPartner" class="text-center">
                    <ng-container
                        *ngIf="isCertifierForCertificationPartner(certificationPartner); else certifierIsNotCertificationPartnerCertifier">
                        <div *ngIf="subscription?.allowCertifierPlus; else notAvailable">
                            <div
                                *ngIf="certificationPartner.certifierAccessState === certifierAccessState.ACCEPTED else dataRequiresCertifierAccess"
                                class="flex flex-col">
                            <span
                                class="underline"
                                *ngIf="certificationPartner.activeTrainingsCount
                                        && (certificationPartner.state === certificationPartnerStates.ACTIVE || certificationPartner.state === certificationPartnerStates.REVOKED || certificationPartner.state === certificationPartnerStates.SUSPENDED); else activeTrainingsCountIsZero"
                                (click)="openSideWithAnchor($event, certificationPartner, subscription?.allowCertifierPlus ? 'formations' : 'formationsPromo')">
                                    {{ certificationPartner.activeTrainingsCount }}
                            </span>
                                <span class="text-secondary"
                                      *ngIf="certificationPartner.activeTrainingActionsCount
                                        && (certificationPartner.state === certificationPartnerStates.ACTIVE || certificationPartner.state === certificationPartnerStates.REVOKED || certificationPartner.state === certificationPartnerStates.SUSPENDED);">
                                    {{ certificationPartner.activeTrainingActionsCount }} action(s)
                            </span>
                                <ng-template #activeTrainingsCountIsZero>0</ng-template>
                            </div>
                        </div>
                    </ng-container>
                    <ng-template #notAvailable>
                        <span class="underline" (click)="openDialogSubscription('displayTrainingAndTrainingActions')">Voir</span>
                    </ng-template>
                </td>
            </ng-container>

            <ng-container matColumnDef="certificationFolders">
                <th class="w-4/12 text-center" mat-header-cell *matHeaderCellDef>
                    {{ 'private.certification.partners.table.certificationFolders' | translate }}
                </th>
                <td mat-cell *matCellDef="let certificationPartner" class="text-center">
                    <ng-container
                        *ngIf="isCertifierForCertificationPartner(certificationPartner); else certifierIsNotCertificationPartnerCertifier">
                        <a *ngIf="certificationPartner.certifierAccessState === certifierAccessState.ACCEPTED || certificationPartner.certificationFoldersCount else dataRequiresCertifierAccess"
                           href="/certification/dossiers/kanban?siret={{ certificationPartner._links.partner.siret }}&certifInfo={{certification.certifInfo}}">{{ certificationPartner.certificationFoldersCount }}</a>
                    </ng-container>
                </td>
            </ng-container>

            <ng-container matColumnDef="tags">
                <th class="w-4/12 text-center" mat-header-cell *matHeaderCellDef>
                    {{ 'common.tags.label' | translate }}
                </th>
                <td *matCellDef="let certificationPartner" mat-cell class="mt-1">
                    <app-form-field-static *ngIf="certificationPartner.tags?.length"
                                           [lengthLimit]="2"
                                           [value]="certificationPartner.tags"
                                           (customClick)="searchTag($event)"
                                           type="tags"></app-form-field-static>
                </td>
            </ng-container>

            <!-- extra columns from apps #columns-app-certification-partners-table -->
            <ng-container *ngFor="let context of organismApplicationEntryPointColumns"
                          [matColumnDef]="context.params.columnDef">
                <th class="w-4/12" mat-header-cell *matHeaderCellDef="let certificationPartner">
                    <ng-container *ngTemplateOutlet="(context.params.tplHeader ? _applicationsService.getTemplate(context.params.tplHeader) : defaultHeader);
                    context: {certificationPartner: certificationPartner, context: context}"></ng-container>
                    <ng-template #defaultHeader>
                        {{ context.params.header.apply(context.organismApplication, [certificationPartner]) }}
                    </ng-template>
                </th>
                <td mat-cell *matCellDef="let certificationPartner">
                    <ng-container *ngTemplateOutlet="(context.params.tplCell ? _applicationsService.getTemplate(context.params.tplCell) : defaultCell);
                    context: {certificationPartner: certificationPartner, context: context}"></ng-container>
                    <ng-template #defaultCell>
                        <ng-container
                            *ngIf="isCertifierForCertificationPartner(certificationPartner); else certifierIsNotCertificationPartnerCertifier">
                            {{ context.params.cell.apply(context.organismApplication, [certificationPartner]) }}
                        </ng-container>
                    </ng-template>
            </ng-container>
            <ng-template #dataRequiresCertifierAccess>
                    <span [matTooltip]="'private.certification.partners.table.dataRequiresCertifierAccess' | translate"
                          [matTooltipPosition]="'above'"
                          [matTooltipShowDelay]="500">N/A</span>
            </ng-template>
            <ng-template #certifierIsNotCertificationPartnerCertifier>
                <span>N/A</span>
            </ng-template>
        </table>

        <app-paginator [length]="total"
                       [pageSizeOptions]="pageSizeOptions"
                       [scrollTopOnPageChange]="true"
                       (page)="onPageEvent($event)">
        </app-paginator>
    </ng-container>
</app-wrapper-spinner>
