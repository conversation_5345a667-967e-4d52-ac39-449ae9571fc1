import {AfterViewInit, Component, Injector, Input, OnInit} from '@angular/core';
import {combineLatest, Observable} from 'rxjs';
import {filter, map, takeUntil} from 'rxjs/operators';
import {Certification, CertificationTypes} from '../../api/models/certification';
import {CertificationPartner, CertificationPartnerStates} from '../../api/models/certification-partner';
import {PaginatedResponse} from '../../api/services/abstract-paginated.service';
import {AbstractTableComponent} from '../../material/table/abstract-table.component';
import {TableFilter} from '../../material/table/table-filter/table-filter.component';
import {TableFilter as TableMultipleFilter} from '../../material/table/table-multiple-filter/table-multiple-filter.component';
import {
    OrganismFilters,
    OrganismQuery
} from '../../../private/certification/abstract-certification-organisms.component';
import {CertificationPartnerService} from '../../api/services/certification-partner.service';
import {CertifierAccessState} from '../../api/models/certifier-access';
import {ApplicationsService} from '../../../applications/shared/applications.service';
import {Subscription, SubscriptionTypes} from '../../api/models/subscription';
import {Select} from '@ngxs/store';
import {SubscriptionState} from '../../api/state/subscription.state';
import {DialogUpgradeSubscriptionComponent} from '../../subscription/dialog-upgrade-subscription/dialog-upgrade-subscription.component';
import {MatDialog} from '@angular/material/dialog';
import {OrganismState} from '../../api/state/organism.state';
import {Organism} from '../../api/models/organism';
import {CertifierAccessStateToIconPipe} from '../../pipes/certifier-access-state-to-icon.pipe';
import {CertifierAccessStateToColorPipe} from '../../pipes/certifier-access-state-to-color.pipe';
import {Location} from '@angular/common';
import {ConnectionStateDynamic} from '../../api/models/connection';
import {SortDirection} from '@angular/material/sort';
import {
    CertificationPartnerAuditResults,
    CertificationPartnerAuditStates
} from '../../api/models/certification-partner-audit';
import {CertificationPartnerAuditResultToIconPipe} from '../../pipes/certification-partner-audit-state-to-icon.pipe';

type AggregateConnectionState = 'active' | 'failed' | 'partial';

@Component({
    selector: 'app-certification-partners-table',
    templateUrl: './certification-partners-table.component.html',
    styleUrls: ['./certification-partners-table.component.scss']
})
export class CertificationPartnersTableComponent extends AbstractTableComponent<CertificationPartner, OrganismQuery> implements OnInit, AfterViewInit {

    organism: Organism;
    subscription: Subscription;
    accessFilters: TableFilter[];
    connectionIssueFilters: TableFilter[];
    stateFilters: TableMultipleFilter[];
    complianceFilters: TableMultipleFilter[];
    @Input() certification: Certification;
    @Input() partnersFilters$: Observable<OrganismFilters>;
    certifierAccessState = CertifierAccessState;
    organismApplicationEntryPointColumns: Array<any> = [];
    hasStatesFilter = false;
    hasComplianceFilter = false;
    certificationPartnerStates = CertificationPartnerStates;
    defaultState: string[] = ['active'];
    certificationPartnerAuditResults = CertificationPartnerAuditResults;

    @Select(SubscriptionState.subscription) subscription$: Observable<Subscription>;
    @Select(OrganismState.organism) organism$: Observable<Organism>;

    constructor(
        injector: Injector,
        private _dialog: MatDialog,
        public _applicationsService: ApplicationsService,
        private _certifierAccessStateToIconPipe: CertifierAccessStateToIconPipe,
        private _certifierAccessStateToColorPipe: CertifierAccessStateToColorPipe,
        private _certificationPartnerService: CertificationPartnerService,
        private _certificationPartnerAuditResultToIconPipe: CertificationPartnerAuditResultToIconPipe,
        private _location: Location
    ) {
        super(injector);
        this.accessFilters = Object.values(CertifierAccessState).map(value => ({
                label: `private.certification.partners.table.accessState.${value}`,
                value,
                icon: this._certifierAccessStateToIconPipe.transform(value),
                class: this._certifierAccessStateToColorPipe.transform(value)
            })
        );
        this.connectionIssueFilters = [
            {
                label: 'private.certification.partners.table.connectionState.state.active',
                icon: 'cloud_sync',
                class: 'green-icon',
                value: 'false'
            },
            {
                label: 'private.certification.partners.table.connectionState.nonActive',
                icon: 'cloud_sync',
                class: 'red-icon',
                value: 'true'
            }];
        // TODO: filter/column to display for 99€ option enable only ?
        this.stateFilters = Object.values(CertificationPartnerStates)
            .filter(value => value !== CertificationPartnerStates.DRAFT)
            .map(value => ({
                label: `private.certification.partners.table.partnership.state.${value}`,
                value
            }));
        this.complianceFilters = Object.values(CertificationPartnerAuditResults).map(value => ({
            label: `private.certification.audit.result.${value}`,
            value: value,
            icon: this._certificationPartnerAuditResultToIconPipe.transform(value),
            color: value === CertificationPartnerAuditResults.NON_COMPLIANT ? 'warn' : null,
            class: value === CertificationPartnerAuditResults.COMPLIANT ? 'text-green' : value === CertificationPartnerAuditResults.PARTIALLY_COMPLIANT ? 'text-orange' : null,
        }));
        this.complianceFilters.unshift({
            label: `private.certification.partners.table.complianceInProgress`,
            value: CertificationPartnerAuditStates.IN_PROGRESS,
            icon: 'more_horiz',
            color: null,
            class: 'text-primary'
        });
    }

    ngOnInit(): void {
        combineLatest([
            this.organism$,
            this.subscription$
        ]).pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe(([organism, subscription]) => {
            this.organism = organism;
            this.subscription = subscription;
            this.hasStatesFilter = subscription.allowCertifierPlus;
            this.hasComplianceFilter = subscription.allowCertifierPlus;

            this.displayedColumns = ['name', 'stateLastUpdate', 'compliance', 'access', 'certificationFolders', 'tags'];
            if (this.certification.type !== CertificationTypes.INTERNAL) {
                this.displayedColumns.splice(4, 0, 'connections');
                if (this.displayedColumns.indexOf('trainings') === -1) {
                    this.displayedColumns.splice(5, 0, 'trainings');
                }
            }
            this._applicationsService.getOrganismApplicationEntryPoints('columns-certification-partners-table')
                .subscribe(organismApplicationEntryPoints => {
                    organismApplicationEntryPoints.forEach(organismApplicationEntryPoint => {
                        this.organismApplicationEntryPointColumns.push(organismApplicationEntryPoint);
                        this.displayedColumns.push(organismApplicationEntryPoint.params.columnDef);
                    });
                });

            if (this.hasStatesFilter) {
                this.defaultState.push('processing');
            }
        });
    }

    getMatSort(): { active: string, direction: SortDirection } {
        return this.hasStatesFilter ? {active: 'stateLastUpdate', direction: 'desc'} : {active: 'name', direction: 'asc'};
    }

    ngAfterViewInit(): void {
        super.ngAfterViewInit();
        this.partnersFilters$.pipe(
            filter(query => query.certifInfo === this.certification.certifInfo),
            takeUntil(this._unsubscribeAll)
        ).subscribe(filters => {
            this.queryCtrl.setValue(filters.query);
            this._changeDetectorRef.detectChanges();
        });
    }

    getCertificationLink(certification: Certification): string {
        if (certification.link) {
            return certification.link;
        } else if ([CertificationTypes.RS, CertificationTypes.RNCP].includes(certification.type)) {
            return 'https://www.francecompetences.fr/recherche/' + certification.type.toLowerCase() + '/' + certification.code;
        }
    }

    onAccessFilter(certifierAccessState: string): void {
        this.applyFilter({name: 'certifierAccessState', value: certifierAccessState});
    }

    onConnectionIssueFilter(connectionState: string): void {
        this.applyFilter({name: 'connectionIssue', value: connectionState});
    }

    onStateFilter(state: string[]): void {
        this.applyFilter({name: 'state', value: state});
    }

    complianceFilter(compliance: string[]): void {
        this.applyFilter({name: 'compliance', value: compliance});
    }

    onRowClick(row: CertificationPartner, hasAnchor: boolean = false): void {
        this.emitRow(row, this.displayedData.indexOf(row), hasAnchor);
    }

    selectNext(row: CertificationPartner, hasAnchor: boolean = false): void {
        const newIndex = this.displayedData.indexOf(row) + 1;
        this.onRowClick(this.displayedData[newIndex], hasAnchor);
    }

    selectPrevious(row: CertificationPartner, hasAnchor: boolean = false): void {
        const newIndex = this.displayedData.indexOf(row) - 1;
        this.onRowClick(this.displayedData[newIndex], hasAnchor);
    }

    protected filterDataQuery(query: OrganismQuery): boolean {
        return query.certifInfo === this.certification.certifInfo;
    }

    protected findRowWithDataQuery(row: CertificationPartner, query: OrganismQuery): boolean {
        return row._links.partner.siret === query.siret;
    }

    protected refreshData(): Observable<PaginatedResponse<CertificationPartner>> {
        return this._certificationPartnerService.list({
            ...this._filters$.value,
            certifInfo: this.certification.certifInfo,
            limit: this.paginator.pageSize,
            page: this.paginator.pageIndex + 1,
            sort: this.sort.active,
            order: this.sort.direction
        }).pipe(
            map(response => {
                const processingItems = response.payload.filter(item => item.state === 'processing');
                const otherItems = response.payload.filter(item => item.state !== 'processing');
                return {
                    ...response,
                    payload: [...processingItems, ...otherItems]
                };
            })
        );
    }

    refreshRow($event: CertificationPartner): void {
        // Map to generate new array because the dumb CDK component does not take into account changes to datasource unless a new array is provided or renderRows is called...
        this.displayedData = this.displayedData.map(row => {
            // @ts-ignore
            if (row.hasOwnProperty('id') && row.id === $event.id) {
                return $event;
            } else {
                return row;
            }
        });
        // @ts-ignore
        if (this.selectedRow && (this.selectedRow.hasOwnProperty('id') && this.selectedRow.id === $event.id)) {
            this.onRowClick($event);
        }
        this._changeDetectorRef.detectChanges();
    }

    getAggregateConnectionState(connections: CertificationPartner['connections']): AggregateConnectionState {
        const availableConnections = connections?.filter((connection) => {
            return connection.state !== ConnectionStateDynamic.ACCOUNT_NOT_EXISTS;
        });
        if (!availableConnections || availableConnections.length === 0) {
            return 'failed';
        }
        return availableConnections.reduce((currentAggregateState: AggregateConnectionState, connection): AggregateConnectionState => {
            const newStateGroup = connection.state === 'active' ? 'active' : 'failed';
            if (currentAggregateState && newStateGroup !== currentAggregateState) {
                return 'partial';
            } else {
                return newStateGroup;
            }
        }, null);
    }

    getColorIconConnection(aggregateConnectionState: AggregateConnectionState): 'green-icon' | 'orange-icon' | 'red-icon' {
        const colorByState = {
            active: 'green-icon',
            partial: 'orange-icon',
            failed: 'red-icon'
        } as const;
        return colorByState[aggregateConnectionState];
    }

    openDialogSubscription(fromPage: string): void {
        this._dialog.open(DialogUpgradeSubscriptionComponent, {
            panelClass: ['full-page-scroll-50'],
            data: {
                organism: this.organism,
                subscription: this.subscription,
                fromPage: fromPage,
                subscriptionTypeToShow: SubscriptionTypes.CERTIFIER
            }
        });
    }

    openSideWithAnchor($event, row: CertificationPartner, anchor: string): void {
        $event.stopPropagation();
        this._location.replaceState('certification/partenariats/' + this.certification.certifInfo + '/' + row._links.partner.siret + '/' + anchor);
        this.onRowClick(row, true);
    }

    isCertifierForCertificationPartner(row: CertificationPartner): boolean {
        return row?._links?.certifier?.siret === this.organism.siret;
    }

    searchTag(tag: string): void {
        this.queryCtrl.setValue(tag);
        this._changeDetectorRef.detectChanges();
    }
}
