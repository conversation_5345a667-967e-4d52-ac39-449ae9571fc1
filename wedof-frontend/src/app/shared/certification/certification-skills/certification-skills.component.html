<mat-card class="flex flex-col mt-3 p-5 pb-0 pt-3 border shadow-none sm"
          [ngClass]="{'card-loading':cardLoading}">

    <div class="flex items-center">
        <mat-icon *ngIf="!loading"
                  [matBadge]="total > 0 ? total.toString() : null"
                  matBadgePosition="below after"
                  matBadgeSize="small"
                  class="mr-3 card-loading-show text-4xl"
                  color="primary"
                  svgIcon="bookmark_added"></mat-icon>
        <div class="flex items-center">
            <span
                class="text-xl font-semibold card-loading-show">{{ 'private.common.skills.title' | translate }}  </span>
        </div>
        <div class="ml-auto -mr-4 card-loading-show flex" *ngIf="isCertifier">
            <button type="button" mat-icon-button
                    *ngIf="certification.type === certificationType.RS"
                    (click)="createSkill()">
                <mat-icon svgIcon="add_circle"
                          [matTooltip]="'private.common.skills.form.title.skill.create' | translate"></mat-icon>
            </button>
            <button type="button" mat-icon-button
                    *ngIf="certification.type === certificationType.INTERNAL"
                    (click)="createSkillSet()">
                <mat-icon svgIcon="add_circle"
                          [matTooltip]="'private.common.skills.form.title.skillSet.create' | translate"></mat-icon>
            </button>
        </div>
    </div>

    <div *ngIf="!loading; else loadingSpinner" class="flex flex-col mt-3 mb-2 overflow-y-auto max-h-80">

        <button type="button" mat-flat-button color="primary" class="mt-2 mb-2"
                *ngIf="isCertifier && certification.type === certificationType.INTERNAL"
                (click)="createSkillSet()">
            {{'private.common.skills.form.title.skillSet.create' | translate}}
        </button>

        <div *ngIf="skills?.length; else noData">

            <ng-container *ngIf="certification.type === certificationType.RS; else showRNCP">
                <ng-template class="mb-2 mt-2"
                             *ngFor="let skill of skills"
                             [ngTemplateOutlet]="skillTemplate"
                             [ngTemplateOutletContext]="{skill: skill, isCertifier: isCertifier, parentSkill: null}">
                </ng-template>
            </ng-container>

            <ng-template #showRNCP>
                <div *ngFor="let parentSkill of skills" class="mb-2">
                    <div class="flex flex-row items-center justify-between mb-1 sticky-header">
                        <p class="font-bold">BC{{getSkillCode(parentSkill.order)}} {{parentSkill.label}}</p>
                        <div *ngIf="isCertifier">
                            <button type="button" mat-icon-button
                                    *ngIf="certification.type === certificationType.INTERNAL"
                                    (click)="update(parentSkill, null, true)">
                                <mat-icon svgIcon="edit" color="primary"
                                          [matTooltip]="'private.common.skills.form.title.skillSet.update' | translate"></mat-icon>
                            </button>
                            <button type="button" mat-icon-button
                                    (click)="createSkill(parentSkill)">
                                <mat-icon svgIcon="add_circle" color="primary"
                                          [matTooltip]="'private.common.skills.form.title.skill.create' | translate"></mat-icon>
                            </button>
                            <button type="button" mat-icon-button
                                    *ngIf="certification.type === certificationType.INTERNAL"
                                    [disabled]="parentSkill.individualSkills.length > 0"
                                    [matTooltip]="(parentSkill.individualSkills.length > 0 ? 'private.common.skills.deleteUnavailable' : '' ) | translate "
                                    (click)="deleteSkill(parentSkill)">
                                <mat-icon svgIcon="delete" color="warn"></mat-icon>
                            </button>
                        </div>
                    </div>
                    <ng-container *ngIf="parentSkill.individualSkills?.length; else noDataSkills">
                        <ng-template class="mb-1 mt-1"
                                     *ngFor="let skill of getIndividualSkills(parentSkill)"
                                     [ngTemplateOutlet]="skillTemplate"
                                     [ngTemplateOutletContext]="{skill: skill, isCertifier: isCertifier, parentSkill : parentSkill}">
                        </ng-template>
                    </ng-container>
                </div>
            </ng-template>

        </div>

        <button type="button" mat-flat-button color="primary" class="mt-4 mb-2"
                *ngIf="isCertifier && certification.type === certificationType.RS"
                (click)="createSkill()">
            {{'private.common.skills.form.title.skill.create' | translate}}
        </button>

        <ng-template #noData>
            <p class="mb-2 text-center m-auto">
                {{'private.common.skills.' + (certification.type === certificationType.RNCP ? 'noDataSkillSet' : 'noData') | translate}}
            </p>
        </ng-template>
        <ng-template #noDataSkills>
            <p class="mb-2 text-secondary">
                {{ 'private.common.skills.noData' | translate }}
            </p>
        </ng-template>
    </div>
    <ng-template #loadingSpinner>
        <div class="flex justify-center mt-3 mb-3">
            <mat-progress-spinner *ngIf="loading" [diameter]="24"
                                  mode="indeterminate"></mat-progress-spinner>
        </div>
    </ng-template>
</mat-card>

<ng-template #skillTemplate
             let-skill="skill"
             let-parentSkill="parentSkill"
             let-isCertifier="isCertifier">
    <div class="flex flex-row justify-between cursor-pointer" [class]="parentSkill ? 'ml-2' : ''"
         (click)="update(skill, parentSkill)">
        <div class="flex items-center">
            <p class="font-bold">C{{getSkillCode(skill.order)}}</p>
            <app-ellipsis-text class="pl-5"
                               [text]="skill.label"
                               [maxLength]="120"></app-ellipsis-text>
        </div>
        <app-skill-menu *ngIf="isCertifier"
                        [skill]="skill"
                        (processedSkill)="update(skill, parentSkill)"
                        (deleteSkill)="deleteSkill($event)"></app-skill-menu>
    </div>
</ng-template>
