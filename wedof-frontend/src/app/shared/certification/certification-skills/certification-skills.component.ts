import {Component, ElementRef, Input, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, SimpleChang<PERSON>} from '@angular/core';
import {BaseCardComponentDirective, RequiredCallSuper} from '../../utils/base-card/base-card.directive';
import {Certification, CertificationTypes} from '../../api/models/certification';
import {Skill, SkillType} from '../../api/models/skill';
import {SkillService} from '../../api/services/skill.service';
import {filter, finalize, switchMap} from 'rxjs/operators';
import {DeletionConfirmationComponent} from '../../material/action-confirmation/deletion-confirmation.component';
import {MatDialog} from '@angular/material/dialog';
import {SkillFormComponent} from '../../skill/skill-form/skill-form.component';

@Component({
    selector: 'app-certification-skills',
    templateUrl: './certification-skills.component.html',
    styleUrls: ['./certification-skills.component.scss']
})
export class CertificationSkillsComponent extends BaseCardComponentDirective implements OnChanges, On<PERSON><PERSON>roy {

    static COMPONENT_ID = 'competences';

    skills: Skill[];
    total = 0;
    loading = false;
    skillType = SkillType;
    certificationType = CertificationTypes;

    @Input() isCertifier: boolean;
    @Input() certification: Certification;


    constructor(private _skillService: SkillService,
                private _dialog: MatDialog,
                private _el: ElementRef) {
        super(CertificationSkillsComponent.COMPONENT_ID, _el);
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes.certification) {
            this.panelLoading();
            this.init();
        }
    }

    init(): void {
        this.loading = true;
        this._skillService.listByUrl(this.certification._links.skills.href).subscribe(skills => {
            this.skills = skills;
            this.total = skills.length;
            this.loading = false;
            this.panelLoaded();
        });
    }

    ngOnDestroy(): RequiredCallSuper {
        return super.ngOnDestroy();
    }

    createSkillSet(): void {
        const dialogRef = this._dialog.open(SkillFormComponent, {
            disableClose: true,
            panelClass: 'full-page-scroll-40',
            data: {
                certifInfo: this.certification.certifInfo,
                title: 'private.common.skills.form.title.skillSet.create',
                nextCode: this.getNextSkillSetCode(),
                createParentSkill: true
            }
        });
        dialogRef.afterClosed().subscribe(result => {
            if (result?.data) {
                this.skills.push(result?.data);
            }
        });
    }

    createSkill(parentSkill?: Skill): void {
        const dialogRef = this._dialog.open(SkillFormComponent, {
            disableClose: true,
            panelClass: 'full-page-scroll-40',
            data: {
                certifInfo: this.certification.certifInfo,
                title: 'private.common.skills.form.title.skill.create',
                parentSkill: parentSkill,
                nextCode: this.getNextSkillCode(parentSkill)
            }
        });
        dialogRef.afterClosed().subscribe(result => {
            if (result?.data) {
                if (!parentSkill) {
                    this.skills.push(result?.data);
                    this.total = this.total + 1;
                } else {
                    this.skills.find((skill) => {
                        if (skill.id === parentSkill.id) {
                            skill.individualSkills.push(result?.data);
                        }
                    });
                }
            }
        });
    }

    processedSkill(skillUpdated: Skill, parentSkill?: Skill): void {
        if (parentSkill) {
            this.getIndividualSkills(parentSkill, skillUpdated);
        } else {
            this.skills = this.skills.map((skill) => {
                return skill.id === skillUpdated.id ? skillUpdated : skill;
            });
        }
    }

    deleteSkill(skillDeleted: Skill): void {
        const dialogRef = this._dialog.open(DeletionConfirmationComponent, {
            panelClass: 'full-page-scroll-40',
            height: 'auto',
            data: {
                messageKey: 'private.common.skills.confirmDeletion',
                data: skillDeleted
            }
        });
        dialogRef.componentInstance.actionValue$.pipe(
            filter((confirmation: boolean) => confirmation),
            switchMap(() => this._skillService.delete(skillDeleted.id)),
            finalize(() => {
                dialogRef.componentInstance.close();
            })
        ).subscribe(() => {
            this.init();
        });
    }

    getIndividualSkills(parentSkill: Skill, skillUpdated?: Skill): Skill[] {
        if (skillUpdated) {
            parentSkill.individualSkills = parentSkill.individualSkills.map((skill) => {
                return skill.id === skillUpdated.id ? skillUpdated : skill;
            });
        }
        return parentSkill.individualSkills;
    }

    getSkillCode(order: number | string): number | string {
        return order <= 9 ? ('0' + order).slice(-2) : order;
    }

    getNextSkillCode(parentSkill?: Skill): string {
        const skills = parentSkill ? parentSkill.individualSkills : this.skills;
        const nextCode = skills?.length ? skills.slice(-1)[0].order + 1 : 1;
        return 'C' + this.getSkillCode(nextCode);
    }

    getNextSkillSetCode(): string {
        const nextCode = this.skills?.length ? this.skills.slice(-1)[0].order + 1 : '01';
        return 'BC' + this.getSkillCode(nextCode);
    }

    update(skill: Skill, parentSkill?: Skill, isSkillSet: boolean = false): void {
        const dialogRef = this._dialog.open(SkillFormComponent, {
            disableClose: true,
            panelClass: 'full-page-scroll-40',
            data: {
                skill: skill,
                title: 'private.common.skills.form.title.' + (isSkillSet ? 'skillSet' : 'skill') + '.update',
                parentSkill: parentSkill ?? null
            }
        });
        dialogRef.afterClosed().subscribe(result => {
            if (result?.data) {
                this.processedSkill(result?.data, parentSkill);
            }
        });
    }
}
