import {Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output} from '@angular/core';
import {MatDialog} from '@angular/material/dialog';
import {CertificationPartner, CertificationPartnerStates} from '../../api/models/certification-partner';
import {filter, finalize, switchMap, takeUntil, tap} from 'rxjs/operators';
import {ActionConfirmationComponent} from '../../material/action-confirmation/action-confirmation.component';
import {CertificationPartnerService} from '../../api/services/certification-partner.service';
import {ThemePalette} from '@angular/material/core';
import {Certification} from '../../api/models/certification';
import {Select} from '@ngxs/store';
import {OrganismState} from '../../api/state/organism.state';
import {Observable, Subject} from 'rxjs';
import {Organism} from '../../api/models/organism';

@Component({
    selector: 'app-certification-catalog-button-update-state',
    templateUrl: './certification-catalog-button-update-state.component.html',
    styleUrls: ['./certification-catalog-button-update-state.component.scss']
})
export class CertificationCatalogButtonUpdateStateComponent implements OnInit, OnDestroy {

    @Input() certificationPartner: CertificationPartner;
    @Input() certification: Certification;
    @Input() targetState: CertificationPartnerStates;
    @Input() color: ThemePalette = 'primary';
    @Input() label: string;
    @Input() class?: string;

    @Output() certificationPartnerUpdated = new EventEmitter<CertificationPartner>();

    @Select(OrganismState.organism) organism$: Observable<Organism>;

    currentOrganismSiret: string;
    loading = false;
    certificationPartnerStates = CertificationPartnerStates;

    private _unsubscribeAll = new Subject<void>();

    constructor(private _certificationPartnerService: CertificationPartnerService,
                private _dialog: MatDialog
    ) {
    }

    ngOnInit(): void {
        this.organism$.pipe(takeUntil(this._unsubscribeAll)).subscribe(organism => {
            this.currentOrganismSiret = organism.siret;
        });
    }

    updateState(): void {
        const certifInfo = this.certification.certifInfo;
        const dialogRef = this._dialog.open(ActionConfirmationComponent, {
            panelClass: 'full-page-scroll-30',
            height: 'auto',
            data: {
                messageKey: 'private.common.certification.managePartnership.confirm.' + this.targetState
            }
        });
        dialogRef.componentInstance.actionValue$.pipe(
            filter(confirmation => confirmation),
            tap(() => {
                this.loading = true;
            }),
            switchMap(() =>
                this._certificationPartnerService.update(certifInfo, this.currentOrganismSiret, {state: this.targetState})
            ),
            finalize(() => {
                dialogRef.componentInstance.close();
                this.loading = false;
            }),
        ).subscribe((certificationPartnerUpdated: CertificationPartner) => {
            this.certificationPartnerUpdated.emit(certificationPartnerUpdated);
        });
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }
}
