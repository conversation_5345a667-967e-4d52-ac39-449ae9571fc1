import {Component, EventEmitter, Input, On<PERSON><PERSON>roy, OnInit, Output} from '@angular/core';
import {Certification} from '../../api/models/certification';
import {CertificationPartner} from '../../api/models/certification-partner';
import {CertificationPartnerService} from '../../api/services/certification-partner.service';
import {filter, finalize, switchMap, takeUntil, tap} from 'rxjs/operators';
import {MatDialog} from '@angular/material/dialog';
import {ActionConfirmationComponent} from '../../material/action-confirmation/action-confirmation.component';
import {Select} from '@ngxs/store';
import {OrganismState} from '../../api/state/organism.state';
import {Observable, Subject} from 'rxjs';
import {Organism} from '../../api/models/organism';

@Component({
    selector: 'app-certification-catalog-button-new',
    templateUrl: './certification-catalog-button-new.component.html',
    styleUrls: ['./certification-catalog-button-new.component.scss']
})
export class CertificationCatalogButtonNewComponent implements OnInit, OnDestroy {

    @Input() certification: Certification;
    @Input() class?: string;
    @Output() certificationPartnerCreated = new EventEmitter<CertificationPartner>();

    @Select(OrganismState.organism) organism$: Observable<Organism>;

    currentOrganismSiret: string;
    loading = false;

    private _unsubscribeAll = new Subject<void>();

    constructor(private _certificationPartnerService: CertificationPartnerService,
                private _dialog: MatDialog
    ) {
    }

    ngOnInit(): void {
        this.organism$.pipe(takeUntil(this._unsubscribeAll)).subscribe(organism => {
            this.currentOrganismSiret = organism.siret;
        });
    }

    openCreateDialogConfirm(): void {
        const dialogRef = this._dialog.open(ActionConfirmationComponent, {
            panelClass: 'full-page-scroll-30',
            height: 'auto',
            data: {
                messageKey: 'private.common.certification.managePartnership.confirm.new'
            }
        });
        dialogRef.componentInstance.actionValue$.pipe(
            filter(confirmation => confirmation),
            tap(() => {
                this.loading = true;
            }),
            switchMap(() =>
                this._certificationPartnerService.create({
                    certifInfo: this.certification.certifInfo,
                    siret: this.currentOrganismSiret
                })
            ),
            finalize(() => {
                dialogRef.componentInstance.close();
                this.loading = false;
            })
        ).subscribe((certificationPartnerCreated) => {
            this.certificationPartnerCreated.emit(certificationPartnerCreated);
        });
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }
}
