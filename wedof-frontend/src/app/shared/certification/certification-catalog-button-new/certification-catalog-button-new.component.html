<button mat-flat-button
        type="button"
        [class]="class ? class : ''"
        [class.grey-button]="!certification.isPromoted"
        [disabled]="loading || !certification.enabled"
        [color]="certification.isPromoted ? 'primary' : undefined"
        (click)="openCreateDialogConfirm(); $event.preventDefault(); $event.stopPropagation();">
    {{ ('private.common.certification.managePartnership.action.' + (certification.isPromoted ? 'createPromoted' : 'createUnpromoted')) | translate }}
</button>
