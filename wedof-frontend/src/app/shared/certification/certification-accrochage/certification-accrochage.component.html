<mat-card class="flex-col mt-3 p-5 pb-0 pt-3 border shadow-none sm">
    <div class="flex items-center mb-2">
        <mat-icon class="mr-3 card-loading-show icon-size-32" svgIcon="verified_user" color="primary">
        </mat-icon>
        <div class="flex items-center">
            <span
                class="text-xl font-semibold card-loading-show"> {{'private.common.certification.exportXml' | translate}}</span>
        </div>
    </div>
    <div class="flex flex-col mb-2">
        <ng-container *ngIf="organism.accrochageDelegationDate; else noDelegationActive">
            <p class="mt-4 mb-4">{{ 'auth.cdc.dialog.delegationMandatoryInformations' | translate}}
                <a href="https://www.wedof.fr/assistance/guides/certificateurs/accrochage" target="_blank">
                    documentation</a> {{'auth.cdc.dialog.delegationMandatoryInformationsExample' | translate}}
            </p>
            <treo-message appearance="outline" [showIcon]="false" type="info" class="mb-2">
                <a href="/profil/organisme"
                   [innerHTML]="'auth.cdc.dialog.certificationAccrochage' | translate: {state: certification.allowGenerateXmlAutomatically ? 'actif' : 'inactif'} "></a>
            </treo-message>
        </ng-container>
        <ng-template #noDelegationActive>
            <p class="mt-4 mb-2">{{ 'auth.cdc.dialog.mandatoryInformations'| translate}}
                <a href="https://www.wedof.fr/assistance/guides/certificateurs/accrochage" target="_blank">
                    documentation</a>
            </p>
            <div class="flex m-auto mt-4 mb-2" *ngIf="subscription.allowCertifierPlus; else showPromoCard">
                <button type="button" color="primary" mat-flat-button>
                    <a class="no-underline"
                       [routerLink]="['/', 'profil', 'connexions', 'cdcCertifiers']">{{'auth.cdc.dialogAccrochage.delegate' | translate}}</a>
                </button>
            </div>
            <ng-template #showPromoCard>
                <app-subscription-small-card [type]="'accrochage'"
                                             [organism]="organism"
                                             [fromPage]="'accrochageDialog'"
                                             [subscription]="subscription">
                </app-subscription-small-card>
            </ng-template>
            <treo-message *ngIf="countCdcFilesExported >= 1 " class="my-2"
                          appearance="outline" [showIcon]="false" type="warning">
                <p [innerHTML]="'auth.cdc.cdcFilesExportedWaiting' | translate : {count: countCdcFilesExported}"></p>
            </treo-message>
        </ng-template>
        <div
            class="flex items-center mt-2 justify-end border-t pl-5 pr-10 -mx-5 py-3 light:bg-cool-gray-50 dark:bg-cool-gray-700 card-loading-hidden">
            <div *ngIf="!isOrganismExportable() || !isCertificationExportable(); else showExport ">
                <a *ngIf="!isOrganismExportable()" [routerLink]="['/profil/organisme']">
                    <p class="font-semibold">{{'auth.cdc.exportNotAvailableCdcContract' | translate}}</p>
                </a>
                <p *ngIf="!isCertificationExportable()" class="font-semibold">
                    {{'auth.cdc.exportNotAvailableObtentionSystem' | translate}}
                </p>
            </div>
            <ng-template #showExport>
                <treo-message *ngIf="subscription.certifierType === subscriptionCertifierType.TRIAL; else other"
                              class="flex-auto"
                              appearance="outline" [showIcon]="false" type="info">
                    {{'auth.cdc.exportForTrialCertifier' | translate}}
                </treo-message>
                <ng-template #other>
                    <button *ngIf="!organism.accrochageDelegationDate" type="button"
                            mat-flat-button color="primary"
                            [disabled]="loadingFile " (click)="exportMail()">
                        {{'auth.cdc.export' | translate}}
                    </button>
                </ng-template>
            </ng-template>
        </div>
    </div>
</mat-card>
