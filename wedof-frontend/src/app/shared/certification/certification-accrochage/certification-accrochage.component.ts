import {
    Component,
    ElementRef,
    EventEmitter,
    Input,
    OnChanges,
    OnDestroy,
    OnInit,
    Output,
    SimpleChanges
} from '@angular/core';
import {Subscription, SubscriptionCertifierTypes} from '../../api/models/subscription';
import {SnackBarComponent} from '../../material/snack-bar/snack-bar.component';
import {displayTextSnackBar} from '../../utils/displayTextSnackBar';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../errors/errors.types';
import {Certification} from '../../api/models/certification';
import {TranslateService} from '@ngx-translate/core';
import {CdcFileService} from '../../api/services/cdc-file.service';
import {MatSnackBar} from '@angular/material/snack-bar';
import {Organism} from '../../api/models/organism';
import {takeUntil} from 'rxjs/operators';
import {Select} from '@ngxs/store';
import {UserState} from '../../api/state/user.state';
import {Observable, Subject} from 'rxjs';
import {User} from '../../api/models/user';
import {CdcFileStates} from '../../api/models/certification-folder';
import {BaseCardComponentDirective, RequiredCallSuper} from '../../utils/base-card/base-card.directive';

@Component({
    selector: 'app-certification-accrochage',
    templateUrl: './certification-accrochage.component.html',
    styleUrls: ['./certification-accrochage.component.scss']
})
export class CertificationAccrochageComponent extends BaseCardComponentDirective implements OnInit, OnChanges, OnDestroy {
    static COMPONENT_ID = 'accrochage';
    loadingFile = false;
    subscriptionCertifierType = SubscriptionCertifierTypes;
    loading = true;
    user: User;
    countCdcFilesExported = 0;

    @Input() certification: Certification;
    @Input() organism: Organism;
    @Input() subscription: Subscription;
    @Input() isDefaultCertifier: boolean;
    @Output() certificationUpdated: EventEmitter<Certification> = new EventEmitter<Certification>();

    @Select(UserState.user) user$: Observable<User>;

    private _unsubscribeAll = new Subject<void>();

    constructor(
        private _translateService: TranslateService,
        private _cdcFileService: CdcFileService,
        private _snackBar: MatSnackBar,
        private _el: ElementRef
    ) {
        super(CertificationAccrochageComponent.COMPONENT_ID, _el);
    }

    ngOnInit(): void {
        this.user$.pipe(takeUntil(this._unsubscribeAll)).subscribe(user => {
            this.user = user;
            this.loading = false;
        });
        this._cdcFileService.list({state: CdcFileStates.EXPORTED}).subscribe((cdcFiles) => {
            this.countCdcFilesExported = cdcFiles.total;
        });
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes.certification) {
            this.panelLoading();
            this.initForm();
        }
    }

    protected initForm(): void {
        if (!this.certification) {
            return;
        }
        this.panelLoaded();
    }

    exportMail(): void {
        this.loadingFile = true;
        this._cdcFileService.generateXmlFileForCdc(this.certification.id).subscribe(
            () => {
                this.loadingFile = false;
                const feedbackText = this.subscription.certifierType === SubscriptionCertifierTypes.TRIAL ?
                    'La version d\'essai de la ' : 'La ';
                this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(
                    this._translateService.instant(feedbackText + ' génération du fichier XML a commencé, vous devriez bientôt le recevoir par mail.'), 7000));
            },
            (httpErrorResponse: HttpErrorResponse) => {
                this.loadingFile = false;
                this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(
                    (httpErrorResponse.error as ApiError).errorMessages.toString(), 5000, 'red'));
            }
        );
    }

    isOrganismExportable(): boolean {
        return !!(this.organism.cdcClientId && this.organism.cdcContractId);
    }

    isCertificationExportable(): boolean {
        return !!this.certification.obtentionSystem;
    }


    ngOnDestroy(): RequiredCallSuper {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
        return super.ngOnDestroy();
    }
}
