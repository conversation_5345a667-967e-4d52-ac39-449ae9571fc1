<form class="flex flex-col mb-2" [formGroup]="formGroup">
    <mat-card class="flex-col mt-3 p-5 pb-0 pt-3 border shadow-none sm">
        <div class="flex items-center mb-2">
            <mat-icon class="mr-3 text-4xl card-loading-show" svgIcon="group"
                      color="primary">
            </mat-icon>
            <div>
                <div class="text-xl font-semibold card-loading-show">
                    {{ ('private.certification.partners.table.partnership.' + (isRequest(certificationPartner) ? 'titleRequest' : 'title'))  | translate }}
                </div>
                <div class="text-secondary text-md card-loading-hidden">
                    <a [routerLink]="['/aide/guides/certificateurs/gerer-partenariats']">Consulter la documentation</a>
                </div>
            </div>
            <div class="ml-auto flex -mr-4 card-loading-show">
                <app-activity-form-menu *ngIf="isCertifier" [entityClass]="EntityClass.CERTIFICATION_FOLDER"
                                        [entityId]="certificationPartner.id"
                                        (createdActivity)="createdActivity($event)">

                </app-activity-form-menu>
            </div>
        </div>
        <div class="flex flex-col mb-3 card-loading-hidden" *ngIf="!loading">
            <div class="grid grid-cols-6 gap-2">
                <app-form-field-static *ngIf="certificationPartner"
                                       class="col-span-3"
                                       [value]="certificationPartner | certificationPartnerStateName"
                                       label="private.certification.partners.table.partnership.stateLabel"
                                       type="text"></app-form-field-static>
                <app-form-fields class="col-span-3"
                                 formGroupName="certificationPartner"
                                 [entity]="certificationPartner"
                                 [appFormFieldsData]="appFormFieldsGroupData['compliance']"
                                 [formGroup]="formGroup"></app-form-fields>
            </div>
            <app-form-field-static
                *ngIf="certification.type !== certificationTypes.INTERNAL && certificationPartner?.habilitation &&
                certificationPartner.state === states.ACTIVE"
                [value]="('private.certification.partners.table.habilitation.' + certificationPartner.habilitation) | translate"
                label="private.certification.partners.table.habilitation.label"
                type="text"></app-form-field-static>
            <app-form-field-static
                *ngIf="subscription?.allowCertifierPlus && (states.ACTIVE === certificationPartner.state || states.REVOKED === certificationPartner.state) && certification?.openDataLastUpdate"
                [help]="'private.certification.partners.estimatedRegistrationFoldersCountToolTip'"
                [value]="certificationPartner.estimatedRegistrationFoldersCount"
                label="{{'private.certification.partners.estimatedRegistrationFoldersCount' | translate}} {{certification.openDataLastUpdate | date}}"
                type="text"></app-form-field-static>
            <ng-container *ngIf="certificationPartner?.urls?.length; else noUrls">
                <mat-label class="font-medium">{{'private.certification.partners.websites' | translate }}</mat-label>
                <app-form-field-static *ngFor="let url of certificationPartner.urls"
                    [value]="url"
                    type="url"></app-form-field-static>
            </ng-container>
            <ng-template #noUrls>
                <app-form-field-static
                    [value]="null"
                    label="private.certification.partners.websites"
                    type="text"></app-form-field-static>
            </ng-template>
            <app-form-field-static *ngIf="subscription?.allowCertifierPlus"
                                   [value]="certifiersName"
                                   label="private.certification.partners.table.certifiers"
                                   type="text"></app-form-field-static>
            <ng-container *ngIf="isCertifier">
                <app-form-fields *ngIf="subscription?.allowCertifierPlus; else showPromo"
                                 formGroupName="certificationPartner"
                                 [entity]="certificationPartner"
                                 [appFormFieldsData]="appFormFieldsGroupData['partnership']"
                                 [formGroup]="formGroup">
                </app-form-fields>
                <ng-template #showPromo>
                    <app-subscription-small-card [organism]="organism"
                                                 [subscription]="subscription"
                                                 [fromPage]="'managePartnership'"
                                                 [type]="'managePartnership'"
                                                 [showFullCard]="false"
                    >
                    </app-subscription-small-card>
                </ng-template>
            </ng-container>
            <span *ngIf="!certificationPartner"
                  class="text-secondary">{{ 'private.certification.partners.table.partnership.certifierPlaceholder' | translate }}</span>
        </div>
        <app-file-list *ngIf="subscription?.allowCertifierPlus && isCertifier"
                       [isOwner]="isCertifier"
                       [entity]="certificationPartner"
                       [fileTypes]="certification.certificationPartnerFileTypes"
                       [orderedStates]="orderedCertificationPartnerStates"
                       [showManageFile]="isCertifier"
                       [linkManageFile]="'/certification/partenariats/' + certification.certifInfo"
                       [entityParentId]="certification.id"
                       entityApiPath="certificationPartners"
                       entityIdProperty="id"
                       titleStateChange="private.certification.partners.table.partnership.state.">
        </app-file-list>
        <div *ngIf="subscription?.allowCertifierPlus && (isCertifier || isPotentialCertifier)"
             class="flex items-center justify-end border-t pl-5 pr-10 -mx-5 py-3 light:bg-cool-gray-50 dark:bg-cool-gray-700 card-loading-hidden">
            <mat-error *ngIf="errorMessages.length">
                <ul>
                    <li *ngFor="let errorMessage of errorMessages">{{ errorMessage }}</li>
                </ul>
            </mat-error>
            <app-certification-certifier-partnership-menu *ngIf="isCertifier"
                                                          class="-mr-4 flex items-center justify-end"
                                                          (errorMessages)="setErrorMessages($event)"
                                                          (loading)="setLoading($event)"
                                                          [formGroup]="formGroup"
                                                          [allSkillSetIds]="allSkillSetIds"
                                                          [certification]="certification"
                                                          [isExpiredCertification]="isExpiredCertification()"
                                                          (processedCertificationPartner)="refresh($event)"
                                                          (initCertificationPartner)="initForm(certificationPartner, certification)"
                                                          [certificationPartner]="certificationPartner"
            ></app-certification-certifier-partnership-menu>
            <div *ngIf="certificationPartner._links.certifier === undefined && isPotentialCertifier"
                 (click)="confirmPartnership()">
                <button mat-flat-button color="primary">
                    {{'private.certification.partners.partnership.title' | translate}}
                </button>
            </div>
        </div>
    </mat-card>
</form>
