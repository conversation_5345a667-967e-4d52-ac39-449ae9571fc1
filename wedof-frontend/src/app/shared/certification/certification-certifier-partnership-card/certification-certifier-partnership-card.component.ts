import {
    Component,
    ElementRef,
    EventEmitter,
    Input,
    OnChanges,
    OnDestroy,
    OnInit,
    Output,
    SimpleChanges
} from '@angular/core';
import {
    CertificationPartner,
    CertificationPartnerHabilitations,
    CertificationPartnerStates
} from '../../api/models/certification-partner';
import {Certification, CertificationTypes} from '../../api/models/certification';
import {FormBuilder, FormGroup} from '@angular/forms';
import {filter, finalize, switchMap, takeUntil, tap} from 'rxjs/operators';
import {AppFormFieldData, Choices} from '../../material/app-form-field/app-form-field.component';
import {TranslateService} from '@ngx-translate/core';
import {combineLatest, Observable, of, Subject} from 'rxjs';
import {Select} from '@ngxs/store';
import {SubscriptionState} from '../../api/state/subscription.state';
import {Subscription} from '../../api/models/subscription';
import {OrganismState} from '../../api/state/organism.state';
import {Organism} from '../../api/models/organism';
import {EntityClass} from '../../utils/enums/entity-class';
import {Activity} from '../../api/models/activity';
import {BaseCardComponentDirective, RequiredCallSuper} from '../../utils/base-card/base-card.directive';
import {OrganismService} from '../../api/services/organism.service';
import {MatDialog} from '@angular/material/dialog';
import {CertificationPartnerService} from '../../api/services/certification-partner.service';
import {ActionConfirmationComponent} from '../../material/action-confirmation/action-confirmation.component';
import {CertificationPartnerAuditResults} from '../../api/models/certification-partner-audit';
import {CertificationPartnerAuditResultToIconPipe} from '../../pipes/certification-partner-audit-state-to-icon.pipe';
import {City} from '../../api/models/attendee';
import {CityService} from '../../api/services/city.service';
import {displaySkillSetTitle, Skill} from '../../api/models/skill';

@Component({
    selector: 'app-certification-certifier-partnership-card',
    templateUrl: './certification-certifier-partnership-card.component.html',
    styleUrls: ['./certification-certifier-partnership-card.component.scss']
})
export class CertificationCertifierPartnershipCardComponent extends BaseCardComponentDirective implements OnInit, OnChanges, OnDestroy {
    static COMPONENT_ID = 'gestionPartenariats';
    formGroup: FormGroup;
    organism: Organism;
    subscription: Subscription;
    appFormFieldsGroupData: AppFormFieldData[][];
    certificationTypes = CertificationTypes;

    allSkillSetIds: number[] = null;
    certifiersName = '';
    isPotentialCertifier = false;
    loading = true;
    errorMessages: string[] = [];
    states = CertificationPartnerStates;
    orderedCertificationPartnerStates: CertificationPartnerStates[] = Object.values(CertificationPartnerStates);
    readonly EntityClass = EntityClass;
    readonly CertificationTypes = CertificationTypes;

    @Input() certificationPartner: CertificationPartner;
    @Input() certification: Certification;
    @Input() isCertifier: boolean;
    @Input() trainingsZones?: City[];
    @Input() skillSets?: Skill[];
    @Output() certificationPartnerChange?: EventEmitter<CertificationPartner> = new EventEmitter<CertificationPartner>();

    @Select(SubscriptionState.subscription) subscription$: Observable<Subscription>;
    @Select(OrganismState.organism) organism$: Observable<Organism>;

    private _unsubscribeAll = new Subject<void>();

    constructor(private _fb: FormBuilder,
                private _translateService: TranslateService,
                private _organismService: OrganismService,
                private _dialog: MatDialog,
                private _cityService: CityService,
                private _certificationPartnerService: CertificationPartnerService,
                private _certificationPartnerAuditResultToIconPipe: CertificationPartnerAuditResultToIconPipe,
                private _el: ElementRef) {
        super(CertificationCertifierPartnershipCardComponent.COMPONENT_ID, _el);
    }

    ngOnInit(): void {
        combineLatest([
            this.organism$,
            this.subscription$
        ]).pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe(([organism, subscription]) => {
            this.subscription = subscription;
            this.organism = organism;
            this.initForm(this.certificationPartner, this.certification);
        });
    }

    ngOnChanges(changes: SimpleChanges): void {
        this.panelLoading();
        this.initForm(this.certificationPartner, this.certification);
    }

    initForm(certificationPartner: CertificationPartner, certification: Certification): void {
        this.loading = true;
        this.errorMessages = [];
        this.formGroup = this._fb.group({
            certificationPartner: this._fb.group({})
        });
        const appFormFieldsData: AppFormFieldData[][] = [];
        appFormFieldsData['compliance'] = [
            {
                value: !this.subscription?.allowCertifierPlus || certificationPartner.compliance === CertificationPartnerAuditResults.NONE ?
                    this._translateService.instant('private.certification.audit.result.none') : certificationPartner.compliance,
                controlName: 'compliance',
                label: 'private.common.certification.compliance',
                help: 'private.common.certification.complianceTooltip',
                type: certificationPartner.compliance === CertificationPartnerAuditResults.NONE ? 'text' : 'select',
                choices: [
                    {
                        key: this._translateService.instant('private.certification.audit.result.compliant'),
                        value: CertificationPartnerAuditResults.COMPLIANT,
                        icon: this._certificationPartnerAuditResultToIconPipe.transform(CertificationPartnerAuditResults.COMPLIANT),
                        color: undefined,
                        iconClass: 'text-green'
                    },
                    {
                        key: this._translateService.instant('private.certification.audit.result.partiallyCompliant'),
                        value: CertificationPartnerAuditResults.PARTIALLY_COMPLIANT,
                        icon: this._certificationPartnerAuditResultToIconPipe.transform(CertificationPartnerAuditResults.PARTIALLY_COMPLIANT),
                        color: undefined,
                        iconClass: 'text-orange'
                    },
                    {
                        key: this._translateService.instant('private.certification.audit.result.nonCompliant'),
                        value: CertificationPartnerAuditResults.NON_COMPLIANT,
                        icon: this._certificationPartnerAuditResultToIconPipe.transform(CertificationPartnerAuditResults.NON_COMPLIANT),
                        color: 'warn'
                    }
                ],
                disabled: !(this.isCertifier && this.subscription?.allowCertifierPlus && certificationPartner.compliance &&
                    certificationPartner.compliance !== CertificationPartnerAuditResults.NONE)
            },
        ];

        if (this.subscription?.allowCertifierPlus) {
            if (certificationPartner._links.certifier === undefined) {
                this._organismService.listByUrl(this.certification._links.certifiers.href).subscribe(certifiers => {
                    this.isPotentialCertifier = certifiers.find(certifier => certifier.siret === this.organism.siret) !== undefined;
                    this.certifiersName = certifiers.map(certifier => certifier.name).join(', ');
                });
            } else {
                this.certifiersName = this.certificationPartner._links.certifier.name;
            }

            const comment = certificationPartner.comment ? certificationPartner.comment : certification.partnershipComment; // Don't use "??", we want to skip empty string
            const amountHt = certificationPartner.amountHt ? certificationPartner.amountHt : certification.amountHt; // Don't use "??", we want to skip empty string
            const isInternalCertification = certification.type === this.certificationTypes.INTERNAL;

            let skillSetChoices: Choices = [];
            let skillSetValues = [];
            if (this.skillSets) {
                skillSetChoices = [{
                    key: 'Tous les blocs de compétences de la certification',
                    value: '*'
                }];
                this.skillSets.forEach((skillSet) => {
                    skillSetChoices.push({
                        value: skillSet.id,
                        key: displaySkillSetTitle(skillSet)
                    });
                });
                this.allSkillSetIds = this.skillSets?.map((skillSet) => skillSet.id);
                if (certificationPartner.skillSets.length === this.skillSets.length || !certificationPartner.skillSets.length) {
                    skillSetValues = ['*'];
                    skillSetChoices.filter((skillSetChoice) => skillSetChoice.disabled = skillSetChoice.value !== '*');
                } else {
                    skillSetValues = certificationPartner.skillSets?.length ? certificationPartner.skillSets.map((skill) => skill.id) : [];
                }
            }

            appFormFieldsData['partnership'] = [
                {
                    removed: isInternalCertification ? false : certificationPartner.state === CertificationPartnerStates.ACTIVE,
                    controlName: 'habilitation',
                    required: true,
                    type: 'select',
                    disabled: !this.isCertifier,
                    label: 'private.certification.partners.table.habilitation.label',
                    choices: [
                        {
                            key: this._translateService.instant('private.certification.partners.table.habilitation.train'),
                            value: CertificationPartnerHabilitations.TRAIN
                        },
                        {
                            key: this._translateService.instant('private.certification.partners.table.habilitation.evaluate'),
                            value: CertificationPartnerHabilitations.EVALUATE
                        },
                        {
                            key: this._translateService.instant('private.certification.partners.table.habilitation.train_evaluate'),
                            value: CertificationPartnerHabilitations.TRAIN_EVALUATE
                        },
                    ]
                },
                {
                    value: comment,
                    controlName: 'comment',
                    label: 'private.certification.partners.table.comment',
                    type: 'textarea',
                    maxLength: 5000,
                    disabled: !this.isCertifier,
                    help: 'private.certification.partners.table.commentTooltip'
                },
                {
                    value: amountHt,
                    controlName: 'amountHt',
                    label: 'private.common.certification.amountHt',
                    help: 'private.common.certification.amountHtTooltip',
                    type: 'number',
                    min: 1,
                    disabled: !this.isCertifier
                },
                {
                    controlName: 'tags',
                    removed: !this.isCertifier,
                    label: 'common.tags.label',
                    placeholder: 'common.tags.placeholder',
                    type: 'tags',
                    isCreateAvailable: true
                },
                {
                    controlName: 'skillSets',
                    label: 'private.certification.partners.skillSets',
                    type: 'select',
                    disabled: !this.isCertifier,
                    removed: this.certification.allowPartialSkillSets === false,
                    searchMultiple: true,
                    placeholder: 'private.certification.folders.createFolder.form.skillsSet.placeholder',
                    choices: skillSetChoices,
                    value: skillSetValues,
                    change: (controlName, newValue, formData) => {
                        const appFormFieldSkillSets = formData.find(field => field.controlName === 'skillSets');
                        if (newValue) {
                            if (newValue.includes('*') || newValue.sort().toString() === this.allSkillSetIds.sort().toString()) {
                                appFormFieldSkillSets.choices.forEach((choice) => {
                                    if (choice.value !== '*') {
                                        choice.disabled = true;
                                    }
                                });
                                appFormFieldSkillSets.value = ['*'];
                            } else {
                                appFormFieldSkillSets.choices.forEach((choice) => choice.disabled = false);
                                appFormFieldSkillSets.value = newValue;
                            }
                        } else {
                            appFormFieldSkillSets.value = null;
                        }
                        return [appFormFieldSkillSets];
                    }
                },
                {
                    controlName: 'trainingsZone',
                    removed: !this.isCertifier,
                    value: this.trainingsZones,
                    label: 'private.certification.partners.trainingsZone',
                    type: 'search',
                    removable: true,
                    searchMultiple: true,
                    placeholder: 'private.common.form.placeholder',
                    searchNoEntriesFoundLabel: 'common.actions.search.noResult',
                    searchComparisonProperty: 'postalCode',
                    searchMethod: (searchTerm) => searchTerm ? this._cityService.listCitiesByName(searchTerm) :
                        this.trainingsZones ? of(this.trainingsZones) : of([]),
                    searchResultFormatter: (city: City) => city.name + ' (' + city.postalCode + ')',
                }
            ];
        }
        this.loading = false;
        this.appFormFieldsGroupData = appFormFieldsData;
        this.panelLoaded();
    }

    setErrorMessages(error: string[]): void {
        this.errorMessages = error;
    }

    setLoading(loading: boolean): void {
        this.loading = loading;
    }

    refresh(certificationPartner: CertificationPartner): void {
        this.certificationPartner = certificationPartner;
        this.certificationPartnerChange.emit(certificationPartner);
    }

    isRequest(certificationPartner: CertificationPartner): boolean {
        return !certificationPartner || [
            CertificationPartnerStates.DRAFT, CertificationPartnerStates.PROCESSING, CertificationPartnerStates.ABORTED, CertificationPartnerStates.REFUSED
        ].includes(certificationPartner.state);
    }

    ngOnDestroy(): RequiredCallSuper {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
        return super.ngOnDestroy();
    }

    createdActivity(activity: Activity): void {
        this.certificationPartnerChange.emit({...this.certificationPartner});
    }

    confirmPartnership(): void {
        const dialogRef = this._dialog.open(ActionConfirmationComponent, {
            panelClass: 'full-page-scroll-30',
            height: 'auto',
            data: {
                title: 'private.certification.partners.partnership.title',
                messageKey: 'private.certification.partners.partnership.messageKey'
            }
        });
        dialogRef.componentInstance.actionValue$.pipe(
            filter(confirmation => confirmation),
            tap(() => {
                this.loading = true;
            }),
            switchMap(() =>
                this._certificationPartnerService.update(this.certification.certifInfo, this.certificationPartner._links.partner.siret, {certifierSiret: this.organism.siret})
            ),
            finalize(() => {
                dialogRef.componentInstance.close();
                this.loading = false;
            }),
        ).subscribe((certificationPartnerUpdated: CertificationPartner) => {
            this.certificationPartnerChange.emit(certificationPartnerUpdated);
        });
    }

    isExpiredCertification(): boolean {
        if (this.certification.cpfDateEnd) {
            return (new Date(this.certification.cpfDateEnd)) < new Date();
        } else {
            return false;
        }
    }
}
