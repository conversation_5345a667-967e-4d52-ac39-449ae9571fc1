import {
    Component,
    ElementRef,
    EventEmitter,
    Input,
    OnChanges,
    OnDestroy,
    OnInit,
    Output,
    SimpleChanges
} from '@angular/core';
import {Certification, CertificationFolderFileType} from '../../api/models/certification';
import {StateChoices} from '../../file/file-type-list/file-type-list.component';
import {CertificationFolderStates} from '../../api/models/certification-folder';
import {TranslateService} from '@ngx-translate/core';
import {CertificationFolderStateToIconPipe} from '../../pipes/certification-folder-state-to-icon.pipe';
import {CertificationFolderStateToColorPipe} from '../../pipes/certification-folder-state-to-color.pipe';
import {FormBuilder, FormGroup} from '@angular/forms';
import {AppFormFieldData} from '../../material/app-form-field/app-form-field.component';
import {finalize} from 'rxjs/operators';
import {SnackBarComponent} from '../../material/snack-bar/snack-bar.component';
import {displayTextSnackBar} from '../../utils/displayTextSnackBar';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../errors/errors.types';
import {CertificationService} from '../../api/services/certification.service';
import {MatSnackBar} from '@angular/material/snack-bar';
import {BaseCardComponentDirective, RequiredCallSuper} from '../../utils/base-card/base-card.directive';

@Component({
    selector: 'app-certification-manage-certification-folders',
    templateUrl: './certification-manage-certification-folders.component.html',
    styleUrls: ['./certification-manage-certification-folders.component.scss']
})
export class CertificationManageCertificationFoldersComponent extends BaseCardComponentDirective implements OnInit, OnChanges, OnDestroy {
    static COMPONENT_ID = 'gestionDossiers';
    stateChoicesCertificationFoldersFileTypes: StateChoices;
    errorMessages: string[] = [];
    loading = false;
    formGroup: FormGroup;
    appFormFieldsData: AppFormFieldData[];


    @Input() certification: Certification;
    @Input() isCertifier: boolean;
    @Output() certificationUpdated: EventEmitter<Certification> = new EventEmitter<Certification>();

    constructor(private _translateService: TranslateService,
                private _snackBar: MatSnackBar,
                private _formBuilder: FormBuilder,
                private _certificationService: CertificationService,
                private _certificationFolderStateToIconPipe: CertificationFolderStateToIconPipe,
                private _certificationFolderStateToColorPipe: CertificationFolderStateToColorPipe,
                private _el: ElementRef) {
        super(CertificationManageCertificationFoldersComponent.COMPONENT_ID, _el);
    }

    ngOnInit(): void {
        this.stateChoicesCertificationFoldersFileTypes = Object.values(CertificationFolderStates).map(value => ({
            key: this._translateService.instant('private.common.certificationFolder.state.' + value),
            icon: this._certificationFolderStateToIconPipe.transform(value as string),
            color: this._certificationFolderStateToColorPipe.transform(value as string),
            value: value
        }));
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes.certification) {
            this.panelLoading();
            this.initForm();
        }
    }

    ngOnDestroy(): RequiredCallSuper {
        return super.ngOnDestroy();
    }

    protected initForm(): void {
        this.errorMessages = [];
        this.loading = true;
        if (!this.certification) {
            return;
        }
        this.formGroup = this._formBuilder.group({
            certification: this._formBuilder.group({})
        });
        this.appFormFieldsData = [
            {
                controlName: 'autoRegistering',
                label: 'private.common.certification.autoRegistering.title',
                help: 'private.common.certification.autoRegistering.help',
                type: 'radio',
                inline: true,
                choices: [
                    {key: this._translateService.instant('common.actions.yes'), value: true},
                    {key: this._translateService.instant('common.actions.no'), value: false}
                ],
            },
        ];
        this.loading = false;
        this.panelLoaded();
    }

    onCertificationFolderFileTypeListUpdated(certificationFolderFileTypes: CertificationFolderFileType[]): void {
        this.certification.certificationFolderFileTypes = certificationFolderFileTypes;
    }

    submit(): void {
        this.errorMessages = [];
        this.loading = true;
        const certificationUpdatedForm = this.formGroup.getRawValue().certification;

        const certification: Certification = {
            ...this.certification, ...{
                autoRegistering: certificationUpdatedForm.autoRegistering
            }
        };

        this._certificationService.update(certification).pipe(
            finalize(() => this.loading = false)
        ).subscribe(
            (updatedCertification) => {
                this.certificationUpdated.emit(updatedCertification);
                this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(this._translateService.instant('common.actions.certificationUpdatedSuccessfully', {
                        certificationName: updatedCertification.name
                    },
                )));
            },
            (httpErrorResponse: HttpErrorResponse) => {
                this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
            }
        );
    }
}
