<mat-card class="flex-col mt-3 p-5 pb-0 pt-3 border shadow-none sm" *ngIf="isCertifier; else showPartner">
    <div class="flex items-center mb-2">
        <mat-icon class="mr-3 card-loading-show icon-size-32" svgIcon="library_books" color="primary">
        </mat-icon>
        <div>
            <div
                class="text-xl font-semibold card-loading-show">{{ 'private.common.certification.manageMyCertificationFolders' | translate }}
            </div>
        </div>
    </div>
    <treo-message type="info" *ngIf="isCertifier && certification.hasMultipleCertifiers"
                  [showIcon]="false" class="mb-4 mt-2 text-center" appearance="outline">
        {{ 'private.certification.common.coCertifiers' | translate}}
    </treo-message>
    <form [formGroup]="formGroup" (submit)="submit()" class="flex flex-col mt-4">
        <app-form-fields class="grid grid-cols-6 gap-2 mb-5"
                         formGroupName="certification"
                         [entity]="certification"
                         [appFormFieldsData]="appFormFieldsData"
                         [formGroup]="formGroup">
        </app-form-fields>
        <ng-template class="mb-2 mt-2"
                     [ngTemplateOutlet]="fileTypeListTemplate"
                     [ngTemplateOutletContext]="{isCard: false, displayedColumns: ['allowAttendeeOrPartner', 'name', 'toState', 'actions'], allowExtraFields: true}">
        </ng-template>
        <div class="flex items-center mt-2 justify-end border-t pl-5 pr-10 -mx-5 py-3 light:bg-cool-gray-50 dark:bg-cool-gray-700 card-loading-hidden">
            <div class="flex">
                <mat-error *ngIf="errorMessages.length">
                    <ul>
                        <li *ngFor="let errorMessage of errorMessages">{{ errorMessage }}</li>
                    </ul>
                </mat-error>
                <button type="submit" class="flex align-center" mat-flat-button color="primary"
                        [disabled]="loading || !formGroup.dirty || formGroup.invalid">
                    <mat-progress-spinner class="mr-4" *ngIf="loading" [diameter]="24"
                                          mode="indeterminate"></mat-progress-spinner>
                    <ng-container *ngIf="!loading">{{ 'common.actions.update' | translate }}</ng-container>
                </button>
            </div>
        </div>
    </form>
</mat-card>

<ng-template #showPartner>
    <ng-template [ngTemplateOutlet]="fileTypeListTemplate"
                 [ngTemplateOutletContext]="{isCard: true, displayedColumns: ['name', 'toState'], allowExtraFields: false}">
    </ng-template>
</ng-template>

<ng-template #fileTypeListTemplate
             let-isCard="isCard"
             let-displayedColumns="displayedColumns"
             let-allowExtraFields="allowExtraFields">
    <app-file-type-list [isCard]="isCard"
                        [icon]="'checklist_rtl'"
                        [title]="'private.common.fileTypes.title' | translate"
                        [subtitle]="'private.common.certification.certificationFolderFileTypesSubtitle' | translate"
                        [displayedColumns]="displayedColumns"
                        [entityId]="certification.id"
                        [entityClass]="'Certification'"
                        [entityField]="'certificationFolderFileTypes'"
                        [fileTypes]="certification.certificationFolderFileTypes"
                        [stateChoices]="stateChoicesCertificationFoldersFileTypes"
                        [stateChoicesGenerated]="stateChoicesCertificationFoldersFileTypes"
                        [toStateLabel]="'private.certification.folders.actions.'"
                        [isDefaultCertifierOrOrganism]="isCertifier"
                        [allowExtraFields]="allowExtraFields"
                        (fileTypeListUpdated)="onCertificationFolderFileTypeListUpdated($event)">
    </app-file-type-list>
</ng-template>
