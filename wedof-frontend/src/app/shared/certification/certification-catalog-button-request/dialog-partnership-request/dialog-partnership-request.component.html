<app-dialog-layout (dialogClose)="close()" [actions]="actions"
                   [showCancel]="false"
                   [title]=" 'private.common.certification.managePartnership.requestDialog.title' | translate ">

    <p class="mt-2 mb-4">
        {{ "private.common.certification.managePartnership.requestDialog.message" | translate : {
        certifiersName: dialogData.certifiersName,
        certificationName: dialogData.certification.externalId + ' - ' + dialogData.certification.name
    } }}
    </p>

    <p class="mb-2">{{'private.common.certification.managePartnership.confirmQuestion' | translate}}</p>

    <div *ngIf="errorMessages?.length" class="flex items-center">
        <treo-message class="flex-auto" appearance="outline" [showIcon]="false" type="error">
            <ul>
                <li *ngFor="let errorMessage of errorMessages">
                    {{ errorMessage }}
                </li>
            </ul>
        </treo-message>
    </div>

    <ng-template #actions>
        <button type="submit" mat-flat-button color="primary" (click)="submit()" [disabled]="loading">
            <mat-progress-spinner *ngIf="loading; else submitLabel" [diameter]="24" mode="indeterminate">
            </mat-progress-spinner>
            <ng-template #submitLabel>
                {{ 'common.actions.submit' | translate }}
            </ng-template>
        </button>
    </ng-template>

</app-dialog-layout>
