import {Component, Inject} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material/dialog';
import {Certification} from '../../../api/models/certification';
import {CertificationPartnerService} from '../../../api/services/certification-partner.service';
import {HttpErrorResponse} from '@angular/common/http';
import {CertificationPartnerStates} from '../../../api/models/certification-partner';
import {ApiError} from '../../../errors/errors.types';

@Component({
    selector: 'app-dialog-partnership-request',
    templateUrl: './dialog-partnership-request.component.html',
    styleUrls: ['./dialog-partnership-request.component.scss']
})
export class DialogPartnershipRequestComponent {

    loading = false;
    errorMessages: string[] = [];

    constructor(
        public dialogRef: MatDialogRef<DialogPartnershipRequestComponent>,
        private _certificationPartnerService: CertificationPartnerService,
        @Inject(MAT_DIALOG_DATA) public dialogData: {
            certification: Certification;
            certifiersName: string,
            siret: string
        }
    ) {
    }

    close(): void {
        this.dialogRef.close();
    }

    submit(): void {
        this.loading = true;
        this._certificationPartnerService.update(this.dialogData.certification.certifInfo, this.dialogData.siret, {
            state: CertificationPartnerStates.PROCESSING
        }).subscribe({
            next: (certificationPartner) => {
                this.loading = false;
                this.dialogRef.close({data: certificationPartner});
            },
            error: (httpErrorResponse: HttpErrorResponse) => {
                this.loading = false;
                this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
            }
        });
    }
}
