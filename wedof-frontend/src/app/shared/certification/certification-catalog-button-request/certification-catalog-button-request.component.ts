import {Component, EventEmitter, Input, OnDestroy, OnInit, Output} from '@angular/core';
import {Certification} from '../../api/models/certification';
import {SnackBarComponent} from '../../material/snack-bar/snack-bar.component';
import {displayTextSnackBar} from '../../utils/displayTextSnackBar';
import {MatDialog} from '@angular/material/dialog';
import {MatSnackBar} from '@angular/material/snack-bar';
import {TranslateService} from '@ngx-translate/core';
import {CertificationPartner} from '../../api/models/certification-partner';
import {takeUntil} from 'rxjs/operators';
import {DialogPartnershipRequestComponent} from './dialog-partnership-request/dialog-partnership-request.component';
import {Select} from '@ngxs/store';
import {OrganismState} from '../../api/state/organism.state';
import {Observable, Subject} from 'rxjs';
import {Organism} from '../../api/models/organism';

@Component({
    selector: 'app-certification-catalog-button-request',
    templateUrl: './certification-catalog-button-request.component.html',
    styleUrls: ['./certification-catalog-button-request.component.scss']
})
export class CertificationCatalogButtonRequestComponent implements OnInit, OnDestroy {

    @Input() certification: Certification;
    @Input() class?: string;
    @Input() disabled = false;
    @Input() certifiersName: string;
    @Output() certificationPartnerUpdated = new EventEmitter<CertificationPartner>();

    @Select(OrganismState.organism) organism$: Observable<Organism>;

    currentOrganismSiret: string;

    private _unsubscribeAll = new Subject<void>();

    constructor(private _translateService: TranslateService,
                private _snackBar: MatSnackBar,
                private _dialog: MatDialog
    ) {
    }

    ngOnInit(): void {
        this.organism$.pipe(takeUntil(this._unsubscribeAll)).subscribe(organism => {
            this.currentOrganismSiret = organism.siret;
        });
    }

    openRequestPartnershipDialog(): void {
        const dialogRef = this._dialog.open(DialogPartnershipRequestComponent, {
            panelClass: 'full-page-scroll-30',
            data: {
                certification: this.certification,
                certifiersName: this.certifiersName,
                siret: this.currentOrganismSiret
            }
        });
        dialogRef.afterClosed().subscribe(result => {
            if (result?.data) {
                this.certificationPartnerUpdated.emit(result.data);
                this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(
                    this._translateService.instant('private.common.certification.managePartnership.requestDialog.success'), 7000));
            }
        });
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }
}
