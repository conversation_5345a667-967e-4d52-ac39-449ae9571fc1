import {Component, ElementRef, EventEmitter, Input, OnDestroy, Output, } from '@angular/core';
import {CertifierAccess, CertifierAccessState} from '../../api/models/certifier-access';
import {CertifierAccessService} from '../../api/services/certifier-access.service';
import {MatDialog} from '@angular/material/dialog';
import {
    BaseCardCertificationAccessManagementDirective
} from '../../utils/base-card/base-card-certification-access-management.directive';
import {RequiredCallSuper} from '../../utils/base-card/base-card.directive';

@Component({
    selector: 'app-certification-partner-access-card',
    templateUrl: './certification-partner-access-card.component.html',
    styleUrls: ['./certification-partner-access-card.component.scss']
})
export class CertificationPartnerAccessCardComponent extends BaseCardCertificationAccessManagementDirective implements OnDestroy {
    static COMPONENT_ID = 'donnees';
    certifierAccessStates = CertifierAccessState;

    @Input() certifierAccess: CertifierAccess;
    @Output() certifierAccessUpdated: EventEmitter<CertifierAccess> = new EventEmitter<CertifierAccess>();

    constructor(
        protected _dialog: MatDialog,
        protected _certifierAccessService: CertifierAccessService,
        private _el: ElementRef
    ) {
        super(_dialog, _certifierAccessService, CertificationPartnerAccessCardComponent.COMPONENT_ID, _el);
    }

    manageCertifierAccess(): void {
        this.panelLoading();
        this.openCertifierAccessDialog(this.certifierAccess).subscribe((updatedCertifierAccess) => {
            this.certifierAccess = updatedCertifierAccess;
            this.certifierAccessUpdated.emit(this.certifierAccess);
            this.panelLoaded();
        });
    }

    ngOnDestroy(): RequiredCallSuper {
        return super.ngOnDestroy();
    }
}
