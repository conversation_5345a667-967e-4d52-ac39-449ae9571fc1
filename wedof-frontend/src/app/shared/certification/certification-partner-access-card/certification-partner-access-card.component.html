<mat-card class="flex-col mt-3 p-5 pb-0 pt-3 border shadow-none sm" [ngClass]="{'card-loading':isLoadingCertifierAccess}">
    <div class="flex items-center mb-2">
        <mat-icon class="mr-3 card-loading-show text-4xl"
                  title="{{ 'private.certification.partners.table.accessState.' + certifierAccess.state | translate }}"
                  [ngClass]="certifierAccess.state | certifierAccessStateToColor">
            {{certifierAccess.state | certifierAccessStateToIcon}}
        </mat-icon>
        <div>
            <div class="text-xl font-semibold card-loading-show">{{ 'private.certification.partners.table.partnerAccess' | translate }}</div>
            <div class="text-secondary text-md card-loading-hidden">
                {{ 'private.certification.partners.table.partnerAccessSubtitle' | translate: certifierAccess._links.certifier }}
                <a [routerLink]="['/aide/guides/organismes-formation/autoriser-certificateur']">(documentation)</a>
            </div>
        </div>
    </div>
    <div class="mb-3 card-loading-hidden">
        {{ 'private.certification.partners.table.access' | translate }}
        {{ ('private.certification.partners.table.accessState.' + certifierAccess.state) | translate | lowercase }}
        {{ 'common.actions.search.sinceDate' | translate: {
        date: (certifierAccess.state === certifierAccessStates.WAITING ? certifierAccess.createdOn :
               certifierAccess.state === certifierAccessStates.TERMINATED || certifierAccess.state === certifierAccessStates.REFUSED ?
               certifierAccess.terminatedOn : certifierAccess.state === certifierAccessStates.ACCEPTED && certifierAccess.activatedOn) | date:'mediumDate'
    } }}
    </div>
    <div
        class="flex items-center justify-end border-t -mx-5 px-5 py-3 light:bg-cool-gray-50 dark:bg-cool-gray-700 card-loading-hidden">
        <button mat-flat-button
                *ngIf="certifierAccess.state === certifierAccessStates.WAITING"
                (click)="manageCertifierAccess()"
                color="primary"
                type="button">
            {{ "private.training-organism.certifications.actions.manage" | translate }}
        </button>
        <button mat-flat-button
                *ngIf="certifierAccess.state === certifierAccessStates.ACCEPTED"
                (click)="manageCertifierAccess()"
                color="warn"
                type="button">
            {{ "private.training-organism.certifications.actions.terminate" | translate }}
        </button>
        <button mat-flat-button
                *ngIf="certifierAccess.state === certifierAccessStates.REFUSED"
                (click)="manageCertifierAccess()"
                color="primary"
                type="button">
            {{ "private.training-organism.certifications.actions.authorize" | translate }}
        </button>
        <button mat-flat-button
                *ngIf="certifierAccess.state === certifierAccessStates.TERMINATED"
                (click)="manageCertifierAccess()"
                color="primary"
                type="button">
            {{ "private.training-organism.certifications.actions.authorizeAgain" | translate }}
        </button>
    </div>
</mat-card>
