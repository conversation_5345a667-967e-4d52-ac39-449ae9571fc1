<mat-card class="flex-col mt-3 p-5 pb-0 pt-3 border shadow-none sm">
    <div class="flex items-center mb-2">
        <mat-icon class="mr-3 card-loading-show icon-size-32" svgIcon="group" color="primary">
        </mat-icon>
        <div>
            <div
                class="text-xl font-semibold card-loading-show">{{'private.common.certification.manageMyPartnershipTitle' | translate}}</div>
        </div>
    </div>
    <treo-message type="info" *ngIf="certification.hasMultipleCertifiers"
                  [showIcon]="false" class="mb-4 mt-2 text-center" appearance="outline">
        {{ 'private.certification.common.coCertifiers' | translate}}
    </treo-message>
    <form [formGroup]="formGroup" (submit)="submit()" class="flex flex-col mt-2">
        <app-form-fields class="grid grid-cols-6 gap-2 "
                         formGroupName="certification"
                         [entity]="certification"
                         [appFormFieldsData]="appFormFieldsData"
                         [formGroup]="formGroup">
        </app-form-fields>
        <app-file-type-list [isCard]="false"
                            [icon]="'checklist_rtl'"
                            [title]="'private.common.certification.certificationPartnerFileTypesTitle' | translate"
                            [subtitle]="'private.common.certification.certificationPartnerFileTypesSubtitle' | translate"
                            [displayedColumns]="['allowAttendeeOrPartner', 'name', 'toState', 'actions']"
                            [entityId]="certification.id"
                            [entityClass]="'Certification'"
                            [entityField]="'certificationPartnerFileTypes'"
                            [fileTypes]="certification.certificationPartnerFileTypes"
                            [stateChoices]="stateChoicesCertificationPartnerFileTypes"
                            [stateChoicesGenerated]="stateChoicesCertificationPartnerFileTypesGenerated"
                            [toStateLabel]="'private.certification.partners.table.partnership.state.'"
                            [isDefaultCertifierOrOrganism]="true"
                            [allowExtraFields]="true"
                            (fileTypeListUpdated)="onCertificationPartnerFileTypeListUpdated($event)">
        </app-file-type-list>
        <div
            class="flex items-center mt-2 justify-end border-t pl-5 pr-10 -mx-5 py-3 light:bg-cool-gray-50 dark:bg-cool-gray-700 card-loading-hidden">
            <div class="flex">
                <mat-error *ngIf="errorMessages.length">
                    <ul>
                        <li *ngFor="let errorMessage of errorMessages">{{ errorMessage }}</li>
                    </ul>
                </mat-error>
                <button type="submit" class="flex align-center" mat-flat-button color="primary"
                        [disabled]="loading || !formGroup.dirty || formGroup.invalid">
                    <mat-progress-spinner class="mr-4" *ngIf="loading" [diameter]="24"
                                          mode="indeterminate"></mat-progress-spinner>
                    <ng-container *ngIf="!loading">{{ 'common.actions.update' | translate}}</ng-container>
                </button>
            </div>
        </div>
    </form>
</mat-card>
