import {
    Component,
    ElementRef,
    EventEmitter,
    Input,
    OnChanges,
    OnDestroy,
    OnInit,
    Output,
    SimpleChanges
} from '@angular/core';
import {Certification, CertificationPartnerFileTypes} from '../../api/models/certification';
import {FormBuilder, FormGroup} from '@angular/forms';
import {AppFormFieldData} from '../../material/app-form-field/app-form-field.component';
import {CertificationService} from '../../api/services/certification.service';
import {TranslateService} from '@ngx-translate/core';
import {MatSnackBar} from '@angular/material/snack-bar';
import {finalize} from 'rxjs/operators';
import {SnackBarComponent} from '../../material/snack-bar/snack-bar.component';
import {displayTextSnackBar} from '../../utils/displayTextSnackBar';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../errors/errors.types';
import {StateChoices} from '../../file/file-type-list/file-type-list.component';
import {CertificationPartnerStates} from '../../api/models/certification-partner';
import {BaseCardComponentDirective, RequiredCallSuper} from '../../utils/base-card/base-card.directive';

@Component({
    selector: 'app-certification-manage-partnership',
    templateUrl: './certification-manage-partnership.component.html',
    styleUrls: ['./certification-manage-partnership.component.scss']
})
export class CertificationManagePartnershipComponent extends BaseCardComponentDirective implements OnInit, OnChanges, OnDestroy {
    static COMPONENT_ID = 'gestionPartenariats';
    errorMessages: string[] = [];
    loading = false;
    formGroup: FormGroup;
    appFormFieldsData: AppFormFieldData[];
    stateChoicesCertificationPartnerFileTypes: StateChoices;
    stateChoicesCertificationPartnerFileTypesGenerated: StateChoices;

    @Input() certification: Certification;
    @Output() certificationUpdated: EventEmitter<Certification> = new EventEmitter<Certification>();

    constructor(
        private _certificationService: CertificationService,
        private _translateService: TranslateService,
        private _snackBar: MatSnackBar,
        private _formBuilder: FormBuilder,
        private _el: ElementRef
    ) {
        super(CertificationManagePartnershipComponent.COMPONENT_ID, _el);
    }

    ngOnInit(): void {
        this.stateChoicesCertificationPartnerFileTypes = [{
            key: this._translateService.instant('private.certification.partners.table.partnership.state.processing'),
            value: CertificationPartnerStates.PROCESSING
        }];
        this.stateChoicesCertificationPartnerFileTypesGenerated = Object.values(CertificationPartnerStates)
            .filter((state) => state !== CertificationPartnerStates.DRAFT)
            .map(value => ({
                key: this._translateService.instant('private.certification.partners.table.partnership.state.' + value),
                value: value
            }));
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes.certification) {
            this.panelLoading();
            this.initForm();
        }
    }


    ngOnDestroy(): RequiredCallSuper {
        return super.ngOnDestroy();
    }

    protected initForm(): void {
        this.errorMessages = [];
        this.loading = true;
        if (!this.certification) {
            return;
        }
        this.formGroup = this._formBuilder.group({
            certification: this._formBuilder.group({})
        });
        this.appFormFieldsData = [
            {
                controlName: 'allowPartnershipRequest',
                label: 'private.common.certification.allowPartnershipRequest',
                type: 'radio',
                inline: true,
                choices: [
                    {key: this._translateService.instant('common.actions.yes'), value: true},
                    {key: this._translateService.instant('common.actions.no'), value: false}
                ],
            },
            {
                controlName: 'partnershipComment',
                label: 'private.common.certification.partnershipComment',
                type: 'textarea',
                placeholder: 'private.common.certification.partnershipCommentPlaceholder',
                icon: 'notes'
            }
        ];
        this.loading = false;
        this.panelLoaded();
    }

    submit(): void {
        this.errorMessages = [];
        this.loading = true;
        const certificationUpdatedForm = this.formGroup.getRawValue().certification;

        const certification: Certification = {
            ...this.certification, ...{
                allowPartnershipRequest: certificationUpdatedForm.allowPartnershipRequest,
                partnershipComment: certificationUpdatedForm.partnershipComment
            }
        };

        this._certificationService.update(certification).pipe(
            finalize(() => this.loading = false)
        ).subscribe(
            (updatedCertification) => {
                this.certificationUpdated.emit(updatedCertification);
                this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(this._translateService.instant('common.actions.certificationUpdatedSuccessfully', {
                        certificationName: updatedCertification.name
                    },
                )));
            },
            (httpErrorResponse: HttpErrorResponse) => {
                this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
            }
        );
    }

    onCertificationPartnerFileTypeListUpdated(certificationPartnerFileTypes: CertificationPartnerFileTypes[]): void {
        this.certification.certificationPartnerFileTypes = certificationPartnerFileTypes;
    }
}
