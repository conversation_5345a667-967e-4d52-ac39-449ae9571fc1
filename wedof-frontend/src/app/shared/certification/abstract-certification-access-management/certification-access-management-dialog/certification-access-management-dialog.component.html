<app-dialog-layout [title]="'private.training-organism.certifications.actions.modal.title.' + (certifierAccess.state === certifierAccessStates.ACCEPTED ? 'terminate' : 'authorize')| translate"
                   [actions]="actions" (dialogClose)="this.dialogRef.close()">

    <app-invitation [invitingOrganism]="certifierAccess._links.certifier"
                    [terminate]="certifierAccess.state === certifierAccessStates.ACCEPTED">
    </app-invitation>

    <ng-template #actions [ngSwitch]="certifierAccess.state">
        <div *ngSwitchCase="certifierAccessStates.WAITING">
            <button type="button" class="mr-4" mat-flat-button (click)="refuseOrTerminateAccess('refuse')"
                    [color]="'warn'">
                {{ 'common.invitation.button.refuse' | translate }}
            </button>
            <button type="button" mat-flat-button (click)="acceptAccess()" [color]="'primary'">
                {{ 'common.invitation.button.authorize' | translate }}
            </button>
        </div>
        <div *ngSwitchCase="certifierAccessStates.ACCEPTED">
            <button type="button" class="mr-4" mat-flat-button (click)="refuseOrTerminateAccess('terminate')"
                    [color]="'warn'">
                {{ 'common.invitation.button.terminate' | translate }}
            </button>
        </div>
        <div *ngSwitchCase="certifierAccessStates.TERMINATED">
            <button type="button" mat-flat-button (click)="acceptAccess()" [color]="'primary'">
                {{ 'common.invitation.button.reauthorize' | translate }}
            </button>
        </div>
        <div *ngSwitchCase="certifierAccessStates.REFUSED">
            <button type="button" mat-flat-button (click)="acceptAccess()" [color]="'primary'">
                {{ 'common.invitation.button.authorize' | translate }}
            </button>
        </div>
    </ng-template>

</app-dialog-layout>
