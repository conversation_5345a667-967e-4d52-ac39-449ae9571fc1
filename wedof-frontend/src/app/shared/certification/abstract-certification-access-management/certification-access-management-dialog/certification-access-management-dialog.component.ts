import {Component, Inject, OnInit} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialog, MatDialogRef} from '@angular/material/dialog';
import {CertifierAccess, CertifierAccessState} from '../../../api/models/certifier-access';
import {DeletionConfirmationComponent} from '../../../material/action-confirmation/deletion-confirmation.component';
import {filter, finalize} from 'rxjs/operators';

@Component({
    selector: 'app-certification-access-management-dialog',
    templateUrl: './certification-access-management-dialog.component.html',
    styleUrls: ['./certification-access-management-dialog.component.scss']
})
export class CertificationAccessManagementDialogComponent implements OnInit {
    certifierAccess: CertifierAccess;
    certifierAccessStates = CertifierAccessState;

    constructor(
        @Inject(MAT_DIALOG_DATA) private _dialogData: { certifierAccess: CertifierAccess },
        public _dialog: MatDialog,
        public dialogRef: MatDialogRef<CertificationAccessManagementDialogComponent>
    ) {
    }

    ngOnInit(): void {
        this.certifierAccess = this._dialogData.certifierAccess;
    }

    acceptAccess(): void {
        this.dialogRef.close('accept');
    }

    refuseOrTerminateAccess(action: string): void {
        const dialogRef = this._dialog.open(DeletionConfirmationComponent, {
            panelClass: 'full-page-scroll-50',
            height: 'auto',
            data: {
                messageKey: 'private.certification.partners.table.actions.' + action + '.messageKey',
                title: 'private.certification.partners.table.actions.' + action + '.action'
            }
        });
        dialogRef.componentInstance.actionValue$.pipe(
            filter(confirmation => confirmation),
            finalize(() => {
                dialogRef.componentInstance.close();
            })
        ).subscribe(() => {
            this.dialogRef.close(action);
        });
    }
}
