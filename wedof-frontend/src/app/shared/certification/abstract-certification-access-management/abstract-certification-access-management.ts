import {Directive} from '@angular/core';
import {MatDialog} from '@angular/material/dialog';
import {CertifierAccess} from '../../api/models/certifier-access';
import {EMPTY, Observable} from 'rxjs';
import {switchMap, tap} from 'rxjs/operators';
import {CertifierAccessService} from '../../api/services/certifier-access.service';
import {
    CertificationAccessManagementDialogComponent
} from './certification-access-management-dialog/certification-access-management-dialog.component';


@Directive()
// tslint:disable-next-line: directive-class-suffix
export abstract class AbstractCertificationAccessManagement {

    isLoadingCertifierAccess = false;

    protected constructor(
        protected _dialog: MatDialog,
        protected _certifierAccessService: CertifierAccessService
    ) {
    }

    openCertifierAccessDialog(certifierAccess: CertifierAccess): Observable<CertifierAccess> {
        const dialogRef = this._dialog.open(
            CertificationAccessManagementDialogComponent,
            {
                panelClass: 'full-page-scroll-50',
                height: 'auto',
                data: {
                    certifierAccess: certifierAccess
                }
            }
        );
        return dialogRef.afterClosed().pipe(
            switchMap(choice => {
                    if (!choice) {
                        return EMPTY;
                    }
                    this.isLoadingCertifierAccess = true;
                    let observable: Observable<CertifierAccess>;
                    switch (choice) {
                        case 'accept':
                            observable = this._certifierAccessService.accept(certifierAccess);
                            break;
                        case 'refuse':
                            observable = this._certifierAccessService.refuse(certifierAccess);
                            break;
                        case 'terminate':
                            observable = this._certifierAccessService.terminate(certifierAccess);
                            break;
                    }
                    return observable;
                }
            ),
            tap(() => {
                    this.isLoadingCertifierAccess = false;
                }
            )
        );
    }
}
