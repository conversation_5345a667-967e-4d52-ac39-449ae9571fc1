<app-dialog-layout
    [title]=" 'private.common.certification.audit.template.' + (existingCertificationPartnerTemplate ? 'update' : 'create')| translate"
    [showCancel]="false"
    [disabled]="loading"
    (dialogClose)="closeModal()">
    <form [formGroup]="formGroup" class="flex flex-col">
        <app-form-fields class="grid grid-cols-6 gap-2"
                         formGroupName="templateForm"
                         [entity]="existingCertificationPartnerTemplate"
                         [appFormFieldsData]="appFormFieldsData"
                         [formGroup]="formGroup"></app-form-fields>
        <div *ngIf="errorMessages?.length" class="flex items-center">
            <treo-message class="flex-auto" appearance="outline" [showIcon]="false" type="error">
                <ul>
                    <li *ngFor="let errorMessage of errorMessages">
                        {{ errorMessage }}
                    </li>
                </ul>
            </treo-message>
        </div>

        <treo-message type="info"
                      class="flex-auto mt-2"
                      *ngIf="!existingCertificationPartnerTemplate && loading"
                      [showIcon]="false"
                      appearance="outline">
            <p>{{ 'private.common.certification.audit.template.pendingCreation' | translate }}</p>
        </treo-message>

        <div class="flex flex-row py-2 mt-2 mb-2" [class]="existingCertificationPartnerTemplate ? 'justify-between' : 'justify-end'">
            <button *ngIf="existingCertificationPartnerTemplate"
                    mat-flat-button color="primary"
                    (click)="openDocumentIframe(existingCertificationPartnerTemplate)">
                {{'common.actions.updateDocument' | translate}}
            </button>
            <div class="flex flex-row">
                <button type="button" mat-button (click)="closeModal()">
                    {{ 'common.actions.cancel' | translate }}
                </button>
                <button type="submit" class="flex align-center ml-3" mat-flat-button color="primary"
                        (click)="existingCertificationPartnerTemplate ? update() : create()"
                        [disabled]="loading || formGroup.invalid || !formGroup.dirty">
                    <mat-progress-spinner class="mr-4" *ngIf="loading" [diameter]="24"
                                          mode="indeterminate"></mat-progress-spinner>
                    <ng-container
                        *ngIf="!loading">{{ (existingCertificationPartnerTemplate ? 'common.actions.update' : 'common.actions.create') | translate }}
                    </ng-container>
                </button>
            </div>
        </div>
    </form>
</app-dialog-layout>
