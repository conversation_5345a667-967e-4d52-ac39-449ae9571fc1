import {Component, Inject, OnInit} from '@angular/core';
import {<PERSON><PERSON><PERSON><PERSON>, FormGroup} from '@angular/forms';
import {MAT_DIALOG_DATA, MatDialog, MatDialogRef} from '@angular/material/dialog';
import {HttpErrorResponse} from '@angular/common/http';
import {AppFormFieldData} from '../../../material/app-form-field/app-form-field.component';
import {ApiError} from '../../../errors/errors.types';
import {
    CertificationPartnerAuditCriteria,
    CertificationPartnerAuditTemplate,
    CertificationPartnerAuditTemplateCreateBody
} from '../../../api/models/certification-partner-audit';
import {CertificationPartnerAuditTemplateService} from '../../../api/services/certification-partner-audit-template.service';
import {Certification} from '../../../api/models/certification';
import {TranslateService} from '@ngx-translate/core';
import {FileIframeComponent} from '../../../file/dialogs/file-iframe/file-iframe.component';
import {FileTypeService} from '../../../api/services/file-type.service';

@Component({
    selector: 'app-dialog-certification-audit-template',
    templateUrl: './dialog-certification-audit-template.component.html',
    styleUrls: ['./dialog-certification-audit-template.component.scss']
})
export class DialogCertificationAuditTemplateComponent<T> implements OnInit {

    existingCertificationPartnerTemplate: CertificationPartnerAuditTemplate;
    loading = false;
    formGroup: FormGroup;
    errorMessages: string[] = [];
    appFormFieldsData: AppFormFieldData[];

    constructor(public dialogRef: MatDialogRef<DialogCertificationAuditTemplateComponent<T>>,
                private _certificationPartnerAuditTemplateService: CertificationPartnerAuditTemplateService,
                private _translateService: TranslateService,
                private _formBuilder: FormBuilder,
                private _fileTypeService: FileTypeService<T>,
                private _dialog: MatDialog,
                @Inject(MAT_DIALOG_DATA) public dialogData: {
                    certification: Certification,
                    googleId: string,
                    certificationPartnerAuditTemplate: CertificationPartnerAuditTemplate,
                    criterias: CertificationPartnerAuditCriteria[]
                }
    ) {
    }

    ngOnInit(): void {
        this.existingCertificationPartnerTemplate = this.dialogData.certificationPartnerAuditTemplate;
        this.formGroup = this._formBuilder.group({
            templateForm: this._formBuilder.group({})
        });
        this.appFormFieldsData = [
            {
                controlName: 'name',
                label: 'private.common.certification.audit.template.form.name',
                type: 'text',
                maxLength: 255
            },
            {
                value: this.existingCertificationPartnerTemplate?.allowVisibilityPartner ?? false,
                controlName: 'allowVisibilityPartner',
                label: 'private.common.certification.audit.template.form.allowVisibilityPartner',
                type: 'radio',
                inline: true,
                choices: [
                    {key: this._translateService.instant('common.actions.yes'), value: true},
                    {key: this._translateService.instant('common.actions.no'), value: false}
                ],
            }
        ];
    }

    closeModal(): void {
        this.dialogRef.close();
        if (this.dialogData.googleId) {
            this._fileTypeService.deleteTemplate('Certification', 'certificationPartnerAuditFileTypes').subscribe();
        }
    }

    create(): void {
        this.loading = true;
        this.errorMessages = [];
        const certificationPartnerAuditTemplateFormValue = this.formGroup?.getRawValue().templateForm;
        const certificationPartnerAuditTemplateCreateBody: CertificationPartnerAuditTemplateCreateBody = {
            certifInfo: this.dialogData.certification.certifInfo,
            name: certificationPartnerAuditTemplateFormValue.name,
            allowVisibilityPartner: certificationPartnerAuditTemplateFormValue.allowVisibilityPartner,
            googleId: this.dialogData.googleId,
            criterias: this.dialogData.criterias,
        };
        this._certificationPartnerAuditTemplateService.create(certificationPartnerAuditTemplateCreateBody).subscribe(
            certificationPartnerAuditTemplateCreated => {
                this.loading = false;
                this.dialogRef.close({data: certificationPartnerAuditTemplateCreated});
            },
            (httpErrorResponse: HttpErrorResponse) => {
                this.loading = false;
                this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
            }
        );
    }

    update(): void {
        this.loading = true;
        this.errorMessages = [];
        const certificationPartnerAuditTemplateFormValue = this.formGroup?.getRawValue().templateForm;
        const certificationPartnerAuditTemplate: CertificationPartnerAuditTemplate = {
            ...this.existingCertificationPartnerTemplate,
            name: certificationPartnerAuditTemplateFormValue.name,
            allowVisibilityPartner: certificationPartnerAuditTemplateFormValue.allowVisibilityPartner
        };
        this._certificationPartnerAuditTemplateService.update(certificationPartnerAuditTemplate).subscribe(
            certificationPartnerAuditTemplateUpdated => {
                this.loading = false;
                this.dialogRef.close({data: certificationPartnerAuditTemplateUpdated});
            },
            (httpErrorResponse: HttpErrorResponse) => {
                this.loading = false;
                this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
            }
        );
    }

    openDocumentIframe(cpat: CertificationPartnerAuditTemplate): void {
        this._dialog.open(FileIframeComponent, {
            disableClose: true,
            width: '80%',
            data: {
                templateEditor: {
                    templateGid: cpat.auditTemplateFileType.googleId,
                    contextId: cpat.id,
                    layout: 'document',
                    type: 'document',
                    scope: 'audit'
                }
            }
        });
    }
}
