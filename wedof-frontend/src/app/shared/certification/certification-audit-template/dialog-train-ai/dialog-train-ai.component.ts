import {Component, Inject, OnInit, ViewChild, TemplateRef} from '@angular/core';
import {FormArray, FormBuilder, FormControl, FormGroup, Validators} from '@angular/forms';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material/dialog';
import {TranslateService} from '@ngx-translate/core';
import {MatSnackBar} from '@angular/material/snack-bar';
import {SnackBarComponent} from '../../../material/snack-bar/snack-bar.component';
import {displayTextSnackBar} from '../../../utils/displayTextSnackBar';
import {cloneDeep} from 'lodash-es';
import {CertificationPartnerAuditService} from '../../../api/services/certification-partner-audit.service';

interface TrainingQuestion {
    title: string;
    isConform: boolean | null;
    justification: string;
    isManual: boolean;
}

@Component({
    selector: 'app-dialog-train-ai',
    templateUrl: './dialog-train-ai.component.html',
    styleUrls: ['./dialog-train-ai.component.scss']
})
export class DialogTrainAIComponent implements OnInit {

    formGroup: FormGroup;
    loading = false;
    loadingTitles = false;
    errorMessages: string[] = [];
    trainingQuestions: TrainingQuestion[] = [];
    @ViewChild('actions') actions: TemplateRef<any>;

    constructor(
        private _formBuilder: FormBuilder,
        private _translateService: TranslateService,
        private _snackBar: MatSnackBar,
        private _certificationPartnerAuditService: CertificationPartnerAuditService,
        public dialogRef: MatDialogRef<DialogTrainAIComponent>,
        @Inject(MAT_DIALOG_DATA) public dialogData: {
            existingTrainingData?: TrainingQuestion[],
            certifInfo: string
        }
    ) {
    }

    ngOnInit(): void {
        if (this.dialogData && this.dialogData.existingTrainingData && this.dialogData.existingTrainingData.length > 0) {
            this.trainingQuestions = cloneDeep(this.dialogData.existingTrainingData).map(question => {
                if (question.isManual === undefined) {
                    question.isManual = false;
                }
                return question;
            });
            this.initForm();
        } else {
            this.loadTrainingTitles();
        }
    }

    loadTrainingTitles(): void {
        this.loadingTitles = true;
        this.trainingQuestions = [];

        this._certificationPartnerAuditService.getTrainingTitles(this.dialogData.certifInfo)
            .subscribe({
                next: (titles) => {
                    if (titles && titles.length > 0) {
                        this.trainingQuestions = titles.map(title => {
                            return {
                                title,
                                isConform: null,
                                justification: '',
                                isManual: false
                            };
                        });
                    }
                    this.initForm();
                    this.loadingTitles = false;
                },
                error: () => {
                    this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(this._translateService.instant('private.common.certification.audit.ai.loadingTitlesError'), 5000, 'red'));
                    this.initForm();
                    this.loadingTitles = false;
                }
            });
    }

    initForm(): void {
        this.formGroup = this._formBuilder.group({
            trainingForm: this._formBuilder.group({
                questions: this._formBuilder.array([]),
                newQuestionTitle: ['']
            })
        });
        const questionsArray = this.getQuestionsFormArray();
        this.trainingQuestions.forEach(question => {
            questionsArray.push(
                this._formBuilder.group({
                    title: [question.title],
                    isConform: [question.isConform, Validators.required],
                    justification: [question.justification, Validators.required],
                    isManual: [question.isManual]
                })
            );
        });
    }

    getQuestionsFormArray(): FormArray {
        return this.formGroup.get('trainingForm').get('questions') as FormArray;
    }

    getNewQuestionTitleControl(): FormControl {
        return this.formGroup.get('trainingForm').get('newQuestionTitle') as FormControl;
    }

    closeModal(): void {
        this.dialogRef.close();
    }

    removeQuestion(index: number): void {
        const questionsArray = this.getQuestionsFormArray();
        questionsArray.removeAt(index);
        this.trainingQuestions.splice(index, 1);
    }

    addQuestion(): void {
        const newQuestionTitleControl = this.getNewQuestionTitleControl();
        const title = newQuestionTitleControl.value;
        if (!title || title.trim() === '') {
            return;
        }
        const newQuestion: TrainingQuestion = {
            title: title.trim(),
            isConform: null,
            justification: '',
            isManual: true
        };
        this.trainingQuestions.push(newQuestion);
        const questionsArray = this.getQuestionsFormArray();
        questionsArray.push(
            this._formBuilder.group({
                title: [newQuestion.title],
                isConform: [newQuestion.isConform, Validators.required],
                justification: [newQuestion.justification, Validators.required],
                isManual: [newQuestion.isManual]
            })
        );
        newQuestionTitleControl.setValue('');
    }

    submit(): void {
        this.loading = true;
        const trainingData = this.getQuestionsFormArray().value;
        setTimeout(() => {
            this.loading = false;
            this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(this._translateService.instant('private.common.certification.audit.ai.trainSuccess'), 3000));
            this.dialogRef.close(trainingData ?? []);
        }, 1500); // Artificial delay
    }
}
