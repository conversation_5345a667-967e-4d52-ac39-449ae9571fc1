<app-dialog-layout
    [title]="'Entraîner l\'IA sur les intitulés de formations'"
    [actions]="actions"
    [disabled]="loading"
    [showCancel]="true"
    (dialogClose)="closeModal()">
    <p class="mb-4">{{ 'private.common.certification.audit.ai.trainingTitle.summary' | translate }}</p>
    <div *ngIf="loadingTitles"
         class="loading-container min-h-50 flex flex-col min-h-200 items-center justify-center py-8">
        <mat-spinner diameter="50"></mat-spinner>
        <p class="mt-4 text-center text-gray-600">{{ 'private.common.certification.audit.ai.loadingTitles' | translate }}</p>
    </div>
    <form *ngIf="formGroup && !loadingTitles"
          [formGroup]="formGroup"
          class="flex flex-col">
        <div formGroupName="trainingForm">
            <div formArrayName="questions"
                 class="max-h-120 pr-2 overflow-y-auto">
                <div *ngFor="let questionGroup of getQuestionsFormArray().controls; let i = index"
                     class="mb-4 p-3 border border-gray-200 rounded-md">
                    <div [formGroupName]="i">
                        <div class="font-medium mb-3 bg-gray-100 rounded flex justify-between items-center">
                            <span class="ml-3">{{ trainingQuestions[i].title }}</span>
                            <button mat-icon-button
                                    type="button"
                                    (click)="$event.stopPropagation(); removeQuestion(i)"
                                    [matTooltip]="'common.actions.delete' | translate">
                                <mat-icon svgIcon="delete" class="text-red"></mat-icon>
                            </button>
                        </div>
                        <div class="grid grid-cols-6 gap-4">
                            <div class="col-span-2" style="margin-top: -1px">
                                <mat-label class="font-medium">{{ 'private.common.certification.audit.ai.trainingTitle.question' | translate }} *</mat-label>
                                <mat-radio-group formControlName="isConform"
                                                 required
                                                 class="flex mt-2">
                                    <mat-radio-button [value]="true" class="mr-4">{{ 'common.actions.yes' | translate }}</mat-radio-button>
                                    <mat-radio-button [value]="false">{{ 'common.actions.no' | translate }}</mat-radio-button>
                                </mat-radio-group>
                            </div>
                            <mat-form-field class="col-span-4">
                                <mat-label class="self-center">{{ 'private.common.certification.audit.ai.trainingTitle.justification.title' | translate }}</mat-label>
                                <textarea matInput
                                          formControlName="justification"
                                          [placeholder]="'private.common.certification.audit.ai.trainingTitle.justification.placeholder' | translate"
                                          rows="3"
                                          required></textarea>
                            </mat-form-field>
                        </div>
                    </div>
                </div>
                <div class="mb-4 p-3 pb-2 border border-gray-200 rounded-md border-dashed">
                    <mat-form-field class="w-full">
                        <input matInput [formControl]="getNewQuestionTitleControl()"
                               placeholder="{{'private.common.certification.audit.ai.trainingTitle.titlePlaceholder' | translate}}"
                               (keyup.enter)="addQuestion()">
                        <button mat-icon-button matSuffix
                                type="button"
                                [disabled]="!getNewQuestionTitleControl().value"
                                (click)="addQuestion()"
                                [matTooltip]="'private.common.certification.audit.ai.trainingTitle.add' | translate">
                            <mat-icon svgIcon="add_circle" class="text-primary"></mat-icon>
                        </button>
                    </mat-form-field>
                </div>
            </div>
        </div>
        <div *ngIf="errorMessages?.length" class="flex items-center mt-4">
            <treo-message class="flex-auto" appearance="outline" [showIcon]="false" type="error">
                <ul>
                    <li *ngFor="let errorMessage of errorMessages">
                        {{ errorMessage }}
                    </li>
                </ul>
            </treo-message>
        </div>
    </form>
    <ng-template #actions>
        <button
            type="submit"
            mat-flat-button
            color="primary"
            [disabled]="formGroup?.invalid || !formGroup || loading || getQuestionsFormArray().length === 0"
            (click)="submit()">
            <span *ngIf="loading">{{ 'private.common.certification.audit.ai.trainingLoading' | translate }}</span>
            <span *ngIf="!loading">{{ 'private.common.certification.audit.ai.trainConfirm' | translate }}</span>
        </button>
    </ng-template>
</app-dialog-layout>
