import {
    Component,
    ElementRef,
    EventEmitter,
    Input,
    OnChanges,
    OnDestroy,
    OnInit,
    Output,
    SimpleChanges,
    ViewChild
} from '@angular/core';
import {Certification} from '../../api/models/certification';
import {BaseCardComponentDirective, RequiredCallSuper} from '../../utils/base-card/base-card.directive';
import {MatDialog} from '@angular/material/dialog';
import {DeletionConfirmationComponent} from '../../material/action-confirmation/deletion-confirmation.component';
import {filter, finalize, switchMap, takeUntil, tap} from 'rxjs/operators';
import {
    CertificationPartnerAuditTemplate,
    CriteriaScopeSelectChoice
} from '../../api/models/certification-partner-audit';
import {CertificationPartnerAuditTemplateService} from '../../api/services/certification-partner-audit-template.service';
import {DialogCertificationAuditTemplateComponent} from './dialog-certification-audit-template/dialog-certification-audit-template.component';
import {DialogCertificationAuditTemplateCriteriasComponent} from './dialog-certification-audit-template-criterias/dialog-certification-audit-template-criterias.component';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../errors/errors.types';
import {groupBy} from 'lodash-es';
import {TranslateService} from '@ngx-translate/core';
import {SnackBarComponent} from '../../material/snack-bar/snack-bar.component';
import {displayTextSnackBar} from '../../utils/displayTextSnackBar';
import {MatSnackBar} from '@angular/material/snack-bar';
import {CertificationPartnerAuditService} from '../../api/services/certification-partner-audit.service';
import {MatMenuTrigger} from '@angular/material/menu';
import {CertificationService} from '../../api/services/certification.service';
import {FileTypeService} from '../../api/services/file-type.service';
import {PickerComponent} from '../../material/picker/picker.component';
import {Observable, Subject} from 'rxjs';
import {Subscription} from '../../api/models/subscription';
import {Select} from '@ngxs/store';
import {UserState} from '../../api/state/user.state';
import {User} from '../../api/models/user';
import {FileService} from '../../api/services/file.service';
import {DialogCertificationAuditTemplateAutomaticComponent} from './dialog-certification-audit-template-automatic/dialog-certification-audit-template-automatic.component';

@Component({
    selector: 'app-certification-audit-template',
    templateUrl: './certification-audit-template.component.html',
    styleUrls: ['./certification-audit-template.component.scss']
})
export class CertificationAuditTemplateComponent<T> extends BaseCardComponentDirective implements OnInit, OnChanges, OnDestroy {
    static COMPONENT_ID = 'modelesAudit';

    isLoading = false;
    loading = false;
    allowAudits = false;
    errorMessages: string[] = [];
    subscriptionErrorMessages: string[] = [];
    displayedColumns: string[] = ['allowVisibility', 'name', 'criterias', 'auditCount', 'menu'];
    criteriasList: CriteriaScopeSelectChoice[];
    isAnnualSubscription: boolean;

    @Input() certification: Certification;
    @Input() subscription: Subscription;
    @Output() certificationUpdated: EventEmitter<Certification> = new EventEmitter<Certification>();
    @ViewChild('menuTrigger') menuTrigger: MatMenuTrigger;

    certificationPartnerAuditTemplates: CertificationPartnerAuditTemplate[] = [];

    user: User;
    @Select(UserState.user) user$: Observable<User>;
    private _unsubscribeAll = new Subject<void>();

    constructor(private _el: ElementRef,
                private _snackBar: MatSnackBar,
                private _fileService: FileService,
                private _translateService: TranslateService,
                private _fileTypeService: FileTypeService<T>,
                private _certificationService: CertificationService,
                private _certificationPartnerAuditService: CertificationPartnerAuditService,
                private _certificationPartnerAuditTemplateService: CertificationPartnerAuditTemplateService,
                private _dialog: MatDialog) {
        super(CertificationAuditTemplateComponent.COMPONENT_ID, _el);
    }

    ngOnDestroy(): RequiredCallSuper {
        return super.ngOnDestroy();
    }

    ngOnInit(): void {
        this.user$.pipe(takeUntil(this._unsubscribeAll)).subscribe((user) => {
            this.user = user;
            const certifierPeriodStartDate = new Date(this.subscription.certifierPeriodStartDate);
            const certifierPeriodEndDate = this.subscription.certifierPeriodEndDate;
            const daysBetweenSubscription = (new Date(certifierPeriodEndDate).getTime() - new Date(certifierPeriodStartDate).getTime()) / (1000 * 3600 * 24);
            this.isAnnualSubscription = daysBetweenSubscription > 31;
        });
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes.certification) {
            this.cardLoading = true;
            this.errorMessages = [];
            this.panelLoading();
            this._certificationPartnerAuditTemplateService.listCriterias(this.certification.certifInfo).subscribe((criteriasList) => {
                this.criteriasList = Object.entries(groupBy(criteriasList, 'category')).map(([category, scopes]) => {
                    const translateKey = 'private.common.certification.audit.criterias.list.helper.' + category;
                    const tooltip = this._translateService.instant(translateKey)  !== translateKey ? translateKey : null;
                    return ({
                        name: this._translateService.instant('private.common.certification.audit.criterias.list.category.' + category),
                        value: scopes.map((scope) => ({
                            key: this._translateService.instant('private.common.certification.audit.criterias.list.' + scope.key),
                            value: scope.key,
                            type: scope.type,
                            operationValues: scope.operationValues,
                            tooltip: tooltip
                        }))
                    });
                });
                this.listAuditTemplates();
            });
        }
    }

    listAuditTemplates(): void {
        this.allowAudits = this.certification.allowAudits;
        if (this.allowAudits) {
            this._certificationPartnerAuditTemplateService.list({certifInfo: this.certification.certifInfo}).subscribe(response => {
                this.certificationPartnerAuditTemplates = response.payload;
                this.cardLoading = false;
            });
        } else {
            this.cardLoading = false;
        }
    }

    createTemplate(): void {
        this.loading = true;
        const entityField = 'certificationPartnerAuditFileTypes';
        this._fileTypeService.listAllModels(entityField).subscribe((templateFileTypes) => {
            const cards = templateFileTypes.filter((template) =>
                template.additionalInformations.format === '.doc,.docx' && template.templateName !== 'New' && template.templateName !== 'TemplateInWedof');
            this._certificationPartnerAuditTemplateService.list().subscribe((response) => {
                this.loading = false;
                const auditTemplates = response.payload;
                if (auditTemplates?.length) {
                    auditTemplates.forEach((auditTemplate) => {
                        cards.push({
                            title: auditTemplate.name,
                            description: 'Modèle d\'audit utilisé sur - <b>' + auditTemplate._links.certification.externalId + '</b>',
                            tags: ['Mon modèle d\'audit', auditTemplate._links.certification.externalId],
                            templateName: auditTemplate.auditTemplateFileType.googleId,
                            additionalInformations: {
                                id: auditTemplate.id, // use it to determined that it needs to be duplicated from organism fileTypes and not from drive
                                colorTags: '#e0e7ff'
                            },
                            hasAccess: true,
                            display: true,
                        });
                    });
                }
                const dialogRefPicker = this._dialog.open(PickerComponent, {
                    disableClose: true, // necessary => avoid creating template that could be loss
                    panelClass: ['full-page-scroll-40'],
                    data: {
                        cards: cards,
                        feedback: this._translateService.instant('common.actions.file.pendingCreation'),
                        selectTemplate: (templateName: string) => {
                            const templateCard = cards.find((cardTemplate) => cardTemplate.templateName === templateName);
                            if (templateCard.additionalInformations?.id) {
                                return this._certificationPartnerAuditTemplateService.duplicate(
                                    templateCard.additionalInformations.id,
                                    {certifInfo: this.certification.certifInfo}
                                );
                            } else {
                                return this._fileTypeService.createTemplate('Certification', this.certification.id, entityField,
                                    templateCard.additionalInformations.format, templateCard.templateName);
                            }
                        }
                    }
                });
                dialogRefPicker.afterClosed().subscribe(resultPicker => {
                    if (resultPicker?.data?.id) {
                        const dialogRefCreate = this._dialog.open(DialogCertificationAuditTemplateComponent, {
                            disableClose: true,
                            panelClass: 'full-page-scroll-30',
                            data: {
                                certification: this.certification,
                                googleId: resultPicker.data.id,
                                criterias: resultPicker.data.criterias
                            }
                        });
                        dialogRefCreate.afterClosed().subscribe(resultCreate => {
                            if (resultCreate?.data) {
                                this.certificationPartnerAuditTemplates = [...this.certificationPartnerAuditTemplates, resultCreate.data];
                            }
                        });
                    }
                });
            });
        });
    }

    onCertificationPartnerAuditTemplateUpdated(certificationPartnerAuditTemplate: CertificationPartnerAuditTemplate): void {
        // Map required because of freaking datasource
        this.certificationPartnerAuditTemplates = this.certificationPartnerAuditTemplates.map(cpat =>
            cpat.id === certificationPartnerAuditTemplate.id ? certificationPartnerAuditTemplate : cpat
        );
    }

    deleteTemplate(certificationPartnerAuditTemplate: CertificationPartnerAuditTemplate): void {
        const dialogRef = this._dialog.open(DeletionConfirmationComponent, {
            panelClass: 'full-page-scroll-40',
            height: 'auto',
            data: {
                messageKey: 'private.common.certification.audit.template.confirmDeletion',
                data: certificationPartnerAuditTemplate
            }
        });
        dialogRef.componentInstance.actionValue$.pipe(
            filter((confirmation: boolean) => confirmation),
            tap(() => this.isLoading = true),
            switchMap(() => this._certificationPartnerAuditTemplateService.delete(certificationPartnerAuditTemplate)),
            finalize(() => {
                dialogRef.componentInstance.close();
                this.isLoading = false;
            })
        ).subscribe({
            next: () => {
                this.certificationPartnerAuditTemplates = this.certificationPartnerAuditTemplates.filter((cpat) => {
                    return cpat !== certificationPartnerAuditTemplate;
                });
            },
            error: (httpErrorResponse: HttpErrorResponse) => {
                this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
            }
        });
    }

    openCriteriasDialog(certificationPartnerAuditTemplate: CertificationPartnerAuditTemplate): void {
        const dialogRef = this._dialog.open(DialogCertificationAuditTemplateCriteriasComponent, {
            disableClose: true,
            panelClass: 'full-page-scroll-50',
            data: {
                certificationPartnerAuditTemplate: certificationPartnerAuditTemplate,
                criteriasList: this.criteriasList
            }
        });
        dialogRef.componentInstance.submitted$.subscribe(certificationPartnerAuditTemplateUpdated => {
            this.onCertificationPartnerAuditTemplateUpdated(certificationPartnerAuditTemplateUpdated);
        });
    }

    exportAudit(certificationPartnerAuditTemplate: CertificationPartnerAuditTemplate): void {
        this.errorMessages = [];
        this.isLoading = true;
        const url = '/api/certifications/' + this.certification.certifInfo + '/partners/audits/' + certificationPartnerAuditTemplate.id + '/report';
        this._fileService.download(url, 'rapport audits - partenariats actifs / suspendus', null).subscribe(
            (blob) => {
                if (blob.state === 'PENDING') {
                    this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(
                        this._translateService.instant('common.actions.export.exportStarted'), 0));
                } else if (blob.state === 'DONE') {
                    this.isLoading = false;
                    this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(
                        this._translateService.instant('common.actions.export.exportFinished')));
                }
            }
        );
    }

    generateAudit(certificationPartnerAuditTemplate: CertificationPartnerAuditTemplate): void {
        this.errorMessages = [];
        this.isLoading = true;
        const dialogRef = this._dialog.open(DialogCertificationAuditTemplateAutomaticComponent, {
            panelClass: 'full-page-scroll-40',
            height: 'auto',
            data: {
                certification: this.certification
            }
        });
        dialogRef.afterClosed().subscribe(result => {
            if (result?.data) {
                this.loading = true;
                this._certificationPartnerAuditService.createOnPartners(
                    this.certification.certifInfo, {
                        templateId: certificationPartnerAuditTemplate.id,
                        complete: result.data.complete,
                        suspend: result.data.suspend,
                        updateCompliance: result.data.updateCompliance,
                        partnerCompliance: result.data.partnerCompliance
                    })
                    .subscribe({
                        next: (auditResults) => {
                            this.loading = false;
                            this.isLoading = false;
                            this.onCertificationPartnerAuditTemplateUpdated(auditResults['auditTemplate']);
                            this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(
                                this._translateService.instant('private.common.certification.audit.template.auditPartners.feedback', {
                                    auditCount: auditResults['count'], certificationName: this.certification.name
                                }), 7000));
                        },
                        error: (httpErrorResponse: HttpErrorResponse) => {
                            this.loading = false;
                            this.isLoading = false;
                            this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
                        }
                    });
            } else {
                this.isLoading = false;
            }
        });
    }

    deactivateAudits(): void {
        const dialogRef = this._dialog.open(DeletionConfirmationComponent, {
            panelClass: 'full-page-scroll-30',
            height: 'auto',
            disableClose: true,
            data: {
                title: 'private.common.certification.audit.template.deactivate.menu',
                messageKey: this._translateService.instant('private.common.certification.audit.template.deactivate.subtitle', {certificationName: this.certification.name}),
                submissionText: 'common.actions.deactivate'
            }
        });
        dialogRef.componentInstance.actionValue$.pipe(
            filter(confirmation => confirmation),
            tap(() => {
                this.loading = true;
            }),
            switchMap((): Observable<Certification> => this._certificationService.deactivateAudits(this.certification.certifInfo)),
            finalize(() => {
                dialogRef.componentInstance.close();
                this.loading = false;
            })
        ).subscribe({
            next: (certificationUpdated) => {
                this.allowAudits = certificationUpdated.allowAudits;
                this.certificationUpdated.emit(certificationUpdated);
                this.cardLoading = true;
                this.listAuditTemplates();
            },
            error: (httpErrorResponse: HttpErrorResponse) => {
                this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
            }
        });
    }

    activateAudits(isAnnualSubscription: boolean): void {
        if (isAnnualSubscription) {
            window.location.href = 'mailto:<EMAIL>?' +
                'subject=Activation Audit &' +
                'body=Bonjour l\'équipe Wedof, %0D%0A  %0D%0A Je souhaiterais profiter de l\'abonnement sur la gestion des audits pour ma certification '
                + this.certification.externalId + '.' + '%0D%0A %0D%0A' + 'Bien à vous, %0D%0A ' + this.user.name;
        }
        this._certificationService.activateAudits(this.certification.certifInfo, isAnnualSubscription).subscribe({
            next: (certificationUpdated) => {
                this.allowAudits = certificationUpdated.allowAudits;
                if (!isAnnualSubscription) {
                    this.certificationUpdated.emit(certificationUpdated);
                }
            },
            error: (httpErrorResponse: HttpErrorResponse) => {
                this.subscriptionErrorMessages = (httpErrorResponse.error as ApiError).errorMessages;
            }
        });
    }
}
