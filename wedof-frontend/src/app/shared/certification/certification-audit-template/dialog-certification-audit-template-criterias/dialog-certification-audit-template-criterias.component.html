<app-dialog-layout [title]=" 'private.common.certification.audit.criterias.title' | translate"
                   (dialogClose)="closeModal()"
                   [showCancel]="false">
    <div class="flex flex-col mb-2">
        <div class="flex flex-col mb-4">
            <div class="flex items-center justify-between">
                <p class="text-secondary">{{ 'private.common.certification.audit.criterias.subtitle' | translate }}</p>
                <button type="button" mat-icon-button
                        (click)="createCriteria()">
                    <mat-icon svgIcon="add_circle"
                              [matTooltip]="'private.common.certification.audit.criterias.add' | translate"></mat-icon>
                </button>
            </div>
        </div>
        <treo-message class="mt-2 mb-2" appearance="outline"
                      [showIcon]="false" type="info">
            {{ 'private.common.certification.audit.criterias.form.severity.disclaimer' | translate }}
        </treo-message>
        <table *ngIf="certificationPartnerAuditTemplate.criterias?.length"
               [dataSource]="certificationPartnerAuditTemplate.criterias" class="table-fixed" mat-table>
            <ng-container matColumnDef="title">
                <td *matCellDef="let criteria" class="w-40" mat-cell>
                    {{ criteria.title }}
                    <p class="text-secondary text-sm">
                        id: {{ criteria.code }}
                    </p>
                </td>
            </ng-container>
            <ng-container matColumnDef="condition">
                <td *matCellDef="let criteria" class="w-100" mat-cell>
                    <p [innerHTML]="criteria | certificationPartnerAuditCriteriaRequirement"></p>
                </td>
            </ng-container>
            <ng-container matColumnDef="severity">
                <td *matCellDef="let criteria" class="w-10 text-right" mat-cell>
                    <mat-icon class="icon-small text-grey"
                              *ngIf="criteria.severity !== certificationPartnerAuditCriteriaSeverity.NONE"
                              [matTooltip]="'private.common.certification.audit.criterias.form.severity.tooltip.' + criteria.severity | translate ">
                        {{ criteria.severity === certificationPartnerAuditCriteriaSeverity.MAJEURE ? 'priority_high' : 'warning' }}
                    </mat-icon>
                </td>
            </ng-container>
            <ng-container matColumnDef="menu">
                <td *matCellDef="let criteria" class="w-10 text-right" mat-cell>
                    <app-certification-audit-criteria-menu *ngIf="!isLoading; else showLoading"
                                                           (updateCriteria)="updateCriteria(criteria)"
                                                           (deleteCriteria)="deleteCriteria(criteria)"></app-certification-audit-criteria-menu>
                    <ng-template #showLoading>
                        <mat-progress-spinner class="mr-4" [diameter]="24"
                                              mode="indeterminate"></mat-progress-spinner>
                    </ng-template>
                </td>
            </ng-container>
            <tr mat-row (click)="updateCriteria(row)" *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>
        <mat-error *ngIf="errorMessages.length" class="mb-4">
            <ul>
                <li *ngFor="let errorMessage of errorMessages">{{ errorMessage }}</li>
            </ul>
        </mat-error>
    </div>
    <div class="flex flex-row mt-2 pt-2 items-center justify-between">
        <button (click)="openTemplate()"
                mat-flat-button
                color="primary"
                type="button">
            {{ 'private.common.certification.audit.template.update' | translate }}
        </button>
        <div class="flex flex-row">
            <button type="button" mat-button (click)="closeModal()">
                {{ 'common.actions.cancel' | translate }}
            </button>
            <button type="button"
                    class="ml-2"
                    mat-flat-button
                    color="primary"
                    (click)="createCriteria()"
                    [disabled]="loading">
                {{ 'private.common.certification.audit.criterias.add'| translate }}
            </button>
        </div>
    </div>

</app-dialog-layout>
