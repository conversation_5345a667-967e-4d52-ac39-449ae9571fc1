import {Component, Inject, OnInit} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialog, MatDialogRef} from '@angular/material/dialog';
import {CertificationAuditCriteriaFormComponent} from '../certification-audit-criteria-form/certification-audit-criteria-form.component';
import {DeletionConfirmationComponent} from '../../../material/action-confirmation/deletion-confirmation.component';
import {filter, finalize, switchMap, tap} from 'rxjs/operators';
import {TranslateService} from '@ngx-translate/core';
import {CertificationPartnerAuditCriteria, CertificationPartnerAuditCriteriaSeverity, CertificationPartnerAuditTemplate, CriteriaScopeSelectChoice} from '../../../api/models/certification-partner-audit';
import {CertificationPartnerAuditTemplateService} from '../../../api/services/certification-partner-audit-template.service';
import {Subject} from 'rxjs';
import {FileIframeComponent} from '../../../file/dialogs/file-iframe/file-iframe.component';

@Component({
    selector: 'app-certification-audit-template-criterias',
    templateUrl: './dialog-certification-audit-template-criterias.component.html',
    styleUrls: ['./dialog-certification-audit-template-criterias.component.scss']
})
export class DialogCertificationAuditTemplateCriteriasComponent implements OnInit {

    isLoading = false;
    loading = false;
    errorMessages: string[] = [];
    certificationPartnerAuditTemplate: CertificationPartnerAuditTemplate;
    submitted$ = new Subject<CertificationPartnerAuditTemplate>();
    displayedColumns: string[] = ['title', 'condition', 'severity', 'menu'];
    certificationPartnerAuditCriteriaSeverity = CertificationPartnerAuditCriteriaSeverity;

    constructor(public dialogRef: MatDialogRef<DialogCertificationAuditTemplateCriteriasComponent>,
                private _certificationPartnerAuditTemplateService: CertificationPartnerAuditTemplateService,
                private _dialog: MatDialog,
                private _translateService: TranslateService,
                @Inject(MAT_DIALOG_DATA) public dialogData: {
                    certificationPartnerAuditTemplate: CertificationPartnerAuditTemplate,
                    criteriasList: CriteriaScopeSelectChoice[];
                }) {
    }

    ngOnInit(): void {
        this.errorMessages = [];
        this.certificationPartnerAuditTemplate = this.dialogData.certificationPartnerAuditTemplate;
    }

    createCriteria(): void {
        const dialogRef = this._dialog.open(CertificationAuditCriteriaFormComponent, {
            disableClose: true,
            panelClass: 'full-page-scroll-40',
            data: {
                certificationPartnerAuditTemplate: this.certificationPartnerAuditTemplate,
                criteriasList: this.dialogData.criteriasList
            }
        });
        dialogRef.afterClosed().subscribe(result => {
            if (result?.data) {
                this.certificationPartnerAuditTemplate = result.data;
                this.updateTemplate(this.certificationPartnerAuditTemplate);
            }
        });
    }

    onCertificationPartnerAuditTemplateUpdated(certificationPartnerAuditTemplate: CertificationPartnerAuditTemplate): void {
        this.updateTemplate(certificationPartnerAuditTemplate);
    }

    deleteCriteria(criteriaToDelete: CertificationPartnerAuditCriteria): void {
        const dialogRef = this._dialog.open(DeletionConfirmationComponent, {
            panelClass: 'full-page-scroll-40',
            height: 'auto',
            data: {
                messageKey: 'private.common.certification.audit.criterias.confirmDeletion',
                data: criteriaToDelete
            }
        });
        const updatedCriterias = this.certificationPartnerAuditTemplate.criterias.filter(criteria =>
            criteria !== criteriaToDelete
        );
        const certificationPartnerAuditTemplate: CertificationPartnerAuditTemplate = {
            ...this.certificationPartnerAuditTemplate, ...{
                criterias: updatedCriterias
            }
        };
        dialogRef.componentInstance.actionValue$.pipe(
            filter((confirmation: boolean) => confirmation),
            tap(() => this.isLoading = true),
            switchMap(() => this._certificationPartnerAuditTemplateService.update(certificationPartnerAuditTemplate)),
            finalize(() => {
                dialogRef.componentInstance.close();
                this.isLoading = false;
            })
        ).subscribe(certificationPartnerAuditTemplateUpdated => {
            this.updateTemplate(certificationPartnerAuditTemplateUpdated);
        });
    }

    private updateTemplate(certificationPartnerAuditTemplate: CertificationPartnerAuditTemplate): void {
        this.certificationPartnerAuditTemplate = certificationPartnerAuditTemplate;
        this.submitted$.next(certificationPartnerAuditTemplate);
    }

    closeModal(): void {
        this.dialogRef.close();
    }

    openTemplate(): void {
        this._dialog.open(FileIframeComponent, {
            disableClose: true,
            width: '80%',
            data: {
                templateEditor: {
                    templateGid: this.certificationPartnerAuditTemplate.auditTemplateFileType.googleId,
                    contextId: this.certificationPartnerAuditTemplate.id,
                    layout: 'document',
                    type: 'document',
                    scope: 'audit'
                }
            }
        });
    }

    updateCriteria(criteria: CertificationPartnerAuditCriteria): void {
        const dialogRef = this._dialog.open(CertificationAuditCriteriaFormComponent, {
            disableClose: true,
            panelClass: 'full-page-scroll-40',
            data: {
                certificationPartnerAuditTemplate: this.certificationPartnerAuditTemplate,
                criteria: criteria,
                criteriasList: this.dialogData.criteriasList
            }
        });
        dialogRef.afterClosed().subscribe(result => {
            if (result?.data) {
                this.onCertificationPartnerAuditTemplateUpdated(result?.data);
            }
        });
    }
}
