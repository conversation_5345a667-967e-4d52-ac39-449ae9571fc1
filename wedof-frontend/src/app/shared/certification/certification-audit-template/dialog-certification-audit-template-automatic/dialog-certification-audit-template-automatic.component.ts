import {Component, Inject, OnInit} from '@angular/core';
import {CertificationPartnerAuditResults, } from '../../../api/models/certification-partner-audit';
import {FormBuilder, FormGroup} from '@angular/forms';
import {AppFormFieldData} from '../../../material/app-form-field/app-form-field.component';
import {MAT_DIALOG_DATA, MatDialog, MatDialogRef} from '@angular/material/dialog';
import {TranslateService} from '@ngx-translate/core';
import {Certification} from '../../../api/models/certification';

@Component({
    selector: 'app-dialog-certification-audit-template-automatic',
    templateUrl: './dialog-certification-audit-template-automatic.component.html',
    styleUrls: ['./dialog-certification-audit-template-automatic.component.scss']
})
export class DialogCertificationAuditTemplateAutomaticComponent implements OnInit {

    loading = false;
    allowSuspend = true;
    formGroup: FormGroup;
    errorMessages: string[] = [];
    appFormFieldsData: AppFormFieldData[];

    constructor(public dialogRef: MatDialogRef<DialogCertificationAuditTemplateAutomaticComponent>,
                private _translateService: TranslateService,
                private _formBuilder: FormBuilder,
                private _dialog: MatDialog,
                @Inject(MAT_DIALOG_DATA) public dialogData: {
                    certification: Certification
                }
    ) {
    }

    ngOnInit(): void {
        this.formGroup = this._formBuilder.group({
            auditAutomatic: this._formBuilder.group({})
        });
        this.allowSuspend = this.dialogData.certification.enabled;
        const defaultUpdateCompliance = true;
        const defaultSuspend = false;
        this.appFormFieldsData = [
            {
                controlName: 'partnerCompliance',
                label: 'private.common.certification.audit.template.automaticAudit.partnerCompliance',
                type: 'select',
                value: ['all'],
                choices: [
                    {
                        key: this._translateService.instant('common.actions.search.allConformities'),
                        value: 'all'
                    },
                    {
                        key: this._translateService.instant('private.certification.audit.result.' + CertificationPartnerAuditResults.NONE),
                        value: CertificationPartnerAuditResults.NONE
                    },
                    {
                        key: this._translateService.instant('private.certification.audit.result.' + CertificationPartnerAuditResults.NON_COMPLIANT),
                        value: CertificationPartnerAuditResults.NON_COMPLIANT
                    },
                    {
                        key: this._translateService.instant('private.certification.audit.result.' + CertificationPartnerAuditResults.PARTIALLY_COMPLIANT),
                        value: CertificationPartnerAuditResults.PARTIALLY_COMPLIANT
                    },
                    {
                        key: this._translateService.instant('private.certification.audit.result.' + CertificationPartnerAuditResults.COMPLIANT),
                        value: CertificationPartnerAuditResults.COMPLIANT
                    },
                ],
                searchMultiple: true,
                removable: true,
                change: (controlName, newValue, formData) => {
                    const appFormFieldPartnerCompliance = formData.find(field => field.controlName === 'partnerCompliance');
                    if (newValue) {
                        if (newValue.includes('all') || newValue.length === 4) {
                            appFormFieldPartnerCompliance.choices.forEach((choice) => {
                                if (choice.value !== 'all') {
                                    choice.disabled = true;
                                }
                            });
                            appFormFieldPartnerCompliance.value = ['all'];
                        } else {
                            appFormFieldPartnerCompliance.choices.forEach((choice) => choice.disabled = false);
                            appFormFieldPartnerCompliance.value = newValue;
                        }
                    }
                    return [appFormFieldPartnerCompliance];
                }
            },
            {
                controlName: 'complete',
                label: 'private.common.certification.audit.template.automaticAudit.complete.title',
                help: 'private.common.certification.audit.template.automaticAudit.complete.help',
                type: 'radio',
                inline: true,
                value: true,
                choices: [
                    {key: this._translateService.instant('common.actions.yes'), value: true},
                    {key: this._translateService.instant('common.actions.no'), value: false}
                ],
                change: (controlName, newValue, formData) => {
                    const appFormFieldUpdateCompliance = formData.find(field => field.controlName === 'updateCompliance');
                    const appFormFieldSuspend = formData.find(field => field.controlName === 'suspend');
                    if (newValue === true) {
                        appFormFieldUpdateCompliance.removed = false;
                        appFormFieldUpdateCompliance.value = defaultUpdateCompliance;
                        appFormFieldSuspend.removed = false;
                        appFormFieldSuspend.value = defaultSuspend && this.allowSuspend;
                    } else {
                        appFormFieldUpdateCompliance.removed = true;
                        appFormFieldUpdateCompliance.value = false;
                        appFormFieldSuspend.removed = true;
                        appFormFieldSuspend.value = false;
                    }
                    return [appFormFieldUpdateCompliance, appFormFieldSuspend];
                }
            },
            {
                controlName: 'updateCompliance',
                label: 'private.common.certification.audit.template.automaticAudit.updateCompliance.title',
                help: 'private.common.certification.audit.template.automaticAudit.updateCompliance.help',
                type: 'radio',
                inline: true,
                value: defaultUpdateCompliance,
                choices: [
                    {key: this._translateService.instant('common.actions.yes'), value: true},
                    {key: this._translateService.instant('common.actions.no'), value: false}
                ],
            },
            {
                controlName: 'suspend',
                label: 'private.common.certification.audit.template.automaticAudit.suspend.title',
                help: 'private.common.certification.audit.template.automaticAudit.suspend.help',
                type: 'radio',
                inline: true,
                value: defaultSuspend && this.allowSuspend,
                disabled: !this.allowSuspend,
                choices: [
                    {key: this._translateService.instant('common.actions.yes'), value: true},
                    {key: this._translateService.instant('common.actions.no'), value: false}
                ]
            }
        ];
    }

    closeModal(): void {
        this.dialogRef.close();
    }

    create(): void {
        this.dialogRef.close({data: this.formGroup?.getRawValue().auditAutomatic});
    }
}
