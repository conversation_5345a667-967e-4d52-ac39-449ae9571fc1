<app-dialog-layout
    [title]="'private.common.certification.audit.template.auditPartners.title' | translate "
    [disabled]="loading"
    [actions]="actions"
    (dialogClose)="closeModal()">

    <p class="mb-2"> {{'private.common.certification.audit.template.auditPartners.messageKey' | translate }}
        (<a class="mt-2" href="/aide/guides/certificateurs/audit" target="_blank">Voir la documentation</a>)
    </p>

    <form [formGroup]="formGroup" class="flex flex-col">

        <treo-message *ngIf="!allowSuspend" type="info" [showIcon]="false" appearance="outline" class="mb-4">
            {{'private.common.certification.audit.template.automaticAudit.suspendedNotAllowed' | translate }}
        </treo-message>

        <app-form-fields class="grid grid-cols-6 gap-2"
                         formGroupName="auditAutomatic"
                         [appFormFieldsData]="appFormFieldsData"
                         [formGroup]="formGroup"></app-form-fields>

        <div *ngIf="errorMessages?.length" class="flex items-center">
            <treo-message class="flex-auto" appearance="outline" [showIcon]="false" type="error">
                <ul>
                    <li *ngFor="let errorMessage of errorMessages">
                        {{ errorMessage }}
                    </li>
                </ul>
            </treo-message>
        </div>

        <ng-template #actions>
            <button type="submit" mat-flat-button color="primary" (click)="create()"
                    [disabled]="loading || !formGroup?.valid">
                <mat-progress-spinner *ngIf="loading; else submitLabel" [diameter]="24" mode="indeterminate">
                </mat-progress-spinner>
                <ng-template #submitLabel>
                    {{ 'private.common.certification.audit.template.auditPartners.create' | translate }}
                </ng-template>
            </button>
        </ng-template>

    </form>
</app-dialog-layout>
