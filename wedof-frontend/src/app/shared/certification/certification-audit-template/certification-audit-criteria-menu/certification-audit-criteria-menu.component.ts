import {Component, EventEmitter, Output} from '@angular/core';
import {MatDialog} from '@angular/material/dialog';
import {CertificationPartnerAuditCriteria} from '../../../api/models/certification-partner-audit';

@Component({
    selector: 'app-certification-audit-criteria-menu',
    templateUrl: './certification-audit-criteria-menu.component.html',
    styleUrls: ['./certification-audit-criteria-menu.component.scss']
})
export class CertificationAuditCriteriaMenuComponent {

    @Output() deleteCriteria = new EventEmitter<void>();
    @Output() updateCriteria: EventEmitter<CertificationPartnerAuditCriteria> = new EventEmitter<CertificationPartnerAuditCriteria>();

    constructor(private _dialog: MatDialog) {
    }

    delete(): void {
        this.deleteCriteria.emit();
    }

    update(): void {
        this.updateCriteria.emit();
    }
}
