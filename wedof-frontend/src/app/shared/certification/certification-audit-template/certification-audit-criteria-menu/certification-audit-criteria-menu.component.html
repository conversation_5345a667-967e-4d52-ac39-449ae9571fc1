<button mat-icon-button
        (click)="$event.stopPropagation()"
        [matMenuTriggerFor]="actionsMenu"
        title="Actions">
    <mat-icon svgIcon="more_vert"></mat-icon>
</button>
<mat-menu #actionsMenu="matMenu">
    <ng-template matMenuContent>
        <button mat-menu-item (click)="update()">
            <mat-icon color="primary" svgIcon="edit"></mat-icon>
            <span>{{ 'common.actions.update' | translate }}</span>
        </button>
        <button mat-menu-item (click)="delete()">
            <mat-icon color="warn" svgIcon="delete"></mat-icon>
            <span>{{'common.actions.delete' | translate}}</span>
        </button>
    </ng-template>
</mat-menu>
