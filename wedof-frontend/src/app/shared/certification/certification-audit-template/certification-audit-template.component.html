<mat-card class="flex-col mt-3 p-5 pb-0 pt-3 border shadow-none sm" [ngClass]="{'card-loading':cardLoading}">
    <div class="flex items-center mb-2">
        <mat-icon class="mr-3 card-loading-show icon-size-32" svgIcon="fact_check" color="primary">
        </mat-icon>
        <div class="flex items-center">
            <span
                class="text-xl font-semibold card-loading-show"> {{ 'private.common.certification.audit.template.title' | translate }}</span>
        </div>
        <div class="ml-auto -mr-4 card-loading-hidden" *ngIf="allowAudits && !certification.auditsPendingCancellation">
            <button
                type="button" mat-icon-button
                (click)="menuTrigger.openMenu()"
                title="Actions">
                <mat-icon svgIcon="more_vert"></mat-icon>
            </button>
            <button
                type="button" #menuTrigger="matMenuTrigger"
                [matMenuTriggerFor]="actionsMenu">
            </button>
            <mat-menu #actionsMenu="matMenu" class="large-menu">
                <ng-template matMenuContent>
                    <button
                        type="button" mat-menu-item (click)="deactivateAudits()">
                        <mat-icon color="warn" svgIcon="delete"></mat-icon>
                        <span>{{ 'private.common.certification.audit.template.deactivate.menu' | translate}}</span>
                    </button>
                </ng-template>
            </mat-menu>
        </div>
    </div>
    <p *ngIf="certification.auditTrialEndDate" class="mb-2">{{'common.trialEnd' | translate}} {{certification.auditTrialEndDate | date: 'dd/MM/yy' }}</p>

    <ng-container *ngIf="!allowAudits" class="card-loading-show">
        <app-subscription-small-card [type]="'allowAudits'"
                                     [showFullCard]="false"
                                     (privateUpdate)="activateAudits(isAnnualSubscription)"
                                     [errorMessages]="subscriptionErrorMessages"
                                     [submissionText]="isAnnualSubscription ? 'private.common.certification.audit.activateContact' : 'common.actions.activate'">
        </app-subscription-small-card>
    </ng-container>

    <ng-container *ngIf="certification.auditsPendingCancellation" class="card-loading-show">
        <treo-message type="info" class="mt-2" appearance="outline"
                      [showIcon]="false">
            {{'private.common.certification.audit.pendingCancellation.title' | translate }}
        </treo-message>
        <button class="w-2/3 bg-gray-200 mt-4 mb-4 flex ml-auto mr-auto" mat-flat-button [disabled]="loading"
                (click)="activateAudits(false)">
            {{'private.common.certification.audit.pendingCancellation.reactivateButton' | translate }}
        </button>
    </ng-container>

    <ng-container *ngIf="allowAudits">
        <div class="flex flex-col mb-2 card-loading-show">
            <div class="flex flex-col mb-4">
                <div class="flex items-center justify-between">
                    <div class="flex flex-col">
                        <p class="font-medium"> {{ 'private.common.certification.audit.template.titleTemplates' | translate }}</p>
                        <p class="text-secondary">{{ 'private.common.certification.audit.template.subtitleTemplates' | translate }}</p>
                    </div>
                    <button type="button" mat-icon-button
                            [disabled]="loading"
                            (click)="createTemplate()">
                        <mat-icon svgIcon="add_circle"
                                  [matTooltip]="'private.common.certification.audit.template.create' | translate"></mat-icon>
                    </button>

                </div>
            </div>
            <ng-container *ngIf="!cardLoading">
                <table *ngIf="certificationPartnerAuditTemplates?.length;"
                       [dataSource]="certificationPartnerAuditTemplates"
                       mat-table>
                    <ng-container matColumnDef="allowVisibility">
                        <th mat-header-cell class="w-1/12" *matHeaderCellDef></th>
                        <td *matCellDef="let certificationPartnerAuditTemplate" mat-cell>
                            <mat-icon [matTooltipPosition]="'above'"
                                      class="text-green align-middle"
                                      [matTooltip]="'private.common.certification.audit.template.allowVisibilityPartner' | translate "
                                      *ngIf="certificationPartnerAuditTemplate.allowVisibilityPartner"
                                      svgIcon="person"></mat-icon>
                        </td>
                    </ng-container>
                    <ng-container matColumnDef="name">
                        <th mat-header-cell
                            *matHeaderCellDef>{{ 'private.common.certification.audit.template.table.name' | translate }}</th>
                        <td *matCellDef="let certificationPartnerAuditTemplate" class="w-40" mat-cell>
                            {{ certificationPartnerAuditTemplate.name }}
                        </td>
                    </ng-container>
                    <ng-container matColumnDef="criterias">
                        <th mat-header-cell
                            *matHeaderCellDef>{{ 'private.common.certification.audit.template.table.criterias' | translate }}</th>
                        <td *matCellDef="let certificationPartnerAuditTemplate" class="w-10 text-center underline"
                            mat-cell>
                            {{ certificationPartnerAuditTemplate.criterias?.length ? certificationPartnerAuditTemplate.criterias?.length : 0 }}
                        </td>
                    </ng-container>
                    <ng-container matColumnDef="auditCount">
                        <th mat-header-cell
                            *matHeaderCellDef>{{ 'private.common.certification.audit.template.table.audits' | translate }}</th>
                        <td *matCellDef="let certificationPartnerAuditTemplate" class="w-10" mat-cell>
                            <div class="flex justify-center">
                                <p [matTooltip]="'private.common.certification.audit.template.auditCount' | translate"> {{ certificationPartnerAuditTemplate.auditCount.all }}</p>
                                <mat-icon class="ml-1 icon-small"
                                    *ngIf=" certificationPartnerAuditTemplate.auditCount.pendingComputationOrComputing > 0"
                                    matPrefix svgIcon="published_with_changes"
                                    [matTooltip]="'private.certification.audit.tooltipInProgress' | translate : {pendingComputationOrComputing : certificationPartnerAuditTemplate.auditCount.pendingComputationOrComputing } ">
                                </mat-icon>
                            </div>
                        </td>
                    </ng-container>
                    <ng-container matColumnDef="menu">
                        <th mat-header-cell *matHeaderCellDef></th>
                        <td *matCellDef="let certificationPartnerAuditTemplate" class="w-10 text-right" mat-cell>
                            <app-certification-audit-template-menu *ngIf="!isLoading; else showLoading"
                                                                   [certificationPartnerAuditTemplate]="certificationPartnerAuditTemplate"
                                                                   (manageCriterias)="openCriteriasDialog(certificationPartnerAuditTemplate)"
                                                                   (deleteTemplate)="deleteTemplate(certificationPartnerAuditTemplate)"
                                                                   (generateAuditOnPartners)="generateAudit(certificationPartnerAuditTemplate)"
                                                                   (exportAuditFile)="exportAudit(certificationPartnerAuditTemplate)"
                                                                   (certificationPartnerAuditTemplateUpdated)="onCertificationPartnerAuditTemplateUpdated($event)"></app-certification-audit-template-menu>
                            <ng-template #showLoading>
                                <mat-progress-spinner class="mr-4" [diameter]="24"
                                                      mode="indeterminate"></mat-progress-spinner>
                            </ng-template>
                        </td>
                    </ng-container>
                    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                    <tr mat-row (click)="!isLoading && openCriteriasDialog(row)"
                        *matRowDef="let row; columns: displayedColumns;"></tr>
                </table>
                <mat-error *ngIf="errorMessages.length" class="mb-4">
                    <ul>
                        <li *ngFor="let errorMessage of errorMessages">{{ errorMessage }}</li>
                    </ul>
                </mat-error>
            </ng-container>
        </div>
        <div
            class="flex items-center mt-2 justify-end border-t pl-5 pr-10 -mx-5 py-3 light:bg-cool-gray-50 dark:bg-cool-gray-700 card-loading-hidden">
            <button type="button"
                    class="ml-2 max-w-1/2"
                    mat-flat-button
                    color="primary"
                    (click)="createTemplate()"
                    [disabled]="loading">
                {{ 'private.common.certification.audit.template.create'| translate }}
            </button>
        </div>
    </ng-container>
</mat-card>
