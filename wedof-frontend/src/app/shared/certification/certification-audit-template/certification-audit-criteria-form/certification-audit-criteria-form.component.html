<app-dialog-layout
    [title]=" 'private.common.certification.audit.criterias.' + (existingCriteria ? 'edit' : 'add')| translate"
    [showCancel]="false"
    (dialogClose)="closeModal()">
    <form [formGroup]="formGroup" class="flex flex-col">
        <app-form-fields class="grid grid-cols-6 gap-2"
                         formGroupName="criteriaForm"
                         [entity]="existingCriteria"
                         [appFormFieldsData]="appFormFieldsData"
                         [formGroup]="formGroup">
        </app-form-fields>
        <div *ngIf="errorMessages?.length" class="flex items-center">
            <treo-message class="flex-auto" appearance="outline" [showIcon]="false" type="error">
                <ul>
                    <li *ngFor="let errorMessage of errorMessages">
                        {{ errorMessage }}
                    </li>
                </ul>
            </treo-message>
        </div>
        <ng-container *ngIf="isAI()">
            <treo-message [type]="'info'" class="mt-2 flex-auto pb-6"
                          [showIcon]="false"
                          appearance="outline">
                <p class="py-2 text-justify">{{ 'private.common.certification.audit.criterias.list.helper.aiEvaluated' | translate }}</p>
            </treo-message>
            <app-precision-gauge class="mt-3 w-full"
                                 [percentage]="getAIPrecisionPercentage()"
                                 [yellowThreshold]="70"
                                 [redThreshold]="40"
                                 [greenText]="'private.common.certification.audit.ai.highPrecision' | translate"
                                 [yellowText]="'private.common.certification.audit.ai.midPrecision' | translate"
                                 [redText]="'private.common.certification.audit.ai.lowPrecision' | translate">
            </app-precision-gauge>
        </ng-container>
        <div class="flex flex-row items-center pt-2 mt-2" [class]="isAI() ? 'justify-between' : 'justify-end'">
            <div *ngIf="isAI()"
                 class="flex items-center">
                <button mat-flat-button color="primary"
                        type="button"
                        (click)="this.trainAI()">
                    {{ 'private.common.certification.audit.ai.train' | translate }}
                </button>
            </div>
            <div class="flex flex-row">
                <button type="button" mat-button (click)="closeModal()">
                    {{ 'common.actions.cancel' | translate }}
                </button>
                <button type="submit" class="flex align-center" mat-flat-button color="primary"
                        (click)="createOrUpdate()"
                        [disabled]="loading || formGroup.invalid || !formGroup.dirty">
                    <mat-progress-spinner class="mr-4" *ngIf="loading" [diameter]="24"
                                          mode="indeterminate"></mat-progress-spinner>
                    <ng-container *ngIf="!loading">
                        {{ 'common.actions.' + (existingCriteria ? 'update' : 'create') | translate }}
                    </ng-container>
                </button>
            </div>
        </div>
    </form>
</app-dialog-layout>
