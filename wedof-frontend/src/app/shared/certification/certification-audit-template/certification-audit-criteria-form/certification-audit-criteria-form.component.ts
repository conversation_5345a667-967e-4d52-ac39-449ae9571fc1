import {Component, Inject, OnInit} from '@angular/core';
import {FormBuilder, FormGroup} from '@angular/forms';
import {MAT_DIALOG_DATA, MatDialog, MatDialogRef} from '@angular/material/dialog';
import {DialogTrainAIComponent} from '../dialog-train-ai/dialog-train-ai.component';
import {TranslateService} from '@ngx-translate/core';
import {HttpErrorResponse} from '@angular/common/http';
import {AppFormFieldData, Choices} from '../../../material/app-form-field/app-form-field.component';
import {ApiError} from '../../../errors/errors.types';
import {
    CertificationPartnerAuditCriteria,
    CertificationPartnerAuditCriteriaOperation,
    CertificationPartnerAuditCriteriaSeverity,
    CertificationPartnerAuditTemplate,
    CriteriaScopeSelectChoice,
    CriteriaScopeValue
} from '../../../api/models/certification-partner-audit';
import {camelCase} from 'lodash-es';
import {CertificationPartnerAuditTemplateService} from '../../../api/services/certification-partner-audit-template.service';


type OperationType = 'string' | 'number' | 'boolean' | 'select' | 'selectArray' | 'custom';

@Component({
    selector: 'app-certification-audit-criteria-form',
    templateUrl: './certification-audit-criteria-form.component.html',
    styleUrls: ['./certification-audit-criteria-form.component.scss']
})
export class CertificationAuditCriteriaFormComponent implements OnInit {

    certificationPartnerAuditTemplate: CertificationPartnerAuditTemplate;
    existingCriteria: CertificationPartnerAuditCriteria;
    loading = false;
    formGroup: FormGroup;
    errorMessages: string[] = [];
    appFormFieldsData: AppFormFieldData[];
    currentOperationType: OperationType;
    currentCustomParameter?: any;
    scopeChoices: CriteriaScopeValue[] = [];
    operationsByType: Record<OperationType, CertificationPartnerAuditCriteriaOperation[]> = {
        string: [
            CertificationPartnerAuditCriteriaOperation.CONTAINS,
            CertificationPartnerAuditCriteriaOperation.NOT_CONTAINS,
            CertificationPartnerAuditCriteriaOperation.CONTAINS_ALL,
            CertificationPartnerAuditCriteriaOperation.NOT_CONTAINS_ANY,
            CertificationPartnerAuditCriteriaOperation.EQUALS_STRING,
            CertificationPartnerAuditCriteriaOperation.NOT_EQUALS_STRING,
            CertificationPartnerAuditCriteriaOperation.MATCHES
        ],
        number: [
            CertificationPartnerAuditCriteriaOperation.LESS_THAN,
            CertificationPartnerAuditCriteriaOperation.LESS_THAN_EQUAL,
            CertificationPartnerAuditCriteriaOperation.GREATER_THAN,
            CertificationPartnerAuditCriteriaOperation.GREATER_THAN_EQUAL,
            CertificationPartnerAuditCriteriaOperation.EQUALS_NUMBER,
            CertificationPartnerAuditCriteriaOperation.NOT_EQUALS_NUMBER
        ],
        boolean: [
            CertificationPartnerAuditCriteriaOperation.IS_TRUE,
            CertificationPartnerAuditCriteriaOperation.IS_FALSE
        ],
        select: [
            CertificationPartnerAuditCriteriaOperation.EQUALS_STRING,
        ],
        selectArray: [
            CertificationPartnerAuditCriteriaOperation.CONTAINS_ARRAY,
            CertificationPartnerAuditCriteriaOperation.NOT_CONTAINS_ARRAY,
        ],
        custom: [
            CertificationPartnerAuditCriteriaOperation.AI
        ]
    };

    constructor(public dialogRef: MatDialogRef<CertificationAuditCriteriaFormComponent>,
                private _certificationPartnerAuditTemplateService: CertificationPartnerAuditTemplateService,
                private _translateService: TranslateService,
                private _formBuilder: FormBuilder,
                private _dialog: MatDialog,
                @Inject(MAT_DIALOG_DATA) public dialogData: {
                    certificationPartnerAuditTemplate: CertificationPartnerAuditTemplate,
                    criteria: CertificationPartnerAuditCriteria,
                    criteriasList: CriteriaScopeSelectChoice[]
                }
    ) {
    }

    ngOnInit(): void {
        Object.values(this.dialogData.criteriasList).forEach((scope) => {
            scope.value.forEach((scopeValue) => this.scopeChoices.push({
                key: scopeValue.key,
                value: scopeValue.value,
                type: scopeValue.type,
                operationValues: scopeValue.operationValues
            }));
        });
        this.certificationPartnerAuditTemplate = this.dialogData.certificationPartnerAuditTemplate;
        this.initForm(this.dialogData.criteria);
    }

    initForm(existingCriteria: CertificationPartnerAuditCriteria): void {
        let operationChoices: Choices = [];
        if (existingCriteria) {
            this.existingCriteria = existingCriteria;
            const selectedScope = this.scopeChoices.find((scopeChoice) => this.existingCriteria.scope === scopeChoice.value);
            this.currentOperationType = selectedScope.type as OperationType;
            operationChoices = this.getOperationChoicesForType(selectedScope, this.currentOperationType);
            if (this.currentOperationType === 'custom') {
                this.currentCustomParameter = this.existingCriteria.parameter;
            }
        } else {
            this.existingCriteria = null;
        }
        this.formGroup = this._formBuilder.group({
            criteriaForm: this._formBuilder.group({})
        });
        const appFormFieldsData: AppFormFieldData[] = [
            {
                controlName: 'scope',
                value: this.existingCriteria?.scope ?? null,
                required: true,
                disabled: !!this.existingCriteria,
                label: 'private.common.certification.audit.criterias.form.scope',
                type: 'select',
                listChoices: this.dialogData.criteriasList,
                placeholderSearchInList: 'private.common.certification.audit.criterias.form.placeHolder',
                change: (controlName, newScope, appFormFieldsDatas, formGroup, formGroupName) => {
                    const appFormFieldCode = appFormFieldsDatas.find(field => field.controlName === 'code');
                    const appFormFieldTitle = appFormFieldsDatas.find(field => field.controlName === 'title');
                    appFormFieldCode.value = camelCase(newScope);
                    const selectedScope = this.scopeChoices.find((scopeChoice) => newScope === scopeChoice.value);
                    appFormFieldTitle.value = selectedScope.key;
                    const updatedAppFormFields = [appFormFieldCode, appFormFieldTitle];
                    const newType = selectedScope.type as OperationType;
                    if (newType !== this.currentOperationType) {
                        this.currentOperationType = newType;
                        const appFormFieldOperation = appFormFieldsDatas.find(field => field.controlName === 'operation');
                        const appFormFieldParameter = appFormFieldsDatas.find(field => field.controlName === 'parameter');
                        if (newType === 'custom') {
                            appFormFieldOperation.removed = true;
                            appFormFieldParameter.removed = true;
                        } else {
                            const operationChoicesForType = this.getOperationChoicesForType(selectedScope, this.currentOperationType);
                            appFormFieldOperation.choices = operationChoicesForType;
                            appFormFieldOperation.disabled = false;
                            appFormFieldOperation.removed = false;
                            if (this.currentOperationType === 'select') {
                                appFormFieldOperation.value = operationChoicesForType[0].value;
                            } else if (formGroup.get(formGroupName).get('operation')) {
                                formGroup.get(formGroupName).get('operation').setValue(undefined);
                                // HACK isEqual(null, undefined) == false => trigger async update that resets validation
                            }
                            if (this.currentOperationType === 'select') {
                                appFormFieldParameter.removed = false;
                                appFormFieldParameter.type = 'select';
                                appFormFieldParameter.label = this._translateService.instant('common.matching.title.' + appFormFieldOperation.value);
                                appFormFieldParameter.help = this.getParameterHelp(appFormFieldOperation.value);
                                appFormFieldParameter.choices = this.getParameterChoices(selectedScope.operationValues);
                            } else {
                                appFormFieldParameter.removed = true;
                            }
                        }
                        updatedAppFormFields.push(appFormFieldOperation, appFormFieldParameter);
                    }
                    return updatedAppFormFields;
                }
            },
            {
                controlName: 'title',
                value: this.existingCriteria?.title ?? null,
                label: 'private.common.certification.audit.criterias.form.title',
                type: 'text',
                colSpan: 4,
                maxLength: 50
            },
            {
                controlName: 'code',
                value: this.existingCriteria?.code ?? null,
                required: true,
                disabled: !!this.existingCriteria,
                label: 'private.common.certification.audit.criterias.form.code.title',
                help: 'private.common.certification.audit.criterias.form.code.help',
                type: 'text',
                colSpan: 2,
                maxLength: 35
            },
            {
                controlName: 'operation',
                removed: this.currentOperationType === 'custom',
                value: this.existingCriteria?.operation ?? null,
                required: true,
                disabled: operationChoices.length === 0,
                colSpan: 3,
                label: 'private.common.certification.audit.criterias.form.operation.title',
                type: 'select',
                choices: operationChoices,
                change: (controlName, newValue, appFormFieldsDatas) => {
                    const appFormFieldParameter = appFormFieldsDatas.find(field => field.controlName === 'parameter');
                    if (newValue) {
                        appFormFieldParameter.value = null;
                    }
                    if (this.currentOperationType === 'boolean' || this.currentOperationType === 'selectArray') {
                        appFormFieldParameter.removed = true;
                    } else {
                        appFormFieldParameter.removed = false;
                        appFormFieldParameter.label = this._translateService.instant('common.matching.title.' + newValue);
                        appFormFieldParameter.help = this.getParameterHelp(newValue);
                        if (this.currentOperationType === 'number') {
                            appFormFieldParameter.type = 'number';
                            appFormFieldParameter.placeholder = '5';
                        } else if (this.currentOperationType === 'string') {
                            appFormFieldParameter.type = 'text';
                            appFormFieldParameter.placeholder = 'Niveau A2';
                        }
                    }
                    return [appFormFieldParameter];
                }
            },
            {
                controlName: 'parameter',
                required: true,
                value: this.existingCriteria?.parameter ?? null,
                removed: !this.existingCriteria || ['boolean', 'selectArray', 'custom'].includes(this.currentOperationType),
                colSpan: 3,
                choices: this.currentOperationType === 'select' ? this.getParameterChoices(this.existingCriteria.operationValues) : null,
                label: this.existingCriteria ? 'common.matching.title.' + this.existingCriteria.operation : '',
                help: this.existingCriteria ? this.getParameterHelp(this.existingCriteria.operation) : null,
                type: this.currentOperationType === 'number' ? 'number' : this.currentOperationType === 'select' ? 'select' : 'text'
            },
            {
                controlName: 'advice',
                value: this.existingCriteria?.advice ?? null,
                label: 'private.common.certification.audit.criterias.form.advice.title',
                help: 'private.common.certification.audit.criterias.form.advice.help',
                type: 'textarea',
                maxLength: 500 // TODO(audit): maybe allow more?
            },
            {
                controlName: 'severity',
                value: this.existingCriteria?.severity ?? CertificationPartnerAuditCriteriaSeverity.NONE,
                label: 'private.common.certification.audit.criterias.form.severity.title',
                type: 'select',
                choices: [
                    {
                        key: this._translateService.instant('private.common.certification.audit.criterias.form.severity.none'),
                        value: CertificationPartnerAuditCriteriaSeverity.NONE
                    },
                    {
                        key: this._translateService.instant('private.common.certification.audit.criterias.form.severity.mineure'),
                        value: CertificationPartnerAuditCriteriaSeverity.MINEURE,
                        icon: 'warning',
                        iconClass: 'text-orange'
                    },
                    {
                        key: this._translateService.instant('private.common.certification.audit.criterias.form.severity.majeure'),
                        value: CertificationPartnerAuditCriteriaSeverity.MAJEURE,
                        icon: 'priority_high',
                        iconClass: 'text-red'
                    }
                ]
            }
        ];
        this.appFormFieldsData = appFormFieldsData.filter(value => value != null);
    }

    closeModal(): void {
        this.dialogRef.close({data: this.certificationPartnerAuditTemplate});
    }

    createOrUpdate(close = true): void {
        const updatedCriteria = this.getCriteriaFromForm();
        let updatedCriterias: CertificationPartnerAuditCriteria[];
        if (this.existingCriteria) {
            updatedCriterias = this.certificationPartnerAuditTemplate.criterias.map(criteria => { // COPY to avoid updating original
                return criteria.code === this.existingCriteria.code ? updatedCriteria : criteria;
            });
        } else {
            // COPY to avoid updating original
            updatedCriterias = this.certificationPartnerAuditTemplate.criterias?.length ? [...this.certificationPartnerAuditTemplate.criterias] : [];
            updatedCriterias.push(updatedCriteria);
        }
        const updatedCertificationPartnerAuditTemplate: CertificationPartnerAuditTemplate = {
            ...this.certificationPartnerAuditTemplate, // COPY to avoid updating original
            criterias: updatedCriterias
        };
        this.loading = true;
        this.errorMessages = [];
        this._certificationPartnerAuditTemplateService.update(updatedCertificationPartnerAuditTemplate).subscribe(
            certificationPartnerAuditTemplateUpdated => {
                this.loading = false;
                this.certificationPartnerAuditTemplate = certificationPartnerAuditTemplateUpdated;
                if (close) {
                    this.closeModal();
                } else {
                    const criteriaUpdated = certificationPartnerAuditTemplateUpdated.criterias.find(criteria => criteria.code === updatedCriteria.code);
                    this.initForm(criteriaUpdated);
                }
            },
            (httpErrorResponse: HttpErrorResponse) => {
                this.loading = false;
                this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
            }
        );
    }

    private getCriteriaFromForm(): CertificationPartnerAuditCriteria {
        const criteriaValue = this.formGroup.getRawValue().criteriaForm;
        const selectedScope = this.scopeChoices.find((scopeChoice) => criteriaValue.scope === scopeChoice.value);
        let operationTitle: string;
        let operation: CertificationPartnerAuditCriteriaOperation;
        let parameter: any;
        if (selectedScope.type === 'custom') {
            operation = CertificationPartnerAuditCriteriaOperation.AI; // TODO way to discriminate operation
            operationTitle = this._translateService.instant('common.matching.title.ai');
            parameter = this.currentCustomParameter;
        } else {
            operation = criteriaValue.operation;
            operationTitle = selectedScope.operationValues && selectedScope.operationValues[operation]
                ? (this._translateService.instant('common.matching.title.' + operation) + selectedScope.operationValues[operation])
                : this._translateService.instant('common.matching.title.' + operation);
            parameter = criteriaValue.parameter;
        }
        return {
            code: criteriaValue.code,
            title: criteriaValue.title,
            scope: criteriaValue.scope,
            scopeTitle: selectedScope.key,
            operation: operation,
            operationTitle: operationTitle,
            parameter: parameter ?? null, // null is better because undefined is not sent to the back so $body['parameter'] fails
            advice: criteriaValue.advice,
            operationValues: selectedScope.operationValues,
            severity: criteriaValue.severity
        };
    }

    private getOperationChoicesForType(selectedScope, scopeType: OperationType): Choices {
        const operations = this.operationsByType[scopeType];
        return operations.map(operation => {
            return {
                key: selectedScope.operationValues && selectedScope.operationValues[operation] ? selectedScope.operationValues[operation] : this._translateService.instant('common.matching.' + operation),
                value: operation
            };
        });
    }

    private getParameterHelp(operation: string): string {
        const helpKey = 'common.matching.help.' + operation;
        const help = this._translateService.instant(helpKey);
        return help !== helpKey ? help : null;
    }

    private getParameterChoices(operationValues: object): Choices {
        return Object.entries(operationValues).map(([key, value]: [string, any]) => {
            return {
                key: value,
                value: key
            };
        });
    }

    isAI(): boolean {
        return this.currentOperationType === 'custom';
    }

    trainAI(): void {
        const dialogRef = this._dialog.open(DialogTrainAIComponent, {
            panelClass: ['full-page-scroll-40'],
            disableClose: true,
            data: {
                existingTrainingData: this.currentCustomParameter,
                certifInfo: this.certificationPartnerAuditTemplate._links.certification.certifInfo
            }
        });
        dialogRef.afterClosed().subscribe(trainingData => {
            if (Array.isArray(trainingData)) {
                this.currentCustomParameter = trainingData;
                this.formGroup.markAsDirty();
                this.createOrUpdate(false);
            }
        });
    }

    getAIPrecisionPercentage(): number {
        const trainingData = this.currentCustomParameter;
        if (!trainingData || !Array.isArray(trainingData)) {
            return 0;
        }
        const count = trainingData.length;
        const fullTrainingThreshold = 25;
        return Math.min(Math.round((count / fullTrainingThreshold) * 100), 100);
    }
}
