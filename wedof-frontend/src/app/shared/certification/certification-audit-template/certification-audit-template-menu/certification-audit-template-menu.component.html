<button mat-icon-button
        (click)="$event.stopPropagation()"
        [matMenuTriggerFor]="actionsMenu"
        title="Actions">
    <mat-icon svgIcon="more_vert"></mat-icon>
</button>
<mat-menu #actionsMenu="matMenu">
    <ng-template matMenuContent>
        <button mat-menu-item (click)="openUpdateCriteriasDialog()">
            <mat-icon color="primary" svgIcon="checklist"></mat-icon>
            <span>{{ 'private.common.certification.audit.template.updateCriterias' | translate }}</span>
        </button>
        <button mat-menu-item (click)="update()">
            <mat-icon color="primary" svgIcon="edit"></mat-icon>
            <span>{{ 'common.actions.update' | translate }}</span>
        </button>
        <button mat-menu-item (click)="openTemplate()">
            <mat-icon color="primary" svgIcon="explore"></mat-icon>
            <span>{{'common.actions.updateOnline' | translate : {type: "de rapport"} }}</span>
        </button>
        <button mat-menu-item *ngIf="certificationPartnerAuditTemplate.criterias?.length"
                [disabled]="!certificationPartnerAuditTemplate.allowCreateAudit"
                (click)="createOnPartners()">
            <mat-icon color="primary" svgIcon="present_to_all"></mat-icon>
            <span>{{ 'private.common.certification.audit.template.auditPartners.title' | translate }}</span>
        </button>
        <button mat-menu-item *ngIf="certificationPartnerAuditTemplate.auditCount.all >= 1" (click)="exportFile()">
            <mat-icon color="primary" svgIcon="download"></mat-icon>
            <span>{{'common.actions.export.exportAuditReport' | translate}}</span>
        </button>
        <button mat-menu-item (click)="delete()">
            <mat-icon color="warn" svgIcon="delete"></mat-icon>
            <span>{{ 'common.actions.delete' | translate }}</span>
        </button>
    </ng-template>
</mat-menu>
