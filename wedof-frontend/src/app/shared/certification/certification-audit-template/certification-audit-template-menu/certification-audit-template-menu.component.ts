import {Component, EventEmitter, Input, Output} from '@angular/core';
import {MatDialog} from '@angular/material/dialog';
import {CertificationPartnerAuditTemplate} from '../../../api/models/certification-partner-audit';
import {DialogCertificationAuditTemplateComponent} from '../dialog-certification-audit-template/dialog-certification-audit-template.component';
import {FileIframeComponent} from '../../../file/dialogs/file-iframe/file-iframe.component';

@Component({
    selector: 'app-certification-audit-template-menu',
    templateUrl: './certification-audit-template-menu.component.html',
    styleUrls: ['./certification-audit-template-menu.component.scss']
})
export class CertificationAuditTemplateMenuComponent {

    @Input() certificationPartnerAuditTemplate: CertificationPartnerAuditTemplate;
    @Output() deleteTemplate = new EventEmitter<void>();
    @Output() certificationPartnerAuditTemplateUpdated = new EventEmitter<CertificationPartnerAuditTemplate>();
    @Output() manageCriterias = new EventEmitter<CertificationPartnerAuditTemplate>();
    @Output() generateAuditOnPartners = new EventEmitter<void>();
    @Output() exportAuditFile = new EventEmitter<void>();

    constructor(private _dialog: MatDialog) {
    }

    delete(): void {
        this.deleteTemplate.emit();
    }

    openUpdateCriteriasDialog(): void {
        this.manageCriterias.emit(this.certificationPartnerAuditTemplate);
    }

    openTemplate(): void {
        this._dialog.open(FileIframeComponent, {
            disableClose: true,
            width: '80%',
            data: {
                templateEditor: {
                    templateGid: this.certificationPartnerAuditTemplate.auditTemplateFileType.googleId,
                    contextId: this.certificationPartnerAuditTemplate.id,
                    layout: 'document',
                    type: 'document',
                    scope: 'audit'
                }
            }
        });
    }

    update(): void {
        const dialogRef = this._dialog.open(DialogCertificationAuditTemplateComponent, {
            disableClose: true,
            panelClass: 'full-page-scroll-40',
            data: {
                certificationPartnerAuditTemplate: this.certificationPartnerAuditTemplate,
            }
        });
        dialogRef.afterClosed().subscribe(result => {
            if (result?.data) {
                this.certificationPartnerAuditTemplateUpdated.emit(result?.data);
            }
        });
    }

    createOnPartners(): void {
        this.generateAuditOnPartners.emit();
    }

    exportFile(): void {
        this.exportAuditFile.emit();
    }
}
