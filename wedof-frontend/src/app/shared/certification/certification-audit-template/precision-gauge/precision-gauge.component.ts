import {Component, Input, OnChanges, SimpleChanges} from '@angular/core';

@Component({
    selector: 'app-precision-gauge',
    templateUrl: './precision-gauge.component.html',
    styleUrls: ['./precision-gauge.component.scss']
})
export class PrecisionGaugeComponent implements OnChanges {
    @Input() percentage = 0;
    @Input() yellowThreshold = 70;
    @Input() redThreshold = 40;
    @Input() greenText: string;
    @Input() yellowText: string;
    @Input() redText: string;

    color: 'green' | 'yellow' | 'red';
    statusText: string;

    ngOnChanges(changes: SimpleChanges): void {
        if (changes.percentage) {
            this.updateStatus();
        }
    }

    private updateStatus(): void {
        if (this.percentage < this.redThreshold) {
            this.color = 'red';
            this.statusText = this.redText;
        } else if (this.percentage < this.yellowThreshold) {
            this.color = 'yellow';
            this.statusText = this.yellowText;
        } else {
            this.color = 'green';
            this.statusText = this.greenText;
        }
    }
}
