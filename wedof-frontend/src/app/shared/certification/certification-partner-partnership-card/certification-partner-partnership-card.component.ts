import {
    AfterViewChecked,
    ChangeDetector<PERSON>ef,
    Component,
    ElementRef,
    EventEmitter,
    Input,
    OnChanges,
    OnDestroy,
    Output,
    SimpleChanges,
    ViewChild
} from '@angular/core';
import {CertificationPartner, CertificationPartnerStates} from '../../api/models/certification-partner';
import {Certification, CertificationTypes} from '../../api/models/certification';
import {FormBuilder, FormGroup} from '@angular/forms';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../errors/errors.types';
import {CertificationPartnerService} from '../../api/services/certification-partner.service';
import {filter, finalize, switchMap, tap} from 'rxjs/operators';
import {SnackBarComponent} from '../../material/snack-bar/snack-bar.component';
import {displayTextSnackBar} from '../../utils/displayTextSnackBar';
import {MatSnackBar} from '@angular/material/snack-bar';
import {TranslateService} from '@ngx-translate/core';
import {DeletionConfirmationComponent} from '../../material/action-confirmation/deletion-confirmation.component';
import {MatDialog} from '@angular/material/dialog';
import {AppFormFieldData} from '../../material/app-form-field/app-form-field.component';
import {ActionConfirmationComponent} from '../../material/action-confirmation/action-confirmation.component';
import {BaseCardComponentDirective, RequiredCallSuper} from '../../utils/base-card/base-card.directive';
import {OrganismService} from '../../api/services/organism.service';
import {FileType} from '../../api/models/file';
import {FileListPartnerPartnershipComponent} from '../../file/file-list-partner-partnership/file-list-partner-partnership.component';
import {displaySkillSetTitle, Skill} from '../../api/models/skill';
import {CityService} from '../../api/services/city.service';
import {CertificationPartnerAuditResults} from '../../api/models/certification-partner-audit';

@Component({
    selector: 'app-certification-partner-partnership-card',
    templateUrl: './certification-partner-partnership-card.component.html',
    styleUrls: ['./certification-partner-partnership-card.component.scss']
})
export class CertificationPartnerPartnershipCardComponent extends BaseCardComponentDirective implements OnChanges, OnDestroy, AfterViewChecked {
    static COMPONENT_ID = 'partenariat';
    CertificationPartnerStates = CertificationPartnerStates;

    loading = false;
    formGroup: FormGroup;
    appFormFieldsData: AppFormFieldData[];

    certifiersName = '';
    errorMessages: string[] = [];
    states = CertificationPartnerStates;
    orderedCertificationPartnerStates: CertificationPartnerStates[] = Object.values(CertificationPartnerStates);
    fileTypes: FileType<CertificationPartnerStates>[];
    certificationTypes = CertificationTypes;
    trainingsZoneTitle = '';
    certificationPartnerAuditResults = CertificationPartnerAuditResults;

    @Input() certification: Certification;
    @Input() certificationPartner?: CertificationPartner;
    @Output() certificationPartnerUpdated: EventEmitter<CertificationPartner> = new EventEmitter<CertificationPartner>();
    @Output() certificationPartnerDeleted: EventEmitter<CertificationPartner> = new EventEmitter<CertificationPartner>();
    @ViewChild('appFileList') appFileList: FileListPartnerPartnershipComponent<CertificationPartner, CertificationPartnerStates>;

    constructor(private _fb: FormBuilder,
                private _snackBar: MatSnackBar,
                private _cityService: CityService,
                private _translateService: TranslateService,
                private _certificationPartnerService: CertificationPartnerService,
                private _changeDetectorRef: ChangeDetectorRef,
                private _dialog: MatDialog,
                private _organismService: OrganismService,
                private _el: ElementRef) {
        super(CertificationPartnerPartnershipCardComponent.COMPONENT_ID, _el);
    }

    ngOnChanges(changes: SimpleChanges): void {
        this.panelLoading();
        this.initForm(this.certificationPartner);
    }

    ngAfterViewChecked(): void {
        // Hack for isRequiredDocumentsMissingForNextState to prevent ExpressionChangedAfterItHasBeenCheckedError
        this._changeDetectorRef.detectChanges();
    }

    ngOnDestroy(): RequiredCallSuper {
        return super.ngOnDestroy();
    }

    initForm(certificationPartner: CertificationPartner): void {
        this.errorMessages = [];
        if (this.isEditableByPartner(certificationPartner)) {
            this.formGroup = this._fb.group({
                certificationPartner: this._fb.group({})
            });
            const comment = certificationPartner.comment ? certificationPartner.comment : this.certification.partnershipComment; // Don't use "??", we want to skip empty string
            this.appFormFieldsData = [
                {
                    value: comment,
                    controlName: 'comment',
                    label: 'private.certification.partners.table.partnerComment',
                    type: 'textarea',
                    maxLength: 5000,
                    help: 'private.certification.partners.table.commentTooltip',
                    placeholder: 'private.certification.partners.table.partnerCommentPlaceholder'
                },
            ];
        }
        if (this.certificationPartner?._links.certifier !== undefined) {
            this.certifiersName = this.certificationPartner._links.certifier.name;
        } else {
            this.certifiersName = '';
            this._organismService.listByUrl(this.certification._links.certifiers.href).subscribe(certifiers => {
                this.certifiersName = certifiers.map(certifier => certifier.name).join(', ');
            });
        }
        if (this.certificationPartner) {
            this._certificationPartnerService.listAllFiles(this.certification.certifInfo, this.certificationPartner._links.partner.siret).subscribe((filesOrFileTypes) => {
                const allowedFileTypeIds = filesOrFileTypes.map(fileOrFileType => fileOrFileType.typeId);
                this.fileTypes = this.certification.certificationPartnerFileTypes.filter(fileType => allowedFileTypeIds.includes(fileType.id));
                if (this.certificationPartner.trainingsZone) {
                    this._cityService.getByPostalCodes(this.certificationPartner.trainingsZone.toString()).subscribe((cities) => {
                        this.trainingsZoneTitle = cities.map((city) => city.name + ' (' + city.postalCode + ')').join(', ');
                    });
                } else {
                    this.panelLoaded();
                }
            });
        } else {
            this.fileTypes = [];
            this.panelLoaded();
        }
    }

    isEditableByPartner(certificationPartner: CertificationPartner): boolean {
        return certificationPartner && (certificationPartner.state === CertificationPartnerStates.PROCESSING || certificationPartner.state === CertificationPartnerStates.DRAFT);
    }

    submit(certificationPartner: CertificationPartner): void {
        this.errorMessages = [];
        this.panelLoading();
        if (this.formGroup.valid) {
            this.loading = true;
            const certificationPartnerValue = this.formGroup.getRawValue().certificationPartner;
            const body = {...certificationPartnerValue};
            this._certificationPartnerService.update(this.certification.certifInfo, certificationPartner._links.partner.siret, body).pipe(
                finalize(() => {
                    this.panelLoaded();
                })
            ).subscribe({
                next: (certificationPartnerUpdated) => {
                    this.certificationPartner = certificationPartnerUpdated;
                    this.initForm(certificationPartnerUpdated);
                    this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(
                        this._translateService.instant('common.actions.certificationPartnerUpdatedSuccessfully')));
                },
                error: (httpErrorResponse: HttpErrorResponse) => {
                    this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
                }
            });
        }
    }

    delete(certificationPartner: CertificationPartner): void {
        const dialogRef = this._dialog.open(DeletionConfirmationComponent, {
            panelClass: 'full-page-scroll-30',
            height: 'auto',
            data: {
                messageKey: 'private.common.certification.managePartnership.confirmDeletion'
            }
        });
        dialogRef.componentInstance.actionValue$.pipe(
            filter(confirmation => confirmation),
            tap(() => this.loading = true),
            switchMap(() => this._certificationPartnerService.delete(this.certification.certifInfo, certificationPartner._links.partner.siret)),
            finalize(() => {
                dialogRef.componentInstance.close();
                this.loading = false;
            })
        ).subscribe(() => {
            this.certificationPartnerDeleted.emit(this.certificationPartner);
        });
    }

    isRequiredDocumentsMissingForNextState(certificationPartner: CertificationPartner): boolean {
        return this.appFileList ? this.appFileList.isRequiredDocumentsMissingForNextState(certificationPartner) : false;
    }

    reinitialize(certificationPartner: CertificationPartner): void {
        const dialogRef = this._dialog.open(ActionConfirmationComponent, {
            panelClass: 'full-page-scroll-30',
            height: 'auto',
            data: {
                title: 'common.confirmation.reinitialization.title',
                color: 'warn',
                messageKey: 'private.common.certification.managePartnership.confirm.reinitialize',
            }
        });
        dialogRef.componentInstance.actionValue$.pipe(
            filter(confirmation => confirmation),
            tap(() => this.loading = true),
            switchMap(() => this._certificationPartnerService.reinitialize(this.certification.certifInfo, certificationPartner._links.partner.siret)),
            finalize(() => {
                dialogRef.componentInstance.close();
                this.loading = false;
            })
        ).subscribe((certificationPartnerUpdated: CertificationPartner) => {
            this.certificationPartner = certificationPartnerUpdated;
            this.initForm(certificationPartnerUpdated);
            this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(
                this._translateService.instant('common.actions.certificationPartnerUpdatedSuccessfully')));
        });
    }

    isRequest(certificationPartner: CertificationPartner): boolean {
        return !certificationPartner || [
            this.states.DRAFT, this.states.PROCESSING, this.states.ABORTED, this.states.REFUSED
        ].includes(certificationPartner.state);
    }

    getSkillSets(skillSets: Skill[]): string {
        return skillSets.length ? skillSets.map((skillSet) => displaySkillSetTitle(skillSet)).join(', ') :
            this._translateService.instant('private.common.skills.placeholderAll');
    }
}
