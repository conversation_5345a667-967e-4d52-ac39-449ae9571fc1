<mat-card class="flex-col mt-3 p-5 pb-0 pt-3 border shadow-none sm">
    <div class="flex items-center mb-2">
        <mat-icon class="mr-3 text-4xl card-loading-show" svgIcon="group"
                  color="primary">
        </mat-icon>
        <div>
            <div class="text-xl font-semibold card-loading-show">
                {{ ('private.certification.partners.table.partnership.' + (isRequest(certificationPartner) ? 'titleRequest' : 'title'))  | translate }}
            </div>
            <div class="text-secondary text-md card-loading-hidden">
                <a [routerLink]="['/aide/guides/organismes-formation/gerer-partenariats']">Consulter la
                    documentation</a>
            </div>
        </div>
    </div>
    <div class="flex flex-col mb-3 card-loading-hidden">
        <div class="grid grid-cols-6 gap-2 mt-2" *ngIf="certificationPartner">
            <app-form-field-static class="col-span-3"
                                   [value]="certificationPartner | certificationPartnerStateName"
                                   label="private.certification.partners.table.partnership.stateLabel"
                                   type="text"></app-form-field-static>
            <app-form-field-static class="col-span-3"
                                   [value]="'private.certification.audit.result.' + certificationPartner.compliance | translate"
                                   [icon]="certificationPartner.compliance | certificationPartnerAuditResultToIcon"
                                   [iconClass]="certificationPartner.compliance === certificationPartnerAuditResults.COMPLIANT ? 'text-green' :
                                     certificationPartner.compliance === certificationPartnerAuditResults.PARTIALLY_COMPLIANT ? 'text-orange' : ''"
                                   [iconColor]="certificationPartner.compliance === certificationPartnerAuditResults.NON_COMPLIANT ? 'warn' : undefined"
                                   label="private.common.certification.compliance"
                                   type="text"></app-form-field-static>
        </div>
        <app-form-field-static
            *ngIf="certificationPartner?.habilitation && !isEditableByPartner(certificationPartner)"
            [value]="('private.certification.partners.table.habilitation.' + certificationPartner.habilitation) | translate"
            label="private.certification.partners.table.habilitation.label"
            type="text"></app-form-field-static>
        <ng-container *ngIf="certificationPartner?.urls?.length; else noUrls">
            <mat-label class="font-medium">{{'private.certification.partners.websites' | translate }}</mat-label>
            <app-form-field-static *ngFor="let url of certificationPartner.urls"
                                   [value]="url"
                                   type="url"></app-form-field-static>
        </ng-container>
        <ng-template #noUrls>
            <app-form-field-static
                [value]="null"
                label="private.certification.partners.websites"
                type="text"></app-form-field-static>
        </ng-template>
        <app-form-field-static
            *ngIf="certificationPartner && (certificationPartner.comment || certification.partnershipComment) && !isEditableByPartner(certificationPartner)"
            [value]="certificationPartner?.comment ? certificationPartner.comment : certification.partnershipComment "
            label="private.certification.partners.table.comment"
            type="notes"></app-form-field-static>
        <app-form-field-static
            [value]="certifiersName"
            label="private.certification.partners.table.certifiers"
            type="text"></app-form-field-static>
        <form class="flex flex-col mb-2"
              (ngSubmit)="submit(certificationPartner)"
              [formGroup]="formGroup"
              *ngIf="isEditableByPartner(certificationPartner)">
            <app-form-fields formGroupName="certificationPartner"
                             [entity]="certificationPartner"
                             [appFormFieldsData]="appFormFieldsData"
                             [formGroup]="formGroup">
            </app-form-fields>
        </form>
        <ng-container *ngIf="certificationPartner">
            <app-form-field-static
                *ngIf="certification.type === certificationTypes.RNCP"
                [value]="getSkillSets(certificationPartner.skillSets)"
                label="private.certification.partners.skillSets"
                type="text"></app-form-field-static>
            <app-form-field-static
                [value]="trainingsZoneTitle ? trainingsZoneTitle : ('private.common.form.placeholder' | translate)"
                label="private.certification.partners.trainingsZone"
                type="text"></app-form-field-static>
        </ng-container>

        <span *ngIf="!certificationPartner"
              class="text-secondary">{{ 'private.certification.partners.table.partnership.partnerPlaceholder' | translate }}</span>

        <app-file-list-partner-partnership #appFileList
                                           *ngIf="certificationPartner"
                                           [entity]="certificationPartner"
                                           [subtitle]="'private.common.files.subtitlePartnership'"
                                           [fileTypes]="fileTypes"
                                           [entityParentId]="certification.id.toString()"
                                           [orderedStates]="orderedCertificationPartnerStates"
                                           entityApiPath="certificationPartners"
                                           entityIdProperty="id"
                                           titleStateChange="private.certification.partners.table.partnership.state.">
        </app-file-list-partner-partnership>

    </div>
    <div *ngIf="!certificationPartner"
         class="flex items-center justify-end border-t -mx-5 px-5 py-3 light:bg-cool-gray-50 dark:bg-cool-gray-700 card-loading-hidden">
        <app-certification-catalog-button-new [certification]="certification"
                                              (certificationPartnerCreated)="certificationPartnerUpdated.emit($event)">
        </app-certification-catalog-button-new>
    </div>
    <div *ngIf="formGroup?.dirty"
         class="flex items-center justify-end border-t -mx-5 px-5 py-3 light:bg-cool-gray-50 dark:bg-cool-gray-700 card-loading-hidden">
        <button type="button"
                [disabled]="formGroup?.invalid || loading"
                mat-flat-button
                color="primary"
                (click)="submit(certificationPartner)">
            <mat-progress-spinner *ngIf="loading"
                                  [diameter]="24"
                                  [mode]="'indeterminate'"></mat-progress-spinner>
            <ng-container *ngIf="!loading"> {{ 'common.actions.update' | translate }}
            </ng-container>
        </button>
    </div>
    <div
        *ngIf="certificationPartner && !formGroup?.dirty && (certificationPartner.state === states.DRAFT || certificationPartner.state === states.PROCESSING || certificationPartner.state === states.ABORTED || (certificationPartner.state === states.ACTIVE && certificationPartner._links.certifier && !certificationPartner._links.certifier.hasOwner))"
        class="flex items-center justify-end border-t -mx-5 px-5 py-3 light:bg-cool-gray-50 dark:bg-cool-gray-700 card-loading-hidden">
        <app-certification-catalog-button-invite
            *ngIf="certificationPartner.state === states.ACTIVE && certificationPartner._links.certifier && !certificationPartner._links.certifier.hasOwner"
            [certification]="certification">
        </app-certification-catalog-button-invite>
        <app-certification-catalog-button-update-state *ngIf="certificationPartner.state == states.PROCESSING"
                                                       [certificationPartner]="certificationPartner"
                                                       [certification]="certification"
                                                       [targetState]="states.ABORTED"
                                                       [label]="'private.common.certification.managePartnership.menu.abort' | translate"
                                                       (certificationPartnerUpdated)="certificationPartnerUpdated.emit($event)"
                                                       color="warn">
        </app-certification-catalog-button-update-state>
        <app-certification-catalog-button-update-state *ngIf="certificationPartner.state == states.ABORTED"
                                                       [certificationPartner]="certificationPartner"
                                                       [certification]="certification"
                                                       [targetState]="states.PROCESSING"
                                                       [label]="'private.common.certification.managePartnership.menu.reopenShort' | translate"
                                                       (certificationPartnerUpdated)="certificationPartnerUpdated.emit($event)"
                                                       color="primary">
        </app-certification-catalog-button-update-state>
        <ng-container *ngIf="isEditableByPartner(certificationPartner)">
            <app-certification-catalog-button-request *ngIf="certificationPartner?.state === states.DRAFT"
                                                      [certification]="certification"
                                                      [certifiersName]="certifiersName"
                                                      [disabled]="isRequiredDocumentsMissingForNextState(certificationPartner)"
                                                      (certificationPartnerUpdated)="certificationPartnerUpdated.emit($event)">
            </app-certification-catalog-button-request>
            <button mat-icon-button
                    class="ml-2"
                    (click)="$event.stopPropagation()"
                    [matMenuTriggerFor]="actionsMenu"
                    title="Actions">
                <mat-icon svgIcon="more_vert"></mat-icon>
            </button>
            <mat-menu #actionsMenu="matMenu" class="large-menu">
                <button *ngIf="certificationPartner?.state === states.DRAFT"
                        (click)="delete(certificationPartner)"
                        mat-menu-item
                        type="button">
                    <mat-icon color="warn" svgIcon="delete"></mat-icon>
                    {{ 'common.actions.delete' | translate }}
                </button>
                <button *ngIf="certificationPartner?.state === states.PROCESSING"
                        type="button"
                        mat-menu-item
                        (click)="reinitialize(certificationPartner)">
                    <mat-icon color="warn" svgIcon="restart_alt"></mat-icon>
                    {{ 'private.common.certification.managePartnership.menu.reinitialize' | translate }}
                </button>
            </mat-menu>
        </ng-container>
    </div>
</mat-card>
