import {Component, Inject, Injector, OnInit} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material/dialog';
import {Certification} from '../../../api/models/certification';
import {AbstractDialogFormComponent} from '../../../material/form/abstract-dialog-form.component';
import {Validators} from '@angular/forms';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../../errors/errors.types';
import {CertificationService} from '../../../api/services/certification.service';

export interface CertifierContactInformation {
    name: string;
    email: string;
}

@Component({
    selector: 'app-dialog-invitation-confirm',
    templateUrl: './dialog-invitation-confirm.component.html',
    styleUrls: ['./dialog-invitation-confirm.component.scss']
})
export class DialogInvitationConfirmComponent extends AbstractDialogFormComponent<CertifierContactInformation> implements OnInit {

    constructor(
        injector: Injector,
        private _certificationService: CertificationService,
        public dialogRef: MatDialogRef<DialogInvitationConfirmComponent>,
        @Inject(MAT_DIALOG_DATA) public dialogData: { certification: Certification }
    ) {
        super(injector);
    }

    ngOnInit(): void {
        this.initForm();
    }

    close(): void {
        this.dialogRef.close();
    }

    submit(): void {
        this.errorMessages = [];
        if (this.formGroup.valid) {
            this.loading = true;
            const certifierContactInformation = this.formGroup.value;
            this._certificationService.inviteCertifier(this.dialogData.certification.certifInfo, certifierContactInformation).subscribe({
                next: () => {
                    this.dialogRef.close(true);
                },
                error: (httpErrorResponse: HttpErrorResponse) => {
                    this.loading = false;
                    this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
                }
            });
        }
    }

    protected initForm(): void {
        this.formGroup = this._fb.group({
            name: [''],
            email: ['', [Validators.required, Validators.email]],
            phoneNumber: []
        });
    }
}
