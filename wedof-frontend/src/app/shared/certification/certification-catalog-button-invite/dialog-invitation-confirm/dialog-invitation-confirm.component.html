<form (ngSubmit)="submit()" [formGroup]="formGroup">
    <app-dialog-layout
        [title]="'private.common.certification.managePartnership.invitationDialog.title' | translate"
        [actions]="actions" [disabled]="loading" [errorMessages]="errorMessages" (dialogClose)="close()">
        <p class="mb-4">
            {{ 'private.common.certification.managePartnership.invitationDialog.message' | translate}}
        </p>

        <mat-form-field class="pb-2 input-line">
            <mat-label>{{ 'private.certification.certifiers.form.labels.full-name' | translate }} {{ 'private.common.certification.managePartnership.invitationDialog.certifierSuffix' | translate}}</mat-label>
            <input id="full-name" matInput formControlName="name" [placeholder]="'private.certification.certifiers.form.placeholders.full-name' | translate" required/>
            <mat-icon matPrefix svgIcon="account_circle"></mat-icon>
        </mat-form-field>

        <mat-form-field class="pb-2 input-line">
            <mat-label>{{ 'private.certification.certifiers.form.labels.email' | translate }} {{ 'private.common.certification.managePartnership.invitationDialog.certifierSuffix' | translate}}</mat-label>
            <input id="email" matInput formControlName="email" [placeholder]="'private.certification.certifiers.form.placeholders.email' | translate" required/>
            <mat-icon matPrefix svgIcon="mail"></mat-icon>
            <mat-error>
                {{ 'private.certification.certifiers.form.errors.email' | translate }}
            </mat-error>
        </mat-form-field>

        <mat-form-field class="pb-2 input-line">
            <mat-label>{{ 'private.certification.certifiers.form.labels.phoneNumber' | translate }} {{ 'private.common.certification.managePartnership.invitationDialog.certifierSuffix' | translate}}</mat-label>
            <input id="phoneNumber" type="text" matInput formControlName="phoneNumber" [placeholder]="'private.certification.certifiers.form.placeholders.phoneNumber' | translate" />
            <mat-icon matPrefix svgIcon="phone"></mat-icon>
        </mat-form-field>

        <ng-template #actions>
            <button type="submit" mat-flat-button color="primary">
                <mat-progress-spinner *ngIf="loading; else submitLabel" [diameter]="24" mode="indeterminate">
                </mat-progress-spinner>
                <ng-template #submitLabel>
                    {{ 'common.actions.submit' | translate }}
                </ng-template>
            </button>
        </ng-template>

    </app-dialog-layout>
</form>
