import {Component, Input} from '@angular/core';
import {Certification} from '../../api/models/certification';
import {SnackBarComponent} from '../../material/snack-bar/snack-bar.component';
import {displayTextSnackBar} from '../../utils/displayTextSnackBar';
import {MatDialog} from '@angular/material/dialog';
import {MatSnackBar} from '@angular/material/snack-bar';
import {TranslateService} from '@ngx-translate/core';
import {DialogInvitationConfirmComponent} from './dialog-invitation-confirm/dialog-invitation-confirm.component';

@Component({
    selector: 'app-certification-catalog-button-invite',
    templateUrl: './certification-catalog-button-invite.component.html',
    styleUrls: ['./certification-catalog-button-invite.component.scss']
})
export class CertificationCatalogButtonInviteComponent {

    @Input() certification: Certification;
    @Input() class?: string;

    inviteSent = false;

    constructor(private _translateService: TranslateService,
                private _snackBar: MatSnackBar,
                private _dialog: MatDialog
    ) {
    }

    openInvitationDialogConfirm(): void {
        const dialogRef = this._dialog.open(DialogInvitationConfirmComponent, {
            disableClose: true,
            panelClass: 'full-page-scroll-40',
            data: {
                certification: this.certification
            }
        });
        dialogRef.afterClosed().subscribe(result => {
            if (result) {
                this.inviteSent = true;
                this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(
                    this._translateService.instant('private.common.certification.managePartnership.invitationDialog.success'), 7000));
            }
        });
    }
}
