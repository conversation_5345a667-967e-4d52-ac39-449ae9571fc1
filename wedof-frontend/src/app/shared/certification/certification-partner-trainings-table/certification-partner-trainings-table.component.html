<table [dataSource]="displayedData" mat-table *ngIf="displayedData">
    <ng-container matColumnDef="name">
        <th mat-header-cell *matHeaderCellDef class="pl-0">
            Formations ({{ total ? total : 0 }})
        </th>
        <td *matCellDef="let training" class="text-left w-2 pl-0" mat-cell>
            <app-form-field-static
                [value]="training.title.length >= 35 ? ((training.title | slice:0:34) + '...') : training.title"
                [matTooltip]="training.title"
                [copyValue]="training.title"
                [copy]="true"
                class="truncate"
                type="text">
            </app-form-field-static>
            <p class="text-sm">Pub<PERSON><PERSON> sur {{ training.catalog }}, mis à jour
                le {{ training.lastUpdate | date:'short' }}</p>
        </td>
    </ng-container>

    <ng-container matColumnDef="trainingActionsOnsite">
        <th mat-header-cell *matHeaderCellDef class="text-center">
            {{ 'private.training-organism.certifications.trainingsActives.onsite' | translate }}
        </th>
        <td *matCellDef="let training" class="text-center w-3 pr-0" mat-cell>
            <a *ngIf="training.activeTrainingActions.onsite.length > 0; else noTrainingActions"
               [href]="training.activeTrainingActions.onsiteLink"
               [matTooltip]="training.activeTrainingActions.onsite.length > 1 ? ('private.training-organism.certifications.trainingsActives.disclaimer' | translate) : null"
               target="_blank" rel="noopener noreferrer">
                {{ training.activeTrainingActions.onsite.length }}
            </a>
            <ng-template #noTrainingActions>
                0
            </ng-template>
        </td>
    </ng-container>

    <ng-container matColumnDef="trainingActionsOnline">
        <th mat-header-cell *matHeaderCellDef class="text-center pr-0">
            {{ 'private.training-organism.certifications.trainingsActives.online' | translate }}
        </th>
        <td *matCellDef="let training" class="text-center w-3 pr-0" mat-cell>
            <a *ngIf="training.activeTrainingActions.online.length > 0; else noTrainingActions"
               [href]="training.activeTrainingActions.onlineLink"
               [matTooltip]="training.activeTrainingActions.online.length > 1 ? ('private.training-organism.certifications.trainingsActives.disclaimer' | translate) : null"
               target="_blank" rel="noopener noreferrer">
                {{ training.activeTrainingActions.online.length }}
            </a>
            <ng-template #noTrainingActions>
                0
            </ng-template>
        </td>
    </ng-container>

    <ng-container matColumnDef="compliance">
        <th mat-header-cell *matHeaderCellDef class="text-center pr-0">
            {{ 'private.training-organism.certifications.trainingsActives.compliance' | translate }}
        </th>
        <td *matCellDef="let training" class="text-center w-3 pr-0" mat-cell>
            <button type="button" mat-icon-button
                    [disabled]="false"
                    [matMenuTriggerFor]="updateCompliance">
                <mat-icon [matTooltip]="getTooltip(training.compliance, training.lastComplianceUpdate)"
                          [color]="training.compliance === trainingComplianceType.NOT_VERIFIED ? undefined : training.compliance === trainingComplianceType.COMPLIANT ? 'primary' : 'warn' ">
                    {{ training.compliance === trainingComplianceType.NOT_VERIFIED ? 'help' : training.compliance === trainingComplianceType.COMPLIANT ? 'check_circle' : 'unpublished' }}
                </mat-icon>
            </button>
            <mat-menu #updateCompliance="matMenu" xPosition="before">
                <ng-template matMenuContent>
                    <button *ngIf="training.compliance !== trainingComplianceType.COMPLIANT"
                            (click)="updateTrainingCompliance(training, trainingComplianceType.COMPLIANT)"
                            type="button"
                            mat-menu-item>
                        <mat-icon color="primary">check_circle</mat-icon>
                        {{ 'private.training-organism.certifications.trainingsActives.complianceType.menu.compliant' | translate }}
                    </button>
                    <button *ngIf="training.compliance !== trainingComplianceType.NOT_COMPLIANT"
                            (click)="updateTrainingCompliance(training, trainingComplianceType.NOT_COMPLIANT)"
                            type="button"
                            mat-menu-item>
                        <mat-icon color="warn">unpublished</mat-icon>
                        {{ 'private.training-organism.certifications.trainingsActives.complianceType.menu.notCompliant' | translate }}
                    </button>
                    <button *ngIf="training.compliance !== trainingComplianceType.NOT_VERIFIED"
                            (click)="updateTrainingCompliance(training, trainingComplianceType.NOT_VERIFIED)"
                            type="button"
                            mat-menu-item>
                        <mat-icon color="accent">help</mat-icon>
                        {{ 'private.training-organism.certifications.trainingsActives.complianceType.menu.notVerified' | translate }}
                    </button>
                </ng-template>
            </mat-menu>
        </td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr mat-row class="mat-row-static" *matRowDef="let row; columns: displayedColumns;"></tr>

</table>
<app-paginator [length]="total"
               [pageSizeOptions]="pageSizeOptions"
               [scrollTopOnPageChange]="false"
               (page)="onPageEvent($event)">
</app-paginator>

