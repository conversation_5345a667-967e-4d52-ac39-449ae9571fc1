import {Component, Injector, Input} from '@angular/core';
import {Observable} from 'rxjs';
import {Training, TrainingComplianceTypes} from '../../api/models/training';
import {PaginatedResponse} from '../../api/services/abstract-paginated.service';
import {AbstractTableComponent} from '../../material/table/abstract-table.component';
import {Certification} from '../../api/models/certification';
import {TrainingService} from '../../api/services/training.service';
import {TranslateService} from '@ngx-translate/core';
import moment from 'moment/moment';

export type RefreshCertificationPartnerTrainings = (limit: number, page: number) => Observable<PaginatedResponse<Training>>;

@Component({
    selector: 'app-certification-partner-trainings-table',
    templateUrl: './certification-partner-trainings-table.component.html',
    styleUrls: ['./certification-partner-trainings-table.component.scss']
})
export class CertificationPartnerTrainingsTableComponent extends AbstractTableComponent<Training> {

    trainingComplianceType = TrainingComplianceTypes;

    @Input() refreshTrainings: RefreshCertificationPartnerTrainings;
    @Input() partnerName: string;
    @Input() certification: Certification;

    constructor(
        private _trainingService: TrainingService,
        private _translateService: TranslateService,
        injector: Injector
    ) {
        super(injector);
        this.displayedColumns = ['name', 'trainingActionsOnsite', 'trainingActionsOnline', 'compliance'];
        this.pageSizeOptions = [7];
    }

    protected refreshData(): Observable<PaginatedResponse<Training>> {
        return this.refreshTrainings(this.paginator.pageSize, this.paginator.pageIndex + 1);
    }

    updateTrainingCompliance(training: Training, compliance: TrainingComplianceTypes): void {
        const trainingToUpdate: Training = {
            ...training, ...{compliance: compliance}
        };
        this._trainingService.update(trainingToUpdate).subscribe(
            (updatedTraining) => {
                this.displayedData = this.displayedData.map((t: Training) => { // t = training pour éviter les problèmes de shadowed variable
                    if (t.externalId === updatedTraining.externalId) {
                        t.compliance = updatedTraining.compliance;
                        t.lastComplianceUpdate = updatedTraining.lastComplianceUpdate;
                        this.getTooltip(t.compliance, updatedTraining.lastComplianceUpdate);
                    }
                    return t;
                });
            }
        );
    }

    getTooltip(compliance: string, lastComplianceUpdate?: Date): string {
        const tooltip = this._translateService.instant('private.training-organism.certifications.trainingsActives.complianceType.' + compliance) + '. ';
        if (lastComplianceUpdate) {
            return tooltip + this._translateService.instant('private.training-organism.certifications.trainingsActives.complianceType.lastUpdate', {
                date: moment(lastComplianceUpdate).format('DD/MM/YYYY (HH:mm)')
            });
        } else {
            return tooltip;
        }
    }
}

