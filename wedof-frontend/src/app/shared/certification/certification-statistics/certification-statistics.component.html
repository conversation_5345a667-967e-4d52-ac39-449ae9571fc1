<mat-card class="flex-col mt-3 p-5 pb-0 pt-3 border shadow-none sm" [ngClass]="{'card-loading':cardLoading}">
    <div class="flex items-center">
        <mat-icon color="primary" class="mr-3 card-loading-show text-4xl">poll</mat-icon>
        <div>
            <div
                class="text-xl font-semibold card-loading-show">{{ cardTitle }}</div>
        </div>
        <div class="ml-auto -mr-4 card-loading-hidden">
            <!-- placeholder for menu -->
        </div>
    </div>
    <div class="flex flex-col" *ngIf="!panelOpenState || cardLoading">
        <div class="grid grid-cols-6 gap-2 mt-2">
            <app-form-field-static class="col-span-2"
                                   *ngIf="certificationStatistics?.takeRate !== null"
                                   label="private.training-organism.certifications.statistiques.takeRate"
                                   [help]="'private.training-organism.certifications.statistiques.' + (showPartner ? 'takeRateHelpPartner' : 'takeRateHelp')"
                                   type="percent"
                                   [value]="roundNumber(certificationStatistics?.takeRate)">
            </app-form-field-static>
            <app-form-field-static class="col-span-2"
                                   *ngIf="certificationStatistics?.successRate !== null"
                                   label="private.training-organism.certifications.statistiques.successRate"
                                   [help]="'private.training-organism.certifications.statistiques.' + (showPartner ? 'successRateHelpPartner' : 'successRateHelp')"
                                   type="percent"
                                   [value]="roundNumber(certificationStatistics?.successRate)">
            </app-form-field-static>
            <app-form-field-static class="col-span-2"
                                   *ngIf="certificationStatistics?.evaluation?.averageRating !== null"
                                   label="private.training-organism.certifications.statistiques.averageRating"
                                   help="private.training-organism.certifications.statistiques.averageRatingHelp"
                                   type="number"
                                   suffix="/ 5"
                                   [value]="certificationStatistics?.evaluation?.averageRating">
            </app-form-field-static>
        </div>
        <treo-message [type]="statSummaryErrors.length ? 'error' : 'info'"
                      [showIcon]="false" class="my-2 card-loading-show" appearance="outline">
            <app-form-field-static icon="info" type="text" [value]="statSummaryText">
            </app-form-field-static>
        </treo-message>
        <button class="flex justify-center mt-2 -mx-5 pt-2 pb-2 open-panel card-loading-hidden" (click)="openPanel()">
            <mat-icon svgIcon="keyboard_arrow_down"></mat-icon>
        </button>
    </div>

    <div class="flex flex-col" *ngIf="panelOpenState && !cardLoading">
        <form [formGroup]="formGroup" class="flex flex-col" *ngIf="certificationStatistics">
            <h5 class="mb-1">{{ 'private.training-organism.certifications.statistiques.foldersTitle' | translate }}</h5>
            <app-form-fields formGroupName="certificationStatistics"
                             class="grid grid-cols-6 gap-2"
                             [entity]="certificationStatistics"
                             [appFormFieldsData]="appFormFieldsDataToTake"
                             [formGroup]="formGroup">
            </app-form-fields>
            <h5 class="mb-1">{{ 'private.training-organism.certifications.statistiques.abortTitle' | translate }}</h5>
            <app-form-fields formGroupName="certificationStatistics"
                             class="grid grid-cols-6 gap-2"
                             [entity]="certificationStatistics"
                             [appFormFieldsData]="appFormFieldsDataAbort"
                             [formGroup]="formGroup">
            </app-form-fields>
            <ng-container *ngIf="showPartner">
                <h5 class="mb-1">{{ 'private.training-organism.certifications.statistiques.ratingTitle' | translate }}</h5>
                <app-form-fields formGroupName="certificationStatistics"
                                 class="grid grid-cols-6 gap-2"
                                 [entity]="certificationStatistics"
                                 [appFormFieldsData]="appFormFieldsDataRatings"
                                 [formGroup]="formGroup">
                </app-form-fields>
                <h5 class="mb-1">{{ 'private.training-organism.certifications.statistiques.trainingsTitle' | translate }}</h5>
                <app-form-fields formGroupName="certificationStatistics"
                                 class="grid grid-cols-6 gap-2"
                                 [entity]="certificationStatistics"
                                 [appFormFieldsData]="appFormFieldsDataTrainings"
                                 [formGroup]="formGroup">
                </app-form-fields>
            </ng-container>
            <h5 class="mb-1">{{ 'private.training-organism.certifications.statistiques.foldersByStateTitle' | translate }}</h5>
            <table>
                <tr *ngFor="let entry of foldersByState">
                    <td class="w-1/2"><p class="{{entry.class}}">{{ entry.label | translate }}</p></td>
                    <td class="w-1/2">{{ entry.value }}</td>
                </tr>
            </table>
            <h5 class="mb-1">{{ 'common.actions.search.funding' | translate }}</h5>
            <table *ngIf="registrationFolderByType.length; else showNoData">
                <tr *ngFor="let rfType of registrationFolderByType">
                    <td class="w-1/2">{{ rfType.label | translate }}</td>
                    <td class="w-1/2">{{ rfType.value }}</td>
                </tr>
            </table>
            <ng-template #showNoData>
                {{'private.training-organism.certifications.statistiques.noDataRegistrationFolder' | translate}}
            </ng-template>
        </form>
        <button class="flex justify-center -mx-5 pt-2 pb-2 close-panel" (click)="closePanel()">
            <mat-icon svgIcon="keyboard_arrow_up"></mat-icon>
        </button>
    </div>
</mat-card>
