import {Component, ElementRef, Input, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy, <PERSON>Chang<PERSON>} from '@angular/core';
import {CertificationPartnerService} from '../../api/services/certification-partner.service';
import {AppFormFieldData} from '../../material/app-form-field/app-form-field.component';
import {FormBuilder, FormGroup} from '@angular/forms';
import {TranslateService} from '@ngx-translate/core';
import {BaseCardComponentDirective, RequiredCallSuper} from '../../utils/base-card/base-card.directive';
import {CertificationStatistics} from '../../api/models/certification-statistics';
import {Observable} from 'rxjs';
import {CertificationService} from '../../api/services/certification.service';

@Component({
    selector: 'app-certification-statistics',
    templateUrl: './certification-statistics.component.html',
    styleUrls: ['./certification-statistics.component.scss']
})
export class CertificationStatisticsComponent extends BaseCardComponentDirective implements OnChanges, On<PERSON><PERSON>roy {
    static COMPONENT_ID = 'statistiques';
    panelOpenState = false;

    formGroup: FormGroup;
    appFormFieldsDataToTake: AppFormFieldData[];
    appFormFieldsDataRatings: AppFormFieldData[];
    appFormFieldsDataTrainings: AppFormFieldData[];
    appFormFieldsDataAbort: AppFormFieldData[];
    foldersByState: { label: string; value: number, class?: string }[];
    statSummaryText: string;
    statSummaryErrors: string[];
    certificationStatistics: CertificationStatistics;
    registrationFolderByType: { label: string; value: number }[];

    @Input() siret?: string;
    @Input() certifInfo: string;
    @Input() cardTitle: string;
    @Input() showPartner: boolean;

    constructor(
        private _certificationPartnerService: CertificationPartnerService,
        private _certificationService: CertificationService,
        private _formBuilder: FormBuilder,
        private _translateService: TranslateService,
        private _el: ElementRef
    ) {
        super(CertificationStatisticsComponent.COMPONENT_ID, _el);
        this.statSummaryErrors = [];
    }

    ngOnChanges(changes: SimpleChanges): void {
        this.panelLoading();
        this.getCertificationStatistics().subscribe((certificationStatistics: CertificationStatistics) => {
            this.certificationStatistics = certificationStatistics;
            if (certificationStatistics) {
                this.initForm(certificationStatistics);
            }
        }).add(() => {
            this.panelLoaded();
        });
    }

    getCertificationStatistics(): Observable<CertificationStatistics> {
        if (!this.certifInfo || (this.showPartner && !this.siret)) {
            this.panelLoaded();
            return;
        }
        if (this.showPartner) {
            return this._certificationPartnerService.getDetails({siret: this.siret, certifInfo: this.certifInfo});
        } else {
            return this._certificationService.getDetails(this.certifInfo);
        }
    }

    roundNumber(numberToConvert?: number, withDecimals = false): any {
        if (numberToConvert == null) {
            return null;
        } else {
            return withDecimals ? (Math.round(numberToConvert * 100) / 100).toFixed(2) : Math.round(numberToConvert);
        }
    }

    protected initForm(certificationStatistics: CertificationStatistics): void {
        const TAKE_RATE_MIN_THRESHOLD = 70;
        const SUCCESS_RATE_MIN_THRESHOLD = 70;
        const ABORT_RATE_MAX_THRESHOLD = 10;
        const AVERAGE_RATING_THRESHOLD = 4;
        const REVIEW_RATE_THRESHOLD = 50;
        this.formGroup = this._formBuilder.group({
            certificationStatistics: this._formBuilder.group({})
        });
        const roundedTakeRate = this.roundNumber(certificationStatistics?.takeRate);
        const roundedTakeRate1Month = this.roundNumber(certificationStatistics?.takeRateAfterDelay['1Month']);
        const roundedTakeRate3Months = this.roundNumber(certificationStatistics?.takeRateAfterDelay['3Months']);
        const roundedTakeRate6Months = this.roundNumber(certificationStatistics?.takeRateAfterDelay['6Months']);
        const roundedSuccessRate = this.roundNumber(certificationStatistics?.successRate);
        const roundedAbortRate = this.roundNumber(certificationStatistics?.abortRate);
        const roundedReviewRate = this.roundNumber(certificationStatistics?.evaluation?.reviewRate);
        const roundedAbortBeforeTraining = this.roundNumber(certificationStatistics?.abortExploded?.beforeTrainingRate);
        const roundedAbortInTraining = this.roundNumber(certificationStatistics?.abortExploded?.inTrainingRate);
        const roundedAbortAfterTraining = this.roundNumber(certificationStatistics?.abortExploded?.afterTrainingRate);
        this.statSummaryErrors = [];
        if (roundedTakeRate == null && roundedSuccessRate == null && roundedAbortRate == null &&
            certificationStatistics?.evaluation?.averageRating == null && roundedReviewRate == null) {
            this.statSummaryText = this._translateService.instant('private.training-organism.certifications.statistiques.summary.unavailable');
        } else {
            if (roundedTakeRate != null && roundedTakeRate < TAKE_RATE_MIN_THRESHOLD) {
                this.statSummaryErrors.push('private.training-organism.certifications.statistiques.takeRate');
            }
            if (roundedSuccessRate != null && roundedSuccessRate < SUCCESS_RATE_MIN_THRESHOLD) {
                this.statSummaryErrors.push('private.training-organism.certifications.statistiques.successRate');
            }
            if (roundedAbortRate != null && roundedAbortRate > ABORT_RATE_MAX_THRESHOLD) {
                this.statSummaryErrors.push('private.training-organism.certifications.statistiques.abortRate');
            }
            if (certificationStatistics?.evaluation?.averageRating != null && certificationStatistics?.evaluation?.averageRating < AVERAGE_RATING_THRESHOLD) {
                this.statSummaryErrors.push('private.training-organism.certifications.statistiques.averageRating');
            }
            if (roundedReviewRate != null && roundedReviewRate < REVIEW_RATE_THRESHOLD) {
                this.statSummaryErrors.push('private.training-organism.certifications.statistiques.reviewRate');
            }
            if (this.statSummaryErrors.length) {
                this.statSummaryText = this._translateService.instant('private.training-organism.certifications.statistiques.summary.issue') + ' ' +
                    this.statSummaryErrors.map((errorField) => this._translateService.instant(errorField)).join(', ');
            } else {
                this.statSummaryText = this._translateService.instant('private.training-organism.certifications.statistiques.summary.good');
            }
        }
        this.appFormFieldsDataToTake = [
            {
                controlName: 'takeRate',
                value: roundedTakeRate,
                disabled: true,
                label: 'private.training-organism.certifications.statistiques.takeRate',
                help: 'private.training-organism.certifications.statistiques.' + (this.showPartner ? 'takeRateHelpPartner' : 'takeRateHelp'),
                type: 'percent'
            },
            {
                controlName: 'takeRate1Month',
                value: roundedTakeRate1Month,
                disabled: true,
                label: 'private.training-organism.certifications.statistiques.takeRate1Month',
                help: 'private.training-organism.certifications.statistiques.takeRateHelp1Month',
                type: 'percent',
                colSpan: 2
            },
            {
                controlName: 'takeRate3Month',
                value: roundedTakeRate3Months,
                disabled: true,
                label: 'private.training-organism.certifications.statistiques.takeRate3Months',
                help: 'private.training-organism.certifications.statistiques.takeRateHelp3Months',
                type: 'percent',
                colSpan: 2
            },
            {
                controlName: 'takeRate6Month',
                value: roundedTakeRate6Months,
                disabled: true,
                label: 'private.training-organism.certifications.statistiques.takeRate6Months',
                help: 'private.training-organism.certifications.statistiques.takeRateHelp6Months',
                type: 'percent',
                colSpan: 2
            },
            {
                controlName: 'successRate',
                value: roundedSuccessRate,
                disabled: true,
                label: 'private.training-organism.certifications.statistiques.successRate',
                help: 'private.training-organism.certifications.statistiques.' + (this.showPartner ? 'successRateHelpPartner' : 'successRateHelp'),
                type: 'percent'
            }
        ];
        this.appFormFieldsDataAbort = [
            {
                controlName: 'abortRate',
                value: roundedAbortRate,
                disabled: true,
                label: 'private.training-organism.certifications.statistiques.abortRate',
                help: 'private.training-organism.certifications.statistiques.' + (this.showPartner ? 'abortRateHelpPartner' : 'abortRateHelp'),
                type: 'percent'
            },
            {
                controlName: 'abortRateBeforeTraining',
                value: roundedAbortBeforeTraining,
                disabled: true,
                label: 'private.training-organism.certifications.statistiques.abortRateBeforeTraining',
                type: 'percent',
                colSpan: 2
            },
            {
                controlName: 'abortRateInTraining',
                value: roundedAbortInTraining,
                disabled: true,
                label: 'private.training-organism.certifications.statistiques.abortRateInTraining',
                type: 'percent',
                colSpan: 2
            },
            {
                controlName: 'abortRateAfterTraining',
                value: roundedAbortAfterTraining,
                disabled: true,
                label: 'private.training-organism.certifications.statistiques.abortRateAfterTraining',
                type: 'percent',
                colSpan: 2
            }
        ];
        if (this.showPartner) {
            this.appFormFieldsDataRatings = [
                {
                    controlName: 'averageRating',
                    value: certificationStatistics?.evaluation?.averageRating,
                    disabled: true,
                    label: 'private.training-organism.certifications.statistiques.averageRating',
                    help: 'private.training-organism.certifications.statistiques.averageRatingHelp',
                    type: 'number',
                    suffix: '/ 5',
                    colSpan: 2
                },
                {
                    controlName: 'reviewRate',
                    value: roundedReviewRate,
                    disabled: true,
                    label: 'private.training-organism.certifications.statistiques.reviewRate',
                    help: 'private.training-organism.certifications.statistiques.reviewRateHelp',
                    type: 'percent',
                    colSpan: 2
                },
                {
                    controlName: 'reviewCount',
                    value: certificationStatistics?.evaluation?.reviewCount,
                    disabled: true,
                    label: 'private.training-organism.certifications.statistiques.reviewCount',
                    help: 'private.training-organism.certifications.statistiques.reviewCountHelp',
                    type: 'number',
                    colSpan: 2
                }
            ];
            this.appFormFieldsDataTrainings = [
                {
                    controlName: 'minPricing',
                    value: this.roundNumber(certificationStatistics?.pricing.min),
                    disabled: true,
                    label: 'private.training-organism.certifications.statistiques.minPricing',
                    help: 'private.training-organism.certifications.statistiques.minPricingHelp',
                    type: 'price',
                    colSpan: 2
                },
                {
                    controlName: 'maxPricing',
                    value: this.roundNumber(certificationStatistics?.pricing.max),
                    disabled: true,
                    label: 'private.training-organism.certifications.statistiques.maxPricing',
                    help: 'private.training-organism.certifications.statistiques.maxPricingHelp',
                    type: 'price',
                    colSpan: 2
                },
                {
                    controlName: 'averagePricing',
                    value: this.roundNumber(certificationStatistics?.pricing.average),
                    disabled: true,
                    label: 'private.training-organism.certifications.statistiques.averagePricing',
                    help: 'private.training-organism.certifications.statistiques.averagePricingHelp',
                    type: 'price',
                    colSpan: 2
                },
                {
                    controlName: 'minDuration',
                    value: certificationStatistics?.duration.min,
                    disabled: true,
                    label: 'private.training-organism.certifications.statistiques.minDuration',
                    help: 'private.training-organism.certifications.statistiques.minDurationHelp',
                    type: 'number',
                    suffix: 'heures',
                    colSpan: 2
                },
                {
                    controlName: 'maxDuration',
                    value: certificationStatistics?.duration.max,
                    disabled: true,
                    label: 'private.training-organism.certifications.statistiques.maxDuration',
                    help: 'private.training-organism.certifications.statistiques.maxDurationHelp',
                    type: 'number',
                    suffix: 'heures',
                    colSpan: 2
                },
                {
                    controlName: 'averageDuration',
                    value: this.roundNumber(certificationStatistics?.duration.average),
                    disabled: true,
                    label: 'private.training-organism.certifications.statistiques.averageDuration',
                    help: 'private.training-organism.certifications.statistiques.averageDurationHelp',
                    type: 'number',
                    suffix: 'heures',
                    colSpan: 2
                }
            ];
        }
        this.foldersByState = [];
        this.foldersByState.push({
            label: 'private.common.total',
            class: 'font-semibold',
            value: certificationStatistics?.foldersTotal,
        });
        ['toRegister', 'registered', 'toTake', 'toControl', 'success', 'toRetake', 'failed', 'refused', 'aborted'].forEach((stateName) => {
            this.foldersByState.push({
                label: 'private.training-organism.certifications.statistiques.' + stateName + 'Count',
                value: certificationStatistics?.countByState[stateName],
            });
        });
        this.registrationFolderByType = [];
        if (certificationStatistics?.countRegistrationFolderByType) {
            Object.entries(certificationStatistics?.countRegistrationFolderByType).forEach((registrationFolderByType) => {
                const type = registrationFolderByType[0] === 'none' ? 'common.actions.noFunding' : 'private.training-organism.folders.common.type.' + registrationFolderByType[0];
                this.registrationFolderByType.push({
                    label: type,
                    value: registrationFolderByType[1]
                });
            });
        }
    }

    openPanel(): void {
        this.panelOpenState = true;
    }

    closePanel(): void {
        this.panelOpenState = false;
    }


    ngOnDestroy(): RequiredCallSuper {
        return super.ngOnDestroy();
    }

}
