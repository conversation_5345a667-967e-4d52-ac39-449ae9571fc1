import {CommonModule} from '@angular/common';
import {NgModule} from '@angular/core';
import {TranslateModule} from '@ngx-translate/core';
import {MaterialModule} from '../material/material.module';
import {CertificationSummaryComponent} from './certification-summary/certification-summary.component';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {RouterModule} from '@angular/router';
import {OrganismModule} from '../organism/organism.module';
import {MarkdownModule} from 'ngx-markdown';
import {CertificationPartnersTableComponent} from './certification-partners-table/certification-partners-table.component';
import {CertificationCertifierAccessCardComponent} from './certification-certifier-access-card/certification-certifier-access-card.component';
import {CertificationPartnerAccessCardComponent} from './certification-partner-access-card/certification-partner-access-card.component';
import {MatCardModule} from '@angular/material/card';
import {PipesModule} from '../pipes/pipes.module';
import {CertificationStatisticsComponent} from './certification-statistics/certification-statistics.component';
import {MatTooltipModule} from '@angular/material/tooltip';
import {CertificationPartnerTrainingsTableComponent} from './certification-partner-trainings-table/certification-partner-trainings-table.component';
import {CertificationCatalogCardComponent} from './certification-catalog-card/certification-catalog-card.component';
import {FileModule} from '../file/file.module';
import {MatChipsModule} from '@angular/material/chips';
import {DialogInvitationConfirmComponent} from './certification-catalog-button-invite/dialog-invitation-confirm/dialog-invitation-confirm.component';
import {CertificationAccrochageComponent} from './certification-accrochage/certification-accrochage.component';
import {CertificationCertifierPartnershipCardComponent} from './certification-certifier-partnership-card/certification-certifier-partnership-card.component';
import {CertificationPartnerPartnershipCardComponent} from './certification-partner-partnership-card/certification-partner-partnership-card.component';
import {CertificationAccessManagementDialogComponent} from './abstract-certification-access-management/certification-access-management-dialog/certification-access-management-dialog.component';
import {InvitationModule} from '../invitation/invitation.module';
import {CertificationCertifierPartnershipMenuComponent} from './certification-certifier-partnership-menu/certification-certifier-partnership-menu.component';
import {CertificationCatalogButtonInviteComponent} from './certification-catalog-button-invite/certification-catalog-button-invite.component';
import {CertificationCatalogButtonNewComponent} from './certification-catalog-button-new/certification-catalog-button-new.component';
import {CertificationCatalogButtonRequestComponent} from './certification-catalog-button-request/certification-catalog-button-request.component';
import {DialogPartnershipRequestComponent} from './certification-catalog-button-request/dialog-partnership-request/dialog-partnership-request.component';
import {CertificationManagePartnershipComponent} from './certification-manage-partnership/certification-manage-partnership.component';
import {CertificationManageCertificationFoldersComponent} from './certification-manage-certification-folders/certification-manage-certification-folders.component';
import {ActivitiesModule} from '../activities/activities.module';
import {ClipboardModule} from 'ngx-clipboard';
import {CertificationCertificateTemplateComponent} from './certification-certificate-template/certification-certificate-template.component';
import {CertificationPartnerTrainingsComponent} from './certification-partner-trainings/certification-partner-trainings.component';
import {SubscriptionModule} from '../subscription/subscription.module';
import {MatButtonToggleModule} from '@angular/material/button-toggle';
import {CertificationCatalogButtonUpdateStateComponent} from './certification-catalog-button-update-state/certification-catalog-button-update-state.component';
import {StringModule} from '../utils/string.module';
import {CertificationSkillsComponent} from './certification-skills/certification-skills.component';
import {InfiniteScrollModule} from 'ngx-infinite-scroll';
import {SkillModule} from '../skill/skill.module';
import {CertificationSurveyComponent} from './certification-survey/certification-survey.component';
import {DialogCertificationAuditTemplateCriteriasComponent} from './certification-audit-template/dialog-certification-audit-template-criterias/dialog-certification-audit-template-criterias.component';
import {CertificationAuditPartnerComponent} from './certification-audit-partner/certification-audit-partner.component';
import {CertificationAuditCriteriaFormComponent} from './certification-audit-template/certification-audit-criteria-form/certification-audit-criteria-form.component';
import {CertificationAuditCriteriaMenuComponent} from './certification-audit-template/certification-audit-criteria-menu/certification-audit-criteria-menu.component';
import {DialogCertificationAuditCompleteComponent} from './certification-audit-partner/dialog-certification-audit-complete/dialog-certification-audit-complete.component';
import {DialogCertificationAuditTemplateComponent} from './certification-audit-template/dialog-certification-audit-template/dialog-certification-audit-template.component';
import {CertificationAuditTemplateComponent} from './certification-audit-template/certification-audit-template.component';
import {CertificationAuditTemplateMenuComponent} from './certification-audit-template/certification-audit-template-menu/certification-audit-template-menu.component';
import {DialogCertificationAuditCreateComponent} from './certification-audit-partner/dialog-certification-audit-create/dialog-certification-audit-create.component';
import {DialogCertificationAuditDetailsComponent} from './certification-audit-partner/dialog-certification-audit-details/dialog-certification-audit-details.component';
import {CertificationAuditPartnerMenuComponent} from './certification-audit-partner/certification-audit-partner-menu/certification-audit-partner-menu.component';
import {CertificationCreateComponent} from './certification-create/certification-create.component';
import {DialogCertificationAuditTemplateAutomaticComponent} from './certification-audit-template/dialog-certification-audit-template-automatic/dialog-certification-audit-template-automatic.component';
import {DialogTrainAIComponent} from './certification-audit-template/dialog-train-ai/dialog-train-ai.component';

@NgModule({
    declarations: [
        CertificationAccessManagementDialogComponent,
        CertificationSummaryComponent,
        CertificationCertifierAccessCardComponent,
        CertificationCertifierPartnershipCardComponent,
        CertificationPartnerAccessCardComponent,
        CertificationPartnerPartnershipCardComponent,
        CertificationPartnersTableComponent,
        CertificationPartnerTrainingsTableComponent,
        CertificationStatisticsComponent,
        CertificationCatalogCardComponent,
        CertificationCatalogButtonInviteComponent,
        CertificationCatalogButtonNewComponent,
        CertificationCatalogButtonRequestComponent,
        CertificationCatalogButtonUpdateStateComponent,
        CertificationAccrochageComponent,
        DialogInvitationConfirmComponent,
        DialogPartnershipRequestComponent,
        CertificationCertifierPartnershipMenuComponent,
        CertificationManagePartnershipComponent,
        CertificationManageCertificationFoldersComponent,
        CertificationCertificateTemplateComponent,
        CertificationPartnerTrainingsComponent,
        CertificationSkillsComponent,
        CertificationSurveyComponent,
        DialogCertificationAuditTemplateCriteriasComponent,
        CertificationAuditPartnerComponent,
        DialogCertificationAuditCompleteComponent,
        CertificationAuditCriteriaFormComponent,
        CertificationAuditCriteriaMenuComponent,
        CertificationAuditTemplateComponent,
        CertificationAuditTemplateMenuComponent,
        DialogCertificationAuditTemplateComponent,
        DialogCertificationAuditCreateComponent,
        DialogCertificationAuditDetailsComponent,
        CertificationAuditPartnerMenuComponent,
        CertificationCreateComponent,
        DialogTrainAIComponent,
        DialogCertificationAuditTemplateAutomaticComponent
    ],
    imports: [
        CommonModule,
        ClipboardModule,
        MaterialModule,
        TranslateModule,
        ReactiveFormsModule,
        FormsModule,
        RouterModule,
        OrganismModule,
        MarkdownModule,
        MatCardModule,
        PipesModule,
        MatTooltipModule,
        FileModule,
        StringModule,
        MatChipsModule,
        InvitationModule,
        ActivitiesModule,
        SubscriptionModule,
        MatButtonToggleModule,
        InfiniteScrollModule,
        SkillModule
    ],
    exports: [
        CertificationSummaryComponent,
        CertificationCertifierAccessCardComponent,
        CertificationCertifierPartnershipCardComponent,
        CertificationPartnerAccessCardComponent,
        CertificationPartnerPartnershipCardComponent,
        CertificationPartnersTableComponent,
        CertificationStatisticsComponent,
        CertificationCatalogCardComponent,
        CertificationCatalogButtonInviteComponent,
        CertificationAccrochageComponent,
        CertificationManagePartnershipComponent,
        CertificationManageCertificationFoldersComponent,
        CertificationCertificateTemplateComponent,
        CertificationPartnerTrainingsComponent,
        CertificationSkillsComponent,
        CertificationSurveyComponent,
        DialogCertificationAuditTemplateCriteriasComponent,
        CertificationAuditPartnerComponent,
        CertificationAuditTemplateComponent,
        CertificationCreateComponent
    ]
})
export class CertificationModule {
}
