<mat-card class="flex-col mt-3 p-5 pb-0 pt-3 border shadow-none sm" [ngClass]="{'card-loading':cardLoading}">
    <div class="flex items-center mb-2">
        <mat-icon class="mr-3 card-loading-show text-4xl"
                  title="{{ 'private.certification.partners.table.accessState.' + certificationPartner.certifierAccessState | translate }}"
                  [ngClass]="certificationPartner.certifierAccessState | certifierAccessStateToColor">
            {{certificationPartner.certifierAccessState | certifierAccessStateToIcon}}
        </mat-icon>
        <div>
            <div>
                <div
                    class="text-xl font-semibold card-loading-show">{{ 'private.certification.partners.table.certifierAccess' | translate }}</div>
                <div class="text-secondary text-md card-loading-hidden">
                    {{ 'private.certification.partners.table.certifierAccessSubtitle' | translate: certificationPartner._links.partner }}
                    <a [routerLink]="['/aide/guides/certificateurs/demander-autorisation-partenaire']">(documentation)</a>
                </div>
            </div>
        </div>
        <div class="ml-auto -mr-4 card-loading-hidden">
            <!-- placeholder for menu -->
        </div>
    </div>
    <div class="mt-2 mb-3 card-loading-hidden"
         *ngIf="certifierAccess">
        {{ 'private.certification.partners.table.access' | translate }}
        <b>{{ ('private.certification.partners.table.accessState.' + certifierAccess.state) | translate | lowercase }}</b>
        <ng-container
            *ngIf="(certifierAccess.state === certifierAccessStates.WAITING
                || certifierAccess.state === certifierAccessStates.ACCEPTED
                || certifierAccess.state === certifierAccessStates.REFUSED) && certifierAccess.fullName && certifierAccess.email">
            par {{ certifierAccess.fullName }} ({{certifierAccess.email}})
        </ng-container>
        {{ 'common.actions.search.sinceDate' | translate: {
        date: (certifierAccess.state === certifierAccessStates.WAITING ? certifierAccess.createdOn :
            certifierAccess.state === certifierAccessStates.TERMINATED || certifierAccess.state === certifierAccessStates.REFUSED ?
                certifierAccess.terminatedOn : certifierAccess.state === certifierAccessStates.ACCEPTED && certifierAccess.activatedOn) | date:'mediumDate'
    } }}
        <span *ngIf="certifierAccess.state === certifierAccessStates.WAITING && certifierAccess.inviteLink">
                (<span class="underline cursor-pointer"
                       ngxClipboard
                       [cbContent]="certifierAccess.inviteLink"
                       (cbOnSuccess)="onInviteLinkCopySuccess(certifierAccess.inviteLink)">{{ 'private.certification.partners.table.actions.copyInviteLink' | translate }}</span>)
            </span>
    </div>
    <div class="mt-2 mb-3 text-secondary card-loading-hidden"
         *ngIf="!certifierAccess">
        {{ 'private.certification.partners.table.no-access' | translate }}
    </div>
    <ng-container *ngIf="certification.type !== certificationTypes.INTERNAL">
        <div class="mt-2 mb-3 card-loading-hidden" *ngIf="certificationPartner.connections?.length">
            <div class="flex justify-between mb-1">
                <p class="font-bold">{{'private.certification.partners.table.connectionState.tableCard.name' | translate}}</p>
                <p class="font-bold">{{'private.certification.partners.table.connectionState.tableCard.state' | translate}}</p>
            </div>
            <div *ngFor="let connection of certificationPartner.connections" class="flex justify-between mb-1">
                <p>{{'auth.' + connection.name + '.name' | translate}}</p>
                <div [matTooltip]="connection.failedAt && 'private.certification.partners.table.connectionState.tableCard.failedAt' | translate : {
                  date : connection.failedAt | date  : 'dd/MM/yyyy'
                }" class="flex">
                    <p>{{'private.certification.partners.table.connectionState.state.' + connection.state | translate}}</p>
                    <mat-icon class="ml-2" *ngIf="connection.failedAt" svgIcon="info"></mat-icon>
                </div>
            </div>
        </div>
    </ng-container>
    <div
        class="flex items-center justify-end border-t -mx-5 px-5 py-3 light:bg-cool-gray-50 dark:bg-cool-gray-700 card-loading-hidden">
        <button mat-flat-button
                [disabled]="loading"
                *ngIf="certificationPartner.certifierAccessState !== certifierAccessStates.ACCEPTED"
                (click)="requestCertifierAccess()"
                color="primary"
                [class]="certificationPartner.certifierAccessState === certifierAccessStates.WAITING ? 'button-actions' : ''"
                type="button">
            <ng-container
                *ngIf="certificationPartner.certifierAccessState === certifierAccessStates.NONE
                || certificationPartner.certifierAccessState === certifierAccessStates.TERMINATED; else showOther">
                {{'private.certification.partners.table.actions.askAccess' | translate}}
            </ng-container>
            <ng-template #showOther>
                {{ 'private.certification.partners.table.actions.askAccessAgain' | translate }}
            </ng-template>
        </button>
        <button mat-flat-button
                [disabled]="loading"
                *ngIf="certificationPartner.certifierAccessState === certifierAccessStates.ACCEPTED"
                (click)="revokedCertifierAccess('revoked')"
                color="warn"
                type="button">
            {{ 'private.certification.partners.table.actions.revoked.action' | translate }}
        </button>
        <button class="flex justify-center items-center button-arrow button-arrow-primary"
                color="primary"
                *ngIf="certificationPartner.certifierAccessState === certifierAccessStates.WAITING"
                (click)="$event.stopPropagation()"
                mat-flat-button
                [disabled]="loading"
                [matMenuTriggerFor]="actionsMenu"
                title="Actions" type="button">
            <mat-icon class="icon" svgIcon="arrow_drop_down"></mat-icon>
        </button>
        <mat-menu #actionsMenu="matMenu" class="large-menu">
            <ng-template matMenuContent>
                <button mat-menu-item
                        type="button"
                        (click)="requestCertifierAccess()">
                    {{ 'private.certification.partners.table.actions.askAccessAgain' | translate }}
                </button>
                <button mat-menu-item
                        type="button"
                        (click)="revokedCertifierAccess('cancel')">
                    {{ 'private.certification.partners.table.actions.cancel.action' | translate }}
                </button>
            </ng-template>
        </mat-menu>
    </div>
</mat-card>
<ng-template #copySuccess>
    {{ 'private.certification.partners.table.actions.copyInviteLinkSuccess' | translate }}
</ng-template>
