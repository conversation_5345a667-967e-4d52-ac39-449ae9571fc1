import {
    Component,
    ElementRef,
    EventEmitter,
    Input,
    OnChanges,
    OnDestroy,
    Output,
    SimpleChanges,
    TemplateRef,
    ViewChild
} from '@angular/core';
import {FormGroup} from '@angular/forms';
import {CertificationPartner} from '../../api/models/certification-partner';
import {Organism} from '../../api/models/organism';
import {CertificationCertifierAccessDialogFormComponent} from '../../../private/certification/partners/certification-certifier-access-dialog-form/certification-certifier-access-dialog-form.component';
import {CertifierAccess, CertifierAccessState} from '../../api/models/certifier-access';
import {MatDialog} from '@angular/material/dialog';
import {CertificationService} from '../../api/services/certification.service';
import {MatSnackBar} from '@angular/material/snack-bar';
import {BaseCardComponentDirective, RequiredCallSuper} from '../../utils/base-card/base-card.directive';
import {Certification, CertificationTypes} from '../../api/models/certification';
import {DeletionConfirmationComponent} from '../../material/action-confirmation/deletion-confirmation.component';
import {filter, finalize, switchMap, tap} from 'rxjs/operators';
import {CertifierAccessService} from '../../api/services/certifier-access.service';

@Component({
    selector: 'app-certification-certifier-access-card',
    templateUrl: './certification-certifier-access-card.component.html',
    styleUrls: ['./certification-certifier-access-card.component.scss']
})
export class CertificationCertifierAccessCardComponent extends BaseCardComponentDirective implements OnChanges, OnDestroy {
    static COMPONENT_ID = 'donnees';
    formGroup: FormGroup;
    certifierAccessStates = CertifierAccessState;
    certificationTypes = CertificationTypes;
    loading = false;

    @Input() partner: Organism;
    @Input() certificationPartner: CertificationPartner;
    @Input() certification: Certification;
    @Input() certifierAccess?: CertifierAccess;
    @Output() certifierAccessChange: EventEmitter<CertifierAccess> = new EventEmitter<CertifierAccess>();
    @ViewChild('copySuccess') copySuccessTemplate: TemplateRef<any>;

    constructor(
        private _dialog: MatDialog,
        private _certifierAccessService: CertifierAccessService,
        private _snackBar: MatSnackBar,
        private _certificationService: CertificationService,
        private _el: ElementRef
    ) {
        super(CertificationCertifierAccessCardComponent.COMPONENT_ID, _el);
    }

    ngOnChanges(changes: SimpleChanges): void {
        this.panelLoading();
        this.initForm(this.certificationPartner);
    }

    ngOnDestroy(): RequiredCallSuper {
        return super.ngOnDestroy();
    }

    protected initForm(certificationPartner: CertificationPartner): void {
        if (!certificationPartner || (certificationPartner.certifierAccessState !== this.certifierAccessStates.NONE && !this.certifierAccess)) {
            return;
        }
        this.panelLoaded();
    }

    requestCertifierAccess(): void {
        const organism = this.partner;
        const certification$ = this._certificationService.get(this.certificationPartner._links.certification.certifInfo);
        certification$.subscribe(certification => {
            const dialogRef = this._dialog.open(CertificationCertifierAccessDialogFormComponent, {
                disableClose: true,
                panelClass: 'full-page-scroll-40',
                height: 'auto',
                data: {
                    certification,
                    organism
                }
            });
            dialogRef.componentInstance.submitted$.subscribe((certifierAccess: CertifierAccess) => {
                this.certificationPartner.certifierAccessState = certifierAccess.state;
                this.certifierAccessChange.emit(certifierAccess); // This will trigger change of certifierAccess Input
            });
        });
    }

    revokedCertifierAccess(title: string): void {
        const dialogRef = this._dialog.open(DeletionConfirmationComponent, {
            panelClass: 'full-page-scroll-40',
            height: 'auto',
            data: {
                messageKey: 'private.certification.partners.table.actions.' + title + '.messageKey',
                title: 'private.certification.partners.table.actions.' + title + '.action'
            }
        });
        dialogRef.componentInstance.actionValue$.pipe(
            filter(confirmation => confirmation),
            tap(() => this.loading = true),
            switchMap(() => this._certifierAccessService.terminate(this.certifierAccess)),
            finalize(() => {
                dialogRef.componentInstance.close();
                this.loading = false;
            })
        ).subscribe((certifierAccess) => {
            this.certificationPartner.certifierAccessState = certifierAccess.state;
            this.certifierAccessChange.emit(certifierAccess);
        });
    }

    onInviteLinkCopySuccess(value: string): any {
        this._snackBar.openFromTemplate(this.copySuccessTemplate, {
            verticalPosition: 'top',
            horizontalPosition: 'center',
            duration: 5000,
        });
        return value;
    }
}
