import {Component, ElementRef, Input, OnChanges, SimpleChanges} from '@angular/core';
import {BaseCardComponentDirective} from '../../utils/base-card/base-card.directive';
import {SnackBarComponent} from '../../material/snack-bar/snack-bar.component';
import {displayTextSnackBar} from '../../utils/displayTextSnackBar';
import {MatSnackBar} from '@angular/material/snack-bar';
import {Certification} from '../../api/models/certification';
import {TranslateService} from '@ngx-translate/core';
import {FileService} from '../../api/services/file.service';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../errors/errors.types';
import {CertificationFolderSurveyService} from '../../api/services/certification-folder-survey.service';
import {CertificationFolderSurveyDetails} from '../../api/models/certification-folder-survey';
import {AppFormFieldData} from '../../material/app-form-field/app-form-field.component';
import {FormBuilder, FormGroup} from '@angular/forms';
import {DialogUpgradeSubscriptionComponent} from '../../subscription/dialog-upgrade-subscription/dialog-upgrade-subscription.component';
import {Subscription, SubscriptionTypes} from '../../api/models/subscription';
import {Organism} from '../../api/models/organism';
import {MatDialog} from '@angular/material/dialog';

@Component({
    selector: 'app-certification-survey',
    templateUrl: './certification-survey.component.html',
    styleUrls: ['./certification-survey.component.scss']
})
export class CertificationSurveyComponent extends BaseCardComponentDirective implements OnChanges {

    static COMPONENT_ID = 'enquete';
    loading = false;
    errorMessages: string[] = [];
    formGroup: FormGroup;
    appFormFieldsDataSurveyDetails: AppFormFieldData[];
    surveyDetails: CertificationFolderSurveyDetails;
    canExport = false;

    @Input() certification: Certification;
    @Input() organism: Organism;
    @Input() subscription: Subscription;

    constructor(private _el: ElementRef,
                private _translateService: TranslateService,
                private _formBuilder: FormBuilder,
                private _certificationFolderSurveyService: CertificationFolderSurveyService,
                private _fileService: FileService,
                private _dialog: MatDialog,
                private _snackBar: MatSnackBar) {
        super(CertificationSurveyComponent.COMPONENT_ID, _el);
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes.certification) {
            this.errorMessages = [];
            this.panelLoading();
            this.initForm();
        }
    }

    initForm(): void {
        this.loading = true;
        this._certificationFolderSurveyService.details(this.certification.certifInfo).subscribe((surveyDetails) => {
            this.surveyDetails = surveyDetails;
            this.formGroup = this._formBuilder.group({
                certificationFolderSurveyDetails: this._formBuilder.group({})
            });
            this.appFormFieldsDataSurveyDetails = [
                {
                    controlName: 'created',
                    value: this.subscription.allowCertifierPlus ? [surveyDetails.total] : surveyDetails.total,
                    disabled: true,
                    label: 'private.certification.surveys.statistiques.created',
                    navigateCommands: ['/', 'certification', 'dossiers', 'kanban'],
                    navigateQueryParams: [{
                        state: 'registered,toTake,toControl,toRetake,failed,success',
                        certifInfo: this.certification.certifInfo
                    }],
                    type: this.subscription.allowCertifierPlus ? 'url-html' : 'text',
                    colSpan: 3
                },
                {
                    controlName: 'beforeCertificationSuccess',
                    value: this.subscription.allowCertifierPlus ?
                        [surveyDetails.beforeCertificationSuccess, surveyDetails.total] : surveyDetails.beforeCertificationSuccess + ' / ' + surveyDetails.total,
                    disabled: true,
                    label: 'private.certification.surveys.statistiques.beforeCertificationSuccess',
                    navigateCommands: ['/', 'certification', 'dossiers', 'kanban'],
                    navigateQueryParams: [{
                        survey: 'initialExperienceAnsweredDate',
                        certifInfo: this.certification.certifInfo
                    }, {
                        state: 'registered,toTake,toControl,toRetake,failed,success',
                        certifInfo: this.certification.certifInfo
                    }],
                    type: this.subscription.allowCertifierPlus ? 'url-html' : 'text',
                    colSpan: 3
                },
                {
                    controlName: 'sixMonths',
                    value: this.subscription.allowCertifierPlus ?
                        [surveyDetails.afterSixMonthsCertificationSuccess, surveyDetails.canAnswerSixMonths] :
                        surveyDetails.afterSixMonthsCertificationSuccess + ' / ' + surveyDetails.canAnswerSixMonths,
                    disabled: true,
                    class: 'mt-4',
                    navigateCommands: ['/', 'certification', 'dossiers', 'kanban'],
                    navigateQueryParams: [{
                        survey: 'sixMonthExperienceAnsweredDate',
                        certifInfo: this.certification.certifInfo
                    }, {
                        survey: 'sixMonthExperienceStartDate',
                        certifInfo: this.certification.certifInfo
                    }],
                    label: 'private.certification.surveys.statistiques.sixMonths',
                    help: this._translateService.instant('private.certification.surveys.statistiques.help', {
                        totalCanAnswer: surveyDetails.canAnswerSixMonths
                    }),
                    type: this.subscription.allowCertifierPlus ? 'url-html' : 'text',
                    colSpan: 3
                },
                {
                    controlName: 'finished',
                    value: this.subscription.allowCertifierPlus ?
                        [surveyDetails.finished, surveyDetails.canAnswerLongTerm] :
                        surveyDetails.finished + ' / ' + surveyDetails.canAnswerLongTerm,
                    disabled: true,
                    navigateCommands: ['/', 'certification', 'dossiers', 'kanban'],
                    navigateQueryParams: [{
                        survey: 'longTermExperienceAnsweredDate',
                        certifInfo: this.certification.certifInfo
                    }, {
                        survey: 'longTermExperienceStartDate',
                        certifInfo: this.certification.certifInfo
                    }],
                    class: 'mt-2',
                    label: 'private.certification.surveys.statistiques.finished',
                    help: this._translateService.instant('private.certification.surveys.statistiques.help', {
                        totalCanAnswer: surveyDetails.canAnswerLongTerm
                    }),
                    type: this.subscription.allowCertifierPlus ? 'url-html' : 'text',
                    colSpan: 3,
                }
            ];
            this.canExport = this.subscription.allowCertifierPlus ?
                (surveyDetails.beforeCertificationSuccess + surveyDetails.afterSixMonthsCertificationSuccess + surveyDetails.finished) > 0 : true;
            this.loading = false;
        });
    }

    export(): void {
        if (this.subscription.allowCertifierPlus) {
            this.errorMessages = [];
            this.loading = true;
            this._fileService.download('/api/surveys/export/' + this.certification.certifInfo, 'franceCompetences').subscribe(
                (blob) => {
                    if (blob.state === 'PENDING') {
                        this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(
                            this._translateService.instant('common.actions.export.exportStarted'), 0));
                    } else if (blob.state === 'DONE') {
                        this.loading = false;
                        this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(
                            this._translateService.instant('common.actions.export.exportFinished')));
                    }
                }, (httpErrorResponse: HttpErrorResponse) => {
                    this.loading = false;
                    this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(
                        this._translateService.instant('common.actions.error'), 1000, 'red'));
                    this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
                }
            );
        } else {
            this._dialog.open(DialogUpgradeSubscriptionComponent, {
                panelClass: ['full-page-scroll-50'],
                data: {
                    organism: this.organism,
                    subscription: this.subscription,
                    fromPage: 'surveys',
                    subscriptionTypeToShow: SubscriptionTypes.CERTIFIER
                }
            });
        }
    }

}
