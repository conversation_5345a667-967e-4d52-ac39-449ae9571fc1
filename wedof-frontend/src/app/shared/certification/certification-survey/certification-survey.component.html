<mat-card class="flex-col mt-3 p-5 pb-0 pt-3 border shadow-none sm">
    <div class="flex items-center mb-2">
        <mat-icon class="mr-3 card-loading-show icon-size-32" color="primary">view_timeline</mat-icon>
        <div class="flex items-center">
            <span
                class="text-xl font-semibold card-loading-show">{{ 'private.certification.surveys.title' | translate }}</span>
        </div>
    </div>
    <div class="flex flex-col mb-2">
        <p class="mb-2 card-loading-show">{{ 'private.certification.surveys.subtitle' | translate }}</p>
        <ul class="mb-4 p-4 card-loading-show list-disc">
            <li>{{ 'private.certification.surveys.explanation.initial' | translate }} </li>
            <li>{{ 'private.certification.surveys.explanation.sixMonth' | translate }} </li>
            <li>{{ 'private.certification.surveys.explanation.longTerm' | translate }} </li>
        </ul>
        <form [formGroup]="formGroup" class="flex flex-col" *ngIf="surveyDetails">
            <app-form-fields formGroupName="certificationFolderSurveyDetails"
                             class="grid grid-cols-6 gap-2 mb-4 card-loading-show"
                             [entity]="surveyDetails"
                             [appFormFieldsData]="appFormFieldsDataSurveyDetails"
                             [formGroup]="formGroup">
            </app-form-fields>
        </form>
        <div
            class="flex items-center mt-2 justify-end border-t pl-5 pr-10 -mx-5 py-3 light:bg-cool-gray-50 dark:bg-cool-gray-700 card-loading-hidden">
            <div class="flex">
                <mat-error *ngIf="errorMessages.length">
                    <ul>
                        <li *ngFor="let errorMessage of errorMessages">{{ errorMessage }}</li>
                    </ul>
                </mat-error>
                <button type="button" (click)="export()" class="flex align-center" mat-flat-button color="primary"
                        [matTooltip]="(!canExport ? 'private.certification.surveys.exportDisabled' : '' )| translate"
                        [disabled]="loading || !canExport">
                    <mat-progress-spinner class="mr-4" *ngIf="loading" [diameter]="24"
                                          mode="indeterminate"></mat-progress-spinner>
                    <ng-container
                        *ngIf="!loading">{{ 'common.actions.export.actions' | translate }}
                    </ng-container>
                </button>
            </div>
        </div>
    </div>
</mat-card>
