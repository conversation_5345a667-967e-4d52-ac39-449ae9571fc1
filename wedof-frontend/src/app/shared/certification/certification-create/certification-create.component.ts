import {Component, EventEmitter, OnInit, Output, ViewChild} from '@angular/core';
import {Certification, CertificationCreateBody} from '../../api/models/certification';
import {FormBuilder, FormGroup, FormGroupDirective, Validators} from '@angular/forms';
import {AppFormFieldData} from '../../material/app-form-field/app-form-field.component';
import {TranslateService} from '@ngx-translate/core';
import {MatDialog} from '@angular/material/dialog';
import {CertificationService} from '../../api/services/certification.service';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../errors/errors.types';
import {FormValidators} from '../../api/shared/form-validators';

@Component({
    selector: 'app-certification-create',
    templateUrl: './certification-create.component.html',
    styleUrls: ['./certification-create.component.scss']
})
export class CertificationCreateComponent implements OnInit {

    errorMessages: string[] = [];
    loading = false;
    formGroup: FormGroup;
    appFormFieldsData: AppFormFieldData[];
    newCertification: CertificationCreateBody;

    @Output() certificationCreate = new EventEmitter<Certification>();
    @ViewChild('form') form: FormGroupDirective;

    constructor(private _formBuilder: FormBuilder,
                private _certificationService: CertificationService,
                private _dialog: MatDialog,
                private _translateService: TranslateService) {
    }

    ngOnInit(): void {
        this.formGroup = this._formBuilder.group({
            newCertification: this._formBuilder.group({})
        });
        const appFormFieldsData: AppFormFieldData[] = [
            {
                controlName: 'name',
                required: true,
                value: null,
                label: 'private.certification.common.create.form.name',
                type: 'text'
            },
            {
                controlName: 'link',
                required: false,
                value: null,
                label: 'private.certification.common.create.form.link.label',
                type: 'url',
                placeholder: 'private.certification.common.create.form.link.placeholder',
                validators: [Validators.pattern(FormValidators.URL_PATTERN)],
                validatorsMessages: {
                    pattern: 'common.errors.url'
                }
            }
        ];
        this.appFormFieldsData = appFormFieldsData.filter(value => value != null);
    }

    submit(): void {
        this.loading = true;
        this.errorMessages = [];
        const certificationFormValue = this.formGroup.getRawValue().newCertification;
        this._certificationService.create(certificationFormValue).subscribe({
            next: (certification) => {
                this.loading = false;
                this.certificationCreate.emit(certification);
            },
            error: (httpErrorResponse: HttpErrorResponse) => {
                this.loading = false;
                this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
            }
        });
    }
}
