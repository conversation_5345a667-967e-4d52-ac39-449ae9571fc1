import {Component, EventEmitter, Input, On<PERSON><PERSON>roy, OnInit, Output} from '@angular/core';
import {
    CertificationPartnerAudit,
    CertificationPartnerAuditStates
} from '../../../api/models/certification-partner-audit';
import {ViewFileDialogComponent} from '../../../file/dialogs/view-file-dialog/view-file-dialog.component';
import {File} from '../../../api/models/file';
import {SnackBarComponent} from '../../../material/snack-bar/snack-bar.component';
import {displayTextSnackBar} from '../../../utils/displayTextSnackBar';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../../errors/errors.types';
import {FileIframeComponent} from '../../../file/dialogs/file-iframe/file-iframe.component';
import {DeletionConfirmationComponent} from '../../../material/action-confirmation/deletion-confirmation.component';
import {filter, finalize, switchMap, takeUntil} from 'rxjs/operators';
import {DialogCertificationAuditCompleteComponent} from '../dialog-certification-audit-complete/dialog-certification-audit-complete.component';
import {MatDialog} from '@angular/material/dialog';
import {MatSnackBar} from '@angular/material/snack-bar';
import {TranslateService} from '@ngx-translate/core';
import {CertificationPartnerAuditService} from '../../../api/services/certification-partner-audit.service';
import {CertificationPartner} from '../../../api/models/certification-partner';
import {Certification} from '../../../api/models/certification';
import {Select} from '@ngxs/store';
import {UserState} from '../../../api/state/user.state';
import {Observable, Subject} from 'rxjs';
import {User} from '../../../api/models/user';

@Component({
    selector: 'app-certification-audit-partner-menu',
    templateUrl: './certification-audit-partner-menu.component.html',
    styleUrls: ['./certification-audit-partner-menu.component.scss']
})
export class CertificationAuditPartnerMenuComponent implements OnInit, OnDestroy {

    actionsLoading = false;
    certificationPartnerAuditStates = CertificationPartnerAuditStates;

    @Input() disabled?: boolean;
    @Input() isCertifier: boolean;
    @Input() withButton = false;
    @Input() certificationPartnerAudit: CertificationPartnerAudit;
    @Input() certificationPartner: CertificationPartner;
    @Input() certification: Certification;
    @Output() openCriterias = new EventEmitter<CertificationPartnerAudit>();
    @Output() certificationPartnerChange: EventEmitter<CertificationPartner> = new EventEmitter<CertificationPartner>();
    @Output() certificationPartnerAuditUpdated: EventEmitter<CertificationPartnerAudit> = new EventEmitter<CertificationPartnerAudit>();
    @Output() errorMessages: EventEmitter<string[]> = new EventEmitter<[]>();
    @Output() loading: EventEmitter<boolean> = new EventEmitter<boolean>();
    @Output() delete: EventEmitter<CertificationPartnerAudit> = new EventEmitter<CertificationPartnerAudit>();
    @Output() refresh?: EventEmitter<CertificationPartnerAudit> = new EventEmitter<CertificationPartnerAudit>();

    isImpersonator = false;
    @Select(UserState.user) user$: Observable<User>;
    private _unsubscribeAll = new Subject<void>();

    constructor(private _dialog: MatDialog,
                private _snackBar: MatSnackBar,
                private _translateService: TranslateService,
                private _certificationPartnerAuditService: CertificationPartnerAuditService
    ) {
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    ngOnInit(): void {
        this.user$.pipe(takeUntil(this._unsubscribeAll)).subscribe((user) => {
            this.isImpersonator = user.is_impersonator;
        });
    }

    openCriteriasDialog(): void {
        this.openCriterias.emit(this.certificationPartnerAudit);
    }

    openPdfReport(certificationPartnerAudit: CertificationPartnerAudit): void {
        const report = certificationPartnerAudit.report;
        const siret = this.certificationPartner._links.partner.siret;
        this._dialog.open(ViewFileDialogComponent, {
            width: '80%',
            height: '90%',
            data: {
                file: report,
                auth: true,
                title: ((_file: File) => _file.fileName),
                src: (_file: File) =>
                    '/api/certifications/' + this.certification.certifInfo + '/partners/' + siret + '/audits/' + this.certificationPartnerAudit.id + '/files/' + _file.id,
                files: [report],
                canUpdateFileState: (_ => false),
            }
        });
    }

    generateEditableReport(certificationPartnerAudit: CertificationPartnerAudit): void {
        this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(this._translateService.instant('common.actions.file.generatingInProgress'), 5000));
        this.loading.emit(true);
        this.actionsLoading = true;
        this.errorMessages.emit([]);
        this._certificationPartnerAuditService.generateReport(
            this.certification.certifInfo,
            this.certificationPartner._links.partner.siret,
            certificationPartnerAudit.id
        ).subscribe(updatedCertificationPartnerAudit => {
                this.certificationPartnerAudit = updatedCertificationPartnerAudit;
                this.certificationPartnerAuditUpdated.emit(this.certificationPartnerAudit);
                if (this.certificationPartnerAudit.state === this.certificationPartnerAuditStates.COMPLETED) {
                    this.openPdfReport(this.certificationPartnerAudit);
                } else {
                    this.openEditableReport(this.certificationPartnerAudit);
                }
            },
            (httpErrorResponse: HttpErrorResponse) => {
                this.errorMessages.emit((httpErrorResponse.error as ApiError).errorMessages);
            }
        ).add(() => {
            this.loading.emit(false);
            this.actionsLoading = false;
        });
    }

    openEditableReport(certificationPartnerAudit: CertificationPartnerAudit): void {
        this._dialog.open(FileIframeComponent, {
            disableClose: true,
            width: '80%',
            height: '90%',
            data: {
                url: certificationPartnerAudit.report.link,
                title: certificationPartnerAudit.report.fileName
            }
        });
    }

    restartAudit(certificationPartnerAudit: CertificationPartnerAudit): void {
        this.loading.emit(true);
        this.actionsLoading = true;
        this.errorMessages.emit([]);
        this._certificationPartnerAuditService.update(
            this.certification.certifInfo,
            this.certificationPartner._links.partner.siret,
            certificationPartnerAudit.id,
            {restartAudit: true}
        ).subscribe(updatedCertificationPartnerAudit => {
                this.certificationPartnerAudit = updatedCertificationPartnerAudit;
                this.certificationPartnerAuditUpdated.emit(this.certificationPartnerAudit);
                if (this.refresh) {
                    this.refresh.emit(updatedCertificationPartnerAudit);
                }
            },
            (httpErrorResponse: HttpErrorResponse) => {
                this.errorMessages.emit((httpErrorResponse.error as ApiError).errorMessages);
            }
        ).add(() => {
            this.loading.emit(false);
            this.actionsLoading = false;
        });
    }

    deleteAudit(certificationPartnerAudit: CertificationPartnerAudit): void {
        this.errorMessages.emit([]);
        const dialogRef = this._dialog.open(DeletionConfirmationComponent, {
            panelClass: 'full-page-scroll-40',
            height: 'auto',
            data: {
                messageKey: 'private.certification.audit.confirmDeletion',
                data: certificationPartnerAudit
            }
        });
        dialogRef.componentInstance.actionValue$.pipe(
            filter((confirmation: boolean) => confirmation),
            switchMap(() => this._certificationPartnerAuditService.delete(certificationPartnerAudit.id)),
            finalize(() => {
                dialogRef.componentInstance.close();
            })
        ).subscribe(
            () => {
                this.delete.emit(certificationPartnerAudit);
            },
            (httpErrorResponse: HttpErrorResponse) => {
                this.errorMessages.emit((httpErrorResponse.error as ApiError).errorMessages);
            }
        );
    }

    completeAudit(certificationPartnerAudit: CertificationPartnerAudit): void {
        this.loading.emit(true);
        this.actionsLoading = true;
        const dialogRef = this._dialog.open(DialogCertificationAuditCompleteComponent, {
            disableClose: true,
            panelClass: 'full-page-scroll-30',
            data: {
                certificationPartnerAudit: certificationPartnerAudit,
                certification: this.certification,
                certificationPartner: this.certificationPartner
            }
        });
        dialogRef.afterClosed().subscribe(result => {
            if (result?.data) {
                this.certificationPartnerAudit = result.data;
                this.certificationPartnerAuditUpdated.emit(this.certificationPartnerAudit);
                const certificationPartner = {
                    ...result.updatedPartner ?? this.certificationPartner,
                };
                if (result.updatePartnerCompliance) {
                    certificationPartner.compliance = this.certificationPartnerAudit.result;
                }
                if (result.updatePartnerCompliance || result.updatedPartner) {
                    this.certificationPartnerChange.emit(certificationPartner);
                }
            }
            this.loading.emit(false);
            this.actionsLoading = false;
        });
    }

}
