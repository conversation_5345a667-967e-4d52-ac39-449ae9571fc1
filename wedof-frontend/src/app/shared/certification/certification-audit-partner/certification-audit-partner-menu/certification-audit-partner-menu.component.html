<ng-container *ngIf="withButton else onlyMenu">
    <div class="flex align-center">
        <ng-container
            *ngIf="certificationPartnerAudit.state === certificationPartnerAuditStates.COMPLETED; else showStateInProgress">
            <button
                type="button" mat-flat-button class="button-actionsOnly"
                (click)="certificationPartnerAudit.report ? openPdfReport(certificationPartnerAudit) : generateEditableReport(certificationPartnerAudit)"
                [disabled]="actionsLoading" color="primary">
                {{ 'private.certification.audit.report.open' | translate }}
            </button>
        </ng-container>
        <ng-template #showStateInProgress>
            <ng-container
                *ngIf="certificationPartnerAudit.state === certificationPartnerAuditStates.IN_PROGRESS; else showImpersonator">
                <button *ngIf="!certificationPartnerAudit.report"
                        type="button" mat-flat-button color="primary"
                        class="button-actions"
                        (click)="generateEditableReport(certificationPartnerAudit)"
                        [disabled]="actionsLoading">
                    <mat-progress-spinner *ngIf="actionsLoading; else submitLabel" [diameter]="24" mode="indeterminate">
                    </mat-progress-spinner>
                    <ng-template #submitLabel>
                        {{ 'private.certification.audit.report.open' | translate }}
                    </ng-template>
                </button>
                <button *ngIf="certificationPartnerAudit.report"
                        type="button" mat-flat-button color="primary"
                        class="button-actions"
                        (click)="openEditableReport(certificationPartnerAudit)"
                        [disabled]="actionsLoading">
                    {{ 'private.certification.audit.report.edit' | translate }}
                </button>
                <button class="flex justify-center items-center button-arrow button-arrow-primary"
                        color="primary"
                        (click)="$event.stopPropagation()"
                        mat-flat-button
                        [disabled]="actionsLoading"
                        [matMenuTriggerFor]="actionsMenuUpdateState"
                        title="Actions" type="button">
                    <mat-icon class="icon" svgIcon="arrow_drop_down"></mat-icon>
                </button>
            </ng-container>
            <ng-template #showImpersonator>
                <button
                    *ngIf="isImpersonator && certificationPartnerAudit.state === certificationPartnerAuditStates.FAILED; else showNoActions"
                    type="button" mat-flat-button color="primary" class="button-actionsOnly"
                    (click)="restartAudit(certificationPartnerAudit)"
                    [disabled]="actionsLoading">
                    <span>{{'private.certification.audit.restart' | translate}}</span>
                </button>
                <ng-template #showNoActions>
                    {{ 'common.actions.noAction' | translate }}
                </ng-template>
            </ng-template>
        </ng-template>
    </div>
</ng-container>

<ng-template #onlyMenu>
    <button
        [disabled]="disabled"
        [matTooltip]="disabled ? ('private.certification.audit.state.' + certificationPartnerAudit.state | translate) : null "
        *ngIf="isCertifier || (!this.isCertifier && certificationPartnerAudit.state === certificationPartnerAuditStates.COMPLETED)"
        type="button"
        mat-icon-button
        (click)="$event.stopPropagation()"
        [matMenuTriggerFor]="actionsMenu"
        title="Actions">
        <mat-icon svgIcon="more_vert"></mat-icon>
    </button>
</ng-template>

<mat-menu #actionsMenu="matMenu">
    <ng-template matMenuContent>
        <button (click)="openCriteriasDialog()"
                [disabled]="actionsLoading"
                mat-menu-item>
            <mat-icon color="primary" svgIcon="checklist"></mat-icon>
            <span>{{ 'private.certification.audit.criterias' | translate }}</span>
        </button>
        <ng-container
            *ngIf="certificationPartnerAudit.state === certificationPartnerAuditStates.IN_PROGRESS && isCertifier">
            <button *ngIf="!certificationPartnerAudit.report"
                    (click)="generateEditableReport(certificationPartnerAudit)"
                    [disabled]="actionsLoading"
                    mat-menu-item>
                <mat-icon color="primary" svgIcon="bolt"></mat-icon>
                <span>{{ 'private.certification.audit.report.open' | translate }}</span>
            </button>
            <button *ngIf="certificationPartnerAudit.report"
                    (click)="openEditableReport(certificationPartnerAudit)"
                    [disabled]="actionsLoading"
                    mat-menu-item>
                <mat-icon color="primary">edit_note</mat-icon>
                <span>{{ 'private.certification.audit.report.edit' | translate }}</span>
            </button>
            <button (click)="completeAudit(certificationPartnerAudit)"
                    [disabled]="actionsLoading"
                    mat-menu-item>
                <mat-icon color="primary" svgIcon="check"></mat-icon>
                {{ 'private.certification.audit.complete' | translate }}
            </button>
        </ng-container>
        <ng-container *ngIf="certificationPartnerAudit.state === certificationPartnerAuditStates.COMPLETED">
            <button *ngIf="certificationPartnerAudit.report"
                    (click)="openPdfReport(certificationPartnerAudit)"
                    [disabled]="actionsLoading"
                    mat-menu-item>
                <mat-icon color="primary" svgIcon="edit"></mat-icon>
                <span>{{ 'private.certification.audit.report.open' | translate }}</span>
            </button>
            <button *ngIf="!certificationPartnerAudit.report"
                    (click)="generateEditableReport(certificationPartnerAudit)"
                    [disabled]="actionsLoading"
                    mat-menu-item>
                <mat-icon color="primary" svgIcon="edit"></mat-icon>
                <span>{{ 'private.certification.audit.report.open' | translate }}</span>
            </button>
        </ng-container>
        <button *ngIf="isImpersonator && certificationPartnerAudit.state === certificationPartnerAuditStates.FAILED"
                mat-menu-item (click)="restartAudit(certificationPartnerAudit)"
                [disabled]="actionsLoading">
            <mat-icon color="primary" svgIcon="restart_alt"></mat-icon>
            <span> {{'private.certification.audit.restart' | translate}}</span>
            <!-- todo when impresonator is removed don't forget to add isCertifier -->
        </button>
        <button *ngIf="isImpersonator" mat-menu-item (click)="deleteAudit(certificationPartnerAudit)"
                [disabled]="actionsLoading">
            <mat-icon color="warn" svgIcon="delete"></mat-icon>
            <span>{{ 'common.actions.delete' | translate }}</span>
            <!-- todo when impresonator is removed don't forget to add isCertifier -->
        </button>
    </ng-template>
</mat-menu>

<mat-menu #actionsMenuUpdateState="matMenu">
    <button *ngIf="!certificationPartnerAudit.report"
            (click)="generateEditableReport(certificationPartnerAudit)"
            [disabled]="actionsLoading"
            mat-menu-item>
        <mat-icon color="primary" svgIcon="bolt"></mat-icon>
        <span>{{ 'private.certification.audit.report.open' | translate }}</span>
    </button>
    <button *ngIf="certificationPartnerAudit.report"
            (click)="openEditableReport(certificationPartnerAudit)"
            [disabled]="actionsLoading"
            mat-menu-item>
        <mat-icon color="primary">edit_note</mat-icon>
        <span>{{ 'private.certification.audit.report.edit' | translate }}</span>
    </button>
    <button (click)="completeAudit(certificationPartnerAudit)"
            [disabled]="actionsLoading"
            mat-menu-item>
        <mat-icon color="primary" svgIcon="check"></mat-icon>
        {{ 'private.certification.audit.complete' | translate }}
    </button>
</mat-menu>
