<app-dialog-layout
    [title]=" 'private.certification.audit.create' | translate"
    [actions]="actions"
    (dialogClose)="closeModal()">

    <form [formGroup]="formGroup" class="flex flex-col">
        <app-form-fields class="grid grid-cols-6 gap-2"
                         formGroupName="certificationAuditForm"
                         [appFormFieldsData]="appFormFieldsData"
                         [formGroup]="formGroup"></app-form-fields>
        <div *ngIf="errorMessages?.length" class="flex items-center">
            <treo-message class="flex-auto" appearance="outline" [showIcon]="false" type="error">
                <ul>
                    <li *ngFor="let errorMessage of errorMessages">
                        {{ errorMessage }}
                    </li>
                </ul>
            </treo-message>
        </div>
    </form>

    <ng-template #actions>
        <button type="submit" class="flex align-center" mat-flat-button color="primary"
                (click)="submit()"
                [disabled]="loading || formGroup?.invalid">
            <mat-progress-spinner class="mr-4" *ngIf="loading" [diameter]="24"
                                  mode="indeterminate"></mat-progress-spinner>
            <ng-container
                *ngIf="!loading">{{ ('private.certification.audit.create') | translate }}
            </ng-container>
        </button>
    </ng-template>
</app-dialog-layout>
