import {Component, Inject, OnInit} from '@angular/core';
import {FormBuilder, FormGroup} from '@angular/forms';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material/dialog';
import {HttpErrorResponse} from '@angular/common/http';
import {AppFormFieldData} from '../../../material/app-form-field/app-form-field.component';
import {Certification} from '../../../api/models/certification';
import {ApiError} from '../../../errors/errors.types';
import {CertificationPartnerAuditService} from '../../../api/services/certification-partner-audit.service';
import {CertificationPartner} from '../../../api/models/certification-partner';
import {CertificationPartnerAuditTemplate} from '../../../api/models/certification-partner-audit';

@Component({
    selector: 'app-dialog-certification-audit-create',
    templateUrl: './dialog-certification-audit-create.component.html',
    styleUrls: ['./dialog-certification-audit-create.component.scss']
})
export class DialogCertificationAuditCreateComponent implements OnInit {

    loading = false;
    formGroup: FormGroup;
    errorMessages: string[] = [];
    appFormFieldsData: AppFormFieldData[];

    constructor(public dialogRef: MatDialogRef<DialogCertificationAuditCreateComponent>,
                private _certificationPartnerAuditService: CertificationPartnerAuditService,
                private _formBuilder: FormBuilder,
                @Inject(MAT_DIALOG_DATA) public dialogData: {
                    certification: Certification
                    certificationPartner: CertificationPartner
                    auditTemplates: CertificationPartnerAuditTemplate[]
                }
    ) {
    }

    ngOnInit(): void {
        const templateChoices = this.dialogData.auditTemplates.map(certificationPartnerAuditTemplate => ({
            key: certificationPartnerAuditTemplate.name,
            value: certificationPartnerAuditTemplate.id
        }));
        this.formGroup = this._formBuilder.group({
            certificationAuditForm: this._formBuilder.group({})
        });
        this.appFormFieldsData = [
            {
                controlName: 'template',
                required: true,
                label: 'private.common.certification.audit.template.template',
                type: 'select',
                choices: templateChoices
            }
        ];
    }

    closeModal(): void {
        this.dialogRef.close();
    }

    submit(): void {
        this.loading = true;
        const templateId = this.formGroup.getRawValue().certificationAuditForm.template;
        this._certificationPartnerAuditService.create(
            this.dialogData.certification.certifInfo,
            this.dialogData.certificationPartner._links.partner.siret,
            {
                templateId: templateId
            }
        ).subscribe(
            (newCertificationPartnerAudit) => {
                this.dialogRef.close({data: newCertificationPartnerAudit});
            },
            (httpErrorResponse: HttpErrorResponse) => {
                this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
            }
        ).add(() => this.loading = false);
    }
}
