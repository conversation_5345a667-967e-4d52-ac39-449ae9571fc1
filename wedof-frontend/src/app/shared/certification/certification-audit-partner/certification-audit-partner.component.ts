import {
    Component,
    ElementRef,
    EventEmitter,
    Input,
    OnChanges,
    OnDestroy,
    OnInit,
    Output,
    SimpleChanges
} from '@angular/core';
import {Certification} from '../../api/models/certification';
import {BaseCardComponentDirective, RequiredCallSuper} from '../../utils/base-card/base-card.directive';
import {CertificationPartner, CertificationPartnerStates} from '../../api/models/certification-partner';
import {MatDialog} from '@angular/material/dialog';
import {
    CertificationPartnerAudit,
    CertificationPartnerAuditCriteriaCompliances,
    CertificationPartnerAuditResults,
    CertificationPartnerAuditStates,
    CertificationPartnerAuditTemplate
} from '../../api/models/certification-partner-audit';
import {
    CertificationPartnerAuditHttpParams,
    CertificationPartnerAuditService
} from '../../api/services/certification-partner-audit.service';
import {ThemePalette} from '@angular/material/core';
import {DialogCertificationAuditCreateComponent} from './dialog-certification-audit-create/dialog-certification-audit-create.component';
import {DialogCertificationAuditDetailsComponent} from './dialog-certification-audit-details/dialog-certification-audit-details.component';
import {CertificationPartnerAuditResultToIconPipe} from '../../pipes/certification-partner-audit-state-to-icon.pipe';
import {Select} from '@ngxs/store';
import {UserState} from '../../api/state/user.state';
import {combineLatest, Observable, Subject} from 'rxjs';
import {User} from '../../api/models/user';
import {takeUntil} from 'rxjs/operators';
import {CertificationPartnerAuditTemplateService} from '../../api/services/certification-partner-audit-template.service';
import {CertificationService} from '../../api/services/certification.service';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../errors/errors.types';
import {Subscription} from '../../api/models/subscription';
import {SubscriptionState} from '../../api/state/subscription.state';
import {Organism} from '../../api/models/organism';
import {OrganismState} from '../../api/state/organism.state';

@Component({
    selector: 'app-certification-audit-partner',
    templateUrl: './certification-audit-partner.component.html',
    styleUrls: ['./certification-audit-partner.component.scss']
})
export class CertificationAuditPartnerComponent extends BaseCardComponentDirective implements OnInit, OnChanges, OnDestroy {

    static COMPONENT_ID = 'audit';

    params: CertificationPartnerAuditHttpParams;
    loading = false;
    allowAudits = false;
    canCreateAudit = false;
    actionsLoading = false;
    errorMessages: string[] = [];
    subscriptionErrorMessages: string[] = [];
    templateWithoutCriteriaCounter = 0;
    auditTemplates: CertificationPartnerAuditTemplate[] = [];
    auditTemplatesAvailable: CertificationPartnerAuditTemplate[] = [];
    certificationPartnerAudits: CertificationPartnerAudit[] = [];
    certificationPartnerAuditStates = CertificationPartnerAuditStates;
    refreshPendingAuditsTimeout?: number = null;
    isAnnualSubscription: boolean;
    user: User;
    currentOrganism: Organism;
    subscription: Subscription;

    @Input() isCertifier = true;
    @Input() displayedColumns: string[];
    @Input() certification: Certification;
    @Input() certificationPartner: CertificationPartner;
    @Output() certificationPartnerChange: EventEmitter<CertificationPartner> = new EventEmitter<CertificationPartner>();
    @Output() certificationUpdated?: EventEmitter<Certification> = new EventEmitter<Certification>();

    isImpersonator = false;
    @Select(UserState.user) user$: Observable<User>;
    @Select(SubscriptionState.subscription) subscription$: Observable<Subscription>;
    @Select(OrganismState.organism) organism$: Observable<Organism>;
    private _unsubscribeAll = new Subject<void>();

    constructor(private _el: ElementRef,
                private _certificationService: CertificationService,
                private _certificationPartnerAuditService: CertificationPartnerAuditService,
                private _certificationPartnerAuditTemplateService: CertificationPartnerAuditTemplateService,
                private _certificationPartnerAuditResultToIconPipe: CertificationPartnerAuditResultToIconPipe,
                private _dialog: MatDialog) {
        super(CertificationAuditPartnerComponent.COMPONENT_ID, _el);
    }

    ngOnDestroy(): RequiredCallSuper {
        this.clearRefreshPendingAuditsTimeout();
        return super.ngOnDestroy();
    }

    ngOnInit(): void {
        combineLatest([
            this.subscription$,
            this.organism$,
            this.user$
        ]).pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe(([subscription, organism, user]) => {
            this.isImpersonator = user.is_impersonator;
            this.user = user;
            this.currentOrganism = organism;
            this.subscription = subscription;
            if (this.isCertifier) {
                const certifierPeriodStartDate = new Date(subscription.certifierPeriodStartDate);
                const certifierPeriodEndDate = subscription.certifierPeriodEndDate;
                const daysBetweenSubscription = (new Date(certifierPeriodEndDate).getTime() - new Date(certifierPeriodStartDate).getTime()) / (1000 * 3600 * 24);
                this.isAnnualSubscription = daysBetweenSubscription > 31;
            }
        });
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes.certification || changes.certificationPartner) {
            this.panelLoading();
            this.init();
        }
    }

    getIcon(certificationPartnerAudit: CertificationPartnerAudit): { class: string, color: ThemePalette, icon: string, tooltip: string } {
        const icon = {
            tooltip: 'private.certification.audit.state.' + certificationPartnerAudit.state,
            class: '',
            icon: undefined,
            color: undefined
        };
        if (certificationPartnerAudit.state === CertificationPartnerAuditStates.COMPLETED) {
            icon.tooltip = 'private.certification.audit.result.' + certificationPartnerAudit.result;
            icon.icon = this._certificationPartnerAuditResultToIconPipe.transform(certificationPartnerAudit.result);
            if (certificationPartnerAudit.result === CertificationPartnerAuditResults.COMPLIANT) {
                icon.class = 'text-green';
            } else if (certificationPartnerAudit.result === CertificationPartnerAuditResults.NON_COMPLIANT) {
                icon.color = 'warn';
            } else if (certificationPartnerAudit.result === CertificationPartnerAuditResults.PARTIALLY_COMPLIANT) {
                icon.class = 'text-orange';
            }
        } else if (certificationPartnerAudit.state === CertificationPartnerAuditStates.IN_PROGRESS) {
            icon.icon = 'fact_check';
        } else if (certificationPartnerAudit.state === CertificationPartnerAuditStates.PENDING_COMPUTATION ||
            certificationPartnerAudit.state === CertificationPartnerAuditStates.COMPUTING) {
            icon.icon = 'hourglass_empty';
        } else {
            icon.icon = 'close';
            icon.color = 'warn';
        }
        return icon;
    }

    init(): void {
        this.errorMessages = [];
        this.cardLoading = true;
        this.clearRefreshPendingAuditsTimeout();
        this.allowAudits = this.certification.allowAudits;
        this.params = {
            siret: this.certificationPartner._links.partner.siret,
            certifInfo: this.certification.certifInfo
            // TODO(audit): pagination ?
        };
        if (!this.isCertifier) {
            this.params['state'] = CertificationPartnerAuditStates.COMPLETED;
        }
        this.canCreateAudit = this.isCertifier &&
            [CertificationPartnerStates.ACTIVE, CertificationPartnerStates.REVOKED, CertificationPartnerStates.SUSPENDED].includes(this.certificationPartner.state);
        if ((this.isCertifier && this.allowAudits) || !this.isCertifier) {
            this._certificationPartnerAuditService.list(this.params).subscribe((certificationPartnerAuditResponse) => {
                this.certificationPartnerAudits = certificationPartnerAuditResponse.payload;
                const computingOrPendingComputationAudits = this.certificationPartnerAudits?.filter((cpAudit) =>
                    cpAudit.state === CertificationPartnerAuditStates.COMPUTING || cpAudit.state === CertificationPartnerAuditStates.PENDING_COMPUTATION);
                this.canCreateAudit = computingOrPendingComputationAudits?.length === 0;
                if (this.isCertifier) {
                    this._certificationPartnerAuditTemplateService.list({certifInfo: this.certification.certifInfo}).subscribe(
                        (certificationPartnerAuditTemplateResponse) => {
                            this.auditTemplates = certificationPartnerAuditTemplateResponse.payload;
                            this.auditTemplates?.map((certificationPartnerAuditTemplate) => {
                                if (!certificationPartnerAuditTemplate.criterias?.length) {
                                    this.templateWithoutCriteriaCounter += 1;
                                }
                            });
                            this.auditTemplatesAvailable = this.auditTemplates.filter((certificationPartnerAuditTemplate) => {
                                return certificationPartnerAuditTemplate.criterias?.length > 0;
                            });
                            this.cardLoading = false;
                            this.panelLoaded();
                        });
                } else {
                    this.cardLoading = false;
                    this.panelLoaded();
                }
            });
        } else {
            this.certificationPartnerAudits = [];
            this.cardLoading = false;
            this.panelLoaded();
        }
    }

    processedCertificationPartnerAudit(certificationPartnerAuditToUpdate: CertificationPartnerAudit): void {
        this.certificationPartnerAudits = this.certificationPartnerAudits.map((certificationPartnerAudit) => {
            return certificationPartnerAudit.id === certificationPartnerAuditToUpdate.id ? certificationPartnerAuditToUpdate : certificationPartnerAudit;
        });
    }

    createAudit(): void {
        this.actionsLoading = true;
        const dialogRef = this._dialog.open(DialogCertificationAuditCreateComponent, {
            disableClose: true,
            panelClass: 'full-page-scroll-30',
            data: {
                certification: this.certification,
                certificationPartner: this.certificationPartner,
                auditTemplates: this.auditTemplatesAvailable
            }
        });
        dialogRef.afterClosed().subscribe(result => {
            if (result?.data) {
                this.certificationPartnerAudits = [result.data, ...this.certificationPartnerAudits];
                this.refreshPendingAudits();
            }
            this.actionsLoading = false;
        });
    }

    refreshPendingAudits(): void {
        if (this.refreshPendingAuditsTimeout) { // Already pending a timeout, no need to add another one
            return;
        }
        const pendingAudits = this.certificationPartnerAudits.filter((certificationPartnerAudit) => {
            return [CertificationPartnerAuditStates.PENDING_COMPUTATION, CertificationPartnerAuditStates.COMPUTING].includes(certificationPartnerAudit.state);
        }).length;
        if (pendingAudits) {
            const refreshDelayInSeconds = 30;
            this.refreshPendingAuditsTimeout = setTimeout(() => {
                this.refreshPendingAuditsTimeout = null;
                return this._certificationPartnerAuditService.list(this.params).subscribe((certificationPartnerAuditResponse) => {
                    // TODO update only the pending ones to avoid losing references that may be opened in dialogs
                    this.certificationPartnerAudits = certificationPartnerAuditResponse.payload;
                    this.refreshPendingAudits();
                });
            }, refreshDelayInSeconds * 1000);
        }
    }

    clearRefreshPendingAuditsTimeout(): void {
        if (this.refreshPendingAuditsTimeout != null) {
            clearTimeout(this.refreshPendingAuditsTimeout);
        }
    }

    delete(certificationPartnerAuditToDelete: CertificationPartnerAudit): void {
        this.certificationPartnerAudits = this.certificationPartnerAudits.filter((certificationPartnerAudit) => {
            return certificationPartnerAudit.id !== certificationPartnerAuditToDelete.id;
        });
    }

    openDetailsDialog(certificationPartnerAudit: CertificationPartnerAudit): void {
        this._dialog.open(DialogCertificationAuditDetailsComponent, {
            panelClass: 'full-page-scroll-80',
            height: 'auto',
            data: {
                certificationPartnerAudit: certificationPartnerAudit,
                certificationPartner: this.certificationPartner,
                certification: this.certification,
                isCertifier: this.isCertifier,
                certificationPartnerChange: (certificationPartnerUpdated) => this.certificationPartnerChange.emit(certificationPartnerUpdated),
                certificationPartnerAuditChange: (certificationPartnerAuditUpdated) => this.processedCertificationPartnerAudit(certificationPartnerAuditUpdated),
                delete: (certificationPartnerAuditDeleted) => this.delete(certificationPartnerAuditDeleted),
                currentOrganism: this.currentOrganism,
                subscription: this.subscription
            }
        });
    }

    getCompliantCriteriaCount(certificationPartnerAudit: CertificationPartnerAudit): number {
        return certificationPartnerAudit.evaluatedCriterias.filter(evaluatedCriteria =>
            evaluatedCriteria.compliance === CertificationPartnerAuditCriteriaCompliances.COMPLIANT
        ).length;
    }

    setLoading(loading: boolean): void {
        this.actionsLoading = loading;
    }

    setErrorMessages(error: string[]): void {
        this.errorMessages = error;
    }

    isDisabledMenu(audit): boolean {
        if (audit.state === CertificationPartnerAuditStates.PENDING_COMPUTATION || audit.state === CertificationPartnerAuditStates.COMPUTING) {
            return true;
        } else if (audit.state === CertificationPartnerAuditStates.FAILED) {
            return !this.isImpersonator;
        } else {
            return false;
        }
    }

    mailTo(): string {
        this.loading = false;
        return 'mailto:<EMAIL>?' +
            'subject=Activation Audit &' +
            'body=Bonjour l\'équipe Wedof, %0D%0A  %0D%0A Je souhaiterais profiter de l\'abonnement sur la gestion des audits pour ma certification '
            + this.certification.externalId + '.' + '%0D%0A %0D%0A' + 'Bien à vous, %0D%0A ' + this.user.name;
    }

    activateAudits(isAnnualSubscription: boolean): void {
        if (isAnnualSubscription) {
            window.location.href = 'mailto:<EMAIL>?' +
                'subject=Activation Audit &' +
                'body=Bonjour l\'équipe Wedof, %0D%0A  %0D%0A Je souhaiterais profiter de l\'abonnement sur la gestion des audits pour ma certification '
                + this.certification.externalId + '.' + '%0D%0A %0D%0A' + 'Bien à vous, %0D%0A ' + this.user.name;
        }
        this._certificationService.activateAudits(this.certification.certifInfo, isAnnualSubscription).subscribe({
            next: (certificationUpdated) => {
                this.allowAudits = certificationUpdated.allowAudits;
                if (!isAnnualSubscription) {
                    this.certificationUpdated.emit(certificationUpdated);
                }
            },
            error: (httpErrorResponse: HttpErrorResponse) => {
                this.subscriptionErrorMessages = (httpErrorResponse.error as ApiError).errorMessages;
            }
        });
    }
}
