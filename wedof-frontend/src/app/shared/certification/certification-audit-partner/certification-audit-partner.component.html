<mat-card class="flex-col mt-3 p-5 pb-0 pt-3 border shadow-none sm"
          [ngClass]="{'card-loading':cardLoading}">
    <div class="flex items-center mb-2">
        <mat-icon class="mr-3 card-loading-show icon-size-32" svgIcon="fact_check" color="primary">
        </mat-icon>
        <div class="flex items-center">
            <span
                class="text-xl font-semibold card-loading-show"> {{ ('private.common.certification.audit.' + (isCertifier ? 'title' : 'partnerTitle')) | translate }}</span>
        </div>
        <div class="ml-auto -mr-4 card-loading-show flex" *ngIf="isCertifier && allowAudits">
            <a [href]="'/certification/partenariats/' + certification.certifInfo + '/modelesAudit'"
               mat-icon-button>
                <mat-icon [matTooltip]="'private.common.certification.audit.criterias.settings' | translate"
                          color="text-gray">
                    settings
                </mat-icon>
            </a>
            <button type="button" mat-icon-button
                    [disabled]="actionsLoading || !canCreateAudit || !auditTemplates.length ||
                    (auditTemplates.length && templateWithoutCriteriaCounter > 0 && auditTemplates.length === templateWithoutCriteriaCounter) "
                    (click)="createAudit()">
                <mat-icon svgIcon="add_circle"></mat-icon>
            </button>
        </div>
    </div>
    <p *ngIf="certification.auditTrialEndDate"
       class="mb-2">{{'common.trialEnd' | translate}} {{certification.auditTrialEndDate | date: 'dd/MM/yy' }}</p>

    <ng-container *ngIf="isCertifier && !allowAudits" class="card-loading-show">
        <app-subscription-small-card [type]="'allowAudits'"
                                     [showFullCard]="false"
                                     (privateUpdate)="activateAudits(isAnnualSubscription)"
                                     [errorMessages]="subscriptionErrorMessages"
                                     [submissionText]="isAnnualSubscription ? 'private.common.certification.audit.activateContact' : 'common.actions.activate'">
        </app-subscription-small-card>
    </ng-container>

    <ng-container *ngIf="certification.auditsPendingCancellation && isCertifier" class="card-loading-show">
        <treo-message type="info" class="mt-2" appearance="outline"
                      [showIcon]="false">
            {{'private.common.certification.audit.pendingCancellation.title' | translate }}
        </treo-message>
        <button class="w-2/3 bg-gray-200 mt-4 mb-4 flex ml-auto mr-auto" mat-flat-button [disabled]="loading"
                (click)="activateAudits(false)">
            {{'private.common.certification.audit.pendingCancellation.reactivateButton' | translate }}
        </button>
    </ng-container>

    <ng-container *ngIf="isCertifier && allowAudits">
        <treo-message type="info" class="mt-2 mb-2 card-loading-show" appearance="outline"
                      *ngIf="!canCreateAudit; else createAuditAvailable"
                      [showIcon]="false">
            {{'private.common.certification.audit.criterias.auditNotAvailable' | translate  }}
        </treo-message>
        <ng-template #createAuditAvailable>
            <div *ngIf="!auditTemplates.length" class="mt-2 mb-2 card-loading-show">
                <treo-message
                    [showIcon]="false" appearance="outline" type="info">
                    {{'private.certification.audit.createUnavailableNoTemplate' | translate : {certificationName: certification.name}  }}
                </treo-message>
                <button type="button" mat-flat-button color="primary"
                        class="mb-2 mt-2 card-loading-show flex mr-auto ml-auto">
                    <a class="no-underline"
                       [href]="'/certification/partenariats/' + certification.certifInfo + '/modelesAudit'">
                        {{'private.common.certification.audit.template.create'|translate}}
                    </a>
                </button>
            </div>
            <treo-message *ngIf="auditTemplates.length && templateWithoutCriteriaCounter > 0 "
                          [showIcon]="false" appearance="outline" type="info" class="mt-2 mb-2 card-loading-show">
                {{'private.certification.audit.createUnavailable' | translate  }}
            </treo-message>
        </ng-template>
    </ng-container>

    <div *ngIf="!cardLoading" class="flex flex-col mt-3 mb-2 overflow-y-auto max-h-120">

        <ng-container *ngIf="!this.isCertifier && certificationPartnerAudits?.length">
            <p class="mb-2 mt-2 text-center m-auto">
                {{'private.certification.audit.feedbackPartner' | translate}}
            </p>
        </ng-container>

        <table *ngIf="certificationPartnerAudits?.length; else noData"
               [dataSource]="certificationPartnerAudits" mat-table>
            <ng-container matColumnDef="state">
                <th mat-header-cell *matHeaderCellDef></th>
                <td *matCellDef="let audit" class="w-1/12 pl-0" mat-cell>
                    <div class="flex items-center ml-1">
                        <mat-icon
                            class="icon-small"
                            [matTooltip]="getIcon(audit).tooltip | translate"
                            [class]="getIcon(audit).class"
                            [color]="getIcon(audit).color">
                            {{ getIcon(audit).icon }}
                        </mat-icon>
                    </div>
                </td>
            </ng-container>
            <ng-container matColumnDef="name">
                <th mat-header-cell
                    *matHeaderCellDef>{{(isCertifier ? 'private.common.certification.audit.template.table.name' : 'private.common.shortcut.audit') | translate }}</th>
                <td *matCellDef="let audit" class="w-4/8" mat-cell>
                    {{ audit._links.certificationPartnerAuditTemplate.name }}
                </td>
            </ng-container>
            <ng-container matColumnDef="date">
                <th mat-header-cell
                    *matHeaderCellDef>{{'private.common.certification.audit.template.table.date' | translate}}</th>
                <td *matCellDef="let audit" class="w-2/6" mat-cell>
                    {{ audit.startDate | date: 'dd/MM/yyyy (HH:mm)' }}
                    <p *ngIf="audit.endDate" class="text-secondary text-sm"
                       [matTooltip]="'private.certification.audit.endDate' | translate">
                        {{ audit.endDate | date: 'dd/MM/yyyy (HH:mm)' }}</p>
                </td>
            </ng-container>
            <ng-container matColumnDef="criteriasNumber" *ngIf="displayedColumns.includes('criteriasNumber')">
                <th mat-header-cell
                    *matHeaderCellDef>{{'private.common.certification.audit.template.table.criterias' | translate}}</th>
                <td *matCellDef="let audit" class="w-1/6" mat-cell>
                    <span
                        *ngIf="audit.state === certificationPartnerAuditStates.PENDING_COMPUTATION ||
                         audit.state === certificationPartnerAuditStates.COMPUTING ||
                         audit.state === certificationPartnerAuditStates.FAILED else criteriasCount">
                        ?
                    </span>
                    <ng-template #criteriasCount>
                        <span class="underline">
                            {{ getCompliantCriteriaCount(audit) }} / {{ audit.evaluatedCriterias?.length }}
                        </span>
                    </ng-template>
                </td>
            </ng-container>
            <ng-container matColumnDef="menu">
                <th mat-header-cell *matHeaderCellDef></th>
                <td *matCellDef="let audit" class="w-1/12 text-right" mat-cell>
                    <app-certification-audit-partner-menu
                        [disabled]="isDisabledMenu(audit)"
                        [isCertifier]="isCertifier"
                        [certificationPartnerAudit]="audit"
                        (certificationPartnerAuditUpdated)="processedCertificationPartnerAudit($event)"
                        [certificationPartner]="certificationPartner"
                        [certification]="certification"
                        (certificationPartnerChange)="certificationPartnerChange.emit($event)"
                        (delete)="delete($event)"
                        (openCriterias)="openDetailsDialog(audit)"
                        (errorMessages)="setErrorMessages($event)"
                        (loading)="setLoading($event)">
                    </app-certification-audit-partner-menu>
                </td>
            </ng-container>
            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row
                (click)="audit.state !== certificationPartnerAuditStates.PENDING_COMPUTATION && audit.state !== certificationPartnerAuditStates.COMPUTING && !actionsLoading && openDetailsDialog(audit)"
                *matRowDef="let audit; columns: displayedColumns;"></tr>
        </table>

        <ng-template #noData>
            <p class="mb-2 text-center m-auto" *ngIf="!isCertifier || (isCertifier && allowAudits)">
                {{ 'private.certification.audit.noData' | translate }}
            </p>
        </ng-template>
    </div>

    <div *ngIf="isCertifier && allowAudits"
         class="flex items-center mt-2 justify-end border-t pl-5 pr-10 -mx-5 py-3 light:bg-cool-gray-50 dark:bg-cool-gray-700 card-loading-hidden">
        <div class="flex">
            <mat-error *ngIf="errorMessages?.length">
                <ul>
                    <li *ngFor="let errorMessage of errorMessages">{{ errorMessage }}</li>
                </ul>
            </mat-error>
            <button
                type="button" mat-flat-button color="primary"
                [disabled]="actionsLoading || !canCreateAudit || !auditTemplates.length ||
                (auditTemplates.length && templateWithoutCriteriaCounter > 0 && auditTemplates.length === templateWithoutCriteriaCounter)"
                (click)="createAudit()">
                {{ 'private.certification.audit.' + (certificationPartnerAudits?.length ? 'create' : 'createFirst')| translate }}
            </button>
        </div>
    </div>
</mat-card>

