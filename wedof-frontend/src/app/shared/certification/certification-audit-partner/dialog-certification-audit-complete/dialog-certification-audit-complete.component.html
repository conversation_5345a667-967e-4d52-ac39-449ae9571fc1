<app-dialog-layout
    [title]=" 'private.certification.audit.complete' | translate"
    [actions]="actions"
    (dialogClose)="closeModal()">
    <form [formGroup]="formGroup" class="flex flex-col">
        <app-form-fields class="grid grid-cols-6 gap-2"
                         formGroupName="certificationAuditForm"
                         [entity]="certificationPartnerAudit"
                         [appFormFieldsData]="appFormFieldsData"
                         [formGroup]="formGroup"></app-form-fields>
        <div *ngIf="errorMessages?.length" class="flex items-center">
            <treo-message class="flex-auto" appearance="outline" [showIcon]="false" type="error">
                <ul>
                    <li *ngFor="let errorMessage of errorMessages">
                        {{ errorMessage }}
                    </li>
                </ul>
            </treo-message>
        </div>
        <treo-message type="warn"
                      class="flex-auto mt-2 mb-2"
                      *ngIf="!certificationPartnerAudit.report"
                      [showIcon]="false"
                      appearance="outline">
            <p>{{ 'private.certification.audit.completeNoReport' | translate }}</p>
        </treo-message>
        <treo-message type="warn"
                      class="flex-auto mt-2 mb-2"
                      *ngIf="certificationPartnerAudit.report"
                      [showIcon]="false"
                      appearance="outline">
            <p>{{ 'private.certification.audit.completeNoUpdatable' | translate }}</p>
        </treo-message>
        <ng-template #actions>
            <ng-container *ngIf="showSuspension && dialogData.certificationPartner.state === certificationPartnerStateActive; else showComplete">
                <div class="flex align-center">
                    <button type="button" mat-flat-button
                            color="primary"
                            [disabled]="loading || formGroup.invalid"
                            class="flex justify-center items-center button-actions"
                            (click)=complete(true)>
                        {{ 'private.certification.audit.completeAndSuspended' | translate }}
                    </button>
                </div>
                <button class="flex justify-center items-center button-arrow button-arrow-primary"
                        color="primary"
                        (click)="$event.stopPropagation()"
                        mat-flat-button
                        [disabled]="loading || formGroup.invalid"
                        [matMenuTriggerFor]="menu"
                        title="Actions" type="button">
                    <mat-icon class="icon" svgIcon="arrow_drop_down"></mat-icon>
                </button>
            </ng-container>
            <ng-template #showComplete>
                <button type="button" class="flex align-center" mat-flat-button color="primary"
                        (click)="complete()"
                        [disabled]="loading || formGroup.invalid">
                    <mat-progress-spinner class="mr-4" *ngIf="loading" [diameter]="24"
                                          mode="indeterminate"></mat-progress-spinner>
                    <ng-container
                        *ngIf="!loading">{{ ('private.certification.audit.complete') | translate }}
                    </ng-container>
                </button>
            </ng-template>
        </ng-template>
    </form>
</app-dialog-layout>

<mat-menu #menu="matMenu">
    <ng-template matMenuContent>
        <button mat-menu-item (click)="complete()">
            <span>{{ 'private.certification.audit.complete' | translate}}</span>
        </button>
    </ng-template>
</mat-menu>
