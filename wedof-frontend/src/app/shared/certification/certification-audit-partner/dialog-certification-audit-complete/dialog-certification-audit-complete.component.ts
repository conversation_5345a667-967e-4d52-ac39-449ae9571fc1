import {Component, Inject, <PERSON><PERSON><PERSON><PERSON>, OnInit} from '@angular/core';
import {Form<PERSON>uilder, FormGroup} from '@angular/forms';
import {MAT_DIALOG_DATA, MatDialog, MatDialogRef} from '@angular/material/dialog';
import {HttpErrorResponse} from '@angular/common/http';
import {AppFormFieldData} from '../../../material/app-form-field/app-form-field.component';
import {Certification, CertificationTypes} from '../../../api/models/certification';
import {ApiError} from '../../../errors/errors.types';
import {
    CertificationPartnerAudit,
    CertificationPartnerAuditCompleteBody,
    CertificationPartnerAuditCriteriaCompliances,
    CertificationPartnerAuditCriteriaSeverity,
    CertificationPartnerAuditResults
} from '../../../api/models/certification-partner-audit';
import {CertificationPartnerAuditService} from '../../../api/services/certification-partner-audit.service';
import {CertificationPartner, CertificationPartnerStates} from '../../../api/models/certification-partner';
import {TranslateService} from '@ngx-translate/core';
import {CertificationPartnerAuditResultToIconPipe} from '../../../pipes/certification-partner-audit-state-to-icon.pipe';
import {ActionConfirmationComponent} from '../../../material/action-confirmation/action-confirmation.component';
import {filter, finalize, switchMap, takeUntil} from 'rxjs/operators';
import {CertificationPartnerService} from '../../../api/services/certification-partner.service';
import {combineLatest, Observable, Subject} from 'rxjs';
import {Connection, ConnectionState, DataProviders} from '../../../api/models/connection';
import {Select} from '@ngxs/store';
import {ConnectionsState} from '../../../api/state/connections.state';
import {DialogConnectionAuthComponent} from '../../../connection/dialog-connection-auth/dialog-connection-auth.component';
import {Organism} from '../../../api/models/organism';
import {OrganismState} from '../../../api/state/organism.state';

@Component({
    selector: 'app-dialog-certification-audit-complete',
    templateUrl: './dialog-certification-audit-complete.component.html',
    styleUrls: ['./dialog-certification-audit-complete.component.scss']
})
export class DialogCertificationAuditCompleteComponent implements OnInit, OnDestroy {

    certificationPartnerAudit: CertificationPartnerAudit;
    loading = false;
    formGroup: FormGroup;
    errorMessages: string[] = [];
    appFormFieldsData: AppFormFieldData[];
    showSuspension = false;
    certificationPartnerStateActive = CertificationPartnerStates.ACTIVE;

    franceCompetencesConnection: Connection;
    currentOrganism: Organism;
    @Select(OrganismState.organism) organism$: Observable<Organism>;
    @Select(ConnectionsState.connections) connections$: Observable<Connection[]>;
    private _unsubscribeAll = new Subject<void>();

    constructor(public dialogRef: MatDialogRef<DialogCertificationAuditCompleteComponent>,
                private _certificationPartnerAuditService: CertificationPartnerAuditService,
                private _formBuilder: FormBuilder,
                private _certificationPartnerAuditResultToIconPipe: CertificationPartnerAuditResultToIconPipe,
                private _certificationPartnerService: CertificationPartnerService,
                private _translateService: TranslateService,
                private _dialog: MatDialog,
                @Inject(MAT_DIALOG_DATA) public dialogData: {
                    certificationPartnerAudit: CertificationPartnerAudit
                    certification: Certification,
                    certificationPartner: CertificationPartner
                }
    ) {
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    ngOnInit(): void {
        combineLatest([
            this.connections$,
            this.organism$
        ]).pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe(([connections, currentOrganism]) => {
            this.franceCompetencesConnection = connections.find(connection => connection.dataProvider === DataProviders.FRANCE_COMPETENCES);
            this.currentOrganism = currentOrganism;
            this.certificationPartnerAudit = this.dialogData.certificationPartnerAudit;
            const iconsColorByResult = {
                [CertificationPartnerAuditResults.COMPLIANT]: undefined,
                [CertificationPartnerAuditResults.PARTIALLY_COMPLIANT]: undefined,
                [CertificationPartnerAuditResults.NON_COMPLIANT]: 'warn'
            };
            const iconsClassByResult = {
                [CertificationPartnerAuditResults.COMPLIANT]: 'text-green',
                [CertificationPartnerAuditResults.PARTIALLY_COMPLIANT]: 'text-orange',
                [CertificationPartnerAuditResults.NON_COMPLIANT]: undefined
            };
            const resultChoices = Object.values(CertificationPartnerAuditResults)
                .filter((result) => result !== CertificationPartnerAuditResults.NONE).map(value => ({
                    key: this._translateService.instant('private.certification.audit.result.' + value),
                    value: value,
                    icon: this._certificationPartnerAuditResultToIconPipe.transform(value),
                    color: iconsColorByResult[value],
                    iconClass: iconsClassByResult[value]
                }));
            this.formGroup = this._formBuilder.group({
                certificationAuditForm: this._formBuilder.group({})
            });
            const appFormFieldsData: AppFormFieldData[] = [
                {
                    controlName: 'result',
                    required: true,
                    label: 'private.certification.audit.result.title',
                    type: 'select',
                    choices: resultChoices,
                    value: this.resultGuessing(),
                    change: (controlName, newValue, formData) => {
                        this.showSuspension = newValue === CertificationPartnerAuditResults.NON_COMPLIANT && this.dialogData.certification.enabled;
                        return [];
                    },
                },
                {
                    controlName: 'updatePartnerCompliance',
                    required: true,
                    label: 'private.certification.audit.updatePartnerCompliance',
                    type: 'radio',
                    inline: true,
                    choices: [
                        {key: this._translateService.instant('common.actions.yes'), value: true},
                        {key: this._translateService.instant('common.actions.no'), value: false}
                    ],
                },
                {
                    controlName: 'notes',
                    label: 'private.certification.audit.notes',
                    type: 'textarea',
                    maxLength: 2500
                }
            ];
            this.appFormFieldsData = appFormFieldsData.filter(value => value != null);
        });
    }

    closeModal(): void {
        this.dialogRef.close();
    }

    complete(suspend: boolean = false): void {
        this.loading = true;
        this.errorMessages = [];
        const certificationAuditFormValues = this.formGroup.getRawValue().certificationAuditForm;
        const certificationPartnerAuditComplete: CertificationPartnerAuditCompleteBody = {
            result: certificationAuditFormValues.result,
            notes: certificationAuditFormValues.notes,
            updatePartnerCompliance: certificationAuditFormValues.updatePartnerCompliance
        };
        this._certificationPartnerAuditService.complete(
            this.dialogData.certification.certifInfo,
            this.dialogData.certificationPartner._links.partner.siret,
            this.certificationPartnerAudit.id,
            certificationPartnerAuditComplete
        ).subscribe(
            (updatedCertificationPartnerAudit) => {
                if (suspend) {
                    if ((this.franceCompetencesConnection && this.franceCompetencesConnection.state === ConnectionState.ACTIVE) ||
                        this.dialogData.certification.type === CertificationTypes.INTERNAL) {
                        this.suspendPartner(updatedCertificationPartnerAudit, certificationAuditFormValues.updatePartnerCompliance);
                    } else {
                        this.showFranceCompetencesConnectionDialog().subscribe(connectionOk => {
                            if (connectionOk) {
                                this.suspendPartner(updatedCertificationPartnerAudit, certificationAuditFormValues.updatePartnerCompliance);
                            } else {
                                this.dialogRef.close({
                                    data: updatedCertificationPartnerAudit,
                                    updatePartnerCompliance: certificationAuditFormValues.updatePartnerCompliance
                                });
                            }
                        });
                    }
                } else {
                    this.dialogRef.close({
                        data: updatedCertificationPartnerAudit,
                        updatePartnerCompliance: certificationAuditFormValues.updatePartnerCompliance
                    });
                }
            },
            (httpErrorResponse: HttpErrorResponse) => {
                this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
            }
        ).add(() => this.loading = false);
    }

    private resultGuessing(): CertificationPartnerAuditResults {
        // Guess the same way as automated evaluateResult in back
        let result = CertificationPartnerAuditResults.COMPLIANT;
        const evaluatedCriterias = this.certificationPartnerAudit.evaluatedCriterias;
        for (const evaluatedCriteria of evaluatedCriterias) { // For is required so we can "break"
            if (evaluatedCriteria.severity !== CertificationPartnerAuditCriteriaSeverity.NONE) {
                if (evaluatedCriteria.compliance === CertificationPartnerAuditCriteriaCompliances.NON_COMPLIANT) {
                    if (evaluatedCriteria.severity === CertificationPartnerAuditCriteriaSeverity.MAJEURE) {
                        result = CertificationPartnerAuditResults.NON_COMPLIANT; // Cannot be overriden so break
                        break;
                    }
                    result = CertificationPartnerAuditResults.PARTIALLY_COMPLIANT; // Can be overriden
                } else if (evaluatedCriteria.compliance === CertificationPartnerAuditCriteriaCompliances.NOT_APPLICABLE) {
                    result = CertificationPartnerAuditResults.PARTIALLY_COMPLIANT; // Can be overriden
                }
            }
        }
        return result;
    }

    suspendPartner(updatedCertificationPartnerAudit: CertificationPartnerAudit, updatePartnerCompliance: boolean): void {
        this.loading = true;
        const certifInfo = this.dialogData.certification.certifInfo;
        const siret = this.dialogData.certificationPartner._links.partner.siret;
        const dialogRef = this._dialog.open(ActionConfirmationComponent, {
            panelClass: 'full-page-scroll-30',
            height: 'auto',
            data: {
                messageKey: 'private.common.certification.managePartnership.confirm.pendingSuspension'
            }
        });
        let confirm: boolean;
        dialogRef.componentInstance.actionValue$.pipe(
            filter((confirmation: boolean) => {
                confirm = confirmation;
                return confirmation;
            }),
            switchMap(() =>
                this._certificationPartnerService.update(certifInfo, siret, {'pendingSuspension': true})
            ),
            finalize(() => {
                dialogRef.componentInstance.close();
                if (!confirm) {
                    this.dialogRef.close({
                        data: updatedCertificationPartnerAudit,
                        updatePartnerCompliance: updatePartnerCompliance
                    });
                }
            })
        ).subscribe((certificationPartnerUpdated) => {
            this.dialogRef.close({
                data: updatedCertificationPartnerAudit,
                updatePartnerCompliance: updatePartnerCompliance,
                updatedPartner: certificationPartnerUpdated
            });
        });
    }

    private showFranceCompetencesConnectionDialog(): Observable<boolean> {
        const dialogRef = this._dialog.open(DialogConnectionAuthComponent, {
            disableClose: true,
            panelClass: ['no-padding-panel', 'full-page-scroll-40'],
            height: 'auto',
            data: {
                dataProvider: DataProviders.FRANCE_COMPETENCES,
                organism: this.currentOrganism
            }
        });
        return dialogRef.afterClosed();
    }
}
