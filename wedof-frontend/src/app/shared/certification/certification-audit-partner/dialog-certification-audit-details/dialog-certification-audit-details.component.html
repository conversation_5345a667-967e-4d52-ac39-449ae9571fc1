<app-dialog-layout
    [title]="dialogData.certificationPartnerAudit._links.certificationPartnerAuditTemplate.name"
    [showCancel]="false"
    [disabled]="loading"
    (dialogClose)="closeModal()">

    <treo-message class="mt-2 mb-2" *ngIf="dialogData.certificationPartnerAudit.errorMessage" appearance="outline"
                  [showIcon]="false" type="error">
        {{ dialogData.certificationPartnerAudit.errorMessage }}
    </treo-message>

    <p>
        <span class="font-medium">{{ 'private.certification.audit.criteriaCountTitle'| translate }} :</span>
        {{ getCompliantCriteriaCount(dialogData.certificationPartnerAudit) }}
        / {{ dialogData.certificationPartnerAudit.evaluatedCriterias?.length }}
    </p>
    <p>
        <span class="font-medium">{{ 'private.certification.audit.startDate'| translate }} :</span>
        {{ dialogData.certificationPartnerAudit.startDate |  date: 'dd/MM/yyyy à HH:mm' }}
    </p>
    <ng-container *ngIf="dialogData.certificationPartnerAudit.state === certificationPartnerAuditStates.COMPLETED">
        <p>
            <span class="font-medium">{{ 'private.certification.audit.endDate'| translate }} :</span>
            {{ dialogData.certificationPartnerAudit.endDate |  date: 'dd/MM/yyyy à HH:mm' }}
        </p>
        <p>
            <span class="font-medium">{{ 'private.certification.audit.result.title'| translate }} :</span>
            {{ 'private.certification.audit.result.' + dialogData.certificationPartnerAudit.result | translate }}
        </p>
        <p *ngIf="dialogData.certificationPartnerAudit.notes">
            <span class="font-medium">{{ 'private.certification.audit.notes'| translate }} :</span>
            {{ dialogData.certificationPartnerAudit.notes }}
        </p>
    </ng-container>

    <table class="mt-4" mat-table [dataSource]="dialogData.certificationPartnerAudit.evaluatedCriterias">
        <ng-container matColumnDef="icon">
            <th mat-header-cell class="pl-0"
                *matHeaderCellDef>{{ 'private.certification.audit.evaluatedCriterias.criteria' | translate }}</th>
            <td *matCellDef="let evaluatedCriteria" mat-cell class="w-5">
                <mat-icon *ngIf="evaluatedCriteria.compliance" class="icon-small"
                          [matTooltip]="'private.certification.audit.criteriaCompliance.' + evaluatedCriteria.compliance | translate"
                          [class]="getIcon(evaluatedCriteria).class"
                          [color]="getIcon(evaluatedCriteria).color">
                    {{ getIcon(evaluatedCriteria).icon }}
                </mat-icon>
            </td>
        </ng-container>
        <ng-container matColumnDef="title">
            <th mat-header-cell class="pl-0"
                *matHeaderCellDef>{{ 'private.common.certification.audit.criterias.form.title' | translate }}
            </th>
            <td *matCellDef="let evaluatedCriteria" mat-cell class="w-3/12">
                {{ evaluatedCriteria.title }}
            </td>
        </ng-container>
        <ng-container matColumnDef="severity">
            <th mat-header-cell class="pl-0"
                *matHeaderCellDef>{{ 'private.certification.audit.evaluatedCriterias.severity' | translate }}
            </th>
            <td *matCellDef="let evaluatedCriteria" mat-cell class="w-1/12">
                <mat-icon
                    *ngIf="evaluatedCriteria.compliance !== auditCriteriaCompliances.COMPLIANT && evaluatedCriteria.severity && evaluatedCriteria.severity !== auditCriteriaSeverity.NONE"
                    class="icon-small"
                    [matTooltip]="'private.common.certification.audit.criterias.form.severity.tooltip.' + evaluatedCriteria.severity | translate"
                    [color]="evaluatedCriteria.severity === auditCriteriaSeverity.MAJEURE ? 'warn' : undefined"
                    [class]="evaluatedCriteria.severity === auditCriteriaSeverity.MAJEURE ? '' : 'text-orange'">
                    {{ evaluatedCriteria.severity === auditCriteriaSeverity.MAJEURE ? 'priority_high' : 'warning' }}
                </mat-icon>
            </td>
        </ng-container>
        <ng-container matColumnDef="requirement">
            <th mat-header-cell class="pl-0"
                *matHeaderCellDef>{{ 'private.certification.audit.evaluatedCriterias.requirement' | translate }}
            </th>
            <td *matCellDef="let evaluatedCriteria" mat-cell class="w-2/6">
                <div class="flex items-center">
                    <p [innerHTML]="evaluatedCriteria | certificationPartnerAuditCriteriaRequirement"></p>
                    <mat-icon
                        *ngIf="evaluatedCriteria.advice &&
                         (evaluatedCriteria.compliance === auditCriteriaCompliances.NON_COMPLIANT || evaluatedCriteria.compliance === auditCriteriaCompliances.PARTIALLY_COMPLIANT)"
                        class="icon-small ml-1"
                        [matTooltip]="'private.certification.audit.evaluatedCriterias.advice' | translate : {advice: evaluatedCriteria.advice} ">
                        help_outline
                    </mat-icon>
                </div>
            </td>
        </ng-container>
        <ng-container matColumnDef="value">
            <th mat-header-cell class="pl-0"
                *matHeaderCellDef>{{ 'private.certification.audit.evaluatedCriterias.value' | translate }}
            </th>
            <td *matCellDef="let evaluatedCriteria" mat-cell
                class="w-4/6 criteria-value">
                <ng-container
                    *ngIf="isObjectValue(evaluatedCriteria) && evaluatedCriteria.compliance !== auditCriteriaCompliances.NOT_APPLICABLE else rawValue">
                    {{ displayObjectValue(evaluatedCriteria)['title'] }}
                    <app-ellipsis-text *ngIf="displayObjectValue(evaluatedCriteria)['text']"
                                       class="break-words min-h-10 block"
                                       [copy]="true"
                                       [allowShowMore]="true"
                                       [showMinText]="displayObjectValue(evaluatedCriteria)['showMinText']"
                                       [showToolTipValue]="false"
                                       [matTooltip]="displayObjectValue(evaluatedCriteria)['justification']"
                                       [matTooltipDisabled]="!displayObjectValue(evaluatedCriteria)['justification'] || !dialogData.isCertifier"
                                       [text]="displayObjectValue(evaluatedCriteria)['text']"
                                       [maxLength]="300"></app-ellipsis-text>
                </ng-container>
                <ng-template #rawValue>
                    <span
                        *ngIf="evaluatedCriteria.value != null && evaluatedCriteria.compliance !== auditCriteriaCompliances.NOT_APPLICABLE else nonApplicable"
                        class="font-semibold">
                        {{ evaluatedCriteria.value }}
                    </span>
                    <ng-template #nonApplicable>
                        <span class="text-secondary">
                            {{ 'private.certification.audit.evaluatedCriterias.nonApplicable' | translate }}
                        </span>
                    </ng-template>
                </ng-template>
            </td>
        </ng-container>
        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr *matRowDef="let row; columns: displayedColumns ;" mat-row></tr>
    </table>

    <div class="mt-4"
         *ngIf="environmentService.isEnableBetaFeatures(dialogData.currentOrganism) &&
                    !dialogData.isCertifier && dialogData.currentOrganism.isTrainingOrganism && dialogData.certificationPartnerAudit.result === auditResults.NON_COMPLIANT">
        <treo-message type="info"
                      [showIcon]="false" class="mb-4 mt-2 text-center" appearance="outline">
            <a (click)="openDialogExportCatalog()">
                {{'private.common.organism.catalog.export.partners' | translate }}
            </a>
        </treo-message>
    </div>

    <div class="mt-4 flex justify-end">
        <mat-error *ngIf="errorMessages.length" class="mb-4">
            <ul>
                <li *ngFor="let errorMessage of errorMessages">{{ errorMessage }}</li>
            </ul>
        </mat-error>
        <app-certification-audit-partner-menu [isCertifier]="dialogData.isCertifier"
                                              [withButton]="true"
                                              [certificationPartnerAudit]="dialogData.certificationPartnerAudit"
                                              [certificationPartner]="dialogData.certificationPartner"
                                              [certification]="dialogData.certification"
                                              (certificationPartnerChange)="dialogData.certificationPartnerChange($event)"
                                              (certificationPartnerAuditUpdated)="dialogData.certificationPartnerAuditChange($event)"
                                              (errorMessages)="setErrorMessages($event)"
                                              (loading)="setLoading($event)"
                                              (refresh)="refreshAudit($event)"
                                              (delete)="deleteAudit($event)">
        </app-certification-audit-partner-menu>
    </div>

</app-dialog-layout>
