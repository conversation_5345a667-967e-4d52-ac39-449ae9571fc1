import {Component, Inject} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialog, MatDialogRef} from '@angular/material/dialog';
import {
    CertificationPartnerAudit,
    CertificationPartnerAuditCriteriaCompliances,
    CertificationPartnerAuditCriteriaSeverity,
    CertificationPartnerAuditEvaluatedCriteria,
    CertificationPartnerAuditResults,
    CertificationPartnerAuditStates,
    CriteriaObjectValue
} from '../../../api/models/certification-partner-audit';
import {TranslateService} from '@ngx-translate/core';
import {CertificationPartner} from '../../../api/models/certification-partner';
import {Certification} from '../../../api/models/certification';
import {Observable} from 'rxjs';
import {ThemePalette} from '@angular/material/core';
import {CertificationPartnerAuditResultToIconPipe} from '../../../pipes/certification-partner-audit-state-to-icon.pipe';
import {Organism} from '../../../api/models/organism';
import {Subscription, SubscriptionTrainingTypes} from '../../../api/models/subscription';
import {DisclaimerComponent} from '../../../material/disclaimer/disclaimer.component';
import {SnackBarComponent} from '../../../material/snack-bar/snack-bar.component';
import {displayTextSnackBar} from '../../../utils/displayTextSnackBar';
import {MatSnackBar} from '@angular/material/snack-bar';
import {EnvironmentService} from '../../../environment/environment.service';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../../errors/errors.types';
import {OrganismService} from '../../../api/services/organism.service';

@Component({
    selector: 'app-dialog-certification-audit-details',
    templateUrl: './dialog-certification-audit-details.component.html',
    styleUrls: ['./dialog-certification-audit-details.component.scss']
})
export class DialogCertificationAuditDetailsComponent {

    loading = false;
    errorMessages: string[] = [];
    certificationPartnerAuditStates = CertificationPartnerAuditStates;
    auditCriteriaCompliances = CertificationPartnerAuditCriteriaCompliances;
    auditCriteriaSeverity = CertificationPartnerAuditCriteriaSeverity;
    displayedColumns: string[] = ['icon', 'title', 'severity', 'requirement', 'value'];
    auditResults = CertificationPartnerAuditResults;

    constructor(public dialogRef: MatDialogRef<DialogCertificationAuditDetailsComponent>,
                public environmentService: EnvironmentService,
                private _translateService: TranslateService,
                private _dialog: MatDialog,
                private _snackBar: MatSnackBar,
                private _organismService: OrganismService,
                private _certificationPartnerAuditResultToIconPipe: CertificationPartnerAuditResultToIconPipe,
                @Inject(MAT_DIALOG_DATA) public dialogData: {
                    certificationPartnerAudit: CertificationPartnerAudit,
                    certificationPartner: CertificationPartner,
                    certification: Certification,
                    isCertifier: boolean,
                    currentOrganism: Organism,
                    subscription: Subscription
                    certificationPartnerChange(certificationPartnerUpdated: CertificationPartner): Observable<CertificationPartner>,
                    certificationPartnerAuditChange(certificationPartnerAuditUpdated: CertificationPartnerAudit): Observable<CertificationPartnerAudit>,
                    delete(certificationPartnerAuditToDelete: CertificationPartnerAudit): void,
                }
    ) {
    }

    getCompliantCriteriaCount(certificationPartnerAudit: CertificationPartnerAudit): number {
        return certificationPartnerAudit.evaluatedCriterias.filter(evaluatedCriteria =>
            evaluatedCriteria.compliance === CertificationPartnerAuditCriteriaCompliances.COMPLIANT
        ).length;
    }

    getIcon(evaluatedCriteria: CertificationPartnerAuditEvaluatedCriteria): { class: string, color: ThemePalette, icon: string } {
        const icon = {
            class: '',
            icon: undefined,
            color: undefined
        };
        switch (evaluatedCriteria.compliance) {
            case CertificationPartnerAuditCriteriaCompliances.COMPLIANT:
                icon.class = 'text-green';
                icon.icon = this._certificationPartnerAuditResultToIconPipe.transform(evaluatedCriteria.compliance);
                break;
            case CertificationPartnerAuditCriteriaCompliances.NON_COMPLIANT:
                icon.color = 'warn';
                icon.icon = this._certificationPartnerAuditResultToIconPipe.transform(evaluatedCriteria.compliance);
                break;
            case CertificationPartnerAuditCriteriaCompliances.PARTIALLY_COMPLIANT:
                icon.class = 'text-orange';
                icon.icon = this._certificationPartnerAuditResultToIconPipe.transform(evaluatedCriteria.compliance);
                break;
            case CertificationPartnerAuditCriteriaCompliances.NOT_APPLICABLE:
                icon.icon = 'error_outline';
                break;
        }
        return icon;
    }

    isObjectValue(evaluatedCriteria: CertificationPartnerAuditEvaluatedCriteria): boolean {
        return evaluatedCriteria.value != null && typeof evaluatedCriteria.value === 'object';
    }

    displayObjectValue(evaluatedCriteria: CertificationPartnerAuditEvaluatedCriteria): { title: string, text?: string, showMinText?: boolean, justification?: string} {
        const objectValue: CriteriaObjectValue = evaluatedCriteria.value as CriteriaObjectValue;
        const values = objectValue.notCompliant;
        if (values.length) {
            const text = [];
            const justification = [];
            let showMinText = true;
            values.forEach((value) => {
                if (typeof value === 'string') {
                    text.push(value);
                } else {
                    showMinText = false;
                    if (value.justification && this.dialogData.isCertifier){
                       justification.push(value.id + ': ' + value.justification);
                    }
                    if (value.link){
                        text.push('<a class="underline" href="' + value.link + '" target="_blank">' + value.id + '</a>');
                    } else {
                        text.push(value.id);
                    }
                }
            });
            return {
                title: values.length + '/' + objectValue.total + ' non conformes : ',
                text: text.join(', '),
                showMinText: showMinText,
                justification : justification.length > 0 ? justification.join('\n\n') : undefined
            };
        } else {
            return {
                title: objectValue.total + '/' + objectValue.total + ' conformes'
            };
        }
    }

    closeModal(): void {
        this.dialogRef.close();
    }

    setLoading(loading: boolean): void {
        this.loading = loading;
    }

    setErrorMessages(error: string[]): void {
        this.errorMessages = error;
    }

    deleteAudit(certificationPartnerAuditToDelete: CertificationPartnerAudit): void {
        this.dialogData.delete(certificationPartnerAuditToDelete);
        this.closeModal();
    }

    refreshAudit(certificationPartnerAudit: CertificationPartnerAudit): void {
        this.dialogData.certificationPartnerAudit = certificationPartnerAudit;
    }

    openDialogExportCatalog(): void {
        const subscription = this.dialogData.subscription;
        const hasExportCatalogAllow = [SubscriptionTrainingTypes.STANDARD, SubscriptionTrainingTypes.PREMIUM, SubscriptionTrainingTypes.ACCESS].includes(subscription.trainingType);
        const mainButtonTitle = hasExportCatalogAllow ? 'private.common.organism.catalog.export.button.export' : 'private.common.organism.catalog.export.button.subscribeOnce';
        this._dialog.open(DisclaimerComponent, {
            panelClass: ['full-page-scroll-30'],
            data: {
                title: 'private.common.organism.catalog.export.title',
                subtitle: 'private.common.organism.catalog.export.subtitle',
                mainButton: {
                    title: mainButtonTitle,
                    method: () => {
                        if (hasExportCatalogAllow) {
                            this._organismService.exportCpfCatalogXml(this.dialogData.currentOrganism).subscribe(
                                () => {
                                    this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(
                                        this._translateService.instant('private.common.organism.catalog.export.success'), 5000));
                                },
                                (httpErrorResponse: HttpErrorResponse) => {
                                    this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
                                }
                            );
                        } else { // todo replace by stripe payment one shot
                            this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(
                                this._translateService.instant('common.actions.comingSoon'), 5000));
                        }
                    }
                },
                secondaryButton: null
            }
        });
    }
}
