<mat-card class="flex-col mt-3 p-5 pb-0 pt-3 border shadow-none sm">
    <div class="flex items-center mb-2">
        <mat-icon class="mr-3 card-loading-show icon-size-32" svgIcon="analytics" color="primary">
        </mat-icon>
        <div>
            <div
                class="text-xl font-semibold card-loading-show">{{ title ? title : ('private.common.certification.certifInfo' | translate: certification)
                + (!certification.enabled ? (' - ' + ('private.certification.partners.enabled.' + certification.enabled | translate)) : '') }}
            </div>
            <div *ngIf="certification.updatedOn"
                 class="text-secondary text-md card-loading-show">{{
                'private.common.certification.updatedOn' | translate: {
                    date: (certification.updatedOn | date:'mediumDate')
                }
                }}
            </div>
        </div>
    </div>
    <treo-message type="info" *ngIf="isDefaultCertifier && certification.hasMultipleCertifiers"
                  [showIcon]="false" class="mb-4 mt-2 text-center" appearance="outline">
        {{ 'private.certification.common.coCertifiers' | translate}}
    </treo-message>
    <div class="flex flex-col mb-2 card-loading-hidden">
        <app-form-field-static type="text" icon="info"
                               [value]="certification.externalId"></app-form-field-static>
        <app-form-field-static type="text" icon="info"
                               [value]="'private.common.certification.certifInfo' | translate : {certifInfo : certification.certifInfo}"></app-form-field-static>
        <app-form-field-static
            *ngIf="isDefaultCertifier"
            [value]="certification.certificationInfo.folderCount"
            label="{{'private.common.certification.certificationFolders' | translate}}"
            icon="folder"
            type="text"></app-form-field-static>
        <app-form-field-static
            *ngIf="isDefaultCertifier && certification?.openDataLastUpdate"
            [help]="'private.common.certification.estimatedRegistrationFoldersCountToolTip'"
            [value]="certification.estimatedRegistrationFoldersCount"
            label="{{'private.common.certification.estimatedRegistrationFoldersCount' | translate}} {{certification.openDataLastUpdate | date}}"
            icon="folder"
            type="text"></app-form-field-static>
        <app-form-field-static *ngIf="!isDefaultCertifier"
                               type="url" icon="link" [href]="getCertificationLink(certification)"
                               [value]="getCertificationLink(certification)"></app-form-field-static>
        <p *ngIf="certification.type !== certificationTypes.INTERNAL"
           class="pt-2 pb-2">{{ "private.common.certification.modalities" | translate }} <a
            href="{{ getCertificationLink(certification) }}" target="_blank"
            class="underline">{{ "private.common.certification.modalitiesLink" | translate }}</a></p>
    </div>

    <mat-chip-list class="card-loading-hidden ">
        <mat-chip
            class="cursor-pointer"
            *ngFor="let code of codes" (click)=" checkTag(code) ; $event.preventDefault(); $event.stopPropagation() "
            [matTooltip]="code.name">
            {{ code.code }}
            <mat-icon *ngIf="code.link" class=" pl-1 icon-small icon-copy"
                      (click)="goToLinkRome(code.link); $event.preventDefault(); $event.stopPropagation();"
                      [svgIcon]="'open_in_new'"></mat-icon>
        </mat-chip>
    </mat-chip-list>

    <form [formGroup]="formGroup" (submit)="submit()" class="flex flex-col mt-2"
          *ngIf="isDefaultCertifier && !loading; else showForPartner">
        <app-form-fields class="grid grid-cols-6 gap-2 mb-3"
                         formGroupName="certification"
                         [entity]="certification"
                         [appFormFieldsData]="appFormFieldsData"
                         [formGroup]="formGroup">
        </app-form-fields>
        <ng-template class="mb-2"
                     [ngTemplateOutlet]="showAppFileList"
                     [ngTemplateOutletContext]="{isOwner: isDefaultCertifier, showManageFile: true, removeVisibilityForPartner: false }">
        </ng-template>
        <div
            class="flex items-center mt-2 justify-end border-t pl-5 pr-10 -mx-5 py-3 light:bg-cool-gray-50 dark:bg-cool-gray-700 card-loading-hidden">
            <div class="flex">
                <mat-error *ngIf="errorMessages.length">
                    <ul>
                        <li *ngFor="let errorMessage of errorMessages">{{ errorMessage }}</li>
                    </ul>
                </mat-error>
                <button type="submit" class="flex align-center" mat-flat-button color="primary"
                        [disabled]="loading || !formGroup.dirty || formGroup.invalid">
                    <mat-progress-spinner class="mr-4" *ngIf="loading" [diameter]="24"
                                          mode="indeterminate"></mat-progress-spinner>
                    <ng-container
                        *ngIf="!loading">{{ 'common.actions.update' | translate }}
                    </ng-container>
                </button>
            </div>
        </div>
    </form>

    <ng-template #showForPartner>
        <ng-container *ngIf="!loading">
            <div class="flex flex-col mt-2 mb-3">
                <app-form-field-static icon="info" type="text" *ngIf="certification?.validityPeriod"
                                       [label]="'private.common.certification.validityPlaceholder' | translate"
                                       [value]="certification?.validityPeriod">
                </app-form-field-static>
                <app-form-field-static icon="devices" type="text" *ngIf="certification?.obtentionSystem"
                                       [label]="'private.common.certification.obtentionSystem.title' | translate"
                                       [value]="'private.common.certification.obtentionSystem.' +
                               (certification.obtentionSystem === obtentionSystem.PAR_SCORING  ? 'scoring' : 'admission')| translate ">
                </app-form-field-static>
                <app-form-field-static icon="school" type="text" *ngIf="certification?.examinationType"
                                       [label]="'private.common.certification.examinationType.title' | translate"
                                       [value]=" 'private.common.certification.examinationType.' + certification?.examinationType | translate  ">
                </app-form-field-static>
            </div>
            <ng-template class="mb-2" *ngIf="certification?.files.length > 0"
                         [ngTemplateOutlet]="showAppFileList"
                         [ngTemplateOutletContext]="{isOwner: false, showManageFile: false, removeVisibilityForPartner: true }">
            </ng-template>
        </ng-container>
    </ng-template>
</mat-card>

<ng-template #showAppFileList let-isOwner="isOwner" let-showManageFile="showManageFile"
             let-removeVisibilityForPartner="removeVisibilityForPartner">
    <app-file-list *ngIf="showFiles"
                   #appFileList
                   [isOwner]="isOwner"
                   [showManageFile]="showManageFile"
                   [entity]="certification"
                   [fileTypes]="certifier?.certificationFileTypes"
                   [orderedStates]="orderedCertificationStates"
                   [manageFile]="false"
                   [getSecondaryText]="getSecondaryText"
                   [removeVisibilityForPartner]="removeVisibilityForPartner"
                   entityApiPath="certifications"
                   entityIdProperty="id"
                   class="mt-3"
                   titleStateChange="private.certification.folders.actions.">
    </app-file-list>
</ng-template>
