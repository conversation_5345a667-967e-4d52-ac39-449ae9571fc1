import {
    Component,
    ElementRef,
    EventEmitter,
    Input,
    OnChanges,
    OnDestroy,
    Output,
    SimpleChanges,
    ViewChild
} from '@angular/core';
import {Certification, CertificationStates, CertificationTypes, ObtentionSystem} from '../../api/models/certification';
import {CertificationService} from '../../api/services/certification.service';
import {finalize, takeUntil} from 'rxjs/operators';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../errors/errors.types';
import {SnackBarComponent} from '../../material/snack-bar/snack-bar.component';
import {TranslateService} from '@ngx-translate/core';
import {MatSnackBar} from '@angular/material/snack-bar';
import {displayTextSnackBar} from '../../utils/displayTextSnackBar';
import {AppFormFieldData} from '../../material/app-form-field/app-form-field.component';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {BaseCardComponentDirective, RequiredCallSuper} from '../../utils/base-card/base-card.directive';
import {FileListComponent} from '../../file/file-list/file-list.component';
import {Organism} from '../../api/models/organism';
import {OrganismService} from '../../api/services/organism.service';
import {FileType} from '../../api/models/file';
import {FormValidators} from '../../api/shared/form-validators';
import {SkillService} from '../../api/services/skill.service';
import {combineLatest, of, Subject} from 'rxjs';
import {SkillType} from '../../api/models/skill';

@Component({
    selector: 'app-certification-summary',
    templateUrl: './certification-summary.component.html',
    styleUrls: ['./certification-summary.component.scss']
})
export class CertificationSummaryComponent extends BaseCardComponentDirective implements OnChanges, OnDestroy {
    static COMPONENT_ID = 'certification';
    errorMessages: string[] = [];
    appFormFieldsData: AppFormFieldData[];
    formGroup: FormGroup;
    obtentionSystem = ObtentionSystem;
    codes: Array<{ name: string, code: string, link?: string }> = [];
    loading = false;
    certifier: Organism;
    orderedCertificationStates: CertificationStates[] = Object.values(CertificationStates);
    getSecondaryText: (fileType: FileType<Certification>) => string;
    certificationTypes = CertificationTypes;

    @Input() certification: Certification;
    @Input() title?: string;
    @Input() searchByTagAuthorized = false;
    @Input() showFiles: boolean;
    @Input() isDefaultCertifier: boolean;
    @Output() filterByCode: EventEmitter<string> = new EventEmitter<string>();
    @Output() certificationUpdated: EventEmitter<Certification> = new EventEmitter<Certification>();

    @ViewChild('appFileList') appFileList: FileListComponent<Certification, CertificationStates>;

    private _unsubscribeAll: Subject<void> = new Subject<void>();

    constructor(
        private _certificationService: CertificationService,
        private _translateService: TranslateService,
        private _snackBar: MatSnackBar,
        private _formBuilder: FormBuilder,
        private _organismService: OrganismService,
        private _skillService: SkillService,
        private _el: ElementRef
    ) {
        super(CertificationSummaryComponent.COMPONENT_ID, _el);
        this.getSecondaryText = (fileType: FileType<Certification>) => {
            return this._translateService.instant(fileType.allowVisibilityPublic ? 'private.common.files.certification.publicFiles' : 'private.common.files.certification.partnerFiles');
        };
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes.certification || changes.isDefaultCertifier) {
            this.codes = this.getCodes();
            this.panelLoading();
            this.initForm();
        }
    }

    submit(): void {
        this.errorMessages = [];
        this.loading = true;
        const certificationUpdatedForm = this.formGroup.getRawValue().certification;

        const certification: Certification = {
            ...this.certification, ...{
                validityPeriod: certificationUpdatedForm.setValidity === false ? null : certificationUpdatedForm.validityPeriod,
                obtentionSystem: certificationUpdatedForm.obtentionSystem,
                examinationType: certificationUpdatedForm.examinationType,
                amountHt: certificationUpdatedForm.amountHt,
                surveyOptional: certificationUpdatedForm.surveyOptional
            }
        };
        if (this.certification.type === this.certificationTypes.INTERNAL) {
            certification.link = certificationUpdatedForm.link;
        }
        if (this.certification.type === this.certificationTypes.RNCP) {
            certification.allowPartialSkillSets = certificationUpdatedForm.allowPartialSkillSets;
        }
        this._certificationService.update(certification).pipe(
            finalize(() => this.loading = false)
        ).subscribe(
            (updatedCertification) => {
                this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(this._translateService.instant('common.actions.certificationUpdatedSuccessfully', {
                        certificationName: updatedCertification.name
                    },
                )));
                this.certificationUpdated.emit(updatedCertification);
            },
            (httpErrorResponse: HttpErrorResponse) => {
                this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
            }
        );
    }

    getCertificationLink(certification: Certification): string {
        if (certification.link) {
            return certification.link;
        } else if ([CertificationTypes.RS, CertificationTypes.RNCP].includes(certification.type)) {
            return 'https://www.francecompetences.fr/recherche/' + certification.type.toLowerCase() + '/' + certification.code;
        }
    }

    protected initForm(): void {
        this.errorMessages = [];
        this.loading = true;
        combineLatest([
            this._organismService.listByUrl(this.certification._links.certifiers.href),
            this.certification.type === CertificationTypes.RNCP ? this._skillService.listByUrl(this.certification._links.skills.href) : of(null)
        ]).pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe(([certifiers, skills]) => {
            this.certifier = certifiers[0];
            if (!this.certification || !this.isDefaultCertifier) {
                this.loading = false;
                return;
            }
            this.formGroup = this._formBuilder.group({
                certification: this._formBuilder.group({})
            });
            const certificationLink = this.getCertificationLink(this.certification);
            const isInternalCertification = this.certification.type === this.certificationTypes.INTERNAL;
            const hasSkillSets = skills?.filter((skill) => skill.type === SkillType.SKILL_SET).length >= 1;
            this.appFormFieldsData = [
                {
                    controlName: 'link',
                    label: isInternalCertification ? 'private.common.certification.link' : '',
                    value: certificationLink,
                    type: isInternalCertification ? 'text' : 'url',
                    disabled: !isInternalCertification,
                    icon: 'link',
                    href: certificationLink ?? null,
                    validators: [Validators.pattern(FormValidators.URL_PATTERN)],
                    validatorsMessages: {
                        pattern: 'common.errors.url'
                    }
                },
                {
                    controlName: 'setValidity',
                    label: 'private.common.certification.validityPeriodSet',
                    value: !!this.certification.validityPeriod,
                    type: 'radio',
                    inline: true,
                    colSpan: this.certification.validityPeriod ? 4 : 6,
                    choices: [
                        {key: this._translateService.instant('common.actions.yes'), value: true},
                        {key: this._translateService.instant('common.actions.no'), value: false}
                    ],
                    change: (controlName, newValue, formData) => {
                        const appFormFieldSetValidity = formData.find(field => field.controlName === 'setValidity');
                        const appFormFieldValidityPeriod = formData.find(field => field.controlName === 'validityPeriod');
                        if (newValue === true) {
                            appFormFieldValidityPeriod.value = this.certification.validityPeriod ?? null;
                            appFormFieldValidityPeriod.removed = false;
                            appFormFieldValidityPeriod.required = true;
                            appFormFieldValidityPeriod.colSpan = 2;
                            appFormFieldSetValidity.colSpan = 4;

                        } else {
                            appFormFieldValidityPeriod.removed = true;
                            appFormFieldValidityPeriod.value = null;
                            appFormFieldSetValidity.colSpan = 6;
                        }
                        return [appFormFieldValidityPeriod];
                    },
                },
                {
                    controlName: 'validityPeriod',
                    removed: !this.certification.validityPeriod,
                    label: 'private.common.certification.validityPeriod',
                    type: 'number',
                    min: 1,
                    placeholder: 'private.common.certification.validityPeriod',
                    colSpan: 2,
                    suffix: 'années'
                },
                {
                    controlName: 'amountHt',
                    label: 'private.common.certification.amountHt',
                    type: 'number',
                    min: 1,
                    help: 'private.common.certification.amountHtTooltip',
                    placeholder: 'private.common.certification.amountHtPlaceholder',
                    icon: 'euro_symbol'
                },
                {
                    controlName: 'examinationType',
                    label: 'private.common.certification.examinationType.titleDefault',
                    type: 'radio',
                    colSpan: 3,
                    choices: [
                        {
                            key: this._translateService.instant('private.common.certification.examinationType.A_DISTANCE'),
                            value: 'A_DISTANCE'
                        },
                        {
                            key: this._translateService.instant('private.common.certification.examinationType.EN_PRESENTIEL'),
                            value: 'EN_PRESENTIEL'
                        },
                        {
                            key: this._translateService.instant('private.common.certification.examinationType.MIXTE'),
                            value: 'MIXTE'
                        },
                    ]
                },
                {
                    controlName: 'obtentionSystem',
                    label: 'private.common.certification.obtentionSystem.title',
                    type: 'radio',
                    colSpan: 3,
                    choices: [
                        {
                            key: this._translateService.instant('private.common.certification.obtentionSystem.scoring'),
                            value: 'PAR_SCORING'
                        },
                        {
                            key: this._translateService.instant('private.common.certification.obtentionSystem.admission'),
                            value: 'PAR_ADMISSION'
                        }
                    ]
                },
                {
                    controlName: 'surveyOptional',
                    label: 'private.common.certification.surveyOptional',
                    type: 'radio',
                    inline: true,
                    choices: [
                        {key: this._translateService.instant('common.actions.yes'), value: true},
                        {key: this._translateService.instant('common.actions.no'), value: false}
                    ]
                },
                {
                    controlName: 'allowPartialSkillSets',
                    label: 'private.common.certification.allowPartialSkillSets',
                    type: 'radio',
                    removed: !(this.certification.type === CertificationTypes.RNCP && hasSkillSets),
                    inline: true,
                    choices: [
                        {key: this._translateService.instant('common.actions.yes'), value: true},
                        {key: this._translateService.instant('common.actions.no'), value: false}
                    ]
                }
            ];
            this.loading = false;
            this.panelLoaded();
        });
    }

    getCodes(): Array<{ name: string, code: string, link?: string }> {
        return [
            ...this.certification.rome?.map(rome => {
                return {
                    ...rome,
                    code: rome.code.replace(/(<([^>]+)>)/ig, '')
                };
            }),
            ...this.certification.domains
        ];
    }

    goToLinkRome(url: string): any {
        return url ? window.open(url, '_blank') : null;
    }

    checkTag(code: { name: string; code: string; link?: string } | null): void {
        return this.searchByTagAuthorized ? this.filterByCode.emit(code.code) : null;
    }


    ngOnDestroy(): RequiredCallSuper {
        if (this._intersectionObserver) {
            this._intersectionObserver.disconnect();
        }
        return super.ngOnDestroy();
    }
}
