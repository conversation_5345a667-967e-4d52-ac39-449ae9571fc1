<mat-card class="flex-col mt-3 p-5 pb-0 pt-3 border shadow-none sm">
    <div class="flex items-center mb-2">
        <mat-icon class="mr-3 card-loading-show icon-size-32"
                  svgIcon="school"
                  color="primary">
        </mat-icon>
        <div>
            <div>
                <div
                    class="text-xl font-semibold card-loading-show">{{ 'private.certification.common.certificate.certificateTemplate' | translate }}</div>
                <div class="text-secondary text-md card-loading-hidden">
                    {{'private.certification.common.certificate.subtitle' | translate}}
                    <a [routerLink]="['/aide/guides/certificateurs/parchemin']">(En savoir plus)</a>
                </div>
            </div>
        </div>
    </div>
    <treo-message type="info" *ngIf="certification.hasMultipleCertifiers"
                  [showIcon]="false" class="mb-4 mt-2 text-center" appearance="outline">
        {{ 'private.certification.common.coCertifiers' | translate}}
    </treo-message>
    <treo-message type="info" [showIcon]="false" *ngIf="certificateFileType?.templateFile"
                  class="mb-4 mt-2" appearance="outline">
        {{ 'private.certification.common.certificate.fileType' | translate}}
    </treo-message>
    <p class="my-2"
       [innerHTML]="(certificateFileType?.templateFile ? 'private.certification.common.certificate.certificateTemplateActive' : 'private.certification.common.certificate.certificateTemplateNotActive') | translate "></p>
    <form class="flex flex-col mt-2" [formGroup]="formGroupCustomize"
          (submit)="formGroupCustomizeSubmit()">
        <app-form-fields formGroupName="certification"
                         [entity]="certification"
                         [appFormFieldsData]="appFormFieldsData"
                         [formGroup]="formGroupCustomize">
        </app-form-fields>
        <div
            class="flex items-center mt-2 justify-end border-t pl-5 pr-10 -mx-5 py-3 light:bg-cool-gray-50 dark:bg-cool-gray-700 card-loading-hidden">
            <div class="flex">
                <mat-error *ngIf="errorMessages.length">
                    <ul>
                        <li *ngFor="let errorMessage of errorMessages">{{ errorMessage }}</li>
                    </ul>
                </mat-error>
                <button type="submit" class="flex align-center" mat-flat-button color="primary"
                        [disabled]="loading || disabledButton || formGroupCustomize.invalid || !formGroupCustomize.dirty">
                    <mat-progress-spinner class="mr-4" *ngIf="loading" [diameter]="24"
                                          mode="indeterminate"></mat-progress-spinner>
                    <ng-container
                        *ngIf="!loading">{{ 'common.actions.update' | translate}}</ng-container>
                </button>
            </div>
        </div>
    </form>
</mat-card>
