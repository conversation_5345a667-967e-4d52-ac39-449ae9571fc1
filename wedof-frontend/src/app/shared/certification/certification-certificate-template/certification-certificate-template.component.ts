import {Component, ElementRef, EventEmitter, Input, OnChang<PERSON>, On<PERSON><PERSON>roy, Output, SimpleChanges} from '@angular/core';
import {Certification, CertificationFolderFileType} from '../../api/models/certification';
import {FormBuilder, FormGroup} from '@angular/forms';
import {AppFormFieldData} from '../../material/app-form-field/app-form-field.component';
import {finalize} from 'rxjs/operators';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../errors/errors.types';
import {CertificationService} from '../../api/services/certification.service';
import {TranslateService} from '@ngx-translate/core';
import {BaseCardComponentDirective, RequiredCallSuper} from '../../utils/base-card/base-card.directive';
import {CERTIFICATE_FILE_TYPE_ID} from '../../api/models/file';
import {FileIframeComponent} from '../../file/dialogs/file-iframe/file-iframe.component';
import {MatDialog} from '@angular/material/dialog';

@Component({
    selector: 'app-certification-certificate-template',
    templateUrl: './certification-certificate-template.component.html',
    styleUrls: ['./certification-certificate-template.component.scss']
})
export class CertificationCertificateTemplateComponent extends BaseCardComponentDirective implements OnChanges, OnDestroy {
    static COMPONENT_ID = 'generationParchemin';
    formGroupCustomize: FormGroup;
    loading = false;
    disabledButton = false;
    errorMessages: string[] = [];
    appFormFieldsData: AppFormFieldData[];
    certificateFileType: CertificationFolderFileType;

    @Input() certification: Certification;
    @Output() certificationUpdated: EventEmitter<Certification> = new EventEmitter<Certification>();

    constructor(private _fb: FormBuilder,
                private _dialog: MatDialog,
                private _translateService: TranslateService,
                private _certificationService: CertificationService,
                private _el: ElementRef) {
        super(CertificationCertificateTemplateComponent.COMPONENT_ID, _el);
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes.certification) {
            this.panelLoading();
            this.initForm();
        }
    }

    ngOnDestroy(): RequiredCallSuper {
        return super.ngOnDestroy();
    }

    initForm(): void {
        this.errorMessages = [];
        if (!this.certification) {
            return;
        }
        this.formGroupCustomize = this._fb.group({
            certification: this._fb.group({})
        });
        this.certificateFileType = this.certification.certificationFolderFileTypes.find(fileType => fileType.id === CERTIFICATE_FILE_TYPE_ID);
        const appFormFieldsData: AppFormFieldData[] = [
            {
                controlName: 'certificateTemplate',
                value: this.certificateFileType?.templateFile,
                label: this._translateService.instant('common.actions.file.label.certificateTemplate', {format: '.pptx'}),
                chooseLabel: this._translateService.instant('common.actions.file.noformat.' +
                    (this.certificateFileType?.templateFile ? 'replace' : 'choose'), {file: 'modèle'}),
                removeLabel: this._translateService.instant('common.actions.file.remove', {file: 'modèle'}),
                type: 'file',
                icon: 'image',
                removable: true,
                showFilePreview: true,
                fileTypesAccept: ['.pptx'],
                thumbnailFile: this.certification.certificateTemplateThumbnail ?? false, // false => no preview on select file
                actionMethod: () => this.openDocumentIframe(this.certificateFileType.googleId),
                change: (controlName, newValue, formData) => {
                    this.errorMessages = [];
                    this.disabledButton = true;
                    if (newValue) {
                        const isTypeRespected = newValue['type'] === 'application/vnd.openxmlformats-officedocument.presentationml.presentation';
                        this.errorMessages = isTypeRespected ? [] : [this._translateService.instant('common.errors.wrongFormat')];
                        this.disabledButton = !isTypeRespected;
                    }
                    return [];
                },
            },
            {
                controlName: 'googleId',
                colSpan: 3,
                type: 'action',
                hideLabel: true,
                actionText: 'common.actions.updateDocument',
                removed: !this.certificateFileType || !this.certificateFileType.generated || !this.certificateFileType.templateFile,
                actionMethod: () => this.openDocumentIframe(this.certificateFileType.googleId)
            }];
        this.appFormFieldsData = appFormFieldsData.filter(value => value != null);
        this.panelLoaded();
    }

    formGroupCustomizeSubmit(): void {
        this.errorMessages = [];
        this.loading = true;
        const formValue = this.formGroupCustomize.getRawValue();
        const certificationUpdated: Certification = {
            ...this.certification,
            certificateTemplate: formValue.certification.certificateTemplate,
        };
        this._certificationService.update(certificationUpdated).pipe(
            finalize(() => this.loading = false)
        ).subscribe(
            (updatedCertification) => {
                this.certificationUpdated.emit(updatedCertification);
            },
            (httpErrorResponse: HttpErrorResponse) => {
                this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
            }
        );
    }

    openDocumentIframe(googleId: string): void {
        this._dialog.open(FileIframeComponent, {
            disableClose: true,
            width: '80%',
            data: {
                entityId: this.certification.id,
                templateEditor: {
                    layout: 'presentation',
                    templateGid: googleId,
                    type: 'document',
                    scope: 'certificate',
                    additionalInformation: 'private.certification.common.certificate.mandatory'
                }
            }
        });
    }

}
