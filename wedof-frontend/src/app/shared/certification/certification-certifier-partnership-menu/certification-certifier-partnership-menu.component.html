<ng-container *ngIf="!formGroup?.dirty">
    <ng-container
        *ngIf="certificationPartner.state == states.PROCESSING || certificationPartner.state == states.REVOKED || certificationPartner.state == states.SUSPENDED">
        <button *ngIf="certification.type !== certificationTypes.INTERNAL"
                [disabled]="loadingAction || isExpiredCertification"
                type="button"
                mat-flat-button
                [matTooltip]="(isExpiredCertification ? 'private.common.certification.managePartnership.tooltip.disabled' : '')| translate"
                [color]="certificationPartner.pendingActivation ? 'warn' : 'primary'"
                (click)="updatePending(certificationPartner, 'pendingActivation', !certificationPartner.pendingActivation)">
            {{ 'private.common.certification.managePartnership.menu.' + (certificationPartner.pendingActivation ? 'revertPendingAcceptation' : 'pendingActivation') | translate }}
        </button>
        <button *ngIf="certification.type === certificationTypes.INTERNAL"
                [disabled]="loadingAction"
                type="button"
                mat-flat-button
                color="primary"
                [color]="'primary'"
                (click)="updateState(certificationPartner, states.ACTIVE, 'activationInternal')">
            {{ 'private.common.certification.managePartnership.menu.pendingActivation' | translate }}
        </button>
    </ng-container>

    <ng-container *ngIf="certificationPartner.state == states.PROCESSING">
        <button mat-icon-button
                class="ml-2"
                (click)="$event.stopPropagation()"
                [matMenuTriggerFor]="actionsMenu"
                title="Actions">
            <mat-icon svgIcon="more_vert"></mat-icon>
        </button>
        <mat-menu #actionsMenu="matMenu" class="large-menu">
            <button [disabled]="loadingAction"
                    type="button"
                    mat-menu-item
                    (click)="updateState(certificationPartner, states.ABORTED)">
                <mat-icon color="warn" svgIcon="block"></mat-icon>
                {{ 'private.common.certification.managePartnership.menu.abort' | translate }}
            </button>
            <button [disabled]="loadingAction"
                    type="button"
                    mat-menu-item
                    (click)="updateState(certificationPartner, states.REFUSED)">
                <mat-icon color="warn" svgIcon="close"></mat-icon>
                {{ 'private.common.certification.managePartnership.menu.refuse' | translate }}
            </button>
            <button [disabled]="loadingAction"
                    type="button"
                    mat-menu-item
                    (click)="reinitialize(certificationPartner)">
                <mat-icon color="warn" svgIcon="restart_alt"></mat-icon>
                {{ 'private.common.certification.managePartnership.menu.reinitialize' | translate }}
            </button>
        </mat-menu>
    </ng-container>
    <button *ngIf="certificationPartner.state == states.ABORTED || certificationPartner.state == states.REFUSED"
            [disabled]="loadingAction"
            type="button"
            mat-flat-button
            color="primary"
            (click)="updateState(certificationPartner, states.PROCESSING)">
        {{ 'private.common.certification.managePartnership.menu.reopen' | translate }}
    </button>
    <ng-container *ngIf="certificationPartner.state == states.ACTIVE">
        <button *ngIf="certification.type !== certificationTypes.INTERNAL"
                [disabled]="loadingAction || isExpiredCertification"
                type="button"
                mat-flat-button
                [matTooltip]="(isExpiredCertification ? 'private.common.certification.managePartnership.tooltip.disabled' : '')| translate"
                color="warn"
                (click)="updatePending(certificationPartner, 'pendingSuspension', !certificationPartner.pendingSuspension)">
            {{ 'private.common.certification.managePartnership.menu.' + (certificationPartner.pendingSuspension ? 'revertPendingSuspension' : 'pendingSuspension') | translate }}
        </button>
        <button *ngIf="certification.type === certificationTypes.INTERNAL"
                [disabled]="loadingAction"
                type="button"
                mat-flat-button
                color="warn"
                (click)="updateState(certificationPartner, states.SUSPENDED, 'suspensionInternal')">
            {{ 'private.common.certification.managePartnership.menu.pendingSuspension' | translate }}
        </button>
        <button mat-icon-button
                class="ml-2"
                (click)="$event.stopPropagation()"
                [matMenuTriggerFor]="actionsMenu"
                title="Actions">
            <mat-icon svgIcon="more_vert"></mat-icon>
        </button>
        <mat-menu #actionsMenu="matMenu" class="large-menu">
            <button *ngIf="certification.type !== certificationTypes.INTERNAL"
                    [disabled]="loadingAction || isExpiredCertification"
                    type="button"
                    mat-menu-item
                    [matTooltip]="(isExpiredCertification ? 'private.common.certification.managePartnership.tooltip.disabled' : '')| translate"
                    (click)="updatePending(certificationPartner, 'pendingRevocation', !certificationPartner.pendingRevocation)">
                <mat-icon color="warn" svgIcon="block"></mat-icon>
                {{ 'private.common.certification.managePartnership.menu.' + (certificationPartner.pendingRevocation ? 'revertPendingRevocation' : 'pendingRevocation') | translate }}
            </button>
            <button *ngIf="certification.type === certificationTypes.INTERNAL"
                    [disabled]="loadingAction"
                    type="button"
                    mat-menu-item
                    (click)="updateState(certificationPartner, states.REVOKED, 'revocationInternal')">
                {{ 'private.common.certification.managePartnership.menu.pendingRevocation' | translate }}
            </button>
        </mat-menu>
    </ng-container>
</ng-container>
<button *ngIf="formGroup?.dirty"
        type="button"
        [disabled]="formGroup?.invalid || loadingAction"
        mat-flat-button
        color="primary"
        (click)="update(certificationPartner)">
    <mat-progress-spinner *ngIf="loadingAction"
                          [diameter]="24"
                          [mode]="'indeterminate'"></mat-progress-spinner>
    <ng-container *ngIf="!loadingAction"> {{ 'common.actions.update' | translate }}
    </ng-container>
</button>
