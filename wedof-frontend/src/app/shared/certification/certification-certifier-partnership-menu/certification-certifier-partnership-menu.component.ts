import {Component, EventEmitter, Input, On<PERSON><PERSON>roy, OnInit, Output} from '@angular/core';
import {CertificationPartner, CertificationPartnerStates} from '../../api/models/certification-partner';
import {CertificationPartnerService} from '../../api/services/certification-partner.service';
import {filter, finalize, switchMap, takeUntil, tap} from 'rxjs/operators';
import {HttpErrorResponse} from '@angular/common/http';
import {FormGroup} from '@angular/forms';
import {SnackBarComponent} from '../../material/snack-bar/snack-bar.component';
import {displayTextSnackBar} from '../../utils/displayTextSnackBar';
import {TranslateService} from '@ngx-translate/core';
import {MatSnackBar} from '@angular/material/snack-bar';
import {ApiError} from '../../errors/errors.types';
import {MatDialog} from '@angular/material/dialog';
import {ActionConfirmationComponent} from '../../material/action-confirmation/action-confirmation.component';
import {combineLatest, Observable, Subject} from 'rxjs';
import {Connection, ConnectionState, DataProviders} from '../../api/models/connection';
import {DialogConnectionAuthComponent} from '../../connection/dialog-connection-auth/dialog-connection-auth.component';
import {Organism} from '../../api/models/organism';
import {Select} from '@ngxs/store';
import {OrganismState} from '../../api/state/organism.state';
import {ConnectionsState} from '../../api/state/connections.state';
import {CertificationPartnerAuditResults} from '../../api/models/certification-partner-audit';
import {Certification, CertificationTypes} from '../../api/models/certification';

@Component({
    selector: 'app-certification-certifier-partnership-menu',
    templateUrl: './certification-certifier-partnership-menu.component.html',
    styleUrls: ['./certification-certifier-partnership-menu.component.scss']
})
export class CertificationCertifierPartnershipMenuComponent implements OnInit, OnDestroy {

    franceCompetencesConnection: Connection;
    currentOrganism: Organism;
    states = CertificationPartnerStates;
    loadingAction = false;
    certificationTypes = CertificationTypes;

    @Output() errorMessages?: EventEmitter<string[]> = new EventEmitter<[]>();
    @Output() loading?: EventEmitter<boolean> = new EventEmitter<boolean>();
    @Output() stateChanged?: EventEmitter<CertificationPartnerStates> = new EventEmitter<CertificationPartnerStates>();
    @Input() certificationPartner: CertificationPartner;
    @Input() formGroup: FormGroup;
    @Input() isExpiredCertification: boolean;
    @Input() certification: Certification;
    @Output() initCertificationPartner: EventEmitter<any> = new EventEmitter<any>();
    @Output() processedCertificationPartner: EventEmitter<CertificationPartner> = new EventEmitter<CertificationPartner>();
    @Input() allSkillSetIds?: number[];

    @Select(OrganismState.organism) organism$: Observable<Organism>;
    @Select(ConnectionsState.connections) connections$: Observable<Connection[]>;

    private _unsubscribeAll = new Subject<void>();

    constructor(private _certificationPartnerService: CertificationPartnerService,
                private _snackBar: MatSnackBar,
                private _translateService: TranslateService,
                private _dialog: MatDialog) {
    }

    ngOnInit(): void {
        combineLatest([
            this.connections$,
            this.organism$
        ]).pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe(([connections, currentOrganism]) => {
            this.franceCompetencesConnection = connections.find(connection => connection.dataProvider === DataProviders.FRANCE_COMPETENCES);
            this.currentOrganism = currentOrganism;
        });
    }

    updateState(certificationPartner: CertificationPartner, state: CertificationPartnerStates, suffix?: string): void {
        const certifInfo = certificationPartner._links.certification.certifInfo;
        const siret = certificationPartner._links.partner.siret;
        const suffixLabel = suffix ?? state;
        const dialogRef = this._dialog.open(ActionConfirmationComponent, {
            panelClass: 'full-page-scroll-30',
            height: 'auto',
            data: {
                messageKey: 'private.common.certification.managePartnership.confirm.' + suffixLabel
            }
        });
        dialogRef.componentInstance.actionValue$.pipe(
            filter(confirmation => confirmation),
            tap(() => {
                this.loading.emit(true);
                this.loadingAction = true;
            }),
            switchMap(() =>
                this._certificationPartnerService.update(certifInfo, siret, {state})
            ),
            finalize(() => {
                dialogRef.componentInstance.close();
                this.loading.emit(false);
                this.loadingAction = false;
            }),
        ).subscribe((certificationPartnerUpdated) => {
            this.processedCertificationPartner.emit(certificationPartnerUpdated);
        });
    }

    updatePending(certificationPartner: CertificationPartner, field: 'pendingActivation' | 'pendingRevocation' | 'pendingSuspension', value: boolean): void {
        // If pending switched to false : update, if switched to true then check first is FC connexion and ask credentials if needed
        if (value === false || (this.franceCompetencesConnection && this.franceCompetencesConnection.state === ConnectionState.ACTIVE)) {
            this.internalUpdatePending(certificationPartner, field, value);
        } else {
            this.showFranceCompetencesConnectionDialog().subscribe(connectionOk => {
                if (connectionOk) {
                    this.internalUpdatePending(certificationPartner, field, value);
                }
            });
        }
    }

    private showFranceCompetencesConnectionDialog(): Observable<boolean> {
        const dialogRef = this._dialog.open(DialogConnectionAuthComponent, {
            disableClose: true,
            panelClass: ['no-padding-panel', 'full-page-scroll-40'],
            height: 'auto',
            data: {
                dataProvider: DataProviders.FRANCE_COMPETENCES,
                organism: this.currentOrganism
            }
        });
        return dialogRef.afterClosed();
    }

    private internalUpdatePending(certificationPartner: CertificationPartner, field: 'pendingActivation' | 'pendingRevocation' | 'pendingSuspension', value: boolean): void {
        const certifInfo = certificationPartner._links.certification.certifInfo;
        const siret = certificationPartner._links.partner.siret;
        let confirmSuffix = '';
        if (field === 'pendingActivation') {
            confirmSuffix = value ? 'pendingActivation' : 'revertPendingAcceptation';
        } else if (field === 'pendingRevocation') {
            confirmSuffix = value ? 'pendingRevocation' : 'revertPendingRevocation';
        } else if (field === 'pendingSuspension') {
            confirmSuffix = value ? 'pendingSuspension' : 'revertPendingSuspension';
        }
        const habilitation = this._translateService.instant('private.certification.partners.table.habilitation.' + certificationPartner.habilitation);
        const dialogRef = this._dialog.open(ActionConfirmationComponent, {
            panelClass: 'full-page-scroll-30',
            height: 'auto',
            data: {
                messageKey: 'private.common.certification.managePartnership.confirm.' + confirmSuffix,
                treoMessage: field === 'pendingActivation' ? this._translateService.instant('private.common.certification.managePartnership.confirmHabilitation',
                    {habilitation: habilitation}) : null
            }
        });
        dialogRef.componentInstance.actionValue$.pipe(
            filter(confirmation => confirmation),
            tap(() => {
                this.loading.emit(true);
                this.loadingAction = true;
            }),
            switchMap(() =>
                this._certificationPartnerService.update(certifInfo, siret, {[field]: value})
            ),
            finalize(() => {
                dialogRef.componentInstance.close();
                this.loading.emit(false);
                this.loadingAction = false;
            })
        ).subscribe((certificationPartnerUpdated) => {
            this.processedCertificationPartner.emit(certificationPartnerUpdated);
        });
    }

    update(certificationPartner: CertificationPartner): void {
        this.errorMessages.emit([]);
        this.loadingAction = true;
        this.loading.emit(true);
        const certifInfo = certificationPartner._links.certification.certifInfo;
        const siret = certificationPartner._links.partner.siret;
        const certificationPartnerValue = this.formGroup.getRawValue().certificationPartner;
        const trainingsZone = certificationPartnerValue.trainingsZone?.length ?
            certificationPartnerValue.trainingsZone.map((trainingZone) => {
                return trainingZone.postalCode;
            }) : null;
        const body = {
            ...certificationPartnerValue,
            trainingsZone : trainingsZone
        };
        if (certificationPartner.compliance === CertificationPartnerAuditResults.NONE) {
            delete body.compliance;
        }
        if (this.certification.type === CertificationTypes.RNCP && this.certification.allowPartialSkillSets) {
            let skillSets = null;
            if (certificationPartnerValue.skillSets) {
                skillSets = certificationPartnerValue.skillSets.includes('*') ? this.allSkillSetIds : certificationPartnerValue.skillSets;
            }
            body.skillSets = skillSets;
        }
        this._certificationPartnerService.update(certifInfo, siret, body).pipe(
            finalize(() => {
                this.loadingAction = false;
                this.loading.emit(false);
            })
        ).subscribe(
            (certificationPartnerUpdated) => {
                this.processedCertificationPartner.emit(certificationPartnerUpdated);
                this.initCertificationPartner.emit(certificationPartnerUpdated);
                this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(this._translateService.instant('common.actions.certificationPartnerUpdatedSuccessfully')));
            },
            (httpErrorResponse: HttpErrorResponse) => {
                this.errorMessages.emit((httpErrorResponse.error as ApiError).errorMessages);
            }
        );
    }

    reinitialize(certificationPartner: CertificationPartner): void {
        this.errorMessages.emit([]);
        const certifInfo = certificationPartner._links.certification.certifInfo;
        const siret = certificationPartner._links.partner.siret;
        const dialogRef = this._dialog.open(ActionConfirmationComponent, {
            panelClass: 'full-page-scroll-30',
            height: 'auto',
            data: {
                title: 'common.confirmation.reinitialization.title',
                color: 'warn',
                messageKey: 'private.common.certification.managePartnership.confirm.reinitialize'
            }
        });
        dialogRef.componentInstance.actionValue$.pipe(
            filter(confirmation => confirmation),
            tap(() => {
                this.loading.emit(true);
                this.loadingAction = true;
            }),
            switchMap(() => this._certificationPartnerService.reinitialize(certifInfo, siret)
            ),
            finalize(() => {
                dialogRef.componentInstance.close();
                this.loading.emit(false);
                this.loadingAction = false;
            }),
        ).subscribe((certificationPartnerUpdated) => {
            this.processedCertificationPartner.emit(certificationPartnerUpdated);
            this.initCertificationPartner.emit(certificationPartnerUpdated);
            this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(
                this._translateService.instant('common.actions.certificationPartnerUpdatedSuccessfully')));
        });
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }
}
