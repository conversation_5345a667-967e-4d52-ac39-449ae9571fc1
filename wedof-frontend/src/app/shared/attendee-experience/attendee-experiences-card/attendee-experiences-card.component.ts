import {Component, Input, OnDestroy, OnInit} from '@angular/core';
import {AttendeeExperience, SituationCertification} from '../../api/models/attendee-experience';
import {takeUntil} from 'rxjs/operators';
import {Subject} from 'rxjs';
import {AttendeeExperienceService} from '../../api/services/attendee-experience.service';

@Component({
    selector: 'app-attendee-experiences-card',
    templateUrl: './attendee-experiences-card.component.html',
    styleUrls: ['./attendee-experiences-card.component.scss']
})
export class AttendeeExperiencesCardComponent implements OnInit, OnDestroy {

    cardLoading = true;
    attendeeExperiences: AttendeeExperience[];
    limit = 6;
    total = 0;
    filters: any;
    situation = SituationCertification;

    @Input() isCandidate: boolean;
    @Input() candidateId: number;
    @Input() certificationFolderExternalId: string;

    private _unsubscribeAll: Subject<void> = new Subject();

    constructor(private _attendeeExperienceService: AttendeeExperienceService) {
    }

    ngOnInit(): void {
        this.filters = {limit: this.limit};
        this._attendeeExperienceService.list(this.candidateId, this.filters)
            .subscribe((attendeeExperiences) => {
                this.attendeeExperiences = attendeeExperiences.payload;
                this.total = attendeeExperiences?.total;
                this.cardLoading = false;
            });
    }

    onScroll(): void {
        const displayedPages = this.attendeeExperiences.length / this.limit;
        if (Number.isInteger(displayedPages)) {
            this._attendeeExperienceService.list(this.candidateId, {
                ...this.filters,
                page: displayedPages + 1
            })
                .pipe(takeUntil(this._unsubscribeAll))
                .subscribe((response) => {
                    this.attendeeExperiences.push(...response.payload);
                });
        }
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }
}
