<mat-card class="flex h-full flex-auto flex-col mt-3 p-5 pb-0 pt-3 border shadow-none sm"
          [ngClass]="{'card-loading':cardLoading}">
    <div class="flex items-center mb-2">
        <mat-icon
            [matBadge]="total > 0 ? total.toString() : null"
            matBadgePosition="below after"
            matBadgeSize="small"
            class="mr-3 card-loading-show text-4xl"
            color="primary">work
        </mat-icon>
        <div class="flex items-center">
            <span
                class="text-xl font-semibold card-loading-show">{{ 'private.attendee.experiences.title' | translate }}</span>
        </div>
    </div>

    <div *ngIf="!cardLoading" class="flex flex-col mt-3 mb-2 overflow-y-auto max-h-80"
         infiniteScroll
         [infiniteScrollDistance]="2"
         [infiniteScrollThrottle]="50"
         (scrolled)="onScroll()"
         [scrollWindow]="false">
        <div *ngIf="attendeeExperiences?.length; else noData">
            <div *ngFor="let attendeeExperience of attendeeExperiences" class="mb-3">
                <p class="pb-1">{{'private.attendee.experiences.created' | translate : {createdOn: attendeeExperience.createdOn | date:'mediumDate'} }} </p>
                <app-form-field-static *ngIf="attendeeExperience.certificationName"
                                       type="text"
                                       [value]="attendeeExperience.certificationName">
                </app-form-field-static>
                <ng-container *ngIf="attendeeExperience.situation === situation.ACTIVE ; else notActive">
                    <app-form-field-static icon="work"
                                           type="text"
                                           [value]="attendeeExperience.job + ' - ' + ('private.attendee.experiences.form.form.contractType.' + attendeeExperience.contractType | translate) ">
                    </app-form-field-static>
                    <app-form-field-static icon="domain"
                                           type="text"
                                           [value]="attendeeExperience.companyName ">
                    </app-form-field-static>
                    <app-form-field-static *ngIf="attendeeExperience.executiveStatus === true"
                                           type="text"
                                           [value]="'private.attendee.experiences.form.form.executiveStatus' | translate">
                    </app-form-field-static>
                </ng-container>
                <ng-template #notActive>
                    <app-form-field-static icon="work"
                                           type="text"
                                           [value]="'private.attendee.experiences.form.form.situation.' + attendeeExperience.situation | translate">
                    </app-form-field-static>
                </ng-template>
                <app-form-field-static *ngIf="attendeeExperience.salaryYearly"
                                       icon="euro"
                                       type="text"
                                       suffix="brut annuel"
                                       [value]="attendeeExperience.salaryYearly">
                </app-form-field-static>
            </div>
        </div>

        <ng-template #noData>
            <p class="mb-2 text-center m-auto">
                {{ 'private.attendee.experiences.noData' | translate }}
            </p>
        </ng-template>
    </div>

</mat-card>
