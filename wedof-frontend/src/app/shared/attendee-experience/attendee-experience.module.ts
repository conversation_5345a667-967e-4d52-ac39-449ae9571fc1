import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {AttendeeExperienceFormComponent} from './attendee-experience-form/attendee-experience-form.component';
import {MaterialModule} from '../material/material.module';
import {TranslateModule} from '@ngx-translate/core';
import {ReactiveFormsModule} from '@angular/forms';
import {AttendeeExperiencesCardComponent} from './attendee-experiences-card/attendee-experiences-card.component';
import {MatCardModule} from '@angular/material/card';
import {InfiniteScrollModule} from 'ngx-infinite-scroll';
import {AttendeeExperienceComponent} from './attendee-experience/attendee-experience.component';


@NgModule({
    declarations: [AttendeeExperienceFormComponent, AttendeeExperiencesCardComponent, AttendeeExperienceComponent],
    exports: [AttendeeExperiencesCardComponent, AttendeeExperienceFormComponent, AttendeeExperienceComponent],
    imports: [
        CommonModule,
        MaterialModule,
        TranslateModule,
        MatCardModule,
        ReactiveFormsModule,
        InfiniteScrollModule,
    ]
})
export class AttendeeExperienceModule {
}
