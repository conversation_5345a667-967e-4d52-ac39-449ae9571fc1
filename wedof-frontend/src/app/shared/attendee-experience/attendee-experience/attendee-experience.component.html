<p>{{'private.attendee.experiences.form.form.situation.' + attendeeExperience.situation | translate }}</p>
<p *ngIf="attendeeExperience.job && attendeeExperience.companyName">{{attendeeExperience.job}}
    - {{attendeeExperience.companyName}} {{getMoreInformations(attendeeExperience)}}</p>
<p *ngIf="attendeeExperience.startDate && attendeeExperience.endDate">
    {{
    'private.common.dates.fromTo' | translate : {
        startDate: attendeeExperience.startDate | date : 'mediumDate',
        endDate: attendeeExperience.endDate| date : 'mediumDate'
    }
    }}
</p>
<p *ngIf="attendeeExperience.startDate && !attendeeExperience.endDate"> {{'common.actions.search.since' | translate}} {{attendeeExperience.startDate | date : 'mediumDate'}} </p>
