import {Component, Input} from '@angular/core';
import {AttendeeExperience} from '../../api/models/attendee-experience';
import {TranslateService} from '@ngx-translate/core';

@Component({
    selector: 'app-attendee-experience',
    templateUrl: './attendee-experience.component.html',
    styleUrls: ['./attendee-experience.component.scss']
})
export class AttendeeExperienceComponent {

    @Input() attendeeExperience: AttendeeExperience;

    constructor(private _translateService: TranslateService) {
    }

    getMoreInformations(attendeeExperience: AttendeeExperience): string {
        let moreInformations = '';
        if (attendeeExperience.contractType) {
            moreInformations += this._translateService.instant('private.attendee.experiences.form.form.contractType.' + attendeeExperience.contractType);
        }
        if (attendeeExperience.executiveStatus) {
            moreInformations += (moreInformations?.length ? ' - ' : '') + this._translateService.instant('private.attendee.experiences.form.form.executiveStatus');
        }
        if (attendeeExperience.salaryYearly) {
            moreInformations += (moreInformations?.length ? ' - ' : '') + attendeeExperience.salaryYearly + ' € / brut / an';
        }
        return moreInformations?.length ? ' (' + moreInformations + ')' : moreInformations;
    }

}
