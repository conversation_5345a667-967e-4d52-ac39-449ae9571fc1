<form [formGroup]="formGroup" class="flex flex-col">

    <treo-message class="text-center mb-4"
                  appearance="outline" [showIcon]="false" type="info">
        {{ 'private.attendee.survey.subtitleAttendee' | translate }}
    </treo-message>

    <app-form-fields class="xs:block grid grid-cols-1 sm:grid-cols-6 md:grid-cols-6 lg:grid-cols-6 xl:grid-cols-6 gap-2"
                     formGroupName="attendeeExperienceForm"
                     [entity]="newAttendeeExperience"
                     [appFormFieldsData]="appFormFieldsData"
                     [formGroup]="formGroup"></app-form-fields>

    <div *ngIf="errorMessages?.length" class="flex items-center">
        <treo-message class="flex-auto" appearance="outline" [showIcon]="false" type="error">
            <ul>
                <li *ngFor="let errorMessage of errorMessages">
                    {{ errorMessage }}
                </li>
            </ul>
        </treo-message>
    </div>

    <div class="flex items-center justify-end">
        <button type="button" class="mr-2"
                (click)="goBack()"
                [disabled]="loading">
            {{ 'common.actions.cancel' | translate}}
        </button>
        <button type="submit" class="flex align-center" mat-flat-button color="primary"
                (click)="create()"
                [disabled]="loading || formGroup.invalid || !formGroup.dirty">
            <mat-progress-spinner class="mr-4" *ngIf="loading" [diameter]="24"
                                  mode="indeterminate"></mat-progress-spinner>
            <ng-container
                *ngIf="!loading">{{ 'common.actions.createAndAnswer' | translate}}</ng-container>
        </button>
    </div>
</form>
