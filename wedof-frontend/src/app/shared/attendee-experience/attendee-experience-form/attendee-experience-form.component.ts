import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {AttendeeExperience, ContractType, SituationCertification} from '../../api/models/attendee-experience';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {AppFormFieldData} from '../../material/app-form-field/app-form-field.component';
import {TranslateService} from '@ngx-translate/core';
import Diploma from '../../../../assets/diploma/diplomaLevel.json';
import {AttendeeExperienceService} from '../../api/services/attendee-experience.service';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../errors/errors.types';
import moment from 'moment/moment';
import {CertificationFolderSurveyExperience} from '../../api/models/certification-folder-survey';

@Component({
    selector: 'app-attendee-experience-form',
    templateUrl: './attendee-experience-form.component.html',
    styleUrls: ['./attendee-experience-form.component.scss']
})
export class AttendeeExperienceFormComponent implements OnInit {

    newAttendeeExperience: AttendeeExperience;
    loading = false;
    formGroup: FormGroup;
    errorMessages: string[] = [];
    appFormFieldsData: AppFormFieldData[];
    diplomas: typeof Diploma;

    @Input() candidateId: number;
    @Input() certificationFolderExternalId: string;
    @Input() surveyExperienceName: CertificationFolderSurveyExperience;
    @Output() chooseAttendeeExperience: EventEmitter<AttendeeExperience> = new EventEmitter<AttendeeExperience>();
    @Output() hideAttendeeExperienceForm: EventEmitter<void> = new EventEmitter<void>();

    constructor(private _formBuilder: FormBuilder,
                private _attendeeExperienceService: AttendeeExperienceService,
                private _translateService: TranslateService,
    ) {
        this.diplomas = Diploma;
    }

    ngOnInit(): void {
        this.formGroup = this._formBuilder.group({
            attendeeExperienceForm: this._formBuilder.group({})
        });
        const appFormFieldsData: AppFormFieldData[] = [
            {
                controlName: 'certificationName',
                label: 'private.attendee.experiences.form.form.certificationName',
                value: null,
                type: 'text',
                removed: this.surveyExperienceName !== CertificationFolderSurveyExperience.INITIAL_EXPERIENCE
            },
            {
                controlName: 'qualification',
                label: 'private.attendee.experiences.form.form.qualification',
                value: null,
                type: 'select',
                required: true,
                choices: this.diplomas.map((diploma) => ({key: diploma.libelle, value: diploma.code})),
                colSpan: 3,
            },
            {
                controlName: 'situation',
                label: 'private.attendee.experiences.form.form.situation.title',
                value: null,
                required: true,
                type: 'select',
                colSpan: 3,
                choices: [
                    {
                        key: this._translateService.instant('private.attendee.experiences.form.form.situation.active'),
                        value: SituationCertification.ACTIVE
                    },
                    {
                        key: this._translateService.instant('private.attendee.experiences.form.form.situation.inactive'),
                        value: SituationCertification.INACTIVE
                    },
                    {
                        key: this._translateService.instant('private.attendee.experiences.form.form.situation.searching'),
                        value: SituationCertification.SEARCHING
                    },
                    {
                        key: this._translateService.instant('private.attendee.experiences.form.form.situation.training'),
                        value: SituationCertification.TRAINING
                    }
                ],
                change: (controlName, newValue, formData) => {
                    const appFormFieldJob = formData.find(field => field.controlName === 'job');
                    const appFormFieldStartDate = formData.find(field => field.controlName === 'startDate');
                    const appFormFieldCompanyName = formData.find(field => field.controlName === 'companyName');
                    const appFormFieldContractType = formData.find(field => field.controlName === 'contractType');
                    let isValueRequired = false;
                    if (newValue === SituationCertification.ACTIVE) {
                        isValueRequired = true;
                    }
                    appFormFieldJob.required = isValueRequired;
                    appFormFieldStartDate.required = isValueRequired;
                    appFormFieldCompanyName.required = isValueRequired;
                    appFormFieldContractType.required = isValueRequired;
                    return [];
                }
            },
            {
                controlName: 'job',
                label: 'private.attendee.experiences.form.form.job',
                value: null,
                type: 'text',
                colSpan: 3
            },
            {
                controlName: 'companyName',
                label: 'private.attendee.experiences.form.form.companyName',
                value: null,
                type: 'text',
                colSpan: 3
            },
            {
                controlName: 'contractType',
                label: 'private.attendee.experiences.form.form.contractType.title',
                value: null,
                type: 'select',
                colSpan: 2,
                choices: [
                    {
                        key: this._translateService.instant('private.attendee.experiences.form.form.contractType.cdd'),
                        value: ContractType.CDD
                    },
                    {
                        key: this._translateService.instant('private.attendee.experiences.form.form.contractType.cdi'),
                        value: ContractType.CDI
                    },
                    {
                        key: this._translateService.instant('private.attendee.experiences.form.form.contractType.interim'),
                        value: ContractType.INTERIM
                    },
                    {
                        key: this._translateService.instant('private.attendee.experiences.form.form.contractType.independant'),
                        value: ContractType.INDEPENDANT
                    },
                    {
                        key: this._translateService.instant('private.attendee.experiences.form.form.contractType.inactif'),
                        value: ContractType.INACTIF
                    }
                ]
            },
            {
                controlName: 'salaryYearly',
                label: 'private.attendee.experiences.form.form.salaryYearly.title',
                value: null,
                type: 'number',
                min: 10000,
                validators: [Validators.min(10000)],
                colSpan: 2,
                validatorsMessages: {
                    min:  'private.attendee.experiences.form.form.salaryYearly.error',
                },
            },
            {
                controlName: 'executiveStatus',
                label: 'private.attendee.experiences.form.form.executiveStatus',
                value: false,
                type: 'radio',
                inline: true,
                choices: [
                    {key: this._translateService.instant('common.actions.yes'), value: true},
                    {key: this._translateService.instant('common.actions.no'), value: false}
                ],
                colSpan: 2
            },
            {
                controlName: 'startDate',
                label: 'private.attendee.experiences.form.form.startDate',
                value: null,
                type: 'date',
                max: new Date(),
                colSpan: 3,
                validatorsMessages: {
                    max: 'common.errors.date',
                },
                change: (controlName, newValue, formData) => {
                    const appFormFieldEndDate = formData.find(field => field.controlName === 'endDate');
                    appFormFieldEndDate.removed = !newValue;
                    appFormFieldEndDate.min = moment(new Date(newValue)).toISOString();
                    return [appFormFieldEndDate];
                },
            },
            {
                controlName: 'endDate',
                label: 'private.attendee.experiences.form.form.endDate',
                value: null,
                type: 'date',
                colSpan: 3,
                removed: true,
                removable: true,
                max: new Date(),
                validatorsMessages: {
                    max: 'common.errors.date',
                    min: 'common.errors.date',
                },
            }
        ];
        this.appFormFieldsData = appFormFieldsData.filter(value => value != null);
    }

    create(): void {
        this.loading = true;
        this.errorMessages = [];
        const attendeeExperience = this.formGroup.getRawValue().attendeeExperienceForm;
        const body = {
            ...attendeeExperience,
            certificationFolderExternalId: this.certificationFolderExternalId
        };
        this._attendeeExperienceService.create(this.candidateId, body).subscribe({
            next: (attendeeExperienceCreated) => {
                this.chooseAttendeeExperience.emit(attendeeExperienceCreated);
            },
            error: (httpErrorResponse: HttpErrorResponse) => {
                this.loading = false;
                this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
            }
        });
    }

    goBack(): void {
        this.hideAttendeeExperienceForm.emit();
    }
}
