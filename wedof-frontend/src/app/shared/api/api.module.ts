import {CommonModule} from '@angular/common';
import {ModuleWithProviders, NgModule} from '@angular/core';

import {ApiTokenService} from './services/api-token.service';
import {CertificationService} from './services/certification.service';
import {CertifierAccessService} from './services/certifier-access.service';
import {OrganismApplicationService} from './services/organism-application.service';
import {OrganismService} from './services/organism.service';
import {RegistrationFolderReasonService} from './services/registration-folder-reason.service';
import {RegistrationFolderService} from './services/registration-folder.service';
import {SessionService} from './services/session.service';
import {StatisticService} from './services/statistic.service';
import {UserService} from './services/user.service';
import {FunnelService} from './services/funnel.service';
import {ProposalService} from './services/proposal.service';
import {TagService} from './services/tag.service';
import {MetadataService} from './services/metadata.service';
import {AttendeeService} from './services/attendee.service';
import {CertificationPartnerService} from './services/certification-partner.service';
import {ApplicationsService} from '../../applications/shared/applications.service';
import {CdcFileService} from './services/cdc-file.service';
import {ActivityService} from './services/activity.service';
import {CertificationFoldersCdcFilesService} from './services/certification-folders-cdc-files.service';
import {InvoiceService} from './services/invoice.service';
import {CertificationPartnerAuditService} from './services/certification-partner-audit.service';
import {CityService} from './services/city.service';

@NgModule({
    imports: [
        CommonModule
    ]
})
export class ApiModule {
    static forRoot(): ModuleWithProviders<ApiModule> {
        return {
            ngModule: ApiModule,
            providers: [
                TagService,
                MetadataService,
                UserService,
                CityService,
                FunnelService,
                InvoiceService,
                SessionService,
                CdcFileService,
                ActivityService,
                ProposalService,
                ApiTokenService,
                AttendeeService,
                OrganismService,
                StatisticService,
                ApplicationsService,
                CertificationService,
                CertifierAccessService,
                RegistrationFolderService,
                OrganismApplicationService,
                CertificationPartnerService,
                RegistrationFolderReasonService,
                RegistrationFolderReasonService,
                CertificationPartnerAuditService,
                CertificationFoldersCdcFilesService,
            ]
        };
    }
}
