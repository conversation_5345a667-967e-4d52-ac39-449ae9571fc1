import {Validators} from '@angular/forms';

export const FormValidators = {
    LINKEDIN_PAGE_PATTERN: /^http[s]?:\/\/(www\.){0,1}linkedin\.com\/company\/[a-zA-Z0-9\-]+[\/]?/,
    URL_PATTERN: /^http[s]?:\/\/(www\.){0,1}[a-zA-Z0-9\.\-]+\.[a-zA-Z]{2,5}/,
    URL_WEHBOOK_PATTERN: /^http[s]?:\/\/(?!.*\blocalhost\b|\bzapier\b|\b127\.0\.0\.1\b|(\b127\.\b)|(\b10\.\b)|(\b172\.1[6-9]\.\b)|(\b172\.2[0-9]\.\b)|(\b172\.3[0-1]\.\b)|(\b192\.168\.\b))[a-zA-Z0-9\.\-]+\.[a-zA-Z]{2,5}/,
    PHONE_PATTERN: /^((?:(?:\+|00)33|0)[1-9]\d{8}|00212\d{9}|0041\d{9})$/,
    // Angular email validator is more permissive than the back so we can get back errors (e.g. toto@tutu)
    // To work around that we mimick email validation from the back by using a custom regex pattern
    EMAIL_PATTERN: /^[a-zA-Z0-9.!#$%&\'*+\\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+$/,
    AGREEMENT_PATTERN: /^[0-9]{10,12}$/,
    PASSWORD: Validators.minLength(6),
    MOBILEPHONE_PATTERN: /^(0[6-7]\d{8}|002305\d{7}|00491\d{9,10}|00212\d{9}|0041\d{9}|0022901\d{8})$/,
    // 06 or 07 for metropolitan france, 00230 for Maurice, 0049 for Allemagne, 00212 for Maroc, 0041 for Suisse, 00229 for Benin (cf. https://fr.chahaoba.com/Maurice)
    HEX_COLOR: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/
};
