import {Link, OrganismLink} from './link';

export interface Links {
    self: Link;
    partner: OrganismLink;
    certifier: OrganismLink;
}

export interface CertifierAccess {
    id: number;
    activatedOn: string;
    terminatedOn: string;
    fullName: string;
    email: string;
    state: CertifierAccessState;
    _links: Links;
    createdOn: Date;
    inviteLink?: string;
}

export enum CertifierAccessState {
    ACCEPTED = 'accepted',
    REFUSED = 'refused',
    TERMINATED = 'terminated',
    WAITING = 'waiting',
    NONE = 'none'
}
