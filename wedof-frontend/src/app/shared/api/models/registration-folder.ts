import {CertificationFolderLink, CertificationLink, Link, OrganismLink, WorkingContractLink} from './link';
import {CertificationFolder} from './certification-folder';
import {Attendee} from './attendee';
import {File} from './file';
import {DataProviders} from './connection';
import {ThemePalette} from '@angular/material/core';

export interface Links {
    self: Link;
    validate: Link;
    inTraining: Link;
    terminate: Link;
    serviceDone: Link;
    refuse: Link;
    cancel: Link;
    sendBill: Link;
    session: Link;
    organism: OrganismLink;
    payments: Link;
    trainingAction: Link;
    certification: CertificationLink;
    certificationFolder?: CertificationFolderLink;
    inPartnershipWith?: OrganismLink;
    activities: Link;
    currentWorkingContract?: WorkingContractLink;
}

export interface BillingInfos {
    totalIncl: number;
}

export interface TrainingActionInfo {
    indicativeDuration: number;
    weeklyDuration: number;
    externalId: string;
    title: string;
    sessionEndDate: string;
    sessionStartDate: string;
    sessionId: string;
    totalIncl: number;
    totalExcl: number;
    trainingActionId: string;
    trainingId: string;
    teachingModalities: string;
    trainingCompletionRate?: number; // tmp deprecated
    hoursInCenter?: number;
    hoursInCompany?: number;
    externalLink?: string;
    solicitations?: Solicitation[];
}

export enum RegistrationFolderStates {
    NOT_PROCESSED = 'notProcessed',
    VALIDATED = 'validated',
    WAITING_ACCEPTATION = 'waitingAcceptation',
    ACCEPTED = 'accepted',
    IN_TRAINING = 'inTraining',
    TERMINATED = 'terminated',
    SERVICE_DONE_DECLARED = 'serviceDoneDeclared',
    SERVICE_DONE_VALIDATED = 'serviceDoneValidated',
    CANCELED_BY_ATTENDEE = 'canceledByAttendee',
    CANCELED_BY_ATTENDEE_NOT_REALIZED = 'canceledByAttendeeNotRealized',
    CANCELED_BY_ORGANISM = 'canceledByOrganism',
    CANCELED_BY_FINANCER = 'canceledByFinancer',
    REFUSED_BY_ATTENDEE = 'refusedByAttendee',
    REFUSED_BY_ORGANISM = 'refusedByOrganism',
    REFUSED_BY_FINANCER = 'refusedByFinancer',
    REJECTED_WITHOUT_TITULAIRE_SUITE = 'rejectedWithoutTitulaireSuite'
}

export enum RegistrationFolderActions {
    TO_BILL = 'allBilling',
    PAID = 'paid'
}

export type RegistrationFolderActionsAndStates = RegistrationFolderStates | RegistrationFolderActions;

export enum RegistrationFolderPrimaryStates {
    NOT_PROCESSED = 'notProcessed',
    VALIDATED = 'validated',
    WAITING_ACCEPTATION = 'waitingAcceptation',
    ACCEPTED = 'accepted',
    IN_TRAINING = 'inTraining',
    TERMINATED = 'terminated',
    SERVICE_DONE_DECLARED = 'serviceDoneDeclared',
    SERVICE_DONE_VALIDATED = 'serviceDoneValidated',
}

export enum RegistrationFolderErrorStates {
    CANCELED_BY_ATTENDEE = 'canceledByAttendee',
    CANCELED_BY_ATTENDEE_NOT_REALIZED = 'canceledByAttendeeNotRealized',
    CANCELED_BY_ORGANISM = 'canceledByOrganism',
    CANCELED_BY_FINANCER = 'canceledByFinancer',
    REFUSED_BY_ATTENDEE = 'refusedByAttendee',
    REFUSED_BY_ORGANISM = 'refusedByOrganism',
    REJECTED_WITHOUT_TITULAIRE_SUITE = 'rejectedWithoutTitulaireSuite'
}

export enum RegistrationFolderErrorAndTerminatedStates {
    SERVICE_DONE_DECLARED = 'serviceDoneDeclared',
    SERVICE_DONE_VALIDATED = 'serviceDoneValidated',
    CANCELED_BY_ATTENDEE = 'canceledByAttendee',
    CANCELED_BY_ATTENDEE_NOT_REALIZED = 'canceledByAttendeeNotRealized',
    CANCELED_BY_ORGANISM = 'canceledByOrganism',
    REFUSED_BY_ATTENDEE = 'refusedByAttendee',
    REFUSED_BY_ORGANISM = 'refusedByOrganism',
    REJECTED_WITHOUT_TITULAIRE_SUITE = 'rejectedWithoutTitulaireSuite'
}

export enum RegistrationFolderBillingStates {
    TO_BILL = 'toBill',
    BILLED = 'billed',
    DEPOSIT_PAID = 'depositPaid',
    PAID = 'paid',
    NOT_BILLABLE = 'notBillable',
    DEPOSIT_WAIT = 'depositWait'
}

export enum RegistrationFolderStatesForPartnershipView {
    CANCELED_BY_ATTENDEE_NOT_REALIZED = 'canceledByAttendeeNotRealized',
    CANCELED_BY_ORGANISM = 'canceledByOrganism',
    ACCEPTED = 'accepted', // => inTraining => IN_TRAINING // cancel => CANCELED_BY_ORGANISM
    IN_TRAINING = 'inTraining', // => terminate => TERMINATED
    TERMINATED = 'terminated', // => service done => SERVICE_DONE_DECLARED
    SERVICE_DONE_DECLARED = 'serviceDoneDeclared',
    SERVICE_DONE_VALIDATED = 'serviceDoneValidated'
}

export enum RegistrationFolderControlStates {
    NOT_IN_CONTROL = 'notInControl',
    IN_CONTROL = 'inControl',
    RELEASED = 'released'
}

export interface Solicitation {
    amount: number;
    status: string;
    returnDate: Date;
    fundingType: number;
    refusalReason: string;
    submissionDate: Date;
}

export interface RegistrationFolder {
    attendee: Attendee;
    billingInfos: BillingInfos;
    externalId: string;
    dataProviderId?: string;
    lastUpdate: Date;
    state: RegistrationFolderStates;
    trainingActionInfo: TrainingActionInfo;
    certificationFolder?: CertificationFolder;
    _links: Links;
    type: DataProviders;
    billingState: RegistrationFolderBillingStates;
    billNumber?: string;
    tags?: string[];
    notes?: string;
    description?: string;
    inPartnershipWith?: string;
    priceChange: {
        [key: string]: number;
    };
    completionRate?: number;
    expectedCompletionRate?: number;
    files: RegistrationFolderFile[];
    controlState?: RegistrationFolderControlStates;
    attendeeLink?: string;
    surveyLink?: string;
    externalLink?: string;
    history?: RegistrationFolderHistory;
    isAllowActions: boolean;
    metadata?: string;
    paymentDate?: Date;
    permalink?: string;
}

export type RegistrationFolderFile = File;

export interface RegistrationFolderCancelRefuse {
    code: string;
    description?: string;
}

export interface RegistrationFolderInTraining {
    /**
     * Constraints:
     * - min: session start date
     * - max: session end date
     */
    date: Date; // default today
}

export interface RegistrationFolderServiceDone {
    absenceDuration: number; // default 0 hours
    forceMajeureAbsence: boolean; // default false
}

export interface RegistrationFolderDialogServiceDone extends RegistrationFolderServiceDone {
    /**
     * Constraints:
     * - min: session start date
     * - max: session end date
     */
    date?: Date;
    code?: string;
}

export interface RegistrationFolderTerminate {
    code: string; // default code 8
    /**
     * Constraints:
     * - min: session start date
     * - max: session end date
     */
    date: Date; // default today
}

export interface RegistrationFolderToBill {
    billNumber: string;
}

export interface RegistrationFolderCreate {
    attendeeId: number;
    sessionId: number;
    type: string;
    totalTTC: number;
    poleEmploiId?: string;
    poleEmploiRegionCode?: string;
    poleEmploiDevis?: string;
    inPartnershipWith?: string;
}

export interface RegistrationFolderUpdate {
    addedTags?: string[];
    removedTags?: string[];
}

export interface RegistrationFolderUpdateBody extends RegistrationFolder {
    addedTags?: string[];
    removedTags?: string[];
}

export interface SessionMinDates {
    cpfSessionMinDate: string;
    poleEmploiSessionMinDate: string;
}

export interface RegistrationFolderHistory {
    acceptedDate: Date;
    billedDate: Date;
    canceledByAttendeeDate: Date;
    canceledByAttendeeNotRealizedDate: Date;
    canceledByFinancerDate: Date;
    canceledByOrganismDate: Date;
    completionRateLastUpdate: Date;
    inControlDate: Date;
    inTrainingDate: Date;
    notProcessedDate: Date;
    paidDate: Date;
    refusedByAttendeeDate: Date;
    refusedByOrganismDate: Date;
    rejectedWithoutTitulaireSuiteDate: Date;
    releasedDate: Date;
    serviceDoneDeclaredAttendeeDate: Date;
    serviceDoneDeclaredDate: Date;
    serviceDoneValidatedDate: Date;
    terminatedDate: Date;
    validatedDate: Date;
}

export const RegistrationFolderConfig: Record<RegistrationFolderStates,
    {
        color: ThemePalette,
        state: RegistrationFolderActionsAndStates
    }[]> = {
    [RegistrationFolderStates.NOT_PROCESSED]: [
        {
            color: 'primary',
            state: RegistrationFolderStates.VALIDATED,
        },
        {
            color: 'warn',
            state: RegistrationFolderStates.REFUSED_BY_ORGANISM,
        },
    ],
    [RegistrationFolderStates.VALIDATED]: [
        {
            color: 'primary',
            state: RegistrationFolderStates.ACCEPTED,
        },
        {
            color: 'warn',
            state: RegistrationFolderStates.WAITING_ACCEPTATION,
        },
        {
            color: 'warn',
            state: RegistrationFolderStates.REFUSED_BY_ATTENDEE,
        },
        {
            color: 'warn',
            state: RegistrationFolderStates.REJECTED_WITHOUT_TITULAIRE_SUITE,
        }
    ],
    [RegistrationFolderStates.WAITING_ACCEPTATION]: [
        {
            color: 'primary',
            state: RegistrationFolderStates.ACCEPTED,
        },
        {
            color: 'primary',
            state: RegistrationFolderStates.VALIDATED,
        }
    ],
    [RegistrationFolderStates.CANCELED_BY_ATTENDEE]: [],
    [RegistrationFolderStates.CANCELED_BY_ATTENDEE_NOT_REALIZED]: [],
    [RegistrationFolderStates.CANCELED_BY_ORGANISM]: [],
    [RegistrationFolderStates.CANCELED_BY_FINANCER]: [],
    [RegistrationFolderStates.REFUSED_BY_ATTENDEE]: [],
    [RegistrationFolderStates.REFUSED_BY_ORGANISM]: [],
    [RegistrationFolderStates.REFUSED_BY_FINANCER]: [],
    [RegistrationFolderStates.REJECTED_WITHOUT_TITULAIRE_SUITE]: [],
    [RegistrationFolderStates.ACCEPTED]: [
        {
            color: 'primary',
            state: RegistrationFolderStates.IN_TRAINING
        },
        {
            color: 'warn',
            state: RegistrationFolderStates.CANCELED_BY_ORGANISM
        },
        {
            color: 'warn',
            state: RegistrationFolderStates.CANCELED_BY_ATTENDEE_NOT_REALIZED
        }
    ],
    [RegistrationFolderStates.IN_TRAINING]: [
        {
            color: 'primary',
            state: RegistrationFolderStates.TERMINATED
        },
        {
            color: 'primary',
            state: RegistrationFolderStates.SERVICE_DONE_DECLARED
        },
    ],
    [RegistrationFolderStates.TERMINATED]: [
        {
            color: 'primary',
            state: RegistrationFolderStates.SERVICE_DONE_DECLARED
        },
    ],
    [RegistrationFolderStates.SERVICE_DONE_DECLARED]: [
        {
            color: 'primary',
            state: RegistrationFolderStates.SERVICE_DONE_VALIDATED
        },
    ],
    [RegistrationFolderStates.SERVICE_DONE_VALIDATED]: [
        {
            color: 'primary',
            state: RegistrationFolderActions.TO_BILL
        },
        {
            color: 'primary',
            state: RegistrationFolderActions.PAID
        },
    ]
};

export const RegistrationFolderStatesGeneratedConfig: Record<RegistrationFolderStates, RegistrationFolderStates[]> = {
    [RegistrationFolderStates.NOT_PROCESSED]: [
        RegistrationFolderStates.NOT_PROCESSED,
        RegistrationFolderStates.VALIDATED,
        RegistrationFolderStates.WAITING_ACCEPTATION,
        RegistrationFolderStates.ACCEPTED,
        RegistrationFolderStates.IN_TRAINING,
        RegistrationFolderStates.TERMINATED,
        RegistrationFolderStates.SERVICE_DONE_DECLARED,
        RegistrationFolderStates.SERVICE_DONE_VALIDATED,
        RegistrationFolderStates.CANCELED_BY_ATTENDEE,
        RegistrationFolderStates.CANCELED_BY_ATTENDEE_NOT_REALIZED,
        RegistrationFolderStates.CANCELED_BY_ORGANISM,
        RegistrationFolderStates.CANCELED_BY_FINANCER,
        RegistrationFolderStates.REFUSED_BY_ATTENDEE,
        RegistrationFolderStates.REFUSED_BY_ORGANISM,
        RegistrationFolderStates.REFUSED_BY_FINANCER,
        RegistrationFolderStates.REJECTED_WITHOUT_TITULAIRE_SUITE
    ],
    [RegistrationFolderStates.VALIDATED]: [
        RegistrationFolderStates.VALIDATED,
        RegistrationFolderStates.WAITING_ACCEPTATION,
        RegistrationFolderStates.ACCEPTED,
        RegistrationFolderStates.IN_TRAINING,
        RegistrationFolderStates.TERMINATED,
        RegistrationFolderStates.SERVICE_DONE_DECLARED,
        RegistrationFolderStates.SERVICE_DONE_VALIDATED,
        RegistrationFolderStates.CANCELED_BY_ATTENDEE_NOT_REALIZED,
        RegistrationFolderStates.CANCELED_BY_FINANCER,
        RegistrationFolderStates.REFUSED_BY_ATTENDEE,
        RegistrationFolderStates.REFUSED_BY_FINANCER,
        RegistrationFolderStates.REJECTED_WITHOUT_TITULAIRE_SUITE
    ],
    [RegistrationFolderStates.WAITING_ACCEPTATION]: [
        RegistrationFolderStates.WAITING_ACCEPTATION,
        RegistrationFolderStates.VALIDATED,
        RegistrationFolderStates.ACCEPTED,
        RegistrationFolderStates.IN_TRAINING,
        RegistrationFolderStates.TERMINATED,
        RegistrationFolderStates.SERVICE_DONE_DECLARED,
        RegistrationFolderStates.SERVICE_DONE_VALIDATED,
        RegistrationFolderStates.CANCELED_BY_ATTENDEE_NOT_REALIZED,
        RegistrationFolderStates.CANCELED_BY_ORGANISM,
        RegistrationFolderStates.CANCELED_BY_FINANCER,
        RegistrationFolderStates.REFUSED_BY_ATTENDEE,
        RegistrationFolderStates.REFUSED_BY_FINANCER,
        RegistrationFolderStates.REJECTED_WITHOUT_TITULAIRE_SUITE,
    ],
    [RegistrationFolderStates.ACCEPTED]: [
        RegistrationFolderStates.ACCEPTED,
        RegistrationFolderStates.IN_TRAINING,
        RegistrationFolderStates.TERMINATED,
        RegistrationFolderStates.SERVICE_DONE_DECLARED,
        RegistrationFolderStates.SERVICE_DONE_VALIDATED,
        RegistrationFolderStates.REJECTED_WITHOUT_TITULAIRE_SUITE,
        RegistrationFolderStates.REFUSED_BY_ORGANISM
    ],
    [RegistrationFolderStates.IN_TRAINING]: [
        RegistrationFolderStates.IN_TRAINING,
        RegistrationFolderStates.TERMINATED,
        RegistrationFolderStates.SERVICE_DONE_DECLARED,
        RegistrationFolderStates.SERVICE_DONE_VALIDATED
    ],
    [RegistrationFolderStates.TERMINATED]: [
        RegistrationFolderStates.TERMINATED,
        RegistrationFolderStates.SERVICE_DONE_DECLARED,
        RegistrationFolderStates.SERVICE_DONE_VALIDATED
    ],
    [RegistrationFolderStates.SERVICE_DONE_DECLARED]: [RegistrationFolderStates.SERVICE_DONE_DECLARED, RegistrationFolderStates.SERVICE_DONE_VALIDATED],
    [RegistrationFolderStates.SERVICE_DONE_VALIDATED]: [RegistrationFolderStates.SERVICE_DONE_VALIDATED],
    [RegistrationFolderStates.CANCELED_BY_ATTENDEE]: [RegistrationFolderStates.CANCELED_BY_ATTENDEE],
    [RegistrationFolderStates.CANCELED_BY_ATTENDEE_NOT_REALIZED]: [RegistrationFolderStates.CANCELED_BY_ATTENDEE_NOT_REALIZED],
    [RegistrationFolderStates.CANCELED_BY_ORGANISM]: [RegistrationFolderStates.CANCELED_BY_ORGANISM],
    [RegistrationFolderStates.CANCELED_BY_FINANCER]: [RegistrationFolderStates.CANCELED_BY_FINANCER],
    [RegistrationFolderStates.REFUSED_BY_ATTENDEE]: [RegistrationFolderStates.REFUSED_BY_ATTENDEE],
    [RegistrationFolderStates.REFUSED_BY_ORGANISM]: [RegistrationFolderStates.REFUSED_BY_ORGANISM],
    [RegistrationFolderStates.REFUSED_BY_FINANCER]: [RegistrationFolderStates.REFUSED_BY_FINANCER],
    [RegistrationFolderStates.REJECTED_WITHOUT_TITULAIRE_SUITE]: [RegistrationFolderStates.REJECTED_WITHOUT_TITULAIRE_SUITE]
};
