import {UserLink} from './link';
import {EntityClass} from '../../utils/enums/entity-class';

export interface Activity {
    id: number;
    type: ActivityTypes;
    eventTime?: Date;
    eventEndTime?: Date;
    origin?: string;
    link?: string;
    title?: string;
    description?: string;
    field?: string;
    entityClass: string;
    entityId: string;
    previousValue?: string;
    newValue?: string;
    qualiopiIndicators?: number[];
    dueDate?: Date;
    done?: boolean;
    _links?: Links;
}

export interface Links {
    user: UserLink;
}

export enum ActivityTypes {
    CREATE = 'create',
    UPDATE = 'update',
    PROGRESS = 'progress',
    UPDATE_STATE = 'updateState',
    PHONE = 'phone',
    EMAIL = 'email',
    MEETING = 'meeting',
    CHAT = 'chat',
    SMS = 'sms',
    EXAMINATION = 'examination',
    TRAINING = 'training',
    CDC = 'cdc',
    REMARK = 'remark',
    FILE = 'file',
    CONNECTION_LOG  = 'connectionLog'
}

export interface ActivityCreateBody {
    type: ActivityTypes;
    eventTime?: Date;
    eventEndTime?: Date;
    origin?: string;
    link?: string;
    title?: string;
    description?: string;
    qualiopiIndicators?: number[];
    userEmail: string;
    dueDate?: Date;
}

export interface ActivityUpdateBody {
    type?: ActivityTypes;
    eventTime?: Date;
    eventEndTime?: Date;
    origin?: string;
    link?: string;
    title?: string;
    description?: string;
    qualiopiIndicators?: number[];
    userEmail?: string;
    dueDate?: Date;
    done?: boolean;
}

export interface ActivityMoveBody {
    entityClass: EntityClass;
    entityId: string;
}
