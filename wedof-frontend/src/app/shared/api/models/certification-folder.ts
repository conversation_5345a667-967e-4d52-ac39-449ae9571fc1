import {File} from './file';
import {CertificationLink, Link, OrganismLink, RegistrationFolderLink} from './link';
import {Attendee} from './attendee';
import {CertificationExaminationType} from './certification';
import {Organism} from './organism';
import {ThemePalette} from '@angular/material/core';
import {Skill} from './skill';

export interface CertificationFolder {
    id: number;
    externalId?: string;
    examinationDate?: Date;
    examinationEndDate?: Date;
    examinationPlace?: string;
    examinationType?: CertificationExaminationType;
    issueDate?: Date;
    expirationDate?: Date;
    detailedResult?: string;
    digitalProofLink?: string;
    state: CertificationFolderStates;
    comment: string;
    files?: CertificationFolderFile[];
    attendee: Attendee;
    history: CertificationFolderHistory;
    _links: Links;
    stateLastUpdate?: Date;
    tags?: string[];
    certifiedData: boolean;
    type?: CertificationFolderType;
    gradePass?: CertificationFolderGradePass;
    examinationCenterZipCode?: string;
    europeanLanguageLevel?: EuropeanLanguageLevel;
    accessModality?: AccessModality;
    accessModalityVae?: AccessModalityVae;
    verbatim?: string;
    optionName?: string;
    cdcState?: CertificationFolderCdcStates;
    cdcCompliant?: boolean;
    cdcToExport?: boolean;
    enrollmentDate?: Date;
    amountHt?: number;
    inTraining: boolean;
    cdcExcluded?: boolean;
    certificateId?: string;
    attendeeLink?: string;
    surveyLink?: string;
    addToPassportLink?: string;
    partner?: Organism | string;
    metadata?: string;
    permalink?: string;
    tiersTemps?: boolean;
    fullCertification?: boolean;
    skillSets: Skill[];
    badgeAssertion?: string;
    passportType: PassportType;
}

export enum EuropeanLanguageLevel {
    C2 = 'C2',
    C1 = 'C1',
    B2 = 'B2',
    B1 = 'B1',
    A2 = 'A2',
    A1 = 'A1',
    INSUFFISANT = 'INSUFFISANT'
}

export enum AccessModality {
    FORMATION_INITIALE_HORS_APPRENTISSAGE = 'FORMATION_INITIALE_HORS_APPRENTISSAGE',
    FORMATION_INITIALE_APPRENTISSAGE = 'FORMATION_INITIALE_APPRENTISSAGE',
    FORMATION_CONTINUE_HORS_CONTRAT_DE_PROFESSIONNALISATION = 'FORMATION_CONTINUE_HORS_CONTRAT_DE_PROFESSIONNALISATION',
    FORMATION_CONTINUE_CONTRAT_DE_PROFESSIONNALISATION = 'FORMATION_CONTINUE_CONTRAT_DE_PROFESSIONNALISATION',
    VAE = 'VAE',
    EQUIVALENCE = 'EQUIVALENCE_(DIPLOME_ETRANGER)',
    CANDIDAT_LIBRE = 'CANDIDAT_LIBRE',
}

export enum AccessModalityVae {
    CONGES_VAE = 'CONGES_VAE',
    VAE_CLASSIQUE = 'VAE_CLASSIQUE'
}

export enum CertificationFolderGradePass {
    SANS_MENTION = 'SANS_MENTION',
    MENTION_ASSEZ_BIEN = 'MENTION_ASSEZ_BIEN',
    MENTION_BIEN = 'MENTION_BIEN',
    MENTION_TRES_BIEN = 'MENTION_TRES_BIEN',
    MENTION_TRES_BIEN_AVEC_FELICITATIONS_DU_JURY = 'MENTION_TRES_BIEN_AVEC_FELICITATIONS_DU_JURY',
}

export enum CertificationFolderType {
    CERTIFIE = 'CERTIFIE',
    OF = 'OF',
    POLE_EMPLOI = 'POLE_EMPLOI',
    EMPLOYEUR = 'EMPLOYEUR',
    AUTRE = 'AUTRE'
}

export interface CertificationFoldersCdcFiles {
    id: number;
    state: CertificationFoldersCdcFilesState;
    cdcFile: CdcFile;
    errorMessage?: string;
}

export enum CertificationFoldersCdcFilesState {
    EXPORTED = 'exported',
    PROCESSED_OK = 'processedOk',
    PROCESSED_KO = 'processedKo',
    ABORTED = 'aborted'
}

export interface CdcFile {
    id: number;
    state: CdcFileStates;
    name: string;
    stateLastUpdate?: Date;
    submissionDate?: Date;
    createdOn: Date;
    generatedAutomatically?: boolean;
    hasCertificationFolderError: boolean;
}

export enum CdcFileStates {
    EXPORTED = 'exported',
    ABORTED = 'aborted',
    PROCESSED = 'processed'
}

export interface CertificationFolderCreate {
    attendeeId: number;
    certifInfo: string;
    type?: CertificationFolderType;
    enrollmentDate?: Date;
    partner?: string;
    skillSets?: Skill[];
}

export type CertificationFolderFile = File;

// Si cette liste évolue, la mettre à jour dans la variable "sortedStates" / "getAllActions" / certification-folder-menu.component.ts
export enum CertificationFolderStates {
    TO_REGISTER = 'toRegister',
    REGISTERED = 'registered',
    TO_TAKE = 'toTake',
    TO_CONTROL = 'toControl',
    SUCCESS = 'success',
    TO_RETAKE = 'toRetake',
    FAILED = 'failed',
    REFUSED = 'refused',
    ABORTED = 'aborted'
}

export enum CertificationFolderAccrochageStates {
    ACCROCHAGE_OK = 'accrochageOk',
    ACCROCHAGE_KO = 'accrochageKo',
    MISSING_DATA = 'missingData'
}

export enum CertificationFolderErrorStates {
    FAILED = 'failed',
    REFUSED = 'refused',
    ABORTED = 'aborted'
}

export interface CertificationFolderExamRegistered {
    examinationPlace?: string;
    examinationType?: CertificationExaminationType;
    examinationDate: Date;
    examinationEndDate?: Date;
    comment: string;
    enrollmentDate?: Date;
}

export interface CertificationFolderExamRefused {
    comment: string;
}

export interface CertificationFolderExamToTake {
    examinationType?: CertificationExaminationType;
    examinationPlace?: string;
    examinationDate: Date;
    examinationEndDate?: Date;
    comment: string;
    enrollmentDate?: Date;
    tiersTemps?: boolean;
}

export interface CertificationFolderExamToControl {
    examinationType?: CertificationExaminationType;
    examinationPlace?: string;
    examinationDate: Date;
    examinationEndDate?: Date;
    comment: string;
    enrollmentDate?: Date;
}

export interface CertificationFolderDialogExamFailed {
    canRetake: boolean;
    toControl: CertificationFolderExamToControl;
    failed: CertificationFolderExamFailed;
    retake: CertificationFolderExamRetake;
}

export interface CertificationFolderExamFailed {
    detailedResult: string;
    comment: string;
    europeanLanguageLevel?: EuropeanLanguageLevel;
}

export interface CertificationFolderExamRetake {
    detailedResult: string;
    comment: string;
    examinationPlace?: string;
    examinationType?: CertificationExaminationType;
    examinationDate: Date;
    examinationEndDate?: Date;
    europeanLanguageLevel?: EuropeanLanguageLevel;
    tiersTemps?: boolean;
}

export interface CertificationFolderDialogExamSuccess {
    toControl: CertificationFolderExamToControl;
    success: CertificationFolderExamSuccess;
}

export interface CertificationFolderExamSuccess {
    digitalProofLink: string;
    issueDate: Date;
    detailedResult: string;
    comment: string;
    certifiedData?: boolean;
    gradePass?: CertificationFolderGradePass;
    certificate?: File | string;
    europeanLanguageLevel?: EuropeanLanguageLevel;
    badgeAssertion?: string;
}

export interface CertificationFolderExamAborted {
    comment: string;
}

export interface CertificationFolderUpdate {
    amountHt?: number;
    addedTags?: string[];
    removedTags?: string[];
}

export interface CertificationFolderUpdateBody extends CertificationFolder {
    addedTags?: string[];
    removedTags?: string[];
}

export interface CertificationFolderHistory {
    toTakeDate: Date;
    failedDate: Date;
    successDate: Date;
    toRegisterDate: Date;
    registeredDate: Date;
    abortedDate: Date;
    toControlDate: Date;
    refusedDate: Date;
    toRetakeDate: Date;
    inTrainingStartedDate: Date;
    inTrainingEndedDate: Date;
}

export interface Links {
    self: Link;
    partner: OrganismLink;
    certifier: OrganismLink;
    certification: CertificationLink;
    registrationFolder: RegistrationFolderLink;
    activities: Link;
    survey: Link;
}

export enum PassportType {
    COMPETENCES = 'competences',
    PREVENTION = 'prevention'
}

export enum CertificationFolderCdcStates {
    NOT_EXPORTED = 'notExported',
    EXPORTED = 'exported',
    PROCESSED_OK = 'processedOk',
    PROCESSED_KO = 'processedKo'
}

export enum CertificationFolderActionType {
    GO_BACK = 'goBack',
    DECLARE = 'declare',
    UPDATE = 'update'
}

export interface PassportDataDetails {
    needUpdate: boolean;
    passportType: PassportType;
    certificationName: string;
    certificationFolderState: CertificationFolderStates;
}

export const CertificationFolderConfig: Record<CertificationFolderStates, {
    color: ThemePalette,
    state: CertificationFolderStates,
    actionType: CertificationFolderActionType
}[]> = {
    [CertificationFolderStates.TO_REGISTER]: [
        {
            color: 'primary',
            state: CertificationFolderStates.REGISTERED,
            actionType: CertificationFolderActionType.DECLARE
        },
        {
            color: 'warn',
            state: CertificationFolderStates.REFUSED,
            actionType: CertificationFolderActionType.DECLARE,
        },
        {
            color: 'warn',
            state: CertificationFolderStates.ABORTED,
            actionType: CertificationFolderActionType.DECLARE
        }
    ],
    [CertificationFolderStates.REGISTERED]: [
        {
            color: 'primary',
            state: CertificationFolderStates.TO_TAKE,
            actionType: CertificationFolderActionType.DECLARE
        },
        {
            color: 'warn',
            state: CertificationFolderStates.TO_REGISTER,
            actionType: CertificationFolderActionType.GO_BACK
        },
        {
            color: 'warn',
            state: CertificationFolderStates.ABORTED,
            actionType: CertificationFolderActionType.DECLARE
        }
    ],
    [CertificationFolderStates.TO_TAKE]: [
        {
            color: 'primary',
            state: CertificationFolderStates.TO_CONTROL,
            actionType: CertificationFolderActionType.DECLARE
        },
        {
            color: 'primary',
            state: CertificationFolderStates.SUCCESS,
            actionType: CertificationFolderActionType.DECLARE
        },
        {
            color: 'warn',
            state: CertificationFolderStates.FAILED,
            actionType: CertificationFolderActionType.DECLARE
        },
        {
            color: 'warn',
            state: CertificationFolderStates.REGISTERED,
            actionType: CertificationFolderActionType.GO_BACK
        },
        {
            color: 'warn',
            state: CertificationFolderStates.ABORTED,
            actionType: CertificationFolderActionType.DECLARE
        },
    ],
    [CertificationFolderStates.TO_CONTROL]: [
        {
            color: 'primary',
            state: CertificationFolderStates.SUCCESS,
            actionType: CertificationFolderActionType.DECLARE
        },
        {
            color: 'warn',
            state: CertificationFolderStates.FAILED,
            actionType: CertificationFolderActionType.DECLARE
        },
        {
            color: 'primary',
            state: CertificationFolderStates.TO_RETAKE,
            actionType: CertificationFolderActionType.DECLARE
        },
        {
            color: 'warn',
            state: CertificationFolderStates.TO_TAKE,
            actionType: CertificationFolderActionType.GO_BACK
        }
    ],
    [CertificationFolderStates.SUCCESS]: [
        {
            color: 'warn',
            state: CertificationFolderStates.TO_CONTROL,
            actionType: CertificationFolderActionType.GO_BACK
        }
    ],
    [CertificationFolderStates.TO_RETAKE]: [
        {
            color: 'primary',
            state: CertificationFolderStates.TO_CONTROL,
            actionType: CertificationFolderActionType.DECLARE
        },
        {
            color: 'warn',
            state: CertificationFolderStates.ABORTED,
            actionType: CertificationFolderActionType.DECLARE
        }
    ],
    [CertificationFolderStates.FAILED]: [
        {
            color: 'primary',
            state: CertificationFolderStates.TO_RETAKE,
            actionType: CertificationFolderActionType.DECLARE
        },
        {
            color: 'primary',
            state: CertificationFolderStates.TO_CONTROL,
            actionType: CertificationFolderActionType.GO_BACK
        }
    ],
    [CertificationFolderStates.REFUSED]: [
        {
            color: 'primary',
            state: CertificationFolderStates.TO_REGISTER,
            actionType: CertificationFolderActionType.DECLARE
        }
    ],
    [CertificationFolderStates.ABORTED]: [
        {
            color: 'primary',
            state: CertificationFolderStates.TO_REGISTER,
            actionType: CertificationFolderActionType.DECLARE
        },
        {
            color: 'primary',
            state: CertificationFolderStates.REGISTERED,
            actionType: CertificationFolderActionType.DECLARE
        },
        {
            color: 'primary',
            state: CertificationFolderStates.TO_TAKE,
            actionType: CertificationFolderActionType.DECLARE
        },
        {
            color: 'primary',
            state: CertificationFolderStates.TO_RETAKE,
            actionType: CertificationFolderActionType.DECLARE
        },
    ]
};

export const CertificationFolderStatesGeneratedConfig: Record<CertificationFolderStates, string[]> = {
    [CertificationFolderStates.TO_REGISTER]: [
        CertificationFolderStates.TO_REGISTER,
        CertificationFolderStates.REGISTERED,
        CertificationFolderStates.REFUSED,
        CertificationFolderStates.ABORTED,
        CertificationFolderStates.TO_TAKE,
        CertificationFolderStates.TO_CONTROL,
        CertificationFolderStates.TO_RETAKE,
        CertificationFolderStates.FAILED,
        CertificationFolderStates.SUCCESS
    ],
    [CertificationFolderStates.REGISTERED]: [
        CertificationFolderStates.REGISTERED,
        CertificationFolderStates.ABORTED,
        CertificationFolderStates.TO_TAKE,
        CertificationFolderStates.TO_CONTROL,
        CertificationFolderStates.TO_RETAKE,
        CertificationFolderStates.FAILED,
        CertificationFolderStates.SUCCESS
    ],
    [CertificationFolderStates.TO_TAKE]: [
        CertificationFolderStates.TO_TAKE,
        CertificationFolderStates.ABORTED,
        CertificationFolderStates.TO_CONTROL,
        CertificationFolderStates.TO_RETAKE,
        CertificationFolderStates.FAILED,
        CertificationFolderStates.SUCCESS
    ],
    [CertificationFolderStates.TO_CONTROL]: [
        CertificationFolderStates.TO_CONTROL,
        CertificationFolderStates.TO_RETAKE,
        CertificationFolderStates.FAILED,
        CertificationFolderStates.SUCCESS
    ],
    [CertificationFolderStates.TO_RETAKE]: [
        CertificationFolderStates.TO_RETAKE,
        CertificationFolderStates.TO_CONTROL,
        CertificationFolderStates.FAILED,
        CertificationFolderStates.SUCCESS
    ],
    [CertificationFolderStates.FAILED]: [
        CertificationFolderStates.FAILED,
        CertificationFolderStates.TO_RETAKE,
        CertificationFolderStates.TO_CONTROL,
        CertificationFolderStates.FAILED,
        CertificationFolderStates.SUCCESS
    ],
    [CertificationFolderStates.SUCCESS]: [CertificationFolderStates.SUCCESS],
    [CertificationFolderStates.REFUSED]: [CertificationFolderStates.REFUSED],
    [CertificationFolderStates.ABORTED]: [CertificationFolderStates.ABORTED]
};
