import {AttendeeExperience} from './attendee-experience';

export interface AttendeeAddress {
    city: string;
    corporateName: string;
    number: string;
    repetitionIndex: string;
    repetitionIndexLabel: string;
    residence: string;
    roadName: string;
    roadType: string;
    roadTypeLabel: string;
    zipCode: string;
}

export interface Attendee {
    address?: AttendeeAddress;
    email: string;
    firstName: string;
    firstName2?: string;
    firstName3?: string;
    lastName: string;
    phoneNumber: string;
    phoneFixed?: string;
    description?: string; // for proposal generation
    dateOfBirth?: Date;
    nameCityOfBirth?: string; // birth city
    codeCityOfBirth?: string; // birth code insee
    codeCountryOfBirth?: number; // country's code cog
    nameCountryOfBirth?: string; // country's name
    birthName?: string;
    gender?: Gender;
    id: number;
    degreeTitle?: string;
    readOnly?: boolean;
    identificationDocument?: File | string;
    experiences?: AttendeeExperience[];
    postalCode?: string;
    emailValidated?: boolean;
    phoneNumberValidated?: boolean;
    fullAddress?: string;
    nirValidated?: boolean;
    retrievedNir?: string;
}

export interface City {
    cog: string;
    name: string;
    postalCode?: string;
}

export interface Country {
    name: string;
    cog: number;
    code: string;
    code3: string;
}

export interface CandidatePassportBody {
    firstName: string;
    firstName2?: string;
    firstName3?: string;
    lastName: string;
    birthName?: string;
    gender?: Gender;
    dateOfBirth?: Date;
    nameCityOfBirth?: string;
    codeCityOfBirth?: string;
    postalCode?: string;
    nameCountryOfBirth?: string;
    codeCountryOfBirth?: number;
    identificationDocument?: File;
    email?: string;
    phoneNumber?: string;
    address?: AttendeeAddressBody;
    retrievedNir?: string;
    emailValidated?: boolean;
    phoneNumberValidated?: boolean;
    nirValidated?: boolean;
}

export interface CandidateSocialSecurityBody {
    firstName: string;
    lastName: string;
    nir: string;
}

export enum Gender {
    MALE = 'male',
    FEMALE = 'female'
}

export interface AttendeeCreateBody {
    lastName: string;
    firstName: string;
    email: string;
    phoneNumber?: string;
    phoneFixed?: string;
    dateOfBirth?: Date;
    nameCityOfBirth?: string;
    codeCountryOfBirth?: number;
    nameCountryOfBirth?: string;
    gender?: string;
    birthName?: string;
    codeCityOfBirth?: string;
    identificationDocument?: File | string;
    address?: AttendeeAddressBody;
    nir?: string;
}

interface AttendeeAddressBody {
    number?: string;
    repetitionIndexLabel?: string;
    roadTypeLabel?: string;
    roadName?: string;
    zipCode?: string;
    city?: string;
}
