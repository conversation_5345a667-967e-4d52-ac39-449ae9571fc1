import {File, FileType} from './file';
import {CertificationLink, Link} from './link';

export interface CertificationPartnerAuditTemplate {
    id?: number;
    name: string;
    allowVisibilityPartner: boolean;
    criterias?: CertificationPartnerAuditCriteria[];
    auditTemplateFileType?: CertificationPartnerAuditFileType;
    auditCount?: {
        pendingComputationOrComputing?: number,
        all?: number,
    };
    _links?: {
        certification: CertificationLink
    };
    allowCreateAudit?: boolean;
}

export type CertificationPartnerAuditFileType = FileType<CertificationPartnerAuditStates>;

export interface CertificationPartnerAuditTemplateCreateBody extends Omit<CertificationPartnerAuditTemplate, 'id'> {
    certifInfo: string;
    googleId: string;
}

export interface CertificationPartnerAudit {
    id?: number;
    state: CertificationPartnerAuditStates;
    evaluatedCriterias?: CertificationPartnerAuditEvaluatedCriteria[];
    result?: CertificationPartnerAuditResults;
    startDate: Date;
    endDate?: Date;
    notes?: string;
    report?: File;
    _links?: Links;
    errorMessage?: string;
}

export interface CertificationPartnerAuditCriteria {
    // if new value added don't forget to update json configuration in drive
    code: string;
    title: string;
    scope: string;
    scopeTitle: string;
    operation: CertificationPartnerAuditCriteriaOperation;
    operationTitle: string;
    parameter?: any;
    advice?: string;
    operationValues?: object;
    severity?: CertificationPartnerAuditCriteriaSeverity;
}

export enum CertificationPartnerAuditCriteriaOperation {
    MANUAL = 'manual',
    AI = 'ai',
    // string
    CONTAINS = 'contains',
    CONTAINS_ALL = 'containsAll',
    NOT_CONTAINS = 'notContains',
    NOT_CONTAINS_ANY = 'notContainsAny',
    MATCHES = 'matches',
    EQUALS_STRING = 'eqs',
    NOT_EQUALS_STRING = 'neqs',
    // number
    EQUALS_NUMBER = 'eq',
    NOT_EQUALS_NUMBER = 'neq',
    LESS_THAN = 'lt',
    LESS_THAN_EQUAL = 'lte',
    GREATER_THAN = 'gt',
    GREATER_THAN_EQUAL = 'gte',
    // boolean
    IS_TRUE = 'isTrue',
    IS_FALSE = 'isFalse',
    // array
    CONTAINS_ARRAY = 'containsArray',
    NOT_CONTAINS_ARRAY = 'notContainsArray',
    // dates (before, after)
}

export interface CriteriaScopeSelectChoice {
    name: string;
    value: CriteriaScopeValue[];
}

export interface CriteriaScopeValue {
    key: string;
    value: string;
    type: string;
    operationValues?: object;
    tooltip?: string;
}

export type CriteriaObjectValue = {
    total: number;
    notCompliant: string[] | { id: string, link?: string }[]
};

export interface CertificationPartnerAuditEvaluatedCriteria extends CertificationPartnerAuditCriteria {
    value: number | string | CriteriaObjectValue;
    compliance: CertificationPartnerAuditCriteriaCompliances;
}

export enum CertificationPartnerAuditCriteriaCompliances {
    COMPLIANT = 'compliant',
    PARTIALLY_COMPLIANT = 'partiallyCompliant', // for later use
    NON_COMPLIANT = 'nonCompliant',
    NOT_APPLICABLE = 'notApplicable' // for later use
}

export enum CertificationPartnerAuditStates {
    PENDING_COMPUTATION = 'pendingComputation',
    COMPUTING = 'computing',
    IN_PROGRESS = 'inProgress',
    COMPLETED = 'completed',
    FAILED = 'failed'
}

export enum CertificationPartnerAuditResults { // Actually it is more the PartnerCompliance than the AuditResult
    COMPLIANT = 'compliant',
    PARTIALLY_COMPLIANT = 'partiallyCompliant',
    NON_COMPLIANT = 'nonCompliant',
    NONE = 'none',
}

export enum CertificationPartnerAuditCriteriaSeverity {
    NONE = 'none',
    MINEURE = 'mineure',
    MAJEURE = 'majeure'
}

export interface CertificationPartnerAuditCreateBody {
    templateId: number;
}

export interface CertificationPartnerAuditCreateOnPartnersBody extends CertificationPartnerAuditCreateBody {
    complete: boolean;
    suspend: boolean;
    updateCompliance: boolean;
    partnerCompliance?: CertificationPartnerAuditResults[];
}

export interface CertificationPartnerAuditCompleteBody {
    result: CertificationPartnerAuditResults;
    notes?: string;
    updatePartnerCompliance: boolean;
}

export interface CertificationPartnerAuditUpdateBody {
    restartAudit?: boolean;
}

export interface Links {
    certificationPartnerAuditTemplate: Link & {
        name: string
    };
}

