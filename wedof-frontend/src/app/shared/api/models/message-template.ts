import {EntityClass} from '../../utils/enums/entity-class';

export interface MessageTemplate {
    id?: number;
    title: string;
    subject?: string;
    type: MessageTemplateType;
    state?: MessageTemplateState;
    body: string;
    entityClass: EntityClass;
    events: string[];
    certifInfos?: string[];
    tags?: string[];
    sendAs?: string;
    replyTo: string[];
    cc?: string[];
    cci?: string[];
    to: string[];
    delay?: string;
    enforceConditions: boolean;
    qualiopiIndicators?: number[];
    allowResend: boolean;
}

export enum MessageTemplateType {
    EMAIL = 'email',
    SMS = 'sms'
}

export enum MessageTemplateState {
    ACTIVE = 'active',
    INACTIVE = 'inactive'
}

export interface MessageTemplateUpdate {
    title: string;
    subject?: string;
    body: string;
    type?: MessageTemplateType;
    entityClass: EntityClass;
    events: string[];
    certifInfos?: string[];
    tags?: string[];
    sendAs?: string;
    replyTo?: string[];
    cc?: string[];
    cci?: string[];
    to?: string[];
    delay?: string;
    enforceConditions: boolean;
    qualiopiIndicators?: number[];
    allowResend: boolean;
}
