export enum PeriodTypes {
    CUSTOM = 'custom',

    TODAY = 'today',
    YESTERDAY = 'yesterday',
    TOMORROW = 'tomorrow',

    CURRENT_WEEK = 'currentWeek',
    PREVIOUS_WEEK = 'previousWeek',
    NEXT_WEEK = 'nextWeek',
    ROLLING_WEEK = 'rollingWeek',
    ROLLING_WEEK_FUTURE = 'rollingWeekFuture',

    CURRENT_MONTH = 'currentMonth',
    PREVIOUS_MONTH = 'previousMonth',
    NEXT_MONTH = 'nextMonth',
    ROLLING_MONTH = 'rollingMonth',
    ROLLING_MONTH_FUTURE = 'rollingMonthFuture',

    CURRENT_YEAR = 'currentYear',
    PREVIOUS_YEAR = 'previousYear',
    NEXT_YEAR = 'nextYear',
    ROLLING_YEAR = 'rollingYear',
    ROLLING_YEAR_FUTURE = 'rollingYearFuture'
}

export enum PeriodTypesForCertificationFolders {
    WEDOF_INVOICE = 'wedofInvoice',
    WEDOF_QUOTA = 'wedofQuota'
}


