import {TrainingAction} from './training-action';
import {Link, OrganismLink, RegistrationFolderLink, UserLink} from './link';

export interface Links {
    self: Link;
    registrationFolder?: RegistrationFolderLink;
    organism: OrganismLink;
    trainingActions: Link;
    sales: UserLink;
}

export interface Proposal {
    amount?: number;
    code: string;
    codeRequired: boolean;
    isIndividual: boolean;
    description?: string;
    discountType?: DiscountType;
    email?: string;
    firstName?: string;
    lastName?: string;
    state: ProposalStates;
    organism?: {
        siret: string;
        name: string;
    };
    phoneNumber?: string;
    trainingActions?: Array<TrainingAction>;
    limitUsage?: number;
    usedCount?: number;
    sessionStartDate?: Date;
    sessionEndDate?: Date;
    expire?: Date;
    autoValidate?: boolean;
    link?: string;
    link_commercial?: string;
    selectedTrainingAction?: TrainingAction;
    tags?: string[];
    logo?: any;
    customColorScheme?: string;
    notes: string;
    indicativeDuration?: number;
    stateLastUpdate?: Date;
    sales?: {
        email: string;
        name?: string;
    };
    _links: Links;
    metadata?: string;
}

export enum ProposalStates {
    TEMPLATE = 'template',
    DRAFT = 'draft',
    ACTIVE = 'active',
    VIEWED = 'viewed',
    ACCEPTED = 'accepted',
    REFUSED = 'refused'
}

export enum DiscountType {
    DISCOUNT_TYPE_NONE = 'none',
    DISCOUNT_TYPE_PERCENT = 'percent',
    DISCOUNT_TYPE_PRICE = 'fixed',
    DISCOUNT_TYPE_AMOUNT = 'amount'
}



