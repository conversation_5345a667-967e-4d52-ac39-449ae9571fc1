
export interface Skill {
    fullCode: string;
    id: number;
    label: string;
    order: string;
    modalities: string;
    description: string;
    type: SkillType;
    individualSkills?: Skill[];
}

export interface SkillBody {
    certifInfo?: string;
    label?: string;
    modalities?: string;
    description?: string;
    parentSkillId?: number;
    createParentSkill?: boolean;
}

export enum SkillType {
    SKILL = 'skill',
    SKILL_SET = 'skillSet'
}

export function displaySkillSetTitle(skillSet: Skill): string {
    return 'BC' + (Number(skillSet.order) <= 9 ? ('0' + skillSet.order).slice(-2) : skillSet.order) + ' ' + skillSet.label;
}
