import {OrganismApplication} from './organism-application';

export interface OrganismApplicationEntryPoint {
    params: any;
    allowedInSubscription: boolean;
    entryPointResolved: string;
    organismApplication: OrganismApplication;
}

export interface MaFormationCode {
    'identifiantCarif': string;
    'lePlus': string;
    'prerequisNiveau': number;
    'formationOuverteSalaries': boolean;
    'formationOuverteDemandeursEmploi': boolean;
    'formationOuverteEtudiants': boolean;
    'formationOuverteEntreprise': boolean;
    'tauxReussite': number;
    'formationDisponibleVae': boolean;
    'formationDisponibleBilanCompetence': boolean;
    'difficulte': number;
}

export enum ApplicationOffers {
    MESSAGE_TEMPLATES = 'messageTemplates',
}

export interface ApplicationConfigPromotionalEntry {
    title: string;
    descriptions: readonly string[];
    image?: string;
}

export const ApplicationConfigPromotional: Record<ApplicationOffers, ApplicationConfigPromotionalEntry> = {
    [ApplicationOffers.MESSAGE_TEMPLATES]: {
        title: 'Messages et notifications',
        descriptions: [
            'Programmer l\'envoi automatique de messages à vos apprenants et vos partenaires grâce à un modèle de message défini'
        ]
    }
} as const;


