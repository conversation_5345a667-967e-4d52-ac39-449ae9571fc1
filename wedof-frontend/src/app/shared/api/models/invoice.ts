export interface Invoice {
    id: number;
    externalId: string;
    type: InvoiceTypes;
    state: InvoiceStates;
    link?: string;
    paymentLink?: string;
    entityClass: string;
    entityId: string;
    description?: string;
    dueDate?: Date;
    fileType?: string;
}

export enum InvoiceStates {
    WAITING_PAYMENT = 'waitingPayment',
    PAID = 'paid',
    CANCELED = 'canceled'
}

export enum InvoiceTypes {
    INVOICE = 'invoice',
    DEPOSIT_INVOICE = 'deposit',
    CREDIT_NOTE = 'creditNote'
}

export interface InvoiceBody {
    externalId?: string;
    type?: InvoiceTypes;
    state?: InvoiceStates;
    link?: string;
    paymentLink?: string;
    description?: string;
    dueDate?: Date;
    file?: File;
}
