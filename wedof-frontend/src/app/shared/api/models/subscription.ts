export interface Subscription {
    id: number;
    createdOn: Date;
    trainingType?: SubscriptionTrainingTypes;
    trainingStartDate?: string;
    allowAnalytics: boolean;
    allowApi: boolean;
    allowCertifiers: boolean;
    allowProposals: boolean;
    registrationFoldersNumberCount: number;
    registrationFoldersNumberLimit?: number;
    registrationFolderNumberPeriodStartDate?: Date;
    registrationFolderNumberPeriodEndDate?: Date;
    certificationFoldersNumberCount: number;
    certificationFoldersNumberAnnualCount?: number;
    certificationFoldersNumberAnnualLimit?: number;
    trainingPeriodEndDate?: string;
    trainingPeriodStartDate?: string;
    trainingPendingCancellation: boolean;
    trainingTrialEndDate?: Date;
    isAllowRegistrationFolderManualCreate: boolean;
    isStripeCustomer?: boolean;
    trainingPartnership?: string;
    certifierType?: SubscriptionCertifierTypes;
    certifierStartDate?: string;
    certifierPeriodEndDate?: string;
    certifierPeriodStartDate?: string;
    certificationFoldersNumberPeriodStartDate?: Date;
    certificationFoldersNumberPeriodEndDate?: Date;
    certifierPendingCancellation: boolean;
    certifierPartnership?: string;
    certifierTrialEndDate?: Date;
    allowCertifierPlus: boolean;
    trainingUsersLimit?: number;
    allowRegistrationFolders: boolean;
    allowedApps: string[];
    allowCertifierBetaFeatures: boolean;
    certificationFoldersNumberCap?: number;
    certificationFolderPrice?: number;
    certifierUsersLimit?: number;
    requestCount: number;
    smsSentNumberCount?: number;
    smsSentNumberPeriodStartDate?: Date;
    smsSentNumberPeriodEndDate?: Date;
    allowPaidUsage: boolean;
}

export enum SubscriptionTypes {
    TRAINING = 'training',
    CERTIFIER = 'certifier'
}

export enum SubscriptionTrainingTypes {
    NONE = 'none',
    FREE = 'free',
    TRIAL = 'trial',
    ACCESS = 'access',
    ACCESS_PLUS = 'access_plus',
    API = 'api',
    ESSENTIAL = 'essential',
    STANDARD = 'standard',
    PREMIUM = 'premium'
}

export enum SubscriptionCertifierTypes {
    NONE = 'none',
    FREE = 'free',
    TRIAL = 'trial',
    USAGE = 'usage',
    ACCESS = 'access',
    UNLIMITED = 'unlimited',
    PARTNER = 'partner'
}

export interface SubscriptionOptions {
    certifierPlus: boolean;
    price: number;
    quantity: number;
}

export interface SubscriptionConfigPromotionalEntry {
    title: string;
    descriptions: readonly string[];
    image?: string;
}

export enum SubscriptionPromotionalOffers {
    ACCROCHAGE = 'accrochage',
    CERTIFICATE = 'generateCertificate',
    STATISTICS = 'partnerStatistics',
    MANAGE_PARTNERSHIP = 'managePartnership',
    MANAGE_AUDIT = 'manageAudit',
    AUDIT = 'allowAudits',
}

export const SubscriptionConfigPromotional: Record<SubscriptionPromotionalOffers, SubscriptionConfigPromotionalEntry> = {
    [SubscriptionPromotionalOffers.ACCROCHAGE]: {
        title: 'Accrochage automatique',
        descriptions: [
            'Dépôt automatique des données d’accrochage de vos dossiers de certification',
            'Suivi des dossiers nécessitant une mise à jour avant l’accrochage ou avec un accrochage échoué pour corriger',
            'Suivi de l’historique des accrochages'
        ]
    },
    [SubscriptionPromotionalOffers.CERTIFICATE]: {
        title: 'Parchemins de certification',
        descriptions: [
            'Uploadez votre modèle, Wedof génère automatiquement les parchemins pour les stagiaires ayant validé leur certification'
        ]
    },
    [SubscriptionPromotionalOffers.STATISTICS]: {
        title: 'Statistiques sur les partenaires',
        descriptions: [
            'Visualisez les statistiques de vos partenaires : Taux de passage, taux de réussite, nombre d’actions de formations, prix min / max',
            'Accédez directement aux offres de formation de vos partenaires sur MonCompteFormation pour effectuer des contrôles'
        ]
    },
    [SubscriptionPromotionalOffers.MANAGE_PARTNERSHIP]: {
        title: 'Gestion des partenaires',
        descriptions: [
            'Activez ou révoquez vos partenaires en un clic, grâce à la synchronisation avec CertifPro (France Compétences)',
            'Gérez les documents nécessaires pour vos demandes de partenariats (Kbis, programme, Matrice de compétences…)',
            'Assurez le suivi de vos partenaires',
            'Obtenez le nombre de dossiers CPF estimés via l\'Open Data'
        ]
    },
    [SubscriptionPromotionalOffers.MANAGE_AUDIT]: { // allowCertifierPlus
        title: 'Gestion des audits de vos partenaires',
        descriptions: [
            'Activez et modifiez le modèle d\'audit des partenaires',
            'Générez l\'audit de vos partenaires en un clic'
        ]
    },
    [SubscriptionPromotionalOffers.AUDIT]: { // allowAudits
        title: 'Gestion des audits de vos partenaires',
        descriptions: [
            'Activez sur votre certification pour seulement 99 € HT / mois',
            'Définir des critères d\'exigence personnalisables',
            'Contrôler régulièrement la conformité de votre réseau de partenaires'
        ]
    }
} as const;
