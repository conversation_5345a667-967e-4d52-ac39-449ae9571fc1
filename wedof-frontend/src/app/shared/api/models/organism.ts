import {RegistrationFolderStates} from './registration-folder';
import {FileType} from './file';
import {CertificationStates} from './certification';
import {Link} from './link';

export interface Organism {
    id?: number;
    siret: string;
    siren?: string;
    name?: string;
    address?: string;
    postalCode?: string;
    city?: string;
    phones?: string[];
    agreement?: string;
    emails?: string[];
    urls?: string[];
    isCertifierOrganism?: boolean;
    isTrainingOrganism?: boolean;
    hasUsers?: boolean;
    isInitialized?: boolean;
    vat?: number;
    cdcClientId?: string;
    cdcContractId?: string;
    hasPartnerAccess?: boolean;
    isNonDiffusible?: boolean;
    registrationFolderFileTypes?: RegistrationFolderFileType[];
    accrochageDelegationDate?: Date;
    logo?: File | string;
    customColorScheme?: string;
    hasCertifierAccess?: boolean;
    subDomain?: string;
    linkedInPageUrl?: string;
    linkedInOrganisationId?: number;
    ape?: string;
    isReseller?: boolean;
    certificationFileTypes?: CertificationFileType[];
    sendAs?: { email: string, name: string }[];
    _links?: Links;
    metadata?: string;
    qualiopiVAE?: boolean;
    qualiopiTrainingAction?: boolean;
    qualiopiBilanCompetences?: boolean;
    qualiopiFormationApprentissage?: boolean;
    uaiNumber?: string;
    billingSoftware?: string;
    crm?: string;
    cpfCatalogMetadata?: {
        upload?: {
            startDate?: Date
            endDate?: Date
            fileName?: string
            state: 'inProgress' | 'done'
        }
        export?: {
            startDate?: Date
            endDate?: Date
            fileName?: string
            state: 'pendingSynchronize' | 'inProgress' | 'done'
        }
    };
    allowImportCertificationFolders?: boolean;
}

export interface Links {
    self: Link;
    reseller?: Link & {
        color: string;
        logo: string;
        name: string;
        siret: string;
    };
}

export type RegistrationFolderFileType = FileType<RegistrationFolderStates>;
export type CertificationFileType = FileType<CertificationStates>;
