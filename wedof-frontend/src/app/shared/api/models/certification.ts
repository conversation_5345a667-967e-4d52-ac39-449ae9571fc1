import {Link, OrganismLink} from './link';
import {DataQuery} from '../../material/table/abstract-table.component';
import {CertificationFolderStates} from './certification-folder';
import {CertificationPartner, CertificationPartnerStates} from './certification-partner';
import {File as WedofFile, FileType} from './file';

export interface Links {
    self: Link;
    partners: Link;
    certifiers: Link;
    skills: Link;
    defaultCertifier?: OrganismLink;
}

export interface CertificationInfo {
    partnerCount: number;
    folderCount: number;
}

export interface Certification {
    id: number;
    name: string;
    level?: string;
    descriptif?: string;
    certifInfo: string;
    objectif?: string[];
    domains?: Domain[];
    rome?: Rome[];
    nsf?: Nsf[];
    certificationInfo?: CertificationInfo;
    _links?: Links;
    updatedOn?: string;
    certificationFolderFileTypes?: CertificationFolderFileType[];
    certificationPartnerFileTypes?: CertificationPartnerFileTypes[];
    validityPeriod?: number;
    enabled: boolean;
    type?: CertificationTypes;
    code?: string;
    link?: string;
    externalId?: string;
    obtentionSystem?: ObtentionSystem;
    examinationType?: CertificationExaminationType;
    isCdcExportable?: boolean;
    certificationPartners?: CertificationPartner[];
    cpfDateEnd?: string;
    partnerCount?: number;
    amountHt?: number;
    partnershipComment?: string;
    isPromoted: boolean;
    allowPartnershipRequest: boolean;
    allowGenerateXmlAutomatically: boolean;
    allowGenerateCertificate: boolean;
    certificateTemplate?: File | string;
    certificateTemplateThumbnail?: string;
    autoRegistering?: boolean;
    openDataLastUpdate?: Date;
    estimatedRegistrationFoldersCount?: any;
    hasMultipleCertifiers: boolean;
    state: CertificationStates; // used to retrieve files
    files?: WedofFile[];
    allowAudits: boolean;
    auditsPendingCancellation: boolean;
    auditTrialEndDate?: Date;
    surveyOptional: boolean;
    allowPartialSkillSets: boolean;
}

export enum CertificationStates {
    ACTIVE = 'active',
    INACTIVE = 'inactive'
}

export enum CertificationExaminationType {
    A_DISTANCE = 'A_DISTANCE',
    EN_PRESENTIEL = 'EN_PRESENTIEL',
    MIXTE = 'MIXTE'
}

export enum ObtentionSystem {
    PAR_SCORING = 'PAR_SCORING',
    PAR_ADMISSION = 'PAR_ADMISSION',
}

export interface Domain {
    code: string;
    name: string;
}

export interface Rome {
    code: string;
    link: string;
    name: string;
}

export interface Nsf {
    code: string;
    name: string;
}

export type CertificationFolderFileType = FileType<CertificationFolderStates>;
export type CertificationPartnerFileTypes = FileType<CertificationPartnerStates>;

export interface CertificationQuery extends DataQuery {
    certifInfo: string;
}

export enum CertificationTypes {
    RS = 'RS',
    RNCP = 'RNCP',
    CPF = 'CPF',
    ELU = 'ELU',
    FICHE = 'FICHE',
    HABILITATION = 'Habilitation',
    RECONNAISSANCE = 'Reconnaissance',
    DIPLOME_ETABLISSEMENT = 'Diplôme d\'Etablissement',
    DIPLOME_UNIVERSITAIRE = 'Diplôme Universitaire',
    INTERNAL = 'CERT'
}

export interface CertificationCreateBody {
    name: string;
    link?: string;
}
