import {CertificationLink, Link, OrganismLink} from './link';
import {File} from './file';
import {ConnectionState, ConnectionStateDynamic, DataProviders} from './connection';
import {CertificationPartnerAuditResults} from './certification-partner-audit';
import {Skill} from './skill';

export interface CertificationPartner {
    id?: number;
    state: CertificationPartnerStates;
    habilitation: CertificationPartnerHabilitations;
    certifierAccessState?: string;
    certificationFoldersCount?: number;
    connections?: { name: DataProviders, state: ConnectionState | ConnectionStateDynamic, failedAt?: DataProviders }[];
    activeTrainingsCount?: number;
    activeTrainingActionsCount?: number;
    _links: Links;
    files?: CertificationPartnerFile[];
    comment?: string;
    pendingActivation?: boolean;
    pendingRevocation?: boolean;
    pendingSuspension?: boolean;
    estimatedRegistrationFoldersCount?: any;
    amountHt?: number;
    compliance: CertificationPartnerAuditResults;
    complianceLastUpdate?: Date;
    stateLastUpdate?: Date;
    tags?: string[];
    metadata?: string;
    trainingsZone?: string[];
    skillSets: Skill[];
    urls?: string[];
}

export type CertificationPartnerFile = File;

export enum CertificationPartnerHabilitations {
    TRAIN = 'train',
    EVALUATE = 'evaluate',
    TRAIN_EVALUATE = 'train_evaluate'
}

export enum CertificationPartnerStates {
    DRAFT = 'draft',
    PROCESSING = 'processing',
    ACTIVE = 'active',
    ABORTED = 'aborted',
    REFUSED = 'refused',
    REVOKED = 'revoked',
    SUSPENDED = 'suspended'
}

export interface Links {
    self: Link;
    partner: OrganismLink;
    certifier?: OrganismLink;
    certification: CertificationLink;
    registrationFolders: Link;
}

export interface CertificationPartnerUpdate {
    comment?: string;
    state?: CertificationPartnerStates;
    pendingRevocation?: boolean;
    pendingActivation?: boolean;
    pendingSuspension?: boolean;
    certifierSiret?: string;
    metadata?: string;
}

export const CertificationPartnerStatesGeneratedConfig: Record<CertificationPartnerStates, CertificationPartnerStates[]> = {
    [CertificationPartnerStates.DRAFT]: [],
    [CertificationPartnerStates.PROCESSING]: [
        CertificationPartnerStates.PROCESSING,
        CertificationPartnerStates.ACTIVE,
        CertificationPartnerStates.ABORTED,
        CertificationPartnerStates.REVOKED,
        CertificationPartnerStates.REFUSED,
        CertificationPartnerStates.SUSPENDED
    ],
    [CertificationPartnerStates.ACTIVE]: [CertificationPartnerStates.ACTIVE],
    [CertificationPartnerStates.ABORTED]: [CertificationPartnerStates.ABORTED],
    [CertificationPartnerStates.REFUSED]: [CertificationPartnerStates.REFUSED],
    [CertificationPartnerStates.REVOKED]: [ CertificationPartnerStates.REVOKED],
    [CertificationPartnerStates.SUSPENDED]: [ CertificationPartnerStates.SUSPENDED]

};
