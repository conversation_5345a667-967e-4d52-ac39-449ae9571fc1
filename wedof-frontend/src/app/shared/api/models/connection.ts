import {OrganismLink} from './link';
import {TreoMessageType} from '../../../../@treo/components/message';
import {Subscription, SubscriptionTrainingTypes} from './subscription';
import {DialogConnectionAuthComponent} from '../../connection/dialog-connection-auth/dialog-connection-auth.component';
import {AppFormFieldData} from '../../material/app-form-field/app-form-field.component';
import {Validators} from '@angular/forms';
import {DialogHabilitationComponent} from '../../../auth/dialog-habilitation-edof/dialog-habilitation.component';
import {ComponentType} from '@angular/cdk/portal';
import {SignDocumentDialogComponent} from '../../file/dialogs/sign-document-dialog/sign-document-dialog.component';
import {SnackBarComponent} from '../../material/snack-bar/snack-bar.component';
import {displayTextSnackBar} from '../../utils/displayTextSnackBar';
import {ResellerConnectionsComponent} from '../../../reseller/reseller-connections/reseller-connections.component';

export interface Connection {
    id: number;
    dataProvider: DataProviders;
    username: string;
    _links: Links;
    type?: string;
    message?: string;
    messageType?: TreoMessageType;
    state?: ConnectionState;
    isInitialized: boolean;
    existAtDataProvider: boolean;
}

export enum ConnectionState {
    INACTIVE = 'inactive',
    IN_PROGRESS = 'inProgress',
    ACTIVE = 'active',
    REFRESHING = 'refreshing',
    FAILED = 'failed',
    REVOKED = 'revoked'
}

export enum ConnectionStateDynamic {
    ACCOUNT_NOT_EXISTS = 'accountNotExist'
}

export interface Links {
    organism: OrganismLink;
}

export interface Credentials {
    username: string;
    password: string;
}

export interface AuthenticationResult {
    hasAccess: boolean;
    hasOrganismAccess?: boolean;
    errorCode?: string;
}

export enum DataProviders {
    CPF = 'cpf',
    // PASSPORT_PREVENTION ='passportPrevention',
    OPCO_CFA = 'opcoCfa',
    CDC_CERTIFIERS = 'cdcCertifiers',
    FRANCE_COMPETENCES = 'franceCompetences',
    POLE_EMPLOI = 'poleEmploi',
    KAIROS_AIF = 'kairosAif',
    INDIVIDUAL = 'individual',
    COMPANY = 'company',
    OPCO = 'opco',
    OPCO_CFA_ATLAS = 'opcoCfaAtlas',
    OPCO_CFA_AFDAS = 'opcoCfaAfdas',
    OPCO_CFA_EP = 'opcoCfaEp',
    OPCO_CFA_MOBILITES = 'opcoCfaMobilites',
    OPCO_CFA_AKTO = 'opcoCfaAkto',
    OPCO_CFA_OCAPIAT = 'opcoCfaOcapiat',
    OPCO_CFA_UNIFORMATION = 'opcoCfaUniformation',
    OPCO_CFA_2I = 'opcoCfa2i',
    OPCO_CFA_CONSTRUCTYS = 'opcoCfaConstructys',
    // OPCO_CFA_SANTE = 'opcoCfaSante',
    OPCO_CFA_OPCOMMERCE = 'opcoCfaOpcommerce',
}

export interface DataProviderConfigEntry {
    name: string;
    type?: string;
    required: boolean;
    passwordLink?: string;
    testBeforeSave?: boolean;
    documentation?: string;
    resetPasswordLink?: string;
    forTrainingOrganism: boolean;
    forCertifierOrganism: boolean;
    requiresAuthentication: boolean;
    manageRegistrationFolders: boolean;
    authorizations: readonly string[];
    usernameAppFormFieldType?: 'text' | 'email';
    afterAuthorizationsMessage?: {
        type: string;
        text: string;
        shouldDisplay?: (connection: Connection) => boolean;
    };
    confirmHabilitationDialog?: ComponentType<any>;
    documentName?: string;
    submitLabel?: string;
    submitDisabled?: boolean;
    beta?: boolean;

    displayLimitedUsage?(subscription: Subscription): boolean;

    displayLoginForm?(dialog: DialogConnectionAuthComponent | ResellerConnectionsComponent): boolean;

    manageBeforeSaveResult?(dialog: DialogConnectionAuthComponent | ResellerConnectionsComponent, data): boolean;

    getAppFormFieldsData?(appFormFieldData: AppFormFieldData[], dialog: DialogConnectionAuthComponent | ResellerConnectionsComponent): AppFormFieldData[];
}

export enum ConnectionType {
    HABILITATION = 'habilitation',
    DELEGATION = 'delegation',
}

const DefaultOpcoConfig = {
    required: true,
    type: ConnectionType.DELEGATION,
    confirmHabilitationDialog: DialogHabilitationComponent,
    passwordLink: null,
    resetPasswordLink: null,
    documentation: '/assistance/guides/organismes-formation/connexion-opco-cfa',
    testBeforeSave: true,
    forTrainingOrganism: true,
    forCertifierOrganism: false,
    requiresAuthentication: true,
    manageRegistrationFolders: false,
    authorizations: [
        'organism-informations_read',
        'folders'
    ],
    submitLabel: 'Sauvegarder',
    beta: true,
    displayLoginForm: (dialog: DialogConnectionAuthComponent) => {
        return !dialog.connection
            || dialog.connection.type === ConnectionType.DELEGATION && dialog.connection.state !== ConnectionState.INACTIVE;
    },
    getAppFormFieldsData: (appFormFieldData: AppFormFieldData[], dialog: DialogConnectionAuthComponent) => {
        appFormFieldData = [{
            controlName: 'password', // required to name it "password" for back validation in ConnectionController auth
            label: 'Clé d\'API',
            type: 'text',
            required: true,
            value: null,
            icon: 'vpn_key',
            placeholder: 'azerty12335345azert',
            removed: false,
            maxLength: 2000
        }];
        return appFormFieldData;
    }
};

export const DataProviderConfig: Record<DataProviders, DataProviderConfigEntry> = {
    [DataProviders.CPF]: {
        name: 'EDOF',
        required: true,
        type: ConnectionType.HABILITATION,
        confirmHabilitationDialog: DialogHabilitationComponent,
        passwordLink: 'https://efpconnect.emploi.gouv.fr/efpportail-web/#/services/EDOF?password=true',
        resetPasswordLink: null,
        documentation: '/assistance/guides/organismes-formation/deleguer-habilitation',
        testBeforeSave: true,
        forTrainingOrganism: true,
        forCertifierOrganism: false,
        requiresAuthentication: true,
        manageRegistrationFolders: true,
        submitLabel: 'Habilitation automatique indisponible Procédez à l\'habilitation manuelle',
        submitDisabled: true,
        authorizations: [
            'organism-informations_read',
            'folders',
            'trainings',
            'training-actions',
            'sessions',
            'payments_read'
        ],
        displayLoginForm: (dialog: DialogConnectionAuthComponent | ResellerConnectionsComponent) => {
            return false;
            // return !dialog.connection
            //     || dialog.connection.type === ConnectionType.DELEGATION && dialog.connection.state !== ConnectionState.INACTIVE // old delegation in place
            //     || dialog.connection.type === ConnectionType.HABILITATION && ![ConnectionState.ACTIVE, ConnectionState.IN_PROGRESS].includes(dialog.connection.state);
        },
        displayLimitedUsage: (subscription?: Subscription) => {
            return subscription.trainingType === SubscriptionTrainingTypes.NONE;
        }
    },
    [DataProviders.OPCO_CFA]: {
        name: 'OPCO (Apprentissage)',
        required: false,
        passwordLink: null,
        resetPasswordLink: null,
        authorizations: [],
        manageRegistrationFolders: true,
        forTrainingOrganism: true,
        forCertifierOrganism: false,
        requiresAuthentication: false
    },
    [DataProviders.KAIROS_AIF]: {
        name: 'Kairos AIF',
        required: false,
        beta: true,
        type: ConnectionType.DELEGATION,
        usernameAppFormFieldType: 'text',
        // documentation: '/assistance/guides/organismes-formation/deleguer-habilitation-kairos',
        passwordLink: 'https://www.portail-emploi.fr/portail-tap/mireconnexion?bloc-gauche=mot-de-passe-oublie',
        resetPasswordLink: 'https://www.portail-emploi.fr/portail-tap/mireconnexion?bloc-gauche=modifier-mot-de-passe',
        testBeforeSave: false,
        forTrainingOrganism: false,
        forCertifierOrganism: false,
        requiresAuthentication: true,
        manageRegistrationFolders: true,
        submitLabel: 'La procédure d\'habilitation est temporairement indisponible',
        submitDisabled: true,
        authorizations: [
            'folders'
        ],
        displayLoginForm: () => false,
        displayLimitedUsage: (subscription?: Subscription) => {
            return !subscription?.allowRegistrationFolders;
        }
    },
    [DataProviders.FRANCE_COMPETENCES]: {
        name: 'France Compétences',
        required: true,
        type: ConnectionType.HABILITATION,
        confirmHabilitationDialog: DialogHabilitationComponent,
        passwordLink: 'https://certifpro.francecompetences.fr/login#',
        resetPasswordLink: null,
        authorizations: [
            'managePartnership',
            'manageCertification'
        ],
        testBeforeSave: true,
        manageRegistrationFolders: false,
        forTrainingOrganism: false,
        forCertifierOrganism: true,
        requiresAuthentication: true,
        getAppFormFieldsData: (appFormFieldData: AppFormFieldData[], dialog: DialogConnectionAuthComponent) => {
            return appFormFieldData.concat([{
                controlName: 'mfa',
                label: 'Code de confirmation de connexion',
                type: 'number' as const,
                required: true,
                value: null,
                icon: 'pin',
                placeholder: '123456',
                removed: true
            }]);
        },
        manageBeforeSaveResult: (dialog: DialogConnectionAuthComponent, data) => {
            if (data.result === true) {
                return data;
            } else if (data.result === 'mfa') {
                dialog.appFormFieldsData.find(it => it.controlName === 'mfa').removed = false;
                dialog.appFormFieldsData.find(it => it.controlName === 'username').value = dialog.formGroup.getRawValue().connectionForm.username;
                dialog.appFormFieldsData.find(it => it.controlName === 'password').value = dialog.formGroup.getRawValue().connectionForm.password;
                dialog.appFormFieldsData = dialog.appFormFieldsData.filter(value => value != null);
                return {
                    result: false
                };
            } else {
                return {
                    error: 'error'
                };
            }
        }
    },
    [DataProviders.CDC_CERTIFIERS]: {
        name: 'Portail Certificateurs',
        required: false,
        type: ConnectionType.HABILITATION,
        confirmHabilitationDialog: SignDocumentDialogComponent,
        testBeforeSave: true,
        forTrainingOrganism: false,
        forCertifierOrganism: true,
        requiresAuthentication: true,
        manageRegistrationFolders: false,
        displayLoginForm: (dialog: DialogConnectionAuthComponent) => {
            return dialog.dataProviderConfigEntry['displayLoginFormStep'] === true && dialog.subscription.allowCertifierPlus && !dialog.organism.accrochageDelegationDate &&
                (!dialog.connection || ![ConnectionState.ACTIVE, ConnectionState.IN_PROGRESS].includes(dialog.connection.state));
        },
        authorizations: [
            'organism-informations_read',
            'depot_automated',
            'read_processed_receipt',
            'read_confirmation_receipt'
        ],
        documentName: 'Délégation de pouvoir',
        submitLabel: 'auth.cdcCertifiers.start',
        getAppFormFieldsData: (appFormFieldData: AppFormFieldData[], dialog: DialogConnectionAuthComponent) => {
            return appFormFieldData.concat([{
                controlName: 'firstName',
                label: 'Prénom - référent Accrochage',
                type: 'text' as const,
                required: true,
                colSpan: 3,
                value: dialog.user.firstName
            }, {
                controlName: 'lastName',
                label: 'Nom - référent Accrochage',
                type: 'text' as const,
                required: true,
                colSpan: 3,
                value: dialog.user.lastName
            }, {
                controlName: 'email',
                label: 'Email - référent Accrochage',
                type: 'email' as const,
                required: true,
                value: dialog.user.email,
                validators: [Validators.email],
                validatorsMessages: {
                    email: 'private.profile.organism.form.fields.email.error'
                }
            }, {
                controlName: 'shortName',
                label: 'Nom abrégé de votre organisme (si existe)',
                type: 'text' as const,
                required: false,
                value: null,
            }, {
                controlName: 'commercialName',
                label: 'Nom commercial de votre réseau (si existe)',
                type: 'text' as const,
                required: false,
                value: null,
            }, {
                controlName: 'logo',
                value: dialog.organism.logo,
                label: 'Logo de votre organisme',
                type: 'file' as const,
                icon: 'image',
                removable: true,
                required: true,
                class: dialog.organism.logo ? 'hidden' : null,
                colSpan: dialog.organism.logo ? 6 : 3,
                chooseLabel: 'Choisir (.svg)',
                removeLabel: 'Remplacer (Logo)',
                fileTypesAccept: ['image/svg+xml']
            }, {
                controlName: 'kbis',
                label: 'Kbis (- de 3 mois)',
                type: 'file' as const,
                icon: 'image',
                removable: true,
                required: true,
                value: null,
                colSpan: dialog.organism.logo ? 6 : 3,
                chooseLabel: 'Choisir (.pdf)',
                removeLabel: 'Remplacer (Kbis)'
            }]);
        },
        manageBeforeSaveResult: (dialog: DialogConnectionAuthComponent, data) => {
            if (data.result === 'signed') {
                return data;
            } else if (data.result === 'form') {
                if (dialog.appFormFieldsData[0].controlName === 'username') {
                    dialog.appFormFieldsData.shift();
                }
                if (dialog.appFormFieldsData[0].controlName === 'password') {
                    dialog.appFormFieldsData.shift();
                }
                dialog.dataProviderConfigEntry['displayLoginFormStep'] = true;
                dialog.dataProviderConfigEntry.submitLabel = 'auth.cdcCertifiers.sign';
                return {
                    result: false
                };
            } else if (data.result === 'sign') {
                dialog.loading = true;
                dialog.errorMessages = [];
                dialog._snackBar.openFromComponent(
                    SnackBarComponent,
                    displayTextSnackBar(dialog._translateService.instant('common.actions.signDocument.generation', {name: dialog.dataProviderConfigEntry.documentName}))
                );
                return {
                    result: {
                        disableClose: true,
                        width: '50%',
                        height: '90%',
                        panelClass: '',
                        data: {
                            documentId: 'cdcCertifierDelegationDocument',
                            params: {...dialog.formGroup.getRawValue().connectionForm},
                            documentName: dialog._translateService.instant('auth.cdcCertifiers.delegationDocumentName')
                        }
                    }
                };
            }
        },
    },
    [DataProviders.POLE_EMPLOI]: {
        name: 'Pôle Emploi',
        required: false,
        passwordLink: null,
        resetPasswordLink: null,
        authorizations: [],
        manageRegistrationFolders: true,
        forTrainingOrganism: true,
        forCertifierOrganism: false,
        requiresAuthentication: false
    },
    [DataProviders.INDIVIDUAL]: {
        name: 'Autofinancement',
        required: false,
        passwordLink: null,
        resetPasswordLink: null,
        authorizations: [],
        manageRegistrationFolders: true,
        forTrainingOrganism: true,
        forCertifierOrganism: false,
        requiresAuthentication: false
    },
    [DataProviders.COMPANY]: {
        name: 'Entreprise',
        required: false,
        passwordLink: null,
        resetPasswordLink: null,
        authorizations: [],
        manageRegistrationFolders: true,
        forTrainingOrganism: true,
        forCertifierOrganism: false,
        requiresAuthentication: false
    },
    [DataProviders.OPCO]: {
        name: 'OPCO (Manuel)',
        required: false,
        passwordLink: null,
        resetPasswordLink: null,
        authorizations: [],
        manageRegistrationFolders: true,
        forTrainingOrganism: true,
        forCertifierOrganism: false,
        requiresAuthentication: false
    },
    [DataProviders.OPCO_CFA_ATLAS]: {
        name: 'OPCO Atlas (Apprentissage)',
        ...DefaultOpcoConfig
    },
    [DataProviders.OPCO_CFA_AFDAS]: {
        name: 'OPCO Afdas (Apprentissage)',
        ...DefaultOpcoConfig
    },
    [DataProviders.OPCO_CFA_EP]: {
        name: 'OPCO EP (Apprentissage)',
        ...DefaultOpcoConfig
    },
    [DataProviders.OPCO_CFA_MOBILITES]: {
        name: 'OPCO Mobilités (Apprentissage)',
        ...DefaultOpcoConfig
    },
    [DataProviders.OPCO_CFA_AKTO]: {
        name: 'OPCO Akto (Apprentissage)',
        ...DefaultOpcoConfig
    },
    [DataProviders.OPCO_CFA_OCAPIAT]: {
        name: 'OPCO Ocapiat (Apprentissage)',
        ...DefaultOpcoConfig
    },
    [DataProviders.OPCO_CFA_UNIFORMATION]: {
        name: 'OPCO Uniformation (Apprentissage)',
        ...DefaultOpcoConfig
    },
    [DataProviders.OPCO_CFA_2I]: {
        name: 'OPCO 2i (Apprentissage)',
        ...DefaultOpcoConfig
    },
    [DataProviders.OPCO_CFA_CONSTRUCTYS]: {
        name: 'OPCO Constructys (Apprentissage)',
        ...DefaultOpcoConfig
    },
    // [DataProviders.OPCO_CFA_SANTE]: {
    //     name: 'OPCO Santé (Apprentissage)',
    //     ...DefaultOpcoConfig
    // },
    [DataProviders.OPCO_CFA_OPCOMMERCE]: {
        name: 'OPCO L\'Opcommmerce (Apprentissage)',
        ...DefaultOpcoConfig
    },
    /*[DataProviders.PASSPORT_PREVENTION]: {
        name: 'Passeport de Prévention',
        required: false,
        type: ConnectionType.HABILITATION,
        confirmHabilitationDialog: DialogHabilitationComponent,
        passwordLink: '',
        resetPasswordLink: null,
        documentation: '/assistance/guides/organismes-formation/passport-prevention',
        testBeforeSave: true,
        beta: true,
        forTrainingOrganism: true,
        forCertifierOrganism: false,
        requiresAuthentication: true,
        manageRegistrationFolders: false,
        submitLabel: '',
        submitDisabled: true,
        authorizations: [
        ],
        displayLoginForm: (dialog: DialogConnectionAuthComponent | ResellerConnectionsComponent) => {
            return false;
        },
        displayLimitedUsage: (subscription?: Subscription) => {
            return true
        }
    },*/
} as const; // Const makes it read only and gives it a very strong type
