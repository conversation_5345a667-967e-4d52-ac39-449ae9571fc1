export interface File {
    id: number;
    fileName: string;
    fileType: string;
    permalink: string;
    state?: FileStates;
    link?: string;
    typeId?: number;
    comment?: string;
    url?: string; // case we give an url for file that is not a File Link
    generationState: DocumentGenerationStates;
    signedState?: DocumentSignedStates;
}

export interface FileUpdateBody {
    state?: FileStates;
    comment?: string;
}

export enum FileStates {
    TO_REVIEW = 'toReview',
    VALID = 'valid',
    REFUSED = 'refused'
}

export enum DocumentSignedStates {
    NOT_REQUIRED = 'notRequired',
    NONE = 'none',
    PARTIALLY = 'partially',
    COMPLETED = 'completed'
}

export interface FileType<T> {
    id?: number;
    name: string;
    accept?: string;
    description?: string;
    toState?: T;
    allowMultiple?: boolean;
    generated: boolean;
    templateFile?: string | globalThis.File; // specify a global file than the "File" from the File interface
    allowRegenerate?: boolean;
    enabled?: boolean;
    googleId?: string;
    tags?: string[];
    certifications?: string[];
    // Specific
    allowVisibilityAttendee?: boolean;
    allowUploadAttendee?: boolean;
    allowVisibilityPartner?: boolean;
    allowUploadPartner?: boolean;
    allowVisibilityPublic?: boolean;
    allowSignAttendee?: boolean;
    allowSignPartner?: boolean;
}

export const CERTIFICATE_FILE_TYPE_ID = 1001 as const;
export const CERTIFICATE_FILE_TYPE_STATE = 'success' as const;
export const FILE_TYPE_LINK = 'link' as const;
export const ALL_FILE_TYPES = '.*' as const;
export const FREE_FILE_TYPE_NAME = 'Document libre';

export enum DocumentGenerationStates {
    NOT_GENERATED = 'notGenerated',
    GENERATING = 'generating',
    GENERATED = 'generated'
}

export type FileSlot<S> = { fileType: FileType<S>, file?: File, generating?: boolean };
export type FileHolder<S> = { files?: File[], state: S };
