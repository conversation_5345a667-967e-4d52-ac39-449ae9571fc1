import {Link} from './link';

export interface Links {
    self: Link;
    training?: Link;
    trainingAction?: Link;
    organism: Link;
}

export interface Evaluation {
    id: string;
    averageRating: number;
    reviewNumber: number;
    ratingQuestion1: number;
    ratingQuestion2: number;
    ratingQuestion3: number;
    ratingQuestion4: number;
    ratingQuestion5: number;
    date: Date;
    _links: Links;
}

export interface ShortEvaluation {
    averageRating: number;
    reviewCount: number;
}
