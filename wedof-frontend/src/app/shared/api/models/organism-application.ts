export interface OrganismApplication {
    id: number;
    appId: string;
    enabled: boolean;
    state: OrganismApplicationStates;
    endDate?: Date;
    metadata: {
        [key: string]: any;
    };
}

export interface MaFormationMetadata {
    name: string;
}

export class N8nMetadata {
}

export enum OrganismApplicationStates {
    TRIAL = 'trial',
    PENDING_DISABLE_TRIAL = 'pending_disable_trial',
    PENDING_ENABLE_TRIAL = 'pending_enable_trial',
    ENABLED = 'enabled',
    DISABLED = 'disabled',
    PENDING_ENABLE = 'pending_enable',
    PENDING_DISABLE = 'pending_disable'
}
