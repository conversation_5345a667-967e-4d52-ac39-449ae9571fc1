import {AttendeeExperience} from './attendee-experience';

export interface CertificationFolderSurvey {
    id: number;
    initialExperience?: AttendeeExperience;
    initialExperienceAnsweredDate?: Date;
    sixMonthExperience?: AttendeeExperience;
    sixMonthExperienceAnsweredDate?: Date;
    sixMonthExperienceStartDate?: Date;
    longTermExperience?: AttendeeExperience;
    longTermExperienceAnsweredDate?: Date;
    longTermExperienceStartDate?: Date;
    state?: CertificationFolderSurveyState;
}

export interface CertificationFolderSurveyUpdateBody {
    experience: {
        name: CertificationFolderSurveyExperience;
        attendeeExperienceId: number
    };
}

export enum CertificationFolderSurveyExperience {
    INITIAL_EXPERIENCE = 'initialExperience',
    SIX_MONTH_EXPERIENCE = 'sixMonthExperience',
    LONG_TERM_EXPERIENCE = 'longTermExperience'
}

export enum CertificationFolderSurveyState {
    CREATED = 'created',
    BEFORE_CERTIFICATION_SUCCESS = 'beforeCertificationSuccess',
    AFTER_SIX_MONTHS_CERTIFICATION_SUCCESS = 'afterSixMonthsCertificationSuccess',
    FINISHED = 'finished'
}

export interface CertificationFolderSurveyDetails {
    total: number;
    beforeCertificationSuccess: number;
    afterSixMonthsCertificationSuccess: number;
    finished: number;
    canAnswerSixMonths: number;
    canAnswerLongTerm: number;
}
