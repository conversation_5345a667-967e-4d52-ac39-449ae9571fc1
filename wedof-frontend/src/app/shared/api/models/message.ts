import {MessageTemplateType} from './message-template';
import {Link} from './link';

export interface Links {
    messageTemplate: Link & { title: string };
}

export interface Message {
    id: number;
    replyTo?: string[];
    subject?: string;
    body: string;
    entityClass: string;
    entityId: string;
    scheduledAt: Date;
    sentAt?: Date;
    type: MessageTemplateType;
    cc?: string[];
    cci?: string[];
    state: MessageState;
    to: string[];
    uuid: string;
    _links: Links;
}

export enum MessageState {
    SENT = 'sent',
    NOT_SENT = 'notSent', // à conserver legacy
    NOT_SENT_UNAUTHORIZED = 'notSentUnauthorized',
    NOT_SENT_ENFORCED_CONDITIONS = 'notSentEnforcedConditions',
    NOT_SENT_MISSING_DATA = 'notSentMissingData',
    FAILED = 'failed',
    SCHEDULED = 'scheduled',
}

export interface MessageCreateBody {
    entityClass: string;
    entityId: string;
    siret: string;
    type: MessageTemplateType;
    to: string[];
    replyTo?: string[];
    cc?: string[];
    cci?: string[];
    subject?: string;
    body: string;
    sendAs?: string;
}
