import {ApiToken} from './api-token';
import {Link, OrganismLink} from './link';
import {Organism} from './organism';
import {EntityClass} from '../../utils/enums/entity-class';

export interface User {
    name?: string;
    firstName?: string;
    lastName?: string;
    email: string;
    phoneNumber?: string;
    address?: string;
    password?: string;
    avatar?: string;
    status?: string;
    mainOrganism?: Organism; // used in the wizard
    apiTokens?: ApiToken[];
    can_impersonate?: boolean;
    is_impersonator?: boolean;
    _links?: Links;
    rgpdMsa?: Date;
    isOwner?: boolean;
    roles?: Array<string>;
    filters?: Filter[];
}

export interface Links {
    self: Link;
    mainOrganism: OrganismLink;
}

export interface UserOrganism {
    firstName: string;
    lastName: string;
    phoneNumber?: string;
    email: string;
    password: string;
    roles?: Array<string>;
    _links?: Links;
}

export interface Filter {
    name: string;
    link: string;
    entityClass: EntityClass;
    color?: string;
}
