export interface AttendeeExperience {
    id: number;
    qualification?: number;
    certificationName?: string;
    job?: string;
    startDate?: Date;
    endDate?: Date;
    companyName?: string;
    salaryYearly?: number;
    situation?: SituationCertification;
    executiveStatus?: boolean;
    contractType?: ContractType;
    createdOn: Date;
}

export enum SituationCertification {
    ACTIVE = 'active',
    SEARCHING = 'searching',
    INACTIVE = 'inactive',
    TRAINING = 'training'
}

export enum ContractType {
    CDD = 'cdd',
    CDI = 'cdi',
    INTERIM = 'interim',
    INDEPENDANT = 'independant',
    INACTIF = 'inactif'
}

export interface AttendeeExperienceBody {
    situation: SituationCertification;
    certificationFolderExternalId: string;
    qualification: number;
    certificationName?: string;
    job?: string;
    startDate?: Date;
    endDate?: Date;
    companyName?: string;
    salaryYearly?: number;
    executiveStatus?: boolean;
    contractType?: ContractType;
}
