export interface CertificationStatistics {
    foldersTotal: number;
    countByState: {
        success: number,
        toRegister: number,
        toTake: number,
        refused: number,
        registered: number,
        toControl: number,
        toRetake: number,
        failed: number,
        aborted: number
    };
    averageEvaluation: number;
    takeRate?: number;
    takeRateAfterDelay?: {
        '1Month': number,
        '3Months': number,
        '6Months': number
    };
    successRate?: number;
    abortRate?: number;
    abortExploded?: {
        beforeTrainingRate: number,
        inTrainingRate: number,
        afterTrainingRate: number
    };
    evaluation?: {
        averageRating: number;
        reviewCount: number;
        reviewRate?: number;
    };
    pricing?: {
        min: number,
        max: number,
        average: number
    };
    duration?: {
        min?: number,
        max?: number,
        average?: number
    };
    countRegistrationFolderByType?: {
        [key: string]: number;
    };
}
