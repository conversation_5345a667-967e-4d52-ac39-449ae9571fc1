export interface Link {
    href: string;
}

export interface OrganismLink extends Link {
    name: string;
    siret: string;
    hasOwner?: boolean;
}

export interface CertificationLink extends Link {
    name: string;
    certifInfo: string;
    externalId: string;
    id: number;
    enabled: boolean;
    allowPartialSkillSets?: boolean;
}

export interface RegistrationFolderLink extends Link {
    type: string;
    state: string;
    externalId: string;
}

export interface CertificationFolderLink extends Link {
    externalId: string;
}

export interface UserLink extends Link {
    email: string;
    name: string;
}

export interface WorkingContractLink extends Link {
    financer: string;
    state: string;
    idDeca?: string;
    idTrainingOrganism?: string;
}
