import {Link} from './link';

export interface Links {
    self: Link;
    trainingAction?: Link;
    registrationFolders?: Link;
}

export interface SessionInfo {
    registrationFolderCount?: number;
    activeAttendeeCount?: number;
    trainingTitle?: string;
    certificationName?: string;
    city: string;
    totalTvaTTC?: number;
}

export interface Session {
    externalId: string;
    startDate?: string;
    endDate?: string;
    sessionInfo: SessionInfo;
    _links: Links;
    id?: number;
}
