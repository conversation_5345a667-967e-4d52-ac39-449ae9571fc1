import {CertificationLink, Link, OrganismLink} from './link';

export interface Training {
    title: string;
    externalId: string;
    lastUpdate: string;
    activeTrainingActions: [
        online: { externalId: string } [],
        onlineLink: string,
        onsite: { externalId: string } [],
        onsiteLink: string
    ];
    catalog: string;
    _links?: Links;
    compliance?: TrainingComplianceTypes;
    lastComplianceUpdate?: Date;
}

export enum TrainingComplianceTypes {
    NOT_VERIFIED = 'notVerified',
    COMPLIANT = 'compliant',
    NOT_COMPLIANT = 'notCompliant'
}

export interface Links {
    self: Link;
    certification: CertificationLink;
    organism: OrganismLink;
    trainingActions: Link;
}
