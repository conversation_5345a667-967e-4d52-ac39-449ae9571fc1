import {Link} from './link';

export interface WorkingContract {
    id: number;
    externalIdFinancer: string;
    externalIdTrainingOrganism?: string;
    externalIdDeca?: string;
    financer: WorkingContractFinancers;
    state: WorkingContractStates;
    lastUpdate: Date;
    type: WorkingContractTypes;
    amount?: number;
    startDate: Date;
    endDate: Date;
    signedDate: Date;
    amendmentDate?: Date;
    breakingDate?: Date;
    _links: Links;
}

export interface Links {
    self: Link;
    employer?: Link & {
        name: string;
        siret: string;
    };
}

export enum WorkingContractFinancers {
    OPCO_CFA_ATLAS = 'opcoCfaAtlas',
    OPCO_CFA_AFDAS = 'opcoCfaAfdas',
    OPCO_CFA_MOBILITES = 'opcoCfaMobilites',
    OPCO_CFA_AKTO = 'opcoCfaAkto',
    OPCO_CFA_OCAPIAT = 'opcoCfaOcapiat',
    OPCO_CFA_UNIFORMATION = 'opcoCfaUniformation',
    OPCO_CFA_2I = 'opcoCfa2i',
    OPCO_CFA_CONSTRUCTYS = 'opcoCfaConstructys',
    OPCO_CFA_SANTE = 'opcoCfaSante',
    OPCO_CFA_OPCOMMERCE = 'opcoCfaOpcommerce'
}

export enum WorkingContractStates {
    DRAFT = 'draft', // interne Wedof
    SENT = 'sent', // TRANSMIS
    PENDING_ACCEPTATION = 'pendingAcceptation', // EN_COURS_INSTRUCTION
    ACCEPTED = 'accepted', // ENGAGE
    CANCELLED = 'cancelled', // ANNULE
    REFUSED = 'refused', // REFUSE
    BROKEN = 'broken', // RUPTURE
    COMPLETED = 'completed', // SOLDE
}

export enum WorkingContractTypes {
    // 1X contrat initial
    INITIAL_11 = '11', // Premier contrat d’apprentissage de l’apprenti
    // 2X succession de contrats
    SUCCESSION_21 = '21', // Nouveau contrat avec un apprenti qui a terminé son précédent contrat auprès d’un même employeur
    SUCCESSION_22 = '22', // Nouveau contrat avec un apprenti qui a terminé son précédent contrat auprès d’un autre employeur
    SUCCESSION_23 = '23', // Nouveau contrat avec un apprenti dont le précédent contrat auprès d’un autre employeur a été rompu
    // 3X avenant
    AMENDMENT_31 = '31',  // Modification de la situation juridique de l’employeur
    AMENDMENT_32 = '32', // Changement d’employeur dans le cadre d’un contrat saisonnier
    AMENDMENT_33 = '33', // Prolongation du contrat suite à un échec à l’examen de l’apprenti
    AMENDMENT_34 = '34', // Prolongation du contrat suite à la reconnaissance de l’apprenti comme travailleur handicapé
    AMENDMENT_35 = '35', // Modification du diplôme préparé par l’apprenti
    AMENDMENT_36 = '36', // Autres changements // changement de maître d’apprentissage, de durée de travail hebdomadaire, réduction de durée, etc.
    AMENDMENT_37 = '37', // Modification du lieu d’exécution du contrat
    AMENDMENT_38 = '38' // Modification du lieu principal de réalisation de la formation théorique
}
