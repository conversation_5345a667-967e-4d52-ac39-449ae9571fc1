import {Action, createSelector, Selector, State, StateContext} from '@ngxs/store';
import {UserService} from '../services/user.service';
import {tap} from 'rxjs/operators';
import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {Filter, User} from '../models/user';
import {EntityClass} from '../../utils/enums/entity-class';

export enum FilterUpdate {
    values = 'values',
    order = 'order'
}

// -----------------------------------------------------------------------------------------------------
// @ Actions
// -----------------------------------------------------------------------------------------------------

export class FetchUser {
    static readonly type = '[User] Fetch User';
}

export class CreateUser {
    static readonly type = '[User] Create User';

    constructor(public user: User) {
    }
}

export class UpdateUser {
    static readonly type = '[User] Update User';

    constructor(public email: string, public user: User) {
    }
}

export class AddUserFilter {
    static readonly type = '[User] Add User Filter';

    constructor(public filter: Filter) {
    }
}

export class UpdateUserFilters {
    static readonly type = '[User] Update User Filters';

    constructor(public filters: Filter[], public entityClass: EntityClass ) {
    }
}

// -----------------------------------------------------------------------------------------------------
// @ State
// -----------------------------------------------------------------------------------------------------

export interface UserStateModel {
    user?: User;
}

export interface UserFiltersStateModel {
    filters: Filter[];
}

@State<UserStateModel>({
    name: 'user',
    defaults: {user: undefined}
})

// -----------------------------------------------------------------------------------------------------
// @ Reducer & Selectors
// -----------------------------------------------------------------------------------------------------

@Injectable()
export class UserState {

    constructor(private _userService: UserService) {
    }

    @Selector([UserState])
    static user(state: UserStateModel): User {
        return state.user;
    }

    static userFilteredFilters(entityClass: EntityClass): (state: UserStateModel) => Filter[] {
        return createSelector([UserState], (state: UserStateModel): Filter[] => {
            return state.user.filters ?
                state.user.filters.filter(filter => filter.entityClass === entityClass)
                : [];
        });
    }

    @Action(FetchUser)
    fetchUser(ctx: StateContext<UserStateModel>): Observable<User> {
        return this._userService.privateGetCurrent().pipe(tap(user => {
            ctx.patchState({user});
        }));
    }

    @Action(CreateUser)
    createUser(ctx: StateContext<UserStateModel>, action: CreateUser): Observable<User> {
        return this._userService.privateCreate(action.user).pipe(tap(user => {
            ctx.patchState({user});
        }));
    }

    @Action(UpdateUser)
    updateUser(ctx: StateContext<UserStateModel>, action: UpdateUser): Observable<User> {
        return this._userService.privateUpdate(action.email, action.user).pipe(tap(user => {
            ctx.patchState({user});
        }));
    }

    @Action(AddUserFilter)
    addFilter(ctx: StateContext<UserStateModel>, action: AddUserFilter): Observable<User> {
        const user = ctx.getState().user;
        const filters = user.filters ? [...user.filters, action.filter] : [action.filter];
        return this._userService.privateUpdate(user.email, {email: user.email, filters: filters}).pipe(
            tap(updatedUser => {
                ctx.patchState({user: updatedUser});
            }));
    }

    @Action(UpdateUserFilters)
    updateFilters(ctx: StateContext<UserStateModel>, action: UpdateUserFilters): Observable<User> {
        const user = ctx.getState().user;
        const filters = user.filters.filter((filter) => {
                return filter.entityClass !== action.entityClass;
        }).concat(action.filters);

        return this._userService.privateUpdate(user.email, {email: user.email, filters: filters}).pipe(
            tap(updatedUser => {
                ctx.patchState({user: updatedUser});
            }));
    }
}
