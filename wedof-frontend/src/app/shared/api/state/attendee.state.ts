import {AttendeeService} from '../services/attendee.service';
import {Attendee} from '../models/attendee';
import {Action, Selector, State, StateContext} from '@ngxs/store';
import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {tap} from 'rxjs/operators';
// -----------------------------------------------------------------------------------------------------
// @ Actions
// -----------------------------------------------------------------------------------------------------

export class Fetch<PERSON>ttendee {
    static readonly type = '[Attendee] Fetch Attendee';
}

// -----------------------------------------------------------------------------------------------------
// @ State
// -----------------------------------------------------------------------------------------------------
export interface AttendeeStateModel {
    attendee?: Attendee;
}

@State<AttendeeStateModel>({
    name: 'attendee',
    defaults: {attendee: undefined}
})

// -----------------------------------------------------------------------------------------------------
// @ Reducer & Selectors
// -----------------------------------------------------------------------------------------------------

@Injectable()
export class AttendeeState {

    constructor(private _attendeeService: AttendeeService) {
    }

    @Selector([AttendeeState])
    static attendee(state: AttendeeStateModel): Attendee {
        return state.attendee;
    }

    @Action(FetchAttendee)
    fetchAttendee(ctx: StateContext<AttendeeStateModel>): Observable<Attendee> {
        return this._attendeeService.getCurrent().pipe(
            tap(attendee  => ctx.patchState({attendee}))
        );
    }
}
