import {Action, Selector, State, StateContext} from '@ngxs/store';
import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {tap} from 'rxjs/operators';
import {Subscription} from '../models/subscription';
import {
    SubscriptionCreateParams,
    SubscriptionService,
    SubscriptionUpdateParams
} from '../services/subscription.service';

// -----------------------------------------------------------------------------------------------------
// @ Actions
// -----------------------------------------------------------------------------------------------------

export class FetchSubscription {
    static readonly type = '[Subscription] Fetch Subscription';
}

export class CreateSubscription {
    static readonly type = '[Subscription] Create Subscription';
    constructor(public subscription: SubscriptionCreateParams) { }
}

export class UpdateSubscription {
    static readonly type = '[Subscription] Update Subscription';
    constructor(public id: number, public subscription: SubscriptionUpdateParams) { }
}


// -----------------------------------------------------------------------------------------------------
// @ State
// -----------------------------------------------------------------------------------------------------

export interface SubscriptionStateModel {
    subscription?: Subscription;
}

@State<SubscriptionStateModel>({
    name: 'subscription',
    defaults: {subscription: undefined}
})

// -----------------------------------------------------------------------------------------------------
// @ Reducer & Selectors
// -----------------------------------------------------------------------------------------------------

@Injectable()
export class SubscriptionState {

    constructor(private _subscriptionService: SubscriptionService) {
    }

    @Selector([SubscriptionState])
    static subscription(state: SubscriptionStateModel): Subscription {
        return state.subscription;
    }

    @Action(FetchSubscription)
    fetchSubscription(ctx: StateContext<SubscriptionStateModel>): Observable<Subscription> {
        return this._subscriptionService.privateGetCurrent().pipe(
            tap(subscription  => ctx.patchState({subscription}))
        );
    }

    @Action(CreateSubscription)
    createSubscription(ctx: StateContext<SubscriptionStateModel>, action: CreateSubscription): Observable<Subscription> {
        return this._subscriptionService.privateCreate(action.subscription).pipe(tap(subscription => {
            ctx.patchState({subscription});
        }));
    }

    @Action(UpdateSubscription)
    updateSubscription(ctx: StateContext<SubscriptionStateModel>, action: UpdateSubscription): Observable<Subscription> {
        return this._subscriptionService.privateUpdate(action.id, action.subscription).pipe(tap(subscription => {
            ctx.patchState({subscription});
        }));
    }
}
