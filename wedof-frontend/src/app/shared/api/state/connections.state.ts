import {Action, Selector, State, StateContext} from '@ngxs/store';
import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {tap} from 'rxjs/operators';
import {Connection} from '../models/connection';
import {ConnectionService} from '../services/connection.service';
import {patch, updateItem} from '@ngxs/store/operators';
// -----------------------------------------------------------------------------------------------------
// @ Actions
// -----------------------------------------------------------------------------------------------------

export class FetchConnections {
    static readonly type = '[Connection] Fetch Connections';
}

export class UpdatedConnection {
    static readonly type = '[Connection] Updated Connection';
    constructor(public connection: Connection) {
    }
}

// -----------------------------------------------------------------------------------------------------
// @ State
// -----------------------------------------------------------------------------------------------------
export interface ConnectionsStateModel {
    connections: Connection[];
}

@State<ConnectionsStateModel>({
    name: 'connections',
    defaults: {connections: []}
})

// -----------------------------------------------------------------------------------------------------
// @ Reducer & Selectors
// -----------------------------------------------------------------------------------------------------

@Injectable()
export class ConnectionsState {

    constructor(private _connectionService: ConnectionService) {
    }

    @Selector([ConnectionsState])
    static connections(state: ConnectionsStateModel): Connection[] {
        return state.connections;
    }

    @Action(FetchConnections)
    fetchConnections(ctx: StateContext<ConnectionsStateModel>): Observable<Connection[]> {
        return this._connectionService.listCurrent().pipe(
            tap(connections  => ctx.patchState({connections}))
        );
    }

    @Action(UpdatedConnection)
    updatedConnection(ctx: StateContext<ConnectionsStateModel>, action: UpdatedConnection): void {
        ctx.setState(
            patch({
                connections: updateItem<Connection>(connection => connection.id === action.connection.id, action.connection)
            })
        );
    }
}
