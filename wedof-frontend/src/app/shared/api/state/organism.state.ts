import {Action, Selector, State, StateContext} from '@ngxs/store';
import {tap} from 'rxjs/operators';
import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {Organism} from '../models/organism';
import {OrganismService} from '../services/organism.service';

// -----------------------------------------------------------------------------------------------------
// @ Actions
// -----------------------------------------------------------------------------------------------------

export class FetchOrganism {
    static readonly type = '[Organism] Fetch Organism';
}

export class FetchSubdomainOrganism {
    static readonly type = '[Organism] Fetch Public Subdomain Organism';
}

export class CreateOrganism {
    static readonly type = '[Organism] Create Organism';

    constructor(public siret: string, public refresh: boolean = false) {
    }
}

export class UpdateOrganism {
    static readonly type = '[Organism] Update Organism';

    constructor(public siret: string, public organism: Organism) {
    }
}

export class UpdatedOrganism {
    static readonly type = '[Organism] Updated Organism';

    constructor(public organism: Organism) {
    }
}

// -----------------------------------------------------------------------------------------------------
// @ State
// -----------------------------------------------------------------------------------------------------

export interface OrganismStateModel {
    organism?: Organism;
}

@State<OrganismStateModel>({
    name: 'organism',
    defaults: {organism: undefined}
})

// -----------------------------------------------------------------------------------------------------
// @ Reducer & Selectors
// -----------------------------------------------------------------------------------------------------

@Injectable()
export class OrganismState {

    constructor(private _organismService: OrganismService) {
    }

    @Selector([OrganismState])
    static organism(state: OrganismStateModel): Organism {
        return state.organism;
    }

    @Action(FetchOrganism)
    fetchOrganism(ctx: StateContext<OrganismStateModel>): Observable<Organism> {
        return this._organismService.privateGetCurrent().pipe(tap(organism => {
            ctx.patchState({organism});
        }));
    }

    @Action(FetchSubdomainOrganism)
    fetchPublicOrganism(ctx: StateContext<OrganismStateModel>): Observable<Organism> {
        return this._organismService.getPublicBySubDomain().pipe(tap(organism => {
            ctx.patchState({organism});
        }));
    }

    @Action(CreateOrganism)
    createOrganism(ctx: StateContext<OrganismStateModel>, action: CreateOrganism): Observable<Organism> {
        return this._organismService.privateCreate(action.siret, action.refresh).pipe(tap(organism => {
            ctx.patchState({organism});
        }));
    }

    @Action(UpdateOrganism)
    updateOrganism(ctx: StateContext<OrganismStateModel>, action: UpdateOrganism): Observable<Organism> {
        return this._organismService.privateUpdate(action.siret, action.organism).pipe(tap(organism => {
            ctx.patchState({organism});
        }));
    }

    @Action(UpdatedOrganism)
    updatedOrganism(ctx: StateContext<OrganismStateModel>, action: UpdatedOrganism): void {
        ctx.patchState({organism: action.organism});
    }
}
