import {HttpClient} from '@angular/common/http';
import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {
    CertificationPartner,
    CertificationPartnerStates,
    CertificationPartnerUpdate
} from '../models/certification-partner';
import {AbstractPaginatedService, BaseHttpParams, PaginatedResponse} from './abstract-paginated.service';
import {APIEndpoint} from './api-endpoint.enum';
import {CertificationPartnerAuditResults} from '../models/certification-partner-audit';
import {CertificationStatistics} from '../models/certification-statistics';

export interface CertificationPartnerHttpParams extends BaseHttpParams {
    siret?: string;
    certifInfo: string;
    state?: CertificationPartnerStates;
    connectionIssue?: string;
    compliance?: CertificationPartnerAuditResults;
}

@Injectable()
export class CertificationPartnerService extends AbstractPaginatedService<CertificationPartner> {
    constructor(httpClient: HttpClient) {
        super(httpClient);
    }

    get(params: CertificationPartnerHttpParams): Observable<CertificationPartner> {
        return this._httpClient.get<CertificationPartner>(`${APIEndpoint.API}/certifications/${params.certifInfo}/partners/${params.siret}`);
    }

    getByUrl(url: string): Observable<CertificationPartner> {
        return this._httpClient.get<CertificationPartner>(url);
    }

    getDetails(params: CertificationPartnerHttpParams): Observable<CertificationStatistics> {
        return this._httpClient.get<CertificationStatistics>(`${APIEndpoint.API}/certifications/${params.certifInfo}/partners/${params.siret}/details`);
    }

    list(params: CertificationPartnerHttpParams): Observable<PaginatedResponse<CertificationPartner>> {
        return this.findAllPaginatedSorted(`${APIEndpoint.API}/certifications/${params.certifInfo}/partners`, params);
    }

    listForActivity(query): Observable<PaginatedResponse<CertificationPartner>> {
        return this.findAllPaginatedSorted(`${APIEndpoint.API}/partners`, {query: query});
    }

    create(params: CertificationPartnerHttpParams): Observable<CertificationPartner> {
        return this._httpClient.post<CertificationPartner>(`${APIEndpoint.API}/certifications/partners/${params.siret}`, {certifInfo: params.certifInfo});
    }

    update(certifInfo: string, siret: string, body: CertificationPartnerUpdate): Observable<CertificationPartner> {
        return this._httpClient.put<CertificationPartner>(`${APIEndpoint.API}/certifications/${certifInfo}/partners/${siret}`, body);
    }

    reinitialize(certifInfo: string, siret: string): Observable<CertificationPartner> {
        return this._httpClient.post<CertificationPartner>(`${APIEndpoint.API}/certifications/${certifInfo}/partners/${siret}/reinitialize`, []);
    }

    delete(certifInfo: string, siret: string): Observable<void> {
        return this._httpClient.delete<void>(`${APIEndpoint.API}/certifications/${certifInfo}/partners/${siret}`);
    }

    listAllFiles(certifInfo: string, siret: string): Observable<[{typeId: number}]> {
        return this._httpClient.get<[{typeId: number}]>(`${APIEndpoint.API}/certifications/${certifInfo}/partners/${siret}/files`);
    }
}
