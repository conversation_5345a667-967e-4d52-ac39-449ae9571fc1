import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {APIEndpoint} from './api-endpoint.enum';
import {City} from '../models/attendee';

@Injectable()
export class CityService  {

    constructor(private _httpClient: HttpClient) {
    }

    listCitiesByName(city: string): Observable<City[]> {
        return this._httpClient.get<City[]>(`${APIEndpoint.APP}/public/utils/cities/name/${city}`);
    }

    getByPostalCode(postalCode: string): Observable<City> {
        return this._httpClient.get<City>(`${APIEndpoint.APP}/public/utils/cities/postalCode/${postalCode}`);
    }

    getByPostalCodes(postalCodes: string): Observable<City[]> {
        return this._httpClient.get<City[]>(`${APIEndpoint.APP}/public/utils/cities/postalCodes/${postalCodes}`);
    }

}
