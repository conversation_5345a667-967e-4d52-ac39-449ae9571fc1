import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {APIEndpoint} from './api-endpoint.enum';
import {AbstractPaginatedService, BaseHttpParams, PaginatedResponse} from './abstract-paginated.service';
import {TrainingAction} from '../models/training-action';

export interface TrainingActionHttpParams extends BaseHttpParams {
    state?: 'draft' | 'published' | 'unpublished' | 'archived';
    eligible?: boolean;
}

@Injectable({
    providedIn: 'root'
})
export class TrainingActionService extends AbstractPaginatedService<TrainingAction> {

    /**
     * Constructor
     *
     * @param {HttpClient} _httpClient
     */
    constructor(_httpClient: HttpClient
    ) {
        super(_httpClient);
    }

    list(params: TrainingActionHttpParams): Observable<PaginatedResponse<TrainingAction>> {
        return this.findAllPaginatedSorted(`${APIEndpoint.API}/trainingActions`, params);
    }

    listFromFunnel(params: TrainingActionHttpParams): Observable<PaginatedResponse<TrainingAction>> {
        return this.findAllPaginatedSorted(`${APIEndpoint.FUNNEL_API}/trainingActions`, params);
    }

    listAvailableFromFunnel(term: string): Observable<PaginatedResponse<TrainingAction>> {
        return this.listFromFunnel({query: term, state: 'published', eligible: true});
    }
}
