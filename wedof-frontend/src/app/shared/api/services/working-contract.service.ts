import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {APIEndpoint} from './api-endpoint.enum';
import {AbstractPaginatedService, BaseHttpParams, PaginatedResponse} from './abstract-paginated.service';
import {WorkingContract} from '../models/working-contract';

export interface WorkingContractHttpParams extends BaseHttpParams {
    registrationFolderExternalId?: string;
}

@Injectable({
    providedIn: 'root'
})
export class WorkingContractService extends AbstractPaginatedService<WorkingContract> {

    constructor(_httpClient: HttpClient) {
        super(_httpClient);
    }

    list(params: WorkingContractHttpParams): Observable<PaginatedResponse<WorkingContract>> {
        return this.findAllPaginatedSorted(`${APIEndpoint.API}/workingContracts`, params);
    }

    getByUrl(url: string): Observable<WorkingContract> {
        return this._httpClient.get<WorkingContract>(url);
    }
}
