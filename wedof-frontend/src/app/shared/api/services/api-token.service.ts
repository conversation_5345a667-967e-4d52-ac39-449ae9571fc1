import {HttpClient} from '@angular/common/http';
import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {ApiToken} from '../models/api-token';
import {APIEndpoint} from './api-endpoint.enum';

@Injectable()
export class ApiTokenService {
    constructor(private _httpClient: HttpClient) {
    }

    create(name: string): Observable<ApiToken> {
         return this._httpClient.post<ApiToken>(`${APIEndpoint.API}/apiTokens`, { name });
    }

    delete(token: ApiToken): Observable<void> {
        return this._httpClient.delete<void>(`${APIEndpoint.API}/apiTokens/${token.id}`);
    }

}
