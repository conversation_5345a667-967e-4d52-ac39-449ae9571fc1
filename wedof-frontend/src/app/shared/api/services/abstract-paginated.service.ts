import {HttpClient, HttpResponse} from '@angular/common/http';
import {Observable} from 'rxjs';
import {map} from 'rxjs/operators';

export interface BaseHttpParams {
    limit?: number;
    order?: 'asc' | 'desc' | '';
    page?: number;
    query?: string;
    sort?: string;
}

export interface PaginatedResponse<T> {
    payload: T[];
    total: number;
    currentPage?: number;
    itemsPerPage?: number;
}

export abstract class AbstractPaginatedService<T> {

    constructor(protected _httpClient: HttpClient) {
    }

    findAllPaginatedSorted(endpoint: string, params: BaseHttpParams): Observable<PaginatedResponse<T>> {
        return this._httpClient
            .get<T[]>(endpoint, {
                observe: 'response',
                params: params as any
            })
            .pipe(
                map((httpResponse: HttpResponse<T[]>) => {
                    return {
                        payload: httpResponse.body,
                        total: +httpResponse.headers.get('x-total-count'),
                        currentPage: +httpResponse.headers.get('x-current-page'),
                        itemsPerPage: +httpResponse.headers.get('x-item-per-page')
                    };
                })
            );
    }
}
