import {Injectable} from '@angular/core';
import {AbstractPaginatedService, BaseHttpParams, PaginatedResponse} from './abstract-paginated.service';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {APIEndpoint} from './api-endpoint.enum';
import {Proposal} from '../models/proposal';
import {KanbanColumnConfigsResponse, KanbanColumnResponse} from '../../kanban-board/kanban-board/kanban-board.component';

export interface ProposalHttpParams extends BaseHttpParams {
    state?: string;
    isIndividual?: boolean;
}

@Injectable()
export class ProposalService extends AbstractPaginatedService<Proposal> {
    constructor(httpClient: HttpClient) {
        super(httpClient);
    }

    list(params: ProposalHttpParams): Observable<PaginatedResponse<Proposal>> {
        return this.findAllPaginatedSorted(`${APIEndpoint.API}/proposals`, params);
    }

    listByColumn(columnIds: string[], params: ProposalHttpParams): Observable<KanbanColumnResponse<Proposal>> {
        return this._httpClient.get<KanbanColumnResponse<Proposal>>(
            `${APIEndpoint.APP}/proposals`,
            {params: {...params, columnIds: columnIds.join(',')} as any}
        );
    }

    revenueByColumn(columnId: string, params: ProposalHttpParams): Observable<number> {
        return this._httpClient.get<number>(
            `${APIEndpoint.APP}/proposals/revenue/${columnId}`,
            {params: params as any}
        );
    }

    listColumnConfigs(): Observable<KanbanColumnConfigsResponse> {
        return this._httpClient.get<KanbanColumnConfigsResponse>(`${APIEndpoint.APP}/proposals/kanban/columnConfigs`);
    }

    get(code: string): Observable<Proposal> {
        return this._httpClient.get<Proposal>(`${APIEndpoint.API}/proposals/${code}`);
    }

    create(proposal: Proposal): Observable<Proposal> {
        return this._httpClient.post<Proposal>(`${APIEndpoint.API}/proposals`, proposal);
    }

    delete(code: string): Observable<void> {
        return this._httpClient.delete<void>(`${APIEndpoint.API}/proposals/${code}`);
    }

    update(data: Proposal): Observable<Proposal> {
        const files = Object.keys(data).filter(key => data[key] instanceof File);
        if (files.length > 0) {
            const formData: FormData = new FormData();
            files.forEach(key => {
                formData.append(key, data[key]);
            });
            formData.append('json', JSON.stringify(data));
            return this._httpClient.post<Proposal>(`${APIEndpoint.API}/proposals/${data.code}`, formData);
        } else {
            return this._httpClient.put<Proposal>(`${APIEndpoint.API}/proposals/${data.code}`, data);
        }
    }

    state(data: Proposal, state: string): Observable<Proposal> {
        return this._httpClient.post<Proposal>(`${APIEndpoint.API}/proposals/${data.code}/${state}`, data);
    }

    // ----------------
    // METHODES AUTH NGINX
    // ----------------

    getThroughFunnel(code: string): Observable<Proposal> {
        const params = {};
        return this._httpClient.get<Proposal>(`${APIEndpoint.FUNNEL_API}/proposals/${code}`, params);
    }

    createThroughFunnel(proposal: Proposal): Observable<Proposal> {
        const params = {};
        return this._httpClient.post<Proposal>(`${APIEndpoint.FUNNEL_API}/proposals`, proposal, params);
    }

    updateThroughFunnel(proposal: Proposal): Observable<Proposal> {
        const params = {};
        return this._httpClient.put<Proposal>(`${APIEndpoint.FUNNEL_API}/proposals/${proposal.code}`, proposal, params);
    }

    stateThroughFunnel(proposal: Proposal, state: string): Observable<Proposal> {
        const params = {};
        return this._httpClient.post<Proposal>(`${APIEndpoint.FUNNEL_API}/proposals/${proposal.code}/${state}`, proposal, params);
    }
}
