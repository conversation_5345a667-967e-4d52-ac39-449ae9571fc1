import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {UserMessage} from '../models/user-message';
import {APIEndpoint} from './api-endpoint.enum';

@Injectable({
  providedIn: 'root'
})
export class UserMessageService {
    constructor(private _httpClient: HttpClient) {
    }

    listGlobalMessages(): Observable<UserMessage[]> {
        return this._httpClient.get<UserMessage[]>(`${APIEndpoint.APP}/userMessages/global`);
    }

    myMessages(): Observable<UserMessage[]> {
        return this._httpClient.get<UserMessage[]>(`${APIEndpoint.APP}/userMessages/me`);
    }
}
