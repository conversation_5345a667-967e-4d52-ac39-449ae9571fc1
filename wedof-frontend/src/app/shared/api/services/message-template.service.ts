import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {MessageTemplate, MessageTemplateUpdate} from '../models/message-template';
import {APIEndpoint} from './api-endpoint.enum';
import {AbstractPaginatedService, BaseHttpParams, PaginatedResponse} from './abstract-paginated.service';

export interface MessageTemplateHttpParams extends BaseHttpParams {
    type?: string;
    state?: string;
    entityClass?: string;
    certifInfo?: string;
}

@Injectable({
    providedIn: 'root'
})
export class MessageTemplateService extends AbstractPaginatedService<MessageTemplate> {

    constructor(_httpClient: HttpClient) {
        super(_httpClient);
    }

    get(id: number): Observable<MessageTemplate> {
        return this._httpClient.get<MessageTemplate>(`${APIEndpoint.API}/messageTemplates/${id}`);
    }

    list(params: MessageTemplateHttpParams): Observable<PaginatedResponse<MessageTemplate>> {
        return this.findAllPaginatedSorted(`${APIEndpoint.API}/messageTemplates`, params);
    }

    create(body: MessageTemplate): Observable<MessageTemplate> {
        return this._httpClient.post<MessageTemplate>(`${APIEndpoint.API}/messageTemplates`, body);
    }

    duplicate(id: number): Observable<MessageTemplate> {
        return this._httpClient.post<MessageTemplate>(`${APIEndpoint.API}/messageTemplates/${id}/duplicate`, {});
    }

    update(messageTemplateId: number, body: MessageTemplateUpdate): Observable<MessageTemplate> {
        return this._httpClient.put<MessageTemplate>(`${APIEndpoint.API}/messageTemplates/${messageTemplateId}`, body);
    }

    delete(id: number): Observable<void> {
        return this._httpClient.delete<void>(`${APIEndpoint.API}/messageTemplates/${id}`);
    }

    state(body: MessageTemplate, state: string): Observable<MessageTemplate> {
        return this._httpClient.post<MessageTemplate>(`${APIEndpoint.API}/messageTemplates/${body.id}/${state}`, body);
    }

    testTemplate(messageTemplate: MessageTemplate): Observable<void> {
        return this._httpClient.get<void>(`${APIEndpoint.APP}/messageTemplates/${messageTemplate.id}/testTemplate`, {});
    }

    createFromTemplate(templateName: string): Observable<MessageTemplate> {
        return this._httpClient.post<MessageTemplate>(`${APIEndpoint.APP}/messageTemplates/${templateName}`, {});
    }

    getByUrl(url: string): Observable<MessageTemplate> {
        return this._httpClient.get<MessageTemplate>(url);
    }
}
