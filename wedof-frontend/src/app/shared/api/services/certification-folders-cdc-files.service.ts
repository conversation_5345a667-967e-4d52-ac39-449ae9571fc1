import {Injectable} from '@angular/core';
import {AbstractPaginatedService} from './abstract-paginated.service';
import {HttpClient} from '@angular/common/http';
import {CertificationFoldersCdcFiles} from '../models/certification-folder';
import {Observable} from 'rxjs';
import {APIEndpoint} from './api-endpoint.enum';

@Injectable({
    providedIn: 'root'
})
export class CertificationFoldersCdcFilesService extends AbstractPaginatedService<CertificationFoldersCdcFiles> {

    constructor(_httpClient: HttpClient) {
        super(_httpClient);
    }

    getCurrentByCertificationFolder(certificationFolderExternalId: string): Observable<CertificationFoldersCdcFiles> {
        return this._httpClient.get<CertificationFoldersCdcFiles>(`${APIEndpoint.API}/certificationFoldersCdcFiles/current/${certificationFolderExternalId}`);
    }
}
