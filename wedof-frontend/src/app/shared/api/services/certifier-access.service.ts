import {HttpClient} from '@angular/common/http';
import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {CertifierAccess} from '../models/certifier-access';
import {APIEndpoint} from './api-endpoint.enum';
import {Organism} from '../models/organism';
import {AbstractPaginatedService, BaseHttpParams, PaginatedResponse} from './abstract-paginated.service';


export interface CertifierAccessHttpParams extends BaseHttpParams {
    accessType?: 'partner' | 'certifier';
    certifInfo?: string;
    siret?: string;
}


@Injectable()
export class CertifierAccessService extends AbstractPaginatedService<CertifierAccess> {
    constructor(httpClient: HttpClient) {
        super(httpClient);
    }

    find(params: CertifierAccessHttpParams): Observable<PaginatedResponse<CertifierAccess>> {
        return this.findAllPaginatedSorted(`${APIEndpoint.APP}/certifierAccess`, params);
    }

    get(id: number): Observable<CertifierAccess> {
        return this._httpClient.get<CertifierAccess>(`${APIEndpoint.APP}/certifierAccess/${id}`);
    }

    getByToken(token: string): Observable<CertifierAccess> {
        return this._httpClient.get<CertifierAccess>(`${APIEndpoint.APP}/public/certifierAccess/${token}`);
    }

    getByUrl(url: string): Observable<CertifierAccess> {
        return this._httpClient.get<CertifierAccess>(url);
    }

    create(
        partner: Organism,
        recipient?: { name: string; email: string }
    ): Observable<CertifierAccess> {
        return this._httpClient
            .post<CertifierAccess>(`${APIEndpoint.APP}/certifierAccess`, {
                partner: partner.siret,
                recipientEmail: recipient?.email || undefined,
                recipientName: recipient?.name || undefined
            });
    }

    accept(certifierAccess: CertifierAccess): Observable<CertifierAccess> {
        return this._httpClient
            .post<CertifierAccess>(`${APIEndpoint.APP}/certifierAccess/${certifierAccess.id}/activate`, {});
    }

    refuse(certifierAccess: CertifierAccess): Observable<CertifierAccess> {
        return this._httpClient
            .post<CertifierAccess>(`${APIEndpoint.APP}/public/certifierAccess/${certifierAccess.id}/refuse`, {});
    }

    terminate(certifierAccess: CertifierAccess): Observable<CertifierAccess> {
        return this._httpClient
            .post<CertifierAccess>(`${APIEndpoint.APP}/certifierAccess/${certifierAccess.id}/terminate`, {});
    }

    resendInvitationEmail(siret: string): Observable<string> {
        return this._httpClient.post<string>(`${APIEndpoint.APP}/certifierAccess/resend/${siret}`, null);
    }
}
