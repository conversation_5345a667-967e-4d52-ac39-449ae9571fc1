import {HttpClient, HttpParams} from '@angular/common/http';
import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {RegistrationFolderReason} from '../models/registration-folder-reason';
import {APIEndpoint} from './api-endpoint.enum';

@Injectable()
export class RegistrationFolderReasonService {
    constructor(
        private _httpClient: HttpClient
    ) {
    }

    find(type: 'canceled' | 'refused' | 'terminated'): Observable<RegistrationFolderReason[]> {
        return this._httpClient.get<RegistrationFolderReason[]>(`${APIEndpoint.API}/registrationFoldersReasons`, {
            params: new HttpParams().set('type', type)
        });
    }

}
