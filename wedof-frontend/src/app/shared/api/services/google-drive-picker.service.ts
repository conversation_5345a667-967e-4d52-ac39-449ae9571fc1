import {Injectable} from '@angular/core';
import {environment} from '../../../../environments/environment';

declare const gapi: any;
declare const google: any;

@Injectable({
    providedIn: 'root'
})
export class GoogleDrivePickerService {

    private clientId = environment.gdrive.clientId;
    private apiKey = environment.gdrive.apiKey;
    private appId = environment.gdrive.appId;
    private scope = 'https://www.googleapis.com/auth/drive.file';

    private oauthAccessToken = null;
    private pickerApiLoaded = false;
    private pickerCallback = null;

    public open(callback): void {
        this.pickerCallback = callback;
        if (!this.pickerApiLoaded || !this.oauthAccessToken) {
            gapi.load('auth', {'callback': this.onAuthApiLoad.bind(this)});
            gapi.load('picker', {'callback': this.onPickerApiLoad.bind(this)});
        } else {
            this.createPicker();
        }
    }

    private onAuthApiLoad(): void {
        gapi.auth.authorize({
            'client_id': this.clientId,
            'scope': this.scope,
            'immediate': false,
        }, this.handleAuthResult.bind(this));
    }

    private onPickerApiLoad(): void {
        this.pickerApiLoaded = true;
        this.createPicker();
    }

    private handleAuthResult(authResult): void {
        if (authResult && !authResult.error) {
            this.oauthAccessToken = authResult.access_token;
            this.createPicker();
        }
    }

    private createPicker(): void {
        if (this.pickerApiLoaded && this.oauthAccessToken) {
            const view = new google.picker.View(google.picker.ViewId.DOCS);
            const picker = new google.picker.PickerBuilder()
                .enableFeature(google.picker.Feature.NAV_HIDDEN)
                .enableFeature(google.picker.Feature.MULTISELECT_ENABLED)
                .setAppId(this.appId)
                .setOAuthToken(this.oauthAccessToken)
                .addView(view)
                .addView(new google.picker.DocsUploadView())
                .setDeveloperKey(this.apiKey)
                .setCallback(this.pickerCallback)
                .build();
            picker.setVisible(true);
        }
    }
}
