import {Injectable} from '@angular/core';
import {AbstractPaginatedService} from './abstract-paginated.service';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {APIEndpoint} from './api-endpoint.enum';
import {map} from 'rxjs/operators';
import {Tag} from '../models/tag';


@Injectable()
export class TagService extends AbstractPaginatedService<string[]> {
    constructor(httpClient: HttpClient) {
        super(httpClient);
    }

    listTags(query: string): Observable<string[]> {
        const params = {params: {'query': query}};
        return this._httpClient.get<Tag[]>(`${APIEndpoint.API}/tags`, params).pipe(
            map(tags => tags.map(tag => tag.name))
        );
    }
}
