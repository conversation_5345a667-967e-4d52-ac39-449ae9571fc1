import {AbstractPaginatedService, BaseHttpParams, PaginatedResponse} from './abstract-paginated.service';
import {Message, MessageCreateBody} from '../models/message';
import {Observable} from 'rxjs';
import {APIEndpoint} from './api-endpoint.enum';
import {HttpClient} from '@angular/common/http';
import {Injectable} from '@angular/core';

@Injectable({
    providedIn: 'root'
})
export class MessageService extends AbstractPaginatedService<Message> {

    constructor(private httpClient: HttpClient) {
        super(httpClient);
    }

    listByEntity(entityClass: string, entityId: any, params: BaseHttpParams): Observable<PaginatedResponse<Message>> {
        return this.findAllPaginatedSorted(`${APIEndpoint.API}/messages/${entityClass}/${entityId}`, params);
    }

    delete(id: number): Observable<void> {
        return this._httpClient.delete<void>(`${APIEndpoint.API}/messages/${id}`);
    }

    forceResend(id: number): Observable<Message> {
        return this._httpClient.post<Message>(`${APIEndpoint.API}/messages/${id}/resend`, null);
    }

    create(body: MessageCreateBody): Observable<Message> {
        return this._httpClient.post<Message>(`${APIEndpoint.API}/messages`, body);
    }
}
