import {Injectable} from '@angular/core';
import {HttpClient, HttpParams} from '@angular/common/http';
import {Observable} from 'rxjs';
import {APIEndpoint} from './api-endpoint.enum';
import {Moment} from 'moment/moment';
import {Certification} from '../models/certification';
import {Organism} from '../models/organism';
import {ShortEvaluation} from '../models/evaluation';
import {DataVisualisationWithVariation} from '../../chart/indicator-data';
import {RegistrationFolderBillingStates, RegistrationFolderStates} from '../models/registration-folder';
import {Training} from '../models/training';

@Injectable({
    providedIn: 'root'
})
export class StatisticService {

    constructor(private _httpClient: HttpClient) {
    }

    /**
     *
     * @param from
     * @param to
     * @param state
     * @param training
     * @param billingState
     * @param fct
     * @param field
     */
    dataRegistrationFoldersGroupByDates(
        from: Moment,
        to: Moment,
        state?: Array<RegistrationFolderStates>,
        training?: Training,
        billingState?: Array<RegistrationFolderBillingStates>,
        fct: string = 'count',
        field: string = 'id'): Observable<DataVisualisationWithVariation> {
        let params = new HttpParams();
        if (state) {
            params = params.set('state', state.join(','));
        }
        if (billingState) {
            params = params.set('billingState', billingState.join(','));
        }
        params = params.set('from', from.format('YYYY-MM-DD HH:mm:ss'));
        params = params.set('to', to.format('YYYY-MM-DD HH:mm:ss'));
        params = params.set('function', fct);
        params = params.set('field', field);
        return this._httpClient.get<DataVisualisationWithVariation>(`${APIEndpoint.API}/statistics${training?.externalId ? '/training/' + training.externalId : ''}/registrationFolders`, {params});
    }

    /**
     *
     * @param organism
     * @param certification
     */
    getShortEvaluationForOrganismAndCertification(organism: Organism, certification: Certification): Observable<ShortEvaluation> {
        let params = new HttpParams();
        params = params.set('siret', organism.siret);
        return this._httpClient.get<ShortEvaluation>(`${APIEndpoint.API}/statistics/certification/${certification.certifInfo}/evaluations`, {params});
    }
}
