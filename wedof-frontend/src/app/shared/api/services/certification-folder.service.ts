import {Injectable} from '@angular/core';
import {AbstractPaginatedService, BaseHttpParams, PaginatedResponse} from './abstract-paginated.service';
import {HttpClient} from '@angular/common/http';
import {
    CertificationFolder,
    CertificationFolderCreate,
    CertificationFolderExamAborted,
    CertificationFolderExamFailed,
    CertificationFolderExamRefused,
    CertificationFolderExamRegistered,
    CertificationFolderExamRetake,
    CertificationFolderExamSuccess,
    CertificationFolderExamToControl,
    CertificationFolderExamToTake,
    CertificationFolderStates,
    CertificationFolderUpdateBody,
    PassportDataDetails
} from '../models/certification-folder';
import {Observable} from 'rxjs';
import {APIEndpoint} from './api-endpoint.enum';
import {CertificationFolderActionCondition} from '../../certification-folder/certification-folder-menu/certification-folder-menu.component';
import {
    KanbanColumnConfigsResponse,
    KanbanColumnResponse
} from '../../kanban-board/kanban-board/kanban-board.component';

export interface CertificationFolderHttpParams extends BaseHttpParams {
    siret?: string;
    state?: string;
    certifInfo?: string;
}

@Injectable({
    providedIn: 'root'
})
export class CertificationFolderService extends AbstractPaginatedService<CertificationFolder> {

    constructor(_httpClient: HttpClient) {
        super(_httpClient);
    }

    get(externalId: string): Observable<CertificationFolder> {
        return this._httpClient.get<CertificationFolder>(`${APIEndpoint.API}/certificationFolders/${externalId}`);
    }

    getActionsFolder(externalId: string): Observable<Record<CertificationFolderStates, CertificationFolderActionCondition[]>> {
        return this._httpClient.get<any>(`${APIEndpoint.APP}/certificationFolders/${externalId}/getActions`);
    }

    update(certificationFolder: CertificationFolderUpdateBody): Observable<CertificationFolder> {
        if (typeof certificationFolder.partner === 'object' && certificationFolder.partner != null) {
            certificationFolder.partner = certificationFolder.partner.siret;
        }
        return this._httpClient.put<CertificationFolder>(`${APIEndpoint.API}/certificationFolders/${certificationFolder.externalId}`, certificationFolder);
    }

    getByUrl(url: string): Observable<CertificationFolder> {
        return this._httpClient.get<CertificationFolder>(url);
    }

    getForAttendee(externalId: string): Observable<CertificationFolder> {
        return this._httpClient.get<CertificationFolder>(`${APIEndpoint.APP}/attendees/certificationFolders/${externalId}`);
    }

    getInternalAttendeeLink(externalId: string): Observable<string> {
        return this._httpClient.get<string>(`${APIEndpoint.APP}/attendees/certificationFolders/${externalId}/attendeeLink`);
    }

    getMagicLink(externalId: string): Observable<{ url: string }> {
        return this._httpClient.get<{ url: string }>(`${APIEndpoint.APP}/certificationFolders/${externalId}/magicLink`);
    }

    list(params: CertificationFolderHttpParams): Observable<PaginatedResponse<CertificationFolder>> {
        return this.findAllPaginatedSorted(`${APIEndpoint.API}/certificationFolders`, params);
    }

    listByColumn(columnIds: string[], params: CertificationFolderHttpParams): Observable<KanbanColumnResponse<CertificationFolder>> {
        return this._httpClient.get<KanbanColumnResponse<CertificationFolder>>(
            `${APIEndpoint.APP}/certificationFolders`,
            {params: {...params, columnIds: columnIds.join(',')} as any}
        );
    }

    revenueByColumn(columnId: string, params: CertificationFolderHttpParams): Observable<number> {
        return this._httpClient.get<number>(
            `${APIEndpoint.APP}/certificationFolders/revenue/${columnId}`,
            {params: params as any}
        );
    }

    listColumnConfigs(): Observable<KanbanColumnConfigsResponse> {
        return this._httpClient.get<KanbanColumnConfigsResponse>(`${APIEndpoint.APP}/certificationFolders/kanban/columnConfigs`);
    }

    create(body: CertificationFolderCreate): Observable<CertificationFolder> {
        return this._httpClient.post<CertificationFolder>(`${APIEndpoint.API}/certificationFolders`, body);
    }

    /**
     * Only for toRegister state
     */
    toRegister(externalId: string): Observable<CertificationFolder> {
        return this._httpClient.post<CertificationFolder>(`${APIEndpoint.API}/certificationFolders/${externalId}/toRegister`, null);
    }

    /**
     * Only for toRegister state
     */
    registered(externalId: string, body: CertificationFolderExamRegistered): Observable<CertificationFolder> {
        return this._httpClient.post<CertificationFolder>(`${APIEndpoint.API}/certificationFolders/${externalId}/register`, body);
    }

    /**
     * Only for toRegister state
     */
    refuse(externalId: string, body: CertificationFolderExamRefused): Observable<CertificationFolder> {
        return this._httpClient.post<CertificationFolder>(`${APIEndpoint.API}/certificationFolders/${externalId}/refuse`, body);
    }

    /**
     * Only for registered state
     */
    toTake(externalId: string, body: CertificationFolderExamToTake): Observable<CertificationFolder> {
        return this._httpClient.post<CertificationFolder>(`${APIEndpoint.API}/certificationFolders/${externalId}/take`, body);
    }

    /**
     * Only for toTake or toRetake state
     */
    toControl(externalId: string, body: CertificationFolderExamToControl): Observable<CertificationFolder> {
        return this._httpClient.post<CertificationFolder>(`${APIEndpoint.API}/certificationFolders/${externalId}/control`, body);
    }

    /**
     * Only for toControl state
     */
    toRetake(externalId: string, body: CertificationFolderExamRetake): Observable<CertificationFolder> {
        return this._httpClient.post<CertificationFolder>(`${APIEndpoint.API}/certificationFolders/${externalId}/retake`, body);
    }

    /**
     * Only for toControl state
     */
    fail(externalId: string, body: CertificationFolderExamFailed): Observable<CertificationFolder> {
        return this._httpClient.post<CertificationFolder>(`${APIEndpoint.API}/certificationFolders/${externalId}/fail`, body);
    }

    /**
     * Only for toControl state
     */
    success(externalId: string, body: CertificationFolderExamSuccess): Observable<CertificationFolder> {
        const files = Object.keys(body).filter(key => body[key] instanceof File);
        if (files.length > 0) {
            const formData: FormData = new FormData();
            files.forEach(key => {
                formData.append(key, body[key]);
            });
            formData.append('json', JSON.stringify(body));
            return this._httpClient.post<CertificationFolder>(`${APIEndpoint.API}/certificationFolders/${externalId}/success`, formData);
        } else {
            return this._httpClient.post<CertificationFolder>(`${APIEndpoint.API}/certificationFolders/${externalId}/success`, body);
        }
    }

    /**
     * Only for toRegister, registered, toTake, toRetake state
     */
    abort(externalId: string, body: CertificationFolderExamAborted): Observable<CertificationFolder> {
        return this._httpClient.post<CertificationFolder>(`${APIEndpoint.API}/certificationFolders/${externalId}/abort`, body);
    }

    passportDataDetails(externalId: string): Observable<PassportDataDetails> {
        return this._httpClient.get<PassportDataDetails>(`${APIEndpoint.APP}/public/certificationFolders/${externalId}/passport/details`);
    }

    delete(externalId: string): Observable<void> {
        return this._httpClient.delete<void>(`${APIEndpoint.API}/certificationFolders/${externalId}`);
    }

    listAllFiles(externalId: string): Observable<[{ typeId: number }]> {
        return this._httpClient.get<[{
            typeId: number
        }]>(`${APIEndpoint.API}/certificationFolders/${externalId}/files`);
    }

    getBadgeAssertionData(externalId: string): Observable<string> {
        return this._httpClient.get<string>(`${APIEndpoint.APP}/certificationFolders/${externalId}/badgeAssertion`);
    }
}
