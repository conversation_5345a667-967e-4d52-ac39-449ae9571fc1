import {HttpClient} from '@angular/common/http';
import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {APIEndpoint} from './api-endpoint.enum';
import {FlowRunResponse} from '../models/flow-run';

@Injectable({
    providedIn: 'root'
})
export class FlowRunService {

    constructor(private httpClient: HttpClient) {
    }

    getFlowRunsByEntity(entityClass: string, entityId: string): Observable<FlowRunResponse> {
        return this.httpClient.get<FlowRunResponse>(`${APIEndpoint.API}/flow-runs/${entityClass}/${entityId}`);
    }
}
