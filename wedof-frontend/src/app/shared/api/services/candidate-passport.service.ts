import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {HttpClient} from '@angular/common/http';
import {APIEndpoint} from './api-endpoint.enum';
import {Attendee, CandidatePassportBody} from '../models/attendee';

@Injectable({
    providedIn: 'root'
})
export class CandidatePassportService {

    constructor(private _httpClient: HttpClient) {
    }

    checkMatching(certificationFolderExternalId: string, body: CandidatePassportBody): Observable<boolean> {
        return this._httpClient.put<boolean>(`${APIEndpoint.APP}/public/candidate/passport/${certificationFolderExternalId}/checkMatching`, body);
    }

    update(entityClass: string, externalId: string, body: CandidatePassportBody, identificationDocument: File = null): Observable<Attendee> {
        const formData: FormData = new FormData();
        if (identificationDocument) {
            formData.append('identificationDocument', identificationDocument);
        }
        if (body) {
            formData.append('json', JSON.stringify(body));
        }
        return this._httpClient.post<Attendee>(`${APIEndpoint.APP}/public/attendees/${entityClass}/${externalId}/updateIdentificationData`, formData);
    }
}
