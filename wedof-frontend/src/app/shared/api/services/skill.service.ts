import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {APIEndpoint} from './api-endpoint.enum';
import {AbstractPaginatedService, BaseHttpParams, PaginatedResponse} from './abstract-paginated.service';
import {Skill, SkillBody} from '../models/skill';

export interface SkillHttpParams extends BaseHttpParams {
    certifInfo?: string;
}

@Injectable({
    providedIn: 'root'
})
export class SkillService extends AbstractPaginatedService<Skill> {

    constructor(_httpClient: HttpClient) {
        super(_httpClient);
    }

    list(params: SkillHttpParams): Observable<PaginatedResponse<Skill>> {
        return this.findAllPaginatedSorted(`${APIEndpoint.API}/skills`, params);
    }

    listByUrl(url: string): Observable<Skill[]> {
        return this._httpClient.get<Skill[]>(url);
    }

    create(body: SkillBody): Observable<Skill> {
        return this._httpClient.post<Skill>(`${APIEndpoint.API}/skills`, body);
    }

    update(id: number, body: SkillBody): Observable<Skill> {
        return this._httpClient.put<Skill>(`${APIEndpoint.API}/skills/${id}`, body);
    }

    delete(id: number): Observable<void> {
        return this._httpClient.delete<void>(`${APIEndpoint.API}/skills/${id}`);
    }

}
