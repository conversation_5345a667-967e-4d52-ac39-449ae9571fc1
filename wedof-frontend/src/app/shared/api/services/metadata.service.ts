import {Injectable} from '@angular/core';
import {AbstractPaginatedService} from './abstract-paginated.service';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {APIEndpoint} from './api-endpoint.enum';
import {Metadata} from '../models/metadata';
import {EntityClass} from '../../utils/enums/entity-class';

@Injectable()
export class MetadataService extends AbstractPaginatedService<string[]> {
    constructor(httpClient: HttpClient) {
        super(httpClient);
    }

    find(query: string = '', entity: EntityClass): Observable<Metadata[]> {
        const params = {params: {'query': query, 'entity': entity}};
        return this._httpClient.get<Metadata[]>(`${APIEndpoint.API}/metadata/keys`, params);
    }
}
