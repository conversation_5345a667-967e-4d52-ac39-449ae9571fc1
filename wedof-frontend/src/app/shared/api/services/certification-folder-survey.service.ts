import {HttpClient} from '@angular/common/http';
import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {AbstractPaginatedService} from './abstract-paginated.service';
import {APIEndpoint} from './api-endpoint.enum';
import {AttendeeExperience} from '../models/attendee-experience';
import {
    CertificationFolderSurvey,
    CertificationFolderSurveyDetails,
    CertificationFolderSurveyUpdateBody
} from '../models/certification-folder-survey';

@Injectable({
    providedIn: 'root'
})
export class CertificationFolderSurveyService extends AbstractPaginatedService<CertificationFolderSurvey> {
    constructor(_httpClient: HttpClient) {
        super(_httpClient);
    }

    getByUrl(url: string): Observable<CertificationFolderSurvey> {
        return this._httpClient.get<CertificationFolderSurvey>(url);
    }

    update(externalId: string, body: CertificationFolderSurveyUpdateBody): Observable<CertificationFolderSurvey> {
        return this._httpClient.put<AttendeeExperience>(`${APIEndpoint.API}/surveys/${externalId}`, body);
    }

    details(certifInfo: string): Observable<CertificationFolderSurveyDetails> {
        return this._httpClient.get<CertificationFolderSurveyDetails>(`${APIEndpoint.API}/surveys/details/${certifInfo}`);
    }
}
