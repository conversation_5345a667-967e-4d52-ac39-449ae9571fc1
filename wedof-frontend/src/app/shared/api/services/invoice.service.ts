import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {APIEndpoint} from './api-endpoint.enum';
import {AbstractPaginatedService, BaseHttpParams, PaginatedResponse} from './abstract-paginated.service';
import {Invoice, InvoiceBody, InvoiceStates} from '../models/invoice';

export interface InvoiceHttpParams extends BaseHttpParams {
    state?: string;
}

@Injectable()
export class InvoiceService extends AbstractPaginatedService<Invoice> {

    constructor(_httpClient: HttpClient) {
        super(_httpClient);
    }

    listByEntity(entityClass: string, entityId: number | string, params: InvoiceHttpParams): Observable<PaginatedResponse<Invoice>> {
        return this.findAllPaginatedSorted(`${APIEndpoint.API}/invoices/${entityClass}/${entityId}`, params);
    }

    delete(id: number): Observable<void> {
        return this._httpClient.delete<void>(`${APIEndpoint.API}/invoices/${id}`);
    }

    state(invoice: Invoice, state: InvoiceStates): Observable<Invoice> {
        return this._httpClient.post<Invoice>(`${APIEndpoint.API}/invoices/${invoice.id}/${state}`, {});
    }

    create(body: InvoiceBody, entityClass: string, entityId: number | string): Observable<Invoice> {
        const files = Object.keys(body).filter(key => body[key] instanceof File);
        if (files.length > 0) {
            const formData: FormData = new FormData();
            files.forEach(key => {
                formData.append(key, body[key]);
            });
            formData.append('json', JSON.stringify(body));
            return this._httpClient.post<Invoice>(`${APIEndpoint.API}/invoices/${entityClass}/${entityId}`, formData);
        } else {
            return this._httpClient.post<Invoice>(`${APIEndpoint.API}/invoices/${entityClass}/${entityId}`, body);
        }
    }

    update(body: InvoiceBody, id: number): Observable<Invoice> {
        const files = Object.keys(body).filter(key => body[key] instanceof File);
        if (files.length > 0) {
            const formData: FormData = new FormData();
            files.forEach(key => {
                formData.append(key, body[key]);
            });
            formData.append('json', JSON.stringify(body));
            return this._httpClient.post<Invoice>(`${APIEndpoint.API}/invoices/${id}`, formData);
        } else {
            return this._httpClient.post<Invoice>(`${APIEndpoint.API}/invoices/${id}`, body);
        }
    }

}
