import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {APIEndpoint} from './api-endpoint.enum';
import {AbstractPaginatedService, BaseHttpParams, PaginatedResponse} from './abstract-paginated.service';
import {
    CertificationPartnerAuditTemplate,
    CertificationPartnerAuditTemplateCreateBody
} from '../models/certification-partner-audit';

export interface CertificationPartnerAuditTemplateHttpParams extends BaseHttpParams {
    certifInfo?: string;
}

@Injectable({
    providedIn: 'root'
})
export class CertificationPartnerAuditTemplateService extends AbstractPaginatedService<CertificationPartnerAuditTemplate> {

    constructor(_httpClient: HttpClient) {
        super(_httpClient);
    }

    getByUrl(url: string): Observable<CertificationPartnerAuditTemplate> {
        return this._httpClient.get<CertificationPartnerAuditTemplate>(url);
    }

    list(params?: CertificationPartnerAuditTemplateHttpParams): Observable<PaginatedResponse<CertificationPartnerAuditTemplate>> {
        return this.findAllPaginatedSorted(`${APIEndpoint.API}/certificationPartnerAuditTemplates`, params);
    }

    create(certificationPartnerAuditTemplateBody: CertificationPartnerAuditTemplateCreateBody): Observable<CertificationPartnerAuditTemplate> {
        return this._httpClient.post<CertificationPartnerAuditTemplate>(`${APIEndpoint.API}/certificationPartnerAuditTemplates`, certificationPartnerAuditTemplateBody);
    }

    update(certificationPartnerAuditTemplate: CertificationPartnerAuditTemplate): Observable<CertificationPartnerAuditTemplate> {
        return this._httpClient.put<CertificationPartnerAuditTemplate>(
            `${APIEndpoint.API}/certificationPartnerAuditTemplates/${certificationPartnerAuditTemplate.id}`,
            certificationPartnerAuditTemplate
        );
    }

    delete(certificationPartnerAuditTemplate: CertificationPartnerAuditTemplate): Observable<void> {
        return this._httpClient.delete<void>(`${APIEndpoint.API}/certificationPartnerAuditTemplates/${certificationPartnerAuditTemplate.id}`);
    }

    listCriterias(certifInfo: string): Observable<any> {
        return this._httpClient.get<any>(`${APIEndpoint.API}/certificationPartnerAuditTemplates/criterias/${certifInfo}`);
    }

    duplicate(auditTemplateIdToDuplicate: number, body: { certifInfo: string }): Observable<void> {
        return this._httpClient.post<void>(`${APIEndpoint.API}/certificationPartnerAuditTemplates/duplicateTemplate/${auditTemplateIdToDuplicate}`, body);
    }

}
