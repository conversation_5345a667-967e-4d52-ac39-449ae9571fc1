import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {AuthenticationResult, Connection, Credentials, DataProviders} from '../models/connection';
import {APIEndpoint} from './api-endpoint.enum';

@Injectable({
    providedIn: 'root'
})
export class ConnectionService {

    constructor(private _httpClient: HttpClient) {
    }

    listCurrent(): Observable<Connection[]> {
        return this._httpClient.get<Connection[]>(`${APIEndpoint.APP}/connections/me`);
    }

    authenticate(credentials: Credentials, type: DataProviders): Observable<AuthenticationResult> {
        return this._httpClient.post<any>(`${APIEndpoint.APP}/connections/auth/${type}`, credentials);
    }

    habilitate(params: any, type: DataProviders): Observable<Connection> {
        const files = Object.keys(params).filter(key => params[key] instanceof File);
        const formData: FormData = new FormData();
        if (files.length > 0) {
            files.forEach(key => {
                formData.append(key, params[key]);
                delete params[key];
            });
            formData.append('json', JSON.stringify(params));
        }
        return this._httpClient.post<any>(`${APIEndpoint.APP}/connections/habilitate/${type}`, files.length ? formData : params);
    }

    check(credentials: Credentials, type: DataProviders): Observable<any> {
        return this._httpClient.post<any>(`${APIEndpoint.APP}/connections/check/${type}`, credentials);
    }
}
