import {HttpClient} from '@angular/common/http';
import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {Certification, CertificationCreateBody, CertificationTypes} from '../models/certification';
import {AbstractPaginatedService, BaseHttpParams, PaginatedResponse} from './abstract-paginated.service';
import {APIEndpoint} from './api-endpoint.enum';
import {CertificationStatistics} from '../models/certification-statistics';

export interface CertificationHttpParams extends BaseHttpParams {
    organismType?: 'certifier' | 'partner' | 'all';
    siret?: string;
    enabled?: boolean;
    limit?: number;
    page?: number;
    query?: string;
    partnershipState?: string;
    'certificationTypes[]'?: CertificationTypes[];
}

@Injectable()
export class CertificationService extends AbstractPaginatedService<Certification> {
    constructor(httpClient: HttpClient) {
        super(httpClient);
    }

    create(body: CertificationCreateBody): Observable<Certification> {
        return this._httpClient.post<Certification>(`${APIEndpoint.API}/certifications`, body);
    }

    list(params: CertificationHttpParams): Observable<PaginatedResponse<Certification>> {
        return this.findAllPaginatedSorted(`${APIEndpoint.API}/certifications`, params);
    }

    listLite(params: CertificationHttpParams): Observable<PaginatedResponse<Certification>> {
        return this.findAllPaginatedSorted(`${APIEndpoint.APP}/certifications/lite`, params);
    }

    listPartnershipOrdered(params: CertificationHttpParams): Observable<PaginatedResponse<Certification>> {
        return this.findAllPaginatedSorted(`${APIEndpoint.APP}/certifications/${params.siret}/partnership-ordered`, params);
    }

    get(certifInfo: string): Observable<Certification> {
        return this._httpClient.get<Certification>(`${APIEndpoint.API}/certifications/${certifInfo}`);
    }

    getByUrl(url: string): Observable<Certification> {
        return this._httpClient.get<Certification>(url);
    }

    getForAttendee(certifInfo: string): Observable<Certification> {
        return this._httpClient.get<Certification>(`${APIEndpoint.APP}/attendees/certifications/${certifInfo}`);
    }

    update(certification: Certification): Observable<Certification> {
        const files = Object.keys(certification).filter(key => certification[key] instanceof File);
        if (files.length > 0) {
            const formData: FormData = new FormData();
            files.forEach(key => {
                formData.append(key, certification[key]);
            });
            formData.append('json', JSON.stringify(certification));
            return this._httpClient.post<Certification>(`${APIEndpoint.APP}/certifications/${certification.certifInfo}`, formData);
        } else {
            return this._httpClient.put<Certification>(`${APIEndpoint.API}/certifications/${certification.certifInfo}`, certification);
        }
    }

    inviteCertifier(certifInfo: string, certifierContactInformation): Observable<void> {
        return this._httpClient.post<void>(`${APIEndpoint.APP}/certifications/inviteCertifier/${certifInfo}`, certifierContactInformation);
    }

    updateThumbnail(id: number): Observable<void> {
        return this._httpClient.post<void>(`${APIEndpoint.APP}/certifications/${id}/certificateTemplateThumbnail`, {});
    }

    activateAudits(certifInfo: string, isAnnualSubscription: boolean): Observable<Certification> {
        return this._httpClient.post<Certification>(`${APIEndpoint.APP}/certifications/${certifInfo}/activateAudits`, {isAnnualSubscription});
    }

    deactivateAudits(certifInfo: string): Observable<Certification> {
        return this._httpClient.post<Certification>(`${APIEndpoint.APP}/certifications/${certifInfo}/deactivateAudits`, {});
    }

    getDetails(certifInfo: string): Observable<CertificationStatistics> {
        return this._httpClient.get<CertificationStatistics>(`${APIEndpoint.API}/certifications/${certifInfo}/details`);
    }
}
