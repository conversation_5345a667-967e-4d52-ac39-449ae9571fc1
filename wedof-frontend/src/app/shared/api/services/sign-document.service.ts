import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {APIEndpoint} from './api-endpoint.enum';

@Injectable({
    providedIn: 'root'
})
export class SignDocumentService {

    constructor(private _httpClient: HttpClient) {
    }

    show(documentId): Observable<any> {
        return this._httpClient.get(`${APIEndpoint.APP}/signDocument/${documentId}`, {
            responseType: 'blob',
            observe: 'response'
        });
    }

    preview(documentId, params: any): Observable<any> {
        return this._httpClient.post(`${APIEndpoint.API}/signDocument/${documentId}/preview`, params, {
            responseType: 'blob',
            observe: 'response'
        });
    }

    generateCode(documentId: string, params: any): Observable<string> {
        return this._httpClient.post<string>(`${APIEndpoint.API}/signDocument/${documentId}/generateCode`, params);
    }

    sign(documentId: string, params: any): Observable<boolean> {
        return this._httpClient.post<boolean>(`${APIEndpoint.API}/signDocument/${documentId}/sign`, params);
    }
}
