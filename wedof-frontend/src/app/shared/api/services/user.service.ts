import {HttpClient} from '@angular/common/http';
import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {User} from '../models/user';
import {APIEndpoint} from './api-endpoint.enum';
import {AbstractPaginatedService, BaseHttpParams, PaginatedResponse} from './abstract-paginated.service';

export interface UserHttpParams extends BaseHttpParams {
    siret?: string;
}

@Injectable()
export class UserService extends AbstractPaginatedService<User> {


    constructor(
        protected _httpClient: HttpClient
    ) {
        super(_httpClient);
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Reserved to Ngxs
    // -----------------------------------------------------------------------------------------------------

    privateCreate(user: User): Observable<User> {
        return this._httpClient.post<User>(`${APIEndpoint.APP}/public/users`, user);
    }

    privateUpdate(email: string, user: User): Observable<User> {
        // Email is a separate parameter as it is the identifier of a user
        // but an identifier that can change through the update method...
        // So user.email (new email) may differ from the email used in the endpoint (old email)
        return this._httpClient.put<User>(`${APIEndpoint.API}/users/${email}`, user);
    }

    privateGetCurrent(): Observable<User> {
        return this._httpClient.get<User>(`${APIEndpoint.API}/users/me`);
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    createOnly(user: User): Observable<User> {
        return this._httpClient.post<User>(`${APIEndpoint.APP}/public/users/add`, user);
    }

    forgotPassword(email: string): Observable<void> {
        return this._httpClient.get<void>(`${APIEndpoint.APP}/public/users/forgotPassword/${email}`);
    }

    sendMagicLink(email: string): Observable<void> {
        return this._httpClient.get<void>(`${APIEndpoint.APP}/public/users/sendMagicLink/${email}`);
    }

    list(params: UserHttpParams): Observable<PaginatedResponse<User>> {
        return this.findAllPaginatedSorted(`${APIEndpoint.API}/users`, params);
    }

    findUser(query: string): Observable<User[]> {
        const params = {params: {email: query}};
        return this._httpClient.get<User[]>(`${APIEndpoint.API}/findUser`, params);
    }

    subscribeToPushNotifications(pushSubscription: PushSubscription): Observable<User[]> {
        return this._httpClient.post<User[]>(`${APIEndpoint.APP}/webpush/`, {
            subscription: pushSubscription.toJSON(),
            options: {}
        });
    }

    unSubscribeToPushNotifications(pushSubscription: PushSubscription): Observable<any> {
        return this._httpClient.request('delete', `${APIEndpoint.APP}/webpush/`, {
            body: {
                subscription: pushSubscription.toJSON(),
                options: {}
            }
        });
    }

    associateUser(emailUserInvited: string, resetPassword: boolean = false): Observable<any> {
        if (resetPassword) {
            return this._httpClient
                .post(`${APIEndpoint.API}/users/inviteUser/${emailUserInvited}?resetPassword=true`, {});
        } else {
            return this._httpClient
                .post(`${APIEndpoint.API}/users/inviteUser/${emailUserInvited}`, {});
        }
    }

    revokedUser(emailUserToDessociate: string): Observable<any> {
        return this._httpClient
            .post(`${APIEndpoint.API}/users/revoked/${emailUserToDessociate}`, {});
    }
}
