import {HttpClient} from '@angular/common/http';
import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {AbstractPaginatedService, BaseHttpParams, PaginatedResponse} from './abstract-paginated.service';
import {APIEndpoint} from './api-endpoint.enum';
import {
    CertificationPartnerAudit,
    CertificationPartnerAuditCompleteBody,
    CertificationPartnerAuditCreateBody,
    CertificationPartnerAuditCreateOnPartnersBody,
    CertificationPartnerAuditTemplate,
    CertificationPartnerAuditUpdateBody
} from '../models/certification-partner-audit';
import {Certification} from '../models/certification';

export interface CertificationPartnerAuditHttpParams extends BaseHttpParams {
    siret?: string;
    certifInfo: string;
}

@Injectable()
export class CertificationPartnerAuditService extends AbstractPaginatedService<CertificationPartnerAudit> {

    constructor(httpClient: HttpClient) {
        super(httpClient);
    }

    list(params: CertificationPartnerAuditHttpParams): Observable<PaginatedResponse<CertificationPartnerAudit>> {
        return this.findAllPaginatedSorted(`${APIEndpoint.API}/certifications/${params.certifInfo}/partners/${params.siret}/audits`, params);
    }

    create(certifInfo: string, siret: string, body: CertificationPartnerAuditCreateBody): Observable<CertificationPartnerAudit> {
        return this._httpClient.post<CertificationPartnerAudit>(`${APIEndpoint.API}/certifications/${certifInfo}/partners/${siret}/audits`, body);
    }

    update(certifInfo: string, siret: string, id: number, body: CertificationPartnerAuditUpdateBody): Observable<CertificationPartnerAudit> {
        return this._httpClient.put<CertificationPartnerAudit>(`${APIEndpoint.API}/certifications/${certifInfo}/partners/${siret}/audits/${id}`, body);
    }

    generateReport(certifInfo: string, siret: string, id: number): Observable<CertificationPartnerAudit> {
        return this._httpClient.post<CertificationPartnerAudit>(`${APIEndpoint.API}/certifications/${certifInfo}/partners/${siret}/audits/${id}/generateReport`, {});
    }

    complete(certifInfo: string, siret: string, id: number, body: CertificationPartnerAuditCompleteBody): Observable<CertificationPartnerAudit> {
        return this._httpClient.post<CertificationPartnerAudit>(`${APIEndpoint.API}/certifications/${certifInfo}/partners/${siret}/audits/${id}/complete`, body);
    }

    delete(id: number): Observable<void> {
        return this._httpClient.delete<void>(`${APIEndpoint.API}/certifications/partners/audits/${id}`);
    }

    createOnPartners(certifInfo: string, body: CertificationPartnerAuditCreateOnPartnersBody): Observable<[count: number, auditTemplate: CertificationPartnerAuditTemplate]> {
        const rawBody = {
            ...body,
            partnerCompliance: body.partnerCompliance.join(',')
        };
        return this._httpClient.post<[count: number, auditTemplate: CertificationPartnerAuditTemplate]>(`${APIEndpoint.API}/certifications/${certifInfo}/partners/audits`, rawBody);
    }

    getTrainingTitles(certifInfo: string): Observable<string[]> {
        return this._httpClient.get<string[]>(`${APIEndpoint.APP}/certifications/${certifInfo}/partners/sampleTrainingTitles`, {
            headers: {'X-Timeout-Override': '180000'}
        });
    }
}
