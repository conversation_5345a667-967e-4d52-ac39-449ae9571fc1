import {HttpClient} from '@angular/common/http';
import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {AbstractPaginatedService, BaseHttpParams, PaginatedResponse} from './abstract-paginated.service';
import {APIEndpoint} from './api-endpoint.enum';
import {Evaluation} from '../models/evaluation';

export interface EvaluationHttpParams extends BaseHttpParams {
    siret?: string;
    trainingActionId?: string;
    since?: Date;
    for?: string;
}

@Injectable()
export class EvaluationService extends AbstractPaginatedService<Evaluation> {
    constructor(httpClient: HttpClient) {
        super(httpClient);
    }

    list(params: EvaluationHttpParams): Observable<PaginatedResponse<Evaluation>> {
        return this.findAllPaginatedSorted(`${APIEndpoint.API}/evaluations`, params);
    }

    get(id: string): Observable<Evaluation> {
        return this._httpClient.get<Evaluation>(`${APIEndpoint.API}/evaluations/${id}`);
    }
}
