import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {APIEndpoint} from './api-endpoint.enum';
import {Training} from '../models/training';
import {AbstractPaginatedService, BaseHttpParams, PaginatedResponse} from './abstract-paginated.service';
import {DataProviders} from '../models/connection';

export interface TrainingHttpParams extends BaseHttpParams {
    state?: 'draft' | 'published' | 'unpublished' | 'archived';
    limit?: number;
    page?: number;
    siret?: string;
    certifInfo?: string;
    dataProvider?: DataProviders;
}

@Injectable({
    providedIn: 'root'
})
export class TrainingService extends AbstractPaginatedService<Training> {

    /**
     * Constructor
     *
     * @param {HttpClient} _httpClient
     */
    constructor(_httpClient: HttpClient
    ) {
        super(_httpClient);
    }

    /**
     *
     * @param params
     */
    list(params: TrainingHttpParams): Observable<PaginatedResponse<Training>> {
        return this.findAllPaginatedSorted(`${APIEndpoint.API}/trainings`, params);
    }

    update(training: Training): Observable<Training> {
        return this._httpClient.put<Training>(`${APIEndpoint.API}/trainings/${training.externalId}`, training);
    }
}
