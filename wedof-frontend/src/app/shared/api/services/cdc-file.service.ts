import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {AbstractPaginatedService, BaseHttpParams, PaginatedResponse} from './abstract-paginated.service';
import {Observable} from 'rxjs';
import {APIEndpoint} from './api-endpoint.enum';
import {CdcFile} from '../models/certification-folder';

export interface CdcFileHttpParams extends BaseHttpParams {
    name?: string;
    state?: string;
}

export interface CdcErrorStats {
    wedof: {
        'errorCount': number
        'totalCount': number
        'errorRate': number
    };
    external: {
        'errorCount': number
        'totalCount': number
        'errorRate': number
    };
}

@Injectable()
export class CdcFileService extends AbstractPaginatedService<CdcFile> {
    constructor(httpClient: HttpClient) {
        super(httpClient);
    }

    list(params: CdcFileHttpParams): Observable<PaginatedResponse<CdcFile>> {
        return this.findAllPaginatedSorted(`${APIEndpoint.API}/cdcFiles`, params);
    }

    update(cdcFile: CdcFile): Observable<CdcFile> {
        return this._httpClient.put<CdcFile>(`${APIEndpoint.API}/cdcFiles/${cdcFile.id}`, cdcFile);
    }

    generateXmlFileForCdc(idCertif: number): Observable<void> {
        return this._httpClient.post<void>(`${APIEndpoint.API}/cdcFile/generateXmlFileForCdc/${idCertif}`, {});
    }

    abort(id: number): Observable<CdcFile> {
        return this._httpClient.post<CdcFile>(`${APIEndpoint.API}/cdcFiles/${id}/abort`, null);
    }

    errorStats(): Observable<CdcErrorStats> {
        return this._httpClient.get<CdcErrorStats>(`${APIEndpoint.API}/cdcFiles/errorStats`);
    }
}
