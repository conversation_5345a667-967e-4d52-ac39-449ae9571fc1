import {HttpClient} from '@angular/common/http';
import {Injectable} from '@angular/core';
import {Observable, ReplaySubject} from 'rxjs';
import {APIEndpoint} from './api-endpoint.enum';
import {Proposal} from '../models/proposal';
import {RegistrationFolder} from '../models/registration-folder';
import {AbstractPaginatedService, PaginatedResponse} from './abstract-paginated.service';
import {TrainingAction} from '../models/training-action';
import {SafeHtml, SafeUrl} from '@angular/platform-browser';

@Injectable()
export class FunnelService extends AbstractPaginatedService<TrainingAction> {
    // Observables
    private _proposal: ReplaySubject<Proposal | null>;

    /**
     * Constructor
     *
     * @param {HttpClient} _httpClient
     */
    constructor(_httpClient: HttpClient
    ) {
        super(_httpClient);
        this._proposal = new ReplaySubject();
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Accessors
    // -----------------------------------------------------------------------------------------------------

    get proposal$(): Observable<Proposal> {
        return this._proposal.asObservable();
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------
    start(data: FunnelData): Observable<Proposal> {
        return this._httpClient.post<Proposal>(`${APIEndpoint.APP}/public/funnel/start`, data);
    }

    select(proposal: Proposal): Observable<Proposal> {
        return this._httpClient.put<Proposal>(`${APIEndpoint.APP}/public/funnel/select`, proposal);
    }

    setSvgData(data): Observable<void> {
        return this._httpClient.post<void>(`${APIEndpoint.APP}/public/funnel/svg`, data);
    }

    retrieve(code: string): Observable<FunnelData> {
        return this._httpClient.get<FunnelData>(`${APIEndpoint.APP}/public/funnel/retrieve/${code}`);
    }

    retrieveFolder(state?: string): Observable<RegistrationFolder> {
        return this._httpClient.get<RegistrationFolder>(`${APIEndpoint.APP}/public/funnel/folder${state ? ('/' + state) : ''}`);
    }

    findTrainingActions(term: string): Observable<PaginatedResponse<TrainingAction>> {
        // Dedicated endpoint that does some filtering in the backend (active, eligible etc.)
        return this.findAllPaginatedSorted(`${APIEndpoint.APP}/public/funnel/trainingActions`, {query: term});
    }

    update(data: PartialProposalData): Observable<Proposal> {
        return this._httpClient.post<Proposal>(`${APIEndpoint.APP}/public/funnel/update/${data.code}`, data);
    }
}

export interface PartialProposalData {
    code: string;
    firstName: string;
    lastName: string;
    phoneNumber: string;
    email: string;
}

export interface FunnelData {
    attendee: FunnelAttendeeData;
    genericCode: string;
}

export interface FunnelAttendeeData {
    email: string;
    firstName: string;
    lastName: string;
    phoneNumber: string;
    description: string;
}

export class FunnelConfiguration {
    colorScheme: string;
    logo: SafeUrl;
    financements: boolean;
    description: SafeHtml;
}
