import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {APIEndpoint} from './api-endpoint.enum';
import {FileType} from '../models/file';
import {PickerCards} from '../../material/picker/picker.component';

@Injectable({
    providedIn: 'root'
})
export class FileTypeService<T> {

    constructor(
        private _httpClient: HttpClient,
    ) {
    }

    create(entityClass: string, entityId: number, field: string, fileType: FileType<T>): Observable<FileType<T>> {
        if (fileType.generated === true && fileType.templateFile) {
            const formData: FormData = new FormData();
            formData.append('json', JSON.stringify(fileType));
            formData.append('templateFile', fileType.templateFile);
            return this._httpClient.post<FileType<T>>(`${APIEndpoint.API}/fileTypes/${entityClass}/${entityId}/${field}`, formData);
        }

        return this._httpClient.post<FileType<T>>(`${APIEndpoint.API}/fileTypes/${entityClass}/${entityId}/${field}`, fileType);
    }

    delete(entityClass: string, entityId: number, field: string, fileTypeId: number): Observable<FileType<T>> {
        return this._httpClient.delete<FileType<T>>(`${APIEndpoint.API}/fileTypes/${entityClass}/${entityId}/${field}/${fileTypeId}`);
    }

    update(entityClass: string, entityId: number, field: string, fileTypeId: number, fileType: FileType<T>): Observable<FileType<T>> {
        const templateFile: any = fileType.templateFile;
        if (fileType.templateFile && templateFile instanceof File) {
            const formData: FormData = new FormData();
            formData.append('json', JSON.stringify(fileType));
            formData.append('templateFile', fileType.templateFile);
            // Use a ?_method=PUT hack for Symfony since FormData aren't allowed in PUT method
            return this._httpClient.post<FileType<T>>(`${APIEndpoint.API}/fileTypes/${entityClass}/${entityId}/${field}/${fileTypeId}?_method=PUT`, formData);
        }

        return this._httpClient.put<FileType<T>>(`${APIEndpoint.API}/fileTypes/${entityClass}/${entityId}/${field}/${fileTypeId}`, fileType);
    }

    createTemplate(entityClass: string, entityId: number, field: string, accept: string, templateName: string = null, createFileType: boolean = false): Observable<string> {
        const body = {accept: accept};
        if (templateName) {
            body['templateName'] = templateName;
        }
        if (createFileType) {
            body['createFileType'] = true;
        }
        return this._httpClient.post<string>(`${APIEndpoint.APP}/fileTypes/createTemplate/${entityClass}/${entityId}/${field}`, body);
    }

    deleteTemplate(entityClass: string, field: string): Observable<void> {
        return this._httpClient.delete<void>(`${APIEndpoint.APP}/fileTypes/deleteTemplate/${entityClass}/${field}`);
    }

    listAllModels(field: string): Observable<PickerCards[]> {
        return this._httpClient.get<PickerCards[]>(`${APIEndpoint.APP}/fileTypes/templates/${field}`);
    }
}
