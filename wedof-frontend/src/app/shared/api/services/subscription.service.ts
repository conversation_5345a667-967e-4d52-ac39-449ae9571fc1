import {HttpClient} from '@angular/common/http';
import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {APIEndpoint} from './api-endpoint.enum';
import {
    Subscription,
    SubscriptionCertifierTypes,
    SubscriptionOptions,
    SubscriptionTrainingTypes
} from '../models/subscription';

interface SubscriptionResponse {
    url: string;
    subscriptionTrainingType?: string;
}

export interface SubscriptionCreateParams {
    training?: {
        type?: SubscriptionTrainingTypes;
        partnership?: string;
    };
    certifier?: {
        type?: SubscriptionCertifierTypes;
        partnership?: string;
    };
}

export interface SubscriptionUpdateParams {
    training?: {
        type: SubscriptionTrainingTypes;
    };
    certifier?: {
        type: SubscriptionCertifierTypes;
    };
}

export interface SubscriptionStripeParams {
    subscriptionCertifierType?: SubscriptionCertifierTypes;
    options?: SubscriptionOptions;
}

@Injectable({
    providedIn: 'root'
})
export class SubscriptionService {

    constructor(private _httpClient: HttpClient) {
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Reserved to Ngxs
    // -----------------------------------------------------------------------------------------------------

    privateGetCurrent(): Observable<Subscription> {
        return this._httpClient.get<Subscription>(`${APIEndpoint.APP}/subscriptions/me`);
    }

    privateCreate(subscriptionParams: SubscriptionCreateParams): Observable<Subscription> {
        return this._httpClient.post<Subscription>(`${APIEndpoint.APP}/subscriptions`, subscriptionParams);
    }

    privateUpdate(id: number, subscriptionParams: SubscriptionUpdateParams): Observable<Subscription> {
        return this._httpClient.put<Subscription>(`${APIEndpoint.APP}/subscriptions/${id}`, subscriptionParams);
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    showStripeTrainingAccount(subscriptionTrainingType?: SubscriptionTrainingTypes): Observable<SubscriptionResponse> {
        return this._httpClient.get<SubscriptionResponse>(`${APIEndpoint.APP}/subscriptions/manage/training/${subscriptionTrainingType ?? ''}`);
    }

    showStripeCertifierAccount(params?: SubscriptionStripeParams): Observable<SubscriptionResponse> {
        return this._httpClient.get<SubscriptionResponse>(`${APIEndpoint.APP}/subscriptions/manage/certifier/${params.subscriptionCertifierType ?? ''}`,
            {params: {options: JSON.stringify(params.options)}}
        );
    }

    updateFromReseller(subscriptionId: number): Observable<Subscription> {
        return this._httpClient.put<Subscription>(`${APIEndpoint.APP}/subscriptions/reseller/${subscriptionId}`, {});
    }
}
