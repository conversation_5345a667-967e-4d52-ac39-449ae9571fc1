import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {APIEndpoint} from './api-endpoint.enum';
import {Activity, ActivityCreateBody, ActivityMoveBody, ActivityUpdateBody} from '../models/activity';
import {AbstractPaginatedService, BaseHttpParams, PaginatedResponse} from './abstract-paginated.service';

export interface ActivityHttpParams extends BaseHttpParams {
    type?: string;
    qualiopi?: boolean;
    qualiopiIndicators?: string;
    done?: boolean;
}

@Injectable()
export class ActivityService extends AbstractPaginatedService<Activity> {

    constructor(_httpClient: HttpClient) {
        super(_httpClient);
    }

    listByEntity(entityClass: string, entityId: number | string, params: ActivityHttpParams): Observable<PaginatedResponse<Activity>> {
        return this.findAllPaginatedSorted(`${APIEndpoint.API}/activities/${entityClass}/${entityId}`, params);
    }

    create(body: ActivityCreateBody, entityClass: string, entityId: number | string): Observable<Activity> {
        return this._httpClient.post<Activity>(`${APIEndpoint.API}/activities/${entityClass}/${entityId}`, body);
    }

    update(body: ActivityUpdateBody, activityId: number): Observable<Activity> {
        return this._httpClient.put<Activity>(`${APIEndpoint.API}/activities/${activityId}`, body);
    }

    move(body: ActivityMoveBody, activityId: number, ): Observable<Activity> {
        return this._httpClient.post<Activity>(`${APIEndpoint.API}/activities/${activityId}/move`, body);
    }

    delete(id: number): Observable<void> {
        return this._httpClient.delete<void>(`${APIEndpoint.API}/activities/${id}`);
    }
}
