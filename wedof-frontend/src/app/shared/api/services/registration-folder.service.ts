import {HttpClient} from '@angular/common/http';
import {Injectable} from '@angular/core';
import {Observable, ReplaySubject} from 'rxjs';
import {tap} from 'rxjs/operators';

import {
    RegistrationFolder,
    RegistrationFolderCancelRefuse,
    RegistrationFolderCreate,
    RegistrationFolderInTraining,
    RegistrationFolderServiceDone,
    RegistrationFolderStates,
    RegistrationFolderTerminate,
    RegistrationFolderToBill, RegistrationFolderUpdateBody,
    SessionMinDates
} from '../models/registration-folder';
import {APIEndpoint} from './api-endpoint.enum';
import {AbstractPaginatedService, BaseHttpParams, PaginatedResponse} from './abstract-paginated.service';
import {
    RegistrationFolderActionCondition
} from '../../registration-folder/registration-folder-menu/registration-folder-menu.component';
import {
    KanbanColumnConfigsResponse,
    KanbanColumnResponse
} from '../../kanban-board/kanban-board/kanban-board.component';

export interface RegistrationFolderHttpParams extends BaseHttpParams {
    session?: string;
    siret?: string;
    state?: string;
    certifInfo?: string;
    view?: string;
    proposalCode?: string;
    type?: string;
}

@Injectable()
export class RegistrationFolderService extends AbstractPaginatedService<RegistrationFolder> {
    // Observables
    private _sessionMinDates: ReplaySubject<SessionMinDates>;

    constructor(_httpClient: HttpClient) {
        super(_httpClient);
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Accessors
    // -----------------------------------------------------------------------------------------------------

    get sessionMinDates$(): Observable<SessionMinDates> {
        if (!this._sessionMinDates) {
            return this.fetchSessionMinDates();
        }
        return this._sessionMinDates.asObservable();
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    listByColumn(columnIds: string[], params: RegistrationFolderHttpParams): Observable<KanbanColumnResponse<RegistrationFolder>> {
        return this._httpClient.get<KanbanColumnResponse<RegistrationFolder>>(
            `${APIEndpoint.APP}/registrationFolders`,
            {params: {...params, columnIds: columnIds.join(',')} as any}
        );
    }

    revenueByColumn(columnId: string, params: RegistrationFolderHttpParams): Observable<number> {
        return this._httpClient.get<number>(
            `${APIEndpoint.APP}/registrationFolders/revenue/${columnId}`,
            {params: params as any}
        );
    }

    listColumnConfigs(): Observable<KanbanColumnConfigsResponse> {
        return this._httpClient.get<KanbanColumnConfigsResponse>(`${APIEndpoint.APP}/registrationFolders/kanban/columnConfigs`);
    }

    getActionsRegistrationFolder(id: string): Observable<Record<RegistrationFolderStates, RegistrationFolderActionCondition[]>> {
        return this._httpClient.get<any>(`${APIEndpoint.APP}/registrationFolders/${id}/getActions`);
    }

    list(params: RegistrationFolderHttpParams): Observable<PaginatedResponse<RegistrationFolder>> {
        if (params.state?.includes(RegistrationFolderStates.REJECTED_WITHOUT_TITULAIRE_SUITE)) {
            params.state = params.state + ',rejected,rejectedWithoutCdcSuite,rejectedWithoutOfSuite'; // ajout des autres états rejected non gérés par le front
        }
        return this.findAllPaginatedSorted(`${APIEndpoint.API}/registrationFolders`, params);
    }

    get(externalId: string): Observable<RegistrationFolder> {
        return this._httpClient.get<RegistrationFolder>(`${APIEndpoint.API}/registrationFolders/${externalId}`);
    }

    getByUrl(url: string): Observable<RegistrationFolder> {
        return this._httpClient.get<RegistrationFolder>(url);
    }

    getForAttendee(externalId: string): Observable<RegistrationFolder> {
        return this._httpClient.get<RegistrationFolder>(`${APIEndpoint.APP}/attendees/registrationFolders/${externalId}`);
    }

    getInternalAttendeeLink(externalId: string): Observable<string> {
        return this._httpClient.get<string>(`${APIEndpoint.APP}/attendees/registrationFolders/${externalId}/attendeeLink`);
    }

    getMagicLink(externalId: string): Observable<{ url: string }> {
        return this._httpClient.get<{ url: string }>(`${APIEndpoint.APP}/registrationFolders/${externalId}/magicLink`);
    }

    create(body: RegistrationFolderCreate): Observable<RegistrationFolder> {
        return this._httpClient.post<RegistrationFolder>(`${APIEndpoint.API}/registrationFolders`, body);
    }

    update(registrationFolder: RegistrationFolderUpdateBody): Observable<RegistrationFolder> {
        return this._httpClient.put<RegistrationFolder>(`${APIEndpoint.API}/registrationFolders/${registrationFolder.externalId}`, registrationFolder);
    }

    /**
     * Only for ACCEPTED state and (VALIDATED state and type NOT cpf)
     * Reason Code: /api/registrationFoldersReasons?type=canceled
     */
    cancel(externalId: string, body: RegistrationFolderCancelRefuse): Observable<RegistrationFolder> {
        return this._httpClient.post<RegistrationFolder>(`${APIEndpoint.API}/registrationFolders/${externalId}/cancel`, body);
    }

    /**
     * Only for ACCEPTED state and type NOT cpf
     */
    canceledByAttendeeNotRealized(externalId: string): Observable<RegistrationFolder> {
        return this._httpClient.post<RegistrationFolder>(`${APIEndpoint.API}/registrationFolders/${externalId}/attendeeCancel`, null);
    }

    /**
     * Only for ACCEPTED state
     */
    inTraining(externalId: string, body: RegistrationFolderInTraining): Observable<RegistrationFolder> {
        return this._httpClient.post<RegistrationFolder>(`${APIEndpoint.API}/registrationFolders/${externalId}/inTraining`, body);
    }

    /**
     * Only for VALIDATED state and type NOT cpf
     */
    rejectedWithoutTitulaire(externalId: string): Observable<RegistrationFolder> {
        return this._httpClient.post<RegistrationFolder>(`${APIEndpoint.API}/registrationFolders/${externalId}/reject`, null);
    }

    /**
     * Only for VALIDATED state and type NOT cpf
     */
    refusedByAttendee(externalId: string): Observable<RegistrationFolder> {
        return this._httpClient.post<RegistrationFolder>(`${APIEndpoint.API}/registrationFolders/${externalId}/attendeeRefuse`, null);
    }

    /**
     * Only for VALIDATED state and type NOT cpf
     */
    waitingAcceptation(externalId: string): Observable<RegistrationFolder> {
        return this._httpClient.post<RegistrationFolder>(`${APIEndpoint.API}/registrationFolders/${externalId}/wait`, null);
    }

    /**
     * Only for NOT_PROCESSED state
     * Reason Code: /api/registrationFoldersReasons?type=refused
     */
    refuse(externalId: string, body: RegistrationFolderCancelRefuse): Observable<RegistrationFolder> {
        return this._httpClient.post<RegistrationFolder>(`${APIEndpoint.API}/registrationFolders/${externalId}/refuse`, body);
    }

    /**
     * Only for TERMINATED state
     */
    serviceDone(externalId: string, body: RegistrationFolderServiceDone): Observable<RegistrationFolder> {
        return this._httpClient.post<RegistrationFolder>(`${APIEndpoint.API}/registrationFolders/${externalId}/serviceDone`, body);
    }

    /**
     * Only for IN_TRAINING state
     * Reason Code: /api/registrationFoldersReasons?type=terminated
     */
    terminate(externalId: string, body: RegistrationFolderTerminate): Observable<RegistrationFolder> {
        return this._httpClient.post<RegistrationFolder>(`${APIEndpoint.API}/registrationFolders/${externalId}/terminate`, body);
    }

    /**
     * Only for SERVICE_DONE_VALIDATED state
     */
    toBill(externalId: string, body: RegistrationFolderToBill): Observable<RegistrationFolder> {
        return this._httpClient.post<RegistrationFolder>(`${APIEndpoint.API}/registrationFolders/${externalId}/billing`, body);
    }

    /**
     * Only for SERVICE_DONE_VALIDATED state
     */
    paid(externalId: string): Observable<RegistrationFolder> {
        return this._httpClient.post<RegistrationFolder>(`${APIEndpoint.API}/registrationFolders/${externalId}/paid`, {});
    }

    /**
     * Only for NOT_PROCESSED state
     */
    validate(externalId: string): Observable<RegistrationFolder> {
        return this._httpClient.post<RegistrationFolder>(`${APIEndpoint.API}/registrationFolders/${externalId}/validate`, null);
    }

    /**
     * Only for VALIDATED state
     */
    accept(externalId: string): Observable<RegistrationFolder> {
        return this._httpClient.post<RegistrationFolder>(`${APIEndpoint.API}/registrationFolders/${externalId}/accept`, null);
    }

    fetchSessionMinDates(): Observable<SessionMinDates> {
        if (!this._sessionMinDates) {
            this._sessionMinDates = new ReplaySubject();
        }
        return this._httpClient.get<SessionMinDates>(`${APIEndpoint.API}/registrationFolders/utils/sessionMinDates`).pipe(
            tap((sessionMinDates) => {
                this._sessionMinDates.next(sessionMinDates);
            })
        );
    }

    listAllFiles(externalId: string): Observable<[{ typeId: number }]> {
        return this._httpClient.get<[{ typeId: number }]>(`${APIEndpoint.API}/registrationFolders/${externalId}/files`);
    }

    // -------------------
    // METHODES AUTH NGINX
    // -------------------
    listThroughFunnel(params: RegistrationFolderHttpParams): Observable<PaginatedResponse<RegistrationFolder>> {
        return this.findAllPaginatedSorted(`${APIEndpoint.FUNNEL_API}/registrationFolders`, params);
    }

    fetchSessionMinDatesThroughFunnel(): Observable<SessionMinDates> {
        const params = {};
        return this._httpClient.get<SessionMinDates>(`${APIEndpoint.FUNNEL_API}/registrationFolders/utils/sessionMinDates`, {params: params});
    }
}

