import {Inject, Injectable} from '@angular/core';
import {HttpClient, HttpEvent, HttpParams, HttpRequest} from '@angular/common/http';
import {Observable} from 'rxjs';
import {APIEndpoint} from './api-endpoint.enum';
import {Download, download} from '../../download/download';
import {Saver, SAVER} from '../../download/saver.provider';
import {File as WedofFile, FileUpdateBody} from '../models/file';

@Injectable({
    providedIn: 'root'
})
export class FileService {

    constructor(
        private _httpClient: HttpClient,
        @Inject(SAVER) private save: Saver
    ) {
    }

    upload(resourceType: string, resourceId: string | number, file: File | string, params?: HttpParams, apiEndPoint?: string): Observable<HttpEvent<any>> | any {
        const endPoint = apiEndPoint ?? APIEndpoint.API;
        if (file instanceof File) {
            const formData: FormData = new FormData();
            formData.append('file', file);
            if (params) {
                params.keys().forEach((key) => {
                    formData.append(key, params.get(key));
                });
            }
            const req = new HttpRequest('POST', endPoint + '/' + resourceType + '/' + resourceId + '/files', formData, {
                reportProgress: true,
                responseType: 'json'
            });
            return this._httpClient.request(req);
        } else {
            params = params ? params : new HttpParams();
            if (!params.get('fileToDownload')) {
                params = params.set('file', file);
            }
            if (params) {
                params.keys().forEach((key) => {
                    params.append(key, params.get(key));
                });
            }
            return this._httpClient.post<any>(endPoint + '/' + resourceType + '/' + resourceId + '/files', params);
        }
    }

    generate(entityClass: 'Certification' | 'Organism', entityId: string | number, field: 'certificationFolderFileTypes' | 'registrationFolderFileTypes' | 'certificationPartnerFileTypes',
             idFileType: number, targetEntityClass: 'RegistrationFolder' | 'CertificationFolder' | 'CertificationPartner', targetEntityId: number,
             action: 'generate' | 'regenerate'): Observable<HttpEvent<any>> {
        const url = `${APIEndpoint.APP}/fileTypes/${entityClass}/${entityId}/${field}/${idFileType}/${targetEntityClass}/${targetEntityId}/${action}`;
        const req = new HttpRequest('POST', url, null, {
            reportProgress: true,
            responseType: 'json'
        });
        return this._httpClient.request(req);
    }

    download(typeOrUrl: string, idOrName?: string | number, file?: any, queryParams?: {
        [param: string]: string
    },       apiEndPoint?: string): Observable<Download<Blob>> {
        const apiEndpoint = apiEndPoint ? apiEndPoint : APIEndpoint.API;
        const url = file ? (apiEndpoint + '/' + typeOrUrl + (idOrName ? ('/' + idOrName) : '') + '/files/' + file.id) : typeOrUrl;
        const filename = file ? file.fileName : idOrName;
        return this._httpClient.get(url, {
            reportProgress: true,
            observe: 'events',
            responseType: 'blob',
            params: queryParams
        }).pipe(download((blob?: Blob, contentDisposition?: string) => {
            const _filename = filename ?? contentDisposition;
            this.save(blob, _filename);
        }));
    }

    blob(url: string, filename?: string): Observable<Blob> {
        return this._httpClient.get(url, {
            responseType: 'blob'
        });
    }

    delete(resourceType: string, resourceId: string | number, file: any, apiEndPoint?: string): Observable<any> {
        const endPoint = apiEndPoint ?? APIEndpoint.API;
        return this._httpClient.delete(endPoint + '/' + resourceType + '/' + resourceId + '/files/' + file.id);
    }

    updateState(resourceType: string, resourceId: string | number, fileId: number, body: FileUpdateBody, apiEndPoint?: string): Observable<WedofFile> {
        const endPoint = apiEndPoint ?? APIEndpoint.API;
        return this._httpClient.put<WedofFile>(endPoint + `/${resourceType}/${resourceId}/files/${fileId}`, body);
    }
}
