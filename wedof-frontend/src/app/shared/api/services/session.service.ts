import {HttpClient} from '@angular/common/http';
import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {AbstractPaginatedService, BaseHttpParams, PaginatedResponse} from './abstract-paginated.service';
import {APIEndpoint} from './api-endpoint.enum';
import {Session} from '../models/session';


export interface SessionHttpParams extends BaseHttpParams {
    externalId?: string;
    state?: 'draft' | 'published' | 'unpublished' | 'archived';
    href?: string;
    eligible?: boolean;
}

@Injectable()
export class SessionService extends AbstractPaginatedService<Session> {
    constructor(httpClient: HttpClient) {
        super(httpClient);
    }

    list(params: SessionHttpParams): Observable<PaginatedResponse<Session>> {
        return this.findAllPaginatedSorted(`${APIEndpoint.API}/sessions`, params);
    }

    get(externalId: string): Observable<Session> {
        return this._httpClient.get<Session>(`${APIEndpoint.API}/sessions/${externalId}`);
    }

    getByHref(href: string): Observable<Session> {
        return this._httpClient.get<Session>(href);
    }
}
