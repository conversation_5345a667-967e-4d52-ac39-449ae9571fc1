import {HttpClient, HttpEvent, HttpRequest} from '@angular/common/http';
import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {Organism} from '../models/organism';
import {APIEndpoint} from './api-endpoint.enum';
import {AbstractPaginatedService, BaseHttpParams, PaginatedResponse} from './abstract-paginated.service';
import {CertifierAccessState} from '../models/certifier-access';
import {DataProviderConfig, DataProviders} from '../models/connection';

export interface OrganismHttpParams extends BaseHttpParams {
    accessState?: CertifierAccessState;
    certifInfo?: string;
    organismType?: 'certifier' | 'partner' | 'all';
    isTrainingOrganism?: boolean;
}

@Injectable()
export class OrganismService extends AbstractPaginatedService<Organism> {
    constructor(_httpClient: HttpClient) {
        super(_httpClient);
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Reserved to Ngxs
    // -----------------------------------------------------------------------------------------------------

    privateGetCurrent(): Observable<Organism> {
        return this._httpClient.get<Organism>(`${APIEndpoint.APP}/organisms/me`);
    }

    privateCreate(siret: string, refresh: boolean): Observable<Organism> {
        return this._httpClient.get<Organism>(`${APIEndpoint.APP}/public/organisms/${siret}${refresh ? '?refresh=true' : ''}`);
    }

    privateUpdate(siret: string, organism: Organism): Observable<Organism> {
        const files = Object.keys(organism).filter(key => organism[key] instanceof File);
        if (files.length > 0) {
            const formData: FormData = new FormData();
            files.forEach(key => {
                formData.append(key, organism[key]);
            });
            formData.append('json', JSON.stringify(organism));
            return this._httpClient.post<Organism>(`${APIEndpoint.APP}/organisms/${siret}`, formData);
        } else {
            return this._httpClient.put<Organism>(`${APIEndpoint.API}/organisms/${siret}`, organism);
        }
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------


    find(params: OrganismHttpParams): Observable<PaginatedResponse<Organism>> {
        return this.findAllPaginatedSorted(`${APIEndpoint.API}/organisms`, params);
    }

    findInPartnershipWith(params: BaseHttpParams): Observable<PaginatedResponse<Organism>> {
        return this.findAllPaginatedSorted(`${APIEndpoint.APP}/organisms/inPartnershipWith`, params);
    }

    findPotentialCertificationFolderHolders(params: BaseHttpParams): Observable<PaginatedResponse<Organism>> {
        return this.findAllPaginatedSorted(`${APIEndpoint.APP}/organisms/potentialCertificationFolderHolders`, params);
    }

    get(siret: string): Observable<Organism> {
        return this._httpClient.get<Organism>(`${APIEndpoint.API}/organisms/${siret}`);
    }

    getForAttendee(siret: string): Observable<Organism> {
        return this._httpClient.get<Organism>(`${APIEndpoint.APP}/attendees/organisms/${siret}`);
    }

    getPublic(siret: string): Observable<Organism> {
        return this._httpClient.get<Organism>(`${APIEndpoint.APP}/public/organisms/${siret}`);
    }

    getPublicBySubDomain(): Observable<Organism> {
        return this._httpClient.get<Organism>(`${APIEndpoint.APP}/public/organisms`);
    }

    getOwnerEmail(siret: string): Observable<{ email: string }> {
        return this._httpClient.get<{ email: string }>(`${APIEndpoint.APP}/organisms/${siret}/ownerEmail`);
    }

    getByUrl(url: string): Observable<Organism> {
        return this._httpClient.get<Organism>(url);
    }

    listByUrl(url: string): Observable<Organism[]> {
        return this._httpClient.get<Organism[]>(url);
    }

    getDataProvidersForOrganism(organism: Organism, onlyRequired: boolean = false, requiresAuthentication: boolean = false): DataProviders[] {
        return Object.values(DataProviders).filter(dataProviderType => {
            const dataProviderConfigEntry = DataProviderConfig[dataProviderType];
            return (onlyRequired ? dataProviderConfigEntry.required : true) &&
                (requiresAuthentication ? dataProviderConfigEntry.requiresAuthentication : true) && (
                    organism.isCertifierOrganism && dataProviderConfigEntry.forCertifierOrganism ||
                    organism.isTrainingOrganism && dataProviderConfigEntry.forTrainingOrganism
                );
        });
    }

    verifySendAs(email: string): Observable<void> {
        return this._httpClient.post<void>(`${APIEndpoint.APP}/organisms/verifySendAs/${email}`, {});
    }

    deleteSendAs(email: string): Observable<void> {
        return this._httpClient.delete<void>(`${APIEndpoint.APP}/organisms/deleteSendAs/${email}`, {});
    }

    exportCpfCatalogXml(organism: Organism): Observable<Organism> {
        return this._httpClient.post<Organism>(`${APIEndpoint.API}/organisms/${organism.siret}/exportCpfCatalogXml`, {});
    }

    importCpfCatalogXml(organism: Organism, file: File | string): Observable<HttpEvent<Organism>> {
        const formData: FormData = new FormData();
        formData.append('file', file);
        const req = new HttpRequest('POST', `${APIEndpoint.API}/organisms/${organism.siret}/importCpfCatalogXml`, formData, {
            reportProgress: true,
            responseType: 'json'
        });
        return this._httpClient.request(req);
    }

    importCertificationFolders(file: File, organism: Organism): Observable<Organism> {
        const formData: FormData = new FormData();
        formData.append('file', file);
        return this._httpClient.post<Organism>(`${APIEndpoint.APP}/organisms/${organism.siret}/importCertificationFolders`, formData);
    }
}

