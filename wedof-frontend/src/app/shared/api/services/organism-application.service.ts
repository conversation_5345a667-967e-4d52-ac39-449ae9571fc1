import {HttpClient} from '@angular/common/http';
import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {OrganismApplication, OrganismApplicationStates} from '../models/organism-application';
import {APIEndpoint} from './api-endpoint.enum';
import {map} from 'rxjs/operators';
import {Params} from '@angular/router';
import {Application} from '../../../applications/shared/application.interface';
import {Subscription} from '../models/subscription';

@Injectable()
export class OrganismApplicationService {

    constructor(
        private _httpClient: HttpClient
    ) {
    }

    findAll(): Observable<OrganismApplication[]> {
        return this._httpClient.get<any[]>(`${APIEndpoint.APP}/applications`);
    }

    get(appId: string): Observable<OrganismApplication> {
        return this._httpClient.get<OrganismApplication>(`${APIEndpoint.APP}/applications/${appId}`);
    }

    getData(appId: string, method: string): Observable<any> {
        return this._httpClient.get<any>(`${APIEndpoint.APP}/applications/${appId}/data/${method}`);
    }

    postData(appId: string, method: string, data: any): Observable<any> {
        return this._httpClient.post<any>(`${APIEndpoint.APP}/applications/${appId}/data/${method}`, data);
    }

    setSettings(appId: string, metadata: any): Observable<OrganismApplication> {
        return this._httpClient.put<OrganismApplication>(`${APIEndpoint.APP}/applications/${appId}`, {metadata: metadata});
    }

    enable(appId: string): Observable<OrganismApplication> {
        return this._httpClient.post<OrganismApplication>(`${APIEndpoint.APP}/applications/${appId}/enable`, {});
    }

    disable(appId: string): Observable<OrganismApplication> {
        return this._httpClient.post<OrganismApplication>(`${APIEndpoint.APP}/applications/${appId}/disable`, {});
    }

    isAvailable(subscription: Subscription, application: Application): boolean {
        return subscription.allowedApps?.includes(application.appId());
    }

    isEnabled(subscription: Subscription, organismApplications: OrganismApplication[], application: Application): boolean {
        return this.isAvailable(subscription, application)
            && organismApplications?.some(organismApplication => organismApplication.appId === application.appId()
                && [OrganismApplicationStates.ENABLED,
                    OrganismApplicationStates.PENDING_DISABLE_TRIAL,
                    OrganismApplicationStates.PENDING_DISABLE_TRIAL].includes(organismApplication.state));
    }

    getOAuth2Authorization(appId: string): Observable<any> {
        return this._httpClient.get<any>(`${APIEndpoint.APP}/applications/${appId}/oauth2`).pipe(
            map(data => {
                    document.location.replace(data.url);
                }
            ));
    }

    validateOAuth2(appId: string, queryParams: Params): Observable<OrganismApplication> {
        return this._httpClient.get<OrganismApplication>(`${APIEndpoint.APP}/applications/${appId}/oauth2/callback`, {params: queryParams});
    }
}
