import {HttpClient} from '@angular/common/http';
import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {AbstractPaginatedService, BaseHttpParams, PaginatedResponse} from './abstract-paginated.service';
import {APIEndpoint} from './api-endpoint.enum';
import {Attendee, AttendeeCreateBody, CandidatePassportBody} from '../models/attendee';
import {environment} from '../../../../environments/environment';
import {Md5} from 'ts-md5/dist/md5';

@Injectable()
export class AttendeeService extends AbstractPaginatedService<Attendee> {
    constructor(protected _httpClient: HttpClient) {
        super(_httpClient);
    }

    list(params: BaseHttpParams): Observable<PaginatedResponse<Attendee>> {
        return this.findAllPaginatedSorted(`${APIEndpoint.API}/attendees`, params);
    }

    find(query: string): Observable<Attendee[]> {
        const params = {params: {query: query}};
        return this._httpClient.get<Attendee[]>(`${APIEndpoint.APP}/attendees/find`, params);
    }

    getCurrent(): Observable<Attendee> {
        return this._httpClient.get<Attendee>(`${APIEndpoint.APP}/attendees/me`);
    }

    create(params: AttendeeCreateBody): Observable<Attendee> {
        const files = Object.keys(params).filter(key => params[key] instanceof File);
        if (files.length > 0) {
            const formData: FormData = new FormData();
            files.forEach(key => {
                formData.append(key, params[key]);
            });
            formData.append('json', JSON.stringify(params));
            return this._httpClient.post<Attendee>(`${APIEndpoint.API}/attendees`, formData);
        } else {
            return this._httpClient.post<Attendee>(`${APIEndpoint.API}/attendees`, params);
        }
    }

    getAvatar(attendee: Attendee): string {
        const md5 = new Md5();
        if (environment.development) {
            return 'https://www.gravatar.com/avatar/' + md5.appendStr(attendee.email).end();
        } else {
            return 'https://www.gravatar.com/avatar/' + md5.appendStr(attendee.email).end() + '?d=' + encodeURI(environment.wedofBaseUri +
                '/app/public/users/initialAvatar/' + (attendee.firstName?.substr(0, 1) ?? '') + (attendee.lastName?.substr(0, 1) ?? ''));
        }
    }

    update(entityClass: string, entityId: string, body: CandidatePassportBody, identificationDocument: File = null): Observable<Attendee> {
        const formData: FormData = new FormData();
        if (identificationDocument) {
            formData.append('identificationDocument', identificationDocument);
        }
        if (body) {
            formData.append('json', JSON.stringify(body));
        }
        return this._httpClient.post<Attendee>(`${APIEndpoint.API}/attendees/${entityClass}/${entityId}/updateIdentificationData`, formData);
    }

    canUpdateManually(entityClass: string, entityId: string): Observable<{ canUpdateManually: boolean, nir: { canUpdateNir: boolean, retrievedNir: string}}> {
        return this._httpClient.get<{ canUpdateManually: boolean, nir: { canUpdateNir: boolean, retrievedNir: string  } }>(`${APIEndpoint.API}/attendees/${entityClass}/${entityId}/canUpdateManually`);
    }

    checkMatchingAndUpdateNir(entityId: string, body: {nir: string, firstName?: string, lastName?: string}): Observable<void> {
        return this._httpClient.post<void>(`${APIEndpoint.APP}/public/attendees/${entityId}/checkMatching`, body);
    }
}
