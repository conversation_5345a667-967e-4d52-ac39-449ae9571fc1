import {HttpClient} from '@angular/common/http';
import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {AbstractPaginatedService, PaginatedResponse} from './abstract-paginated.service';
import {APIEndpoint} from './api-endpoint.enum';
import {AttendeeExperience, AttendeeExperienceBody} from '../models/attendee-experience';

@Injectable({
    providedIn: 'root'
})
export class AttendeeExperienceService extends AbstractPaginatedService<AttendeeExperience> {
    constructor(_httpClient: HttpClient) {
        super(_httpClient);
    }

    list(attendeeId: number, params: any): Observable<PaginatedResponse<AttendeeExperience>> {
        return this.findAllPaginatedSorted(`${APIEndpoint.API}/attendees/${attendeeId}/experiences`, params);
    }

    create(candidateId: number, body: AttendeeExperienceBody): Observable<AttendeeExperience> {
        return this._httpClient.post<AttendeeExperience>(`${APIEndpoint.API}/attendees/${candidateId}/experiences`, body);
    }
}
