import {Component, EventEmitter, Output, ViewChild} from '@angular/core';
import {CertificationFolderCreateComponent} from '../../certification-folder/certification-folder-create/certification-folder-create.component';
import {CertificationFolder} from '../../api/models/certification-folder';
import {CanDeactivateComponent} from '../../utils/can-deactivate/can-deactivate.component';

@Component({
    selector: 'app-certification-folder-create-side',
    templateUrl: './certification-folder-create-side.component.html',
    styleUrls: ['./certification-folder-create-side.component.scss']
})
export class CertificationFolderCreateSideComponent extends CanDeactivateComponent {

    @ViewChild('certificationFolderCreate') private certificationFolderCreate: CertificationFolderCreateComponent;

    @Output() closeSide: EventEmitter<void> = new EventEmitter<void>();
    @Output() certificationFolderCreated: EventEmitter<CertificationFolder> = new EventEmitter<CertificationFolder>();

    askClose(): void {
        this.closeSide.emit();
    }

    _certificationFolderCreated($event: CertificationFolder): void {
        this.certificationFolderCreated.emit($event);
    }

    canDeactivate(): boolean {
        return this.certificationFolderCreate.canDeactivate();
    }

}
