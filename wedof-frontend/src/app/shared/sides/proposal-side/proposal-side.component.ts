import {Component, EventEmitter, Input, OnChanges, Output, SimpleChanges, ViewChild} from '@angular/core';
import {Proposal} from '../../api/models/proposal';
import {MatDrawer} from '@angular/material/sidenav';
import {CanDeactivateComponent} from '../../utils/can-deactivate/can-deactivate.component';
import {ProposalCardComponent} from '../../proposal/proposal-card/proposal-card.component';
import {EntityClass} from '../../utils/enums/entity-class';
import {ShortcutLoadingSidePanel, ShortcutSidePanel} from '../../utils/interface/shortcut-interfaces';
import {ActivatedRoute, Router} from '@angular/router';
import {Location} from '@angular/common';
import {getAnchor, goToShortCutWhenLoaded} from '../../utils/shortcut-side-panel-utils';
import {
    ShortcutFolderSidePanelEnum,
    ShortcutProposalSidePanelEnum
} from '../../utils/enums/shortcut-side-panel-enum';
import {ApplicationsService} from '../../../applications/shared/applications.service';
import {CardIntersection} from '../../utils/interface/cards-intersection-interfaces';
import {ActivitiesCardComponent} from '../../activities/activities-card/activities-card.component';
import {MetadataCardComponent} from '../../metadata/metadata-card/metadata-card.component';

@Component({
    selector: 'app-proposal-side',
    templateUrl: './proposal-side.component.html',
    styleUrls: ['./proposal-side.component.scss']
})
export class ProposalSideComponent extends CanDeactivateComponent implements OnChanges {

    isDataLoaded: ShortcutLoadingSidePanel[] = [];
    shortcuts: ShortcutSidePanel[] = [];
    visibleShortcuts: ShortcutSidePanel[] = [];
    organismApplicationEntryPointDelivery: Array<any> = [];
    organismApplicationEntryPointCards = [];
    isScrolling: boolean;
    isLoading: boolean;
    temporaryAnchor: string;

    private _arrayElemIntersection: Array<CardIntersection> = [];

    @Input() proposal: Proposal;
    @Input() hasNext: boolean;
    @Input() hasPrevious: boolean;
    @Output() next: EventEmitter<void> = new EventEmitter<void>();
    @Output() closeSide: EventEmitter<void> = new EventEmitter<void>();
    @Output() previous: EventEmitter<void> = new EventEmitter<void>();
    @Output() proposalRefreshed: EventEmitter<Proposal> = new EventEmitter<Proposal>();
    @Output() proposalDeleted: EventEmitter<any> = new EventEmitter<any>();
    @Input() sidenav: MatDrawer;

    @ViewChild('proposalSide') private proposalSide: ProposalCardComponent;

    public goToShortCutWhenLoaded = goToShortCutWhenLoaded;
    readonly EntityClass = EntityClass;

    constructor(public activatedRoute: ActivatedRoute,
                public applicationsService: ApplicationsService,
                private _location: Location,
                private _router: Router) {
        super();
        this.applicationsService.getOrganismApplicationEntryPoints('card-proposal-side').subscribe(organismApplicationEntryPoints => {
            this.organismApplicationEntryPointDelivery = [...organismApplicationEntryPoints];
        });
    }

    askNext(): void {
        this.next.emit();
    }

    askClose(): void {
        this.closeSide.emit();
    }

    askPrevious(): void {
        this.previous.emit();
    }

    _proposalRefreshed($event: Proposal): void {
        this.proposalRefreshed.emit($event);
    }

    _proposalDeleted($event: Proposal): void {
        this.proposalDeleted.emit($event);
        this.sidenav.close();
    }

    canDeactivate(): boolean {
        return this.proposalSide.canDeactivate();
    }

    ngOnChanges(changes: SimpleChanges): void {
        this.applicationsService.getOrganismApplicationEntryPoints('card-proposal-side').subscribe(organismApplicationEntryPoints => {
            this.shortcuts = this.defaultShortcuts();
            this.organismApplicationEntryPointCards = [];
            organismApplicationEntryPoints.forEach(organismApplicationEntryPointCard => {
                if (this.proposal.isIndividual) {
                    this.organismApplicationEntryPointCards.push(organismApplicationEntryPointCard);
                    this.shortcuts.push(organismApplicationEntryPointCard.params.shortcut);
                }
            });
            this.visibleShortcuts = this.shortcuts.filter((shortcut: ShortcutSidePanel) => shortcut.visible);

            const anchor: string = getAnchor(this._location.path());
            const navigateCommands = ['..', this.proposal.code];
            if (!Object.values(ShortcutFolderSidePanelEnum)
                .some((shortcut: ShortcutFolderSidePanelEnum): boolean => shortcut === anchor)) {
                navigateCommands.push(this.visibleShortcuts[0].target);
                this._router.navigate(navigateCommands, {
                    replaceUrl: true,
                    relativeTo: this.activatedRoute,
                    queryParamsHandling: 'preserve'
                });
            }
        });
    }

    defaultShortcuts(): ShortcutSidePanel[] {
        return [
            {
                target: ProposalCardComponent.COMPONENT_ID,
                title: 'private.common.shortcut.proposal',
                icon: 'folder_open',
                visible: true
            },
            {
                target: ActivitiesCardComponent.COMPONENT_ID,
                title: 'private.common.shortcut.activities',
                icon: 'format_list_bulleted',
                visible: true
            },
            {
                target: MetadataCardComponent.COMPONENT_ID,
                title: 'private.common.shortcut.metadatas',
                icon: 'dataset',
                visible: true
            }
        ];
    }

    updateUrlWithAnchor(anchor: string): void {
        const navigateCommands = ['..', this.proposal.code];
        if (Object.values(ShortcutProposalSidePanelEnum)
            .some((shortcut: ShortcutProposalSidePanelEnum): boolean => shortcut === anchor)) {
            navigateCommands.push(anchor);
            this._router.navigate(navigateCommands, {
                replaceUrl: true,
                relativeTo: this.activatedRoute,
                queryParamsHandling: 'preserve'
            });
        }
    }


    waitingScroll(scrollInfo: boolean, target = null): void {
        this.isScrolling = scrollInfo;
        this.temporaryAnchor = target;
        if (this.isScrolling === false) {
            this.scrollEndListener();
        }
    }

    scrollEndListener(): void {
        this.isScrolling = false;
        if (this.temporaryAnchor) {
            this.updateUrlWithAnchor(this.temporaryAnchor);
            this.temporaryAnchor = null;
        }
    }


    writeUrlFromIntersection(event): void {
        if (event.visible === true && !this.isScrolling && !this.isLoading) {
            if (this._arrayElemIntersection.some((innerEvent) => innerEvent.name === event.name)) {
                const indexOfEvent = this._arrayElemIntersection.findIndex((elem) => elem.name === event.name);
                this._arrayElemIntersection[indexOfEvent] = event;

            } else {
                this._arrayElemIntersection.push(event);
            }
            const visibleElement = this._arrayElemIntersection.reduce((highest, elem) => {
                return highest.ratio > elem.ratio ? highest : elem;
            });
            this.updateUrlWithAnchor(visibleElement.name);
        } else {
            if (this._arrayElemIntersection.some((innerEvent) => innerEvent.name === event.name)) {
                const indexOfEvent = this._arrayElemIntersection.findIndex((elem) => elem.name === event.name);
                event.ratio = 0;
                this._arrayElemIntersection[indexOfEvent] = event;
            }
        }
    }
}
