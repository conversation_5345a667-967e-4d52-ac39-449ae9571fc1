<header class="border-b">
    <mat-toolbar>
        <mat-toolbar-row>
            <span class="truncate"
                  title="{{ proposal.email ? proposal.firstName && proposal.lastName ? (proposal.firstName | titlecase) + (proposal.lastName | uppercase) :
                        proposal.email :
                    proposal.code}}">
                {{
                    proposal.email ? proposal.firstName && proposal.lastName ? (proposal.firstName | titlecase) + " " + (proposal.lastName | uppercase) :
                                     proposal.email :
                    proposal.code
                }}
            </span>
            <span class="flex-grow"></span>
            <button mat-icon-button (click)="askPrevious()" [disabled]="!hasPrevious">
                <mat-icon svgIcon="chevron_left"></mat-icon>
            </button>
            <button mat-icon-button (click)="askNext()" [disabled]="!hasNext">
                <mat-icon svgIcon="chevron_right"></mat-icon>
            </button>
            <a mat-icon-button (click)="askClose()">
                <mat-icon svgIcon="close"></mat-icon>
            </a>
        </mat-toolbar-row>
    </mat-toolbar>
    <div class="flex flex-row flex-wrap gap-1 p-4 pt-0">
        <app-shortcut *ngFor="let shortcut of visibleShortcuts"
                      [target]="shortcut.target"
                      [title]="shortcut.title"
                      [icon]="shortcut.icon"
                      (eventScrolling)="waitingScroll($event, shortcut.target)"
        ></app-shortcut>
    </div>
</header>
<aside class="flex flex-grow overflow-auto p-3 pt-0 light:bg-default">
    <app-proposal-card #proposalSide [proposal]="proposal"
                       (proposalDeleted)="_proposalDeleted($event)"
                       (proposalChange)="_proposalRefreshed($event)"
                       (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"
                       (elementIntersection)="writeUrlFromIntersection($event)">
    </app-proposal-card>
    <app-activities-card class="max-h-80 mb-3"
                         [entityClass]="EntityClass.PROPOSAL"
                         [entity]="proposal"
                         [entityId]="proposal.code"
                         (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"
                         (elementIntersection)="writeUrlFromIntersection($event)"></app-activities-card>
    <app-metadata-card class="mb-3"
                       [entity]="proposal"
                       [entityClass]="EntityClass.PROPOSAL"
                       [showInSide]="true"
                       (entityChange)="_proposalRefreshed($event)"
                       (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"
                       (elementIntersection)="writeUrlFromIntersection($event)"></app-metadata-card>
    <ng-container *ngFor="let context of organismApplicationEntryPointDelivery">
        <ng-container *ngTemplateOutlet="applicationsService.getTemplate(context.params.tpl);
                context: { context: context, proposal : proposal,
                activatedRoute: activatedRoute, goToShortCutWhenLoaded: goToShortCutWhenLoaded,
                isDataLoaded: isDataLoaded,elementIntersection: writeUrlFromIntersection}"></ng-container>
    </ng-container>
</aside>
