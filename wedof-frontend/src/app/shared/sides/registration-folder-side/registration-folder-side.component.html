<header class="border-b">
    <mat-toolbar>
        <mat-toolbar-row>
            <mat-icon svgIcon="mat_outline:folder_open"
                      class="mr-3"
                      title="{{ 'private.training-organism.folders.common.state.' + registrationFolder.state | translate}}"></mat-icon>
            <span class="truncate"
                  title="{{ registrationFolder.attendee.firstName | titlecase }} {{ registrationFolder.attendee.lastName | uppercase }}">{{ registrationFolder.attendee.firstName | titlecase }} {{ registrationFolder.attendee.lastName | uppercase }}</span>
            <span class="flex-grow"></span>
            <button mat-icon-button (click)="askPrevious()" [disabled]="!hasPrevious">
                <mat-icon svgIcon="chevron_left"></mat-icon>
            </button>
            <button mat-icon-button (click)="askNext()" [disabled]="!hasNext">
                <mat-icon svgIcon="chevron_right"></mat-icon>
            </button>
            <a mat-icon-button (click)="askClose()">
                <mat-icon svgIcon="close"></mat-icon>
            </a>
        </mat-toolbar-row>
    </mat-toolbar>
    <div class="flex flex-row flex-wrap gap-1 p-4 pt-0">
        <app-shortcut *ngFor="let shortcut of visibleShortcuts"
                      [target]="shortcut.target"
                      [title]="shortcut.title"
                      [icon]="shortcut.icon"
                      (eventScrolling)="waitingScroll($event, shortcut.target)"
        ></app-shortcut>
    </div>
</header>
<aside (scroll)="detectScroll()"
       class="flex flex-grow overflow-auto p-3 pt-0 light:bg-default">
    <app-attendee-card #attendeeSide
                       (attendeeChange)="_attendeeRefreshed($event)"
                       [attendeeFolderEntityContext]="{id:registrationFolder.externalId, class:EntityClass.REGISTRATION_FOLDER}"
                       [attendee]="registrationFolder.attendee"
                       (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"
                       (elementIntersection)="writeUrlFromIntersection($event)"
                       [attendeeLink]="certificationFolder?.attendeeLink"
    ></app-attendee-card>
    <app-registration-folder-card [openCard]="true"
                                  #registrationFolderSide
                                  [registrationFolder]="registrationFolder"
                                  (registrationFolderChange)="_registrationFolderRefreshed($event)"
                                  (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"
                                  (elementIntersection)="writeUrlFromIntersection($event)"></app-registration-folder-card>
    <app-certification-folder-card #certificationFolderSide
                                   *ngIf="certificationFolder"
                                   [certificationFolder]="certificationFolder"
                                   (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"
                                   (elementIntersection)="writeUrlFromIntersection($event)"></app-certification-folder-card>
    <app-activities-card class="max-h-80 mb-3"
                         [entityClass]="EntityClass.REGISTRATION_FOLDER"
                         [entity]="registrationFolder"
                         [entityId]="registrationFolder.externalId"
                         (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"
                         (elementIntersection)="writeUrlFromIntersection($event)"></app-activities-card>
    <app-metadata-card class="mb-3"
                       [entity]="registrationFolder"
                       [entityClass]="EntityClass.REGISTRATION_FOLDER"
                       [showInSide]="true"
                       (entityChange)="_registrationFolderRefreshed($event)"
                       (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"
                       (elementIntersection)="writeUrlFromIntersection($event)"></app-metadata-card>
    <app-working-contract-card *ngFor="let workingContract of workingContracts"
                               [workingContract]="workingContract"
    ></app-working-contract-card>
    <ng-container *ngFor="let context of organismApplicationEntryPointCards">
        <ng-container *ngTemplateOutlet="applicationsService.getTemplate(context.params.tpl);
                    context: { context: context, registrationFolder : registrationFolder,
                    activatedRoute: activatedRoute, goToShortCutWhenLoaded: goToShortCutWhenLoaded,
                    isDataLoaded: isDataLoaded, elementIntersection: writeUrlFromIntersection}"></ng-container>
    </ng-container>
</aside>
