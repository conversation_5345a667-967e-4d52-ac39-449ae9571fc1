import {Component, EventEmitter, Input, OnChanges, Output, SimpleChanges, ViewChild} from '@angular/core';
import {RegistrationFolder} from '../../api/models/registration-folder';
import {CertificationFolder} from '../../api/models/certification-folder';
import {CertificationFolderService} from '../../api/services/certification-folder.service';
import {CanDeactivateComponent} from '../../utils/can-deactivate/can-deactivate.component';
import {RegistrationFolderCardComponent} from '../../registration-folder/registration-folder-card/registration-folder-card.component';
import {Attendee} from '../../api/models/attendee';
import {CertificationFolderCardComponent} from '../../certification-folder/certification-folder-card/certification-folder-card.component';
import {AttendeeCardComponent} from '../../attendee/attendee-card/attendee-card.component';
import {ApplicationsService} from '../../../applications/shared/applications.service';
import {EntityClass} from '../../utils/enums/entity-class';
import {ShortcutLoadingSidePanel, ShortcutSidePanel} from '../../utils/interface/shortcut-interfaces';
import {ActivatedRoute, Router} from '@angular/router';
import {Location} from '@angular/common';
import {getAnchor, goToShortCutWhenLoaded} from '../../utils/shortcut-side-panel-utils';
import {ShortcutFolderSidePanelEnum} from '../../utils/enums/shortcut-side-panel-enum';
import {CardIntersection} from '../../utils/interface/cards-intersection-interfaces';
import {ActivitiesCardComponent} from '../../activities/activities-card/activities-card.component';
import {MetadataCardComponent} from '../../metadata/metadata-card/metadata-card.component';
import {WorkingContractService} from '../../api/services/working-contract.service';
import {WorkingContract} from '../../api/models/working-contract';
import {DataProviders} from '../../api/models/connection';


@Component({
    selector: 'app-registration-folder-side',
    templateUrl: './registration-folder-side.component.html',
    styleUrls: ['./registration-folder-side.component.scss']
})
export class RegistrationFolderSideComponent extends CanDeactivateComponent implements OnChanges {

    certificationFolder: CertificationFolder;
    attendee: Attendee;
    isDataLoaded: ShortcutLoadingSidePanel[] = [];
    organismApplicationEntryPointCards = [];
    shortcuts: ShortcutSidePanel[] = [];
    visibleShortcuts: ShortcutSidePanel[] = [];
    workingContracts: WorkingContract[] = [];
    isScrolling = false;
    temporaryAnchor: string = null;
    isLoading = true;
    scrollTimeOut: number;
    public goToShortCutWhenLoaded = goToShortCutWhenLoaded;
    readonly EntityClass = EntityClass;
    @Input() registrationFolder: RegistrationFolder;
    @Input() hasNext: boolean;
    @Input() hasPrevious: boolean;
    @Output() next: EventEmitter<void> = new EventEmitter<void>();
    @Output() closeSide: EventEmitter<void> = new EventEmitter<void>();
    @Output() previous: EventEmitter<void> = new EventEmitter<void>();
    @Output() registrationFolderRefreshed: EventEmitter<RegistrationFolder> = new EventEmitter<RegistrationFolder>();
    @Output() attendeeRefreshed: EventEmitter<Attendee> = new EventEmitter<Attendee>();
    @ViewChild('registrationFolderSide') private registrationFolderSide: RegistrationFolderCardComponent;
    @ViewChild('certificationFolderSide') private certificationFolderSide: CertificationFolderCardComponent;
    @ViewChild('attendeeSide') private attendeeSide: AttendeeCardComponent;

    private _arrayElemIntersection: Array<CardIntersection> = [];

    constructor(private _certificationFolderService: CertificationFolderService,
                public applicationsService: ApplicationsService,
                public activatedRoute: ActivatedRoute,
                public workingContractService: WorkingContractService,
                private _location: Location,
                private _router: Router) {
        super();
    }


    askNext(): void {
        this.next.emit();
    }

    askClose(): void {
        this.closeSide.emit();
    }

    askPrevious(): void {
        this.previous.emit();
    }

    _registrationFolderRefreshed($event: RegistrationFolder): void {
        this.registrationFolder = $event;
        this.registrationFolderRefreshed.emit($event);
    }

    _attendeeRefreshed($event: Attendee): void {
        this.attendeeRefreshed.emit($event);
    }


    ngOnChanges(changes: SimpleChanges): void {
        this.certificationFolder = null;
        this._arrayElemIntersection = [];
        this.attendee = this.registrationFolder.attendee;
        if (this.registrationFolder._links.certificationFolder) {
            this._certificationFolderService.getByUrl(this.registrationFolder._links.certificationFolder.href).subscribe((certificationFolder) => {
                this.certificationFolder = certificationFolder;
            });
        }
        this.workingContracts = [];
        if (this.registrationFolder.type === DataProviders.OPCO_CFA) {
            this.workingContractService.list({registrationFolderExternalId: this.registrationFolder.externalId}).subscribe(paginatedResponse => {
                this.workingContracts = paginatedResponse.payload;
            });
        }
        this.loadShortcuts();
    }

    loadShortcuts(): void {
        this.applicationsService.getOrganismApplicationEntryPoints('card-registration-folder-side').subscribe(organismApplicationEntryPoints => {
            this.shortcuts = this.defaultShortcuts();
            this.organismApplicationEntryPointCards = [];
            organismApplicationEntryPoints.forEach(organismApplicationEntryPointCard => {
                this.organismApplicationEntryPointCards.push(organismApplicationEntryPointCard);
                this.shortcuts.push(organismApplicationEntryPointCard.params.shortcut);
            });
            this.visibleShortcuts = this.shortcuts.filter((shortcut: ShortcutSidePanel) => shortcut.visible);
            const anchor: string = getAnchor(this._location.path());
            const navigateCommands = ['..', this.registrationFolder.externalId];
            if (!Object.values(ShortcutFolderSidePanelEnum)
                .some((shortcut: ShortcutFolderSidePanelEnum): boolean => shortcut === anchor)) {
                navigateCommands.push(this.visibleShortcuts[0].target);
                this._router.navigate(navigateCommands, {
                    replaceUrl: true,
                    relativeTo: this.activatedRoute,
                    queryParamsHandling: 'preserve'
                });
            }
        });
    }

    canDeactivate(): boolean {
        if (this.attendeeSide?.formGroup?.dirty) {
            return this.attendeeSide.canDeactivate();
        } else if (this.certificationFolderSide?.formGroup?.dirty) {
            return this.certificationFolderSide.canDeactivate();
        } else if (this.registrationFolderSide?.formGroup?.dirty) {
            return this.registrationFolderSide.canDeactivate();
        } else {
            return true;
        }
    }

    defaultShortcuts(): ShortcutSidePanel[] {
        return [
            {
                target: AttendeeCardComponent.COMPONENT_ID,
                title: 'private.common.shortcut.attendee',
                icon: 'person',
                visible: true
            },
            {
                target: RegistrationFolderCardComponent.COMPONENT_ID,
                title: 'private.common.shortcut.registrationFolder',
                icon: 'folder_open',
                visible: true
            },
            {
                target: CertificationFolderCardComponent.COMPONENT_ID,
                title: 'private.common.shortcut.certificationFolder',
                icon: 'folder_open',
                visible: !!this.registrationFolder._links.certificationFolder
            },
            {
                target: ActivitiesCardComponent.COMPONENT_ID,
                title: 'private.common.shortcut.activities',
                icon: 'format_list_bulleted',
                visible: true
            },
            {
                target: MetadataCardComponent.COMPONENT_ID,
                title: 'private.common.shortcut.metadatas',
                icon: 'dataset',
                visible: true
            }
        ];
    }

    updateUrlWithAnchor(anchor: string): void {
        const navigateCommands = ['..', this.registrationFolder.externalId];
        if (Object.values(ShortcutFolderSidePanelEnum).some((shortcut: ShortcutFolderSidePanelEnum): boolean => shortcut === anchor) && !this.isScrolling) {
            navigateCommands.push(anchor);
            this._router.navigate(navigateCommands, {
                replaceUrl: true,
                relativeTo: this.activatedRoute,
                queryParamsHandling: 'preserve'
            });
        }
    }


    waitingScroll(scrollInfo: boolean, target = null): void {
        this.isScrolling = scrollInfo;
        this.temporaryAnchor = target;
        if (this.isScrolling === false) {
            this.scrollEndListener();
        }
    }

    scrollEndListener(): void {
        this.isScrolling = false;
        if (this.temporaryAnchor) {
            this.updateUrlWithAnchor(this.temporaryAnchor);
            this.temporaryAnchor = null;
        } else {
            this.isLoading = false;
        }
    }

    detectScroll(): void {
        if (this.scrollTimeOut) {
            clearTimeout(this.scrollTimeOut);
        }
        this.scrollTimeOut = setTimeout(() => {
            this.scrollEndListener();
        }, 300);
    }


    writeUrlFromIntersection(event): void {
        if (event.visible === true && !this.isScrolling && !this.isLoading) {
            if (this._arrayElemIntersection.some((innerEvent) => innerEvent.name === event.name)) {
                const indexOfEvent = this._arrayElemIntersection.findIndex((elem) => elem.name === event.name);
                this._arrayElemIntersection[indexOfEvent] = event;

            } else {
                this._arrayElemIntersection.push(event);
            }
            const visibleElement = this._arrayElemIntersection.reduce((highest, elem) => {
                return highest.ratio > elem.ratio ? highest : elem;
            });
            this.updateUrlWithAnchor(visibleElement.name);
        } else {
            if (this._arrayElemIntersection.some((innerEvent) => innerEvent.name === event.name)) {
                const indexOfEvent = this._arrayElemIntersection.findIndex((elem) => elem.name === event.name);
                event.ratio = 0;
                this._arrayElemIntersection[indexOfEvent] = event;
            }
        }
    }
}

