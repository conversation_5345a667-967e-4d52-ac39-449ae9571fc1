<header class="border-b">
    <mat-toolbar>
        <mat-toolbar-row>
            <mat-icon svgIcon="mat_outline:folder_open"
                      class="mr-3"
                      title="{{ 'private.common.certificationFolder.state.' + certificationFolder.state | translate}}"></mat-icon>
            <span class="truncate"
                  title="{{ certificationFolder.attendee?.firstName | titlecase }} {{ certificationFolder.attendee?.lastName | uppercase }}">{{ certificationFolder.attendee?.firstName | titlecase }} {{ certificationFolder.attendee?.lastName | uppercase }}</span>
            <span class="flex-grow"></span>
            <button mat-icon-button (click)="askPrevious()" [disabled]="!hasPrevious">
                <mat-icon svgIcon="chevron_left"></mat-icon>
            </button>
            <button mat-icon-button (click)="askNext()" [disabled]="!hasNext">
                <mat-icon svgIcon="chevron_right"></mat-icon>
            </button>
            <a mat-icon-button (click)="askClose()">
                <mat-icon svgIcon="close"></mat-icon>
            </a>
        </mat-toolbar-row>
    </mat-toolbar>
    <div class="flex flex-row flex-wrap gap-1 p-4 pt-0">
        <app-shortcut *ngFor="let shortcut of visibleShortcuts"
                      [target]="shortcut.target"
                      [title]="shortcut.title"
                      [icon]="shortcut.icon"
                      (eventScrolling)="waitingScroll($event, shortcut.target)"
        ></app-shortcut>
    </div>
</header>
<aside (scroll)="detectScroll()" class="flex flex-grow overflow-auto p-3 pt-0 light:bg-default">
    <app-attendee-card #attendeeSide [attendee]="certificationFolder.attendee"
                       [attendeeFolderEntityContext]="{id:certificationFolder.externalId, class:EntityClass.CERTIFICATION_FOLDER}"
                       (attendeeChange)="_attendeeRefreshed($event)"
                       (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"
                       (elementIntersection)="writeUrlFromIntersection($event)"
                       [attendeeLink]="certificationFolder.attendeeLink"
    ></app-attendee-card>
    <app-certification-folder-card #certificationFolderSide
                                   [openCard]="true"
                                   (certificationFolderDeleted)="_certificationFolderDeleted($event)"
                                   (certificationFolderChange)="_certificationFolderRefreshed($event)"
                                   [certificationFolder]="certificationFolder"
                                   (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"
                                   (elementIntersection)="writeUrlFromIntersection($event)"></app-certification-folder-card>
    <app-registration-folder-card #registrationFolderSide *ngIf="registrationFolder"
                                  (registrationFolderChange)="_registrationFolderRefreshed($event)"
                                  [registrationFolder]="registrationFolder"
                                  (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"
                                  (elementIntersection)="writeUrlFromIntersection($event)"></app-registration-folder-card>
    <app-activities-card class="max-h-80 mb-3"
                         [entityClass]="EntityClass.CERTIFICATION_FOLDER"
                         [entity]="certificationFolder"
                         [entityId]="certificationFolder.externalId"
                         (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"
                         (elementIntersection)="writeUrlFromIntersection($event)"></app-activities-card>
    <app-certification-folder-survey-card class="mb-3"
                                          [isCandidate]="false"
                                          [candidateId]="certificationFolder.attendee.id"
                                          [certificationFolder]="certificationFolder"
                                          (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"
                                          (elementIntersection)="writeUrlFromIntersection($event)"></app-certification-folder-survey-card>
    <app-metadata-card class="mb-3"
                       *ngIf="certificationFolder._links.certifier.siret === organism.siret"
                       [entityClass]="EntityClass.CERTIFICATION_FOLDER"
                       [showInSide]="true"
                       (entityChange)="_certificationFolderRefreshed($event)"
                       [entity]="certificationFolder"
                       (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"
                       (elementIntersection)="writeUrlFromIntersection($event)"></app-metadata-card>
    <ng-container *ngFor="let context of organismApplicationEntryPointDelivery">
        <ng-container *ngTemplateOutlet="applicationsService.getTemplate(context.params.tpl);
                    context: { context: context, certificationFolder : certificationFolder,
                    isOwner: certificationFolder._links.certifier.siret === organism.siret,
                    activatedRoute: activatedRoute, goToShortCutWhenLoaded: goToShortCutWhenLoaded,
                    isDataLoaded: isDataLoaded, elementIntersection: writeUrlFromIntersection }"></ng-container>
    </ng-container>
</aside>
