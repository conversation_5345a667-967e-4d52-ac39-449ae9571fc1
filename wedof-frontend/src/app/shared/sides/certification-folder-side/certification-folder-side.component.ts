import {
    Component,
    EventEmitter,
    Input,
    OnChanges,
    OnDestroy,
    OnInit,
    Output,
    SimpleChanges,
    ViewChild
} from '@angular/core';
import {CertificationFolder} from '../../api/models/certification-folder';
import {RegistrationFolder} from '../../api/models/registration-folder';
import {RegistrationFolderService} from '../../api/services/registration-folder.service';
import {CertificationFolderCardComponent} from '../../certification-folder/certification-folder-card/certification-folder-card.component';
import {CanDeactivateComponent} from '../../utils/can-deactivate/can-deactivate.component';
import {Attendee} from '../../api/models/attendee';
import {AttendeeCardComponent} from '../../attendee/attendee-card/attendee-card.component';
import {RegistrationFolderCardComponent} from '../../registration-folder/registration-folder-card/registration-folder-card.component';
import {ApplicationsService} from '../../../applications/shared/applications.service';
import {EntityClass} from '../../utils/enums/entity-class';
import {ShortcutLoadingSidePanel, ShortcutSidePanel} from '../../utils/interface/shortcut-interfaces';
import {ActivatedRoute, Router} from '@angular/router';
import {Location} from '@angular/common';
import {getAnchor, goToShortCutWhenLoaded} from '../../utils/shortcut-side-panel-utils';
import {ShortcutFolderSidePanelEnum} from '../../utils/enums/shortcut-side-panel-enum';
import {CardIntersection} from '../../utils/interface/cards-intersection-interfaces';
import {ActivitiesCardComponent} from '../../activities/activities-card/activities-card.component';
import {CertificationFolderSurveyCardComponent} from '../../certification-folder-survey/certification-folder-survey-card/certification-folder-survey-card.component';
import {MetadataCardComponent} from '../../metadata/metadata-card/metadata-card.component';
import {takeUntil} from 'rxjs/operators';
import {Select} from '@ngxs/store';
import {OrganismState} from '../../api/state/organism.state';
import {Observable, Subject} from 'rxjs';
import {Organism} from '../../api/models/organism';

@Component({
    selector: 'app-certification-folder-side',
    templateUrl: './certification-folder-side.component.html',
    styleUrls: ['./certification-folder-side.component.scss']
})
export class CertificationFolderSideComponent extends CanDeactivateComponent implements OnInit, OnChanges, OnDestroy {

    constructor(private _registrationFolderService: RegistrationFolderService,
                public applicationsService: ApplicationsService,
                public activatedRoute: ActivatedRoute,
                private _location: Location,
                private _router: Router) {
        super();
        this.applicationsService.getOrganismApplicationEntryPoints('card-certification-folder-side').subscribe(organismApplicationEntryPoints => {
            this.organismApplicationEntryPointDelivery = [...organismApplicationEntryPoints];
        });
    }

    organism: Organism;
    registrationFolder: RegistrationFolder;
    attendee: Attendee;
    organismApplicationEntryPointDelivery: Array<any> = [];
    isDataLoaded: ShortcutLoadingSidePanel[] = [];
    visibleShortcuts: ShortcutSidePanel[] = [];
    shortcuts: ShortcutSidePanel[] = [];
    organismApplicationEntryPointCards = [];
    isLoading = true;
    isScrolling = false;
    temporaryAnchor: string = null;
    scrollTimeOut: number;

    private _arrayElemIntersection: Array<CardIntersection> = [];
    private _unsubscribeAll = new Subject<void>();

    @Select(OrganismState.organism) organism$: Observable<Organism>;

    @ViewChild('certificationFolderSide') private certificationFolderSide: CertificationFolderCardComponent;
    @ViewChild('attendeeSide') private attendeeSide: AttendeeCardComponent;
    @ViewChild('registrationFolderSide') private registrationFolderSide: RegistrationFolderCardComponent;

    @Input() certificationFolder: CertificationFolder;
    @Input() hasNext: boolean;
    @Input() hasPrevious: boolean;

    @Output() next: EventEmitter<void> = new EventEmitter<void>();
    @Output() closeSide: EventEmitter<void> = new EventEmitter<void>();
    @Output() previous: EventEmitter<void> = new EventEmitter<void>();
    @Output() certificationFolderRefreshed: EventEmitter<CertificationFolder> = new EventEmitter<CertificationFolder>();
    @Output() registrationFolderRefreshed: EventEmitter<RegistrationFolder> = new EventEmitter<RegistrationFolder>();
    @Output() attendeeRefreshed: EventEmitter<Attendee> = new EventEmitter<Attendee>();
    @Output() certificationFolderDeleted: EventEmitter<any> = new EventEmitter<any>();

    readonly EntityClass = EntityClass;

    public goToShortCutWhenLoaded = goToShortCutWhenLoaded;

    ngOnInit(): void{
        this.organism$.pipe(takeUntil(this._unsubscribeAll)).subscribe(organism => {
            this.organism = organism;
        });
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    askNext(): void {
        this.next.emit();
    }

    askClose(): void {
        this.closeSide.emit();
    }

    askPrevious(): void {
        this.previous.emit();
    }

    _certificationFolderRefreshed($event: CertificationFolder): void {
        this.certificationFolder = $event;
        this.certificationFolderRefreshed.emit($event);
    }

    _certificationFolderDeleted($event: CertificationFolder): void {
        this.certificationFolderDeleted.emit($event);
        this.askClose();
    }

    _registrationFolderRefreshed($event: RegistrationFolder): void {
        this.registrationFolderRefreshed.emit($event);
    }

    _attendeeRefreshed($event: Attendee): void {
        this.attendeeRefreshed.emit($event);
    }

    ngOnChanges(changes: SimpleChanges): void {
        this.registrationFolder = null;
        this.attendee = this.certificationFolder.attendee;
        if (this.certificationFolder._links.registrationFolder) {
            this._registrationFolderService.getByUrl(this.certificationFolder._links.registrationFolder.href).subscribe((registrationFolder) => {
                this.registrationFolder = registrationFolder;
            });
        }
        this.loadShortcuts();
    }

    loadShortcuts(): void {
        this.applicationsService.getOrganismApplicationEntryPoints('card-certification-folder-side').subscribe(organismApplicationEntryPoints => {
            this.shortcuts = this.defaultShortcuts();
            this.organismApplicationEntryPointCards = [];
            organismApplicationEntryPoints.forEach(organismApplicationEntryPointCard => {
                this.organismApplicationEntryPointCards.push(organismApplicationEntryPointCard);
                this.shortcuts.push(organismApplicationEntryPointCard.params.shortcut);
            });
            this.visibleShortcuts = this.shortcuts.filter((shortcut: ShortcutSidePanel) => shortcut.visible);

            const anchor: string = getAnchor(this._location.path());
            const navigateCommands = ['..', this.certificationFolder.externalId];
            if (!Object.values(ShortcutFolderSidePanelEnum)
                .some((shortcut: ShortcutFolderSidePanelEnum): boolean => shortcut === anchor)) {
                navigateCommands.push(this.visibleShortcuts[0].target);
                this._router.navigate(navigateCommands, {
                    replaceUrl: true,
                    relativeTo: this.activatedRoute,
                    queryParamsHandling: 'preserve'
                });
            }
        });
    }

    canDeactivate(): boolean {
        if (this.attendeeSide?.formGroup?.dirty) {
            return this.attendeeSide.canDeactivate();
        } else if (this.certificationFolderSide?.formGroup?.dirty) {
            return this.certificationFolderSide.canDeactivate();
        } else if (this.registrationFolderSide?.formGroup?.dirty) {
            return this.registrationFolderSide.canDeactivate();
        } else {
            return true;
        }
    }

    defaultShortcuts(): ShortcutSidePanel[] {
        return [
            {
                target: 'candidat',
                title: 'private.common.shortcut.candidate',
                icon: 'person',
                visible: true
            },
            {
                target: CertificationFolderCardComponent.COMPONENT_ID,
                title: 'private.common.shortcut.certificationFolder',
                icon: 'folder_open',
                visible: true
            },
            {
                target: RegistrationFolderCardComponent.COMPONENT_ID,
                title: 'private.common.shortcut.registrationFolder',
                icon: 'folder_open',
                visible: !!this.certificationFolder._links.registrationFolder
            },
            {
                target: ActivitiesCardComponent.COMPONENT_ID,
                title: 'private.common.shortcut.activities',
                icon: 'format_list_bulleted',
                visible: true
            },
            {
                target: CertificationFolderSurveyCardComponent.COMPONENT_ID,
                title: 'private.common.shortcut.surveys',
                icon: 'view_timeline',
                visible: true
            },
            {
                target: MetadataCardComponent.COMPONENT_ID,
                title: 'private.common.shortcut.metadatas',
                icon: 'dataset',
                visible: this.certificationFolder._links.certifier.siret === this.organism.siret
            }
        ];
    }

    updateUrlWithAnchor(anchor: string): void {
        const navigateCommands = ['..', this.certificationFolder.externalId];
        if (Object.values(ShortcutFolderSidePanelEnum).some((shortcut: ShortcutFolderSidePanelEnum): boolean => shortcut === anchor)) {
            navigateCommands.push(anchor);
            this._router.navigate(navigateCommands, {
                replaceUrl: true,
                relativeTo: this.activatedRoute,
                queryParamsHandling: 'preserve'
            });
        }
    }

    waitingScroll(scrollInfo: boolean, target = null): void {
        this.isScrolling = scrollInfo;
        this.temporaryAnchor = target;
        if (this.isScrolling === false) {
            this.scrollEndListener();
        }
    }

    scrollEndListener(): void {
        this.isScrolling = false;
        if (this.temporaryAnchor) {
            this.updateUrlWithAnchor(this.temporaryAnchor);
            this.temporaryAnchor = null;
        } else {
            this.isLoading = false;
        }

    }

    detectScroll(): void {
        if (this.scrollTimeOut) {
            clearTimeout(this.scrollTimeOut);
        }
        this.scrollTimeOut = setTimeout(() => {
            this.scrollEndListener();
        }, 300);
    }


    writeUrlFromIntersection(event): void {
        if (event.visible === true && !this.isScrolling && !this.isLoading) {
            if (this._arrayElemIntersection.some((innerEvent) => innerEvent.name === event.name)) {
                const indexOfEvent = this._arrayElemIntersection.findIndex((elem) => elem.name === event.name);
                this._arrayElemIntersection[indexOfEvent] = event;

            } else {
                this._arrayElemIntersection.push(event);
            }
            const visibleElement = this._arrayElemIntersection.reduce((highest, elem) => {
                return highest.ratio > elem.ratio ? highest : elem;
            });
            this.updateUrlWithAnchor(visibleElement.name);
        } else {
            if (this._arrayElemIntersection.some((innerEvent) => innerEvent.name === event.name)) {
                const indexOfEvent = this._arrayElemIntersection.findIndex((elem) => elem.name === event.name);
                event.ratio = 0;
                this._arrayElemIntersection[indexOfEvent] = event;
            }
        }
    }
}

