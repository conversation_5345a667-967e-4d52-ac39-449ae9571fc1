import {Component, EventEmitter, Output, ViewChild} from '@angular/core';
import {Certification} from '../../api/models/certification';
import {CertificationCreateComponent} from '../../certification/certification-create/certification-create.component';

@Component({
    selector: 'app-certification-create-side',
    templateUrl: './certification-create-side.component.html',
    styleUrls: ['./certification-create-side.component.scss']
})
export class CertificationCreateSideComponent  {

    @ViewChild('certificationCreate') private certificationCreate: CertificationCreateComponent;

    @Output() closeSide: EventEmitter<void> = new EventEmitter<void>();
    @Output() certificationCreated: EventEmitter<Certification> = new EventEmitter<Certification>();

    askClose(): void {
        this.closeSide.emit();
    }

    _certificationCreated($event: Certification): void {
        this.certificationCreated.emit($event);
    }

}
