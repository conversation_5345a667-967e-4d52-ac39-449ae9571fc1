<header class="border-b">
    <mat-toolbar>
        <mat-toolbar-row>
            <span class="truncate"
                  title="{{ 'private.training-organism.proposals.form.subtitle' | translate}}">{{ 'private.training-organism.proposals.form.subtitle' | translate}}</span>
            <span class="flex-grow"></span>
            <a mat-icon-button (click)="askClose()">
                <mat-icon svgIcon="close"></mat-icon>
            </a>
        </mat-toolbar-row>
        <mat-toolbar-row>
        </mat-toolbar-row>
    </mat-toolbar>
</header>

<aside class="flex flex-grow overflow-auto p-3 pt-0 light:bg-default">
    <app-proposal-create #proposalCreate (proposalCreate)="_proposalCreated($event)"></app-proposal-create>
</aside>

