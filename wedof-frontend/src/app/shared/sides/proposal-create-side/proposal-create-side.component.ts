import {Component, EventEmitter, Output, ViewChild} from '@angular/core';
import {Proposal} from '../../api/models/proposal';
import {CanDeactivateComponent} from '../../utils/can-deactivate/can-deactivate.component';
import {ProposalCreateComponent} from '../../proposal/proposal-create/proposal-create.component';

@Component({
    selector: 'app-proposal-create-side',
    templateUrl: './proposal-create-side.component.html',
    styleUrls: ['./proposal-create-side.component.scss']
})
export class ProposalCreateSideComponent extends CanDeactivateComponent {

    @Output() closeSide: EventEmitter<void> = new EventEmitter<void>();
    @Output() proposalCreated: EventEmitter<Proposal> = new EventEmitter<Proposal>();
    @ViewChild('proposalCreate') private proposalCreate: ProposalCreateComponent;

    askClose(): void {
        this.closeSide.emit();
    }

    _proposalCreated($event: Proposal): void {
        this.proposalCreated.emit($event);
    }

    canDeactivate(): boolean {
        return this.proposalCreate.canDeactivate();
    }
}
