<header class="border-b">
    <mat-toolbar>
        <mat-toolbar-row>
            <span class="truncate flex items-center"
                  title="{{ certification?.externalId}} - {{certification?.name}}">
                  <mat-icon class="mr-1"
                            *ngIf="certificationPartner && certificationPartner.compliance !== certificationPartnerAuditResults.NONE "
                            [matTooltip]="'private.certification.audit.result.' + certificationPartner.compliance | translate"
                            [class]="certificationPartner.compliance === certificationPartnerAuditResults.COMPLIANT ? 'text-green' :
                                     certificationPartner.compliance === certificationPartnerAuditResults.PARTIALLY_COMPLIANT ? 'text-orange' : ''"
                            [color]="certificationPartner.compliance === certificationPartnerAuditResults.NON_COMPLIANT ? 'warn' : undefined">
                      {{ certificationPartner.compliance | certificationPartnerAuditResultToIcon }}
                  </mat-icon>
                <span>{{ certification?.externalId }} - {{ certification?.name }}</span>
            </span>
            <span class="flex-grow"></span>
            <button mat-icon-button (click)="askPrevious()" [disabled]="!hasPrevious">
                <mat-icon svgIcon="chevron_left"></mat-icon>
            </button>
            <button mat-icon-button (click)="askNext()" [disabled]="!hasNext">
                <mat-icon svgIcon="chevron_right"></mat-icon>
            </button>
            <a mat-icon-button (click)="askClose()">
                <mat-icon svgIcon="close"></mat-icon>
            </a>
        </mat-toolbar-row>
    </mat-toolbar>
    <div class="flex flex-row flex-wrap gap-1 p-4 pt-0" *ngIf="certification" [ngClass]="{'card-loading':cardLoading}">
        <app-shortcut *ngFor="let shortcut of visibleShortcuts"
                      [target]="shortcut.target"
                      [title]="shortcut.title"
                      [icon]="shortcut.icon"
                      (eventScrolling)="waitingScroll($event, shortcut.target)"
        ></app-shortcut>
    </div>
</header>
<aside (scroll)="detectScroll()" class="flex flex-grow overflow-auto p-3 pt-0 light:bg-default">
    <div *ngIf="certification" [ngClass]="{'card-loading':cardLoading}">

        <app-certification-partner-partnership-card *ngIf="!isCertifier"
                                                    [certificationPartner]="certificationPartner"
                                                    [certification]="certification"
                                                    (certificationPartnerUpdated)="onCertificationPartnerUpdated($event)"
                                                    (certificationPartnerDeleted)="certificationPartnerDeleted.emit($event)"
                                                    (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"
                                                    (elementIntersection)="writeUrlFromIntersection($event)">
        </app-certification-partner-partnership-card>

        <app-certification-partner-access-card *ngIf="certifierAccess && !isCertifier"
                                               [certifierAccess]="certifierAccess"
                                               (certifierAccessUpdated)="certifierAccessUpdated.emit($event)"
                                               (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"
                                               (elementIntersection)="writeUrlFromIntersection($event)">
        </app-certification-partner-access-card>

        <app-certification-statistics
            *ngIf="isCertifier || ((certificationPartner?.state === certificationPartnerStates.ACTIVE || certificationPartner?.state === certificationPartnerStates.REVOKED || certificationPartner?.state === certificationPartnerStates.SUSPENDED) && certificationPartner?._links?.certifier?.hasOwner)"
            [cardTitle]="'private.training-organism.certifications.statistiques.title' | translate"
            [siret]="organism.siret"
            [showPartner]="true"
            [certifInfo]="certification.certifInfo"
            (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"
            (elementIntersection)="writeUrlFromIntersection($event)">
        </app-certification-statistics>

        <app-certification-audit-partner *ngIf="certificationPartner"
                                         [isCertifier]="isCertifier"
                                         [displayedColumns]="['state', 'name', 'date', 'menu']"
                                         [certification]="certification"
                                         [certificationPartner]="certificationPartner"
                                         (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"
                                         (elementIntersection)="writeUrlFromIntersection($event)">
        </app-certification-audit-partner>

        <app-certification-summary [searchByTagAuthorized]="true"
                                   [showFiles]=true
                                   (certificationUpdated)="onCertificationUpdated($event)"
                                   [isDefaultCertifier]="isCertifier"
                                   (filterByCode)="filterCode($event)"
                                   [title]="'private.certification.partners.table.certification' | translate"
                                   [certification]="certification"
                                   (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"
                                   (elementIntersection)="writeUrlFromIntersection($event)">
        </app-certification-summary>

        <ng-container *ngIf="isCertifier">
            <app-certification-manage-partnership *ngIf="subscription.allowCertifierPlus"
                                                  [certification]="certification"
                                                  (certificationUpdated)="onCertificationUpdated($event)"
                                                  (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"
                                                  (elementIntersection)="writeUrlFromIntersection($event)">
            </app-certification-manage-partnership>
            <app-subscription-small-card *ngIf="!subscription.allowCertifierPlus"
                                         [organism]="organism"
                                         [subscription]="subscription"
                                         [shortCutId]="'gestionPartenariatsPromo'"
                                         [fromPage]="'managePartnership'"
                                         [type]="'managePartnership'"
                                         (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"
                                         (elementIntersection)="writeUrlFromIntersection($event)">
            </app-subscription-small-card>
        </ng-container>

        <app-certification-manage-certification-folders
            *ngIf="(certificationPartner && certificationPartner.state === certificationPartnerStates.ACTIVE && certification.certificationFolderFileTypes?.length) || isCertifier"
            [certification]="certification"
            (certificationUpdated)="onCertificationUpdated($event)"
            [isCertifier]="isCertifier"
            (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"
            (elementIntersection)="writeUrlFromIntersection($event)">
        </app-certification-manage-certification-folders>

        <app-certification-skills
            *ngIf="certification.type === certificationTypes.RS || certification.type === certificationTypes.RNCP || certification.type === certificationTypes.INTERNAL"
            [certification]="certification"
            [isCertifier]="isCertifier"
            (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"
            (elementIntersection)="writeUrlFromIntersection($event)">
        </app-certification-skills>

        <app-invoice-card
            *ngIf="certificationPartner && (isCertifier || certificationPartner._links.partner.siret === organism.siret)"
            (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"
            (elementIntersection)="writeUrlFromIntersection($event)"
            [entityClass]="'CertificationPartner'"
            [canPay]="true"
            [canMarkAsPaid]="isCertifier"
            [canMarkAsCanceled]="isCertifier"
            [canMarkAsWaitingPayment]="isCertifier"
            [entity]="certificationPartner"
            [entityId]="certificationPartner.id">
        </app-invoice-card>
    </div>
</aside>
