import {Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges} from '@angular/core';
import {Certification, CertificationTypes} from '../../api/models/certification';
import {finalize, map, switchMap, takeUntil} from 'rxjs/operators';
import {EMPTY, Observable, Subject} from 'rxjs';
import {CertifierAccess} from '../../api/models/certifier-access';
import {Organism} from '../../api/models/organism';
import {CertificationPartner, CertificationPartnerStates} from '../../api/models/certification-partner';
import {CertificationPartnerService} from '../../api/services/certification-partner.service';
import {Subscription} from '../../api/models/subscription';
import {Select} from '@ngxs/store';
import {SubscriptionState} from '../../api/state/subscription.state';
import {ShortcutLoadingSidePanel, ShortcutSidePanel} from '../../utils/interface/shortcut-interfaces';
import {ActivatedRoute, Router} from '@angular/router';
import {Location} from '@angular/common';
import {getAnchor, goToShortCutWhenLoaded} from '../../utils/shortcut-side-panel-utils';
import {ShortcutCatalogCertificationSidePanelEnum} from '../../utils/enums/shortcut-side-panel-enum';
import {CardIntersection} from '../../utils/interface/cards-intersection-interfaces';
import {OrganismService} from '../../api/services/organism.service';
import {CertificationService} from '../../api/services/certification.service';
import {CertificationPartnerAccessCardComponent} from '../../certification/certification-partner-access-card/certification-partner-access-card.component';
import {CertificationPartnerPartnershipCardComponent} from '../../certification/certification-partner-partnership-card/certification-partner-partnership-card.component';
import {CertificationStatisticsComponent} from '../../certification/certification-statistics/certification-statistics.component';
import {CertificationSummaryComponent} from '../../certification/certification-summary/certification-summary.component';
import {CertificationManageCertificationFoldersComponent} from '../../certification/certification-manage-certification-folders/certification-manage-certification-folders.component';
import {InvoiceCardComponent} from '../../invoice/invoice-card/invoice-card.component';
import {CertificationSkillsComponent} from '../../certification/certification-skills/certification-skills.component';
import {CertificationAuditPartnerComponent} from '../../certification/certification-audit-partner/certification-audit-partner.component';
import {CertificationPartnerAuditResults} from '../../api/models/certification-partner-audit';

@Component({
    selector: 'app-certification-catalog-side',
    templateUrl: './certification-catalog-side.component.html',
    styleUrls: ['./certification-catalog-side.component.scss']
})
export class CertificationCatalogSideComponent implements OnInit, OnChanges, OnDestroy {

    constructor(private _organismService: OrganismService,
                private _certificationPartnerService: CertificationPartnerService,
                private _certificationService: CertificationService,
                public activatedRoute: ActivatedRoute,
                private _location: Location,
                private _router: Router) {
    }

    cardLoading = true;
    subscription: Subscription;
    isCertifier = false;
    certificationPartnerStates = CertificationPartnerStates;
    isDataLoaded: ShortcutLoadingSidePanel[] = [];
    shortcuts: ShortcutSidePanel[] = [];
    visibleShortcuts: ShortcutSidePanel[] = [];
    certificationPartner: CertificationPartner;
    certification: Certification;
    isLoading = true;
    isScrolling = false;
    temporaryAnchor: string = null;
    scrollTimeOut: number;
    certificationTypes = CertificationTypes;
    certificationPartnerAuditResults = CertificationPartnerAuditResults;

    private _cardsIntersection: Array<CardIntersection> = [];
    private _unsubscribeAll: Subject<void> = new Subject<void>();
    @Select(SubscriptionState.subscription) subscription$: Observable<Subscription>;

    @Input() partialCertification: Certification;
    @Input() hasNext: boolean;
    @Input() hasPrevious: boolean;
    @Input() certifierAccess: CertifierAccess;
    @Input() certificationPartnerState?: CertificationPartnerStates; // Hack to force refresh if partner created or update from the outside (e.g. the catalog card)
    @Input() partialCertificationPartner?: CertificationPartner;
    @Input() organism?: Organism;
    @Output() previous: EventEmitter<void> = new EventEmitter<void>();
    @Output() next: EventEmitter<void> = new EventEmitter<void>();
    @Output() closeSide: EventEmitter<void> = new EventEmitter<void>();
    @Output() searchTag: EventEmitter<string> = new EventEmitter<string>();
    @Output() certifierAccessUpdated: EventEmitter<CertifierAccess> = new EventEmitter<CertifierAccess>();
    @Output() certificationPartnerUpdated: EventEmitter<CertificationPartner> = new EventEmitter<CertificationPartner>();
    @Output() certificationPartnerDeleted: EventEmitter<CertificationPartner> = new EventEmitter<CertificationPartner>();
    @Output() certificationUpdated: EventEmitter<Certification> = new EventEmitter<Certification>();

    public goToShortCutWhenLoaded = goToShortCutWhenLoaded;

    ngOnInit(): void {
        this.subscription$.pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe((subscription) => {
            this.subscription = subscription;
        });
    }

    askPrevious(): void {
        this.previous.emit();
    }

    askNext(): void {
        this.next.emit();
    }

    askClose(): void {
        this.closeSide.emit();
    }

    filterCode(code): void {
        this.searchTag.emit(code);
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (!changes.partialCertification && (!changes.certificationPartnerState || this.certificationPartner?.state === changes.certificationPartnerState.currentValue)) {
            // Hack to prevent refresh if partner created from the inside
            return;
        }
        this.cardLoading = true;
        this.isCertifier = false;
        this.certification = null;
        this.certificationPartner = null;
        this._certificationService.getByUrl(this.partialCertification._links.self.href).pipe(
            switchMap(certification => {
                this.certification = certification;
                if (this.partialCertificationPartner) {
                    // Get the full certificationPartner to ensure that we have the files
                    return this._certificationPartnerService.getByUrl(this.partialCertificationPartner?._links.self.href).pipe(
                        map(certificationPartner => {
                            this.certificationPartner = certificationPartner;
                            this.isCertifier = this.certificationPartner._links.certifier?.siret === this.organism.siret;
                            return EMPTY;
                        })
                    );
                } else {
                    return this._organismService.listByUrl(this.certification?._links.certifiers.href).pipe(
                        map(certifiers => {
                            this.isCertifier = certifiers.some(certifier => certifier.siret === this.organism.siret);
                            return EMPTY;
                        })
                    );
                }
            }),
            finalize(() => {
                this.cardLoading = false;
                this.updateShortcuts();
            })
        ).subscribe();
    }

    updateShortcuts(): void {
        this.shortcuts = this.defaultShortcut();
        this.visibleShortcuts = this.shortcuts.filter((shortcut: ShortcutSidePanel) => shortcut.visible);
        this.updateDefaultUrl();
    }

    onCertificationPartnerUpdated(certificationPartner: CertificationPartner): void {
        this.certificationPartner = certificationPartner;
        this.certificationPartnerUpdated.emit(certificationPartner);
    }

    onCertificationUpdated(certification: Certification): void {
        this.certification = certification;
        this.certificationUpdated.emit(certification);
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    defaultShortcut(): ShortcutSidePanel[] {
        return [
            {
                target: CertificationPartnerPartnershipCardComponent.COMPONENT_ID,
                title: 'private.common.shortcut.partner',
                icon: 'group',
                visible: !this.isCertifier
            },
            {
                target: CertificationPartnerAccessCardComponent.COMPONENT_ID,
                title: 'private.common.shortcut.certifierAccess',
                icon: 'timelapse',
                visible: this.certifierAccess && !this.isCertifier
            },
            {
                target: CertificationStatisticsComponent.COMPONENT_ID,
                title: 'private.common.shortcut.statistics', icon: 'poll',
                visible: this.isCertifier ||
                    ((this.certificationPartner?.state === this.certificationPartnerStates.ACTIVE
                            || this.certificationPartner?.state === this.certificationPartnerStates.REVOKED
                            || this.certificationPartner?.state === this.certificationPartnerStates.SUSPENDED)
                        && this.certificationPartner?._links?.certifier?.hasOwner)
            },
            {
                target: CertificationSummaryComponent.COMPONENT_ID,
                title: 'private.common.shortcut.certification',
                icon: 'analytics',
                visible: true
            },
            {
                target: this.subscription?.allowCertifierPlus ? 'gestionPartenariats' : 'gestionPartenariatsPromo',
                title: 'private.common.shortcut.partnership', icon: 'group',
                visible: this.isCertifier
            },
            {
                target: CertificationAuditPartnerComponent.COMPONENT_ID,
                title: 'private.common.shortcut.audit',
                icon: 'fact_check',
                visible: this.certificationPartner && !this.isCertifier
            },
            {
                target: CertificationManageCertificationFoldersComponent.COMPONENT_ID,
                title: 'private.common.shortcut.certificationFolders',
                icon: 'library_books',
                visible: !!((this.certificationPartner && this.certificationPartner.state === this.certificationPartnerStates.ACTIVE
                    && this.certification.certificationFolderFileTypes?.length) || this.isCertifier)
            },
            {
                target: CertificationSkillsComponent.COMPONENT_ID,
                title: 'private.common.shortcut.skills',
                icon: 'bookmark_added',
                visible: this.certification.type === this.certificationTypes.RS || this.certification.type === this.certificationTypes.RNCP ||
                    this.certification.type === this.certificationTypes.INTERNAL
            },
            {
                target: InvoiceCardComponent.COMPONENT_ID,
                title: 'private.common.shortcut.invoices',
                icon: 'format_list_bulleted',
                visible: this.certificationPartner && (this.isCertifier || this.certificationPartner._links.partner.siret === this.organism.siret)
            }
        ];
    }

    updateUrlWithAnchor(anchor: string): void {
        const navigateCommands = ['..', this.certification?.certifInfo];
        if (Object.values(ShortcutCatalogCertificationSidePanelEnum)
            .some((shortcut: ShortcutCatalogCertificationSidePanelEnum): boolean => shortcut === anchor)) {
            navigateCommands.push(anchor);
            this._router.navigate(navigateCommands, {
                replaceUrl: true,
                relativeTo: this.activatedRoute,
                queryParamsHandling: 'preserve'
            });
        }
    }

    updateDefaultUrl(): void {
        const anchor: string = getAnchor(this._location.path());
        const navigateCommands = ['..', this.certification?.certifInfo];
        if (!Object.values(ShortcutCatalogCertificationSidePanelEnum)
            .some((shortcut: ShortcutCatalogCertificationSidePanelEnum): boolean => shortcut === anchor)) {
            navigateCommands.push(this.visibleShortcuts.filter((shortcut) => shortcut.visible === true)[0].target);
            this._router.navigate(navigateCommands, {
                replaceUrl: true,
                relativeTo: this.activatedRoute,
                queryParamsHandling: 'preserve'
            });
        }
    }

    waitingScroll(scrollInfo: boolean, target = null): void {
        this.isScrolling = scrollInfo;
        this.temporaryAnchor = target;
        if (this.isScrolling === false) {
            this.scrollEndListener();
        }
    }

    scrollEndListener(): void {
        this.isScrolling = false;
        if (this.temporaryAnchor) {
            this.updateUrlWithAnchor(this.temporaryAnchor);
            this.temporaryAnchor = null;
        } else {
            this.isLoading = false;
        }

    }

    detectScroll(): void {
        if (this.scrollTimeOut) {
            clearTimeout(this.scrollTimeOut);
        }
        this.scrollTimeOut = setTimeout(() => {
            this.scrollEndListener();
        }, 300);
    }

    writeUrlFromIntersection(event): void {
        if (event.visible === true && !this.isScrolling && !this.isLoading) {
            if (this._cardsIntersection.some((innerEvent) => innerEvent.name === event.name)) {
                const indexOfEvent = this._cardsIntersection.findIndex((elem) => elem.name === event.name);
                this._cardsIntersection[indexOfEvent] = event;

            } else {
                this._cardsIntersection.push(event);
            }
            const visibleElement = this._cardsIntersection.reduce((highest, elem) => {
                return highest.ratio > elem.ratio ? highest : elem;
            });
            this.updateUrlWithAnchor(visibleElement.name);
        } else {
            if (this._cardsIntersection.some((innerEvent) => innerEvent.name === event.name)) {
                const indexOfEvent = this._cardsIntersection.findIndex((elem) => elem.name === event.name);
                event.ratio = 0;
                this._cardsIntersection[indexOfEvent] = event;
            }
        }
    }
}
