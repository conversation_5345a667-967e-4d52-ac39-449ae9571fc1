<mat-card class="mat-card flex h-full flex-auto flex-col mt-3 p-5 pb-0 pt-3 border shadow-none sm"
          [ngClass]="{'card-loading':cardLoading}"
          *ngIf="shouldShowCard">
    <div class="flex items-center mb-2">
        <mat-icon
            [matBadge]="total > 0 ? total.toString() : null"
            matBadgePosition="below after"
            matBadgeSize="small"
            class="mr-3 card-loading-show text-4xl"
            color="primary">play_arrow
        </mat-icon>
        <div class="flex items-center">
            <span
                class="text-xl font-semibold card-loading-show">{{ 'private.application.workflow-runs.title' | translate }}</span>
        </div>
    </div>

    <div *ngIf="!cardLoading" class="flex flex-col mt-3 mb-3 overflow-y-auto">
        <div *ngIf="errorMessages.length > 0" class="mb-2">
            <div *ngFor="let errorMessage of errorMessages" class="text-red-500 text-sm">
                {{ errorMessage }}
            </div>
        </div>

        <div *ngIf="flowRuns?.length; else noResult">
            <div *ngFor="let flowRun of flowRuns" class="mb-2 cursor-pointer" (click)="openFlowRun(flowRun)">
                <div class="flex flex-row justify-between">
                    <div class="flex items-center">
                        <mat-icon *ngIf="flowRun.status === 'FAILED'"
                                  [matTooltip]="('private.application.workflow-runs.status.label' | translate) + ('private.application.workflow-runs.status.' + flowRun.status | translate)"
                                  class="icon-size-18"
                                  color="warn">error
                        </mat-icon>
                        <mat-icon *ngIf="flowRun.status === 'SUCCEEDED'"
                                  [matTooltip]="('private.application.workflow-runs.status.label' | translate) + ('private.application.workflow-runs.status.' + flowRun.status | translate)"
                                  class="icon-size-18 text-green">check_circle
                        </mat-icon>
                        <mat-icon *ngIf="flowRun.status === 'RUNNING'"
                                  [matTooltip]="('private.application.workflow-runs.status.label' | translate) + ('private.application.workflow-runs.status.' + flowRun.status | translate)"
                                  class="icon-size-18 text-blue">play_circle
                        </mat-icon>
                        <mat-icon *ngIf="flowRun.status === 'PAUSED' || flowRun.status === 'STOPPED'"
                                  [matTooltip]="('private.application.workflow-runs.status.label' | translate) + ('private.application.workflow-runs.status.' + flowRun.status | translate)"
                                  class="icon-size-18 text-orange">pause_circle
                        </mat-icon>
                        <div class="pl-1">
                            <div class="flex">
                                <p class="font-medium">{{ flowRun.flowDisplayName }}</p>
                                <span class="ml-2 text-disabled" [matTooltip]="flowRun.id">{{ flowRun.id.slice(0, 7) }}...</span>
                                <span *ngIf="flowRun.source === 'activepieces'" class="ml-2 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">activepieces</span>
                            </div>
                            <span class="text-disabled">{{ flowRun.tasks }} {{ 'private.application.workflow-runs.tasks' | translate }} - {{ flowRun.duration }}{{ 'private.application.workflow-runs.duration' | translate }}</span>
                            <br/>
                            <span class="text-disabled"
                                  [matTooltip]="flowRun.startTime | date:'short'">{{ daysAgo(flowRun.startTime) }}</span>
                        </div>
                    </div>

                    <button mat-icon-button (click)="openFlowRun(flowRun); $event.stopPropagation();" [disabled]="cardLoading"
                            type="button">
                        <mat-icon color="primary">open_in_new</mat-icon>
                    </button>
                </div>
            </div>
        </div>

        <ng-template #noResult>
            <p class="mt-3 mb-3 text-center m-auto">
                {{ 'private.application.workflow-runs.noData' | translate }}
            </p>
        </ng-template>
    </div>
</mat-card>
