import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {MaterialModule} from '../material/material.module';
import {AttendeeModule} from '../attendee/attendee.module';
import {RegistrationFolderModule} from '../registration-folder/registration-folder.module';
import {CertificationFolderModule} from '../certification-folder/certification-folder.module';
import {CertificationFolderSideComponent} from './certification-folder-side/certification-folder-side.component';
import {CertificationSideComponent} from './certification-side/certification-side.component';
import {OrganismModule} from '../organism/organism.module';
import {RegistrationFolderSideComponent} from './registration-folder-side/registration-folder-side.component';
import {CertificationPartnerSideComponent} from './certification-partner-side/certification-partner-side.component';
import {RegistrationFolderCreateSideComponent} from './registration-folder-create-side/registration-folder-create-side.component';
import {CertificationModule} from '../certification/certification.module';
import {ProposalSideComponent} from './proposal-side/proposal-side.component';
import {ProposalCreateSideComponent} from './proposal-create-side/proposal-create-side.component';
import {MatTooltipModule} from '@angular/material/tooltip';
import {MatChipsModule} from '@angular/material/chips';
import {NgxMatSelectSearchModule} from 'ngx-mat-select-search';
import {ReactiveFormsModule} from '@angular/forms';
import {MatCardModule} from '@angular/material/card';
import {ProposalModule} from '../proposal/proposal.module';
import {PipesModule} from '../pipes/pipes.module';
import {CertificationFolderCreateSideComponent} from './certification-folder-create-side/certification-folder-create-side.component';
import {ActivitiesModule} from '../activities/activities.module';
import {CertificationCatalogSideComponent} from './certification-catalog-side/certification-catalog-side.component';
import {FileModule} from '../file/file.module';
import {TranslateModule} from '@ngx-translate/core';
import {SubscriptionModule} from '../subscription/subscription.module';
import {InvoicesModule} from '../invoice/invoices.module';
import {CertificationFolderSurveyModule} from '../certification-folder-survey/certification-folder-survey.module';
import {MetadataModule} from '../metadata/metadata.module';
import {WorkingContractModule} from '../working-contract/working-contract.module';
import {CertificationCreateSideComponent} from './certification-create-side/certification-create-side.component';
@NgModule({
    declarations: [
        CertificationSideComponent,
        RegistrationFolderSideComponent,
        CertificationFolderSideComponent,
        CertificationPartnerSideComponent,
        RegistrationFolderCreateSideComponent,
        ProposalSideComponent,
        ProposalCreateSideComponent,
        CertificationFolderCreateSideComponent,
        CertificationCatalogSideComponent,
        CertificationCreateSideComponent
    ],
    imports: [
        CommonModule,
        TranslateModule,
        MaterialModule,
        AttendeeModule,
        CertificationModule,
        RegistrationFolderModule,
        CertificationFolderModule,
        ActivitiesModule,
        OrganismModule,
        CertificationModule,
        MatTooltipModule,
        MatChipsModule,
        NgxMatSelectSearchModule,
        ReactiveFormsModule,
        MatCardModule,
        ProposalModule,
        PipesModule,
        FileModule,
        SubscriptionModule,
        InvoicesModule,
        CertificationFolderSurveyModule,
        MetadataModule,
        WorkingContractModule
    ],
    exports: [
        CertificationSideComponent,
        RegistrationFolderSideComponent,
        CertificationFolderSideComponent,
        CertificationPartnerSideComponent,
        RegistrationFolderCreateSideComponent,
        ProposalSideComponent,
        ProposalCreateSideComponent,
        CertificationFolderCreateSideComponent,
        CertificationCatalogSideComponent,
        CertificationCreateSideComponent
    ]
})
export class SidesModule {
}
