import {Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges} from '@angular/core';
import {Certification, CertificationTypes} from '../../api/models/certification';
import {Organism} from '../../api/models/organism';
import {OrganismService} from '../../api/services/organism.service';
import {CertificationPartner, CertificationPartnerStates} from '../../api/models/certification-partner';
import {combineLatest, Observable, Subject} from 'rxjs';
import {takeUntil} from 'rxjs/operators';
import {CertifierAccessService} from '../../api/services/certifier-access.service';
import {CertifierAccess} from '../../api/models/certifier-access';
import {Select} from '@ngxs/store';
import {SubscriptionState} from '../../api/state/subscription.state';
import {Subscription} from '../../api/models/subscription';
import {OrganismState} from '../../api/state/organism.state';
import {ActivatedRoute, Router} from '@angular/router';
import {Location} from '@angular/common';
import {ShortcutLoadingSidePanel, ShortcutSidePanel} from '../../utils/interface/shortcut-interfaces';
import {getAnchor, goToShortCutWhenLoaded} from '../../utils/shortcut-side-panel-utils';
import {ShortcutPartnerSidePanelEnum} from '../../utils/enums/shortcut-side-panel-enum';
import {CardIntersection} from '../../utils/interface/cards-intersection-interfaces';
import {InvoiceCardComponent} from '../../invoice/invoice-card/invoice-card.component';
import {OrganismCardComponent} from '../../organism/organism-card/organism-card.component';
import {CertificationCertifierPartnershipCardComponent} from '../../certification/certification-certifier-partnership-card/certification-certifier-partnership-card.component';
import {CertificationStatisticsComponent} from '../../certification/certification-statistics/certification-statistics.component';
import {ApplicationsService} from '../../../applications/shared/applications.service';
import {CertificationAuditPartnerComponent} from '../../certification/certification-audit-partner/certification-audit-partner.component';
import {CertificationPartnerTrainingsComponent} from '../../certification/certification-partner-trainings/certification-partner-trainings.component';
import {CertificationPartnerAuditResults} from '../../api/models/certification-partner-audit';
import {CertificationCertifierAccessCardComponent} from '../../certification/certification-certifier-access-card/certification-certifier-access-card.component';
import {EntityClass} from '../../utils/enums/entity-class';
import {MetadataCardComponent} from '../../metadata/metadata-card/metadata-card.component';
import {CityService} from '../../api/services/city.service';
import {City} from '../../api/models/attendee';
import {SkillService} from '../../api/services/skill.service';
import {Skill, SkillType} from '../../api/models/skill';

@Component({
    selector: 'app-certification-partner-side',
    templateUrl: './certification-partner-side.component.html',
    styleUrls: ['./certification-partner-side.component.scss']
})
export class CertificationPartnerSideComponent implements OnInit, OnChanges, OnDestroy {

    constructor(private _organismService: OrganismService,
                public applicationsService: ApplicationsService,
                private _cityService: CityService,
                private _skillService: SkillService,
                private _certifierAccessService: CertifierAccessService,
                public activatedRoute: ActivatedRoute,
                private _location: Location,
                private _router: Router) {
        this.applicationsService.getOrganismApplicationEntryPoints('card-certification-partner-side').subscribe(organismApplicationEntryPoints => {
            this.organismApplicationEntryPointDelivery = [...organismApplicationEntryPoints];
        });
    }

    partner: Organism = null;
    certifierAccess: CertifierAccess;
    subscription: Subscription;
    certificationPartnerStates = CertificationPartnerStates;
    certificationPartnerAuditResults = CertificationPartnerAuditResults;
    isDataLoaded: ShortcutLoadingSidePanel[] = [];
    shortcuts: ShortcutSidePanel[] = [];
    visibleShortcuts: ShortcutSidePanel[] = [];
    isLoading = true;
    isScrolling = false;
    temporaryAnchor: string = null;
    scrollTimeOut: number;
    organismApplicationEntryPointDelivery: Array<any> = [];
    organismApplicationEntryPointCards = [];
    certificationTypes = CertificationTypes;
    public goToShortCutWhenLoaded = goToShortCutWhenLoaded;
    trainingsZone?: City[] = null;
    skillSets?: Skill[] = null;

    @Input() certification: Certification = null;
    @Input() certificationPartner: CertificationPartner = null;
    @Input() hasNext: boolean;
    @Input() organism: Organism;
    @Input() hasPrevious: boolean;

    @Output() certificationPartnerChange: EventEmitter<CertificationPartner> = new EventEmitter<CertificationPartner>();
    @Output() next: EventEmitter<void> = new EventEmitter<void>();
    @Output() closeSide: EventEmitter<void> = new EventEmitter<void>();
    @Output() previous: EventEmitter<void> = new EventEmitter<void>();
    @Output() certificationPartnerRefreshed: EventEmitter<CertificationPartner> = new EventEmitter<CertificationPartner>();
    @Output() certificationUpdated: EventEmitter<Certification> = new EventEmitter<Certification>();

    @Select(SubscriptionState.subscription) subscription$: Observable<Subscription>;
    @Select(OrganismState.organism) organism$: Observable<Organism>;

    private _arrayElemIntersection: Array<CardIntersection> = [];
    private _unsubscribeAll: Subject<any> = new Subject();
    readonly EntityClass = EntityClass;

    ngOnInit(): void {
        combineLatest([
            this.subscription$
        ]).pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe(([subscription]) => {
            this.subscription = subscription;
        });
    }

    askNext(): void {
        this.next.emit();
    }

    askClose(): void {
        this.closeSide.emit();
    }

    askPrevious(): void {
        this.previous.emit();
    }

    ngOnChanges(changes: SimpleChanges): void {
        this.partner = null;
        this.certifierAccess = null;
        this.trainingsZone = null;
        this.skillSets = null;
        const partnerSiret = this.certificationPartner._links.partner.siret;
        this.applicationsService.getOrganismApplicationEntryPoints('card-certification-partner-side').subscribe(organismApplicationEntryPoints => {
            this.shortcuts = this.defaultShortcuts();
            this.organismApplicationEntryPointCards = [];
            if (this.isCertifierForCertificationPartner(this.certificationPartner)) {
                organismApplicationEntryPoints.forEach(organismApplicationEntryPointCard => {
                    this.organismApplicationEntryPointCards.push(organismApplicationEntryPointCard);
                    this.shortcuts.push(organismApplicationEntryPointCard.params.shortcut);
                });
            }
            this.visibleShortcuts = this.shortcuts.filter((shortcut: ShortcutSidePanel) => shortcut.visible ?? true);
            const anchor: string = getAnchor(this._location.path());
            const navigateCommands = ['..', this.certification.certifInfo, partnerSiret];
            if (!Object.values(ShortcutPartnerSidePanelEnum).some((shortcut: ShortcutPartnerSidePanelEnum): boolean => shortcut === anchor)) {
                navigateCommands.push(this.visibleShortcuts[0].target);
                this._router.navigate(navigateCommands, {
                    replaceUrl: true,
                    relativeTo: this.activatedRoute,
                    queryParamsHandling: 'preserve'
                });
            }
        });
        if (partnerSiret) {
            this._organismService.get(partnerSiret).pipe(
                takeUntil(this._unsubscribeAll)
            ).subscribe((organism) => {
                this.partner = organism;
            });
        }
        if (this.isCertifierForCertificationPartner(this.certificationPartner)) {
            this._certifierAccessService.find({
                siret: this.certificationPartner?._links.certifier?.siret,
// Hack with query as there is no endpoint to get a certifierAccess form the 2 organisms & the request with only the certifier may have more results than the page size
                query: partnerSiret,
                accessType: 'certifier',
                certifInfo: this.certification.certifInfo,
            }).pipe(
                takeUntil(this._unsubscribeAll)
            ).subscribe((response) => {
                if (response.payload.length && response.payload[0]._links.partner.siret === partnerSiret) {
                    this.certifierAccess = response.payload[0];
                }
            });
            if (this.certificationPartner.trainingsZone) {
                this._cityService.getByPostalCodes(this.certificationPartner.trainingsZone.toString()).subscribe((cities) => {
                    this.trainingsZone = cities;
                });
            }
            if (this.certification.allowPartialSkillSets) {
                this._skillService.listByUrl(this.certification._links.skills.href).subscribe(
                    (skills) => this.skillSets = skills.filter((skill) => skill.type === SkillType.SKILL_SET)
                );
            }
        }
    }

    _certifierAccessRefreshed(certifierAccess: CertifierAccess): void {
        this.certifierAccess = certifierAccess;
        this.certificationPartner.certifierAccessState = certifierAccess.state;
        this.certificationPartnerRefreshed.emit(this.certificationPartner);
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    updateCertificationPartner(certificationPartner: CertificationPartner): void {
        this.certificationPartner = certificationPartner;
        this.certificationPartnerChange.emit(certificationPartner);
    }

    updateUrlWithAnchor(anchor: string): void {
        const navigateCommands = ['..', this.certification.certifInfo, this.certificationPartner._links.partner.siret];
        if (Object.values(ShortcutPartnerSidePanelEnum).some((shortcut: ShortcutPartnerSidePanelEnum): boolean => shortcut === anchor)) {
            navigateCommands.push(anchor);
            this._router.navigate(navigateCommands, {
                replaceUrl: true,
                relativeTo: this.activatedRoute,
                queryParamsHandling: 'preserve'
            });
        }
    }

    waitingScroll(scrollInfo: boolean, target = null): void {
        this.isScrolling = scrollInfo;
        this.temporaryAnchor = target;
        if (this.isScrolling === false) {
            this.scrollEndListener();
        }
    }

    scrollEndListener(): void {
        this.isScrolling = false;
        if (this.temporaryAnchor) {
            this.updateUrlWithAnchor(this.temporaryAnchor);
            this.temporaryAnchor = null;
        } else {
            this.isLoading = false;
        }

    }

    detectScroll(): void {
        if (this.scrollTimeOut) {
            clearTimeout(this.scrollTimeOut);
        }
        this.scrollTimeOut = setTimeout(() => {
            this.scrollEndListener();
        }, 300);
    }

    writeUrlFromIntersection(event): void {
        if (event.visible === true && !this.isScrolling && !this.isLoading) {
            if (this._arrayElemIntersection.some((innerEvent) => innerEvent.name === event.name)) {
                const indexOfEvent = this._arrayElemIntersection.findIndex((elem) => elem.name === event.name);
                this._arrayElemIntersection[indexOfEvent] = event;

            } else {
                this._arrayElemIntersection.push(event);
            }
            const visibleElement = this._arrayElemIntersection.reduce((highest, elem) => {
                return highest.ratio > elem.ratio ? highest : elem;
            });
            this.updateUrlWithAnchor(visibleElement.name);
        } else {
            if (this._arrayElemIntersection.some((innerEvent) => innerEvent.name === event.name)) {
                const indexOfEvent = this._arrayElemIntersection.findIndex((elem) => elem.name === event.name);
                event.ratio = 0;
                this._arrayElemIntersection[indexOfEvent] = event;
            }
        }
    }

    isCertifierForCertificationPartner(certificationPartner: CertificationPartner): boolean {
        return certificationPartner?._links?.certifier?.siret === this.organism?.siret;
    }

    defaultShortcuts(): ShortcutSidePanel[] {
        return [
            {
                target: OrganismCardComponent.COMPONENT_ID,
                title: 'private.common.shortcut.organism',
                icon: 'business',
                visible: true
            },
            {
                target: CertificationCertifierPartnershipCardComponent.COMPONENT_ID,
                title: 'private.common.shortcut.partner',
                icon: 'group',
                visible: true
            },
            {
                target: CertificationCertifierAccessCardComponent.COMPONENT_ID,
                title: 'private.common.shortcut.certifierAccess',
                icon: 'timelapse',
                visible: this.isCertifierForCertificationPartner(this.certificationPartner)
            },
            {
                target: this.subscription?.allowCertifierPlus ? CertificationAuditPartnerComponent.COMPONENT_ID : 'manageAuditPromo',
                title: 'private.common.shortcut.audit',
                icon: 'fact_check',
                visible: this.isCertifierForCertificationPartner(this.certificationPartner)
            },
            {
                target: CertificationStatisticsComponent.COMPONENT_ID,
                title: 'private.common.shortcut.statistics',
                icon: 'poll',
                visible: this.isCertifierForCertificationPartner(this.certificationPartner)
                    && (this.certificationPartner.state === this.certificationPartnerStates.ACTIVE
                        || this.certificationPartner.state === this.certificationPartnerStates.REVOKED
                        || this.certificationPartner.state === this.certificationPartnerStates.SUSPENDED)
            },
            {
                target: this.subscription?.allowCertifierPlus ? CertificationPartnerTrainingsComponent.COMPONENT_ID : 'formationsPromo',
                title: 'private.common.shortcut.trainings',
                icon: 'ballot',
                visible: this.certification.type !== this.certificationTypes.INTERNAL && this.isCertifierForCertificationPartner(this.certificationPartner)
                    && (this.certificationPartner.state === this.certificationPartnerStates.ACTIVE
                        || this.certificationPartner.state === this.certificationPartnerStates.REVOKED
                        || this.certificationPartner.state === this.certificationPartnerStates.SUSPENDED)
            },
            {
                target: InvoiceCardComponent.COMPONENT_ID,
                title: 'private.common.shortcut.invoices',
                icon: 'format_list_bulleted',
                visible: this.isCertifierForCertificationPartner(this.certificationPartner)
            },
            {
                target: 'activites',
                title: 'private.common.shortcut.activities',
                icon: 'format_list_bulleted',
                visible: this.isCertifierForCertificationPartner(this.certificationPartner)
            },
            {
                target: MetadataCardComponent.COMPONENT_ID,
                title: 'private.common.shortcut.metadatas',
                icon: 'dataset',
                visible: this.isCertifierForCertificationPartner(this.certificationPartner)
            }
        ];
    }
}

