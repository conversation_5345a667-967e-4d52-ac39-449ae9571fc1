<header class="border-b">
    <mat-toolbar>
        <mat-toolbar-row>
            <span class="truncate flex items-center"
                  title="{{ certificationPartner?._links?.partner?.name }}">
                  <mat-icon class="mr-1"
                            [matTooltip]="'private.certification.audit.result.' + certificationPartner.compliance | translate"
                            [class]="certificationPartner.compliance === certificationPartnerAuditResults.COMPLIANT ? 'text-green' :
                                     certificationPartner.compliance === certificationPartnerAuditResults.PARTIALLY_COMPLIANT ? 'text-orange' : ''"
                            [color]="certificationPartner.compliance === certificationPartnerAuditResults.NON_COMPLIANT ? 'warn' : undefined">
                      {{ certificationPartner.compliance | certificationPartnerAuditResultToIcon }}
                  </mat-icon>
                <span>{{ certificationPartner?._links?.partner?.name }}</span>
            </span>
            <span class="flex-grow"></span>
            <button mat-icon-button (click)="askPrevious()" [disabled]="!hasPrevious">
                <mat-icon svgIcon="chevron_left"></mat-icon>
            </button>
            <button mat-icon-button (click)="askNext()" [disabled]="!hasNext">
                <mat-icon svgIcon="chevron_right"></mat-icon>
            </button>
            <a mat-icon-button (click)="askClose()">
                <mat-icon svgIcon="close"></mat-icon>
            </a>
        </mat-toolbar-row>
    </mat-toolbar>
    <div class="flex flex-row flex-wrap gap-1 p-4 pt-0">
        <app-shortcut *ngFor="let shortcut of visibleShortcuts"
                      [target]="shortcut.target"
                      [title]="shortcut.title"
                      [icon]="shortcut.icon"
                      (eventScrolling)="waitingScroll($event,shortcut.target)"
        ></app-shortcut>
    </div>
</header>
<aside (scroll)="detectScroll()" class="flex flex-grow overflow-auto p-3 pt-0 light:bg-default">
    <app-organism-card (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"
                       (elementIntersection)="writeUrlFromIntersection($event)"
                       [organism]="partner">
    </app-organism-card>

    <app-certification-certifier-partnership-card
        [isCertifier]="isCertifierForCertificationPartner(certificationPartner)"
        (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"
        [certification]="certification"
        [trainingsZones]="trainingsZone"
        [skillSets]="skillSets"
        [certificationPartner]="certificationPartner"
        (certificationPartnerChange)="updateCertificationPartner($event)"
        (elementIntersection)="writeUrlFromIntersection($event)">
    </app-certification-certifier-partnership-card>

    <app-certification-certifier-access-card [partner]="partner"
                                             *ngIf="isCertifierForCertificationPartner(certificationPartner)"
                                             (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"
                                             [certification]="certification"
                                             [certifierAccess]="certifierAccess"
                                             [certificationPartner]="certificationPartner"
                                             (certifierAccessChange)="_certifierAccessRefreshed($event)"
                                             (elementIntersection)="writeUrlFromIntersection($event)">
    </app-certification-certifier-access-card>

    <app-certification-audit-partner
        *ngIf="isCertifierForCertificationPartner(certificationPartner) && subscription?.allowCertifierPlus"
        [certification]="certification"
        [displayedColumns]="['state', 'name', 'date', 'criteriasNumber', 'menu']"
        [certificationPartner]="certificationPartner"
        (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"
        (certificationUpdated)="certificationUpdated.emit($event)"
        (certificationPartnerChange)="updateCertificationPartner($event)"
        (elementIntersection)="writeUrlFromIntersection($event)">
    </app-certification-audit-partner>
    <app-subscription-small-card
        *ngIf="isCertifierForCertificationPartner(certificationPartner) && !subscription?.allowCertifierPlus"
        [organism]="organism"
        [subscription]="subscription"
        [shortCutId]="'manageAuditPromo'"
        [fromPage]="'manageAuditPromo'"
        [type]="'manageAudit'"
        (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"
    >
    </app-subscription-small-card>

    <app-certification-statistics
        *ngIf="isCertifierForCertificationPartner(certificationPartner) && (certificationPartner.state === certificationPartnerStates.ACTIVE || certificationPartner.state === certificationPartnerStates.REVOKED || certificationPartner.state === certificationPartnerStates.SUSPENDED)"
        (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"
        (elementIntersection)="writeUrlFromIntersection($event)"
        [showPartner]="true"
        [siret]="certificationPartner._links.partner.siret"
        [certifInfo]="certificationPartner._links.certification.certifInfo"
        [cardTitle]="certificationPartner._links.certification.externalId + ' - ' + certificationPartner._links.certification.name">
    </app-certification-statistics>

    <ng-container
        *ngIf="certification.type !== certificationTypes.INTERNAL && isCertifierForCertificationPartner(certificationPartner) && (certificationPartner.state === certificationPartnerStates.ACTIVE || certificationPartner.state === certificationPartnerStates.REVOKED || certificationPartner.state === certificationPartnerStates.SUSPENDED)">
        <app-certification-partner-trainings *ngIf="subscription?.allowCertifierPlus"
                                             (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"
                                             (elementIntersection)="writeUrlFromIntersection($event)"
                                             [certifierAccess]="certifierAccess"
                                             [certificationPartner]="certificationPartner"
                                             [certification]="certification"></app-certification-partner-trainings>
        <app-subscription-small-card *ngIf="!subscription?.allowCertifierPlus"
                                     (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"
                                     (elementIntersection)="writeUrlFromIntersection($event)"
                                     [organism]="organism"
                                     [shortCutId]="'formationsPromo'"
                                     [subscription]="subscription"
                                     [fromPage]="'displayTrainingAndTrainingActions'"
                                     [type]="'partnerStatistics'">
        </app-subscription-small-card>
    </ng-container>

    <app-invoice-card *ngIf="isCertifierForCertificationPartner(certificationPartner)"
                      (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"
                      (elementIntersection)="writeUrlFromIntersection($event)"
                      [entityClass]="'CertificationPartner'"
                      [canPay]="true"
                      [canMarkAsPaid]="isCertifierForCertificationPartner(certificationPartner)"
                      [canMarkAsCanceled]="isCertifierForCertificationPartner(certificationPartner)"
                      [canMarkAsWaitingPayment]="isCertifierForCertificationPartner(certificationPartner)"
                      [entity]="certificationPartner"
                      [entityId]="certificationPartner.id">
    </app-invoice-card>

    <app-activities-card class="max-h-80 mb-3"
                         *ngIf="isCertifierForCertificationPartner(certificationPartner)"
                         (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"
                         (elementIntersection)="writeUrlFromIntersection($event)"
                         [entityClass]="'CertificationPartner'"
                         [entity]="certificationPartner"
                         [entityId]="certificationPartner.id"></app-activities-card>

    <app-metadata-card class="mb-3"
                       *ngIf="isCertifierForCertificationPartner(certificationPartner)"
                       [entityClass]="EntityClass.CERTIFICATIONS_PARTNER"
                       [showInSide]="true"
                       [certifInfo]="certificationPartner._links.certification.certifInfo"
                       [organismSiret]="certificationPartner._links.partner.siret"
                       (entityChange)="updateCertificationPartner($event)"
                       [entity]="certificationPartner"
                       (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"
                       (elementIntersection)="writeUrlFromIntersection($event)"></app-metadata-card>

    <ng-container *ngIf="isCertifierForCertificationPartner(certificationPartner)">
        <ng-container *ngFor="let context of organismApplicationEntryPointDelivery">
            <ng-container *ngTemplateOutlet="applicationsService.getTemplate(context.params.tpl);
                    context: { context: context, certificationPartner : certificationPartner,
                    activatedRoute: activatedRoute, goToShortCutWhenLoaded: goToShortCutWhenLoaded,
                    isDataLoaded: isDataLoaded, elementIntersection: writeUrlFromIntersection }"></ng-container>
        </ng-container>
    </ng-container>
</aside>
