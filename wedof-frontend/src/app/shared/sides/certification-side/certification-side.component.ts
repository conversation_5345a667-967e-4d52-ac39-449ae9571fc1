import {Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges} from '@angular/core';
import {Certification, CertificationTypes} from '../../api/models/certification';
import {combineLatest, Observable, Subject} from 'rxjs';
import {Organism} from '../../api/models/organism';
import {Subscription} from '../../api/models/subscription';
import {CertificationFolderStates} from '../../api/models/certification-folder';
import {TranslateService} from '@ngx-translate/core';
import {CertificationFolderStateToIconPipe} from '../../pipes/certification-folder-state-to-icon.pipe';
import {CertificationFolderStateToColorPipe} from '../../pipes/certification-folder-state-to-color.pipe';
import {StateChoices} from '../../file/file-type-list/file-type-list.component';
import {Select} from '@ngxs/store';
import {SubscriptionState} from '../../api/state/subscription.state';
import {takeUntil} from 'rxjs/operators';
import {OrganismState} from '../../api/state/organism.state';
import {UserState} from '../../api/state/user.state';
import {User} from '../../api/models/user';
import {ApplicationsService} from '../../../applications/shared/applications.service';
import {ShortcutLoadingSidePanel, ShortcutSidePanel} from '../../utils/interface/shortcut-interfaces';
import {Location} from '@angular/common';
import {ActivatedRoute, Router} from '@angular/router';
import {getAnchor, goToShortCutWhenLoaded} from '../../utils/shortcut-side-panel-utils';
import {ShortcutCertificationSidePanelEnum} from '../../utils/enums/shortcut-side-panel-enum';
import {CertificationService} from '../../api/services/certification.service';
import {OrganismService} from '../../api/services/organism.service';
import {CertificationAccrochageComponent} from '../../certification/certification-accrochage/certification-accrochage.component';
import {CertificationSummaryComponent} from '../../certification/certification-summary/certification-summary.component';
import {CertificationManageCertificationFoldersComponent} from '../../certification/certification-manage-certification-folders/certification-manage-certification-folders.component';
import {CertificationSkillsComponent} from '../../certification/certification-skills/certification-skills.component';
import {CertificationAuditTemplateComponent} from '../../certification/certification-audit-template/certification-audit-template.component';
import {CertificationSurveyComponent} from '../../certification/certification-survey/certification-survey.component';
import {CertificationManagePartnershipComponent} from '../../certification/certification-manage-partnership/certification-manage-partnership.component';
import {CertificationCertificateTemplateComponent} from '../../certification/certification-certificate-template/certification-certificate-template.component';
import {CertificationStatisticsComponent} from '../../certification/certification-statistics/certification-statistics.component';

@Component({
    selector: 'app-certification-side',
    templateUrl: './certification-side.component.html',
    styleUrls: ['./certification-side.component.scss']
})
export class CertificationSideComponent implements OnInit, OnChanges, OnDestroy {

    user: User;
    organism: Organism;
    subscription: Subscription;
    isDefaultCertifier = false;
    organismApplicationEntryPointCards: Array<any> = [];
    stateChoicesCertificationFoldersFileTypes: StateChoices;
    isDataLoaded: ShortcutLoadingSidePanel[] = [];
    shortcuts: ShortcutSidePanel[] = [];
    visibleShortcuts: ShortcutSidePanel[] = [];
    isLoading: boolean;
    isScrolling: boolean;
    temporaryAnchor: string;
    scrollTimeOut: number;
    certificationTypes = CertificationTypes;

    @Select(SubscriptionState.subscription) subscription$: Observable<Subscription>;
    @Select(OrganismState.organism) organism$: Observable<Organism>;
    @Select(UserState.user) user$: Observable<User>;

    @Input() certification: Certification;
    @Input() hasNext: boolean;
    @Input() hasPrevious: boolean;
    @Output() next: EventEmitter<void> = new EventEmitter<void>();
    @Output() closeSide: EventEmitter<void> = new EventEmitter<void>();
    @Output() previous: EventEmitter<void> = new EventEmitter<void>();
    @Output() certificationUpdated: EventEmitter<Certification> = new EventEmitter<Certification>();

    private _arrayElemIntersection: Array<any> = [];
    private _unsubscribeAll: Subject<void> = new Subject<void>();

    public goToShortCutWhenLoaded = goToShortCutWhenLoaded;


    constructor(private _translateService: TranslateService,
                public _applicationsService: ApplicationsService,
                public _certificationService: CertificationService,
                public _organismService: OrganismService,
                public activatedRoute: ActivatedRoute,
                private _certificationFolderStateToIconPipe: CertificationFolderStateToIconPipe,
                private _certificationFolderStateToColorPipe: CertificationFolderStateToColorPipe,
                private _location: Location,
                private _router: Router
    ) {
    }

    ngOnInit(): void {
        combineLatest([
            this.organism$,
            this.subscription$,
            this.user$
        ]).pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe(([organism, subscription, user]) => {
            this.organism = organism;
            this.subscription = subscription;
            this.user = user;
        });
        this.stateChoicesCertificationFoldersFileTypes = Object.values(CertificationFolderStates).map(value => ({
            key: this._translateService.instant('private.common.certificationFolder.state.' + value),
            icon: this._certificationFolderStateToIconPipe.transform(value as string),
            color: this._certificationFolderStateToColorPipe.transform(value as string),
            value: value
        }));
    }

    ngOnChanges(changes: SimpleChanges): void {
        this._organismService.listByUrl(this.certification._links.certifiers.href).subscribe(certifiers => {
            // c'est pas default du coup
            this.isDefaultCertifier = certifiers.find(certifier => certifier.siret === this.organism.siret) !== undefined;
            this._applicationsService.getOrganismApplicationEntryPoints('card-certification-side').subscribe(organismApplicationEntryPoints => {
                this.shortcuts = this.defaultShortcuts();
                this.organismApplicationEntryPointCards = [];
                organismApplicationEntryPoints.forEach(organismApplicationEntryPointCard => {
                    this.organismApplicationEntryPointCards.push(organismApplicationEntryPointCard);
                    this.shortcuts.push(organismApplicationEntryPointCard.params.shortcut);
                });
                this.visibleShortcuts = this.shortcuts.filter((shortcut: ShortcutSidePanel) => shortcut.visible);
                const anchor: string = getAnchor(this._location.path());
                const navigateCommands = ['..', this.certification.certifInfo];
                if (!Object.values(ShortcutCertificationSidePanelEnum)
                    .some((shortcut: ShortcutCertificationSidePanelEnum): boolean => shortcut === anchor)) {
                    navigateCommands.push(this.visibleShortcuts[0].target);
                    this._router.navigate(navigateCommands, {
                        replaceUrl: true,
                        relativeTo: this.activatedRoute,
                        queryParamsHandling: 'preserve'
                    });
                }
            });
        });
    }

    defaultShortcuts(): ShortcutSidePanel[] {
        return [
            {
                target: CertificationSummaryComponent.COMPONENT_ID,
                title: 'private.common.shortcut.certification',
                icon: 'analytics',
                visible: true
            },
            {
                target: CertificationSkillsComponent.COMPONENT_ID,
                title: 'private.common.shortcut.skills',
                icon: 'bookmark_added',
                visible: this.certification.type === this.certificationTypes.RS || this.certification.type === this.certificationTypes.RNCP ||
                    this.certification.type === this.certificationTypes.INTERNAL
            },
            {
                target: CertificationStatisticsComponent.COMPONENT_ID,
                title: 'private.common.shortcut.statistics',
                icon: 'poll',
                visible: this.isDefaultCertifier
            },
            {
                target: this.subscription.allowCertifierPlus ? CertificationManagePartnershipComponent.COMPONENT_ID : 'gestionPartenariatsPromo',
                title: 'private.common.shortcut.partnership',
                icon: 'timelapse',
                visible: this.isDefaultCertifier
            },
            {
                target: CertificationManageCertificationFoldersComponent.COMPONENT_ID,
                title: 'private.common.shortcut.certificationFolders',
                icon: 'library_books', visible: true
            },
            {
                target: CertificationAccrochageComponent.COMPONENT_ID,
                title: 'private.common.shortcut.xml',
                icon: 'verified_user',
                visible: this.isDefaultCertifier && this.subscription.allowCertifiers && this.certification.type !== this.certificationTypes.INTERNAL
            },
            {
                target: CertificationSurveyComponent.COMPONENT_ID,
                title: 'private.common.shortcut.surveys',
                icon: 'view_timeline',
                visible: this.isDefaultCertifier
            },
            {
                target: this.subscription.allowCertifierPlus ? CertificationAuditTemplateComponent.COMPONENT_ID : 'modelesAuditPromo',
                title: 'private.common.shortcut.audit',
                icon: 'fact_check',
                visible: this.isDefaultCertifier && this.subscription.allowCertifiers
            },
            {
                target: this.subscription.allowCertifierPlus ? CertificationCertificateTemplateComponent.COMPONENT_ID : 'generationParcheminPromo',
                title: 'private.common.shortcut.certificateTemplate',
                icon: 'school',
                visible: this.isDefaultCertifier
            },
        ];
    }

    askNext(): void {
        this.next.emit();
    }

    askClose(): void {
        this.closeSide.emit();
    }

    askPrevious(): void {
        this.previous.emit();
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    updateUrlWithAnchor(anchor: string): void {
        const navigateCommands = ['..', this.certification.certifInfo];
        if (Object.values(ShortcutCertificationSidePanelEnum).some((shortcut: ShortcutCertificationSidePanelEnum): boolean => shortcut === anchor)) {
            navigateCommands.push(anchor);
            this._router.navigate(navigateCommands, {
                replaceUrl: true,
                relativeTo: this.activatedRoute,
                queryParamsHandling: 'preserve'
            });
        }
    }

    waitingScroll(scrollInfo: boolean, target = null): void {
        this.isScrolling = scrollInfo;
        this.temporaryAnchor = target;
        if (this.isScrolling === false) {
            this.scrollEndListener();
        }
    }

    scrollEndListener(): void {
        this.isScrolling = false;
        if (this.temporaryAnchor) {
            this.updateUrlWithAnchor(this.temporaryAnchor);
            this.temporaryAnchor = null;
        }
    }

    detectScroll(): void {
        if (this.scrollTimeOut) {
            clearTimeout(this.scrollTimeOut);
        }
        this.scrollTimeOut = setTimeout(() => {
            this.scrollEndListener();
        }, 300);
    }


    writeUrlFromIntersection(event): void {
        if (event.visible === true && !this.isScrolling && !this.isLoading) {
            if (this._arrayElemIntersection.some((innerEvent) => innerEvent.name === event.name)) {
                const indexOfEvent = this._arrayElemIntersection.findIndex((elem) => elem.name === event.name);
                this._arrayElemIntersection[indexOfEvent] = event;

            } else {
                this._arrayElemIntersection.push(event);
            }
            const visibleElement = this._arrayElemIntersection.reduce((highest, elem) => {
                return highest.ratio > elem.ratio ? highest : elem;
            });
            this.updateUrlWithAnchor(visibleElement.name);
        } else {
            if (this._arrayElemIntersection.some((innerEvent) => innerEvent.name === event.name)) {
                const indexOfEvent = this._arrayElemIntersection.findIndex((elem) => elem.name === event.name);
                event.ratio = 0;
                this._arrayElemIntersection[indexOfEvent] = event;
            }
        }
    }
}

