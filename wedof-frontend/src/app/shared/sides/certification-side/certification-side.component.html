<header class="border-b">
    <mat-toolbar>
        <mat-toolbar-row>
            <span class="truncate"
                  title="{{ certification.externalId}} - {{certification.name}} {{
                !certification.enabled ? ' - ' + ('private.certification.partners.enabled.' + certification.enabled | translate) : ''}}">
                {{ certification.externalId }} - {{ certification.name }}  {{
                !certification.enabled ? ' - ' + ('private.certification.partners.enabled.' + certification.enabled | translate) : ''}}</span>
            <span class="flex-grow"></span>
            <button mat-icon-button (click)="askPrevious()" [disabled]="!hasPrevious">
                <mat-icon svgIcon="chevron_left"></mat-icon>
            </button>
            <button mat-icon-button (click)="askNext()" [disabled]="!hasNext">
                <mat-icon svgIcon="chevron_right"></mat-icon>
            </button>
            <a mat-icon-button (click)="askClose()">
                <mat-icon svgIcon="close"></mat-icon>
            </a>
        </mat-toolbar-row>
    </mat-toolbar>
    <div class="flex flex-row flex-wrap gap-1 p-4 pt-0">
        <app-shortcut *ngFor="let shortcut of visibleShortcuts"
                      [target]="shortcut.target"
                      [title]="shortcut.title"
                      [icon]="shortcut.icon"
                      (eventScrolling)="waitingScroll($event, shortcut.target)"
        ></app-shortcut>

    </div>
</header>
<aside (scroll)="detectScroll()" class="flex flex-grow overflow-auto p-3 pt-0 light:bg-default">
    <app-certification-summary [certification]="certification"
                               [showFiles]="isDefaultCertifier && subscription.allowCertifierPlus"
                               [isDefaultCertifier]="isDefaultCertifier"
                               (certificationUpdated)="certificationUpdated.emit($event)"
                               (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"
                               (elementIntersection)="writeUrlFromIntersection($event)"
    ></app-certification-summary>

    <app-certification-skills
        *ngIf="certification.type === certificationTypes.RS || certification.type === certificationTypes.RNCP || certification.type === certificationTypes.INTERNAL"
        [certification]="certification"
        [isCertifier]="isDefaultCertifier"
        (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"
        (elementIntersection)="writeUrlFromIntersection($event)"
    >
    </app-certification-skills>

    <app-certification-statistics *ngIf="isDefaultCertifier"
                                  [certifInfo]="certification.certifInfo"
                                  [cardTitle]="'private.training-organism.certifications.statistiques.titleCertification' | translate"
                                  [showPartner]="false"
                                  (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"
                                  (elementIntersection)="writeUrlFromIntersection($event)">
    </app-certification-statistics>

    <ng-container *ngIf="isDefaultCertifier">
        <app-certification-manage-partnership *ngIf="subscription.allowCertifierPlus"
                                              (certificationUpdated)="certificationUpdated.emit($event)"
                                              [certification]="certification"
                                              (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"
                                              (elementIntersection)="writeUrlFromIntersection($event)">
        </app-certification-manage-partnership>
        <app-subscription-small-card *ngIf="!subscription.allowCertifierPlus"
                                     [organism]="organism"
                                     [subscription]="subscription"
                                     [shortCutId]="'gestionPartenariatsPromo'"
                                     [fromPage]="'managePartnership'"
                                     [type]="'managePartnership'"
                                     (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"
                                     (elementIntersection)="writeUrlFromIntersection($event)"
        ></app-subscription-small-card>
    </ng-container>

    <app-certification-manage-certification-folders [certification]="certification"
                                                    [isCertifier]="isDefaultCertifier"
                                                    (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"
                                                    (elementIntersection)="writeUrlFromIntersection($event)"
    >
    </app-certification-manage-certification-folders>

    <app-certification-accrochage *ngIf="isDefaultCertifier && subscription.allowCertifiers && certification.type !== certificationTypes.INTERNAL"
        [organism]="organism"
        [subscription]="subscription"
        [isDefaultCertifier]="isDefaultCertifier"
        (certificationUpdated)="certificationUpdated.emit($event)"
        [certification]="certification"
        (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"
        (elementIntersection)="writeUrlFromIntersection($event)">
    </app-certification-accrochage>

    <app-certification-survey *ngIf="isDefaultCertifier"
                              [certification]="certification"
                              [organism]="organism"
                              [subscription]="subscription"
                              (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"
                              (elementIntersection)="writeUrlFromIntersection($event)">
    </app-certification-survey>
    <ng-container *ngIf="isDefaultCertifier">
        <app-certification-audit-template *ngIf="subscription?.allowCertifierPlus"
                                          [certification]="certification"
                                          [subscription]="subscription"
                                          (certificationUpdated)="certificationUpdated.emit($event)"
                                          (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"
                                          (elementIntersection)="writeUrlFromIntersection($event)">
        </app-certification-audit-template>
        <app-subscription-small-card *ngIf="!subscription.allowCertifierPlus"
                                     [organism]="organism"
                                     [subscription]="subscription"
                                     [shortCutId]="'modelesAuditPromo'"
                                     [fromPage]="'manageAudit'"
                                     [type]="'manageAudit'"
                                     (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"
        >
        </app-subscription-small-card>

        <app-certification-certificate-template *ngIf="subscription.allowCertifierPlus"
                                                (certificationUpdated)="certificationUpdated.emit($event)"
                                                [certification]="certification"
                                                (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"
                                                (elementIntersection)="writeUrlFromIntersection($event)">
        </app-certification-certificate-template>
        <app-subscription-small-card *ngIf="!subscription.allowCertifierPlus"
                                     [organism]="organism"
                                     [subscription]="subscription"
                                     [shortCutId]="'generationParcheminPromo'"
                                     [fromPage]="'generateCertificate'"
                                     [type]="'generateCertificate'"
                                     (shortcutSidePanelLoaded)="goToShortCutWhenLoaded($event, activatedRoute, isDataLoaded)"
        >
        </app-subscription-small-card>

        <ng-container *ngFor="let context of organismApplicationEntryPointCards">
            <ng-container *ngTemplateOutlet="_applicationsService.getTemplate(context.params.tpl);
                    context: { context: context, certification : certification,
                    activatedRoute: activatedRoute, goToShortCutWhenLoaded: goToShortCutWhenLoaded,
                    isDataLoaded: isDataLoaded, elementIntersection: writeUrlFromIntersection}"></ng-container>
        </ng-container>
    </ng-container>

</aside>
