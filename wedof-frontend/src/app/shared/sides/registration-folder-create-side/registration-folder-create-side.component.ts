import {Component, EventEmitter, Output, ViewChild} from '@angular/core';
import {RegistrationFolder} from '../../api/models/registration-folder';
import {CanDeactivateComponent} from '../../utils/can-deactivate/can-deactivate.component';
import {RegistrationFolderCreateComponent} from '../../registration-folder/registration-folder-create/registration-folder-create.component';

@Component({
    selector: 'app-registration-folder-create-side',
    templateUrl: './registration-folder-create-side.component.html',
    styleUrls: ['./registration-folder-create-side.component.scss']
})
export class RegistrationFolderCreateSideComponent extends CanDeactivateComponent {

    @ViewChild('registrationFolderCreate') private registrationFolderCreate: RegistrationFolderCreateComponent;

    @Output() closeSide: EventEmitter<void> = new EventEmitter<void>();
    @Output() registrationFolderCreated: EventEmitter<RegistrationFolder> = new EventEmitter<RegistrationFolder>();

    askClose(): void {
        this.closeSide.emit();
    }

    _registrationFolderCreated($event: RegistrationFolder): void {
        this.registrationFolderCreated.emit($event);
    }

    canDeactivate(): boolean {
        return this.registrationFolderCreate.canDeactivate();
    }
}

