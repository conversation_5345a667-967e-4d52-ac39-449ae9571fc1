<app-dialog-layout [title]="'auth.cdc.dialogAccrochage.title' | translate"
                   [actions]="actions"
                   [disabled]="loading" (dialogClose)="close()">

    <p class="font-semibold">{{dialogData.cdcFile.name}}.xml</p>

    <form [formGroup]="formGroup" class="flex flex-col mt-3" >
        <div>
            <mat-label>{{'auth.cdc.dialogAccrochage.form.file.sendFile' | translate}}</mat-label>
            <input type="file" id="file" accept="text/xml" class="ml-2" (change)="onFileChange($event)"/>
            <mat-error *ngIf="hasErrorTypeFile" class="pb-1">
                {{'auth.cdc.dialogAccrochage.form.file.errorFile' | translate}}
            </mat-error>
        </div>

        <mat-form-field class="mt-3 flex-auto">
            <mat-label>{{'auth.cdc.dialogAccrochage.form.submissionDate.label' | translate}}</mat-label>
            <input formControlName="submissionDate" matInput required
                   [matDatepicker]="submissionDateDatePicker"
                   [min]="dialogData.cdcFile.createdOn"
                   [max]="today">
            <mat-datepicker-toggle matSuffix [for]="submissionDateDatePicker"></mat-datepicker-toggle>
            <mat-datepicker #submissionDateDatePicker></mat-datepicker>
            <mat-error>
                {{'common.errors.required' | translate }}
            </mat-error>
        </mat-form-field>

        <div *ngIf="errorMessages?.length" class="flex items-center">
            <treo-message class="flex-auto" appearance="outline" [showIcon]="false" type="error">
                <ul>
                    <li *ngFor="let errorMessage of errorMessages">
                        {{ errorMessage }}
                    </li>
                </ul>
            </treo-message>
        </div>

        <ng-template #actions>
            <div class="flex flex-row py-2 mb-2">
                <button type="submit" color="primary" class="mt-2"
                        mat-flat-button (click)="submit()" [disabled]="loading || hasErrorTypeFile">
                    <mat-progress-spinner class="mr-4" *ngIf="loading" [diameter]="24"
                                          mode="indeterminate"></mat-progress-spinner>
                    <ng-container
                        *ngIf="!loading">{{ 'common.actions.submit' | translate }}</ng-container>
                </button>
            </div>
        </ng-template>

    </form>

</app-dialog-layout>
