import {Component, Inject, OnInit} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material/dialog';
import {FileService} from '../../api/services/file.service';
import {MatSnackBar} from '@angular/material/snack-bar';
import {SnackBarComponent} from '../../material/snack-bar/snack-bar.component';
import {displayTextSnackBar} from '../../utils/displayTextSnackBar';
import {HttpErrorResponse, HttpResponse} from '@angular/common/http';
import {FormBuilder, FormGroup} from '@angular/forms';
import moment, {Moment} from 'moment';
import {CdcFile} from '../../api/models/certification-folder';
import {CdcFileService} from '../../api/services/cdc-file.service';
import {finalize} from 'rxjs/operators';
import {ApiError} from '../../errors/errors.types';
import {TranslateService} from '@ngx-translate/core';

@Component({
    selector: 'app-accrochage-dialog',
    templateUrl: './accrochage-dialog.component.html',
    styleUrls: ['./accrochage-dialog.component.scss']
})
export class AccrochageDialogComponent implements OnInit {

    loading = false;
    fileToUpload: File | null = null;
    formGroup: FormGroup;
    today: Moment;
    errorMessages: string[] = [];
    hasErrorTypeFile = false;

    constructor(
        private _snackBar: MatSnackBar,
        private _fileService: FileService,
        private _formBuilder: FormBuilder,
        private _translateService: TranslateService,
        private _cdcFileService: CdcFileService,
        @Inject(MAT_DIALOG_DATA) public dialogData: {
            cdcFile: CdcFile
        },
        public _dialogRef: MatDialogRef<AccrochageDialogComponent>,
    ) {
    }

    ngOnInit(): void {
        this.today = moment();
        this.formGroup = this._formBuilder.group({
            submissionDate: [this.dialogData.cdcFile.createdOn]
        });
    }

    close(): void {
        this._dialogRef.close();
    }

    onFileChange(event): void {
        if (event.target.files.length > 0) {
            this.fileToUpload = event.target.files[0];
            this.hasErrorTypeFile = this.fileToUpload.type !== 'text/xml';
        }
    }

    submit(): void {
        this.errorMessages = [];
        this.loading = true;
        const submissionDate = this.formGroup.get('submissionDate').value;
        const cdcFile: CdcFile = {
            ...this.dialogData.cdcFile, ...{
                submissionDate: submissionDate
            }
        };
        const formData: FormData = new FormData();
        formData.append('fileKey', this.fileToUpload, this.fileToUpload.name);
        this._fileService.upload('cdcFile', cdcFile.id, this.fileToUpload).subscribe(
            (event) => {
                if (event instanceof HttpResponse && event?.status < 300) {
                    this._cdcFileService.update(cdcFile).pipe(
                        finalize(() => this.loading = false)
                    ).subscribe(
                        (updatedCdcFile) => {
                            this.dialogData.cdcFile = updatedCdcFile;
                            this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(this._translateService.instant('common.actions.cdcFileUpdatedSuccessfully')));
                            this._dialogRef.close({data: updatedCdcFile});
                        },
                        (httpErrorResponse: HttpErrorResponse) => {
                            this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
                        }
                    );
                }
            },
            (httpErrorResponse: HttpErrorResponse) => {
                this.loading = false;
                this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
            }
        );


    }

}
