import {Component, EventEmitter, Input, Output, ViewChild} from '@angular/core';
import {MatMenuTrigger} from '@angular/material/menu';
import {CdcFile} from '../../api/models/certification-folder';
import {CdcFileService} from '../../api/services/cdc-file.service';
import {DeletionConfirmationComponent} from '../../material/action-confirmation/deletion-confirmation.component';
import {filter, finalize, switchMap, tap} from 'rxjs/operators';
import {MatDialog} from '@angular/material/dialog';

@Component({
    selector: 'app-cdc-file-menu',
    templateUrl: './cdc-file-menu.component.html',
    styleUrls: ['./cdc-file-menu.component.scss']
})
export class CdcFileMenuComponent {

    loading = false;

    @Input() cdcFile: CdcFile;
    @Output() processedCdcFile: EventEmitter<CdcFile> = new EventEmitter<CdcFile>();
    @ViewChild('menuTrigger') menuTrigger: MatMenuTrigger;

    constructor(private _cdcFileService: CdcFileService, private _dialog: MatDialog) {
    }

    initialize(): void {
        if (this.menuTrigger) {
            this.menuTrigger.openMenu();
        }
    }

    abort(): void {
        const dialogRef = this._dialog.open(DeletionConfirmationComponent, {
            panelClass: 'full-page-scroll-40',
            height: 'auto',
            data: {
                messageKey: 'auth.cdc.dialogAccrochage.menu.confirmAbort',
                data: this.cdcFile
            }
        });

        dialogRef.componentInstance.actionValue$.pipe(
            filter(confirmation => confirmation),
            tap(() => this.loading = true),
            switchMap(() => this._cdcFileService.abort(this.cdcFile.id)),
            finalize(() => {
                dialogRef.componentInstance.close();
                this.loading = false;
            })
        ).subscribe((cdcFileAborted) => {
            this.processedCdcFile.emit(cdcFileAborted);
        });

    }


}
