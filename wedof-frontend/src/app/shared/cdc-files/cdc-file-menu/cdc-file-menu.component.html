<div class="flex align-center">
    <button
        type="button" mat-icon-button
        (click)="initialize()"
        title="Actions">
        <mat-icon [svgIcon]="'more_vert'"></mat-icon>
    </button>
    <button
        type="button" #menuTrigger="matMenuTrigger"
        [matMenuTriggerFor]="actionsMenu">
    </button>
</div>


<mat-menu #actionsMenu="matMenu">
    <ng-template matMenuContent>
        <button
            type="button" mat-menu-item (click)="abort()">
            <mat-icon  color="warn" svgIcon="close"></mat-icon>
            <span>{{'auth.cdc.dialogAccrochage.menu.abort' | translate}}</span>
        </button>
    </ng-template>
</mat-menu>

