import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {TranslateModule} from '@ngx-translate/core';
import {MaterialModule} from '../material/material.module';
import {ReactiveFormsModule} from '@angular/forms';
import {MatTooltipModule} from '@angular/material/tooltip';
import {NgxMatSelectSearchModule} from 'ngx-mat-select-search';
import {MatCardModule} from '@angular/material/card';
import {ClipboardModule} from 'ngx-clipboard';
import {AccrochageDialogComponent} from './accrochage-dialog/accrochage-dialog.component';
import {CdcFileMenuComponent} from './cdc-file-menu/cdc-file-menu.component';

@NgModule({
    declarations: [AccrochageDialogComponent, CdcFileMenuComponent],
    imports: [
        CommonModule,
        TranslateModule,
        MaterialModule,
        ReactiveFormsModule,
        MatTooltipModule,
        NgxMatSelectSearchModule,
        MatCardModule,
        ClipboardModule,
    ],
    exports: [
        AccrochageDialogComponent,
        CdcFileMenuComponent
    ]
})
export class CdcFileModule {
}
