import {Component, EventEmitter, Injector, Input, OnInit, Output, ViewChild} from '@angular/core';
import {DataQuery} from '../../material/table/abstract-table.component';
import {AbstractSelectableTableComponent} from '../../material/table/abstract-selectable-table.component';
import {DiscountType, Proposal, ProposalStates} from '../../api/models/proposal';
import {ProposalTableColumns} from './proposal-table-params';
import {Observable} from 'rxjs';
import {
    TableFilter,
    TableMultipleFilterComponent
} from '../../material/table/table-multiple-filter/table-multiple-filter.component';
import {ProposalStateToIconPipe} from '../../pipes/proposal-state-to-icon.pipe';
import {ProposalStateToColorPipe} from '../../pipes/proposal-state-to-color.pipe';
import {takeUntil, tap} from 'rxjs/operators';
import {PaginatedResponse} from '../../api/services/abstract-paginated.service';
import {ProposalService} from '../../api/services/proposal.service';

export interface ProposalFilters {
    query: string;
}

export interface ProposalQuery extends DataQuery {
    code: string;
}

@Component({
    selector: 'app-proposal-table',
    templateUrl: './proposal-table.component.html',
    styleUrls: ['./proposal-table.component.scss']
})
export class ProposalTableComponent extends AbstractSelectableTableComponent<Proposal> implements OnInit {
    @Input() displayedColumns: ProposalTableColumns[];
    @Input() proposalFilters$: Observable<ProposalFilters>;
    @Output() proposalDeleted: EventEmitter<any> = new EventEmitter<any>();
    @Output() newProposalFilters: EventEmitter<ProposalFilters> = new EventEmitter<ProposalFilters>();
    @ViewChild('stateFilterSelector') private stateTableMultipleFilterComponentState: TableMultipleFilterComponent;

    trainingAction: boolean;
    viewMore: {} = {};
    stateFilters: TableFilter[];
    states = ProposalStates;
    DiscountType = DiscountType;
    pendingSelectAll: boolean;

    constructor(
        injector: Injector,
        private _proposalService: ProposalService,
        private _proposalStateToIconPipe: ProposalStateToIconPipe,
        private _proposalStateToColorPipe: ProposalStateToColorPipe
    ) {
        super(injector);
        this.trainingAction = false;
        this.stateFilters = Object.values(this.states).map(value => ({
            label: `private.training-organism.proposals.table.states.${value}`,
            value: value,
            icon: this._proposalStateToIconPipe.transform(value),
            color: this._proposalStateToColorPipe.transform(value),
        }));
    }

    ngOnInit(): void {
        this.proposalFilters$.pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe(filters => {
            this.applyFilter({name: 'query', value: filters.query});
            this._changeDetectorRef.detectChanges();
        });
    }

    onStateFilterChange(accessStates: string[] | string): void {
        this.applyFilter({name: 'state', value: accessStates});
    }

    isEnded(proposal: Proposal): boolean {
        return !proposal.isIndividual && !this.isExpired(proposal) && proposal.limitUsage === proposal.usedCount;
    }

    isExpired(proposal: Proposal): boolean {
        return (proposal.expire && new Date(proposal.expire).getTime() < new Date().getTime());
    }

    protected findRowWithDataQuery(row: Proposal, query: ProposalQuery): boolean {
        return row.code === query.code;
    }

    deleteProposal($event: Proposal): void {
        this.proposalDeleted.emit($event);
        this.refreshData();
    }

    searchTag(tag: string): void {
        this.newProposalFilters.emit({query: tag});
    }

    selectAllForState(stateValue?: string): void {
        this.onStateFilterChange(stateValue);
        this.stateTableMultipleFilterComponentState.updateCurrentFiltersData(stateValue === undefined ? [] : [stateValue]);
        this.pendingSelectAll = true; // We cannot subscribe to the refresh from here, so we record that we want to select All
    }

    protected refreshData(): Observable<PaginatedResponse<Proposal>> {
        this.clearSelection();
        return this._proposalService.list({
            ...this._filters$.value,
            order: this.sort?.direction,
            sort: this.sort?.active,
            limit: this.paginator.pageSize,
            page: this.paginator.pageIndex + 1,
        }).pipe(tap(response => {
            if (this.pendingSelectAll === true) {
                setTimeout(() => { // Doesn't work if no timeout
                    this.selectAll();
                    this.pendingSelectAll = false;
                });
            }
            response.payload.forEach(proposal => {
                this.viewMore[proposal.code] = false;
            });
        }));
    }

}
