<ng-template #noData>
    <p class="bg-white p-6 cursor-auto text-center m-auto">
        {{ 'private.training-organism.proposals.noData' | translate }}
    </p>
</ng-template>

<app-wrapper-spinner [active]="isLoading">
    <ng-container *ngIf="isLoading || displayedData?.length || activeFilters ; else noData">
        <table class="table-fixed" mat-table [dataSource]="displayedData" matSort matSortActive="stateLastUpdate"
               matSortDisableClear
               multiTemplateDataRows
               matSortDirection="desc">
            <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
            <tr *matRowDef="let row; let rowIndex = dataIndex; columns:['selectAll'];"
                [hidden]="rowIndex > 0 || total <= paginator.pageSize || !isAllSelected()"
                mat-row></tr>
            <tr mat-row (click)="onRowClick(row)"
                *matRowDef="let row; columns: displayedColumns;"
                [ngClass]="{'selected':selectedRow?.code === row?.code}"></tr>
            <tr mat-footer-row *matFooterRowDef="['noDataForFilters']"
                [hidden]="isLoading || displayedData?.length">
            </tr>

            <ng-container matColumnDef="selectAll">
                <td *matCellDef="let element" [attr.colspan]="displayedColumns.length" class="text-center" mat-cell>
                    <div *ngIf="!isTotalRowsSelected">
                        <span
                            class="text-warn font-semibold">{{ 'common.table.selection.current' | translate:{currentSelectionCount: currentSelectionCount()} }}</span>
                        <button (click)="selectAllTotalLine(); $event.stopPropagation()"
                                class="font-semibold pr-2 underline"
                                mat-button>
                            {{ 'common.table.selection.selectAll' | translate:{total: total} }}
                        </button>
                    </div>
                    <div *ngIf="isTotalRowsSelected">
                        {{ 'common.table.selection.currentAll' | translate:{currentSelectionCount: currentSelectionCount()} }}
                        <button mat-button class="font-semibold pr-2 underline"
                                (click)="toggleAll(); $event.stopPropagation()">
                            {{ 'common.table.selection.deselectAll' | translate }}
                        </button>
                    </div>
                </td>
            </ng-container>

            <ng-container matColumnDef="noDataForFilters">
                <td mat-footer-cell *matFooterCellDef [attr.colspan]="displayedColumns.length">
                    {{ 'common.table.filters.no-data' | translate }}
                </td>
            </ng-container>

            <ng-container matColumnDef="actions">
                <th class="text-left w-26 pl-4 border-t" mat-header-cell *matHeaderCellDef>
                    <div class="flex justify-between pr-2">
                        <div class="flex flex-row items-center cursor-pointer">
                            <mat-checkbox class="self-center checkbox-dropdown"
                                          (change)="$event ? toggleAll() : null"
                                          [checked]="(selection.hasValue() && isAllSelected()) || isTotalRowsSelected"
                                          [indeterminate]="selection.hasValue() && !isAllSelected()"
                                          [aria-label]="checkboxAllLabelKey() | translate">
                            </mat-checkbox>
                            <mat-icon class="icon-small" svgIcon="arrow_drop_down"
                                      [matMenuTriggerFor]="actionsMenu"></mat-icon>
                        </div>
                        <mat-menu #actionsMenu="matMenu" class="large-menu">
                            <button class="mat-menu-item"
                                    (click)="selectAllForState()">
                                {{ 'common.table.selection.all.select' | translate }}
                            </button>
                            <button *ngFor="let stateFilter of stateFilters" class="mat-menu-item"
                                    (click)="selectAllForState(stateFilter.value)">
                                <mat-icon class="ml-1"
                                          matPrefix
                                          [color]="stateFilter.color">{{ stateFilter.icon }}
                                </mat-icon>
                                {{ stateFilter.label | translate }}
                            </button>
                        </mat-menu>
                        <app-proposal-menu
                            [proposals]="selection.selected"
                            (deletedProposal)="deleteProposal($event)"
                            (processedProposal)="refreshRow($event)"
                        >
                        </app-proposal-menu>
                    </div>
                </th>
                <td class="text-left w-26 pl-4" mat-cell *matCellDef="let row; let proposal;">
                    <div class="flex flex-direction:row">
                        <mat-checkbox (change)="$event ? toggleRow(row) : null" (click)="$event.stopPropagation()"
                                      class="self-center"
                                      [checked]="isRowSelected(row)" [disabled]="!isSelectable(row)"
                                      [aria-label]="checkboxRowLabelKey(row) | translate:{ name: row.code }">
                        </mat-checkbox>
                        <app-proposal-menu
                            class="ml-4"
                            (deletedProposal)="deleteProposal($event)"
                            [proposals]="[proposal]"
                            (processedProposal)="refreshRow($event)">
                        </app-proposal-menu>
                    </div>
                </td>
            </ng-container>

            <ng-container matColumnDef="code">
                <th *matHeaderCellDef class="border-t" mat-header-cell>
                    {{ 'private.training-organism.proposals.table.proposal.title' | translate }}
                </th>
                <td *matCellDef="let proposal" mat-cell>
                    <div *ngIf="proposal.email !=null; else noNamesNoEmail">
                                        <span class="font-semibold"
                                              *ngIf="proposal.firstName && proposal.lastName; else noNames">{{ proposal.firstName | titlecase }} {{ proposal.lastName | uppercase }}</span>
                        <ng-template #noNames>
                            <span class="font-semibold"> {{ proposal.email | titlecase }}</span>
                        </ng-template>
                    </div>
                    <ng-template #noNamesNoEmail>
                        {{ 'private.training-organism.proposals.table.proposal.generic' | translate }}
                    </ng-template>
                    <div *ngIf="proposal.email != null else noLink">
                        <a href="{{proposal.link_commercial}}" class="text-secondary"
                           *ngIf="proposal.codeRequired"
                           target="_blank">{{ proposal.code }}</a>
                        <div class="text-secondary"
                             *ngIf="!proposal.codeRequired">{{ 'private.training-organism.proposals.proposalCard.codeRequired.value'|translate }}
                        </div>
                    </div>
                    <ng-template #noLink>
                        <div class="text-secondary" *ngIf="proposal.codeRequired">{{ proposal.code }}</div>
                        <div class="text-secondary"
                             *ngIf="!proposal.codeRequired">{{ 'private.training-organism.proposals.proposalCard.codeRequired.value'|translate }}
                        </div>
                    </ng-template>
                </td>
            </ng-container>

            <ng-container matColumnDef="stateLastUpdate">
                <th *matHeaderCellDef class="border-t" mat-header-cell mat-sort-header>
                    <app-table-multiple-filter #stateFilterSelector
                                               [filters]="stateFilters"
                                               [title]="'état'"
                                               (selectFilters)="onStateFilterChange($event)">
                        {{ 'private.training-organism.proposals.table.states.title' | translate }}
                    </app-table-multiple-filter>
                </th>
                <td *matCellDef="let proposal" mat-cell>
                    <ng-container *ngIf="proposal.isIndividual else genericProposal">
                        <div>
                            {{ 'private.training-organism.proposals.table.states.' + proposal.state | translate }}
                            <a *ngIf="proposal._links['registrationFolder']"
                               [routerLink]="['/formation/dossiers/liste/' + proposal._links['registrationFolder']['href'].split('registrationFolder/')[1]]"
                               href="/formation/dossiers/liste/{{ proposal._links['registrationFolder']['href'].split('registrationFolder/')[1]}}">
                                ({{ proposal._links['registrationFolder']['href'].split('registrationFolder/')[1] }})
                            </a>
                        </div>
                    </ng-container>
                    <ng-template #genericProposal>
                        <div>
                            <span
                                *ngIf="isExpired(proposal)">{{ 'private.training-organism.proposals.table.states.expired' | translate }}</span>
                            <span
                                *ngIf="!isExpired(proposal) && isEnded(proposal)">{{ 'private.training-organism.proposals.table.states.ended' | translate }}</span>
                            <span
                                *ngIf="!isExpired(proposal) && !isEnded(proposal)">{{ 'private.training-organism.proposals.table.states.' + (proposal.state === states.TEMPLATE ? 'active' : proposal.state) | translate }}</span>
                            ({{ proposal.usedCount }}
                            / {{ proposal.limitUsage != null && proposal.limitUsage != 0 ? proposal.limitUsage : '∞' }})
                        </div>
                    </ng-template>
                    <span class="text-secondary"
                          title="{{ 'private.training-organism.proposals.table.proposal.stateLastUpdate' | translate}}">{{ proposal.stateLastUpdate | date: 'dd/MM/yy' }}</span>
                </td>
            </ng-container>

            <ng-container matColumnDef="trainingActions">
                <th *matHeaderCellDef class="border-t" mat-header-cell>
                    {{ 'private.training-organism.proposals.table.trainingActions.title' | translate }}
                </th>
                <td *matCellDef="let proposal;" mat-cell>
                    <ng-container
                        *ngIf="proposal.trainingActions.length > 0 else noTrainingActions;">
                        <span>{{ proposal.trainingActions[0].trainingTitle }}</span>
                        <ng-container *ngIf="proposal.trainingActions.length > 1;">
                            &nbsp;<a (click)="viewMore[proposal.code] = true;"
                                     *ngIf="!viewMore[proposal.code]">et {{ this.proposal.trainingActions.length - 1 }}
                            de plus </a>
                            <span *ngIf="viewMore[proposal.code]">
                                                <span
                                                    *ngFor="let trainingAction of proposal.trainingActions; let isLast = last;">{{ trainingAction.trainingTitle }}
                                                    <span *ngIf="!isLast">,</span></span>
                                                &nbsp;<a (click)="viewMore[proposal.code] = false;"
                                                         *ngIf="viewMore[proposal.code]">voir moins</a>
                                            </span>
                        </ng-container>
                    </ng-container>
                    <ng-template #noTrainingActions>
                        <p>{{ 'private.training-organism.proposals.table.trainingActions.noTrainingActions' | translate }}</p>
                    </ng-template>
                </td>
            </ng-container>

            <ng-container matColumnDef="conditions">
                <th *matHeaderCellDef class="border-t" mat-header-cell>
                    {{ 'private.training-organism.proposals.table.conditions.title' | translate }}
                </th>
                <td *matCellDef="let proposal" mat-cell>
                    <div>
                        <p *ngIf="proposal.discountType === DiscountType.DISCOUNT_TYPE_NONE">{{ 'private.training-organism.proposals.table.conditions.none' | translate }}</p>
                        <p *ngIf="proposal.discountType === DiscountType.DISCOUNT_TYPE_PRICE">{{ 'private.training-organism.proposals.table.conditions.fixed' | translate : proposal }}</p>
                        <p *ngIf="proposal.discountType === DiscountType.DISCOUNT_TYPE_PERCENT">{{ (proposal.amount > 0 ? 'private.training-organism.proposals.table.conditions.percentPlus' : 'private.training-organism.proposals.table.conditions.percentMinus') | translate : proposal }}</p>
                        <p *ngIf="proposal.discountType === DiscountType.DISCOUNT_TYPE_AMOUNT">{{ (proposal.amount > 0 ? 'private.training-organism.proposals.table.conditions.amountPlus' : 'private.training-organism.proposals.table.conditions.amountMinus') | translate : proposal }}</p>

                        <p *ngIf="proposal.sessionStartDate !=null && proposal.sessionEndDate != null"> {{ 'private.training-organism.proposals.table.sessionDates.date-from-to' | translate : proposal }} {{ proposal.sessionStartDate | date:'mediumDate' }}
                            au {{ proposal.sessionEndDate | date:'mediumDate' }}</p>
                        <p *ngIf="proposal.sessionStartDate !=null && proposal.sessionEndDate == null">{{ 'private.training-organism.proposals.table.sessionDates.date-from' | translate : proposal }} {{ proposal.sessionStartDate | date:'mediumDate' }}</p>
                    </div>
                    <div class="text-secondary">
                        <p>
                            {{ (proposal.autoValidate ? 'private.training-organism.proposals.table.validation.auto' : 'private.training-organism.proposals.table.validation.manuel') | translate }}
                            <br/>
                            <span
                                *ngIf="proposal.expire != null">{{ 'private.training-organism.proposals.table.proposal.expire' | translate : proposal }}{{ proposal.expire | date:'d/MM/yy' }}</span>
                        </p>
                    </div>
                </td>
            </ng-container>

            <ng-container matColumnDef="tags">
                <th class="border-t" mat-header-cell *matHeaderCellDef>
                    {{ 'private.training-organism.proposals.table.tags.title' | translate }}
                </th>
                <td *matCellDef="let proposal" mat-cell>
                    <app-form-field-static *ngIf="proposal.tags?.length"
                                           [lengthLimit]="2"
                                           [value]="proposal.tags"
                                           (customClick)="searchTag($event)"
                                           type="tags"></app-form-field-static>
                </td>
            </ng-container>

        </table>
        <app-paginator [length]="total"
                       [pageSizeOptions]="pageSizeOptions"
                       [scrollTopOnPageChange]="true"
                       (page)="onPageEvent($event)">
        </app-paginator>
    </ng-container>
</app-wrapper-spinner>
