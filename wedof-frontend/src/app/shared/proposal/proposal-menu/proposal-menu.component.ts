import {Component, EventEmitter, Input, OnDestroy, OnInit, Output} from '@angular/core';
import {ProposalService} from '../../api/services/proposal.service';
import {Proposal, ProposalStates} from '../../api/models/proposal';
import {concat, Subject} from 'rxjs';
import {SnackBarComponent} from '../../material/snack-bar/snack-bar.component';
import {TranslateService} from '@ngx-translate/core';
import {MatSnackBar} from '@angular/material/snack-bar';
import {displayTextSnackBar} from '../../utils/displayTextSnackBar';
import {FormGroup} from '@angular/forms';
import {filter, finalize, switchMap, tap} from 'rxjs/operators';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../errors/errors.types';
import {DeletionConfirmationComponent} from '../../material/action-confirmation/deletion-confirmation.component';
import {MatDialog} from '@angular/material/dialog';

@Component({
    selector: 'app-proposal-menu',
    templateUrl: './proposal-menu.component.html',
    styleUrls: ['./proposal-menu.component.scss']
})
export class ProposalMenuComponent implements OnDestroy, OnInit {
    @Input() withButton = false;
    @Input() proposals: Proposal[];
    @Input() panelOpenState = false;
    @Input() formGroup?: FormGroup;
    @Input() isUpdatable?: boolean;
    @Output() processedProposal: EventEmitter<Proposal>;
    @Output() deletedProposal: EventEmitter<any>;
    @Output() openEvent: EventEmitter<any> = new EventEmitter();
    @Output() closeEvent: EventEmitter<any> = new EventEmitter();
    @Output() errorMessages?: EventEmitter<string[]> = new EventEmitter<[]>();
    @Output() loading?: EventEmitter<boolean> = new EventEmitter<boolean>();
    @Output() initProposal: EventEmitter<Proposal> = new EventEmitter<Proposal>();

    hasOpenEvent: boolean;
    proposalStates = ProposalStates;
    isLoading: boolean;
    errors: string[] = [];

    private readonly _unsubscribeAll: Subject<any>;

    constructor(private _proposalService: ProposalService,
                private _translateService: TranslateService,
                private _snackBar: MatSnackBar,
                private _dialog: MatDialog) {
        this.processedProposal = new EventEmitter<Proposal>();
        this.deletedProposal = new EventEmitter<any>();
        this._unsubscribeAll = new Subject();
    }

    ngOnInit(): void {
        this.hasOpenEvent = this.openEvent.observers.length > 0;
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    openPanel(): void {
        this.openEvent.emit();
    }

    closePanel(): void {
        this.closeEvent.emit();
    }

    displayAvailableActions(proposals: Proposal[]): string {
        if (!proposals.length) {
            return this._translateService.instant('common.table.selection.selectAllForStateProposals');
        } else if (proposals?.every(proposal => proposal?.state === proposal[0]?.state)
            && ![
                ProposalStates.TEMPLATE, ProposalStates.VIEWED, ProposalStates.DRAFT,
                ProposalStates.ACCEPTED, ProposalStates.ACTIVE, ProposalStates.REFUSED
            ].includes(proposals[0]?.state)) {
            return this._translateService.instant('common.actions.noAction');
        } else {
            return this._translateService.instant('common.table.selection.selectAllForStateProposals');
        }
    }

    checkProposalsState(proposals: Proposal[]): boolean {
        return proposals[0] && proposals.every(proposal => proposal.state === proposals[0].state) && [
            ProposalStates.TEMPLATE, ProposalStates.VIEWED, ProposalStates.DRAFT,
            ProposalStates.ACCEPTED, ProposalStates.ACTIVE, ProposalStates.REFUSED
        ].includes(proposals[0].state);
    }

    deleteProposal(proposals: Proposal[], event: MouseEvent): void {
        const multipleProposals = proposals.length > 1;
        const dialogRef = this._dialog.open(DeletionConfirmationComponent, {
            panelClass: 'full-page-scroll-40',
            height: 'auto',
            data: {
                messageKey: 'private.training-organism.proposals.confirmDeletion' + (multipleProposals ? 'Multiple' : ''),
                data: multipleProposals ? {} : proposals[0]
            }
        });
        dialogRef.componentInstance.actionValue$.pipe(
            filter((confirmation: boolean) => confirmation),
            switchMap(() => {
                this.loading.emit(true);
                return concat(...proposals.map(proposal => // Call delete in sequence
                    this._proposalService.delete(proposal.code).pipe(
                        tap(() => {
                            this.deletedProposal.emit(proposal);
                        })
                    )
                ));
            }),
            finalize(() => { // On complete of observable = on complete of delete sequence
                dialogRef.componentInstance.close();
                this.loading.emit(false);
            })
        ).subscribe(() => {
            this._snackBar.openFromComponent(
                SnackBarComponent,
                displayTextSnackBar(this._translateService.instant('common.actions.proposalDeletedSuccessfully' + (multipleProposals ? 'Multiple' : '')))
            );
        });
        event.stopPropagation();
    }


    toState(proposals: Proposal[], state: ProposalStates, event: MouseEvent): void {
        proposals.forEach((proposal) => {
            this._proposalService.state(proposal, state).subscribe((proposalUpdated: Proposal) => {
                this.processedProposal.emit(proposalUpdated);
                this.initProposal.emit(proposalUpdated);
            });
        });
        event.stopPropagation();
    }

    submit(proposalToUpdate: Proposal, event: MouseEvent, nextState?: ProposalStates): void {
        this.errors = [];
        this.errorMessages.emit([]);
        this.isLoading = true;
        this.loading.emit(true);
        const proposalFormValue = this.formGroup.getRawValue().proposal;
        const proposal: Proposal = {
            ...proposalToUpdate, ...{
                autoValidate: typeof proposalFormValue.autoValidate === 'boolean' ?? null,
                codeRequired: proposalFormValue.codeRequired,
                email: proposalFormValue.email ?? null,
                firstName: proposalFormValue.firstName ?? null,
                lastName: proposalFormValue.lastName ?? null,
                phoneNumber: proposalFormValue.phoneNumber ?? null,
                limitUsage: proposalFormValue.email ? 1 : proposalFormValue.limitUsage ?? null,
                description: proposalFormValue.description ?? null,
                notes: proposalFormValue.notes ?? null,
                tags: proposalFormValue.tags,
                indicativeDuration: proposalFormValue.indicativeDuration ?? null,
                expire: proposalFormValue.expire ?? null,
                sessionStartDate: proposalFormValue.sessionStartDate ?? null,
                sessionEndDate: proposalFormValue.sessionEndDate ?? null,
                trainingActions: proposalFormValue.trainingActions ?? [],
                logo: proposalFormValue.logo ?? null,
                customColorScheme: proposalFormValue.customColorScheme ?? null,
                sales: proposalFormValue.sales ? {email: proposalFormValue.sales.email} : null
            }
        };
        if (proposalFormValue.discountType) { // Either both or nothing
            proposal.discountType = proposalFormValue.discountType;
            proposal.amount = proposalFormValue.amount ?? null; // because "undefined" will not be sent and the back expects the field
        }
        this._proposalService.update(proposal).pipe(
            finalize(() => {
                this.isLoading = false;
                this.loading.emit(false);
            })
        ).subscribe((proposalUpdated) => {
            proposalToUpdate = {...proposalToUpdate, ...proposalUpdated};
            this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(this._translateService.instant('common.actions.proposalUpdatedSuccessfully', {
                    proposalNumber: proposalUpdated.code
                },
            )));
            this.processedProposal.emit(proposalUpdated);
            this.initProposal.emit(proposalUpdated);
            if (nextState) {
                this.toState([proposalToUpdate], nextState, event);
            }
        }, (httpErrorResponse: HttpErrorResponse) => {
            this.errors = (httpErrorResponse.error as ApiError).errorMessages;
            this.errorMessages.emit((httpErrorResponse.error as ApiError).errorMessages);
        });
    }

    updateAndStateAction(): any {
        return this.formGroup?.dirty && this.panelOpenState;
    }

    updateAndStateActionText(): any {
        return this.formGroup?.dirty && this.panelOpenState ? this._translateService.instant('common.actions.updateAnd') : ' ';
    }

}
