<div class="flex align-center" *ngIf="withButton else onlyMenu">
    <div *ngIf="!formGroup?.dirty || !panelOpenState">
        <div [ngSwitch]="proposals[0]?.state">
            <button *ngSwitchCase="proposalStates.DRAFT"
                    type="button" mat-flat-button
                    color="primary"
                    class="button-actions flex justify-center items-center"
                    (click)="toState(proposals, proposalStates.ACTIVE, $event)">
                {{'private.training-organism.proposals.table.actions.active' | translate: {update: updateAndStateActionText()} }}
            </button>
            <button *ngSwitchDefault
                    type="button" mat-flat-button
                    color="primary"
                    class="button-actions flex justify-center items-center"
                    (click)="toState(proposals, proposalStates.DRAFT, $event)">
                {{'private.training-organism.proposals.table.actions.draft' | translate: {update: updateAndStateActionText()} }}
            </button>
            <div *ngIf="!checkProposalsState(proposals)"
                 class="text-disabled mr-4 p-2">{{'common.actions.noAction' | translate}}</div>
        </div>
    </div>

    <button [class]="checkProposalsState(proposals) ? 'flex justify-center items-center button-actions' :
    'flex justify-center items-center button-actionsOnly' "
            *ngIf="isUpdatable && formGroup?.dirty && panelOpenState"
            type="button"
            mat-flat-button
            color="primary"
            (click)="submit(proposals[0], $event)"
            [disabled]="isLoading || formGroup.invalid">
        <mat-progress-spinner class="mr-4" *ngIf="isLoading" [diameter]="24"
                              mode="indeterminate"></mat-progress-spinner>
        <ng-container *ngIf="!isLoading"> {{ 'common.actions.update' | translate}}</ng-container>
    </button>

    <button class="flex justify-center items-center button-arrow button-arrow-primary"
            color="primary"
            *ngIf="checkProposalsState(proposals)"
            (click)="$event.stopPropagation()"
            mat-flat-button
            [disabled]="updateAndStateAction() && (isLoading || formGroup.invalid)"
            [matMenuTriggerFor]="actionsMenuUpdateState"
            title="Actions" type="button">
        <mat-icon class="icon" svgIcon="arrow_drop_down"></mat-icon>
    </button>
</div>


<ng-template #onlyMenu>
    <button mat-icon-button
            (click)="$event.stopPropagation()"
            [matMenuTriggerFor]="actionsMenu"
            title="Actions">
        <mat-icon svgIcon="more_vert"></mat-icon>
    </button>
</ng-template>

<mat-menu #actionsMenu="matMenu" class="large-menu">
    <ng-template matMenuContent>
        <button mat-menu-item type="button" disabled="disabled" *ngIf="!checkProposalsState(proposals); else actions">
            <span>{{displayAvailableActions(proposals)}}</span>
        </button>
        <button mat-menu-item type="button" (click)="openPanel()" *ngIf="hasOpenEvent && !panelOpenState">
            <mat-icon svgIcon="keyboard_arrow_down"></mat-icon>
            <span>{{ 'private.common.menu.open' | translate}}</span>
        </button>
        <button mat-menu-item type="button" (click)="closePanel()" *ngIf="hasOpenEvent && panelOpenState">
            <mat-icon svgIcon="keyboard_arrow_up"></mat-icon>
            <span>{{ 'private.common.menu.close' | translate}}</span>
        </button>
        <ng-template #actions>
            <button mat-menu-item type="button" *ngIf="proposals.length === 1"
                    [cdkCopyToClipboard]="proposals[0]?.link">
                <mat-icon svgIcon="file_copy"></mat-icon>
                <span>{{'private.training-organism.proposals.table.actions.copy' | translate}}</span>
            </button>
            <button mat-menu-item type="button" *ngIf="proposals[0]?.usedCount === 0"
                    (click)="deleteProposal(proposals, $event)">
                <mat-icon color="warn" svgIcon="delete"></mat-icon>
                <span>{{'common.actions.delete' | translate}}</span>
            </button>
            <button mat-menu-item type="button" *ngIf="proposals[0]?.state === proposalStates.DRAFT"
                    (click)="toState(proposals, proposalStates.ACTIVE, $event)">
                <mat-icon
                    color="{{proposalStates.ACTIVE | proposalStateToColor}}">{{proposalStates.ACTIVE | proposalStateToIcon}}</mat-icon>
                <span>{{'private.training-organism.proposals.table.actions.active' | translate: {update: updateAndStateActionText()} }}</span>
            </button>
            <button mat-menu-item type="button" *ngIf="proposals[0]?.state !== proposalStates.DRAFT"
                    (click)="toState(proposals, proposalStates.DRAFT, $event)">
                <mat-icon
                    color="{{proposalStates.DRAFT | proposalStateToColor}}">{{proposalStates.DRAFT | proposalStateToIcon}}</mat-icon>
                <span>{{'private.training-organism.proposals.table.actions.draft' | translate: {update: updateAndStateActionText()} }}</span>
            </button>
            <button mat-menu-item type="button" *ngIf="proposals[0]?.state !== proposalStates.REFUSED "
                    (click)="toState(proposals, proposalStates.REFUSED, $event)">
                <mat-icon
                    color="{{proposalStates.REFUSED | proposalStateToColor}}">{{proposalStates.REFUSED | proposalStateToIcon}}</mat-icon>
                <span>{{'private.training-organism.proposals.table.actions.refused' | translate: {update: updateAndStateActionText()} }}</span>
            </button>
        </ng-template>
    </ng-template>
</mat-menu>

<mat-menu #actionsMenuUpdateState="matMenu" class="large-menu">
    <ng-template matMenuContent>
        <button mat-menu-item type="button" *ngIf="proposals[0]?.state === proposalStates.DRAFT"
                (click)="updateAndStateAction() ? submit(proposals[0], $event, proposalStates.ACTIVE) : toState(proposals, proposalStates.ACTIVE, $event)">
            <mat-icon
                color="{{proposalStates.ACTIVE | proposalStateToColor}}">{{proposalStates.ACTIVE | proposalStateToIcon}}</mat-icon>
            <span>{{'private.training-organism.proposals.table.actions.active' | translate: {update: updateAndStateActionText()} }}</span>
        </button>
        <button mat-menu-item type="button" *ngIf="proposals[0]?.state !== proposalStates.DRAFT"
                (click)="updateAndStateAction() ? submit(proposals[0], $event, proposalStates.DRAFT) : toState(proposals, proposalStates.DRAFT, $event)">
            <mat-icon
                color="{{proposalStates.DRAFT | proposalStateToColor}}">{{proposalStates.DRAFT | proposalStateToIcon}}</mat-icon>
            <span>{{'private.training-organism.proposals.table.actions.draft' | translate: {update: updateAndStateActionText()} }}</span>
        </button>
        <button mat-menu-item type="button" *ngIf="proposals[0]?.state !== proposalStates.REFUSED"
                (click)="updateAndStateAction() ? submit(proposals[0], $event, proposalStates.REFUSED) : toState(proposals, proposalStates.REFUSED, $event)">
            <mat-icon
                color="{{proposalStates.REFUSED | proposalStateToColor}}">{{proposalStates.REFUSED | proposalStateToIcon}}</mat-icon>
            <span>{{'private.training-organism.proposals.table.actions.refused' | translate: {update: updateAndStateActionText()} }}</span>
        </button>
    </ng-template>
</mat-menu>
