<mat-card class="flex-col mt-3 p-5 pb-0 pt-3 border shadow-none sm" [ngClass]="{'card-loading':cardLoading}"
          *ngIf="proposal">
    <div class="flex items-center mb-2">
        <mat-icon class="mr-3 card-loading-show text-4xl"
                  color="{{proposal.state === states.REFUSED ? 'warn' : 'primary'}}"
                  title="{{ 'private.training-organism.proposals.table.states.' + proposal.state | translate}}">
            {{ proposal.state | proposalStateToIcon}}
        </mat-icon>
        <div class="truncate">
                <span
                    class="text-xl font-semibold card-loading-show">
                    {{ (proposal.email ? 'private.training-organism.proposals.proposalCard.individual' : 'private.training-organism.proposals.proposalCard.generic') | translate }}
                </span>
            <div class="text-secondary text-md truncate card-loading-show"
                 title="{{ 'private.training-organism.proposals.table.states.' + proposal.state | translate}}">
                {{ 'private.training-organism.proposals.table.states.' + proposal.state | translate}} -
                <span
                    title="{{ 'private.training-organism.proposals.proposalCard.stateLastUpdate' | translate}}">{{ proposal.stateLastUpdate | date: 'dd/MM/yyyy' }}</span>
            </div>
        </div>
        <div class="ml-auto -mr-4 card-loading-hidden">
            <app-proposal-menu [proposals]="[proposal]"
                               (processedProposal)="refresh($event)"
                               (deletedProposal)="delete($event)"
                               [panelOpenState]="panelOpenState"
                               (openEvent)="openPanel()"
                               (closeEvent)="closePanel()"
            ></app-proposal-menu>
        </div>
    </div>

    <div class="grid grid-cols-6 gap-2" *ngIf="!panelOpenState || cardLoading">
        <app-form-field-static class="col-span-3"
                               *ngIf="proposal.codeRequired"
                               [value]="'private.training-organism.proposals.proposalCard.link.link.placeholder' | translate"
                               [copy]="true"
                               icon="link"
                               [href]="proposal.link"
                               label="private.training-organism.proposals.proposalCard.link.link.title"
                               type="url">
        </app-form-field-static>
        <app-form-field-static class="col-span-3"
                               *ngIf="proposal.isIndividual"
                               [value]="'private.training-organism.proposals.proposalCard.link.linkCommercial.placeholder' | translate"
                               [copy]="true"
                               icon="link"
                               [href]="proposal.link_commercial"
                               label="private.training-organism.proposals.proposalCard.link.linkCommercial.title"
                               type="url">
        </app-form-field-static>
        <app-form-field-static class="col-span-3"
                               [value]="proposal.codeRequired ? proposal.code : ('private.training-organism.proposals.proposalCard.codeRequired.value' | translate)"
                               [copy]="proposal.codeRequired"
                               label="private.training-organism.proposals.proposalCard.code"
                               type="text">
        </app-form-field-static>
        <app-form-field-static class="col-span-3"
                               *ngIf="proposal.isIndividual"
                               [value]="proposal._links.sales?.name"
                               label="private.training-organism.proposals.form.fields.sales.label"
                               type="select">
        </app-form-field-static>
        <app-form-field-static class="col-span-3"
                               *ngIf="proposal.email"
                               [value]="proposal.email"
                               [copy]="true"
                               icon="email"
                               label="private.training-organism.proposals.form.fields.email.label"
                               type="email">
        </app-form-field-static>
        <app-form-field-static class="col-span-3"
                               *ngIf="proposal.phoneNumber"
                               [value]="proposal.phoneNumber"
                               [copy]="true"
                               icon="phone"
                               label="private.training-organism.proposals.proposalCard.phoneNumber"
                               type="tel">
        </app-form-field-static>
        <app-form-field-static [value]="proposal.autoValidate === true ? 'Oui' : 'Non'"
                               class="col-span-3"
                               icon="sync"
                               label="private.training-organism.proposals.proposalCard.autoValidate"
                               type="text">
        </app-form-field-static>
        <app-form-field-static class="col-span-3"
                               [value]="proposal.expire"
                               label="private.training-organism.proposals.proposalCard.expireDate"
                               icon="event_busy"
                               type="date">
        </app-form-field-static>
        <app-form-field-static class="col-span-6"
                               [value]="proposal.tags"
                               label="common.tags.label"
                               type="tags">
        </app-form-field-static>
        <app-form-field-static class="col-span-6"
                               *ngIf="proposal.notes?.length >= 1"
                               [value]="proposal.notes"
                               label="private.training-organism.proposals.form.fields.notes.label"
                               type="textarea">
        </app-form-field-static>
        <button type="button" class="col-span-6 flex justify-center mt-2 -mx-5 pt-2 pb-2 open-panel card-loading-hidden"
                (click)="openPanel()">
            <mat-icon svgIcon="keyboard_arrow_down"></mat-icon>
        </button>
    </div>

    <form #form="ngForm" [formGroup]="formGroup" class="flex flex-col" *ngIf="panelOpenState && !cardLoading">
        <app-form-fields formGroupName="proposal"
                         class="grid grid-cols-6 gap-2"
                         [entity]="proposal"
                         [appFormFieldsData]="appFormFieldsData"
                         [formGroup]="formGroup">
        </app-form-fields>
        <button type="button" class="flex justify-center -mx-5 pt-2 pb-2 close-panel" (click)="closePanel()">
            <mat-icon svgIcon="keyboard_arrow_up"></mat-icon>
        </button>
    </form>

    <div
        class="flex items-center justify-end border-t pl-5 pr-10 -mx-5 py-3 light:bg-cool-gray-50 dark:bg-cool-gray-700 card-loading-hidden">
        <mat-error *ngIf="errorMessages.length">
            <ul>
                <li *ngFor="let errorMessage of errorMessages">{{ errorMessage }}</li>
            </ul>
        </mat-error>
        <app-proposal-menu class="-mr-4"
                           [proposals]="[proposal]"
                           (processedProposal)="refresh($event)"
                           (deletedProposal)="delete($event)"
                           [withButton]="true"
                           [panelOpenState]="panelOpenState"
                           (openEvent)="openPanel()"
                           (closeEvent)="closePanel()"
                           (initProposal)="initForm($event)"
                           [formGroup]="formGroup"
                           (errorMessages)="setErrorMessages($event)"
                           (loading)="setLoading($event)"
                           [isUpdatable]="isUpdatable"></app-proposal-menu>
    </div>
</mat-card>
