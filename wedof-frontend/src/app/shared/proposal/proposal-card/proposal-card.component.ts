import {
    AfterViewChecked,
    ChangeDetector<PERSON>ef,
    Component,
    ElementRef,
    EventEmitter,
    Input,
    OnChanges,
    OnDestroy,
    OnInit,
    Output,
    SimpleChanges,
    ViewChild
} from '@angular/core';
import {DiscountType, Proposal, ProposalStates} from '../../api/models/proposal';
import {FormBuilder, FormGroup, FormGroupDirective, Validators} from '@angular/forms';
import {TranslateService} from '@ngx-translate/core';
import {AppFormFieldData} from '../../material/app-form-field/app-form-field.component';
import {first} from 'rxjs/operators';
import {TrainingActionService} from '../../api/services/training-action.service';
import {FormValidators} from '../../api/shared/form-validators';
import moment from 'moment';
import business from 'moment-business';
import {RegistrationFolderService} from '../../api/services/registration-folder.service';
import {TrainingAction} from '../../api/models/training-action';
import {User} from '../../api/models/user';
import {UserService} from '../../api/services/user.service';
import {BaseCardCanDeactivateDirective} from '../../utils/base-card/base-card-can-deactivate.directive';
import {RequiredCallSuper} from '../../utils/base-card/base-card.directive';


@Component({
    selector: 'app-proposal-card',
    templateUrl: './proposal-card.component.html',
    styleUrls: ['./proposal-card.component.scss']
})
export class ProposalCardComponent extends BaseCardCanDeactivateDirective implements OnChanges, OnInit, AfterViewChecked, OnDestroy {
    static COMPONENT_ID = 'proposition';
    @Input() proposal: Proposal;
    @Output() proposalChange = new EventEmitter<Proposal>();
    @Output() proposalDeleted = new EventEmitter<any>();
    states = ProposalStates;
    appFormFieldsData: AppFormFieldData[];
    panelOpenState = false;
    formGroup: FormGroup;
    loading = false;
    errorMessages: string[] = [];
    cpfSessionMinDate: Date = null;
    isUpdatable = false;
    selectedTrainingAction: TrainingAction;

    @ViewChild('form') form: FormGroupDirective;

    constructor(private _translateService: TranslateService,
                private _trainingActionService: TrainingActionService,
                private _userService: UserService,
                private _formBuilder: FormBuilder,
                private _registrationFolderService: RegistrationFolderService,
                private _el: ElementRef,
                readonly changeDetectorRef: ChangeDetectorRef) {
        super(ProposalCardComponent.COMPONENT_ID, _el);
    }

    ngOnChanges(changes: SimpleChanges): void {
        this.panelLoading();
        this.selectedTrainingAction = this.proposal.selectedTrainingAction;
        this.initForm(this.proposal);
    }

    ngOnInit(): void {
        this._registrationFolderService.sessionMinDates$.pipe(first()).subscribe((sessionMinDates) => {
            this.cpfSessionMinDate = new Date(sessionMinDates.cpfSessionMinDate);
        });
    }

    openPanel(): void {
        this.panelOpenState = true;
    }

    closePanel(): void {
        this.panelOpenState = false;
    }

    initForm(proposal: Proposal): void {
        this.errorMessages = [];
        if (!proposal) {
            return;
        }
        this.formGroup = this._formBuilder.group({
            proposal: this._formBuilder.group({})
        });
        let sessionDate;
        if (proposal.sessionStartDate !== null && proposal.sessionEndDate !== null) {
            const sessionStartDate = new Date(proposal.sessionStartDate).toLocaleDateString();
            const sessionEndDate = new Date(proposal.sessionEndDate).toLocaleDateString();
            sessionDate = this._translateService.instant('private.training-organism.proposals.proposalCard.sessionDates', {
                sessionStartDate, sessionEndDate
            });
        } else if (proposal.sessionStartDate !== null) {
            sessionDate = this._translateService.instant('public.funnel.merchant.sessionDates.sessionStartDate') + ' ' + new Date(proposal.sessionStartDate).toLocaleDateString();
        }
        this.isUpdatable = proposal.state === this.states.DRAFT || proposal.state === this.states.REFUSED ||
            proposal.state === this.states.ACTIVE || (proposal.state === this.states.TEMPLATE && proposal.usedCount === 0);
        const getDiscountTypeChoices = (trainingAction: TrainingAction) => {
            const choicesDiscountType = [
                {
                    key: this._translateService.instant('private.training-organism.common.discountType.none.radio'),
                    value: DiscountType.DISCOUNT_TYPE_NONE
                },
                {
                    key: this._translateService.instant('private.training-organism.common.discountType.amount.radio'),
                    value: DiscountType.DISCOUNT_TYPE_AMOUNT
                },
                {
                    key: this._translateService.instant('private.training-organism.common.discountType.percent.radio'),
                    value: DiscountType.DISCOUNT_TYPE_PERCENT
                }
            ];
            if (trainingAction && proposal.state === ProposalStates.DRAFT) {
                choicesDiscountType.push({
                    key: this._translateService.instant('private.training-organism.common.discountType.fixed.radio'),
                    value: DiscountType.DISCOUNT_TYPE_PRICE
                });
            }
            return choicesDiscountType;
        };
        const getAmountAppFormFieldData = (discountType: DiscountType, trainingAction: TrainingAction) => {
            let appFormFieldAmount: AppFormFieldData;
            if (proposal.state === ProposalStates.DRAFT) {
                appFormFieldAmount = {
                    controlName: 'amount',
                    type: 'number',
                    colSpan: 3
                };
                if (discountType === DiscountType.DISCOUNT_TYPE_NONE) {
                    appFormFieldAmount.disabled = true;
                    appFormFieldAmount.removed = true;
                    appFormFieldAmount.required = false;
                } else {
                    appFormFieldAmount.removed = false;
                    appFormFieldAmount.disabled = false;
                    appFormFieldAmount.required = true;
                    appFormFieldAmount.label = 'private.training-organism.common.discountType.' + discountType + '.label';
                    appFormFieldAmount.help = 'private.training-organism.common.discountType.' + discountType + '.placeholder';
                    appFormFieldAmount.suffix = 'private.training-organism.common.discountType.' + discountType + '.unit';
                    if (discountType === DiscountType.DISCOUNT_TYPE_AMOUNT) {
                        appFormFieldAmount.type = 'price';
                        appFormFieldAmount.validators = [];
                        appFormFieldAmount.validatorsMessages = {};
                        if (trainingAction) {
                            const minValue = -trainingAction.totalTvaTTC;
                            const maxValue = Number((trainingAction.totalTvaTTC * 0.15).toFixed(2));
                            appFormFieldAmount.min = minValue;
                            appFormFieldAmount.max = maxValue;
                            appFormFieldAmount.validators.push(Validators.min(minValue));
                            appFormFieldAmount.validators.push(Validators.max(maxValue));
                            appFormFieldAmount.validatorsMessages.min =
                                this._translateService.instant('private.training-organism.common.discountType.errors.minAmount', {value: minValue});
                            appFormFieldAmount.validatorsMessages.max =
                                this._translateService.instant('private.training-organism.common.discountType.errors.maxAmount', {value: maxValue});
                        }
                    } else if (discountType === DiscountType.DISCOUNT_TYPE_PERCENT) {
                        appFormFieldAmount.type = 'percent';
                        appFormFieldAmount.min = -100;
                        appFormFieldAmount.max = 15;
                        appFormFieldAmount.validators = [Validators.min(-100), Validators.max(15)];
                        appFormFieldAmount.validatorsMessages = {
                            min: 'common.errors.discountType.percent',
                            max: 'common.errors.discountType.percent'
                        };
                    } else if (discountType === DiscountType.DISCOUNT_TYPE_PRICE) {
                        appFormFieldAmount.type = 'price';
                        appFormFieldAmount.min = 0;
                        appFormFieldAmount.validators = [Validators.min(0)];
                        appFormFieldAmount.validatorsMessages = {
                            min: this._translateService.instant('private.training-organism.common.discountType.errors.min', {value: 0})
                        };
                        if (trainingAction) {
                            const maxValue = Number((trainingAction.totalTvaTTC * 1.15).toFixed(2));
                            appFormFieldAmount.max = maxValue;
                            appFormFieldAmount.validators.push(Validators.max(maxValue));
                            appFormFieldAmount.validatorsMessages.max = this._translateService.instant('private.training-organism.common.discountType.errors.max',
                                {value: maxValue}
                            );
                        }
                    }
                }
            } else {
                appFormFieldAmount = {
                    controlName: 'amount',
                    placeholder: 'private.training-organism.proposals.table.conditions.none',
                    suffix: proposal.discountType === DiscountType.DISCOUNT_TYPE_PERCENT ? ' %' :
                        (proposal.discountType === DiscountType.DISCOUNT_TYPE_PRICE || proposal.discountType === DiscountType.DISCOUNT_TYPE_AMOUNT ? ' €' : ''),
                    disabled: true,
                    icon: 'euro_symbol',
                    label: proposal.discountType === DiscountType.DISCOUNT_TYPE_NONE ? 'private.training-organism.proposals.form.fields.amount.label' : ('private.training-organism.common.discountType.' + proposal.discountType + '.label'),
                    type: 'number',
                };
            }
            return appFormFieldAmount;
        };
        const updateSessionDates = !!(proposal.sessionStartDate || proposal.sessionEndDate) && proposal.state === ProposalStates.DRAFT;
        this.appFormFieldsData = [
            {
                controlName: 'link',
                disabled: true,
                label: 'private.training-organism.proposals.proposalCard.link.link.title',
                type: 'url',
                icon: 'link',
                href: proposal.link,
                copy: true,
                removed: !proposal.codeRequired,
                value: this._translateService.instant('private.training-organism.proposals.proposalCard.link.link.placeholder'),
                colSpan: 3
            },
            {
                controlName: 'link_commercial',
                disabled: true,
                removed: !proposal.isIndividual,
                label: 'private.training-organism.proposals.proposalCard.link.linkCommercial.title',
                type: 'url',
                icon: 'link',
                href: proposal.link_commercial,
                copy: true,
                value: this._translateService.instant('private.training-organism.proposals.proposalCard.link.linkCommercial.placeholder'),
                colSpan: 3
            },
            {
                controlName: 'code',
                disabled: true,
                label: 'private.training-organism.proposals.proposalCard.code',
                type: 'text',
                copy: proposal.codeRequired,
                value: proposal.codeRequired ? proposal.code : (this._translateService.instant('private.training-organism.proposals.proposalCard.codeRequired.value')),
                colSpan: 3
            },
            {
                controlName: 'sales',
                disabled: proposal.state !== ProposalStates.DRAFT,
                value: proposal._links.sales,
                label: 'private.training-organism.proposals.form.fields.sales.label',
                type: 'infiniteSearch',
                help: 'private.training-organism.proposals.form.fields.sales.toolTip',
                icon: 'cases',
                placeholder: 'private.training-organism.proposals.form.fields.sales.placeholder',
                searchNoEntriesFoundLabel: 'private.training-organism.proposals.form.fields.sales.noUserFound',
                searchMethodPaginated: (params) => this._userService.list(params),
                parameters: {limit: 10, order: 'asc', sort: 'name', siret: proposal._links.organism.siret},
                searchResultFormatter: (sales: User) => sales.name,
                searchComparisonProperty: 'email',
                removable: true,
                colSpan: 3
            },
            {
                controlName: 'expire',
                disabled: proposal.state !== ProposalStates.DRAFT,
                min: moment(new Date()).add(1, 'days').toDate(),
                label: 'private.training-organism.proposals.proposalCard.expireDate',
                type: 'datetime-local',
                removable: true,
                icon: 'event_busy',
                placeholder: 'private.training-organism.proposals.form.fields.expire.placeholder',
                colSpan: 3
            },
            {
                controlName: 'autoValidate',
                value: proposal.state !== ProposalStates.DRAFT ?
                    proposal.autoValidate === true ? 'Oui' : 'Non' : proposal.autoValidate,
                disabled: proposal.state !== ProposalStates.DRAFT,
                label: 'private.training-organism.proposals.proposalCard.autoValidate',
                type: proposal.state !== ProposalStates.DRAFT ? 'text' : 'checkbox',
                icon: 'sync',
                colSpan: 3
            },
            {
                controlName: 'firstName',
                removed: !proposal.isIndividual,
                disabled: proposal.state !== ProposalStates.DRAFT,
                label: 'private.training-organism.proposals.form.fields.firstName.label',
                type: 'text',
                icon: 'badge',
                colSpan: 3
            },
            {
                controlName: 'lastName',
                removed: !proposal.isIndividual,
                disabled: proposal.state !== ProposalStates.DRAFT,
                label: 'private.training-organism.proposals.form.fields.lastName.label',
                type: 'text',
                icon: 'badge',
                colSpan: 3,
            },
            {
                controlName: 'email',
                removed: !proposal.isIndividual,
                required: true,
                disabled: proposal.state !== ProposalStates.DRAFT,
                label: 'private.training-organism.proposals.form.fields.email.label',
                type: 'email',
                icon: 'email',
                copy: true,
                placeholder: 'private.training-organism.proposals.form.fields.email.placeholder',
                help: proposal.state === ProposalStates.DRAFT && 'public.funnel.merchant.emailTooltip',
                validators: [Validators.email],
                validatorsMessages: {
                    email: 'public.funnel.errors.email',
                },
                colSpan: 3,
            },
            {
                controlName: 'phoneNumber',
                removed: !proposal.isIndividual,
                disabled: proposal.state !== ProposalStates.DRAFT,
                label: 'private.training-organism.proposals.form.fields.phone.label',
                type: 'tel',
                icon: 'smartphone',
                help: proposal.state === ProposalStates.DRAFT && 'public.funnel.merchant.phoneNumberTooltip',
                validators: [Validators.pattern(FormValidators.PHONE_PATTERN), Validators.minLength(10), Validators.maxLength(10)],
                validatorsMessages: {
                    pattern: 'public.funnel.errors.phoneNumber'
                },
                colSpan: 3,
            },
            {
                controlName: 'trainingActions',
                type: 'infiniteSearch',
                label: 'private.training-organism.proposals.table.trainingActions.title',
                disabled: proposal.state !== ProposalStates.DRAFT,
                removable: true,
                change: (controlName, newValue, formData, formGroup): any[] => {
                    this.selectedTrainingAction = (newValue && newValue.length === 1) ? newValue[0] : null;
                    const appFormFieldAmount = formData.find(field => field.controlName === 'amount');
                    const appFormFieldDiscountType = formData.find(field => field.controlName === 'discountType');
                    const appFormFieldIndicativeDuration = formData.find(field => field.controlName === 'indicativeDuration');
                    const appFormFieldUpdateSessionDates = formData.find(field => field.controlName === 'updateSessionDates');
                    const appFormFieldsSessionStart = formData.find(field => field.controlName === 'sessionStartDate');
                    const appFormFieldsSessionEnd = formData.find(field => field.controlName === 'sessionEndDate');
                    appFormFieldDiscountType.choices = getDiscountTypeChoices(this.selectedTrainingAction);
                    const discountTypeFormControl = formGroup.get('proposal.discountType');
                    let discountType = discountTypeFormControl.value;
                    if (!appFormFieldDiscountType.choices.find(choice => choice.value === discountTypeFormControl.value)) {
                        appFormFieldDiscountType.value = DiscountType.DISCOUNT_TYPE_NONE;
                        discountType = appFormFieldDiscountType.value;
                    }
                    Object.assign(appFormFieldAmount, getAmountAppFormFieldData(discountType, this.selectedTrainingAction));
                    if (this.selectedTrainingAction) {
                        appFormFieldIndicativeDuration.removed = false;
                        appFormFieldIndicativeDuration.value = this.selectedTrainingAction.indicativeDuration ?? null;
                        appFormFieldUpdateSessionDates.removed = false;
                        appFormFieldsSessionStart.removed = false;
                        appFormFieldsSessionStart.value = null;
                        appFormFieldsSessionEnd.removed = false;
                        appFormFieldsSessionEnd.value = null;
                    } else {
                        appFormFieldIndicativeDuration.removed = true;
                        appFormFieldIndicativeDuration.value = null;
                        appFormFieldUpdateSessionDates.removed = true;
                        appFormFieldsSessionStart.removed = true;
                        appFormFieldsSessionStart.value = null;
                        appFormFieldsSessionEnd.removed = true;
                        appFormFieldsSessionEnd.value = null;
                    }
                    return [appFormFieldAmount, appFormFieldDiscountType, appFormFieldIndicativeDuration,
                        appFormFieldUpdateSessionDates, appFormFieldsSessionStart, appFormFieldsSessionEnd];
                },
                placeholder: proposal.state === ProposalStates.DRAFT ? 'public.funnel.merchant.trainingActions.placeholder' : 'private.training-organism.proposals.table.trainingActions.noTrainingActions',
                searchNoEntriesFoundLabel: 'public.funnel.merchant.trainingActions.noTrainingAction',
                searchMultiple: true,
                searchMethodPaginated: (params) => this._trainingActionService.list(params),
                parameters: {state: 'published', eligible: true, limit: 50},
                searchResultFormatter: (trainingAction: TrainingAction) => trainingAction.trainingTitle,
                searchComparisonProperty: 'externalId',
                removed: proposal.state !== ProposalStates.DRAFT && proposal.trainingActions.length === 0
            },
            {
                controlName: 'discountType',
                label: 'private.training-organism.common.discountType.title',
                type: 'select',
                colSpan: 3,
                removed: proposal.state !== ProposalStates.DRAFT,
                choices: getDiscountTypeChoices(this.selectedTrainingAction),
                change: (controlName, newValue, formData) => {
                    const appFormFieldAmount = formData.find(field => field.controlName === 'amount');
                    Object.assign(appFormFieldAmount, getAmountAppFormFieldData(newValue, this.selectedTrainingAction));
                    appFormFieldAmount.value = null;
                    return [appFormFieldAmount];
                }
            },
            getAmountAppFormFieldData(proposal.discountType, this.selectedTrainingAction), // Dynamic app form field
            {
                controlName: 'updateSessionDates',
                value: updateSessionDates,
                label: 'public.funnel.merchant.sessionDates.title',
                type: 'radio',
                inline: true,
                removed: proposal.state !== ProposalStates.DRAFT || !this.selectedTrainingAction,
                choices: [
                    {key: this._translateService.instant('public.funnel.merchant.sessionDates.yes'), value: true},
                    {key: this._translateService.instant('public.funnel.merchant.sessionDates.no'), value: false}
                ],
                change: (controlName, newValue, formData) => {
                    const appFormFieldSessionStartDate = formData.find(field => field.controlName === 'sessionStartDate');
                    const appFormFieldSessionEndDate = formData.find(field => field.controlName === 'sessionEndDate');
                    if (newValue) {
                        appFormFieldSessionStartDate.disabled = false;
                        appFormFieldSessionEndDate.disabled = false;
                    } else {
                        appFormFieldSessionStartDate.disabled = true;
                        appFormFieldSessionEndDate.disabled = true;
                    }
                    return [appFormFieldSessionStartDate, appFormFieldSessionEndDate];
                },
            },
            {
                controlName: 'sessionStartDate',
                disabled: !updateSessionDates,
                removed: !this.selectedTrainingAction,
                min: moment(this.cpfSessionMinDate),
                label: 'public.funnel.merchant.sessionDates.sessionStartDate',
                placeholder: proposal.state === ProposalStates.DRAFT ? 'public.funnel.merchant.sessionDates.sessionStartDate' : null,
                type: 'date',
                change: this.checkSessionDates,
                removable: true,
                icon: 'event',
                validatorsMessages: {
                    min: 'common.errors.date',
                },
                colSpan: 3
            },
            {
                controlName: 'sessionEndDate',
                disabled: !updateSessionDates,
                removed: !this.selectedTrainingAction,
                label: 'public.funnel.merchant.sessionDates.sessionEndDate',
                placeholder: proposal.state === ProposalStates.DRAFT ? 'public.funnel.merchant.sessionDates.sessionEndDate' : null,
                min: proposal.sessionStartDate ? moment(new Date(proposal.sessionStartDate)) : moment(this.cpfSessionMinDate),
                type: 'date',
                change: this.checkSessionDates,
                removable: true,
                icon: 'event',
                validatorsMessages: {
                    min: 'common.errors.date',
                },
                colSpan: 3
            },
            {
                controlName: 'indicativeDuration',
                type: 'hours',
                icon: 'schedule',
                removed: !this.selectedTrainingAction,
                disabled: proposal.state !== ProposalStates.DRAFT,
                label: 'private.training-organism.proposals.proposalCard.indicativeDuration',
                min: 1,
                validators: [Validators.min(1)],
                validatorsMessages: {
                    min: 'private.training-organism.folders.dialog.errors.indicativeDuration',
                },
            },
            {
                controlName: 'limitUsage',
                removed: !!proposal.email,
                disabled: proposal.state !== ProposalStates.DRAFT,
                label: 'private.training-organism.proposals.proposalCard.limitUsage',
                type: 'number',
                icon: 'confirmation_number',
                colSpan: 3
            },
            {
                controlName: 'usedCount',
                removed: !!proposal.email,
                disabled: true,
                label: 'private.training-organism.proposals.proposalCard.usedCount',
                type: 'text',
                icon: 'data_usage',
                colSpan: 3,
            },
            {
                controlName: 'tags',
                disabled: !this.isUpdatable,
                label: 'common.tags.label',
                type: 'tags',
                isCreateAvailable: true,
            },
            {
                controlName: 'description',
                disabled: !this.isUpdatable,
                label: 'private.training-organism.proposals.form.fields.description.label',
                type: 'textarea',
                maxLength: 5000,
                removed: !proposal.codeRequired,
                placeholder: 'private.training-organism.folders.dialog.placeholders.description',
                icon: 'notes',
            },
            {
                controlName: 'notes',
                disabled: !this.isUpdatable,
                label: 'private.training-organism.proposals.form.fields.notes.label',
                type: 'textarea',
                maxLength: 5000,
                removed: !proposal.codeRequired,
                icon: 'text_snippet',
                placeholder: 'private.training-organism.folders.common.notes.placeholder',
            },
            {
                removed: proposal.isIndividual,
                disabled: !this.isUpdatable,
                controlName: 'customColorScheme',
                value: proposal.customColorScheme ?? (this._translateService.instant('private.profile.organism.form.fields.customColorScheme.placeholder')),
                label: 'private.profile.organism.form.fields.customColorScheme.label',
                placeholder: 'private.profile.organism.form.fields.customColorScheme.placeholder',
                type: 'color',
                maxLength: 7,
                validators: [Validators.pattern(FormValidators.HEX_COLOR)],
            }, {
                removed: proposal.isIndividual,
                disabled: !this.isUpdatable,
                controlName: 'logo',
                value: proposal.logo,
                label: 'common.actions.file.label.logo',
                viewLabel: this._translateService.instant('common.actions.file.view', {file: 'le logo'}),
                chooseLabel: this._translateService.instant('common.actions.file.choose', {format: '.svg'}),
                removeLabel: this._translateService.instant('common.actions.file.remove', {file: 'logo'}),
                type: 'file',
                icon: 'image',
                removable: true,
                showFilePreview: true,
                fileTypesAccept: ['image/svg+xml']
            }
        ];
        this.panelLoaded();
    }

    refresh(proposal: Proposal): void {
        this.proposal = proposal;
        this.proposalChange.emit(proposal);
    }

    delete(proposal: Proposal): void {
        this.proposal = proposal;
        this.proposalDeleted.emit(proposal);
        this.proposal = null;
    }

    ngAfterViewChecked(): void {
        this.changeDetectorRef.detectChanges();
    }

    canDeactivate(): boolean {
        return !this.loading && (this.form?.submitted || !this.formGroup?.dirty);
    }

    checkSessionDates(controlName, newValue, formData, formGroup): any[] {
        const appFormFieldSessionStartDate = formData.find(field => field.controlName === 'sessionStartDate');
        const appFormFieldSessionEndDate = formData.find(field => field.controlName === 'sessionEndDate');
        const sessionStartDate = controlName === 'sessionStartDate' ? newValue : formGroup.get('proposal.sessionStartDate')?.value;
        const sessionEndDate = controlName === 'sessionEndDate' ? newValue : formGroup.get('proposal.sessionEndDate')?.value;
        appFormFieldSessionStartDate.value = sessionStartDate === null ? null : moment(new Date(sessionStartDate)).toISOString();
        appFormFieldSessionEndDate.value = sessionEndDate === null ? null : moment(new Date(sessionEndDate)).toISOString();
        appFormFieldSessionEndDate.min = sessionStartDate ? moment(new Date(sessionStartDate)) : business.addWeekDays(moment(new Date()), 11).toDate();
        appFormFieldSessionStartDate.validators = sessionEndDate ? [Validators.required] : [];
        appFormFieldSessionEndDate.required = !!sessionEndDate;
        return [appFormFieldSessionStartDate];
    }

    setLoading(loading: boolean): void {
        this.loading = loading;
    }

    setErrorMessages(error: string[]): void {
        this.errorMessages = error;
    }

    ngOnDestroy(): RequiredCallSuper {
        return super.ngOnDestroy();
    }
}
