<mat-card class="flex-col mt-3 p-5 pb-0 pt-3 border shadow-none sm">
    <form #form="ngForm" (ngSubmit)="onSubmit()" [formGroup]="formGroup" class="flex flex-col">
        <app-form-fields class="grid grid-cols-12 gap-2"
                         formGroupName="proposal"
                         [appFormFieldsData]="appFormFieldsData"
                         [formGroup]="formGroup"></app-form-fields>
        <div
            class="flex items-center justify-end border-t border-b -mx-5 mt-5 px-8 py-5 light:bg-cool-gray-50 dark:bg-cool-gray-700">
            <mat-error *ngIf="errorMessages.length">
                <ul>
                    <li *ngFor="let errorMessage of errorMessages">{{ errorMessage }}</li>
                </ul>
            </mat-error>
            <div class="-mr-4">
                <button [disabled]="formGroup.invalid || loading"
                        color="primary"
                        mat-flat-button>
                    <mat-progress-spinner class="mr-4" *ngIf="loading" [diameter]="24"
                                          mode="indeterminate"></mat-progress-spinner>
                    <ng-container
                        *ngIf="!loading"> {{ 'private.training-organism.proposals.form.button.text' | translate }}
                    </ng-container>
                </button>
            </div>
        </div>
    </form>
</mat-card>
