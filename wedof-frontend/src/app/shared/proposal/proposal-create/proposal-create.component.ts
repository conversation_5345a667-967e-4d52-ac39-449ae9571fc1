import {Component, <PERSON>E<PERSON>ter, On<PERSON><PERSON>roy, OnInit, Output, ViewChild} from '@angular/core';
import {DiscountType, Proposal} from '../../api/models/proposal';
import {FormBuilder, FormGroup, FormGroupDirective, Validators} from '@angular/forms';
import {Subject} from 'rxjs';
import {TrainingAction} from '../../api/models/training-action';
import {MatAutocomplete} from '@angular/material/autocomplete';
import {ProposalService} from '../../api/services/proposal.service';
import {TrainingActionService} from '../../api/services/training-action.service';
import {HttpErrorResponse} from '@angular/common/http';
import moment from 'moment';
import {CanDeactivateComponent} from '../../utils/can-deactivate/can-deactivate.component';
import {ApiError} from '../../errors/errors.types';
import {SearchMethod} from '../../material/infinite-scroll/infinite-scroll.component';
import {DisplayCatalogExternalIdPipe} from '../../pipes/display-catalog-external-id.pipe';
import {AppFormFieldData} from '../../material/app-form-field/app-form-field.component';
import {TranslateService} from '@ngx-translate/core';
import {first} from 'rxjs/operators';
import {RegistrationFolderService} from '../../api/services/registration-folder.service';

@Component({
    selector: 'app-proposal-create',
    templateUrl: './proposal-create.component.html',
    styleUrls: ['./proposal-create.component.scss']
})
export class ProposalCreateComponent extends CanDeactivateComponent implements OnInit, OnDestroy {

    @Output() proposalCreate = new EventEmitter<Proposal>();
    proposal: Proposal;
    amountMinusPlus = '-';
    today: Date = new Date();
    tomorrow: Date;
    minSessionStartDate: Date;
    trainingActionSelected: TrainingAction[];
    loading = false;
    DiscountType = DiscountType;
    errorMessages: string[] = [];
    appFormFieldsData: AppFormFieldData[];
    formGroup: FormGroup;
    resultFormatterTrainingActions: any;
    searchMethodTrainingActions: SearchMethod;

    @ViewChild('auto') matAutocomplete: MatAutocomplete;
    @ViewChild('form') form: FormGroupDirective;

    protected _unsubscribeAll = new Subject<void>();

    constructor(
        private _formBuilder: FormBuilder,
        private _proposalService: ProposalService,
        private _registrationFolderService: RegistrationFolderService,
        private _trainingActionService: TrainingActionService,
        private _displayCatalogExternalIdPipe: DisplayCatalogExternalIdPipe,
        private _translateService: TranslateService
    ) {
        super();
        this.tomorrow = moment(this.today).add(1, 'days').toDate();
    }

    ngOnInit(): void {
        this._registrationFolderService.sessionMinDates$.pipe(first()).subscribe((sessionMinDates) => {
            this.minSessionStartDate = new Date(sessionMinDates.cpfSessionMinDate);
            this.initForm();
        });
    }

    initForm(): void {
        this.resultFormatterTrainingActions = (trainingAction: TrainingAction) =>
            this._displayCatalogExternalIdPipe.transform(trainingAction.externalId) + ' - ' + trainingAction.trainingTitle;
        this.searchMethodTrainingActions = (params: any) => this._trainingActionService.list(params);

        this.formGroup = this._formBuilder.group({
            proposal: this._formBuilder.group({})
        });

        const discountTypes = [
            {
                key: this._translateService.instant('private.training-organism.proposals.form.fields.discountType.percent.increase'),
                value: 'percent-increase'
            },
            {
                key: this._translateService.instant('private.training-organism.proposals.form.fields.discountType.percent.decrease'),
                value: 'percent-decrease'
            },
            {
                key: this._translateService.instant('private.training-organism.proposals.form.fields.discountType.amount.increase'),
                value: 'amount-increase'
            },
            {
                key: this._translateService.instant('private.training-organism.proposals.form.fields.discountType.amount.decrease'),
                value: 'amount-decrease'
            }
        ];

        const appFormFieldsData: AppFormFieldData[] = [
            {
                controlName: 'isIndividual',
                type: 'radio',
                label: 'private.training-organism.proposals.form.fields.type.label',
                value: false,
                inline: true,
                colSpan: 12,
                choices: [
                    {
                        key: this._translateService.instant('private.training-organism.proposals.form.fields.type.generic'),
                        value: false
                    },
                    {
                        key: this._translateService.instant('private.training-organism.proposals.form.fields.type.single'),
                        value: true,
                    }
                ],
                change: (controlName, newValue, formData) => {
                    const appFormFieldCodeRequired = formData.find(field => field.controlName === 'codeRequired');
                    const appFormFieldLimitUsage = formData.find(field => field.controlName === 'limitUsage');
                    const appFormFieldFirstName = formData.find(field => field.controlName === 'firstName');
                    const appFormFieldLastName = formData.find(field => field.controlName === 'lastName');
                    const appFormFieldPhone = formData.find(field => field.controlName === 'phoneNumber');
                    const appFormFieldEmail = formData.find(field => field.controlName === 'email');
                    const appFormFieldCode = formData.find(field => field.controlName === 'code');
                    const appFormFieldExpire = formData.find(field => field.controlName === 'expire');
                    appFormFieldLastName.removed = !newValue;
                    appFormFieldFirstName.removed = !newValue;
                    appFormFieldPhone.removed = !newValue;
                    appFormFieldEmail.removed = !newValue;
                    appFormFieldCodeRequired.removed = newValue;
                    appFormFieldLimitUsage.removed = newValue;
                    appFormFieldLimitUsage.value = null;
                    if (newValue) {
                        appFormFieldLimitUsage.value = 1;
                        appFormFieldCode.colSpan = 6;
                        appFormFieldExpire.colSpan = 6;
                    } else {
                        appFormFieldCode.colSpan = 4;
                        appFormFieldExpire.colSpan = 4;
                    }
                    return [appFormFieldCodeRequired, appFormFieldLimitUsage, appFormFieldFirstName, appFormFieldLastName, appFormFieldEmail, appFormFieldPhone];
                }
            },
            {
                controlName: 'codeRequired',
                type: 'radio',
                label: 'private.training-organism.proposals.form.fields.codeRequired.label',
                inline: true,
                value: true,
                removable: true,
                removed: false,
                colSpan: 12,
                choices: [
                    {
                        key: this._translateService.instant('common.actions.yes'),
                        value: true
                    },
                    {
                        key: this._translateService.instant('common.actions.no'),
                        value: false
                    }
                ],
                change: (controlName, newValue, formData) => {
                    const appFormFieldCode = formData.find(field => field.controlName === 'code');
                    const appFormFieldLimite = formData.find(field => field.controlName === 'limitUsage');
                    const appFormFieldExpire = formData.find(field => field.controlName === 'expire');
                    appFormFieldCode.removed = newValue === false;
                    appFormFieldLimite.colSpan = newValue ? 4 : 6;
                    appFormFieldExpire.colSpan = newValue ? 4 : 6;
                    return [appFormFieldCode, appFormFieldLimite, appFormFieldExpire];
                }
            },
            {
                controlName: 'code',
                type: 'text',
                label: 'private.training-organism.proposals.form.fields.code.label',
                placeholder: 'private.training-organism.proposals.form.fields.code.placeholder',
                help: 'private.training-organism.proposals.form.fields.code.help',
                icon: 'vpn_key',
                iconClass: 'order-first pr-1',
                colSpan: 4,
                removed: false,
            },
            {
                controlName: 'limitUsage',
                icon: 'confirmation_number',
                iconClass: 'order-first pr-1',
                type: 'number',
                label: 'private.training-organism.proposals.form.fields.limitUsage.label',
                placeholder: 'private.training-organism.proposals.form.fields.limitUsage.placeholder',
                help: 'private.training-organism.proposals.form.fields.limitUsage.help',
                error: 'private.training-organism.proposals.form.fields.limitUsage.error',
                colSpan: 4,
                removed: false
            },
            {
                controlName: 'expire',
                type: 'date',
                label: 'private.training-organism.proposals.form.fields.expire.label',
                placeholder: 'private.training-organism.proposals.form.fields.expire.placeholder',
                help: 'private.training-organism.proposals.form.fields.expire.help',
                error: 'private.training-organism.proposals.form.fields.expire.error',
                min: this.tomorrow,
                removable: true,
                colSpan: 4,
            },
            {
                controlName: 'trainingActions',
                type: 'infiniteSearch',
                searchMultiple: true,
                colSpan: 12,
                label: 'private.training-organism.proposals.form.fields.trainingActions.label',
                placeholder: 'private.training-organism.proposals.form.fields.trainingActions.placeholder',
                searchNoEntriesFoundLabel: 'private.training-organism.proposals.form.fields.trainingActions.noTrainingAction',
                chooseLabel: 'private.training-organism.proposals.form.fields.trainingActions.searching',
                parameters: {state: 'published', eligible: true, limit: 50},
                searchMethodPaginated: (params) => this._trainingActionService.list(params),
                searchResultFormatter: this.resultFormatterTrainingActions,
                change: (controlName, newValue, formData) => {
                    const appFormFieldIndicativeDuration = formData.find(field => field.controlName === 'indicativeDuration');
                    const appFormFieldSessionStartDate = formData.find(field => field.controlName === 'sessionStartDate');
                    const appFormFieldSessionEndDate = formData.find(field => field.controlName === 'sessionEndDate');
                    const appFormFieldDiscountType = formData.find(field => field.controlName === 'discountType');
                    const appFormFieldAmount = formData.find(field => field.controlName === 'amount');

                    appFormFieldSessionStartDate.removed = newValue === null || newValue.length !== 1;
                    appFormFieldSessionEndDate.removed = newValue === null || newValue.length !== 1;
                    appFormFieldDiscountType.value = null;
                    this.trainingActionSelected = newValue;
                    if (newValue && newValue.length === 1) {
                        appFormFieldDiscountType.choices = [{
                            key: this._translateService.instant('private.training-organism.proposals.form.fields.discountType.fixed'),
                            value: 'fixed'
                        }, ...discountTypes];
                        appFormFieldIndicativeDuration.removed = false;
                        appFormFieldIndicativeDuration.value = newValue[0].indicativeDuration;
                        appFormFieldAmount.removed = false;
                        appFormFieldAmount.label = this._translateService.instant('private.training-organism.proposals.form.fields.amount.labelDisabled');
                        appFormFieldAmount.placeholder = this.trainingActionSelected[0].totalTvaTTC?.toString() + ' €';
                        appFormFieldAmount.disabled = true;
                    } else {
                        appFormFieldDiscountType.choices = discountTypes;
                        appFormFieldIndicativeDuration.removed = true;
                        appFormFieldIndicativeDuration.value = null;
                    }

                    return [appFormFieldIndicativeDuration, appFormFieldSessionStartDate, appFormFieldSessionEndDate, appFormFieldAmount];
                }
            },
            {
                controlName: 'indicativeDuration',
                type: 'number',
                label: 'private.training-organism.proposals.form.fields.indicativeDuration.label',
                placeholder: 'private.training-organism.proposals.form.fields.indicativeDuration.placeholder',
                colSpan: 4,
                removed: true,
            },
            {
                controlName: 'sessionStartDate',
                type: 'date',
                label: 'private.training-organism.proposals.form.fields.sessionStartDate.label',
                placeholder: 'private.training-organism.proposals.form.fields.sessionStartDate.placeholder',
                colSpan: 4,
                removable: true,
                removed: true,
                min: this.minSessionStartDate,
                max: this.proposal?.sessionEndDate ?? null,
                change: (controlName, newValue, formData) => {
                    const appFormFieldSessionEndDate = formData.find(field => field.controlName === 'sessionEndDate');
                    appFormFieldSessionEndDate.min = newValue ?? this.minSessionStartDate;
                    return [appFormFieldSessionEndDate];
                }
            },
            {
                controlName: 'sessionEndDate',
                type: 'date',
                label: 'private.training-organism.proposals.form.fields.sessionEndDate.label',
                placeholder: 'private.training-organism.proposals.form.fields.sessionEndDate.placeholder',
                colSpan: 4,
                removable: true,
                removed: true,
                min: this.proposal?.sessionStartDate ?? this.minSessionStartDate,
            },
            {
                controlName: 'discountType',
                type: 'select',
                label: 'private.training-organism.proposals.form.fields.discountType.label',
                placeholder: 'private.training-organism.proposals.form.fields.discountType.placeholder',
                removable: true,
                colSpan: 6,
                choices: discountTypes,
                change: (controlName, newValue, formData) => {
                    const appFormFieldAmount = formData.find(field => field.controlName === 'amount');
                    appFormFieldAmount.removed = !newValue;
                    appFormFieldAmount.label = ' ';
                    appFormFieldAmount.disabled = false;
                    appFormFieldAmount.required = false;
                    appFormFieldAmount.placeholder = null;
                    appFormFieldAmount.value = null;
                    appFormFieldAmount.max = null;
                    appFormFieldAmount.min = 0;
                    appFormFieldAmount.validators = [];

                    const maxTrainingActionSelectedIncrease = (this.trainingActionSelected && this.trainingActionSelected.length === 1) ?
                        (this.trainingActionSelected[0].totalTvaTTC * 1.15) : null;
                    const maxTrainingActionSelectedDecrease = (this.trainingActionSelected && this.trainingActionSelected.length === 1) ?
                        this.trainingActionSelected[0]?.totalTvaTTC : null;

                    switch (newValue) {
                        case 'amount-increase':
                            appFormFieldAmount.required = true;
                            appFormFieldAmount.max = maxTrainingActionSelectedIncrease;
                            appFormFieldAmount.validators = [Validators.max(maxTrainingActionSelectedIncrease), Validators.min(0)];
                            appFormFieldAmount.validatorsMessages = {
                                max: this._translateService.instant(
                                    'private.training-organism.proposals.form.fields.discountType.errors.increase-max',
                                    {'value': maxTrainingActionSelectedIncrease + '€'}),
                                min: this._translateService.instant(
                                    'private.training-organism.proposals.form.fields.discountType.errors.increase-min',
                                    {'value': '0€'})
                            };
                            appFormFieldAmount.suffix = '€';
                            this.amountMinusPlus = '+';
                            break;
                        case 'amount-decrease':
                            appFormFieldAmount.required = true;
                            appFormFieldAmount.max = maxTrainingActionSelectedDecrease;
                            appFormFieldAmount.validators = [Validators.max(maxTrainingActionSelectedDecrease), Validators.min(0)];
                            appFormFieldAmount.validatorsMessages = {
                                max: this._translateService.instant(
                                    'private.training-organism.proposals.form.fields.discountType.errors.decrease-max',
                                    {'value': maxTrainingActionSelectedIncrease + '€'}),
                                min: this._translateService.instant(
                                    'private.training-organism.proposals.form.fields.discountType.errors.decrease-min',
                                    {'value': '0€'})
                            };
                            appFormFieldAmount.suffix = '€';
                            this.amountMinusPlus = '-';
                            break;
                        case 'percent-increase':
                            appFormFieldAmount.required = true;
                            appFormFieldAmount.max = 15;
                            appFormFieldAmount.validators = [Validators.max(15), Validators.min(0)];
                            appFormFieldAmount.validatorsMessages = {
                                max: this._translateService.instant(
                                    'private.training-organism.proposals.form.fields.discountType.errors.increase-max',
                                    {'value': '15%'}),
                                min: this._translateService.instant(
                                    'private.training-organism.proposals.form.fields.discountType.errors.increase-min',
                                    {'value': '0%'})
                            };
                            appFormFieldAmount.suffix = '%';
                            this.amountMinusPlus = '+';
                            break;
                        case 'percent-decrease':
                            appFormFieldAmount.required = true;
                            appFormFieldAmount.max = 100;
                            appFormFieldAmount.validators = [Validators.max(100), Validators.min(0)];
                            appFormFieldAmount.validatorsMessages = {
                                max: this._translateService.instant(
                                    'private.training-organism.proposals.form.fields.discountType.errors.decrease-max',
                                    {'value': '100%'}),
                                min: this._translateService.instant(
                                    'private.training-organism.proposals.form.fields.discountType.errors.decrease-min',
                                    {'value': '0%'})
                            };
                            appFormFieldAmount.suffix = '%';
                            this.amountMinusPlus = '-';
                            break;
                        case 'fixed':
                            appFormFieldAmount.required = true;
                            appFormFieldAmount.max = maxTrainingActionSelectedIncrease;
                            appFormFieldAmount.validators = [Validators.max(maxTrainingActionSelectedIncrease)];
                            appFormFieldAmount.placeholder = this.trainingActionSelected[0]?.totalTvaTTC?.toString();
                            appFormFieldAmount.validatorsMessages = {
                                max: this._translateService.instant(
                                    'private.training-organism.proposals.form.fields.discountType.errors.fixed-max',
                                    {'value': maxTrainingActionSelectedIncrease + '€'}),
                                min: this._translateService.instant(
                                    'private.training-organism.proposals.form.fields.discountType.errors.fixed-min',
                                    {'value': '0€'})
                            };
                            appFormFieldAmount.suffix = '€';
                            this.amountMinusPlus = '+';
                            break;
                        default:
                            appFormFieldAmount.suffix = null;
                            appFormFieldAmount.required = false;
                            appFormFieldAmount.max = null;
                            appFormFieldAmount.validators = [];
                            this.amountMinusPlus = '-';
                            if (this.trainingActionSelected && this.trainingActionSelected.length === 1) {
                                appFormFieldAmount.removed = false;
                                appFormFieldAmount.label = this._translateService.instant('private.training-organism.proposals.form.fields.amount.labelDisabled');
                                appFormFieldAmount.placeholder = this.trainingActionSelected[0].totalTvaTTC?.toString() + ' €';
                                appFormFieldAmount.disabled = true;
                            }
                            break;
                    }
                    return [appFormFieldAmount];
                }
            },
            {
                controlName: 'amount',
                type: 'price',
                floatLabel: 'always',
                label: ' ',
                colSpan: 6,
                removed: true,
                min: null,
                max: null,
            },
            {
                controlName: 'autoValidate',
                type: 'radio',
                label: 'private.training-organism.proposals.proposalCard.autoValidate',
                inline: true,
                colSpan: 12,
                choices: [
                    {
                        key: this._translateService.instant('common.actions.yes'),
                        value: true
                    },
                    {
                        key: this._translateService.instant('common.actions.no'),
                        value: false
                    }
                ]
            },
            {
                controlName: 'notes',
                type: 'textarea',
                label: 'private.training-organism.proposals.form.fields.notes.label',
                placeholder: 'private.training-organism.proposals.form.fields.notes.placeholder',
                colSpan: 12,
                icon: 'notes',
                iconClass: 'order-first self-start mt-3',
                validators: Validators['test']
            },
            {
                controlName: 'firstName',
                type: 'text',
                label: 'private.training-organism.proposals.form.fields.firstName.label',
                placeholder: 'private.training-organism.proposals.form.fields.firstName.placeholder',
                icon: 'account_circle',
                iconClass: 'order-first',
                removed: true,
                colSpan: 6
            },
            {
                controlName: 'lastName',
                type: 'text',
                label: 'private.training-organism.proposals.form.fields.lastName.label',
                placeholder: 'private.training-organism.proposals.form.fields.lastName.placeholder',
                icon: 'account_circle',
                iconClass: 'order-first',
                removed: true,
                colSpan: 6
            },
            {
                controlName: 'email',
                required: true,
                type: 'email',
                label: 'private.training-organism.proposals.form.fields.email.label',
                placeholder: 'private.training-organism.proposals.form.fields.email.placeholder',
                help: 'private.training-organism.proposals.form.fields.email.emailTooltip',
                icon: 'email',
                iconClass: 'order-first',
                removed: true,
                colSpan: 6
            },
            {
                controlName: 'phoneNumber',
                type: 'tel',
                label: 'private.training-organism.proposals.form.fields.phone.label',
                placeholder: 'private.training-organism.proposals.form.fields.phone.placeholder',
                help: 'private.training-organism.proposals.form.fields.phone.phoneNumberTooltip',
                icon: 'phone',
                iconClass: 'order-first',
                removed: true,
                colSpan: 6
            },
            {
                controlName: 'description',
                type: 'textarea',
                label: 'private.training-organism.proposals.form.fields.description.label',
                placeholder: 'private.training-organism.proposals.form.fields.description.placeholder',
                icon: 'notes',
                iconClass: 'order-first self-start mt-3',
                colSpan: 12
            },
            {
                controlName: 'tag',
                type: 'tags',
                label: 'common.tags.label',
                colSpan: 12,
                isCreateAvailable: false,
            }
        ];

        this.appFormFieldsData = appFormFieldsData.filter(value => value != null);
    }


    onSubmit(): void {
        this.loading = true;
        this.errorMessages = [];

        const proposalForm = this.formGroup.getRawValue().proposal;

        if (proposalForm.trainingActions && proposalForm.trainingActions.length !== 1) {
            proposalForm.sessionStartDate = null;
            proposalForm.sessionEndDate = null;
        }

        if (proposalForm.isIndividual) {
            proposalForm.limitUsage = 1;
            proposalForm.codeRequired = true;
        } else {
            // Needed in back
            proposalForm.lastName = null;
            proposalForm.firstName = null;
            proposalForm.phoneNumber = null;
            proposalForm.email = null;
        }

        switch (proposalForm.discountType) {
            case 'amount-increase':
            case 'amount-decrease':
                proposalForm.discountType = DiscountType.DISCOUNT_TYPE_AMOUNT;
                break;
            case 'percent-increase':
            case 'percent-decrease':
                proposalForm.discountType = DiscountType.DISCOUNT_TYPE_PERCENT;
                break;
            case 'fixed':
                proposalForm.discountType = DiscountType.DISCOUNT_TYPE_PRICE;
                break;
            default:
                proposalForm.discountType = DiscountType.DISCOUNT_TYPE_NONE;
                break;
        }

        if (proposalForm.discountType !== DiscountType.DISCOUNT_TYPE_NONE) {
            proposalForm.amount = parseInt(this.amountMinusPlus + proposalForm.amount, 10);
        } else {
            proposalForm.amount = null;
        }


        proposalForm.trainingActions = proposalForm.trainingActions?.map(trainingAction => {
            return {
                'externalId': trainingAction.externalId
            };
        });
        if (this.formGroup.valid) {
            this._proposalService.create(proposalForm).subscribe({
                next: (proposal) => {
                    this.proposalCreate.emit(proposal);
                    this.loading = false;
                    this.formGroup.enable();
                },
                error: (httpErrorResponse: HttpErrorResponse) => {
                    this.loading = false;
                    this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
                    this.formGroup.enable();
                }
            });
        } else {
            this.loading = false;
        }

    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    canDeactivate(): boolean {
        return !this.loading && (this.form?.submitted || !this.formGroup.dirty);
    }

}
