import {NgModule} from '@angular/core';
import {ProposalMenuComponent} from './proposal-menu/proposal-menu.component';
import {MaterialModule} from '../material/material.module';
import {TranslateModule} from '@ngx-translate/core';
import {ClipboardModule} from '@angular/cdk/clipboard';
import {CommonModule} from '@angular/common';
import {PipesModule} from '../pipes/pipes.module';
import {MarkdownModule} from 'ngx-markdown';
import {MatCardModule} from '@angular/material/card';
import {ProposalCreateComponent} from './proposal-create/proposal-create.component';
import {ProposalCardComponent} from './proposal-card/proposal-card.component';
import {ProposalTableComponent} from './proposal-table/proposal-table.component';
import {ReactiveFormsModule} from '@angular/forms';
import {MatTooltipModule} from '@angular/material/tooltip';
import {NgxMatSelectSearchModule} from 'ngx-mat-select-search';
import {MatChipsModule} from '@angular/material/chips';
import {RouterModule} from '@angular/router';

@NgModule({
    declarations: [
        ProposalMenuComponent,
        ProposalCreateComponent,
        ProposalCardComponent,
        ProposalTableComponent
    ],
    imports: [
        CommonModule,
        MaterialModule,
        TranslateModule,
        ClipboardModule,
        PipesModule,
        MarkdownModule,
        MatCardModule,
        ReactiveFormsModule,
        MatTooltipModule,
        NgxMatSelectSearchModule,
        MatChipsModule,
        RouterModule
    ],
    exports: [
        ProposalMenuComponent,
        ProposalCreateComponent,
        ProposalCardComponent,
        ProposalTableComponent,
    ]
})
export class ProposalModule {
}

