import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {ExportFoldersComponent} from './export-folder/export-folders.component';
import {TranslateModule} from '@ngx-translate/core';
import {MaterialModule} from '../material/material.module';
import {FileModule} from '../file/file.module';
import {ReactiveFormsModule} from '@angular/forms';

@NgModule({
    declarations: [ExportFoldersComponent],
    imports: [
        CommonModule,
        TranslateModule,
        MaterialModule,
        FileModule,
        ReactiveFormsModule
    ],
    exports: [ExportFoldersComponent]
})
export class ExportCsvModule {
}
