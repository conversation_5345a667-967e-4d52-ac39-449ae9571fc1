import {Component, Inject, OnInit} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material/dialog';
import {FileService} from '../../api/services/file.service';
import {FormArray, FormBuilder, FormControl, FormGroup, Validators} from '@angular/forms';
import {ApiError} from '../../errors/errors.types';
import {HttpErrorResponse} from '@angular/common/http';

@Component({
    selector: 'app-export-folders',
    templateUrl: './export-folders.component.html',
    styleUrls: ['./export-folders.component.scss']
})
export class ExportFoldersComponent implements OnInit {

    loading = false;
    formGroup: FormGroup;
    formArray: FormArray;
    columnEntries: { name: string }[];
    errorMessages: string[] = [];

    constructor(
        public dialogRef: MatDialogRef<ExportFoldersComponent>,
        private _fileService: FileService,
        private _formBuilder: FormBuilder,
        @Inject(MAT_DIALOG_DATA) public _dialogData: {
            title: string,
            url: string,
            filters: { query?: string, [p: string]: string | string[] } | null,
            query: string | null,
            columns: string[],
            idOrName: string
        }
    ) {
    }

    ngOnInit(): void {
        const defaultSelection = true;
        this.columnEntries = this._dialogData.columns.map((title) => ({name: title}));
        this.formArray = new FormArray(this.columnEntries.map(() => new FormControl(defaultSelection)));
        this.formGroup = this._formBuilder.group({
            limit: [1000, Validators.max(1000)], // TODO max or no max ? that is the question
            columns: this.formArray,
            checkAllColumns: [defaultSelection]
        });
    }

    closeModal(): void {
        this.dialogRef.close();
    }

    checkAll(checked: boolean): void {
        this.formArray.controls.forEach((control) => control.setValue(checked));
    }

    selectedColumnCount(): number {
        return this.formGroup.value.columns.filter((checked) => checked).length;
    }

    download(): void {
        this.errorMessages = [];
        const selectedColumns = this.formGroup.value.columns
            .map((checked, i) => checked ? this.columnEntries[i].name : null)
            .filter(v => v !== null);
        const queryParams: { [param: string]: string } = {
            format: 'csv',
            limit: this.formGroup.get('limit').value,
            ...this._dialogData.filters
        };
        if (selectedColumns.length) {
            queryParams.csvColumns = selectedColumns.join(',');
        }
        if (this._dialogData.query) {
            queryParams.query = this._dialogData.query;
        }
        this.loading = true;
        this._fileService.download('/api/' + this._dialogData.url, this._dialogData.idOrName, null, queryParams).subscribe({
                next: () => {
                    this.loading = false;
                },
                error: (httpErrorResponse: HttpErrorResponse) => {
                    this.loading = false;
                    this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
                }
            }
        );
    }
}
