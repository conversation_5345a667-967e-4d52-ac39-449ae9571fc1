<app-dialog-layout [title]="_dialogData.title"
                   [actions]="actions" [errorMessages]="errorMessages"
                   (dialogClose)="closeModal()">

    <form [formGroup]="formGroup" class="flex flex-col">
        <h5>
            <mat-checkbox formControlName="checkAllColumns" class="mr-2"
                          (change)='checkAll($event.checked)'
                          [checked]="selectedColumnCount() === columnEntries.length"
                          [indeterminate]="selectedColumnCount() > 0 && selectedColumnCount() < columnEntries.length">
                {{'common.actions.export.allColumns' | translate}}
            </mat-checkbox>
        </h5>
        <ng-container formArrayName="columns"
                      *ngFor="let column of formArray.controls; let i = index">
            <mat-checkbox [formControlName]="i"
                          class="mr-2">
                {{ columnEntries[i].name }}
            </mat-checkbox>
        </ng-container>
        <mat-form-field class="flex-auto mt-4 mb-2">
            <mat-label> {{'common.actions.export.limit' | translate}}</mat-label>
            <input max="1000" type="number" matInput formControlName="limit">
        </mat-form-field>
        <ng-template #actions>
            <div class="flex flex-row py-2 mb-2">
                <button type="submit" color="primary" class="mt-2"
                        mat-flat-button (click)="download()">
                    <mat-progress-spinner class="mr-4" *ngIf="loading" [diameter]="24"
                                          mode="indeterminate"></mat-progress-spinner>
                    <ng-container
                        *ngIf="!loading">{{ 'common.actions.export.exportCsvButton' | translate }}
                    </ng-container>
                </button>
            </div>
        </ng-template>
    </form>
</app-dialog-layout>
