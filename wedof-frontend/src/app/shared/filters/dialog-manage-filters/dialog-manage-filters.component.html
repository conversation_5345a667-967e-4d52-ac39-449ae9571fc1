<app-dialog-layout [title]="'private.common.filters.table.title' | translate"
                   [actions]="save"
                   cancelText="{{'common.actions.close' | translate}}"
                   (dialogClose)="closeDialog()">
    <div>
        <table #table mat-table [dataSource]="filters" [cdkDropListData]="filters" cdkDropList
               (cdkDropListDropped)="updateOrder($event)">
            <ng-container matColumnDef="position">
                <th mat-header-cell *matHeaderCellDef></th>
                <td mat-cell *matCellDef="let element" class="w-16">
                    <button type="button" mat-icon-button>
                        <mat-icon cdkDragHandle>format_list_bulleted</mat-icon>
                    </button>
                </td>
            </ng-container>
            <ng-container matColumnDef="name">
                <th mat-header-cell *matHeaderCellDef>{{'private.common.filters.table.filterName' | translate}}</th>
                <td mat-cell *matCellDef="let filter, let id = index" class="w-80">
                    <input class="ml-5"
                           matInput [value]="filter.name"
                           maxlength="255"
                           (change)="updateName(id, $event.target.value)"
                           type="text"/>
                </td>
            </ng-container>
            <ng-container matColumnDef="color">
                <th mat-header-cell *matHeaderCellDef>{{'private.common.filters.table.filterColor' | translate}}</th>
                <td mat-cell *matCellDef="let filter, let id = index"  class="w-28">
                    <input matInput [value]="filter.color ? filter.color : '#f2e5f2'"
                           (change)="updateColor(id, $event.target.value)"
                           type="color" class="w-16"/>
                </td>
            </ng-container>
            <ng-container matColumnDef="actions">
                <th class="text-right" mat-header-cell
                    *matHeaderCellDef>{{'private.common.filters.table.actions' | translate}}</th>
                <td mat-cell *matCellDef="let id = index"  class="w-16">
                    <button mat-icon-button (click)="deleteFilter(id)">
                        <mat-icon svgIcon="delete"></mat-icon>
                    </button>
                </td>
            </ng-container>
            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns;" cdkDrag [cdkDragData]="row"></tr>
        </table>
    </div>

    <ng-template #save>
        <button type="submit" color="primary"
                [disabled]="disabled"
                mat-flat-button
                (click)="saveFilters()">{{ 'common.actions.update' | translate }}
        </button>
    </ng-template>
</app-dialog-layout>
