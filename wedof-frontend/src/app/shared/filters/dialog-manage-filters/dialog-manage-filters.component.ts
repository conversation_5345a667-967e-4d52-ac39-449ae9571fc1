import {Component, Inject, OnInit, ViewChild} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material/dialog';
import {Filter} from '../../api/models/user';
import {Store} from '@ngxs/store';
import {CdkDragDrop, moveItemInArray} from '@angular/cdk/drag-drop';
import {MatTable} from '@angular/material/table';
import {Observable, Subject} from 'rxjs';
import {cloneDeep} from 'lodash-es';
import {UpdateUserFilters} from '../../api/state/user.state';
import {EntityClass} from '../../utils/enums/entity-class';

@Component({
    selector: 'app-dialog-manage-filters',
    templateUrl: './dialog-manage-filters.component.html',
    styleUrls: ['./dialog-manage-filters.component.scss']
})
export class DialogManageFiltersComponent implements OnInit {

    displayedColumns: string[];
    filters: Filter[];
    disabled: boolean;
    dirty$: Subject<boolean> = new Subject<boolean>();

    @ViewChild('table') table: MatTable<Filter[]>;

    constructor(private _store: Store,
                @Inject(MAT_DIALOG_DATA) public dialogData: {
                    filters$: Observable<Filter[]>,
                    entityClass: EntityClass
                },
                public dialogRef: MatDialogRef<DialogManageFiltersComponent>) {
        this.displayedColumns = ['position', 'name', 'color', 'actions'];
        this.dirty$.subscribe((dirty) => {
            dialogRef.disableClose = dirty;
            this.disabled = !dirty;
        });
    }

    ngOnInit(): void {
        this.dialogData.filters$.subscribe((filters) => {
            this.filters = cloneDeep(filters);
            this.dirty$.next(false);
        });
    }

    closeDialog(): void {
        this.dialogRef.close();
    }

    deleteFilter(filterIndex: number): void {
        this.filters.splice(filterIndex, 1);
        this.dirty$.next(true);
        this.table.renderRows();
    }

    updateColor(filterIndex: number, color: string): void {
        this.filters[filterIndex].color = color;
        this.dirty$.next(true);
        this.table.renderRows();
    }

    updateName(filterIndex: number, name: string): void {
        this.filters[filterIndex].name = name;
        this.dirty$.next(true);
        this.table.renderRows();
    }

    updateOrder(event: CdkDragDrop<Filter[]>): void {
        const prevIndex = this.filters.findIndex((filter) => filter === event.item.data);
        moveItemInArray(this.filters, prevIndex, event.currentIndex);
        this.dirty$.next(true);
        this.table.renderRows();
    }

    saveFilters(): void {
        this._store.dispatch(new UpdateUserFilters(this.filters, this.dialogData.entityClass)).subscribe(() => this.closeDialog());
    }
}
