import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FiltersBarComponent} from './filters-bar/filters-bar.component';
import {MatTooltipModule} from '@angular/material/tooltip';
import {MaterialModule} from '../material/material.module';
import {EntityFiltersComponent} from './entity-filters/entity-filters.component';
import {DialogManageFiltersComponent} from './dialog-manage-filters/dialog-manage-filters.component';
import {TranslateModule} from '@ngx-translate/core';
import {DragDropModule} from '@angular/cdk/drag-drop';


@NgModule({
    declarations: [FiltersBarComponent, EntityFiltersComponent, DialogManageFiltersComponent],
    exports: [FiltersBarComponent, EntityFiltersComponent],
    imports: [
        CommonModule,
        MatTooltipModule,
        MaterialModule,
        TranslateModule,
        DragDropModule
    ]
})
export class FiltersModule {
}
