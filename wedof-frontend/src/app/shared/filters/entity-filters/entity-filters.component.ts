import {Component, HostBinding, Input, OnD<PERSON>roy, OnInit} from '@angular/core';
import {EntityClass} from '../../utils/enums/entity-class';
import {Filter} from '../../api/models/user';
import {Store} from '@ngxs/store';
import {UserState} from '../../api/state/user.state';
import {Subject} from 'rxjs';
import {takeUntil} from 'rxjs/operators';
import {TranslateService} from '@ngx-translate/core';

@Component({
    selector: 'app-entity-filters',
    templateUrl: './entity-filters.component.html',
    styleUrls: ['./entity-filters.component.scss']
})
export class EntityFiltersComponent implements OnInit, OnDestroy {

    @HostBinding('class') class = 'flex-grow';

    filteredFilters: Filter[];

    private _unsubscribeAll: Subject<any> = new Subject();

    @Input() entityClass: EntityClass;

    constructor(private _store: Store,
                private _translateService: TranslateService) {
    }

    ngOnInit(): void {
        this._store.select(UserState.userFilteredFilters(this.entityClass)).pipe(takeUntil(this._unsubscribeAll))
            .subscribe((filters) => {
                this.filteredFilters = [
                    {
                        name:  this._translateService.instant('private.common.filters.noFilter'),
                        link: '',
                        entityClass: this.entityClass
                    },
                    ...filters
                ];
            });
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }
}
