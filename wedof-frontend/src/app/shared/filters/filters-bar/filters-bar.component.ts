import {
    AfterViewInit,
    Component,
    ElementRef,
    HostBinding,
    Input, NgZone,
    OnChanges, OnDestroy,
    Renderer2,
    SimpleChanges,
    ViewChild
} from '@angular/core';
import {Filter} from '../../api/models/user';
import {Router} from '@angular/router';
import {isLight} from '../../utils/color-utils';

@Component({
    selector: 'app-filters-bar',
    templateUrl: './filters-bar.component.html',
    styleUrls: ['./filters-bar.component.scss']
})
export class FiltersBarComponent implements OnChanges, AfterViewInit, OnDestroy {

    @Input() filters: Filter[];
    @HostBinding('class') class = 'flex-grow';
    @ViewChild('menuContainer') menuContainer: ElementRef;

    visibleFiltersCount = 0;
    resizeObserver: { disconnect(): void; observe(target: Element): void; unobserve(target: Element): void; }; // The type does not exist in Angular 10, will be fixed in 11

    constructor(private _router: Router,
                private renderer: Renderer2,
                private zone: NgZone) {
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes.filters && !changes.filters.isFirstChange()) {
            this.computeDisplayedShorcuts();
        }
    }

    ngAfterViewInit(): void {
        const ResizeObserver = (window as any).ResizeObserver; // The type does not exist in Angular 10, will be fixed in 11
        if (ResizeObserver) {
            this.resizeObserver = new ResizeObserver(() => {
                this.zone.run(() => {
                    this.computeDisplayedShorcuts();
                });
            });
            this.resizeObserver.observe(this.menuContainer.nativeElement);
        } else {
            // Fallback at least for first display if browser does not support it
            setTimeout(() => {
                this.computeDisplayedShorcuts();
            });
        }
    }

    private computeDisplayedShorcuts(): void {
        const margin = 55; // Space for the dropdown + "room" for error
        const containerWidth = this.menuContainer.nativeElement.clientWidth - margin;
        let accumulatedWidth = 0;
        this.visibleFiltersCount = 0;
        for (const filter of this.filters) {
            accumulatedWidth += this.getEstimatedFilterWidth(this.truncate(filter.name));
            if (accumulatedWidth <= containerWidth) {
                this.visibleFiltersCount++;
            } else {
                break;
            }
        }
    }

    private getEstimatedFilterWidth(text: string): number {
        const dummySpan = this.renderer.createElement('div');
        this.renderer.appendChild(dummySpan, this.renderer.createText(text));
        // To change if style of filter-button changes
        this.renderer.setStyle(dummySpan, 'visibility', 'hidden');
        this.renderer.setStyle(dummySpan, 'position', 'absolute');
        this.renderer.setStyle(dummySpan, 'word-wrap', 'no-wrap');
        this.renderer.setStyle(dummySpan, 'font-size', '12px');
        this.renderer.setStyle(dummySpan, 'font-weight', '600');
        this.renderer.setStyle(dummySpan, 'padding', '4px');
        this.renderer.setStyle(dummySpan, 'border', '2px solid black');
        document.body.appendChild(dummySpan);
        const width = dummySpan.offsetWidth;
        document.body.removeChild(dummySpan);
        const buttonMargin = 8;
        return width + buttonMargin;
    }

    truncate(text: string, maxLength = 24): string {
        return text.length > maxLength ? (text.substring(0, maxLength) + '...') : text;
    }

    hasHiddenFilters(): boolean {
        return this.isHiddenFilter(this.filters.length - 1);
    }

    isHiddenFilter(index: number): boolean {
        return index >= this.visibleFiltersCount;
    }

    applyFilter(link: string): void {
        const URL = this._router.url.split('?')[0];
        const queryParams = {};
        if (link) {
            const params = new URLSearchParams(link);
            params.forEach((value, key) => {
                queryParams[key] = value;
            });
        }
        this._router.navigate([URL], {queryParams: queryParams});
    }

    isFilterActive(filter: Filter): boolean {
        const splitedUrl = this._router.url.split('?');
        return filter.link === (splitedUrl.length > 1 ? splitedUrl[1] : '');
    }

    isBackgroundLight(color?: string): boolean {
        return !color ? true : isLight(color);
    }

    ngOnDestroy(): void {
        this.resizeObserver?.disconnect();
    }
}
