<div #menuContainer
     class="menuContainer flex items-center flex-grow">
    <button *ngFor="let filter of filters, let index = index"
            (click)="applyFilter(filter.link)"
            class="filter-button flex ml-2"
            [ngClass]="{'hidden': isHiddenFilter(index), 'filter-active': isFilterActive(filter), 'text-white': !isBackgroundLight(filter.color) }"
            [style.background-color]="filter.color ? filter.color : '#DCDCE8FF'"
            [style.border-color]="filter.color ? filter.color : '#DCDCE8FF'"
            [matTooltip]="filter.name">
        {{truncate(filter.name)}}
    </button>
    <div [hidden]="!hasHiddenFilters()"
         class="ml-2">
        <button mat-icon-button
                [matMenuTriggerFor]="hiddenFilters">
            <mat-icon>keyboard_arrow_down</mat-icon>
        </button>
        <mat-menu #hiddenFilters="matMenu">
            <button *ngFor="let filter of filters; let index = index"
                    mat-menu-item
                    (click)="applyFilter(filter.link)"
                    class="filter-hidden"
                    [style.border-left-color]="filter.color ? filter.color : '#DCDCE8FF'"
                    [ngClass]="{'hidden': !isHiddenFilter(index), 'filter-active': isFilterActive(filter)}">
                {{filter.name}}
            </button>
        </mat-menu>
    </div>
</div>
