import {Injectable} from '@angular/core';
import {environment} from 'environments/environment';
import {Organism} from '../api/models/organism';

@Injectable()
export class EnvironmentService {

    constructor() {
    }

    isEnableBetaFeatures(organism?: Organism): boolean {
        return environment.enableBetaFeatures || organism?.siret === '53222292400039' || organism?.siret === '90301927100019';
    }

    getWedofBaseUri(): string {
        return environment.wedofBaseUri;
    }
}
