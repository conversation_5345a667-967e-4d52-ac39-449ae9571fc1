import {Component, ElementRef, Input, OnChanges, OnDestroy, SimpleChanges, ViewChild} from '@angular/core';
import {Activity, ActivityTypes} from '../../api/models/activity';
import {MatDialog} from '@angular/material/dialog';
import {ActivityService} from '../../api/services/activity.service';
import {Observable, Subject} from 'rxjs';
import {
    TableFilter,
    TableMultipleFilterComponent
} from '../../material/table/table-multiple-filter/table-multiple-filter.component';
import {ActivityTypeToIconPipe} from '../../pipes/activity-type-to-icon.pipe';
import {TranslateService} from '@ngx-translate/core';
import {filter, finalize, switchMap, takeUntil} from 'rxjs/operators';
import QualiopiIndicators from '../../../../assets/qualiopi/indicateur.json';
import {HttpErrorResponse} from '@angular/common/http';
import {daysAgo} from '../../utils/date-utils';
import {MatSnackBar} from '@angular/material/snack-bar';
import {SnackBarComponent} from '../../material/snack-bar/snack-bar.component';
import {displayTextSnackBar} from '../../utils/displayTextSnackBar';
import {ApiError} from '../../errors/errors.types';
import {EntityClass} from '../../utils/enums/entity-class';
import {Select} from '@ngxs/store';
import {UserState} from '../../api/state/user.state';
import {User} from '../../api/models/user';
import {DeletionConfirmationComponent} from '../../material/action-confirmation/deletion-confirmation.component';
import {BaseCardComponentDirective, RequiredCallSuper} from '../../utils/base-card/base-card.directive';
import {ActivityFormComponent} from '../activity-form/activity-form.component';
import {FileService} from '../../api/services/file.service';

type QualiopiIndicator = { name: string, value: { key: string, value: number }[] };

@Component({
    selector: 'app-activities-card',
    templateUrl: './activities-card.component.html',
    styleUrls: ['./activities-card.component.scss']
})
export class ActivitiesCardComponent extends BaseCardComponentDirective implements OnChanges, OnDestroy {
    static COMPONENT_ID = 'activites';
    cardLoading = false;
    activities: Activity[];
    activitiesType = ActivityTypes;
    total = 0;
    activitieslimit = 6;
    newActivityCreated: Activity;
    typeFilters: TableFilter[];
    filters: any;
    qualiopiIndicators: typeof QualiopiIndicators;
    hoverActivity: number;
    user: User;
    exportingCSV = false;

    private _unsubscribeAll: Subject<void> = new Subject();

    @Input() entityId: number | string;
    @Input() entityClass: string;
    @Input() entity: object;
    @ViewChild('typeFilterSelector') private typeTableMultipleFilterComponentState: TableMultipleFilterComponent;
    @Select(UserState.user) user$: Observable<User>;

    constructor(private _snackBar: MatSnackBar,
                private _activityService: ActivityService,
                private _activityTypeToIconPipe: ActivityTypeToIconPipe,
                private _translateService: TranslateService,
                private _dialog: MatDialog,
                private _fileService: FileService,
                private _el: ElementRef
    ) {
        super(ActivitiesCardComponent.COMPONENT_ID, _el);
        this.qualiopiIndicators = QualiopiIndicators;
    }

    ngOnChanges(changes: SimpleChanges): void {
        this.user$.pipe(takeUntil(this._unsubscribeAll)).subscribe(user => this.user = user);
        this.panelLoading();
        this.initFilters();
        this.filters = {limit: this.activitieslimit};
        this.init(this.filters);
    }

    initFilters(): void {
        this.typeFilters = [{
            label: this._translateService.instant('private.training-organism.common.activities.form.activityType.task'),
            type: 'header'
        }];
        this.typeFilters.push({
            label: 'private.training-organism.common.activities.form.label.done',
            value: 'done'
        });
        this.typeFilters.push({
            label: this._translateService.instant('private.training-organism.common.activities.form.activityType.activity'),
            type: 'header'
        });
        Object.values(ActivityTypes).forEach(value => {
            this.typeFilters.push({
                label: `private.training-organism.common.activities.type.${value}`,
                value: value,
                icon: this._activityTypeToIconPipe.transform(value, this.entityClass, null),
                color: 'primary',
            });
        });
        if (this.entityClass !== EntityClass.CERTIFICATIONS_PARTNER) {
            const filters = [];
            Object.values(this.qualiopiIndicators).forEach((qualiopi: QualiopiIndicator) => {
                const data = [];
                qualiopi.value.forEach((indicator) => {
                    data.push({
                        label: indicator.key,
                        value: indicator.value
                    });
                });
                filters.push({
                    label: qualiopi.name,
                    value: data,
                });
            });
            this.typeFilters.push({
                label: this._translateService.instant('private.training-organism.common.activities.form.label.qualiopi'),
                type: 'header'
            });
            this.typeFilters.push(...filters);
        }
    }

    daysAgo(date: Date): string {
        const getDays = daysAgo(date);
        return getDays === 0 ? 'Aujourd\'hui' : getDays > 0 ? 'Il y a ' + getDays + ' jours' : 'Dans ' + -getDays + ' jours';
    }

    daysDueDate(date: Date): string {
        const getDays = daysAgo(date);
        return 'Terminer au plus tard ' + (getDays === 0 ? 'aujourd\'hui' : getDays > 0 ? 'il y a ' + getDays + ' jours' : 'dans ' + -getDays + ' jours');
    }

    init(params: {}): void {
        this._activityService.listByEntity(this.entityClass, this.entityId, params)
            .subscribe((activities) => {
                this.activities = activities.payload;
                this.total = activities.total;
                this.panelLoaded();
                this.hoverActivity = 0;
            });
    }

    onScroll(): void {
        const displayedPages = this.activities.length / this.activitieslimit;
        if (Number.isInteger(displayedPages)) {
            this._activityService.listByEntity(this.entityClass, this.entityId, {
                ...this.filters,
                page: displayedPages + 1
            })
                .pipe(takeUntil(this._unsubscribeAll))
                .subscribe((response) => {
                    this.activities.push(...response.payload);
                });
        }
    }

    onFilterChange(filters: string[]): void {
        const filtersType = [];
        const filtersQualiopiIndicators = [];
        this.filters = {limit: this.activitieslimit};
        filters.forEach((activityFilter) => {
            if (activityFilter === 'qualiopi') {
                this.filters.qualiopi = true;
            } else if (activityFilter === 'done') {
                this.filters.done = false;
            } else if (typeof activityFilter === 'number') {
                filtersQualiopiIndicators.push(activityFilter);
            } else if (activityFilter) {
                filtersType.push(activityFilter);
            }
        });
        if (filtersType.length) {
            this.filters.type = filtersType.join(',');
        }
        if (filtersQualiopiIndicators.length) {
            this.filters.qualiopiIndicators = filtersQualiopiIndicators.join(',');
        }
        this.init(this.filters);
    }

    ngOnDestroy(): RequiredCallSuper {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
        return super.ngOnDestroy();
    }

    getQualiopiIndicatorsLabel(activity: Activity): string {
        const activityIndicators = activity.qualiopiIndicators;
        let labelToDisplay = '';
        activityIndicators.forEach((activityIndicator) => {
            Object.values(this.qualiopiIndicators).map((qualiopiIndicator: QualiopiIndicator) => {
                qualiopiIndicator.value.map((indicator) => {
                    if (indicator.value === activityIndicator) {
                        labelToDisplay += indicator.key + '; ';
                    }
                });
            });
        });
        return labelToDisplay.slice(0, -2);
    }

    changeHoverActivity(id: number): void {
        this.hoverActivity = id;
    }

    updateActivityDone(activity: Activity): void {
        this._activityService.update({
                eventTime: activity.eventTime ?? new Date(),
                eventEndTime: activity.eventEndTime ?? new Date(),
                done: true
            },
            activity.id).subscribe({
            next: (activityResponse) => {
                this.processedActivity(activityResponse);
                this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(this._translateService.instant('private.common.activities.isDone', {
                    task: activityResponse.title
                })));
            },
            error: (httpErrorResponse: HttpErrorResponse) => {
                this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar((httpErrorResponse.error as ApiError).errorMessages.toString(), 5000, 'red'));
            }
        });
    }

    processedActivity(activityToUpdate: Activity): void {
        this.activities = this.activities.map((activity) => {
            return activity.id === activityToUpdate.id ? activityToUpdate : activity;
        });
    }

    update(activity: Activity): void {
        const dialogRef = this._dialog.open(ActivityFormComponent, {
            panelClass: ['full-page-scroll-50'],
            data: {
                activityToUpdate: activity
            }
        });
        dialogRef.afterClosed().subscribe(res => {
            if (res?.data) {
                this.processedActivity(res?.data);
            }
        });
    }

    createdActivity(createdActivity: Activity): void {
        this.newActivityCreated = createdActivity;
        this.activities.unshift(this.newActivityCreated);
        this.total = this.total + 1;
    }

    remove(removedActivity: Activity): void{
        this.activities = this.activities.filter((activity) => {
            return activity.id !== removedActivity.id;
        });
    }

    delete(deletedActivity: Activity): void {
        const dialogRef = this._dialog.open(DeletionConfirmationComponent, {
            panelClass: 'full-page-scroll-40',
            height: 'auto',
            data: {
                messageKey: 'private.common.activities.confirmDeletion',
                data: deletedActivity
            }
        });
        dialogRef.componentInstance.actionValue$.pipe(
            filter((confirmation: boolean) => confirmation),
            switchMap(() => this._activityService.delete(deletedActivity.id)),
            finalize(() => {
                dialogRef.componentInstance.close();
            })
        ).subscribe(() => {
            this.remove(deletedActivity);
        });
    }

    canBeModified(activity = null): boolean {
        const typesNotAllowed = ['updateState', 'create', 'update', 'file', 'progress', 'cdc'];
        return this.user.email === activity._links?.user?.email && !typesNotAllowed.includes(activity.type);
    }

    canAccessMenu(activity = null): boolean {
        const typesNotAllowed = ['updateState', 'create', 'update', 'file', 'progress', 'cdc'];
        return !typesNotAllowed.includes(activity.type);
    }

    openDialogExport(): void {
        this.exportingCSV = true;
        const queryParams: { [param: string]: string } = {
            format: 'csv',
            ...this.filters
        };
        const fileName = 'Activités_' + this._translateService.instant('private.common.message-templates.entityClass.value.' + this.entityClass) + '_' + this.entityId;
        this._fileService.download('/api/activities/' + this.entityClass + '/' + this.entityId, fileName, null, queryParams).subscribe(
            (blob) => {
                if (blob.state === 'PENDING') {
                    this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(
                        this._translateService.instant('common.actions.export.exportStarted'), 0));
                } else if (blob.state === 'DONE') {
                    this.exportingCSV = false;
                    this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(
                        this._translateService.instant('common.actions.export.exportFinished')));
                }
            }
        );
    }
}
