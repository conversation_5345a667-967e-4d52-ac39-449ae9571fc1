<mat-card class="flex h-full flex-auto flex-col mt-3 p-5 pb-0 pt-3 border shadow-none sm"
          [ngClass]="{'card-loading':cardLoading}">

    <div class="flex items-center">
        <mat-icon
            [matBadge]="total > 0 ? total.toString() : null"
            matBadgePosition="below after"
            matBadgeSize="small"
            class="mr-3 card-loading-show text-4xl"
            color="primary"
            svgIcon="format_list_bulleted"></mat-icon>
        <div class="flex items-center">
            <span
                class="text-xl font-semibold card-loading-show">{{ 'private.common.activities.title' | translate }}  </span>
        </div>
        <div class="ml-auto -mr-4 card-loading-show flex">
            <app-table-multiple-filter class="mr-1" #typeFilterSelector
                                       [filters]="typeFilters"
                                       [title]="'type'"
                                       (selectFilters)="onFilterChange($event)">
            </app-table-multiple-filter>
            <app-activity-form-menu [entityClass]="entityClass"
                                    [entityId]="entityId"
                                    (createdActivity)="createdActivity($event)">
            </app-activity-form-menu>
            <button mat-icon-button
                    (click)="$event.stopPropagation()"
                    [matMenuTriggerFor]="actionsMenu"
                    title="Actions">
                <mat-icon svgIcon="more_vert"></mat-icon>
            </button>
            <mat-menu #actionsMenu="matMenu">
                <ng-template matMenuContent>
                    <button mat-menu-item [disabled]="exportingCSV"
                            (click)="openDialogExport()">
                        {{ 'common.actions.export.exportCsvButton' | translate }}
                    </button>
                </ng-template>
            </mat-menu>
        </div>
    </div>

    <div *ngIf="!cardLoading" class="flex flex-col mt-3 mb-3 overflow-y-auto"
         infiniteScroll
         [infiniteScrollDistance]="2"
         [infiniteScrollThrottle]="50"
         (scrolled)="onScroll()"
         [scrollWindow]="false">
        <div *ngIf="activities.length; else noActivity">
            <div *ngFor="let activity of activities" class="mb-2">
                <div class="flex flex-row justify-between"
                     (click)="canBeModified(activity) ? update(activity) : null"
                     [class]="canBeModified(activity) ? 'cursor-pointer' : ''"
                     (mouseenter)="changeHoverActivity(activity.id)"
                     (mouseleave)="changeHoverActivity(0)">
                    <div class="flex items-center">
                        <mat-icon
                            *ngIf="hoverActivity !== activity.id || activity.eventEndTime || !canBeModified(activity) "
                            [ngClass]=" { 'text-primary' : activity.eventEndTime === null} "
                            title="{{activity.type}}"
                            class="mr-1 mt-1 icon-size-18">
                            {{
                            activity.type === activitiesType.UPDATE_STATE ?
                                (activity.type | activityTypeToIcon : entityClass : activity.newValue) :
                                (activity.type | activityTypeToIcon : null : null)
                            }}
                        </mat-icon>

                        <div
                            *ngIf="hoverActivity === activity.id && activity.eventEndTime === null && canBeModified(activity)">
                            <button (click)="updateActivityDone(activity); $event.stopPropagation()">
                                <mat-icon [svgIcon]="'check_circle_outline'" [color]="'primary'"
                                          class="mr-1 mt-1 icon-size-18"
                                          [matTooltip]="'private.common.activities.markAsDone' | translate"></mat-icon>
                            </button>
                        </div>

                        <div class="pl-1">
                            <a class="flex items-center" [href]="activity.link" target="_blank"
                               *ngIf="activity.link else defaultTitle">{{ activity.title }}
                                <mat-icon [svgIcon]="'open_in_new'" class="icon-copy"></mat-icon>
                            </a>
                            <ng-template #defaultTitle>{{ activity.title }}</ng-template>
                            <div class="text-secondary whitespace-pre-wrap"
                                 *ngIf="activity.description">{{ activity.description }}
                            </div>
                            <div class="text-secondary whitespace-pre-wrap"
                                 *ngIf="activity.qualiopiIndicators?.length">{{ getQualiopiIndicatorsLabel(activity) }}
                            </div>
                            <div class="text-disabled text-sm ">
                            <span matTooltipPosition="above"
                                  *ngIf="activity.eventTime && (!activity.dueDate || activity.eventEndTime); else showDueDate"
                                  [matTooltip]="activity.eventTime | date:'dd/MM/yyyy HH:mm'">
                                {{ daysAgo(activity.eventTime) }}
                            </span>
                                <ng-template #showDueDate>
                                    <span matTooltipPosition="above" *ngIf="activity.dueDate"
                                          [matTooltip]="activity.dueDate | date:'dd/MM/yyyy HH:mm'">
                                {{ daysDueDate(activity.dueDate) }}
                            </span>
                                </ng-template>

                                <span *ngIf="activity._links?.user?.name || activity.origin"
                                      class="text-disabled text-sm ">
                                {{ 'private.common.activities.by' | translate:{by: activity._links?.user?.name ? activity._links.user.name : activity.origin} }}
                            </span>
                            </div>
                        </div>
                    </div>
                    <app-activity-menu
                        *ngIf="canAccessMenu(activity)"
                        [activity]="activity"
                        [canBeModified]="canBeModified(activity)"
                        (deleteActivityTask)="delete($event)"
                        (moveActivity)="remove($event)"
                        (processedActivity)="processedActivity(activity)"
                    ></app-activity-menu>

                </div>
            </div>
        </div>

        <ng-template #noActivity>
            <p class="mt-3 mb-3 text-center m-auto">
                {{ 'private.training-organism.common.activities.noData' | translate }}
            </p>
        </ng-template>
    </div>


</mat-card>
