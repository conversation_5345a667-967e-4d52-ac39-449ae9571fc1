import {Component, Inject, OnInit, ViewChild} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialog, MatDialogRef} from '@angular/material/dialog';
import {FormBuilder, FormGroup, FormGroupDirective} from '@angular/forms';
import {AppFormFieldData} from '../../material/app-form-field/app-form-field.component';
import {ActivityService} from '../../api/services/activity.service';
import {Activity} from '../../api/models/activity';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../errors/errors.types';
import {TranslateService} from '@ngx-translate/core';
import {EntityClass} from '../../utils/enums/entity-class';
import {filter, finalize, switchMap} from 'rxjs/operators';
import {ActionConfirmationComponent} from '../../material/action-confirmation/action-confirmation.component';
import {CertificationPartner} from '../../api/models/certification-partner';
import {CertificationFolder} from '../../api/models/certification-folder';
import {RegistrationFolder} from '../../api/models/registration-folder';
import {Proposal} from '../../api/models/proposal';
import {RegistrationFolderService} from '../../api/services/registration-folder.service';
import {CertificationFolderService} from '../../api/services/certification-folder.service';
import {CertificationPartnerService} from '../../api/services/certification-partner.service';
import {ProposalService} from '../../api/services/proposal.service';

@Component({
    selector: 'app-activity-move',
    templateUrl: './activity-move-form.component.html',
    styleUrls: ['./activity-move-form.component.scss']
})
export class ActivityMoveFormComponent implements OnInit {
    loading = false;
    errorMessages: string[] = [];
    formGroup: FormGroup;
    appFormFieldsData: AppFormFieldData[];
    @ViewChild('form') form: FormGroupDirective;

    constructor(
        public dialogRef: MatDialogRef<ActivityMoveFormComponent>,
        private _activityService: ActivityService,
        private _translateService: TranslateService,
        private _formBuilder: FormBuilder,
        private _registrationFolderService: RegistrationFolderService,
        private _certificationFolderService: CertificationFolderService,
        private _certificationPartnerService: CertificationPartnerService,
        private _proposalService: ProposalService,
        private _dialog: MatDialog,
        @Inject(MAT_DIALOG_DATA) public dialogData: {
            activityToUpdate: Activity,
        },
    ) {
    }

    ngOnInit(): void {
        this.formGroup = this._formBuilder.group({
            activityForm: this._formBuilder.group({})
        });

        const appFormFieldsData: AppFormFieldData[] = [
            {
                controlName: 'entityClass',
                required: true,
                value: null,
                label: 'private.common.activities.move.entityClass.label',
                type: 'select',
                choices: [
                    {
                        key: this._translateService.instant('private.common.message-templates.entityClass.value.CertificationFolder'),
                        value: EntityClass.CERTIFICATION_FOLDER
                    },
                    {
                        key: this._translateService.instant('private.common.message-templates.entityClass.value.RegistrationFolder'),
                        value: EntityClass.REGISTRATION_FOLDER
                    },
                    {
                        key: this._translateService.instant('private.common.message-templates.entityClass.value.CertificationPartner'),
                        value: EntityClass.CERTIFICATIONS_PARTNER
                    },
                    {
                        key: this._translateService.instant('private.common.message-templates.entityClass.value.Proposal'),
                        value: EntityClass.PROPOSAL
                    }
                ],
                change: (controlName, newValue, formData) => {
                    const appFormFieldCertificationFolder = formData.find(field => field.controlName === 'certificationFolder');
                    const appFormFieldRegistrationFolder = formData.find(field => field.controlName === 'registrationFolder');
                    const appFormFieldCertificationPartner = formData.find(field => field.controlName === 'certificationPartner');
                    const appFormFieldProposal = formData.find(field => field.controlName === 'proposal');

                    appFormFieldCertificationFolder.required = newValue === EntityClass.CERTIFICATION_FOLDER;
                    appFormFieldRegistrationFolder.required = newValue === EntityClass.REGISTRATION_FOLDER;
                    appFormFieldCertificationPartner.required = newValue === EntityClass.CERTIFICATIONS_PARTNER;
                    appFormFieldProposal.required = newValue === EntityClass.PROPOSAL;

                    appFormFieldCertificationFolder.removed = newValue !== EntityClass.CERTIFICATION_FOLDER;
                    appFormFieldRegistrationFolder.removed = newValue !== EntityClass.REGISTRATION_FOLDER;
                    appFormFieldCertificationPartner.removed = newValue !== EntityClass.CERTIFICATIONS_PARTNER;
                    appFormFieldProposal.removed = newValue !== EntityClass.PROPOSAL;

                    if (newValue === EntityClass.CERTIFICATION_FOLDER) {
                        appFormFieldCertificationFolder.disabled = false;
                        appFormFieldCertificationFolder.placeholder = 'private.common.activities.move.entityId.placeholder.CertificationFolder';
                    }

                    return [appFormFieldCertificationFolder, appFormFieldRegistrationFolder, appFormFieldCertificationPartner, appFormFieldProposal];
                },
                colSpan: 12,
            },
            {
                controlName: 'certificationFolder',
                value: null,
                disabled: true,
                placeholder: 'private.common.activities.move.chooseType',
                label: 'private.common.activities.move.entityId.label',
                type: 'infiniteSearch',
                colSpan: 12,
                parameters: {limit: 30},
                searchNoEntriesFoundLabel: 'common.actions.search.noEntries',
                searchMethodPaginated: (params) => this._certificationFolderService.list({query: params.query}),
                searchResultFormatter: (certificationFolder: CertificationFolder) =>
                    certificationFolder.externalId + ' - ' + certificationFolder.attendee.firstName + ' ' + certificationFolder.attendee.lastName
            },
            {
                controlName: 'registrationFolder',
                value: null,
                removed: true,
                placeholder: 'private.common.activities.move.entityId.placeholder.RegistrationFolder',
                label: 'private.common.activities.move.entityId.label',
                type: 'infiniteSearch',
                colSpan: 12,
                parameters: {limit: 30},
                searchNoEntriesFoundLabel: 'common.actions.search.noEntries',
                searchMethodPaginated: (params) => this._registrationFolderService.list({query: params.query}),
                searchResultFormatter: (registrationFolder: RegistrationFolder) =>
                    registrationFolder.externalId + ' - ' + registrationFolder.attendee.firstName + ' ' + registrationFolder.attendee.lastName
            },
            {
                controlName: 'certificationPartner',
                value: null,
                removed: true,
                placeholder: 'private.common.activities.move.entityId.placeholder.CertificationPartner',
                label: 'private.common.activities.move.entityId.label',
                type: 'infiniteSearch',
                colSpan: 12,
                parameters: {limit: 30},
                searchNoEntriesFoundLabel: 'common.actions.search.noEntries',
                searchMethodPaginated: (params) => this._certificationPartnerService.listForActivity(params.query),
                searchResultFormatter: (certificationPartner: CertificationPartner) =>
                    certificationPartner._links.certification.externalId + ' - ' + certificationPartner._links.partner.name
            },
            {
                controlName: 'proposal',
                value: null,
                removed: true,
                placeholder: 'private.common.activities.move.entityId.placeholder.Proposal',
                label: 'private.common.activities.move.entityId.label',
                type: 'infiniteSearch',
                colSpan: 12,
                parameters: {limit: 30},
                searchNoEntriesFoundLabel: 'common.actions.search.noEntries',
                searchMethodPaginated: (params) => this._proposalService.list({
                    query: params.query,
                    isIndividual: true
                }),
                searchResultFormatter: (proposal: Proposal) => proposal.code +
                    (proposal.email && !proposal.firstName ? ' - ' + proposal.email :
                        proposal.firstName && proposal.lastName ? ' - ' + proposal.firstName + ' ' + proposal.lastName : '')
            }
        ];
        this.appFormFieldsData = appFormFieldsData.filter(value => value != null);
    }

    closeModal(): void {
        this.dialogRef.close();
    }

    move(): void {
        this.loading = true;
        this.errorMessages = [];
        const activityFormValue = this.formGroup.getRawValue().activityForm;
        const entityId = activityFormValue.entityClass === EntityClass.CERTIFICATION_FOLDER ? activityFormValue.certificationFolder.externalId :
            activityFormValue.entityClass === EntityClass.REGISTRATION_FOLDER ? activityFormValue.registrationFolder.externalId :
                activityFormValue.entityClass === EntityClass.CERTIFICATIONS_PARTNER ? activityFormValue.certificationPartner.id.toString() : activityFormValue.proposal.code;

        const dialogRef = this._dialog.open(ActionConfirmationComponent, {
            panelClass: 'full-page-scroll-40',
            height: 'auto',
            data: {
                messageKey: 'private.common.activities.move.confirmLabel',
                data: activityFormValue
            }
        });
        let confirm: boolean;
        dialogRef.componentInstance.actionValue$.pipe(
            filter((confirmation: boolean) => {
                confirm = confirmation;
                return confirmation;
            }),
            switchMap(() => this._activityService.move({
                entityClass: activityFormValue.entityClass,
                entityId: entityId
            }, this.dialogData.activityToUpdate.id)),
            finalize(() => {
                dialogRef.componentInstance.close();
                if (!confirm) {
                    this.dialogRef.close({data: null});
                }
            })
        ).subscribe({
            next: () => {
                this.loading = false;
                this.dialogRef.close({data: this.dialogData.activityToUpdate});
            },
            error: (httpErrorResponse: HttpErrorResponse) => {
                this.loading = false;
                this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
            }
        });
    }
}
