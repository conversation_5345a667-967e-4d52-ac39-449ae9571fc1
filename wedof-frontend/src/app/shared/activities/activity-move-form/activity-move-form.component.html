<app-dialog-layout
    [title]="'private.common.activities.move.title' | translate"
    [actions]="actions"
    (dialogClose)="closeModal()">
    <form #form="ngForm" [formGroup]="formGroup" class="flex flex-col">
        <treo-message class="flex-auto mb-4" appearance="outline" type="info">
            <p>{{ 'private.common.activities.move.treoMessage' | translate}}</p>
        </treo-message>
        <app-form-fields
            formGroupName="activityForm"
            class="grid grid-cols-6 gap-4"
            [appFormFieldsData]="appFormFieldsData"
            [formGroup]="formGroup">
        </app-form-fields>

        <div *ngIf="errorMessages?.length" class="flex items-center">
            <treo-message class="flex-auto" appearance="outline" [showIcon]="false" type="error">
                <ul>
                    <li *ngFor="let errorMessage of errorMessages">
                        {{ errorMessage }}
                    </li>
                </ul>
            </treo-message>
        </div>
        <ng-template #actions>
            <div class="flex flex-row py-2 my-2">
                <button type="submit"
                        color="primary"
                        mat-flat-button
                        (click)="move()"
                        title="move"
                        [disabled]="loading || !formGroup.dirty || formGroup.invalid">
                    <mat-progress-spinner class="mr-4" *ngIf="loading" [diameter]="24"
                                          mode="indeterminate">
                    </mat-progress-spinner>
                    <ng-container *ngIf="!loading">
                        {{ 'private.common.activities.move.submit' | translate }}
                    </ng-container>
                </button>
            </div>
        </ng-template>
    </form>
</app-dialog-layout>
