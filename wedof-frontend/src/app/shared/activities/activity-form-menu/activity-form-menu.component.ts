import {Component, OnInit, Input, Output, EventEmitter} from '@angular/core';
import {ActivityFormComponent} from '../activity-form/activity-form.component';
import {Activity} from '../../api/models/activity';
import {MatDialog} from '@angular/material/dialog';

@Component({
    selector: 'app-activity-form-menu',
    templateUrl: './activity-form-menu.component.html',
    styleUrls: ['./activity-form-menu.component.scss']
})
export class ActivityFormMenuComponent implements OnInit {
    @Input() entityId: string | number;
    @Input() entityClass: string;
    @Output() createdActivity: EventEmitter<Activity> = new EventEmitter<Activity>();

    constructor(private _dialog: MatDialog) {
    }

    ngOnInit(): void {

    }

    addActivity(task: boolean): void {
        const dialogRef = this._dialog.open(ActivityFormComponent, {
            panelClass: ['full-page-scroll-50'], data: {
                entityClass: this.entityClass, entityId: this.entityId, task: task
            }
        });
        dialogRef.afterClosed().subscribe(res => {
            if (res?.data) {
                this.createdActivity.emit(res.data);
            }
        });
    }
}
