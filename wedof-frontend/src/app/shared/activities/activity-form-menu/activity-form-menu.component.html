<button
    [matTooltip]="'private.training-organism.common.activities.form.title' | translate"
    [matMenuTriggerFor]="actionsMenu"
    (click)="$event.stopPropagation()"
    type="button" mat-icon-button>
    <mat-icon svgIcon="add_circle"></mat-icon>
</button>
<mat-menu #actionsMenu="matMenu">
    <ng-template matMenuContent>
        <button mat-menu-item (click)="addActivity(true)" >
            <span>{{'private.common.activities.create.task' | translate}}</span>
        </button>
        <button mat-menu-item (click)="addActivity(false)">
            <span>{{ 'private.common.activities.create.activity' | translate}}</span>
        </button>
    </ng-template>
</mat-menu>
