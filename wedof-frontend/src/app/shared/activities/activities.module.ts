import {CommonModule} from '@angular/common';
import {NgModule} from '@angular/core';
import {TranslateModule} from '@ngx-translate/core';
import {MaterialModule} from '../material/material.module';
import {MatCardModule} from '@angular/material/card';
import {ActivitiesCardComponent} from './activities-card/activities-card.component';
import {PipesModule} from '../pipes/pipes.module';
import {MatTooltipModule} from '@angular/material/tooltip';
import {ReactiveFormsModule} from '@angular/forms';
import {InfiniteScrollModule} from 'ngx-infinite-scroll';
import {ActivityFormComponent} from './activity-form/activity-form.component';
import {ActivityMenuComponent} from './activity-menu/activity-menu.component';
import {ActivityFormMenuComponent} from './activity-form-menu/activity-form-menu.component';
import {ActivityMoveFormComponent} from './activity-move-form/activity-move-form.component';

@NgModule({
    declarations: [
        ActivityMoveFormComponent,
        ActivitiesCardComponent,
        ActivityFormComponent,
        ActivityMenuComponent,
        ActivityFormMenuComponent
    ],
    imports: [
        CommonModule,
        InfiniteScrollModule,
        ReactiveFormsModule,
        TranslateModule,
        MaterialModule,
        MatCardModule,
        PipesModule,
        MatTooltipModule
    ],
    exports: [
        ActivitiesCardComponent,
        ActivityFormMenuComponent,
        ActivityMoveFormComponent
    ]
})
export class ActivitiesModule {
}
