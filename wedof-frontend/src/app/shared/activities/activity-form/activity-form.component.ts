import {Component, Inject, <PERSON><PERSON><PERSON>roy, OnInit, ViewChild} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material/dialog';
import {FormBuilder, FormGroup, FormGroupDirective, Validators} from '@angular/forms';
import {AppFormFieldData} from '../../material/app-form-field/app-form-field.component';
import {ActivityService} from '../../api/services/activity.service';
import {Activity, ActivityCreateBody, ActivityTypes} from '../../api/models/activity';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../errors/errors.types';
import {TranslateService} from '@ngx-translate/core';
import {User} from '../../api/models/user';
import {Select} from '@ngxs/store';
import {UserState} from '../../api/state/user.state';
import {combineLatest, Observable, Subject} from 'rxjs';
import {takeUntil} from 'rxjs/operators';
import QualiopiIndicators from '../../../../assets/qualiopi/indicateur.json';
import {OrganismState} from '../../api/state/organism.state';
import {Organism} from '../../api/models/organism';
import {UserService} from '../../api/services/user.service';
import {EntityClass} from '../../utils/enums/entity-class';
import {FormValidators} from '../../api/shared/form-validators';

@Component({
    selector: 'app-activity-create',
    templateUrl: './activity-form.component.html',
    styleUrls: ['./activity-form.component.scss']
})
export class ActivityFormComponent implements OnInit, OnDestroy {
    loading = false;
    errorMessages: string[] = [];
    formGroup: FormGroup;
    appFormFieldsData: AppFormFieldData[];
    newActivity: ActivityCreateBody;
    activity: Activity;
    qualiopiIndicators: typeof QualiopiIndicators;
    task = false;
    usersOrganism = [];
    user: User;
    ready = false;
    buttonSubmitText: string;

    @Select(UserState.user) user$: Observable<User>;
    @Select(OrganismState.organism) organism$: Observable<Organism>;

    @ViewChild('form') form: FormGroupDirective;

    private _unsubscribeAll = new Subject<void>();


    constructor(
        public dialogRef: MatDialogRef<ActivityFormComponent>,
        private _activityService: ActivityService,
        private _userService: UserService,
        private _translateService: TranslateService,
        private _formBuilder: FormBuilder,
        @Inject(MAT_DIALOG_DATA) public dialogData: {
            entityClass: string,
            entityId: number | string,
            task?: boolean,
            type?: string,
            title?: string,
            activityCreated?: Activity,
            userEmail: string,
            activityToUpdate?: Activity,
            eventEndTime?: Date,
            isDirty?: boolean,
        },
    ) {
        this.qualiopiIndicators = QualiopiIndicators;
        this.ready = false;
        this.task = this.dialogData.task;
    }

    initForm(user: User, usersOrganism: any[], activity?: Activity, isDirty: boolean = false): void {
        this.formGroup = this._formBuilder.group({
            activityForm: this._formBuilder.group({})
        });

        const appFormFieldsData: AppFormFieldData[] = [
            {
                controlName: 'title',
                required: true,
                value: activity?.title ?? this.dialogData.title ?? null,
                label: 'private.training-organism.common.activities.form.label.title',
                type: 'text',
                colSpan: this.task ? 3 : 2,
            },
            {
                controlName: 'dueDate',
                value: activity?.dueDate ? new Date(activity.dueDate) : null,
                label: 'private.training-organism.common.activities.form.label.dueDate',
                type: 'datetime-local',
                colSpan: 3,
                removable: true,
                removed: !this.task
            },
            {
                controlName: 'type',
                required: true,
                value: activity?.type ?? this.dialogData.type ?? null,
                label: 'private.training-organism.common.activities.form.label.type',
                type: 'select',
                colSpan: this.task ? 3 : 2,
                choices: [
                    {
                        key: this._translateService.instant('private.training-organism.common.activities.form.type.phone'),
                        value: ActivityTypes.PHONE
                    },
                    {
                        key: this._translateService.instant('private.training-organism.common.activities.form.type.email'),
                        value: ActivityTypes.EMAIL
                    },
                    {
                        key: this._translateService.instant('private.training-organism.common.activities.form.type.meeting'),
                        value: ActivityTypes.MEETING
                    },
                    {
                        key: this._translateService.instant('private.training-organism.common.activities.form.type.chat'),
                        value: ActivityTypes.CHAT
                    },
                    {
                        key: this._translateService.instant('private.training-organism.common.activities.form.type.sms'),
                        value: ActivityTypes.SMS
                    },
                    {
                        key: this._translateService.instant('private.training-organism.common.activities.form.type.training'),
                        value: ActivityTypes.TRAINING
                    },
                    {
                        key: this._translateService.instant('private.training-organism.common.activities.form.type.remark'),
                        value: ActivityTypes.REMARK
                    },
                    {
                        key: this._translateService.instant('private.training-organism.common.activities.form.type.file'),
                        value: ActivityTypes.FILE
                    },
                    {
                        key: this._translateService.instant('private.training-organism.common.activities.form.type.connectionLog'),
                        value: ActivityTypes.CONNECTION_LOG
                    }
                ]
            },
            {
                controlName: 'qualiopiIndicators',
                label: 'private.training-organism.common.activities.form.label.qualiopi',
                value: activity?.qualiopiIndicators ? activity?.qualiopiIndicators.slice() : null,
                placeholder: 'private.training-organism.common.activities.form.placeholder.qualiopi',
                type: 'select',
                removed: this.dialogData.entityClass === EntityClass.CERTIFICATIONS_PARTNER && this.dialogData.activityToUpdate?.entityClass === EntityClass.CERTIFICATIONS_PARTNER,
                removable: true,
                searchMultiple: true,
                listChoices: this.qualiopiIndicators,
                colSpan: this.task ? 3 : 2,
            },
            {
                controlName: 'description',
                value: activity?.description ?? null,
                label: 'private.training-organism.common.activities.form.label.description',
                type: 'textarea',
            },
            {
                controlName: 'user',
                required: true,
                value: activity?._links?.user?.email ?? user.email ?? null,
                label: 'private.training-organism.common.activities.form.label.user',
                type: 'select',
                colSpan: 2,
                choices: usersOrganism
            },
            {
                controlName: 'eventTime',
                max: this.task ? new Date().toISOString() : null,
                required: !this.task,
                removable: this.task,
                removed: this.task && !activity,
                value: activity?.eventTime ? activity.eventTime : this.task ? null : new Date().toISOString(),
                label: this.task ? 'private.training-organism.common.activities.form.label.startAt' : 'private.training-organism.common.activities.form.label.startTime',
                type: 'datetime-local',
                colSpan: 2,
                change: (controlName, newValue, formData) => {
                    const appFormFieldEventEndTime = formData.find(field => field.controlName === 'eventEndTime');
                    appFormFieldEventEndTime.min = newValue;
                    this.getButtonSubmitText();
                    return [];
                },
            },
            {
                controlName: 'eventEndTime',
                value: activity?.eventEndTime ? activity.eventEndTime : this.dialogData.eventEndTime ? this.dialogData.eventEndTime : null,
                label: this.task ? 'private.training-organism.common.activities.form.label.endedAt' : 'private.training-organism.common.activities.form.label.endTime',
                type: 'datetime-local',
                colSpan: 2,
                min: activity?.eventTime ?? new Date().toISOString(),
                validatorsMessages: {
                    min: 'common.errors.date',
                },
                removable: true,
                removed: this.task && !activity,
                change: (controlName, newValue, formData) => {
                    const appFormFieldEventEndTime = formData.find(field => field.controlName === 'eventEndTime');
                    appFormFieldEventEndTime.value = newValue;

                    this.getButtonSubmitText();
                    return [];
                },
            },
            {
                controlName: 'link',
                value: activity?.link ?? null,
                label: 'private.training-organism.common.activities.form.label.link',
                type: 'url',
                colSpan: 3,
                validators: [Validators.pattern(FormValidators.URL_PATTERN)],
                validatorsMessages: {
                    pattern: 'common.errors.url'
                }
            }
        ];
        this.appFormFieldsData = appFormFieldsData.filter(value => value != null);

        if (isDirty) {
            this.formGroup.markAsDirty();
        }
    }

    ngOnInit(): void {
        combineLatest(
            [
                this.user$,
                this.organism$
            ]
        ).pipe(takeUntil(this._unsubscribeAll)).subscribe(([user, organism]) => {
            this._userService.list({siret: organism.siret}).subscribe(users => {
                this.usersOrganism = users.payload.map(data => ({
                    value: data.email,
                    key: data.name
                }));
                this.user = user;
                if (this.dialogData?.activityToUpdate) {
                    this.task = !this.dialogData.activityToUpdate.done;
                    this.initForm(this.user, this.usersOrganism, this.dialogData.activityToUpdate, this.dialogData.isDirty);
                } else {
                    this.initForm(this.user, this.usersOrganism);
                }
                this.getButtonSubmitText();
                this.ready = true;
            });
        });
    }

    closeModal(): void {
        this.dialogRef.close();
    }

    create(withDate = true): void {
        this.loading = true;
        this.errorMessages = [];
        const activityFormValue = this.formGroup.getRawValue().activityForm;

        this._activityService.create({
            type: activityFormValue.type,
            eventTime: activityFormValue.eventTime ?? withDate ? new Date() : null,
            eventEndTime: activityFormValue.eventEndTime ?? this.task ? null : activityFormValue.eventTime,
            dueDate: activityFormValue.dueDate,
            origin: 'manual',
            link: activityFormValue.link,
            title: activityFormValue.title,
            description: activityFormValue.description,
            qualiopiIndicators: activityFormValue.qualiopiIndicators,
            userEmail: activityFormValue.user
        }, this.dialogData.entityClass, this.dialogData.entityId).subscribe({
            next: (activity) => {
                this.loading = false;
                this.dialogData.activityCreated = activity;
                this.dialogRef.close({data: this.dialogData.activityCreated});
            },
            error: (httpErrorResponse: HttpErrorResponse) => {
                this.loading = false;
                this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
            }
        });
    }

    update(): void {
        this.loading = true;
        this.errorMessages = [];
        const activityFormValue = this.formGroup.getRawValue().activityForm;
        this._activityService.update({
            type: activityFormValue.type,
            eventTime: activityFormValue.eventTime,
            eventEndTime: activityFormValue.eventEndTime,
            dueDate: activityFormValue.dueDate,
            link: activityFormValue.link,
            title: activityFormValue.title,
            description: activityFormValue.description,
            qualiopiIndicators: activityFormValue.qualiopiIndicators,
            userEmail: activityFormValue.user,
            done: activityFormValue.done
        }, this.dialogData.activityToUpdate.id).subscribe({
            next: (activityUpdated) => {
                this.loading = false;
                this.dialogRef.close({data: activityUpdated});
            },
            error: (httpErrorResponse: HttpErrorResponse) => {
                this.loading = false;
                this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
            }
        });
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    getTitle(): string {
        const isUpdate = this.dialogData?.activityToUpdate ? '.update' : '.create';
        const partsType = this.task ? '.task' : '.activity';
        return 'private.common.activities' + isUpdate + partsType;
    }

    getButtonSubmitText(): void {
        let text = '';
        const eventEndTime = this.dialogData?.isDirty;

        if (this.dialogData?.activityToUpdate) {
            if (this.task && eventEndTime) {
                text = 'private.training-organism.common.activities.form.taskDone';
            } else if (this.task) {
                text = 'private.common.activities.update.task';
            } else {
                text = 'private.common.activities.update.activity';
            }
        } else {
            if (this.task) {
                text = 'private.common.activities.create.task';
            } else {
                text = 'private.common.activities.create.activity';
            }
        }
        this.buttonSubmitText = text;
    }
}
