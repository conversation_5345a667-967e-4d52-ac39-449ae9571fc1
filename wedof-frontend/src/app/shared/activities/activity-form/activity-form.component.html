<app-dialog-layout
    *ngIf="ready"
    [title]="getTitle() | translate"
    [actions]="actions"
    (dialogClose)="closeModal()">

    <form #form="ngForm" [formGroup]="formGroup" class="flex flex-col">
        <app-form-fields formGroupName="activityForm"
                         class="grid grid-cols-6 gap-4"
                         [entity]="dialogData.activityToUpdate ? activity : newActivity"
                         [appFormFieldsData]="appFormFieldsData"
                         [formGroup]="formGroup"></app-form-fields>

        <div *ngIf="errorMessages?.length" class="flex items-center">
            <treo-message class="flex-auto" appearance="outline" [showIcon]="false" type="error">
                <ul>
                    <li *ngFor="let errorMessage of errorMessages">
                        {{ errorMessage }}
                    </li>
                </ul>
            </treo-message>
        </div>
        <ng-template #actions>
            <div class="flex flex-row py-2 my-2">
                <button type="submit"
                        color="primary"
                        mat-flat-button
                        [ngClass]="{'button-actions' : task && !dialogData.activityToUpdate}"
                        (click)="!dialogData.activityToUpdate ? create(false) : update()"
                        [disabled]="loading || !formGroup.dirty || formGroup.invalid">
                    <mat-progress-spinner class="mr-4" *ngIf="loading" [diameter]="24"
                                          mode="indeterminate"></mat-progress-spinner>

                    <ng-container *ngIf="!loading">
                        {{ buttonSubmitText | translate }}
                    </ng-container>
                </button>
                <ng-container *ngIf="!loading && task && !dialogData.activityToUpdate">
                    <button mat-flat-button
                            class="button-arrow button-arrow-primary"
                            color="primary"
                            (click)="$event.stopPropagation()"
                            [matMenuTriggerFor]="choiceCreateStart"
                            [disabled]="loading || !formGroup.dirty || formGroup.invalid"
                            title="create">
                        <mat-icon class="icon" svgIcon="arrow_drop_down"></mat-icon>
                    </button>
                    <mat-menu #choiceCreateStart="matMenu">
                        <ng-template matMenuContent>
                            <button mat-menu-item (click)="create()">
                                <mat-icon color="primary" svgIcon="play_arrow"></mat-icon>
                                <span>{{ 'private.common.activities.startTask' | translate }}</span>
                            </button>
                            <button mat-menu-item (click)="create(false)">
                                <mat-icon color="primary" svgIcon="edit"></mat-icon>
                                <span>{{ "private.common.activities.create.task" | translate }}</span>
                            </button>
                        </ng-template>
                    </mat-menu>
                </ng-container>
            </div>
        </ng-template>
    </form>
</app-dialog-layout>
<div *ngIf="!ready" class="flex justify-center">
    <mat-spinner role="progressbar" mode="indeterminate"
                 class="mat-spinner mat-progress-spinner mat-primary mat-progress-spinner-indeterminate-animation"
                 style="width: 100px; height: 100px;">
    </mat-spinner>
</div>
