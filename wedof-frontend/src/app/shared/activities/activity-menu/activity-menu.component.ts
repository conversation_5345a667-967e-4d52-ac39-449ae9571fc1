import {Component, EventEmitter, Input, Output} from '@angular/core';
import {Activity} from '../../api/models/activity';
import {MatDialog} from '@angular/material/dialog';
import {ActivityFormComponent} from '../activity-form/activity-form.component';
import {ActivityMoveFormComponent} from '../activity-move-form/activity-move-form.component';

@Component({
    selector: 'app-activity-menu',
    templateUrl: './activity-menu.component.html',
    styleUrls: ['./activity-menu.component.scss']
})
export class ActivityMenuComponent {

    @Input() activity: Activity;
    @Input() canBeModified: boolean;
    @Output() processedActivity: EventEmitter<Activity> = new EventEmitter<Activity>();
    @Output() deleteActivityTask: EventEmitter<Activity> = new EventEmitter<Activity>();
    @Output() moveActivity: EventEmitter<Activity> = new EventEmitter<Activity>();

    constructor(private _dialog: MatDialog) {
    }

    update(): void {
        this.processedActivity.emit(this.activity);
    }

    markAsDone(): void {
        const dialogRef = this._dialog.open(ActivityFormComponent, {
            panelClass: ['full-page-scroll-50'],
            data: {
                activityToUpdate: this.activity,
                eventEndTime: new Date(),
                isDirty: true,
            }
        });
        dialogRef.afterClosed().subscribe(res => {
            if (res?.data) {
                this.processedActivity.emit(res?.data);
            }
        });
    }

    move(): void {
        const dialogRef = this._dialog.open(ActivityMoveFormComponent, {
            panelClass: ['full-page-scroll-30'],
            data: {
                activityToUpdate: this.activity
            }
        });
        dialogRef.afterClosed().subscribe(res => {
            if (res?.data) {
                this.moveActivity.emit(this.activity);
            }
        });
    }

    delete(): void {
        this.deleteActivityTask.emit(this.activity);
    }
}
