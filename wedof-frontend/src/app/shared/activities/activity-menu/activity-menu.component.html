<button mat-icon-button
        (click)="$event.stopPropagation()"
        [matMenuTriggerFor]="actionsMenu"
        title="Actions">
    <mat-icon svgIcon="more_vert"></mat-icon>
</button>
<mat-menu #actionsMenu="matMenu">
    <ng-template matMenuContent>
        <button mat-menu-item (click)="update()" *ngIf="canBeModified">
            <mat-icon color="primary" svgIcon="edit"></mat-icon>
            <span>{{ 'common.actions.update' | translate }}</span>
        </button>
        <button mat-menu-item (click)="markAsDone()" *ngIf="!activity.eventEndTime && canBeModified">
            <mat-icon color="primary" svgIcon="check_circle_outline"></mat-icon>
            <span>{{ 'private.common.activities.markAsDone' | translate }}</span>
        </button>
        <button mat-menu-item (click)="move()">
            <mat-icon color="primary" svgIcon="arrow_forward"></mat-icon>
            <span>{{ 'private.common.activities.move.submit' | translate }}</span>
        </button>
        <button mat-menu-item (click)="delete()" *ngIf="canBeModified">
            <mat-icon color="warn" svgIcon="delete"></mat-icon>
            <span>{{ 'common.actions.delete' | translate }}</span>
        </button>
    </ng-template>
</mat-menu>
