<app-dialog-layout [title]="'private.layout.user.subscription.modal.title.certifier' | translate"
                   cancelText="{{'common.actions.close' | translate}}"
                   (dialogClose)="closeModal()">

    <treo-message *ngIf="action" type="info"
                  [showIcon]="false" appearance="outline">
        <p class="py-2">{{'private.layout.user.subscription.help.action' | translate }}</p>
    </treo-message>

    <div class="flex justify-between"
         *ngIf="subscription.certifierType === SubscriptionCertifierTypes.PARTNER">
        <strong> {{'private.layout.user.subscription.modal.subtitlePartner' | translate}} </strong>
    </div>
    <div class="flex flex-row mt-5">
        <div class="w-full">
            <div class="mb-5 flex flex-col items-center"
                 *ngIf="[SubscriptionCertifierTypes.NONE.toString(), SubscriptionCertifierTypes.PARTNER.toString()].includes(subscription.certifierType) && !subscription.certifierTrialEndDate">
                <p>{{'private.layout.user.subscription.switchTrialCertifier' | translate}}</p>
                <button
                    id="switch-trial-button"
                    [disabled]="isSwitching"
                    color="primary" mat-flat-button class="w-5/7 mr-auto ml-auto flex mb-4 mt-4"
                    (click)="switchToTrial()">
                    <span *ngIf="!isSwitching">{{'private.layout.user.subscription.try'| translate }}</span>
                    <mat-progress-spinner class="mr-4" *ngIf="isSwitching"
                                          [diameter]="24"
                                          [mode]="'indeterminate'"></mat-progress-spinner>
                </button>
                <div *ngIf="trialErrorMessages.length" class="flex items-center">
                    <treo-message class="flex-auto" appearance="outline" [showIcon]="false" type="error">
                        <ul>
                            <li *ngFor="let errorMessage of trialErrorMessages">
                                {{ errorMessage }}
                            </li>
                        </ul>
                    </treo-message>
                </div>
            </div>
            <table class="flex flex-col"
                   *ngIf="[SubscriptionCertifierTypes.USAGE.toString(), SubscriptionCertifierTypes.UNLIMITED.toString(), SubscriptionCertifierTypes.TRIAL.toString(), SubscriptionCertifierTypes.ACCESS].includes(subscription.certifierType)">
                <tr class="flex justify-between"
                    *ngIf="subscription.allowCertifiers && subscription.certifierType !== SubscriptionCertifierTypes.ACCESS">
                    <th class="flex flex-row items-center"><a (click)="this.closeModal()"
                                                              matTooltip="Voir les dossiers créés sur la période"
                                                              [routerLink]="'/certification/dossiers'"
                                                              [queryParams]="{filterOnStateDate: 'wedofInvoice', period: PeriodTypesForCertificationFolders.WEDOF_INVOICE}"
                    >{{'private.layout.user.subscription.certificationFoldersCount' | translate}}</a></th>
                    <td class="flex justify-center items-center">
                        <a class="no-underline flex flex-row items-center" (click)="this.closeModal()"
                           [routerLink]="['/aide/guides/certificateurs/facturation']"
                           [queryParams]="{filterOnStateDate: 'wedofInvoice', period: PeriodTypesForCertificationFolders.WEDOF_INVOICE}"
                        >
                            <mat-icon class="pr-1"
                                      matTooltip="En savoir plus sur la facturation des dossiers de certification"
                                      [svgIcon]="'help'"></mat-icon>
                            <p>{{certificationFoldersCounter}} {{(subscription.certifierType === SubscriptionCertifierTypes.TRIAL || subscription.certifierType === SubscriptionCertifierTypes.USAGE) ? ' / ' + subscription.certificationFoldersNumberCap : ''}} </p>
                        </a>
                    </td>
                </tr>
                <tr class="flex justify-between"
                    *ngIf="subscription.certifierType === SubscriptionCertifierTypes.ACCESS">
                    <th class="flex flex-row items-center"><a (click)="this.closeModal()"
                                                              matTooltip="Voir les dossiers créés sur la période"
                                                              [routerLink]="'/certification/dossiers'"
                                                              [queryParams]="{filterOnStateDate: 'wedofInvoice', period: PeriodTypesForCertificationFolders.WEDOF_INVOICE}"
                    >{{'private.layout.user.subscription.certificationFoldersCountAccessMonthly' | translate}}</a></th>
                    <td class="flex justify-center items-center">
                        <a class="no-underline flex flex-row items-center" (click)="this.closeModal()"
                           [routerLink]="['/aide/guides/certificateurs/facturation']"
                           [queryParams]="{filterOnStateDate: 'wedofInvoice', period: PeriodTypesForCertificationFolders.WEDOF_INVOICE}"
                        >
                            <mat-icon class="pr-1"
                                      matTooltip="En savoir plus sur la facturation des dossiers de certification"
                                      [svgIcon]="'help'"></mat-icon>
                            <p>{{certificationFoldersCounter}}</p>
                        </a>
                    </td>
                </tr>
                <tr class="flex justify-between"
                    *ngIf="subscription.certifierType !== SubscriptionCertifierTypes.PARTNER">
                    <th class="flex flex-row items-center">{{'private.layout.user.subscription.activeApps' | translate}}</th>
                    <td class="flex flex-row items-center"> {{counterApplications}}</td>
                </tr>
                <tr class="flex justify-between"
                    *ngIf="subscription.certifierType !== SubscriptionCertifierTypes.PARTNER && subscription.smsSentNumberPeriodStartDate">
                    <th class="flex flex-row items-center">{{'private.layout.user.subscription.smsSent.title' | translate}}</th>
                    <td class="flex flex-row items-center">
                        <mat-icon class="pr-1" matTooltip="{{'private.layout.user.subscription.smsSent.tooltip' | translate: {
                                    startDate: subscription.smsSentNumberPeriodStartDate | date:'mediumDate',
                                    endDate: subscription.smsSentNumberPeriodEndDate | date:'mediumDate'
                            }
                            }}" [svgIcon]="'help'"></mat-icon>
                        {{subscription.smsSentNumberCount}}
                    </td>
                </tr>
                <tr class="flex justify-between">
                    <th class="flex flex-row items-center">{{'common.token.requestCountTitle' | translate}}</th>
                    <td class="flex flex-row items-center">{{subscription.requestCount}}</td>
                </tr>
                <ng-container *ngFor="let organismApplication of organismApplicationsOptions">
                    <tr class="flex justify-between">
                        <th class="flex flex-row items-center">{{ getApplicationName(organismApplication.appId) }}</th>
                        <td class="flex flex-row items-center">
                            <a (click)="closeModal()"
                               [routerLink]="[organismApplication.state != OrganismApplicationStates.DISABLED ? '/mes-applications/'+organismApplication.appId+'/reglages' : '/mes-applications']"
                               [state]="{'enableAppId': (organismApplication.state == OrganismApplicationStates.DISABLED ? organismApplication.appId : null)}">
                                {{'private.layout.user.subscription.subscriptionOptionStates.' + organismApplication.state | translate}}
                                <ng-container
                                    *ngIf="organismApplication.endDate">
                                    ({{organismApplication.endDate | date:'mediumDate'}})
                                </ng-container>
                            </a>
                        </td>
                    </tr>
                </ng-container>
                <tr class="flex justify-between">
                    <th class="flex flex-row items-center">{{'private.layout.user.subscription.allowAudits' | translate}}</th>
                    <td class="flex flex-row items-center">{{certificationAllowAudits}}</td>
                </tr>
                <tr class="flex justify-between">
                    <th class="flex flex-row items-center"
                        *ngIf="subscription.certifierType !== SubscriptionCertifierTypes.TRIAL">{{'private.layout.user.subscription.startDate' | translate}}</th>
                    <th class="flex flex-row items-center"
                        *ngIf="subscription.certifierType === SubscriptionCertifierTypes.TRIAL">{{'private.layout.user.subscription.startTrial' | translate}}</th>
                    <td class="flex flex-row items-center">{{subscription.certifierStartDate |  date:'mediumDate'}}</td>
                </tr>
                <ng-container *ngIf="subscription.certifierPendingCancellation; else noCancellation">
                    <table>
                        <tr class="flex justify-between"
                            *ngIf="subscription.certifierType !== SubscriptionCertifierTypes.PARTNER">
                            <th class="flex flex-row items-center">{{'private.layout.user.subscription.endDate' | translate}}</th>
                            <td class="flex flex-row items-center">{{subscription.certifierPeriodEndDate |  date:'mediumDate'}}</td>
                        </tr>
                    </table>
                </ng-container>
                <ng-template #noCancellation>
                    <table>
                        <tr class="flex justify-between"
                            *ngIf="!subscription.trainingPeriodEndDate.includes('2199-12-31') && subscription.certifierType !== SubscriptionCertifierTypes.TRIAL && subscription.certifierType !== SubscriptionCertifierTypes.PARTNER">
                            <th class="flex flex-row items-center">{{'private.layout.user.subscription.period' | translate}}</th>
                            <td class="flex flex-row items-center">
                                {{'private.layout.user.subscription.fromTo' | translate : {
                                from: subscription.certifierPeriodStartDate |  date:'mediumDate',
                                to: subscription.certifierPeriodEndDate |  date:'mediumDate'
                            } }}
                            </td>
                        </tr>
                    </table>
                </ng-template>
                <tr class="flex justify-between"
                    *ngIf="subscription.certifierType === SubscriptionCertifierTypes.ACCESS">
                    <th class="flex flex-row items-center">{{'private.layout.user.subscription.certificationFoldersCountAccessAnnual' | translate}}</th>
                    <td class="flex justify-center items-center">
                        <a (click)="this.closeModal()"
                           matTooltip="Voir les dossiers créés sur la période annuelle"
                           [routerLink]="'/certification/dossiers'"
                           [queryParams]="{filterOnStateDate: 'wedofInvoice', period: PeriodTypesForCertificationFolders.WEDOF_QUOTA}"
                        >{{subscription.certificationFoldersNumberAnnualCount}}
                            / {{subscription.certificationFoldersNumberAnnualLimit}}
                        </a>
                    </td>
                </tr>
            </table>
            <div class="flex justify-between mt-3"
                 *ngIf="![SubscriptionCertifierTypes.NONE.toString(), SubscriptionCertifierTypes.PARTNER.toString()].includes(subscription.certifierType) || organism.isReseller">
                <div>
                    <button
                        color="primary" mat-flat-button
                        (click)="subscriptionLink()"
                        *ngIf="organism.isReseller || (subscription.isStripeCustomer && user.isOwner && subscription.certifierType !== SubscriptionCertifierTypes.PARTNER)">{{ 'private.layout.user.subscription.invoices.downloadInvoice' | translate }}
                    </button>
                    <button *ngIf="organism.isTrainingOrganism && subscription.trainingType"
                            mat-flat-button
                            (click)="switchToTrainingAction($event)">{{ 'private.layout.user.subscription.modal.access.training' | translate }}</button>
                </div>
                <button
                    color="primary" mat-flat-button
                    (click)="subscriptionLink()"
                    *ngIf="subscription.certifierType === SubscriptionCertifierTypes.USAGE || subscription.certifierType === SubscriptionCertifierTypes.UNLIMITED">{{ 'private.layout.user.subscription.manageMySubscription' | translate }}
                </button>

            </div>
        </div>
    </div>
    <div class="flex flex-row items-center mt-4 mb-4"
         *ngIf="fromPage && subscription.certifierType !== SubscriptionCertifierTypes.UNLIMITED">
        <mat-icon color="primary">lightbulb</mat-icon>
        <p [innerHTML]=" 'private.layout.user.subscription.help.' + fromPage | translate "></p>
    </div>

    <table class="w-full mt-2 mb-10 table offers">
        <colgroup>
            <col>
            <col
                [class]="subscription.certifierType === SubscriptionCertifierTypes.USAGE && !subscription.allowCertifierPlus ? 'highlightCard' : '' ">
            <col
                [class]="subscription.certifierType === SubscriptionCertifierTypes.ACCESS ? 'highlightCard' : '' ">
            <col
                [class]="(subscription.certifierType === SubscriptionCertifierTypes.USAGE && subscription.allowCertifierPlus) ||
                subscription.certifierType === SubscriptionCertifierTypes.UNLIMITED  || subscription.certifierType === SubscriptionCertifierTypes.TRIAL   ? 'highlightCard' : '' ">
        </colgroup>
        <thead
            [class]="subscription.certifierType === SubscriptionCertifierTypes.PARTNER ? 'w-1/6 mr-3' : 'w-1/5 mr-3'">
        <tr class="h-5" *ngIf="subscription.certifierType === SubscriptionCertifierTypes.TRIAL">
            <th class="w-1/6 mr-3"></th>
            <th class="w-1/6 mr-3"></th>
            <th class="w-1/6 mr-3"></th>
            <th class="rounded-t w-1/5 mr-3 pt-2 pb-2 text-white bg-trial-purple">
                {{'private.layout.user.subscription.certifier.trial.offer' | translate }}<br>
                {{ remainingDays }} {{ 'private.layout.user.subscription.certifier.trial.remaining-days' | translate }}
            </th>
        </tr>
        <tr>
            <th class="w-1/6 mr-3"></th>
            <!-- todo partner 2 fois ? -->
            <th [class]="(subscription.certifierType === SubscriptionCertifierTypes.USAGE && !subscription.allowCertifierPlus) || subscription.certifierType === SubscriptionCertifierTypes.PARTNER ? 'rounded-t w-1/5 mr-3 pt-4 pb-2' : 'w-1/6 mr-3 pt-4 pb-2'">
                {{'private.layout.user.subscription.certifier.usage.offer' | translate}} {{'private.layout.user.subscription.certifier.usage.subtype.standard' | translate}}
            </th>
            <th [class]="(subscription.certifierType === SubscriptionCertifierTypes.ACCESS) ? 'rounded-t w-1/5 pt-4 pb-2' : 'w-1/6 pt-4 pb-2'">
                {{'private.layout.user.subscription.certifier.access.offer' | translate}}
            </th>
            <th [class]="(subscription.certifierType === SubscriptionCertifierTypes.USAGE && subscription.allowCertifierPlus) || subscription.certifierType === SubscriptionCertifierTypes.UNLIMITED ||
            subscription.certifierType === SubscriptionCertifierTypes.PARTNER || subscription.certifierType === SubscriptionCertifierTypes.TRIAL ? 'rounded-t w-1/5 pt-4 pb-2' : 'w-1/6 pt-4 pb-2'">
                <ng-container
                    *ngIf="subscription.certifierType === SubscriptionCertifierTypes.UNLIMITED else usageOffer">
                    {{'private.layout.user.subscription.certifier.unlimited.offer' | translate}}
                </ng-container>
                <ng-template #usageOffer>
                    {{'private.layout.user.subscription.certifier.usage.offer' | translate}} {{'private.layout.user.subscription.certifier.usage.subtype.premium' | translate}}
                </ng-template>
            </th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td style="padding-left: 8px;"></td>
            <td>
                <strong>{{ getCertificationFolderPrice(false).toFixed(2) }}€ HT / dossier</strong>
                <br> <span
                class="text-sm">{{'private.layout.user.subscription.certifier.foldersCountExplanation' | translate : {
                count: 500
            }    }}</span>
            </td>
            <td>
                <strong>49€ HT / mois
                    + {{ getCertificationFolderPrice(true).toFixed(2) }}€ HT / dossier</strong>
                <br> <span
                class="text-sm">{{'private.layout.user.subscription.certifier.foldersCountExplanationAccess' | translate : {
                count: 100
            }    }}</span>
            </td>
            <td>
                <strong>
                    <ng-container
                        *ngIf="subscription.certifierType === SubscriptionCertifierTypes.UNLIMITED else mensualUsage">
                        {{'private.layout.user.subscription.annual' | translate}}
                    </ng-container>
                    <ng-template #mensualUsage>
                        199€ HT / mois
                        + {{ getCertificationFolderPrice(true).toFixed(2) }}€ HT / dossier
                        <br> <span
                        class="text-sm">{{'private.layout.user.subscription.certifier.foldersCountExplanation' | translate : {
                        count: 500
                    }    }}</span>
                    </ng-template>
                </strong>
            </td>
        </tr>
        <tr class="h-8 block-standard-content first-row">
            <td class="font-bold no-text-center">{{'private.layout.user.subscription.table.header.userLimit' | translate}}
            </td>
            <td>1</td>
            <td>
                <mat-icon color="primary" class="opacity-54">all_inclusive</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="opacity-54">all_inclusive</mat-icon>
            </td>
        </tr>
        <tr class="h-8 block-standard-content">
            <td class="font-bold flex items-center no-text-center">{{'private.layout.user.subscription.certifier.folders.header' | translate}}
                <mat-icon class="pl-1"
                          [matTooltip]="'private.layout.user.subscription.certifier.folders.explanation' | translate"
                          [matTooltipPosition]="'above'"
                          [matTooltipShowDelay]="500"
                          [svgIcon]="'help'"></mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
            </td>
        </tr>
        <tr class="h-8 block-standard-content">
            <td class="font-bold flex items-center no-text-center">{{'private.layout.user.subscription.certifier.api.header' | translate}}
                <mat-icon class="pl-1"
                          [matTooltip]="'private.layout.user.subscription.certifier.api.explanation' | translate"
                          [matTooltipPosition]="'above'"
                          [matTooltipShowDelay]="500"
                          [svgIcon]="'help'"></mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
            </td>
        </tr>
        <tr class="h-8 block-standard-content">
            <td class="font-bold flex items-center no-text-center">{{'private.layout.user.subscription.certifier.webhooks.header' | translate}}
                <mat-icon class="pl-1"
                          [matTooltip]="'private.layout.user.subscription.certifier.webhooks.explanation' | translate"
                          [matTooltipPosition]="'above'"
                          [matTooltipShowDelay]="500"
                          [svgIcon]="'help'"></mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
            </td>
        </tr>
        <tr class="h-8 block-standard-content">
            <td class="font-bold flex items-center">{{'private.layout.user.subscription.certifier.synchronization.header' | translate}}
                <mat-icon class="pl-1"
                          [matTooltip]="'private.layout.user.subscription.certifier.synchronization.explanation' | translate"
                          [matTooltipPosition]="'above'"
                          [matTooltipShowDelay]="500"
                          [svgIcon]="'help'"></mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
            </td>
        </tr>
        <tr class="h-8 block-standard-content">
            <td class="font-bold flex items-center">{{'private.layout.user.subscription.certifier.apps.header' | translate}}
                <mat-icon class="pl-1"
                          [matTooltip]="'private.layout.user.subscription.certifier.apps.explanation' | translate"
                          [matTooltipPosition]="'above'"
                          [matTooltipShowDelay]="500"
                          [svgIcon]="'help'"></mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
            </td>
        </tr>
        <tr class="h-8 block-standard-content">
            <td class="font-bold">{{'private.layout.user.subscription.certifier.additionalCost.header' | translate}}*
            </td>
            <td>{{'private.layout.user.subscription.certifier.additionalCost.price' | translate}}</td>
            <td>{{'private.layout.user.subscription.certifier.additionalCost.priceAccess' | translate}}</td>
            <td>{{'private.layout.user.subscription.certifier.additionalCost.price' | translate}}</td>
        </tr>
        <tr class="h-8 block-standard-content">
            <td class="font-bold">{{'private.layout.user.subscription.signature' | translate}} </td>
            <td>
                49€ HT / mois
            </td>
            <td>
                49€ HT / mois
            </td>
            <td>
                49€ HT / mois
            </td>
        </tr>
        <tr class="h-8 block-standard-content last-row">
            <td class="font-bold">{{'private.layout.user.subscription.workflow' | translate}} </td>
            <td>
                49€ HT / mois
            </td>
            <td>
                49€ HT / mois
            </td>
            <td>
                49€ HT / mois
            </td>
        </tr>
        <tr class="h-8 block-premium-content first-row">
            <td class="font-bold flex items-center">{{'private.layout.user.subscription.certifier.autoDeposit.header' | translate}}
                <mat-icon class="pl-1"
                          [matTooltip]="'private.layout.user.subscription.certifier.autoDeposit.explanation' | translate"
                          [matTooltipPosition]="'above'"
                          [matTooltipShowDelay]="500"
                          [svgIcon]="'help'"></mat-icon>
            </td>
            <td>
                <mat-icon color="warn" class="align-middle opacity-54">cancel</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
            </td>
        </tr>
        <tr class="h-8 block-premium-content">
            <td class="font-bold">{{'private.layout.user.subscription.certifier.managePartnership' | translate}}</td>
            <td>
                <mat-icon color="warn" class="align-middle opacity-54">cancel</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
            </td>
        </tr>
        <tr class="h-8 block-premium-content">
            <td class="font-bold flex items-center">{{'private.layout.user.subscription.certifier.nocode.header' | translate}}
                <mat-icon class="pl-1"
                          [matTooltip]="'private.layout.user.subscription.certifier.nocode.explanation' | translate"
                          [matTooltipPosition]="'above'"
                          [matTooltipShowDelay]="500"
                          [svgIcon]="'help'"></mat-icon>
            </td>
            <td>
                <mat-icon color="warn" class="align-middle opacity-54">cancel</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
            </td>
        </tr>
        <tr class="h-8 block-premium-content">
            <td class="font-bold">{{'private.layout.user.subscription.certifier.surveys' | translate}}</td>
            <td>
                <mat-icon color="warn" class="align-middle opacity-54">cancel</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
            </td>
        </tr>
        <tr class="h-8 block-premium-content">
            <td class="font-bold">{{'private.layout.user.subscription.certifier.messageTemplates' | translate}}</td>
            <td>
                <mat-icon color="warn" class="align-middle opacity-54">cancel</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
            </td>
        </tr>
        <tr class="h-8 block-premium-content">
            <td class="font-bold">{{'private.layout.user.subscription.certifier.documents' | translate}}</td>
            <td>
                <mat-icon color="warn" class="align-middle opacity-54">cancel</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
            </td>
        </tr>
        <tr class="h-8 block-premium-content">
            <td class="font-bold">{{'private.layout.user.subscription.certifier.promoteCertification' | translate}}</td>
            <td>
                <mat-icon color="warn" class="align-middle opacity-54">cancel</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
            </td>
        </tr>
        <tr class="h-8 block-premium-content">
            <td class="font-bold">{{'private.layout.user.subscription.certifier.attendee' | translate}}</td>
            <td>
                <mat-icon color="warn" class="align-middle opacity-54">cancel</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
            </td>
        </tr>
        <tr class="h-8 block-premium-content">
            <td class="font-bold">{{'private.layout.user.subscription.certifier.generateCertificate' | translate}}</td>
            <td>
                <mat-icon color="warn" class="align-middle opacity-54">cancel</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
            </td>
        </tr>
        <tr class="h-8 block-premium-content">
            <td class="font-bold">{{'private.layout.user.subscription.certifier.partnerStatistics' | translate}}</td>
            <td>
                <mat-icon color="warn" class="align-middle opacity-54">cancel</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
            </td>
        </tr>
        <tr class="h-8 block-premium-content">
            <td class="font-bold">{{'private.layout.user.subscription.certifier.importExport' | translate}}</td>
            <td>
                <mat-icon color="warn" class="align-middle opacity-54">cancel</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
            </td>
        </tr>
        <tr class="h-8 block-premium-content">
            <td class="font-bold">{{'private.layout.user.subscription.certifier.openData' | translate}}</td>
            <td>
                <mat-icon color="warn" class="align-middle opacity-54">cancel</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
            </td>
        </tr>
        <tr class="h-8 block-premium-content">
            <td class="font-bold">{{'private.layout.user.subscription.certifier.adequation' | translate}}</td>
            <td>
                <mat-icon color="accent" class="align-middle opacity-54">check_circle</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
            </td>
        </tr>
        <tr class="h-8 block-premium-content last-row">
            <td class="font-bold">{{'private.layout.user.subscription.certifier.support' | translate}}</td>
            <td>{{'private.layout.user.subscription.certifier.supportStandard' | translate}}</td>
            <td>{{'private.layout.user.subscription.certifier.supportPremium' | translate}}</td>
            <td>{{'private.layout.user.subscription.certifier.supportPremium' | translate}}</td>
        </tr>
        <tr *ngIf="!subscription.certifierPartnership">
            <td [class]="subscription.certifierType === SubscriptionCertifierTypes.FREE ? 'rounded-b' : ''">
            </td>
            <td [class]="subscription.certifierType === SubscriptionCertifierTypes.USAGE && !subscription.allowCertifierPlus ? 'rounded-b' : ''">
                <ng-template [ngTemplateOutlet]="genericButton"
                             [ngTemplateOutletContext]="{isMySubscription: subscription.certifierType == SubscriptionCertifierTypes.USAGE && !subscription.allowCertifierPlus, type:SubscriptionCertifierTypes.USAGE, certifierPlus : false}">
                </ng-template>
            </td>
            <td [class]="(subscription.certifierType === SubscriptionCertifierTypes.ACCESS) ? 'rounded-b' : ''">
                <ng-template [ngTemplateOutlet]="genericButton"
                             [ngTemplateOutletContext]="{isMySubscription: subscription.certifierType == SubscriptionCertifierTypes.ACCESS, type: SubscriptionCertifierTypes.ACCESS, certifierPlus : true}">
                </ng-template>
            </td>
            <td [class]="(subscription.certifierType === SubscriptionCertifierTypes.USAGE && subscription.allowCertifierPlus) || subscription.certifierType === SubscriptionCertifierTypes.UNLIMITED  ? 'rounded-b' : ''">
                <ng-template [ngTemplateOutlet]="genericButton"
                             [ngTemplateOutletContext]="{isMySubscription: ((subscription.certifierType == SubscriptionCertifierTypes.USAGE) && subscription.allowCertifierPlus) || subscription.certifierType === SubscriptionCertifierTypes.UNLIMITED, type: SubscriptionCertifierTypes.USAGE, certifierPlus : true}">
                </ng-template>
            </td>
            <ng-template #genericButton let-isMySubscription="isMySubscription" let-type="type"
                         let-certifierPlus="certifierPlus">
                <button *ngIf="isMySubscription; else notMineSubscription"
                        color="primary" mat-flat-button class="w-5/6 mr-auto ml-auto flex mb-2 mt-4"
                        [disabled]="isMySubscription">
                    {{ 'private.layout.user.subscription.mySubscription' | translate }}
                </button>
                <ng-template #notMineSubscription>
                    <button *ngIf="isCustomer(); else notCustomer"
                            color="primary" mat-flat-button class="w-5/6 mr-auto ml-auto flex mb-2 mt-4">
                        <a class="no-underline"
                           [href]="getMail(type, certifierPlus)"> {{ 'private.layout.user.subscription.select' | translate }}</a>
                    </button>
                    <ng-template #notCustomer>
                        <button
                            color="primary" mat-flat-button class="w-5/6 mr-auto ml-auto flex mb-2 mt-4"
                            (click)="subscriptionLink(type, {certifierPlus: certifierPlus, quantity: type == SubscriptionCertifierTypes.ACCESS ? this.defaultCertificationFoldersNumberCapAccess : this.defaultCertificationFoldersNumberCap, price: getCertificationFolderPrice(certifierPlus)})">
                            {{ 'private.layout.user.subscription.select' | translate }}
                        </button>
                    </ng-template>
                </ng-template>
            </ng-template>
        </tr>
        <tr>
            <td></td>
            <td></td>
            <td></td>
            <td>
                <a class="no-underline" *ngIf="subscription.certifierType !== SubscriptionCertifierTypes.UNLIMITED"
                   [href]="getMail(SubscriptionCertifierTypes.UNLIMITED, true)">{{'private.layout.user.subscription.certifier.goAnnual' | translate}}
                </a>
            </td>
        </tr>
        </tbody>
    </table>

    <mat-card class="text-center m-auto p-5 shadow-none mb-4">
        <div class="mb-2"><strong>{{'private.layout.user.subscription.audit.title' | translate}}</strong>
        </div>
        <p><strong
            class="text-xl">99€</strong> HT / mois / certification</p>
        <div class="flex items-center text-center">
            <mat-icon color="primary" class="opacity-54">check_circle</mat-icon>
            {{'private.layout.user.subscription.audit.description' | translate}}
            <mat-icon color="primary" class="opacity-54 ml-3">check_circle</mat-icon>
            {{'private.layout.user.subscription.audit.description2' | translate}}
        </div>
        <ng-container *ngIf="isAnnualSubscription; else showText">
            <a class="cursor-pointer" (click)="sendMail()">
                {{'private.common.certification.audit.activateContact' | translate}}
            </a>
        </ng-container>
        <ng-template #showText>
            {{'private.layout.user.subscription.audit.activate' | translate}}
        </ng-template>
    </mat-card>

    <p class="mb-2 mat-caption">* {{'private.layout.user.subscription.certifier.additionalCost.title' | translate}}</p>
    <p class="mb-2 mat-caption">** {{'private.layout.user.subscription.certifier.additionalCostBeta' | translate}}</p>
    <!-- frais de mise en service payant -->
    <!--    <p *ngIf="subscription.certifierType === SubscriptionCertifierTypes.FREE || subscription.certifierType === SubscriptionCertifierTypes.TRIAL" class="mb-2 text-red">* {{'private.layout.user.subscription.certifier.additionalCostFree' | translate}}</p> &lt;!&ndash; frais de mise en service offerts &ndash;&gt;-->
    <div *ngIf="subscription.certifierType !== SubscriptionCertifierTypes.PARTNER">
        <div class="mb-5 flex flex-col items-center"
             *ngIf=" !subscription.certifierPartnership; else contactPartnership">
            <p>{{'private.layout.user.subscription.table.contact.question' | translate}}</p>
            <p>{{'private.layout.user.subscription.table.contact.contact' | translate}} : <a class="link font-semibold"
                                                                                             href="mailto:{{'private.support.mail' | translate}}">{{'private.support.mail' | translate}}</a>
                ou au <a
                    href="tel:{{'private.layout.user.subscription.table.contact.phoneNumber' | translate}}">{{'private.layout.user.subscription.table.contact.phoneNumber' | translate}}</a>
            </p>
        </div>
        <ng-template #contactPartnership>
            <div class="mb-5 flex flex-col items-center">
                <p>{{'private.layout.user.subscription.table.contact.questionPartnership' | translate}}</p>
                <p>{{'private.layout.user.subscription.table.contact.contactPartnership' | translate : {partnership: subscription.certifierPartnership} }}</p>
            </div>
        </ng-template>
    </div>
</app-dialog-layout>
