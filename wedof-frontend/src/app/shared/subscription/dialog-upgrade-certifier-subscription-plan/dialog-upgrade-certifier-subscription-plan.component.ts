import {Component, EventEmitter, Input, OnDestroy, OnInit, Output} from '@angular/core';
import {MatDialogRef} from '@angular/material/dialog';
import {OrganismApplicationService} from '../../api/services/organism-application.service';
import {SubscriptionService} from '../../api/services/subscription.service';
import {
    Subscription,
    SubscriptionCertifierTypes,
    SubscriptionOptions,
    SubscriptionTypes
} from 'app/shared/api/models/subscription';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../errors/errors.types';
import '../../utils/date-extension-utils';
import {Organism} from '../../api/models/organism';
import {SubscriptionState, UpdateSubscription} from '../../api/state/subscription.state';
import {mergeMap, takeUntil} from 'rxjs/operators';
import {Select, Store} from '@ngxs/store';
import {UserState} from '../../api/state/user.state';
import {Observable, Subject} from 'rxjs';
import {User} from '../../api/models/user';
import {PeriodTypesForCertificationFolders} from '../../api/models/period-types';
import {CertificationService} from '../../api/services/certification.service';
import moment from 'moment';
import {ApplicationsService} from '../../../applications/shared/applications.service';
import {OrganismApplication, OrganismApplicationStates} from '../../api/models/organism-application';
import {Application} from '../../../applications/shared/application.interface';

@Component({
    selector: 'app-dialog-upgrade-certifier-subscription-plan',
    templateUrl: './dialog-upgrade-certifier-subscription-plan.component.html',
    styleUrls: ['./dialog-upgrade-certifier-subscription-plan.component.scss']
})
export class DialogUpgradeCertifierSubscriptionPlanComponent implements OnInit, OnDestroy {
    readonly defaultCertificationFoldersNumberCap = 500;
    readonly defaultCertificationFoldersNumberCapAccess = 100;

    SubscriptionCertifierTypes = SubscriptionCertifierTypes;
    PeriodTypesForCertificationFolders = PeriodTypesForCertificationFolders;
    OrganismApplicationStates = OrganismApplicationStates;

    user: User;
    counterApplications = 0;
    certificationFoldersCounter = 0;
    certificationFoldersNumberCap: number;
    isSwitching = false;
    trialErrorMessages: string[] = [];
    remainingDays = '0';
    certificationAllowAudits = 0;
    isAnnualSubscription: boolean;
    organismApplicationsOptions: OrganismApplication[] = [];
    applicationsOptionsAvailable: Application[] = [];

    @Input() organism: Organism;
    @Input() subscription: Subscription;
    @Input() fromPage?: string;
    @Input() action?: boolean;
    @Output() shownSubscriptionType: EventEmitter<SubscriptionTypes> = new EventEmitter<SubscriptionTypes>();

    @Select(UserState.user) user$: Observable<User>;

    private _unsubscribeAll = new Subject<void>();

    constructor(
        private _store: Store,
        private _subscriptionService: SubscriptionService,
        private _applicationsService: ApplicationsService,
        private _organismApplicationService: OrganismApplicationService,
        private _certificationService: CertificationService,
        public dialogRef: MatDialogRef<DialogUpgradeCertifierSubscriptionPlanComponent>
    ) {
    }

    ngOnInit(): void {
        this.applicationsOptionsAvailable = this._applicationsService.findAll().filter((application) => application.isSubscriptionOption());
        this.user$.pipe(takeUntil(this._unsubscribeAll)).subscribe((user: User) => {
            this.user = user;
        });
        if (this.subscription.certifierType === SubscriptionCertifierTypes.TRIAL) {
            this.remainingDays = new Date(this.subscription.certifierPeriodEndDate).remainingDays();
        }
        this._organismApplicationService.findAll().subscribe((organismApplications) => {
            organismApplications.forEach((application) => {
                if (application.enabled === true) {
                    this.counterApplications += 1;
                }
            });
            this.applicationsOptionsAvailable.forEach(_appAvail => {
                const organismApplication = organismApplications.find((app) => app.appId === _appAvail.appId());
                if (organismApplication) {
                    this.organismApplicationsOptions.push(organismApplication);
                } else {
                    const virtualOrganismApplication = {
                        id: null,
                        appId: _appAvail.appId(),
                        enabled: false,
                        endDate: _appAvail.hasTrialAvailable() ? null : new Date(),
                        state: OrganismApplicationStates.DISABLED,
                        metadata: {}
                    };
                    this.organismApplicationsOptions.push(virtualOrganismApplication);
                }
            });
        });
        this._certificationService.listLite({
            organismType: 'certifier',
            siret: this.organism.siret
        }).subscribe((certifications) => {
            this.certificationAllowAudits = certifications.payload?.filter((certification) => certification.allowAudits).length;
        });
        this.certificationFoldersCounter = this.subscription.certificationFoldersNumberCount;
        this.certificationFoldersNumberCap = this.subscription.certificationFoldersNumberCap === 0 ? this.defaultCertificationFoldersNumberCap :
            this.subscription.certificationFoldersNumberCap ?? this.defaultCertificationFoldersNumberCap;
        const daysBetweenSubscription = (new Date(this.subscription.certifierPeriodEndDate).getTime() -
            new Date(this.subscription.certifierPeriodStartDate).getTime()) / (1000 * 3600 * 24);
        this.isAnnualSubscription = daysBetweenSubscription > 31;
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    subscriptionLink(subscriptionCertifierType?: SubscriptionCertifierTypes, options?: SubscriptionOptions): void {
        this._subscriptionService.showStripeCertifierAccount({
            subscriptionCertifierType: subscriptionCertifierType ?? null,
            options: options
        }).subscribe(response => {
            document.location.href = response.url;
        });
    }

    switchToTrial(): void {
        this.trialErrorMessages = [];
        this.isSwitching = true;
        this._store.dispatch(new UpdateSubscription(this.subscription.id, {certifier: {type: SubscriptionCertifierTypes.TRIAL}})).pipe(
            mergeMap(() => this._store.selectOnce(SubscriptionState.subscription))
        ).subscribe({
            next: () => {
                window.location.reload();
            },
            error: (httpErrorResponse: HttpErrorResponse) => {
                this.trialErrorMessages = (httpErrorResponse.error as ApiError).errorMessages;
                this.isSwitching = false;
            }
        });
    }

    closeModal(): void {
        this.dialogRef.close();
    }

    switchToTrainingAction($event): void {
        this.shownSubscriptionType.emit(SubscriptionTypes.TRAINING);
    }

    isCustomer(): boolean {
        return [SubscriptionCertifierTypes.UNLIMITED, SubscriptionCertifierTypes.USAGE].includes(this.subscription.certifierType);
    }

    getMail(subscriptionCertifierType: SubscriptionCertifierTypes, certifierPlus: boolean): string {
        const currentSubscription =
            this.subscription.certifierType === SubscriptionCertifierTypes.UNLIMITED ? 'Illimité' :
                (this.subscription.certifierType === SubscriptionCertifierTypes.USAGE && this.subscription.allowCertifierPlus ? 'Premium Certificateur' : 'Standard Certificateur');
        const targetSubscription = subscriptionCertifierType === SubscriptionCertifierTypes.UNLIMITED ? 'Illimité' :
            (subscriptionCertifierType === SubscriptionCertifierTypes.USAGE && certifierPlus ? 'Premium Certificateur' : 'Standard Certificateur');
        return 'mailto:<EMAIL>?' +
            'subject=Modification de l\'abonnement&' +
            'body=Bonjour l\'équipe Wedof, %0D%0A  %0D%0A Je souhaiterais modifier mon abonnement actuel ' + currentSubscription + ' pour l\'organisme ' +
            this.organism.name + ' ' + this.organism.siret + ' ' + ' et avoir l\'abonnement ' + targetSubscription + ' %0D%0A %0D%0A' + 'Bien à vous, %0D%0A ' + this.user.name;
    }

    getCertificationFolderPrice(certifierPlus: boolean): number {
        return certifierPlus ? 8.90 : 9.90;
    }

    sendMail(): void {
        window.location.href = 'mailto:<EMAIL>?' +
            'subject=Activation Audit &' +
            'body=Bonjour l\'équipe Wedof, %0D%0A  %0D%0A Je souhaiterais profiter de l\'abonnement sur la gestion des audits.' +
            '%0D%0A %0D%0A' + 'Bien à vous, %0D%0A ' + this.user.name;
    }

    convertDate(dateToConvert: Date): string {
        return moment(new Date(dateToConvert)).format('YYYY-MM-DD');
    }

    getApplicationName(appId: string): string {
        return this.applicationsOptionsAvailable.find(app => app.appId() === appId).appName();
    }
}
