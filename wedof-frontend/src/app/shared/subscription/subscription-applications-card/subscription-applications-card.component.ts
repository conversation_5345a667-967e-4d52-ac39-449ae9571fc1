import {Component, Input, OnInit} from '@angular/core';
import {MatDialog} from '@angular/material/dialog';
import {DialogUpgradeSubscriptionComponent} from '../dialog-upgrade-subscription/dialog-upgrade-subscription.component';
import {Subscription, SubscriptionTypes} from '../../api/models/subscription';
import {Organism} from '../../api/models/organism';
import {ApplicationConfigPromotional, ApplicationConfigPromotionalEntry} from '../../api/models/applications';

@Component({
    selector: 'app-subscription-applications-card',
    templateUrl: './subscription-applications-card.component.html',
    styleUrls: ['./subscription-applications-card.component.scss']
})
export class SubscriptionApplicationsCardComponent implements OnInit {

    configPromotionalEntry: ApplicationConfigPromotionalEntry;

    @Input() type: string;
    @Input() fromPage: string;
    @Input() subscriptionType: string;
    @Input() organism: Organism;
    @Input() subscription: Subscription;

    constructor(private _dialog: MatDialog) {
    }

    ngOnInit(): void {
        this.configPromotionalEntry = ApplicationConfigPromotional[this.type];
    }

    openDialogSubscription(): void {
        this._dialog.open(DialogUpgradeSubscriptionComponent, {
            panelClass: ['full-page-scroll-50'],
            data: {
                organism: this.organism,
                subscription: this.subscription,
                fromPage: this.fromPage,
                subscriptionTypeToShow: this.subscriptionType === 'RegistrationFolder' ? SubscriptionTypes.TRAINING : SubscriptionTypes.CERTIFIER
            }
        });
    }

}
