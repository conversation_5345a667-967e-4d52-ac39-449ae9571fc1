<mat-card class="flex-col mt-3 p-5 pb-0 pt-3 border shadow-none sm">
    <div *ngIf="configPromotionalEntry.title"
         class="text-xl mb-2 font-semibold card-loading-show">{{configPromotionalEntry.title  | translate}}</div>
    <img class="mb-2" *ngIf="configPromotionalEntry.image"
         src="{{configPromotionalEntry.image}}"
         alt="{{configPromotionalEntry.title}}"/>
    <div class="light:bg-cool-gray-50 dark:bg-cool-gray-700 border mb-4 p-4">
        <ul>
            <li *ngFor="let description of configPromotionalEntry.descriptions"
                class="flex items-center pb-1">
                <mat-icon svgIcon="check" color="primary" class="icon-small mr-2"></mat-icon>
                {{description | translate}}
            </li>
        </ul>
        <div
            class="flex justify-center mt-4 mb-3">
            <a class="no-underline cursor-pointer" (click)="openDialogSubscription()">
                {{ 'private.layout.user.subscription.' + (subscriptionType === 'RegistrationFolder' ? 'changeSubscription' : 'changeSubscriptionCertifier') | translate}}
            </a>
        </div>
    </div>
</mat-card>
