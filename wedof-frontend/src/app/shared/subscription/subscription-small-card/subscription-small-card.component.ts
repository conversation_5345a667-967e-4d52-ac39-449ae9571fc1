import {Component, ElementRef, EventEmitter, Input, OnDestroy, OnInit, Output} from '@angular/core';
import {DialogUpgradeSubscriptionComponent} from '../dialog-upgrade-subscription/dialog-upgrade-subscription.component';
import {
    Subscription,
    SubscriptionConfigPromotional,
    SubscriptionConfigPromotionalEntry,
    SubscriptionTypes
} from '../../api/models/subscription';
import {MatDialog} from '@angular/material/dialog';
import {Organism} from '../../api/models/organism';
import {BaseCardComponentDirective, RequiredCallSuper} from '../../utils/base-card/base-card.directive';

@Component({
    selector: 'app-subscription-small-card',
    templateUrl: './subscription-small-card.component.html',
    styleUrls: ['./subscription-small-card.component.scss']
})
export class SubscriptionSmallCardComponent extends BaseCardComponentDirective implements OnInit, <PERSON><PERSON><PERSON>roy {
    static COMPONENT_ID = '';
    subscriptionConfigPromotionalEntry: SubscriptionConfigPromotionalEntry;
    loading = false;

    @Input() organism?: Organism;
    @Input() subscription?: Subscription;
    @Input() fromPage?: string;
    @Input() type?: string;
    @Input() showFullCard = true;
    @Input() showTraining = false;
    @Input() shortCutId?: string;
    @Input() submissionText?: string;
    @Input() errorMessages?: string[];
    @Output() privateUpdate: EventEmitter<void> = new EventEmitter<void>();

    constructor(private _dialog: MatDialog, private _el: ElementRef) {
        super(SubscriptionSmallCardComponent.COMPONENT_ID, _el);
    }

    ngOnInit(): void {
        this.updateShortcutId(this.shortCutId);
        this.panelLoading();
        this.subscriptionConfigPromotionalEntry = SubscriptionConfigPromotional[this.type];
        this.panelLoaded();
    }

    action(): void {
        if (this.organism && this.subscription) {
            this._dialog.open(DialogUpgradeSubscriptionComponent, {
                panelClass: ['full-page-scroll-50'],
                data: {
                    organism: this.organism,
                    subscription: this.subscription,
                    fromPage: this.fromPage,
                    subscriptionTypeToShow: this.showTraining ? SubscriptionTypes.TRAINING : SubscriptionTypes.CERTIFIER
                }
            });
        } else {
            this.loading = true;
            this.privateUpdate.emit();
            setTimeout(() => {
                this.loading = false;
            }, 1000);
        }
    }

    ngOnDestroy(): RequiredCallSuper {
        return super.ngOnDestroy();
    }
}
