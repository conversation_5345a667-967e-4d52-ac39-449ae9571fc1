<mat-card class="flex-col mt-3 p-5 pb-0 pt-3 border shadow-none sm" *ngIf="showFullCard; else notFullCard">
    <div *ngIf="subscriptionConfigPromotionalEntry.title"
         class="text-xl mb-2 font-semibold card-loading-show">{{subscriptionConfigPromotionalEntry.title  | translate}}</div>
    <img class="mb-2" *ngIf="subscriptionConfigPromotionalEntry.image"
         src="{{subscriptionConfigPromotionalEntry.image}}"
         alt="{{subscriptionConfigPromotionalEntry.title}}"/>
    <ng-template [ngTemplateOutlet]="showPromoCardDescription">
    </ng-template>
</mat-card>

<ng-template #notFullCard>
    <ng-template [ngTemplateOutlet]="showPromoCardDescription">
    </ng-template>
</ng-template>

<ng-template #showPromoCardDescription>
    <div class="light:bg-cool-gray-50 dark:bg-cool-gray-700 border mb-4 p-4">
        <ul>
            <li *ngFor="let description of subscriptionConfigPromotionalEntry.descriptions"
                class="flex items-center pb-1">
                <mat-icon svgIcon="check" color="primary" class="icon-small mr-2"></mat-icon>
                {{description | translate}}
            </li>
        </ul>
        <div
            class="flex justify-center mt-4 mb-3">
            <ng-container *ngIf="loading; else showText">
                <mat-progress-spinner class="m-auto" [diameter]="24" mode="indeterminate"></mat-progress-spinner>
            </ng-container>
            <ng-template #showText>
                <a class="no-underline cursor-pointer" (click)="action()">
                    <ng-container *ngIf="submissionText; else nolabel">
                        {{submissionText | translate}}
                    </ng-container>
                    <ng-template #nolabel>
                        {{ 'private.layout.user.subscription.' + (showTraining ? 'changeSubscription' : 'changeSubscriptionCertifier') | translate}}
                    </ng-template>
                </a>
            </ng-template>
        </div>
        <mat-error *ngIf="errorMessages?.length" class="mb-4">
            <ul>
                <li *ngFor="let errorMessage of errorMessages">{{ errorMessage }}</li>
            </ul>
        </mat-error>
    </div>
</ng-template>
