<div *ngIf="subscriptionTypeToShow === SubscriptionTypes.TRAINING; else certifierSubscriptionDialog">
    <app-dialog-upgrade-training-subscription-plan
        [organism]="dialogData.organism"
        [subscription]="dialogData.subscription"
        [fromPage]="dialogData.fromPage"
        [action]="dialogData.action"
        (showedSubscriptionType)="subscriptionTypeToShow = $event"
    >
    </app-dialog-upgrade-training-subscription-plan>
</div>

<ng-template #certifierSubscriptionDialog>
    <app-dialog-upgrade-certifier-subscription-plan
        [organism]="dialogData.organism"
        [subscription]="dialogData.subscription"
        [fromPage]="dialogData.fromPage"
        [action]="dialogData.action"
        (shownSubscriptionType)="subscriptionTypeToShow = $event"
    >
    </app-dialog-upgrade-certifier-subscription-plan>
</ng-template>
