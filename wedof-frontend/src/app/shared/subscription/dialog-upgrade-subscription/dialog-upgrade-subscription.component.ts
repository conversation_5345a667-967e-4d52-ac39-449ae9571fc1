import {Component, Inject} from '@angular/core';
import {MAT_DIALOG_DATA} from '@angular/material/dialog';
import {Organism} from '../../api/models/organism';
import {Subscription, SubscriptionTypes} from '../../api/models/subscription';

@Component({
    selector: 'app-dialog-upgrade-subscription',
    templateUrl: './dialog-upgrade-subscription.component.html',
    styleUrls: ['./dialog-upgrade-subscription.component.scss']
})
export class DialogUpgradeSubscriptionComponent {

    public subscriptionTypeToShow: SubscriptionTypes;
    public SubscriptionTypes = SubscriptionTypes;

    constructor(
        @Inject(MAT_DIALOG_DATA) public dialogData: {
            organism: Organism
            subscription: Subscription,
            fromPage?: string;
            action?: boolean;
            subscriptionTypeToShow?: SubscriptionTypes;
        }
    ) {
        this.subscriptionTypeToShow = this.dialogData.subscriptionTypeToShow ?? SubscriptionTypes.TRAINING;
    }
}
