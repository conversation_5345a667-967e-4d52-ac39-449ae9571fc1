import {Component, EventEmitter, Input, OnDestroy, OnInit, Output} from '@angular/core';
import {MatDialogRef} from '@angular/material/dialog';
import {OrganismApplicationService} from '../../api/services/organism-application.service';
import {SubscriptionService} from '../../api/services/subscription.service';
import {
    Subscription,
    SubscriptionTrainingTypes,
    SubscriptionTypes
} from 'app/shared/api/models/subscription';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../errors/errors.types';
import '../../utils/date-extension-utils';
import {Organism} from '../../api/models/organism';
import {formatMomentToIsoDate} from '../../utils/date-utils';
import moment from 'moment';
import {Select, Store} from '@ngxs/store';
import {FetchSubscription, SubscriptionState, UpdateSubscription} from '../../api/state/subscription.state';
import {mergeMap, takeUntil} from 'rxjs/operators';
import {UserState} from '../../api/state/user.state';
import {Observable, Subject} from 'rxjs';
import {User} from '../../api/models/user';
import {OrganismApplication, OrganismApplicationStates} from '../../api/models/organism-application';
import {Application} from '../../../applications/shared/application.interface';
import {ApplicationsService} from '../../../applications/shared/applications.service';

@Component({
    selector: 'app-dialog-upgrade-training-subscription-plan',
    templateUrl: './dialog-upgrade-training-subscription-plan.component.html',
    styleUrls: ['./dialog-upgrade-training-subscription-plan.component.scss']
})
export class DialogUpgradeTrainingSubscriptionPlanComponent implements OnInit, OnDestroy {

    SubscriptionTrainingTypes = SubscriptionTrainingTypes;
    OrganismApplicationStates = OrganismApplicationStates;

    counterApplications = 0;
    registrationFoldersCounter = 0;
    certificationFoldersCounter = 0;
    isSwitching = false;
    errorMessages: string[] = [];
    remainingDays = '0';
    formatMomentPeriodStartDate: string;
    formatMomentPeriodEndDate: string;
    user: User;
    loadingWorkflow = false;
    organismApplicationsOptions: OrganismApplication[] = [];
    applicationsOptionsAvailable: Application[] = [];

    @Input() organism: Organism;
    @Input() subscription: Subscription;
    @Input() fromPage?: string;
    @Input() action?: boolean;
    @Output() showedSubscriptionType: EventEmitter<SubscriptionTypes> = new EventEmitter<SubscriptionTypes>();

    @Select(UserState.user) user$: Observable<User>;

    private _unsubscribeAll = new Subject<void>();

    constructor(
        private _store: Store,
        private _subscriptionService: SubscriptionService,
        private _applicationsService: ApplicationsService,
        private _organismApplicationService: OrganismApplicationService,
        public dialogRef: MatDialogRef<DialogUpgradeTrainingSubscriptionPlanComponent>
    ) {
    }

    ngOnInit(): void {
        this.applicationsOptionsAvailable = this._applicationsService.findAll().filter((application) => application.isSubscriptionOption());
        this.user$.pipe(takeUntil(this._unsubscribeAll)).subscribe(user => {
            this.user = user;
        });
        if (this.subscription.trainingType === SubscriptionTrainingTypes.TRIAL) {
            this.remainingDays = new Date(this.subscription.trainingPeriodEndDate).remainingDays();
        }
        this._organismApplicationService.findAll().subscribe((organismApplications) => {
            organismApplications.forEach((application) => {
                if (application.enabled === true) {
                    this.counterApplications += 1;
                }
            });
            this.applicationsOptionsAvailable.forEach(_appAvail => {
                const organismApplication = organismApplications.find((app) => app.appId === _appAvail.appId());
                if (organismApplication) {
                    this.organismApplicationsOptions.push(organismApplication);
                } else {
                    const virtualOrganismApplication = {
                        id: null,
                        appId: _appAvail.appId(),
                        enabled: false,
                        endDate: _appAvail.hasTrialAvailable() ? null : new Date(),
                        state: OrganismApplicationStates.DISABLED,
                        metadata: {}
                    };
                    this.organismApplicationsOptions.push(virtualOrganismApplication);
                }
            });
        });
        const isAccess = (this.subscription.trainingType === SubscriptionTrainingTypes.ACCESS || this.subscription.trainingType === SubscriptionTrainingTypes.ACCESS_PLUS);
        this.registrationFoldersCounter = this.subscription.registrationFoldersNumberCount;
        this.certificationFoldersCounter = this.subscription.certificationFoldersNumberCount;
        this.formatMomentPeriodStartDate = isAccess ?
            formatMomentToIsoDate(moment(new Date(this.subscription.registrationFolderNumberPeriodStartDate)).startOf('day')) :
            formatMomentToIsoDate(moment(new Date(this.subscription.trainingPeriodStartDate)).startOf('day'));
        this.formatMomentPeriodEndDate = isAccess ?
            formatMomentToIsoDate(moment(new Date(this.subscription.registrationFolderNumberPeriodEndDate)).startOf('day')) :
            formatMomentToIsoDate(moment(new Date(this.subscription.trainingPeriodEndDate)).endOf('day'));
    }

    subscriptionLink(subscriptionTrainingType?: SubscriptionTrainingTypes): void {
        this._subscriptionService.showStripeTrainingAccount(subscriptionTrainingType ?? null).subscribe(response => {
            document.location.href = response.url;
        });
    }

    switchToTrial(): void {
        this.errorMessages = [];
        this.isSwitching = true;
        this._store.dispatch(new UpdateSubscription(this.subscription.id, {training: {type: SubscriptionTrainingTypes.TRIAL}})).pipe(
            mergeMap(() => this._store.selectOnce(SubscriptionState.subscription))
        ).subscribe({
            next: () => {
                window.location.reload();
            },
            error: (httpErrorResponse: HttpErrorResponse) => {
                this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
                this.isSwitching = false;
            }
        });
    }

    closeModal(): void {
        this.dialogRef.close();
    }

    switchToCertifierAction($event): void {
        this.showedSubscriptionType.emit(SubscriptionTypes.CERTIFIER);
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    enableWorkflow(): boolean {
        this.loadingWorkflow = true;
        this._organismApplicationService.enable('workflow').subscribe((data) => {
            if (data) {
                setTimeout(() => {
                    this._store.dispatch(new FetchSubscription()).pipe(
                        mergeMap(() => {
                            return this._store.selectOnce(SubscriptionState.subscription);
                        }),
                    ).subscribe().add(() => {
                        this.loadingWorkflow = false;
                    });
                }, 5000);
            } else {
                this.loadingWorkflow = false;
            }
        });
        return false;
    }

    getApplicationName(appId: string): string {
        return this.applicationsOptionsAvailable.find(app => app.appId() === appId).appName();
    }
}
