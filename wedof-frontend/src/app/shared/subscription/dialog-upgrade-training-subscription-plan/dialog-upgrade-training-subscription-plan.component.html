<app-dialog-layout [title]="'private.layout.user.subscription.modal.title.training' | translate"
                   cancelText="{{'common.actions.close' | translate}}"
                   (dialogClose)="closeModal()">

    <treo-message *ngIf="action" type="info"
                  [showIcon]="false" appearance="outline">
        <p class="py-2">{{'private.layout.user.subscription.help.action' | translate }}</p>
    </treo-message>

    <div class="flex flex-row mt-5">
        <div class="w-full">
            <table class="flex flex-col">
                <ng-container *ngIf="subscription.trainingType !== SubscriptionTrainingTypes.NONE">
                    <tr class="flex justify-between">
                        <th class="flex flex-row items-center">
                            <a class="flex flex-row items-center" (click)="this.closeModal()"
                               [routerLink]="'/formation/dossiers/liste'"
                               [queryParams]="{period: 'custom', filterOnStateDate: 'notProcessedDate', since: formatMomentPeriodStartDate , until : formatMomentPeriodEndDate}"
                            >
                                <ng-container
                                    *ngIf="subscription.registrationFolderNumberPeriodEndDate && subscription.registrationFoldersNumberLimit; else showCurrent">
                                    {{'private.layout.user.subscription.registrationFoldersLimit' | translate : {
                                    period: subscription.registrationFolderNumberPeriodEndDate ? '(remise à zéro le ' + (subscription.registrationFolderNumberPeriodEndDate |  date:'mediumDate') + ')' : ''
                                }
                                    }}
                                </ng-container>
                                <ng-template #showCurrent>
                                    {{'private.layout.user.subscription.registrationFoldersLimit' | translate : {
                                    period: '(période en cours)'
                                } }}
                                </ng-template>
                            </a>
                        </th>
                        <td class="flex justify-center items-center">
                            <a class="no-underline flex flex-row items-center" (click)="this.closeModal()"
                               [routerLink]="'/formation/dossiers/liste'"
                               [queryParams]="{period: 'custom', filterOnStateDate: 'notProcessedDate', since: formatMomentPeriodStartDate , until : formatMomentPeriodEndDate}"
                            >
                                <mat-icon class="pr-1" matTooltip="Voir les dossiers créés sur la période"
                                          [svgIcon]="'help'"></mat-icon>
                                <mat-icon class="pr-1"
                                          *ngIf="subscription.trainingType === SubscriptionTrainingTypes.FREE && subscription.registrationFoldersNumberCount >= subscription.registrationFoldersNumberLimit"
                                          color="warn" [svgIcon]="'warning'"></mat-icon>
                                {{registrationFoldersCounter}}
                                / {{subscription.registrationFoldersNumberLimit === null ? 'Illimités' : subscription.registrationFoldersNumberLimit}}
                            </a>
                        </td>
                    </tr>
                    <tr class="flex justify-between">
                        <th class="flex flex-row items-center">{{'private.layout.user.subscription.activeApps' | translate}}</th>
                        <td class="flex flex-row items-center"> {{counterApplications}}</td>
                    </tr>
                    <tr class="flex justify-between" *ngIf="subscription.smsSentNumberPeriodStartDate">
                        <th class="flex flex-row items-center">{{'private.layout.user.subscription.smsSent.title' | translate}}</th>
                        <td class="flex flex-row items-center">
                            <mat-icon class="pr-1" matTooltip="{{'private.layout.user.subscription.smsSent.tooltip' | translate: {
                                    startDate: subscription.smsSentNumberPeriodStartDate | date:'mediumDate',
                                    endDate: subscription.smsSentNumberPeriodEndDate | date:'mediumDate'
                            }
                            }}" [svgIcon]="'help'"></mat-icon>
                            {{subscription.smsSentNumberCount}}
                        </td>
                    </tr>
                    <tr class="flex justify-between">
                        <th class="flex flex-row items-center">{{'common.token.requestCountTitle' | translate}}</th>
                        <td class="flex flex-row items-center">{{subscription.requestCount}}</td>
                    </tr>
                    <ng-container *ngFor="let organismApplication of organismApplicationsOptions">
                        <tr class="flex justify-between">
                            <th class="flex flex-row items-center">{{ getApplicationName(organismApplication.appId) }}</th>
                            <td class="flex flex-row items-center">
                                <a (click)="closeModal()"
                                   [routerLink]="[organismApplication.state != OrganismApplicationStates.DISABLED ? '/mes-applications/'+organismApplication.appId+'/reglages' : '/mes-applications']"
                                   [state]="{'enableAppId': (organismApplication.state == OrganismApplicationStates.DISABLED ? organismApplication.appId : null)}">
                                    {{'private.layout.user.subscription.subscriptionOptionStates.' + organismApplication.state | translate}}
                                    <ng-container
                                        *ngIf="organismApplication.endDate">
                                        ({{organismApplication.endDate | date:'mediumDate'}})
                                    </ng-container>
                                </a>
                            </td>
                        </tr>
                    </ng-container>
                    <tr class="flex justify-between">
                        <th class="flex flex-row items-center"
                            *ngIf="subscription.trainingType !== SubscriptionTrainingTypes.TRIAL">{{'private.layout.user.subscription.startDate' | translate}}</th>
                        <th class="flex flex-row items-center"
                            *ngIf="subscription.trainingType === SubscriptionTrainingTypes.TRIAL">{{'private.layout.user.subscription.startTrial' | translate}}</th>
                        <td class="flex flex-row items-center">{{subscription.trainingStartDate |  date:'mediumDate'}}</td>
                    </tr>
                </ng-container>
                <ng-container *ngIf="subscription.trainingPendingCancellation; else noCancellation">
                    <tr class="flex justify-between">
                        <th class="flex flex-row items-center">{{'private.layout.user.subscription.endDate' | translate}}</th>
                        <td class="flex flex-row items-center text-red font-bold">{{subscription.trainingPeriodEndDate |  date:'mediumDate'}}</td>
                    </tr>
                </ng-container>
                <ng-template #noCancellation>
                    <tr class="flex justify-between"
                        *ngIf="!subscription.trainingPeriodEndDate.includes('2199-12-31') && subscription.trainingType !== SubscriptionTrainingTypes.TRIAL">
                        <th class="flex flex-row items-center">{{'private.layout.user.subscription.period' | translate}}</th>
                        <td class="flex flex-row items-center">
                            {{'private.layout.user.subscription.fromTo' | translate : {
                            from: subscription.trainingPeriodStartDate|  date:'mediumDate',
                            to: subscription.trainingPeriodEndDate |  date:'mediumDate'
                        } }}
                        </td>
                    </tr>
                </ng-template>
            </table>
            <div class="flex justify-between mt-3">
                <div>
                    <button
                        color="primary" mat-flat-button class="mr-2"
                        (click)="subscriptionLink()"
                        *ngIf="subscription.isStripeCustomer && user.isOwner">{{ 'private.layout.user.subscription.invoices.downloadInvoice' | translate }}
                    </button>
                    <button *ngIf="organism.isCertifierOrganism && subscription.certifierType"
                            mat-flat-button
                            (click)="switchToCertifierAction($event)">{{ 'private.layout.user.subscription.modal.access.certifier' | translate }}</button>
                </div>
                <button
                    color="primary" mat-flat-button
                    (click)="subscriptionLink()"
                    *ngIf="subscription.trainingType !== SubscriptionTrainingTypes.FREE && subscription.trainingType !== SubscriptionTrainingTypes.TRIAL && subscription.trainingType !== SubscriptionTrainingTypes.NONE">{{ 'private.layout.user.subscription.manageMySubscription' | translate }}
                </button>

            </div>
        </div>
    </div>
    <div class="flex flex-row items-center mt-4 mb-4"
         *ngIf="fromPage && subscription.trainingType !== SubscriptionTrainingTypes.PREMIUM">
        <mat-icon color="primary">lightbulb</mat-icon>
        <p [innerHTML]=" 'private.layout.user.subscription.help.' + fromPage | translate "></p>
    </div>

    <table class="w-full mt-5 mb-10 table offers">
        <colgroup>
            <col>
            <col
                [class]="subscription.trainingType === SubscriptionTrainingTypes.API ? 'highlightCard' : '' ">
            <col
                [class]="subscription.trainingType === SubscriptionTrainingTypes.ESSENTIAL ? 'highlightCard' : '' ">
            <col
                [class]="subscription.trainingType === SubscriptionTrainingTypes.STANDARD ? 'highlightCard' : '' ">
            <col
                [class]="subscription.trainingType === SubscriptionTrainingTypes.PREMIUM || [SubscriptionTrainingTypes.TRIAL.toString()].includes(subscription.trainingType.toString()) ? 'highlightCard' : ''">
        </colgroup>
        <thead class="w-1/5 mr-3">
        <tr class="h-5" *ngIf="subscription.trainingType === SubscriptionTrainingTypes.TRIAL">
            <th class="w-1/5 mr-3"></th>
            <th class="w-1/5 mr-3"></th>
            <th class="w-1/5 mr-3"></th>
            <th class="w-1/5 mr-3"></th>
            <th [class]="subscription.trainingType === SubscriptionTrainingTypes.TRIAL ? 'rounded-t w-1/5 mr-3 pt-2 pb-2 bg-trial-purple' : ''">
                <p *ngIf="subscription.trainingType === SubscriptionTrainingTypes.TRIAL"
                   class="text-white">{{'private.layout.user.subscription.certifier.trial.offer' | translate }}<br>
                    {{ remainingDays }} {{ 'private.layout.user.subscription.training.trial.remaining-days' | translate }}
                </p>
            </th>
            <th></th>
        </tr>
        <tr>
            <th class="w-1/5 mr-3"></th>
            <th [class]="subscription.trainingType === SubscriptionTrainingTypes.API ? 'rounded-t w-1/5 mr-3 pt-4' : 'w-1/6 mr-3 pt-4'">{{'private.layout.user.subscription.training.api.offer' | translate}}</th>
            <th [class]="subscription.trainingType === SubscriptionTrainingTypes.ESSENTIAL ? 'rounded-t w-1/5 mr-3 pt-4' : 'w-1/6 mr-3 pt-4'">{{'private.layout.user.subscription.training.essential.offer' | translate}}</th>
            <th [class]="subscription.trainingType === SubscriptionTrainingTypes.STANDARD ? 'rounded-t w-1/5 mr-3 pt-4' : 'w-1/6 mr-3 pt-4'">{{'private.layout.user.subscription.training.standard.offer' | translate}}</th>
            <th [class]="subscription.trainingType === SubscriptionTrainingTypes.PREMIUM || subscription.trainingType === SubscriptionTrainingTypes.TRIAL? 'rounded-t w-1/5 pt-4' : 'w-1/6 pt-4'">{{'private.layout.user.subscription.training.premium.offer' | translate}}</th>
        </tr>
        </thead>
        <tbody>

        <tr class="m-2">
            <td></td>
            <td>
                <strong>{{'private.layout.user.subscription.training.api.price' | translate}}</strong>  {{'private.layout.user.subscription.monthly' | translate}}
            </td>
            <td>
                <strong>{{'private.layout.user.subscription.training.essential.price' | translate}}</strong>  {{'private.layout.user.subscription.monthly' | translate}}
            </td>
            <td>
                <strong>{{'private.layout.user.subscription.training.standard.price' | translate}}</strong>  {{'private.layout.user.subscription.monthly' | translate}}
            </td>
            <td>
                <strong>{{'private.layout.user.subscription.training.premium.price' | translate}}</strong>  {{'private.layout.user.subscription.monthly' | translate}}
            </td>
        </tr>
        <tr>
            <td class="font-bold no-text-center">{{'private.layout.user.subscription.table.header.userLimit' | translate}}
            </td>
            <td>1</td>
            <td>5</td>
            <td>
                <mat-icon color="primary" class="opacity-54">all_inclusive</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="opacity-54">all_inclusive</mat-icon>
            </td>
        </tr>
        <tr>
            <td class="font-bold flex items-center no-text-center">{{'private.layout.user.subscription.table.header.folders' | translate}}
                <mat-icon class="pl-1" [matTooltip]="'private.layout.user.subscription.folders.explanation' | translate"
                          [matTooltipPosition]="'above'"
                          [matTooltipShowDelay]="500"
                          [svgIcon]="'help'"></mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="opacity-54">all_inclusive</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="opacity-54">all_inclusive</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="opacity-54">all_inclusive</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="opacity-54">all_inclusive</mat-icon>
            </td>
        </tr>
        <tr>
            <td class="font-bold flex items-center no-text-center">{{'private.layout.user.subscription.table.header.api' | translate}}
                <mat-icon class="pl-1" [matTooltip]="'private.layout.user.subscription.api.explanation' | translate"
                          [matTooltipPosition]="'above'"
                          [matTooltipShowDelay]="500"
                          [svgIcon]="'help'"></mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="opacity-54">check_circle</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="opacity-54">check_circle</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="opacity-54">check_circle</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="opacity-54">check_circle</mat-icon>
            </td>
        </tr>
        <tr>
            <td class="font-bold flex items-center no-text-center">
                <p>
                {{'private.layout.user.subscription.table.header.webhooks' | translate}}
                <mat-icon class="pl-1"
                          [matTooltip]="'private.layout.user.subscription.webhooks.explanation' | translate"
                          [matTooltipPosition]="'above'"
                          [matTooltipShowDelay]="500"
                          [svgIcon]="'help'"></mat-icon> </p>
            </td>
            <td>
                <mat-icon color="primary" class="opacity-54">check_circle</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="opacity-54">check_circle</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="opacity-54">check_circle</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="opacity-54">check_circle</mat-icon>
            </td>
        </tr>
        <tr>
            <td class="font-bold flex items-center no-text-center">{{'private.layout.user.subscription.table.header.automatisationEDOF' | translate}}
                <mat-icon class="pl-1"
                          [matTooltip]="'private.layout.user.subscription.automatisationEDOF.explanation' | translate"
                          [matTooltipPosition]="'above'"
                          [matTooltipShowDelay]="500"
                          [svgIcon]="'help'"></mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="opacity-54">check_circle</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="opacity-54">check_circle</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="opacity-54">check_circle</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="opacity-54">check_circle</mat-icon>
            </td>
        </tr>
        <!--<tr>
            <td class="font-font-bold flex items-center no-text-center">{{'private.layout.user.subscription.table.header.automatisationKairos' | translate}}
                <mat-icon class="pl-1" [matTooltip]="'private.layout.user.subscription.automatisationKairos.explanation' | translate"
                [matTooltipPosition]="'above'"
                [matTooltipShowDelay]="500"
                          [svgIcon]="'help'"></mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="opacity-54">check_circle</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="opacity-54">check_circle</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="opacity-54">check_circle</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="opacity-54">check_circle</mat-icon>
            </td>
        </tr>-->
        <tr>
            <td class="font-bold flex items-center no-text-center">
                <p>{{'private.layout.user.subscription.table.header.automatisationOpco' | translate}}
                <mat-icon class="pl-1"
                          [matTooltip]="'private.layout.user.subscription.automatisationOpco.explanation' | translate"
                          [matTooltipPosition]="'above'"
                          [matTooltipShowDelay]="500"
                          [svgIcon]="'help'"></mat-icon></p>
            </td>
            <td>
                <mat-icon color="primary" class="opacity-54">check_circle</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="opacity-54">check_circle</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="opacity-54">check_circle</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="opacity-54">check_circle</mat-icon>
            </td>
        </tr>
        <tr>
            <td class="font-bold">{{'private.layout.user.subscription.signature' | translate}} </td>
            <td>
                49€ HT / mois
            </td>
            <td>
                49€ HT / mois
            </td>
            <td>
                49€ HT / mois
            </td>
            <td>
                49€ HT / mois
            </td>
        </tr>
        <tr>
            <td class="font-bold">{{'private.layout.user.subscription.workflow' | translate}} </td>
            <td>
                49€ HT / mois
            </td>
            <td>
                49€ HT / mois
            </td>
            <td>
                49€ HT / mois
            </td>
            <td>
                49€ HT / mois
            </td>
        </tr>
        <tr>
            <td class="font-bold">{{'private.layout.user.subscription.table.header.crm' | translate}} </td>
            <td>
                <mat-icon color="warn" class="opacity-54">cancel</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="opacity-54">check_circle</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="opacity-54">check_circle</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="opacity-54">check_circle</mat-icon>
            </td>
        </tr>
        <tr>
            <td class="font-bold flex items-center">{{'private.layout.user.subscription.table.header.nocode' | translate}}
                <mat-icon class="pl-1" [matTooltip]="'private.layout.user.subscription.nocode.explanation' | translate"
                          [matTooltipPosition]="'above'"
                          [matTooltipShowDelay]="500"
                          [svgIcon]="'help'"></mat-icon>
            </td>
            <td>
                <mat-icon color="warn" class="opacity-54">cancel</mat-icon>
            </td>
            <td>
                <mat-icon color="warn" class="opacity-54">cancel</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="opacity-54">check_circle</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="opacity-54">check_circle</mat-icon>
            </td>
        </tr>
        <tr>
            <td class="font-bold">{{'private.layout.user.subscription.table.header.messageTemplates' | translate}} </td>
            <td>
                <mat-icon color="warn" class="opacity-54">cancel</mat-icon>
            </td>
            <td>
                <mat-icon color="warn" class="opacity-54">cancel</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="opacity-54">check_circle</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="opacity-54">check_circle</mat-icon>
            </td>
        </tr>
        <tr>
            <td class="font-bold">{{'private.layout.user.subscription.table.header.documents' | translate}} </td>
            <td>
                <mat-icon color="warn" class="opacity-54">cancel</mat-icon>
            </td>
            <td>
                <mat-icon color="warn" class="opacity-54">cancel</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="opacity-54">check_circle</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="opacity-54">check_circle</mat-icon>
            </td>
        </tr>
        <tr>
            <td class="font-bold">{{'private.layout.user.subscription.table.header.attendee' | translate}}
            </td>
            <td>
                <mat-icon color="warn" class="opacity-54">cancel</mat-icon>
            </td>
            <td>
                <mat-icon color="warn" class="opacity-54">cancel</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="opacity-54">check_circle</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="opacity-54">check_circle</mat-icon>
            </td>
        </tr>
        <tr>
            <td class="font-bold flex items-center">{{'private.layout.user.subscription.table.header.apps' | translate}}
                <mat-icon class="pl-1" [matTooltip]="'private.layout.user.subscription.apps.explanation' | translate"
                          [matTooltipPosition]="'above'"
                          [matTooltipShowDelay]="500"
                          [svgIcon]="'help'"></mat-icon>
            </td>
            <td>
                <mat-icon color="warn" class="opacity-54">cancel</mat-icon>
            </td>
            <td>
                <mat-icon color="warn" class="opacity-54">cancel</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="opacity-54">check_circle</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="opacity-54">check_circle</mat-icon>
            </td>
        </tr>
        <tr>
            <td class="font-bold flex items-center">{{'private.layout.user.subscription.table.header.financial' | translate}}
                <mat-icon class="pl-1"
                          [matTooltip]="'private.layout.user.subscription.analytics.explanation' | translate"
                          [matTooltipPosition]="'above'"
                          [matTooltipShowDelay]="500"
                          [svgIcon]="'help'"></mat-icon>
            </td>
            <td>
                <mat-icon color="warn" class="opacity-54">cancel</mat-icon>
            </td>
            <td>
                <mat-icon color="warn" class="opacity-54">cancel</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="opacity-54">check_circle</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="opacity-54">check_circle</mat-icon>
            </td>
        </tr>
        <tr>
            <td class="font-bold flex items-center">{{'private.layout.user.subscription.table.header.proposals' | translate}}
                <mat-icon class="pl-1"
                          [matTooltip]="'private.layout.user.subscription.proposals.explanation' | translate"
                          [matTooltipPosition]="'above'"
                          [matTooltipShowDelay]="500"
                          [svgIcon]="'help'"></mat-icon>
            </td>
            <td>
                <mat-icon color="warn" class="opacity-54">cancel</mat-icon>
            </td>
            <td>
                <mat-icon color="warn" class="opacity-54">cancel</mat-icon>
            </td>
            <td>
                <mat-icon color="warn" class="opacity-54">cancel</mat-icon>
            </td>
            <td>
                <mat-icon color="primary" class="opacity-54">check_circle</mat-icon>
            </td>
        </tr>
        <tr *ngIf="!subscription.trainingPartnership">
            <td></td>
            <td [class]="subscription.trainingType === SubscriptionTrainingTypes.API ? 'rounded-b' : ''">
                <button
                    *ngIf="(subscription.trainingType === SubscriptionTrainingTypes.NONE && !subscription.trainingTrialEndDate) else buttonApi"
                    id="switch-trial-button"
                    [disabled]="isSwitching"
                    color="primary" mat-flat-button class="w-5/6 mr-auto ml-auto flex mb-4 mt-4"
                    (click)="switchToTrial()">
                    <span *ngIf="!isSwitching">{{'private.layout.user.subscription.try'| translate }}</span>
                    <mat-progress-spinner class="mr-4" *ngIf="isSwitching"
                                          [diameter]="24"
                                          [mode]="'indeterminate'"></mat-progress-spinner>
                </button>
                <ng-template #buttonApi>
                    <button
                        color="primary" mat-flat-button class="w-5/6 mr-auto ml-auto flex mb-4 mt-4"
                        [disabled]="subscription.trainingType == SubscriptionTrainingTypes.API"
                        (click)="subscriptionLink(SubscriptionTrainingTypes.API)">{{ (subscription.trainingType === SubscriptionTrainingTypes.API ? 'private.layout.user.subscription.mySubscription' : 'private.layout.user.subscription.select') | translate }}
                    </button>
                </ng-template>
            </td>
            <td [class]="subscription.trainingType === SubscriptionTrainingTypes.ESSENTIAL ? 'rounded-b' : ''">
                <button
                    *ngIf="(subscription.trainingType === SubscriptionTrainingTypes.NONE && !subscription.trainingTrialEndDate) else buttonEssential"
                    id="switch-trial-button"
                    [disabled]="isSwitching"
                    color="primary" mat-flat-button class="w-5/6 mr-auto ml-auto flex mb-4 mt-4"
                    (click)="switchToTrial()">
                    <span *ngIf="!isSwitching">{{'private.layout.user.subscription.try'| translate }}</span>
                    <mat-progress-spinner class="mr-4" *ngIf="isSwitching"
                                          [diameter]="24"
                                          [mode]="'indeterminate'"></mat-progress-spinner>
                </button>
                <ng-template #buttonEssential>
                    <button
                        color="primary" mat-flat-button class="w-5/6 mr-auto ml-auto flex mb-4 mt-4"
                        [disabled]="subscription.trainingType == SubscriptionTrainingTypes.ESSENTIAL"
                        (click)="subscriptionLink(SubscriptionTrainingTypes.ESSENTIAL)">{{ (subscription.trainingType === SubscriptionTrainingTypes.ESSENTIAL ? 'private.layout.user.subscription.mySubscription' : 'private.layout.user.subscription.select') | translate }}
                    </button>
                </ng-template>
            </td>
            <td [class]="subscription.trainingType === SubscriptionTrainingTypes.STANDARD ? 'rounded-b' : ''">
                <button
                    *ngIf="(subscription.trainingType === SubscriptionTrainingTypes.NONE && !subscription.trainingTrialEndDate) else buttonStandard"
                    id="switch-trial-button"
                    [disabled]="isSwitching"
                    color="primary" mat-flat-button class="w-5/6 mr-auto ml-auto flex mb-4 mt-4"
                    (click)="switchToTrial()">
                    <span *ngIf="!isSwitching">{{'private.layout.user.subscription.try'| translate }}</span>
                    <mat-progress-spinner class="mr-4" *ngIf="isSwitching"
                                          [diameter]="24"
                                          [mode]="'indeterminate'"></mat-progress-spinner>
                </button>
                <ng-template #buttonStandard>
                    <button
                        color="primary" mat-flat-button class="w-5/6 mr-auto ml-auto flex mb-4 mt-4"
                        [disabled]="subscription.trainingType == SubscriptionTrainingTypes.STANDARD"
                        (click)="subscriptionLink(SubscriptionTrainingTypes.STANDARD)">{{ (subscription.trainingType === SubscriptionTrainingTypes.STANDARD ? 'private.layout.user.subscription.mySubscription' : 'private.layout.user.subscription.select') | translate }}
                    </button>
                </ng-template>
            </td>
            <td [class]="subscription.trainingType === SubscriptionTrainingTypes.PREMIUM ? 'rounded-b' : ''">
                <button
                    *ngIf="(subscription.trainingType === SubscriptionTrainingTypes.NONE && !subscription.trainingTrialEndDate) else buttonPremium"
                    id="switch-trial-button"
                    [disabled]="isSwitching"
                    color="primary" mat-flat-button class="w-5/6 mr-auto ml-auto flex mb-4 mt-4"
                    (click)="switchToTrial()">
                    <span *ngIf="!isSwitching">{{'private.layout.user.subscription.try'| translate }}</span>
                    <mat-progress-spinner class="mr-4" *ngIf="isSwitching"
                                          [diameter]="24"
                                          [mode]="'indeterminate'"></mat-progress-spinner>
                </button>
                <ng-template #buttonPremium>
                    <button
                        color="primary" mat-flat-button class="w-5/6 mr-auto ml-auto flex mb-4 mt-4"
                        [disabled]="subscription.trainingType == SubscriptionTrainingTypes.PREMIUM"
                        (click)="subscriptionLink(SubscriptionTrainingTypes.PREMIUM)">{{ (subscription.trainingType === SubscriptionTrainingTypes.PREMIUM ? 'private.layout.user.subscription.mySubscription' : 'private.layout.user.subscription.select') | translate }}
                    </button>
                </ng-template>
            </td>
        </tr>
        </tbody>
    </table>
    <p class="mt-3 mb-3 text-center" [innerHTML]="'private.layout.user.subscription.training.annual' | translate"></p>

    <!--<div class="text-center mt-3 mb-3 flex flex-col ml-auto mr-auto"
         *ngIf="subscription === OrganismApplicationStates.DISABLED">
        <button mat-flat-button (click)="enableWorkflow()" [disabled]="loadingWorkflow" color="primary">
            <mat-progress-spinner class="mr-4" *ngIf="loadingWorkflow" [diameter]="24"
                                  mode="indeterminate"></mat-progress-spinner>
            <ng-container
                *ngIf="!loadingWorkflow">{{'private.application.workflow.activate' | translate}}</ng-container>
        </button>
        <a class="text-secondary text-sm pt-1" href="/aide/guides/applications/workflow"
           target="_blank">{{'private.application.workflow.documentation' | translate}}</a>
    </div>-->

    <mat-card class="text-center m-auto p-5 shadow-none mb-2">
        <div class="mb-2"><strong>{{'private.layout.user.subscription.training.access.offer' | translate}}</strong>
        </div>
        <p>
            <strong>{{'private.layout.user.subscription.training.access.price' | translate}}</strong> {{'private.layout.user.subscription.monthly' | translate}}
        </p>
        <div class="flex items-center text-center">
            <mat-icon color="primary" class="opacity-54">check_circle</mat-icon>
            {{'private.layout.user.subscription.included.folderLimitAccess' | translate}}
            <mat-icon color="primary" class="opacity-54 ml-3">check_circle</mat-icon>
            {{'private.layout.user.subscription.included.standardIncluded' | translate}}
        </div>
        <button
            *ngIf="(subscription.trainingType === SubscriptionTrainingTypes.NONE && !subscription.trainingTrialEndDate) else buttonAccess"
            id="switch-trial-button"
            [disabled]="isSwitching"
            color="primary" mat-flat-button class="w-2/6 mr-auto ml-auto flex mb-4 mt-4"
            (click)="switchToTrial()">
            <span *ngIf="!isSwitching">{{'private.layout.user.subscription.try'| translate }}</span>
            <mat-progress-spinner class="mr-4" *ngIf="isSwitching"
                                  [diameter]="24"
                                  [mode]="'indeterminate'"></mat-progress-spinner>
        </button>
        <ng-template #buttonAccess>
            <button
                *ngIf="subscription.trainingType === SubscriptionTrainingTypes.NONE || subscription.trainingType === SubscriptionTrainingTypes.TRIAL || subscription.trainingType === SubscriptionTrainingTypes.ACCESS"
                color="primary" mat-flat-button class="w-2/6 mr-auto ml-auto flex mb-4 mt-4"
                [disabled]="subscription.trainingType === SubscriptionTrainingTypes.ACCESS"
                (click)="subscriptionLink(SubscriptionTrainingTypes.ACCESS)">{{ (subscription.trainingType === SubscriptionTrainingTypes.ACCESS ? 'private.layout.user.subscription.mySubscription' : 'private.layout.user.subscription.select') | translate }}
            </button>
        </ng-template>
    </mat-card>
    <div class="mb-2 flex flex-col items-center" *ngIf="errorMessages.length">
        <div class="flex items-center">
            <treo-message class="flex-auto" appearance="outline" [showIcon]="false" type="error">
                <ul>
                    <li *ngFor="let errorMessage of errorMessages">
                        {{ errorMessage }}
                    </li>
                </ul>
            </treo-message>
        </div>
    </div>
    <div *ngIf="!subscription.trainingPartnership; else contactPartnership" class="mb-5 flex flex-col items-center">
        <p>{{'private.layout.user.subscription.table.contact.question' | translate}}</p>
        <p>{{'private.layout.user.subscription.table.contact.contact' | translate}}
            : <a class="link font-semibold"
                 href="mailto:{{'private.support.mail' | translate}}">{{'private.support.mail' | translate}}</a>
            ou au <a
                href="tel:{{'private.layout.user.subscription.table.contact.phoneNumber' | translate}}">{{'private.layout.user.subscription.table.contact.phoneNumber' | translate}}</a>
        </p>
    </div>
    <ng-template #contactPartnership>
        <div class="mb-5 flex flex-col items-center">
            <p>{{'private.layout.user.subscription.table.contact.questionPartnership' | translate}}</p>
            <p>{{'private.layout.user.subscription.table.contact.contactPartnership' | translate : {partnership: subscription.trainingPartnership} }}</p>
        </div>
    </ng-template>
</app-dialog-layout>
