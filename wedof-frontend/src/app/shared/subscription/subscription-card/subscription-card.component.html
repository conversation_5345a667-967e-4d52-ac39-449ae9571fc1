<div class="bg-card rounded w-full flex h-full items-center" *ngIf="!loading">
    <div class="lg:flex lg:flex-row xl:flex xl:flex-row 2xl:flex 2xl:flex-row py-8 px-8 items-center">
        <div class="lg:w-1/2 xl:w-1/2 2xl:w-1/2" *ngIf="video">
            <video src="{{video}}" autoplay muted loop playsinline class="w-full object-contain"></video>
        </div>
        <div class="lg:w-1/2 xl:w-1/2 2xl:w-1/2" *ngIf="image"><img src="{{image}}" alt="{{image}}"/></div>
        <div class="lg:w-1/2 xl:w-1/2 2xl:w-1/2 flex flex-col pl-4">
            <div class="ml-auto mr-auto">
                <h1>{{subtitle | translate}}</h1>
                <p class="mb-10">{{explanation | translate}}</p>
                <ul *ngIf="description1">
                    <li class="flex items-center pb-1">
                        <mat-icon svgIcon="check" color="primary" class="mr-2"></mat-icon>
                        {{description1 | translate}} </li>
                    <li *ngIf="description2" class="flex items-center pb-1">
                        <mat-icon svgIcon="check" color="primary" class="mr-2"></mat-icon>
                        {{description2 | translate}}  </li>
                    <li *ngIf="description3" class="flex items-center pb-1">
                        <mat-icon svgIcon="check" color="primary" class="mr-2"></mat-icon>
                        {{description3 | translate}}  </li>
                    <li *ngIf="description4" class="flex items-center pb-1">
                        <mat-icon svgIcon="check" color="primary" class="mr-2"></mat-icon>
                        {{description4 | translate}}</li>
                    <li *ngIf="description5" class="flex items-center pb-1">
                        <mat-icon svgIcon="check" color="primary" class="mr-2"></mat-icon>
                        {{description5 | translate}}</li>
                </ul>
            </div>
            <div class="py-8 px-8 flex justify-center">
                <div class="mt-6" *ngIf="contactButton; else buttons">
                    <p class="pb-2">{{'private.support.content.text1' | translate}}
                        <a class="link font-semibold"
                           href="mailto:{{'private.support.mail' | translate}}">{{'private.support.mail' | translate}}</a>
                    </p>
                </div>
                <ng-template #buttons>
                    <button type="button" *ngIf="certifierSubscription && !enableAppId"
                            (click)="openSubscriptionDialog(SubscriptionTypes.CERTIFIER)" color="primary"
                            class="mt-6 mr-4"
                            mat-flat-button>
                        {{ (subscription.certifierType === SubscriptionCertifierTypes.NONE ? 'private.layout.user.subscription.chooseSubscriptionCertifier' : 'private.layout.user.subscription.changeSubscriptionCertifier') | translate}}
                    </button>
                    <div class="flex align-middle items-center mt-6 mr-4"
                         *ngIf="certifierSubscription && trainingSubscription && !enableAppId">ou
                    </div>
                    <button type="button" *ngIf="trainingSubscription && !enableAppId"
                            (click)="openSubscriptionDialog(SubscriptionTypes.TRAINING)" color="primary"
                            class="mt-6"
                            mat-flat-button>
                        {{ (subscription.trainingType === SubscriptionTrainingType.NONE ? 'private.layout.user.subscription.chooseSubscription' : 'private.layout.user.subscription.changeSubscription') | translate}}
                    </button>
                    <button type="button" *ngIf="enableAppId"
                            [routerLink]="['/mes-applications']" [state]="{enableAppId: enableAppId}" color="primary"
                            class="mt-6"
                            mat-flat-button>
                        {{ (hasTrialAvailable() ? 'private.layout.user.subscription.tryApp' : 'private.layout.user.subscription.enableApp') | translate}}
                    </button>
                </ng-template>
            </div>
        </div>
    </div>
</div>
