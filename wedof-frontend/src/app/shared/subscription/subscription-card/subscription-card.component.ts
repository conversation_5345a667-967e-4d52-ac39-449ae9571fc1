import {Component, Input, OnD<PERSON>roy, OnInit} from '@angular/core';
import {MatDialog} from '@angular/material/dialog';
import {
    Subscription,
    SubscriptionCertifierTypes,
    SubscriptionTrainingTypes,
    SubscriptionTypes
} from 'app/shared/api/models/subscription';
import {takeUntil} from 'rxjs/operators';
import {combineLatest, Observable, of, Subject} from 'rxjs';
import {Organism} from '../../api/models/organism';
import {DialogUpgradeSubscriptionComponent} from '../dialog-upgrade-subscription/dialog-upgrade-subscription.component';
import {Select} from '@ngxs/store';
import {SubscriptionState} from '../../api/state/subscription.state';
import {OrganismState} from '../../api/state/organism.state';
import {OrganismApplication, OrganismApplicationStates} from '../../api/models/organism-application';
import {ApplicationsService} from '../../../applications/shared/applications.service';
import {OrganismApplicationService} from '../../api/services/organism-application.service';

@Component({
    selector: 'app-subscription-card',
    templateUrl: './subscription-card.component.html',
    styleUrls: ['./subscription-card.component.scss']
})
export class SubscriptionCardComponent implements OnInit, OnDestroy {

    private _organism: Organism;
    subscription: Subscription;
    organismApplications: OrganismApplication[];
    loading = true;

    SubscriptionTypes = SubscriptionTypes;
    SubscriptionTrainingType = SubscriptionTrainingTypes;
    SubscriptionCertifierTypes = SubscriptionCertifierTypes;

    @Input() subtitle: string;
    @Input() explanation: string;
    @Input() description1?: string;
    @Input() description2?: string;
    @Input() description3?: string;
    @Input() description4?: string;
    @Input() description5?: string;
    @Input() image?: string;
    @Input() video?: string;
    @Input() enableAppId?: string;
    @Input() fromPage?: string;
    @Input() contactButton = false;
    @Input() certifierSubscription = false;
    @Input() trainingSubscription = true;

    @Select(SubscriptionState.subscription) subscription$: Observable<Subscription>;
    @Select(OrganismState.organism) organism$: Observable<Organism>;

    private _unsubscribeAll = new Subject<void>();

    constructor(
        private _dialog: MatDialog,
        private _applicationsService: ApplicationsService,
        private _organismApplicationService: OrganismApplicationService
    ) {
    }

    ngOnInit(): void {
        this.loading = true;
        combineLatest([
            this.organism$,
            this.subscription$,
            this.enableAppId ? this._organismApplicationService.findAll() : of([]),
        ]).pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe(([organism, subscription, organismApplications]) => {
            this._organism = organism;
            this.subscription = subscription;
            this.organismApplications = organismApplications;
            this.loading = false;
        });
    }

    openSubscriptionDialog(subscriptionType: SubscriptionTypes): void {
        this._dialog.open(DialogUpgradeSubscriptionComponent, {
            panelClass: ['full-page-scroll-50'],
            data: {
                organism: this._organism,
                subscription: this.subscription,
                fromPage: this.fromPage,
                subscriptionTypeToShow: subscriptionType
            }
        });
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    hasTrialAvailable(): boolean {
        const organismApplication = this.organismApplications.find(app => app.appId === this.enableAppId);
        const application = this._applicationsService.findAll().find(app => app.appId() === this.enableAppId);
        if (organismApplication) {
            return application.hasTrialAvailable() && organismApplication.state === OrganismApplicationStates.DISABLED && organismApplication.endDate == null;
        } else {
            return application.hasTrialAvailable();
        }
    }
}
