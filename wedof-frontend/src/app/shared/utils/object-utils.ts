import _ from 'lodash';

export function enumFromString<T>(type: T, str: string): T[keyof T] {
    const casted = _.snakeCase(str).toUpperCase() as keyof T;
    return type[casted];
}

export function downloadJSONData(filename: string, dataObjToWrite: any): void {
    const blob = new Blob([JSON.stringify(dataObjToWrite)], {type: 'text/json'});
    const link = document.createElement('a');

    link.download = filename;
    link.href = window.URL.createObjectURL(blob);
    link.dataset.downloadurl = ['text/json', link.download, link.href].join(':');

    const evt = new MouseEvent('click', {
        view: window,
        bubbles: true,
        cancelable: true,
    });

    link.dispatchEvent(evt);
    link.remove();
}

export function downloadCSVData(filename: string, dataObjToWrite: any): void {
    const pom = document.createElement('a');
    const blob = new Blob([dataObjToWrite], {type: 'text/csv;charset=utf-8;'});
    pom.href = URL.createObjectURL(blob);
    pom.setAttribute('download', 'wedof-rapport-import-dossiers-certification.csv');
    pom.click();
    pom.remove();
}
