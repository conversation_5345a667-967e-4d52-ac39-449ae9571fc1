import {RegistrationFolder, RegistrationFolderStates} from '../api/models/registration-folder';
import {ThemePalette} from '@angular/material/core';

export function isLight(color: string): boolean {
    const hex = color.replace('#', '');
    const red = parseInt(hex.substring(0, 2), 16);
    const green = parseInt(hex.substring(2, 2 + 2), 16);
    const blue = parseInt(hex.substring(4, 4 + 2), 16);
    const brightness = ((red * 299) + (green * 587) + (blue * 114)) / 1000;
    return brightness > 170;
}

export function generateColor(): string {
    const red = Math.floor((Math.random() * 127) + 127);
    const green = Math.floor((Math.random() * 127) + 127);
    const blue = Math.floor((Math.random() * 127) + 127);
    // tslint:disable-next-line:no-bitwise
    const rgb = (red << 16) + (green << 8) + blue;
    return `#${rgb.toString(16)}`;
}
export function expectedCompletionRateColor(registrationFolder: RegistrationFolder): ThemePalette | null {
    if ([RegistrationFolderStates.ACCEPTED, RegistrationFolderStates.IN_TRAINING].includes(registrationFolder.state)) {
        return (registrationFolder.expectedCompletionRate >= 0
            && registrationFolder.expectedCompletionRate > registrationFolder.completionRate) ? 'warn' : (registrationFolder.expectedCompletionRate ? 'primary' : 'accent');
    } else {
        return null;
    }
}
