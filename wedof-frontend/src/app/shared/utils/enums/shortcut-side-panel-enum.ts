export enum ShortcutPartnerSidePanelEnum {
    ORGANISM = 'organisme',
    PARTNERSHIP = 'gestionPartenariats',
    SYNCHRONIZE = 'donnees',
    AUDIT = 'audit',
    STATISTICS = 'statistiques',
    ACTIVITIES = 'activites',
    TRAININGS = 'formations',
    INVOICES = 'factures',
    TRAININGSPROMO = 'formationsPromo',
    MESSAGESECERTIFICATION = 'messages', // certif side
    AUDITPROMO = 'manageAuditPromo',
    METADATAS = 'metadonnees' // certif side
}

export enum ShortcutFolderSidePanelEnum {
    ATTENDEE = 'apprenant',
    CANDIDATE = 'candidat',
    CERTIFICATIONFOLDER = 'dossierCertification',
    REGISTRATIONFOLDER = 'dossierFormation',
    ACTIVITIES = 'activites',
    MESSAGESFORMATION = 'messages', // training side
    DELIVERIESFORMATION = 'webhooks', // training side
    MESSAGESECERTIFICATION = 'messages', // certif side
    DELIVERIESCERTIFICATION = 'webhooks', // certif side
    SURVEYSCERTIFICATION = 'enquete',  // certif side
    METADATAS = 'metadonnees',
    WORKFLOWRUNS = 'workflowRuns'
}

export enum ShortcutCatalogCertificationSidePanelEnum {
    PARTNER = 'partenariat',
    CERTIFIERACCESS = 'donnees',
    CERTIFICATION = 'certification',
    STATISTICS = 'statistiques',
    CERTIFICATIONFOLDERFILES = 'gestionDossiers',
    SKILLS = 'competences',
    MANAGEPARTNERSHIP = 'gestionPartenariats',
    AUDIT = 'audit',
    MANAGEPARTNERSHIPPROMO = 'gestionPartenariatsPromo',
}

export enum ShortcutCertificationSidePanelEnum {
    CERTIFICATION = 'certification',
    STATISTICS = 'statistiques',
    PARTNERSHIP = 'gestionPartenariats',
    PARTNERSHIPROMO = 'gestionPartenariatsPromo',
    CERTIFICATIONFOLDERS = 'gestionDossiers',
    SKILLS = 'competences',
    XML = 'accrochage',
    CERTIFICATIONSURVEY = 'enquete',
    AUDITTEMPLATE = 'modelesAudit',
    AUDITTEMPLATEPROMO = 'modelesAuditPromo',
    CERTIFICATETEMPLATE = 'generationParchemin',
    CERTIFICATETEMPLATEPROMO = 'generationParcheminPromo',
    MESSAGESETEMPLATESCERTIFICATION = 'messages'
}

export enum ShortcutProposalSidePanelEnum {
    PROPOSITION = 'proposition',
    ACTIVITIES = 'activites',
    MESSAGESETEMPLATESCERTIFICATION = 'messages',
    METADATASPROPOSAL = 'metadonnees'
}
