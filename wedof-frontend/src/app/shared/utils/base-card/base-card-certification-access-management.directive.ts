import {Directive, ElementRef, EventEmitter, OnDestroy, Output} from '@angular/core';
import {ShortcutLoadingSidePanel} from '../interface/shortcut-interfaces';
import {CardIntersection} from '../interface/cards-intersection-interfaces';
import {setUpIntersectionObserver} from '../shortcut-side-panel-utils';
import {
    AbstractCertificationAccessManagement
} from '../../certification/abstract-certification-access-management/abstract-certification-access-management';
import {MatDialog} from '@angular/material/dialog';
import {CertifierAccessService} from '../../api/services/certifier-access.service';
import {RequiredCallSuper} from './base-card.directive';
import {BaseCardInterface} from './base-card.interface';

/**
 * Doesn't work when extends without Directive decorator
 */
@Directive()
export abstract class BaseCardCertificationAccessManagementDirective extends AbstractCertificationAccessManagement implements BaseCardInterface, OnDestroy {

    public cardLoading = false;
    private shortcutId: string;
    @Output() shortcutSidePanelLoaded: EventEmitter<ShortcutLoadingSidePanel> = new EventEmitter<ShortcutLoadingSidePanel>();
    @Output() elementIntersection: EventEmitter<CardIntersection> = new EventEmitter<CardIntersection>();
    protected _intersectionObserver: IntersectionObserver;

    protected constructor(dialog: MatDialog,
                          certifierAccessService: CertifierAccessService,
                          shortcutId: string,
                          private elementRef: ElementRef) {
        super(dialog, certifierAccessService);
        this.updateShortcutId(shortcutId);
    }

    updateShortcutId(shortcutId: string): void {
        this.shortcutId = shortcutId;
        this.elementRef.nativeElement.id = shortcutId;
    }

    panelLoading(): void {
        this.cardLoading = true;
        this.shortcutSidePanelLoaded.emit({name: this.shortcutId, loaded: false});
        if (!this._intersectionObserver) {
            this._intersectionObserver = setUpIntersectionObserver(this.shortcutId, this.elementIntersection);
            this._intersectionObserver.observe(this.elementRef.nativeElement as HTMLElement);
        }
    }

    panelLoaded(): void {
        this.cardLoading = false;
        this.shortcutSidePanelLoaded.emit({name: this.shortcutId, loaded: true});
    }


    ngOnDestroy(): RequiredCallSuper {
        if (this._intersectionObserver) {
            this._intersectionObserver.disconnect();
        }
        return new RequiredCallSuper();
    }
}
