import {RequiredCallSuper} from './base-card.directive';
import {Directive, ElementRef, EventEmitter, OnDestroy, Output} from '@angular/core';
import {ShortcutLoadingSidePanel} from '../interface/shortcut-interfaces';
import {CardIntersection} from '../interface/cards-intersection-interfaces';
import {setUpIntersectionObserver} from '../shortcut-side-panel-utils';
import {CanDeactivateComponent} from '../can-deactivate/can-deactivate.component';
import {BaseCardInterface} from './base-card.interface';

/**
 * Doesn't work when extends without Directive decorator
 */
@Directive()
export abstract class BaseCardCanDeactivateDirective extends CanDeactivateComponent implements BaseCardInterface, OnDestroy {

    public cardLoading = false;
    private shortcutId: string;
    @Output() shortcutSidePanelLoaded: EventEmitter<ShortcutLoadingSidePanel> = new EventEmitter<ShortcutLoadingSidePanel>();
    @Output() elementIntersection: EventEmitter<CardIntersection> = new EventEmitter<CardIntersection>();
    protected _intersectionObserver: IntersectionObserver;

    protected constructor(shortcutId: string, private elementRef: ElementRef) {
        super();
        this.updateShortcutId(shortcutId);
    }

    updateShortcutId(shortcutId: string): void {
        this.shortcutId = shortcutId;
        this.elementRef.nativeElement.id = shortcutId;
    }

    panelLoading(): void {
        this.cardLoading = true;
        this.shortcutSidePanelLoaded.emit({name: this.shortcutId, loaded: false});
        if (!this._intersectionObserver) {
            this._intersectionObserver = setUpIntersectionObserver(this.shortcutId, this.elementIntersection);
            this._intersectionObserver.observe(this.elementRef.nativeElement as HTMLElement);
        }
    }

    panelLoaded(): void {
        this.cardLoading = false;
        this.shortcutSidePanelLoaded.emit({name: this.shortcutId, loaded: true});
    }


    ngOnDestroy(): RequiredCallSuper {
        if (this._intersectionObserver) {
            this._intersectionObserver.disconnect();
        }
        return new RequiredCallSuper();
    }
}
