import {Moment} from 'moment';

export function formatMomentToIsoDate(dateToConvert: Moment): string {
    return dateToConvert.toISOString().split('.')[0] + 'Z';
}

export function formatMomentToIsoDay(dateToConvert: Moment): string {
    // Use current timezone instead of switching to ISO / UTC otherwise we would go a day back
    return dateToConvert.format('YYYY-MM-DD');
}

export function daysAgo(eventTime: Date): number {
    const today = new Date();
    const oneDay = 1000 * 60 * 60 * 24;
    const diffInTime = today.getTime() - new Date(eventTime).getTime();
    return Math.round(diffInTime / oneDay);
}

