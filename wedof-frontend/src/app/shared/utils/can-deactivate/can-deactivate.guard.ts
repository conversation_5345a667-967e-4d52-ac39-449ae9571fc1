import {Injectable} from '@angular/core';
import {CanDeactivate} from '@angular/router';
import {CanDeactivateComponent} from './can-deactivate.component';
import {TranslateService} from '@ngx-translate/core';

@Injectable({
    providedIn: 'root'
})
export class CanDeactivateGuard implements CanDeactivate<CanDeactivateComponent> {

    constructor(private  _translateService: TranslateService) {
    }

    canDeactivate(component: CanDeactivateComponent): boolean {
        if (!component.canDeactivate()) {
            return confirm(this._translateService.instant('common.actions.leavePage'));
        }
        return true;
    }
}
