import {ShortcutLoadingSidePanel} from './interface/shortcut-interfaces';
import {ActivatedRoute} from '@angular/router';

export function goToShortCutWhenLoaded(event: ShortcutLoadingSidePanel, activatedRoute: ActivatedRoute, isDataLoaded: ShortcutLoadingSidePanel[]): void {
    const anchor: string = activatedRoute.snapshot.firstChild?.params?.anchor
        ? activatedRoute.snapshot.firstChild?.params?.anchor
        : activatedRoute.snapshot.firstChild?.params?.siret;
    if (isDataLoaded.some((data: ShortcutLoadingSidePanel) => data.name === event.name)) {
        isDataLoaded.map((data: ShortcutLoadingSidePanel) => data.name === event.name ? data.loaded = event.loaded : data);
    } else {
        isDataLoaded.push(event);
    }
    if (anchor && isDataLoaded.some((data: ShortcutLoadingSidePanel) => data.name === anchor && data.loaded === true)) {
        document.getElementById(anchor).scrollIntoView({
            behavior: 'auto',
            block: 'start',
            inline: 'nearest'
        });
    }
}

export function isSiret(siret: string): boolean {
    // Vérifier le format du SIRET (14 chiffres)
    if (!/^\d{14}$/.test(siret)) {
        return false;
    }
    // Calculer la clé de contrôle du SIRET
    let sum = 0;
    for (let i = 0; i < 14; i++) {
        let digit = parseInt(siret.charAt(i), 10);
        // Appliquer la pondération
        if (i % 2 === 0) {
            digit *= 2;
            if (digit > 9) {
                digit -= 9;
            }
        }
        sum += digit;
    }
    // Vérifier que la somme est un multiple de 10
    return sum % 10 === 0;
}

export function getAnchor(path: string): string {
    const segments: string[] = path.split('/');
    const notEmptySegments: string[] = segments.filter((segment: string): boolean => segment !== '');
    return notEmptySegments[notEmptySegments.length - 1].split('?')[0];
}

export function setUpIntersectionObserver(name, emiter): IntersectionObserver {
    return new IntersectionObserver((intersectionEntries: IntersectionObserverEntry[]) => {
        const intersectionFirstEntries: IntersectionObserverEntry = intersectionEntries[0];
        if (intersectionFirstEntries.isIntersecting === true) {
            emiter.emit({
                name: name,
                visible: true,
                ratio: intersectionFirstEntries.intersectionRatio
            });
        } else {
            emiter.emit({name: name, visible: false, ratio: 0});
        }
    }, {
        threshold: [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1]
    });
}
