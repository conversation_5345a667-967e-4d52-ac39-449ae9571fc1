import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {EllipsisTextComponent} from './ellipsis-text/ellipsis-text.component';
import {MatTooltipModule} from '@angular/material/tooltip';
import {ClipboardModule} from 'ngx-clipboard';
import {MaterialModule} from '../material/material.module';
import {TranslateModule} from '@ngx-translate/core';


@NgModule({
    declarations: [EllipsisTextComponent],
    exports: [
        EllipsisTextComponent
    ],
    imports: [
        CommonModule,
        MaterialModule,
        MatTooltipModule,
        ClipboardModule,
        TranslateModule
]
})
export class StringModule { }
