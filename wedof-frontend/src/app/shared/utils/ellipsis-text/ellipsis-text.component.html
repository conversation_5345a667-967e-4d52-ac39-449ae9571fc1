<div [matTooltip]="getTooltip(text)">
    <ng-container *ngIf="showMinText">
        {{text.length <= maxLength ? text : text.slice(0, maxLength) }}
    </ng-container>

    <ng-container
        *ngIf="allowShowMore; else showDots">
        <a class="italic" (click)="viewMore[text] = true;"
           *ngIf="!viewMore[text]">Voir plus </a>
        <span *ngIf="viewMore[text]">
          <span *ngIf="showMinText"> {{ text.slice(maxLength, text.length) }}</span>
                <span *ngIf="!showMinText" [innerHTML]="text"></span>
            <a class="italic ml-1" (click)="viewMore[text] = false;"
               *ngIf="viewMore[text]">Voir moins</a>
        </span>
    </ng-container>
    <ng-template #showDots>
        ...
    </ng-template>

    <button *ngIf="copy"
            class="ml-1"
            type="button"
            ngxClipboard
            [cbContent]="text"
            (cbOnSuccess)="copyValue(text)"
            [attr.aria-label]="'Copier'">
        <mat-icon class="icon-copy" svgIcon="file_copy"></mat-icon>
    </button>
</div>

<ng-template #copySuccess>
    {{  'common.actions.copySuccess' | translate }}
</ng-template>
