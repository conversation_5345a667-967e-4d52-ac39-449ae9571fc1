import {Component, Input, TemplateRef, ViewChild} from '@angular/core';
import {MatSnackBar} from '@angular/material/snack-bar';
import {TranslateService} from '@ngx-translate/core';

@Component({
    selector: 'app-ellipsis-text',
    templateUrl: './ellipsis-text.component.html',
    styleUrls: ['./ellipsis-text.component.scss']
})
export class EllipsisTextComponent {

    viewMore: {} = {};

    @Input() text: string;
    @Input() maxLength: number;
    @Input() copy = false;
    @Input() allowShowMore = false;
    @Input() showToolTipValue = true;
    @Input() showMinText = true;

    @ViewChild('copySuccess') copySuccessTemplate: TemplateRef<any>;

    constructor(private _snackBar: MatSnackBar,
                private _translateService: TranslateService
    ) {
    }

    copyValue(value: string): any {
        this._snackBar.openFromTemplate(this.copySuccessTemplate, {
            verticalPosition: 'top',
            horizontalPosition: 'center',
            duration: 5000,
        });
        return value;
    }

    getTooltip(text: string): string {
        if (this.showToolTipValue) {
            return text.length > this.maxLength ? text : undefined;
        } else {
            return '';
        }
    }
}
