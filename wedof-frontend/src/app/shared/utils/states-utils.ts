import {
    CertificationFolderAccrochageStates,
    CertificationFolderCdcStates,
    CertificationFolderStates
} from '../api/models/certification-folder';
import {
    RegistrationFolderBillingStates,
    RegistrationFolderControlStates,
    RegistrationFolderStates
} from '../api/models/registration-folder';
import {PeriodTypes, PeriodTypesForCertificationFolders} from '../api/models/period-types';
import {DataProviderConfig} from '../api/models/connection';
import {ProposalStates} from '../api/models/proposal';
import {CertificationPartnerStates} from '../api/models/certification-partner';
import {MessageState} from '../api/models/message';

export function getCertificationFolderAllStates(): any[] {
    return Object.values(CertificationFolderStates).map(value => ({
        label: `private.common.certificationFolder.state.${value}`,
        value: value
    }));
}

export function getCertificationFolderAccrochageStates(): any[] {
    return Object.values(CertificationFolderAccrochageStates).map(value => ({
        label: `private.common.certificationFolder.state.${value}`,
        value: value
    }));
}

export function getProposalAllStates(): any[] {
    return Object.values(ProposalStates).map(value => ({
        label: `private.training-organism.proposals.table.states.${value}`,
        value: value
    }));
}

export function getRegistrationFolderAllStates(): any[] {
    return Object.values(RegistrationFolderStates).map(value => ({
        label: `private.training-organism.folders.common.state.${value}`,
        value: value
    }));
}

export function getRegistrationFolderAllBillingStates(): any[] {
    return Object.values(RegistrationFolderBillingStates).map(value => ({
        label: `private.training-organism.folders.common.billingState.${value}`,
        value: value
    }));
}

export function getCertificationPartnerAllStates(): any[] {
    return Object.values(CertificationPartnerStates).map(value => ({
        label: `private.certification.partners.table.partnership.state.${value}`,
        value: value
    }));
}

export function getCdcStates(): any[] {
    return Object.values(CertificationFolderCdcStates).map(value => ({
        label: `private.common.certificationFolder.cdcState.${value}`,
        value: value
    }));
}

export function getRegistrationFolderAllDataProviders(): any[] {
    return Object.entries(DataProviderConfig)
        .filter(([dataProviderType, dataProviderConfigEntry]) => dataProviderConfigEntry.manageRegistrationFolders !== false)
        .map(([dataProviderType]) => ({
            label: `private.training-organism.folders.common.type.${dataProviderType}`,
            value: dataProviderType
        }));
}

export function getRegistrationFolderAllControlStates(): any[] {
    return Object.values(RegistrationFolderControlStates).map(value => ({
        label: `private.common.registrationFolder.controlState.state.${value}`,
        value: value
    }));
}

export function getPeriodTypes(forCertificationFolder: boolean = false): any[] {
    let values = Object.values(PeriodTypes).map(value => ({
        label: `common.actions.search.period.type.${value}`,
        value: value
    }));
    if (forCertificationFolder) {
        values = values.concat(Object.values(PeriodTypesForCertificationFolders).map(value => ({
            label: `common.actions.search.period.typeForCertificationFolders.${value}`,
            value: value as unknown as PeriodTypes
        })));
    }
    return values;
}

export function getEventFileStatesChoices(folderFile: string): any[] {
    return [
        {
            value: folderFile + '.*',
            label: `private.common.files.state.allEvents`
        },
        {
            value: folderFile + '.added',
            label: `private.common.files.state.fileStateLong.added`
        },
        {
            value: folderFile + '.updated',
            label: `private.common.files.state.fileStateLong.updated`
        },
        {
            value: folderFile + '.valid',
            label: `private.common.files.state.fileStateLong.valid`
        },
        {
            value: folderFile + '.toReview',
            label: `private.common.files.state.fileStateLong.toReview`
        },
        {
            value: folderFile + '.refused',
            label: `private.common.files.state.fileStateLong.refused`
        },
        {
            value: folderFile + '.deleted',
            label: `private.common.files.state.fileStateLong.deleted`
        },
    ];
}

export function getMessageStates(): any[] {
    const messageStates = [];
    Object.values(MessageState)
        .filter((messageState) => ![MessageState.NOT_SENT_UNAUTHORIZED, MessageState.NOT_SENT_ENFORCED_CONDITIONS, MessageState.NOT_SENT_MISSING_DATA].includes(messageState))
        .map(value => {
            messageStates.push({
                label: `private.common.message-templates.messages.state.${value}`,
                value: value
            });
        });
    messageStates.push({
        label: 'Non envoyé, mauvais abonnement/application inactive',
        value: MessageState.NOT_SENT_UNAUTHORIZED
    }, {
        label: 'Non envoyé, re-vérification des conditions',
        value: MessageState.NOT_SENT_ENFORCED_CONDITIONS
    }, {
        label: 'Non envoyé, informations manquantes',
        value: MessageState.NOT_SENT_MISSING_DATA
    });
    return messageStates;
}

export function getSurveys(): any[] {
    return [
        {
            label: `private.common.certificationFolderSurvey.created`,
            value: 'initialExperienceStartDate'
        },
        {
            label: `private.common.certificationFolderSurvey.sixMonthExperienceAvailable`,
            value: 'sixMonthExperienceStartDate'
        },
        {
            label: `private.common.certificationFolderSurvey.longTermExperienceAvailable`,
            value: 'longTermExperienceStartDate'
        },
        {
            label: `private.common.certificationFolderSurvey.initialExperienceAnswered`,
            value: 'initialExperienceAnsweredDate'
        },
        {
            label: `private.common.certificationFolderSurvey.sixMonthExperienceAnswered`,
            value: 'sixMonthExperienceAnsweredDate'
        },
        {
            label: `private.common.certificationFolderSurvey.longTermExperienceAnswered`,
            value: 'longTermExperienceAnsweredDate'
        }
    ];
}


