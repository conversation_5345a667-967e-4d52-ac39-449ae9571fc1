<mat-card class="flex h-full flex-auto flex-col mt-3 p-5 pb-0 pt-3 border shadow-none sm"
          [ngClass]="{'card-loading':cardLoading}">
    <div class="flex items-center mb-2">
        <mat-icon
            class="mr-3 card-loading-show text-4xl"
            color="primary">view_timeline
        </mat-icon>
        <div class="flex items-center">
            <span
                class="text-xl font-semibold card-loading-show">{{ 'private.attendee.survey.title' | translate }}</span>
        </div>
    </div>
    <p class="mb-4 card-loading-show" *ngIf="isCandidate">{{ 'private.attendee.survey.subtitle' | translate }}</p>
    <div class="mb-4" *ngIf="!cardLoading && certificationFolderSurvey">
        <ng-template [ngTemplateOutlet]="experienceTemplate"
                     [ngTemplateOutletContext]="{
                            surveyExperienceName: certificationFolderSurveyExperience.INITIAL_EXPERIENCE,
                            experience: certificationFolderSurvey.initialExperience,
                            date: certificationFolderSurvey.initialExperienceAnsweredDate,
                            activeButton: true
                         }">
        </ng-template>

        <ng-container *ngIf="certificationFolder.state === certificationFolderStates.SUCCESS">
            <hr class="w-60 mr-auto ml-auto border-t mt-4 mb-4">
            <ng-template [ngTemplateOutlet]="experienceTemplate"
                         [ngTemplateOutletContext]="{
                                surveyExperienceName: certificationFolderSurveyExperience.SIX_MONTH_EXPERIENCE,
                                experience: certificationFolderSurvey.sixMonthExperience,
                                date: certificationFolderSurvey.sixMonthExperienceAnsweredDate,
                                activeButton: canAnswerSixMonthExperience()
                         }">
            </ng-template>

            <hr class="w-60 mr-auto ml-auto border-t mt-4 mb-4">
            <ng-template [ngTemplateOutlet]="experienceTemplate"
                         [ngTemplateOutletContext]="{
                                surveyExperienceName: certificationFolderSurveyExperience.LONG_TERM_EXPERIENCE,
                                experience: certificationFolderSurvey.longTermExperience,
                                date: certificationFolderSurvey.longTermExperienceAnsweredDate,
                                activeButton: canAnswerLongTermExperience()
                         }">
            </ng-template>
        </ng-container>
    </div>

    <ng-template #experienceTemplate
                 let-surveyExperienceName="surveyExperienceName"
                 let-experience="experience"
                 let-date="date"
                 let-activeButton="activeButton">

        <ng-container *ngIf="isCandidate; else showOther">
            <p class="font-bold mb-2">{{ 'private.attendee.survey.' + surveyExperienceName + '.title' | translate }}</p>

            <div class="flex flex-row items-center mb-2">
                <button type="button" mat-flat-button color="primary" class="mr-3 w-100"
                        *ngIf="!experience"
                        [disabled]="!activeButton"
                        (click)="listExperience(surveyExperienceName)">
                    {{ 'common.actions.answer' | translate }}
                </button>
                <p [innerHTML]="'private.attendee.survey.' + surveyExperienceName + '.description' | translate: {certificationName: certificationFolder._links.certification.name}"></p>
            </div>

            <ng-template [ngTemplateOutlet]="showAnswer"
                         [ngTemplateOutletContext]="{
                                surveyExperienceName: surveyExperienceName,
                                experience: experience,
                                date: date
                         }">
            </ng-template>
        </ng-container>

        <ng-template #showOther>
            <div class="flex items-center mb-2">
                <mat-icon [ngClass]="{'text-green':experience, 'text-secondary':!experience}" class="mr-2"
                          svgIcon="{{ experience ? 'check_circle' : 'help' }}"></mat-icon>
                <p class="font-bold">{{ 'private.attendee.survey.' + surveyExperienceName + '.title' | translate }}</p>
            </div>
            <app-attendee-experience *ngIf="experience"
                                     class="mb-2"
                                     [attendeeExperience]="experience"></app-attendee-experience>
            <ng-template [ngTemplateOutlet]="showAnswer"
                         [ngTemplateOutletContext]="{
                                surveyExperienceName: surveyExperienceName,
                                experience: experience,
                                date: date
                         }">
            </ng-template>
        </ng-template>
    </ng-template>

</mat-card>


<ng-template #showAnswer
             let-date="date"
             let-surveyExperienceName="surveyExperienceName"
             let-experience="experience">
    <ng-container class="mb-4" *ngIf="experience; else noData">
        <p class="text-sm">{{ 'private.attendee.survey.date' | translate : {date: date | date : "mediumDate"} }}
        </p>
    </ng-container>
    <ng-template class="mb-4" #noData>
        <ng-container *ngIf="surveyExperienceName !== certificationFolderSurveyExperience.INITIAL_EXPERIENCE">
            <p class="text-sm">
                {{
                    'private.attendee.survey.dueDate' | translate: {
                        date: getDueDate(surveyExperienceName) | date: "mediumDate"
                    }
                }}
            </p>
        </ng-container>
    </ng-template>
</ng-template>
