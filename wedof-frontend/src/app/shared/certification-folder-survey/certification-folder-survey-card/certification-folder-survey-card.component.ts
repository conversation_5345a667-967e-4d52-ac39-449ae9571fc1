import {Component, ElementRef, EventEmitter, Input, OnChanges, Output, SimpleChanges} from '@angular/core';
import {
    CertificationFolderSurvey,
    CertificationFolderSurveyExperience,
    CertificationFolderSurveyState
} from '../../api/models/certification-folder-survey';
import {CertificationFolder, CertificationFolderStates} from '../../api/models/certification-folder';
import {CertificationFolderSurveyService} from '../../api/services/certification-folder-survey.service';
import {MatDialog} from '@angular/material/dialog';
import {AttendeeExperienceService} from '../../api/services/attendee-experience.service';
import {AttendeeExperience} from '../../api/models/attendee-experience';
import {CertificationFolderSurveyFormComponent} from '../certification-folder-survey-form/certification-folder-survey-form.component';
import {BaseCardComponentDirective} from '../../utils/base-card/base-card.directive';

@Component({
    selector: 'app-certification-folder-survey-card',
    templateUrl: './certification-folder-survey-card.component.html',
    styleUrls: ['./certification-folder-survey-card.component.scss']
})
export class CertificationFolderSurveyCardComponent extends BaseCardComponentDirective implements OnChanges {

    static COMPONENT_ID = 'enquete';

    cardLoading = true;
    certificationFolderSurvey: CertificationFolderSurvey;
    attendeeExperiences: AttendeeExperience[] = [];
    certificationFolderSurveyExperience = CertificationFolderSurveyExperience;
    certificationFolderStates = CertificationFolderStates;

    @Input() isCandidate: boolean;
    @Input() candidateId: number;
    @Input() certificationFolder: CertificationFolder;
    @Input() showSurvey: boolean;
    @Output() shouldRefreshCertificateAccess: EventEmitter<boolean> = new EventEmitter<boolean>();

    constructor(private _certificationFolderSurveyService: CertificationFolderSurveyService,
                private _attendeeExperienceService: AttendeeExperienceService,
                private _dialog: MatDialog,
                private _el: ElementRef) {
        super(CertificationFolderSurveyCardComponent.COMPONENT_ID, _el);
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes.certificationFolder) {
            this.panelLoading();
        }
        if (changes.certificationFolder || (changes.showSurvey && changes.showSurvey.currentValue === true)) {
            this.initForm();
        }
    }

    initForm(): void {
        this.certificationFolderSurvey = null;
        this.attendeeExperiences = [];
        this.cardLoading = true;
        this._certificationFolderSurveyService.getByUrl(this.certificationFolder._links.survey.href).subscribe((certificationFolderSurvey) => {
            this.certificationFolderSurvey = certificationFolderSurvey;
            const params = this.isCandidate ? {} : {certificationFolderExternalId: this.certificationFolder.externalId};
            this._attendeeExperienceService.list(this.candidateId, params).subscribe((attendeeExperiences) => {
                this.attendeeExperiences = attendeeExperiences.payload;
                if (this.showSurvey) {
                    this.listExperience(this.getFirstUnanswered());
                }
                this.cardLoading = false;
                this.panelLoaded();
            });
        });
    }

    listExperience(surveyExperienceName: CertificationFolderSurveyExperience): void {
        const dialogRef = this._dialog.open(CertificationFolderSurveyFormComponent, {
            panelClass: ['full-page-scroll-50'],
            data: {
                attendeeExperiences: this.attendeeExperiences,
                surveyExperienceName: surveyExperienceName,
                candidateId: this.candidateId,
                certificationFolder: this.certificationFolder,
                certificationFolderSurvey: this.certificationFolderSurvey,
                selectAttendeeExperience: (attendeeExperienceId: number) => {
                    const body = {
                        experience: {
                            name: surveyExperienceName,
                            attendeeExperienceId: attendeeExperienceId
                        }
                    };
                    return this._certificationFolderSurveyService.update(this.certificationFolder.externalId, body);
                }
            }
        });
        dialogRef.afterClosed().subscribe(result => {
            if (result?.data) {
                this.certificationFolderSurvey = result.data;
                this.shouldRefreshCertificateAccess.emit(true);
            }
        });
    }

    canAnswerSixMonthExperience(): boolean {
        if (!this.certificationFolderSurvey.initialExperience) {
            return false;
        }
        return new Date() >= new Date(this.certificationFolderSurvey.sixMonthExperienceStartDate);
    }

    canAnswerLongTermExperience(): boolean {
        if (!this.certificationFolderSurvey.sixMonthExperience) {
            return false;
        }
        return new Date() >= new Date(this.certificationFolderSurvey.longTermExperienceStartDate);
    }

    getDueDate(surveyExperienceName: string): Date {
        return surveyExperienceName === CertificationFolderSurveyExperience.SIX_MONTH_EXPERIENCE ?
            this.certificationFolderSurvey.sixMonthExperienceStartDate : this.certificationFolderSurvey.longTermExperienceStartDate;
    }

    getFirstUnanswered(): CertificationFolderSurveyExperience {
        let surveyExperience: CertificationFolderSurveyExperience;
        switch (this.certificationFolderSurvey.state) {
            case CertificationFolderSurveyState.CREATED:
                surveyExperience = CertificationFolderSurveyExperience.INITIAL_EXPERIENCE;
                break;
            case CertificationFolderSurveyState.BEFORE_CERTIFICATION_SUCCESS:
                surveyExperience = CertificationFolderSurveyExperience.SIX_MONTH_EXPERIENCE;
                break;
            case CertificationFolderSurveyState.AFTER_SIX_MONTHS_CERTIFICATION_SUCCESS:
            case CertificationFolderSurveyState.FINISHED:
                surveyExperience = CertificationFolderSurveyExperience.LONG_TERM_EXPERIENCE;
                break;
            default: // on ne devrait jamais arriver ici
                surveyExperience = null;
        }
        return surveyExperience;
    }
}
