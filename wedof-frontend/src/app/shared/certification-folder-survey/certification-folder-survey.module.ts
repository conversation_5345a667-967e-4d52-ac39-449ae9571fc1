import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {MaterialModule} from '../material/material.module';
import {TranslateModule} from '@ngx-translate/core';
import {CertificationFolderSurveyCardComponent} from './certification-folder-survey-card/certification-folder-survey-card.component';
import {MatCardModule} from '@angular/material/card';
import {MatTooltipModule} from '@angular/material/tooltip';
import {CertificationFolderSurveyFormComponent} from './certification-folder-survey-form/certification-folder-survey-form.component';
import {AttendeeExperienceModule} from '../attendee-experience/attendee-experience.module';


@NgModule({
    declarations: [CertificationFolderSurveyCardComponent, CertificationFolderSurveyFormComponent],
    exports: [CertificationFolderSurveyCardComponent],
    imports: [
        CommonModule,
        MaterialModule,
        TranslateModule,
        MatCardModule,
        MatTooltipModule,
        AttendeeExperienceModule
    ]
})
export class CertificationFolderSurveyModule {
}
