import {Component, Inject} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material/dialog';
import {AttendeeExperience} from '../../api/models/attendee-experience';
import {Observable} from 'rxjs';
import {finalize} from 'rxjs/operators';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../errors/errors.types';
import {
    CertificationFolderSurvey,
    CertificationFolderSurveyExperience
} from '../../api/models/certification-folder-survey';
import {CertificationFolder} from '../../api/models/certification-folder';

@Component({
    selector: 'app-certification-folder-survey-form',
    templateUrl: './certification-folder-survey-form.component.html',
    styleUrls: ['./certification-folder-survey-form.component.scss']
})
export class CertificationFolderSurveyFormComponent {

    loading = false;
    errorMessages: string[] = [];
    showNewAttendeeExperienceForm = false;
    selectedAttendeeExperience: AttendeeExperience;

    constructor(
        public _dialogRef: MatDialogRef<CertificationFolderSurveyFormComponent>,
        @Inject(MAT_DIALOG_DATA) public dialogData: {
            attendeeExperiences: AttendeeExperience[],
            surveyExperienceName: CertificationFolderSurveyExperience,
            candidateId: number,
            certificationFolder: CertificationFolder,
            certificationFolderSurvey: CertificationFolderSurvey,
            selectAttendeeExperience(id: number): Observable<AttendeeExperience>
        },
    ) {
    }

    close(): void {
        this._dialogRef.close();
    }

    selectCard(attendeeExperience: AttendeeExperience): void {
        this.selectedAttendeeExperience = attendeeExperience;
    }

    chooseAttendeeExperience(attendeeExperience: AttendeeExperience): void {
        this.loading = true;
        this.errorMessages = [];
        this.dialogData.selectAttendeeExperience(attendeeExperience.id).pipe(
            finalize(() => {
                this.loading = false;
            })
        ).subscribe({
            next: (result) => {
                this.dialogData.attendeeExperiences.push(attendeeExperience);
                this._dialogRef.close({data: result});
            },
            error: (httpErrorResponse: HttpErrorResponse) => {
                this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
            }
        });
    }

    isDisabled(attendeeExperience: AttendeeExperience): boolean {
        const issueDate = this.dialogData.certificationFolder.issueDate;
        return attendeeExperience.startDate && issueDate ? attendeeExperience.startDate <= issueDate : false;
    }

    addAttendeeExperience(): void {
        this.showNewAttendeeExperienceForm = true;
    }

    hideAttendeeExperienceForm(): void {
        this.selectedAttendeeExperience = null;
        this.showNewAttendeeExperienceForm = false;
    }
}
