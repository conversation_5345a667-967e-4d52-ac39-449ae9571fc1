<app-dialog-layout [title]="'private.attendee.survey.form.title' | translate"
                   [showCancel]="!showNewAttendeeExperienceForm"
                   [disabled]="loading"
                   [actions]="actions"
                   (dialogClose)="close()">

    <ng-container *ngIf="!showNewAttendeeExperienceForm; else showCreateForm">

        <ng-container class="mt-4" *ngIf="dialogData.attendeeExperiences?.length">
            <div class="mb-4 flex justify-center" *ngFor="let attendeeExperience of dialogData.attendeeExperiences">
                <button [disabled]="isDisabled(attendeeExperience)" (click)="selectCard(attendeeExperience)">
                    <mat-card class="flex flex-col justify-center rounded-md w-120 h-26"
                              [ngClass]="selectedAttendeeExperience === attendeeExperience ? 'bg-indigo-600 text-white' :
                              isDisabled(attendeeExperience) ? '' : 'attendeeExperienceCard'">
                        <app-attendee-experience [attendeeExperience]="attendeeExperience"></app-attendee-experience>
                    </mat-card>
                </button>
            </div>
        </ng-container>

        <button class="attendeeExperienceCreate rounded-md w-120 h-14 m-auto"
                [disabled]="loading"
                type="button"
                (click)="addAttendeeExperience()">
            {{'private.attendee.survey.form.add' | translate}}
        </button>

        <div *ngIf="errorMessages?.length" class="flex mt-3 items-center">
            <treo-message class="flex-auto" appearance="outline" [showIcon]="false" type="error">
                <ul>
                    <li *ngFor="let errorMessage of errorMessages">
                        {{ errorMessage }}
                    </li>
                </ul>
            </treo-message>
        </div>
    </ng-container>

    <ng-template #showCreateForm>
        <app-attendee-experience-form [candidateId]="dialogData.candidateId"
                                      [surveyExperienceName]="dialogData.surveyExperienceName"
                                      [certificationFolderExternalId]="dialogData.certificationFolder.externalId"
                                      (hideAttendeeExperienceForm)="hideAttendeeExperienceForm()"
                                      (chooseAttendeeExperience)="chooseAttendeeExperience($event)">
        </app-attendee-experience-form>
    </ng-template>

    <ng-template #actions>
        <ng-container *ngIf="!showNewAttendeeExperienceForm">
            <button type="submit"
                    mat-flat-button
                    *ngIf="dialogData.attendeeExperiences?.length"
                    color="primary"
                    [disabled]="!selectedAttendeeExperience"
                    (click)="chooseAttendeeExperience(selectedAttendeeExperience)">
                <mat-progress-spinner *ngIf="loading; else submitLabel" [diameter]="24"
                                      mode="indeterminate"></mat-progress-spinner>
                <ng-template #submitLabel>
                    {{ 'common.actions.answer' | translate }}
                </ng-template>
            </button>
        </ng-container>
    </ng-template>

</app-dialog-layout>
