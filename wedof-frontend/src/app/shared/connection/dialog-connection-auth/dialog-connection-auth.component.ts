import {Component, Inject, <PERSON><PERSON><PERSON>roy, OnInit} from '@angular/core';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {MAT_DIALOG_DATA, MatDialog, MatDialogRef} from '@angular/material/dialog';
import {TranslateService} from '@ngx-translate/core';
import {TreoAnimations} from '../../../../@treo/animations';
import {Organism} from '../../api/models/organism';
import {ConnectionService} from '../../api/services/connection.service';
import {
    AuthenticationResult,
    Connection,
    ConnectionState,
    ConnectionType,
    Credentials,
    DataProviderConfig, DataProviderConfigEntry,
    DataProviders
} from '../../api/models/connection';
import {Router} from '@angular/router';
import {Select, Store} from '@ngxs/store';
import {ConnectionsState, UpdatedConnection} from '../../api/state/connections.state';
import {combineLatest, Observable, of, Subject} from 'rxjs';
import {catchError, map, mergeMap, takeUntil} from 'rxjs/operators';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../errors/errors.types';
import {Subscription} from '../../api/models/subscription';
import {AppFormFieldData} from '../../material/app-form-field/app-form-field.component';
import {MatSnackBar} from '@angular/material/snack-bar';
import {SubscriptionState} from '../../api/state/subscription.state';
import {OrganismState} from '../../api/state/organism.state';
import {UserState} from '../../api/state/user.state';
import {User} from '../../api/models/user';

@Component({
    selector: 'dialog-connection-auth',
    templateUrl: './dialog-connection-auth.component.html',
    styleUrls: ['./dialog-connection-auth.component.scss', '../../../auth/sign-up/sign-up-wizard.component.scss'],
    animations: [TreoAnimations]
})
export class DialogConnectionAuthComponent implements OnInit, OnDestroy {

    messages: Array<string>;
    errorMessages: Array<string>;
    loading = false;

    dataProviderName: string;
    authorizations: readonly string[];

    user: User;
    organism: Organism;
    subscription: Subscription;
    connection: Connection;
    formGroup: FormGroup;
    dataProviderConfigEntry: DataProviderConfigEntry;

    dataProviders = DataProviders;
    connectionsType = ConnectionType;
    connectionState = ConnectionState;

    @Select(ConnectionsState.connections) connections$: Observable<Connection[]>;
    @Select(SubscriptionState.subscription) subscription$: Observable<Subscription>;
    @Select(OrganismState.organism) organism$: Observable<Organism>;
    @Select(UserState.user) user$: Observable<User>;

    private _unsubscribeAll = new Subject<void>();
    appFormFieldsData: AppFormFieldData[];

    constructor(
        public dialogRef: MatDialogRef<DialogConnectionAuthComponent>,
        public connectionService: ConnectionService,
        public _translateService: TranslateService,
        private _formBuilder: FormBuilder,
        private _router: Router,
        private _dialog: MatDialog,
        private _store: Store,
        public _snackBar: MatSnackBar,
        @Inject(MAT_DIALOG_DATA) public dialogData: {
            dataProvider: DataProviders,
            organism: Organism,
            subscription: Subscription,
            limitedUsage: boolean
        }
    ) {
        this.formGroup = this._formBuilder.group({
            connectionForm: this._formBuilder.group({})
        });
    }


    ngOnInit(): void {
        this.errorMessages = [];
        this.dataProviderName = this._translateService.instant('auth.' + this.dialogData.dataProvider + '.name');
        combineLatest([
            this.connections$,
            this.subscription$,
            this.organism$,
            this.user$
        ]).pipe(takeUntil(this._unsubscribeAll)).subscribe(([connections, subscription, organism, user]) => {
            this.connection = connections.find(connection => connection.dataProvider === this.dialogData.dataProvider);
            this.dataProviderConfigEntry = DataProviderConfig[this.dialogData.dataProvider];
            this.authorizations = this.dataProviderConfigEntry.authorizations;
            this.subscription = subscription;
            this.organism = organism;
            this.user = user;
        });
        const usernameAppFormFieldType = this.dataProviderConfigEntry.usernameAppFormFieldType ?? 'email';
        let appFormFieldsData: AppFormFieldData[] = [
            {
                controlName: 'username',
                label: this._translateService.instant('auth.connection.form.email.label', {dataProvider: this.dataProviderName}),
                type: usernameAppFormFieldType,
                required: true,
                value: null,
                validators: usernameAppFormFieldType !== 'email' ? [] : [Validators.email],
                icon: usernameAppFormFieldType !== 'email' ? 'person' : 'mail',
                placeholder: this._translateService.instant('auth.connection.form.email.placeholder', {dataProvider: this.dataProviderName}),
                validatorsMessages: {
                    email: 'private.profile.organism.form.fields.email.error',
                },
            },
            {
                controlName: 'password',
                label: this._translateService.instant('auth.connection.form.password.label', {dataProvider: this.dataProviderName}),
                type: 'password',
                required: true,
                value: null,
                icon: 'vpn_key',
                placeholder: this._translateService.instant('auth.connection.form.password.placeholder', {dataProvider: this.dataProviderName}),
            }
        ];
        appFormFieldsData = this.getAppFormFieldsData(appFormFieldsData);
        this.appFormFieldsData = appFormFieldsData.filter(value => value != null);
    }

    saveConnection(): void {
        let beforeSave;
        this.errorMessages = [];
        const isValidForm = this.formGroup.valid; // .disable() => change formgroup to not valid
        if (!this.loading) {
            this.loading = true;
            this.formGroup.disable();
        }
        if (this.dataProviderConfigEntry.testBeforeSave) {
            const showForm = this.displayLoginForm();
            if (!showForm || (showForm && isValidForm)) {
                const formValues = showForm ? this.formGroup.getRawValue().connectionForm : {};
                beforeSave = this.connectionService.check(formValues, this.dialogData.dataProvider)
                    .pipe(
                        map(data => {
                            if (this.dataProviderConfigEntry.manageBeforeSaveResult) {
                                return this.dataProviderConfigEntry.manageBeforeSaveResult(this, data);
                            } else {
                                return data;
                            }
                        }),
                        catchError(data => {
                            try {
                                const result = JSON.parse(data.error.detail);
                                const message = this._translateService.instant('auth.' + this.dialogData.dataProvider + '.error.' + result.error, result);
                                this.errorMessages.push(!message.includes('auth.') ? message : this._translateService.instant('auth.' + this.dialogData.dataProvider + '.error.other'));
                            } catch (e) {
                                this.errorMessages
                                    .push(this._translateService.instant('auth.' + this.dialogData.dataProvider + '.error.other'));
                            }
                            return of(data);
                        }),
                        map(data => {
                            if (data.error) {
                                this.loading = false;
                                this.connection = null; // reset connection
                                this.formGroup.enable();
                                return false;
                            } else {
                                return data.result;
                            }
                        })
                    );
            }
        } else {
            beforeSave = of(true);
        }
        beforeSave.subscribe(result => {
            let doHabilitation;
            if (result) {
                if (this.dataProviderConfigEntry.type === ConnectionType.DELEGATION) {
                    const credentials: Credentials = (this.formGroup.get('connectionForm') as FormGroup).getRawValue();
                    this.connectionService.authenticate(credentials, this.dialogData.dataProvider).subscribe(
                        (access: AuthenticationResult) => {
                            this.formGroup.enable();
                            this.loading = false;
                            if (access.hasAccess) {
                                if (access.hasOrganismAccess) {
                                    this.close(access.hasOrganismAccess);
                                } else {
                                    this.errorMessages.push(this._translateService.instant('auth.connection.form.errors.no-organism-access', {
                                        dataProvider: this.dialogData.dataProvider.toUpperCase(),
                                        organismeName: this.dialogData.organism.name
                                    }));
                                }
                            } else {
                                if (access.errorCode === 'reset-password') {
                                    this.errorMessages.push(this._translateService.instant('auth.connection.form.errors.reset-password', {
                                        dataProviderLink: this.dataProviderConfigEntry.resetPasswordLink,
                                        dataProviderName: this.dataProviderConfigEntry.name
                                    }));
                                } else if (access.errorCode === 'credentials') {
                                    this.errorMessages.push(this._translateService.instant('auth.connection.form.errors.wrong-login-password', {
                                        dataProviderLink: this.dataProviderConfigEntry.passwordLink,
                                        dataProviderName: this.dataProviderConfigEntry.name
                                    }));
                                } else {
                                    this.errorMessages.push(this._translateService.instant('auth.connection.form.errors.other', {
                                        dataProviderName: this.dataProviderConfigEntry.name
                                    }));
                                }
                            }
                        },
                        () => {
                            this.loading = false;
                            this.formGroup.enable();
                            this.errorMessages.push(this._translateService.instant('auth.connection.form.errors.unknown-error'));
                        });
                } else {
                    if (this.dataProviderConfigEntry.confirmHabilitationDialog) {
                        const config = {
                            ...{
                                panelClass: 'full-page-scroll-40',
                                height: 'auto',
                                data: {
                                    organism: this.dialogData.organism,
                                    dataProvider: this.dialogData.dataProvider,
                                    dataProviderConfigEntry: this.dataProviderConfigEntry,
                                    formValues: this.displayLoginForm() ? this.formGroup.getRawValue().connectionForm : {}
                                }
                            },
                            ...result
                        };
                        doHabilitation = this._dialog.open(this.dataProviderConfigEntry.confirmHabilitationDialog, config).afterClosed();
                    } else {
                        doHabilitation = of(result);
                    }
                    doHabilitation.subscribe(res => {
                        if (res?.result) {
                            const showForm = this.displayLoginForm();
                            this.connectionService.habilitate(showForm ? (this.formGroup.get('connectionForm') as FormGroup).getRawValue() : []
                                , this.dialogData.dataProvider).subscribe({
                                next: (connection) => {
                                    this.loading = false;
                                    this._store.dispatch(new UpdatedConnection(connection)).pipe(
                                        mergeMap(() => this._store.selectOnce(ConnectionsState.connections))
                                    );
                                    this.close(true);
                                },
                                error: (httpErrorResponse: HttpErrorResponse) => {
                                    this.loading = false;
                                    this.formGroup.enable();
                                    this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
                                }
                            });
                        } else {
                            this.loading = false;
                            this.formGroup.enable();
                        }
                    });
                }
            } else {
                this.loading = false;
                this.formGroup.enable();
            }
        });
    }

    close(result?: boolean): void {
        if (this._router.url.includes('/profil/connexions/' + this.dialogData.dataProvider)) {
            this._router.navigate(['profil']);
        }
        this.dialogRef.close(result);
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    displayLimitedUsage(): boolean {
        return this.dialogData.limitedUsage || (
            this.dataProviderConfigEntry.displayLimitedUsage && this.dialogData.subscription ?
                this.dataProviderConfigEntry.displayLimitedUsage(this.dialogData.subscription) :
                false
        );
    }

    displayLoginForm(): boolean {
        if (this.dataProviderConfigEntry) {
            return this.dataProviderConfigEntry.displayLoginForm ? this.dataProviderConfigEntry.displayLoginForm(this) : true;
        } else {
            return false;
        }
    }

    getAppFormFieldsData(appFormFieldData: AppFormFieldData[]): AppFormFieldData[] {
        return this.dataProviderConfigEntry?.getAppFormFieldsData ? this.dataProviderConfigEntry.getAppFormFieldsData(appFormFieldData, this) : appFormFieldData;
    }
}
