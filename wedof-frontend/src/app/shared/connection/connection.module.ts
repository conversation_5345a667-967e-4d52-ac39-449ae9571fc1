import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {TranslateModule} from '@ngx-translate/core';
import {MaterialModule} from '../material/material.module';
import {ReactiveFormsModule} from '@angular/forms';
import {DialogConnectionAuthComponent} from './dialog-connection-auth/dialog-connection-auth.component';

@NgModule({
    declarations: [DialogConnectionAuthComponent],
    imports: [
        CommonModule,
        TranslateModule,
        MaterialModule,
        ReactiveFormsModule
    ],
    exports: [DialogConnectionAuthComponent]
})
export class ConnectionModule {
}
