<app-dialog-layout
    [title]="'private.training-organism.attendees.dialog.title.update' | translate"
    [disabled]="loading"
    [showCancel]="false"
    (dialogClose)="closeModal()">

    <treo-message *ngIf="this.dialogData.attendeeLink && !this.dialogData.allowUpdateWithoutDocument"
                  type="info"
                  [showIcon]="false"
                  class="mt-2 mb-4" appearance="outline">
        <button ngxClipboard
                [cbContent]="this.dialogData.attendeeLink"
                (cbOnSuccess)="copyLink()"
                [attr.aria-label]="'Copier'">
            <p class="text-left"> {{ 'private.common.registrationFolder.attendee.updateRestriction' | translate }}
                <span class="underline">{{ 'common.actions.attendeeLink'| translate }}</span>
            </p>
        </button>
    </treo-message>

    <form class="flex flex-col" [formGroup]="formGroup">

        <app-form-fields class="grid grid-cols-6 gap-4 card-loading-show"
                         formGroupName="attendee"
                         [appFormFieldsData]="appFormFieldsData"
                         [formGroup]="formGroup">
        </app-form-fields>

        <div *ngIf="errorMessages?.length" class="flex items-center">
            <treo-message class="flex-auto" appearance="outline" [showIcon]="false" type="error">
                <ul>
                    <li *ngFor="let errorMessage of errorMessages">
                        {{ errorMessage }}
                    </li>
                </ul>
            </treo-message>
        </div>

        <treo-message type="warn" *ngIf="identificationDocument" [showIcon]="false" class="mr-2" appearance="outline">
            {{ 'common.actions.updateFromIdDisclaimer' | translate }}
        </treo-message>

        <div class="flex flex-row mt-2 py-2 mb-2 justify-between">
            <div class="flex flex-row">
                <button (click)="idDocument.click()"
                        style="height: 50px !important;"
                        mat-flat-button type="button"
                        color="primary"
                        [disabled]="loading">
                    <mat-icon svgIcon="badge" class="mr-2"></mat-icon>
                    <ng-container *ngIf="loading; else notLoading">
                        <p class="mr-2" *ngIf="identificationDocument">{{ 'common.actions.analysing' | translate }}</p>
                        <mat-progress-spinner [color]="'primary'" [diameter]="24"
                                              [mode]="'indeterminate'"></mat-progress-spinner>
                    </ng-container>
                    <ng-template #notLoading>
                        {{ 'common.actions.updateFromId' | translate }}
                    </ng-template>
                </button>
                <input #idDocument
                       type="file"
                       class="hidden"
                       (change)="onFileChange($event)"
                       accept="image/jpeg,image/jpg,image/bmp,image/tiff,image/heif,image/png,application/pdf">
                <ng-container *ngIf="!removedNir">
                    <button (click)="nirDocument.click()"
                            class="ml-2"
                            style="height: 50px !important;"
                            mat-flat-button type="button"
                            color="primary"
                            [disabled]="loading">
                        <div class="flex items-center justify-center">
                            <mat-icon svgIcon="badge" class="mr-2"></mat-icon>
                            <p *ngIf="!loading">
                                {{ 'common.actions.completeNir' | translate }}
                            </p>
                            <mat-progress-spinner [color]="'primary'" *ngIf="loading" [diameter]="24"
                                                  [mode]="'indeterminate'"></mat-progress-spinner>
                        </div>
                    </button>
                    <input #nirDocument
                           type="file"
                           class="hidden"
                           (change)="onSocialSecurityChange($event)"
                           accept="image/jpeg,image/jpg,image/bmp,image/tiff,image/heif,image/png,application/pdf">
                </ng-container>
            </div>
            <div class="flex flex-row">
                <button type="button" mat-button (click)="closeModal()">
                    {{ 'common.actions.cancel' | translate }}
                </button>
                <button *ngIf="allowUpdate || formGroup.dirty "
                        (click)="update()"
                        [disabled]="loading || formGroup.invalid"
                        type="button" mat-flat-button color="primary">
                    <mat-progress-spinner *ngIf="loading; else submitLabel" [diameter]="24" mode="indeterminate">
                    </mat-progress-spinner>
                    <ng-template #submitLabel>
                        {{ 'common.actions.update' | translate }}
                    </ng-template>
                </button>
            </div>
        </div>
    </form>
</app-dialog-layout>

<ng-template #copySuccess>
    {{ 'common.actions.copySuccessAttendee' | translate }}
</ng-template>
