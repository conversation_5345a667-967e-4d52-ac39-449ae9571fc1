import {Component, Inject, OnInit, TemplateRef, ViewChild} from '@angular/core';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material/dialog';
import {Attendee, CandidatePassportBody, CandidateSocialSecurityBody, City, Country} from '../../api/models/attendee';
import {TranslateService} from '@ngx-translate/core';
import {AppFormFieldData} from '../../material/app-form-field/app-form-field.component';
import {AuthService} from '../../../core/auth/auth.service';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../errors/errors.types';
import {EntityContext} from '../../api/models/entity-context';
import {AttendeeService} from '../../api/services/attendee.service';
import {CandidatePassportService} from '../../api/services/candidate-passport.service';
import {of} from 'rxjs';
import CountriesList from '../../../../assets/countriesAndCitiesList/countriesListCode.json';
import {CityService} from '../../api/services/city.service';
import {MatSnackBar} from '@angular/material/snack-bar';
import {FormValidators} from '../../api/shared/form-validators';
import {map} from 'rxjs/operators';
import {GeoApiGouvAddress, GeoApiGouvAddressService} from '@placeme/ngx-geo-api-gouv-address';
import {LocationToStringPipe} from '../../pipes/location-to-string.pipe';
import {EnvironmentService} from '../../environment/environment.service';
import {Organism} from '../../api/models/organism';

@Component({
    selector: 'app-attendee-edit',
    templateUrl: './attendee-edit.component.html',
    styleUrls: ['./attendee-edit.component.scss']
})
export class AttendeeEditComponent implements OnInit {
    formGroup: FormGroup;
    appFormFieldsData: AppFormFieldData[];
    loading = false;
    errorMessages: string[] = [];
    allowUpdate = false;
    identificationDocument: File;
    countriesList: typeof CountriesList;
    @ViewChild('copySuccess') copySuccessTemplate: TemplateRef<any>;
    removedNir = true;

    constructor(
        private _formBuilder: FormBuilder,
        private _translateService: TranslateService,
        public dialogRef: MatDialogRef<AttendeeEditComponent>,
        private _authService: AuthService,
        private _attendeeService: AttendeeService,
        private _cityService: CityService,
        private _candidatePassportService: CandidatePassportService,
        private _snackBar: MatSnackBar,
        public environmentService: EnvironmentService,
        private _geoApiGouvAddressService: GeoApiGouvAddressService,
        private _locationToStringPipe: LocationToStringPipe,
        @Inject(MAT_DIALOG_DATA) public dialogData: {
            attendee: Attendee;
            contextEntity: EntityContext,
            allowUpdateWithoutDocument: boolean,
            attendeeLink?: string,
            organism?: Organism,
            isAttendee: boolean,
            nirData?: { canUpdateNir: boolean, retrievedNir: string }
        },
    ) {
        this.countriesList = CountriesList;
    }

    closeModal(): void {
        this.dialogRef.close();
    }

    ngOnInit(): void {
        this.loading = true;
        if (!this.dialogData.isAttendee && this.dialogData.nirData && this.dialogData.organism
            && this.environmentService.isEnableBetaFeatures(this.dialogData.organism)) {
            this.removedNir = !this.dialogData.nirData.canUpdateNir;
        }
        this.initForm(this.dialogData.attendee,
            this.dialogData.allowUpdateWithoutDocument && !this.dialogData.isAttendee,
            this.removedNir ? null : this.dialogData.nirData?.retrievedNir);
        this.loading = false;
    }

    initForm(attendee: Attendee, allowUpdate: boolean, nirValue: string): void {
        this.allowUpdate = allowUpdate;
        this.formGroup = this._formBuilder.group({
            attendee: this._formBuilder.group({})
        });
        const attendeeAddress = attendee.address;
        let addressValues = '';
        if (attendeeAddress) {
            ['number', 'repetitionIndexLabel', 'roadTypeLabel', 'roadName'].forEach((field) => {
                if (attendeeAddress[field] && attendeeAddress[field].length) {
                    addressValues += attendeeAddress[field] + ' ';
                }
            });
        }
        const addressLocation = addressValues.length ? {
            address: addressValues,
            postalCode: attendeeAddress.zipCode,
            city: attendeeAddress.city
        } : null;
        this.appFormFieldsData = [
            {
                controlName: 'genderDisabled',
                disabled: true,
                removed: allowUpdate,
                label: 'private.common.registrationFolder.attendee.gender.label',
                type: 'text',
                value: attendee.gender ? this._translateService.instant('private.common.registrationFolder.attendee.gender.' + attendee.gender) : 'Non renseigné',
                colSpan: 2
            },
            {
                controlName: 'gender',
                removed: !allowUpdate,
                label: 'private.common.registrationFolder.attendee.gender.label',
                type: 'radio',
                value: attendee.gender,
                colSpan: 2,
                required: true,
                choices: [
                    {
                        key: this._translateService.instant('private.training-organism.attendees.dialog.gender.female'),
                        value: 'female'
                    },
                    {
                        key: this._translateService.instant('private.training-organism.attendees.dialog.gender.male'),
                        value: 'male'
                    }
                ]
            },
            {
                controlName: 'firstName',
                value: attendee.firstName,
                disabled: !allowUpdate,
                label: 'private.common.registrationFolder.attendee.firstName',
                type: 'text',
                icon: 'badge',
                required: true,
                colSpan: 2
            },
            {
                controlName: 'lastName',
                value: attendee.lastName,
                disabled: !allowUpdate,
                label: 'private.common.registrationFolder.attendee.lastName',
                type: 'text',
                icon: 'badge',
                required: true,
                colSpan: 2
            },
            {
                controlName: 'birthName',
                value: attendee.birthName,
                disabled: !allowUpdate,
                label: 'private.common.registrationFolder.attendee.birthName',
                type: 'text',
                icon: 'dns',
                colSpan: 2
            },
            {
                controlName: 'firstName2',
                value: attendee.firstName2,
                disabled: !allowUpdate,
                label: 'private.common.registrationFolder.attendee.firstName2',
                type: 'text',
                icon: 'badge',
                colSpan: 2
            },
            {
                controlName: 'firstName3',
                value: attendee.firstName3,
                disabled: !allowUpdate,
                label: 'private.common.registrationFolder.attendee.firstName3',
                type: 'text',
                icon: 'badge',
                colSpan: 2
            },
            {
                controlName: 'email',
                value: attendee.email,
                disabled: attendee.emailValidated,
                label: 'private.common.registrationFolder.attendee.email',
                type: 'email',
                icon: attendee.emailValidated ? 'verified_user' : 'email',
                iconTooltip: attendee.emailValidated ? 'common.actions.validatedField.email' : '',
                iconClass: attendee.emailValidated ? 'text-primary' : '',
                colSpan: 2,
                copy: attendee.emailValidated,
                validators: [Validators.email],
                validatorsMessages: {
                    email: 'private.profile.organism.form.fields.email.error'
                },
            },
            {
                controlName: 'phoneNumber',
                value: attendee.phoneNumber,
                disabled: attendee.phoneNumberValidated,
                label: 'private.common.registrationFolder.attendee.phoneNumber',
                type: 'tel',
                icon: attendee.phoneNumberValidated ? 'verified_user' : 'smartphone',
                iconTooltip: attendee.phoneNumberValidated ? 'common.actions.validatedField.phoneNumber' : '',
                iconClass: attendee.phoneNumberValidated ? 'text-primary' : '',
                colSpan: 2,
                copy: attendee.phoneNumberValidated,
                validators: [Validators.pattern(FormValidators.MOBILEPHONE_PATTERN)],
                validatorsMessages: {
                    pattern: 'public.funnel.errors.phoneNumber'
                },
            },
            {
                controlName: 'phoneFixed',
                value: attendee.phoneFixed,
                disabled: true,
                label: 'private.common.registrationFolder.attendee.phoneFixed',
                type: 'tel',
                icon: 'local_phone',
                copy: true,
                folderEntityContext: this.dialogData.contextEntity,
                colSpan: 2,
            },
            {
                controlName: 'dateOfBirth',
                value: attendee.dateOfBirth,
                disabled: !allowUpdate,
                label: 'private.common.registrationFolder.attendee.dateOfBirth',
                type: 'date',
                icon: 'event',
                colSpan: 2
            },
            {
                controlName: 'cityOfBirth',
                value: attendee.codeCityOfBirth ? {
                    cog: attendee.codeCityOfBirth,
                    name: attendee.nameCityOfBirth,
                    postalCode: attendee.postalCode ?? null
                } : null,
                disabled: !allowUpdate,
                label: 'private.common.registrationFolder.attendee.nameCityOfBirth',
                type: 'search',
                icon: 'location_city',
                removable: true,
                removed: attendee.codeCountryOfBirth && attendee.codeCountryOfBirth !== 100,
                searchNoEntriesFoundLabel: 'private.training-organism.attendees.dialog.nameCityOfBirthNotFound',
                placeholder: !attendee.nameCityOfBirth ? 'private.training-organism.attendees.dialog.nameCityOfBirthSearch' : 'private.common.form.placeholder',
                searchComparisonProperty: 'cog',
                change: (controlName, newValue, formData) => {
                    const appFormFieldNameCountryOfBirth = formData.find(field => field.controlName === 'countryOfBirth');
                    appFormFieldNameCountryOfBirth.removed = !!newValue;
                    return [appFormFieldNameCountryOfBirth];
                },
                searchMethod: (searchTerm: string) => searchTerm ? this._cityService.listCitiesByName(searchTerm) : of([]),
                searchResultFormatter: (city: City) => city.name + ' (' + (city.postalCode ? city.postalCode : city.cog.slice(0, 2)) + ')',
                colSpan: 2
            },
            {
                controlName: 'countryOfBirth',
                value: attendee.nameCountryOfBirth ? this.countriesList.find((country) => attendee.nameCountryOfBirth === country.name) : null,
                disabled: !allowUpdate,
                removed: attendee.codeCityOfBirth && (!attendee.codeCountryOfBirth || attendee.codeCountryOfBirth === 100),
                icon: 'location_city',
                label: 'private.common.registrationFolder.attendee.nameCountryOfBirth',
                type: 'select',
                removable: true,
                change: (controlName, newValue, formData) => {
                    const appFormFieldNameCityOfBirth = formData.find(field => field.controlName === 'cityOfBirth');
                    appFormFieldNameCityOfBirth.removed = !!newValue;
                    return [appFormFieldNameCityOfBirth];
                },
                choices: Object.values(this.countriesList).map((country: Country) => ({
                    key: country.name,
                    value: country
                })),
                colSpan: 2
            },
            {
                controlName: 'location',
                label: 'private.profile.organism.form.fields.location.label',
                placeholder: addressLocation?.address ? this._locationToStringPipe.transform(addressLocation) : 'private.common.form.placeholder',
                type: 'search',
                searchNoEntriesFoundLabel: 'private.profile.organism.form.fields.location.error',
                searchMethod: (q) => q && q.length >= 4 ? this._geoApiGouvAddressService.query({q}).pipe(
                    map((searchResultsReponse) => {
                        return searchResultsReponse.features.map(f => f.properties);
                    })
                ) : of([]),
                searchResultFormatter: (geoApiGouvAddress: GeoApiGouvAddress) => geoApiGouvAddress.label,
                change: (controlName, newAddress, formData) => {
                    const appFormFieldCity = formData.find(field => field.controlName === 'city');
                    const appFormFieldNumber = formData.find(field => field.controlName === 'number');
                    const appFormFieldRoadName = formData.find(field => field.controlName === 'roadName');
                    const appFormFieldRoadTypeLabel = formData.find(field => field.controlName === 'roadTypeLabel');
                    const appFormFieldZipCode = formData.find(field => field.controlName === 'zipCode');
                    const appFormFieldRepetitionIndexLabel = formData.find(field => field.controlName === 'repetitionIndexLabel');
                    [appFormFieldCity, appFormFieldNumber, appFormFieldRoadName, appFormFieldRoadTypeLabel,
                        appFormFieldZipCode, appFormFieldRepetitionIndexLabel].forEach((appFormField) => {
                        appFormField.removed = !newAddress;
                    });
                    appFormFieldCity.value = newAddress ? newAddress.city : null;
                    appFormFieldZipCode.value = newAddress ? newAddress.postcode : null;
                    appFormFieldNumber.value = newAddress ? newAddress.housenumber : null;
                    const street = newAddress ? newAddress.street.split(' ') : null;
                    appFormFieldRoadTypeLabel.value = street ? street[0] : null;
                    appFormFieldRoadName.value = street ? street.slice(1, street.length).join(' ') : null;
                    return [appFormFieldCity, appFormFieldNumber, appFormFieldZipCode, appFormFieldRepetitionIndexLabel, appFormFieldRoadName, appFormFieldRoadTypeLabel];
                }
            },
            {
                controlName: 'number',
                removed: true,
                disabled: true,
                label: 'private.training-organism.attendees.dialog.address.number',
                type: 'text',
                colSpan: 1
            },
            {
                controlName: 'repetitionIndexLabel',
                removed: true,
                label: 'private.training-organism.attendees.dialog.address.repetitionIndexLabel',
                type: 'select',
                removable: true,
                choices: [
                    {key: 'bis', value: 'bis'},
                    {key: 'quinquies', value: 'quinquies'},
                    {key: 'quater', value: 'quater'},
                    {key: 'ter', value: 'ter'}
                ],
                colSpan: 1
            },
            {
                controlName: 'roadTypeLabel',
                removed: true,
                disabled: true,
                label: 'private.training-organism.attendees.dialog.address.roadTypeLabel',
                type: 'text',
                colSpan: 1
            },
            {
                controlName: 'roadName',
                removed: true,
                disabled: true,
                label: 'private.training-organism.attendees.dialog.address.roadName',
                type: 'text',
                colSpan: 1
            },
            {
                controlName: 'zipCode',
                removed: true,
                disabled: true,
                label: 'private.training-organism.attendees.dialog.address.zipCode',
                type: 'text',
                colSpan: 1
            },
            {
                controlName: 'city',
                removed: true,
                disabled: true,
                label: 'private.training-organism.attendees.dialog.address.city',
                type: 'text',
                colSpan: 1
            },
            {
                controlName: 'degreeTitle',
                value: attendee.degreeTitle,
                disabled: true,
                label: 'private.common.registrationFolder.attendee.degreeTitle',
                type: 'text',
                icon: 'school'
            },
            {
                controlName: 'nirGender',
                placeholder: 'X',
                type: 'magicText',
                removed: this.removedNir,
                value: nirValue?.length >= 1 ? nirValue?.substring(0, 1) : null,
                colSpan: 1,
                maxLength: 1,
                minLength: 1,
                parameters: {
                    current: 'nirGender',
                    next: 'nirYearBirth',
                    length: 1
                },
                paste: (event, controlName, formGroup, formGroupName) => {
                    const clipboardData = event.clipboardData;
                    const nirCopied = clipboardData.getData('text');
                    if (nirCopied?.length) {
                        formGroup.get(formGroupName).get('nirGender').setValue(nirCopied.charAt(0));
                        formGroup.get(formGroupName).get('nirYearBirth').setValue(nirCopied.substring(1, 3));
                        formGroup.get(formGroupName).get('nirMonthBirth').setValue(nirCopied.substring(3, 5));
                        formGroup.get(formGroupName).get('nirInseeBirth').setValue(nirCopied.substring(5, 10));
                        formGroup.get(formGroupName).get('nirOrderBirth').setValue(nirCopied.substring(10, 13));
                        formGroup.get(formGroupName).get('nirKey').setValue(nirCopied.substring(13));
                    }
                },
                validatorsMessages: {
                    minlength: this._translateService.instant('common.errors.minlength', {number: 1})
                }
            },
            {
                controlName: 'nirYearBirth',
                placeholder: 'XX',
                type: 'magicText',
                value: nirValue?.length >= 5 ? nirValue?.substring(1, 3) : null,
                removed: this.removedNir,
                colSpan: 1,
                maxLength: 2,
                minLength: 2,
                parameters: {
                    previous: 'nirGender',
                    current: 'nirYearBirth',
                    next: 'nirMonthBirth',
                    length: 2
                },
                validatorsMessages: {
                    minlength: this._translateService.instant('common.errors.minlength', {number: 2})
                }
            },
            {
                controlName: 'nirMonthBirth',
                placeholder: 'XX',
                type: 'magicText',
                value: nirValue?.length >= 5 ? nirValue.substring(3, 5) : null,
                removed: this.removedNir,
                colSpan: 1,
                maxLength: 2,
                minLength: 2,
                parameters: {
                    previous: 'nirYearBirth',
                    current: 'nirMonthBirth',
                    next: 'nirInseeBirth',
                    length: 2
                },
                validatorsMessages: {
                    minlength: this._translateService.instant('common.errors.minlength', {number: 2})
                }
            },
            {
                controlName: 'nirInseeBirth',
                placeholder: 'XXXXX',
                type: 'magicText',
                value: nirValue?.length >= 10 ? nirValue.substring(5, 10) : null,
                removed: this.removedNir,
                colSpan: 1,
                maxLength: 5,
                minLength: 5,
                parameters: {
                    previous: 'nirMonthBirth',
                    current: 'nirInseeBirth',
                    next: 'nirOrderBirth',
                    length: 5
                },
                validatorsMessages: {
                    minlength: this._translateService.instant('common.errors.minlength', {number: 5})
                }
            },
            {
                controlName: 'nirOrderBirth',
                placeholder: 'XXX',
                type: 'magicText',
                value: nirValue?.length === 15 ? nirValue.substring(10, 13) : null,
                removed: this.removedNir,
                colSpan: 1,
                minLength: 3,
                maxLength: 3,
                parameters: {
                    previous: 'nirInseeBirth',
                    current: 'nirOrderBirth',
                    next: 'nirKey',
                    length: 3
                },
                validatorsMessages: {
                    minlength: this._translateService.instant('common.errors.minlength', {number: 3})
                }
            },
            {
                controlName: 'nirKey',
                placeholder: 'XX',
                type: 'magicText',
                colSpan: 1,
                removed: this.removedNir,
                value: nirValue?.length === 15 ? nirValue.substring(13) : null,
                maxLength: 2,
                minLength: 2,
                parameters: {
                    previous: 'nirOrderBirth',
                    current: 'nirKey',
                    length: 2
                },
                validatorsMessages: {
                    minlength: this._translateService.instant('common.errors.minlength', {number: 2})
                }
            }
        ];
    }

    onFileChange(event: any): void {
        if (event.target.files.length > 0) {
            if (event.target.files[0].size > 1048576 * 4) { // 1,048,576 = 1MB max 4MB at the moment
                this.errorMessages = [this._translateService.instant('public.passport.errors.filesize')];
                return;
            }
            this.loading = true;
            this._authService.authWithIdDocument(event.target.files[0]).subscribe({
                next: (valueCollected: CandidatePassportBody) => {
                    const attendee = this.dialogData.attendee;
                    if (!valueCollected.codeCityOfBirth && attendee.codeCityOfBirth) {
                        valueCollected.codeCityOfBirth = attendee.codeCityOfBirth;
                        valueCollected.nameCityOfBirth = attendee.nameCityOfBirth;
                    } else if (!valueCollected.codeCountryOfBirth && attendee.codeCountryOfBirth) {
                        valueCollected.codeCountryOfBirth = attendee.codeCountryOfBirth;
                        valueCollected.nameCountryOfBirth = attendee.nameCountryOfBirth;
                    }
                    const attendeeFromId = {
                        ...attendee,
                        gender: valueCollected.gender,
                        firstName: valueCollected.firstName,
                        lastName: valueCollected.lastName,
                        birthName: valueCollected.birthName,
                        firstName2: valueCollected.firstName2,
                        firstName3: valueCollected.firstName3,
                        dateOfBirth: valueCollected.dateOfBirth,
                        codeCityOfBirth: valueCollected.codeCityOfBirth,
                        nameCityOfBirth: valueCollected.nameCityOfBirth,
                        postalCode: valueCollected.postalCode,
                        nameCountryOfBirth: valueCollected.nameCountryOfBirth !== 'France' ? valueCollected.nameCountryOfBirth : null,
                        codeCountryOfBirth: valueCollected.codeCountryOfBirth !== 100 ? valueCollected.codeCountryOfBirth : null,
                    };
                    this.initForm(attendeeFromId, true, this.removedNir ? null : this.dialogData.nirData?.retrievedNir);
                    this.identificationDocument = event.target.files[0];
                    this.loading = false;
                }, error: (error: HttpErrorResponse): void => {
                    this.loading = false;
                    this.errorMessages = (error.error as ApiError)?.errorMessages ?? ['Une erreur est survenue, veuillez réessayer'];
                }
            });
        }
    }

    update(): void {
        this.loading = true;
        const formGroup = this.formGroup.getRawValue().attendee;
        const body: CandidatePassportBody = {
            gender: formGroup.gender,
            firstName: formGroup.firstName,
            lastName: formGroup.lastName,
            birthName: formGroup.birthName,
            firstName2: formGroup.firstName2,
            firstName3: formGroup.firstName3,
            dateOfBirth: formGroup.dateOfBirth,
            nameCityOfBirth: formGroup.countryOfBirth && formGroup.countryOfBirth?.name !== 'France' ?
                null : formGroup.cityOfBirth ? formGroup.cityOfBirth.name : null,
            codeCityOfBirth: formGroup.countryOfBirth && formGroup.countryOfBirth?.name !== 'France' ?
                null : formGroup.cityOfBirth ? formGroup.cityOfBirth.cog : null,
            codeCountryOfBirth: formGroup.cityOfBirth ? null : formGroup.countryOfBirth ? formGroup.countryOfBirth?.cog : null,
            nameCountryOfBirth: formGroup.cityOfBirth ? null : formGroup.countryOfBirth ? formGroup.countryOfBirth?.name : null,
            email: formGroup.email,
            phoneNumber: formGroup.phoneNumber
        };
        if (!this.removedNir && formGroup.nirGender?.length) {
            const nirGender = formGroup.nirGender;
            const nirYearBirth = nirGender + formGroup.nirYearBirth;
            const nirMonthBirth = nirYearBirth + formGroup.nirMonthBirth;
            const nirInseeBirth = nirMonthBirth + formGroup.nirInseeBirth;
            const nirOrderBirth = nirInseeBirth + formGroup.nirOrderBirth;
            body.retrievedNir = nirOrderBirth + formGroup.nirKey;
        }
        if (formGroup.location) {
            body.address = {
                city: formGroup.city,
                number: formGroup.number,
                zipCode: formGroup.zipCode,
                roadName: formGroup.roadName,
                roadTypeLabel: formGroup.roadTypeLabel,
                repetitionIndexLabel: formGroup.repetitionIndexLabel,
            };
        }
        (this.dialogData.isAttendee ? this._candidatePassportService : this._attendeeService)
            .update(this.dialogData.contextEntity.class, this.dialogData.contextEntity.id, body, this.identificationDocument)
            .subscribe({
                next: (attendee) => {
                    this.loading = false;
                    this.dialogRef.close({attendee});
                },
                error: (httpErrorResponse: HttpErrorResponse) => {
                    this.loading = false;
                    this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
                }
            });
    }

    copyLink(): void {
        this._snackBar.openFromTemplate(this.copySuccessTemplate, {
            duration: 2500,
        });
    }

    onSocialSecurityChange(event: any): void {
        if (event.target.files.length > 0) {
            if (event.target.files[0].size > 1048576 * 4) { // 1,048,576 = 1MB max 4MB at the moment
                this.errorMessages = [this._translateService.instant('public.passport.errors.filesize')];
                return;
            }
            this.loading = true;
            this._authService.authWithSocialSecurityCardDocument(event.target.files[0]).subscribe({
                next: (data: CandidateSocialSecurityBody) => {
                    this.loading = false;
                    let displayError = null;
                    if (data.firstName.toLowerCase() !== this.dialogData.attendee.firstName.toLowerCase()) {
                        displayError = ['Erreur, le prénom inscrit sur la carte vitale ne correspond pas avec le candidat'];
                    }
                    if (data.lastName.toLowerCase() !== this.dialogData.attendee.lastName.toLowerCase() &&
                        data.lastName.toLowerCase() !== this.dialogData.attendee.birthName.toLowerCase()) {
                        displayError = ['Erreur, le nom de famille inscrit sur la carte vitale ne correspond pas avec le candidat'];
                    }
                    if (displayError) {
                        this.errorMessages = displayError;
                    } else {
                        const formGroup = this.formGroup.getRawValue().attendee;
                        const attendee = this.dialogData.attendee;
                        const body = {
                            ...attendee,
                            gender: formGroup.gender,
                            firstName: formGroup.firstName,
                            lastName: formGroup.lastName,
                            birthName: formGroup.birthName,
                            firstName2: formGroup.firstName2,
                            firstName3: formGroup.firstName3,
                            dateOfBirth: formGroup.dateOfBirth,
                            nameCityOfBirth: formGroup.countryOfBirth && formGroup.countryOfBirth?.name !== 'France' ?
                                null : formGroup.cityOfBirth ? formGroup.cityOfBirth.name : null,
                            codeCityOfBirth: formGroup.countryOfBirth && formGroup.countryOfBirth?.name !== 'France' ?
                                null : formGroup.cityOfBirth ? formGroup.cityOfBirth.cog : null,
                            codeCountryOfBirth: formGroup.cityOfBirth ? null : formGroup.countryOfBirth ? formGroup.countryOfBirth?.cog : null,
                            nameCountryOfBirth: formGroup.cityOfBirth ? null : formGroup.countryOfBirth ? formGroup.countryOfBirth?.name : null,
                            email: formGroup.email,
                            phoneNumber: formGroup.phoneNumber
                        };
                        this.initForm(body, this.dialogData.allowUpdateWithoutDocument && !this.dialogData.isAttendee, data.nir);
                    }
                }, error: (error: HttpErrorResponse): void => {
                    this.loading = false;
                    this.errorMessages = (error.error as ApiError)?.errorMessages ?? ['Une erreur est survenue, veuillez réessayer'];
                }
            });
        }
    }
}
