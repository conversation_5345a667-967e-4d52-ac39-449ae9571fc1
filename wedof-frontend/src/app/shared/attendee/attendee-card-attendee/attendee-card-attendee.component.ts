import {Component, Input, OnInit, ViewChild} from '@angular/core';
import {FormBuilder, FormGroup, FormGroupDirective} from '@angular/forms';
import {AppFormFieldData} from '../../material/app-form-field/app-form-field.component';
import {Attendee, City} from '../../api/models/attendee';
import {TranslateService} from '@ngx-translate/core';
import {AttendeeService} from '../../api/services/attendee.service';
import CountriesList from '../../../../assets/countriesAndCitiesList/countriesListCode.json';
import _ from 'lodash';
import {startCase, toLower} from 'lodash-es';
import {EntityContext} from '../../api/models/entity-context';
import {EntityClass} from '../../utils/enums/entity-class';
import {AttendeeEditComponent} from '../attendee-edit/attendee-edit.component';
import {MatDialog} from '@angular/material/dialog';

@Component({
    selector: 'app-attendee-card-attendee',
    templateUrl: './attendee-card-attendee.component.html',
    styleUrls: ['./attendee-card-attendee.component.scss']
})
export class AttendeeCardAttendeeComponent implements OnInit {

    city: string;
    countriesList: typeof CountriesList;
    fullName: string;
    cardLoading = true;
    panelOpenState = false;
    formGroup: FormGroup = null;
    appFormFieldsData: AppFormFieldData[];

    @Input() attendee: Attendee;
    @ViewChild('form') form: FormGroupDirective;
    @Input() entityContext: EntityContext;

    constructor(
        private _formBuilder: FormBuilder,
        private _translateService: TranslateService,
        private _attendeeService: AttendeeService,
        private _dialog: MatDialog
    ) {
        this.countriesList = CountriesList;
    }


    ngOnInit(): void {
        this.initForm(this.attendee);
    }

    openPanel(): void {
        this.panelOpenState = true;
    }

    closePanel(): void {
        this.panelOpenState = false;
    }

    initForm(attendee: Attendee): void {
        if (!attendee) {
            return;
        }
        this.cardLoading = true;
        this.fullName = _.capitalize(attendee.firstName) + ' ' + attendee.lastName.toUpperCase();
        this.formGroup = this._formBuilder.group({
            attendee: this._formBuilder.group({})
        });
        this.city = attendee.fullAddress ? attendee.fullAddress : attendee.address?.city && startCase(toLower(attendee.address.city));
        const cityAttendee: City = attendee.codeCityOfBirth ? {
            cog: attendee.codeCityOfBirth,
            name: attendee.nameCityOfBirth
        } : null;
        // Rely on name because two countries can have the same cog (e.g. code 502)
        const countryAttendee = attendee.nameCountryOfBirth ? this.countriesList.find((country) => attendee.nameCountryOfBirth === country.name) : null;
        this.appFormFieldsData = [
            {
                controlName: 'gender',
                disabled: true,
                label: 'private.common.registrationFolder.attendee.gender.label',
                type: 'text',
                icon: 'person',
                value: attendee.gender && this._translateService.instant('private.training-organism.attendees.dialog.gender.' + attendee.gender)
            },
            {
                controlName: 'lastName',
                disabled: true,
                label: 'private.common.registrationFolder.attendee.lastName',
                type: 'text',
                icon: 'badge',
                colSpan: 3,
            },
            {
                controlName: 'firstName',
                disabled: true,
                label: 'private.common.registrationFolder.attendee.firstName',
                type: 'text',
                icon: 'badge',
                colSpan: 3,
            },
            {
                controlName: 'firstName2',
                disabled: true,
                label: 'private.common.registrationFolder.attendee.firstName2',
                type: 'text',
                icon: 'badge',
                colSpan: 3,
            },
            {
                controlName: 'firstName3',
                disabled: true,
                label: 'private.common.registrationFolder.attendee.firstName3',
                type: 'text',
                icon: 'badge',
                colSpan: 3,
            },
            {
                controlName: 'email',
                disabled: true,
                label: 'private.common.registrationFolder.attendee.email',
                type: 'email',
                icon: 'email'
            },
            {
                controlName: 'phoneNumber',
                disabled: true,
                label: 'private.common.registrationFolder.attendee.phoneNumber',
                type: 'tel',
                icon: 'smartphone',
                colSpan: 3,
            },
            {
                controlName: 'phoneFixed',
                disabled: true,
                label: 'private.common.registrationFolder.attendee.phoneFixed',
                type: 'tel',
                icon: 'local_phone'
            },
            {
                controlName: 'city',
                value: this.city,
                disabled: true,
                label: 'private.common.registrationFolder.attendee.address',
                type: 'text',
                icon: 'apartment',
            },
            {
                controlName: 'birthName',
                disabled: true,
                label: 'private.common.registrationFolder.attendee.birthName',
                type: 'text',
                icon: 'dns'
            },
            {
                controlName: 'dateOfBirth',
                disabled: true,
                label: 'private.common.registrationFolder.attendee.dateOfBirth',
                type: 'date',
                icon: 'event',
                colSpan: 3,
            },
            {
                controlName: 'nameCityOfBirth',
                value: cityAttendee,
                disabled: true,
                label: 'private.common.registrationFolder.attendee.nameCityOfBirth',
                type: 'search',
                icon: 'location_city',
                removable: false,
                removed: !!attendee.nameCountryOfBirth,
                searchComparisonProperty: 'cog',
                searchResultFormatter: (city: City) => city.name + ' (' + (city.postalCode ? city.postalCode : city.cog.slice(0, 2)) + ')',
                colSpan: 3,
            },
            {
                controlName: 'nameCountryOfBirth',
                disabled: true,
                value: countryAttendee?.name,
                icon: 'location_city',
                label: 'private.common.registrationFolder.attendee.nameCountryOfBirth',
                type: 'text',
                removed: !attendee.nameCountryOfBirth,
                colSpan: 3,
            },
            {
                controlName: 'degreeTitle',
                disabled: true,
                label: 'private.common.registrationFolder.attendee.degreeTitle',
                type: 'text',
                icon: 'school'
            }
        ];
        this.cardLoading = false;
    }

    getAvatar(): string {
        return this.attendee ? this._attendeeService.getAvatar(this.attendee) : '';
    }

    getCardTitle(): string {
        const type = this.entityContext.class === EntityClass.CERTIFICATION_FOLDER ? 'candidate' : 'attendee';
        return this._translateService.instant('private.common.registrationFolder.attendee.' + type);
    }

    openEdit(): void {
        const dialogRef = this._dialog.open(AttendeeEditComponent, {
            disableClose: true,
            panelClass: ['full-page-scroll-50'],
            data: {
                attendee: this.attendee,
                contextEntity: this.entityContext,
                allowUpdateWithoutDocument: false,
                isAttendee: true
            }
        });
        dialogRef.afterClosed().subscribe(result => {
            if (result?.attendee) {
                this.attendee = {...this.attendee, ...result?.attendee};
                this.initForm(this.attendee);
            }
        });
    }
}
