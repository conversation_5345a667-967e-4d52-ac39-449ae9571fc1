<mat-card class="flex-col mt-3 p-5 pb-0 pt-3 border shadow-none sm">
    <div class="flex items-center mb-2">
        <mat-icon class="mr-3 text-4xl" color="primary">share</mat-icon>
        <div>
            <div
                class="text-xl font-semibold">Partager votre réussite !
            </div>
            <div
                class="text-secondary text-md">Ajouter votre certification à votre profil professionnel
            </div>
        </div>
    </div>
    <div class="flex my-4" *ngIf="certifier">
        <div class="text-center w-1/4">
            <a href="{{ getFacebookUrl() }}" class="block" target="_blank">
                <img src="../../../../assets/images/sharing/facebook.svg" style="width: 50px;" class="m-auto mb-2"/>
                <b>Facebook</b>
            </a>
        </div>
        <div class="text-center w-1/4">
            <a href="{{ getLinkedInProfilUrl() }}" class="block" target="_blank">
                <img src="../../../../assets/images/sharing/linkedin.svg" style="width: 50px;" class="m-auto mb-2"/>
                <b>LinkedIn (Profil)</b>
            </a>
        </div>
        <div class="text-center w-1/4">
            <a href="{{ getLinkedInPostUrl() }}" class="block" target="_blank">
                <img src="../../../../assets/images/sharing/linkedin.svg" style="width: 50px;" class="m-auto mb-2"/>
                <b>LinkedIn</b>
            </a>
        </div>
        <div class="text-center w-1/4">
            <a href="{{ getXUrl() }}" class="block" target="_blank">
                <img src="../../../../assets/images/sharing/x.svg" style="width: 50px;" class="m-auto mb-2"/>
                <b>X (Twitter)</b>
            </a>
        </div>
    </div>
</mat-card>
