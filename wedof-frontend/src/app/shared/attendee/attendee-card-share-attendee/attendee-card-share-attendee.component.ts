import {Component, Input, OnInit} from '@angular/core';
import {Attendee} from '../../api/models/attendee';
import {CertificationFolder} from '../../api/models/certification-folder';
import {Organism} from '../../api/models/organism';
import {OrganismService} from '../../api/services/organism.service';
import {Certification} from '../../api/models/certification';
import {map} from 'rxjs/operators';
import {CERTIFICATE_FILE_TYPE_ID} from '../../api/models/file';

@Component({
    selector: 'app-attendee-card-share-attendee',
    templateUrl: './attendee-card-share-attendee.component.html',
    styleUrls: ['./attendee-card-share-attendee.component.scss']
})
export class AttendeeCardShareAttendeeComponent implements OnInit {

    certifier: Organism;
    @Input() attendee: Attendee;
    @Input() certification: Certification;
    @Input() certificationFolder: CertificationFolder;

    constructor(
        private _organismService: OrganismService
    ) {
    }


    ngOnInit(): void {
        this._organismService.get(this.certificationFolder._links.certifier.siret).pipe(
            map(organism => {
                this.certifier = organism;
            })
        ).subscribe();
    }

    public getLinkedInProfilUrl(): URL {
        const issueDate = this.certificationFolder.issueDate;
        const expirationDate = this.certificationFolder.expirationDate;
        const url = new URL('https://www.linkedin.com/profile/add');
        url.searchParams.append('startTask', 'CERTIFICATION_NAME');
        url.searchParams.append('name', this.certificationFolder._links.certification.name);
        url.searchParams.append('issueYear', String(new Date(issueDate).getFullYear()));
        url.searchParams.append('issueMonth', String(new Date(issueDate).getMonth()));
        if (expirationDate) {
            url.searchParams.append('expirationYear', String(new Date(expirationDate).getFullYear()));
            url.searchParams.append('expirationMonth', String(new Date(expirationDate).getMonth()));
        }
        if (this.certificationFolder.certificateId) {
            url.searchParams.append('certId', this.certificationFolder.certificateId);
        }
        if (this.certifier.linkedInOrganisationId) {
            url.searchParams.append('organizationId', String(this.certifier.linkedInOrganisationId));
        } else {
            url.searchParams.append('organizationName', String(this.certifier.name));
        }
        if (this.certificationFolder.files.find(file => (file.typeId === CERTIFICATE_FILE_TYPE_ID)) && this.certificationFolder.certificateId) {
            url.searchParams.append('certUrl', document.location.origin
                + '/app/public/certificateHolder/'
                + this.certificationFolder._links.certification.externalId
                + '/' + this.certificationFolder.certificateId);
        }

        return url;
    }

    getFacebookUrl(): URL {
        let url;
        if (this.getShareableUrl()) {
            url = new URL('http://www.facebook.com/sharer/sharer.php');
            url.searchParams.append('u', this.getShareableUrl());
            url.searchParams.append('t', this.getShareableTitle());
        } else {
            url = new URL('https://www.facebook.com/');
        }
        return url;
    }

    public getLinkedInPostUrl(): URL {
        const url = new URL('https://www.linkedin.com/feed/');
        url.searchParams.append('shareActive', 'true');
        url.searchParams.append('text', this.getFullText());
        return url;
    }

    public getXUrl(): URL {
        const url = new URL('https://twitter.com/intent/tweet?');
        url.searchParams.set('text', this.getFullText());
        return url;
    }

    private getFullText(): string {
        let text = this.getShareableTitle();
        if (this.getShareableUrl()) {
            text += ' \r\n\r\n' + this.getShareableUrl();
        }
        const tags = [this.certificationFolder._links.certification.externalId];
        tags.push(...this.certification.domains.map(it => it.name.replace(/ /g, '').trim()));
        text += ' \r\n\r\n' + tags.map(it => '#' + it).join(' ');
        return text;
    }

    private getShareableUrl(): string {
        if (this.certificationFolder.certificateId) {
            return document.location.origin
                + '/app/public/certificateHolder/'
                + this.certification.type
                + '/'
                + this.certification.code
                + '/'
                + this.certificationFolder.certificateId;
        } else {
            return null;
        }
    }

    private getShareableTitle(): string {
        return 'Je viens d\'obtenir la certification : ' + this.certification.name;
    }
}
