<app-dialog-layout
    [title]="getTitle()"
    [actions]="actions"
    [disabled]="loading"
    (dialogClose)="closeModal()">

    <form class="flex flex-col" [formGroup]="formGroupCreateAttendee" (ngSubmit)="submit()" (keydown.enter)="submit()">
        <fieldset class="p-2 fieldset fieldset-primary">
            <legend class="flex justify-between w-full">
                <div class="px-1 self-center font-medium text-primary">
                    Identité
                </div>
                <div class="my-3 mr-2">
                    <button (click)="idDocument.click()"
                            style="height: 50px !important;"
                            mat-flat-button type="button"
                            color="primary"
                            [disabled]="loading">
                        <mat-icon svgIcon="badge" class="mr-2"></mat-icon>
                        <ng-container *ngIf="loading; else notLoading">
                            <p class="mr-2">{{'common.actions.analysing' | translate}}</p>
                            <mat-progress-spinner [color]="'primary'" [diameter]="24"
                                                  [mode]="'indeterminate'"></mat-progress-spinner>
                        </ng-container>
                        <ng-template #notLoading>
                            {{ "Préremplir depuis une pièce d'identité" | translate }}
                        </ng-template>
                    </button>
                    <input #idDocument
                           type="file"
                           class="hidden"
                           (change)="onFileChange($event)"
                           accept="image/jpeg,image/jpg,image/bmp,image/tiff,image/heif,image/png,application/pdf">
                </div>
            </legend>
            <div class="flex flex-col gt-xs:flex-row">
                <mat-form-field class="flex-auto gt-xs:pr-3">
                    <mat-label>{{ 'private.training-organism.attendees.dialog.gender.gender' | translate }}</mat-label>
                    <mat-select formControlName="gender" required>
                        <mat-option
                            value=female> {{ 'private.training-organism.attendees.dialog.gender.female' | translate }}
                        </mat-option>
                        <mat-option
                            value=male> {{ 'private.training-organism.attendees.dialog.gender.male' | translate }}
                        </mat-option>
                    </mat-select>
                </mat-form-field>

                <mat-form-field class="flex-auto gt-xs:pr-3">
                    <mat-label> {{ 'private.training-organism.attendees.dialog.firstName' | translate }}</mat-label>
                    <input type="text" matInput required
                           formControlName="firstName">
                    <mat-error class="pb-1">
                        {{ 'private.training-organism.' + contextType + '.dialog.errors.firstName' | translate }}
                    </mat-error>
                </mat-form-field>

                <mat-form-field class="flex-auto gt-xs:pl-3">
                    <mat-label> {{ 'private.training-organism.attendees.dialog.lastName' | translate }}
                    </mat-label>
                    <input type="text" matInput required
                           formControlName="lastName">
                    <mat-error class="pb-1">
                        {{ 'private.training-organism.' + contextType + '.dialog.errors.lastName' | translate }}
                    </mat-error>
                </mat-form-field>
            </div>

            <div class="flex flex-col gt-xs:flex-row">
                <mat-form-field class="flex-auto gt-xs:pr-3">
                    <mat-label> {{ 'private.training-organism.attendees.dialog.birthName' | translate }}</mat-label>
                    <input type="text" matInput
                           formControlName="birthName">
                </mat-form-field>

                <mat-form-field class="flex-auto gt-xs:pl-3">
                    <mat-label> {{ 'private.training-organism.attendees.dialog.dateOfBirth' | translate }}</mat-label>
                    <input formControlName="dateOfBirth" matInput [max]="maxDate" [min]="minDate"
                           [matDatepicker]="dateOfBirth"
                           [placeholder]="'private.training-organism.attendees.dialog.dateOfBirth' | translate">
                    <mat-datepicker-toggle matSuffix [for]="dateOfBirth"></mat-datepicker-toggle>
                    <mat-datepicker #dateOfBirth></mat-datepicker>
                </mat-form-field>
            </div>

            <div class="flex flex-col gt-xs:flex-row">
                <mat-form-field class="flex-auto gt-xs:pr-3"
                                *ngIf="!formGroupCreateAttendee.get('countryOfBirth').value">
                    <mat-label> {{ 'private.training-organism.attendees.dialog.nameCityOfBirth' | translate }}</mat-label>
                    <mat-select #citiesSelect
                                formControlName="cityOfBirth"
                                [placeholder]="'private.training-organism.attendees.dialog.nameCityOfBirthSearch' | translate">
                        <mat-option>
                            <ngx-mat-select-search
                                [formControl]="citiesFilteringCtrl"
                                [clearSearchInput]="true"
                                [noEntriesFoundLabel]="'private.training-organism.attendees.dialog.nameCityOfBirthNotFound' | translate"
                                [placeholderLabel]="'private.training-organism.attendees.dialog.nameCityOfBirthSearch' | translate"
                                [searching]="searchingCity"></ngx-mat-select-search>
                        </mat-option>
                        <mat-option *ngFor="let city of filteredCities | async" [value]="city">
                            {{ city.name + ' (' + (city.postalCode ? city.postalCode : city.cog.slice(0, 2)) + ')' }}
                        </mat-option>
                    </mat-select>
                    <button *ngIf="formGroupCreateAttendee.get('cityOfBirth').value" mat-button matSuffix
                            mat-icon-button aria-label="Clear" (click)="clearValue('cityOfBirth', $event)">
                        <mat-icon svgIcon="close"></mat-icon>
                    </button>
                </mat-form-field>

                <mat-form-field
                    [class]=" formGroupCreateAttendee.get('cityOfBirth').value ? 'flex-auto gt-xs:pl-3' : 'flex-auto gt-xs:pl-0' "
                    *ngIf="!formGroupCreateAttendee.get('cityOfBirth').value">
                    <mat-label> {{ 'private.training-organism.attendees.dialog.countryOfBirth' | translate }}</mat-label>
                    <mat-select formControlName="countryOfBirth">
                        <mat-option *ngFor="let country of countriesList" [value]="country">
                            {{ country.name }}
                        </mat-option>
                    </mat-select>
                    <button *ngIf="formGroupCreateAttendee.get('countryOfBirth').value" mat-button matSuffix
                            mat-icon-button aria-label="Clear" (click)="clearValue('countryOfBirth', $event)">
                        <mat-icon svgIcon="close"></mat-icon>
                    </button>
                </mat-form-field>
            </div>
        </fieldset>

        <fieldset class="p-2 fieldset fieldset-primary">
            <legend class="px-1 self-center font-medium text-primary">
                Coordonnées
            </legend>
            <div class="flex flex-col gt-xs:flex-row">
                <mat-form-field class="flex-auto gt-xs:pr-3">
                    <mat-label> {{ 'private.training-organism.attendees.dialog.email' | translate }}</mat-label>
                    <input type="email" matInput required formControlName="email"
                           (ngModelChange)="formGroupCreateAttendee.get('email').value !== '' && showErrorIfEmailOrPhoneTaken(formGroupCreateAttendee.get('email').value)">
                    <mat-error class="pb-1">
                        {{ 'private.training-organism.' + contextType + '.dialog.errors.email' | translate }}
                    </mat-error>
                </mat-form-field>
                <mat-form-field class="flex-auto gt-xs:pr-3 gt-xs:pl-3">
                    <mat-label> {{ 'private.training-organism.attendees.dialog.phoneNumber' | translate }}</mat-label>
                    <input type="text" matInput formControlName="phoneNumber"
                           [matTooltip]="'private.training-organism.'+ contextType + '.dialog.toolTip' | translate"
                           [matTooltipPosition]="'above'"
                           [matTooltipShowDelay]="500"
                           (change)="formGroupCreateAttendee.get('phoneNumber').value !== '' &&
                       showErrorIfEmailOrPhoneTaken(formGroupCreateAttendee.get('phoneNumber').value)"
                           [required]="!this.formGroupCreateAttendee.get('phoneFixed').value && this.formGroupCreateAttendee.get('phoneFixed').valid"
                    >
                    <mat-error class="pb-1">
                        {{ 'private.training-organism.' + contextType + '.dialog.errors.phoneNumber' | translate }}
                    </mat-error>
                </mat-form-field>
                <mat-form-field class="flex-auto gt-xs:pl-3">
                    <mat-label> {{ 'private.training-organism.attendees.dialog.phoneFixed' | translate }}</mat-label>
                    <input type="text" matInput formControlName="phoneFixed"
                           [required]="!this.formGroupCreateAttendee.get('phoneNumber').value && this.formGroupCreateAttendee.get('phoneNumber').valid">
                    <mat-error class="pb-1">
                        {{ 'private.training-organism.' + contextType + '.dialog.errors.phoneFixed' | translate }}
                    </mat-error>
                </mat-form-field>
            </div>

            <treo-message class="mb-2 flex justify-start align-middle" type="warn" [showIcon]="false"
                          appearance="outline"
                          *ngIf="isMsgErrorAttendeeAlreadyCreatedEmail || isMsgErrorAttendeeAlreadyCreatedPhone">
                <p>{{ 'private.training-organism.' + contextType + (isMsgErrorAttendeeAlreadyCreatedEmail ? '.dialog.errors.emailAlreadyExist' : '.dialog.errors.phoneAlreadyExist') | translate }}</p>
            </treo-message>

            <mat-form-field class="flex-auto w-full">
                <mat-label> {{ 'private.common.registrationFolder.attendee.address' | translate }}</mat-label>
                <mat-select #addressesSelect
                            (selectionChange)="selectionLocation($event)"
                            formControlName="location">
                    <mat-option>
                        <ngx-mat-select-search
                            [formControl]="addressesFilteringCtrl"
                            [clearSearchInput]="true"
                            [noEntriesFoundLabel]="'private.profile.organism.form.fields.location.error' | translate"
                            [placeholderLabel]="'private.profile.organism.form.fields.location.placeholder' | translate"
                            [searching]="searchingAddress"></ngx-mat-select-search>
                    </mat-option>
                    <mat-option *ngFor="let address of filteredAddress | async" [value]="address">
                        {{ address.label }}
                    </mat-option>
                </mat-select>
                <button *ngIf="formGroupCreateAttendee.get('location').value" mat-button matSuffix
                        mat-icon-button aria-label="Clear" (click)="clearLocation($event)">
                    <mat-icon svgIcon="close"></mat-icon>
                </button>
            </mat-form-field>
            <div class="flex flex-col gt-xs:flex-row" *ngIf="formGroupCreateAttendee.get('location').value">
                <mat-form-field class="flex-auto gt-xs:pr-3">
                    <mat-label>{{ 'private.training-organism.attendees.dialog.address.number' | translate }}</mat-label>
                    <input type="text" matInput formControlName="number">
                </mat-form-field>
                <mat-form-field class="flex-auto gt-xs:pr-3">
                    <mat-label>{{ 'private.training-organism.attendees.dialog.address.repetitionIndexLabel' | translate }}</mat-label>
                    <mat-select formControlName="repetitionIndexLabel">
                        <mat-option value=bis>bis</mat-option>
                        <mat-option value=quinquies>quinquies</mat-option>
                        <mat-option value=quater>quater</mat-option>
                        <mat-option value=ter>ter</mat-option>
                    </mat-select>
                    <button type="button" *ngIf="formGroupCreateAttendee.get('repetitionIndexLabel').value" mat-button
                            matSuffix
                            mat-icon-button aria-label="Clear" (click)="clearValue('repetitionIndexLabel', $event)">
                        <mat-icon svgIcon="close"></mat-icon>
                    </button>
                </mat-form-field>
                <mat-form-field class="flex-auto gt-xs:pr-3">
                    <mat-label>{{ 'private.training-organism.attendees.dialog.address.roadTypeLabel' | translate }}</mat-label>
                    <input type="text" matInput formControlName="roadTypeLabel">
                </mat-form-field>
                <mat-form-field class="flex-auto gt-xs:pr-3">
                    <mat-label>{{ 'private.training-organism.attendees.dialog.address.roadName' | translate }}</mat-label>
                    <input type="text" matInput formControlName="roadName">
                </mat-form-field>
                <mat-form-field class="flex-auto gt-xs:pr-3">
                    <mat-label>{{ 'private.training-organism.attendees.dialog.address.zipCode' | translate }}</mat-label>
                    <input type="text" matInput formControlName="zipCode">
                </mat-form-field>
                <mat-form-field class="flex-auto gt-xs:pl-3">
                    <mat-label> {{ 'private.training-organism.attendees.dialog.address.city' | translate }}</mat-label>
                    <input type="text" matInput formControlName="city">
                </mat-form-field>
            </div>
        </fieldset>

        <fieldset class="p-2 fieldset fieldset-primary" *ngIf="canFillNir">
            <legend class="flex justify-between w-full">
                <div class="px-1 self-center font-medium text-primary">
                    Numéro de sécurité sociale
                </div>
                <div class="my-3 mr-2">
                    <button (click)="nirDocument.click()"
                            style="height: 50px !important;"
                            mat-flat-button type="button"
                            color="primary"
                            class="mr-2"
                            [matTooltip]="disabledNirButton ? 'Remplissez les informations' : ''"
                            [disabled]="loading || disabledNirButton">
                        <mat-icon svgIcon="badge" class="mr-2"></mat-icon>
                        <ng-container *ngIf="loading; else notLoading">
                            <p class="mr-2">{{'common.actions.analysing' | translate}}</p>
                            <mat-progress-spinner [color]="'primary'" [diameter]="24"
                                                  [mode]="'indeterminate'"></mat-progress-spinner>
                        </ng-container>
                        <ng-template #notLoading>
                            {{ "Remplir le numéro de sécurité sociale" | translate }}
                        </ng-template>
                    </button>
                    <input #nirDocument
                           type="file"
                           class="hidden"
                           (change)="onSocialSecurityFileChange($event)"
                           accept="image/jpeg,image/jpg,image/bmp,image/tiff,image/heif,image/png,application/pdf">
                </div>
            </legend>
            <div class="flex flex-col gt-xs:flex-row">
                <mat-form-field class="flex-auto gt-xs:pr-3">
                    <input type="text"
                           (paste)="pasteMethod($event)"
                           (keyup)="keyup($event, 'nirGender', 1, 'nirYearBirth')"
                           (change)="checkNirGender()"
                           id="nirGender"
                           [minlength]="1"
                           [maxlength]="1"
                           placeholder="X"
                           matInput
                           formControlName="nirGender">
                </mat-form-field>
                <mat-form-field class="flex-auto gt-xs:pr-3">
                    <input type="text"
                           (keyup)="keyup($event, 'nirYearBirth', 2, 'nirMonthBirth', 'nirGender')"
                           [minlength]="2"
                           [maxlength]="2"
                           id="nirYearBirth"
                           matInput
                           placeholder="XX"
                           formControlName="nirYearBirth">
                </mat-form-field>
                <mat-form-field class="flex-auto gt-xs:pr-3">
                    <input type="text"
                           (keyup)="keyup($event, 'nirMonthBirth', 2, 'nirInseeBirth', 'nirYearBirth')"
                           [minlength]="2"
                           (change)="checkNirDateOfBirth()"
                           [maxlength]="2"
                           id="nirMonthBirth"
                           placeholder="XX"
                           matInput
                           formControlName="nirMonthBirth">
                </mat-form-field>
                <mat-form-field class="flex-auto gt-xs:pr-3">
                    <input type="text"
                           [minlength]="5"
                           (keyup)="keyup($event, 'nirInseeBirth', 5, 'nirOrderBirth', 'nirMonthBirth')"
                           (change)="checkNirInseeOfBirth()"
                           [maxlength]="5"
                           id="nirInseeBirth"
                           placeholder="XXXXX"
                           matInput
                           formControlName="nirInseeBirth">
                </mat-form-field>
                <mat-form-field class="flex-auto gt-xs:pr-3">
                    <input type="text"
                           [minlength]="3"
                           [maxlength]="3"
                           (keyup)="keyup($event, 'nirOrderBirth', 3, 'nirKey', 'nirInseeBirth')"
                           id="nirOrderBirth"
                           placeholder="XXX"
                           matInput
                           formControlName="nirOrderBirth">
                </mat-form-field>
                <mat-form-field class="flex-auto gt-xs:pl-3">
                    <input type="text"
                           [minlength]="2"
                           [maxlength]="2"
                           (keyup)="keyup($event, 'nirKey', 2, null, 'nirOrderBirth')"
                           id="nirKey"
                           placeholder="XX"
                           matInput
                           formControlName="nirKey">
                </mat-form-field>
            </div>
        </fieldset>

        <div *ngIf="errorMessages?.length" class="flex items-center">
            <treo-message class="flex-auto" appearance="outline" [showIcon]="false" type="error">
                <ul>
                    <li *ngFor="let errorMessage of errorMessages">
                        {{ errorMessage }}
                    </li>
                </ul>
            </treo-message>
        </div>

        <ng-template #actions>
            <div class="flex flex-row py-2 mb-2">
                <button type="button" color="primary" class="mt-2" mat-flat-button
                        *ngIf="isMsgErrorAttendeeAlreadyCreatedEmail || isMsgErrorAttendeeAlreadyCreatedPhone"
                        (click)="selectAttendee()">{{ 'private.training-organism.candidates.dialog.errors.useEmailOrPhone' | translate : {contextEntity: dialogData.contextEntity === "RegistrationFolder" ? "l'apprenant" : "le candidat"} }}
                </button>

                <button type="submit" color="primary" class="mt-2"
                        *ngIf="!isMsgErrorAttendeeAlreadyCreatedEmail && !isMsgErrorAttendeeAlreadyCreatedPhone"
                        mat-flat-button (click)="submit()" [disabled]="disabled()">
                    <mat-progress-spinner class="mr-4" *ngIf="loading" [diameter]="24"
                                          mode="indeterminate"></mat-progress-spinner>
                    <ng-container
                        *ngIf="!loading">{{ 'private.training-organism.' + contextType + '.button' | translate }}
                    </ng-container>
                </button>
            </div>
        </ng-template>
    </form>
</app-dialog-layout>
