import {After<PERSON>iewInit, Component, Inject, OnDestroy, OnInit, ViewChild} from '@angular/core';
import {FormBuilder, FormControl, FormGroup, Validators} from '@angular/forms';
import {AttendeeService} from '../../api/services/attendee.service';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material/dialog';
import CountriesList from '../../../../assets/countriesAndCitiesList/countriesListCode.json';
import {Observable, ReplaySubject, Subject} from 'rxjs';
import {
    Attendee,
    AttendeeCreateBody,
    CandidatePassportBody,
    CandidateSocialSecurityBody,
    City
} from '../../api/models/attendee';
import {FormValidators} from '../../api/shared/form-validators';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../errors/errors.types';
import {MatSelect} from '@angular/material/select';
import {filter, map, switchMap, takeUntil, tap} from 'rxjs/operators';
import {toLower, trim} from 'lodash-es';
import {EntityClass} from '../../utils/enums/entity-class';
import {TranslateService} from '@ngx-translate/core';
import {AuthService} from '../../../core/auth/auth.service';
import {CityService} from '../../api/services/city.service';
import {GeoApiGouvAddress, GeoApiGouvAddressService} from '@placeme/ngx-geo-api-gouv-address';
import {MatSelectChange} from '@angular/material/select/select';
import {EnvironmentService} from '../../environment/environment.service';
import {Select} from '@ngxs/store';
import {OrganismState} from '../../api/state/organism.state';
import {Organism} from '../../api/models/organism';
import moment from 'moment/moment';

@Component({
    selector: 'app-attendee-create',
    templateUrl: './attendee-create.component.html',
    styleUrls: ['./attendee-create.component.scss']
})
export class AttendeeCreateComponent implements OnInit, OnDestroy, AfterViewInit {
    formGroupCreateAttendee: FormGroup;
    countriesList: typeof CountriesList;
    searchCityValue: string;
    phoneNumber: string;
    email: string;
    isMsgErrorAttendeeAlreadyCreatedEmail: boolean;
    isMsgErrorAttendeeAlreadyCreatedPhone: boolean;
    filteredAttendees: ReplaySubject<Attendee[]> = new ReplaySubject<Attendee[]>(1);
    maxDate: Date;
    minDate: Date;
    loading = false;
    errorMessages: string[] = [];
    contextType: string;
    @ViewChild('citiesSelect', {static: true}) citiesSelect: MatSelect;
    searchingCity = false;
    filteredCities: ReplaySubject<City[]> = new ReplaySubject<City[]>(1);
    citiesFilteringCtrl: FormControl = new FormControl();

    searchAddressValue: string;
    searchingAddress = false;
    filteredAddress: ReplaySubject<GeoApiGouvAddress[]> = new ReplaySubject<GeoApiGouvAddress[]>(1);
    addressesFilteringCtrl: FormControl = new FormControl();
    @ViewChild('addressesSelect', {static: true}) addressesSelect: MatSelect;

    attendeeFound: Attendee = null;
    protected _unsubscribeAll = new Subject<void>();
    identificationDocument: File;
    socialSecurityBody: CandidateSocialSecurityBody;
    canFillNir = false;
    disabledNirButton = true;

    @Select(OrganismState.organism) organism$: Observable<Organism>;

    constructor(
        private _formBuilder: FormBuilder,
        private _attendeeService: AttendeeService,
        private _cityService: CityService,
        private _translateService: TranslateService,
        private _authService: AuthService,
        private _geoApiGouvAddressService: GeoApiGouvAddressService,
        public environmentService: EnvironmentService,
        public dialogRef: MatDialogRef<AttendeeCreateComponent>,
        @Inject(MAT_DIALOG_DATA) public dialogData: {
            attendeeSearchValue: string;
            newEmailCreateAttendee: Attendee;
            contextEntity: EntityClass;
        },
    ) {
        this.isMsgErrorAttendeeAlreadyCreatedEmail = false;
        this.isMsgErrorAttendeeAlreadyCreatedPhone = false;
    }

    ngOnInit(): void {
        this.organism$.pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe((organism) => {
            this.canFillNir = this.environmentService.isEnableBetaFeatures(organism) &&
                this.dialogData?.contextEntity === EntityClass.CERTIFICATION_FOLDER && organism.isCertifierOrganism;
            this.maxDate = new Date();
            this.minDate = new Date(1900, 0, 1);
            this.isMsgErrorAttendeeAlreadyCreatedEmail = false;
            this.isMsgErrorAttendeeAlreadyCreatedPhone = false;
            this.showErrorIfEmailOrPhoneTaken(this.dialogData.attendeeSearchValue);
            this.countriesList = CountriesList;
            if (this.dialogData.attendeeSearchValue) {
                this.dialogData.attendeeSearchValue.includes('@') ? this.email = this.dialogData.attendeeSearchValue : this.phoneNumber = this.dialogData.attendeeSearchValue;
            }
            this.formGroupCreateAttendee = this._formBuilder.group({
                lastName: ['', Validators.required],
                firstName: ['', Validators.required],
                email: [this.email ?? '', [Validators.required, Validators.pattern(FormValidators.EMAIL_PATTERN)]],
                phoneNumber: [this.phoneNumber ?? '', Validators.pattern(FormValidators.MOBILEPHONE_PATTERN)],
                phoneFixed: ['', Validators.pattern(FormValidators.PHONE_PATTERN)],
                dateOfBirth: [''],
                cityOfBirth: [],
                countryOfBirth: [],
                gender: ['', Validators.required],
                birthName: [''],
                location: [''],
                city: [{value: '', disabled: true}],
                number: [{value: '', disabled: true}],
                zipCode: [{value: '', disabled: true}],
                roadName: [{value: '', disabled: true}],
                roadTypeLabel: [{value: '', disabled: true}],
                repetitionIndexLabel: [''],
            });
            if (this.canFillNir) {
                this.formGroupCreateAttendee.addControl('nirGender', new FormControl({value: '', disabled: true}));
                this.formGroupCreateAttendee.addControl('nirYearBirth', new FormControl({value: '', disabled: true}));
                this.formGroupCreateAttendee.addControl('nirMonthBirth', new FormControl({value: '', disabled: true}));
                this.formGroupCreateAttendee.addControl('nirInseeBirth', new FormControl({value: '', disabled: true}));
                this.formGroupCreateAttendee.addControl('nirOrderBirth', new FormControl({value: '', disabled: true}));
                this.formGroupCreateAttendee.addControl('nirKey', new FormControl({value: '', disabled: true}));
            }
            this.contextType = this.dialogData?.contextEntity === EntityClass.CERTIFICATION_FOLDER ? 'candidates' : 'attendees';
        });
    }

    closeModal(): void {
        this.dialogRef.close();
    }

    showErrorIfEmailOrPhoneTaken(query: string): void {
        this._attendeeService.find(query).subscribe(result => {
            this.filteredAttendees.next(result);
            const attendeeFound = result.length > 0;
            this.attendeeFound = result[0];
            const isEmail = query.indexOf('@') > 0;
            const isPhone = query.charAt(0) === '0';
            this.isMsgErrorAttendeeAlreadyCreatedEmail = isEmail && attendeeFound;
            this.isMsgErrorAttendeeAlreadyCreatedPhone = isPhone && attendeeFound;
        });
    }

    disabled(): boolean {
        return (this.isMsgErrorAttendeeAlreadyCreatedEmail ||
            this.isMsgErrorAttendeeAlreadyCreatedPhone ||
            this.formGroupCreateAttendee.invalid ||
            this.errorMessages.length >= 1 ||
            (!this.formGroupCreateAttendee.get('phoneNumber').value && !this.formGroupCreateAttendee.get('phoneFixed').value)
        );
    }

    ngAfterViewInit(): void {
        this.citiesFilteringCtrl.valueChanges
            .pipe(
                filter(search => search && search.length >= 2),
                tap(() => this.searchingCity = true),
                takeUntil(this._unsubscribeAll),
                switchMap(search => {
                    this.searchCityValue = trim(toLower(search));
                    return this._cityService.listCitiesByName(search);
                }),
                takeUntil(this._unsubscribeAll),
            )
            .subscribe(filteredCities => {
                this.searchingCity = false;
                this.filteredCities.next(filteredCities);
            });
        setTimeout(() => {
            this.citiesFilteringCtrl.patchValue(' ');
            this.citiesFilteringCtrl.patchValue('');
        });
        this.addressesFilteringCtrl.valueChanges
            .pipe(
                filter(search => search && search.length >= 4),
                tap(() => this.searchingAddress = true),
                takeUntil(this._unsubscribeAll),
                switchMap(q => {
                    return this._geoApiGouvAddressService.query({q}).pipe(
                        map((searchResultsReponse) => {
                            return searchResultsReponse.features.map(f => f.properties);
                        })
                    );
                }),
                takeUntil(this._unsubscribeAll),
            )
            .subscribe(filteredAddress => {
                this.searchingAddress = false;
                this.filteredAddress.next(filteredAddress);
            });
        setTimeout(() => {
            this.addressesFilteringCtrl.patchValue(' ');
            this.addressesFilteringCtrl.patchValue('');
        });
        this.formGroupCreateAttendee.valueChanges.subscribe((values) => {
            let disabledNirFields = true;
            if (values.gender && values.firstName && values.lastName && values.dateOfBirth) {
                if (values.cityOfBirth || values.countryOfBirth) {
                    disabledNirFields = false;
                }
            }
            this.disabledNirButton = disabledNirFields;
            if (!disabledNirFields) {
                this.formGroupCreateAttendee.get('nirGender').enable({emitEvent: false});
            } else if (this.formGroupCreateAttendee.get('nirGender').disabled === false) {
                this.formGroupCreateAttendee.get('nirGender').disable();
            }
        });
        this.formGroupCreateAttendee.get('nirGender').valueChanges.subscribe(() => {
            this.enableFormNir();
        });
    }

    clearValue(valueToRemoved: string, event: MouseEvent): void {
        this.formGroupCreateAttendee.get(valueToRemoved).setValue(null);
        event.stopPropagation();
    }

    clearLocation(event: MouseEvent): void {
        const valuesToRemove = ['location', 'city', 'number', 'zipCode', 'roadName', 'roadTypeLabel', 'repetitionIndexLabel'];
        valuesToRemove.forEach((value) => {
            this.formGroupCreateAttendee.get(value).setValue(null);
        });
        event.stopPropagation();
    }

    selectionLocation($event: MatSelectChange): void {
        const newAddress = $event.value;
        const street = newAddress.street.split(' ');
        this.formGroupCreateAttendee.get('city').setValue(newAddress.city);
        this.formGroupCreateAttendee.get('zipCode').setValue(newAddress.postcode);
        this.formGroupCreateAttendee.get('number').setValue(newAddress.housenumber);
        this.formGroupCreateAttendee.get('roadTypeLabel').setValue(street[0]);
        this.formGroupCreateAttendee.get('roadName').setValue(street.slice(1, street.length).join(' '));
    }

    submit(): void {
        if (this.formGroupCreateAttendee.valid) {
            this.loading = true;
            const formGroupValues = this.formGroupCreateAttendee.getRawValue();
            const body: AttendeeCreateBody = {
                lastName: formGroupValues.lastName.toUpperCase(),
                firstName: formGroupValues.firstName,
                email: formGroupValues.email,
                phoneNumber: formGroupValues.phoneNumber?.length ? formGroupValues.phoneNumber : null,
                phoneFixed: formGroupValues.phoneFixed?.length ? formGroupValues.phoneFixed : null,
                dateOfBirth: formGroupValues.dateOfBirth?.length ? formGroupValues.dateOfBirth : null,
                nameCityOfBirth: formGroupValues.cityOfBirth?.name,
                codeCityOfBirth: formGroupValues.cityOfBirth?.cog,
                codeCountryOfBirth: formGroupValues.countryOfBirth?.cog,
                nameCountryOfBirth: formGroupValues.countryOfBirth?.name,
                gender: formGroupValues.gender,
                birthName: formGroupValues.birthName?.length ? formGroupValues.birthName : null,
                identificationDocument: this.identificationDocument ?? null,
            };
            if (formGroupValues.location) {
                body.address = {
                    city: formGroupValues.city?.length ? formGroupValues.city : null,
                    number: formGroupValues.number?.length ? formGroupValues.number : null,
                    zipCode: formGroupValues.zipCode?.length ? formGroupValues.zipCode : null,
                    roadName: formGroupValues.roadName?.length ? formGroupValues.roadName : null,
                    roadTypeLabel: formGroupValues.roadTypeLabel?.length ? formGroupValues.roadTypeLabel : null,
                    repetitionIndexLabel: formGroupValues.repetitionIndexLabel?.length ? formGroupValues.repetitionIndexLabel : null,
                };
            }
            if (this.canFillNir) {
                const nirGender = formGroupValues.nirGender;
                const nirYearBirth = nirGender + formGroupValues.nirYearBirth;
                const nirMonthBirth = nirYearBirth + formGroupValues.nirMonthBirth;
                const nirInseeBirth = nirMonthBirth + formGroupValues.nirInseeBirth;
                const nirOrderBirth = nirInseeBirth + formGroupValues.nirOrderBirth;
                body.nir = nirOrderBirth + formGroupValues.nirKey;
            }
            this._attendeeService.create(body).subscribe({
                next: (newAttendee) => {
                    this.loading = false;
                    this.dialogRef.close({data: newAttendee});
                },
                error: (httpErrorResponse: HttpErrorResponse) => {
                    this.loading = false;
                    this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
                }
            });
        }
    }

    selectAttendee(): void {
        this.dialogRef.close({data: this.attendeeFound});
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    getTitle(): string {
        const title = this.dialogData?.contextEntity === EntityClass.CERTIFICATION_FOLDER ? 'candidate' : 'attendee';
        return this._translateService.instant('private.training-organism.attendees.dialog.title.' + title);
    }

    onFileChange(event: any): void {
        if (event.target.files.length > 0) {
            if (event.target.files[0].size > 1048576 * 4) { // 1,048,576 = 1MB max 4MB at the moment
                this.errorMessages = [this._translateService.instant('public.passport.errors.filesize')];
                return;
            }
            this.loading = true;
            this._authService.authWithIdDocument(event.target.files[0]).subscribe(
                {
                    next: (valueCollected: CandidatePassportBody) => {
                        const cityCandidate: City = valueCollected.codeCityOfBirth ? {
                            cog: valueCollected.codeCityOfBirth,
                            name: valueCollected.nameCityOfBirth,
                            postalCode: valueCollected.postalCode
                        } : null;
                        const countryCandidate = cityCandidate ? null :
                            (valueCollected.nameCountryOfBirth ? this.countriesList.find((country) => valueCollected.codeCountryOfBirth === country.cog) : null);
                        this.formGroupCreateAttendee.get('gender').setValue(valueCollected.gender);
                        this.formGroupCreateAttendee.get('firstName').setValue(valueCollected.firstName);
                        this.formGroupCreateAttendee.get('lastName').setValue(valueCollected.lastName);
                        this.formGroupCreateAttendee.get('birthName').setValue(valueCollected.birthName);
                        this.formGroupCreateAttendee.get('dateOfBirth').setValue(valueCollected.dateOfBirth);
                        if (cityCandidate) {
                            this.filteredCities.next([cityCandidate]);
                        }
                        this.formGroupCreateAttendee.get('cityOfBirth').setValue(cityCandidate);
                        this.formGroupCreateAttendee.get('countryOfBirth').setValue(countryCandidate);
                        this.identificationDocument = event.target.files[0];
                        this.loading = false;
                    }, error: (error: HttpErrorResponse): void => {
                        this.loading = false;
                        this.errorMessages = (error.error as ApiError)?.errorMessages ?? ['Une erreur est survenue, veuillez réessayer'];
                    }
                }
            );
        }
    }

    onSocialSecurityFileChange(event: any): void {
        if (event.target.files.length > 0) {
            if (event.target.files[0].size > 1048576 * 4) { // 1,048,576 = 1MB max 4MB at the moment
                this.errorMessages = [this._translateService.instant('public.passport.errors.filesize')];
                return;
            }
            this.loading = true;
            this._authService.authWithSocialSecurityCardDocument(event.target.files[0]).subscribe({
                next: (socialSecurityBody: CandidateSocialSecurityBody) => {
                    this.socialSecurityBody = socialSecurityBody;
                    this.loading = false;
                    let matchingError = null;
                    const nir = socialSecurityBody.nir;
                    if (this.formGroupCreateAttendee.get('firstName').value ||
                        this.formGroupCreateAttendee.get('lastName').value ||
                        this.formGroupCreateAttendee.get('birthName').value) {
                        if (this.formGroupCreateAttendee.get('firstName').value && (socialSecurityBody.firstName !== this.formGroupCreateAttendee.get('firstName').value)) {
                            matchingError = ['Erreur, le prénom inscrit sur la carte vitale ne correspond pas.'];
                        }
                        const lastNamesValues = [this.formGroupCreateAttendee.get('lastName').value, this.formGroupCreateAttendee.get('birthName').value];
                        if (!lastNamesValues.includes(socialSecurityBody.lastName)) {
                            matchingError = ['Erreur, le nom de famille inscrit sur la carte vitale ne correspond pas.'];
                        }
                        if (matchingError) {
                            this.errorMessages = matchingError;
                        } else {
                            this.collectNir(nir);
                        }
                    } else {
                        this.collectNir(nir);
                    }
                }, error: (error: HttpErrorResponse): void => {
                    this.loading = false;
                    this.errorMessages = (error.error as ApiError)?.errorMessages ?? ['Une erreur est survenue, veuillez réessayer'];
                }
            });
        }
    }

    collectNir(nir: string): void {
        this.formGroupCreateAttendee.get('nirGender').setValue(nir.substring(0, 1));
        this.formGroupCreateAttendee.get('nirYearBirth').setValue(nir.substring(1, 3));
        this.formGroupCreateAttendee.get('nirMonthBirth').setValue(nir.substring(3, 5));
        this.formGroupCreateAttendee.get('nirInseeBirth').setValue(nir.substring(5, 10));
        this.formGroupCreateAttendee.get('nirOrderBirth').setValue(nir.substring(10, 13));
        this.formGroupCreateAttendee.get('nirKey').setValue(nir.substring(13));
        this.checkNirGender();
        this.checkNirDateOfBirth();
        this.checkNirInseeOfBirth();
    }

    enableFormNir(): void {
        this.formGroupCreateAttendee.get('nirYearBirth').enable({emitEvent: false});
        this.formGroupCreateAttendee.get('nirMonthBirth').enable({emitEvent: false});
        this.formGroupCreateAttendee.get('nirInseeBirth').enable({emitEvent: false});
        this.formGroupCreateAttendee.get('nirOrderBirth').enable({emitEvent: false});
        this.formGroupCreateAttendee.get('nirKey').enable({emitEvent: false});
    }

    pasteMethod(event): void {
        const clipboardData = event.clipboardData;
        const nirCopied = clipboardData.getData('text');
        this.enableFormNir();
        this.collectNir(nirCopied);
        document.getElementById('nirKey').focus();
    }

    keyup(event, current: string, length: number, next?: string, previous?: string): void {
        let previousFormField = null;
        if (previous) {
            previousFormField = document.getElementById(previous);
        }
        let nextFormField = null;
        if (next) {
            nextFormField = document.getElementById(next);
        }
        const formValue = this.formGroupCreateAttendee.get(current).value;
        if (event.code === 'Backspace' && !formValue.length && previousFormField) {
            previousFormField.focus();
        } else if (nextFormField && formValue?.length === length) {
            nextFormField.focus();
        }
    }

    checkNirGender(): void {
        this.errorMessages = this.errorMessages.filter((errorMessage) =>
            errorMessage !== 'La civilité ne correspond pas'
        );
        const nirGender = this.formGroupCreateAttendee.get('nirGender').value;
        const gender = this.formGroupCreateAttendee.get('gender').value;
        if ((gender === 'female' && nirGender !== '2') ||
            (gender === 'male' && nirGender !== '1')) {
            this.errorMessages.push('La civilité ne correspond pas');
        }
    }

    checkNirDateOfBirth(): void {
        this.errorMessages = this.errorMessages.filter((errorMessage) =>
            errorMessage !== 'La date de naissance ne correspond pas'
        );
        const nirYearBirth = this.formGroupCreateAttendee.get('nirYearBirth').value;
        const nirMonthBirth = this.formGroupCreateAttendee.get('nirMonthBirth').value;
        const dateOfBirth = this.formGroupCreateAttendee.get('dateOfBirth').value;
        if (moment(new Date(dateOfBirth)).format('YY') !== nirYearBirth ||
            moment(new Date(dateOfBirth)).format('MM') !== nirMonthBirth
        ) {
            this.errorMessages.push('La date de naissance ne correspond pas');
        }
    }

    checkNirInseeOfBirth(): void {
        this.errorMessages = this.errorMessages.filter((errorMessage) =>
            errorMessage !== 'Le lieu de naissance ne correspond pas'
        );
        const nirInseeBirth = this.formGroupCreateAttendee.get('nirInseeBirth').value;
        const cityOfBirth = this.formGroupCreateAttendee.get('cityOfBirth').value;
        const countryOfBirth = this.formGroupCreateAttendee.get('countryOfBirth').value;
        if ((cityOfBirth && nirInseeBirth !== cityOfBirth?.cog) ||
            (countryOfBirth && nirInseeBirth !== countryOfBirth?.cog)) {
            this.errorMessages.push('Le lieu de naissance ne correspond pas');
        }
    }
}
