<div class="flex align-center" *ngIf="withButton else onlyMenu">
    <button *ngIf="!attendee.readOnly else noActions"
            color="primary"
            [disabled]="loading"
            type="button" mat-flat-button
            (click)="openEdit()">
        <mat-progress-spinner class="mr-4"
                              *ngIf="loading" [diameter]="24"
                              mode="indeterminate"></mat-progress-spinner>
        <ng-container *ngIf="!loading">{{ 'common.actions.edit' | translate }}</ng-container>
    </button>
    <ng-template #noActions>
        <div class="text-disabled mt-2">{{ 'common.actions.noAction' | translate }}</div>
    </ng-template>
</div>

<ng-template #onlyMenu>
    <button
        type="button" mat-icon-button
        (click)="menuTrigger.openMenu()"
        title="Actions">
        <mat-icon svgIcon="more_vert"></mat-icon>
    </button>
    <button
        type="button" #menuTrigger="matMenuTrigger"
        [matMenuTriggerFor]="actionsMenu">
    </button>
</ng-template>

<mat-menu #actionsMenu="matMenu" class="large-menu">
    <button *ngIf="!attendee.readOnly"
            [disabled]="loading"
            type="button" mat-menu-item
            (click)="openEdit()">
        <mat-progress-spinner class="mr-4"
                              *ngIf="loading" [diameter]="24"
                              mode="indeterminate"></mat-progress-spinner>
        <ng-container *ngIf="!loading">
            <mat-icon svgIcon="edit"></mat-icon>
            {{ 'common.actions.edit' | translate }}
        </ng-container>
    </button>
    <ng-template matMenuContent>
        <button
            type="button" mat-menu-item (click)="openPanel()" *ngIf="hasOpenEvent && !panelOpenState">
            <mat-icon svgIcon="keyboard_arrow_down"></mat-icon>
            <span>{{ 'private.common.menu.open' | translate }}</span>
        </button>
        <button
            type="button" mat-menu-item (click)="closePanel()" *ngIf="hasOpenEvent && panelOpenState">
            <mat-icon svgIcon="keyboard_arrow_up"></mat-icon>
            <span>{{ 'private.common.menu.close' | translate }}</span>
        </button>
    </ng-template>
</mat-menu>
