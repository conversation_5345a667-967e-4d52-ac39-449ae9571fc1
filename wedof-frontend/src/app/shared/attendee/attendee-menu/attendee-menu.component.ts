import {Component, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild} from '@angular/core';
import {MatMenuTrigger} from '@angular/material/menu';
import {Attendee} from '../../api/models/attendee';
import {SnackBarComponent} from '../../material/snack-bar/snack-bar.component';
import {displayTextSnackBar} from '../../utils/displayTextSnackBar';
import {MatSnackBar} from '@angular/material/snack-bar';
import {TranslateService} from '@ngx-translate/core';
import {EntityContext} from '../../api/models/entity-context';
import {AttendeeEditComponent} from '../attendee-edit/attendee-edit.component';
import {MatDialog} from '@angular/material/dialog';
import {AttendeeService} from '../../api/services/attendee.service';
import {Observable, Subject} from 'rxjs';
import {takeUntil} from 'rxjs/operators';
import {Organism} from '../../api/models/organism';
import {Select} from '@ngxs/store';
import {OrganismState} from '../../api/state/organism.state';

@Component({
    selector: 'app-attendee-menu',
    templateUrl: './attendee-menu.component.html',
    styleUrls: ['./attendee-menu.component.scss']
})
export class AttendeeMenuComponent implements OnInit, OnDestroy {

    hasOpenEvent: boolean;
    loading = false;
    organism: Organism = null;

    @Input() panelOpenState: boolean;
    @Input() entityContext?: EntityContext;
    @Output() openEvent = new EventEmitter<void>();
    @Output() closeEvent = new EventEmitter<void>();
    @Input() withButton = false;
    @Output() processedAttendee: EventEmitter<Attendee> = new EventEmitter<Attendee>();
    @Output() initAttendee: EventEmitter<Attendee> = new EventEmitter<Attendee>();
    @Input() attendee: Attendee;
    @Input() attendeeLink?: string;
    @ViewChild('menuTrigger') menuTrigger: MatMenuTrigger;

    @Select(OrganismState.organism) organism$: Observable<Organism>;
    private _unsubscribeAll = new Subject<void>();

    constructor(private _dialog: MatDialog,
                private _snackBar: MatSnackBar,
                private _translateService: TranslateService,
                private _attendeeService: AttendeeService) {
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    ngOnInit(): void {
        this.organism$.pipe(takeUntil(this._unsubscribeAll)).subscribe((organism) => {
            this.hasOpenEvent = this.openEvent.observers.length > 0;
            this.organism = organism;
        });
    }

    openPanel(): void {
        this.openEvent.emit();
    }

    closePanel(): void {
        this.closeEvent.emit();
    }

    openEdit(): void {
        this.loading = true;
        this._attendeeService.canUpdateManually(this.entityContext.class, this.entityContext.id).subscribe(response => {
            this.loading = false;
            const allowUpdateWithoutDocument = response.canUpdateManually;
            const dialogRef = this._dialog.open(AttendeeEditComponent, {
                disableClose: true,
                panelClass: ['full-page-scroll-50'],
                data: {
                    attendee: this.attendee,
                    contextEntity: this.entityContext,
                    allowUpdateWithoutDocument: allowUpdateWithoutDocument,
                    attendeeLink: this.attendeeLink,
                    isAttendee: false,
                    organism: this.organism,
                    nirData: response.nir
                }
            });
            dialogRef.afterClosed().subscribe(result => {
                if (result?.attendee) {
                    this.attendee = {...this.attendee, ...result?.attendee};
                    this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(this._translateService.instant('common.actions.attendeeUpdatedSuccessfully', {
                            email: result?.attendee.email
                        },
                    )));
                    this.processedAttendee.emit(this.attendee);
                    this.initAttendee.emit(this.attendee);
                }
            });
        });
    }
}
