import {CommonModule} from '@angular/common';
import {NgModule} from '@angular/core';
import {TranslateModule} from '@ngx-translate/core';

import {AttendeeCreateComponent} from './attendee-create/attendee-create.component';
import {MaterialModule} from '../material/material.module';
import {ReactiveFormsModule} from '@angular/forms';
import {MatTooltipModule} from '@angular/material/tooltip';
import {NgxMatSelectSearchModule} from 'ngx-mat-select-search';
import {AttendeeCardComponent} from './attendee-card/attendee-card.component';
import {MatCardModule} from '@angular/material/card';
import {AttendeeMenuComponent} from './attendee-menu/attendee-menu.component';
import {ClipboardModule} from 'ngx-clipboard';
import {AttendeeCardAttendeeComponent} from './attendee-card-attendee/attendee-card-attendee.component';
import {AttendeeMenuFoldersComponent} from './attendee-menu-folders/attendee-menu-folders.component';
import {AttendeeCardShareAttendeeComponent} from './attendee-card-share-attendee/attendee-card-share-attendee.component';
import {AttendeeEditComponent} from './attendee-edit/attendee-edit.component';


@NgModule({
    declarations: [
        AttendeeCreateComponent,
        AttendeeEditComponent,
        AttendeeCardComponent,
        AttendeeMenuComponent,
        AttendeeCardAttendeeComponent,
        AttendeeCardShareAttendeeComponent,
        AttendeeMenuFoldersComponent
    ],
    imports: [
        CommonModule,
        TranslateModule,
        MaterialModule,
        ReactiveFormsModule,
        MatTooltipModule,
        NgxMatSelectSearchModule,
        MatCardModule,
        ClipboardModule,
    ],
    exports: [
        AttendeeCreateComponent,
        AttendeeEditComponent,
        AttendeeCardComponent,
        AttendeeCardAttendeeComponent,
        AttendeeMenuFoldersComponent,
        AttendeeCardShareAttendeeComponent
    ]
})
export class AttendeeModule {
}
