<mat-card class="flex-col mt-3 p-5 pb-0 pt-3 border shadow-none sm" [ngClass]="{'card-loading':cardLoading}">
    <div class="flex items-center mb-2">
        <mat-icon class="mr-3 card-loading-show text-4xl" color="primary">
            {{ attendee?.gender === 'female' ? 'woman' : attendee?.gender === 'male' ? 'man' : 'person' }}
        </mat-icon>
        <div>
            <div
                class="text-xl font-semibold card-loading-show">{{ getCardTitle() }}
            </div>
            <div
                class="text-secondary text-md card-loading-show">{{ attendee?.address?.city }} {{ attendee?.address?.zipCode && "(" + attendee?.address?.zipCode + ")" }}
            </div>
        </div>
        <div class="ml-auto -mr-4 card-loading-hidden">
            <app-attendee-menu [attendee]="attendee"
                               [entityContext]="attendeeFolderEntityContext"
                               [attendeeLink]="attendeeLink"
                               [panelOpenState]="panelOpenState"
                               (processedAttendee)="refresh($event)"
                               (initAttendee)="initForm($event)"
                               (openEvent)="openPanel()"
                               (closeEvent)="closePanel()"></app-attendee-menu>
        </div>
    </div>
    <div class="flex flex-col" *ngIf="!panelOpenState || cardLoading">
        <div class="flex flex-row mb-2">
            <div class="gravatar card-loading-show">
                <img [src]="getAvatar()" class="rounded-full" alt="avatar"/>
            </div>
            <div class="ml-4">
                <app-form-field-static class="font-bold" [value]="fullName" type="text"
                                       icon="badge"></app-form-field-static>
                <app-form-field-static *ngIf="attendee?.email"
                                       [value]="attendee?.email"
                                       [copy]="true"
                                       [folderEntityContext]="attendeeFolderEntityContext"
                                       type="email"
                                       [iconTooltip]="attendee.emailValidated ? 'common.actions.validatedField.email' : ''"
                                       [iconClass]="attendee.emailValidated ? 'text-primary' : ''"
                                       [icon]="attendee.emailValidated ? 'verified_user' : 'email'"></app-form-field-static>
                <app-form-field-static *ngIf="attendee?.phoneNumber else showPhoneFixed"
                                       [value]="attendee?.phoneNumber"
                                       [copy]="true"
                                       [folderEntityContext]="attendeeFolderEntityContext"
                                       type="tel"
                                       [iconTooltip]="attendee.phoneNumberValidated ? 'common.actions.validatedField.phoneNumber': ''"
                                       [iconClass]="attendee.phoneNumberValidated ? 'text-primary' : ''"
                                       [icon]="attendee.phoneNumberValidated ? 'verified_user' : 'smartphone'"></app-form-field-static>
                <ng-template #showPhoneFixed>
                    <app-form-field-static *ngIf="attendee?.phoneFixed"
                                           [value]="attendee?.phoneFixed"
                                           [folderEntityContext]="attendeeFolderEntityContext"
                                           [copy]="true"
                                           type="tel"
                                           icon="local_phone"></app-form-field-static>
                </ng-template>
            </div>
        </div>
        <app-form-field-static *ngIf="attendee?.degreeTitle" [value]="attendee?.degreeTitle" type="text"
                               icon="school"></app-form-field-static>
        <button class="flex justify-center mt-2 -mx-5 pt-2 pb-2 open-panel card-loading-hidden" (click)="openPanel()">
            <mat-icon svgIcon="keyboard_arrow_down"></mat-icon>
        </button>
    </div>

    <form #form="ngForm" [formGroup]="formGroup" class="flex flex-col" *ngIf="panelOpenState && !cardLoading">
        <app-form-fields class="grid grid-cols-6 gap-2"
                         formGroupName="attendee"
                         [entity]="attendee"
                         [appFormFieldsData]="appFormFieldsData"
                         [formGroup]="formGroup">
        </app-form-fields>
        <button class="flex justify-center -mx-5 pt-2 pb-2 close-panel" (click)="closePanel()">
            <mat-icon svgIcon="keyboard_arrow_up"></mat-icon>
        </button>
    </form>
    <div
        class="flex items-center justify-end border-t pl-5 pr-10 -mx-5 py-3 light:bg-cool-gray-50 dark:bg-cool-gray-700 card-loading-hidden">
        <app-attendee-menu class="-mr-4"
                           [attendee]="attendee"
                           [entityContext]="attendeeFolderEntityContext"
                           [attendeeLink]="attendeeLink"
                           [withButton]="true"
                           [panelOpenState]="panelOpenState"
                           (processedAttendee)="refresh($event)"
                           (initAttendee)="initForm($event)"
                           (openEvent)="openPanel()"
                           (closeEvent)="closePanel()"
        ></app-attendee-menu>
    </div>
</mat-card>
