import {
    Component,
    ElementRef,
    EventEmitter,
    Input,
    OnChanges,
    OnDestroy,
    OnInit,
    Output,
    SimpleChanges
} from '@angular/core';
import {FormBuilder, FormGroup} from '@angular/forms';
import {Attendee, City} from '../../api/models/attendee';
import CountriesList from '../../../../assets/countriesAndCitiesList/countriesListCode.json';
import {AttendeeService} from '../../api/services/attendee.service';
import _ from 'lodash';
import {AppFormFieldData} from '../../material/app-form-field/app-form-field.component';
import {startCase, toLower} from 'lodash-es';
import {TranslateService} from '@ngx-translate/core';
import {takeUntil} from 'rxjs/operators';
import {Observable, Subject} from 'rxjs';
import {EntityContext} from '../../api/models/entity-context';
import {EntityClass} from '../../utils/enums/entity-class';
import {BaseCardCanDeactivateDirective} from '../../utils/base-card/base-card-can-deactivate.directive';
import {RequiredCallSuper} from '../../utils/base-card/base-card.directive';
import {Select} from '@ngxs/store';
import {UserState} from '../../api/state/user.state';
import {User} from '../../api/models/user';
import {DocumentGenerationStates, File, FileStates} from '../../api/models/file';

@Component({
    selector: 'app-attendee-card',
    templateUrl: './attendee-card.component.html',
    styleUrls: ['./attendee-card.component.scss']
})
export class AttendeeCardComponent extends BaseCardCanDeactivateDirective implements OnInit, OnChanges, OnDestroy {
    static COMPONENT_ID = 'apprenant';
    city: string;
    countriesList: typeof CountriesList;
    fullName: string;
    panelOpenState = false;
    formGroup: FormGroup = null;
    appFormFieldsData: AppFormFieldData[];

    @Input() attendee: Attendee;
    @Input() attendeeLink?: string;
    @Input() attendeeFolderEntityContext: EntityContext;
    @Output() attendeeChange: EventEmitter<Attendee> = new EventEmitter<Attendee>();

    @Select(UserState.user) user$: Observable<User>;
    private _unsubscribeAll = new Subject<void>();

    constructor(
        private _formBuilder: FormBuilder,
        private _translateService: TranslateService,
        private _attendeeService: AttendeeService,
        private _el: ElementRef
    ) {
        super(AttendeeCardComponent.COMPONENT_ID, _el);
        this.countriesList = CountriesList;
    }

    /**
     * Shortcuts need to be enabled when the input params is available, otherwise create a bug where apprenant is the only value of shortcuts
     */
    ngOnInit(): void {
        this.updateShortcutId(this.getShortcutId());
        this.panelLoading();
        this.initForm(this.attendee);
    }

    ngOnChanges(changes: SimpleChanges): void {
        this.updateShortcutId(this.getShortcutId());
        this.panelLoading();
        this.initForm(this.attendee);
    }

    openPanel(): void {
        this.panelOpenState = true;
    }

    closePanel(): void {
        this.panelOpenState = false;
    }

    refresh(attendee: Attendee): void {
        this.attendee = attendee;
        this.attendeeChange.emit(attendee);
    }

    canDeactivate(): boolean {
        return true;
    }

    initForm(attendee: Attendee): void {
        this.user$.pipe(takeUntil(this._unsubscribeAll)).subscribe(user => {
            const isAdmin = user.is_impersonator ?? false;
            this.cardLoading = true;
            if (!attendee) {
                return;
            }
            this.fullName = _.capitalize(attendee.firstName) + ' ' + attendee.lastName.toUpperCase();
            this.formGroup = this._formBuilder.group({
                attendee: this._formBuilder.group({})
            });
            this.city = attendee.fullAddress ? attendee.fullAddress : attendee.address?.city && startCase(toLower(attendee.address.city));
            const cityAttendee: City = attendee.codeCityOfBirth ? {
                cog: attendee.codeCityOfBirth,
                name: attendee.nameCityOfBirth
            } : null;
            // Rely on name because two countries can have the same cog (e.g. code 502)
            const countryAttendee = attendee.nameCountryOfBirth ? this.countriesList.find((country) => attendee.nameCountryOfBirth === country.name) : null;

            const identificationDocumentLink = '/app/attendees/' + this.attendee.id + '/identificationDocument';
            const identificationDocument: File = {
                fileName: 'Document d\'identité',
                fileType: this.attendee.identificationDocument === 'pdf' ? 'application/pdf' : 'image/',
                generationState: DocumentGenerationStates.NOT_GENERATED,
                id: 0,
                permalink: identificationDocumentLink,
                state: FileStates.VALID,
                url: identificationDocumentLink
            };

            this.appFormFieldsData = [
                {
                    controlName: 'gender',
                    disabled: true,
                    label: 'private.common.registrationFolder.attendee.gender.label',
                    type: 'text',
                    icon: 'person',
                    value: attendee.gender ? this._translateService.instant('private.training-organism.attendees.dialog.gender.' + attendee.gender) : null
                },
                {
                    controlName: 'lastName',
                    disabled: true,
                    label: 'private.common.registrationFolder.attendee.lastName',
                    type: 'text',
                    icon: 'badge',
                    colSpan: 3,
                },
                {
                    controlName: 'firstName',
                    disabled: true,
                    label: 'private.common.registrationFolder.attendee.firstName',
                    type: 'text',
                    icon: 'badge',
                    colSpan: 3,
                },
                {
                    controlName: 'firstName2',
                    disabled: true,
                    label: 'private.common.registrationFolder.attendee.firstName2',
                    type: 'text',
                    icon: 'badge',
                    colSpan: 3,
                },
                {
                    controlName: 'firstName3',
                    disabled: true,
                    label: 'private.common.registrationFolder.attendee.firstName3',
                    type: 'text',
                    icon: 'badge',
                    colSpan: 3,
                },
                {
                    controlName: 'email',
                    disabled: true,
                    label: 'private.common.registrationFolder.attendee.email',
                    type: 'email',
                    icon: attendee.emailValidated ? 'verified_user' : 'email',
                    iconTooltip: attendee.emailValidated ? 'common.actions.validatedField.email' : '',
                    iconClass: attendee.emailValidated ? 'text-primary' : '',
                    copy: true,
                    folderEntityContext: this.attendeeFolderEntityContext
                },
                {
                    controlName: 'phoneNumber',
                    disabled: true,
                    label: 'private.common.registrationFolder.attendee.phoneNumber',
                    type: 'tel',
                    icon: attendee.phoneNumberValidated ? 'verified_user' : 'smartphone',
                    iconTooltip: attendee.phoneNumberValidated ? 'common.actions.validatedField.phoneNumber' : '',
                    iconClass: attendee.phoneNumberValidated ? 'text-primary' : '',
                    copy: !!attendee.phoneNumber,
                    folderEntityContext: this.attendeeFolderEntityContext,
                    colSpan: 3,
                },
                {
                    controlName: 'phoneFixed',
                    disabled: true,
                    label: 'private.common.registrationFolder.attendee.phoneFixed',
                    type: 'tel',
                    icon: 'local_phone',
                    copy: !!attendee.phoneFixed,
                    folderEntityContext: this.attendeeFolderEntityContext,
                    colSpan: 3,
                },
                {
                    controlName: 'city',
                    value: this.city,
                    disabled: true,
                    label: 'private.common.registrationFolder.attendee.address',
                    type: 'text',
                    icon: 'apartment',
                },
                {
                    controlName: 'birthName',
                    disabled: true,
                    label: 'private.common.registrationFolder.attendee.birthName',
                    type: 'text',
                    icon: 'dns'
                },
                {
                    controlName: 'dateOfBirth',
                    disabled: true,
                    label: 'private.common.registrationFolder.attendee.dateOfBirth',
                    type: 'date',
                    icon: 'event',
                    colSpan: 3,
                },
                {
                    controlName: 'placeOfBirth',
                    disabled: true,
                    label: 'private.common.registrationFolder.attendee.placeOfBirth.title',
                    icon: 'location_city',
                    colSpan: 3,
                    type: 'text',
                    value : countryAttendee ? countryAttendee.name : cityAttendee ? cityAttendee.name +  ' (' + cityAttendee.cog.slice(0, 2) + ')' : null,
                },
                {
                    controlName: 'degreeTitle',
                    disabled: true,
                    label: 'private.common.registrationFolder.attendee.degreeTitle',
                    type: 'text',
                    icon: 'school'
                },
                {
                    controlName: 'identificationDocument',
                    disabled: true,
                    value: identificationDocument,
                    removed: !(isAdmin && attendee.identificationDocument),
                    label: 'Pièce d\'identité',
                    type: 'file',
                    icon: 'image',
                    removable: false,
                    showFilePreview: false,
                    viewLabel: this._translateService.instant('common.actions.file.view', {file: 'la pièce d\'identité'}),
                }
            ];
            this.panelLoaded();
        });
    }

    getAvatar(): string {
        return this.attendee ? this._attendeeService.getAvatar(this.attendee) : '';
    }

    ngOnDestroy(): RequiredCallSuper {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
        return super.ngOnDestroy();
    }

    getCardTitle(): string {
        const type = this.attendeeFolderEntityContext.class === EntityClass.CERTIFICATION_FOLDER ? 'candidate' : 'attendee';
        return this._translateService.instant('private.common.registrationFolder.attendee.' + type);
    }

    protected getShortcutId(): string {
        return this.attendeeFolderEntityContext?.class === EntityClass.CERTIFICATION_FOLDER ? 'candidat' : 'apprenant';
    }
}
