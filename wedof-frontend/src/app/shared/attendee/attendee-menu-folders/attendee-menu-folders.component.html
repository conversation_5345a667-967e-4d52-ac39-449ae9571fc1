<button mat-icon-button type="button"
        (click)="$event.stopPropagation()"
        [matTooltip]="getTooltip()"
        [matMenuTriggerFor]="actionsMenu">
    <mat-icon svgIcon="share"></mat-icon>
</button>

<mat-menu #actionsMenu="matMenu" class="large-menu">
    <ng-template matMenuContent>
        <button ngxClipboard
                [cbContent]="permalink"
                (cbOnSuccess)="copy()"
                [attr.aria-label]="'Copier'"
                mat-menu-item>
            <mat-icon color="primary" svgIcon="person"></mat-icon>
            <span>{{ 'common.actions.permalink' | translate }}</span>
        </button>
        <button *ngIf="hasAccess(); else noActionsAvailable"
                ngxClipboard
                [cbContent]="attendeeLink"
                (cbOnSuccess)="copy()"
                [attr.aria-label]="'Copier'"
                mat-menu-item>
            <mat-icon color="primary" svgIcon="person"></mat-icon>
            <span>{{ 'common.actions.attendeeLink' | translate }} {{ attendeeTypeI18n }}</span>
        </button>
        <button *ngIf="hasAccess(); else noSurveyLink"
                ngxClipboard
                [cbContent]="surveyLink"
                (cbOnSuccess)="copy()"
                [attr.aria-label]="'Copier'"
                mat-menu-item>
            <mat-icon color="primary" svgIcon="person"></mat-icon>
            <span>{{ 'common.actions.surveyLink' | translate }}</span>
        </button>
        <button *ngIf="attendeeType === 'candidate' && addToPassportLink"
                ngxClipboard
                [cbContent]="addToPassportLink"
                (cbOnSuccess)="copy()"
                [attr.aria-label]="'Copier'"
                mat-menu-item>
            <mat-icon color="primary" svgIcon="person"></mat-icon>
            <span>{{ 'common.actions.addToPassportLink' | translate }}</span>
        </button>
        <ng-template #noActionsAvailable>
            <button mat-menu-item (click)="openDialogSubscription()">
                <mat-icon color="primary" svgIcon="person"></mat-icon>
                <span>{{ 'common.actions.attendeeLink' | translate }} {{ attendeeTypeI18n }}</span>
            </button>
        </ng-template>
        <ng-template #noSurveyLink>
            <button mat-menu-item (click)="openDialogSubscription()">
                <mat-icon color="primary" svgIcon="person"></mat-icon>
                <span>{{ 'common.actions.surveyLink' | translate }}</span>
            </button>
        </ng-template>
        <button *ngIf="user?.is_impersonator && magicLink"
                ngxClipboard
                [cbContent]="magicLink"
                (cbOnSuccess)="copy()"
                [attr.aria-label]="'Copier'"
                mat-menu-item>
            <mat-icon color="primary" svgIcon="person"></mat-icon>
            <span>{{ 'common.actions.adminMagicLink' | translate }}</span>
        </button>
    </ng-template>
</mat-menu>

<ng-template #copySuccess>
    {{ 'common.actions.copySuccessAttendee' | translate }}
</ng-template>
