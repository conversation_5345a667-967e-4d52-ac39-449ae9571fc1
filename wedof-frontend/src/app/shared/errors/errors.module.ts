import {NgModule} from '@angular/core';
import {RouterModule} from '@angular/router';
import {Error404Component} from './error-404/error-404.component';
import {Error500Component} from './error-500/error-500.component';
import {
    Error403AuthenticatedToAttendeeComponent
} from './error-403-authenticated-to-attendee/error-403-authenticated-to-attendee.component';

@NgModule({
    declarations: [
        Error404Component,
        Error403AuthenticatedToAttendeeComponent,
        Error500Component
    ],
    imports: [
        RouterModule
    ]
})
export class ErrorsModule {
}
