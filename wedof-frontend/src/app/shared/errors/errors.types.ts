/**
 * Represent an error returned by the Wedof API
 * Should follow rfc7807 (https://datatracker.ietf.org/doc/html/rfc7807)
 */
export interface ApiError {
    status: number;
    type: string;
    title: string;
    detail: string;
    violations?: ApiErrorViolation[]; // This part is not standard, it is used to return validation errors
    errorMessages: string[]; // Generated in the front in order to provide pre-generated messages for components
}

export interface ApiErrorViolation {
    code: string;
    propertyPath: string;
    title: string;
}
