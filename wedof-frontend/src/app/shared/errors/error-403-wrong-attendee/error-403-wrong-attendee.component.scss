@import "treo";

:host {
    display: flex;

    .content-layout {
        align-items: center;
        justify-content: center;

        .image {
            max-height: 480px;
            min-height: 0;
        }

        h1 {
            text-align: center;
        }

        h6 {
            margin: 8px 0 0 0;
            text-align: center;
        }

        .link {
            margin-top: 48px;
        }
    }
}

// -----------------------------------------------------------------------------------------------------
// @ Theming
// -----------------------------------------------------------------------------------------------------
@include treo-theme {
    $foreground: map-get($theme, foreground);

    :host {
        .content-layout {
            h6 {
                color: map-get($foreground, secondary-text);
            }
        }
    }
}
