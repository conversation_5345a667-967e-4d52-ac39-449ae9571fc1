import {Component, OnInit} from '@angular/core';
import {ActivatedRoute} from '@angular/router';

@Component({
    selector: 'error-403-wrong-attendee',
    templateUrl: './error-403-wrong-attendee.component.html',
    styleUrls: ['./error-403-wrong-attendee.component.scss'],
})
export class Error403WrongAttendeeComponent {
    redirectURL: any;

    constructor(private route: ActivatedRoute) {
        this.route.queryParams.subscribe(params => {
            this.redirectURL = params['redirectURL'] ?? '';
        });
    }
}
