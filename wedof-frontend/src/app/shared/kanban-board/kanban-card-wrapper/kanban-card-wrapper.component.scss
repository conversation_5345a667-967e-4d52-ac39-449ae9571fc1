:host {
    .mat-card {
        box-shadow: none !important;

        &.selected-card {
            background-color: #E5EDFF;
        }

        &:not(.selected-card):not(.empty-card):hover {
            cursor: pointer;
            background-color: #e2e8f0;
        }
    }

    &.kanban-card-loading .mat-card {
        background-color: #e2e8f0 !important;
        pointer-events: none;
        color: transparent;

        &::after {
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            transform: translateX(-100%);
            background-image: linear-gradient(
                    90deg,
                    rgba(255, 255, 255, 0) 0,
                    rgba(255, 255, 255, 0.2) 20%,
                    rgba(255, 255, 255, 0.5) 60%,
                    rgba(255, 255, 255, 0)
            );
            animation: shimmer 3s infinite;
            content: '';
        }
    }
}
