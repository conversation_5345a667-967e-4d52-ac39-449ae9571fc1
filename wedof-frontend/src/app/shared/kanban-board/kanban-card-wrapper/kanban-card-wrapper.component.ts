import {
    Component,
    ElementRef,
    Input,
} from '@angular/core';

@Component({
    selector: 'app-kanban-card-wrapper',
    templateUrl: './kanban-card-wrapper.component.html',
    styleUrls: ['./kanban-card-wrapper.component.scss']
})
export class KanbanCardWrapperComponent<T extends object> {

    @Input() item: T;
    @Input() isSelected: boolean;

    constructor(public elementRef: ElementRef) {
    }
}
