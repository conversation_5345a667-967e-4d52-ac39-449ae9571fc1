<div>
    <div (mouseenter)="showCheckBox = true"
         (mouseleave)="showCheckBox = false">
        <div class="flex justify-between items-center">
            <div class="flex items-center">
                <mat-checkbox
                    *ngIf="showMultipleSelection || ( showManualSelectionCard && (showCheckBox || isCardSelected) && canAddCardToSelection )"
                    [checked]="isCardSelected"
                    [disabled]="showMultipleSelection && readOnlyCheckBox "
                    [class]="showMultipleSelection && readOnlyCheckBox ? 'opacity-50' : ''"
                    (change)="checkCard($event)"
                    (click)="$event.stopPropagation()">
                </mat-checkbox>
                <p class="text-secondary">{{ item.externalId }}</p>
            </div>
            <div class="flex items-center">
                <mat-icon *ngIf="showWarningSkillSets"
                          class="icon-size-14 mr-1"
                          color="warn"
                          [svgIcon]="'notification_important'"
                          [matTooltipPosition]="'above'"
                          [matTooltip]="'private.common.certificationFolder.skillSets' | translate"></mat-icon>
                <ng-container [ngSwitch]="item?.cdcState">
                    <ng-container *ngSwitchCase="certificationFolderCdcStates.NOT_EXPORTED">
                        <mat-icon
                            *ngIf="!item.cdcExcluded && item.cdcToExport && item.state === states.SUCCESS && isCertifier"
                            class="icon-size-14 mr-1"
                            color="warn"
                            svgIcon="error"
                            [matTooltipPosition]="'above'"
                            [matTooltip]="'private.common.certificationFolder.cdcExport.maxDateForExport' | translate : {
            maxDate: maxDateForExport
        }"
                        ></mat-icon>
                        <mat-icon
                            *ngIf="!item.cdcExcluded && item.cdcToExport && item.state === states.SUCCESS && item.cdcCompliant === false"
                            class="icon-size-14 mr-1"
                            color="warn"
                            svgIcon="{{item.cdcState | certificationFolderCdcStateToIcon}}"
                            [matTooltipPosition]="'above'"
                            [matTooltip]="'private.common.certificationFolder.cdcExport.no' | translate: {
                        result : isCertifier ? 'Passeport (accrochage)' : 'Passeport de Compétences'  } "
                        ></mat-icon>
                    </ng-container>
                    <mat-icon *ngSwitchCase="certificationFolderCdcStates.EXPORTED"
                              class="icon-size-14 mr-1"
                              color="warn"
                              svgIcon="{{item.cdcState | certificationFolderCdcStateToIcon}}"
                              [matTooltipPosition]="'above'"
                              [matTooltip]="'private.common.certificationFolder.cdcExport.' + (isCertifier ? 'exported' : 'exportedPartner') | translate "
                    ></mat-icon>
                    <mat-icon *ngSwitchCase="certificationFolderCdcStates.PROCESSED_OK"
                              class="icon-size-14 mr-1"
                              svgIcon="{{item.cdcState | certificationFolderCdcStateToIcon}}"
                              [matTooltipPosition]="'above'"
                              [matTooltip]="(isCertifier ? 'private.common.certificationFolder.cdcExport.processedOkCertifier' : 'private.common.certificationFolder.cdcExport.processedOkPartner' )| translate "
                    ></mat-icon>
                    <ng-container *ngSwitchCase="certificationFolderCdcStates.PROCESSED_KO">
                        <mat-icon
                            *ngIf="isCertifier"
                            class="icon-size-14 mr-1"
                            color="warn"
                            svgIcon="{{item.cdcState | certificationFolderCdcStateToIcon}}"
                            [matTooltipPosition]="'above'"
                            [matTooltip]="'private.common.certificationFolder.cdcExport.processedKo' | translate"
                        ></mat-icon>
                    </ng-container>
                </ng-container>
                <p [matTooltip]="'private.common.certificationFolder.stateLastUpdate' | translate">{{ item.stateLastUpdate | date: 'dd/MM/yyyy' }}</p>
            </div>
        </div>
        <p class="font-bold">{{ item.attendee.firstName }} {{ item.attendee.lastName }}</p>
        <p [matTooltip]="item._links.certification.name"
           *ngIf="item._links.certification.name.length >= 35; else noTruncateTitle">
            {{ item._links.certification.externalId }} - {{ item._links.certification.name.slice(0, 25) + ' ...' }}
        </p>
    </div>
    <div class="mt-1" *ngIf="skillSets?.length">
        <app-tag *ngFor="let skillSet of skillSets" color="#e0e7ff"
                 class="text-sm font-semibold mr-1">
            {{skillSet.name}}
        </app-tag>
    </div>
    <ng-template #noTruncateTitle>
        <p>{{ item._links.certification.externalId }} - {{ item._links.certification.name }}</p>
    </ng-template>
    <p *ngIf="item.examinationDate"
       [matTooltip]="'private.common.certificationFolder.examinationDates' | translate">
        <ng-container
            *ngIf="!item.examinationEndDate || item.examinationDate === item.examinationEndDate else noSameDay">
            {{
            'private.common.dates.fromToSameDay' | translate: {
                date: item.examinationDate | date : 'dd/MM/yyyy'
            }
            }}
        </ng-container>
        <ng-template #noSameDay>
            {{
            'private.common.dates.fromTo' | translate : {
                startDate: item.examinationDate | date : 'dd/MM/yyyy',
                endDate: item.examinationEndDate | date : 'dd/MM/yyyy'
            }
            }}
        </ng-template>
    </p>
</div>

<div class="flex justify-between items-center">
    <div *ngIf="item.tags?.length">
        <app-form-field-static [lengthLimit]="2"
                               [value]="item.tags"
                               (customClick)="setSearchTag($event)"
                               type="tags"></app-form-field-static>
    </div>
    <app-certification-folder-menu class="ml-auto -mr-4 card-loading-hidden"
                                   [disabled]="isCardSelected"
                                   (deleteFolder)="delete()"
                                   (processedFolder)="setRefresh($event)"
                                   [folders]="[item]"></app-certification-folder-menu>
</div>



