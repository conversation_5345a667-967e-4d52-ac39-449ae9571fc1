import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {
    CertificationFolder,
    CertificationFolderCdcStates,
    CertificationFolderStates
} from '../../api/models/certification-folder';
import moment from 'moment/moment';
import {MatCheckboxChange} from '@angular/material/checkbox';

@Component({
    selector: 'app-certification-folder-kanban-card',
    templateUrl: './certification-folder-kanban-card.component.html',
    styleUrls: ['./certification-folder-kanban-card.component.scss']
})
export class CertificationFolderKanbanCardComponent implements OnInit {

    maxDateForExport: string;
    states = CertificationFolderStates;
    certificationFolderCdcStates = CertificationFolderCdcStates;
    skillSets = [];
    showWarningSkillSets = false;
    showCheckBox = false;

    @Input() item: CertificationFolder;
    @Input() isCertifier: boolean;
    @Input() isCardSelected: boolean;
    @Input() showMultipleSelection: boolean;
    @Input() showManualSelectionCard: boolean;
    @Input() canAddCardToSelection: boolean;
    @Input() readOnlyCheckBox: boolean;
    @Output() deleteFolder = new EventEmitter<any>();
    @Output() processedFolder: EventEmitter<CertificationFolder> = new EventEmitter<CertificationFolder>();
    @Output() searchTag = new EventEmitter<string>();
    @Output() addCardToSelection = new EventEmitter<{ item: CertificationFolder, isManual: boolean }>();
    @Output() removeCardFromSelection = new EventEmitter<CertificationFolder>();

    constructor() {
    }

    ngOnInit(): void {
        this.maxDateForExport = this.item.state === CertificationFolderStates.SUCCESS && moment(this.item.issueDate).add(3, 'months').format('DD/MM/YYYY');
        if (this.item.skillSets.length) {
            this.item.skillSets.forEach((skillSet) => {
                const order = Number(skillSet.order) <= 9 ? ('0' + skillSet.order).slice(-2) : skillSet.order;
                this.skillSets.push({name: 'BC' + order});
            });
        } else {
            this.showWarningSkillSets = this.isCertifier && this.item._links.certification.allowPartialSkillSets;
        }
    }

    setRefresh(certificationFolderUpdated: CertificationFolder): void {
        this.item = certificationFolderUpdated;
        this.processedFolder.emit(certificationFolderUpdated);
    }

    setSearchTag(tag: string): void {
        this.searchTag.emit(tag);
    }

    delete(): void {
        this.deleteFolder.emit(this.item);
    }

    checkCard(event: MatCheckboxChange): void {
        const isManualSelection = this.showManualSelectionCard && (this.showCheckBox || this.isCardSelected) && this.canAddCardToSelection;
        if (event.checked) {
            this.addCardToSelection.emit({item : this.item, isManual : isManualSelection});
        } else {
            this.removeCardFromSelection.emit(this.item);
        }
    }
}
