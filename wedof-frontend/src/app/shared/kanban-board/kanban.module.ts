import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {TranslateModule} from '@ngx-translate/core';
import {MarkdownModule} from 'ngx-markdown';
import {MaterialModule} from '../material/material.module';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {TreoMessageModule} from '../../../@treo/components/message';
import {DndModule} from '../directives/DndModule';
import {FileModule} from '../file/file.module';
import {MatTooltipModule} from '@angular/material/tooltip';
import {PipesModule} from '../pipes/pipes.module';
import {MatCardModule} from '@angular/material/card';
import {NgxMatSelectSearchModule} from 'ngx-mat-select-search';
import {MatSelectInfiniteScrollModule} from 'ng-mat-select-infinite-scroll';
import {KanbanBoardComponent} from './kanban-board/kanban-board.component';
import {RegistrationFolderModule} from '../registration-folder/registration-folder.module';
import {RegistrationFolderKanbanCardComponent} from './registration-folder-kanban-card/registration-folder-kanban-card.component';
import {InfiniteScrollModule} from 'ngx-infinite-scroll';
import {ProposalKanbanCardComponent} from './proposal-kanban-card/proposal-kanban-card.component';
import {ProposalModule} from '../proposal/proposal.module';
import {RouterModule} from '@angular/router';
import {MatProgressBarModule} from '@angular/material/progress-bar';
import {KanbanCardWrapperComponent} from './kanban-card-wrapper/kanban-card-wrapper.component';
import {CertificationFolderKanbanCardComponent} from './certification-folder-kanban-card/certification-folder-kanban-card.component';
import {CertificationFolderModule} from '../certification-folder/certification-folder.module';
import {MatTabsModule} from '@angular/material/tabs';
import {StringModule} from '../utils/string.module';


@NgModule({
    declarations: [
        KanbanBoardComponent,
        RegistrationFolderKanbanCardComponent,
        ProposalKanbanCardComponent,
        KanbanCardWrapperComponent,
        CertificationFolderKanbanCardComponent
    ],
    imports: [
        CommonModule,
        MaterialModule,
        MarkdownModule,
        TranslateModule,
        ReactiveFormsModule,
        TreoMessageModule,
        DndModule,
        FileModule,
        MatCardModule,
        PipesModule,
        MatTooltipModule,
        NgxMatSelectSearchModule,
        MatSelectInfiniteScrollModule,
        RegistrationFolderModule,
        CertificationFolderModule,
        InfiniteScrollModule,
        MatProgressBarModule,
        ProposalModule,
        RouterModule,
        FormsModule,
        MatTabsModule,
        StringModule
    ],
    exports: [
        KanbanBoardComponent,
        RegistrationFolderKanbanCardComponent,
        ProposalKanbanCardComponent,
        CertificationFolderKanbanCardComponent
    ]
})
export class KanbanModule {
}
