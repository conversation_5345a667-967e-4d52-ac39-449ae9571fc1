import {
    Component,
    ContentChild,
    EventEmitter,
    Input,
    OnDestroy,
    OnInit,
    Output,
    QueryList,
    TemplateRef,
    ViewChildren
} from '@angular/core';
import {Observable, Subject} from 'rxjs';
import {KanbanCardWrapperComponent} from '../kanban-card-wrapper/kanban-card-wrapper.component';
import {debounceTime, distinctUntilChanged, takeUntil} from 'rxjs/operators';
import {PaginatedResponse} from '../../api/services/abstract-paginated.service';
import {isArray, isEqual} from 'lodash-es';
import {MatCheckboxChange} from '@angular/material/checkbox';

export interface KanbanColumnResponse<T> {
    columns: KanbanColumnData<T>[];
}

export interface KanbanAdditionalDataResponse {
    columnId: string;
    fieldName: string;
    newValue: any;
}

export type KanbanColumnConfigsResponse = KanbanColumnConfig[];

interface KanbanColumnData<T> {
    columnId: string;
    items: T[];
    total: number;
}

interface KanbanColumnConfig {
    columnId: string;
    filter: object;
    title: string;
    parentColumnId?: string;
}

interface KanbanColumnSlot {
    activeColumnId: string;
    isExpanded: boolean;
    availableColumnIds: string[];
    isLoading: boolean;
}

@Component({
    selector: 'app-kanban-board',
    templateUrl: './kanban-board.component.html',
    styleUrls: ['./kanban-board.component.scss']
})
export class KanbanBoardComponent<T extends object> implements OnInit, OnDestroy {

    filters: any;
    isLoading = true;
    loadingMenu = false;
    maximumDisplayedChildColumns = 3;

    columnConfigs: KanbanColumnConfig[];
    columnSlots: KanbanColumnSlot[];
    columnDataByColumnId: { [columnId: string]: KanbanColumnData<T> } = {};

    private _unsubscribeAll: Subject<void> = new Subject();

    @Input() showMultipleSelection = false;
    @Input() canSelectCardsManually = false;
    @Input() allChecked = false;
    @Input() selectedItem: T = null;
    @Input() itemsToLoadPerColumn = 10;
    @Input() comparisonProperty: string;
    @Input() columnSelected?: string;
    @Input() filters$: Observable<object>;
    @Input() titleNoData: string;
    @Input() selectedCards?: T[];
    @Input() listColumnConfigs: () => Observable<KanbanColumnConfigsResponse>;
    @Input() listAllItemsByColumn: (columnIds: string[], params: object) => Observable<KanbanColumnResponse<T>>;
    @Input() listItemsForColumn: (params: object) => Observable<PaginatedResponse<T>>;
    @Input() getAdditionalDataForColumn?: (columnId: string, params: object) => Observable<KanbanAdditionalDataResponse>;

    @Output() getActiveFilters = new EventEmitter<{ query?: string, [param: string]: string | string[] | boolean }>();
    @Output() openCard = new EventEmitter<{ item: T; keepShortcutSide: boolean }>();
    @Output() checkedColumn = new EventEmitter<KanbanColumnData<T> | null>();
    @Output() clearSelection = new EventEmitter<void>();
    @Output() folderCount = new EventEmitter<number>();

    @ViewChildren('kanbanCardWrapperComponents') kanbanCardWrapperComponents: QueryList<KanbanCardWrapperComponent<T>>;
    @ContentChild('kanbanCardTemplate') kanbanCardTemplate: TemplateRef<any>;
    @ContentChild('kanbanColumnFooterTemplate') kanbanColumnFooterTemplate: TemplateRef<any>;
    @ContentChild('kanbanMenuTemplate') kanbanMenuTemplate: TemplateRef<any>;

    constructor() {
    }

    ngOnInit(): void {
        this.listColumnConfigs().subscribe(columnConfigs => {
            this.columnConfigs = columnConfigs;
            this.initColumnSlots(columnConfigs);
            this.filters$
                .pipe(
                    distinctUntilChanged(),
                    debounceTime(500),
                    takeUntil(this._unsubscribeAll)
                )
                .subscribe(filters => {
                    const params = {limit: this.itemsToLoadPerColumn};
                    if (filters) {
                        this.clearSelection.emit();
                        Object.entries(filters).forEach(([paramTitle]) => {
                            if (filters[paramTitle]) {
                                params[paramTitle] = filters[paramTitle];
                            }
                        });
                    }
                    this.filters = params;
                    this.getActiveFilters.emit(this.filters);
                    this.initKanban(params);
                });
        });
    }

    private initColumnSlots(columnConfigs: KanbanColumnConfig[]): void {
        this.columnSlots = [];
        columnConfigs.forEach(columnConfig => {
            if (columnConfig.parentColumnId) {
                const parentColumnSlot = this.getColumnSlot(columnConfig.parentColumnId);
                parentColumnSlot.availableColumnIds.push(columnConfig.columnId);
            } else {
                this.columnSlots.push({
                    activeColumnId: columnConfig.columnId,
                    isExpanded: true, // Looks nicer on first loading
                    availableColumnIds: [columnConfig.columnId],
                    isLoading: false
                });
            }
        });
    }

    private initKanban(params: object): void {
        this.isLoading = true;
        // Load only active columns to save performances
        // TODO improve by using a config param to select column to display at load
        // const columnIds = this.columnSlots.map(columnSlot => columnSlot.activeColumnId === 'serviceDoneValidated' ? 'serviceDoneValidatedtoBill' : columnSlot.activeColumnId);
        const columnIds = this.columnSlots.map(columnSlot => columnSlot.activeColumnId);
        this.listAllItemsByColumn(columnIds, params)
            .pipe(takeUntil(this._unsubscribeAll))
            .subscribe((response) => {
                response.columns.forEach(columnData => {
                    // if (columnData.columnId === 'serviceDoneValidatedtoBill') {
                    //     this.columnSlots.find(columnSlot => columnSlot.activeColumnId === 'serviceDoneValidated').activeColumnId = 'serviceDoneValidatedtoBill';
                    // }
                    this.columnDataByColumnId[columnData.columnId] = columnData;
                    this.automaticColumnSlotToggle(columnData.columnId);
                });
                this.isLoading = false;
                this.getKanbanTotal();
            });
    }

    onScroll(columnId: string): void {
        const columnData = this.columnDataByColumnId[columnId];
        const displayedPages = columnData.items.length / this.itemsToLoadPerColumn;
        if (Number.isInteger(displayedPages)) { // ex: 4 pages maybe there is more 4.5 pages => no more
            this.listItemsForColumn({
                ...this.filters,
                columnId: columnId,
                limit: this.itemsToLoadPerColumn,
                page: displayedPages + 1
            })
                .pipe(takeUntil(this._unsubscribeAll))
                .subscribe((response) => {
                    this.columnDataByColumnId[columnId].items.push(...response.payload);
                    if (this.allChecked || this.columnSelected === columnId) {
                        this.selectedCards.push(...response.payload);
                    }
                });
        }
    }

    refreshColumnAdditionalData(columnId: string): void {
        if (this.getAdditionalDataForColumn) {
            this.getAdditionalDataForColumn(columnId, this.filters).subscribe(kanbanAdditionalDataResponse => {
                const columnData = this.columnDataByColumnId[kanbanAdditionalDataResponse.columnId];
                if (columnData) {
                    columnData[kanbanAdditionalDataResponse.fieldName] = kanbanAdditionalDataResponse.newValue;
                }
            });
        }
    }

    toggleColumnSlot(columnSlot: KanbanColumnSlot, $event = null): void {
        columnSlot.isExpanded = !columnSlot.isExpanded;
        if ($event) {
            $event.stopPropagation();
        }
    }

    hasPrevious(item?: T): boolean {
        if (!item) {
            return false;
        }
        const columnData = this.findCurrentColumnData(item);
        if (!columnData) {
            return false;
        }
        const itemIndex = this.getCurrentItemIndex(columnData, item);
        return itemIndex > 0;
    }

    hasNext(item?: T): boolean {
        if (!item) {
            return false;
        }
        const columnData = this.findCurrentColumnData(item);
        if (!columnData) {
            return false;
        }
        const itemIndex = this.getCurrentItemIndex(columnData, item);
        return itemIndex >= 0 && itemIndex < columnData?.items.length - 1;
    }

    selectCard(item: T, keepShortcutSide: boolean = false): void {
        // Open card will change the selectedItem input from the outside
        this.openCard.emit({
            item: item,
            keepShortcutSide
        });
        const kanbanCardWrapperComponent = this.kanbanCardWrapperComponents.find(currentCard => currentCard.item[this.comparisonProperty] === item[this.comparisonProperty]);
        if (kanbanCardWrapperComponent) {
            // Native scroll seems to interfere with EventEmitter : if EventEmitter emits a new event then scroll does not work unless in setTimeout
            setTimeout(() => {
                kanbanCardWrapperComponent.elementRef.nativeElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start',
                    inline: 'nearest'
                });
            });
        }
    }

    selectNextCard(item: T): void {
        const columnData = this.findCurrentColumnData(item);
        this.selectedItem = columnData.items[this.getCurrentItemIndex(columnData, item) + 1];
        this.selectCard(this.selectedItem, true);
    }

    selectPreviousCard(item: T): void {
        const columnData = this.findCurrentColumnData(item);
        this.selectedItem = columnData.items[this.getCurrentItemIndex(columnData, item) - 1];
        this.selectCard(this.selectedItem, true);
    }

    refreshCard(item: T, openSide = true): void {
        const oldColumnData = this.findCurrentColumnData(item);
        if (oldColumnData) {
            const newColumnData = this.findMatchingColumnData(item);
            if (oldColumnData.columnId === newColumnData.columnId) {
                oldColumnData.items[this.getCurrentItemIndex(oldColumnData, item)] = item;
            } else {
                this.removeCard(item);
                this.insertCard(item, openSide);
            }
        } else {
            this.insertCard(item, openSide);
        }
        if (openSide && this.selectedItem && this.isItemEquals(this.selectedItem, item)) {
            this.selectCard(item);
        }
    }

    insertCard(item: T, openSide = true): void {
        const columnData = this.findMatchingColumnData(item);
        if (columnData) {
            columnData.items.unshift(item); // It is assumed that the new card should be on top (= most recently changed)
            columnData.total++;
            if (openSide) {
                this.selectCard(item);
            }
            this.refreshColumnAdditionalData(columnData.columnId);
            this.automaticColumnSlotToggle(columnData.columnId);
        }
    }

    removeCard(item: T): void {
        const columnData = this.findCurrentColumnData(item);
        if (columnData) {
            columnData.total--;
            columnData.items.splice(this.getCurrentItemIndex(columnData, item), 1);
            this.refreshColumnAdditionalData(columnData.columnId);
            this.automaticColumnSlotToggle(columnData.columnId);
        }
    }

    automaticColumnSlotToggle(columnId: string): void {
        const columnData = this.columnDataByColumnId[columnId];
        const columnSlot = this.getColumnSlot(columnData.columnId);
        if (columnSlot && (!columnSlot.isExpanded && columnData.total > 0 || columnSlot.isExpanded && columnData.total < 1)) {
            this.toggleColumnSlot(columnSlot);
        }
    }

    isItemEquals(item: T, currentItem: T): boolean {
        return currentItem[this.comparisonProperty] === item[this.comparisonProperty];
    }

    getColumnTitle(columnId: string): string {
        return this.columnConfigs.find(columnConfig => columnConfig.columnId === columnId).title;
    }

    getColumnTotal(columnId: string): number {
        return this.columnDataByColumnId[columnId]?.total ?? 0;
    }

    getKanbanTotal(): void {
        let total = 0;
        Object.values(this.columnDataByColumnId).forEach((columnData) => {
            total += columnData.total ?? 0;
        });
        return this.folderCount.emit(total);
    }

    isActiveColumnInSlot(columnSlot: KanbanColumnSlot, availableColumnId: string): boolean {
        return columnSlot.activeColumnId === availableColumnId;
    }

    setActiveColumnInSlot(columnSlot: KanbanColumnSlot, columnId: string): void {
        delete this.columnDataByColumnId[columnSlot.activeColumnId];
        columnSlot.activeColumnId = columnId;
        columnSlot.isLoading = true;
        this.listItemsForColumn({
            ...this.filters,
            columnId: columnId,
            limit: this.itemsToLoadPerColumn,
            page: 1
        })
            .pipe(takeUntil(this._unsubscribeAll))
            .subscribe((response) => {
                this.columnDataByColumnId[columnId] = {
                    columnId: columnId,
                    items: response.payload,
                    total: response.total
                };
                columnSlot.isLoading = false;
                this.refreshColumnAdditionalData(columnId);
            });
    }

    private getColumnSlot(columnId: string): KanbanColumnSlot {
        return this.columnSlots.find(columnSlot => columnSlot.activeColumnId === columnId);
    }

    private getCurrentItemIndex(columnData: KanbanColumnData<T>, item: T): number {
        return columnData.items.findIndex(currentItem => this.isItemEquals(item, currentItem));
    }

    // Returns the columnData the card is currently in
    private findCurrentColumnData(item: T): KanbanColumnData<T> {
        return Object.values(this.columnDataByColumnId).find(columnData => {
            return this.getCurrentItemIndex(columnData, item) >= 0;
        });
    }

    // Returns the columnData where the item "fits" according to its values
    private findMatchingColumnData(item: T): KanbanColumnData<T> {
        const matchingColumnConfig = this.columnConfigs.find(columnConfig => {
            // TODO KANBAN : maybe extract + support more advanced filters
            return Object.entries(columnConfig.filter).every(([filterName, filterValue]) => {
                const itemFieldValue = item[filterName];
                if (isArray(filterValue) && !isArray(itemFieldValue)) {
                    return filterValue.includes(itemFieldValue);
                } else {
                    return isEqual(itemFieldValue, filterValue);
                }
            });
        });
        return this.columnDataByColumnId[matchingColumnConfig.columnId];
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    selectColumn(activeColumnId: string, event: MatCheckboxChange): void {
        if (event.checked === true) {
            const columnDataForActiveColumn = this.columnDataByColumnId[activeColumnId];
            this.checkedColumn.emit(columnDataForActiveColumn);
        } else {
            this.checkedColumn.emit(null);
        }
    }

    isCardSelected(item: T): boolean {
        return this.selectedCards.some((card) => card[this.comparisonProperty] === item[this.comparisonProperty]);
    }

    addCardToSelection(item: T): void {
        this.selectedCards.push(item);
    }

    removeCardFromSelection(item: T): void {
        const index = this.selectedCards.findIndex((card) => card[this.comparisonProperty] === item[this.comparisonProperty]);
        this.selectedCards.splice(index, 1);
    }
}
