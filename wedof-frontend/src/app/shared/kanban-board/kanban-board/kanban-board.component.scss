:host {
    ::ng-deep {
        app-table-multiple-filter {
            height: auto !important;
        }

        app-wrapper-spinner {
            display: flex;
            flex-direction: column;
            height: 100%;
            overflow: hidden;
        }

        .mat-spinner {
            margin-left: -200px; //2x width
        }
    }
}

.min-height-col {
    min-height: 4.2rem;
}

.mat-tab-link {
    min-width: 0;
    height: 1.8rem;
    padding: 0 0;
}

.child-column-active {
    border-bottom: 2px solid #5850ec;

    app-ellipsis-text, p {
        margin-bottom: -2px;
    }
}

.kanban {
    display: flex;
    flex: 1 1 auto;
    overflow-y: auto;
    overflow-x: hidden;
    flex-direction: row;
    background-color: #ffffff;
    min-height: 346px; //mandatory
}

.kanban-column {
    display: flex;
    flex-direction: column;

    &.not-expanded {
        cursor: pointer;

        .rotate-kanban-column-header {
            width: 46px; //mandatory
        }

        .rotate-kanban-column-header > .header-rotate {
            -webkit-transform: rotate(-90deg) translate(-100%, 10px); /* <PERSON><PERSON> and Chrome */
            -moz-transform: rotate(-90deg) translate(-100%, 10px); /* Firefox */
            -ms-transform: rotate(-90deg) translate(-100%, 10px); /* IE 9 */
            -o-transform: rotate(-90deg) translate(-100%, 10px); /* Opera */
            transform: rotate(-90deg) translate(-100%, 10px);
            width: calc(100vh - 46px);
            transform-origin: top left;
            text-align: right;
            margin-bottom: -100%;
            max-width: 300px; //mandatory
        }
    }
}

.kanban-column-list {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow-y: auto;
    margin: 0;
    padding: 0 15px 0;
    height: 10px;
    background-color: #f1f5f9;
}

.end-scroll {
    border-top-color: #e2e8f0;
}
