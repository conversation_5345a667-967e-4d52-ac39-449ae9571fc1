<app-wrapper-spinner [active]="isLoading" loadingClasses="h-full w-screen">
    <div class="kanban h-full">
        <div *ngFor="let columnSlot of columnSlots"
             class="kanban-column border-t border-r"
             (click)="!columnSlot.isExpanded && toggleColumnSlot(columnSlot, $event)"
             [ngClass]="{'px-3': !columnSlot.isExpanded, 'not-expanded': !columnSlot.isExpanded}">

            <div *ngIf="!columnSlot.isExpanded; else expandedColumn"
                 class="rotate-kanban-column-header font-semibold">
                <div class="text-center text-indigo py-3">
                    {{ columnSlot.isLoading ? '-' : getColumnTotal(columnSlot.activeColumnId) }}
                </div>
                <div
                    class="header-rotate">{{ (getColumnTitle(columnSlot.availableColumnIds[0]) | translate) + (columnSlot.activeColumnId !== columnSlot.availableColumnIds[0] ? ' - ' + getColumnTitle(columnSlot.activeColumnId) : '') }}
                </div>
            </div>

            <ng-template #expandedColumn>
                <div
                    [class]="columnSlot.availableColumnIds.length > 1 ? 'pt-3 pb-1 px-2' : 'pt-3 pb-3 px-2 border-b min-height-col'">
                    <div class="flex justify-between items-center m-auto cursor-pointer font-semibold pl-2 pr-1"
                         (click)="toggleColumnSlot(columnSlot, $event)">
                        <div class="flex items-center">
                            <div *ngIf="showMultipleSelection" class="cursor-pointer">
                                <mat-checkbox
                                    *ngIf="!isLoading && getColumnTotal(columnSlot.activeColumnId) > 0"
                                    [disabled]="(columnSelected && (columnSelected !== columnSlot.activeColumnId)) || allChecked || loadingMenu || (selectedCards.length >= 1 && selectedCards[0].state !== columnSlot.activeColumnId)"
                                    [checked]="(columnSelected === columnSlot.activeColumnId && selectedCards?.length > 0 && !allChecked) || allChecked"
                                    (change)="selectColumn(columnSlot.activeColumnId, $event)"
                                    [class]="(columnSelected && (columnSelected !== columnSlot.activeColumnId)) || allChecked || loadingMenu || (selectedCards.length >= 1 && selectedCards[0].state !== columnSlot.activeColumnId) ? 'opacity-50' : ''"
                                    (click)="$event.stopPropagation()">
                                </mat-checkbox>
                            </div>
                            {{ getColumnTitle(columnSlot.availableColumnIds[0]) | translate }}
                        </div>
                        <div class="flex items-center">
                            <div class="text-indigo pr-1">
                                <ng-container *ngIf="columnSlot.isLoading; else noLoading">
                                    -
                                </ng-container>
                                <ng-template #noLoading>
                                    <ng-container
                                        *ngIf="canSelectCardsManually && selectedCards.length && selectedCards[0].state === columnSlot.activeColumnId else showCount">
                                        {{ selectedCards.length }} / {{getColumnTotal(columnSlot.activeColumnId)}}
                                    </ng-container>
                                    <ng-template #showCount>
                                        {{getColumnTotal(columnSlot.activeColumnId)}}
                                    </ng-template>
                                </ng-template>
                            </div>
                            <div *ngIf="showMultipleSelection">
                                <ng-template [ngTemplateOutlet]="kanbanMenuTemplate"
                                             [ngTemplateOutletContext]="{
                                                selectedCards: selectedCards,
                                                disabled : (columnSelected !== columnSlot.activeColumnId && !canSelectCardsManually)
                                                || (canSelectCardsManually && selectedCards.length && selectedCards[0].state !== columnSlot.activeColumnId )
                                             }"></ng-template>
                            </div>
                        </div>
                    </div>
                </div>
                <div *ngIf="columnSlot.availableColumnIds.length > 1">
                    <nav mat-tab-nav-bar>
                        <ng-container *ngFor="let availableColumnId of columnSlot.availableColumnIds; let i = index">
                            <a *ngIf="i < maximumDisplayedChildColumns"
                               mat-tab-link
                               class="flex-grow text-sm"
                               (click)="setActiveColumnInSlot(columnSlot, availableColumnId)"
                               [ngClass]="{'child-column-active': isActiveColumnInSlot(columnSlot, availableColumnId)}">
                                <p *ngIf="i === 0; else regularColumnTitle">Tout</p>
                                <ng-template #regularColumnTitle>
                                    <app-ellipsis-text [text]="getColumnTitle(availableColumnId)"
                                                       [maxLength]="15"></app-ellipsis-text>
                                </ng-template>
                            </a>
                        </ng-container>
                        <a *ngIf="columnSlot.availableColumnIds.length > maximumDisplayedChildColumns"
                           mat-tab-link
                           class="flex-grow-0 px-3 text-sm"
                           [matMenuTriggerFor]="hiddenChildColumns">
                            <mat-icon class="icon" svgIcon="arrow_drop_down"></mat-icon>
                        </a>
                        <mat-menu #hiddenChildColumns="matMenu">
                            <ng-template matMenuContent>
                                <div class="min-w-50 text-sm"
                                     *ngFor="let availableColumnId of columnSlot.availableColumnIds | slice:maximumDisplayedChildColumns ; let i = index">
                                    <button type="button"
                                            mat-menu-item>
                                        <a mat-tab-link
                                           (click)="setActiveColumnInSlot(columnSlot, availableColumnId)"
                                           [ngClass]="{'child-column-active': isActiveColumnInSlot(columnSlot, availableColumnId)}">
                                            {{ getColumnTitle(availableColumnId) }}
                                        </a>
                                    </button>
                                </div>
                            </ng-template>
                        </mat-menu>
                    </nav>
                </div>
                <div class="kanban-column-list p-3"
                     [ngClass]="{'kanban-column-loading': columnSlot.isLoading}"
                     infiniteScroll
                     [infiniteScrollDistance]="2"
                     [infiniteScrollThrottle]="50"
                     (scrolled)="onScroll(columnSlot.activeColumnId)"
                     [scrollWindow]="false">
                    <div *ngIf="getColumnTotal(columnSlot.activeColumnId) > 0; else noFolder">
                        <app-kanban-card-wrapper #kanbanCardWrapperComponents
                                                 *ngFor="let item of columnDataByColumnId[columnSlot.activeColumnId]?.items"
                                                 [item]="item"
                                                 [isSelected]="selectedItem != null && isItemEquals(selectedItem, item)"
                                                 (click)="selectCard(item)">
                            <ng-template [ngTemplateOutlet]="kanbanCardTemplate"
                                         [ngTemplateOutletContext]="{$implicit: item}"></ng-template>
                        </app-kanban-card-wrapper>
                        <mat-divider
                            *ngIf="columnDataByColumnId[columnSlot.activeColumnId]?.items.length === getColumnTotal(columnSlot.activeColumnId)"
                            class="pb-3 end-scroll"></mat-divider>
                    </div>
                    <ng-template #noFolder>
                        <app-kanban-card-wrapper class="self-center"
                                                 [ngClass]="{'kanban-card-loading': columnSlot.isLoading}">
                            <ng-container *ngIf="isLoading || columnSlot.isLoading; else noData"
                                          class="items-center justify-center">
                                -
                            </ng-container>
                            <ng-template #noData>
                                {{ titleNoData }}
                            </ng-template>
                        </app-kanban-card-wrapper>
                    </ng-template>
                </div>
            </ng-template>

            <ng-template [ngTemplateOutlet]="kanbanColumnFooterTemplate"
                         [ngTemplateOutletContext]="{column: columnDataByColumnId[columnSlot.activeColumnId], isExpanded: columnSlot.isExpanded, isKanbanLoading: isLoading || columnSlot.isLoading}"></ng-template>
        </div>
    </div>
</app-wrapper-spinner>
