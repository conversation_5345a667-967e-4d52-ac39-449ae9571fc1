import {Component, EventEmitter, Input, Output} from '@angular/core';
import {DiscountType, Proposal} from '../../api/models/proposal';
import {TrainingAction} from '../../api/models/training-action';

@Component({
    selector: 'app-proposal-kanban-card',
    templateUrl: './proposal-kanban-card.component.html',
    styleUrls: ['./proposal-kanban-card.component.scss']
})
export class ProposalKanbanCardComponent {

    discountType = DiscountType;

    @Input() item: Proposal;
    @Output() processedProposal: EventEmitter<Proposal> = new EventEmitter<Proposal>();
    @Output() deleteProposal = new EventEmitter<any>();
    @Output() searchTag = new EventEmitter<string>();

    setRefresh(proposalUpdated: Proposal): void {
        this.item = proposalUpdated;
        this.processedProposal.emit(proposalUpdated);
    }

    setSearchTag(tag: string): void {
        this.searchTag.emit(tag);
    }

    delete(proposal: Proposal): void {
        this.deleteProposal.emit(proposal);
    }

    showMore(trainingActions: TrainingAction[]): string {
        let toolTipTrainingActions = '';
        for (let i = 0; i < trainingActions.length; i++) {
            toolTipTrainingActions += trainingActions[i].trainingTitle;
            toolTipTrainingActions += i === trainingActions.length - 1 ? '. ' : ', ';
        }
        return toolTipTrainingActions;
    }

}
