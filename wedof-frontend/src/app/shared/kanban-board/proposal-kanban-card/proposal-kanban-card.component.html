<div>
    <div class="flex justify-between items-center">
        <p class="text-secondary" *ngIf="item.codeRequired">{{item.code}}</p>
        <p class="text-secondary"
           *ngIf="!item.codeRequired">{{'private.training-organism.proposals.proposalCard.codeRequired.value'|translate}}</p>
        <div class="flex items-center">
            <mat-icon class="mr-1 icon-size-14"
                      [matTooltip]="'private.training-organism.proposals.table.validation.auto' | translate"
                      *ngIf="item.autoValidate" svgIcon="sync"></mat-icon>
            <mat-icon color="warn" class="mr-1 icon-size-14"
                      [matTooltip]="('private.training-organism.proposals.table.proposal.expire' | translate ) + (item.expire | date:'d/MM/yy')"
                      *ngIf="item.expire" svgIcon="event_busy"></mat-icon>
            <p [matTooltip]="'private.training-organism.proposals.proposalCard.stateLastUpdate' | translate">{{item.stateLastUpdate | date: 'dd/MM/yyyy'}}</p>
        </div>
    </div>

    <p class="font-bold"
       *ngIf="item.firstName && item.lastName; else showEmail">{{ item.firstName | titlecase }} {{ item.lastName | uppercase }}</p>
    <ng-template #showEmail>
        <p class="font-bold"> {{ item.email | titlecase }}</p>
    </ng-template>

    <a *ngIf="item._links['registrationFolder']"
       [routerLink]="['/formation/dossiers/liste/' + item._links['registrationFolder']['href'].split('registrationFolder/')[1]]"
       href="/formation/dossiers/liste/{{ item._links['registrationFolder']['href'].split('registrationFolder/')[1]}}">
        {{ item._links['registrationFolder']['href'].split('registrationFolder/')[1]}}
    </a>

    <div
        *ngIf="item.trainingActions.length > 0 else noTrainingActions;">
        <span
            *ngIf="item.trainingActions[0].trainingTitle.length >= 35">{{item.trainingActions[0].trainingTitle.slice(0, 30) + ' ...'}}</span>
        <span *ngIf="item.trainingActions[0].trainingTitle.length < 35">{{item.trainingActions[0].trainingTitle}}</span>
        <ng-container *ngIf="item.trainingActions.length > 1;">
            <p class="underline" [matTooltip]="showMore(item.trainingActions)">
                et {{item.trainingActions.length - 1}} de plus </p>
        </ng-container>
    </div>
    <ng-template #noTrainingActions>
        <p>{{ 'private.training-organism.proposals.table.trainingActions.noTrainingActions' | translate }}</p>
    </ng-template>

    <p *ngIf="item.discountType === discountType.DISCOUNT_TYPE_PRICE">{{item.amount + ' €'  }}</p>
    <p *ngIf="item.discountType === discountType.DISCOUNT_TYPE_PERCENT"> {{((item.amount > 0 ? 'private.training-organism.proposals.form.fields.discountType.options.percent.increase' : 'private.training-organism.proposals.form.fields.discountType.options.percent.decrease') | translate) + item.amount + ' %'  }}</p>
    <p *ngIf="item.discountType === discountType.DISCOUNT_TYPE_AMOUNT"> {{((item.amount > 0 ? 'private.training-organism.proposals.form.fields.discountType.options.percent.increase' : 'private.training-organism.proposals.form.fields.discountType.options.percent.decrease') | translate) + item.amount + ' €'  }}</p>
</div>

<div class="flex justify-between items-center">
    <div *ngIf="item.tags.length">
        <app-form-field-static [lengthLimit]="2"
                               [value]="item.tags"
                               (customClick)="setSearchTag($event)"
                               type="tags"></app-form-field-static>
    </div>
    <app-proposal-menu class="ml-auto -mr-4 card-loading-hidden" [proposals]="[item]"
                       (processedProposal)="setRefresh($event)"
                       (deletedProposal)="delete($event)"
    ></app-proposal-menu>
</div>
