<div>
    <div class="flex justify-between items-center">
        <p class="text-secondary">{{ item | displayRegistrationFolderExternalId }}</p>
        <div class="flex items-center">
            <mat-icon *ngIf="item.controlState && item.controlState === controlStates.IN_CONTROL"
                      [matTooltip]="'private.common.registrationFolder.controlState.tooltip.inControl' | translate"
                      class="icon-size-14 mr-1"
                      color="{{controlStates.IN_CONTROL | registrationFolderControlStateToColor}}"
                      svgIcon="{{controlStates.IN_CONTROL | registrationFolderControlStateToIcon}}">
            </mat-icon>
            <mat-icon *ngIf="item.state === states.SERVICE_DONE_VALIDATED"
                      [matTooltip]="'private.training-organism.folders.common.billingState.' + item.billingState | translate"
                      class="icon-size-14 mr-1"
                      [color]="item.billingState === billingStates.TO_BILL ? 'warn' : undefined ">
                {{ item.billingState | registrationFolderBillingStateToIcon }}
            </mat-icon>
            <p [matTooltip]="'private.common.registrationFolder.lastUpdate' | translate">{{ item.lastUpdate | date: 'dd/MM/yyyy' }}</p>
        </div>
    </div>
    <p class="font-bold">{{ item.attendee.firstName }} {{ item.attendee.lastName }}</p>
    <p *ngIf="item.trainingActionInfo.title.length >= 35; else noTruncateTitle">{{ item.trainingActionInfo.title.slice(0, 34) + ' ...' }}</p>
    <ng-template #noTruncateTitle>
        <p>{{ item.trainingActionInfo.title }}</p>
    </ng-template>
    <p *ngIf="item.trainingActionInfo.sessionStartDate && item.trainingActionInfo.sessionEndDate">{{ item.trainingActionInfo.sessionStartDate | date: 'dd/MM/yyyy' }}
        - {{ item.trainingActionInfo.sessionEndDate | date: 'dd/MM/yyyy' }}</p>
</div>
<div>
    <div class="flex justify-between items-center">
        <div *ngIf="item.tags.length">
            <app-form-field-static [lengthLimit]="2"
                                   [value]="item.tags"
                                   (customClick)="setSearchTag($event)"
                                   type="tags"></app-form-field-static>
        </div>
        <app-registration-folder-menu class="ml-auto -mr-4 card-loading-hidden"
                                      [folders]="[item]"
                                      (processedFolder)="setRefresh($event)"
        ></app-registration-folder-menu>
    </div>
    <mat-progress-bar
        mode="determinate"
        matTooltipPosition="above"
        *ngIf="item.completionRate"
        [matTooltip]="(item.history.completionRateLastUpdate ? 'private.training-organism.folders.common.completionRateDoneDate' : 'private.training-organism.folders.common.completionRateDone') |translate: {
        'completionRate':item.completionRate,
        'expectedCompletionRate': item.completionRate === 100 ? '' : (' / ' + item.expectedCompletionRate + ' % attendu'),
        'date' : item.history.completionRateLastUpdate | date : 'dd/MM/yyyy à hh:mm' }"
        [color]="expectedCompletionRateColor(item)"
        [value]="item.completionRate">
    </mat-progress-bar>
</div>
