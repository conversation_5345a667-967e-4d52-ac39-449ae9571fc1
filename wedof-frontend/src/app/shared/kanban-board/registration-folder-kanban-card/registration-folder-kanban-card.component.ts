import {Component, EventEmitter, Input, Output} from '@angular/core';
import {
    RegistrationFolder,
    RegistrationFolderBillingStates,
    RegistrationFolderControlStates,
    RegistrationFolderStates
} from '../../api/models/registration-folder';
import {expectedCompletionRateColor} from '../../utils/color-utils';

@Component({
    selector: 'app-registration-folder-kanban-card',
    templateUrl: './registration-folder-kanban-card.component.html',
    styleUrls: ['./registration-folder-kanban-card.component.scss']
})
export class RegistrationFolderKanbanCardComponent {

    constructor() {
    }
    states = RegistrationFolderStates;
    billingStates = RegistrationFolderBillingStates;
    controlStates = RegistrationFolderControlStates;
    readonly expectedCompletionRateColor = expectedCompletionRateColor;

    @Input() item: RegistrationFolder;
    @Output() processedFolder: EventEmitter<RegistrationFolder> = new EventEmitter<RegistrationFolder>();
    @Output() searchTag = new EventEmitter<string>();

    setRefresh(registrationFolderUpdated: RegistrationFolder): void {
        this.item = registrationFolderUpdated;
        this.processedFolder.emit(registrationFolderUpdated);
    }

    setSearchTag(tag: string): void {
        this.searchTag.emit(tag);
    }
}
