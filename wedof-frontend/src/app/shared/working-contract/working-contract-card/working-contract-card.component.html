<mat-card class="flex-col mt-3 p-5 pb-0 pt-3 border shadow-none sm" [ngClass]="{'card-loading':cardLoading}">
    <div class="flex items-center mb-2">
        <mat-icon class="mr-3 card-loading-show text-4xl"
                  [color]="workingContract?.state | workingContractStateToColor"
                  title="{{ 'private.common.workingContract.state.' + workingContract.state | translate}}">
            {{ workingContract?.state | workingContractStateToIcon }}</mat-icon>
        <div class="truncate">
            <span class="text-xl font-semibold card-loading-show">
               {{ 'private.common.workingContract.title' | translate }}
            </span>
            <div class="text-secondary text-md truncate card-loading-show">
                Dernière mise à jour le {{workingContract.lastUpdate | date: 'dd/MM/yyyy' }}
            </div>
        </div>
    </div>
    <div class="flex flex-col" *ngIf="!panelOpenState || cardLoading ">
        <button type="button" class="flex justify-center mt-2 -mx-5 pt-2 pb-2 open-panel card-loading-hidden"
                (click)="openPanel()">
            <mat-icon svgIcon="keyboard_arrow_down"></mat-icon>
        </button>
    </div>
    <div>
        <div *ngIf="panelOpenState && !cardLoading">
            <form [formGroup]="formGroup" class="flex flex-col">
                <app-form-fields class="grid grid-cols-6 gap-2"
                                 formGroupName="workingContract"
                                 [entity]="workingContract"
                                 [appFormFieldsData]="appFormFieldsData"
                                 [formGroup]="formGroup">
                </app-form-fields>

                <button type="button" class="flex justify-center -mx-5 pt-2 pb-2 close-panel" (click)="closePanel()">
                    <mat-icon svgIcon="keyboard_arrow_up"></mat-icon>
                </button>
            </form>
        </div>
    </div>
</mat-card>
