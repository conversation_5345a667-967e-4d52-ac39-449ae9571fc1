import {Component, ElementRef, Input, OnChanges, OnInit, SimpleChanges} from '@angular/core';
import {WorkingContract} from '../../api/models/working-contract';
import {FormBuilder, FormGroup} from '@angular/forms';
import {AppFormFieldData} from '../../material/app-form-field/app-form-field.component';
import {TranslateService} from '@ngx-translate/core';
import {BaseCardComponentDirective} from '../../utils/base-card/base-card.directive';

@Component({
    selector: 'app-working-contract-card',
    templateUrl: './working-contract-card.component.html',
    styleUrls: ['./working-contract-card.component.scss']
})
export class WorkingContractCardComponent extends BaseCardComponentDirective implements OnInit, OnChanges {

    static COMPONENT_ID = 'contrat';

    panelOpenState = false;
    formGroup: FormGroup = null;
    appFormFieldsData: AppFormFieldData[];
    @Input() workingContract: WorkingContract;

    constructor(private _el: ElementRef,
                private _formBuilder: FormBuilder,
                private _translateService: TranslateService, ) {
        super(WorkingContractCardComponent.COMPONENT_ID, _el);
    }

    ngOnInit(): void {
        this.panelLoading();
        this.initForm(this.workingContract);
    }

    ngOnChanges(changes: SimpleChanges): void {
        this.panelLoading();
        this.initForm(this.workingContract);
    }

    openPanel(): void {
        this.panelOpenState = true;
    }

    closePanel(): void {
        this.panelOpenState = false;
    }

    initForm(workingContract: WorkingContract): void {
        this.formGroup = this._formBuilder.group({
            workingContract: this._formBuilder.group({})
        });
        this.appFormFieldsData = [
            {
                controlName: 'externalIdTrainingOrganism',
                label: 'private.common.workingContract.externalIdTrainingOrganism',
                type: 'text',
                disabled: true,
                value: workingContract.externalIdTrainingOrganism,
                colSpan: 3
            },
            {
                controlName: 'externalIdDeca',
                label: 'private.common.workingContract.externalIdDeca',
                type: 'text',
                disabled: true,
                value: workingContract.externalIdDeca,
                colSpan: 3
            },
            {
                controlName: 'state',
                label: 'private.common.workingContract.state.title',
                type: 'text',
                disabled: true,
                value: this._translateService.instant('private.common.workingContract.state.' + workingContract.state),
                colSpan: 3
            },
            {
                controlName: 'financer',
                label: 'private.common.workingContract.financer.title',
                type: 'text',
                disabled: true,
                value: this._translateService.instant('private.common.workingContract.financer.' + workingContract.financer),
                colSpan: 3
            },
            {
                controlName: 'type',
                label: 'private.common.workingContract.type.title',
                type: 'text',
                disabled: true,
                value: this._translateService.instant('private.common.workingContract.type.' + workingContract.type)
            },
            {
                controlName: 'startDate',
                label: 'private.common.workingContract.startDate',
                type: 'date',
                disabled: true,
                value: workingContract.startDate,
                colSpan: 3
            },
            {
                controlName: 'endDate',
                label: 'private.common.workingContract.endDate',
                type: 'date',
                disabled: true,
                value: workingContract.endDate,
                colSpan: 3
            },
            {
                controlName: 'signedDate',
                label: 'private.common.workingContract.signedDate',
                type: 'date',
                disabled: true,
                value: workingContract.signedDate
            },
            {
                controlName: 'amendmentDate',
                removed: !workingContract.amendmentDate,
                label: 'private.common.workingContract.amendmentDate',
                type: 'date',
                disabled: true,
                value: workingContract.amendmentDate,
                colSpan: 3
            },
            {
                controlName: 'breakingDate',
                removed: !workingContract.breakingDate,
                label: 'private.common.workingContract.breakingDate',
                type: 'date',
                disabled: true,
                value: workingContract.breakingDate,
                colSpan: 3
            },
            {
                controlName: 'amount',
                label: 'private.common.workingContract.amount',
                type: 'price',
                disabled: true,
                value: workingContract.amount
            },
            {
                controlName: 'employer',
                removed: !workingContract._links.employer,
                label: 'private.common.workingContract.employer',
                type: 'text',
                disabled: true,
                value: workingContract._links.employer ? workingContract._links.employer.name + '(' + workingContract._links.employer.siret + ')' : ''
            },
        ];
        this.panelLoaded();
    }

}
