import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {WorkingContractCardComponent} from './working-contract-card/working-contract-card.component';
import {MaterialModule} from '../material/material.module';
import {TranslateModule} from '@ngx-translate/core';
import {ReactiveFormsModule} from '@angular/forms';
import {PipesModule} from '../pipes/pipes.module';
import {MatCardModule} from '@angular/material/card';


@NgModule({
    declarations: [WorkingContractCardComponent],
    exports: [
        WorkingContractCardComponent
    ],
    imports: [
        PipesModule,
        CommonModule,
        MatCardModule,
        MaterialModule,
        TranslateModule,
        ReactiveFormsModule
    ]
})
export class WorkingContractModule {
}
