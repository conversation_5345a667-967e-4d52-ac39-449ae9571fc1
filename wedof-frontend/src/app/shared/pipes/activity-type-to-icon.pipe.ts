import {Injectable, Pipe, PipeTransform} from '@angular/core';
import {ActivityTypes} from '../api/models/activity';
import {RegistrationFolderStateToIconPipe} from './registration-folder-state-to-icon.pipe';
import {CertificationFolderStateToIconPipe} from './certification-folder-state-to-icon.pipe';
import {EntityClass} from '../utils/enums/entity-class';

@Pipe({
    name: 'activityTypeToIcon'
})
@Injectable()
export class ActivityTypeToIconPipe implements PipeTransform {
    transform(type: string, entityClass: string | null, newState: string | null): string {
        let icon = null;
        if (type === ActivityTypes.UPDATE_STATE) {
            icon = entityClass === EntityClass.REGISTRATION_FOLDER ? new RegistrationFolderStateToIconPipe().transform(newState) :
                entityClass === EntityClass.CERTIFICATION_FOLDER && new CertificationFolderStateToIconPipe().transform(newState);
            if (!icon) {
                icon = 'multiple_stop';
            }
        } else {
            const activityByType = {
                [ActivityTypes.CREATE]: 'create_new_folder',
                [ActivityTypes.UPDATE]: 'edit_note',
                [ActivityTypes.PROGRESS]: 'autorenew',
                [ActivityTypes.PHONE]: 'call',
                [ActivityTypes.EMAIL]: 'email',
                [ActivityTypes.MEETING]: 'groups',
                [ActivityTypes.CHAT]: 'chat',
                [ActivityTypes.SMS]: 'ad_units',
                [ActivityTypes.EXAMINATION]: 'policy',
                [ActivityTypes.TRAINING]: 'play_arrow',
                [ActivityTypes.CDC]: 'assured_workload',
                [ActivityTypes.REMARK]: 'list',
                [ActivityTypes.FILE]: 'picture_as_pdf',
                [ActivityTypes.CONNECTION_LOG]: 'login'
            };
            icon = activityByType[type];
            if (!icon) {
                icon = 'edit_note';
            }
        }
        return icon;
    }
}



