import {Pipe, PipeTransform} from '@angular/core';

/*
 * Display the external id from full external id
 * Usage:
 *   value | displayExternalId:type:withoutSiret
 * Example:
 *   {{ "12345678234_zefezf/12345678234_zdzdadc" | displayExternalId:'ta':true }}
 *   formats to: zdzdadc
*/
@Pipe({name: 'displayCatalogExternalId'})
export class DisplayCatalogExternalIdPipe implements PipeTransform {
    transform(value: string, type: string = 'trainingAction', withoutSiret: boolean = true): string {
        if (type === 'trainingAction') {
            const ids = value.split('/');
            if (ids.length === 1) {
                return withoutSiret ? ids[0].substring(15) : ids[0];
            } else {
                return withoutSiret ? ids[1].substring(15) : ids[1];
            }
        } else if (type === 'training') {
            const ids = value.split('/');
            return withoutSiret ? ids[0].substring(15) : ids[0];
        } else if (type === 'session') {
            const ids = value.split('/');
            return withoutSiret ? ids[value.length - 1].substring(15) : ids[value.length - 1];
        }
    }
}
