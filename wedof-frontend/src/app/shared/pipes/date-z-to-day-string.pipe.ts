import {Injectable, Pipe, PipeTransform} from '@angular/core';

@Pipe({
    name: 'dateZToDayString'
})
@Injectable()
export class DateZToDayStringPipe implements PipeTransform {
    transform(date?: Date | string): string {
        // When a date is stored as a "date" (and not a datetime) in the database
        // It is serialized a midnight UTC from the back
        // So to get back only the "day" part and get rid of the time,
        // We tell JS to format it to FR format (day/month/year) and tell it that the date is UTC
        if (typeof date === 'string') {
            date = new Date(date);
        }
        return date ? date.toLocaleDateString('fr-FR', {timeZone: 'UTC'}) : '';
    }
}
