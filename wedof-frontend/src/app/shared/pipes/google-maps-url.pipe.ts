import {Pipe, PipeTransform} from '@angular/core';
import {Location} from './location';

import {LocationToStringPipe} from './location-to-string.pipe';

@Pipe({
    name: 'googleMapsUrl'
})
export class GoogleMapsUrlPipe implements PipeTransform {
    constructor(private locationToStringPipe: LocationToStringPipe) {
    }

    transform(location: Location): string {
    const l = this.locationToStringPipe.transform(location);
    if (!l) {
      return;
    }
    return `https://www.google.com/maps/search/?api=1&query=${l}`;
  }
}
