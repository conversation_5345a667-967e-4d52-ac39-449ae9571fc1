import {Injectable, Pipe, PipeTransform} from '@angular/core';
import {ThemePalette} from '@angular/material/core';
import {WorkingContractStates} from '../api/models/working-contract';

@Pipe({
  name: 'workingContractStateToColor'
})
@Injectable()

export class WorkingContractStateToColorPipe implements PipeTransform {

    transform(state: string): ThemePalette {
        let color: ThemePalette;
        switch (state) {
            case WorkingContractStates.PENDING_ACCEPTATION:
                color = 'accent';
                break;
            case WorkingContractStates.DRAFT:
            case WorkingContractStates.SENT:
            case WorkingContractStates.ACCEPTED:
            case WorkingContractStates.COMPLETED:
                color = 'primary';
                break;
            case WorkingContractStates.CANCELLED:
            case WorkingContractStates.REFUSED:
            case WorkingContractStates.BROKEN:
                color = 'warn';
                break;
            default:
                color = 'warn';
                break;
        }
        return color;
    }
}
