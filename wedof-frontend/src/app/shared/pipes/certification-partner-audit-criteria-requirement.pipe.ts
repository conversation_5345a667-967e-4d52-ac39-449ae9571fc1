import {Pipe, PipeTransform} from '@angular/core';

import {TranslateService} from '@ngx-translate/core';
import {CertificationPartnerAuditCriteria} from '../api/models/certification-partner-audit';
import {isObject} from 'lodash';

@Pipe({
    name: 'certificationPartnerAuditCriteriaRequirement'
})
export class CertificationPartnerAuditCriteriaRequirementPipe implements PipeTransform {

    constructor(private _translateService: TranslateService) {
    }

    transform(criteria: CertificationPartnerAuditCriteria): string {
        const skipParameter = criteria.parameter == null || isObject(criteria.parameter);
        const translationKey = 'private.common.certification.audit.criterias.dynamicRequirement' + (skipParameter ? 'Unary' : '');
        return this._translateService.instant(translationKey, criteria);
    }
}
