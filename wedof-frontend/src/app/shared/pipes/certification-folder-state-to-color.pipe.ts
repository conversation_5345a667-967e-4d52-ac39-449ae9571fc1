import {Injectable, Pipe, PipeTransform} from '@angular/core';
import {CertificationFolderStates} from '../api/models/certification-folder';
import {ThemePalette} from '@angular/material/core';

@Pipe({
  name: 'certificationFolderStateToColor'
})
@Injectable()

export class CertificationFolderStateToColorPipe implements PipeTransform {
    transform(state: string): ThemePalette {
        let color: ThemePalette;
        switch (state) {
            case CertificationFolderStates.TO_REGISTER:
            case CertificationFolderStates.REGISTERED:
            case CertificationFolderStates.TO_RETAKE:
            case CertificationFolderStates.TO_TAKE:
            case CertificationFolderStates.TO_CONTROL:
            case CertificationFolderStates.SUCCESS:
            case 'update':
                color = 'primary';
                break;
            case CertificationFolderStates.FAILED:
            case CertificationFolderStates.ABORTED:
            case CertificationFolderStates.REFUSED:
            case 'goBack':
                color = 'warn';
                break;
            default:
                color = 'warn';
                break;
        }
        return color;
    }
}
