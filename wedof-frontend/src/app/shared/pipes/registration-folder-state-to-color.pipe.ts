import {Injectable, Pipe, PipeTransform} from '@angular/core';
import {RegistrationFolderActions, RegistrationFolderStates} from '../api/models/registration-folder';
import {ThemePalette} from '@angular/material/core';

@Pipe({
  name: 'registrationFolderStateToColor'
})
@Injectable()

export class RegistrationFolderStateToColorPipe implements PipeTransform {

    transform(state: string): ThemePalette {
        let color: ThemePalette;
        switch (state) {
            case RegistrationFolderStates.NOT_PROCESSED:
            case RegistrationFolderStates.WAITING_ACCEPTATION:
            case RegistrationFolderStates.VALIDATED:
            case RegistrationFolderStates.IN_TRAINING:
            case RegistrationFolderStates.SERVICE_DONE_DECLARED:
            case RegistrationFolderStates.SERVICE_DONE_VALIDATED:
            case RegistrationFolderActions.TO_BILL:
            case RegistrationFolderActions.PAID:
            case RegistrationFolderStates.ACCEPTED:
            case RegistrationFolderStates.TERMINATED:
            case 'update':
                color = 'primary';
                break;
            case RegistrationFolderStates.REFUSED_BY_ATTENDEE:
            case RegistrationFolderStates.REFUSED_BY_ORGANISM:
            case RegistrationFolderStates.CANCELED_BY_ATTENDEE:
            case RegistrationFolderStates.CANCELED_BY_ATTENDEE_NOT_REALIZED:
            case RegistrationFolderStates.CANCELED_BY_ORGANISM:
            case RegistrationFolderStates.REJECTED_WITHOUT_TITULAIRE_SUITE:
            case 'edit':
                color = 'warn';
                break;
            default:
                color = 'warn';
                break;
        }
        return color;
    }
}
