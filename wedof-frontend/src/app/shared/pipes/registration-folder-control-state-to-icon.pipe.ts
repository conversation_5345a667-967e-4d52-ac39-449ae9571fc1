import {Injectable, Pipe, PipeTransform} from '@angular/core';
import {RegistrationFolderControlStates} from '../api/models/registration-folder';

@Pipe({
  name: 'registrationFolderControlStateToIcon'
})
@Injectable()
export class RegistrationFolderControlStateToIconPipe implements PipeTransform {

    transform(controlState: string): string {
        let icon;
        switch (controlState) {
            case RegistrationFolderControlStates.NOT_IN_CONTROL:
                icon = 'rule_folder';
                break;
            case RegistrationFolderControlStates.IN_CONTROL:
                icon = 'pending_actions';
                break;
            case RegistrationFolderControlStates.RELEASED:
                icon = 'event_note';
                break;
            default:
                icon = 'rule_folder';
                break;
        }
        return icon;
    }
}
