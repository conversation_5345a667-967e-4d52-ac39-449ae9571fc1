import {NgModule} from '@angular/core';
import {RegistrationFolderStateToIconPipe} from './registration-folder-state-to-icon.pipe';
import {CertificationFolderStateToIconPipe} from './certification-folder-state-to-icon.pipe';
import {ProposalStateToIconPipe} from './proposal-state-to-icon.pipe';
import {CertificationFolderStateToColorPipe} from './certification-folder-state-to-color.pipe';
import {ProposalStateToColorPipe} from './proposal-state-to-color.pipe';
import {RegistrationFolderStateToColorPipe} from './registration-folder-state-to-color.pipe';
import {ControlRequiredPipe} from './control-required.pipe';
import {CertifierAccessStateToIconPipe} from './certifier-access-state-to-icon.pipe';
import {CertifierAccessStateToColorPipe} from './certifier-access-state-to-color.pipe';
import {LocationToStringPipe} from './location-to-string.pipe';
import {GoogleMapsUrlPipe} from './google-maps-url.pipe';
import {DisplayCatalogExternalIdPipe} from './display-catalog-external-id.pipe';
import {ActivityTypeToIconPipe} from './activity-type-to-icon.pipe';
import {RegistrationFolderBillingStateToColorPipe} from './registration-folder-billing-state-to-color.pipe';
import {RegistrationFolderBillingStateToIconPipe} from './registration-folder-billing-state-to-icon.pipe';
import {CertificationPartnerStateNamePipe} from './certification-partner-state-name.pipe';
import {RegistrationFolderControlStateToColorPipe} from './registration-folder-control-state-to-color.pipe';
import {RegistrationFolderControlStateToIconPipe} from './registration-folder-control-state-to-icon.pipe';
import {CertificationFolderCdcStateToIconPipe} from './certification-folder-cdc-state-to-icon.pipe';
import {InvoiceStateToIconPipe} from './invoice-state-to-icon.pipe';
import {InvoiceTypeToIconPipe} from './invoice-type-to-icon.pipe';
import {DisplayRegistrationFolderExternalIdPipe} from './display-registration-folder-external-id.pipe';
import {DateZToDayStringPipe} from './date-z-to-day-string.pipe';
import {CertificationPartnerAuditResultToIconPipe} from './certification-partner-audit-state-to-icon.pipe';
import {WorkingContractStateToIconPipe} from './working-contract-state-to-icon.pipe';
import {WorkingContractStateToColorPipe} from './working-contract-state-to-color.pipe';
import {CertificationPartnerAuditCriteriaRequirementPipe} from './certification-partner-audit-criteria-requirement.pipe';

@NgModule({
    declarations: [
        GoogleMapsUrlPipe,
        ControlRequiredPipe,
        DateZToDayStringPipe,
        LocationToStringPipe,
        InvoiceTypeToIconPipe,
        InvoiceStateToIconPipe,
        ActivityTypeToIconPipe,
        ProposalStateToIconPipe,
        ProposalStateToColorPipe,
        DisplayCatalogExternalIdPipe,
        CertifierAccessStateToIconPipe,
        WorkingContractStateToIconPipe,
        WorkingContractStateToColorPipe,
        CertifierAccessStateToColorPipe,
        CertificationPartnerStateNamePipe,
        RegistrationFolderStateToIconPipe,
        RegistrationFolderStateToColorPipe,
        CertificationFolderStateToIconPipe,
        CertificationFolderStateToColorPipe,
        CertificationFolderCdcStateToIconPipe,
        DisplayRegistrationFolderExternalIdPipe,
        CertificationPartnerAuditResultToIconPipe,
        RegistrationFolderBillingStateToIconPipe,
        RegistrationFolderBillingStateToColorPipe,
        RegistrationFolderControlStateToColorPipe,
        RegistrationFolderControlStateToIconPipe,
        CertificationPartnerAuditCriteriaRequirementPipe,
    ],
    imports: [],
    exports: [
        GoogleMapsUrlPipe,
        ControlRequiredPipe,
        DateZToDayStringPipe,
        LocationToStringPipe,
        InvoiceTypeToIconPipe,
        InvoiceStateToIconPipe,
        ActivityTypeToIconPipe,
        ProposalStateToIconPipe,
        ProposalStateToColorPipe,
        DisplayCatalogExternalIdPipe,
        CertifierAccessStateToIconPipe,
        WorkingContractStateToIconPipe,
        WorkingContractStateToColorPipe,
        CertifierAccessStateToColorPipe,
        CertificationPartnerStateNamePipe,
        RegistrationFolderStateToIconPipe,
        RegistrationFolderStateToColorPipe,
        CertificationFolderStateToIconPipe,
        CertificationFolderStateToColorPipe,
        CertificationFolderCdcStateToIconPipe,
        DisplayRegistrationFolderExternalIdPipe,
        CertificationPartnerAuditResultToIconPipe,
        RegistrationFolderBillingStateToIconPipe,
        RegistrationFolderBillingStateToColorPipe,
        RegistrationFolderControlStateToColorPipe,
        RegistrationFolderControlStateToIconPipe,
        CertificationPartnerAuditCriteriaRequirementPipe,
    ],
    providers: [
        GoogleMapsUrlPipe,
        ControlRequiredPipe,
        DateZToDayStringPipe,
        LocationToStringPipe,
        InvoiceTypeToIconPipe,
        ActivityTypeToIconPipe,
        InvoiceStateToIconPipe,
        ProposalStateToIconPipe,
        ProposalStateToColorPipe,
        DisplayCatalogExternalIdPipe,
        CertifierAccessStateToIconPipe,
        WorkingContractStateToIconPipe,
        WorkingContractStateToColorPipe,
        CertifierAccessStateToColorPipe,
        CertificationPartnerStateNamePipe,
        RegistrationFolderStateToIconPipe,
        RegistrationFolderStateToColorPipe,
        CertificationFolderStateToIconPipe,
        CertificationFolderStateToColorPipe,
        CertificationFolderCdcStateToIconPipe,
        DisplayRegistrationFolderExternalIdPipe,
        CertificationPartnerAuditResultToIconPipe,
        RegistrationFolderBillingStateToIconPipe,
        RegistrationFolderBillingStateToColorPipe,
        CertificationPartnerAuditCriteriaRequirementPipe,
    ]
})
export class PipesModule {
}
