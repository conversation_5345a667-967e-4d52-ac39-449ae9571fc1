import {Injectable, Pipe, PipeTransform} from '@angular/core';
import {ThemePalette} from '@angular/material/core';
import {RegistrationFolderControlStates} from '../api/models/registration-folder';

@Pipe({
    name: 'registrationFolderControlStateToColor'
})
@Injectable()
export class RegistrationFolderControlStateToColorPipe implements PipeTransform {

    transform(controlState: string): ThemePalette {
        let color: ThemePalette;
        switch (controlState) {
            case RegistrationFolderControlStates.NOT_IN_CONTROL:
            case RegistrationFolderControlStates.RELEASED:
                color = 'primary';
                break;
            case RegistrationFolderControlStates.IN_CONTROL:
                color = 'warn';
                break;
            default:
                color = 'primary';
                break;
        }
        return color;
    }

}
