import {Injectable, Pipe, PipeTransform} from '@angular/core';
import {RegistrationFolderActions, RegistrationFolderStates} from '../api/models/registration-folder';


@Pipe({
    name: 'registrationFolderStateToIcon'
})
@Injectable()
export class RegistrationFolderStateToIconPipe implements PipeTransform {
    transform(state: string): string {
        let icon = null;
        switch (state) {
            case RegistrationFolderStates.NOT_PROCESSED:
                icon = 'edit';
                break;
            case RegistrationFolderStates.VALIDATED:
                icon = 'done';
                break;
            case RegistrationFolderStates.WAITING_ACCEPTATION:
                icon = 'hourglass_empty';
                break;
            case RegistrationFolderStates.ACCEPTED:
                icon = 'done_all';
                break;
            case RegistrationFolderStates.IN_TRAINING:
                icon = 'play_arrow';
                break;
            case RegistrationFolderStates.TERMINATED:
                icon = 'assignment_turned_in';
                break;
            case RegistrationFolderStates.REFUSED_BY_ATTENDEE:
                icon = 'person_off';
                break;
            case RegistrationFolderStates.REFUSED_BY_ORGANISM:
                icon = 'cancel_presentation';
                break;
            case RegistrationFolderStates.SERVICE_DONE_DECLARED:
                icon = 'done_outline';
                break;
            case RegistrationFolderActions.TO_BILL:
            case RegistrationFolderActions.PAID:
            case RegistrationFolderStates.SERVICE_DONE_VALIDATED:
                icon = 'verified';
                break;
            case RegistrationFolderStates.CANCELED_BY_ATTENDEE:
                icon = 'warning';
                break;
            case RegistrationFolderStates.CANCELED_BY_ATTENDEE_NOT_REALIZED:
                icon = 'error';
                break;
            case RegistrationFolderStates.CANCELED_BY_ORGANISM:
                icon = 'error_outline';
                break;
            case RegistrationFolderStates.REJECTED_WITHOUT_TITULAIRE_SUITE:
                icon = 'warning_amber';
                break;
            /*case billed
                icon = 'receipt';
                break; */
            case 'edit':
            case 'update':
                icon = 'edit';
                break;
            default:
                icon = 'multiple_stop';
                break;
        }
        return icon;
    }
}
