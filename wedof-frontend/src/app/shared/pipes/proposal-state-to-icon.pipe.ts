import {Injectable, Pipe, PipeTransform} from '@angular/core';
import {ProposalStates} from '../api/models/proposal';

@Pipe({
    name: 'proposalStateToIcon'
})
@Injectable()
export class ProposalStateToIconPipe implements PipeTransform {
    transform(state: string): string {
        let icon = null;
        switch (state) {
            case ProposalStates.TEMPLATE:
                icon = 'email';
                break;
            case ProposalStates.DRAFT:
                icon = 'drafts';
                break;
            case ProposalStates.ACTIVE:
                icon = 'forward_to_inbox';
                break;
            case ProposalStates.ACCEPTED:
                icon = 'mark_email_read';
                break;
            case ProposalStates.REFUSED:
                icon = 'unsubscribe';
                break;
            case ProposalStates.VIEWED:
                icon = 'visibility';
                break;
            case 'edit':
                icon = 'edit';
                break;
            default:
                icon = 'priority_high';
                break;
        }
        return icon;
    }
}
