import {Injectable, Pipe, PipeTransform} from '@angular/core';
import {CertifierAccessState} from '../api/models/certifier-access';

@Pipe({
    name: 'certifierAccessStateToColor'
})
@Injectable()

export class CertifierAccessStateToColorPipe implements PipeTransform {
    transform(state: string): string {
        let color;
        switch (state) {
            case CertifierAccessState.WAITING:
            case CertifierAccessState.NONE:
                color = 'blue-icon';
                break;
            case CertifierAccessState.REFUSED:
            case CertifierAccessState.TERMINATED:
                color = 'red-icon';
                break;
            case CertifierAccessState.ACCEPTED:
            default:
                color = 'green-icon';
                break;
        }
        return color;
    }
}
