import {Injectable, Pipe, PipeTransform} from '@angular/core';
import {CertificationFolderCdcStates} from '../api/models/certification-folder';

@Pipe({
    name: 'certificationFolderCdcStateToIcon'
})
@Injectable()
export class CertificationFolderCdcStateToIconPipe implements PipeTransform {
    transform(state: string): string {
        let icon = null;
        switch (state) {
            case CertificationFolderCdcStates.NOT_EXPORTED:
                icon = 'warning';
                break;
            case CertificationFolderCdcStates.EXPORTED:
                icon = 'hourglass_empty';
                break;
            case CertificationFolderCdcStates.PROCESSED_KO:
                icon = 'report';
                break;
            case CertificationFolderCdcStates.PROCESSED_OK:
                icon = 'verified_user';
                break;
        }
        return icon;
    }
}
