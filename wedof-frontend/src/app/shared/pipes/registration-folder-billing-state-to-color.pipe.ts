import {Pipe, PipeTransform} from '@angular/core';
import {RegistrationFolderBillingStates} from '../api/models/registration-folder';
import {ThemePalette} from '@angular/material/core';

@Pipe({
    name: 'registrationFolderBillingStateToColor'
})
export class RegistrationFolderBillingStateToColorPipe implements PipeTransform {

    transform(billingState: string): ThemePalette {
        let color: ThemePalette;
        switch (billingState) {
            case RegistrationFolderBillingStates.TO_BILL:
            case RegistrationFolderBillingStates.DEPOSIT_PAID:
            case RegistrationFolderBillingStates.DEPOSIT_WAIT:
            case RegistrationFolderBillingStates.BILLED:
            case RegistrationFolderBillingStates.NOT_BILLABLE:
            case RegistrationFolderBillingStates.PAID:
                color = 'primary';
                break;
            default:
                color = 'primary';
                break;
        }
        return color;
    }

}
