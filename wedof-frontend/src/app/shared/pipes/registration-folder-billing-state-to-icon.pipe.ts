import {Pipe, PipeTransform} from '@angular/core';
import {RegistrationFolderBillingStates} from '../api/models/registration-folder';

@Pipe({
  name: 'registrationFolderBillingStateToIcon'
})
export class RegistrationFolderBillingStateToIconPipe implements PipeTransform {

    transform(billingState: string): string {
        let icon = null;
        switch (billingState) {
            case RegistrationFolderBillingStates.TO_BILL:
                icon = 'receipt';
                break;
            case RegistrationFolderBillingStates.DEPOSIT_WAIT:
                icon = 'inactive_order';
                break;
            case RegistrationFolderBillingStates.PAID:
                icon = 'receipt_long';
                break;
            case RegistrationFolderBillingStates.NOT_BILLABLE:
                icon = 'money_off';
                break;
            case RegistrationFolderBillingStates.DEPOSIT_PAID:
                icon = 'order_approve';
                break;
            case RegistrationFolderBillingStates.BILLED:
                icon = 'euro_symbol';
                break;
            default:
                icon = 'priority_high';
                break;
        }
        return icon;
    }
}
