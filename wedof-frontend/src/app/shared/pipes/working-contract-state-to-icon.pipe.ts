import {Injectable, Pipe, PipeTransform} from '@angular/core';
import {WorkingContractStates} from '../api/models/working-contract';

@Pipe({
    name: 'workingContractStateToIcon'
})
@Injectable()
export class WorkingContractStateToIconPipe implements PipeTransform {
    transform(state: string): string {
        let icon = null;
        switch (state) {
            case WorkingContractStates.DRAFT:
                icon = 'drafts';
                break;
            case WorkingContractStates.SENT:
                icon = 'forward';
                break;
            case WorkingContractStates.PENDING_ACCEPTATION:
                icon = 'hourglass_empty';
                break;
            case WorkingContractStates.ACCEPTED:
                icon = 'published_with_changes';
                break;
            case WorkingContractStates.CANCELLED:
                icon = 'cancel';
                break;
            case WorkingContractStates.REFUSED:
                icon = 'close';
                break;
            case WorkingContractStates.BROKEN:
                icon = 'block';
                break;
            case WorkingContractStates.COMPLETED:
                icon = 'done_all';
                break;
            default:
                icon = 'priority_high';
                break;
        }
        return icon;
    }
}
