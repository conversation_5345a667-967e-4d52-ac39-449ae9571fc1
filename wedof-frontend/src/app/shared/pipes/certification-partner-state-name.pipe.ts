import {Pipe, PipeTransform} from '@angular/core';

import {CertificationPartner} from '../api/models/certification-partner';
import {TranslateService} from '@ngx-translate/core';

@Pipe({
    name: 'certificationPartnerStateName'
})
export class CertificationPartnerStateNamePipe implements PipeTransform {

    constructor(private _translateService: TranslateService) {
    }

    transform(certificationPartner: CertificationPartner): string {
        let suffix = '';
        if (certificationPartner.pendingActivation) {
            suffix = ' ' + this._translateService.instant('private.certification.partners.table.partnership.pendingActivationSuffix');
        } else if (certificationPartner.pendingRevocation) {
            suffix = ' ' + this._translateService.instant('private.certification.partners.table.partnership.pendingRevocationSuffix');
        }
        return this._translateService.instant('private.certification.partners.table.partnership.state.' + certificationPartner.state) + suffix;
    }
}
