import {Injectable, Pipe, PipeTransform} from '@angular/core';
import {CertifierAccessState} from '../api/models/certifier-access';

@Pipe({
    name: 'certifierAccessStateToIcon'
})
@Injectable()
export class CertifierAccessStateToIconPipe implements PipeTransform {
    transform(state: string): string {
        let icon;
        switch (state) {
            case CertifierAccessState.WAITING:
                icon = 'timelapse';
                break;
            case CertifierAccessState.ACCEPTED:
                icon = 'published_with_changes';
                break;
            case CertifierAccessState.TERMINATED:
                icon = 'sync_disabled';
                break;
            case CertifierAccessState.REFUSED:
                icon = 'sync_lock';
                break;
            default:
                icon = 'sync_problem';
                break;
        }
        return icon;
    }
}
