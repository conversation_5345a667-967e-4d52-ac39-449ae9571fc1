import {Injectable, Pipe, PipeTransform} from '@angular/core';
import {InvoiceStates} from '../api/models/invoice';

@Pipe({
    name: 'invoiceStateToIcon'
})
@Injectable()
export class InvoiceStateToIconPipe implements PipeTransform {
    transform(state: string): string {
        let icon = null;
        switch (state) {
            case InvoiceStates.PAID:
                icon = 'app_registration';
                break;
            case InvoiceStates.CANCELED:
                icon = 'block';
                break;
            case InvoiceStates.WAITING_PAYMENT:
                icon = 'block';
                break;
        }
        return icon;
    }
}
