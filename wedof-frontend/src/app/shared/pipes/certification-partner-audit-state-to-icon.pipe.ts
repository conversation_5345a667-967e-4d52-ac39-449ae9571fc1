import {Injectable, Pipe, PipeTransform} from '@angular/core';
import {CertificationPartnerAuditResults} from '../api/models/certification-partner-audit';

@Pipe({
    name: 'certificationPartnerAuditResultToIcon'
})
@Injectable()
export class CertificationPartnerAuditResultToIconPipe implements PipeTransform {
    transform(result: string): string {
        let icon = null;
        switch (result) {
            case CertificationPartnerAuditResults.COMPLIANT:
                icon = 'task_alt';
                break;
            case CertificationPartnerAuditResults.NON_COMPLIANT:
                icon = 'cancel';
                break;
            case CertificationPartnerAuditResults.PARTIALLY_COMPLIANT:
                icon = 'rule';
                break;
            case CertificationPartnerAuditResults.NONE:
                icon = 'question_mark';
                break;
            default:
                icon = 'check';
                break;
        }
        return icon;
    }
}
