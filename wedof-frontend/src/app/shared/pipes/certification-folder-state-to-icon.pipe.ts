import {Injectable, Pipe, PipeTransform} from '@angular/core';
import {CertificationFolderStates} from '../api/models/certification-folder';

@Pipe({
    name: 'certificationFolderStateToIcon'
})
@Injectable()
export class CertificationFolderStateToIconPipe implements PipeTransform {
    transform(state: string): string {
        let icon = null;
        switch (state) {
            case CertificationFolderStates.TO_REGISTER:
                icon = 'app_registration';
                break;
            case CertificationFolderStates.REFUSED:
                icon = 'block';
                break;
            case CertificationFolderStates.REGISTERED:
                icon = 'add_box';
                break;
            case CertificationFolderStates.TO_RETAKE:
                icon = 'history';
                break;
            case CertificationFolderStates.TO_TAKE:
                icon = 'school';
                break;
            case CertificationFolderStates.TO_CONTROL:
                icon = 'policy';
                break;
            case CertificationFolderStates.SUCCESS:
                icon = 'thumb_up';
                break;
            case CertificationFolderStates.FAILED:
                icon = 'close';
                break;
            case CertificationFolderStates.ABORTED:
                icon = 'cancel';
                break;
            case 'goBack':
                icon = 'rotate_left';
                break;
            case 'update':
                icon = 'edit';
                break;
            default:
                icon = 'multiple_stop';
                break;
        }
        return icon;
    }
}
