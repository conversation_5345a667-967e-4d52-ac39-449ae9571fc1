import {Pipe, PipeTransform} from '@angular/core';

import {Location} from './location';

@Pipe({
  name: 'locationToString'
})
export class LocationToStringPipe implements PipeTransform {
  transform(location: Location): string {
    if (!location || !location.address || !location.postalCode || !location.city) {
      return;
    }
    return `${location.address} ${location.postalCode} ${location.city}`;
  }
}
