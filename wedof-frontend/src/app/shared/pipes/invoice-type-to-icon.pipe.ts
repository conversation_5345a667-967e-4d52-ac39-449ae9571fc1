import {Injectable, Pipe, PipeTransform} from '@angular/core';
import {InvoiceStates, InvoiceTypes} from '../api/models/invoice';

@Pipe({
    name: 'invoiceTypeToIcon'
})
@Injectable()
export class InvoiceTypeToIconPipe implements PipeTransform {
    transform(state: string): string {
        let icon = null;
        switch (state) {
            case InvoiceTypes.INVOICE:
                icon = 'app_registration';
                break;
            case InvoiceTypes.DEPOSIT_INVOICE:
                icon = 'block';
                break;
            case InvoiceTypes.CREDIT_NOTE:
                icon = 'block';
                break;
        }
        return icon;
    }
}
