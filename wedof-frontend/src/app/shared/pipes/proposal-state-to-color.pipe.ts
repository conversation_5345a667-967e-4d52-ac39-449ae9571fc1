import {Injectable, Pipe, PipeTransform} from '@angular/core';
import {ProposalStates} from '../api/models/proposal';
import {ThemePalette} from '@angular/material/core';

@Pipe({
    name: 'proposalStateToColor'
})
@Injectable()

export class ProposalStateToColorPipe implements PipeTransform {

    transform(state: string): ThemePalette {
        let color: ThemePalette;
        switch (state) {
            case ProposalStates.TEMPLATE:
            case ProposalStates.DRAFT:
            case ProposalStates.ACTIVE:
            case ProposalStates.ACCEPTED:
            case ProposalStates.VIEWED:
                color = 'primary';
                break;
            case ProposalStates.REFUSED:
            case 'edit' :
                color = 'warn';
                break;
            default:
                color = 'warn';
                break;
        }
        return color;
    }
}
