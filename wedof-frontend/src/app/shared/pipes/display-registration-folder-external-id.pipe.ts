import {Injectable, Pipe, PipeTransform} from '@angular/core';
import {RegistrationFolder} from '../api/models/registration-folder';
import {DataProviders} from '../api/models/connection';

@Pipe({
    name: 'displayRegistrationFolderExternalId'
})
@Injectable()
export class DisplayRegistrationFolderExternalIdPipe implements PipeTransform {
    transform(registrationFolder: RegistrationFolder): string {
        if (!registrationFolder) {
            return '';
        }
        if (registrationFolder.type === DataProviders.CPF) {
            return 'CPF-' + registrationFolder.externalId;
        } else {
            return registrationFolder.externalId;
        }
    }
}
