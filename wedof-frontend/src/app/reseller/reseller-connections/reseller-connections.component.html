<div class="mx-auto my-auto fullwidth-basic-normal-scroll py-8">
    <div class="flex flex-row justify-center pb-4">
        <img *ngIf="reseller.logo" class="dialog-logos" [src]="reseller.logo" alt="Logo {{reseller.name}}"/>
        <mat-icon *ngIf="reseller.logo" id="double-arrows-icon2" [inline]="true" class="self-center text-xl pl-2 pr-2"
                  svgIcon="compare_arrows"></mat-icon>
        <img class="dialog-logos" src="../../../assets/images/dataProviders/wedof-square.png"
             alt="{{'auth.connection.header.logos.wedof' | translate}}"/>
        <mat-icon id="double-arrows-icon" [inline]="true" class="self-center text-xl pl-2 pr-2"
                  svgIcon="compare_arrows"></mat-icon>
        <img class="dialog-logos" src="../../../assets/images/dataProviders/{{dataProvider}}-square.png"
             alt="{{ dataProviderName | translate}}"/>
    </div>
    <h5 class="mt-4 text-center font-bold">{{ organism.name }} (EDOF) / Wedof / {{reseller.name}} </h5>
    <div class="p-10">
        <treo-message [type]="'info'" class="flex-auto px-4 pb-6"
                      *ngIf="('auth.' + dataProvider + '.subtitle' | translate)"
                      [showIcon]="false"
                      appearance="outline">
            <p class="py-2 text-justify" [innerHTML]="'auth.' + dataProvider + '.subtitle' | translate"></p>
        </treo-message>

        <h3 class="font-normal m-4">L'organisme <span
            class="font-bold">{{ organism.name | titlecase }}</span> {{ dataProviderConfigEntry.type === connectionsType.HABILITATION ? 'habilite' : 'delegue' }}
            <span
                class="wedof-name font-bold">&nbsp;Wedof</span> à :</h3>
        <div class="flex-auto">
            <ul class="px-5">
                <li class="{{ displayLimitedUsage() ? 'pt-2 pb-2' : 'py-4' }} {{!last ? 'border-b' : ''}}"
                    *ngFor="let authorization of dataProviderConfigEntry.authorizations; let last = last;">
                    <div class="flex flex-row justify-between">
                        <p
                            class="self-center"
                            [innerHTML]="'auth.' + dataProvider + '.authorizations.' + authorization + '.text' | translate"></p>
                        <mat-icon class="pl-2"
                                  title="{{'auth.'+ dataProvider+'.authorizations.'+authorization+'.title' | translate}}"
                                  svgIcon="info"></mat-icon>
                    </div>
                    <p *ngIf="displayLimitedUsage()"
                       class="text-disabled text-sm"
                       [innerHTML]="'auth.' + dataProvider + '.authorizations.' + authorization + '.limitedUsage' | translate"></p>
                </li>
            </ul>
            <h3 class="font-normal m-4 pb-6"> et délègue à <span class="wedof-name font-bold">{{reseller.name}}</span>
                l'accès à <span
                    class="wedof-name font-bold">&nbsp;Wedof</span></h3>
            <treo-message [type]="dataProviderConfigEntry.afterAuthorizationsMessage?.type" class=" flex-auto px-4 pb-6"
                          *ngIf="dataProviderConfigEntry.afterAuthorizationsMessage?.shouldDisplay(connection)"
                          [showIcon]="false"
                          appearance="outline">
                <p class="py-2 text-justify">
                    {{ dataProviderConfigEntry.afterAuthorizationsMessage.text }}
                </p>
            </treo-message>
            <form [formGroup]="formGroup" class="px-6 w-full flex flex-col"
                  *ngIf="!connection || (connection?.state !== connectionState.ACTIVE); else resume">
                <app-form-fields *ngIf="displayLoginForm()"
                                 formGroupName="connectionForm"
                                 [appFormFieldsData]="appFormFieldsData"
                                 [entity]="connection"
                                 [formGroup]="formGroup">
                </app-form-fields>
                <div *ngIf="errorMessages?.length">
                    <treo-message class="flex-auto" appearance="outline" [showIcon]="false" type="error">
                        <ul>
                            <li *ngFor="let errorMessage of errorMessages">
                                <span [innerHTML]="errorMessage"></span>
                            </li>
                        </ul>
                    </treo-message>
                </div>
                <button
                    *ngIf="!connection || (connection?.state !== connectionState.ACTIVE)"
                    class="m-auto whitespace-pre-wrap w-80 mt-6"
                    [disabled]="loading || (displayLoginForm() && formGroup.invalid)"
                    mat-flat-button
                    type="submit"
                    color="primary"
                    (click)="saveConnection()">
                    <mat-progress-spinner *ngIf="loading; else submitLabel" [diameter]="24" mode="indeterminate">
                    </mat-progress-spinner>
                    <ng-template #submitLabel>
                        {{ (dataProviderConfigEntry.submitLabel ? dataProviderConfigEntry.submitLabel : ('auth.connection.form.' + dataProviderConfigEntry.type + '.auto')) | translate }}
                    </ng-template>
                </button>
            </form>
            <ng-template #resume>
                <h5 class="text-center">{{'auth.connection.state.' + connection.type + '.' + connection.state  | translate }}</h5>
            </ng-template>
            <div class="flex flex-col"
                 *ngIf="dataProviderConfigEntry.documentation
                        && (!connection || (connection?.state !== connectionState.ACTIVE))
                        && !loading">
                <a class="mt-3 text-center"
                   target="_blank"
                   href="{{dataProviderConfigEntry.documentation}}">
                    {{ 'auth.' + dataProvider + '.documentation' | translate }}
                </a>
            </div>
            <div *ngIf="messages?.length" class="flex flex-col items-center mt-2 mb-2">
                <treo-message appearance="outline" [showIcon]="false" type="info">
                    <ul>
                        <li *ngFor="let message of messages">
                            {{ message }}
                        </li>
                    </ul>
                </treo-message>
            </div>
        </div>
    </div>
</div>

