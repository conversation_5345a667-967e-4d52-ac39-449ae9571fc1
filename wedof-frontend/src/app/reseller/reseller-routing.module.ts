import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {CanDeactivateGuard} from '../shared/utils/can-deactivate/can-deactivate.guard';
import {ResellerAuthComponent} from './reseller-auth/reseller-auth.component';

const routes: Routes =  [{
    path: '',
    canDeactivate: [CanDeactivateGuard],
    component: ResellerAuthComponent
}];

@NgModule({
    imports: [
        RouterModule.forChild(routes)
    ],
    exports: [RouterModule]
})
export class ResellerRoutingModule {
}
