<div class="content-layout fullwidth-basic-normal-scroll">
    <div class="card w-full p-5 md:w-2/3 md:p-0 lg:w-2/3 lg:p-0 xl:w-1/3 xl:p-0">

        <mat-progress-spinner class="m-auto" *ngIf="loading; else showComponent" [diameter]="75"
                              mode="indeterminate"></mat-progress-spinner>

        <ng-template #showComponent>

            <ng-container *ngIf="!reseller; else showReseller">
                <treo-message type="error" [showIcon]="false" appearance="outline">
                    {{'public.reseller.noResellerAssociated' | translate}}
                </treo-message>
            </ng-container>

            <ng-template #showReseller>
                <ng-container *ngIf="!showConnectionsForm; else connectionsForm">
                    <button class="underline"
                            type="button"
                            (click)="signOut()"
                            *ngIf="user">
                        {{ "common.actions.signOut" | translate }}
                    </button>

                    <h5 class="text-center">{{organism?.name}}</h5>

                    <div class="logo pb-5 flex flex-row justify-center content-center">
                        <img *ngIf="reseller?.logo" [src]="reseller.logo" alt="Logo {{reseller?.name}}">
                        <mat-icon *ngIf="reseller?.logo" id="double-arrows-icon" [inline]="true"
                                  class="self-center text-xl pl-2 pr-2"
                                  svgIcon="compare_arrows"></mat-icon>
                        <img src="./assets/images/logo/logo.svg" alt="Logo wedof">
                    </div>

                    <treo-message class="mt-5 mb-5" type="info" [showIcon]="false"
                                  appearance="outline">
                        <p [innerHTML]="'public.reseller.wedofDescription' | translate | markdown"></p>
                    </treo-message>

                    <treo-message class="mb-5" type="info" [showIcon]="false"
                                  appearance="outline">
                        <p [innerHTML]="'public.reseller.resellerDescription' | translate : {reseller : reseller?.name} "></p>
                    </treo-message>

                    <form [formGroup]="formGroup" class="flex flex-col pt-10">
                        <div class="flex flex-row items-center justify-between">
                            <app-form-fields formGroupName="reseller"
                                             class="mr-2"
                                             [appFormFieldsData]="appFormFieldsData"
                                             [formGroup]="formGroup"></app-form-fields>
                            <button mat-flat-button color="primary"
                                    [disabled]="loadingSubscription || (edofConnection && !allowedConnectionState.includes(edofConnection?.state))"
                                    (click)="updateSubscription()">
                                <mat-progress-spinner class="mr-4" *ngIf="loadingSubscription" [diameter]="24"
                                                      mode="indeterminate"></mat-progress-spinner>
                                {{'public.reseller.form.button' | translate}}</button>
                        </div>
                        <mat-error class="mt-3" *ngIf="errorMessages.length">
                            <ul>
                                <li *ngFor="let errorMessage of errorMessages">{{ errorMessage }}</li>
                            </ul>
                        </mat-error>
                    </form>
                </ng-container>

                <ng-template #connectionsForm>
                    <app-reseller-connections [dataProvider]="dataProviderCpf"
                                              [organism]="organism"
                                              [reseller]="reseller"
                                              [subscription]="subscription"
                                              [limitedUsage]="false">
                    </app-reseller-connections>
                </ng-template>
            </ng-template>
        </ng-template>

    </div>
    <div class="text-secondary text-center mt-10 text-sm">
        Un service proposé par © <a href="https://www.wedof.fr">Wedof</a> 2025
    </div>
</div>
