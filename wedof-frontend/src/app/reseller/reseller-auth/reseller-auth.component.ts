import {Component, OnInit} from '@angular/core';
import {Organism} from '../../shared/api/models/organism';
import {FormBuilder, FormGroup} from '@angular/forms';
import {AppFormFieldData} from '../../shared/material/app-form-field/app-form-field.component';
import {Store} from '@ngxs/store';
import {FetchUser, UpdateUser, UserState} from '../../shared/api/state/user.state';
import {User} from '../../shared/api/models/user';
import {mergeMap, switchMap} from 'rxjs/operators';
import {FetchOrganism, OrganismState} from '../../shared/api/state/organism.state';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../shared/errors/errors.types';
import {CanDeactivateComponent} from '../../shared/utils/can-deactivate/can-deactivate.component';
import {FetchSubscription, SubscriptionState} from '../../shared/api/state/subscription.state';
import {Subscription} from '../../shared/api/models/subscription';
import {SubscriptionService} from '../../shared/api/services/subscription.service';
import {Connection, ConnectionState, DataProviders} from '../../shared/api/models/connection';
import {ConnectionsState, FetchConnections} from '../../shared/api/state/connections.state';
import {AuthService} from '../../core/auth/auth.service';
import {ActivatedRoute, Router} from '@angular/router';
import {Title} from '@angular/platform-browser';
import {TranslateService} from '@ngx-translate/core';
import {OrganismService} from '../../shared/api/services/organism.service';

@Component({
    selector: 'app-reseller-auth',
    templateUrl: './reseller-auth.component.html',
    styleUrls: ['./reseller-auth.component.scss']
})
export class ResellerAuthComponent extends CanDeactivateComponent implements OnInit {

    user: User;
    organism: Organism;
    reseller: Organism;
    edofConnection: Connection;
    subscription: Subscription;
    showConnectionsForm = false;
    loadingSubscription = false;
    dataProviderCpf = DataProviders.CPF;
    allowedConnectionState = [ConnectionState.INACTIVE, ConnectionState.FAILED];

    formGroup: FormGroup;
    loading = false;
    errorMessages: string[] = [];
    appFormFieldsData: AppFormFieldData[];

    token: string;
    refreshToken: string;

    constructor(
        private _store: Store,
        private _router: Router,
        private _organismService: OrganismService,
        private _authService: AuthService,
        private _activatedRoute: ActivatedRoute,
        private _subscriptionService: SubscriptionService,
        private _titleService: Title,
        private _translateService: TranslateService,
        private _formBuilder: FormBuilder) {
        super();
    }

    ngOnInit(): void {
        this._titleService.setTitle(this._translateService.instant('public.reseller.title'));
        this._authService.accessToken = this._activatedRoute.snapshot.queryParams['token'];
        this._authService.refreshToken = this._activatedRoute.snapshot.queryParams['refresh_token'];
        this.token = this._authService.accessToken;
        this.refreshToken = this._authService.refreshToken;
        if (this.token && this.refreshToken) {
            this.loading = true;
            this._store.dispatch(new FetchUser()).pipe(
                mergeMap(() => this._store.selectOnce(UserState.user)),
                switchMap(user => {
                    this.user = user;
                    return this._store.dispatch(new FetchOrganism()).pipe(
                        mergeMap(() => this._store.selectOnce(OrganismState.organism)),
                    );
                }),
                switchMap(organism => {
                    this.organism = organism;
                    if (organism._links.reseller?.href) {
                        this._organismService.getByUrl(organism._links.reseller.href).subscribe((reseller) => {
                            this.reseller = reseller;
                        });
                    }
                    return this._store.dispatch(new FetchSubscription()).pipe(
                        mergeMap(() => this._store.selectOnce(SubscriptionState.subscription)),
                    );
                }),
                switchMap(subscription => {
                    this.subscription = subscription;
                    return this._store.dispatch(new FetchConnections()).pipe(
                        mergeMap(() => this._store.selectOnce(ConnectionsState.connections)),
                    );
                }),
            ).subscribe({
                    next: (connections) => {
                        this.edofConnection = connections.find((connection) => connection.dataProvider === DataProviders.CPF);
                        if (this.user.rgpdMsa) {
                            // todo compare subscription with resellerMetadata
                            // if nok update subscription else showConnectionsForm directly
                            this._subscriptionService.updateFromReseller(this.subscription.id).pipe(
                                switchMap(subscription => {
                                    this.subscription = subscription;
                                    return this._store.dispatch(new FetchSubscription()).pipe(
                                        mergeMap(() => this._store.selectOnce(SubscriptionState.subscription)),
                                    );
                                })
                            ).subscribe({
                                next: () => {
                                    this.loading = false;
                                    this.showConnectionsForm = true;
                                },
                                error: (httpErrorResponse: HttpErrorResponse) => {
                                    this.loading = false;
                                    this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
                                }
                            });
                        } else {
                            this.formGroup = this._formBuilder.group({
                                reseller: this._formBuilder.group({})
                            });
                            const appFormFieldsData: AppFormFieldData[] = [
                                {
                                    controlName: 'msa',
                                    label: 'auth.sign-up.step3.checkboxes.msa.text',
                                    value: !!this.user.rgpdMsa,
                                    type: 'checkbox',
                                    href: '/public-cgu'
                                },
                                {
                                    controlName: 'rgpd',
                                    label: 'auth.sign-up.step3.checkboxes.rgpd.text',
                                    value: !!this.user.rgpdMsa,
                                    type: 'checkbox',
                                    href: '/public-confidentialite'
                                }];
                            this.appFormFieldsData = appFormFieldsData.filter(value => value != null);
                            this.loading = false;
                        }
                    },
                    error: (httpErrorResponse: HttpErrorResponse) => {
                        this.loading = false;
                        this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
                    }
                },
            );
        } else {
            this._router.navigate(['/404-not-found']);
        }
    }

    updateSubscription(): void {
        this.loadingSubscription = true;
        this._store.dispatch(new UpdateUser(this.user.email, {
            email: this.user.email,
            rgpdMsa: new Date()
        })).pipe(
            mergeMap(() => this._store.selectOnce(UserState.user)),
            switchMap(() => {
                return this._subscriptionService.updateFromReseller(this.subscription.id);
            }),
            switchMap(subscription => {
                this.subscription = subscription;
                return this._store.dispatch(new FetchSubscription()).pipe(
                    mergeMap(() => this._store.selectOnce(SubscriptionState.subscription)),
                );
            }),
        ).subscribe({
            next: () => {
                this.showConnectionsForm = true;
            },
            error: (httpErrorResponse: HttpErrorResponse) => {
                this.loadingSubscription = false;
                this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
            }
        });
    }

    canDeactivate(): boolean {
        return false;
    }

    signOut(): void {
        this._authService.signOut().subscribe(result => {
            if (result) {
                window.location.href = 'https://www.google.com';
            }
        });
    }

}
