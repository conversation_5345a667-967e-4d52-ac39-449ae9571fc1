import {
    After<PERSON>iewInit,
    ChangeDetectionStrategy,
    Component,
    OnDestroy,
    OnInit,
    ViewChild,
    ViewEncapsulation
} from '@angular/core';
import {combineLatest, Observable, ReplaySubject, Subject} from 'rxjs';
import {debounceTime, filter, mergeMap, switchMap, takeUntil, tap} from 'rxjs/operators';
import {User} from 'app/shared/api/models/user';
import {UserService} from 'app/shared/api/services/user.service';
import {FormControl} from '@angular/forms';
import {AuthService} from '../../../core/auth/auth.service';
import {MatSelect} from '@angular/material/select';
import {Select, Store} from '@ngxs/store';
import {UserState} from '../../../shared/api/state/user.state';
import {SubscriptionUpdateParams} from '../../../shared/api/services/subscription.service';
import {SubscriptionState, UpdateSubscription} from '../../../shared/api/state/subscription.state';
import {
    Subscription,
    SubscriptionCertifierTypes,
    SubscriptionTrainingTypes
} from '../../../shared/api/models/subscription';
import {OrganismState} from '../../../shared/api/state/organism.state';
import {Organism} from '../../../shared/api/models/organism';

@Component({
    selector: 'impersonate',
    templateUrl: './impersonate.component.html',
    styleUrls: ['./impersonate.component.scss'],
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    exportAs: 'impersonate'
})
export class ImpersonateComponent implements OnInit, OnDestroy, AfterViewInit {

    user: User;
    organism: Organism;
    subscription: Subscription;

    usersFilteringCtrl: FormControl = new FormControl();
    filteredUsers: ReplaySubject<User[]> = new ReplaySubject<User[]>(1);
    searching = false;
    selectedUser: FormControl = new FormControl();

    @Select(UserState.user) user$: Observable<User>;
    @Select(OrganismState.organism) organism$: Observable<Organism>;
    @Select(SubscriptionState.subscription) subscription$: Observable<Subscription>;

    @ViewChild('userSelect') userSelect: MatSelect;

    private _unsubscribeAll = new Subject<void>();

    constructor(
        private _userService: UserService,
        private _authService: AuthService,
        private _store: Store,
    ) {
    }

    ngOnInit(): void {
        combineLatest([
            this.user$,
            this.organism$,
            this.subscription$
        ]).pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe(([user, organism, subscription]) => {
            this.user = user;
            this.organism = organism;
            this.subscription = subscription;
        });
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    ngAfterViewInit(): void {
        if (this.canImpersonate()) {
            this.userSelect.openedChange.pipe(takeUntil(this._unsubscribeAll)).subscribe(opened => {
                if (opened) { // Force search on open
                    this.usersFilteringCtrl.patchValue(' ');
                    this.usersFilteringCtrl.patchValue('');
                }
            });
            this.usersFilteringCtrl.valueChanges.pipe(
                filter(search => !!search),
                tap(() => this.searching = true),
                takeUntil(this._unsubscribeAll),
                debounceTime(200),
                switchMap(search => {
                    return this._userService.list({query: search});
                }),
                takeUntil(this._unsubscribeAll)
            ).subscribe(filteredUsers => {
                this.searching = false;
                this.filteredUsers.next(filteredUsers.payload);
            });
            this.selectedUser.valueChanges.pipe(takeUntil(this._unsubscribeAll)).subscribe((user) => {
                if (user && this._authService.impersonate(user.email)) {
                    document.location.href = '/accueil';
                }
            });
        }
    }

    canImpersonate(): boolean {
        return this.user?.can_impersonate && !this.user?.is_impersonator;
    }

    removeImpersonate(): void {
        if (this._authService.impersonate(null)) {
            document.location.href = '/accueil';
        }
    }

    isAdminView(): boolean {
        return this._authService.adminView === String(true);
    }

    toggleAdminView(): void {
        this._authService.toggleViewAsAdmin();
        document.location.href = '/accueil';
    }

    startTrial(forTraining: boolean = true): void {
        const params = (forTraining ? {
            training: {
                type: 'trial'
            }
        } : {
            certifier: {
                type: 'trial'
            }
        }) as SubscriptionUpdateParams;
        this._store.dispatch(new UpdateSubscription(this.subscription.id, params)).pipe(
            mergeMap(() => this._store.selectOnce(SubscriptionState.subscription))
        ).subscribe();
    }

    enableStartTrial(forTraining: boolean = true): boolean {
        if (forTraining) {
            return this.organism?.isTrainingOrganism && [SubscriptionTrainingTypes.NONE,
                SubscriptionTrainingTypes.FREE,
                SubscriptionTrainingTypes.TRIAL].includes(this.subscription.trainingType);
        } else {
            return this.organism?.isCertifierOrganism && [SubscriptionCertifierTypes.PARTNER,
                SubscriptionCertifierTypes.NONE,
                SubscriptionCertifierTypes.FREE,
                SubscriptionCertifierTypes.TRIAL].includes(this.subscription.certifierType);
        }
    }

    formatResult(user: User): string {
        return user.firstName + ' ' + user.lastName
            + (user._links?.mainOrganism ? ' - ' + user._links.mainOrganism.name : '')
            + (user._links?.mainOrganism ? ' (...' + user._links.mainOrganism.siret.substring(user._links.mainOrganism.siret.length - 5) + ')' : '');
    }
}
