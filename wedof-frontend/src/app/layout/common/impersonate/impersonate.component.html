<ng-container *ngIf="canImpersonate()">
    <button [matMenuTriggerFor]="impersonateActions" class="icon-button" mat-icon-button>
        <span class="avatar">
            <mat-icon svgIcon="supervisor_account"></mat-icon>
        </span>
    </button>
    <mat-menu #impersonateActions="matMenu" class="impersonate-actions-menu" xPosition="before">
        <div class="m-2">
            <mat-select #userSelect
                        [formControl]="selectedUser"
                        [placeholder]="'Rechercher par email' | translate">
                <mat-option>
                    <ngx-mat-select-search [formControl]="usersFilteringCtrl"
                                           [indexAndLengthScreenReaderText]="' sur '"
                                           [noEntriesFoundLabel]="'Aucun utilisateur' | translate"
                                           [placeholderLabel]="'Email' | translate"
                                           [searching]="searching"></ngx-mat-select-search>
                </mat-option>
                <mat-option *ngFor="let user of filteredUsers | async"
                            [value]="user">
                    {{ formatResult(user) }}
                </mat-option>
            </mat-select>
        </div>
    </mat-menu>
</ng-container>
<ng-container *ngIf="user?.is_impersonator">
    <button type="button" class="icon-button" mat-icon-button [matMenuTriggerFor]="actionsMenu">
        <span class="avatar">
            <mat-icon svgIcon="settings"></mat-icon>
        </span>
    </button>
    <mat-menu #actionsMenu="matMenu">
        <ng-template matMenuContent>
            <button type="button" mat-menu-item (click)="removeImpersonate()">
                <mat-icon svgIcon="person_off"></mat-icon>
                <span>Retour admin</span>
            </button>
            <mat-divider></mat-divider>
            <button mat-menu-item [matMenuTriggerFor]="trial">
                <mat-icon>cake</mat-icon>
                <span>Activer trial</span>
            </button>
            <button type="button" mat-menu-item (click)="toggleAdminView()">
                <mat-icon svgIcon="{{isAdminView() ? 'visibility_off' : 'visibility'}}"></mat-icon>
                <span>Bascule vue normale / vue admin</span>
            </button>
        </ng-template>
    </mat-menu>
    <mat-menu #trial="matMenu">
        <ng-template matMenuContent>
            <button [disabled]="!enableStartTrial()" type="button" mat-menu-item (click)="startTrial()">
                Espace formation
            </button>
            <button [disabled]="!enableStartTrial(false)" type="button" mat-menu-item (click)="startTrial(false)">
                Espace certification
            </button>
        </ng-template>
    </mat-menu>
</ng-container>
