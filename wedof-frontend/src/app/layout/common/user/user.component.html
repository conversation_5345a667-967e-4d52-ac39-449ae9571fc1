<!-- Button -->
<button class="icon-button" mat-icon-button [matMenuTriggerFor]="userActions">
    <span class="avatar">
        <img *ngIf="showAvatar && user?.avatar" [src]="user?.avatar">
        <mat-icon *ngIf="!showAvatar || !user?.avatar" svgIcon="account_circle"></mat-icon>
        <span class="status" [ngClass]="user?.status"></span>
    </span>
</button>

<mat-menu class="user-actions-menu" xPosition="before" #userActions="matMenu">
    <a mat-menu-item [routerLink]="['/', 'profil']">
        <mat-icon svgIcon="account_circle"></mat-icon>
        <span>{{ 'private.layout.user.actions-menu.info' | translate }}</span>
    </a>
    <a mat-menu-item [routerLink]="['/', 'mes-applications']">
        <mat-icon svgIcon="apps"></mat-icon>
        <span>{{ 'private.layout.user.actions-menu.application' | translate }}</span>
    </a>
    <button mat-menu-item (click)="openSubscriptionDialog()">
        <mat-icon svgIcon="card_membership"></mat-icon>
        <span>{{ 'private.layout.user.actions-menu.settingsSubscription' | translate }}</span>
    </button>
    <mat-divider class="my-2"></mat-divider>
    <button mat-menu-item (click)="signOut()">
        <mat-icon svgIcon="power_settings_new"></mat-icon>
        <span>{{ 'private.layout.user.actions-menu.sign-out' | translate }}</span>
    </button>
</mat-menu>
