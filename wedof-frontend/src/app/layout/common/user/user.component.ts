import {ChangeDetectionStrategy, Component, Input, OnDestroy, OnInit, ViewEncapsulation} from '@angular/core';
import {Router} from '@angular/router';
import {combineLatest, Observable, Subject} from 'rxjs';
import {takeUntil} from 'rxjs/operators';
import {User} from 'app/shared/api/models/user';
import {MatDialog} from '@angular/material/dialog';
import {Subscription, SubscriptionTypes} from '../../../shared/api/models/subscription';
import {Organism} from '../../../shared/api/models/organism';
import {
    DialogUpgradeSubscriptionComponent
} from '../../../shared/subscription/dialog-upgrade-subscription/dialog-upgrade-subscription.component';
import {Select} from '@ngxs/store';
import {UserState} from '../../../shared/api/state/user.state';
import {SubscriptionState} from '../../../shared/api/state/subscription.state';
import {OrganismState} from '../../../shared/api/state/organism.state';

@Component({
    selector: 'user',
    templateUrl: './user.component.html',
    styleUrls: ['./user.component.scss'],
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    exportAs: 'user'
})
export class UserComponent implements OnInit, OnDestroy {

    @Input() showAvatar: boolean;

    @Select(UserState.user) user$: Observable<User>;
    @Select(SubscriptionState.subscription) subscription$: Observable<Subscription>;
    @Select(OrganismState.organism) organism$: Observable<Organism>;

    user: User;
    subscription: Subscription;
    organism: Organism;

    private _unsubscribeAll = new Subject<void>();

    constructor(
        private _router: Router,
        private _dialog: MatDialog,
    ) {
        this.showAvatar = true;
    }

    ngOnInit(): void {
        combineLatest([
            this.user$,
            this.organism$,
            this.subscription$
        ]).pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe(([user, organism, subscription]) => {
            this.user = user;
            this.subscription = subscription;
            this.organism = organism;
        });
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    signOut(): void {
        this._router.navigate(['/', 'auth', 'deconnexion']);
    }

    openSubscriptionDialog(): void {
        this._dialog.open(DialogUpgradeSubscriptionComponent, {
            panelClass: ['full-page-scroll-50'],
            data: {
                organism: this.organism,
                subscription: this.subscription,
                subscriptionTypeToShow: this.organism.isTrainingOrganism ? SubscriptionTypes.TRAINING : SubscriptionTypes.CERTIFIER
            }
        });
    }
}
