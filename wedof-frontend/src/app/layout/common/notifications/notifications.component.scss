@import 'treo';

notifications {
    display: flex;
    align-items: center;
}

// Notifications panel
.notifications-panel {
    min-width: 360px !important;
    max-width: 360px !important;
    border-radius: 4px;
    max-height: calc(100vh - 80px);
    overflow-y: auto;
    @include treo-elevation('lg');
    @include treo-breakpoint('xs') {
        min-width: 0 !important;
        max-width: 384px !important;
        width: calc(100vw - 32px);
    }

    .header {
        display: flex;
        align-items: center;
        padding: 12px 16px 12px 24px;
        border-top-width: 4px;
        border-bottom-width: 1px;

        .title {
            display: flex;
            align-items: center;
            font-size: 16px;
            font-weight: 500;
        }

        .mark-all-as-read {
            margin-left: auto;
        }
    }

    .content {
        position: relative;
        display: flex;
        flex-direction: column;
        max-height: 480px;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;

        .notification {
            display: flex;
            flex: 1 0 auto;
            border-bottom-width: 1px;

            &:last-child {
                border-bottom-width: 0;
            }

            @include treo-breakpoint('gt-xs') {

                &:hover {

                    .actions {

                        .indicator {

                            .read {
                                opacity: 1;
                            }
                        }

                        .remove {
                            opacity: 1;
                        }
                    }
                }
            }

            > a {
                cursor: pointer;
            }

            > a,
            > span {
                display: flex;
                flex: 1 1 auto;
                padding: 20px 0 20px 24px;

                .icon,
                .image {
                    min-width: 32px;
                    width: 32px;
                    min-height: 32px;
                    height: 32px;
                    border-radius: 50%;
                    margin-right: 16px;
                }

                .icon {
                    padding: 6px;
                }

                .image {
                    overflow: hidden;
                    object-fit: cover;
                    object-position: center;
                }

                .notification-content {
                    display: flex;
                    flex-direction: column;
                    flex: 1 1 auto;

                    .title {
                        display: -webkit-box;
                        -webkit-line-clamp: 1;
                        -webkit-box-orient: vertical;
                        overflow: hidden;
                        font-weight: 600;
                        font-size: 15px;
                    }

                    .description {
                        display: -webkit-box;
                        -webkit-line-clamp: 2;
                        -webkit-box-orient: vertical;
                        overflow: hidden;
                    }

                    .time {
                        margin-top: 8px;
                        font-size: 12px;
                        line-height: 1;
                    }
                }

            }

            .actions {
                position: relative;
                display: flex;
                flex-direction: column;
                margin: 20px 24px 20px 8px;

                .indicator {
                    width: 24px;
                    height: 24px;
                    min-height: 24px;
                    line-height: 24px !important;

                    .read,
                    .unread {
                        width: 8px;
                        min-width: 8px;
                        max-width: 8px;
                        height: 8px;
                        min-height: 8px;
                        max-height: 8px;
                        border-radius: 50%;
                    }

                    @include treo-breakpoint('gt-xs') {

                        .read {
                            opacity: 0;
                        }
                    }
                }

                .remove {
                    width: 24px;
                    height: 24px;
                    min-height: 24px;
                    line-height: 24px !important;

                    @include treo-breakpoint('gt-xs') {
                        opacity: 0;
                    }

                    .mat-icon {
                        @include treo-icon-size(16);
                    }
                }
            }
        }

        .empty {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 96px 24px;

            .mat-icon {
                @include treo-icon-size(64);

                + * {
                    margin-top: 16px;
                    font-size: 16px
                }
            }
        }
    }
}

// -----------------------------------------------------------------------------------------------------
// @ Theming
// -----------------------------------------------------------------------------------------------------
@include treo-theme {

    $background: map-get($theme, background);
    $foreground: map-get($theme, foreground);
    $primary: map-get($theme, primary);
    $is-dark: map-get($theme, is-dark);

    // Notifications panel
    .notifications-panel {

        .header {
            border-top-color: map-get($primary, default);

            @if ($is-dark) {
                background: treo-color('cool-gray', 800);
            } @else {
                background: treo-color('cool-gray', 100);
            }

            .mark-all-as-read {
                color: treo-color('cool-gray', 400);
            }
        }

        .content {
            background: map-get($background, card);

            .notification {

                @include treo-breakpoint('gt-xs') {

                    &:hover {
                        @if ($is-dark) {
                            background: rgba(0, 0, 0, 0.05) !important;
                        } @else {
                            background: treo-color('cool-gray', 100) !important;
                        }
                    }
                }

                .icon {
                    color: map-get($primary, default);

                    @if ($is-dark) {
                        background: rgba(0, 0, 0, 0.05);
                    } @else {
                        background: map-get($primary, 100);
                    }
                }

                .notification-content {

                    .time {
                        color: map-get($foreground, secondary-text);
                    }
                }

                .actions {

                    .indicator {

                        .unread {
                            background: treo-color('red', 600);
                        }

                        .read {
                            @if ($is-dark) {
                                background: treo-color('cool-gray', 500);
                            } @else {
                                background: treo-color('cool-gray', 400);
                            }
                        }
                    }
                }
            }
        }
    }
}
