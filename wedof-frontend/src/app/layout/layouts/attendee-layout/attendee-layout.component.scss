attendee-layout {
    position: relative;
    display: flex;
    flex: 1 1 auto;
    width: 100%;

    > .wrapper {
        display: flex;
        flex-direction: column;
        flex: 1 1 auto;
        min-width: 0;

        > .content {
            display: flex;
            flex-direction: column;
            flex: 1 1 auto;

            > *:not(router-outlet) {
                position: relative;
                display: flex;
                flex: 1 1 auto;
            }
        }
    }
}

