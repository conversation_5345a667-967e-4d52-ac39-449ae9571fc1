@import 'treo';

$treo-vertical-navigation-width: 280;
$treo-vertical-navigation-dense-width: 72;

dense-layout {
    position: relative;
    display: flex;
    flex: 1 1 auto;
    width: 100%;

    > treo-vertical-navigation {

        .treo-vertical-navigation-content-header {

            .logo {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 80px;
                min-height: 80px;
                max-height: 80px;

                img {
                    max-width: 32px;
                }
            }
        }

        // Dense appearance overrides
        &.treo-vertical-navigation-appearance-dense {

            &:not(.treo-vertical-navigation-mode-over) {
                width: #{$treo-vertical-navigation-dense-width}px;
                min-width: #{$treo-vertical-navigation-dense-width}px;
                max-width: #{$treo-vertical-navigation-dense-width}px;

                // Left positioned
                &.treo-vertical-navigation-position-left {

                    // Side mode
                    &.treo-vertical-navigation-mode-side {
                        margin-left: -#{$treo-vertical-navigation-dense-width}px;
                    }

                    // Opened
                    &.treo-vertical-navigation-opened {
                        margin-left: 0;
                    }
                }

                // Right positioned
                &.treo-vertical-navigation-position-right {

                    // Side mode
                    &.treo-vertical-navigation-mode-side {
                        margin-right: -#{$treo-vertical-navigation-dense-width}px;
                    }

                    // Opened
                    &.treo-vertical-navigation-opened {
                        margin-right: 0;
                    }

                    // Aside wrapper
                    .treo-vertical-navigation-aside-wrapper {
                        left: auto;
                        right: #{$treo-vertical-navigation-dense-width}px;
                    }

                    &.treo-vertical-navigation-hover {

                        .treo-vertical-navigation-aside-wrapper {
                            left: auto;
                            right: #{$treo-vertical-navigation-width}px;
                        }
                    }
                }
            }

            // Wrapper
            .treo-vertical-navigation-wrapper {

                // Content
                .treo-vertical-navigation-content {

                    treo-vertical-navigation-aside-item,
                    treo-vertical-navigation-basic-item,
                    treo-vertical-navigation-collapsable-item,
                    treo-vertical-navigation-group-item {

                        .treo-vertical-navigation-item {
                            width: #{$treo-vertical-navigation-width}px;
                            min-width: #{$treo-vertical-navigation-width}px;
                            max-width: #{$treo-vertical-navigation-width}px;

                            .treo-vertical-navigation-item-arrow,
                            .treo-vertical-navigation-item-badge,
                            .treo-vertical-navigation-item-title-wrapper {
                                transition: opacity 400ms cubic-bezier(0.25, 0.8, 0.25, 1);
                            }
                        }
                    }

                    treo-vertical-navigation-group-item {

                        &:first-of-type {
                            margin-top: 0;
                        }
                    }
                }
            }

            &:not(.treo-vertical-navigation-hover):not(.treo-vertical-navigation-mode-over) {

                // Wrapper
                .treo-vertical-navigation-wrapper {

                    // Content
                    .treo-vertical-navigation-content {

                        .treo-vertical-navigation-item {
                            padding: 12px 24px;

                            .treo-vertical-navigation-item-arrow,
                            .treo-vertical-navigation-item-badge,
                            .treo-vertical-navigation-item-title-wrapper {
                                white-space: nowrap;
                            }

                            .treo-vertical-navigation-item-arrow,
                            .treo-vertical-navigation-item-badge-content,
                            .treo-vertical-navigation-item-title-wrapper {
                                opacity: 0;
                            }
                        }

                        treo-vertical-navigation-collapsable-item {

                            .treo-vertical-navigation-item-children {
                                display: none;
                            }
                        }

                        treo-vertical-navigation-group-item {

                            > .treo-vertical-navigation-item-wrapper {

                                .treo-vertical-navigation-item {

                                    &:before {
                                        content: '';
                                        position: absolute;
                                        top: 20px;
                                        width: 23px;
                                        border-top-width: 2px;
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // Aside wrapper
            .treo-vertical-navigation-aside-wrapper {
                left: #{$treo-vertical-navigation-dense-width}px;
            }

            // Hover
            &.treo-vertical-navigation-hover {

                .treo-vertical-navigation-wrapper {
                    width: #{$treo-vertical-navigation-width}px;
                }

                .treo-vertical-navigation-aside-wrapper {
                    left: #{$treo-vertical-navigation-width}px;
                }
            }
        }
    }

    > .wrapper {
        display: flex;
        flex-direction: column;
        flex: 1 1 auto;
        min-width: 0;

        > .header {
            position: relative;
            display: flex;
            flex: 1 1 auto;
            align-items: center;
            width: 100%;
            height: 64px;
            max-height: 64px;
            min-height: 64px;
            padding: 0 24px;
            z-index: 49;

            @include treo-breakpoint('lt-md') {
                padding: 0 16px;
            }

            .navigation-toggle-button {
                margin-right: 8px;
            }

            .navigation-appearance-toggle-button {
                margin-right: 8px;

                @include treo-breakpoint('lt-md') {
                    display: none !important;
                }
            }

            .spacer {
                display: flex;
                flex: 1 1 auto;
                height: 1px;
            }

            search {
                margin-right: 8px;
            }

            notifications {
                margin-right: 8px;
            }
        }

        > .content {
            display: flex;
            flex-direction: column;
            flex: 1 1 auto;

            > *:not(router-outlet) {
                position: relative;
                display: flex;
                flex: 1 1 auto;
            }
        }

        > .footer {
            display: flex;
            flex: 1 1 auto;
            align-items: center;
            justify-content: flex-start;
            width: 100%;
            height: 56px;
            max-height: 56px;
            min-height: 56px;
            padding: 0 24px;
            z-index: 49;
        }
    }

    &.fixed-header {

        > .wrapper {

            > .header {
                position: sticky;
                top: 0;
            }
        }
    }

    &.fixed-footer {

        > .wrapper {

            > .footer {
                position: sticky;
                bottom: 0;
            }
        }
    }
}

// -----------------------------------------------------------------------------------------------------
// @ Theming
// -----------------------------------------------------------------------------------------------------
@include treo-theme {

    $background: map-get($theme, background);
    $foreground: map-get($theme, foreground);
    $primary: map-get($theme, primary);
    $is-dark: map-get($theme, is-dark);

    dense-layout {

        > .wrapper {

            > .header {
                @if ($is-dark) {
                    box-shadow: 0 1px 0 0 map-get($foreground, divider);
                } @else {
                    background: map-get($background, card);
                    @include treo-elevation();
                }
            }

            > .footer {
                @if (not $is-dark) {
                    background: map-get($background, card);
                    @include treo-elevation();
                }
                box-shadow: 0 -1px 0 0 map-get($foreground, divider);
                color: map-get($foreground, secondary-text);
            }
        }
    }
}
