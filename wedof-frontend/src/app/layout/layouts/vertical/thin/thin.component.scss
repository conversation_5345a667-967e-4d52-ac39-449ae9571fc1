@import 'treo';

$treo-vertical-navigation-thin-width: 88;

thin-layout {
    position: relative;
    display: flex;
    flex: 1 1 auto;
    width: 100%;

    > treo-vertical-navigation {

        .treo-vertical-navigation-content-header {

            .logo {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 80px;
                min-height: 80px;
                max-height: 80px;

                img {
                    max-width: 32px;
                }
            }
        }

        // Thin appearance overrides
        &.treo-vertical-navigation-appearance-thin {
            width: #{$treo-vertical-navigation-thin-width}px;
            min-width: #{$treo-vertical-navigation-thin-width}px;
            max-width: #{$treo-vertical-navigation-thin-width}px;

            // Left positioned
            &.treo-vertical-navigation-position-left {

                &.treo-vertical-navigation-mode-side {
                    margin-left: -#{$treo-vertical-navigation-thin-width}px;
                }

                &.treo-vertical-navigation-opened {
                    margin-left: 0;
                }
            }

            // Right positioned
            &.treo-vertical-navigation-position-right {

                &.treo-vertical-navigation-mode-side {
                    margin-right: -#{$treo-vertical-navigation-thin-width}px;
                }

                &.treo-vertical-navigation-opened {
                    margin-right: 0;
                }

                .treo-vertical-navigation-aside-wrapper {
                    left: auto;
                    right: #{$treo-vertical-navigation-thin-width}px;
                }
            }

            // Wrapper
            .treo-vertical-navigation-wrapper {

                // Content
                .treo-vertical-navigation-content {

                    > treo-vertical-navigation-aside-item,
                    > treo-vertical-navigation-basic-item {
                        flex-direction: column;
                        justify-content: center;
                        height: 64px;
                        min-height: 64px;
                        max-height: 64px;
                        padding: 0 16px;

                        .treo-vertical-navigation-item-wrapper {
                            display: flex;
                            align-items: center;
                            justify-content: center;

                            .treo-vertical-navigation-item {
                                justify-content: center;
                                padding: 12px;
                                border-radius: 4px;

                                .treo-vertical-navigation-item-icon {
                                    margin: 0;
                                }

                                .treo-vertical-navigation-item-arrow,
                                .treo-vertical-navigation-item-badge-content,
                                .treo-vertical-navigation-item-title-wrapper {
                                    display: none;
                                }
                            }
                        }
                    }

                    > treo-vertical-navigation-collapsable-item {
                        display: none
                    }

                    > treo-vertical-navigation-group-item {

                        > .treo-vertical-navigation-item-wrapper {
                            display: none
                        }
                    }
                }
            }

            // Aside wrapper
            .treo-vertical-navigation-aside-wrapper {
                left: #{$treo-vertical-navigation-thin-width}px;
            }
        }
    }

    > .wrapper {
        display: flex;
        flex-direction: column;
        flex: 1 1 auto;
        min-width: 0;

        > .header {
            position: relative;
            display: flex;
            flex: 1 1 auto;
            align-items: center;
            width: 100%;
            height: 64px;
            max-height: 64px;
            min-height: 64px;
            padding: 0 24px;
            z-index: 49;

            @include treo-breakpoint('lt-md') {
                padding: 0 16px;
            }

            .navigation-toggle-button {
                margin-right: 8px;
            }

            .spacer {
                display: flex;
                flex: 1 1 auto;
                height: 1px;
            }

            search {
                margin-right: 8px;
            }

            notifications {
                margin-right: 8px;
            }
        }

        > .content {
            display: flex;
            flex-direction: column;
            flex: 1 1 auto;

            > *:not(router-outlet) {
                position: relative;
                display: flex;
                flex: 1 1 auto;
            }
        }

        > .footer {
            display: flex;
            flex: 1 1 auto;
            align-items: center;
            justify-content: flex-start;
            width: 100%;
            height: 56px;
            max-height: 56px;
            min-height: 56px;
            padding: 0 24px;
            z-index: 49;
        }
    }

    &.fixed-header {

        > .wrapper {

            > .header {
                position: sticky;
                top: 0;
            }
        }
    }

    &.fixed-footer {

        > .wrapper {

            > .footer {
                position: sticky;
                bottom: 0;
            }
        }
    }
}

// -----------------------------------------------------------------------------------------------------
// @ Theming
// -----------------------------------------------------------------------------------------------------
@include treo-theme {

    $background: map-get($theme, background);
    $foreground: map-get($theme, foreground);
    $primary: map-get($theme, primary);
    $is-dark: map-get($theme, is-dark);

    thin-layout {

        > treo-vertical-navigation {
            @if ($is-dark) {
                background: treo-color('cool-gray', 900);
            } @else {
                background: map-get($background, card);
            }
        }

        > .wrapper {

            > .header {
                @if ($is-dark) {
                    box-shadow: 0 1px 0 0 map-get($foreground, divider);
                } @else {
                    background: map-get($background, card);
                    @include treo-elevation();
                }
            }

            > .footer {
                @if (not $is-dark) {
                    background: map-get($background, card);
                    @include treo-elevation();
                }
                box-shadow: 0 -1px 0 0 map-get($foreground, divider);
                color: map-get($foreground, secondary-text);
            }
        }
    }
}
