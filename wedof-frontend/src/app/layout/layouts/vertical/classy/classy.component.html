<!-- Navigation -->
<treo-vertical-navigation class="theme-dark"
                          [appearance]="'classic'"
                          [mode]="isScreenSmall ? 'over' : 'side'"
                          [name]="'mainNavigation'"
                          [navigation]="navigation"
                          [hidden]="!displayLayout"
                          [opened]="!isScreenSmall">

    <div treoVerticalNavigationContentHeader>

        <!-- Actions -->
        <div class="actions">

            <!-- Logo -->
            <a class="logo" [routerLink]="this.isDev && this.isAuthenticated ? ['simulator'] : ['/', 'accueil']">
                <img alt="logo" src="assets/images/logo/logo.svg">
            </a>

            <!-- Spacer -->
            <div class="spacer"></div>

            <!-- Notifications -->
            <!--            <notifications [notifications]="data.notifications"></notifications>-->

            <!-- Impersonate -->
            <impersonate>
            </impersonate>

            <!-- User -->
            <user [showAvatar]="false"></user>

            <!-- Navigation toggle button -->
            <button class="navigation-toggle-button"
                    mat-icon-button
                    (click)="toggleNavigation('mainNavigation')">
                <mat-icon svgIcon="menu"></mat-icon>
            </button>
        </div>

        <!-- User -->
        <div class="user" *ngIf="user">
            <div class="flex flex-row w-full">
                <a [routerLink]="'profil'" class="avatar">
                    <img alt="{{user.name}}" *ngIf="user.avatar"
                         [src]="user.avatar">
                    <mat-icon *ngIf="!user.avatar"
                              title="{{user.name}} - {{organism?.name}}"
                              [ngClass]="{'adminIcon': isAdminView(), 'resellerIcon': organism?.isReseller, 'reselleeIcon': organism?._links?.reseller?.siret}"
                              svgIcon="account_circle">
                    </mat-icon>
                    <mat-icon class="icon-size-32 -mt-10 flex items-end ml-12"
                              *ngIf="hasARequiredConnectionNotActive(requiredConnections).length > 0"
                              color="warn"
                              svgIcon="warning"
                              [matTooltip]="getTooltipForRequiredConnectionNotActive(requiredConnections)"
                              [matTooltipPosition]="'above'"
                              [matTooltipShowDelay]="500">
                    </mat-icon>
                </a>
                <div class="info ml-3">
                    <div class="name" title="{{user.name}}">{{user.name}}</div>
                    <div class="email text-secondary" title="{{user.email}}">{{user.email}}</div>
                    <div class="email text-secondary" title="{{organism?.name}}">{{organism?.name}}</div>
                    <div *ngIf="organism?._links?.reseller?.name" class="email text-secondary">
                        Via {{organism?._links?.reseller?.name}}</div>
                    <div *ngIf="isAdminView()">Vue administrateur</div>
                </div>
            </div>

            <div *ngIf="showDemoText && (organism?.isCertifierOrganism || organism?.isTrainingOrganism)"
                 class="font-semibold text-center mt-6 align-middle cursor-pointer flex flex-row">
                <div>
                    <mat-icon class="mr-2 icon-size-24" color="primary" svgIcon="groups"></mat-icon>
                </div>
                <div [attr.data-cal-link]="this.getCalLinkUrl()">
                    {{ 'private.layout.user.askForDemo' | translate }}
                </div>
            </div>
        </div>
    </div>

    <div treoVerticalNavigationContentFooter>
        <!-- <div class="logo"></div> -->
        <div class="version">
            <p [matTooltip]="releaseDate"> {{ wedofVersion }} </p>
        </div>
    </div>

</treo-vertical-navigation>

<!-- Wrapper -->
<div class="wrapper">
    <!-- Content -->
    <div class="content">
        <div class="notification-messages" *ngIf="user">
            <div *ngFor="let message of globalMessages">
                <app-notification-message [message]="message"></app-notification-message>
            </div>
            <div *ngFor="let message of myMessages">
                <app-notification-message [message]="message"></app-notification-message>
            </div>
            <treo-message *ngIf="organism?.vat === null && organism?.isTrainingOrganism"
                          class="treo-message-wedof border-b"
                          type="info"
                          [showIcon]="false"
                          appearance="outline">
                <div class="py-2">
                    <a [routerLink]="['/profil/organisme']">{{'private.training-organism.proposals.kanbantva' | translate}}</a>
                </div>
            </treo-message>
        </div>
        <button class="navigation-toggle-button" style="position: absolute" *ngIf="!verticalNavigationOpened"
                mat-icon-button
                (click)="toggleNavigation('mainNavigation')">
            <mat-icon svgIcon="menu"></mat-icon>
        </button>
        <router-outlet *ngIf="true">
        </router-outlet>
    </div>
</div>
