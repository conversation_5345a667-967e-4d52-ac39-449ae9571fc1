@import 'treo';

classy-layout {
    position: relative;
    display: flex;
    flex: 1 1 auto;
    width: 100%;

    > treo-vertical-navigation {

        .treo-vertical-navigation-content-header {

            .actions {
                display: flex;
                align-items: center;
                width: 100%;
                padding: 16px 16px 16px 24px;

                .logo {
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    img {
                        max-width: 32px;
                    }
                }

                .spacer {
                    display: flex;
                    flex: 1 1 auto;
                    height: 1px;
                }
            }

            .user {
                display: flex;
                flex-direction: column;
                align-items: center;
                width: 100%;
                padding: 16px;

                .avatar {
                    position: relative;
                    width: 80px;
                    height: 80px;

                    img {
                        width: 80px;
                        min-width: 80px;
                        height: 80px;
                        min-height: 80px;
                        border-radius: 50%;
                    }

                    .mat-icon {
                        @include treo-icon-size(80);
                    }

                    .resellerIcon {
                        color: #27a835 !important;
                    }

                    .reselleeIcon {
                        color: #8b2acc !important;
                    }

                    .adminIcon {
                        color: #ff0000 !important;
                    }
                }

                .info {
                    display: flex;
                    flex-direction: column;
                    flex: 1 1 auto;
                    align-items: center;
                    justify-content: center;
                    width: 100%;
                    min-width: 0;

                    .name,
                    .email {
                        width: 100%;
                        min-width: 0;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        overflow: hidden;
                        text-align: left;
                        line-height: normal;
                    }

                    .email {
                        margin-top: 2px;
                    }

                    .menu {
                        margin-left: auto;

                        .mat-icon {
                            @include treo-icon-size(20);
                        }
                    }
                }

                .iconColorDemo {
                    color: #1A202E !important;
                    vertical-align: -5px
                }
            }
        }

        .treo-vertical-navigation-content-footer {

            .logo {
                display: flex;
                align-items: center;
                height: 64px;
                min-height: 64px;
                max-height: 64px;
                padding: 0 24px;
                margin: 8px 0 16px 0;
                opacity: 0.12;

                img {
                    max-width: 144px;
                }
            }

            .version {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 30px;
                min-height: 30px;
                max-height: 30px;
                padding: 0 24px;
                margin: 16px auto 8px auto;
                opacity: 0.5;
            }
        }
    }

    > .wrapper {
        display: flex;
        flex-direction: column;
        flex: 1 1 auto;
        min-width: 0;

        > .header {
            position: relative;
            display: flex;
            flex: 1 1 auto;
            align-items: center;
            width: 100%;
            height: 64px;
            max-height: 64px;
            min-height: 64px;
            padding: 0 24px;
            z-index: 49;

            @include treo-breakpoint('lt-md') {
                padding: 0 16px;
            }

            .navigation-toggle-button {
                margin-right: 8px;
            }

            .message {
                display: flex;
                align-items: center;
                margin: 0 32px 0 8px;
                line-height: 1;

                @include treo-breakpoint('lt-md') {
                    display: none;
                }

                .mat-icon {
                    margin-right: 8px;
                }

                a {
                    display: flex;
                    align-items: center;

                    .mat-icon {
                        margin-left: 4px;
                        color: currentColor !important;
                        @include treo-icon-size(16);
                    }
                }

            }

            .spacer {
                display: flex;
                flex: 1 1 auto;
                height: 1px;
            }

            search {
                margin-right: 8px;
            }

            notifications {
                margin-right: 8px;
            }
        }

        > .content {
            display: flex;
            flex-direction: column;
            flex: 1 1 auto;

            > *:not(router-outlet) {
                position: relative;
                display: flex;
                flex: 1 1 auto;
            }

            > .notification-messages {
                display: flex;
                flex-direction: column;
                flex: none;
            }
        }
    }
}

// -----------------------------------------------------------------------------------------------------
// @ Theming
// -----------------------------------------------------------------------------------------------------
@include treo-theme {

    $background: map-get($theme, background);
    $foreground: map-get($theme, foreground);
    $primary: map-get($theme, primary);
    $is-dark: map-get($theme, is-dark);

    classy-layout {

        > .wrapper {

            > .header {
                @if ($is-dark) {
                    box-shadow: 0 1px 0 0 map-get($foreground, divider);
                } @else {
                    background: map-get($background, card);
                    @include treo-elevation();
                }
            }
        }
    }

    .clickable-tag-part {
        &:hover {
            background: map-get($background, card);
        }
    }
}
