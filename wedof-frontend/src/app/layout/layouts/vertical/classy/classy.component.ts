import {AfterViewInit, Component, OnDestroy, OnInit, ViewEncapsulation} from '@angular/core';
import {combineLatest, Observable, Subject} from 'rxjs';
import {takeUntil} from 'rxjs/operators';
import {TreoMediaWatcherService} from '@treo/services/media-watcher';
import {TreoNavigationItem, TreoNavigationService} from '@treo/components/navigation';
import {NavigationService} from '../../../../shared/navigation/navigation.service';
import {User} from 'app/shared/api/models/user';
import '../../../../shared/utils/date-extension-utils';
import {UserMessageService} from '../../../../shared/api/services/user-message.service';
import {UserMessage} from '../../../../shared/api/models/user-message';
import {Connection, ConnectionState, DataProviderConfig, DataProviders} from '../../../../shared/api/models/connection';
import {Select} from '@ngxs/store';
import {UserState} from '../../../../shared/api/state/user.state';
import {Organism} from '../../../../shared/api/models/organism';
import {ActivatedRoute} from '@angular/router';
import {ConnectionsState} from '../../../../shared/api/state/connections.state';
import {OrganismState} from '../../../../shared/api/state/organism.state';
import {SubscriptionState} from '../../../../shared/api/state/subscription.state';
import {Subscription} from '../../../../shared/api/models/subscription';
import moment from 'moment';
import {environment} from '../../../../../environments/environment';
import {AuthService} from '../../../../core/auth/auth.service';
import {HttpClient} from '@angular/common/http';
import {TranslateService} from '@ngx-translate/core';
import {OrganismService} from '../../../../shared/api/services/organism.service';
import {SwPush} from '@angular/service-worker';
import {UserService} from '../../../../shared/api/services/user.service';

@Component({
    selector: 'classy-layout',
    templateUrl: './classy.component.html',
    styleUrls: ['./classy.component.scss'],
    encapsulation: ViewEncapsulation.None,
})
export class ClassyLayoutComponent implements OnInit, OnDestroy, AfterViewInit {

    isScreenSmall: boolean;
    displayLayout: boolean;
    user: User;
    subscription: Subscription;
    organism: Organism;
    navigation: TreoNavigationItem[];
    requiredConnections: Connection[];
    verticalNavigationOpened: boolean;
    globalMessages: UserMessage[];
    myMessages: UserMessage[];
    showDemoText = true;
    isDev: boolean;
    isAuthenticated: boolean;
    wedofVersion: string;
    releaseDate: string;

    @Select(UserState.user) user$: Observable<User>;
    @Select(OrganismState.organism) organism$: Observable<Organism>;
    @Select(SubscriptionState.subscription) subscription$: Observable<Subscription>;
    @Select(ConnectionsState.connections) connections$: Observable<Connection[]>;

    private _unsubscribeAll = new Subject<void>();

    constructor(
        private swPush: SwPush,
        private _activatedRoute: ActivatedRoute,
        public navigationService: NavigationService,
        private _treoMediaWatcherService: TreoMediaWatcherService,
        private _treoNavigationService: TreoNavigationService,
        private _messageService: UserMessageService,
        private _organismService: OrganismService,
        private httpClient: HttpClient,
        private _authService: AuthService,
        private _translateService: TranslateService,
        private _userService: UserService
    ) {
        this.isDev = environment.development;
        this.wedofVersion = ``;
        this.releaseDate = ``;
    }

    ngAfterViewInit(): void {
        if (this.swPush.isEnabled) {
            this.swPush.notificationClicks.subscribe((result) => {
                console.log('clicked', result);
            });
        }
    }

    ngOnInit(): void {

        if (this.isDev) {
            this._authService.check().subscribe(isAuthenticated => {
                this.isAuthenticated = isAuthenticated;
            });
        }

        this.httpClient.get<any>('/app/public/version').subscribe(data => {
            this.wedofVersion = data.wedofVersion;
            this.releaseDate = data.releaseDate;
        });

        this._activatedRoute.data.subscribe((data) => {
            this.navigation = data.navigation;
        });

        this.navigationService.displayLayout.subscribe((displayLayout: boolean) => {
            setTimeout(() => {
                this.displayLayout = displayLayout;
            });
        });

        this._treoMediaWatcherService.onMediaChange$
            .pipe(takeUntil(this._unsubscribeAll))
            .subscribe(({matchingAliases}) => {
                this.isScreenSmall = matchingAliases.includes('lt-md');
                const navigation = this._treoNavigationService.getComponent('mainNavigation');
                this.verticalNavigationOpened = navigation ? navigation._opened : true;
            });

        // Don't combineLatest on purpose because we don't want to wait all the next() before displaying data
        this.user$.pipe(takeUntil(this._unsubscribeAll)).subscribe(user => {
            this.user = user;
        });

        this.organism$.pipe(takeUntil(this._unsubscribeAll)).subscribe((organism) => {
            this.organism = organism;
        });

        this.subscription$.pipe(takeUntil(this._unsubscribeAll)).subscribe((subscription) => {
            this.subscription = subscription;
            this.showDemoText = moment(this.subscription.createdOn).add(45, 'days').toDate() > new Date();
        });

        this.connections$.pipe(takeUntil(this._unsubscribeAll)).subscribe((connections) => {
            const requiredDataProviders = this._organismService.getDataProvidersForOrganism(this.organism, true);
            this.requiredConnections = connections.filter(
                connection => requiredDataProviders.includes(connection.dataProvider)
            );
        });

        combineLatest([
            this._messageService.listGlobalMessages(),
            this._messageService.myMessages()
        ]).pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe(([globalMessages, myMessages]) => {
            this.globalMessages = globalMessages;
            this.myMessages = myMessages;
        });
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    toggleNavigation(key): void {
        const navigation = this._treoNavigationService.getComponent(key);
        if (navigation) {
            navigation.toggle();
        }
        this.verticalNavigationOpened = navigation._opened;
    }

    getCalLinkUrl(): string {
        return 'team/commercial/demo?' +
            'name=' + this.user.name
            + '&email=' + this.user.email
            + '&phone=' + this.user.phoneNumber
            + '&organism=' + this.organism.name
            + '&siret=' + this.organism.siret;
    }

    public hasARequiredConnectionNotActive(connections: Connection[]): Connection[] {
        return connections.filter(connection => {
            return this.requiredConnections.includes(connection)
                && connection.existAtDataProvider === true
                && [ConnectionState.INACTIVE, ConnectionState.FAILED, ConnectionState.REVOKED].includes(connection.state);
        });
    }

    public getTooltipForRequiredConnectionNotActive(_connections: Connection[]): string {
        let tooltip = '';
        const connections = this.hasARequiredConnectionNotActive(_connections);
        if (connections) {
            tooltip += this._translateService.instant('auth.connection.existAtDataProvider');
            tooltip += ' (' + connections.map(connection => DataProviderConfig[connection.dataProvider].name).join(', ') + ')';
        }
        return tooltip.trim();
    }

    getSubscription(): void {
        if (Notification.permission === 'default') {
            Notification.requestPermission().then(() => {
                this.requestSubscription();
            }).catch(() => {
                // show permission denied error
            });
        } else if (Notification.permission === 'denied') {
            // show permission is denied, please allow it error
        } else {
            this.requestSubscription();
        }
    }

    requestSubscription(): void {
        if (this.swPush.isEnabled && environment.production) {
            try {
                this.swPush.requestSubscription({
                        serverPublicKey: environment.vapid.publicKey
                    }
                ).then((subscription) => {
                    console.log(subscription.toJSON());
                    this._userService.subscribeToPushNotifications(subscription).subscribe();
                }).catch(console.error);
            } catch (e) {
                console.log(e);
            }
        }
    }

    isAdminView(): boolean {
        return this._authService.adminView === String(true);
    }
}
