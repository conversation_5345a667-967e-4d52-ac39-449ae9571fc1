import {Component, <PERSON><PERSON><PERSON>roy, OnInit} from '@angular/core';
import {combineLatest, Observable, of, Subject} from 'rxjs';
import {switchMap, takeUntil} from 'rxjs/operators';

import {Organism} from '../../shared/api/models/organism';
import {User} from '../../shared/api/models/user';
import {MatDialog, MatDialogRef} from '@angular/material/dialog';
import {
    Connection,
    ConnectionState,
    ConnectionType,
    DataProviderConfig,
    DataProviders
} from '../../shared/api/models/connection';
import {DialogConnectionAuthComponent} from '../../shared/connection/dialog-connection-auth/dialog-connection-auth.component';
import {Select, Store} from '@ngxs/store';
import {UserState} from '../../shared/api/state/user.state';
import {SubscriptionState} from '../../shared/api/state/subscription.state';
import {Subscription} from '../../shared/api/models/subscription';
import {ConnectionsState, FetchConnections} from '../../shared/api/state/connections.state';
import {OrganismState} from '../../shared/api/state/organism.state';
import {ActivatedRoute, Router} from '@angular/router';
import {TranslateService} from '@ngx-translate/core';
import {OrganismService} from '../../shared/api/services/organism.service';
import {DialogOrganismCollectInformationsComponent} from './organism-form-page/dialog-organism-collect-informations/dialog-organism-collect-informations.component';

@Component({
    selector: 'app-profile',
    templateUrl: './profile.component.html',
    styleUrls: ['./profile.component.scss']
})
export class ProfileComponent implements OnInit, OnDestroy {

    public dialogRef: MatDialogRef<DialogConnectionAuthComponent>;

    @Select(UserState.user) user$: Observable<User>;
    @Select(SubscriptionState.subscription) subscription$: Observable<Subscription>;
    @Select(ConnectionsState.connections) connections$: Observable<Connection[]>;
    @Select(OrganismState.organism) organism$: Observable<Organism>;

    private _unsubscribeAll = new Subject<void>();

    user: User;
    connections: Connection[];
    subscription: Subscription;
    currentOrganism: Organism;
    dataProvidersWithAuthentication: DataProviders[];
    dataProviders = DataProviders;
    connectionState = ConnectionState;
    DataProviderConfig = DataProviderConfig;

    constructor(
        private _dialog: MatDialog,
        private _store: Store,
        private _router: Router,
        private _activatedRoute: ActivatedRoute,
        private _translateService: TranslateService,
        private _organismService: OrganismService
    ) {
    }

    ngOnInit(): void {
        combineLatest([
            this.user$,
            this.organism$,
            this.subscription$,
            this.connections$
        ]).pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe(([user, organism, subscription, connections]) => {
            this.user = user;
            this.currentOrganism = organism;
            this.subscription = subscription;
            this.connections = connections;
            this.dataProvidersWithAuthentication = this._organismService.getDataProvidersForOrganism(organism, false, true);

            // only selected organisms can see Kairos
            const hasKairosEnabled = connections.find(connection => {
                return connection.existAtDataProvider === true && connection.dataProvider === DataProviders.KAIROS_AIF;
            })?.existAtDataProvider ?? false;

            if (!hasKairosEnabled) {
                this.dataProvidersWithAuthentication = this.dataProvidersWithAuthentication.filter(dataProvider => dataProvider !== DataProviders.KAIROS_AIF);
            }
            if (!this.currentOrganism.isReseller && !this.currentOrganism._links?.reseller && (!this.currentOrganism.crm || !this.currentOrganism.billingSoftware))  {
                this._dialog.open(DialogOrganismCollectInformationsComponent, {
                    panelClass: ['full-page-scroll-40'],
                    data: {
                        organism: this.currentOrganism
                    }
                });
            }
        });
        const {dataProviderName} = this._activatedRoute.snapshot.params;
        if (dataProviderName) {
            if (this.dataProvidersWithAuthentication.find(dataProviderAuth => dataProviderAuth === dataProviderName)) {
                this.openAuthCheck(dataProviderName);
            } else {
                this._router.navigate(['profil']);
            }
        }
    }

    openAuthCheck(dataProvider: DataProviders): void {
        const dialogRef = this._dialog.open(DialogConnectionAuthComponent, {
            disableClose: true,
            panelClass: ['no-padding-panel', 'full-page-scroll-40'],
            height: 'auto',
            data: {
                dataProvider: dataProvider,
                organism: this.currentOrganism,
                subscription: this.subscription
            }
        });
        dialogRef.afterClosed().pipe(
            switchMap(connectionOk => {
                if (connectionOk) {
                    return this._store.dispatch(new FetchConnections());
                } else {
                    return of();
                }
            })
        ).subscribe();
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    getConnection(dataProviderAuth: DataProviders): Connection {
        return this.connections.find(connection => connection.dataProvider === dataProviderAuth);
    }

    isCardClickable(dataProviderAuth: DataProviders): boolean {
        const connection = this.getConnection(dataProviderAuth);
        return connection?.type === ConnectionType.HABILITATION || connection?.type === ConnectionType.DELEGATION
            ? ![ConnectionState.ACTIVE, ConnectionState.IN_PROGRESS, ConnectionState.REFRESHING].includes(connection?.state)
            : true;
    }

    getIdConnection(dataProvider: string): string {
        return this.connections.filter(connection => connection.dataProvider === dataProvider)[0]?.id
            ? `ID : ${this.connections.filter(connection => connection.dataProvider === dataProvider)[0]?.id}`
            : null;
    }

    getTextHabilitationOrDelegationState(dataProviderAuth: DataProviders): string {
        const connection = this.getConnection(dataProviderAuth);
        return this._translateService.instant('auth.connection.state.' + connection.type + '.' + connection.state, {'username': connection.username});
    }

    hasErrorState(state: ConnectionState): boolean {
        return [ConnectionState.INACTIVE, ConnectionState.FAILED, ConnectionState.REVOKED].includes(state);
    }
}
