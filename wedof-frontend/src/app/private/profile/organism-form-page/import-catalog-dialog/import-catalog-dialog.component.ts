import {Component, Inject, OnInit} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material/dialog';
import {MatSnackBar} from '@angular/material/snack-bar';
import {HttpErrorResponse, HttpResponse} from '@angular/common/http';
import {FormBuilder, FormGroup} from '@angular/forms';
import {TranslateService} from '@ngx-translate/core';
import {ApiError} from '../../../../shared/errors/errors.types';
import {SnackBarComponent} from '../../../../shared/material/snack-bar/snack-bar.component';
import {displayTextSnackBar} from '../../../../shared/utils/displayTextSnackBar';
import {OrganismService} from '../../../../shared/api/services/organism.service';
import {Organism} from '../../../../shared/api/models/organism';
import {UpdatedOrganism} from '../../../../shared/api/state/organism.state';
import {Store} from '@ngxs/store';

@Component({
    selector: 'app-import-catalog-dialog',
    templateUrl: './import-catalog-dialog.component.html',
    styleUrls: ['./import-catalog-dialog.component.scss']
})
export class ImportCatalogDialogComponent implements OnInit {

    loading = false;
    fileToUpload: File | null = null;
    formGroup: FormGroup;
    errorMessages: string[] = [];
    hasErrorTypeFile = false;

    constructor(
        private _store: Store,
        private _snackBar: MatSnackBar,
        private _organismService: OrganismService,
        private _formBuilder: FormBuilder,
        private _translateService: TranslateService,
        public _dialogRef: MatDialogRef<ImportCatalogDialogComponent>,
        @Inject(MAT_DIALOG_DATA) public dialogData: {
            organism: Organism
        },
    ) {
    }

    ngOnInit(): void {
        this.formGroup = this._formBuilder.group({});
    }

    close(): void {
        this._dialogRef.close();
    }

    onFileChange(event): void {
        if (event.target.files.length > 0) {
            this.fileToUpload = event.target.files[0];
            this.hasErrorTypeFile = this.fileToUpload.type !== 'text/xml';
        }
    }

    submit(): void {
        this.errorMessages = [];
        this.loading = true;
        const formData: FormData = new FormData();
        formData.append('fileKey', this.fileToUpload, this.fileToUpload.name);
        this._organismService.importCpfCatalogXml(this.dialogData.organism, this.fileToUpload).subscribe(
            (event) => {
                if (event instanceof HttpResponse && event?.status < 300) {
                    const organism = event.body;
                    this._store.dispatch(new UpdatedOrganism({
                        ...organism // To get new metadata
                    }));
                    this._snackBar.openFromComponent(
                        SnackBarComponent,
                        displayTextSnackBar(this._translateService.instant('private.common.organism.catalog.import.success'), 10000)
                    );
                    this.close();
                }
            },
            (httpErrorResponse: HttpErrorResponse) => {
                this.loading = false;
                this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
            }
        );
    }
}
