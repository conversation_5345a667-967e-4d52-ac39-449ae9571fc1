<app-dialog-layout [title]="'private.common.organism.catalog.import.title' | translate"
                   [actions]="actions"
                   [disabled]="loading" (dialogClose)="close()">
    <form [formGroup]="formGroup" class="flex flex-col mt-3">
        <div class="flex flex-col">
            <mat-label class="flex items-center icon-cool-gray-400">
                {{ 'private.common.organism.catalog.import.sendFile' | translate }}
                <mat-icon [matTooltipPosition]="'above'"
                          [matTooltip]="'private.common.organism.catalog.import.help' | translate"
                          class="ml-1"
                          matSuffix
                          svgIcon="help_outline"></mat-icon>
            </mat-label>
            <input type="file"
                   id="file"
                   accept="text/xml"
                   class="mt-2"
                   (change)="onFileChange($event)"/>
            <mat-error *ngIf="hasErrorTypeFile"
                       class="mt-1 pb-1">
                {{ 'private.common.organism.catalog.import.errorFile' | translate }}
            </mat-error>
        </div>
        <div *ngIf="errorMessages?.length"
             class="flex items-center mt-2">
            <treo-message class="flex-auto" appearance="outline" [showIcon]="false" type="error">
                <ul>
                    <li *ngFor="let errorMessage of errorMessages">
                        {{ errorMessage }}
                    </li>
                </ul>
            </treo-message>
        </div>
        <ng-template #actions>
            <div class="flex flex-row py-2">
                <button type="submit"
                        color="primary"
                        class="mt-2"
                        mat-flat-button
                        (click)="submit()"
                        [disabled]="loading || hasErrorTypeFile">
                    <mat-progress-spinner class="mr-4"
                                          *ngIf="loading"
                                          [diameter]="24"
                                          mode="indeterminate"></mat-progress-spinner>
                    <ng-container *ngIf="!loading">
                        {{ 'private.common.organism.catalog.import.button.import' | translate }}
                    </ng-container>
                </button>
            </div>
        </ng-template>
    </form>
</app-dialog-layout>
