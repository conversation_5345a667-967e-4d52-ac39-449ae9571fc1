import {Component, Input, OnInit} from '@angular/core';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {Organism} from '../../../../shared/api/models/organism';
import {OrganismState, UpdatedOrganism, UpdateOrganism} from '../../../../shared/api/state/organism.state';
import {finalize, mergeMap, takeUntil} from 'rxjs/operators';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../../../shared/errors/errors.types';
import {Select, Store} from '@ngxs/store';
import {Router} from '@angular/router';
import {AppFormFieldData} from '../../../../shared/material/app-form-field/app-form-field.component';
import {FormValidators} from '../../../../shared/api/shared/form-validators';
import {TranslateService} from '@ngx-translate/core';
import {UserState} from '../../../../shared/api/state/user.state';
import {combineLatest, Observable, Subject} from 'rxjs';
import {User} from '../../../../shared/api/models/user';
import {OrganismService} from '../../../../shared/api/services/organism.service';
import {SubscriptionState} from '../../../../shared/api/state/subscription.state';
import {
    Subscription,
    SubscriptionCertifierTypes,
    SubscriptionTrainingTypes
} from '../../../../shared/api/models/subscription';
import {MatSnackBar} from '@angular/material/snack-bar';
import {SnackBarComponent} from '../../../../shared/material/snack-bar/snack-bar.component';
import {displayTextSnackBar} from '../../../../shared/utils/displayTextSnackBar';
import {
    choicesCrm,
    choicesFacturation
} from '../dialog-organism-collect-informations/dialog-organism-collect-informations.component';

@Component({
    selector: 'app-organism-customize',
    templateUrl: './organism-customize.component.html',
    styleUrls: ['./organism-customize.component.scss']
})
export class OrganismCustomizeComponent implements OnInit {

    formGroupCustomize: FormGroup;
    user: User;
    loading = false;
    errorMessages: string[] = [];
    appFormFieldsGroupData: AppFormFieldData[][];
    loadingSendAsEmail = false;
    subscription: Subscription;
    loadingImportCertificationFolders = false;
    subscriptionCertifierTypes = SubscriptionCertifierTypes;

    @Input() organism: Organism;

    @Select(UserState.user) user$: Observable<User>;
    @Select(SubscriptionState.subscription) subscription$: Observable<Subscription>;

    private _unsubscribeAll = new Subject<void>();

    constructor(
        private _fb: FormBuilder,
        private _store: Store,
        private _router: Router,
        private _snackBar: MatSnackBar,
        private _translateService: TranslateService,
        private _organismService: OrganismService,
    ) {
    }

    ngOnInit(): void {
        this.formGroupCustomize = this._fb.group({
            organism: this._fb.group({})
        });
        combineLatest([
            this.user$,
            this.subscription$
        ]).pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe(([user, subscription]) => {
            this.user = user;
            this.subscription = subscription;
            this.initForm();
        });
    }

    initForm(): void {
        const appFormFieldsData: AppFormFieldData[][] = [];
        appFormFieldsData['logoAndColor'] = [
            {
                controlName: 'customColorScheme',
                value: this.organism.customColorScheme ?? this._translateService.instant('private.profile.organism.form.fields.customColorScheme.placeholder'),
                label: 'private.profile.organism.form.fields.customColorScheme.label',
                placeholder: 'private.profile.organism.form.fields.customColorScheme.placeholder',
                type: 'color',
                maxLength: 7,
                validators: [Validators.pattern(FormValidators.HEX_COLOR)],
            }, {
                controlName: 'logo',
                value: this.organism.logo,
                label: 'common.actions.file.label.logo',
                chooseLabel: this._translateService.instant('common.actions.file.choose', {format: '.svg'}),
                removeLabel: this._translateService.instant('common.actions.file.remove', {file: 'logo'}),
                type: 'file',
                icon: 'image',
                removable: true,
                showFilePreview: true,
                fileTypesAccept: ['image/svg+xml']
            }
        ];
        let billingSoftwareOption;
        let crmOption;
        if (this.organism.billingSoftware) {
            const billingSoftwareChoice = choicesFacturation.find((choiceFacturation) => choiceFacturation.value === this.organism.billingSoftware);
            billingSoftwareOption = billingSoftwareChoice ? billingSoftwareChoice.value : 'other';
        } else {
            billingSoftwareOption = null;
        }
        if (this.organism.crm) {
            const crmChoice = choicesFacturation.find((choiceFacturation) => choiceFacturation.value === this.organism.crm);
            crmOption = crmChoice ? crmChoice.value : 'other';
        } else {
            crmOption = null;
        }
        appFormFieldsData['tools'] = [
            {
                controlName: 'billingSoftware',
                value: billingSoftwareOption,
                removable: true,
                label: 'private.profile.organism.form.fields.tools.billingSoftware',
                type: 'select',
                choices: choicesFacturation,
                change: (controlName, newValue, formData) => {
                    const appFormFieldNameBillingSoftwareOther = formData.find(field => field.controlName === 'billingSoftwareOther');
                    if (newValue === 'other') {
                        appFormFieldNameBillingSoftwareOther.removed = false;
                        appFormFieldNameBillingSoftwareOther.required = true;
                    } else {
                        appFormFieldNameBillingSoftwareOther.removed = true;
                        appFormFieldNameBillingSoftwareOther.required = false;
                    }
                    return [appFormFieldNameBillingSoftwareOther];
                },
                colSpan: billingSoftwareOption === 'other' ? 3 : 6
            },
            {
                controlName: 'billingSoftwareOther',
                removed: billingSoftwareOption !== 'other',
                value: this.organism.billingSoftware,
                label: this._translateService.instant('private.profile.organism.form.fields.tools.billingSoftware') +
                    ' - ' + this._translateService.instant('common.actions.other'),
                type: 'text',
                placeholder: 'Indiquez votre logiciel de facturation',
                colSpan: billingSoftwareOption === 'other' ? 3 : 6
            },
            {
                controlName: 'crm',
                value: crmOption,
                removable: true,
                label: 'private.profile.organism.form.fields.tools.crm',
                type: 'select',
                choices: choicesCrm,
                change: (controlName, newValue, formData) => {
                    const appFormFieldNameCrmOther = formData.find(field => field.controlName === 'crmOther');
                    if (newValue === 'other') {
                        appFormFieldNameCrmOther.removed = false;
                        appFormFieldNameCrmOther.required = true;
                    } else {
                        appFormFieldNameCrmOther.removed = true;
                        appFormFieldNameCrmOther.required = false;
                    }
                    return [appFormFieldNameCrmOther];
                },
                colSpan: crmOption === 'other' ? 3 : 6
            },
            {
                controlName: 'crmOther',
                removed: crmOption !== 'other',
                value: this.organism.crm,
                label: this._translateService.instant('private.profile.organism.form.fields.tools.crm') + ' - ' + this._translateService.instant('common.actions.other'),
                type: 'text',
                placeholder: 'Indiquez votre CRM',
                colSpan: crmOption === 'other' ? 3 : 6
            }
        ];
        this.appFormFieldsGroupData = appFormFieldsData;
    }

    formGroupCustomizeSubmit(): void {
        this.errorMessages = [];
        const formValue = this.formGroupCustomize.getRawValue();
        const organism: Organism = {
            siret: this.organism.siret,
            logo: formValue.organism.logo,
            customColorScheme: formValue.organism.customColorScheme,
            billingSoftware: formValue.organism.billingSoftwareOther ? formValue.organism.billingSoftwareOther : formValue.organism.billingSoftware,
            crm: formValue.organism.crmOther ? formValue.organism.crmOther : formValue.organism.crm
        };
        this._store.dispatch(new UpdateOrganism(organism.siret, organism)).pipe(
            mergeMap(() => this._store.selectOnce(OrganismState.organism)),
            finalize(() => this.loading = false)
        ).subscribe(
            () => this._router.navigate(['profil']),
            (httpErrorResponse: HttpErrorResponse) => {
                this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
            }
        );
    }

    verifyEmailIdentity(): void {
        const email = this.organism.emails[0];
        this.loadingSendAsEmail = true;
        this._organismService.verifySendAs(email).subscribe(
            () => {
                this._store.dispatch(new UpdatedOrganism({
                    ...this.organism,
                    sendAs: []
                }));
            },
            (httpErrorResponse: HttpErrorResponse) => {
                this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
            }
        ).add(() => {
            this.loadingSendAsEmail = false;
        });
    }

    deleteEmailIdentity(): void {
        const email = this.organism.emails[0];
        this.loadingSendAsEmail = true;
        this._organismService.deleteSendAs(email).subscribe(
            () => {
                this._store.dispatch(new UpdatedOrganism({
                    ...this.organism,
                    sendAs: null
                }));
            },
            (httpErrorResponse: HttpErrorResponse) => {
                this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
            }
        ).add(() => {
            this.loadingSendAsEmail = false;
        });
    }

    showCustomizeEmail(): boolean {
        if (!this.user.isOwner) {
            return false;
        }
        return [SubscriptionTrainingTypes.TRIAL, SubscriptionTrainingTypes.ACCESS, SubscriptionTrainingTypes.ACCESS_PLUS, SubscriptionTrainingTypes.STANDARD,
                SubscriptionTrainingTypes.PREMIUM].includes(this.subscription.trainingType) ||
            [SubscriptionCertifierTypes.TRIAL, SubscriptionCertifierTypes.ACCESS,
                SubscriptionCertifierTypes.USAGE, SubscriptionCertifierTypes.UNLIMITED].includes(this.subscription.certifierType);
    }

    getText(): string {
        const state = this.organism.sendAs === null ? 'unknown' : this.organism.sendAs.length === 0 ? 'notActivated' : 'activated';
        return this._translateService.instant('private.common.organism.sendAs.' + state, {
            email: this.organism.sendAs?.length ? this.organism.sendAs[0].email : (this.organism.emails[0] ?? ''),
            name: this.organism.sendAs?.length ? this.organism.sendAs[0].name : ''
        });
    }

    importFolders(event): void {
        if (event.target.files.length > 0) {
            this.loadingImportCertificationFolders = true;
            this._organismService.importCertificationFolders(event.target.files[0], this.organism).subscribe(
                (organism) => {
                    this._store.dispatch(new UpdatedOrganism({
                        ...organism
                    }));
                    this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(
                        this._translateService.instant('private.common.organism.importCertificationFolders.feedback')));
                },
                (httpErrorResponse: HttpErrorResponse) => {
                    this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
                }
            ).add(() => {
                this.loadingImportCertificationFolders = false;
            });
        }
    }

    cancel(): void {
        this._router.navigate(['profil']);
    }
}
