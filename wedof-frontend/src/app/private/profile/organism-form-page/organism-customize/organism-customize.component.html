<treo-card class="p-8 pb-0 w-full flex flex-col">
    <div class="flex items-center mb-2">
        <mat-icon class="mr-3 card-loading-show icon-size-32"
                  svgIcon="group"
                  color="primary">
        </mat-icon>
        <div class="text-xl font-semibold card-loading-show">
            {{ 'private.common.organism.customize' | translate }}
        </div>
    </div>

    <form [formGroup]="formGroupCustomize"
          (submit)="formGroupCustomizeSubmit()"
          class="mt-2">
        <div class="flex flex-col">
            <app-form-fields class="grid grid-cols-6 gap-2"
                             formGroupName="organism"
                             [entity]="organism"
                             [appFormFieldsData]="appFormFieldsGroupData['logoAndColor']"
                             [formGroup]="formGroupCustomize"></app-form-fields>
            <app-form-fields class="grid grid-cols-6 gap-2"
                             formGroupName="organism"
                             [entity]="organism"
                             [appFormFieldsData]="appFormFieldsGroupData['tools']"
                             [formGroup]="formGroupCustomize"></app-form-fields>
        </div>
        <div class="mt-4 mb-2" *ngIf="showCustomizeEmail()">
            <div>
                <div class="mb-1">
                    <label
                        class="text-base font-medium">{{ 'private.common.organism.sendAs.title' | translate }}</label>
                </div>
                <div class="flex items-center">
                    <button *ngIf="organism.sendAs !== []"
                            [disabled]="loadingSendAsEmail"
                            class="bg-gray-200 mr-2"
                            mat-flat-button
                            type="button"
                            (click)="organism.sendAs ? deleteEmailIdentity() : verifyEmailIdentity() ">
                        {{ (organism.sendAs ? 'common.actions.deactivate' : 'common.actions.activate') | translate }}
                    </button>
                    <mat-progress-spinner *ngIf="loadingSendAsEmail" [diameter]="24"
                                          mode="indeterminate"></mat-progress-spinner>
                    <div style="width:600px">
                        <ng-container
                            *ngIf="!loadingSendAsEmail">
                            <p [innerHTML]="getText()"></p>
                        </ng-container>
                    </div>
                </div>
            </div>
        </div>
        <div class="mt-4 mb-2"
             *ngIf="subscription.allowCertifiers && organism.isCertifierOrganism && user.isOwner &&
 [subscriptionCertifierTypes.USAGE.toString(), subscriptionCertifierTypes.UNLIMITED.toString(), subscriptionCertifierTypes.ACCESS].includes(subscription.certifierType)">
            <div>
                <div class="mb-1">
                    <label
                        class="text-base font-medium">{{ 'private.common.organism.importCertificationFolders.title' | translate }}</label>
                </div>
                <div class="flex items-center">
                    <button (click)="importCertificationFolders.click()"
                            [disabled]="loadingImportCertificationFolders || !organism.allowImportCertificationFolders"
                            mat-flat-button
                            type="button"
                            class="bg-gray-200 mr-2">
                        <span *ngIf="!loadingImportCertificationFolders">
                            {{ 'common.actions.export.importExcelButton' | translate }}
                        </span>
                        <mat-progress-spinner *ngIf="loadingImportCertificationFolders"
                                              [diameter]="24"
                                              [mode]="'indeterminate'"></mat-progress-spinner>
                    </button>
                    <input #importCertificationFolders
                           type="file"
                           class="hidden"
                           accept=".xlsx, .xls, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
                           (change)="importFolders($event)">
                    <div>
                        <a
                            href="https://www.wedof.fr/aide/guides/certificateurs/import-dossiers-certification-excel"
                            target="_blank">Documentation et modèle</a>
                    </div>
                </div>
            </div>
        </div>
        <div
            class="flex items-center justify-end border-t -mx-8 mt-5 px-8 py-5 light:bg-cool-gray-50 dark:bg-cool-gray-700">
            <div *ngIf="errorMessages.length" class="flex items-center">
                <treo-message class="flex-auto" appearance="outline" [showIcon]="false" type="error">
                    <ul>
                        <li *ngFor="let errorMessage of errorMessages">
                            {{ errorMessage }}
                        </li>
                    </ul>
                </treo-message>
            </div>
            <button (click)="cancel()"
                    type="button"
                    mat-button>
                {{ 'common.actions.cancel' | translate }}
            </button>
            <button type="submit"
                    class="px-6 ml-3"
                    mat-flat-button color="primary"
                    [disabled]="loading || formGroupCustomize.invalid || !formGroupCustomize.dirty">
                <mat-progress-spinner class="mr-4" *ngIf="loading" [diameter]="24"
                                      mode="indeterminate"></mat-progress-spinner>
                {{ 'common.actions.update' | translate }}
            </button>
        </div>
    </form>
</treo-card>
