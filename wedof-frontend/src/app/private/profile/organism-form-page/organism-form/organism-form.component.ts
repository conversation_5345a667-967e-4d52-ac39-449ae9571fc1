import {HttpErrorResponse} from '@angular/common/http';
import {AfterViewInit, Component, Injector, OnDestroy, OnInit} from '@angular/core';
import {FormGroup, Validators} from '@angular/forms';
import {GeoApiGouvAddress, GeoApiGouvAddressService} from '@placeme/ngx-geo-api-gouv-address';
import {combineLatest, Observable, of, Subject} from 'rxjs';
import {finalize, map, takeUntil} from 'rxjs/operators';
import {Organism, RegistrationFolderFileType} from '../../../../shared/api/models/organism';
import {LocationToStringPipe} from '../../../../shared/pipes/location-to-string.pipe';
import {AbstractFormComponent} from '../../../../shared/material/form/abstract-form.component';
import {ApiError} from '../../../../shared/errors/errors.types';
import {TranslateService} from '@ngx-translate/core';
import {Select, Store} from '@ngxs/store';
import {StateChoices} from '../../../../shared/file/file-type-list/file-type-list.component';
import {RegistrationFolderStateToIconPipe} from '../../../../shared/pipes/registration-folder-state-to-icon.pipe';
import {RegistrationFolderStateToColorPipe} from '../../../../shared/pipes/registration-folder-state-to-color.pipe';
import {RegistrationFolderStates} from '../../../../shared/api/models/registration-folder';
import {OrganismState, UpdateOrganism} from '../../../../shared/api/state/organism.state';
import {Router} from '@angular/router';
import {AppFormFieldData} from '../../../../shared/material/app-form-field/app-form-field.component';
import {FormValidators} from '../../../../shared/api/shared/form-validators';
import {SubscriptionState} from '../../../../shared/api/state/subscription.state';
import {Subscription} from '../../../../shared/api/models/subscription';
import {EntityClass} from '../../../../shared/utils/enums/entity-class';
import {CertificationService} from '../../../../shared/api/services/certification.service';
import {Certification} from '../../../../shared/api/models/certification';
import {ThemePalette} from '@angular/material/core';
import {UserState} from '../../../../shared/api/state/user.state';
import {User} from '../../../../shared/api/models/user';

@Component({
    selector: 'app-organism-form',
    templateUrl: './organism-form.component.html',
    styleUrls: ['./organism-form.component.scss']
})
export class OrganismFormComponent extends AbstractFormComponent<Organism> implements OnInit, OnDestroy, AfterViewInit {

    // to cast string in number in template
    Number = Number;
    @Select(UserState.user) user$: Observable<User>;
    @Select(OrganismState.organism) organism$: Observable<Organism>;
    @Select(SubscriptionState.subscription) subscription$: Observable<Subscription>;

    subscription: Subscription;
    errorMessages: string[] = [];
    vatValue: number;
    organism: Organism;
    certifications: Certification[];
    stateChoicesRegistrationFoldersFileTypes: StateChoices;
    appFormFieldsData: AppFormFieldData[];
    hasQualiopi = false;
    qualiopiDatas: { title: string, color: ThemePalette, icon: string; } [] = [];
    isUserOwner = false;

    private _unsubscribeAll = new Subject<void>();
    readonly EntityClass = EntityClass;

    constructor(
        injector: Injector,
        private _geoApiGouvAddressService: GeoApiGouvAddressService,
        private _locationToStringPipe: LocationToStringPipe,
        private _translateService: TranslateService,
        private _store: Store,
        private _router: Router,
        private _certificationService: CertificationService,
        private _registrationFolderStateToIconPipe: RegistrationFolderStateToIconPipe,
        private _registrationFolderStateToColorPipe: RegistrationFolderStateToColorPipe
    ) {
        super(injector);
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    ngOnInit(): void {
        combineLatest([
            this.organism$,
            this.subscription$,
            this.user$
        ]).pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe(([organism, subscription, user]) => {
            this.organism = organism;
            this.subscription = subscription;
            this.isUserOwner = user.isOwner;
            this.initCertifications();
            this.vatValue = this.organism.vat;
            this.stateChoicesRegistrationFoldersFileTypes = Object.values(RegistrationFolderStates).map(value => ({
                key: this._translateService.instant('private.training-organism.folders.common.state.' + value),
                icon: this._registrationFolderStateToIconPipe.transform(value as string),
                color: this._registrationFolderStateToColorPipe.transform(value as string),
                value: value
            }));
            this.stateChoicesRegistrationFoldersFileTypes.shift(); // remove notProcessed state
            this.initForm();
        });
    }

    ngAfterViewInit(): void {
        // Hack to trigger validation to show validation errors if existing data is invalid
        setTimeout(() => {
            const organismFormGroup = this.formGroup.get('organism') as FormGroup;
            Object.values(organismFormGroup.controls).forEach(control => {
                control.markAsTouched();
            });
        });
    }

    initCertifications(): void {
        this._certificationService.list({
            organismType: 'all',
            siret: this.organism.siret,
            limit: 100
        }).subscribe((certificationsResponse) => {
            this.certifications = certificationsResponse.payload;
        });
    }

    submit(): void {
        this.errorMessages = [];
        this.loading = true;
        const formValue = this.formGroup.getRawValue().organism;
        const organism: Organism = {
            name: formValue.name,
            address: formValue.address,
            postalCode: formValue.postalCode,
            city: formValue.city,
            emails: [formValue.email],
            phones: [formValue.phone],
            urls: [formValue.url],
            siret: formValue.siret,
            uaiNumber: formValue.uaiNumber ?? null,
            agreement: formValue.agreement,
            vat: formValue.vat,
            linkedInPageUrl: formValue.linkedInPageUrl
        };
        this._store.dispatch(new UpdateOrganism(organism.siret, organism)).pipe(
            finalize(() => this.loading = false)
        ).subscribe(
            () => this._router.navigate(['profil']),
            (httpErrorResponse: HttpErrorResponse) => {
                this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
            }
        );
    }

    initForm(): void {
        const organism = this.organism;
        this.formGroup = this._fb.group({
            organism: this._fb.group({})
        });
        const appFormFieldsData: AppFormFieldData[] = [
            {
                controlName: 'name',
                disabled: true,
                label: 'private.profile.organism.form.fields.name.label',
                type: 'text',
                required: true,
                icon: 'business',
                colSpan: 3
            },
            {
                controlName: 'siret',
                disabled: true,
                label: 'private.profile.organism.form.fields.siret.label',
                type: 'text',
                required: true,
                icon: 'fingerprint',
                colSpan: 3
            },
            {
                controlName: 'agreement',
                disabled: !!organism.agreement,
                label: 'private.profile.organism.form.fields.agreement.label',
                type: 'text',
                required: organism.isTrainingOrganism,
                icon: 'loyalty',
                validators: [Validators.pattern(FormValidators.AGREEMENT_PATTERN)],
                validatorsMessages: {
                    pattern: 'private.profile.organism.form.fields.agreement.errors.pattern'
                },
                colSpan: 3
            },
            {
                controlName: 'uaiNumber',
                label: 'private.profile.organism.form.fields.uaiNumber.label',
                type: 'text',
                removed: !organism.qualiopiFormationApprentissage,
                disabled: !this.isUserOwner,
                icon: 'pin',
                help : 'private.profile.organism.form.fields.uaiNumber.help',
                colSpan: 3,
                placeholder: '0000000A'
            },
            {
                controlName: 'vat',
                label: 'private.profile.organism.form.fields.tvaTaux.label',
                type: 'select',
                required: true,
                icon: 'corporate_fare',
                placeholder: this._translateService.instant('private.common.form.placeholder'),
                choices: [
                    {
                        key: this._translateService.instant('private.profile.organism.form.fields.tvaTaux.option.0'),
                        value: 0
                    },
                    {
                        key: this._translateService.instant('private.profile.organism.form.fields.tvaTaux.option.5-5'),
                        value: 5.5
                    },
                    {
                        key: this._translateService.instant('private.profile.organism.form.fields.tvaTaux.option.20'),
                        value: 20
                    }
                ],
                colSpan: 3
            },
            {
                controlName: 'location',
                label: 'private.profile.organism.form.fields.location.label',
                required: !organism.address,
                placeholder: this._locationToStringPipe.transform(organism),
                type: 'search',
                searchNoEntriesFoundLabel: 'private.profile.organism.form.fields.location.error',
                searchMethod: (q) => q ? this._geoApiGouvAddressService.query({q}).pipe(
                    map((searchResultsReponse) => {
                        return searchResultsReponse.features.map(f => f.properties);
                    })
                ) : of([]),
                searchResultFormatter: (geoApiGouvAddress: GeoApiGouvAddress) => geoApiGouvAddress.label,
                change: (controlName, newAddress, formData) => {
                    const appFormFieldAddress = formData.find(field => field.controlName === 'address');
                    const appFormFieldCity = formData.find(field => field.controlName === 'city');
                    const appFormFieldPostalCode = formData.find(field => field.controlName === 'postalCode');
                    appFormFieldAddress.value = newAddress.name;
                    appFormFieldCity.value = newAddress.city;
                    appFormFieldPostalCode.value = newAddress.postcode;
                    return [appFormFieldAddress, appFormFieldCity, appFormFieldPostalCode];
                },
            },
            {
                controlName: 'address',
                disabled: true,
                label: 'private.profile.organism.form.fields.address.label',
                type: 'text',
                required: true,
                icon: 'house'
            },
            {
                controlName: 'postalCode',
                disabled: true,
                label: 'private.profile.organism.form.fields.postalCode.label',
                type: 'text',
                required: true,
                icon: 'markunread_mailbox',
                colSpan: 3
            },
            {
                controlName: 'city',
                disabled: true,
                label: 'private.profile.organism.form.fields.city.label',
                type: 'text',
                required: true,
                icon: 'location_city',
                colSpan: 3
            },
            {
                controlName: 'url',
                value: organism.urls[0],
                label: 'private.profile.organism.form.fields.url.label',
                type: 'text',
                icon: 'link',
                validators: [Validators.pattern(FormValidators.URL_PATTERN)],
                validatorsMessages: {
                    pattern: 'common.errors.url'
                }
            },
            {
                controlName: 'email',
                value: organism.emails[0],
                label: 'private.profile.organism.form.fields.email.label',
                type: 'email',
                required: true,
                disabled: organism.sendAs !== null,
                icon: 'email',
                validators: [Validators.email],
                validatorsMessages: {
                    email: 'private.profile.organism.form.fields.email.error'
                },
                colSpan: 3
            },
            {
                controlName: 'phone',
                value: organism.phones[0],
                label: 'private.profile.organism.form.fields.phone.label',
                type: 'tel',
                required: true,
                icon: 'phone',
                validators: [Validators.pattern(FormValidators.PHONE_PATTERN), Validators.minLength(10), Validators.maxLength(10)],
                validatorsMessages: {
                    pattern: 'private.profile.organism.form.fields.phone.error'
                },
                colSpan: 3
            },
            {
                controlName: 'linkedInPageUrl',
                value: organism.linkedInPageUrl,
                label: 'private.profile.organism.form.fields.linkedinPageUrl.label',
                placeholder: 'private.profile.organism.form.fields.linkedinPageUrl.placeholder',
                type: 'url',
                required: false,
                icon: 'feather:linkedin',
                validators: [Validators.pattern(FormValidators.LINKEDIN_PAGE_PATTERN)],
                validatorsMessages: {
                    pattern: 'private.profile.organism.form.fields.linkedinPageUrl.error'
                }
            }
        ];
        this.appFormFieldsData = appFormFieldsData.filter(value => value != null); // Hack: do it in two steps to avoid complex typescript error
        this.hasQualiopi = organism.qualiopiVAE || organism.qualiopiFormationApprentissage || organism.qualiopiBilanCompetences || organism.qualiopiTrainingAction;
        if (this.hasQualiopi) {
            this.qualiopiDatas = [
                {
                    title: 'private.common.organism.qualiopi.trainingAction',
                    color: organism.qualiopiTrainingAction ? 'primary' : 'warn',
                    icon: organism.qualiopiTrainingAction ? 'check_circle' : 'cancel',
                },
                {
                    title: 'private.common.organism.qualiopi.bilanCompetences',
                    color: organism.qualiopiBilanCompetences ? 'primary' : 'warn',
                    icon: organism.qualiopiBilanCompetences ? 'check_circle' : 'cancel',
                },
                {
                    title: 'private.common.organism.qualiopi.vae',
                    color: organism.qualiopiVAE ? 'primary' : 'warn',
                    icon: organism.qualiopiVAE ? 'check_circle' : 'cancel',
                },
                {
                    title: 'private.common.organism.qualiopi.formationApprentissage',
                    color: organism.qualiopiFormationApprentissage ? 'primary' : 'warn',
                    icon: organism.qualiopiFormationApprentissage ? 'check_circle' : 'cancel',
                }
            ];
        }
    }

    onRegistrationFolderFileTypeListUpdated(registrationFolderFileTypes: RegistrationFolderFileType[]): void {
        this._store.dispatch(new UpdateOrganism(this.organism.siret, {
            siret: this.organism.siret,
            registrationFolderFileTypes: registrationFolderFileTypes
        }));
    }

    cancel(): void {
        this._router.navigate(['profil']);
    }
}
