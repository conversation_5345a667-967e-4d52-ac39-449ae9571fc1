<div class="content-layout fullwidth-basic-normal-scroll">
    <div class="main">
        <div class="content flex flex-wrap">
            <div class="p-4 w-full gt-md:w-1/2">
                <treo-card class="p-8 pb-0 w-full flex flex-col">
                    <div class="flex justify-between mb-2">
                        <div class="flex items-center mb-2">
                            <mat-icon class="mr-3 card-loading-show icon-size-32"
                                      svgIcon="group"
                                      color="primary">
                            </mat-icon>
                            <div class="text-xl font-semibold card-loading-show">
                                {{ 'private.common.organism.organism' | translate }}
                            </div>
                        </div>
                        <img class="image cursor-pointer" *ngIf="hasQualiopi"
                             src="assets/images/qualiopi/qualiopi.png" style="width:150px">
                    </div>
                    <treo-message class="text-center mb-2"
                                  appearance="outline" [showIcon]="false" type="info">
                        <p [innerHTML]="'private.common.organism.updateSiret' | translate"></p>
                    </treo-message>
                    <form class="w-full flex flex-col"
                          (submit)="submit()"
                          [formGroup]="formGroup">
                        <app-form-fields formGroupName="organism"
                                         class="grid grid-cols-6 gap-2"
                                         [entity]="organism"
                                         [appFormFieldsData]="appFormFieldsData"
                                         [formGroup]="formGroup">
                        </app-form-fields>
                        <table class="w-full mt-2 mb-2 table">
                            <tbody>
                            <tr *ngFor="let qualiopiData of qualiopiDatas">
                                <td class="font-semibold">{{qualiopiData.title | translate}}
                                </td>
                                <td class="text-center">
                                    <mat-icon [color]="qualiopiData.color">
                                        {{qualiopiData.icon}}</mat-icon>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <div
                            class="flex items-center justify-end border-t -mx-8 mt-5 px-8 py-5 light:bg-cool-gray-50 dark:bg-cool-gray-700">
                            <div *ngIf="errorMessages.length" class="flex items-center">
                                <treo-message class="flex-auto" appearance="outline" [showIcon]="false" type="error">
                                    <ul>
                                        <li *ngFor="let errorMessage of errorMessages">
                                            {{ errorMessage }}
                                        </li>
                                    </ul>
                                </treo-message>
                            </div>
                            <button (click)="cancel()" type="button" mat-button>
                                {{ 'common.actions.cancel' | translate}}
                            </button>
                            <button type="submit" class="px-6 ml-3" mat-flat-button color="primary"
                                    [disabled]="loading || formGroup.invalid || !formGroup.dirty">
                                <mat-progress-spinner class="mr-4" *ngIf="loading" [diameter]="24"
                                                      mode="indeterminate"></mat-progress-spinner>
                                {{ 'common.actions.update' | translate}}
                            </button>
                        </div>
                    </form>
                </treo-card>
            </div>
            <div class="p-4 w-full gt-md:w-1/2">
                <app-organism-customize [organism]="organism"></app-organism-customize>
                <app-catalog-mcf *ngIf="organism.isTrainingOrganism" [organism]="organism"></app-catalog-mcf>
            </div>
            <div class="p-4 w-full gt-md:w-1/2">
                <app-organism-users-list [organism]="organism"></app-organism-users-list>
            </div>
            <div class="p-4 w-full gt-md:w-1/2">
                <treo-card class="p-8 w-full flex flex-col">
                    <div class="flex items-center mb-2">
                        <mat-icon class="mr-3 card-loading-show icon-size-32"
                                  svgIcon="library_books"
                                  color="primary">
                        </mat-icon>
                        <div class="text-xl font-semibold card-loading-show">
                            {{'private.common.organism.manageMyRegistrationFolderFiles' | translate}}
                        </div>
                    </div>
                    <app-file-type-list [isCard]="false"
                                        [icon]="'checklist_rtl'"
                                        [title]="'private.common.fileTypes.title' | translate"
                                        [subtitle]="'private.common.organism.registrationFolderFileTypesSubtitle' | translate"
                                        [displayedColumns]="['allowAttendeeOrPartner', 'name', 'toState', 'actions']"
                                        [entityId]="Number(organism.siret)"
                                        [entityClass]="'Organism'"
                                        [entityField]="'registrationFolderFileTypes'"
                                        [fileTypes]="organism.registrationFolderFileTypes"
                                        [stateChoices]="stateChoicesRegistrationFoldersFileTypes"
                                        [stateChoicesGenerated]="stateChoicesRegistrationFoldersFileTypes"
                                        [toStateLabel]="'private.training-organism.folders.common.state.'"
                                        [isDefaultCertifierOrOrganism]="true"
                                        [allowExtraFields]="true"
                                        (fileTypeListUpdated)="onRegistrationFolderFileTypeListUpdated($event)"
                                        [certifications]="certifications">
                    </app-file-type-list>
                </treo-card>
            </div>
            <div class="p-4 w-full gt-md:w-1/2" *ngIf="subscription.allowCertifiers">
                <app-certifier-cdc [organism]="organism" [subscription]="subscription"></app-certifier-cdc>
            </div>
            <div class="p-4 w-full gt-md:w-1/2">
                <app-metadata-card [entity]="organism"
                                   [organismSiret]="organism.siret"
                                   [entityClass]="EntityClass.ORGANISM"
                                   [showInSide]="false"></app-metadata-card>
            </div>
        </div>
    </div>
</div>
