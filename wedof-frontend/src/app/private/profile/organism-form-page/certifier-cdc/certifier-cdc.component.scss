::ng-deep {
    .app-form-field-radio-inline > div {
        display: grid;
        grid-template-columns: 1fr min-content;
        grid-column-gap: 0.75rem;
        height: 100%; // Required to set height + scroll on parent
        align-items: center;

        .mat-radio-group {
            margin-top: 0 !important;
        }
    }

    .matErrorFormField {
        margin-top: 0 !important;
    }
}

.app-form-fields-certifications {
    max-height: 350px;
    overflow: auto;
}
