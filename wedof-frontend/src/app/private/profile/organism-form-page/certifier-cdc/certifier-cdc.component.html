<treo-card class="p-8 pb-0 w-full flex flex-col">

    <div class="flex items-center mb-2">
        <mat-icon class="mr-3 card-loading-show icon-size-32"
                  svgIcon="verified_user"
                  color="primary">
        </mat-icon>
        <div class="text-xl font-semibold card-loading-show">
            {{ 'private.common.certification.exportXml' | translate }}
        </div>
    </div>

    <div *ngIf="errorStats?.wedof?.totalCount > 0 || errorStats?.external?.totalCount"
         class="mt-5 mb-5">
        <h5 class="mb-2">{{ 'auth.cdc.errorRate.title' | translate }}</h5>
        <div *ngIf="errorStats?.wedof?.totalCount > 0">
            <div>
                <span class="font-medium">{{ 'auth.cdc.errorRate.wedof.rate' | translate }}</span>
                {{ 'auth.cdc.errorRate.wedof.details' | translate : errorStats.wedof }}
            </div>
            <span [ngClass]="errorStats.wedof.errorRate > 8 ? 'text-warn' : 'text-green'"
                  [matTooltip]="'auth.cdc.errorRate.tooltip' | translate"
                  matTooltipPosition="right">
                {{ errorStats.wedof.errorRate | number: '1.0-2' }}%
            </span>
        </div>
        <div *ngIf="errorStats?.external?.totalCount"
             [ngClass]="{'mt-1': errorStats?.wedof?.totalCount > 0}">
            <div>
                <span class="font-medium">{{ 'auth.cdc.errorRate.external.rate' | translate }}</span>
                {{ 'auth.cdc.errorRate.external.details' | translate : errorStats.external }}
            </div>
            <span [ngClass]="errorStats.external.errorRate > 8 ? 'text-warn' : 'text-green'"
                  [matTooltip]="'auth.cdc.errorRate.tooltip' | translate"
                  matTooltipPosition="right">
                {{ errorStats.external.errorRate | number: '1.0-2' }}%
            </span>
        </div>
    </div>

    <form [formGroup]="formGroup" class="flex flex-col mb-2">
        <h5>{{ 'auth.cdc.dialog.title' | translate }}</h5>
        <mat-form-field class="pb-2 input-line">
            <mat-label> {{ 'auth.cdc.dialog.contractId.label' | translate }}</mat-label>
            <input formControlName="cdcContractId"
                   type="text"
                   [readonly]="organism.accrochageDelegationDate"
                   matInput
                   placeholder="MCFCER000123">
            <mat-icon [matTooltipPosition]="'above'"
                      [matTooltip]="'auth.cdc.dialog.contractId.tooltip' | translate"
                      matPrefix
                      svgIcon="help_outline"></mat-icon>
            <mat-error class="pb-1">
                {{ 'auth.cdc.dialog.contractId.error' | translate }}
            </mat-error>
        </mat-form-field>
        <mat-form-field class="pb-2 input-line">
            <mat-label> {{ 'auth.cdc.dialog.clientId.label' | translate }}</mat-label>
            <input formControlName="cdcClientId"
                   type="text"
                   [readonly]="organism.accrochageDelegationDate"
                   matInput
                   placeholder="12ABC345">
            <mat-icon [matTooltipPosition]="'above'"
                      [matTooltip]="'auth.cdc.dialog.clientId.tooltip' | translate"
                      matPrefix
                      svgIcon="help_outline"></mat-icon>
            <mat-error class="pb-1">
                {{ 'auth.cdc.dialog.clientId.error' | translate }}
            </mat-error>
        </mat-form-field>
        <div>
            Documentation : <a href="https://www.wedof.fr/aide/guides/certificateurs/statut-accrochage" target="_blank">
                Aide Statut d'accrochage</a>
        </div>
        <ng-container *ngIf="organism.accrochageDelegationDate">
            <h5>{{ 'auth.cdc.dialog.delegationAccrochageTitle' | translate }}</h5>
            <p class="mt-2"
               [innerHTML]="'auth.cdc.dialog.delegationAccrochageData' | translate : {date : organism.accrochageDelegationDate | date: 'dd/MM/yyyy'}"></p>
                <app-form-fields *ngIf="!loadingCertifications"
                                 class="mt-5 mb-2 app-form-fields-certifications"
                                 formGroupName="settings"
                                 [appFormFieldsData]="appFormFieldsData"
                                 [formGroup]="formGroup">
                </app-form-fields>
        </ng-container>
        <div class="flex justify-end">
            <div *ngIf="errorMessages?.length" class="flex items-center">
                <treo-message class="flex-auto" appearance="outline" [showIcon]="false" type="error">
                    <ul>
                        <li *ngFor="let errorMessage of errorMessages">
                            {{ errorMessage }}
                        </li>
                    </ul>
                </treo-message>
            </div>
            <div *ngIf="errorMessagesCertifications?.length" class="flex items-center">
                <treo-message class="flex-auto" appearance="outline" [showIcon]="false" type="error">
                    <ul>
                        <li *ngFor="let errorMessageCertification of errorMessagesCertifications">
                            {{ errorMessageCertification }}
                        </li>
                    </ul>
                </treo-message>
            </div>

        </div>
    </form>

    <app-subscription-small-card *ngIf="!subscription.allowCertifierPlus"
                                 [type]="'accrochage'"
                                 [organism]="organism"
                                 [fromPage]="'accrochageDialog'"
                                 [subscription]="subscription">
    </app-subscription-small-card>

    <app-wrapper-spinner [active]="loadingCdcFiles"
                         class="mt-6">
        <div class="flex items-center mb-5">
            <h5>{{ 'auth.cdc.dialogAccrochage.cdcFilesTitle' | translate }}</h5>
            <div class="ml-auto">
                <app-table-multiple-filter #stateFilterSelector
                                           [filters]="stateFilters"
                                           [title]="'état'"
                                           (selectFilters)="onFilterChange($event)">
                </app-table-multiple-filter>
            </div>
        </div>

        <ul *ngIf="cdcFiles?.length">
            <li *ngFor="let cdcFile of cdcFiles">
                <div class="flex justify-between mb-3">
                    <div>
                        <p><span
                            class="font-semibold">{{ 'auth.cdc.cdcFileStates.' + cdcFile.state | translate }}</span>
                            - {{ cdcFile.name }}.xml</p>
                        <p class="text-secondary" [ngSwitch]="cdcFile.state">
                            <span *ngSwitchCase="cdcFileStates.PROCESSED">
                                {{ 'auth.cdc.dialogAccrochage.submissionDate' | translate }} {{ cdcFile.submissionDate | date: 'dd/MM/yyyy' }}
                            </span>
                            <span *ngSwitchCase="cdcFileStates.ABORTED">
                                {{ 'auth.cdc.dialogAccrochage.abortedOn' | translate }} {{ cdcFile.stateLastUpdate | date: 'dd/MM/yyyy' }}
                            </span>
                            <span *ngSwitchDefault>
                                {{ 'auth.cdc.dialogAccrochage.createdOn' | translate }} {{ cdcFile.createdOn | date: 'dd/MM/yyyy' }}
                            </span>
                        </p>
                    </div>
                    <div class="flex">
                        <div class="flex flex-col">
                            <button
                                *ngIf="cdcFile.hasCertificationFolderError && cdcFile.state === cdcFileStates.PROCESSED;"
                                type="button"
                                mat-flat-button>
                                <a class="no-underline"
                                   href="/certification/dossiers/kanban?cdcFile={{ cdcFile.id }}&cdcState=processedKo">{{ 'auth.cdc.dialogAccrochage.linkShowFoldersErrors' | translate }}</a>
                            </button>
                            <button
                                *ngIf="canUpdateCdcFile(cdcFile) && (cdcFile.state === cdcFileStates.EXPORTED || cdcFile.state === cdcFileStates.PROCESSED)"
                                (click)="openAccrochageDialog(cdcFile)"
                                type="button"
                                [color]="cdcFile.state === cdcFileStates.EXPORTED ? 'primary' : undefined"
                                mat-flat-button>
                                {{ (cdcFile.state === cdcFileStates.EXPORTED ? 'auth.cdc.dialogAccrochage.title' : 'auth.cdc.dialogAccrochage.reUploadXml') | translate }}
                            </button>
                        </div>
                        <app-cdc-file-menu class="ml-1" *ngIf="cdcFile.state ===  cdcFileStates.EXPORTED"
                                           (processedCdcFile)="onProcessedCdcFile($event)"
                                           [cdcFile]="cdcFile"></app-cdc-file-menu>
                    </div>
                </div>
            </li>
        </ul>
        <div *ngIf="!cdcFiles?.length">
            <p class="text-secondary">{{ 'auth.cdc.dialogAccrochage.nofile' | translate }}</p>
        </div>
    </app-wrapper-spinner>
    <div class="mt-4 mb-1 flex flex-col" *ngIf="subscription.allowCertifierPlus">
        <div *ngIf="!ingestXmlDone" class="flex flex-col">
            <label class="text-base font-medium mb-2" for="ingestXmlFile">
                {{ 'auth.cdc.dialogAccrochage.ingestXml' | translate }}
            </label>
            <treo-message type="primary" [showIcon]="false" class="mb-2" appearance="outline">
                {{'private.common.certificationFolder.import.information2' | translate}}
            </treo-message>
            <div>
                <button type="button" mat-flat-button
                        [disabled]="loadingIngestXml"
                        class="bg-gray-200"
                        (click)="upload.click()">
                    {{ 'auth.cdc.dialogAccrochage.ingestXmlSend' | translate }}
                </button>
            </div>
            <input #upload type="file" id="ingestXmlFile" accept="text/xml" style="display:none"
                   (change)="onIngestXmlFileChange($event)"/>
        </div>
        <p *ngIf="ingestXmlDone">{{ 'auth.cdc.dialogAccrochage.ingestXmlDone' | translate }}</p>
        <mat-progress-spinner class="mr-4" *ngIf="loadingIngestXml" [diameter]="24"
                              mode="indeterminate"></mat-progress-spinner>
        <div *ngIf="errorMessagesIngestXml?.length" class="mt-1 flex items-center">
            <treo-message class="flex-auto" appearance="outline" [showIcon]="false" type="error">
                <ul>
                    <li *ngFor="let errorMessage of errorMessagesIngestXml">
                        {{ errorMessage }}
                    </li>
                </ul>
            </treo-message>
        </div>
    </div>
    <div
        class="flex items-center justify-end border-t -mx-8 mt-5 px-8 py-5 light:bg-cool-gray-50 dark:bg-cool-gray-700">
        <button (click)="submit()" type="submit" class="px-6 ml-3" mat-flat-button color="primary"
                [disabled]="loading || !formGroup.dirty || formGroup.invalid">
            <mat-progress-spinner class="mr-4" *ngIf="loading" [diameter]="24"
                                  mode="indeterminate"></mat-progress-spinner>
            <ng-container *ngIf="!loading"> {{ 'common.actions.update' | translate }}</ng-container>
        </button>
    </div>
</treo-card>
