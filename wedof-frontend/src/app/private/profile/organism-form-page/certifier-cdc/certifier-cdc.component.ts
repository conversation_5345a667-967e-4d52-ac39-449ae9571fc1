import {Component, Input, OnDestroy, OnInit, ViewChild} from '@angular/core';
import {MatDialog} from '@angular/material/dialog';
import {Organism} from '../../../../shared/api/models/organism';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {HttpErrorResponse, HttpResponse} from '@angular/common/http';
import {ApiError} from '../../../../shared/errors/errors.types';
import {FileService} from '../../../../shared/api/services/file.service';
import {AccrochageDialogComponent} from '../../../../shared/cdc-files/accrochage-dialog/accrochage-dialog.component';
import {CdcErrorStats, CdcFileService} from '../../../../shared/api/services/cdc-file.service';
import {CdcFile, CdcFileStates} from '../../../../shared/api/models/certification-folder';
import {
    TableFilter,
    TableMultipleFilterComponent
} from '../../../../shared/material/table/table-multiple-filter/table-multiple-filter.component';
import {OrganismState, UpdateOrganism} from '../../../../shared/api/state/organism.state';
import {mergeMap, takeUntil, tap} from 'rxjs/operators';
import {Select, Store} from '@ngxs/store';
import {UserState} from '../../../../shared/api/state/user.state';
import {forkJoin, Observable, Subject} from 'rxjs';
import {User} from '../../../../shared/api/models/user';
import {downloadJSONData} from '../../../../shared/utils/object-utils';
import {Subscription} from '../../../../shared/api/models/subscription';
import {AppFormFieldData} from '../../../../shared/material/app-form-field/app-form-field.component';
import {CertificationService} from '../../../../shared/api/services/certification.service';
import {TranslateService} from '@ngx-translate/core';
import {Certification, CertificationTypes} from '../../../../shared/api/models/certification';

@Component({
    selector: 'app-certifier-cdc',
    templateUrl: './certifier-cdc.component.html',
    styleUrls: ['./certifier-cdc.component.scss']
})
export class CertifierCdcComponent implements OnInit, OnDestroy {

    formGroup: FormGroup;
    loading = false;
    loadingIngestXml = false;
    ingestXmlDone = false;
    loadingCertifications = false;
    loadingCdcFiles: boolean;
    errorMessages: string[] = [];
    errorMessagesIngestXml: string[] = [];
    errorMessagesCertifications: string[] = [];
    user: User;
    cdcFiles: CdcFile[];
    cdcFileStates = CdcFileStates;
    stateFilters: TableFilter[];
    filters: any;
    appFormFieldsData: AppFormFieldData[];
    certifications: Certification[];
    errorStats: CdcErrorStats;

    @Input() organism: Organism;
    @Input() subscription: Subscription;

    @Select(UserState.user) user$: Observable<User>;
    @ViewChild('stateFilterSelector') private typeTableMultipleFilterComponentState: TableMultipleFilterComponent;

    private _unsubscribeAll = new Subject<void>();

    constructor(
        private _formBuilder: FormBuilder,
        private _fileService: FileService,
        private _dialog: MatDialog,
        private _certificationService: CertificationService,
        private _cdcFileService: CdcFileService,
        private _translateService: TranslateService,
        private _store: Store,
    ) {
        this.stateFilters = Object.values(CdcFileStates).map(value => ({
            label: `auth.cdc.cdcFileStates.${value}`,
            value: value
        }));
    }

    ngOnInit(): void {
        this.user$.pipe(takeUntil(this._unsubscribeAll)).subscribe(user => this.user = user);
        this.initForm(this.organism);
        this.initErrorStats();
        this.initCdcFiles({state: CdcFileStates.EXPORTED});
        this.initCertifications();
    }

    initErrorStats(): void {
        this._cdcFileService.errorStats().subscribe(errorStats => {
            this.errorStats = errorStats;
        });
    }

    initCdcFiles(params: any = {}): void {
        this.loadingCdcFiles = true;
        this._cdcFileService.list(params).subscribe((cdcFile) => {
            this.cdcFiles = cdcFile.payload;
            this.loadingCdcFiles = false;
            const valueState = params['state'] ? params['state'].split(',') : [];
            this.typeTableMultipleFilterComponentState.updateCurrentFiltersData(valueState);
        });
    }

    initForm(organism: Organism): void {
        this.formGroup = this._formBuilder.group({
            cdcContractId: [organism.cdcContractId, [Validators.pattern('MCFCER\\d{6}')]],
            cdcClientId: [organism.cdcClientId, [Validators.pattern('\\d{2}[A-Z]{3}\\d{3}')]],
            settings: this._formBuilder.group({})
        });
    }

    initCertifications(): void {
        this.loadingCertifications = true;
        const appFormFieldsData: AppFormFieldData[] = [];
        this._certificationService.list({
            organismType: 'certifier',
            siret: this.organism.siret
        }).subscribe((certificationsResponse) => {
            this.certifications = certificationsResponse.payload?.filter((certification) => certification.type !== CertificationTypes.INTERNAL) ;
            this.certifications?.forEach((certification) => {
                appFormFieldsData.push({
                    controlName: certification.certifInfo,
                    label: certification.externalId + ' - ' + certification.name,
                    class: 'app-form-field-radio-inline',
                    inline: true,
                    href: '/certification/partenariats/' + certification.certifInfo + '/certification',
                    type: 'radio',
                    help: certification.obtentionSystem ? null : this._translateService.instant('auth.cdc.exportNotAvailableObtentionSystem'),
                    helpIcon: certification.obtentionSystem ? null : 'warning',
                    helpIconClass: 'order-first mr-2',
                    disabled: certification.obtentionSystem == null,
                    value: certification.allowGenerateXmlAutomatically ?? false,
                    choices: [
                        {key: this._translateService.instant('common.actions.yes'), value: true},
                        {key: this._translateService.instant('common.actions.no'), value: false}
                    ],
                });
            });
            this.appFormFieldsData = appFormFieldsData;
            this.loadingCertifications = false;
        });
    }

    submit(): void {
        if (this.formGroup.valid &&
            (this.formGroup.get('cdcContractId').value !== this.organism.cdcContractId || this.formGroup.get('cdcClientId').value !== this.organism.cdcClientId)) {
            this.loading = true;
            const organism = this.organism;
            this._store.dispatch(new UpdateOrganism(organism.siret, {
                name: organism.name,
                siret: organism.siret,
                cdcContractId: this.formGroup.get('cdcContractId').value,
                cdcClientId: this.formGroup.get('cdcClientId').value
            })).pipe(
                mergeMap(() => this._store.selectOnce(OrganismState.organism))
            ).subscribe({
                next: (organismUpdated) => {
                    this.loading = false;
                    this.organism = organismUpdated;
                },
                error: (httpErrorResponse: HttpErrorResponse) => {
                    this.loading = false;
                    this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
                }
            });
        }
        this.submitCertifications();
    }

    openAccrochageDialog(cdcFile: CdcFile): void {
        const dialogRef = this._dialog.open(AccrochageDialogComponent, {
            panelClass: 'full-page-scroll-40',
            height: 'auto',
            data: {
                cdcFile: cdcFile
            }
        });
        dialogRef.afterClosed().subscribe(res => {
            if (res?.data) {
                const cdcFileName = res.data?.name;
                this.initCdcFiles({name: cdcFileName});
            }
        });
    }

    onIngestXmlFileChange(event): void {
        if (event.target.files.length > 0) {
            this.loadingIngestXml = true;
            const file = event.target.files[0];
            const formData: FormData = new FormData();
            formData.append('fileKey', file, file.name);
            this._fileService.upload('cdcFile', 'ingestXml', file).subscribe(
                (e) => {
                    if (e instanceof HttpResponse && e?.status === 200) {
                        this._cdcFileService.list({}).subscribe((cdcFile) => {
                            this.cdcFiles = cdcFile.payload;
                            this.loadingIngestXml = false;
                            this.ingestXmlDone = true;
                            downloadJSONData('wedof-report-' + file.name + '.json', e.body);
                        });
                    }
                },
                (httpErrorResponse: HttpErrorResponse) => {
                    this.loadingIngestXml = false;
                    this.errorMessagesIngestXml = (httpErrorResponse.error as ApiError).errorMessages;
                }
            );
        }
    }

    onFilterChange(filters: string[]): void {
        const filtersType = [];
        this.filters = {};
        filters.forEach((filter) => {
            if (filter) {
                filtersType.push(filter);
            }
        });
        if (filtersType.length) {
            this.filters.state = filtersType.join(',');
        }
        this.initCdcFiles(this.filters);
    }

    onProcessedCdcFile(cdcFileUpdated: CdcFile): void {
        const cdcFile = this.cdcFiles.find(currentCdcFile => currentCdcFile.id === cdcFileUpdated.id);
        if (cdcFile) {
            cdcFile.state = cdcFileUpdated.state;
        }
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    canUpdateCdcFile(cdcFile: CdcFile): boolean {
        return this.user.is_impersonator || !cdcFile.generatedAutomatically && !this.organism.accrochageDelegationDate;
    }


    submitCertifications(): void {
        this.errorMessagesCertifications = [];
        const certificationsToUpdate: {certification: Certification, allowGenerateXmlAutomatically: boolean}[] = [];
        Object.entries(this.formGroup.getRawValue().settings).forEach(([certifInfo, allowGenerateXmlAutomatically]: [string, boolean]) => {
            const certification = this.certifications.find(currentCertification => currentCertification.certifInfo === certifInfo);
            if (certification && certification.allowGenerateXmlAutomatically !== allowGenerateXmlAutomatically) {
                certificationsToUpdate.push({certification, allowGenerateXmlAutomatically});
            }
        });
        if (certificationsToUpdate.length) {
            const certificationUpdates$ = certificationsToUpdate.map(({certification, allowGenerateXmlAutomatically}) => {
                return this._certificationService.update({
                    ...certification,
                    allowGenerateXmlAutomatically: allowGenerateXmlAutomatically
                }).pipe(
                    tap((updatedCertification) => {
                        this.certifications.splice(this.certifications.indexOf(certification), 1, updatedCertification);
                    })
                );
            });
            this.loading = true;
            forkJoin(certificationUpdates$).subscribe(
                () => {
                    this.formGroup.get('settings').markAsPristine();
                },
                (httpErrorResponse: HttpErrorResponse) => {
                    this.errorMessagesCertifications = (httpErrorResponse.error as ApiError).errorMessages;
                }
            ).add(() => {
                this.loading = false;
            });
        }
    }
}
