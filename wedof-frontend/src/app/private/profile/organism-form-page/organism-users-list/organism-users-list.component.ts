import {AfterViewInit, Component, Input, OnDestroy, OnInit} from '@angular/core';
import {User} from '../../../../shared/api/models/user';
import {Subscription, SubscriptionTypes} from '../../../../shared/api/models/subscription';
import {UserService} from '../../../../shared/api/services/user.service';
import {Organism} from '../../../../shared/api/models/organism';
import {FormBuilder, FormControl, FormGroup, Validators} from '@angular/forms';
import {debounceTime, filter, finalize, switchMap, takeUntil, tap} from 'rxjs/operators';
import {SnackBarComponent} from '../../../../shared/material/snack-bar/snack-bar.component';
import {displayTextSnackBar} from '../../../../shared/utils/displayTextSnackBar';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../../../shared/errors/errors.types';
import {OrganismUserDialogComponent} from '../organism-user-dialog/organism-user-dialog.component';
import {DeletionConfirmationComponent} from '../../../../shared/material/action-confirmation/deletion-confirmation.component';
import {DialogUpgradeSubscriptionComponent} from '../../../../shared/subscription/dialog-upgrade-subscription/dialog-upgrade-subscription.component';
import {BehaviorSubject, combineLatest, Observable, Subject} from 'rxjs';
import {TranslateService} from '@ngx-translate/core';
import {MatSnackBar} from '@angular/material/snack-bar';
import {MatDialog} from '@angular/material/dialog';
import {Select} from '@ngxs/store';
import {SubscriptionState} from '../../../../shared/api/state/subscription.state';
import {UserState} from '../../../../shared/api/state/user.state';

@Component({
    selector: 'app-organism-users-list',
    templateUrl: './organism-users-list.component.html',
    styleUrls: ['./organism-users-list.component.scss']
})
export class OrganismUsersListComponent implements OnInit, OnDestroy, AfterViewInit {

    user: User;
    loading = false;
    usersCount: number;
    usersLimit: number;
    addUsers: FormGroup;
    usersOrganism: User[];
    searchingUser = false;
    showCreateBtn = false;
    subscription: Subscription;
    loadingDeleteUser: boolean;
    displayedColumns: string[];
    errorMessages: string[] = [];
    userFilteringCtrl: FormControl;
    showErrorAssociateUser: string = null;
    filteredUsers: BehaviorSubject<User[]> = new BehaviorSubject<User[]>([]);

    @Input() organism: Organism;

    @Select(UserState.user) user$: Observable<User>;
    @Select(SubscriptionState.subscription) subscription$: Observable<Subscription>;

    private _unsubscribeAll = new Subject<void>();

    constructor(private _fb: FormBuilder,
                private _dialog: MatDialog,
                private _snackBar: MatSnackBar,
                private _userService: UserService,
                private _translateService: TranslateService,
    ) {
        this.userFilteringCtrl = new FormControl('', Validators.required);
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    ngOnInit(): void {
        combineLatest([
            this.subscription$,
            this.user$
        ]).pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe(([subscription, user]) => {
            this.subscription = subscription;
            this.user = user;
            const trainingLimit = this.subscription.trainingUsersLimit;
            const certifierLimit = this.subscription.certifierUsersLimit;
            // si un des 2 est null alors illimité sinon on prend la limite la plus haute
            this.usersLimit = !trainingLimit || !certifierLimit ? null : trainingLimit > certifierLimit ? trainingLimit : certifierLimit;
            this._userService.list({siret: this.organism.siret}).subscribe((users) => {
                this.usersOrganism = users.payload;
                this.usersCount = users.payload.length;
            });
            this.addUsers = this._fb.group({
                email: ['', Validators.email]
            });
            this.displayedColumns = ['user', 'accountType'];
            if (this.user.isOwner) {
                this.displayedColumns.push('actions');
            }
        });
    }

    ngAfterViewInit(): void {
        this.userFilteringCtrl.valueChanges.pipe(
            debounceTime(500),
            filter(email => !email || email.length >= 2),
            takeUntil(this._unsubscribeAll)
        ).subscribe((email) => {
            this.addUsers.get('email').setValue(email);
            if (this.userFilteringCtrl.valid && email.indexOf('@') > 0) {
                this.searchUser();
            }
        });
        setTimeout(() => {
            this.addUsers.updateValueAndValidity({onlySelf: false, emitEvent: true});
        });
    }

    searchUser(): void {
        this.searchingUser = true;
        this._userService.findUser(this.addUsers.get('email').value).subscribe((userFound) => {
            const hasUser = this.usersOrganism.filter((user) => {
                return user.email === this.addUsers.get('email').value;
            }).length > 0;
            this.searchingUser = false;
            this.showErrorAssociateUser = null;
            if (hasUser) {
                this.showErrorAssociateUser = this._translateService.instant('private.common.organism.userOrganism.userAlreadyInvited');
            } else {
                this.filteredUsers.next(userFound);
                this.showCreateBtn = userFound.length <= 0;
            }
            this.searchingUser = false;
        });
    }

    associateAnotherUser(newUser: User): void {
        this.errorMessages = [];
        this._userService.associateUser(newUser.email).subscribe({
                next: () => {
                    this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(
                        this._translateService.instant('private.common.organism.userOrganism.userSuccessfullyInvited', {
                                email: newUser.email, organism: this.organism.name
                            }
                        ), 5000));
                    this.usersOrganism = [...this.usersOrganism, newUser]; // did this way because push(newUser) doesn't update the dom
                },
                error: (httpErrorResponse: HttpErrorResponse) => {
                    this.loading = false;
                    this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
                }
            }
        );
    }

    createUser(email: string): void {
        const user: User = {
            email: email
        };
        const dialog = this._dialog.open(OrganismUserDialogComponent, {
            disableClose: true,
            panelClass: ['full-page-scroll-40'],
            data: {
                user: user,
                subscription: this.subscription
            }
        });
        dialog.afterClosed().subscribe(res => {
            if (res?.data) {
                this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(
                    this._translateService.instant('private.common.organism.userOrganism.userSuccessfullyInvited', {
                            email: res.data.email, organism: this.organism.name
                        }
                    ), 5000));
                this.usersOrganism = [...this.usersOrganism, res.data]; // did this way because push(newUser) doesn't update the dom
            }
        });
    }

    revokedUser(userToRevoke: User): void {
        const dialogRef = this._dialog.open(DeletionConfirmationComponent, {
            panelClass: 'full-page-scroll-40',
            height: 'auto',
            data: {
                messageKey: 'private.common.organism.userOrganism.confirmDeletion',
                data: userToRevoke
            }
        });

        dialogRef.componentInstance.actionValue$.pipe(
            filter(confirmation => confirmation),
            tap(() => this.loadingDeleteUser = true),
            switchMap(() => this._userService.revokedUser(userToRevoke.email)),
            finalize(() => {
                this.loadingDeleteUser = false;
            })
        ).subscribe(
            () => {
                dialogRef.componentInstance.close();
                this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(
                    this._translateService.instant('private.common.organism.userOrganism.userSuccessfullyRevoked'), 5000));
                this.usersOrganism = this.usersOrganism.filter((user) => {
                    return user !== userToRevoke;
                });
            }, (httpErrorResponse: HttpErrorResponse) => {
                dialogRef.componentInstance.close();
                this.loading = false;
                this.loadingDeleteUser = false;
                this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
            });
    }

    resetPassword(emailUser: string): void {
        this._userService.forgotPassword(emailUser).subscribe({
            next: () => {
                this.loading = false;
                this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(
                    this._translateService.instant('private.common.organism.userOrganism.confirmNewPassword', {email: emailUser}), 5000));
            }, error: (httpErrorResponse: HttpErrorResponse) => {
                this.loading = false;
                this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
            }
        });
    }

    updateUser(user: User): void {
        const dialog = this._dialog.open(OrganismUserDialogComponent, {
            disableClose: true,
            panelClass: ['full-page-scroll-40'],
            data: {
                user: user,
                subscription: this.subscription
            }
        });
        dialog.afterClosed().subscribe(res => {
            if (res?.data) {
                this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(
                    this._translateService.instant('private.common.organism.userOrganism.userSuccessfullyUpdated', {
                            email: res.data.email, organism: this.organism.name
                        }
                    ), 5000));
                // use user.email to get the old user (and eventually old email) then update with new data
                const userToUpdate = this.usersOrganism.find(arrayItem => arrayItem.email === user.email);
                Object.assign(userToUpdate, res.data);
            }
        });
    }

    openDialogSubscription(): void {
        this._dialog.open(DialogUpgradeSubscriptionComponent, {
            panelClass: ['full-page-scroll-50'],
            data: {
                organism: this.organism,
                subscription: this.subscription,
                subscriptionTypeToShow: this.organism.isCertifierOrganism ? SubscriptionTypes.CERTIFIER : SubscriptionTypes.TRAINING
            }
        });
    }

    getUserOrganismRole(roles: Array<string>): string {
        if (roles.includes('ROLE_USER')) {
            return 'ROLE_USER';
        } else if (roles.includes('ROLE_SALES')) {
            return 'ROLE_SALES';
        } else {
            return 'ROLE_MISSING';
        }
    }

    selectUserOrganism(): void {
        if (this.showCreateBtn && this.addUsers.get('email').value && !this.showErrorAssociateUser) {
            this.createUser(this.addUsers.get('email').value);
        } else if (this.filteredUsers.value) {
            this.associateAnotherUser(this.filteredUsers.value[0]);
        }
        return null;
    }
}
