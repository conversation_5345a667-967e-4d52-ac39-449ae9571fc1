<treo-card class="p-8 w-full flex flex-col">
    <div class="flex items-center mb-2">
        <mat-icon class="mr-3 card-loading-show icon-size-32"
                  svgIcon="group"
                  color="primary">
        </mat-icon>
        <div class="text-xl font-semibold card-loading-show">
            {{ 'private.common.organism.userOrganism.listUserInvited' | translate }}
        </div>
    </div>
    <form *ngIf="user.isOwner && (usersLimit === null || (usersLimit && usersLimit > usersCount))"
          class="mt-2 flex flex-col w-full align-middle"
          [formGroup]="addUsers">
        <mat-form-field class="flex-auto">
            <mat-label>
                {{'private.common.organism.userOrganism.label ' | translate}}
            </mat-label>
            <mat-select formControlName="email"
                        [placeholder]="'private.common.organism.userOrganism.placeholder' | translate">
                <mat-option (keydown.enter)="selectUserOrganism()">
                    <ngx-mat-select-search [formControl]="userFilteringCtrl"
                                           [noEntriesFoundLabel]="searchingUser ? null : 'private.common.organism.userOrganism.notFound' | translate"
                                           [placeholderLabel]="'<EMAIL>' | translate"
                                           [searching]="searchingUser"></ngx-mat-select-search>
                </mat-option>
                <mat-option *ngFor="let user of filteredUsers | async">
                    <button (click)="associateAnotherUser(user)"
                            *ngIf="!searchingUser"
                            class="min-w-full">
                        {{'private.common.organism.userOrganism.associateUser' | translate : {
                        firstName: user.firstName,
                        lastName: user.lastName,
                        organism: organism.name
                    }
                        }}
                    </button>
                </mat-option>
                <mat-option *ngIf="!searchingUser && showCreateBtn && addUsers.get('email').value && !showErrorAssociateUser">
                    <button (click)="createUser(addUsers.get('email').value)"
                            class="min-w-full">
                        {{'private.common.organism.userOrganism.createUser' | translate : {
                        email: addUsers.get('email').value
                    }
                        }}
                    </button>
                </mat-option>
                <mat-option disabled *ngIf="showErrorAssociateUser">
                    {{showErrorAssociateUser}}
                </mat-option>
            </mat-select>
            <mat-icon [matTooltipPosition]="'above'"
                      [matTooltip]="'private.common.organism.userOrganism.help' | translate"
                      matSuffix
                      svgIcon="help_outline"></mat-icon>
        </mat-form-field>
    </form>
    <table [dataSource]="usersOrganism"
           class="table"
           mat-table>
        <ng-container matColumnDef="user">
            <th mat-header-cell
                *matHeaderCellDef>{{'private.common.organism.userOrganism.table.user' | translate}}</th>
            <td *matCellDef="let userOrganism" class="cursor-default" mat-cell>
                {{userOrganism.name}}
                <p class="text-secondary">{{userOrganism.email}}</p>
            </td>
        </ng-container>
        <ng-container matColumnDef="accountType">
            <th mat-header-cell
                *matHeaderCellDef>{{'private.common.organism.userOrganism.table.accountType' | translate}}</th>
            <td *matCellDef="let userOrganism" class="cursor-default" mat-cell>
                                <span
                                        *ngIf="userOrganism.isOwner; else displayRole">{{ 'private.layout.user.roles.ROLE_OWNER' | translate }}</span>
                <ng-template #displayRole>
                    <span>{{ 'private.layout.user.roles.' + getUserOrganismRole(userOrganism.roles) | translate }}</span>
                </ng-template>
            </td>
        </ng-container>
        <ng-container matColumnDef="actions">
            <th mat-header-cell
                *matHeaderCellDef
                class="text-right">{{'private.common.organism.userOrganism.table.actions' | translate}}</th>
            <td *matCellDef="let userOrganism" class="cursor-default pr-2" mat-cell>
                <mat-progress-spinner *ngIf="loadingDeleteUser; else actionsAvailable" [diameter]="20"
                                      mode="indeterminate">
                </mat-progress-spinner>
                <ng-template #actionsAvailable>
                    <div class="flex justify-end">
                        <button [matTooltipPosition]="'above'"
                                [matTooltip]="'private.common.organism.userOrganism.helpUpdate' | translate"
                                *ngIf="!userOrganism.isOwner" mat-icon-button
                                (click)="updateUser(userOrganism)"
                                color="primary">
                            <mat-icon>edit</mat-icon>
                        </button>
                        <button [matTooltipPosition]="'above'"
                                [matTooltip]="'private.common.organism.userOrganism.helpPassword' | translate : {email: userOrganism.email }"
                                *ngIf="!userOrganism.isOwner" mat-icon-button
                                (click)="resetPassword(userOrganism.email)"
                                color="accent">
                            <mat-icon>lock_reset</mat-icon>
                        </button>
                        <button [matTooltipPosition]="'above'"
                                [matTooltip]="'private.common.organism.userOrganism.helpRemove' | translate : {email: userOrganism.email }"
                                *ngIf="!userOrganism.isOwner" mat-icon-button
                                (click)="revokedUser(userOrganism)"
                                color="warn">
                            <mat-icon>close</mat-icon>
                        </button>
                    </div>
                </ng-template>

            </td>
        </ng-container>
        <tr *matRowDef="let row; columns:displayedColumns"
            mat-row></tr>
        <tr mat-header-row
            *matHeaderRowDef="displayedColumns"></tr>
    </table>
    <treo-message *ngIf="user.isOwner && usersLimit && (usersLimit <= usersCount)"
                  class="mt-8"
                  appearance="outline" [showIcon]="false" type="error">
        <button (click)="openDialogSubscription()"
                class="flex m-2">
            <a>{{'private.common.organism.userOrganism.limitReached' | translate}}</a>
        </button>
    </treo-message>
    <treo-message *ngIf="errorMessages.length"
                  class="mt-2"
                  appearance="outline" [showIcon]="false" type="error">
        <ul>
            <li *ngFor="let errorMessage of errorMessages">
                {{ errorMessage }}
            </li>
        </ul>
    </treo-message>
</treo-card>
