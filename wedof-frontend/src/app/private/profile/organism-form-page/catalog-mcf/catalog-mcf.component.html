<treo-card class="mt-3 p-8 pb-0 w-full flex flex-col">
    <div class="flex items-center mb-2">
        <mat-icon class="mr-3 card-loading-show icon-size-32"
                  svgIcon="library_books"
                  color="primary">
        </mat-icon>
        <div class="text-xl font-semibold card-loading-show">
            {{ 'private.common.organism.catalogMcf' | translate }}
        </div>
    </div>
    <a target='_blank' href='/assistance/guides/organismes-formation/gerer-catalogue-edof'>Consulter la documentation</a>
    <div class="mt-4 mb-2" *ngIf="organism.isTrainingOrganism">
        <div>
            <div class="mb-1">
                <label class="text-base font-medium">{{ 'private.common.organism.catalog.export.title' | translate }}</label>
            </div>
            <div class="flex items-center">
                <button class="bg-gray-200 mr-2"
                        mat-flat-button
                        type="button"
                        (click)="openDialogExportCatalog()">
                    {{ 'private.common.organism.catalog.export.button.export' | translate }}
                </button>
            </div>
        </div>
    </div>
    <div class="mt-4 mb-6" *ngIf="organism.isTrainingOrganism">
        <div>
            <div class="mb-1">
                <label class="text-base font-medium">{{ 'private.common.organism.catalog.import.title' | translate }}</label>
            </div>
            <div class="flex items-center">
                <button class="bg-gray-200 mr-2"
                        mat-flat-button
                        type="button"
                        (click)="openDialogImportCatalog()">
                    {{ 'private.common.organism.catalog.import.button.import' | translate }}
                </button>
                <span *ngIf="organism.cpfCatalogMetadata?.upload?.state === 'done'" class="underline cursor-pointer"
                      (click)="downloadLastImportReport()">
                    {{ 'private.common.organism.catalog.import.button.downloadReport' | translate }} ({{ organism.cpfCatalogMetadata.upload.endDate | dateZToDayString }})
                </span>
            </div>
        </div>
    </div>
    <div *ngIf="errorMessages.length" class="flex items-center mt-3 mb-6">
        <treo-message class="flex-auto" appearance="outline" [showIcon]="false" type="error">
            <ul>
                <li *ngFor="let errorMessage of errorMessages">
                    {{ errorMessage }}
                </li>
            </ul>
        </treo-message>
    </div>
</treo-card>
