import {Component, Input, OnInit} from '@angular/core';
import {Organism} from '../../../../shared/api/models/organism';
import {Select, Store} from '@ngxs/store';
import {SubscriptionState} from '../../../../shared/api/state/subscription.state';
import {Observable, Subject} from 'rxjs';
import {Subscription, SubscriptionTrainingTypes} from '../../../../shared/api/models/subscription';
import {takeUntil} from 'rxjs/operators';
import {TranslateService} from '@ngx-translate/core';
import {ImportCatalogDialogComponent} from '../import-catalog-dialog/import-catalog-dialog.component';
import {DisclaimerComponent} from '../../../../shared/material/disclaimer/disclaimer.component';
import {UpdatedOrganism} from '../../../../shared/api/state/organism.state';
import {SnackBarComponent} from '../../../../shared/material/snack-bar/snack-bar.component';
import {displayTextSnackBar} from '../../../../shared/utils/displayTextSnackBar';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../../../shared/errors/errors.types';
import {MatDialog} from '@angular/material/dialog';
import {MatSnackBar} from '@angular/material/snack-bar';
import {OrganismService} from '../../../../shared/api/services/organism.service';
import {FileService} from '../../../../shared/api/services/file.service';

@Component({
    selector: 'app-catalog-mcf',
    templateUrl: './catalog-mcf.component.html',
    styleUrls: ['./catalog-mcf.component.scss']
})
export class CatalogMcfComponent implements OnInit {

    subscription: Subscription;
    errorMessages: string[] = [];

    @Input() organism: Organism;
    @Select(SubscriptionState.subscription) subscription$: Observable<Subscription>;

    private _unsubscribeAll = new Subject<void>();

    constructor(
        private _store: Store,
        private _dialog: MatDialog,
        private _snackBar: MatSnackBar,
        private _fileService: FileService,
        private _organismService: OrganismService,
        private _translateService: TranslateService
    ) {
    }

    ngOnInit(): void {
        this.subscription$.pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe((subscription) => {
            this.subscription = subscription;
        });
    }

    openDialogImportCatalog(): void {
        if (this.isPayingOf()) {
            this._dialog.open(ImportCatalogDialogComponent, {
                panelClass: 'full-page-scroll-40',
                height: 'auto',
                data: {
                    organism: this.organism
                }
            });
        } else { // todo replace by stripe payment one shot
            this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(this._translateService.instant('common.actions.comingSoon'), 10000));
        }
    }

    downloadLastImportReport(): void {
        this._fileService.download('/api/organisms/' + this.organism.siret + '/cpfCatalogUploadReport').subscribe();
    }

    openDialogExportCatalog(): void {
        const hasExportCatalogAllow = this.isPayingOf();
        const mainButtonTitle = hasExportCatalogAllow ? 'private.common.organism.catalog.export.button.export' : 'private.common.organism.catalog.export.button.subscribeOnce';
        this._dialog.open(DisclaimerComponent, {
            panelClass: ['full-page-scroll-30'],
            data: {
                title: 'private.common.organism.catalog.export.title',
                subtitle: 'private.common.organism.catalog.export.subtitle',
                mainButton: {
                    title: mainButtonTitle,
                    method: () => {
                        if (hasExportCatalogAllow) {
                            this._organismService.exportCpfCatalogXml(this.organism).subscribe(
                                (organism: Organism) => {
                                    this._store.dispatch(new UpdatedOrganism({
                                        ...organism // To get new metadata
                                    }));
                                    this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(
                                        this._translateService.instant('private.common.organism.catalog.export.success'), 10000));
                                },
                                (httpErrorResponse: HttpErrorResponse) => {
                                    this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
                                }
                            );
                        } else { // todo replace by stripe payment one shot
                            this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(this._translateService.instant('common.actions.comingSoon'), 10000));
                        }
                    }
                },
                secondaryButton: null
            }
        });
    }

    private isPayingOf(): boolean {
        return [
            SubscriptionTrainingTypes.STANDARD, SubscriptionTrainingTypes.ESSENTIAL,
            SubscriptionTrainingTypes.PREMIUM, SubscriptionTrainingTypes.API,
            SubscriptionTrainingTypes.ACCESS, SubscriptionTrainingTypes.ACCESS_PLUS
        ].includes(this.subscription.trainingType);
    }
}
