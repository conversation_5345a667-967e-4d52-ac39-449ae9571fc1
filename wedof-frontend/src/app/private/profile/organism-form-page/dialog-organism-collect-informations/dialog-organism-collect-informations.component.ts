import {Component, Inject, OnInit} from '@angular/core';
import {Form<PERSON>uilder, FormGroup} from '@angular/forms';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material/dialog';
import {Organism} from '../../../../shared/api/models/organism';
import {AppFormFieldData} from '../../../../shared/material/app-form-field/app-form-field.component';
import {OrganismState, UpdateOrganism} from '../../../../shared/api/state/organism.state';
import {mergeMap} from 'rxjs/operators';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../../../shared/errors/errors.types';
import {Store} from '@ngxs/store';
import {TranslateService} from '@ngx-translate/core';

export const choicesFacturation = [
    {key: 'Outils interne avec API', value: 'internalWithApi'},
    {key: 'Outils interne sans API', value: 'internalWithoutApi'},
    {key: 'Dendreo', value: 'dendreo'},
    {key: 'Digiforma', value: 'digiforma'},
    {key: 'Qonto', value: 'qonto'},
    {key: 'Henrri', value: 'henrri'},
    {key: 'MEG', value: 'meg'},
    {key: 'Pennylane', value: 'pennylane'},
    {key: 'Stripe', value: 'stripe'},
    {key: 'Tiime', value: 'tiime'},
    {key: 'YMAG', value: 'ymag'},
    {key: 'Axonaut', value: 'axonaut'},
    {key: 'Evoliz', value: 'evoliz'},
    {key: 'Indy', value: 'indy'},
    {key: 'Quickbooks', value: 'quickbooks'},
    {key: 'Sage', value: 'sage'},
    {key: 'Sellsy', value: 'sellsy'},
    {key: 'Zoho', value: 'zoho'},
    {key: 'Autre', value: 'other'}
];

export const choicesCrm = [
    {key: 'Outils interne avec API', value: 'internalWithApi'},
    {key: 'Outils interne sans API', value: 'internalWithoutApi'},
    {key: 'Airtable', value: 'airtable'},
    {key: 'Dendreo', value: 'dendreo'},
    {key: 'Digiforma', value: 'digiforma'},
    {key: 'Eforma', value: 'eforma'},
    {key: 'Hubspot', value: 'hubspot'},
    {key: 'Salesforce', value: 'salesforce'},
    {key: 'YPAREO', value: 'ypareo'},
    {key: 'Je n\'utilise que Wedof', value: 'wedofOnly'},
    {key: 'Autre', value: 'other'}
];

@Component({
    selector: 'app-dialog-organism-collect-informations',
    templateUrl: './dialog-organism-collect-informations.component.html',
    styleUrls: ['./dialog-organism-collect-informations.component.scss']
})
export class DialogOrganismCollectInformationsComponent implements OnInit {

    loading = false;
    formGroup: FormGroup;
    errorMessages: string[] = [];
    appFormFieldsData: AppFormFieldData[];

    constructor(private _store: Store,
                private _formBuilder: FormBuilder,
                private _translateService: TranslateService,
                public dialogRef: MatDialogRef<DialogOrganismCollectInformationsComponent>,
                @Inject(MAT_DIALOG_DATA) public dialogData: {
                    organism: Organism;
                }) {
    }

    ngOnInit(): void {
        this.formGroup = this._formBuilder.group({
            organism: this._formBuilder.group({})
        });
        let billingSoftwareOption;
        let crmOption;
        if (this.dialogData.organism.billingSoftware) {
            const billingSoftwareChoice = choicesFacturation.find((choiceFacturation) => choiceFacturation.value === this.dialogData.organism.billingSoftware);
            billingSoftwareOption = billingSoftwareChoice ? billingSoftwareChoice.value : 'other';
        } else {
            billingSoftwareOption = null;
        }
        if (this.dialogData.organism.crm) {
            const crmChoice = choicesFacturation.find((choiceFacturation) => choiceFacturation.value === this.dialogData.organism.crm);
            crmOption = crmChoice ? crmChoice.value : 'other';
        } else {
            crmOption = null;
        }
        const appFormFieldsData: AppFormFieldData[] = [
            {
                controlName: 'billingSoftware',
                value: billingSoftwareOption,
                removable: true,
                label: 'private.profile.organism.form.fields.tools.billingSoftware',
                type: 'select',
                choices: choicesFacturation,
                change: (controlName, newValue, formData) => {
                    const appFormFieldNameBillingSoftwareOther = formData.find(field => field.controlName === 'billingSoftwareOther');
                    if (newValue === 'other') {
                        appFormFieldNameBillingSoftwareOther.removed = false;
                        appFormFieldNameBillingSoftwareOther.required = true;
                    } else {
                        appFormFieldNameBillingSoftwareOther.removed = true;
                        appFormFieldNameBillingSoftwareOther.required = false;
                    }
                    return [appFormFieldNameBillingSoftwareOther];
                },
            },
            {
                controlName: 'billingSoftwareOther',
                removed: billingSoftwareOption !== 'other',
                value: this.dialogData.organism.billingSoftware,
                label: this._translateService.instant('private.profile.organism.form.fields.tools.billingSoftware') +
                    ' - ' + this._translateService.instant('common.actions.other'),
                type: 'text',
                placeholder: 'Indiquez votre logiciel de facturation'
            },
            {
                controlName: 'crm',
                value: crmOption,
                removable: true,
                label: 'private.profile.organism.form.fields.tools.crm',
                type: 'select',
                choices: choicesCrm,
                change: (controlName, newValue, formData) => {
                    const appFormFieldNameCrmOther = formData.find(field => field.controlName === 'crmOther');
                    if (newValue === 'other') {
                        appFormFieldNameCrmOther.removed = false;
                        appFormFieldNameCrmOther.required = true;
                    } else {
                        appFormFieldNameCrmOther.removed = true;
                        appFormFieldNameCrmOther.required = false;
                    }
                    return [appFormFieldNameCrmOther];
                },
            },
            {
                controlName: 'crmOther',
                removed: crmOption !== 'other',
                value: this.dialogData.organism.crm,
                label: this._translateService.instant('private.profile.organism.form.fields.tools.crm') + ' - ' + this._translateService.instant('common.actions.other'),
                type: 'text',
                placeholder: 'Indiquez votre CRM'
            }
        ];
        this.appFormFieldsData = appFormFieldsData.filter(value => value != null);
    }

    submit(): void {
        this.loading = true;
        const formGroupValue = this.formGroup.getRawValue().organism;
        this._store.dispatch(new UpdateOrganism(this.dialogData.organism.siret, {
            siret: this.dialogData.organism.siret,
            crm: formGroupValue.crmOther ? formGroupValue.crmOther : formGroupValue.crm,
            billingSoftware: formGroupValue.billingSoftwareOther ? formGroupValue.billingSoftwareOther : formGroupValue.billingSoftware,
        })).pipe(
            mergeMap(() => this._store.selectOnce(OrganismState.organism))
        ).subscribe({
            next: () => {
                this.loading = false;
                this.closeModal();
            },
            error: (httpErrorResponse: HttpErrorResponse) => {
                this.loading = false;
                this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
            }
        });
    }

    closeModal(): void {
        this.dialogRef.close();
    }

}
