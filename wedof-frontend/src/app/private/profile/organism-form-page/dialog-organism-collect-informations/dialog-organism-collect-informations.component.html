<app-dialog-layout
    [title]="'private.profile.organism.form.fields.tools.title' | translate"
    [actions]="actions"
    [showCancel]="true"
    (dialogClose)="closeModal()">

    <p class="mt-2 mb-2">{{ 'private.profile.organism.form.fields.tools.subtitle' | translate }}</p>

    <form class="flex flex-col" [formGroup]="formGroup">
        <app-form-fields class="grid grid-cols-6 gap-4 card-loading-show"
                         formGroupName="organism"
                         [appFormFieldsData]="appFormFieldsData"
                         [formGroup]="formGroup">
        </app-form-fields>

        <div *ngIf="errorMessages?.length" class="flex items-center">
            <treo-message class="flex-auto" appearance="outline" [showIcon]="false" type="error">
                <ul>
                    <li *ngFor="let errorMessage of errorMessages">
                        {{ errorMessage }}
                    </li>
                </ul>
            </treo-message>
        </div>

        <ng-template #actions>
            <button type="submit" [disabled]="loading || !formGroup.dirty || formGroup.invalid" (click)="submit()" mat-flat-button
                    color="primary">
                <mat-progress-spinner *ngIf="loading; else submitLabel" [diameter]="24" mode="indeterminate">
                </mat-progress-spinner>
                <ng-template #submitLabel>
                    {{ 'common.actions.confirm' | translate }}
                </ng-template>
            </button>
        </ng-template>
    </form>
</app-dialog-layout>
