import {Component, Inject, OnInit} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material/dialog';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {AppFormFieldData} from '../../../../shared/material/app-form-field/app-form-field.component';
import {FormValidators} from '../../../../shared/api/shared/form-validators';
import {User, UserOrganism} from '../../../../shared/api/models/user';
import {UserService} from '../../../../shared/api/services/user.service';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../../../shared/errors/errors.types';
import {switchMap} from 'rxjs/operators';
import {Observable} from 'rxjs';
import {Subscription} from '../../../../shared/api/models/subscription';
import {TranslateService} from '@ngx-translate/core';
import {MustMatch} from '../../../../auth/sign-up/helper/must-match.validator';

@Component({
    selector: 'app-organism-user-dialog',
    templateUrl: './organism-user-dialog.component.html',
    styleUrls: ['./organism-user-dialog.component.scss']
})
export class OrganismUserDialogComponent implements OnInit {

    loading = false;
    errorMessages: string[] = [];
    formGroup: FormGroup;
    appFormFieldsData: AppFormFieldData[];
    userOrganism: UserOrganism;
    create: boolean;

    constructor(
        private _formBuilder: FormBuilder,
        public dialogRef: MatDialogRef<OrganismUserDialogComponent>,
        private _userService: UserService,
        private _translateService: TranslateService,
        @Inject(MAT_DIALOG_DATA) public dialogData: {
            user?: User,
            subscription?: Subscription
        }
    ) {
    }

    ngOnInit(): void {
        this.create = !this.dialogData.user._links;
        this.formGroup = this._formBuilder.group({
            userOrganism: this._formBuilder.group({}, {validator: MustMatch('password', 'passwordCheck')})
        });
        const appFormFieldsData: AppFormFieldData[] = [
            {
                controlName: 'lastName',
                required: true,
                value: this.dialogData.user?.lastName ?? null,
                label: 'private.common.organism.userOrganism.lastName',
                type: 'text',
                colSpan: 3
            },
            {
                controlName: 'firstName',
                required: true,
                value: this.dialogData.user?.firstName ?? null,
                label: 'private.common.organism.userOrganism.firstName',
                type: 'text',
                colSpan: 3
            },
            {
                controlName: 'email',
                required: true,
                value: this.dialogData.user?.email ?? null,
                label: 'private.common.organism.userOrganism.email',
                validators: [Validators.pattern(FormValidators.EMAIL_PATTERN)],
                type: 'text',
                maxLength: 100,
                colSpan: 3,
            },
            {
                controlName: 'phoneNumber',
                value: this.dialogData.user?.phoneNumber ?? null,
                label: 'private.common.organism.userOrganism.phoneNumber',
                type: 'tel',
                validators: [Validators.pattern(FormValidators.PHONE_PATTERN), Validators.minLength(10), Validators.maxLength(10)],
                validatorsMessages: {
                    pattern: 'public.funnel.errors.phoneNumber'
                },
                colSpan: 3
            },
            {
                controlName: 'roles',
                removed: !this.dialogData.subscription.allowProposals,
                label: 'Type de compte',
                help: 'Un compte commercial n\'aura pas accès à tout Wedof mais uniquement à la création de propositions individuelles',
                type: 'select',
                icon: 'folder',
                value: this.dialogData.user?.roles ? this.dialogData.user?.roles[0] : 'ROLE_USER',
                choices: [
                    {
                        key: this._translateService.instant('private.layout.user.roles.ROLE_SALES'),
                        value: 'ROLE_SALES'
                    },
                    {
                        key: this._translateService.instant('private.layout.user.roles.ROLE_USER'),
                        value: 'ROLE_USER'
                    }
                ]
            },
            {
                controlName: 'password',
                removed: !this.create,
                value: null,
                label: 'private.common.organism.userOrganism.create.password',
                type: 'password',
                validators: [FormValidators.PASSWORD],
                validatorsMessages: {
                    notSame: 'common.errors.password.verification'
                },
                colSpan: 3
            },
            {
                controlName: 'passwordCheck',
                removed: !this.create,
                value: null,
                label: 'private.common.organism.userOrganism.create.passwordCheck',
                type: 'password',
                validators: [FormValidators.PASSWORD],
                validatorsMessages: {
                    mustMatch: 'common.errors.password.verification'
                },
                colSpan: 3
            },
        ];
        this.appFormFieldsData = appFormFieldsData.filter(value => value != null);
    }

    closeModal(): void {
        this.dialogRef.close();
    }

    submit(): void {
        this.loading = true;
        this.errorMessages = [];
        const userFormValue = this.formGroup.getRawValue().userOrganism;
        const user: UserOrganism = {
            firstName: userFormValue.firstName,
            lastName: userFormValue.lastName,
            phoneNumber: userFormValue.phoneNumber,
            email: userFormValue.email,
            password: userFormValue.password ?? Math.random().toString(12),
            roles: [!this.dialogData.subscription.allowProposals ? 'ROLE_USER' : userFormValue.roles]
        };
        let method$;
        if (this.create) {
            method$ = this._userService.createOnly(user).pipe(
                switchMap((userCreated: UserOrganism): Observable<any> => {
                        return this._userService.associateUser(userCreated.email, !userFormValue.password);
                    }
                )
            );
        } else {
            // don't use user.email as we can change email and then it will change the wrong user!
            method$ = this._userService.privateUpdate(this.dialogData.user?.email, user);
        }
        method$.subscribe({
            next: (_user: User) => {
                this.loading = false;
                this.dialogRef.close({data: _user});
            },
            error: (httpErrorResponse: HttpErrorResponse) => {
                this.loading = false;
                this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
            }
        });
    }
}
