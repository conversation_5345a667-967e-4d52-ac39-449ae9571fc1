<app-dialog-layout
    [title]=" 'private.common.organism.userOrganism.'+(create ? 'create' :'update' )+'.title'| translate"
    [actions]="actions"
    (dialogClose)="closeModal()">
    <form [formGroup]="formGroup"
          class="flex flex-col"
          (ngSubmit)="submit()">
        <app-form-fields formGroupName="userOrganism"
                         class="gt-xs:grid gt-xs:grid-cols-6 gap-4"
                         [entity]="userOrganism"
                         [appFormFieldsData]="appFormFieldsData"
                         [formGroup]="formGroup"></app-form-fields>
        <div *ngIf="errorMessages?.length" class="flex items-center">
            <treo-message class="flex-auto" appearance="outline" [showIcon]="false" type="error">
                <ul>
                    <li *ngFor="let errorMessage of errorMessages">
                        {{ errorMessage }}
                    </li>
                </ul>
            </treo-message>
        </div>
        <ng-template #actions>
            <div class="flex flex-row py-2 mb-2">
                <button type="submit"
                        (click)="submit()"
                        color="primary" class="mt-2"
                        mat-flat-button
                        [disabled]="loading || !formGroup.dirty || formGroup.invalid">
                    <mat-progress-spinner *ngIf="loading"
                                          class="mr-4"
                                          [diameter]="24"
                                          mode="indeterminate"></mat-progress-spinner>
                    <ng-container *ngIf="!loading">
                        {{'private.common.organism.userOrganism.' + (create ? 'create' : 'update') + '.submit' | translate}}
                    </ng-container>
                </button>
            </div>
        </ng-template>
    </form>
</app-dialog-layout>
