<treo-card class="w-full flex flex-col">
    <div class="p-8 flex items-center">
        <mat-icon class="mr-3 card-loading-show icon-size-32"
                  svgIcon="account_circle"
                  color="primary">
        </mat-icon>
        <div class="text-xl font-semibold card-loading-show">
            {{ 'private.profile.user.form.title.info' | translate}}
        </div>
    </div>
    <form [formGroup]="formGroup" (submit)="submit()" class="p-8 py-0 flex flex-col w-full">
        <div class="flex flex-col gt-xs:flex-row">
            <mat-form-field class="flex-auto gt-xs:pr-3">
                <mat-label>
                    {{ 'private.profile.user.form.fields.first-name.label' | translate}}
                </mat-label>
                <input formControlName="firstName" type="text" matInput required>
                <mat-icon matPrefix svgIcon="account_circle"></mat-icon>
                <mat-error class="pb-1">
                    {{ 'private.profile.user.form.fields.first-name.error' | translate}}
                </mat-error>
            </mat-form-field>
            <mat-form-field class="flex-auto gt-xs:pl-3">
                <mat-label>
                    {{ 'private.profile.user.form.fields.last-name.label' | translate}}
                </mat-label>
                <input formControlName="lastName" type="text" matInput required>
                <mat-icon matPrefix svgIcon="account_circle"></mat-icon>
                <mat-error class="pb-1">
                    {{ 'private.profile.user.form.fields.last-name.error' | translate}}
                </mat-error>
            </mat-form-field>
        </div>
        <mat-form-field class="flex-auto">
            <mat-label>
                {{ 'private.profile.user.form.fields.email.label' | translate}}
            </mat-label>
            <input formControlName="email" type="email" matInput required>
            <mat-icon matPrefix svgIcon="email"></mat-icon>
            <mat-error class="pb-1">
                {{ 'private.profile.user.form.fields.email.error' | translate}}
            </mat-error>
        </mat-form-field>
        <mat-form-field class="flex-auto">
            <mat-label>
                {{ 'private.profile.user.form.fields.phone.label' | translate}}
            </mat-label>
            <input formControlName="phoneNumber" type="tel" matInput>
            <mat-icon matPrefix svgIcon="phone"></mat-icon>
            <mat-error class="pb-1">
                {{ 'private.profile.user.form.fields.phone.error' | translate}}
            </mat-error>
        </mat-form-field>
        <mat-form-field class="flex-auto">
            <mat-label>
                {{ 'private.profile.user.form.fields.address.label' | translate}}
            </mat-label>
            <input formControlName="address" type="text" matInput>
            <mat-icon matPrefix svgIcon="location_on"></mat-icon>
        </mat-form-field>
        <div
            class="flex items-center justify-end border-t -mx-8 mt-5 px-8 py-5 light:bg-cool-gray-50 dark:bg-cool-gray-700">
            <button (click)="cancel.emit()" mat-button type="button">
                {{ 'common.actions.cancel' | translate}}
            </button>
            <button type="submit" class="px-6 ml-3" mat-flat-button color="primary" [disabled]="loading || !formGroup.dirty || formGroup.invalid">
                <mat-progress-spinner class="mr-4" *ngIf="loading" [diameter]="24"
                                      mode="indeterminate"></mat-progress-spinner>
                <ng-container
                    *ngIf="!loading">{{ 'private.profile.user.form.actions.submit' | translate}}</ng-container>
            </button>
        </div>
    </form>
</treo-card>
