import {Component, EventEmitter, Injector, Input, OnChanges, Output} from '@angular/core';
import {Valida<PERSON>} from '@angular/forms';
import {MatDialog} from '@angular/material/dialog';
import {finalize, switchMap} from 'rxjs/operators';
import {User} from '../../../../shared/api/models/user';
import {FormValidators} from '../../../../shared/api/shared/form-validators';
import {AbstractFormComponent} from '../../../../shared/material/form/abstract-form.component';
import {LogoutConfirmationComponent} from '../logout-confirmation/logout-confirmation.component';
import {EMPTY} from 'rxjs';
import {FetchUser, UpdateUser} from '../../../../shared/api/state/user.state';
import {Store} from '@ngxs/store';

@Component({
    selector: 'app-user-form',
    templateUrl: './user-form.component.html',
    styleUrls: ['./user-form.component.scss']
})
export class UserFormComponent extends AbstractFormComponent<User> implements OnChanges {

    @Input() user: User;
    @Output() cancel = new EventEmitter<void>();

    constructor(
        injector: Injector,
        private _store: Store,
        private _dialog: MatDialog
    ) {
        super(injector);
    }

    ngOnChanges(): void {
        this.initForm(this.user);
    }

    submit(): void {
        if (this.formGroup.valid) {
            this.loading = true;
            const updatedUser: User = this.formGroup.value;
            const oldEmail = this.user.email;
            const hasEmailChanged = updatedUser.email !== oldEmail;
            this._store.dispatch(new UpdateUser(oldEmail, updatedUser)).pipe(
                switchMap(() => {
                    if (hasEmailChanged) {
                        return this._dialog.open(LogoutConfirmationComponent, {
                            disableClose: true
                        }).afterClosed().pipe(
                            switchMap(() => this._store.dispatch(new FetchUser()) // This triggers a 401 that triggers a logout
                        ));
                    } else {
                        return EMPTY;
                    }
                }),
                finalize(() => this.loading = false)
            ).subscribe(() => {
            });
        }
    }

    protected initForm(user: User): void {
        this.formGroup = this._fb.group({
            firstName: [user.firstName, [Validators.required]],
            lastName: [user.lastName, [Validators.required]],
            email: [user.email, [Validators.required, Validators.pattern(FormValidators.EMAIL_PATTERN)]],
            phoneNumber: [user.phoneNumber, [Validators.pattern(FormValidators.PHONE_PATTERN)]],
            address: [user.address],
        });
    }

}
