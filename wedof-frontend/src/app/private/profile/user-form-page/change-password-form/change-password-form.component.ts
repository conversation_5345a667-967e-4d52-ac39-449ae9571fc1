import {Component, EventEmitter, Injector, Input, OnChanges, Output} from '@angular/core';
import {Validators} from '@angular/forms';
import {Router} from '@angular/router';
import {MatDialog} from '@angular/material/dialog';
import {finalize, switchMap} from 'rxjs/operators';
import {AbstractFormComponent} from '../../../../shared/material/form/abstract-form.component';
import {User} from '../../../../shared/api/models/user';
import {LogoutConfirmationComponent} from '../logout-confirmation/logout-confirmation.component';
import {FormValidators} from '../../../../shared/api/shared/form-validators';
import {MustMatch} from '../../../../auth/sign-up/helper/must-match.validator';
import {Store} from '@ngxs/store';
import {UpdateUser} from '../../../../shared/api/state/user.state';

@Component({
    selector: 'app-change-password-form',
    templateUrl: './change-password-form.component.html',
    styleUrls: ['./change-password-form.component.scss']
})
export class ChangePasswordFormComponent extends AbstractFormComponent<User> implements OnChanges {

    @Input() user: User;
    @Output() cancel = new EventEmitter<void>();
    visible = false;

    constructor(
        injector: Injector,
        private _router: Router,
        private _dialog: MatDialog,
        private _store: Store,
    ) {
        super(injector);
    }

    ngOnChanges(): void {
        this.initForm(this.user);
    }

    submit(): void {
        if (this.formGroup.valid) {
            this.loading = true;
            this._store.dispatch(new UpdateUser(this.user.email, {
                email: this.user.email,
                password: this.formGroup.get('password').value
            })).pipe(
                switchMap(() => this._dialog.open(LogoutConfirmationComponent, {
                    disableClose: true
                }).afterClosed()),
                finalize(() => this.loading = false)
            ).subscribe(() =>
                this._router.navigate(['/', 'auth', 'deconnexion'])
            );
        }
    }

    protected initForm(user: User): void {
        this.formGroup = this._fb.group({
                email: [user.email, Validators.required],
                password: ['', [Validators.required, FormValidators.PASSWORD]],
                passwordCheck: ['', [Validators.required]],
            },
            MustMatch('password', 'passwordCheck'));
    }

}
