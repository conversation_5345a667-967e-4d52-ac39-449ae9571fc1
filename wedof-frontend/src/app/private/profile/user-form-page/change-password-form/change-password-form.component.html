<treo-card class="w-full flex flex-col">
    <div class=" p-8 flex items-center">
        <mat-icon class="mr-3 card-loading-show icon-size-32"
                  svgIcon="lock"
                  color="primary">
        </mat-icon>
        <div class="text-xl font-semibold card-loading-show">
            {{ 'private.profile.user.form.title.password' | translate}}
        </div>
    </div>
    <form [formGroup]="formGroup" (submit)="submit()" class="p-8 py-0 flex flex-col w-full ">

        <input [hidden]="true" formControlName="email" type="text" required name="username" autocomplete="username">

        <mat-form-field class="flex-auto">
            <mat-label>
                {{ 'private.profile.user.form.fields.password.label' | translate}}
            </mat-label>
            <input formControlName="password" [type]="visible ? 'text': 'password'" matInput required name="password"
                   autocomplete="new-password">
            <mat-icon matPrefix svgIcon="lock"></mat-icon>
            <button tabindex="-1" mat-icon-button (click)="visible = !visible" type="button" matSuffix>
                <mat-icon *ngIf="!visible" svgIcon="visibility"></mat-icon>
                <mat-icon *ngIf="visible" svgIcon="visibility_off"></mat-icon>
            </button>
            <mat-error class="pb-1">
                {{'auth.sign-up.step1.errors.password' | translate }}
            </mat-error>
        </mat-form-field>

        <mat-form-field class="flex-auto">
            <mat-label>
                {{ 'private.profile.user.form.fields.password-repeat.label' | translate}}
            </mat-label>
            <input formControlName="passwordCheck" [type]="visible ? 'text': 'password'" matInput required
                   name="password" autocomplete="new-password">
            <mat-icon matPrefix svgIcon="lock"></mat-icon>
            <button tabindex="-1" mat-icon-button (click)="visible = !visible" type="button" matSuffix>
                <mat-icon *ngIf="!visible" svgIcon="visibility"></mat-icon>
                <mat-icon *ngIf="visible" svgIcon="visibility_off"></mat-icon>
            </button>
            <mat-error class="flex-auto gt-xs:pr-3">
                {{'common.errors.password.verification' | translate }}
            </mat-error>
        </mat-form-field>

        <div
            class="flex items-center justify-end border-t -mx-8 mt-5 px-8 py-5 light:bg-cool-gray-50 dark:bg-cool-gray-700">
            <button (click)="cancel.emit()" mat-button type="button">
                {{ 'common.actions.cancel' | translate}}
            </button>
            <button type="submit" class="px-6 ml-3" mat-flat-button color="warn"
                    [disabled]="loading || !formGroup.dirty || formGroup.invalid">
                <mat-progress-spinner class="mr-4" *ngIf="loading" [diameter]="24"
                                      mode="indeterminate"></mat-progress-spinner>
                <ng-container
                    *ngIf="!loading">{{ 'private.profile.user.form.actions.update-password' | translate}}</ng-container>
            </button>
        </div>
    </form>
</treo-card>
