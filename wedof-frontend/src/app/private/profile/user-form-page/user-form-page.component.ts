import {Component} from '@angular/core';
import {Router} from '@angular/router';
import {Select} from '@ngxs/store';
import {UserState} from '../../../shared/api/state/user.state';
import {Observable} from 'rxjs';
import {User} from '../../../shared/api/models/user';

@Component({
    selector: 'app-user-form-page',
    templateUrl: './user-form-page.component.html',
    styleUrls: ['./user-form-page.component.scss']
})
export class UserFormPageComponent {


    @Select(UserState.user) user$: Observable<User>;

    constructor(
        private _router: Router,
    ) {
    }

    cancel(): void {
        this._router.navigate(['profil']);
    }

}
