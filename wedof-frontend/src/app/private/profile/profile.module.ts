import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {ReactiveFormsModule} from '@angular/forms';
import {ClipboardModule} from 'ngx-clipboard';

import {SharedModule} from '../../shared/shared.module';
import {ProfileRoutingModule} from './profile-routing.module';
import {ProfileComponent} from './profile.component';
import {UserComponent} from './user/user.component';
import {OrganismComponent} from './organism/organism.component';
import {UserFormComponent} from './user-form-page/user-form/user-form.component';
import {OrganismFormComponent} from './organism-form-page/organism-form/organism-form.component';
import {UserFormPageComponent} from './user-form-page/user-form-page.component';
import {ChangePasswordFormComponent} from './user-form-page/change-password-form/change-password-form.component';
import {LogoutConfirmationComponent} from './user-form-page/logout-confirmation/logout-confirmation.component';
import {ApiTokenComponent} from './api-token/api-token.component';
import {ApiTokenDialogFormComponent} from './api-token/api-token-dialog-form/api-token-dialog-form.component';
import {MatTooltipModule} from '@angular/material/tooltip';
import {MatCardModule} from '@angular/material/card';
import {OrganismUserDialogComponent} from './organism-form-page/organism-user-dialog/organism-user-dialog.component';
import {FileModule} from '../../shared/file/file.module';
import {CertifierCdcComponent} from './organism-form-page/certifier-cdc/certifier-cdc.component';
import {OrganismCustomizeComponent} from './organism-form-page/organism-customize/organism-customize.component';
import {OrganismUsersListComponent} from './organism-form-page/organism-users-list/organism-users-list.component';
import {SubscriptionModule} from '../../shared/subscription/subscription.module';
import {DialogOrganismCollectInformationsComponent} from './organism-form-page/dialog-organism-collect-informations/dialog-organism-collect-informations.component';
import {ImportCatalogDialogComponent} from './organism-form-page/import-catalog-dialog/import-catalog-dialog.component';
import {CatalogMcfComponent} from './organism-form-page/catalog-mcf/catalog-mcf.component';

@NgModule({
    declarations: [
        ProfileComponent,
        UserComponent,
        OrganismComponent,
        UserFormComponent,
        OrganismFormComponent,
        UserFormPageComponent,
        ChangePasswordFormComponent,
        LogoutConfirmationComponent,
        ApiTokenComponent,
        ApiTokenDialogFormComponent,
        CertifierCdcComponent,
        OrganismUserDialogComponent,
        OrganismCustomizeComponent,
        OrganismUsersListComponent,
        ImportCatalogDialogComponent,
        DialogOrganismCollectInformationsComponent,
        CatalogMcfComponent
    ],
    imports: [
        CommonModule,
        ProfileRoutingModule,
        SharedModule,
        ReactiveFormsModule,
        ClipboardModule,
        MatTooltipModule,
        MatCardModule,
        FileModule,
        SubscriptionModule
    ]
})
export class ProfileModule {
}
