import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';

import {ProfileComponent} from './profile.component';
import {UserFormPageComponent} from './user-form-page/user-form-page.component';
import {OrganismFormComponent} from './organism-form-page/organism-form/organism-form.component';

const routes: Routes = [
    {
        path: '',
        component: ProfileComponent,
    }, {
        path: 'utilisateur',
        component: UserFormPageComponent
    },
    {
        path: 'organisme',
        component: OrganismFormComponent
    },
    {
        path: 'connexions/:dataProviderName',
        component: ProfileComponent
    }
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class ProfileRoutingModule {
}
