<treo-card class="flex flex-col items-center p-8 w-full">
  <mat-icon svgIcon="account_circle" class="w-32 h-32 rounded-full"></mat-icon>
  <div class="text-2xl font-semibold leading-tight text-center mt-8">
    {{ user?.name }}
  </div>
  <div class="text-secondary leading-tight text-center mt-1">
    {{ user?.email }}
  </div>
  <div class="text-secondary leading-tight text-center mt-1">
    {{ user?.phoneNumber }}
  </div>
  <hr class="w-full border-t mt-8 mb-0">
  <a [routerLink]="['utilisateur']" mat-button color="primary" class="px-6 mt-4">
    {{ 'private.profile.user.update' | translate }}
  </a>
</treo-card>
