<form (ngSubmit)="submit()" [formGroup]="formGroup">
    <app-dialog-layout [title]="'private.profile.api-token.form.title' | translate" [actions]="actions"
        [disabled]="loading" [errorMessages]=" errorMessages" (dialogClose)="close()">

        <mat-form-field class="pb-2 input-line">
            <mat-label>{{ "private.profile.api-token.form.labels.name" | translate}}</mat-label>
            <input id="name" matInput formControlName="name"
                [placeholder]="'private.profile.api-token.form.placeholders.name' | translate" required />
            <mat-error>
                {{'common.errors.required' | translate }}
            </mat-error>
        </mat-form-field>

        <ng-template #actions>
            <button type="submit" mat-flat-button color="primary">
                <mat-progress-spinner *ngIf="loading; else submitLabel" [diameter]="24" mode="indeterminate">
                </mat-progress-spinner>
                <ng-template #submitLabel>
                    {{ 'common.actions.submit' | translate }}
                </ng-template>
            </button>
        </ng-template>
    </app-dialog-layout>
</form>