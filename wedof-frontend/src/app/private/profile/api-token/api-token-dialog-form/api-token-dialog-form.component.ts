import {HttpErrorResponse} from '@angular/common/http';
import {Component, Injector, OnInit} from '@angular/core';
import {Validators} from '@angular/forms';
import {ApiToken} from '../../../../shared/api/models/api-token';
import {ApiTokenService} from '../../../../shared/api/services/api-token.service';
import {AbstractDialogFormComponent} from '../../../../shared/material/form/abstract-dialog-form.component';
import {ApiError} from '../../../../shared/errors/errors.types';

@Component({
    selector: 'app-api-token-dialog-form',
    templateUrl: './api-token-dialog-form.component.html',
    styleUrls: ['./api-token-dialog-form.component.scss']
})
export class ApiTokenDialogFormComponent extends AbstractDialogFormComponent<ApiToken> implements OnInit {

    constructor(
        injector: Injector,
        private _apiTokenService: ApiTokenService,
    ) {
        super(injector);
    }

    ngOnInit(): void {
        this.initForm();
    }

    submit(): void {
        this.errorMessages = [];
        if (this.formGroup.valid) {
            this.loading = true;
            const name = this.formGroup.value.name;

            this._apiTokenService.create(name)
                .subscribe({
                    next: (certifierAccess) => {
                        this.submitted$.next(certifierAccess);
                        this.close();
                    },
                    error: (httpErrorResponse: HttpErrorResponse) => {
                        this.loading = false;
                        this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
                    }
                });
        }
    }

    protected initForm(): void {
        this.formGroup = this._fb.group({
            name: ['', Validators.required],
        });
    }

}
