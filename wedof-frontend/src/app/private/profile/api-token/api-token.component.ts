import {Component, Input, OnD<PERSON>roy, OnInit, TemplateRef, ViewChild} from '@angular/core';
import {MatDialog} from '@angular/material/dialog';
import {MatSnackBar} from '@angular/material/snack-bar';
import {filter, finalize, switchMap, takeUntil, tap} from 'rxjs/operators';
import {ApiToken} from '../../../shared/api/models/api-token';
import {ApiTokenService} from '../../../shared/api/services/api-token.service';
import {DeletionConfirmationComponent} from '../../../shared/material/action-confirmation/deletion-confirmation.component';
import {ApiTokenDialogFormComponent} from './api-token-dialog-form/api-token-dialog-form.component';
import {Select} from '@ngxs/store';
import {SubscriptionState} from '../../../shared/api/state/subscription.state';
import {Observable, Subject} from 'rxjs';
import {Subscription} from '../../../shared/api/models/subscription';

@Component({
    selector: 'app-api-token',
    templateUrl: './api-token.component.html',
    styleUrls: ['./api-token.component.scss']
})
export class ApiTokenComponent implements OnInit, OnDestroy {
    isLoading = false;
    @Input() tokens: ApiToken[];
    @ViewChild('copySuccess') copySuccessTemplate: TemplateRef<any>;

    requestsCount: number;
    @Select(SubscriptionState.subscription) subscription$: Observable<Subscription>;
    private _unsubscribeAll = new Subject<void>();

    constructor(
        private _snackBar: MatSnackBar,
        private _apiTokenService: ApiTokenService,
        private _dialog: MatDialog,
    ) {
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    ngOnInit(): void {
        this.subscription$.pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe((subscription) => {
            this.requestsCount = subscription.requestCount;
        });
    }

    addToken(): void {
        const dialogRef = this._dialog.open(ApiTokenDialogFormComponent, {
            panelClass: 'full-page-scroll-40',
            height: 'auto'
        });

        dialogRef.componentInstance.submitted$.subscribe((data: ApiToken) => {
            if (data) {
                this.tokens = [...this.tokens, data];
            }
        });
    }

    delete(token: ApiToken): void {
        const dialogRef = this._dialog.open(DeletionConfirmationComponent, {
            panelClass: 'full-page-scroll-40',
            height: 'auto',
            data: {
                messageKey: 'private.profile.api-token.deletion',
                data: token
            }
        });
        dialogRef.componentInstance.actionValue$.pipe(
            filter(confirmation => confirmation),
            tap(() => this.isLoading = true),
            switchMap(() => this._apiTokenService.delete(token)),
            finalize(() => {
                dialogRef.componentInstance.close();
                this.isLoading = false;
            })
        ).subscribe(() => {
            this.tokens = this.tokens.filter((apiToken) => {
                return apiToken !== token;
            });
        });
    }

    onTokenCopy(): void {
        this._snackBar.openFromTemplate(this.copySuccessTemplate, {
            duration: 2000,
        });
    }
}
