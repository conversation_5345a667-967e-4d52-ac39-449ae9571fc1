<treo-card class="flex flex-col p-8 w-full">
    <div class="text-2xl font-semibold leading-tight">
        {{ 'private.profile.api-token.title' | translate }}
    </div>
    <a href="/api/doc" target="_blank">
        {{ 'private.profile.api-token.apiDoc' | translate }}
    </a>
    <div class="mt-2">
        {{ 'common.token.requestCountTitle' | translate }} : {{requestsCount}}
    </div>
    <hr class="w-full border-t my-6">
    <div class="flex flex-row items-end" *ngFor="let apiToken of tokens, let i = index">
        <mat-form-field class="flex-grow">
            <mat-label class="flex">{{apiToken.name}}
                <p *ngIf="apiToken.lastUsed" class="italic pl-1">{{'private.profile.api-token.lastUsed' | translate }}
                    {{apiToken.lastUsed | date:'dd/MM/yyyy'}} à {{ apiToken.lastUsed | date:'shortTime'}}</p>
            </mat-label>
            <input matInput [value]="apiToken.token" [attr.aria-label]="apiToken.token" readonly
                   type="password"
                   ngxClipboard [cbContent]="apiToken.token" (click)="onTokenCopy()" #apiTokenField>
            <button mat-button matSuffix mat-icon-button ngxClipboard [cbContent]="apiToken.token"
                    (cbOnSuccess)="onTokenCopy()"
                    [attr.aria-label]="'private.profile.api-token.actions.copy' | translate">
                <mat-icon>content_copy</mat-icon>
            </button>
            <button mat-button matSuffix mat-icon-button ngxClipboard
                    (click)="apiTokenField.type === 'password' ? apiTokenField.type = 'text' : apiTokenField.type = 'password'">
                <mat-icon *ngIf="apiTokenField.type === 'password'"
                          svgIcon="visibility"></mat-icon>
                <mat-icon *ngIf="apiTokenField.type === 'text'"
                          svgIcon="visibility_off"></mat-icon>
            </button>
        </mat-form-field>
        <button *ngIf="i !== 0" class="mb-5 ml-2" mat-icon-button [disabled]="isLoading || tokens.length === 1"
                [attr.aria-label]="'common.actions.delete' | translate"
                (click)="delete(apiToken)"
                color="warn">
            <mat-icon>delete</mat-icon>
        </button>
    </div>
    <button mat-button color="primary" class="px-6 mt-4" (click)="addToken()">
        {{ 'private.profile.api-token.actions.add' | translate }}
    </button>
</treo-card>

<ng-template #copySuccess>
    {{ 'private.profile.api-token.copySuccess' | translate }}
</ng-template>
