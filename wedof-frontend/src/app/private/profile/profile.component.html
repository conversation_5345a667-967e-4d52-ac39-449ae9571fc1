<div class="content-layout fullwidth-basic-normal-scroll">
    <div class="main">
        <div class="content flex flex-wrap">
            <div class="flex flex-col max-w-120 w-full p-4">
                <app-organism [organism]="currentOrganism"></app-organism>
            </div>
            <div class="flex flex-col max-w-80 w-full p-4">
                <app-user [user]="user"></app-user>
            </div>
            <div class="flex flex-col max-w-140 w-full p-4" *ngIf="user.isOwner">
                <app-api-token [tokens]="user.apiTokens"></app-api-token>
            </div>

            <ng-container *ngIf="user.isOwner">
                <div class="flex flex-col card-dataprovider w-full p-4"
                     *ngFor="let dataProviderWithAuthentication of dataProvidersWithAuthentication">
                    <treo-card class="flex flex-col items-center p-8 w-full">
                        <div class="absolute right-0 top-0 h-16 w-16" *ngIf="DataProviderConfig[dataProviderWithAuthentication].beta">
                            <div class="absolute transform rotate-45 bg-primary-600 text-center text-white font-semibold py-1 ribbon-beta">beta</div>
                        </div>
                        <img src="../../../assets/images/dataProviders/{{dataProviderWithAuthentication}}-square.png"
                             class="w-1/2"
                             alt="{{'auth.'+dataProviderWithAuthentication+'.name' | translate}}"/>
                        <h5 class="text-center mt-2">{{'auth.' + dataProviderWithAuthentication + '.name' | translate}}
                            <span class="text-sm text-gray" *ngIf="user.can_impersonate || user.is_impersonator">
                                {{getIdConnection(dataProviderWithAuthentication)}}
                            </span>
                        </h5>
                        <hr class="w-full border-t mt-2 mb-0">
                        <a [routerLink]="isCardClickable(dataProviderWithAuthentication) ? ['connexions/' + dataProviderWithAuthentication] : null"
                           [class.mat-button]="getConnection(dataProviderWithAuthentication)?.state !== connectionState.ACTIVE"
                           [color]="!getConnection(dataProviderWithAuthentication)  || hasErrorState(getConnection(dataProviderWithAuthentication).state) ? 'primary' : 'accent'"
                           class="text-center no-underline px-6 mt-4 mb-4 whitespace-normal leading-normal"
                           [ngClass]="{'text-red': getConnection(dataProviderWithAuthentication) && (connectionState.REVOKED == getConnection(dataProviderWithAuthentication).state || getConnection(dataProviderWithAuthentication).state == connectionState.FAILED)}"
                        >
                            {{ (!getConnection(dataProviderWithAuthentication) ? 'auth.connection.state.' + DataProviderConfig[dataProviderWithAuthentication].type + '.inactive' : getTextHabilitationOrDelegationState(dataProviderWithAuthentication)) | translate }}
                        </a>
                        <treo-message
                            *ngIf="getConnection(dataProviderWithAuthentication)?.message"
                            [type]="getConnection(dataProviderWithAuthentication)?.messageType ? getConnection(dataProviderWithAuthentication)?.messageType : 'info'"
                            class="text-center"
                            appearance="outline"
                            [showIcon]="false">
                            {{getConnection(dataProviderWithAuthentication)?.message}}
                        </treo-message>
                    </treo-card>
                </div>
            </ng-container>
        </div>
    </div>
</div>
