<ng-template #loadingBlock>
    <mat-progress-spinner [diameter]="24" class="mr-4" mode="indeterminate"></mat-progress-spinner>
</ng-template>

<treo-card class="flex flex-col p-8 w-full">
    <div class="flex flex-row justify-between">
        <div class="text-2xl font-semibold leading-tight">
            <mat-icon class="icon-size-20 mr-3 mat-icon-no-color" color="primary"
                      role="img" svgIcon="verified_user"></mat-icon>
            {{ organism?.name }}
            <span class="text-sm text-gray" *ngIf="organism?.id">
                                ID : {{organism?.id}}
            </span>
        </div>
        <div *ngIf="organism.linkedInPageUrl else noLinkedInPage">
            <a href="{{organism.linkedInPageUrl}}" target="_blank">
                <mat-icon [matTooltip]="'private.profile.organism.linkedInPageUrl' | translate"
                          matTooltipPosition="above"
                          role="img"
                          svgIcon="feather:linkedin"></mat-icon>
            </a>
        </div>
        <ng-template #noLinkedInPage>
            <mat-icon [routerLink]="['organisme']"
                      [matTooltip]="'private.profile.organism.noLinkedInPageUrl' | translate"
                      matTooltipPosition="above"
                      class="opacity-25" role="img"
                      svgIcon="feather:linkedin"></mat-icon>
        </ng-template>
    </div>
    <treo-message *ngIf="organism?.isNonDiffusible"
                  [showIcon]="false"
                  appearance="outline"
                  class="my-2 card-loading-show" type="warning">
        <app-form-field-static [value]="'private.profile.organism.nondiffusible' | translate" icon="info"
                               type="text">
        </app-form-field-static>
    </treo-message>
    <hr class="w-full border-t my-6">
    <div *ngIf="organism; else loadingBlock" class="flex flex-col">
        <div class="flex items-center">
            <mat-icon class="icon-size-20 mr-3 mat-icon-no-color" role="img" svgIcon="location_on"></mat-icon>
            <a [href]="organism | googleMapsUrl" class="text-primary leading-none" target="_blank">
                {{ organism | locationToString }}
            </a>
        </div>
        <div class="flex items-center mt-4">
            <mat-icon class="icon-size-20 mr-3 mat-icon-no-color" role="img" svgIcon="email"></mat-icon>
            <span class="leading-none">
                <div *ngFor="let email of organism?.emails">
                    <a [href]="'mailto:'+email" class="text-primary">
                        {{ email }}
                    </a>
                </div>
            </span>
        </div>
        <div class="flex items-center mt-4">
            <mat-icon class="icon-size-20 mr-3 mat-icon-no-color" role="img" svgIcon="phone"></mat-icon>
            <span class="leading-none">
                <div *ngFor="let phone of organism?.phones">
                    <a [href]="'tel:'+phone" class="text-primary">
                        {{ phone }}
                    </a>
                </div>
            </span>
        </div>
        <div class="flex items-center mt-4">
            <mat-icon class="icon-size-20 mr-3 mat-icon-no-color" role="img" svgIcon="link"></mat-icon>
            <span class="leading-none">
                <div *ngFor="let url of organism?.urls">
                    <a [href]="url" class="text-primary" target="_blank">
                        {{ url }}
                    </a>
                </div>
            </span>
        </div>
        <div class="flex items-center mt-4">
            <mat-icon class="icon-size-20 mr-3 mat-icon-no-color" role="img" svgIcon="fingerprint"></mat-icon>
            <span class="leading-none">
                {{ organism?.siret }}
            </span>
        </div>
        <div class="flex items-center mt-4">
            <mat-icon class="icon-size-20 mr-3 mat-icon-no-color" role="img" svgIcon="loyalty"></mat-icon>
            <span class="leading-none">
                {{ organism?.agreement }}
            </span>
        </div>
        <hr class="w-full border-t mt-8 mb-0">
        <a [routerLink]="['organisme']" class="px-6 mt-4" color="primary" mat-button>
            {{ 'private.profile.organism.update' | translate }}
        </a>
    </div>
</treo-card>
