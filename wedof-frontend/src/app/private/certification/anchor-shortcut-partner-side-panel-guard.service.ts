import {Observable} from 'rxjs';
import {ShortcutPartnerSidePanelEnum} from '../../shared/utils/enums/shortcut-side-panel-enum';
import {Injectable} from '@angular/core';
import {ActivatedRouteSnapshot, CanActivate} from '@angular/router';

@Injectable({
    providedIn: 'root'
})
export class AnchorShortcutPartnerSidePanelGuard implements CanActivate {
    canActivate(
        route: ActivatedRouteSnapshot
    ): Observable<boolean> | Promise<boolean> | boolean  {
        const allowedAnchors: ShortcutPartnerSidePanelEnum[] = Object.values(ShortcutPartnerSidePanelEnum);
        const anchor: string = route.paramMap.get('anchor');
        if (allowedAnchors.includes(anchor as ShortcutPartnerSidePanelEnum)){
            return true;
        } else {
            window.location.href = '404-not-found';
            return false;
        }
    }
}
