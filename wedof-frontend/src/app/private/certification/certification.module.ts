import {NgModule} from '@angular/core';
import {ReactiveFormsModule} from '@angular/forms';

import {SharedModule} from '../../shared/shared.module';
import {CertificationCertifierAccessDialogFormComponent} from './partners/certification-certifier-access-dialog-form/certification-certifier-access-dialog-form.component';
import {CertificationRoutingModule} from './certification-routing.module';
import {CertificationFoldersViewByCertificationComponent} from './certification-folders/certification-folders-view-by-certification/certification-folders-view-by-certification.component';
import {CertificationFoldersViewByCertificationFoldersComponent} from './certification-folders/certification-folders-view-by-certification-folders/certification-folders-view-by-certification-folders.component';
import {CertificationPartnersViewByCertificationComponent} from './partners/certification-partners-view-by-certification/certification-partners-view-by-certification.component';
import {SubscriptionModule} from '../../shared/subscription/subscription.module';
import {MatTooltipModule} from '@angular/material/tooltip';
import {MdePopoverModule} from '@material-extended/mde';
import {KanbanCertificationFoldersComponent} from './certification-folders/kanban-certification-folders/kanban-certification-folders.component';
import {KanbanModule} from '../../shared/kanban-board/kanban.module';
import { DialogCertificationPartnersAddPartnerComponent } from './partners/certification-partners-view-by-certification/certification-partners-add-partner-dialog/dialog-certification-partners-add-partner.component';

@NgModule({
    declarations: [
        CertificationCertifierAccessDialogFormComponent,
        CertificationPartnersViewByCertificationComponent,
        CertificationFoldersViewByCertificationFoldersComponent,
        CertificationFoldersViewByCertificationComponent,
        KanbanCertificationFoldersComponent,
        DialogCertificationPartnersAddPartnerComponent,
    ],
    exports: [],
    imports: [
        CertificationRoutingModule,
        SharedModule,
        ReactiveFormsModule,
        SubscriptionModule,
        MatTooltipModule,
        MdePopoverModule,
        KanbanModule
    ]
})
export class CertificationModule {
}
