<mat-progress-spinner class="m-auto" *ngIf="loading; else showComponent" [diameter]="75"
                      mode="indeterminate"></mat-progress-spinner>

<ng-template #showComponent>
    <div class="w-full">
        <app-subscription-card
            *ngIf="!subscription.allowCertifiers; else showPartners"
            [subtitle]="'private.layout.user.subscription.certification.subtitle'"
            [explanation]="'private.layout.user.subscription.certification.explanation'"
            [description1]="'private.layout.user.subscription.certification.description.description1'"
            [description2]="'private.layout.user.subscription.certification.description.description2' "
            [description3]="'private.layout.user.subscription.certification.description.description3'"
            [image]="'assets/images/subscription/partnersSubscription.png'"
            [certifierSubscription]="true"
        ></app-subscription-card>
    </div>

    <ng-template #showPartners>
        <div class="min-w-full bg-white content-layout fullwidth-standard-inner-scroll">

            <mat-drawer-container>
                <mat-drawer-content>
                    <header class="flex flex-row justify-between pt-4 px-4 bg-white">
                        <div>
                            <h1>{{ 'private.certification.partners.title' | translate }}</h1>
                            <p class="subtitle">{{ 'private.certification.partners.subtitle' | translate }}</p>
                        </div>
                        <div class="py-4 pl-4 flex flex-row flex-nowrap items-center">
                            <div class="mr-8">
                                <mat-form-field class="w-90">
                                    <input [formControl]="queryCtrl"
                                           [placeholder]="'common.table.filters.globalSearch' | translate"
                                           [title]="'private.certification.partners.searchbar' | translate"
                                           matInput maxlength="50">
                                    <mat-icon matPrefix svgIcon="search"></mat-icon>
                                    <button mat-button *ngIf="queryCtrl.value" matSuffix mat-icon-button
                                            aria-label="Clear"
                                            (click)="clearQueryFilter()">
                                        <mat-icon svgIcon="close"></mat-icon>
                                    </button>
                                </mat-form-field>
                                <div class="flex items-center justify-end">
                                    <div class="flex flex-row"
                                         [matTooltip]=" 'private.certification.common.create.subtitle' | translate "
                                         [matTooltipPosition]="'above'"
                                         [matTooltipShowDelay]="500">
                                        <button type="button" color="primary"
                                                mat-flat-button (click)="openCreate()"
                                                [disabled]="openCertificationCreateSide || !subscription.allowCertifierPlus">
                                            {{ 'private.certification.common.create.title' | translate }}</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </header>
                    <main>
                        <ng-template #noData>
                            <p class="no-table p-2">
                                {{ 'private.certification.partners.certifications.no-data' | translate }}
                            </p>
                        </ng-template>

                        <app-wrapper-spinner [active]="isLoading">
                            <ng-container *ngIf="isLoading || displayedData?.length || activeFilters ; else noData">

                                <table [dataSource]="displayedData" mat-table>
                                    <tr class="collapsable-row" mat-row
                                        *matRowDef="let row; columns: displayedColumns;">
                                    </tr>
                                    <tr mat-footer-row *matFooterRowDef="['noDataForFilters']"
                                        [hidden]="isLoading || displayedData?.length">
                                    </tr>
                                    <ng-container matColumnDef="noDataForFilters">
                                        <td mat-footer-cell *matFooterCellDef [attr.colspan]="displayedColumns.length">
                                            {{ 'common.table.filters.no-data' | translate }}
                                        </td>
                                    </ng-container>

                                    <ng-container matColumnDef="name">
                                        <td class="p-0" mat-cell *matCellDef="let certification">
                                            <div class="font-bold flex flex-row justify-between box"
                                                 (click)="toggleRow(certification, $event)"
                                                 [ngClass]="{'sticky-section-title': isOpened(certification)}">
                                                <div class="flex flex-row py-4 pl-4">
                                                    <mat-icon class="self-center"
                                                              [svgIcon]="isOpened(certification) ? 'expand_less' : 'expand_more'">
                                                    </mat-icon>
                                                    <a [routerLink]="['/certification/partenariats/' + certification.certifInfo + '/modelesAudit']"
                                                       mat-icon-button>
                                                        <mat-icon *ngIf="certification.auditsPendingCancellation" class="ml-1 mr-1 text-grey"
                                                                  svgIcon="cancel_presentation"
                                                                  [matTooltip]="'private.certification.partners.auditsPendingCancellation'  | translate">
                                                        </mat-icon>
                                                        <mat-icon *ngIf="!certification.auditsPendingCancellation"
                                                                  class="ml-1 mr-1"
                                                                  svgIcon="fact_check"
                                                                  [matTooltip]="'private.certification.partners.allowAudits'  | translate : {
                                                              state : certification.allowAudits ? 'active' : 'inactive' }"
                                                                  [class]="certification.allowAudits ? 'text-green' : 'text-red opacity-25'">
                                                        </mat-icon>
                                                    </a>
                                                    <span
                                                        class="pl-1 self-center">{{ certification.externalId }}
                                                        - {{ certification.name }}</span>
                                                    <app-tag *ngIf="!certification.enabled"
                                                             class="ml-2 self-center text-white" [color]="'#64748b'">
                                                        {{ 'private.certification.partners.enabled.' + certification.enabled | translate }}
                                                    </app-tag>
                                                    <app-tag class="ml-2 self-center">
                                                        {{ 'private.certification.partners.certifications.partners' | translate: certification }}
                                                    </app-tag>
                                                </div>
                                                <div class="flex items-center">
                                                    <button class="mr-5"
                                                            [matTooltip]="'private.certification.partners.addPartner.title' | translate"
                                                            mat-icon-button
                                                            (click)=" toggleRowIfNotOpened(certification) ; openAddPartnerDialog($event, certification)">
                                                        <mat-icon class="icon-size-25" svgIcon="add_circle"></mat-icon>
                                                    </button>

                                                    <div class="bg-gray-400 h-full flex items-center">
                                                        <button class="bg-gray-400" mat-icon-button
                                                                (click)="toggleSide(certification); $event.stopPropagation()">
                                                            <mat-icon
                                                                [svgIcon]="isSideOpened(certification) ? 'chevron_right' : 'chevron_left'"></mat-icon>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                            <ng-container *ngIf="isFetched(certification)">
                                                <app-certification-partners-table #certificationPartnersTable
                                                                                  [certification]="certification"
                                                                                  (followingRow)="openSide(certification, $event)"
                                                                                  [selectedRow]="selectedCertificationPartner"
                                                                                  [externalSelectionChange]="true"
                                                                                  [dataQuery$]="organismQuery$"
                                                                                  [hidden]="!isOpened(certification)"
                                                                                  [partnersFilters$]="organismFilters$">
                                                </app-certification-partners-table>
                                            </ng-container>
                                        </td>
                                    </ng-container>
                                </table>

                                <app-paginator [length]="total"
                                               [pageSizeOptions]="pageSizeOptions"
                                               [scrollTopOnPageChange]="true"
                                               (page)="onPageEvent($event)">
                                </app-paginator>
                            </ng-container>
                        </app-wrapper-spinner>
                    </main>
                </mat-drawer-content>
                <mat-drawer #sidenav mode="side" position="end" [autoFocus]="false" [disableClose]="true">
                    <app-certification-create-side
                        #certificationCreateSide
                        *ngIf="openCertificationCreateSide"
                        (closeSide)="closeSide()"
                        (certificationCreated)="sendInsertRow($event)">
                    </app-certification-create-side>
                    <app-certification-partner-side
                        *ngIf="selectedCertificationPartner"
                        [certification]="selectedCertification"
                        [certificationPartner]="selectedCertificationPartner"
                        (certificationPartnerChange)="updateSelectedCertificationPartner($event)"
                        (certificationPartnerRefreshed)="sendRefreshRow($event)"
                        [organism]="organism"
                        [hasPrevious]="getCertificationPartnerTableComponent(selectedCertification.certifInfo)?.hasPrevious(selectedCertificationPartner)"
                        [hasNext]="getCertificationPartnerTableComponent(selectedCertification.certifInfo)?.hasNext(selectedCertificationPartner)"
                        (next)="nextCertificationPartner()"
                        (previous)="previousCertificationPartner()"
                        (certificationUpdated)="onCertificationUpdated($event)"
                        (closeSide)="closeSide()"></app-certification-partner-side>
                    <app-certification-side
                        *ngIf="!selectedCertificationPartner && selectedCertification"
                        [certification]="selectedCertification"
                        [hasPrevious]="hasPrevious(selectedCertification)"
                        [hasNext]="hasNext(selectedCertification)"
                        (next)="next()"
                        (previous)="previous()"
                        (certificationUpdated)="onCertificationUpdated($event)"
                        (closeSide)="closeSide()"></app-certification-side>
                </mat-drawer>
            </mat-drawer-container>
        </div>
    </ng-template>
</ng-template>

