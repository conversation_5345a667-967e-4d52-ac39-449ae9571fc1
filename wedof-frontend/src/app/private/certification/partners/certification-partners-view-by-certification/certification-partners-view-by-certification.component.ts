import {<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Que<PERSON>List, ViewChild, ViewChildren} from '@angular/core';
import {Certification} from '../../../../shared/api/models/certification';
import {CertificationPartner} from '../../../../shared/api/models/certification-partner';
import {AbstractCertificationOrganismsTable} from '../../abstract-certification-organisms.component';
import {DataResponse} from '../../../../shared/material/table/abstract-table.component';
import {CertificationPartnersTableComponent} from '../../../../shared/certification/certification-partners-table/certification-partners-table.component';
import {Subscription, SubscriptionTypes} from '../../../../shared/api/models/subscription';
import {MatDialog} from '@angular/material/dialog';
import {DialogCertificationPartnersAddPartnerComponent} from './certification-partners-add-partner-dialog/dialog-certification-partners-add-partner.component';
import {Select} from '@ngxs/store';
import {SubscriptionState} from '../../../../shared/api/state/subscription.state';
import {combineLatest, Observable} from 'rxjs';
import {takeUntil} from 'rxjs/operators';
import {DialogUpgradeSubscriptionComponent} from '../../../../shared/subscription/dialog-upgrade-subscription/dialog-upgrade-subscription.component';
import {OrganismState} from '../../../../shared/api/state/organism.state';
import {Organism} from '../../../../shared/api/models/organism';
import {ShortcutPartnerSidePanelEnum} from '../../../../shared/utils/enums/shortcut-side-panel-enum';
import {getAnchor} from '../../../../shared/utils/shortcut-side-panel-utils';
import {CertificationCreateSideComponent} from '../../../../shared/sides/certification-create-side/certification-create-side.component';

@Component({
    selector: 'home',
    templateUrl: './certification-partners-view-by-certification.component.html',
    styleUrls: ['./certification-partners-view-by-certification.component.scss'],
})
export class CertificationPartnersViewByCertificationComponent extends AbstractCertificationOrganismsTable implements AfterViewInit, OnDestroy {

    context = 'partenariats';
    displayedColumns: string[] = ['name'];
    subscription: Subscription;
    organism: Organism;
    loading = true;
    @ViewChildren('certificationPartnersTable') certificationPartnersTables: QueryList<CertificationPartnersTableComponent>;
    @ViewChild('certificationCreateSide') private certificationCreateSide: CertificationCreateSideComponent;
    @Select(SubscriptionState.subscription) subscription$: Observable<Subscription>;
    @Select(OrganismState.organism) organism$: Observable<Organism>;

    constructor(
        injector: Injector,
        private _dialog: MatDialog,
    ) {
        super(injector);
        this.needCertificationPartner = true;
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    ngAfterViewInit(): void {
        combineLatest([
            this.organism$,
            this.subscription$
        ]).pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe(([organism, subscription]) => {
            this.organism = organism;
            this.subscription = subscription;
            super.ngAfterViewInit();
            this.loading = false;
        });
    }

    openSide(certification: Certification, response?: DataResponse<CertificationPartner>): void {
        this.selectedCertification = certification;
        this.selectedCertificationPartner = response.row;
        const anchor: string = getAnchor(this._location.path());
        if (Object.values(ShortcutPartnerSidePanelEnum).some(
            (shortcut: ShortcutPartnerSidePanelEnum): boolean => shortcut === anchor) && response?.keepShortcutSide) {
            this._router.navigate(['certification', this.context, response.row._links.certification.certifInfo, response.row._links.partner.siret, anchor]);
        } else {
            this._router.navigate(['certification', this.context, response.row._links.certification.certifInfo, response.row._links.partner.siret]);
        }
    }

    nextCertificationPartner(): void {
        if (this.selectedCertificationPartner) {
            const certificationPartnerTableComponent = this.getCertificationPartnerTableComponent(this.selectedCertification.certifInfo);
            certificationPartnerTableComponent.selectNext(this.selectedCertificationPartner, true);
        }
    }

    previousCertificationPartner(): void {
        if (this.selectedCertificationPartner) {
            const certificationPartnerTableComponent = this.getCertificationPartnerTableComponent(this.selectedCertification.certifInfo);
            certificationPartnerTableComponent.selectPrevious(this.selectedCertificationPartner, true);
        }
    }

    isSideOpened(certification: Certification): boolean {
        return this.selectedOrganism == null && this.selectedCertificationPartner == null && certification?.certifInfo === this.selectedCertification?.certifInfo;
    }

    toggleRowIfNotOpened(certification: Certification): void {
        if (!this.isOpened(certification)) {
            this.toggleRow(certification);
        }
    }

    // override
    openCertificationSummary(response: DataResponse<Certification>): void {
        this.selectedCertificationPartner = null;
        super.openCertificationSummary(response);
    }

    sendRefreshRow(certificationPartner: CertificationPartner): void {
        this.getCertificationPartnerTableComponent(certificationPartner._links.certification.certifInfo).refreshRow(certificationPartner);
    }

    updateSelectedCertificationPartner(certificationPartner: CertificationPartner): void {
        this.selectedCertificationPartner = certificationPartner;
        this.getCertificationPartnerTableComponent(certificationPartner._links.certification.certifInfo).refreshRow(certificationPartner);
    }

    openAddPartnerDialog($event: MouseEvent, certification: Certification): void {
        $event.stopPropagation();
        if (this.subscription?.allowCertifierPlus) {
            this._router.navigate(['certification/partenariats']);
            const dialogRef = this._dialog.open(DialogCertificationPartnersAddPartnerComponent, {
                disableClose: true,
                panelClass: 'full-page-scroll-40',
                data: {
                    certification: certification
                }
            });
            dialogRef.afterClosed().subscribe((res) => {
                if (res?.data) {
                    const certificationPartnerTableComponent = this.getCertificationPartnerTableComponent(certification.certifInfo);
                    certificationPartnerTableComponent.forceRefresh();
                    const certificationPartner = res?.data;
                    certificationPartnerTableComponent.onRowClick(certificationPartner);
                }
            });
        } else {
            this._dialog.open(DialogUpgradeSubscriptionComponent, {
                panelClass: ['full-page-scroll-50'],
                data: {
                    organism: this.organism,
                    subscription: this.subscription,
                    fromPage: 'managePartnership',
                    subscriptionTypeToShow: SubscriptionTypes.CERTIFIER
                }
            });
        }
    }

    getCertificationPartnerTableComponent(certifInfo: string): CertificationPartnersTableComponent {
        return this.certificationPartnersTables?.find((certificationPartnersTable) => certificationPartnersTable.certification.certifInfo === certifInfo);
    }

    toggleSide(certification: Certification): void {
        if (!this.isSideOpened(certification)) {
            this.onRowClick(certification);
            this.toggleRowIfNotOpened(certification);
        } else {
            this.closeSide();
        }
    }

    onCertificationUpdated(certification: Certification): void {
        this.refreshRow(certification);
        if (this.selectedCertification && this.isRowEqual(certification, this.selectedCertification)) {
            this.selectedCertification = certification;
        }
    }
}
