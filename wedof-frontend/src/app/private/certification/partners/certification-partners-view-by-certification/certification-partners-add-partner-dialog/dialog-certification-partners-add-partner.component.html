<app-dialog-layout (dialogClose)="close()" [actions]="actions"
                   [showCancel]="false"
                   [title]=" 'private.certification.partners.addPartner.title' | translate ">

    <p class="mb-2">
        {{ "private.certification.partners.addPartner.message" | translate : {
        externalId: dialogData.certification.externalId,
        name: dialogData.certification.name
    }
        }}
        <ng-container *ngIf="dialogData.certification.type === certificationTypes.INTERNAL; else showSubtitle">
            {{ "private.certification.partners.addPartner.subtitleInternal" | translate }}
        </ng-container>
        <ng-template #showSubtitle>
            {{ "private.certification.partners.addPartner.subtitle" | translate }}
        </ng-template>
        <a (click)="close()"
           [routerLink]="['/aide/guides/certificateurs/gerer-partenariats']">(documentation)</a>
    </p>

    <form class="flex flex-col" [formGroup]="formCreatePartner" (ngSubmit)="submit()">
        <app-infinite-scroll class="flex-auto mt-5"
                             [controlName]="'partner'"
                             [noEntriesFoundLabel]="'private.certification.partners.addPartner.partner.noOrganismFound' | translate"
                             [placeholderLabel]="'private.certification.partners.addPartner.partner.placeholder' | translate"
                             [placeholder]="'private.certification.partners.addPartner.partner.placeholder' | translate"
                             [label]="'private.certification.partners.addPartner.partner.label' | translate "
                             [formGroup]="formCreatePartner"
                             [tooltip]="'private.certification.partners.addPartner.partner.toolTip'"
                             [resultFormatter]="resultFormatterOrganism"
                             [searchMethod]="searchMethodOrganism"
                             [parameters]="{'sort' : 'name', 'order' : 'asc', 'limit' : 20}"
                             [containerClass]="'flex-auto'"
                             (changeSelection)="clearErrorMessages()"
        ></app-infinite-scroll>

        <div *ngIf="(errorMessages?.length || errorMessagesRedirect?.length) && formCreatePartner.get('partner').value "
             class="flex items-center">
            <treo-message class="flex-auto" appearance="outline" [showIcon]="false" type="error">
                <ul>
                    <li *ngFor="let errorMessage of errorMessages">
                        {{ errorMessage }}
                    </li>
                    <li *ngFor="let errorMessageRedirect of errorMessagesRedirect">
                        {{ errorMessageRedirect }}
                        <a (click)="close()"
                           [routerLink]="['/certification/partenariats/' + dialogData.certification.certifInfo + '/' + formCreatePartner.get('partner').value.siret]">
                            {{'private.certification.partners.addPartner.displayPartnerIfExists' | translate}}
                        </a>
                    </li>
                </ul>
            </treo-message>
        </div>

        <ng-template #actions>
            <button [mat-dialog-close]="null" mat-flat-button> {{ 'common.actions.cancel' | translate }}   </button>
            <div class="flex flex-row py-2 mb-2">
                <button type="submit" color="primary" class="mt-2"
                        mat-flat-button (click)="submit()" [disabled]="disabled()">
                    <mat-progress-spinner class="mr-4" *ngIf="loading" [diameter]="24"
                                          mode="indeterminate"></mat-progress-spinner>
                    <ng-container *ngIf="!loading">{{'private.certification.partners.addPartner.submit' | translate}}
                    </ng-container>
                </button>
            </div>
        </ng-template>
    </form>


</app-dialog-layout>
