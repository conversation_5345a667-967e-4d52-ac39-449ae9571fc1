import {Component, Inject, OnInit} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material/dialog';
import {Certification, CertificationTypes} from '../../../../../shared/api/models/certification';
import {OrganismService} from '../../../../../shared/api/services/organism.service';
import {SearchMethod} from '../../../../../shared/material/infinite-scroll/infinite-scroll.component';
import {Organism} from '../../../../../shared/api/models/organism';
import {FormBuilder, FormGroup} from '@angular/forms';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../../../../shared/errors/errors.types';
import {CertificationPartnerService} from '../../../../../shared/api/services/certification-partner.service';
import {of} from 'rxjs';

@Component({
    selector: 'app-dialog-certification-partners-add-partner',
    templateUrl: './dialog-certification-partners-add-partner.component.html',
    styleUrls: ['./dialog-certification-partners-add-partner.component.scss']
})
export class DialogCertificationPartnersAddPartnerComponent implements OnInit {
    searchMethodOrganism: SearchMethod;
    resultFormatterOrganism: any;
    formCreatePartner: FormGroup;
    loading = false;
    errorMessages: string[];
    errorMessagesRedirect: string[];
    certificationTypes = CertificationTypes;

    constructor(
        public dialogRef: MatDialogRef<DialogCertificationPartnersAddPartnerComponent>,
        @Inject(MAT_DIALOG_DATA) public dialogData: { certification: Certification },
        private _organismService: OrganismService,
        private _certificationPartnerService: CertificationPartnerService,
        private _formBuilder: FormBuilder,
    ) {
    }

    ngOnInit(): void {
        this.resultFormatterOrganism = (organism: Organism) => organism.name + ' (...' + organism.siret.substr(organism.siret.length - 5) + ')';
        this.searchMethodOrganism = (params: any) => params.query?.length >= 2 ? this._organismService.find(params) : of(null);
        this.formCreatePartner = this._formBuilder.group({
            partner: [null]
        });
    }

    close(): void {
        this.dialogRef.close();
    }

    disabled(): boolean {
        return this.loading || !this.formCreatePartner.dirty || this.formCreatePartner.invalid || this.errorMessagesRedirect?.length >= 1 || this.errorMessages?.length >= 1;
    }

    submit(): void {
        this.loading = true;
        this.errorMessages = [];
        this.errorMessagesRedirect = [];
        if (this.formCreatePartner.valid) {
            this._certificationPartnerService.create({
                certifInfo: this.dialogData.certification.certifInfo,
                siret: this.formCreatePartner.get('partner').value ? this.formCreatePartner.get('partner').value.siret : null
            }).subscribe({
                    next: (certificationPartner) => {
                        this.loading = false;
                        this.dialogRef.close({data: certificationPartner});
                    },
                    error: (httpErrorResponse: HttpErrorResponse) => {
                        this.loading = false;
                        const apiError = httpErrorResponse.error as ApiError;
                        if (apiError.detail.includes('Object(App\\Entity\\CertificationPartner).partner')) {
                            this.errorMessagesRedirect = ['Erreur, ce partenariat existe déjà pour la certification '
                            + this.dialogData.certification.externalId + ' ' + this.dialogData.certification.name + '. '];
                        } else {
                            this.errorMessages = apiError.errorMessages;
                        }
                    }
                }
            );
        }
    }

    clearErrorMessages(): void {
        this.errorMessagesRedirect = [];
        this.errorMessages = [];
    }
}
