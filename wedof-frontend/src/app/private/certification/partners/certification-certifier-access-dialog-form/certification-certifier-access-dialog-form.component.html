<form (ngSubmit)="submit()" [formGroup]="formGroup">
    <app-dialog-layout
        [title]="'private.certification.partners.form.title' | translate: {name: dialogData.organism.name | titlecase}"
        [actions]="actions" [disabled]="loading" [errorMessages]="errorMessages" (dialogClose)="close()">
        <p class="mb-4">
            {{ 'private.certification.partners.form.description' | translate }}
        </p>

        <mat-form-field class="pb-2 input-line">
            <mat-label>{{ 'private.certification.partners.form.labels.email' | translate }}</mat-label>
            <input id="email" matInput formControlName="email" [placeholder]="'private.certification.partners.form.placeholders.email' | translate" required/>
            <mat-icon matPrefix svgIcon="mail"></mat-icon>
            <mat-error>
                {{ 'private.certification.partners.form.errors.email' | translate }}
            </mat-error>
        </mat-form-field>

        <mat-form-field class="pb-2 input-line">
            <mat-label>{{ 'private.certification.partners.form.labels.full-name' | translate }}</mat-label>
            <input id="full-name" matInput formControlName="name" [placeholder]="'private.certification.partners.form.placeholders.full-name' | translate"/>
            <mat-icon matPrefix svgIcon="account_circle"></mat-icon>
        </mat-form-field>

        <ng-template #actions>
            <button type="submit" mat-flat-button color="primary">
                <mat-progress-spinner *ngIf="loading; else submitLabel" [diameter]="24" mode="indeterminate">
                </mat-progress-spinner>
                <ng-template #submitLabel>
                    {{ 'common.actions.submit' | translate }}
                </ng-template>
            </button>
        </ng-template>

    </app-dialog-layout>
</form>
