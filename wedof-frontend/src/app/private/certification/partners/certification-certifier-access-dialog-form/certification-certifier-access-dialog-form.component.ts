import {HttpErrorResponse} from '@angular/common/http';
import {Component, Inject, Injector, OnInit} from '@angular/core';
import {Validators} from '@angular/forms';
import {MAT_DIALOG_DATA} from '@angular/material/dialog';
import {Certification} from '../../../../shared/api/models/certification';
import {CertifierAccess} from '../../../../shared/api/models/certifier-access';
import {Organism} from '../../../../shared/api/models/organism';
import {CertifierAccessService} from '../../../../shared/api/services/certifier-access.service';
import {AbstractDialogFormComponent} from '../../../../shared/material/form/abstract-dialog-form.component';
import {ApiError} from '../../../../shared/errors/errors.types';

@Component({
    selector: 'app-certification-certifier-access-dialog-form',
    templateUrl: './certification-certifier-access-dialog-form.component.html',
    styleUrls: ['./certification-certifier-access-dialog-form.component.scss']
})
export class CertificationCertifierAccessDialogFormComponent extends AbstractDialogFormComponent<CertifierAccess> implements OnInit {

    constructor(
        injector: Injector,
        @Inject(MAT_DIALOG_DATA) public dialogData: { organism: Organism, certification: Certification },
        private _certifierAccessService: CertifierAccessService
    ) {
        super(injector);
    }

    ngOnInit(): void {
        this.initForm();
    }

    submit(): void {
        this.errorMessages = [];
        if (this.formGroup.valid) {
            this.loading = true;
            const recipient = this.formGroup.value;

            this._certifierAccessService.create(this.dialogData.organism, recipient).subscribe({
                next: (certifierAccess) => {
                    this.submitted$.next(certifierAccess);
                    this.close();
                },
                error: (httpErrorResponse: HttpErrorResponse) => {
                    this.loading = false;
                    this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
                }
            });
        }
    }

    protected initForm(): void {
        this.formGroup = this._fb.group({
            name: [''],
            email: [this.dialogData.organism.emails?.[0], [Validators.required, Validators.email]]
        });
    }

}
