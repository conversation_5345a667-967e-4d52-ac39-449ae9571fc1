import {AfterViewChecked, AfterViewInit, Directive, Injector, Input, OnDestroy, OnInit, ViewChild} from '@angular/core';
import {MatSidenav} from '@angular/material/sidenav';
import {ActivatedRoute, Router} from '@angular/router';
import {forkJoin, iif, Observable, of, ReplaySubject, Subject} from 'rxjs';
import {takeUntil, tap} from 'rxjs/operators';
import {Certification, CertificationQuery} from '../../shared/api/models/certification';
import {Organism} from '../../shared/api/models/organism';
import {PaginatedResponse} from '../../shared/api/services/abstract-paginated.service';
import {CertificationService} from '../../shared/api/services/certification.service';
import {OrganismService} from '../../shared/api/services/organism.service';
import {UserService} from '../../shared/api/services/user.service';
import {AbstractCollapsableRowTableComponent} from '../../shared/material/table/abstract-collapsable-row-table.component';
import {DataQuery, DataResponse} from '../../shared/material/table/abstract-table.component';
import {User} from '../../shared/api/models/user';
import {CertificationPartner} from '../../shared/api/models/certification-partner';
import {CertificationPartnerService} from '../../shared/api/services/certification-partner.service';
import {Select} from '@ngxs/store';
import {UserState} from '../../shared/api/state/user.state';
import {Location} from '@angular/common';
import {ShortcutCertificationSidePanelEnum} from '../../shared/utils/enums/shortcut-side-panel-enum';
import {getAnchor, isSiret} from '../../shared/utils/shortcut-side-panel-utils';

export interface OrganismFilters {
    certifInfo: string;
    query: string;
}

export interface OrganismQuery extends DataQuery {
    certifInfo?: string;
    siret: string;
}

@Directive()
// tslint:disable-next-line: directive-class-suffix
export abstract class AbstractCertificationOrganismsTable extends AbstractCollapsableRowTableComponent<Certification> implements OnInit, AfterViewInit, OnDestroy, AfterViewChecked {
    organismQuery$: Subject<OrganismQuery> = new Subject<OrganismQuery>();
    organismFilters$: ReplaySubject<OrganismFilters> = new ReplaySubject<OrganismFilters>();
    selectedCertification: Certification;
    selectedOrganism: Organism;
    selectedCertificationPartner: CertificationPartner = null;
    openCertificationCreateSide: boolean;

    @Input() dataQuery$: Subject<CertificationQuery> = new Subject<CertificationQuery>();

    @Select(UserState.user) user$: Observable<User>;

    @ViewChild('sidenav') sidenav: MatSidenav;


    protected _activatedRoute: ActivatedRoute;
    protected _certificationService: CertificationService;
    protected _certificationPartnerService: CertificationPartnerService;
    protected _userService: UserService;
    protected _organismService: OrganismService;
    protected _router: Router;
    protected _location: Location;
    protected context: string;
    protected needCertificationPartner = false;

    protected constructor(
        injector: Injector,
    ) {
        super(injector);
        this._activatedRoute = injector.get(ActivatedRoute);
        this._certificationService = injector.get(CertificationService);
        this._certificationPartnerService = injector.get(CertificationPartnerService);
        this._userService = injector.get(UserService);
        this._organismService = injector.get(OrganismService);
        this._router = injector.get(Router);
        this._location = injector.get(Location);
    }

    ngAfterViewChecked(): void {
        this._changeDetectorRef.detectChanges();
    }

    ngOnInit(): void {
        this.privateOnNgInit();
        this._activatedRoute.params.pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe((params) => {
            const {certification} = params;
            const {siret} = this._activatedRoute.snapshot.firstChild && isSiret(this._activatedRoute.snapshot.firstChild?.params?.siret)
                ? this._activatedRoute.snapshot.firstChild.params
                : {siret: null};
            if (certification && certification !== 'creer') {
                this.openCertificationCreateSide = false;
                const certification$ = this.selectedCertification?.certifInfo === certification
                    ? of(this.selectedCertification)
                    : this._certificationService.get(certification);
                iif(
                    () => !siret,
                    forkJoin([certification$, of(null), of(null)]),
                    forkJoin([
                        certification$,
                        this.selectedOrganism?.siret === siret
                            ? of(this.selectedOrganism)
                            : this._organismService.get(siret),
                        this.needCertificationPartner ? (this.selectedCertificationPartner?._links?.partner?.siret === siret
                            ? of(this.selectedCertificationPartner)
                            : this._certificationPartnerService.get({certifInfo: certification, siret})) : of(null)
                    ])
                ).subscribe(([selectedCertification, selectedOrganism, selectedCertificationPartner]: [Certification, Organism, CertificationPartner]) => {
                    this.selectedCertification = selectedCertification;
                    this.selectedOrganism = selectedOrganism;
                    this.selectedCertificationPartner = selectedCertificationPartner;
                    this.sidenav.open();
                });
            } else if (certification === 'creer') {
                this.clearSelectedRow();
                this.selectedCertification = null;
                this.selectedCertificationPartner = null;
                this.openCertificationCreateSide = true;
                setTimeout(() => {
                    this.sidenav.open();
                });
            } else {
                this.openCertificationCreateSide = false;
                this.selectedCertification = null;
                this.selectedOrganism = null;
                this.selectedCertificationPartner = null;
                if (this.sidenav?.opened) {
                    this.sidenav.close();
                }
            }
        });
    }

    ngOnDestroy(): void {
        super.ngOnDestroy();
        this.organismQuery$.complete();
        this.organismFilters$.complete();
    }

    ngAfterViewInit(): void {
        super.ngAfterViewInit();
        this.followingRow.subscribe((_certification: DataResponse<Certification>) => {
            this.openCertificationSummary(_certification);
        });
    }

    next(): void {
        if (this.selectedOrganism) {
            this.organismQuery$.next({
                certifInfo: this.selectedCertification.certifInfo,
                siret: this.selectedOrganism.siret,
                next: true,
                keepShortcutSide: true
            });
        } else {
            this.dataQuery$.next({
                certifInfo: this.selectedCertification.certifInfo,
                next: true,
                keepShortcutSide: true
            });
        }
    }

    previous(): void {
        if (this.selectedOrganism) {
            this.organismQuery$.next({
                certifInfo: this.selectedCertification.certifInfo,
                siret: this.selectedOrganism.siret,
                previous: true,
                keepShortcutSide: true
            });
        } else {
            this.dataQuery$.next({
                certifInfo: this.selectedCertification.certifInfo,
                previous: true,
                keepShortcutSide: true
            });
        }
    }

    closeSide(): void {
        this._router.navigate(['..'], {relativeTo: this._activatedRoute});
    }

    protected openCertificationSummary(response: DataResponse<Certification>): void {
        this.selectedCertification = response.row;
        this.selectedOrganism = null;
        const anchor: string = getAnchor(this._location.path());

        if (Object.values(ShortcutCertificationSidePanelEnum).some(
            (shortcut: ShortcutCertificationSidePanelEnum): boolean => shortcut === anchor) && response.keepShortcutSide) {
            this._router.navigate(['certification', this.context, this.selectedCertification.certifInfo, anchor]);
        } else {
            this._router.navigate(['certification', this.context, this.selectedCertification.certifInfo]);
        }
    }

    protected findRowWithDataQuery(row: Certification, query: CertificationQuery): boolean {
        return row.certifInfo === query.certifInfo;
    }

    protected getRowIdentifier(row: Certification): string | number {
        return row.certifInfo;
    }

    protected refreshData(): Observable<PaginatedResponse<Certification>> {
        return this._certificationService.list({
            ...this._filters$.value,
            organismType: 'certifier',
            limit: this.paginator.pageSize,
            page: this.paginator.pageIndex + 1,
            sort: 'createdOn',
            order: 'desc',
        }).pipe(
            tap((response) => {
                if (this.selectedCertification) {
                    this.openRow(this.selectedCertification);
                } else {
                    this.openRow(response?.payload?.[0]);
                }
            })
        );
    }

    private privateOnNgInit(): void {
        const {_certification} = this._activatedRoute.snapshot.params;
        const {siret} = this._activatedRoute.snapshot.firstChild && isSiret(this._activatedRoute.snapshot.firstChild?.params?.siret)
            ? this._activatedRoute.snapshot.firstChild.params
            : {siret: null};
        if (_certification) {
            this.queryCtrl.setValue(_certification);
            this.organismFilters$.next({certifInfo: _certification, query: siret});
        }

        this.user$.pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe((user: User) => {
            this.applyFilter({name: 'siret', value: user._links.mainOrganism.siret});
        });
    }

    openCreate(): void {
        this._router.navigate(['certification', 'partenariats', 'creer']).then((success) => {
            if (success) {
                this.selectedCertification = null;
            }
        });
    }

    sendInsertRow($newRow: Certification): void {
        this.insertRow($newRow);
        this.selectedCertification = $newRow;
        this.openRow(this.selectedCertification);
    }
}

