import {Observable} from 'rxjs';
import {ShortcutCertificationSidePanelEnum} from '../../shared/utils/enums/shortcut-side-panel-enum';
import {Injectable} from '@angular/core';
import {ActivatedRouteSnapshot, CanActivate} from '@angular/router';
import {isSiret} from '../../shared/utils/shortcut-side-panel-utils';

@Injectable({
    providedIn: 'root'
})
export class AnchorShortcutOrganismPartnerSidePanelGuardService implements CanActivate {
    canActivate(
        route: ActivatedRouteSnapshot
    ): Observable<boolean> | Promise<boolean> | boolean  {
        const allowedAnchors: ShortcutCertificationSidePanelEnum[] = Object.values(ShortcutCertificationSidePanelEnum);
        const siretOrAnchor: string = route.paramMap.get('siret');
        if (allowedAnchors.includes(siretOrAnchor as ShortcutCertificationSidePanelEnum)){
            return true;
        } else {
            if (isSiret(siretOrAnchor)){
                return true;
            } else {
                window.location.href = '404-not-found';
                return false;
            }
        }
    }

}
