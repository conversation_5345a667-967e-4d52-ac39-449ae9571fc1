import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {CertificationFoldersViewByCertificationComponent} from './certification-folders/certification-folders-view-by-certification/certification-folders-view-by-certification.component';
import {CertificationFoldersViewByCertificationFoldersComponent} from './certification-folders/certification-folders-view-by-certification-folders/certification-folders-view-by-certification-folders.component';
import {CertificationPartnersViewByCertificationComponent} from './partners/certification-partners-view-by-certification/certification-partners-view-by-certification.component';
import {CanDeactivateGuard} from '../../shared/utils/can-deactivate/can-deactivate.guard';
import {KanbanCertificationFoldersComponent} from './certification-folders/kanban-certification-folders/kanban-certification-folders.component';
import {AnchorShortcutPartnerSidePanelGuard} from './anchor-shortcut-partner-side-panel-guard.service';
import {AnchorShortcutFolderSidePanelGuard} from './anchor-shortcut-folder-side-panel-guard.service';
import {AnchorShortcutOrganismPartnerSidePanelGuardService} from './anchor-shortcut-organism-partner-side-panel-guard.service';

export const routes: Routes = [
    {
        // Lazy loaded behind 'certificateur'
        path: '',
        children: [
            {
                // dossiers
                path: '',
                children: [
                    {
                        path: 'dossiers',
                        redirectTo: 'dossiers/kanban/',
                        pathMatch: 'full',
                        canDeactivate: [CanDeactivateGuard],
                    }, {
                        path: 'dossiers/liste',
                        redirectTo: 'dossiers/liste/',
                        pathMatch: 'full',
                        canDeactivate: [CanDeactivateGuard]
                    }, {
                        path: 'dossiers/liste/:externalId',
                        component: CertificationFoldersViewByCertificationFoldersComponent,
                        canDeactivate: [CanDeactivateGuard],
                        children: [
                            {
                                path: ':anchor',
                                canActivate: [AnchorShortcutFolderSidePanelGuard],
                            }
                        ]
                    },  {
                        path: 'dossiers/kanban',
                        redirectTo: 'dossiers/kanban/',
                        pathMatch: 'full',
                        canDeactivate: [CanDeactivateGuard]
                    }, {
                        path: 'dossiers/kanban/:externalId',
                        component: KanbanCertificationFoldersComponent,
                        canDeactivate: [CanDeactivateGuard],
                        children: [
                            {
                                path: ':anchor',
                                canActivate: [AnchorShortcutFolderSidePanelGuard],
                            }
                        ]
                    }, {
                        path: 'certifications',
                        redirectTo: 'dossiers/certifications/',
                        pathMatch: 'full'
                    }, {
                        path: 'dossiers/certifications',
                        redirectTo: 'dossiers/certifications/',
                        pathMatch: 'full'
                    }, {
                        path: 'dossiers/certifications/:certification',
                        component: CertificationFoldersViewByCertificationComponent,
                        children: [
                            {
                                path: ':externalId',
                            },
                        ]
                    }
                ]
            },
            {
                path: 'partenariats',
                redirectTo: 'partenariats/',
                pathMatch: 'full'
            },
            {
                path: 'partenariats/:certification',
                component: CertificationPartnersViewByCertificationComponent,
                children: [
                    {
                        path: ':siret',
                        canActivate: [AnchorShortcutOrganismPartnerSidePanelGuardService]
                    },
                    {
                        path: ':siret/:anchor',
                        canActivate: [AnchorShortcutPartnerSidePanelGuard],
                    }
                ]
            },
            {
                path: 'organismes-suspicieux',
                redirectTo: 'organismes-suspicieux/',
                pathMatch: 'full'
            }
        ]
    }
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class CertificationRoutingModule {
}
