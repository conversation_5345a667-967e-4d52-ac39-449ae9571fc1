import {Observable} from 'rxjs';
import {ShortcutFolderSidePanelEnum} from '../../shared/utils/enums/shortcut-side-panel-enum';
import {Injectable} from '@angular/core';
import {ActivatedRouteSnapshot, CanActivate} from '@angular/router';

@Injectable({
    providedIn: 'root'
})
export class AnchorShortcutFolderSidePanelGuard implements CanActivate {
    canActivate(
        route: ActivatedRouteSnapshot
    ): Observable<boolean> | Promise<boolean> | boolean {
        const allowedAnchors: ShortcutFolderSidePanelEnum[] = Object.values(ShortcutFolderSidePanelEnum);
        const anchor: string = route.paramMap.get('anchor');
        if (allowedAnchors.includes(anchor as ShortcutFolderSidePanelEnum)) {
            return true;
        } else {
            window.location.href = '404-not-found';
            return false;
        }
    }
}
