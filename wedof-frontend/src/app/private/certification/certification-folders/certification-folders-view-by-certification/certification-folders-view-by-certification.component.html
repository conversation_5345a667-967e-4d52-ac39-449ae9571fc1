<div class="min-w-full bg-white content-layout fullwidth-standard-inner-scroll">
    <app-registration-folder-connection-check *ngIf="currentOrganism?.isTrainingOrganism">
    </app-registration-folder-connection-check>
    <mat-drawer-container>
        <mat-drawer-content>
            <header class="flex flex-row justify-between pt-4 px-4 bg-white">
                <div class="flex flex-col">
                    <div class="flex flex-row">
                        <h1>{{ 'private.certification.folders.certifications.title' | translate }}</h1>
                        <a class="flex flex-row pl-4 self-center"
                           [routerLink]="['/certification/dossiers/liste']">
                            <p class="text-center">{{ 'private.certification.folders.certifications.folderView' | translate }}</p>
                            <mat-icon class="pl-1 self-center" matPrefix svgIcon="sync_alt"></mat-icon>
                        </a>
                    </div>
                    <p class="subtitle">{{ 'private.certification.folders.certifications.subtitle' | translate }}</p>
                </div>
            </header>
            <main>
                <ng-template #noData>
                    <p class="no-table p-2">
                        {{ 'private.certification.partners.certifications.no-data' | translate }}
                    </p>
                </ng-template>

                <app-wrapper-spinner [active]="isLoading">
                    <ng-container *ngIf="isLoading || displayedData?.length || activeFilters ; else noData">

                        <table *ngIf="displayedData?.length" [dataSource]="displayedData" mat-table>
                            <tr class="collapsable-row" mat-row *matRowDef="let row; columns: displayedColumns;">
                            </tr>
                            <tr mat-footer-row *matFooterRowDef="['noDataForFilters']"
                                [hidden]="isLoading || displayedData?.length">
                            </tr>

                            <ng-container matColumnDef="noDataForFilters">
                                <td mat-footer-cell *matFooterCellDef [attr.colspan]="displayedColumns.length">
                                    {{ 'common.table.filters.no-data' | translate }}
                                </td>
                            </ng-container>

                            <ng-container matColumnDef="name">
                                <td class="p-0" mat-cell *matCellDef="let certification">
                                    <div class="font-bold flex flex-row justify-between box"
                                         (click)="toggleRow(certification, $event)"
                                         [ngClass]="{'sticky-section-title': isOpened(certification)}">
                                        <div class="flex flex-row py-4 pl-4">
                                            <mat-icon class="self-center"
                                                      [svgIcon]="isOpened(certification) ? 'expand_less' : 'expand_more'">
                                            </mat-icon>
                                            <span
                                                class="pl-2 self-center">{{ certification.externalId }}
                                                - {{ certification.name }}</span>
                                        </div>
                                        <div class="flex">
                                            <app-tag class="mr-2 self-center"
                                                     *ngIf="certification?.certificationInfo?.folderCount != null"
                                                     color="#f2e5f2">
                                                {{
                                                    'private.certification.folders.certifications.folderCount'
                                                        | translate: certification
                                                }}
                                            </app-tag>
                                            <div class="flex bg-gray-400">
                                                <button class="self-center" mat-icon-button
                                                        (click)="onRowClick(certification); toggleRowIfNotOpened(certification); $event.stopPropagation()">
                                                    <mat-icon
                                                        [svgIcon]="isSideOpened(certification) ? 'chevron_right' : 'chevron_left'"></mat-icon>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <ng-container *ngIf="isFetched(certification)">
                                        <app-certification-folder-table #certificationFolderTable
                                                                        [displayedColumns]="certificationFolderTableColumns"
                                                                        [dataQuery$]="certificationFolderQuery$"
                                                                        [certificationFolderFilters$]="certificationFolderFilters$"
                                                                        (certificationFolderDeleted)="sendDeleteRow($event)"
                                                                        (followingRow)="openCertificationFolderSummary($event, certification)"
                                                                        [certification]="certification"
                                                                        [selectedRow]="selectedCertificationFolder"
                                                                        [externalSelectionChange]="true"
                                                                        [hidden]="!isOpened(certification)">
                                        </app-certification-folder-table>
                                    </ng-container>
                                </td>
                            </ng-container>
                        </table>
                        <app-paginator [length]="total"
                                       [pageSizeOptions]="pageSizeOptions"
                                       [scrollTopOnPageChange]="true"
                                       (page)="onPageEvent($event)">
                        </app-paginator>
                    </ng-container>
                </app-wrapper-spinner>
            </main>
        </mat-drawer-content>
        <mat-drawer #sidenav mode="side" position="end" [autoFocus]="false" [disableClose]="true">
            <app-certification-folder-side
                *ngIf="selectedCertificationFolder && selectedCertification"
                [certificationFolder]="selectedCertificationFolder"
                (certificationFolderRefreshed)="sendRefreshRow($event)"
                (certificationFolderDeleted)="sendDeleteRow($event)"
                (attendeeRefreshed)="sendRefreshAttendee($event)"
                (previous)="previous()"
                (next)="next()"
                [hasPrevious]="certificationFolderTable?.hasPrevious(selectedCertificationFolder)"
                [hasNext]="certificationFolderTable?.hasNext(selectedCertificationFolder)"
                (closeSide)="closeSide()"></app-certification-folder-side>
            <app-certification-side
                *ngIf="!selectedCertificationFolder && selectedCertification"
                [certification]="selectedCertification"
                (previous)="previous()"
                (next)="next()"
                [hasPrevious]="hasPrevious(selectedCertification)"
                [hasNext]="hasNext(selectedCertification)"
                (certificationUpdated)="onCertificationUpdated($event)"
                (closeSide)="closeSide()"></app-certification-side>
        </mat-drawer>
    </mat-drawer-container>
</div>
