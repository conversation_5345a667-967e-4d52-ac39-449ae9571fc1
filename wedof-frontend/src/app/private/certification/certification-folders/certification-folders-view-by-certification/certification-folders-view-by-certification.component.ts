import {AfterViewInit, Component, Injector, Input, OnDestroy, OnInit, ViewChild} from '@angular/core';
import {Certification, CertificationQuery} from '../../../../shared/api/models/certification';
import {AbstractCollapsableRowTableComponent} from '../../../../shared/material/table/abstract-collapsable-row-table.component';
import {CertificationService} from '../../../../shared/api/services/certification.service';
import {forkJoin, iif, Observable, of, ReplaySubject, Subject} from 'rxjs';
import {PaginatedResponse} from '../../../../shared/api/services/abstract-paginated.service';
import {takeUntil, tap} from 'rxjs/operators';
import {MatSidenav} from '@angular/material/sidenav';
import {ActivatedRoute, Router} from '@angular/router';
import {
    CertificationFolderFilters,
    CertificationFolderQuery,
    CertificationFolderTableComponent
} from '../../../../shared/certification-folder/certification-folder-table/certification-folder-table.component';
import {CertificationFolder} from '../../../../shared/api/models/certification-folder';
import {CertificationFolderService} from '../../../../shared/api/services/certification-folder.service';
import {DataResponse} from '../../../../shared/material/table/abstract-table.component';
import {CertificationFolderTableColumns} from '../../../../shared/certification-folder/certification-folder-table/certification-folder-table-params';
import {Select} from '@ngxs/store';
import {OrganismState} from '../../../../shared/api/state/organism.state';
import {Organism} from '../../../../shared/api/models/organism';
import {Attendee} from '../../../../shared/api/models/attendee';

@Component({
    selector: 'app-certifier-folders-view-by-certification',
    templateUrl: './certification-folders-view-by-certification.component.html',
    styleUrls: ['./certification-folders-view-by-certification.component.scss']
})
export class CertificationFoldersViewByCertificationComponent extends AbstractCollapsableRowTableComponent<Certification> implements OnInit, AfterViewInit, OnDestroy {
    currentOrganism: Organism = null;
    selectedCertification: Certification = null;
    selectedCertificationFolder: CertificationFolder = null;
    certificationFolderTableColumns: CertificationFolderTableColumns[];

    certificationFolderQuery$: Subject<CertificationFolderQuery> = new Subject<CertificationFolderQuery>();
    certificationFolderFilters$: ReplaySubject<CertificationFolderFilters> = new ReplaySubject<CertificationFolderFilters>();

    private getRegistrationFolder$ = new Subject<string>();

    @Input() dataQuery$ = new Subject<CertificationQuery>();
    @ViewChild('sidenav') sidenav: MatSidenav;
    @ViewChild('certificationFolderTable') certificationFolderTable: CertificationFolderTableComponent;

    @Select(OrganismState.organism) organism$: Observable<Organism>;

    constructor(
        injector: Injector,
        private _router: Router,
        private _activatedRoute: ActivatedRoute,
        private _certificationService: CertificationService,
        private _certificationFolderService: CertificationFolderService
    ) {
        super(injector);
        this.displayedColumns = ['name'];
    }

    ngOnInit(): void {
        this.organism$.pipe(takeUntil(this._unsubscribeAll)).subscribe(organism => {
            this.currentOrganism = organism;
        });

        this.followingRow.subscribe((certification: DataResponse<Certification>) => {
            this.openCertificationSummary(certification);
        });

        this.certificationFolderTableColumns = [
            CertificationFolderTableColumns.ACTIONS,
            CertificationFolderTableColumns.CANDIDATE,
            CertificationFolderTableColumns.STATE,
            CertificationFolderTableColumns.ORGANISM,
            CertificationFolderTableColumns.CERTIFICATION
        ];

        this._activatedRoute.params.pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe((params) => {
            const {certification} = params;
            const {externalId} = this._activatedRoute.snapshot.firstChild ? this._activatedRoute.snapshot.firstChild.params : {externalId: null};
            if (certification) {
                const certification$ = this.selectedCertification?.certifInfo === certification
                    ? of(this.selectedCertification)
                    : this._certificationService.get(certification);
                iif(
                    () => !externalId,
                    forkJoin([certification$, of(null)]),
                    forkJoin([
                        certification$,
                        this.selectedCertificationFolder?.externalId === externalId
                            ? of(this.selectedCertificationFolder)
                            : this._certificationFolderService.get(externalId)
                    ])
                ).subscribe(([selectedCertification, selectedCertificationFolder]: [Certification, CertificationFolder]) => {
                    this.selectedCertification = selectedCertification;
                    this.selectedCertificationFolder = selectedCertificationFolder;
                    if (this.selectedCertificationFolder && selectedCertificationFolder._links.registrationFolder) {
                        this.getRegistrationFolder$.next(selectedCertificationFolder._links.registrationFolder.externalId);
                    }
                    this.sidenav.open();
                });
            } else {
                this.selectedCertification = null;
                this.selectedCertificationFolder = null;
                if (this.sidenav?.opened) {
                    this.sidenav.close();
                }
            }
        });
    }

    ngOnDestroy(): void {
        this.certificationFolderQuery$.complete();
        this.certificationFolderFilters$.complete();
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    ngAfterViewInit(): void {
        super.ngAfterViewInit();
    }

    next(): void {
        if (this.selectedCertificationFolder) {
            this.certificationFolderQuery$.next({
                certifInfo: this.selectedCertification.certifInfo,
                certificationFolderExternalId: this.selectedCertificationFolder.externalId,
                next: true
            });
        } else {
            this.dataQuery$.next({certifInfo: this.selectedCertification.certifInfo, next: true});
        }
    }

    closeSide(): void {
        this._router.navigate(['certification', 'dossiers', 'certifications']);
    }

    previous(): void {
        if (this.selectedCertificationFolder) {
            this.certificationFolderQuery$.next({
                certifInfo: this.selectedCertification.certifInfo,
                certificationFolderExternalId: this.selectedCertificationFolder.externalId,
                previous: true
            });
        } else {
            this.dataQuery$.next({certifInfo: this.selectedCertification.certifInfo, previous: true});
        }
    }

    openCertificationFolderSummary(response: DataResponse<CertificationFolder>, certification?: Certification): void {
        this.selectedCertification = certification;
        this.selectedCertificationFolder = response.row;
        this._router.navigate(['certification', 'dossiers', 'certifications', this.selectedCertification.certifInfo, this.selectedCertificationFolder.externalId]);
    }

    openCertificationSummary(response?: DataResponse<Certification>): void {
        this.selectedCertificationFolder = null;
        this.selectedCertification = response.row;
        this._router.navigate(['certification', 'dossiers', 'certifications', this.selectedCertification.certifInfo]);
    }

    sendRefreshRow($event: CertificationFolder): void {
        this.certificationFolderTable.refreshRow($event);
    }

    sendRefreshAttendee(attendee: Attendee): void {
        this.certificationFolderTable.refreshAttendee(attendee);
    }

    sendDeleteRow($event: CertificationFolder): void {
        this.certificationFolderTable.deleteRow($event);
        this.forceRefresh();
        if (this.selectedCertificationFolder && this.selectedCertificationFolder.externalId === this.selectedCertificationFolder.externalId) {
            this.closeSide();
        }
    }

    isSideOpened(certification: Certification): boolean {
        return this.selectedCertificationFolder === null && certification === this.selectedCertification;
    }

    toggleRowIfNotOpened(certification: Certification): void {
        if (!this.isOpened(certification)) {
            this.toggleRow(certification);
        }
    }

    onCertificationUpdated(certification: Certification): void {
        this.refreshRow(certification);
        if (this.selectedCertification && this.isRowEqual(certification, this.selectedCertification)) {
            this.selectedCertification = certification;
        }
    }

    protected findRowWithDataQuery(row: Certification, query: CertificationQuery): boolean {
        return row.certifInfo === query.certifInfo;
    }

    protected getRowIdentifier(row: Certification): string | number {
        return row.certifInfo;
    }

    protected refreshData(): Observable<PaginatedResponse<Certification>> {
        return this._certificationService.list({
            ...this._filters$.value,
            organismType: 'all',
            limit: this.paginator.pageSize,
            page: this.paginator.pageIndex + 1,
            sort: 'name',
            order: 'asc',
        }).pipe(
            tap((response) => {
                this.openRow(this.selectedCertification ?? response?.payload?.[0]);
            })
        );
    }
}
