import {
    After<PERSON><PERSON>w<PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    ChangeDetector<PERSON><PERSON>,
    Component,
    OnDestroy,
    OnInit,
    ViewChild
} from '@angular/core';
import {CertificationFolderTableColumns} from '../../../../shared/certification-folder/certification-folder-table/certification-folder-table-params';
import {CertificationFolder} from '../../../../shared/api/models/certification-folder';
import {FormBuilder, FormGroup} from '@angular/forms';
import {distinct, switchMap, takeUntil} from 'rxjs/operators';
import {combineLatest, Observable, ReplaySubject, Subject} from 'rxjs';
import {
    CertificationFolderFilters,
    CertificationFolderQuery,
    CertificationFolderTableComponent
} from '../../../../shared/certification-folder/certification-folder-table/certification-folder-table.component';
import {MatSidenav} from '@angular/material/sidenav';
import {ActivatedRoute, Router} from '@angular/router';
import {CertificationFolderService} from '../../../../shared/api/services/certification-folder.service';
import {RegistrationFolder} from '../../../../shared/api/models/registration-folder';
import {CertificationFolderSideComponent} from '../../../../shared/sides/certification-folder-side/certification-folder-side.component';
import {CertificationFolderCreateSideComponent} from '../../../../shared/sides/certification-folder-create-side/certification-folder-create-side.component';
import {CanDeactivateComponent} from '../../../../shared/utils/can-deactivate/can-deactivate.component';
import {Organism} from '../../../../shared/api/models/organism';
import {FileService} from '../../../../shared/api/services/file.service';
import {MatDialog} from '@angular/material/dialog';
import {TranslateService} from '@ngx-translate/core';
import {MdePopoverTrigger} from '@material-extended/mde';
import {MatSnackBar} from '@angular/material/snack-bar';
import {SnackBarComponent} from '../../../../shared/material/snack-bar/snack-bar.component';
import {displayTextSnackBar} from '../../../../shared/utils/displayTextSnackBar';
import {OrganismState} from '../../../../shared/api/state/organism.state';
import {Select} from '@ngxs/store';
import {CertificationFolderImportDialogComponent} from '../../../../shared/certification-folder/certification-folder-import-dialog/certification-folder-import-dialog.component';
import {Subscription, SubscriptionTypes} from '../../../../shared/api/models/subscription';
import {SubscriptionState} from '../../../../shared/api/state/subscription.state';
import {DialogUpgradeSubscriptionComponent} from '../../../../shared/subscription/dialog-upgrade-subscription/dialog-upgrade-subscription.component';
import {EntityClass} from '../../../../shared/utils/enums/entity-class';
import {Location} from '@angular/common';
import {ShortcutFolderSidePanelEnum} from '../../../../shared/utils/enums/shortcut-side-panel-enum';
import {getAnchor} from '../../../../shared/utils/shortcut-side-panel-utils';
import {Attendee} from '../../../../shared/api/models/attendee';

@Component({
    selector: 'app-certification-folders-view-by-certification-folders',
    templateUrl: './certification-folders-view-by-certification-folders.component.html',
    styleUrls: ['./certification-folders-view-by-certification-folders.component.scss']
})
export class CertificationFoldersViewByCertificationFoldersComponent extends CanDeactivateComponent implements OnInit, OnDestroy, AfterViewInit, AfterViewChecked {
    folderCount = 0;
    loadingExportToFormat = false;
    currentOrganism: Organism = null;
    subscription: Subscription;
    openCertificationFolderCreateSide: boolean;
    filters: FormGroup;
    searchAdvanced: FormGroup;
    selectedRegistrationFolder: RegistrationFolder = null;
    selectedCertificationFolder: CertificationFolder = null;
    certificationFolderTableColumns: CertificationFolderTableColumns[];
    certificationFolderQuery$: Subject<CertificationFolderQuery> = new Subject<CertificationFolderQuery>();
    certificationFolderFilters$: ReplaySubject<CertificationFolderFilters> = new ReplaySubject<CertificationFolderFilters>();
    queryFilters: string;
    queryConstructor: string;
    activeFiltersCount = 0;
    showTableView = true;
    readonly EntityClass = EntityClass;

    private _unsubscribeAll = new Subject<void>();
    private getCertificationFolder$ = new Subject<number>();
    private getRegistrationFolder$ = new Subject<string>();

    @ViewChild('sidenav') sidenav: MatSidenav;
    @ViewChild('certificationFolderTable') private certificationFolderTable: CertificationFolderTableComponent;
    @ViewChild('certificationFolderSide') private certificationFolderSide: CertificationFolderSideComponent;
    @ViewChild('certificationFolderCreateSide') private certificationFolderCreateSide: CertificationFolderCreateSideComponent;
    @ViewChild(MdePopoverTrigger, {static: false}) trigger: MdePopoverTrigger;

    @Select(OrganismState.organism) organism$: Observable<Organism>;
    @Select(SubscriptionState.subscription) subscription$: Observable<Subscription>;

    constructor(
        private _router: Router,
        private _activatedRoute: ActivatedRoute,
        private _certificationFolderService: CertificationFolderService,
        private _formBuilder: FormBuilder,
        private _fileService: FileService,
        private _dialog: MatDialog,
        private _snackBar: MatSnackBar,
        private _translateService: TranslateService,
        private _changeDetector: ChangeDetectorRef,
        private _location: Location) {
        super();
    }

    ngAfterViewChecked(): void {
        this._changeDetector.detectChanges();
    }

    closePopover(): void {
        this.trigger.togglePopover();
    }

    exportToFormat(format: 'csv' | 'xlsx'): void {
        this.loadingExportToFormat = true;
        const queryParams: { [param: string]: string } = {
            format: format,
            ...this.certificationFolderTable.activeFilters
        };
        if (queryParams.siret == null) {
            queryParams.siret = 'all';
        }
        if (this.queryFilters) {
            queryParams.query = this.queryFilters;
        }
        this._fileService.download('/api/' + 'certificationFolders', 'dossiersCertification', null, queryParams).subscribe(
            (blob) => {
                if (blob.state === 'PENDING') {
                    this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(
                        this._translateService.instant('common.actions.export.exportStarted'), 0));
                } else if (blob.state === 'DONE') {
                    this.loadingExportToFormat = false;
                    this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(
                        this._translateService.instant('common.actions.export.exportFinished')));
                }
            }
        );
    }

    openImportDialog(): void {
        this._dialog.open(CertificationFolderImportDialogComponent, {
            panelClass: 'full-page-scroll-40',
            height: 'auto'
        });
    }

    openDialogSubscription(): void {
        this._dialog.open(DialogUpgradeSubscriptionComponent, {
            panelClass: ['full-page-scroll-50'],
            data: {
                organism: this.currentOrganism,
                subscription: this.subscription,
                fromPage: 'excelImportExport',
                subscriptionTypeToShow: SubscriptionTypes.CERTIFIER
            }
        });
    }

    ngOnInit(): void {
        combineLatest([
            this.organism$,
            this.subscription$,
        ]).pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe(([organism, subscription]: [Organism, Subscription]) => {
            this.currentOrganism = organism;
            this.subscription = subscription;
        });
        const {externalId} = this._activatedRoute.snapshot.params;
        this.openCertificationFolderCreateSide = externalId === 'creer';
        this._unsubscribeAll = new Subject();
        this.filters = this._formBuilder.group({
            query: [undefined]
        });
        this.searchAdvanced = this._formBuilder.group({
            certifInfo: [undefined],
            siret: [undefined],
            state: [undefined],
            period: [undefined],
            until: [undefined],
            since: [undefined],
            filterOnStateDate: [undefined],
            query: [undefined],
            registrationFolderState: [undefined],
            registrationFolderType: [undefined],
            registrationFolderCompletionRate: [undefined],
            cdcState: [undefined],
            cdcCompliant: [undefined],
            cdcToExport: [undefined],
            cdcExcluded: [undefined],
            cdcFile: [undefined],
            messageTemplate: [undefined],
            messageState: [undefined],
            survey: [undefined],
            skillSets: [undefined],
            tags: [undefined]
        });
        this.certificationFolderTableColumns = [
            CertificationFolderTableColumns.ACTIONS,
            CertificationFolderTableColumns.CANDIDATE,
            CertificationFolderTableColumns.STATE,
            CertificationFolderTableColumns.ORGANISM,
            CertificationFolderTableColumns.CERTIFICATION,
            CertificationFolderTableColumns.TAGS
        ];

        this.filters.valueChanges.pipe(
            distinct(),
            takeUntil(this._unsubscribeAll)
        ).subscribe((value: CertificationFolderFilters) => {
            if (this.filters.valid) {
                this.certificationFolderFilters$.next({
                    query: value?.query
                });
                this.searchAdvanced.get('query').setValue(value.query);
                this.queryFilters = value?.query;
            }
        });

        this.searchAdvanced.valueChanges.pipe(
            distinct(),
            takeUntil(this._unsubscribeAll)
        ).subscribe((certificationFoldersFilters: CertificationFolderFilters) => {
            if (certificationFoldersFilters['query']) {
                this.queryConstructor = certificationFoldersFilters['query'];
            }
            this.activeFiltersCount = Object.keys(certificationFoldersFilters).filter(key => {
                if (key === 'filterOnStateDate' && certificationFoldersFilters[key] === 'stateLastUpdate') {
                    return false;
                } else if (Array.isArray(certificationFoldersFilters[key])) {
                    return certificationFoldersFilters[key].length > 0;
                } else {
                    return certificationFoldersFilters[key];
                }
            }).length;
        });

        this.getCertificationFolder$
            .pipe(
                takeUntil(this._unsubscribeAll),
                switchMap((certificationFolderExternalId) => this._certificationFolderService.get(certificationFolderExternalId.toString())),
            ).subscribe(selectedCertificationFolder => {
            if (!this.certificationFolderTable.selectedRow) {
                this.certificationFolderTable.selectedRow = selectedCertificationFolder;
            }
            this.selectedCertificationFolder = selectedCertificationFolder;
            if (selectedCertificationFolder._links.registrationFolder) {
                this.getRegistrationFolder$.next(selectedCertificationFolder._links.registrationFolder.externalId);
            }
            this.sidenav.open();
        });
    }

    applyNewFilter(certificationFolderFilters: CertificationFolderFilters): void {
        const tag = certificationFolderFilters.tags;
        this.searchAdvanced.get('tags').setValue(tag);
        this._router.navigate([], {queryParams: {tags: tag}});
    }

    ngAfterViewInit(): void {
        this._activatedRoute.queryParams.pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe((queryParams) => {
            const certificationFoldersFilters: CertificationFolderFilters = {};
            const availableFilters = [
                {key: 'query'},
                {key: 'siret', type: 'list'},
                {key: 'certifInfo', type: 'list'},
                {key: 'cdcCompliant'},
                {key: 'cdcToExport'},
                {key: 'cdcExcluded'},
                {key: 'cdcFile'},
                {key: 'filterOnStateDate', default: 'stateLastUpdate'},
                {key: 'period'},
                {key: 'since', type: 'date'},
                {key: 'until', type: 'date'},
                {key: 'state', type: 'list'},
                {key: 'cdcState', type: 'list'},
                {key: 'registrationFolderState', type: 'list'},
                {key: 'registrationFolderType', type: 'list'},
                {key: 'registrationFolderCompletionRate'},
                {key: 'messageTemplate', type: 'list'},
                {key: 'messageState', type: 'list'},
                {key: 'survey', type: 'list'},
                {key: 'skillSets', type: 'list'},
                {key: 'tags', default: [], type: 'list'}
            ];
            availableFilters.forEach((availableFilter) => {
                const key = availableFilter.key;
                const stringValue = queryParams[key] ?? (availableFilter.default ?? null); // force null if it was undefined
                certificationFoldersFilters[key] = stringValue;
                let typedValue: any;
                if (stringValue && availableFilter.type === 'list' && (key !== 'tags' || stringValue.length)) {
                    typedValue = stringValue.split(',');
                } else {
                    typedValue = stringValue;
                }
                this.searchAdvanced.get(key).setValue(typedValue);
            });
            this.certificationFolderFilters$.next(certificationFoldersFilters);
        });

        this._activatedRoute.params.pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe((params) => {
            const {externalId} = params;
            if (externalId && externalId !== 'creer') {
                this.openCertificationFolderCreateSide = false;
                if (this.selectedCertificationFolder?.externalId !== externalId) {
                    this.getCertificationFolder$.next(externalId);
                } else {
                    this.sidenav.open();
                }
            } else if (externalId === 'creer') {
                this.certificationFolderTable.clearSelectedRow();
                this.selectedCertificationFolder = null;
                this.openCertificationFolderCreateSide = true;
                setTimeout(() => {
                    this.sidenav.open();
                });
            } else {
                this.certificationFolderTable.clearSelectedRow();
                this.openCertificationFolderCreateSide = false;
                this.selectedRegistrationFolder = null;
                if (this.sidenav?.opened) {
                    this.sidenav.close();
                }
            }
        });
    }

    ngOnDestroy(): void {
        this.certificationFolderQuery$.complete();
        this.certificationFolderFilters$.complete();
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    next(): void {
        this.certificationFolderQuery$.next({
            certificationFolderExternalId: this.selectedCertificationFolder.externalId,
            certifInfo: null,
            next: true,
            keepShortcutSide: true
        });
    }

    previous(): void {
        this.certificationFolderQuery$.next({
            certificationFolderExternalId: this.selectedCertificationFolder.externalId,
            certifInfo: null,
            previous: true,
            keepShortcutSide: true
        });
    }

    clearFilter(): void {
        this.filters.reset();
        this.searchAdvanced.reset();
        this._router.navigate(['certification', 'dossiers', 'liste']);
    }

    canDeactivate(): boolean {
        if (this.certificationFolderCreateSide && this.openCertificationFolderCreateSide) {
            return this.certificationFolderCreateSide.canDeactivate();
        } else if (this.certificationFolderSide && this.selectedCertificationFolder) {
            return this.certificationFolderSide.canDeactivate();
        } else {
            return true;
        }
    }

    openSummary(certificationFolder: CertificationFolder, keepShortcutSide: boolean = false): void {
        const anchor: string = getAnchor(this._location.path());
        const navigateCommands = ['certification', 'dossiers', 'liste', certificationFolder.externalId];
        if (Object.values(ShortcutFolderSidePanelEnum).some((shortcut: ShortcutFolderSidePanelEnum): boolean => shortcut === anchor) && keepShortcutSide) {
            navigateCommands.push(anchor);
        }
        this._router.navigate(navigateCommands, {
            queryParamsHandling: 'preserve'
        }).then((success) => {
            if (success) {
                this.selectedCertificationFolder = certificationFolder;
            }
        });
    }

    closeSide(): void {
        this._router.navigate(['certification', 'dossiers', 'liste'], {queryParamsHandling: 'preserve'}).then((success) => {
            if (success) {
                this.selectedCertificationFolder = null;
            }
        });
    }

    sendRefreshRow($event: CertificationFolder): void {
        this.certificationFolderTable.refreshRow($event);
    }

    sendRefreshAttendee(attendee: Attendee): void {
        this.certificationFolderTable.refreshAttendee(attendee);
    }

    sendDeleteRow($event: CertificationFolder): void {
        this.certificationFolderTable.deleteRow($event);
        this.folderCount -= 1;
        this.certificationFolderTable.forceRefresh();
        if (this.selectedCertificationFolder && this.selectedCertificationFolder.externalId === this.selectedCertificationFolder.externalId) {
            this.closeSide();
        }
    }

    sendInsertRow($newRow: CertificationFolder): void {
        this.certificationFolderTable.insertRow($newRow);
        this.folderCount += 1;
    }

    openCreate(): void {
        this._router.navigate(['certification', 'dossiers', 'liste', 'creer']).then((success) => {
            if (success) {
                this.selectedCertificationFolder = null;
            }
        });
    }

    setShowKanbanView(): void {
        this.showTableView = false;
        this.selectedRegistrationFolder = null;
        this.openCertificationFolderCreateSide = false;
        if (this.sidenav?.opened) {
            this.sidenav.close();
        }
        this._router.navigate(['/', 'certification', 'dossiers', 'kanban'], {
            queryParams: this._activatedRoute.snapshot.queryParams
        });
    }
}
