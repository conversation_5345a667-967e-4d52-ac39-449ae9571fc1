<div class="min-w-full content-layout fullwidth-standard-inner-scroll" id="main-content-layout">
    <app-registration-folder-connection-check *ngIf="currentOrganism?.isTrainingOrganism">
    </app-registration-folder-connection-check>
    <mat-drawer-container>
        <mat-drawer-content>
            <header class="flex flex-row justify-between pt-4 px-4 bg-white">
                <div class="flex flex-col flex-grow">
                    <div class="flex flex-row">
                        <h1>{{ 'private.certification.folders.title' | translate }}</h1>
                        <a class="flex flex-row pl-4 self-center" routerLink="/certification/dossiers/certifications">
                            <p class="text-center">{{ 'private.certification.folders.certificationView' | translate }}</p>
                            <mat-icon class="pl-1 self-center" matPrefix svgIcon="sync_alt"></mat-icon>
                        </a>
                    </div>
                    <div>
                        <p class="subtitle">{{ 'private.certification.folders.subtitle' | translate }}</p>
                        <app-tag class="font-semibold flex-auto mt-2 mb-2" color="#f2e5f2">
                            {{ 'private.certification.folders.subtitleCount' | translate : {folderCount: folderCount} }}
                        </app-tag>
                        <div class="mt-2 flex flex-row flex-grow items-center">
                            <div class="flex flex-row items-center">
                                <button
                                    [matTooltip]=" 'private.training-organism.folders.toolTip.kanbanView' | translate "
                                    [matTooltipPosition]="'above'"
                                    [matTooltipShowDelay]="500"
                                    (click)="setShowKanbanView()" mat-icon-button
                                    *ngIf="showTableView"
                                    [color]="'accent'">
                                    <mat-icon>view_kanban</mat-icon>
                                </button>
                                <mat-icon color="primary" svgIcon="table_rows"></mat-icon>
                            </div>
                            <app-entity-filters [entityClass]="EntityClass.CERTIFICATION_FOLDER">
                            </app-entity-filters>
                        </div>
                    </div>
                </div>
                <div class="py-4 pl-4 flex flex-row flex-nowrap items-center" [formGroup]="filters">
                    <div class="mr-8">
                        <mat-form-field class="w-90">
                            <input matInput formControlName="query" [value]="queryConstructor"
                                   [title]="'private.training-organism.folders.searchbar' | translate"
                                   [placeholder]="'common.table.filters.globalSearch' | translate" maxlength="50">
                            <mat-icon matPrefix svgIcon="search"></mat-icon>
                            <button mat-button *ngIf="activeFiltersCount" matSuffix mat-icon-button
                                    aria-label="Clear" (click)="clearFilter()">
                                <mat-icon svgIcon="close"></mat-icon>
                            </button>
                            <div>
                                <button [mdePopoverTriggerFor]="searchPopover" mdePopoverTriggerOn="click" class="flex">
                                    <mat-icon
                                        [matBadge]="activeFiltersCount ? activeFiltersCount+'' : null"
                                        matBadgePosition="below after"
                                        matBadgeSize="small"
                                        [color]="activeFiltersCount ? 'primary' : undefined"
                                        [svgIcon]="activeFiltersCount ? 'filter_alt' : 'manage_search'"></mat-icon>
                                </button>
                                <mde-popover #searchPopover="mdePopover"
                                             [mdePopoverOverlapTrigger]="false"
                                             mdePopoverOffsetX="17"
                                             mdePopoverOffsetY="10"
                                             mdePopoverArrowWidth="0"
                                             mdePopoverPositionX="before"
                                             mdePopoverCloseOnClick="false"
                                             mdePopoverArrowColor="#FFF">
                                    <app-certification-folder-advanced-search [searchAdvanced]="searchAdvanced"
                                                                              (closePopOver)="closePopover()"
                                    ></app-certification-folder-advanced-search>
                                </mde-popover>
                            </div>
                        </mat-form-field>
                        <div class="flex items-center justify-end">
                            <div class="flex flex-row"
                                 [matTooltip]=" 'private.certification.folders.createFolder.create' | translate "
                                 [matTooltipPosition]="'above'"
                                 [matTooltipShowDelay]="500">
                                <button type="button" color="primary"
                                        mat-flat-button (click)="openCreate()"
                                        [disabled]="openCertificationFolderCreateSide">
                                    {{ 'private.certification.folders.createFolder.create' | translate }}</button>
                            </div>
                            <div class="flex align-center">
                                <button type="button"
                                        color="primary"
                                        class="button-actions ml-2"
                                        mat-flat-button
                                        [disabled]="loadingExportToFormat"
                                        (click)="subscription?.allowCertifierPlus ? exportToFormat('xlsx') : openDialogSubscription()">{{ 'common.actions.export.exportExcelButton' | translate }}
                                </button>
                                <button class="flex justify-center items-center button-arrow button-arrow-primary"
                                        color="primary"
                                        mat-flat-button
                                        [matMenuTriggerFor]="exportOtherFormats"
                                        [disabled]="loadingExportToFormat"
                                        title="Autres formats"
                                        type="button">
                                    <mat-icon class="icon" svgIcon="arrow_drop_down"></mat-icon>
                                </button>
                                <mat-menu #exportOtherFormats="matMenu" xPosition="before">
                                    <ng-template matMenuContent>
                                        <button type="button"
                                                mat-menu-item
                                                [disabled]="loadingExportToFormat"
                                                (click)="exportToFormat('csv')">{{ 'common.actions.export.exportCsvButton' | translate }}
                                        </button>
                                        <button type="button"
                                                *ngIf="subscription?.allowCertifiers"
                                                mat-menu-item
                                                [disabled]="loadingExportToFormat"
                                                (click)="subscription?.allowCertifierPlus ? openImportDialog() : openDialogSubscription()">{{ 'common.actions.export.importExcelButton' | translate }}
                                        </button>
                                    </ng-template>
                                </mat-menu>
                            </div>
                        </div>
                    </div>
                </div>
            </header>
            <main>
                <app-certification-folder-table #certificationFolderTable
                                                [dataQuery$]="certificationFolderQuery$"
                                                [certificationFolderFilters$]="certificationFolderFilters$"
                                                (followingRow)="openSummary($event.row, $event.keepShortcutSide)"
                                                [selectedRow]="selectedCertificationFolder"
                                                [externalSelectionChange]="true"
                                                (totalRows)="folderCount = $event"
                                                (certificationFolderDeleted)="sendDeleteRow($event)"
                                                [displayedColumns]="certificationFolderTableColumns"
                                                (searchTag)="applyNewFilter({tags: $event})">
                </app-certification-folder-table>
            </main>
        </mat-drawer-content>
        <mat-drawer #sidenav mode="side" position="end" [autoFocus]="false" [disableClose]="true">
            <app-certification-folder-side
                #certificationFolderSide
                *ngIf="selectedCertificationFolder"
                [certificationFolder]="selectedCertificationFolder"
                (certificationFolderDeleted)="sendDeleteRow($event)"
                (certificationFolderRefreshed)="sendRefreshRow($event)"
                (previous)="previous()"
                (attendeeRefreshed)="sendRefreshAttendee($event)"
                (next)="next()"
                [hasPrevious]="certificationFolderTable?.hasPrevious(selectedCertificationFolder)"
                [hasNext]="certificationFolderTable?.hasNext(selectedCertificationFolder)"
                (closeSide)="closeSide()"></app-certification-folder-side>
            <app-certification-folder-create-side
                #certificationFolderCreateSide
                *ngIf="openCertificationFolderCreateSide"
                (closeSide)="closeSide()"
                (certificationFolderCreated)="sendInsertRow($event)">
            </app-certification-folder-create-side>
        </mat-drawer>
    </mat-drawer-container>
</div>
