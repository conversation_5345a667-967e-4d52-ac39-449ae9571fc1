import {
    AfterViewChecked,
    After<PERSON>iew<PERSON>nit,
    ChangeDetectorRef,
    Component,
    OnDestroy,
    OnInit,
    ViewChild
} from '@angular/core';
import {CanDeactivateComponent} from '../../../../shared/utils/can-deactivate/can-deactivate.component';
import {ActivatedRoute, Router} from '@angular/router';
import {FormBuilder, FormGroup} from '@angular/forms';
import {Organism} from '../../../../shared/api/models/organism';
import {Subscription, SubscriptionTypes} from '../../../../shared/api/models/subscription';
import {BehaviorSubject, combineLatest, Observable, Subject} from 'rxjs';
import {
    KanbanAdditionalDataResponse,
    KanbanBoardComponent,
    KanbanColumnConfigsResponse,
    KanbanColumnResponse
} from '../../../../shared/kanban-board/kanban-board/kanban-board.component';
import {PaginatedResponse} from '../../../../shared/api/services/abstract-paginated.service';
import {CertificationFolder} from '../../../../shared/api/models/certification-folder';
import {CertificationFolderFilters} from '../../../../shared/certification-folder/certification-folder-table/certification-folder-table.component';
import {
    CertificationFolderHttpParams,
    CertificationFolderService
} from '../../../../shared/api/services/certification-folder.service';
import {MatSidenav} from '@angular/material/sidenav';
import {MdePopoverTrigger} from '@material-extended/mde';
import {CertificationFolderSideComponent} from '../../../../shared/sides/certification-folder-side/certification-folder-side.component';
import {CertificationFolderCreateSideComponent} from '../../../../shared/sides/certification-folder-create-side/certification-folder-create-side.component';
import {SnackBarComponent} from '../../../../shared/material/snack-bar/snack-bar.component';
import {displayTextSnackBar} from '../../../../shared/utils/displayTextSnackBar';
import {FileService} from '../../../../shared/api/services/file.service';
import {MatSnackBar} from '@angular/material/snack-bar';
import {TranslateService} from '@ngx-translate/core';
import {distinct, map, switchMap, takeUntil} from 'rxjs/operators';
import {CertificationService} from '../../../../shared/api/services/certification.service';
import {Select} from '@ngxs/store';
import {SubscriptionState} from '../../../../shared/api/state/subscription.state';
import {OrganismState} from '../../../../shared/api/state/organism.state';
import {CertificationFolderImportDialogComponent} from '../../../../shared/certification-folder/certification-folder-import-dialog/certification-folder-import-dialog.component';
import {MatDialog} from '@angular/material/dialog';
import {DialogUpgradeSubscriptionComponent} from '../../../../shared/subscription/dialog-upgrade-subscription/dialog-upgrade-subscription.component';
import {EntityClass} from '../../../../shared/utils/enums/entity-class';
import {Location} from '@angular/common';
import {ShortcutFolderSidePanelEnum} from '../../../../shared/utils/enums/shortcut-side-panel-enum';
import {getAnchor} from '../../../../shared/utils/shortcut-side-panel-utils';
import {Attendee} from '../../../../shared/api/models/attendee';
import {MatCheckboxChange} from '@angular/material/checkbox';
import {EnvironmentService} from '../../../../shared/environment/environment.service';

@Component({
    selector: 'app-kanban-certification-folders',
    templateUrl: './kanban-certification-folders.component.html',
    styleUrls: ['./kanban-certification-folders.component.scss']
})
export class KanbanCertificationFoldersComponent extends CanDeactivateComponent implements OnInit, AfterViewInit, OnDestroy, AfterViewChecked {

    constructor(private _router: Router,
                private _activatedRoute: ActivatedRoute,
                private _certificationFolderService: CertificationFolderService,
                private _certificationService: CertificationService,
                private _formBuilder: FormBuilder,
                private _fileService: FileService,
                private _snackBar: MatSnackBar,
                private _dialog: MatDialog,
                private _translateService: TranslateService,
                private _changeDetector: ChangeDetectorRef,
                public environmentService: EnvironmentService,
                private _location: Location) {
        super();
        this._unsubscribeAll = new Subject();
    }

    filters$: { query?: string, [param: string]: string | string[] | boolean } = {};
    filters: FormGroup;
    loadingExportToFormat = false;
    showKanbanView = true;
    activeFiltersCount = 0;
    queryConstructor: string;
    searchAdvanced: FormGroup;
    currentOrganism: Organism = null;
    subscription: Subscription;
    selectedCertificationFolder: CertificationFolder = null;
    openCertificationFolderCreateSide: boolean;
    certificationFolderFilters$: BehaviorSubject<CertificationFolderFilters> = new BehaviorSubject({});
    queryFilters: string;
    hasAllAmountHtInit = true;
    loading = true;
    readonly EntityClass = EntityClass;
    folderCount = 0;

    @Select(SubscriptionState.subscription) subscription$: Observable<Subscription>;
    @Select(OrganismState.organism) organism$: Observable<Organism>;

    @ViewChild('sidenav') sidenav: MatSidenav;
    @ViewChild(KanbanBoardComponent) private kanbanBoardComponent: KanbanBoardComponent<CertificationFolder>;
    @ViewChild('certificationFolderSide') private certificationFolderSide: CertificationFolderSideComponent;
    @ViewChild('certificationFolderCreateSide') private certificationFolderCreateSide: CertificationFolderCreateSideComponent;
    @ViewChild(MdePopoverTrigger, {static: false}) trigger: MdePopoverTrigger;

    private _unsubscribeAll: Subject<void> = new Subject();
    private getCertificationFolder$: Subject<number> = new Subject<number>();

    loadingMenu = false;
    selectedCards: CertificationFolder[] = [];
    columnSelected: string;
    allChecked = false;
    columnConfigs: KanbanColumnConfigsResponse;
    isTotalCardsSelected = false;
    columnSelectedFolderCount = 0;
    canSelectCardsManually = true;

    getActiveFilters(filters: { query?: string, [param: string]: string | string[] | boolean }): { query?: string, [param: string]: string | string[] | boolean } {
        return this.filters$ = filters;
    }

    listColumnConfigs: () => Observable<KanbanColumnConfigsResponse> = () => this._certificationFolderService.listColumnConfigs();
    listAllItemsByColumn: (columnIds: string[], params: CertificationFolderHttpParams) => Observable<KanbanColumnResponse<CertificationFolder>> =
        (columnIds: string[], params: CertificationFolderHttpParams) => {
            if (params.siret == null) {
                params.siret = 'all'; // Hack
            }
            return this._certificationFolderService.listByColumn(columnIds, params);
        }
    listItemsForColumn: (params: CertificationFolderHttpParams) => Observable<PaginatedResponse<CertificationFolder>> =
        (params: CertificationFolderHttpParams) => {
            if (params.siret == null) {
                params.siret = 'all'; // Hack
            }
            return this._certificationFolderService.list(params);
        }
    getRevenueForColumn: (state: string, params: CertificationFolderHttpParams) => Observable<KanbanAdditionalDataResponse> =
        (state: string, params: CertificationFolderHttpParams) => {
            return this._certificationFolderService.revenueByColumn(state, params).pipe(
                map(newValue => ({columnId: state, fieldName: 'revenue', newValue}))
            );
        }

    ngAfterViewChecked(): void {
        this._changeDetector.detectChanges();
    }

    ngOnInit(): void {
        combineLatest([
            this.organism$,
            this.subscription$,
        ]).pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe(([organism, subscription]: [Organism, Subscription]) => {
            this.currentOrganism = organism;
            this.subscription = subscription;
        });
        const {externalId} = this._activatedRoute.snapshot.params;
        this.openCertificationFolderCreateSide = externalId === 'creer';
        this.listColumnConfigs().subscribe((columnConfigResponse) => {
            this.columnConfigs = columnConfigResponse;
        });
        if (this.currentOrganism.isCertifierOrganism) {
            this._certificationService.listLite({
                organismType: 'certifier',
                siret: this.currentOrganism.siret
            }).subscribe((certifications) => {
                certifications.payload.forEach((certification) => {
                    if (!certification.amountHt) {
                        this.hasAllAmountHtInit = false;
                        return;
                    }
                });
                this.loading = false;
            });
        }
        this.filters = this._formBuilder.group({
            query: [undefined]
        });
        this.searchAdvanced = this._formBuilder.group({
            certifInfo: [undefined],
            siret: [undefined],
            state: [undefined],
            period: [undefined],
            until: [undefined],
            since: [undefined],
            filterOnStateDate: [undefined],
            query: [undefined],
            registrationFolderState: [undefined],
            registrationFolderType: [undefined],
            registrationFolderCompletionRate: [undefined],
            cdcState: [undefined],
            cdcCompliant: [undefined],
            cdcToExport: [undefined],
            cdcExcluded: [undefined],
            cdcFile: [undefined],
            messageTemplate: [undefined],
            messageState: [undefined],
            survey: [undefined],
            skillSets: [undefined],
            tags: [undefined]
        });
        this.filters.valueChanges.pipe(
            distinct(),
            takeUntil(this._unsubscribeAll)
        ).subscribe((value: { query: string }) => {
            if (this.filters.valid) {
                this.searchAdvanced.get('query').setValue(value.query);
                this.certificationFolderFilters$.next(this.searchAdvanced.getRawValue());
                this.queryFilters = value?.query;
            }
        });

        this.searchAdvanced.valueChanges.pipe(
            distinct(),
            takeUntil(this._unsubscribeAll)
        ).subscribe((certificationFoldersFilters: CertificationFolderFilters) => {
            if (certificationFoldersFilters['query']) {
                this.queryConstructor = certificationFoldersFilters['query'];
            }
            this.activeFiltersCount = Object.keys(certificationFoldersFilters).filter(key => {
                if (key === 'filterOnStateDate' && certificationFoldersFilters[key] === 'stateLastUpdate') {
                    return false;
                } else if (key === 'siret' && certificationFoldersFilters[key] === 'all') {
                    return false;
                } else if (Array.isArray(certificationFoldersFilters[key])) {
                    return certificationFoldersFilters[key].length > 0;
                } else {
                    return certificationFoldersFilters[key];
                }
            }).length;
        });

        this.getCertificationFolder$
            .pipe(
                takeUntil(this._unsubscribeAll),
                switchMap((certificationFolderExternalId) => this._certificationFolderService.get(certificationFolderExternalId.toString())),
            ).subscribe(selectedCertificationFolder => {
            this.selectedCertificationFolder = selectedCertificationFolder;
            this.sidenav.open();
        });

    }

    canDeactivate(): boolean {
        if (this.certificationFolderCreateSide && this.openCertificationFolderCreateSide) {
            return this.certificationFolderCreateSide.canDeactivate();
        } else if (this.certificationFolderSide && this.selectedCertificationFolder) {
            return this.certificationFolderSide.canDeactivate();
        } else {
            return true;
        }
    }

    setShowTableView(): void {
        this.showKanbanView = false;
        this.selectedCertificationFolder = null;
        if (this.sidenav?.opened) {
            this.sidenav.close();
        }
        this._router.navigate(['/', 'certification', 'dossiers', 'liste'], {
            queryParams: this._activatedRoute.snapshot.queryParams
        });
    }

    exportToFormat(format: 'csv' | 'xlsx'): void {
        this.loadingExportToFormat = true;
        const queryParams = Object.assign({}, this._activatedRoute.snapshot.queryParams);
        queryParams['format'] = format;
        if (queryParams.siret == null) {
            queryParams.siret = 'all';
        }
        if (this.queryFilters) {
            queryParams.query = this.queryFilters;
        }
        this._fileService.download('/api/' + 'certificationFolders', 'dossiersCertification', null, queryParams).subscribe(
            (blob) => {
                if (blob.state === 'PENDING') {
                    this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(
                        this._translateService.instant('common.actions.export.exportStarted'), 0));
                } else if (blob.state === 'DONE') {
                    this.loadingExportToFormat = false;
                    this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(
                        this._translateService.instant('common.actions.export.exportFinished')));
                }
            }
        );
    }

    openImportDialog(): void {
        this._dialog.open(CertificationFolderImportDialogComponent, {
            panelClass: 'full-page-scroll-40',
            height: 'auto'
        });
    }

    openDialogSubscription(): void {
        this._dialog.open(DialogUpgradeSubscriptionComponent, {
            panelClass: ['full-page-scroll-50'],
            data: {
                organism: this.currentOrganism,
                subscription: this.subscription,
                fromPage: 'excelImportExport',
                subscriptionTypeToShow: SubscriptionTypes.CERTIFIER
            }
        });
    }

    closePopover(): void {
        this.trigger.togglePopover();
    }

    openCreate(): void {
        this._router.navigate(['..', 'creer'], {relativeTo: this._activatedRoute}).then((success) => {
            if (success) {
                this.selectedCertificationFolder = null;
            }
        });
    }

    sendRefreshCard(certificationFolder: CertificationFolder, openSide = true): void {
        this.kanbanBoardComponent.refreshCard(certificationFolder, openSide);
    }

    sendRefreshAttendee(attendee: Attendee): void {
        Object.values(this.kanbanBoardComponent.columnDataByColumnId).forEach((column) => {
            column.items.forEach(certificationFolder => {
                if (certificationFolder.attendee.id === attendee.id) {
                    certificationFolder.attendee = attendee;
                    this.sendRefreshCard(certificationFolder);
                }
            });
        });
    }

    sendDeleteCard(certificationFolder: CertificationFolder): void {
        this.kanbanBoardComponent.removeCard(certificationFolder);
        this.folderCount -= 1;
        if (this.selectedCertificationFolder && this.selectedCertificationFolder.externalId === certificationFolder.externalId) {
            this.closeSide();
        }
    }

    hasPrevious(): boolean {
        return this.kanbanBoardComponent.hasPrevious(this.selectedCertificationFolder);
    }

    hasNext(): boolean {
        return this.kanbanBoardComponent.hasNext(this.selectedCertificationFolder);
    }

    selectPreviousCard(): void {
        this.kanbanBoardComponent.selectPreviousCard(this.selectedCertificationFolder);
    }

    selectNextCard(): void {
        this.kanbanBoardComponent.selectNextCard(this.selectedCertificationFolder);
    }

    sendInsertCard(certificationFolder: CertificationFolder): void {
        this.kanbanBoardComponent.insertCard(certificationFolder);
        this.folderCount += 1;
        this.selectedCertificationFolder = certificationFolder;
    }

    ngAfterViewInit(): void {
        this._activatedRoute.queryParams.pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe((queryParams) => {
            const certificationFoldersFilters: CertificationFolderFilters = {};
            const availableFilters = [
                {key: 'query'},
                {key: 'siret', type: 'list'},
                {key: 'certifInfo', type: 'list'},
                {key: 'cdcCompliant'},
                {key: 'cdcToExport'},
                {key: 'cdcExcluded'},
                {key: 'cdcFile'},
                {key: 'filterOnStateDate', default: 'stateLastUpdate'},
                {key: 'period'},
                {key: 'since', type: 'date'},
                {key: 'until', type: 'date'},
                {key: 'state', type: 'list'},
                {key: 'cdcState', type: 'list'},
                {key: 'registrationFolderState', type: 'list'},
                {key: 'registrationFolderType', type: 'list'},
                {key: 'registrationFolderCompletionRate'},
                {key: 'messageTemplate', type: 'list'},
                {key: 'messageState', type: 'list'},
                {key: 'survey', type: 'list'},
                {key: 'skillSets', type: 'list'},
                {key: 'tags', default: [], type: 'list'}
            ];
            availableFilters.forEach((availableFilter) => {
                const key = availableFilter.key;
                const stringValue = queryParams[key] ?? (availableFilter.default ?? null); // force null if it was undefined
                certificationFoldersFilters[key] = stringValue;
                let typedValue: any;
                if (stringValue && availableFilter.type === 'list' && (key !== 'tags' || stringValue.length)) {
                    typedValue = stringValue.split(',');
                } else {
                    typedValue = stringValue;
                }
                this.searchAdvanced.get(key).setValue(typedValue);
            });
            this.certificationFolderFilters$.next(certificationFoldersFilters);
        });

        this._activatedRoute.params.pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe((params) => {
            const {externalId} = params;
            if (externalId === 'creer') {
                this.selectedCertificationFolder = null;
                this.openCertificationFolderCreateSide = true;
                setTimeout(() => {
                    this.sidenav.open();
                });
            } else if (externalId) {
                this.openCertificationFolderCreateSide = false;
                if (this.selectedCertificationFolder?.externalId !== externalId) {
                    this.getCertificationFolder$.next(externalId);
                } else {
                    this.sidenav.open();
                }
            } else {
                this.openCertificationFolderCreateSide = false;
                if (this.sidenav?.opened) {
                    this.sidenav.close();
                }
            }
        });
    }

    applyNewFilter(certificationFolderFilters: CertificationFolderFilters): void {
        const tag = certificationFolderFilters.tags;
        this.searchAdvanced.get('tags').setValue(tag);
        this._router.navigate([], {queryParams: {tags: tag}});
    }

    clearSearch(): void {
        this.filters.reset();
        this.searchAdvanced.reset();
        this._router.navigate(['/', 'certification', 'dossiers', 'kanban']);
    }

    openSummary(certificationFolder: CertificationFolder, keepShortcutSide: boolean = false): void {
        const anchor: string = getAnchor(this._location.path());
        const navigateCommands = ['..', certificationFolder.externalId];
        if (Object.values(ShortcutFolderSidePanelEnum).some((shortcut: ShortcutFolderSidePanelEnum): boolean => shortcut === anchor) && keepShortcutSide) {
            navigateCommands.push(anchor);
        }
        this._router.navigate(navigateCommands, {
            relativeTo: this._activatedRoute,
            queryParamsHandling: 'preserve'
        }).then((success) => {
            // success is true if new navigation, null if it stays on the same, false if failed
            // Here we need to get the freshest hasNext / hasPrevious and CF even on refresh (= stay on same)
            if (success === true || success === null) {
                this.selectedCertificationFolder = certificationFolder;
            }
        });
    }

    closeSide(): void {
        this._router.navigate(['..'], {
            relativeTo: this._activatedRoute,
            queryParamsHandling: 'preserve'
        }).then((success) => {
            if (success) {
                this.selectedCertificationFolder = null;
            }
        });
    }

    ngOnDestroy(): void {
        this.certificationFolderFilters$.complete();
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    clearSelection(): void {
        this.selectedCards = [];
        this.columnSelected = null;
        this.allChecked = false;
        this.columnSelectedFolderCount = 0;
        this.isTotalCardsSelected = false;
        this.canSelectCardsManually = true;
    }

    checkedColumn(kanbanColumnData: null | { columnId: string; items: CertificationFolder[]; total: number }): void {
        this.isTotalCardsSelected = false;
        if (kanbanColumnData) {
            this.columnSelected = kanbanColumnData.columnId;
            this.filters$['state'] = this.columnSelected;
            this.columnSelectedFolderCount = kanbanColumnData.total;
            this.selectedCards.push(...kanbanColumnData.items);
            this.canSelectCardsManually = false;
        } else {
            this.columnSelected = null;
            this.selectedCards = [];
            this.columnSelectedFolderCount = 0;
            this.canSelectCardsManually = true;
        }
    }

    checkedAll(event: MatCheckboxChange): void {
        this.columnSelected = null;
        this.columnSelectedFolderCount = 0;
        if (event.checked === true) {
            this.allChecked = true;
            this.isTotalCardsSelected = true;
            Object.values(this.kanbanBoardComponent.columnDataByColumnId).forEach((columnData) => {
                this.selectedCards.push(...columnData.items);
            });
            this.canSelectCardsManually = false;
        } else {
            this.selectedCards = [];
            this.allChecked = false;
            this.isTotalCardsSelected = false;
            this.canSelectCardsManually = true;
        }
    }

    isCardSelected(certificationFolder: CertificationFolder): boolean {
        return this.kanbanBoardComponent.isCardSelected(certificationFolder);
    }

    addCardToSelection(certificationFolder: CertificationFolder, isManual): void {
        this.kanbanBoardComponent.addCardToSelection(certificationFolder);
        if (isManual) {
            const selectedState = certificationFolder.state;
            const columnDataByState = this.kanbanBoardComponent.columnDataByColumnId[selectedState];
            if (columnDataByState.total === this.selectedCards.length) {
                this.columnSelected = selectedState;
            }
        }
    }

    canAddCardToSelection(certificationFolderState: string): boolean {
        return this.selectedCards.length ? this.selectedCards[0].state === certificationFolderState : true;
    }

    removeCardFromSelection(certificationFolder: CertificationFolder): void {
        this.kanbanBoardComponent.removeCardFromSelection(certificationFolder);
        if (this.selectedCards.length === 0) {
            this.columnSelected = null;
        }
    }

    getKanbanTotal(total: number): void {
        this.folderCount = total;
    }

    getTotalFolderCountForMenu(): number {
        if (this.columnSelected) {
            return this.columnSelectedFolderCount;
        } else if (this.allChecked) {
            return this.folderCount;
        } else {
            return this.selectedCards.length;
        }
    }
}
