<div class="min-w-full content-layout fullwidth-standard-inner-scroll">
    <app-registration-folder-connection-check *ngIf="currentOrganism?.isTrainingOrganism">
    </app-registration-folder-connection-check>
    <mat-drawer-container>
        <mat-drawer-content>
            <header class="flex flex-row justify-between pt-4 px-4 bg-white">
                <div class="flex flex-col flex-grow">
                    <div class="flex flex-row">
                        <h1>{{ 'private.certification.folders.title' | translate }}</h1>
                    </div>
                    <div>
                        <p class="subtitle">{{ 'private.certification.folders.subtitle' | translate }}</p>
                        <div class="mt-2 flex flex-row flex-grow items-center">
                            <div class="flex flex-row items-center">
                                <mat-checkbox *ngIf="environmentService.isEnableBetaFeatures(currentOrganism)"
                                              [checked]="allChecked"
                                              [disabled]="loadingMenu || columnSelected || selectedCards.length >= 1 "
                                              [class]="columnSelected ? 'opacity-50' : ''"
                                              [matTooltip]="'Sélectionner tous les dossiers'"
                                              (change)="checkedAll($event)"
                                              (click)="$event.stopPropagation()">
                                </mat-checkbox>
                                <div class="text-indigo font-semibold">
                                    {{ folderCount }}
                                </div>
                                <app-certification-folder-menu *ngIf="environmentService.isEnableBetaFeatures(currentOrganism)"
                                                               [disabled]="!selectedCards.length || !allChecked || loadingMenu"
                                                               [folders]="selectedCards"
                                                               (processedFolder)="sendRefreshCard($event, false)"
                                                               (allFoldersProcessed)="clearSelection()"
                                                               (deleteFolder)="sendDeleteCard($event)"
                                                               [totalFolderCount]="getTotalFolderCountForMenu()"
                                                               [filters]="filters$"
                                                               [isTotalRowsSelected]="isTotalCardsSelected">
                                </app-certification-folder-menu>
                                <mat-icon color="primary">view_kanban</mat-icon>
                                <button
                                    [matTooltip]=" 'private.training-organism.folders.toolTip.tableView' | translate "
                                    [matTooltipPosition]="'above'"
                                    [matTooltipShowDelay]="500" (click)="setShowTableView()" mat-icon-button
                                    [disabled]="!showKanbanView"
                                    [color]="'accent'">
                                    <mat-icon svgIcon="table_rows"></mat-icon>
                                </button>
                            </div>
                            <app-entity-filters [entityClass]="EntityClass.CERTIFICATION_FOLDER">
                            </app-entity-filters>
                        </div>
                    </div>
                </div>
                <div class="py-4 pl-4 flex flex-row flex-nowrap items-center" [formGroup]="filters">
                    <div class="mr-8">
                        <mat-form-field class="w-90">
                            <input matInput formControlName="query" [value]="queryConstructor"
                                   [title]="'private.training-organism.folders.searchbar' | translate"
                                   [placeholder]="'common.table.filters.globalSearch' | translate" maxlength="50">
                            <mat-icon matPrefix svgIcon="search"></mat-icon>
                            <button mat-button *ngIf="activeFiltersCount" matSuffix mat-icon-button
                                    aria-label="Clear" (click)="clearSearch()">
                                <mat-icon svgIcon="close"></mat-icon>
                            </button>
                            <div>
                                <button [mdePopoverTriggerFor]="searchPopover" mdePopoverTriggerOn="click" class="flex">
                                    <mat-icon
                                        [matBadge]="activeFiltersCount ? activeFiltersCount+'' : null"
                                        matBadgePosition="below after"
                                        matBadgeSize="small"
                                        [color]="activeFiltersCount ? 'primary' : undefined"
                                        [svgIcon]="activeFiltersCount ? 'filter_alt' : 'manage_search'"></mat-icon>
                                </button>
                                <mde-popover #searchPopover="mdePopover"
                                             [mdePopoverOverlapTrigger]="false"
                                             mdePopoverOffsetX="17"
                                             mdePopoverOffsetY="10"
                                             mdePopoverArrowWidth="0"
                                             mdePopoverPositionX="before"
                                             mdePopoverCloseOnClick="false"
                                             mdePopoverArrowColor="#FFF">
                                    <app-certification-folder-advanced-search [searchAdvanced]="searchAdvanced"
                                                                              (closePopOver)="closePopover()"
                                    ></app-certification-folder-advanced-search>
                                </mde-popover>
                            </div>
                        </mat-form-field>
                        <div class="flex items-center justify-end">
                            <div class="flex flex-row"
                                 [matTooltip]=" 'private.certification.folders.createFolder.create' | translate "
                                 [matTooltipPosition]="'above'"
                                 [matTooltipShowDelay]="500">
                                <button type="button" color="primary"
                                        mat-flat-button (click)="openCreate()"
                                        [disabled]="openCertificationFolderCreateSide">
                                    {{ 'private.certification.folders.createFolder.create' | translate }}
                                </button>
                            </div>
                            <div class="flex align-center">
                                <button type="button"
                                        color="primary"
                                        class="button-actions ml-2"
                                        mat-flat-button
                                        [disabled]="loadingExportToFormat"
                                        (click)="subscription?.allowCertifierPlus ? exportToFormat('xlsx') : openDialogSubscription()">{{ 'common.actions.export.exportExcelButton' | translate }}
                                </button>
                                <button class="flex justify-center items-center button-arrow button-arrow-primary"
                                        color="primary"
                                        mat-flat-button
                                        [matMenuTriggerFor]="exportOtherFormats"
                                        [disabled]="loadingExportToFormat"
                                        title="Autres formats"
                                        type="button">
                                    <mat-icon class="icon" svgIcon="arrow_drop_down"></mat-icon>
                                </button>
                                <mat-menu #exportOtherFormats="matMenu" xPosition="before">
                                    <ng-template matMenuContent>
                                        <button type="button"
                                                mat-menu-item
                                                [disabled]="loadingExportToFormat"
                                                (click)="exportToFormat('csv')">{{ 'common.actions.export.exportCsvButton' | translate }}
                                        </button>
                                        <button type="button"
                                                *ngIf="subscription?.allowCertifiers"
                                                mat-menu-item
                                                [disabled]="loadingExportToFormat"
                                                (click)="subscription?.allowCertifierPlus ? openImportDialog() : openDialogSubscription()">{{ 'common.actions.export.importExcelButton' | translate }}
                                        </button>
                                    </ng-template>
                                </mat-menu>
                            </div>
                        </div>
                    </div>
                </div>
            </header>
            <main>
                <app-kanban-board [showMultipleSelection]="environmentService.isEnableBetaFeatures(currentOrganism)"
                                  [canSelectCardsManually]="canSelectCardsManually"
                                  [listColumnConfigs]="listColumnConfigs"
                                  [listAllItemsByColumn]="listAllItemsByColumn"
                                  [listItemsForColumn]="listItemsForColumn"
                                  [getAdditionalDataForColumn]="getRevenueForColumn"
                                  [selectedItem]="selectedCertificationFolder"
                                  [comparisonProperty]="'id'"
                                  [filters$]="certificationFolderFilters$"
                                  [selectedCards]="selectedCards"
                                  [columnSelected]="columnSelected"
                                  [allChecked]="allChecked"
                                  (getActiveFilters)="getActiveFilters($event)"
                                  (folderCount)="getKanbanTotal($event)"
                                  (clearSelection)="clearSelection()"
                                  (checkedColumn)="checkedColumn($event)"
                                  [titleNoData]="'common.kanban.card.no-dataFolder'| translate"
                                  (openCard)="openSummary($event.item, $event.keepShortcutSide)"
                >
                    <ng-template #kanbanMenuTemplate let-selectedCards="selectedCards" let-disabled="disabled">
                        <app-certification-folder-menu [disabled]="disabled"
                                                       [folders]="selectedCards"
                                                       [filters]="filters$"
                                                       [totalFolderCount]="getTotalFolderCountForMenu()"
                                                       [isTotalRowsSelected]="isTotalCardsSelected"
                                                       (processedFolder)="sendRefreshCard($event, false)"
                                                       (allFoldersProcessed)="clearSelection()"
                                                       (deleteFolder)="sendDeleteCard($event)">
                        </app-certification-folder-menu>
                    </ng-template>
                    <ng-template #kanbanCardTemplate let-item>
                        <app-certification-folder-kanban-card *ngIf="item"
                                                              [showMultipleSelection]="environmentService.isEnableBetaFeatures(currentOrganism) && (item.state === columnSelected || allChecked)"
                                                              [showManualSelectionCard]="environmentService.isEnableBetaFeatures(currentOrganism) && canSelectCardsManually"
                                                              [readOnlyCheckBox]="!allChecked || !columnSelected"
                                                              [isCardSelected]="isCardSelected(item)"
                                                              [canAddCardToSelection]="canAddCardToSelection(item.state)"
                                                              (addCardToSelection)="addCardToSelection($event.item, $event.isManual)"
                                                              (removeCardFromSelection)="removeCardFromSelection(item)"
                                                              class="flex-1 flex flex-col justify-between"
                                                              [item]="item"
                                                              [isCertifier]="item._links.certifier.siret === currentOrganism.siret"
                                                              (deleteFolder)="sendDeleteCard($event)"
                                                              (processedFolder)="sendRefreshCard($event)"
                                                              (searchTag)="applyNewFilter({tags: $event})">
                        </app-certification-folder-kanban-card>
                    </ng-template>
                    <div *ngIf="subscription.allowCertifiers">
                        <ng-template #kanbanColumnFooterTemplate let-column="column" let-isExpanded="isExpanded"
                                     let-isKanbanLoading="isKanbanLoading">
                            <div class="py-3 px-2 border-t text-center font-semibold" *ngIf="isExpanded">
                                <span class="font-semibold">
                                    <div *ngIf="!!column?.revenue else noCA"
                                         class="flex items-center justify-center">
                                        <div
                                            [matTooltip]="'private.certification.folders.kanban.toolTipRevenue' | translate">
                                            {{ 'private.training-organism.folders.revenue.total' | translate }}
                                            <span class="text-indigo">{{ +column?.revenue | number: '1.2-2':'fr-FR' }}
                                                €</span>
                                        </div>
                                        <div *ngIf="!hasAllAmountHtInit">
                                             <mat-icon [matTooltip]="(currentOrganism?.isTrainingOrganism ? 'private.certification.folders.kanban.toolTipExplicationTrainingOrganism' :
                                             'private.certification.folders.kanban.toolTipExplication') | translate"
                                                       color="warn"
                                                       class="ml-1 icon-size-16"
                                                       svgIcon="error"></mat-icon>
                                        </div>
                                    </div>
                                    <ng-template #noCA>
                                        <a *ngIf="!loading && !isKanbanLoading && column.total > 0; else footerLoading"
                                           class="link" [routerLink]="['/certification/partenariats']"
                                           [matTooltip]="'private.certification.folders.kanban.toolTipRevenueNoCA' | translate">
                                            {{ 'private.training-organism.folders.revenue.total' | translate }} **
                                        </a>
                                        <ng-template #footerLoading>-</ng-template>
                                    </ng-template>
                                </span>
                            </div>
                        </ng-template>
                    </div>
                </app-kanban-board>
            </main>
        </mat-drawer-content>
        <mat-drawer #sidenav mode="side" position="end" [autoFocus]="false" [disableClose]="true">
            <app-certification-folder-side #certificationFolderSide
                                           *ngIf="selectedCertificationFolder"
                                           [certificationFolder]="selectedCertificationFolder"
                                           (next)="selectNextCard()"
                                           (previous)="selectPreviousCard()"
                                           (certificationFolderRefreshed)="sendRefreshCard($event)"
                                           (certificationFolderDeleted)="sendDeleteCard($event)"
                                           (attendeeRefreshed)="sendRefreshAttendee($event)"
                                           (closeSide)="closeSide()"
                                           [hasNext]="hasNext()" [hasPrevious]="hasPrevious()"
            ></app-certification-folder-side>
            <app-certification-folder-create-side
                #certificationFolderCreateSide
                *ngIf="openCertificationFolderCreateSide"
                (closeSide)="closeSide()"
                (certificationFolderCreated)="sendInsertCard($event)">
            </app-certification-folder-create-side>
        </mat-drawer>
    </mat-drawer-container>
</div>
