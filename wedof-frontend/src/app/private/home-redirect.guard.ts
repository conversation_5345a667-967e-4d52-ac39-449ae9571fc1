import {Injectable} from '@angular/core';
import {ActivatedRouteSnapshot, CanActivate, Router, RouterStateSnapshot, UrlTree} from '@angular/router';
import {forkJoin, Observable} from 'rxjs';
import {map} from 'rxjs/operators';
import {Store} from '@ngxs/store';
import {OrganismState} from '../shared/api/state/organism.state';
import {SubscriptionState} from '../shared/api/state/subscription.state';
import {SubscriptionCertifierTypes} from '../shared/api/models/subscription';

@Injectable({
    providedIn: 'root'
})
export class HomeRedirectGuard implements CanActivate {

    constructor(
        private _router: Router,
        private _store: Store
    ) {
    }

    canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<UrlTree> {
        return forkJoin([
            this._store.selectOnce(OrganismState.organism),
            this._store.selectOnce(SubscriptionState.subscription)
        ]).pipe(
            map(([organism, subscription]) => {
                let urlTree;
                const missingOrganismInformations = !organism.billingSoftware || !organism.crm;
                const showCatalog = organism.isTrainingOrganism && subscription.certifierType === SubscriptionCertifierTypes.PARTNER;
                if (organism.isReseller || (missingOrganismInformations && !showCatalog)) {
                    urlTree = this._router.createUrlTree(['/profil']);
                } else if (showCatalog) {
                    urlTree = this._router.createUrlTree(['/formation/certifications/catalogue'], {queryParams: {state: 'active'}});
                } else if (organism.isCertifierOrganism) {
                    urlTree = this._router.createUrlTree(['/certification/dossiers']);
                } else {
                    urlTree = this._router.createUrlTree(['/formation/dossiers']);
                }
                return urlTree;
            })
        );
    }
}
