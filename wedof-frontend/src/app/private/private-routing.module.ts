import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {HomeRedirectGuard} from './home-redirect.guard';
import {PrivateGuard} from './private.guard';

const routes: Routes = [{
    path: '',
    canActivate: [PrivateGuard],
    children: [
        {
            path: 'accueil',
            canActivate: [HomeRedirectGuard],
        }, {
            path: 'profil',
            loadChildren: () => import('./profile/profile.module').then((m) => m.ProfileModule),
        },
        {
            path: 'certification',
            loadChildren: () => import('./certification/certification.module').then((m) => m.CertificationModule),
        },
        {
            path: 'formation',
            loadChildren: () => import('./training-organism/training-organism.module').then((m) => m.TrainingOrganismModule),
        },
        {
            path: 'mes-applications',
            loadChildren: () => import('../applications/applications-routing.module').then((m) => m.ApplicationsRoutingModule)
        }
    ]
}];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class PrivateRoutingModule {
}
