import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';

import {PrivateRoutingModule} from './private-routing.module';
import {ApiModule} from '../shared/api/api.module';
import {ClipboardModule} from '@angular/cdk/clipboard';

@NgModule({
    declarations: [],
    imports: [
        CommonModule,
        ApiModule,
        PrivateRoutingModule,
        ClipboardModule
    ]
})
export class PrivateModule { }
