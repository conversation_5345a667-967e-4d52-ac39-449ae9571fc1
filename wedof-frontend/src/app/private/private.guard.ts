import {Injectable} from '@angular/core';
import {ActivatedRouteSnapshot, CanActivate, Router, RouterStateSnapshot, UrlTree} from '@angular/router';
import {forkJoin, Observable} from 'rxjs';
import {map} from 'rxjs/operators';
import {AuthService} from '../core/auth/auth.service';
import {UserState} from '../shared/api/state/user.state';
import {Store} from '@ngxs/store';
import {OrganismState} from '../shared/api/state/organism.state';

@Injectable({
    providedIn: 'root'
})
export class PrivateGuard implements CanActivate {

    constructor(
        private _authService: AuthService,
        private _router: Router,
        private _store: Store
    ) {
    }

    canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<UrlTree | boolean> {
        return forkJoin([
            this._store.selectOnce(OrganismState.organism),
            this._store.selectOnce(UserState.user)
        ]).pipe(
            map(([organism, user]) => {
                if (!organism || user.rgpdMsa == null) {
                    this._authService.signOut();
                    return this._router.createUrlTree(['/auth/inscription'], {
                        queryParams: {
                            email: user.email,
                            phoneNumber: user.phoneNumber,
                            lastName: user.lastName,
                            firstName: user.firstName
                        }
                    });
                } else if (organism && this._authService.isSales) {
                    let hostname = window.location.hostname;
                    const domain = window.location.hostname.split('.');
                    if (domain.length > 1 && domain[0] === organism.subDomain) {
                        return this._router.createUrlTree(['/funnel/commercial/proposition'], {});
                    } else if (domain.length > 1) {
                        hostname = hostname.replace(domain[0], organism.subDomain);
                    } else {
                        hostname = organism.subDomain + '.' + hostname;
                    }
                    const refreshToken = this._authService.refreshToken;
                    this._authService.signOut();
                    window.location.replace(window.location.protocol + '//' + hostname + ':' + window.location.port + '/auth/connexion?' + 'refreshToken=' + refreshToken);
                    return false;
                } else {
                    return true;
                }
            })
        );
    }
}
