import {Component, OnInit} from '@angular/core';
import {OrganismApplication, OrganismApplicationStates} from '../../shared/api/models/organism-application';
import {ActivatedRoute, Router} from '@angular/router';
import {OrganismApplicationService} from '../../shared/api/services/organism-application.service';
import {Application} from '../../applications/shared/application.interface';
import {ApplicationsService} from '../../applications/shared/applications.service';
import {Subscription, SubscriptionTypes} from '../../shared/api/models/subscription';
import {MatDialog} from '@angular/material/dialog';
import {
    DialogUpgradeSubscriptionComponent
} from '../../shared/subscription/dialog-upgrade-subscription/dialog-upgrade-subscription.component';
import {Organism} from '../../shared/api/models/organism';
import {Select, Store} from '@ngxs/store';
import {combineLatest, Observable, Subject} from 'rxjs';
import {SubscriptionState} from '../../shared/api/state/subscription.state';
import {takeUntil} from 'rxjs/operators';
import {OrganismState} from '../../shared/api/state/organism.state';
import {WebhooksApplication} from '../../applications/webhooks/webhooks.application';
import {MessageTemplateApplication} from '../../applications/message-template/message-template.application';
import {DocumentApplication} from '../../applications/document/document.application';
import {DisclaimerComponent} from '../../shared/material/disclaimer/disclaimer.component';

@Component({
    selector: 'applications-list',
    templateUrl: './applications-list.component.html',
    styleUrls: ['./applications-list.component.scss']
})
export class ApplicationsListComponent implements OnInit {

    OrganismApplicationStates = OrganismApplicationStates;

    constructor(
        private _store: Store,
        private _router: Router,
        private _dialog: MatDialog,
        private _activatedRoute: ActivatedRoute,
        private _applicationsService: ApplicationsService,
        private _organismApplicationService: OrganismApplicationService,
    ) {
    }

    @Select(SubscriptionState.subscription) subscription$: Observable<Subscription>;
    @Select(OrganismState.organism) organism$: Observable<Organism>;

    applications: Application[];
    _organismApplications: OrganismApplication[];
    subscription: Subscription;
    loading = true;
    organism: Organism;

    private _unsubscribeAll = new Subject<void>();

    ngOnInit(): void {
        combineLatest([
            this.organism$,
            this.subscription$,
            this._organismApplicationService.findAll()
        ]).pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe(([organism, subscription, organismApplications]) => {
            this.organism = organism;
            this.subscription = subscription;
            this.applications = this._applicationsService.findAll();
            if (this.organism.isReseller) {
                this.applications = this.applications.filter((application) => application.appId() === WebhooksApplication.appId());
            }
            this._organismApplications = organismApplications;
            this.loading = false;
            // app to enable from state params
            const enableAppId = history.state.enableAppId ?? null;
            if (enableAppId) {
                const appToEnable = this.applications.find((application) => application.appId() === enableAppId);
                if (appToEnable) {
                    this.activateApp(appToEnable);
                }
            }
        });
    }

    isAvailable(application: Application): boolean {
        return this.subscription.allowedApps?.includes(application.appId());
    }

    isEnabled(application: Application): boolean {
        return this.isAvailable(application) && this._organismApplications?.some(app => app.appId === application.appId() && app.enabled);
    }

    isPendingDisable(application: Application): boolean {
        const states = [OrganismApplicationStates.PENDING_DISABLE, OrganismApplicationStates.PENDING_DISABLE_TRIAL];
        return this._organismApplications?.some(app => app.appId === application.appId() && states.includes(app.state));
    }

    isPendingEnable(application: Application): boolean {
        const states = [OrganismApplicationStates.PENDING_ENABLE, OrganismApplicationStates.PENDING_ENABLE_TRIAL];
        return this._organismApplications?.some(app => app.appId === application.appId() && states.includes(app.state));
    }

    getOrganismApplication(application: Application): OrganismApplication | null {
        return this._organismApplications?.find(app => app.appId === application.appId());
    }

    enable(application: Application): void {
        this._organismApplicationService.enable(application.appId()).subscribe((_application) => {
            if (this._organismApplications.find(app => app.id === _application.id)) { // compare with internal id first if app already exist
                this._organismApplications = this._organismApplications.map(obj => _application.id === obj.id ? _application : obj);
            } else {
                this._organismApplications.push(_application);
            }
            this._router.navigate(['mes-applications', _application.appId, 'reglages']);
        });
    }

    openSubscriptionDialog(appId: string): void {
        let subscriptionTypeToShow = SubscriptionTypes.TRAINING;
        let name = appId === WebhooksApplication.appId() ? WebhooksApplication.appId() : 'apps';
        if (appId === DocumentApplication.appId()) {
            name = 'documentTraining';
            if (!this.organism.isTrainingOrganism) {
                name = 'documentCertifier';
                subscriptionTypeToShow = SubscriptionTypes.CERTIFIER;
            }
        } else if (appId === MessageTemplateApplication.appId()) {
            name = 'messageTemplatesTraining';
            if (!this.organism.isTrainingOrganism) {
                name = 'messageTemplatesCertifier';
                subscriptionTypeToShow = SubscriptionTypes.CERTIFIER;
            }
        }

        this._dialog.open(DialogUpgradeSubscriptionComponent, {
            panelClass: ['full-page-scroll-50'],
            data: {
                organism: this.organism,
                subscription: this.subscription,
                fromPage: name,
                subscriptionTypeToShow: subscriptionTypeToShow
            }
        });
    }

    activateApp(application: Application): void {
        if (this.isAvailable(application)) {
            if (application.isSubscriptionOption()) {
                const trialAvailable = this.hasTrialAvailable(application);
                this._dialog.open(trialAvailable ? DisclaimerComponent : DisclaimerComponent, {
                    panelClass: ['full-page-scroll-30'],
                    disableClose: true,
                    data: {
                        title: application.appName(),
                        subtitle: 'private.application.' + application.appId() + '.subscriptionOptionDialog.subtitle',
                        subtitle2: 'private.application.' + application.appId() + '.subscriptionOptionDialog.subtitle2' + (trialAvailable ? 'Trial' : ''),
                        mainButton: {
                            title: trialAvailable ? 'private.application.items.actions.try' : 'private.application.items.actions.enable',
                            method: () => this.enable(application)
                        },
                        secondaryButton: {
                            title: 'common.actions.cancel',
                            method: () => {
                            }
                        }
                    }
                });
            } else {
                this.enable(application);
            }
        } else {
            this.openSubscriptionDialog(application.appId());
        }
    }

    hasTrialAvailable(application: Application): boolean {
        const organismApplication = this._organismApplications.find(app => app.appId === application.appId());
        if (organismApplication) {
            return application.hasTrialAvailable() && organismApplication.state === OrganismApplicationStates.DISABLED && organismApplication.endDate == null;
        } else {
            return application.hasTrialAvailable();
        }
    }
}
