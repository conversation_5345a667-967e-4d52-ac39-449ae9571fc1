<div class="content-layout fullwidth-basic-normal-scroll">
    <mat-progress-spinner class="m-auto" *ngIf="loading; else showComponent" [diameter]="75"
                          mode="indeterminate"></mat-progress-spinner>
    <ng-template #showComponent>
        <div class="main">

            <div class="content flex flex-wrap" *ngIf="_organismApplications && applications">
                <treo-card class="flex flex-col w-full p-6">
                    <div class="flex items-center justify-between">
                        <div class="text-2xl font-semibold leading-tight">
                            {{ 'private.application.title' | translate }}
                        </div>
                    </div>

                    <div class="flex flex-col mt-5">
                        <div class="flex items-center mt-6" *ngFor="let application of applications">
                            <img [class.disabled]="(isEnabled(application) || isPendingEnable(application)) !== true"
                                 [src]="'/assets/applications/'+application.appId()+'.png'"
                                 alt="{{ application.appName() }}"
                                 class="w-14 h-14 mr-4 rounded object-cover"/>

                            <div [class.disabled]="(isEnabled(application) || isPendingEnable(application)) !== true"
                                 class="flex flex-col w-full">
                                <div class="font-medium leading-none">
                                    {{ application.appName() }}
                                    <span class="text-sm font-normal"
                                          *ngIf="getOrganismApplication(application)?.endDate && isPendingDisable(application)">
                                        {{ 'private.application.common.subtitle.pendingDisable' | translate: {endDate: getOrganismApplication(application).endDate| date : 'dd/MM/yyyy'} }}
                                    </span>
                                    <span class="text-sm font-normal"
                                          *ngIf="getOrganismApplication(application)?.endDate && getOrganismApplication(application).state == OrganismApplicationStates.TRIAL">
                                        {{ 'private.application.common.subtitle.trial' | translate: {endDate: getOrganismApplication(application).endDate| date : 'dd/MM/yyyy'} }}
                                    </span>
                                </div>
                                <span
                                    [innerHTML]="'private.application.descriptif.' + application.appId() | translate"></span>
                            </div>

                            <div class="flex flex-row">
                                <ng-container *ngIf="isEnabled(application); else enableBlock">
                                    <a [routerLink]="[application.appId(), 'reglages']" mat-button>
                                        {{ 'private.application.items.actions.settings' | translate }}
                                    </a>
                                </ng-container>
                                <ng-template #enableBlock>
                                    <button
                                        *ngIf="isPendingEnable(application)"
                                        class="mat-focus-indicator mat-raised-button mat-button-base mat-button-disabled">
                                        {{ 'private.application.items.actions.pendingEnable'|translate }}
                                    </button>
                                    <button
                                        *ngIf="!isPendingEnable(application)"
                                        class="mat-focus-indicator mat-raised-button mat-button-base mat-primary"
                                        (click)="activateApp(application)">
                                        {{ (hasTrialAvailable(application) ? 'private.application.items.actions.try' : 'private.application.items.actions.enable') | translate }}
                                    </button>
                                </ng-template>
                            </div>
                        </div>
                    </div>

                </treo-card>
            </div>
        </div>
    </ng-template>
</div>
