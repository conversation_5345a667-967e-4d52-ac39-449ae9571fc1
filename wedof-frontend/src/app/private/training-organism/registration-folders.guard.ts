import {Injectable} from '@angular/core';
import {CanActivate, Router, UrlTree} from '@angular/router';
import {Observable} from 'rxjs';
import {first, map} from 'rxjs/operators';
import {Select} from '@ngxs/store';
import {SubscriptionState} from '../../shared/api/state/subscription.state';
import {Subscription} from '../../shared/api/models/subscription';

@Injectable({
    providedIn: 'root'
})
export class RegistrationFoldersGuard implements CanActivate {

    @Select(SubscriptionState.subscription) subscription$: Observable<Subscription>;

    constructor(private _router: Router) {
    }

    canActivate(): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
        return this.subscription$.pipe(
            first(),
            map(subscription => {
                const canActivate = subscription?.allowRegistrationFolders;
                if (!canActivate) {
                    this._router.navigate(['formation/dossiers/souscription']);
                }
                return canActivate;
            })
        );
    }

}
