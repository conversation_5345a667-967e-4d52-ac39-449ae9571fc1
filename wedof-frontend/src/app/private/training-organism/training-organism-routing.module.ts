import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {TrainingOrganismStatsComponent} from './stats/training-organism-stats.component';
import {TrainingOrganismProposalsComponent} from './proposals/proposals/training-organism-proposals.component';
import {
    RegistrationFoldersViewByRegistrationFoldersComponent
} from './registration-folders/registration-folders-view-by-registration-folders/registration-folders-view-by-registration-folders.component';
import {
    RegistrationFoldersViewBySessionComponent
} from './registration-folders/registration-folders-view-by-session/registration-folders-view-by-session.component';
import {CanDeactivateGuard} from '../../shared/utils/can-deactivate/can-deactivate.guard';
import {
    KanbanRegistrationFoldersComponent
} from './registration-folders/kanban-registration-folders/kanban-registration-folders.component';
import {TrainingOrganismChildGuard} from './training-organism-child.guard';
import {RegisterTrainingFolderComponent} from './register-training-folder/register-training-folder.component';
import {ProposalGuard} from './proposal.guard';
import {RegisterProposalsComponent} from './register-proposals/register-proposals.component';
import {RegisterStatsComponent} from './register-stats/register-stats.component';
import {StatsGuard} from './stats.guard';
import {KanbanProposalsComponent} from './proposals/kanban-proposals-component/kanban-proposals.component';
import {TrainingOrganismCertificationsComponent} from './certifications/training-organism-certifications.component';
import {RegistrationFoldersGuard} from './registration-folders.guard';
import {ConfigureProposalComponent} from './configure-proposal/configure-proposal.component';
import {
    AnchorShortcutTrainingOrganismCertificationSidePanelGuard
} from './anchor-shortcut-training-organism-certification-side-panel-guard.service';
import {AnchorShortcutFolderSidePanelGuard} from '../certification/anchor-shortcut-folder-side-panel-guard.service';
import {AnchorShortcutProposalSidePanelGuard} from './anchor-shortcut-proposal-side-panel-guard.service';

export const routes: Routes = [
    {
        // Lazy loaded behind 'organisme'
        path: '',
        children: [
            {
                path: '',
                children: [
                    {
                        path: 'dossiers/souscription',
                        component: RegisterTrainingFolderComponent,
                    }, {
                        path: 'propositions/souscription',
                        component: RegisterProposalsComponent,
                    }, {
                        path: 'propositions/configuration',
                        component: ConfigureProposalComponent,
                    }, {
                        path: 'statistiques/souscription',
                        component: RegisterStatsComponent,
                    },
                ]
            },
            {
                path: '',
                canActivateChild: [TrainingOrganismChildGuard],
                children: [
                    {
                        // Dossiers
                        path: '',
                        canActivate: [RegistrationFoldersGuard],
                        children: [
                            {
                                path: 'dossiers',
                                redirectTo: 'dossiers/kanban/',
                                pathMatch: 'full',
                                canDeactivate: [CanDeactivateGuard]
                            }, {
                                path: 'dossiers/liste',
                                redirectTo: 'dossiers/liste/',
                                pathMatch: 'full',
                                canDeactivate: [CanDeactivateGuard]
                            }, {
                                path: 'dossiers/liste/:externalId',
                                component: RegistrationFoldersViewByRegistrationFoldersComponent,
                                canDeactivate: [CanDeactivateGuard],
                                children: [
                                    {
                                        path: ':anchor',
                                        canActivate: [AnchorShortcutFolderSidePanelGuard],
                                    }
                                ]
                            }, {
                                path: 'dossiers/kanban',
                                redirectTo: 'dossiers/kanban/',
                                pathMatch: 'full',
                                canDeactivate: [CanDeactivateGuard]
                            }, {
                                path: 'dossiers/kanban/:externalId',
                                component: KanbanRegistrationFoldersComponent,
                                canDeactivate: [CanDeactivateGuard],
                                children: [
                                    {
                                        path: ':anchor',
                                        canActivate: [AnchorShortcutFolderSidePanelGuard],
                                    }
                                ]
                            }, {
                                path: 'sessions',
                                redirectTo: 'dossiers/sessions/',
                                pathMatch: 'full'
                            }, {
                                path: 'dossiers/sessions',
                                redirectTo: 'dossiers/sessions/',
                                pathMatch: 'full'
                            }, {
                                path: 'dossiers/sessions/:session',
                                component: RegistrationFoldersViewBySessionComponent,
                                children: [
                                    {
                                        path: ':externalId',
                                    },
                                    {
                                        path: ':externalId/:anchor',
                                        canActivate: [AnchorShortcutFolderSidePanelGuard]
                                    },
                                ]
                            }
                        ]
                    },
                    {
                        // propositions
                        path: '',
                        canActivate: [ProposalGuard],
                        children: [
                            {
                                path: 'propositions',
                                redirectTo: 'propositions/kanban/',
                                pathMatch: 'full',
                                canDeactivate: [CanDeactivateGuard],
                            }, {
                                path: 'propositions/liste',
                                redirectTo: 'propositions/liste/',
                                pathMatch: 'full',
                                canDeactivate: [CanDeactivateGuard]
                            }, {
                                path: 'propositions/liste/:code',
                                component: TrainingOrganismProposalsComponent,
                                canDeactivate: [CanDeactivateGuard],
                                children: [
                                    {
                                        path: ':anchor',
                                        canActivate: [AnchorShortcutProposalSidePanelGuard],
                                    }
                                ]
                            }, {
                                path: 'propositions/kanban',
                                redirectTo: 'propositions/kanban/',
                                pathMatch: 'full',
                            }, {
                                path: 'propositions/kanban/:code',
                                component: KanbanProposalsComponent,
                                canDeactivate: [CanDeactivateGuard],
                                children: [
                                    {
                                        path: ':anchor',
                                        canActivate: [AnchorShortcutProposalSidePanelGuard],
                                    }
                                ]
                            }
                        ]
                    },
                    {
                        path: 'statistiques',
                        component: TrainingOrganismStatsComponent,
                        canActivate: [StatsGuard]
                    },
                ]
            }, {
                path: 'certifications',
                redirectTo: 'certifications/catalogue/',
                pathMatch: 'full',
            }, {
                path: 'certifications/catalogue',
                redirectTo: 'certifications/catalogue/',
                pathMatch: 'full',
            },
            {
                path: 'certifications/catalogue/:certifInfo',
                component: TrainingOrganismCertificationsComponent,
                children: [
                    {
                        path: ':anchor',
                        canActivate: [AnchorShortcutTrainingOrganismCertificationSidePanelGuard],
                    }
                ]
            }
        ]
    }
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class TrainingOrganismRoutingModule {
}
