import {Injectable} from '@angular/core';
import {ActivatedRouteSnapshot, CanActivate} from '@angular/router';
import {Observable} from 'rxjs';
import {ShortcutProposalSidePanelEnum} from '../../shared/utils/enums/shortcut-side-panel-enum';

@Injectable({
    providedIn: 'root'
})
export class AnchorShortcutProposalSidePanelGuard implements CanActivate {
    canActivate(
        route: ActivatedRouteSnapshot
    ): Observable<boolean> | Promise<boolean> | boolean {
        const allowedAnchors: ShortcutProposalSidePanelEnum[] = Object.values(ShortcutProposalSidePanelEnum);
        const anchor: string = route.paramMap.get('anchor');
        if (allowedAnchors.includes(anchor as ShortcutProposalSidePanelEnum)) {
            return true;
        } else {
            window.location.href = '404-not-found';
            return false;
        }
    }

}
