import {AfterViewInit, Component, OnDestroy, OnInit, ViewChild} from '@angular/core';
import {distinct, first, map, switchMap, takeUntil} from 'rxjs/operators';
import {ActivatedRoute, Router} from '@angular/router';
import {FormBuilder, FormGroup} from '@angular/forms';
import {MatSidenav} from '@angular/material/sidenav';
import {ProposalSideComponent} from '../../../../shared/sides/proposal-side/proposal-side.component';
import {ProposalCreateSideComponent} from '../../../../shared/sides/proposal-create-side/proposal-create-side.component';
import {CanDeactivateComponent} from '../../../../shared/utils/can-deactivate/can-deactivate.component';
import {Observable, ReplaySubject, Subject} from 'rxjs';
import {PaginatedResponse} from '../../../../shared/api/services/abstract-paginated.service';
import {Proposal, ProposalStates} from '../../../../shared/api/models/proposal';
import {ProposalFilters} from '../../../../shared/proposal/proposal-table/proposal-table.component';
import {ProposalHttpParams, ProposalService} from '../../../../shared/api/services/proposal.service';
import {RegistrationFolderService} from '../../../../shared/api/services/registration-folder.service';
import {
    KanbanAdditionalDataResponse,
    KanbanBoardComponent,
    KanbanColumnConfigsResponse,
    KanbanColumnResponse
} from '../../../../shared/kanban-board/kanban-board/kanban-board.component';
import {SnackBarComponent} from '../../../../shared/material/snack-bar/snack-bar.component';
import {displayTextSnackBar} from '../../../../shared/utils/displayTextSnackBar';
import {FileService} from '../../../../shared/api/services/file.service';
import {MatSnackBar} from '@angular/material/snack-bar';
import {TranslateService} from '@ngx-translate/core';

interface ProposalFiltersKanban {
    query: string;
}

@Component({
    selector: 'app-kanban-proposals',
    templateUrl: './kanban-proposals.component.html',
    styleUrls: ['./kanban-proposals.component.scss']
})
export class KanbanProposalsComponent extends CanDeactivateComponent implements OnInit, AfterViewInit, OnDestroy {

    constructor(
        private _proposalService: ProposalService,
        private _registrationFolderService: RegistrationFolderService,
        private _router: Router,
        private _activatedRoute: ActivatedRoute,
        private _formBuilder: FormBuilder,
        private _translateService: TranslateService,
        private _fileService: FileService,
        private _snackBar: MatSnackBar
    ) {
        super();
    }

    filters: FormGroup;
    showKanbanView = true;
    queryConstructor: string;
    cpfSessionMinDate: Date = null;
    selectedProposal: Proposal = null;
    openProposalCreateSide;
    proposalFilters$ = new ReplaySubject<ProposalFilters>();
    activeFiltersCount = 0;
    exportingCSV = false;

    private _unsubscribeAll: Subject<void> = new Subject();
    private getProposal$ = new Subject<string>();

    @ViewChild('sidenav') sidenav: MatSidenav;
    @ViewChild('proposalSide') private proposalSide: ProposalSideComponent;
    @ViewChild('proposalCreateSide') private proposalCreateSide: ProposalCreateSideComponent;
    @ViewChild(KanbanBoardComponent) private kanbanBoardComponent: KanbanBoardComponent<Proposal>;
    listColumnConfigs: () => Observable<KanbanColumnConfigsResponse> = () => this._proposalService.listColumnConfigs();
    listAllItemsByColumn: (columnIds: string[], params: ProposalHttpParams) => Observable<KanbanColumnResponse<Proposal>> =
        (columnIds: string[], params: ProposalHttpParams) => this._proposalService.listByColumn(columnIds, params)
    listItemsForColumn: (params: ProposalHttpParams) => Observable<PaginatedResponse<Proposal>> =
        (params: ProposalHttpParams) => this._proposalService.list({
            ...params,
            isIndividual: true
        })
    getRevenueForColumn: (state: string, params: ProposalHttpParams) => Observable<KanbanAdditionalDataResponse> =
        (state: string, params: ProposalHttpParams) =>
            this._proposalService.revenueByColumn(state, params).pipe(
                map(newValue => ({columnId: state, fieldName: 'revenue', newValue}))
            )

    ngOnInit(): void {
        const {externalId} = this._activatedRoute.snapshot.params;
        this.openProposalCreateSide = externalId === 'creer';
        this._registrationFolderService.sessionMinDates$.pipe(first()).subscribe((sessionMinDates) => {
            this.cpfSessionMinDate = new Date(sessionMinDates.cpfSessionMinDate);
        });
        this.filters = this._formBuilder.group({
            query: [undefined]
        });
        this.filters.valueChanges.pipe(
            distinct(),
            takeUntil(this._unsubscribeAll)
        ).subscribe((proposalFilters: ProposalFiltersKanban) => {
            if (this.filters.valid) {
                this.proposalFilters$.next({
                    query: proposalFilters?.query
                });
                this.queryConstructor = proposalFilters['query'];
                this.activeFiltersCount = Object.keys(proposalFilters).filter(key => proposalFilters[key]).length;
            }
        });
        this.proposalFilters$.next();
        this.getProposal$.pipe(
            switchMap((code) => this._proposalService.get(code)),
            takeUntil(this._unsubscribeAll)
        ).subscribe(selectedProposal => {
            this.selectedProposal = selectedProposal;
            this.sidenav.open();
        });
    }

    openSummary(proposal: Proposal): void {
        this._router.navigate(['..', proposal.code], {
            relativeTo: this._activatedRoute,
            queryParamsHandling: 'preserve'
        }).then((success) => {
            // success is true if new navigation, null if it stays on the same, false if failed
            // Here we need to get the freshest hasNext / hasPrevious and proposal even on refresh (= stay on same)
            if (success === true || success === null) {
                this.selectedProposal = proposal;
            }
        });
    }

    ngAfterViewInit(): void {
        this._activatedRoute.params.pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe((params) => {
            const {code} = params;
            if (code === 'creer') {
                this.selectedProposal = null;
                this.openProposalCreateSide = true;
                setTimeout(() => {
                    this.sidenav.open();
                });
            } else if (code) {
                this.openProposalCreateSide = false;
                if (this.selectedProposal?.code !== code) {
                    this.getProposal$.next(code);
                } else {
                    this.sidenav.open();
                }
            } else {
                this.openProposalCreateSide = false;
                this.selectedProposal = null;
                if (this.sidenav?.opened) {
                    this.sidenav.close();
                }
            }
        });
    }

    sendRefreshCard(proposal: Proposal): void {
        this.kanbanBoardComponent.refreshCard(proposal);
    }

    hasPrevious(): boolean {
        return this.kanbanBoardComponent.hasPrevious(this.selectedProposal);
    }

    hasNext(): boolean {
        return this.kanbanBoardComponent.hasNext(this.selectedProposal);
    }

    selectPreviousCard(): void {
        this.kanbanBoardComponent.selectPreviousCard(this.selectedProposal);
    }

    selectNextCard(): void {
        this.kanbanBoardComponent.selectNextCard(this.selectedProposal);
    }

    closeSide(): void {
        this._router.navigate(['..'], {
            relativeTo: this._activatedRoute,
            queryParamsHandling: 'preserve'
        }).then((success) => {
            if (success) {
                this.selectedProposal = null;
            }
        });
    }

    canDeactivate(): boolean {
        if (this.proposalCreateSide && this.openProposalCreateSide) {
            return this.proposalCreateSide.canDeactivate();
        } else if (this.proposalSide && this.selectedProposal) {
            return this.proposalSide.canDeactivate();
        } else {
            return true;
        }
    }

    applyNewFilter(proposalFilters: ProposalFilters): void {
        this.filters.get('query').setValue(proposalFilters.query);
    }

    clearFilter(formControlName: string): void {
        this.filters.get(formControlName).setValue(null);
    }

    setShowTableView(): void {
        this.showKanbanView = false;
        this.selectedProposal = null;
        if (this.sidenav?.opened) {
            this.sidenav.close();
        }
        this._router.navigate(['/', 'formation', 'propositions', 'liste'], {
            queryParams: this._activatedRoute.snapshot.queryParams
        });
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    openCreate(): void {
        this._router.navigate(['..', 'creer'], {relativeTo: this._activatedRoute}).then((success) => {
            if (success) {
                this.selectedProposal = null;
            }
        });
    }

    sendInsertCard(proposal: Proposal): void {
        if (proposal.state !== ProposalStates.TEMPLATE) {
            this.kanbanBoardComponent.insertCard(proposal);
            this.selectedProposal = proposal;
            this._router.navigate(['/formation/propositions/kanban/', proposal.code]); // todo
        } else {
            this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(this._translateService.instant('common.actions.proposalGeneriqueCreatedSuccessfully'), 7000));
            this._router.navigate(['/formation/propositions/liste', proposal.code]);
        }
    }

    sendDeleteCard(proposal: Proposal): void {
        this.kanbanBoardComponent.removeCard(proposal);
        if (this.selectedProposal && this.selectedProposal.code === proposal.code) {
            this.closeSide();
        }
    }

    openDialogExport(): void {
        this.exportingCSV = true;
        const queryParams: { [param: string]: string } = {
            format: 'csv',
        };
        if (this.filters.get('query').value) {
            queryParams.query = this.filters.get('query').value;
        }
        this._fileService.download('/api/' + 'proposals', 'Propositions', null, queryParams).subscribe(
            (blob) => {
                if (blob.state === 'PENDING') {
                    this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(
                        this._translateService.instant('common.actions.export.exportStarted'), 0));
                } else if (blob.state === 'DONE') {
                    this.exportingCSV = false;
                    this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(
                        this._translateService.instant('common.actions.export.exportFinished')));
                }
            }
        );
    }

}
