<div class="content-layout fullwidth-standard-inner-scroll">
    <mat-drawer-container>
        <mat-drawer-content>
            <header class="flex flex-row justify-between pt-4 px-4 bg-white">
                <div class="flex flex-col">
                    <div class="flex flex-row">
                        <h1>{{ 'private.training-organism.proposals.titleKanban' | translate }}</h1>
                    </div>
                    <div>
                        <div>
                            <p class="subtitle">
                                {{ 'private.training-organism.proposals.subtitleKanban' | translate }}
                                <br/>{{ 'private.training-organism.common.minDateCpf' | translate }}
                                <b>{{ cpfSessionMinDate | dateZToDayString }}</b>
                            </p>
                        </div>
                        <div class="mt-2">
                            <mat-icon color="primary">view_kanban</mat-icon>
                            <button [matTooltip]=" 'private.training-organism.folders.toolTip.tableView' | translate "
                                    [matTooltipPosition]="'above'"
                                    [matTooltipShowDelay]="500" (click)="setShowTableView()" mat-icon-button
                                    [disabled]="!showKanbanView"
                                    [color]="'accent'">
                                <mat-icon svgIcon="table_rows"></mat-icon>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="py-4 pl-4 mr-8">
                    <mat-form-field class="w-90" [formGroup]="filters">
                        <input [title]="'private.training-organism.proposals.searchbar' | translate" matInput
                               formControlName="query" [value]="queryConstructor"
                               [placeholder]="'common.table.filters.globalSearch' | translate" maxlength="50">
                        <!--<mat-icon matPrefix svgIcon="search"></mat-icon>-->
                        <button mat-button *ngIf="activeFiltersCount > 0" matSuffix mat-icon-button
                                aria-label="Clear"
                                (click)="clearFilter('query')">
                            <mat-icon svgIcon="close"></mat-icon>
                        </button>
                        <!--<mat-icon
                            [matBadge]="activeFiltersCount ? activeFiltersCount+'' : null"
                            matBadgePosition="below after"
                            matBadgeSize="small"
                            [color]="activeFiltersCount ? 'primary' : undefined"
                            [svgIcon]="activeFiltersCount ? 'filter_alt' : 'manage_search'"></mat-icon>-->
                    </mat-form-field>
                    <div class="flex items-center justify-end">
                        <button type="button" (click)="openCreate()"
                                [disabled]="openProposalCreateSide"
                                color="primary"
                                mat-flat-button>{{ 'private.training-organism.proposals.button' | translate }}
                        </button>
                        <button type="button" color="primary" class="ml-2"
                                [disabled]="exportingCSV"
                                mat-flat-button
                                (click)="openDialogExport()"> {{ 'common.actions.export.exportCsvButton' | translate }}
                        </button>
                    </div>
                </div>
            </header>
            <main>
                <app-kanban-board [showMultipleSelection]="false"
                                  [listColumnConfigs]="listColumnConfigs"
                                  [listAllItemsByColumn]="listAllItemsByColumn"
                                  [listItemsForColumn]="listItemsForColumn"
                                  [getAdditionalDataForColumn]="getRevenueForColumn"
                                  [selectedItem]="selectedProposal"
                                  [comparisonProperty]="'code'"
                                  [filters$]="proposalFilters$"
                                  [titleNoData]="'common.kanban.card.no-dataProposal'| translate"
                                  (openCard)="openSummary($event.item)">
                    <ng-template #kanbanCardTemplate let-item>
                        <app-proposal-kanban-card *ngIf="item"
                                                  class="flex-1 flex flex-col justify-between"
                                                  [item]="item"
                                                  (processedProposal)="sendRefreshCard($event)"
                                                  (deleteProposal)="sendDeleteCard($event)"
                                                  (searchTag)="applyNewFilter({query: $event})">
                        </app-proposal-kanban-card>
                    </ng-template>
                    <ng-template #kanbanColumnFooterTemplate let-column="column" let-isExpanded="isExpanded">
                        <div class="py-3 px-2 border-t text-center" *ngIf="isExpanded"
                             [matTooltip]="'private.training-organism.folders.toolTip.revenue' | translate">
                            <span class="font-semibold"><span
                                *ngIf="!!column?.revenue else noCA">{{ 'private.training-organism.folders.revenue.total' | translate }}
                                <span
                                    class="text-indigo">{{ +column?.revenue | number: '1.2-2':'fr-FR' }} €</span></span><ng-template
                                #noCA>-</ng-template></span>
                        </div>
                    </ng-template>
                </app-kanban-board>
            </main>
        </mat-drawer-content>
        <mat-drawer #sidenav mode="side" position="end" [autoFocus]="false" [disableClose]="true">
            <app-proposal-side #proposalSide *ngIf="selectedProposal"
                               [proposal]="selectedProposal"
                               [hasNext]="hasNext()"
                               [hasPrevious]="hasPrevious()"
                               [sidenav]="sidenav"
                               (next)="selectNextCard()"
                               (previous)="selectPreviousCard()"
                               (closeSide)="closeSide()"
                               (proposalDeleted)="sendDeleteCard($event)"
                               (proposalRefreshed)="sendRefreshCard($event)"></app-proposal-side>
            <app-proposal-create-side #proposalCreateSide *ngIf="openProposalCreateSide"
                                      (closeSide)="closeSide()"
                                      (proposalCreated)="sendInsertCard($event)"></app-proposal-create-side>
        </mat-drawer>
    </mat-drawer-container>
</div>
