import {AfterViewInit, Component, OnDestroy, OnInit, ViewChild} from '@angular/core';
import {Proposal} from '../../../../shared/api/models/proposal';
import {ProposalService} from '../../../../shared/api/services/proposal.service';
import {ReplaySubject, Subject} from 'rxjs';
import {distinct, first, switchMap, takeUntil} from 'rxjs/operators';
import {TranslateService} from '@ngx-translate/core';
import {MatSidenav} from '@angular/material/sidenav';
import {ActivatedRoute, Router} from '@angular/router';
import {
    ProposalFilters,
    ProposalQuery,
    ProposalTableComponent
} from '../../../../shared/proposal/proposal-table/proposal-table.component';
import {ProposalTableColumns} from '../../../../shared/proposal/proposal-table/proposal-table-params';
import {FormBuilder, FormGroup} from '@angular/forms';
import {CanDeactivateComponent} from '../../../../shared/utils/can-deactivate/can-deactivate.component';
import {ProposalSideComponent} from '../../../../shared/sides/proposal-side/proposal-side.component';
import {ProposalCreateSideComponent} from '../../../../shared/sides/proposal-create-side/proposal-create-side.component';
import {RegistrationFolderService} from '../../../../shared/api/services/registration-folder.service';
import {SnackBarComponent} from '../../../../shared/material/snack-bar/snack-bar.component';
import {displayTextSnackBar} from '../../../../shared/utils/displayTextSnackBar';
import {FileService} from '../../../../shared/api/services/file.service';
import {MatSnackBar} from '@angular/material/snack-bar';

interface Filters {
    query: string;
}

@Component({
    selector: 'app-training-organism-proposals',
    templateUrl: './training-organism-proposals.component.html',
    styleUrls: ['./training-organism-proposals.component.scss']
})

export class TrainingOrganismProposalsComponent extends CanDeactivateComponent implements OnDestroy, OnInit, AfterViewInit {
    openProposalCreateSide = false;
    exportingCSV = false;
    filters: FormGroup;
    loading = true;
    selectedProposal: Proposal = null;
    proposalTableDisplayedColumns: ProposalTableColumns[];
    proposalQuery$ = new Subject<ProposalQuery>();
    proposalFilters$ = new ReplaySubject<ProposalFilters>();
    proposalCount = 0;
    cpfSessionMinDate: Date = null;
    private _unsubscribeAll = new Subject<void>();
    private getProposal$ = new Subject<string>();
    activeFiltersCount = 0;
    queryConstructor: string;
    showTableView = true;

    @ViewChild('sidenav') sidenav: MatSidenav;
    @ViewChild(ProposalTableComponent) proposalTableComponent: ProposalTableComponent;
    @ViewChild('proposalSide') private proposalSide: ProposalSideComponent;
    @ViewChild('proposalCreateSide') private proposalCreateSide: ProposalCreateSideComponent;

    constructor(
        private _proposalService: ProposalService,
        private _translateService: TranslateService,
        private _router: Router,
        private _fileService: FileService,
        private _snackBar: MatSnackBar,
        private _registrationFolderService: RegistrationFolderService,
        private _activatedRoute: ActivatedRoute,
        private _formBuilder: FormBuilder
    ) {
        super();
    }

    ngOnInit(): void {
        this.filters = this._formBuilder.group({
            query: [undefined]
        });
        this.proposalTableDisplayedColumns = [
            ProposalTableColumns.ACTIONS,
            ProposalTableColumns.CODE,
            ProposalTableColumns.STATE,
            ProposalTableColumns.TRAINING_ACTIONS,
            ProposalTableColumns.CONDITIONS,
            ProposalTableColumns.TAGS
        ];

        this._registrationFolderService.sessionMinDates$.pipe(first()).subscribe((sessionMinDates) => {
            this.cpfSessionMinDate = new Date(sessionMinDates.cpfSessionMinDate);
        });

        this.getProposal$.pipe(
            switchMap((code) => this._proposalService.get(code)),
            takeUntil(this._unsubscribeAll)
        ).subscribe(selectedProposal => {
            if (!this.proposalTableComponent.selectedRow) {
                this.proposalTableComponent.selectedRow = selectedProposal;
            }
            this.selectedProposal = selectedProposal;
            this.sidenav.open();
        });

        this.filters.valueChanges.pipe(
            distinct(),
            takeUntil(this._unsubscribeAll)
        ).subscribe((proposalFilters: Filters) => {
            if (this.filters.valid) {
                this.proposalFilters$.next({
                    query: proposalFilters?.query,
                });
                this.queryConstructor = proposalFilters['query'];
                this.activeFiltersCount = Object.keys(proposalFilters).filter(key => proposalFilters[key]).length;
            }
        });
        this.loading = false;
    }

    ngOnDestroy(): void {
        this.proposalFilters$.complete();
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    openDialogExport(): void {
        this.exportingCSV = true;
        const queryParams: { [param: string]: string } = {
            format: 'csv',
            ...this.proposalTableComponent.activeFilters
        };
        if (queryParams.siret == null) {
            queryParams.siret = 'all';
        }
        this._fileService.download('/api/' + 'proposals', 'Propositions', null, queryParams).subscribe(
            (blob) => {
                if (blob.state === 'PENDING') {
                    this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(
                        this._translateService.instant('common.actions.export.exportStarted'), 0));
                } else if (blob.state === 'DONE') {
                    this.exportingCSV = false;
                    this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(
                        this._translateService.instant('common.actions.export.exportFinished')));
                }
            }
        );
    }

    next(): void {
        this.proposalQuery$.next({
            code: this.selectedProposal.code,
            next: true,
        });
    }

    previous(): void {
        this.proposalQuery$.next({
            code: this.selectedProposal.code,
            previous: true,
        });
    }

    closeSide(): void {
        this._router.navigate(['..'], {relativeTo: this._activatedRoute}).then((success) => {
            if (success) {
                this.selectedProposal = null;
            }
        });
    }

    openSummary(proposal: Proposal): void {
        this._router.navigate(['..', proposal.code], {relativeTo: this._activatedRoute}).then((success) => {
            if (success) {
                this.selectedProposal = proposal;
            }
        });
    }

    openCreate(): void {
        this._router.navigate(['..', 'creer'], {relativeTo: this._activatedRoute}).then((success) => {
            if (success) {
                this.selectedProposal = null;
            }
        });
    }

    clearFilter(formControlName: string): void {
        this.filters.get(formControlName).setValue(null);
    }

    sendRefreshRow($event: Proposal): void {
        this.proposalTableComponent.refreshRow($event);
    }

    sendDeleteRow(proposal: Proposal): void {
        this.proposalTableComponent.deleteRow(proposal);
        this.proposalCount -= 1;
        this.proposalTableComponent.forceRefresh();
        if (this.selectedProposal && this.selectedProposal.code === proposal.code) {
            this.closeSide();
        }
    }

    sendInsertRow($newRow: Proposal): void {
        this.proposalTableComponent.insertRow($newRow);
        this.proposalCount += 1;
    }

    applyNewFilter(proposalFilters: ProposalFilters): void {
        this.filters.get('query').setValue(proposalFilters.query);
    }

    ngAfterViewInit(): void {
        this._activatedRoute.params.pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe((params) => {
            const {code} = params;
            if (code && code !== 'creer') {
                this.openProposalCreateSide = false;
                if (this.selectedProposal?.code !== code) {
                    this.getProposal$.next(code);
                } else {
                    this.sidenav.open();
                }
            } else if (code === 'creer') {
                if (this.proposalTableComponent) {
                    this.proposalTableComponent.clearSelectedRow();
                    this.selectedProposal = null;
                    this.openProposalCreateSide = true;
                    setTimeout(() => {
                        this.sidenav.open();
                    });
                } else {
                    this._router.navigate(['formation/propositions']);
                }
            } else {
                this.proposalTableComponent?.clearSelectedRow();
                this.openProposalCreateSide = false;
                this.selectedProposal = null;
                if (this.sidenav?.opened) {
                    this.sidenav.close();
                }
            }
        });
    }

    canDeactivate(): boolean {
        if (this.proposalCreateSide && this.openProposalCreateSide) {
            return this.proposalCreateSide.canDeactivate();
        } else if (this.proposalSide && this.selectedProposal) {
            return this.proposalSide.canDeactivate();
        } else {
            return true;
        }
    }

    setShowKanbanView(): void {
        this.showTableView = false;
        this.selectedProposal = null;
        this.openProposalCreateSide = false;
        if (this.sidenav?.opened) {
            this.sidenav.close();
        }
        this._router.navigate(['/', 'formation', 'propositions', 'kanban'], { // todo
            queryParams: this._activatedRoute.snapshot.queryParams
        });
    }
}
