<div class="content-layout fullwidth-standard-inner-scroll">
    <mat-drawer-container>
        <mat-drawer-content>
            <header class="flex flex-row justify-between pt-4 px-4 bg-white"
                    *ngIf="!loading">
                <div>
                    <h1>{{ 'private.training-organism.proposals.title' | translate }}</h1>
                    <p class="subtitle">
                        {{ 'private.training-organism.proposals.subtitle' | translate }}
                        <br/>{{ 'private.training-organism.common.minDateCpf' | translate }}
                        <b>{{ cpfSessionMinDate | dateZToDayString }}</b>
                    </p>
                    <app-tag class="font-semibold flex-auto mt-2 mb-2" color="#f2e5f2">
                        {{ 'private.training-organism.proposals.subtitleCount' | translate : {proposalCount: proposalCount} }}
                    </app-tag>
                    <div class="mt-2">
                        <button (click)="setShowKanbanView()" mat-icon-button
                                *ngIf="showTableView"
                                [color]="'accent'">
                            <mat-icon>view_kanban</mat-icon>
                        </button>
                        <mat-icon color="primary" svgIcon="table_rows"></mat-icon>
                    </div>
                </div>
                <div class="py-4 pl-4 mr-8">
                    <mat-form-field class="w-90" [formGroup]="filters">
                        <input [title]="'private.training-organism.proposals.searchbar' | translate" matInput
                               formControlName="query" [value]="queryConstructor"
                               [placeholder]="'common.table.filters.globalSearch' | translate" maxlength="50">
                        <!--<mat-icon matPrefix svgIcon="search"></mat-icon>-->
                        <button mat-button *ngIf="activeFiltersCount > 0" matSuffix mat-icon-button
                                aria-label="Clear"
                                (click)="clearFilter('query')">
                            <mat-icon svgIcon="close"></mat-icon>
                        </button>
                        <!--<mat-icon
                            [matBadge]="activeFiltersCount ? activeFiltersCount+'' : null"
                            matBadgePosition="below after"
                            matBadgeSize="small"
                            [color]="activeFiltersCount ? 'primary' : undefined"
                            [svgIcon]="activeFiltersCount ? 'filter_alt' : 'manage_search'"></mat-icon>-->
                    </mat-form-field>
                    <div class="flex items-center justify-end">
                        <button type="button" (click)="openCreate()"
                                [disabled]="openProposalCreateSide"
                                color="primary"
                                mat-flat-button>{{ 'private.training-organism.proposals.button' | translate }}
                        </button>
                        <button type="button" color="primary" class="ml-2"
                                mat-flat-button
                                [disabled]="exportingCSV"
                                (click)="openDialogExport()">{{ 'common.actions.export.exportCsvButton' | translate }}
                        </button>
                    </div>
                </div>
            </header>
            <main>
                <mat-progress-spinner class="m-auto" *ngIf="loading" [diameter]="75"
                                      mode="indeterminate"></mat-progress-spinner>

                <app-proposal-table *ngIf="!loading"
                                    [dataQuery$]="proposalQuery$"
                                    [proposalFilters$]="proposalFilters$"
                                    [selectedRow]="selectedProposal"
                                    [externalSelectionChange]="true"
                                    (newProposalFilters)="applyNewFilter($event)"
                                    (proposalDeleted)="sendDeleteRow($event)"
                                    (totalRows)="proposalCount = $event"
                                    (followingRow)="openSummary($event.row)"
                                    [displayedColumns]="proposalTableDisplayedColumns"></app-proposal-table>
            </main>
        </mat-drawer-content>
        <mat-drawer #sidenav mode="side" position="end" [autoFocus]="false" [disableClose]="true"
                    *ngIf="!loading">
            <app-proposal-side #proposalSide *ngIf="selectedProposal"
                               [proposal]="selectedProposal"
                               [hasNext]="proposalTableComponent?.hasNext(selectedProposal, 'code')"
                               [sidenav]="sidenav"
                               [hasPrevious]="proposalTableComponent?.hasPrevious(selectedProposal, 'code')"
                               (next)="next()"
                               (previous)="previous()"
                               (closeSide)="closeSide()"
                               (proposalDeleted)="sendDeleteRow($event)"
                               (proposalRefreshed)="sendRefreshRow($event)"></app-proposal-side>
            <app-proposal-create-side #proposalCreateSide *ngIf="openProposalCreateSide"
                                      (closeSide)="closeSide()"
                                      (proposalCreated)="sendInsertRow($event)"></app-proposal-create-side>
        </mat-drawer>
    </mat-drawer-container>
</div>
