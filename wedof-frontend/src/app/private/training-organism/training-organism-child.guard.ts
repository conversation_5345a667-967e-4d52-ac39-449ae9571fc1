import {Injectable} from '@angular/core';
import {ActivatedRouteSnapshot, CanActivateChild, Router, RouterStateSnapshot, UrlTree} from '@angular/router';
import {Observable} from 'rxjs';
import {Subscription, SubscriptionTrainingTypes} from '../../shared/api/models/subscription';
import {first, map} from 'rxjs/operators';
import {Select} from '@ngxs/store';
import {SubscriptionState} from '../../shared/api/state/subscription.state';

@Injectable({
    providedIn: 'root'
})
export class TrainingOrganismChildGuard implements CanActivateChild {

    @Select(SubscriptionState.subscription) subscription$: Observable<Subscription>;

    constructor(private _router: Router) {
    }

    canActivateChild(
        route: ActivatedRouteSnapshot,
        state: RouterStateSnapshot): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
        return this.subscription$.pipe(
            first(),
            map(subscription => {
                const canActivate = subscription.trainingType !== SubscriptionTrainingTypes.NONE;
                if (!canActivate) {
                    switch (route.url[0]?.path || route.routeConfig.children[0]?.path) {
                        case 'propositions':
                            this._router.navigate(['formation/propositions/souscription']);
                            break;
                        case 'statistiques':
                            this._router.navigate(['formation/statistiques/souscription']);
                            break;
                        case 'dossiers':
                        case 'kanban':
                            this._router.navigate(['formation/dossiers/souscription']);
                            break;
                    }
                }
                return canActivate;
            })
        );
    }

}
