import {NgModule} from '@angular/core';
import {ReactiveFormsModule} from '@angular/forms';

import {InvitationModule} from '../../shared/invitation/invitation.module';
import {SharedModule} from '../../shared/shared.module';
import {OrganismModule} from '../../shared/organism/organism.module';
import {TrainingOrganismRoutingModule} from './training-organism-routing.module';
import {TrainingOrganismStatsComponent} from './stats/training-organism-stats.component';
import {TrainingOrganismProposalsComponent} from './proposals/proposals/training-organism-proposals.component';
import {ClipboardModule} from '@angular/cdk/clipboard';
import {MatTooltipModule} from '@angular/material/tooltip';
import {MatProgressBarModule} from '@angular/material/progress-bar';
import {MatChipsModule} from '@angular/material/chips';
import {MatTabsModule} from '@angular/material/tabs';
import {AttendeeModule} from '../../shared/attendee/attendee.module';
import {RegistrationFoldersViewBySessionComponent} from './registration-folders/registration-folders-view-by-session/registration-folders-view-by-session.component';
import {RegistrationFoldersViewByRegistrationFoldersComponent} from './registration-folders/registration-folders-view-by-registration-folders/registration-folders-view-by-registration-folders.component';
import {SubscriptionModule} from '../../shared/subscription/subscription.module';
import {MdePopoverModule} from '@material-extended/mde';
import {KanbanRegistrationFoldersComponent} from './registration-folders/kanban-registration-folders/kanban-registration-folders.component';
import {KanbanModule} from '../../shared/kanban-board/kanban.module';
import {KanbanProposalsComponent} from './proposals/kanban-proposals-component/kanban-proposals.component';
import {RegisterTrainingFolderComponent} from './register-training-folder/register-training-folder.component';
import {RegisterProposalsComponent} from './register-proposals/register-proposals.component';
import {RegisterStatsComponent} from './register-stats/register-stats.component';
import {TrainingOrganismCertificationsComponent} from './certifications/training-organism-certifications.component';
import {InfiniteScrollModule} from 'ngx-infinite-scroll';
import {ConfigureProposalComponent} from './configure-proposal/configure-proposal.component';

@NgModule({
    declarations: [
        TrainingOrganismCertificationsComponent,
        RegistrationFoldersViewByRegistrationFoldersComponent,
        TrainingOrganismStatsComponent,
        RegistrationFoldersViewBySessionComponent,
        TrainingOrganismProposalsComponent,
        KanbanRegistrationFoldersComponent,
        KanbanProposalsComponent,
        RegisterTrainingFolderComponent,
        RegisterProposalsComponent,
        RegisterStatsComponent,
        ConfigureProposalComponent
    ],
    imports: [
        TrainingOrganismRoutingModule,
        SharedModule,
        OrganismModule,
        InvitationModule,
        ReactiveFormsModule,
        ClipboardModule,
        MatTooltipModule,
        MatProgressBarModule,
        MatChipsModule,
        AttendeeModule,
        MatTabsModule,
        SubscriptionModule,
        MdePopoverModule,
        KanbanModule,
        InfiniteScrollModule
    ]
})
export class TrainingOrganismModule {
}
