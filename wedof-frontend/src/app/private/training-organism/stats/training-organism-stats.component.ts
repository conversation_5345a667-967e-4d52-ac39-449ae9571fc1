import {AfterViewInit, Component, OnD<PERSON>roy, OnInit} from '@angular/core';
import {forkJoin, Observable, of, ReplaySubject, Subject} from 'rxjs';
import {debounceTime, filter, switchMap, takeUntil, tap} from 'rxjs/operators';
import {StatisticService} from '../../../shared/api/services/statistic.service';
import {FormBuilder, FormControl, FormGroup, Validators} from '@angular/forms';
import {TrainingService} from '../../../shared/api/services/training.service';
import {Training} from '../../../shared/api/models/training';
import moment from 'moment';
import {DataVisualisationWithVariation, IndicatorData} from '../../../shared/chart/indicator-data';
import {RegistrationFolderBillingStates, RegistrationFolderStates} from '../../../shared/api/models/registration-folder';
import {Subscription} from 'app/shared/api/models/subscription';
import {Select} from '@ngxs/store';
import {SubscriptionState} from '../../../shared/api/state/subscription.state';

@Component({
    selector: 'app-training-organism-stats',
    templateUrl: './training-organism-stats.component.html',
    styleUrls: ['./training-organism-stats.component.scss']
})
export class TrainingOrganismStatsComponent implements OnDestroy, OnInit, AfterViewInit {

    @Select(SubscriptionState.subscription) subscription$: Observable<Subscription>;

    trainingsFilteringCtrl: FormControl = new FormControl();
    filteredTrainings: ReplaySubject<Training[]> = new ReplaySubject<Training[]>(1);
    searching = false;
    allowAnalytics: boolean;

    foldersData: IndicatorData;
    attendeeData: IndicatorData;
    foldersInTrainingData: IndicatorData;
    foldersCanceledData: IndicatorData;
    foldersAcceptedData: IndicatorData;
    foldersValidatedData: IndicatorData;
    foldersRevenuesData: IndicatorData;
    foldersBilledData: IndicatorData;

    private _unsubscribeAll = new Subject<void>();
    form: FormGroup;

    constructor(
        private _formBuilder: FormBuilder,
        private _statisticService: StatisticService,
        private _trainingService: TrainingService) {

        this.foldersData = {amount: 0, values: [], labels: [], variation: 0};
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    ngOnInit(): void {
        this.subscription$.pipe(takeUntil(this._unsubscribeAll)).subscribe((subscription: Subscription) =>
            this.allowAnalytics = subscription?.allowAnalytics
        );
        const to = moment().utcOffset(0).set({hour: 0, minute: 0, second: 0, millisecond: 0});
        const from = moment(to).add({month: -1, day: 1});
        this.form = this._formBuilder.group({
            from: [from, Validators.required],
            to: [to, Validators.required],
            training: [''],
        });
    }

    ngAfterViewInit(): void {
        if (this.allowAnalytics) {
            this.trainingsFilteringCtrl.valueChanges
                .pipe(
                    filter(search => !!search),
                    tap(() => this.searching = true),
                    takeUntil(this._unsubscribeAll),
                    debounceTime(200),
                    switchMap(search => {
                        return this._trainingService.list({query: search});
                    }),
                    takeUntil(this._unsubscribeAll)
                )
                .subscribe(filteredTrainings => {
                    this.searching = false;
                    this.filteredTrainings.next(filteredTrainings.payload);
                });

            this.form.valueChanges
                .pipe(
                    takeUntil(this._unsubscribeAll),
                    debounceTime(250),
                    switchMap(() => {
                        if (this.form.valid) {
                            this.form.disable({emitEvent: false});
                            const selectedTraining = this.form.get('training').value && this.form.get('training').value !== '' ? this.form.get('training').value : null;
                            return this.applyData(selectedTraining);
                        } else {
                            return of({});
                        }
                    }),
                    takeUntil(this._unsubscribeAll)
                ).subscribe(() => {
                this.form.enable({emitEvent: false});
            });

            // to init search
            setTimeout(() => {
                this.trainingsFilteringCtrl.patchValue(' ');
                this.trainingsFilteringCtrl.patchValue('');
                this.form.updateValueAndValidity({onlySelf: false, emitEvent: true}); // first load
            });
        }
    }

    applyData(training: Training): Observable<IndicatorData[]> {
        return forkJoin([
            // get folders
            this._statisticService
                .dataRegistrationFoldersGroupByDates(this.form.get('from').value, this.form.get('to').value, [RegistrationFolderStates.TERMINATED], training)
                .pipe(
                    switchMap((returnedData: DataVisualisationWithVariation) => {
                        const attendeeData = {amount: 0, values: [], labels: [], variation: returnedData.variation};
                        returnedData.data.forEach((single) => {
                            attendeeData.labels.push(single.label);
                            attendeeData.values.push(single.value);
                            attendeeData.amount += single.value; // only the last one will be keep;
                        });
                        return of(attendeeData);
                    }),
                    tap(attendeeData => {
                        this.attendeeData = attendeeData;
                    })
                ),
            this._statisticService
                .dataRegistrationFoldersGroupByDates(this.form.get('from').value, this.form.get('to').value, [RegistrationFolderStates.ACCEPTED], training)
                .pipe(
                    switchMap((returnedData: DataVisualisationWithVariation) => {
                        const foldersAcceptedData = {
                            amount: 0,
                            values: [],
                            labels: [],
                            variation: returnedData.variation
                        };
                        returnedData.data.forEach((single) => {
                            foldersAcceptedData.labels.push(single.label);
                            foldersAcceptedData.values.push(single.value);
                            foldersAcceptedData.amount += single.value; // only the last one will be keep;
                        });
                        return of(foldersAcceptedData);
                    }),
                    tap(foldersAcceptedData => {
                        this.foldersAcceptedData = foldersAcceptedData;
                    })
                ),
            this._statisticService
                .dataRegistrationFoldersGroupByDates(this.form.get('from').value, this.form.get('to').value, [RegistrationFolderStates.VALIDATED], training)
                .pipe(
                    switchMap((returnedData: DataVisualisationWithVariation) => {
                        const foldersValidatedData = {
                            amount: 0,
                            values: [],
                            labels: [],
                            variation: returnedData.variation
                        };
                        returnedData.data.forEach((single) => {
                            foldersValidatedData.labels.push(single.label);
                            foldersValidatedData.values.push(single.value);
                            foldersValidatedData.amount += single.value; // only the last one will be keep;
                        });
                        return of(foldersValidatedData);
                    }),
                    tap(foldersValidatedData => {
                        this.foldersValidatedData = foldersValidatedData;
                    })
                ),
            this._statisticService
                .dataRegistrationFoldersGroupByDates(this.form.get('from').value, this.form.get('to').value, [RegistrationFolderStates.IN_TRAINING], training)
                .pipe(
                    switchMap((returnedData: DataVisualisationWithVariation) => {
                        const foldersInTrainingData = {
                            amount: 0,
                            values: [],
                            labels: [],
                            variation: returnedData.variation
                        };
                        returnedData.data.forEach((single) => {
                            foldersInTrainingData.labels.push(single.label);
                            foldersInTrainingData.values.push(single.value);
                            foldersInTrainingData.amount += single.value; // only the last one will be keep;
                        });
                        return of(foldersInTrainingData);
                    }),
                    tap(foldersInTrainingData => {
                        this.foldersInTrainingData = foldersInTrainingData;
                    })
                ),
            this._statisticService
                .dataRegistrationFoldersGroupByDates(this.form.get('from').value, this.form.get('to').value,
                    [RegistrationFolderStates.CANCELED_BY_ATTENDEE_NOT_REALIZED, RegistrationFolderStates.CANCELED_BY_ATTENDEE, RegistrationFolderStates.CANCELED_BY_ORGANISM],
                    training)
                .pipe(
                    switchMap((returnedData: DataVisualisationWithVariation) => {
                        const foldersCanceledData = {
                            amount: 0,
                            values: [],
                            labels: [],
                            variation: returnedData.variation
                        };
                        returnedData.data.forEach((single) => {
                            foldersCanceledData.labels.push(single.label);
                            foldersCanceledData.values.push(single.value);
                            foldersCanceledData.amount += single.value; // only the last one will be keep;
                        });
                        return of(foldersCanceledData);
                    }),
                    tap(foldersCanceledData => {
                        this.foldersCanceledData = foldersCanceledData;
                    })
                ),
            this._statisticService
                .dataRegistrationFoldersGroupByDates(this.form.get('from').value, this.form.get('to').value,
                    [RegistrationFolderStates.ACCEPTED],
                    training,
                    [],
                    'sum',
                    'trainingActionInfo.totalExcl')
                .pipe(
                    switchMap((returnedData: DataVisualisationWithVariation) => {
                        const foldersRevenuesData = {
                            amount: 0,
                            values: [],
                            labels: [],
                            variation: returnedData.variation
                        };
                        returnedData.data.forEach((single) => {
                            foldersRevenuesData.labels.push(single.label);
                            foldersRevenuesData.values.push(single.value);
                            foldersRevenuesData.amount += single.value; // only the last one will be keep;
                        });
                        return of(foldersRevenuesData);
                    }),
                    tap(foldersRevenuesData => {
                        this.foldersRevenuesData = foldersRevenuesData;
                    })
                ),
            this._statisticService
                .dataRegistrationFoldersGroupByDates(this.form.get('from').value, this.form.get('to').value,
                    [],
                    training,
                    [RegistrationFolderBillingStates.BILLED, RegistrationFolderBillingStates.PAID],
                    'sum',
                    'amountToInvoice')
                .pipe(
                    switchMap((returnedData: DataVisualisationWithVariation) => {
                        const foldersBilledData = {
                            amount: 0,
                            values: [],
                            labels: [],
                            variation: returnedData.variation
                        };
                        returnedData.data.forEach((single) => {
                            foldersBilledData.labels.push(single.label);
                            foldersBilledData.values.push(single.value);
                            foldersBilledData.amount += single.value; // only the last one will be keep;
                        });
                        return of(foldersBilledData);
                    }),
                    tap(foldersBilledData => {
                        this.foldersBilledData = foldersBilledData;
                    })
                )
        ]);
    }
}
