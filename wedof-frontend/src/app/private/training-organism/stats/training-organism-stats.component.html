<div class="content-layout fullwidth-standard-inner-scroll">
    <div class="main">
        <div class="content flex flex-wrap">
            <div class="flex flex-wrap w-full">
                <div class="flex flex-auto w-1/2 min-w-80 gt-xs:pr-4 justify-between">
                    <h1>
                        {{ "private.training-organism.stats.title" | translate }}
                    </h1>
                </div>
                <form [formGroup]="form" class="flex flex-auto w-1/2 min-w-80 gt-xs:pl-4">
                    <button *ngIf="form.disabled" [disabled]="true" class="mr-2" mat-icon-button>
                        <mat-progress-spinner [diameter]="24"
                                              mode="indeterminate"></mat-progress-spinner>
                    </button>
                    <mat-form-field class="mr-2 align-middle">
                        <mat-label>
                            {{ 'private.training-organism.stats.select.label'|translate }}
                        </mat-label>
                        <mat-select [formControl]="form.get('training')"
                                    [placeholder]="'private.training-organism.stats.select.default' | translate">
                            <mat-option>
                                <ngx-mat-select-search
                                    [formControl]="trainingsFilteringCtrl"
                                    [indexAndLengthScreenReaderText]="' sur '"
                                    [noEntriesFoundLabel]="'private.training-organism.stats.select.noEntry' | translate"
                                    [placeholderLabel]="'private.training-organism.stats.select.placeholder' | translate"
                                    [searching]="searching"></ngx-mat-select-search>
                            </mat-option>
                            <mat-option
                                value="">{{ 'private.training-organism.stats.select.default'|translate }}
                            </mat-option>
                            <mat-option
                                *ngFor="let training of filteredTrainings | async"
                                [value]="training">
                                {{ training.externalId | displayCatalogExternalId }} - {{ training.title }}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>
                    <mat-form-field class="align-middle">
                        <mat-label>{{ 'private.training-organism.stats.period.label' | translate }}</mat-label>
                        <mat-date-range-input [rangePicker]="picker">
                            <input formControlName="from" matStartDate
                                   placeholder="{{'private.training-organism.stats.period.start' | translate}}">
                            <input formControlName="to" matEndDate
                                   placeholder="{{'private.training-organism.stats.period.end' | translate}}">
                        </mat-date-range-input>
                        <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                        <mat-date-range-picker #picker></mat-date-range-picker>
                    </mat-form-field>
                </form>
            </div>
        </div>
        <mat-tab-group [selectedIndex]="1">
            <mat-tab label="Financière">
                <ng-template matTabContent>
                    <div class="flex flex-wrap w-full">
                        <div class="flex flex-auto w-1/3 min-w-80 h-50 p-4">
                            <app-indicator
                                [data]="foldersValidatedData"
                                [subTitle]="''"
                                [title]="'Dossiers validés'"
                                class="bg-card shadow-md rounded overflow-hidden">
                            </app-indicator>
                        </div>
                        <div class="flex flex-auto w-1/3 min-w-80 h-50 p-4">
                            <app-indicator
                                [data]="foldersAcceptedData"
                                [subTitle]="''"
                                [title]="'Dossiers acceptés'"
                                class="bg-card shadow-md rounded overflow-hidden">
                            </app-indicator>
                        </div>
                        <div class="flex flex-auto w-1/3 min-w-80 h-50 p-4">
                            <app-indicator
                                [data]="foldersCanceledData"
                                [subTitle]="'Par l\'apprenant ou l\'organisme'"
                                [title]="'Dossiers annulés'"
                                class="bg-card shadow-md rounded overflow-hidden">
                            </app-indicator>
                        </div>
                        <div class="flex flex-auto w-1/3 min-w-80 h-50 p-4">
                            <app-indicator
                                [data]="foldersInTrainingData"
                                [subTitle]="'Apprenants entrés en formation'"
                                [title]="'En formation'"
                                class="bg-card shadow-md rounded overflow-hidden">
                            </app-indicator>
                        </div>
                        <div class="flex flex-auto w-1/3 min-w-80 h-50 p-4">
                            <app-indicator
                                [data]="attendeeData"
                                [subTitle]="'Personnes sorties de formation'"
                                [title]="'Personnes formées'"
                                class="bg-card shadow-md rounded overflow-hidden">
                            </app-indicator>
                        </div>
                        <div class="flex flex-auto w-1/3 min-w-80 h-50 p-4">
                            <app-indicator
                                [data]="foldersRevenuesData"
                                [subTitle]="'À partir des dossiers acceptés sur la période'"
                                [title]="'Chiffre d\'affaires'"
                                class="bg-card shadow-md rounded overflow-hidden">
                            </app-indicator>
                        </div>
                        <div class="flex flex-auto w-1/3 min-w-80 h-50 p-4">
                            <app-indicator
                                [data]="foldersBilledData"
                                [subTitle]="'Montant total facturé sur la période (payé ou pas)'"
                                [title]="'Montant facturé'"
                                class="bg-card shadow-md rounded overflow-hidden">
                            </app-indicator>
                        </div>
                    </div>
                </ng-template>
            </mat-tab>
        </mat-tab-group>
    </div>
</div>
