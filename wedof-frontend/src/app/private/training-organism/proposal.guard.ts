import {Injectable} from '@angular/core';
import {CanActivate, Router, UrlTree} from '@angular/router';
import {combineLatest, Observable} from 'rxjs';
import {first, map} from 'rxjs/operators';
import {OrganismService} from '../../shared/api/services/organism.service';
import {Select} from '@ngxs/store';
import {SubscriptionState} from '../../shared/api/state/subscription.state';
import {Subscription} from '../../shared/api/models/subscription';

@Injectable({
    providedIn: 'root'
})
export class ProposalGuard implements CanActivate {

    @Select(SubscriptionState.subscription) subscription$: Observable<Subscription>;

    constructor(private _organismService: OrganismService, private _router: Router) {
    }

    canActivate(): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
        return combineLatest([
            this.subscription$
        ]).pipe(
            first(),
            map(([subscription]) => {
                const canActivate = subscription?.allowProposals;
                if (!canActivate) {
                    this._router.navigate(['formation/propositions/souscription']);
                }
                return canActivate;
            })
        );
    }
}
