import {
    AfterView<PERSON>nit,
    Component,
    ElementRef,
    OnDestroy,
    OnInit,
    QueryList,
    ViewChild,
    ViewChildren
} from '@angular/core';
import {MatDialog} from '@angular/material/dialog';
import {MatSidenav} from '@angular/material/sidenav';
import {ActivatedRoute, Params, Router} from '@angular/router';
import {debounceTime, distinctUntilChanged, switchMap, takeUntil} from 'rxjs/operators';
import {CertifierAccessService} from '../../../shared/api/services/certifier-access.service';
import {TranslateService} from '@ngx-translate/core';
import {Certification, CertificationTypes} from '../../../shared/api/models/certification';
import {CertificationService} from '../../../shared/api/services/certification.service';
import {FormControl} from '@angular/forms';
import {combineLatest, Observable, of, ReplaySubject, Subject} from 'rxjs';
import {WrapperSpinnerComponent} from '../../../shared/material/wrapper-spinner/wrapper-spinner.component';
import {CertificationCatalogCardComponent} from '../../../shared/certification/certification-catalog-card/certification-catalog-card.component';
import {CertifierAccess, CertifierAccessState} from '../../../shared/api/models/certifier-access';
import {find, groupBy, mapValues} from 'lodash-es';
import {AbstractCertificationAccessManagement} from '../../../shared/certification/abstract-certification-access-management/abstract-certification-access-management';
import {MatSnackBar} from '@angular/material/snack-bar';
import {displayTextSnackBar} from '../../../shared/utils/displayTextSnackBar';
import {SnackBarComponent} from '../../../shared/material/snack-bar/snack-bar.component';
import {CertificationPartner} from '../../../shared/api/models/certification-partner';
import {Select, Store} from '@ngxs/store';
import {OrganismState} from '../../../shared/api/state/organism.state';
import {Organism} from '../../../shared/api/models/organism';
import {EntityClass} from '../../../shared/utils/enums/entity-class';
import {Filter} from '../../../shared/api/models/user';
import {ShortcutCatalogCertificationSidePanelEnum} from '../../../shared/utils/enums/shortcut-side-panel-enum';
import {Location} from '@angular/common';
import {getAnchor} from '../../../shared/utils/shortcut-side-panel-utils';
import {Connection, ConnectionState, DataProviders} from '../../../shared/api/models/connection';
import {ConnectionsState, FetchConnections} from '../../../shared/api/state/connections.state';
import {DialogConnectionAuthComponent} from '../../../shared/connection/dialog-connection-auth/dialog-connection-auth.component';
import {SubscriptionState} from '../../../shared/api/state/subscription.state';
import {Subscription} from '../../../shared/api/models/subscription';

@Component({
    selector: 'app-training-organism-certifications',
    templateUrl: './training-organism-certifications.component.html',
    styleUrls: ['./training-organism-certifications.component.scss']
})
export class TrainingOrganismCertificationsComponent extends AbstractCertificationAccessManagement implements OnInit, AfterViewInit, OnDestroy {

    @ViewChild('sidenav') sidenav: MatSidenav;
    @ViewChild('scrollContainer') scrollContainer: WrapperSpinnerComponent;
    @ViewChild('catalogBlock') catalogBlock: ElementRef;
    @ViewChildren('cards') cards: QueryList<CertificationCatalogCardComponent>;

    @Select(OrganismState.organism) organism$: Observable<Organism>;
    @Select(ConnectionsState.connections) connections$: Observable<Connection[]>;
    @Select(SubscriptionState.subscription) subscription$: Observable<Subscription>;

    subscription: Subscription;
    connectionCpf: Connection;
    queryParams$: ReplaySubject<Params> = new ReplaySubject<Params>();
    isLoading = true;
    queryCtrl: FormControl = new FormControl();
    certifications: Certification[] = [];
    total = 0;
    currentPage = 1;
    itemsPerPage = 20;
    selectedCertification: Certification = null;
    selectedCertificationIndex: number = null;
    certifierAccessByCertifier: { [siret: string]: CertifierAccess } = {};
    activeFiltersCount = 0;
    isActiveFiltersCount = false;
    organism: Organism;
    certifInfo: string;
    filteredFilters: Filter[] = [
        {
            'link': 'isPromoted=true',
            'name': '⭐️ Recommandés',
            'color': '#77a640',
            'entityClass': EntityClass.CERTIFICATIONS
        },
        {
            'link': 'state=draft,refused,processing',
            'name': '✉ Mes demandes️',
            'color': '#326fe3',
            'entityClass': EntityClass.CERTIFICATIONS
        },
        {
            'link': 'state=active',
            'name': '🟢 Partenariats actifs',
            'color': '#89878f',
            'entityClass': EntityClass.CERTIFICATIONS
        },
        {
            'link': 'state=suspended',
            'name': '🟠 Partenariats suspendus',
            'color': '#89878f',
            'entityClass': EntityClass.CERTIFICATIONS
        },
        {
            'link': 'state=revoked',
            'name': '🔴 Partenariats revoqués',
            'color': '#89878f',
            'entityClass': EntityClass.CERTIFICATIONS
        },
        {
            name: this._translateService.instant('private.common.filters.noFilter'),
            link: '',
            entityClass: EntityClass.CERTIFICATIONS
        },
    ];

    private _unsubscribeAll: Subject<void> = new Subject<void>();

    constructor(
        private _activatedRoute: ActivatedRoute,
        private _router: Router,
        protected _dialog: MatDialog,
        private _store: Store,
        private _translateService: TranslateService,
        private _certificationService: CertificationService,
        protected _certifierAccessService: CertifierAccessService,
        private _snackBar: MatSnackBar,
        private _location: Location
    ) {
        super(_dialog, _certifierAccessService);
    }

    ngOnInit(): void {
        combineLatest([this.organism$, this.connections$, this.subscription$]).pipe(takeUntil(this._unsubscribeAll)).subscribe(([organism, connections, subscription]) => {
            this.organism = organism;
            this.connectionCpf = connections.find((connection) => connection.dataProvider === DataProviders.CPF);
            this.subscription = subscription;
            if (organism.isCertifierOrganism) {
                this.filteredFilters.splice(1, 0, {
                    'link': 'defaultCertifier=' + this.organism.siret,
                    'name': '🏅 Mes certifications',
                    'color': '#b7b0ef',
                    'entityClass': EntityClass.CERTIFICATIONS
                });
            }
        });

        // This will not work if the OF is the partner of more than 100 certifiers, as the default page size is 100
        this._certifierAccessService.find({siret: this.organism.siret, accessType: 'partner'}).pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe((response) => {
            const certifierAccesses = response.payload;
            this.certifierAccessByCertifier = mapValues(
                groupBy(certifierAccesses, (certifierAccess) => certifierAccess._links.certifier.siret),
                ([certifierAccess]) => certifierAccess
            );
            const certifierAccessId = this._activatedRoute.snapshot.queryParamMap.get('certifierAccess');
            if (certifierAccessId) {
                const certifierAccess = find(certifierAccesses, currentCertifierAccess => currentCertifierAccess.id.toString() === certifierAccessId);
                if (certifierAccess) {
                    this.openDialogCertifierAccess(certifierAccess, certifierAccessId);
                } else {
                    const siret = this.organism.siret;
                    this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(
                        this._translateService.instant('private.certification.partners.table.wrongInvitation', {siret}), 5000, 'red'));
                }
            } else if (Object.keys(this.certifierAccessByCertifier).length) {
                Object.values(this.certifierAccessByCertifier).forEach((certifierAccess) => {
                    if (certifierAccess.state === CertifierAccessState.WAITING) {
                        this.openDialogCertifierAccess(certifierAccess);
                    }
                });
            }
        });

        this.queryCtrl.valueChanges.pipe(
            distinctUntilChanged(),
            debounceTime(500),
            takeUntil(this._unsubscribeAll),
        ).subscribe(value => {
            this.activeFiltersCount = value ? this.activeFiltersCount = 1 : 0;
            this.loadFirstPage(this._activatedRoute.snapshot.queryParams);
        });
    }

    openDialogCertifierAccess(certifierAccess: CertifierAccess, certifierAccessId: string = null): void {
        this.openCertifierAccessDialog(certifierAccess).subscribe((updatedCertifierAccess) => {
            this.certifierAccessByCertifier[updatedCertifierAccess._links.certifier.siret] = updatedCertifierAccess;
            this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(this._translateService.instant('private.certification.partners.table.accessUpdated')));
        }).add(() => {
            if (certifierAccessId) {
                this._router.navigate([]);
            }
            if ([ConnectionState.REVOKED, ConnectionState.FAILED, ConnectionState.INACTIVE].includes(this.connectionCpf.state)) {
                this.openAuthCheck();
            }
        });
    }

    ngAfterViewInit(): void {
        this._activatedRoute.params.pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe((params) => {
            const {certifInfo} = params;
            this.certifInfo = certifInfo;
            if (!certifInfo) {
                this.selectedCertification = null;
                this.selectedCertificationIndex = null;
                if (this.sidenav?.opened) {
                    this.sidenav.close();
                }
            }
        });

        this._activatedRoute.queryParams.pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe(queryParams => this.loadFirstPage(queryParams));

        // Use an observable, so we can use a switchMap
        // because the switchMap will automagically cancel pending requests
        this.queryParams$.pipe(
            switchMap((queryParams) => {
                this.isLoading = true;
                return this._certificationService.listPartnershipOrdered({
                    page: this.currentPage,
                    limit: this.itemsPerPage,
                    siret: this.organism.siret,
                    query: this.queryCtrl.value ?? '',
                    ...queryParams,
                    'certificationTypes[]': [CertificationTypes.RS, CertificationTypes.RNCP]
                });
            }),
            takeUntil(this._unsubscribeAll)
        ).subscribe((certifications) => {
            this.certifications.push(...certifications.payload);
            this.total = certifications.total;

            if (this.certifInfo) {
                const selectedCertification = this.selectedCertification?.certifInfo === this.certifInfo ?
                    this.selectedCertification : this.certifications?.find(certification => certification.certifInfo === this.certifInfo);
                const anchor: string = getAnchor(this._location.path());
                this.openCertificationSummary(selectedCertification,
                    Object.values(ShortcutCatalogCertificationSidePanelEnum)
                        .some((shortcut: ShortcutCatalogCertificationSidePanelEnum): boolean => shortcut === anchor));
            }

            this.isLoading = false;
            if (this.scrollContainer.getHeight() > this.catalogBlock.nativeElement.offsetHeight) {
                this.onScroll();
            }
        });
    }

    loadFirstPage(queryParams: Params): void {
        this.currentPage = 1;
        this.certifications = [];
        this.queryParams$.next(queryParams);
    }

    onScroll(): void {
        if (this.total > this.certifications.length && !this.isLoading) {
            this.currentPage++;
            this.queryParams$.next(this._activatedRoute.snapshot.queryParams);
        }
    }

    closeSide(): void {
        this._router.navigate(['..'], {
            relativeTo: this._activatedRoute,
            queryParamsHandling: 'preserve'
        }).then((success) => {
            if (success) {
                this.selectedCertification = null;
                this.selectedCertificationIndex = null;
            }
        });
    }

    openCertificationSummary(certification: Certification, keepShortcutSide: boolean = false): void {

        this.selectedCertification = certification;
        if (this.sidenav.opened) {
            const anchor: string = getAnchor(this._location.path());

            if (Object.values(ShortcutCatalogCertificationSidePanelEnum).some(
                (shortcut: ShortcutCatalogCertificationSidePanelEnum): boolean => shortcut === anchor) && keepShortcutSide) {
                this._router.navigate(['..', certification.certifInfo, anchor], {
                    relativeTo: this._activatedRoute,
                    queryParamsHandling: 'preserve'
                }).then(() => {
                    this.scrollToEl(this.cards.toArray()[this.getSelectedIndex()]);
                });
            } else {
                this._router.navigate(['..', certification.certifInfo], {
                    relativeTo: this._activatedRoute,
                    queryParamsHandling: 'preserve'
                }).then(() => {
                    this.scrollToEl(this.cards.toArray()[this.getSelectedIndex()]);
                });
            }
        } else {
            const anchor = this._activatedRoute.snapshot.firstChild?.params.anchor;
            const navigateCommands = ['..', certification.certifInfo];
            if (Object.values(ShortcutCatalogCertificationSidePanelEnum)
                .some((shortcut: ShortcutCatalogCertificationSidePanelEnum): boolean => shortcut === anchor) && keepShortcutSide) {
                navigateCommands.push(anchor);
            }

            this._router.navigate(navigateCommands, {
                relativeTo: this._activatedRoute,
                queryParamsHandling: 'preserve'
            }).then((success: boolean) => {
                this.sidenav.open().then(() => {
                    this.scrollToEl(this.cards.toArray()[this.getSelectedIndex()]);
                });
            });
        }
    }

    hasNext(): boolean {
        return this.certifications.length > 0
            && !!this.selectedCertification
            && this.getSelectedIndex() < this.certifications.length - 1;
    }

    hasPrevious(): boolean {
        return this.certifications.length > 0
            && !!this.selectedCertification
            && this.getSelectedIndex() > 0;
    }

    selectNext(): void {
        this.selectedCertification = this.certifications[this.getSelectedIndex() + 1];
        this.openCertificationSummary(this.selectedCertification, true);
    }

    selectPrevious(): void {
        this.selectedCertification = this.certifications[this.getSelectedIndex() + -1];
        this.openCertificationSummary(this.selectedCertification, true);
    }

    private scrollToEl(card?: CertificationCatalogCardComponent): void {
        card?.scrollToTop();
    }

    getSelectedIndex(): number {
        this.selectedCertificationIndex = this.certifications.findIndex(certification => certification.certifInfo === this.selectedCertification?.certifInfo);
        return this.selectedCertificationIndex;
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    filterCode(code: string): void {
        this.activeFiltersCount = 1;
        this.queryCtrl.setValue(code);
    }

    onCertifierAccessUpdated(certifierAccess: CertifierAccess): void {
        this.certifierAccessByCertifier[certifierAccess._links.certifier.siret] = certifierAccess;
    }

    onCertificationPartnerUpdated(certificationPartner: CertificationPartner): void {
        const certification = this.certifications.find((currentCertification) => currentCertification.certifInfo === certificationPartner._links.certification.certifInfo);
        if (certification) {
            certification.certificationPartners = [certificationPartner];
        }
    }

    onCertificationPartnerDeleted(certificationPartner: CertificationPartner): void {
        const certification = this.certifications.find((currentCertification) => currentCertification.certifInfo === certificationPartner?._links.certification.certifInfo);
        if (certification) {
            certification.certificationPartners = [];
        }
        this.closeSide();
    }

    onCertificationUpdated(certificationUpdated: Certification): void {
        // We don't replace the certification as it is not the same type as the one returned by the catalog (!= serializer in the back)
        if (!certificationUpdated.allowPartnershipRequest) {
            if (this.sidenav?.opened) {
                this.sidenav.close();
            }
            const index = this.certifications.findIndex(currentCertification => currentCertification.certifInfo === certificationUpdated.certifInfo);
            this.certifications.splice(index, 1);
            this._router.navigate([]);
        }
    }

    clearSearch(): void {
        this.activeFiltersCount = 0;
        this.queryCtrl.setValue(null);
        // this._router.navigate([]); TODO uncomment when search advanced
    }

    // TODO factoriser car il y a de nombreux appels (utiliser une variable)
    getPartialCertificationPartner(certification: Certification): CertificationPartner {
        // Embedded certificationPartner with partial data
        return certification.certificationPartners?.find(certificationPartner => certificationPartner._links.partner.siret === this.organism.siret);
    }

    isCertifier(certification: Certification): boolean {
        const queryParams = this._activatedRoute.snapshot.queryParams;
        if (queryParams.defaultCertifier === this.organism.siret) {
            return true; // Hack to rely on back to know that an organism is a certifier (even if no default)
        }
        return certification._links.defaultCertifier?.siret === this.organism.siret;
    }

    openAuthCheck(): void {
        const dialogRef = this._dialog.open(DialogConnectionAuthComponent, {
            disableClose: true,
            panelClass: ['no-padding-panel', 'full-page-scroll-40'],
            height: 'auto',
            data: {
                dataProvider: DataProviders.CPF,
                organism: this.organism,
                subscription: this.subscription
            }
        });
        dialogRef.afterClosed().pipe(
            switchMap(connectionOk => {
                if (connectionOk) {
                    return this._store.dispatch(new FetchConnections());
                } else {
                    return of();
                }
            })
        ).subscribe();
    }
}

