<div class="min-w-full content-layout fullwidth-standard-inner-scroll">
    <mat-drawer-container>
        <mat-drawer #sidenav [autoFocus]="false" mode="side" position="end" [disableClose]="true">
            <app-certification-catalog-side *ngIf="selectedCertification"
                                            [hasNext]="hasNext()"
                                            [hasPrevious]="hasPrevious()"
                                            (next)="selectNext()"
                                            (previous)="selectPrevious()"
                                            (closeSide)="closeSide()"
                                            (searchTag)="filterCode($event)"
                                            [organism]="organism"
                                            [certifierAccess]="certifierAccessByCertifier[getPartialCertificationPartner(selectedCertification)?._links.certifier?.siret]"
                                            (certifierAccessUpdated)="onCertifierAccessUpdated($event)"
                                            (certificationPartnerUpdated)="onCertificationPartnerUpdated($event)"
                                            (certificationPartnerDeleted)="onCertificationPartnerDeleted($event)"
                                            (certificationUpdated)="onCertificationUpdated($event)"
                                            [certificationPartnerState]="getPartialCertificationPartner(selectedCertification)?.state"
                                            [partialCertificationPartner]="getPartialCertificationPartner(selectedCertification)"
                                            [partialCertification]="selectedCertification">
            </app-certification-catalog-side>
        </mat-drawer>
        <mat-drawer-content>
            <header class="flex flex-row justify-between pt-4 px-4 bg-white">
                <div class="flex flex-col flex-grow">
                    <div class="flex flex-row">
                        <h1>{{ "private.training-organism.certifications.title" | translate }}</h1>
                    </div>
                    <div>
                        <p class="subtitle">{{ "private.training-organism.certifications.subtitle" | translate }}</p>
                        <div class="mt-2 flex flex-row flex-grow items-center">
                            <div class="flex flex-row items-center">
                            </div>
                            <app-filters-bar [filters]="filteredFilters">
                            </app-filters-bar>
                        </div>
                    </div>
                </div>
                <div class="py-6 pl-4 mr-8">
                    <div class="flex flex-row flex-nowrap items-center">
                        <mat-form-field class="mr-8 w-90">
                            <input [title]="'private.certification.partners.searchbar' | translate" matInput
                                   [formControl]="queryCtrl"
                                   [placeholder]="'common.table.filters.globalSearch' | translate" maxlength="50">
                            <mat-icon matPrefix svgIcon="search"></mat-icon>
                            <button mat-button *ngIf="activeFiltersCount" matSuffix mat-icon-button
                                    aria-label="Clear" (click)="clearSearch()">
                                <mat-icon svgIcon="close"></mat-icon>
                            </button>
                            <mat-icon *ngIf="activeFiltersCount && isActiveFiltersCount"
                                      [matBadge]="activeFiltersCount+''"
                                      matBadgePosition="below after"
                                      matBadgeSize="small"
                                      [color]="'primary' "
                                      [svgIcon]="'filter_alt'"></mat-icon>
                        </mat-form-field>
                    </div>
                </div>
            </header>
            <main class="h-full overflow-hidden">
                <app-wrapper-spinner [active]="isLoading" #scrollContainer
                                     infiniteScroll
                                     [scrollWindow]="false"
                                     [immediateCheck]="true"
                                     [alwaysCallback]="true"
                                     (scrolled)="onScroll()"
                                     class="h-full overflow-auto">
                    <div class="grid dynamic-grid justify-start gap-5 p-3" #catalogBlock>
                        <app-certification-catalog-card #cards
                                                        (openCard)="openCertificationSummary(certification)"
                                                        (searchCode)="filterCode($event)"
                                                        (certificationPartnerUpdated)="onCertificationPartnerUpdated($event)"
                                                        *ngFor="let certification of certifications; let index = index"
                                                        [certification]="certification"
                                                        [certifierAccess]="certifierAccessByCertifier[getPartialCertificationPartner(certification)?._links.certifier?.siret]"
                                                        [certificationPartner]="getPartialCertificationPartner(certification)"
                                                        [isCertifier]="isCertifier(certification)"
                                                        [isSelected]="selectedCertificationIndex === +index">
                        </app-certification-catalog-card>
                    </div>
                </app-wrapper-spinner>
            </main>
        </mat-drawer-content>
    </mat-drawer-container>
</div>
