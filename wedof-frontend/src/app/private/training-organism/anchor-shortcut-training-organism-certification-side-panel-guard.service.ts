import {Observable} from 'rxjs';
import {ShortcutCatalogCertificationSidePanelEnum} from '../../shared/utils/enums/shortcut-side-panel-enum';
import {Injectable} from '@angular/core';
import {ActivatedRouteSnapshot, CanActivate} from '@angular/router';

@Injectable({
    providedIn: 'root'
})
export class AnchorShortcutTrainingOrganismCertificationSidePanelGuard implements CanActivate {
    canActivate(
        route: ActivatedRouteSnapshot
    ): Observable<boolean> | Promise<boolean> | boolean  {
        const allowedAnchors: ShortcutCatalogCertificationSidePanelEnum[] = Object.values(ShortcutCatalogCertificationSidePanelEnum);
        const anchor: string = route.paramMap.get('anchor');
        if (allowedAnchors.includes(anchor as ShortcutCatalogCertificationSidePanelEnum)){
            return true;
        } else {
            window.location.href = '404-not-found';
            return false;
        }
    }
}
