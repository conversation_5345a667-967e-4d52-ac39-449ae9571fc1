import {AfterViewInit, Component, Injector, Input, OnInit, ViewChild} from '@angular/core';
import {FormBuilder, FormGroup} from '@angular/forms';
import {MatSidenav} from '@angular/material/sidenav';
import {ActivatedRoute, Router} from '@angular/router';
import moment, {Moment} from 'moment';
import {forkJoin, iif, Observable, of, Subject} from 'rxjs';
import {first, takeUntil, tap} from 'rxjs/operators';
import {RegistrationFolder} from '../../../../shared/api/models/registration-folder';
import {Session} from '../../../../shared/api/models/session';
import {PaginatedResponse} from '../../../../shared/api/services/abstract-paginated.service';
import {RegistrationFolderService} from '../../../../shared/api/services/registration-folder.service';
import {SessionService} from '../../../../shared/api/services/session.service';
import {AbstractCollapsableRowTableComponent} from '../../../../shared/material/table/abstract-collapsable-row-table.component';
import {DataQuery, DataResponse} from '../../../../shared/material/table/abstract-table.component';
import {
    RegistrationFolderQuery,
    RegistrationFolderTableComponent
} from '../../../../shared/registration-folder/registration-folder-table/registration-folder-table.component';
import {RegistrationFolderTableColumns} from '../../../../shared/registration-folder/registration-folder-table/registration-folder-table-params';
import {ShortcutFolderSidePanelEnum} from '../../../../shared/utils/enums/shortcut-side-panel-enum';
import {Location} from '@angular/common';
import {getAnchor} from '../../../../shared/utils/shortcut-side-panel-utils';
import {Attendee} from '../../../../shared/api/models/attendee';

interface SessionFilters {
    startDate: Moment;
    endDate: Moment;
}

export interface SessionQuery extends DataQuery {
    sessionId: string;
}

@Component({
    selector: 'registration-folders-view-by-session',
    templateUrl: './registration-folders-view-by-session.component.html',
    styleUrls: ['./registration-folders-view-by-session.component.scss']
})
export class RegistrationFoldersViewBySessionComponent extends AbstractCollapsableRowTableComponent<Session> implements AfterViewInit, OnInit {
    displayedColumns = ['name'];

    cpfSessionMinDate: Date = null;

    today: Moment;
    filters: FormGroup;
    selectedSession: Session;
    preSetStartDate: Moment;
    selectedRegistrationFolder: RegistrationFolder;
    registrationFolderTableDisplayedColumns: RegistrationFolderTableColumns[];
    previousViewType?: string;

    regFolderQuery$ = new Subject<RegistrationFolderQuery>();

    @ViewChild('sidenav') sidenav: MatSidenav;
    @ViewChild(RegistrationFolderTableComponent) registrationFolderTableComponent: RegistrationFolderTableComponent;
    @Input() dataQuery$ = new Subject<SessionQuery>();

    constructor(
        injector: Injector,
        private _sessionService: SessionService,
        private _formBuilder: FormBuilder,
        private _router: Router,
        private _activatedRoute: ActivatedRoute,
        private _registrationFolderService: RegistrationFolderService,
        private _location: Location
    ) {
        super(injector);
        this.preSetStartDate = moment().startOf('isoWeek');
        this.today = moment();
        this.registrationFolderTableDisplayedColumns = [
            RegistrationFolderTableColumns.ACTIONS,
            RegistrationFolderTableColumns.LAST_NAME,
            RegistrationFolderTableColumns.STATE,
            RegistrationFolderTableColumns.FUNDING,
            RegistrationFolderTableColumns.TAGS
        ];
    }

    ngOnInit(): void {
        this.followingRow.subscribe((session: DataResponse<Session>) => {
            this.openSessionSummary(session);
        });

        this._registrationFolderService.sessionMinDates$.pipe(first()).subscribe((sessionMinDates) => {
            this.cpfSessionMinDate = new Date(sessionMinDates.cpfSessionMinDate);
        });

        this._activatedRoute.params.pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe((params) => {
            const {session} = params;
            const {externalId} = this._activatedRoute.snapshot.firstChild ? this._activatedRoute.snapshot.firstChild.params : {externalId: null};
            if (session) {
                const session$ = this.selectedSession?.externalId === session
                    ? of(this.selectedSession)
                    : this._sessionService.get(session);
                iif(
                    () => !externalId,
                    forkJoin([session$, of(null)]),
                    forkJoin([
                        session$,
                        this.selectedRegistrationFolder?.externalId === externalId
                            ? of(this.selectedRegistrationFolder)
                            : this._registrationFolderService.get(externalId)
                    ])
                ).subscribe(([selectedSession, selectedRegistrationFolder]: [Session, RegistrationFolder]) => {
                    this.selectedSession = selectedSession;
                    this.selectedRegistrationFolder = selectedRegistrationFolder;
                    this.sidenav.open();
                });
            } else {
                this.selectedSession = null;
                this.selectedRegistrationFolder = null;
                if (this.sidenav?.opened) {
                    this.sidenav.close();
                }
            }
        });

        this._activatedRoute.queryParams.pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe((queryParams) => {
            if (queryParams.previousViewType === 'liste' || queryParams.previousViewType === 'kanban') {
                this.previousViewType = queryParams.previousViewType;
            }
        });

        const dateFormat = 'YYYY-MM-DD';
        this.filters = this._formBuilder.group({
            startDate: [undefined],
            endDate: [undefined]
        });

        this.filters.valueChanges.pipe(takeUntil(this._unsubscribeAll))
            .subscribe((value: SessionFilters) => {
                if (this.filters.valid) {
                    this.applyFilter({name: 'since', value: value?.startDate?.format(dateFormat)});
                    this.applyFilter({name: 'until', value: value?.endDate?.format(dateFormat)});
                }
            });

        this.filters.get('startDate').setValue(this.preSetStartDate);
        this.applyFilter({name: 'since', value: this.preSetStartDate.format(dateFormat)});
    }

    ngAfterViewInit(): void {
        super.ngAfterViewInit();
    }

    next(): void {
        if (this.selectedRegistrationFolder) {
            this.regFolderQuery$.next({
                sessionId: this.selectedSession.externalId,
                regFolderId: this.selectedRegistrationFolder.externalId,
                next: true,
                keepShortcutSide: true
            });
        } else {
            this.dataQuery$.next({sessionId: this.selectedSession.externalId, next: true});
        }
    }

    closeSide(): void {
        this._router.navigate(['..'], {relativeTo: this._activatedRoute});
    }

    previous(): void {
        if (this.selectedRegistrationFolder) {
            this.regFolderQuery$.next({
                sessionId: this.selectedSession.externalId,
                regFolderId: this.selectedRegistrationFolder.externalId,
                previous: true,
                keepShortcutSide: true
            });
        } else {
            this.dataQuery$.next({sessionId: this.selectedSession.externalId, previous: true});
        }
    }

    clearDateFilter(filterValue: string): void {
        this.filters.get(filterValue).setValue(null);
    }

    openRegistrationFolderSummary(response: DataResponse<RegistrationFolder>, session?: Session): void {
        if (!response.row.isAllowActions) {
            return;
        }
        this.selectedSession = session;
        this.selectedRegistrationFolder = response.row;
        const anchor: string = getAnchor(this._location.path());
        const navigateCommands = ['formation', 'dossiers', 'sessions', session.externalId, response.row.externalId];
        if (Object.values(ShortcutFolderSidePanelEnum).some((shortcut: ShortcutFolderSidePanelEnum): boolean => shortcut === anchor) && response?.keepShortcutSide) {
            navigateCommands.push(anchor);
        }
        this._router.navigate(navigateCommands);
    }

    openSessionSummary(response?: DataResponse<Session>): void {
        this.selectedSession = response.row;
        this.selectedRegistrationFolder = null;
        this._router.navigate(['formation', 'dossiers', 'sessions', response.row.externalId]);
    }

    protected findRowWithDataQuery(row: Session, query: SessionQuery): boolean {
        return row.externalId === query.sessionId;
    }

    protected getRowIdentifier(row: Session): string {
        return row.externalId;
    }

    protected refreshData(pageIndex = this.paginator.pageIndex): Observable<PaginatedResponse<Session>> {
        return this._sessionService.list({
            ...this._filters$.value,
            limit: this.paginator.pageSize,
            page: pageIndex + 1,
            order: 'asc'
        }).pipe(
            tap((response) => {
                if (this.selectedSession) {
                    this.openRow(this.selectedSession);
                } else {
                    this.openRow(response.payload.find((session) => moment(session.startDate).date() >= this.today.date()));
                }
            })
        );
    }

    sendRefreshAttendee(attendee: Attendee): void {
        this.registrationFolderTableComponent.refreshAttendee(attendee);
    }

    sendRefreshRow($event: RegistrationFolder): void {
        this.registrationFolderTableComponent.refreshRow($event);
    }
}
