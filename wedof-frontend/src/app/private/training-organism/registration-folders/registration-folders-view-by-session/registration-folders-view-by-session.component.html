<div class="min-w-full bg-white content-layout fullwidth-standard-inner-scroll">
    <app-registration-folder-connection-check>
    </app-registration-folder-connection-check>
    <mat-drawer-container>
        <mat-drawer-content class="bg-white">
            <header class="flex flex-row justify-between py-4 px-4 bg-white">
                <div class="flex flex-col">
                    <div class="flex flex-row">
                        <h1>{{ 'private.training-organism.sessions.title' | translate }}</h1>
                        <a class="flex flex-row pl-4 self-center"
                           [routerLink]="['/formation', 'dossiers', (previousViewType != null ? previousViewType : 'liste')]">
                            <p class="text-center">{{ 'private.training-organism.sessions.folderView' | translate }}</p>
                            <mat-icon class="pl-1 self-center" matPrefix svgIcon="sync_alt"></mat-icon>
                        </a>
                    </div>
                    <p class="subtitle">
                        {{ 'private.training-organism.folders.subtitle' | translate }}
                        <br/>{{ 'private.training-organism.common.minDateCpf' | translate }}
                        <b>{{ cpfSessionMinDate | dateZToDayString }}</b>
                    </p>
                </div>
                <div class="pl-4 py-4 pr-10 flex flex-row flex-nowrap items-center">
                    <ng-container>
                        <mat-form-field class="mr-2">
                            <input matInput [formControl]="queryCtrl"
                                   [title]="'private.training-organism.sessions.searchbar' | translate"
                                   [placeholder]="'private.training-organism.sessions.search' | translate"
                                   maxlength="50">
                            <mat-icon matPrefix svgIcon="search"></mat-icon>
                            <button mat-button *ngIf="queryCtrl.value" matSuffix mat-icon-button aria-label="Clear"
                                    (click)="clearQueryFilter()">
                                <mat-icon svgIcon="close"></mat-icon>
                            </button>
                        </mat-form-field>
                    </ng-container>
                    <ng-container [formGroup]="filters">
                        <div class="flex flex-row">
                            <mat-form-field class="mr-2" appearance="fill">
                                <input formControlName="startDate" matInput [max]="filters.get('endDate').value"
                                       [matDatepicker]="startDatePicker"
                                       [placeholder]="'private.training-organism.sessions.filters.startDate' | translate">
                                <button class="mr-2" *ngIf="filters.get('startDate').value" mat-button matSuffix
                                        mat-icon-button aria-label="Clear" (click)="clearDateFilter('startDate')">
                                    <mat-icon svgIcon="close"></mat-icon>
                                </button>
                                <mat-datepicker-toggle matSuffix [for]="startDatePicker"></mat-datepicker-toggle>
                                <mat-datepicker #startDatePicker></mat-datepicker>
                            </mat-form-field>
                            <mat-form-field appearance="fill">
                                <input formControlName="endDate" matInput [min]="filters.get('startDate').value"
                                       [matDatepicker]="endDatePicker"
                                       [placeholder]="'private.training-organism.sessions.filters.endDate' | translate">
                                <button class="mr-2" *ngIf="filters.get('endDate').value" mat-button matSuffix
                                        mat-icon-button aria-label="Clear" (click)="clearDateFilter('endDate')">
                                    <mat-icon svgIcon="close"></mat-icon>
                                </button>
                                <mat-datepicker-toggle matSuffix [for]="endDatePicker"></mat-datepicker-toggle>
                                <mat-datepicker #endDatePicker></mat-datepicker>
                            </mat-form-field>
                        </div>
                    </ng-container>
                </div>
            </header>
            <main>
                <ng-template #noData>
                    <p class="no-table p-2">
                        {{ 'private.training-organism.sessions.no-data' | translate }}
                    </p>
                </ng-template>

                <app-wrapper-spinner [active]="isLoading">
                    <ng-container *ngIf="isLoading || displayedData?.length || activeFilters ; else noData">

                        <table *ngIf="displayedData?.length" [dataSource]="displayedData" mat-table>
                            <tr class="collapsable-row" mat-row *matRowDef="let row; columns: displayedColumns;">
                            </tr>
                            <tr mat-footer-row *matFooterRowDef="['noDataForFilters']"
                                [hidden]="isLoading || displayedData?.length">
                            </tr>

                            <ng-container matColumnDef="noDataForFilters">
                                <td mat-footer-cell *matFooterCellDef [attr.colspan]="displayedColumns.length">
                                    {{ 'common.table.filters.no-data' | translate }}
                                </td>
                            </ng-container>

                            <ng-container matColumnDef="name">
                                <td class="p-0" mat-cell *matCellDef="let session">
                                    <div class="font-bold flex flex-row justify-between"
                                         [ngClass]="{'sticky-section-title': isOpened(session)}"
                                         (click)="toggleRow(session, $event)">
                                        <div class="flex flex-row py-4 pl-4 w-full self-center">
                                            <div class="w-6/12 flex flex-row">
                                                <mat-icon class="self-center"
                                                          [svgIcon]="isOpened(session) ? 'expand_less' : 'expand_more'">
                                                </mat-icon>
                                                <p class="self-center pl-2 title-ellipsis w-96">
                                                    {{ 'private.training-organism.sessions.label' | translate : session }}
                                                </p>
                                            </div>

                                            <div class="flex flex-row self-center text-center w-3/12 pl-4">
                                                <mat-icon class="self-center" svgIcon="calendar_today"></mat-icon>
                                                <p *ngIf="session.startDate else noDate" class="self-center pl-2">
                                                    {{
                                                        'private.training-organism.sessions.dates' | translate : {
                                                            session: session,
                                                            startDate: session.startDate | date:'mediumDate',
                                                            endDate: session.endDate | date:'mediumDate'
                                                        }
                                                    }}
                                                </p>
                                                <ng-template #noDate>
                                            <span class="pl-2" id="noDate">
                                                {{
                                                    'private.training-organism.sessions.labelNoDate' | translate: session
                                                }}
                                            </span>
                                                </ng-template>
                                            </div>


                                            <div class="flex flex-row self-center w-3/12 justify-evenly">
                                                <app-tag class="ml-2 self-center w-32 text-center" color="#e0e7ff">
                                                    {{ session.sessionInfo.city | titlecase }}
                                                </app-tag>
                                                <app-tag class="ml-2 self-center text-center w-32" color="#f2e5f2">
                                                    {{
                                                        'private.training-organism.sessions.attendees' | translate:
                                                            session
                                                    }}
                                                </app-tag>
                                            </div>
                                        </div>
                                        <!--<div class="flex bg-gray-400">
                                            <button class="self-center" mat-icon-button
                                                    (click)="onRowClick(session); toggleRowIfNotOpened(session); $event.stopPropagation()">
                                                <mat-icon
                                                    [svgIcon]="isSideOpened(session) ? 'chevron_right' : 'chevron_left'"></mat-icon>
                                            </button>
                                        </div>-->
                                    </div>

                                    <ng-container *ngIf="isFetched(session)">
                                        <app-registration-folder-table [session]="session"
                                                                       [dataQuery$]="regFolderQuery$"
                                                                       (followingRow)="openRegistrationFolderSummary($event, session)"
                                                                       [selectedRow]="selectedRegistrationFolder"
                                                                       [externalSelectionChange]="true"
                                                                       [hidden]="!isOpened(session)"
                                                                       [emptyMessage]="'private.training-organism.sessions.registration-folder.no-data' | translate"
                                                                       [displayedColumns]="registrationFolderTableDisplayedColumns">
                                        </app-registration-folder-table>
                                    </ng-container>
                                </td>
                            </ng-container>
                        </table>

                        <app-paginator [length]="total"
                                       [pageSizeOptions]="pageSizeOptions"
                                       [scrollTopOnPageChange]="true"
                                       (page)="onPageEvent($event)">
                        </app-paginator>
                    </ng-container>
                </app-wrapper-spinner>
            </main>
        </mat-drawer-content>
        <mat-drawer #sidenav mode="side" position="end" [autoFocus]="false" [disableClose]="true">
            <app-registration-folder-side
                *ngIf="selectedRegistrationFolder"
                [hasNext]="registrationFolderTableComponent?.hasNext(selectedRegistrationFolder, 'externalId')"
                [hasPrevious]="registrationFolderTableComponent?.hasPrevious(selectedRegistrationFolder, 'externalId')"
                (attendeeRefreshed)="sendRefreshAttendee($event)"
                (next)="next()"
                (previous)="previous()"
                (closeSide)="closeSide()"
                (registrationFolderRefreshed)="sendRefreshRow($event)"
                [registrationFolder]="selectedRegistrationFolder">
            </app-registration-folder-side>
        </mat-drawer>
    </mat-drawer-container>
</div>
