<div class="min-w-full content-layout fullwidth-standard-inner-scroll">
    <app-registration-folder-connection-check>
    </app-registration-folder-connection-check>
    <mat-drawer-container>
        <mat-drawer-content>
            <header class="flex flex-row justify-between pt-4 px-4 bg-white">
                <div class="flex flex-col flex-grow">
                    <div class="flex flex-row">
                        <h1>{{ 'private.training-organism.folders.title' | translate }}</h1>
                        <a class="flex flex-row pl-4 self-center"
                           [queryParams]="{ previousViewType: 'kanban' }"
                           routerLink="/formation/dossiers/sessions">
                            <p class="text-center">{{ 'private.training-organism.folders.sessionView' | translate }}</p>
                            <mat-icon class="pl-1 self-center" matPrefix svgIcon="sync_alt"></mat-icon>
                        </a>
                    </div>
                    <div>
                        <p class="subtitle">
                            {{ 'private.training-organism.folders.subtitle' | translate }}
                            <br/>{{ 'private.training-organism.common.minDateCpf' | translate }}
                            <b>{{ cpfSessionMinDate | dateZToDayString }}</b>
                        </p>
                        <div class="mt-2 flex flex-row flex-grow items-center">
                            <div class="flex flex-row items-center">
                                <mat-icon color="primary">view_kanban</mat-icon>
                                <button
                                    [matTooltip]=" 'private.training-organism.folders.toolTip.tableView' | translate "
                                    [matTooltipPosition]="'above'"
                                    [matTooltipShowDelay]="500" (click)="setShowTableView()" mat-icon-button
                                    [disabled]="!showKanbanView"
                                    [color]="'accent'">
                                    <mat-icon svgIcon="table_rows"></mat-icon>
                                </button>
                            </div>
                            <app-entity-filters [entityClass]="EntityClass.REGISTRATION_FOLDER">
                            </app-entity-filters>
                        </div>
                    </div>
                </div>
                <div class="py-6 pl-4 mr-8">
                    <div class="flex flex-row flex-nowrap items-center" [formGroup]="filters">
                        <mat-form-field class="w-90">
                            <input matInput formControlName="query" [value]="queryConstructor"
                                   [title]="'private.training-organism.folders.searchbar' | translate"
                                   [placeholder]="'common.table.filters.globalSearch' | translate" maxlength="50">
                            <mat-icon matPrefix svgIcon="search"></mat-icon>
                            <button mat-button *ngIf="activeFiltersCount > 0" matSuffix mat-icon-button
                                    aria-label="Clear" (click)="clearSearch()">
                                <mat-icon svgIcon="close"></mat-icon>
                            </button>
                            <button [mdePopoverTriggerFor]="searchPopover"
                                    mdePopoverTriggerOn="click" class="flex">
                                <mat-icon
                                    [matBadge]="activeFiltersCount ? activeFiltersCount+'' : null"
                                    matBadgePosition="below after"
                                    matBadgeSize="small"
                                    [color]="activeFiltersCount ? 'primary' : undefined"
                                    [svgIcon]="activeFiltersCount ? 'filter_alt' : 'manage_search'"></mat-icon>
                            </button>
                            <mde-popover #searchPopover="mdePopover"
                                         [mdePopoverOverlapTrigger]="false"
                                         mdePopoverOffsetX="17"
                                         mdePopoverOffsetY="10"
                                         mdePopoverArrowWidth="0"
                                         mdePopoverPositionX="before"
                                         mdePopoverCloseOnClick="false"
                                         mdePopoverArrowColor="#FFF">
                                <app-registration-folder-advanced-search
                                    [searchAdvanced]="searchAdvanced"
                                    (closePopOver)="closePopover()">
                                </app-registration-folder-advanced-search>
                            </mde-popover>
                        </mat-form-field>
                    </div>
                    <div class="flex items-center justify-end">
                        <button
                            [matTooltip]="(!subscription?.isAllowRegistrationFolderManualCreate ? 'private.training-organism.folders.createFolder.toolTip.notAvailable' : 'private.training-organism.folders.createFolder.toolTip.available' ) | translate "
                            [matTooltipPosition]="'above'"
                            [matTooltipShowDelay]="500"
                            type="button" color="primary"
                            mat-flat-button (click)="openCreate()"
                            [disabled]="!subscription?.isAllowRegistrationFolderManualCreate || openRegistrationFolderCreateSide">
                            {{ 'private.training-organism.folders.createFolder.create' | translate }}
                        </button>
                        <button type="button" color="primary" class="ml-2"
                                [disabled]="exportingCSV"
                                mat-flat-button
                                (click)="openDialogExport()"> {{ 'common.actions.export.exportCsvButton' | translate }}
                        </button>
                    </div>
                </div>
            </header>
            <main>
                <app-kanban-board [showMultipleSelection]="false"
                                  [listColumnConfigs]="listColumnConfigs"
                                  [listAllItemsByColumn]="listAllItemsByColumn"
                                  [listItemsForColumn]="listItemsForColumn"
                                  [getAdditionalDataForColumn]="getRevenueForColumn"
                                  [selectedItem]="selectedRegistrationFolder"
                                  [comparisonProperty]="'externalId'"
                                  [filters$]="regFolderFilters$"
                                  [titleNoData]="'common.kanban.card.no-dataFolder'| translate"
                                  (openCard)="openSummary($event.item, $event.keepShortcutSide)">
                    <ng-template #kanbanCardTemplate let-item>
                        <app-registration-folder-kanban-card *ngIf="item"
                                                             class="flex-1 flex flex-col justify-between"
                                                             [item]="item"
                                                             (processedFolder)="sendRefreshCard($event)"
                                                             (searchTag)="applyNewFilter({tags: $event})">
                        </app-registration-folder-kanban-card>
                    </ng-template>
                    <ng-template #kanbanColumnFooterTemplate let-column="column" let-isExpanded="isExpanded" let-isKanbanLoading="isKanbanLoading">
                        <div *ngIf="isExpanded"
                             class="py-3 px-2 border-t text-center font-semibold">
                            <div *ngIf="subscription.allowAnalytics; else showCta"
                                          [matTooltip]="'private.training-organism.folders.toolTip.revenue' | translate">
                                <ng-container *ngIf="!!column?.revenue else noCA">
                                    {{ 'private.training-organism.folders.revenue.total' | translate }}
                                    <span class="text-indigo">{{ +column?.revenue | number: '1.2-2':'fr-FR' }} €</span>
                                </ng-container>
                                <ng-template #noCA>-</ng-template>
                            </div>
                            <ng-template #showCta>
                                <button *ngIf="!isKanbanLoading"
                                        (click)="openSubscriptionCard(organism)">
                                    <span [innerHTML]=" 'private.layout.user.subscription.help.statistics' | translate "></span>
                                </button>
                            </ng-template>
                        </div>
                    </ng-template>
                </app-kanban-board>
            </main>
        </mat-drawer-content>
        <mat-drawer #sidenav mode="side" position="end" [autoFocus]="false" [disableClose]="true">
            <app-registration-folder-side #registrationFolderSide
                                          *ngIf="selectedRegistrationFolder"
                                          [registrationFolder]="selectedRegistrationFolder"
                                          (next)="selectNextCard()"
                                          (previous)="selectPreviousCard()"
                                          (registrationFolderRefreshed)="sendRefreshCard($event)"
                                          (attendeeRefreshed)="sendRefreshAttendee($event)"
                                          (closeSide)="closeSide()"
                                          [hasNext]="hasNext()" [hasPrevious]="hasPrevious()"
            ></app-registration-folder-side>
            <app-registration-folder-create-side
                #registrationFolderCreateSide
                *ngIf="openRegistrationFolderCreateSide"
                (closeSide)="closeSide()"
                (registrationFolderCreated)="sendInsertCard($event)">
            </app-registration-folder-create-side>
        </mat-drawer>
    </mat-drawer-container>
</div>
