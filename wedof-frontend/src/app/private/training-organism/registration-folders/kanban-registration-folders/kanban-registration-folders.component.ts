import {
    AfterViewChecked,
    After<PERSON><PERSON>w<PERSON>nit,
    ChangeDetector<PERSON>ef,
    Component,
    OnDestroy,
    OnInit,
    ViewChild
} from '@angular/core';
import {distinct, first, map, switchMap, takeUntil} from 'rxjs/operators';
import {
    RegistrationFolderHttpParams,
    RegistrationFolderService
} from '../../../../shared/api/services/registration-folder.service';
import {RegistrationFolder} from '../../../../shared/api/models/registration-folder';
import {combineLatest, Observable, ReplaySubject, Subject} from 'rxjs';
import {PaginatedResponse} from '../../../../shared/api/services/abstract-paginated.service';
import {ActivatedRoute, Router} from '@angular/router';
import {MatSidenav} from '@angular/material/sidenav';
import {RegistrationFolderSideComponent} from '../../../../shared/sides/registration-folder-side/registration-folder-side.component';
import {RegistrationFolderFilters} from '../../../../shared/registration-folder/registration-folder-table/registration-folder-table.component';
import {
    KanbanAdditionalDataResponse,
    KanbanBoardComponent,
    KanbanColumnConfigsResponse,
    KanbanColumnResponse
} from '../../../../shared/kanban-board/kanban-board/kanban-board.component';
import {CanDeactivateComponent} from '../../../../shared/utils/can-deactivate/can-deactivate.component';
import {FormBuilder, FormGroup} from '@angular/forms';
import {MdePopoverTrigger} from '@material-extended/mde';
import moment from 'moment';
import {Subscription, SubscriptionTypes} from '../../../../shared/api/models/subscription';
import {RegistrationFolderCreateSideComponent} from '../../../../shared/sides/registration-folder-create-side/registration-folder-create-side.component';
import {SnackBarComponent} from '../../../../shared/material/snack-bar/snack-bar.component';
import {displayTextSnackBar} from '../../../../shared/utils/displayTextSnackBar';
import {FileService} from '../../../../shared/api/services/file.service';
import {TranslateService} from '@ngx-translate/core';
import {MatSnackBar} from '@angular/material/snack-bar';
import {DialogUpgradeSubscriptionComponent} from '../../../../shared/subscription/dialog-upgrade-subscription/dialog-upgrade-subscription.component';
import {MatDialog} from '@angular/material/dialog';
import {Organism} from '../../../../shared/api/models/organism';
import {Select} from '@ngxs/store';
import {SubscriptionState} from '../../../../shared/api/state/subscription.state';
import {OrganismState} from '../../../../shared/api/state/organism.state';
import {EntityClass} from '../../../../shared/utils/enums/entity-class';
import {Location} from '@angular/common';
import {ShortcutFolderSidePanelEnum} from '../../../../shared/utils/enums/shortcut-side-panel-enum';
import {getAnchor} from '../../../../shared/utils/shortcut-side-panel-utils';
import {Attendee} from '../../../../shared/api/models/attendee';

@Component({
    selector: 'app-kanban-registration-folders',
    templateUrl: './kanban-registration-folders.component.html',
    styleUrls: ['./kanban-registration-folders.component.scss']
})
export class KanbanRegistrationFoldersComponent extends CanDeactivateComponent implements OnInit, AfterViewInit, OnDestroy, AfterViewChecked {

    constructor(
        private _registrationFolderService: RegistrationFolderService,
        private _router: Router,
        private _activatedRoute: ActivatedRoute,
        private _formBuilder: FormBuilder,
        private _fileService: FileService,
        private _translateService: TranslateService,
        private _snackBar: MatSnackBar,
        private _dialog: MatDialog,
        private _changeDetector: ChangeDetectorRef,
        private _location: Location
    ) {
        super();
    }

    filters: FormGroup;
    exportingCSV = false;
    showKanbanView = true;
    activeFiltersCount = 0;
    queryConstructor: string;
    queryFilters: string;
    searchAdvanced: FormGroup;
    cpfSessionMinDate: Date = null;
    subscription: Subscription = null;
    organism: Organism = null;
    selectedRegistrationFolder: RegistrationFolder = null;
    openRegistrationFolderCreateSide;
    regFolderFilters$ = new ReplaySubject<RegistrationFolderFilters>();
    readonly EntityClass = EntityClass;

    @Select(SubscriptionState.subscription) subscription$: Observable<Subscription>;
    @Select(OrganismState.organism) organism$: Observable<Organism>;

    @ViewChild('sidenav') sidenav: MatSidenav;
    @ViewChild('registrationFolderSide') private registrationFolderSide: RegistrationFolderSideComponent;
    @ViewChild('registrationFolderCreateSide') private registrationFolderCreateSide: RegistrationFolderCreateSideComponent;
    @ViewChild(KanbanBoardComponent) private kanbanBoardComponent: KanbanBoardComponent<RegistrationFolder>;
    @ViewChild(MdePopoverTrigger, {static: false}) trigger: MdePopoverTrigger;

    private _unsubscribeAll: Subject<void> = new Subject();
    private getRegistrationFolder$ = new Subject<string>();
    listColumnConfigs: () => Observable<KanbanColumnConfigsResponse> = () => this._registrationFolderService.listColumnConfigs();
    listAllItemsByColumn: (columnIds: string[], params: RegistrationFolderHttpParams) => Observable<KanbanColumnResponse<RegistrationFolder>> =
        (columnIds: string[], params: RegistrationFolderHttpParams) => this._registrationFolderService.listByColumn(columnIds, params)
    listItemsForColumn: (params: RegistrationFolderHttpParams) => Observable<PaginatedResponse<RegistrationFolder>>
        = (params: RegistrationFolderHttpParams) => this._registrationFolderService.list(params)
    getRevenueForColumn: (state: string, params: RegistrationFolderHttpParams) => Observable<KanbanAdditionalDataResponse> =
        (state: string, params: RegistrationFolderHttpParams) =>
            this._registrationFolderService.revenueByColumn(state, params).pipe(
                map(newValue => ({columnId: state, fieldName: 'revenue', newValue}))
            )

    ngAfterViewChecked(): void {
        this._changeDetector.detectChanges();
    }

    ngOnInit(): void {
        const {externalId} = this._activatedRoute.snapshot.params;
        this.openRegistrationFolderCreateSide = externalId === 'creer';
        this._registrationFolderService.sessionMinDates$.pipe(first()).subscribe((sessionMinDates) => {
            this.cpfSessionMinDate = new Date(sessionMinDates.cpfSessionMinDate);
        });
        this.filters = this._formBuilder.group({
            query: [undefined]
        });
        this.filters.valueChanges.pipe(
            distinct(),
            takeUntil(this._unsubscribeAll)
        ).subscribe((value: { query: string }) => {
            if (this.filters.valid) {
                this.searchAdvanced.get('query').setValue(value.query);
                this.regFolderFilters$.next(this.searchAdvanced.getRawValue());
                this.queryFilters = value?.query;
            }
        });
        this.getRegistrationFolder$.pipe(
            switchMap((id) => this._registrationFolderService.get(id)),
            takeUntil(this._unsubscribeAll)
        ).subscribe(selectedRegistrationFolder => {
            this.selectedRegistrationFolder = selectedRegistrationFolder;
            this.sidenav.open();
        });
        this.searchAdvanced = this._formBuilder.group({
            state: [undefined],
            type: [undefined],
            billingState: [undefined],
            certificationFolderState: [undefined],
            certifInfo: [undefined],
            period: [undefined],
            since: [undefined],
            until: [undefined],
            query: [undefined],
            proposalCode: [undefined],
            sessionId: [undefined],
            trainingActionId: [undefined],
            trainingId: [undefined],
            filterOnStateDate: [undefined],
            controlState: [undefined],
            completionRate: [undefined],
            daysSinceLastUpdatedCompletionRate: [undefined],
            messageTemplate: [undefined],
            messageState: [undefined],
            tags: [undefined]
        });
        this.searchAdvanced.valueChanges.pipe(
            distinct(),
            takeUntil(this._unsubscribeAll)
        ).subscribe((registrationFolderFilters: RegistrationFolderFilters) => {
            if (registrationFolderFilters['query']) {
                this.queryConstructor = registrationFolderFilters['query'];
            }
            this.activeFiltersCount = Object.keys(registrationFolderFilters).filter(key => {
                if (key === 'filterOnStateDate' && registrationFolderFilters[key] === 'lastUpdate') {
                    return false;
                } else if (Array.isArray(registrationFolderFilters[key])) {
                    return registrationFolderFilters[key].length > 0;
                } else {
                    return registrationFolderFilters[key];
                }
            }).length;
        });

        combineLatest([
            this.organism$,
            this.subscription$
        ]).pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe(([organism, subscription]) => {
            this.subscription = subscription;
            this.organism = organism;
        });
    }

    openSummary(registrationFolder: RegistrationFolder, keepShortcutSide: boolean = false): void {
        //  Open subscriptionCard if isAllowActions isn't defined or false.
        if (!registrationFolder.isAllowActions) {
            this.openSubscriptionCard(this.organism);
            return;
        }
        const anchor: string = getAnchor(this._location.path());
        const navigateCommands = ['..', registrationFolder.externalId];
        if (Object.values(ShortcutFolderSidePanelEnum).some((shortcut: ShortcutFolderSidePanelEnum): boolean => shortcut === anchor) && keepShortcutSide) {
            navigateCommands.push(anchor);
        }
        this._router.navigate(navigateCommands, {
            relativeTo: this._activatedRoute,
            queryParamsHandling: 'preserve'
        }).then((success) => {
            // success is true if new navigation, null if it stays on the same, false if failed
            // Here we need to get the freshest hasNext / hasPrevious and RF even on refresh (= stay on same)
            if (success === true || success === null) {
                this.selectedRegistrationFolder = registrationFolder;
            }
        });
    }

    ngAfterViewInit(): void {
        this._activatedRoute.queryParams.pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe((queryParams) => {
            const registrationFolderFilters: RegistrationFolderFilters = {};
            const availableFilters = [
                {key: 'query'},
                {key: 'certifInfo'},
                {key: 'trainingId'},
                {key: 'proposalCode'},
                {key: 'filterOnStateDate', default: 'lastUpdate'},
                {key: 'period'},
                {key: 'since', type: 'date'},
                {key: 'until', type: 'date'},
                {key: 'type', type: 'list'},
                {key: 'state', type: 'list'},
                {key: 'billingState', type: 'list'},
                {key: 'certificationFolderState', type: 'list'},
                {key: 'controlState', type: 'list'},
                {key: 'sessionId'},
                {key: 'trainingActionId'},
                {key: 'completionRate'},
                {key: 'daysSinceLastUpdatedCompletionRate'},
                {key: 'messageTemplate', type: 'list'},
                {key: 'messageState', type: 'list'},
                {key: 'tags', default: [], type: 'list'}
            ];
            availableFilters.forEach((availableFilter) => {
                const key = availableFilter.key;
                const stringValue = queryParams[key] ?? (availableFilter.default ?? null); // force null if it was undefined
                registrationFolderFilters[key] = stringValue;
                let typedValue: any;
                if (stringValue && availableFilter.type === 'list' && (key !== 'tags' || stringValue.length)) {
                    typedValue = stringValue.split(',');
                } else if (stringValue && availableFilter.type === 'date') {
                    typedValue = moment(stringValue);
                } else {
                    typedValue = stringValue;
                }
                this.searchAdvanced.get(key).setValue(typedValue);
            });
            this.regFolderFilters$.next(registrationFolderFilters);
        });

        this._activatedRoute.params.pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe((params) => {
            const {externalId} = params;
            if (externalId === 'creer') {
                this.selectedRegistrationFolder = null;
                this.openRegistrationFolderCreateSide = true;
                setTimeout(() => {
                    this.sidenav.open();
                });
            } else if (externalId) {
                this.openRegistrationFolderCreateSide = false;
                if (this.selectedRegistrationFolder?.externalId !== externalId) {
                    this.getRegistrationFolder$.next(externalId);
                } else {
                    this.sidenav.open();
                }
            } else {
                this.openRegistrationFolderCreateSide = false;
                this.selectedRegistrationFolder = null;
                if (this.sidenav?.opened) {
                    this.sidenav.close();
                }
            }
        });
    }

    sendRefreshCard(registrationFolder: RegistrationFolder): void {
        this.kanbanBoardComponent.refreshCard(registrationFolder);
    }

    sendRefreshAttendee(attendee: Attendee): void {
        Object.values(this.kanbanBoardComponent.columnDataByColumnId).forEach((column) => {
            column.items.forEach(registrationFolder => {
                if (registrationFolder.attendee.id === attendee.id) {
                    registrationFolder.attendee = attendee;
                    this.sendRefreshCard(registrationFolder);
                }
            });
        });
    }

    hasPrevious(): boolean {
        return this.kanbanBoardComponent.hasPrevious(this.selectedRegistrationFolder);
    }

    hasNext(): boolean {
        return this.kanbanBoardComponent.hasNext(this.selectedRegistrationFolder);
    }

    selectPreviousCard(): void {
        this.kanbanBoardComponent.selectPreviousCard(this.selectedRegistrationFolder);
    }

    selectNextCard(): void {
        this.kanbanBoardComponent.selectNextCard(this.selectedRegistrationFolder);
    }

    closeSide(): void {
        this._router.navigate(['..'], {
            relativeTo: this._activatedRoute,
            queryParamsHandling: 'preserve'
        }).then((success) => {
            if (success) {
                this.selectedRegistrationFolder = null;
            }
        });
    }

    canDeactivate(): boolean {
        if (this.registrationFolderCreateSide && this.openRegistrationFolderCreateSide) {
            return this.registrationFolderCreateSide.canDeactivate();
        } else if (this.registrationFolderSide && this.selectedRegistrationFolder) {
            return this.registrationFolderSide.canDeactivate();
        } else {
            return true;
        }
    }

    closePopover(): void {
        this.trigger.togglePopover();
    }

    applyNewFilter(registrationFolderFilters: RegistrationFolderFilters): void {
        const tag = registrationFolderFilters.tags;
        this.searchAdvanced.get('tags').setValue(tag);
        this._router.navigate([], {queryParams: {tags: tag}});
    }

    setShowTableView(): void {
        this.showKanbanView = false;
        this.selectedRegistrationFolder = null;
        if (this.sidenav?.opened) {
            this.sidenav.close();
        }
        this._router.navigate(['/', 'formation', 'dossiers', 'liste'], {
            queryParams: this._activatedRoute.snapshot.queryParams
        });
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    clearSearch(): void {
        this.filters.reset();
        this.searchAdvanced.reset();
        this._router.navigate(['/', 'formation', 'dossiers', 'kanban']);
    }

    openCreate(): void {
        this._router.navigate(['..', 'creer'], {relativeTo: this._activatedRoute}).then((success) => {
            if (success) {
                this.selectedRegistrationFolder = null;
            }
        });
    }

    sendInsertCard(registrationFolder: RegistrationFolder): void {
        this.kanbanBoardComponent.insertCard(registrationFolder);
        this.selectedRegistrationFolder = registrationFolder;
    }

    openDialogExport(): void {
        this.exportingCSV = true;
        const queryParams = Object.assign({}, this._activatedRoute.snapshot.queryParams);
        queryParams['format'] = 'csv';
        if (this.queryFilters) {
            queryParams.query = this.queryFilters;
        }
        this._fileService.download('/api/' + 'registrationFolders', 'dossiersFormation', null, queryParams).subscribe(
            (blob) => {
                if (blob.state === 'PENDING') {
                    this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(
                        this._translateService.instant('common.actions.export.exportStarted'), 0));
                } else if (blob.state === 'DONE') {
                    this.exportingCSV = false;
                    this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(
                        this._translateService.instant('common.actions.export.exportFinished')));
                }
            }
        );
    }

    openSubscriptionCard(organism: Organism): void {
        this._dialog.open(DialogUpgradeSubscriptionComponent, {
            panelClass: ['full-page-scroll-50'],
            data: {
                organism: organism,
                subscription: this.subscription,
                fromPage: 'statistics',
                subscriptionTypeToShow: SubscriptionTypes.TRAINING
            }
        });
    }
}
