<div class="min-w-full content-layout fullwidth-standard-inner-scroll">
    <app-registration-folder-connection-check>
    </app-registration-folder-connection-check>
    <mat-drawer-container>
        <mat-drawer-content>
            <header class="flex flex-row justify-between pt-4 px-4 bg-white">
                <div class="flex flex-col flex-grow">
                    <div class="flex flex-row">
                        <h1>{{ 'private.training-organism.folders.title' | translate }}</h1>
                        <a class="flex flex-row pl-4 self-center"
                           [queryParams]="{ previousViewType: 'liste' }"
                           routerLink="/formation/dossiers/sessions">
                            <p class="text-center">{{ 'private.training-organism.folders.sessionView' | translate }}</p>
                            <mat-icon class="pl-1 self-center" matPrefix svgIcon="sync_alt"></mat-icon>
                        </a>
                    </div>
                    <div>
                        <p class="subtitle">
                            {{ 'private.training-organism.folders.subtitle' | translate }}
                            <br/>{{ 'private.training-organism.common.minDateCpf' | translate }}
                            <b>{{ cpfSessionMinDate | dateZToDayString }}</b>
                        </p>
                        <p class="mt-2">
                            <app-tag class="font-semibold flex-auto" color="#f2e5f2">
                                {{ 'private.training-organism.folders.subtitleCount' | translate : {folderCount: folderCount} }}
                            </app-tag>
                        </p>
                        <div class="mt-2 flex flex-row flex-grow items-center">
                            <div class="flex flex-row items-center">
                                <button
                                    [matTooltip]=" 'private.training-organism.folders.toolTip.kanbanView' | translate "
                                    [matTooltipPosition]="'above'"
                                    [matTooltipShowDelay]="500"
                                    (click)="setShowKanbanView()" mat-icon-button
                                    *ngIf="showTableView"
                                    [color]="'accent'">
                                    <mat-icon>view_kanban</mat-icon>
                                </button>
                                <mat-icon color="primary" svgIcon="table_rows"></mat-icon>
                            </div>
                            <app-entity-filters [entityClass]="EntityClass.REGISTRATION_FOLDER">
                            </app-entity-filters>
                        </div>
                    </div>
                </div>
                <div class="py-4 pl-4 flex flex-row flex-nowrap items-center" [formGroup]="filters">
                    <div class="mr-8">
                        <mat-form-field class="w-90">
                            <input matInput formControlName="query" [value]="queryConstructor"
                                   [title]="'private.training-organism.folders.searchbar' | translate"
                                   [placeholder]="'common.table.filters.globalSearch' | translate" maxlength="50">
                            <mat-icon matPrefix svgIcon="search"></mat-icon>
                            <button mat-button *ngIf="activeFiltersCount" matSuffix mat-icon-button
                                    aria-label="Clear" (click)="clearSearch()">
                                <mat-icon svgIcon="close"></mat-icon>
                            </button>
                            <button [mdePopoverTriggerFor]="searchPopover" mdePopoverTriggerOn="click" class="flex">
                                <mat-icon
                                    [matBadge]="activeFiltersCount ? activeFiltersCount+'' : null"
                                    matBadgePosition="below after"
                                    matBadgeSize="small"
                                    [color]="activeFiltersCount ? 'primary' : undefined"
                                    [svgIcon]="activeFiltersCount ? 'filter_alt' : 'manage_search'"></mat-icon>
                            </button>
                            <mde-popover #searchPopover="mdePopover"
                                         [mdePopoverOverlapTrigger]="false"
                                         mdePopoverOffsetX="17"
                                         mdePopoverOffsetY="10"
                                         mdePopoverArrowWidth="0"
                                         mdePopoverPositionX="before"
                                         mdePopoverCloseOnClick="false"
                                         [mdePopoverTriggerOn]=""
                                         mdePopoverArrowColor="#FFF">
                                <app-registration-folder-advanced-search
                                    [searchAdvanced]="searchAdvanced"
                                    (closePopOver)="closePopover()">
                                </app-registration-folder-advanced-search>
                            </mde-popover>
                        </mat-form-field>
                        <div class="flex items-center justify-end">
                            <button type="button"
                                    [matTooltip]="(!isAllowRegistrationFolderManualCreate ? 'private.training-organism.folders.createFolder.toolTip.notAvailable' : 'private.training-organism.folders.createFolder.toolTip.available' ) | translate "
                                    [matTooltipPosition]="'above'"
                                    [matTooltipShowDelay]="500"
                                    color="primary"
                                    mat-flat-button (click)="openCreate()"
                                    [disabled]="!isAllowRegistrationFolderManualCreate || openRegistrationFolderCreateSide">
                                {{ 'private.training-organism.folders.createFolder.create' | translate }}
                            </button>
                            <button type="button" color="primary" class="ml-2"
                                    [disabled]="exportingCSV"
                                    mat-flat-button
                                    (click)="openDialogExport()"> {{ 'common.actions.export.exportCsvButton' | translate }}
                            </button>
                        </div>
                    </div>
                </div>
            </header>
            <main>
                <app-registration-folder-table [emptyMessage]="'private.training-organism.folders.no-data' | translate"
                                               [dataQuery$]="regFolderQuery$"
                                               [regFolderFilters$]="regFolderFilters$"
                                               (followingRow)="openSummary($event.row, $event.keepShortcutSide)"
                                               [selectedRow]="selectedRegistrationFolder"
                                               [externalSelectionChange]="true"
                                               (totalRows)="folderCount = $event"
                                               (searchTag)="applyNewFilter({tags: $event})"
                                               [displayedColumns]="registrationFolderTableDisplayedColumns">
                </app-registration-folder-table>
            </main>
        </mat-drawer-content>
        <mat-drawer #sidenav mode="side" position="end" [autoFocus]="false" [disableClose]="true">
            <app-registration-folder-side
                #registrationFolderSide
                *ngIf="selectedRegistrationFolder"
                [registrationFolder]="selectedRegistrationFolder"
                [hasNext]="registrationFolderTableComponent?.hasNext(selectedRegistrationFolder, 'externalId')"
                [hasPrevious]="registrationFolderTableComponent?.hasPrevious(selectedRegistrationFolder, 'externalId')"
                (attendeeRefreshed)="sendRefreshAttendee($event)"
                (next)="next()"
                (previous)="previous()"
                (closeSide)="closeSide()"
                (registrationFolderRefreshed)="sendRefreshRow($event)"
            ></app-registration-folder-side>
            <app-registration-folder-create-side
                #registrationFolderCreateSide
                *ngIf="openRegistrationFolderCreateSide"
                (closeSide)="closeSide()"
                (registrationFolderCreated)="sendInsertRow($event)">
            </app-registration-folder-create-side>
        </mat-drawer>
    </mat-drawer-container>
</div>
