import {
    AfterV<PERSON>w<PERSON><PERSON><PERSON>,
    After<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    ChangeDetector<PERSON><PERSON>,
    Component,
    OnDestroy,
    OnInit,
    ViewChild
} from '@angular/core';
import {FormBuilder, FormGroup} from '@angular/forms';
import {MatSidenav} from '@angular/material/sidenav';
import {ActivatedRoute, Router} from '@angular/router';
import moment, {Moment} from 'moment';
import {Observable, ReplaySubject, Subject} from 'rxjs';
import {distinct, first, switchMap, takeUntil} from 'rxjs/operators';
import {RegistrationFolder} from '../../../../shared/api/models/registration-folder';
import {RegistrationFolderService} from '../../../../shared/api/services/registration-folder.service';
import {
    RegistrationFolderFilters,
    RegistrationFolderQuery,
    RegistrationFolderTableComponent
} from '../../../../shared/registration-folder/registration-folder-table/registration-folder-table.component';
import {RegistrationFolderTableColumns} from '../../../../shared/registration-folder/registration-folder-table/registration-folder-table-params';
import {CanDeactivateComponent} from '../../../../shared/utils/can-deactivate/can-deactivate.component';
import {RegistrationFolderCreateSideComponent} from '../../../../shared/sides/registration-folder-create-side/registration-folder-create-side.component';
import {RegistrationFolderSideComponent} from '../../../../shared/sides/registration-folder-side/registration-folder-side.component';
import {FileService} from '../../../../shared/api/services/file.service';
import {TranslateService} from '@ngx-translate/core';
import {MdePopoverTrigger} from '@material-extended/mde';
import {SnackBarComponent} from '../../../../shared/material/snack-bar/snack-bar.component';
import {displayTextSnackBar} from '../../../../shared/utils/displayTextSnackBar';
import {MatSnackBar} from '@angular/material/snack-bar';
import {Select} from '@ngxs/store';
import {SubscriptionState} from '../../../../shared/api/state/subscription.state';
import {Subscription} from '../../../../shared/api/models/subscription';
import {EntityClass} from '../../../../shared/utils/enums/entity-class';
import {Location} from '@angular/common';
import {ShortcutFolderSidePanelEnum} from '../../../../shared/utils/enums/shortcut-side-panel-enum';
import {getAnchor} from '../../../../shared/utils/shortcut-side-panel-utils';
import {Attendee} from '../../../../shared/api/models/attendee';

interface FolderFilters {
    query: string;
    startDate: Moment;
    endDate: Moment;
}

@Component({
    selector: 'registration-folders-view-by-registration-folders',
    templateUrl: './registration-folders-view-by-registration-folders.component.html',
    styleUrls: ['./registration-folders-view-by-registration-folders.component.scss']
})

export class RegistrationFoldersViewByRegistrationFoldersComponent extends CanDeactivateComponent implements OnDestroy, OnInit, AfterViewInit, AfterViewChecked {
    folderCount = 0;
    exportingCSV = false;
    openRegistrationFolderCreateSide;
    cpfSessionMinDate: Date = null;
    isAllowRegistrationFolderManualCreate: boolean;
    filters: FormGroup;
    searchAdvanced: FormGroup;
    activeFiltersCount = 0;
    selectedRegistrationFolder: RegistrationFolder = null;
    registrationFolderTableDisplayedColumns: RegistrationFolderTableColumns[];

    regFolderQuery$ = new Subject<RegistrationFolderQuery>();
    regFolderFilters$ = new ReplaySubject<RegistrationFolderFilters>();
    queryFilters: string;
    queryConstructor: string;
    showTableView = true;
    readonly EntityClass = EntityClass;

    private _unsubscribeAll: Subject<void> = new Subject();
    private getRegistrationFolder$ = new Subject<string>();

    @ViewChild('sidenav') sidenav: MatSidenav;
    @ViewChild('registrationFolderSide') private registrationFolderSide: RegistrationFolderSideComponent;
    @ViewChild('registrationFolderCreateSide') private registrationFolderCreateSide: RegistrationFolderCreateSideComponent;
    @ViewChild(RegistrationFolderTableComponent) registrationFolderTableComponent: RegistrationFolderTableComponent;
    @ViewChild(MdePopoverTrigger, {static: false}) trigger: MdePopoverTrigger;

    @Select(SubscriptionState.subscription) subscription$: Observable<Subscription>;

    constructor(
        private _router: Router,
        private _fileService: FileService,
        private _activatedRoute: ActivatedRoute,
        private _registrationFolderService: RegistrationFolderService,
        private _formBuilder: FormBuilder,
        private _translateService: TranslateService,
        private _snackBar: MatSnackBar,
        private _location: Location,
        private _changeDetector: ChangeDetectorRef,
    ) {
        super();
    }

    ngAfterViewChecked(): void {
        this._changeDetector.detectChanges();
    }

    openDialogExport(): void {
        this.exportingCSV = true;
        const queryParams: { [param: string]: string } = {
            format: 'csv',
            ...this.registrationFolderTableComponent.activeFilters
        };
        if (this.queryFilters) {
            queryParams.query = this.queryFilters;
        }
        this._fileService.download('/api/' + 'registrationFolders', 'dossiersFormation', null, queryParams).subscribe(
            (blob) => {
                if (blob.state === 'PENDING') {
                    this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(
                        this._translateService.instant('common.actions.export.exportStarted'), 0));
                } else if (blob.state === 'DONE') {
                    this.exportingCSV = false;
                    this._snackBar.openFromComponent(SnackBarComponent, displayTextSnackBar(
                        this._translateService.instant('common.actions.export.exportFinished')));
                }
            }
        );
    }

    closePopover(): void {
        this.trigger.togglePopover();
    }

    ngOnInit(): void {
        const {externalId} = this._activatedRoute.snapshot.params;
        this.openRegistrationFolderCreateSide = externalId === 'creer';

        this._registrationFolderService.sessionMinDates$.pipe(first()).subscribe((sessionMinDates) => {
            this.cpfSessionMinDate = new Date(sessionMinDates.cpfSessionMinDate);
        });

        this.filters = this._formBuilder.group({
            query: [undefined],
            startDate: [undefined],
            endDate: [undefined]
        });
        this.searchAdvanced = this._formBuilder.group({
            state: [undefined],
            type: [undefined],
            billingState: [undefined],
            certificationFolderState: [undefined],
            certifInfo: [undefined],
            period: [undefined],
            since: [undefined],
            until: [undefined],
            query: [undefined],
            proposalCode: [undefined],
            sessionId: [undefined],
            trainingActionId: [undefined],
            trainingId: [undefined],
            filterOnStateDate: [undefined],
            controlState: [undefined],
            completionRate: [undefined],
            daysSinceLastUpdatedCompletionRate: [undefined],
            messageTemplate: [undefined],
            messageState: [undefined],
            tags: [undefined]
        });

        this.registrationFolderTableDisplayedColumns = [
            RegistrationFolderTableColumns.ACTIONS,
            RegistrationFolderTableColumns.LAST_NAME,
            RegistrationFolderTableColumns.STATE,
            RegistrationFolderTableColumns.FUNDING,
            RegistrationFolderTableColumns.TRAINING_INFOS,
            RegistrationFolderTableColumns.TAGS
        ];

        this.filters.valueChanges.pipe(
            distinct(),
            takeUntil(this._unsubscribeAll)
        ).subscribe((value: FolderFilters) => {
            if (this.filters.valid) {
                this.regFolderFilters$.next({
                    sessionId: null,
                    query: value?.query
                });
                this.searchAdvanced.get('query').setValue(value.query);
                this.queryFilters = value?.query;
            }
        });

        this.searchAdvanced.valueChanges.pipe(
            distinct(),
            takeUntil(this._unsubscribeAll)
        ).subscribe((registrationFolderFilters: RegistrationFolderFilters) => {
            if (registrationFolderFilters['query']) {
                this.queryConstructor = registrationFolderFilters['query'];
            }
            this.activeFiltersCount = Object.keys(registrationFolderFilters).filter(key => {
                if (key === 'filterOnStateDate' && registrationFolderFilters[key] === 'lastUpdate') {
                    return false;
                } else if (Array.isArray(registrationFolderFilters[key])) {
                    return registrationFolderFilters[key].length > 0;
                } else {
                    return registrationFolderFilters[key];
                }
            }).length;
        });

        this.getRegistrationFolder$.pipe(
            switchMap((id) => this._registrationFolderService.get(id)),
            takeUntil(this._unsubscribeAll)
        ).subscribe(selectedRegistrationFolder => {
            if (!this.registrationFolderTableComponent.selectedRow) {
                this.registrationFolderTableComponent.selectedRow = selectedRegistrationFolder;
            }
            this.selectedRegistrationFolder = selectedRegistrationFolder;
            this.sidenav.open();
        });

        this.subscription$.pipe(takeUntil(this._unsubscribeAll)).subscribe((subscription) => {
            this.isAllowRegistrationFolderManualCreate = subscription?.isAllowRegistrationFolderManualCreate;
        });
    }

    ngOnDestroy(): void {
        this.regFolderFilters$.complete();
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    next(): void {
        this.regFolderQuery$.next({
            sessionId: null,
            regFolderId: this.selectedRegistrationFolder.externalId,
            next: true,
            keepShortcutSide: true
        });
    }

    previous(): void {
        this.regFolderQuery$.next({
            sessionId: null,
            regFolderId: this.selectedRegistrationFolder.externalId,
            previous: true,
            keepShortcutSide: true
        });
    }

    closeSide(): void {
        this._router.navigate(['..'], {
            relativeTo: this._activatedRoute,
            queryParamsHandling: 'preserve'
        }).then((success) => {
            if (success) {
                this.selectedRegistrationFolder = null;
            }
        });
    }

    openSummary(registrationFolder: RegistrationFolder, keepShortcutSide: boolean = false): void {
        if (!registrationFolder.isAllowActions) {
            return;
        }
        const anchor: string = getAnchor(this._location.path());
        const navigateCommands = ['..', registrationFolder.externalId];
        if (Object.values(ShortcutFolderSidePanelEnum).some((shortcut: ShortcutFolderSidePanelEnum): boolean => shortcut === anchor) && keepShortcutSide) {
            navigateCommands.push(anchor);
        }
        this._router.navigate(navigateCommands, {
            relativeTo: this._activatedRoute,
            queryParamsHandling: 'preserve'
        }).then((success) => {
            if (success) {
                this.selectedRegistrationFolder = registrationFolder;
            }
        });
    }

    openCreate(): void {
        this._router.navigate(['..', 'creer'], {relativeTo: this._activatedRoute}).then((success) => {
            if (success) {
                this.selectedRegistrationFolder = null;
            }
        });
    }

    clearSearch(): void {
        this.filters.reset();
        this.searchAdvanced.reset();
        this._router.navigate(['/', 'formation', 'dossiers', 'liste']);
    }

    sendRefreshRow($event: RegistrationFolder): void {
        this.registrationFolderTableComponent.refreshRow($event);
    }

    sendRefreshAttendee(attendee: Attendee): void {
        this.registrationFolderTableComponent.refreshAttendee(attendee);
    }

    sendInsertRow($newRow: RegistrationFolder): void {
        this.registrationFolderTableComponent.insertRow($newRow);
        this.folderCount += 1;
    }

    applyNewFilter(registrationFolderFilters: RegistrationFolderFilters): void {
        const tag = registrationFolderFilters.tags;
        this.searchAdvanced.get('tags').setValue(tag);
        this._router.navigate([], {queryParams: {tags: tag}});
    }

    ngAfterViewInit(): void {
        this._activatedRoute.queryParams.pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe((queryParams) => {
            const registrationFolderFilters: RegistrationFolderFilters = {};
            const availableFilters = [
                {key: 'query'},
                {key: 'certifInfo'},
                {key: 'trainingId'},
                {key: 'proposalCode'},
                {key: 'filterOnStateDate', default: 'lastUpdate'},
                {key: 'period'},
                {key: 'since', type: 'date'},
                {key: 'until', type: 'date'},
                {key: 'type', type: 'list'},
                {key: 'state', type: 'list'},
                {key: 'billingState', type: 'list'},
                {key: 'certificationFolderState', type: 'list'},
                {key: 'controlState', type: 'list'},
                {key: 'sessionId'},
                {key: 'trainingActionId'},
                {key: 'completionRate'},
                {key: 'daysSinceLastUpdatedCompletionRate'},
                {key: 'messageTemplate', type: 'list'},
                {key: 'messageState', type: 'list'},
                {key: 'tags', default: [], type: 'list'}
            ];
            availableFilters.forEach((availableFilter) => {
                const key = availableFilter.key;
                const stringValue = queryParams[key] ?? (availableFilter.default ?? null); // force null if it was undefined
                registrationFolderFilters[key] = stringValue;
                let typedValue: any;
                if (stringValue && availableFilter.type === 'list' && (key !== 'tags' || stringValue.length)) {
                    typedValue = stringValue.split(',');
                } else if (stringValue && availableFilter.type === 'date') {
                    typedValue = moment(stringValue);
                } else {
                    typedValue = stringValue;
                }
                this.searchAdvanced.get(key).setValue(typedValue);
            });
            this.regFolderFilters$.next(registrationFolderFilters);
        });

        this._activatedRoute.params.pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe((params) => {
            const {externalId} = params;
            if (externalId && externalId !== 'creer') {
                this.openRegistrationFolderCreateSide = false;
                if (this.selectedRegistrationFolder?.externalId !== externalId) {
                    this.getRegistrationFolder$.next(externalId);
                } else {
                    this.sidenav.open();
                }
            } else if (externalId === 'creer') {
                this.registrationFolderTableComponent.clearSelectedRow();
                this.selectedRegistrationFolder = null;
                this.openRegistrationFolderCreateSide = true;
                setTimeout(() => {
                    this.sidenav.open();
                });
            } else {
                this.registrationFolderTableComponent.clearSelectedRow();
                this.openRegistrationFolderCreateSide = false;
                this.selectedRegistrationFolder = null;
                if (this.sidenav?.opened) {
                    this.sidenav.close();
                }
            }
        });
    }

    canDeactivate(): boolean {
        if (this.registrationFolderCreateSide && this.openRegistrationFolderCreateSide) {
            return this.registrationFolderCreateSide.canDeactivate();
        } else if (this.registrationFolderSide && this.selectedRegistrationFolder) {
            return this.registrationFolderSide.canDeactivate();
        } else {
            return true;
        }
    }

    setShowKanbanView(): void {
        this.showTableView = false;
        this.selectedRegistrationFolder = null;
        this.openRegistrationFolderCreateSide = false;
        if (this.sidenav?.opened) {
            this.sidenav.close();
        }
        this._router.navigate(['/', 'formation', 'dossiers', 'kanban'], {
            queryParams: this._activatedRoute.snapshot.queryParams
        });
    }
}
