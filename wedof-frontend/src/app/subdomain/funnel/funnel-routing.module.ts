import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {AttendeeProposalsComponent} from './attendee/proposals/attendee-proposals.component';
import {FunnelDataResolver} from '../../core/funnel/resolvers/funnel-data.resolver';
import {SalesProposalsComponent} from './sales/proposals/sales-proposals.component';
import {FunnelDataAuthResolver} from '../../core/funnel/resolvers/funnel-data-auth.resolver';
import {AuthGuard} from '../../core/auth/guards/auth.guard';
import {SalesDataGuard} from './sales/guards/sales-data.guard';


const routes: Routes = [{
    path: '',
    children: [
        {
            path: 'apprenant/proposition',
            resolve: {
                data: FunnelDataResolver,
            },
            component: AttendeeProposalsComponent
        },
        {
            path: 'apprenant/proposition/:code',
            resolve: {
                data: FunnelDataResolver,
            },
            component: AttendeeProposalsComponent
        },
        {
            path: 'commercial/proposition',
            canActivate: [AuthGuard, SalesDataGuard],
            resolve: {
                data: FunnelDataAuthResolver,
            },
            component: SalesProposalsComponent
        },
        {
            path: 'commercial/proposition/:code',
            canActivate: [AuthGuard, SalesDataGuard],
            resolve: {
                data: FunnelDataAuthResolver,
            },
            component: SalesProposalsComponent
        }
    ]
}];

@NgModule({
    imports: [
        RouterModule.forChild(routes)
    ],
    exports: [RouterModule]
})
export class FunnelRoutingModule {
}
