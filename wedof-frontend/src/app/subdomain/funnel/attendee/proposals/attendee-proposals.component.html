<div class="content-layout bg-white">
    <div class="w-1/3 md:w-2/3 sm:w-2/3 xs:w-100 m-auto">
        <div class="xs:p-1 p-5 overflow-hidden colors-{{organism.siret}}">
            <div class="text-center" *ngIf="configuration.logo else onlyTitle">
                <img [src]="configuration.logo"
                     class="gt-xs:py-5 gt-xs:px-20 xs:p-3 m-auto"/>
            </div>
            <ng-template #onlyTitle>
                <h1 class="text-center">{{organism.name}}</h1>
            </ng-template>
            <div class="my-5 ml-6" [innerHTML]="configuration.description"></div>
            <mat-vertical-stepper #Stepper (animationDone)="setFocus()" (selectionChange)="setTargetId($event)"
                                  *ngIf="!hideStepper else showEndedFunnel" linear>
                <mat-step [editable]="false" completed="false">
                    <ng-template matStepperIcon="phone">
                        <mat-icon>call_end</mat-icon>
                    </ng-template>
                    <ng-template matStepLabel>
                        {{'public.funnel.steps.attendee' | translate}}
                    </ng-template>
                    <form #ngFormStep1="ngForm" (submit)="proposal ? updateProposal() : (funnelForm.get('genericCode').valid ? createProposal() : goToNextStep())"
                          [formGroup]="funnelForm.get('attendee')">
                        <div class="flex flex-col gt-xs:flex-row">
                            <mat-form-field class="flex-auto input-line">
                                <mat-label>{{'public.funnel.firstName' | translate}}</mat-label>
                                <input [placeholder]="'public.funnel.firstName' | translate"
                                       formControlName="firstName"
                                       id="step-focus-0"
                                       matInput required>
                                <mat-icon matPrefix>account_circle</mat-icon>
                                <mat-error class="flex-auto gt-xs:pr-3">
                                    {{'public.funnel.errors.firstName' | translate }}
                                </mat-error>
                            </mat-form-field>
                        </div>
                        <div class="flex flex-col gt-xs:flex-row">
                            <mat-form-field class="flex-auto input-line">
                                <mat-label>{{'public.funnel.lastName' | translate}}</mat-label>
                                <input [placeholder]="'public.funnel.lastName' | translate"
                                       formControlName="lastName"
                                       matInput
                                       required>
                                <mat-icon matPrefix>account_circle</mat-icon>
                                <mat-error class="flex-auto">
                                    {{'public.funnel.errors.lastName' | translate }}
                                </mat-error>
                            </mat-form-field>
                        </div>
                        <div class="flex flex-col gt-xs:flex-row">
                            <mat-form-field class="flex-auto input-line">
                                <mat-label>{{'public.funnel.phoneNumber' | translate}}</mat-label>
                                <input [placeholder]="'public.funnel.phoneNumber' | translate"
                                       formControlName="phoneNumber"
                                       matInput
                                       required>
                                <mat-icon matPrefix>phone</mat-icon>
                                <mat-icon [matTooltipPosition]="'above'"
                                          [matTooltip]="'public.funnel.phoneNumberTooltip' | translate" matSuffix>
                                    help_outline
                                </mat-icon>
                                <mat-error class="flex-auto">
                                    {{'public.funnel.errors.phoneNumber' | translate }}
                                </mat-error>
                            </mat-form-field>
                        </div>
                        <div class="flex flex-col gt-xs:flex-row">
                            <mat-form-field class="flex-auto input-line">
                                <mat-label>{{'auth.sign-up.step1.labels.email' | translate}}</mat-label>
                                <input [placeholder]="'auth.sign-up.step1.placeholders.email' | translate"
                                       formControlName="email"
                                       matInput
                                       required>
                                <mat-icon matPrefix>email</mat-icon>
                                <mat-icon [matTooltipPosition]="'above'"
                                          [matTooltip]="'public.funnel.emailTooltip' | translate" matSuffix>help_outline
                                </mat-icon>
                                <mat-error class="flex-auto">
                                    {{'auth.sign-up.step1.errors.email' | translate }}
                                </mat-error>
                            </mat-form-field>
                        </div>
                        <button [disabled]="ngFormStep1.invalid || funnelForm.disabled"
                                class="w-full"
                                color="primary" mat-flat-button type="submit">
                            <mat-progress-spinner *ngIf="funnelForm.disabled" [diameter]="24" class="mr-4"
                                                  mode="indeterminate"></mat-progress-spinner>
                            <span class="text-center">{{'public.funnel.validate' | translate }}</span>
                        </button>
                    </form>
                </mat-step>
                <mat-step *ngIf="!hideCodeStep" [editable]="false" completed="false">
                    <form (submit)="createProposal()" [formGroup]="funnelForm">
                        <ng-template matStepLabel>
                            {{'public.funnel.steps.code' | translate}}
                        </ng-template>
                        <div class="flex flex-col gt-xs:flex-row">
                            <mat-form-field class="flex-auto input-line">
                                <mat-label>{{'public.funnel.code' | translate}}</mat-label>
                                <input formControlName="genericCode"
                                       id="step-focus-1"
                                       matInput
                                       required>
                                <mat-icon matPrefix>account_circle</mat-icon>
                                <mat-error class="flex-auto">
                                    {{'public.funnel.errors.code' | translate }}
                                </mat-error>
                            </mat-form-field>
                        </div>
                        <button [disabled]="funnelForm.disabled || funnelForm.get('genericCode').invalid"
                                class="w-full" color="primary"
                                mat-flat-button type="submit">
                            <span *ngIf="!funnelForm.disabled"
                                  class="text-center">{{'public.funnel.check' | translate }}</span>
                            <mat-progress-spinner *ngIf="funnelForm.disabled" [diameter]="24"
                                                  [mode]="'indeterminate'"
                                                  class="mr-4"></mat-progress-spinner>
                        </button>
                    </form>
                </mat-step>
                <mat-step [editable]="false" completed="false">
                    <form #ngFormStep3="ngForm" (submit)="selectTrainingAction();"
                          [formGroup]="funnelForm.get('proposal')">
                        <ng-template matStepLabel>
                            {{'public.funnel.steps.trainingAction' | translate}}
                        </ng-template>
                        <div *ngIf="proposal" class="text-secondary">
                            <p [innerHTML]="getProposalText()" class='pb-2 '></p>
                        </div>
                        <div class="flex flex-col gt-xs:flex-row select-training-action">
                            <div class="flex-col flex-auto input-line">
                                <mat-form-field class="w-full">
                                    <mat-select #trainingActionsSelect
                                                [placeholder]="'public.funnel.placeholder.trainingAction' | translate"
                                                formControlName="selectedTrainingAction">
                                        <mat-option>
                                            <ngx-mat-select-search
                                                [formControl]="trainingActionsFilteringCtrl"
                                                [indexAndLengthScreenReaderText]="' sur '"
                                                [noEntriesFoundLabel]="'public.funnel.placeholder.noTrainingAction' | translate"
                                                [placeholderLabel]="'public.funnel.placeholder.trainingAction' | translate"
                                                [searching]="searching"></ngx-mat-select-search>
                                        </mat-option>
                                        <mat-option
                                            *ngFor="let trainingAction of filteredTrainingActions | async"
                                            [value]="trainingAction">
                                            {{trainingAction.trainingTitle}}
                                        </mat-option>
                                    </mat-select>
                                </mat-form-field>
                                <button [disabled]="funnelForm.disabled || ngFormStep3.invalid"
                                        class="w-full" color="primary"
                                        mat-flat-button type="submit">
                                    <span *ngIf="!funnelForm.disabled"
                                          class="text-center">{{'public.funnel.select' | translate }}
                                    </span>
                                    <mat-progress-spinner *ngIf="funnelForm.disabled" [diameter]="24"
                                                          [mode]="'indeterminate'"
                                                          class="mr-4"></mat-progress-spinner>
                                </button>
                            </div>
                            <div *ngIf="this.funnelForm?.get('proposal.selectedTrainingAction')?.value != null"
                                 class="flex-col">
                                <a href="{{getMCFUrlTrainingAction()}}" target="_blank">
                                    <img alt="mcf" class="ml-2" src="../../../../../assets/mcf/mcf.png"
                                         style="width: 95px;"/>
                                </a>
                            </div>
                        </div>
                    </form>
                </mat-step>
                <mat-step [editable]="false" completed="false">
                    <ng-template matStepLabel>
                        {{'public.funnel.steps.folder' | translate}}
                    </ng-template>
                    <div *ngIf="proposal?.selectedTrainingAction" class="text-secondary mb-5">
                        <span [innerHTML]="getProposalText(true)"></span>
                    </div>
                    <div *ngIf="!registrationFolder && !inMCF">
                        <div class="m-auto text-center">
                            <a (click)="openMCF();" class="font-medium"
                               href style="text-decoration: none; color:#000;">
                                {{'public.funnel.gotToMCF' | translate}}
                            </a>
                        </div>
                        <div class="m-auto mt-1 text-center flex flex-row">
                            <a (click)="openMCF()" href class="inline-block mr-2">
                                <img alt='mcf' style="width: 95px;"
                                     src="../../../../../assets/mcf/mcf.png"/>
                            </a>
                            <a (click)="openMCF()" href class="inline-block">
                                <img alt="fc" style="height: 80px;width:auto;" src="../../../../../assets/mcf/fc.svg"/>
                            </a>
                        </div>
                        <div class="mt-5 text-secondary text-md text-center" *ngIf="configuration.financements">
                            Nos formations sont aussi éligibles aux financements : Entreprise, OPCO ou Pôle Emploi et
                            par paiement direct : Carte Bancaire ou Virement.
                            Contactez nous pour en savoir plus :
                            <ng-container *ngIf="organism.phones[0]"><a
                                href="tel:{{organism.phones[0]}}">{{organism.phones[0]}}</a></ng-container>
                            <ng-container *ngIf="organism.phones[0] && organism.emails[0]"> -</ng-container>
                            <ng-container *ngIf="organism.phones[0]"><a
                                href="mailto:{{organism.emails[0]}}">{{organism.emails[0]}}</a></ng-container>
                        </div>
                    </div>
                    <div *ngIf="!registrationFolder && inMCF" class="mt-5">
                        <div class="m-auto font-medium text-center mb-2">
                            <mat-progress-spinner *ngIf="funnelForm.disabled" [diameter]="24"
                                                  [mode]="'indeterminate'"
                                                  class="mr-2 inline-block"></mat-progress-spinner>
                            {{'public.funnel.waitingFolder' | translate}} <br/>
                        </div>
                        <img alt="gif" (click)="openMCF()" class="my-2" src="../../../../../assets/mcf/v1.gif"
                             style="cursor:pointer">
                        <p class="pt-2 font-medium"
                           *ngIf="inMCF && proposal.discountType !== DiscountType.DISCOUNT_TYPE_NONE">
                            {{ proposal?.amount ? 'Merci de ne pas tenir compte du prix affiché lors de la création de votre dossier. Celui-ci sera actualisé lors de la validation de votre dossier.' : 'Votre dossier sera actualisé conformément à cette proposition lors de la validation de celui-ci.' }}
                        </p>
                    </div>
                    <div *ngIf="registrationFolder" class="text-center mt-2">
                        {{'public.funnel.receivedFolder' | translate}}{{ registrationFolder.externalId }} {{ 'public.funnel.receivedFolderSuffix' | translate }}
                    </div>
                </mat-step>
                <mat-step *ngIf="proposal?.autoValidate" [editable]="false" completed="false">
                    <ng-template matStepLabel>
                        {{'public.funnel.steps.process' | translate}}
                    </ng-template>
                    <app-wrapper-spinner [active]="true" class="bg-white"></app-wrapper-spinner>
                    <div class="text-center">
                        {{'public.funnel.processingFolder' | translate}}
                    </div>
                </mat-step>
                <mat-step *ngIf="!proposal?.autoValidate" [editable]="false" completed="false">
                    <ng-template matStepLabel>
                        {{'public.funnel.steps.verification' | translate}}
                    </ng-template>
                    <app-wrapper-spinner [active]="true" class="bg-white"></app-wrapper-spinner>
                    <div *ngIf="registrationFolder">
                        {{'public.funnel.waitingValidation' | translate}}{{ registrationFolder.externalId }}
                    </div>
                </mat-step>
                <mat-step [editable]="false" completed="false">
                    <ng-template matStepLabel>
                        {{'public.funnel.steps.accept' | translate}}
                    </ng-template>
                    <app-wrapper-spinner [active]="true" class="bg-white"></app-wrapper-spinner>
                    <div class="text-center">
                        {{'public.funnel.waitingAcceptationFolder' | translate}}
                    </div>
                    <div class="mt-3 text-center">
                        <a (click)="openMCFAccept()" *ngIf="registrationFolder" color="primary" href mat-flat-button>
                            {{'public.funnel.accept' | translate}}
                        </a>
                    </div>
                </mat-step>
                <mat-step [editable]="false">
                    <ng-template matStepLabel>
                        {{'public.funnel.steps.finished' | translate}}
                    </ng-template>
                    <div *ngIf="registrationFolder">
                        {{'public.funnel.acceptedFolder' | translate}}{{ registrationFolder.externalId }}
                    </div>
                </mat-step>
            </mat-vertical-stepper>
            <ng-template #showEndedFunnel>
                <div class="text-center pt-4">
                    <h4>{{'public.funnel.ended' | translate: {externalId: registrationFolder.externalId} }}</h4>
                </div>
            </ng-template>
            <div class="text-secondary text-center mt-10 text-sm">
                Un service proposé par © <a href="https://www.wedof.fr">Wedof</a> 2025
            </div>
        </div>
    </div>
</div>
