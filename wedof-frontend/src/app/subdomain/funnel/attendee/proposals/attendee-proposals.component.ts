import {After<PERSON>iewInit, Component, Inject, OnChanges, OnDestroy, OnInit, Renderer2, ViewChild} from '@angular/core';
import {MatStepper} from '@angular/material/stepper';
import {FormBuilder, FormControl, FormGroup, Validators} from '@angular/forms';
import {FunnelConfiguration, FunnelService} from '../../../../shared/api/services/funnel.service';
import {debounceTime, delay, filter, map, retryWhen, switchMap, takeUntil, tap} from 'rxjs/operators';
import {RegistrationFolder, RegistrationFolderStates} from '../../../../shared/api/models/registration-folder';
import {ActivatedRoute} from '@angular/router';
import {Organism} from '../../../../shared/api/models/organism';
import {DiscountType, Proposal} from '../../../../shared/api/models/proposal';
import {of, ReplaySubject, Subject} from 'rxjs';
import {MatSelect} from '@angular/material/select';
import {TrainingAction} from '../../../../shared/api/models/training-action';
import {DatePipe, DOCUMENT, Location} from '@angular/common';
import {DomSanitizer} from '@angular/platform-browser';

@Component({
    selector: 'attendee-proposals',
    templateUrl: './attendee-proposals.component.html',
    styleUrls: ['./attendee-proposals.component.scss']
})
export class AttendeeProposalsComponent implements OnInit, AfterViewInit, OnDestroy, OnChanges {

    funnelForm: FormGroup;

    organism: Organism;
    proposal: Proposal;
    DiscountType = DiscountType;

    email: string;
    firstName: string;
    lastName: string;
    phoneNumber: string;

    targetId: string;
    registrationFolderValidated = false;
    registrationFolderAccepted = false;

    paramsSelectedTrainingAction = null;

    filteredTrainingActions: ReplaySubject<TrainingAction[]> = new ReplaySubject<TrainingAction[]>(1);
    trainingActionsFilteringCtrl: FormControl = new FormControl(null);

    searching = false;
    stateToCheck = null;
    hideStepper = false;
    hideCodeStep = false;
    disableSearch = false;
    inMCF = false;

    funnelWindow: Window = null;
    edofWindow: Window = null;

    configuration: FunnelConfiguration = new FunnelConfiguration();

    registrationFolder: RegistrationFolder = null;

    @ViewChild('Stepper') private stepper: MatStepper;
    @ViewChild('trainingActionsSelect', {static: true}) trainingActionsSelect: MatSelect;

    protected _unsubscribeAll = new Subject<void>();

    constructor(private _funnelService: FunnelService,
                private _formBuilder: FormBuilder,
                private _activatedRoute: ActivatedRoute,
                private _location: Location,
                private _route: ActivatedRoute,
                private _renderer: Renderer2,
                private _datePipe: DatePipe,
                private _sanitizer: DomSanitizer,
                @Inject(DOCUMENT) private document: any) {
    }

    public get style(): string {
        return this.configuration.colorScheme ? `
            .treo-theme-light .mat-primary .mat-pseudo-checkbox-checked, .treo-theme-light .mat-primary .mat-pseudo-checkbox-indeterminate {
                background: #${this.configuration.colorScheme} !important;
            }
            .treo-theme-light .primary{
                background: ${this.configuration.colorScheme} !important;
            }
            .treo-theme-light .mat-step-header .mat-step-icon-selected,
            .treo-theme-light .mat-step-header .mat-step-icon-state-done,
            .treo-theme-light .mat-step-header .mat-step-icon-state-edit {
                background-color: ${this.configuration.colorScheme};
            }

            .treo-theme-light .mat-progress-spinner circle, .treo-theme-light .mat-spinner circle, .treo-theme-light .mat-progress-spinner circle, .treo-theme-light .mat-spinner circle {
                background-color: ${this.configuration.colorScheme};
            }

            .treo-theme-light .mat-progress-spinner circle, .treo-theme-light .mat-spinner circle {
                stroke: ${this.configuration.colorScheme};
            }

            .treo-theme-light .mat-input-element {
                caret-color: ${this.configuration.colorScheme};
            }

            .treo-theme-light .mat-form-field.mat-form-field-appearance-fill.mat-focused:not(.mat-form-field-invalid) .mat-form-field-wrapper .mat-form-field-flex {
                border-color: ${this.configuration.colorScheme};
            }

            .treo-theme-light .mat-flat-button.mat-primary, .treo-theme-light .mat-raised-button.mat-primary, .treo-theme-light .mat-fab.mat-primary, .treo-theme-light .mat-mini-fab.mat-primary {
                background-color: ${this.configuration.colorScheme};
            }
            .treo-theme-light .mat-primary .mat-option.mat-selected:not(.mat-option-disabled) {
                color: ${this.configuration.colorScheme};
            }
        ` : '';
    }

    /**
     * On init
     */
    ngOnInit(): void {
        const routeData = this._activatedRoute.snapshot.data?.data;
        this.organism = routeData[0];

        const proposal = routeData[1];
        this.email = proposal.email;
        this.phoneNumber = proposal.phoneNumber;
        this.firstName = proposal.firstName;
        this.lastName = proposal.lastName;

        this.configuration.colorScheme = proposal.customColorScheme ? proposal.customColorScheme : (this.organism.customColorScheme ?? null);
        const logoUrl = proposal.logo ? proposal.logo : (this.organism.logo ?? null);
        this.configuration.logo = logoUrl ? this._sanitizer.bypassSecurityTrustUrl(logoUrl) : null;
        this.configuration.description = this._sanitizer.bypassSecurityTrustHtml(proposal.description ? proposal.description : '');

        // todo remove ASAP
        this.configuration.financements = this.organism.siret === '53222292400039';

        if (proposal.code && proposal.isIndividual) {
            this.proposal = proposal;
        }

        if (proposal.code) {
            this.hideCodeStep = true;
        }

        const styles = this.document.createElement('STYLE') as HTMLStyleElement;
        styles.id = 'dynamic-theme-css';
        styles.innerHTML = this.style;
        this._renderer.appendChild(this.document.head, styles);

        this.funnelForm = this._formBuilder.group({
            attendee: this._formBuilder.group({
                lastName: ['', Validators.required],
                firstName: ['', Validators.required],
                description: [null],
                phoneNumber: ['', [Validators.required, Validators.minLength(10), Validators.maxLength(10)]],
                email: ['', Validators.email],
            }),
            genericCode: ['', [Validators.pattern('^[a-zA-Z0-9_\-]{5,15}$')]],
            proposal: this._formBuilder.group({
                selectedTrainingAction: [null, Validators.required]
            })
        });

        this._route.queryParams.subscribe((params) => {
            const email = params['email'] ? params['email'] : this.email;
            const firstName = params['firstName'] ? params['firstName'] : this.firstName;
            const lastName = params['lastName'] ? params['lastName'] : this.lastName;

            let phoneNumber = params['phoneNumber'] ? params['phoneNumber'] : this.phoneNumber;
            if (phoneNumber) {
                phoneNumber = phoneNumber.replace(/\s/g, '');
                phoneNumber = phoneNumber.replace('\+33', '0');
                if (phoneNumber.startsWith('33')) {
                    phoneNumber = phoneNumber.substring(phoneNumber, 2);
                }
                if (phoneNumber.length === 9) {
                    phoneNumber = '0' + phoneNumber;
                }
            }
            this.funnelForm.get('attendee.phoneNumber').setValue(phoneNumber ?? '');
            this.funnelForm.get('attendee.firstName').setValue(firstName ?? '');
            this.funnelForm.get('attendee.lastName').setValue(lastName ?? '');
            this.funnelForm.get('attendee.email').setValue(email ?? '');
            this.funnelForm.get('attendee.description').setValue(params['description'] ?? null);
            this.funnelForm.get('genericCode').setValue(routeData[1].code ?? '');
            if (params['trainingAction'] && !this.proposal) {
                this.paramsSelectedTrainingAction = params['trainingAction'];
            }
            this.inMCF = params['process'] ?? false;
        });
    }

    ngAfterViewInit(): void {
        if (this.proposal && this.proposal.firstName != null && this.proposal.lastName != null && this.proposal.phoneNumber != null && this.proposal.email != null) {
            if (this.inMCF) {
                this.funnelWindow = window.open('', 'funnelWindow');
            }
            setTimeout(() => {
                this.funnelForm.disable();
                this.goToNextStep();
                if (!this.hideCodeStep) {
                    this.goToNextStep();
                }
                if (this.proposal.selectedTrainingAction && (this.inMCF || this.proposal.trainingActions.length === 1)) {
                    this.filteredTrainingActions.next(this.proposal.trainingActions);
                    this.trainingActionsFilteringCtrl.setValue(this.proposal.selectedTrainingAction.trainingTitle);
                    this.funnelForm.get('proposal').patchValue(this.proposal);
                    this.goToNextStep();
                    this.processing();
                } else if (this.proposal.selectedTrainingAction) {
                    this.disableSearch = true;
                    this._funnelService.findTrainingActions('').pipe(
                        map(filteredTrainingActions => {
                            this.filteredTrainingActions.next(filteredTrainingActions.payload);
                            this.proposal.selectedTrainingAction = filteredTrainingActions.payload.find((trainingAction) => {
                                return trainingAction.externalId === this.proposal.selectedTrainingAction.externalId;
                            });
                            if (this.proposal.selectedTrainingAction) {
                                this.trainingActionsFilteringCtrl.setValue(this.proposal.selectedTrainingAction.trainingTitle);
                            }
                            this.funnelForm.get('proposal').patchValue(this.proposal);
                            this.disableSearch = false;
                            return this.proposal;
                        }),
                        switchMap(() => {
                            return this._funnelService.retrieveFolder();
                        }),
                        map((registrationFolder: RegistrationFolder) => {
                            if (registrationFolder) {
                                this.goToNextStep();
                                this.processing();
                            } else {
                                this.funnelForm.enable();
                            }
                        })
                    ).subscribe();
                } else {
                    setTimeout(() => {
                        this.funnelForm.enable();
                    });
                }
            });
        } else if (this.paramsSelectedTrainingAction) {
            this.disableSearch = true;
        }
        this.trainingActionsFilteringCtrl.valueChanges
            .pipe(
                filter(search => !!search && !this.disableSearch),
                tap(() => this.searching = true),
                takeUntil(this._unsubscribeAll),
                debounceTime(200),
                switchMap(search => {
                    return this._funnelService.findTrainingActions(search);
                }),
                takeUntil(this._unsubscribeAll)
            )
            .subscribe(filteredTrainingActions => {
                this.searching = false;
                this.filteredTrainingActions.next(filteredTrainingActions.payload);
            });
    }

    ngOnChanges(): void {
        const styles = this.document.getElementById('dynamic-theme-css');
        if (styles) {
            styles.innerHTML = this.style;
        }
    }

    ngOnDestroy(): void {
        const styles = this.document.getElementById('dynamic-theme-css');
        if (styles) {
            this.document.removeElement(styles);
        }
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    updateProposal(): void {
        this._funnelService.update({
            code: this.proposal.code,
            lastName: this.funnelForm.get('attendee.lastName').value,
            firstName: this.funnelForm.get('attendee.firstName').value,
            phoneNumber: this.funnelForm.get('attendee.phoneNumber').value,
            email: this.funnelForm.get('attendee.email').value
        }).subscribe((proposal: Proposal) => {
            this.afterCreateOrUpdateProposal(proposal);
        });
    }

    createProposal(): void {
        if (this.funnelForm.get('attendee').valid && this.funnelForm.get('genericCode').valid) {
            this.funnelForm.disable();
            this._funnelService.start({
                genericCode: this.funnelForm.get('genericCode').value,
                attendee: {
                    lastName: this.funnelForm.get('attendee.lastName').value,
                    firstName: this.funnelForm.get('attendee.firstName').value,
                    description: this.funnelForm.get('attendee.description').value,
                    phoneNumber: this.funnelForm.get('attendee.phoneNumber').value,
                    email: this.funnelForm.get('attendee.email').value,
                }
            }).subscribe((proposal) => {
                this.afterCreateOrUpdateProposal(proposal);
            }, () => {
                this.funnelForm.enable();
                this.funnelForm.get('genericCode').setErrors({invalid: true});
            });
        }
    }

    afterCreateOrUpdateProposal(proposal: Proposal): void {
        this._location.replaceState('/funnel/apprenant/proposition/' + proposal.code);
        this.proposal = proposal;
        this.funnelForm.get('proposal').patchValue(this.proposal);
        this.paramsSelectedTrainingAction = this.paramsSelectedTrainingAction ? this.paramsSelectedTrainingAction :
            (this.proposal.selectedTrainingAction ? this.proposal.selectedTrainingAction.externalId : '');
        this._funnelService.findTrainingActions(this.paramsSelectedTrainingAction).subscribe(filteredTrainingActions => {
            this.filteredTrainingActions.next(filteredTrainingActions.payload);
            this.funnelForm.enable();
            this.goToNextStep();
            if (filteredTrainingActions && this.paramsSelectedTrainingAction) {
                const selectedTrainingAction = filteredTrainingActions.payload.find((trainingAction) => {
                    return trainingAction.externalId === this.paramsSelectedTrainingAction;
                });
                if (selectedTrainingAction) {
                    this.proposal.selectedTrainingAction = selectedTrainingAction;
                    this.funnelForm.get('proposal').patchValue(this.proposal);
                    this.trainingActionsFilteringCtrl.setValue(selectedTrainingAction.trainingTitle);
                    this.selectTrainingAction();
                }
            }
        });
    }

    selectTrainingAction(): void {
        this.funnelForm.disable();
        this.proposal.selectedTrainingAction = this.funnelForm.get('proposal.selectedTrainingAction').value;
        this._funnelService.select(this.proposal).subscribe((proposal) => {
            if (proposal.selectedTrainingAction.externalId === this.proposal.selectedTrainingAction.externalId) {
                this.goToNextStep();
                this.processing();
            }
        });
    }

    processing(): void {
        this.funnelForm.disable();
        of({})
            .pipe(
                switchMap(() => {
                    return this._funnelService.retrieveFolder(this.stateToCheck ? this.stateToCheck : '');
                })
            )
            .pipe(
                map((registrationFolder: RegistrationFolder) => {
                    if (!registrationFolder) {
                        throw false;
                    } else {
                        return registrationFolder;
                    }
                }),
                retryWhen(errors =>
                    errors.pipe(delay(20000))
                ),
                map((registrationFolder: RegistrationFolder) => {
                    switch (registrationFolder.state) {
                        case RegistrationFolderStates.NOT_PROCESSED:
                            if (!this.registrationFolder) {
                                this.registrationFolder = registrationFolder;
                                this.stepper.selected.completed = true;
                                this.stepper.next(); // notProcessed
                                this.tryFocusFunnelWindow();
                            }
                            this.stateToCheck = RegistrationFolderStates.VALIDATED;
                            throw false;
                        case RegistrationFolderStates.VALIDATED:
                            if (!this.registrationFolder) {
                                this.registrationFolder = registrationFolder;
                                this.goToNextStep();
                                this.tryFocusFunnelWindow();
                            }
                            if (!this.registrationFolderValidated) {
                                this.registrationFolderValidated = true;
                                this.goToNextStep();
                                this.tryFocusEdofWindow();
                                this.openMCFAccept();
                            }
                            this.stateToCheck = RegistrationFolderStates.ACCEPTED;
                            throw false;
                        case RegistrationFolderStates.ACCEPTED:
                            if (!this.registrationFolder) {
                                this.registrationFolder = registrationFolder;
                                this.goToNextStep();
                                this.tryFocusFunnelWindow();
                            }
                            if (!this.registrationFolderValidated) {
                                this.registrationFolderValidated = true;
                                this.goToNextStep();
                                this.tryFocusEdofWindow();
                            }
                            if (!this.registrationFolderAccepted) {
                                this.registrationFolderAccepted = true;
                                this.goToNextStep();
                                this.tryFocusFunnelWindow();
                            }
                            this.stateToCheck = null;
                            break;
                        default:
                            this.registrationFolder = registrationFolder;
                            this.hideStepper = true;
                            break;
                    }
                    return registrationFolder;
                }),
                retryWhen(errors =>
                    errors.pipe(delay(20000))
                )
            )
            .subscribe();
    }

    setFocus(): void {
        const targetElem = document.getElementById(this.targetId ?? `step-focus-0`);
        if (targetElem) {
            targetElem.focus();
        }
    }

    setTargetId(event: any): void {
        this.targetId = `step-focus-${event.selectedIndex}`;
    }

    openMCF(): boolean {
        const widthFunnel = 500;
        const height = screen.height;
        const width = screen.width > 1680 ? 1680 : screen.width;
        let marginLeft = 0;
        if (width !== screen.width) {
            marginLeft = (screen.width - width) / 2;
        }
        if (!this.inMCF) {
            // mcf will be open by the new window
            this.funnelWindow = window.open(location.href + '?process=true',
                'funnelWindow', 'height=' + height + ', width=' + widthFunnel + ', top=0, left=' + marginLeft);
            this.openEdofWindow();
            if (this.edofWindow) {
                document.location.replace('https://www.google.fr');
            } else {
                document.location.replace(this.getMCFUrlTrainingAction(true));
            }
            return false;
        } else {
            this.edofWindow = window.open(this.getMCFUrlTrainingAction(),
                'edofWindow', 'height=' + height + ', width=' + (width - widthFunnel) + ', top=0, left=' + (marginLeft + widthFunnel) + '');
            this.tryFocusEdofWindow();
        }
        return false;
    }

    openMCFAccept(): boolean {
        const height = screen.height;
        const width = (screen.width > 1680 ? 1680 : screen.width) - this.funnelWindow.outerWidth;
        const left = this.funnelWindow.screenX + this.funnelWindow.outerWidth;
        this.edofWindow = window.open('https://www.moncompteformation.gouv.fr/espace-prive/html/#/dossiers/v2/'
            + this.registrationFolder.externalId + '/detail/financement', 'edofWindow', 'height=' + height + ', width=' + width + ', top=0, left=' + left);
        this.tryFocusEdofWindow();
        return false;
    }

    getMCFUrlTrainingAction(login?: boolean, trainingAction?: TrainingAction): string {
        trainingAction = trainingAction ?? this.funnelForm.get('proposal.selectedTrainingAction').value;
        if (login && trainingAction) {
            return 'https://www.moncompteformation.gouv.fr/espace-prive/html/#/compte-utilisateur/connexion-fc?'
                + 'scope=dossier&redirectTo=%2Fformation%2Frecherche%2F' + trainingAction.externalId + '&pop=true';
        } else if (trainingAction) {
            return 'https://www.moncompteformation.gouv.fr/espace-prive/html/#/formation/recherche/' + trainingAction.externalId;
        } else {
            return '';
        }
    }

    goToNextStep(): void {
        this.stepper.selected.completed = true;
        this.stepper.next();
    }

    getProposalText(useSelectedTrainingAction: boolean = false): string {
        let text = 'Cette proposition';
        if (this.proposal.expire) {
            text += ' valable jusqu\'au <b>' + this._datePipe.transform(this.proposal.expire, 'mediumDate') + '</b>';
        }
        text += ' vous permet ';
        if (this.proposal.discountType === DiscountType.DISCOUNT_TYPE_PRICE) {
            text += ' de profiter d\'un tarif personnalisé de <b>' + this.proposal.amount + '</b>€ TTC à valoir sur';
        } else if (this.proposal.discountType === DiscountType.DISCOUNT_TYPE_PERCENT) {
            if (this.proposal.amount >= 0) {
                text += ' de profiter d\'un <b>tarif personnalisé (+' + this.proposal.amount + '%)</b> à valoir sur';
            } else {
                text += ' de profiter d\'une <b>remise (' + this.proposal.amount + '%)</b> à valoir sur';
            }
        } else if (this.proposal.discountType === DiscountType.DISCOUNT_TYPE_AMOUNT) {
            if (this.proposal.amount >= 0) {
                text += ' de profiter d\'un <b>tarif personnalisé (+' + this.proposal.amount + '€ TTC)</b> à valoir sur';
            } else {
                text += ' de profiter d\'une <b>remise  (-' + this.proposal.amount + '€ TTC)</b> à valoir sur';
            }
        } else if (this.proposal.discountType === DiscountType.DISCOUNT_TYPE_NONE) {
            text += ' de vous inscrire à ';
        }
        if ((this.proposal.selectedTrainingAction && useSelectedTrainingAction) || (this.proposal.trainingActions && this.proposal.trainingActions.length === 1)) {
            const trainingAction = (this.proposal.selectedTrainingAction && useSelectedTrainingAction) ? this.proposal.selectedTrainingAction : this.proposal.trainingActions[0];
            text += ' la formation : <b>' + trainingAction.trainingTitle + '</b>';
            if (this.proposal.sessionStartDate) {
                text += ' commencant le <b>' + this._datePipe.transform(this.proposal.sessionStartDate, 'mediumDate') + '</b>';
            }
            if (this.proposal.sessionEndDate) {
                text += ' et se terminant le <b>' + this._datePipe.transform(this.proposal.sessionEndDate, 'mediumDate') + '</b>';
            }
        } else {
            text += ' une selection de formations : ';
        }
        return text;
    }

    tryFocusFunnelWindow(): boolean {
        if (this.funnelWindow) {
            this.funnelWindow.focus();
            return true;
        }
        return false;
    }

    tryFocusEdofWindow(): boolean {
        if (this.edofWindow) {
            this.edofWindow.focus();
            return true;
        }
        return false;
    }

    private openEdofWindow(): void {
        const height = screen.height;
        const width = (screen.width > 1680 ? 1680 : screen.width) - this.funnelWindow.outerWidth;
        const left = this.funnelWindow.screenX + this.funnelWindow.outerWidth;
        this.edofWindow = window.open(this.getMCFUrlTrainingAction(true, this.proposal.selectedTrainingAction),
            'edofWindow', 'height=' + height + ', width=' + width + ', top=0, left=' + left);
        this.tryFocusEdofWindow();
    }

    next(): void {
        this.goToNextStep();
    }
}
