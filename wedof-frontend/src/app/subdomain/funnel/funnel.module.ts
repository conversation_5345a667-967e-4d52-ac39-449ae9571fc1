import {NgModule} from '@angular/core';
import {CommonModule, DatePipe} from '@angular/common';

import {FunnelRoutingModule} from './funnel-routing.module';
import {SharedModule} from '../../shared/shared.module';
import {AttendeeProposalsComponent} from './attendee/proposals/attendee-proposals.component';
import {RouterModule} from '@angular/router';
import {SalesProposalsComponent} from './sales/proposals/sales-proposals.component';
import {ClipboardModule} from '@angular/cdk/clipboard';
import {MatTooltipModule} from '@angular/material/tooltip';

@NgModule({
    declarations: [
        AttendeeProposalsComponent,
        SalesProposalsComponent
    ],
    imports: [
        CommonModule,
        FunnelRoutingModule,
        RouterModule,
        SharedModule,
        ClipboardModule,
        MatTooltipModule,
    ],
    providers: [
        DatePipe,
    ],
})
export class FunnelModule {
}
