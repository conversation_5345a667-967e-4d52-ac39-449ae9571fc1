<div class="content-layout bg-white">
    <div class="w-2/5 sm:w-2/3 xs:w-100 m-auto">
        <div class="xs:p-1 p-5 overflow-hidden">
            <div class="px-6">
                <div class="text-center" *ngIf="configuration.logo else onlyTitle">
                    <img [src]="configuration.logo"
                         class="gt-xs:py-5 gt-xs:px-20 xs:p-3 m-auto"/>
                </div>
                <ng-template #onlyTitle>
                    <h1>{{ organism.name }}</h1>
                </ng-template>
                <div class="pb-2 text-right text-sm">
                    {{ 'public.funnel.merchant.connectedAs' | translate: {'name': user.name} }}
                    (
                    <button class="underline" type="button" (click)="signOut()">
                        {{ "common.actions.signOut" | translate: {"name": user.name} }}
                    </button>
                    )
                </div>
                <treo-message *ngIf="!hideStepper" appearance="outline" [showIcon]="false" [type]="'info'">
                    <p class="py-2">
                        {{ 'private.training-organism.common.minDateCpf' | translate }}
                        <b>{{ cpfSessionMinDate | dateZToDayString }}</b>
                    </p>
                </treo-message>
            </div>
            <mat-vertical-stepper #Stepper *ngIf="!hideStepper else showEndedFunnel" linear>
                <mat-step [editable]="true" completed="false">
                    <ng-template matStepLabel>
                        {{ 'public.funnel.merchant.steps.quoteData' | translate }}
                    </ng-template>
                    <form (ngSubmit)="onSubmit()" [formGroup]="funnelForm">
                        <div class="flex flex-col gt-xs:flex-row">
                            <mat-form-field class="flex-auto input-line">
                                <mat-label>{{ 'public.funnel.merchant.trainingActions.title' | translate }}</mat-label>
                                <mat-select #trainingActionsSelect
                                            [formControl]="funnelForm.get('trainingActions')"
                                            [placeholder]="'public.funnel.merchant.trainingActions.placeholder' | translate"
                                            multiple>
                                    <mat-option>
                                        <ngx-mat-select-search
                                            [formControl]="trainingActionsFilteringCtrl"
                                            [indexAndLengthScreenReaderText]="' sur '"
                                            [noEntriesFoundLabel]="'public.funnel.merchant.trainingActions.noTrainingAction' | translate"
                                            [placeholderLabel]="'public.funnel.merchant.trainingActions.placeholder' | translate"
                                            [searching]="searching"></ngx-mat-select-search>
                                    </mat-option>
                                    <mat-option
                                        *ngFor="let trainingAction of filteredTrainingActions | async"
                                        [value]="trainingAction">
                                        {{ trainingAction.externalId | displayCatalogExternalId }}
                                        - {{ trainingAction.trainingTitle }}
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                        </div>
                        <div *ngIf="funnelForm.get('trainingActions').value?.length == 1">
                            <div class="flex flex-col gt-xs:flex-row">
                                <mat-form-field class="flex-auto input-line">
                                    <mat-label
                                        class="mat-form-field font-medium">{{ 'private.training-organism.proposals.form.fields.indicativeDuration.label' | translate }}
                                    </mat-label>
                                    <input type="number" matInput
                                           [placeholder]="funnelForm.get('trainingActions').value[0]?.indicativeDuration"
                                           [formControl]="funnelForm.get('indicativeDuration')"
                                    >
                                </mat-form-field>
                            </div>
                            <label
                                class="mat-form-field font-medium">{{ 'public.funnel.merchant.sessionDates.title' | translate }}</label>
                            <div class="flex flex-col gt-xs:flex-row">
                                <mat-radio-group class="flex w-full pt-2 pb-3" formControlName="setSessionDates">
                                    <mat-radio-button
                                        [value]="true">{{ 'public.funnel.merchant.sessionDates.yes' | translate }}
                                    </mat-radio-button>
                                    <mat-radio-button [value]="false"
                                                      class="mr-3">{{ 'public.funnel.merchant.sessionDates.no' | translate }}
                                    </mat-radio-button>
                                </mat-radio-group>
                            </div>
                        </div>
                        <div
                            [hidden]="funnelForm.get('setSessionDates').value === false || funnelForm.get('trainingActions').value?.length != 1">
                            <div class="flex justify-between">
                                <mat-form-field appearance="fill" class="w-1/2 gt-md:mr-4">
                                    <mat-label>{{ 'public.funnel.merchant.sessionDates.sessionStartDate' | translate }}</mat-label>
                                    <input [matDatepicker]="startDatePicker"
                                           [max]="funnelForm.get('sessionEndDate').value"
                                           [min]="cpfSessionMinDate"
                                           [placeholder]="'public.funnel.merchant.sessionDates.sessionStartDate' | translate"
                                           [required]="funnelForm.get('sessionEndDate').value"
                                           formControlName="sessionStartDate"
                                           matInput>
                                    <button (click)="funnelForm.get('sessionStartDate').setValue(null);"
                                            *ngIf="funnelForm.get('sessionStartDate').value"
                                            aria-label="Clear"
                                            class="mr-2"
                                            mat-button mat-icon-button matSuffix>
                                        <mat-icon>close</mat-icon>
                                    </button>
                                    <mat-datepicker-toggle [for]="startDatePicker" matSuffix></mat-datepicker-toggle>
                                    <mat-datepicker #startDatePicker></mat-datepicker>
                                    <mat-error class="flex-auto">
                                        {{ 'public.funnel.merchant.sessionDates.errors.sessionStartDate' | translate }}
                                    </mat-error>
                                </mat-form-field>
                                <mat-form-field appearance="fill" class="w-1/2 gt-md:ml-2">
                                    <mat-label>{{ 'public.funnel.merchant.sessionDates.sessionEndDate' | translate }}</mat-label>
                                    <input [matDatepicker]="endDatePicker"
                                           [min]="this.funnelForm.get('sessionStartDate')?.value ? this.funnelForm.get('sessionStartDate').value : cpfSessionMinDate"
                                           [placeholder]="'public.funnel.merchant.sessionDates.sessionEndDate' | translate"
                                           formControlName="sessionEndDate"
                                           matInput>
                                    <button (click)="funnelForm.get('sessionEndDate').setValue(null);"
                                            *ngIf="funnelForm.get('sessionEndDate').value"
                                            aria-label="Clear"
                                            class="mr-2"
                                            mat-button mat-icon-button matSuffix>
                                        <mat-icon>close</mat-icon>
                                    </button>
                                    <mat-datepicker-toggle [for]="endDatePicker" matSuffix></mat-datepicker-toggle>
                                    <mat-datepicker #endDatePicker></mat-datepicker>
                                    <mat-error class="flex-auto">
                                        {{ 'public.funnel.merchant.sessionDates.errors.sessionEndDate' | translate }}
                                    </mat-error>
                                </mat-form-field>
                            </div>
                        </div>
                        <div>
                            <label class="mat-form-field"
                                   style="color:#27303f; font-weight: 500">{{ 'public.funnel.merchant.discountType.title' | translate }}</label>
                            <div class="flex flex-col gt-xs:flex-row">
                                <mat-radio-group (change)="funnelForm.get('amount').setValue(null)"
                                                 class="flex w-full pt-2 pb-3"
                                                 formControlName="discountType">
                                    <mat-radio-button class="mr-3"
                                                      [value]="DiscountType.DISCOUNT_TYPE_NONE">{{ 'public.funnel.merchant.discountType.none' | translate }}
                                    </mat-radio-button>
                                    <mat-radio-button *ngIf="funnelForm.get('trainingActions').value?.length == 1"
                                                      class="mr-3"
                                                      [value]="DiscountType.DISCOUNT_TYPE_PRICE">{{ 'public.funnel.merchant.discountType.fixed' | translate }}
                                    </mat-radio-button>
                                    <mat-radio-button class="mr-3"
                                                      [value]="DiscountType.DISCOUNT_TYPE_PERCENT">{{ 'public.funnel.merchant.discountType.percent' | translate }}
                                    </mat-radio-button>
                                    <mat-radio-button class="mr-3"
                                                      [value]="DiscountType.DISCOUNT_TYPE_AMOUNT">{{ 'public.funnel.merchant.discountType.amount' | translate }}
                                    </mat-radio-button>
                                </mat-radio-group>
                            </div>
                            <div class="flex flex-row">
                                <div
                                    [hidden]="funnelForm.get('discountType').value !== DiscountType.DISCOUNT_TYPE_PRICE"
                                    class="flex-auto">
                                    <div class="flex flex-col">
                                        <mat-form-field
                                            [hidden]="funnelForm.get('discountType').value !== DiscountType.DISCOUNT_TYPE_PRICE"
                                            class="flex-auto input-line">
                                            <input
                                                [max]="funnelForm.get('trainingActions').value[0]?.totalTvaTTC * 1.15"
                                                [min]="0"
                                                [placeholder]="funnelForm.get('trainingActions').value[0]?.totalTvaTTC"
                                                [required]="funnelForm.get('discountType').value === DiscountType.DISCOUNT_TYPE_PRICE"
                                                class="text-right"
                                                formControlName="amount"
                                                matInput
                                                type="number">
                                            <span matSuffix>&nbsp;€ TTC</span>
                                            <mat-error class="flex-auto">
                                                {{ 'public.funnel.merchant.discountType.errors.fixed' | translate }}
                                            </mat-error>
                                        </mat-form-field>
                                    </div>
                                </div>
                                <div
                                    [hidden]="funnelForm.get('discountType').value !== DiscountType.DISCOUNT_TYPE_PERCENT"
                                    class="flex-auto">
                                    <div class="flex flex-col">
                                        <mat-form-field class="flex-auto input-line">
                                            <input
                                                [required]="funnelForm.get('discountType').value === DiscountType.DISCOUNT_TYPE_PERCENT"
                                                class="text-right"
                                                formControlName="amount"
                                                matInput
                                                type="number">
                                            <span matPrefix>
                                                    <mat-select [(value)]="amountMinusPlus">
                                                        <mat-option value="-">Réduction de</mat-option>
                                                        <mat-option value="+">Augmentation de</mat-option>
                                                    </mat-select>
                                                </span>
                                            <span matSuffix>&nbsp;%</span>
                                            <mat-error class="flex-auto">
                                                {{ 'public.funnel.merchant.discountType.errors.percent' | translate }}
                                            </mat-error>
                                        </mat-form-field>
                                    </div>
                                </div>
                                <div
                                    [hidden]="funnelForm.get('discountType').value !== DiscountType.DISCOUNT_TYPE_AMOUNT"
                                    class="flex-auto">
                                    <div class="flex flex-col">
                                        <mat-form-field class="flex-auto input-line">
                                            <input
                                                [required]="funnelForm.get('discountType').value === DiscountType.DISCOUNT_TYPE_AMOUNT"
                                                class="text-right"
                                                formControlName="amount"
                                                matInput
                                                type="number">
                                            <span matPrefix>
                                                <mat-select [(value)]="amountMinusPlus">
                                                    <mat-option value="-">Réduction de</mat-option>
                                                    <mat-option value="+">Augmentation de</mat-option>
                                                </mat-select>
                                            </span>
                                            <span matSuffix>&nbsp;€</span>
                                            <mat-error class="flex-auto">
                                                {{ 'public.funnel.merchant.discountType.errors.amount' | translate }}
                                            </mat-error>
                                        </mat-form-field>
                                    </div>
                                </div>
                            </div>
                            <div class="flex flex-col gt-xs:flex-row">
                                <mat-form-field class="flex-auto input-line">
                                    <mat-label>{{ 'public.funnel.merchant.notes.title' | translate }}</mat-label>
                                    <textarea [placeholder]="'public.funnel.merchant.notes.placeholder' | translate"
                                              formControlName="notes"
                                              matInput></textarea>
                                </mat-form-field>
                            </div>
                        </div>
                        <div class="pt-5 pb-5">
                            <div class="flex justify-between">
                                <mat-form-field class="gt-md:w-1/2 xs:w-full gt-md:mr-4 input-line">
                                    <mat-label>{{ 'public.funnel.firstName' | translate }}</mat-label>
                                    <input [placeholder]="'public.funnel.firstName' | translate"
                                           formControlName="firstName"
                                           matInput>
                                    <mat-icon matPrefix>account_circle</mat-icon>
                                    <mat-error class="flex-auto gt-xs:pr-3">
                                        {{ 'public.funnel.errors.firstName' | translate }}
                                    </mat-error>
                                </mat-form-field>
                                <mat-form-field class="gt-md:w-1/2 xs:w-full gt-md:ml-2">
                                    <mat-label>{{ 'public.funnel.lastName' | translate }}</mat-label>
                                    <input [placeholder]="'public.funnel.lastName' | translate"
                                           formControlName="lastName"
                                           matInput>
                                    <mat-icon matPrefix>account_circle</mat-icon>
                                    <mat-error class="flex-auto">
                                        {{ 'public.funnel.errors.lastName' | translate }}
                                    </mat-error>
                                </mat-form-field>
                            </div>
                            <div class="flex justify-between">
                                <mat-form-field class="gt-md:w-1/2 xs:w-full gt-md:mr-4 input-line">
                                    <mat-label>{{ 'public.funnel.phoneNumber' | translate }}</mat-label>
                                    <input [placeholder]="'public.funnel.phoneNumber' | translate"
                                           formControlName="phoneNumber"
                                           matInput>
                                    <mat-icon matPrefix>phone</mat-icon>
                                    <mat-icon [matTooltipPosition]="'above'"
                                              [matTooltip]="'public.funnel.merchant.phoneNumberTooltip' | translate"
                                              matSuffix>help_outline
                                    </mat-icon>
                                    <mat-error class="flex-auto">
                                        {{ 'public.funnel.errors.phoneNumber' | translate }}
                                    </mat-error>
                                </mat-form-field>
                                <mat-form-field class="gt-md:w-1/2 xs:w-full gt-md:ml-2">
                                    <mat-label>{{ 'auth.sign-up.step1.labels.email' | translate }}</mat-label>
                                    <input [placeholder]="'auth.sign-up.step1.placeholders.email' | translate"
                                           formControlName="email"
                                           matInput
                                           required>
                                    <mat-icon matPrefix>email</mat-icon>
                                    <mat-icon [matTooltipPosition]="'above'"
                                              [matTooltip]="'public.funnel.merchant.emailTooltip' | translate"
                                              matSuffix>help_outline
                                    </mat-icon>
                                    <mat-error class="flex-auto">
                                        {{ 'auth.sign-up.step1.errors.email' | translate }}
                                    </mat-error>
                                </mat-form-field>
                            </div>
                        </div>
                        <div class="flex flex-col gt-xs:flex-row">
                            <mat-form-field class="flex-auto input-line">
                                <mat-label>{{ 'public.funnel.merchant.description.title' | translate }}</mat-label>
                                <textarea [placeholder]="'public.funnel.merchant.description.placeholder' | translate"
                                          formControlName="description"
                                          matInput></textarea>
                            </mat-form-field>
                        </div>
                        <div class="flex">
                            <div class="gt-sm:mr-2 gt-sm:w-1/2"
                                 *ngIf="this.proposal?.state !== proposalStates.ACTIVE && !funnelForm.disabled">
                                <button [disabled]="funnelForm.disabled || funnelForm.invalid"
                                        color="primary"
                                        class="w-full"
                                        (click)="funnelForm.get('state').setValue(proposalStates.ACTIVE);"
                                        mat-flat-button type="submit">
                            <span *ngIf="!funnelForm.disabled"
                                  class="text-center">{{ (this.proposal ? 'public.funnel.merchant.updateAndActive' : 'public.funnel.merchant.createAndActive') | translate }}</span>
                                    <mat-progress-spinner *ngIf="funnelForm.disabled" [diameter]="24"
                                                          [mode]="'indeterminate'"
                                                          class="mr-4"></mat-progress-spinner>
                                </button>
                            </div>
                            <div class="gt-sm:ml-2"
                                 [ngClass]="{'gt-sm:w-1/2': this.proposal?.state !== proposalStates.ACTIVE && !funnelForm.disabled, 'w-full': (this.proposal?.state === proposalStates.ACTIVE || funnelForm.disabled)}">
                                <button [disabled]="funnelForm.disabled || funnelForm.invalid"
                                        class="w-full"
                                        mat-flat-button type="submit">
                            <span *ngIf="!funnelForm.disabled"
                                  class="text-center">{{ (this.proposal ? 'public.funnel.merchant.update' : 'public.funnel.merchant.create') | translate }}</span>
                                    <mat-progress-spinner *ngIf="funnelForm.disabled" [diameter]="24"
                                                          [mode]="'indeterminate'"
                                                          class="mr-4"></mat-progress-spinner>
                                </button>
                            </div>
                        </div>
                    </form>
                </mat-step>
                <mat-step [editable]="false" completed="false">
                    <ng-template matStepLabel>
                        {{ 'public.funnel.merchant.steps.quoteStatus' | translate }}
                    </ng-template>
                    <p *ngIf="proposal" [innerHTML]="getProposalText()" class='pb-2'></p>
                    <div class="flex flex-col gt-xs:flex-row mt-5"
                         *ngIf="proposal && proposal.state === proposalStates.ACTIVE">
                        <mat-form-field appearance="fill" class="flex-auto input-line">
                            <input [value]="proposal.link"
                                   matInput
                                   readonly="readonly"
                                   type="text">
                            <button [cdkCopyToClipboard]="proposal.link"
                                    class="flex-shrink"
                                    mat-button mat-icon-button matSuffix>
                                <mat-icon>file_copy</mat-icon>
                            </button>
                        </mat-form-field>
                    </div>
                    <div class="flex justify-between"
                         *ngIf="!registrationFolder && proposal && proposal.state === proposalStates.ACTIVE">
                        <div class="mr-2">
                            <a class="primary"
                               href="mailto:{{proposal.email}}?subject=[{{organism.name}}] Votre proposition de formation personnalisée{{organism.emails.length > 0 ? '&bcc='+organism.emails[0] : ''}}&body={{getBodyEmail()}}"
                               mat-button
                               target="_blank">
                                {{ 'public.funnel.merchant.send' | translate }}
                            </a>
                        </div>
                        <button mat-icon-button [matMenuTriggerFor]="menu" [disabled]="requestInProgress">
                            <mat-icon>more_vert</mat-icon>
                        </button>
                    </div>
                    <mat-menu #menu="matMenu">
                        <ng-template matMenuContent>
                            <button mat-menu-item *ngIf="proposal.state === proposalStates.DRAFT"
                                    (click)="toState(proposalStates.ACTIVE); requestInProgress = true;">
                                {{ 'public.funnel.merchant.active' | translate }}
                            </button>
                            <a mat-menu-item *ngIf="proposal.state === proposalStates.ACTIVE"
                               href="mailto:{{proposal.email}}?subject=[{{organism.name}}] Votre proposition de formation personnalisée{{organism.emails.length > 0 ? '&bcc='+organism.emails[0] : ''}}&body={{getBodyEmail()}}"
                               target="_blank">
                                {{ 'public.funnel.merchant.send' | translate }}
                            </a>
                            <button mat-menu-item *ngIf="proposal.state !== proposalStates.DRAFT"
                                    (click)="toState(proposalStates.DRAFT); requestInProgress = true;">
                                {{ 'public.funnel.merchant.draft' | translate }}
                            </button>
                            <button mat-menu-item *ngIf="proposal.state !== proposalStates.REFUSED "
                                    (click)="toState(proposalStates.REFUSED); requestInProgress = true;">
                                {{ 'public.funnel.merchant.refused' | translate }}
                            </button>
                        </ng-template>
                    </mat-menu>
                    <div class="pt-5 text-center"
                         *ngIf="proposal && proposal.state !== proposalStates.ACTIVE && proposal.state !== proposalStates.VIEWED">
                        Proposition à l'état :
                        <b>{{ 'public.funnel.merchant.proposal.states.' + proposal.state | translate }}</b>.
                        Celle-ci doit être <b>active pour être
                        utilisée</b>.
                    </div>
                    <div class="pt-5"
                         *ngIf="proposal?.state === proposalStates.ACTIVE || proposal?.state === proposalStates.VIEWED">
                        <app-wrapper-spinner [active]="true" class="bg-white"></app-wrapper-spinner>
                        <div *ngIf="!registrationFolder && proposal" class="text-center">
                        <span
                            *ngIf="proposal.firstName && proposal.lastName && proposal.phoneNumber; else onlyEmail">
                            {{
                                'public.funnel.merchant.spinner.waitingFolder' | translate:{
                                    firstName: proposal.firstName,
                                    lastName: proposal.lastName,
                                    phoneNumber: proposal.phoneNumber
                                }
                            }} <a href="tel:{{proposal.phoneNumber}}" target="_blank">({{ proposal.phoneNumber }})</a>
                        </span>
                            <ng-template #onlyEmail class="font-bold">
                                {{
                                    'public.funnel.merchant.spinner.waitingFolder-onlyEmail' | translate:{
                                        email: proposal.email
                                    }
                                }}
                            </ng-template>
                        </div>
                    </div>
                </mat-step>
                <mat-step *ngIf="!proposal?.autoValidate" [editable]="false" completed="false">
                    <ng-template matStepLabel>
                        {{ 'public.funnel.merchant.steps.verificationFolder' | translate }}
                    </ng-template>
                    <ng-container *ngIf="!registrationFolderValidated">
                        <app-wrapper-spinner [active]="true" class="bg-white"></app-wrapper-spinner>
                        <div *ngIf="registrationFolder" class="text-center">
                            {{ 'public.funnel.merchant.spinner.waitingVerificationFolder' | translate:{externalId: registrationFolder.externalId} }}
                        </div>
                    </ng-container>
                </mat-step>
                <mat-step *ngIf="proposal?.autoValidate" [editable]="false" completed="false">
                    <ng-template matStepLabel>
                        {{ 'public.funnel.merchant.steps.validateFolder' | translate }}
                    </ng-template>
                    <ng-container *ngIf="!registrationFolderValidated">
                        <app-wrapper-spinner [active]="true" class="bg-white"></app-wrapper-spinner>
                        <div *ngIf="registrationFolder" class="text-center">
                            {{ 'public.funnel.merchant.spinner.validatingFolder' | translate:{externalId: registrationFolder.externalId} }}
                        </div>
                    </ng-container>
                </mat-step>
                <mat-step [editable]="false" completed="false">
                    <ng-template matStepLabel>
                        {{ 'public.funnel.merchant.steps.acceptFolder' | translate }}
                    </ng-template>
                    <ng-container *ngIf="!registrationFolderAccepted">
                        <app-wrapper-spinner [active]="true" class="bg-white"></app-wrapper-spinner>
                        <div *ngIf="registrationFolder" class="text-center">
                            {{
                                'public.funnel.merchant.spinner.waitingAcceptationFolder' | translate:{
                                    externalId: registrationFolder.externalId,
                                    firstName: registrationFolder.attendee.firstName,
                                    lastName: registrationFolder.attendee.lastName,
                                    phoneNumber: registrationFolder.attendee.phoneNumber ? registrationFolder.attendee.phoneNumber : registrationFolder.attendee.phoneFixed
                                }
                            }}
                        </div>
                    </ng-container>
                </mat-step>
                <mat-step [editable]="false" completed="false">
                    <ng-template matStepLabel>
                        {{ 'public.funnel.merchant.steps.acceptedFolder' | translate }}
                    </ng-template>
                    <div *ngIf="registrationFolder && registrationFolder.trainingActionInfo.sessionStartDate"
                         class="text-center">
                        {{
                            'public.funnel.merchant.steps.acceptedFolderText' | translate:{
                                firstName: registrationFolder.attendee.firstName,
                                lastName: registrationFolder.attendee.lastName,
                                title: registrationFolder.trainingActionInfo.title,
                                sessionStartDate: registrationFolder.trainingActionInfo.sessionStartDate| date:'mediumDate',
                                sessionEndDate: registrationFolder.trainingActionInfo.sessionEndDate| date:'mediumDate',
                                totalIncl: registrationFolder.trainingActionInfo.totalIncl
                            }
                        }}
                    </div>
                    <div *ngIf="registrationFolder && !registrationFolder.trainingActionInfo.sessionStartDate"
                         class="text-center">
                        {{
                            'public.funnel.merchant.steps.acceptedFolderTextNoSessionDate' | translate:{
                                firstName: registrationFolder.attendee.firstName,
                                lastName: registrationFolder.attendee.lastName,
                                title: registrationFolder.trainingActionInfo.title,
                                totalIncl: registrationFolder.trainingActionInfo.totalIncl
                            }
                        }}
                    </div>
                </mat-step>
            </mat-vertical-stepper>
            <ng-template #showEndedFunnel>
                <div *ngIf="registrationFolder || proposal._links.registrationFolder; else no_registration_folder"
                     class="text-center pt-4">
                    <h4>
                        Proposition {{ 'private.training-organism.proposals.table.states.' + proposal.state | translate }}
                        avec le dossier
                        n° {{ registrationFolder ? registrationFolder.externalId : proposal._links.registrationFolder.externalId }}
                        dans l'état
                        '{{ (registrationFolder ? 'private.training-organism.folders.common.state.' + registrationFolder.state : 'private.training-organism.folders.common.state.' + proposal._links.registrationFolder.state) | translate }}
                        '</h4>
                </div>
            </ng-template>
            <ng-template #no_registration_folder>
                <div class="text-center pt-4">
                    <h4>{{ 'public.funnel.closed' | translate }}</h4>
                </div>
            </ng-template>
            <div class="text-secondary text-center mt-10 text-sm">
                Un service proposé par © <a href="https://www.wedof.fr">Wedof</a> 2025
            </div>
        </div>
    </div>
</div>
