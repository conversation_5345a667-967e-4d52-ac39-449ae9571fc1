import {<PERSON><PERSON>iew<PERSON>nit, Component, Inject, OnChanges, OnDestroy, OnInit, Renderer2, ViewChild} from '@angular/core';
import {MatStepper} from '@angular/material/stepper';
import {FormBuilder, FormControl, FormGroup, Validators} from '@angular/forms';
import {debounceTime, delay, filter, map, retryWhen, switchMap, takeUntil, tap} from 'rxjs/operators';
import {RegistrationFolder, RegistrationFolderStates} from '../../../../shared/api/models/registration-folder';
import {ActivatedRoute, Router} from '@angular/router';
import {Organism} from '../../../../shared/api/models/organism';
import {DiscountType, Proposal, ProposalStates} from '../../../../shared/api/models/proposal';
import {Observable, of, ReplaySubject, Subject, Subscription} from 'rxjs';
import {MatSelect} from '@angular/material/select';
import {TrainingActionService} from '../../../../shared/api/services/training-action.service';
import {TrainingAction} from '../../../../shared/api/models/training-action';
import {FormValidators} from '../../../../shared/api/shared/form-validators';
import {ProposalService} from '../../../../shared/api/services/proposal.service';
import {RegistrationFolderService} from '../../../../shared/api/services/registration-folder.service';
import {PaginatedResponse} from '../../../../shared/api/services/abstract-paginated.service';
import {DatePipe, DOCUMENT, Location} from '@angular/common';
import {FunnelConfiguration} from '../../../../shared/api/services/funnel.service';
import {DomSanitizer} from '@angular/platform-browser';
import {AuthService} from '../../../../core/auth/auth.service';
import {Select} from '@ngxs/store';
import {UserState} from '../../../../shared/api/state/user.state';
import {User} from '../../../../shared/api/models/user';

@Component({
    selector: 'sales-proposals',
    templateUrl: './sales-proposals.component.html',
    styleUrls: ['./sales-proposals.component.scss']
})
export class SalesProposalsComponent implements OnInit, AfterViewInit, OnDestroy, OnChanges {

    funnelForm: FormGroup;
    organism: Organism;
    proposal: Proposal;
    configuration: FunnelConfiguration = new FunnelConfiguration();
    DiscountType = DiscountType;

    registrationFolderValidated = false;
    registrationFolderAccepted = false;
    trainingActionsFilteringCtrl: FormControl = new FormControl();
    filteredTrainingActions: ReplaySubject<TrainingAction[]> = new ReplaySubject<TrainingAction[]>(1);
    searching = false;
    amountMinusPlus = '-';
    stateToCheck = null;
    processing$: Subscription;
    proposalStates = ProposalStates;
    tags = [];

    @Select(UserState.user) user$: Observable<User>;
    user: User;

    registrationFolder: RegistrationFolder = null;
    @ViewChild('trainingActionsSelect', {static: true}) trainingActionsSelect: MatSelect;
    protected _unsubscribeAll = new Subject<void>();
    @ViewChild('Stepper') private stepper: MatStepper;
    hideStepper = false;
    requestInProgress = false;
    cpfSessionMinDate: Date;

    constructor(private _trainingActionService: TrainingActionService,
                private _proposalService: ProposalService,
                private _formBuilder: FormBuilder,
                private _activatedRoute: ActivatedRoute,
                private _location: Location,
                private _registrationFolderService: RegistrationFolderService,
                private _renderer: Renderer2,
                private _datePipe: DatePipe,
                private _sanitizer: DomSanitizer,
                private _authService: AuthService,
                private _router: Router,
                @Inject(DOCUMENT) private document: any) {
    }

    public get style(): string {
        return this.configuration.colorScheme ? `
            .treo-theme-light .mat-primary .mat-pseudo-checkbox-checked, .treo-theme-light .mat-primary .mat-pseudo-checkbox-indeterminate {
                background: ${this.configuration.colorScheme} !important;
            }
            .treo-theme-light .primary{
                background: ${this.configuration.colorScheme} !important;
            }
            .treo-theme-light .mat-step-header .mat-step-icon-selected,
            .treo-theme-light .mat-step-header .mat-step-icon-state-done,
            .treo-theme-light .mat-step-header .mat-step-icon-state-edit {
                background-color: ${this.configuration.colorScheme};
            }

            .treo-theme-light .mat-progress-spinner circle, .treo-theme-light .mat-spinner circle, .treo-theme-light .mat-progress-spinner circle, .treo-theme-light .mat-spinner circle {
                background-color: ${this.configuration.colorScheme};
            }

            .treo-theme-light .mat-progress-spinner circle, .treo-theme-light .mat-spinner circle {
                stroke: ${this.configuration.colorScheme};
            }

            .treo-theme-light .mat-input-element {
                caret-color: ${this.configuration.colorScheme};
            }

            .treo-theme-light .mat-form-field.mat-form-field-appearance-fill.mat-focused:not(.mat-form-field-invalid) .mat-form-field-wrapper .mat-form-field-flex {
                border-color: ${this.configuration.colorScheme};
            }

            .treo-theme-light .mat-flat-button.mat-primary, .treo-theme-light .mat-raised-button.mat-primary, .treo-theme-light .mat-fab.mat-primary, .treo-theme-light .mat-mini-fab.mat-primary {
                background-color: ${this.configuration.colorScheme};
            }
            .treo-theme-light .mat-primary .mat-option.mat-selected:not(.mat-option-disabled) {
                color: ${this.configuration.colorScheme};
            }
        ` : '';
    }

    /**
     * On init
     */
    ngOnInit(): void {
        this.user$.subscribe(user => {
            this.user = user;
        });
        const routeData = this._activatedRoute.snapshot.data?.data;
        this.organism = routeData[0];
        this.configuration.colorScheme = routeData[1].color ? routeData[1].color : (this.organism.customColorScheme ?? null);
        const logoUrl = routeData[1].logo ? routeData[1].logo : (this.organism.logo ?? null);
        this.configuration.logo = logoUrl ? this._sanitizer.bypassSecurityTrustUrl(logoUrl) : null;

        const styles = this.document.createElement('STYLE') as HTMLStyleElement;
        styles.id = 'dynamic-theme-css';
        styles.innerHTML = this.style;
        this._renderer.appendChild(this.document.head, styles);

        this.funnelForm = this._formBuilder.group({
            expire: [null],
            limitUsage: [1], // TODO config
            notes: [''],
            state: [this.proposalStates.DRAFT],
            description: [''],
            autoValidate: [true], // TODO config
            setSessionDates: [false],
            trainingActions: [[]],
            sessionStartDate: [null, [this.conditionalValidator(() => this.funnelForm.get('sessionEndDate').value, Validators.required)]],
            sessionEndDate: [null],
            indicativeDuration: [null],

            lastName: [null, []],
            firstName: [null, []],
            email: ['', [Validators.required, Validators.email]],
            phoneNumber: ['', [Validators.pattern(FormValidators.PHONE_PATTERN), Validators.minLength(10), Validators.maxLength(10)]],

            discountType: [DiscountType.DISCOUNT_TYPE_NONE, Validators.required],
            amount: [null, [
                this.conditionalAmountMaxValidator(),
                this.conditionalAmountMinValidator(),
                this.conditionalValidator(() => this.funnelForm.get('discountType').value !== DiscountType.DISCOUNT_TYPE_NONE, Validators.required)]
            ]
        });

        this._activatedRoute.queryParams.subscribe((params) => {
            this.funnelForm.get('phoneNumber').setValue(params['phoneNumber'] ?? '');
            this.funnelForm.get('firstName').setValue(params['firstName'] ?? '');
            this.funnelForm.get('lastName').setValue(params['lastName'] ?? '');
            this.funnelForm.get('email').setValue(params['email'] ?? '');
            this.funnelForm.get('notes').setValue(params['notes'] ?? '');
            // hidden
            this.tags = params['tags'] ? params['tags'].split(',') : [];
        });

        if (routeData[1].code) {
            this.proposal = routeData[1];
            if (this.proposal.state === this.proposalStates.ACCEPTED) {
                this.hideStepper = true;
                this.funnelForm.disable();
            } else {
                this.funnelForm.patchValue(this.proposal);
                if (this.proposal.amount != null) {
                    this.funnelForm.get('amount').setValue(Math.abs(this.proposal.amount));
                }
                if (this.proposal.trainingActions.length > 0) {
                    this.filteredTrainingActions.next(this.proposal.trainingActions);
                    let trainingActionsNames = '';
                    this.proposal.trainingActions.forEach(trainingAction =>
                        trainingActionsNames += (trainingAction.trainingTitle) + ', '
                    );
                    this.trainingActionsFilteringCtrl.setValue(trainingActionsNames.slice(0, -2));
                    this.funnelForm.get('trainingActions').setValue(this.proposal.trainingActions);
                }
                if (this.proposal.sessionEndDate || this.proposal.sessionStartDate) {
                    this.funnelForm.get('setSessionDates').setValue(true);
                }
            }
        }

        this._registrationFolderService.fetchSessionMinDatesThroughFunnel().subscribe((sessionMinDates) => {
            this.cpfSessionMinDate = new Date(sessionMinDates.cpfSessionMinDate);
        });
    }

    ngAfterViewInit(): void {
        this.trainingActionsFilteringCtrl.valueChanges
            .pipe(
                filter(search => !!search),
                tap(() => this.searching = true),
                takeUntil(this._unsubscribeAll),
                debounceTime(200),
                switchMap(search => {
                    return this._trainingActionService.listAvailableFromFunnel(search);
                }),
                takeUntil(this._unsubscribeAll)
            )
            .subscribe(filteredTrainingActions => {
                this.searching = false;
                this.filteredTrainingActions.next(filteredTrainingActions.payload);
            });

        this.funnelForm.get('trainingActions').valueChanges.subscribe(trainingActions => {
            if (trainingActions.length === 1 && this.funnelForm.get('discountType').untouched && !this.proposal) {
                setTimeout(() => {
                    this.funnelForm.get('discountType').setValue(DiscountType.DISCOUNT_TYPE_PRICE);
                });
            } else if (trainingActions.length !== 1 && this.funnelForm.get('discountType').value === DiscountType.DISCOUNT_TYPE_PRICE) {
                this.funnelForm.get('discountType').setValue(DiscountType.DISCOUNT_TYPE_NONE);
                this.funnelForm.get('amount').setValue(null);
            } else if (this.proposal?.discountType) {
                this.funnelForm.get('discountType').setValue(this.proposal.discountType);
            }

            if (this.funnelForm.get('trainingActions').value?.length === 1) {
                const trainingActionSelected = this.funnelForm.get('trainingActions').value;
                this.funnelForm.get('indicativeDuration').setValue(trainingActionSelected[0].indicativeDuration ?? null);
            } else {
                this.funnelForm.get('indicativeDuration').setValue(null);
            }
        });
        setTimeout(() => {
            this._trainingActionService.listAvailableFromFunnel('').subscribe(filteredTrainingActions => {
                this.filteredTrainingActions.next(filteredTrainingActions.payload);
                if (this.proposal && this.proposal.trainingActions) {
                    const trainingActions = [];
                    this.proposal.trainingActions.forEach(trainingAction => {
                        const _trainingAction = filteredTrainingActions.payload.find(filteredTrainingAction => {
                            return trainingAction.externalId === filteredTrainingAction.externalId;
                        });
                        if (_trainingAction) {
                            trainingActions.push(_trainingAction);
                        }
                    });
                    this.proposal.trainingActions = trainingActions;
                    this.funnelForm.get('trainingActions').setValue(this.proposal.trainingActions);
                }
            });
            if (this.proposal && this.proposal.state !== this.proposalStates.DRAFT) {
                if (this.stepper) {
                    this.stepper.selected.completed = true;
                    this.stepper.next();
                    this.processing();
                }
            }
        });
    }

    ngOnChanges(): void {
        const styles = this.document.getElementById('dynamic-theme-css');
        if (styles) {
            styles.innerHTML = this.style;
        }
    }

    ngOnDestroy(): void {
        const styles = this.document.getElementById('dynamic-theme-css');
        if (styles) {
            this.document.removeElement(styles);
        }
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    conditionalValidator(predicate, validator): Validators | null {
        return (formControl => {
            if (!formControl.parent) {
                return null;
            }
            if (predicate()) {
                return validator(formControl);
            }
            return null;
        });
    }

    conditionalAmountMinValidator(): Validators | null {
        return (formControl => {
            if (!formControl.parent) {
                return null;
            }
            if (this.funnelForm.get('discountType').value === DiscountType.DISCOUNT_TYPE_PRICE) {
                return Validators.min(0)(formControl);
            } else if (this.funnelForm.get('discountType').value === DiscountType.DISCOUNT_TYPE_PERCENT) {
                return Validators.min(0)(formControl);
            } else if (this.funnelForm.get('discountType').value === DiscountType.DISCOUNT_TYPE_AMOUNT) {
                return Validators.min(0)(formControl);
            }
            return null;
        });
    }

    conditionalAmountMaxValidator(): Validators | null {
        return (formControl => {
            if (!formControl.parent) {
                return null;
            }
            if (this.funnelForm.get('discountType').value === DiscountType.DISCOUNT_TYPE_PRICE) {
                const price = this.funnelForm.get('trainingActions').value[0].totalTvaTTC;
                return Validators.max(price * 1.15)(formControl);
            } else if (this.funnelForm.get('discountType').value === DiscountType.DISCOUNT_TYPE_PERCENT) {
                return this.amountMinusPlus === '+' ? Validators.max(15)(formControl) : Validators.max(100)(formControl);
            } else if (this.funnelForm.get('discountType').value === DiscountType.DISCOUNT_TYPE_AMOUNT) {
                if (this.funnelForm.get('trainingActions').value.length === 1) {
                    const price = this.funnelForm.get('trainingActions').value[0].totalTvaTTC;
                    return this.amountMinusPlus === '+' ? Validators.max(price * 1.15 - price)(formControl) : Validators.max(price)(formControl);
                }
            }
            return null;
        });
    }

    onSubmit(): void {
        const values = this.funnelForm.value;
        const state = values.state;
        delete values.state;

        this.funnelForm.disable();

        if (values.trainingActions.length !== 1) {
            values.sessionStartDate = null;
            values.sessionEndDate = null;
        }

        delete values.setSessionDates;

        if (values.discountType === DiscountType.DISCOUNT_TYPE_AMOUNT || values.discountType === DiscountType.DISCOUNT_TYPE_PERCENT) {
            values.amount = parseInt(this.amountMinusPlus + values.amount, 10);
        } else if (values.discountType === DiscountType.DISCOUNT_TYPE_NONE) {
            values.amount = null;
        }
        values.trainingActions = values.trainingActions.map(trainingAction => {
            return {
                'externalId': trainingAction.externalId
            };
        });

        let proposal$ = this._proposalService.createThroughFunnel(values);
        if (this.proposal) {
            values.code = this.proposal.code;
            proposal$ = this._proposalService.updateThroughFunnel(values);
        } else if (this.tags) {
            // only on create
            values.tags = this.tags;
        }

        proposal$.pipe(
            switchMap(_proposal => {
                if (_proposal.state !== state) {
                    return this._proposalService.stateThroughFunnel(_proposal, state);
                } else {
                    return of(_proposal);
                }
            })
        ).subscribe(proposal => {
            this.proposal = proposal;
            this._location.replaceState('/funnel/commercial/proposition/' + proposal.code);
            this.funnelForm.enable();
            if (this.proposal.state === this.proposalStates.ACTIVE) {
                setTimeout(() => {
                    this.stepper.selected.completed = true;
                    this.stepper.next();
                });
            }
            this.processing();
        });
    }

    processing(): void {
        if (this.processing$) {
            this.processing$.unsubscribe();
        }
        if ([this.proposalStates.ACTIVE, this.proposalStates.VIEWED, this.proposalStates.ACCEPTED].indexOf(this.proposal.state) === -1) {
            return;
        }
        this.processing$ = of({proposalCode: this.proposal.code, state: null})
            .pipe(
                takeUntil(this._unsubscribeAll),
                switchMap(params => {
                    params.state = this.stateToCheck;
                    if (!params.state) {
                        delete params.state;
                    }
                    return this._registrationFolderService.listThroughFunnel(params);
                })
            )
            .pipe(
                map((response: PaginatedResponse<RegistrationFolder>) => {
                    if (response.payload.length !== 1) {
                        throw false;
                    } else {
                        return response.payload[0];
                    }
                }),
                retryWhen(errors =>
                    errors.pipe(delay(20000))
                ),
                map((registrationFolder: RegistrationFolder) => {
                    switch (registrationFolder.state) {
                        case RegistrationFolderStates.NOT_PROCESSED:
                            if (!this.registrationFolder) {
                                this.registrationFolder = registrationFolder;
                                this.stepper.selected.completed = true;
                                this.stepper.next(); // notProcessed
                            }
                            this.stateToCheck = RegistrationFolderStates.VALIDATED;
                            throw false;
                        case RegistrationFolderStates.VALIDATED:
                            if (!this.registrationFolder) {
                                this.registrationFolder = registrationFolder;
                                this.stepper.selected.completed = true;
                                this.stepper.next(); // notProcessed
                            }
                            if (!this.registrationFolderValidated) {
                                this.registrationFolder = registrationFolder;
                                this.registrationFolderValidated = true;
                                this.stepper.selected.completed = true;
                                this.stepper.next(); // validated
                            }
                            this.stateToCheck = RegistrationFolderStates.ACCEPTED;
                            throw false;
                        case RegistrationFolderStates.ACCEPTED:
                            if (!this.registrationFolder) {
                                this.registrationFolder = registrationFolder;
                                this.stepper.selected.completed = true;
                                this.stepper.next(); // notProcessed
                            }
                            if (!this.registrationFolderValidated) {
                                this.registrationFolder = registrationFolder;
                                this.registrationFolderValidated = true;
                                this.stepper.selected.completed = true;
                                this.stepper.next(); // validated
                            }
                            if (!this.registrationFolderAccepted) {
                                this.registrationFolder = registrationFolder;
                                this.registrationFolderAccepted = true;
                                this.stepper.selected.completed = true;
                                this.stepper.next(); // accepted
                            }
                            this.stateToCheck = null;
                            this.hideStepper = true;
                            break;
                        default:
                            this.registrationFolder = registrationFolder;
                            this.hideStepper = true;
                            break;
                    }
                    return registrationFolder;
                }),
                retryWhen(errors =>
                    errors.pipe(delay(20000))
                )
            )
            .subscribe();
    }

    getBodyEmail(): string {
        return encodeURIComponent('Bonjour ' + this.proposal.firstName + ' ' + this.proposal.lastName + ',\n\nVeuillez trouver votre proposition personnalisée :  '
            + this.proposal.link + '.\n\nCette page vous accompagnera dans la création de votre dossier sur le site Mon Compte Formation et appliquera les conditions associées à votre proposition.\n\nCordialement\n\nL\'équipe ' + this.organism.name);
    }

    getProposalText(): string {
        let text = 'Cette proposition';
        if (this.proposal.expire) {
            text += ' valable jusqu\'au <b>' + this._datePipe.transform(this.proposal.expire, 'mediumDate') + '</b>';
        }
        if (this.proposal.firstName != null && this.proposal.lastName != null) {
            text += ' permet à <b><a target="_blank"  href="mailto:' + this.proposal.email + '">' + this.proposal.firstName + ' ' + this.proposal.lastName + '</a></b>';

        } else {
            text += ' permet à <b><a target="_blank"  href="mailto:' + this.proposal.email + '">' + this.proposal.email + '</a></b>';
        }
        if (this.proposal.discountType === DiscountType.DISCOUNT_TYPE_PRICE) {
            text += ' de profiter d\'un tarif personnalisé de <b>' + this.proposal.amount + '€ TTC</b> à valoir sur';
        } else if (this.proposal.discountType === DiscountType.DISCOUNT_TYPE_PERCENT) {
            if (this.proposal.amount >= 0) {
                text += ' de profiter d\'un <b>tarif personnalisé (+' + this.proposal.amount + '%)</b> à valoir sur';
            } else {
                text += ' de profiter d\'une <b>remise (' + this.proposal.amount + '%)</b> à valoir sur';
            }
        } else if (this.proposal.discountType === DiscountType.DISCOUNT_TYPE_AMOUNT) {
            if (this.proposal.amount >= 0) {
                text += ' de profiter d\'un <b>tarif personnalisé (+' + this.proposal.amount + '€ TTC)</b> à valoir sur';
            } else {
                text += ' de profiter d\'une <b>remise  (' + this.proposal.amount + '€ TTC)</b> à valoir sur';
            }
        } else if (this.proposal.discountType === DiscountType.DISCOUNT_TYPE_NONE) {
            text += ' de s\'inscrire à ';
        }
        if (this.proposal.selectedTrainingAction) {
            text += ' la formation : <b><a target="_blank" href="https://www.moncompteformation.gouv.fr/espace-prive/html/#/formation/recherche/'
                + this.proposal.selectedTrainingAction.externalId + '">' + this.proposal.selectedTrainingAction.trainingTitle + '</a></b>';
            if (this.proposal.sessionStartDate) {
                text += ' commençant le <b>' + this._datePipe.transform(this.proposal.sessionStartDate, 'mediumDate') + '</b>';
            }
            if (this.proposal.sessionEndDate) {
                text += ' et se terminant le <b>' + this._datePipe.transform(this.proposal.sessionEndDate, 'mediumDate') + '</b>';
            }
        } else if (this.proposal.trainingActions && this.proposal.trainingActions.length === 1) {
            text += ' la formation : <b>' + this.proposal.trainingActions[0].trainingTitle + '</b>';
            if (this.proposal.sessionStartDate) {
                text += ' commençant le <b>' + this._datePipe.transform(this.proposal.sessionStartDate, 'mediumDate') + '</b>';
            }
            if (this.proposal.sessionEndDate) {
                text += ' et se terminant le <b>' + this._datePipe.transform(this.proposal.sessionEndDate, 'mediumDate') + '</b>';
            }
        } else {
            text += ' une selection de formations.';
        }
        return text;
    }

    toState(state: ProposalStates): void {
        this._proposalService.stateThroughFunnel(this.proposal, state).subscribe((proposal: Proposal) => {
            this.proposal = proposal;
            this.requestInProgress = false;
            this.funnelForm.patchValue(this.proposal);
            this.processing();
        });
    }

    signOut(): void {
        this._authService.signOut();
        this._authService.redirectToAuth();
    }
}
