import {Injectable} from '@angular/core';
import {ActivatedRouteSnapshot, CanActivate, RouterStateSnapshot} from '@angular/router';
import {forkJoin, Observable} from 'rxjs';
import {Store} from '@ngxs/store';
import {mapTo} from 'rxjs/operators';
import {TranslateService} from '@ngx-translate/core';
import {FetchUser} from '../../../../shared/api/state/user.state';
import {FetchOrganism} from '../../../../shared/api/state/organism.state';

@Injectable({
    providedIn: 'root'
})
export class SalesDataGuard implements CanActivate {

    constructor(
        private _store: Store,
        private _translateService: TranslateService
    ) {
    }

    canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> {
        // We load data here rather than in a resolver because all canActivate guards are called before all resolve are called
        // See https://angular.io/api/router/Resolve#usage-notes
        // This way, data is ensured to be loaded as early as possible
        return forkJoin([
            this._store.dispatch(new FetchUser()),
            this._store.dispatch(new FetchOrganism()),
            this._translateService.use('fr'),
        ]).pipe(
            mapTo(true)
        );
    }
}
