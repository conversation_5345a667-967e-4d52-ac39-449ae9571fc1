import {CUSTOM_ELEMENTS_SCHEMA, NgModule} from '@angular/core';
import {CandidatCertificationFolderComponent} from './certification-folder/candidat-certification-folder.component';
import {FileModule} from '../shared/file/file.module';
import {SharedModule} from '../shared/shared.module';
import {MatCardModule} from '@angular/material/card';
import {TranslateModule} from '@ngx-translate/core';
import {CandidatRoutingModule} from './candidat-routing.module';

@NgModule({
    schemas: [CUSTOM_ELEMENTS_SCHEMA],
    declarations: [
        CandidatCertificationFolderComponent
    ],
    imports: [
        CandidatRoutingModule,
        FileModule,
        SharedModule,
        MatCardModule,
        TranslateModule
    ]
})
export class CandidatModule {
}
