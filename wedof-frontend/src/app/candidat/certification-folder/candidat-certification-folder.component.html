<div class="mx-auto my-auto fullwidth-basic-normal-scroll">
    <div class="flex flex-row justify-between text-sm pb-2 pt-5">
        <div>
            <b>{{ 'private.attendee.spaces.certificationFolderLink' | translate }}</b><span
            *ngIf="attendeeLink"> | </span>
            <button *ngIf="attendeeLink" class="underline" type="button" (click)="switchToAttendeeSpace()">
                {{ 'private.attendee.spaces.registrationFolderLink' | translate }}
            </button>
        </div>
        <div>
            <button class="underline" type="button" (click)="signOut()">{{ 'common.actions.signOut' | translate }}
            </button>
        </div>
    </div>
    <div class="content flex flex-wrap">
        <div class="p-2 w-full gt-md:w-1/2 ">
            <app-attendee-card-attendee class="cardAttendee m-auto p-4 pb-0"
                                        [entityContext]="{class : 'CertificationFolder', id : certificationFolder.externalId}"
                                        [attendee]="attendee"></app-attendee-card-attendee>
            <app-certification-folder-card-attendee class="cardAttendee m-auto p-4 pb-0"
                                                    [certificationFolder]="certificationFolder"
                                                    [certification]="certification">
            </app-certification-folder-card-attendee>
            <app-file-list-attendee class="cardAttendee m-auto p-4 pb-0"
                                    *ngIf="fileTypes"
                                    #appFileListAttendee
                                    [entity]="certificationFolder"
                                    [certification]="certification"
                                    [shouldRefreshCertificateAccess]="shouldRefreshCertificateAccess"
                                    [fileTypes]="fileTypes"
                                    [typeFileId]="typeFileId"
                                    entityApiPath="attendees/certificationFolders"
                                    entityIdProperty="externalId"
                                    [entityParentId]="certification.id.toString()"
                                    (openSurvey)="openSurvey()"
                                    [surveyHref]="certificationFolder._links.survey.href"
                                    [orderedStates]="orderedCertificationFolderStates">
            </app-file-list-attendee>
        </div>
        <div class="p-2 w-full gt-md:w-1/2 ">
            <div class="flex justify-center mt-4 mb-2">
                <wedof-passport-button id="{{certificationFolder.externalId}}"></wedof-passport-button>
            </div>
            <app-attendee-card-share-attendee *ngIf="certificationFolder.state === states.SUCCESS"
                                              class="cardAttendee m-auto p-4 pb-0"
                                              [attendee]="attendee"
                                              [certification]="certification"
                                              [certificationFolder]="certificationFolder">
            </app-attendee-card-share-attendee>
            <app-certification-folder-survey-card class="cardAttendee m-auto max-h-100 p-4 pb-0"
                                                  [isCandidate]="true"
                                                  [candidateId]="attendee.id"
                                                  [certificationFolder]="certificationFolder"
                                                  (shouldRefreshCertificateAccess)="_refreshCertificateAccess($event)"
                                                  [showSurvey]="showSurvey">
            </app-certification-folder-survey-card>
        </div>
    </div>
</div>
