import {Component, OnInit, ViewChild} from '@angular/core';
import {ActivatedRoute, Router} from '@angular/router';
import {Select} from '@ngxs/store';
import {AttendeeState} from '../../shared/api/state/attendee.state';
import {Observable} from 'rxjs';
import {Attendee} from '../../shared/api/models/attendee';
import {FileListComponent} from '../../shared/file/file-list/file-list.component';
import {CertificationFolder, CertificationFolderStates} from '../../shared/api/models/certification-folder';
import {Certification} from '../../shared/api/models/certification';
import {AuthService} from '../../core/auth/auth.service';
import {FileType} from '../../shared/api/models/file';
import {CertificationFolderService} from '../../shared/api/services/certification-folder.service';

@Component({
    selector: 'app-candidat-certification-folder',
    templateUrl: './candidat-certification-folder.component.html',
    styleUrls: ['./candidat-certification-folder.component.scss']
})
export class CandidatCertificationFolderComponent implements OnInit {

    CertificationFolderStates = CertificationFolderStates;

    @Select(AttendeeState.attendee) attendee$: Observable<Attendee>;
    @ViewChild('appFileListAttendee') appFileListAttendee: FileListComponent<CertificationFolder, CertificationFolderStates>;

    states = CertificationFolderStates;
    attendee: Attendee;
    certificationFolder: CertificationFolder;
    certification: Certification;
    typeFileId: number;
    attendeeLink: string;
    orderedCertificationFolderStates: CertificationFolderStates[] = Object.values(CertificationFolderStates);
    showSurvey = false;
    fileTypes: FileType<CertificationFolderStates>[];
    shouldRefreshCertificateAccess: boolean;

    constructor(
        private _activatedRoute: ActivatedRoute,
        private _authService: AuthService,
        private _router: Router,
        private _certificationFolderService: CertificationFolderService,
    ) {
    }

    ngOnInit(): void {
        this.attendee$.subscribe(attendee => this.attendee = attendee);
        this.certificationFolder = this._activatedRoute.snapshot.data?.data[0];
        this.certification = this._activatedRoute.snapshot.data?.data[1];
        this.typeFileId = this._activatedRoute.snapshot.data?.data[2];
        this.attendeeLink = this._activatedRoute.snapshot.data?.data[3];
        this.showSurvey = this._activatedRoute.snapshot.data?.data[4];
        this._certificationFolderService.listAllFiles(this.certificationFolder.externalId).subscribe((filesOrFileTypes) => {
            const allowedFileTypeIds = filesOrFileTypes.map(fileOrFileType => fileOrFileType.typeId);
            this.fileTypes = this.certification.certificationFolderFileTypes.filter(fileType => allowedFileTypeIds.includes(fileType.id));
        });
    }

    signOut(): void {
        this._authService.signOut();
        window.location.reload(); // will trigger auth then redirect to connection page
    }

    switchToAttendeeSpace(): void {
        this._router.navigateByUrl(this.attendeeLink);
    }

    openSurvey(): void {
        if (this.showSurvey === true) {
            // if already true showSurvey is not recalculated and pop up survey is not open again
            this.showSurvey = false;
            setTimeout(() => {
                this.showSurvey = true;
            }, 500);
        } else {
            this.showSurvey = true;
        }
    }

    _refreshCertificateAccess($event: boolean): void {
        this.shouldRefreshCertificateAccess = $event;
    }
}
