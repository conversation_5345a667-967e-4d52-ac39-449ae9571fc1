import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {CandidatCertificationFolderComponent} from './certification-folder/candidat-certification-folder.component';
import {
    AttendeeCertificationFolderDataResolver
} from '../core/attendee/resolvers/attendee-certification-folder-data.resolver';

const routes: Routes = [{
    path: '',
    children: [
        {
            path: 'certification/dossier/:code',
            resolve: {
                data: AttendeeCertificationFolderDataResolver,
            },
            component: CandidatCertificationFolderComponent
        },
        {
            path: 'certification/dossier/:code/files/:typeFileId',
            resolve: {
                data: AttendeeCertificationFolderDataResolver,
            },
            component: CandidatCertificationFolderComponent
        },
        {
            path: 'certification/dossier/:code/enquete',
            resolve: {
                data: AttendeeCertificationFolderDataResolver,
            },
            component: CandidatCertificationFolderComponent
        }
    ]
}];

@NgModule({
    imports: [
        RouterModule.forChild(routes)
    ],
    exports: [RouterModule]
})
export class CandidatRoutingModule {
}
