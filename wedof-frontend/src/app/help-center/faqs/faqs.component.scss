@import 'src/@treo/styles/treo';

:host {
    ::ng-deep {
        img {
            width: initial !important;
            height: initial !important;
        }

        markdown {
            max-width: 100%;
        }

        .header {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            width: 100%;
            max-width: 960px;
            margin-top: 64px;

            @include treo-breakpoint('xs') {
                margin-top: 48px;
            }

            .mat-button {
                margin: 0 0 12px -16px;

                .mat-button-wrapper {

                    .mat-icon {
                        margin-right: 8px;
                        @include treo-icon-size(18);
                    }

                    span {
                        font-weight: 500;
                        line-height: 1;
                    }
                }
            }

            h1 {
                margin: 0;
            }
        }
    }
}

.main {
    @include treo-breakpoint('xs') {
        margin-top: 25px;
    }

    .faqs {

        position: relative;
        display: flex;
        align-items: flex-start !important;
        flex-direction: column;
        width: inherit !important;
        max-width: 960px;
        margin: 48px 0 32px 0 !important;
        padding: 40px !important;
        border-radius: 4px;
        background-color: white;
        @include treo-elevation();
    }
}

// -----------------------------------------------------------------------------------------------------
// @ Theming
// -----------------------------------------------------------------------------------------------------
@include treo-theme {

    $foreground: map-get($theme, foreground);
    $primary: map-get($theme, primary);
    $is-dark: map-get($theme, is-dark);

    help-center-faqs {

        .main {

            .faqs {

                h6 {
                    color: map-get($foreground, secondary-text);
                }
            }
        }
    }
}
