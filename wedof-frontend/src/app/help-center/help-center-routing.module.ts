import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

import { HelpCenterFaqsComponent } from './faqs/faqs.component';
import { HelpCenterGuidesCategoryComponent } from './guides/category/category.component';
import { HelpCenterGuidesGuideComponent } from './guides/guide/guide.component';
import { HelpCenterGuidesComponent } from './guides/guides.component';
import { HelpCenterComponent } from './help-center.component';
import { HelpCenterFaqsResolver, HelpCenterMostAskedFaqsResolver } from './help-center.resolvers';
import { HelpCenterSupportComponent } from './support/support.component';

export const routes: Routes = [
    {
        path: '',
        component: HelpCenterComponent,
        resolve: {
            faqs: HelpCenterMostAskedFaqsResolver
        },
    },
    {
        path: 'faqs',
        component: HelpCenterFaqsComponent,
        resolve: {
            faqs: HelpCenterFaqsResolver
        }
    },
    {
        path: 'guides',
        children: [
            {
                path: '',
                component: HelpCenterGuidesComponent
            },
            {
                path: ':categorySlug',
                children: [
                    {
                        path: '',
                        component: HelpCenterGuidesCategoryComponent
                    },
                    {
                        path: ':guideSlug',
                        component: HelpCenterGuidesGuideComponent
                    }
                ]
            }
        ]
    },
    {
        path: 'support',
        component: HelpCenterSupportComponent
    }
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class HelpCenterRoutingModule {
}
