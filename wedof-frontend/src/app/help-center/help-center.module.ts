import { NgModule } from '@angular/core';
import { TreoMessageModule } from '@treo/components/message';

import { SharedModule } from '../shared/shared.module';
import { HelpCenterFaqsComponent } from './faqs/faqs.component';
import { HelpCenterGuidesCategoryComponent } from './guides/category/category.component';
import { HelpCenterGuidesGuideComponent } from './guides/guide/guide.component';
import { HelpCenterGuidesComponent } from './guides/guides.component';
import { HelpCenterRoutingModule } from './help-center-routing.module';
import { HelpCenterComponent } from './help-center.component';
import { HelpCenterSupportComponent } from './support/support.component';

@NgModule({
    declarations: [
        HelpCenterComponent,
        HelpCenterFaqsComponent,
        HelpCenterGuidesComponent,
        HelpCenterGuidesCategoryComponent,
        HelpCenterGuidesGuideComponent,
        HelpCenterSupportComponent
    ],
    imports: [
        HelpCenterRoutingModule,
        SharedModule,
        TreoMessageModule,
    ]
})
export class HelpCenterModule {
}
