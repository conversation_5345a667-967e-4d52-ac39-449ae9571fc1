@import 'src/@treo/styles/treo';
:host{
    ::ng-deep{
        a{
            text-decoration:none;
        }
    }
}

.content-layout {
    position: relative;
    align-items: center;
    padding: 0 64px;
    overflow: hidden;

    @include treo-breakpoint('xs') {
        padding: 0 32px;
    }

    .background {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 891px;

        path {
            opacity: 0.1;
        }
    }

    .main {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 100%;
        max-width: 960px;
        padding: 0;

        .header {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
            margin: 64px 0 0 0;

            @include treo-breakpoint('xs') {
                margin: 48px 0 0 0;
            }

            h1 {
                margin: 64px 0 56px 0;
                text-align: center;
            }

            .search {
                margin-top: 56px;
                max-width: 600px;
                width: 100%;

                @include treo-breakpoint('xs') {
                    margin-top: 48px;
                }

                .mat-form-field {
                    width: 100%;
                }
            }

            h5 {
                text-align: center;
            }

            .categories {
                display: flex;
                flex-wrap: wrap;
                justify-content: center;

                .category {
                    background-color:white;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    max-width: 240px;
                    min-width: 240px;
                    width: 240px;
                    min-height: 240px;
                    padding: 32px;
                    margin: 16px;
                    border-radius: 8px;
                    transition: box-shadow 225ms cubic-bezier(0.4, 0.0, 0.2, 1);
                    @include treo-elevation();

                    @include treo-breakpoint('gt-xs') {

                        &:hover {
                            @include treo-elevation('lg');
                        }
                    }

                    .mat-icon {
                        @include treo-icon-size(48);
                        color:#5850ec;
                    }

                    .title {
                        margin-top: 24px;
                        font-size: 16px;
                        font-weight: 600;
                    }

                    .subtitle {
                        margin-top: 2px;
                        text-align: center;
                        line-height: 1.3;
                    }
                }
            }
        }

        .faqs {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            margin: 112px 0 64px 0;

            @include treo-breakpoint('xs') {
                margin: 64px 0;
            }

            h2 {
                margin: 0;
                text-align: center;
            }

            h6 {
                margin: 8px 0 0 0;
                text-align: center;
            }

            .mat-accordion {
                margin-top: 64px;
                max-width: 784px;

                .mat-expansion-panel {

                    .mat-expansion-panel-header {

                        .mat-expansion-panel-header-title {
                            line-height: 1;
                            font-weight: 500;
                        }
                    }
                }
            }
        }
    }
}


// -----------------------------------------------------------------------------------------------------
// @ Theming
// -----------------------------------------------------------------------------------------------------
@include treo-theme {

    $background: map-get($theme, background);
    $foreground: map-get($theme, foreground);
    $primary: map-get($theme, primary);
    $is-dark: map-get($theme, is-dark);

    help-center {

        .background {

            path {
                @if ($is-dark) {
                    fill: treo-color('cool-gray', 800);
                } @else {
                    fill: treo-color('blue', 300);
                }
            }
        }

        .main {

            .header {

                .search {

                    .mat-form-field {

                        .mat-form-field-flex {
                            background: map-get($background, card);
                        }
                    }
                }

                h6 {
                    color: map-get($foreground, secondary-text);
                }
            }

            .categories {

                .category {
                    background: map-get($background, card);

                    .mat-icon {
                        color: map-get($primary, default);
                    }

                    .subtitle {
                        color: map-get($foreground, secondary-text);
                    }
                }
            }

            .faqs {

                h6 {
                    color: map-get($foreground, secondary-text);
                }
            }
        }
    }
}
