import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import {FaqCategory, Guide, GuideCategory} from './help-center.type';
import { map } from 'rxjs/operators';

@Injectable({
    providedIn: 'root'
})
export class HelpCenterService {


    constructor(private _translateService: TranslateService) { }

    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    /**
     * Get all FAQs
     */
    getAllFaqs(): Observable<FaqCategory[]> {
        return this._translateService.get('common.help-center.faqs.faqCategories');
    }

    /**
     * Get FAQs by category using category slug
     *
     * @param slug
     */
    getFaqCategory(slug): Observable<FaqCategory> {
        return this.getAllFaqs().pipe(
            map((faqCategories: FaqCategory[]): FaqCategory => faqCategories.find(f => f.slug === slug))
        );
    }

    /**
     * Get all guides limited per category by the given number
     *
     */
    getAllGuides(): Observable<GuideCategory[]> {
        return this._translateService.get('common.help-center.guides.guideCategories');
    }

    /**
     * Get guides by category using category slug
     *
     * @param slug
     */
    getGuideCategory(slug): Observable<GuideCategory> {
        return this.getAllGuides().pipe(
            map((guideCategories: GuideCategory[]): GuideCategory => guideCategories.find(g => g.slug === slug))
        );
    }

    /**
     * Get guide by category and guide slug
     *
     * @param categorySlug
     * @param guideSlug
     */
    getGuide(categorySlug, guideSlug): Observable<Guide> {
        return this.getGuideCategory(categorySlug).pipe(
            map((guideCategory: GuideCategory): Guide => guideCategory.guides.find(g => g.slug === guideSlug))
        );
    }

    /**
     * Get guide by category and guide slug
     *
     * @param categorySlug
     * @param guideSlug
     */
    getNextGuide(categorySlug, guideSlug): Observable<Guide> {
        return this.getGuideCategory(categorySlug).pipe(
            map((guideCategory: GuideCategory): Guide => {
                const index = guideCategory.guides.findIndex(g => g.slug === guideSlug);
                const nextIndex = index + 1;
                if (guideCategory.guides.length >= nextIndex) {
                    return guideCategory.guides[nextIndex];
                } else {
                    return null;
                }
            })
        );
    }
}
