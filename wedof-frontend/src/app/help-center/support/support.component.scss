@import 'src/@treo/styles/treo';


    .header {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        width: 100%;
        max-width: 960px;
        margin-top: 64px;

        @include treo-breakpoint('xs') {
            margin-top: 48px;
        }

        .mat-button {
            margin: 0 0 12px -16px;

            .mat-button-wrapper {

                .mat-icon {
                    margin-right: 8px;
                    @include treo-icon-size(18);
                }

                span {
                    font-weight: 500;
                    line-height: 1;
                }
            }
        }

        h1 {
            margin: 0;
        }
    }

    .main {
        margin: 80px 0;
        padding: 0 !important;

        @include treo-breakpoint('xs') {
            margin: 64px 0 48px 0;
        }

        .card {
            width: 100%;
            padding: 40px;
            border-radius: 8px;
            background-color: white;
            @include treo-elevation('md');

            @include treo-breakpoint('xs') {
                padding: 0;
                @include treo-elevation('none');
            }

            treo-message {

                + form {
                    margin-top: 24px;
                }
            }

            form {
                display: flex;
                flex-direction: column;
                width: 100%;

                .label {
                    margin: 8px 0 4px 0;
                    font-weight: 600;

                    &:first-of-type {
                        margin-top: 0;
                    }
                }

                .actions {
                    display: flex;
                    justify-content: center;
                    margin: 24px 0 0 0;

                    .clear-button {
                        margin-right: 12px;
                    }
                }
            }
        }
    }



// -----------------------------------------------------------------------------------------------------
// @ Theming
// -----------------------------------------------------------------------------------------------------
@include treo-theme {

    $background: map-get($theme, background);
    $primary: map-get($theme, primary);
    $is-dark: map-get($theme, is-dark);

    help-center-support {

        .main {

            .card {
                background: map-get($background, card);

                @include treo-breakpoint('xs') {
                    background: transparent;
                }
            }
        }
    }
}
