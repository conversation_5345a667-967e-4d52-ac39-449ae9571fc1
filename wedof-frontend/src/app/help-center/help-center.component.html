<div class="content-layout fullwidth-basic-normal-scroll">

    <!-- Background - Inline SVG for easy customizations -->
    <svg class="background"
         preserveAspectRatio="xMidYMax slice"
         viewBox="0 0 1531 891"
         xmlns="http://www.w3.org/2000/svg">
        <path d="M0 182c144.327 47.973 534.462 219.712 440.509 369.87C346.555 702.028 79.877 662.846 0 638V182z"></path>
        <path
            d="M1405 848c-424.366 158.009-437-164-437-272s102-425 563-576v769c-21.333 29.333-63.333 55.667-126 79z"></path>
        <path
            d="M1531 162c-122.914-17.284-377.96 33.191-543.433 206.414C822.095 541.636 797.342 648.75 835.842 731.622c38.5 82.871 198.243 134.841 400.555 92.053C1438.71 780.886 1492.752 775.894 1531 768V162z"></path>
    </svg>

    <!-- Main -->
    <div class="main">

        <!-- Header -->
        <div class="header">

            <h1>{{ 'common.help-center.title' | translate}}</h1>

            <div class="categories">
                <a [routerLink]="['guides']"
                   class="category">
                    <mat-icon svgIcon="feather:book"></mat-icon>
                    <span class="title">
                        {{ 'common.help-center.guides.title' | translate }}
                    </span>
                    <span class="subtitle">
                        {{ 'common.help-center.guides.description' | translate }}
                    </span>
                </a>
            </div>
            <div class="categories">
                <a [routerLink]="['faqs']"
                   class="category">
                    <mat-icon svgIcon="feather:file-text"></mat-icon>
                    <span class="title">
                        {{ 'common.help-center.faqs.title' | translate }}
                    </span>
                    <span class="subtitle">
                        {{ 'common.help-center.faqs.description' | translate }}
                    </span>
                </a>
            </div>
            <div class="categories">
                <a [routerLink]="['support']"
                   class="category">
                    <mat-icon>support</mat-icon>
                    <span class="title">
                        {{ 'common.help-center.support.title' | translate }}
                    </span>
                    <span class="subtitle">
                        {{ 'common.help-center.support.description' | translate }}
                    </span>
                </a>
            </div>

        </div>

    </div>

</div>
