:host{
    ::ng-deep{
        .header {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            width: 100%;
            max-width: 960px;
            margin-top: 64px;

            .mat-button {
                margin: 0 0 12px -16px;

                .mat-button-wrapper {

                    .mat-icon {
                        margin-right: 8px;
                    }

                    span {
                        font-weight: 500;
                        line-height: 1;
                    }
                }
            }

            h1 {
                margin: 0;
            }
        }

        .main {
            align-items: flex-start !important;
            margin-top: 64px;

            a{
                text-decoration: underline;
            }

            .guides {
                display: flex;
                flex-direction: column;

                .guide {
                    margin-bottom: 12px;
                    font-size: 15px;
                    font-weight: 500;
                }
            }
        }
    }
}



