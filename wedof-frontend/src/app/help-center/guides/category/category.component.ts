import {Component, On<PERSON><PERSON>roy, OnInit} from '@angular/core';
import {ActivatedRoute, Params} from '@angular/router';
import {Subject} from 'rxjs';
import {takeUntil} from 'rxjs/operators';
import {HelpCenterService} from '../../help-center.service';
import {GuideCategory} from '../../help-center.type';

@Component({
    selector: 'help-center-guides-category',
    templateUrl: './category.component.html',
    styleUrls: ['./category.component.scss', '../../help-center.component.scss'],
})
export class HelpCenterGuidesCategoryComponent implements OnInit, OnDestroy {
    guideCategory: GuideCategory;

    // Private
    private _unsubscribeAll: Subject<any>;

    /**
     * Constructor
     *
     * @param {ActivatedRoute} _activatedRoute
     * @param {HelpCenterService} _helpCenterService
     */
    constructor(
        private _activatedRoute: ActivatedRoute,
        private _helpCenterService: HelpCenterService
    ) {
        // Set the private defaults
        this._unsubscribeAll = new Subject();
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    ngOnInit(): void {
        // Get the Guides
        this._activatedRoute.params.pipe(takeUntil(this._unsubscribeAll)).subscribe((params: Params) => {
            const { categorySlug } = params;
            this._helpCenterService.getGuideCategory(categorySlug)
                .subscribe((guideCategory) => this.guideCategory = guideCategory );
        });
    }

    /**
     * On destroy
     */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    /**
     * Track by function for ngFor loops
     *
     * @param index
     * @param item
     */
    trackByFn(index: number, item: any): any {
        return item.id || index;
    }
}
