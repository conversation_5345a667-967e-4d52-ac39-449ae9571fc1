import {Component, On<PERSON><PERSON>roy, OnInit} from '@angular/core';
import { ActivatedRoute, Params } from '@angular/router';
import {Subject} from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import {HelpCenterService} from '../../help-center.service';
import {Guide, GuideCategory} from '../../help-center.type';

@Component({
    selector: 'help-center-guides-guide',
    templateUrl: './guide.component.html',
    styleUrls: ['./guide.component.scss', '../../help-center.component.scss'],
})
export class HelpCenterGuidesGuideComponent implements OnInit, OnDestroy {
    guide: Guide;
    nextGuide: Guide;
    guideCategory: GuideCategory;

    // Private
    private _unsubscribeAll: Subject<any>;

    /**
     * Constructor
     *
     * @param {HelpCenterService} _helpCenterService
     * @param _activatedRoute
     */
    constructor(
        private _helpCenterService: HelpCenterService,
        private _activatedRoute: ActivatedRoute
    ) {
        // Set the private defaults
        this._unsubscribeAll = new Subject();
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    ngOnInit(): void {
        this._activatedRoute.params.pipe(takeUntil(this._unsubscribeAll)).subscribe((params: Params) => {
            const { categorySlug, guideSlug } = params;
            this._helpCenterService.getGuideCategory(categorySlug)
                .subscribe((guideCategory) => this.guideCategory = guideCategory );
            this._helpCenterService.getGuide(categorySlug, guideSlug)
                .subscribe((guide) => this.guide = guide );
            this._helpCenterService.getNextGuide(categorySlug, guideSlug)
                .subscribe((nextGuide) => this.nextGuide = nextGuide );
        });
    }

    /**
     * On destroy
     */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    /**
     * Track by function for ngFor loops
     *
     * @param index
     * @param item
     */
    trackByFn(index: number, item: any): any {
        return item.id || index;
    }
}
