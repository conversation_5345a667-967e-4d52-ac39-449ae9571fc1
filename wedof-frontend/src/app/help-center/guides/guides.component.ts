import {Component, <PERSON><PERSON><PERSON><PERSON>, OnInit} from '@angular/core';
import {Subject} from 'rxjs';
import {HelpCenterService} from '../help-center.service';
import {GuideCategory} from '../help-center.type';

@Component({
    selector: 'help-center-guides',
    templateUrl: './guides.component.html',
    styleUrls: ['./guides.component.scss', '../help-center.component.scss'],
})
export class HelpCenterGuidesComponent implements OnInit, OnDestroy {
    guideCategories: GuideCategory[];

    // Private
    private _unsubscribeAll: Subject<any>;

    /**
     * Constructor
     *
     * @param {HelpCenterService} _helpCenterService
     */
    constructor(
        private _helpCenterService: HelpCenterService
    ) {
        // Set the private defaults
        this._unsubscribeAll = new Subject();
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    ngOnInit(): void {
        // Get the Guide categories
        this._helpCenterService
            .getAllGuides()
            .subscribe((guideCategories) => this.guideCategories = guideCategories);
    }

    /**
     * On destroy
     */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    /**
     * Track by function for ngFor loops
     *
     * @param index
     * @param item
     */
    trackByFn(index: number, item: any): any {
        return item.id || index;
    }
}
