<div class="content-layout fullwidth-basic-normal-scroll">
    <svg class="background"
         preserveAspectRatio="xMidYMax slice"
         viewBox="0 0 1531 891"
         xmlns="http://www.w3.org/2000/svg">
        <path d="M0 182c144.327 47.973 534.462 219.712 440.509 369.87C346.555 702.028 79.877 662.846 0 638V182z"></path>
        <path
            d="M1405 848c-424.366 158.009-437-164-437-272s102-425 563-576v769c-21.333 29.333-63.333 55.667-126 79z"></path>
        <path
            d="M1531 162c-122.914-17.284-377.96 33.191-543.433 206.414C822.095 541.636 797.342 648.75 835.842 731.622c38.5 82.871 198.243 134.841 400.555 92.053C1438.71 780.886 1492.752 775.894 1531 768V162z"></path>
    </svg>

    <!-- Header -->
    <div class="header">

        <a color="primary"
           [routerLink]="['../..']"
           mat-button>
            <mat-icon svgIcon="arrow_back"></mat-icon>
            <span>{{ 'common.help-center.common.return' | translate }}</span>
        </a>
        <h1>{{ 'common.help-center.common.title' | translate }}</h1>

    </div>

    <!-- Main -->
    <div class="main">

        <div class="guides">

            <div *ngFor="let guideCategory of guideCategories; trackBy: trackByFn"
                 class="category">

                <h3 class="title">{{guideCategory.title}}</h3>

                <ng-container *ngFor="let guide of guideCategory.guides; trackBy: trackByFn">
                    <a *ngIf="guide.slug else externalLink"
                       [routerLink]="['../' + guideCategory.slug, guide.slug]"
                       class="link guide">
                        {{guide.title}}
                    </a>
                    <ng-template #externalLink>
                        <a [href]="guide.href"
                           class="link guide">
                            {{guide.title}}
                        </a>
                    </ng-template>
                </ng-container>
            </div>

        </div>

    </div>

</div>
