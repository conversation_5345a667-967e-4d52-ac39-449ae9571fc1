@import 'src/@treo/styles/treo';

.header {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
    max-width: 960px;
    margin-top: 64px;

    @include treo-breakpoint('xs') {
        margin-top: 48px;
    }

    .mat-button {
        margin: 0 0 12px -16px;

        .mat-button-wrapper {

            .mat-icon {
                margin-right: 8px;
                @include treo-icon-size(18);
            }

            span {
                font-weight: 500;
                line-height: 1;
            }
        }
    }

    h1 {
        margin: 0;
    }
}

.main {
    align-items: flex-start !important;
    margin-top: 80px;
    padding: 0 !important;

    @include treo-breakpoint('xs') {
        margin-top: 64px;
    }

    .guides {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        flex-wrap: wrap;

        .category {
            background-color:white;
            border-radius: 8px;
            padding: 32px;

            display: flex;
            flex-direction: column;
            flex: 1 1 auto;
            width: 45%;
            max-width: 45%;
            min-width: 45%;
            margin-bottom: 80px;

            @include treo-breakpoint('xs') {
                margin-bottom: 64px;
            }

            .title {
                    margin: 0 0 16px 0 !important;
            }

            .guide {
                margin-bottom: 8px;
                font-size: 15px;
                font-weight: 500;
            }

            .mat-flat-button {
                align-self: flex-start;
                margin-top: 24px;

                .mat-icon {
                    @include treo-icon-size(18);
                    margin-left: 8px;
                }
            }

            .view-all {
                display: flex;
                align-items: center;
                align-self: flex-start;
                height: 24px;
                min-height: 24px;
                margin-top: 12px;
                padding: 0 8px 0 12px;
                border-radius: 4px;

                span {
                    font-size: 12px;
                    font-weight: 500;
                }

                .mat-icon {
                    @include treo-icon-size(14);
                    margin-left: 8px;
                }
            }
        }
    }
}


// -----------------------------------------------------------------------------------------------------
// @ Theming
// -----------------------------------------------------------------------------------------------------
@include treo-theme {

    $background: map-get($theme, background);
    $foreground: map-get($theme, foreground);
    $primary: map-get($theme, primary);
    $is-dark: map-get($theme, is-dark);

    help-center-guides {

        .main {

            .guides {

                .category {

                    .view-all {
                        color: map-get($foreground, secondary-text);
                        background: map-get($background, hover);

                        &:hover {
                            color: map-get($foreground, text);

                            .mat-icon {
                                color: map-get($foreground, text);
                            }
                        }
                    }
                }
            }
        }
    }
}
