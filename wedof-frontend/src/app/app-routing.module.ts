import {NgModule} from '@angular/core';
import {PreloadAllModules, RouterModule, Routes} from '@angular/router';
import {AuthGuard} from './core/auth/guards/auth.guard';
import {LayoutComponent} from './layout/layout.component';
import {Error404Component} from './shared/errors/error-404/error-404.component';
import {Error500Component} from './shared/errors/error-500/error-500.component';
import {PrivateNavigationResolver} from './core/private/resolvers/private-navigation.resolver';
import {PrivateDataGuard} from './core/private/guards/private-data.guard';
import {AttendeeDataGuard} from './core/attendee/guards/attendee-data.guard';
import {SubdomainDataGuard} from './core/auth/guards/subdomain-data.guard';
import {Error403AuthenticatedToAttendeeComponent} from './shared/errors/error-403-authenticated-to-attendee/error-403-authenticated-to-attendee.component';
import {Error403WrongAttendeeComponent} from './shared/errors/error-403-wrong-attendee/error-403-wrong-attendee.component';
import {PassportGuard} from './core/attendee/guards/passport.guard';
import {ResellerGuard} from './core/reseller/guards/reseller.guard';

export const routes: Routes = [
    {
        path: '',
        children: [
            {
                path: '',
                component: LayoutComponent,
                data: {
                    layout: 'empty'
                },
                children: [
                    {
                        path: '',
                        loadChildren: () => import('./public/public.module').then(m => m.PublicModule)
                    },
                    {
                        path: 'assistance',
                        loadChildren: () => import('./help-center/help-center.module').then((m) => m.HelpCenterModule),
                    },
                    {
                        path: 'candidat/certification/dossier/:code/passeport',
                        canActivate: [PassportGuard, SubdomainDataGuard],
                        data: {
                            layout: 'attendee'
                        },
                        loadChildren: () => import('./passport/passport.module').then(m => m.PassportModule)
                    },
                    {
                        path: 'connect',
                        canActivate: [ResellerGuard, SubdomainDataGuard],
                        data: {
                            layout: 'attendee'
                        },
                        loadChildren: () => import('./reseller/reseller.module').then(m => m.ResellerModule)
                    },
                    {
                        path: 'auth',
                        canActivate: [SubdomainDataGuard],
                        loadChildren: () => import('./auth/auth.module').then(m => m.AuthModule)
                    },
                    // subdomain is required from here
                    {
                        path: 'funnel',
                        loadChildren: () => import('./subdomain/funnel/funnel.module').then(m => m.FunnelModule)
                    },
                    {
                        path: '404-not-found',
                        component: Error404Component
                    },
                    {
                        path: '403-authenticated-to-attendee',
                        component: Error403AuthenticatedToAttendeeComponent
                    },
                    {
                        path: '403-wrong-attendee',
                        component: Error403WrongAttendeeComponent
                    },
                    {
                        path: '500-server-issue',
                        component: Error500Component
                    },
                    {
                        path: 'simulator',
                        loadChildren: () => import('./simulator/simulator.module').then(m => m.SimulatorModule)
                    },
                ]
            },
            {
                path: '',
                component: LayoutComponent,
                data: {
                    layout: 'attendee'
                },
                canActivate: [AuthGuard],
                // Needs an intermediate empty route in order to ensure that the guard is checked ONLY if authentication is ok
                // This avoids trying to get the attendee etc. in PrivateDataGuard and get an HTTP error because we are not authenticated
                children: [
                    {
                        path: '',
                        canActivate: [AttendeeDataGuard],
                        children: [
                            {
                                path: 'apprenant',
                                loadChildren: () => import('./attendee/attendee.module').then(m => m.AttendeeModule)
                            },
                            {
                                path: 'candidat',
                                loadChildren: () => import('./candidat/candidat.module').then(m => m.CandidatModule)
                            }
                        ]
                    }
                ]
            },
            {
                path: '',
                component: LayoutComponent,
                canActivate: [AuthGuard],
                resolve: {
                    navigation: PrivateNavigationResolver, // Use a resolver to ensure that the navigation is entirely loaded when the loading screen is closed
                },
                // Needs an intermediate empty route in order to ensure that the guard is checked ONLY if authentication is ok
                // This avoids trying to get the user / organism etc. in PrivateDataGuard and get an HTTP error because we are not authenticated
                children: [
                    {
                        path: '',
                        canActivate: [PrivateDataGuard],
                        children: [
                            {
                                path: '',
                                loadChildren: () => import('./private/private.module').then(m => m.PrivateModule)
                            },
                            {
                                path: 'aide',
                                loadChildren: () => import('./help-center/help-center.module').then((m) => m.HelpCenterModule),
                            }
                        ]
                    }
                ]
            }
        ],
    }
];

@NgModule({
    imports: [
        RouterModule.forRoot(routes, {
            scrollPositionRestoration: 'enabled',
            preloadingStrategy: PreloadAllModules,
            enableTracing: false,
            // TODO : changer urls dans le back si ajouter le #
            // useHash: true
        })
    ],
    exports: [RouterModule]
})
export class AppRoutingModule {
}
