import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

export const routes: Routes = [
    {
        path: 'connexion',
        pathMatch: 'prefix',
        redirectTo: 'auth/connexion'
    },
    {
        path: 'invitation',
        pathMatch: 'prefix',
        redirectTo: 'auth/invitation'
    },
    {
        path: 'apprenant/connexion',
        pathMatch: 'prefix',
        redirectTo: 'auth/apprenant/connexion'
    },
    {
        path: 'candidat/connexion',
        pathMatch: 'prefix',
        redirectTo: 'auth/candidat/connexion'
    },
    {
        path: 'signed-in-redirect',
        pathMatch: 'prefix',
        redirectTo: 'accueil'
    },
    {
        path: '**',
        redirectTo: '404-not-found',
    }
];

@NgModule({
    imports: [
        RouterModule.forChild(routes)
    ],
    exports: [RouterModule]
})
export class AppRedirectRoutingModule {
}
