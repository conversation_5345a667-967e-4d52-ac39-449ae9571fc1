import {Component, OnInit} from '@angular/core';
import {environment} from '../../../environments/environment';
import {AuthService} from '../../core/auth/auth.service';

@Component({
    selector: 'app-home',
    template: `
        <a *ngIf="this.isDev" [routerLink]="[\'auth\', \'connexion\']" class="cta-button connexion-btn m-3"
           color="primary" mat-flat-button>
            {{this.isAuthenticated ? 'Ouvrir Wedof' : 'Connexion' }}
        </a>
        <a *ngIf="this.isDev && this.isAuthenticated" [routerLink]="[\'auth\', \'deconnexion\']"
           class="cta-button connexion-btn block m-3" color="primary" mat-flat-button>
            Deconnexion {{ typeAccount }}
        </a>
        <a *ngIf="this.isDev && this.isAuthenticated" [routerLink]="[\'simulator\']"
           class="cta-button connexion-btn block m-3" color="primary" mat-flat-button>Simulateur CPF
        </a>
        <a *ngIf="this.isDev && this.isAuthenticated"
           href="/app/public/logs"
           class="cta-button connexion-btn block m-3" color="primary"
           mat-flat-button>Télécharger Zip log de prod
        </a>
    `
})
export class HomeComponent implements OnInit {

    isDev: boolean;
    isAuthenticated: boolean;
    authService: AuthService;
    typeAccount: string;

    constructor(authService: AuthService) {
        this.isDev = environment.development;
        this.authService = authService;
    }

    ngOnInit(): void {
        if (!this.isDev) {
            window.open('/', '_self');
        } else {
            this.authService.check().subscribe(isAuthenticated => {
                this.isAuthenticated = isAuthenticated;
                this.typeAccount = this.authService.isAttendee ? 'Attendee' : this.authService.isSales ? 'Sales' : 'User';
            });
        }
    }
}
