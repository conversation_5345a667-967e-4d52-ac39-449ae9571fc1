import {NgModule} from '@angular/core';
import {SharedModule} from '../shared/shared.module';
import {PassportAuthComponent} from './passport-auth/passport-auth.component';
import {PassportRoutingModule} from './passport-routing.module';
import {PassportSuccessComponent} from './passport-success/passport-success.component';
import {PassportFormComponent} from './passport-form/passport-form.component';


@NgModule({
    declarations: [
        PassportAuthComponent,
        PassportSuccessComponent,
        PassportFormComponent
    ],
    imports: [
        SharedModule,
        PassportRoutingModule
    ]
})
export class PassportModule {
}
