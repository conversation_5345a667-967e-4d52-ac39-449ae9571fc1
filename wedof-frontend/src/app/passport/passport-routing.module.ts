import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {PassportAuthComponent} from './passport-auth/passport-auth.component';
import {PassportDataResolver} from '../core/attendee/resolvers/passport-data-resolver';
import {CanDeactivateGuard} from '../shared/utils/can-deactivate/can-deactivate.guard';

const routes: Routes =  [{
    path: '',
    resolve: {
        data: PassportDataResolver
    },
    canDeactivate: [CanDeactivateGuard],
    component: PassportAuthComponent
}];

@NgModule({
    imports: [
        RouterModule.forChild(routes)
    ],
    exports: [RouterModule]
})
export class PassportRoutingModule {
}
