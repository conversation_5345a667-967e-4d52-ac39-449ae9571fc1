.content-layout {
    align-items: center;
    justify-content: center;
    background: #fff;
    border-radius: 0;
    box-shadow: none;
}

.button-lin {
    cursor: pointer;
    height: 56px;
    width: 100%;
    color: #0000FF;
    font-size: 16px;
    font-weight: bold;
    font-family: "Montserrat", serif;
    background-color: #FFCB05;
    line-height: 21px;
    padding: 10px;
    border-radius: 10px;

    &:hover, &:focus {
        background-color: #FFD94D;
    }

    &:active {
        background-color: #FFDA0D;
    }
}

.button-lin.primary-lin:not(:disabled) {
    color: #f0f5ff;
    background-color: #5850ec;

    .mat-icon {
        color: #f0f5ff;
    }

    &:hover, &:focus {
        background-color: #4E48CC;
    }

    &:active {
        background-color: #4E48CC;
    }
}

.button-lin.primary-lin:disabled {
    background-color: #6B7280 !important;
    color: #97a6ba !important;

    .mat-icon, .mat-progress-spinner {
        color: #97a6ba !important;
    }

    .mat-progress-spinner circle {
        stroke: #97a6ba !important;
    }
}

.discover-lin {
    display: block;
    margin-top: 12px;
    color: #4F4F4F;
    font-family: "<PERSON>", serif;
    text-decoration: none;
    font-size: 13px;

    > img {
        width: 16px;
        margin-right: 4px;
    }
}

.card {
    .logo img {
        height: auto !important;
        max-width: 250px !important;
        margin: auto !important;
    }

    .title {
        text-align: center;
        font-size: 30px;
        font-weight: 800;
    }
}

.rotate-25 {
    --transform-rotate: 25deg !important;
}

.passport-exposant {
    position: relative;
    right: 10px;
    bottom: -25px;
}
