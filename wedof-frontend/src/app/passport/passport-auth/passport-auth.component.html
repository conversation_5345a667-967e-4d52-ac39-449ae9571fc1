<div class="content-layout fullwidth-basic-normal-scroll">
    <div class="card w-full p-5 md:w-2/3 md:p-0 lg:w-2/3 lg:p-0 xl:w-1/3 xl:p-0">
        <div class="logo pb-5 flex flex-row justify-center content-center">
            <div class="flex content-center">
                <img [src]="logo" alt="{{organism?.name}}">
            </div>
            <div class="ml-5 passport-exposant">
                <img class="w-24"
                     [src]="passportType === passportTypes.PREVENTION ? 'assets/images/dataProviders/passportPrevention-square.png' : 'assets/images/sign-up/passport-competences.png'"
                     [alt]="'logo de passeport de ' + passportType">
            </div>
        </div>

        <ng-container *ngIf="passportType === passportTypes.COMPETENCES">
            <div class="pt-5 title flex items-center" *ngIf="needUpdate; else showSuccess">
                {{ 'public.passport.competences.' + (certificationFolderState === certificationFolderStates.SUCCESS ? 'titleSuccess' : 'titlePendingSuccess') | translate }}
            </div>
            <ng-template #showSuccess>
                <div class="pt-5 title flex items-center">
                    {{ 'public.passport.competences.success.' + accrochageState  | translate }}
                </div>
            </ng-template>
        </ng-container>

        <div class="text-center text-sm mt-4" *ngIf="needUpdate">
            <ng-container *ngIf="passportType === passportTypes.COMPETENCES">
                <a href="https://competences.moncompteformation.gouv.fr/espace-public/" target="_blank"
                   rel="noopener noreferrer"
                   style="display: inline-block">
                    <mat-icon matPrefix svgIcon="smart_display" class="mr-2" style="vertical-align:middle;"></mat-icon>
                    {{ 'public.passport.competences.subtitle' | translate }}
                </a>
            </ng-container>
            <ng-container *ngIf="passportType === passportTypes.PREVENTION">
                <a href="https://passeport-prevention.travail-emploi.gouv.fr" target="_blank"
                   rel="noopener noreferrer"
                   style="display: inline-block">
                    <mat-icon matPrefix svgIcon="smart_display" class="mr-2" style="vertical-align:middle;"></mat-icon>
                    {{ 'public.passport.prevention.subtitle' | translate }}
                </a>
            </ng-container>
        </div>
        <div *ngIf="needUpdate && !matchedCandidate">
            <div class="flex-col flex items-center justify-center">
                <treo-message *ngIf="passportType === passportTypes.COMPETENCES"
                              type="info" class="mt-4" [showIcon]="false" appearance="outline">
                    <p class="p-3 text-center"
                       [innerHTML]="('public.passport.form.' + (certificationFolderState === certificationFolderStates.SUCCESS ? 'explanationSuccess' : 'explanationPendingSuccess')) | translate:{certificationName:certificationName, folderId:folderId}"></p>
                </treo-message>
                <div class="flex text-center justify-center py-3">
                    <mat-error *ngIf="errorMessages.length">
                        <ul>
                            <li *ngFor="let errorMessage of errorMessages">{{ errorMessage }}</li>
                        </ul>
                    </mat-error>
                </div>
                <div class="flex flex-row justify-center content-center flex-wrap">
                    <div class="text-center my-3 mr-2" *ngIf="!hideFile">
                        <button (click)="idDocument.click()"
                                style="height: 50px !important;"
                                class="button-lin primary-lin"
                                [disabled]="loading">
                            <div class="flex items-center justify-center">
                                <mat-icon svgIcon="badge" class="mr-2"></mat-icon>
                                <p *ngIf="!loading">
                                    {{ 'Pièce d\'identité' | translate }}
                                </p>
                                <mat-progress-spinner [color]="'primary'" *ngIf="loading" [diameter]="24"
                                                      [mode]="'indeterminate'"></mat-progress-spinner>
                            </div>
                        </button>
                        <input #idDocument
                               type="file"
                               class="hidden"
                               (change)="onFileChange($event)"
                               accept="image/jpeg,image/jpg,image/bmp,image/tiff,image/heif,image/png,application/pdf">
                    </div>
                    <div class="text-center my-3 ml-2" *ngIf="!hideIn">
                        <button (click)="authWithID360()"
                                style="height: 50px !important;"
                                class="button-lin"
                                [disabled]="loading">
                            <div class="flex items-center justify-center">
                                <img alt="L'Identité Numérique" *ngIf="!loading"
                                     src="/assets/images/lin/marqueur.svg" style="width:32px;" class="mr-2">
                                <p *ngIf="!loading">{{ 'public.passport.form.button.id360' | translate }}</p>
                                <mat-progress-spinner [color]="'primary'" *ngIf="loading" [diameter]="24"
                                                      [mode]="'indeterminate'"></mat-progress-spinner>
                            </div>
                        </button>
                        <a href="https://lidentitenumerique.laposte.fr"
                           target="_blank"
                           class="discover-lin flex items-center justify-center">
                            <img alt="Découvrir L'Identité Numérique" src="/assets/images/lin/help.svg">
                            <p>Découvrir L'Identité Numérique</p>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <app-passport-form *ngIf="needUpdate && matchedCandidate"
                           [folderId]="folderId"
                           [organism]="organism"
                           [passportType]="passportType"
                           [certificationName]="certificationName"
                           [originalCandidate]="originalCandidate"
                           [candidateDataCollected]="candidateDataCollected"
                           [isSuccess]="certificationFolderState === certificationFolderStates.SUCCESS"
                           (confirmData)="confirm($event)">
        </app-passport-form>


        <ng-container *ngIf="!needUpdate">
            <app-passport-success *ngIf="passportType === passportTypes.COMPETENCES"
                                  [folderId]="folderId"></app-passport-success>
            <ng-container *ngIf="passportType === passportTypes.PREVENTION">
                <div class="text-center text-lg mt-5">
                    <a href="https://passeport-prevention.travail-emploi.gouv.fr" target="_blank"
                       style="display: inline-block">
                        {{'public.passport.prevention.success' | translate}}
                    </a>
                </div>
            </ng-container>
        </ng-container>
    </div>
    <div class="text-secondary text-center mt-10 text-sm">
        Un service proposé par © <a href="https://www.wedof.fr">Wedof</a> 2025
    </div>
</div>
