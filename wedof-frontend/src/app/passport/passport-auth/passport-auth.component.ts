import {Component, OnInit} from '@angular/core';
import {ActivatedRoute} from '@angular/router';
import {AuthService} from '../../core/auth/auth.service';
import {Select} from '@ngxs/store';
import {OrganismState} from '../../shared/api/state/organism.state';
import {EMPTY, Observable, of} from 'rxjs';
import {Organism} from '../../shared/api/models/organism';
import {DomSanitizer, SafeUrl} from '@angular/platform-browser';
import {CandidatePassportService} from '../../shared/api/services/candidate-passport.service';
import {TranslateService} from '@ngx-translate/core';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../shared/errors/errors.types';
import {Attendee, CandidatePassportBody} from '../../shared/api/models/attendee';
import {catchError, mergeMap} from 'rxjs/operators';
import {CertificationFolderStates, PassportType} from '../../shared/api/models/certification-folder';

interface MatchedCandidateResult {
    matching: boolean;
    userData: CandidatePassportBody;
}

@Component({
    selector: 'app-passport-auth',
    templateUrl: './passport-auth.component.html',
    styleUrls: ['./passport-auth.component.scss']
})
export class PassportAuthComponent implements OnInit {

    token: string;
    folderId: string;
    certificationName: string;
    certificationFolderState: CertificationFolderStates;
    certificationFolderStates = CertificationFolderStates;
    loading: boolean;
    needUpdate = false;
    logo: SafeUrl;
    candidateDataCollected = null;
    errorMessages: string[] = [];
    matchedCandidate = false;
    organism: Organism = null;
    hideIn = false;
    hideFile = false;
    originalCandidate: Attendee;
    accrochageState: string;
    passportType: PassportType;
    passportTypes = PassportType;

    @Select(OrganismState.organism) organism$: Observable<Organism>;

    constructor(
        private _activatedRoute: ActivatedRoute,
        private _authService: AuthService,
        private _sanitizer: DomSanitizer,
        private _translateService: TranslateService,
        private _candidatePassportService: CandidatePassportService
    ) {
    }

    ngOnInit(): void {
        this.token = this._activatedRoute.snapshot.queryParams['token'] ?? null;
        this.errorMessages = [];
        this.folderId = this._activatedRoute.snapshot.data?.data[0];
        this.needUpdate = this._activatedRoute.snapshot.data?.data[1].needUpdate;
        this.certificationName = this._activatedRoute.snapshot.data?.data[1].certificationName;
        this.certificationFolderState = this._activatedRoute.snapshot.data?.data[1].certificationFolderState;
        this.originalCandidate = this._activatedRoute.snapshot.data?.data[1].originalCandidate;
        this.accrochageState = this._activatedRoute.snapshot.data?.data[1].accrochageState;
        this.passportType = this._activatedRoute.snapshot.data?.data[1].passportType;
        this.organism$.subscribe((organism: Organism) => {
            const logo: any = organism && organism.logo ? organism.logo : 'assets/images/logo/logo-text.svg';
            this.logo = this._sanitizer.bypassSecurityTrustUrl(logo);
            this.organism = organism;
        });
        if (this.token) {
            this.loading = true;
            this.hideFile = true;
            this.errorMessages = [];
            this.checkCandidateMatchingWithID360().subscribe(
                {
                    next: (result: MatchedCandidateResult) => {
                        this.matchedCandidate = result.matching;
                        this.loading = false;
                        if (!this.matchedCandidate) {
                            this.candidateDataCollected = null;
                            this.errorMessages = [this._translateService.instant('public.passport.errors.unmatching')];
                        } else {
                            this.hideFile = false;
                            this.candidateDataCollected = result.userData;
                        }
                    },
                    error: (httpErrorResponse: HttpErrorResponse) => {
                        this.loading = false;
                        this.hideFile = false;
                        this.matchedCandidate = false;
                        this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
                    }
                }
            );
        }
    }

    authWithID360(): void {
        this.loading = true;
        this.hideFile = true;
        const redirectUrl = '/candidat/certification/dossier/' + this.folderId + '/passeport';
        this._authService.iD360SignInUrl('passport', null, this.folderId, redirectUrl).subscribe(response => {
            if (response.url) {
                window.location.href = response.url;
            }
        });
    }

    confirm(confirm: boolean): void {
        if (confirm) {
            this.needUpdate = false;
        }
    }

    private checkCandidateMatchingWithID360(): Observable<MatchedCandidateResult> {
        const redirectUrl = '/candidat/certification/dossier/' + this.folderId + '/passeport';
        return this._authService.iD360UserData('passport', this.token)
            .pipe(
                mergeMap((dataFromId360) => {
                    if (dataFromId360?.firstName) {
                        return this._candidatePassportService.checkMatching(this.folderId, dataFromId360).pipe(
                            mergeMap((match) => {
                                return of({matching: match, userData: dataFromId360});
                            }),
                            catchError((e: HttpErrorResponse) => {
                                this.loading = false;
                                this.hideFile = false;
                                this.errorMessages = [this._translateService.instant('public.passport.errors.internalError')];
                                return EMPTY;
                            })
                        );
                    } else {
                        this.loading = false;
                        this.hideFile = false;
                        this.errorMessages = [this._translateService.instant('public.passport.errors.wrongData')];
                        return EMPTY;
                    }
                }),
                catchError((e: HttpErrorResponse) => {
                    this.loading = false;
                    this.hideFile = false;
                    this.errorMessages = [this._translateService.instant('public.passport.errors.id360Error')];
                    return EMPTY;
                })
            );
    }

    onFileChange(event): void {
        if (event.target.files.length > 0) {
            if (event.target.files[0].size > 1048576 * 4) { // 1,048,576 = 1MB max 4MB at the moment
                this.errorMessages = [this._translateService.instant('public.passport.errors.filesize')];
                return;
            }
            this.loading = true;
            this.hideIn = true;
            this._authService.authWithIdDocument(event.target.files[0]).pipe(
                mergeMap((data) => {
                    if (data?.firstName) {
                        return this._candidatePassportService.checkMatching(this.folderId, data).pipe(
                            mergeMap((match) => {
                                return of({matching: match, userData: data});
                            }),
                            catchError((e: HttpErrorResponse) => {
                                this.loading = false;
                                this.hideIn = false;
                                this.errorMessages = [this._translateService.instant('public.passport.errors.internalError')];
                                return EMPTY;
                            })
                        );
                    } else {
                        this.loading = false;
                        this.hideIn = false;
                        this.errorMessages = [this._translateService.instant('public.passport.errors.internalError')];
                        return EMPTY;
                    }
                }),
                catchError((e: HttpErrorResponse) => {
                    this.loading = false;
                    this.hideIn = false;
                    this.errorMessages = (e.error as ApiError)?.errorMessages ?? [this._translateService.instant('public.passport.errors.internalError')];
                    return EMPTY;
                })
            ).subscribe(
                {
                    next: (result: MatchedCandidateResult) => {
                        this.matchedCandidate = result.matching;
                        this.loading = false;
                        if (!this.matchedCandidate) {
                            this.hideIn = false;
                            this.candidateDataCollected = null;
                            this.errorMessages = [this._translateService.instant('public.passport.errors.unmatching')];
                        } else {
                            result.userData.identificationDocument = event.target.files[0];
                            this.candidateDataCollected = result.userData;
                        }
                    },
                    error: (httpErrorResponse: HttpErrorResponse) => {
                        this.loading = false;
                        this.hideFile = false;
                        this.matchedCandidate = false;
                        this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
                    }
                }
            );
        }
    }
}
