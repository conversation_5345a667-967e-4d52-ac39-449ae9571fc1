<div class="mx-auto my-auto fullwidth-basic-normal-scroll py-8">
    <div class="mt-5" *ngIf="passportType === passportTypes.COMPETENCES"
         [innerHTML]="('public.passport.confirmData.' + (isSuccess ? 'descriptionSuccess' : 'descriptionPendingSuccess')) | translate : {certificationName: certificationName}"></div>
    <ng-container *ngIf="candidateDataCollected">
        <form #form="ngForm" class="mt-5 px-8 flex flex-col" [formGroup]="formGroup">
            <ng-container *ngIf="showStep1">
                <h5 class="mt-4">{{ 'public.passport.confirmData.form.title.civility' | translate }}</h5>
                <app-form-fields
                    class="grid grid-cols-1 sm:grid-cols-6 md:grid-cols-6 lg:grid-cols-6 xl:grid-cols-6 gap-2"
                    formGroupName="candidate"
                    [appFormFieldsData]="appFormFieldsGroupData['civility']"
                    [formGroup]="formGroup"></app-form-fields>

                <h5 class="mt-5">{{ 'public.passport.confirmData.form.title.contact' | translate }}</h5>
                <app-form-fields
                    class="grid grid-cols-1 sm:grid-cols-6 md:grid-cols-6 lg:grid-cols-6 xl:grid-cols-6 gap-2"
                    formGroupName="candidate"
                    [appFormFieldsData]="appFormFieldsGroupData['contact']"
                    [formGroup]="formGroup"></app-form-fields>

                <h5 class="mt-5">{{ 'public.passport.confirmData.form.title.lastNames' | translate }}</h5>
                <app-form-fields
                    class="grid grid-cols-1 sm:grid-cols-6 md:grid-cols-6 lg:grid-cols-6 xl:grid-cols-6 gap-2"
                    formGroupName="candidate"
                    [appFormFieldsData]="appFormFieldsGroupData['lastNames']"
                    [formGroup]="formGroup"></app-form-fields>

                <h5 class="mt-5">{{ 'public.passport.confirmData.form.title.firstNames' | translate }}</h5>
                <app-form-fields
                    class="grid grid-cols-1 sm:grid-cols-6 md:grid-cols-6 lg:grid-cols-6 xl:grid-cols-6 gap-2"
                    formGroupName="candidate"
                    [appFormFieldsData]="appFormFieldsGroupData['firstNames']"
                    [formGroup]="formGroup"></app-form-fields>

                <h5 class="mt-5">{{ 'public.passport.confirmData.form.title.birthday' | translate }}</h5>
                <app-form-fields
                    class="grid grid-cols-1 sm:grid-cols-6 md:grid-cols-6 lg:grid-cols-6 xl:grid-cols-6 gap-2"
                    formGroupName="candidate"
                    [appFormFieldsData]="appFormFieldsGroupData['birthday']"
                    [formGroup]="formGroup"></app-form-fields>
            </ng-container>
            <ng-container *ngIf="showStep2">
                <div class="text-center my-3 mr-2" *ngIf="!originalCandidate.nirValidated">
                    <button (click)="idDocument.click()"
                            style="height: 50px !important;"
                            class="button-lin primary-lin"
                            [disabled]="loading">
                        <div class="flex items-center justify-center">
                            <mat-icon svgIcon="badge" class="mr-2"></mat-icon>
                            <p *ngIf="!loading">
                                {{ 'Carte Vitale' | translate }}
                            </p>
                            <mat-progress-spinner [color]="'primary'" *ngIf="loading" [diameter]="24"
                                                  [mode]="'indeterminate'"></mat-progress-spinner>
                        </div>
                    </button>
                    <input #idDocument
                           type="file"
                           class="hidden"
                           (change)="onFileChange($event)"
                           accept="image/jpeg,image/jpg,image/bmp,image/tiff,image/heif,image/png,application/pdf">
                </div>
                <h5 class="mt-4">{{'public.passport.confirmData.form.title.nir' | translate }}</h5>
                <app-form-fields
                    class="grid grid-cols-1 sm:grid-cols-6 md:grid-cols-6 lg:grid-cols-6 xl:grid-cols-6 gap-2"
                    formGroupName="candidate"
                    [appFormFieldsData]="appFormFieldsGroupData['nir']"
                    [formGroup]="formGroup"></app-form-fields>
            </ng-container>
            <div class="flex text-center mt-6 mb-4">
                <mat-error *ngIf="errorMessages.length">
                    <ul>
                        <li *ngFor="let errorMessage of errorMessages">{{ errorMessage }}</li>
                    </ul>
                </mat-error>
            </div>
            <div *ngIf="showStep1"
                 class="grid grid-cols-1 sm:grid-cols-6 md:grid-cols-6 lg:grid-cols-6 xl:grid-cols-6 gap-4 justify-around mx-5">
                <ng-container *ngIf="!loading">
                    <button *ngIf="readonly else showCancel"
                            type="button" (click)="editForm()"
                            class="col-span-3"
                            mat-flat-button color="warn"
                            [disabled]="formGroup.invalid">{{ 'public.passport.confirmData.updateButton' | translate }}
                    </button>
                    <ng-template #showCancel>
                        <button type="button" (click)="cancel()"
                                class="col-span-3"
                                mat-button>
                            {{ (errorMessages?.length ? 'public.passport.errors.restartForm' : 'common.actions.cancel') | translate }}
                        </button>
                    </ng-template>
                </ng-container>
                <button type="button" (click)="formGroup.dirty ? preValidateForm() : submitData()"
                        [disabled]="loading || formGroup.invalid"
                        class="col-span-3"
                        mat-flat-button color="primary">
                    <mat-progress-spinner class="mr-4" *ngIf="loading" [diameter]="24"
                                          mode="indeterminate"></mat-progress-spinner>
                    <ng-container
                        *ngIf="!loading"> {{ (formGroup.dirty ? 'common.actions.update' : 'public.passport.confirmData.confirmButton') | translate }}
                    </ng-container>
                </button>
            </div>
            <div *ngIf="showStep2"
                 class="flex justify-center mx-5">
                <button type="button"
                        (click)="confirmData.emit(true)"
                        mat-flat-button
                        class="mr-2"
                        color="primary"
                        *ngIf="originalCandidate.nirValidated || showConfirmButton">
                    {{ 'public.passport.confirmData.confirmButton' | translate }}
                </button>
                <button type="button"
                        *ngIf="!originalCandidate.nirValidated && !showConfirmButton"
                        (click)="checkMatchingNir()"
                        [disabled]="loading || !formGroup.dirty || formGroup.invalid"
                        mat-flat-button
                        color="primary">
                    <mat-progress-spinner class="mr-4" *ngIf="loading" [diameter]="24"
                                          mode="indeterminate"></mat-progress-spinner>
                    <ng-container
                        *ngIf="!loading">{{ 'common.actions.update' | translate }}
                    </ng-container>
                </button>
            </div>
        </form>
    </ng-container>
</div>
