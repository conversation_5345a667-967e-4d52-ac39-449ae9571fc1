import {Component, EventEmitter, Input, OnInit, Output, ViewChild} from '@angular/core';
import {FormBuilder, FormGroup, FormGroupDirective, Validators} from '@angular/forms';
import {AppFormFieldData} from '../../shared/material/app-form-field/app-form-field.component';
import {TranslateService} from '@ngx-translate/core';
import {EMPTY, of} from 'rxjs';
import {Attendee, CandidatePassportBody, City, Country, Gender} from '../../shared/api/models/attendee';
import {PassportType} from '../../shared/api/models/certification-folder';
import {CityService} from '../../shared/api/services/city.service';
import CountriesList from '../../../assets/countriesAndCitiesList/countriesListCode.json';
import {CanDeactivateComponent} from '../../shared/utils/can-deactivate/can-deactivate.component';
import {CandidatePassportService} from '../../shared/api/services/candidate-passport.service';
import {HttpErrorResponse} from '@angular/common/http';
import {ApiError} from '../../shared/errors/errors.types';
import {FormValidators} from '../../shared/api/shared/form-validators';
import {AttendeeService} from '../../shared/api/services/attendee.service';
import {EnvironmentService} from '../../shared/environment/environment.service';
import {AuthService} from '../../core/auth/auth.service';
import {catchError, mergeMap} from 'rxjs/operators';
import {Organism} from '../../shared/api/models/organism';
import moment from 'moment/moment';

@Component({
    selector: 'app-passport-form',
    templateUrl: './passport-form.component.html',
    styleUrls: ['./passport-form.component.scss']
})
export class PassportFormComponent extends CanDeactivateComponent implements OnInit {

    @Input() candidateDataCollected: CandidatePassportBody;
    @Input() passportType: PassportType;
    @Input() originalCandidate: Attendee;
    @Input() organism: Organism;
    @Input() folderId: string;
    @Input() certificationName: string;
    @Input() isSuccess: boolean;
    @Output() confirmData: EventEmitter<boolean> = new EventEmitter<boolean>();
    @ViewChild('form') form: FormGroupDirective;

    showStep1 = true;
    showStep2 = false;
    loading = false;
    formGroup: FormGroup;
    isCandidateMatching = false;
    errorMessages: string[] = [];
    countriesList: typeof CountriesList;
    appFormFieldsGroupData: AppFormFieldData[][];
    franceCountryObject = {'name': 'France', 'cog': 100, 'code': 'FR', 'code3': 'FRA'};
    readonly = true;
    passportTypes = PassportType;
    matchedNir = false;
    showConfirmButton = false;

    constructor(
        private _formBuilder: FormBuilder,
        private _cityService: CityService,
        private _attendeeService: AttendeeService,
        private _translateService: TranslateService,
        private _candidatePassportService: CandidatePassportService,
        private _environmentService: EnvironmentService,
        private _authService: AuthService,
    ) {
        super();
        this.countriesList = CountriesList;
    }

    ngOnInit(): void {
        if (!this.candidateDataCollected) {
            return;
        }
        this.loading = true;
        this.readonly = true;
        this.initForm(this.candidateDataCollected);
        this.loading = false;
        this.countriesList.unshift(this.franceCountryObject);
    }

    initForm(valueCollected: CandidatePassportBody): void {
        if (!valueCollected.codeCityOfBirth && this.originalCandidate.codeCityOfBirth) {
            valueCollected.codeCityOfBirth = this.originalCandidate.codeCityOfBirth;
            valueCollected.nameCityOfBirth = this.originalCandidate.nameCityOfBirth;
        } else if (!valueCollected.codeCountryOfBirth && this.originalCandidate.codeCountryOfBirth) {
            valueCollected.codeCountryOfBirth = this.originalCandidate.codeCountryOfBirth;
            valueCollected.nameCountryOfBirth = this.originalCandidate.nameCountryOfBirth;
        }

        const cityCandidate: City = valueCollected.codeCityOfBirth ? {
            cog: valueCollected.codeCityOfBirth,
            name: valueCollected.nameCityOfBirth,
            postalCode: valueCollected.postalCode ?? null
        } : null;
        let countryCandidate: Country;
        if (cityCandidate) {
            countryCandidate = this.readonly ? null : this.franceCountryObject;
        } else {
            countryCandidate = valueCollected.nameCountryOfBirth ? this.countriesList.find((country) => valueCollected.nameCountryOfBirth === country.name) : null;
        }

        this.formGroup = this._formBuilder.group({
            candidate: this._formBuilder.group({})
        });
        const appFormFieldsData: AppFormFieldData[][] = [];
        appFormFieldsData['civility'] = [
            {
                controlName: 'gender',
                disabled: true,
                value: valueCollected.gender && this._translateService.instant('private.training-organism.attendees.dialog.gender.' + valueCollected.gender),
                label: 'private.common.registrationFolder.attendee.gender.label',
                type: 'text'
            },
        ];
        const email = this.originalCandidate.email && !valueCollected.email ? this.originalCandidate.email : valueCollected.email;
        const phoneNumber = this.originalCandidate.phoneNumber && !valueCollected.phoneNumber ? this.originalCandidate.phoneNumber : valueCollected.phoneNumber;
        appFormFieldsData['contact'] = [
            {
                controlName: 'email',
                value: email,
                disabled: this.originalCandidate.emailValidated ? true : this.disabledFieldFromPasseportData('email'),
                removed: !this.originalCandidate.email,
                label: 'private.common.registrationFolder.attendee.email',
                type: 'email',
                icon: this.originalCandidate.emailValidated ? 'verified_user' : 'email',
                iconClass: this.originalCandidate.emailValidated ? 'text-primary' : '',
                iconTooltip: this.originalCandidate.email ? 'common.actions.validatedField.email' : '',
                validators: [Validators.email],
                validatorsMessages: {
                    email: 'private.profile.organism.form.fields.email.error'
                },
            },
            {
                controlName: 'phoneNumber',
                value: phoneNumber,
                disabled: this.originalCandidate.phoneNumberValidated ? true : this.disabledFieldFromPasseportData('phoneNumber'),
                removed: !this.originalCandidate.phoneNumber,
                label: 'private.common.registrationFolder.attendee.phoneNumber',
                type: 'tel',
                icon: this.originalCandidate.phoneNumberValidated ? 'verified_user' : 'smartphone',
                iconClass: this.originalCandidate.phoneNumberValidated ? 'text-primary' : '',
                iconTooltip: this.originalCandidate.phoneNumber ? 'common.actions.validatedField.phoneNumber' : '',
                validators: [Validators.pattern(FormValidators.MOBILEPHONE_PATTERN)],
                validatorsMessages: {
                    pattern: 'public.funnel.errors.phoneNumber'
                },
            },
        ];
        appFormFieldsData['lastNames'] = [
            {
                controlName: 'lastName',
                required: true,
                disabled: this.disabledFieldFromPasseportData('lastName'),
                value: valueCollected.lastName,
                label: 'private.common.registrationFolder.attendee.lastName',
                type: 'text',
                colSpan: 3
            },
            {
                controlName: 'birthName',
                disabled: this.disabledFieldFromPasseportData('birthName'),
                value: valueCollected.birthName,
                label: 'private.common.registrationFolder.attendee.birthName',
                type: 'text',
                colSpan: 3
            }
        ];
        appFormFieldsData['firstNames'] = [
            {
                controlName: 'firstName',
                required: true,
                disabled: this.disabledFieldFromPasseportData('firstName'),
                value: valueCollected.firstName,
                label: 'private.common.registrationFolder.attendee.firstName',
                type: 'text',
                colSpan: 3
            },
            {
                controlName: 'firstName2',
                disabled: this.disabledFieldFromPasseportData('firstName2'),
                value: valueCollected.firstName2,
                label: 'public.passport.confirmData.form.firstName2',
                type: 'text',
                colSpan: 3
            },
            {
                controlName: 'firstName3',
                disabled: this.disabledFieldFromPasseportData('firstName3'),
                value: valueCollected.firstName3,
                label: 'public.passport.confirmData.form.firstName3',
                type: 'text',
                colSpan: 3
            },
        ];
        appFormFieldsData['birthday'] = [
            {
                controlName: 'dateOfBirth',
                disabled: this.disabledFieldFromPasseportData('dateOfBirth'),
                value: valueCollected.dateOfBirth,
                label: 'private.common.registrationFolder.attendee.dateOfBirth',
                type: 'date',
                colSpan: 3,
                min: new Date(1900, 0, 1),
                max: new Date()
            },
            {
                controlName: 'nameCountryOfBirth',
                removed: this.readonly && countryCandidate ? false : !!(this.readonly && !countryCandidate && cityCandidate),
                colSpan: 3,
                disabled: this.disabledFieldFromPasseportData('nameCountryOfBirth'),
                value: countryCandidate,
                label: 'private.common.registrationFolder.attendee.nameCountryOfBirth',
                placeholder: !this.readonly && !valueCollected.nameCountryOfBirth ? 'private.common.registrationFolder.attendee.placeOfBirth.placeholderCountry' : 'private.common.form.placeholder',
                type: 'select',
                removable: true,
                choices: Object.values(this.countriesList).map((country: Country) => ({
                    key: country.name,
                    value: country
                })),
                change: (controlName, newValue, formData) => {
                    const appFormFieldNameCityOfBirth = formData.find(field => field.controlName === 'nameCityOfBirth');
                    if (newValue && newValue === this.franceCountryObject) {
                        appFormFieldNameCityOfBirth.removed = false;
                        appFormFieldNameCityOfBirth.value = null;
                        appFormFieldNameCityOfBirth.required = true;
                    } else {
                        appFormFieldNameCityOfBirth.removed = true;
                        appFormFieldNameCityOfBirth.value = null;
                        appFormFieldNameCityOfBirth.required = false;
                    }
                    return [appFormFieldNameCityOfBirth];
                },
            },
            {
                controlName: 'nameCityOfBirth',
                disabled: this.disabledFieldFromPasseportData('nameCityOfBirth'),
                removed: !cityCandidate,
                value: cityCandidate,
                label: 'private.common.registrationFolder.attendee.nameCityOfBirth',
                type: 'search',
                colSpan: 3,
                searchNoEntriesFoundLabel: 'private.training-organism.attendees.dialog.nameCityOfBirthNotFound',
                placeholder: !this.readonly && !valueCollected.nameCityOfBirth ? 'private.training-organism.attendees.dialog.nameCityOfBirthSearch' : 'private.common.form.placeholder',
                removable: true,
                searchComparisonProperty: 'cog',
                searchMethod: (searchTerm: string) => searchTerm ? this._cityService.listCitiesByName(searchTerm) : of([]),
                searchResultFormatter: (city: City) => city.name + ' (' + (city.postalCode ? city.postalCode : city.cog.slice(0, 2)) + ')',
            },
        ];
        this.appFormFieldsGroupData = appFormFieldsData;
    }

    editForm(): void {
        this.loading = true;
        this.errorMessages = [];
        this.readonly = false;
        this.initForm(this.transformFormToValue());
        this.loading = false;
    }

    cancel(): void {
        this.loading = true;
        this.errorMessages = [];
        this.readonly = true;
        this.initForm(this.candidateDataCollected);
        this.loading = false;
    }

    preValidateForm(): void {
        if (this.formGroup.valid) {
            this.loading = true;
            this.errorMessages = [];
            const formRawValue = this.transformFormToValue();
            this._candidatePassportService.checkMatching(this.folderId, formRawValue).subscribe({
                next: (isCandidateMatching) => {
                    this.isCandidateMatching = isCandidateMatching;
                    this.loading = false;
                    if (isCandidateMatching) {
                        this.readonly = true;
                        this.initForm(formRawValue);
                    } else {
                        this.errorMessages = [this._translateService.instant('public.passport.errors.unmatchingForm')];
                    }
                },
                error: (httpErrorResponse: HttpErrorResponse) => {
                    this.loading = false;
                    this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
                }
            });
        }
    }

    submitData(): void {
        this.loading = true;
        this.errorMessages = [];
        this._candidatePassportService.update(
            'CertificationFolder',
            this.folderId,
            this.transformFormToValue(),
            this.candidateDataCollected.identificationDocument).subscribe({
            next: (attendee: Attendee) => {
                this.loading = false;
                if (this._environmentService.isEnableBetaFeatures(this.organism)) {
                    this.showStep1 = false;
                    let nir;
                    if (this.candidateDataCollected.nirValidated) {
                        nir = this.candidateDataCollected.retrievedNir;
                    } else if (attendee.gender) {
                        nir = attendee.gender === 'female' ? '2' : '1';
                        if (attendee.dateOfBirth) {
                            nir += moment(attendee.dateOfBirth).format('YY') + moment(attendee.dateOfBirth).format('MM');
                            if (attendee.codeCityOfBirth) {
                                nir += attendee.codeCityOfBirth;
                            } else if (attendee.codeCountryOfBirth) {
                                nir += '99' + attendee.codeCountryOfBirth;
                            }
                        }
                    }
                    this.initFormNir(nir);
                    this.showStep2 = true;
                } else {
                    this.confirmData.emit(true);
                }
            },
            error: (httpErrorResponse: HttpErrorResponse) => {
                this.loading = false;
                this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
            }
        });
    }

    canDeactivate(): boolean {
        return false;
    }

    transformFormToValue(): CandidatePassportBody {
        const formRawValue = this.formGroup.getRawValue().candidate;
        return {
            firstName: formRawValue.firstName,
            lastName: formRawValue.lastName,
            firstName2: formRawValue.firstName2,
            firstName3: formRawValue.firstName3,
            dateOfBirth: formRawValue.dateOfBirth,
            birthName: formRawValue.birthName,
            gender: formRawValue.gender === 'Madame' ? Gender.FEMALE : Gender.MALE,
            nameCityOfBirth: formRawValue.nameCountryOfBirth && formRawValue.nameCountryOfBirth?.name !== 'France' ?
                null : formRawValue.nameCityOfBirth ? formRawValue.nameCityOfBirth.name : null,
            codeCityOfBirth: formRawValue.nameCountryOfBirth && formRawValue.nameCountryOfBirth?.name !== 'France' ?
                null : formRawValue.nameCityOfBirth ? formRawValue.nameCityOfBirth.cog : null,
            codeCountryOfBirth: formRawValue.nameCityOfBirth ? null : formRawValue.nameCountryOfBirth ? formRawValue.nameCountryOfBirth?.cog : null,
            nameCountryOfBirth: formRawValue.nameCityOfBirth ? null : formRawValue.nameCountryOfBirth ? formRawValue.nameCountryOfBirth?.name : null,
            email: formRawValue.email,
            phoneNumber: formRawValue.phoneNumber
        };
    }

    disabledFieldFromPasseportData(candidateField: string): boolean {
        if (this.candidateDataCollected[candidateField] === this.originalCandidate[candidateField]) {
            return true;
        } else {
            return this.readonly;
        }
    }

    initFormNir(socialSecurityNumber: string): void {
        const disabled = this.originalCandidate.nirValidated;
        this.appFormFieldsGroupData['nir'] = [
            {
                controlName: 'nirGender',
                placeholder: 'X',
                type: 'magicText',
                value: socialSecurityNumber?.length >= 1 ? socialSecurityNumber.substring(0, 1) : null,
                required: true,
                disabled: disabled,
                colSpan: 1,
                maxLength: 1,
                minLength: 1,
                parameters: {
                    current: 'nirGender',
                    next: 'nirYearBirth',
                    length: 1
                },
                paste: (event, controlName, formGroup, formGroupName) => {
                    const clipboardData = event.clipboardData;
                    const nirCopied = clipboardData.getData('text');
                    if (nirCopied?.length) {
                        formGroup.get(formGroupName).get('nirGender').setValue(nirCopied.charAt(0));
                        formGroup.get(formGroupName).get('nirYearBirth').setValue(nirCopied.substring(1, 3));
                        formGroup.get(formGroupName).get('nirMonthBirth').setValue(nirCopied.substring(3, 5));
                        formGroup.get(formGroupName).get('nirInseeBirth').setValue(nirCopied.substring(5, 10));
                        formGroup.get(formGroupName).get('nirOrderBirth').setValue(nirCopied.substring(10, 13));
                        formGroup.get(formGroupName).get('nirKey').setValue(nirCopied.substring(13));
                    }
                },
                validatorsMessages: {
                    minlength: this._translateService.instant('common.errors.minlength', {number: 1})
                }
            },
            {
                controlName: 'nirYearBirth',
                placeholder: 'XX',
                type: 'magicText',
                value: socialSecurityNumber?.length >= 5 ? socialSecurityNumber.substring(1, 3) : null,
                required: true,
                disabled: disabled,
                colSpan: 1,
                maxLength: 2,
                minLength: 2,
                parameters: {
                    previous: 'nirGender',
                    current: 'nirYearBirth',
                    next: 'nirMonthBirth',
                    length: 2
                },
                validatorsMessages: {
                    minlength: this._translateService.instant('common.errors.minlength', {number: 2})
                }
            },
            {
                controlName: 'nirMonthBirth',
                placeholder: 'XX',
                type: 'magicText',
                value: socialSecurityNumber?.length >= 5 ? socialSecurityNumber.substring(3, 5) : null,
                required: true,
                disabled: disabled,
                colSpan: 1,
                maxLength: 2,
                minLength: 2,
                parameters: {
                    previous: 'nirYearBirth',
                    current: 'nirMonthBirth',
                    next: 'nirInseeBirth',
                    length: 2
                },
                validatorsMessages: {
                    minlength: this._translateService.instant('common.errors.minlength', {number: 2})
                }
            },
            {
                controlName: 'nirInseeBirth',
                placeholder: 'XXXXX',
                type: 'magicText',
                value: socialSecurityNumber?.length >= 10 ? socialSecurityNumber.substring(5, 10) : null,
                required: true,
                disabled: disabled,
                colSpan: 1,
                maxLength: 5,
                minLength: 5,
                parameters: {
                    previous: 'nirMonthBirth',
                    current: 'nirInseeBirth',
                    next: 'nirOrderBirth',
                    length: 5
                },
                validatorsMessages: {
                    minlength: this._translateService.instant('common.errors.minlength', {number: 5})
                }
            },
            {
                controlName: 'nirOrderBirth',
                placeholder: 'XXX',
                type: 'magicText',
                required: true,
                value: socialSecurityNumber?.length === 15 ? socialSecurityNumber.substring(10, 13) : null,
                disabled: disabled,
                colSpan: 1,
                minLength: 3,
                maxLength: 3,
                parameters: {
                    previous: 'nirInseeBirth',
                    current: 'nirOrderBirth',
                    next: 'nirKey',
                    length: 3
                },
                validatorsMessages: {
                    minlength: this._translateService.instant('common.errors.minlength', {number: 3})
                }
            },
            {
                controlName: 'nirKey',
                placeholder: 'XX',
                type: 'magicText',
                colSpan: 1,
                disabled: disabled,
                required: true,
                value: socialSecurityNumber?.length === 15 ? socialSecurityNumber.substring(13) : null,
                maxLength: 2,
                minLength: 2,
                parameters: {
                    previous: 'nirOrderBirth',
                    current: 'nirKey',
                    length: 2
                },
                validatorsMessages: {
                    minlength: this._translateService.instant('common.errors.minlength', {number: 2})
                }
            }
        ];
    }

    checkMatchingNir(): void {
        this.loading = true;
        this.errorMessages = [];
        const formGroupValues = this.formGroup.getRawValue().candidate;
        const nirGender = formGroupValues.nirGender;
        const nirYearBirth = nirGender + formGroupValues.nirYearBirth;
        const nirMonthBirth = nirYearBirth + formGroupValues.nirMonthBirth;
        const nirInseeBirth = nirMonthBirth + formGroupValues.nirInseeBirth;
        const nirOrderBirth = nirInseeBirth + formGroupValues.nirOrderBirth;
        const nir = nirOrderBirth + formGroupValues.nirKey;
        this._attendeeService.checkMatchingAndUpdateNir(this.folderId, {nir: nir}).subscribe({
            next: () => {
                this.loading = false;
                this.confirmData.emit(true);
            },
            error: (httpErrorResponse: HttpErrorResponse) => {
                this.loading = false;
                this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
            }
        });
    }

    onFileChange(event): void {
        this.errorMessages = [];
        this.showConfirmButton = false;
        if (event.target.files.length > 0) {
            if (event.target.files[0].size > 1048576 * 4) { // 1,048,576 = 1MB max 4MB at the moment
                this.errorMessages = [this._translateService.instant('public.passport.errors.filesize')];
                return;
            }
            this.loading = true;
            this._authService.authWithSocialSecurityCardDocument(event.target.files[0]).pipe(
                mergeMap((data) => {
                    if (data?.firstName) {
                        return this._attendeeService.checkMatchingAndUpdateNir(this.folderId, data).pipe(
                            mergeMap(() => {
                                return of(data);
                            }),
                            catchError((e: HttpErrorResponse) => {
                                this.loading = false;
                                this.errorMessages = (e.error as ApiError)?.errorMessages ?? [this._translateService.instant('public.passport.errors.internalError')];
                                return EMPTY;
                            })
                        );
                    } else {
                        this.loading = false;
                        this.errorMessages = [this._translateService.instant('public.passport.errors.internalError')];
                        return EMPTY;
                    }
                }),
                catchError((e: HttpErrorResponse) => {
                    this.loading = false;
                    this.errorMessages = (e.error as ApiError)?.errorMessages ?? [this._translateService.instant('public.passport.errors.internalError')];
                    return EMPTY;
                })
            ).subscribe(
                {
                    next: (result) => {
                        this.loading = false;
                        this.showConfirmButton = true;
                        this.matchedNir = true;
                        this.initFormNir(result.nir);
                    },
                    error: (httpErrorResponse: HttpErrorResponse) => {
                        this.loading = false;
                        this.matchedNir = false;
                        this.showConfirmButton = false;
                        this.errorMessages = (httpErrorResponse.error as ApiError).errorMessages;
                    }
                }
            );
        }
    }
}
