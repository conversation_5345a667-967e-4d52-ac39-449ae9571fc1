import {Inject, Injectable} from '@angular/core';
import {DOCUMENT} from '@angular/common';
import {NavigationEnd, Router} from '@angular/router';
import {filter} from 'rxjs/operators';

@Injectable()
export class TreoSplashScreenService {
    constructor(
        @Inject(DOCUMENT) private _document: any,
        private _router: Router
    ) {
        this._init();
    }

    private _init(): void {
        // Hide loading on every NavigationEnd event
        // Which means that all the guards & resolvers are complete and that the new route has been reached
        this._router.events.pipe(
            filter(event => event instanceof NavigationEnd)
        ).subscribe(() => {
            this.hide();
        });
    }

    show(title = '', subtitle = ''): void {
        this._document.body.classList.remove('treo-splash-screen-hidden');
        // Customized by Wedof!
        this._document.getElementById('loading-text-title').innerHTML = title;
        this._document.getElementById('loading-text-subtitle').innerHTML = subtitle;
    }

    hide(): void {
        this._document.body.classList.add('treo-splash-screen-hidden');
        // Customized by Wedof!
        this._document.getElementById('loading-text-title').innerHTML = '';
        this._document.getElementById('loading-text-subtitle').innerHTML = '';
    }
}
