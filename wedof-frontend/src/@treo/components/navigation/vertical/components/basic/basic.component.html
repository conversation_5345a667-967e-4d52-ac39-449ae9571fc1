<!-- Item wrapper -->
<div class="treo-vertical-navigation-item-wrapper"
     [class.treo-vertical-navigation-item-has-subtitle]="!!item.subtitle"
     [ngClass]="item.classes">

    <!-- Item with an internal link -->
    <a class="treo-vertical-navigation-item"
       *ngIf="item.link && !item.externalLink && !item.function && !item.disabled"
       [ngClass]="{'treo-vertical-navigation-item-active-forced': item.active}"
       [routerLink]="[item.link]"
       [queryParams]="item.queryParams ? item.queryParams : {}"
       [routerLinkActive]="'treo-vertical-navigation-item-active'"
       [routerLinkActiveOptions]="{exact: item.exactMatch || false}">
        <ng-container *ngTemplateOutlet="itemTemplate"></ng-container>
    </a>

    <!-- Item with an external link -->
    <a class="treo-vertical-navigation-item"
       *ngIf="item.link && item.externalLink && !item.function && !item.disabled"
       [href]="item.link">
        <ng-container *ngTemplateOutlet="itemTemplate"></ng-container>
    </a>

    <!-- Item with a function -->
    <div class="treo-vertical-navigation-item"
         *ngIf="!item.link && item.function && !item.disabled"
         [ngClass]="{'treo-vertical-navigation-item-active-forced': item.active}"
         (click)="item.function(item)">
        <ng-container *ngTemplateOutlet="itemTemplate"></ng-container>
    </div>

    <!-- Item with an internal link and function -->
    <a class="treo-vertical-navigation-item"
       *ngIf="item.link && !item.externalLink && item.function && !item.disabled"
       [ngClass]="{'treo-vertical-navigation-item-active-forced': item.active}"
       [routerLink]="[item.link]"
       [routerLinkActive]="'treo-vertical-navigation-item-active'"
       [routerLinkActiveOptions]="{exact: item.exactMatch || false}"
       (click)="item.function(item)">
        <ng-container *ngTemplateOutlet="itemTemplate"></ng-container>
    </a>

    <!-- Item with an external link and function -->
    <a class="treo-vertical-navigation-item"
       *ngIf="item.link && item.externalLink && item.function && !item.disabled"
       [href]="item.link"
       (click)="item.function(item)">
        <ng-container *ngTemplateOutlet="itemTemplate"></ng-container>
    </a>

    <!-- Item with a no link and no function -->
    <div class="treo-vertical-navigation-item"
         *ngIf="!item.link && !item.function && !item.disabled"
         [ngClass]="{'treo-vertical-navigation-item-active-forced': item.active}">
        <ng-container *ngTemplateOutlet="itemTemplate"></ng-container>
    </div>

    <!-- Item is disabled -->
    <div class="treo-vertical-navigation-item treo-vertical-navigation-item-disabled"
         *ngIf="item.disabled">
        <ng-container *ngTemplateOutlet="itemTemplate"></ng-container>
    </div>

</div>

<!-- Item template -->
<ng-template #itemTemplate>

    <!-- Icon -->
    <mat-icon class="treo-vertical-navigation-item-icon"
              [ngClass]="item.iconClasses"
              *ngIf="item.icon"
              [svgIcon]="item.icon"></mat-icon>

    <!-- Title & Subtitle -->
    <div class="treo-vertical-navigation-item-title-wrapper">
        <div class="treo-vertical-navigation-item-title">{{item.title}}</div>
        <div class="treo-vertical-navigation-item-subtitle"
             *ngIf="item.subtitle">
            {{item.subtitle}}
        </div>
    </div>

    <!-- Badge -->
    <div class="treo-vertical-navigation-item-badge"
         *ngIf="item.badge">
        <div class="treo-vertical-navigation-item-badge-content"
             [ngClass]="[(item.badge.style != undefined ? 'treo-vertical-navigation-item-badge-style-' + item.badge.style : ''),
                             (item.badge.background != undefined && !item.badge.background.startsWith('#') ? item.badge.background : ''),
                             (item.badge.color != undefined && !item.badge.color.startsWith('#') ? item.badge.color : '')]"
             [ngStyle]="{'background-color': item.badge.background, 'color': item.badge.color}">
            {{item.badge.title}}
        </div>
    </div>

</ng-template>
