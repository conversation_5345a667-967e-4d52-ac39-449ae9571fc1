@import 'treo';

$treo-drawer-width: 320;

treo-drawer {
    position: relative;
    display: flex;
    flex-direction: column;
    flex: 1 1 auto;
    width: #{$treo-drawer-width}px;
    min-width: #{$treo-drawer-width}px;
    max-width: #{$treo-drawer-width}px;
    z-index: 300;
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, .35);

    // Animations
    &.treo-drawer-animations-enabled {
        transition-duration: 400ms;
        transition-timing-function: cubic-bezier(0.25, 0.8, 0.25, 1);
        transition-property: visibility, margin-left, margin-right, transform, width, max-width, min-width;

        .treo-drawer-content {
            transition-duration: 400ms;
            transition-timing-function: cubic-bezier(0.25, 0.8, 0.25, 1);
            transition-property: width, max-width, min-width;
        }
    }

    // Over mode
    &.treo-drawer-mode-over {
        position: absolute;
        top: 0;
        bottom: 0;

        // Fixed mode
        &.treo-drawer-fixed {
            position: fixed;
        }
    }

    // Left position
    &.treo-drawer-position-left {

        // Side mode
        &.treo-drawer-mode-side {
            margin-left: #{$treo-drawer-width}px;

            &.treo-drawer-opened {
                margin-left: 0;
            }
        }

        // Over mode
        &.treo-drawer-mode-over {
            left: 0;
            transform: translate3d(-100%, 0, 0);

            &.treo-drawer-opened {
                transform: translate3d(0, 0, 0);
            }
        }

        // Content
        .treo-drawer-content {
            left: 0;
        }
    }

    // Right position
    &.treo-drawer-position-right {

        // Side mode
        &.treo-drawer-mode-side {
            margin-right: -#{$treo-drawer-width}px;

            &.treo-drawer-opened {
                margin-right: 0;
            }
        }

        // Over mode
        &.treo-drawer-mode-over {
            right: 0;
            transform: translate3d(100%, 0, 0);

            &.treo-drawer-opened {
                transform: translate3d(0, 0, 0);
            }
        }

        // Content
        .treo-drawer-content {
            right: 0;
        }
    }

    // Content
    .treo-drawer-content {
        position: absolute;
        display: flex;
        flex: 1 1 auto;
        top: 0;
        bottom: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
    }
}

// Overlay
.treo-drawer-overlay {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 299;
    opacity: 0;
    background-color: rgba(0, 0, 0, 0.6);

    // Fixed mode
    &.treo-drawer-overlay-fixed {
        position: fixed;
    }

    // Transparent overlay
    &.treo-drawer-overlay-transparent {
        background-color: transparent;
    }
}

// -----------------------------------------------------------------------------------------------------
// @ Theming
// -----------------------------------------------------------------------------------------------------
@include treo-theme {

    $background: map-get($theme, background);

    treo-drawer {
        background: map-get($background, card);

        .treo-drawer-content {
            background: map-get($background, card);
        }
    }
}
