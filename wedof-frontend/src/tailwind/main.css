/* ----------------------------------------------------------------------------------------------------- */
/* This injects Tailwind's component classes and any component classes registered by plugins.
/* ----------------------------------------------------------------------------------------------------- */
@tailwind components;


/* ----------------------------------------------------------------------------------------------------- */
/* Use custom @apply directives here to inline any existing utility classes into your own custom CSS.
/* ----------------------------------------------------------------------------------------------------- */
/**
 * .btn {
 *   @apply font-bold py-2 px-4 rounded;
 * }
 */


/* ----------------------------------------------------------------------------------------------------- */
/* This injects Tailwind's utility classes and any utility classes registered by plugins.
/* ----------------------------------------------------------------------------------------------------- */
@tailwind utilities;


/* ----------------------------------------------------------------------------------------------------- */
/* Use custom @variant directives here to build them.
/* ----------------------------------------------------------------------------------------------------- */
@variants dark-light {}


/* ----------------------------------------------------------------------------------------------------- */
/*  Use this directive to control where Tailwind injects the responsive variations of each utility.
/*  If omitted, Tailwind will append these classes to the very end of your stylesheet by default.
/* ----------------------------------------------------------------------------------------------------- */
@tailwind screens;
