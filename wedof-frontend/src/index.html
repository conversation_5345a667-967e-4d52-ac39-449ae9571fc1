<!doctype html>
<html lang="fr" translate="no">
<head>
    <!-- Angular index -->
    <meta charset="utf-8">
    <title>Wedof</title>
    <base href="/">

    <meta name="viewport"
          content="width=device-width, height=device-height, initial-scale=1.0, minimum-scale=1.0">

    <!-- Favicon -->
    <link rel="icon"
          type="image/png"
          href="favicon-16x16.png">
    <link rel="icon"
          type="image/png"
          href="favicon-32x32.png">

    <!-- Fonts -->
    <link href="assets/fonts/roboto/roboto.css"
          rel="stylesheet">
    <link href="assets/fonts/plex/plex.css"
          rel="stylesheet">
    <link href="assets/fonts/inter/inter.css"
          rel="stylesheet">
    <link href="assets/fonts/custom/custom.css"
          rel="stylesheet">
    <!-- Icon Fonts -->
    <link href="assets/fonts/material-icons/material-icons.css"
          rel="stylesheet">
    <script type="application/javascript" src="https://apis.google.com/js/api.js?onload=loadPicker"></script>
    <script type="application/javascript" id="wedof-script"
            src="/assets/cli/wedof-v1.js?time=itsChristmasTime"></script>
    <script type="application/javascript" id="wedof-sign-script"
            src="https://sign.wedof.fr/js/form.js"></script>
    <!-- Splash screen styles -->
    <style>
        body treo-splash-screen {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #1A202E;
            color: #FBFDFE;
            z-index: 999999;
            pointer-events: none;
            opacity: 1;
            visibility: visible;
            transition: opacity 400ms cubic-bezier(0.4, 0, 0.2, 1);
        }

        body treo-splash-screen img {
            width: 120px;
            max-width: 120px;
        }

        body treo-splash-screen .spinner {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 40px;
            width: 56px;
        }

        body treo-splash-screen .spinner > div {
            width: 12px;
            height: 12px;
            background-color: #4AF7FF;
            border-radius: 100%;
            display: inline-block;
            -webkit-animation: treo-bouncedelay 1s infinite ease-in-out both;
            animation: treo-bouncedelay 1s infinite ease-in-out both;
        }

        body treo-splash-screen .spinner .bounce1 {
            -webkit-animation-delay: -0.32s;
            animation-delay: -0.32s;
        }

        body treo-splash-screen .spinner .bounce2 {
            -webkit-animation-delay: -0.16s;
            animation-delay: -0.16s;
        }

        @-webkit-keyframes treo-bouncedelay {
            0%, 80%, 100% {
                -webkit-transform: scale(0)
            }
            40% {
                -webkit-transform: scale(1.0)
            }
        }

        @keyframes treo-bouncedelay {
            0%, 80%, 100% {
                -webkit-transform: scale(0);
                transform: scale(0);
            }
            40% {
                -webkit-transform: scale(1.0);
                transform: scale(1.0);
            }
        }

        body:not(.treo-splash-screen-hidden) {
            overflow: hidden;
        }

        body.treo-splash-screen-hidden treo-splash-screen {
            visibility: hidden;
            opacity: 0;
        }
    </style>

</head>

<body>

<!-- Splash screen -->
<treo-splash-screen>
    <img src="assets/images/logo/logo.svg"
         alt="Wedof logo">
    <div class="spinner">
        <div class="bounce1"></div>
        <div class="bounce2"></div>
        <div class="bounce3"></div>
    </div>
    <div class="text-center">
        <p class="pt-4 font-bold text-xl uppercase" id="loading-text-title"></p>
        <p class="text-xl" id="loading-text-subtitle"></p>
    </div>
</treo-splash-screen>

<!-- App root -->
<app-root></app-root>
</body>
<script type="text/javascript">
    (function (C, A, L) {
        let p = function (a, ar) {
            a.q.push(ar);
        };
        let d = C.document;
        C.Cal = C.Cal || function () {
            let cal = C.Cal;
            let ar = arguments;
            if (!cal.loaded) {
                cal.ns = {};
                cal.q = cal.q || [];
                d.head.appendChild(d.createElement("script")).src = A;
                cal.loaded = true;
            }
            if (ar[0] === L) {
                const api = function () {
                    p(api, arguments);
                };
                const namespace = ar[1];
                api.q = api.q || [];
                typeof namespace === "string" ? (cal.ns[namespace] = api) && p(api, ar) : p(cal, ar);
                return;
            }
            p(cal, ar);
        };
    })(window, "https://calendar.wedof.fr/embed/embed.js", "init");
    Cal("init", {origin: "https://calendar.wedof.fr"});
    Cal("ui", {
        "styles": {
            "branding": {
                "brandColor": "#000000"
            }
        },
        "hideEventTypeDetails": false
    });
</script>
</html>
