{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"wedof": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss", "skipTests": true}, "@schematics/angular:class": {"skipTests": true}, "@schematics/angular:directive": {"skipTests": true}, "@schematics/angular:pipe": {"skipTests": true}, "@schematics/angular:service": {"skipTests": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "aot": true, "allowedCommonJsDependencies": ["highlight.js", "crypto-js/enc-utf8", "crypto-js/hmac-sha256", "crypto-js/enc-base64", "lodash", "file-saver", "moment-business"], "assets": ["src/favicon-16x16.png", "src/favicon-32x32.png", "src/assets", {"glob": "**/*", "input": "node_modules/ngx-extended-pdf-viewer/assets/", "output": "/assets/"}], "stylePreprocessorOptions": {"includePaths": ["src/@treo/styles"]}, "styles": ["src/styles/vendors.scss", "src/@treo/styles/main.scss", "src/styles/styles.scss", "src/styles/tailwind.scss"], "scripts": [{"input": "src/assets/cli/wedof-v1.js", "inject": false, "bundleName": "wedof-v1.min"}]}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "serviceWorker": true, "budgets": [{"type": "initial", "maximumWarning": "5mb", "maximumError": "8mb"}, {"type": "anyComponentStyle", "maximumWarning": "100kb", "maximumError": "150kb"}]}, "staging": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.staging.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "serviceWorker": true, "budgets": [{"type": "initial", "maximumWarning": "5mb", "maximumError": "8mb"}, {"type": "anyComponentStyle", "maximumWarning": "100kb", "maximumError": "150kb"}]}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "wedof:build"}, "configurations": {"production": {"browserTarget": "wedof:build:production"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "wedof:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "assets": ["src/favicon-16x16.png", "src/favicon-32x32.png", "src/assets"], "styles": ["src/styles/vendors.scss", "src/@treo/styles/main.scss", "src/styles/styles.scss", "src/styles/tailwind.scss"], "scripts": []}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["tsconfig.app.json", "tsconfig.spec.json", "e2e/tsconfig.json"], "exclude": ["**/node_modules/**"]}}, "e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "wedof:serve"}, "configurations": {"production": {"devServerTarget": "wedof:serve:production"}}}}}}, "defaultProject": "wedof", "cli": {"analytics": false}}