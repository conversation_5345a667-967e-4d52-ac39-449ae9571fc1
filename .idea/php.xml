<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="MessDetector">
    <phpmd_settings>
      <MessDetectorConfiguration asDefaultInterpreter="true" />
    </phpmd_settings>
  </component>
  <component name="PhpCodeSniffer">
    <phpcs_settings>
      <PhpCSConfiguration asDefaultInterpreter="true" />
    </phpcs_settings>
  </component>
  <component name="PhpIncludePathManager">
    <include_path>
      <path value="$PROJECT_DIR$/wedof-backend/vendor/composer" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/jms/serializer-bundle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/jms/metadata" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/jms/serializer" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/psr/http-message" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/psr/container" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/psr/cache" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/psr/simple-cache" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/psr/http-factory" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/psr/http-client" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/psr/log" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/vich/uploader-bundle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/voku/simple_html_dom" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/sllh/iso-codes-validator" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/twig/twig" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/embed/embed" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/lexik/jwt-authentication-bundle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/voku/html-min" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/brick/math" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/beelab/tag-bundle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/defuse/php-encryption" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/meyfa/php-svg" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/nikic/php-parser" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/gpslab/geoip2" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/league/oauth2-server-bundle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/ezyang/htmlpurifier" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/geoip2/geoip2" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/league/uri-interfaces" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/league/uri" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/league/oauth2-google" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/league/oauth2-server" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/matomo/device-detector" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/namshi/jose" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/league/event" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/league/oauth2-client" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/ramsey/collection" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/ramsey/uuid" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/nelmio/api-doc-bundle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/nyholm/psr7" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/yectep/phpspreadsheet-bundle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/egulias/email-validator" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/sensio/framework-extra-bundle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/stripe/stripe-php" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/knplabs/knp-components" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/knplabs/knp-paginator-bundle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/giggsey/locale" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/giggsey/libphonenumber-for-php" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/monolog/monolog" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/myclabs/php-enum" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/laminas/laminas-code" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/maxmind/web-service-common" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/phar-io/version" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/myclabs/deep-copy" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/phar-io/manifest" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/phpunit/phpunit" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/phpunit/php-text-template" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/phpunit/php-timer" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/phpunit/php-token-stream" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/scienta/doctrine-json-functions" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/framework-bundle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/phpunit/php-code-coverage" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/phpunit/php-file-iterator" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/polyfill-intl-icu" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/form" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/monolog-bundle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/slack-notifier" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/polyfill-php73" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/serializer" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/amqp-messenger" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/amazon-mailer" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/validator" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/monolog-bridge" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/polyfill-php80" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/dotenv" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/polyfill-php81" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/polyfill-php72" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/cache" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/asset" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/translation" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/translation-contracts" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/browser-kit" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/security-bundle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/security-http" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/expression-language" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/notifier" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/config" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/lock" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/var-dumper" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/event-dispatcher" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/phpunit-bridge" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/options-resolver" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/security-csrf" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/mailer" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/filesystem" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/maker-bundle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/twig-bridge" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/redis-messenger" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/polyfill-intl-idn" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/polyfill-intl-normalizer" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/css-selector" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/polyfill-uuid" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/security-guard" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/dom-crawler" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/property-access" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/event-dispatcher-contracts" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/messenger" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/http-client" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/stopwatch" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/twig-bundle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/finder" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/http-kernel" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/yaml" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/doctrine-messenger" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/psr-http-message-bridge" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/string" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/security-core" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/mime" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/rate-limiter" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/service-contracts" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/http-client-contracts" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/deprecation-contracts" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/routing" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/doctrine-bridge" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/var-exporter" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/property-info" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/http-foundation" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/cache-contracts" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/dependency-injection" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/flex" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/error-handler" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/console" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/polyfill-mbstring" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/password-hasher" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/process" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/zircote/swagger-php" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/2captcha/2captcha" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/polyfill-intl-grapheme" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/theseer/tokenizer" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/beberlei/doctrineextensions" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/doctrine/doctrine-migrations-bundle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/doctrine/cache" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/doctrine/lexer" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/doctrine/migrations" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/doctrine/data-fixtures" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/doctrine/annotations" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/doctrine/common" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/doctrine/dbal" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/doctrine/inflector" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/doctrine/instantiator" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/doctrine/orm" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/doctrine/persistence" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/doctrine/doctrine-fixtures-bundle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/doctrine/sql-formatter" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/doctrine/event-manager" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/doctrine/collections" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/firebase/php-jwt" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/gesdinet/jwt-refresh-token-bundle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/doctrine/doctrine-bundle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/fakerphp/faker" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/overtrue/pinyin" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/async-aws/core" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/lcobucci/jwt" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/lcobucci/clock" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/lasserafn/php-string-script-language" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/lasserafn/php-initials" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/async-aws/ses" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/lasserafn/php-initial-avatar-generator" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/markbaker/matrix" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/phpoffice/phpspreadsheet" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/maennchen/zipstream-php" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/markbaker/complex" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/sebastian/global-state" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/sebastian/object-enumerator" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/ralouphie/getallheaders" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/sebastian/comparator" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/sebastian/diff" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/sebastian/recursion-context" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/sebastian/object-reflector" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/sebastian/code-unit-reverse-lookup" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/sebastian/environment" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/sebastian/exporter" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/sebastian/type" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/sebastian/version" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/zenstruck/foundry" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/zenstruck/assert" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/sebastian/resource-operations" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/guzzlehttp/psr7" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/guzzlehttp/guzzle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/zenstruck/callback" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/guzzlehttp/promises" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/willdurand/negotiation" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/willdurand/hateoas-bundle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/maxmind-db/reader" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/willdurand/jsonp-callback-validator" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/intervention/image" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfonycasts/verify-email-bundle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/willdurand/hateoas" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/friendsofphp/proxy-manager-lts" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/mustangostang/spyc" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/adam-paterson/oauth2-slack" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/knpuniversity/oauth2-client-bundle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/ronanguilloux/isocodes" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/simplehtmldom/simplehtmldom" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/stevenmaguire/oauth2-salesforce" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/friendsofsymfony/rest-bundle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/proxy-manager-bridge" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/nelmio/cors-bundle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/minishlink/web-push" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/spomky-labs/base64url" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/bentools/webpush-bundle" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/chillerlan/php-qrcode" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/chillerlan/php-settings-container" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/web-token/jwt-signature-algorithm-ecdsa" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/web-token/jwt-key-mgmt" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/web-token/jwt-util-ecc" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/web-token/jwt-core" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/web-token/jwt-signature" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/fgrosse/phpasn1" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/uid" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/ml/iri" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/ml/json-ld" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/aws/aws-crt-php" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/aws/aws-sdk-php" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/oscarotero/html-parser" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/mtdowling/jmespath.php" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/symfony/google-mailer" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/knplabs/gaufrette" />
      <path value="$PROJECT_DIR$/wedof-backend/vendor/knplabs/knp-gaufrette-bundle" />
    </include_path>
  </component>
  <component name="PhpProjectSharedConfiguration" php_language_level="7.4" />
  <component name="PhpRuntimeConfiguration">
    <extensions>
      <extension name="Ev" enabled="false" />
      <extension name="SQLite" enabled="false" />
      <extension name="SplType" enabled="false" />
      <extension name="Zend OPcache" enabled="false" />
      <extension name="ZendDebugger" enabled="false" />
      <extension name="ZendUtils" enabled="false" />
      <extension name="apache" enabled="false" />
      <extension name="apcu" enabled="false" />
      <extension name="brotli" enabled="false" />
      <extension name="cassandra" enabled="false" />
      <extension name="couchbase" enabled="false" />
      <extension name="cubrid" enabled="false" />
      <extension name="dba" enabled="false" />
      <extension name="decimal" enabled="false" />
      <extension name="dio" enabled="false" />
      <extension name="elastic_apm" enabled="false" />
      <extension name="enchant" enabled="false" />
      <extension name="fann" enabled="false" />
      <extension name="ffmpeg" enabled="false" />
      <extension name="frankenphp" enabled="false" />
      <extension name="gearman" enabled="false" />
      <extension name="geoip" enabled="false" />
      <extension name="geos" enabled="false" />
      <extension name="gmagick" enabled="false" />
      <extension name="gmp" enabled="false" />
      <extension name="gnupg" enabled="false" />
      <extension name="grpc" enabled="false" />
      <extension name="http" enabled="false" />
      <extension name="ibm_db2" enabled="false" />
      <extension name="igbinary" enabled="false" />
      <extension name="imagick" enabled="false" />
      <extension name="inotify" enabled="false" />
      <extension name="interbase" enabled="false" />
      <extension name="jsonpath" enabled="false" />
      <extension name="judy" enabled="false" />
      <extension name="libevent" enabled="false" />
      <extension name="libsodium" enabled="false" />
      <extension name="mailparse" enabled="false" />
      <extension name="mcrypt" enabled="false" />
      <extension name="memcache" enabled="false" />
      <extension name="memcached" enabled="false" />
      <extension name="ming" enabled="false" />
      <extension name="mongo" enabled="false" />
      <extension name="mongodb" enabled="false" />
      <extension name="mosquitto-php" enabled="false" />
      <extension name="mqseries" enabled="false" />
      <extension name="mssql" enabled="false" />
      <extension name="mysql" enabled="false" />
      <extension name="mysql_xdevapi" enabled="false" />
      <extension name="ncurses" enabled="false" />
      <extension name="newrelic" enabled="false" />
      <extension name="oauth" enabled="false" />
      <extension name="oci8" enabled="false" />
      <extension name="odbc" enabled="false" />
      <extension name="opentelemetry" enabled="false" />
      <extension name="pdflib" enabled="false" />
      <extension name="pspell" enabled="false" />
      <extension name="pthreads" enabled="false" />
      <extension name="rar" enabled="false" />
      <extension name="recode" enabled="false" />
      <extension name="redis" enabled="false" />
      <extension name="relay" enabled="false" />
      <extension name="rrd" enabled="false" />
      <extension name="shmop" enabled="false" />
      <extension name="snappy" enabled="false" />
      <extension name="snmp" enabled="false" />
      <extension name="sqlsrv" enabled="false" />
      <extension name="ssh2" enabled="false" />
      <extension name="suhosin" enabled="false" />
      <extension name="svn" enabled="false" />
      <extension name="sybase" enabled="false" />
      <extension name="sysvmsg" enabled="false" />
      <extension name="sysvsem" enabled="false" />
      <extension name="sysvshm" enabled="false" />
      <extension name="tidy" enabled="false" />
      <extension name="v8js" enabled="false" />
      <extension name="wddx" enabled="false" />
      <extension name="win32service" enabled="false" />
      <extension name="wincache" enabled="false" />
      <extension name="xhprof" enabled="false" />
      <extension name="xlswriter" enabled="false" />
      <extension name="yaml" enabled="false" />
      <extension name="zend" enabled="false" />
      <extension name="zmq" enabled="false" />
    </extensions>
  </component>
  <component name="PhpUnit">
    <phpunit_settings>
      <PhpUnitSettings configuration_file_path="$PROJECT_DIR$/wedof-backend/phpunit.xml.dist" custom_loader_path="$PROJECT_DIR$/wedof-backend/vendor/autoload.php" use_configuration_file="true" />
    </phpunit_settings>
  </component>
</project>