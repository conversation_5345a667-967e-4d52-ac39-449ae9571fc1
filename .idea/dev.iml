<?xml version="1.0" encoding="UTF-8"?>
<module type="JAVA_MODULE" version="4">
  <component name="NewModuleRootManager" inherit-compiler-output="true">
    <exclude-output />
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/wedof-backend/src" isTestSource="false" packagePrefix="App\" />
      <sourceFolder url="file://$MODULE_DIR$/wedof-backend/tests" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/wedof-backend/tests" isTestSource="true" packagePrefix="App\Tests\" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/2captcha/2captcha" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/adam-paterson/oauth2-slack" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/async-aws/core" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/async-aws/ses" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/beberlei/doctrineextensions" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/beelab/tag-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/brick/math" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/composer" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/defuse/php-encryption" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/doctrine/annotations" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/doctrine/cache" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/doctrine/collections" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/doctrine/common" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/doctrine/data-fixtures" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/doctrine/dbal" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/doctrine/deprecations" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/doctrine/doctrine-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/doctrine/doctrine-fixtures-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/doctrine/doctrine-migrations-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/doctrine/event-manager" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/doctrine/inflector" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/doctrine/instantiator" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/doctrine/lexer" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/doctrine/migrations" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/doctrine/orm" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/doctrine/persistence" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/doctrine/sql-formatter" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/egulias/email-validator" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/embed/embed" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/ezyang/htmlpurifier" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/fakerphp/faker" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/firebase/php-jwt" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/friendsofphp/proxy-manager-lts" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/friendsofsymfony/rest-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/geoip2/geoip2" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/gesdinet/jwt-refresh-token-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/giggsey/libphonenumber-for-php" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/giggsey/locale" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/gpslab/geoip2" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/guzzlehttp/guzzle" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/guzzlehttp/promises" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/guzzlehttp/psr7" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/intervention/image" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/jms/metadata" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/jms/serializer" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/jms/serializer-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/knplabs/knp-components" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/knplabs/knp-paginator-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/knpuniversity/oauth2-client-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/laminas/laminas-code" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/lasserafn/php-initial-avatar-generator" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/lasserafn/php-initials" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/lasserafn/php-string-script-language" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/lcobucci/clock" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/lcobucci/jwt" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/league/event" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/league/oauth2-client" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/league/oauth2-google" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/league/oauth2-server" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/league/oauth2-server-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/league/uri" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/league/uri-interfaces" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/lexik/jwt-authentication-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/maennchen/zipstream-php" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/markbaker/complex" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/markbaker/matrix" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/matomo/device-detector" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/maxmind-db/reader" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/maxmind/web-service-common" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/meyfa/php-svg" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/monolog/monolog" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/mustangostang/spyc" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/myclabs/deep-copy" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/myclabs/php-enum" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/namshi/jose" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/nelmio/api-doc-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/nikic/php-parser" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/nyholm/psr7" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/overtrue/pinyin" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/phar-io/manifest" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/phar-io/version" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/phpdocumentor/reflection-common" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/phpdocumentor/reflection-docblock" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/phpdocumentor/type-resolver" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/phpoffice/phpspreadsheet" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/phpstan/phpdoc-parser" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/phpunit/php-code-coverage" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/phpunit/php-file-iterator" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/phpunit/php-text-template" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/phpunit/php-timer" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/phpunit/php-token-stream" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/phpunit/phpunit" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/psr/cache" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/psr/container" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/psr/event-dispatcher" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/psr/http-client" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/psr/http-factory" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/psr/http-message" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/psr/log" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/psr/simple-cache" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/ralouphie/getallheaders" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/ramsey/collection" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/ramsey/uuid" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/ronanguilloux/isocodes" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/scienta/doctrine-json-functions" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/sebastian/code-unit-reverse-lookup" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/sebastian/comparator" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/sebastian/diff" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/sebastian/environment" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/sebastian/exporter" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/sebastian/global-state" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/sebastian/object-enumerator" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/sebastian/object-reflector" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/sebastian/recursion-context" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/sebastian/resource-operations" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/sebastian/type" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/sebastian/version" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/sensio/framework-extra-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/simplehtmldom/simplehtmldom" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/sllh/iso-codes-validator" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/stevenmaguire/oauth2-salesforce" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/stripe/stripe-php" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/amazon-mailer" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/amqp-messenger" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/asset" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/browser-kit" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/cache" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/cache-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/config" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/console" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/css-selector" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/dependency-injection" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/deprecation-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/doctrine-bridge" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/doctrine-messenger" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/dom-crawler" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/dotenv" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/error-handler" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/event-dispatcher" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/event-dispatcher-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/expression-language" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/filesystem" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/finder" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/flex" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/form" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/framework-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/http-client" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/http-client-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/http-foundation" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/http-kernel" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/lock" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/mailer" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/maker-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/messenger" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/mime" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/monolog-bridge" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/monolog-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/notifier" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/options-resolver" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/password-hasher" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/phpunit-bridge" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/polyfill-intl-grapheme" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/polyfill-intl-icu" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/polyfill-intl-idn" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/polyfill-intl-normalizer" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/polyfill-mbstring" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/polyfill-php72" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/polyfill-php73" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/polyfill-php80" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/polyfill-php81" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/polyfill-uuid" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/process" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/property-access" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/property-info" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/psr-http-message-bridge" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/rate-limiter" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/redis-messenger" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/routing" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/security-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/security-core" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/security-csrf" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/security-guard" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/security-http" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/serializer" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/service-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/slack-notifier" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/stopwatch" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/string" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/translation" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/translation-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/twig-bridge" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/twig-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/validator" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/var-dumper" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/var-exporter" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/yaml" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfonycasts/verify-email-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/theseer/tokenizer" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/twig/twig" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/vich/uploader-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/voku/html-min" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/voku/simple_html_dom" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/webmozart/assert" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/willdurand/hateoas" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/willdurand/hateoas-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/willdurand/jsonp-callback-validator" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/willdurand/negotiation" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/yectep/phpspreadsheet-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/zenstruck/assert" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/zenstruck/callback" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/zenstruck/foundry" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/zircote/swagger-php" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-frontend/dist" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-frontend/tmp" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/proxy-manager-bridge" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/nelmio/cors-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/bentools/webpush-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/chillerlan/php-qrcode" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/chillerlan/php-settings-container" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/fgrosse/phpasn1" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/minishlink/web-push" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/spomky-labs/base64url" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/uid" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/web-token/jwt-core" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/web-token/jwt-key-mgmt" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/web-token/jwt-signature" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/web-token/jwt-signature-algorithm-ecdsa" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/web-token/jwt-util-ecc" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/aws/aws-crt-php" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/aws/aws-sdk-php" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/ml/iri" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/ml/json-ld" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/mtdowling/jmespath.php" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/oscarotero/html-parser" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/symfony/google-mailer" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/knplabs/gaufrette" />
      <excludeFolder url="file://$MODULE_DIR$/wedof-backend/vendor/knplabs/knp-gaufrette-bundle" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
</module>