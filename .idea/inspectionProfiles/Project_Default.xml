<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="AngularMissingOrInvalidDeclarationInModule" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="DuplicatedCode" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <Languages>
        <language minSize="132" name="TypeScript" />
        <language minSize="48" name="PHP" />
      </Languages>
    </inspection_tool>
    <inspection_tool class="ForgottenDebugOutputInspection" enabled="true" level="ERROR" enabled_by_default="true">
      <option name="configuration">
        <list>
          <option value="\Codeception\Util\Debug::debug" />
          <option value="\Codeception\Util\Debug::pause" />
          <option value="\Doctrine\Common\Util\Debug::dump" />
          <option value="\Doctrine\Common\Util\Debug::export" />
          <option value="\Illuminate\Support\Debug\Dumper::dump" />
          <option value="\Symfony\Component\Debug\Debug::enable" />
          <option value="\Symfony\Component\Debug\DebugClassLoader::enable" />
          <option value="\Symfony\Component\Debug\ErrorHandler::register" />
          <option value="\Symfony\Component\Debug\ExceptionHandler::register" />
          <option value="\TYPO3\CMS\Core\Utility\DebugUtility::debug" />
          <option value="\Zend\Debug\Debug::dump" />
          <option value="\Zend\Di\Display\Console::export" />
          <option value="dd" />
          <option value="debug_print_backtrace" />
          <option value="debug_zval_dump" />
          <option value="dpm" />
          <option value="dpq" />
          <option value="dsm" />
          <option value="dump" />
          <option value="dvm" />
          <option value="error_log" />
          <option value="kpr" />
          <option value="phpinfo" />
          <option value="print_r" />
          <option value="var_dump" />
          <option value="var_export" />
          <option value="wp_die" />
          <option value="xdebug_break" />
          <option value="xdebug_call_class" />
          <option value="xdebug_call_file" />
          <option value="xdebug_call_function" />
          <option value="xdebug_call_line" />
          <option value="xdebug_code_coverage_started" />
          <option value="xdebug_debug_zval" />
          <option value="xdebug_debug_zval_stdout" />
          <option value="xdebug_dump_superglobals" />
          <option value="xdebug_enable" />
          <option value="xdebug_get_code_coverage" />
          <option value="xdebug_get_collected_errors" />
          <option value="xdebug_get_declared_vars" />
          <option value="xdebug_get_function_stack" />
          <option value="xdebug_get_headers" />
          <option value="xdebug_get_monitored_functions" />
          <option value="xdebug_get_profiler_filename" />
          <option value="xdebug_get_stack_depth" />
          <option value="xdebug_get_tracefile_name" />
          <option value="xdebug_is_enabled" />
          <option value="xdebug_memory_usage" />
          <option value="xdebug_peak_memory_usage" />
          <option value="xdebug_print_function_stack" />
          <option value="xdebug_start_code_coverage" />
          <option value="xdebug_start_error_collection" />
          <option value="xdebug_start_function_monitor" />
          <option value="xdebug_start_trace" />
          <option value="xdebug_stop_code_coverage" />
          <option value="xdebug_stop_error_collection" />
          <option value="xdebug_stop_function_monitor" />
          <option value="xdebug_stop_trace" />
          <option value="xdebug_time_index" />
          <option value="xdebug_var_dump" />
        </list>
      </option>
      <option name="migratedIntoUserSpace" value="true" />
    </inspection_tool>
    <inspection_tool class="NestedTernaryOperatorInspection" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="PhpExpressionWithSameOperandsInspection" enabled="false" level="WEAK WARNING" enabled_by_default="false" />
    <inspection_tool class="PhpInconsistentReturnPointsInspection" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="TsLint" enabled="true" level="WARNING" enabled_by_default="true" />
  </profile>
</component>