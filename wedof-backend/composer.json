{"type": "project", "license": "proprietary", "require": {"php": "^7.4", "ext-amqp": "*", "ext-ctype": "*", "ext-curl": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-gd": "*", "ext-iconv": "*", "ext-json": "*", "ext-libxml": "*", "ext-zip": "*", "2captcha/2captcha": "^1.0", "adam-paterson/oauth2-slack": "^1.1", "aws/aws-sdk-php": "^3.0", "beberlei/doctrineextensions": "^1.3", "beelab/tag-bundle": "^1.5", "bentools/webpush-bundle": "0.9", "chillerlan/php-qrcode": "^5.0", "composer/package-versions-deprecated": "*********", "doctrine/doctrine-bundle": "^2.1", "doctrine/doctrine-migrations-bundle": "^2", "doctrine/orm": "^2.7", "embed/embed": "4.4.11", "ezyang/htmlpurifier": "^4.16", "fakerphp/faker": "^1.22", "firebase/php-jwt": "^5.2", "friendsofsymfony/rest-bundle": "^3.0", "gaufrette/aws-s3-adapter": "^0.4.0", "gesdinet/jwt-refresh-token-bundle": "^v1.0.0-beta4", "guzzlehttp/promises": "1.5.3", "jms/serializer-bundle": "^3.7", "knplabs/knp-gaufrette-bundle": "^0.9.0", "knplabs/knp-paginator-bundle": "^5.3", "knpuniversity/oauth2-client-bundle": "^2.8", "lasserafn/php-initial-avatar-generator": "^4.2", "lcobucci/jwt": "^3.3", "league/oauth2-google": "^3.0", "league/oauth2-server-bundle": "^0.5.0", "lexik/jwt-authentication-bundle": "^2.9", "matomo/device-detector": "^6.1", "myclabs/php-enum": "^1.7", "nelmio/api-doc-bundle": "^4.0", "nelmio/cors-bundle": "^2.4", "nyholm/psr7": "^1.3", "phpoffice/phpspreadsheet": "^1.29", "ramsey/uuid": "^4.1", "scienta/doctrine-json-functions": "^4.3", "sensio/framework-extra-bundle": "^5.5", "simplehtmldom/simplehtmldom": "^2.0@RC", "sllh/iso-codes-validator": "^4.1", "stevenmaguire/oauth2-salesforce": "^2.0", "stripe/stripe-php": "^7.100", "symfony/amazon-mailer": "5.4.*", "symfony/amqp-messenger": "5.4.*", "symfony/asset": "5.4.*", "symfony/cache": "5.4.*", "symfony/console": "5.4.*", "symfony/doctrine-messenger": "5.4.*", "symfony/dotenv": "5.4.*", "symfony/expression-language": "5.4.*", "symfony/filesystem": "5.4.*", "symfony/flex": "^1.9", "symfony/framework-bundle": "5.4.*", "symfony/google-mailer": "5.4.*", "symfony/http-client": "5.4.*", "symfony/lock": "5.4.*", "symfony/mailer": "5.4.*", "symfony/messenger": "5.4.*", "symfony/monolog-bundle": "^3.5", "symfony/polyfill-uuid": "^1.20", "symfony/process": "5.4.*", "symfony/proxy-manager-bridge": "5.4.*", "symfony/rate-limiter": "5.4.*", "symfony/security-bundle": "5.4.*", "symfony/serializer": "5.4.*", "symfony/slack-notifier": "5.4.*", "symfony/twig-bundle": "5.4.*", "symfony/uid": "5.4.*", "symfony/validator": "5.4.*", "symfony/yaml": "5.4.*", "symfonycasts/verify-email-bundle": "^1.0", "vich/uploader-bundle": "^1.23.1", "willdurand/hateoas-bundle": "^2.1", "yectep/phpspreadsheet-bundle": "^1.0"}, "require-dev": {"doctrine/doctrine-fixtures-bundle": "^3.4", "phpunit/phpunit": "^8.5", "symfony/browser-kit": "5.4.*", "symfony/css-selector": "5.4.*", "symfony/maker-bundle": "^1.43", "symfony/phpunit-bridge": "5.4.*", "zenstruck/foundry": "^1.7"}, "config": {"platform": {"php": "7.4"}, "preferred-install": {"*": "dist"}, "sort-packages": true, "allow-plugins": {"symfony/flex": true}}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"paragonie/random_compat": "2.*", "symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php71": "*", "symfony/polyfill-php70": "*", "symfony/polyfill-php56": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "5.4.*"}}}