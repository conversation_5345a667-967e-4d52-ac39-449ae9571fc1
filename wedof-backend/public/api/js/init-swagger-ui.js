window.onload = function () {
    const data = JSON.parse(document.getElementById('swagger-data').innerText);
    const ui = SwaggerUIBundle({
        oauth2RedirectUrl: data['redirect_uri'],
        spec: data.spec,
        dom_id: '#swagger-ui',
        validatorUrl: null,
        presets: [
            SwaggerUIBundle.presets.apis,
            SwaggerUIStandalonePreset
        ],
        plugins: [
            SwaggerUIBundle.plugins.DownloadUrl
        ],
        layout: 'StandaloneLayout'
    });

    // ui.initOAuth({
    //     clientId: data['client_id'],
    //     clientSecret: data['client_secret']
    // });

    const storageKey = 'nelmio_api_auth';

    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('apiKey')) {
        sessionStorage.setItem(storageKey, JSON.stringify(
            {accessCode: {
                name: "accessCode",
                schema: {type: "api<PERSON><PERSON>", name: "X-API-KEY", in: "header"},
                value: urlParams.get('apiKey')
            }
        }));
    }

    // if we have auth in storage use it
    if (sessionStorage.getItem(storageKey)) {
        try {
            ui.authActions.authorize(JSON.parse(sessionStorage.getItem(storageKey)));
        } catch (ignored) {
            // catch any errors here so it does not stop script execution
        }
    }

    // hook into authorize to store the auth in local storage when user performs authorization
    const currentAuthorize = ui.authActions.authorize;

    ui.authActions.authorize = function (payload) {

        sessionStorage.setItem(storageKey, JSON.stringify(payload));
        return currentAuthorize(payload);
    };

    // hook into logout to clear auth from storage if user logs out
    const currentLogout = ui.authActions.logout;
    ui.authActions.logout = function (payload) {
        sessionStorage.removeItem(storageKey);
        return currentLogout(payload);
    };

    window.ui = ui;
};
