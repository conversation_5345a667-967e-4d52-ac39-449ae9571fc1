<?xml version="1.0" encoding="UTF-8"?>
<module type="WEB_MODULE" version="4">
  <component name="NewModuleRootManager" inherit-compiler-output="true">
    <exclude-output />
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/src" isTestSource="false" packagePrefix="App\" />
      <sourceFolder url="file://$MODULE_DIR$/tests" isTestSource="true" packagePrefix="App\Tests\" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/http-factory" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/cache" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/namshi/jose" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/http-message" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/knplabs/knp-paginator-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/knplabs/knp-components" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/jms/serializer-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/jms/serializer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/jms/metadata" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/asset" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/dotenv" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/cache-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-uuid" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/twig-bridge" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/serializer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/migrations" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/process" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/yaml" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/http-kernel" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/var-dumper" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/maker-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/sql-formatter" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/security-guard" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/lcobucci/jwt" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-intl-idn" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/console" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/filesystem" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-php73" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/dbal" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/nikic/php-parser" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/twig-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-php72" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/security-http" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/error-handler" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/mime" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/mailer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/orm" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/zircote/swagger-php" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/config" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-intl-normalizer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/dependency-injection" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/ramsey/uuid" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/monolog-bridge" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/doctrine-bridge" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/http-client" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/ramsey/collection" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/http-client-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/finder" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpdocumentor/reflection-common" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/routing" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/translation" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpdocumentor/reflection-docblock" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/security-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpdocumentor/type-resolver" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/validator" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/service-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/friendsofsymfony/rest-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/framework-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/translation-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/event-dispatcher" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/http-foundation" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/flex" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/options-resolver" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/security-csrf" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/stopwatch" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/property-access" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/event-dispatcher-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/nyholm/psr7" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/stripe/stripe-php" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/property-info" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/security-core" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-php80" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/var-exporter" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/willdurand/hateoas-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/cache" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/egulias/email-validator" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/deprecation-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/willdurand/hateoas" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/monolog-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/willdurand/negotiation" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/expression-language" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-mbstring" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/willdurand/jsonp-callback-validator" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/doctrine-migrations-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/common" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/composer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/annotations" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfonycasts/verify-email-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/inflector" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/collections" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/persistence" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/doctrine-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/myclabs/php-enum" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/instantiator" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/cache" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/webmozart/assert" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/lexik/jwt-authentication-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/lexer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/event-manager" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/laminas/laminas-code" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/brick/math" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/gesdinet/jwt-refresh-token-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/firebase/php-jwt" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sensio/framework-extra-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/nelmio/api-doc-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/twig/twig" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/monolog/monolog" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/simplehtmldom/simplehtmldom" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/log" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/container" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/lock" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/beberlei/doctrineextensions" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/scienta/doctrine-json-functions" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/voku/simple_html_dom" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/voku/html-min" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/css-selector" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/fakerphp/faker" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/zenstruck/foundry" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/zenstruck/callback" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/doctrine-fixtures-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/data-fixtures" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/ralouphie/getallheaders" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/knpuniversity/oauth2-client-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/guzzlehttp/psr7" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/guzzlehttp/guzzle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/guzzlehttp/promises" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/http-client" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/league/oauth2-client" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/adam-paterson/oauth2-slack" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/stevenmaguire/oauth2-salesforce" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phar-io/version" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phar-io/manifest" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/resource-operations" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/version" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/exporter" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/environment" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/recursion-context" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/diff" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/type" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/object-enumerator" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/global-state" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/code-unit-reverse-lookup" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/object-reflector" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/comparator" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpunit/php-file-iterator" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/theseer/tokenizer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpunit/php-code-coverage" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpunit/php-text-template" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpunit/phpunit" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpunit/php-token-stream" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpunit/php-timer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/dom-crawler" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/phpunit-bridge" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/browser-kit" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/myclabs/deep-copy" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/league/oauth2-google" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/string" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-intl-grapheme" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/form" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-intl-icu" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/vich/uploader-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/geoip2/geoip2" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/maxmind/web-service-common" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/maxmind-db/reader" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/gpslab/geoip2" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/embed/embed" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/beelab/tag-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sllh/iso-codes-validator" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/ronanguilloux/isocodes" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/giggsey/locale" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/giggsey/libphonenumber-for-php" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/meyfa/php-svg" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/overtrue/pinyin" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/lasserafn/php-initial-avatar-generator" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/lasserafn/php-string-script-language" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/lasserafn/php-initials" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/intervention/image" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/deprecations" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-php81" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/friendsofphp/proxy-manager-lts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpstan/phpdoc-parser" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/event-dispatcher" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/password-hasher" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/slack-notifier" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/notifier" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/amazon-mailer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/async-aws/core" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/async-aws/ses" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/messenger" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/redis-messenger" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/doctrine-messenger" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/amqp-messenger" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/zenstruck/assert" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/psr-http-message-bridge" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/defuse/php-encryption" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/league/event" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/league/oauth2-server" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/league/oauth2-server-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/2captcha/2captcha" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/league/uri" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/league/uri-interfaces" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/rate-limiter" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/matomo/device-detector" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/mustangostang/spyc" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpoffice/phpspreadsheet" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/simple-cache" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/markbaker/matrix" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/ezyang/htmlpurifier" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/maennchen/zipstream-php" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/markbaker/complex" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/yectep/phpspreadsheet-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/lcobucci/clock" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/proxy-manager-bridge" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/nelmio/cors-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/bentools/webpush-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/chillerlan/php-qrcode" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/chillerlan/php-settings-container" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/fgrosse/phpasn1" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/minishlink/web-push" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/spomky-labs/base64url" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/uid" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/web-token/jwt-core" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/web-token/jwt-key-mgmt" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/web-token/jwt-signature" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/web-token/jwt-signature-algorithm-ecdsa" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/web-token/jwt-util-ecc" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/aws/aws-crt-php" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/aws/aws-sdk-php" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/mtdowling/jmespath.php" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/google-mailer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/ml/iri" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/ml/json-ld" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/oscarotero/html-parser" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/knplabs/gaufrette" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/knplabs/knp-gaufrette-bundle" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
</module>