server {
     server_name ~.*;

     location / {
         root /var/www;

         try_files $uri /index.php$is_args$args;
     }

     location ~ ^/index\.php(/|$) {
         client_max_body_size 50m;

         fastcgi_pass php:9000;
          fastcgi_buffers 16 16k;
          fastcgi_buffer_size 32k;
          include fastcgi_params;
          fastcgi_param SCRIPT_FILENAME /var/www/public/index.php;
      }

      error_log /dev/stderr debug;
      access_log /dev/stdout;
 }
