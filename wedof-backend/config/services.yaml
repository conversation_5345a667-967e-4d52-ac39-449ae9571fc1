# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices/configuration.html#application-related-configuration
parameters:
  domain: '%env(DOMAIN)%'
  container.dumper.inline_factories: true
  locale: 'fr'

services:
  # default configuration for services in *this* file
  _defaults:
    autowire: true      # Automatically injects dependencies in your services.
    autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.

  # makes classes in src/ available to be used as services
  # this creates a service per class whose id is the fully-qualified class name
  App\:
    resource: '../src/*'
    exclude: '../src/{DependencyInjection,Entity,Migrations,Tests,Kernel.php}'

  # controllers are imported separately to make sure services can be injected
  # as action arguments even if you don't extend any base controller class
  App\Controller\:
    resource: '../src/Controller'
    tags: [ 'controller.service_arguments' ]


  App\Application\:
    resource: '../src/Application'
    public: true

  # you can also make public individual services
  App\Service\AzureAiDocumentService:
    public: true
    arguments:
      $projectDir: '%kernel.project_dir%'


  # you can also make public individual services
  App\Service\ApplicationService:
    public: true

  App\Service\WedofDirectoryNamer:
    public: true

  App\Service\CertificationService:
    public: true

  App\Service\CdcXMLService:
    public: true

  App\Service\CatalogXMLService:
    public: true

  App\Service\AttendeeExperienceService:
    public: true

  App\Service\CronTaskManagerService:
    public: true

  #Any XXXXApiService must be declared as public to be visible from the $container
  App\Service\DataProviders\InternalApiService:
    public: true
  App\Service\DataProviders\RegistrationFolderInternalApiService:
    public: true
  App\Service\DataProviders\CertificationInternalApiService:
    public: true

  App\Service\DataProviders\CpfApiService:
    public: true

  App\Service\DataProviders\CdcCertifiersApiService:
    public: true

  App\Service\ProposalService:
    public: true

  App\Service\DataProviders\KairosAifApiService:
    public: true

  App\Service\DataProviders\PoleEmploiApiService:
    public: true

  App\Service\DataProviders\FranceCompetencesApiService:
    public: true
    arguments:
      $projectDir: '%kernel.project_dir%'

  App\Application\Digiforma\Service\DigiformaApiService:
    public: true

  App\Service\DataProviders\AutomatorApiService:
    public: true

  App\Service\DataProviders\OpcoCfaApiService:
    public: true

  App\Service\DataProviders\OpcoCfa2iApiService:
    public: true

  App\Service\DataProviders\OpcoCfaAfdasApiService:
    public: true

  App\Service\DataProviders\OpcoCfaAktoApiService:
    public: true

  App\Service\DataProviders\OpcoCfaAtlasApiService:
    public: true

  App\Service\DataProviders\OpcoCfaConstructysApiService:
    public: true

  App\Service\DataProviders\OpcoCfaEpApiService:
    public: true

  App\Service\DataProviders\OpcoCfaMobilitesApiService:
    public: true

  App\Service\DataProviders\OpcoCfaOcapiatApiService:
    public: true

  App\Service\DataProviders\OpcoCfaOpcommerceApiService:
    public: true

  App\Service\DataProviders\OpcoCfaSanteApiService:
    public: true

  App\Service\DataProviders\OpcoCfaUniformationApiService:
    public: true

  App\Service\DataProviders\WorkingContractApiService:
    public: true

  App\Service\BrowserlessService:
    public: true
    arguments:
      $projectDir: '%kernel.project_dir%'

  App\Service\AttendeeService:
    public: true
    arguments:
      $loginLinkHandler: '@security.authenticator.login_link_handler.api'

  App\Service\RegistrationFolderReasonService:
    public: true

  App\Service\InvoiceService:
    public: true
    arguments:
      $projectDir: '%kernel.project_dir%'

  App\Service\WebhookService:
    public: true

  App\Service\StripeService:
    public: true

  App\Service\UserService:
    public: true
    arguments:
      $loginLinkHandler: '@security.authenticator.login_link_handler.api'

  App\Service\TrainingActionService:
    public: true

  App\Service\SessionService:
    public: true

  App\Service\TrainingService:
    public: true

  App\Service\OrganismService:
    public: true

  App\Service\EvaluationService:
    public: true

  App\Service\RegistrationFolderService:
    public: true

  App\Service\CertificationFolderService:
    public: true

  App\Service\CertificationFolderSurveyService:
    public: true

  App\Service\RegistrationFolderFileService:
    public: true

  App\Service\CertificationFolderFileService:
    public: true

  App\Service\CertificationPartnerFileService:
    public: true

  App\Service\CertificationPartnerService:
    public: true

  App\Service\CertificationPartnerAuditService:
    public: true

  App\Service\CertificationPartnerAuditTemplateService:
    public: true

  App\Application\MessageTemplates\Service\MessageTemplateService:
    public: true

  App\Service\SubscriptionService:
    public: true

  App\Service\ConnectionService:
    public: true

  App\Service\SkillService:
    public: true

  App\Service\CityService:
    public: true

  App\Service\WorkingContractService:
    public: true

  App\Library\utils\VariableParser:
    public: true

  App\Service\EntityEventListener:
    tags:
      - { name: doctrine.event_listener, event: postLoad, method: postLoad }

  App\Service\TaggableEventListener:
    tags:
      - { name: doctrine.event_listener, event: postLoad, method: postLoad }

  App\Event\KernelControllerEventListener:
    tags:
      - { name: kernel.event_listener, event: kernel.controller, method: onKernelController }

  api_jwt.event.authentication_failure_listener:
    class: App\Event\AuthenticationFailureListener
    arguments: [ '@jms_serializer' ]
    tags:
      - { name: kernel.event_listener, event: lexik_jwt_authentication.on_authentication_failure, method: onAuthenticationFailureResponse }

  App\Service\SerializerService:
    tags:
      - jms.expression.function_provider

  serializer_listener.certification:
    class: App\Event\Certification\CertificationSerializeSubscriber
    tags:
      - { name: jms_serializer.event_subscriber }

  serializer_listener.session:
    class: App\Event\Session\SessionSerializeSubscriber
    tags:
      - { name: jms_serializer.event_subscriber }

  serializer_listener.subscription:
    class: App\Event\Subscription\SubscriptionSerializeSubscriber
    tags:
      - { name: jms_serializer.event_subscriber }

  serializer_listener.proposal:
    class: App\Event\Proposal\ProposalSerializeSubscriber
    tags:
      - { name: jms_serializer.event_subscriber }

  serializer_listener.user:
    class: App\Event\User\UserSerializeSubscriber
    tags:
      - { name: jms_serializer.event_subscriber }

  #Should be removed when Transport (Notifier) will bring ringover by default
  App\Library\Notifier\RingoverTransportFactory:
    tags:
      - { name: 'texter.transport_factory' }

  App\Library\Notifier\WedofNotifierTransport:
    arguments:
      - !tagged_iterator 'texter.transport_factory'

  App\Serializer\ConstraintViolationHandler:
    tags:
      - { name: jms_serializer.handler, type: Symfony\Component\Validator\ConstraintViolationList, direction: serialization, format: json, method: serializeListToJson }

  App\Security\Id360Authenticator:
    arguments:
      $checkPath: '/app/identities/id360/auth'
      $userProvider: '@security.user.provider.concrete.app_attendees_provider'

  App\Security\SecurityExtended:
    arguments:
      $firewallMap: '@security.firewall.map'

  App\Service\UserSubscriptionManager:
    class: App\Service\UserSubscriptionManager
    arguments:
      - '@doctrine'
    tags:
      - { name: bentools_webpush.subscription_manager, user_class: 'App\Entity\User' }

  acme.aws_s3.client:
    class: Aws\S3\S3Client
    factory: [ Aws\S3\S3Client, 'factory' ]
    arguments:
      - version: 'latest'
        region: 'us-east-1'
        endpoint: 'https://storage.wedof.fr/'
        use_path_style_endpoint: true
        credentials:
          key: 'mLMJdHcctHwqRicnsKi0'
          secret: '87wbbZyxcVTY7H7IXccM7k22i0iZd0zzbuwIs6ZW'
