api_signin:
  path: /api/auth

api_refresh_token:
  path: /api/refresh

oauth2_authorize:
  path: /oauth2/authorize
  controller: [ 'league.oauth2_server.controller.authorization', 'indexAction' ]

oauth2_token:
  path: /oauth2/token
  controller: [ 'league.oauth2_server.controller.token', 'indexAction' ]
  methods: [ POST ]

api_login_check:
  path: /api/auth

bentools_webpush:
  resource: '@WebPushBundle/Resources/config/routing.xml'
  prefix: /app/webpush