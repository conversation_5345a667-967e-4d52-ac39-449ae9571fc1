nelmio_cors:
  defaults:
    allow_origin: [ '*' ]
    allow_methods: [ 'GET', 'OPTIONS', 'POST', 'PUT', 'PATCH', 'DELETE' ]
    allow_headers: [ 'Content-Type', 'Authorization', 'Authorization-JWT' ]
    expose_headers: [ 'Link' ]
    max_age: 3600
  paths:
    '^/':
    '^/api/':
      allow_origin: [ '*' ]
      allow_headers: [ 'X-API-KEY' ]
      allow_methods: [ 'POST', 'PUT', 'GET', 'DELETE' ]
      max_age: 3600