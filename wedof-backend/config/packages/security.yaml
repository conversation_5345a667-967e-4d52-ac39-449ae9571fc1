security:
  enable_authenticator_manager: true
  password_hashers:
    App\Entity\User:
      algorithm: auto

  providers:
    app_users_provider:
      entity:
        class: App\Entity\User
        property: email

    app_attendees_provider:
      entity:
        class: App\Entity\Attendee
        property: id

    app_all_users:
      chain:
        providers: [ 'app_users_provider', 'app_attendees_provider' ]



  firewalls:
    shortener:
      host: ^s\.wedof\.fr$
      security: false

    shortener_dev:
      host: ^s\.localhost$
      security: false

    dev:
      pattern: ^/(_(profiler|wdt)|css|images|js)/
      security: false

    funnel:
      pattern: ^/f/p
      security: false

    api_doc:
      pattern: ^/api/doc
      security: false

    app_public:
      pattern: ^/app/public
      security: false

    api_token:
      pattern: ^/api/token$
      security: false

    oauth2_token:
      pattern: ^/oauth2/token$
      security: false

    refresh:
      pattern: ^/api/refresh
      stateless: true
      refresh_jwt:
        provider: app_all_users
        check_path: /api/refresh

    api:
      provider: app_users_provider #in order to avoid attendee on api / oauth2 only User entity
      pattern: ^/(api|app|funnel/api)
      entry_point: jwt
      security: true
      stateless: true
      switch_user: true
      oauth2: true
      json_login:
        check_path: /api/auth
        username_path: email
        success_handler: lexik_jwt_authentication.handler.authentication_success
        failure_handler: lexik_jwt_authentication.handler.authentication_failure
      jwt:
        provider: app_all_users
      custom_authenticators:
        - App\Security\Id360Authenticator #use its own userProvider => app_attendees_provider (from services.yaml)
        - App\Security\ApiTokenAuthenticator
      login_link:
        check_route: login_check
        provider: app_all_users
        signature_properties: [ id, email, lastLogin ]
        # lifetime in seconds
        lifetime: 600
        success_handler: App\Security\LoginLinkSuccessHandler

    main:
      entry_point: form_login
      form_login:
        provider: app_users_provider
        login_path: login
      jwt:
        provider: app_all_users

  access_control:
    - { path: '^/funnel/apprenant/proposition', roles: PUBLIC_ACCESS } #remove asap: custom SSR and meta tags...
    - { path: '^/api/auth',                     roles: PUBLIC_ACCESS }
    - { path: '^/api/refresh',                  roles: PUBLIC_ACCESS }
    - { path: '^/app',                          roles: IS_AUTHENTICATED_FULLY }
    - { path: '^/api',                          roles: IS_AUTHENTICATED_FULLY }
    - { path: '^/funnel/api',                   roles: IS_AUTHENTICATED_FULLY }
    - { path: '^/cron',                         roles: PUBLIC_ACCESS, ips: [ 127.0.0.1, ::1, *************/24, *************/24, **************/24, **************/24, *************/24, **************/24 ] }
    - { path: '^/cron',                         roles: PUBLIC_ACCESS, host: localhost }
    #- { path: '^/auto',                         roles: PUBLIC_ACCESS, ips: [ 127.0.0.1, ::1, **************/24, *************/24 ] }
    #- { path: '^/auto',                         roles: PUBLIC_ACCESS, host: localhost }
    - { path: '^/slack',                        roles: PUBLIC_ACCESS }
    - { path: '^/oauth2/token',                 roles: PUBLIC_ACCESS }
    - { path: '^/oauth2/authorize',             roles: IS_AUTHENTICATED_REMEMBERED }
    - { path: '^/oauth2/client',                roles: PUBLIC_ACCESS }
    - { path: '^/preview',                      roles: PUBLIC_ACCESS }
    - { path: '^/certifications/check',         roles: PUBLIC_ACCESS }
    - { path: '^/verify/email',                 roles: PUBLIC_ACCESS }
    - { path: '^/login',                        roles: PUBLIC_ACCESS }
    - { path: '^/',                             roles: IS_AUTHENTICATED_FULLY }

  access_decision_manager:
    strategy: unanimous
