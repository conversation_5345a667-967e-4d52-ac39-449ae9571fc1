framework:
  messenger:
    buses:
      command_bus:
        middleware:
          - doctrine_ping_connection
          - doctrine_close_connection

    # Uncomment this (and the failed transport below) to send failed messages to this transport for later handling.
    failure_transport: failed
    reset_on_message: true
    transports:
      # https://symfony.com/doc/current/messenger.html#transport-configuration

      async:
        dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
        options:
          exchange:
            name: '%env(CONTEXT_PREFIX)%%env(APP_ENV)%_async'
          queues:
            '%env(CONTEXT_PREFIX)%%env(APP_ENV)%_messages': ~

      async_batch:
        dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
        options:
          exchange:
            name: '%env(CONTEXT_PREFIX)%%env(APP_ENV)%_async_batch'
          queues:
            '%env(CONTEXT_PREFIX)%%env(APP_ENV)%_messages_batch': ~

      async_captcha:
        dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
        options:
          exchange:
            name: '%env(CONTEXT_PREFIX)%%env(APP_ENV)%_async_captcha'
          queues:
            '%env(CONTEXT_PREFIX)%%env(APP_ENV)%_messages_captcha': ~

      async_priority_low:
        dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
        options:
          exchange:
            name: '%env(CONTEXT_PREFIX)%%env(APP_ENV)%_async_priority_low'
          queues:
            '%env(CONTEXT_PREFIX)%%env(APP_ENV)%_messages_priority_low': ~

      async_priority_low_catalog:
        dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
        options:
          exchange:
            name: '%env(CONTEXT_PREFIX)%%env(APP_ENV)%_async_priority_low_catalog'
          queues:
            '%env(CONTEXT_PREFIX)%%env(APP_ENV)%_messages_priority_low_catalog': ~

      async_priority_low_sync:
        dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
        options:
          exchange:
            name: '%env(CONTEXT_PREFIX)%%env(APP_ENV)%_async_priority_low_sync'
          queues:
            '%env(CONTEXT_PREFIX)%%env(APP_ENV)%_messages_priority_low_sync':
              arguments:
                x-message-ttl: 8000 #new message emitted every Xsec so no need to keep old one


      async_priority_high_sync:
        dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
        options:
          exchange:
            name: '%env(CONTEXT_PREFIX)%%env(APP_ENV)%_async_priority_high_sync'
          queues:
            '%env(CONTEXT_PREFIX)%%env(APP_ENV)%_messages_priority_high_sync':
              arguments:
                x-message-ttl: 10000 #new message emitted every 10sec so no need to keep old one

      async_priority_high_webhook:
        dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
        failure_transport: failed_high_priority
        options:
          exchange:
            name: '%env(CONTEXT_PREFIX)%%env(APP_ENV)%_async_priority_high_webhook'
          queues:
            '%env(CONTEXT_PREFIX)%%env(APP_ENV)%_messages_priority_high_webhook': ~


      failed:
        dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
        options:
          exchange:
            name: '%env(CONTEXT_PREFIX)%%env(APP_ENV)%_failed'
          queues:
            '%env(CONTEXT_PREFIX)%%env(APP_ENV)%_messages_failed': ~

      failed_high_priority:
        dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
        options:
          exchange:
            name: '%env(CONTEXT_PREFIX)%%env(APP_ENV)%_failed_high_priority'
          queues:
            '%env(CONTEXT_PREFIX)%%env(APP_ENV)%_messages_failed_high_priority': ~

    routing:
      # Route your messages to the transports
      'App\Message\SendWebhook': async_priority_high_webhook
      'App\Message\CreateCertification': async_priority_low
      'App\Message\SynchronizeCertifiers': async_priority_low
      'App\Message\SynchronizePartners': async_priority_low
      'App\Message\SynchronizeEvaluations': async_priority_low
      'App\Message\SynchronizeCertification': async_priority_low
      'App\Message\CreateCertificationFolder': async_priority_low
      'App\Message\CreateCertificationFolders': async_priority_low
      'App\Message\SynchronizeCertificationPartners': async_priority_low
      'App\Message\InTrainingRegistrationFolder': async_batch
      'App\Message\TerminateRegistrationFolder': async_batch
      'App\Message\ServiceDoneRegistrationFolder': async_batch
      'App\Message\BillRegistrationFolder': async_batch
      'App\Message\GenerateCdcXML': async
      'App\Message\ReadReceiptXmlForCdc': async
      'App\Message\SynchronizeOrganisms': async
      'App\Message\MonitorRegistrationFolders': async
      'App\Message\PropagateSkillSetsFromTraining': async

      #Applications
      'App\Application\Digiforma\Message\SendRegistrationFolderDigiforma': async_priority_low
      'App\Application\Digiforma\Message\PullCompletionRateRegistrationFolderDigiforma': async_priority_low

      #'App\Message\RefreshConnection': async_captcha
      #'App\Message\SynchronizeFranceCompetencesFiles' <= must be done by main web so not async

when@prod:
  framework:
    messenger:
      routing:
        'App\Message\SynchronizeRegistrationFolder': async_batch
        'App\Message\MessageToSend': async
        'App\Message\SynchronizeNewRegistrationFolders': async_priority_high_sync
        'App\Message\SynchronizeStateRegistrationFolders': async_priority_low_sync
        'App\Message\OneBatchRegistrationFolders': async_priority_low
        'App\Message\StartBatchesRegistrationFolders': async_priority_low
        'App\Message\SynchronizePayments': async
        'App\Message\SynchronizeCatalog': async_priority_low_catalog
        'App\Message\SynchronizeCatalogForAudit': async_priority_low_catalog
        'App\Message\ComputeCertificationPartnerAudit': async
        'App\Message\SynchronizeTrainingActionAndSessions': async_priority_low_catalog
        'App\Message\SynchronizeWorkingContracts': async
        'App\Message\GenerateCpfCatalogXML': async

        #applications
        'App\Application\Onlineformapro\Message\PullCompletionRateRegistrationFoldersOnlineformapro': async_priority_low
