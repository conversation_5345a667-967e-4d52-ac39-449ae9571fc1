# Read the documentation: https://symfony.com/doc/master/bundles/FOSRestBundle/index.html
fos_rest:
  #  body_converter:
  #    enabled: true
  #  view:
  #    formats: { json: true, xml: false, rss: false }
  #  serializer:
  #    serialize_null: true
  #    param_fetcher_listener:  true
  #    allowed_methods_listener:  true
  #    routing_loader: true
  view:
    view_response_listener: true
    mime_types:
      json: [ 'application/json; charset=UTF-8', 'application/json' ]
  format_listener:
    rules:
      - { path: ^/funnel/apprenant/proposition, prefer_extension: true, fallback_format: html, priorities: [ html ] }
      - { path: ^/app/public/certificateHolder, prefer_extension: true, fallback_format: html, priorities: [ html ] }
      - { path: ^/app/public/message/html, prefer_extension: true, fallback_format: html, priorities: [ html ] }
      - { path: ^/app/public/organisms/verifyIdentitySuccess, prefer_extension: true, fallback_format: html, priorities: [ html ] }
      - { path: ^/app/public/organisms/verifyIdentityFailure, prefer_extension: true, fallback_format: html, priorities: [ html ] }
      - { path: ^/f/p, stop: true }
      - { path: ^/funnel, prefer_extension: true, fallback_format: json, priorities: [ json ] }
      - { path: ^/app, prefer_extension: true, fallback_format: json, priorities: [ json ] }
      - { path: ^/api, prefer_extension: true, fallback_format: json, priorities: [ json ] }
      - { path: ^/, prefer_extension: false, fallback_format: html, priorities: [ html ] }

  exception:
    enabled: true
    serializer_error_renderer: true
    map_exception_codes: true
    flatten_exception_format: 'rfc7807'
    codes:
      'App\Exception\WedofCpfBackendException': 500 # required only if not HttpExceptionInterface, otherwise the http status is already set
      'App\Exception\WedofConflictHttpException': 409 # required only if not HttpExceptionInterface, otherwise the http status is already set
    messages:
      'App\Exception\WedofSubscriptionException': true
      'App\Exception\ServerException': true
      'App\Exception\WedofBadRequestHttpException': true
      'App\Exception\WedofAccessDeniedHttpException': true
      'App\Exception\WedofNotFoundHttpException': true
      'App\Exception\WedofConflictHttpException': true

  body_converter:
    enabled: true
    validate: true
    validation_errors_argument: violations

  serializer:
    groups: [ 'Default' ]
    serialize_null: true
  # useful to catch json requests and use them with form requests
  # https://fosrestbundle.readthedocs.io/en/3.x/body_listener.html#decoders
  #  body_listener:
  #    decoders:
  #      json: fos_rest.decoder.jsontoform

  param_fetcher_listener: true
