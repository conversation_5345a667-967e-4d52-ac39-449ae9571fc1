league_oauth2_server:
  authorization_server:
    private_key: '%env(PRIVATE_KEY_PATH)%'                     # Change this
    private_key_passphrase: '%env(PRIVATE_KEY_PASSPHRASE)%'    # Passphrase of the private key, if any

    encryption_key: '%env(string:OAUTH2_ENCRYPTION_KEY)%'      # (Optional) Change this

    access_token_ttl: P3M

    refresh_token_ttl: P1Y

    auth_code_ttl: PT10M

    # Whether to enable the refresh token grant
    enable_refresh_token_grant: true

    # Whether to enable the authorization code grant
    enable_auth_code_grant: true

  resource_server:
    public_key: '%env(PUBLIC_KEY_PATH)%'                     # Change this

  persistence:
    doctrine:
      entity_manager: default

  scopes:
    available:
      - 'certification:read'
      - 'certification:write'
      - 'registrationfolder:read'
      - 'registrationfolder:write'
      - 'webhook:read'
      - 'webhook:write'
      - 'session:read'
      - 'session:write'
      - 'user:read'
      - 'user:write'
      - 'organism:read'
      - 'organism:write'
      - 'training:read'
      - 'training:write'
      - 'trainingaction:read'
      - 'trainingaction:write'
      - 'payment:read'
      - 'certifieraccess:read'
      - 'certifieraccess:write'
      - 'evaluation:read'
      - 'evaluation:write'
      - 'certificationfolder:read'
      - 'certificationfolder:write'
      - 'attendee:read'
      - 'attendee:write'
      - 'apitoken:read'
      - 'apitoken:write'
      - 'subscription:read'
      - 'subscription:write'
      - 'messagetemplate:read'
      - 'messagetemplate:write'
      - 'message:read'
      - 'message:write'
      - 'activity:write'
    default:
      - 'user:read'
