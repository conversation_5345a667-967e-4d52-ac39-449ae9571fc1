nelmio_api_doc:
  documentation:
    servers:
      - url: /
        description: API over HTTPS
    info:
      title: API Wedof
      description: Cette API permet d'agir sur les dossiers d'enregistrement de la plateforme CPF
      version: 0.1.0
    components:
      schemas:
        Attendee:
          type: object
          properties:
            id:
              type: number
            lastName:
              type: string
            firstName:
              type: string
            firstName2:
              type: string
            firstName3:
              type: string
            email:
              type: string
            phoneNumber:
              type: string
            phoneFixed:
              type: string
            address:
              type: object
              properties:
                id:
                  type: string
                city:
                  type: string
                line4:
                  type: string
                number:
                  type: string
                country:
                  type: string
                postBox:
                  type: string
                zipCode:
                  type: string
                roadName:
                  type: string
                roadType:
                  type: string
                idAddress:
                  type: string
                residence:
                  type: string
                countryCode:
                  type: string
                trainingSite:
                  type: string
                corporateName:
                  type: string
                roadTypeLabel:
                  type: string
                informationSite:
                  type: string
                repetitionIndex:
                  type: string
                subscriptionSite:
                  type: string
                additionalAddress:
                  type: string
                repetitionIndexLabel:
                  type: string
                reducedMobilityAccessCompliant:
                  type: string
                reducedMobilityAccessModalities:
                  type: string
            fullAddress:
              type: string
              description: Adresse complète (numéro dans la voie + complément éventuel + type de voie + nom de voie + code postal + ville)
            dateOfBirth:
              type: string
              format: date-time
            nameCityOfBirth:
              type: string
              description: Ville de naissance (obligatoire si né(e) en France)
            codeCityOfBirth:
              type: string
              description: Code Insee de la ville de naissance (obligatoire si né(e) en France)
            codeCountryOfBirth:
              type: number
              description: Code COG du pays de naissance (obligatoire si né(e) à l'étranger)
            nameCountryOfBirth:
              type: string
              description: Pays de naissance (obligatoire si né(e) à l'étranger)
            gender:
              type: string
              enum:
                - male
                - female
            birthName:
              type: string
            emailValidated:
              type: boolean
              description: Indique que l'email de l'apprenant / candidat a été validé par l'apprenant / candidat
            phoneNumberValidated:
              type: boolean
              description: Indique que le numéro de téléphone de l'apprenant / candidat a été validé par l'apprenant / candidat
        AttendeeCreateBody:
          type: object
          required:
            - lastName
            - firstName
            - gender
            - email
          properties:
            lastName:
              type: string
              description: Nom de famille de l'apprenant / candidat - obligatoire
            firstName:
              type: string
              description: Prénom de l'apprenant / candidat - obligatoire
            firstName2:
              type: string
              description: 2eme prénom de l'apprenant / candidat - facultatif
            firstName3:
              type: string
              description: 3eme prénom de l'apprenant / candidat - facultatif
            email:
              type: string
              description: Email de l'apprenant / candidat - obligatoire
            phoneNumber:
              type: string
              description: Téléphone portable de l'apprenant / candidat - obligatoire si phoneFixed n'est pas renseigné
            phoneFixed:
              type: string
              description: Téléphone fixe de l'apprenant / candidat - obligatoire si phoneNumber n'est pas renseigné
            dateOfBirth:
              type: string
              format: date-time
              description: Date de naissance de l'apprenant / candidat - facultatif
            nameCityOfBirth:
              type: string
              description: Ville de naissance de l'apprenant / candidat (obligatoire si né(e) en France)
            codeCityOfBirth:
              type: string
              description: Code Insee de la ville de naissance de l'apprenant / candidat (obligatoire si né(e) en France)
            codeCountryOfBirth:
              type: number
              description: Code COG du pays de naissance de l'apprenant / candidat (obligatoire si né(e) à l'étranger)
            nameCountryOfBirth:
              type: string
              description: Pays de naissance de l'apprenant / candidat (obligatoire si né(e) à l'étranger)
            gender:
              type: string
              enum:
                - male
                - female
              description: Sexe de l'apprenant / candidat - obligatoire
            birthName:
              type: string
              description: Nom de naissance de l'apprenant / candidat - facultatif
            address:
              type: object
              description: Adresse de l'apprenant / candidat - facultatif
              properties:
                number:
                  type: string
                  description: Numéro de rue
                  example: 8
                repetitionIndexLabel:
                  type: string
                  enum:
                    - bis
                    - quinquies
                    - quater
                    - ter
                roadTypeLabel:
                  type: string
                  description: Type de rue
                  example: Rue
                roadName:
                  type: string
                  description: Nom de rue
                  example: de metz
                zipCode:
                  type: string
                  description: Code postal
                  example: 31500
                city:
                  type: string
                  description: Ville
                  example: Toulouse
        AttendeeUpdateBody:
          type: object
          properties:
            lastName:
              type: string
              description: Nom de famille de l'apprenant
            firstName:
              type: string
              description: Prénom de l'apprenant
            firstName2:
              type: string
              description: Deuxième prénom de l'apprenant
            firstName3:
              type: string
              description: Troisième prénom de l'apprenant
            dateOfBirth:
              type: string
              format: date-time
              description: Date de naissance de l'apprenant
            nameCityOfBirth:
              type: string
              description: Ville de naissance (obligatoire si né(e) en France)
            codeCityOfBirth:
              type: string
              description: Code Insee de la ville de naissance (obligatoire si né(e) en France)
            codeCountryOfBirth:
              type: number
              description: Code COG du pays de naissance (obligatoire si né(e) à l'étranger)
            nameCountryOfBirth:
              type: string
              description: Pays de naissance (obligatoire si né(e) à l'étranger)
            birthName:
              type: string
            gender:
              type: string
              enum:
                - male
                - female
            address:
              type: object
              properties:
                number:
                  type: string
                  description: Numéro de rue
                  example: 8
                repetitionIndexLabel:
                  type: string
                  enum:
                    - bis
                    - quinquies
                    - quater
                    - ter
                roadTypeLabel:
                  type: string
                  description: Type de rue
                  example: Rue
                roadName:
                  type: string
                  description: Nom de rue
                  example: de metz
                zipCode:
                  type: string
                  description: Code postal
                  example: 31500
                city:
                  type: string
                  description: Ville
                  example: Toulouse
        FileTypeCreate:
          type: object
          properties:
            name:
              type: string
              description: Nom du fichier
              example: Dossier d'admission du candidat
            accept:
              type: string
              description: Format(s) du fichier attendu
              example: link, .pdf, .doc, .docx, .xls, .xlsx, .ppt, .pptx, .txt, .png, .jpg, .zip, .rar, .7z, .ace, .tar.gz, .*
            toState:
              type: string
              description: Indique l'état pour lequel le fichier est requis (optionnel) ou sera généré (obligatoire)
              example: 'success'
            description:
              type: string
              description: Description du fichier attendu
            allowVisibilityPartner:
              type: boolean
              description: Indique si le fichier est visible pour le partenaire (uniquement pour des dossiers de certification)
            allowUploadPartner:
              type: boolean
              description: Indique si le fichier peut être transmis par le partenaire (Uniquement pour des dossiers de certification)
            allowVisibilityAttendee:
              type: boolean
              description: Indique si le fichier est disponible depuis l'Espace Apprenant / Candidat (uniquement pour des dossiers de formation / certification)
            allowUploadAttendee:
              type: boolean
              description: Indique si le fichier peut être transmis par l'apprenant depuis l'Espace Apprenant / Candidat (uniquement pour des dossiers de formation / certification)
            qualiopiIndicators:
              type: array
              description: Tableau des indicateurs Qualiopi
              items:
                type: integer
            generated:
              type: boolean
              description: Indique si le fichier est généré automatiquement
            enabled:
              type: boolean
              description: Indique si la génération automatique de ce type de fichier est activé ou désactivé
            templateFile:
              type: string
              format: binary
              description: Modèle du fichier
        ActivityCreateBody:
          type: object
          properties:
            type:
              type: string
              enum:
                - create
                - update
                - updateState
                - phone
                - email
                - meeting
                - chat
                - sms
                - examination
                - training
                - cdc
                - remark
                - file
            eventTime:
              type: string
              format: date-time
              description: Date de début de l'activité
            eventEndTime:
              type: string
              format: date-time
              description: Date de fin de l'activité
            title:
              type: string
            description:
              type: string
            origin:
              type: string
              description: Source de donnée de l'activité
            link:
              type: string
              description: Lien (url) vers l'activité
            userEmail:
              type: string
              description: email de l'utilisateur qui créé une activité si applicable
            qualiopiIndicators:
              type: array
              description: Tableau des indicateurs Qualiopi; ne concerne que les dossiers de formation et de certification et les propositions
              items:
                type: integer
            dueDate:
              type: string
              format: date-time
              description: Date à laquelle l'activité / tâche doit être finie
            done:
              type: boolean
              description: Indique si la tâche est terminée, par défaut true
        Activity:
          type: object
          properties:
            id:
              type: number
            type:
              type: string
              enum:
                #- create
                - update
                #- updateState
                - phone
                - email
                - meeting
                - chat
                - sms
                - examination
                - training
                - cdc
                - remark
                - file
            eventTime:
              type: string
              format: date-time
              description: Date de début de l'activité
            eventEndTime:
              type: string
              format: date-time
              description: Date de fin de l'activité
            qualiopiIndicators:
              type: array
              description: Tableau des indicateurs Qualiopi; ne concerne que les dossiers de formation et de certification et les propositions
              items:
                type: integer
            title:
              type: string
            description:
              type: string
            origin:
              type: string
              description: Source de donnée de l'activité
            link:
              type: string
              description: Lien (url) vers l'activité
            field:
              type: string
              description: Permet d'indiquer le changement d'une donnée
              example: Changement de prix sur le field price du dossier de formation
            previousValue:
              type: string
              example: 1500
            newValue:
              type: string
              example: 1250
            entityClass:
              type: string
              description: L'entité modifiée
              example: RegistrationFolder ou CertificationFolder ou CertificationPartner ou Proposal
            entityId:
              type: string
              description: L'externalId du dossier de formation ou l'id du dossier de certification ou l'id d'un partenariat ou le code d'une proposition
            dueDate:
              type: string
              format: date-time
              description: Date à laquelle l'activité / tâche doit être finie
            done:
              type: boolean
              description: Indique si la tâche est terminée, par défaut true
        ActivityUpdateBody:
          type: object
          properties:
            type:
              type: string
              enum:
                - create
                - update
                - updateState
                - phone
                - email
                - meeting
                - chat
                - sms
                - examination
                - training
                - cdc
                - remark
                - file
            eventTime:
              type: string
              format: date-time
              description: Date de début de l'activité
            eventEndTime:
              type: string
              format: date-time
              description: Date de fin de l'activité
            title:
              type: string
            description:
              type: string
            origin:
              type: string
              description: Source de donnée de l'activité
            link:
              type: string
              description: lien (url) vers l'activité
            userEmail:
              type: string
              description: email de l'utilisateur qui créé une activité si applicable
            qualiopiIndicators:
              type: array
              description: Tableau des indicateurs Qualiopi si le champ 'qualiopi' est valorisé à true ; ne concerne que les dossiers de formation et de certification et les propositions
              items:
                type: integer
            dueDate:
              type: string
              format: date-time
              description: Date à laquelle l'activité / tâche doit être finie
            done:
              type: boolean
              description: Indique si la tâche est terminée, par défaut true
        ActivityMoveBody:
          type: object
          properties:
            entityClass:
              type: string
              description: Type de l'entity
              example: RegistrationFolder ou CertificationFolder
            entityId:
              type: string
              description: L'entityId correspond à l'externalId du dossier de formation ou de l'externalId du dossier de certification.
        CertificationFolderSurvey:
          type: object
          properties:
            id:
              type: number
            initialExperience:
              type: object
              properties:
                id:
                  type: number
                qualification:
                  type: number
                certificationName:
                  type: string
                job:
                  type: string
                companyName:
                  type: string
                salaryYearly:
                  type: number
                situation:
                  type: string
                contractType:
                  type: string
                executiveStatus:
                  type: boolean
                startDate:
                  type: string
                  format: date-time
                endDate:
                  type: string
                  format: date-time
                createdOn:
                  type: string
                  format: date-time
                updatedOn:
                  type: string
                  format: date-time
            initialExperienceAnsweredDate:
              type: string
              format: date-time
            sixMonthExperience:
              type: object
              properties:
                id:
                  type: number
                qualification:
                  type: number
                certificationName:
                  type: string
                job:
                  type: string
                companyName:
                  type: string
                salaryYearly:
                  type: number
                situation:
                  type: string
                contractType:
                  type: string
                executiveStatus:
                  type: boolean
                startDate:
                  type: string
                  format: date-time
                endDate:
                  type: string
                  format: date-time
                createdOn:
                  type: string
                  format: date-time
                updatedOn:
                  type: string
                  format: date-time
            sixMonthExperienceAnsweredDate:
              type: string
              format: date-time
            sixMonthExperienceStartDate:
              type: string
              format: date-time
            longTermExperience:
              type: object
              properties:
                id:
                  type: number
                qualification:
                  type: number
                certificationName:
                  type: string
                job:
                  type: string
                companyName:
                  type: string
                salaryYearly:
                  type: number
                situation:
                  type: string
                contractType:
                  type: string
                executiveStatus:
                  type: boolean
                startDate:
                  type: string
                  format: date-time
                endDate:
                  type: string
                  format: date-time
                createdOn:
                  type: string
                  format: date-time
                updatedOn:
                  type: string
                  format: date-time
            longTermExperienceAnsweredDate:
              type: string
              format: date-time
            longTermExperienceStartDate:
              type: string
              format: date-time
            state:
              type: string
              enum:
                - created
                - beforeCertificationSuccess
                - afterSixMonthsCertificationSuccess
                - finished
              description: created = Créée ; beforeCertificationSuccess = Commencée ; afterSixMonthsCertificationSuccess = En cours ; finished = Terminée
        RegistrationFolder:
          type: object
          properties:
            createdOn:
              type: string
              format: date-time
            updatedOn:
              type: string
              format: date-time
            lastUpdate:
              type: string
              format: date-time
            attendee:
              type: object
              properties:
                id:
                  type: number
                lastName:
                  type: string
                firstName:
                  type: string
                firstName2:
                  type: string
                firstName3:
                  type: string
                phoneNumber:
                  type: string
                phoneFixed:
                  type: string
                email:
                  type: string
                dateOfBirth:
                  type: string
                  format: date-time
                nameCityOfBirth:
                  type: string
                codeCountryOfBirth:
                  type: number
                nameCountryOfBirth:
                  type: string
                codeCityOfBirth:
                  type: string
                gender:
                  type: string
                  enum:
                    - male
                    - female
                birthName:
                  type: string
                degree:
                  type: string
                degreeTitle:
                  type: string
                address:
                  type: object
                  properties:
                    id:
                      type: string
                    city:
                      type: string
                    line4:
                      type: string
                    number:
                      type: string
                    country:
                      type: string
                    postBox:
                      type: string
                    zipCode:
                      type: string
                    roadName:
                      type: string
                    roadType:
                      type: string
                    idAddress:
                      type: string
                    residence:
                      type: string
                    countryCode:
                      type: string
                    trainingSite:
                      type: string
                    corporateName:
                      type: string
                    roadTypeLabel:
                      type: string
                    informationSite:
                      type: string
                    repetitionIndex:
                      type: string
                    subscriptionSite:
                      type: string
                    additionalAddress:
                      type: string
                    repetitionIndexLabel:
                      type: string
                    reducedMobilityAccessCompliant:
                      type: string
                    reducedMobilityAccessModalities:
                      type: string
                fullAddress:
                  type: string
                  description: Adresse complète (numéro dans la voie + complément éventuel + type de voie + nom de voie + code postal + ville)
            state:
              type: string
              enum:
                - notProcessed
                - validated
                - waitingAcceptation
                - accepted
                - inTraining
                - terminated
                - serviceDoneDeclared
                - serviceDoneValidated
                - canceledByAttendee
                - canceledByAttendeeNotRealized
                - canceledByOrganism
                - refusedByAttendee
                - refusedByOrganism
                - rejectedWithoutTitulaireSuite
                - rejectedWithoutCdcSuite
                - rejectedWithoutOfSuite
                - rejected
              description: notProcessed = À traiter ; validated = Validé ; waitingAcceptation = Validé (En cours d'instruction Pôle emploi) ; accepted = Accepté ; inTraining = En formation ; terminated = Sortie de formation ; serviceDoneDeclared = Service fait déclaré ; serviceDoneValidated = Service fait validé ; canceledByAttendee = Annulation titulaire ; canceledByAttendeeNotRealized = Annulation titulaire (non réalisé) ; canceledByOrganism = Annulé (par l'organisme) ; refusedByAttendee = Refus titulaire ; refusedByOrganism = Refusé (par l'organisme) ; rejectedWithoutTitulaireSuite = Annulé sans suite ; rejectedWithoutCdcSuite = Annulé sans suite ; rejectedWithoutOfSuite = Annulé sans suite ; rejected = Annulé
            billingState:
              type: string
              enum:
                - notBillable
                - depositWait
                - depositPaid
                - toBill
                - billed
                - paid
              description: notBillable = Non facturable ; depositWait = Acompte en attente ; depositPaid = Acompte versé ; toBill = À Facturer ; billed = Facturé ; paid = Payé
            controlState:
              type: string
              enum:
                - inControl
                - notInControl
                - released
              description: inControl = En cours de contrôle ; notInControl = Aucun contrôle en cours ; released = contrôle terminé
            externalId:
              type: string
              description: Identifiant "métier" du dossier de formation dans Wedof (potentiellement préfixé selon le type de financement, par ex. AIF-XXXX)
            dataProviderId:
              type: string
              description: Identifiant du dossier de formation dans le référentiel externe du financeur (si disponible)
            externalLink:
              type: string
              description: Lien vers le dossier de formation dans le référentiel externe du financeur (si disponible)
            type:
              type: string
              description: Type de financement du dossier
              enum:
                - cpf
                - kairosAif
                - individual
                - opco
                - poleEmploi
                - company
            billId:
              type: string
            billNumber:
              type: string
            amountHtNet:
              type: number
            amountToInvoice:
              type: number
            amountCGU:
              type: number
            amountTtc:
              type: number
            amountHt:
              type: number
            vatHtAmount5:
              type: number
            vatAmount5:
              type: number
            vatHtAmount20:
              type: number
            vatAmount20:
              type: number
            iban:
              type: string
            metadata:
              type: array
              items:
                type: string
              description: tableau associatif clé - valeur, disponible uniquement pour le propriétaire
            history:
              type: object
              properties:
                notProcessedDate:
                  type: string
                  format: date-time
                validatedDate:
                  type: string
                  format: date-time
                acceptedDate:
                  type: string
                  format: date-time
                inTrainingDate:
                  type: string
                  format: date-time
                terminatedDate:
                  type: string
                  format: date-time
                serviceDoneDeclaredDate:
                  type: string
                  format: date-time
                serviceDoneValidatedDate:
                  type: string
                  format: date-time
                refusedByAttendeeDate:
                  type: string
                  format: date-time
                refusedByOrganismDate:
                  type: string
                  format: date-time
                canceledByAttendeeDate:
                  type: string
                  format: date-time
                canceledByOrganismDate:
                  type: string
                  format: date-time
                canceledByAttendeeNotRealizedDate:
                  type: string
                  format: date-time
                billedDate:
                  type: string
                  format: date-time
            trainingActionInfo:
              type: object
              properties:
                vat:
                  type: number
                title:
                  type: string
                quitReason:
                  type: string
                  description: Les codes de sortie sont disponibles via l'appel /api/registrationFoldersReasons et l'option type=terminated
                address:
                  type: object
                  properties:
                    id:
                      type: string
                    city:
                      type: string
                    line4:
                      type: string
                    number:
                      type: string
                    country:
                      type: string
                    postBox:
                      type: string
                    zipCode:
                      type: number
                    roadName:
                      type: string
                    roadType:
                      type: string
                    idAddress:
                      type: string
                    residence:
                      type: string
                    countryCode:
                      type: string
                    fullAddress:
                      type: string
                    trainingSite:
                      type: string
                    corporateName:
                      type: string
                    roadTypeLabel:
                      type: string
                    informationSite:
                      type: string
                    repetitionIndex:
                      type: string
                    subscriptionSite:
                      type: string
                    additionalAddress:
                      type: string
                    repetitionIndexLabel:
                      type: string
                    reducedMobilityAccessCompliant:
                      type: string
                    reducedMobilityAccessModalities:
                      type: string
                content:
                  type: string
                sessionId:
                  type: string
                totalExcl:
                  type: number
                  description: montant total de la formation HT
                totalIncl:
                  type: number
                  description: montant total de la formation TTC
                companyName:
                  type: string
                vatExclTax5:
                  type: number
                  description: montant soumis à la TVA à 5.5% - HT
                vatInclTax5:
                  type: number
                  description: montant soumis à la TVA à 5.5% - TTC
                teachingFees:
                  type: number
                  description: montant total de la formation TTC
                trainingGoal:
                  type: string
                vatExclTax20:
                  type: number
                  description: montant soumis à la TVA à 20% - HT
                vatInclTax20:
                  type: number
                  description: montant soumis à la TVA à 20% - TTC
                hoursInCenter:
                  type: number
                solicitations:
                  type: array
                  items:
                    type: string
                trainingPaces:
                  description: rythme de formation, 1 => en journée, 2 => en soirée, 3 => en semaine, 4 => le weekend, 5 => temps plein, 6 => temps partiel, 7 => plusieurs rythmes possibles
                  type: array
                  items:
                    type: number
                additionalFees:
                  type: number
                dureeAbsenceOF:
                  type: number
                expectedResult:
                  type: string
                hoursInCompany:
                  type: number
                sessionEndDate:
                  type: string
                  format: date-time
                uniteAbsenceOF:
                  type: number
                averageDuration:
                  type: number
                sessionStartDate:
                  type: string
                  format: date-time
                forceMajeurCaseOf:
                  type: boolean
                indicativeDuration:
                  type: number
                weeklyDuration:
                  type: number
                teachingModalities:
                  type: string
                  enum:
                    - 0
                    - 1
                    - 2
                  description: formation présentielle ou à distance; 0 => entièrement présentielle, 1 => mixte (présentielle et à distance), 2 => entièrement à distance
                trainingReleaseDate:
                  type: number
                  format: date-time
                typeOfTrainingCourse:
                  type: number
                validatedServiceDone:
                  type: boolean
                additionalFeesDetails:
                  type: string
                trainingCompletionRate:
                  type: number
                isConditionsPrerequisites:
                  type: boolean
                conditionsPrerequisitesDetails:
                  type: string
                externalId:
                  type: string
                trainingActionId:
                  type: string
                trainingId:
                  type: string
                duration:
                  type: number
                externalLink:
                  type: string
                  description: Lien vers l'action de formation publique ou privée (si disponible)
            completionRate:
              type: number
            files:
              type: array
              items:
                $ref: '#/components/schemas/RegistrationFolderFile'
            tags:
              type: array
              description: liste de tags associée au dossier de formation
              items:
                type: string
            _links:
              type: object
              properties:
                self:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/registrationFolders/*externalIdRegistrationFolder*
                validate:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/registrationFolders/*externalIdRegistrationFolder*/validate
                inTraining:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/registrationFolders/*externalIdRegistrationFolder*/inTraining
                terminate:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/registrationFolders/*externalIdRegistrationFolder*/terminate
                serviceDone:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/registrationFolders/*externalIdRegistrationFolder*/serviceDone
                refuse:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/registrationFolders/*externalIdRegistrationFolder*/refuse
                cancel:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/registrationFolders/*externalIdRegistrationFolder*/cancel
                billing:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/registrationFolders/*externalIdRegistrationFolder*/billing
                session:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/sessions/*idSession*
                organism:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/organisms/*siret*
                    name:
                      type: string
                      example: WEDOF
                    siret:
                      type: string
                      example: 90301927100019
                payments:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/payments?registrationFolderId=*externalIdRegistrationFolder*
                trainingAction:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/trainingActions/*idTrainingAction*
                certification:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/certifications/*certifInfo*
                    name:
                      type: string
                      description: Nom de la certification
                    certifInfo:
                      type: string
                      description: Code certif info - Carif-Oref
                      example: 123456
                    externalId:
                      type: string
                      description: Code RS / RNCP
                      example: RS1234
                    id:
                      type: number
                      description: ID technique
                    enabled:
                      type: boolean
                      description: Indique si la certification est active ou non
                inPartnershipWith:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/organisms/*siret*
                    name:
                      type: string
                      description: Nom de l'organisme partenaire
                    siret:
                      type: string
                      description: Siret de l'organisme partenaire
                activities:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/activities/RegistrationFolder/*externalIdRegistrationFolder*
                workingContracts:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/workingContracts?registrationFolderExternalId=*externalIdRegistrationFolder*
        RegistrationFolderCreateBody:
          type: object
          properties:
            sessionId:
              type: number
              description: Obligatoire - ID technique de la session choisie (pas l'externalId)
            attendeeId:
              type: number
              description: Obligatoire - ID de l'Apprenant sélectionné
            totalTTC:
              type: number
              description: Obligatoire - Le prix de la formation TTC (nombre flottant)
            type:
              type: string
              description: Obligatoire - Type de financement du dossier à créer
              enum:
                - individual
                - opco
                - poleEmploi
                - company
            poleEmploiId:
              type: string
              description: UNIQUEMENT requis si le type du dossier est poleEmploi - L'id Pole Emploi de l'apprenant
            poleEmploiRegionCode:
              type: string
              enum:
                - 024
                - 034
                - 017
                - 001
                - 044
                - 040
                - 050
                - 027
                - 035
                - 051
                - 065
                - 032
                - 061
                - 020
                - 066
                - 069
                - 041
                - 068
                - 046
                - 012
                - 063
                - 067
                - 071
                - 048
                - 057
                - 056
                - 026
                - 013
                - 049
                - 025
                - 039
                - 070
                - 016
                - 031
              description: UNIQUEMENT requis si le type du dossier est poleEmploi - Le département de l'apprenant, 024 - Alpes, 034 Alpes Provence, 017 Alsace, 001 Aquitaine, 044 Auvergne, 040 Basse Normandie, 050 Bourgogne, 027 Bretagne, 035 Centre, 051 Champagne Ardennes, 065 Corse, 032 Cote d'Azur, 061 Est Francilien, 020 Franche Comte, 066 Guadeloupe, 069 Guyane, 041 Haute Normandie, 068 La Reunion, 046 Languedoc Roussillon, 012 Limousin, 063 Lorraine, 067 Martinique, 071 Mayotte, 048 Midi Pyrenees, 057 Ouest Francilien, 056 Paris, 026 Pas de Calais, 013 Pays de la Loire, 049 Pays du Nord, 025 Picardie, 039 Poitou Charentes, 070 Saint Pierre et Miquelon, 016 Sud Est Francilien, 031 Vallees Rhone Loire
            poleEmploiDevis:
              type: string
              description: UNIQUEMENT requis si le type du dossier est poleEmploi - Le numéro de devis Pole Emploi de l'apprenant
            inPartnershipWith:
              type: string
              description: Facultatif - le SIRET du partenaire
        RegistrationFolderFile:
          type: object
          properties:
            id:
              type: string
            fileName:
              type: string
            typeId:
              type: string
            link:
              type: string
            fileType:
              type: string
            state:
              type: string
              enum:
                - valid
                - refused
                - toReview
            comment:
              type: string
            signedState:
              type: string
              enum:
                - notRequired
                - none
                - partially
                - completed
            generationState:
              type: string
              enum:
                - notGenerated
                - generating
                - generated
            createdOn:
              type: string
              format: date-time
            updatedOn:
              type: string
              format: date-time
        WorkingContract:
          type: object
          properties:
            id:
              type: number
            externalIdTrainingOrganism:
              type: string
            externalIdDeca:
              type: string
            financer:
              type: string
              enum:
                - opcoCfaAtlas
                - opcoCfaAfdas
                - opcoCfaEp
                - opcoCfaMobilites
                - opcoCfaAkto
                - opcoCfaOcapiat
                - opcoCfaUniformation
                - opcoCfa2i
                - opcoCfaConstructys
                - opcoCfaSante
                - opcoCfaOpcommerce
            state:
              type: string
              enum:
                - draft
                - sent
                - pendingAcceptation
                - accepted
                - cancelled
                - refused
                - broken
                - completed
              description: draft = Brouillon ; sent = Transmis ; pendingAcceptation = En cours d'instruction ; accepted = Engagé ; cancelled = Annulé ; refused = Refusé ; broken = Rupture ; completed = Soldé
            type:
              type: string
              enum:
                - 11
                - 21
                - 22
                - 23
                - 31
                - 32
                - 33
                - 34
                - 35
                - 36
                - 37
                - 38
              description: 11 = Premier contrat d’apprentissage de l’apprenti ; 21 = Nouveau contrat avec un apprenti qui a terminé son précédent contrat auprès d’un même employeur ; 22 = Nouveau contrat avec un apprenti qui a terminé son précédent contrat auprès d’un autre employeur ; 23 = Nouveau contrat avec un apprenti dont le précédent contrat auprès d’un autre employeur a été rompu ; 31 = Modification de la situation juridique de l’employeur ; 32 = Changement d’employeur dans le cadre d’un contrat saisonnier ; 33 = Prolongation du contrat suite à un échec à l’examen de l’apprenti ; 34 = Prolongation du contrat suite à la reconnaissance de l’apprenti comme travailleur handicapé ; 35 = Modification du diplôme préparé par l’apprenti ; 36 = Autres changements (changement de maître d’apprentissage, de durée de travail hebdomadaire, réduction de durée, etc) ; 37 = Modification du lieu d’exécution du contrat ; 38 = Modification du lieu principal de réalisation de la formation théorique
            amount:
              type: number
            startDate:
              type: string
              format: date-time
            endDate:
              type: string
              format: date-time
            signedDate:
              type: string
              format: date-time
            amendmentDate:
              type: string
              format: date-time
            breakingDate:
              type: string
              format: date-time
            lastUpdate:
              type: string
              format: date-time
            createdOn:
              type: string
              format: date-time
            updatedOn:
              type: string
              format: date-time
            _links:
              type: object
              properties:
                self:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/workingContracts/*workingContractId*
                registrationFolder:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/registrationFolders/*registrationFolderExternalId*
                    externalId:
                      type: string
                      description: ExternalId du dossier de formation
                employer:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/organisms/*siret*
                    siret:
                      type: string
                      description: Siret de l'employeur
                    name:
                      type: string
                      description: Nom de l'employeur
        CertificationRegistrationFolderFile:
          type: object
          properties:
            name:
              type: string
            fileName:
              type: string
            typeId:
              type: string
            link:
              type: string
            requiredToState:
              type: string
            state:
              type: string
              enum:
                - nonDéposé
                - valid
                - refused
                - toReview
            allowUpload:
              type: boolean
            required:
              type: boolean
        Certification:
          type: object
          properties:
            id:
              type: number
            externalId:
              type: string
              description: Type de la certification associé au code de la certification
            name:
              type: string
            level:
              type: string
            descriptif:
              type: string
            objectif:
              type: string
            amountHt:
              type: float
              description: Prix HT d'un passage de la certification
              example: 85.00
            domains:
              type: array
              description: Tableau des Formacodes relatifs à la certification
              items:
                type: object
                properties:
                  code:
                    type: string
                  name:
                    type: string
            rome:
              type: array
              description: Tableau d'objet des codes rome relatifs à la certification
              items:
                type: object
                properties:
                  code:
                    type: string
                  link:
                    type: string
                    example: https://candidat.pole-emploi.fr/marche-du-travail/fichemetierrome?codeRome=*code*
                  name:
                    type: string
            gfe:
              type: array
              description: Tableau d'objet des groupes de formations emploi relatifs à la certification
              items:
                type: object
                properties:
                  code:
                    type: string
                  name:
                    type: string
            nsf:
              type: array
              description: Tableau d'objet des codes NSF relatifs à la certification
              items:
                type: object
                properties:
                  code:
                    type: string
                  name:
                    type: string
            cpf:
              type: string
              description: Code CPF
            cpfDateStart:
              type: string
              format: date-time
              description: Date d'enregistrement de la certification sur le CPF
            cpfDateEnd:
              type: string
              format: date-time
              description: Date de fin d'enregistrement de la certification sur le CPF
            certifInfo:
              type: string
            lastExternalUpdate:
              type: string
              format: date-time
            enabled:
              type: boolean
              description: true si la certification est active, sinon false
            certificationPartnerFileTypes:
              type: array
              description: Tableau d'objet des fichiers nécessaires au partenariat de certification
              items:
                type: object
                properties:
                  id:
                    type: number
                  name:
                    type: string
                  accept:
                    type: string
                    description: Format attendu
                    example: ".pdf"
                  description:
                    type: string
                  generated:
                    type: boolean
                    description: Indique si le fichier est généré automatiquement
                  enabled:
                    type: boolean
                    description: Indique si la génération automatique de ce type de fichier est activé ou désactivé
                  templateFile:
                    type: string
                    description: Nom du modèle de document qui sera généré automatiquement
            certificationFolderFileTypes:
              type: array
              description: Tableau d'objet des fichiers nécessaires au passage de la certification
              items:
                type: object
                properties:
                  id:
                    type: number
                  name:
                    type: string
                  accept:
                    type: string
                    description: Format attendu
                    example: ".pdf"
                  toState:
                    type: string
                    description: L'état pour lequel le document est requis / sera généré
                    example: "toControl"
                  description:
                    type: string
                  allowVisibilityPartner:
                    type: boolean
                    description: Indique si le fichier est visible pour le partenaire
                  allowUploadPartner:
                    type: boolean
                    description: Indique si le fichier peut être transmis par le partenaire
                  allowVisibilityAttendee:
                    type: boolean
                    description: Indique si le fichier est disponible depuis l'Espace Candidat
                  allowUploadAttendee:
                    type: boolean
                    description: Indique si le fichier peut être transmis par le candidat depuis l'Espace Candidat
                  qualiopiIndicators:
                    type: array
                    description: Tableau des indicateurs Qualiopi
                    items:
                      type: integer
                  generated:
                    type: boolean
                    description: Indique si le fichier est généré automatiquement
                  enabled:
                    type: boolean
                    description: Indique si la génération automatique de ce type de fichier est activé ou désactivé
                  templateFile:
                    type: string
                    description: Nom du modèle de document qui sera généré automatiquement
            files:
              type: array
              description: Tableau d'objets des fichiers liés à la certification
              items:
                type: object
                properties:
                  id:
                    type: number
                  typeId:
                    type: number
                  fileName:
                    type: string
                  link:
                    type: string
                  fileType:
                    type: string
                  state:
                    type: string
                  comment:
                    type: string
                  generationState:
                    type: string
            validityPeriod:
              type: number
              description: Durée de validité de la certification si appliquée
            type:
              type: string
              example: "RS"
            code:
              type: number
            link:
              type: string
              example: https://www.francecompetences.fr/recherche/rs/*code*
            obtentionSystem:
              type: string
              enum:
                - PAR_ADMISSION
                - PAR_SCORING
            examinationType:
              type: string
              enum:
                - A_DISTANCE
                - EN_PRESENTIEL
                - MIXTE
            isCdcExportable:
              type: boolean
              description: Indique si la certification contient les données obligatoires pour que ses dossiers soient exportables auprès de la CDC
            createdOn:
              type: string
              format: date-time
            updatedOn:
              type: string
              format: date-time
            _links:
              type: object
              properties:
                self:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/certifications/*certifInfo*
                partners:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/certifications/*certifInfo*/partners
                certifiers:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/organisms?organismType=certifier&certifInfo=*certifInfo*
                defaultCertifier:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/organisms/*siret*
                    name:
                      type: string
                    siret:
                      type: string
            certificationInfos:
              type: object
              properties:
                partnerCount:
                  type: number
                  description: Nombre des partenaires sur la certification
                folderCount:
                  type: number
                  description: Nombre des dossiers sur la certification
            isPromoted:
              type: boolean
            hasMultipleCertifiers:
              type: boolean
            allowGenerateCertificate:
              type: boolean
            partnershipComment:
              type: string
              description: Message à destination d'un organisme qui effectue une demande de partenariat sur la certification
            allowPartnershipRequest:
              type: boolean
              description: Indique si un organisme de formation peut effectuer une demande de partenariat sur la certification
            autoRegistering:
              type: boolean
              description: Indique si les dossiers de certification sont automatiquement passés à l'état Enregistré
            state:
              type: string
              enum:
                - active
                - inactive
              description: Indique si la certification est active ou inactive
            surveyOptional:
              type: boolean
              description: Indique si le candidat est autorisé à télécharger le parchemin sans les enquêtes de suivi d'insertion professionnelle
            dataProvider:
              type: string
              enum:
                - franceCompetences
                - internal
            openDataLastUpdate:
              type: string
              format: date-time
            allowAudits:
              type: boolean
            estimatedRegistrationFoldersCount:
              type: number
              description: Estimation du nombre de dossiers de formations issus du CPF
        CertificationCreateBody:
          type: object
          properties:
            name:
              type: string
              description: Nom de la nouvelle certification interne
            link:
              type: string
              description: Lien vers la nouvelle certification (si applicable)
        CertificationUpdateBody:
          type: object
          properties:
            validityPeriod:
              type: number
            obtentionSystem:
              type: string
              enum:
                - PAR_ADMISSION
                - PAR_SCORING
            examinationType:
              type: string
              enum:
                - A_DISTANCE
                - EN_PRESENTIEL
                - MIXTE
            surveyOptional:
              type: boolean
              description: Indique si le candidat peut télécharger le parchemin si les enquêtes de suivi d'insertion profesionnelle ont été répondues
            amountHt:
              type: float
              description: Prix HT d'un passage de la certification
              example: 85.00
            allowPartnershipRequest:
              type: boolean
              description: Indique si les demandes de partneriats sont autorisées sur la certification
            partnershipComment:
              type: string
              description: Permet d'indiquer un commentaire à destination d'un organisme qui effectue une nouvelle demande de partenariat
            link:
              type: string
              description: Permet d'indiquer le lien d'une certification, ne s'applique que dans le cas d'une micro-certification
            autoRegistering:
              type: boolean
              description: Indique si les dossiers de certification sont automatiquement passés à l'état Enregistré
        CertificationFolder:
          type: object
          properties:
            id:
              type: number
              description: identifiant technique du dossier (unique)
            externalId:
              type: string
              description: identifiant métier du dossier (unique)
            examinationDate:
              type: string
              format: date-time
            amountHt:
              type: float
              description: Prix HT de ce passage de la certification
              example: 85.00
            enrollmentDate:
              type: string
              format: date-time
            verbatim:
              type: string
              description: Information complémentaire sur la certification
            optionName:
              type: string
              description: Option si appliquée
              example: Anglais
            examinationEndDate:
              type: string
              format: date-time
            examinationPlace:
              type: string
            tiersTemps:
              type: boolean
              description: Indique si le candidat a besoin d'un tiers temps
            examinationCenterZipCode:
              type: string
            examinationType:
              type: string
              enum:
                - A_DISTANCE
                - EN_PRESENTIEL
                - MIXTE
            issueDate:
              type: string
              format: date-time
            expirationDate:
              type: string
              format: date-time
            detailedResult:
              type: string
            europeanLanguageLevel:
              type: string
              enum:
                - C2
                - C1
                - B2
                - B1
                - A2
                - A1
                - INSUFFISANT
            accessModality:
              type: string
              enum:
                - FORMATION_INITIALE_HORS_APPRENTISSAGE
                - FORMATION_INITIALE_APPRENTISSAGE
                - FORMATION_CONTINUE_HORS_CONTRAT_DE_PROFESSIONNALISATION
                - FORMATION_CONTINUE_CONTRAT_DE_PROFESSIONNALISATION
                - VAE
                - EQUIVALENCE_(DIPLOME_ETRANGER)
                - CANDIDAT_LIBRE
              description: Si accessModality est de type VAE, accessModalityVae doit être déclaré
            accessModalityVae:
              type: string
              enum:
                - CONGES_VAE
                - VAE_CLASSIQUE
              description: Requis si la valeur accessModality est 'VAE'
            digitalProofLink:
              type: string
            createdOn:
              type: string
              format: date-time
            updatedOn:
              type: string
              format: date-time
            stateLastUpdate:
              type: string
              format: date-time
            gradePass:
              type: string
              enum:
                - SANS_MENTION
                - MENTION_ASSEZ_BIEN
                - MENTION_BIEN
                - MENTION_TRES_BIEN
                - MENTION_TRES_BIEN_AVEC_FELICITATIONS_DU_JURY
            type:
              type: string
              enum:
                - CERTIFIE
                - OF
                - POLE_EMPLOI
                - EMPLOYEUR
                - AUTRE
              description: Initiative à laquelle l'inscription a été réalisée
            state:
              type: string
              enum:
                - toRegister
                - refused
                - registered
                - toTake
                - toControl
                - toRetake
                - failed
                - aborted
                - success
              description: toRegister = À enregistrer ; refused = Refusé ; registered = Enregistré ; toTake = Prêt à passer ; toControl = À contrôler ; toRetake = À repasser ; failed = Échoué ; aborted = Abandonné ; success = Réussi
            inTraining:
              type: boolean
              description: indique si le dossier de formation associé (s'il existe) est dans l'état "en formation"
            certifiedData:
              type: boolean
              description: Indique si le résultat du passage de la certification est temporaire au moment de l’envoi des données à la Caisse des Dépôts (false) ou s’il est définitif (true).
            fullCertification:
              type: boolean
              description: Indique si le dossier de certification est certifiant sur l'ensemble de la certification (= non limité à certains blocs de compétences).
            cdcState:
              type: string
              enum:
                - notExported
                - exported
                - processedOk
                - processedKo
              description: Indique où en est le dossier de certification dans le processus d'accrochage auprès de la CDC ; notExported = Jamais exporté; exported = Exporté et en attente de l'accusé; processedOk = Traité avec succès; processedKo = Traité avec erreur
            cdcCompliant:
              type: boolean
              description: Indique si le dossier de certification contient les données de l'apprenant obligatoires pour l'accrochage en cas d'obtention de la certification
            cdcToExport:
              type: boolean
              description: Indique si le dossier de certification devra être inclus dans les prochains exports pour l'accrochage (par défaut oui, sauf si déjà accroché avec succès)
            cdcExcluded:
              type: boolean
              description: Indique si le dossier de certification doit être exclu de l'accrochage
            tags:
              type: array
              description: liste de tags associée au dossier de certification, visible uniquement par le certificateur
              items:
                type: string
            files:
              type: array
              items:
                $ref: '#/components/schemas/CertificationFolderFile'
            comment:
              type: string
            certificateId:
              type: string
              description: identifiant du parchemin de la certification (unique pour la certification)
            attendeeLink:
              type: string
              description: lien de visionnage du dossier à destination de l'apprenant
            cdcTechnicalId:
              type: string
              description: identifiant technique du passage de la certification pour l'accrochage
            badgeAssertion:
              type: string
              description: Lien vers le badge de la certification
            metadata:
              type: array
              items:
                type: string
              description: tableau associatif clé - valeur, disponible uniquement pour le certificateur
            skillSets:
              type: array
              description: liste des blocs de compétences sur laquelle porte le dossier (dans le cas d'une certification RNCP) - Un tableau vide correspond à tous les blocs de compétences
              items:
                type: object
                properties:
                  description:
                    type: string
                  fullCode:
                    type: string
                  id:
                    type: number
                  label:
                    type: string
                  modalities:
                    type: string
                  order:
                    type: number
                  type:
                    type: string
                    description: Valeur "skillSet" => bloc de compétences
                  individualSkills:
                    type: array
                    items:
                      type: object
                      properties:
                        description:
                          type: string
                        fullCode:
                          type: string
                        id:
                          type: number
                        label:
                          type: string
                        modalities:
                          type: string
                        order:
                          type: number
                        type:
                          type: string
                          description: Valeur "skill" => compétence
            history:
              type: object
              properties:
                toTakeDate:
                  type: string
                  format: date-time
                failedDate:
                  type: string
                  format: date-time
                successDate:
                  type: string
                  format: date-time
                toRegisterDate:
                  type: string
                  format: date-time
                registeredDate:
                  type: string
                  format: date-time
                abortedDate:
                  type: string
                  format: date-time
                toControlDate:
                  type: string
                  format: date-time
                refusedDate:
                  type: string
                  format: date-time
                toRetakeDate:
                  type: string
                  format: date-time
            attendee:
              type: object
              properties:
                id:
                  type: number
                lastName:
                  type: string
                firstName:
                  type: string
                firstName2:
                  type: string
                firstName3:
                  type: string
                phoneNumber:
                  type: string
                phoneFixed:
                  type: string
                email:
                  type: string
                dateOfBirth:
                  type: string
                  format: date-time
                nameCityOfBirth:
                  type: string
                codeCountryOfBirth:
                  type: number
                nameCountryOfBirth:
                  type: string
                codeCityOfBirth:
                  type: string
                gender:
                  type: string
                  enum:
                    - male
                    - female
                birthName:
                  type: string
                degree:
                  type: string
                degreeTitle:
                  type: string
                address:
                  type: object
                  properties:
                    id:
                      type: string
                    city:
                      type: string
                    line4:
                      type: string
                    number:
                      type: string
                    country:
                      type: string
                    postBox:
                      type: string
                    zipCode:
                      type: string
                    roadName:
                      type: string
                    roadType:
                      type: string
                    idAddress:
                      type: string
                    residence:
                      type: string
                    countryCode:
                      type: string
                    trainingSite:
                      type: string
                    corporateName:
                      type: string
                    roadTypeLabel:
                      type: string
                    informationSite:
                      type: string
                    repetitionIndex:
                      type: string
                    subscriptionSite:
                      type: string
                    additionalAddress:
                      type: string
                    repetitionIndexLabel:
                      type: string
                    reducedMobilityAccessCompliant:
                      type: string
                    reducedMobilityAccessModalities:
                      type: string
                fullAddress:
                  type: string
                  description: Adresse complète (numéro dans la voie + complément éventuel + type de voie + nom de voie + code postal + ville)
            _links:
              type: object
              properties:
                self:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/certificationFolders/*externalIdCertificationFolder*
                register:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/certificationFolders/*externalIdCertificationFolder*/register
                refuse:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/certificationFolders/*externalIdCertificationFolder*/refuse
                take:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/certificationFolders/*externalIdCertificationFolder*/take
                control:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/certificationFolders/*externalIdCertificationFolder*/control
                retake:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/certificationFolders/*externalIdCertificationFolder*/retake
                fail:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/certificationFolders/*externalIdCertificationFolder*/fail
                success:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/certificationFolders/*externalIdCertificationFolder*/succeed
                abort:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/certificationFolders/*externalIdCertificationFolder*/abort
                certification:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/certifications/*certifInfo*
                survey:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/surveys/*externalIdCertificationFolder*/
                registrationFolder:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/registrationFolders/*externalIdRegistrationFolder*
                    externalId:
                      type: string
                    type:
                      type: string
                    state:
                      type: string
                      enum:
                        - notProcessed
                        - validated
                        - waitingAcceptation
                        - accepted
                        - inTraining
                        - terminated
                        - serviceDoneDeclared
                        - serviceDoneValidated
                        - canceledByAttendee
                        - canceledByAttendeeNotRealized
                        - canceledByOrganism
                        - refusedByAttendee
                        - refusedByOrganism
                        - rejectedWithoutTitulaireSuite
                        - rejectedWithoutCdcSuite
                        - rejectedWithoutOfSuite
                        - rejected
                    description: notProcessed = À traiter ; validated = Validé ; waitingAcceptation = Validé (En cours d'instruction Pôle emploi) ; accepted = Accepté ; inTraining = En formation ; terminated = Sortie de formation ; serviceDoneDeclared = Service fait déclaré ; serviceDoneValidated = Service fait validé ; canceledByAttendee = Annulation titulaire ; canceledByAttendeeNotRealized = Annulation titulaire (non réalisé) ; canceledByOrganism = Annulé (par l'organisme) ; refusedByAttendee = Refus titulaire ; refusedByOrganism = Refusé (par l'organisme) ; rejectedWithoutTitulaireSuite = Annulé sans suite ; rejectedWithoutCdcSuite = Annulé sans suite ; rejectedWithoutOfSuite = Annulé sans suite ; rejected = Annulé sans suite
                partner:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/organisms/*siret*
                    name:
                      type: string
                    siret:
                      type: string
        CertificationFolderCreateBody:
          type: object
          properties:
            certifInfo:
              type: string
              description: Le certifInfo de la certification sélectionnée
            attendeeId:
              type: number
              description: Id de l'apprenant sélectionné
            optionName:
              type: string
              description: Option si appliquée
              example: Anglais
            enrollmentDate:
              type: string
              format: date
              description: Date d'inscription à la certification
            type:
              type: string
              enum:
                - CERTIFIE
                - OF
                - POLE_EMPLOI
                - EMPLOYEUR
                - AUTRE
              description: Initiative à laquelle l'inscription a été réalisée
            accessModality:
              type: string
              enum:
                - FORMATION_INITIALE_HORS_APPRENTISSAGE
                - FORMATION_INITIALE_APPRENTISSAGE
                - FORMATION_CONTINUE_HORS_CONTRAT_DE_PROFESSIONNALISATION
                - FORMATION_CONTINUE_CONTRAT_DE_PROFESSIONNALISATION
                - VAE
                - EQUIVALENCE_(DIPLOME_ETRANGER)
                - CANDIDAT_LIBRE
              description: Si accessModality est de type VAE, accessModalityVae doit être déclaré
            accessModalityVae:
              type: string
              enum:
                - CONGES_VAE
                - VAE_CLASSIQUE
              description: Requis si la valeur accessModality est 'VAE'
        CertificationFolderFile:
          type: object
          properties:
            id:
              type: string
            fileName:
              type: string
            typeId:
              type: string
            link:
              type: string
            fileType:
              type: string
            state:
              type: string
              enum:
                - valid
                - refused
                - toReview
            comment:
              type: string
            signedState:
              type: string
              enum:
                - notRequired
                - none
                - partially
                - completed
            generationState:
              type: string
              enum:
                - notGenerated
                - generating
                - generated
            createdOn:
              type: string
              format: date-time
            updatedOn:
              type: string
              format: date-time
        UploadFileResult:
          type: object
          properties:
            id:
              type: string
              example: 0
            typeId:
              type: string
              example: 0
            fileName:
              type: string
              example: Parchemin.pdf
            link:
              type: string
              example: null
            fileType:
              type: string
              example: application/pdf
            state:
              type: string
              enum:
                - valid
                - refused
                - toReview
              example: valid
            comment:
              type: string
              example: null
            generationState:
              type: string
              enum:
                - notGenerated
                - generating
                - generated
              example: notGenerated
            createdOn:
              type: string
              format: date-time
            updatedOn:
              type: string
              format: date-time
            _links:
              type: object
              properties:
                self:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/certificationFolders/*idCertificationFolder*
        Invoice:
          type: object
          properties:
            createdOn:
              type: string
              format: date-time
            updatedOn:
              type: string
              format: date-time
            id:
              type: number
            externalId:
              type: string
              description: Numéro de la facture
            dueDate:
              type: string
              format: date-time
              description: Date d'échéance de la facture
            description:
              type: string
            paymentLink:
              type: string
              description: Lien de paiement de la facture
            entityClass:
              type: string
              description: Entité associée à la facture - ne peut être que CertificationPartner
            entityId:
              type: string
              description: Identifiant de l'entité associée à la facture
            state:
              type: string
              enum:
                - paid
                - canceled
                - waitingPayment
              description: paid = Payée ; canceled = Annulée ; waitingPayment = En attente
            type:
              type: string
              enum:
                - invoice
                - deposit
                - creditNote
              description: invoice = Facture ; deposit = Acompte ; creditNote = Avoir
            link:
              type: string
              description: Lien de la facture
        InvoiceCreateBody:
          type: object
          properties:
            externalId:
              type: string
            state:
              type: string
              enum:
                - paid
                - canceled
                - waitingPayment
            type:
              type: string
              enum:
                - invoice
                - deposit
                - creditNote
            paymentLink:
              type: string
            dueDate:
              type: string
              format: date-time
            description:
              type: string
            file:
              type: string
              description: Peut être un fichier ou un lien
        InvoiceUpdateBody:
          type: object
          properties:
            externalId:
              type: string
            state:
              type: string
              enum:
                - paid
                - canceled
                - waitingPayment
            type:
              type: string
              enum:
                - invoice
                - deposit
                - creditNote
            paymentLink:
              type: string
            dueDate:
              type: string
              format: date-time
            description:
              type: string
            file:
              type: string
              description: Peut être un fichier ou un lien
        MessageTemplate:
          type: object
          properties:
            createdOn:
              type: string
              format: date-time
            updatedOn:
              type: string
              format: date-time
            id:
              type: number
            title:
              type: string
            subject:
              type: string
              description: Objet du message
            type:
              type: string
              enum:
                - sms
                - email
            state:
              type: string
              enum:
                - active
                - inactive
            body:
              type: string
              description: Corps du message
            replyTo:
              type: array
              description: Liste d'adresses mail (message de type mail) ou de téléphone (message de type sms) associé à "Répondre à"
              items:
                type: string
            to:
              type: array
              description: Liste d'adresses mail de destinataires (message de type mail) ou de téléphone (message de type sms)
              items:
                type: string
            cc:
              type: array
              description: Liste d'adresses mail mis en copie (uniquement pour un message de type mail)
              items:
                type: string
            cci:
              type: array
              description: Liste d'adresses mail mis en copie cachée (uniquement pour un message de type mail)
              items:
                type: string
            entityClass:
              type: string
              description: L'entité cible pour programmer le message
              example: RegistrationFolder ou CertificationFolder ou Proposal
            events:
              type: array
              description: Liste des évènements associés au message
              items:
                type: string
            delay:
              type: string
              description: Permet de rajouter un délai pour l'envoi du message
              example: +6days
            tags:
              type: array
              description: liste de tags associée au dossier de formation
              items:
                type: string
            certifInfos:
              type: array
              description: Liste des certifications associées au message
              items:
                type: string
            qualiopiIndicators:
              type: array
              description: Tableau des indicateurs Qualiopi
              items:
                type: integer
            allowResend:
              type: boolean
              description: Permet l'envoi du message plusieurs fois pour un même dossier
        MessageCreateBody:
          type: object
          properties:
            entityClass:
              type: string
              description: Entité (RegistrationFolder ou CertificationFolder ou CertificationPartner ou Proposal)
            entityId:
              type: string
              description: Identifiant de l'entité (externalId du dossier de formation / certification ou le code de la proposition ou l'id du partenariat)
            type:
              type: string
              enum:
                - email
                - sms
            siret:
              type: string
              description: Siret de l'organisme courant
            body:
              type: string
              description: Corps du message
            subject:
              type: string
              description: Sujet du mail (valable que pour l'envoi de message de type email)
            to:
              type: array
              description: Liste d'adresses mail de destinataires (message de type mail) ou de téléphone (message de type sms)
              items:
                type: string
            replyTo:
              type: array
              description: Liste d'adresses mail associé à "Répondre à" (valable que pour l'envoi de message de type email)
              items:
                type: string
            cc:
              type: array
              description: Liste d'adresses mail mis en copie (valable que pour l'envoi de message de type email)
              items:
                type: string
            cci:
              type: array
              description: Liste d'adresses mail mis en copie cachée (valable que pour l'envoi de message de type email)
              items:
                type: string
        Message:
          type: object
          properties:
            id:
              type: number
            subject:
              type: string
              description: Objet du message
            body:
              type: string
              description: Corps du message
            entityClass:
              type: string
              description: L'entité modifiée
              example: RegistrationFolder ou CertificationFolder ou Proposal
            entityId:
              type: string
              description: L'externalId du dossier de formation ou l'id du dossier de certification ou du code d'une proposition
            type:
              type: string
              enum:
                - sms
                - email
            state:
              type: string
              enum:
                - sent
                - notSent
                - failed
                - scheduled
            replyTo:
              type: array
              description: Liste d'adresses mail (message de type mail) ou de téléphone (message de type sms) associé à "Répondre à"
              items:
                type: string
            to:
              type: array
              description: Liste d'adresses mail de destinataires (message de type mail) ou de téléphone (message de type sms)
              items:
                type: string
            cc:
              type: array
              description: Liste d'adresses mail mis en copie (uniquement pour un message de type mail)
              items:
                type: string
            cci:
              type: array
              description: Liste d'adresses mail mis en copie cachée (uniquement pour un message de type mail)
              items:
                type: string
            scheduledAt:
              type: string
              format: date-time
              description: Date programmé de l'envoi du message
            sentAt:
              type: string
              format: date-time
              description: Date d'envoi du message
            _links:
              type: object
              properties:
                messageTemplate:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/messageTemplates/*entityClass*
        RegistrationFolderEvent:
          type: string
          enum:
            - created
            - updated
            - notProcessed
            - validated
            - waitingAcceptation
            - accepted
            - inTraining
            - terminated
            - serviceDoneDeclared
            - serviceDoneValidated
            - canceledByAttendee
            - canceledByAttendeeNotRealized
            - canceledByOrganism
            - refusedByAttendee
            - refusedByOrganism
            - rejectedWithoutTitulaireSuite
            - rejected
            - fileAdded
            - fileUpdated
            - fileDeleted
        RegistrationFolderAlertEvent:
          type: string
          enum:
            - notValidated
            - notAccepted
            - notInTraining
            - notServiceDoneDeclared
        RegistrationFolderBillingEvent:
          type: string
          enum:
            - notBillable
            - depositWait
            - depositPaid
            - toBill
            - billed
            - paid
        RegistrationFolderControlEvent:
          type: string
          enum:
            - inControl
            - notInControl
            - released
        OrganismEvent:
          type: string
          enum:
            - cpfCatalogUploadFinished
            - cpfCatalogExportFinished
        CertificationFolderEvent:
          type: string
          enum:
            - toRegister
            - refused
            - registered
            - toTake
            - toRetake
            - toControl
            - failed
            - success
            - abort
            - accrochageOk
            - accrochageKo
            - fileAdded
            - fileUpdated
            - fileDeleted
        CertificationFolderSurveyEvent:
          type: string
          enum:
            - created
            - initialExperienceAnswered
            - sixMonthExperienceAnswered
            - longTermExperienceAnswered
            - sixMonthExperienceAvailable
            - longTermExperienceAvailable
        Proposal:
          type: object
          properties:
            amount:
              type: number
            discountType:
              type: string
              enum:
                - none
                - percent
                - fixed
                - amount
            expire:
              type: string
              format: date-time
            state:
              type: string
              enum:
                - template
                - draft
                - active
                - viewed
                - accepted
                - refused
              description: template = Modèle ; draft = Brouillon ; active = Active ; viewed = Vue ; accepted = Acceptée ; refused = Refusée
            firstName:
              type: string
              description: Prénom de l'apprenant si la proposition est individuelle, null si la proposition est générique
            lastName:
              type: string
              description: Nom de famille de l'apprenant si la proposition est individuelle, null si la proposition est générique
            email:
              type: string
              description: Email de l'apprenant si la proposition est individuelle, null si la proposition est générique
            phoneNumber:
              type: string
              description: Téléphone portable de l'apprenant si la proposition est individuelle, null si la proposition est générique
            code:
              type: string
              description: Code de la proposition
            notes:
              type: string
            limitUsage:
              type: number
              description: Nombre d'utilisation de la proposition (un seul usage pour une proposition individuelle)
            autoValidate:
              type: boolean
              description: Auto-validation de la proposition
            sessionStartDate:
              type: string
              format: date-time
            sessionEndDate:
              type: string
              format: date-time
            description:
              type: string
            stateLastUpdate:
              type: string
              format: date-time
            createdOn:
              type: string
              format: date-time
            updatedOn:
              type: string
              format: date-time
            indicativeDuration:
              type: number
            logo:
              type: string
            color:
              type: string
            link:
              type: string
              description: Lien de la proposition apprenant
            link_commercial:
              type: string
              description: Lien de la proposition commerciale
            usedCount:
              type: number
              description: Nombre de dossiers créés sur la proposition
            isIndividual:
              type: boolean
              description: Proposition associée à un apprenant
            tags:
              type: array
              description: liste de tags associée à la proposition
              items:
                type: string
            trainingActions:
              type: array
              description: Tableau d'objets des trainings actions de la proposition composés d'externalId
              items:
                type: object
                properties:
                  externalId:
                    type: string
            selectedTrainingAction:
              type: object
              properties:
                lastUpdate:
                  type: string
                  format: date-time
                externalId:
                  type: string
                  description: l'externalId de la training action
                indicativeDuration:
                  type: number
                totalTvaTTC:
                  type: number
                trainingTitle:
                  type: string
                metadata:
                  type: array
                  items:
                    type: string
                  description: tableau associatif clé - valeur
                _links:
                  type: object
                  properties:
                    self:
                      type: object
                      properties:
                        href:
                          type: string
                          example: /api/trainingActions/*externalIdTrainingAction*
                    sessions:
                      type: object
                      properties:
                        href:
                          type: string
                          example: /api/sessions?trainingActionId=*externalIdTrainingAction*
                    evaluations:
                      type: object
                      properties:
                        href:
                          type: string
                          example: /api/evaluations?trainingActionId=*externalIdTrainingAction*
                    training:
                      type: object
                      properties:
                        href:
                          type: string
                          example: /api/trainings?trainingActionId=*externalIdTraining*
                    sales:
                      type: object
                      properties:
                        name:
                          type: string
                        email:
                          type: string
                        href:
                          type: string
                          example: /api/users/*email*
            _links:
              type: object
              properties:
                self:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/proposals/*id*
                organism:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/organisms/*siret*
                    name:
                      type: string
                    siret:
                      type: string
                trainingAction:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/trainingActions?proposalId=*idProposition*
                registrationFolder:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/registrationFolders/*externalIdRegistrationFolder*
                    externalId:
                      type: string
                    type:
                      type: string
                    state:
                      type: string
                      enum:
                        - notProcessed
                        - validated
                        - waitingAcceptation
                        - accepted
                        - inTraining
                        - terminated
                        - serviceDoneDeclared
                        - serviceDoneValidated
                        - canceledByAttendee
                        - canceledByAttendeeNotRealized
                        - canceledByOrganism
                        - refusedByAttendee
                        - refusedByOrganism
                        - rejectedWithoutTitulaireSuite
                        - rejectedWithoutCdcSuite
                        - rejectedWithoutOfSuite
                        - rejected
                      description: notProcessed = À traiter ; validated = Validé ; waitingAcceptation = Validé (En cours d'instruction Pôle emploi) ; accepted = Accepté ; inTraining = En formation ; terminated = Sortie de formation ; serviceDoneDeclared = Service fait déclaré ; serviceDoneValidated = Service fait validé ; canceledByAttendee = Annulation titulaire ; canceledByAttendeeNotRealized = Annulation titulaire (non réalisé) ; canceledByOrganism = Annulé (par l'organisme) ; refusedByAttendee = Refus titulaire ; refusedByOrganism = Refusé (par l'organisme) ; rejectedWithoutTitulaireSuite = Annulé sans suite ; rejectedWithoutCdcSuite = Annulé sans suite ; rejectedWithoutOfSuite = Annulé sans suite ; rejected = Annulé sans suite
        ProposalCreateOrUpdateBody:
          type: object
          properties:
            firstName:
              type: string
              description: Prénom de l'apprenant si la proposition est individuelle, null si la proposition est générique
            lastName:
              type: string
              description: Nom de famille de l'apprenant si la proposition est individuelle, null si la proposition est générique
            email:
              type: string
              description: Email de l'apprenant si la proposition est individuelle, null si la proposition est générique
            phoneNumber:
              type: string
              description: Téléphone portable de l'apprenant si la proposition est individuelle, null si la proposition est générique
            limitUsage:
              type: number
              description: Nombre d'utilisation de la proposition (un seul usage pour une proposition individuelle)
            code:
              type: string
            notes:
              type: string
              description: Notes privées
            description:
              type: string
              description: Message public
            logo:
              type: string
            color:
              type: string
            amount:
              type: number
            discountType:
              type: string
              enum:
                - none
                - percent
                - fixed
                - amount
            sessionStartDate:
              type: string
              format: date-time
            sessionEndDate:
              type: string
              format: date-time
            expire:
              type: string
              format: date-time
            indicativeDuration:
              type: number
            trainingActions:
              type: array
              description: Tableau d'objets des trainings actions de la proposition composés d'externalId
              items:
                type: object
                properties:
                  externalId:
                    type: string
            autoValidate:
              type: boolean
            tags:
              type: array
              description: liste de tags pour une proposition
              items:
                type: string
        CertificationPartnerDetails:
          type: object
          properties:
            foldersTotal:
              type: number
              description: Nombre total de dossiers de certification
            countByState:
              type: array
              items:
                type: object
                properties:
                  registered:
                    type: number
                    description: Nombre de dossiers de certification à l'état Enregistré
                  toTake:
                    type: number
                    description: Nombre de dossiers de certification à l'état Prêt à passer
                  failed:
                    type: number
                    description: Nombre de dossiers de certification à l'état Échoué
                  success:
                    type: number
                    description: Nombre de dossiers de certification à l'état Réussi
                  aborted:
                    type: number
                    description: Nombre de dossiers de certification à l'état Abandonné
                  toRegister:
                    type: number
                    description: Nombre de dossiers de certification à l'état À enregistrer
                  toControl:
                    type: number
                    description: Nombre de dossiers de certification à l'état À contrôler
                  toRetake:
                    type: number
                    description: Nombre de dossiers de certification à l'état À repasser
                  refused:
                    type: number
                    description: Nombre de dossiers de certification à l'état Refusé
            successRate:
              type: number
              description: Taux de réussite
            takeRate:
              type: number
              description: Taux de passage global
            takeRateAfterDelay:
              type: array
              items:
                type: object
                properties:
                  1Month:
                    type: number
                    description: Taux de passage après au moins 1 mois
                  3Months:
                    type: number
                    description: Taux de passage après au moins 3 mois
                  6Months:
                    type: number
                    description: Taux de passage après au moins 6 mois
            abortRate:
              type: number
              description: Taux d'abandon global
            abortExploded:
              type: array
              items:
                type: object
                properties:
                  beforeTrainingRate:
                    type: number
                    description: Taux d'abandon avant la formation
                  inTrainingRate:
                    type: number
                    description: Taux d'abandon pendant la formation
                  afterTrainingRate:
                    type: number
                    description: Taux d'abandon après la formation
                  undeterminedRate:
                    type: number
                  beforeTrainingCount:
                    type: number
                    description: Nombre de dossiers abandonnés avant la formation
                  inTrainingCount:
                    type: number
                    description: Nombre de dossiers abandonnés pendant la formation
                  afterTrainingCount:
                    type: number
                    description: Nombre de dossiers abandonnés après la formation
                  undeterminedCount:
                    type: number
                  count:
                    type: number
            evaluation:
              type: array
              items:
                type: object
                properties:
                  averageRating:
                    type: number
                    description: Évaluation moyenne
                  reviewCount:
                    type: number
                    description: Nombre d'évaluations
                  reviewRate:
                    type: number
                    description: Taux d'évaluation
            pricing:
              type: array
              items:
                type: object
                properties:
                  min:
                    type: number
                    description: Prix minimum d'une formation
                  max:
                    type: number
                    description: Prix maximum d'une formation
                  average:
                    type: number
                    description: Prix moyen d'une formation
            duration:
              type: array
              items:
                type: object
                properties:
                  min:
                    type: number
                    description: Durée minimum d'une formation
                  max:
                    type: number
                    description: Durée maximum d'une formation
                  average:
                    type: number
                    description: Durée moyenne d'une formation
            countRegistrationFolderByType:
              type: array
              items:
                type: object
                properties:
                  cpf:
                    type: number
                    description: Nombre de dossiers de formation issu du financement CPF ayant un dossier de cetification
                  company:
                    type: number
                    description: Nombre de dossiers de formation issu du financement Entreprise ayant un dossier de cetification
                  individual:
                    type: number
                    description: Nombre de dossiers de formation issu du financement Individuel ayant un dossier de cetification
                  poleEmploi:
                    type: number
                    description: Nombre de dossiers de formation issu du financement Pole Emploi ayant un dossier de cetification
                  opco:
                    type: number
                    description: Nombre de dossiers de formation issu du financement OPCO ayant un dossier de cetification
                  kairosAif:
                    type: number
                    description: Nombre de dossiers de formation issu du financement Kairos AIF ayant un dossier de cetification
                  none:
                    type: number
                    description: Nombre de dossier de cetification sans financement connu
        CertificationPartner:
          type: object
          properties:
            id:
              type: string
            certificationId:
              type: number
              description: Id de la certification portant sur le partenariat
            partnerId:
              type: number
              description: Id de l'organisme partenaire
            amountHt:
              type: number
              description: Prix de vente du passage de la certification (Hors Taxe) pour le partenariat
            habilitation:
              type: string
              enum:
                - evaluate
                - train
                - train_evaluate
              description: Habilitation du partenaire à évaluer ou à former ou à évaluer et à former
            state:
              type: string
              enum:
                - draft
                - processing
                - active
                - aborted
                - refused
                - revoked
              description: draft = Demande à compléter, processing = Demande en traitement, active = Partenariat actif, aborted = Demande abandonnée, refused = Demande refusée, revoked = Partenariat révoqué
            comment:
              type: string
              description: Commentaire portant sur le partenariat
            createdOn:
              type: string
              format: date-time
            updatedOn:
              type: string
              format: date-time
            pendingActivation:
              type: boolean
              description: Précise si le partenariat est en attente d'activation
            pendingRevocation:
              type: boolean
              description: Précise si le partenariat est en attente de révocation
            compliance:
              type: string
              description: Conformité du partenariat
              enum:
                - none
                - compliant
                - partiallyCompliant
                - nonCompliant
            files:
              type: array
              items:
                $ref: '#/components/schemas/CertificationPartnerFile'
            tags:
              type: array
              description: Liste de tags associée au partenariat
              items:
                type: string
            metadata:
              description: tableau associatif clé - valeur, disponible uniquement pour le certificateur
              type: array
              items:
                type: string
            trainingsZone:
              type: array
              items:
                type: string
                description: zone géographique (code postaux) du partenaire, disponible uniquement pour le certificateur
            skillSets:
              type: array
              description: liste des blocs de compétences sur lesquels porte le partenariat (dans le cas d'une certification RNCP) - Un tableau vide correspond à tous les blocs de compétences
              items:
                type: object
                properties:
                  description:
                    type: string
                  fullCode:
                    type: string
                  id:
                    type: number
                  label:
                    type: string
                  modalities:
                    type: string
                  order:
                    type: number
                  type:
                    type: string
                    description: Valeur "skillSet" => bloc de compétences
                  individualSkills:
                    type: array
                    items:
                      type: object
                      properties:
                        description:
                          type: string
                        fullCode:
                          type: string
                        id:
                          type: number
                        label:
                          type: string
                        modalities:
                          type: string
                        order:
                          type: number
                        type:
                          type: string
                          description: Valeur "skill" => compétence
            _links:
              type: object
              properties:
                self:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/certifications/*certifInfo*/partners/*siretPartner*
                partner:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/organisms/*siretPartner*
                    name:
                      type: string
                    siret:
                      type: string
                certifier:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/organisms/*siretCertifier*
                    name:
                      type: string
                    siret:
                      type: string
                certification:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/certifications/*certifInfo*
                    name:
                      type: string
                    certifInfo:
                      type: string
                    externalId:
                      type: string
                    id:
                      type: number
                    enabled:
                      type: boolean
                certificationFolders:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/certificationFolders/?siret=*siretPartner*&certifInfo=*certifInfo*
                audits:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/certifications/*certifInfo*/partners/*siretPartner*/audits?siret=*siretPartner*&certifInfo=*certifInfo*
        CertificationPartnerFile:
          type: object
          properties:
            id:
              type: string
            fileName:
              type: string
            typeId:
              type: string
            link:
              type: string
            fileType:
              type: string
            state:
              type: string
              enum:
                - valid
                - refused
                - toReview
            comment:
              type: string
            createdOn:
              type: string
              format: date-time
            updatedOn:
              type: string
              format: date-time
            generationState:
              type: string
              enum:
                - notGenerated
                - generating
                - generated
              example: notGenerated
            signedState:
              type: string
              enum:
                - notRequired
                - none
                - partially
                - completed
        CertificationPartnerUpdateBody:
          type: object
          properties:
            state:
              type: string
              enum:
                - draft
                - processing
                - active
                - aborted
                - refused
                - suspended
                - revoked
              description: État du partenariat de certification
            habilitation:
              type: string
              enum:
                - evaluate
                - train
                - train_evaluate
              description: Habilitation du partenaire à évaluer ou à former ou à évaluer et à former
            comment:
              type: string
              description: Informations complémentaires partagées entre le partenaire et le certificateur portant sur le partenariat
            pendingActivation:
              type: boolean
              description: Précise si le partenariat est en attente d'activation
            pendingRevocation:
              type: boolean
              description: Précise si le partenariat est en attente de révocation
            pendingSuspension:
              type: boolean
              description: Précise si le partenariat est en attente de suspension
            amountHt:
              type: number
              description: Prix de vente du passage de la certification (Hors Taxe) pour le partenaire
            compliance:
              type: string
              enum:
                - compliant
                - partiallyCompliant
                - nonCompliant
            tags:
              type: array
              description: liste de tags associée au partenariat
              items:
                type: string
            metadata:
              type: array
              description: tableau associatif clé - valeur
              items:
                type: string
            trainingsZone:
              type: array
              description: zone géographique (code postaux) du partenaire
              items:
                type: string
            skillSets:
              type: array
              description: liste des blocs de compétences pour lesquels le partenaire peut former (dans le cas d'une certification RNCP) - Un tableau vide correspond à tous les blocs de compétences
              items:
                type: number
        CertificationPartnerReinitialize:
          type: object
        CertificationPartnerAuditTemplate:
          type: object
          properties:
            id:
              type: number
            name:
              type: string
              description: Nom du modèle d'audit
            allowVisibilityPartner:
              type: boolean
              description: Indique si le partenaire peut voir l'audit une fois finalisé
            criterias:
              type: array
              items:
                type: object
                properties:
                  code:
                    type: string
                    description: Identifiant unique du critère
                    example: "trainingsAverageRating"
                  scope:
                    type: string
                    description: Donnée auditée
                    example: "trainingsAverageRating"
                  title:
                    type: string
                    description: Intitulé
                    example: "Évaluation moyenne"
                  advice:
                    type: string
                    description: Conseil de mise en conformité pour le partenaire
                    example: "Effectuer des relances de satisfaction"
                  operation:
                    type: string
                    enum:
                      - contains
                      - containsAll
                      - notContains
                      - notContainsAny
                      - matches
                      - eqs
                      - neqs
                      - eq
                      - neq
                      - lt
                      - lte
                      - gt
                      - gte
                      - isTrue
                      - isFalse
                      - containsArray
                      - notContainsArray
                    description: contains => contient le texte, containsAll => contient tous les mots, notContains => ne contient pas les mots, notContainsAny => ne contient aucun des mots, matches => correspond à l'expression régulière, eqs => chaîne de caractères est égal à, neqs => chaîne de caractères n'est pas égal à, eq => nombre est égal à, neq => nombre n'est pas égal à, lt => plus petit que, lte => plus petit que ou égal à, gt => plus grand que, gte => plus grand que ou égal à, isTrue => oui, isFalse => non, containsArray => tableau inclus, notContainsArray => tableau n'inclut pas
                  parameter:
                    type: string
                    description: Nombre ou chaine de caractères qui sera évalué  doit être respecter
                    example: "4"
                  severity:
                    type: string
                    enum:
                      - majeure
                      - mineure
                      - none
                    description: Indique si le critère entraîne une non-conformité
                  scopeTitle:
                    type: string
                    description: Titre de la donnée auditée
                    example: "Évaluation moyenne"
                  operationTitle:
                    type: string
                    description: Décrit l'opération du critère
                    example: "doit être supérieur ou égal à"
            auditTemplateFileType:
              type: object
              properties:
                id:
                  type: number
                name:
                  type: string
                accept:
                  type: string
                enabled:
                  type: boolean
                toState:
                  type: string
                generated:
                  type: boolean
                googleId:
                  type: string
                description:
                  type: string
                templateFile:
                  type: string
                allowMultiple:
                  type: boolean
                targetFileType:
                  type: string
                allowRegenerate:
                  type: boolean
                allowSignPartner:
                  type: boolean
                allowUploadPartner:
                  type: boolean
                allowVisibilityPartner:
                  type: boolean
            auditCount:
              type: number
              description: Nombre d'audits réalisés associés au modèle
        CertificationPartnerAuditCreateBody:
          type: object
          properties:
            templateId:
              type: number
              description: Id du modèle d'audit
        CertificationPartnerAuditCreateOnPartnersBody:
          type: object
          properties:
            templateId:
              type: number
              description: Id du modèle d'audit
            complete:
              type: boolean
              description: Indique si l'audit doit être clôturer
            updateCompliance:
              type: boolean
              description: Indique si il faut mettre à jour la conformité du partenariat
            suspend:
              type: boolean
              description: Indique si le partenariat doit être suspendu en cas de non-conformité (ne s'applique que pour les certifications actives)
        CertificationPartnerAuditGenerateOnPartners:
          type: object
          properties:
            count:
              type: number
              description: Nombre de partenaires audités
            auditTemplate:
              type: object
              $ref: '#/components/schemas/CertificationPartnerAuditTemplate'
        CertificationPartnerAuditUpdateBody:
          type: object
          properties:
            restartAudit:
              type: boolean
              description: Relancer l'audit si il a échoué
        CertificationPartnerAuditCompleteBody:
          type: object
          properties:
            result:
              type: string
              enum:
                - compliant
                - partiallyCompliant
                - nonCompliant
              description: Résultat de l'audit, compliant => Conforme, partiallyCompliant => Partiellement conforme, nonCompliant => Non conforme
            endDate:
              type: string
              format: date-time
              description: Date de clôture de l'audit
            notes:
              type: string
              description: Notes du certificateur
            updatePartnerCompliance:
              type: boolean
              description: Permet de mettre à jour la conformité du partenariat
        CertificationPartnerAudit:
          type: object
          properties:
            id:
              type: number
            state:
              type: string
              enum:
                - pendingComputation
                - computing
                - inProgress
                - completed
              description: État de l'audit, pendingComputation => En préparation (collecte des données en cours), computing => Analyse des données en cours, inProgress => En cours, completed => Terminé
            result:
              type: string
              enum:
                - compliant
                - partiallyCompliant
                - nonCompliant
              description: Résultat de l'audit réalise, compliant => Conforme, partiallyCompliant => Partiellement conforme, nonCompliant => Non conforme
            startDate:
              type: string
              format: date-time
              description: Date d'enregistrement des données
            endDate:
              type: string
              format: date-time
              description: Date de clôture de l'audit
            notes:
              type: string
              description: Notes du certificateur sur l'audit réalisé
            evaluatedCriterias:
              type: array
              items:
                type: object
                properties:
                  code:
                    type: string
                    description: Identifiant unique du critère
                    example: "trainingsAverageRating"
                  scope:
                    type: string
                    description: Donnée auditée
                    example: "trainingsAverageRating"
                  title:
                    type: string
                    description: Intitulé
                    example: "Évaluation moyenne"
                  value:
                    type: string
                    description: Résultat du critère
                    example: "2"
                  advice:
                    type: string
                    description: Conseil de mise en conformité du certificateur pour le partenaire
                    example: "Effectuer des relances de satisfaction"
                  severity:
                    type: string
                    enum:
                      - majeure
                      - mineure
                      - none
                    description: Indique si le critère entraîne une non-conformité
                  operation:
                    type: string
                    enum:
                      - contains
                      - containsAll
                      - notContains
                      - notContainsAny
                      - matches
                      - eqs
                      - neqs
                      - eq
                      - neq
                      - lt
                      - lte
                      - gt
                      - gte
                      - isTrue
                      - isFalse
                      - containsArray
                      - notContainsArray
                    description: contains => contient le texte, containsAll => contient tous les mots, notContains => ne contient pas les mots, notContainsAny => ne contient aucun des mots, matches => correspond à l'expression régulière, eqs => chaîne de caractères est égal à, neqs => chaîne de caractères n'est pas égal à, eq => nombre est égal à, neq => nombre n'est pas égal à, lt => plus petit que, lte => plus petit que ou égal à, gt => plus grand que, gte => plus grand que ou égal à, isTrue => oui, isFalse => non, containsArray => tableau inclus, notContainsArray => tableau n'inclut pas
                    example: "lte"
                  parameter:
                    type: string
                    description: Nombre ou chaine de caractères qui sera évalué  doit être respecter
                    example: "4"
                  compliance:
                    type: string
                    enum:
                      - compliant
                      - nonCompliant
                      - partiallyCompliant
                    description: Résultat du critère, compliant => Conforme, partiallyCompliant => Partiellement conforme, nonCompliant => Non conforme
                  scopeTitle:
                    type: string
                    description: Titre de la donnée auditée
                    example: "Évaluation moyenne"
                  operationTitle:
                    type: string
                    description: Décrit l'opération du critère
                    example: "doit être supérieur ou égal à"
            _links:
              type: object
              properties:
                self:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/certifications/*certifInfo*/partners/*siretPartner*/audits/*certificationPartnerAuditId*
                complete:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/certifications/*certifInfo*/partners/*siretPartner*/audits/*certificationPartnerAuditId*/complete
                certificationPartnerAuditTemplate:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/certificationPartnerAuditTemplates/*certificationPartnerAuditTemplateId*
                    name:
                      type: string
                certification:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/certifications/*certifInfo*
                    name:
                      type: string
                    certifInfo:
                      type: string
                    externalId:
                      type: string
                    id:
                      type: number
                    enabled:
                      type: boolean
                partner:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/organisms/*siretPartner*
                    name:
                      type: string
                    siret:
                      type: string
                certifier:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/organisms/*siretCertifier*
                    name:
                      type: string
                    siret:
                      type: string
        ApiToken:
          type: object
          properties:
            id:
              type: string
            token:
              type: string
            name:
              type: string
              description: Nom du token
            lastUsed:
              type: string
              format: date-time
              description: Dernière utilisation du token
        ApiTokenCreateBody:
          type: object
          properties:
            name:
              type: string
        Delivery:
          type: object
          properties:
            id:
              type: string
            date:
              type: string
              format: date-time
            guid:
              type: string
              description: Identifiant unique du delivery
            statusCode:
              type: number
              description: Code HTTP Response
            error_message:
              type: string
            event:
              type: string
              description: Type de l'évènement (CertificationEvent, CertificationFolderEvent, CertificationPartnerEvents, CertificationFolderSurveyEvent, EvaluationEvent, PaymentEvent, RegistrationFolderBillingEvent, RegistrationFolderControlEvent, RegistrationFolderEvent, RegistrationFolderAlertEvent)
        Order:
          type: string
          enum:
            - asc
            - desc
        Boolean:
          type: string
          enum:
            - true
            - false
        EvaluationFor:
          type: string
          enum:
            - organism
            - trainingActions
            - all
        RegistrationFolderReasonType:
          type: string
          enum:
            - terminated
            - refused
            - canceled
        OrganismType:
          type: string
          enum:
            - certifier
            - partner
            - all
        CertificationPartnerEvent:
          type: string
          enum:
            - processing
            - active
            - aborted
            - refused
            - revoked
            - fileAdded
            - fileUpdated
            - fileDeleted
            - fileValid
            - fileToReview
            - fileRefused
            - invoiceCreated
            - invoiceUpdated
            - invoicePaid
            - invoiceDeleted
        CertificationEvent:
          type: string
          enum:
            - created
            - updated
            - partners
            - certifiers
        EvaluationEvent:
          type: string
          enum:
            - trainingActionNew
            - trainingActionChanged
            - trainingNew
            - trainingChanged
            - organismNew
            - organismChanged
        Payment:
          type: object
          properties:
            id:
              type: number
            state:
              $ref: '#/components/schemas/PaymentState'
            amount:
              type: float
              description: Montant
            type:
              $ref: '#/components/schemas/PaymentType'
            billNumber:
              type: string
              description: Numéro de facture
            scheduledDate:
              type: string
              format: date-time
              description: Date prévue du versement
            lastUpdate:
              type: string
              format: date-time
              description: Date de dernière mise à jour
            _links:
              type: object
              properties:
                self:
                  type: object
                  properties:
                    href:
                      type: string
                registrationFolder:
                  type: object
                  properties:
                    href:
                      type: string
                    externalId:
                      type: string
                    type:
                      type: string
                    state:
                      type: string
        PaymentEvent:
          type: string
          enum:
            - depositWaiting
            - depositRejected
            - depositIssued
            - issued
            - waiting
            - rejected
        PaymentFormat:
          type: string
          enum:
            - json
            - csv
        PaymentType:
          type: string
          enum:
            - bill
            - deposit
          description: bill = facture, deposit = acompte
        PaymentState:
          type: string
          enum:
            - issued
            - waiting
            - rejected
          description: waiting = en attente, issued = versé, rejected = rejeté
        AddressRoadType:
          type: string
          enum:
            - ACH
            - ALL
            - ART
            - AV
            - BD
            - BEGI
            - CHE
            - CHP
            - CHS
            - CHT
            - CHV
            - CITE
            - COR
            - COTE
            - CRS
            - DOM
            - DSC
            - ECA
            - ESP
            - ESPA
            - FG
            - GR
            - LOT
            - CAR
            - HAM
            - HLE
            - IMP
            - LD
            - MAIL
            - MAR
            - MTE
            - PARC
            - PAS
            - PL
            - PLT
            - PRO
            - PRV
            - PTR
            - QUA
            - QUAI
            - RES
            - RETT
            - RLE
            - ROC
            - RPT
            - RTD
            - RTE
            - RUE
            - SEN
            - SQ
            - TPL
            - TRA
            - VEN
            - VLA
            - VOIE
            - VTE
            - VLGE
          description: ACH => Ancien chemin, ALL => Allée, ART => Ancienne route, AV => Avenue, BD => Boulevard, BEGI => Béguinage, CHE => Chemin, CHP => Champ, CHS => Chaussée, CHT => Château, CHV => Chemin vicinal,  CITE => Cité, COR => Corniche, COTE => Coteau(x), Côte, CRS => Cours, DOM => Domaine, DSC => Descente, ECA => Ecart, ESP => Esplanade, ESPA => Espace, FG => Faubourg, GR => Grande Rue, LOT => Lotissement, CAR => Carrefour, HAM => Hameau, HLE => Halle, IMP => Impasse, LD => Lieu-dit, MAIL => Mail, MAR => Marché, MTE => Montée, PARC => Parc, PAS => Passage, PL => Place, PLN => Plaine, PLT => Plateau, PRO => Promenade, PRV => Parvis, PTR => Petite rue, QUA => Quartier, QUAI => Quai, RETT => Ruellette, RES => Résidence, RLE => Ruelle, ROC => Rocade, RPT => Rond-point, RTD => Rotonde, RTE => Route, RUE => Rue, SEN => Sente - Sentier, SQ => Square, TPL => Terre-plein, TRA => Traverse, VEN => Venelle, VLA => Villa, VOIE => Voie, VTE => Vieille route, VLGE => Village
        RegistrationFolderUpdateBody:
          type: object
          properties:
            priceChange:
              type: object
              description: ATTENTION ! Une seule valeur acceptée parmi les trois suivants, percent => le pourcentage de variation du prix (float), price => le nouveau prix, amount => le montant de variation du prix. Le prix ne peut pas augmenter de plus de 15% du prix de base et peut diminuer jusqu'à 0
              properties:
                percent:
                  type: number
                  description: Valeurs acceptées de 15 à -100 (valeur négative = réduction du prix)
                amount:
                  type: number
                  description: Fait varier le prix de X €. X négatif pour une réduction et positif pour une augmentation du prix.
                price:
                  type: number
                  description: Nouveau tarif en €
            notes:
              type: string
              description: notes privées
            description:
              type: string
              description: note publique visible de l'apprenant
            tags:
              type: array
              description: liste de tags associée au dossier de formation
              items:
                type: string
            inPartnershipWith:
              type: string
              description: le siret d'un partenaire
            controlState:
              type: string
              enum:
                - inControl
                - notInControl
                - released
              description: inControl = En cours de contrôle ; notInControl = Aucun contrôle en cours ; released = contrôle terminé
            completionRate:
              type: number
              description: indique le taux de réalisation (peut être mise à jour à l'état Accepté, En Formation, Terminé)
            metadata:
              type: array
              items:
                type: string
              description: tableau associatif clé - valeur
            trainingActionInfo:
              type: object
              properties:
                sessionStartDate:
                  type: string
                  format: date
                  description: date de début de la session au format YYYY-MM-DD
                sessionEndDate:
                  type: string
                  format: date
                  description: date de fin de la session au format YYYY-MM-DD
                indicativeDuration:
                  type: number
                  description: durée moyenne de la formation (en heures)
                weeklyDuration:
                  type: number
                  description: temps de formation par semaine (en heures). Ne peut pas être supérieur à 99
                hoursInCenter:
                  type: number
                  description: nombre d'heures en centre
                hoursInCompany:
                  type: number
                  description: nombre d'heures en entreprise
                additionalFees:
                  type: number
                  description: montant total des frais additionnels non pris en charge en € TTC - Ce champ est obligatoire si vous avez saisi des détails des frais additionnels non pris en charge
                additionalFeesDetails:
                  type: string
                  example: ""
                  description: détails des frais additionnels non pris en charge - Ce champ est obligatoire si vous avez saisi un montant des frais additionnels non pris en charge
                title:
                  type: string
                  description: intitulé de la formation
                  example: ""
                content:
                  type: string
                  description: contenu de la formation - ne doit pas dépasser 3000 caractères
                  example: ""
                expectedResult:
                  type: string
                  description: résultats attendus de la formation - ne doit pas dépasser 3000 caractères
                  example: ""
                trainingGoal:
                  type: string
                  description: objectif de la formation - ne doit pas dépasser 3000 caractères
                  example: ""
                trainingPaces:
                  description: rythme de formation, 1 => en journée, 2 => en soirée, 3 => en semaine, 4 => le weekend, 5 => temps plein, 6 => temps partiel, 7 => plusieurs rythmes possibles
                  type: array
                  items:
                    type: string
                    enum:
                      - 1
                      - 2
                      - 3
                      - 4
                      - 5
                      - 6
                      - 7
                teachingModalities:
                  type: string
                  enum:
                    - 0
                    - 1
                    - 2
                  description: formation présentielle ou à distance; 0 => entièrement présentielle, 1 => mixte (présentielle et à distance), 2 => entièrement à distance
                typeOfTrainingCourse:
                  type: number
                  enum:
                    - 1
                    - 2
                    - 3
                    - 4
                  description: type de parcours; 1 => 96213 - Collectif, 2 => 96211 - Individualisé, 3 => 96212 - Modulaire, 4 => 96214 - Mixte
                address:
                  type: object
                  properties:
                    number:
                      type: string
                      description: numéro
                      example: "8"
                    repetitionIndex:
                      type: string
                      enum:
                        - B
                        - C
                        - Q
                        - T
                      description: indice de répétition
                      example: B
                    residence:
                      type: string
                      description: résidence, bâtiment, escalier, appartement
                      example: local 1
                    postBox:
                      type: string
                      description: lieu-dit, boîte postale
                      example: BP 31500
                    roadType:
                      type: voir le schéma AddressRoadType
                      description: type de voie
                      example: IMP
                    roadName:
                      type: string
                      description: nom de la voie
                      example: Bonnet
                    additionalAddress:
                      type: string
                      description: complément d'adresse
                      example: rez de chaussée
                    zipCode:
                      type: number
                      description: code postal
                      example: 31500
                    city:
                      type: string
                      description: ville
                      example: TOULOUSE
        CertificationFolderUpdateBody:
          type: object
          properties:
            examinationDate:
              type: string
              format: date-time
              description: Date de début de passage de l'examen - peut être modifié dans les états de certifications "registered", "toTake", "toRetake", "toControl" | peut-être mis à jour par le certificateur et le partenaire
            examinationEndDate:
              type: string
              format: date-time
              description: Date de fin de passage de l'examen - peut être modifié dans les états de certifications "registered", "toTake", "toRetake", "toControl" | peut-être mis à jour par le certificateur et le partenaire | par défault la date de début de passage de l'examen
            enrollmentDate:
              type: string
              format: date-time
              description: Date d'inscription à la certification - peut être modifié dans les états de certifications "toRegister", "registered", "toTake", "toControl" | peut-être mis à jour par le certificateur et le partenaire
            examinationPlace:
              type: string
              description: Lieu de passage de l'examen - peut être modifié dans les états de certifications "registered", "toTake", "toControl", "toRetake" | peut-être mis à jour par le certificateur et le partenaire
              example: Toulouse
            examinationCenterZipCode:
              type: string
              description: Code postal du centre d'examen principal qui a assuré la certification - peut être modifié dans tous les états sauf dans l'état "success" | peut-être mis à jour par le certificateur uniquement
            examinationType:
              type: string
              enum:
                - A_DISTANCE
                - EN_PRESENTIEL
                - MIXTE
              description: Type de passage de l'examen - peut être modifié dans les états de certifications "registered", "toTake", "toControl", "toRetake" | peut-être mis à jour par le certificateur et le partenaire
            verbatim:
              type: string
              description: Information complémentaire sur la certification - peut être modifié dans tous les états sauf dans l'état "success" | peut-être mis à jour par le certificateur et le partenaire
            optionName:
              type: string
              description: Option si appliquée - peut être modifié dans tous les états sauf dans l'état "success" | peut-être mis à jour par le certificateur et le partenaire
              example: Anglais
            accessModality:
              type: string
              enum:
                - FORMATION_INITIALE_HORS_APPRENTISSAGE
                - FORMATION_INITIALE_APPRENTISSAGE
                - FORMATION_CONTINUE_HORS_CONTRAT_DE_PROFESSIONNALISATION
                - FORMATION_CONTINUE_CONTRAT_DE_PROFESSIONNALISATION
                - VAE
                - EQUIVALENCE_(DIPLOME_ETRANGER)
                - CANDIDAT_LIBRE
              description: Si accessModality est de type VAE, accessModalityVae doit être déclaré - peut être modifié dans tous les états sauf dans l'état "success"  | peut-être mis à jour par le certificateur et le partenaire
            accessModalityVae:
              type: string
              enum:
                - CONGES_VAE
                - VAE_CLASSIQUE
              description: Requis si la valeur accessModality est 'VAE' - peut être modifié dans tous les états sauf dans l'état "success" | peut-être mis à jour par le certificateur et le partenaire
            comment:
              type: string
              description: Commentaires - peut être modifié dans tous les états de certification - écrase la valeur précédente | peut-être mis à jour par le certificateur et le partenaire
              example: Obtenu au second passage
            type:
              type: string
              enum:
                - CERTIFIE
                - OF
                - POLE_EMPLOI
                - EMPLOYEUR
                - AUTRE
              description: Initiative à laquelle l'inscription a été réalisée - peut être modifié dans tous les états sauf dans l'état "success" | peut-être mis à jour par le certificateur uniquement
            tags:
              type: array
              description: Liste de tags associée au dossier de certification, uniquement pour le certificateur
              items:
                type: string
            cdcExcluded:
              type: string
              description: Indique si le dossier de certification doit être exclu de l'accrochage | peut-être mis à jour par le certificateur uniquement
            amountHt:
              type: number
              description: Prix de vente du passage de la certification (Hors Taxe)
            certificate:
              type: string
              format: binary
              description: Fichier du parchemin de la certification
            certificateId:
              type: string
              description: Identifiant du parchemin de la certification (unique pour la certification)
            tiersTemps:
              type: boolean
              description: Indique si le candidat a besoin d'un tiers temps
            cdcTechnicalId:
              type: string
              description: Identifiant technique du passage de la certification pour l'accrochage
            badgeAssertion:
              type: string
              description: Lien vers le badge de la certification | peut-être mis à jour par le certificateur et à l'état "success"
        UploadFile:
          type: object
          properties:
            title:
              type: string
              description: Titre du fichier
              example: Parchemin
            typeId:
              type: string
              description: Id du modèle de document
              example: 2
            file:
              type: string
              format: binary
              description: Fichier a envoyer / facultatif si fileToDownload est fourni
              example: Parchemin.pdf
            fileToDownload:
              type: string
              description: URL du fichier à télécharger / facultatif si file est fourni
              example: www.exemple.com/mon_fichier.pdf
        SolicitationFundingType:
          type: string
          enum:
            - POLE_EMPLOI
            - TITULAIRE
            - FINANCEUR
            - EMPLOYEUR
          description: POLE_EMPLOI = 9 ; TITULAIRE = 29 ; FINANCEUR = -1 ; EMPLOYEUR = -2
        TrainingAction:
          type: object
          properties:
            externalId:
              type: string
            indicativeDuration:
              type: number
            totalTvaTTC:
              type: number
            trainingTitle:
              type: string
            lastUpdate:
              type: string
              format: date-time
            _links:
              type: object
              properties:
                self:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/trainingActions/*externalIdTrainingAction*
                sessions:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/sessions?trainingActionId=*externalIdTrainingAction*
                evaluations:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/evaluations?trainingActionId=*externalIdTrainingAction*
                training:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/trainings/*externalIdTraining*
        Session:
          type: object
          properties:
            id:
              type: number
              description: ID technique
            externalId:
              type: string
              description: ID du fournisseur
            startDate:
              type: string
              format: date-time
            endDate:
              type: string
              format: date-time
            sessionInfo:
              type: object
              properties:
                city:
                  type: string
                registrationFolderCount:
                  type: number
                  description: nombre de dossiers de formation pour la session
                activeAttendeeCount:
                  type: number
                  description: nombre d'apprenants ayant un dossier actif (non annulé)
                trainingTitle:
                  type: string
                certificationName:
                  type: string
                totalTvaTTC:
                  type: number
            _links:
              type: object
              properties:
                self:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/sessions/*externalIdSession*
                trainingAction:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/trainingActions/*externalIdTrainingAction*
                registrationFolders:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/registrationFolders?sessionId=*externalIdSession*
        SessionMinDates:
          type: object
          properties:
            cpfSessionMinDate:
              type: string
              format: date-time
              description: Date minimale de session pour un dossier à financement CPF
            poleEmploiSessionMinDate:
              type: string
              format: date-time
              description: Date minimale de session pour un dossier à financement CPF et Pole Emploi
        RefreshRegistrationFoldersAsync:
          type: object
          properties:
            registrationFolders:
              type: array
              description: Liste des dossiers de formation à rafraichir (min = 1, max = 100 dossiers de formation)
              items:
                type: object
                properties:
                  externalId:
                    type: string
                    description: Id du dossier de formation
        RefreshRegistrationFoldersAsyncResult:
          type: object
          properties:
            result:
              type: array
              description: Résultat de la demande de rafraichissement
              items:
                type: object
                properties:
                  externalId:
                    type: string
                    description: Id du dossier de formation
                  state:
                    type: string
                    enum:
                      - scheduledToCreateIfExist
                      - scheduledToRefresh
                      - notScheduledFinalState
                      - notScheduled
                    description: scheduledToCreateIfExist = Programmé et sera créé si existe; scheduledToRefresh = Programmé pour rafraichissement; notScheduledFinalState = Non programmé dossier en l'état final; notScheduled = Non programmable
        InTrainingRegistrationFoldersAsync:
          type: object
          properties:
            registrationFolders:
              type: array
              description: Liste des dossiers de formation à passer à l'état 'en formation' (min = 1)
              items:
                type: object
                properties:
                  externalId:
                    type: string
                    description: Id du dossier de formation
                  date:
                    type: string
                    format: date-time
                    description: Optionnel - Date de l'entrée en formation
        TerminateRegistrationFoldersAsync:
          type: object
          properties:
            registrationFolders:
              type: array
              description: Liste des dossiers de formation à passer à l'état 'sortie de formation' (min = 1)
              items:
                type: object
                properties:
                  externalId:
                    type: string
                    description: Id du dossier de formation
                  date:
                    type: string
                    format: date-time
                    description: Optionnel - Date de la fin de formation
                  code:
                    type: string
                    description: Optionnel - Code raison de la fin de formation
                  absenceDuration:
                    type: float
                    description: Optionnel - durée d'absence
                    example: 1.57
        InTrainingRegistrationFoldersAsyncResult:
          type: object
          properties:
            result:
              type: array
              description: Résultat des dossiers à passer à 'en formation'
              items:
                type: object
                properties:
                  externalId:
                    type: string
                    description: Id du dossier de formation
                  state:
                    type: string
                    enum:
                      - scheduledToInTraining
                      - notScheduledWrongState
                      - notScheduled
                    description: scheduledToInTraining = Programmé pour le passage à 'en formation'; notScheduled = Non programmable; notScheduledWrongState = Dossier dans un état incohérent
        TerminateRegistrationFoldersAsyncResult:
          type: object
          properties:
            result:
              type: array
              description: Résultat des dossiers à passer à 'sortie de formation'
              items:
                type: object
                properties:
                  externalId:
                    type: string
                    description: Id du dossier de formation
                  state:
                    type: string
                    enum:
                      - scheduledToTerminated
                      - notScheduledWrongState
                      - notScheduled
                    description: scheduledToTerminated = Programmé pour le passage à 'sortie de formation'; notScheduled = Non programmable; notScheduledWrongState = Dossier dans un état incohérent
        UpdateCertificationFoldersAsync:
          type: object
          properties:
            certificationFolders:
              type: array
              description: Liste des dossiers de certification à modifier
              items:
                type: object
                properties:
                  id:
                    type: number
                    description: Id du dossier de certification
                  amountHt:
                    type: float
                    description: Nouveau prix à appliquer
        UpdateCertificationFoldersAsyncResult:
          type: object
          properties:
            result:
              type: array
              description: Résultat des dossiers de certification modifiés dans l'état "success" (réussi) et dans l'état "failed" (échoué)
              items:
                type: object
                properties:
                  success:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: number
                          description: Id du dossier de certification
                        amountHt:
                          type: float
                          description: Nouveau prix appliqué
                  failed:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: number
                          description: Id du dossier de certification
        ServiceDoneRegistrationFoldersAsync:
          type: object
          properties:
            registrationFolders:
              type: array
              description: Liste des dossiers de formation à passer à l'état 'service fait déclaré' (min = 1)
              items:
                type: object
                properties:
                  externalId:
                    type: string
                    description: Id du dossier de formation
                  absenceDuration:
                    type: float
                    description: Optionnel - durée d'absence en heures - aucune absence par défaut
                    example: 1.57
                  forceMajeureAbsence:
                    type: boolean
                    description: Optionnel - indique si l'absence est liée à un cas de force majeure - pas de force majeure par défaut
                  trainingDuration:
                    type: float
                    description: Optionnel - durée de la formation - par défaut l'information est lue depuis le dossier de formation (si l'information est présente)
                  code:
                    type: string
                    description: Optionnel - Code raison de la fin de formation - code 8 par défaut
                  date:
                    type: string
                    format: date
                    description: Optionnel - Date du sortie de formation au format YYYY-MM-DD. Par défaut, date du jour. Si la date a déjà été indiquée au moment du terminate, il n'est pas nécessaire de la repréciser.
        ServiceDoneRegistrationFoldersAsyncResult:
          type: object
          properties:
            result:
              type: array
              description: Résultat des dossiers à passer à 'service fait déclaré'
              items:
                type: object
                properties:
                  externalId:
                    type: string
                    description: Id du dossier de formation
                  state:
                    type: string
                    enum:
                      - scheduledToServiceDone
                      - notScheduledWrongState
                      - notScheduled
                    description: scheduledToServiceDone = Programmé pour le passage à 'service fait déclaré'; notScheduled = Non programmable; notScheduledWrongState = Dossier dans un état incohérent
        BillingRegistrationFoldersAsync:
          type: object
          properties:
            registrationFolders:
              type: array
              description: Liste des dossiers de formation à facturer (min = 1)
              items:
                type: object
                properties:
                  externalId:
                    type: string
                    description: (obligatoire) Id du dossier de formation
                  billNumber:
                    type: string
                    description: (obligatoire) Numéro de facture interne de l'organisme
                  vatRate:
                    type: number
                    enum:
                      - 0
                      - 5.5
                      - 20
                    description: (facultatif) Taux de TVA de l'organisme
        BillingRegistrationFoldersAsyncResult:
          type: object
          properties:
            result:
              type: array
              description: Résultat des dossiers à passer à 'facturé'
              items:
                type: object
                properties:
                  externalId:
                    type: string
                    description: Id du dossier de formation
                  state:
                    type: string
                    enum:
                      - scheduledToBilling
                      - notScheduledWrongState
                      - notScheduled
                    description: scheduledToBilling = Programmé pour le passage à 'facturé'; notScheduled = Non programmable; notScheduledWrongState = Dossier dans un état incohérent
        Subscription:
          type: object
          properties:
            trainingType:
              type: string
              enum:
                - none
                - free
                - trial
                - api
                - essential
                - standard
                - premium
              description: nom de l'offre
            trainingPeriodStartDate:
              type: string
              format: date-time
              description: date de début de la période d'abonnement
            trainingPeriodEndDate:
              type: string
              format: date-time
              description: date de fin de période d'abonnement
            trainingPendingCancellation:
              type: boolean
              description: annulation de l'abonnement en cours
            registrationFoldersNumberCount:
              type: number
              description: nombre de dossiers depuis le début de la période d'abonnement
            registrationFoldersNumberLimit:
              type: number
              description: nombre de dossiers maximum sur la période d'abonnement (null si illimité)
        OrganismUpdateBody:
          type: object
          properties:
            emails:
              type: array
              items:
                type: string
            phones:
              type: array
              items:
                type: string
            urls:
              type: array
              items:
                type: string
            address:
              type: string
            postalCode:
              type: string
            city:
              type: string
            agreement:
              type: string
            vat:
              type: number
              enum:
                - 0
                - 5.5
                - 20
            cdcClientId:
              type: string
              description: Numéro de la fiche client CDC Certificateur (reservé aux certificateurs)
            cdcContractId:
              type: string
              description: Numéro de contrat spécifique CDC Certificateur (reservé aux certificateurs)
            linkedInPageUrl:
              type: string
              description: Page LinkedIn de l'organisme
            customColorScheme:
              type: string
              example: "#4e6ead"
            logo:
              type: string
              format: binary
              description: Logo de l'organisme au format svg
        Organism:
          type: object
          properties:
            name:
              type: string
              description: Acme
            siren:
              type: string
              description: "123456789"
            siret:
              type: string
              description: "12345678901234"
            address:
              type: string
            fullAddress:
              type: string
              description: Acme
            city:
              type: string
            postalCode:
              type: string
            phones:
              type: array
              items:
                type: string
            emails:
              type: array
              items:
                type: string
            lastUpdate:
              type: string
              format: date-time
            agreement:
              type: string
            ape:
              type: string
            urls:
              type: array
              items:
                type: string
            subDomain:
              type: string
            registrationFolderFileTypes:
              type: array
              items:
                type: object
                properties:
                  id:
                    type: number
                  name:
                    type: string
                  accept:
                    type: string
                    description: Format attendu
                    example: ".pdf"
                  toState:
                    type: string
                    description: L'état pour lequel le document est requis / sera généré
                    example: "inTraining"
                  description:
                    type: string
                  allowVisibilityAttendee:
                    type: boolean
                    description: Indique si le fichier est disponible depuis l'Espace Apprenant
                  allowUploadAttendee:
                    type: boolean
                    description: Indique si le fichier peut être transmis par l'apprenant depuis l'Espace Apprenant
                  qualiopiIndicators:
                    type: array
                    description: Tableau des indicateurs Qualiopi
                    items:
                      type: integer
                  generated:
                    type: boolean
                    description: Indique si le fichier est généré automatiquement
                  enabled:
                    type: boolean
                    description: Indique si la génération automatique de ce type de fichier est activé ou désactivé
                  templateFile:
                    type: string
                    description: Nom du modèle de document qui sera généré automatiquement
            linkedInPageUrl:
              type: string
            linkedInOrganisationId:
              type: string
            customColorScheme:
              type: string
            isCertifierOrganism:
              type: boolean
            isTrainingOrganism:
              type: boolean
            isNonDiffusible:
              type: boolean
            logo:
              type: string
            metadata:
              type: array
              items:
                type: string
              description: tableau associatif clé - valeur, disponible uniquement pour le propriétaire
            uaiNumber:
              type: string
              description: Numéro UAI du CFA (applicable uniquement pour les organismes de formation)
            _links:
              type: object
              properties:
                self:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/organisms/*siret*
                payments:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/payments?siret=*siret*
                certifierCertifications:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/certifications?organismType=certifier&siret=*siret*
                partnerCertifications:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/certifications?organismType=partner&siret=*siret*
                trainings:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/trainings?siret=*siret*
                registrationFolders:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/registrationFolders?siret=*siret*
                evaluations:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/evaluations?siret=*siret*
                subscription:
                  type: object
                  properties:
                    href:
                      type: string
                      example: /api/subscription/*subscriptionId*
      securitySchemes:
        accessCode:
          type: apiKey
          name: X-API-KEY
          in: header
  areas:
    path_patterns: # an array of regexps
      - ^/api/registrationFolders
      - ^/api/webhooks
      - ^/api/trainingActions
      - ^/api/payments
      - ^/api/users
      - ^/api/evaluations
      - ^/api/registrationFolderReasons
      - ^/api/certifications
      - ^/api/certifiers
      - ^/api/partners
      - ^/api/certificationFolders
      - ^/api/attendees
      - ^/api/messages
      - ^/api/trainings
      - ^/api/proposals
      - ^/api/activities
      - ^/api/files
      - ^/api/subscriptions
      - ^/api/sessions
      - ^/api/organisms
      - ^/api/surveys
      - ^/api/invoices
      - ^/api/certificationPartnerAuditTemplates
    with_annotation: true
