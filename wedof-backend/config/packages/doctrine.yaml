doctrine:
  dbal:
    url: '%env(resolve:DATABASE_URL)%'

    # IMPORTANT: You MUST configure your server version,
    # either here or in the DATABASE_URL env var (see .env file)
    server_version: '5.7'
    mapping_types:
      enum: string

  orm:
    auto_generate_proxy_classes: true
    naming_strategy: doctrine.orm.naming_strategy.underscore_number_aware
    auto_mapping: true
    mappings:
      App:
        is_bundle: false
        type: annotation
        dir: '%kernel.project_dir%/src/Entity'
        prefix: 'App\Entity'
        alias: App
      MessageTemplate:
        is_bundle: false
        type: annotation
        dir: '%kernel.project_dir%/src/Application/MessageTemplates'
        prefix: 'App\Application\MessageTemplates\Entity'
        alias: App
    dql:
      string_functions:
        HOUR: DoctrineExtensions\Query\Mysql\Hour
        DAY: DoctrineExtensions\Query\Mysql\Day
        WEEK: DoctrineExtensions\Query\Mysql\Week
        MONTH: DoctrineExtensions\Query\Mysql\Month
        YEAR: DoctrineExtensions\Query\Mysql\Year
        CAST: DoctrineExtensions\Query\Mysql\Cast
        STRTODATE: DoctrineExtensions\Query\Mysql\StrToDate
        REPLACE: DoctrineExtensions\Query\Mysql\Replace
        DATE_FORMAT: DoctrineExtensions\Query\Mysql\DateFormat
        JSON_CONTAINS: Scienta\DoctrineJsonFunctions\Query\AST\Functions\Mysql\JsonContains
        JSON_UNQUOTE: Scienta\DoctrineJsonFunctions\Query\AST\Functions\Mysql\JsonUnquote
        JSON_EXTRACT: Scienta\DoctrineJsonFunctions\Query\AST\Functions\Mysql\JsonExtract
        JSON_TYPE: Scienta\DoctrineJsonFunctions\Query\AST\Functions\Mysql\JsonType
        JSON_SET: Scienta\DoctrineJsonFunctions\Query\AST\Functions\Mysql\JsonSet
        JSON_LENGTH: Scienta\DoctrineJsonFunctions\Query\AST\Functions\Mysql\JsonLength