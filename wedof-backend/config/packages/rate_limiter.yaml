framework:
  rate_limiter:
    free_api:
      policy: 'sliding_window'
      limit: 10
      interval: '1 minute'
      cache_pool: rate_limiter.cache
    free_async_api:
      policy: 'sliding_window'
      limit: 0
      interval: '1 minute'
      cache_pool: rate_limiter.cache
    cpf_api:
      policy: 'sliding_window'
      limit: 40
      interval: '1 minute'
      cache_pool: rate_limiter.cache
    customer_api:
      policy: 'sliding_window'
      limit: 100
      interval: '1 minute'
      cache_pool: rate_limiter.cache
    customer_async_api:
      policy: 'sliding_window'
      limit: 1
      interval: '1 minute'
      cache_pool: rate_limiter.cache
    admin_api:
      policy: 'sliding_window'
      limit: 300
      interval: '1 minute'
      cache_pool: rate_limiter.cache



    #implement rateLimiter for externals API
    kairos_aif_api:
      policy: 'fixed_window'
      limit: 50
      interval: '1 minute'
      cache_pool: rate_limiter.cache