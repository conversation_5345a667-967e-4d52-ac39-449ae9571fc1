monolog:
  handlers:
    main:
      type: fingers_crossed
      action_level: error
      handler: nested
      excluded_http_codes: [ 403, 404, 405, 400, 429 ]
      buffer_size: 50 # How many messages should be saved? Prevent memory leaks
      channels: [ "!messenger" ]
    nested:
      type: stream
      path: "%kernel.logs_dir%/%kernel.environment%.log"
    console:
      type: console
      process_psr_3_messages: false
      channels: [ "!event", "!doctrine" ]