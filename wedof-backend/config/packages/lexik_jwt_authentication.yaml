lexik_jwt_authentication:
  secret_key: '%env(PRIVATE_KEY_PATH)%' # required for token creation
  public_key: '%env(PUBLIC_KEY_PATH)%'  # required for token verification
  pass_phrase: '%env(PRIVATE_KEY_PASSPHRASE)%' # required for token creation, usage of an environment variable is recommended
  token_ttl: 14400
  token_extractors:
    # look for a token as Authorization Header
    authorization_header:
      enabled: true
      prefix: Bearer
      name: Authorization-JWT

    # check token in a query parameter
    query_parameter:
      enabled: true
      name: token