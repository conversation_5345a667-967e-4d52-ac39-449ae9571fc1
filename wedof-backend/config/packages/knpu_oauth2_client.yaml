knpu_oauth2_client:
  clients:
    wedof_slack:
      type: generic
      provider_class: App\Application\Slack\SlackWedofApplicationOAuth2Provider
      client_id: '%env(OAUTH_SLACK_CLIENT_ID)%'
      client_secret: '%env(OAUTH_SLACK_CLIENT_SECRET)%'
      redirect_route: oauth2CallbackFake
      redirect_params: { appId: 'slack' }
      use_state: false

    wedof_salesforce:
      type: generic
      provider_class: App\Application\Salesforce\SalesforceWedofApplicationOAuth2Provider
      client_id: '%env(OAUTH_SALESFORCE_CLIENT_ID)%'
      client_secret: '%env(OAUTH_SALESFORCE_CLIENT_SECRET)%'
      redirect_route: oauth2CallbackFake
      redirect_params: { appId: 'salesforce' }
      use_state: false

    wedof_google:
      type: generic
      provider_class: App\Application\Google\GOogleWedofApplicationOAuth2Provider
      client_id: '546954457730-ecv4u39bsgok4onoott6bpq3ddt2ikfm.apps.googleusercontent.com'
      client_secret: 'l13LU8GK1EZJWlzxzXq5DCcI'
      redirect_route: oauth2CallbackFake
      redirect_params: { appId: 'google' }
      use_state: false
