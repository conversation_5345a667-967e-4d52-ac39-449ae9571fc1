<?php

namespace App\Tests\Service;

use App\Entity\Organism;
use App\Entity\User;
use App\Repository\OrganismRepository;
use App\Service\DataProviders\CpfApiService;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class EdofServiceTest extends KernelTestCase
{
    /** @var CpfApiService $cpfApiService */
    private $cpfApiService;
    /** @var Organism $organism */
    private $organism;
    /** @var User $user */
    private $user;


    protected function setUp(): void
    {
        self::bootKernel();
        $container = self::$container;
        /** @var OrganismRepository $organismRepository */
        $organismRepository = static::$container->get(OrganismRepository::class);
        $this->cpfApiService = $container->get(CpfApiService::class);
        $this->organism = $organismRepository->findOneBy(['siret' => '53222292400039']);
        $this->user = $this->organism->getOwnedBy();
    }

    public function testHasCpfAccess()
    {
        $this->assertTrue($this->cpfApiService->hasCpfAccess($this->user));
    }

    public function testGetRegistrationFoldersRawData()
    {
        //todo we should test all states
        $result = $this->cpfApiService->getRegistrationFoldersRawData($this->organism);
        $this->assertTrue(is_array($result));
        $this->assertTrue(sizeof($result) > 0);
        //todo we should test all properties
        $this->assertTrue(isset($result[0]['id']));
    }

    public function testGetRegistrationFolderRawData()
    {
        $results = $this->cpfApiService->getRegistrationFoldersRawData($this->organism);
        $this->assertTrue(is_array($results));
        $this->assertTrue(sizeof($results) > 0);
        $this->assertTrue(isset($results[0]['id']));
        //todo we should test all properties
        $result = $this->cpfApiService->getRegistrationFolderRawData($this->organism, $results[0]['id']);
        $this->assertTrue($result['id'] === $results[0]['id']);
    }

    public function testGetRegistrationFoldersCount()
    {
        //todo we should test all states
        $result = $this->cpfApiService->getRegistrationFoldersCount($this->organism);
        $this->assertTrue(is_integer($result));
    }
}
