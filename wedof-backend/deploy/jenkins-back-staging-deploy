#!/usr/bin/env groovy
pipeline {
    agent any
    environment {
        IP = '***************'
        DIRECTORY = '/home/<USER>/wedof.fr/wedof-frontend'
        DIRECTORY_BACKEND = '/home/<USER>/wedof.fr/wedof-backend'
        FILE_ENV = '/home/<USER>/wedof.fr/wedof-backend/.env.test'
        FILE_INDEX_ANGULAR = '/home/<USER>/wedof.fr/wedof-frontend/dist/index.html'
        FILE_INDEX_SSR = '/home/<USER>/wedof.fr/wedof-backend/templates/funnel/index.html.twig'
    }
    stages {
        stage('deploy') {
            steps {
                // The migration is at the end of a command so that the build fails if migration fails
                sh """ssh root@$IP bash << EOF
                    cd $DIRECTORY_BACKEND
                    chmod 777 $DIRECTORY_BACKEND/var -R
                    su web -c "git fetch origin"
                    su web -c "git reset --hard origin/master"
                    su web -c "composer install"
                    su web -c "php bin/console cache:clear --env=test"
                    su web -c "php bin/console doctrine:migrations:migrate -n --allow-no-migration"
EOF"""// do not INDENT it will fail and you will lost your mind
                sh """ssh root@$IP bash << EOF
                    cd $DIRECTORY_BACKEND
                    sed -i "s/BUILD_ID_PLACEHOLDER/${env.BUILD_ID}/g" $FILE_ENV
                    su web -c "rm -f var/log/test.log"
                    cp $FILE_INDEX_ANGULAR $FILE_INDEX_SSR
                    sed -i "s/Wedof/{{ data.title }}/g" $FILE_INDEX_SSR
                    sed -i "s/<\\/title>/<\\/title>\\n   {\\% include \\'shared\\/_meta.html.twig' \\%}/g" $FILE_INDEX_SSR
                    cd $DIRECTORY_BACKEND
                    su web -c "php bin/console cache:clear --env=test"
                    su web -c "php bin/console messenger:stop-workers"
                    chmod 777 $DIRECTORY_BACKEND/var -R
                    service php7.4-fpm restart
EOF"""// do not INDENT it will fail and you will lost your mind
            }
        }
    }
}

