volumes:
  vendor:
  var:
services:
  php:
    build:
      context: ./docker/php
    depends_on:
      - database
    environment:
      - APP_ENV=dev

    volumes:
      - ./:/var/www/
      - vendor:/var/www/vendor
      - var:/var/www/var
  nginx:
    image: nginx:1.19.0-alpine
    restart: on-failure
    volumes:
      - './:/var/www'
      - './docker/nginx/default.conf:/etc/nginx/conf.d/default.conf:ro'
    ports:
      - '80:80'
    depends_on:
      - php

  database:
    image: mysql:5.7
    ports:
      - 3306:3306
    restart: always
    environment:
      - MYSQL_DATABASE=wedof
      - MYSQL_USER=wedof
      - MYSQL_PASSWORD=root
      - MYSQL_ROOT_PASSWORD=root

