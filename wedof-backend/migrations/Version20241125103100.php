<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241125103100 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE working_contract CHANGE external_id_training_organism external_id_training_organism VARCHAR(100) DEFAULT NULL');
        $this->addSql('CREATE INDEX financerId_idx ON working_contract (external_id_financer, financer)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('DROP INDEX financerId_idx ON working_contract');
        $this->addSql('ALTER TABLE working_contract CHANGE external_id_training_organism external_id_training_organism VARCHAR(100) CHARACTER SET utf8mb4 NOT NULL COLLATE utf8mb4_unicode_ci');
    }
}
