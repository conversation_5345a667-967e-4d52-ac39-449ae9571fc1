<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20231128180312 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('UPDATE user
JOIN organism on user.main_organism_id = organism.id
SET user.filters = CONCAT(\'{"\', organism.siret, \'":\', \'[
  {
      "link": "filterOnStateDate=stateLastUpdate&cdcState=processedKo",
    "name": "Accrochés avec erreur",
    "color": "#8cbdc1",
    "entityClass": "CertificationFolder"
  },
  {
      "link": "filterOnStateDate=lastUpdate&controlState=inControl",
    "name": "En contrôle",
    "color": "#ff0000",
    "entityClass": "RegistrationFolder"
  },
  {
      "link": "billingState=billed&filterOnStateDate=lastUpdate&controlState=",
    "name": "Facturés",
    "color": "#e2d7d0",
    "entityClass": "RegistrationFolder"
  },
  {
      "link": "billingState=paid&filterOnStateDate=lastUpdate",
    "name": "Payés",
    "color": "#f9f8eb",
    "entityClass": "RegistrationFolder"
  }
]\', \'}\')
WHERE user.filters IS NULL;');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');
    }
}
