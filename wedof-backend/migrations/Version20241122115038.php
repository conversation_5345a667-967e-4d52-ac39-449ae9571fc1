<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241122115038 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('CREATE TABLE working_contract (id INT AUTO_INCREMENT NOT NULL, registration_folder_id INT DEFAULT NULL, employer_id INT DEFAULT NULL, external_id_financer VARCHAR(100) NOT NULL, external_id_training_organism VARCHAR(100) NOT NULL, external_id_deca VARCHAR(100) DEFAULT NULL, financer VARCHAR(100) NOT NULL, state VARCHAR(100) NOT NULL, type VARCHAR(3) NOT NULL, amount DOUBLE PRECISION DEFAULT NULL, start_date DATE NOT NULL, end_date DATE NOT NULL, signed_date DATE NOT NULL, amendment_date DATE DEFAULT NULL, breaking_date DATE DEFAULT NULL, raw_data JSON NOT NULL, created_on DATETIME DEFAULT NULL, updated_on DATETIME DEFAULT NULL, UNIQUE INDEX UNIQ_9F8D196BF5704727 (registration_folder_id), INDEX IDX_9F8D196B41CD9E7A (employer_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE = InnoDB');
        $this->addSql('ALTER TABLE working_contract ADD CONSTRAINT FK_9F8D196BF5704727 FOREIGN KEY (registration_folder_id) REFERENCES registration_folder (id)');
        $this->addSql('ALTER TABLE working_contract ADD CONSTRAINT FK_9F8D196B41CD9E7A FOREIGN KEY (employer_id) REFERENCES organism (id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('DROP TABLE working_contract');
    }
}
