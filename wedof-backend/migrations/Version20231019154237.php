<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20231019154237 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE subscription DROP allow_automatic_cdc_files, DROP allow_generate_certificate, DROP allow_display_trainings_and_training_actions, CHANGE allow_manage_partnership allow_certifier_plus TINYINT(1) DEFAULT \'0\' NOT NULL');
    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE subscription ADD allow_automatic_cdc_files TINYINT(1) NOT NULL, ADD allow_generate_certificate TINYINT(1) NOT NULL, ADD allow_display_trainings_and_training_actions TINYINT(1) NOT NULL, CHANGE allow_certifier_plus allow_manage_partnership TINYINT(1) DEFAULT \'0\' NOT NULL');
    }
}
