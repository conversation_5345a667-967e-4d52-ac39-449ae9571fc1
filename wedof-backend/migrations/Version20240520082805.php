<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use App\Library\utils\Tools;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240520082805 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        if (Tools::isEnvIn(['dev'])) {
            $this->addSql('ALTER TABLE certification_folder DROP FOREIGN KEY FK_A8A107A1E058452');
            $this->addSql('ALTER TABLE certification_folder ADD CONSTRAINT FK_A8A107A1E058452 FOREIGN KEY (history_id) REFERENCES certification_folder_history (id) ON DELETE CASCADE');
        }
    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        if (Tools::isEnvIn(['dev'])) {
            $this->addSql('ALTER TABLE certification_folder DROP FOREIGN KEY FK_A8A107A1E058452');
            $this->addSql('ALTER TABLE certification_folder ADD CONSTRAINT FK_A8A107A1E058452 FOREIGN KEY (history_id) REFERENCES certification_folder_history (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        }
    }
}
