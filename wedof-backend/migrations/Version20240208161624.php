<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240208161624 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE certifications_partners ADD certifier_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE certifications_partners ADD CONSTRAINT FK_FAEE1ECB5BB49544 FOREIGN KEY (certifier_id) REFERENCES organism (id)');
        $this->addSql('CREATE INDEX IDX_FAEE1ECB5BB49544 ON certifications_partners (certifier_id)');
        $this->addSql('UPDATE certifications_partners
            JOIN certification ON certifications_partners.certification_id = certification.id
            SET certifications_partners.certifier_id = certification.default_certifier_id');
    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE certifications_partners DROP FOREIGN KEY FK_FAEE1ECB5BB49544');
        $this->addSql('DROP INDEX IDX_FAEE1ECB5BB49544 ON certifications_partners');
        $this->addSql('ALTER TABLE certifications_partners DROP certifier_id');
    }
}
