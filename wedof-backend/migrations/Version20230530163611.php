<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230530163611 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('UPDATE certification_folder 
JOIN certification_folders_cdc_files ON certification_folders_cdc_files.certification_folder_id = certification_folder.id
SET cdc_state = "exported" 
WHERE certification_folder.state = "success"
AND certification_folders_cdc_files.state != "aborted"');

        $this->addSql('UPDATE certification_folder 
JOIN certification_folders_cdc_files ON certification_folders_cdc_files.certification_folder_id = certification_folder.id
SET cdc_state = "processedKo" 
WHERE certification_folder.state = "success"
AND certification_folders_cdc_files.state = "processedKo"');

        $this->addSql('UPDATE certification_folder 
JOIN certification_folders_cdc_files ON certification_folders_cdc_files.certification_folder_id = certification_folder.id
SET cdc_state = "processedOk" 
WHERE certification_folder.state = "success"
AND certification_folders_cdc_files.state = "processedOk"');

    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');
    }
}
