<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20231027155650 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('CREATE TABLE message_template (id INT AUTO_INCREMENT NOT NULL, organism_id INT NOT NULL, title VARCHAR(255) NOT NULL, object VARCHAR(255) DEFAULT NULL, type VARCHAR(40) NOT NULL, state VARCHAR(40) NOT NULL, body LONGTEXT NOT NULL, reply_to VARCHAR(255) NOT NULL, entity_class VARCHAR(40) NOT NULL, events LONGTEXT NOT NULL COMMENT \'(DC2Type:array)\', created_on DATETIME DEFAULT NULL, updated_on DATETIME DEFAULT NULL, INDEX IDX_9E46DB9264180A36 (organism_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE message_template_certification (message_template_id INT NOT NULL, certification_id INT NOT NULL, INDEX IDX_DDD55FFF65A55141 (message_template_id), INDEX IDX_DDD55FFFCB47068A (certification_id), PRIMARY KEY(message_template_id, certification_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE message_template_tag (message_template_id INT NOT NULL, tag_id INT NOT NULL, INDEX IDX_69E30B4765A55141 (message_template_id), INDEX IDX_69E30B47BAD26311 (tag_id), PRIMARY KEY(message_template_id, tag_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE message_template ADD CONSTRAINT FK_9E46DB9264180A36 FOREIGN KEY (organism_id) REFERENCES organism (id)');
        $this->addSql('ALTER TABLE message_template_certification ADD CONSTRAINT FK_DDD55FFF65A55141 FOREIGN KEY (message_template_id) REFERENCES message_template (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE message_template_certification ADD CONSTRAINT FK_DDD55FFFCB47068A FOREIGN KEY (certification_id) REFERENCES certification (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE message_template_tag ADD CONSTRAINT FK_69E30B4765A55141 FOREIGN KEY (message_template_id) REFERENCES message_template (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE message_template_tag ADD CONSTRAINT FK_69E30B47BAD26311 FOREIGN KEY (tag_id) REFERENCES tag (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE user_user_message RENAME INDEX idx_eeb02e75a76ed395 TO IDX_6E7C5277A76ED395');
        $this->addSql('ALTER TABLE user_user_message RENAME INDEX idx_eeb02e75537a1329 TO IDX_6E7C5277F41DD5C5');
    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE message_template_certification DROP FOREIGN KEY FK_DDD55FFF65A55141');
        $this->addSql('ALTER TABLE message_template_tag DROP FOREIGN KEY FK_69E30B4765A55141');
        $this->addSql('DROP TABLE message_template');
        $this->addSql('DROP TABLE message_template_certification');
        $this->addSql('DROP TABLE message_template_tag');
        $this->addSql('ALTER TABLE user_user_message RENAME INDEX idx_6e7c5277f41dd5c5 TO IDX_EEB02E75537A1329');
        $this->addSql('ALTER TABLE user_user_message RENAME INDEX idx_6e7c5277a76ed395 TO IDX_EEB02E75A76ED395');
    }
}
