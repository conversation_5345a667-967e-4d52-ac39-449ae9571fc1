<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241016073038 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('CREATE TABLE certification_partner_audit_file (id INT AUTO_INCREMENT NOT NULL, certification_partner_audit_id INT NOT NULL, type_id INT NOT NULL, file_name VARCHAR(255) NOT NULL, file_path VARCHAR(255) NOT NULL, link VARCHAR(255) DEFAULT NULL, file_type VARCHAR(255) NOT NULL, state VARCHAR(255) NOT NULL, comment LONGTEXT DEFAULT NULL, generation_state VARCHAR(30) DEFAULT \'notGenerated\' NOT NULL, signed_state VARCHAR(30) DEFAULT \'notRequired\' NOT NULL, created_on DATETIME DEFAULT NULL, updated_on DATETIME DEFAULT NULL, UNIQUE INDEX UNIQ_70A52A92F842FB47 (certification_partner_audit_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE certification_partner_audit_file ADD CONSTRAINT FK_70A52A92F842FB47 FOREIGN KEY (certification_partner_audit_id) REFERENCES certification_partner_audit (id)');
        $this->addSql('ALTER TABLE certification_partner_audit DROP FOREIGN KEY FK_428A0B404BD2A4C0');
        $this->addSql('ALTER TABLE certification_partner_audit ADD CONSTRAINT FK_428A0B404BD2A4C0 FOREIGN KEY (report_id) REFERENCES certification_partner_audit_file (id)');
    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE certification_partner_audit DROP FOREIGN KEY FK_428A0B404BD2A4C0');
        $this->addSql('DROP TABLE certification_partner_audit_file');
        $this->addSql('ALTER TABLE certification_partner_audit ADD CONSTRAINT FK_428A0B404BD2A4C0 FOREIGN KEY (report_id) REFERENCES certification_partner_file (id)');
    }
}
