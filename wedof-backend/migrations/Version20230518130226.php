<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230518130226 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE registration_folder ADD control_state VARCHAR(20) DEFAULT NULL');
        $this->addSql('UPDATE registration_folder
SET registration_folder.control_state = CASE
    WHEN JSON_EXTRACT(registration_folder.raw_data, \'$.etatBlocage\') IS NULL THEN \'notBlocked\'
    WHEN JSON_EXTRACT(registration_folder.raw_data, \'$.etatBlocage\') = \'NON_BLOQUE\' THEN \'notBlocked\'
    WHEN JSON_EXTRACT(registration_folder.raw_data, \'$.etatBlocage\') = \'BLOQUE\' THEN \'blocked\'
    WHEN JSON_EXTRACT(registration_folder.raw_data, \'$.etatBlocage\') = \'DEBLOQUE\' THEN \'blocked\'
END
WHERE true');
        $this->addSql('ALTER TABLE registration_folder MODIFY control_state VARCHAR(255) NOT NULL');
    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE registration_folder DROP control_state');
    }
}
