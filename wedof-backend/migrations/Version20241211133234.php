<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241211133234 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('CREATE TABLE certification_partner_tag (certification_partner_id INT NOT NULL, tag_id INT NOT NULL, INDEX IDX_83471CD014F5FFC5 (certification_partner_id), INDEX IDX_83471CD0BAD26311 (tag_id), PRIMARY KEY(certification_partner_id, tag_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE certification_partner_tag ADD CONSTRAINT FK_83471CD014F5FFC5 FOREIGN KEY (certification_partner_id) REFERENCES certifications_partners (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE certification_partner_tag ADD CONSTRAINT FK_83471CD0BAD26311 FOREIGN KEY (tag_id) REFERENCES tag (id) ON DELETE CASCADE');
    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('DROP TABLE certification_partner_tag');
    }
}
