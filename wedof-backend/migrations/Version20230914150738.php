<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230914150738 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE registration_folder RENAME INDEX idx_42fcc927dad4f72 TO IDX_42FCC927F1A2B364');
        $this->addSql('ALTER TABLE registration_folder CHANGE absence_duration absence_duration DOUBLE PRECISION DEFAULT NULL');
    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE registration_folder RENAME INDEX idx_42fcc927f1a2b364 TO IDX_42FCC927DAD4F72');
        $this->addSql('ALTER TABLE registration_folder CHANGE absence_duration absence_duration SMALLINT DEFAULT NULL');
    }
}
