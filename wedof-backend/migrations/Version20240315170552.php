<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240315170552 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');
        $this->addSql("UPDATE subscription SET training_trial_end_date = training_start_date WHERE training_type = 'free' AND training_trial_end_date IS NULL");
        $this->addSql("UPDATE subscription SET training_trial_end_date = training_period_end_date WHERE training_type = 'trial' AND training_trial_end_date IS NULL");
        $this->addSql("UPDATE subscription SET certifier_trial_end_date = certifier_start_date WHERE certifier_type = 'free' AND certifier_trial_end_date IS NULL");
        $this->addSql("UPDATE subscription SET certifier_trial_end_date = certifier_period_end_date WHERE certifier_type = 'trial' AND certifier_trial_end_date IS NULL");
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');
    }
}
