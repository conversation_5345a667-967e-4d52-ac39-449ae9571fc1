<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240726143910 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE certification_folder_survey CHANGE state state VARCHAR(255) DEFAULT \'created\' NOT NULL');
        $this->addSql('UPDATE certification_folder_survey SET state = "created" where state = "notStarted"');
        $this->addSql('UPDATE certification_folder_survey SET state = "beforeCertificationSuccess" where state = "started"');
        $this->addSql('UPDATE certification_folder_survey SET state = "afterSixMonthsCertificationSuccess" where state = "inProgress"');
    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE certification_folder_survey CHANGE state state VARCHAR(255) CHARACTER SET utf8mb4 DEFAULT \'notStarted\' NOT NULL COLLATE `utf8mb4_unicode_ci`');
    }
}
