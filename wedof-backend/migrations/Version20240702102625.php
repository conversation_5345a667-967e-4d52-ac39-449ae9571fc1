<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240702102625 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE attendee_experience ADD qualification INT DEFAULT NULL, ADD certification_name VARCHAR(255) DEFAULT NULL, ADD job VARCHAR(255) DEFAULT NULL, ADD experience_years INT DEFAULT NULL, ADD company_name VARCHAR(255) DEFAULT NULL, ADD salary_yearly INT DEFAULT NULL, ADD situation VARCHAR(255) DEFAULT NULL, ADD contract_type VARCHAR(255) DEFAULT NULL, ADD executive_status TINYINT(1) DEFAULT NULL');
    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE attendee_experience DROP qualification, DROP certification_name, DROP job, DROP experience_years, DROP company_name, DROP salary_yearly, DROP situation, DROP contract_type, DROP executive_status');
    }
}
