<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240801115205 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE certification_folder ADD metadata JSON DEFAULT NULL');
        $this->addSql('ALTER TABLE organism ADD metadata JSON DEFAULT NULL');
        $this->addSql('ALTER TABLE proposal ADD metadata JSON DEFAULT NULL');
        $this->addSql('ALTER TABLE registration_folder ADD metadata JSON DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE certification_folder DROP metadata');
        $this->addSql('ALTER TABLE organism DROP metadata');
        $this->addSql('ALTER TABLE proposal DROP metadata');
        $this->addSql('ALTER TABLE registration_folder DROP metadata');
    }
}
