<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241212151624 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('DROP TABLE certifications_fraudsters');
    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('CREATE TABLE certifications_fraudsters (certification_id INT NOT NULL, organism_id INT NOT NULL, INDEX IDX_DF98933864180A36 (organism_id), INDEX IDX_DF989338CB47068A (certification_id), PRIMARY KEY(certification_id, organism_id)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('ALTER TABLE certifications_fraudsters ADD CONSTRAINT FK_DF98933864180A36 FOREIGN KEY (organism_id) REFERENCES organism (id) ON UPDATE NO ACTION ON DELETE CASCADE');
        $this->addSql('ALTER TABLE certifications_fraudsters ADD CONSTRAINT FK_DF989338CB47068A FOREIGN KEY (certification_id) REFERENCES certification (id) ON UPDATE NO ACTION ON DELETE CASCADE');
    }
}
