<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241125143950 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE subscription DROP free_audits_number_count, DROP pro_audits_number_count, DROP expert_audits_number_count, DROP ia_audits_number_count, DROP audit_number_period_start_date, DROP audit_number_period_end_date');
    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE subscription ADD free_audits_number_count INT DEFAULT 0 NOT NULL, ADD pro_audits_number_count INT DEFAULT 0 NOT NULL, ADD expert_audits_number_count INT DEFAULT 0 NOT NULL, ADD ia_audits_number_count INT DEFAULT 0 NOT NULL, ADD audit_number_period_start_date DATE DEFAULT NULL, ADD audit_number_period_end_date DATE DEFAULT NULL');
    }
}
