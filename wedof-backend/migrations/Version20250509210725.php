<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250509210725 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');
        $this->addSql('ALTER TABLE subscription DROP workflow_end_date, DROP signature_state, DROP signature_end_date, DROP workflow_state');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');
        $this->addSql('ALTER TABLE subscription ADD workflow_end_date DATE DEFAULT NULL, ADD signature_state VARCHAR(255) CHARACTER SET utf8mb4 DEFAULT \'pending_enable_trial\' NOT NULL COLLATE `utf8mb4_unicode_ci`, ADD signature_end_date DATE DEFAULT NULL, ADD workflow_state VARCHAR(255) CHARACTER SET utf8mb4 DEFAULT \'pending_enable_trial\' NOT NULL COLLATE `utf8mb4_unicode_ci`');
    }
}
