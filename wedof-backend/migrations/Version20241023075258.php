<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241023075258 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE certification_partner_audit ADD pricing VARCHAR(255) NOT NULL');
        $this->addSql('ALTER TABLE certification_partner_audit_template ADD certifier_id INT NOT NULL, ADD pricing VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE certification_partner_audit_template ADD CONSTRAINT FK_54AE5B695BB49544 FOREIGN KEY (certifier_id) REFERENCES organism (id)');
        $this->addSql('CREATE INDEX IDX_54AE5B695BB49544 ON certification_partner_audit_template (certifier_id)');
        $this->addSql('ALTER TABLE subscription ADD free_audits_number_count INT DEFAULT 0 NOT NULL, ADD pro_audits_number_count INT DEFAULT 0 NOT NULL, ADD expert_audits_number_count INT DEFAULT 0 NOT NULL, ADD ia_audits_number_count INT DEFAULT 0 NOT NULL, ADD audit_number_period_start_date DATE DEFAULT NULL, ADD audit_number_period_end_date DATE DEFAULT NULL');
    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE certification_partner_audit DROP pricing');
        $this->addSql('ALTER TABLE certification_partner_audit_template DROP FOREIGN KEY FK_54AE5B695BB49544');
        $this->addSql('DROP INDEX IDX_54AE5B695BB49544 ON certification_partner_audit_template');
        $this->addSql('ALTER TABLE certification_partner_audit_template DROP certifier_id, DROP pricing');
        $this->addSql('ALTER TABLE subscription DROP free_audits_number_count, DROP pro_audits_number_count, DROP expert_audits_number_count, DROP ia_audits_number_count, DROP audit_number_period_start_date, DROP audit_number_period_end_date');
    }
}
