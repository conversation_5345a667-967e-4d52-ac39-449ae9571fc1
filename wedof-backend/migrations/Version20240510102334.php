<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240510102334 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('DROP INDEX UNIQ_5E3DE47777153098 ON skill');
        $this->addSql('ALTER TABLE skill ADD parent_skill_id INT DEFAULT NULL, ADD type VARCHAR(255) DEFAULT \'skill\' NOT NULL, CHANGE code `order` VARCHAR(25) NOT NULL');
        $this->addSql('ALTER TABLE skill ADD CONSTRAINT FK_5E3DE477CA486D93 FOREIGN KEY (parent_skill_id) REFERENCES skill (id)');
        $this->addSql('CREATE INDEX IDX_5E3DE477CA486D93 ON skill (parent_skill_id)');
    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE skill DROP FOREIGN KEY FK_5E3DE477CA486D93');
        $this->addSql('DROP INDEX IDX_5E3DE477CA486D93 ON skill');
        $this->addSql('ALTER TABLE skill DROP parent_skill_id, DROP type, CHANGE `order` code VARCHAR(25) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_5E3DE47777153098 ON skill (code)');
    }
}
