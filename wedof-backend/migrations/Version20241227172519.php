<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241227172519 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE subscription CHANGE certification_folder_number_period_start_date certification_folders_number_period_start_date DATE DEFAULT NULL');
        $this->addSql('ALTER TABLE subscription CHANGE certification_folder_number_period_end_date certification_folders_number_period_end_date DATE DEFAULT NULL');
    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE subscription CHANGE certification_folders_number_period_start_date certification_folder_number_period_start_date DATE DEFAULT NULL');
        $this->addSql('ALTER TABLE subscription CHANGE certification_folders_number_period_end_date certification_folder_number_period_end_date DATE DEFAULT NULL');
    }
}
