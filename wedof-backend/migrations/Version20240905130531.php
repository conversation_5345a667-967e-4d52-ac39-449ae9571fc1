<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240905130531 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE subscription CHANGE sms_sent_number_period_start_date sms_sent_number_period_start_date DATE DEFAULT NULL, CHANGE sms_sent_number_period_end_date sms_sent_number_period_end_date DATE DEFAULT NULL');
        $this->addSql('ALTER TABLE subscription ADD allow_paid_usage TINYINT(1) DEFAULT \'0\' NOT NULL');
        $this->addSql('UPDATE subscription
            SET subscription.allow_paid_usage = 1
            WHERE subscription.certifier_type in (\'usage\', \'unlimited\', \'access\')
               OR subscription.training_type in (\'api\', \'essential\', \'access\', \'standard\', \'premium\')');
    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE subscription CHANGE sms_sent_number_period_start_date sms_sent_number_period_start_date DATETIME DEFAULT NULL, CHANGE sms_sent_number_period_end_date sms_sent_number_period_end_date DATETIME DEFAULT NULL');
        $this->addSql('ALTER TABLE subscription DROP allow_paid_usage');
    }
}
