<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230731121552 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');
        $this->addSql('ALTER TABLE proposal CHANGE logo logo_name VARCHAR(500) DEFAULT NULL, CHANGE color custom_color_scheme VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE user ADD last_login DATETIME DEFAULT NULL');
        $this->addSql('ALTER TABLE proposal ADD sales_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE proposal ADD CONSTRAINT FK_BFE59472A4522A07 FOREIGN KEY (sales_id) REFERENCES user (id)');
        $this->addSql('CREATE INDEX IDX_BFE59472A4522A07 ON proposal (sales_id)');

    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');
        $this->addSql('ALTER TABLE proposal DROP FOREIGN KEY FK_BFE59472A4522A07');
        $this->addSql('DROP INDEX IDX_BFE59472A4522A07 ON proposal');
        $this->addSql('ALTER TABLE proposal DROP sales_id, CHANGE logo_name logo_name VARCHAR(500) CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_ci`');

        $this->addSql('ALTER TABLE proposal ADD logo VARCHAR(500) CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_ci`, ADD color VARCHAR(255) CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_ci`, DROP custom_color_scheme, DROP logo_name');
        $this->addSql('ALTER TABLE user DROP last_login');
    }
}
