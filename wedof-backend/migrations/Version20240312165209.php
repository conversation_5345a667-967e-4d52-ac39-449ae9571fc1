<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240312165209 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');
        $this->addSql('ALTER TABLE organism DROP reseller_id');
        $this->addSql('ALTER TABLE organism ADD reseller_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE organism ADD CONSTRAINT FK_D538A2C91E6A19D FOREIGN KEY (reseller_id) REFERENCES organism (id)');
        $this->addSql('CREATE INDEX IDX_D538A2C91E6A19D ON organism (reseller_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE organism DROP FOREIGN KEY FK_D538A2C91E6A19D');
        $this->addSql('DROP INDEX IDX_D538A2C91E6A19D ON organism');
        $this->addSql('ALTER TABLE organism DROP reseller_id');
    }
}
