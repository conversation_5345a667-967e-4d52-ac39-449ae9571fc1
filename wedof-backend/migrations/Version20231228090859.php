<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20231228090859 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE certification_folder ADD registration_folder_external_id VARCHAR(255) DEFAULT NULL, ADD data_provider VARCHAR(50) DEFAULT NULL');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_A8A107A8D91025A ON certification_folder (registration_folder_external_id)');
        $this->addSql('UPDATE certification_folder JOIN registration_folder ON certification_folder.registration_folder_id = registration_folder.id
            SET registration_folder_external_id = registration_folder.external_id,
                data_provider                   = registration_folder.type
            WHERE true');
    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('DROP INDEX UNIQ_A8A107A8D91025A ON certification_folder');
        $this->addSql('ALTER TABLE certification_folder DROP registration_folder_external_id, DROP data_provider');
    }
}
