<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241129134712 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE registration_folder ADD current_working_contract_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE registration_folder ADD CONSTRAINT FK_42FCC927506CB504 FOREIGN KEY (current_working_contract_id) REFERENCES working_contract (id)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_42FCC927506CB504 ON registration_folder (current_working_contract_id)');
    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE registration_folder DROP FOREIGN KEY FK_42FCC927506CB504');
        $this->addSql('DROP INDEX UNIQ_42FCC927506CB504 ON registration_folder');
        $this->addSql('ALTER TABLE registration_folder DROP current_working_contract_id');
    }
}
