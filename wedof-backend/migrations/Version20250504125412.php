<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250504125412 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');
        $this->addSql('ALTER TABLE subscription ADD workflow_state VARCHAR(255) DEFAULT \'pending_enable_trial\' NOT NULL, DROP activepieces_state, CHANGE signature_state signature_state VARCHAR(255) DEFAULT \'pending_enable_trial\' NOT NULL, CHANGE activepieces_end_date workflow_end_date DATE DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');
        $this->addSql('ALTER TABLE subscription ADD activepieces_state VARCHAR(255) CHARACTER SET utf8mb4 DEFAULT \'disabled\' NOT NULL COLLATE `utf8mb4_unicode_ci`, DROP workflow_state, CHANGE signature_state signature_state VARCHAR(255) CHARACTER SET utf8mb4 DEFAULT \'disabled\' NOT NULL COLLATE `utf8mb4_unicode_ci`, CHANGE workflow_end_date activepieces_end_date DATE DEFAULT NULL');
    }
}
