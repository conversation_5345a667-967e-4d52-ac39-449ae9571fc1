<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230525094702 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE registration_folder_history ADD in_control_date DATETIME DEFAULT NULL, ADD released_date DATETIME DEFAULT NULL');
        $this->addSql('UPDATE registration_folder
SET registration_folder.control_state = CASE
    WHEN registration_folder.control_state = \'notBlocked\' THEN \'notInControl\'
    WHEN registration_folder.control_state = \'blocked\' THEN \'inControl\'
    WHEN JSON_EXTRACT(registration_folder.raw_data, \'$.etatBlocage\') = \'DEBLOQUE\' THEN \'released\' /*fix bug from previous migration*/
    ELSE \'notInControl\'
END
WHERE true');
    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE registration_folder_history DROP in_control_date, DROP released_date');
    }
}
