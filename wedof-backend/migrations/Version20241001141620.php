<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241001141620 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('CREATE INDEX external_idx ON session (external_id)');
        $this->addSql('CREATE INDEX sState_idx ON session (state)');
        $this->addSql('CREATE INDEX tState_idx ON training (state)');
        $this->addSql('CREATE INDEX taState_idx ON training_action (state)');
    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('DROP INDEX external_idx ON session');
        $this->addSql('DROP INDEX sState_idx ON session');
        $this->addSql('DROP INDEX tState_idx ON training');
        $this->addSql('DROP INDEX taState_idx ON training_action');
    }
}
