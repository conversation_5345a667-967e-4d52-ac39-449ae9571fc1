<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241217142957 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE certification ADD data_provider VARCHAR(50) DEFAULT NULL');
        $this->addSql('UPDATE certification
            SET certification.data_provider = \'franceCompetences\'
            WHERE certification.type IN (\'RS\', \'RNCP\')');
        $this->addSql('UPDATE certification
            SET certification.data_provider = \'cpf\'
            WHERE certification.type = \'CPF\'');
        $this->addSql('UPDATE certification
            SET certification.data_provider = \'internal\'
            WHERE certification.type = \'CERT\'');
    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE certification DROP data_provider');
    }
}
