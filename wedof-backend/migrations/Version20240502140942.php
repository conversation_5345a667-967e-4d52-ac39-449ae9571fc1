<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240502140942 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('INSERT INTO certification_folder_file (certification_folder_id, file_path, updated_on, file_name, file_type, state, type_id, created_on, generation_state)
            SELECT certification_folder.id, certificate_name, state_last_update, CONCAT(\'parchemin-\', certification.type, certification_folder.external_id, \'.pdf\'), \'application/pdf\', \'valid\', 1001, state_last_update, certificate_generation_state
                FROM certification_folder
                JOIN certification ON certification_folder.certification_id = certification.id
                    WHERE certificate_name IS NOT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

    }
}
