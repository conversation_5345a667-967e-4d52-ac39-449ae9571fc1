<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240816072229 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE certification_folder_file ADD signed_state VARCHAR(30) DEFAULT \'notRequired\' NOT NULL');
        $this->addSql('ALTER TABLE certification_partner_file ADD signed_state VARCHAR(30) DEFAULT \'notRequired\' NOT NULL');
        $this->addSql('ALTER TABLE registration_folder_file ADD signed_state VARCHAR(30) DEFAULT \'notRequired\' NOT NULL');
    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE certification_folder_file DROP signed_state');
        $this->addSql('ALTER TABLE certification_partner_file DROP signed_state');
        $this->addSql('ALTER TABLE registration_folder_file DROP signed_state');
    }
}
