<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240701151725 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('CREATE TABLE attendee_experience (id INT AUTO_INCREMENT NOT NULL, attendee_id INT NOT NULL, INDEX IDX_644F860EBCFD782A (attendee_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE certification_folder_survey (id INT AUTO_INCREMENT NOT NULL, certification_folder_id INT NOT NULL, initial_experience_id INT DEFAULT NULL, six_month_experience_id INT DEFAULT NULL, long_term_experience_id INT DEFAULT NULL, state VARCHAR(20) NOT NULL, initial_experience_date DATE DEFAULT NULL, six_month_experience_date DATE DEFAULT NULL, long_terme_experience_date DATE DEFAULT NULL, UNIQUE INDEX UNIQ_8B49B9D6788850D3 (certification_folder_id), INDEX IDX_8B49B9D66345CC93 (initial_experience_id), INDEX IDX_8B49B9D68B0CFD6F (six_month_experience_id), INDEX IDX_8B49B9D6132D6771 (long_term_experience_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE attendee_experience ADD CONSTRAINT FK_644F860EBCFD782A FOREIGN KEY (attendee_id) REFERENCES attendee (id)');
        $this->addSql('ALTER TABLE certification_folder_survey ADD CONSTRAINT FK_8B49B9D6788850D3 FOREIGN KEY (certification_folder_id) REFERENCES certification_folder (id)');
        $this->addSql('ALTER TABLE certification_folder_survey ADD CONSTRAINT FK_8B49B9D66345CC93 FOREIGN KEY (initial_experience_id) REFERENCES attendee_experience (id)');
        $this->addSql('ALTER TABLE certification_folder_survey ADD CONSTRAINT FK_8B49B9D68B0CFD6F FOREIGN KEY (six_month_experience_id) REFERENCES attendee_experience (id)');
        $this->addSql('ALTER TABLE certification_folder_survey ADD CONSTRAINT FK_8B49B9D6132D6771 FOREIGN KEY (long_term_experience_id) REFERENCES attendee_experience (id)');
    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE certification_folder_survey DROP FOREIGN KEY FK_8B49B9D66345CC93');
        $this->addSql('ALTER TABLE certification_folder_survey DROP FOREIGN KEY FK_8B49B9D68B0CFD6F');
        $this->addSql('ALTER TABLE certification_folder_survey DROP FOREIGN KEY FK_8B49B9D6132D6771');
        $this->addSql('DROP TABLE attendee_experience');
        $this->addSql('DROP TABLE certification_folder_survey');
    }
}
