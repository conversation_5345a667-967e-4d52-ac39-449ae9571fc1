<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230913093316 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');
        $this->addSql('ALTER TABLE certification_folder ADD external_id VARCHAR(255) DEFAULT NULL');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_A8A107A9F75D7B0 ON certification_folder (external_id)');
        $this->addSql('UPDATE certification_folder as cfs 
                            SET external_id = ( 
                                SELECT concat(cert.code,"-", crc32(concat(cf.partner_id, cf.id))) 
                                FROM (SELECT * FROM certification_folder) AS cf 
                                JOIN certification AS cert ON cf.certification_id = cert.id 
                                WHERE cf.id = cfs.id
                            ) 
                            WHERE cfs.external_id IS NULL;');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');
        $this->addSql('DROP INDEX UNIQ_A8A107A9F75D7B0 ON certification_folder');
    }
}