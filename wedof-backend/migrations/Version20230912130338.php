<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230912130338 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE registration_folder ADD terminated_reason_id INT DEFAULT NULL, ADD absence_duration SMALLINT DEFAULT NULL');
        $this->addSql('ALTER TABLE registration_folder ADD CONSTRAINT FK_42FCC927DAD4F72 FOREIGN KEY (terminated_reason_id) REFERENCES registration_folder_reason (id)');
        $this->addSql('CREATE INDEX IDX_42FCC927DAD4F72 ON registration_folder (terminated_reason_id)');
    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE registration_folder DROP FOREIGN KEY FK_42FCC927DAD4F72');
        $this->addSql('DROP INDEX IDX_42FCC927DAD4F72 ON registration_folder');
        $this->addSql('ALTER TABLE registration_folder DROP terminated_reason_id, DROP absence_duration');
    }
}
