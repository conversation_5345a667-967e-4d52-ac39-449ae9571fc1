<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240411100925 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE organism ADD training_organism TINYINT(1) DEFAULT \'0\' NOT NULL');
        $this->addSql('UPDATE organism SET training_organism = 1 WHERE agreement IS NOT NULL');
        $this->addSql('UPDATE organism JOIN training on training.organism_id = organism.id SET training_organism = 1 WHERE training_organism = 0');
        $this->addSql('UPDATE organism JOIN certifications_partners on certifications_partners.partner_id = organism.id SET training_organism = 1 WHERE training_organism = 0 AND certifications_partners.habilitation in ("train","train_evaluate")');

    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE organism DROP training_organism');
    }
}
