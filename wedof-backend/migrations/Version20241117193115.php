<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241117193115 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE organism CHANGE qualiopi_training_formation qualiopi_training_action TINYINT(1) DEFAULT \'0\' NOT NULL, CHANGE qualiopi_bilan_competence qualiopi_bilan_competences TINYINT(1) DEFAULT \'0\' NOT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE organism CHANGE qualiopi_training_action qualiopi_training_formation TINYINT(1) DEFAULT \'0\' NOT NULL, CHANGE qualiopi_bilan_competences qualiopi_bilan_competence  TINYINT(1) DEFAULT \'0\' NOT NULL');
    }
}
