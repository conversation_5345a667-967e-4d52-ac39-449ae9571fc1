<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use App\Service\OrganismService;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\ORM\EntityManagerInterface;
use <PERSON>ymfony\Component\DependencyInjection\ContainerAwareInterface;
use Symfony\Component\DependencyInjection\ContainerAwareTrait;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250325155419 extends AbstractMigration implements ContainerAwareInterface
{
    use ContainerAwareTrait;

    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE organism CHANGE send_as_email send_as_email JSON DEFAULT NULL');
        $this->addSql('UPDATE organism SET send_as_email = \'[1]\' WHERE send_as_email = 1');
        $this->addSql('UPDATE organism SET send_as_email = \'[]\' WHERE send_as_email = 0');
    }

//    public function postUp(Schema $schema) : void
//    {
//        /** @var EntityManagerInterface $entityManager */
//        $entityManager = $this->container->get('doctrine.orm.entity_manager');
//        /** @var OrganismService $organismService */
//        $organismService = $this->container->get(OrganismService::class);
//
//        $organisms = $organismService->listReturnQueryBuilder(['sendAsEmail' => [], 'sort' => 'id', 'order' => 'ASC'])->getQuery()->getResult();
//        foreach ($organisms as $organism) {
//            if ($organism->getSendAs() !== []) {
//                $organism->setSendAs([['email' => $organism->getEmails()[0], 'name' => $organism->getName()]]);
//            }
//        }
//        $entityManager->flush();
//    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

//        $this->addSql('ALTER TABLE organism CHANGE send_as_email send_as_email TINYINT(1) DEFAULT NULL');
    }
}
