<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20231020102801 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE subscription ADD allow_certifier_beta_features TINYINT(1) DEFAULT \'0\' NOT NULL');
        $this->addSql('UPDATE subscription SET allow_certifier_beta_features = 1 WHERE certifier_type in (\'trial\', \'usage\', \'unlimited\')');
        $this->addSql('ALTER TABLE subscription ADD certification_folders_number_cap INT DEFAULT NULL');
        $this->addSql('UPDATE subscription SET certification_folders_number_cap = 100 WHERE certifier_type in (\'trial\', \'usage\')');
        $this->addSql('UPDATE subscription SET certification_folders_number_cap = 0 WHERE certifier_type = \'unlimited\'');
    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE subscription DROP allow_certifier_beta_features');
        $this->addSql('ALTER TABLE subscription DROP certification_folders_number_cap');
    }
}
