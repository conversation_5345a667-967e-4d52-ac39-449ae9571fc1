<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240925153907 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('CREATE TABLE certification_partner_audit_template (id INT AUTO_INCREMENT NOT NULL, certification_id INT NOT NULL, name VARCHAR(255) NOT NULL, allow_visibility_partner TINYINT(1) DEFAULT \'0\' NOT NULL, criterias JSON DEFAULT NULL, INDEX IDX_54AE5B69CB47068A (certification_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE = InnoDB');
        $this->addSql('ALTER TABLE certification_partner_audit_template ADD CONSTRAINT FK_54AE5B69CB47068A FOREIGN KEY (certification_id) REFERENCES certification (id)');
        $this->addSql('ALTER TABLE certification DROP certification_partner_audit_criterias');
        $this->addSql('ALTER TABLE certification_partner_audit ADD template_id INT NOT NULL');
        $this->addSql('ALTER TABLE certification_partner_audit ADD CONSTRAINT FK_428A0B405DA0FB8 FOREIGN KEY (template_id) REFERENCES certification_partner_audit_template (id)');
        $this->addSql('CREATE INDEX IDX_428A0B405DA0FB8 ON certification_partner_audit (template_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE certification_partner_audit DROP FOREIGN KEY FK_428A0B405DA0FB8');
        $this->addSql('DROP TABLE certification_partner_audit_template');
        $this->addSql('ALTER TABLE certification ADD certification_partner_audit_criterias JSON DEFAULT NULL');
        $this->addSql('DROP INDEX IDX_428A0B405DA0FB8 ON certification_partner_audit');
        $this->addSql('ALTER TABLE certification_partner_audit DROP template_id');
    }
}
