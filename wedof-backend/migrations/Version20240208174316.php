<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240208174316 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE certification_folder ADD certifier_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE certification_folder ADD CONSTRAINT FK_A8A107A5BB49544 FOREIGN KEY (certifier_id) REFERENCES organism (id)');
        $this->addSql('CREATE INDEX IDX_A8A107A5BB49544 ON certification_folder (certifier_id)');
    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE certification_folder DROP FOREIGN KEY FK_A8A107A5BB49544');
        $this->addSql('DROP INDEX IDX_A8A107A5BB49544 ON certification_folder');
        $this->addSql('ALTER TABLE certification_folder DROP certifier_id');
    }
}
