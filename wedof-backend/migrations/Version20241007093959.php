<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use App\Library\utils\Tools;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241007093959 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        if (Tools::isEnvIn(['dev', 'test'])) {
            $this->addSql('UPDATE session SET state = \'archived\' WHERE state is null');
            $this->addSql('UPDATE training_action SET state = \'archived\' WHERE state is null');
            $this->addSql('UPDATE training SET state = \'archived\' WHERE state is null');
            $this->addSql('UPDATE training
                JOIN certification ON training.certification_id = certification.id
                SET training.state = \'unpublished\'
                WHERE certification.enabled = 0
                AND training.state = \'active\'');
            $this->addSql('UPDATE training
                SET state = \'published\'
                WHERE state = \'active\'');
            $this->addSql('UPDATE training
                SET state = \'draft\'
                WHERE state = \'validated\'');
            $this->addSql('UPDATE training_action
                JOIN training ON training_action.training_id = training.id
                SET training_action.state = \'unpublished\'
                WHERE training.state = \'unpublished\'
                AND training_action.state not in (\'deleted\', \'archived\')');
            $this->addSql('UPDATE training_action
                SET state = \'published\'
                WHERE state = \'active\'');
            $this->addSql('UPDATE training_action
                SET state = \'draft\'
                WHERE state = \'validated\'');
            $this->addSql('UPDATE session
                JOIN training_action ON session.training_action_id = training_action.id
                SET session.state = \'unpublished\'
                WHERE training_action.state = \'unpublished\'
                AND session.state not in (\'deleted\', \'archived\')');
            $this->addSql('UPDATE session
                SET state = \'published\'
                WHERE state = \'active\'');
            $this->addSql('UPDATE session
                SET state = \'draft\'
                WHERE state = \'validated\'');

            $this->addSql('ALTER TABLE session CHANGE state state VARCHAR(50) NOT NULL');
            $this->addSql('ALTER TABLE training CHANGE state state VARCHAR(50) NOT NULL');
            $this->addSql('ALTER TABLE training_action CHANGE state state VARCHAR(50) NOT NULL');
        }
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        if (Tools::isEnvIn(['dev', 'test'])) {
            $this->addSql('ALTER TABLE session CHANGE state state VARCHAR(50) CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_ci`');
            $this->addSql('ALTER TABLE training CHANGE state state VARCHAR(50) CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_ci`');
            $this->addSql('ALTER TABLE training_action CHANGE state state VARCHAR(50) CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_ci`');
        }
    }
}
