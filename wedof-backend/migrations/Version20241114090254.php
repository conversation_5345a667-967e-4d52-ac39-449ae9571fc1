<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241114090254 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');
        $this->addSql('ALTER TABLE organism ADD qualiopi_start_date DATETIME DEFAULT NULL, ADD qualiopi_end_date DATETIME DEFAULT NULL, ADD qualiopi_training_formation TINYINT(1) DEFAULT \'0\' NOT NULL, ADD qualiopi_bilan_competence TINYINT(1) DEFAULT \'0\' NOT NULL, ADD qualiopi_vae TINYINT(1) DEFAULT \'0\' NOT NULL, ADD qualiopi_formation_apprentissage TINYINT(1) DEFAULT \'0\' NOT NULL');
    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');
        $this->addSql('ALTER TABLE organism DROP qualiopi_start_date, DROP qualiopi_end_date, DROP qualiopi_training_formation, DROP qualiopi_bilan_competence, DROP qualiopi_vae, DROP qualiopi_formation_apprentissage');
    }
}
