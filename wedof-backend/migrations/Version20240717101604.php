<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240717101604 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE certification_folder_survey 
                CHANGE initial_experience_date initial_experience_answered_date DATE DEFAULT NULL, 
                CHANGE six_month_experience_date six_month_experience_answered_date DATE DEFAULT NULL, 
                CHANGE long_term_experience_date long_term_experience_answered_date DATE DEFAULT NULL, 
                ADD six_month_experience_start_date DATE DEFAULT NULL, 
                ADD long_term_experience_start_date DATE DEFAULT NULL');
    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE certification_folder_survey 
                CHANGE initial_experience_answered_date initial_experience_date  DATE DEFAULT NULL, 
                CHANGE six_month_experience_answered_date six_month_experience_date  DATE DEFAULT NULL, 
                CHANGE long_term_experience_answered_date long_term_experience_date  DATE DEFAULT NULL, 
                DROP six_month_experience_start_date, 
                DROP long_term_experience_start_date');
    }
}
