<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250121150615 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('CREATE TABLE certification_partner_skill (certification_partner_id INT NOT NULL, skill_id INT NOT NULL, INDEX IDX_8EAF104E14F5FFC5 (certification_partner_id), INDEX IDX_8EAF104E5585C142 (skill_id), PRIMARY KEY(certification_partner_id, skill_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE certification_partner_skill ADD CONSTRAINT FK_8EAF104E14F5FFC5 FOREIGN KEY (certification_partner_id) REFERENCES certifications_partners (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE certification_partner_skill ADD CONSTRAINT FK_8EAF104E5585C142 FOREIGN KEY (skill_id) REFERENCES skill (id) ON DELETE CASCADE');
    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('DROP TABLE certification_partner_skill');
    }
}
