<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240201154359 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE connection ADD is_initialized TINYINT(1) DEFAULT \'0\' NOT NULL');
        $this->addSql('UPDATE connection
            JOIN organism o on connection.organism_id = o.id
            SET connection.is_initialized = o.is_initialized
            WHERE connection.data_provider = \'cpf\'');
        $this->addSql('ALTER TABLE organism DROP is_initialized');
    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE connection DROP is_initialized');
        $this->addSql('ALTER TABLE organism ADD is_initialized TINYINT(1) NOT NULL');
    }
}
