{"2captcha/2captcha": {"version": "v1.0.5"}, "adam-paterson/oauth2-slack": {"version": "1.1.3"}, "async-aws/core": {"version": "1.12.0"}, "async-aws/ses": {"version": "1.4.1"}, "beberlei/doctrineextensions": {"version": "v1.3.0"}, "beelab/tag-bundle": {"version": "1.5", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "master", "version": "1.3", "ref": "9bd5cf1ad8576a8ad13e317cf8d7b854883cab9e"}, "files": ["config/packages/beelab_tag.yaml", "src/Entity/Tag.php"]}, "bentools/webpush-bundle": {"version": "0.9"}, "brick/math": {"version": "0.9.1"}, "composer/ca-bundle": {"version": "1.2.10"}, "composer/package-versions-deprecated": {"version": "*********"}, "defuse/php-encryption": {"version": "v2.3.1"}, "doctrine/annotations": {"version": "1.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "a2759dd6123694c8d901d0ec80006e044c2e6457"}, "files": ["./config/routes/annotations.yaml"]}, "doctrine/cache": {"version": "1.10.1"}, "doctrine/collections": {"version": "1.6.5"}, "doctrine/common": {"version": "2.13.3"}, "doctrine/data-fixtures": {"version": "1.5.0"}, "doctrine/dbal": {"version": "2.10.2"}, "doctrine/deprecations": {"version": "v0.5.3"}, "doctrine/doctrine-bundle": {"version": "2.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "2.0", "ref": "a9f2463b9f73efe74482f831f03a204a41328555"}, "files": ["config/packages/doctrine.yaml", "config/packages/prod/doctrine.yaml", "src/Entity/.gitignore", "src/Repository/.gitignore"]}, "doctrine/doctrine-fixtures-bundle": {"version": "3.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.0", "ref": "e5b542d4ef47d8a003c91beb35650c76907f7e53"}, "files": ["src/DataFixtures/AppFixtures.php"]}, "doctrine/doctrine-migrations-bundle": {"version": "2.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "2.2", "ref": "baaa439e3e3179e69e3da84b671f0a3e4a2f56ad"}, "files": ["config/packages/doctrine_migrations.yaml", "migrations/.gitignore"]}, "doctrine/event-manager": {"version": "1.1.0"}, "doctrine/inflector": {"version": "1.4.3"}, "doctrine/instantiator": {"version": "1.3.1"}, "doctrine/lexer": {"version": "1.2.1"}, "doctrine/migrations": {"version": "3.0.1"}, "doctrine/orm": {"version": "v2.7.0"}, "doctrine/persistence": {"version": "1.3.7"}, "doctrine/sql-formatter": {"version": "1.1.0"}, "egulias/email-validator": {"version": "3.1.2"}, "embed/embed": {"version": "3.4.17"}, "fakerphp/faker": {"version": "v1.14.1"}, "firebase/php-jwt": {"version": "v5.2.0"}, "friendsofphp/proxy-manager-lts": {"version": "v1.0.3"}, "friendsofsymfony/rest-bundle": {"version": "2.2", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "master", "version": "2.2", "ref": "cad41ef93d6150067ae2bb3c7fd729492dff6f0a"}, "files": ["./config/packages/fos_rest.yaml"]}, "gesdinet/jwt-refresh-token-bundle": {"version": "v0.9.1"}, "giggsey/libphonenumber-for-php": {"version": "8.12.34"}, "giggsey/locale": {"version": "2.0"}, "guzzlehttp/guzzle": {"version": "7.3.0"}, "guzzlehttp/promises": {"version": "1.4.1"}, "guzzlehttp/psr7": {"version": "1.8.2"}, "intervention/image": {"version": "2.7.0"}, "jms/metadata": {"version": "2.3.0"}, "jms/serializer": {"version": "3.8.0"}, "jms/serializer-bundle": {"version": "3.0", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "master", "version": "3.0", "ref": "384cec52df45f3bfd46a09930d6960a58872b268"}, "files": ["./config/packages/dev/jms_serializer.yaml", "./config/packages/jms_serializer.yaml", "./config/packages/prod/jms_serializer.yaml"]}, "knplabs/knp-components": {"version": "v2.4.2"}, "knplabs/knp-gaufrette-bundle": {"version": "v0.9.0"}, "knplabs/knp-paginator-bundle": {"version": "v5.3.0"}, "knpuniversity/oauth2-client-bundle": {"version": "1.20", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "master", "version": "1.20", "ref": "80acc9223a205cbf5d9828fd616c82a374d281dd"}, "files": ["config/packages/knpu_oauth2_client.yaml"]}, "laminas/laminas-code": {"version": "3.4.1"}, "lasserafn/php-initial-avatar-generator": {"version": "4.2.1"}, "lasserafn/php-initials": {"version": "3.1"}, "lasserafn/php-string-script-language": {"version": "0.3"}, "lcobucci/jwt": {"version": "3.3.2"}, "league/event": {"version": "2.2.0"}, "league/oauth2-client": {"version": "2.6.0"}, "league/oauth2-google": {"version": "3.0.4"}, "league/oauth2-server": {"version": "8.3.3"}, "league/oauth2-server-bundle": {"version": "v0.1.2"}, "league/uri": {"version": "6.5.0"}, "league/uri-interfaces": {"version": "2.3.0"}, "lexik/jwt-authentication-bundle": {"version": "2.5", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "2.5", "ref": "5b2157bcd5778166a5696e42f552ad36529a07a6"}, "files": ["config/packages/lexik_jwt_authentication.yaml"]}, "meyfa/php-svg": {"version": "v0.9.1"}, "monolog/monolog": {"version": "1.25.4"}, "myclabs/deep-copy": {"version": "1.10.2"}, "myclabs/php-enum": {"version": "1.7.6"}, "namshi/jose": {"version": "7.2.3"}, "nelmio/api-doc-bundle": {"version": "3.0", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "master", "version": "3.0", "ref": "c8e0c38e1a280ab9e37587a8fa32b251d5bc1c94"}, "files": ["./config/packages/nelmio_api_doc.yaml", "./config/routes/nelmio_api_doc.yaml"]}, "nelmio/cors-bundle": {"version": "2.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.5", "ref": "6bea22e6c564fba3a1391615cada1437d0bde39c"}, "files": ["config/packages/nelmio_cors.yaml"]}, "nikic/php-parser": {"version": "v4.5.0"}, "nyholm/psr7": {"version": "1.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "7c0a9352a57376f04f5444e74565102c3a23d0c7"}, "files": ["config/packages/nyholm_psr7.yaml"]}, "overtrue/pinyin": {"version": "4.0.8"}, "phar-io/manifest": {"version": "2.0.1"}, "phar-io/version": {"version": "3.1.0"}, "php-http/message-factory": {"version": "v1.0.2"}, "phpdocumentor/reflection-common": {"version": "2.2.0"}, "phpdocumentor/reflection-docblock": {"version": "5.2.2"}, "phpdocumentor/type-resolver": {"version": "1.4.0"}, "phpspec/prophecy": {"version": "1.13.0"}, "phpstan/phpdoc-parser": {"version": "0.4.11"}, "phpunit/php-code-coverage": {"version": "7.0.14"}, "phpunit/php-file-iterator": {"version": "2.0.3"}, "phpunit/php-text-template": {"version": "1.2.1"}, "phpunit/php-timer": {"version": "2.1.3"}, "phpunit/php-token-stream": {"version": "3.1.2"}, "phpunit/phpunit": {"version": "4.7", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.7", "ref": "fc18a41d910bd168b6d83b09330d3dd892ddbcdd"}, "files": [".env.test", "phpunit.xml.dist", "tests/bootstrap.php"]}, "psr/cache": {"version": "1.0.1"}, "psr/container": {"version": "1.0.0"}, "psr/event-dispatcher": {"version": "1.0.0"}, "psr/http-client": {"version": "1.0.1"}, "psr/http-factory": {"version": "1.0.1"}, "psr/http-message": {"version": "1.0.1"}, "psr/log": {"version": "1.1.3"}, "ralouphie/getallheaders": {"version": "3.0.3"}, "ramsey/collection": {"version": "1.1.1"}, "ramsey/uuid": {"version": "4.1.1"}, "ronanguilloux/isocodes": {"version": "2.3.1"}, "scienta/doctrine-json-functions": {"version": "4.3.0"}, "sebastian/code-unit-reverse-lookup": {"version": "1.0.2"}, "sebastian/comparator": {"version": "3.0.3"}, "sebastian/diff": {"version": "3.0.3"}, "sebastian/environment": {"version": "4.2.4"}, "sebastian/exporter": {"version": "3.1.3"}, "sebastian/global-state": {"version": "3.0.1"}, "sebastian/object-enumerator": {"version": "3.0.4"}, "sebastian/object-reflector": {"version": "1.1.2"}, "sebastian/recursion-context": {"version": "3.0.1"}, "sebastian/resource-operations": {"version": "2.0.2"}, "sebastian/type": {"version": "1.1.4"}, "sebastian/version": {"version": "2.0.1"}, "sensio/framework-extra-bundle": {"version": "5.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.2", "ref": "fb7e19da7f013d0d422fa9bce16f5c510e27609b"}, "files": ["./config/packages/sensio_framework_extra.yaml"]}, "simplehtmldom/simplehtmldom": {"version": "2.0-rc2"}, "sllh/iso-codes-validator": {"version": "v4.0.0"}, "stevenmaguire/oauth2-salesforce": {"version": "2.0.1"}, "stripe/stripe-php": {"version": "v7.53.1"}, "symfony/amazon-mailer": {"version": "5.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.4", "ref": "9648db3ecae5c8a6b1a5f74715d3907124348815"}}, "symfony/amqp-messenger": {"version": "v5.3.7"}, "symfony/asset": {"version": "v4.4.17"}, "symfony/browser-kit": {"version": "v4.4.24"}, "symfony/cache": {"version": "v4.4.9"}, "symfony/cache-contracts": {"version": "v2.1.2"}, "symfony/config": {"version": "v4.4.9"}, "symfony/console": {"version": "4.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.4", "ref": "ea8c0eda34fda57e7d5cd8cbd889e2a387e3472c"}, "files": ["./bin/console", "./config/bootstrap.php"]}, "symfony/css-selector": {"version": "v4.4.22"}, "symfony/dependency-injection": {"version": "v4.4.9"}, "symfony/deprecation-contracts": {"version": "v2.2.0"}, "symfony/doctrine-bridge": {"version": "v4.4.10"}, "symfony/doctrine-messenger": {"version": "v5.3.8"}, "symfony/dom-crawler": {"version": "v4.4.24"}, "symfony/dotenv": {"version": "v4.4.9"}, "symfony/error-handler": {"version": "v4.4.9"}, "symfony/event-dispatcher": {"version": "v4.4.9"}, "symfony/event-dispatcher-contracts": {"version": "v1.1.7"}, "symfony/expression-language": {"version": "v4.4.16"}, "symfony/filesystem": {"version": "v4.4.9"}, "symfony/finder": {"version": "v4.4.9"}, "symfony/flex": {"version": "1.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "c0eeb50665f0f77226616b6038a9b06c03752d8e"}, "files": ["./.env"]}, "symfony/form": {"version": "v4.4.26"}, "symfony/framework-bundle": {"version": "4.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.4", "ref": "36d3075b2b8e0c4de0e82356a86e4c4a4eb6681b"}, "files": ["./config/bootstrap.php", "./config/packages/cache.yaml", "./config/packages/framework.yaml", "./config/packages/test/framework.yaml", "./config/routes/dev/framework.yaml", "./config/services.yaml", "./public/index.php", "./src/Controller/.gitignore", "./src/Kernel.php"]}, "symfony/google-mailer": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.4", "ref": "f8fd4ddb9b477510f8f4bce2b9c054ab428c0120"}}, "symfony/http-client": {"version": "v4.4.9"}, "symfony/http-client-contracts": {"version": "v2.1.2"}, "symfony/http-foundation": {"version": "v4.4.9"}, "symfony/http-kernel": {"version": "v4.4.9"}, "symfony/lock": {"version": "4.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.4", "ref": "e13a79427495e670ef7a19f0e25fd13fbdd909d4"}, "files": ["config/packages/lock.yaml"]}, "symfony/mailer": {"version": "5.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.3", "ref": "bbfc7e27257d3a3f12a6fb0a42540a42d9623a37"}, "files": ["config/packages/mailer.yaml"]}, "symfony/maker-bundle": {"version": "1.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "fadbfe33303a76e25cb63401050439aa9b1a9c7f"}}, "symfony/messenger": {"version": "5.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.3", "ref": "25e3c964d3aee480b3acc3114ffb7940c89edfed"}, "files": ["config/packages/messenger.yaml"]}, "symfony/mime": {"version": "v4.4.9"}, "symfony/monolog-bridge": {"version": "v4.4.10"}, "symfony/monolog-bundle": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.3", "ref": "d7249f7d560f6736115eee1851d02a65826f0a56"}, "files": ["./config/packages/dev/monolog.yaml", "./config/packages/prod/deprecations.yaml", "./config/packages/prod/monolog.yaml", "./config/packages/test/monolog.yaml"]}, "symfony/notifier": {"version": "5.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.0", "ref": "c31585e252b32fe0e1f30b1f256af553f4a06eb9"}, "files": ["config/packages/notifier.yaml"]}, "symfony/options-resolver": {"version": "v4.4.16"}, "symfony/password-hasher": {"version": "v5.3.8"}, "symfony/phpunit-bridge": {"version": "5.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.1", "ref": "bf16921ef8309a81d9f046e9b6369c46bcbd031f"}, "files": [".env.test", "bin/phpunit", "phpunit.xml.dist", "tests/bootstrap.php"]}, "symfony/polyfill-intl-grapheme": {"version": "v1.23.0"}, "symfony/polyfill-intl-icu": {"version": "v1.23.0"}, "symfony/polyfill-intl-idn": {"version": "v1.17.0"}, "symfony/polyfill-intl-normalizer": {"version": "v1.17.1"}, "symfony/polyfill-mbstring": {"version": "v1.17.0"}, "symfony/polyfill-php72": {"version": "v1.17.0"}, "symfony/polyfill-php73": {"version": "v1.17.0"}, "symfony/polyfill-php80": {"version": "v1.17.0"}, "symfony/polyfill-php81": {"version": "v1.23.0"}, "symfony/polyfill-uuid": {"version": "v1.20.0"}, "symfony/process": {"version": "v4.4.17"}, "symfony/property-access": {"version": "v4.4.10"}, "symfony/property-info": {"version": "v4.4.16"}, "symfony/psr-http-message-bridge": {"version": "v2.1.1"}, "symfony/redis-messenger": {"version": "v5.3.8"}, "symfony/routing": {"version": "4.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.2", "ref": "683dcb08707ba8d41b7e34adb0344bfd68d248a7"}, "files": ["./config/packages/prod/routing.yaml", "./config/packages/routing.yaml", "./config/routes.yaml"]}, "symfony/security-bundle": {"version": "4.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.4", "ref": "7b4408dc203049666fe23fabed23cbadc6d8440f"}, "files": ["config/packages/security.yaml"]}, "symfony/security-core": {"version": "v4.4.10"}, "symfony/security-csrf": {"version": "v4.4.10"}, "symfony/security-guard": {"version": "v4.4.10"}, "symfony/security-http": {"version": "v4.4.10"}, "symfony/serializer": {"version": "v4.4.11"}, "symfony/service-contracts": {"version": "v2.1.2"}, "symfony/slack-notifier": {"version": "5.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.2", "ref": "192bc241a3095c6861353e5a7a652ad35659ca92"}}, "symfony/stopwatch": {"version": "v4.4.10"}, "symfony/string": {"version": "v5.3.3"}, "symfony/translation": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.3", "ref": "2ad9d2545bce8ca1a863e50e92141f0b9d87ffcd"}, "files": ["config/packages/translation.yaml", "translations/.gitignore"]}, "symfony/translation-contracts": {"version": "v2.1.2"}, "symfony/twig-bridge": {"version": "v4.4.16"}, "symfony/twig-bundle": {"version": "4.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.4", "ref": "15a41bbd66a1323d09824a189b485c126bbefa51"}, "files": ["config/packages/test/twig.yaml", "config/packages/twig.yaml", "templates/base.html.twig"]}, "symfony/validator": {"version": "4.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.3", "ref": "d902da3e4952f18d3bf05aab29512eb61cabd869"}, "files": ["./config/packages/test/validator.yaml", "./config/packages/validator.yaml"]}, "symfony/var-dumper": {"version": "v4.4.9"}, "symfony/var-exporter": {"version": "v4.4.9"}, "symfony/yaml": {"version": "v4.4.9"}, "symfonycasts/verify-email-bundle": {"version": "v1.0.0"}, "theseer/tokenizer": {"version": "1.2.0"}, "twig/twig": {"version": "v3.0.3"}, "vich/uploader-bundle": {"version": "1.5", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "master", "version": "1.5", "ref": "c4f5755b37fb65b9c6a3cbdae91205c15a137ed4"}, "files": ["config/packages/vich_uploader.yaml"]}, "webmozart/assert": {"version": "1.9.1"}, "willdurand/hateoas": {"version": "3.6.0"}, "willdurand/hateoas-bundle": {"version": "2.0", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "master", "version": "2.0", "ref": "34df072c6edaa61ae19afb2f3a239f272fecab87"}, "files": ["./config/packages/bazinga_hateoas.yaml"]}, "willdurand/jsonp-callback-validator": {"version": "v1.1.0"}, "willdurand/negotiation": {"version": "v2.3.1"}, "yectep/phpspreadsheet-bundle": {"version": "v1.0.0"}, "zenstruck/assert": {"version": "v1.0.0"}, "zenstruck/callback": {"version": "v1.2.0"}, "zenstruck/foundry": {"version": "v1.7.1"}, "zircote/swagger-php": {"version": "3.1.0"}}