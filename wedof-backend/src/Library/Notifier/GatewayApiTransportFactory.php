<?php

namespace App\Library\Notifier;

use Symfony\Component\Notifier\Exception\UnsupportedSchemeException;
use Symfony\Component\Notifier\Transport\AbstractTransportFactory;
use Symfony\Component\Notifier\Transport\Dsn;
use Symfony\Component\Notifier\Transport\TransportInterface;

class GatewayApiTransportFactory extends AbstractTransportFactory
{
    public function create(Dsn $dsn, string $from = null): TransportInterface
    {
        $scheme = $dsn->getScheme();

        if ('gatewayapi' !== $scheme) {
            throw new UnsupportedSchemeException($dsn, 'gatewayapi', $this->getSupportedSchemes());
        }

        $authToken = $this->getUser($dsn);
        $from = $from ?? $dsn->getRequiredOption('from');
        $host = 'default' === $dsn->getHost() ? null : $dsn->getHost();
        $port = $dsn->getPort();

        return (new GatewayApiTransport($authToken, $from, $this->client, $this->dispatcher))->setHost($host)->setPort($port);
    }

    protected function getSupportedSchemes(): array
    {
        return ['gatewayapi'];
    }
}