<?php

namespace App\Library\Notifier;

use Symfony\Component\Notifier\Transport;
use Symfony\Component\Notifier\Transport\TransportFactoryInterface;

/**
 * This class replace Transport.php (Notifier) to use dependency injection in Wedof Project
 */
class WedofNotifierTransport extends Transport
{
    /**
     * @param TransportFactoryInterface[] $factories
     */
    public function __construct(iterable $factories)
    {
        parent::__construct($factories);
    }
}
