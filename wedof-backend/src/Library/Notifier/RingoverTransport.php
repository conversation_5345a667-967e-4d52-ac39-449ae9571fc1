<?php

namespace App\Library\Notifier;

use Symfony\Component\Notifier\Exception\TransportException;
use Symfony\Component\Notifier\Exception\TransportExceptionInterface;
use Symfony\Component\Notifier\Exception\UnsupportedMessageTypeException;
use Symfony\Component\Notifier\Message\MessageInterface;
use Symfony\Component\Notifier\Message\SentMessage;
use Symfony\Component\Notifier\Message\SmsMessage;
use Symfony\Component\Notifier\Transport\AbstractTransport;
use Symfony\Contracts\EventDispatcher\EventDispatcherInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

final class RingoverTransport extends AbstractTransport
{
    private const TRANSPORT = 'ringover';
    protected const HOST = 'public-api.ringover.com';
    private $authToken;
    private $from;

    public function __construct(string $authToken, string $from = null, HttpClientInterface $client = null, EventDispatcherInterface $dispatcher = null)
    {
        $this->authToken = $authToken;
        $this->from = $from;

        parent::__construct($client, $dispatcher);
    }

    public function __toString(): string
    {
        if (null === $this->from) {
            return sprintf(self::TRANSPORT . '://%s', $this->getEndpoint());
        }

        return sprintf(self::TRANSPORT . '://%s?from=%s', $this->getEndpoint(), $this->from);
    }

    public function supports(MessageInterface $message): bool
    {
        return $message instanceof SmsMessage;
    }

    protected function doSend(MessageInterface $message): SentMessage
    {
        if (!$message instanceof SmsMessage) {
            throw new UnsupportedMessageTypeException(__CLASS__, SmsMessage::class, $message);
        }

        $endpoint = sprintf('https://%s/v2/push/sms', $this->getEndpoint());
        $response = $this->client->request('POST', $endpoint, [
            'headers' => [
                'Accept' => 'application/json',
                'Authorization' => $this->authToken,
                'Content-Type' => 'application/json'
            ],
            'json' => [
                "archived_auto" => false,
                'from_number' => str_replace(' ', '+', $this->from),
                'to_number' => $message->getPhone(),
                'content' => $message->getSubject(),
            ],
        ]);

        try {
            $statusCode = $response->getStatusCode();
        } catch (TransportExceptionInterface $e) {
            throw new TransportException('Could not reach the remote Ringover server.', $response, 0, $e);
        }

        if (202 === $statusCode) {
            $result = $response->toArray();
            $sentMessage = new SentMessage($message, (string)$this);
            $sentMessage->setMessageId($result['message_id']);

            return $sentMessage;
        }
        $content = $response->toArray(false);
        $errorCode = $content['error']['code'] ?? '';
        $errorInfo = $content['error']['description'] ?? '';
        $errorDocumentation = $content['error']['documentation'] ?? '';

        throw new TransportException(sprintf('Unable to send SMS with Ringover: Error code %d with message "%s" (%s).', $errorCode, $errorInfo, $errorDocumentation), $response);
    }
}
