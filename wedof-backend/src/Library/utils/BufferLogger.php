<?php

namespace App\Library\utils;

use Psr\Log\LoggerInterface;

class BufferLogger implements LoggerInterface
{
    private array $buffer = [];

    public function emergency($message, array $context = array())
    {
        $this->buffer[] = "[" . date("Y-m-d H:i:s") . "][emergency] $message \n";
    }

    public function alert($message, array $context = array())
    {
        $this->buffer[] = "[" . date("Y-m-d H:i:s") . "][alert] $message \n";
    }

    public function critical($message, array $context = array())
    {
        $this->buffer[] = "[" . date("Y-m-d H:i:s") . "][critical] $message \n";
    }

    public function error($message, array $context = array())
    {
        $this->buffer[] = "[" . date("Y-m-d H:i:s") . "][error] $message \n";
    }

    public function warning($message, array $context = array())
    {
        $this->buffer[] = "[" . date("Y-m-d H:i:s") . "][warning] $message \n";
    }

    public function notice($message, array $context = array())
    {
        $this->buffer[] = "[" . date("Y-m-d H:i:s") . "][notice] $message \n";
    }

    public function info($message, array $context = array())
    {
        $this->buffer[] = "[" . date("Y-m-d H:i:s") . "][info] $message \n";
    }

    public function debug($message, array $context = array())
    {
        $this->buffer[] = "[" . date("Y-m-d H:i:s") . "][debug] $message \n";
    }

    public function log($level, $message, array $context = array())
    {
        $this->buffer[] = "[" . date("Y-m-d H:i:s") . "][log][$level] $message \n";
    }

    public function getBufferAsString(): string
    {
        return implode("", $this->buffer);
    }

    public function getBuffer(): array
    {
        return $this->buffer;
    }

    public function cleanBuffer()
    {
        $this->buffer = [];
    }

    public function addTrace(string $trace)
    {
        $this->buffer = array_merge($this->buffer, explode('#', $trace));
    }
}
