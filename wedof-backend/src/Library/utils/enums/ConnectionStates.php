<?php


namespace App\Library\utils\enums;


use MyCLabs\Enum\Enum;

class ConnectionStates extends Enum
{
    private const INACTIVE = 'inactive';
    private const IN_PROGRESS = 'inProgress';
    private const ACTIVE = 'active';
    private const FAILED = 'failed';
    private const REVOKED = 'revoked';
    private const REFRESHING = 'refreshing';


    /**
     * @return ConnectionStates
     */
    public static function INACTIVE(): ConnectionStates
    {
        return new ConnectionStates(self::INACTIVE);
    }

    /**
     * @return ConnectionStates
     */
    public static function IN_PROGRESS(): ConnectionStates
    {
        return new ConnectionStates(self::IN_PROGRESS);
    }

    /**
     * @return ConnectionStates
     */
    public static function ACTIVE(): ConnectionStates
    {
        return new ConnectionStates(self::ACTIVE);
    }

    /**
     * @return ConnectionStates
     */
    public static function REFRESHING(): ConnectionStates
    {
        return new ConnectionStates(self::REFRESHING);
    }

    /**
     * @return ConnectionStates
     */
    public static function FAILED(): ConnectionStates
    {
        return new ConnectionStates(self::FAILED);
    }

    /**
     * @return ConnectionStates
     */
    public static function REVOKED(): ConnectionStates
    {
        return new ConnectionStates(self::REVOKED);
    }

    /**
     * @return ConnectionStates[]
     */
    public static function valuesTypes(): array
    {
        return [
            new ConnectionStates(self::INACTIVE),
            new ConnectionStates(self::IN_PROGRESS),
            new ConnectionStates(self::ACTIVE),
            new ConnectionStates(self::REFRESHING),
            new ConnectionStates(self::FAILED),
            new ConnectionStates(self::REVOKED)
        ];
    }

    /**
     * @param $state
     * @return string
     */
    public static function toFrString($state): string
    {
        $fr = [
            self::INACTIVE => "Inactive",
            self::IN_PROGRESS => "En cours d'activation",
            self::ACTIVE => "Active",
            self::REFRESHING => "Actualisation",
            self::FAILED => "Échouée",
            self::REVOKED => "Révoquée"
        ];
        return $fr[$state];
    }
}