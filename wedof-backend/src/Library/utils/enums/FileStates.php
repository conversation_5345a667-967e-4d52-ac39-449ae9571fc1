<?php

namespace App\Library\utils\enums;

use MyCLabs\Enum\Enum;

class FileStates extends Enum
{
    private const NOT_SUBMITTED = "notSubmitted";
    private const TO_REVIEW = "toReview";
    private const VALID = "valid";
    private const REFUSED = "refused";

    /**
     * @return FileStates
     */
    public static function NOT_SUBMITTED(): FileStates
    {
        return new FileStates(self::NOT_SUBMITTED);
    }

    public static function TO_REVIEW(): FileStates
    {
        return new FileStates(self::TO_REVIEW);
    }

    /**
     * @return FileStates
     */
    public static function VALID(): FileStates
    {
        return new FileStates(self::VALID);
    }

    /**
     * @return FileStates
     */
    public static function REFUSED(): FileStates
    {
        return new FileStates(self::REFUSED);
    }

    /**
     * @param $state
     * @return string
     */
    public static function toFrStringActivity($state): string
    {
        $fr = [
            self::TO_REVIEW => "est à vérifier",
            self::VALID => "à été validé",
            self::REFUSED => "à été refusé",
            self::NOT_SUBMITTED => "non déposé"
        ];
        return $fr[$state];
    }

    /**
     * @return FileStates[]
     */
    public static function valuesStates(): array
    {
        return [
            new FileStates(self::VALID),
            new FileStates(self::REFUSED),
            new FileStates(self::TO_REVIEW),
        ];
    }
}
