<?php

namespace App\Library\utils\enums;

use MyCLabs\Enum\Enum;

class WorkingContractStates extends Enum
{
    private const DRAFT = 'draft'; // interne Wedof
    private const SENT = 'sent'; // TRANSMIS
    private const PENDING_ACCEPTATION = 'pendingAcceptation'; // EN_COURS_INSTRUCTION
    private const ACCEPTED = 'accepted'; // ENGAGE
    private const CANCELLED = 'cancelled'; // ANNULE
    private const REFUSED = 'refused'; // REFUSE
    private const BROKEN = 'broken'; // RUPTURE
    private const COMPLETED = 'completed'; // SOLDE

    private const ALL = 'all';

    /**
     * @return WorkingContractStates
     */
    public static function DRAFT(): WorkingContractStates
    {
        return new WorkingContractStates(self::DRAFT);
    }

    /**
     * @return WorkingContractStates
     */
    public static function SENT(): WorkingContractStates
    {
        return new WorkingContractStates(self::SENT);
    }

    /**
     * @return WorkingContractStates
     */
    public static function PENDING_ACCEPTATION(): WorkingContractStates
    {
        return new WorkingContractStates(self::PENDING_ACCEPTATION);
    }

    /**
     * @return WorkingContractStates
     */
    public static function ACCEPTED(): WorkingContractStates
    {
        return new WorkingContractStates(self::ACCEPTED);
    }

    /**
     * @return WorkingContractStates
     */
    public static function CANCELLED(): WorkingContractStates
    {
        return new WorkingContractStates(self::CANCELLED);
    }

    /**
     * @return WorkingContractStates
     */
    public static function REFUSED(): WorkingContractStates
    {
        return new WorkingContractStates(self::REFUSED);
    }

    /**
     * @return WorkingContractStates
     */
    public static function BROKEN(): WorkingContractStates
    {
        return new WorkingContractStates(self::BROKEN);
    }

    /**
     * @return WorkingContractStates
     */
    public static function COMPLETED(): WorkingContractStates
    {
        return new WorkingContractStates(self::COMPLETED);
    }

    /**
     * @return WorkingContractStates
     */
    public static function ALL(): WorkingContractStates
    {
        return new WorkingContractStates(self::ALL);
    }

    /**
     * @param string $result
     * @return string
     */
    public static function toFrString(string $result): string
    {
        // used for Message & Notifications
        $fr = [
            self::DRAFT => 'Brouillon',
            self::SENT => 'Transmis',
            self::PENDING_ACCEPTATION => 'En cours d\'instruction',
            self::ACCEPTED => 'Engagé',
            self::CANCELLED => 'Annulé',
            self::REFUSED => 'Refusé',
            self::BROKEN => 'Rupture',
            self::COMPLETED => 'Soldé'
        ];
        return $fr[$result];
    }

    /**
     * @return WorkingContractStates[]
     */
    public static function valuesStates(): array
    {
        return [
            new WorkingContractStates(self::DRAFT),
            new WorkingContractStates(self::SENT),
            new WorkingContractStates(self::PENDING_ACCEPTATION),
            new WorkingContractStates(self::ACCEPTED),
            new WorkingContractStates(self::CANCELLED),
            new WorkingContractStates(self::REFUSED),
            new WorkingContractStates(self::BROKEN),
            new WorkingContractStates(self::COMPLETED),
            new WorkingContractStates(self::ALL),
        ];
    }
}