<?php

namespace App\Library\utils\enums;

use MyCLabs\Enum\Enum;

class ProposalDiscountTypes extends Enum
{

    private const NONE = 'none';
    private const PERCENT = 'percent';
    private const FIXED = 'fixed';
    private const AMOUNT = 'amount';

    public static function NONE(): ProposalDiscountTypes
    {
        return new ProposalDiscountTypes(self::NONE);
    }

    public static function PERCENT(): ProposalDiscountTypes
    {
        return new ProposalDiscountTypes(self::PERCENT);
    }

    public static function FIXED(): ProposalDiscountTypes
    {
        return new ProposalDiscountTypes(self::FIXED);
    }

    public static function AMOUNT(): ProposalDiscountTypes
    {
        return new ProposalDiscountTypes(self::AMOUNT);
    }
}