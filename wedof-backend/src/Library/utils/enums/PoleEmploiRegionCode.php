<?php


namespace App\Library\utils\enums;


use MyCLabs\Enum\Enum;

class PoleEmploiRegionCode extends Enum
{
    // https://pole-emploi.io/data/api/zero-saisie-test?tabgroup-api=documentation&doc-sect[…]-de-la-prise-de-contact-pour-les-inscriptions-sur-candidature

    private const ALPES = "024";
    private const ALPES_PROVENCE = "034";
    private const ALSACE = "017";
    private const AQUITAINE = "001";
    private const AUVERGNE = "044";
    private const BASSE_NORMANDIE = "040";
    private const BOURGOGNE = "050";
    private const BRETAGNE = "027";
    private const CENTRE = "035";
    private const CHAMPAGNE_ARDENNE = "051";
    private const CORSE = "065";
    private const COTE_D_AZUR = "032";
    private const EST_FRANCILIEN = "061";
    private const FRANCHE_COMTE = "020";
    private const GUADELOUPE = "066";
    private const GUYANE = "069";
    private const HAUTE_NORMANDIE = "041";
    private const LA_REUNION = "068";
    private const LANGUEDOC_ROUSSILLON = "046";
    private const LIMOUSIN = "012";
    private const LORRAINE = "063";
    private const MARTINIQUE = "067";
    private const MAYOTTE = "071";
    private const MIDI_PYRENEES = "048";
    private const OUEST_FRANCILIEN = "057";
    private const PARIS = "056";
    private const PAS_DE_CALAIS = "026";
    private const PAYS_DE_LA_LOIRE = "013";
    private const PAYS_DU_NORD = "049";
    private const PICARDIE = "025";
    private const POITOU_CHARENTES = "039";
    private const SAINT_PIERRE_ET_MIQUELON = "070";
    private const SUD_EST_FRANCILIEN = "016";
    private const VALLEES_RHONE_LOIRE = "031";

    /**
     * @return PoleEmploiRegionCode
     */
    public static function ALPES(): PoleEmploiRegionCode
    {
        return new PoleEmploiRegionCode(self::ALPES);
    }

    /**
     * @return PoleEmploiRegionCode
     */
    public static function ALPES_PROVENCE(): PoleEmploiRegionCode
    {
        return new PoleEmploiRegionCode(self::ALPES_PROVENCE);
    }

    /**
     * @return PoleEmploiRegionCode
     */
    public static function ALSACE(): PoleEmploiRegionCode
    {
        return new PoleEmploiRegionCode(self::ALSACE);
    }

    /**
     * @return PoleEmploiRegionCode
     */
    public static function AQUITAINE(): PoleEmploiRegionCode
    {
        return new PoleEmploiRegionCode(self::AQUITAINE);
    }

    /**
     * @return PoleEmploiRegionCode
     */
    public static function AUVERGNE(): PoleEmploiRegionCode
    {
        return new PoleEmploiRegionCode(self::AUVERGNE);
    }

    /**
     * @return PoleEmploiRegionCode
     */
    public static function BASSE_NORMANDIE(): PoleEmploiRegionCode
    {
        return new PoleEmploiRegionCode(self::BASSE_NORMANDIE);
    }

    /**
     * @return PoleEmploiRegionCode
     */
    public static function BOURGOGNE(): PoleEmploiRegionCode
    {
        return new PoleEmploiRegionCode(self::BOURGOGNE);
    }

    /**
     * @return PoleEmploiRegionCode
     */
    public static function BRETAGNE(): PoleEmploiRegionCode
    {
        return new PoleEmploiRegionCode(self::BRETAGNE);
    }

    /**
     * @return PoleEmploiRegionCode
     */
    public static function CENTRE(): PoleEmploiRegionCode
    {
        return new PoleEmploiRegionCode(self::CENTRE);
    }

    /**
     * @return PoleEmploiRegionCode
     */
    public static function CHAMPAGNE_ARDENNE(): PoleEmploiRegionCode
    {
        return new PoleEmploiRegionCode(self::CHAMPAGNE_ARDENNE);
    }

    /**
     * @return PoleEmploiRegionCode
     */
    public static function CORSE(): PoleEmploiRegionCode
    {
        return new PoleEmploiRegionCode(self::CORSE);
    }

    /**
     * @return PoleEmploiRegionCode
     */
    public static function COTE_D_AZUR(): PoleEmploiRegionCode
    {
        return new PoleEmploiRegionCode(self::COTE_D_AZUR);
    }

    /**
     * @return PoleEmploiRegionCode
     */
    public static function EST_FRANCILIEN(): PoleEmploiRegionCode
    {
        return new PoleEmploiRegionCode(self::EST_FRANCILIEN);
    }

    /**
     * @return PoleEmploiRegionCode
     */
    public static function FRANCHE_COMTE(): PoleEmploiRegionCode
    {
        return new PoleEmploiRegionCode(self::FRANCHE_COMTE);
    }

    /**
     * @return PoleEmploiRegionCode
     */
    public static function GUADELOUPE(): PoleEmploiRegionCode
    {
        return new PoleEmploiRegionCode(self::GUADELOUPE);
    }

    /**
     * @return PoleEmploiRegionCode
     */
    public static function GUYANE(): PoleEmploiRegionCode
    {
        return new PoleEmploiRegionCode(self::GUYANE);
    }

    /**
     * @return PoleEmploiRegionCode
     */
    public static function HAUTE_NORMANDIE(): PoleEmploiRegionCode
    {
        return new PoleEmploiRegionCode(self::HAUTE_NORMANDIE);
    }

    /**
     * @return PoleEmploiRegionCode
     */
    public static function LA_REUNION(): PoleEmploiRegionCode
    {
        return new PoleEmploiRegionCode(self::LA_REUNION);
    }

    /**
     * @return PoleEmploiRegionCode
     */
    public static function LANGUEDOC_ROUSSILLON(): PoleEmploiRegionCode
    {
        return new PoleEmploiRegionCode(self::LANGUEDOC_ROUSSILLON);
    }

    /**
     * @return PoleEmploiRegionCode
     */
    public static function LIMOUSIN(): PoleEmploiRegionCode
    {
        return new PoleEmploiRegionCode(self::LIMOUSIN);
    }

    /**
     * @return PoleEmploiRegionCode
     */
    public static function LORRAINE(): PoleEmploiRegionCode
    {
        return new PoleEmploiRegionCode(self::LORRAINE);
    }

    /**
     * @return PoleEmploiRegionCode
     */
    public static function MARTINIQUE(): PoleEmploiRegionCode
    {
        return new PoleEmploiRegionCode(self::MARTINIQUE);
    }

    /**
     * @return PoleEmploiRegionCode
     */
    public static function MAYOTTE(): PoleEmploiRegionCode
    {
        return new PoleEmploiRegionCode(self::MAYOTTE);
    }

    /**
     * @return PoleEmploiRegionCode
     */
    public static function MIDI_PYRENEES(): PoleEmploiRegionCode
    {
        return new PoleEmploiRegionCode(self::MIDI_PYRENEES);
    }

    /**
     * @return PoleEmploiRegionCode
     */
    public static function OUEST_FRANCILIEN(): PoleEmploiRegionCode
    {
        return new PoleEmploiRegionCode(self::OUEST_FRANCILIEN);
    }

    /**
     * @return PoleEmploiRegionCode
     */
    public static function PARIS(): PoleEmploiRegionCode
    {
        return new PoleEmploiRegionCode(self::PARIS);
    }

    /**
     * @return PoleEmploiRegionCode
     */
    public static function PAS_DE_CALAIS(): PoleEmploiRegionCode
    {
        return new PoleEmploiRegionCode(self::PAS_DE_CALAIS);
    }

    /**
     * @return PoleEmploiRegionCode
     */
    public static function PAYS_DE_LA_LOIRE(): PoleEmploiRegionCode
    {
        return new PoleEmploiRegionCode(self::PAYS_DE_LA_LOIRE);
    }

    /**
     * @return PoleEmploiRegionCode
     */
    public static function PAYS_DU_NORD(): PoleEmploiRegionCode
    {
        return new PoleEmploiRegionCode(self::PAYS_DU_NORD);
    }

    /**
     * @return PoleEmploiRegionCode
     */
    public static function PICARDIE(): PoleEmploiRegionCode
    {
        return new PoleEmploiRegionCode(self::PICARDIE);
    }

    /**
     * @return PoleEmploiRegionCode
     */
    public static function POITOU_CHARENTES(): PoleEmploiRegionCode
    {
        return new PoleEmploiRegionCode(self::POITOU_CHARENTES);
    }

    /**
     * @return PoleEmploiRegionCode
     */
    public static function SAINT_PIERRE_ET_MIQUELON(): PoleEmploiRegionCode
    {
        return new PoleEmploiRegionCode(self::SAINT_PIERRE_ET_MIQUELON);
    }

    /**
     * @return PoleEmploiRegionCode
     */
    public static function SUD_EST_FRANCILIEN(): PoleEmploiRegionCode
    {
        return new PoleEmploiRegionCode(self::SUD_EST_FRANCILIEN);
    }

    /**
     * @return PoleEmploiRegionCode
     */
    public static function VALLEES_RHONE_LOIRE(): PoleEmploiRegionCode
    {
        return new PoleEmploiRegionCode(self::VALLEES_RHONE_LOIRE);
    }

    /**
     * @return PoleEmploiRegionCode[]
     */
    public static function valuesCodes(): array
    {
        return [
            new PoleEmploiRegionCode(self::ALPES),
            new PoleEmploiRegionCode(self::ALPES_PROVENCE),
            new PoleEmploiRegionCode(self::ALSACE),
            new PoleEmploiRegionCode(self::AQUITAINE),
            new PoleEmploiRegionCode(self::AUVERGNE),
            new PoleEmploiRegionCode(self::BASSE_NORMANDIE),
            new PoleEmploiRegionCode(self::BOURGOGNE),
            new PoleEmploiRegionCode(self::BRETAGNE),
            new PoleEmploiRegionCode(self::CENTRE),
            new PoleEmploiRegionCode(self::CHAMPAGNE_ARDENNE),
            new PoleEmploiRegionCode(self::CORSE),
            new PoleEmploiRegionCode(self::COTE_D_AZUR),
            new PoleEmploiRegionCode(self::EST_FRANCILIEN),
            new PoleEmploiRegionCode(self::FRANCHE_COMTE),
            new PoleEmploiRegionCode(self::GUADELOUPE),
            new PoleEmploiRegionCode(self::GUYANE),
            new PoleEmploiRegionCode(self::HAUTE_NORMANDIE),
            new PoleEmploiRegionCode(self::LA_REUNION),
            new PoleEmploiRegionCode(self::LANGUEDOC_ROUSSILLON),
            new PoleEmploiRegionCode(self::LIMOUSIN),
            new PoleEmploiRegionCode(self::LORRAINE),
            new PoleEmploiRegionCode(self::MARTINIQUE),
            new PoleEmploiRegionCode(self::MAYOTTE),
            new PoleEmploiRegionCode(self::MIDI_PYRENEES),
            new PoleEmploiRegionCode(self::OUEST_FRANCILIEN),
            new PoleEmploiRegionCode(self::PARIS),
            new PoleEmploiRegionCode(self::PAS_DE_CALAIS),
            new PoleEmploiRegionCode(self::PAYS_DE_LA_LOIRE),
            new PoleEmploiRegionCode(self::PAYS_DU_NORD),
            new PoleEmploiRegionCode(self::PICARDIE),
            new PoleEmploiRegionCode(self::POITOU_CHARENTES),
            new PoleEmploiRegionCode(self::SAINT_PIERRE_ET_MIQUELON),
            new PoleEmploiRegionCode(self::SUD_EST_FRANCILIEN),
            new PoleEmploiRegionCode(self::VALLEES_RHONE_LOIRE),
        ];
    }
}