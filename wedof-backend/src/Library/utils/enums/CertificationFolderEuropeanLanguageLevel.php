<?php


namespace App\Library\utils\enums;


use MyCLabs\Enum\Enum;

class CertificationFolderEuropeanLanguageLevel extends Enum
{
    private const C2 = 'C2';
    private const C1 = 'C1';
    private const B2 = 'B2';
    private const B1 = 'B1';
    private const A2 = 'A2';
    private const A1 = 'A1';
    private const INSUFFISANT = 'INSUFFISANT';

    /**
     * @return CertificationFolderEuropeanLanguageLevel
     */
    public static function C2(): CertificationFolderEuropeanLanguageLevel
    {
        return new CertificationFolderEuropeanLanguageLevel(self::C2);
    }

    /**
     * @return CertificationFolderEuropeanLanguageLevel
     */
    public static function C1(): CertificationFolderEuropeanLanguageLevel
    {
        return new CertificationFolderEuropeanLanguageLevel(self::C1);
    }

    /**
     * @return CertificationFolderEuropeanLanguageLevel
     */
    public static function B2(): CertificationFolderEuropeanLanguageLevel
    {
        return new CertificationFolderEuropeanLanguageLevel(self::B2);
    }

    /**
     * @return CertificationFolderEuropeanLanguageLevel
     */
    public static function B1(): CertificationFolderEuropeanLanguageLevel
    {
        return new CertificationFolderEuropeanLanguageLevel(self::B1);
    }

    /**
     * @return CertificationFolderEuropeanLanguageLevel
     */
    public static function A2(): CertificationFolderEuropeanLanguageLevel
    {
        return new CertificationFolderEuropeanLanguageLevel(self::A2);
    }

    /**
     * @return CertificationFolderEuropeanLanguageLevel
     */
    public static function A1(): CertificationFolderEuropeanLanguageLevel
    {
        return new CertificationFolderEuropeanLanguageLevel(self::A1);
    }

    /**
     * @return CertificationFolderEuropeanLanguageLevel
     */
    public static function INSUFFISANT(): CertificationFolderEuropeanLanguageLevel
    {
        return new CertificationFolderEuropeanLanguageLevel(self::INSUFFISANT);
    }

    /**
     * @param $nivelLanguage
     * @return string
     */
    public static function toFrString($nivelLanguage): string
    {
        $fr = [
            self::C2 => "C2",
            self::C1 => "C1",
            self::B2 => "B2",
            self::B1 => "B1",
            self::A2 => "A2",
            self::A1 => "A1",
            self::INSUFFISANT => "Insuffisant",
        ];
        return $fr[$nivelLanguage];
    }

    /**
     * @return CertificationFolderEuropeanLanguageLevel[]
     */
    public static function valuesTypes(): array
    {
        return [
            new CertificationFolderEuropeanLanguageLevel(self::C2),
            new CertificationFolderEuropeanLanguageLevel(self::C1),
            new CertificationFolderEuropeanLanguageLevel(self::B2),
            new CertificationFolderEuropeanLanguageLevel(self::B1),
            new CertificationFolderEuropeanLanguageLevel(self::A2),
            new CertificationFolderEuropeanLanguageLevel(self::A1),
            new CertificationFolderEuropeanLanguageLevel(self::INSUFFISANT)
        ];
    }
}