<?php

namespace App\Library\utils\enums;

use MyCLabs\Enum\Enum;

class TrainingActionStates extends Enum
{
    private const DRAFT = "draft";
    private const PUBLISHED = "published";
    private const UNPUBLISHED = "unpublished";
    private const ARCHIVED = "archived";
    private const DELETED = "deleted";

    /**
     * @return TrainingActionStates
     */
    public static function DRAFT(): TrainingActionStates
    {
        return new TrainingActionStates(self::DRAFT);
    }

    /**
     * @return TrainingActionStates
     */
    public static function PUBLISHED(): TrainingActionStates
    {
        return new TrainingActionStates(self::PUBLISHED);
    }

    /**
     * @return TrainingActionStates
     */
    public static function UNPUBLISHED(): TrainingActionStates
    {
        return new TrainingActionStates(self::UNPUBLISHED);
    }

    /**
     * @return TrainingActionStates
     */
    public static function ARCHIVED(): TrainingActionStates
    {
        return new TrainingActionStates(self::ARCHIVED);
    }

    /**
     * @return TrainingActionStates
     */
    public static function DELETED(): TrainingActionStates
    {
        return new TrainingActionStates(self::DELETED);
    }

    /**
     * @param string $state
     * @return string
     */
    public static function toFrString(string $state): string
    {
        $fr = [
            self::DRAFT => "En brouillon",
            self::PUBLISHED => "Publié",
            self::UNPUBLISHED => "Dépublié",
            self::ARCHIVED => "Archivée",
            self::DELETED => "Supprimée"
        ];
        return $fr[$state];
    }
}