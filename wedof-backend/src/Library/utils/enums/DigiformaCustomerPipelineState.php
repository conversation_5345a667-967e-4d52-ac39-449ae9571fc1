<?php

namespace App\Library\utils\enums;

use MyCLabs\Enum\Enum;

class DigiformaCustomerPipelineState extends Enum
{
    private const ENTRANT = 0;
    private const DISCUSSION = 1;
    private const DEVIS = 2;
    private const CONVENTION = 3;
    private const FACTURE = 4;

    /**
     * @return DigiformaCustomerPipelineState
     */
    public static function UNDECIDED(): DigiformaCustomerPipelineState
    {
        return new DigiformaCustomerPipelineState(self::ENTRANT);
    }

    /**
     * @return DigiformaCustomerPipelineState
     */
    public static function DISCUSSION(): DigiformaCustomerPipelineState
    {
        return new DigiformaCustomerPipelineState(self::DISCUSSION);
    }

    /**
     * @return DigiformaCustomerPipelineState
     */
    public static function DEVIS(): DigiformaCustomerPipelineState
    {
        return new DigiformaCustomerPipelineState(self::DEVIS);
    }

    /**
     * @return DigiformaCustomerPipelineState
     */
    public static function CONVENTION(): DigiformaCustomerPipelineState
    {
        return new DigiformaCustomerPipelineState(self::CONVENTION);
    }

    /**
     * @return DigiformaCustomerPipelineState
     */
    public static function FACTURE(): DigiformaCustomerPipelineState
    {
        return new DigiformaCustomerPipelineState(self::FACTURE);
    }
}