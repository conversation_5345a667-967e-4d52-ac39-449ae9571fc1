<?php

namespace App\Library\utils\enums;

use MyCLabs\Enum\Enum;

class CertificationPartnerAuditResults extends Enum
{
    private const COMPLIANT = 'compliant';
    private const PARTIALLY_COMPLIANT = 'partiallyCompliant';
    private const NON_COMPLIANT = 'nonCompliant';
    private const NONE = 'none';
    private const ALL = 'all';

    /**
     * @return CertificationPartnerAuditResults
     */
    public static function COMPLIANT(): CertificationPartnerAuditResults
    {
        return new CertificationPartnerAuditResults(self::COMPLIANT);
    }

    /**
     * @return CertificationPartnerAuditResults
     */
    public static function PARTIALLY_COMPLIANT(): CertificationPartnerAuditResults
    {
        return new CertificationPartnerAuditResults(self::PARTIALLY_COMPLIANT);
    }

    /**
     * @return CertificationPartnerAuditResults
     */
    public static function NON_COMPLIANT(): CertificationPartnerAuditResults
    {
        return new CertificationPartnerAuditResults(self::NON_COMPLIANT);
    }

    /**
     * @return CertificationPartnerAuditResults
     */
    public static function NONE(): CertificationPartnerAuditResults
    {
        return new CertificationPartnerAuditResults(self::NONE);
    }

    /**
     * @return CertificationPartnerAuditResults
     */
    public static function ALL(): CertificationPartnerAuditResults
    {
        return new CertificationPartnerAuditResults(self::ALL);
    }

    /**
     * @param string $result
     * @return string
     */
    public static function toFrString(string $result): string
    {
        $fr = [
            self::COMPLIANT => 'Conforme',
            self::PARTIALLY_COMPLIANT => 'Partiellement conforme',
            self::NON_COMPLIANT => 'Non conforme',
        ];
        return $fr[$result];
    }

    /**
     * @return CertificationPartnerAuditResults[]
     */
    public static function valuesResults(): array
    {
        return [
            new CertificationPartnerAuditResults(self::COMPLIANT),
            new CertificationPartnerAuditResults(self::PARTIALLY_COMPLIANT),
            new CertificationPartnerAuditResults(self::NON_COMPLIANT),
            new CertificationPartnerAuditResults(self::NONE),
            new CertificationPartnerAuditResults(self::ALL)
        ];
    }
}