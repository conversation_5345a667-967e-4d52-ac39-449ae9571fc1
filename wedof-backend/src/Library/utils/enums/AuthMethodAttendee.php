<?php

namespace App\Library\utils\enums;

use MyCLabs\Enum\Enum;

class AuthMethodAttendee extends Enum
{
    private const MAGIC_LINK = 'magicLink';
    private const IDENTIFICATION_360 = 'id360';

    /**
     * @return AuthMethodAttendee
     */
    public static function MAGIC_LINK(): AuthMethodAttendee
    {
        return new AuthMethodAttendee(self::MAGIC_LINK);
    }

    /**
     * @return AuthMethodAttendee
     */
    public static function IDENTIFICATION_360(): AuthMethodAttendee
    {
        return new AuthMethodAttendee(self::IDENTIFICATION_360);
    }

}
