<?php

namespace App\Library\utils\enums;

use MyCLabs\Enum\Enum;

class CertificationPartnerAuditStates extends Enum
{
    private const PENDING_COMPUTATION = 'pendingComputation';
    private const COMPUTING = 'computing';
    private const IN_PROGRESS = 'inProgress';
    private const COMPLETED = 'completed';
    private const FAILED = 'failed';
    private const ALL = 'all';

    /**
     * @return CertificationPartnerAuditStates
     */
    public static function PENDING_COMPUTATION(): CertificationPartnerAuditStates
    {
        return new CertificationPartnerAuditStates(self::PENDING_COMPUTATION);
    }

    /**
     * @return CertificationPartnerAuditStates
     */
    public static function IN_PROGRESS(): CertificationPartnerAuditStates
    {
        return new CertificationPartnerAuditStates(self::IN_PROGRESS);
    }

    /**
     * @return CertificationPartnerAuditStates
     */
    public static function COMPUTING(): CertificationPartnerAuditStates
    {
        return new CertificationPartnerAuditStates(self::COMPUTING);
    }

    /**
     * @return CertificationPartnerAuditStates
     */
    public static function COMPLETED(): CertificationPartnerAuditStates
    {
        return new CertificationPartnerAuditStates(self::COMPLETED);
    }

    /**
     * @return CertificationPartnerAuditStates
     */
    public static function FAILED(): CertificationPartnerAuditStates
    {
        return new CertificationPartnerAuditStates(self::FAILED);
    }

    /**
     * @return CertificationPartnerAuditStates
     */
    public static function ALL(): CertificationPartnerAuditStates
    {
        return new CertificationPartnerAuditStates(self::ALL);
    }

    /**
     * @param string $result
     * @return string
     */
    public static function toFrString(string $result): string
    {
        // used for Message & Notifications
        $fr = [
            self::PENDING_COMPUTATION => 'En préparation : collecte des données en cours',
            self::COMPUTING => 'Analyse des données en cours',
            self::IN_PROGRESS => 'En cours ',
            self::COMPLETED => 'Finalisé',
        ];
        return $fr[$result];
    }

    /**
     * @return CertificationPartnerAuditStates[]
     */
    public static function valuesStates(): array
    {
        return [
            new CertificationPartnerAuditStates(self::PENDING_COMPUTATION),
            new CertificationPartnerAuditStates(self::IN_PROGRESS),
            new CertificationPartnerAuditStates(self::COMPLETED),
            new CertificationPartnerAuditStates(self::FAILED),
            new CertificationPartnerAuditStates(self::ALL)
        ];
    }
}