<?php

namespace App\Library\utils\enums;

use MyCLabs\Enum\Enum;

class RegistrationFolderStates extends Enum
{
    private const NOT_PROCESSED = "notProcessed";
    private const VALIDATED = "validated";
    private const WAITING_ACCEPTATION = "waitingAcceptation";
    private const ACCEPTED = "accepted";
    private const IN_TRAINING = "inTraining";
    private const TERMINATED = "terminated";
    private const SERVICE_DONE_DECLARED = "serviceDoneDeclared";
    private const SERVICE_DONE_VALIDATED = "serviceDoneValidated";
    private const CANCELED_BY_ATTENDEE = "canceledByAttendee";
    private const CANCELED_BY_ATTENDEE_NOT_REALIZED = "canceledByAttendeeNotRealized";
    private const CANCELED_BY_ORGANISM = "canceledByOrganism";
    private const CANCELED_BY_FINANCER = "canceledByFinancer";
    private const CANCELED_BY_CDC = "canceledByCDC"; // équivalent du canceledByFinancer pour les dossiers CPF -> remplacé par canceledByFinancer en BDD
    private const REFUSED_BY_ATTENDEE = "refusedByAttendee";
    private const REFUSED_BY_ORGANISM = "refusedByOrganism";
    private const REFUSED_BY_FINANCER = "refusedByFinancer";
    private const REJECTED_WITHOUT_TITULAIRE_SUITE = "rejectedWithoutTitulaireSuite";
    private const REJECTED_WITHOUT_CDC_SUITE = 'rejectedWithoutCdcSuite';
    private const REJECTED_WITHOUT_OF_SUITE = 'rejectedWithoutOfSuite';
    private const REJECTED = "rejected";
    // ne sont pas des états de dossiers mais des états pour interroger l'api CPF
    private const CANCELED = "canceled";
    private const REFUSED = "refused";
    private const ALL = "all";
    private const ALL_OPENED = "allOpened";
    private const ALL_CLOSED = "allClosed";
    private const TO_BILL = "allBilling";
    private const BILLED = "billed";
    // ne sont pas des états de dossiers mais des paramètres pour changer l'état des dossiers dans CPF
    private const UPDATE_TO_CANCELED = "CANCELED";
    private const UPDATE_TO_IN_TRAINING = "IN_TRAINING";
    private const UPDATE_TO_OPENED = "OPENED";
    private const UPDATE_TO_REFUSED = "REFUSED";
    private const UPDATE_TO_SERVICE_DONE_DECLARED = "SERVICE_DONE_DECLARED";
    private const UPDATE_TO_SERVICE_DONE_VALIDATED = "SERVICE_DONE_VALIDATED";
    private const UPDATE_TO_TERMINATED = "TERMINATED";
    private const UPDATE_TO_VALIDATED = "VALIDATED";

    /**
     * @return RegistrationFolderStates
     */
    public static function ACCEPTED(): RegistrationFolderStates
    {
        return new RegistrationFolderStates(self::ACCEPTED);
    }

    /**
     * @return RegistrationFolderStates
     */
    public static function ALL(): RegistrationFolderStates
    {
        return new RegistrationFolderStates(self::ALL);
    }

    /**
     * @return RegistrationFolderStates
     */
    public static function ALL_OPENED(): RegistrationFolderStates
    {
        return new RegistrationFolderStates(self::ALL_OPENED);
    }

    /**
     * @return RegistrationFolderStates
     */
    public static function ALL_CLOSED(): RegistrationFolderStates
    {
        return new RegistrationFolderStates(self::ALL_CLOSED);
    }

    /**
     * @return RegistrationFolderStates
     */
    public static function TO_BILL(): RegistrationFolderStates
    {
        return new RegistrationFolderStates(self::TO_BILL);
    }

    /**
     * @return RegistrationFolderStates
     */
    public static function BILLED(): RegistrationFolderStates
    {
        return new RegistrationFolderStates(self::BILLED);
    }

    /**
     * @return RegistrationFolderStates
     */
    public static function CANCELED_BY_ATTENDEE(): RegistrationFolderStates
    {
        return new RegistrationFolderStates(self::CANCELED_BY_ATTENDEE);
    }

    /**
     * @return RegistrationFolderStates
     */
    public static function CANCELED_BY_ATTENDEE_NOT_REALIZED(): RegistrationFolderStates
    {
        return new RegistrationFolderStates(self::CANCELED_BY_ATTENDEE_NOT_REALIZED);
    }

    /**
     * @return RegistrationFolderStates
     */
    public static function CANCELED_BY_ORGANISM(): RegistrationFolderStates
    {
        return new RegistrationFolderStates(self::CANCELED_BY_ORGANISM);
    }

    /**
     * @return RegistrationFolderStates
     */
    public static function CANCELED_BY_FINANCER(): RegistrationFolderStates
    {
        return new RegistrationFolderStates(self::CANCELED_BY_FINANCER);
    }

    /**
     * @return RegistrationFolderStates
     */
    public static function CANCELED_BY_CDC(): RegistrationFolderStates
    {
        return new RegistrationFolderStates(self::CANCELED_BY_CDC);
    }

    /**
     * @return RegistrationFolderStates
     */
    public static function CANCELED(): RegistrationFolderStates
    {
        return new RegistrationFolderStates(self::CANCELED);
    }

    /**
     * @return RegistrationFolderStates
     */
    public static function IN_TRAINING(): RegistrationFolderStates
    {
        return new RegistrationFolderStates(self::IN_TRAINING);
    }

    /**
     * @return RegistrationFolderStates
     */
    public static function NOT_PROCESSED(): RegistrationFolderStates
    {
        return new RegistrationFolderStates(self::NOT_PROCESSED);
    }

    /**
     * @return RegistrationFolderStates
     */
    public static function REFUSED_BY_ATTENDEE(): RegistrationFolderStates
    {
        return new RegistrationFolderStates(self::REFUSED_BY_ATTENDEE);
    }

    /**
     * @return RegistrationFolderStates
     */
    public static function REFUSED_BY_ORGANISM(): RegistrationFolderStates
    {
        return new RegistrationFolderStates(self::REFUSED_BY_ORGANISM);
    }

    /**
     * @return RegistrationFolderStates
     */
    public static function REFUSED_BY_FINANCER(): RegistrationFolderStates
    {
        return new RegistrationFolderStates(self::REFUSED_BY_FINANCER);
    }

    /**
     * @return RegistrationFolderStates
     */
    public static function REFUSED(): RegistrationFolderStates
    {
        return new RegistrationFolderStates(self::REFUSED);
    }

    /**
     * @return RegistrationFolderStates
     */
    public static function REJECTED_WITHOUT_TITULAIRE_SUITE(): RegistrationFolderStates
    {
        return new RegistrationFolderStates(self::REJECTED_WITHOUT_TITULAIRE_SUITE);
    }

    /**
     * @return RegistrationFolderStates
     */
    public static function REJECTED_WITHOUT_CDC_SUITE(): RegistrationFolderStates
    {
        return new RegistrationFolderStates(self::REJECTED_WITHOUT_CDC_SUITE);
    }

    /**
     * @return RegistrationFolderStates
     */
    public static function REJECTED_WITHOUT_OF_SUITE(): RegistrationFolderStates
    {
        return new RegistrationFolderStates(self::REJECTED_WITHOUT_OF_SUITE);
    }

    /**
     * @return RegistrationFolderStates
     */
    public static function REJECTED(): RegistrationFolderStates
    {
        return new RegistrationFolderStates(self::REJECTED);
    }

    /**
     * @return RegistrationFolderStates
     */
    public static function SERVICE_DONE_DECLARED(): RegistrationFolderStates
    {
        return new RegistrationFolderStates(self::SERVICE_DONE_DECLARED);
    }

    /**
     * @return RegistrationFolderStates
     */
    public static function SERVICE_DONE_VALIDATED(): RegistrationFolderStates
    {
        return new RegistrationFolderStates(self::SERVICE_DONE_VALIDATED);
    }

    /**
     * @return RegistrationFolderStates
     */
    public static function TERMINATED(): RegistrationFolderStates
    {
        return new RegistrationFolderStates(self::TERMINATED);
    }

    /**
     * @return RegistrationFolderStates
     */
    public static function VALIDATED(): RegistrationFolderStates
    {
        return new RegistrationFolderStates(self::VALIDATED);
    }

    /**
     * @return RegistrationFolderStates
     */
    public static function WAITING_ACCEPTATION(): RegistrationFolderStates
    {
        return new RegistrationFolderStates(self::WAITING_ACCEPTATION);
    }

    /**
     * @return RegistrationFolderStates
     */
    public static function UPDATE_TO_CANCELED(): RegistrationFolderStates
    {
        return new RegistrationFolderStates(self::UPDATE_TO_CANCELED);
    }

    /**
     * @return RegistrationFolderStates
     */
    public static function UPDATE_TO_IN_TRAINING(): RegistrationFolderStates
    {
        return new RegistrationFolderStates(self::UPDATE_TO_IN_TRAINING);
    }

    /**
     * @return RegistrationFolderStates
     */
    public static function UPDATE_TO_OPENED(): RegistrationFolderStates
    {
        return new RegistrationFolderStates(self::UPDATE_TO_OPENED);
    }

    /**
     * @return RegistrationFolderStates
     */
    public static function UPDATE_TO_REFUSED(): RegistrationFolderStates
    {
        return new RegistrationFolderStates(self::UPDATE_TO_REFUSED);
    }

    /**
     * @return RegistrationFolderStates
     */
    public static function UPDATE_TO_SERVICE_DONE_DECLARED(): RegistrationFolderStates
    {
        return new RegistrationFolderStates(self::UPDATE_TO_SERVICE_DONE_DECLARED);
    }

    /**
     * @return RegistrationFolderStates
     */
    public static function UPDATE_TO_SERVICE_DONE_VALIDATED(): RegistrationFolderStates
    {
        return new RegistrationFolderStates(self::UPDATE_TO_SERVICE_DONE_VALIDATED);
    }

    /**
     * @return RegistrationFolderStates
     */
    public static function UPDATE_TO_TERMINATED(): RegistrationFolderStates
    {
        return new RegistrationFolderStates(self::UPDATE_TO_TERMINATED);
    }

    /**
     * @return RegistrationFolderStates
     */
    public static function UPDATE_TO_VALIDATED(): RegistrationFolderStates
    {
        return new RegistrationFolderStates(self::UPDATE_TO_VALIDATED);
    }

    /**
     * @param $state
     * @return string
     */
    public static function toFrString($state): string
    {
        $fr = [
            self::CANCELED_BY_ATTENDEE => "Annulé (par le titulaire)",
            self::CANCELED_BY_ATTENDEE_NOT_REALIZED => "Annulation titulaire (non réalisé)",
            self::CANCELED_BY_ORGANISM => "Annulé (par l'organisme)",
            self::CANCELED_BY_FINANCER => "Annulé (par le financeur)",
            self::REFUSED_BY_ATTENDEE => "Refus titulaire",
            self::REFUSED_BY_ORGANISM => "Refusé (par l'organisme)",
            self::REFUSED_BY_FINANCER => "Refusé (par le financeur)",
            self::REJECTED_WITHOUT_TITULAIRE_SUITE => "Annulé sans suite",
            self::REJECTED_WITHOUT_CDC_SUITE => "Annulé sans suite",
            self::REJECTED_WITHOUT_OF_SUITE => "Annulé sans suite",
            self::REJECTED => "Annulé sans suite",
            self::NOT_PROCESSED => "À traiter",
            self::VALIDATED => "Validé",
            self::WAITING_ACCEPTATION => "Validé (En cours d'instruction Pôle emploi)",
            self::ACCEPTED => "Accepté",
            self::IN_TRAINING => "En formation",
            self::TERMINATED => "Sortie de formation",
            self::SERVICE_DONE_DECLARED => "Service fait déclaré",
            self::SERVICE_DONE_VALIDATED => "Service fait validé",
            self::ALL => "Tout"
        ];
        return $fr[$state];
    }

    /**
     * @return RegistrationFolderStates[]
     */
    public static function valuesStates(): array
    {
        //THE POSITION IN THIS ARRAY IS EXTREMELY IMPORTANT
        return [
            new RegistrationFolderStates(self::CANCELED_BY_ATTENDEE),
            new RegistrationFolderStates(self::CANCELED_BY_ATTENDEE_NOT_REALIZED),
            new RegistrationFolderStates(self::CANCELED_BY_ORGANISM),
            new RegistrationFolderStates(self::CANCELED_BY_FINANCER),
            new RegistrationFolderStates(self::REFUSED_BY_ATTENDEE),
            new RegistrationFolderStates(self::REFUSED_BY_ORGANISM),
            new RegistrationFolderStates(self::REFUSED_BY_FINANCER),
            new RegistrationFolderStates(self::REJECTED_WITHOUT_TITULAIRE_SUITE),
            new RegistrationFolderStates(self::REJECTED_WITHOUT_CDC_SUITE),
            new RegistrationFolderStates(self::REJECTED_WITHOUT_OF_SUITE),
            new RegistrationFolderStates(self::REJECTED),
            new RegistrationFolderStates(self::NOT_PROCESSED),
            new RegistrationFolderStates(self::VALIDATED),
            new RegistrationFolderStates(self::WAITING_ACCEPTATION),
            new RegistrationFolderStates(self::ACCEPTED),
            new RegistrationFolderStates(self::IN_TRAINING),
            new RegistrationFolderStates(self::TERMINATED),
            new RegistrationFolderStates(self::SERVICE_DONE_DECLARED),
            new RegistrationFolderStates(self::SERVICE_DONE_VALIDATED),
            new RegistrationFolderStates(self::ALL)];
    }

    /**
     * @return string[]
     */
    public static function valuesStatesToString(): array
    {
        return [
            self::CANCELED_BY_ATTENDEE,
            self::CANCELED_BY_ATTENDEE_NOT_REALIZED,
            self::CANCELED_BY_ORGANISM,
            self::CANCELED_BY_FINANCER,
            self::REFUSED_BY_ATTENDEE,
            self::REFUSED_BY_ORGANISM,
            self::REFUSED_BY_FINANCER,
            self::REJECTED_WITHOUT_TITULAIRE_SUITE,
            self::REJECTED_WITHOUT_CDC_SUITE,
            self::REJECTED_WITHOUT_OF_SUITE,
            self::REJECTED,
            self::NOT_PROCESSED,
            self::VALIDATED,
            self::WAITING_ACCEPTATION,
            self::ACCEPTED,
            self::IN_TRAINING,
            self::TERMINATED,
            self::SERVICE_DONE_DECLARED,
            self::SERVICE_DONE_VALIDATED
        ];
    }

    /**
     * @return RegistrationFolderStates[]
     */
    public static function updatableStates(): array
    {
        return [new RegistrationFolderStates(self::NOT_PROCESSED),
            new RegistrationFolderStates(self::ACCEPTED),
            new RegistrationFolderStates(self::VALIDATED),
            new RegistrationFolderStates(self::WAITING_ACCEPTATION),
            new RegistrationFolderStates(self::IN_TRAINING),
            new RegistrationFolderStates(self::TERMINATED),
            new RegistrationFolderStates(self::SERVICE_DONE_DECLARED),
            new RegistrationFolderStates(self::SERVICE_DONE_VALIDATED)]; //because billingState can still change in this state
    }

    /**
     * @return RegistrationFolderStates[]
     */
    public static function updatableStatesToString(): array
    {
        return [self::NOT_PROCESSED,
            self::ACCEPTED,
            self::VALIDATED,
            self::WAITING_ACCEPTATION,
            self::SERVICE_DONE_VALIDATED]; //because billingState can still change in this state
    }

    /**
     * @return RegistrationFolderStates[]
     */
    public static function finalStates(): array
    {
        return [
            new RegistrationFolderStates(self::CANCELED_BY_ATTENDEE),
            new RegistrationFolderStates(self::REFUSED_BY_ATTENDEE),
            new RegistrationFolderStates(self::REFUSED_BY_ORGANISM),
            new RegistrationFolderStates(self::REJECTED_WITHOUT_TITULAIRE_SUITE),
            new RegistrationFolderStates(self::REJECTED),
            new RegistrationFolderStates(self::REJECTED_WITHOUT_CDC_SUITE),
            new RegistrationFolderStates(self::REJECTED_WITHOUT_OF_SUITE)
        ];
    }

    /**
     * @return RegistrationFolderStates[]
     */
    public static function updateToStates(): array
    {
        return [new RegistrationFolderStates(self::UPDATE_TO_CANCELED),
            new RegistrationFolderStates(self::UPDATE_TO_IN_TRAINING),
            new RegistrationFolderStates(self::UPDATE_TO_OPENED),
            new RegistrationFolderStates(self::UPDATE_TO_REFUSED),
            new RegistrationFolderStates(self::UPDATE_TO_SERVICE_DONE_DECLARED),
            new RegistrationFolderStates(self::UPDATE_TO_SERVICE_DONE_VALIDATED),
            new RegistrationFolderStates(self::UPDATE_TO_TERMINATED),
            new RegistrationFolderStates(self::UPDATE_TO_VALIDATED)];
    }

    /**
     * @return RegistrationFolderStates[]
     */
    public static function certifierAuthorizedStates(): array
    {
        return [
            new RegistrationFolderStates(self::ACCEPTED),
            new RegistrationFolderStates(self::CANCELED_BY_ATTENDEE_NOT_REALIZED),
            new RegistrationFolderStates(self::CANCELED_BY_ORGANISM),
            new RegistrationFolderStates(self::IN_TRAINING),
            new RegistrationFolderStates(self::TERMINATED),
            new RegistrationFolderStates(self::SERVICE_DONE_DECLARED),
            new RegistrationFolderStates(self::SERVICE_DONE_VALIDATED)
        ];
    }

    /**
     * @return RegistrationFolderStates[]
     */
    public static function activeAttendeeStates(): array
    {
        return [
            new RegistrationFolderStates(self::NOT_PROCESSED),
            new RegistrationFolderStates(self::VALIDATED),
            new RegistrationFolderStates(self::WAITING_ACCEPTATION),
            new RegistrationFolderStates(self::ACCEPTED),
            new RegistrationFolderStates(self::IN_TRAINING),
            new RegistrationFolderStates(self::TERMINATED),
            new RegistrationFolderStates(self::SERVICE_DONE_DECLARED),
            new RegistrationFolderStates(self::SERVICE_DONE_VALIDATED)
        ];
    }

    /**
     * @return RegistrationFolderStates[]
     */
    public static function billableStates(): array
    {
        return [
            new RegistrationFolderStates(self::SERVICE_DONE_VALIDATED),
            new RegistrationFolderStates(self::CANCELED_BY_ORGANISM),
            new RegistrationFolderStates(self::CANCELED_BY_ATTENDEE_NOT_REALIZED)
        ];
    }

    /**
     * @param string $originState
     * @param RegistrationFolderStates $targetState
     * @return bool
     */
    public static function isForwardTransition(string $originState, RegistrationFolderStates $targetState): bool
    {
        $forwardStates = [
            self::NOT_PROCESSED => [
                self::VALIDATED(),
                self::WAITING_ACCEPTATION(),
                self::ACCEPTED(),
                self::IN_TRAINING(),
                self::TERMINATED(),
                self::SERVICE_DONE_DECLARED(),
                self::SERVICE_DONE_VALIDATED(),
                self::CANCELED_BY_ATTENDEE(),
                self::CANCELED_BY_ATTENDEE_NOT_REALIZED(),
                self::CANCELED_BY_ORGANISM(),
                self::CANCELED_BY_FINANCER(),
                self::REFUSED_BY_ATTENDEE(),
                self::REFUSED_BY_ORGANISM(),
                self::REFUSED_BY_FINANCER(),
                self::REJECTED_WITHOUT_TITULAIRE_SUITE(),
                self::REJECTED_WITHOUT_CDC_SUITE(),
                self::REJECTED_WITHOUT_OF_SUITE()
            ],
            self::VALIDATED => [
                self::WAITING_ACCEPTATION(),
                self::ACCEPTED(),
                self::IN_TRAINING(),
                self::TERMINATED(),
                self::SERVICE_DONE_DECLARED(),
                self::SERVICE_DONE_VALIDATED(),
                self::CANCELED_BY_ATTENDEE_NOT_REALIZED(),
                self::CANCELED_BY_FINANCER(),
                self::REFUSED_BY_ATTENDEE(),
                self::REFUSED_BY_FINANCER(),
                self::REJECTED_WITHOUT_TITULAIRE_SUITE(),
                self::REJECTED_WITHOUT_CDC_SUITE(),
                self::REJECTED_WITHOUT_OF_SUITE()
            ],
            self::WAITING_ACCEPTATION => [
                self::VALIDATED(),
                self::ACCEPTED(),
                self::IN_TRAINING(),
                self::TERMINATED(),
                self::SERVICE_DONE_DECLARED(),
                self::SERVICE_DONE_VALIDATED(),
                self::CANCELED_BY_ATTENDEE_NOT_REALIZED(),
                self::CANCELED_BY_ORGANISM(),
                self::CANCELED_BY_FINANCER(),
                self::REFUSED_BY_ATTENDEE(),
                self::REFUSED_BY_FINANCER(),
                self::REJECTED_WITHOUT_TITULAIRE_SUITE()
            ],
            self::ACCEPTED => [
                self::IN_TRAINING(),
                self::TERMINATED(),
                self::SERVICE_DONE_DECLARED(),
                self::SERVICE_DONE_VALIDATED(),
                self::REJECTED_WITHOUT_TITULAIRE_SUITE(),
                self::REFUSED_BY_ORGANISM()
            ],
            self::IN_TRAINING => [
                self::TERMINATED(),
                self::SERVICE_DONE_DECLARED(),
                self::SERVICE_DONE_VALIDATED()
            ],
            self::TERMINATED => [
                self::SERVICE_DONE_DECLARED(),
                self::SERVICE_DONE_VALIDATED()
            ],
            self::SERVICE_DONE_DECLARED => [self::SERVICE_DONE_VALIDATED()],
            self::SERVICE_DONE_VALIDATED => [self::SERVICE_DONE_VALIDATED()],
            self::CANCELED_BY_ATTENDEE => [],
            self::CANCELED_BY_ATTENDEE_NOT_REALIZED => [],
            self::CANCELED_BY_ORGANISM => [],
            self::CANCELED_BY_FINANCER => [],
            self::CANCELED_BY_CDC => [],
            self::REFUSED_BY_ATTENDEE => [],
            self::REFUSED_BY_ORGANISM => [],
            self::REFUSED_BY_FINANCER => [],
            self::REJECTED_WITHOUT_TITULAIRE_SUITE => [],
            self::REJECTED_WITHOUT_CDC_SUITE => [],
            self::REJECTED => [],
        ];
        return in_array($targetState, $forwardStates[$originState]);
    }
}
