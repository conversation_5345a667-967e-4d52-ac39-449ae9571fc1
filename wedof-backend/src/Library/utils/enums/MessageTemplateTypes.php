<?php

namespace App\Library\utils\enums;

use MyCLabs\Enum\Enum;

class MessageTemplateTypes extends Enum
{
    private const SMS = 'sms';
    private const EMAIL = 'email';
    private const ALL = 'all';

    /**
     * @return MessageTemplateTypes
     */
    public static function SMS(): MessageTemplateTypes
    {
        return new MessageTemplateTypes(self::SMS);
    }

    /**
     * @return MessageTemplateTypes
     */
    public static function EMAIL(): MessageTemplateTypes
    {
        return new MessageTemplateTypes(self::EMAIL);
    }

    /**
     * @return MessageTemplateTypes
     */
    public static function ALL(): MessageTemplateTypes
    {
        return new MessageTemplateTypes(self::ALL);
    }

    /**
     * @param $state
     * @return string
     */
    public static function toFrString($state): string
    {
        $fr = [
            self::SMS => "sms",
            self::EMAIL => "email",
        ];
        return $fr[$state];
    }

    /**
     * @return MessageTemplateTypes[]
     */
    public static function valuesTypes(): array
    {
        return [
            new MessageTemplateTypes(self::SMS),
            new MessageTemplateTypes(self::EMAIL),
            new MessageTemplateTypes(self::ALL)
        ];
    }

    /**
     * @return string[]
     */
    public static function valuesToString(): array
    {
        return [
            self::EMAIL,
            self::SMS
        ];
    }
}