<?php

namespace App\Library\utils\enums;

use MyCLabs\Enum\Enum;

class CertificationPartnerStates extends Enum
{
    private const DRAFT = 'draft';
    private const PROCESSING = 'processing';
    private const ACTIVE = 'active';
    private const ABORTED = 'aborted';
    private const REFUSED = 'refused';
    private const REVOKED = 'revoked';
    private const SUSPENDED = 'suspended';

    private const ALL = 'all';

    /**
     * @return CertificationPartnerStates
     */
    public static function DRAFT(): CertificationPartnerStates
    {
        return new CertificationPartnerStates(self::DRAFT);
    }

    /**
     * @return CertificationPartnerStates
     */
    public static function PROCESSING(): CertificationPartnerStates
    {
        return new CertificationPartnerStates(self::PROCESSING);
    }

    /**
     * @return CertificationPartnerStates
     */
    public static function ACTIVE(): CertificationPartnerStates
    {
        return new CertificationPartnerStates(self::ACTIVE);
    }

    /**
     * @return CertificationPartnerStates
     */
    public static function ABORTED(): CertificationPartnerStates
    {
        return new CertificationPartnerStates(self::ABORTED);
    }

    /**
     * @return CertificationPartnerStates
     */
    public static function REFUSED(): CertificationPartnerStates
    {
        return new CertificationPartnerStates(self::REFUSED);
    }

    /**
     * @return CertificationPartnerStates
     */
    public static function REVOKED(): CertificationPartnerStates
    {
        return new CertificationPartnerStates(self::REVOKED);
    }

    /**
     * @return CertificationPartnerStates
     */
    public static function SUSPENDED(): CertificationPartnerStates
    {
        return new CertificationPartnerStates(self::SUSPENDED);
    }

    /**
     * @return CertificationPartnerStates
     */
    public static function ALL(): CertificationPartnerStates
    {
        return new CertificationPartnerStates(self::ALL);
    }

    /**
     * @param $state
     * @return string
     */
    public static function toFrString($state): string
    {
        $fr = [
            self::DRAFT => "Demande à compléter",
            self::PROCESSING => "Demande en traitement",
            self::ACTIVE => "Partenariat actif",
            self::ABORTED => "Demande abandonnée",
            self::REFUSED => "Demande refusée",
            self::REVOKED => "Partenariat révoqué",
            self::SUSPENDED => "Partenariat suspendu"
        ];
        return $fr[$state];
    }

    /**
     * @return CertificationPartnerStates[]
     */
    public static function valuesStates(): array
    {
        return [
            new CertificationPartnerStates(self::DRAFT),
            new CertificationPartnerStates(self::PROCESSING),
            new CertificationPartnerStates(self::ACTIVE),
            new CertificationPartnerStates(self::ABORTED),
            new CertificationPartnerStates(self::REFUSED),
            new CertificationPartnerStates(self::REVOKED),
            new CertificationPartnerStates(self::SUSPENDED),
            new CertificationPartnerStates(self::ALL)
        ];
    }

    /**
     * @param string $originState
     * @param CertificationPartnerStates $targetState
     * @return bool
     */
    public static function isForwardTransition(string $originState, CertificationPartnerStates $targetState): bool
    {
        $forwardStates = [
            self::DRAFT => [],
            self::PROCESSING => [self::ACTIVE(), self::ABORTED(), self::REVOKED(), self::REFUSED(), self::SUSPENDED()],
            self::ACTIVE => [],
            self::ABORTED => [],
            self::REFUSED => [],
            self::REVOKED => [],
            self::SUSPENDED => []
        ];
        return in_array($targetState, $forwardStates[$originState]);
    }

    /**
     * @return array
     */
    public static function allExceptDraft(): array
    {
        return array_filter(self::valuesStates(), function ($e) {
            return ($e->getValue() !== self::DRAFT);
        });
    }
}
