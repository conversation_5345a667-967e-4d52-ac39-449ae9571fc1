<?php


namespace App\Library\utils\enums;


use MyCLabs\Enum\Enum;

class CertificationFoldersCdcFilesStates extends Enum
{
    private const EXPORTED = "exported";
    private const PROCESSED_OK = "processedOk";
    private const PROCESSED_KO = "processedKo";
    private const ABORTED = "aborted";

    /**
     * @return CertificationFoldersCdcFilesStates
     */
    public static function EXPORTED(): CertificationFoldersCdcFilesStates
    {
        return new CertificationFoldersCdcFilesStates(self::EXPORTED);
    }

    /**
     * @return CertificationFoldersCdcFilesStates
     */
    public static function PROCESSED_OK(): CertificationFoldersCdcFilesStates
    {
        return new CertificationFoldersCdcFilesStates(self::PROCESSED_OK);
    }

    /**
     * @return CertificationFoldersCdcFilesStates
     */
    public static function PROCESSED_KO(): CertificationFoldersCdcFilesStates
    {
        return new CertificationFoldersCdcFilesStates(self::PROCESSED_KO);
    }

    /**
     * @return CertificationFoldersCdcFilesStates
     */
    public static function ABORTED(): CertificationFoldersCdcFilesStates
    {
        return new CertificationFoldersCdcFilesStates(self::ABORTED);
    }
}
