<?php

namespace App\Library\utils\enums;

use MyCLabs\Enum\Enum;

class InvoiceStates extends Enum
{
    private const WAITING_PAYMENT = "waitingPayment";
    private const PAID = "paid";
    private const CANCELED = "canceled";


    /**
     * @return InvoiceStates
     */
    public static function WAITING_PAYMENT(): InvoiceStates
    {
        return new InvoiceStates(self::WAITING_PAYMENT);
    }

    /**
     * @return InvoiceStates
     */
    public static function PAID(): InvoiceStates
    {
        return new InvoiceStates(self::PAID);
    }

    public static function CANCELED(): InvoiceStates
    {
        return new InvoiceStates(self::CANCELED);
    }

    /**
     * @param $state
     * @return string
     */
    public static function toFrString($state): string
    {
        $fr = [
            self::WAITING_PAYMENT => "en cours",
            self::PAID => "payée",
            self::CANCELED => "annulée"
        ];
        return $fr[$state];
    }
}
