<?php

namespace App\Library\utils\enums;

use MyCLabs\Enum\Enum;

class RegistrationFolderBillingStates extends Enum
{
    private const NOT_BILLABLE = "notBillable";
    private const DEPOSIT_WAIT = "depositWait";
    private const DEPOSIT_PAID = "depositPaid";
    private const TO_BILL = "toBill";
    private const BILLED = "billed";
    private const PAID = "paid";

    private const ALL = "all";

    /**
     * @return RegistrationFolderBillingStates
     */
    public static function NOT_BILLABLE(): RegistrationFolderBillingStates
    {
        return new RegistrationFolderBillingStates(self::NOT_BILLABLE);
    }

    /**
     * @return RegistrationFolderBillingStates
     */
    public static function DEPOSIT_WAIT(): RegistrationFolderBillingStates
    {
        return new RegistrationFolderBillingStates(self::DEPOSIT_WAIT);
    }

    /**
     * @return RegistrationFolderBillingStates
     */
    public static function DEPOSIT_PAID(): RegistrationFolderBillingStates
    {
        return new RegistrationFolderBillingStates(self::DEPOSIT_PAID);
    }

    /**
     * @return RegistrationFolderBillingStates
     */
    public static function TO_BILL(): RegistrationFolderBillingStates
    {
        return new RegistrationFolderBillingStates(self::TO_BILL);
    }

    /**
     * @return RegistrationFolderBillingStates
     */
    public static function BILLED(): RegistrationFolderBillingStates
    {
        return new RegistrationFolderBillingStates(self::BILLED);
    }

    /**
     * @return RegistrationFolderBillingStates
     */
    public static function PAID(): RegistrationFolderBillingStates
    {
        return new RegistrationFolderBillingStates(self::PAID);
    }

    /**
     * @return RegistrationFolderBillingStates
     */
    public static function ALL(): RegistrationFolderBillingStates
    {
        return new RegistrationFolderBillingStates(self::ALL);
    }


    /**
     * @param $state
     * @return string
     */
    public static function toFrString($state): string
    {
        $fr = [
            self::NOT_BILLABLE => "Non facturable",
            self::DEPOSIT_WAIT => "Acompte en attente",
            self::DEPOSIT_PAID => "Acompte payé",
            self::TO_BILL => "À facturer",
            self::BILLED => "Facturé",
            self::PAID => "Payé"
        ];
        return $fr[$state];
    }

    /**
     * @return RegistrationFolderBillingStates[]
     */
    public static function valuesStates(): array
    {
        //THE POSITION IN THIS ARRAY IS EXTREMELY IMPORTANT
        return [
            new RegistrationFolderBillingStates(self::NOT_BILLABLE),
            new RegistrationFolderBillingStates(self::DEPOSIT_WAIT),
            new RegistrationFolderBillingStates(self::DEPOSIT_PAID),
            new RegistrationFolderBillingStates(self::TO_BILL),
            new RegistrationFolderBillingStates(self::BILLED),
            new RegistrationFolderBillingStates(self::PAID),
            new RegistrationFolderBillingStates(self::ALL)
        ];
    }

    /**
     * @return RegistrationFolderBillingStates[]
     */
    public static function updatableStates(): array
    {
        return [
            new RegistrationFolderBillingStates(self::NOT_BILLABLE),
            new RegistrationFolderBillingStates(self::DEPOSIT_WAIT),
            new RegistrationFolderBillingStates(self::DEPOSIT_PAID),
            new RegistrationFolderBillingStates(self::TO_BILL),
            new RegistrationFolderBillingStates(self::BILLED)
        ];
    }

    /**
     * @return RegistrationFolderBillingStates[]
     */
    public static function finalStates(): array
    {
        return [new RegistrationFolderBillingStates(self::PAID)];
    }

    /**
     * @return string[]
     */
    public static function valuesStatesToString(): array
    {
        return [
            self::NOT_BILLABLE,
            self::DEPOSIT_WAIT,
            self::DEPOSIT_PAID,
            self::TO_BILL,
            self::BILLED,
            self::PAID
        ];
    }
}
