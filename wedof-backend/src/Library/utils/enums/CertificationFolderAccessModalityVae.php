<?php


namespace App\Library\utils\enums;

use MyCLabs\Enum\Enum;

class CertificationFolderAccessModalityVae extends Enum
{
    private const CONGES_VAE = 'CONGES_VAE';
    private const VAE_CLASSIQUE = 'VAE_CLASSIQUE';

    /**
     * @return CertificationFolderAccessModalityVae
     */
    public static function CONGES_VAE(): CertificationFolderAccessModalityVae
    {
        return new CertificationFolderAccessModalityVae(self::CONGES_VAE);
    }

    /**
     * @return CertificationFolderAccessModalityVae
     */
    public static function VAE_CLASSIQUE(): CertificationFolderAccessModalityVae
    {
        return new CertificationFolderAccessModalityVae(self::VAE_CLASSIQUE);
    }

    /**
     * @param $accessModalityVae
     * @return string
     */
    public static function toFrString($accessModalityVae): string
    {
        $fr = [
            self::CONGES_VAE => "CONGES_VAE",
            self::VAE_CLASSIQUE => "VAE_CLASSIQUE"
        ];
        return $fr[$accessModalityVae];
    }

    /**
     * @return CertificationFolderAccessModalityVae[]
     */
    public static function valuesTypes(): array
    {
        return [
            new CertificationFolderAccessModalityVae(self::CONGES_VAE),
            new CertificationFolderAccessModalityVae(self::VAE_CLASSIQUE),
        ];
    }

}