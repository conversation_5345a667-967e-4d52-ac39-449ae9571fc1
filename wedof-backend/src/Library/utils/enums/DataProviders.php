<?php

namespace App\Library\utils\enums;

use MyCLabs\Enum\Enum;

class DataProviders extends Enum
{
    private const CPF = "cpf";
    private const CDC_CERTIFIERS = "cdcCertifiers";
    private const OPCO = "opco";
    private const OPCO_CFA = "opcoCfa";
    private const OPCO_CFA_ATLAS = "opcoCfaAtlas";
    private const OPCO_CFA_AFDAS = "opcoCfaAfdas";
    private const OPCO_CFA_EP = "opcoCfaEp";
    private const OPCO_CFA_MOBILITES = "opcoCfaMobilites";
    private const OPCO_CFA_AKTO = "opcoCfaAkto";
    private const OPCO_CFA_OCAPIAT = "opcoCfaOcapiat";
    private const OPCO_CFA_UNIFORMATION = "opcoCfaUniformation";
    private const OPCO_CFA_2I = "opcoCfa2i";
    private const OPCO_CFA_CONSTRUCTYS = "opcoCfaConstructys";
    private const OPCO_CFA_SANTE = "opcoCfaSante";
    private const OPCO_CFA_OPCOMMERCE = "opcoCfaOpcommerce";
    private const KAIROS_AIF = "kairosAif";
    private const INTERNAL = "internal";
    private const COMPANY = "company";
    private const INDIVIDUAL = "individual";
    private const POLE_EMPLOI = "poleEmploi";
    private const FRANCE_COMPETENCES = "franceCompetences";
    private const DIGIFORMA = "digiforma";
    private const ONLINEFORMAPRO = "onlineformapro";
    private const STATUS = "status";
    private const AUTOMATOR = "automator";
    private const OPEN_DATA = "openData";
    private const DOCUSEAL = "docuseal";
    private const AI = "ai";
    private const PASSPORT_PREVENTION = "passportPrevention";

    /**
     * @return DataProviders
     */
    public static function CPF(): DataProviders
    {
        return new DataProviders(self::CPF);
    }

    /**
     * @return DataProviders
     */
    public static function CDC_CERTIFIERS(): DataProviders
    {
        return new DataProviders(self::CDC_CERTIFIERS);
    }

    /**
     * @return DataProviders
     */
    public static function OPCO(): DataProviders
    {
        return new DataProviders(self::OPCO);
    }

    /**
     * @return DataProviders
     */
    public static function OPCO_CFA(): DataProviders
    {
        return new DataProviders(self::OPCO_CFA);
    }

    /**
     * @return DataProviders
     */
    public static function OPCO_CFA_ATLAS(): DataProviders
    {
        return new DataProviders(self::OPCO_CFA_ATLAS);
    }

    /**
     * @return DataProviders
     */
    public static function OPCO_CFA_AFDAS(): DataProviders
    {
        return new DataProviders(self::OPCO_CFA_AFDAS);
    }

    /**
     * @return DataProviders
     */
    public static function OPCO_CFA_EP(): DataProviders
    {
        return new DataProviders(self::OPCO_CFA_EP);
    }

    /**
     * @return DataProviders
     */
    public static function OPCO_CFA_MOBILITES(): DataProviders
    {
        return new DataProviders(self::OPCO_CFA_MOBILITES);
    }

    /**
     * @return DataProviders
     */
    public static function OPCO_CFA_AKTO(): DataProviders
    {
        return new DataProviders(self::OPCO_CFA_AKTO);
    }

    /**
     * @return DataProviders
     */
    public static function OPCO_CFA_OCAPIAT(): DataProviders
    {
        return new DataProviders(self::OPCO_CFA_OCAPIAT);
    }

    /**
     * @return DataProviders
     */
    public static function OPCO_CFA_UNIFORMATION(): DataProviders
    {
        return new DataProviders(self::OPCO_CFA_UNIFORMATION);
    }

    /**
     * @return DataProviders
     */
    public static function OPCO_CFA_2I(): DataProviders
    {
        return new DataProviders(self::OPCO_CFA_2I);
    }

    /**
     * @return DataProviders
     */
    public static function OPCO_CFA_CONSTRUCTYS(): DataProviders
    {
        return new DataProviders(self::OPCO_CFA_CONSTRUCTYS);
    }

    /**
     * @return DataProviders
     */
    public static function OPCO_CFA_SANTE(): DataProviders
    {
        return new DataProviders(self::OPCO_CFA_SANTE);
    }

    /**
     * @return DataProviders
     */
    public static function OPCO_CFA_OPCOMMERCE(): DataProviders
    {
        return new DataProviders(self::OPCO_CFA_OPCOMMERCE);
    }

    /**
     * @return DataProviders
     */
    public static function KAIROS_AIF(): DataProviders
    {
        return new DataProviders(self::KAIROS_AIF);
    }

    /**
     * @return DataProviders
     */
    public static function INTERNAL(): DataProviders
    {
        return new DataProviders(self::INTERNAL);
    }

    /**
     * @return DataProviders
     */
    public static function COMPANY(): DataProviders
    {
        return new DataProviders(self::COMPANY);
    }

    /**
     * @return DataProviders
     */
    public static function INDIVIDUAL(): DataProviders
    {
        return new DataProviders(self::INDIVIDUAL);
    }

    /**
     * @return DataProviders
     */
    public static function POLE_EMPLOI(): DataProviders
    {
        return new DataProviders(self::POLE_EMPLOI);
    }

    /**
     * @return DataProviders
     */
    public static function FRANCE_COMPETENCES(): DataProviders
    {
        return new DataProviders(self::FRANCE_COMPETENCES);
    }

    /**
     * @return DataProviders
     */
    public static function DIGIFORMA(): DataProviders
    {
        return new DataProviders(self::DIGIFORMA);
    }

    /**
     * @return DataProviders
     */
    public static function ONLINEFORMAPRO(): DataProviders
    {
        return new DataProviders(self::ONLINEFORMAPRO);
    }

    /**
     * @return DataProviders
     */
    public static function STATUS(): DataProviders
    {
        return new DataProviders(self::STATUS);
    }

    /**
     * @return DataProviders
     */
    public static function AUTOMATOR(): DataProviders
    {
        return new DataProviders(self::AUTOMATOR);
    }

    /**
     * @return DataProviders
     */
    public static function OPEN_DATA(): DataProviders
    {
        return new DataProviders(self::OPEN_DATA);
    }

    /**
     * @return DataProviders
     */
    public static function DOCUSEAL(): DataProviders
    {
        return new DataProviders(self::DOCUSEAL);
    }

    /**
     * @return DataProviders
     */
    public static function AI(): DataProviders
    {
        return new DataProviders(self::AI);
    }

    /**
     * @return DataProviders
     */
    public static function PASSPORT_PREVENTION(): DataProviders
    {
        return new DataProviders(self::PASSPORT_PREVENTION);
    }

    /**
     * @return DataProviders[]
     */
    public static function valuesTypes(): array
    {
        return [
            new DataProviders(self::CPF),
            new DataProviders(self::CDC_CERTIFIERS),
            new DataProviders(self::POLE_EMPLOI),
            new DataProviders(self::KAIROS_AIF),
            new DataProviders(self::INDIVIDUAL),
            new DataProviders(self::COMPANY),
            new DataProviders(self::OPCO),
            new DataProviders(self::OPCO_CFA),
            new DataProviders(self::INTERNAL),
            new DataProviders(self::FRANCE_COMPETENCES),
            new DataProviders(self::AUTOMATOR),
            new DataProviders(self::DIGIFORMA),
            new DataProviders(self::STATUS),
            new DataProviders(self::OPEN_DATA),
            new DataProviders(self::DOCUSEAL),
            new DataProviders(self::AI),
            new DataProviders(self::PASSPORT_PREVENTION)
        ];
    }

    public static function toFrString($dataProvider): string
    {
        $fr = [
            self::CPF => "EDOF",
            self::CDC_CERTIFIERS => "CDC CERTIFIERS",
            self::POLE_EMPLOI => "Pôle Emploi",
            self::KAIROS_AIF => "Kairos AIF",
            self::INDIVIDUAL => "Autofinancement",
            self::INTERNAL => "Wedof",
            self::COMPANY => "Entreprise",
            self::OPCO => "OPCO (manuel)",
            self::OPCO_CFA => "OPCO (Apprentissage)",
            self::OPCO_CFA_ATLAS => "OPCO Atlas (Apprentissage)",
            self::OPCO_CFA_AFDAS => "OPCO Afdas (Apprentissage)",
            self::OPCO_CFA_EP => "OPCO EP (Apprentissage)",
            self::OPCO_CFA_MOBILITES => "OPCO Mobilités (Apprentissage)",
            self::OPCO_CFA_AKTO => "OPCO Akto (Apprentissage)",
            self::OPCO_CFA_OCAPIAT => "OPCO Ocapiat (Apprentissage)",
            self::OPCO_CFA_UNIFORMATION => "OPCO Uniformation (Apprentissage)",
            self::OPCO_CFA_2I => "OPCO 2i (Apprentissage)",
            self::OPCO_CFA_CONSTRUCTYS => "OPCO Constructys (Apprentissage)",
            self::OPCO_CFA_SANTE => "OPCO Santé (Apprentissage)",
            self::OPCO_CFA_OPCOMMERCE => "OPCO L'Opcommmerce (Apprentissage)",
            self::FRANCE_COMPETENCES => "France Compétences",
            self::AUTOMATOR => "Automator",
            self::DIGIFORMA => "DIGIFORMA",
            self::STATUS => "Status",
            self::OPEN_DATA => "Open Data",
            self::AI => "AI Service",
            self::PASSPORT_PREVENTION => "Passeport de Prévention"
        ];
        return $fr[$dataProvider];
    }

    /**
     * @return DataProviders[]
     */
    public static function getOpcoCfaDataProviders(): array
    {
        return [
            new DataProviders(self::OPCO_CFA_ATLAS),
            new DataProviders(self::OPCO_CFA_AFDAS),
            new DataProviders(self::OPCO_CFA_EP),
            new DataProviders(self::OPCO_CFA_MOBILITES),
            new DataProviders(self::OPCO_CFA_AKTO),
            new DataProviders(self::OPCO_CFA_OCAPIAT),
            new DataProviders(self::OPCO_CFA_UNIFORMATION),
            new DataProviders(self::OPCO_CFA_2I),
            new DataProviders(self::OPCO_CFA_CONSTRUCTYS),
            new DataProviders(self::OPCO_CFA_SANTE),
            new DataProviders(self::OPCO_CFA_OPCOMMERCE)
        ];
    }

    /**
     * @return string[]
     */
    public static function getInternalToString(): array
    {
        return [
            self::POLE_EMPLOI,
            self::INDIVIDUAL,
            self::COMPANY,
            self::OPCO
        ];
    }

    /**
     * @return DataProviders[]
     */
    public static function requiredConnectionsDataProvidersForTrainingOrganisms(): array
    {
        return [
            self::CPF()
//            self::KAIROS_AIF()
        ];
    }

    public static function requiredConnectionsDataProvidersForCertifiers(): array
    {
        return
            [
                self::FRANCE_COMPETENCES()
            ];
    }

    public static function requiredConnectionsDataProvidersForAll(): array
    {
        $values = [];
        array_push($values, ...self::requiredConnectionsDataProvidersForTrainingOrganisms());
        array_push($values, ...self::requiredConnectionsDataProvidersForCertifiers());
        return $values;
    }
}
