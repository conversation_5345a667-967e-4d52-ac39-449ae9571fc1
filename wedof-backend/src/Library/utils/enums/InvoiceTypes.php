<?php

namespace App\Library\utils\enums;

use My<PERSON>abs\Enum\Enum;

class InvoiceTypes extends Enum
{
    private const INVOICE = "invoice";
    private const INVOICE_DEPOSIT = "deposit";
    private const CREDIT_NOTE = "creditNote";


    /**
     * @return InvoiceTypes
     */
    public static function INVOICE(): InvoiceTypes
    {
        return new InvoiceTypes(self::INVOICE);
    }

    /**
     * @return InvoiceTypes
     */
    public static function INVOICE_DEPOSIT(): InvoiceTypes
    {
        return new InvoiceTypes(self::INVOICE_DEPOSIT);
    }

    public static function CREDIT_NOTE(): InvoiceTypes
    {
        return new InvoiceTypes(self::CREDIT_NOTE);
    }

    /**
     * @param $state
     * @return string
     */
    public static function toFrString($state): string
    {
        $fr = [
            self::INVOICE => "facture",
            self::INVOICE_DEPOSIT => "facture d'acompte",
            self::CREDIT_NOTE => "note de crédit"
        ];
        return $fr[$state];
    }
}