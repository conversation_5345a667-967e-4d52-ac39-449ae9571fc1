<?php

namespace App\Library\utils\enums;

use MyCLabs\Enum\Enum;

class PaymentStates extends Enum
{
    private const WAITING = "waiting";
    private const REJECTED = "rejected";
    private const ISSUED = "issued";
    private const ALL = "all";

    /**
     * @return PaymentStates
     */
    public static function WAITING(): PaymentStates
    {
        return new PaymentStates(self::WAITING);
    }

    /**
     * @return PaymentStates
     */
    public static function REJECTED(): PaymentStates
    {
        return new PaymentStates(self::REJECTED);
    }

    /**
     * @return PaymentStates
     */
    public static function ISSUED(): PaymentStates
    {
        return new PaymentStates(self::ISSUED);
    }

    /**
     * @return PaymentStates
     */
    public static function ALL(): PaymentStates
    {
        return new PaymentStates(self::ALL);
    }

    /**
     * @return PaymentStates[]
     */
    public static function valuesStates(): array
    {
        // ordre réorganisé pour être plus efficace lors de la mise à jour des paiements
        return [
            new PaymentStates(self::ISSUED()),
            new PaymentStates(self::REJECTED()),
            new PaymentStates(self::WAITING()),
            new PaymentStates(self::ALL())
        ];
    }
}
