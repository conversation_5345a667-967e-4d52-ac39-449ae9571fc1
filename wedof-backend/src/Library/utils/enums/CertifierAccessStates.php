<?php

namespace App\Library\utils\enums;

use MyCLabs\Enum\Enum;

class CertifierAccessStates extends Enum
{
    private const ACCEPTED = "accepted";
    private const REFUSED = "refused";
    private const TERMINATED = "terminated";
    private const WAITING = "waiting";
    private const NONE = "none";

    private const ALL = "all";

    /**
     * @return CertifierAccessStates
     */
    public static function ACCEPTED(): CertifierAccessStates
    {
        return new CertifierAccessStates(self::ACCEPTED);
    }

    /**
     * @return CertifierAccessStates
     */
    public static function REFUSED(): CertifierAccessStates
    {
        return new CertifierAccessStates(self::REFUSED);
    }

    /**
     * @return CertifierAccessStates
     */
    public static function TERMINATED(): CertifierAccessStates
    {
        return new CertifierAccessStates(self::TERMINATED);
    }

    /**
     * @return CertifierAccessStates
     */
    public static function WAITING(): CertifierAccessStates
    {
        return new CertifierAccessStates(self::WAITING);
    }

    /**
     * @return CertifierAccessStates
     */
    public static function NONE(): CertifierAccessStates
    {
        return new CertifierAccessStates(self::NONE);
    }

    /**
     * @return CertifierAccessStates
     */
    public static function ALL(): CertifierAccessStates
    {
        return new CertifierAccessStates(self::ALL);
    }


    /**
     * @param $state
     * @return string
     */
    public static function toFrString($state): string
    {
        $fr = [
            self::ACCEPTED => "accepté",
            self::REFUSED => "refusé",
            self::TERMINATED => "terminé",
            self::WAITING => "en attente",
            self::NONE => "aucun"
        ];
        return $fr[$state];
    }

    /**
     * @return CertifierAccessStates[]
     */
    public static function valuesStates(): array
    {
        //THE POSITION IN THIS ARRAY IS EXTREMELY IMPORTANT
        return [
            new CertifierAccessStates(self::ACCEPTED),
            new CertifierAccessStates(self::REFUSED),
            new CertifierAccessStates(self::TERMINATED),
            new CertifierAccessStates(self::WAITING),
            new CertifierAccessStates(self::NONE),
            new CertifierAccessStates(self::ALL)
        ];
    }
}
