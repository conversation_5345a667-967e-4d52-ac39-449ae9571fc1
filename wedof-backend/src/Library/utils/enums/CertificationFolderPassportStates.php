<?php

namespace App\Library\utils\enums;

use MyCLabs\Enum\Enum;

class CertificationFolderPassportStates extends enum
{
    private const COMPLETED = "completed";
    private const ADDED = "added";
    private const TO_COMPLETE = "toComplete";
    private const INVALID = "invalid";

    public static function COMPLETED(): CertificationFolderPassportStates
    {
        return new CertificationFolderPassportStates(self::COMPLETED);
    }

    public static function ADDED(): CertificationFolderPassportStates
    {
        return new CertificationFolderPassportStates(self::ADDED);
    }

    public static function TO_COMPLETE(): CertificationFolderPassportStates
    {
        return new CertificationFolderPassportStates(self::TO_COMPLETE);
    }

    public static function INVALID(): CertificationFolderPassportStates
    {
        return new CertificationFolderPassportStates(self::INVALID);
    }
}