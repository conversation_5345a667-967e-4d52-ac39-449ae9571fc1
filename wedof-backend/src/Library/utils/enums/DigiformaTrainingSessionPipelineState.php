<?php

namespace App\Library\utils\enums;

use MyCLabs\Enum\Enum;

class DigiformaTrainingSessionPipelineState extends Enum
{
    private const DRAFT = "draft";
    private const INCOMPLETE = "incomplete";
    private const ONGOING = "ongoing";
    private const FINISHED = "finished";

    /**
     * @return DigiformaTrainingSessionPipelineState
     */
    public static function DRAFT(): DigiformaTrainingSessionPipelineState
    {
        return new DigiformaTrainingSessionPipelineState(self::DRAFT);
    }

    /**
     * @return DigiformaTrainingSessionPipelineState
     */
    public static function INCOMPLETE(): DigiformaTrainingSessionPipelineState
    {
        return new DigiformaTrainingSessionPipelineState(self::INCOMPLETE);
    }

    /**
     * @return DigiformaTrainingSessionPipelineState
     */
    public static function ONGOING(): DigiformaTrainingSessionPipelineState
    {
        return new DigiformaTrainingSessionPipelineState(self::ONGOING);
    }

    /**
     * @return DigiformaTrainingSessionPipelineState
     */
    public static function FINISHED(): DigiformaTrainingSessionPipelineState
    {
        return new DigiformaTrainingSessionPipelineState(self::FINISHED);
    }
}