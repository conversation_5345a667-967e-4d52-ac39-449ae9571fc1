<?php


namespace App\Library\utils\enums;

use MyCLabs\Enum\Enum;

class SubscriptionTrainingTypes extends Enum
{
    private const NONE = 'none';
    private const FREE = 'free';
    private const TRIAL = 'trial';
    private const ACCESS = 'access';
    private const ACCESS_PLUS = 'access_plus';
    private const API = 'api';
    private const ESSENTIAL = 'essential';
    private const STANDARD = 'standard';
    private const PREMIUM = 'premium';

    public const VALUES_TYPES_TO_STRING = [
        self::NONE,
        self::FREE,
        self::TRIAL,
        self::ACCESS,
        self::ACCESS_PLUS,
        self::API,
        self::ESSENTIAL,
        self::STANDARD,
        self::PREMIUM
    ];

    /**
     * @return SubscriptionTrainingTypes
     */
    public static function NONE(): SubscriptionTrainingTypes
    {
        return new SubscriptionTrainingTypes(self::NONE);
    }

    /**
     * @return SubscriptionTrainingTypes
     */
    public static function FREE(): SubscriptionTrainingTypes
    {
        return new SubscriptionTrainingTypes(self::FREE);
    }

    /**
     * @return SubscriptionTrainingTypes
     */
    public static function TRIAL(): SubscriptionTrainingTypes
    {
        return new SubscriptionTrainingTypes(self::TRIAL);
    }

    /**
     * @return SubscriptionTrainingTypes
     */
    public static function ACCESS(): SubscriptionTrainingTypes
    {
        return new SubscriptionTrainingTypes(self::ACCESS);
    }

    /**
     * @return SubscriptionTrainingTypes
     */
    public static function ACCESS_PLUS(): SubscriptionTrainingTypes
    {
        return new SubscriptionTrainingTypes(self::ACCESS_PLUS);
    }

    /**
     * @return SubscriptionTrainingTypes
     */
    public static function API(): SubscriptionTrainingTypes
    {
        return new SubscriptionTrainingTypes(self::API);
    }

    /**
     * @return SubscriptionTrainingTypes
     */
    public static function ESSENTIAL(): SubscriptionTrainingTypes
    {
        return new SubscriptionTrainingTypes(self::ESSENTIAL);
    }

    /**
     * @return SubscriptionTrainingTypes
     */
    public static function STANDARD(): SubscriptionTrainingTypes
    {
        return new SubscriptionTrainingTypes(self::STANDARD);
    }

    /**
     * @return SubscriptionTrainingTypes
     */
    public static function PREMIUM(): SubscriptionTrainingTypes
    {
        return new SubscriptionTrainingTypes(self::PREMIUM);
    }

    /**
     * @return SubscriptionTrainingTypes[]
     */
    public static function valuesTypes(): array
    {
        return [
            new SubscriptionTrainingTypes(self::NONE),
            new SubscriptionTrainingTypes(self::FREE),
            new SubscriptionTrainingTypes(self::TRIAL),
            new SubscriptionTrainingTypes(self::ACCESS),
            new SubscriptionTrainingTypes(self::ACCESS_PLUS),
            new SubscriptionTrainingTypes(self::API),
            new SubscriptionTrainingTypes(self::ESSENTIAL),
            new SubscriptionTrainingTypes(self::STANDARD),
            new SubscriptionTrainingTypes(self::PREMIUM)
        ];
    }

    /**
     * @return SubscriptionTrainingTypes[]
     */
    public static function getPaidTrainingTypes(bool $withTrial = true): array
    {
        $types = [
            new SubscriptionTrainingTypes(self::ACCESS),
            new SubscriptionTrainingTypes(self::ACCESS_PLUS),
            new SubscriptionTrainingTypes(self::API),
            new SubscriptionTrainingTypes(self::ESSENTIAL),
            new SubscriptionTrainingTypes(self::STANDARD),
            new SubscriptionTrainingTypes(self::PREMIUM)
        ];
        if ($withTrial) {
            $types[] = new SubscriptionTrainingTypes(self::TRIAL);
        }
        return $types;
    }

    /**
     * @return SubscriptionTrainingTypes[]
     */
    public static function getPaidTrainingTypesForApps(bool $withTrial = true): array
    {
        $types = [
            new SubscriptionTrainingTypes(self::ACCESS),
            new SubscriptionTrainingTypes(self::ACCESS_PLUS),
            new SubscriptionTrainingTypes(self::STANDARD),
            new SubscriptionTrainingTypes(self::PREMIUM)
        ];
        if ($withTrial) {
            $types[] = new SubscriptionTrainingTypes(self::TRIAL);
        }
        return $types;
    }
}
