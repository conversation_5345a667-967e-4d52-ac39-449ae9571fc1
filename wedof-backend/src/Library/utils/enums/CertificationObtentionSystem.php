<?php


namespace App\Library\utils\enums;


use My<PERSON>abs\Enum\Enum;

class CertificationObtentionSystem extends Enum
{
    private const PAR_ADMISSION = "PAR_ADMISSION";
    private const PAR_SCORING = "PAR_SCORING";


    /**
     * @return CertificationObtentionSystem
     */
    public static function PAR_ADMISSION(): CertificationObtentionSystem
    {
        return new CertificationObtentionSystem(self::PAR_ADMISSION);
    }

    /**
     * @return CertificationObtentionSystem
     */
    public static function PAR_SCORING(): CertificationObtentionSystem
    {
        return new CertificationObtentionSystem(self::PAR_SCORING);
    }

    /**
     * @param $obtentionSystem
     * @return string
     */
    public static function toFrString($obtentionSystem): string
    {
        $fr = [
            self::PAR_ADMISSION => "Par admission",
            self::PAR_SCORING => "Par score",
        ];
        return $fr[$obtentionSystem];
    }

    /**
     * @return CertificationObtentionSystem[]
     */
    public static function valuesTypes(): array
    {
        return [
            new CertificationObtentionSystem(self::PAR_ADMISSION),
            new CertificationObtentionSystem(self::PAR_SCORING)
        ];
    }
}