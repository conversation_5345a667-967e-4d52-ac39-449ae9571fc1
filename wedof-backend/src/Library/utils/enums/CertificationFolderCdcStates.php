<?php


namespace App\Library\utils\enums;


use MyCLabs\Enum\Enum;

class CertificationFolderCdcStates extends Enum
{
    private const NOT_EXPORTED = "notExported";
    private const EXPORTED = "exported";
    private const PROCESSED_OK = "processedOk";
    private const PROCESSED_KO = "processedKo";
    private const ALL = "all";

    /**
     * @return CertificationFolderCdcStates
     */
    public static function NOT_EXPORTED(): CertificationFolderCdcStates
    {
        return new CertificationFolderCdcStates(self::NOT_EXPORTED);
    }

    /**
     * @return CertificationFolderCdcStates
     */
    public static function EXPORTED(): CertificationFolderCdcStates
    {
        return new CertificationFolderCdcStates(self::EXPORTED);
    }

    /**
     * @return CertificationFolderCdcStates
     */
    public static function PROCESSED_OK(): CertificationFolderCdcStates
    {
        return new CertificationFolderCdcStates(self::PROCESSED_OK);
    }

    /**
     * @return CertificationFolderCdcStates
     */
    public static function PROCESSED_KO(): CertificationFolderCdcStates
    {
        return new CertificationFolderCdcStates(self::PROCESSED_KO);
    }

    /**
     * @return CertificationFolderCdcStates
     */
    public static function ALL(): CertificationFolderCdcStates
    {
        return new CertificationFolderCdcStates(self::ALL);
    }

    /**
     * @param $state
     * @return string
     */
    public static function toFrString($state): string
    {
        $fr = [
            self::NOT_EXPORTED => "Jamais exporté pour l'accrochage",
            self::EXPORTED => "Exporté pour l'accrochage",
            self::PROCESSED_OK => "Traité avec succès pour l'accrochage",
            self::PROCESSED_KO => "Traité avec erreur pour l'accrochage",
        ];
        return $fr[$state];
    }

    /**
     * @return CertificationFolderCdcStates[]
     */
    public static function valuesStates(): array
    {
        return [
            new CertificationFolderCdcStates(self::NOT_EXPORTED),
            new CertificationFolderCdcStates(self::EXPORTED),
            new CertificationFolderCdcStates(self::PROCESSED_OK),
            new CertificationFolderCdcStates(self::PROCESSED_KO),
            new CertificationFolderCdcStates(self::ALL)
        ];
    }

    /**
     * @return string[]
     */
    public static function valuesStatesToString(): array
    {
        return [
            self::NOT_EXPORTED,
            self::EXPORTED,
            self::PROCESSED_OK,
            self::PROCESSED_KO
        ];
    }
}
