<?php

namespace App\Library\utils\enums;

use MyCLabs\Enum\Enum;

class ConnectionTypes extends Enum
{
    private const DELEGATION = 'delegation';
    private const HABILITATION = 'habilitation';

    /**
     * @return ConnectionTypes
     */
    public static function DELEGATION(): ConnectionTypes
    {
        return new ConnectionTypes(self::DELEGATION);
    }

    /**
     * @return ConnectionTypes
     */
    public static function HABILITATION(): ConnectionTypes
    {
        return new ConnectionTypes(self::HABILITATION);
    }

    /**
     * @return array
     */
    public static function valuesTypes(): array
    {
        return [
            new ConnectionStates(self::DELEGATION),
            new ConnectionStates(self::HABILITATION)
        ];
    }

    /**
     * @param $state
     * @return string
     */
    public static function toFrString($state): string
    {
        $fr = [
            self::DELEGATION => "Délégation",
            self::HABILITATION => "Habilitation"
        ];
        return $fr[$state];
    }

    /**
     * @param DataProviders $dataProvider
     * @return ConnectionTypes|null
     */
    public static function getConnectionTypeFromDataProvider(DataProviders $dataProvider): ?ConnectionTypes
    {
        $values = [
            DataProviders::CPF()->getValue() => ConnectionTypes::HABILITATION(),
            DataProviders::CDC_CERTIFIERS()->getValue() => ConnectionTypes::HABILITATION(),
            DataProviders::POLE_EMPLOI()->getValue() => null,
            DataProviders::KAIROS_AIF()->getValue() => ConnectionTypes::DELEGATION(),
            DataProviders::INDIVIDUAL()->getValue() => null,
            DataProviders::COMPANY()->getValue() => null,
            DataProviders::OPCO()->getValue() => null,
            DataProviders::OPCO_CFA()->getValue() => ConnectionTypes::DELEGATION(),
            DataProviders::OPCO_CFA_ATLAS()->getValue() => ConnectionTypes::DELEGATION(),
            DataProviders::OPCO_CFA_AFDAS()->getValue() => ConnectionTypes::DELEGATION(),
            DataProviders::OPCO_CFA_EP()->getValue() => ConnectionTypes::DELEGATION(),
            DataProviders::OPCO_CFA_MOBILITES()->getValue() => ConnectionTypes::DELEGATION(),
            DataProviders::OPCO_CFA_AKTO()->getValue() => ConnectionTypes::DELEGATION(),
            DataProviders::OPCO_CFA_OCAPIAT()->getValue() => ConnectionTypes::DELEGATION(),
            DataProviders::OPCO_CFA_UNIFORMATION()->getValue() => ConnectionTypes::DELEGATION(),
            DataProviders::OPCO_CFA_2I()->getValue() => ConnectionTypes::DELEGATION(),
            DataProviders::OPCO_CFA_CONSTRUCTYS()->getValue() => ConnectionTypes::DELEGATION(),
            DataProviders::OPCO_CFA_SANTE()->getValue() => ConnectionTypes::DELEGATION(),
            DataProviders::OPCO_CFA_OPCOMMERCE()->getValue() => ConnectionTypes::DELEGATION(),
            DataProviders::FRANCE_COMPETENCES()->getValue() => ConnectionTypes::HABILITATION(),
            DataProviders::AUTOMATOR()->getValue() => null,
            DataProviders::DIGIFORMA()->getValue() => null,
            DataProviders::STATUS()->getValue() => null,
            DataProviders::DOCUSEAL()->getValue() => null,
            DataProviders::ONLINEFORMAPRO()->getValue() => null
        ];
        return $values[$dataProvider->getValue()];
    }
}
