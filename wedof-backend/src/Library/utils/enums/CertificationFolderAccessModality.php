<?php


namespace App\Library\utils\enums;


use My<PERSON>abs\Enum\Enum;

class CertificationFolderAccessModality extends Enum
{
    private const FORMATION_INITIALE_HORS_APPRENTISSAGE = 'FORMATION_INITIALE_HORS_APPRENTISSAGE';
    private const FORMATION_INITIALE_APPRENTISSAGE = 'FORMATION_INITIALE_APPRENTISSAGE';
    private const FORMATION_CONTINUE_HORS_CONTRAT_DE_PROFESSIONNALISATION = 'FORMATION_CONTINUE_HORS_CONTRAT_DE_PROFESSIONNALISATION';
    private const FORMATION_CONTINUE_CONTRAT_DE_PROFESSIONNALISATION = 'FORMATION_CONTINUE_CONTRAT_DE_PROFESSIONNALISATION';
    private const VAE = 'VAE';
    private const EQUIVALENCE = 'EQUIVALENCE_(DIPLOME_ETRANGER)';
    private const CANDIDAT_LIBRE = 'CANDIDAT_LIBRE';

    /**
     * @return CertificationFolderAccessModality
     */
    public static function FORMATION_INITIALE_HORS_APPRENTISSAGE(): CertificationFolderAccessModality
    {
        return new CertificationFolderAccessModality(self::FORMATION_INITIALE_HORS_APPRENTISSAGE);
    }

    /**
     * @return CertificationFolderAccessModality
     */
    public static function FORMATION_INITIALE_APPRENTISSAGE(): CertificationFolderAccessModality
    {
        return new CertificationFolderAccessModality(self::FORMATION_INITIALE_APPRENTISSAGE);
    }

    /**
     * @return CertificationFolderAccessModality
     */
    public static function FORMATION_CONTINUE_HORS_CONTRAT_DE_PROFESSIONNALISATION(): CertificationFolderAccessModality
    {
        return new CertificationFolderAccessModality(self::FORMATION_CONTINUE_HORS_CONTRAT_DE_PROFESSIONNALISATION);
    }

    /**
     * @return CertificationFolderAccessModality
     */
    public static function FORMATION_CONTINUE_CONTRAT_DE_PROFESSIONNALISATION(): CertificationFolderAccessModality
    {
        return new CertificationFolderAccessModality(self::FORMATION_CONTINUE_CONTRAT_DE_PROFESSIONNALISATION);
    }

    /**
     * @return CertificationFolderAccessModality
     */
    public static function VAE(): CertificationFolderAccessModality
    {
        return new CertificationFolderAccessModality(self::VAE);
    }

    /**
     * @return CertificationFolderAccessModality
     */
    public static function EQUIVALENCE(): CertificationFolderAccessModality
    {
        return new CertificationFolderAccessModality(self::EQUIVALENCE);
    }

    /**
     * @return CertificationFolderAccessModality
     */
    public static function CANDIDAT_LIBRE(): CertificationFolderAccessModality
    {
        return new CertificationFolderAccessModality(self::CANDIDAT_LIBRE);
    }

    /**
     * @param $modalityAccess
     * @return string
     */
    public static function toFrString($modalityAccess): string
    {
        $fr = [
            self::FORMATION_INITIALE_HORS_APPRENTISSAGE => "Formation initiale hors apprentissage",
            self::FORMATION_INITIALE_APPRENTISSAGE => "Formation initiale apprentissage",
            self::FORMATION_CONTINUE_HORS_CONTRAT_DE_PROFESSIONNALISATION => "Formation continue hors contrat de professionnalisation",
            self::FORMATION_CONTINUE_CONTRAT_DE_PROFESSIONNALISATION => "Formation continue contrat de professionnalisation",
            self::VAE => "VAE",
            self::EQUIVALENCE => "Equivalence (Diplôme étranger)",
            self::CANDIDAT_LIBRE => "Candidat libre",
        ];
        return $fr[$modalityAccess];
    }

    /**
     * @return CertificationFolderAccessModality[]
     */
    public static function valuesTypes(): array
    {
        return [
            new CertificationFolderAccessModality(self::FORMATION_INITIALE_HORS_APPRENTISSAGE),
            new CertificationFolderAccessModality(self::FORMATION_INITIALE_APPRENTISSAGE),
            new CertificationFolderAccessModality(self::FORMATION_CONTINUE_HORS_CONTRAT_DE_PROFESSIONNALISATION),
            new CertificationFolderAccessModality(self::FORMATION_CONTINUE_CONTRAT_DE_PROFESSIONNALISATION),
            new CertificationFolderAccessModality(self::VAE),
            new CertificationFolderAccessModality(self::EQUIVALENCE),
            new CertificationFolderAccessModality(self::CANDIDAT_LIBRE)
        ];
    }
}