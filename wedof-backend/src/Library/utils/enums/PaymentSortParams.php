<?php

namespace App\Library\utils\enums;

use MyCLabs\Enum\Enum;

class PaymentSortParams extends Enum
{
    private const PAYMENT_TYPE = 'typeFacture';
    private const BILL_NUMBER = 'numeroFacture';
    private const RF_EXTERNAL_ID = 'idDossier';
    private const BILLING_DATE = 'dateFacture';
    private const AMOUNT_TO_INVOICE = 'montantAFacturer';

    /**
     * @return PaymentSortParams
     */
    public static function PAYMENT_TYPE(): PaymentSortParams
    {
        return new PaymentSortParams(self::PAYMENT_TYPE);
    }

    /**
     * @return PaymentSortParams
     */
    public static function BILL_NUMBER(): PaymentSortParams
    {
        return new PaymentSortParams(self::BILL_NUMBER);
    }

    /**
     * @return PaymentSortParams
     */
    public static function RF_EXTERNAL_ID(): PaymentSortParams
    {
        return new PaymentSortParams(self::RF_EXTERNAL_ID);
    }

    /**
     * @return PaymentSortParams
     */
    public static function BILLING_DATE(): PaymentSortParams
    {
        return new PaymentSortParams(self::BILLING_DATE);
    }

    /**
     * @return PaymentSortParams
     */
    public static function AMOUNT_TO_INVOICE(): PaymentSortParams
    {
        return new PaymentSortParams(self::AMOUNT_TO_INVOICE);
    }
}