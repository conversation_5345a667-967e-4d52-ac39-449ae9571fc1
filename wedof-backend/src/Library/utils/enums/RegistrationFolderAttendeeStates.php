<?php

namespace App\Library\utils\enums;

use MyCLabs\Enum\Enum;

class RegistrationFolderAttendeeStates extends Enum
{
    private const SERVICE_DONE_NOT_DECLARED = "serviceDoneNotDeclared";
    private const SERVICE_DONE_DECLARED = "serviceDoneDeclared";

    /**
     * @return RegistrationFolderAttendeeStates
     */
    public static function SERVICE_DONE_NOT_DECLARED(): RegistrationFolderAttendeeStates
    {
        return new RegistrationFolderAttendeeStates(self::SERVICE_DONE_NOT_DECLARED);
    }

    /**
     * @return RegistrationFolderAttendeeStates
     */
    public static function SERVICE_DONE_DECLARED(): RegistrationFolderAttendeeStates
    {
        return new RegistrationFolderAttendeeStates(self::SERVICE_DONE_DECLARED);
    }


    /**
     * @param $state
     * @return string
     */
    public static function toFrString($state): string
    {
        $fr = [
            self::SERVICE_DONE_NOT_DECLARED => "Service fait non déclaré",
            self::SERVICE_DONE_DECLARED => "Service fait déclaré"
        ];
        return $fr[$state];
    }

    /**
     * @return RegistrationFolderAttendeeStates[]
     */
    public static function valuesStates(): array
    {
        //THE POSITION IN THIS ARRAY IS EXTREMELY IMPORTANT
        return [
            new RegistrationFolderAttendeeStates(self::SERVICE_DONE_NOT_DECLARED),
            new RegistrationFolderAttendeeStates(self::SERVICE_DONE_DECLARED)
        ];
    }

    /**
     * @return RegistrationFolderAttendeeStates[]
     */
    public static function updatableStates(): array
    {
        return [
            new RegistrationFolderAttendeeStates(self::SERVICE_DONE_NOT_DECLARED),
            new RegistrationFolderAttendeeStates(self::SERVICE_DONE_DECLARED) // un dossier peut être mis à jour alors que l'attendee state est "SERVICE_DONE_DECLARED"
        ];
    }

    /**
     * @return RegistrationFolderAttendeeStates[]
     */
    public static function finalStates(): array
    {
        return [new RegistrationFolderBillingStates(self::SERVICE_DONE_DECLARED)];
    }
}
