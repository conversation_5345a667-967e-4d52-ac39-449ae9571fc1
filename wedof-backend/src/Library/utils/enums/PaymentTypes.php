<?php

namespace App\Library\utils\enums;

use MyCLabs\Enum\Enum;

class PaymentTypes extends Enum
{
    private const BILL = "BILL";
    private const DEPOSIT = "DEPOSIT";

    /**
     * @return PaymentTypes
     */
    public static function DEPOSIT(): PaymentTypes
    {
        return new PaymentTypes(self::DEPOSIT);
    }

    /**
     * @return PaymentTypes
     */
    public static function BILL(): PaymentTypes
    {
        return new PaymentTypes(self::BILL);
    }
}
