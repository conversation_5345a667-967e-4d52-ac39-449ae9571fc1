<?php


namespace App\Library\utils\enums;


use MyCLabs\Enum\Enum;

class TrainerQualification extends Enum
{
    private const PROFESSIONAL = "professional";
    private const TEACHER = "teacher";
    private const TRAINER = "trainer";
    private const ENGINEER = "engineer";
    private const PREVENTION = "prevention";
    private const PSYCHOLOGIST = "psychologist";
    private const RESPONSIBLE = "responsible";

    /**
     * @return TrainerQualification
     */
    public static function PROFESSIONAL(): TrainerQualification
    {
        return new TrainerQualification(self::PROFESSIONAL);
    }

    /**
     * @return TrainerQualification
     */
    public static function TEACHER(): TrainerQualification
    {
        return new TrainerQualification(self::TEACHER);
    }

    /**
     * @return TrainerQualification
     */
    public static function TRAINER(): TrainerQualification
    {
        return new TrainerQualification(self::TRAINER);
    }

    /**
     * @return TrainerQualification
     */
    public static function ENGINEER(): TrainerQualification
    {
        return new TrainerQualification(self::ENGINEER);
    }

    /**
     * @return TrainerQualification
     */
    public static function PREVENTION(): TrainerQualification
    {
        return new TrainerQualification(self::PREVENTION);
    }

    /**
     * @return TrainerQualification
     */
    public static function PSYCHOLOGIST(): TrainerQualification
    {
        return new TrainerQualification(self::PSYCHOLOGIST);
    }

    /**
     * @return TrainerQualification
     */
    public static function RESPONSIBLE(): TrainerQualification
    {
        return new TrainerQualification(self::RESPONSIBLE);
    }

    /**
     * @param $qualificationsTrainer
     * @return string
     */
    public static function toFrString($qualificationsTrainer): string
    {
        $fr = [
            self::PROFESSIONAL => "Ancien professionnel du secteur concerné",
            self::TEACHER => "Enseignant (lycée, université)",
            self::TRAINER => "Formateur d'adultes ayant suivi une formation spécialisée au domaine concerné",
            self::ENGINEER => "Ingénieur",
            self::PREVENTION => "Préventeur",
            self::PSYCHOLOGIST => "Psychologue",
            self::RESPONSIBLE => "Responsable qualité - hygiène - sécurité - environnement",
        ];
        return $fr[$qualificationsTrainer];
    }

    /**
     * @return TrainerQualification[]
     */
    public static function valuesTypes(): array
    {
        return [
            new TrainerQualification(self::PROFESSIONAL),
            new TrainerQualification(self::TEACHER),
            new TrainerQualification(self::TRAINER),
            new TrainerQualification(self::ENGINEER),
            new TrainerQualification(self::PREVENTION),
            new TrainerQualification(self::PSYCHOLOGIST),
            new TrainerQualification(self::RESPONSIBLE),
        ];
    }
}
