<?php

namespace App\Library\utils\enums;

use MyCLabs\Enum\Enum;

class UserMessageTypes extends Enum
{
    private const PRIMARY = 'primary';
    private const ACCENT = 'accent';
    private const WARN = 'warn';
    private const BASIC = 'basic';
    private const INFO = 'info';
    private const SUCCESS = 'success';
    private const WARNING = 'warning';
    private const ERROR = 'error';

    /**
     * @return UserMessageTypes
     */
    public static function PRIMARY(): UserMessageTypes
    {
        return new UserMessageTypes(self::PRIMARY);
    }

    /**
     * @return UserMessageTypes
     */
    public static function ACCENT(): UserMessageTypes
    {
        return new UserMessageTypes(self::ACCENT);
    }

    /**
     * @return UserMessageTypes
     */
    public static function WARN(): UserMessageTypes
    {
        return new UserMessageTypes(self::WARN);
    }

    /**
     * @return UserMessageTypes
     */
    public static function BASIC(): UserMessageTypes
    {
        return new UserMessageTypes(self::BASIC);
    }

    /**
     * @return UserMessageTypes
     */
    public static function INFO(): UserMessageTypes
    {
        return new UserMessageTypes(self::INFO);
    }

    /**
     * @return UserMessageTypes
     */
    public static function SUCCESS(): UserMessageTypes
    {
        return new UserMessageTypes(self::SUCCESS);
    }

    /**
     * @return UserMessageTypes
     */
    public static function WARNING(): UserMessageTypes
    {
        return new UserMessageTypes(self::WARNING);
    }

    /**
     * @return UserMessageTypes
     */
    public static function ERROR(): UserMessageTypes
    {
        return new UserMessageTypes(self::ERROR);
    }
}