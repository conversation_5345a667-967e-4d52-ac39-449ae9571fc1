<?php


namespace App\Library\utils\enums;


use MyCLabs\Enum\Enum;

class CertificationFolderType extends Enum
{
    private const CERTIFIE = 'CERTIFIE';
    private const OF = 'OF';
    private const POLE_EMPLOI = 'POLE_EMPLOI';
    private const EMPLOYEUR = 'EMPLOYEUR';
    private const AUTRE = 'AUTRE';

    /**
     * @return CertificationFolderType
     */
    public static function CERTIFIE(): CertificationFolderType
    {
        return new CertificationFolderType(self::CERTIFIE);
    }
    /**
     * @return CertificationFolderType
     */
    public static function OF(): CertificationFolderType
    {
        return new CertificationFolderType(self::OF);
    }

    /**
     * @return CertificationFolderType
     */
    public static function POLE_EMPLOI(): CertificationFolderType
    {
        return new CertificationFolderType(self::POLE_EMPLOI);
    }

    /**
     * @return CertificationFolderType
     */
    public static function EMPLOYEUR(): CertificationFolderType
    {
        return new CertificationFolderType(self::EMPLOYEUR);
    }

    /**
     * @return CertificationFolderType
     */
    public static function AUTRE(): CertificationFolderType
    {
        return new CertificationFolderType(self::AUTRE);
    }

    /**
     * @param $inscription
     * @return string
     */
    public static function toFrString($inscription): string
    {
        $fr = [
            self::CERTIFIE => "Certifié(e)",
            self::OF => "Organisme de formation",
            self::POLE_EMPLOI => "Pôle Emploi",
            self::EMPLOYEUR => "Employeur",
            self::AUTRE => "Autre",
        ];
        return $fr[$inscription];
    }

    /**
     * @return CertificationFolderType[]
     */
    public static function valuesTypes(): array
    {
        return [
            new CertificationFolderType(self::CERTIFIE),
            new CertificationFolderType(self::OF),
            new CertificationFolderType(self::POLE_EMPLOI),
            new CertificationFolderType(self::EMPLOYEUR),
            new CertificationFolderType(self::AUTRE)
        ];
    }
}