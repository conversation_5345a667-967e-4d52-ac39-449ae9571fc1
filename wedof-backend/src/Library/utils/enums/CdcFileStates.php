<?php


namespace App\Library\utils\enums;


use MyCLabs\Enum\Enum;

class CdcFileStates extends Enum
{
    private const EXPORTED = "exported";
    private const ABORTED = "aborted";
    private const PROCESSED = "processed";
    private const ALL = "all";

    /**
     * Wedof does not know it the file has been uploaded
     * Wedof does not have a receipt
     * @return CdcFileStates
     */
    public static function EXPORTED(): CdcFileStates
    {
        return new CdcFileStates(self::EXPORTED);
    }

    /**
     * The file is to be ignored
     * The receipt information is not taken into account
     * @return CdcFileStates
     */
    public static function ABORTED(): CdcFileStates
    {
        return new CdcFileStates(self::ABORTED);
    }

    /**
     * The receipt has been read at least once
     * @return CdcFileStates
     */
    public static function PROCESSED(): CdcFileStates
    {
        return new CdcFileStates(self::PROCESSED);
    }

    /**
     * @return CdcFileStates
     */
    public static function ALL(): CdcFileStates
    {
        return new CdcFileStates(self::ALL);
    }

    /**
     * @param $cdcFileStates
     * @return string
     */
    public static function toFrString($cdcFileStates): string
    {
        $fr = [
            self::EXPORTED => "Exporté",
            self::ABORTED => "Abandonné",
            self::PROCESSED => "Traité",
            self::ALL => "Tout",
        ];
        return $fr[$cdcFileStates];
    }

    /**
     * @return CdcFileStates[]
     */
    public static function valuesTypes(): array
    {
        return [
            new CdcFileStates(self::EXPORTED),
            new CdcFileStates(self::ABORTED),
            new CdcFileStates(self::PROCESSED),
            new CdcFileStates(self::ALL)
        ];
    }
}
