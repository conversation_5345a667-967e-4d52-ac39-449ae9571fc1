<?php

namespace App\Library\utils\enums;

use MyCLabs\Enum\Enum;

class OrganismTypes extends Enum
{
    private const CERTIFIER = "certifier";
    private const PARTNER = "partner";
    private const ALL = "all";

    /**
     * @return OrganismTypes
     */
    public static function CERTIFIER(): OrganismTypes
    {
        return new OrganismTypes(self::CERTIFIER);
    }

    /**
     * @return OrganismTypes
     */
    public static function PARTNER(): OrganismTypes
    {
        return new OrganismTypes(self::PARTNER);
    }

    /**
     * @return OrganismTypes
     */
    public static function ALL(): OrganismTypes
    {
        return new OrganismTypes(self::ALL);
    }


    /**
     * @param $state
     * @return string
     */
    public static function toFrString($state): string
    {
        $fr = [
            self::CERTIFIER => "certificateur",
            self::PARTNER => "partenaire"
        ];
        return $fr[$state];
    }

    /**
     * @return OrganismTypes[]
     */
    public static function valuesTypes(): array
    {
        //THE POSITION IN THIS ARRAY IS EXTREMELY IMPORTANT
        return [
            new OrganismTypes(self::CERTIFIER),
            new OrganismTypes(self::PARTNER),
            new OrganismTypes(self::ALL)
        ];
    }
}
