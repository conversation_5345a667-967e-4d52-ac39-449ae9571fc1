<?php

namespace App\Library\utils\enums;

use MyCLabs\Enum\Enum;

class TrainingComplianceTypes extends Enum
{
    private const NOT_VERIFIED = 'notVerified';
    private const COMPLIANT = 'compliant';
    private const NOT_COMPLIANT = 'notCompliant';

    /**
     * @return TrainingComplianceTypes
     */
    public static function NOT_VERIFIED(): TrainingComplianceTypes
    {
        return new TrainingComplianceTypes(self::NOT_VERIFIED);
    }

    /**
     * @return TrainingComplianceTypes
     */
    public static function COMPLIANT(): TrainingComplianceTypes
    {
        return new TrainingComplianceTypes(self::COMPLIANT);
    }

    /**
     * @return TrainingComplianceTypes
     */
    public static function NOT_COMPLIANT(): TrainingComplianceTypes
    {
        return new TrainingComplianceTypes(self::NOT_COMPLIANT);
    }

    /**
     * @return array
     */
    public static function valuesStatesToString(): array
    {
        return [
            self::NOT_VERIFIED,
            self::COMPLIANT,
            self::NOT_COMPLIANT
        ];
    }

    /**
     * @param $complianceType
     * @return string
     */
    public static function toFrString($complianceType): string
    {
        $fr = [
            self::NOT_VERIFIED => "À vérifier",
            self::COMPLIANT => "Conforme",
            self::NOT_COMPLIANT => "Non conforme"
        ];
        return $fr[$complianceType];
    }
}