<?php


namespace App\Library\utils\enums;


use My<PERSON>abs\Enum\Enum;

class CertificationFolderSurveyStates extends Enum
{

    private const CREATED = "created";
    private const BEFORE_CERTIFICATION_SUCCESS = "beforeCertificationSuccess"; // initialExperience
    private const AFTER_SIX_MONTHS_CERTIFICATION_SUCCESS = "afterSixMonthsCertificationSuccess"; // sixMonthExperience
    private const FINISHED = "finished"; // longTermExperience
    private const ALL = "all";

    /**
     * @return CertificationFolderSurveyStates
     */
    public static function CREATED(): CertificationFolderSurveyStates
    {
        return new CertificationFolderSurveyStates(self::CREATED);
    }

    /**
     * @return CertificationFolderSurveyStates
     */
    public static function BEFORE_CERTIFICATION_SUCCESS(): CertificationFolderSurveyStates
    {
        return new CertificationFolderSurveyStates(self::BEFORE_CERTIFICATION_SUCCESS);
    }

    /**
     * @return CertificationFolderSurveyStates
     */
    public static function AFTER_SIX_MONTHS_CERTIFICATION_SUCCESS(): CertificationFolderSurveyStates
    {
        return new CertificationFolderSurveyStates(self::AFTER_SIX_MONTHS_CERTIFICATION_SUCCESS);
    }

    /**
     * @return CertificationFolderSurveyStates
     */
    public static function FINISHED(): CertificationFolderSurveyStates
    {
        return new CertificationFolderSurveyStates(self::FINISHED);
    }

    /**
     * @return CertificationFolderSurveyStates
     */
    public static function ALL(): CertificationFolderSurveyStates
    {
        return new CertificationFolderSurveyStates(self::ALL);
    }

    /**
     * @param $state
     * @return string
     */
    public static function toFrString($state): string
    {
        $fr = [
            self::CREATED => "Créée",
            self::BEFORE_CERTIFICATION_SUCCESS => "Commencée",
            self::AFTER_SIX_MONTHS_CERTIFICATION_SUCCESS => "En cours",
            self::FINISHED => "Terminée",
            self::ALL => "Tout"
        ];
        return $fr[$state];
    }

    /**
     * @return CertificationFolderSurveyStates[]
     */
    public static function valuesStates(): array
    {
        return [
            new CertificationFolderSurveyStates(self::CREATED),
            new CertificationFolderSurveyStates(self::BEFORE_CERTIFICATION_SUCCESS),
            new CertificationFolderSurveyStates(self::AFTER_SIX_MONTHS_CERTIFICATION_SUCCESS),
            new CertificationFolderSurveyStates(self::FINISHED),
            new CertificationFolderSurveyStates(self::ALL)
        ];
    }
}