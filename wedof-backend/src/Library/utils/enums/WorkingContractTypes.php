<?php

namespace App\Library\utils\enums;

use MyCLabs\Enum\Enum;

class WorkingContractTypes extends Enum
{
    private const INITIAL_11 = '11';
    private const SUCCESSION_21 = '21';
    private const SUCCESSION_22 = '22';
    private const SUCCESSION_23 = '23';
    private const AMENDMENT_31 = '31';
    private const AMENDMENT_32 = '32';
    private const AMENDMENT_33 = '33';
    private const AMENDMENT_34 = '34';
    private const AMENDMENT_35 = '35';
    private const AMENDMENT_36 = '36';
    private const AMENDMENT_37 = '37';
    private const AMENDMENT_38 = '38';

    /**
     * @return WorkingContractTypes
     */
    public static function INITIAL_11(): WorkingContractTypes
    {
        return new WorkingContractTypes(self::INITIAL_11);
    }

    /**
     * @return WorkingContractTypes
     */
    public static function SUCCESSION_21(): WorkingContractTypes
    {
        return new WorkingContractTypes(self::SUCCESSION_21);
    }

    /**
     * @return WorkingContractTypes
     */
    public static function SUCCESSION_22(): WorkingContractTypes
    {
        return new WorkingContractTypes(self::SUCCESSION_22);
    }

    /**
     * @return WorkingContractTypes
     */
    public static function SUCCESSION_23(): WorkingContractTypes
    {
        return new WorkingContractTypes(self::SUCCESSION_23);
    }

    /**
     * @return WorkingContractTypes
     */
    public static function AMENDMENT_31(): WorkingContractTypes
    {
        return new WorkingContractTypes(self::AMENDMENT_31);
    }

    /**
     * @return WorkingContractTypes
     */
    public static function AMENDMENT_32(): WorkingContractTypes
    {
        return new WorkingContractTypes(self::AMENDMENT_32);
    }

    /**
     * @return WorkingContractTypes
     */
    public static function AMENDMENT_33(): WorkingContractTypes
    {
        return new WorkingContractTypes(self::AMENDMENT_33);
    }

    /**
     * @return WorkingContractTypes
     */
    public static function AMENDMENT_34(): WorkingContractTypes
    {
        return new WorkingContractTypes(self::AMENDMENT_34);
    }

    /**
     * @return WorkingContractTypes
     */
    public static function AMENDMENT_35(): WorkingContractTypes
    {
        return new WorkingContractTypes(self::AMENDMENT_35);
    }

    /**
     * @return WorkingContractTypes
     */
    public static function AMENDMENT_36(): WorkingContractTypes
    {
        return new WorkingContractTypes(self::AMENDMENT_36);
    }

    /**
     * @return WorkingContractTypes
     */
    public static function AMENDMENT_37(): WorkingContractTypes
    {
        return new WorkingContractTypes(self::AMENDMENT_37);
    }

    /**
     * @return WorkingContractTypes
     */
    public static function AMENDMENT_38(): WorkingContractTypes
    {
        return new WorkingContractTypes(self::AMENDMENT_38);
    }


    /**
     * @param string $result
     * @return string
     */
    public static function toFrString(string $result): string
    {
        $fr = [
            self::INITIAL_11 => 'Premier contrat d’apprentissage de l’apprenti',
            self::SUCCESSION_21 => 'Nouveau contrat avec un apprenti qui a terminé son précédent contrat auprès d’un même employeur',
            self::SUCCESSION_22 => 'Nouveau contrat avec un apprenti qui a terminé son précédent contrat auprès d’un autre employeur',
            self::SUCCESSION_23 => 'Nouveau contrat avec un apprenti dont le précédent contrat auprès d’un autre employeur a été rompu',
            self::AMENDMENT_31 => 'Modification de la situation juridique de l’employeur',
            self::AMENDMENT_32 => 'Changement d’employeur dans le cadre d’un contrat saisonnier',
            self::AMENDMENT_33 => 'Prolongation du contrat suite à un échec à l’examen de l’apprenti',
            self::AMENDMENT_34 => 'Prolongation du contrat suite à la reconnaissance de l’apprenti comme travailleur handicapé',
            self::AMENDMENT_35 => 'Modification du diplôme préparé par l’apprenti',
            self::AMENDMENT_36 => 'Autres changements (changement de maître d’apprentissage, de durée de travail hebdomadaire, réduction de durée, etc.)',
            self::AMENDMENT_37 => 'Modification du lieu d’exécution du contrat',
            self::AMENDMENT_38 => 'Modification du lieu principal de réalisation de la formation théorique',
        ];
        return $fr[$result];
    }

    /**
     * @return WorkingContractTypes[]
     */
    public static function valuesTypes(): array
    {
        return [
            new WorkingContractTypes(self::INITIAL_11),
            new WorkingContractTypes(self::SUCCESSION_21),
            new WorkingContractTypes(self::SUCCESSION_22),
            new WorkingContractTypes(self::SUCCESSION_23),
            new WorkingContractTypes(self::AMENDMENT_31),
            new WorkingContractTypes(self::AMENDMENT_32),
            new WorkingContractTypes(self::AMENDMENT_33),
            new WorkingContractTypes(self::AMENDMENT_34),
            new WorkingContractTypes(self::AMENDMENT_35),
            new WorkingContractTypes(self::AMENDMENT_36),
            new WorkingContractTypes(self::AMENDMENT_37),
            new WorkingContractTypes(self::AMENDMENT_38)
        ];
    }
}