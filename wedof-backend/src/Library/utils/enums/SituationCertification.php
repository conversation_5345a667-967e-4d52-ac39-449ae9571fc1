<?php


namespace App\Library\utils\enums;


use MyCLabs\Enum\Enum;

class SituationCertification extends Enum
{
    private const ACTIVE = "active";
    private const SEARCHING = "searching";
    private const INACTIVE = "inactive";
    private const TRAINING = "training";

    /**
     * @return SituationCertification
     */
    public static function ACTIVE(): SituationCertification
    {
        return new SituationCertification(self::ACTIVE);
    }

    /**
     * @return SituationCertification
     */
    public static function SEARCHING(): SituationCertification
    {
        return new SituationCertification(self::SEARCHING);
    }

    /**
     * @return SituationCertification
     */
    public static function INACTIVE(): SituationCertification
    {
        return new SituationCertification(self::INACTIVE);
    }

    /**
     * @return SituationCertification
     */
    public static function TRAINING(): SituationCertification
    {
        return new SituationCertification(self::TRAINING);
    }

    /**
     * @param $situation
     * @return string
     */
    public static function toFrString($situation): string
    {
        $fr = [
            self::ACTIVE => "En poste (hors alternance)",
            self::SEARCHING => "En recherche d'emploi",
            self::INACTIVE => "Inactif",
            self::TRAINING => "En formation",
        ];
        return $fr[$situation];
    }

    /**
     * @return SituationCertification[]
     */
    public static function valuesTypes(): array
    {
        return [
            new CdcFileStates(self::ACTIVE),
            new CdcFileStates(self::INACTIVE),
            new CdcFileStates(self::SEARCHING),
            new CdcFileStates(self::TRAINING)
        ];
    }

    /**
     * @param $situation
     * @return string
     */
    public static function toFrStringForFranceCompetences($situation): string
    {
        $fr = [
            self::ACTIVE => "Actif occupé hors alternance",
            self::SEARCHING => "En recherche d'emploi",
            self::INACTIVE => "Inactif",
            self::TRAINING => "En formation",
        ];
        return $fr[$situation];
    }
}
