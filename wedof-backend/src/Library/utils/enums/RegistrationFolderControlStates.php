<?php

namespace App\Library\utils\enums;

use MyCLabs\Enum\Enum;

class RegistrationFolderControlStates extends Enum
{
    private const NOT_IN_CONTROL = "notInControl";
    private const IN_CONTROL = "inControl";
    private const RELEASED = "released";
    private const ALL = "all";

    /**
     * @return RegistrationFolderControlStates
     */
    public static function NOT_IN_CONTROL(): RegistrationFolderControlStates
    {
        return new RegistrationFolderControlStates(self::NOT_IN_CONTROL);
    }

    /**
     * @return RegistrationFolderControlStates
     */
    public static function IN_CONTROL(): RegistrationFolderControlStates
    {
        return new RegistrationFolderControlStates(self::IN_CONTROL);
    }

    /**
     * @return RegistrationFolderControlStates
     */
    public static function RELEASED(): RegistrationFolderControlStates
    {
        return new RegistrationFolderControlStates(self::RELEASED);
    }

    /**
     * @return RegistrationFolderControlStates
     */
    public static function ALL(): RegistrationFolderControlStates
    {
        return new RegistrationFolderControlStates(self::ALL);
    }

    /**
     * @return RegistrationFolderControlStates[]
     */
    public static function valuesStates(): array
    {
        // Attention à ne pas modifier l'ordre, c'est utilisé pour l'envoi des évènements
        return [
            new RegistrationFolderControlStates(self::NOT_IN_CONTROL()),
            new RegistrationFolderControlStates(self::IN_CONTROL()),
            new RegistrationFolderControlStates(self::RELEASED()),
            new RegistrationFolderControlStates(self::ALL())
        ];
    }
}
