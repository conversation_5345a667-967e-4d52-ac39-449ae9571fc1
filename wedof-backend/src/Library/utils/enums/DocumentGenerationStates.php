<?php

namespace App\Library\utils\enums;

use MyCLabs\Enum\Enum;

class DocumentGenerationStates extends Enum
{
    private const NOT_GENERATED = "notGenerated";
    private const GENERATING = "generating";
    private const GENERATED = "generated";

    /**
     * @return DocumentGenerationStates
     */
    public static function NOT_GENERATED(): DocumentGenerationStates
    {
        return new DocumentGenerationStates(self::NOT_GENERATED);
    }

    /**
     * @return DocumentGenerationStates
     */
    public static function GENERATING(): DocumentGenerationStates
    {
        return new DocumentGenerationStates(self::GENERATING);
    }

    /**
     * @return DocumentGenerationStates
     */
    public static function GENERATED(): DocumentGenerationStates
    {
        return new DocumentGenerationStates(self::GENERATED);
    }
}