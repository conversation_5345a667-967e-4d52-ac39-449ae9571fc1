<?php


namespace App\Library\utils\enums;


use MyCLabs\Enum\Enum;

class CertificationFolderGradePass extends Enum
{
    private const SANS_MENTION = 'SANS_MENTION';
    private const MENTION_ASSEZ_BIEN = 'MENTION_ASSEZ_BIEN';
    private const MENTION_BIEN = 'MENTION_BIEN';
    private const MENTION_TRES_BIEN = 'MENTION_TRES_BIEN';
    private const MENTION_TRES_BIEN_AVEC_FELICITATIONS_DU_JURY = 'MENTION_TRES_BIEN_AVEC_FELICITATIONS_DU_JURY';

    /**
     * @return CertificationFolderGradePass
     */
    public static function SANS_MENTION(): CertificationFolderGradePass
    {
        return new CertificationFolderGradePass(self::SANS_MENTION);
    }

    /**
     * @return CertificationFolderGradePass
     */
    public static function MENTION_ASSEZ_BIEN(): CertificationFolderGradePass
    {
        return new CertificationFolderGradePass(self::MENTION_ASSEZ_BIEN);
    }

    /**
     * @return CertificationFolderGradePass
     */
    public static function MENTION_BIEN(): CertificationFolderGradePass
    {
        return new CertificationFolderGradePass(self::MENTION_BIEN);
    }

    /**
     * @return CertificationFolderGradePass
     */
    public static function MENTION_TRES_BIEN(): CertificationFolderGradePass
    {
        return new CertificationFolderGradePass(self::MENTION_TRES_BIEN);
    }


    /**
     * @return CertificationFolderGradePass
     */
    public static function MENTION_TRES_BIEN_AVEC_FELICITATIONS_DU_JURY(): CertificationFolderGradePass
    {
        return new CertificationFolderGradePass(self::MENTION_TRES_BIEN_AVEC_FELICITATIONS_DU_JURY);
    }

    /**
     * @param $honours
     * @return string
     */
    public static function toFrString($honours): string
    {
        $fr = [
            self::SANS_MENTION => "Sans mention",
            self::MENTION_ASSEZ_BIEN => "Mention assez bien",
            self::MENTION_BIEN => "Mention bien",
            self::MENTION_TRES_BIEN => "Mention très bien",
            self::MENTION_TRES_BIEN_AVEC_FELICITATIONS_DU_JURY => "Mention très bien avec félicitations du jury",
        ];
        return $fr[$honours];
    }

    /**
     * @return CertificationFolderGradePass[]
     */
    public static function valuesTypes(): array
    {
        return [
            new CertificationFolderGradePass(self::SANS_MENTION),
            new CertificationFolderGradePass(self::MENTION_ASSEZ_BIEN),
            new CertificationFolderGradePass(self::MENTION_BIEN),
            new CertificationFolderGradePass(self::MENTION_TRES_BIEN),
            new CertificationFolderGradePass(self::MENTION_TRES_BIEN_AVEC_FELICITATIONS_DU_JURY)
        ];
    }
}
