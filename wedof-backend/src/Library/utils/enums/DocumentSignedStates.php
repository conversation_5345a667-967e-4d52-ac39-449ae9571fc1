<?php

namespace App\Library\utils\enums;

use MyCLabs\Enum\Enum;

class DocumentSignedStates extends Enum
{
    private const NOT_REQUIRED = "notRequired";
    private const NONE = "none";
    private const PARTIALLY = "partially";
    private const COMPLETED = "completed";

    private const DECLINED = "declined";

    /**
     * @return DocumentSignedStates
     */
    public static function NOT_REQUIRED(): DocumentSignedStates
    {
        return new DocumentSignedStates(self::NOT_REQUIRED);
    }

    /**
     * @return DocumentSignedStates
     */
    public static function NONE(): DocumentSignedStates
    {
        return new DocumentSignedStates(self::NONE);
    }

    /**
     * @return DocumentSignedStates
     */
    public static function PARTIALLY(): DocumentSignedStates
    {
        return new DocumentSignedStates(self::PARTIALLY);
    }

    /**
     * @return DocumentSignedStates
     */
    public static function COMPLETED(): DocumentSignedStates
    {
        return new DocumentSignedStates(self::COMPLETED);
    }

    /**
     * @return DocumentSignedStates
     */
    public static function DECLINED(): DocumentSignedStates
    {
        return new DocumentSignedStates(self::DECLINED);
    }
}