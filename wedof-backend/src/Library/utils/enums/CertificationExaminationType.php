<?php


namespace App\Library\utils\enums;


use MyCLabs\Enum\Enum;

class CertificationExaminationType extends Enum
{

    private const A_DISTANCE = "A_DISTANCE";
    private const EN_PRESENTIEL = "EN_PRESENTIEL";
    private const MIXTE = "MIXTE";


    /**
     * @return CertificationExaminationType
     */
    public static function A_DISTANCE(): CertificationExaminationType
    {
        return new CertificationExaminationType(self::A_DISTANCE);
    }

    /**
     * @return CertificationExaminationType
     */
    public static function EN_PRESENTIEL(): CertificationExaminationType
    {
        return new CertificationExaminationType(self::EN_PRESENTIEL);
    }

    /**
     * @return CertificationExaminationType
     */
    public static function MIXTE(): CertificationExaminationType
    {
        return new CertificationExaminationType(self::MIXTE);
    }

    /**
     * @param $modalityExamination
     * @return string
     */
    public static function toFrString($modalityExamination): string
    {
        $fr = [
            self::A_DISTANCE => "À distance",
            self::EN_PRESENTIEL => "En présentiel",
            self::MIXTE => "Mixte (à distance et en présentiel)",
        ];
        return $fr[$modalityExamination];
    }

    /**
     * @return CertificationExaminationType[]
     */
    public static function valuesTypes(): array
    {
        return [
            new CertificationExaminationType(self::A_DISTANCE),
            new CertificationExaminationType(self::EN_PRESENTIEL),
            new CertificationExaminationType(self::MIXTE)
        ];
    }
}