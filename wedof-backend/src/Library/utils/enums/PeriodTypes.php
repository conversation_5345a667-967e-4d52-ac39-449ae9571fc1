<?php

namespace App\Library\utils\enums;

use MyCLabs\Enum\Enum;

class PeriodTypes extends Enum
{
    private const CUSTOM = 'custom';
    private const WEDOF_INVOICE = 'wedofInvoice';
    private const WEDOF_QUOTA = 'wedofQuota';
    private const NEXT_YEAR = 'nextYear';
    private const PREVIOUS_YEAR = 'previousYear';
    private const CURRENT_YEAR = 'currentYear';
    private const ROLLING_YEAR = 'rollingYear';
    private const ROLLING_YEAR_FUTURE = 'rollingYearFuture';

    private const NEXT_MONTH = 'nextMonth';
    private const PREVIOUS_MONTH = 'previousMonth';
    private const CURRENT_MONTH = 'currentMonth';
    private const ROLLING_MONTH = 'rollingMonth';
    private const ROLLING_MONTH_FUTURE = 'rollingMonthFuture';

    private const NEXT_WEEK = 'nextWeek';
    private const PREVIOUS_WEEK = 'previousWeek';
    private const CURRENT_WEEK = 'currentWeek';
    private const ROLLING_WEEK = 'rollingWeek';

    private const ROLLING_WEEK_FUTURE = 'rollingWeekFuture';
    private const TOMORROW = 'tomorrow';
    private const TODAY = 'today';
    private const YESTERDAY = 'yesterday';


    /**
     * @return PeriodTypes
     */
    public static function CUSTOM(): PeriodTypes
    {
        return new PeriodTypes(self::CUSTOM);
    }

    /**
     * @return PeriodTypes
     */
    public static function WEDOF_INVOICE(): PeriodTypes
    {
        return new PeriodTypes(self::WEDOF_INVOICE);
    }

    /**
     * @return PeriodTypes
     */
    public static function WEDOF_QUOTA(): PeriodTypes
    {
        return new PeriodTypes(self::WEDOF_QUOTA);
    }

    /**
     * @return PeriodTypes
     */
    public static function NEXT_YEAR(): PeriodTypes
    {
        return new PeriodTypes(self::NEXT_YEAR);
    }

    /**
     * @return PeriodTypes
     */
    public static function ROLLING_YEAR(): PeriodTypes
    {
        return new PeriodTypes(self::ROLLING_YEAR);
    }

    public static function ROLLING_YEAR_FUTURE(): PeriodTypes
    {
        return new PeriodTypes(self::ROLLING_YEAR_FUTURE);
    }

    /**
     * @return PeriodTypes
     */
    public static function PREVIOUS_YEAR(): PeriodTypes
    {
        return new PeriodTypes(self::PREVIOUS_YEAR);
    }

    /**
     * @return PeriodTypes
     */
    public static function CURRENT_YEAR(): PeriodTypes
    {
        return new PeriodTypes(self::CURRENT_YEAR);
    }

    /**
     * @return PeriodTypes
     */
    public static function NEXT_MONTH(): PeriodTypes
    {
        return new PeriodTypes(self::NEXT_MONTH);
    }

    /**
     * @return PeriodTypes
     */
    public static function PREVIOUS_MONTH(): PeriodTypes
    {
        return new PeriodTypes(self::PREVIOUS_MONTH);
    }

    /**
     * @return PeriodTypes
     */
    public static function CURRENT_MONTH(): PeriodTypes
    {
        return new PeriodTypes(self::CURRENT_MONTH);
    }

    /**
     * @return PeriodTypes
     */
    public static function ROLLING_MONTH(): PeriodTypes
    {
        return new PeriodTypes(self::ROLLING_MONTH);
    }

    public static function ROLLING_MONTH_FUTURE(): PeriodTypes
    {
        return new PeriodTypes(self::ROLLING_MONTH_FUTURE);
    }

    /**
     * @return PeriodTypes
     */
    public static function NEXT_WEEK(): PeriodTypes
    {
        return new PeriodTypes(self::NEXT_WEEK);
    }

    /**
     * @return PeriodTypes
     */
    public static function PREVIOUS_WEEK(): PeriodTypes
    {
        return new PeriodTypes(self::PREVIOUS_WEEK);
    }

    /**
     * @return PeriodTypes
     */
    public static function CURRENT_WEEK(): PeriodTypes
    {
        return new PeriodTypes(self::CURRENT_WEEK);
    }

    /**
     * @return PeriodTypes
     */
    public static function ROLLING_WEEK(): PeriodTypes
    {
        return new PeriodTypes(self::ROLLING_WEEK);
    }

    public static function ROLLING_WEEK_FUTURE(): PeriodTypes
    {
        return new PeriodTypes(self::ROLLING_WEEK_FUTURE);
    }

    /**
     * @return PeriodTypes
     */
    public static function TOMORROW(): PeriodTypes
    {
        return new PeriodTypes(self::TOMORROW);
    }

    /**
     * @return PeriodTypes
     */
    public static function TODAY(): PeriodTypes
    {
        return new PeriodTypes(self::TODAY);
    }

    /**
     * @return PeriodTypes
     */
    public static function YESTERDAY(): PeriodTypes
    {
        return new PeriodTypes(self::YESTERDAY);
    }

    /**
     * @return PeriodTypes[]
     */
    public static function valuesStates(): array
    {
        return [
            new PeriodTypes(self::CUSTOM),
            new PeriodTypes(self::WEDOF_INVOICE),
            new PeriodTypes(self::WEDOF_QUOTA),
            new PeriodTypes(self::NEXT_YEAR),
            new PeriodTypes(self::PREVIOUS_YEAR),
            new PeriodTypes(self::CURRENT_YEAR),
            new PeriodTypes(self::ROLLING_YEAR),
            new PeriodTypes(self::ROLLING_YEAR_FUTURE),
            new PeriodTypes(self::NEXT_MONTH),
            new PeriodTypes(self::CURRENT_MONTH),
            new PeriodTypes(self::PREVIOUS_MONTH),
            new PeriodTypes(self::ROLLING_MONTH),
            new PeriodTypes(self::ROLLING_MONTH_FUTURE),
            new PeriodTypes(self::NEXT_WEEK),
            new PeriodTypes(self::PREVIOUS_WEEK),
            new PeriodTypes(self::CURRENT_WEEK),
            new PeriodTypes(self::ROLLING_WEEK),
            new PeriodTypes(self::ROLLING_WEEK_FUTURE),
            new PeriodTypes(self::TOMORROW),
            new PeriodTypes(self::TODAY),
            new PeriodTypes(self::YESTERDAY)
        ];
    }
}
