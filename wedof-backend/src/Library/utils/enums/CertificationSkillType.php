<?php


namespace App\Library\utils\enums;


use MyCLabs\Enum\Enum;

class CertificationSkillType extends Enum
{

    private const SKILL = "skill";
    private const SKILL_SET = "skillSet";
    private const ALL = "all";


    /**
     * @return CertificationSkillType
     */
    public static function SKILL(): CertificationSkillType
    {
        return new CertificationSkillType(self::SKILL);
    }

    /**
     * @return CertificationSkillType
     */
    public static function SKILL_SET(): CertificationSkillType
    {
        return new CertificationSkillType(self::SKILL_SET);
    }

    /**
     * @return CertificationSkillType
     */
    public static function ALL(): CertificationSkillType
    {
        return new CertificationSkillType(self::ALL);
    }


    /**
     * @param $certificationSkillType
     * @return string
     */
    public static function toFrString($certificationSkillType): string
    {
        $fr = [
            self::SKILL => "Compétence",
            self::SKILL_SET => "Bloc de compétence"
        ];
        return $fr[$certificationSkillType];
    }

    /**
     * @return CertificationSkillType[]
     */
    public static function valuesTypes(): array
    {
        return [
            new CertificationSkillType(self::SKILL),
            new CertificationSkillType(self::SKILL_SET),
            new CertificationSkillType(self::ALL)
        ];
    }
}