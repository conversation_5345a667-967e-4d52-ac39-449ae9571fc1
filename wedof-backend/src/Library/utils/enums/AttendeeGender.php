<?php


namespace App\Library\utils\enums;


use MyCLabs\Enum\Enum;

class AttendeeGender extends Enum
{
    private const MALE = "male";
    private const FEMALE = "female";
    /**
     * @return AttendeeGender
     */
    public static function MALE(): AttendeeGender
    {
        return new AttendeeGender(self::MALE);
    }

    /**
     * @return AttendeeGender
     */
    public static function FEMALE(): AttendeeGender
    {
        return new AttendeeGender(self::FEMALE);
    }

    /**
     * @return AttendeeGender[]
     */
    public static function valuesTypes(): array
    {
        return [
            new AttendeeGender(self::MALE),
            new AttendeeGender(self::FEMALE)
        ];
    }

    /**
     * @param $state
     * @return string
     */
    public static function toFrString($state): string
    {
        $fr = [
            self::MALE => "M.",
            self::FEMALE => "Mme"
        ];
        return $fr[$state];
    }
}
