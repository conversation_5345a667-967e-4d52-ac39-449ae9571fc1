<?php

namespace App\Library\utils\enums;

use MyCLabs\Enum\Enum;

class MessageTemplateStates extends Enum
{
    private const ACTIVE = 'active';
    private const INACTIVE = 'inactive';
    private const ALL = 'all';
    private const HIDDEN = 'hidden';

    /**
     * @return MessageTemplateStates
     */
    public static function ACTIVE(): MessageTemplateStates
    {
        return new MessageTemplateStates(self::ACTIVE);
    }

    /**
     * @return MessageTemplateStates
     */
    public static function INACTIVE(): MessageTemplateStates
    {
        return new MessageTemplateStates(self::INACTIVE);
    }

    /**
     * @return MessageTemplateStates
     */
    public static function ALL(): MessageTemplateStates
    {
        return new MessageTemplateStates(self::ALL);
    }

    /**
     * @return MessageTemplateStates
     */
    public static function HIDDEN(): MessageTemplateStates
    {
        return new MessageTemplateStates(self::HIDDEN);
    }

    /**
     * @param $state
     * @return string
     */
    public static function toFrString($state): string
    {
        $fr = [
            self::ACTIVE => "Actif",
            self::INACTIVE => "Inactif",
        ];
        return $fr[$state];
    }

    /**
     * @return MessageTemplateStates[]
     */
    public static function valuesTypes(): array
    {
        // don't add hidden state as we don't want to list them
        return [
            new MessageTemplateStates(self::ACTIVE),
            new MessageTemplateStates(self::INACTIVE),
            new MessageTemplateStates(self::ALL)
        ];
    }
}