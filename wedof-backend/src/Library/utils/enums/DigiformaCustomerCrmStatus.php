<?php

namespace App\Library\utils\enums;

use MyCLabs\Enum\Enum;

class DigiformaCustomerCrmStatus extends Enum
{
    private const UNDECIDED = "UNDECIDED";
    private const WON = "WON";
    private const LOST = "LOST";
    private const CANCELLED = "CANCELLED";
    private const COMPLETED = "COMPLETED";

    /**
     * @return DigiformaCustomerCrmStatus
     */
    public static function UNDECIDED(): DigiformaCustomerCrmStatus
    {
        return new DigiformaCustomerCrmStatus(self::UNDECIDED);
    }

    /**
     * @return DigiformaCustomerCrmStatus
     */
    public static function WON(): DigiformaCustomerCrmStatus
    {
        return new DigiformaCustomerCrmStatus(self::WON);
    }

    /**
     * @return DigiformaCustomerCrmStatus
     */
    public static function LOST(): DigiformaCustomerCrmStatus
    {
        return new DigiformaCustomerCrmStatus(self::LOST);
    }

    /**
     * @return DigiformaCustomerCrmStatus
     */
    public static function CANCELLED(): DigiformaCustomerCrmStatus
    {
        return new DigiformaCustomerCrmStatus(self::CANCELLED);
    }

    /**
     * @return DigiformaCustomerCrmStatus
     */
    public static function COMPLETED(): DigiformaCustomerCrmStatus
    {
        return new DigiformaCustomerCrmStatus(self::COMPLETED);
    }
}