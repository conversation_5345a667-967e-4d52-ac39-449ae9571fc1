<?php

namespace App\Library\utils\enums;

use MyCLabs\Enum\Enum;

class CertificationPartnerAuditCriteriaSeverity extends Enum
{
    private const NONE = 'none';
    private const MINEURE = 'mineure';
    private const MAJEURE = 'majeure';

    /**
     * @return CertificationPartnerAuditCriteriaSeverity
     */
    public static function NONE(): CertificationPartnerAuditCriteriaSeverity
    {
        return new CertificationPartnerAuditCriteriaSeverity(self::NONE);
    }

    /**
     * @return CertificationPartnerAuditCriteriaSeverity
     */
    public static function MINEURE(): CertificationPartnerAuditCriteriaSeverity
    {
        return new CertificationPartnerAuditCriteriaSeverity(self::MINEURE);
    }

    /**
     * @return CertificationPartnerAuditCriteriaSeverity
     */
    public static function MAJEURE(): CertificationPartnerAuditCriteriaSeverity
    {
        return new CertificationPartnerAuditCriteriaSeverity(self::MAJEURE);
    }

    /**
     * @param string $result
     * @return string
     */
    public static function toFrString(string $result): string
    {
        $fr = [
            self::NONE => '',
            self::MINEURE => 'Entraîne une non-conformité mineure',
            self::MAJEURE => 'Entraîne une non-conformité majeure'
        ];
        return $fr[$result];
    }
}