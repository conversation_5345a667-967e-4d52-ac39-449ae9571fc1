<?php

namespace App\Library\utils\enums;

use MyCLabs\Enum\Enum;

class SolicitationFundingType extends Enum
{
    private const POLE_EMPLOI = 9;
    private const TITULAIRE = 29;
    private const FINANCEUR = -1;
    private const EMPLOYEUR = -2;

    /**
     * @return SolicitationFundingType
     */
    public static function POLE_EMPLOI(): SolicitationFundingType
    {
        return new SolicitationFundingType(self::POLE_EMPLOI);
    }

    /**
     * @return SolicitationFundingType
     */
    public static function TITULAIRE(): SolicitationFundingType
    {
        return new SolicitationFundingType(self::TITULAIRE);
    }

    /**
     * @return SolicitationFundingType
     */
    public static function FINANCEUR(): SolicitationFundingType
    {
        return new SolicitationFundingType(self::FINANCEUR);
    }

    /**
     * @return SolicitationFundingType
     */
    public static function EMPLOYEUR(): SolicitationFundingType
    {
        return new SolicitationFundingType(self::EMPLOYEUR);
    }
}
