<?php


namespace App\Library\utils\enums;


use MyCLabs\Enum\Enum;

class ApplicationStates extends Enum
{
    private const TRIAL = "trial";
    private const PENDING_DISABLE_TRIAL = "pending_disable_trial";
    private const PENDING_ENABLE_TRIAL = "pending_enable_trial";
    private const ENABLED = "enabled";
    private const DISABLED = "disabled";
    private const PENDING_ENABLE = "pending_enable";
    private const PENDING_DISABLE = "pending_disable";


    public static function TRIAL(): ApplicationStates
    {
        return new ApplicationStates(self::TRIAL);
    }

    public static function PENDING_ENABLE_TRIAL(): ApplicationStates
    {
        return new ApplicationStates(self::PENDING_ENABLE_TRIAL);
    }

    public static function PENDING_DISABLE_TRIAL(): ApplicationStates
    {
        return new ApplicationStates(self::PENDING_DISABLE_TRIAL);
    }

    public static function ENABLED(): ApplicationStates
    {
        return new ApplicationStates(self::ENABLED);
    }

    public static function DISABLED(): ApplicationStates
    {
        return new ApplicationStates(self::DISABLED);
    }

    public static function PENDING_ENABLE(): ApplicationStates
    {
        return new ApplicationStates(self::PENDING_ENABLE);
    }

    public static function PENDING_DISABLE(): ApplicationStates
    {
        return new ApplicationStates(self::PENDING_DISABLE);
    }


    public static function toFrString($activePieceHostingStates): string
    {
        $fr = [
            self::ENABLED => "Actif",
            self::DISABLED => "Inactif",
            self::PENDING_ENABLE => "Activation programmée",
            self::TRIAL => "Période d'essai en cours",
            self::PENDING_DISABLE => "Désactivation programmée",
            self::PENDING_ENABLE_TRIAL => "Activation programmée de la période d'essai",
            self::PENDING_DISABLE_TRIAL => "Désactivation programmée de la période d'essai",
        ];
        return $fr[$activePieceHostingStates];
    }

    /**
     * @return ApplicationStates[]
     */
    public static function valuesTypes(): array
    {
        return [
            new ApplicationStates(self::TRIAL),
            new ApplicationStates(self::PENDING_ENABLE_TRIAL),
            new ApplicationStates(self::PENDING_DISABLE_TRIAL),
            new ApplicationStates(self::ENABLED),
            new ApplicationStates(self::DISABLED),
            new ApplicationStates(self::PENDING_ENABLE),
            new ApplicationStates(self::PENDING_DISABLE)
        ];
    }
}
