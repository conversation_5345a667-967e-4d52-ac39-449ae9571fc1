<?php


namespace App\Library\utils\enums;


use MyCLabs\Enum\Enum;

class ContractType extends Enum
{
    private const CDD = "cdd";
    private const CDI = "cdi";
    private const INTERIM = "interim";
    private const INDEPENDANT = "independant";
    private const INACTIF = "inactif";

    /**
     * @return ContractType
     */
    public static function CDD(): ContractType
    {
        return new ContractType(self::CDD);
    }

    /**
     * @return ContractType
     */
    public static function CDI(): ContractType
    {
        return new ContractType(self::CDI);
    }

    /**
     * @return ContractType
     */
    public static function INTERIM(): ContractType
    {
        return new ContractType(self::INTERIM);
    }

    /**
     * @return ContractType
     */
    public static function INDEPENDANT(): ContractType
    {
        return new ContractType(self::INDEPENDANT);
    }

    /**
     * @return ContractType
     */
    public static function INACTIF(): ContractType
    {
        return new ContractType(self::INACTIF);
    }

    /**
     * @param $contractType
     * @return string
     */
    public static function toFrString($contractType): string
    {
        $fr = [
            self::CDD => "CDD",
            self::CDI => "En recherche d'emploi",
            self::INTERIM => "Intérim",
            self::INDEPENDANT => "Indépendant",
            self::INACTIF => "Sans travail",
        ];
        return $fr[$contractType];
    }

    /**
     * @return ContractType[]
     */
    public static function valuesTypes(): array
    {
        return [
            new CdcFileStates(self::CDD),
            new CdcFileStates(self::INTERIM),
            new CdcFileStates(self::CDI),
            new CdcFileStates(self::INDEPENDANT),
            new CdcFileStates(self::INACTIF)
        ];
    }
}
