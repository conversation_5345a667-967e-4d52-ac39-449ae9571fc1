<?php

namespace App\Library\utils\enums;

use MyCLabs\Enum\Enum;

class MessageStates extends Enum
{
    private const SENT = 'sent';
    private const NOT_SENT = 'notSent'; // à conserver legacy
    private const NOT_SENT_UNAUTHORIZED = 'notSentUnauthorized';
    private const NOT_SENT_ENFORCED_CONDITIONS = 'notSentEnforcedConditions';
    private const NOT_SENT_MISSING_DATA = 'notSentMissingData';
    private const FAILED = 'failed';
    private const SCHEDULED = 'scheduled';
    private const TO_SEND = 'toSend';
    private const ALL = 'all';

    /**
     * @return MessageStates
     */
    public static function SENT(): MessageStates
    {
        return new MessageStates(self::SENT);
    }

    /**
     * @return MessageStates
     */
    public static function NOT_SENT(): MessageStates
    {
        return new MessageStates(self::NOT_SENT);
    }

    /**
     * @return MessageStates
     */
    public static function NOT_SENT_UNAUTHORIZED(): MessageStates
    {
        return new MessageStates(self::NOT_SENT_UNAUTHORIZED);
    }

    /**
     * @return MessageStates
     */
    public static function NOT_SENT_ENFORCED_CONDITIONS(): MessageStates
    {
        return new MessageStates(self::NOT_SENT_ENFORCED_CONDITIONS);
    }

    /**
     * @return MessageStates
     */
    public static function NOT_SENT_MISSING_DATA(): MessageStates
    {
        return new MessageStates(self::NOT_SENT_MISSING_DATA);
    }

    /**
     * @return MessageStates
     */
    public static function FAILED(): MessageStates
    {
        return new MessageStates(self::FAILED);
    }

    /**
     * @return MessageStates
     */
    public static function TO_SEND(): MessageStates
    {
        return new MessageStates(self::TO_SEND);
    }

    /**
     * @return MessageStates
     */
    public static function SCHEDULED(): MessageStates
    {
        return new MessageStates(self::SCHEDULED);
    }

    /**
     * @return MessageStates
     */
    public static function ALL(): MessageStates
    {
        return new MessageStates(self::ALL);
    }

    /**
     * @param $state
     * @return string
     */
    public static function toFrString($state): string
    {
        $fr = [
            self::SENT => "Envoyé",
            self::NOT_SENT => "Non envoyé",
            self::NOT_SENT_UNAUTHORIZED => 'Non envoyé, mauvais abonnement/application inactive',
            self::NOT_SENT_ENFORCED_CONDITIONS => 'Non envoyé, re-vérification des conditions',
            self::NOT_SENT_MISSING_DATA => 'Non envoyé, données manquantes',
            self::FAILED => "Échoué",
            self::SCHEDULED => "Programmé",
            self::TO_SEND => "A envoyer"
        ];
        return $fr[$state];
    }

    /**
     * @return MessageStates[]
     */
    public static function valuesTypes(): array
    {
        return [
            new MessageStates(self::SENT),
            new MessageStates(self::NOT_SENT),
            new MessageStates(self::NOT_SENT_UNAUTHORIZED),
            new MessageStates(self::NOT_SENT_ENFORCED_CONDITIONS),
            new MessageStates(self::NOT_SENT_MISSING_DATA),
            new MessageStates(self::FAILED),
            new MessageStates(self::SCHEDULED),
            new MessageStates(self::TO_SEND)
        ];
    }
}