<?php

namespace App\Library\utils\enums;

use ReflectionClass;
use UnexpectedValueException;

abstract class TypeEnum
{
    /**
     * @param string $typeShortName
     * @return string
     */
    public static function getByName(string $typeShortName): string
    {
        $availableConstList = self::getAvailableConstantList();

        if (isset($availableConstList[$typeShortName]))
            return $availableConstList[$typeShortName];
        else {
            throw new UnexpectedValueException('Unknown constant name');
        }
    }

    /**
     * returns a array with available constants
     * @return array|null
     */
    private static function getAvailableConstantList(): ?array
    {
        return (new ReflectionClass(static::class))->getConstants();
    }

    /**
     * @return string[]
     */
    public static function getAvailableTypes(): ?array
    {
        return self::getAvailableConstantList();
    }

    /**
     * @param $constantName - value to check
     * @return bool
     */
    public static function isValidName($constantName): bool
    {
        $availableConstList = self::getAvailableConstantList();
        return isset($availableConstList[$constantName]);
    }

    /**
     * @param $constantValue - value to check
     * @return bool
     */
    public static function isValidValue($constantValue): bool
    {
        $availableConstList = self::getAvailableConstantList();
        return array_search($constantValue, $availableConstList, true) !== false;
    }
}

