<?php

namespace App\Library\utils\enums;

use MyCLabs\Enum\Enum;

class SubscriptionCertifierTypes extends Enum
{
    private const NONE = 'none';
    private const PARTNER = 'partner';
    private const FREE = 'free';
    private const TRIAL = 'trial';
    private const USAGE = 'usage';
    private const ACCESS = 'access';
    private const UNLIMITED = 'unlimited';

    public const VALUES_TYPES_TO_STRING = [
        self::NONE,
        self::PARTNER,
        self::FREE,
        self::TRIAL,
        self::USAGE,
        self::ACCESS,
        self::UNLIMITED
    ];

    /**
     * @return SubscriptionCertifierTypes
     */
    public static function NONE(): SubscriptionCertifierTypes
    {
        return new SubscriptionCertifierTypes(self::NONE);
    }

    /**
     * @return SubscriptionCertifierTypes
     */
    public static function PARTNER(): SubscriptionCertifierTypes
    {
        return new SubscriptionCertifierTypes(self::PARTNER);
    }

    /**
     * @return SubscriptionCertifierTypes
     */
    public static function FREE(): SubscriptionCertifierTypes
    {
        return new SubscriptionCertifierTypes(self::FREE);
    }

    /**
     * @return SubscriptionCertifierTypes
     */
    public static function TRIAL(): SubscriptionCertifierTypes
    {
        return new SubscriptionCertifierTypes(self::TRIAL);
    }

    /**
     * @return SubscriptionCertifierTypes
     */
    public static function USAGE(): SubscriptionCertifierTypes
    {
        return new SubscriptionCertifierTypes(self::USAGE);
    }

    /**
     * @return SubscriptionCertifierTypes
     */
    public static function ACCESS(): SubscriptionCertifierTypes
    {
        return new SubscriptionCertifierTypes(self::ACCESS);
    }

    /**
     * @return SubscriptionCertifierTypes
     */
    public static function UNLIMITED(): SubscriptionCertifierTypes
    {
        return new SubscriptionCertifierTypes(self::UNLIMITED);
    }

    /**
     * @return SubscriptionCertifierTypes[]
     */
    public static function getPaidCertifierTypes(): array
    {
        return [
            new SubscriptionCertifierTypes(self::TRIAL),
            new SubscriptionCertifierTypes(self::USAGE),
            new SubscriptionCertifierTypes(self::ACCESS),
            new SubscriptionCertifierTypes(self::UNLIMITED)
        ];
    }

    /**
     * @return SubscriptionCertifierTypes[]
     */
    public static function valuesTypes(): array
    {
        return [
            new SubscriptionCertifierTypes(self::NONE),
            new SubscriptionCertifierTypes(self::PARTNER),
            new SubscriptionCertifierTypes(self::FREE),
            new SubscriptionCertifierTypes(self::TRIAL),
            new SubscriptionCertifierTypes(self::USAGE),
            new SubscriptionCertifierTypes(self::ACCESS),
            new SubscriptionCertifierTypes(self::UNLIMITED)
        ];
    }
}
