<?php

namespace App\Library\utils\enums;

use MyCLabs\Enum\Enum;

class ProposalStates extends Enum
{
    private const TEMPLATE = "template";
    private const DRAFT = "draft";
    private const ACTIVE = "active";
    private const VIEWED = "viewed";
    private const ACCEPTED = "accepted";
    private const REFUSED = "refused";
    private const DELETED = "deleted"; // don't add this state anywhere else than toFrString method as we don't want to return the state
    private const ALL = "all";

    /**
     * @return ProposalStates
     */
    public static function TEMPLATE(): ProposalStates
    {
        return new ProposalStates(self::TEMPLATE);
    }

    /**
     * @return ProposalStates
     */
    public static function DRAFT(): ProposalStates
    {
        return new ProposalStates(self::DRAFT);
    }

    /**
     * @return ProposalStates
     */
    public static function ACTIVE(): ProposalStates
    {
        return new ProposalStates(self::ACTIVE);
    }

    /**
     * @return ProposalStates
     */
    public static function VIEWED(): ProposalStates
    {
        return new ProposalStates(self::VIEWED);
    }

    /**
     * @return ProposalStates
     */
    public static function ACCEPTED(): ProposalStates
    {
        return new ProposalStates(self::ACCEPTED);
    }

    /**
     * @return ProposalStates
     */
    public static function REFUSED(): ProposalStates
    {
        return new ProposalStates(self::REFUSED);
    }

    /**
     * @return ProposalStates
     */
    public static function ALL(): ProposalStates
    {
        return new ProposalStates(self::ALL);
    }

    /**
     * @param $state
     * @return string
     */
    public static function toFrString($state): string
    {
        $fr = [
            self::TEMPLATE => "Modèle",
            self::DRAFT => "Brouillon",
            self::ACTIVE => "Active",
            self::VIEWED => "Lue",
            self::ACCEPTED => "Acceptée",
            self::REFUSED => "Refusée",
            self::DELETED => "Supprimée",
            self::ALL => "Tout",
        ];
        return $fr[$state];
    }

    /**
     * @return ProposalStates[]
     */
    public static function valuesStates(): array
    {
        //THE POSITION IN THIS ARRAY IS EXTREMELY IMPORTANT
        return [
            new ProposalStates(self::DRAFT),
            new ProposalStates(self::ACTIVE),
            new ProposalStates(self::VIEWED),
            new ProposalStates(self::ACCEPTED),
            new ProposalStates(self::REFUSED),
            new ProposalStates(self::TEMPLATE),
            new ProposalStates(self::ALL)
        ];
    }

    /**
     * @return ProposalStates[]
     */
    public static function updatableStates(): array
    {
        return [new ProposalStates(self::DRAFT),
            new ProposalStates(self::ACTIVE),
            new ProposalStates(self::VIEWED)
        ];
    }

    /**
     * @return ProposalStates[]
     */
    public static function finalStates(): array
    {
        return [
            new ProposalStates(self::ACCEPTED),
            new ProposalStates(self::REFUSED)
        ];
    }

    /**
     * @return string[]
     */
    public static function valuesStatesToString(): array
    {
        return [
            self::DRAFT,
            self::ACTIVE,
            self::VIEWED,
            self::ACCEPTED,
            self::REFUSED,
            self::TEMPLATE
        ];
    }
}
