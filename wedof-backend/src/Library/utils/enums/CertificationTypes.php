<?php

namespace App\Library\utils\enums;

use MyCLabs\Enum\Enum;

class CertificationTypes extends Enum
{
    private const RS = 'RS';
    private const RNCP = 'RNCP';
    private const CPF = 'CPF';
    private const ELU = 'ELU';
    private const FICHE = 'FICHE';
    private const HABILITATION = 'Habilitation';
    private const RECONNAISSANCE = 'Reconnaissance';
    private const DIPLOME_ETABLISSEMENT = "Diplôme d'Etablissement";
    private const DIPLOME_UNIVERSITAIRE = "Diplôme Universitaire";
    private const INTERNAL = "CERT";
    private const PREVENTION = "PREVENTION";

    /**
     * @return CertificationTypes
     */
    public static function RS(): CertificationTypes
    {
        return new CertificationTypes(self::RS);
    }

    /**
     * @return CertificationTypes
     */
    public static function RNCP(): CertificationTypes
    {
        return new CertificationTypes(self::RNCP);
    }

    /**
     * @return CertificationTypes
     */
    public static function CPF(): CertificationTypes
    {
        return new CertificationTypes(self::CPF);
    }

    /**
     * @return CertificationTypes
     */
    public static function ELU(): CertificationTypes
    {
        return new CertificationTypes(self::ELU);
    }

    /**
     * @return CertificationTypes
     */
    public static function FICHE(): CertificationTypes
    {
        return new CertificationTypes(self::FICHE);
    }

    /**
     * @return CertificationTypes
     */
    public static function HABILITATION(): CertificationTypes
    {
        return new CertificationTypes(self::HABILITATION);
    }


    /**
     * @return CertificationTypes
     */
    public static function RECONNAISSANCE(): CertificationTypes
    {
        return new CertificationTypes(self::RECONNAISSANCE);
    }

    /**
     * @return CertificationTypes
     */
    public static function DIPLOME_ETABLISSEMENT(): CertificationTypes
    {
        return new CertificationTypes(self::DIPLOME_ETABLISSEMENT);
    }

    /**
     * @return CertificationTypes
     */
    public static function DIPLOME_UNIVERSITAIRE(): CertificationTypes
    {
        return new CertificationTypes(self::DIPLOME_UNIVERSITAIRE);
    }

    /**
     * @return CertificationTypes
     */
    public static function INTERNAL(): CertificationTypes
    {
        return new CertificationTypes(self::INTERNAL);
    }

    /**
     * @return CertificationTypes
     */
    public static function PREVENTION(): CertificationTypes
    {
        return new CertificationTypes(self::PREVENTION);
    }


    /**
     * @return array
     */
    public static function franceCompetencesTypes(): array
    {
        return [
            new CertificationTypes(self::RS),
            new CertificationTypes(self::RNCP)
        ];
    }

    /**
     * @return string[]
     */
    public static function franceCompetencesTypeValues(): array
    {
        return [
            self::RS,
            self::RNCP
        ];
    }

    /**
     * @return CertificationTypes[]
     */
    public static function accrochageTypes(): array
    {
        return [
            new CertificationTypes(self::RNCP),
            new CertificationTypes(self::RS),
            new CertificationTypes(self::HABILITATION),
            new CertificationTypes(self::RECONNAISSANCE),
            new CertificationTypes(self::DIPLOME_ETABLISSEMENT),
            new CertificationTypes(self::DIPLOME_UNIVERSITAIRE),
        ];
    }

    /**
     * @return CertificationTypes[]
     */
    public static function valuesTypes(): array
    {
        return [
            new CertificationTypes(self::RNCP),
            new CertificationTypes(self::RS),
            new CertificationTypes(self::CPF),
            new CertificationTypes(self::ELU),
            new CertificationTypes(self::FICHE),
            new CertificationTypes(self::HABILITATION),
            new CertificationTypes(self::RECONNAISSANCE),
            new CertificationTypes(self::DIPLOME_ETABLISSEMENT),
            new CertificationTypes(self::DIPLOME_UNIVERSITAIRE),
            new CertificationTypes(self::INTERNAL),
            new CertificationTypes(self::PREVENTION),
        ];
    }
}