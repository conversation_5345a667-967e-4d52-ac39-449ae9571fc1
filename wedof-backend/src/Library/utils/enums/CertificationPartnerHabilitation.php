<?php

namespace App\Library\utils\enums;

use MyCLabs\Enum\Enum;

class CertificationPartnerHabilitation extends Enum
{
    private const EVALUATE = "evaluate";
    private const TRAIN = "train";
    private const TRAIN_EVALUATE = "train_evaluate";

    /**
     * @return CertificationPartnerHabilitation
     */
    public static function EVALUATE(): CertificationPartnerHabilitation
    {
        return new CertificationPartnerHabilitation(self::EVALUATE);
    }

    /**
     * @return CertificationPartnerHabilitation
     */
    public static function TRAIN(): CertificationPartnerHabilitation
    {
        return new CertificationPartnerHabilitation(self::TRAIN);
    }

    /**
     * @return CertificationPartnerHabilitation
     */
    public static function TRAIN_EVALUATE(): CertificationPartnerHabilitation
    {
        return new CertificationPartnerHabilitation(self::TRAIN_EVALUATE);
    }

    /**
     * @param $habilitation
     * @return string
     */
    public static function toFrString($habilitation): string
    {
        $fr = [
            self::EVALUATE => "Habilitation pour organiser l’évaluation",
            self::TRAIN => "Habilitation pour former",
            self::TRAIN_EVALUATE => "Habilitation pour former et organiser l’évaluation"
        ];
        return $fr[$habilitation];
    }
}
