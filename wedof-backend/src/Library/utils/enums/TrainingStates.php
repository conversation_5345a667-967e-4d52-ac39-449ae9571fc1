<?php

namespace App\Library\utils\enums;

use MyCLabs\Enum\Enum;

class TrainingStates extends Enum
{
    private const DRAFT = "draft";
    private const PUBLISHED = "published";
    private const UNPUBLISHED = "unpublished";
    private const ARCHIVED = "archived";
    private const DELETED = "deleted";

    /**
     * @return TrainingStates
     */
    public static function DRAFT(): TrainingStates
    {
        return new TrainingStates(self::DRAFT);
    }

    /**
     * @return TrainingStates
     */
    public static function PUBLISHED(): TrainingStates
    {
        return new TrainingStates(self::PUBLISHED);
    }

    /**
     * @return TrainingStates
     */
    public static function UNPUBLISHED(): TrainingStates
    {
        return new TrainingStates(self::UNPUBLISHED);
    }

    /**
     * @return TrainingStates
     */
    public static function ARCHIVED(): TrainingStates
    {
        return new TrainingStates(self::ARCHIVED);
    }

    /**
     * @return TrainingStates
     */
    public static function DELETED(): TrainingStates
    {
        return new TrainingStates(self::DELETED);
    }

    /**
     * @param string $state
     * @return string
     */
    public static function toFrString(string $state): string
    {
        $fr = [
            self::DRAFT => "En brouillon",
            self::PUBLISHED => "Publié",
            self::UNPUBLISHED => "Dépublié",
            self::ARCHIVED => "Archivée",
            self::DELETED => "Supprimée"
        ];
        return $fr[$state];
    }
}