<?php


namespace App\Library\utils\enums;


use MyCLabs\Enum\Enum;

class ProcessingMethod extends Enum
{

    private const BATCH = "batch";
    private const ASYNC = "async";

    /**
     * @return ProcessingMethod
     */
    public static function BATCH(): ProcessingMethod
    {
        return new ProcessingMethod(self::BATCH);
    }

    /**
     * @return ProcessingMethod
     */
    public static function ASYNC(): ProcessingMethod
    {
        return new ProcessingMethod(self::ASYNC);
    }
}