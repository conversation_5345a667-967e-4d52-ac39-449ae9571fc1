<?php

namespace App\Library\utils\enums;

use MyCLabs\Enum\Enum;

class SessionStates extends Enum
{
    private const DRAFT = "draft";
    private const PUBLISHED = "published";
    private const UNPUBLISHED = "unpublished";
    private const ARCHIVED = "archived";
    private const DELETED = "deleted";

    /**
     * @return SessionStates
     */
    public static function DRAFT(): SessionStates
    {
        return new SessionStates(self::DRAFT);
    }

    /**
     * @return SessionStates
     */
    public static function PUBLISHED(): SessionStates
    {
        return new SessionStates(self::PUBLISHED);
    }

    /**
     * @return SessionStates
     */
    public static function UNPUBLISHED(): SessionStates
    {
        return new SessionStates(self::UNPUBLISHED);
    }

    /**
     * @return SessionStates
     */
    public static function ARCHIVED(): SessionStates
    {
        return new SessionStates(self::ARCHIVED);
    }

    /**
     * @return SessionStates
     */
    public static function DELETED(): SessionStates
    {
        return new SessionStates(self::DELETED);
    }

    /**
     * @param string $state
     * @return string
     */
    public static function toFrString(string $state): string
    {
        $fr = [
            self::DRAFT => "En brouillon",
            self::PUBLISHED => "Publié",
            self::UNPUBLISHED => "Dépublié",
            self::ARCHIVED => "Archivée",
            self::DELETED => "Supprimée"
        ];
        return $fr[$state];
    }
}