<?php

namespace App\Library\utils\enums;

use My<PERSON><PERSON>s\Enum\Enum;

class CertificationFolderSurveyExperience extends Enum
{
    private const INITIAL_EXPERIENCE = "initialExperience";
    private const SIX_MONTH_EXPERIENCE = "sixMonthExperience";
    private const LONG_TERM_EXPERIENCE = "longTermExperience";

    /**
     * @return CertificationFolderSurveyExperience
     */
    public static function INITIAL_EXPERIENCE(): CertificationFolderSurveyExperience
    {
        return new CertificationFolderSurveyExperience(self::INITIAL_EXPERIENCE);
    }

    /**
     * @return CertificationFolderSurveyExperience
     */
    public static function SIX_MONTH_EXPERIENCE(): CertificationFolderSurveyExperience
    {
        return new CertificationFolderSurveyExperience(self::SIX_MONTH_EXPERIENCE);
    }

    /**
     * @return CertificationFolderSurveyExperience
     */
    public static function LONG_TERM_EXPERIENCE(): CertificationFolderSurveyExperience
    {
        return new CertificationFolderSurveyExperience(self::LONG_TERM_EXPERIENCE);
    }

    /**
     * @param $experienceName
     * @return string
     */
    public static function toFrString($experienceName): string
    {
        $fr = [
            self::INITIAL_EXPERIENCE => "Questionnaire en début de cursus certifiant",
            self::SIX_MONTH_EXPERIENCE => "Questionnaire 6 mois après la certification",
            self::LONG_TERM_EXPERIENCE => "Questionnaire à moyen ou long terme",
        ];
        return $fr[$experienceName];
    }

    /**
     * @return CertificationFolderSurveyExperience[]
     */
    public static function valuesStates(): array
    {
        return [
            new CertificationFolderSurveyExperience(self::INITIAL_EXPERIENCE),
            new CertificationFolderSurveyExperience(self::SIX_MONTH_EXPERIENCE),
            new CertificationFolderSurveyExperience(self::LONG_TERM_EXPERIENCE)
        ];
    }

}
