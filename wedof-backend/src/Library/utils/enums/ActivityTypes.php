<?php

namespace App\Library\utils\enums;

use MyCLabs\Enum\Enum;

class ActivityTypes extends Enum
{
    // Updates
    private const CREATE = 'create';
    private const UPDATE = 'update';
    private const UPDATE_STATE = 'updateState';
    // Contact
    private const PHONE = 'phone';
    private const EMAIL = 'email';
    private const MEETING = 'meeting';
    private const CHAT = 'chat';
    private const SMS = 'sms';
    // Business event
    private const PROGRESS = 'progress';
    private const EXAMINATION = 'examination';
    private const TRAINING = 'training';
    private const CDC = 'cdc';
    // Other
    private const REMARK = 'remark';
    private const ALL = 'all';
    private const FILE = 'file';
    private const CONNECTION_LOG = 'connectionLog';

    /**
     * @return ActivityTypes
     */
    public static function CREATE(): ActivityTypes
    {
        return new ActivityTypes(self::CREATE);
    }

    /**
     * @return ActivityTypes
     */
    public static function UPDATE(): ActivityTypes
    {
        return new ActivityTypes(self::UPDATE);
    }

    /**
     * @return ActivityTypes
     */
    public static function UPDATE_STATE(): ActivityTypes
    {
        return new ActivityTypes(self::UPDATE_STATE);
    }

    /**
     * @return ActivityTypes
     */
    public static function PHONE(): ActivityTypes
    {
        return new ActivityTypes(self::PHONE);
    }

    /**
     * @return ActivityTypes
     */
    public static function EMAIL(): ActivityTypes
    {
        return new ActivityTypes(self::EMAIL);
    }

    /**
     * @return ActivityTypes
     */
    public static function MEETING(): ActivityTypes
    {
        return new ActivityTypes(self::MEETING);
    }

    /**
     * @return ActivityTypes
     */
    public static function CHAT(): ActivityTypes
    {
        return new ActivityTypes(self::CHAT);
    }

    /**
     * @return ActivityTypes
     */
    public static function SMS(): ActivityTypes
    {
        return new ActivityTypes(self::SMS);
    }

    /**
     * @return ActivityTypes
     */
    public static function PROGRESS(): ActivityTypes
    {
        return new ActivityTypes(self::PROGRESS);
    }

    /**
     * @return ActivityTypes
     */
    public static function EXAMINATION(): ActivityTypes
    {
        return new ActivityTypes(self::EXAMINATION);
    }

    /**
     * @return ActivityTypes
     */
    public static function TRAINING(): ActivityTypes
    {
        return new ActivityTypes(self::TRAINING);
    }

    /**
     * @return ActivityTypes
     */
    public static function CDC(): ActivityTypes
    {
        return new ActivityTypes(self::CDC);
    }

    /**
     * @return ActivityTypes
     */
    public static function REMARK(): ActivityTypes
    {
        return new ActivityTypes(self::REMARK);
    }

    /**
     * @return ActivityTypes
     */
    public static function FILE(): ActivityTypes
    {
        return new ActivityTypes(self::FILE);
    }

    /**
     * @return ActivityTypes
     */
    public static function CONNECTION_LOG(): ActivityTypes
    {
        return new ActivityTypes(self::CONNECTION_LOG);
    }

    /**
     * @return ActivityTypes
     */
    public static function ALL(): ActivityTypes
    {
        return new ActivityTypes(self::ALL);
    }

    /**
     * @return ActivityTypes[]
     */
    public static function valuesTypes(): array
    {
        return [
            new ActivityTypes(self::CREATE),
            new ActivityTypes(self::UPDATE_STATE),
            new ActivityTypes(self::UPDATE),
            new ActivityTypes(self::PHONE),
            new ActivityTypes(self::EMAIL),
            new ActivityTypes(self::MEETING),
            new ActivityTypes(self::CHAT),
            new ActivityTypes(self::SMS),
            new ActivityTypes(self::PROGRESS),
            new ActivityTypes(self::EXAMINATION),
            new ActivityTypes(self::TRAINING),
            new ActivityTypes(self::CDC),
            new ActivityTypes(self::REMARK),
            new ActivityTypes(self::FILE),
            new ActivityTypes(self::CONNECTION_LOG),
            new ActivityTypes(self::ALL)
        ];
    }

    /**
     * @return string[]
     */
    public static function createOrUpdateValuesTypeToString(): array
    {
        return [
            self::CREATE,
            self::UPDATE_STATE,
            self::UPDATE,
            self::PHONE,
            self::EMAIL,
            self::MEETING,
            self::CHAT,
            self::SMS,
            self::EXAMINATION,
            self::TRAINING,
            self::CDC,
            self::REMARK,
            self::FILE,
            self::CONNECTION_LOG
        ];
    }

    /**
     * @param string $type
     * @return string
     */
    public static function toFrString(string $type): string
    {
        $fr = [
            self::CREATE => "Création",
            self::UPDATE_STATE => "Mise à jour de l'état",
            self::UPDATE => "Mise à jour",
            self::PHONE => "Téléphone",
            self::EMAIL => "Email",
            self::MEETING => "Meeting",
            self::CHAT => "Chat",
            self::SMS => "SMS",
            self::PROGRESS => "Taux de réalisation",
            self::EXAMINATION => "Examen",
            self::TRAINING => "Formation",
            self::CDC => "Certification",
            self::REMARK => "Remarque",
            self::FILE => "Document",
            self::CONNECTION_LOG => "Log de connexion",
        ];
        return $fr[$type];
    }
}
