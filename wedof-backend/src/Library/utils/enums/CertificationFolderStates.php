<?php

namespace App\Library\utils\enums;

use MyCLabs\Enum\Enum;

class CertificationFolderStates extends Enum
{
    private const TO_REGISTER = "toRegister";
    private const REFUSED = "refused";
    private const REGISTERED = "registered";
    private const TO_TAKE = "toTake";
    private const TO_CONTROL = "toControl";
    private const TO_RETAKE = "toRetake";
    private const FAILED = "failed";
    private const ABORTED = "aborted";
    private const SUCCESS = "success";

    private const ALL = "all";

    /**
     * @return CertificationFolderStates
     */
    public static function TO_REGISTER(): CertificationFolderStates
    {
        return new CertificationFolderStates(self::TO_REGISTER);
    }

    /**
     * @return CertificationFolderStates
     */
    public static function REFUSED(): CertificationFolderStates
    {
        return new CertificationFolderStates(self::REFUSED);
    }

    /**
     * @return CertificationFolderStates
     */
    public static function REGISTERED(): CertificationFolderStates
    {
        return new CertificationFolderStates(self::REGISTERED);
    }

    /**
     * @return CertificationFolderStates
     */
    public static function TO_TAKE(): CertificationFolderStates
    {
        return new CertificationFolderStates(self::TO_TAKE);
    }

    /**
     * @return CertificationFolderStates
     */
    public static function TO_CONTROL(): CertificationFolderStates
    {
        return new CertificationFolderStates(self::TO_CONTROL);
    }

    /**
     * @return CertificationFolderStates
     */
    public static function TO_RETAKE(): CertificationFolderStates
    {
        return new CertificationFolderStates(self::TO_RETAKE);
    }

    /**
     * @return CertificationFolderStates
     */
    public static function FAILED(): CertificationFolderStates
    {
        return new CertificationFolderStates(self::FAILED);
    }

    /**
     * @return CertificationFolderStates
     */
    public static function ABORTED(): CertificationFolderStates
    {
        return new CertificationFolderStates(self::ABORTED);
    }

    /**
     * @return CertificationFolderStates
     */
    public static function SUCCESS(): CertificationFolderStates
    {
        return new CertificationFolderStates(self::SUCCESS);
    }

    /**
     * @return CertificationFolderStates
     */
    public static function ALL(): CertificationFolderStates
    {
        return new CertificationFolderStates(self::ALL);
    }

    /**
     * @param $state
     * @return string
     */
    public static function toFrString($state): string
    {
        return self::toFrStrings()[$state];
    }

    /**
     * @param string $frString
     * @return CertificationFolderStates|null
     */
    public static function fromFrString(string $frString): ?CertificationFolderStates
    {
        $stateString = array_flip(self::toFrStrings())[$frString];
        return $stateString ? new CertificationFolderStates($stateString) : null;
    }

    /**
     * @return string[]
     */
    public static function valuesStatesToString(): array
    {
        return [
            self::TO_REGISTER,
            self::REFUSED,
            self::REGISTERED,
            self::TO_TAKE,
            self::TO_CONTROL,
            self::TO_RETAKE,
            self::FAILED,
            self::ABORTED,
            self::SUCCESS
        ];
    }

    /**
     * @return CertificationFolderStates[]
     */
    public static function valuesStates(): array
    {
        //THE POSITION IN THIS ARRAY IS EXTREMELY IMPORTANT
        return [
            new CertificationFolderStates(self::TO_REGISTER),
            new CertificationFolderStates(self::REGISTERED),
            new CertificationFolderStates(self::TO_TAKE),
            new CertificationFolderStates(self::TO_CONTROL),
            new CertificationFolderStates(self::TO_RETAKE),
            new CertificationFolderStates(self::SUCCESS),
            new CertificationFolderStates(self::REFUSED),
            new CertificationFolderStates(self::FAILED),
            new CertificationFolderStates(self::ABORTED),
            new CertificationFolderStates(self::ALL)
        ];
    }

    /**
     * @return CertificationFolderStates[]
     */
    public static function updatableStates(): array
    {
        return [
            new CertificationFolderStates(self::TO_REGISTER),
            new CertificationFolderStates(self::REGISTERED),
            new CertificationFolderStates(self::TO_TAKE),
            new CertificationFolderStates(self::TO_CONTROL),
            new CertificationFolderStates(self::TO_RETAKE)
        ];
    }

    /**
     * @return CertificationFolderStates[]
     */
    public static function abortableStates(): array
    {
        return [
            new CertificationFolderStates(self::TO_REGISTER),
            new CertificationFolderStates(self::REGISTERED),
            new CertificationFolderStates(self::TO_TAKE),
            new CertificationFolderStates(self::TO_RETAKE) // WHY
        ];
    }

    public static function isForwardTransition(string $originState, CertificationFolderStates $targetState): bool
    {
        $forwardStates = [
            self::TO_REGISTER => [self::REGISTERED(), self::REFUSED(), self::ABORTED(), self::TO_TAKE(), self::TO_CONTROL(), self::TO_RETAKE(), self::FAILED(), self::SUCCESS()],
            self::REGISTERED => [self::ABORTED(), self::TO_TAKE(), self::TO_CONTROL(), self::TO_RETAKE(), self::FAILED(), self::SUCCESS()],
            self::REFUSED => [],
            self::ABORTED => [],
            self::TO_TAKE => [self::ABORTED(), self::TO_CONTROL(), self::TO_RETAKE(), self::FAILED(), self::SUCCESS()],
            self::TO_CONTROL => [self::TO_RETAKE(), self::FAILED(), self::SUCCESS()],
            self::TO_RETAKE => [self::TO_CONTROL(), self::FAILED(), self::SUCCESS()],
            self::FAILED => [self::TO_RETAKE(), self::TO_CONTROL(), self::FAILED(), self::SUCCESS()],
            self::SUCCESS => [],
        ];
        return in_array($targetState, $forwardStates[$originState]);
    }

    //-----------------
    // METHODES PRIVEES
    //-----------------

    /**
     * @return array
     */
    private static function toFrStrings(): array
    {
        return [
            self::TO_REGISTER => "À enregistrer",
            self::REFUSED => "Refusé",
            self::REGISTERED => "Enregistré",
            self::TO_TAKE => "Prêt à passer",
            self::TO_CONTROL => "À contrôler",
            self::TO_RETAKE => "À repasser",
            self::FAILED => "Échoué",
            self::ABORTED => "Abandonné",
            self::SUCCESS => "Réussi"
        ];
    }
}
