<?php

namespace App\Library\utils\enums;

use MyCLabs\Enum\Enum;

class PassportType extends Enum
{
    private const COMPETENCES = "competences";
    private const PREVENTION = "prevention";

    /**
     * @return PassportType
     */
    public static function COMPETENCES(): PassportType
    {
        return new PassportType(self::COMPETENCES);
    }

    /**
     * @return PassportType
     */
    public static function PREVENTION(): PassportType
    {
        return new PassportType(self::PREVENTION);
    }


}