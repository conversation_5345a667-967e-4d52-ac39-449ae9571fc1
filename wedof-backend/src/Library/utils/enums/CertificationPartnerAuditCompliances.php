<?php

namespace App\Library\utils\enums;

use MyCLabs\Enum\Enum;

class CertificationPartnerAuditCompliances extends Enum
{
    private const COMPLIANT = 'compliant';
    private const PARTIALLY_COMPLIANT = 'partiallyCompliant'; // for later use
    private const NON_COMPLIANT = 'nonCompliant';
    private const NOT_APPLICABLE = 'notApplicable'; // for later use

    /**
     * @return CertificationPartnerAuditCompliances
     */
    public static function COMPLIANT(): CertificationPartnerAuditCompliances
    {
        return new CertificationPartnerAuditCompliances(self::COMPLIANT);
    }

    /**
     * @return CertificationPartnerAuditCompliances
     */
    public static function PARTIALLY_COMPLIANT(): CertificationPartnerAuditCompliances
    {
        return new CertificationPartnerAuditCompliances(self::PARTIALLY_COMPLIANT);
    }

    /**
     * @return CertificationPartnerAuditCompliances
     */
    public static function NON_COMPLIANT(): CertificationPartnerAuditCompliances
    {
        return new CertificationPartnerAuditCompliances(self::NON_COMPLIANT);
    }

    /**
     * @return CertificationPartnerAuditCompliances
     */
    public static function NOT_APPLICABLE(): CertificationPartnerAuditCompliances
    {
        return new CertificationPartnerAuditCompliances(self::NOT_APPLICABLE);
    }

    public static function toFrString(string $compliance): string
    {
        $fr = [
            self::COMPLIANT => 'Conforme',
            self::PARTIALLY_COMPLIANT => 'Partiellement conforme',
            self::NON_COMPLIANT => 'Non conforme',
            self::NOT_APPLICABLE => 'Non applicable',
        ];
        return $fr[$compliance];
    }
}