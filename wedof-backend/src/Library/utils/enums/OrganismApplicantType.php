<?php


namespace App\Library\utils\enums;


use MyCLabs\Enum\Enum;

class OrganismApplicantType extends Enum
{
    private const CERTIFICATEUR = "CERTIFICATEUR";
    private const TIERS_CONFIANCE = "TIERS_CONFIANCE";


    /**
     * @return OrganismApplicantType
     */
    public static function CERTIFICATEUR(): OrganismApplicantType
    {
        return new OrganismApplicantType(self::CERTIFICATEUR);
    }

    /**
     * @return OrganismApplicantType
     */
    public static function TIERS_CONFIANCE(): OrganismApplicantType
    {
        return new OrganismApplicantType(self::TIERS_CONFIANCE);
    }

    /**
     * @param $applicantType
     * @return string
     */
    public static function toFrString($applicantType): string
    {
        $fr = [
            self::CERTIFICATEUR => "Certificateur",
            self::TIERS_CONFIANCE => "Tiers confiance",
        ];
        return $fr[$applicantType];
    }

    /**
     * @return OrganismApplicantType[]
     */
    public static function valuesTypes(): array
    {
        return [
            new OrganismApplicantType(self::CERTIFICATEUR),
            new OrganismApplicantType(self::TIERS_CONFIANCE)
        ];
    }
}