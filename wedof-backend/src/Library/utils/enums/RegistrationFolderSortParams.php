<?php
// src/Librairy.utils/RegistrationFolderSortParams.php
namespace App\Library\utils\enums;

use MyCLabs\Enum\Enum;

class RegistrationFolderSortParams extends Enum
{
    private const ETAT = "ETAT";
    private const INTITULE_FORMATION = "INTITULE_FORMATION";
    private const DATE_DERNIERE_ACTION = "DATE_DERNIERE_ACTION";
    private const DATE_DEBUT_SESSION = "DATE_DEBUT_SESSION";
    private const DATE_DERNIERE_MAJ_CONTROLE = "DATE_DERNIERE_MAJ_CONTROLE";
    private const ETAT_CONTROLE = "ETAT_CONTROLE";
    private const TYPE_CONTROLE = "TYPE_CONTROLE";
    private const SIRET_OF = "SIRET_OF";
    private const ID_FONCTIONNEL = "ID_FONCTIONNEL";
    private const STATUT_DECLARATION_OF = "STATUT_DECLARATION_OF";

    /**
     * @return RegistrationFolderSortParams
     */
    public static function ETAT(): RegistrationFolderSortParams
    {
        return new RegistrationFolderSortParams(self::ETAT);
    }

    /**
     * @return RegistrationFolderSortParams
     */
    public static function INTITULE_FORMATION(): RegistrationFolderSortParams
    {
        return new RegistrationFolderSortParams(self::INTITULE_FORMATION);
    }

    /**
     * @return RegistrationFolderSortParams
     */
    public static function DATE_DERNIERE_ACTION(): RegistrationFolderSortParams
    {
        return new RegistrationFolderSortParams(self::DATE_DERNIERE_ACTION);
    }

    /**
     * @return RegistrationFolderSortParams
     */
    public static function DATE_DEBUT_SESSION(): RegistrationFolderSortParams
    {
        return new RegistrationFolderSortParams(self::DATE_DEBUT_SESSION);
    }

    /**
     * @return RegistrationFolderSortParams
     */
    public static function DATE_DERNIERE_MAJ_CONTROLE(): RegistrationFolderSortParams
    {
        return new RegistrationFolderSortParams(self::DATE_DERNIERE_MAJ_CONTROLE);
    }

    /**
     * @return RegistrationFolderSortParams
     */
    public static function ETAT_CONTROLE(): RegistrationFolderSortParams
    {
        return new RegistrationFolderSortParams(self::ETAT_CONTROLE);
    }

    /**
     * @return RegistrationFolderSortParams
     */
    public static function TYPE_CONTROLE(): RegistrationFolderSortParams
    {
        return new RegistrationFolderSortParams(self::TYPE_CONTROLE);
    }

    /**
     * @return RegistrationFolderSortParams
     */
    public static function SIRET_OF(): RegistrationFolderSortParams
    {
        return new RegistrationFolderSortParams(self::SIRET_OF);
    }

    /**
     * @return RegistrationFolderSortParams
     */
    public static function ID_FONCTIONNEL(): RegistrationFolderSortParams
    {
        return new RegistrationFolderSortParams(self::ID_FONCTIONNEL);
    }

    /**
     * @return RegistrationFolderSortParams
     */
    public static function STATUT_DECLARATION_OF(): RegistrationFolderSortParams
    {
        return new RegistrationFolderSortParams(self::STATUT_DECLARATION_OF);
    }
}
