<?php

namespace App\Library\utils;

class VariableParser
{
    const FUNCTION_SEPARATOR = '|';
    const ARGUMENT_SEPARATOR = ':';
    const FUNCTIONS = [
        'truncate' => [Tools::class, 'truncate'],
        'lowercase' => 'mb_strtolower',
        'uppercase' => 'mb_strtoupper',
        // Ajoutez d'autres fonctions ici
    ];

    /**
     * @param string $variable
     * @param string $value
     * @return string
     */
    public static function parseVariable(string $variable, string $value): string
    {
        if (empty($variable) || strpos($variable, self::FUNCTION_SEPARATOR) === false) {
            return $value;
        }
        $parts = explode(self::FUNCTION_SEPARATOR, $variable);
        // Apply functions in sequence from left to right (skip part[0] as it is the variable itself)
        for ($i = 1; $i < count($parts); $i++) {
            $fullFunction = trim($parts[$i]);
            $functionParts = explode(self::ARGUMENT_SEPARATOR, $fullFunction, 2); // Split at first argument
            $functionName = trim($functionParts[0]);
            if (isset(self::FUNCTIONS[$functionName])) {
                $function = self::FUNCTIONS[$functionName];
                $functionArguments = isset($functionParts[1]) ? array_map('trim', explode(self::ARGUMENT_SEPARATOR, $functionParts[1])) : []; // Support multiple argument
                array_unshift($functionArguments, $value);
                $value = call_user_func_array($function, $functionArguments);
            }
        }
        return $value;
    }
}
