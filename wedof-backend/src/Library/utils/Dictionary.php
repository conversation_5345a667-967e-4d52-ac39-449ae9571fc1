<?php

namespace App\Library\utils;

use App\Application\Signature\SignatureWedofApplication;
use App\Entity\Attendee;
use App\Entity\CertificationFolder;
use App\Entity\CertificationFolderSurvey;
use App\Entity\CertificationPartner;
use App\Entity\CertificationPartnerAudit;
use App\Entity\CertificationPartnerAuditTemplate;
use App\Entity\Organism;
use App\Entity\Proposal;
use App\Entity\RegistrationFolder;
use App\Exception\WedofBadRequestHttpException;
use App\Repository\CertificationFolderRepository;
use App\Repository\CertificationPartnerRepository;
use App\Repository\OrganismRepository;
use App\Repository\ProposalRepository;
use App\Repository\RegistrationFolderRepository;
use App\Service\ApplicationService;
use App\Service\CertificationPartnerService;
use DateTime;
use DateTimeZone;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\Exception\NotSupported;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Exception;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\ContainerInterface;
use Psr\Container\NotFoundExceptionInterface;

class Dictionary
{
    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    /**
     * @param string $type
     * @param string $scope
     * @param array $context
     * @param ContainerInterface|null $container
     * @return array
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws NotSupported
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws \Doctrine\DBAL\Exception
     * @throws ORMException
     * @throws NonUniqueResultException
     * @throws \Doctrine\ORM\ORMException
     */
    public static function getDictionaryFor(string $type, string $scope, array $context = [], ContainerInterface $container = null): array
    {
        $dictionary = array_values(array_filter(self::availableStaticVariables(), function (array $array) use ($type, $scope) {
            return in_array($type, $array["availableIn"]) && (!$scope || in_array($scope, $array["scopes"]));
        }));

        if ($container && $context['organism'] != null) {
            $currentOrganism = $context['organism'];
            foreach (['proposal', 'registrationFolder', 'certificationFolder', 'certificationPartner', 'myOrganism'] as $keyWithMetadata) {
                $dictionary = self::addMetadataDictionary($dictionary, $currentOrganism, $keyWithMetadata, $container);
            }
        }

        if ($scope === 'audit' && !empty($context['certificationPartnerAuditTemplate'])) {
            /** @var CertificationPartnerAuditTemplate $certificationPartnerAuditTemplate */
            $certificationPartnerAuditTemplate = $context['certificationPartnerAuditTemplate'];
            $criterias = $certificationPartnerAuditTemplate->getCriterias();
            array_unshift($dictionary, [
                "name" => "Audit",
                "key" => "audit",
                "availableIn" => ['document'],
                "scopes" => ['audit'],
                "values" => array_merge([
                    [
                        "key" => "startDate",
                        "name" => "Date d'enregistrement des données",
                        "example" => ""
                    ],
                    [
                        "key" => "nbCriterias",
                        "name" => "Nombre total de critères",
                        "example" => ""
                    ],
                    [
                        "key" => "nbCriteriasCompliant",
                        "name" => "Nombre de critères conformes",
                        "example" => ""
                    ],
                    [
                        "key" => "nbCriteriasNonCompliant",
                        "name" => "Nombre de critères non conformes",
                        "example" => ""
                    ]
                ], ...array_values(array_map('self::getAuditVariables', $criterias)))
            ]);
        }
        if ($type === 'document' && isset($context['fileType'])) {
            $values = [];
            /** @var ApplicationService $applicationService */
            $applicationService = $container->get('App\Service\ApplicationService');
            $application = $applicationService->getByOrganismAndAppId($context['organism'], SignatureWedofApplication::getAppId(), true, false);
            if (!empty($application->getMetadata()['signature'])) {
                $values = array_merge($values, [
                    [
                        "key" => "owner",
                        "name" => "Signature de votre organisme (automatique)",
                        "example" => "YYY"
                    ]
                ]);
            }
            if (!empty($application->getMetadata()['initials'])) {
                $values = array_merge($values, [
                    [
                        "key" => "ownerInitials",
                        "name" => "Paraphe de votre organisme (automatique)",
                        "example" => "Y"
                    ]
                ]);
            }
            if (!empty($application->getMetadata()['stamp'])) {
                $values = array_merge($values, [
                    [
                        "key" => "ownerStamp",
                        "name" => "Tampon de votre organisme (automatique)",
                        "example" => "Z"
                    ]
                ]);
            }
            if (!empty($application->getMetadata()['signature'])) {
                $values = array_merge($values, [
                    [
                        "key" => "ownerDate",
                        "name" => "Date de votre signature (automatique)",
                        "example" => "12/12/2012"
                    ]
                ]);
            }
            if (!empty($context['fileType']['allowSignAttendee'])) {
                $values = array_merge($values, [
                    [
                        "key" => "attendee",
                        "name" => "Signature du candidat / apprenant",
                        "example" => "YYY"
                    ],
                    [
                        "key" => "attendeeInitials",
                        "name" => "Paraphe du candidat / apprenant",
                        "example" => "Y"
                    ],
                    [
                        "key" => "attendeeDate",
                        "name" => "Date de signature du candidat / apprenant",
                        "example" => "12/12/2012"
                    ]
                ]);
            }
            if (!empty($context['fileType']['allowSignPartner'])) {
                $values = array_merge($values, [
                    [
                        "key" => "partner",
                        "name" => "Signature du partenaire",
                        "example" => "XXX"
                    ],
                    [
                        "key" => "partnerInitials",
                        "name" => "Paraphe du partenaire",
                        "example" => "X"
                    ],
                    [
                        "key" => "partnerDate",
                        "name" => "Date de signature du partenaire",
                        "example" => "12/12/2012"
                    ]
                ]);
            }
            if ($values) {
                array_unshift($dictionary, [
                        "name" => "Signature",
                        "key" => "signature",
                        "availableIn" => ['document'],
                        "scopes" => [$scope],
                        "values" => $values
                    ]
                );
            }
        }
        return $dictionary;
    }

    // TODO si changement ici, faire la modif aussi dans dictionnaire.md dans le front, merci
    public static function availableStaticVariables(): array
    {
        return [
            [
                "name" => "Parchemin de certification",
                "key" => "", // volontairement pas mis de key sinon la valeur sera {{certificate.xxx}} hors dans les parchemins on a directement les valeurs sans la key
                "availableIn" => ["document"],
                "scopes" => ["certificate"],
                "values" => [
                    [
                        "key" => "prenomNom",
                        "name" => "Identité du titulaire",
                        "rawKey" => "attendee.displayName",
                        "example" => "John Doe"
                    ],
                    [
                        "key" => "date",
                        "name" => "Date de réussite",
                        "rawKey" => "certificationFolder.issueDate",
                        "example" => (new DateTime())->setTimezone(new DateTimeZone('Europe/Paris'))->format("d/m/Y")
                    ],
                    [
                        "key" => "identifiant",
                        "name" => "Identifiant du dossier",
                        "rawKey" => "certificateId",
                        "example" => "1234567"
                    ],
                    [
                        "key" => "nomPartenaire",
                        "name" => "Nom de l'organisme de formation partenaire",
                        "rawKey" => "trainingOrganism.name",
                        "example" => "Wedof Formation"
                    ],
                    [
                        "key" => "nomCertificateur",
                        "name" => "Nom de l'organisme certificateur",
                        "rawKey" => "certifierOrganism.name",
                        "example" => "Wedof Certification"
                    ]
                ],
            ],
            [
                "name" => "Fonctions",
                "key" => "",
                "availableIn" => ["document", "message"],
                "scopes" => ["registrationFolder", "certificationFolder", "certificate", "certificationPartner", "proposal", "audit", "certificationFolderSurvey"],
                "values" => [
                    [
                        "key" => "variable|truncate:nbChar",
                        "name" => "Réduit 'variable' à 'nbChar' caractères",
                        "example" => ""
                    ],
                    [
                        "key" => "variable|uppercase",
                        "name" => "Converti tous les caractères de 'variable' en majuscule",
                        "example" => ""
                    ],
                    [
                        "key" => "variable|lowercase",
                        "name" => "Converti tous les caractères de 'variable' en minuscule",
                        "example" => ""
                    ]
                ]
            ],
            [
                "name" => "Commun",
                "key" => "common",
                "availableIn" => ["document", "message"],
                "scopes" => ["registrationFolder", "certificationFolder", "certificate", "certificationPartner", "proposal", "audit", "certificationFolderSurvey"],
                "values" => [
                    [
                        "key" => "date",
                        "name" => "Date du jour",
                        "example" => (new DateTime())->setTimezone(new DateTimeZone('Europe/Paris'))->format("d/m/Y")
                    ],
                    [
                        "key" => "datetime",
                        "name" => "Date et heure du jour",
                        "example" => (new DateTime())->setTimezone(new DateTimeZone('Europe/Paris'))->format("d/m/Y à H\hi")
                    ]
                ]
            ],
            [
                "name" => "Candidat / Apprenant",
                "key" => "attendee",
                "entityClass" => Attendee::CLASSNAME,
                "availableIn" => ["document", "message"],
                "scopes" => ["certificate", "registrationFolder", "certificationFolder", "certificationFolderSurvey", "proposal"],
                "values" => [
                    [
                        "key" => "firstName",
                        "name" => "Prénom",
                        "example" => "John"
                    ],
                    [
                        "key" => "lastName",
                        "name" => "Nom",
                        "example" => "Doe"
                    ],
                    [
                        "key" => "email",
                        "name" => "Email",
                        "example" => "<EMAIL>"
                    ],
                    [
                        "key" => "dateOfBirth",
                        "name" => "Date de naissance",
                        "example" => "18/04/1993"
                    ],
                    [
                        "key" => "placeOfBirth",
                        "rawKey" => "attendee.getPlaceOfBirth",
                        "name" => "Lieu de naissance",
                        "example" => "Toulouse (31)"
                    ],
                    [
                        "key" => "phoneNumber",
                        "name" => "Numéro de téléphone",
                        "example" => "0650505050"
                    ],
                    [
                        "key" => "phoneFixed",
                        "name" => "Numéro de téléphone fixe",
                        "example" => "0987654321"
                    ],
                    [
                        "key" => "gender",
                        "name" => "Genre",
                        "enum" => "App\\Library\\utils\\enums\\AttendeeGender",
                        "example" => "M"
                    ],
                    [
                        "key" => "degreeTitle",
                        "name" => "Titre du diplôme",
                        "example" => "niveau Bac+3 (Licence)"
                    ],
                    [
                        "key" => "fullAddress",
                        "name" => "Adresse complète",
                        "example" => "8 impasse Bonnet 31500 Toulouse"
                    ],
                ]
            ],
            [
                "name" => "Formation",
                "key" => "training",
                "availableIn" => ["document", "message"],
                "scopes" => ["registrationFolder", "certificationFolder", "proposal"],
                "values" => [
                    [
                        "key" => "title",
                        "rawKey" => "registrationFolder.trainingActionInfo.[title]",
                        "name" => "Nom de la formation",
                        "example" => "Formation en marketing"
                    ],
                    [
                        "key" => "expectedResult",
                        "rawKey" => "registrationFolder.rawdata.[trainingActionInfo].[expectedResult]",
                        "name" => "Résultat attendu de la formation",
                        "example" => "Maitrise des notions de marketing"
                    ],
                    [
                        "key" => "teachingFees",
                        "rawKey" => "registrationFolder.rawdata.[trainingActionInfo].[teachingFees]",
                        "name" => "Tarif de la formation",
                        "example" => "1200"
                    ],
                    [
                        "key" => "conditionsPrerequisitesDetails",
                        "rawKey" => "registrationFolder.rawdata.[trainingActionInfo].[conditionsPrerequisitesDetails]",
                        "name" => "Prérequis de la formation",
                        "example" => "Maitrise de l'anglais"
                    ],
                    [
                        "key" => "sessionStartDate",
                        "rawKey" => "registrationFolder.trainingActionInfo.[sessionStartDate]",
                        "name" => "Date de début de session",
                        "example" => "10/12/2023"
                    ],
                    [
                        "key" => "sessionEndDate",
                        "rawKey" => "registrationFolder.trainingActionInfo.[sessionEndDate]",
                        "name" => "Date de fin de session",
                        "example" => "15/12/2023"
                    ],
                    [
                        "key" => "indicativeDuration",
                        "rawKey" => "registrationFolder.trainingActionInfo.[duration]",
                        "name" => "Durée de la formation",
                        "example" => "24"
                    ],
                    [
                        "key" => "trainingGoal",
                        "rawKey" => "registrationFolder.rawdata.[trainingActionInfo].[trainingGoal]",
                        "name" => "Objectif pédagogique",
                        "example" => "Savoir [...] Comprendre [...] Appliquer [...]"
                    ]
                ]
            ],
            [
                "name" => "Proposition commerciale",
                "key" => "proposal",
                "entityClass" => Proposal::CLASSNAME,
                "availableIn" => ["message"],
                "scopes" => ["proposal", "registrationFolder", "certificationFolder", "certificationFolderSurvey"],
                "values" => [
                    [
                        "key" => "state",
                        "enum" => "App\\Library\\utils\\enums\\ProposalStates",
                        "name" => "État de la proposition",
                        "example" => "Vue"
                    ],
                    [
                        "key" => "code",
                        "name" => "Code de la proposition",
                        "example" => "H123AB"
                    ],
                    [
                        "key" => "permalink",
                        "name" => "Lien interne Wedof",
                        "example" => "https://www.wedof.fr/proposition-XXXXXX"
                    ],
                    [
                        "key" => "link",
                        "name" => "Lien Apprenant",
                        "example" => "https://www.wedof.fr/funnel/apprenant/proposition/XXXXXX"
                    ],
                    [
                        "key" => "link_commercial",
                        "rawKey" => "proposal.linkCommercial",
                        "name" => "Lien Commercial",
                        "example" => "https://www.wedof.fr/funnel/commercial/proposition/XXXXXX"
                    ],
                    [
                        "key" => "email",
                        "name" => "Email",
                        "example" => "<EMAIL>"
                    ],
                    [
                        "key" => "firstName",
                        "name" => "Prénom",
                        "example" => "John"
                    ],
                    [
                        "key" => "lastName",
                        "name" => "Nom de famille",
                        "example" => "Doe"
                    ],
                    [
                        "key" => "phoneNumber",
                        "name" => "Numéro de téléphone",
                        "example" => "0650505050"
                    ],
                    [
                        "key" => "sessionStartDate",
                        "name" => "Date de début de session",
                        "example" => "12 Juin 2024"
                    ],
                    [
                        "key" => "sessionEndDate",
                        "name" => "Date de fin de session",
                        "example" => "15 Juin 2024"
                    ],
                    [
                        "key" => "expire",
                        "name" => "Date d'expiration",
                        "example" => "09/06/2024"
                    ],
                    [
                        "key" => "indicativeDuration",
                        "name" => "Durée de formation (en heures)",
                        "example" => "14"
                    ],
                    [
                        "key" => "amountDiscount",
                        "name" => "Montant",
                        "example" => "Montant modifié de 15%"
                    ],
                    [
                        "key" => "trainingActions",
                        "rawKey" => "proposal.trainingActionsResume",
                        "name" => "Actions de formation",
                        "example" => "valable pour toutes actions de formation"
                    ],
                    [
                        "key" => "sales",
                        "rawKey" => "proposal.sales.email",
                        "name" => "Agent Commercial",
                        "example" => "<EMAIL>"
                    ]
                ]
            ],
            [
                "name" => "Dossier de formation",
                "key" => "registrationFolder",
                "entityClass" => RegistrationFolder::CLASSNAME,
                "availableIn" => ["document", "message"],
                "scopes" => ["registrationFolder", "certificationFolder", "proposal", "certificationFolderSurvey"],
                "values" => [
                    [
                        "key" => "attendeeLink",
                        "name" => "Espace apprenant",
                        "example" => "https://wwww.wedof.fr/apprenant-XXXXXXXXXXX"
                    ],
                    [
                        "key" => "state",
                        "enum" => "App\\Library\\utils\\enums\\RegistrationFolderStates",
                        "name" => "État du dossier (formation)",
                        "example" => "Accepté"
                    ],
                    [
                        "key" => "externalId",
                        "name" => "Numéro de dossier",
                        "example" => "*********"
                    ],
                    [
                        "key" => "permalink",
                        "name" => "Lien interne Wedof",
                        "example" => "https://www.wedof.fr/dossier-formation-XXXXXXXXXXX"
                    ],
                    [
                        "key" => "billingState",
                        "name" => "État de facturation",
                        "example" => "Non facturable"
                    ],
                    [
                        "key" => "billNumber",
                        "name" => "Numéro de facture",
                        "example" => "FAC-0123"
                    ],
                    [
                        "key" => "amountTtc",
                        "name" => "Montant TTC",
                        "example" => "990"
                    ],
                    [
                        "key" => "notes",
                        "name" => "Notes privées",
                        "example" => "Texte libre"
                    ],
                    [
                        "key" => "completionRate",
                        "name" => "Taux d'avancement",
                        "example" => "50"
                    ],
                    [
                        "key" => "terminatedReason",
                        "rawKey" => "terminatedReason.[label]",
                        "name" => "Raison de sortie de formation",
                        "example" => "Malade"
                    ],
                    [
                        "key" => "description",
                        "name" => "Description",
                        "example" => "description",
                        "options" => ["nl2br"]
                    ],
                    [
                        "key" => "amountToInvoice",
                        "name" => "Montant à facturer",
                        "example" => "1200"
                    ],
                    [
                        "key" => "sessionCity",
                        "name" => "Lieu de la formation",
                        "example" => "À distance"
                    ],
                    [
                        "key" => "completedHours",
                        "name" => "Heures attestées",
                        "example" => 12, 5
                    ],
                    [
                        "key" => "surveyLink",
                        "name" => "Lien enquête",
                        "example" => "https://wedof.fr/candidat-externalId-enquete"
                    ]
                ]
            ],
            [
                "name" => "Dossier de certification",
                "key" => "certificationFolder",
                "entityClass" => CertificationFolder::CLASSNAME,
                "availableIn" => ["document", "message"],
                "scopes" => ["registrationFolder", "certificationFolder", "certificationFolderSurvey", "proposal", "certificate"],
                "values" => [
                    [
                        "key" => "addToPassportLink",
                        "name" => "Ajouter au Passeport de Compétences",
                        "example" => "https://www.wedof.fr/candidat-XXXX-XXXXXXXXX-passeport"
                    ],
                    [
                        "key" => "attendeeLink",
                        "name" => "Espace candidat",
                        "example" => "https://www.wedof.fr/candidat-XXXX-XXXXXXXXX"
                    ],
                    [
                        "key" => "state",
                        "enum" => "App\\Library\\utils\\enums\\CertificationFolderStates",
                        "name" => "État du dossier (certification)",
                        "example" => "Enregistré"
                    ],
                    [
                        "key" => "externalId",
                        "name" => "Numéro de dossier",
                        "example" => "*********"
                    ],
                    [
                        "key" => "permalink",
                        "name" => "Lien interne Wedof",
                        "example" => "https://www.wedof.fr/dossier-certification-XXXXXX"
                    ],
                    [
                        "key" => "certification",
                        "rawKey" => "certification.name",
                        "name" => "Titre de la certification",
                        "example" => "Animer des soirées dansantes"
                    ],
                    [
                        "key" => "certifier",
                        "rawKey" => "certifierOrganism.name",
                        "name" => "Nom du certificateur",
                        "example" => "Wedof Certificateur"
                    ],
                    [
                        "key" => "codeReconnaissance",
                        "rawKey" => "certification.externalId",
                        "name" => "Code de reconnaissance",
                        "example" => "RSXXXX"
                    ],
                    [
                        "key" => "franceCompetenceUrl",
                        "rawKey" => "certification.link",
                        "name" => "URL de la certification",
                        "example" => "https://www.francecompetences.fr/recherche/rs/R5XXXX"
                    ],
                    [
                        "key" => "europeanLanguageLevel",
                        "enum" => "App\\Library\\utils\\enums\\CertificationFolderEuropeanLanguageLevel",
                        "name" => "Niveau de langue Européen",
                        "example" => "B1"
                    ],
                    [
                        "key" => "examinationDate",
                        "name" => "Date de début d'examen",
                        "example" => "23/12/2023"
                    ],
                    [
                        "key" => "examinationTime",
                        "name" => "Heure de début d'examen (h de Paris)",
                        "example" => (new DateTime())->setTimezone(new DateTimeZone('Europe/Paris'))->format('H\hi')
                    ],
                    [
                        "key" => "examinationType",
                        "enum" => "App\\Library\\utils\\enums\\CertificationExaminationType",
                        "name" => "Type d'examen",
                        "example" => "A distance"
                    ],
                    [
                        "key" => "examinationEndDate",
                        "name" => "Date de fin d'examen",
                        "example" => "30/12/2023"
                    ],
                    [
                        "key" => "examinationEndTime",
                        "name" => "Heure de fin d'examen (h de Paris)",
                        "example" => (new DateTime())->setTimezone(new DateTimeZone('Europe/Paris'))->format('H\hi')
                    ],
                    [
                        "key" => "examinationPlace",
                        "name" => "Lieu de passage de l'examen",
                        "example" => "Toulouse"
                    ],
                    [
                        "key" => "detailedResult",
                        "name" => "Détail du résultat de l'examen",
                        "example" => "80 / 100"
                    ],
                    [
                        "key" => "comment",
                        "name" => "Commentaire",
                        "example" => "Commentaire sur le dossier de certification",
                        "options" => ["nl2br"]
                    ],
                    [
                        "key" => "digitalProofLink",
                        "name" => "Lien de la preuve numérique",
                        "example" => "https://proofExample.com/check/12345"
                    ],
                    [
                        "key" => "gradePass",
                        "enum" => "App\\Library\\utils\\enums\\CertificationFolderGradePass",
                        "name" => "Mention obtenue",
                        "example" => "B"
                    ],
                    [
                        "key" => "surveyLink",
                        "name" => "Lien enquête",
                        "example" => "https://wedof.fr/candidat-externalId-enquete"
                    ],
                    [
                        "key" => "skills",
                        "rawKey" => "certificationFolder.getSkillSetsName",
                        "name" => "Blocs de compétences",
                        "example" => "BC01 Analyser les enjeux, BC02 Identifier les impacts"
                    ]
                ]
            ],
            [
                "name" => "Enquête de suivi d'insertion professionnelle",
                "key" => "certificationFolderSurvey",
                "entityClass" => CertificationFolderSurvey::CLASSNAME,
                "availableIn" => ["message"],
                "scopes" => ["registrationFolder", "certificationFolder", "certificationFolderSurvey", "proposal"],
                "values" => [
                    [
                        "key" => "state",
                        "enum" => "App\\Library\\utils\\enums\\CertificationFolderSurveyStates",
                        "name" => "État de l'enquête",
                        "example" => "Commencée"
                    ],
                    [
                        "key" => "initialExperienceAnsweredDate",
                        "name" => "Date de réponse à l'enquête en début de cursus",
                        "example" => "18/01/2024"
                    ],
                    [
                        "key" => "sixMonthExperienceAnsweredDate",
                        "name" => "Date de réponse à l'enquête 6 mois après la certification",
                        "example" => "18/07/2024"
                    ],
                    [
                        "key" => "longTermExperienceAnsweredDate",
                        "name" => "Date de réponse à l'enquête un an après la certification",
                        "example" => "18/01/2025"
                    ],
                    [
                        "key" => "sixMonthExperienceStartDate",
                        "name" => "Date de début de réponse à l'enquête 6 mois après la certification",
                        "example" => "18/07/2024"
                    ],
                    [
                        "key" => "longTermExperienceStartDate",
                        "name" => "Date de début de réponse à l'enquête un an après la certification",
                        "example" => "18/01/2025"
                    ]
                ]
            ],
            [
                "name" => "Partenariat de certification",
                "key" => "certificationPartner",
                "entityClass" => CertificationPartner::CLASSNAME,
                "availableIn" => ["document", "message"],
                "scopes" => ["certificationPartner", "certificationFolder", "certificationFolderSurvey", "proposal", "registrationFolder", "certificate", 'audit'],
                "values" => [
                    [
                        "key" => "certification",
                        "rawKey" => "certification.name",
                        "name" => "Titre de la certification",
                        "example" => "Animer des soirées dansantes"
                    ],
                    [
                        "key" => "codeReconnaissance",
                        "rawKey" => "certification.externalId",
                        "name" => "Code de reconnaissance",
                        "example" => "RSXXXX"
                    ],
                    [
                        "key" => "certifier",
                        "rawKey" => "certifierOrganism.name",
                        "name" => "Nom du certificateur",
                        "example" => "Wedof Certificateur"
                    ],
                    [
                        "key" => "certifierOwner",
                        "rawKey" => "certifierOwner.name",
                        "name" => "Nom du propriétaire du compte de l'organisme certificateur",
                        "example" => "John Doe"
                    ],
                    [
                        "key" => "certifierSiret",
                        "rawKey" => "certifierOrganism.siret",
                        "name" => "Siret du certificateur",
                        "example" => "33299992400012"
                    ],
                    [
                        "key" => "certifierCity",
                        "rawKey" => "certifierOrganism.city",
                        "name" => "Ville du certificateur",
                        "example" => "Toulouse"
                    ],
                    [
                        "key" => "certifierPostalCode",
                        "rawKey" => "certifierOrganism.postalCode",
                        "name" => "Code postal du certificateur",
                        "example" => "31500"
                    ],
                    [
                        "key" => "certifierAddress",
                        "rawKey" => "certifierOrganism.address",
                        "name" => "Adresse du certificateur",
                        "example" => "8 impasse Bonnet"
                    ],
                    [
                        "key" => "certifierFullAddress",
                        "rawKey" => "certifierOrganism.fullAddress",
                        "name" => "Adresse complète du certificateur",
                        "example" => "8 impasse Bonnet 31500 Toulouse"
                    ],
                    [
                        "key" => "partner",
                        "rawKey" => "trainingOrganism.name",
                        "name" => "Nom du partenaire",
                        "example" => "Wedof Formation"
                    ],
                    [
                        "key" => "partnerOwner",
                        "rawKey" => "partnerOwner.name",
                        "name" => "Nom du propriétaire du compte de l'organisme de formation",
                        "example" => "Jane Doe"
                    ],
                    [
                        "key" => "partnerSiret",
                        "rawKey" => "trainingOrganism.siret",
                        "name" => "Siret du partenaire",
                        "example" => "33299992400012"
                    ],
                    [
                        "key" => "partnerCity",
                        "rawKey" => "trainingOrganism.city",
                        "name" => "Ville du partenaire",
                        "example" => "Toulouse"
                    ],
                    [
                        "key" => "partnerPostalCode",
                        "rawKey" => "trainingOrganism.postalCode",
                        "name" => "Code postal du partenaire",
                        "example" => "31500"
                    ],
                    [
                        "key" => "partnerAddress",
                        "rawKey" => "trainingOrganism.address",
                        "name" => "Adresse du partenaire",
                        "example" => "8 impasse Bonnet"
                    ],
                    [
                        "key" => "partnerFullAddress",
                        "rawKey" => "trainingOrganism.fullAddress",
                        "name" => "Adresse complète du partenaire",
                        "example" => "8 impasse Bonnet 31500 Toulouse"
                    ],
                    [
                        "key" => "email",
                        "rawKey" => "trainingOrganism.emails.[0]",
                        "name" => "Mail du partenaire",
                        "example" => "<EMAIL>"
                    ],
                    [
                        "key" => "state",
                        "enum" => "App\\Library\\utils\\enums\\CertificationPartnerStates",
                        "name" => "État du partenariat",
                        "example" => "Active"
                    ],
                    [
                        "key" => "habilitation",
                        "enum" => "App\\Library\\utils\\enums\\CertificationPartnerHabilitation",
                        "name" => "Habilitation du partenaire",
                        "example" => "Habilitation pour former"
                    ],
                    [
                        "key" => "partnerLink",
                        "name" => "Lien Wedof Partenaire",
                        "example" => "www.wedof.fr"
                    ],
                    [
                        "key" => "certifierLink",
                        "name" => "Lien Wedof Certificateur",
                        "example" => "www.wedof.fr"
                    ],
                    [
                        "key" => "comment",
                        "name" => "Commentaire",
                        "example" => "Texte libre "
                    ],
                    [
                        "key" => "amountHt",
                        "name" => "Prix HT par certification",
                        "example" => "100"
                    ]
                ]
            ],
            [
                "name" => "Mon Organisme",
                "key" => "myOrganism",
                "entityClass" => Organism::CLASSNAME,
                "availableIn" => ["document", "message"],
                "scopes" => ["registrationFolder", "certificationFolder", "certificate", "certificationPartner", "proposal", "audit", "certificationFolderSurvey"],
                "values" => [
                    [
                        "key" => "name",
                        "name" => "Nom de mon organisme",
                        "example" => "Wedof Organisme"
                    ],
                    [
                        "key" => "siren",
                        "name" => "Siren",
                        "example" => "332999924"
                    ],
                    [
                        "key" => "siret",
                        "name" => "Siret",
                        "example" => "33299992400012"
                    ],
                    [
                        "key" => "email",
                        "rawKey" => "myOrganism.emails.[0]",
                        "name" => "Email",
                        "example" => "<EMAIL>"
                    ],
                    [
                        "key" => "city",
                        "name" => "Ville",
                        "example" => "Toulouse"
                    ],
                    [
                        "key" => "postalCode",
                        "name" => "Code postal",
                        "example" => "31500"
                    ],
                    [
                        "key" => "address",
                        "name" => "Adresse",
                        "example" => "8 impasse Bonnet"
                    ],
                    [
                        "key" => "fullAddress",
                        "name" => "Adresse complète",
                        "example" => "8 impasse Bonnet 31500 Toulouse"
                    ]
                ]
            ],
            [
                "name" => "Organisme certificateur",
                "key" => "certifierOrganism",
                "entityClass" => Organism::CLASSNAME,
                "availableIn" => ["document", "message"],
                "scopes" => ["registrationFolder", "certificationFolder", "certificate", "certificationPartner", "proposal", "audit", "certificationFolderSurvey"],
                "values" => [
                    [
                        "key" => "name",
                        "name" => "Nom de l'organisme",
                        "example" => "Wedof Formation"
                    ],
                    [
                        "key" => "siren",
                        "name" => "Siren",
                        "example" => "332999924"
                    ],
                    [
                        "key" => "siret",
                        "name" => "Siret",
                        "example" => "33299992400012"
                    ],
                    [
                        "key" => "email",
                        "rawKey" => "certifierOrganism.emails.[0]",
                        "name" => "Email",
                        "example" => "<EMAIL>"
                    ],
                    [
                        "key" => "city",
                        "name" => "Ville",
                        "example" => "Toulouse"
                    ],
                    [
                        "key" => "postalCode",
                        "name" => "Code postal",
                        "example" => "31500"
                    ],
                    [
                        "key" => "address",
                        "name" => "Adresse",
                        "example" => "8 impasse Bonnet"
                    ],
                    [
                        "key" => "fullAddress",
                        "name" => "Adresse complète",
                        "example" => "8 impasse Bonnet 31500 Toulouse"
                    ]
                ]
            ],
            [
                "name" => "Organisme de formation",
                "key" => "trainingOrganism",
                "entityClass" => Organism::CLASSNAME,
                "availableIn" => ["document", "message"],
                "scopes" => ["registrationFolder", "certificationFolder", "certificate", "certificationPartner", "proposal", "audit", "certificationFolderSurvey"],
                "values" => [
                    [
                        "key" => "name",
                        "name" => "Nom de l'organisme",
                        "example" => "Wedof Formation"
                    ],
                    [
                        "key" => "siren",
                        "name" => "Siren",
                        "example" => "332999924"
                    ],
                    [
                        "key" => "siret",
                        "name" => "Siret",
                        "example" => "33299992400012"
                    ],
                    [
                        "key" => "email",
                        "rawKey" => "trainingOrganism.emails.[0]",
                        "name" => "Email",
                        "example" => "<EMAIL>"
                    ],
                    [
                        "key" => "city",
                        "name" => "Ville",
                        "example" => "Toulouse"
                    ],
                    [
                        "key" => "postalCode",
                        "name" => "Code postal",
                        "example" => "31500"
                    ],
                    [
                        "key" => "address",
                        "name" => "Adresse",
                        "example" => "8 impasse Bonnet"
                    ],
                    [
                        "key" => "fullAddress",
                        "name" => "Adresse complète",
                        "example" => "8 impasse Bonnet 31500 Toulouse"
                    ]
                ]
            ],
            [
                "name" => "Facture",
                "key" => "invoice",
                "availableIn" => ["document", "message"],
                "scopes" => ["certificationPartner"],
                "values" => [
                    [
                        "key" => "externalId",
                        "name" => "Numéro de facture",
                        "example" => "INV-EXT-1A2"
                    ],
                    [
                        "key" => "state",
                        "enum" => "App\\Library\\utils\\enums\\InvoiceStates",
                        "name" => "Etat de la facture",
                        "example" => "Payée"
                    ],
                    [
                        "key" => "type",
                        "enum" => "App\\Library\\utils\\enums\\InvoiceTypes",
                        "name" => "Type de facture",
                        "example" => "Facture"
                    ],
                    [
                        "key" => "paymentLink",
                        "name" => "Lien de paiement",
                        "example" => "https://www.stripe.com/invoices/INV-EXT-1A2"
                    ],
                    [
                        "key" => "description",
                        "name" => "Description",
                        "example" => "Facture Juin 2024 partenariat de certification"
                    ],
                    [
                        "key" => "dueDate",
                        "name" => "Date d'échéance de paiment",
                        "example" => "31 Juillet 2024"
                    ]
                ]
            ],
            [
                "name" => "Audit",
                "key" => "audit",
                "entityClass" => CertificationPartnerAudit::CLASSNAME,
                "availableIn" => ["message"],
                "scopes" => ["certificationPartner"],
                "values" => [
                    [
                        "key" => "state",
                        "enum" => "App\\Library\\utils\\enums\\CertificationPartnerAuditStates",
                        "name" => "État de l'audit",
                        "example" => "En cours"
                    ],
                    [
                        "key" => "startDate",
                        "name" => "Date de l'audit",
                        "example" => "12/10/2024"
                    ],
                    [
                        "key" => "endDate",
                        "name" => "Date de clôture de l'audit",
                        "example" => "13/10/2024"
                    ],
                    [
                        "key" => "result",
                        "enum" => "App\\Library\\utils\\enums\\CertificationPartnerAuditResults",
                        "name" => "Résultat de l'audit",
                        "example" => "Conforme"
                    ],
                    [
                        "key" => "notes",
                        "name" => "Notes de l'audit",
                        "example" => ""
                    ],
                    [
                        "key" => "certifierLink",
                        "name" => "Lien vers l'audit (certificateur)",
                        "example" => "https://www.wedof.fr"
                    ],
                    [
                        "key" => "partnerLink",
                        "name" => "Lien vers l'audit (partenaire)",
                        "example" => "https://www.wedof.fr"
                    ]
                ]
            ],
            [
                "name" => "Document",
                "key" => "document",
                "availableIn" => ["document", "message"],
                "scopes" => ["registrationFolder", "certificationFolder", "certificationPartner", "proposal"],
                "values" => [
                    [
                        "key" => "name",
                        "rawKey" => "document.fileName",
                        "name" => "Nom du fichier",
                        "example" => "grille-évaluation.pdf"
                    ],
                    [
                        "key" => "state",
                        "enum" => "App\\Library\\utils\\enums\\FileStates",
                        "name" => "Etat du document",
                        "example" => "Validé"
                    ],
                    [
                        "key" => "comment",
                        "name" => "Commentaire sur le document",
                        "example" => "complète et bien remplie"
                    ],
                    [
                        "key" => "type",
                        "rawKey" => "document.retrieveFileType.[name]",
                        "name" => "Type de document attendu",
                        "example" => "La Grille d'évaluation",
                    ],
                    [
                        "key" => "description",
                        "rawKey" => "document.retrieveFileType.[description]",
                        "name" => "Description du document attendu",
                        "example" => "Le document attendu est une grille d'évaluation du candidat",
                    ],
                    [
                        "key" => "permalink",
                        "name" => "Lien vers le document (pour le candidat / apprenant / partenaire )",
                        "example" => "https://www.wedof.fr/candidat-XXXX-XXXXXXXXX-document-XXXXXX"
                    ]
                ]
            ]
        ];
    }

    /**
     * @param $entity
     * @param $document
     * @param $invoice
     * @param $audit
     * @param ContainerInterface $container
     * @param bool $useExampleValues
     * @param string $type
     * @param Organism|null $mainOrganismFromEntity
     * @return array
     * @throws ContainerExceptionInterface
     * @throws NonUniqueResultException
     * @throws NotFoundExceptionInterface
     * @throws NotSupported
     * @throws ORMException
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws \Doctrine\DBAL\Exception
     * @throws \Doctrine\ORM\ORMException
     */
    public static function getComputedContext($entity, $document, $invoice, $audit, ContainerInterface $container, bool $useExampleValues, string $type, Organism $mainOrganismFromEntity = null): array
    {
        /** @var RegistrationFolder|CertificationFolder|Proposal|CertificationPartner|CertificationFolderSurvey $entity */
        if ($entity instanceof Proposal) {
            $registrationFolder = $entity->getRegistrationFolder();
            $certificationPartnerService = $container->get(CertificationPartnerService::class);
            $certificationPartner = ($registrationFolder && $registrationFolder->getCertification()) ? $certificationPartnerService->getByCertificationAndPartner($registrationFolder->getCertification(), $registrationFolder->getOrganism()) : null;
            $certifier = $certificationPartner ? $certificationPartner->getCertifier() : null;
            $trainingOrganism = $entity->getOrganism();
            $attendee = $registrationFolder ? $registrationFolder->getAttendee() : self::simulateAttendee($entity);
            $unsafeContext = [
                'document' => $document,
                'attendee' => $attendee,
                'trainingOrganism' => $trainingOrganism,
                'certifierOrganism' => $certifier ?? null,
                'myOrganism' => $mainOrganismFromEntity,
                'certification' => $registrationFolder ? $registrationFolder->getCertification() : null,
                'registrationFolder' => $registrationFolder,
                'certificationFolder' => $registrationFolder ? $registrationFolder->getCertificationFolder() : null,
                'proposal' => $entity,
                'trainingActions' => $entity->getTrainingActions(),
                'certificationPartner' => $certificationPartner ?? null,
                'certificationFolderSurvey' => $registrationFolder && $registrationFolder->getCertificationFolder() ? $registrationFolder->getCertificationFolder()->getSurvey() : null,
                'invoice' => null,
                'audit' => null
            ];
            if ($type === 'document') {
                $unsafeContext['signature'] = [
                    'owner' => "{{Signature;role=" . $mainOrganismFromEntity->getName() . ";type=signature}}",
                    'ownerInitials' => "{{Paraphe;role=" . $mainOrganismFromEntity->getName() . ";type=initials}}",
                    'ownerStamp' => "{{Tampon;role=" . $mainOrganismFromEntity->getName() . ";type=image}}",
                    'ownerDate' => "{{Date;role=" . $mainOrganismFromEntity->getName() . ";type=datenow;format=DD/MM/YYYY}}",
                    'attendee' => "{{Signature;role=" . $attendee->getDisplayName() . ";type=signature}}",
                    'attendeeInitials' => "{{Paraphe;role=" . $attendee->getDisplayName() . ";type=initials}}",
                    'attendeeDate' => "{{Date;role=" . $attendee->getDisplayName() . ";type=datenow;format=DD/MM/YYYY}}"
                ];
            }
        } else if ($entity instanceof RegistrationFolder) {
            $certificationPartnerService = $container->get(CertificationPartnerService::class);
            $certificationPartner = $entity->getCertification() ? $certificationPartnerService->getByCertificationAndPartner($entity->getCertification(), $entity->getOrganism()) : null;
            $certificationFolder = $entity->getCertificationFolder();
            $certifier = $certificationPartner ? $certificationPartner->getCertifier() : null;
            $trainingOrganism = $entity->getOrganism();
            $unsafeContext = [
                'document' => $document,
                'attendee' => $entity->getAttendee(),
                'trainingOrganism' => $trainingOrganism,
                'certifierOrganism' => $certifier ?? null,
                'myOrganism' => $mainOrganismFromEntity,
                'certification' => $entity->getCertification() ?? null,
                'registrationFolder' => $entity,
                'certificationFolder' => $certificationFolder,
                'certifierOwner' => $certifier ? $certifier->getOwnedBy() : null,
                'partnerOwner' => $trainingOrganism->getOwnedBy(),
                'proposal' => $entity->getProposal(),
                'trainingActions' => null,
                'certificationPartner' => $certificationPartner ?? null,
                'certificationFolderSurvey' => $certificationFolder ? $certificationFolder->getSurvey() : null,
                'invoice' => null,
                'audit' => null
            ];
            if ($type === 'document') {
                $unsafeContext['signature'] = [
                    'owner' => "{{Signature;role=" . $mainOrganismFromEntity->getName() . ";type=signature}}",
                    'ownerInitials' => "{{Paraphe;role=" . $mainOrganismFromEntity->getName() . ";type=initials}}",
                    'ownerStamp' => "{{Tampon;role=" . $mainOrganismFromEntity->getName() . ";type=image}}",
                    'ownerDate' => "{{Date;role=" . $mainOrganismFromEntity->getName() . ";type=datenow;format=DD/MM/YYYY}}",
                    'attendee' => "{{Signature;role=" . $entity->getAttendee()->getDisplayName() . ";type=signature}}",
                    'attendeeInitials' => "{{Paraphe;role=" . $entity->getAttendee()->getDisplayName() . ";type=initials}}",
                    'attendeeDate' => "{{Date;role=" . $entity->getAttendee()->getDisplayName() . ";type=datenow;format=DD/MM/YYYY}}"
                ];
            }
        } else if ($entity instanceof CertificationFolder) {
            $certificationPartnerService = $container->get(CertificationPartnerService::class);
            $certificationPartner = $certificationPartnerService->getByCertificationAndPartner($entity->getCertification(), $entity->getPartner());
            $registrationFolder = $entity->getRegistrationFolder();
            $trainingOrganism = $entity->getPartner();
            $certifierOrganism = $entity->getCertifier();
            $unsafeContext = [
                'document' => $document,
                'attendee' => $entity->getAttendee(),
                'trainingOrganism' => $trainingOrganism,
                'certifierOrganism' => $certifierOrganism,
                'myOrganism' => $mainOrganismFromEntity,
                'certification' => $entity->getCertification(),
                'registrationFolder' => $registrationFolder,
                'certificationFolder' => $entity,
                'certifierOwner' => $certifierOrganism->getOwnedBy() ?? null,
                'partnerOwner' => $trainingOrganism ? $trainingOrganism->getOwnedBy() : null,
                'proposal' => $registrationFolder ? $registrationFolder->getProposal() : null,
                'trainingActions' => null,
                'certificationPartner' => $certificationPartner ?? null,
                'certificationFolderSurvey' => $entity->getSurvey(),
                'invoice' => null,
                'audit' => null
            ];
            if ($type === 'document') {
                $unsafeContext['signature'] = [
                    'owner' => "{{Signature;role=" . $mainOrganismFromEntity->getName() . ";type=signature}}",
                    'ownerInitials' => "{{Paraphe;role=" . $mainOrganismFromEntity->getName() . ";type=initials}}",
                    'ownerStamp' => "{{Tampon;role=" . $mainOrganismFromEntity->getName() . ";type=image}}",
                    'ownerDate' => "{{Date;role=" . $mainOrganismFromEntity->getName() . ";type=datenow;format=DD/MM/YYYY}}",
                    'attendee' => "{{Signature;role=" . $entity->getAttendee()->getDisplayName() . ";type=signature}}",
                    'attendeeInitials' => "{{Paraphe;role=" . $entity->getAttendee()->getDisplayName() . ";type=initials}}",
                    'attendeeDate' => "{{Date;role=" . $entity->getAttendee()->getDisplayName() . ";type=datenow;format=DD/MM/YYYY}}"
                ];
                if ($entity->getPartner()) {
                    $unsafeContext['signature']['partner'] = "{{Signature;role=" . $entity->getPartner()->getName() . ";type=signature}}";
                    $unsafeContext['signature']['partnerInitials'] = "{{Paraphe;role=" . $entity->getPartner()->getName() . ";type=initials}}";
                }
            }
        } else if ($entity instanceof CertificationPartner) {
            $trainingOrganism = $entity->getPartner();
            $certifierOrganism = $entity->getCertifier();
            $unsafeContext = [
                'document' => $document,
                'attendee' => null,
                'trainingOrganism' => $trainingOrganism,
                'certifierOrganism' => $certifierOrganism,
                'myOrganism' => $mainOrganismFromEntity,
                'certification' => $entity->getCertification(),
                'registrationFolder' => null,
                'certificationFolder' => null,
                'certifierOwner' => $certifierOrganism->getOwnedBy(),
                'partnerOwner' => $trainingOrganism->getOwnedBy(),
                'proposal' => null,
                'trainingActions' => null,
                'certificationPartner' => $entity,
                'certificationFolderSurvey' => null,
                'invoice' => $invoice,
                'audit' => $audit
            ];
            if ($type === 'document') {
                $unsafeContext['signature'] = [
                    'owner' => "{{Signature;role=" . $mainOrganismFromEntity->getName() . ";type=signature}}",
                    'ownerInitials' => "{{Paraphe;role=" . $mainOrganismFromEntity->getName() . ";type=initials}}",
                    'ownerStamp' => "{{Tampon;role=" . $mainOrganismFromEntity->getName() . ";type=image}}",
                    'ownerDate' => "{{Date;role=" . $mainOrganismFromEntity->getName() . ";type=datenow;format=DD/MM/YYYY}}",
                    'partner' => "{{Signature;role=" . $trainingOrganism->getName() . ";type=signature}}",
                    'partnerInitials' => "{{Paraphe;role=" . $trainingOrganism->getName() . ";type=initials}}",
                    'partnerDate' => "{{Date;role=" . $trainingOrganism->getName() . ";type=datenow;format=DD/MM/YYYY}}"
                ];
            }
        } else if ($entity instanceof CertificationFolderSurvey) {
            $certificationFolder = $entity->getCertificationFolder();
            $certificationPartnerService = $container->get(CertificationPartnerService::class);
            $certificationPartner = $certificationPartnerService->getByCertificationAndPartner($certificationFolder->getCertification(), $certificationFolder->getPartner());
            $registrationFolder = $certificationFolder->getRegistrationFolder();
            $trainingOrganism = $certificationFolder->getPartner();
            $certifierOrganism = $certificationFolder->getCertifier();
            $unsafeContext = [
                'document' => null,
                'attendee' => $certificationFolder->getAttendee(),
                'trainingOrganism' => $trainingOrganism,
                'certifierOrganism' => $certifierOrganism,
                'myOrganism' => $mainOrganismFromEntity,
                'certification' => $certificationFolder->getCertification(),
                'registrationFolder' => $registrationFolder ?? null,
                'certificationFolder' => $certificationFolder,
                'certifierOwner' => $certifierOrganism->getOwnedBy() ?? null,
                'partnerOwner' => $trainingOrganism ? $trainingOrganism->getOwnedBy() : null,
                'proposal' => $registrationFolder && $registrationFolder->getProposal() ? $registrationFolder->getProposal() : null,
                'trainingActions' => null,
                'certificationPartner' => $certificationPartner ?? null,
                'certificationFolderSurvey' => $entity,
                'invoice' => null,
                'audit' => null
            ];
        } else {
            $unsafeContext = [
                'document' => null,
                'attendee' => null,
                'trainingOrganism' => null,
                'certifierOrganism' => null,
                'myOrganism' => $mainOrganismFromEntity,
                'certification' => null,
                'registrationFolder' => null,
                'certificationFolder' => null,
                'proposal' => null,
                'trainingActions' => null,
                'certificationPartner' => null,
                'certificationFolderSurvey' => null,
                'invoice' => null,
                'audit' => null
            ];
        }
        $unsafeContext['common'] = [
            'date' => (new DateTime())->setTimezone(new DateTimeZone('Europe/Paris'))->format("d/m/Y"),
            'datetime' => (new DateTime())->setTimezone(new DateTimeZone('Europe/Paris'))->format("d/m/Y à H\hi")
        ];
        return self::getSafeContextFromUnsafeContext($unsafeContext, $useExampleValues, $type, $container, $mainOrganismFromEntity);
    }

    /**
     * @param string $type
     * @return array
     * @throws ContainerExceptionInterface
     * @throws NonUniqueResultException
     * @throws NotFoundExceptionInterface
     * @throws NotSupported
     * @throws ORMException
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws \Doctrine\DBAL\Exception
     * @throws \Doctrine\ORM\ORMException
     */
    public static function getExampleContextFromDictionary(string $type): array
    {
        $emptyContext = [
            'document' => null,
            'attendee' => null,
            'trainingOrganism' => null,
            'certifierOrganism' => null,
            'myOrganism' => null,
            'certification' => null,
            'registrationFolder' => null,
            'certificationFolder' => null,
            'proposal' => null,
            'trainingActions' => null,
            'certificationFolderSurvey' => null,
            'invoice' => null,
            'audit' => null
        ];
        return self::getSafeContextFromUnsafeContext($emptyContext, true, $type);
    }

    /**
     * @param string $content
     * @param array $context
     * @return string
     */
    public static function parseTemplateVariables(string $content, array $context): string
    {
        return preg_replace_callback('/\{\{(.*?)}}/', function ($matches) use ($context) {
            $variable = trim($matches[1]);
            $value = self::getVariableValue($variable, $context);
            return VariableParser::parseVariable($variable, $value);
        }, $content);
    }

    //-------------------
    // METHODES PRIVEES
    //-------------------

    /**
     * @param array $context
     * @param bool $useExampleValues
     * @param string $type
     * @param ContainerInterface|null $container
     * @param null $mainOrganismFromEntity
     * @return array
     * @throws ContainerExceptionInterface
     * @throws NonUniqueResultException
     * @throws NotFoundExceptionInterface
     * @throws NotSupported
     * @throws ORMException
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws \Doctrine\DBAL\Exception
     * @throws \Doctrine\ORM\ORMException
     */
    private static function getSafeContextFromUnsafeContext(array $context, bool $useExampleValues, string $type, ContainerInterface $container = null, $mainOrganismFromEntity = null): array
    {
        $safeContext = [];
        $variables = self::getDictionaryFor($type, "", ['organism' => $mainOrganismFromEntity], $container);
        $functionsIndex = array_search('Fonctions', array_column($variables, 'name'));
        unset($variables[$functionsIndex]);
        foreach ($variables as $object) {
            $safeContext[$object['key']] = [];
            foreach ($object['values'] as $property) {
                try {
                    if (str_contains($property['key'], 'metadata.') && !empty($context[$object['key']]) && empty($safeContext[$object['key']][$property['key']])) {
                        if (!isset($safeContext[$object['key']]['metadata'])) {
                            $safeContext[$object['key']]['metadata'] = [];
                        }
                        $metadata = $context[$object['key']]->getMetadata();
                        $metadataKey = explode('.', $property['key'])[1];
                        $safeContext[$object['key']]['metadata'][$metadataKey] = $metadata[$metadataKey] ?? null;
                    } else
                        if (isset($property['enum']) && isset($context[$object['key']])) {
                            $method = "get" . ucfirst($property['key']);
                            $enumValue = $context[$object['key']]->$method();
                            $safeContext[$object['key']][$property['key']] = $enumValue ? $property['enum']::toFrString($enumValue) : '';
                        } else if (isset($property['rawKey'])) {
                            $value = null;
                            $rawKeys = explode('.', $property['rawKey']);
                            $rawKeyContext = array_shift($rawKeys);
                            foreach ($rawKeys as $key) {
                                if (Tools::startsWith($key, '[')) {
                                    $key = str_replace('[', '', $key);
                                    $key = str_replace(']', '', $key);
                                    $value = $value[$key] ?? ($useExampleValues ? $property['example'] : '');
                                } else {
                                    if (isset($context[$rawKeyContext])) {
                                        if (method_exists($context[$rawKeyContext], $key)) {
                                            $value = $context[$rawKeyContext]->$key();
                                        } else if (method_exists($context[$rawKeyContext], 'get' . ucfirst($key))) {
                                            $method = 'get' . ucfirst($key);
                                            $value = $context[$rawKeyContext]->$method();
                                        }
                                    }
                                    if ($value == null) {
                                        $value = $useExampleValues ? $property['example'] : '';
                                        break;
                                    }
                                }
                            }
                            if ($value == null) {
                                $value = $useExampleValues ? $property['example'] : '';
                            }
                            $safeContext[$object['key']][$property['key']] = self::formatValueWithOptions($value, $property);
                        } else if (isset($context[$object['key']])) {
                            if (is_array($context[$object['key']])) {
                                $propertyValue = $context[$object['key']][$property['key']] ?? null;
                            } else {
                                $method = 'get' . ucfirst($property['key']);
                                $propertyValue = $context[$object['key']]->$method();
                            }
                            $safeContext[$object['key']][$property['key']] = self::formatValueWithOptions($propertyValue, $property);
                        } else {
                            $safeContext[$object['key']][$property['key']] = self::formatValueWithOptions($useExampleValues ? $property['example'] : '', $property);
                        }
                    if (isset($safeContext[$object['key']][$property['key']]) && $safeContext[$object['key']][$property['key']] instanceof DateTime) {
                        $safeContext[$object['key']][$property['key']] = $safeContext[$object['key']][$property['key']]->format('d/m/Y');
                    }
                } catch
                (Exception $e) {
                    $safeContext[$object['key']][$property['key']] = self::formatValueWithOptions($useExampleValues ? $property['example'] : '', $property); // Put nothing to avoid breaking twig
                }
            }
        }
        if (!empty($context['signature'])) {
            $safeContext['signature'] = $context['signature'];
        }
        return $safeContext;
    }

    /**
     * @param $value
     * @param $property
     * @return mixed|string
     */
    private static function formatValueWithOptions($value, $property)
    {
        if (isset($property['options'])) {
            if (in_array('nl2br', $property['options'])) {
                return nl2br($value);
            }
        }
        return $value;
    }

    /**
     * @param Proposal $proposal
     * @return Attendee
     */
    private static function simulateAttendee(Proposal $proposal): Attendee
    {
        $attendee = new Attendee();
        $attendee->setFirstName($proposal->getFirstName() ?? "");
        $attendee->setLastName($proposal->getLastName() ?? "");
        $attendee->setPhoneNumber($proposal->getPhoneNumber() ?? "");
        $attendee->setEmail($proposal->getEmail());
        return $attendee;
    }

    /**
     * @param array $criteria
     * @return array
     */
    private static function getAuditVariables(array $criteria): array
    {
        return [
            [
                "key" => $criteria['code'] . "_title",
                "name" => "Titre",
                "example" => ""
            ], [
                "key" => $criteria['code'] . "_complianceIcon",
                "name" => "Icône de conformité",
                "example" => ""
            ], [
                "key" => $criteria['code'] . "_compliance",
                "name" => "Conformité",
                "example" => ""
            ], [
                "key" => $criteria['code'] . "_requirement",
                "name" => "Exigence",
                "example" => ""
            ], [
                "key" => $criteria['code'] . "_value",
                "name" => "Valeur enregistrée",
                "example" => ""
            ], [
                "key" => $criteria['code'] . "_advice",
                "name" => "Conseil de mise en conformité",
                "example" => ""
            ]
        ];
    }

    /**
     * @param string $variable
     * @param array $context
     * @return string
     */
    private static function getVariableValue(string $variable, array $context): string
    {
        // Extraire le nom de la variable sans les fonctions
        $variablePath = explode(VariableParser::FUNCTION_SEPARATOR, $variable)[0];
        $parts = explode('.', trim($variablePath));
        $value = $context;
        foreach ($parts as $part) {
            $part = trim($part);
            if (isset($value[$part])) {
                $value = $value[$part];
            } else {
                return '';
            }
        }
        return (string)$value;
    }

    /**
     * @param array $dictionary
     * @param Organism $organism
     * @param string $keyWithMetadata
     * @param ContainerInterface $container
     * @return array
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws NotSupported
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws \Doctrine\DBAL\Exception
     */
    private static function addMetadataDictionary(array $dictionary, Organism $organism, string $keyWithMetadata, ContainerInterface $container): array
    {
        $indexOfDictionaryEntry = array_search($keyWithMetadata, array_column($dictionary, 'key'));
        if (!empty($indexOfDictionaryEntry)) {
            $dictionaryEntry = $dictionary[$indexOfDictionaryEntry];
            $entityClass = $dictionaryEntry['entityClass'] ?? null;
            if (empty($entityClass)) {
                throw new WedofBadRequestHttpException('Error, ' . $keyWithMetadata . ' must have an entityClass');
            }
            /* @var $em EntityManager */
            $em = $container->get('doctrine')->getManager();
            /** @var OrganismRepository|ProposalRepository|CertificationFolderRepository|RegistrationFolderRepository|CertificationPartnerRepository $repository */
            $repository = $em->getRepository('App\Entity\\' . $entityClass);
            $metadata = $repository->extractMetadataKeys($organism);
            if (!empty($metadata)) {
                $dictionary[$indexOfDictionaryEntry]['values'] = array_merge($dictionaryEntry['values'] ?? [],
                    array_map(
                        fn($value) => [
                            "key" => "metadata." . $value,
                            "name" => $value,
                            "example" => ""
                        ],
                        $metadata,
                        array_keys($metadata)
                    )
                );
            }
        }
        return $dictionary;
    }
}
