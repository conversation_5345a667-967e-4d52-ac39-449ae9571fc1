<?php

namespace App\Library\utils;

use DOMDocument;
use Exception;
use SimpleXMLElement;

class SimpleXMLElementTools
{
    /**
     * Adds a CDATA property to an XML document.
     *
     * @param string $name
     *   Name of property that should contain CDATA.
     * @param string $value
     *   Value that should be inserted into a CDATA child.
     * @param SimpleXMLElement $parent
     *   Element that the CDATA child should be attached too.
     * @return mixed
     */
    public static function addCData(string $name, string $value, SimpleXMLElement $parent): SimpleXMLElement
    {
        $child = $parent->addChild($name);

        if ($child !== NULL) {
            $child_node = dom_import_simplexml($child);
            $child_owner = $child_node->ownerDocument;
            $child_node->appendChild($child_owner->createCDATASection($value));
        }

        return $child;
    }

    /**
     * @param string $xmlString
     * @param string $path
     * @throws Exception
     */
    public static function validateCdcXML(string $xmlString, string $path): void
    {
        $xsdFilePath = __DIR__ . $path;
        $domDocument = new DOMDocument();
        $domDocument->loadXML($xmlString);
        if (!$domDocument->schemaValidate($xsdFilePath)) {
            $errors = libxml_get_errors();
            $errorString = '';
            foreach ($errors as $error) {
                $errorString .= print_r($error, true);
            }
            libxml_clear_errors();
            throw new Exception($errorString);
        }
    }
}