<?php

namespace App\Library\utils;

use App\Entity\Organism;
use App\Entity\Subscription;
use App\Entity\Tag;
use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofConnectionException;
use App\Library\utils\enums\PeriodTypes;
use Beelab\TagBundle\Entity\AbstractTaggable;
use Closure;
use DateInterval;
use DatePeriod;
use DateTime;
use DateTimeInterface;
use DateTimeZone;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use DOMDocument;
use DOMXPath;
use ErrorException;
use Exception;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use RecursiveArrayIterator;
use RecursiveIteratorIterator;
use SimpleXMLElement;
use Symfony\Component\HttpClient\CurlHttpClient;
use Symfony\Component\HttpClient\HttpClient;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class Tools
{
    const MIMETYPE_CONVERSION = [
        "link" => ["application/link-format"],
        ".pdf" => ["application/pdf"],
        ".doc" => ["application/msword"],
        ".docx" => ["application/vnd.openxmlformats-officedocument.wordprocessingml.document"],
        ".xls" => ["application/vnd.ms-excel"],
        ".xlsx" => ["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"],
        ".ppt" => ["application/vnd.ms-powerpoint"],
        ".pptx" => ["application/vnd.openxmlformats-officedocument.presentationml.presentation"],
        ".txt" => ["text/plain"],
        ".png" => ["image/png"],
        ".jpg" => ["image/jpeg"],
        ".zip" => ["application/zip", "application/x-zip-compressed"],
        ".rar" => ["application/vnd.rar"],
        ".7z" => ["application/x-7z-compressed"],
        ".ace" => ["application/x-ace-compressed"],
        ".tar.gz" => ["application/tar", "application/tar+gzip"]
    ];
    const MOBILEPHONE_PATTERN = '/^(0[6-7]\d{8}|002305\d{7}|00491\d{9,10}|00212\d{9}|0041\d{9}|0022901\d{8})$/';
    // 06 or 07 for metropolitan france, 00230 for Maurice, 0049 for Allemagne, 00212 for Morocco, 0041 for Switzerland, 00229 for Benin (cf. https://fr.chahaoba.com/Maurice)
    const PHONE_PATTERN = '/^((?:(?:\+|00)33|0)[1-9]\d{8}|00212\d{9}|0041\d{9})$/';

    /**
     * @param string|null $text
     * @return string|null
     */
    public static function removeAccent(?string $text): ?string
    {
        if (empty($text)) {
            return $text;
        }
        $replacements = array('Š' => 'S', 'š' => 's', 'Ž' => 'Z', 'ž' => 'z', 'À' => 'A', 'Á' => 'A', 'Â' => 'A', 'Ã' => 'A', 'Ä' => 'A', 'Å' => 'A', 'Æ' => 'A', 'Ç' => 'C', 'È' => 'E', 'É' => 'E',
            'Ê' => 'E', 'Ë' => 'E', 'Ì' => 'I', 'Í' => 'I', 'Î' => 'I', 'Ï' => 'I', 'Ñ' => 'N', 'Ò' => 'O', 'Ó' => 'O', 'Ô' => 'O', 'Õ' => 'O', 'Ö' => 'O', 'Ø' => 'O', 'Ù' => 'U',
            'Ú' => 'U', 'Û' => 'U', 'Ü' => 'U', 'Ý' => 'Y', 'Þ' => 'B', 'ß' => 'Ss', 'à' => 'a', 'á' => 'a', 'â' => 'a', 'ã' => 'a', 'ä' => 'a', 'å' => 'a', 'æ' => 'a', 'ç' => 'c',
            'è' => 'e', 'é' => 'e', 'ê' => 'e', 'ë' => 'e', 'ì' => 'i', 'í' => 'i', 'î' => 'i', 'ï' => 'i', 'ð' => 'o', 'ñ' => 'n', 'ò' => 'o', 'ó' => 'o', 'ô' => 'o', 'õ' => 'o',
            'ö' => 'o', 'ø' => 'o', 'ù' => 'u', 'ú' => 'u', 'û' => 'u', 'ý' => 'y', 'þ' => 'b', 'ÿ' => 'y');
        return strtr($text, $replacements);
    }

    /**
     * "deburr" the text so that it can be compared to another one
     * @param string $text
     * @return string
     */
    public static function normalizeString(string $text): string
    {
        return str_replace('-', ' ', strtolower(self::removeAccent($text)));
    }

    /**
     * Replace weird quotes and dash by their standard version so they are compatible with Latin-1
     * Only replace with equivalent number of chars (don't do euro sign, ... etc.)
     * @param string|null $text
     * @return string|null
     */
    public static function normalizeUnicodeChars(?string $text): ?string
    {
        if (empty($text)) {
            return $text;
        }
        $replacements = [
            '‘' => "'", '’' => "'", '‛' => "'", '′' => "'", // Single quote
            '“' => '"', '”' => '"', '‟' => '"', '″' => '"', // Double quote
            '–' => '-', '—' => '-', '―' => '-' // Dash
        ];
        return strtr($text, $replacements);
    }

    /**
     * Percentage of similarity
     * @param string $a
     * @param string $b
     * @return float
     */
    public static function computeStringSimilarity(string $a, string $b): float
    {
        // See https://www.php.net/manual/en/function.levenshtein.php
        // Weights of insert / delete / replace can be configured
        // For other options see
        // For how the text sounds, see https://www.php.net/manual/en/function.soundex.php and https://www.php.net/manual/en/function.metaphone.php
        // Another option, more forgiving regarding order of similar characters: https://www.php.net/manual/en/function.similar-text.php
        $normalizedA = self::normalizeString($a);
        $normalizedB = self::normalizeString($b);

        $similarityRate = 0.0;
        if (strlen($normalizedA) > strlen($normalizedB)) {
            if (str_contains($normalizedA, $normalizedB)) {
                $similarityRate = 100;
            }
        } else if (strlen($normalizedA) < strlen($normalizedB)) {
            if (str_contains($normalizedB, $normalizedA)) {
                $similarityRate = 100;
            }
        }
        if ($similarityRate === 0.0) {
            $length = max(strlen($normalizedA), strlen($normalizedB));
            $similarityRate = 100 - levenshtein($normalizedA, $normalizedB) / $length * 100;
        }

        return $similarityRate;
    }

    /**
     * @param string $text
     * @return string
     */
    public static function fixWeirdSymbols(string $text): string
    {
        $unwanted_array = array('Ã©' => 'é', 'Ã¨' => 'è', 'Ã ' => 'à', 'Ã¯' => 'ï', 'Ã´' => 'ô', 'Ã§' => 'ç', 'Ãª' => 'ê', 'Ã¹' => 'ù', 'Ã¦' => 'æ', 'Å' => 'œ', 'Ã«' => 'ë', 'Ã¼' => 'ü', 'Ã¢' => 'â', 'Â©' => '©', 'Â¤' => '¤', 'â¬' => '€');
        return strtr($text, $unwanted_array);
    }

    /**
     * @param string $text
     * @return string
     */
    public static function toTitleCase(string $text): string
    {
        return str_replace('- ', '-', ucwords(str_replace('-', '- ', mb_strtolower($text))));
    }

    /**
     * @param string $haystack
     * @param string $needle
     * @param bool $case_insensitive
     * @return bool
     */
    public static function startsWith(string $haystack, string $needle, bool $case_insensitive = false): bool
    {
        return substr_compare($haystack, $needle, 0, strlen($needle), $case_insensitive) === 0;
    }

    /**
     * @param string $haystack
     * @param string $needle
     * @return bool
     */
    public static function endsWith(string $haystack, string $needle): bool
    {
        return substr_compare($haystack, $needle, -strlen($needle)) === 0;
    }

    /**
     * @param string $haystack
     * @param string $needle
     * @return bool
     */
    public static function contains(string $haystack, string $needle): bool
    {
        return strpos($haystack, $needle) !== false;
    }

    /**
     * @param string $string
     * @param string $prefix
     * @return string
     */
    public static function removePrefix(string $string, string $prefix): string
    {
        return self::startsWith($string, $prefix) ? substr($string, strlen($prefix)) : $string;
    }

    /**
     * @param string $string
     * @param string $suffix
     * @return string
     */
    public static function removeSuffix(string $string, string $suffix): string
    {
        return self::endsWith($string, $suffix) ? substr($string, 0, (strrpos($string, $suffix))) : $string;
    }

    /**
     * @param string $needle
     * @param string $replace
     * @param string $haystack
     * @return string|string[]
     */
    public static function strReplaceFirst(string $needle, string $replace, string $haystack): string
    {
        $pos = strpos($haystack, $needle);
        if ($pos !== false) {
            $haystack = substr_replace($haystack, $replace, $pos, strlen($needle));
        }
        return $haystack;
    }

    /**
     * @param $a
     * @param $b
     * @param string $field
     * @return int
     */
    public static function sortArrayByDate($a, $b, string $field = "issued"): int
    {
        return strtotime($b[$field]) - strtotime($a[$field]);
    }

    /**
     * @param array $defaultOptions
     * @param int $maxHostConnections
     * @param int $maxPendingPushes
     * @return HttpClientInterface
     */
    public static function getHttpClient(array $defaultOptions = [], int $maxHostConnections = 6, int $maxPendingPushes = 50): HttpClientInterface
    {
        if ($_ENV["HTTP_CLIENT_WITH_CURL"] == 'yes') {
            return new CurlHttpClient($defaultOptions, $maxHostConnections, $maxPendingPushes);
        } else {
            return HttpClient::create($defaultOptions, $maxHostConnections, $maxPendingPushes);
        }
    }

    /**
     * @param $arr
     * @return array
     */
    public static function flatten($arr): array
    {
        $it = new RecursiveIteratorIterator(new RecursiveArrayIterator($arr));
        return iterator_to_array($it);
    }

    /**
     * @param DateTime $date
     * @param int $weekDaysNumber
     * @param int $hours
     * @param int $minutes
     * @param array $holidays array of DateTime
     * @return DateTime
     */
    public static function addWeekDays(DateTime $date, int $weekDaysNumber, int $hours = 0, int $minutes = 0, array $holidays = []): DateTime
    {
        if (!empty($holidays)) {
            $nextBusinessDay = clone $date;
            for ($i = 1; $i <= $weekDaysNumber; $i++) {
                $nextBusinessDay->modify("+1 weekdays");
                if (in_array($nextBusinessDay, $holidays)) {
                    $weekDaysNumber++;
                }
            }
        }
        return $date->modify("+$weekDaysNumber weekdays + $hours hours + $minutes minutes");
    }

    /**
     * @param DateTime $startDate
     * @param DateTime $endDate
     * @return int
     */
    public static function getNumberWeekDays(DateTime $startDate, DateTime $endDate): int
    {
        $workingDays = [1, 2, 3, 4, 5];
        $holidayDays = ['*-01-01', '*-05-01', '*-05-08', '*-07-14', '*-08-15', '*-11-01', '*-11-11', '*-12-25'];
        // missing : Lundi de Pâques, Ascension, Lundi de Pentecôte
        $interval = new DateInterval('P1D');
        $periods = new DatePeriod($startDate, $interval, $endDate);
        $days = 0;
        foreach ($periods as $period) {
            if (!in_array($period->format('N'), $workingDays)) continue;
            if (in_array($period->format('Y-m-d'), $holidayDays)) continue;
            if (in_array($period->format('*-m-d'), $holidayDays)) continue;
            $days++;
        }
        return $days;
    }

    /**
     * @param int $length
     * @param bool $allowUpper
     * @param bool $allowLower
     * @param bool $allowDigit
     * @return string
     * @throws Exception
     */
    public static function generateRandomString(int $length, bool $allowUpper = true, bool $allowLower = true, bool $allowDigit = true): string
    {
        $permittedChars = '';
        if ($allowUpper) {
            $permittedChars = 'ABCDEFGHJKLMNPQRSTUVWXYZ';
        }
        if ($allowLower) {
            $permittedChars = $permittedChars . 'abcdefghijkmnopqrstuvwxyz';
        }
        if ($allowDigit) {
            $permittedChars = $permittedChars . '123456789';
        }

        $input_length = strlen($permittedChars);
        $random_string = '';
        for ($i = 0; $i < $length; $i++) {
            $random_character = $permittedChars[random_int(0, $input_length - 1)];
            $random_string .= $random_character;
        }

        return $random_string;
    }

    public static function isNoResultException(ErrorException $e): bool
    {
        return in_array($e->getCode(), [404, 418]);
    }

    // Polecat's Multi-dimensional array_replace function
    //
    /**
     * Will take all data in second array and apply to first array leaving any non-corresponding values untouched and intact
     *
     * @param array $array1
     * @param array $array2
     * @return array
     */
    public static function multidimensionalArrayReplace(array &$array1, array &$array2): array
    {
        foreach ($array2 as $key => $val) {
            if (is_array($val) && array_key_exists($key, $array1) && $array1[$key] != null) {
                self::tierParse($array1[$key], $array2[$key]);
            } else {
                $array1[$key] = $val;
            }
        }
        return $array1;
    }

    /**
     * @param array $reference
     * @param array $against
     * @param string $key
     * @return array
     */
    public static function arrayDiffAssocKey(array $reference, array $against, string $key): array
    {
        $diff = $reference;
        $referenceColumn = array_column($reference, $key);

        foreach ($against as $a_key => $value) {
            //if array1 name value exist in array 2 get that key and delete from array 2 using unset.
            $index = array_search($value[$key], $referenceColumn);
            if ($index !== false) {
                unset($diff[$index]);
            }
        }

        return $diff;
    }

    /**
     * @param array $reference
     * @param array $against
     * @param string $key
     * @return array
     */
    public static function arrayIntersectAssocKey(array $reference, array $against, string $key): array
    {
        $intersection = [];
        $referenceColumn = array_column($reference, $key);

        foreach ($against as $a_key => $value) {
            //if array1 name value exist in array 2 get that key and delete from array 2 using unset.
            $index = array_search($value[$key], $referenceColumn);
            if ($index !== false) {
                $intersection[] = $reference[$index];
            }
        }

        return $intersection;
    }

    // This sub function is the iterator that will loop back on itself ad infinitum till it runs out of array dimensions
    private static function tierParse(array &$t_array1, array &$t_array2): void
    {
        foreach ($t_array2 as $k2 => $v2) {
            if (is_array($v2) && array_key_exists($k2, $t_array1)) {
                self::tierParse($t_array1[$k2], $t_array2[$k2]);
            } else {
                $t_array1[$k2] = $v2;
            }
        }
    }

    /**
     * This methods tells wether we should update CPF raw data for catalog objects
     * If new data is better => update, otherwise we keep the old data.
     * It is based on public / private data differences
     * @param $object
     * @param array $newRawData
     * @param string $publicField
     * @return bool
     */
    public static function shouldUpdateCpfRawData($object, array $newRawData, string $publicField): bool
    {
        // If no new data, it is useless => don't update
        if (!isset($newRawData)) {
            return false;
        }
        // If no old data, any new data is better => update
        $oldRawData = $object->getRawData();
        if (!isset($oldRawData)) {
            return true;
        }
        // Otherwise
        // - If old data is public, any new data is better (private or public) => update
        // - If new data is private, we want it => update
        return array_key_exists($publicField, $oldRawData) || !array_key_exists($publicField, $newRawData);
    }

    public static function dashesToCamelCase($string, $capitalizeFirstCharacter = false)
    {

        $str = str_replace(' ', '', ucwords(str_replace('-', ' ', $string)));

        if (!$capitalizeFirstCharacter) {
            $str[0] = strtolower($str[0]);
        }

        return $str;
    }

    /**
     * @param $object
     * @param array $data
     * @param array $updatableProperties
     * @return array
     */
    public static function filterDataToUpdateOnObject($object, array $data, array $updatableProperties): array
    {
        // Keeps only properties XXX listed as updatable
        // where there exist a method getXXX or isXXX on the object
        // and where data has changed
        return array_filter($data, function ($newValue, $propertyName) use ($object, $updatableProperties) {
            if (!in_array($propertyName, $updatableProperties)) {
                return false;
            }
            $ucKey = ucwords($propertyName);
            if (method_exists($object, "get" . $ucKey)) {
                $currentValue = $object->{"get" . $ucKey}();
                if ($propertyName === 'tags') {
                    // Hack to handle equality with an array in input (e.g. ['tag1', 'tag2'])
                    $currentValue = array_map(function ($tagObject) {
                        return (string)$tagObject; // Call __toString()
                    }, $currentValue->toArray());
                }
                if (is_object($currentValue) || is_object($newValue)) {
                    return $currentValue != $newValue; // Triple equals check for ref equality on objects while we only want value equality
                } else {
                    return $currentValue !== $newValue;
                }
            } else if (method_exists($object, "is" . $ucKey)) {
                $currentValue = $object->{"is" . $ucKey}();
                return $currentValue !== $newValue;
            } else {
                return false;
            }
        }, ARRAY_FILTER_USE_BOTH);
    }

    /**
     * @param $tempFile
     * @param string $fileName
     * @return Response
     */
    public static function getCsvResponse($tempFile, string $fileName): Response
    {
        rewind($tempFile);
        $response = new Response(stream_get_contents($tempFile));
        fclose($tempFile);
        $response->headers->set('Content-Type', 'text/csv');
        $response->headers->set('Content-Disposition', 'attachment; filename="' . $fileName . '.csv"');
        return $response;
    }

    /**
     * @param Xlsx $writer
     * @param string $fileName
     * @return Response
     */
    public static function getExcelResponse(Xlsx $writer, string $fileName): Response
    {
        $response = new StreamedResponse();
        $response->headers->set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        $response->headers->set('Content-Disposition', 'attachment; filename="' . $fileName . '.xlsx"');
        $response->setPrivate();
        $response->headers->addCacheControlDirective('no-cache');
        $response->headers->addCacheControlDirective('must-revalidate');
        $response->setCallback(function () use ($writer) {
            $writer->save('php://output');
        });
        return $response;
    }

    /**
     * @param $data
     * @param array $availableColumns
     * @param string|null $includedColumns
     * @param null $tempFile
     * @param bool $isForCertificationFolders
     * @return false|resource
     */
    public static function convertDataToCSVFile($data, array $availableColumns, string $includedColumns = null, $tempFile = null, bool $isForCertificationFolders = false)
    {
        $csvColumns = isset($includedColumns) ? explode(',', strtoupper($includedColumns)) : null;
        $unknownColumns = $csvColumns ? array_diff($csvColumns, $availableColumns) : [];
        if (!empty($unknownColumns)) {
            throw new WedofBadRequestHttpException("Erreur, les colonnes suivantes ne sont pas reconnues pour l'export csv : " . join(",", $unknownColumns));
        }
        $columns = $csvColumns ? array_intersect($csvColumns, $availableColumns) : $availableColumns;
        $separator = ';';
        $addHeaders = empty($tempFile);
        $tempFile = $tempFile ?? fopen('php://temp', 'w');
        if ($addHeaders) fputcsv($tempFile, $columns, $separator);
        if ($isForCertificationFolders) {
            $value = ['', 'PAR_SCORING ou PAR_ADMISSION', '', '', '', 'M ou F', '', '', '', '', '', '', '', 'A_DISTANCE ou EN_PRESENTIEL ou MIXTE', '', '', '', '', '', 'URL', '', '',
                '(Facultatif) C2 ou C1 ou B2 ou B1 ou A2 ou A1 ou INSUFFISANT', 'SANS_MENTION ou MENTION_ASSEZ_BIEN ou MENTION_BIEN ou MENTION_TRES_BIEN ou MENTION_TRES_BIEN_AVEC_FELICITATIONS_DU_JURY', '', 'FORMATION_INITIALE_HORS_APPRENTISSAGE ou 
            FORMATION_INITIALE_APPRENTISSAGE ou FORMATION_CONTINUE_HORS_CONTRAT_DE_PROFESSIONNALISATION ou FORMATION_CONTINUE_CONTRAT_DE_PROFESSIONNALISATION ou VAE ou EQUIVALENCE ou CANDIDAT_LIBRE',
                '(Doit être rempli si MODALITE_ACCESS est VAE) CONGES_VAE ou VAE_CLASSIQUE', 'OF ou CERTIFIE ou POLE_EMPLOI ou EMPLOYEUR ou AUTRE ', '', '', '', '', '', 'Dossier complet pour export CDC', 'toExport (Dossier à exporter) ou doNotExport (Dossier ne doit pas être exporté, ex: doublon)'];
            fputcsv($tempFile, $value, $separator);
        }
        foreach ($data as $data_entry) {
            fputcsv($tempFile, call_user_func(array(get_class($data_entry), 'getCSVFormat'), $data_entry, $columns), $separator);
        }
        return $tempFile;
    }

    public static function cleanString($string)
    {
        $string = str_replace(' ', '-', $string); // Replaces all spaces with hyphens.

        return preg_replace('/[^A-Za-z0-9\-]/', '', $string); // Removes special chars.
    }

    /**
     * @param $dateString
     * @param false $endOfDay
     * @param DateTimeZone|null $dateTimeZone
     * @return DateTime|bool
     */
    public static function generateDateStartOrEndOfDay($dateString, bool $endOfDay = false, DateTimeZone $dateTimeZone = null)
    {
        $date = DateTime::createFromFormat('d/m/Y', $dateString);
        if ($date) {
            $date->setTime($endOfDay ? 23 : 0, $endOfDay ? 59 : 0, $endOfDay ? 59 : 0);
            if ($dateTimeZone) {
                $date->setTimezone($dateTimeZone);
            }
        }
        return $date;
    }


    /**
     * Custom XML serialization that supports attributes, cdata, repeting keys, conditions...
     * Config is provided with "_" and if there is config, the value must be provided in "_value"
     * Supported config:
     * _value: any
     * _key: string
     * _cdata: bool
     * _attributes: array
     * _if: bool
     * _ifNotEmpty: bool // not set, null, empty string or empty array. "false" boolean value, '0' and 0 are kept
     * @param SimpleXMLElement $parent
     * @param array $data
     * @return void
     */
    public static function arrayToXml(SimpleXMLElement $parent, array $data): void
    {
        /* EXAMPLE
        $parent = new SimpleXMLElement('<root></root>');
        $data = [
            // Raw value
            'toto' => 'tutu',
            // Nested element
            'foo' => [
                'bar' => 'baz'
            ],
            // Attributes
            'zz' => [
                '_attributes' => [
                    'id' => '123',
                ],
                '_value' => 'xx'
            ],
            // CDATA
            'blu' => [
                '_cdata' => true,
                '_value' => '<strong>Bla</strong>'
            ],
            // Override key so it can be repeated
            [
                '_key' => 'li',
                '_value' => 'john'
            ],
            [
                '_key' => 'li',
                '_value' => 'jane'
            ]
        ];
        ======>
        <root>
            <toto>tutu</toto>
            <foo>
              <bar>baz</bar>
            </foo>
            <zz id="123">xx</zz>
            <blu><![CDATA[<strong>Bla</strong>]]></blu>
            <li>john</li>
            <li>jane</li>
        </root>
        */
        foreach ($data as $key => $value) {
            if (isset($value['_if']) && !$value['_if']) {
                continue;
            }
            if (isset($value['_ifNotEmpty']) &&
                (empty($value['_value']) && $value['_value'] !== 0 && $value['_value'] !== '0' && $value['_value'] !== false || is_string($value['_value']) && trim($value['_value']) === '')) {
                continue;
            }
            $attributes = $value['_attributes'] ?? [];
            $cdata = !empty($value['_cdata']);
            if (!empty($value['_key'])) {
                $key = $value['_key']; // Override in special cases
            }
            if (isset($value['_value'])) {
                $value = $value['_value'];
            }
            if (is_array($value)) {
                $child = $parent->addChild($key);
                self::arrayToXml($child, $value); // recursive call
            } else if ($cdata) {
                $child = $parent->addChild($key);
                $domNode = dom_import_simplexml($child);
                $domNode->appendChild($domNode->ownerDocument->createCDATASection($value));
            } else {
                if ($value === false) {
                    $value = '0'; // Because (string)true == '1" and (string)false == '', which is not consistent
                }
                $child = $parent->addChild($key, htmlspecialchars((string)$value));
            }
            foreach ($attributes as $attributeName => $attributeValue) {
                $child->addAttribute($attributeName, $attributeValue);
            }
        }
    }

    /**
     * @param $object
     * @return mixed
     */
    public static function xmlToArray($object)
    {
        return json_decode(json_encode($object), 1);
    }

    /**
     * @param DateTimeInterface $dateTimeInterface
     * @return DateTime
     */
    public static function convertDateTimeInterfaceToDateTime(DateTimeInterface $dateTimeInterface): DateTime
    {
        $dateTime = new DateTime();
        return $dateTime->setTimestamp($dateTimeInterface->getTimestamp());
    }

    /**
     * @param object $object
     * @return string
     */
    public static function getClassName(object $object): string
    {
        $exploded = explode('\\', get_class($object));
        return array_pop($exploded);
    }

    /**
     * @param int|null $cog
     * @param string|null $name
     * @param string|null $code
     * @return array|null
     */
    public static function findCountry(int $cog = null, string $name = null, string $code = null): ?array
    {
        try {
            $cog = $cog > 99000 ? ($cog - 99000) : $cog;
            if ($cog == 100 || strtolower($name) == 'france' || strtolower($code) == 'fr' || strtolower($code) == 'fra') {
                return [
                    "name" => "France",
                    "cog" => 100,
                    "code" => "FR",
                    "code3" => "FRA"
                ];
            }
            $inputFileName = __DIR__ . '/../../../data/countriesListCode.json';
            $json = file_get_contents($inputFileName);
            $array = json_decode($json, true);
            if ($name && self::normalizeString($name) === self::normalizeString('COTE D IVOIRE')) {
                // Hack for when data comes from portail
                $name = "COTE D'IVOIRE";
            }
            foreach ($array as $element) {
                if ($cog && $cog == $element['cog']) {
                    return $element;
                } else if ($name && self::normalizeString($name) == self::normalizeString($element['name'])) {
                    return $element;
                } else if ($code && (strtolower($code) == strtolower($element['code']) || strtolower($code) == strtolower($element['code3']))) {
                    return $element;
                }
            }
        } catch (Exception $e) {
            return null;
        }
        return null;
    }

    /**
     * @param string $email
     * @return string
     */
    public static function obfuscateEmailAddress(string $email): string
    {
        $emailParts = explode('@', $email);
        $username = $emailParts[0];
        $domain = $emailParts[1];
        // firstname.lastname@... => f******e.l******e@...
        $usernameParts = explode('.', $username);
        $nbUsernameParts = count($usernameParts);
        $obfuscatedUsername = '';
        foreach ($usernameParts as $index => $usernamePart) {
            $usernamePartLength = strlen($usernamePart);
            $obfuscatedUsernamePart = $usernamePartLength > 1 ? $usernamePart[0] . str_repeat('*', $usernamePartLength - 2) . $usernamePart[$usernamePartLength - 1] : $usernamePart;
            $obfuscatedUsername .= $obfuscatedUsernamePart . ($index < $nbUsernameParts - 1 ? '.' : '');
        }
        // ...@subdomain.domain.tld => ...@s*******n.d****n.tld
        $domainParts = explode('.', $domain);
        $nbDomainParts = count($domainParts);
        $obfuscatedDomain = '';
        foreach ($domainParts as $index => $domainPart) {
            if ($index < $nbDomainParts - 1) {
                $domainPartLength = strlen($domainPart);
                $obfuscatedDomainPart = $domainPartLength > 1 ? $domainPart[0] . str_repeat('*', $domainPartLength - 2) . $domainPart[$domainPartLength - 1] : $domainPart;
                $obfuscatedDomain .= $obfuscatedDomainPart . '.';
            } else {
                $obfuscatedDomain .= $domainPart;
            }
        }
        // <EMAIL> => f*******e.l******e@s*******n.d****n.tld
        return $obfuscatedUsername . '@' . $obfuscatedDomain;
    }

    /**
     * @param DateTimeInterface|null $dateTime
     * throws WedofBadRequestHttpException
     */
    public static function throwIfDateNotContemporary(?DateTimeInterface $dateTime)
    {
        if (!empty($dateTime)) {
            $year = $dateTime->format('Y');
            if ($year <= 1900 || $year >= 2100) { // loosely based on cdc dictionnary
                throw new WedofBadRequestHttpException("Erreur, l'année " . $year . " n'est pas contemporaine");
            }
        }
    }

    /**
     * @param array $events
     * @param array $eventTypes
     * @return array|string[]
     */
    public static function cleanEvents(array $events, array $eventTypes): array
    {
        $eventsFinal = [];
        $events = new ArrayCollection($events);
        if ($events->contains("*") || empty($events)) {
            $eventsFinal = ['*'];
        } else {
            foreach ($eventTypes as $type) {
                $events_for_type = $events->filter(function ($element) use ($type) {
                    return str_starts_with($element, $type . ".");
                });
                if ($events_for_type->contains($type . ".*")) {
                    $eventsFinal[] = $type . ".*";
                } else {
                    foreach ($events_for_type as $event) {
                        $eventsFinal[] = $event;
                    }
                }
            }
        }
        return $eventsFinal;
    }

    /**
     * @param $html
     * @param $tag
     * @return string|null
     */
    public static function getMetaTagsFromString($html, $tag): ?string
    {
        $doc = new DOMDocument();
        libxml_use_internal_errors(true);
        $doc->loadHTML($html);
        libxml_clear_errors();

        $xpath = new DOMXPath($doc);
        $nodes = $xpath->query('//head/meta[@name]');
        $meta = [];

        foreach ($nodes as $node) {
            $meta[$node->getAttribute('name')] = $node->getAttribute('content');
        }

        return $meta[$tag] ?? null;
    }

    /**
     * @param string $date
     * @return DateTime
     * @throws Exception
     */
    public static function createDateFromString(string $date): DateTime
    {
        if (preg_match('/^(\d{2})\/(\d{2})\/(\d{4})$/', $date)) {
            return DateTime::createFromFormat("d/m/Y", $date);
        } else if (preg_match('/^(\d{4})-(\d{2})-(\d{2})$/', $date)) {
            return DateTime::createFromFormat("Y-m-d", $date);
        } else {
            return new DateTime($date);
        }
    }

    /**
     * @param array $fileTypes
     * @param Collection $files A collection of files (either RegistrationFolderFile or CertificationFolderFile or CertificationPartnerFile).
     * @param string $state
     * @return array
     */
    public static function getMissingFileTypesForState(array $fileTypes, Collection $files, string $state): array
    {
        $missingFileTypes = [];
        $requiredFileTypes = array_filter($fileTypes, function ($fileType) use ($state) {
            return isset($fileType['toState']) && $fileType['toState'] === $state && empty($fileType['generated']);
        });
        $uploadedFileTypeIds = $files->map(function ($file) {
            return $file->getTypeId();
        });
        foreach ($requiredFileTypes as $requiredFileType) {
            if (!$uploadedFileTypeIds->contains($requiredFileType['id'])) {
                $missingFileTypes[] = $requiredFileType;
            }
        }
        return $missingFileTypes;
    }

    /**
     * @param array $fileTypes
     * @param iterable $entityTags
     * @param string|null $entityCertifInfo
     * @return array
     */
    public static function filterFileTypes(array $fileTypes, iterable $entityTags, ?string $entityCertifInfo): array
    {
        return array_values(array_filter($fileTypes, function ($fileType) use ($entityTags, $entityCertifInfo) {
            $fileTypeTags = $fileType['tags'] ?? [];
            $fileTypeCertifInfos = $fileType['certifications'] ?? [];
            $certifOk = empty($fileTypeCertifInfos) || (!empty($entityCertifInfo) && in_array($entityCertifInfo, $fileTypeCertifInfos));
            if (empty($fileTypeTags)) {
                $tagsOk = true;
            } else {
                $tagsOk = false;
                foreach ($entityTags as $entityTag) {
                    if (in_array($entityTag, $fileTypeTags)) { // One matching tag is enough
                        $tagsOk = true;
                        break;
                    }
                }
            }
            return $tagsOk && $certifOk;
        }));
    }

    /**
     * @param $name
     * @param null $defaultValue
     * @return mixed|true|null
     */
    public static function getEnvValue($name, $defaultValue = null)
    {
        if (isset($_ENV[$name])) {
            if (is_numeric($_ENV[$name])) {
                return (int)$_ENV[$name];
            } else if ($_ENV[$name] === 'true' || $_ENV[$name] === 'on' || $_ENV[$name] === 'yes') {
                return true;
            } else if ($_ENV[$name] === 'false' || $_ENV[$name] === 'off' || $_ENV[$name] === 'no') {
                return false;
            } else {
                return $_ENV[$name];
            }
        } else {
            return $defaultValue;
        }
    }

    /**
     * @return string
     */
    public static function getEnv(): string
    {
        return $_ENV['APP_ENV'];
    }

    /**
     * @param array $envs
     * @return bool
     */
    public static function isEnvIn(array $envs): bool
    {
        return in_array(self::getEnv(), $envs);
    }

    /**
     * @param string $period
     * @param DateTimeZone|null $timezone
     * @param Subscription|null $subscription
     * @return array
     * @throws Exception
     */
    public static function getSinceAndUntilDates(string $period, DateTimeZone $timezone = null, Subscription $subscription = null): array
    {
        if (!$timezone) {
            $timezone = new DateTimeZone('UTC');
        }
        $getSinceStart = function ($dateString) use ($timezone) {
            return (new DateTime($dateString, $timezone))->modify('midnight');
        };
        $getUntilEnd = function ($dateString) use ($timezone) {
            return (new DateTime($dateString, $timezone))->modify('midnight')->modify('+1 day -1 microsecond');
        };
        $since = null;
        $until = null;
        switch ($period) {
            // year
            case PeriodTypes::NEXT_YEAR()->getValue():
                $since = $getSinceStart('next year January 1st');
                $until = $getUntilEnd('next year December 31st');
                break;
            case PeriodTypes::CURRENT_YEAR()->getValue():
                $since = $getSinceStart('this year January 1st');
                $until = $getUntilEnd('this year December 31st');
                break;
            case PeriodTypes::PREVIOUS_YEAR()->getValue():
                $since = $getSinceStart('last year January 1st');
                $until = $getUntilEnd('last year December 31st');
                break;
            case PeriodTypes::ROLLING_YEAR()->getValue():
                $since = $getSinceStart('- 12 months');
                $until = $getUntilEnd('today');
                break;
            case PeriodTypes::ROLLING_YEAR_FUTURE()->getValue():
                $since = $getSinceStart('today');
                $until = $getUntilEnd('+ 12 months');
                break;
            // Months
            case PeriodTypes::NEXT_MONTH()->getValue():
                $since = $getSinceStart('first day of next month');
                $until = $getUntilEnd('last day of next month');
                break;
            case PeriodTypes::CURRENT_MONTH()->getValue():
                $since = $getSinceStart('first day of this month');
                $until = $getUntilEnd('last day of this month');
                break;
            case PeriodTypes::PREVIOUS_MONTH()->getValue():
                $since = $getSinceStart('first day of previous month');
                $until = $getUntilEnd('last day of previous month');
                break;
            case PeriodTypes::ROLLING_MONTH()->getValue():
                $since = $getSinceStart('- 30 days');
                $until = $getUntilEnd('today');
                break;
            case PeriodTypes::ROLLING_MONTH_FUTURE()->getValue():
                $since = $getSinceStart('today');
                $until = $getUntilEnd('- 30 days');
                break;
            // Weeks
            case PeriodTypes::NEXT_WEEK()->getValue():
                $since = $getSinceStart('next week monday');
                $until = $getUntilEnd('next week sunday');
                break;
            case PeriodTypes::CURRENT_WEEK()->getValue():
                $day = date('w') - 1;
                $since = $getSinceStart('-' . $day . ' days');
                $until = $getUntilEnd('+' . (6 - $day) . ' days');
                break;
            case PeriodTypes::PREVIOUS_WEEK()->getValue():
                $since = $getSinceStart('last week monday');
                $until = $getUntilEnd('last week sunday');
                break;
            case PeriodTypes::ROLLING_WEEK()->getValue():
                $since = $getSinceStart('-7 days');
                $until = $getUntilEnd('today');
                break;
            case PeriodTypes::ROLLING_WEEK_FUTURE()->getValue():
                $since = $getSinceStart('today');
                $until = $getUntilEnd('+7 days');
                break;
            // Days
            case PeriodTypes::TOMORROW()->getValue():
                $since = $getSinceStart('tomorrow');
                $until = $getUntilEnd('tomorrow');
                break;
            case PeriodTypes::TODAY()->getValue():
                $since = $getSinceStart('today');
                $until = $getUntilEnd('today');
                break;
            case PeriodTypes::YESTERDAY()->getValue():
                $since = $getSinceStart('yesterday');
                $until = $getUntilEnd('yesterday');
                break;
            // Other
            case PeriodTypes::WEDOF_INVOICE()->getValue():
                if ($subscription) {
                    $since = $subscription->getCertifierPeriodStartDate();
                    $until = new DateTime();
                    $until->setTimestamp($subscription->getCertifierPeriodEndDate()->getTimestamp())->modify('- 1 second'); // Prevent overlap with next month period
                } else {
                    throw new WedofBadRequestHttpException("L'abonnement est obligatoire pour pouvoir filtrer sur la période de facturation.");
                }
                break;
            case PeriodTypes::WEDOF_QUOTA()->getValue():
                if ($subscription) {
                    $since = $subscription->getCertificationFoldersNumberPeriodStartDate();
                    $until = new DateTime();
                    $until->setTimestamp($subscription->getCertificationFoldersNumberPeriodEndDate()->getTimestamp())->modify('- 1 second'); // Prevent overlap with next month period
                } else {
                    throw new WedofBadRequestHttpException("L'abonnement est obligatoire pour pouvoir filtrer sur la période de facturation.");
                }
                break;
            default:
                throw new WedofBadRequestHttpException("Période inconnue !");
        }
        return ['since' => $since, 'until' => $until];
    }

    /**
     * @param string $csvRawFileContent
     * @param string $separator
     * @param bool $convertEmptyToNull
     * @return array
     */
    public static function csvToArray(string $csvRawFileContent, string $separator = ';', bool $convertEmptyToNull = false): array
    {
        $array = [];
        if (!empty($csvRawFileContent)) {
            $lines = array_map(fn($line) => mb_convert_encoding($line, 'UTF-8', 'auto'), explode(PHP_EOL, $csvRawFileContent));
            $lines = array_filter($lines, fn($line) => trim($line) !== ''); // Filter last line and potential empty lines
            $rows = array_map(function ($line) use ($separator, $convertEmptyToNull) {
                $row = str_getcsv($line, $separator);
                $row = array_map(fn($cell) => trim($cell), $row);
                if ($convertEmptyToNull) {
                    $row = array_map(fn($cell) => $cell === '' ? null : $cell, $row); // by default empty cell is "", replace it by null instead
                }
                return $row;
            }, $lines);
            $keys = array_shift($rows); // Extract header row to make it keys of the associative array
            $keys[0] = preg_replace('/^\x{FEFF}/u', '', $keys[0]); // Remove BOM if present
            foreach ($rows as $row) {
                $entry = array_combine($keys, $row);
                $array[] = $entry;
            }
        }
        return $array;
    }

    /**
     * @param $str
     * @param string $allowable_tags
     * @param bool $strip_attrs
     * @param bool $preserve_comments
     * @param callable|null $callback
     * @return string
     */
    public static function stripTagsContent($str, string $allowable_tags = '', bool $strip_attrs = false, bool $preserve_comments = false, callable $callback = null): string
    {
        $allowable_tags = array_map('strtolower', array_filter( // lowercase
            preg_split('/(?:>|^)\\s*(?:<|$)/', $allowable_tags, -1, PREG_SPLIT_NO_EMPTY), // get tag names
            function ($tag) {
                return preg_match('/^[a-z][a-z0-9_]*$/i', $tag);
            } // filter broken
        ));
        $comments_and_stuff = preg_split('/(<!--.*?(?:-->|$))/', $str, -1, PREG_SPLIT_DELIM_CAPTURE);
        foreach ($comments_and_stuff as $i => $comment_or_stuff) {
            if ($i % 2) { // html comment
                if (!($preserve_comments && preg_match('/<!--.*?-->/', $comment_or_stuff))) {
                    $comments_and_stuff[$i] = '';
                }
            } else { // stuff between comments
                $tags_and_text = preg_split("/(<(?:[^>\"']++|\"[^\"]*+(?:\"|$)|'[^']*+(?:'|$))*(?:>|$))/", $comment_or_stuff, -1, PREG_SPLIT_DELIM_CAPTURE);
                foreach ($tags_and_text as $j => $tag_or_text) {
                    $tag = false;
                    $is_broken = false;
                    $is_allowable = true;
                    $result = $tag_or_text;
                    if ($j % 2) { // tag
                        if (preg_match("%^(</?)([a-z][a-z0-9_]*)\\b(?:[^>\"'/]++|/+?|\"[^\"]*\"|'[^']*')*?(/?>)%i", $tag_or_text, $matches)) {
                            $tag = strtolower($matches[2]);
                            if (in_array($tag, $allowable_tags)) {
                                if ($strip_attrs) {
                                    $opening = $matches[1];
                                    $closing = '>';
                                    $result = $opening . $tag . $closing;
                                }
                            } else {
                                $is_allowable = false;
                                $result = '';
                            }
                        } else {
                            $is_broken = true;
                            $result = '';
                        }
                    }
                    if (!$is_broken && isset($callback)) {
                        // allow result modification
                        call_user_func_array($callback, array(&$result, $tag_or_text, $tag, $is_allowable));
                    }
                    $tags_and_text[$j] = $result;
                }
                $comments_and_stuff[$i] = implode('', $tags_and_text);
            }
        }
        return implode('', $comments_and_stuff);
    }

    /**
     * @param Organism $organism
     * @return string
     */
    public static function getSubdomainForOrganism(Organism $organism): string
    {
        if (Tools::getEnvValue('DOMAIN') === 'localhost' && $organism->getSiret() != "53222292400039") {
            $subDomain = "";
        } else {
            $subDomain = $organism->getSubDomain() ? $organism->getSubDomain() . "." : "";
        }
        return 'https://' . $subDomain . Tools::getEnvValue('DOMAIN') . (Tools::getEnvValue('PORT') && !in_array($_ENV['PORT'], ['80', '443']) ? (':' . Tools::getEnvValue('PORT')) : '');
    }

    /**
     * @param string $string
     * @param string $obfuscateChar
     * @return string
     */
    public static function obfuscateString(string $string, string $obfuscateChar = '*'): string
    {
        $string = Tools::removeAccent($string); // THIS IS A HACK!! Because accentued chars turn into non UTF-8 ones that break JMS Serializer
        $chars = str_split($string);
        $size = count($chars);
        $min = round($size / 1.5);
        $rand = rand($min, ($size - $min) + 1);
        shuffle($chars);
        for ($i = 0; $rand > $i; $i++) {
            $chars[$i] = $obfuscateChar;
        }
        shuffle($chars);
        return implode($chars);
    }

    /**
     * @param array $parameters
     * @param array $columnConfigs
     * @param string $columnId
     * @return array
     */
    public static function computeKanbanColumnParameters(array $parameters, array $columnConfigs, string $columnId): array
    {
        $columnFilter = [];
        // If only in parameters => keep
        // If only in filter => keep
        // If in both
        //   if both are arrays => intersection
        //   else => keep filter, ignore parameter
        // TODO KANBAN : manage complex cases where there are both filters and they conflict
        foreach ($columnConfigs as $columnConfig) {
            if ($columnConfig['columnId'] === $columnId) {
                $columnFilter = $columnConfig['filter'];
            }
        }
        $mergedParameters = $parameters; // copy
        foreach ($columnFilter as $filterName => $filterValue) {
            if (isset($mergedParameters[$filterName]) && is_array($mergedParameters[$filterName]) && is_array($filterValue)) {
                $mergedParameters[$filterName] = array_intersect($mergedParameters[$filterName], $filterValue);
            } else {
                $mergedParameters[$filterName] = $filterValue;
            }
        }
        return $mergedParameters;
    }

    /**
     * @param string|null $name
     * @param string $email
     * @return string
     */
    public static function getFromEmail(?string $name, string $email): string
    {
        return $name ? '"' . $name . '" <' . $email . '>' : $email;
    }

    /**
     * @param array $fileExtensions
     * @return array
     */
    public static function fileExtensionsToMimeTypes(array $fileExtensions): array
    {
        $mimeTypes = [];
        foreach ($fileExtensions as $fileExtension) {
            $mimeTypes = array_merge($mimeTypes, self::MIMETYPE_CONVERSION[$fileExtension]);
        }
        return $mimeTypes;
    }

    /**
     * @param int $qualification
     * @return string
     */
    public static function getQualificationTitleFromNumber(int $qualification): string
    {
        $qualificationTitle = '';
        switch ($qualification) {
            case 0:
                $qualificationTitle = "Non renseigné";
                break;
            case 2:
                $qualificationTitle = "Sans diplôme ou diplôme national du Brevet (NIVEAU 2)";
                break;
            case 3:
                $qualificationTitle = "CAP, BEP... (NIVEAU 3)";
                break;
            case 4:
                $qualificationTitle = "BAC : BP, BT, bac pro ou techno (NIVEAU 4)";
                break;
            case 5:
                $qualificationTitle = "BAC+2 : DEUG, BT, DUT... (NIVEAU 5)";
                break;
            case 6:
                $qualificationTitle = "BAC+3 ou 4 : licence, master 1, maîtrise (NIVEAU 6)";
                break;
            case 7:
                $qualificationTitle = "BAC+5 : grade master, DEA, DESS, ingénieur... (NIVEAU 7)";
                break;
            case 8:
                $qualificationTitle = "BAC+8 : doctorat... (NIVEAU 8)";
                break;
        }
        return $qualificationTitle;
    }

    /**
     * @param array|null $arr
     * @return array
     */
    public static function removeSpacesInKeys(?array $arr): array
    {
        if ($arr != null && sizeof($arr) > 0) {
            $arr = array_combine(
                array_map(
                    function ($str) {
                        return str_replace(" ", "_", $str);
                    },
                    array_keys($arr)
                ),
                array_values($arr)
            );
        }
        return $arr;
    }

    /**
     * @param array $array
     * @param string $key
     * @return array
     */
    public static function findDuplicatesOnKeyInArray(array $array, string $key): array
    {
        return array_keys(array_diff(array_count_values(array_column($array, $key)), [1]));
    }

    public static function convertTtcToHt(float $priceTTC, Organism $organism): float
    {
        $rateVat = $organism->getVat();
        if ($rateVat == 20) {
            $convertNumber = $priceTTC / 1.2;
            $priceHt = round($convertNumber * 100) / 100;
        } else if ($rateVat == 5.5) {
            $convertNumber = ($priceTTC * 100) / 1.055;
            $priceHt = round($convertNumber * 100) / 100;
        } else {
            $priceHt = $priceTTC;
        }
        return $priceHt;
    }

    /**
     * @param int|float|null $num
     * @param int $precision
     * @param int $mode
     * @return ?float
     */
    public static function properRound($num, int $precision = 0, int $mode = PHP_ROUND_HALF_UP): ?float
    {
        // Wrapper arround round that returns null if null is provided
        // while native round returns 0 when null is provided, which is bad
        if (!isset($num)) {
            return null;
        }
        return round($num, $precision, $mode);
    }

    /**
     * @param string $text
     * @param int $length
     * @return string
     */
    public static function truncate(string $text, int $length): string
    {
        if ($length <= 3) {
            $truncatedString = substr($text, 0, $length);
        } else if (strlen($text) <= $length) {
            $truncatedString = $text;
        } else {
            $truncatedString = substr($text, 0, $length - 3) . '...';
        }
        return $truncatedString;
    }

    /**
     * @param string $jwt
     * @return array
     */
    public static function unsecureDecodeJWT(string $jwt): array
    {
        // THIS IS UNSECURE
        // For secure way, use :
        // (array)JWT::decode($jwt, $key, array('RS256'))
        list($header, $payload, $signature) = explode('.', $jwt);
        $jsonToken = base64_decode($payload);
        return json_decode($jsonToken, true);
    }

    /**
     * Very naïve: does not allow callback, only first level key, does not support missing key
     * @param array $array
     * @param string $key
     * @return array
     */
    public static function groupBy(array $array, string $key): array
    {
        $result = array();
        foreach ($array as $item) {
            $result[$item[$key]][] = $item;
        }
        return $result;
    }

    /**
     * @param array|null $result
     * @param Closure $param
     * @return array
     */
    public static function array_find_returns_array(?array $result, Closure $param): array
    {
        foreach ($result as $x) {
            if (call_user_func($param, $x) === true)
                return [$x]; // result
        }
        return []; // not found
    }

    /**
     * @param array $array
     * @param callable $predicate
     * @return mixed|null
     */
    public static function array_find(array $array, callable $predicate)
    {
        foreach ($array as $key => $value) {
            if ($predicate($value, $key)) {
                return $value;
            }
        }
        return null;
    }

    /**
     * Otherwise available in php 8.4
     * @param array $array
     * @param callable $predicate
     * @return bool
     */
    public static function array_all(array $array, callable $predicate): bool
    {
        foreach ($array as $item) {
            if (!$predicate($item)) {
                return false;
            }
        }
        return true;
    }

    /**
     * Otherwise available in php 8.4
     * @param array $array
     * @param callable $predicate
     * @return bool
     */
    public static function array_any(array $array, callable $predicate): bool
    {
        foreach ($array as $item) {
            if ($predicate($item)) {
                return true;
            }
        }
        return false;
    }

    /**
     * @param Organism $organism
     * @return array
     */
    public static function retrieveWebsite(Organism $organism): array
    {
        $urls = [];
        if (count($organism->getUrls()) > 0) {
            $urls = $organism->getUrls();
        }

        $siret = $organism->getSiret();
        if ($siret) {
            global $kernel;
            if ($kernel) {
                try {
                    $automatorApiService = $kernel->getContainer()->get('App\Service\DataProviders\AutomatorApiService');
                    $apiUrls = $automatorApiService->getOrganismUrls($organism);
                    foreach ($apiUrls as $apiUrl) {
                        if (!in_array($apiUrl, $urls)) {
                            $urls[] = $apiUrl;
                        }
                    }
                } catch (Exception $e) {
                    throw new WedofConnectionException("Enable to get result");                }
            }
        }

        if ($organism->getOwnedBy()) {
            $userEmail = $organism->getOwnedBy()->getEmail();
            $fullDomain = explode('@', $userEmail)[1];
            $domain = explode('.', $fullDomain)[0];
            $excludedDomainEmails = ['orange', 'gmail', 'live', 'laposte', 'wanadoo', 'outlook', 'hotmail', 'yahoo', 'free', 'icloud', 'yopmail', 'bbox', 'neuf', 'aol', 'gmx', 'mail'];
            if (!in_array($domain, $excludedDomainEmails)) {
                $websiteUrl = 'https://' . $fullDomain;
                if (!in_array($websiteUrl, $urls)) {
                    $urls[] = $websiteUrl;
                }
            }
        }
        return $urls;
    }

    /**
     * @param AbstractTaggable $object
     * @return array
     */
    public static function tagsToArray(AbstractTaggable $object): array
    {
        $tags = [];
        if (!$object->getTags()->isEmpty()) {
            /** @var Tag $tag */
            foreach ($object->getTags() as $tag) {
                $tags[] = $tag->getName();
            }
        }
        return $tags;
    }

    /**
     * @param string $separator
     * @param string $string
     * @return array
     */
    public static function explodeLast(string $separator, string $string): array
    {
        $pos = strrpos($string, $separator);
        if ($pos === false) {
            return [$string];
        }
        return [
            substr($string, 0, $pos),
            substr($string, $pos + strlen($separator))
        ];
    }
}
