<?php

namespace App\Library\utils;

use Psr\Log\LoggerInterface;

class EchoLogger implements LoggerInterface
{
    private $html;

    public function __construct($html = false)
    {
        $this->html = $html;
        header('X-Accel-Buffering: no');
        ob_implicit_flush(true);
        ob_end_flush();
    }

    public function emergency($message, array $context = array())
    {
        echo "[" . date("Y-m-d H:i:s") . "][emergency] $message";
        echo $this->html ? "<br/>" : "\n";
    }

    public function alert($message, array $context = array())
    {
        echo "[" . date("Y-m-d H:i:s") . "][alert] $message";
        echo $this->html ? "<br/>" : "\n";
    }

    public function critical($message, array $context = array())
    {
        echo "[" . date("Y-m-d H:i:s") . "][critical] $message";
        echo $this->html ? "<br/>" : "\n";
    }

    public function error($message, array $context = array())
    {
        echo "[" . date("Y-m-d H:i:s") . "][error] $message";
        echo $this->html ? "<br/>" : "\n";
    }

    public function warning($message, array $context = array())
    {
        echo "[" . date("Y-m-d H:i:s") . "][warning] $message";
        echo $this->html ? "<br/>" : "\n";
    }

    public function notice($message, array $context = array())
    {
        echo "[" . date("Y-m-d H:i:s") . "][notice] $message";
        echo $this->html ? "<br/>" : "\n";
    }

    public function info($message, array $context = array())
    {
        echo "[" . date("Y-m-d H:i:s") . "][info] $message";
        echo $this->html ? "<br/>" : "\n";
    }

    public function debug($message, array $context = array())
    {
        echo "[" . date("Y-m-d H:i:s") . "][debug] $message";
        echo $this->html ? "<br/>" : "\n";
    }

    public function log($level, $message, array $context = array())
    {
        echo "[" . date("Y-m-d H:i:s") . "][log][$level] $message";
        echo $this->html ? "<br/>" : "\n";
    }
}
