.cpf-alert {
    display: block;
    margin: auto;
    max-width: 320px
}

.cpf-alert {
    align-items: center;
    border-radius: 4px;
    border-style: solid;
    border-width: 1px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 16px;
    position: relative;
    text-align: center
}

.cpf-alert mat-icon {
    font-size: 24px;
    height: 1em;
    line-height: 1em;
    width: 1em
}

.cpf-alert:before, .cpf-alert mat-icon {
    left: 50%;
    position: absolute;
    top: 0;
    transform: translate(-50%, -50%)
}

.cpf-alert:before {
    background-color: #fff;
    border-radius: 24px;
    content: "";
    height: 19px;
    width: 19px
}

.cpf-alert p {
    margin: 0 !important
}

.cpf-alert.cpf-alert-info {
    background-color: #e5f0fa;
    border-color: #0077d4;
    color: #0077d4
}

.cpf-alert.cpf-alert-warning {
    background-color: #fdf1e6;
    border-color: #be5901;
    color: #be5901
}

.cpf-alert.cpf-alert-error {
    background-color: #fbebeb;
    border-color: #d63c3c;
    color: #d63c3c
}

.cpf-alert.cpf-alert-success {
    background-color: #eaf0ea;
    border-color: #16591d;
    color: #16591d
}
