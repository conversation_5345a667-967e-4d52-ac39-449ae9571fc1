<?php


namespace App\Library;

use App\Entity\Certification;
use App\Entity\Organism;
use App\Library\utils\enums\CertificationFolderStates;
use App\Library\utils\enums\DataProviders;
use App\Repository\CertificationFolderRepository;
use App\Repository\TrainingActionRepository;
use App\Service\EvaluationService;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use JMS\Serializer\Annotation as Serializer;

/**
 * @Serializer\ExclusionPolicy("ALL")
 */
class CertificationStatistics
{
    private Certification $certification;
    private CertificationFolderRepository $certificationFolderRepository;
    private ?Organism $organism;
    private ?EvaluationService $evaluationService;
    private ?TrainingActionRepository $trainingActionRepository;

    /**
     * @var bool
     */
    private bool $isDataRetrieved = false;

    /**
     * @var int
     * @Serializer\Expose
     * @Serializer\Accessor(getter="getFoldersTotal")
     */
    private int $foldersTotal;

    /**
     * @var array
     * @Serializer\Expose
     * @Serializer\Accessor(getter="getCountByState")
     */
    private array $countByState;

    /**
     * @var float
     * @Serializer\Expose
     * @Serializer\Accessor(getter="getSuccessRate")
     */
    private float $successRate;

    /**
     * @var float
     * @Serializer\Expose
     * @Serializer\Accessor(getter="getTakeRate")
     */
    private float $takeRate;

    /**
     * @var array
     * @Serializer\Expose
     * @Serializer\Accessor(getter="getTakeRateAfterDelay")
     */
    private array $takeRateAfterDelay;

    /**
     * @var float
     * @Serializer\Expose
     * @Serializer\Accessor(getter="getAbortRate")
     */
    private float $abortRate;

    /**
     * @var array
     * @Serializer\Expose
     * @Serializer\Accessor(getter="getAbortRateExploded")
     */
    private array $abortExploded;

    /**
     * @var array|null
     * @Serializer\Expose
     * @Serializer\Accessor(getter="getEvaluation")
     */
    private ?array $evaluation;

    /**
     * @var array|null
     * @Serializer\Expose
     * @Serializer\Accessor(getter="getPricing")
     */
    private ?array $pricing;

    /**
     * @var array|null
     * @Serializer\Expose
     * @Serializer\Accessor(getter="getDuration")
     */
    private ?array $duration;

    /**
     * @var array
     */
    private array $toTakeAfterDelay;

    /**
     * @var array|null
     * @Serializer\Expose
     * @Serializer\Accessor(getter="getCountRegistrationFolderByType")
     */
    private array $countRegistrationFolderByType;

    /**
     * @param Certification $certification
     * @param CertificationFolderRepository $certificationFolderRepository
     * @param Organism|null $organism
     * @param EvaluationService|null $evaluationService
     * @param TrainingActionRepository|null $trainingActionRepository
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function __construct(Certification $certification,
                                CertificationFolderRepository $certificationFolderRepository,
                                Organism $organism = null,
                                EvaluationService $evaluationService = null,
                                TrainingActionRepository $trainingActionRepository = null)
    {
        $this->certification = $certification;
        $this->organism = $organism;
        $this->evaluationService = $evaluationService;
        $this->certificationFolderRepository = $certificationFolderRepository;
        $this->trainingActionRepository = $trainingActionRepository;
        $this->retrieveData();
    }

    /**
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    private function retrieveData(): void
    {
        if (!$this->isDataRetrieved) {
            $siret = $this->organism ? $this->organism->getSiret() : null;
            $this->foldersTotal = $this->certificationFolderRepository->countForCertification($this->certification, $siret);
            $countByStatePairs = $this->certificationFolderRepository->countForCertificationByState($this->certification, $siret);

            $countByState = [];
            foreach ($countByStatePairs as $pair) {
                $countByState[$pair['state']] = $pair['count'];
            }
            foreach (CertificationFolderStates::valuesStates() as $certificationFolderState) {
                $stateName = $certificationFolderState->getValue();
                if (!isset($countByState[$stateName]) && $stateName !== 'all') {
                    $countByState[$stateName] = 0;
                }
            }
            $this->toTakeAfterDelay = [];
            foreach (['1Month', '3Months', '6Months'] as $delay) {
                $this->toTakeAfterDelay[$delay] = $this->certificationFolderRepository->countToTakeForCertificationAfterDelay($this->certification, $delay, $siret);
            }
            $this->countByState = $countByState;

            $abortExploded = [
                "all" => $countByState[CertificationFolderStates::ABORTED()->getValue()],
                "beforeTraining" => 0,
                "inTraining" => 0,
                "afterTraining" => 0,
                "undetermined" => 0
            ];

            if ($abortExploded["all"] > 0) {
                $abortExploded = $this->certificationFolderRepository->countForCertificationByStateAbortedExploded($this->certification, $siret);
                $abortExploded['undetermined'] = $countByState[CertificationFolderStates::ABORTED()->getValue()]
                    - ($abortExploded['beforeTraining']
                        + $abortExploded['inTraining']
                        + $abortExploded['afterTraining']);
            }
            $this->abortExploded = $abortExploded;
            $this->countRegistrationFolderByType = $this->certificationFolderRepository->findAllForCertificationAndDataProvider($this->certification, $this->organism ? $this->organism : null);
            $this->isDataRetrieved = true;
        }
    }

    /**
     * @return int
     */
    public function getFoldersTotal(): int
    {
        return $this->foldersTotal;
    }

    /**
     * @return array
     */
    public function getCountByState(): array
    {
        return $this->countByState;
    }


    /**
     * @return float
     */
    public function getSuccessRate(): ?float
    {
        $certificationPassedCount = $this->countByState[CertificationFolderStates::SUCCESS()->getValue()] +
            $this->countByState[CertificationFolderStates::FAILED()->getValue()] +
            $this->countByState[CertificationFolderStates::TO_RETAKE()->getValue()];
        return $certificationPassedCount > 0 ? $this->countByState[CertificationFolderStates::SUCCESS()->getValue()] / $certificationPassedCount * 100 : null;
    }

    /**
     * @return float
     */
    public function getTakeRate(): ?float
    {
        $toTakeCount = $this->countByState[CertificationFolderStates::TO_TAKE()->getValue()];
        return $this->getTakeRateFromToTakeCount($toTakeCount);
    }

    /**
     * @return array
     */
    public function getTakeRateAfterDelay(): array
    {
        $takeRateExploded = [];
        foreach (['1Month', '3Months', '6Months'] as $delay) {
            $toTakeCount = $this->toTakeAfterDelay[$delay];
            $takeRateExploded[$delay] = $this->getTakeRateFromToTakeCount($toTakeCount);
        }
        return $takeRateExploded;
    }

    /**
     * @return float
     */
    public function getAbortRate(): ?float
    {
        return $this->foldersTotal > 0 ? $this->countByState[CertificationFolderStates::ABORTED()->getValue()] / $this->foldersTotal * 100 : null;
    }

    /**
     * @return array
     */
    public function getAbortRateExploded(): array
    {
        $all = $this->abortExploded['all'];
        return [
            'beforeTrainingRate' => $this->abortExploded['beforeTraining'] && $all ? (($this->abortExploded['beforeTraining'] * 100) / $all) : 0,
            'inTrainingRate' => $this->abortExploded['inTraining'] && $all ? (($this->abortExploded['inTraining'] * 100) / $all) : 0,
            'afterTrainingRate' => $this->abortExploded['afterTraining'] && $all ? (($this->abortExploded['afterTraining'] * 100) / $all) : 0,
            'undeterminedRate' => $this->abortExploded['undetermined'] && $all ? (($this->abortExploded['undetermined'] * 100) / $all) : 0,
            'beforeTrainingCount' => $this->abortExploded['beforeTraining'],
            'inTrainingCount' => $this->abortExploded['inTraining'],
            'afterTrainingCount' => $this->abortExploded['afterTraining'],
            'undeterminedCount' => $this->abortExploded['undetermined'],
            'count' => $all
        ];
    }

    /**
     * @return array|null
     */
    public function getEvaluation(): ?array
    {
        if (!$this->organism) {
            return null;
        }
        $evaluationData = $this->evaluationService->summarizeForOrganismAndCertification($this->organism, $this->certification);
        if (empty($evaluationData)) {
            return null;
        } else {
            $finishedTraining = $this->countByState[CertificationFolderStates::TO_TAKE()->getValue()] +
                $this->countByState[CertificationFolderStates::TO_CONTROL()->getValue()] +
                $this->countByState[CertificationFolderStates::SUCCESS()->getValue()] +
                $this->countByState[CertificationFolderStates::FAILED()->getValue()] +
                $this->countByState[CertificationFolderStates::TO_RETAKE()->getValue()];
            $evaluationData['reviewRate'] = $finishedTraining > 0 ? $evaluationData['reviewCount'] / $finishedTraining * 100 : null;
        }
        return $evaluationData;
    }

    /**
     * @return array|null
     */
    public function getPricing(): ?array
    {
        if (!$this->organism) {
            return null;
        }
        return $this->trainingActionRepository->getPricingStats($this->certification, $this->organism->getSiret());
    }

    /**
     * @return array|null
     */
    public function getDuration(): ?array
    {
        if (!$this->organism) {
            return null;
        }
        return $this->trainingActionRepository->getDurationStats($this->certification, $this->organism->getSiret());
    }

    /**
     * @param int $toTakeCount
     * @return float|null
     */
    private function getTakeRateFromToTakeCount(int $toTakeCount): ?float
    {
        $takenCount = $this->countByState[CertificationFolderStates::TO_CONTROL()->getValue()] +
            $this->countByState[CertificationFolderStates::SUCCESS()->getValue()] +
            $this->countByState[CertificationFolderStates::FAILED()->getValue()] +
            $this->countByState[CertificationFolderStates::TO_RETAKE()->getValue()];

        $totalCount = $takenCount + $toTakeCount + $this->getAbortRateExploded()['afterTrainingCount'];

        return $totalCount > 0 ? $takenCount / $totalCount * 100 : null;
    }

    public function getCountRegistrationFolderByType(): ?array
    {
        $countByRfState = [];
        $dataProviders = [
            DataProviders::CPF()->getValue(),
            DataProviders::INDIVIDUAL()->getValue(),
            DataProviders::POLE_EMPLOI()->getValue(),
            DataProviders::COMPANY()->getValue(),
            DataProviders::OPCO()->getValue(),
            DataProviders::KAIROS_AIF()->getValue(),
            'none'
        ];
        foreach ($this->countRegistrationFolderByType as $pair) {
            $countByRfState[$pair['type'] ?? "none"] = $pair['count'];
        }
        foreach ($dataProviders as $dataProvider) {
            if (!array_key_exists($dataProvider, $countByRfState)) {
                $countByRfState[$dataProvider] = 0;
            }
        }
        return $countByRfState;
    }
}
