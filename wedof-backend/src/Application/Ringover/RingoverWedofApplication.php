<?php
// src/Event/Application/ApplicationEventsSubscriber.php
namespace App\Application\Ringover;

use App\Application\WedofApplication;
use App\Entity\Application;
use App\Entity\Subscription;
use App\Event\WedofNotifyEvents;
use App\Exception\WedofBadRequestHttpException;
use App\Library\Notifier\WedofNotifierTransport;
use App\Library\utils\enums\ActivityTypes;
use App\Service\ActivityService;
use App\Service\ApplicationService;
use App\Service\OrganismService;
use DateTime;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Exception;
use Symfony\Component\HttpClient\CurlHttpClient;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Notifier\Exception\TransportExceptionInterface;
use Symfony\Component\Notifier\Message\SmsMessage;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Throwable;

class RingoverWedofApplication extends WedofApplication
{
    protected static string $APP_ID = "ringover";
    protected static array $SCOPES = [""]; // used when oauth2 app auth
    protected static array $METHODS = ['phoneNumbers', 'test']; // custom methods called from front

    protected WedofNotifierTransport $transport;
    private OrganismService $organismService;

    /**
     * RingoverWedofApplication constructor.
     * @param ActivityService $activityService
     * @param ApplicationService $applicationService
     * @param OrganismService $organismService
     * @param WedofNotifierTransport $transport
     */
    public function __construct(ActivityService $activityService, ApplicationService $applicationService, OrganismService $organismService, WedofNotifierTransport $transport)
    {
        parent::__construct($applicationService, $activityService);
        $this->transport = $transport;
        $this->organismService = $organismService;
    }

    /**
     * @Route("/app/ringover/{siret}", name="webhook-ringover", methods={"POST"})
     * @param Request $request
     * @param string $siret
     * @return void
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws \Doctrine\ORM\ORMException
     */
    public function webhook(Request $request, string $siret): Response
    {
        $organism = $this->organismService->getBySiret($siret);
        if ($this->isEnabled($organism)) {
            $body = json_decode($request->getContent(), true);
            switch ($body['event']) {
                case 'hangup':
                    //todo
                    break;
                case 'record_available':
                    //todo bis
                    break;
                case 'missed':
                    //todo 2 bis
                    break;
                case 'voicemail_available':
                    //todo 3 bis
                    break;
                case 'sent':
                    //todo 4 bis
                    break;
                case 'received':
                    //todo 5 bis
                    break;
                default:
                    return new Response("", 404);
            }
        } else {
            return new Response("", 404);
        }
        return new Response("", 200);
    }

    /**
     * @param WedofNotifyEvents $event
     * @throws ORMException
     * @throws TransportExceptionInterface
     * @throws NonUniqueResultException
     * @throws \Doctrine\ORM\ORMException|Throwable
     */
    public function send(WedofNotifyEvents $event)
    {
        $application = $this->getApplication($event->getEntity()->getOrganism()); // TODO manage case RF / OF (different organism source)
        if ($application) {
            $phoneNumber = $application->getMetadata()['phoneNumber'] ?? null;
            $apiKey = $application->getMetadata()['apiKey'] ?? null;
            if ($phoneNumber && $apiKey) {
                $dsn = sprintf('ringover://%s@default?from=%s', $apiKey, $phoneNumber);
                $transport = $this->transport->fromString($dsn);
                $sms = new SmsMessage(
                    $event->getTo(),
                    $event->getContent()
                );
                if ($transport->send($sms)) {
                    $this->activityService->create(
                        [
                            'origin' => $application->getId(),
                            'title' => "SMS envoyé",
                            'description' => $event->getContent(),
                            'type' => ActivityTypes::SMS(),
                            'eventTime' => new DateTime()
                        ],
                        null,
                        $event->getEntity()
                    );
                }
            }
        }
    }

    /**
     * @param Application $application
     */
    public function enabled(Application $application): void
    {
        // TODO: Implement onEnable() method.
    }

    /**
     * @param Application $application
     */
    public function disabled(Application $application): void
    {
        // TODO: Implement onDisable() method.
    }

    /**
     * @param Application $application
     * @param array $data
     * @return bool[]
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws \Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface
     */
    public function phoneNumbers(Application $application, array $data): array
    {
        $numbers = [];
        $client = new CurlHttpClient();
        $response = $client->request('GET', 'https://public-api.ringover.com/v2/numbers', [
            'headers' => [
                'Accept' => 'application/json',
                'Authorization' => $data['apiKey'],
                'Content-Type' => 'application/json'
            ]
        ]);
        try {
            $json = json_decode($response->getContent());
            if ($json->list_count) {
                $numbers = array_column(array_column(array_values(array_filter($json->list, function ($number) {
                    return $number->is_sms;
                })), 'format'), 'e164');
            }
            return $numbers;
        } catch (Exception $e) {
            throw new WedofBadRequestHttpException();
        }
    }

    /**
     * @param Application $application
     * @param array $previousMetadata
     */
    public function onUpdateMetadata(Application $application, array $previousMetadata): void
    {
    }

    /**
     * @param Application $application
     * @throws Throwable
     */
    public function onNewOAuth(Application $application): void
    {
        // TODO: Implement onNewOAuth() method.
    }

    /**
     * @param Application $application
     */
    public function onRefreshOAuth(Application $application): void
    {
        // TODO: Implement onRefreshOAuth() method.
    }

    public function dispatch(object $event)
    {
        // TODO: Implement dispatch() method.
    }

    /**
     * @return array
     */
    public static function getApplicationSubscribedEvents(): array
    {
        return ['sendSmsMessage' => ['send']];
    }

    public function beforeEnable(Application $application): void
    {
        // TODO: Implement beforeEnable() method.
    }

    public function beforeDisable(Application $application): void
    {
        // TODO: Implement beforeDisable() method.
    }

    static public function getAllowedSubscriptionTypes(Subscription $subscription): array
    {
        return ['training' => [], 'certifier' => []];
    }

    public function show(Application $application): void
    {
        // TODO: Implement onShow() method.
    }
}
