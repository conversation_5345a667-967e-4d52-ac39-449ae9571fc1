<?php
// src/Application/Workflow/WorkflowWedofApplication.php
namespace App\Application\Workflow;

use App\Application\WedofApplication;
use App\Entity\Application;
use App\Entity\Subscription;
use App\Entity\User;
use App\Exception\WedofBadRequestHttpException;
use App\Library\utils\enums\ApplicationStates;
use App\Library\utils\enums\SubscriptionTrainingTypes;
use App\Library\utils\Tools;
use App\Service\ActivityService;
use App\Service\ApplicationService;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Firebase\JWT\JWT;
use RuntimeException;
use Symfony\Component\HttpClient\CurlHttpClient;
use Symfony\Component\Security\Core\Security;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;

class WorkflowWedofApplication extends WedofApplication
{
    protected static string $APP_ID = "workflow";
    protected static array $METHODS = ["auth"];
    private Security $security;
    private EntityManagerInterface $entityManager;
    private const INACTIVE = 'INACTIVE';
    private const ACTIVE = 'ACTIVE';

    protected static array $PRICES = [
        'prod' => [
            'month' => "price_1PQY8rLLgg6l7qY92ZFwn52T",
            "year" => "price_1PPYVHLLgg6l7qY9b2F2i3Cc",
            'trial' => "+ 15 days"
        ],
        'test' => [
            'month' => "price_1QXjKtLLgg6l7qY9pvXaAWGp",
            "year" => "price_1QXjLMLLgg6l7qY9jVfKkB6i"
        ],
        'dev' => [
            'month' => "price_1QXjKtLLgg6l7qY9pvXaAWGp",
            "year" => "price_1QXjLMLLgg6l7qY9jVfKkB6i",
            'trial' => "+ 15 days"
        ]
    ];

    public function __construct(ApplicationService $applicationService, ActivityService $activityService, Security $security, EntityManagerInterface $entityManager)
    {
        parent::__construct($applicationService, $activityService);
        $this->security = $security;
        $this->entityManager = $entityManager;
    }

    static public function getAllowedSubscriptionTypes(Subscription $subscription): array
    {
        $hasWorkflowEnabled = !empty(array_filter($subscription->getOrganism()->getApplications()->toArray(), fn(Application $app) => $app->getAppId() === self::$APP_ID && $app->getEnabled() === true));
        return [
            'training' => $hasWorkflowEnabled ? [$subscription->getTrainingType()] : SubscriptionTrainingTypes::getPaidTrainingTypesForApps(),
            'certifier' => $hasWorkflowEnabled ? [$subscription->getCertifierType()] : ($subscription->isAllowCertifierPlus() ? [$subscription->getCertifierType()] : [])
        ];
    }

    /**
     * @inheritDoc
     */
    public function enabled(Application $application): void
    {
        //real enabled is done in a workflow
        //https://automator.wedof.fr/workflow/Y2U2s0voXrySo6Db
        // TODO: Implement enabled() method.
    }

    /**
     * @inheritDoc
     */
    public function disabled(Application $application): void
    {
        //real disabled is done in a workflow
        //https://automator.wedof.fr/workflow/Y2U2s0voXrySo6Db
    }

    /**
     * @inheritDoc
     */
    public function beforeEnable(Application $application): void
    {
        //saved in service after
        if ($application->getState() == ApplicationStates::DISABLED()) {
            $application->setState($application->getEndDate() == null ? ApplicationStates::PENDING_ENABLE_TRIAL() : ApplicationStates::PENDING_ENABLE());
        }
    }

    /**
     * @inheritDoc
     */
    public function beforeDisable(Application $application): void
    {
        $metadata = $application->getMetadata();
        if ($metadata) {
            unset($metadata['projectId']);
            unset($metadata['token']);
            $application->setMetadata($metadata);
        }
    }

    /**
     * @inheritDoc
     */
    public function onNewOAuth(Application $application): void
    {
        // TODO: Implement onNewOAuth() method.
    }

    /**
     * @inheritDoc
     */
    public function onRefreshOAuth(Application $application): void
    {
        // TODO: Implement onRefreshOAuth() method.
    }

    /**
     * @inheritDoc
     */
    public function onUpdateMetadata(Application $application, array $previousMetadata): void
    {
        // TODO: Implement onUpdateMetadata() method.
    }

    /**
     * @inheritDoc
     */
    static public function getApplicationSubscribedEvents(): array
    {
        return [];
    }

    public function show(Application $application): void
    {
        // TODO: Implement onShow() method.
    }

    /**
     * @param Application $application
     * @return array
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    public function auth(Application $application): array
    {
        // Use the same pattern as ProcessusMetierService.getAuthDataForApplication
        $authData = $this->getAuthDataForApplication($application);
        if (!$authData) {
            throw new WedofBadRequestHttpException("Unable to authenticate user");
        }

        try {
            // Add platformId to the JWT payload - Activepieces expects this field
            $jwtPayload = [
                'projectId' => $authData['projectId'],
                'token' => $authData['token'],
                'platformId' => $authData['projectId'], // Use projectId as platformId for Activepieces compatibility
                'iat' => (new DateTime("now"))->getTimestamp(),
                'exp' => (new DateTime("now"))->modify("+ 1 day")->getTimestamp(),
                'iss' => 'wedof'
            ];

            $jwt = JWT::encode($jwtPayload, WorkflowWedofApplication::$APP_ID);
            return ['jwt' => $jwt];
        } catch (Exception $e) {
            throw new WedofBadRequestHttpException("Unable to auth user");
        }
    }

    /**
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     */
    private function getUsers(Application $application, string $auth): array
    {
        $client = new CurlHttpClient();
        $response = $client->request('GET', $this->getBaseUrl($application) . "/api/v1/users", [
            'headers' => [
                "Authorization" => 'Bearer ' . $auth,
                'Accept' => 'application/json',
                'Content-Type' => 'application/json'
            ]
        ]);
        return json_decode($response->getContent(), true)['data'];
    }

    /**
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     */
    private function signin(Application $application, string $username): ?array
    {
        $client = new CurlHttpClient();
        $response = $client->request('POST', $this->getBaseUrl($application) . "/api/v1/authentication/sign-in", [
            'headers' => [
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ],
            'body' => json_encode([
                "email" => $username,
                "password" => Tools::getEnvValue('WORKFLOW_PASSWORD')
            ])
        ]);
        return $response->getStatusCode() == 200 ? json_decode($response->getContent(), true) : null;
    }

    /**
     * @param Application $application
     * @return string
     */
    private function getBaseUrl(Application $application): string
    {
        return 'https://' . $application->getOrganism()->getSubDomain() . "." . Tools::getEnvValue('WORKFLOW_DOMAIN_SUFFIX');
    }

    /**
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     * @throws Exception
     */
    private function createUser(Application $application, User $user, string $projectId, string $adminToken): ?array
    {
        $client = new CurlHttpClient();
        if ($adminToken) {
            //create invitation
            $response = $client->request('POST', $this->getBaseUrl($application) . "/api/v1/user-invitations", [
                'headers' => [
                    "Authorization" => 'Bearer ' . $adminToken,
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json',
                ],
                'body' => json_encode([
                    "email" => $user->getEmail(),
                    "type" => "PROJECT",
                    "projectRole" => "EDITOR",
                    "projectId" => $projectId
                ])
            ]);
            //validate invitation
            $invitation = json_decode($response->getContent(), true);
            $jwt = JWT::encode([
                "id" => $invitation['id'],
                "iat" => (new DateTime("now"))->getTimestamp(),
                "exp" => (new DateTime("now"))->modify("+ 1 day")->getTimestamp(),
                "iss" => "activepieces"
            ], Tools::getEnvValue("WORKFLOW_JWT_SECRET"));
            $client->request('POST', $this->getBaseUrl($application) . "/api/v1/user-invitations/accept", [
                'headers' => [
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json',
                ],
                'body' => json_encode(["invitationToken" => $jwt])
            ]);
            if (!$response->getContent()) {
                throw new RuntimeException();
            }
        }
        //create user
        $response = $client->request('POST', $this->getBaseUrl($application) . "/api/v1/authentication/sign-up", [
            'headers' => [
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ],
            'body' => json_encode([
                "email" => $user->getEmail(),
                "firstName" => $user->getFirstName(),
                "lastName" => $user->getLastName(),
                "newsLetter" => false,
                "password" => Tools::generateRandomString(6) . "B$1",
                "trackEvents" => true
            ])
        ]);
        $fullUserAuth = $response->getStatusCode() == 200 ? json_decode($response->getContent(), true) : null;
        //create email validation otp
        $response = $client->request('POST', $this->getBaseUrl($application) . "/api/v1/otp", [
            'headers' => [
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ],
            'body' => json_encode([
                "email" => $user->getEmail(),
                "type" => "EMAIL_VERIFICATION"
            ])
        ]);
        if ($response->getStatusCode() == 204) {
            //validate otp
            $response = $client->request('POST', $this->getBaseUrl($application) . "/api/v1/authn/local/verify-email", [
                'headers' => [
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json',
                ],
                'body' => json_encode([
                    "otp" => Tools::getEnvValue("WORKFLOW_OTP"),
                    "userId" => $fullUserAuth['id']
                ])
            ]);
            if ($response->getStatusCode() == 200) {
                return $fullUserAuth;
            }
        }
        return null;
    }

    /**
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     */
    private function activateUser(Application $application, string $adminToken, array $workflowUser): bool
    {
        $client = new CurlHttpClient();
        $response = $client->request('POST', $this->getBaseUrl($application) . "/api/v1/users/" . $workflowUser['id'], [
            'headers' => [
                "Authorization" => 'Bearer ' . $adminToken,
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ],
            'body' => json_encode([
                "status" => self::ACTIVE
            ])
        ]);
        return json_decode($response->getContent(), true)['status'] === self::ACTIVE;
    }

    /**
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     */
    private function desactivateUser(Application $application, string $adminToken, array $workflowUser): bool
    {
        $client = new CurlHttpClient();
        $response = $client->request('POST', $this->getBaseUrl($application) . "/api/v1/users/" . $workflowUser['id'], [
            'headers' => [
                "Authorization" => 'Bearer ' . $adminToken,
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ],
            'body' => json_encode([
                "status" => self::INACTIVE
            ])
        ]);
        return json_decode($response->getContent(), true)['status'] === self::INACTIVE;
    }

    /**
     * Get authentication data for application - follows ProcessusMetierService pattern exactly
     * This method implements the lazy loading pattern: check cache first, authenticate if needed
     *
     * @param Application $application
     * @return array|null
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    private function getAuthDataForApplication(Application $application): ?array
    {
        $metadata = $application->getMetadata();
        $cachedProjectId = $metadata['projectId'] ?? null;
        $cachedToken = $metadata['token'] ?? null;

        // If cached credentials exist, return them (lazy loading optimization)
        if ($cachedProjectId && $cachedToken) {
            // Log cache hit for debugging
            if (isset($this->logger)) {
                $this->logger->info('WorkflowWedofApplication: Using cached credentials', [
                    'organism_id' => $application->getOrganism()->getId(),
                    'has_project_id' => !empty($cachedProjectId),
                    'has_token' => !empty($cachedToken)
                ]);
            }

            return [
                'projectId' => $cachedProjectId,
                'token' => $cachedToken
            ];
        }

        // Log cache miss for debugging
        if (isset($this->logger)) {
            $this->logger->info('WorkflowWedofApplication: Cache miss, authenticating', [
                'organism_id' => $application->getOrganism()->getId()
            ]);
        }

        // If no cached credentials, authenticate and cache the results
        return $this->authenticateAndCacheWorkflow($application);
    }

    /**
     * Validate cached credentials by making a test API call
     * If credentials are invalid, clear cache and re-authenticate
     *
     * @param Application $application
     * @param array $authData
     * @return array|null
     */
    private function validateAndRefreshAuthData(Application $application, array $authData): ?array
    {
        // For now, we trust cached credentials
        // In the future, we could add a validation API call here
        // If validation fails, call $this->clearCachedCredentials($application)
        // and return $this->authenticateAndCacheWorkflow($application)
        return $authData;
    }

    /**
     * Authenticate and cache workflow credentials
     * Simplified version following ProcessusMetierService pattern exactly
     *
     * @param Application $application
     * @return array|null
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    private function authenticateAndCacheWorkflow(Application $application): ?array
    {
        if ($application->getAppId() !== 'workflow') {
            return null;
        }

        /** @var User $user */
        $user = $this->security->getUser();

        // Try user authentication first
        $userAuth = $this->signin($application, $user->getEmail());
        if (!empty($userAuth['token']) && !empty($userAuth['projectId'])) {
            $this->cacheWorkflowCredentials($application, $userAuth);
            return $userAuth;
        }

        // Fallback to admin authentication
        $adminAuth = $this->signin($application, Tools::getEnvValue('WORKFLOW_ADMIN_USERNAME'));
        if (!empty($adminAuth['token']) && !empty($adminAuth['projectId'])) {
            // For admin auth, we still need to handle user creation/activation
            $this->handleUserCreationIfNeeded($application, $user, $adminAuth);

            // Try user auth again after potential user creation
            $userAuth = $this->signin($application, $user->getEmail());
            if (!empty($userAuth['token']) && !empty($userAuth['projectId'])) {
                $this->cacheWorkflowCredentials($application, $userAuth);
                return $userAuth;
            }

            // If user auth still fails, cache admin auth as fallback
            $this->cacheWorkflowCredentials($application, $adminAuth);
            return $adminAuth;
        }

        return null;
    }

    /**
     * Handle user creation and activation if needed
     * Separated from main authentication flow for clarity
     *
     * @param Application $application
     * @param User $user
     * @param array $adminAuth
     * @return void
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    private function handleUserCreationIfNeeded(Application $application, User $user, array $adminAuth): void
    {
        try {
            $workflowUsers = $this->getUsers($application, $adminAuth['token']);
            $workflowUserKey = array_search($user->getEmail(), array_column($workflowUsers, 'email'));

            if ($workflowUserKey === false) {
                // User doesn't exist, create them
                $this->createUser($application, $user, $adminAuth['projectId'], $adminAuth['token']);
            } else if ($workflowUsers[$workflowUserKey]['status'] === self::INACTIVE) {
                // User exists but is inactive, activate them
                $this->activateUser($application, $adminAuth['token'], $workflowUsers[$workflowUserKey]);
            }
        } catch (Exception $e) {
            // If user creation/activation fails, we'll fallback to admin auth
            // This is acceptable for the lazy loading pattern
        }
    }

    /**
     * Cache workflow credentials in application metadata
     * Following the same pattern as ProcessusMetierService.cacheWorkflowCredentials()
     *
     * @param Application $application
     * @param array $authData
     * @return void
     */
    private function cacheWorkflowCredentials(Application $application, array $authData): void
    {
        $metadata = $application->getMetadata() ?? [];
        $metadata['projectId'] = $authData['projectId'];
        $metadata['token'] = $authData['token'];
        $application->setMetadata($metadata);
        $this->entityManager->persist($application);
        $this->entityManager->flush();
    }

    /**
     * Clear cached credentials from application metadata
     * Following the same pattern as ProcessusMetierService.clearCachedCredentials()
     *
     * @param Application $application
     * @return void
     */
    private function clearCachedCredentials(Application $application): void
    {
        $metadata = $application->getMetadata() ?? [];
        unset($metadata['projectId']);
        unset($metadata['token']);
        $application->setMetadata($metadata);
        $this->entityManager->persist($application);
        $this->entityManager->flush();
    }
}
