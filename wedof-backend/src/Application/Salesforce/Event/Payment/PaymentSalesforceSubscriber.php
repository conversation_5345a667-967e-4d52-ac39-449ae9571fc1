<?php


namespace App\Application\Salesforce\Event\Payment;


use App\Application\Salesforce\SalesforceWedofApplication;
use App\Entity\Webhook;
use App\Event\Payment\PaymentEvents;
use App\Message\SendWebhook;
use App\Service\AccessService;
use App\Service\ApplicationService;
use App\Service\WebhookService;
use Doctrine\Common\Collections\ArrayCollection;
use Exception;
use JMS\Serializer\SerializationContext;
use <PERSON>MS\Serializer\Serializer;
use JMS\Serializer\SerializerInterface;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Messenger\MessageBusInterface;

class PaymentSalesforceSubscriber implements EventSubscriberInterface, LoggerAwareInterface
{

    private LoggerInterface $logger;
    private WebhookService $webhookService;
    private MessageBusInterface $messageBus;
    private AccessService $accessService;
    private SerializerInterface $serializer;
    private ApplicationService $applicationService;

    public function __construct(WebhookService $webhookService, MessageBusInterface $messageBus, AccessService $accessService, SerializerInterface $serializer, ApplicationService $applicationService)
    {
        $this->webhookService = $webhookService;
        $this->messageBus = $messageBus;
        $this->accessService = $accessService;
        $this->serializer = $serializer;
        $this->applicationService = $applicationService;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    /**
     * @return array|string[]
     */
    public static function getSubscribedEvents(): array
    {
        return array(
            PaymentEvents::DEPOSIT_WAITING => 'sendSalesforceEventOnPayment',
            PaymentEvents::DEPOSIT_REJECTED => 'sendSalesforceEventOnPayment',
            PaymentEvents::DEPOSIT_ISSUED => 'sendSalesforceEventOnPayment',
            PaymentEvents::WAITING => 'sendSalesforceEventOnPayment',
            PaymentEvents::ISSUED => 'sendSalesforceEventOnPayment',
            PaymentEvents::REJECTED => 'sendSalesforceEventOnPayment',
        );
    }

    /**
     * @param PaymentEvents $event
     * @param string $eventName
     * @throws Exception
     */
    public function sendSalesforceEventOnPayment(PaymentEvents $event, string $eventName)
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        $payment = $event->getPayment();
        try {
            $this->logger->debug("[Salesforce Webhook Payment] " . self::class . " event $eventName");
            $organism = $payment->getOrganism();
            $webhooks = $this->webhookService->listByOrganismsAndEventAndType(new ArrayCollection([$organism]), $eventName, ['type' => 'salesforce']);
            if (sizeof($webhooks) > 0) {
                if (!$this->accessService->isApplicationAllowedAndEnabled(SalesforceWedofApplication::getAppId(), $organism)) {
                    $this->logger->error("[Error sendSalesforceEventOnPayment : Webhook Salesforce non envoyé car l'application Salesforce n'est pas active ou votre abonnement ne permet pas l'utilisation de Salesforce.");
                    return;
                }
                $application = $this->applicationService->getByOrganismAndAppId($organism, SalesforceWedofApplication::getAppId(), null, false);
                if (!isset($application->getMetadata()['sendPayments']) || $application->getMetadata()['sendPayments'] !== true) {
                    $this->logger->error("[Error sendSalesforceEventOnPayment : Webhook Salesforce non envoyé car l'option 'sendPayments' est désactivé.");
                    return;
                }

                $serializationContext = new SerializationContext();
                $serializationContext->setSerializeNull(true);
                $serializationContext->setGroups("Default");
                /** @var Serializer $serializer */
                $payload = $this->serializer->serialize($payment, 'json', $serializationContext);

                $this->logger->debug("[Salesforce Webhook Payment] " . sizeof($webhooks) . " to send");
                /** @var Webhook $webhook */
                foreach ($webhooks as $webhook) {
                    $message = new SendWebhook($webhook->getId(), $eventName, json_encode($payload), get_class($payment), $payment->getId());
                    $this->messageBus->dispatch($message);
                }
            } else {
                $this->logger->debug("[Salesforce Webhook Payment] no event to send");
            }
        } catch (Exception $e) {
            $this->logger->error("[Error sendSalesforceEventOnPayment " . $payment->getId() . "] at Line " . $e->getLine() . " " . $e->getFile() . " " . $e->getMessage());
        }
    }

}