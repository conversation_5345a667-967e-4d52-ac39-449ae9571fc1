<?php

namespace App\Application\Salesforce\Event\RegistrationFolder;

use App\Application\Salesforce\SalesforceWedofApplication;
use App\Entity\RegistrationFolder;
use App\Entity\Webhook;
use App\Event\RegistrationFolder\RegistrationFolderEvents;
use App\Message\SendWebhook;
use App\Service\AccessService;
use App\Service\WebhookService;
use Doctrine\Common\Collections\ArrayCollection;
use Exception;
use JMS\Serializer\SerializationContext;
use J<PERSON>\Serializer\SerializerInterface;
use J<PERSON>\Serializer\Serializer;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Messenger\Stamp\DelayStamp;

class RegistrationFolderSalesforceSubscriber implements EventSubscriberInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    private WebhookService $webhookService;
    private MessageBusInterface $messageBus;
    private AccessService $accessService;
    private SerializerInterface $serializer;

    public function __construct(WebhookService $webhookService, MessageBusInterface $messageBus, AccessService $accessService, SerializerInterface $serializer)
    {
        $this->webhookService = $webhookService;
        $this->messageBus = $messageBus;
        $this->accessService = $accessService;
        $this->serializer = $serializer;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    /**
     * @return array|string[]
     */
    public static function getSubscribedEvents(): array
    {
        return array(
            RegistrationFolderEvents::CREATED => 'sendSalesforceCreateEventOnRegistrationFolder',
            RegistrationFolderEvents::UPDATED => 'sendSalesforceUpdateEventOnRegistrationFolder'
        );
    }

    public function sendSalesforceCreateEventOnRegistrationFolder(RegistrationFolderEvents $event, string $eventName)
    {
        $this->sendSalesforceEventOnRegistrationFolder($event, $eventName);
    }

    public function sendSalesforceUpdateEventOnRegistrationFolder(RegistrationFolderEvents $event, string $eventName)
    {
        $delayInSeconds = 10;
        $this->sendSalesforceEventOnRegistrationFolder($event, $eventName, $delayInSeconds);
    }

    /**
     * @param RegistrationFolderEvents $event
     * @param string $eventName
     * @param int|null $delayInSeconds
     */
    public function sendSalesforceEventOnRegistrationFolder(RegistrationFolderEvents $event, string $eventName, int $delayInSeconds = null)
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        $registrationFolder = $event->getRegistrationFolder();
        try {
            $this->logger->debug("[Salesforce Webhook RegistrationFolder] " . self::class . " event $eventName");
            $organism = $registrationFolder->getOrganism();
            $webhooks = $this->webhookService->listByOrganismsAndEventAndType(new ArrayCollection([$organism]), $eventName, ['type' => 'salesforce']);
            if (sizeof($webhooks) > 0) {
                // la vérification d'accès s'effectue ici car on ne veut pas sauvegarder les deliveries non envoyés Salesforce car les utilisateurs n'ont pas la possibilité de les renvoyer via l'interface
                // si l'on veut sauvegarder les deliveries, supprimer ce code, le webhookservice fera le reste
                if (!$this->accessService->isApplicationAllowedAndEnabled(SalesforceWedofApplication::getAppId(), $organism)) {
                    $this->logger->error("[Error sendSalesforceEventOnRegistrationFolder : Webhook Salesforce non envoyé car l'application Salesforce n'est pas active ou votre abonnement ne permet pas l'utilisation de Salesforce.");
                    return;
                }
                $payload = self::serializeRegistrationFolder($registrationFolder);
                $this->logger->debug("[Salesforce Webhook RegistrationFolder] " . sizeof($webhooks) . " to send");
                /** @var Webhook $webhook */
                foreach ($webhooks as $webhook) {
                    $message = new SendWebhook($webhook->getId(), $eventName, $payload, get_class($registrationFolder), $registrationFolder->getExternalId());
                    if (isset($delayInSeconds)) {
                        $envelope = new Envelope($message, [
                            new DelayStamp($delayInSeconds * 1000)
                        ]);
                        $this->messageBus->dispatch($envelope);
                    } else {
                        $this->messageBus->dispatch($message);
                    }
                }
            } else {
                $this->logger->debug("[Salesforce Webhook RegistrationFolder] no event to send");
            }
        } catch (Exception $e) {
            $this->logger->error("[Error sendSalesforceEventOnRegistrationFolder " . $registrationFolder->getExternalId() . "] at Line " . $e->getLine() . " " . $e->getFile() . " " . $e->getMessage());
        }
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @return string
     */
    private function serializeRegistrationFolder(RegistrationFolder $registrationFolder): string
    {
        $serializationContext = new SerializationContext();
        $serializationContext->setSerializeNull(true);
        $serializationContext->setGroups("owner");
        /** @var Serializer $serializer */
        $serializer = $this->serializer;
        $registrationFolderArray = $serializer->toArray($registrationFolder, $serializationContext); // HACK to allow adding a custom property,
        // This is ugly, but the alternative is creating a custom serizalizatio group (e.g. add owner + salesforce group) and have a custom serialize subscriber just for that
        if ($registrationFolder->getProposal() && $registrationFolder->getProposal()->getParentProposal()) {
            $registrationFolderArray['parentProposalCode'] = $registrationFolder->getProposal()->getParentProposal()->getCode();
        }
        unset($registrationFolderArray['files']); // Avoid unecessary data + multiple send of same data (updateOn on files may change for no reason and cause a different payload)
        unset($registrationFolderArray['updatedOn']); // Avoid multiple send of same data (updateOn may change for no reason and cause a different payload)
        return json_encode($registrationFolderArray);
    }
}
