<?php
// src/Event/Application/SalesforceWedofApplication.php
namespace App\Application\Salesforce;

use App\Application\WedofApplication;
use App\Entity\Application;
use App\Entity\Subscription;
use App\Entity\Webhook;
use App\Exception\MissingAccessTokenException;
use App\Library\utils\enums\SubscriptionTrainingTypes;
use App\Service\ActivityService;
use App\Service\ApplicationService;
use App\Service\WebhookService;
use Doctrine\Common\Collections\ArrayCollection;
use Throwable;

class SalesforceWedofApplication extends WedofApplication
{
    protected static string $APP_ID = "salesforce";
    protected static array $SCOPES = ["api", "id", "lightning", "offline_access", "refresh_token"];
    protected static array $METHODS = ['status', 'updatePayment'];
    private WebhookService $webhookService;

    /**
     * SalesforceWedofApplication constructor.
     * @param ApplicationService $applicationService
     * @param ActivityService $activityService
     * @param WebhookService $webhookService
     */
    public function __construct(ApplicationService $applicationService, ActivityService $activityService, WebhookService $webhookService)
    {
        parent::__construct($applicationService, $activityService);
        $this->webhookService = $webhookService;
    }

    /**
     * @param Application $application
     * @return bool[]
     */
    public function status(Application $application): array
    {
        try {
            $webhooks = $this->webhookService->listByOrganismsAndEventAndType(new ArrayCollection([$application->getOrganism()]), 'registrationFolder.created', ['type' => self::getAppId()]);
            return ['status' => sizeof($webhooks) > 0];
        } catch (MissingAccessTokenException $e) {
            return ['status' => false];
        }
    }

    /**
     * @param Application $application
     * @param array $webhookData
     * @return array|null
     * @throws Throwable
     */
    public function updatePayment(Application $application, array $webhookData): ?array
    {
        $metadata = $application->getMetadata();
        $metadata['sendPayments'] = $webhookData['sendPayments'];
        $this->applicationService->updateMetadata($application, $metadata);

        $enabled = $metadata['sendPayments'];
        $webhooks = $this->webhookService->listByOrganismsAndEventAndType(new ArrayCollection([$application->getOrganism()]), 'payment.depositWaiting', ['type' => self::getAppId(), 'onlyEnabled' => false]);

        if ($webhooks->count() !== 0) {
            /** @var Webhook $webhook */
            $webhook = $webhooks[0];
            if ($webhook->getEnabled() != $enabled) {
                $this->webhookService->update($webhook, ['enabled' => $enabled]);
            }
        } else if ($enabled) {
            $instanceUrl = $application->getOauth2()['instance_url'];
            $webhookData = [
                'url' => "$instanceUrl/services/apexrest/wedof/payment",
                'secret' => 'XqWA7G9SDTA5JdN',
                'events' => ['payment.depositWaiting', 'payment.depositRejected', 'payment.depositIssued', 'payment.issued', 'payment.waiting', 'payment.rejected'],
                'type' => self::getAppId(),
                'options' => [
                    'useAccessToken' => true,
                    'version' => 2
                ]
            ];
            $this->webhookService->create($webhookData, $application->getOrganism());
        }

        return $application->getMetadata();
    }

    /**
     * @param Application $application
     */
    public function enabled(Application $application): void
    {
        // TODO: Implement onEnable() method.
    }

    /**
     * @param Application $application
     * @throws Throwable
     */
    public function disabled(Application $application): void
    {
        $eventsName = ['registrationFolder.created', 'payment.depositWaiting'];
        foreach ($eventsName as $eventName) {
            $webhooks = $this->webhookService->listByOrganismsAndEventAndType(new ArrayCollection([$application->getOrganism()]), $eventName, ['type' => self::getAppId()]);
            foreach ($webhooks as $webhook) {
                $this->webhookService->update($webhook, ['enabled' => false]);
            }
        }
    }

    /**
     * @param Application $application
     * @throws Throwable
     */
    public function onNewOAuth(Application $application): void
    {
        // Start by disabling the app as we have a new oauth
        $this->disabled($application);

        $webhooks = $this->webhookService->listByOrganismsAndEventAndType(new ArrayCollection([$application->getOrganism()]), 'registrationFolder.created', ['type' => self::getAppId(), 'onlyEnabled' => false]);
        if (count($webhooks) === 0) {
            $instanceUrl = $application->getOauth2()['instance_url'];
            $webhookData = [
                'url' => "$instanceUrl/services/apexrest/wedof/wedof",
                'secret' => 'XqWA7G9SDTA5JdN', // écris en dur dans le plugin salesforce mais plus utilisé
                'events' => ['registrationFolder.created', 'registrationFolder.updated'],
                'type' => self::getAppId(),
                'options' => [
                    'useAccessToken' => true,
                    'version' => 1
                ]
            ];
            $this->webhookService->create($webhookData, $application->getOrganism());
        } else if (!$webhooks[0]->getEnabled()) {
            /** @var Webhook $webhook */
            $webhook = $webhooks[0];
            $this->webhookService->update($webhook, ['enabled' => true]);
        }
    }

    public function onUpdateMetadata(Application $application, array $previousMetadata): void
    {
        // TODO: Implement onUpdateMetadata() method.
    }

    public function onRefreshOAuth(Application $application): void
    {
        // TODO: Implement onRefreshOAuth() method.
    }

    /**
     * @return array
     */
    public static function getApplicationSubscribedEvents(): array
    {
        return [];
    }

    public function beforeEnable(Application $application): void
    {
        // TODO: Implement beforeEnable() method.
    }

    public function beforeDisable(Application $application): void
    {
        // TODO: Implement beforeDisable() method.
    }

    static public function getAllowedSubscriptionTypes(Subscription $subscription): array
    {
        return [
            'training' => SubscriptionTrainingTypes::getPaidTrainingTypesForApps(),
            'certifier' => []
        ];
    }

    public function show(Application $application): void
    {
        // TODO: Implement onShow() method.
    }
}
