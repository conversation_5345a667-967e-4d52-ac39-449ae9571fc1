<?php

namespace App\Application\Salesforce;

use App\Application\oauth2\WedofApplicationOAuth2Provider;
use App\Entity\Application;
use Stevenmagu<PERSON>\OAuth2\Client\Provider\Salesforce;

class SalesforceWedofApplicationOAuth2Provider extends Salesforce implements WedofApplicationOAuth2Provider
{

    /**
     * SalesforceWedofApplicationOAuth2Provider constructor.
     * @param array $options
     * @param array $collaborators
     */
    public function __construct(array $options = [], array $collaborators = [])
    {
        parent::__construct($options, $collaborators);
    }

    /**
     * @param Application $application
     */
    public function configure(Application $application)
    {
        $metadata = $application->getMetadata();
        $domain = !empty($metadata['domain']) ? $metadata['domain'] : 'login';
        if ($domain == 'login') {
            $this->setDomain("https://login.salesforce.com");
        } else {
            $this->setDomain("https://" . $domain . ".my.salesforce.com");
        }
    }

    /**
     * @return string[]
     */
    public function getDefaultScopes(): array
    {
        return SalesforceWedofApplication::getOAuth2Scopes();
    }
}
