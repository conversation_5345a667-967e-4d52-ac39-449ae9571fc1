<?php

namespace App\Application\Slack\Event\Subscription;

use App\Event\Subscription\SubscriptionEvents;
use App\Library\utils\Tools;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Throwable;


class SubscriptionSlackSubscriber implements EventSubscriberInterface, LoggerAwareInterface
{

    private LoggerInterface $logger;

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    /**
     * @return array|string[]
     */
    public static function getSubscribedEvents(): array
    {
        return [
            SubscriptionEvents::REQUEST_COUNT_RESET => 'sendSlackEventOnSubscriptionRequestCountResest',
            SubscriptionEvents::TRIAL_STARTED => 'sendSlackEventOnSubscriptionTrialStarted'
        ];
    }

    /**
     * @param SubscriptionEvents $event
     * @param string $eventName
     */
    public function sendSlackEventOnSubscriptionRequestCountResest(SubscriptionEvents $event, string $eventName)
    {
        if (!$_ENV["COMMERCIAL_SLACK_URI"]) {
            return;
        }
        try {
            $subscription = $event->getSubscription();
            $organism = $subscription->getOrganism();
            $requestCount = $subscription->getRequestCount();
            $REQUEST_COUNT_THRESHOLD = 100;
            if ($requestCount && $requestCount >= $REQUEST_COUNT_THRESHOLD) {
                Tools::getHttpClient()->request('POST', $_ENV["COMMERCIAL_SLACK_URI"], ['json' => [
                    'blocks' => [
                        0 => [
                            'type' => 'section',
                            'text' => [
                                'type' => 'mrkdwn',
                                'text' => "*Renouvellement abonnement Training - Remise à zéro du requestCount*"
                            ]
                        ],
                        1 => [
                            'type' => 'section',
                            'text' => [
                                'type' => 'mrkdwn',
                                'text' => $organism->getName() . " / " . $organism->getSiret() . ". Ancien requestCount : *" . $requestCount . "*"
                            ]
                        ]
                    ]
                ]]);
            }
        } catch (Throwable $e) {
            $this->logger->error($e->getMessage());
        }
    }

    /**
     * @param SubscriptionEvents $event
     * @param string $eventName
     */
    public function sendSlackEventOnSubscriptionTrialStarted(SubscriptionEvents $event, string $eventName)
    {
        if (!$_ENV["COMMERCIAL_SLACK_URI"]) {
            return;
        }
        try {
            $subscription = $event->getSubscription();
            $organism = $subscription->getOrganism();
                Tools::getHttpClient()->request('POST', $_ENV["COMMERCIAL_SLACK_URI"], ['json' => [
                    'blocks' => [
                        0 => [
                            'type' => 'section',
                            'text' => [
                                'type' => 'mrkdwn',
                                'text' => "* Nouvelle période d'essai *"
                            ]
                        ],
                        1 => [
                            'type' => 'section',
                            'text' => [
                                'type' => 'mrkdwn',
                                'text' => "Période d'essai commencée pour " . $organism->getName() . " / " . $organism->getSiret()
                            ]
                        ]
                    ]
                ]]);
        } catch (Throwable $e) {
            $this->logger->error($e->getMessage());
        }
    }
}
