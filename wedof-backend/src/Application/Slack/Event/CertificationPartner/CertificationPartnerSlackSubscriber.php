<?php

namespace App\Application\Slack\Event\CertificationPartner;

use App\Entity\Organism;
use App\Entity\Webhook;
use App\Event\CertificationPartner\CertificationPartnerEvents;
use App\Event\CertificationPartnerAudit\CertificationPartnerAuditEvents;
use App\Event\CertificationPartnerFile\CertificationPartnerFileEvents;
use App\Event\Invoice\InvoiceEvents;
use App\Library\utils\enums\CertificationPartnerStates;
use App\Library\utils\enums\FileStates;
use App\Library\utils\enums\InvoiceTypes;
use App\Message\SendWebhook;
use App\Service\WebhookService;
use Doctrine\Common\Collections\ArrayCollection;
use LogicException;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Throwable;

class CertificationPartnerSlackSubscriber implements EventSubscriberInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    private MessageBusInterface $messageBus;
    private WebhookService $webhookService;

    public function __construct(WebhookService $webhookService, MessageBusInterface $messageBus)
    {
        $this->webhookService = $webhookService;
        $this->messageBus = $messageBus;
    }

    /**
     * @return array|string[]
     */
    public static function getSubscribedEvents(): array
    {
        $events = array(
            CertificationPartnerFileEvents::FILE_ADDED => 'sendSlackEventOnCertificationPartnerFile',
            CertificationPartnerFileEvents::FILE_UPDATED => 'sendSlackEventOnCertificationPartnerFile',
            CertificationPartnerFileEvents::FILE_DELETED => 'sendSlackEventOnCertificationPartnerFile',
            InvoiceEvents::CERTIFICATION_PARTNER_CREATED => 'sendSlackEventOnCertificationPartnerInvoice',
            InvoiceEvents::CERTIFICATION_PARTNER_UPDATED => 'sendSlackEventOnCertificationPartnerInvoice',
            InvoiceEvents::CERTIFICATION_PARTNER_PAID => 'sendSlackEventOnCertificationPartnerInvoice',
            InvoiceEvents::CERTIFICATION_PARTNER_DELETED => 'sendSlackEventOnCertificationPartnerInvoice',
            CertificationPartnerAuditEvents::PENDING_COMPUTATION => 'sendSlackEventOnCertificationPartnerAudit',
            CertificationPartnerAuditEvents::COMPUTING => 'sendSlackEventOnCertificationPartnerAudit',
            CertificationPartnerAuditEvents::IN_PROGRESS => 'sendSlackEventOnCertificationPartnerAudit',
            CertificationPartnerAuditEvents::COMPLIANT => 'sendSlackEventOnCertificationPartnerAudit',
            CertificationPartnerAuditEvents::NON_COMPLIANT => 'sendSlackEventOnCertificationPartnerAudit',
            CertificationPartnerAuditEvents::PARTIALLY_COMPLIANT => 'sendSlackEventOnCertificationPartnerAudit'
        );
        foreach (CertificationPartnerStates::valuesStates() as $state) {
            if ($state->getValue() !== CertificationPartnerStates::DRAFT()->getValue() && $state->getValue() !== CertificationPartnerStates::ALL()->getValue()) {
                $events["certificationPartner." . $state->getValue()] = 'sendSlackEventOnCertificationPartner';
            }
        }
        foreach (FileStates::valuesStates() as $state) {
            $events["certificationPartnerFile." . $state->getValue()] = "sendSlackEventOnCertificationPartnerFile";
        }
        return $events;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    /**
     * @param CertificationPartnerEvents $event
     * @param string $eventName
     * @throws Throwable
     */
    public function sendSlackEventOnCertificationPartner(CertificationPartnerEvents $event, string $eventName): void
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        $this->logger->debug(self::class . " event $eventName");
        $certificationPartner = $event->getCertificationPartner();
        $certifiers = $certificationPartner->getCertifier() ? [$certificationPartner->getCertifier()] : $certificationPartner->getCertification()->getCertifiers();
        if ($certifiers) {
            foreach ($certifiers as $certifier) {
                $webhooks = $this->webhookService->listByOrganismsAndEventAndType(new ArrayCollection([$certifier]), $eventName, ['type' => 'slack']);
                if (sizeof($webhooks) > 0) {
                    $this->logger->debug("[Slack CertificationPartner] " . sizeof($webhooks) . " slack event found : $eventName");
                    $this->sendSlackEventOnCertificationPartnerToOrganisms($webhooks, $event, $certifier, $eventName);
                } else {
                    $this->logger->debug("[Slack CertificationPartner] no event to send for certificationPartner");
                }
            }
        }
    }

    /**
     * @param InvoiceEvents $event
     * @param string $eventName
     */
    public function sendSlackEventOnCertificationPartnerInvoice(InvoiceEvents $event, string $eventName): void
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        $this->logger->debug(self::class . " event $eventName");
        $certificationPartner = $event->getCertificationPartner();
        $certifiers = $certificationPartner->getCertifier() ? [$certificationPartner->getCertifier()] : $certificationPartner->getCertification()->getCertifiers();
        if ($certifiers) {
            foreach ($certifiers as $certifier) {
                $webhooks = $this->webhookService->listByOrganismsAndEventAndType(new ArrayCollection([$certifier]), $eventName, ['type' => 'slack']);
                if (sizeof($webhooks) > 0) {
                    $this->logger->debug("[Slack CertificationPartnerInvoice] " . sizeof($webhooks) . " slack event found : $eventName");
                    $this->sendSlackEventOnCertificationPartnerInvoiceToOrganisms($webhooks, $event, $certifier, $eventName);
                } else {
                    $this->logger->debug("[Slack CertificationPartnerInvoice] no event to send for invoice");
                }
            }
        }
    }


    /**
     * @param CertificationPartnerAuditEvents $event
     * @param string $eventName
     */
    public function sendSlackEventOnCertificationPartnerAudit(CertificationPartnerAuditEvents $event, string $eventName): void
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        $this->logger->debug(self::class . " event $eventName");
        $certificationPartner = $event->getCertificationPartner();
        $certifiers = $certificationPartner->getCertifier() ? [$certificationPartner->getCertifier()] : $certificationPartner->getCertification()->getCertifiers();
        if ($certifiers) {
            foreach ($certifiers as $certifier) {
                $webhooks = $this->webhookService->listByOrganismsAndEventAndType(new ArrayCollection([$certifier]), $eventName, ['type' => 'slack']);
                if (sizeof($webhooks) > 0) {
                    $this->logger->debug("[Slack CertificationPartnerAudit] " . sizeof($webhooks) . " slack event found : $eventName");
                    $this->sendSlackEventOnCertificationPartnerAuditToOrganisms($webhooks, $event, $certifier, $eventName);
                } else {
                    $this->logger->debug("[Slack CertificationPartnerAudit] no event to send for certificationPartnerAudit");
                }
            }
        }
    }

    /**
     * @param CertificationPartnerFileEvents $event
     * @param string $eventName
     */
    public function sendSlackEventOnCertificationPartnerFile(CertificationPartnerFileEvents $event, string $eventName): void
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        $this->logger->debug(self::class . " event $eventName");
        $certificationPartner = $event->getCertificationPartnerFile()->getCertificationPartner();
        $certifiers = $certificationPartner->getCertifier() ? [$certificationPartner->getCertifier()] : $certificationPartner->getCertification()->getCertifiers();
        if ($certifiers) {
            foreach ($certifiers as $certifier) {
                $webhooks = $this->webhookService->listByOrganismsAndEventAndType(new ArrayCollection([$certifier]), $eventName, ['type' => 'slack']);
                if (sizeof($webhooks) > 0) {
                    $this->logger->debug("[Slack CertificationPartnerFile] " . sizeof($webhooks) . " slack event found : $eventName");
                    $this->sendSlackEventOnCertificationPartnerFileToOrganisms($webhooks, $event, $certifier, $eventName);
                } else {
                    $this->logger->debug("[Slack CertificationPartnerFile] no event to send for certificationPartner");
                }
            }
        }
    }

    //-------------------
    // METHODES PRIVEES
    //-------------------
    /**
     * @param ArrayCollection $webhooks
     * @param CertificationPartnerEvents $event
     * @param Organism $certifier
     * @param string $eventName
     */
    private function sendSlackEventOnCertificationPartnerToOrganisms(ArrayCollection $webhooks, CertificationPartnerEvents $event, Organism $certifier, string $eventName): void
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        /** @var Webhook $webhook */
        foreach ($webhooks as $webhook) {
            $state = explode('.', $eventName)[1];
            $certificationPartner = $event->getCertificationPartner();
            $certification = $certificationPartner->getCertification();
            $certificationName = $certification->getExternalId() . " " . $certification->getName();
            if ($certifier->getSubscription() && $certifier->getSubscription()->isAllowCertifierPlus()) {
                $partnerName = $event->getCertificationPartner()->getPartner()->getName();
                $message = "Le partenariat de *$partnerName* sur la certification *$certificationName* est passé à l'état *" . CertificationPartnerStates::toFrString($state) . "*.";
            } else {
                $message = "Un partenariat sur la certification *$certificationName* est passé à l'état *" . CertificationPartnerStates::toFrString($state) . "*. Pour connaître l'organisme de formation correspondant, souscrivez à l'option de gestion des partenaires sur votre abonnement.";
            }
            $payload = array();
            $payload['blocks'][] = [
                'type' => 'section',
                'text' => [
                    'type' => 'mrkdwn',
                    'text' => $message
                ]
            ];
            $payload = json_encode($payload);
            $message = new SendWebhook($webhook->getId(), $eventName, $payload, get_class($event->getCertificationPartner()), $event->getCertificationPartner()->getId());
            $this->messageBus->dispatch($message);
        }
    }

    /**
     * @param ArrayCollection $webhooks
     * @param InvoiceEvents $event
     * @param Organism $certifier
     * @param string $eventName
     */
    private function sendSlackEventOnCertificationPartnerInvoiceToOrganisms(ArrayCollection $webhooks, InvoiceEvents $event, Organism $certifier, string $eventName): void
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        /** @var Webhook $webhook */
        foreach ($webhooks as $webhook) {
            $certificationPartner = $event->getCertificationPartner();
            $invoice = $event->getInvoice();
            $hasSubscriptionAccess = $certifier->getSubscription() && $certifier->getSubscription()->isAllowCertifierPlus();

            $invoiceType = InvoiceTypes::toFrString($invoice->getType());

            switch ($eventName) {
                case InvoiceEvents::CERTIFICATION_PARTNER_CREATED:
                    $message = "Nouvelle " . $invoiceType . " " . $invoice->getExternalId() . " créée";
                    break;
                case InvoiceEvents::CERTIFICATION_PARTNER_UPDATED:
                    $message = "Une " . $invoiceType . " " . $invoice->getExternalId() . " mise à jour";
                    break;
                case InvoiceEvents::CERTIFICATION_PARTNER_PAID:
                    $message = "La " . $invoiceType . " " . $invoice->getExternalId() . " a été payée";
                    break;
                case InvoiceEvents::CERTIFICATION_PARTNER_DELETED:
                    $message = ucfirst($invoiceType) . " supprimée";
                    break;
                default:
                    throw new LogicException('New events on Invoice not handled.');
            }

            if ($hasSubscriptionAccess) {
                $partnerNameToDisplay = $certificationPartner->getPartner()->getName() . " (" . $certificationPartner->getPartner()->getSiret() . ") ";
                $message .= " sur le partenariat $partnerNameToDisplay";
            }

            $payload = array();
            $payload['blocks'][] = [
                'type' => 'section',
                'text' => [
                    'type' => 'mrkdwn',
                    'text' => $message
                ]
            ];
            $payload = json_encode($payload);
            $message = new SendWebhook($webhook->getId(), $eventName, $payload, get_class($certificationPartner), $event->getCertificationPartner()->getId());
            $this->messageBus->dispatch($message);
        }
    }

    /**
     * @param ArrayCollection $webhooks
     * @param CertificationPartnerFileEvents $event
     * @param Organism $certifier
     * @param string $eventName
     */
    private function sendSlackEventOnCertificationPartnerFileToOrganisms(ArrayCollection $webhooks, CertificationPartnerFileEvents $event, Organism $certifier, string $eventName): void
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        /** @var Webhook $webhook */
        foreach ($webhooks as $webhook) {
            $certificationPartnerFile = $event->getCertificationPartnerFile();
            $certificationPartner = $certificationPartnerFile->getCertificationPartner();
            $certification = $certificationPartner->getCertification();
            $certificationName = $certification->getExternalId() . " " . $certification->getName();
            $hasSubscriptionAccess = $certifier->getSubscription() && $certifier->getSubscription()->isAllowCertifierPlus();

            switch ($eventName) {
                case CertificationPartnerFileEvents::FILE_ADDED:
                    $message = "Un nouveau document a été ajouté au partenariat ";
                    break;
                case CertificationPartnerFileEvents::FILE_UPDATED:
                    $message = "Un document a été modifié au partenariat ";
                    break;
                case CertificationPartnerFileEvents::FILE_DELETED:
                    $message = "Un document a été supprimé au partenariat ";
                    break;
                case 'certificationPartnerFile.' . FileStates::VALID()->getValue():
                    $message = "Un document " . FileStates::toFrStringActivity(FileStates::VALID()->getValue()) . " au partenariat";
                    break;
                case 'certificationPartnerFile.' . FileStates::TO_REVIEW()->getValue():
                    $message = "Un document " . FileStates::toFrStringActivity(FileStates::TO_REVIEW()->getValue()) . " au partenariat";
                    break;
                case 'certificationPartnerFile.' . FileStates::REFUSED()->getValue():
                    $message = "Un document " . FileStates::toFrStringActivity(FileStates::REFUSED()->getValue()) . " au partenariat";
                    break;
                default:
                    throw new LogicException('New state on CertificationPartnerFile not handled.');
            }

            if ($hasSubscriptionAccess) {
                $partnerName = $certificationPartner->getPartner()->getName();
                $message .= "$partnerName* sur la certification *$certificationName* ";
            } else {
                $message .= "sur la certification *$certificationName*. Pour connaître l'organisme de formation correspondant, souscrivez à l'option de gestion des partenaires sur votre abonnement.";
            }

            $payload = array();
            /**
             * Allow the rewriting of GUID
             * each time we perform update or change on state for a file, we need to apply a change on the GUID.
             */
            $payload['update_date'] = $certificationPartnerFile->getUpdatedOn()->getTimestamp();
            $payload['blocks'][] = [
                'type' => 'section',
                'text' => [
                    'type' => 'mrkdwn',
                    'text' => $message
                ]
            ];
            $payload = json_encode($payload);
            $message = new SendWebhook($webhook->getId(), $eventName, $payload, get_class($certificationPartnerFile), $certificationPartnerFile->getId());
            $this->messageBus->dispatch($message);
        }
    }

    /**
     * @param ArrayCollection $webhooks
     * @param CertificationPartnerAuditEvents $event
     * @param Organism $certifier
     * @param string $eventName
     */
    private function sendSlackEventOnCertificationPartnerAuditToOrganisms(ArrayCollection $webhooks, CertificationPartnerAuditEvents $event, Organism $certifier, string $eventName): void
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        /** @var Webhook $webhook */
        foreach ($webhooks as $webhook) {
            $certificationPartner = $event->getCertificationPartner();
            $audit = $event->getCertificationPartnerAudit();
            $auditName = $audit->getTemplate()->getName();
            $hasSubscriptionAccess = $certifier->getSubscription() && $certifier->getSubscription()->isAllowCertifierPlus();

            $message = "Un";
            if ($hasSubscriptionAccess) {
                $message = "L'";
            }

            switch ($eventName) {
                case CertificationPartnerAuditEvents::PENDING_COMPUTATION:
                    $message .= "audit *" . $auditName . "* est en préparation";
                    break;
                case CertificationPartnerAuditEvents::COMPUTING:
                    $message .= "audit *" . $auditName . "* collecte les données";
                    break;
                case CertificationPartnerAuditEvents::IN_PROGRESS:
                    $message .= "audit *" . $auditName . "* est en cours";
                    break;
                case CertificationPartnerAuditEvents::COMPLETED:
                    $message .= "audit *" . $auditName . "* est finalisé";
                    break;
                case CertificationPartnerAuditEvents::COMPLIANT:
                    $message .= "audit *" . $auditName . "* est conforme";
                    break;
                case CertificationPartnerAuditEvents::NON_COMPLIANT:
                    $message .= "audit *" . $auditName . "* est non conforme";
                    break;
                case CertificationPartnerAuditEvents::PARTIALLY_COMPLIANT:
                    $message .= "audit *" . $auditName . "* est partiellement conforme";
                    break;
                default:
                    throw new LogicException('New events on CertificationPartnerAudit not handled.');
            }

            if ($hasSubscriptionAccess) {
                $partnerNameToDisplay = $certificationPartner->getPartner()->getName() . " (" . $certificationPartner->getPartner()->getSiret() . ") ";
                $message .= " sur le partenariat $partnerNameToDisplay";
            }

            $payload = array();
            $payload['blocks'][] = [
                'type' => 'section',
                'text' => [
                    'type' => 'mrkdwn',
                    'text' => $message
                ]
            ];
            $payload = json_encode($payload);
            $message = new SendWebhook($webhook->getId(), $eventName, $payload, get_class($certificationPartner), $event->getCertificationPartner()->getId());
            $this->messageBus->dispatch($message);
        }
    }
}
