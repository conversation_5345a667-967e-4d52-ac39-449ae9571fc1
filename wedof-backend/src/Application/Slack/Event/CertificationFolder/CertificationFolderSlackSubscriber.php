<?php

namespace App\Application\Slack\Event\CertificationFolder;

use App\Entity\Webhook;
use App\Event\CertificationFolder\CertificationFolderEvents;
use App\Event\CertificationFolderFile\CertificationFolderFileEvents;
use App\Library\utils\enums\CertificationFolderStates;
use App\Library\utils\enums\FileStates;
use App\Message\SendWebhook;
use App\Service\WebhookService;
use Doctrine\Common\Collections\ArrayCollection;
use LogicException;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Messenger\MessageBusInterface;

class CertificationFolderSlackSubscriber implements EventSubscriberInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    private WebhookService $webhookService;
    private MessageBusInterface $messageBus;

    public function __construct(WebhookService $webhookService, MessageBusInterface $messageBus)
    {
        $this->webhookService = $webhookService;
        $this->messageBus = $messageBus;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    //-------------------
    // METHODES PUBLIQUES
    //-------------------
    /**
     * @return array|string[]
     */
    public static function getSubscribedEvents(): array
    {
        $events = array(
            CertificationFolderFileEvents::FILE_ADDED => 'sendSlackEventOnCertificationFolderFile',
            CertificationFolderFileEvents::FILE_UPDATED => 'sendSlackEventOnCertificationFolderFile',
            CertificationFolderFileEvents::FILE_DELETED => 'sendSlackEventOnCertificationFolderFile',
        );
        foreach (CertificationFolderStates::valuesStates() as $state) {
            $events["certificationFolder." . $state->getValue()] = 'sendSlackEventOnCertificationFolder';
        }
        foreach (FileStates::valuesStates() as $state) {
            $events["certificationFolderFile." . $state->getValue()] = "sendSlackEventOnCertificationFolderFile";
        }
        return $events;
    }

    /**
     * @param CertificationFolderEvents $event
     * @param string $eventName
     */
    public function sendSlackEventOnCertificationFolder(CertificationFolderEvents $event, string $eventName): void
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        $this->logger->debug(self::class . " event $eventName");
        $certificationFolder = $event->getCertificationFolder();
        $organisms = [$certificationFolder->getCertifier()];
        if ($certificationFolder->getPartner()) {
            array_push($organisms, $certificationFolder->getPartner());
        }
        $webhooks = $this->webhookService->listByOrganismsAndEventAndType(new ArrayCollection($organisms), $eventName, ['type' => 'slack']);
        if (sizeof($webhooks) > 0) {
            $this->logger->debug("[Slack CertificationFolder] " . sizeof($webhooks) . " slack event found : $eventName");
            $this->sendSlackEventOnCertificationFolderToOrganisms($webhooks, $event, $eventName);
        } else {
            $this->logger->debug("[Slack CertificationFolder] no event to send for certifiers");
        }
    }

    /**
     * @param CertificationFolderFileEvents $event
     * @param string $eventName
     */
    public function sendSlackEventOnCertificationFolderFile(CertificationFolderFileEvents $event, string $eventName): void
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        $this->logger->debug(self::class . " event $eventName");
        $certificationFolder = $event->getCertificationFolderFile()->getCertificationFolder();
        $organisms = [$certificationFolder->getCertifier()];
        if ($certificationFolder->getPartner()) {
            array_push($organisms, $certificationFolder->getPartner());
        }
        $webhooks = $this->webhookService->listByOrganismsAndEventAndType(new ArrayCollection($organisms), $eventName, ['type' => 'slack']);
        if (sizeof($webhooks) > 0) {
            $this->logger->debug("[Slack CertificationFolderFile] " . sizeof($webhooks) . " slack event found : $eventName");
            $this->sendSlackEventOnCertificationFolderFileToOrganisms($webhooks, $event, $eventName);
        } else {
            $this->logger->debug("[Slack CertificationFolderFile] no event to send for certifiers");
        }
    }

    //-------------------
    // METHODES PRIVEES
    //-------------------
    /**
     * @param ArrayCollection $webhooks
     * @param CertificationFolderEvents $event
     * @param string $eventName
     */
    private function sendSlackEventOnCertificationFolderToOrganisms(ArrayCollection $webhooks, CertificationFolderEvents $event, string $eventName): void
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        /** @var Webhook $webhook */
        foreach ($webhooks as $webhook) {

            $state = explode('.', $eventName)[1];
            $certificationFolder = $event->getCertificationFolder();

            $displayNameOrganism = $certificationFolder->getPartner() ? $certificationFolder->getPartner()->getName() : "(aucun organisme partenaire déclaré)";

            $displayName = isset($webhook->getOptions()['RGPD']) && $webhook->getOptions()['RGPD'] ? '' : $certificationFolder->getAttendee()->getDisplayName();
            $displayNameCertification = $certificationFolder->getCertification()->getName();

            switch ($state) {
                case CertificationFolderStates::TO_REGISTER():
                    $message = $webhook->getOrganism() === $certificationFolder->getPartner() ? "Un dossier de certification a été créé pour l'apprenant *$displayName* et est en attente d'enregistrement par le certificateur." :
                        "Un nouvel apprenant *$displayName* provenant de *$displayNameOrganism* est à enregistrer.";
                    break;
                case CertificationFolderStates::REFUSED():
                    $message = "Le dossier de *$displayName* provenant de *$displayNameOrganism* a été refusé par le certificateur.";
                    break;
                case CertificationFolderStates::REGISTERED():
                    $message = "Le dossier de *$displayName* provenant de *$displayNameOrganism* a été enregistré auprès du certificateur.";
                    break;
                case CertificationFolderStates::TO_TAKE():
                    $message = $webhook->getOrganism() === $certificationFolder->getPartner() ? "L'apprenant *$displayName* est prêt à passer l'examen de certification."
                        : "L'apprenant *$displayName* de *$displayNameOrganism* est à certifier";
                    break;
                case CertificationFolderStates::TO_CONTROL():
                    $message = "Le dossier de l'apprenant *$displayName* provenant de *$displayNameOrganism* est en attente de contrôle du certificateur.";
                    break;
                case CertificationFolderStates::TO_RETAKE():
                    $message = "L'apprenant *$displayName* de *$displayNameOrganism* n'a pas réussi son examen et doit le repasser.";
                    break;
                case CertificationFolderStates::FAILED():
                    $message = "L'apprenant *$displayName* provenant de *$displayNameOrganism* n'a pas obtenu sa certification.";
                    break;
                case CertificationFolderStates::SUCCESS():
                    $message = "L'apprenant *$displayName* provenant de *$displayNameOrganism* a obtenu sa certification.";
                    break;
                case CertificationFolderStates::ABORTED():
                    $message = "L'apprenant *$displayName* provenant de *$displayNameOrganism* a abandonné l'examen de la certification.";
                    break;
                default:
                    throw new LogicException('New state on CertificationFolder not handled.');
            }
            $message .= "\n\nCertification : *$displayNameCertification*";
            if ($certificationFolder->getRegistrationFolder()) {
                $message .= "\nN° de dossier de formation : " . $certificationFolder->getRegistrationFolder()->getExternalId();
                $message .= "\nFormation : " . $certificationFolder->getRegistrationFolder()->getRawData()['trainingActionInfo']['title'];
            } else {
                $message .= "\n\nN° de dossier de certification (hors dossier de formation) : " . $certificationFolder->getId();
            }

            $payload = array();
            $payload['blocks'][] = [
                'type' => 'section',
                'text' => [
                    'type' => 'mrkdwn',
                    'text' => $message
                ]
            ];
            $payload = json_encode($payload);
            $message = new SendWebhook($webhook->getId(), $eventName, $payload, get_class($event->getCertificationFolder()), $event->getCertificationFolder()->getId());
            $this->messageBus->dispatch($message);
        }
    }

    /**
     * @param ArrayCollection $webhooks
     * @param CertificationFolderFileEvents $event
     * @param string $eventName
     */
    private function sendSlackEventOnCertificationFolderFileToOrganisms(ArrayCollection $webhooks, CertificationFolderFileEvents $event, string $eventName): void
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        /** @var Webhook $webhook */
        foreach ($webhooks as $webhook) {

            $certificationFolder = $event->getCertificationFolderFile()->getCertificationFolder();
            $displayName = isset($webhook->getOptions()['RGPD']) && $webhook->getOptions()['RGPD'] ? '' : $certificationFolder->getAttendee()->getDisplayName();
            $displayNameCertification = $certificationFolder->getCertification()->getName();

            switch ($eventName) {
                case CertificationFolderFileEvents::FILE_ADDED:
                    $message = "Un nouveau document a été ajouté au dossier de certification pour l'apprenant *$displayName* ";
                    break;
                case CertificationFolderFileEvents::FILE_UPDATED:
                    $message = "Un document a été modifié au dossier de certification pour l'apprenant *$displayName* ";
                    break;
                case CertificationFolderFileEvents::FILE_DELETED:
                    $message = "Un document a été supprimé au dossier de certification pour l'apprenant *$displayName* ";
                    break;
                case 'certificationFolderFile.' . FileStates::VALID()->getValue():
                    $message = "Un document " . FileStates::toFrStringActivity(FileStates::VALID()->getValue()) . " au dossier de formation pour l'apprenant *$displayName*";
                    break;
                case 'certificationFolderFile.' . FileStates::TO_REVIEW()->getValue():
                    $message = "Un document " . FileStates::toFrStringActivity(FileStates::TO_REVIEW()->getValue()) . " au dossier de formation pour l'apprenant *$displayName*";
                    break;
                case 'certificationFolderFile.' . FileStates::REFUSED()->getValue():
                    $message = "Un document " . FileStates::toFrStringActivity(FileStates::REFUSED()->getValue()) . " au dossier de formation pour l'apprenant *$displayName*";
                    break;
                default:
                    throw new LogicException('New state on CertificationFolderFile not handled.');
            }
            $message .= "\n\nCertification : *$displayNameCertification*";
            $message .= "\n\nN° de dossier de certification : " . $certificationFolder->getId();

            $payload = array();
            /**
             * Allow the rewriting of GUID
             * each time we perform update or change on state for a file, we need to apply a change on the GUID.
             */
            $payload['update_date'] = $event->getCertificationFolderFile()->getUpdatedOn()->getTimestamp();
            $payload['blocks'][] = [
                'type' => 'section',
                'text' => [
                    'type' => 'mrkdwn',
                    'text' => $message
                ]
            ];
            $payload = json_encode($payload);
            $message = new SendWebhook($webhook->getId(), $eventName, $payload, get_class($event->getCertificationFolderFile()), $event->getCertificationFolderFile()->getId());
            $this->messageBus->dispatch($message);
        }
    }
}
