<?php

namespace App\Application\Slack\Event\Certification;

use App\Entity\Webhook;
use App\Event\Certification\CertificationEvents;
use App\Library\utils\Tools;
use App\Message\SendWebhook;
use App\Service\WebhookService;
use LogicException;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Throwable;

class CertificationSlackSubscriber implements EventSubscriberInterface, LoggerAwareInterface
{

    private LoggerInterface $logger;
    private MessageBusInterface $messageBus;
    private WebhookService $webhookService;

    public function __construct(WebhookService $webhookService, MessageBusInterface $messageBus)
    {
        $this->webhookService = $webhookService;
        $this->messageBus = $messageBus;
    }

    /**
     * @return array|string[]
     */
    public static function getSubscribedEvents(): array
    {
        return [
            CertificationEvents::CREATED => 'sendSlackEventOnCertification',
            CertificationEvents::UPDATED => 'sendSlackEventOnCertification',
            CertificationEvents::AUDITS_ANNUAL_SUBSCRIPTION => 'sendSlackEventOnAuditsAnnualSubscription'
        ];
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    /**
     * @param CertificationEvents $event
     * @param string $eventName
     */
    public function sendSlackEventOnCertification(CertificationEvents $event, string $eventName)
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        $this->logger->debug(self::class . " event $eventName");
        $certification = $event->getCertification();
        $webhooks = $this->webhookService->listByOrganismsAndEventAndType($certification->getCertifiers(), $eventName, ['type' => 'slack']);
        $this->logger->debug(sizeof($webhooks) . " slack event found : $eventName");
        if (sizeof($webhooks) > 0) {
            switch ($eventName) {
                case CertificationEvents::CREATED:
                    $message = "La certification " . $certification->getName() . " a été créée.\n";
                    break;
                case CertificationEvents::UPDATED:
                    $message = "La certification " . $certification->getName() . " a été mise à jour.\n";
                    break;
                default:
                    throw new LogicException("Unhandled certification event");
            }
            $payload = array();
            $payload['blocks'][] = [
                'type' => 'section',
                'text' => [
                    'type' => 'mrkdwn',
                    'text' => trim($message)
                ]
            ];
            $payload = json_encode($payload);
            $this->logger->debug("[Webhook Certification] " . sizeof($webhooks) . " to send");
            /** @var Webhook $webhook */
            foreach ($webhooks as $webhook) {
                $message = new SendWebhook($webhook->getId(), $eventName, $payload, get_class($event->getCertification()), $event->getCertification()->getId());
                $this->messageBus->dispatch($message);
            }

        } else {
            $this->logger->debug("[Webhook Certification] no event to send");
        }
    }

    /**
     * @param CertificationEvents $event
     * @param string $eventName
     */
    public function sendSlackEventOnAuditsAnnualSubscription(CertificationEvents $event, string $eventName)
    {
        if (!$_ENV["COMMERCIAL_SLACK_URI"]) {
            return;
        }
        $certification = $event->getCertification();
        $certificationTitle = $certification->getExternalId() . " " . $certification->getName();
        $organism = $event->getOrganism();
        try {
            Tools::getHttpClient()->request('POST', $_ENV["COMMERCIAL_SLACK_URI"], ['json' => [
                'blocks' => [
                    0 => [
                        'type' => 'section',
                        'text' => [
                            'type' => 'mrkdwn',
                            'text' => "* Gestion des audits " . $certificationTitle . " (Abonnement annuel) *"
                        ]
                    ],
                    1 => [
                        'type' => 'section',
                        'text' => [
                            'type' => 'mrkdwn',
                            'text' => "<@U07EN26KRPD> <@U07BEGQBX52> : il faut *contacter* " . $organism->getName() . " - " . $organism->getSiret()
                        ]
                    ]
                ]
            ]]);
        } catch (Throwable $e) {
            $this->logger->error($e->getMessage());
        }
    }
}
