<?php

namespace App\Application\Slack\Event\Application;

use App\Event\Application\ApplicationEvent;
use App\Library\utils\Tools;
use App\Service\WebhookService;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Throwable;

class ApplicationSlackSubscriber implements EventSubscriberInterface, LoggerAwareInterface
{

    private LoggerInterface $logger;
    private MessageBusInterface $messageBus;
    private WebhookService $webhookService;

    public function __construct(WebhookService $webhookService, MessageBusInterface $messageBus)
    {
        $this->webhookService = $webhookService;
        $this->messageBus = $messageBus;
    }

    /**
     * @return array|string[]
     */
    public static function getSubscribedEvents(): array
    {
        return [
            ApplicationEvent::TRIAL_STARTED => 'sendSlackEventOnTrialApplication',
            ApplicationEvent::TRIAL_ENDED => 'sendSlackEventOnTrialApplication',
        ];
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    /**
     * @param ApplicationEvent $event
     * @param string $eventName
     */
    public function sendSlackEventOnTrialApplication(ApplicationEvent $event, string $eventName)
    {
        if (!empty($_SERVER['COMMERCIAL_SLACK_URI'])) {
            return;
        }
        $this->logger->debug(self::class . " event $eventName");
        $application = $event->getApplication();
        $organismName = $application->getOrganism()->getName();
        $organismSiret = $application->getOrganism()->getSiret();
        $event = $eventName === ApplicationEvent::TRIAL_STARTED ? 'commencée' : 'terminée';
        try {
            Tools::getHttpClient()->request('POST', $_ENV["COMMERCIAL_SLACK_URI"], ['json' => [
                'blocks' => [
                    0 => [
                        'type' => 'section',
                        'text' => [
                            'type' => 'mrkdwn',
                            'text' => "Application " . $application->getAppId() . " période d'essai *" . $event . "*  pour " . $organismName . " (" . $organismSiret . ")"
                        ]
                    ],
                    1 => [
                        'type' => 'section',
                        'text' => [
                            'type' => 'mrkdwn',
                            'text' => "<@U07EN26KRPD> <@U07BEGQBX52>"
                        ]
                    ]
                ]
            ]]);
        } catch (Throwable $e) {
            $this->logger->error($e->getMessage());
        }
    }
}
