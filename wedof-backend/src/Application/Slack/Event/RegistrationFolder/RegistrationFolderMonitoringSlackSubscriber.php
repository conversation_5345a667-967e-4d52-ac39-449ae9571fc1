<?php

namespace App\Application\Slack\Event\RegistrationFolder;

use App\Entity\Webhook;
use App\Event\RegistrationFolder\RegistrationFolderMonitoringEvents;
use App\Message\SendWebhook;
use App\Service\WebhookService;
use DateTime;
use DateTimeZone;
use Doctrine\Common\Collections\ArrayCollection;
use Exception;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Messenger\MessageBusInterface;

class RegistrationFolderMonitoringSlackSubscriber implements EventSubscriberInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    private WebhookService $webhookService;

    private MessageBusInterface $messageBus;

    public function __construct(WebhookService $webhookService, MessageBusInterface $messageBus)
    {
        $this->webhookService = $webhookService;
        $this->messageBus = $messageBus;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    /**
     * @return array|string[]
     */
    public static function getSubscribedEvents(): array
    {
        return [
            RegistrationFolderMonitoringEvents::NOT_VALIDATED => 'sendSlackEventOnMonitoring',
            RegistrationFolderMonitoringEvents::NOT_IN_TRAINING => 'sendSlackEventOnMonitoring',
            RegistrationFolderMonitoringEvents::NOT_ACCEPTED => 'sendSlackEventOnMonitoring',
            RegistrationFolderMonitoringEvents::NOT_SERVICE_DONE_DECLARED => 'sendSlackEventOnMonitoring',
        ];
    }

    /**
     * @param RegistrationFolderMonitoringEvents $event
     * @param string $eventName
     * @throws Exception
     */
    public function sendSlackEventOnMonitoring(RegistrationFolderMonitoringEvents $event, string $eventName)
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        $this->logger->debug(self::class . " event $eventName");
        $organism = $event->getRegistrationFolder()->getOrganism();
        $timeRemaining = $event->getTimeRemaining();
        $webhooks = $this->webhookService->listByOrganismsAndEventAndType(new ArrayCollection([$organism]), $eventName, ['type' => 'slack']);
        $this->logger->debug("[Slack RegistrationFolder Monitoring] " . sizeof($webhooks) . " slack event found : $eventName");
        if (sizeof($webhooks) > 0) {
            $registrationFolder = $event->getRegistrationFolder();
            $displayName = $registrationFolder->getAttendee()->getDisplayName();
            if ($eventName == RegistrationFolderMonitoringEvents::NOT_VALIDATED) {
                $message = "Il reste moins de *$timeRemaining heures* pour valider le dossier déposé par *$displayName*.";
            } else if ($eventName == RegistrationFolderMonitoringEvents::NOT_ACCEPTED) {
                $message = "Il reste moins de *$timeRemaining heures* à l'apprenant *$displayName* pour accepter son dossier de formation.";
            } else if ($eventName == RegistrationFolderMonitoringEvents::NOT_IN_TRAINING) {
                $message = "Il reste moins de *$timeRemaining heures* pour déclarer l'entrée en formation du dossier de l'apprenant *$displayName*.";
            } else {
                $message = "Il reste moins de *$timeRemaining heures* pour déclarer la fin de formation du dossier de l'apprenant *$displayName*.";
            }
            $message .= "\n\nN° de dossier : " . $registrationFolder->getExternalId();
            $message .= "\nIntitulé de Formation : " . $registrationFolder->getRawData()['trainingActionInfo']['title'];
            $message .= "\nIdentifiant de session : " . $registrationFolder->getSession()->getExternalId();
            if (isset($registrationFolder->getRawData()['trainingActionInfo']['sessionStartDate'])) {
                $message .= "\nDate de la session : " . (new DateTime($registrationFolder->getRawData()['trainingActionInfo']['sessionStartDate']))->setTimezone(new DateTimeZone('Europe/Paris'))->format("d/m/Y"); // Hack because of EDOF wrong UTC dates;
            }
            $payload = array();
            $payload['blocks'][] = [
                'type' => 'section',
                'text' => [
                    'type' => 'mrkdwn',
                    'text' => $message
                ]
            ];
            if ($registrationFolder->getLink()) {
                $payload['blocks'][] = [
                    'type' => 'actions',
                    'elements' => [
                        0 => [
                            'type' => 'button',
                            'text' => [
                                'type' => 'plain_text',
                                'text' => 'Détails du dossier'
                            ],
                            'value' => 'details',
                            'url' => $registrationFolder->getLink()

                        ]
                    ]
                ];
            }
            $payload = json_encode($payload);
            /** @var Webhook $webhook */
            foreach ($webhooks as $webhook) {
                $message = new SendWebhook($webhook->getId(), $eventName, $payload, get_class($event->getRegistrationFolder()), $event->getRegistrationFolder()->getExternalId());
                $this->messageBus->dispatch($message);
            }
        }
    }
}
