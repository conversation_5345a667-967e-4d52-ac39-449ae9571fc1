<?php

namespace App\Application\Slack\Event\RegistrationFolder;

use App\Entity\Webhook;
use App\Event\RegistrationFolder\RegistrationFolderEvents;
use App\Event\RegistrationFolderFile\RegistrationFolderFileEvents;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\enums\FileStates;
use App\Library\utils\enums\RegistrationFolderAttendeeStates;
use App\Library\utils\enums\RegistrationFolderBillingStates;
use App\Library\utils\enums\RegistrationFolderControlStates;
use App\Library\utils\enums\RegistrationFolderStates;
use App\Message\SendWebhook;
use App\Service\ConfigService;
use App\Service\RegistrationFolderService;
use App\Service\WebhookService;
use DateTime;
use DateTimeZone;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use Exception;
use LogicException;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Throwable;

class RegistrationFolderSlackSubscriber implements EventSubscriberInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    private WebhookService $webhookService;
    private MessageBusInterface $messageBus;
    private ConfigService $configService;
    private RegistrationFolderService $registrationFolderService;

    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    public function __construct(WebhookService $webhookService, RegistrationFolderService $registrationFolderService, MessageBusInterface $messageBus, ConfigService $configService)
    {
        $this->webhookService = $webhookService;
        $this->messageBus = $messageBus;
        $this->configService = $configService;
        $this->registrationFolderService = $registrationFolderService;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    /**
     * @return array|string[]
     */
    public static function getSubscribedEvents(): array
    {
        $events = array(
            RegistrationFolderEvents::PROPOSAL_APPLIED => 'sendSlackEventOnAppliedProposal',
            RegistrationFolderFileEvents::FILE_ADDED => 'sendSlackEventOnRegistrationFolderFile',
            RegistrationFolderFileEvents::FILE_UPDATED => 'sendSlackEventOnRegistrationFolderFile',
            RegistrationFolderFileEvents::FILE_DELETED => 'sendSlackEventOnRegistrationFolderFile'
        );
        foreach (RegistrationFolderStates::valuesStates() as $state) {
            $events["registrationFolder." . $state->getValue()] = 'sendSlackEventOnRegistrationFolder';
        }
        foreach (RegistrationFolderAttendeeStates::valuesStates() as $state) {
            $events["registrationFolderAttendee." . $state->getValue()] = 'sendSlackEventOnRegistrationFolderAttendee';
        }
        foreach (RegistrationFolderBillingStates::valuesStates() as $state) {
            $events["registrationFolderBilling." . $state->getValue()] = 'sendSlackEventOnRegistrationFolderBilling';
        }
        foreach (RegistrationFolderControlStates::valuesStates() as $state) {
            $events["registrationFolderControl." . $state->getValue()] = 'sendSlackEventOnRegistrationFolderControl';
        }
        foreach (FileStates::valuesStates() as $state) {
            $events['registrationFolderFile.' . $state->getValue()] = "sendSlackEventOnRegistrationFolderFile";
        }
        return $events;
    }

    /**
     * @param RegistrationFolderEvents $event
     * @param string $eventName
     * @throws Exception
     */
    public function sendSlackEventOnRegistrationFolderBilling(RegistrationFolderEvents $event, string $eventName): void
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        $this->logger->debug(self::class . " event $eventName");
        $organism = $event->getRegistrationFolder()->getOrganism();
        $webhooks = $this->webhookService->listByOrganismsAndEventAndType(new ArrayCollection([$organism]), $eventName, ['type' => 'slack']);
        $this->logger->debug(sizeof($webhooks) . " slack event found : $eventName");
        if (sizeof($webhooks) > 0) {
            $this->sendSlackEventOnRegistrationFolderBillingToOrganisms($webhooks, $event, $eventName);
        }
    }

    /**
     * @param RegistrationFolderEvents $event
     * @param string $eventName
     */
    public function sendSlackEventOnRegistrationFolderControl(RegistrationFolderEvents $event, string $eventName): void
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        $this->logger->debug(self::class . " event $eventName");
        $organism = $event->getRegistrationFolder()->getOrganism();
        $webhooks = $this->webhookService->listByOrganismsAndEventAndType(new ArrayCollection([$organism]), $eventName, ['type' => 'slack']);
        $this->logger->debug(sizeof($webhooks) . " slack event found : $eventName");
        if (sizeof($webhooks) > 0) {
            $this->sendSlackEventOnRegistrationFolderControlToOrganism($webhooks, $event, $eventName);
        }
    }

    /**
     * @param RegistrationFolderEvents $event
     * @param string $eventName
     * @throws Exception
     */
    public function sendSlackEventOnRegistrationFolderAttendee(RegistrationFolderEvents $event, string $eventName): void
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        $this->logger->debug(self::class . " event $eventName");
        $organism = $event->getRegistrationFolder()->getOrganism();
        $webhooks = $this->webhookService->listByOrganismsAndEventAndType(new ArrayCollection([$organism]), $eventName, ['type' => 'slack']);
        $this->logger->debug(sizeof($webhooks) . " slack event found : $eventName");
        if (sizeof($webhooks) > 0) {
            $this->sendSlackEventOnRegistrationFolderAttendeeToOrganisms($webhooks, $event, $eventName);
        }
    }

    /**
     * @param RegistrationFolderFileEvents $event
     * @param string $eventName
     * @throws Exception
     */
    public function sendSlackEventOnRegistrationFolderFile(RegistrationFolderFileEvents $event, string $eventName): void
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        $this->logger->debug(self::class . " event $eventName");
        $organism = $event->getRegistrationFolderFile()->getRegistrationFolder()->getOrganism();
        $webhooks = $this->webhookService->listByOrganismsAndEventAndType(new ArrayCollection([$organism]), $eventName, ['type' => 'slack']);
        $this->logger->debug(sizeof($webhooks) . " slack event found : $eventName");
        if (sizeof($webhooks) > 0) {
            $this->sendSlackEventOnRegistrationFolderFileToOrganisms($webhooks, $event, $eventName);
        }
    }

    /**
     * @param RegistrationFolderEvents $event
     * @param string $eventName
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function sendSlackEventOnRegistrationFolder(RegistrationFolderEvents $event, string $eventName): void
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        $this->logger->debug(self::class . " event $eventName");
        $organism = $event->getRegistrationFolder()->getOrganism();

        $webhooks = $this->webhookService->listByOrganismsAndEventAndType(new ArrayCollection([$organism]), $eventName, ['type' => 'slack']);
        if (sizeof($webhooks) > 0) {
            $this->logger->debug("[Slack RegistrationFolder] " . sizeof($webhooks) . " slack event found : $eventName");
            $this->sendSlackEventOnRegistrationFolderToOrganisms($webhooks, $event, $eventName);
        } else {
            $this->logger->debug("[Slack RegistrationFolder] no event to send");
        }
    }

    /**
     * @param RegistrationFolderEvents $event
     * @param string $eventName
     * @return void
     * @throws Exception
     */
    public function sendSlackEventOnAppliedProposal(RegistrationFolderEvents $event, string $eventName): void
    {

        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        $this->logger->debug(self::class . " event $eventName");
        $organism = $event->getRegistrationFolder()->getOrganism();

        $webhooks = $this->webhookService->listByOrganismsAndEventAndType(new ArrayCollection([$organism]), $eventName, ['type' => 'slack']);
        if (sizeof($webhooks) > 0) {
            $this->logger->debug("[Slack RegistrationFolder] " . sizeof($webhooks) . " slack event found : $eventName");
            $this->sendSlackEventOnProposalAppliedToOrganisms($webhooks, $event, $eventName);
        } else {
            $this->logger->debug("[Slack RegistrationFolder] no event to send");
        }
    }

    //-------------------
    // METHODES PRIVEES
    //-------------------
    /**
     * @param ArrayCollection $webhooks
     * @param RegistrationFolderEvents $event
     * @param string $eventName
     * @throws Exception
     */
    private function sendSlackEventOnRegistrationFolderBillingToOrganisms(ArrayCollection $webhooks, RegistrationFolderEvents $event, string $eventName): void
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        /** @var Webhook $webhook */
        foreach ($webhooks as $webhook) {
            $state = str_replace("registrationFolderBilling.", "", $eventName);
            $registrationFolder = $event->getRegistrationFolder();
            $displayName = isset($webhook->getOptions()['RGPD']) && $webhook->getOptions()['RGPD'] ? "d'un apprenant" : "de " . $registrationFolder->getAttendee()->getDisplayName();

            if ($state == RegistrationFolderBillingStates::TO_BILL()->getValue()) {
                $message = "Le dossier *$displayName* est à facturer";
            } else if ($state == RegistrationFolderBillingStates::PAID()->getValue()) {
                $message = "Le dossier *$displayName* a été payé";
            } else if ($state == RegistrationFolderBillingStates::DEPOSIT_WAIT()->getValue()) {
                $message = "Le dossier *$displayName* a un acompte programmé";
            } else if ($state == RegistrationFolderBillingStates::DEPOSIT_PAID()->getValue()) {
                $message = "Le dossier *$displayName* a reçu un acompte";
            } else if ($state == RegistrationFolderBillingStates::BILLED()->getValue()) {
                $message = "Le dossier *$displayName* a été facturé";
            } else {
                $message = "Le dossier *$displayName* n'est pas encore facturable";
            }
            $message .= "\n\nFinancement : " . DataProviders::toFrString($registrationFolder->getType());

            if ($registrationFolder->getLink()) {
                $message .= "\n\nN° de dossier : <" . $registrationFolder->getLink() . "| " . $registrationFolder->getExternalId() . ">";
            } else {
                $message .= "\n\nN° de dossier : " . $registrationFolder->getExternalId();
            }
            $message .= "\nFormation : " . $registrationFolder->getRawData()['trainingActionInfo']['title'];
            $message .= "\nSession : " . $registrationFolder->getSession()->getExternalId();
            if (isset($registrationFolder->getRawData()['trainingActionInfo']['sessionStartDate'])) {
                $startDate = DateTime::createFromFormat('d/m/Y', $registrationFolder->getRawData()['trainingActionInfo']['sessionStartDate']);
                $message .= "\nDate de la session : " . ($startDate ?: new DateTime($registrationFolder->getRawData()['trainingActionInfo']['sessionStartDate']))->setTimezone(new DateTimeZone('Europe/Paris'))->format("d/m/Y"); // Hack because of EDOF wrong UTC dates
            }

            $payload = array();
            $payload['blocks'][] = [
                'type' => 'section',
                'text' => [
                    'type' => 'mrkdwn',
                    'text' => $message
                ]
            ];
            $payload['blocks'][] = [
                'type' => 'actions',
                'elements' => [
                    0 => [
                        'type' => 'button',
                        'text' => [
                            'type' => 'plain_text',
                            'text' => 'Voir sur Wedof'
                        ],
                        'value' => 'details',
                        'url' => $_ENV["WEDOF_REGISTRATION_FOLDER_CONSULTATION_URI"] . $registrationFolder->getExternalId()
                    ]
                ]
            ];

            $payload = json_encode($payload);
            $message = new SendWebhook($webhook->getId(), $eventName, $payload, get_class($event->getRegistrationFolder()), $event->getRegistrationFolder()->getExternalId());
            $this->messageBus->dispatch($message);
        }
    }

    /**
     * @param ArrayCollection $webhooks
     * @param RegistrationFolderEvents $event
     * @param string $eventName
     */
    private function sendSlackEventOnRegistrationFolderControlToOrganism(ArrayCollection $webhooks, RegistrationFolderEvents $event, string $eventName): void
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        /** @var Webhook $webhook */
        foreach ($webhooks as $webhook) {
            $state = str_replace("registrationFolderControl.", "", $eventName);
            $registrationFolder = $event->getRegistrationFolder();
            $message = "";
            if ($state == RegistrationFolderControlStates::IN_CONTROL()->getValue()) {
                $message .= "Le dossier est en cours de contrôle";
            } else if ($state == RegistrationFolderControlStates::RELEASED()->getValue()) {
                $message .= "Le dossier n'est plus en contrôle";
            }
            $message .= "\n\nFinancement : " . DataProviders::toFrString($registrationFolder->getType());

            if ($registrationFolder->getLink()) {
                $message .= "\n\nN° de dossier : <" . $registrationFolder->getLink() . "| " . $registrationFolder->getExternalId() . ">";
            } else {
                $message .= "\n\nN° de dossier : " . $registrationFolder->getExternalId();
            }

            $payload = array();
            $payload['blocks'][] = [
                'type' => 'section',
                'text' => [
                    'type' => 'mrkdwn',
                    'text' => $message
                ]
            ];
            $payload['blocks'][] = [
                'type' => 'actions',
                'elements' => [
                    0 => [
                        'type' => 'button',
                        'text' => [
                            'type' => 'plain_text',
                            'text' => 'Voir sur Wedof'
                        ],
                        'value' => 'details',
                        'url' => $_ENV["WEDOF_REGISTRATION_FOLDER_CONSULTATION_URI"] . $registrationFolder->getExternalId()
                    ]
                ]
            ];

            $payload = json_encode($payload);
            $message = new SendWebhook($webhook->getId(), $eventName, $payload, get_class($event->getRegistrationFolder()), $event->getRegistrationFolder()->getExternalId());
            $this->messageBus->dispatch($message);
        }
    }

    /**
     * @param ArrayCollection $webhooks
     * @param RegistrationFolderEvents $event
     * @param string $eventName
     * @throws Exception
     */
    private function sendSlackEventOnRegistrationFolderAttendeeToOrganisms(ArrayCollection $webhooks, RegistrationFolderEvents $event, string $eventName): void
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        /** @var Webhook $webhook */
        foreach ($webhooks as $webhook) {
            $message = "";
            $state = str_replace("registrationFolderAttendee.", "", $eventName);
            $registrationFolder = $event->getRegistrationFolder();
            $displayName = isset($webhook->getOptions()['RGPD']) && $webhook->getOptions()['RGPD'] ? "d'un apprenant" : "de " . $registrationFolder->getAttendee()->getDisplayName();
            if ($state == RegistrationFolderAttendeeStates::SERVICE_DONE_DECLARED()->getValue()) {
                $message = "Le dossier *$displayName* a reçu le service fait déclaré par l'apprenant";
            }
            $message .= "\n\nFinancement : " . DataProviders::toFrString($registrationFolder->getType());
            if ($registrationFolder->getLink()) {
                $message .= "\n\nN° de dossier : <" . $registrationFolder->getLink() . "| " . $registrationFolder->getExternalId() . ">";
            } else {
                $message .= "\n\nN° de dossier : " . $registrationFolder->getExternalId();
            }
            $message .= "\nFormation : " . $registrationFolder->getRawData()['trainingActionInfo']['title'];
            $message .= "\nSession : " . $registrationFolder->getSession()->getExternalId();
            if (isset($registrationFolder->getRawData()['trainingActionInfo']['sessionStartDate'])) {
                $startDate = DateTime::createFromFormat('d/m/Y', $registrationFolder->getRawData()['trainingActionInfo']['sessionStartDate']);
                $message .= "\nDate de la session : " . ($startDate ?: new DateTime($registrationFolder->getRawData()['trainingActionInfo']['sessionStartDate']))->setTimezone(new DateTimeZone('Europe/Paris'))->format("d/m/Y"); // Hack because of EDOF wrong UTC dates
            }
            $payload = array();
            $payload['blocks'][] = [
                'type' => 'section',
                'text' => [
                    'type' => 'mrkdwn',
                    'text' => $message
                ]
            ];

            $payload['blocks'][] = [
                'type' => 'actions',
                'elements' => [
                    0 => [
                        'type' => 'button',
                        'text' => [
                            'type' => 'plain_text',
                            'text' => 'Voir sur Wedof'
                        ],
                        'value' => 'details',
                        'url' => $_ENV["WEDOF_REGISTRATION_FOLDER_CONSULTATION_URI"] . $registrationFolder->getExternalId()
                    ]
                ]
            ];

            $payload = json_encode($payload);
            $message = new SendWebhook($webhook->getId(), $eventName, $payload, get_class($event->getRegistrationFolder()), $event->getRegistrationFolder()->getExternalId());
            $this->messageBus->dispatch($message);
        }
    }

    /**
     * @param ArrayCollection $webhooks
     * @param RegistrationFolderFileEvents $event
     * @param string $eventName
     * @throws Exception
     */
    private function sendSlackEventOnRegistrationFolderFileToOrganisms(ArrayCollection $webhooks, RegistrationFolderFileEvents $event, string $eventName): void
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        /** @var Webhook $webhook */
        foreach ($webhooks as $webhook) {
            $registrationFolder = $event->getRegistrationFolderFile()->getRegistrationFolder();
            //TODO: Change for FilesState
            $displayName = isset($webhook->getOptions()['RGPD']) && $webhook->getOptions()['RGPD'] ? "d'un apprenant" : "de " . $registrationFolder->getAttendee()->getDisplayName();
            switch ($eventName) {
                case RegistrationFolderFileEvents::FILE_ADDED:
                    $message = "Un nouveau document a été ajouté au dossier de formation pour l'apprenant *$displayName* ";
                    break;
                case RegistrationFolderFileEvents::FILE_UPDATED:
                    $message = "Un document a été modifié au dossier de formation pour l'apprenant *$displayName* ";
                    break;
                case RegistrationFolderFileEvents::FILE_DELETED:
                    $message = "Un document a été supprimé au dossier de formation pour l'apprenant *$displayName* ";
                    break;
                case 'registrationFolderFile.' . FileStates::VALID()->getValue():
                    $message = "Un document " . FileStates::toFrStringActivity(FileStates::VALID()->getValue()) . " au dossier de formation pour l'apprenant *$displayName*";
                    break;
                case 'registrationFolderFile.' . FileStates::TO_REVIEW()->getValue():
                    $message = "Un document " . FileStates::toFrStringActivity(FileStates::TO_REVIEW()->getValue()) . " au dossier de formation pour l'apprenant *$displayName*";
                    break;
                case 'registrationFolderFile.' . FileStates::REFUSED()->getValue():
                    $message = "Un document " . FileStates::toFrStringActivity(FileStates::REFUSED()->getValue()) . " au dossier de formation pour l'apprenant *$displayName*";
                    break;
                default:
                    throw new LogicException('New state on RegistrationFolderFile not handled.');
            }
            $message .= "\n\nFinancement : " . DataProviders::toFrString($registrationFolder->getType());

            if ($registrationFolder->getLink()) {
                $message .= "\n\nN° de dossier : <" . $registrationFolder->getLink() . "| " . $registrationFolder->getExternalId() . ">";
            } else {
                $message .= "\n\nN° de dossier : " . $registrationFolder->getExternalId();
            }
            $message .= "\nFormation : " . $registrationFolder->getRawData()['trainingActionInfo']['title'];
            $message .= "\nSession : " . $registrationFolder->getSession()->getExternalId();

            $payload = array();
            /**
             * Allow the rewriting of GUID
             * each time we perform update or change on state for a file, we need to apply a change on the GUID.
             */
            $payload['update_date'] = $event->getRegistrationFolderFile()->getUpdatedOn()->getTimestamp();
            $payload['blocks'][] = [
                'type' => 'section',
                'text' => [
                    'type' => 'mrkdwn',
                    'text' => $message
                ]
            ];

            $payload['blocks'][] = [
                'type' => 'actions',
                'elements' => [
                    0 => [
                        'type' => 'button',
                        'text' => [
                            'type' => 'plain_text',
                            'text' => 'Voir sur Wedof'
                        ],
                        'value' => 'details',
                        'url' => $_ENV["WEDOF_REGISTRATION_FOLDER_CONSULTATION_URI"] . $registrationFolder->getExternalId()
                    ]
                ]
            ];

            $payload = json_encode($payload);
            $message = new SendWebhook($webhook->getId(), $eventName, $payload, get_class($event->getRegistrationFolderFile()), $event->getRegistrationFolderFile()->getId());
            $this->messageBus->dispatch($message);
        }
    }

    /**
     * @param ArrayCollection $webhooks
     * @param RegistrationFolderEvents $event
     * @param string $eventName
     * @throws ORMException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function sendSlackEventOnRegistrationFolderToOrganisms(ArrayCollection $webhooks, RegistrationFolderEvents $event, string $eventName): void
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        $registrationFolder = $event->getRegistrationFolder();
        $attendeesCount = $this->registrationFolderService->countActiveAttendeesForSession($registrationFolder->getSession());
        /** @var Webhook $webhook */
        foreach ($webhooks as $webhook) {
            $state = str_replace("registrationFolder.", "", $eventName);
            $displayName = isset($webhook->getOptions()['RGPD']) && $webhook->getOptions()['RGPD'] ? "un apprenant" : $registrationFolder->getAttendee()->getDisplayName();
            if ($state == RegistrationFolderStates::NOT_PROCESSED()->getValue()) {
                $message = "Un nouveau dossier a été déposé par *$displayName*. Il est à l'état : *" . RegistrationFolderStates::toFrString($state) . "*.";
            } else {
                $message = "Le dossier déposé par *$displayName* est passé à l'état : *" . RegistrationFolderStates::toFrString($state) . "*.";
            }
            $message .= "\n\nFinancement : " . DataProviders::toFrString($registrationFolder->getType());

            if ($registrationFolder->getLink()) {
                $message .= "\n\nN° de dossier : <" . $registrationFolder->getLink() . "| " . $registrationFolder->getExternalId() . ">";
            } else {
                $message .= "\n\nN° de dossier : " . $registrationFolder->getExternalId();
            }
            $message .= "\nFormation : " . $registrationFolder->getRawData()['trainingActionInfo']['title'];
            $message .= "\nSession : " . $registrationFolder->getSession()->getExternalId() . " - *" . $attendeesCount . " apprenant" . ($attendeesCount > 1 ? "s" : "") . " inscrit" . ($attendeesCount > 1 ? "s" : "") . " ou en cours d'inscription" . ($attendeesCount > 1 ? "s" : "") . " au total*";
            if (isset($registrationFolder->getRawData()['trainingActionInfo']['sessionStartDate'])) {
                $startDate = DateTime::createFromFormat('d/m/Y', $registrationFolder->getRawData()['trainingActionInfo']['sessionStartDate']);
                $message .= "\nDate de la session : " . ($startDate ?: new DateTime($registrationFolder->getRawData()['trainingActionInfo']['sessionStartDate']))->setTimezone(new DateTimeZone('Europe/Paris'))->format("d/m/Y"); // Hack because of EDOF wrong UTC dates
            }
            if ($registrationFolder->getProposal()) {
                $parentCode = $registrationFolder->getProposal()->getParentProposal() ? $registrationFolder->getProposal()->getParentProposal()->getCode() : null;
                $message .= "\n Code proposition : " . $registrationFolder->getProposal()->getCode() . ($parentCode ? ' (' . $parentCode . ')' : '');
            }
            $payload = array();
            $payload['blocks'][] = [
                'type' => 'section',
                'text' => [
                    'type' => 'mrkdwn',
                    'text' => $message
                ]
            ];
            if ($state == RegistrationFolderStates::NOT_PROCESSED()->getValue() && (!$registrationFolder->getProposal() || $registrationFolder->getProposal()->getAutoValidate() === false)) {
                $sessionStartDate = DateTime::createFromFormat('d/m/Y', $registrationFolder->getRawData()['trainingActionInfo']['sessionStartDate']) ?: (new DateTime($registrationFolder->getRawData()['trainingActionInfo']['sessionStartDate']))->setTimezone(new DateTimeZone('Europe/Paris'));
                if (($sessionStartDate && $sessionStartDate >= $this->configService->getConfig()->getCpfSessionMinDate()) || $registrationFolder->getType() != DataProviders::CPF()->getValue()) {
                    $userCanValidate = $registrationFolder->getOrganism()->getOwnedBy();
                    $payload['blocks'][] = [
                        'type' => 'actions',
                        'elements' => [
                            0 => [
                                'type' => 'button',
                                'text' => [
                                    'type' => 'plain_text',
                                    'text' => 'Valider'
                                ],
                                'value' => 'Valider',
                                'url' => $_ENV["WEDOF_BASE_URI"] . "/slack/registrationFolders/" . $registrationFolder->getExternalId() . "/validate/" . $userCanValidate->getId(),
                                'style' => 'primary'
                            ],
                            1 => [
                                'type' => 'button',
                                'text' => [
                                    'type' => 'plain_text',
                                    'text' => 'Voir sur Wedof'
                                ],
                                'value' => 'details',
                                'url' => $_ENV["WEDOF_REGISTRATION_FOLDER_CONSULTATION_URI"] . $registrationFolder->getExternalId()
                            ]
                        ]
                    ];
                } else {
                    $payload['blocks'][] = [
                        'type' => 'actions',
                        'elements' => [
                            0 => [
                                'type' => 'button',
                                'text' => [
                                    'type' => 'plain_text',
                                    'text' => 'Non validable'
                                ],
                                'value' => 'non_validable',
                                'style' => 'danger'
                            ],
                            1 => [
                                'type' => 'button',
                                'text' => [
                                    'type' => 'plain_text',
                                    'text' => 'Voir sur Wedof'
                                ],
                                'value' => 'details',
                                'url' => $_ENV["WEDOF_REGISTRATION_FOLDER_CONSULTATION_URI"] . $registrationFolder->getExternalId()
                            ]
                        ]
                    ];
                }
            } else {
                $payload['blocks'][] = [
                    'type' => 'actions',
                    'elements' => [
                        0 => [
                            'type' => 'button',
                            'text' => [
                                'type' => 'plain_text',
                                'text' => 'Voir sur Wedof'
                            ],
                            'value' => 'details',
                            'url' => $_ENV["WEDOF_REGISTRATION_FOLDER_CONSULTATION_URI"] . $registrationFolder->getExternalId()

                        ]
                    ]
                ];
            }
            $payload = json_encode($payload);
            $message = new SendWebhook($webhook->getId(), $eventName, $payload, get_class($event->getRegistrationFolder()), $event->getRegistrationFolder()->getExternalId());
            $this->messageBus->dispatch($message);
        }
    }

    /**
     * @throws Exception
     */
    private function sendSlackEventOnProposalAppliedToOrganisms(ArrayCollection $webhooks, RegistrationFolderEvents $event, string $eventName): void
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        /** @var Webhook $webhook */
        foreach ($webhooks as $webhook) {
            $registrationFolder = $event->getRegistrationFolder();
            $displayName = isset($webhook->getOptions()['RGPD']) && $webhook->getOptions()['RGPD'] ? "d'un apprenant" : "de " . $registrationFolder->getAttendee()->getDisplayName();
            $parentCode = $registrationFolder->getProposal()->getParentProposal() ? $registrationFolder->getProposal()->getParentProposal()->getCode() : null;
            $proposalCode = $registrationFolder->getProposal()->getCode() . ($parentCode ? ' (' . $parentCode . ')' : '');

            $message = "Le dossier déposé par *$displayName* a bénéficié du code proposition : *$proposalCode*";
            $message .= "\n\nFinancement : " . DataProviders::toFrString($registrationFolder->getType());

            if ($registrationFolder->getLink()) {
                $message .= "\n\nN° de dossier : <" . $registrationFolder->getLink() . "| " . $registrationFolder->getExternalId() . ">";
            } else {
                $message .= "\n\nN° de dossier : " . $registrationFolder->getExternalId();
            }
            $message .= "\nFormation : " . $registrationFolder->getRawData()['trainingActionInfo']['title'];
            $message .= "\nSession : " . $registrationFolder->getSession()->getExternalId();
            if (isset($registrationFolder->getRawData()['trainingActionInfo']['sessionStartDate'])) {
                $startDate = DateTime::createFromFormat('d/m/Y', $registrationFolder->getRawData()['trainingActionInfo']['sessionStartDate']);
                $message .= "\nDate de la session : " . ($startDate ?: new DateTime($registrationFolder->getRawData()['trainingActionInfo']['sessionStartDate']))->setTimezone(new DateTimeZone('Europe/Paris'))->format("d/m/Y"); // Hack because of EDOF wrong UTC dates
            }

            $payload = array();
            $payload['blocks'][] = [
                'type' => 'section',
                'text' => [
                    'type' => 'mrkdwn',
                    'text' => $message
                ]
            ];
            $payload['blocks'][] = [
                'type' => 'actions',
                'elements' => [
                    0 => [
                        'type' => 'button',
                        'text' => [
                            'type' => 'plain_text',
                            'text' => 'Voir sur Wedof'
                        ],
                        'value' => 'details',
                        'url' => $_ENV["WEDOF_REGISTRATION_FOLDER_CONSULTATION_URI"] . $registrationFolder->getExternalId()
                    ]
                ]
            ];

            $payload = json_encode($payload);
            $message = new SendWebhook($webhook->getId(), $eventName, $payload, get_class($event->getRegistrationFolder()), $event->getRegistrationFolder()->getExternalId());
            $this->messageBus->dispatch($message);
        }
    }
}
