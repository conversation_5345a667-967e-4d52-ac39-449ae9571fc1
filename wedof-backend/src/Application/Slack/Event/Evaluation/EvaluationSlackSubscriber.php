<?php

namespace App\Application\Slack\Event\Evaluation;

use App\Entity\Webhook;
use App\Event\Evaluation\EvaluationEvents;
use App\Library\utils\Tools;
use App\Message\SendWebhook;
use App\Service\WebhookService;
use Doctrine\Common\Collections\ArrayCollection;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Messenger\MessageBusInterface;

class EvaluationSlackSubscriber implements EventSubscriberInterface, LoggerAwareInterface
{

    private LoggerInterface $logger;
    private WebhookService $webhookService;
    private MessageBusInterface $messageBus;

    public function __construct(WebhookService $webhookService, MessageBusInterface $messageBus)
    {
        $this->webhookService = $webhookService;
        $this->messageBus = $messageBus;
    }

    /**
     * @return array|string[]
     */
    public static function getSubscribedEvents(): array
    {
        $events = array();
        $events["evaluation.organismNew"] = 'sendSlackEventOnEvaluationOrganism';
        $events["evaluation.organismChanged"] = 'sendSlackEventOnEvaluationOrganism';
        $events["evaluation.trainingNew"] = 'sendSlackEventOnEvaluationTraining';
        $events["evaluation.trainingChanged"] = 'sendSlackEventOnEvaluationTraining';
        $events["evaluation.trainingActionNew"] = 'sendSlackEventOnEvaluationTrainingAction';
        $events["evaluation.trainingActionChanged"] = 'sendSlackEventOnEvaluationTrainingAction';
        return $events;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    /**
     * @param EvaluationEvents $event
     * @param string $eventName
     */
    public function sendSlackEventOnEvaluationOrganism(EvaluationEvents $event, string $eventName)
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        $this->logger->debug(self::class . " event $eventName");
        $organism = $event->getEvaluation()->getOrganism();
        $webhooks = $this->webhookService->listByOrganismsAndEventAndType(new ArrayCollection([$organism]), $eventName, ['type' => 'slack']);
        $this->logger->debug(sizeof($webhooks) . " slack event found : $eventName");
        if (sizeof($webhooks) > 0) {
            $eval = $event->getEvaluation();
            $peval = $event->getPreviousEvaluation();
            $variationRating = $peval != null ? ($this->getVariationRating($eval->getAverageRating(), $peval->getAverageRating())) . " " : "";
            $text = $peval != null ? (Tools::startsWith($variationRating, '(+') ? "a augmenté" : (Tools::startsWith($variationRating, '(-') ? 'a diminué' : 'est stable')) : "a été initialisée";
            $message = "L'évaluation de l'organisme *" . $event->getEvaluation()->getOrganism()->getName() . "*" . " " . $text . ". Elle est de *" . $eval->getAverageRating() . "* sur 5 " . $variationRating . "\n";
            $payload = array();
            $payload['blocks'][] = [
                'type' => 'section',
                'text' => [
                    'type' => 'mrkdwn',
                    'text' => trim($message)
                ]
            ];
            $payload = json_encode($payload);
            /** @var Webhook $webhook */
            foreach ($webhooks as $webhook) {
                $message = new SendWebhook($webhook->getId(), $eventName, $payload, get_class($event->getEvaluation()), $event->getEvaluation()->getId());
                $this->messageBus->dispatch($message);
            }
        }
    }

    /**
     * @param EvaluationEvents $event
     * @param string $eventName
     */
    public function sendSlackEventOnEvaluationTrainingAction(EvaluationEvents $event, string $eventName)
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        $this->logger->debug(self::class . " event $eventName");
        $organism = $event->getEvaluation()->getOrganism();
        $webhooks = $this->webhookService->listByOrganismsAndEventAndType(new ArrayCollection([$organism]), $eventName, ['type' => 'slack']);
        $this->logger->debug(sizeof($webhooks) . " slack event found : $eventName");
        if (sizeof($webhooks) > 0) {
            $eval = $event->getEvaluation();
            $peval = $event->getPreviousEvaluation();
            $variationReview = ($peval != null && $eval->getReviewNumber() - $peval->getReviewNumber() > 1) || $eval->getReviewNumber() > 1 ? "s" : "";
            $variationReview .= $peval != null ? " (+" . ($eval->getReviewNumber() - $peval->getReviewNumber()) . ")." : ".";
            $variationRating = $peval != null ? ($this->getVariationRating($eval->getAverageRating(), $peval->getAverageRating())) . " " : "";
            $text = $peval != null ? (Tools::startsWith($variationRating, '(+') ? "a augmenté" : (Tools::startsWith($variationRating, '(-') ? 'a diminué' : 'est stable')) : "a été initialisée";

            $message = "L'évaluation de l'action de formation *" . $eval->getTrainingAction()->getTraining()->getTitle() . "*" . " " . $text . ". Elle est de *" . $eval->getAverageRating() . "* sur 5 " . $variationRating . "avec " . $eval->getReviewNumber() . " évaluation" . $variationReview . "\n";
            $message .= "\n";
            if ($peval) {
                $message .= "q1 : " . $eval->getRatingQuestion1() . " " . $this->getVariationRating($eval->getRatingQuestion1(), $peval->getRatingQuestion1()) . " | q2 : " . $eval->getRatingQuestion2() . " " . $this->getVariationRating($eval->getRatingQuestion2(), $peval->getRatingQuestion2()) . " | q3 : " . $eval->getRatingQuestion3() . " " . $this->getVariationRating($eval->getRatingQuestion3(), $peval->getRatingQuestion3()) . " | q4 : " . $eval->getRatingQuestion4() . " " . $this->getVariationRating($eval->getRatingQuestion4(), $peval->getRatingQuestion4()) . " | q5 : " . $eval->getRatingQuestion5() . " " . $this->getVariationRating($eval->getRatingQuestion5(), $peval->getRatingQuestion5());
            } else {
                $message .= "q1 : " . $eval->getRatingQuestion1() . " | q2 : " . $eval->getRatingQuestion2() . " | q3 : " . $eval->getRatingQuestion3() . " | q4 : " . $eval->getRatingQuestion4() . " | q5 : " . $eval->getRatingQuestion5();
            }

            $payload = array();
            $payload['blocks'][] = [
                'type' => 'section',
                'text' => [
                    'type' => 'mrkdwn',
                    'text' => trim($message)
                ]
            ];
            $payload = json_encode($payload);
            /** @var Webhook $webhook */
            foreach ($webhooks as $webhook) {
                $message = new SendWebhook($webhook->getId(), $eventName, $payload, get_class($event->getEvaluation()), $event->getEvaluation()->getId());
                $this->messageBus->dispatch($message);
            }
        }
    }

    /**
     * @param EvaluationEvents $event
     * @param string $eventName
     */
    public function sendSlackEventOnEvaluationTraining(EvaluationEvents $event, string $eventName)
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        $this->logger->debug(self::class . " event $eventName");
        $organism = $event->getEvaluation()->getOrganism();
        $webhooks = $this->webhookService->listByOrganismsAndEventAndType(new ArrayCollection([$organism]), $eventName, ['type' => 'slack']);
        $this->logger->debug(sizeof($webhooks) . " slack event found : $eventName");
        if (sizeof($webhooks) > 0) {
            $eval = $event->getEvaluation();
            $peval = $event->getPreviousEvaluation();
            $variationReview = ($peval != null && $eval->getReviewNumber() - $peval->getReviewNumber() > 1) || $eval->getReviewNumber() > 1 ? "s" : "";
            $variationReview .= $peval != null ? " (+" . ($eval->getReviewNumber() - $peval->getReviewNumber()) . ")." : ".";
            $variationRating = $peval != null ? ($this->getVariationRating($eval->getAverageRating(), $peval->getAverageRating())) . " " : "";
            $text = $peval != null ? (Tools::startsWith($variationRating, '(+') ? "a augmenté" : (Tools::startsWith($variationRating, '(-') ? 'a diminué' : 'est stable')) : "a été initialisée";

            $message = "L'évaluation de la formation *" . $eval->getTraining()->getTitle() . "*" . " " . $text . ". Elle est de *" . $eval->getAverageRating() . "* sur 5 " . $variationRating . "avec " . $eval->getReviewNumber() . " évaluation" . $variationReview . "\n";

            $payload = array();
            $payload['blocks'][] = [
                'type' => 'section',
                'text' => [
                    'type' => 'mrkdwn',
                    'text' => trim($message)
                ]
            ];
            $payload = json_encode($payload);
            /** @var Webhook $webhook */
            foreach ($webhooks as $webhook) {
                $message = new SendWebhook($webhook->getId(), $eventName, $payload, get_class($event->getEvaluation()), $event->getEvaluation()->getId());
                $this->messageBus->dispatch($message);
            }
        }
    }

    private function getVariationRating($newVal, $oldVal): string
    {
        if (!$oldVal) {
            return "";
        }
        $var = round($newVal - $oldVal, 2);
        $text = ($newVal != $oldVal) ? $var : "";
        if ($newVal != $oldVal) {
            if ($var < 0) {
                if ($var < -0.3) {
                    $text = "*" . $var . "*";
                } else {
                    $text = "" . $var;
                }
            } else {
                if ($var > +0.3) {
                    $text = "*+" . $var . "*";
                } else {
                    $text = "+" . $var;
                }
            }
        }
        return $text ? "(" . $text . ")" : "";
    }
}
