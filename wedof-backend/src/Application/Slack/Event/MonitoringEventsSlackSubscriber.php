<?php

namespace App\Application\Slack\Event;

use App\Event\MonitoringEvents;
use App\Library\utils\Tools;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Throwable;

class MonitoringEventsSlackSubscriber implements EventSubscriberInterface, LoggerAwareInterface
{

    private LoggerInterface $logger;

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    /**
     * @return array|string[]
     */
    public static function getSubscribedEvents(): array
    {
        $events = [];
        foreach (MonitoringEvents::getEvents() as $event) {
            if ($event != MonitoringEvents::SEND_MESSAGE) {
                $events[$event] = 'sendSlackException';
            } else {
                $events[$event] = 'sendSlackMessage';
            }
        }
        return $events;
    }

    /**
     * @param MonitoringEvents $event
     * @param string $eventName
     */
    public function sendSlackException(MonitoringEvents $event, string $eventName)
    {
        if (!$_ENV["LOG_ERRORS_SLACK_URI"]) {
            return;
        }
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        try {
            Tools::getHttpClient()->request('POST', $_ENV["LOG_ERRORS_SLACK_URI"], ['json' => [
                'blocks' => [
                    0 => [
                        'type' => 'section',
                        'text' => [
                            'type' => 'mrkdwn',
                            'text' => "Alerte : une erreur *$eventName* est survenue !"
                        ]
                    ],
                    1 => [
                        'type' => 'section',
                        'text' => [
                            'type' => 'mrkdwn',
                            'text' => "Serveur ou worker: " . gethostname() ?? "inconnu"
                        ]
                    ],
                    2 => [
                        'type' => 'section',
                        'text' => [
                            'type' => 'mrkdwn',
                            'text' => "Exception message: " . ($event->getException() ? $event->getException()->getMessage() : "Aucune exception remontée")
                        ]
                    ]
                ]
            ]]);
        } catch (Throwable $e) {
            $this->logger->error($e->getMessage());
        }
    }

    /**
     * @param MonitoringEvents $event
     * @param string $eventName
     */
    public function sendSlackMessage(MonitoringEvents $event, string $eventName)
    {
        if (!$_ENV["LOG_ERRORS_SLACK_URI"]) {
            return;
        }
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        try {
            Tools::getHttpClient()->request('POST', $_ENV["LOG_ERRORS_SLACK_URI"], ['json' => [
                'blocks' => [
                    0 => [
                        'type' => 'section',
                        'text' => [
                            'type' => 'mrkdwn',
                            'text' => $event->getMessage()
                        ]
                    ]
                ]
            ]]);
        } catch (Throwable $e) {
            $this->logger->error($e->getMessage());
        }
    }
}
