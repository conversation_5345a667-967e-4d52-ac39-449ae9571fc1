<?php

namespace App\Application\Slack\Event\User;

use App\Event\User\UserEvents;
use App\Library\utils\Tools;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Throwable;

class UserSlackSubscriber implements EventSubscriberInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    public static function getSubscribedEvents(): array
    {
        return [
            UserEvents::CREATED => 'sendSlackOnCreatedUser',
            UserEvents::SIGN_UP_COMPLETE => 'sendSlackOnSignUpCompleted'
        ];
    }

    /**
     * @param UserEvents $event
     * @param string $eventName
     */
    public function sendSlackOnCreatedUser(UserEvents $event, string $eventName): void
    {
        if (!$_ENV["COMMERCIAL_SLACK_URI"]) {
            return;
        }
        try {
            Tools::getHttpClient()->request('POST', $_ENV["COMMERCIAL_SLACK_URI"], ['json' => [
                'blocks' => [
                    0 => [
                        'type' => 'section',
                        'text' => [
                            'type' => 'mrkdwn',
                            'text' => "*Un nouvel utilisateur s'est inscrit !*"
                        ]
                    ],
                    1 => [
                        'type' => 'section',
                        'text' => [
                            'type' => 'mrkdwn',
                            'text' => $event->getUser()->getName() . " vient de s'inscrire."
                        ]
                    ]
                ]
            ]]);
        } catch (Throwable $e) {
            $this->logger->error($e->getMessage());
        }
    }

    /**
     * @param UserEvents $event
     * @param string $eventName
     */
    public function sendSlackOnSignUpCompleted(UserEvents $event, string $eventName): void
    {
        if (!$_ENV["COMMERCIAL_SLACK_URI"]) {
            return;
        }
        try {
            $user =  $event->getUser();
            $organism = $user->getMainOrganism();
            $reseller = $organism->getReseller() ? " (Reseller : " . $organism->getReseller()->getName() . ") " : '';
            Tools::getHttpClient()->request('POST', $_ENV["COMMERCIAL_SLACK_URI"], ['json' => [
                'blocks' => [
                    0 => [
                        'type' => 'section',
                        'text' => [
                            'type' => 'mrkdwn',
                            'text' => "*Fin d'inscription !*"
                        ]
                    ],
                    1 => [
                        'type' => 'section',
                        'text' => [
                            'type' => 'mrkdwn',
                            'text' => $user->getName() . " vient d'associer son compte à l'organisme " . $organism->getName() . $reseller . " et de terminer son inscription."
                        ]
                    ]
                ]
            ]]);
        } catch (Throwable $e) {
            $this->logger->error($e->getMessage());
        }
    }
}