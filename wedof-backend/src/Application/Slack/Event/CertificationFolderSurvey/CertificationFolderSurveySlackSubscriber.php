<?php

namespace App\Application\Slack\Event\CertificationFolderSurvey;

use App\Entity\Organism;
use App\Entity\Webhook;
use App\Event\CertificationFolderSurvey\CertificationFolderSurveyEvents;
use App\Library\utils\enums\CertificationFolderSurveyExperience;
use App\Message\SendWebhook;
use App\Service\WebhookService;
use Doctrine\Common\Collections\ArrayCollection;
use LogicException;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Throwable;

class CertificationFolderSurveySlackSubscriber implements EventSubscriberInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    private MessageBusInterface $messageBus;
    private WebhookService $webhookService;

    public function __construct(WebhookService $webhookService, MessageBusInterface $messageBus)
    {
        $this->webhookService = $webhookService;
        $this->messageBus = $messageBus;
    }

    /**
     * @return array|string[]
     */
    public static function getSubscribedEvents(): array
    {
        return [
            CertificationFolderSurveyEvents::CREATED => 'sendSlackEventOnCertificationFolderSurvey',
            CertificationFolderSurveyEvents::INITIAL_EXPERIENCE_ANSWERED => 'sendSlackEventOnCertificationFolderSurvey',
            CertificationFolderSurveyEvents::SIX_MONTH_EXPERIENCE_ANSWERED => 'sendSlackEventOnCertificationFolderSurvey',
            CertificationFolderSurveyEvents::LONG_TERM_EXPERIENCE_ANSWERED => 'sendSlackEventOnCertificationFolderSurvey',
            CertificationFolderSurveyEvents::SIX_MONTH_EXPERIENCE_AVAILABLE => 'sendSlackEventOnCertificationFolderSurvey',
            CertificationFolderSurveyEvents::LONG_TERM_EXPERIENCE_AVAILABLE => 'sendSlackEventOnCertificationFolderSurvey',
        ];
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    /**
     * @param CertificationFolderSurveyEvents $event
     * @param string $eventName
     * @throws Throwable
     */
    public function sendSlackEventOnCertificationFolderSurvey(CertificationFolderSurveyEvents $event, string $eventName): void
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        $this->logger->debug(self::class . " event $eventName");
        $certificationFolderSurvey = $event->getCertificationFolderSurvey();
        $certificationFolder = $certificationFolderSurvey->getCertificationFolder();
        $certifiers = $certificationFolder->getCertifier() ? [$certificationFolder->getCertifier()] : $certificationFolder->getCertification()->getCertifiers();
        if ($certifiers) {
            foreach ($certifiers as $certifier) {
                $webhooks = $this->webhookService->listByOrganismsAndEventAndType(new ArrayCollection([$certifier]), $eventName, ['type' => 'slack']);
                if (sizeof($webhooks) > 0) {
                    $this->logger->debug("[Slack CertificationFolderSurvey] " . sizeof($webhooks) . " slack event found : $eventName");
                    $this->sendSlackEventOnCertificationFolderSurveyToOrganisms($webhooks, $event, $certifier, $eventName);
                } else {
                    $this->logger->debug("[Slack CertificationFolderSurvey] no event to send for certificationFolderSurvey");
                }
            }
        }
    }

    //-------------------
    // METHODES PRIVEES
    //-------------------
    /**
     * @param ArrayCollection $webhooks
     * @param CertificationFolderSurveyEvents $event
     * @param Organism $certifier
     * @param string $eventName
     */
    private function sendSlackEventOnCertificationFolderSurveyToOrganisms(ArrayCollection $webhooks, CertificationFolderSurveyEvents $event, Organism $certifier, string $eventName): void
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        /** @var Webhook $webhook */
        foreach ($webhooks as $webhook) {
            $certificationFolderSurvey = $event->getCertificationFolderSurvey();
            $certificationFolder = $certificationFolderSurvey->getCertificationFolder();
            $certificationFolderExternalId = $certificationFolder->getExternalId();
            $candidateLink = $certificationFolder->getAttendeeLink();
            $isAllowCertifierPlus = $certifier->getSubscription() && $certifier->getSubscription()->isAllowCertifierPlus();

            switch ($eventName) {
                case CertificationFolderSurveyEvents::CREATED:
                    $message = "L'enquête associée au dossier de certification " . $certificationFolderExternalId . " a été créée.";
                    break;
                case CertificationFolderSurveyEvents::INITIAL_EXPERIENCE_ANSWERED:
                    $message = "Le candidat associé au dossier de certification " . $certificationFolderExternalId . " a répondu au " . CertificationFolderSurveyExperience::toFrString('initialExperience') . ".";
                    break;
                case CertificationFolderSurveyEvents::SIX_MONTH_EXPERIENCE_ANSWERED:
                    $message = "Le candidat associé au dossier de certification " . $certificationFolderExternalId . " a répondu au " . CertificationFolderSurveyExperience::toFrString('sixMonthExperience') . ".";
                    break;
                case CertificationFolderSurveyEvents::LONG_TERM_EXPERIENCE_ANSWERED:
                    $message = "Le candidat associé au dossier de certification " . $certificationFolderExternalId . " a répondu au " . CertificationFolderSurveyExperience::toFrString('longTermExperience') . ".";
                    break;
                case CertificationFolderSurveyEvents::SIX_MONTH_EXPERIENCE_AVAILABLE:
                    $message = "Le " . CertificationFolderSurveyExperience::toFrString('sixMonthExperience') . " est accessible depuis l'Espace Candidat";
                    if ($isAllowCertifierPlus) {
                        $message .= " : " . $candidateLink;
                    }
                    break;
                case CertificationFolderSurveyEvents::LONG_TERM_EXPERIENCE_AVAILABLE:
                    $message = "Le " . CertificationFolderSurveyExperience::toFrString('longTermExperience') . " est accessible depuis l'Espace Candidat";
                    if ($isAllowCertifierPlus) {
                        $message .= " : " . $candidateLink;
                    }
                    break;
                default:
                    throw new LogicException("Unhandled certificationFolderSurvey event");
            }

            $payload = array();
            $payload['blocks'][] = [
                'type' => 'section',
                'text' => [
                    'type' => 'mrkdwn',
                    'text' => $message
                ]
            ];
            $payload = json_encode($payload);
            $message = new SendWebhook($webhook->getId(), $eventName, $payload, get_class($event->getCertificationFolderSurvey()), $event->getCertificationFolderSurvey()->getId());
            $this->messageBus->dispatch($message);

        }
    }
}
