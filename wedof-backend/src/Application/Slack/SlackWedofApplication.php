<?php
// src/Event/Application/ApplicationEventsSubscriber.php
namespace App\Application\Slack;

use App\Application\WedofApplication;
use App\Entity\Application;
use App\Entity\Subscription;
use App\Library\utils\enums\SubscriptionCertifierTypes;
use App\Library\utils\enums\SubscriptionTrainingTypes;
use App\Repository\WebhookRepository;
use App\Service\ActivityService;
use App\Service\ApplicationService;
use App\Service\WebhookService;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Throwable;

class SlackWedofApplication extends WedofApplication
{
    protected static string $APP_ID = "slack";
    protected static array $SCOPES = ["incoming-webhook", "chat:write:bot"];
    protected static array $METHODS = ["removeChannel", "updateChannel"];

    private WebhookService $webhookService;
    private WebhookRepository $webhookRepository;

    /**
     * SlackApplication constructor.
     * @param ApplicationService $applicationService
     * @param ActivityService $activityService
     * @param WebhookRepository $webhookRepository
     * @param WebhookService $webhookService
     */
    public function __construct(ApplicationService $applicationService, ActivityService $activityService, WebhookRepository $webhookRepository, WebhookService $webhookService)
    {
        parent::__construct($applicationService, $activityService);
        $this->webhookService = $webhookService;
        $this->webhookRepository = $webhookRepository;
    }

    /**
     * @param Application $application
     * @param $data
     * @return array|null
     */
    public function removeChannel(Application $application, $data): ?array
    {
        $metadata = $application->getMetadata();
        if (!empty($data['id']) && !empty($metadata['channels'][$data['id']])) {
            unset($metadata['channels'][$data['id']]);
            $this->applicationService->updateMetadata($application, $metadata);
        }
        return $application->getMetadata();
    }

    /**
     * @param Application $application
     * @param $data
     * @return array|null
     */
    public function updateChannel(Application $application, $data): ?array
    {
        if (!empty($data['channel'])) {
            $channel = $data['channel'];
            $metadata = $application->getMetadata();
            if (!empty($channel['id']) && !empty($metadata['channels'][$channel['id']])) {
                $metadata['channels'][$channel['id']]['events'] = $channel['events'];
                $metadata['channels'][$channel['id']]['RGPD'] = $channel['RGPD'] ?? false;
                $this->applicationService->updateMetadata($application, $metadata);
            }
        }
        return $application->getMetadata();
    }

    /**
     * @param Application $application
     */
    public function enabled(Application $application): void
    {
        // TODO: Implement onEnable() method.
    }

    /**
     * @param Application $application
     * @throws Throwable
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function disabled(Application $application): void
    {
        $webhooks = $this->webhookService->listByOrganismsAndEventAndType(new ArrayCollection([$application->getOrganism()]), '*', ['type' => self::getAppId()]);
        foreach ($webhooks as $webhook) {
            $this->webhookService->update($webhook, ['enabled' => false]);
        }
    }


    /**
     * @param Application $application
     * @param array $previousMetadata
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function onUpdateMetadata(Application $application, array $previousMetadata): void
    {
        if (!empty($application->getMetadata()['channels'])) {
            foreach ($application->getMetadata()['channels'] as $id => $channelData) {
                if (!empty($previousMetadata['channels'][$id]) && sizeof(array_diff($channelData['events'], $previousMetadata['channels'][$id]['events'])) > 0) {
                    $webhook = $this->webhookRepository->findOneBy(['organism' => $application->getOrganism(), 'id' => $id]);
                    $this->webhookService->update($webhook, ['events' => $channelData['events'], 'options' => ['RGPD' => $channelData['RGPD']]]);
                    $this->logger->info("[SlackWedofApplication] webhook $id for channel " . $channelData['name'] . " changed from " . print_r($previousMetadata['channels'][$id]['events'], true) . " to " . print_r($channelData['events'], true));
                }
            }
        }
        if (!empty($previousMetadata['channels'])) {
            $ids = [];
            foreach ($previousMetadata['channels'] as $id => $channelData) {
                if (empty($application->getMetadata()['channels'][$id])) {
                    $ids[] = $id;
                }
            }
            //get from both sides to be sure
            $webhooks = $this->webhookRepository->findBy(['organism' => $application->getOrganism(), 'type' => 'slack', 'enabled' => true]);
            foreach ($webhooks as $webhook) {
                if (empty($application->getMetadata()['channels'][$webhook->getId()])) {
                    $ids[] = $webhook->getId();
                }
            }
            foreach ($ids as $id) {
                $webhook = $this->webhookRepository->findOneBy(['organism' => $application->getOrganism(), 'id' => $id]);
                $this->webhookService->update($webhook, ['enabled' => false]);
                $this->logger->info("[SlackWedofApplication] removed webhook for channel " . $channelData['name']);
            }
        }
    }

    /**
     * @param Application $application
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function onNewOAuth(Application $application): void
    {
        //retrieve incoming webhook
        $incomingWebhook = $application->getOauth2()['incoming_webhook'];
        $data = [
            'url' => $incomingWebhook['url'],
            'events' => ['*'],
            'type' => self::getAppId(),
            'options' => array('RGPD' => false)
        ];
        $webhook = $this->webhookService->create($data, $application->getOrganism());
        $metadata = $application->getMetadata();

        if (empty($metadata['channels'])) {
            $metadata['channels'] = [];
        }

        $channelData = [
            "name" => $incomingWebhook['channel'],
            "events" => $webhook->getEvents(),
            "RGPD" => false
        ];
        $metadata['channels'][$webhook->getId()] = $channelData;

        $this->logger->info("[SlackWedofApplication] webhook " . $webhook->getId() . " created for channel " . $channelData['name']);
        $this->applicationService->updateMetadata($application, $metadata);
    }

    /**
     * @param Application $application
     */
    public function onRefreshOAuth(Application $application): void
    {
        // TODO: Implement onRefreshOAuth() method.
    }

    /**
     * @return array
     */
    public static function getApplicationSubscribedEvents(): array
    {
        return [];
    }

    public function beforeEnable(Application $application): void
    {
        // TODO: Implement beforeEnable() method.
    }

    public function beforeDisable(Application $application): void
    {
        // TODO: Implement beforeDisable() method.
    }

    static public function getAllowedSubscriptionTypes(Subscription $subscription): array
    {
        return [
            'training' => SubscriptionTrainingTypes::getPaidTrainingTypesForApps(),
            'certifier' => SubscriptionCertifierTypes::getPaidCertifierTypes()
        ];
    }

    public function show(Application $application): void
    {
        // TODO: Implement onShow() method.
    }
}
