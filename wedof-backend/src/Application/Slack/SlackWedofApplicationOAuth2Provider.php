<?php

namespace App\Application\Slack;

use <PERSON><PERSON><PERSON><PERSON>\OAuth2\Client\Provider\Slack;
use App\Application\oauth2\WedofApplicationOAuth2Provider;
use App\Entity\Application;

class SlackWedofApplicationOAuth2Provider extends Slack implements WedofApplicationOAuth2Provider
{

    /**
     * SlackWedofApplicationOAuth2Provider constructor.
     * @param array $options
     * @param array $collaborators
     */
    public function __construct(array $options = [], array $collaborators = [])
    {
        parent::__construct($options, $collaborators);
    }

    /**
     * @param Application $application
     */
    public function configure(Application $application)
    {
    }

    /**
     * @return string[]
     */
    public function getDefaultScopes(): array
    {
        return SlackWedofApplication::getOAuth2Scopes();
    }
}
