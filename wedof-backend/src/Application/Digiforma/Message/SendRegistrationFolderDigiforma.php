<?php

namespace App\Application\Digiforma\Message;

class SendRegistrationFolderDigiforma
{
    private string $externalId;
    private string $eventName;
    private string $type;

    /**
     * @param string $externalId
     * @param string $eventName
     * @param string $type
     */
    public function __construct(string $externalId = "", string $eventName = "", string $type = "")
    {
        $this->externalId = $externalId;
        $this->eventName = $eventName;
        $this->type = $type;
    }

    public function getExternalId(): string
    {
        return $this->externalId;
    }

    public function getEventName(): string
    {
        return $this->eventName;
    }

    public function getType(): string
    {
        return $this->type;
    }
}