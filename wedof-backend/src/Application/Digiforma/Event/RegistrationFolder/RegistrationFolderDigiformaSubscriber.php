<?php

namespace App\Application\Digiforma\Event\RegistrationFolder;

use App\Application\Digiforma\Message\SendRegistrationFolderDigiforma;
use App\Entity\Application;
use App\Event\RegistrationFolder\RegistrationFolderEvents;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\enums\RegistrationFolderStates;
use App\MessageHandler\WithNextMessage;
use App\Service\ConnectionService;
use Datetime;
use Exception;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Throwable;

class RegistrationFolderDigiformaSubscriber extends WithNextMessage implements EventSubscriberInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    const DIGIFORMA_PROCESS_ONLY_CPF = true;

    private ConnectionService $connectionService;

    public function __construct(MessageBusInterface $messageBus, ConnectionService $connectionService)
    {
        parent::__construct($messageBus);
        $this->connectionService = $connectionService;
    }

    /**
     * @return array|string[]
     */
    public static function getSubscribedEvents(): array
    {
        $events = array(
            RegistrationFolderEvents::CREATED => 'createDigiformaEventOnRegistrationFolder',
            RegistrationFolderEvents::UPDATED => 'updateDigiformaEventOnRegistrationFolder',
        );

        foreach (RegistrationFolderStates::valuesStates() as $state) {
            if (!in_array($state, [
                RegistrationFolderStates::NOT_PROCESSED(),
                RegistrationFolderStates::ACCEPTED(),
                RegistrationFolderStates::VALIDATED(),
                RegistrationFolderStates::WAITING_ACCEPTATION(),
                RegistrationFolderStates::SERVICE_DONE_VALIDATED(),
            ])
            ) {
                $events["registrationFolder." . $state->getValue()] = 'updateStatusDigiformaEventOnRegistrationFolder';
            }
        }

        return $events;
    }

    /**
     * @param LoggerInterface $logger
     * @return void
     */
    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    /**
     * @param RegistrationFolderEvents $event
     * @param string $eventName
     * @return void
     * @throws Throwable
     */
    public function createDigiformaEventOnRegistrationFolder(RegistrationFolderEvents $event, string $eventName)
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }

        try {
            if (!$this->checkRegistrationFolderIsCpf($event)) {
                return;
            }
            if (!$this->checkApplicationAndConnexionIsActive($event)) {
                return;
            }
            if ($event->getRegistrationFolder()->getState() !== RegistrationFolderStates::NOT_PROCESSED()->getValue()) {
                return;
            }

            $this->messageBus->dispatch(new SendRegistrationFolderDigiforma($event->getRegistrationFolder()->getExternalId(), $eventName, "create"));
        } catch (Exception $e) {
            $this->logger->error("[Error sendDigiformaEventOnRegistrationFolder " . $event->getRegistrationFolder()->getExternalId() . "] at Line " . $e->getLine() . " " . $e->getFile() . " " . $e->getMessage());
        }
    }

    /**
     * @param RegistrationFolderEvents $event
     * @param string $eventName
     * @return void
     * @throws Throwable
     */
    public function updateDigiformaEventOnRegistrationFolder(RegistrationFolderEvents $event, string $eventName)
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }

        try {
            if (!$this->checkRegistrationFolderIsCpf($event)) {
                return;
            }
            if (!$this->registrationFolderHaveDigiformaTag($event)) {
                return;
            }
            if (!$this->checkApplicationAndConnexionIsActive($event)) {
                return;
            }

            //Pour éviter de s'abonner à l'événement update et au status RegistrationFolderStates::updatableStates
            //Sinon on avait 2 event qui sont triggered
            if (!in_array($event->getRegistrationFolder()->getState(), RegistrationFolderStates::updatableStatesToString())) {
                return;
            }

            $this->messageBus->dispatch(new SendRegistrationFolderDigiforma($event->getRegistrationFolder()->getExternalId(), $eventName, "update"));
        } catch (Exception $e) {
            $this->logger->error("[Error sendDigiformaEventOnRegistrationFolder " . $event->getRegistrationFolder()->getExternalId() . "] at Line " . $e->getLine() . " " . $e->getFile() . " " . $e->getMessage());
        }
    }

    /**
     * @throws Throwable
     */
    public function updateStatusDigiformaEventOnRegistrationFolder(RegistrationFolderEvents $event, string $eventName)
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }

        try {
            if (!$this->checkRegistrationFolderIsCpf($event)) {
                return;
            }
            if (!$this->registrationFolderHaveDigiformaTag($event)) {
                return;
            }
            if (!$this->checkApplicationAndConnexionIsActive($event)) {
                return;
            }

            $this->messageBus->dispatch(new SendRegistrationFolderDigiforma($event->getRegistrationFolder()->getExternalId(), $eventName, "updateStatus"));
        } catch (Exception $e) {
            $this->logger->error("[Error sendDigiformaEventOnRegistrationFolder " . $event->getRegistrationFolder()->getExternalId() . "] at Line " . $e->getLine() . " " . $e->getFile() . " " . $e->getMessage());
        }
    }

    /**
     * @param RegistrationFolderEvents $event
     * @return bool
     */
    private function checkRegistrationFolderIsCpf(RegistrationFolderEvents $event): bool
    {
        if (self::DIGIFORMA_PROCESS_ONLY_CPF && $event->getRegistrationFolder()->getType() !== DataProviders::CPF()->getValue()) {
            return false;
        }

        return true;
    }

    /**
     * @param RegistrationFolderEvents $event
     * @return bool
     * @throws TransportExceptionInterface
     */
    private function checkApplicationAndConnexionIsActive(RegistrationFolderEvents $event): bool
    {
        $organism = $event->getRegistrationFolder()->getOrganism();
        $connection = $organism->getConnectionForDataProvider(DataProviders::DIGIFORMA());
        $applications = array_values(array_filter($organism->getApplications()->toArray(), fn(Application $app) => $app->getAppId() === DataProviders::DIGIFORMA()->getValue() && $app->getEnabled() === true));
        if (empty($applications) || empty($connection)) {
            return false;
        }

        $digiformaApp = $applications[0];
        $appRawMetadata = $digiformaApp->getMetadata()['digiformaMetadata'] ?? [];
        if (is_null($connection->getExpiresOn()) || strtotime($connection->getExpiresOn()->format('Y-m-d')) < strtotime((new DateTime('now'))->format('Y-m-d'))) {
            $this->connectionService->authenticate($organism, DataProviders::DIGIFORMA(), $appRawMetadata);
        }

        return true;
    }

    private function registrationFolderHaveDigiformaTag(RegistrationFolderEvents $event): bool
    {
        if (in_array('digiforma', explode(',', $event->getRegistrationFolder()->getTagsText()))) {
            return true;
        }
        return false;
    }
}
