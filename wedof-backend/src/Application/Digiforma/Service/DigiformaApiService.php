<?php

namespace App\Application\Digiforma\Service;

use App\Entity\Application;
use App\Entity\Attendee;
use App\Entity\Connection;
use App\Entity\Organism;
use App\Entity\RegistrationFolder;
use App\Exception\ServerException;
use App\Exception\WedofConflictHttpException;
use App\Exception\WedofConnectionException;
use App\Library\utils\enums\ActivityTypes;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\enums\DigiformaCustomerCrmStatus;
use App\Library\utils\enums\DigiformaCustomerPipelineState;
use App\Library\utils\enums\DigiformaTrainingSessionPipelineState;
use App\Library\utils\enums\PaymentStates;
use App\Library\utils\enums\RegistrationFolderBillingStates;
use App\Library\utils\enums\RegistrationFolderStates;
use App\Library\utils\Tools;
use App\Repository\EndPointStatusRepository;
use App\Service\ActivityService;
use App\Service\ConfigService;
use App\Service\ConnectionService;
use App\Service\DataProviders\BaseApiService;
use App\Service\RegistrationFolderService;
use DateInterval;
use Datetime;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use ErrorException;
use Exception;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Security\Core\Security;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Throwable;

class DigiformaApiService extends BaseApiService
{
    const CREATED_BY_WEDOF = 'Créé automatiquement par Wedof';
    private ActivityService $activityService;
    private RegistrationFolderService $registrationFolderService;

    public function __construct(ConfigService $configService, ConnectionService $connectionService, RequestStack $requestStack, EndPointStatusRepository $endPointStatusRepository, EventDispatcherInterface $dispatcher, LoggerInterface $logger, ActivityService $activityService, RegistrationFolderService $registrationFolderService, Security $security)
    {
        parent::__construct(DataProviders::DIGIFORMA(), $configService, $connectionService, $requestStack, $endPointStatusRepository, $dispatcher, $logger, $security);
        $this->activityService = $activityService;
        $this->registrationFolderService = $registrationFolderService;
    }

    /**
     * @return int
     */
    public function getMaxAttemptsBeforeStop(): int
    {
        throw new WedofConflictHttpException("Pas d'implémentation de la fonction getMaxAttemptsBeforeStop dans DigiformaApiService");
    }

    /**
     * @param Connection $connection
     * @param bool $checkOrganismAccess
     * @return array
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    public function authenticate(Connection $connection, bool $checkOrganismAccess = true): array
    {
        $credentials = $connection->getCredentials();
        if (!isset($credentials['username'])) {
            throw new WedofConflictHttpException("Le login Digiforma n'est pas renseigné");
        }
        $result = $this->checkUsernamePassword($credentials['username'], $credentials['password']);
        if ($result) {
            $credentials['cookie'] = $result['cookie'];
            $credentials['csrf'] = $result['csrf'];
        } else {
            throw new WedofConflictHttpException("Le login ou le mot de passe Digiforma est invalide");
        }
        $connection->setCredentials($credentials);
        $result = $this->retrieveTokenWithCredentials($connection);
        if (isset($result["token"])) {
            $token = $result["token"];
            $result = $this->checkToken($token);
        } else {
            $token = null;
        }
        if (isset($result["errors"])) {
            if ($result["errors"] == "start-api-trial") {
                throw new WedofConflictHttpException($result["errors"]);
            } else if ($result["errors"] == "no-api") {
                throw new WedofConflictHttpException($result["errors"]);
            } else {
                throw new WedofConflictHttpException("Le token Digiforma est invalide");
            }
        } else {
            $credentials["token"] = $token;
            $connection->setCredentials($credentials);
            $this->connectionService->save($connection);
            return [
                "hasAccess" => true,
                "hasOrganismAccess" => true,
                "expiresOn" => (new DateTime('now'))->add(new DateInterval('P7D')),
                "refreshAt" => new DateTime('now'),
                "lastRefresh" => new DateTime('now'),
            ];
        }
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @return object
     */
    private function getConfigForProcess(RegistrationFolder $registrationFolder): object
    {

        $organism = $registrationFolder->getOrganism();
        $attendee = $registrationFolder->getAttendee();
        $applications = array_values(array_filter($organism->getApplications()->toArray(), fn(Application $app) => $app->getAppId() === DataProviders::DIGIFORMA()->getValue() && $app->getEnabled() === true));
        $digiformaApp = $applications[0];
        $appRawMetadata = $digiformaApp->getMetadata()['digiformaMetadata'] ?? [];
        $connection = $registrationFolder->getOrganism()->getConnectionForDataProvider(DataProviders::DIGIFORMA());
        $appMetadata = $this->getConfigFromMetadata($appRawMetadata);

        return (object)[
            'organism' => $organism,
            'attendee' => $attendee,
            'connection' => $connection,
            'appMetadata' => $appMetadata,
        ];
    }

    /**
     * @param Connection $connection
     * @param RegistrationFolder $registrationFolder
     * @param int $digiformaCustomerId
     * @return bool
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    public function addWedofLinkActionsDocument(Connection $connection, RegistrationFolder $registrationFolder, int $digiformaCustomerId): bool
    {
        $linkUrl = Tools::getEnvValue('WEDOF_BASE_URI') . '/formation/dossiers/kanban/' . $registrationFolder->getExternalId();
        $result = $this->sendRequest(
            Tools::getEnvValue('DIGIGORMA_APP_BASE_URI') . "ajax/documents/create",
            [
                'method' => 'POST',
                'fullResponse' => true,
                'headers' => [
                    'x-csrf-token' => $connection->getCredentials()['csrf']
                ],
                'cookie' => $connection->getCredentials()['cookie'],
                "content-type" => "application/x-www-form-urlencoded",
                'parameters' => [
                    'files' => '[{"type":"customer","customer_id":' . $digiformaCustomerId . ',"url":"' . $linkUrl . '", "filename":"Actions Wedof","mime":"text/html","size":1}]'
                ]
            ]
        );
        return $result['statusCode'] == 200;
    }

    /**
     * @param string $token
     * @return array
     * @throws ORMException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws OptimisticLockException
     * @throws ErrorException
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     * @throws Throwable
     */
    public function checkToken(string $token): array
    {
        $query = '{
                      managers(pagination: {page: 0, size: 1}) {
                        email
                        firstname
                        id
                        lastname
                        type
                      }
                    }';
        return $this->sendRequest(
            Tools::getEnvValue('DIGIGORMA_BASE_URI'),
            [
                'method' => 'POST',
                'headers' => [
                    'Authorization' => "Bearer $token"
                ],
                'parameters' => [
                    'query' => $query
                ]
            ]
        );
    }

    /**
     * @param $username
     * @param $password
     * @return array
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    private function checkUsernamePassword($username, $password): ?array
    {
        //get initial csrf
        $result_headers = $this->sendRequest(Tools::getEnvValue('DIGIGORMA_APP_BASE_URI') . "users/log_in", ['fullResponse' => true]);
        $csrf = Tools::getMetaTagsFromString($result_headers['content'], 'csrf');

        $result = $this->sendRequest(
            Tools::getEnvValue('DIGIGORMA_APP_BASE_URI') . "users/log_in",
            [
                'cookie' => explode(";", $result_headers['headers']['set-cookie'][0])[0],
                'method' => 'POST',
                'fullResponse' => true,
                "content-type" => "application/x-www-form-urlencoded",
                'parameters' => [
                    '_method' => 'POST',
                    '_csrf_token' => $csrf,
                    'email' => $username,
                    'password' => $password
                ]
            ]
        );
        if (isset($result['headers']['set-cookie']) && Tools::startsWith($result['headers']['set-cookie'][0], '_digiforma_key')) {
            return [
                'csrf' => Tools::getMetaTagsFromString($result['content'], 'csrf'),
                'cookie' => explode(";", $result['headers']['set-cookie'][0])[0],
            ];
        } else {
            return null;
        }
    }

    /**
     * @throws ORMException
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     * @throws OptimisticLockException
     * @throws TransportExceptionInterface
     * @throws Throwable
     * @throws ServerExceptionInterface
     * @throws NonUniqueResultException
     * @throws NoResultException
     * @throws ErrorException
     */
    public function retrieveTokenWithCredentials(Connection $connection): array
    {
        if (!isset($connection->getCredentials()['csrf']) || !isset($connection->getCredentials()['cookie'])) {
            return ["errors" => "invalid-credentials"];
        }
        $result = $this->sendRequest(
            Tools::getEnvValue('DIGIGORMA_APP_BASE_URI') . "academy/interconnection/zapier/",
            [
                'method' => 'GET',
                'fullResponse' => true,
                'headers' => [
                    'x-csrf-token' => $connection->getCredentials()['csrf']
                ],
                'cookie' => $connection->getCredentials()['cookie']
            ]
        );
        if ($result['statusCode'] == 200) {
            if (Tools::contains($result['content'], '<input class="text-input__input " type="text" value="')) {
                return ["token" => explode('" readonly x-on:focus=', explode('<input class="text-input__input " type="text" value="', $result['content'])[1])[0]];
            }
        }
        return ["errors" => "start-api-trial"];
    }

    /**
     * @throws ORMException
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     * @throws WedofConnectionException
     * @throws OptimisticLockException
     * @throws TransportExceptionInterface
     * @throws Throwable
     * @throws ServerExceptionInterface
     * @throws NonUniqueResultException
     * @throws NoResultException
     * @throws ErrorException
     */
    public function retrieveCompletionRate(Connection $connection, string $trainingSessionId): int
    {
        $result = $this->sendRequest(
            Tools::getEnvValue('DIGIGORMA_APP_BASE_URI') . "training_sessions/$trainingSessionId/trainee_support",
            [
                'method' => 'GET',
                'fullResponse' => true,
                'headers' => [
                    'x-csrf-token' => $connection->getCredentials()['csrf']
                ],
                'cookie' => $connection->getCredentials()['cookie']
            ]
        );
        if ($result['statusCode'] == 200) {
            if (Tools::contains($result['content'], "<div class=\"success-true font-bold\">")) {
                return intval(preg_replace("/[\n|\r]/", "", explode("</div>", explode("success-true font-bold\">", $result['content'])[1])[0]));
            }
        }
        return 0;
    }

    /**
     * @param Organism $organism
     * @param array|null $params
     * @return mixed
     */
    public function checkBeforeHabilitate(Organism $organism, array $params = null)
    {
        throw new WedofConflictHttpException("Pas d'implémentation de la fonction checkBeforeHabilitate dans DigiformaApiService");
    }

    /**
     * @param Organism $organism
     * @param Connection $connection
     * @param array $params
     * @return bool
     */
    public function habilitate(Organism $organism, Connection $connection, array $params): bool
    {
        throw new WedofConflictHttpException("Pas d'implémentation de la fonction habilitate dans DigiformaApiService");
    }

    /**
     * @param Connection $connection
     * @return string
     */
    public function getUsername(Connection $connection): string
    {
        return '';
    }

    /**
     * @return bool
     */
    public function requiresAuthentication(): bool
    {
        return true;
    }

    /**
     * @param Organism $organism
     * @return bool
     */
    public function setActiveOrganism(Organism $organism): bool
    {
        throw new WedofConflictHttpException("Pas d'implémentation de la fonction setActiveOrganism dans DigiformaApiService");
    }

    /**
     * @param array $options
     * @return array
     */
    public function setAuthentication(array $options = []): array
    {
        throw new WedofConflictHttpException("Pas d'implémentation de la fonction setAuthentication dans DigiformaApiService");
    }

    /**
     * @throws ORMException
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     * @throws OptimisticLockException
     * @throws ServerException
     * @throws TransportExceptionInterface
     * @throws Throwable
     * @throws ServerExceptionInterface
     * @throws NonUniqueResultException
     * @throws NoResultException
     * @throws ErrorException
     */
    public function pullFromDigiformaRegistrationFolderCompletionRate(RegistrationFolder $registrationFolder)
    {
        $configProcess = $this->getConfigForProcess($registrationFolder);

        $customerDigiforma = $this->getCustomer($configProcess->connection, $registrationFolder);
        $trainingSessionId = $customerDigiforma->trainingSession["id"];

        $registrationFoldercompletionRate = $this->retrieveCompletionRate($configProcess->connection, $trainingSessionId);

        if ($registrationFoldercompletionRate != $registrationFolder->getCompletionRate()) {
            $data = [
                "completionRate" => $registrationFoldercompletionRate
            ];
            $this->registrationFolderService->update($registrationFolder, $data, false);
        }
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @param string $eventName
     * @return void
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws \Symfony\Component\Mailer\Exception\TransportExceptionInterface
     */
    public function processCreateDigiforma(RegistrationFolder $registrationFolder, string $eventName)
    {
        try {
            $this->logger->debug("[Digiforma processCreateDigiforma] " . self::class . " event $eventName");

            $configProcess = $this->getConfigForProcess($registrationFolder);

            $fundingAgencyDigiforma = $this->processCreateFundingAgency($configProcess->connection);
            $traineeDigiforma = $this->processCreateTrainee($configProcess->connection, $configProcess->attendee);
            $sessionDigiforma = $this->processCreateTrainingSession($configProcess->appMetadata, $configProcess->connection, $registrationFolder);
            $this->processCreateCustomer($configProcess->appMetadata, $configProcess->connection, $registrationFolder, $traineeDigiforma, $sessionDigiforma, $fundingAgencyDigiforma);

            $data['tags'] = array_merge($registrationFolder->getTagNames(), ['digiforma']);
            $this->registrationFolderService->update($registrationFolder, $data, false);
        } catch (Exception $e) {
            $this->logger->error("[Error processCreateDigiforma " . $registrationFolder->getExternalId() . "] at Line " . $e->getLine() . " " . $e->getFile() . " " . $e->getMessage());
        }
    }


    /**
     * @throws \Symfony\Component\Mailer\Exception\TransportExceptionInterface
     * @throws ClientExceptionInterface
     * @throws Throwable
     * @throws ServerExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws TransportExceptionInterface
     */
    public function processUpdateDigiforma(RegistrationFolder $registrationFolder, string $eventName)
    {
        try {
            $this->logger->debug("[Digiforma processUpdateDigiforma] " . self::class . " event $eventName");

            $configProcess = $this->getConfigForProcess($registrationFolder);

            $customerDigiforma = $this->getCustomer($configProcess->connection, $registrationFolder);

            $trainingSessionId = $customerDigiforma->trainingSession["id"];
            $sessionDigiforma = $customerDigiforma->trainingSession;
            $trainingSessionPipelineState = $customerDigiforma->trainingSession["pipelineState"];
            $traineeId = $customerDigiforma->customerTrainees[0]['trainee']["id"];

            if (!$configProcess->appMetadata->DIGIFORMA_CREATE_TRAINEE_ONLY) {
                $this->processUpdateTrainee($configProcess->connection, $configProcess->attendee, $traineeId);
            }
            if (!$configProcess->appMetadata->DIGIFORMA_CREATE_TRAINING_SESSION_ONLY) {
                $sessionDigiforma = $this->processUpdateTrainingSession($configProcess->appMetadata, $configProcess->connection, $registrationFolder, $trainingSessionId, $trainingSessionPipelineState);
                $this->processUpdateSubsession($configProcess->appMetadata, $configProcess->connection, $registrationFolder, $sessionDigiforma);
            }
            if (!$configProcess->appMetadata->DIGIFORMA_CREATE_CUSTOMER_ONLY) {
                $this->processUpdateCustomer($configProcess->appMetadata, $configProcess->connection, $registrationFolder, $traineeId, $trainingSessionId, $customerDigiforma);
            }
            if ($configProcess->appMetadata->DIGIFORMA_PROCESS_INVOICES && in_array($registrationFolder->getBillingState(), [
                    RegistrationFolderBillingStates::TO_BILL()->getValue(),
                    RegistrationFolderBillingStates::BILLED()->getValue(),
                    RegistrationFolderBillingStates::PAID()->getValue()
                ])) {
                $invoiceDigiforma = $this->processInvoice($configProcess->appMetadata, $configProcess->connection, $registrationFolder, $customerDigiforma, $traineeId, $sessionDigiforma);
                if ($registrationFolder->getBillingState() === RegistrationFolderBillingStates::PAID()->getValue()) {
                    $this->processInvoicePayment($configProcess->connection, $registrationFolder, $invoiceDigiforma);
                }
            }
        } catch (Exception $e) {
            $this->logger->error("[Error processUpdateDigiforma " . $registrationFolder->getExternalId() . "] at Line " . $e->getLine() . " " . $e->getFile() . " " . $e->getMessage());
        }
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @param string $eventName
     * @return void
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    public function processUpdateStatusDigiforma(RegistrationFolder $registrationFolder, string $eventName)
    {
        try {
            $this->logger->debug("[Digiforma processUpdateStatusDigiforma] " . self::class . " event $eventName");

            $configProcess = $this->getConfigForProcess($registrationFolder);

            $customerDigiforma = $this->getCustomer($configProcess->connection, $registrationFolder);

            $customerDigiformaId = $customerDigiforma->id;
            $trainingSessionId = $customerDigiforma->trainingSession["id"];

            $crmStatus = $this->getCrmStatusCustomer($configProcess->appMetadata, $registrationFolder);
            $customerPipelineState = $this->getPipelineStateCustomer($configProcess->appMetadata, $registrationFolder);
            $trainingSessionPipelineState = $this->getPipelineStateSession($configProcess->appMetadata, $registrationFolder);

            $this->setCustomerCrmStatus($configProcess->connection, $customerDigiformaId, $crmStatus);
            $this->setCustomerPipelineState($configProcess->connection, $customerDigiformaId, $customerPipelineState);
            $this->setTrainingSessionPipelineState($configProcess->connection, $trainingSessionId, $trainingSessionPipelineState);

        } catch (Exception $e) {
            $this->logger->error("[Error processUpdateStatusDigiforma " . $registrationFolder->getExternalId() . "] at Line " . $e->getLine() . " " . $e->getFile() . " " . $e->getMessage());
        }
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @return string
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    private function getCustomerIdFromRegistrationFolder(RegistrationFolder $registrationFolder): string
    {
        $activities = $this->activityService->listByEntityReturnQueryBuilder(
            $registrationFolder->getExternalId(),
            Tools::getClassName($registrationFolder),
            $registrationFolder, ["type" => ['create']])->getQuery()->execute();

        return explode('/', $activities[0]->getLink())[4];
    }

    /**
     * @param array $metadata
     * @return object
     */
    public function getConfigFromMetadata(array $metadata): object
    {
        $configMappingStatus = [];
        foreach (RegistrationFolderStates::valuesStates() as $registrationFolderState) {
            $registrationFolderStateValue = $registrationFolderState->getValue();
            $configMappingStatus[$registrationFolderStateValue] = $metadata[$registrationFolderStateValue] ?? $this->getPipelineStateCustomerDefault($registrationFolderStateValue);
        }

        return (object)[
            "DIGIFORMA_CUSTOMER_CRM_STATUS_ACTIVE" => $metadata['customer_crm_status_active'] ?? true,
            "DIGIFORMA_CUSTOMER_PIPELINE_STATE_ACTIVE" => $metadata['customer_pipeline_state_active'] ?? true,
            "DIGIFORMA_TRAINING_SESSION_PIPELINE_STATE_ACTIVE" => $metadata['training_session_pipeline_state_active'] ?? true,
            "DIGIFORMA_CREATE_TRAINEE_ONLY" => $metadata['create_trainee_only'] ?? true,
            "DIGIFORMA_CREATE_TRAINING_SESSION_ONLY" => $metadata['create_training_session_only'] ?? false,//remettre à false quand on aura trouvé un process d'update correcte
            "DIGIFORMA_CREATE_SUBSESSION_ONLY" => $metadata['create_subsession_only'] ?? false,//remettre à false quand on aura trouvé un process d'update correcte
            "DIGIFORMA_CREATE_CUSTOMER_ONLY" => $metadata['create_customer_only'] ?? false,
            "DIGIFORMA_CREATE_INVOICE_ONLY" => $metadata['create_invoice_only'] ?? true,
            "DIGIFORMA_PROCESS_INVOICES" => $metadata['process_invoice'] ?? false,
            "WEDOF_DIGIFORMA_MAPPING_REGISTRATION_FOLDER_STATE" => $configMappingStatus
        ];
    }

    /**
     * @param Connection $connection
     * @param Attendee $attendee
     * @return mixed
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    private function processCreateTrainee(Connection $connection, Attendee $attendee)
    {
        $filter = 'filter: { email : "' . strtolower($attendee->getEmail()) . '" }';
        $result = $this->getTrainees($connection, $filter);

        $result = $result['data']['trainees'];
        $address = $attendee->getAddress();
        $roadAddress = !empty($address) ? '"' . $attendee->getFullAddress() . '"' : 'null';

        $attendeeToInsertOrUpdate = (object)[
            "id" => null,
            "birthCity" => $attendee->getNameCityOfBirth() ? "\"" . $attendee->getNameCityOfBirth() . "\"" : "null",
            "birthCityCode" => $attendee->getCodeCityOfBirth() ? "\"" . $attendee->getCodeCityOfBirth() . "\"" : "null",
            "birthName" => $attendee->getBirthName() ? "\"" . $attendee->getBirthName() . "\"" : "null",
            "birthdate" => $attendee->getDateOfBirth() ? "\"" . $attendee->getDateOfBirth()->format('Y-m-d') . "\"" : "null",
            "city" => !empty($address['city']) ? "\"" . $address['city'] . "\"" : "null",
            "cityCode" => !empty($address['zipCode']) ? "\"" . $address['zipCode'] . "\"" : "null",
            "civility" => $attendee->getGender() ? "\"" . ($attendee->getGender() === 'male' ? 'M' : 'Mme') . "\"" : "null",
            "code" => $attendee->getEmail() ? "\"" . $attendee->getEmail() . "\"" : "null",
            "companyName" => !empty($address['corporateName']) ? "\"" . $address['corporateName'] . "\"" : "null",
            "country" => !empty($address['country']) ? "\"" . $address['country'] . "\"" : "null",
            "countryCode" => !empty($address['countryCode']) ? "\"" . $address['countryCode'] . "\"" : "null",
            "email" => $attendee->getEmail() ? "\"" . strtolower($attendee->getEmail()) . "\"" : "null",
            "firstname" => $attendee->getFirstName() ? "\"" . $attendee->getFirstName() . "\"" : "null",
            "lastname" => $attendee->getLastName() ? "\"" . $attendee->getLastName() . "\"" : "null",
            "nationality" => $attendee->getNameCountryOfBirth() ? "\"" . $attendee->getNameCountryOfBirth() . "\"" : "null",
            "phone" => $attendee->getPhoneNumber() ? "\"" . $attendee->getPhoneNumber() . "\"" : "null",
            "phoneSecondary" => $attendee->getPhoneFixed() ? "\"" . $attendee->getPhoneFixed() . "\"" : "null",
            "roadAddress" => $roadAddress,
        ];
        if (sizeof($result) !== 0) {
            return $result[0];
        }
        $result = $this->createTrainee($connection, $attendeeToInsertOrUpdate);

        return $result['data']["createTrainee"];
    }


    /**
     * @throws ORMException
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     * @throws OptimisticLockException
     * @throws TransportExceptionInterface
     * @throws Throwable
     * @throws ServerExceptionInterface
     * @throws NonUniqueResultException
     * @throws NoResultException
     * @throws ErrorException
     */
    private function processUpdateTrainee(Connection $connection, Attendee $attendee, string $traineeId): void
    {
        $address = $attendee->getAddress();
        $roadAddress = !empty($address) ? '"' . $attendee->getFullAddress() . '"' : 'null';

        $attendeeToUpdate = (object)[
            "id" => null,
            "birthCity" => $attendee->getNameCityOfBirth() ? "\"" . $attendee->getNameCityOfBirth() . "\"" : "null",
            "birthCityCode" => $attendee->getCodeCityOfBirth() ? "\"" . $attendee->getCodeCityOfBirth() . "\"" : "null",
            "birthName" => $attendee->getBirthName() ? "\"" . $attendee->getBirthName() . "\"" : "null",
            "birthdate" => $attendee->getDateOfBirth() ? "\"" . $attendee->getDateOfBirth()->format('Y-m-d') . "\"" : "null",
            "city" => !empty($address['city']) ? "\"" . $address['city'] . "\"" : "null",
            "cityCode" => !empty($address['zipCode']) ? "\"" . $address['zipCode'] . "\"" : "null",
            "civility" => $attendee->getGender() ? "\"" . ($attendee->getGender() === 'male' ? 'M' : 'Mme') . "\"" : "null",
            "code" => $attendee->getEmail() ? "\"" . $attendee->getEmail() . "\"" : "null",
            "companyName" => !empty($address['corporateName']) ? "\"" . $address['corporateName'] . "\"" : "null",
            "country" => !empty($address['country']) ? "\"" . $address['country'] . "\"" : "null",
            "countryCode" => !empty($address['countryCode']) ? "\"" . $address['countryCode'] . "\"" : "null",
            "email" => $attendee->getEmail() ? "\"" . strtolower($attendee->getEmail()) . "\"" : "null",
            "firstname" => $attendee->getFirstName() ? "\"" . $attendee->getFirstName() . "\"" : "null",
            "lastname" => $attendee->getLastName() ? "\"" . $attendee->getLastName() . "\"" : "null",
            "nationality" => $attendee->getNameCountryOfBirth() ? "\"" . $attendee->getNameCountryOfBirth() . "\"" : "null",
            "phone" => $attendee->getPhoneNumber() ? "\"" . $attendee->getPhoneNumber() . "\"" : "null",
            "phoneSecondary" => $attendee->getPhoneFixed() ? "\"" . $attendee->getPhoneFixed() . "\"" : "null",
            "roadAddress" => $roadAddress,
        ];

        $this->updateTrainee($connection, $traineeId, $attendeeToUpdate);
    }

    /**
     * @param Connection $connection
     * @param string $filter
     * @return array
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    private function getTrainees(Connection $connection, string $filter = "filter :{}"): array
    {
        $query = '{
                        trainees(' . $filter . ', pagination: {page: 0, size: 100}) {
                            birthCity
                            birthCityCode
                            birthName
                            birthdate
                            city
                            cityCode
                            civility
                            code
                            companyName
                            country
                            countryCode
                            email
                            firstname
                            id
                            lastname
                            nationality
                            note
                            phone
                            phoneSecondary
                            position
                            roadAddress
                          }
                    }';
        return $this->postToDigiforma($connection, $query, "trainees");
    }

    /**
     * @param Connection $connection
     * @param string $query
     * @param string|null $endpoint
     * @return array
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    private function postToDigiforma(Connection $connection, string $query, string $endpoint = null): array
    {
        if ($this->connectionService->hasAccess($connection->getOrganism(), DataProviders::DIGIFORMA())) {
            $result = $this->sendRequest(
                Tools::getEnvValue('DIGIGORMA_BASE_URI'),
                [
                    'method' => 'POST',
                    'headers' => [
                        'Authorization' => 'Bearer ' . $connection->getCredentials()['token']
                    ],
                    'parameters' => [
                        'query' => $query
                    ]
                ]
            );
            $this->handleErrorGraphQl($result, $endpoint, $query);
            return $result;
        }
        throw new Exception("Accès connexion Digiforma impossible");
    }

    /**
     * @param array $result
     * @param string $function
     * @param string $query
     * @return void
     * @throws Exception
     */
    private function handleErrorGraphQl(array $result, string $function, string $query)
    {
        if (isset($result['errors'])) {
            throw new Exception("Fonction : " . $function . " Erreur : " . json_encode($result['errors']) . " Query : " . $query);
        }
        if (isset($result['data']['error'])) {
            throw new Exception("Fonction : " . $function . " Erreur : " . json_encode($result['data']['error']) . " Query : " . $query);
        }
    }

    /**
     * @param Connection $connection
     * @param object $attendee
     * @return array
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    private function createTrainee(Connection $connection, object $attendee): array
    {
        $query = "mutation { 
                       createTrainee(traineeInput: {
                          birthCity:$attendee->birthCity, 
                          birthCityCode: $attendee->birthCityCode, 
                          birthName: $attendee->birthName, 
                          birthdate: $attendee->birthdate, 
                          city: $attendee->city, 
                          cityCode: $attendee->cityCode, 
                          civility: $attendee->civility, 
                          code: $attendee->code, 
                          companyName: $attendee->companyName, 
                          country: $attendee->country, 
                          countryCode: $attendee->countryCode, 
                          email: $attendee->email, 
                          firstname: $attendee->firstname, 
                          lastname: $attendee->lastname, 
                          nationality: $attendee->nationality, 
                          phone: $attendee->phone, 
                          phoneSecondary: $attendee->phoneSecondary, 
                          roadAddress: $attendee->roadAddress, 
                          }) {
                        birthCity
                        birthCityCode
                        birthName
                        birthdate
                        city
                        cityCode
                        civility
                        code
                        companyName
                        country
                        countryCode
                        email
                        firstname
                        id
                        lastname
                        nationality
                        note
                        phone
                        phoneSecondary
                        position
                        roadAddress
                      }
                     }";
        return $this->postToDigiforma($connection, $query, "createTrainee");
    }

    /**
     * @throws ORMException
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     * @throws OptimisticLockException
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws NonUniqueResultException
     * @throws NoResultException
     * @throws ErrorException
     */
    private function updateTrainee(Connection $connection, string $traineeId, object $attendee): void
    {
        $query = "mutation { 
                       updateTrainee(id:$traineeId, traineeInput: {
                          birthCity:$attendee->birthCity, 
                          birthCityCode: $attendee->birthCityCode, 
                          birthName: $attendee->birthName, 
                          birthdate: $attendee->birthdate, 
                          city: $attendee->city, 
                          cityCode: $attendee->cityCode, 
                          civility: $attendee->civility, 
                          code: $attendee->code, 
                          companyName: $attendee->companyName, 
                          country: $attendee->country, 
                          countryCode: $attendee->countryCode, 
                          email: $attendee->email, 
                          firstname: $attendee->firstname, 
                          lastname: $attendee->lastname, 
                          nationality: $attendee->nationality, 
                          phone: $attendee->phone, 
                          phoneSecondary: $attendee->phoneSecondary, 
                          roadAddress: $attendee->roadAddress, 
                          }) {
                        birthCity
                        birthCityCode
                        birthName
                        birthdate
                        city
                        cityCode
                        civility
                        code
                        companyName
                        country
                        countryCode
                        email
                        firstname
                        id
                        lastname
                        nationality
                        note
                        phone
                        phoneSecondary
                        position
                        roadAddress
                      }
                     }";
        $this->postToDigiforma($connection, $query, "updateTrainee");
    }

    /**
     * @param object $appMedatada
     * @param Connection $connection
     * @param RegistrationFolder $registrationFolder
     * @return mixed
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    private function processCreateTrainingSession(object $appMedatada, Connection $connection, RegistrationFolder $registrationFolder)
    {
        $training = $registrationFolder->getSession()->getTrainingAction()->getTraining();
        $filter = 'filters: { name : "' . addslashes($training->getTitle()) . " (" . $registrationFolder->getExternalId() . ')" }';

        $result = $this->getTrainingSession($connection, $filter);

        $result = $result['data']['trainingSessions'];
        $managers = $this->getManagers($connection)["data"]["managers"];
        $onwerManager = null;
        foreach ($managers as $manager) {
            if ($manager['type'] === 'owner') {
                $onwerManager = $manager;
                break;
            }
        }
        $organism = $registrationFolder->getOrganism();
        $certification = $registrationFolder->getCertification();
        $trainingSessionToInsert = (object)[
            "cityCode" => $organism->getPostalCode() ? "\"" . $organism->getPostalCode() . "\"" : "null",
            "code" => $registrationFolder->getExternalId() ? "\"" . $registrationFolder->getExternalId() . "\"" : "null",
            "contracted" => "false",
            "datesAreInterval" => "true",
            "diploma" => "\"none\"", // TODO find out what the enum values are
            "diplomaTitle" => $certification ? "\"" . addslashes($certification->getName()) . "\"" : "null",
            "dpc" => "false",
            "inter" => "true",
            "managerId" => $onwerManager['id'],
            "name" => $training->getTitle() ? "\"" . addslashes($training->getTitle()) . " (" . $registrationFolder->getExternalId() . ")\"" : "null",
            "place" => "null",
            "placeName" => "null",
            "qualityAnalysis" => "null",
            "qualityExpectations" => "null",
            "qualitySuccessConditions" => "null",
            "remote" => "false",
            "secondManagerId" => "null",
            "showDatesInExtranet" => "true",
            "showPlaceInExtranet" => "true",
            "showProgramInExtranet" => "true",
            "showRulesInExtranet" => "true",
            "showSigningButtonInExtranet" => "true",
            "showTraineePedagogicalTrackingInExtranet" => "true",
            "showTraineesInExtranet" => "false",
            "specialty" => "null",
            "timezone" => "\"Europe/Paris\"",
            "trainingType" => "\"Action de formation\"",
            "type" => "\"bpf\"",
            "useMap" => "true",
            "vaeAdmissibilityDate" => "null",
            "pipelineState" => "\"" . $this->getPipelineStateSession($appMedatada, $registrationFolder) . "\"",
            "subsession" => $this->getSubsessionInput($registrationFolder),
        ];
        if (sizeof($result) !== 0) {
            return $result[0];
        }
        $result = $this->createTrainingSession($connection, $trainingSessionToInsert);

        return $result['data']["createTrainingSession"];
    }


    /**
     * @param object $appMedatada
     * @param Connection $connection
     * @param RegistrationFolder $registrationFolder
     * @param string $trainingSessionId
     * @param string $oldTrainingSessionPipelineState
     * @return mixed
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    private function processUpdateTrainingSession(object $appMedatada, Connection $connection, RegistrationFolder $registrationFolder, string $trainingSessionId, string $oldTrainingSessionPipelineState)
    {
        $organism = $registrationFolder->getOrganism();
        $training = $registrationFolder->getSession()->getTrainingAction()->getTraining();

        $trainingSessionToUpdate = (object)[
            "cityCode" => $organism->getPostalCode() ? "\"" . $organism->getPostalCode() . "\"" : "null",
            "code" => $registrationFolder->getExternalId() ? "\"" . $registrationFolder->getExternalId() . "\"" : "null",
            "name" => $training->getTitle() ? "\"" . addslashes($training->getTitle()) . " (" . $registrationFolder->getExternalId() . ")\"" : "null",
            "pipelineState" => "\"" . $this->getPipelineStateSession($appMedatada, $registrationFolder, $oldTrainingSessionPipelineState) . "\"",
        ];

        $result = $this->updateTrainingSession($connection, $trainingSessionId, $trainingSessionToUpdate);

        return $result['data']["updateTrainingSession"];
    }

    /**
     * @param Connection $connection
     * @param string $filter
     * @return array
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    private function getTrainingSession(Connection $connection, string $filter = "filters :{}"): array
    {
        $query = '{
                        trainingSessions(' . $filter . ', pagination: {page: 0, size: 100}) {
                            averageDurationPerDate
                            cityCode
                            code
                            codeFundae
                            contracted
                            diploma
                            diplomaTitle
                            dpc
                            inter
                            name
                            pipelineState
                            place
                            placeName
                            qualityAnalysis
                            qualityExpectations
                            qualitySuccessConditions
                            remote
                            room {
                              id
                            }
                            showDatesInExtranet
                            showPlaceInExtranet
                            showProgramInExtranet
                            showRulesInExtranet
                            showSigningButtonInExtranet
                            showTraineePedagogicalTrackingInExtranet
                            showTraineesInExtranet
                            specialty
                            subsessions {
                              name
                              id
                              attendanceFromModules
                              averageDurationPerDate
                              color
                              costs {
                                cost
                                costMode
                                costIndividual
                                costIndependant
                                description
                                monthly
                                type
                                vat
                              }
                              dates {
                                date
                                endTime
                                room {
                                  name
                                }
                                slot
                                startTime
                              }
                              datesAreInterval
                              durationDays
                              durationHours
                              modality
                              name
                            }
                            id
                            trainees{
                                id
                            }
                            timezone
                            trainingType
                            type
                            useMap
                            vaeAdmissibilityDate
                            invoices {
                                date
                                freeText
                                id
                                invoicePayments {
                                    amount 
                                    date 
                                    freeText 
                                    id 
                                    mode 
                                    stripeId
                                }
                                items {
                                    description 
                                    id 
                                    name 
                                    quantity 
                                    type 
                                    unitPrice 
                                    vat
                                }
                                number
                                prefix
                                recipient {
                                    __typename
                                }
                            }
                          }
                    }';
        return $this->postToDigiforma($connection, $query, "trainingSessions");
    }

    /**
     * @param Connection $connection
     * @return array
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    private function getManagers(Connection $connection): array
    {
        $query = '{
                      managers(pagination: {page: 0, size: 100}) {
                        email
                        firstname
                        id
                        lastname
                        type
                      }
                    }';
        return $this->postToDigiforma($connection, $query, "managers");
    }

    /**
     * @param object $appMedatada
     * @param RegistrationFolder $registrationFolder
     * @param string|null $oldPipelineState
     * @return string
     */
    private function getPipelineStateSession(object $appMedatada, RegistrationFolder $registrationFolder, string $oldPipelineState = null): string
    {
        if (!$appMedatada->DIGIFORMA_TRAINING_SESSION_PIPELINE_STATE_ACTIVE && !is_null($oldPipelineState)) {
            return $oldPipelineState;
        }
        switch ($registrationFolder->getState()) {
            case RegistrationFolderStates::ACCEPTED()->getValue():
            case RegistrationFolderStates::VALIDATED()->getValue():
            case RegistrationFolderStates::IN_TRAINING()->getValue():
                return DigiformaTrainingSessionPipelineState::ONGOING()->getValue();
            case RegistrationFolderStates::WAITING_ACCEPTATION()->getValue():
                return DigiformaTrainingSessionPipelineState::INCOMPLETE()->getValue();
            case RegistrationFolderStates::SERVICE_DONE_DECLARED()->getValue():
            case RegistrationFolderStates::SERVICE_DONE_VALIDATED()->getValue():
            case RegistrationFolderStates::CANCELED_BY_ATTENDEE()->getValue():
            case RegistrationFolderStates::CANCELED_BY_ATTENDEE_NOT_REALIZED()->getValue():
            case RegistrationFolderStates::CANCELED_BY_ORGANISM()->getValue():
            case RegistrationFolderStates::REFUSED_BY_ATTENDEE()->getValue():
            case RegistrationFolderStates::REFUSED_BY_ORGANISM()->getValue():
            case RegistrationFolderStates::REJECTED_WITHOUT_TITULAIRE_SUITE()->getValue():
            case RegistrationFolderStates::REJECTED()->getValue():
            case RegistrationFolderStates::REJECTED_WITHOUT_CDC_SUITE()->getValue():
            case RegistrationFolderStates::REJECTED_WITHOUT_OF_SUITE()->getValue():
            case RegistrationFolderStates::REFUSED()->getValue():
            case RegistrationFolderStates::TERMINATED()->getValue():
                return DigiformaTrainingSessionPipelineState::FINISHED()->getValue();
            case RegistrationFolderStates::NOT_PROCESSED()->getValue():
                return DigiformaTrainingSessionPipelineState::DRAFT()->getValue();
            default:
                return $oldPipelineState;
        }
    }

    /**
     * @param Connection $connection
     * @param object $registrationFolder
     * @return array
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    private function createTrainingSession(Connection $connection, object $registrationFolder): array
    {
        $querySubsessionInput =
            "subsessions:[{
        " . $this->getSubsessionQueryInput($registrationFolder->subsession, "createSubsession") . "
                            }]";

        $query = "mutation { 
                        createTrainingSession(trainingSessionInput:{ 
                            cityCode:$registrationFolder->cityCode,
                            code:$registrationFolder->code,
                            contracted:$registrationFolder->contracted,
                            datesAreInterval:$registrationFolder->datesAreInterval,
                            diploma:$registrationFolder->diploma,
                            diplomaTitle:$registrationFolder->diplomaTitle,
                            dpc:$registrationFolder->dpc,
                            inter:$registrationFolder->inter,
                            managerId:$registrationFolder->managerId,
                            name:$registrationFolder->name,
                            pipelineState:$registrationFolder->pipelineState,
                            place:$registrationFolder->place,
                            placeName:$registrationFolder->placeName,
                            qualityAnalysis:$registrationFolder->qualityAnalysis,
                            qualityExpectations:$registrationFolder->qualityExpectations,
                            qualitySuccessConditions:$registrationFolder->qualitySuccessConditions,
                            remote:$registrationFolder->remote,
                            secondManagerId:$registrationFolder->secondManagerId,
                            showDatesInExtranet:$registrationFolder->showDatesInExtranet,
                            showPlaceInExtranet:$registrationFolder->showPlaceInExtranet,
                            showProgramInExtranet:$registrationFolder->showProgramInExtranet,
                            showRulesInExtranet:$registrationFolder->showRulesInExtranet,
                            showSigningButtonInExtranet:$registrationFolder->showSigningButtonInExtranet,
                            showTraineePedagogicalTrackingInExtranet:$registrationFolder->showTraineePedagogicalTrackingInExtranet,
                            showTraineesInExtranet:$registrationFolder->showTraineesInExtranet,
                            specialty:$registrationFolder->specialty,
                            timezone:$registrationFolder->timezone,
                            trainingType:$registrationFolder->trainingType,
                            type:$registrationFolder->type,
                            useMap:$registrationFolder->useMap,
                            vaeAdmissibilityDate:$registrationFolder->vaeAdmissibilityDate,
                            $querySubsessionInput
                          }){
                            averageDurationPerDate
                            cityCode
                            id
                            code
                            codeFundae
                            contracted
                            diploma
                            diplomaTitle
                            dpc
                            inter
                            name
                            pipelineState
                            place
                            placeName
                            qualityAnalysis
                            qualityExpectations
                            qualitySuccessConditions
                            remote
                            room {
                              id
                            }
                            showDatesInExtranet
                            showPlaceInExtranet
                            showProgramInExtranet
                            showRulesInExtranet
                            showSigningButtonInExtranet
                            showTraineePedagogicalTrackingInExtranet
                            showTraineesInExtranet
                            specialty
                            subsessions {
                              name
                              attendanceFromModules
                              averageDurationPerDate
                              color
                              id
                              costs {
                                cost
                                costMode
                                costIndividual
                                costIndependant
                                description
                                monthly
                                type
                                vat
                              }
                              dates {
                                date
                                endTime
                                room {
                                  name
                                }
                                slot
                                startTime
                              }
                              datesAreInterval
                              durationDays
                              durationHours
                              modality
                              name
                            }
                            timezone
                            trainingType
                            type
                            useMap
                            vaeAdmissibilityDate
                            invoices {
                                date
                                freeText
                                id
                                invoicePayments {
                                    amount 
                                    date 
                                    freeText 
                                    id 
                                    mode 
                                    stripeId
                                }
                                items {
                                    description 
                                    id 
                                    name 
                                    quantity 
                                    type 
                                    unitPrice 
                                    vat
                                }
                                number
                                prefix
                                recipient {
                                    __typename
                                }
                            }
                          }  
                     }";
        return $this->postToDigiforma($connection, $query, "createTrainingSession");
    }

    /**
     * @throws ORMException
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     * @throws OptimisticLockException
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws NonUniqueResultException
     * @throws NoResultException
     * @throws ErrorException
     */
    private function updateTrainingSession(Connection $connection, string $trainingSessionId, object $registrationFolder): array
    {
        $query = "mutation { 
                        updateTrainingSession(id:$trainingSessionId, trainingSessionInput:{ 
                            cityCode:$registrationFolder->cityCode,
                            code:$registrationFolder->code,
                            name:$registrationFolder->name,
                            pipelineState:$registrationFolder->pipelineState,
                          }){
                            averageDurationPerDate
                            cityCode
                            id
                            code
                            codeFundae
                            contracted
                            diploma
                            diplomaTitle
                            dpc
                            inter
                            name
                            pipelineState
                            place
                            placeName
                            qualityAnalysis
                            qualityExpectations
                            qualitySuccessConditions
                            remote
                            room {
                              id
                            }
                            showDatesInExtranet
                            showPlaceInExtranet
                            showProgramInExtranet
                            showRulesInExtranet
                            showSigningButtonInExtranet
                            showTraineePedagogicalTrackingInExtranet
                            showTraineesInExtranet
                            specialty
                            subsessions {
                              name
                              attendanceFromModules
                              averageDurationPerDate
                              color
                              id
                              costs {
                                cost
                                costMode
                                costIndividual
                                costIndependant
                                description
                                monthly
                                type
                                vat
                              }
                              dates {
                                date
                                endTime
                                room {
                                  name
                                }
                                slot
                                startTime
                              }
                              datesAreInterval
                              durationDays
                              durationHours
                              modality
                              name
                            }
                            timezone
                            trainingType
                            type
                            useMap
                            vaeAdmissibilityDate
                            invoices {
                                date
                                freeText
                                id
                                invoicePayments {
                                    amount 
                                    date 
                                    freeText 
                                    id 
                                    mode 
                                    stripeId
                                }
                                items {
                                    description 
                                    id 
                                    name 
                                    quantity 
                                    type 
                                    unitPrice 
                                    vat
                                }
                                number
                                prefix
                                recipient {
                                    __typename
                                }
                            }
                          }  
                     }";
        return $this->postToDigiforma($connection, $query, "updateTrainingSession");
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @return object
     */
    private function getSubsessionInput(RegistrationFolder $registrationFolder): object
    {
        $session = $registrationFolder->getSession();
        $rawData = $registrationFolder->getRawData();
        $training = $session->getTrainingAction()->getTraining();

        $trainingSession = [
            "id" => null,
            "attendanceFromModules" => "false",
            "averageDurationPerDate" => "null",
            "color" => "\"#6875F5\"",
            "costs" => (object)[
                "cost" => $registrationFolder->getType() !== DataProviders::CPF()->getValue() ? $rawData["trainingActionInfo"]["totalExcl"] : "0",//sinon entreprise
                "costIndependant" => "0",
                "costIndividual" => $registrationFolder->getType() === DataProviders::CPF()->getValue() ? $rawData["trainingActionInfo"]["totalExcl"] : "0",//si cpf
                "costMode" => "\"per_customer\"",
                "description" => "\"Formation\"",
                "monthly" => "false",
                "type" => "\"training\"",
                "vat" => is_null($registrationFolder->getOrganism()->getVat()) ? "null" : $registrationFolder->getOrganism()->getVat(),//peut être null
            ],
            "datesAreInterval" => "true",
            "durationDays" => !is_null($rawData['trainingActionInfo']['indicativeDuration']) ? $rawData['trainingActionInfo']['indicativeDuration'] : "null",
            "durationHours" => !is_null($rawData['trainingActionInfo']['indicativeDuration']) ? $rawData['trainingActionInfo']['indicativeDuration'] : "null",
            "modality" => "\"classroom\"",
            "name" => $training->getTitle() ? "\"" . addslashes($training->getTitle()) . "\"" : "null",
        ];
        if (!is_null($session->getStartDate())) {
            $trainingSession["startDate"] = (object)[
                "date" => $session->getStartDate() ? "\"" . $session->getStartDate()->format('Y-m-d') . "\"" : "null",
                "endTime" => "null",
                "room" => "null",
                "slot" => "\"morning\"",
                "startTime" => "null",
            ];
            $trainingSession["endDate"] = (object)[
                "date" => $session->getEndDate() ? "\"" . $session->getEndDate()->format('Y-m-d') . "\"" : "null",
                "endTime" => "null",
                "room" => "null",
                "slot" => "\"morning\"",
                "startTime" => "null",
            ];
        }
        return (object)$trainingSession;
    }

    /**
     * @param object $appMedatada
     * @param Connection $connection
     * @param RegistrationFolder $registrationFolder
     * @param array $sessionDigiforma
     * @return void
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    private function processUpdateSubsession(object $appMedatada, Connection $connection, RegistrationFolder $registrationFolder, array $sessionDigiforma): void
    {
        $toCreate = sizeof($sessionDigiforma['subsessions']) === 0;

        $subsession = $this->getSubsessionInput($registrationFolder);

        if (!$toCreate) {
            if ($appMedatada->DIGIFORMA_CREATE_SUBSESSION_ONLY) {
                return;
            }
            $subsession->id = $sessionDigiforma['subsessions'][0]['id'];
        }

        $this->createOrUpdateSubsession($connection, $subsession, (object)$sessionDigiforma, $toCreate);
    }

    /**
     * @param object $subsession
     * @param string $type
     * @return string
     */
    private function getSubsessionQueryInput(object $subsession, string $type): string
    {
        $subsessionCosts = $subsession->costs;
        $costs = "costs:[{
                                  cost:$subsessionCosts->cost,
                                  costIndependant:$subsessionCosts->costIndependant,
                                  costIndividual:$subsessionCosts->costIndividual,
                                  costMode:$subsessionCosts->costMode,
                                  description:$subsessionCosts->description,
                                  monthly:$subsessionCosts->monthly,
                                  type:$subsessionCosts->type,
                                  vat:$subsessionCosts->vat,
                                }],";
        $dates = "";
        if (isset($subsession->startDate)) {
            $subsessionStartDate = $subsession->startDate;
            $subsessionEndDate = $subsession->endDate;
            $dates = "dates:[{
                                  date:$subsessionStartDate->date,
                                  endTime:$subsessionStartDate->endTime,
                                  room:$subsessionStartDate->room,
                                  slot:$subsessionStartDate->slot,
                                  startTime:$subsessionStartDate->startTime,
                                },{
                                  date:$subsessionEndDate->date,
                                  endTime:$subsessionEndDate->endTime,
                                  room:$subsessionEndDate->room,
                                  slot:$subsessionEndDate->slot,
                                  startTime:$subsessionEndDate->startTime,
                                }],";
        }
        $subsessionInput = "";
        if ($type === 'createSubsession') {
            $subsessionInput = "attendanceFromModules:$subsession->attendanceFromModules,
                                averageDurationPerDate:$subsession->averageDurationPerDate,
                                color:$subsession->color,
                                datesAreInterval:$subsession->datesAreInterval,
                                durationDays:$subsession->durationDays,
                                durationHours:$subsession->durationHours,
                                modality:$subsession->modality,
                                name:$subsession->name,";
        }

        return "                $subsessionInput
                                $costs
                                $dates    
                          ";
    }

    /**
     * @param Connection $connection
     * @param object $subsession
     * @param object $trainingSession
     * @param bool $create
     * @return void
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    private function createOrUpdateSubsession(Connection $connection, object $subsession, object $trainingSession, bool $create = true): void
    {
        $type = $create ? "createSubsession" : "updateSubsession";
        $idInput = $create ? "trainingSessionId$trainingSession->id," : "id:$subsession->id,";

        $queryInput = $this->getSubsessionQueryInput($subsession, $type);

        $query = "mutation { 
                       $type($idInput subsessionInput: {
                                $queryInput         
                          }) {
                              name
                              attendanceFromModules
                              averageDurationPerDate
                              color
                              id
                              costs {
                                cost
                                costMode
                                costIndividual
                                costIndependant
                                description
                                monthly
                                type
                                vat
                              }
                              dates {
                                date
                                endTime
                                room {
                                  name
                                }
                                slot
                                startTime
                              }
                              datesAreInterval
                              durationDays
                              durationHours
                              modality
                              name
                      }
                     }";
        $this->postToDigiforma($connection, $query, $type);
    }

    /**
     * @return mixed
     * @throws ClientExceptionInterface
     * @throws ORMException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws OptimisticLockException
     * @throws ErrorException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     * @throws Throwable
     */
    private function processCreateFundingAgency(Connection $connection)
    {
        $filter = 'filter: { name : "Caisse des Dépôts et des Consignations" }';
        $result = $this->getFundingAgencies($connection, $filter);

        $result = $result['data']['fundingAgencies'];
        if (sizeof($result) === 0) {
            $fundingAgency = (object)[
                "accountingNumber" => "null",
                "city" => "null",
                "cityCode" => "null",
                "code" => "\"FE1038581922\"",
                "country" => "\"France\"",
                "countryCode" => "\"FR\"",
                "email" => "null",
                "externalId" => "null",
                "locale" => "\"fr\"",
                "name" => "\"Caisse des Dépôts et des Consignations\"",
                "note" => "\"" . self::CREATED_BY_WEDOF . "\"",
                "phone" => "null",
                "roadAddress" => "null",
                "siret" => "null",
                "type" => "\"Caisse des Dépôts\""
            ];
            $result = $this->createFundingAgency($connection, $fundingAgency);
            $result = $result['data']['createFundingAgency'];
        } else {
            $result = $result[0];
        }
        return $result;
    }

    /**
     * @param Connection $connection
     * @param string $filter
     * @return array
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    private function getFundingAgencies(Connection $connection, string $filter = "filters :{}"): array
    {
        $query = '{
                        fundingAgencies(' . $filter . ',pagination:{page:0,size:100})
                          {
                           accountingNumber
                            city
                            cityCode
                            code
                            country
                            countryCode
                            customFields
                            email
                            externalId
                            id
                            locale
                            name
                            note
                            phone
                            roadAddress
                            siret
                            type   
                          }
                    }';
        return $this->postToDigiforma($connection, $query, "fundingAgencies");
    }

    /**
     * @param Connection $connection
     * @param object $fundingAgency
     * @return array
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    private function createFundingAgency(Connection $connection, object $fundingAgency): array
    {
        $query = "
                        mutation{
                          createFundingAgency(fundingAgencyInput:{
                            accountingNumber:$fundingAgency->accountingNumber,
                            city:$fundingAgency->city,
                            cityCode:$fundingAgency->cityCode,
                            code:$fundingAgency->code,
                            country:$fundingAgency->country,
                            countryCode:$fundingAgency->countryCode,
                            email:$fundingAgency->email,
                            externalId:$fundingAgency->externalId,
                            locale:$fundingAgency->locale,
                            name:$fundingAgency->name,
                            note:$fundingAgency->note,
                            phone:$fundingAgency->phone,
                            roadAddress:$fundingAgency->roadAddress,
                            siret:$fundingAgency->siret,
                            type:$fundingAgency->type
                          }){
                            accountingNumber
                            city
                            cityCode
                            code
                            country
                            countryCode
                            customFields
                            email
                            externalId
                            locale
                            name
                            note
                            phone
                            roadAddress
                            siret
                            type    
                          }
                        }";
        return $this->postToDigiforma($connection, $query, "createFundingAgency");
    }

    /**
     * @param object $appMedatada
     * @param Connection $connection
     * @param RegistrationFolder $registrationFolder
     * @param array $traineeDigiforma
     * @param array $sessionDigiforma
     * @param array $fundingAgencyDigiforma
     * @return void
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    private function processCreateCustomer(object $appMedatada, Connection $connection, RegistrationFolder $registrationFolder, array $traineeDigiforma, array $sessionDigiforma, array $fundingAgencyDigiforma): void
    {
        $filter = 'filter: { name : "' . $traineeDigiforma['lastname'] . '" }';
        $result = $this->getCustomers($connection, $filter)['data']['customers'];

        $customerToCreateOrUpdate = (object)[
            "accountingNumber" => "null",
            "contracted" => "false",
            "contractedFundingUnknown" => "false",
            "crmStatus" => $this->getCrmStatusCustomer($appMedatada, $registrationFolder),
            "conventionSigned" => "false",
            "estimatedTraineeCount" => "1",
            "foreignCustomer" => "false",
            "jobless" => "false",
            "pipelineState" => $this->getPipelineStateCustomer($appMedatada, $registrationFolder),
            "qualityExpectations" => "null",
            "qualitySuccessConditions" => "null",
            "specialPrice" => "false",
            "stripeId" => "null",
            "trainingSessionId" => $sessionDigiforma['id'],
            "traineeId" => $traineeDigiforma['id'],
            "vat" => $registrationFolder->getOrganism()->getVat(),
        ];
        if ($registrationFolder->getType() === DataProviders::CPF()->getValue()) {
            $customerToCreateOrUpdate->customerFundings = (object)[
                "amount" => !empty($registrationFolder->getRawData()["trainingActionInfo"]["totalExcl"]) ? $registrationFolder->getRawData()["trainingActionInfo"]["totalExcl"] : "null",
                "cif" => "false",
                "contratApprentissage" => "false",
                "contratPro" => "false",
                "cpf" => "true",
                "cpfCode" => "null",
                "cpfDuration" => "null",
                "cpfTransition" => "false",
                "fundingAgencyId" => $fundingAgencyDigiforma['id'],
                "fundingAgreement" => "\"" . $registrationFolder->getExternalId() . "\"",
                "periodePro" => "false",
                "planFormation" => "false",
                "subrogation" => "false",
            ];
        }

        foreach ($result as $customer) {
            if ($customer['customerTrainees'][0]['trainee']['id'] === $traineeDigiforma['id'] && $customer['trainingSession']['id'] === $sessionDigiforma['id']
                && $customer["customerFundings"][0]["fundingAgreement"] === $registrationFolder->getExternalId()
                //SI CPF $registrationFolder->getType() === DataProviders::CPF()->getValue()
            ) {
                return;
            }
        }
        $result = $this->createCustomer($connection, $registrationFolder, (object)$traineeDigiforma, (object)$sessionDigiforma, $customerToCreateOrUpdate);

        $customerId = $result['data']["createCustomer"]['id'];
        $this->addRegistrationFolderDigiformaActivity($registrationFolder, $customerId);
        $this->addWedofLinkActionsDocument($connection, $registrationFolder, $customerId);
    }

    /**
     * @param object $appMedatada
     * @param Connection $connection
     * @param RegistrationFolder $registrationFolder
     * @param string $traineeId
     * @param string $trainingSessionId
     * @param object $customer
     * @return void
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    private function processUpdateCustomer(object $appMedatada, Connection $connection, RegistrationFolder $registrationFolder, string $traineeId, string $trainingSessionId, object $customer): void
    {
        $customerToUpdate = (object)[
            "crmStatus" => $this->getCrmStatusCustomer($appMedatada, $registrationFolder, $customer->crmStatus),
            "pipelineState" => $this->getPipelineStateCustomer($appMedatada, $registrationFolder, intval($customer->pipelineState)),
            "trainingSessionId" => $trainingSessionId,
            "traineeId" => $traineeId,
            "vat" => $registrationFolder->getOrganism()->getVat(),
        ];

        $this->updateCustomer($connection, $customerToUpdate, $customer);
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @param string $customerId
     * @return void
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    private function addRegistrationFolderDigiformaActivity(RegistrationFolder $registrationFolder, string $customerId): void
    {
        $this->activityService->create(
            [
                'title' => "Le dossier a été créé avec succès auprès de Digiforma.",
                'type' => ActivityTypes::CREATE()->getValue(),
                'eventTime' => new DateTime(),
                'link' => "https://app.digiforma.com/crm/" . $customerId,
                'origin' => 'Wedof'
            ],
            null,
            $registrationFolder
        );
    }

    /**
     * @param Connection $connection
     * @param string $filter
     * @return array
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    private function getCustomers(Connection $connection, string $filter = "filter :{}"): array
    {
        $query = '{
                      customers(' . $filter . ', pagination: {page: 0, size: 100}) {
                        accountingNumber
                        contracted
                        contractedFundingUnknown
                        conventionSigned
                        crmStatus 
                        customerTrainees {
                            trainee {
                                id
                            }
                        }
                        estimatedTraineeCount
                        foreignCustomer
                        id
                        jobless
                        manualBpf
                        manualBpfAmount
                        manualBpfHours
                        manualBpfHoursAmount
                        manualBpfOtherAmount
                        manualBpfPedagogicalAmount
                        manualBpfTraineesAmount
                        pipelineState
                        qualityExpectations
                        qualitySuccessConditions
                        specialPrice
                        stripeId
                        vat
                        trainingSession {
                          code
                          id
                          specialty
                          name
                        }
                        customerFundings {
                            amount
                            cpf
                            fundingAgency {
                                id
                            }
                            fundingAgreement
                        }
                      }
                    }';
        return $this->postToDigiforma($connection, $query, "customers");
    }

    /**
     * @throws ORMException
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     * @throws OptimisticLockException
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws NonUniqueResultException
     * @throws NoResultException
     * @throws ErrorException
     */
    private function getCustomer(Connection $connection, RegistrationFolder $registrationFolder): object
    {
        $customerId = $this->getCustomerIdFromRegistrationFolder($registrationFolder);

        $filter = 'filter: { name : "' . $registrationFolder->getAttendee()->getLastName() . '" }';

        $query = '{
                      customers(' . $filter . ', pagination: {page: 0, size: 100}) {
                        crmStatus
                        pipelineState 
                        customerTrainees {
                            trainee {
                                id
                            }
                        }
                        id
                        trainingSession {
                          code
                          id
                          pipelineState
                          invoices {
                                date
                                freeText
                                id
                                invoicePayments {
                                    amount 
                                    date 
                                    freeText 
                                    id 
                                    mode 
                                    stripeId
                                }
                                items {
                                    description 
                                    id 
                                    name 
                                    quantity 
                                    type 
                                    unitPrice 
                                    vat
                                }
                                number
                                prefix
                                recipient {
                                    __typename
                                }
                            }
                        }
                        customerFundings {
                            fundingAgency {
                                id
                            }
                        }
                      }
                    }';

        $result = $this->postToDigiforma($connection, $query, "customers")["data"]["customers"];

        $customer = array_filter($result, fn(array $customer) => $customer['id'] === $customerId)[0];

        return (object)$customer;
    }

    /**
     * @throws ORMException
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     * @throws OptimisticLockException
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws NonUniqueResultException
     * @throws NoResultException
     * @throws ErrorException
     */
    public function setCustomerPipelineState(Connection $connection, string $customerId, string $pipelineState): void
    {
        $query = "mutation{
                      updateCustomer(id:$customerId,updateCustomerInput:{
                        pipelineState : $pipelineState
                      }){
                        id,
                        crmStatus
                      }
                    }";

        $this->postToDigiforma($connection, $query, "updateCustomer");
    }

    /**
     * @throws ORMException
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     * @throws OptimisticLockException
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws NonUniqueResultException
     * @throws NoResultException
     * @throws ErrorException
     */
    public function setCustomerCrmStatus(Connection $connection, string $customerId, string $crmStatus): void
    {
        $query = "mutation{
                      updateCustomer(id:$customerId,updateCustomerInput:{
                        crmStatus : $crmStatus
                      }){
                        id,
                        crmStatus
                      }
                    }";

        $this->postToDigiforma($connection, $query, "updateCustomer");
    }

    /**
     * @throws ORMException
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     * @throws OptimisticLockException
     * @throws TransportExceptionInterface
     * @throws Throwable
     * @throws ServerExceptionInterface
     * @throws NonUniqueResultException
     * @throws ErrorException
     * @throws NoResultException
     */
    public function setTrainingSessionPipelineState(Connection $connection, string $trainingSessionId, string $pipelineState): void
    {
        $query = "mutation{
                      updateTrainingSession(id:$trainingSessionId,trainingSessionInput:{
                        pipelineState : \"$pipelineState\"
                      }){
                        id,
                        pipelineState
                      }
                    }";

        $this->postToDigiforma($connection, $query, "updateTrainingSession");
    }

    /**
     * @param object $appMedatada
     * @param RegistrationFolder $registrationFolder
     * @param string|null $oldCrmStatus
     * @return string
     */
    private function getCrmStatusCustomer(object $appMedatada, RegistrationFolder $registrationFolder, string $oldCrmStatus = null): string
    {
        if (!$appMedatada->DIGIFORMA_CUSTOMER_CRM_STATUS_ACTIVE && !is_null($oldCrmStatus)) {
            return $oldCrmStatus;
        }
        switch ($registrationFolder->getState()) {
            case RegistrationFolderStates::IN_TRAINING()->getValue():
            case RegistrationFolderStates::ACCEPTED()->getValue():
                return DigiformaCustomerCrmStatus::WON()->getValue();
            case RegistrationFolderStates::TERMINATED()->getValue():
            case RegistrationFolderStates::SERVICE_DONE_DECLARED()->getValue():
            case RegistrationFolderStates::SERVICE_DONE_VALIDATED()->getValue():
                return DigiformaCustomerCrmStatus::COMPLETED()->getValue();
            case RegistrationFolderStates::VALIDATED()->getValue():
            case RegistrationFolderStates::WAITING_ACCEPTATION()->getValue():
            case RegistrationFolderStates::NOT_PROCESSED()->getValue():
                return DigiformaCustomerCrmStatus::UNDECIDED()->getValue();
            case RegistrationFolderStates::REFUSED_BY_ATTENDEE()->getValue():
            case RegistrationFolderStates::REFUSED_BY_ORGANISM()->getValue():
                return DigiformaCustomerCrmStatus::LOST()->getValue();
            case RegistrationFolderStates::CANCELED_BY_ATTENDEE_NOT_REALIZED()->getValue():
            case RegistrationFolderStates::CANCELED_BY_ATTENDEE()->getValue():
            case RegistrationFolderStates::CANCELED_BY_ORGANISM()->getValue():
            case RegistrationFolderStates::REJECTED_WITHOUT_TITULAIRE_SUITE()->getValue():
            case RegistrationFolderStates::REJECTED()->getValue():
            case RegistrationFolderStates::REJECTED_WITHOUT_CDC_SUITE()->getValue():
            case RegistrationFolderStates::REJECTED_WITHOUT_OF_SUITE()->getValue():
                return DigiformaCustomerCrmStatus::CANCELLED()->getValue();
            default:
                return $oldCrmStatus;
        }
    }

    /**
     * @param object $appMedatada
     * @param RegistrationFolder $registrationFolder
     * @param int $oldPipelineState
     * @return int
     */
    private function getPipelineStateCustomer(object $appMedatada, RegistrationFolder $registrationFolder, int $oldPipelineState = -1): int
    {
        if (!$appMedatada->DIGIFORMA_CUSTOMER_PIPELINE_STATE_ACTIVE && $oldPipelineState !== -1) {
            return $oldPipelineState;
        }

        $currentNewState = $appMedatada->WEDOF_DIGIFORMA_MAPPING_REGISTRATION_FOLDER_STATE[$registrationFolder->getState()];

        if ($currentNewState) {
            return $currentNewState;
        }

        return $oldPipelineState;
    }

    /**
     * @param string $registrationFolderState
     * @return int
     */
    private function getPipelineStateCustomerDefault(string $registrationFolderState): int
    {
        switch ($registrationFolderState) {
            //return return DigiformaCustomerPipelineState::DISCUSSION();
            case RegistrationFolderStates::VALIDATED()->getValue():
            case RegistrationFolderStates::WAITING_ACCEPTATION()->getValue():
                return DigiformaCustomerPipelineState::DEVIS()->getValue();
            case RegistrationFolderStates::ACCEPTED()->getValue():
            case RegistrationFolderStates::IN_TRAINING()->getValue():
            case RegistrationFolderStates::TERMINATED()->getValue():
                return DigiformaCustomerPipelineState::CONVENTION()->getValue();
            case RegistrationFolderStates::SERVICE_DONE_DECLARED()->getValue():
            case RegistrationFolderStates::SERVICE_DONE_VALIDATED()->getValue():
            case RegistrationFolderStates::CANCELED_BY_ATTENDEE()->getValue()://voir avec roland
            case RegistrationFolderStates::CANCELED_BY_ATTENDEE_NOT_REALIZED()->getValue()://voir avec roland
                return DigiformaCustomerPipelineState::FACTURE()->getValue();
            case RegistrationFolderStates::NOT_PROCESSED()->getValue():
                return DigiformaCustomerPipelineState::UNDECIDED()->getValue();
            case RegistrationFolderStates::CANCELED_BY_ORGANISM()->getValue():
            case RegistrationFolderStates::REFUSED_BY_ATTENDEE()->getValue():
            case RegistrationFolderStates::REFUSED_BY_ORGANISM()->getValue():
            case RegistrationFolderStates::REJECTED_WITHOUT_TITULAIRE_SUITE()->getValue():
            case RegistrationFolderStates::REJECTED()->getValue():
            case RegistrationFolderStates::REJECTED_WITHOUT_CDC_SUITE()->getValue():
            case RegistrationFolderStates::REJECTED_WITHOUT_OF_SUITE()->getValue():
            default:
                return 1;
        }
    }

    /**
     * @param Connection $connection
     * @param RegistrationFolder $registrationFolder
     * @param object $traineeDigiforma
     * @param object $sessionDigiforma
     * @param object $customerToCreateOrUpdate
     * @return array
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    private function createCustomer(Connection $connection, RegistrationFolder $registrationFolder, object $traineeDigiforma, object $sessionDigiforma, object $customerToCreateOrUpdate): array
    {
        $textInputIds = "trainingSessionId:$sessionDigiforma->id,
                            traineeId:$traineeDigiforma->id,";
        $textInputCustomerFundings = "";
        if ($registrationFolder->getType() === DataProviders::CPF()->getValue()) {
            $customerFundings = $customerToCreateOrUpdate->customerFundings;
            $textInputCustomerFundings = "customerFundings:[{
                                amount:$customerFundings->amount,
                                cif:$customerFundings->cif,
                                contratApprentissage:$customerFundings->contratApprentissage,
                                contratPro:$customerFundings->contratPro,
                                cpf:$customerFundings->cpf,
                                cpfCode:$customerFundings->cpfCode,
                                cpfDuration:$customerFundings->cpfDuration,
                                cpfTransition:$customerFundings->cpfTransition,
                                fundingAgencyId:$customerFundings->fundingAgencyId,
                                fundingAgreement:$customerFundings->fundingAgreement,
                                periodePro:$customerFundings->periodePro,
                                planFormation:$customerFundings->planFormation,
                                subrogation:$customerFundings->subrogation,
                            }],";
        }
        $query = "
                        mutation {
                          createCustomer(createCustomerInput: {
                            accountingNumber: $customerToCreateOrUpdate->accountingNumber, 
                            contracted: $customerToCreateOrUpdate->contracted, 
                            contractedFundingUnknown: $customerToCreateOrUpdate->contractedFundingUnknown,
                            crmStatus:$customerToCreateOrUpdate->crmStatus,
                            $textInputCustomerFundings
                            conventionSigned: $customerToCreateOrUpdate->conventionSigned, 
                            estimatedTraineeCount: $customerToCreateOrUpdate->estimatedTraineeCount, 
                            foreignCustomer: $customerToCreateOrUpdate->foreignCustomer, 
                            jobless: $customerToCreateOrUpdate->jobless, 
                            pipelineState:$customerToCreateOrUpdate->pipelineState,
                            qualitySuccessConditions:$customerToCreateOrUpdate->qualitySuccessConditions,
                            specialPrice:$customerToCreateOrUpdate->specialPrice,
                            stripeId:$customerToCreateOrUpdate->stripeId,
                            $textInputIds
                            vat:$customerToCreateOrUpdate->vat,
                          }) {
                            accountingNumber
                            contracted
                            contractedFundingUnknown
                            crmStatus
                            conventionSigned
                            customerFundings{
                                amount
                                cif
                                contratApprentissage
                                contratPro
                                cpf
                                cpfCode
                                cpfDuration
                                cpfTransition
                                fundingAgreement
                                periodePro
                                planFormation
                                subrogation                                
                            }
                            estimatedTraineeCount
                            foreignCustomer
                            jobless
                            id
                            manualBpf
                            manualBpfAmount
                            manualBpfHours
                            manualBpfHoursAmount
                            manualBpfOtherAmount
                            manualBpfPedagogicalAmount
                            manualBpfTraineesAmount
                            pipelineState
                            qualityExpectations
                            qualitySuccessConditions
                            specialPrice
                            stripeId
                            trainingSession{
                                id
                            }
                            vat
                          }
                        }";
        return $this->postToDigiforma($connection, $query, "createCustomer");
    }

    /**
     * @throws ORMException
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     * @throws OptimisticLockException
     * @throws TransportExceptionInterface
     * @throws Throwable
     * @throws ServerExceptionInterface
     * @throws NonUniqueResultException
     * @throws NoResultException
     * @throws ErrorException
     */
    private function updateCustomer(Connection $connection, object $customerToUpdate, object $customer): void
    {
        $query = "
                        mutation {
                          updateCustomer(id:$customer->id, updateCustomerInput: {
                            crmStatus:$customerToUpdate->crmStatus,
                            pipelineState:$customerToUpdate->pipelineState,
                            stripeId:$customerToUpdate->stripeId,
                            vat:$customerToUpdate->vat,
                          }) {
                            accountingNumber
                            contracted
                            contractedFundingUnknown
                            crmStatus
                            conventionSigned
                            customerFundings{
                                amount
                                cif
                                contratApprentissage
                                contratPro
                                cpf
                                cpfCode
                                cpfDuration
                                cpfTransition
                                fundingAgreement
                                periodePro
                                planFormation
                                subrogation                                
                            }
                            estimatedTraineeCount
                            foreignCustomer
                            jobless
                            id
                            manualBpf
                            manualBpfAmount
                            manualBpfHours
                            manualBpfHoursAmount
                            manualBpfOtherAmount
                            manualBpfPedagogicalAmount
                            manualBpfTraineesAmount
                            pipelineState
                            qualityExpectations
                            qualitySuccessConditions
                            specialPrice
                            stripeId
                            trainingSession{
                                id
                            }
                            vat
                          }
                        }";
        $this->postToDigiforma($connection, $query, "updateCustomer");
    }

    /**
     * @param object $appMedatada
     * @param Connection $connection
     * @param RegistrationFolder $registrationFolder
     * @param object $customerDigiforma
     * @param string $traineeId
     * @param array $sessionDigiforma
     * @return array
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    private function processInvoice(object $appMedatada, Connection $connection, RegistrationFolder $registrationFolder, object $customerDigiforma, string $traineeId, array $sessionDigiforma): array
    {
        $toCreate = true;
        $returnObject = "createInvoice";

        $result = array_filter($sessionDigiforma['invoices'], fn($invoice) => $invoice["customer"]["id"] === $customerDigiforma->id);
        $training = $registrationFolder->getSession()->getTrainingAction()->getTraining();
        $organism = $registrationFolder->getOrganism();
        $invoiceToInsertOrUpdate = (object)[
            "accountingAnalytics" => "null",
            "companyId" => "null",
            "customerId" => $customerDigiforma->id,
            "date" => "\"" . $registrationFolder->getRawData()["states"][RegistrationFolderBillingStates::TO_BILL()->getValue()]["date"] . "\"",
            "freeText" => "null",
            "fundingAgencyId" => "null",
            "items" => (object)[
                "description" => "null",
                "name" => $training->getTitle() ? "\"" . addslashes($training->getTitle()) . "\"" : "null",
                "quantity" => "1",
                "type" => "\"invoice\"",
                "unitPrice" => is_null($registrationFolder->getTrainingActionInfo()["totalTvaTTc"]) ? "0" : $registrationFolder->getTrainingActionInfo()["totalTvaTTc"],
                "vat" => is_null($organism->getVat()) ? "null" : $organism->getVat()
            ],
            "number" => 2,
            "prefix" => "\"facture\"",
            "reference" => "\"" . $registrationFolder->getBillId() . "\"",
            "traineeId" => $traineeId,
            "vat" => is_null($organism->getVat()) ? "null" : $organism->getVat()
        ];

        if (sizeof($result) !== 0) {
            if ($appMedatada->DIGIFORMA_CREATE_INVOICE_ONLY) {
                return $result[0];
            }
            $toCreate = false;
            $returnObject = "updateInvoice";
            $invoiceToInsertOrUpdate->id = $result[0]["id"];
        }
        $result = $this->createOrUpdateInvoice($connection, $invoiceToInsertOrUpdate, $toCreate);

        return $result['data'][$returnObject];
    }

    /**
     * @param Connection $connection
     * @param object $invoice
     * @param bool $create
     * @return array
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    private function createOrUpdateInvoice(Connection $connection, object $invoice, bool $create = true): array
    {
        $type = $create ? "createInvoice" : "updateInvoice";
        $idInput = !$create ? "id:$invoice->id," : "";
        $textInput = $create ? "createInvoiceInput" : "updateInvoiceInput";
        $textInputIds = $create ? "companyId:$invoice->companyId,
        customerId:$invoice->customerId,
        fundingAgencyId:$invoice->fundingAgencyId,
        traineeId:$invoice->traineeId," : "";
        $invoiceItems = $invoice->items;

        $query = "
                        mutation{
                          $type($idInput $textInput:{
                            accountingAnalytics:$invoice->accountingAnalytics,
                            $textInputIds
                            date:$invoice->date,
                            freeText:$invoice->date,
                            items: {
                              description:$invoiceItems->description,
                              name:$invoiceItems->name,
                              quantity:$invoiceItems->quantity,
                              type:$invoiceItems->type,
                              unitPrice:$invoiceItems->unitPrice,
                              vat:$invoiceItems->vat
                            },
                            number:$invoice->number,
                            prefix:$invoice->prefix,
                            reference:$invoice->reference,
                            vat:$invoice->vat
                          }){
                                accountingAnalytics
                                customer {
                                    id
                                }
                                date 
                                freeText
                                items {
                                    description
                                    name
                                    quantity
                                    type
                                    unitPrice
                                    vat
                                }
                                id
                                number
                                prefix
                                reference
                                invoicePayments {
                                    amount
                                    date
                                    freeText
                                    id
                                    mode
                                    stripeId
                                }
                            }
                        }";
        return $this->postToDigiforma($connection, $query, $type);
    }

    /**
     * @param Connection $connection
     * @param RegistrationFolder $registrationFolder
     * @param $invoiceDigiforma
     * @return void
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    private function processInvoicePayment(Connection $connection, RegistrationFolder $registrationFolder, $invoiceDigiforma): void
    {
        $invoicePayment = $invoiceDigiforma["invoicePayments"];
        foreach ($registrationFolder->getPayments() as $payment) {
            if ($payment->getState() === PaymentStates::ISSUED()->getValue()) {
                $result = array_filter($invoicePayment, fn(array $paymentDigiforma) => $payment->getAmount() === $paymentDigiforma["amount"] && $payment->getScheduledDate()->format("Y-m-d") === $paymentDigiforma["date"]);
                if ($result) {
                    continue;
                }
                $invoicePaymentToInsert = (object)[
                    "amount" => $payment->getAmount(),
                    "date" => "\"" . $payment->getScheduledDate()->format("Y-m-d") . "\"",
                    "freeText" => "\"" . $payment->getBillNumber() . "\"",
                    "mode" => "CARD",
                    "stripeId" => "null",
                ];

                $this->createInvoicePayment($connection, $invoiceDigiforma, $invoicePaymentToInsert);
            }
        }

    }

    /**
     * @param Connection $connection
     * @param object $invoice
     * @param object $invoicePayment
     * @return void
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    private function createInvoicePayment(Connection $connection, object $invoice, object $invoicePayment): void
    {
        $query = "
                        mutation{
                          createInvoicePayment(invoiceId:$invoice->id,invoicePaymentInput:{
                            amount:$invoicePayment->amount,
                            date:$invoicePayment->date,
                            freeText:$invoicePayment->freeText,
                            mode:$invoicePayment->mode,
                            stripeId:$invoicePayment->stripeId
                          }){
                            amount
                            date
                            freeText
                            id
                            mode
                            stripeId
                          }
                        }";
        $this->postToDigiforma($connection, $query, "createInvoicePayment");
    }

    /**
     * @throws ORMException
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     * @throws OptimisticLockException
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws NonUniqueResultException
     * @throws NoResultException
     * @throws ErrorException
     */
    public function getPipelineStates(Connection $connection): array
    {
        $query = "{
                      pipelineStates {
                        position
                        state
                      }
                    }";

        return $this->postToDigiforma($connection, $query, "pipelineStates");
    }
}