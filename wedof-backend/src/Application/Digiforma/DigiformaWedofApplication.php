<?php

namespace App\Application\Digiforma;

use App\Application\Digiforma\Service\DigiformaApiService;
use App\Application\WedofApplication;
use App\Entity\Application;
use App\Entity\Subscription;
use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofConflictHttpException;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\enums\SubscriptionTrainingTypes;
use App\Service\ActivityService;
use App\Service\ApplicationService;
use App\Service\ConnectionService;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use ErrorException;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Throwable;

class DigiformaWedofApplication extends WedofApplication
{

    protected static string $APP_ID = "digiforma";
    protected static string $DIGIFORMA_PASSWORD = "*********";

    protected static array $METHODS = ["checkCredentials", "checkState", "getPipelineStates"];

    private DigiformaApiService $digiformaApiService;

    private ConnectionService $connectionService;

    /**
     * @param ApplicationService $applicationService
     * @param ActivityService $activityService
     * @param DigiformaApiService $digiformaApiService
     * @param ConnectionService $connectionService
     */
    public function __construct(ApplicationService $applicationService, ActivityService $activityService, DigiformaApiService $digiformaApiService, ConnectionService $connectionService)
    {
        parent::__construct($applicationService, $activityService);
        $this->digiformaApiService = $digiformaApiService;
        $this->connectionService = $connectionService;
    }

    /**
     * @param Subscription $subscription
     * @return array
     */
    static public function getAllowedSubscriptionTypes(Subscription $subscription): array
    {
        return [
            'training' => SubscriptionTrainingTypes::getPaidTrainingTypesForApps(),
            'certifier' => []
        ];
    }

    /**
     * @return array
     */
    static public function getApplicationSubscribedEvents(): array
    {
        return [];
    }

    /**
     * @param Application $application
     * @return void
     */
    public function enabled(Application $application): void
    {
        // TODO: Implement enabled() method.
    }

    /**
     * @param Application $application
     * @return void
     */
    public function disabled(Application $application): void
    {
        // TODO: Implement disabled() method.
    }

    /**
     * @param Application $application
     * @return void
     */
    public function beforeEnable(Application $application): void
    {
        // TODO: Implement beforeEnable() method.
    }

    /**
     * @param Application $application
     * @return void
     */
    public function beforeDisable(Application $application): void
    {
        // TODO: Implement beforeDisable() method.
    }

    /**
     * @param Application $application
     * @return void
     */
    public function onNewOAuth(Application $application): void
    {
        // TODO: Implement onNewOAuth() method.
    }

    /**
     * @param Application $application
     * @return void
     */
    public function onRefreshOAuth(Application $application): void
    {
        // TODO: Implement onRefreshOAuth() method.
    }

    /**
     * @param Application $application
     * @param array $previousMetadata
     * @return void
     */
    public function onUpdateMetadata(Application $application, array $previousMetadata): void
    {
        // TODO: Implement onUpdateMetadata() method.
        $metadata = $application->getMetadata();

        if (isset($metadata['digiformaMetadata']['token'])) {
            unset($metadata['digiformaMetadata']['token']);
        }
        if (isset($metadata['digiformaMetadata']['password'])) {
            $metadata['digiformaMetadata']['password'] = self::$DIGIFORMA_PASSWORD;
        }
        $this->applicationService->updateMetadata($application, $metadata, false);
    }

    /**
     * @param Application $application
     * @param array $data
     * @return bool
     * @throws TransportExceptionInterface
     */
    public function checkCredentials(Application $application, array $data): bool
    {
        if (!$data["isPasswordDirty"]) {
            $data["password"] = $application->getOrganism()->getConnectionForDataProvider(DataProviders::DIGIFORMA())->getCredentials()["password"];
        }

        return $this->setDigiformaConnection($application, $data);
    }

    /**
     * @param Application $application
     * @param array $data
     * @return bool
     * @throws TransportExceptionInterface
     */
    private function setDigiformaConnection(Application $application, array $data): bool
    {
        try {
            $access = $this->connectionService->authenticate($application->getOrganism(), DataProviders::DIGIFORMA(), $data);
            return $access['hasAccess'] == true;
        } catch (WedofConflictHttpException $e) {
            throw new WedofBadRequestHttpException($e->getMessage());
        }
    }

    /**
     * @throws ORMException
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     * @throws OptimisticLockException
     * @throws Throwable
     * @throws \Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws NonUniqueResultException
     * @throws ErrorException
     * @throws NoResultException
     */
    public function checkState(Application $application): bool
    {
        if ($this->connectionService->hasAccess($application->getOrganism(), DataProviders::DIGIFORMA())) {
            $connection = $application->getOrganism()->getConnectionForDataProvider(DataProviders::DIGIFORMA());
            $result = $this->digiformaApiService->checkToken($connection->getCredentials()['token']); //enforce check token
            if (isset($result['errors'])) {
                return false;
            }
            return true;
        } else {
            return false;
        }
    }

    /**
     * @param Application $application
     * @return array|array[]
     * @throws ORMException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws OptimisticLockException
     * @throws ErrorException
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws \Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface
     * @throws Throwable
     */
    public function getPipelineStates(Application $application): array
    {
        $appRawMetadata = $application->getMetadata();
        $connection = $application->getOrganism()->getConnectionForDataProvider(DataProviders::DIGIFORMA());
        if (!isset($appRawMetadata["digiformaMetadata"]) || !$connection) {
            return ["data" => ["pipelineStates" => []]];
        }

        return $this->digiformaApiService->getPipelineStates($connection);
    }

    public function show(Application $application): void
    {
        // TODO: Implement onShow() method.
    }
}
