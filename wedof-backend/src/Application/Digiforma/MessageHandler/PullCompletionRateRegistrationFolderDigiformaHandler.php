<?php

namespace App\Application\Digiforma\MessageHandler;

use App\Application\Digiforma\Message\PullCompletionRateRegistrationFolderDigiforma;
use App\Application\Digiforma\Service\DigiformaApiService;
use App\MessageHandler\WithNextMessage;
use App\Service\RegistrationFolderService;
use Exception;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Throwable;

class PullCompletionRateRegistrationFolderDigiformaHandler extends WithNextMessage implements MessageHandlerInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    private RegistrationFolderService $registrationFolderService;

    private DigiformaApiService $digiformaApiService;

    public function __construct(RegistrationFolderService $registrationFolderService, MessageBusInterface $messageBus, DigiformaApiService $digiformaApiService)
    {
        parent::__construct($messageBus);
        $this->registrationFolderService = $registrationFolderService;
        $this->digiformaApiService = $digiformaApiService;
    }

    /**
     * @param PullCompletionRateRegistrationFolderDigiforma $pullRegistrationFolderDigiforma
     * @return void
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     * @throws Throwable
     */
    public function __invoke(PullCompletionRateRegistrationFolderDigiforma $pullRegistrationFolderDigiforma)
    {
        try {
            $registrationFolder = $this->registrationFolderService->getByExternalId($pullRegistrationFolderDigiforma->getExternalId());
            $this->digiformaApiService->pullFromDigiformaRegistrationFolderCompletionRate($registrationFolder);
        } catch (Exception $e) {
            $this->logger->error($e);
        }
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}