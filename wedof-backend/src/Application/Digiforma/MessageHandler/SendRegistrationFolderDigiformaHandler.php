<?php

namespace App\Application\Digiforma\MessageHandler;

use App\Application\Digiforma\Message\SendRegistrationFolderDigiforma;
use App\Application\Digiforma\Service\DigiformaApiService;
use App\MessageHandler\WithNextMessage;
use App\Service\RegistrationFolderService;
use Exception;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Throwable;

class SendRegistrationFolderDigiformaH<PERSON><PERSON> extends WithNextMessage implements MessageHandlerInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    private RegistrationFolderService $registrationFolderService;

    private DigiformaApiService $digiformaApiService;

    public function __construct(RegistrationFolderService $registrationFolderService, MessageBusInterface $messageBus, DigiformaApiService $digiformaApiService)
    {
        parent::__construct($messageBus);
        $this->registrationFolderService = $registrationFolderService;
        $this->digiformaApiService = $digiformaApiService;
    }

    /**
     * @param SendRegistrationFolderDigiforma $sendRegistrationFolderDigiforma
     * @throws TransportExceptionInterface
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws \Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface
     * @throws Throwable
     */
    public function __invoke(SendRegistrationFolderDigiforma $sendRegistrationFolderDigiforma)
    {
        try {
            $registrationFolder = $this->registrationFolderService->getByExternalId($sendRegistrationFolderDigiforma->getExternalId());
            $eventName = $sendRegistrationFolderDigiforma->getEventName();
            $type = $sendRegistrationFolderDigiforma->getType();

            switch ($type) {
                case "create":
                    $this->digiformaApiService->processCreateDigiforma($registrationFolder, $eventName);
                    break;
                case "update":
                    $this->digiformaApiService->processUpdateDigiforma($registrationFolder, $eventName);
                    break;
                case "updateStatus":
                    $this->digiformaApiService->processUpdateStatusDigiforma($registrationFolder, $eventName);
                    break;
            }

        } catch (Exception $e) {
            $this->logger->error($e);
        }
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}