<?php

namespace App\Application\Status;

use App\Application\Status\Service\StatusApiService;
use App\Application\WedofApplication;
use App\Entity\Application;
use App\Entity\Subscription;
use App\Library\utils\enums\SubscriptionCertifierTypes;
use App\Library\utils\enums\SubscriptionTrainingTypes;
use App\Service\ActivityService;
use App\Service\ApplicationService;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use ErrorException;
use Exception;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Throwable;

class StatusWedofApplication extends WedofApplication
{

    protected static string $APP_ID = "status";
    protected static array $METHODS = ["components"];

    private StatusApiService $statusApiService;

    /**
     * @param ApplicationService $applicationService
     * @param ActivityService $activityService
     * @param StatusApiService $statusApiService
     */
    public function __construct(ApplicationService $applicationService, ActivityService $activityService, StatusApiService $statusApiService)
    {
        parent::__construct($applicationService, $activityService);
        $this->statusApiService = $statusApiService;
    }

    static public function getAllowedSubscriptionTypes(Subscription $subscription): array
    {
        return [
            'training' => SubscriptionTrainingTypes::getPaidTrainingTypes(),
            'certifier' => SubscriptionCertifierTypes::getPaidCertifierTypes()
        ];

    }

    /**
     * @param Application $application
     * @param $data
     * @return array|null
     * @throws ClientExceptionInterface
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     * @throws ErrorException
     * @throws Throwable
     */
    public function components(Application $application, $data): ?array
    {
        return $this->statusApiService->getComponents();
    }

    /**
     * @throws ORMException
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     * @throws OptimisticLockException
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws NonUniqueResultException
     * @throws NoResultException
     * @throws ErrorException
     */
    public function beforeDisable(Application $application): void
    {
        $metadata = $application->getMetadata();
        if (isset($metadata['settings'])) {
            $this->statusApiService->deleteSubscriber($metadata['settings']['email']);
        }
    }

    /**
     * @throws ORMException
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     * @throws OptimisticLockException
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws NonUniqueResultException
     * @throws ErrorException
     * @throws NoResultException
     */
    public function onUpdateMetadata(Application $application, array $previousMetadata): void
    {
        $metadata = $application->getMetadata();
        if (isset($metadata['settings']) && $metadata['settings']['email']) {
            $components = array_keys($metadata['settings']);
            $components = array_filter($components, function ($name) use ($metadata) {
                return $name != 'email' && $metadata['settings'][$name];
            });
            $components = array_map(function ($name) {
                return intval(str_replace('component-', '', $name));
            }, $components);
            $components = array_values($components);
            try {
                $this->statusApiService->createOrUpdateSubscriber($metadata['settings']['email'], $components);
            } catch (Exception $e) {
                $this->applicationService->updateMetadata($application, $previousMetadata, false);
            }
        }
    }

    /**
     * @param Application $application
     * @return void
     */
    public function onNewOAuth(Application $application): void
    {
        // TODO: Implement onNewOAuth() method.
    }

    /**
     * @param Application $application
     * @return void
     */
    public function onRefreshOAuth(Application $application): void
    {
        // TODO: Implement onRefreshOAuth() method.
    }

    /**
     * @return array
     */
    static public function getApplicationSubscribedEvents(): array
    {
        return [];
    }


    /**
     * @param Application $application
     * @return void
     */
    public function enabled(Application $application): void
    {
    }

    /**
     * @param Application $application
     * @return void
     */
    public function disabled(Application $application): void
    {
    }

    /**
     * @param Application $application
     * @return void
     */
    public function beforeEnable(Application $application): void
    {
        // TODO: Implement beforeEnable() method.
    }

    public function show(Application $application): void
    {
        // TODO: Implement onShow() method.
    }
}
