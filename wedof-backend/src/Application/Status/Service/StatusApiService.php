<?php

namespace App\Application\Status\Service;

use App\Entity\Connection;
use App\Entity\Organism;
use App\Exception\WedofConflictHttpException;
use App\Library\utils\enums\DataProviders;
use App\Repository\EndPointStatusRepository;
use App\Service\ConfigService;
use App\Service\ConnectionService;
use App\Service\DataProviders\BaseApiService;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use ErrorException;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Security\Core\Security;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Throwable;

class StatusApiService extends BaseApiService
{
    public function __construct(ConfigService $configService, ConnectionService $connectionService, RequestStack $requestStack, EndPointStatusRepository $endPointStatusRepository, EventDispatcherInterface $dispatcher, LoggerInterface $logger, Security $security)
    {
        parent::__construct(DataProviders::STATUS(), $configService, $connectionService, $requestStack, $endPointStatusRepository, $dispatcher, $logger, $security);
    }

    /**
     * @return array
     * @throws ORMException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws OptimisticLockException
     * @throws ErrorException
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     * @throws Throwable
     */
    public function getComponents(): array
    {
        $options = [
            'authenticationHeaderName' => $_ENV['STATUS_AUTH_HEADER'],
            'authenticationHeaderValue' => $_ENV['STATUS_AUTH_TOKEN'],
            'content-type' => "application/json",
            'method' => 'GET'
        ];
        $data = $this->sendRequest($_ENV['STATUS_BASE_URI'] . 'components', $options);
        $groups = $this->getComponentsGroups();
        $filter = function (array $component) use ($groups): ?array {
            if ($component['enabled']) {
                return [
                    'id' => $component['id'],
                    'name' => $component['name'],
                    'status' => $component['status'],
                    'status_name' => $component['status_name'],
                    'group_name' => isset($component['group_id']) && $component['group_id'] != 0 ? array_column($groups, null, 'id')[$component['group_id']]['name'] : null
                ];
            } else {
                return null;
            }
        };
        return (array_map($filter, $data['data']));
    }

    /**
     * @return array
     * @throws ORMException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws OptimisticLockException
     * @throws ErrorException
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     * @throws Throwable
     */
    public function getComponentsGroups(): array
    {
        $options = [
            'authenticationHeaderName' => $_ENV['STATUS_AUTH_HEADER'],
            'authenticationHeaderValue' => $_ENV['STATUS_AUTH_TOKEN'],
            'content-type' => "application/json",
            'method' => 'GET'
        ];
        $data = $this->sendRequest($_ENV['STATUS_BASE_URI'] . 'components/groups', $options);
        $filter = function (array $component): ?array {
            if ($component['visible']) {
                return [
                    'id' => $component['id'],
                    'name' => $component['name']
                ];
            } else {
                return null;
            }
        };
        return (array_map($filter, $data['data']));
    }

    /**
     * @throws ORMException
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     * @throws OptimisticLockException
     * @throws TransportExceptionInterface
     * @throws Throwable
     * @throws ServerExceptionInterface
     * @throws NonUniqueResultException
     * @throws ErrorException
     * @throws NoResultException
     */
    public function createOrUpdateSubscriber($email, array $components): array
    {
        $payload = [
            'email' => $email,
            'verify' => true,
            'components' => $components
        ];
        $options = [
            'authenticationHeaderName' => $_ENV['STATUS_AUTH_HEADER'],
            'authenticationHeaderValue' => $_ENV['STATUS_AUTH_TOKEN'],
            'content-type' => "application/json",
            'method' => 'POST',
            'parameters' => $payload
        ];
        return $this->sendRequest($_ENV['STATUS_BASE_URI'] . 'subscribers', $options)['data'];
    }

    /**
     * @throws ORMException
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     * @throws OptimisticLockException
     * @throws TransportExceptionInterface
     * @throws Throwable
     * @throws ServerExceptionInterface
     * @throws NonUniqueResultException
     * @throws NoResultException
     * @throws ErrorException
     */
    public function deleteSubscriber($email): void
    {
        $data = $this->createOrUpdateSubscriber($email, []);
        if ($data) {
            $options = [
                'authenticationHeaderName' => $_ENV['STATUS_AUTH_HEADER'],
                'authenticationHeaderValue' => $_ENV['STATUS_AUTH_TOKEN'],
                'content-type' => "application/json",
                'method' => 'DELETE'
            ];
            $this->sendRequest($_ENV['STATUS_BASE_URI'] . 'subscribers' . '/' . $data['id'], $options);
        }
    }


    /**
     * @inheritDoc
     */
    public function getMaxAttemptsBeforeStop(): int
    {
        return 10000;
    }

    /**
     * @inheritDoc
     */
    public function authenticate(Connection $connection, bool $checkOrganismAccess = true): array
    {
        throw new WedofConflictHttpException("Pas d'implémentation de la fonction authenticate dans StatusApiService");
    }

    /**
     * @inheritDoc
     */
    public function checkBeforeHabilitate(Organism $organism, array $params = null)
    {
    }

    /**
     * @inheritDoc
     */
    public function habilitate(Organism $organism, Connection $connection, array $params): bool
    {
        return false;
    }

    /**
     * @inheritDoc
     */
    public function getUsername(Connection $connection): string
    {
        return "";
    }

    /**
     * @inheritDoc
     */
    public function requiresAuthentication(): bool
    {
        return false;
    }

    /**
     * @inheritDoc
     */
    public function setActiveOrganism(Organism $organism): bool
    {
        return true;
    }

    /**
     * @inheritDoc
     */
    public function setAuthentication(array $options = []): array
    {
        return $options;
    }
}