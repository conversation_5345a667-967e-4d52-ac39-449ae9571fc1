<?php
// src/Application/Signature/SignatureWedofApplication.php
namespace App\Application\Signature;

use App\Application\WedofApplication;
use App\Entity\Application;
use App\Entity\Subscription;
use App\Entity\User;
use App\Exception\WedofConnectionException;
use App\Library\utils\enums\SubscriptionTrainingTypes;
use App\Library\utils\Tools;
use App\Service\ActivityService;
use App\Service\ApplicationService;
use App\Service\DataProviders\DocusealApiService;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use ErrorException;
use Gaufrette\Adapter\AwsS3;
use Gaufrette\Filesystem;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\ContainerInterface;
use Psr\Container\NotFoundExceptionInterface;
use Stripe\Exception\ApiErrorException;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Security\Core\Security;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Throwable;

class SignatureWedofApplication extends WedofApplication
{
    protected static string $APP_ID = "signature";

    protected static array $PRICES = [
        'prod' => [
            'month' => "price_1RJMMhLLgg6l7qY9Digu6sjk",
            "year" => "price_1RJMMvLLgg6l7qY9wx97FmsV",
            'trial' => '+15 days'
        ],
        'test' => [
            'month' => "",
            "year" => ""
        ],
        'dev' => [
            'month' => "price_1RBL2dLLgg6l7qY9fQtaVrxT",
            "year" => "price_1RBL2yLLgg6l7qY90iMMlsau",
            'trial' => '+15 days'
        ]
    ];

    protected static array $METHODS = ["showSignatureSettingsForm", "saveSignatureSettingsData"];

    private DocusealApiService $docusealApiService;
    private Security $security;
    private ContainerInterface $container;

    public function __construct(DocusealApiService $docusealApiService, ApplicationService $applicationService, ContainerInterface $container, ActivityService $activityService, Security $security)
    {
        parent::__construct($applicationService, $activityService);
        $this->docusealApiService = $docusealApiService;
        $this->security = $security;
        $this->container = $container;
    }

    static public function getAllowedSubscriptionTypes(Subscription $subscription): array
    {
        $hasSignatureEnabled = !empty(array_filter($subscription->getOrganism()->getApplications()->toArray(), fn(Application $app) => $app->getAppId() === self::$APP_ID && $app->getEnabled() === true));
        return [
            'training' => $hasSignatureEnabled ? [$subscription->getTrainingType()] : array_diff(SubscriptionTrainingTypes::getPaidTrainingTypes(), array(SubscriptionTrainingTypes::API())),
            'certifier' => $hasSignatureEnabled ? [$subscription->getCertifierType()] : ($subscription->isAllowCertifierPlus() ? [$subscription->getCertifierType()] : [])
        ];
    }

    /**
     * @throws ORMException
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     * @throws WedofConnectionException
     * @throws OptimisticLockException
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws NonUniqueResultException
     * @throws ErrorException
     * @throws NoResultException
     */
    public function showSignatureSettingsForm(Application $application): Response
    {
        /** @var User $user */
        $user = $this->security->getUser();
        if ($user->getMainOrganism() === $application->getOrganism()) {
            $metadata = $application->getMetadata();
            if (empty($metadata['settings_submission_id'])) {
                $submission_id = $this->docusealApiService->createSignatureSettingsForm($application->getOrganism());
                $metadata['settings_submission_id'] = $submission_id;
                $this->applicationService->updateMetadata($application, $metadata);
            }
            return new Response(json_encode([
                "id" => $metadata['settings_submission_id'],
                "html" => DocusealApiService::getEmbedCode($metadata['settings_submission_id'])
            ]));
        }
        return new Response(null, 403);
    }

    /**
     * @param Application $application
     * @param array $data
     * @return Application|Response
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function saveSignatureSettingsData(Application $application, array $data)
    {
        /** @var User $user */
        $user = $this->security->getUser();
        /** @var Filesystem $filesystem */
        $filesystem = $this->container->get('knp_gaufrette.filesystem_map')->get('wedof_fs');
        $adapter = $filesystem->getAdapter();

        if ($user->getMainOrganism() === $application->getOrganism()) {
            $metadata = $application->getMetadata();
            if (!empty($metadata['settings_submission_id']) && $data['id'] == $metadata['settings_submission_id']) {
                foreach (['signature', 'initials', 'stamp'] as $fieldName) {
                    $fieldFileUrl = $data['values'][array_search($fieldName, array_column($data['values'], 'field'))]['value'];
                    $fieldFile = $this->docusealApiService->downloadFieldFile($fieldFileUrl);
                    $storageFileUrl = "/publicFiles/s/" . Tools::generateRandomString(64);
                    if ($adapter instanceof AwsS3) {
                        //$adapter->setMetadata($storageFileUrl, ['contentType' => 'image/png']);
                        $adapter->write($storageFileUrl, $fieldFile);
                    }
                    $metadata[$fieldName] = "https://storage.wedof.fr/data/" . $storageFileUrl;
                }
                $metadata['settings_submission_id'] = null;
                $this->applicationService->updateMetadata($application, $metadata);
                return $application;
            }
        }
        return new Response(null, 403);
    }

    /**
     * @inheritDoc
     * @param Application $application
     * @throws ApiErrorException
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function enabled(Application $application): void
    {
        $this->docusealApiService->addTenant($application->getOrganism());
        //will execute https://kastorr.wedof.fr/workflow/yUn2KMUzZKIgXVeY
    }

    /**
     * @inheritDoc
     * @param Application $application
     */
    public function disabled(Application $application): void
    {
        //real disable is done in a workflow when pending_disable subscription of docuseal expire
        //https://kastorr.wedof.fr/workflow/yUn2KMUzZKIgXVeY
    }

    /**
     * @inheritDoc
     */
    public function beforeEnable(Application $application): void
    {
        // TODO: Implement beforeEnable() method.
    }

    /**
     * @inheritDoc
     */
    public function beforeDisable(Application $application): void
    {
        // TODO: Implement beforeDisable() method.
    }

    /**
     * @inheritDoc
     */
    public function onNewOAuth(Application $application): void
    {
        // TODO: Implement onNewOAuth() method.
    }

    /**
     * @inheritDoc
     */
    public function onRefreshOAuth(Application $application): void
    {
        // TODO: Implement onRefreshOAuth() method.
    }

    /**
     * @inheritDoc
     */
    public function onUpdateMetadata(Application $application, array $previousMetadata): void
    {
        // TODO: Implement onUpdateMetadata() method.
    }

    /**
     * @inheritDoc
     */
    static public function getApplicationSubscribedEvents(): array
    {
        return [];
    }

    public function show(Application $application): void
    {
        // TODO: Implement onShow() method.
    }
}
