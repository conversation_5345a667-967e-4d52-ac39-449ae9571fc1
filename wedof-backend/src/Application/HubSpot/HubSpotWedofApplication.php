<?php

namespace App\Application\HubSpot;

use App\Application\OldN8nApplication;
use App\Entity\Application;
use App\Entity\Subscription;
use App\Repository\WebhookRepository;
use App\Service\ActivityService;
use App\Service\ApplicationService;
use App\Service\WebhookService;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Throwable;

class HubSpotWedofApplication extends OldN8nApplication
{

    protected static string $APP_ID = "hubspot";
    protected static array $METHODS = ['authUrl', 'setup']; // custom methods called from front

    protected static string $N8N_CREDENTIALS_TYPE = "hubspotOAuth2Api";
    protected static string $N8N_OAUTH2_AUTH_URL = "https://app.hubspot.com/oauth/authorize";
    protected static string $N8N_OAUTH2_TOKEN_URL = "https://api.hubapi.com/oauth/v1/token";
    protected static string $N8N_OAUTH2_SCOPES = "crm.schemas.deals.read crm.objects.owners.read crm.objects.contacts.write crm.objects.companies.write crm.objects.companies.read crm.objects.deals.read crm.schemas.contacts.read crm.objects.deals.write crm.objects.contacts.read crm.schemas.companies.read forms tickets";
    protected static string $N8N_OAUTH2_ACCESS_TYPE = "authorization_code";
    protected static string $N8N_OAUTH2_AUTHENTICATION = "body";
    protected static string $N8N_OAUTH2_CLIENT_ID = "6f7c0535-750d-40a1-bb18-4cff41031513";
    protected static string $N8N_OAUTH2_CLIENT_SECRET = "8872a186-fe5e-40cd-b3f4-cc6903d2cdca";
    protected static string $N8N_WORKFLOW_CREDENTIALS_NODE_TYPE = "n8n-nodes-base.hubspot";

    /**
     * @param ApplicationService $applicationService
     * @param ActivityService $activityService
     * @param WebhookService $webhookService
     * @param WebhookRepository $webhookRepository
     */
    public function __construct(ApplicationService $applicationService, ActivityService $activityService, WebhookService $webhookService, WebhookRepository $webhookRepository)
    {
        parent::__construct($applicationService, $activityService, $webhookService, $webhookRepository);
    }

    /**
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface|Throwable
     */
    public function setup(Application $application): bool
    {
        return parent::setupWorkflows($application);
    }

    static public function getAllowedSubscriptionTypes(Subscription $subscription): array
    {
        return ['training' => [], 'certifier' => []];
    }
}
