<?php

namespace App\Application\HubSpot\Event\RegistrationFolder;

use App\Entity\Webhook;
use App\Event\RegistrationFolder\RegistrationFolderEvents;
use App\Library\utils\enums\DataProviders;
use App\Message\SendWebhook;
use App\Service\DataProviders\BaseApiService;
use App\Service\WebhookService;
use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Exception;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Messenger\MessageBusInterface;

class RegistrationFolderHubSpotSubscriber implements EventSubscriberInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    private WebhookService $webhookService;
    private MessageBusInterface $messageBus;

    public function __construct(WebhookService $webhookService, MessageBusInterface $messageBus)
    {
        $this->webhookService = $webhookService;
        $this->messageBus = $messageBus;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    /**
     * @return array|string[]
     */
    public static function getSubscribedEvents(): array
    {
        return array(
            RegistrationFolderEvents::CREATED => 'sendHubSpotEventOnRegistrationFolder',
            RegistrationFolderEvents::UPDATED => 'sendHubSpotEventOnRegistrationFolder'
        );
    }

    /**
     * @param RegistrationFolderEvents $event
     * @param string $eventName
     * @throws Exception
     */
    public function sendHubSpotEventOnRegistrationFolder(RegistrationFolderEvents $event, string $eventName)
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        try {
            $this->logger->debug("[HubSpot Webhook RegistrationFolder] " . self::class . " event $eventName");
            $organism = $event->getRegistrationFolder()->getOrganism();
            $webhooks = $this->webhookService->listByOrganismsAndEventAndType(new ArrayCollection([$organism]), $eventName, ['type' => 'hubspot']);
            if (sizeof($webhooks) > 0) {
                $registrationFolder = $event->getRegistrationFolder();
                $payload = array();

                $payload['state'] = $registrationFolder->getState();
                $payload['external_id'] = $registrationFolder->getExternalId();
                $payload['amountToInvoice'] = $registrationFolder->getAmountToInvoice();
                $payload['attendee'] = [
                    'first_name' => $registrationFolder->getAttendee()->getFirstName(),
                    'last_name' => $registrationFolder->getAttendee()->getLastName(),
                    'phone_fixed' => $registrationFolder->getAttendee()->getPhoneFixed(),
                    'phone_number' => $registrationFolder->getAttendee()->getPhoneNumber(),
                    'email' => $registrationFolder->getAttendee()->getEmail()
                ];

                $payload['proposal'] = $registrationFolder->getProposal() ? $registrationFolder->getProposal()->getCode() : null;
                $payload['parent_proposal'] = $registrationFolder->getProposal() && $registrationFolder->getProposal()->getParentProposal() ? $registrationFolder->getProposal()->getParentProposal()->getCode() : null;

                $rawData = $registrationFolder->getRawData();
                $sessionStartDate = DateTime::createFromFormat('d/m/Y', $rawData['trainingActionInfo']['sessionStartDate']);
                if (!$sessionStartDate) {
                    $sessionStartDate = new DateTime($rawData['trainingActionInfo']['sessionStartDate']);
                }
                $sessionEndDate = DateTime::createFromFormat('d/m/Y', $rawData['trainingActionInfo']['sessionEndDate']);
                if (!$sessionEndDate) {
                    $sessionEndDate = new DateTime($rawData['trainingActionInfo']['sessionEndDate']);
                }
                $dataProvider = DataProviders::from($registrationFolder->getType());
                $payload['training_action_infos'] = [
                    'session_id' => BaseApiService::getCatalogApiServiceByDataProvider($dataProvider)->getSessionExternalId($rawData['trainingId'], $rawData['trainingActionId'], $rawData['trainingActionInfo']['sessionId']),
                    'totalIncl' => $rawData['trainingActionInfo']['totalIncl'],
                    'sessionStartDate' => $sessionStartDate->format('Y-m-d\TH:i:s.u\Z'),
                    'sessionEndDate' => $sessionEndDate->format('Y-m-d\TH:i:s.u\Z')
                ];

                $this->logger->debug("[HubSpot Webhook RegistrationFolder] " . sizeof($webhooks) . " to send");
                $payload = json_encode($payload);
                /** @var Webhook $webhook */
                foreach ($webhooks as $webhook) {
                    $message = new SendWebhook($webhook->getId(), $eventName, $payload, get_class($event->getRegistrationFolder()), $event->getRegistrationFolder()->getExternalId());
                    $this->messageBus->dispatch($message);
                }
            } else {
                $this->logger->debug("[HubSpot Webhook RegistrationFolder] no event to send");
            }
        } catch (Exception $e) {
            $this->logger->error("[Error sendHubSpotEventOnRegistrationFolder " . $event->getRegistrationFolder()->getExternalId() . "] at Line " . $e->getLine() . " " . $e->getFile() . " " . $e->getMessage());
        }
    }
}
