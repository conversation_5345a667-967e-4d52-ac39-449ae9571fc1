<?php

namespace App\Application\Dendreo;

use App\Application\Webhook\WebhookWedofApplication;
use App\Application\WedofApplication;
use App\Entity\Application;
use App\Entity\Subscription;
use App\Library\utils\enums\SubscriptionTrainingTypes;
use App\Service\ActivityService;
use App\Service\ApplicationService;

class DendreoWedofApplication extends WedofApplication
{

    protected static string $APP_ID = "dendreo";

    /**
     * @param ApplicationService $applicationService
     * @param ActivityService $activityService
     */
    public function __construct(ApplicationService $applicationService, ActivityService $activityService)
    {
        parent::__construct($applicationService, $activityService);
    }

    static public function getAllowedSubscriptionTypes(Subscription $subscription): array
    {
        return ['training' => array_merge([SubscriptionTrainingTypes::API()->getValue()], SubscriptionTrainingTypes::getPaidTrainingTypesForApps()), 'certifier' => []];
    }

    public function enabled(Application $application): void
    {
        $webhookApplication = $this->applicationService->getByOrganismAndAppId($application->getOrganism(), WebhookWedofApplication::getAppId());
        if (!$webhookApplication->getEnabled()) {
            $this->applicationService->enable($webhookApplication);
        }
    }

    public function disabled(Application $application): void
    {
        // TODO: Implement disabled() method.
    }

    public function beforeEnable(Application $application): void
    {
        // TODO: Implement beforeEnable() method.
    }

    public function beforeDisable(Application $application): void
    {
        // TODO: Implement beforeDisable() method.
    }

    public function onNewOAuth(Application $application): void
    {
        // TODO: Implement onNewOAuth() method.
    }

    public function onRefreshOAuth(Application $application): void
    {
        // TODO: Implement onRefreshOAuth() method.
    }

    public function onUpdateMetadata(Application $application, array $previousMetadata): void
    {
        // TODO: Implement onUpdateMetadata() method.
    }

    static public function getApplicationSubscribedEvents(): array
    {
        return [];
    }

    public function show(Application $application): void
    {
        // TODO: Implement onShow() method.
    }
}
