<?php
// src/Application/Make/MakeApplication.php
namespace App\Application\Make;

use App\Application\WedofApplication;
use App\Entity\Application;
use App\Entity\Subscription;
use App\Library\utils\enums\SubscriptionCertifierTypes;
use App\Library\utils\enums\SubscriptionTrainingTypes;

class MakeWedofApplication extends WedofApplication
{
    protected static string $APP_ID = "make";

    static public function getAllowedSubscriptionTypes(Subscription $subscription): array
    {
        $certifierTypes = [SubscriptionCertifierTypes::TRIAL()->getValue(), SubscriptionCertifierTypes::UNLIMITED()->getValue()];
        if ($subscription->isAllowCertifierPlus()) {
            $certifierTypes[] = SubscriptionCertifierTypes::from($subscription->getCertifierType())->getValue();
        }
        return [
            'training' => SubscriptionTrainingTypes::getPaidTrainingTypesForApps(),
            'certifier' => $certifierTypes
        ];
    }

    /**
     * @inheritDoc
     */
    public function enabled(Application $application): void
    {
        // TODO: Implement enabled() method.
    }

    /**
     * @inheritDoc
     */
    public function disabled(Application $application): void
    {
        // TODO: Implement disabled() method.
    }

    /**
     * @inheritDoc
     */
    public function beforeEnable(Application $application): void
    {
        // TODO: Implement beforeEnable() method.
    }

    /**
     * @inheritDoc
     */
    public function beforeDisable(Application $application): void
    {
        // TODO: Implement beforeDisable() method.
    }

    /**
     * @inheritDoc
     */
    public function onNewOAuth(Application $application): void
    {
        // TODO: Implement onNewOAuth() method.
    }

    /**
     * @inheritDoc
     */
    public function onRefreshOAuth(Application $application): void
    {
        // TODO: Implement onRefreshOAuth() method.
    }

    /**
     * @inheritDoc
     */
    public function onUpdateMetadata(Application $application, array $previousMetadata): void
    {
        // TODO: Implement onUpdateMetadata() method.
    }

    /**
     * @inheritDoc
     */
    static public function getApplicationSubscribedEvents(): array
    {
        return [];
    }

    public function show(Application $application): void
    {
        // TODO: Implement onShow() method.
    }
}
