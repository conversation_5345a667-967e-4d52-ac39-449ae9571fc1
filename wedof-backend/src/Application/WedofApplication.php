<?php
// src/Event/Application/ApplicationEventsSubscriber.php
namespace App\Application;

use App\Entity\Application;
use App\Entity\Organism;
use App\Entity\Subscription;
use App\Event\Application\ApplicationEvent;
use App\Library\utils\Tools;
use App\Service\ActivityService;
use App\Service\ApplicationService;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use ReflectionException;
use Symfony\Component\Console\Exception\RuntimeException;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

abstract class WedofApplication implements EventSubscriberInterface, LoggerAwareInterface
{
    private static ?string $APP_ID = null;
    private static bool $AUTO_ACTIVATE = false;
    private static array $SCOPES = [];
    private static array $METHODS = [];
    private static array $PRICES = [];

    protected LoggerInterface $logger;
    protected ApplicationService $applicationService;
    protected ActivityService $activityService;

    /**
     * ApplicationEvents constructor.
     * @param ApplicationService $applicationService
     * @param ActivityService $activityService
     */
    public function __construct(ApplicationService $applicationService, ActivityService $activityService)
    {
        $this->applicationService = $applicationService;
        $this->activityService = $activityService;
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        $events = [
            ApplicationEvent::BEFORE_ENABLE => 'dispatchEvent',
            ApplicationEvent::ENABLED => 'dispatchEvent',
            ApplicationEvent::BEFORE_DISABLE => 'dispatchEvent',
            ApplicationEvent::DISABLED => 'dispatchEvent',
            ApplicationEvent::NEW_OAUTH => 'dispatchEvent',
            ApplicationEvent::REFRESH_OAUTH => 'dispatchEvent',
            ApplicationEvent::UPDATED_METADATA => 'dispatchEvent'
        ];
        $c = get_called_class();
        if (method_exists($c, 'getApplicationSubscribedEvents')) {
            return array_merge($c::getApplicationSubscribedEvents() ?? [], $events);
        }
        return $events;
    }

    /**
     * @return string
     */
    public static function getAppId(): string
    {
        $c = get_called_class();
        if (property_exists($c, 'APP_ID')) {
            return $c::$APP_ID;
        } else {
            throw new RuntimeException("invalid APP ID");
        }
    }

    /**
     * @return bool
     */
    public static function autoActivate(): bool
    {
        $c = get_called_class();
        return property_exists($c, 'AUTO_ACTIVATE') ? $c::$AUTO_ACTIVATE : self::$AUTO_ACTIVATE;
    }

    /**
     * @return array
     */
    public static function getOAuth2Scopes(): array
    {
        $c = get_called_class();
        return property_exists($c, 'SCOPES') ? $c::$SCOPES : self::$SCOPES;
    }

    /**
     * @return array
     */
    public static function availablePrices(): array
    {
        $c = get_called_class();
        $prices = property_exists($c, 'PRICES') ? $c::$PRICES : self::$PRICES;
        return !empty($prices[Tools::getEnv()]) ? $prices[Tools::getEnv()] : $prices;
    }

    /**
     * @param LoggerInterface $logger
     */
    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    /**
     * @return array
     */
    public function availableMethods(): array
    {
        $c = get_called_class();
        return property_exists($c, 'METHODS') ? $c::$METHODS : self::$METHODS;
    }

    public function dispatchEvent(ApplicationEvent $event, string $eventName)
    {
        $c = get_called_class();
        if ($event->getApplication()->getAppId() == $c::getAppId()) {
            switch ($eventName) {
                case ApplicationEvent::SHOW:
                    $this->show($event->getApplication());
                    break;
                case ApplicationEvent::BEFORE_ENABLE:
                    $this->beforeEnable($event->getApplication());
                    break;
                case ApplicationEvent::BEFORE_DISABLE:
                    $this->beforeDisable($event->getApplication());
                    break;
                case ApplicationEvent::ENABLED:
                    $this->enabled($event->getApplication());
                    break;
                case ApplicationEvent::DISABLED:
                    $this->disabled($event->getApplication());
                    break;
                case ApplicationEvent::NEW_OAUTH:
                    $this->onNewOAuth($event->getApplication());
                    break;
                case ApplicationEvent::REFRESH_OAUTH:
                    $this->onRefreshOAuth($event->getApplication());
                    break;
                case ApplicationEvent::UPDATED_METADATA:
                    $this->onUpdateMetadata($event->getApplication(), $event->getPreviousMetadata() ?: []);
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * @param Organism $organism
     * @return bool
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws \Doctrine\ORM\ORMException
     * @throws ReflectionException
     */
    public function isEnabled(Organism $organism): bool
    {
        $c = get_called_class();
        if (method_exists($c, 'getAppId')) {
            $application = $this->applicationService->getByOrganismAndAppId($organism, $c::getAppId(), true, false);
            return $application && $application->getEnabled();
        } else {
            return false;
        }
    }

    /**
     * @throws NonUniqueResultException
     * @throws \Doctrine\ORM\ORMException
     * @throws ORMException|ReflectionException
     */
    public function getApplication(Organism $organism): ?Application
    {
        $c = get_called_class();
        if (method_exists($c, 'getAppId')) {
            $application = $this->applicationService->getByOrganismAndAppId($organism, $c::getAppId(), true, false);
            return $application->getEnabled() ? $application : null;
        } else {
            return null;
        }
    }

    abstract static public function getAllowedSubscriptionTypes(Subscription $subscription): array;

    /**
     * @param Application $application
     * @return void
     */
    abstract public function show(Application $application): void;

    /**
     * @param Application $application
     */
    abstract public function enabled(Application $application): void;

    /**
     * @param Application $application
     */
    abstract public function disabled(Application $application): void;

    /**
     * @param Application $application
     */
    abstract public function beforeEnable(Application $application): void;

    /**
     * @param Application $application
     */
    abstract public function beforeDisable(Application $application): void;

    /**
     * @param Application $application
     */
    abstract public function onNewOAuth(Application $application): void;

    /**
     * @param Application $application
     */
    abstract public function onRefreshOAuth(Application $application): void;

    /**
     * @param Application $application
     * @param array $previousMetadata
     */
    abstract public function onUpdateMetadata(Application $application, array $previousMetadata): void;

    /**
     * @return array
     */
    abstract static public function getApplicationSubscribedEvents(): array;

}
