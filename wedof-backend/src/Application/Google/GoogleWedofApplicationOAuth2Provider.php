<?php

namespace App\Application\Google;

use App\Application\oauth2\WedofApplicationOAuth2Provider;
use App\Entity\Application;
use KnpU\OAuth2ClientBundle\Client\Provider\GoogleClient;

class GoogleWedofApplicationOAuth2Provider extends GoogleClient implements WedofApplicationOAuth2Provider
{

    /**
     * GoogleWedofApplicationOAuth2Provider constructor.
     * @param array $options
     * @param array $collaborators
     */
    public function __construct(array $options = [], array $collaborators = [])
    {
        parent::__construct($options, $collaborators);
    }

    /**
     * @param Application $application
     */
    public function configure(Application $application)
    {
    }

    /**
     * @return string[]
     */
    public function getDefaultScopes(): array
    {
        return GoogleApplication::getOAuth2Scopes();
    }
}
