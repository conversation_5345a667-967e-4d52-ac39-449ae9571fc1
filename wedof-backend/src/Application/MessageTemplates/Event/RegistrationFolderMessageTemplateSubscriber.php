<?php

namespace App\Application\MessageTemplates\Event;

use App\Application\MessageTemplates\Service\MessageTemplateService;
use App\Event\RegistrationFolder\RegistrationFolderEvents;
use App\Library\utils\enums\RegistrationFolderAttendeeStates;
use App\Library\utils\enums\RegistrationFolderBillingStates;
use App\Library\utils\enums\RegistrationFolderControlStates;
use App\Library\utils\enums\RegistrationFolderStates;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use ReflectionException;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Twig\Error\LoaderError;
use Twig\Error\SyntaxError;

class RegistrationFolderMessageTemplateSubscriber implements EventSubscriberInterface
{
    private MessageTemplateService $messageTemplateService;

    public function __construct(MessageTemplateService $messageTemplateService)
    {
        $this->messageTemplateService = $messageTemplateService;
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        $events = array(
            RegistrationFolderEvents::CREATED => 'messageTemplateOnRegistrationFolder',
            RegistrationFolderEvents::UPDATED => 'messageTemplateOnRegistrationFolder'
        );
        foreach (RegistrationFolderStates::valuesStates() as $state) {
            $events["registrationFolder." . $state->getValue()] = 'messageTemplateOnRegistrationFolder';
        }
        foreach (RegistrationFolderBillingStates::valuesStates() as $state) {
            $events["registrationFolderBilling." . $state->getValue()] = 'messageTemplateOnRegistrationFolder';
        }
        foreach (RegistrationFolderAttendeeStates::valuesStates() as $state) {
            $events["registrationFolderAttendee." . $state->getValue()] = 'messageTemplateOnRegistrationFolder';
        }
        foreach (RegistrationFolderControlStates::valuesStates() as $state) {
            $events["registrationFolderControl." . $state->getValue()] = 'messageTemplateOnRegistrationFolder';
        }
        return $events;
    }

    /**
     * @param RegistrationFolderEvents $event
     * @param string $eventName
     * @return void
     * @throws ContainerExceptionInterface
     * @throws LoaderError
     * @throws NonUniqueResultException
     * @throws NotFoundExceptionInterface
     * @throws ORMException
     * @throws ReflectionException
     * @throws SyntaxError
     * @throws \Doctrine\ORM\ORMException
     */
    public function messageTemplateOnRegistrationFolder(RegistrationFolderEvents $event, string $eventName): void
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }

        $this->messageTemplateService->createOrUpdateMessagesToSendForRegistrationFolders($event, $eventName);
    }
}