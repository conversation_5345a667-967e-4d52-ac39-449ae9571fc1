<?php

namespace App\Application\MessageTemplates\Event;

use App\Application\MessageTemplates\Service\MessageTemplateService;
use App\Entity\RegistrationFolderFile;
use App\Event\RegistrationFolder\RegistrationFolderEvents;
use App\Event\RegistrationFolderFile\RegistrationFolderFileEvents;
use App\Library\utils\enums\FileStates;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use ReflectionException;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Twig\Error\LoaderError;
use Twig\Error\SyntaxError;

class RegistrationFolderFileMessageTemplateSubscriber implements EventSubscriberInterface
{
    private MessageTemplateService $messageTemplateService;

    public function __construct(MessageTemplateService $messageTemplateService)
    {
        $this->messageTemplateService = $messageTemplateService;
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        $events = array(
            RegistrationFolderFileEvents::FILE_ADDED => 'messageTemplateOnRegistrationFolderFile',
            RegistrationFolderFileEvents::FILE_UPDATED => 'messageTemplateOnRegistrationFolderFile',
            RegistrationFolderFileEvents::FILE_DELETED => 'messageTemplateOnRegistrationFolderFile',
        );
        foreach (FileStates::valuesStates() as $state) {
            $events["registrationFolderFile." . $state->getValue()] = 'messageTemplateOnRegistrationFolderFile';
        }
        return $events;
    }

    /**
     * @param RegistrationFolderFileEvents $event
     * @param string $eventName
     * @return void
     * @throws ContainerExceptionInterface
     * @throws LoaderError
     * @throws NonUniqueResultException
     * @throws NotFoundExceptionInterface
     * @throws ORMException
     * @throws ReflectionException
     * @throws SyntaxError
     * @throws \Doctrine\ORM\ORMException
     */
    public function messageTemplateOnRegistrationFolderFile(RegistrationFolderFileEvents $event, string $eventName): void
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        $registrationFolder = $event->getRegistrationFolderFile()->getRegistrationFolder();
        $context = [RegistrationFolderFile::CLASSNAME => $event->getRegistrationFolderFile()->getEntityId()];
        $this->messageTemplateService->createOrUpdateMessagesToSendForRegistrationFolders(new RegistrationFolderEvents($registrationFolder), $eventName, $context);
    }
}