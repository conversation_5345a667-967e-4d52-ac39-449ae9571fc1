<?php

namespace App\Application\MessageTemplates\Event;

use App\Application\MessageTemplates\Service\MessageTemplateService;
use App\Event\CertificationFolder\CertificationFolderEvents;
use App\Library\utils\enums\CertificationFolderStates;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use ReflectionException;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Throwable;
use Twig\Error\LoaderError;
use Twig\Error\SyntaxError;

class CertificationFolderMessageTemplateSubscriber implements EventSubscriberInterface
{
    private MessageTemplateService $messageTemplateService;

    public function __construct(MessageTemplateService $messageTemplateService)
    {
        $this->messageTemplateService = $messageTemplateService;
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents()
    {
        $events = array(
            CertificationFolderEvents::CREATED => 'messageTemplateOnCertificationFolder',
            CertificationFolderEvents::UPDATED => 'messageTemplateOnCertificationFolder',
            CertificationFolderEvents::IN_TRAINING_STARTED => 'messageTemplateOnCertificationFolder',
            CertificationFolderEvents::IN_TRAINING_ENDED => 'messageTemplateOnCertificationFolder',
            CertificationFolderEvents::ACCROCHAGE_OK => 'messageTemplateOnCertificationFolder',
            CertificationFolderEvents::ACCROCHAGE_KO => 'messageTemplateOnCertificationFolder',
            CertificationFolderEvents::MISSING_DATA => 'messageTemplateOnCertificationFolder'
        );
        foreach (CertificationFolderStates::valuesStates() as $state) {
            $events["certificationFolder." . $state->getValue()] = 'messageTemplateOnCertificationFolder';
        }
        return $events;
    }

    /**
     * @param CertificationFolderEvents $event
     * @param string $eventName
     * @return void
     * @throws ContainerExceptionInterface
     * @throws LoaderError
     * @throws NonUniqueResultException
     * @throws NotFoundExceptionInterface
     * @throws ORMException
     * @throws ReflectionException
     * @throws SyntaxError
     * @throws NoResultException
     * @throws \Doctrine\ORM\ORMException
     * @throws Throwable
     */
    public function messageTemplateOnCertificationFolder(CertificationFolderEvents $event, string $eventName): void
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        $this->messageTemplateService->createOrUpdateMessagesToSendForCertificationFolders($event, $eventName);
    }
}