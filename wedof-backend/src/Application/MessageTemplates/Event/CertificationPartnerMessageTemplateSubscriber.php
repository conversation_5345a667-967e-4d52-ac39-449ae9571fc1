<?php

namespace App\Application\MessageTemplates\Event;

use App\Application\MessageTemplates\Service\MessageTemplateService;
use App\Event\CertificationPartner\CertificationPartnerEvents;
use App\Library\utils\enums\CertificationPartnerStates;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use ReflectionException;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Throwable;
use Twig\Error\LoaderError;
use Twig\Error\SyntaxError;

class CertificationPartnerMessageTemplateSubscriber implements EventSubscriberInterface
{
    private MessageTemplateService $messageTemplateService;

    public function __construct(MessageTemplateService $messageTemplateService)
    {
        $this->messageTemplateService = $messageTemplateService;
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        $events = [];
        foreach (CertificationPartnerStates::valuesStates() as $state) {
            $events["certificationPartner." . $state->getValue()] = 'messageTemplateOnCertificationPartner';
        }
        return $events;
    }


    /**
     * @param CertificationPartnerEvents $event
     * @param string $eventName
     * @throws ContainerExceptionInterface
     * @throws LoaderError
     * @throws NonUniqueResultException
     * @throws NotFoundExceptionInterface
     * @throws ORMException
     * @throws ReflectionException
     * @throws SyntaxError
     * @throws NoResultException
     * @throws \Doctrine\ORM\ORMException
     * @throws Throwable
     */
    public function messageTemplateOnCertificationPartner(CertificationPartnerEvents $event, string $eventName): void
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        $this->messageTemplateService->createOrUpdateMessagesToSendForCertificationPartners($event, $eventName);
    }
}