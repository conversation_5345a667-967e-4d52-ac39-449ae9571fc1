<?php

namespace App\Application\MessageTemplates\Event;

use App\Application\MessageTemplates\Entity\MessageTemplate;
use App\Entity\Certification;
use App\Event\TagSerializeSubscriber;
use <PERSON><PERSON>\Serializer\EventDispatcher\EventSubscriberInterface;
use <PERSON><PERSON>\Serializer\EventDispatcher\ObjectEvent;
use <PERSON><PERSON>\Serializer\JsonSerializationVisitor;
use <PERSON><PERSON>\Serializer\Metadata\StaticPropertyMetadata;

class MessageTemplateSerializeSubscriber extends TagSerializeSubscriber implements EventSubscriberInterface
{

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return array(
            array(
                'event' => 'serializer.post_serialize',
                'method' => 'onPostSerialize',
                'class' => MessageTemplate::class, // if no class, subscribe to every serialization,
                'format' => 'json'
            ),
        );
    }

    //-------------------
    // METHODES PUBLIQUES
    //-------------------
    /**
     * @param ObjectEvent $event
     */
    public function onPostSerialize(ObjectEvent $event): void
    {
        /** @var MessageTemplate $messageTemplate */
        $messageTemplate = $event->getObject();
        /** @var JsonSerializationVisitor $visitor */
        $visitor = $event->getVisitor();

        $certifications = null;
        /** @var Certification $certification */
        foreach ($messageTemplate->getCertifications() as $certification) {
            $certifications[] = $certification->getCertifInfo();
        }

        $visitor->visitProperty(new StaticPropertyMetadata('', 'certifInfos', null), $certifications);
        $this->serializeTags($messageTemplate, $visitor);
    }
}