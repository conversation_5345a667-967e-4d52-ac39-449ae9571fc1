<?php

namespace App\Application\MessageTemplates\Event;

use App\Application\MessageTemplates\Service\MessageTemplateService;
use App\Entity\CertificationPartnerAudit;
use App\Event\CertificationPartner\CertificationPartnerEvents;
use App\Event\CertificationPartnerAudit\CertificationPartnerAuditEvents;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use ReflectionException;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Throwable;
use Twig\Error\LoaderError;
use Twig\Error\SyntaxError;

class CertificationPartnerAuditMessageTemplateSubscriber implements EventSubscriberInterface
{
    private MessageTemplateService $messageTemplateService;

    public function __construct(MessageTemplateService $messageTemplateService)
    {
        $this->messageTemplateService = $messageTemplateService;
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return [
            CertificationPartnerAuditEvents::PENDING_COMPUTATION => 'messageTemplateOnCertificationPartnerAudit',
            CertificationPartnerAuditEvents::COMPUTING => 'messageTemplateOnCertificationPartnerAudit',
            CertificationPartnerAuditEvents::IN_PROGRESS => 'messageTemplateOnCertificationPartnerAudit',
            CertificationPartnerAuditEvents::COMPLETED => 'messageTemplateOnCertificationPartnerAudit',
            CertificationPartnerAuditEvents::COMPLIANT => 'messageTemplateOnCertificationPartnerAudit',
            CertificationPartnerAuditEvents::NON_COMPLIANT => 'messageTemplateOnCertificationPartnerAudit',
            CertificationPartnerAuditEvents::PARTIALLY_COMPLIANT => 'messageTemplateOnCertificationPartnerAudit',
        ];
    }

    /**
     * @param CertificationPartnerAuditEvents $event
     * @param string $eventName
     * @return void
     * @throws LoaderError
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws ReflectionException
     * @throws SyntaxError
     * @throws \Doctrine\ORM\ORMException
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws Throwable
     */
    public function messageTemplateOnCertificationPartnerAudit(CertificationPartnerAuditEvents $event, string $eventName): void
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }

        $certificationPartner = $event->getCertificationPartner();
        $context = [CertificationPartnerAudit::CLASSNAME => $event->getCertificationPartnerAudit()->getId()];
        $this->messageTemplateService->createOrUpdateMessagesToSendForCertificationPartners(new CertificationPartnerEvents($certificationPartner), $eventName, $context);
    }
}