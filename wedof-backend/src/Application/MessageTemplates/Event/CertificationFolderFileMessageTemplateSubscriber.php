<?php

namespace App\Application\MessageTemplates\Event;

use App\Application\MessageTemplates\Service\MessageTemplateService;
use App\Entity\CertificationFolderFile;
use App\Event\CertificationFolder\CertificationFolderEvents;
use App\Event\CertificationFolderFile\CertificationFolderFileEvents;
use App\Library\utils\enums\FileStates;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use ReflectionException;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Throwable;
use Twig\Error\LoaderError;
use Twig\Error\SyntaxError;

class CertificationFolderFileMessageTemplateSubscriber implements EventSubscriberInterface
{
    private MessageTemplateService $messageTemplateService;

    public function __construct(MessageTemplateService $messageTemplateService)
    {
        $this->messageTemplateService = $messageTemplateService;
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents()
    {
        $events = array(
            CertificationFolderFileEvents::FILE_ADDED => 'messageTemplateOnCertificationFolderFile',
            CertificationFolderFileEvents::FILE_UPDATED => 'messageTemplateOnCertificationFolderFile',
            CertificationFolderFileEvents::FILE_DELETED => 'messageTemplateOnCertificationFolderFile',
        );
        foreach (FileStates::valuesStates() as $state) {
            $events["certificationFolderFile." . $state->getValue()] = 'messageTemplateOnCertificationFolderFile';
        }
        return $events;
    }

    /**
     * @param CertificationFolderFileEvents $event
     * @param string $eventName
     * @return void
     * @throws LoaderError
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws ReflectionException
     * @throws SyntaxError
     * @throws \Doctrine\ORM\ORMException
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws Throwable
     */
    public function messageTemplateOnCertificationFolderFile(CertificationFolderFileEvents $event, string $eventName): void
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        $certificationFolder = $event->getCertificationFolderFile()->getCertificationFolder();
        $context = [CertificationFolderFile::CLASSNAME => $event->getCertificationFolderFile()->getId()];
        $this->messageTemplateService->createOrUpdateMessagesToSendForCertificationFolders(new CertificationFolderEvents($certificationFolder), $eventName, $context);
    }
}