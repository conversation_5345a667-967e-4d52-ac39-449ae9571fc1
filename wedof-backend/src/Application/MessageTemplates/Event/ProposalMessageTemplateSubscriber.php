<?php

namespace App\Application\MessageTemplates\Event;

use App\Application\MessageTemplates\Service\MessageTemplateService;
use App\Event\Proposal\ProposalEvents;
use App\Library\utils\enums\ProposalStates;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use ReflectionException;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Twig\Error\LoaderError;
use Twig\Error\SyntaxError;

class ProposalMessageTemplateSubscriber implements EventSubscriberInterface
{
    private MessageTemplateService $messageTemplateService;

    public function __construct(MessageTemplateService $messageTemplateService)
    {
        $this->messageTemplateService = $messageTemplateService;
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        $events = array(
            ProposalEvents::CREATED => 'messageTemplateOnProposal',
            ProposalEvents::UPDATED => 'messageTemplateOnProposal',
            ProposalEvents::DELETED => 'messageTemplateOnProposal'
        );
        foreach (ProposalStates::valuesStates() as $state) {
            if ($state->getValue() !== ProposalStates::ALL()->getValue() && $state->getValue() !== ProposalStates::TEMPLATE()->getValue()) {
                $events["proposal." . $state->getValue()] = 'messageTemplateOnProposal';
            }
        }

        return $events;
    }

    /**
     * @param ProposalEvents $event
     * @param string $eventName
     * @return void
     * @throws LoaderError
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws ReflectionException
     * @throws SyntaxError
     * @throws \Doctrine\ORM\ORMException
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function messageTemplateOnProposal(ProposalEvents $event, string $eventName): void
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        if ($event->getProposal()->isIndividual()) {
            $this->messageTemplateService->createOrUpdateMessagesToSendForProposals($event, $eventName);
        }
    }
}