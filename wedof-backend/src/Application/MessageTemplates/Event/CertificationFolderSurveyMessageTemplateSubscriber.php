<?php

namespace App\Application\MessageTemplates\Event;

use App\Application\MessageTemplates\Service\MessageTemplateService;
use App\Event\CertificationFolderSurvey\CertificationFolderSurveyEvents;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use ReflectionException;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Throwable;
use Twig\Error\LoaderError;
use Twig\Error\SyntaxError;

class CertificationFolderSurveyMessageTemplateSubscriber implements EventSubscriberInterface
{
    private MessageTemplateService $messageTemplateService;

    public function __construct(MessageTemplateService $messageTemplateService)
    {
        $this->messageTemplateService = $messageTemplateService;
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return [
            CertificationFolderSurveyEvents::CREATED => 'messageTemplateOnCertificationFolderSurvey',
            CertificationFolderSurveyEvents::INITIAL_EXPERIENCE_ANSWERED => 'messageTemplateOnCertificationFolderSurvey',
            CertificationFolderSurveyEvents::SIX_MONTH_EXPERIENCE_ANSWERED => 'messageTemplateOnCertificationFolderSurvey',
            CertificationFolderSurveyEvents::LONG_TERM_EXPERIENCE_ANSWERED => 'messageTemplateOnCertificationFolderSurvey',
            CertificationFolderSurveyEvents::SIX_MONTH_EXPERIENCE_AVAILABLE => 'messageTemplateOnCertificationFolderSurvey',
            CertificationFolderSurveyEvents::LONG_TERM_EXPERIENCE_AVAILABLE => 'messageTemplateOnCertificationFolderSurvey'
        ];
    }

    /**
     * @param CertificationFolderSurveyEvents $event
     * @param string $eventName
     * @return void
     * @throws ContainerExceptionInterface
     * @throws LoaderError
     * @throws NonUniqueResultException
     * @throws NotFoundExceptionInterface
     * @throws ORMException
     * @throws ReflectionException
     * @throws SyntaxError
     * @throws NoResultException
     * @throws \Doctrine\ORM\ORMException
     * @throws Throwable
     */
    public function messageTemplateOnCertificationFolderSurvey(CertificationFolderSurveyEvents $event, string $eventName): void
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        $this->messageTemplateService->createOrUpdateMessagesToSendForCertificationFolderSurveys($event, $eventName);
    }
}