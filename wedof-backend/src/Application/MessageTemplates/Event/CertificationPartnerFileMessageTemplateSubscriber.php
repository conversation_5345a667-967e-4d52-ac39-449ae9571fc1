<?php

namespace App\Application\MessageTemplates\Event;

use App\Application\MessageTemplates\Service\MessageTemplateService;
use App\Entity\CertificationPartnerFile;
use App\Event\CertificationPartner\CertificationPartnerEvents;
use App\Event\CertificationPartnerFile\CertificationPartnerFileEvents;
use App\Library\utils\enums\FileStates;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use ReflectionException;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Throwable;
use Twig\Error\LoaderError;
use Twig\Error\SyntaxError;

class CertificationPartnerFileMessageTemplateSubscriber implements EventSubscriberInterface
{
    private MessageTemplateService $messageTemplateService;

    public function __construct(MessageTemplateService $messageTemplateService)
    {
        $this->messageTemplateService = $messageTemplateService;
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        $events = array(
            CertificationPartnerFileEvents::FILE_ADDED => 'messageTemplateOnCertificationPartnerFile',
            CertificationPartnerFileEvents::FILE_UPDATED => 'messageTemplateOnCertificationPartnerFile',
            CertificationPartnerFileEvents::FILE_DELETED => 'messageTemplateOnCertificationPartnerFile',
        );
        foreach (FileStates::valuesStates() as $state) {
            $events["certificationPartnerFile." . $state->getValue()] = 'messageTemplateOnCertificationPartnerFile';
        }
        return $events;
    }

    /**
     * @param CertificationPartnerFileEvents $event
     * @param string $eventName
     * @return void
     * @throws LoaderError
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws ReflectionException
     * @throws SyntaxError
     * @throws \Doctrine\ORM\ORMException
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws Throwable
     */
    public function messageTemplateOnCertificationPartnerFile(CertificationPartnerFileEvents $event, string $eventName): void
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        $certificationPartner = $event->getCertificationPartnerFile()->getCertificationPartner();
        $context = [CertificationPartnerFile::CLASSNAME => $event->getCertificationPartnerFile()->getId()];
        $this->messageTemplateService->createOrUpdateMessagesToSendForCertificationPartners(new CertificationPartnerEvents($certificationPartner), $eventName, $context);
    }
}