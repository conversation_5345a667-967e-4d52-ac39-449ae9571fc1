<?php

namespace App\Application\MessageTemplates\Event;

use App\Application\MessageTemplates\Service\MessageTemplateService;
use App\Entity\Invoice;
use App\Event\CertificationPartner\CertificationPartnerEvents;
use App\Event\Invoice\InvoiceEvents;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use ReflectionException;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Throwable;
use Twig\Error\LoaderError;
use Twig\Error\SyntaxError;

class CertificationPartnerInvoiceMessageTemplateSubscriber implements EventSubscriberInterface
{
    private MessageTemplateService $messageTemplateService;

    public function __construct(MessageTemplateService $messageTemplateService)
    {
        $this->messageTemplateService = $messageTemplateService;
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return [
            InvoiceEvents::CERTIFICATION_PARTNER_CREATED => 'messageTemplateOnCertificationPartnerInvoice',
            InvoiceEvents::CERTIFICATION_PARTNER_UPDATED => 'messageTemplateOnCertificationPartnerInvoice',
            InvoiceEvents::CERTIFICATION_PARTNER_PAID => 'messageTemplateOnCertificationPartnerInvoice',
            InvoiceEvents::CERTIFICATION_PARTNER_DELETED => 'messageTemplateOnCertificationPartnerInvoice'
        ];
    }

    /**
     * @param InvoiceEvents $event
     * @param string $eventName
     * @return void
     * @throws LoaderError
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws ReflectionException
     * @throws SyntaxError
     * @throws \Doctrine\ORM\ORMException
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws Throwable
     */
    public function messageTemplateOnCertificationPartnerInvoice(InvoiceEvents $event, string $eventName): void
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }

        $certificationPartner = $event->getCertificationPartner();
        $context = [Invoice::CLASSNAME => $event->getInvoice()->getId()];
        $this->messageTemplateService->createOrUpdateMessagesToSendForCertificationPartners(new CertificationPartnerEvents($certificationPartner), $eventName, $context);
    }
}