<?php

namespace App\Application\MessageTemplates\Controller;

use App\Application\MessageTemplates\Entity\MessageTemplate;
use App\Application\MessageTemplates\MessageTemplatesWedofApplication;
use App\Application\MessageTemplates\Security\MessageTemplateVoter;
use App\Application\MessageTemplates\Service\MessageService;
use App\Application\MessageTemplates\Service\MessageTemplateService;
use App\Entity\CertificationFolder;
use App\Entity\CertificationFolderSurvey;
use App\Entity\CertificationPartner;
use App\Entity\Organism;
use App\Entity\Proposal;
use App\Entity\RegistrationFolder;
use App\Entity\User;
use App\Exception\WedofAccessDeniedHttpException;
use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofNotFoundHttpException;
use App\Exception\WedofSubscriptionException;
use App\Library\utils\enums\MessageTemplateStates;
use App\Library\utils\enums\MessageTemplateTypes;
use App\Library\utils\enums\SubscriptionTrainingTypes;
use App\Library\utils\Tools;
use App\Security\Voter\CertificationFolderVoter;
use App\Security\Voter\OrganismVoter;
use App\Service\AccessService;
use App\Service\ApplicationService;
use App\Service\CertificationService;
use App\Service\OrganismService;
use DateTime;
use Doctrine\ORM\Exception\NotSupported;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Exception;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcherInterface;
use FOS\RestBundle\View\View;
use Knp\Component\Pager\PaginatorInterface;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use ReflectionException;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;
use Symfony\Component\Mime\Email;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validation;
use Symfony\Contracts\Translation\TranslatorInterface;
use Twig\Error\LoaderError;
use Twig\Error\SyntaxError;


class MessageTemplateController extends AbstractFOSRestController
{
    //--------------
    // METHODES CRUD
    //--------------

    /**
     * @Rest\Get("/api/messageTemplates/{id}")
     * @IsGranted(MessageTemplateVoter::VIEW, subject="messageTemplate", message="not allowed")
     * @Rest\View(statusCode=200)
     *
     * @param MessageTemplate $messageTemplate
     * @return MessageTemplate
     */
    public function show(MessageTemplate $messageTemplate): MessageTemplate
    {
        return $messageTemplate;
    }

    /**
     * @Rest\Get("/api/messageTemplates")
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_MESSAGETEMPLATE:READ')", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @Rest\QueryParam(name="entityClass", requirements=@Assert\Type("string"), default="all", description="Permet de n'obtenir que les templates de message dans l'entité considéré - par défaut tous les états sont retournés. Valeurs possibles : 'all', 'CertificationFolder', 'RegistrationFolder', 'Proposal', 'CertificationPartner', 'CertificationFolderSurvey'.")
     * @Rest\QueryParam(name="state", requirements=@Assert\Type("string"), default="all", description="Permet de n'obtenir que les templates de message dans l'état considéré - par défaut tous les états sont retournés. Valeurs possibles : 'all', 'active', 'inactive'. Il est possible de demander plusieurs states en séparant chaque state par une virgule.")
     * @Rest\QueryParam(name="type", requirements=@Assert\Type("string"), default="all", description="Permet de n'obtenir que les templates de message pour le type considéré - par défaut tous les types sont retournés. Valeurs possibles : 'all', 'email', 'sms'. Il est possible de demander plusieurs types en séparant chaque type par une virgule.")
     * @Rest\QueryParam(name="certifInfo", requirements=@Assert\Type("string"), nullable=true, description="Permet de n'obtenir que les templates de message liés à la certification considérée - par défaut tous les templates de message de toutes les certifications sont retournés. Il est possible de demander plusieurs certiInfos en séparant chaque certifInfo par une virgule.")
     * @Rest\QueryParam(name="limit", requirements="\d+", default="100", description="Nombre d'éléments retourné par requête - par défaut 100.")
     * @Rest\QueryParam(name="page", requirements="\d+", default="1", description="Numéro de page de la requête - par défaut la première.")
     *
     * @param ParamFetcherInterface $paramFetcher
     * @param PaginatorInterface $paginator
     * @param MessageTemplateService $messageTemplateService
     * @param CertificationService $certificationService
     * @return Response
     */
    public function list(ParamFetcherInterface $paramFetcher, PaginatorInterface $paginator, MessageTemplateService $messageTemplateService, CertificationService $certificationService): Response
    {
        $parameters = $paramFetcher->all(true);
        /** @var User $user */
        $user = $this->getUser();

        if (isset($parameters['entityClass'])) {
            $entityClassAvailable = [RegistrationFolder::CLASSNAME, CertificationFolder::CLASSNAME, Proposal::CLASSNAME, CertificationPartner::CLASSNAME, CertificationFolderSurvey::CLASSNAME, 'all'];
            $parameters['entityClass'] = explode(",", $parameters['entityClass']);
            foreach ($parameters['entityClass'] as $entityClass) {
                if (!in_array($entityClass, $entityClassAvailable)) {
                    throw new WedofBadRequestHttpException("Erreur sur les valeurs renvoyées 'entityClass', elles doivent être : " . join(",", $entityClassAvailable) . ".");
                }
            }
        }
        $allowedStates = [MessageTemplateStates::ACTIVE()->getValue(), MessageTemplateStates::INACTIVE()->getValue()];
        if (isset($parameters['state']) && $parameters['state'] !== MessageTemplateStates::ALL()->getValue()) {
            $parameters['state'] = explode(",", $parameters['state']);
            foreach ($parameters['state'] as $state) {
                if (!in_array($state, $allowedStates)) {
                    throw new WedofBadRequestHttpException("Erreur sur les valeurs renvoyées 'state', elles doivent être : " . join(",", $allowedStates) . ".");
                }
            }
        } else {
            $parameters['state'] = $allowedStates;
        }
        if (isset($parameters['type'])) {
            $parameters['type'] = explode(",", $parameters['type']);
            foreach ($parameters['type'] as $type) {
                if (!in_array($type, MessageTemplateTypes::valuesTypes())) {
                    throw new WedofBadRequestHttpException("Erreur sur les valeurs renvoyées 'type', elles doivent être : " . join(",", MessageTemplateTypes::valuesTypes()) . ".");
                }
            }
        }
        if (isset($parameters['certifInfo'])) {
            $parameters['certifInfo'] = explode(",", $parameters['certifInfo']);
            $parameters['certifications'] = $this->getCertifications($parameters['certifInfo'], $certificationService);
        }

        $data = $paginator->paginate($messageTemplateService->listReturnQueryBuilder($user->getMainOrganism(), $parameters), intval($parameters['page']), intval($parameters['limit']));
        $view = $this->view($data->getItems(), 200);
        $view->setHeader("x-total-count", $data->getTotalItemCount());
        $view->setHeader("x-current-page", $data->getCurrentPageNumber());
        $view->setHeader("x-item-per-page", $data->getItemNumberPerPage());
        return $this->handleView($view);
    }

    /**
     * @Rest\Post("api/messageTemplates")
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_MESSAGETEMPLATE:WRITE')", message="not allowed")
     * @Rest\QueryParam(name="siret", requirements="\d{14}", nullable=true, description="Sélectionne l'organisme pour lequel le template de message sera créé - par défaut l'organisme de l'utilisateur courant.")
     * @Rest\View(statusCode=201)
     *
     * @param ParamFetcherInterface $paramFetcher
     * @param Request $request
     * @param MessageTemplateService $messageTemplateService
     * @param OrganismService $organismService
     * @param CertificationService $certificationService
     * @param MessageService $messageService
     * @param AccessService $accessService
     * @param TranslatorInterface $translator
     * @return MessageTemplate|View
     * @throws ContainerExceptionInterface
     * @throws LoaderError
     * @throws NonUniqueResultException
     * @throws NotFoundExceptionInterface
     * @throws ORMException
     * @throws ReflectionException
     * @throws SyntaxError
     * @throws WedofSubscriptionException
     * @throws \Doctrine\ORM\ORMException
     */
    public function create(ParamFetcherInterface $paramFetcher, Request $request, MessageTemplateService $messageTemplateService, OrganismService $organismService, CertificationService $certificationService, MessageService $messageService, AccessService $accessService, TranslatorInterface $translator)
    {
        /* @var $user User */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();
        if (!$accessService->isApplicationAllowedAndEnabled(MessageTemplatesWedofApplication::getAppId(), $organism)) {
            throw new WedofAccessDeniedHttpException("Votre abonnement ne permet pas l'utilisation de l'application des Messages et notifications ou vous n'avez pas activé l'application.");
        }

        $parameters = $paramFetcher->all(true);

        if (isset($parameters['siret'])) {
            $organism = $organismService->getBySiret($parameters['siret']);
            if (!empty($organism)) {
                if (!$this->isGranted(OrganismVoter::EDIT, $organism)) {
                    throw new WedofAccessDeniedHttpException("Vous n'êtes pas autorisé à créer un template de message pour l'organisme associé au siret " . $parameters['siret']);
                }
            } else {
                throw new WedofNotFoundHttpException("L'organisme associé au siret " . $parameters['siret'] . " n'a pas été trouvé.");
            }
        }

        $body = json_decode($request->getContent(), true);
        if (!isset($body['type']) || !in_array($body['type'], MessageTemplateTypes::valuesToString())) {
            throw new WedofBadRequestHttpException("Un type doit être spécifié et avoir une des valeurs suivantes : " . implode(',', MessageTemplateTypes::valuesToString()));
        }
        if ($body['type'] === MessageTemplateTypes::SMS()->getValue() && !$organism->getSubscription()->isAllowPaidUsage()) {
            throw new WedofSubscriptionException("Votre abonnement ne vous permet pas de créer des messages sur les SMS.");
        }

        $sampleValues = $this->getSampleValuesForValidation($body, $messageService);
        $violations = $this->validateCreateBody($sampleValues, $translator);
        $this->checkOrganismSubscriptionForEntityClass($organism, $body['entityClass']);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }

        if (isset($body['certifInfos'])) {
            $body['certifications'] = $this->getCertifications($body['certifInfos'], $certificationService);
        }
        if (isset($body['delay'])) {
            $date = new DateTime('now');
            try {
                $date->modify($body['delay']);
            } catch (Exception $exception) {
                throw new WedofBadRequestHttpException("Format de la variable delay incorrect.");
            }
        }

        return $messageTemplateService->create($organism, $body);
    }

    /**
     * @Rest\Post("/api/messageTemplates/{id}/duplicate")
     * @IsGranted(MessageTemplateVoter::EDIT, subject="messageTemplate", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @param MessageTemplate $messageTemplate
     * @param MessageTemplateService $messageTemplateService
     * @return MessageTemplate
     */
    public function duplicate(MessageTemplate $messageTemplate, MessageTemplateService $messageTemplateService): MessageTemplate
    {
        /* @var $user User */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();
        $body = [];
        $properties = ['title', 'subject', 'type', 'body', 'tags', 'entityClass', 'sendAs', 'replyTo', 'cc', 'cci', 'to', 'delay', 'events', 'certifications', 'enforceConditions', 'qualiopiIndicators', 'allowResend'];
        foreach ($properties as $property) {
            if ($property === 'tags') {
                $body[$property] = explode(",", $messageTemplate->getTagsText());
            } else if ($property === 'title') {
                $body[$property] = "Copie de " . $messageTemplate->getTitle();
            } else {
                $body[$property] = $messageTemplate->{"get" . ucfirst($property)}();
            }
        }
        return $messageTemplateService->create($organism, $body);
    }

    /**
     * @Rest\Put("/api/messageTemplates/{id}")
     * @IsGranted(MessageTemplateVoter::EDIT, subject="messageTemplate", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @param MessageTemplate $messageTemplate
     * @param MessageTemplateService $messageTemplateService
     * @param CertificationService $certificationService
     * @param MessageService $messageService
     * @param Request $request
     * @param TranslatorInterface $translator
     * @return MessageTemplate|View
     * @throws ContainerExceptionInterface
     * @throws LoaderError
     * @throws NotFoundExceptionInterface
     * @throws SyntaxError
     */
    public function update(MessageTemplate $messageTemplate, MessageTemplateService $messageTemplateService, CertificationService $certificationService, MessageService $messageService, Request $request, TranslatorInterface $translator)
    {
        /* @var $user User */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();
        $body = json_decode($request->getContent(), true) ?? [];
        $body['type'] = $body['type'] ?? $messageTemplate->getType();
        $body['to'] = $body['to'] ?? $messageTemplate->getTo();
        $sampleValues = $this->getSampleValuesForValidation($body, $messageService);
        $violations = $this->validateUpdateBody($sampleValues, $translator);
        $this->checkOrganismSubscriptionForEntityClass($organism, $body['entityClass']);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }

        if (isset($body['certifInfos'])) {
            $body['certifications'] = $this->getCertifications($body['certifInfos'], $certificationService);
        }
        if (isset($body['delay'])) {
            $date = new DateTime('now');
            try {
                $date->modify($body['delay']);
            } catch (Exception $exception) {
                throw new WedofBadRequestHttpException("Format de la variable delay incorrect.");
            }
        }

        return $messageTemplateService->update($messageTemplate, $body);
    }

    /**
     * @Rest\Delete("/api/messageTemplates/{id}")
     * @IsGranted(MessageTemplateVoter::EDIT, subject="messageTemplate", message="not allowed")
     * @Rest\View(StatusCode = 204)
     *
     * @param MessageTemplate $messageTemplate
     * @param MessageTemplateService $messageTemplateService
     * @return void
     */
    public function delete(MessageTemplate $messageTemplate, MessageTemplateService $messageTemplateService): void
    {
        if ($messageTemplate->getMessages()->count() > 0) {
            throw new WedofBadRequestHttpException("Erreur, il n'est pas possible de supprimer ce template de message car il y a des messages associés. Si nécessaire, vous pouvez par contre le désactiver.");
        }
        $messageTemplateService->delete($messageTemplate);
    }

    /**
     * @Rest\Post("/api/messageTemplates/{id}/active")
     * @IsGranted(MessageTemplateVoter::EDIT, subject="messageTemplate", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @param MessageTemplate $messageTemplate
     * @param MessageTemplateService $messageTemplateService
     * @return MessageTemplate
     */
    public function activate(MessageTemplate $messageTemplate, MessageTemplateService $messageTemplateService): MessageTemplate
    {
        return $messageTemplateService->activate($messageTemplate);
    }

    /**
     * @Rest\Post("/api/messageTemplates/{id}/inactive")
     * @IsGranted(MessageTemplateVoter::EDIT, subject="messageTemplate",  message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @param MessageTemplate $messageTemplate
     * @param MessageTemplateService $messageTemplateService
     * @return MessageTemplate
     */
    public function deactivate(MessageTemplate $messageTemplate, MessageTemplateService $messageTemplateService): MessageTemplate
    {
        return $messageTemplateService->deactivate($messageTemplate);
    }

    /**
     * @Rest\Get("/app/messageTemplates/{id}/testTemplate")
     * @IsGranted(MessageTemplateVoter::EDIT, subject="messageTemplate",  message="not allowed")
     * @Rest\View(StatusCode = 200)
     * @param MessageTemplate $messageTemplate
     * @param MessageService $messageService
     * @throws ContainerExceptionInterface
     * @throws LoaderError
     * @throws NotFoundExceptionInterface
     * @throws SyntaxError
     * @throws TransportExceptionInterface
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws \Doctrine\DBAL\Exception
     * @throws NotSupported
     * @throws \Symfony\Component\Notifier\Exception\TransportExceptionInterface
     */
    public function testTemplate(MessageTemplate $messageTemplate, MessageService $messageService): void
    {
        /** @var User $user */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();
        if ($messageTemplate->getType() == MessageTemplateTypes::EMAIL()->getValue()) {
            $testMessage = [];
            if ($messageTemplate->getSendAs()) {
                $testMessage['from'] = Tools::getFromEmail($organism->getName(), $messageTemplate->getSendAs());
            }
            $testMessage['to'] = [$user->getEmail()];
            $testMessage['cc'] = $messageTemplate->getCc() ? $messageService->getComputedArray($messageTemplate->getCc(), null, null, false, true, $organism) : null;
            $testMessage['cci'] = $messageTemplate->getCci() ? $messageService->getComputedArray($messageTemplate->getCci(), null, null, false, true, $organism) : null;
            $testMessage['replyTo'] = $messageService->getComputedArray($messageTemplate->getReplyTo(), null, null, false, true, $organism);
            $testMessage['subject'] = $messageService->getComputedContent($messageTemplate->getSubject(), null, null, false, true, $organism);
            $testMessage['body'] = $messageService->getComputedContent($messageTemplate->getBody(), null, null, true, true, $organism);
            $messageService->sendEmail($testMessage, Email::PRIORITY_HIGH);
        } else if ($messageTemplate->getType() == MessageTemplateTypes::SMS()->getValue()) {
            $sender = $organism->getSubDomain();
            if (strlen($sender) > 11) {
                $sender = substr($sender, 0, 11);
            }
            $testMessage['to'] = [$user->getPhoneNumber()];
            $computedBody = $messageService->getComputedContent($messageTemplate->getBody(), null, null, false, true, $organism);
            $computedBody = $messageService->shortenUrlSmsBody($computedBody, $messageTemplate);
            $testMessage['body'] = $computedBody;
            $testMessage['sender'] = strtoupper($sender);
            $messageService->sendSms($testMessage);
        }
    }

    /**
     * @Rest\Post("/app/messageTemplates/{templateName}", requirements={"templateName"="CertificationSuccess|CertificationFail|TrainingNotification|TrainingAccepted|Proposal|CertificationPartner|CertificationFolderSurveyCreated|CertificationPartnerAuditCompleted|AccrochageMissingData"})
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_MESSAGETEMPLATE:WRITE')", message="not allowed")
     * @Rest\View(statusCode=201)
     *
     * @param string $templateName
     * @param MessageTemplateService $messageTemplateService
     * @param ApplicationService $applicationService
     * @return MessageTemplate|View
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws \Doctrine\ORM\ORMException
     * @throws ReflectionException
     */
    public function createFromTemplate(string $templateName, MessageTemplateService $messageTemplateService, ApplicationService $applicationService): MessageTemplate
    {
        /** @var User $user */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();
        $subscription = $organism->getSubscription();
        $hasAccess = $applicationService->getByOrganismAndAppId($organism, MessageTemplatesWedofApplication::getAppId(), null, false);
        if (!$hasAccess) {
            throw new WedofBadRequestHttpException("Erreur, l'application 'Messages et notifications' n'est pas active.");
        }
        $allowedSubscriptionTypes = MessageTemplatesWedofApplication::getAllowedSubscriptionTypes($subscription);
        $certifierTemplate = ['CertificationSuccess', 'CertificationFail', 'CertificationPartner', 'CertificationFolderSurveyCreated', 'CertificationPartnerAuditCompleted', 'AccrochageMissingData'];
        $trainingTemplate = ['TrainingNotification', 'TrainingAccepted'];
        $proposalTemplate = ['Proposal'];
        $isAllowCertifierSubscription = in_array($subscription->getCertifierType(), $allowedSubscriptionTypes['certifier']);
        $isAllowTrainingSubscription = in_array($subscription->getTrainingType(), $allowedSubscriptionTypes['training']);

        if (!$isAllowCertifierSubscription && in_array($templateName, $certifierTemplate)) {
            throw new WedofBadRequestHttpException("Erreur, votre abonnement ne permet pas l'accès à ce modèle.");
        }
        if (!$isAllowTrainingSubscription && in_array($templateName, $trainingTemplate)) {
            throw new WedofBadRequestHttpException("Erreur, ce modèle est reservé aux organismes de formations.");
        }
        if (!$subscription->isAllowProposals() && in_array($templateName, $proposalTemplate)) {
            throw new WedofBadRequestHttpException("Erreur, votre abonnement ne permet pas l'accès à ce modèle");
        }
        return $messageTemplateService->createFromTemplate($organism, $templateName);
    }

    //----------------
    // METHODES PRIVES
    //----------------
    /**
     * @param array $body
     * @param TranslatorInterface $translator
     * @return ConstraintViolationListInterface
     */
    private function validateCreateBody(array $body, TranslatorInterface $translator): ConstraintViolationListInterface
    {
        $validator = Validation::createValidatorBuilder()->setTranslator($translator)->setTranslationDomain('validators')->getValidator();
        $phoneNumberAsserts = [new Assert\Type('string'), new Assert\Length(['min' => 10, 'max' => 13]), new Assert\Regex(['pattern' => Tools::MOBILEPHONE_PATTERN])];
        $assertCollection = [
            'title' => new Assert\Required([new Assert\Type('string'), new Assert\Length(['max' => 255])]),
            'type' => new Assert\Required([new Assert\Choice(MessageTemplateTypes::valuesToString())]),
            'body' => new Assert\Required([new Assert\Type('string')]),
            'entityClass' => new Assert\Required([new Assert\Choice([RegistrationFolder::CLASSNAME, CertificationFolder::CLASSNAME, Proposal::CLASSNAME, CertificationPartner::CLASSNAME, CertificationFolderSurvey::CLASSNAME])]),
            'events' => new Assert\Required([new Assert\NotNull(), new Assert\Type('array'), new Assert\All([new Assert\NotBlank(), new Assert\Type('string')])]),
            'tags' => new Assert\Optional(new Assert\Type('array')),
            'certifInfos' => new Assert\Optional(new Assert\Type('array')),
            'delay' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 20])]),
            'enforceConditions' => new Assert\Required([new Assert\NotNull(), new Assert\Type('boolean')]),
            'qualiopiIndicators' => new Assert\Optional([
                new Assert\Type('array'),
                new Assert\All([
                    new Assert\Type('integer'),
                    new Assert\Range(['min' => 1, 'max' => 32])
                ])
            ]),
            'allowResend' => new Assert\Required([new Assert\NotNull(), new Assert\Type('boolean')])
        ];
        if ($body['type'] === MessageTemplateTypes::EMAIL()->getValue()) {
            $assertCollection['sendAs'] = new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 255])]);
            $assertCollection['subject'] = new Assert\Required([new Assert\Type('string'), new Assert\Length(['max' => 255])]);
            $assertCollection['replyTo'] = new Assert\Required([new Assert\Type('array'), new Assert\All(new Assert\Email())]);
            $assertCollection['cc'] = new Assert\Optional([new Assert\Type('array'), new Assert\All(new Assert\Email())]);
            $assertCollection['cci'] = new Assert\Optional([new Assert\Type('array'), new Assert\All(new Assert\Email())]);
            $assertCollection['to'] = new Assert\Required([new Assert\Type('array'), new Assert\All(new Assert\Email())]);
        } else {
            $assertCollection['subject'] = new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 255])]);
            $assertCollection['replyTo'] = new Assert\Optional([new Assert\Type('array'), new Assert\All($phoneNumberAsserts)]);
            $assertCollection['to'] = new Assert\Required([new Assert\Type('array'), new Assert\All($phoneNumberAsserts)]);

        }
        $constraints = new Assert\Collection([
                'allowExtraFields' => $body['type'] !== MessageTemplateTypes::EMAIL()->getValue(),
                'fields' => $assertCollection]
        );
        return $validator->validate($body, $constraints);
    }

    /**
     * @param array $body
     * @param TranslatorInterface $translator
     * @return ConstraintViolationListInterface
     */
    private function validateUpdateBody(array $body, TranslatorInterface $translator): ConstraintViolationListInterface
    {
        $validator = Validation::createValidatorBuilder()->setTranslator($translator)->setTranslationDomain('validators')->getValidator();
        $phoneNumberAsserts = [new Assert\Type('string'), new Assert\Length(['min' => 10, 'max' => 13]), new Assert\Regex(['pattern' => Tools::MOBILEPHONE_PATTERN])];
        $assertCollection = [
            'title' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 255])]),
            'type' => new Assert\Required([new Assert\Choice(MessageTemplateTypes::valuesToString())]),
            'subject' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 255])]),
            'body' => new Assert\Optional([new Assert\Type('string')]),
            'entityClass' => new Assert\Optional([new Assert\Choice([RegistrationFolder::CLASSNAME, CertificationFolder::CLASSNAME, Proposal::CLASSNAME, CertificationPartner::CLASSNAME, CertificationFolderSurvey::CLASSNAME])]),
            'events' => new Assert\Optional([new Assert\NotNull(), new Assert\Type('array'), new Assert\All([new Assert\NotBlank(), new Assert\Type('string')])]),
            'tags' => new Assert\Optional(new Assert\Type('array')),
            'certifInfos' => new Assert\Optional(new Assert\Type('array')),
            'delay' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 20])]),
            'enforceConditions' => new Assert\Required([new Assert\NotNull(), new Assert\Type('boolean')]),
            'qualiopiIndicators' => new Assert\Optional([
                new Assert\Type('array'),
                new Assert\All([
                    new Assert\Type('integer'),
                    new Assert\Range(['min' => 1, 'max' => 32])
                ])
            ]),
            'allowResend' => new Assert\Required([new Assert\NotNull(), new Assert\Type('boolean')])
        ];
        if ($body['type'] === MessageTemplateTypes::EMAIL()->getValue()) {
            $assertCollection['sendAs'] = new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 255])]);
            $assertCollection['replyTo'] = new Assert\Optional([new Assert\Type('array'), new Assert\All(new Assert\Email())]);
            $assertCollection['cc'] = new Assert\Optional([new Assert\Type('array'), new Assert\All(new Assert\Email())]);
            $assertCollection['cci'] = new Assert\Optional([new Assert\Type('array'), new Assert\All(new Assert\Email())]);
            $assertCollection['to'] = new Assert\Optional([new Assert\Type('array'), new Assert\All(new Assert\Email())]);
        } else {
            $assertCollection['replyTo'] = new Assert\Optional([new Assert\Type('array'), new Assert\All($phoneNumberAsserts)]);
            $assertCollection['to'] = new Assert\Optional([new Assert\Type('array'), new Assert\All($phoneNumberAsserts)]);
        }
        $constraints = new Assert\Collection([
                'allowExtraFields' => $body['type'] !== MessageTemplateTypes::EMAIL()->getValue(),
                'fields' => $assertCollection]
        );
        return $validator->validate($body, $constraints);
    }

    /**
     * @param array $certifInfos
     * @param CertificationService $certificationService
     * @return array
     */
    private function getCertifications(array $certifInfos, CertificationService $certificationService): array
    {
        $certifications = [];
        foreach ($certifInfos as $certifInfo) {
            $certification = $certificationService->getByCertifInfo($certifInfo);
            if (!$certification) {
                throw new WedofNotFoundHttpException("La certification associée au certifInfo " . $certifInfo . " n'a pas été trouvée.");
            } else if (!$this->isGranted(CertificationFolderVoter::VIEW, $certification)) {
                throw new WedofBadRequestHttpException("Vous n'êtes pas autorisé à accéder à cette certification.");
            }
            $certifications[] = $certification;
        }
        return $certifications;
    }

    /**
     * @param array $body
     * @param MessageService $messageService
     * @return array
     * @throws ContainerExceptionInterface
     * @throws LoaderError
     * @throws NotFoundExceptionInterface
     * @throws NotSupported
     * @throws SyntaxError
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws \Doctrine\DBAL\Exception
     */
    private function getSampleValuesForValidation(array $body, MessageService $messageService): array
    {
        $computedBody = $body;
        $computableProperties = ['to', 'replyTo', 'cc', 'cci'];
        foreach ($computableProperties as $computableProperty) {
            if (isset($computedBody[$computableProperty])) {
                $computedBody[$computableProperty] = $messageService->getComputedArray($computedBody[$computableProperty], null, null, false, true);
            }
        }
        return $computedBody;
    }

    /**
     * @param Organism $organism
     * @param string $entityClass
     */
    private function checkOrganismSubscriptionForEntityClass(Organism $organism, string $entityClass): void
    {
        $subscription = $organism->getSubscription();
        $allowedSubscriptionTypes = MessageTemplatesWedofApplication::getAllowedSubscriptionTypes($subscription);
        $isTrainingType = in_array($subscription->getTrainingType(), $allowedSubscriptionTypes['training']);
        $isCertifierType = in_array($subscription->getCertifierType(), $allowedSubscriptionTypes['certifier']);
        if ($entityClass === Proposal::CLASSNAME && !in_array($subscription->getTrainingType(), [SubscriptionTrainingTypes::PREMIUM()->getValue(), SubscriptionTrainingTypes::TRIAL()->getValue()])) {
            throw new WedofBadRequestHttpException("Erreur, votre abonnement ne permet pas la création d'un message sur une proposition");
        } else if ($isTrainingType && !$isCertifierType && $entityClass === CertificationFolder::CLASSNAME) {
            throw new WedofBadRequestHttpException("Erreur, votre abonnement ne permet pas la création d'un message sur un dossier de certification");
        } else if (!$isTrainingType && $isCertifierType && $entityClass === RegistrationFolder::CLASSNAME) {
            throw new WedofBadRequestHttpException("Erreur, votre abonnement ne permet pas la création d'un message sur un dossier de formation");
        } else if (!$isCertifierType && $entityClass === CertificationPartner::CLASSNAME) {
            throw new WedofBadRequestHttpException("Erreur, votre abonnement ne permet pas la création d'un message sur un partenariat de certification");
        } else if (!$isCertifierType && $entityClass === CertificationFolderSurvey::CLASSNAME) {
            throw new WedofBadRequestHttpException("Erreur, votre abonnement ne permet pas la création d'un message sur une enquête");
        }
    }
}
