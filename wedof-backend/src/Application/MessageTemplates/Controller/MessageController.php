<?php

namespace App\Application\MessageTemplates\Controller;

use App\Application\MessageTemplates\Entity\Message;
use App\Application\MessageTemplates\Entity\MessageTemplate;
use App\Application\MessageTemplates\MessageTemplatesWedofApplication;
use App\Application\MessageTemplates\Repository\MessageRepository;
use App\Application\MessageTemplates\Repository\MessageTemplateRepository;
use App\Application\MessageTemplates\Security\MessageVoter;
use App\Application\MessageTemplates\Service\MessageService;
use App\Application\MessageTemplates\Service\MessageTemplateService;
use App\Entity\CertificationFolder;
use App\Entity\CertificationFolderSurvey;
use App\Entity\CertificationPartner;
use App\Entity\Proposal;
use App\Entity\RegistrationFolder;
use App\Entity\User;
use App\Exception\WedofAccessDeniedHttpException;
use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofNotFoundHttpException;
use App\Exception\WedofSubscriptionException;
use App\Library\utils\enums\MessageStates;
use App\Library\utils\enums\MessageTemplateStates;
use App\Library\utils\enums\MessageTemplateTypes;
use App\Library\utils\Tools;
use App\Security\Voter\CertificationFolderSurveyVoter;
use App\Security\Voter\CertificationFolderVoter;
use App\Security\Voter\CertificationPartnerVoter;
use App\Security\Voter\ProposalVoter;
use App\Security\Voter\RegistrationFolderVoter;
use App\Service\AccessService;
use App\Service\CertificationFolderService;
use App\Service\CertificationFolderSurveyService;
use App\Service\CertificationPartnerService;
use App\Service\ProposalService;
use App\Service\RegistrationFolderService;
use Doctrine\DBAL\Driver\Exception;
use Doctrine\ORM\Exception\NotSupported;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcherInterface;
use FOS\RestBundle\View\View;
use Knp\Component\Pager\PaginatorInterface;
use Nelmio\ApiDocBundle\Annotation as ApiDoc;
use OpenApi\Annotations as OA;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use ReflectionException;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validation;
use Twig\Error\LoaderError;
use Twig\Error\SyntaxError;

/**
 * Class MessageController
 * @package App\Controller\api
 *
 * @OA\Tag(name="Message")
 * @ApiDoc\Security(name="accessCode")
 */
class MessageController extends AbstractFOSRestController
{
    /**
     * @Rest\Get("/api/messages/{entityClass}/{entityId}", requirements={"entityClass"="RegistrationFolder|CertificationFolder|Proposal|CertificationPartner|CertificationFolderSurvey"})
     * @Rest\QueryParam(name="limit", requirements="\d+", default="25", description="Nombre d'éléments retourné par requête - par défaut 25.")
     * @Rest\QueryParam(name="page", requirements="\d+", default="1", description="Numéro de page de la requête - par défaut la première.")
     * @Rest\View(StatusCode = 200)
     *
     * @param string $entityClass
     * @param $entityId
     * @param ParamFetcherInterface $paramFetcher
     * @param PaginatorInterface $paginator
     * @param MessageService $messageService
     * @param ContainerInterface $container
     * @return Response
     */
    public function listByEntity(string $entityClass, $entityId, ParamFetcherInterface $paramFetcher, PaginatorInterface $paginator, MessageService $messageService, ContainerInterface $container): Response
    {
        /** @var RegistrationFolderService|CertificationFolderService|ProposalService|CertificationPartnerService|CertificationFolderSurveyService $entityService */
        $entityService = $container->get('App\Service\\' . $entityClass . 'Service');
        $entity = $entityService->getByEntityId($entityId);
        if (!$entity) {
            throw new WedofNotFoundHttpException("L'entité' " . $entityId . " n'a pas été trouvée.");
        }
        if ($entity instanceof RegistrationFolder) {
            $attribute = RegistrationFolderVoter::OWNER_VIEW;
        } elseif ($entity instanceof CertificationFolder) {
            $attribute = CertificationFolderVoter::VIEW;
            $entityId = $entity->getId(); // hack because entityId is based on "id" on BDD, we cannot list using externalId as entityId otherwise we will need to do a big migration
        } else if ($entity instanceof Proposal) {
            $attribute = ProposalVoter::VIEW;
        } else if ($entity instanceof CertificationFolderSurvey) {
            $attribute = CertificationFolderSurveyVoter::VIEW;
        } else {
            $attribute = CertificationPartnerVoter::VIEW;
        }
        if (!$this->isGranted($attribute, $entity)) {
            throw new WedofAccessDeniedHttpException("Erreur, vous n'êtes pas autorisé à lister les messages pour l'entité " . $entityId);
        }

        $parameters = $paramFetcher->all(true);
        $data = $paginator->paginate($messageService->listByEntityReturnQueryBuilder($entityClass, $entityId), intval($parameters['page']), intval($parameters['limit']));
        $view = $this->view($data->getItems(), 200);
        $view->setHeader("x-total-count", $data->getTotalItemCount());
        $view->setHeader("x-current-page", $data->getCurrentPageNumber());
        $view->setHeader("x-item-per-page", $data->getItemNumberPerPage());
        return $this->handleView($view);
    }

    /**
     * @Rest\Post("/api/messages")
     * @IsGranted("ROLE_USER", message="not allowed")
     * @Rest\View(StatusCode = 201)
     *
     * @ApiDoc\Areas({"messages", "default"})
     * @OA\Post (
     *     summary="Création d'un message.",
     *     description="Permet de créer et d'envoyer un message."
     * )
     * @OA\Response(
     *     response=201,
     *     description="Un json contenant les informations du message créé et envoyé",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/Message")
     *     )
     * )
     *
     * @OA\RequestBody(
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/MessageCreateBody")
     *     )
     * )
     *
     * @param Request $request
     * @param AccessService $accessService
     * @param MessageTemplateRepository $messageTemplateRepository
     * @param MessageService $messageService
     * @param ContainerInterface $container
     * @return Message|View
     * @throws ContainerExceptionInterface
     * @throws Exception
     * @throws LoaderError
     * @throws NonUniqueResultException
     * @throws NotFoundExceptionInterface
     * @throws NotSupported
     * @throws ORMException
     * @throws ReflectionException
     * @throws SyntaxError
     * @throws WedofSubscriptionException
     * @throws \Doctrine\DBAL\Exception
     * @throws \Doctrine\ORM\ORMException
     */
    public function create(Request $request, AccessService $accessService, MessageTemplateRepository $messageTemplateRepository, MessageService $messageService, ContainerInterface $container)
    {
        /* @var $user User */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();
        if (!$accessService->isApplicationAllowedAndEnabled(MessageTemplatesWedofApplication::getAppId(), $organism)) {
            throw new WedofAccessDeniedHttpException("Erreur, l'Application d'envoi de message n'est pas activée / autorisée");
        }
        $body = json_decode($request->getContent(), true);
        $sampleValues = $this->getSampleValuesForValidation($body, $messageService);

        $violations = $this->validateCreateBody($sampleValues);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }

        if ($organism->getSiret() !== $body['siret']) {
            throw new WedofBadRequestHttpException("Erreur, l'organisme ne correspond pas avec le siret donné");
        }
        /* @var $messageTemplate MessageTemplate */
        $messageTemplate = $messageTemplateRepository->findOneBy([
            'organism' => $organism,
            'state' => [MessageTemplateStates::HIDDEN()->getValue()],
            'type' => [$body['type']]
        ]);
        if ($messageTemplate) {
            if ($body['type'] === MessageTemplateTypes::SMS()->getValue() && !$organism->getSubscription()->isAllowPaidUsage()) {
                throw new WedofSubscriptionException("Votre abonnement ne vous permet pas de créer des messages sur les SMS.");
            }
            $entityService = $container->get('App\Service\\' . $body['entityClass'] . 'Service');
            $entity = $entityService->getByEntityId($body['entityId']);
            if (!$entity) {
                throw new WedofNotFoundHttpException("Erreur, l'entité " . $body['entityId'] . " n'a pas été trouvée.");
            }
            $this->checkEditAccessOnEntity($entity, $body['entityId']);
            return $messageService->createOrUpdate($messageTemplate, $entity, '', null, null, true, $body);
        } else {
            // shouldn't reach this part as it is automatically created but "just in case" so we can detect if there is an error
            throw new WedofBadRequestHttpException("Erreur, le modèle de message n'a pas été trouvé");
        }
    }

    /**
     * THIS ENDPOINT IS NOT DOCUMENTED / ONLY FOR DEV - TESTS PURPOSE
     * @Rest\Post("/api/messages/{entityClass}/{entityId}/{messageTemplateId}", requirements={"entityClass"="RegistrationFolder|CertificationFolder|Proposal|CertificationPartner|CertificationFolderSurvey"})
     * @Rest\View (StatusCode = 201)
     *
     * @param string $entityClass
     * @param $entityId
     * @param int $messageTemplateId
     * @param MessageTemplateService $messageTemplateService
     * @param MessageService $messageService
     * @param AccessService $accessService
     * @param ContainerInterface $container
     * @return Response
     * @throws ContainerExceptionInterface
     * @throws LoaderError
     * @throws NonUniqueResultException
     * @throws NotFoundExceptionInterface
     * @throws ORMException
     * @throws ReflectionException
     * @throws SyntaxError
     * @throws WedofSubscriptionException
     * @throws Exception
     * @throws \Doctrine\DBAL\Exception
     * @throws NotSupported
     * @throws \Doctrine\ORM\ORMException
     */
    public function createForDev(string $entityClass, $entityId, int $messageTemplateId, MessageTemplateService $messageTemplateService, MessageService $messageService, AccessService $accessService, ContainerInterface $container): Response
    {
        /* @var $user User */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();
        if (!$accessService->isApplicationAllowedAndEnabled(MessageTemplatesWedofApplication::getAppId(), $organism)) {
            throw new WedofAccessDeniedHttpException("Erreur, l'Application d'envoi de message n'est pas activée / autorisée");
        }
        $messageTemplate = $messageTemplateService->getById($messageTemplateId);
        if (!$messageTemplate) {
            throw new WedofNotFoundHttpException("Erreur, le modèle " . $messageTemplateId . " n'a pas été trouvé.");
        }
        if ($messageTemplate->getOrganism()->getId() !== $organism->getId()) { // TODO define voter
            throw new WedofAccessDeniedHttpException("Erreur, vous n'êtes pas autorisé à utiliser ce modèle de message");
        }
        if ($messageTemplate->getState() !== MessageTemplateStates::ACTIVE()->getValue()) {
            throw new WedofBadRequestHttpException("Erreur, le modèle de message fourni n'est pas actif");
        }
        if ($messageTemplate->getEntityClass() !== $entityClass) {
            throw new WedofBadRequestHttpException("Erreur, l'entité renseignée ne correspond pas à celle du modèle de message");
        }
        /** @var RegistrationFolderService|CertificationFolderService|ProposalService|CertificationPartnerService|CertificationFolderSurveyService $entityService */
        $entityService = $container->get('App\Service\\' . $entityClass . 'Service');
        $entity = $entityService->getByEntityId($entityId);
        if (!$entity) {
            throw new WedofNotFoundHttpException("Erreur, l'entité " . $entityId . " n'a pas été trouvée.");
        }
        $this->checkEditAccessOnEntity($entity, $entityId);
        if ($messageTemplate->getType() === MessageTemplateTypes::SMS()->getValue() && !$organism->getSubscription()->isAllowPaidUsage()) {
            throw new WedofSubscriptionException("Votre abonnement ne vous permet pas de créer des messages sur les SMS.");
        }
        // TODO check right state, certification, tags ? Only state ? State is hard because event can be something else than an event Or we limit that to only certificationTemplates with event "manual" ?
        $message = $messageService->createOrUpdate($messageTemplate, $entity, $messageTemplate->getEvents()[0], null, null, true); // Force no delay
        $view = $this->view($message, 201);
        return $this->handleView($view);
    }

    /**
     * @Rest\Post("/api/messages/{id}/resend")
     * @IsGranted(MessageVoter::EDIT, subject="message", message="not allowed")
     * @Rest\View(StatusCode = 200)
     * @param Message $message
     * @param MessageService $messageService
     * @param AccessService $accessService
     * @param ContainerInterface $container
     * @return Message
     * @throws ContainerExceptionInterface
     * @throws Exception
     * @throws LoaderError
     * @throws NonUniqueResultException
     * @throws NotFoundExceptionInterface
     * @throws NotSupported
     * @throws ORMException
     * @throws ReflectionException
     * @throws SyntaxError
     * @throws \Doctrine\DBAL\Exception
     * @throws \Doctrine\ORM\ORMException
     */
    public function forceResend(Message $message, MessageService $messageService, AccessService $accessService, ContainerInterface $container): Message
    {
        /* @var $user User */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();
        if (!$accessService->isApplicationAllowedAndEnabled(MessageTemplatesWedofApplication::getAppId(), $organism)) {
            throw new WedofAccessDeniedHttpException("Erreur, l'Application d'envoi de message n'est pas activée / autorisée");
        }

        if (!in_array($message->getState(), [MessageStates::SCHEDULED()->getValue(), MessageStates::SENT()->getValue(), MessageStates::NOT_SENT_ENFORCED_CONDITIONS()->getValue(), MessageStates::NOT_SENT_MISSING_DATA()->getValue(), MessageStates::FAILED()->getValue()])) {
            throw new WedofBadRequestHttpException("Erreur, l'état du message ne vous permet pas de le renvoyer.");
        }
        $entityClass = $message->getEntityClass();
        $entityId = $message->getEntityId();
        /** @var RegistrationFolderService|CertificationFolderService|ProposalService|CertificationPartnerService|CertificationFolderSurveyService $entityService */
        $entityService = $container->get('App\Service\\' . $entityClass . 'Service');
        $entity = $entityService->getByEntityId($entityId);
        if (!$entity) {
            throw new WedofNotFoundHttpException("Erreur, l'entité " . $entityId . " n'a pas été trouvée.");
        }
        $this->checkEditAccessOnEntity($entity, $entityId);
        if ($message->getMessageTemplate()->getState() !== MessageTemplateStates::ACTIVE()->getValue()) {
            throw new WedofBadRequestHttpException("Erreur, le modèle de message n'est pas actif");
        }
        return $messageService->forceResend($message, $entity, $user);
    }

    /**
     * @Rest\Delete("/api/messages/{id}")
     * @IsGranted(MessageVoter::EDIT, subject="message", message="not allowed")
     * @Rest\View(StatusCode = 204)
     *
     * @param Message $message
     * @param MessageService $messageService
     * @return void
     */
    public function delete(Message $message, MessageService $messageService): void
    {
        if ($message->getState() !== MessageStates::SCHEDULED()->getValue()) {
            throw new WedofBadRequestHttpException("Erreur, vous ne pouvez pas supprimer un message dans l'état " . MessageStates::toFrString($message->getState()));
        }
        $messageService->delete($message);
    }

    /**
     * @Route("/app/public/message/html/{uuid}",requirements={"uuid"="\w{16}"}, methods={"GET","HEAD"})
     * @param string $uuid
     * @param MessageRepository $messageRepository
     * @return Response
     */
    public function showHtml(string $uuid, MessageRepository $messageRepository): Response
    {
        $message = $messageRepository->findOneBy(['uuid' => $uuid]);
        if (!$message) {
            return new Response("Erreur, ce message n'existe pas.", 404);
        }
        return $this->render('message/index.html.twig', [
            'subject' => $message->getSubject(),
            'body' => $message->getBody()
        ]);
    }

    //----------------
    // METHODES PRIVES
    //----------------

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateCreateBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $phoneNumberAsserts = [new Assert\Type('string'), new Assert\Length(['min' => 10, 'max' => 13]), new Assert\Regex(['pattern' => Tools::MOBILEPHONE_PATTERN])];

        $assertCollection = [
            'entityClass' => new Assert\Required([new Assert\Choice([RegistrationFolder::CLASSNAME, CertificationFolder::CLASSNAME, Proposal::CLASSNAME, CertificationPartner::CLASSNAME])]),
            'type' => new Assert\Required([new Assert\Choice(MessageTemplateTypes::valuesToString())]),
            'siret' => new Assert\Required([new Assert\Type('string')]),
            'entityId' => new Assert\Required([new Assert\Type('string')]),
            'body' => new Assert\Required([new Assert\Type('string')]),
        ];
        if ($body['type'] === MessageTemplateTypes::EMAIL()->getValue()) {
            $assertCollection['sendAs'] = new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 255])]);
            $assertCollection['replyTo'] = new Assert\Required([new Assert\Type('array'), new Assert\All(new Assert\Email())]);
            $assertCollection['subject'] = new Assert\Required([new Assert\Type('string'), new Assert\Length(['max' => 255])]);
            $assertCollection['cc'] = new Assert\Optional([new Assert\Type('array'), new Assert\All(new Assert\Email())]);
            $assertCollection['cci'] = new Assert\Optional([new Assert\Type('array'), new Assert\All(new Assert\Email())]);
            $assertCollection['to'] = new Assert\Required([new Assert\Type('array'), new Assert\All(new Assert\Email())]);
        } else {
            $assertCollection['to'] = new Assert\Required([new Assert\Type('array'), new Assert\All($phoneNumberAsserts)]);
        }
        $constraints = new Assert\Collection([
                'allowExtraFields' => $body['type'] !== MessageTemplateTypes::EMAIL()->getValue(),
                'fields' => $assertCollection]
        );
        return $validator->validate($body, $constraints);
    }

    /**
     * @param array $body
     * @param MessageService $messageService
     * @return array
     * @throws ContainerExceptionInterface
     * @throws LoaderError
     * @throws NotFoundExceptionInterface
     * @throws NotSupported
     * @throws SyntaxError
     * @throws Exception
     * @throws \Doctrine\DBAL\Exception
     */
    private function getSampleValuesForValidation(array $body, MessageService $messageService): array
    {
        $computedBody = $body;
        $computableProperties = ['to', 'replyTo', 'cc', 'cci'];
        foreach ($computableProperties as $computableProperty) {
            if (isset($computedBody[$computableProperty])) {
                $computedBody[$computableProperty] = $messageService->getComputedArray($computedBody[$computableProperty], null, null, false, true);
            }
        }
        return $computedBody;
    }

    /**
     * @param $entity
     * @param $entityId
     */
    private function checkEditAccessOnEntity($entity, $entityId)
    {
        if ($entity instanceof RegistrationFolder) {
            $attribute = RegistrationFolderVoter::EDIT;
        } else if ($entity instanceof CertificationFolder) {
            $attribute = CertificationFolderVoter::EDIT;
            $entityId = $entity->getId(); // hack because entityId is based on "id" on BDD, we cannot list using externalId as entityId otherwise we will need to do a big migration
        } else if ($entity instanceof Proposal) {
            $attribute = ProposalVoter::EDIT;
        } else if ($entity instanceof CertificationFolderSurvey) {
            $attribute = CertificationFolderSurveyVoter::EDIT;
        } else {
            $attribute = CertificationPartnerVoter::EDIT;
        }
        if (!$this->isGranted($attribute, $entity)) {
            throw new WedofAccessDeniedHttpException("Erreur, vous n'êtes pas autorisé à envoyer un message pour cette entité " . $entityId);
        }
    }


}