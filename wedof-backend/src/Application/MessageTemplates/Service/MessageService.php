<?php

namespace App\Application\MessageTemplates\Service;

use App\Application\MessageTemplates\Entity\Message;
use App\Application\MessageTemplates\Entity\MessageTemplate;
use App\Application\MessageTemplates\Repository\MessageRepository;
use App\Entity\CertificationFolder;
use App\Entity\CertificationFolderFile;
use App\Entity\CertificationFolderSurvey;
use App\Entity\CertificationPartner;
use App\Entity\CertificationPartnerAudit;
use App\Entity\CertificationPartnerFile;
use App\Entity\Invoice;
use App\Entity\Organism;
use App\Entity\Proposal;
use App\Entity\RegistrationFolder;
use App\Entity\RegistrationFolderFile;
use App\Entity\Subscription;
use App\Entity\User;
use App\Event\CertificationFolder\CertificationFolderEvents;
use App\Event\CertificationFolderSurvey\CertificationFolderSurveyEvents;
use App\Event\CertificationPartner\CertificationPartnerEvents;
use App\Event\Proposal\ProposalEvents;
use App\Event\RegistrationFolder\RegistrationFolderEvents;
use App\Library\Notifier\GatewayApiTransport;
use App\Library\utils\Dictionary;
use App\Library\utils\enums\ActivityTypes;
use App\Library\utils\enums\MessageStates;
use App\Library\utils\enums\MessageTemplateStates;
use App\Library\utils\enums\MessageTemplateTypes;
use App\Library\utils\Tools;
use App\Message\MessageToSend;
use App\Service\ActivityService;
use App\Service\CertificationFolderFileService;
use App\Service\CertificationFolderService;
use App\Service\CertificationFolderSurveyService;
use App\Service\CertificationPartnerAuditService;
use App\Service\CertificationPartnerFileService;
use App\Service\CertificationPartnerService;
use App\Service\InvoiceService;
use App\Service\MailerService;
use App\Service\ProposalService;
use App\Service\RegistrationFolderFileService;
use App\Service\RegistrationFolderService;
use App\Service\ShortenedUrlService;
use DateTime;
use Doctrine\ORM\Exception\NotSupported;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\QueryBuilder;
use Exception;
use HTMLPurifier;
use HTMLPurifier_Config;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\ContainerInterface;
use Psr\Container\NotFoundExceptionInterface;
use ReflectionException;
use ReflectionProperty;
use Symfony\Bridge\Twig\Mime\TemplatedEmail;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Mime\Email;
use Symfony\Component\Notifier\Message\SmsMessage;
use Symfony\Component\Notifier\Texter;
use Symfony\Component\Notifier\TexterInterface;
use Symfony\Component\Notifier\Transport\TransportInterface;
use Symfony\Component\Notifier\Transport\Transports;
use Throwable;
use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\SyntaxError;

class MessageService
{
    private MessageRepository $messageRepository;
    private MessageTemplateService $messageTemplateService;
    private Environment $twig;
    private MessageBusInterface $messageBus;
    private ContainerInterface $container;
    private ActivityService $activityService;
    private MailerService $mailerService;
    private TexterInterface $texter;

    private ShortenedUrlService $shortenedUrlService;

    public function __construct(MessageTemplateService $messageTemplateService,
                                MessageRepository      $messageRepository,
                                ContainerInterface     $container,
                                Environment            $twig,
                                MessageBusInterface    $messageBus,
                                ActivityService        $activityService,
                                MailerService          $mailerService,
                                TexterInterface        $texterInterface,
                                ShortenedUrlService    $shortenedUrlService)
    {
        $this->messageTemplateService = $messageTemplateService;
        $this->messageRepository = $messageRepository;
        $this->twig = $twig;
        $this->messageBus = $messageBus;
        $this->container = $container;
        $this->activityService = $activityService;
        $this->mailerService = $mailerService;
        $this->texter = $texterInterface;
        $this->shortenedUrlService = $shortenedUrlService;
    }

    //-------------------
    // METHODES PUBLIQUES
    //-------------------
    /**
     * @param int $id
     * @return Message|null
     */
    public function getById(int $id): ?Message
    {
        return $this->messageRepository->find($id);
    }

    /**
     * @param string $entityClass
     * @param int $id
     * @return array|null
     */
    public function listByEntityId(string $entityClass, int $id): ?array
    {
        return $this->messageRepository->findBy(['entityClass' => $entityClass, 'entityId' => $id]);
    }

    /**
     * @param MessageTemplate $messageTemplate
     * @param $entity
     * @param string $event
     * @param array|null $context
     * @param Message|null $message
     * @param bool $forceScheduledNow
     * @param array|null $forceBody
     * @return Message
     * @throws ContainerExceptionInterface
     * @throws LoaderError
     * @throws NotFoundExceptionInterface
     * @throws NotSupported
     * @throws SyntaxError
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws \Doctrine\DBAL\Exception
     */
    public function createOrUpdate(MessageTemplate $messageTemplate, $entity, string $event, array $context = null, Message $message = null, bool $forceScheduledNow = false, array $forceBody = null): Message
    {
        $now = new DateTime('now');
        $scheduledTime = clone $now;
        $scheduledTime = (!$forceScheduledNow && $messageTemplate->getDelay()) ? $scheduledTime->modify($messageTemplate->getDelay()) : $scheduledTime;
        $message = $message ?? new Message();
        $message->setUuid(Tools::generateRandomString(16, false));
        $message->setState($scheduledTime == $now ? MessageStates::TO_SEND() : MessageStates::SCHEDULED()->getValue());
        $message->setMessageTemplate($messageTemplate);
        $mainOrganismFromEntity = $messageTemplate->getOrganism();

        $messageBody = $forceBody ? $forceBody['body'] : $messageTemplate->getBody();
        if ($messageTemplate->getType() == MessageTemplateTypes::SMS()->getValue()) {
            $computedBody = $this->getComputedContent($messageBody, $entity, $context, false, false, $mainOrganismFromEntity);
            $computedBody = $this->shortenUrlSmsBody($computedBody, $messageTemplate);
            $message->setBody($computedBody);
        } else {
            $messageCc = $forceBody ? $forceBody['cc'] : $messageTemplate->getCc();
            $messageCci = $forceBody ? $forceBody['cci'] : $messageTemplate->getCci();
            $messageReplyTo = $forceBody ? $forceBody['replyTo'] : $messageTemplate->getReplyTo();
            $messageSubject = $forceBody ? $forceBody['subject'] : $messageTemplate->getSubject();
            $message->setCc($this->getComputedArray($messageCc, $entity, $context, false, false, $mainOrganismFromEntity));
            $message->setCci($this->getComputedArray($messageCci, $entity, $context, false, false, $mainOrganismFromEntity));
            $message->setReplyTo($this->getComputedArray($messageReplyTo, $entity, $context, false, false, $mainOrganismFromEntity));
            $message->setSubject($this->getComputedContent($messageSubject, $entity, $context, false, false, $mainOrganismFromEntity));
            $message->setBody($this->getComputedContent($messageBody, $entity, $context, true, false, $mainOrganismFromEntity));
        }
        $messageTo = $forceBody ? $forceBody['to'] : $messageTemplate->getTo();
        $message->setTo($this->getComputedArray($messageTo, $entity, $context, false, false, $mainOrganismFromEntity));
        $message->setScheduledAt($scheduledTime);

        $message->setEntityClass($entity ? $entity::CLASSNAME : Message::NO_ENTITY);
        $message->setEntityId($entity ? $entity->getEntityId() : Message::NO_ENTITY);

        $message->setType($messageTemplate->getType());
        $message->setEvent($event);
        $message->setContext($context);
        return $this->save($message);
    }

    /**
     * @param array $messageTemplates
     * @param $entity
     * @param Organism $organism
     * @param string $eventName
     * @param array|null $context
     * @param Message|null $existingMessage
     * @param bool $forceScheduleNow
     * @return array
     * @throws ContainerExceptionInterface
     * @throws LoaderError
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws NotFoundExceptionInterface
     * @throws NotSupported
     * @throws SyntaxError
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws \Doctrine\DBAL\Exception
     */
    public function createOrUpdateFromTemplates(array $messageTemplates, $entity, Organism $organism, string $eventName, ?array $context, ?Message $existingMessage, bool $forceScheduleNow): array
    {
        $messages = [];
        foreach ($messageTemplates as $messageTemplate) {
            $nbMessagesFromMessageTemplate = $this->countByMessageTemplateAndEntity($messageTemplate, $entity::CLASSNAME, $entity->getEntityId());
            if (($existingMessage || $messageTemplate->getAllowResend() || $nbMessagesFromMessageTemplate === 0) // $existingMessage => MAJ ou messagesFromMessageTemplate => création
                && ($messageTemplate->getType() !== MessageTemplateTypes::SMS()->getValue() || $organism->getSubscription()->isAllowPaidUsage())) {
                $messages[] = $this->createOrUpdate($messageTemplate, $entity, $eventName, $context, $existingMessage, $forceScheduleNow);
            }
        }
        return $messages;
    }

    /**
     * @param Message $message
     * @param MessageTemplate $messageTemplate
     * @param $entity
     * @param User|null $user
     * @return Message
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function markAsSent(Message $message, MessageTemplate $messageTemplate, $entity, ?User $user): Message
    {
        $message->setState(MessageStates::SENT()->getValue());
        $message->setSentAt(new DateTime('now'));
        $message = $this->save($message);
        if ($messageTemplate->getState() === MessageTemplateStates::HIDDEN()->getValue()) {
            $title = "Un " . $message->getType() . " a été envoyé";
        } else {
            $title = "Un " . $message->getType() . " a été envoyé à partir du modèle de message : " . $messageTemplate->getTitle();
        }
        if ($messageTemplate->getSendAs()) {
            $title .= ' par ' . $messageTemplate->getSendAs();
        }
        if ($entity) {
            $this->activityService->create([
                'title' => $title,
                'type' => ActivityTypes::from($message->getType()),
                'eventTime' => new DateTime(),
                'qualiopiIndicators' => $messageTemplate->getQualiopiIndicators(),
                'origin' => 'Wedof'
            ], $user, $entity);
        }
        return $message;
    }

    /**
     * @param Message $message
     * @param MessageTemplate $messageTemplate
     * @param $entity
     * @return Message
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function markAsFailed(Message $message, MessageTemplate $messageTemplate, $entity): Message
    {
        $message->setState(MessageStates::FAILED()->getValue());
        $message->setSendFailedCount($message->getSendFailedCount() + 1);
        $message = $this->save($message);
        if ($entity) {
            $this->activityService->create([
                'title' => "Erreur sur l'envoi d'un " . $message->getType() . " à partir du modèle de message : " . $messageTemplate->getTitle(),
                'type' => ActivityTypes::from($message->getType()),
                'eventTime' => new DateTime(),
                'origin' => 'Wedof'
            ], null, $entity);
        }
        return $message;
    }

    /**
     * @param Message $message
     * @param string $reason
     * @param $entity
     * @return Message
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function markAsNotSentUnauthorized(Message $message, string $reason, $entity): Message
    {
        $message->setState(MessageStates::NOT_SENT_UNAUTHORIZED()->getValue());
        $message = $this->save($message);
        if ($entity) {
            $this->activityService->create([
                'title' => "Le message '" . $message->getMessageTemplate()->getTitle() . "' n'a pas pu être envoyé car " . $reason,
                'type' => ActivityTypes::from($message->getType()),
                'eventTime' => new DateTime(),
                'origin' => 'Wedof'
            ], null, $entity);
        }
        return $message;
    }

    /**
     * @param Message $message
     * @param $entity
     * @return Message
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function markAsNotSentEnforcedConditions(Message $message, $entity): Message
    {
        $message->setState(MessageStates::NOT_SENT_ENFORCED_CONDITIONS()->getValue());
        $message = $this->save($message);
        $this->activityService->create([
            'title' => "Le message '" . $message->getMessageTemplate()->getTitle() . "' n'a pas pu être envoyé car les conditions d'envoi ne sont pas respectées.",
            'type' => ActivityTypes::from($message->getType()),
            'eventTime' => new DateTime(),
            'origin' => 'Wedof'
        ], null, $entity);
        return $message;
    }

    /**
     * @param Message $message
     * @param $entity
     * @return Message
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function markAsNotSentMissingData(Message $message, $entity): Message
    {
        $message->setState(MessageStates::NOT_SENT_MISSING_DATA()->getValue());
        $message = $this->save($message);
        if ($entity) {
            $this->activityService->create([
                'title' => "Le message '" . $message->getMessageTemplate()->getTitle() . "' n'a pas pu être envoyé car des informations sont manquantes (adresse du destinataire).",
                'type' => ActivityTypes::from($message->getType()),
                'eventTime' => new DateTime(),
                'origin' => 'Wedof'
            ], null, $entity);
        }
        return $message;
    }

    /**
     * @return void
     * @throws LoaderError
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws ReflectionException
     * @throws SyntaxError
     * @throws \Doctrine\ORM\ORMException
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface|Throwable
     */
    public function sendMessages(): void
    {
        $messagesToSend = $this->messageRepository->findAllToSend(['states' => [MessageStates::TO_SEND()->getValue()], 'limit' => 10]);
        /** @var Message $message */
        foreach ($messagesToSend as $message) {
            $this->messageBus->dispatch(new MessageToSend($message->getId()));
        }

        $messagesToUpdate = $this->messageRepository->findAllToSend(['states' => [MessageStates::SCHEDULED()->getValue()], 'limit' => null]);
        /** @var Message $message */
        foreach ($messagesToUpdate as $message) {
            /** @var RegistrationFolderService|CertificationFolderService|ProposalService|CertificationPartnerService|CertificationFolderFileService|RegistrationFolderFileService|CertificationPartnerFileService|CertificationFolderSurveyService|InvoiceService|CertificationPartnerAuditService $entityService */
            $entityService = $message->getEntityClass() != Message::NO_ENTITY ? $this->container->get('App\Service\\' . $message->getEntityClass() . 'Service') : null;
            $entity = $entityService ? $entityService->getByEntityId($message->getEntityId()) : null;
            if ($entity) {
                $eventPrefix = lcfirst($message->getEntityClass());
                $context = $message->getContext();
                $eventName = Tools::startsWith($message->getEvent(), $eventPrefix) && $message->getMessageTemplate()->getEnforceConditions() ? $eventPrefix . "." . $entity->getState() : $message->getEvent();
                switch ($message->getEntityClass()) {
                    case CertificationFolder::CLASSNAME:
                        $this->messageTemplateService->createOrUpdateMessagesToSendForCertificationFolders(new CertificationFolderEvents($entity), $eventName, $context, $message, true);
                        break;
                    case RegistrationFolder::CLASSNAME:
                        $this->messageTemplateService->createOrUpdateMessagesToSendForRegistrationFolders(new RegistrationFolderEvents($entity), $eventName, $context, $message, true);
                        break;
                    case Proposal::CLASSNAME:
                        $this->messageTemplateService->createOrUpdateMessagesToSendForProposals(new ProposalEvents($entity), $eventName, $context, $message, true);
                        break;
                    case CertificationPartner::CLASSNAME:
                        $this->messageTemplateService->createOrUpdateMessagesToSendForCertificationPartners(new CertificationPartnerEvents($entity), $eventName, $context, $message, true);
                        break;
                    case CertificationFolderSurvey::CLASSNAME:
                        $this->messageTemplateService->createOrUpdateMessagesToSendForCertificationFolderSurveys(new CertificationFolderSurveyEvents($entity), $eventName, $context, $message, true);
                        break;
                }
            } else {
                //cleanup deleted entities
                $this->delete($message);
            }
        }
    }

    /**
     * @param string $templateText
     * @param RegistrationFolder|CertificationFolder|Proposal|CertificationPartner|CertificationFolderFile|RegistrationFolderFile|CertificationPartnerFile|CertificationFolderSurvey|Invoice|CertificationPartnerAudit|null $entity
     * @param array|null $rawContext
     * @param bool $convertToHTML
     * @param bool $useExampleValues
     * @param Organism|null $mainOrganismFromEntity
     * @return string
     * @throws ContainerExceptionInterface
     * @throws LoaderError
     * @throws NotFoundExceptionInterface
     * @throws NotSupported
     * @throws SyntaxError
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws \Doctrine\DBAL\Exception
     */
    public function getComputedContent(string $templateText, $entity, ?array $rawContext, bool $convertToHTML, bool $useExampleValues, Organism $mainOrganismFromEntity = null): string
    {
        /** @var RegistrationFolder|CertificationFolder|Proposal|CertificationPartner|CertificationFolderSurvey $entity */
        $document = null;
        $invoice = null;
        $audit = null;
        if ($rawContext) {
            foreach ($rawContext as $class => $id) {
                if ($class === CertificationFolderFile::CLASSNAME || $class === RegistrationFolderFile::CLASSNAME || $class === CertificationPartnerFile::CLASSNAME) {
                    /** @var CertificationFolderFileService | RegistrationFolderFileService | CertificationPartnerFileService $entityService */
                    $entityService = $this->container->get('App\Service\\' . $class . 'Service'); // Cannot be factored out cause not all contexts are classes
                    $document = $entityService->getByEntityId($id);
                } else if ($class === Invoice::CLASSNAME) {
                    /** @var InvoiceService $entityService */
                    $entityService = $this->container->get('App\Service\\' . $class . 'Service');
                    $invoice = $entityService->getById($id);
                } else if ($class === CertificationPartnerAudit::CLASSNAME) {
                    /** @var CertificationPartnerAuditService $entityService */
                    $entityService = $this->container->get('App\Service\\' . $class . 'Service');
                    $audit = $entityService->getById($id);
                }
            }
        }
        $computedContext = Dictionary::getComputedContext($entity, $document, $invoice, $audit, $this->container, $useExampleValues, 'message', $mainOrganismFromEntity);
        $parsedContent = Dictionary::parseTemplateVariables($templateText, $computedContext);
        if ($convertToHTML) {
            $parsedContent = nl2br($parsedContent);
        }
        $template = $this->twig->createTemplate($parsedContent);
        $computedContent = $template->render($computedContext);
        if (!$convertToHTML) {
            $computedContent = html_entity_decode($computedContent, ENT_QUOTES | ENT_XML1, 'UTF-8');
        }
        return $computedContent;
    }

    /**
     * @param array $array
     * @param RegistrationFolder|CertificationFolder|Proposal|CertificationPartner|CertificationFolderFile|RegistrationFolderFile|CertificationPartnerFile|CertificationFolderSurvey|Invoice|CertificationPartnerAudit|null $entity
     * @param array|null $rawContext
     * @param bool $convertToHTML
     * @param bool $useExampleValues
     * @param Organism|null $mainOrganismFromEntity
     * @return array
     * @throws ContainerExceptionInterface
     * @throws LoaderError
     * @throws NotFoundExceptionInterface
     * @throws NotSupported
     * @throws SyntaxError
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws \Doctrine\DBAL\Exception
     */
    public function getComputedArray(array $array, $entity, ?array $rawContext, bool $convertToHTML, bool $useExampleValues, Organism $mainOrganismFromEntity = null): array
    {
        $computedArray = [];
        foreach ($array as $item) {
            $computedContent = $this->getComputedContent($item, $entity, $rawContext, $convertToHTML, $useExampleValues, $mainOrganismFromEntity);
            if (!empty($computedContent)) {
                $computedArray[] = $computedContent;
            }
        }
        return $computedArray;
    }

    /**
     * @param string $entityClass
     * @param $entityId
     * @return QueryBuilder
     */
    public function listByEntityReturnQueryBuilder(string $entityClass, $entityId): QueryBuilder
    {
        return $this->messageRepository->findByEntityReturnQueryBuilder($entityClass, $entityId);
    }

    /**
     * @param MessageTemplate $messageTemplate
     * @param string $entityClass
     * @param $entityId
     * @return int
     * @throws NonUniqueResultException
     * @throws NoResultException
     */
    public function countByMessageTemplateAndEntity(MessageTemplate $messageTemplate, string $entityClass, $entityId): int
    {
        return $this->messageRepository->countByMessageTemplateAndEntity($messageTemplate, $entityClass, $entityId);
    }

    /**
     * @param Subscription $subscription
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countSmsForSubscription(Subscription $subscription): int
    {
        return $this->messageRepository->countSmsForSubscription($subscription);
    }

    /**
     * @param Message $message
     */
    public function delete(Message $message): void
    {
        $this->messageRepository->delete($message);
    }

    /**
     * @param array $emailData
     * @param int $priority
     * @throws TransportExceptionInterface
     */
    public function sendEmail(array $emailData, int $priority = Email::PRIORITY_NORMAL): void
    {
        $config = HTMLPurifier_Config::createDefault();
        $purifier = new HTMLPurifier($config);
        $purifiedBody = $purifier->purify($emailData['body']);
        $from = $emailData['from'] ?? Tools::getFromEmail(Tools::getEnvValue('SENDER_EMAIL_NAME', 'Wedof'), Tools::getEnvValue('SENDER_EMAIL'));
        $email = (new TemplatedEmail())
            ->from($from)
            ->to(...$emailData['to'] ?? [])
            ->cc(...$emailData['cc'] ?? [])
            ->bcc(...$emailData['cci'] ?? [])
            ->replyTo(...$emailData['replyTo'] ?? [])
            ->priority($priority)
            ->subject($emailData['subject'])
            ->html($purifiedBody);

        $this->mailerService->send($email, true);
    }

    /**
     * @param array $smsData
     * @throws \Symfony\Component\Notifier\Exception\TransportExceptionInterface
     * @throws Exception
     */
    public function sendSms(array $smsData): void
    {
        if (!empty($smsData['sender'])) {
            $this->overrideFromSmsVeryUgly($smsData['sender']);
        }
        foreach ($smsData['to'] as $phone) {
            if (Tools::startsWith($phone, "06") || Tools::startsWith($phone, "07") || Tools::startsWith($phone, "+336") || Tools::startsWith($phone, "+337")) {
                if (Tools::startsWith($phone, "0")) {
                    $phone = "+33" . substr($phone, 1, 9);
                }
                $sms = new SmsMessage($phone, $smsData['body']);
                try {
                    $this->texter->send($sms);
                } catch (Exception $e) {
                    if (!empty($smsData['sender'])) {
                        $this->overrideFromSmsVeryUgly("Wedof");
                    }
                    throw new Exception("Erreur sms non envoyé pour une raison inconnue");
                }
            } else {
                if (!empty($smsData['sender'])) {
                    $this->overrideFromSmsVeryUgly("Wedof");
                }
                throw new Exception("Erreur sms non envoyé mauvais format de numéro de téléphone: $phone");
            }
        }
        if (!empty($smsData['sender'])) {
            $this->overrideFromSmsVeryUgly("Wedof");
        }
    }


    /**
     * @param Message $message
     * @param $entity
     * @param User $user
     * @return Message
     * @throws ContainerExceptionInterface
     * @throws LoaderError
     * @throws NotFoundExceptionInterface
     * @throws NotSupported
     * @throws SyntaxError
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws \Doctrine\DBAL\Exception
     * @throws Exception
     */
    public function forceResend(Message $message, $entity, User $user): Message
    {
        $messageTemplate = $message->getMessageTemplate();
        $mainOrganismFromEntity = $messageTemplate->getOrganism();
        $context = $message->getContext();
        $context['userId'] = $user->getId();
        if ($message->getState() === MessageStates::SENT()->getValue()) {
            // duplication du message s'il a déjà été envoyé
            $messageToSend = $this->clone($message);
        } else {
            $messageToSend = $message;
        }
        if ($messageTemplate->getEnforceConditions()) {
            $messageToSend->setSubject($this->getComputedContent($messageTemplate->getSubject(), $entity, $context, false, false, $mainOrganismFromEntity));
            $messageToSend->setBody($this->getComputedContent($messageTemplate->getBody(), $entity, $context, true, false, $mainOrganismFromEntity));
        }
        $messageToSend->setContext($context);
        $messageToSend->setState(MessageStates::TO_SEND()->getValue());
        $messageToSend->setScheduledAt(new DateTime('now'));
        $messageToSend->setSentAt(null);
        return $this->save($messageToSend);
    }

    /**
     * @param Message $message
     * @return Message
     */
    public function retry(Message $message): Message
    {
        $message->setSendFailedCount($message->getSendFailedCount() + 1);
        $message->setState(MessageStates::SCHEDULED());
        return $this->save($message);
    }

    /**
     * @param $body
     * @param $messageTemplate
     * @return string
     */
    public function shortenUrlSmsBody($body, $messageTemplate): string
    {
        // Shorten URLs and peuso URLs
        $regexes = [ // The order matters: first proper url, second pseudo urls
            '/(https?:\/\/[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}\S*)/i', // Matches URLs starting with http:// or https:// case insensitive and having at least a domain and a tld (2char at least), ending with end of text or whitespace
            '/(?<=^|\s)(([a-zA-Z0-9-]+\.){2,}[a-zA-Z]{2,}\S*)/' // Matches URLs starting with a space or start of text, without protocol but having at least a subdomain, a domain and a tld (2char at least), ending with end of text or whitespace
        ];
        $shortenedBody = $this->shortenedUrlService->shortenUrlsInText($regexes, $body, MessageTemplate::CLASSNAME, $messageTemplate->getId());
        // Get rid of remaining pseudo URLs that may be rejected by Gateway API (they reject any toto.<tld>) by puting a space after the dot
        preg_match_all('/\b([^\s.]+\.[^\s.]+)\b/', $shortenedBody, $matches); // Matches foo.bar when things before and after are not letters and foo / bar do not contain '.'
        $stringsWithDotInside = $matches[0];
        foreach ($stringsWithDotInside as $stringWithDotInside) {
            if (!str_contains($stringWithDotInside, 'https://s.wedof') && !str_contains($stringWithDotInside, 'wedof.fr')) {
                $parts = explode('.', $stringWithDotInside);
                $stringWithDotAndSpace = $parts[0] . '. ' . $parts[1];
                $shortenedBody = Tools::strReplaceFirst($stringWithDotInside, $stringWithDotAndSpace, $shortenedBody);
            }
        }
        return $shortenedBody;
    }

    public static function getSenderName(Organism $organism): string
    {
        $sender = $organism->getSubDomain();
        if (strlen($sender) > 11) {
            $sender = substr($sender, 0, 11);
        }
        return strtoupper($sender);
    }

    //-----------------
    // METHODES PRIVEES
    //-----------------

    /**
     * @param Message $message
     * @return Message
     */
    private function save(Message $message): Message
    {
        return $this->messageRepository->save($message);
    }

    /**
     * @param $newFrom
     * @return void
     */
    private function overrideFromSmsVeryUgly($newFrom)
    {
        $hackTransports = new ReflectionProperty(Texter::class, 'transport');
        $hackTransports->setAccessible(true);
        /** @var Transports $transports */
        $transports = $hackTransports->getValue($this->texter);
        $hackTransportsAgain = new ReflectionProperty(Transports::class, 'transports');
        $hackTransportsAgain->setAccessible(true);

        /** @var TransportInterface[] $transports */
        $transports = $hackTransportsAgain->getValue($transports);
        foreach ($transports as $transportName => $transport) {
            if ($transportName === 'gatewayapi') {
                $hackFrom = new ReflectionProperty(GatewayApiTransport::class, 'from');
                $hackFrom->setAccessible(true);
                $hackFrom->setValue($transport, $newFrom);
            }
        }
    }

    /**
     * Attention la méthode ne fait pas un clone complet du message ni ne le sauvegarde
     *
     * @param Message $message
     * @return Message
     * @throws Exception
     */
    private function clone(Message $message): Message
    {
        $cloneMessage = new Message();

        $cloneMessage->setUuid(Tools::generateRandomString(16, false));
        $cloneMessage->setMessageTemplate($message->getMessageTemplate());
        $cloneMessage->setTo($message->getTo());
        $cloneMessage->setCc($message->getCc());
        $cloneMessage->setCci($message->getCci());
        $cloneMessage->setReplyTo($message->getReplyTo());
        $cloneMessage->setSubject($message->getSubject());
        $cloneMessage->setBody($message->getBody());
        $cloneMessage->setEntityClass($message->getEntityClass());
        $cloneMessage->setEntityId($message->getEntityId());
        $cloneMessage->setType($message->getType());
        $cloneMessage->setEvent($message->getEvent());

        return $cloneMessage;
    }
}
