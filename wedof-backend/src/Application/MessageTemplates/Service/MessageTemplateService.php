<?php

namespace App\Application\MessageTemplates\Service;

use App\Application\MessageTemplates\Entity\Message;
use App\Application\MessageTemplates\Entity\MessageTemplate;
use App\Application\MessageTemplates\MessageTemplatesWedofApplication;
use App\Application\MessageTemplates\Repository\MessageTemplateRepository;
use App\Entity\CertificationFolder;
use App\Entity\CertificationFolderSurvey;
use App\Entity\CertificationPartner;
use App\Entity\Organism;
use App\Entity\Proposal;
use App\Entity\RegistrationFolder;
use App\Event\CertificationFolder\CertificationFolderEvents;
use App\Event\CertificationFolderSurvey\CertificationFolderSurveyEvents;
use App\Event\CertificationPartner\CertificationPartnerEvents;
use App\Event\Proposal\ProposalEvents;
use App\Event\RegistrationFolder\RegistrationFolderEvents;
use App\Exception\WedofBadRequestHttpException;
use App\Library\utils\enums\CertificationFolderCdcStates;
use App\Library\utils\enums\CertificationFolderSurveyStates;
use App\Library\utils\enums\MessageTemplateStates;
use App\Library\utils\enums\MessageTemplateTypes;
use App\Library\utils\Tools;
use App\Service\AccessService;
use App\Service\OrganismService;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\QueryBuilder;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\ContainerInterface;
use Psr\Container\NotFoundExceptionInterface;
use ReflectionException;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Constraints\Email;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Throwable;
use Twig\Error\LoaderError;
use Twig\Error\SyntaxError;

class MessageTemplateService
{
    const EVENT_TYPES = ['certificationFolder', 'certificationFolderFile', 'registrationFolder', 'registrationFolderFile', 'proposal', 'certificationPartner', 'certificationPartnerFile', 'certificationFolderSurvey', 'certificationPartnerInvoice', 'certificationPartnerAudit'];
    private MessageTemplateRepository $messageTemplateRepository;
    private ValidatorInterface $validator;
    private AccessService $accessService;
    private ContainerInterface $container;
    private OrganismService $organismService;

    public function __construct(MessageTemplateRepository $messageTemplateRepository, AccessService $accessService, ValidatorInterface $validator, ContainerInterface $container, OrganismService $organismService)
    {
        $this->messageTemplateRepository = $messageTemplateRepository;
        $this->accessService = $accessService;
        $this->validator = $validator;
        $this->container = $container;
        $this->organismService = $organismService;
    }

    //-------------------
    // METHODES PUBLIQUES
    //-------------------
    /**
     * @param int $id
     * @return MessageTemplate|null
     */
    public function getById(int $id): ?MessageTemplate
    {
        return $this->messageTemplateRepository->find($id);
    }

    /**
     * @param Organism $organism
     * @param array $parameters
     * @return QueryBuilder
     */
    public function listReturnQueryBuilder(Organism $organism, array $parameters): QueryBuilder
    {
        return $this->messageTemplateRepository->findAllReturnQueryBuilder($organism, $parameters);
    }

    /**
     * @param Organism $organism
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countByOrganism(Organism $organism): int
    {
        return $this->messageTemplateRepository->countByOrganism($organism);
    }

    /**
     * @param Organism $organism
     * @param array $parameters
     * @return ArrayCollection
     */
    public function listByOrganismAndParams(Organism $organism, array $parameters = []): ArrayCollection
    {
        return $this->messageTemplateRepository->findAllByOrganismAndParams($organism, $parameters);
    }

    /**
     * @param Organism $organism
     * @param array $parameters
     * @return MessageTemplate
     */
    public function create(Organism $organism, array $parameters): MessageTemplate
    {
        $messageTemplate = new MessageTemplate();
        $messageTemplate->setOrganism($organism);
        $messageTemplate->setState(isset($parameters['state']) ? $parameters['state'] : MessageTemplateStates::INACTIVE());

        return $this->save($this->valueProperties($messageTemplate, $parameters));
    }

    /**
     * @param MessageTemplate $messageTemplate
     * @param array $newValues
     * @return MessageTemplate
     */
    public function update(MessageTemplate $messageTemplate, array $newValues): MessageTemplate
    {
        return $this->save($this->valueProperties($messageTemplate, $newValues));
    }

    /**
     * @param MessageTemplate $messageTemplate
     * @return void
     */
    public function delete(MessageTemplate $messageTemplate): void
    {
        $this->messageTemplateRepository->delete($messageTemplate);
    }

    /**
     * @param MessageTemplate $messageTemplate
     * @return MessageTemplate
     */
    public function activate(MessageTemplate $messageTemplate): MessageTemplate
    {
        $messageTemplate->setState(MessageTemplateStates::ACTIVE()->getValue());
        return $this->save($messageTemplate);
    }

    /**
     * @param MessageTemplate $messageTemplate
     * @return MessageTemplate
     */
    public function deactivate(MessageTemplate $messageTemplate): MessageTemplate
    {
        $messageTemplate->setState(MessageTemplateStates::INACTIVE()->getValue());
        return $this->save($messageTemplate);
    }

    /**
     * @param CertificationFolderEvents $event
     * @param string $eventName
     * @param array|null $context
     * @param Message|null $existingMessage
     * @param bool $forceScheduleNow
     * @return void
     * @throws ContainerExceptionInterface
     * @throws LoaderError
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws NotFoundExceptionInterface
     * @throws ORMException
     * @throws ReflectionException
     * @throws SyntaxError
     * @throws Throwable
     * @throws \Doctrine\ORM\ORMException
     */
    public function createOrUpdateMessagesToSendForCertificationFolders(CertificationFolderEvents $event, string $eventName, array $context = null, Message $existingMessage = null, bool $forceScheduleNow = false): void
    {
        $certificationFolder = $event->getCertificationFolder();
        $certification = $certificationFolder->getCertification();
        $organism = $certificationFolder->getCertifier();
        $listParameters = [
            'state' => ['active'],
            'event' => $eventName,
            'entityClass' => [CertificationFolder::CLASSNAME],
            'certifications' => [$certification],
            'tags' => $certificationFolder->getTags()
        ];
        $this->createOrUpdateMessages($certificationFolder, $organism, $eventName, $context, $existingMessage, $forceScheduleNow, $listParameters);
    }

    /**
     * @param ProposalEvents $event
     * @param string $eventName
     * @param array|null $context
     * @param Message|null $existingMessage
     * @param bool $forceScheduleNow
     * @return void
     * @throws ContainerExceptionInterface
     * @throws LoaderError
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws NotFoundExceptionInterface
     * @throws ORMException
     * @throws ReflectionException
     * @throws SyntaxError
     * @throws Throwable
     * @throws \Doctrine\ORM\ORMException
     */
    public function createOrUpdateMessagesToSendForProposals(ProposalEvents $event, string $eventName, array $context = null, Message $existingMessage = null, bool $forceScheduleNow = false): void
    {
        $proposal = $event->getProposal();
        $organism = $proposal->getOrganism();
        $listParameters = [
            'state' => ['active'],
            'event' => $eventName,
            'entityClass' => [Proposal::CLASSNAME],
            'tags' => $proposal->getTags()
        ];

        $this->createOrUpdateMessages($proposal, $organism, $eventName, $context, $existingMessage, $forceScheduleNow, $listParameters);
    }

    /**
     * @param RegistrationFolderEvents $event
     * @param string $eventName
     * @param array|null $context
     * @param Message|null $existingMessage
     * @param bool $forceScheduleNow
     * @return void
     * @throws ContainerExceptionInterface
     * @throws LoaderError
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws NotFoundExceptionInterface
     * @throws ORMException
     * @throws ReflectionException
     * @throws SyntaxError
     * @throws Throwable
     * @throws \Doctrine\ORM\ORMException
     */
    public function createOrUpdateMessagesToSendForRegistrationFolders(RegistrationFolderEvents $event, string $eventName, array $context = null, Message $existingMessage = null, bool $forceScheduleNow = false): void
    {
        $registrationFolder = $event->getRegistrationFolder();
        $organism = $registrationFolder->getOrganism();
        $listParameters = [
            'state' => ['active'],
            'event' => $eventName,
            'entityClass' => [RegistrationFolder::CLASSNAME],
            'certifications' => [$registrationFolder->getCertification()],
            'tags' => $registrationFolder->getTags()
        ];
        $this->createOrUpdateMessages($registrationFolder, $organism, $eventName, $context, $existingMessage, $forceScheduleNow, $listParameters);
    }

    /**
     * @param CertificationPartnerEvents $event
     * @param string $eventName
     * @param array|null $context
     * @param Message|null $existingMessage
     * @param bool $forceScheduleNow
     * @return void
     * @throws ContainerExceptionInterface
     * @throws LoaderError
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws NotFoundExceptionInterface
     * @throws ORMException
     * @throws ReflectionException
     * @throws SyntaxError
     * @throws Throwable
     * @throws \Doctrine\ORM\ORMException
     */
    public function createOrUpdateMessagesToSendForCertificationPartners(CertificationPartnerEvents $event, string $eventName, array $context = null, Message $existingMessage = null, bool $forceScheduleNow = false): void
    {
        $certificationPartner = $event->getCertificationPartner();
        $organism = $certificationPartner->getCertifier();
        if (empty($organism)) {
            return;
        }
        $listParameters = [
            'state' => ['active'],
            'event' => $eventName,
            'entityClass' => [CertificationPartner::CLASSNAME],
            'certifications' => [$certificationPartner->getCertification()],
            'tags' => $certificationPartner->getTags()
        ];
        $this->createOrUpdateMessages($certificationPartner, $organism, $eventName, $context, $existingMessage, $forceScheduleNow, $listParameters);
    }

    /**
     * @param CertificationFolderSurveyEvents $event
     * @param string $eventName
     * @param array|null $context
     * @param Message|null $existingMessage
     * @param bool $forceScheduleNow
     * @return void
     * @throws ContainerExceptionInterface
     * @throws LoaderError
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws NotFoundExceptionInterface
     * @throws ORMException
     * @throws ReflectionException
     * @throws SyntaxError
     * @throws Throwable
     * @throws \Doctrine\ORM\ORMException
     */
    public function createOrUpdateMessagesToSendForCertificationFolderSurveys(CertificationFolderSurveyEvents $event, string $eventName, array $context = null, Message $existingMessage = null, bool $forceScheduleNow = false): void
    {
        $certificationFolderSurvey = $event->getCertificationFolderSurvey();
        $certificationFolder = $certificationFolderSurvey->getCertificationFolder();
        $organism = $certificationFolder->getCertifier();
        $listParameters = [
            'state' => ['active'],
            'event' => $eventName,
            'entityClass' => [CertificationFolderSurvey::CLASSNAME],
            'certifications' => [$certificationFolder->getCertification()],
            'tags' => $certificationFolder->getTags()
        ];
        $this->createOrUpdateMessages($certificationFolderSurvey, $organism, $eventName, $context, $existingMessage, $forceScheduleNow, $listParameters);
    }


    /**
     * @param Organism $organism
     * @param string $templateName
     * @return MessageTemplate
     */
    public function createFromTemplate(Organism $organism, string $templateName): MessageTemplate
    {
        if (!in_array($templateName, ['CertificationSuccess', 'CertificationFail', 'TrainingNotification', 'TrainingAccepted', 'Proposal', 'CertificationPartner', 'CertificationFolderSurveyCreated', 'CertificationPartnerAuditCompleted', 'AccrochageMissingData'])) { // Todo if change here, change is controller as well
            throw new WedofBadRequestHttpException('Le template de message ' . $templateName . "n'est pas reconnu");
        }
        return $this->{'initialize' . $templateName}($organism);
    }

    /**
     * @param Organism $organism
     */
    public function initializeHiddenMessageTemplates(Organism $organism)
    {
        $hiddenMessageTemplate = $this->listByOrganismAndParams($organism, ['state' => [MessageTemplateStates::HIDDEN()->getValue()]]);
        if ($hiddenMessageTemplate->count() === 0) {
            $this->initializeTemplatesHidden($organism);
        }
    }

    //----------------
    // METHODES PRIVES
    //----------------
    /**
     * @param MessageTemplate $messageTemplate
     * @param array $propertiesValue
     * @return MessageTemplate
     */
    private function valueProperties(MessageTemplate $messageTemplate, array $propertiesValue): MessageTemplate
    {
        // if new property is added, don't forget to add it to the duplicate method in MessageTemplateController & methods initializeProposal / initializeTraining / initializeCertification
        $valuableProperties = ['title', 'subject', 'type', 'body', 'entityClass', 'tags', 'replyTo', 'cc', 'cci', 'to', 'delay', 'enforceConditions', 'qualiopiIndicators', 'allowResend'];
        $type = $propertiesValue["type"] ? (is_string($propertiesValue["type"]) ? MessageTemplateTypes::from($propertiesValue["type"]) : $propertiesValue["type"]) : MessageTemplateTypes::EMAIL();
        foreach ($valuableProperties as $valuableProperty) {
            if ($valuableProperty === 'tags' && isset($propertiesValue['tags'])) {
                $messageTemplate->setTagsText(implode(', ', $propertiesValue['tags']));
            } else if (isset($propertiesValue[$valuableProperty]) && method_exists($messageTemplate, "set" . ucfirst($valuableProperty))) {
                $messageTemplate->{"set" . ucfirst($valuableProperty)}($propertiesValue[$valuableProperty]);
            }
            if ($type === MessageTemplateTypes::EMAIL() && in_array($valuableProperty, ['replyTo', 'cc', 'cci', 'to']) && isset($propertiesValue[$valuableProperty])) {
                $emails = [];
                $emailConstraints = array(
                    new Email(),
                    new NotBlank()
                );
                foreach ($propertiesValue[$valuableProperty] as $email) {
                    $email = str_replace(" ", "", $email);
                    $email = str_replace(",", "", $email);
                    if (Tools::startsWith($email, "{{") && Tools::endsWith($email, ".email}}")) {
                        $emails[] = $email;
                    } else {
                        $error = $this->validator->validate($email, $emailConstraints);
                        if (count($error) == 0) {
                            $emails[] = $email;
                        }
                    }
                }
                $messageTemplate->{"set" . ucfirst($valuableProperty)}($emails);
            } else if ($type === MessageTemplateTypes::SMS() && $valuableProperty == 'to' && isset($propertiesValue[$valuableProperty])) {
                $phones = [];
                $phoneConstraints = [new Assert\Type('string'), new Assert\Length(['min' => 10, 'max' => 13]), new Assert\Regex(['pattern' => Tools::MOBILEPHONE_PATTERN])];

                foreach ($propertiesValue[$valuableProperty] as $phone) {
                    $phone = str_replace(" ", "", $phone);
                    $phone = str_replace(",", "", $phone);
                    if (Tools::startsWith($phone, "{{") && Tools::endsWith($phone, ".phoneNumber}}")) {
                        $phones[] = $phone;
                    } else {
                        $error = $this->validator->validate($phone, $phoneConstraints);
                        if (count($error) == 0) {
                            $phones[] = $phone;
                        }
                    }
                }
                $messageTemplate->{"set" . ucfirst($valuableProperty)}($phones);
            }
        }
        if (isset($propertiesValue['events'])) {
            $events = Tools::cleanEvents($propertiesValue['events'], self::EVENT_TYPES);
            if (empty($events)) {
                throw new WedofBadRequestHttpException("Erreur, seuls les évènements suivants sont possible : " . implode(", ", self::EVENT_TYPES) . ".");
            }

            $messageTemplate->setEvents(Tools::cleanEvents($propertiesValue['events'], self::EVENT_TYPES));
        }
        if (isset($propertiesValue['certifications'])) {
            $messageTemplate->getCertifications()->clear();
            foreach ($propertiesValue['certifications'] as $certification) {
                $messageTemplate->addCertification($certification);
            }
        }
        if (array_key_exists('sendAs', $propertiesValue)) {
            $newSendAs = $propertiesValue['sendAs'];
            if ($newSendAs && !$this->organismService->isSendAsEmail($messageTemplate->getOrganism(), $newSendAs)) {
                throw new WedofBadRequestHttpException("Erreur, l'adresse expéditeur choisie n'est pas un expéditeur reconnu de l'organisme");
            }
            $messageTemplate->setSendAs($newSendAs);
        }

        return $messageTemplate;
    }

    /**
     * @param MessageTemplate $messageTemplate
     * @return MessageTemplate
     */
    private function save(MessageTemplate $messageTemplate): MessageTemplate
    {
        return $this->messageTemplateRepository->save($messageTemplate);
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    private function getMessageService(): MessageService
    {
        /** @var MessageService $messageService */
        $messageService = $this->container->get(MessageService::class);
        return $messageService;
    }

    /**
     * @param Organism $organism
     * @return MessageTemplate
     */
    private function initializeCertificationSuccess(Organism $organism): MessageTemplate
    {
        return $this->create($organism, [
            "title" => "Dossier de certification Réussi",
            "subject" => "Résultat de votre examen pour la certification {{certificationFolder.certification}}",
            "type" => MessageTemplateTypes::EMAIL(),
            "body" => "Bonjour {{attendee.firstName}} {{attendee.lastName}},\n\nNous avons le plaisir de vous annoncer que le jury de certification a validé l'obtention de votre certification {{certificationFolder.certification}}.\n\n<a href=\"{{certificationFolder.attendeeLink}}\">Télécharger votre parchemin de certification.</a>\n\nCordialement,\n\n" . $organism->getName(),
            "entityClass" => "CertificationFolder",
            "tags" => [],
            "replyTo" => !empty($organism->getEmails()) ? [$organism->getEmails()[0]] : [],
            "cc" => [],
            "cci" => [],
            "to" => ["{{attendee.email}}"],
            "delay" => "+5minutes",
            "enforceConditions" => true,
            "certifInfos" => [],
            "events" => ["certificationFolder.success"],
            "allowResend" => true,
            "sendAs" => $organism->getSendAsEmail()
        ]);
    }

    /**
     * @param Organism $organism
     * @return MessageTemplate
     */
    private function initializeCertificationFail(Organism $organism): MessageTemplate
    {
        return $this->create($organism, [
            "title" => "Dossier de certification échoué",
            "subject" => "Résultat de votre examen pour la certification {{certificationFolder.certification}}",
            "type" => MessageTemplateTypes::EMAIL(),
            "body" => "Bonjour {{attendee.firstName}} {{attendee.lastName}},\n\nNous avons le regret de vous annoncer que, conformément à la décision des jury évaluateurs et du jury de certification, vous n'avez pas obtenu la certification {{certificationFolder.certification}}.\n\nCordialement,\n\n" . $organism->getName(),
            "entityClass" => "CertificationFolder",
            "tags" => [],
            "replyTo" => !empty($organism->getEmails()) ? [$organism->getEmails()[0]] : [],
            "cc" => [],
            "cci" => [],
            "to" => ["{{attendee.email}}"],
            "delay" => "+5minutes",
            "enforceConditions" => true,
            "certifInfos" => [],
            "events" => ["certificationFolder.failed"],
            "allowResend" => true,
            "sendAs" => $organism->getSendAsEmail()
        ]);
    }

    /**
     * @param Organism $organism
     * @return MessageTemplate
     */
    private function initializeTrainingNotification(Organism $organism): MessageTemplate
    {
        return $this->create($organism, [
            "title" => "Notification de changement d'état",
            "subject" => "Notification de changement d'état",
            "type" => MessageTemplateTypes::EMAIL(),
            "body" => "Bonjour,\n\n Le dossier <a href=\"{{registrationFolder.permalink}}\">{{registrationFolder.externalId}}</a> est passé à l'état {{registrationFolder.state}}.",
            "entityClass" => "RegistrationFolder",
            "tags" => [],
            "replyTo" => !empty($organism->getEmails()) ? [$organism->getEmails()[0]] : [],
            "cc" => [],
            "cci" => [],
            "to" => ["{{trainingOrganism.email}}"],
            "delay" => "+0minutes",
            "certifInfos" => [],
            "enforceConditions" => false,
            "events" => ["registrationFolder.*"],
            "allowResend" => true,
            "sendAs" => $organism->getSendAsEmail()
        ]);
    }

    /**
     * @param Organism $organism
     * @return MessageTemplate
     */
    private function initializeTrainingAccepted(Organism $organism): MessageTemplate
    {
        return $this->create($organism, [
            "title" => "Dossier de formation Accepté",
            "subject" => "Bienvenue dans votre parcours de formation",
            "type" => MessageTemplateTypes::EMAIL(),
            "body" => "Bonjour {{attendee.firstName}} {{attendee.lastName}},\n\nBienvenue dans votre parcours de formation {{training.title}}.\nVous recevrez tous les documents nécessaires avant le début de votre formation.\n\nVous pouvez visualiser dès à présent votre <a href=\"{{registrationFolder.attendeeLink}}\">dossier sur l'espace Apprenant Wedof</a>.\n\nCordialement,\n\n" . $organism->getName(),
            "entityClass" => "RegistrationFolder",
            "tags" => [],
            "replyTo" => !empty($organism->getEmails()) ? [$organism->getEmails()[0]] : [],
            "cc" => [],
            "cci" => [],
            "to" => ["{{attendee.email}}"],
            "delay" => "+0minutes",
            "certifInfos" => [],
            "enforceConditions" => false,
            "events" => ["registrationFolder.accepted"],
            "allowResend" => true,
            "sendAs" => $organism->getSendAsEmail()
        ]);
    }

    /**
     * @param Organism $organism
     * @return MessageTemplate
     */
    private function initializeProposal(Organism $organism): MessageTemplate
    {
        return $this->create($organism, [
            "title" => "Proposition Lue et Acceptée",
            "subject" => "Votre proposition",
            "type" => MessageTemplateTypes::EMAIL(),
            "body" => "Bonjour {{proposal.firstName}} {{proposal.lastName}},\n\nVotre proposition commerciale est toujours en attente d'acceptation, vous pouvez la retrouver <a href=\"{{proposal.link}}\">ici</a> pour terminer la création de votre dossier avant celle-ci ne soit plus valable.\n\nN'hésitez pas à revenir vers nous pour toute question,\n\nCordialement,\n\n" . $organism->getName(),
            "entityClass" => "Proposal",
            "tags" => [],
            "replyTo" => !empty($organism->getEmails()) ? [$organism->getEmails()[0]] : [],
            "cc" => [],
            "cci" => [],
            "to" => ["{{proposal.email}}"],
            "delay" => "+3days",
            "certifInfos" => [],
            "enforceConditions" => true,
            "events" => ["proposal.viewed", "proposal.accepted"],
            "allowResend" => true,
            "sendAs" => $organism->getSendAsEmail()
        ]);
    }

    /**
     * @param Organism $organism
     * @return MessageTemplate
     */
    private function initializeCertificationPartner(Organism $organism): MessageTemplate
    {
        return $this->create($organism, [
            "title" => "Partenariat de certification Actif",
            "subject" => "Votre partenariat est actif",
            "type" => MessageTemplateTypes::EMAIL(),
            "body" => "Bonjour {{certificationPartner.partner}},\n\nBienvenue sur la certification {{certificationPartner.codeReconnaissance}} {{certificationPartner.certification}}.\n\nVotre partenariat de certification est dorénavant actif sur la certification {{certificationPartner.certification}}.\n\nVous pouvez retrouver ce partenariat <a href=\"{{certificationPartner.partnerLink}}\">ici</a>.\n\nN'hésitez pas à revenir vers nous pour toute question,\n\nCordialement,\n\n{{certificationPartner.certifier}}",
            "entityClass" => "CertificationPartner",
            "tags" => [],
            "replyTo" => !empty($organism->getEmails()) ? [$organism->getEmails()[0]] : [],
            "cc" => [],
            "cci" => [],
            "to" => ["{{certificationPartner.email}}"],
            "delay" => "+0minutes",
            "certifInfos" => [],
            "enforceConditions" => true,
            "events" => ["certificationPartner.active"],
            "allowResend" => false,
            "sendAs" => $organism->getSendAsEmail()
        ]);
    }

    /**
     * @param Organism $organism
     * @return MessageTemplate
     */
    private function initializeCertificationFolderSurveyCreated(Organism $organism): MessageTemplate
    {
        return $this->create($organism, [
            "title" => "Enquête de suivi d'insertion professionnelle",
            "subject" => "Votre enquête en début de parcours est disponible",
            "type" => MessageTemplateTypes::EMAIL(),
            "body" => "Bonjour {{attendee.firstName}} {{attendee.lastName}},\n\nDans le cadre de votre certification, vous devrez répondre à 3 différentes enquêtes.\n\nChaque enquête est très rapide et ne prendra pas plus de 5 minutes de votre temps. Vos réponses sont essentielles pour nos services.\n\nLa première enquête permet d'analyser votre situation professionnelle avant le passage de la certification, la deuxième enquête a pour objectif d'examiner votre situation professionnelle 6 mois après l'obtention de la certification. La troisième enquête a pour objectif d'analyser votre situation professionnelle 1 an après l'obtention de la certification.\n\nVous pouvez dès à présent répondre à la première enquête dans votre Espace Candidat: <a href=\"{{certificationFolder.surveyLink}}\">{{certificationFolder.surveyLink}}</a>.\n\nN'hésitez pas à revenir vers nous pour toute question,\n\nCordialement,\n\n" . $organism->getName(),
            "entityClass" => "CertificationFolderSurvey",
            "tags" => [],
            "replyTo" => !empty($organism->getEmails()) ? [$organism->getEmails()[0]] : [],
            "cc" => [],
            "cci" => [],
            "to" => ["{{attendee.email}}"],
            "delay" => "+0minutes",
            "certifInfos" => [],
            "enforceConditions" => true,
            "events" => ["certificationFolderSurvey.created"],
            "allowResend" => false,
            "sendAs" => $organism->getSendAsEmail()
        ]);
    }

    /**
     * @param Organism $organism
     * @return MessageTemplate
     */
    private function initializeCertificationPartnerAuditCompleted(Organism $organism): MessageTemplate
    {
        return $this->create($organism, [
            "title" => "Audit de partenariat",
            "subject" => "Audit de partenariat",
            "type" => MessageTemplateTypes::EMAIL(),
            "body" => "Bonjour {{certificationPartner.partner}},\n\nUn audit a été réalisé dans le cadre de votre partenariat sur la certification {{certificationPartner.codeReconnaissance}} {{certificationPartner.certification}}.\n\nLe résultat de cet audit est {{audit.result}}, vous pouvez dès à présent retrouver ce résultat ainsi que les critères évalués dans votre espace <a href=\"{{audit.partnerLink}}\">Wedof</a>.\n\nN'hésitez pas à revenir vers nous pour toute question,\n\nCordialement,\n\n" . $organism->getName(),
            "entityClass" => "CertificationPartner",
            "tags" => [],
            "replyTo" => !empty($organism->getEmails()) ? [$organism->getEmails()[0]] : [],
            "cc" => [],
            "cci" => [],
            "to" => ["{{certificationPartner.email}}"],
            "delay" => "+0minutes",
            "certifInfos" => [],
            "enforceConditions" => false,
            "events" => ["certificationPartnerAudit.completed"],
            "allowResend" => false,
            "sendAs" => $organism->getSendAsEmail()
        ]);
    }

    /**
     * @param Organism $organism
     * @return MessageTemplate
     */
    private function initializeAccrochageMissingData(Organism $organism): MessageTemplate
    {
        return $this->create($organism, [
            "title" => "Données manquantes pour l'accrochage",
            "subject" => "Compléter vos données personnelles pour le Passeport de Compétences",
            "type" => MessageTemplateTypes::EMAIL(),
            "body" => "Bonjour {{attendee.firstName}} {{attendee.lastName}},\n\nFélicitations pour l’obtention de votre certification {{certificationFolder.certification}} !\n\nPour que cette certification apparaisse dans votre Passeport de Compétences vous devez obligatoirement complétez vos données personnelles dans votre Espace Candidat de Wedof dédié au Passeport de Compétences <a href=\"{{certificationFolder.addToPassportLink}}\">Passeport de Compétences.</a>\n\nCordialement,\n\n" . $organism->getName(),
            "entityClass" => "CertificationFolder",
            "tags" => [],
            "replyTo" => !empty($organism->getEmails()) ? [$organism->getEmails()[0]] : [],
            "cc" => [],
            "cci" => [],
            "to" => ["{{attendee.email}}"],
            "delay" => "+0minutes",
            "certifInfos" => [],
            "enforceConditions" => true,
            "events" => ["certificationFolder.missingData"],
            "allowResend" => false,
            "sendAs" => $organism->getSendAsEmail()
        ]);
    }

    /**
     * @param Message $message
     * @param RegistrationFolder|CertificationFolder|Proposal|CertificationPartner|CertificationFolderSurvey $entity
     * @return bool
     */
    public function areConditionsRespected(Message $message, $entity): bool
    {
        $messageTemplate = $message->getMessageTemplate();
        $certificationFromEntity = null;
        if (method_exists($entity, 'getCertification')) {
            $certificationFromEntity = $entity->getCertification();
        } else if (method_exists($entity, 'getCertificationFolder')) {
            $certificationFromEntity = $entity->getCertificationFolder()->getCertification();
        }

        // template actif
        $conditionsFailed = $messageTemplate->getState() !== MessageTemplateStates::ACTIVE()->getValue();
        // vérification de la classe
        $conditionsFailed = $conditionsFailed || $message->getEntityClass() !== $messageTemplate->getEntityClass();
        // vérification des certifications
        $conditionsFailed = $conditionsFailed || ($certificationFromEntity && !$messageTemplate->getCertifications()->isEmpty() && !$messageTemplate->getCertifications()->contains($certificationFromEntity));
        // vérification des tags
        $conditionsFailed = $conditionsFailed || (count($messageTemplate->getTagNames()) !== 0 && count(array_intersect($messageTemplate->getTagNames(), $entity->getTagNames())) === 0);
        // vérification des évènements
        // adéquation évènement du message et évènement du template
        $eventPrefix = lcfirst($message->getEntityClass());
        $conditionsFailed = $conditionsFailed || (!in_array($message->getEvent(), $messageTemplate->getEvents()) && !in_array($eventPrefix . '.*', $messageTemplate->getEvents()));
        // adéquation évènement et état de l'entité
        if (!$conditionsFailed) {
            switch ($message->getEntityClass()) {
                case RegistrationFolder::CLASSNAME:
                    $conditionsFailed = !in_array($message->getEvent(), [RegistrationFolderEvents::PROPOSAL_APPLIED, RegistrationFolderEvents::CREATED, RegistrationFolderEvents::UPDATED])
                        && !Tools::startsWith($message->getEvent(), 'registrationFolderFile')
                        && $message->getEvent() !== $eventPrefix . "." . $entity->getState();
                    break;
                case CertificationFolder::CLASSNAME:
                    if ($message->getEvent() === CertificationFolderEvents::ACCROCHAGE_OK) {
                        $conditionsFailed = $entity->getCdcState() !== CertificationFolderCdcStates::PROCESSED_OK()->getValue();
                    } else if ($message->getEvent() === CertificationFolderEvents::ACCROCHAGE_KO) {
                        $conditionsFailed = $entity->getCdcState() !== CertificationFolderCdcStates::PROCESSED_KO()->getValue();
                    } else if ($message->getEvent() === CertificationFolderEvents::MISSING_DATA) {
                        $conditionsFailed = $entity->isCdcCompliant();
                    } else {
                        $conditionsFailed = !in_array($message->getEvent(), [CertificationFolderEvents::CREATED, CertificationFolderEvents::UPDATED])
                            && !Tools::startsWith($message->getEvent(), 'certificationFolderFile')
                            && $message->getEvent() !== $eventPrefix . "." . $entity->getState();
                    }
                    break;
                case Proposal::CLASSNAME:
                    $conditionsFailed = !in_array($message->getEvent(), [ProposalEvents::CREATED, ProposalEvents::UPDATED])
                        && $message->getEvent() !== $eventPrefix . "." . $entity->getState();
                    break;
                case CertificationPartner::CLASSNAME:
                    $conditionsFailed = !Tools::startsWith($message->getEvent(), 'certificationPartnerFile')
                        && !Tools::startsWith($message->getEvent(), 'certificationPartnerInvoice')
                        && !Tools::startsWith($message->getEvent(), 'certificationPartnerAudit')
                        && $message->getEvent() !== $eventPrefix . "." . $entity->getState();
                    break;
                case CertificationFolderSurvey::CLASSNAME:
                    $conditionsFailed = ($message->getEvent() === CertificationFolderSurveyEvents::CREATED
                            && $entity->getState() !== CertificationFolderSurveyStates::CREATED()->getValue())
                        || ($message->getEvent() === CertificationFolderSurveyEvents::SIX_MONTH_EXPERIENCE_AVAILABLE
                            && !in_array($entity->getState(), [CertificationFolderSurveyStates::CREATED()->getValue(), CertificationFolderSurveyStates::BEFORE_CERTIFICATION_SUCCESS()->getValue()]))
                        || ($message->getEvent() === CertificationFolderSurveyEvents::LONG_TERM_EXPERIENCE_AVAILABLE
                            && $entity->getState() === CertificationFolderSurveyStates::FINISHED()->getValue());
                    break;
            }
        }

        return !$conditionsFailed;
    }

    /**
     * @param $entity
     * @param Organism $organism
     * @param string $eventName
     * @param array|null $context
     * @param Message|null $existingMessage
     * @param bool $forceScheduleNow
     * @param array $listParameters
     * @return array
     * @throws ContainerExceptionInterface
     * @throws LoaderError
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws NotFoundExceptionInterface
     * @throws ORMException
     * @throws ReflectionException
     * @throws SyntaxError
     * @throws Throwable
     * @throws \Doctrine\ORM\ORMException
     */
    private function createOrUpdateMessages($entity, Organism $organism, string $eventName, ?array $context, ?Message $existingMessage, bool $forceScheduleNow, array $listParameters): array
    {
        $messages = [];
        if ($this->accessService->isApplicationAllowedAndEnabled(MessageTemplatesWedofApplication::getAppId(), $organism)) {
            if (!$existingMessage) {
                $messageTemplates = $this->listByOrganismAndParams($organism, $listParameters)->toArray();
            } else if ($existingMessage->getMessageTemplate()->getEnforceConditions()) {
                $messageTemplates = $this->areConditionsRespected($existingMessage, $entity) ? [$existingMessage->getMessageTemplate()] : [];
            } else {
                $messageTemplates = [$existingMessage->getMessageTemplate()];
            }
            $messages = $this->getMessageService()->createOrUpdateFromTemplates($messageTemplates, $entity, $organism, $eventName, $context, $existingMessage, $forceScheduleNow);
        }
        if (count($messages) === 0 && $existingMessage) {
            $this->getMessageService()->markAsNotSentEnforcedConditions($existingMessage, $entity);
        }
        return $messages;
    }

    /**
     * @param Organism $organism
     */
    private function initializeTemplatesHidden(Organism $organism)
    {
        $messageTemplate = [
            "title" => "",
            "body" => "",
            "entityClass" => "",
            "to" => [],
            "state" => MessageTemplateStates::HIDDEN()->getValue()
        ];
        foreach ([MessageTemplateTypes::EMAIL(), MessageTemplateTypes::SMS()] as $type) {
            $messageTemplate['type'] = $type;
            $this->create($organism, $messageTemplate);
        }
    }
}
