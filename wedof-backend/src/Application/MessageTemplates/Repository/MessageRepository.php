<?php

namespace App\Application\MessageTemplates\Repository;

use App\Application\MessageTemplates\Entity\Message;
use App\Application\MessageTemplates\Entity\MessageTemplate;
use App\Entity\CertificationFolder;
use App\Entity\CertificationFolderSurvey;
use App\Entity\Subscription;
use App\Library\utils\enums\MessageStates;
use App\Library\utils\enums\MessageTemplateTypes;
use App\Repository\CertificationFolderRepository;
use DateTime;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\Query\ResultSetMapping;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Message>
 *
 * @method Message|null find($id, $lockMode = null, $lockVersion = null)
 * @method Message|null findOneBy(array $criteria, array $orderBy = null)
 * @method Message[]    findAll()
 * @method Message[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class MessageRepository extends ServiceEntityRepository
{
    private CertificationFolderRepository $certificationFolderRepository;

    public function __construct(ManagerRegistry $registry, CertificationFolderRepository $certificationFolderRepository)
    {
        parent::__construct($registry, Message::class);
        $this->certificationFolderRepository = $certificationFolderRepository;
    }

    /**
     * @param Message $message
     * @return Message
     */
    public function save(Message $message): Message
    {
        if (!$this->_em->contains($message)) {
            $this->_em->persist($message);
        }
        $this->_em->flush();
        return $message;
    }

    /**
     * @param Message $message
     * @return void
     */
    public function delete(Message $message): void
    {
        $this->_em->remove($message);
        $this->_em->flush();
    }

    /**
     * @param array $parameters
     * @return ArrayCollection
     */
    public function findAllToSend(array $parameters): ArrayCollection
    {
        $qb = $this->createQueryBuilder('message');
        $qb->where($qb->expr()->lt('message.scheduledAt', ':now'))
            ->andWhere($qb->expr()->isNull('message.sentAt'))
            ->andWhere($qb->expr()->in('message.state', ':states'))
            ->setParameter('now', new DateTime('now'))
            ->setParameter('states', $parameters['states'])
            ->addOrderBy('message.scheduledAt', 'ASC')
            ->setMaxResults($parameters['limit']);

        return new ArrayCollection($qb->getQuery()->getResult());
    }

    /**
     * @param Subscription $subscription
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countSmsForSubscription(Subscription $subscription): int
    {
        $rsm = new ResultSetMapping();
        $rsm->addScalarResult('sum', 'sum');

        $query = $this->_em->createNativeQuery("
                SELECT SUM(IF(LENGTH(CONVERT(message.body USING UTF16)) <= 140, 1, 
                    CEIL((LENGTH(CONVERT(message.body USING UTF16)) - 140) / 134) + 1)) as sum 
                FROM message 
                JOIN message_template ON message.message_template_id = message_template.id 
                      WHERE message.sent_at BETWEEN ? AND ? 
                          AND message.state = ? 
                          AND message.type = ? 
                          AND message_template.organism_id = ?"
            , $rsm);

        $query->setParameter(1, $subscription->getSmsSentNumberPeriodStartDate() ?? new DateTime('yesterday'))
            ->setParameter(2, $subscription->getSmsSentNumberPeriodEndDate() ?? new DateTime('tomorrow'))
            ->setParameter(3, MessageStates::SENT()->getValue())
            ->setParameter(4, MessageTemplateTypes::SMS()->getValue())
            ->setParameter(5, $subscription->getOrganism()->getId());

        return $query->getSingleScalarResult() ?? 0;
    }

    /**
     * @param string $entityClass
     * @param $entityId
     * @return QueryBuilder
     */
    public function findByEntityReturnQueryBuilder(string $entityClass, $entityId): QueryBuilder
    {
        $qb = $this->createQueryBuilder('message');

        if ($entityClass === CertificationFolder::CLASSNAME) {
            $entityIdSurvey = $this->certificationFolderRepository->find($entityId)->getSurvey()->getId();
            $qb->where("(
                    (message.entityClass = :entityClass AND message.entityId = :entityId) OR
                    (message.entityClass = :entityClassSurvey AND message.entityId = :entityIdSurvey)
                 )"
            )
                ->setParameter('entityClassSurvey', CertificationFolderSurvey::CLASSNAME)
                ->setParameter('entityIdSurvey', $entityIdSurvey);
        } else {
            $qb->where($qb->expr()->eq('message.entityClass', ':entityClass'))
                ->andWhere($qb->expr()->eq('message.entityId', ':entityId'));
        }

        $qb->setParameter('entityClass', $entityClass)
            ->setParameter('entityId', $entityId)
            ->addOrderBy('message.scheduledAt', 'DESC');
        return $qb;
    }

    /**
     * @param MessageTemplate $messageTemplate
     * @param string $entityClass
     * @param $entityId
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countByMessageTemplateAndEntity(MessageTemplate $messageTemplate, string $entityClass, $entityId): int
    {
        $qb = $this->createQueryBuilder('message');
        $qb->select('count(message.id)')
            ->andWhere($qb->expr()->eq('message.entityClass', ':entityClass'))
            ->andWhere($qb->expr()->eq('message.entityId', ':entityId'))
            ->andWhere($qb->expr()->eq('message.messageTemplate', ':messageTemplate'))
            ->setParameter('entityClass', $entityClass)
            ->setParameter('entityId', $entityId)
            ->setParameter('messageTemplate', $messageTemplate);
        return $qb->getQuery()->getSingleScalarResult();
    }
}
