<?php

namespace App\Application\MessageTemplates\Repository;

use App\Application\MessageTemplates\Entity\MessageTemplate;
use App\Entity\Organism;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<MessageTemplate>
 *
 * @method MessageTemplate|null find($id, $lockMode = null, $lockVersion = null)
 * @method MessageTemplate|null findOneBy(array $criteria, array $orderBy = null)
 * @method MessageTemplate[]    findAll()
 * @method MessageTemplate[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class MessageTemplateRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, MessageTemplate::class);
    }

    /**
     * @param MessageTemplate $messageTemplate
     * @return MessageTemplate
     */
    public function save(MessageTemplate $messageTemplate): MessageTemplate
    {
        if (!$this->_em->contains($messageTemplate)) {
            $this->_em->persist($messageTemplate);
        }
        $this->_em->flush();
        return $messageTemplate;
    }

    /**
     * @param MessageTemplate $messageTemplate
     * @return void
     */
    public function delete(MessageTemplate $messageTemplate): void
    {
        $this->_em->remove($messageTemplate);
        $this->_em->flush();
    }

    /**
     * @param Organism $organism
     * @param array $parameters
     * @return QueryBuilder
     */
    public function findAllReturnQueryBuilder(Organism $organism, array $parameters): QueryBuilder
    {
        $qb = $this->createQueryBuilder('messageTemplate');

        $qb->where($qb->expr()->eq('messageTemplate.organism', ':organism'))
            ->setParameter('organism', $organism);
        if (isset($parameters['messageTemplate'])) {
            $qb->andWhere($qb->expr()->eq('messageTemplate.id', ':messageTemplateId'))
                ->setParameter('messageTemplateId', $parameters['messageTemplate']->getId());
        }
        if (isset($parameters['state']) && !in_array('all', $parameters['state'])) {
            $qb->andWhere($qb->expr()->in('messageTemplate.state', ':state'))
                ->setParameter('state', $parameters['state']);
        }
        if (isset($parameters['entityClass']) && !in_array('all', $parameters['entityClass'])) {
            $qb->andWhere($qb->expr()->in('messageTemplate.entityClass', ':entityClass'))
                ->setParameter('entityClass', $parameters['entityClass']);
        }
        if (isset($parameters['event'])) {
            $qb->andWhere($qb->expr()->orX(
                $qb->expr()->like('messageTemplate.events', ':event'),
                $qb->expr()->like('messageTemplate.events', ':wildcard'),
                $qb->expr()->like('messageTemplate.events', ':subWildcard')
            ))
                ->setParameter('event', '%"' . $parameters['event'] . '"%')
                ->setParameter('wildcard', serialize(array('*')))
                ->setParameter('subWildcard', '%"' . explode('.', $parameters['event'])[0] . '.*"%');
        }
        if (isset($parameters['certifications'])) {
            $qb->leftjoin('messageTemplate.certifications', 'certification')
                ->andWhere($qb->expr()->orX(
                    $qb->expr()->in('certification', ':certifications'),
                    $qb->expr()->isNull('certification')))
                ->setParameter('certifications', $parameters['certifications']);
        }
        if (isset($parameters['tags'])) {
            $qb->leftJoin('messageTemplate.tags', 'tag')
                ->andWhere($qb->expr()->orX(
                    $qb->expr()->in('tag', ':tags'),
                    $qb->expr()->isNull('tag')))
                ->setParameter('tags', $parameters['tags']);
        }
        if (isset($parameters['type']) && !in_array('all', $parameters['type'])) {
            $qb->andWhere($qb->expr()->in('messageTemplate.type', ':type'))
                ->setParameter('type', $parameters['type']);
        }

        return $qb;
    }

    /**
     * @param Organism $organism
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countByOrganism(Organism $organism): int
    {
        $qb = $this->createQueryBuilder('mt');
        $qb->select('count(mt.id)')
            ->andWhere($qb->expr()->eq('mt.organism', ':organism'))
            ->setParameter('organism', $organism);

        return $qb->getQuery()->getSingleScalarResult();
    }

    /**
     * @param Organism $organism
     * @param array $parameters
     * @return ArrayCollection
     */
    public function findAllByOrganismAndParams(Organism $organism, array $parameters): ArrayCollection
    {
        return new ArrayCollection($this->findAllReturnQueryBuilder($organism, $parameters)->getQuery()->getResult());
    }
}
