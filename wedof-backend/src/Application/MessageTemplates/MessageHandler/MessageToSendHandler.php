<?php

namespace App\Application\MessageTemplates\MessageHandler;

use App\Application\MessageTemplates\Entity\Message;
use App\Application\MessageTemplates\Entity\MessageTemplate;
use App\Application\MessageTemplates\MessageTemplatesWedofApplication;
use App\Application\MessageTemplates\Service\MessageService;
use App\Entity\Proposal;
use App\Entity\Subscription;
use App\Library\utils\enums\MessageTemplateStates;
use App\Library\utils\enums\MessageTemplateTypes;
use App\Library\utils\enums\SubscriptionTrainingTypes;
use App\Library\utils\Tools;
use App\Message\MessageToSend;
use App\MessageHandler\WithNextMessage;
use App\Service\ApplicationService;
use App\Service\CertificationFolderService;
use App\Service\CertificationFolderSurveyService;
use App\Service\CertificationPartnerService;
use App\Service\ProposalService;
use App\Service\RegistrationFolderService;
use App\Service\StripeService;
use App\Service\SubscriptionService;
use App\Service\UserService;
use DateTime;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\ContainerInterface;
use Psr\Container\NotFoundExceptionInterface;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use ReflectionException;
use Stripe\Exception\ApiErrorException;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Notifier\Exception\TransportExceptionInterface;
use Throwable;

class MessageToSendHandler extends WithNextMessage implements MessageHandlerInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    private MessageService $messageService;
    private SubscriptionService $subscriptionService;
    private ApplicationService $applicationService;
    private ContainerInterface $container;
    private UserService $userService;

    public function __construct(MessageService $messageService, SubscriptionService $subscriptionService, MessageBusInterface $messageBus, ApplicationService $applicationService, ContainerInterface $container, UserService $userService)
    {
        parent::__construct($messageBus);
        $this->messageService = $messageService;
        $this->userService = $userService;
        $this->applicationService = $applicationService;
        $this->subscriptionService = $subscriptionService;
        $this->container = $container;
    }

    public function setLogger(LoggerInterface $logger)
    {
        $this->logger = $logger;
    }

    /**
     * @param MessageToSend $messageToSend
     * @return void
     * @throws ContainerExceptionInterface
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws NotFoundExceptionInterface
     * @throws ORMException
     * @throws ReflectionException
     * @throws Throwable
     * @throws \Doctrine\ORM\ORMException
     */
    public function __invoke(MessageToSend $messageToSend)
    {
        $message = $this->messageService->getById($messageToSend->getMessageId());
        $messageTemplate = $message->getMessageTemplate();

        /** @var RegistrationFolderService|CertificationFolderService|ProposalService|CertificationPartnerService|CertificationFolderSurveyService $entityService */
        $entityService = $message->getEntityClass() != Message::NO_ENTITY ? $this->container->get('App\Service\\' . $message->getEntityClass() . 'Service') : null;
        $entity = $entityService ? $entityService->getByEntityId($message->getEntityId()) : null;

        $organism = $message->getMessageTemplate()->getOrganism();
        $subscription = $organism->getSubscription();
        $messageTemplateApp = $this->applicationService->getByOrganismAndAppId($organism, MessageTemplatesWedofApplication::getAppId(), null, false);
        $isProposalEntityClass = $messageTemplate->getEntityClass() === Proposal::CLASSNAME;
        $hasAccessOnProposal = in_array($subscription->getTrainingType(), [SubscriptionTrainingTypes::TRIAL()->getValue(), SubscriptionTrainingTypes::PREMIUM()->getValue()]);
        if ($messageTemplate->getState() === MessageTemplateStates::INACTIVE()->getValue() || !$messageTemplateApp->getEnabled() || !$this->applicationService->isApplicationAllowed(MessageTemplatesWedofApplication::getAppId(), $subscription) || ($isProposalEntityClass && !$hasAccessOnProposal)) {
            $reason = $messageTemplate->getState() === MessageTemplateStates::INACTIVE()->getValue() ? "Le template du message n'est pas actif." : (!$messageTemplateApp->getEnabled() ? "L'application n'est pas active." : "Votre abonnement ne permet pas l'accès à l'application.");
            $this->messageService->markAsNotSentUnauthorized($message, $reason, $entity);
        } else if (empty($message->getTo())) {
            $this->messageService->markAsNotSentMissingData($message, $entity);
        } else {
            try {
                if ($message->getType() === MessageTemplateTypes::EMAIL()->getValue()) {
                    $this->manageSendEmail($message, $messageTemplate);
                } else {
                    $this->manageSendSms($message, $messageTemplate, $entity, $subscription);
                }
                $context = $message->getContext();
                $user = null;
                if (is_array($context) && array_key_exists('userId', $context)) {
                    $user = $this->userService->getById($context['userId']);
                }
                $this->messageService->markAsSent($message, $messageTemplate, $entity, $user);
            } catch (Throwable $e) {
                if ($message->getSendFailedCount() + 1 < Tools::getEnvValue('SEND_MAX_FAILURES')) {
                    $this->messageService->retry($message);
                } else {
                    $this->logger->error($e);
                    $this->messageService->markAsFailed($message, $messageTemplate, $entity);
                    $this->sendSlackAlert($e, $message);
                }
            }
        }
    }

    /**
     * @throws NotFoundExceptionInterface
     * @throws ApiErrorException
     * @throws TransportExceptionInterface
     * @throws ContainerExceptionInterface
     * @throws Throwable
     * @throws NonUniqueResultException
     * @throws NoResultException
     */
    private function manageSendSms(Message $message, MessageTemplate $messageTemplate, $entity, Subscription $subscription)
    {
        //ugly but necessary to auto-create subscription
        if (!$subscription->getSmsSentNumberPeriodEndDate() || $subscription->getSmsSentNumberPeriodEndDate() < new DateTime()) {
            /** @var StripeService $stripeService */
            $stripeService = $this->container->get(StripeService::class);
            if (!$stripeService->findOrCreateStripeSubscriptionWithProductUsage($subscription, $stripeService::PRODUCT_SMS)) {
                $this->logger->error("Error no stripe subscription found ou created to report sms usage");
                $this->messageService->markAsFailed($message, $messageTemplate, $entity);
            }
        }

        $smsData = [
            'to' => $message->getTo(),
            'body' => $message->getBody(),
            'sender' => MessageService::getSenderName($messageTemplate->getOrganism())
        ];
        $this->messageService->sendSms($smsData);
        $this->subscriptionService->updateSmsSentNumberCount($subscription);
    }

    /**
     * @param Message $message
     * @param MessageTemplate $messageTemplate
     * @return void
     * @throws \Symfony\Component\Mailer\Exception\TransportExceptionInterface
     */
    private function manageSendEmail(Message $message, MessageTemplate $messageTemplate)
    {
        $baseUrl = Tools::getEnvValue('WEDOF_BASE_URI');
        $headerLinkViewHtml = "<div style='text-align: center;'><a href='$baseUrl/app/public/message/html/{$message->getUuid()}' style='font-size: 12px; color: #878787;'>Version en ligne</a></div>";
        $emailData = [
            'to' => $message->getTo(),
            'cc' => $message->getCc(),
            'cci' => $message->getCci(),
            'replyTo' => $message->getReplyTo(),
            'subject' => $message->getSubject(),
            'body' => $headerLinkViewHtml . $message->getBody()
        ];
        $email = $messageTemplate->getSendAs();
        if ($email) {
            $emailData['from'] = Tools::getFromEmail($message->getMessageTemplate()->getOrganism()->getSendAsName($email), $email);
        }
        $this->messageService->sendEmail($emailData);
    }

    /**
     * @param Throwable $throwable
     * @param Message $message
     * @return void
     */
    private function sendSlackAlert(Throwable $throwable, Message $message): void
    {
        if (!$_ENV["LOG_ERRORS_SLACK_URI"]) {
            return;
        }
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        try {
            Tools::getHttpClient()->request('POST', $_ENV["LOG_ERRORS_SLACK_URI"], ['json' => [
                'blocks' => [
                    0 => [
                        'type' => 'section',
                        'text' => [
                            'type' => 'mrkdwn',
                            'text' => "Alerte : une erreur *MessageSendEmail* est survenue !"
                        ]
                    ],
                    1 => [
                        'type' => 'section',
                        'text' => [
                            'type' => 'mrkdwn',
                            'text' => "Message ID: " . $message->getId()
                        ]
                    ],
                    2 => [
                        'type' => 'section',
                        'text' => [
                            'type' => 'mrkdwn',
                            'text' => "Exception message: " . ($throwable->getMessage())
                        ]
                    ]
                ]
            ]]);
        } catch (Throwable $e) {
            $this->logger->error($e->getMessage());
        }
    }
}