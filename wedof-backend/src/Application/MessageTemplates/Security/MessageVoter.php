<?php

namespace App\Application\MessageTemplates\Security;

use App\Application\MessageTemplates\Entity\Message;
use App\Application\MessageTemplates\MessageTemplatesWedofApplication;
use App\Entity\User;
use App\Service\AccessService;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use LogicException;
use ReflectionException;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;
use Symfony\Component\Security\Core\Security;

class MessageVoter extends Voter
{
    public const EDIT = 'edit';
    public const VIEW = 'view';
    private Security $security;
    private AccessService $accessService;

    public function __construct(Security $security, AccessService $accessService)
    {
        $this->security = $security;
        $this->accessService = $accessService;
    }

    /**
     * @inheritDoc
     */
    protected function supports(string $attribute, $subject): bool
    {
        return $subject instanceof Message;
    }

    /**
     * @param string $attribute
     * @param $subject
     * @param TokenInterface $token
     * @return bool
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws ReflectionException
     * @throws \Doctrine\ORM\ORMException
     */
    protected function voteOnAttribute(string $attribute, $subject, TokenInterface $token): bool
    {
        if ($this->security->isGranted('ROLE_ADMIN')) {
            return true;
        }

        $accessGranted = false;
        /** @var $user User */
        $user = $token->getUser();

        /** @var Message $message */
        $message = $subject;

        switch ($attribute) {
            case self::VIEW:
                if ($this->security->isGranted('ROLE_USER') || $this->security->isGranted('ROLE_OAUTH2_MESSAGE:READ')) {
                    $accessGranted = $this->hasMessageView($user, $message);
                }
                break;
            case self::EDIT:
                if ($this->security->isGranted('ROLE_USER') || $this->security->isGranted('ROLE_OAUTH2_MESSAGE:WRITE')) {
                    $accessGranted = $this->hasMessageEdit($user, $message);
                }
                break;
            default:
                throw new LogicException("Message Voter can only respond on \"view\" and \"edit\" attribute.");
        }

        return $accessGranted;
    }

    /**
     * @param User $user
     * @param Message $message
     * @return bool
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws ReflectionException
     * @throws \Doctrine\ORM\ORMException
     */
    private function hasMessageView(User $user, Message $message): bool
    {
        return $this->hasMessageEdit($user, $message);
    }

    /**
     * @param User $user
     * @param Message $message
     * @return bool
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws ReflectionException
     * @throws \Doctrine\ORM\ORMException
     */
    private function hasMessageEdit(User $user, Message $message): bool
    {
        $organism = $user->getMainOrganism();
        $messageTemplate = $message->getMessageTemplate();
        return $organism === $messageTemplate->getOrganism() &&
            $this->accessService->isApplicationAllowedAndEnabled(MessageTemplatesWedofApplication::getAppId(), $organism);
    }
}