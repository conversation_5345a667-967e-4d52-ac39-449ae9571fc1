<?php

namespace App\Application\MessageTemplates\Security;

use App\Application\MessageTemplates\Entity\MessageTemplate;
use App\Application\MessageTemplates\MessageTemplatesWedofApplication;
use App\Entity\User;
use App\Service\AccessService;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use LogicException;
use ReflectionException;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;
use Symfony\Component\Security\Core\Security;

class MessageTemplateVoter extends Voter
{
    public const EDIT = 'edit';
    public const VIEW = 'view';
    private Security $security;
    private AccessService $accessService;

    public function __construct(Security $security, AccessService $accessService)
    {
        $this->security = $security;
        $this->accessService = $accessService;
    }

    /**
     * @inheritDoc
     */
    protected function supports(string $attribute, $subject): bool
    {
        return $subject instanceof MessageTemplate;
    }

    /**
     * @param string $attribute
     * @param $subject
     * @param TokenInterface $token
     * @return bool
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws ReflectionException
     * @throws \Doctrine\ORM\ORMException
     */
    protected function voteOnAttribute(string $attribute, $subject, TokenInterface $token): bool
    {
        if ($this->security->isGranted('ROLE_ADMIN')) {
            return true;
        }

        $accessGranted = false;
        /** @var $user User */
        $user = $token->getUser();

        /** @var MessageTemplate $messageTemplate */
        $messageTemplate = $subject;

        switch ($attribute) {
            case self::VIEW:
                if ($this->security->isGranted('ROLE_USER') || $this->security->isGranted('ROLE_OAUTH2_MESSAGETEMPLATE:READ')) {
                    $accessGranted = $this->hasMessageTemplateView($user, $messageTemplate);
                }
                break;
            case self::EDIT:
                if ($this->security->isGranted('ROLE_USER') || $this->security->isGranted('ROLE_OAUTH2_MESSAGETEMPLATE:WRITE')) {
                    $accessGranted = $this->hasMessageTemplateEdit($user, $messageTemplate);
                }
                break;
            default:
                throw new LogicException("MessageTemplate Voter can only respond on \"view\" and \"edit\" attribute.");
        }

        return $accessGranted;
    }

    /**
     * @param User $user
     * @param MessageTemplate $messageTemplate
     * @return bool
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws ReflectionException
     * @throws \Doctrine\ORM\ORMException
     */
    private function hasMessageTemplateView(User $user, MessageTemplate $messageTemplate): bool
    {
        return $this->hasMessageTemplateEdit($user, $messageTemplate);
    }

    /**
     * @param User $user
     * @param MessageTemplate $messageTemplate
     * @return bool
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws ReflectionException
     * @throws \Doctrine\ORM\ORMException
     */
    private function hasMessageTemplateEdit(User $user, MessageTemplate $messageTemplate): bool
    {
        $organism = $user->getMainOrganism();
        return $organism === $messageTemplate->getOrganism() &&
            $this->accessService->isApplicationAllowedAndEnabled(MessageTemplatesWedofApplication::getAppId(), $organism);
    }
}