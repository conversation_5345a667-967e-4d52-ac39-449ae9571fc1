<?php

namespace App\Application\MessageTemplates\Entity;

use App\Application\MessageTemplates\Repository\MessageTemplateRepository;
use App\Entity\Certification;
use App\Entity\Organism;
use App\Entity\Traits\TimestampableTrait;
use Beelab\TagBundle\Entity\AbstractTaggable;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use JMS\Serializer\Annotation as Serializer;

/**
 * @ORM\Entity(repositoryClass=MessageTemplateRepository::class)
 *
 * @ORM\HasLifecycleCallbacks()
 */
class MessageTemplate extends AbstractTaggable
{
    use TimestampableTrait;

    public const CLASSNAME = 'MessageTemplate';

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private int $id;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\Organism")
     * @ORM\JoinColumn(nullable=false)
     * @Serializer\Exclude()
     */
    private Organism $organism;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private string $title;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private ?string $subject = null;

    /**
     * @ORM\Column(type="string", length=40)
     */
    private string $type;

    /**
     * @ORM\Column(type="string", length=40)
     */
    private string $state;

    /**
     * @ORM\Column(type="text")
     */
    private string $body;

    /**
     * @ORM\Column(type="array", nullable=true)
     */
    private array $replyTo = [];

    /**
     * @ORM\Column(type="string", length=40)
     */
    private string $entityClass;

    /**
     * @ORM\Column(type="array")
     */
    private array $events = [];

    /**
     * @ORM\ManyToMany(targetEntity="App\Entity\Certification")
     * @Serializer\Exclude()
     */
    private Collection $certifications;

    /**
     * @ORM\ManyToMany(targetEntity="App\Entity\Tag")
     * Display with serializer to get as array[string] and not as objet
     * CANNOT BE TYPED /!\
     * Don't forget to override setTagsText() and change updatedOn as done below!
     */
    protected $tags; // Type must not be defined (as in base class '\Beelab\TagBundle\Entity\AbstractTaggable')

    /**
     * @ORM\OneToMany(targetEntity="App\Application\MessageTemplates\Entity\Message", mappedBy="messageTemplate", orphanRemoval=true)
     * @Serializer\Exclude()
     */
    private Collection $messages;

    /**
     * @ORM\Column(type="string", length=20, nullable=true)
     */
    private ?string $delay = null;

    /**
     * @ORM\Column(type="array", nullable=true)
     */
    private ?array $cc = [];

    /**
     * @ORM\Column(type="array", nullable=true)
     */
    private ?array $cci = [];

    /**
     * @ORM\Column(type="array")
     * @Serializer\Exclude()
     */
    private array $recipientTo;

    /**
     * @ORM\Column(type="boolean", options={"default" : false})
     */
    private bool $enforceConditions = false;

    /**
     * @ORM\Column(type="json", nullable=true)
     * @Serializer\Type("array")
     */
    private ?array $qualiopiIndicators = null;

    /**
     * @ORM\Column(type="boolean", options={"default" : true})
     */
    private bool $allowResend = true;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private ?string $sendAs = null;


    public function __construct()
    {
        $this->certifications = new ArrayCollection();
        parent::__construct(); // required by tags
        $this->messages = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getOrganism(): Organism
    {
        return $this->organism;
    }

    public function setOrganism(Organism $organism): self
    {
        $this->organism = $organism;

        return $this;
    }

    public function getTitle(): string
    {
        return $this->title;
    }

    public function setTitle(string $title): self
    {
        $this->title = $title;

        return $this;
    }

    public function getSubject(): ?string
    {
        return $this->subject;
    }

    public function setSubject(?string $subject): self
    {
        $this->subject = $subject;

        return $this;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getState(): string
    {
        return $this->state;
    }

    public function setState(string $state): self
    {
        $this->state = $state;

        return $this;
    }

    public function getBody(): string
    {
        return $this->body;
    }

    public function setBody(string $body): self
    {
        $this->body = $body;

        return $this;
    }

    public function getReplyTo(): ?array
    {
        return $this->replyTo;
    }

    public function setReplyTo(?array $replyTo): self
    {
        $this->replyTo = $replyTo;

        return $this;
    }

    public function getEntityClass(): string
    {
        return $this->entityClass;
    }

    public function setEntityClass(string $entityClass): self
    {
        $this->entityClass = $entityClass;

        return $this;
    }

    public function getEvents(): array
    {
        return $this->events;
    }

    public function setEvents(array $events): self
    {
        $this->events = $events;

        return $this;
    }

    /**
     * @return Collection<int, Certification>
     */
    public function getCertifications(): Collection
    {
        return $this->certifications;
    }

    public function addCertification(Certification $certification): self
    {
        if (!$this->certifications->contains($certification)) {
            $this->certifications[] = $certification;
        }

        return $this;
    }

    public function removeCertification(Certification $certification): self
    {
        $this->certifications->removeElement($certification);

        return $this;
    }

    // Required on every taggable so that if only tags are changed, they are persisted
    public function setTagsText(?string $tagsText): void
    {
        if ($tagsText !== $this->getTagsText()) {
            $this->updateUpdatedOnTimestamp();
        }
        parent::setTagsText($tagsText);
    }

    /**
     * @return Collection<int, Message>
     */
    public function getMessages(): Collection
    {
        return $this->messages;
    }

    public function addMessage(Message $message): self
    {
        if (!$this->messages->contains($message)) {
            $this->messages[] = $message;
            $message->setMessageTemplate($this);
        }

        return $this;
    }

    public function removeMessage(Message $message): self
    {
        if ($this->messages->removeElement($message)) {
            // set the owning side to null (unless already changed)
            if ($message->getMessageTemplate() === $this) {
                $message->setMessageTemplate(null);
            }
        }

        return $this;
    }

    public function getDelay(): ?string
    {
        return $this->delay;
    }

    public function setDelay(?string $delay): self
    {
        $this->delay = $delay;

        return $this;
    }

    public function getCc(): ?array
    {
        return $this->cc;
    }

    public function setCc(?array $cc): self
    {
        $this->cc = $cc;

        return $this;
    }

    public function getCci(): ?array
    {
        return $this->cci;
    }

    public function setCci(?array $cci): self
    {
        $this->cci = $cci;

        return $this;
    }

    /**
     * @Serializer\VirtualProperty()
     */
    public function getTo(): array
    {
        return $this->recipientTo;
    }

    public function setTo(array $recipientTo): self
    {
        $this->recipientTo = $recipientTo;

        return $this;
    }

    public function getEnforceConditions(): bool
    {
        return $this->enforceConditions;
    }

    public function setEnforceConditions(bool $enforceConditions): self
    {
        $this->enforceConditions = $enforceConditions;

        return $this;
    }

    public function getQualiopiIndicators(): ?array
    {
        return $this->qualiopiIndicators;
    }

    public function setQualiopiIndicators(?array $qualiopiIndicators): self
    {
        $this->qualiopiIndicators = $qualiopiIndicators;

        return $this;
    }

    public function getAllowResend(): bool
    {
        return $this->allowResend;
    }

    public function setAllowResend(bool $allowResend): self
    {
        $this->allowResend = $allowResend;

        return $this;
    }

    public function getSendAs(): ?string
    {
        return $this->sendAs;
    }

    public function setSendAs(?string $sendAs): self
    {
        $this->sendAs = $sendAs;

        return $this;
    }
}
