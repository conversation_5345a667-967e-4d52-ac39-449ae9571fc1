<?php

namespace App\Application\MessageTemplates\Entity;

use App\Application\MessageTemplates\Repository\MessageRepository;
use App\Entity\Traits\TimestampableTrait;
use DateTimeInterface;
use Doctrine\ORM\Mapping as ORM;
use Hateoas\Configuration\Annotation as Hateoas;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;

/**
 * @ORM\Entity(repositoryClass=MessageRepository::class)
 * @ORM\Table(indexes={@ORM\Index(name="entity",columns={"entity_class", "entity_id"})})
 * @ORM\HasLifecycleCallbacks()
 * @Hateoas\Relation("messageTemplate", href = "expr('/api/messageTemplates/' ~ object.getMessageTemplate().getId())", attributes={"title" = "expr(object.getMessageTemplate().getTitle())"})
 */
class Message
{
    public const NO_ENTITY = 'no_entity';

    use TimestampableTrait;

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private int $id;

    /**
     * @ORM\Column(type="string", unique=true)
     */
    private string $uuid;

    /**
     * @ORM\ManyToOne(targetEntity="App\Application\MessageTemplates\Entity\MessageTemplate", inversedBy="messages")
     * @ORM\JoinColumn(nullable=false)
     * @Serializer\Exclude()
     */
    private MessageTemplate $messageTemplate;

    /**
     * @ORM\Column(type="array")
     * @Serializer\Exclude()
     */
    private array $recipientTo;

    /**
     * @ORM\Column(type="array", nullable=true)
     */
    private ?array $replyTo = [];

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private ?string $subject = null;

    /**
     * @ORM\Column(type="text")
     */
    private string $body;

    /**
     * @ORM\Column(type="string", length=40)
     */
    private string $entityClass;

    /**
     * @ORM\Column(type="string", length=40)
     */
    private string $entityId;

    /**
     * @ORM\Column(type="datetime")
     */
    private DateTimeInterface $scheduledAt;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     */
    private ?DateTimeInterface $sentAt = null;

    /**
     * @ORM\Column(type="string", length=40)
     */
    private string $type;

    /**
     * @ORM\Column(type="array", nullable=true)
     */
    private ?array $cc = [];

    /**
     * @ORM\Column(type="array", nullable=true)
     */
    private ?array $cci = [];

    /**
     * @ORM\Column(type="string", length=40)
     */
    private string $state;

    /**
     * @ORM\Column(type="text", nullable=true)
     */
    private ?string $event;

    /**
     * @ORM\Column(type="json", nullable=true)
     * @Serializer\Type("array")
     */
    private ?array $context = null;

    /**
     * @ORM\Column(type="integer", options={"default" : 0})
     */
    private int $sendFailedCount = 0;

    public function getId(): int
    {
        return $this->id;
    }

    public function getMessageTemplate(): MessageTemplate
    {
        return $this->messageTemplate;
    }

    public function setMessageTemplate(MessageTemplate $messageTemplate): self
    {
        $this->messageTemplate = $messageTemplate;

        return $this;
    }

    /**
     * @Serializer\VirtualProperty()
     */
    public function getTo(): array
    {
        return $this->recipientTo;
    }

    public function setTo(array $recipientTo): self
    {
        $this->recipientTo = $recipientTo;

        return $this;
    }

    public function getReplyTo(): ?array
    {
        return $this->replyTo;
    }

    public function setReplyTo(?array $replyTo): self
    {
        $this->replyTo = $replyTo;

        return $this;
    }

    public function getSubject(): ?string
    {
        return $this->subject;
    }

    public function setSubject(?string $subject): self
    {
        $this->subject = $subject;

        return $this;
    }

    public function getBody(): string
    {
        return $this->body;
    }

    public function setBody(string $body): self
    {
        $this->body = $body;

        return $this;
    }

    public function getEntityClass(): string
    {
        return $this->entityClass;
    }

    public function setEntityClass(string $entityClass): self
    {
        $this->entityClass = $entityClass;

        return $this;
    }

    public function getEntityId(): string
    {
        return $this->entityId;
    }

    public function setEntityId(string $entityId): self
    {
        $this->entityId = $entityId;

        return $this;
    }

    public function getScheduledAt(): DateTimeInterface
    {
        return $this->scheduledAt;
    }

    public function setScheduledAt(DateTimeInterface $scheduledAt): self
    {
        $this->scheduledAt = $scheduledAt;

        return $this;
    }

    public function getSentAt(): ?DateTimeInterface
    {
        return $this->sentAt;
    }

    public function setSentAt(?DateTimeInterface $sentAt): self
    {
        $this->sentAt = $sentAt;

        return $this;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getCc(): ?array
    {
        return $this->cc;
    }

    public function setCc(?array $cc): self
    {
        $this->cc = $cc;

        return $this;
    }

    public function getCci(): ?array
    {
        return $this->cci;
    }

    public function setCci(?array $cci): self
    {
        $this->cci = $cci;

        return $this;
    }

    public function getState(): string
    {
        return $this->state;
    }

    public function setState(string $state): self
    {
        $this->state = $state;

        return $this;
    }

    public function getEvent(): ?string
    {
        return $this->event;
    }

    public function setEvent(string $event): self
    {
        $this->event = $event;

        return $this;
    }

    public function getUuid(): ?string
    {
        return $this->uuid;
    }

    public function setUuid(string $uuid): self
    {
        $this->uuid = $uuid;

        return $this;
    }


    public function getContext(): ?array
    {
        return $this->context;
    }

    public function setContext(?array $context): self
    {
        $this->context = $context;

        return $this;
    }

    public function getSendFailedCount(): int
    {
        return $this->sendFailedCount;
    }

    public function setSendFailedCount(int $sendFailedCount): self
    {
        $this->sendFailedCount = $sendFailedCount;

        return $this;
    }
}
