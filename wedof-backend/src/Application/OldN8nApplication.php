<?php
// src/Event/Application/ApplicationEventsSubscriber.php
namespace App\Application;

use App\Entity\Application;
use App\Entity\Subscription;
use App\Exception\WedofBadRequestHttpException;
use App\Library\utils\Tools;
use App\Repository\WebhookRepository;
use App\Service\ActivityService;
use App\Service\ApplicationService;
use App\Service\WebhookService;
use ArrayObject;
use Exception;
use http\Exception\RuntimeException;
use Symfony\Component\HttpClient\HttpClient;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Throwable;

abstract class OldN8nApplication extends WedofApplication
{
    protected static string $APP_ID = "n8nOld";

    protected static string $N8N = "https://automation.goflexla.com";
    protected static string $N8N_API_KEY = "n8n_api_0e4f44a57b9292f6ea2a1fcb3407cf296b3f9496630607a1347ea4e04431e13a8b9c4cb32f866d6c";
    protected static array $METHODS = ['authUrl'];

    private WebhookService $webhookService;
    private WebhookRepository $webhookRepository;

    /**
     * ApplicationEvents constructor.
     * @param ApplicationService $applicationService
     * @param ActivityService $activityService
     * @param WebhookService $webhookService
     * @param WebhookRepository $webhookRepository
     */
    public function __construct(ApplicationService $applicationService, ActivityService $activityService, WebhookService $webhookService, WebhookRepository $webhookRepository)
    {
        parent::__construct($applicationService, $activityService);
        $this->webhookService = $webhookService;
        $this->webhookRepository = $webhookRepository;
        $this->applicationService = $applicationService;
    }

    /**
     * @param Application $application
     * @return bool
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    public function setupWorkflows(Application $application): bool
    {
        $client = self::getHttpClient();
        $metadata = $application->getMetadata();
        $metadata['workflows'] = [];
        $templates = self::getWorkflowTemplates(self::getAppId());
        foreach ($templates as $template) {
            try {
                $response = $client->request('GET', self::$N8N . '/api/v1/workflows/' . $template['id'], [
                    'headers' => [
                        'Accept' => 'application/json',
                        'Content-Type' => 'application/json'
                    ]
                ]);
                $template = json_decode($response->getContent(), true);
                $webhookPath = null;
                $webhookEvents = null;
                if (self::getWorkflowCredentialsNodeType()) {
                    if (empty($application->getMetadata()['credentials'])) {
                        throw new WedofBadRequestHttpException();
                    }
                    $credentials = $application->getMetadata()['credentials'];
                    $template['nodes'] = array_map(function ($node) use ($application, $credentials, $webhookPath) {
                        if (empty($node['parameters'])) {
                            $node['parameters'] = new ArrayObject();
                        }
                        if ($node['type'] === self::getWorkflowCredentialsNodeType()) {
                            $node['credentials'] = [];
                            $node['credentials'][$credentials['type']] = [
                                'id' => $credentials['id'],
                                'name' => $credentials['name']
                            ];
                        } else if ($node['type'] === 'n8n-nodes-base.webhook') {
                            $node['parameters']['path'] = uniqid();
                        }
                        return $node;
                    }, $template['nodes']);
                    $webhookNode = array_values(array_filter($template['nodes'], function ($node) {
                        return $node['type'] === 'n8n-nodes-base.webhook';
                    }));
                    if (count($webhookNode) === 1) {
                        $webhookPath = $webhookNode[0]['parameters']['path'];
                        $webhookEvents = json_decode($webhookNode[0]['notes'], true)['events'];
                    }
                }
                $template['name'] = str_replace("Template", "[" . $application->getOrganism()->getSiret() . "]", $template['name']);
                if (!Tools::contains($template['name'], $application->getOrganism()->getSiret())) {
                    $template['name'] = "[" . $application->getOrganism()->getSiret() . "] " . $template['name'];
                }
                unset($template['id']);
                unset($template['active']);
                unset($template['createdAt']);
                unset($template['updatedAt']);
                unset($template['tags']);
                $template['settings'] = new ArrayObject();
                $responseT = $client->request('POST', self::$N8N . '/api/v1/workflows', [
                    'headers' => [
                        'Accept' => 'application/json',
                        'Content-Type' => 'application/json'
                    ],
                    'body' => json_encode($template)
                ]);
                $workflow = json_decode($responseT->getContent(), true);
                $responseW = $client->request('POST', self::$N8N . '/api/v1/workflows/' . $workflow['id'] . '/activate', [
                    'headers' => [
                        'Accept' => 'application/json',
                        'Content-Type' => 'application/json'
                    ],
                    'body' => json_encode($workflow)
                ]);
                $webhook = null;
                if ($webhookPath && $webhookEvents) {
                    if ($responseW->getStatusCode() === 200 && $responseW->getContent()) {
                        $webhookData = [
                            'url' => self::$N8N . '/webhook/' . $webhookPath,
                            'events' => $webhookEvents, //feed events from template
                            'type' => self::getAppId()
                        ];
                        $webhook = $this->webhookService->create($webhookData, $application->getOrganism());
                    }
                }
                $metadata['workflows'][] = [
                    'id' => intval($workflow['id']),
                    'webhookId' => $webhook ? $webhook->getId() : null
                ];
            } catch (Exception $e) {
                throw $e instanceof WedofBadRequestHttpException ? $e : new WedofBadRequestHttpException();
            }
            $this->applicationService->updateMetadata($application, $metadata);
        }
        $this->applicationService->updateMetadata($application, $metadata);
        return count($metadata['workflows']) === count($templates);
    }

    /**
     * @param Application $application
     * @return array
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     * @throws Exception
     */
    public function authUrl(Application $application): array
    {
        try {
            $metadata = $application->getMetadata();
            if (!empty($metadata['credentials'])) {
                $client = self::getHttpClient(true);
                $url = self::$N8N . "/rest/oauth2-credential/auth";
                $data = [
                    "id" => $metadata['credentials']['id'],
                    "clientId" => self::getOAuth2ClientId(),
                    "clientSecret" => self::getOAuth2ClientSecret(),
                    "authUrl" => self::getOAuth2AuthUrl(),
                    "accessTokenUrl" => self::getOAuth2TokenUrl(),
                    "scope" => self::getOAuth2ScopesN(),
                    "authQueryParameters" => "access_type=" . self::getOAuth2AccessType(),
                    "authentication" => self::getOAuth2Authentication()
                ];
                $response = $client->request('GET', $url, [
                    'headers' => [
                        'Accept' => 'application/json',
                        'Content-Type' => 'application/json',
                    ],
                    'query' => $data
                ]);
                if ($response->getStatusCode() === 200) {
                    $json = json_decode($response->getContent(), true)['data'];
                    return ['url' => $json];
                } else {
                    throw new WedofBadRequestHttpException();
                }
            } else {
                throw new WedofBadRequestHttpException();
            }
        } catch (Exception $e) {
            throw $e instanceof WedofBadRequestHttpException ? $e : new WedofBadRequestHttpException();
        }
    }

    /**
     * @param Application $application
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     * @throws Exception
     */
    public function beforeEnable(Application $application): void
    {
        $data = [
            "name" => "[" . $application->getOrganism()->getSiret() . "] " . $this->getAppId() . " account",
            "type" => self::getCredentialsType(),
            "data" => [
                "authUrl" => self::getOAuth2AuthUrl(),
                "accessTokenUrl" => self::getOAuth2TokenUrl(),
                "clientId" => self::getOAuth2ClientId(),
                "clientSecret" => self::getOAuth2ClientSecret()
            ]
        ];
        $client = self::getHttpClient();
        $response = $client->request('POST', self::$N8N . '/api/v1/credentials', [
            'headers' => [
                'Accept' => 'application/json',
                'Content-Type' => 'application/json'
            ],
            'body' => json_encode($data)
        ]);
        try {
            if ($response->getContent()) {
                $json = json_decode($response->getContent(), true);
                $this->applicationService->updateMetadata($application, ['credentials' => $json]);
            } else {
                throw new WedofBadRequestHttpException();
            }
        } catch (Exception $e) {
            throw $e instanceof WedofBadRequestHttpException ? $e : new WedofBadRequestHttpException();
        }
    }

    /**
     * @param Application $application
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     * @throws Exception
     */
    public function enabled(Application $application): void
    {
        $this->refresh($application);
    }

    /**
     * @param Application $application
     * @return void
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    public function show(Application $application): void
    {
        $this->refresh($application);
    }

    /**
     * @param Application $application
     * @throws TransportExceptionInterface
     * @throws Exception|Throwable
     */
    public function beforeDisable(Application $application): void
    {
        try {
            if (!empty($application->getMetadata()['credentials'])) {
                $credentials = $application->getMetadata()['credentials'];
                $client = self::getHttpClient();
                $client->request('DELETE', self::$N8N . '/api/v1/credentials/' . $credentials['id'], [
                    'headers' => [
                        'Accept' => 'application/json',
                        'Content-Type' => 'application/json'
                    ]
                ]);
            }
        } catch (Exception $e) {
        }

        try {
            if (!empty($application->getMetadata()['workflows'])) {
                foreach ($application->getMetadata()['workflows'] as $workflow) {
                    $client = self::getHttpClient();
                    $client->request('DELETE', self::$N8N . '/api/v1/workflows/' . $workflow['id'], [
                        'headers' => [
                            'Accept' => 'application/json',
                            'Content-Type' => 'application/json'
                        ]
                    ]);
                    if (!empty($workflow['webhookId'])) {
                        $webhook = $this->webhookRepository->find($workflow['webhookId']);
                        $webhook = $this->webhookService->update($webhook, ['enabled' => false]);
                        if ($webhook->getEnabled()) {
                            throw new WedofBadRequestHttpException();
                        }
                    }
                }
            }
        } catch (Exception $e) {
        }
    }

    /**
     * @param Application $application
     */
    public function disabled(Application $application): void
    {
        // TODO: Implement disabled() method.
    }

    /**
     * @param Application $application
     */
    public function onNewOAuth(Application $application): void
    {
        // TODO: Implement onNewOAuth() method.
    }

    /**
     * @param Application $application
     */
    public function onRefreshOAuth(Application $application): void
    {
        // TODO: Implement onRefreshOAuth() method.
    }

    /**
     * @param Application $application
     * @param array $previousMetadata
     */
    public function onUpdateMetadata(Application $application, array $previousMetadata): void
    {
        // TODO: Implement onUpdateMetadata() method.
    }

    /**
     * @param Application $application
     * @return void
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    private function refresh(Application $application)
    {
        $client = self::getHttpClient(true);
        $response = $client->request('GET', self::$N8N . '/rest/credentials/' . $application->getMetadata()['credentials']['id'], [
            'headers' => [
                'Accept' => 'application/json',
                'Content-Type' => 'application/json'
            ],
            'query' => [
                'includeData' => "true"
            ]
        ]);
        try {
            if ($response->getContent()) {
                $metadata = $application->getMetadata();
                $json = json_decode($response->getContent(), true)['data'];
                $metadata['credentials'] = $json;
                $metadata['connected'] = !empty($metadata['credentials']['data']['oauthTokenData']['expires_in']);
                $this->applicationService->updateMetadata($application, $metadata);
            } else {
                throw new WedofBadRequestHttpException();
            }
        } catch (Exception $e) {
            throw $e instanceof WedofBadRequestHttpException ? $e : new WedofBadRequestHttpException();
        }
    }

    /**
     * @return array
     */
    static public function getApplicationSubscribedEvents(): array
    {
        return [];
    }

    /**
     * @param string $tagSearch
     * @return array
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    public static function getWorkflowTemplates(string $tagSearch): array
    {
        $client = self::getHttpClient();
        $response = $client->request('GET', self::$N8N . "/api/v1/workflows?tags=$tagSearch", [
            'headers' => [
                'Accept' => 'application/json',
                'Content-Type' => 'application/json'
            ]
        ]);
        if ($response->getStatusCode() === 200 && $response->getContent()) {
            return json_decode($response->getContent(), true)['data'];
        } else {
            throw new WedofBadRequestHttpException();
        }
    }

    /**
     * @return string
     */
    public static function getWorkflowCredentialsNodeType(): string
    {
        $c = get_called_class();
        if (property_exists($c, 'N8N_WORKFLOW_CREDENTIALS_NODE_TYPE')) {
            return $c::$N8N_WORKFLOW_CREDENTIALS_NODE_TYPE;
        } else {
            throw new RuntimeException("invalid N8N_WORKFLOW_CREDENTIALS_NODE_TYPE");
        }
    }

    /**
     * @return string
     */
    public static function getOAuth2ClientId(): string
    {
        $c = get_called_class();
        if (property_exists($c, 'N8N_OAUTH2_CLIENT_ID')) {
            return $c::$N8N_OAUTH2_CLIENT_ID;
        } else {
            throw new RuntimeException("invalid N8N_OAUTH2_CLIENT_ID");
        }
    }

    /**
     * @return string
     */
    public static function getOAuth2ClientSecret(): string
    {
        $c = get_called_class();
        if (property_exists($c, 'N8N_OAUTH2_CLIENT_SECRET')) {
            return $c::$N8N_OAUTH2_CLIENT_SECRET;
        } else {
            throw new RuntimeException("invalid N8N_OAUTH2_CLIENT_SECRET");
        }
    }

    /**
     * @return string
     */
    public static function getOAuth2AuthUrl(): string
    {
        $c = get_called_class();
        if (property_exists($c, 'N8N_OAUTH2_AUTH_URL')) {
            return $c::$N8N_OAUTH2_AUTH_URL;
        } else {
            throw new RuntimeException("invalid N8N_OAUTH2_AUTH_URL");
        }
    }

    /**
     * @return string
     */
    public static function getOAuth2Url(): string
    {
        $c = get_called_class();
        if (property_exists($c, 'N8N_OAUTH2_URL')) {
            return $c::$N8N_OAUTH2_URL;
        } else {
            throw new RuntimeException("invalid N8N_OAUTH2_URL");
        }
    }

    /**
     * @return string
     */
    public static function getOAuth2TokenUrl(): string
    {
        $c = get_called_class();
        if (property_exists($c, 'N8N_OAUTH2_TOKEN_URL')) {
            return $c::$N8N_OAUTH2_TOKEN_URL;
        } else {
            throw new RuntimeException("invalid N8N_OAUTH2_TOKEN_URL");
        }
    }

    /**
     * @return string
     */
    public static function getOAuth2AccessType(): string
    {
        $c = get_called_class();
        if (property_exists($c, 'N8N_OAUTH2_ACCESS_TYPE')) {
            return $c::$N8N_OAUTH2_ACCESS_TYPE;
        } else {
            throw new RuntimeException("invalid N8N_OAUTH2_ACCESS_TYPE");
        }
    }

    /**
     * @return string
     */
    public static function getOAuth2ScopesN(): string
    {
        $c = get_called_class();
        if (property_exists($c, 'N8N_OAUTH2_SCOPES')) {
            return $c::$N8N_OAUTH2_SCOPES;
        } else {
            throw new RuntimeException("invalid N8N_OAUTH2_SCOPES");
        }
    }

    /**
     * @return string
     */
    public static function getOAuth2Authentication(): string
    {
        $c = get_called_class();
        if (property_exists($c, 'N8N_OAUTH2_AUTHENTICATION')) {
            return $c::$N8N_OAUTH2_AUTHENTICATION;
        } else {
            throw new RuntimeException("invalid N8N_OAUTH2_AUTHENTICATION");
        }
    }

    /**
     * @return string
     */
    public static function getCredentialsType(): string
    {
        $c = get_called_class();
        if (property_exists($c, 'N8N_CREDENTIALS_TYPE')) {
            return $c::$N8N_CREDENTIALS_TYPE;
        } else {
            throw new RuntimeException("invalid N8N_CREDENTIALS_TYPE");
        }
    }

    static public function getAllowedSubscriptionTypes(Subscription $subscription): array
    {
        return ['training' => [], 'certifier' => []];
    }

    private static function getHttpClient(bool $useCookie = false): HttpClientInterface
    {
        if ($useCookie) {
            return HttpClient::create(['headers' => ['Cookie' => 'n8n-auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.4kguI4XrtGbnxZCL4zPNWO8PdjuHgKSCO99kYGLlM34;']]);
        } else {
            return HttpClient::create(['headers' => ['X-N8N-API-KEY' => self::$N8N_API_KEY]]);
        }
    }
}
