<?php

namespace App\Application\Zoho;

use App\Application\OldN8nApplication;
use App\Entity\Application;
use App\Entity\Subscription;
use App\Repository\WebhookRepository;
use App\Service\ActivityService;
use App\Service\ApplicationService;
use App\Service\WebhookService;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Throwable;

class ZohoWedofApplication extends OldN8nApplication
{
    protected static string $APP_ID = "zoho";
    protected static array $METHODS = ['authUrl', 'setup']; // custom methods called from front

    protected static string $N8N_CREDENTIALS_TYPE = "zohoOAuth2Api";
    protected static string $N8N_OAUTH2_AUTH_URL = "https://accounts.zoho.com/oauth/v2/auth";
    protected static string $N8N_OAUTH2_TOKEN_URL = "https://accounts.zoho.eu/oauth/v2/token";
    protected static string $N8N_OAUTH2_SCOPES = "ZohoCRM.modules.ALL,ZohoCRM.settings.all,ZohoCRM.users.all";
    protected static string $N8N_OAUTH2_ACCESS_TYPE = "offline";
    protected static string $N8N_OAUTH2_AUTHENTICATION = "body";
    protected static string $N8N_OAUTH2_CLIENT_ID = "1000.EONLY4UHANWYT8JCHST1C020GF3I7E";
    protected static string $N8N_OAUTH2_CLIENT_SECRET = "596631fef76c6b87d65924695404b2b3c02ded6d64";
    protected static string $N8N_WORKFLOW_CREDENTIALS_NODE_TYPE = "n8n-nodes-base.zohoCrm";

    /**
     * @param ApplicationService $applicationService
     * @param ActivityService $activityService
     * @param WebhookService $webhookService
     * @param WebhookRepository $webhookRepository
     */
    public function __construct(ApplicationService $applicationService, ActivityService $activityService, WebhookService $webhookService, WebhookRepository $webhookRepository)
    {
        parent::__construct($applicationService, $activityService, $webhookService, $webhookRepository);
    }

    /**
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface|Throwable
     */
    public function setup(Application $application): bool
    {
        return parent::setupWorkflows($application);
    }

    static public function getAllowedSubscriptionTypes(Subscription $subscription): array
    {
        return ['training' => [], 'certifier' => []];
    }
}
