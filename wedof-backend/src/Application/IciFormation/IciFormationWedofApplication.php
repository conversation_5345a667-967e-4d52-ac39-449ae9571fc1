<?php
// src/Event/Application/ApplicationEventsSubscriber.php
namespace App\Application\IciFormation;

use App\Application\WedofApplication;
use App\Entity\Application;
use App\Entity\Session;
use App\Entity\Subscription;
use App\Entity\Training;
use App\Entity\TrainingAction;
use App\Library\utils\enums\SessionStates;
use App\Library\utils\enums\SubscriptionTrainingTypes;
use App\Library\utils\enums\TrainingActionStates;
use App\Library\utils\SimpleXMLElementTools;
use App\Library\utils\Tools;
use App\Service\ActivityService;
use App\Service\ApplicationService;
use App\Service\TrainingService;
use HttpRuntimeException;
use Normalizer;
use SimpleXMLElement;
use Throwable;

class IciFormationWedofApplication extends WedofApplication
{
    protected static string $APP_ID = "ici-formation";
    protected static array $SCOPES = [""]; // used when oauth2 app auth
    protected static array $METHODS = ['download']; // custom methods called from front
    private TrainingService $trainingService;

    /**
     * IciFormationWedofApplication constructor.
     * @param ApplicationService $applicationService
     * @param ActivityService $activityService
     * @param TrainingService $trainingService
     */
    public function __construct(ApplicationService $applicationService, ActivityService $activityService, TrainingService $trainingService)
    {
        parent::__construct($applicationService, $activityService);
        $this->trainingService = $trainingService;
    }

    /**
     * @param Application $application
     * @return bool[]
     * @throws HttpRuntimeException
     */
    public function download(Application $application): array
    {
        $arrayActiveTrainings = [];
        $mappingDomains = $application->getMetadata()['mapping'] ?? [];
        if (empty($mappingDomains)) {
            throw new HttpRuntimeException("no mapping found");
        }
        $activeTrainings = $this->trainingService->getPublishedTrainingsForOrganism($application->getOrganism());
        /** @var Training $activeTraining */
        foreach ($activeTrainings as $activeTraining) {
            /** @var TrainingAction $trainingAction */
            foreach ($activeTraining->getTrainingActions() as $trainingAction) {
                $formatTraining = $trainingAction->getRawData()['teachingMethod'] == 0 ? "presentiel" : ($trainingAction->getRawData()['teachingMethod'] == 1 ? "e-learning" : "distanciel");
                $idTraining = $activeTraining->getExternalId() . $formatTraining . $trainingAction->getRawData()['totalTvaTTC'];
                if ($trainingAction->getState() === TrainingActionStates::PUBLISHED()->getValue() && isset($mappingDomains[$activeTraining->getExternalId()])) {
                    if (empty($arrayActiveTrainings[$idTraining])) {
                        $allowedTags = '<div><p><ul><li><h1><h2><h3><h4><h5><h6>';
                        $dureeJTraining = $trainingAction->getRawData()['averageLearningTime'] / 7;
                        $isPrerequis = $trainingAction->getRawData()['haveSpecificConditionsAndPrerequisites'] === true ? $trainingAction->getRawData()['specificConditionsAndPrerequisites'] : '';
                        $prerequisWithoutTags = Tools::stripTagsContent($isPrerequis, $allowedTags);
                        $prerequisLengthNormalized = Normalizer::normalize(strlen($prerequisWithoutTags) > 245 ? substr($prerequisWithoutTags, 0, 245) . " ..." : $prerequisWithoutTags, Normalizer::FORM_C);
                        $correspondanceDiplome = "";
                        if ($activeTraining->getRawData()['endLevel'] === 1 || $activeTraining->getRawData()['endLevel'] === 2) {
                            $correspondanceDiplome = 0;
                        } else if ($activeTraining->getRawData()['endLevel'] === 3) {
                            $correspondanceDiplome = 1;
                        } else if ($activeTraining->getRawData()['endLevel'] === 4) {
                            $correspondanceDiplome = 2;
                        } else if ($activeTraining->getRawData()['endLevel'] === 5) {
                            $correspondanceDiplome = 3;
                        } else if ($activeTraining->getRawData()['endLevel'] === 6) {
                            $correspondanceDiplome = 4;
                        } else if ($activeTraining->getRawData()['endLevel'] === 7 || $activeTraining->getRawData()['endLevel'] === 8) {
                            $correspondanceDiplome = 5;
                        }

                        $accrocheToNormalize = isset($mappingDomains[$activeTraining->getExternalId()]["accroche"]) ? Tools::stripTagsContent($mappingDomains[$activeTraining->getExternalId()]["accroche"], $allowedTags) : '';
                        $accrocheNormalized = Normalizer::normalize($accrocheToNormalize, Normalizer::FORM_C);

                        $arrayActiveTrainings[$idTraining] = [
                            "domaine1" => $mappingDomains[$activeTraining->getExternalId()]['domaine1'],
                            "sous_domaine1" => $mappingDomains[$activeTraining->getExternalId()]["sous_domaine1"],
                            "format" => $formatTraining,
                            "ref" => substr($activeTraining->getExternalId(), 15),
                            "titre" => $activeTraining->getTitle(),
                            "eligibilite_cpf" => 1,
                            "code_cpf" => $activeTraining->getCertification()->getCpf(),
                            "sanction" => 3,
                            "diplome" => $correspondanceDiplome,
                            "accroche" => $accrocheNormalized,
                            "prerequis" => $prerequisLengthNormalized,
                            "public" => "Tout public",
                            "objectifs" => Normalizer::normalize(Tools::stripTagsContent($activeTraining->getRawData()['goal'], $allowedTags), Normalizer::FORM_C),
                            "programme" => Normalizer::normalize(Tools::stripTagsContent($activeTraining->getRawData()['content'], $allowedTags), Normalizer::FORM_C),
                            "dureeh" => $trainingAction->getRawData()['averageLearningTime'],
                            "dureej" => $dureeJTraining,
                            "observations" => Normalizer::normalize(Tools::stripTagsContent($trainingAction->getRawData()['teachingModality'], $allowedTags), Normalizer::FORM_C),
                            "prix" => $trainingAction->getRawData()['totalTvaTTC'],
                            "sessions" => []
                        ];
                    }
                    $arrayActiveTrainings[$idTraining]['sessions'] = array_merge($arrayActiveTrainings[$idTraining]['sessions'], $this->getSessions($trainingAction, $application));
                }
            }
        }

        $xmlIciFormation = new SimpleXMLElement('<formations xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"></formations>');

        foreach ($arrayActiveTrainings as $trainingActive) {
            if ($trainingActive['sessions']) {
                $formation = $xmlIciFormation->addChild('formation');
                $formation->addChild('domaine1', $trainingActive['domaine1']);
                $formation->addChild('sous_domaine1', $trainingActive['sous_domaine1']);
                $domaine2 = $formation->addChild('domaine2');
                $domaine2->addAttribute('fakeNamespace:xsi:nil', 'true');
                $sousdomaine2 = $formation->addChild('sous_domaine2');
                $sousdomaine2->addAttribute('fakeNamespace:xsi:nil', 'true');
                $formation->addChild('format', $trainingActive['format']);
                $formation->addChild('ref', $trainingActive['ref']);
                SimpleXMLElementTools::addCData('titre', $trainingActive['titre'], $formation);
                $formation->addChild('eligibilite_cpf', $trainingActive['eligibilite_cpf']);
                $formation->addChild('code_cpf', $trainingActive['code_cpf']);
                $formation->addChild('sanction', $trainingActive['sanction']);
                $formation->addChild('diplome', $trainingActive['diplome']);
                SimpleXMLElementTools::addCData('accroche', $trainingActive['accroche'], $formation);
                SimpleXMLElementTools::addCData('prerequis', $trainingActive['prerequis'], $formation);
                SimpleXMLElementTools::addCData('public', $trainingActive['public'], $formation);
                SimpleXMLElementTools::addCData('objectifs', $trainingActive['objectifs'], $formation);
                SimpleXMLElementTools::addCData('programme', $trainingActive['programme'], $formation);
                $formation->addChild('dureej', $trainingActive['dureej']);
                $formation->addChild('dureeh', $trainingActive['dureeh']);
                $formation->addChild('sequencage');
                $formation->addChild('participants');
                SimpleXMLElementTools::addCData('observations', $trainingActive['observations'], $formation);
                $formation->addChild('prix', $trainingActive['prix']);
                $sessions = $formation->addChild('sessions');
                /** @var Session $session */
                foreach ($trainingActive['sessions'] as $session) {
                    $sessionXml = $sessions->addChild('session');
                    SimpleXMLElementTools::addCData('intervenant', $session['intervenant'], $sessionXml);
                    $sessionXml->addChild('zipcode', $session['zipcode']);
                    SimpleXMLElementTools::addCData('realisation', $session['realisation'], $sessionXml);
                    SimpleXMLElementTools::addCData('acces', $session['acces'], $sessionXml);
                    $sessionXml->addChild('start_session', $session['start_session']);
                    $sessionXml->addChild('end_session', $session['end_session']);
                    SimpleXMLElementTools::addCData('msgpromo', $session['msgpromo'], $sessionXml);
                }
            }
        }
        Header('Content-type: text/xml');
        echo $xmlIciFormation->asXML();
        die();
    }

    /**
     * @param Application $application
     */
    public function enabled(Application $application): void
    {
        // TODO: Implement onEnable() method.
    }

    /**
     * @param Application $application
     */
    public function disabled(Application $application): void
    {
        // TODO: Implement onDisable() method.
    }

    /**
     * @param Application $application
     * @throws Throwable
     */
    public function onNewOAuth(Application $application): void
    {
        // TODO: Implement onNewOAuth() method.
    }

    public function onUpdateMetadata(Application $application, array $previousMetadata): void
    {
        // TODO: Implement onUpdateMetadata() method.
    }

    public function onRefreshOAuth(Application $application): void
    {
        // TODO: Implement onRefreshOAuth() method.
    }

    /**
     * @return array
     */
    public static function getApplicationSubscribedEvents(): array
    {
        return [];
    }

    public function beforeEnable(Application $application): void
    {
        // TODO: Implement beforeEnable() method.
    }

    public function beforeDisable(Application $application): void
    {
        // TODO: Implement beforeDisable() method.
    }

    static public function getAllowedSubscriptionTypes(Subscription $subscription): array
    {
        return [
            'training' => SubscriptionTrainingTypes::getPaidTrainingTypesForApps(),
            'certifier' => []
        ];
    }

    /**
     * @param TrainingAction $trainingAction
     * @param Application $application
     * @return array
     */
    private function getSessions(TrainingAction $trainingAction, Application $application): array
    {
        $sessionsResult = [];
        $defaultZipCode = $application->getOrganism()->getPostalCode() ? $application->getOrganism()->getPostalCode() : "00000";
        /**  @var Session $session */
        foreach ($trainingAction->getSessions() as $session) {
            if ($session->getStartDate() && $session->getStartDate()->getTimestamp() > time() && $session->getState() === SessionStates::PUBLISHED()->getValue()) {
                $sessionsResult[] = [
                    'intervenant' => '',
                    'zipcode' => !empty($trainingAction->getRawData()['trainingAddress']['zipCode']) ? $trainingAction->getRawData()['trainingAddress']['zipCode'] : $defaultZipCode,
                    'realisation' => '',
                    'acces' => '',
                    'start_session' => $session->getStartDate()->format('Y-m-d'),
                    'end_session' => $session->getEndDate()->format('Y-m-d'),
                    'msgpromo' => ''
                ];
            }
        }
        return $sessionsResult;
    }

    public function show(Application $application): void
    {
        // TODO: Implement onShow() method.
    }
}
