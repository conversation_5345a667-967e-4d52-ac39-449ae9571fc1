<?php

namespace App\Application\Onlineformapro\MessageHandler;

use App\Application\Onlineformapro\Message\PullCompletionRateRegistrationFoldersOnlineformapro;
use App\Application\Onlineformapro\Service\OnlineformaproApiService;
use App\MessageHandler\WithNextMessage;
use App\Service\OrganismService;
use Exception;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Throwable;

class PullCompletionRateRegistrationFoldersOnlineformaproHand<PERSON> extends WithNextMessage implements MessageHandlerInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    private OrganismService $organismService;

    private OnlineformaproApiService $onlineformaProApiService;

    public function __construct(OrganismService $organismService, MessageBusInterface $messageBus, OnlineformaproApiService $onlineformaproApiService)
    {
        parent::__construct($messageBus);
        $this->organismService = $organismService;
        $this->onlineformaProApiService = $onlineformaproApiService;
    }

    /**
     * @param PullCompletionRateRegistrationFoldersOnlineformapro $pullRegistrationFoldersOnlineformapro
     * @return void
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     * @throws Throwable
     */
    public function __invoke(PullCompletionRateRegistrationFoldersOnlineformapro $pullRegistrationFoldersOnlineformapro)
    {
        try {
            $organism = $this->organismService->getBySiret($pullRegistrationFoldersOnlineformapro->getSiret());
            $this->onlineformaProApiService->pullFromOnlineformapro($organism);
        } catch (Exception $e) {
            $this->logger->error($e);
        }
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
