<?php

namespace App\Application\Onlineformapro\Service;

use App\Entity\ApiToken;
use App\Entity\Connection;
use App\Entity\Organism;
use App\Exception\WedofConflictHttpException;
use App\Exception\WedofConnectionException;
use App\Library\utils\enums\DataProviders;
use App\Repository\EndPointStatusRepository;
use App\Service\ConfigService;
use App\Service\ConnectionService;
use App\Service\DataProviders\BaseApiService;
use DateInterval;
use Datetime;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use ErrorException;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Security\Core\Security;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Throwable;

class OnlineformaproApiService extends BaseApiService
{
    public function __construct(ConfigService $configService, ConnectionService $connectionService, RequestStack $requestStack, EndPointStatusRepository $endPointStatusRepository, EventDispatcherInterface $dispatcher, LoggerInterface $logger, Security $security)
    {
        parent::__construct(DataProviders::ONLINEFORMAPRO(), $configService, $connectionService, $requestStack, $endPointStatusRepository, $dispatcher, $logger, $security);
    }

    /**
     * @return int
     */
    public function getMaxAttemptsBeforeStop(): int
    {
        throw new WedofConflictHttpException("Pas d'implémentation de la fonction getMaxAttemptsBeforeStop dans OnlineformaproApiService");
    }

    /**
     * @param Connection $connection
     * @param bool $checkOrganismAccess
     * @return array
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function authenticate(Connection $connection, bool $checkOrganismAccess = true): array
    {
        $credentials = $connection->getCredentials();
        if (!isset($credentials['baseUrl'])) {
            throw new WedofConflictHttpException("L'url du serveur Onlineformapro n'est pas renseignée");
        } elseif (!isset($credentials['username'])) {
            throw new WedofConflictHttpException("Le username Onlineformapro n'est pas renseigné");
        }
        $result = $this->checkCredentials($credentials['baseUrl'], $credentials['username'], $credentials['password']);
        if (!$result) {
            throw new WedofConflictHttpException("Le login, le mot de passe ou l'url du serveur Onlineformapro est invalide");
        }
        $result = $this->checkApiKey($credentials['baseUrl'], $credentials['apiKey']);
        if (!$result) {
            throw new WedofConflictHttpException("L'api key Onlineformapro est invalide");
        }
        $connection->setCredentials($credentials);
        $this->connectionService->save($connection);
        return [
            "hasAccess" => true,
            "hasOrganismAccess" => true,
            "expiresOn" => (new DateTime('now'))->add(new DateInterval('P7D')),
            "refreshAt" => new DateTime('now'),
            "lastRefresh" => new DateTime('now'),
        ];
    }

    /**
     * @param string $baseUrl
     * @param string $apiKey
     * @return bool
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function checkApiKey(string $baseUrl, string $apiKey): bool
    {
        $options = [
            'authenticationHeaderName' => $_ENV['KASTORR_AUTH_HEADER'],
            'authenticationHeaderValue' => $_ENV['KASTORR_AUTH'],
            'parameters' => [
                'baseUrl' => $baseUrl,
                'apiKey' => $apiKey,
            ],
            'method' => 'POST',
        ];
        $data = $this->sendRequest($_ENV['KASTORR_BASE_URI'] . 'onlineformapro-check-apikey', $options);
        return !empty($data['success']) ?? false;
    }

    /**
     * @param string $baseUrl
     * @param string $username
     * @param string $password
     * @return bool
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    private function checkCredentials(string $baseUrl, string $username, string $password): bool
    {
        $options = [
            'authenticationHeaderName' => $_ENV['KASTORR_AUTH_HEADER'],
            'authenticationHeaderValue' => $_ENV['KASTORR_AUTH'],
            'parameters' => [
                'baseUrl' => $baseUrl,
                'username' => $username,
                'password' => $password
            ],
            'method' => 'POST',
        ];
        $data = $this->sendRequest($_ENV['KASTORR_BASE_URI'] . 'onlineformapro-check-credentials', $options);
        return !empty($data['success']) ?? false;
    }

    /**
     * @param Organism $organism
     * @param array|null $params
     * @return mixed
     */
    public function checkBeforeHabilitate(Organism $organism, array $params = null)
    {
        throw new WedofConflictHttpException("Pas d'implémentation de la fonction checkBeforeHabilitate dans OnlineformaproApiService");
    }

    /**
     * @param Organism $organism
     * @param Connection $connection
     * @param array $params
     * @return bool
     */
    public function habilitate(Organism $organism, Connection $connection, array $params): bool
    {
        throw new WedofConflictHttpException("Pas d'implémentation de la fonction habilitate dans OnlineformaproApiService");
    }

    /**
     * @param Connection $connection
     * @return string
     */
    public function getUsername(Connection $connection): string
    {
        return '';
    }

    /**
     * @return bool
     */
    public function requiresAuthentication(): bool
    {
        return true;
    }

    /**
     * @param Organism $organism
     * @return bool
     */
    public function setActiveOrganism(Organism $organism): bool
    {
        throw new WedofConflictHttpException("Pas d'implémentation de la fonction setActiveOrganism dans OnlineformaproApiService");
    }

    /**
     * @param array $options
     * @return array
     */
    public function setAuthentication(array $options = []): array
    {
        throw new WedofConflictHttpException("Pas d'implémentation de la fonction setAuthentication dans OnlineformaproApiService");
    }

    /**
     * @param Organism $organism
     * @return void
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function pullFromOnlineformapro(Organism $organism): void
    {
        $connection = $organism->getConnectionForDataProvider(DataProviders::ONLINEFORMAPRO());
        $data = $connection->getCredentials();
        /** @var ApiToken $token */
        $token = $organism->getOwnedBy()->getApiTokens()->first();
        $options = [
            'authenticationHeaderName' => $_ENV['KASTORR_AUTH_HEADER'],
            'authenticationHeaderValue' => $_ENV['KASTORR_AUTH'],
            'parameters' => [
                'baseUrl' => $data['baseUrl'],
                'username' => $data['username'],
                'password' => $data['password'],
                'apiKey' => $data['apiKey'],
                'wedof_apiKey' => $token->getToken(),
                'action' => 'refresh_completionRate'
            ],
            'method' => 'POST',
        ];
        $this->sendRequest($_ENV['KASTORR_BASE_URI'] . 'onlineformapro', $options);
    }
}
