<?php

namespace App\Application\Onlineformapro;

use App\Application\Onlineformapro\Service\OnlineformaproApiService;
use App\Application\WedofApplication;
use App\Entity\Application;
use App\Entity\Subscription;
use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofConflictHttpException;
use App\Exception\WedofConnectionException;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\enums\SubscriptionTrainingTypes;
use App\Library\utils\Tools;
use App\Service\ActivityService;
use App\Service\ApplicationService;
use App\Service\ConnectionService;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use ErrorException;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Throwable;

class OnlineformaproWedofApplication extends WedofApplication
{

    protected static string $APP_ID = "onlineformapro";

    protected static array $METHODS = ["checkCredentials", "checkState"];

    private OnlineformaproApiService $onlineformaproApiService;

    private ConnectionService $connectionService;

    /**
     * @param ApplicationService $applicationService
     * @param ActivityService $activityService
     * @param OnlineformaproApiService $onlineformaproApiService
     * @param ConnectionService $connectionService
     */
    public function __construct(ApplicationService $applicationService, ActivityService $activityService, OnlineformaproApiService $onlineformaproApiService, ConnectionService $connectionService)
    {
        parent::__construct($applicationService, $activityService);
        $this->onlineformaproApiService = $onlineformaproApiService;
        $this->connectionService = $connectionService;
    }

    /**
     * @param Subscription $subscription
     * @return array
     */
    static public function getAllowedSubscriptionTypes(Subscription $subscription): array
    {
        return [
            'training' => SubscriptionTrainingTypes::getPaidTrainingTypesForApps(),
            'certifier' => []
        ];
    }

    /**
     * @return array
     */
    static public function getApplicationSubscribedEvents(): array
    {
        return [];
    }

    /**
     * @param Application $application
     * @return void
     */
    public function enabled(Application $application): void
    {
        // TODO: Implement enabled() method.
    }

    /**
     * @param Application $application
     * @return void
     */
    public function disabled(Application $application): void
    {
        // TODO: Implement disabled() method.
    }

    /**
     * @param Application $application
     * @return void
     */
    public function beforeEnable(Application $application): void
    {
        // TODO: Implement beforeEnable() method.
    }

    /**
     * @param Application $application
     * @return void
     */
    public function beforeDisable(Application $application): void
    {
        // TODO: Implement beforeDisable() method.
    }

    /**
     * @param Application $application
     * @return void
     */
    public function onNewOAuth(Application $application): void
    {
        // TODO: Implement onNewOAuth() method.
    }

    /**
     * @param Application $application
     * @return void
     */
    public function onRefreshOAuth(Application $application): void
    {
        // TODO: Implement onRefreshOAuth() method.
    }

    /**
     * @param Application $application
     * @param array $data
     * @return bool
     * @throws TransportExceptionInterface
     */
    public function checkCredentials(Application $application, array $data): bool
    {
        if (!$data["isPasswordDirty"]) {
            $data["password"] = $application->getOrganism()->getConnectionForDataProvider(DataProviders::ONLINEFORMAPRO())->getCredentials()["password"];
        }
        return $this->setOnlineformaproConnection($application, $data);
    }

    /**
     * @param Application $application
     * @param array $data
     * @return bool
     * @throws TransportExceptionInterface
     */
    private function setOnlineformaproConnection(Application $application, array $data): bool
    {
        try {
            $data['baseUrl'] = $this->cleanUrl($data['baseUrl']);
            $access = $this->connectionService->authenticate($application->getOrganism(), DataProviders::ONLINEFORMAPRO(), $data);
            return $access['hasAccess'] == true;
        } catch (WedofConflictHttpException $e) {
            throw new WedofBadRequestHttpException($e->getMessage());
        }
    }

    /**
     * @param Application $application
     * @return bool
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     * @throws ORMException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws OptimisticLockException
     * @throws ErrorException
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws \Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface
     * @throws Throwable
     */
    public function checkState(Application $application): bool
    {
        if ($this->connectionService->hasAccess($application->getOrganism(), DataProviders::ONLINEFORMAPRO())) {
            $connection = $application->getOrganism()->getConnectionForDataProvider(DataProviders::ONLINEFORMAPRO());
            //enforce check apikey
            return $this->onlineformaproApiService->checkApiKey($connection->getCredentials()['baseUrl'], $connection->getCredentials()['apiKey']);
        } else {
            return false;
        }
    }

    public function show(Application $application): void
    {
    }

    /**
     * @param Application $application
     * @param array $previousMetadata
     * @return void
     */
    public function onUpdateMetadata(Application $application, array $previousMetadata): void
    {
        $metadata = $application->getMetadata();

        if (isset($metadata['onlineformaproMetadata']['apiKey'])) {
            unset($metadata['onlineformaproMetadata']['apiKey']);
        }
        if (isset($metadata['onlineformaproMetadata']['password'])) {
            unset($metadata['onlineformaproMetadata']['password']);
        }
        if (isset($metadata['onlineformaproMetadata']['baseUrl'])) {
            $metadata['onlineformaproMetadata']['baseUrl'] = $this->cleanUrl($metadata['onlineformaproMetadata']['baseUrl']);
        }
        $this->applicationService->updateMetadata($application, $metadata, false);
    }

    /**
     * @param string $url
     * @return string
     */
    private function cleanUrl(string $url): string
    {
        $url = str_replace("http:", "https:", $url);
        $url = !Tools::startsWith($url, 'https://') ? "https://" . $url : $url;
        return Tools::endsWith($url, '/') ? substr($url, 0, -1) : $url;
    }
}
