<?php

namespace App\Controller\api;

use FOS\RestBundle\Controller\AbstractFOSRestController;
use Symfony\Component\HttpFoundation\Request;

class AbstractWedofController extends AbstractFOSRestController
{
    protected function getData(): array
    {
        /** @var Request $request */
        $request = $this->container->get('request_stack')->getCurrentRequest();
        if (!empty($request->files->keys()) || $request->get('json')) {
            $body = [];
            if ($request->get('json')) {
                $body = json_decode($request->get('json'), true);
            }
            foreach ($request->files->keys() as $fileKey) {
                $body[$fileKey] = $request->files->get($fileKey);
            }
            return $body;
        } else {
            if (in_array($request->getContentType(), ['application/json', 'text/json', 'json'])) {
                return json_decode($request->getContent(), true);
            } else {
                return [];
            }
        }
    }
}