<?php
// src/Controller/api/ActivityController.php

namespace App\Controller\api;

use App\Entity\Activity;
use App\Entity\CertificationFolder;
use App\Entity\CertificationPartner;
use App\Entity\Proposal;
use App\Entity\RegistrationFolder;
use App\Entity\User;
use App\Exception\WedofAccessDeniedHttpException;
use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofNotFoundHttpException;
use App\Library\utils\enums\ActivityTypes;
use App\Library\utils\Tools;
use App\Repository\CertificationPartnerRepository;
use App\Security\Voter\ActivityVoter;
use App\Security\Voter\CertificationFolderVoter;
use App\Security\Voter\CertificationPartnerVoter;
use App\Security\Voter\ProposalVoter;
use App\Security\Voter\RegistrationFolderVoter;
use App\Service\ActivityService;
use App\Service\CertificationFolderService;
use App\Service\CertificationPartnerService;
use App\Service\ProposalService;
use App\Service\RegistrationFolderService;
use App\Service\UserService;
use DateTime;
use DateTimeZone;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Exception;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcherInterface;
use FOS\RestBundle\View\View;
use Knp\Component\Pager\PaginatorInterface;
use LogicException;
use Nelmio\ApiDocBundle\Annotation as ApiDoc;
use OpenApi\Annotations as OA;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validation;
use Throwable;


/**
 * Class ActivityController
 * @package App\Controller\api
 *
 * @OA\Tag(name="Activity")
 * @ApiDoc\Security(name="accessCode")
 */
class ActivityController extends AbstractFOSRestController
{
    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    /**
     * @Rest\Post("/api/activities/{entityClass}/{entityId}", requirements={"entityClass"="RegistrationFolder|CertificationFolder|CertificationPartner|Proposal"})
     * @IsGranted("ROLE_USER", message="not allowed")
     * @Rest\View(StatusCode = 201)
     *
     * @ApiDoc\Areas({"activities", "default"})
     * @OA\Post (
     *     summary="Créer une nouvelle activité",
     *     description="Permet de créer une nouvelle activité sur un dossier de formation ou de certification ou un partenariat ou une proposition. L'entityId correspond à l'externalId du dossier de formation ou de l'externalId du dossier de certification ou de l'id du partenariat ou du code de la proposition. Via OAuth2, cet appel nécessite le scope 'certificationfolder:write' ou le scope 'proposal:write' ou le scope 'certificationPartner:certifierEdit' ou le scope 'registrationFolder:write' "
     * )
     * @OA\Response(
     *     response=201,
     *     description="Un json contenant les informations de l'activité créée",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/Activity")
     *     )
     * )
     *
     * @OA\RequestBody(
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/ActivityCreateBody")
     *     )
     * )
     *
     * @param int|string $entityId
     * @param string $entityClass
     * @param ProposalService $proposalService
     * @param CertificationPartnerRepository $certificationPartnerRepository
     * @param RegistrationFolderService $registrationFolderService
     * @param CertificationFolderService $certificationFolderService
     * @param ActivityService $activityService
     * @param Request $request
     * @param UserService $userService
     * @return Activity | View
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function create($entityId, string $entityClass, ProposalService $proposalService, CertificationPartnerRepository $certificationPartnerRepository, RegistrationFolderService $registrationFolderService, CertificationFolderService $certificationFolderService, ActivityService $activityService, Request $request, UserService $userService)
    {
        $body = json_decode($request->getContent(), true);
        switch ($entityClass) {
            case RegistrationFolder::CLASSNAME :
                $entity = $registrationFolderService->getByExternalId($entityId);
                if (!$entity) {
                    throw new WedofNotFoundHttpException("Le dossier de formation " . $entityId . " n'a pas été trouvé.");
                }
                $organisms = [$entity->getOrganism()];
                if (!$this->isGranted(RegistrationFolderVoter::EDIT, $entity)) {
                    throw new WedofAccessDeniedHttpException("Erreur, vous n'êtes pas autorisé à créer une activité pour ce dossier de formation");
                }
                break;
            case CertificationFolder::CLASSNAME :
                $entity = $this->retrieveCertificationFolder($entityId, $certificationFolderService);
                if (!$entity) {
                    throw new WedofNotFoundHttpException("Le dossier de certification " . $entityId . " n'a pas été trouvé.");
                }
                $organisms = $entity->getPartner() ? [$entity->getPartner()] : [];
                $certifier = $entity->getCertifier();
                if ($certifier !== $entity->getPartner()) {
                    $organisms[] = $certifier;
                }
                if (!$this->isGranted(CertificationFolderVoter::EDIT, $entity)) {
                    throw new WedofAccessDeniedHttpException("Erreur, vous n'êtes pas autorisé à créer une activité pour ce dossier de certification");
                }
                break;
            case CertificationPartner::CLASSNAME :
                $entity = $certificationPartnerRepository->findOneBy(['id' => $entityId]);
                if (!$entity) {
                    throw new WedofNotFoundHttpException("Le partenariat " . $entityId . " n'a pas été trouvé.");
                }
                if (!$this->isGranted(CertificationPartnerVoter::CERTIFIER_EDIT, $entity)) {
                    throw new WedofAccessDeniedHttpException("Erreur, vous n'êtes pas autorisé à créer une activité pour ce partenariat");
                }
                $organisms = [$entity->getCertifier()];
                break;
            case Proposal::CLASSNAME :
                $entity = $proposalService->getByCode($entityId);
                if (!$entity) {
                    throw new WedofNotFoundHttpException("La proposition " . $entityId . " n'a pas été trouvée.");
                }
                if (!$this->isGranted(ProposalVoter::EDIT, $entity)) {
                    throw new WedofAccessDeniedHttpException("Erreur, vous n'êtes pas autorisé à créer une activité pour cette proposition");
                }
                $organisms = [$entity->getOrganism()];
                break;
            default:
                throw new LogicException("Entity class ne peut être que RegistrationFolder, CertificationFolder, CertificationPartner ou Proposal !");
        }

        if (!empty($body['dueDate'])) {
            if (strtotime($body['dueDate'])) {
                $body['dueDate'] = (new DateTime($body['dueDate']))->setTimezone(new DateTimeZone('GMT'));
            }
        }

        if (!empty($body['eventTime'])) {
            if (strtotime($body['eventTime'])) {
                $body['eventTime'] = (new DateTime($body['eventTime']))->setTimezone(new DateTimeZone('GMT'));
            }
        }

        if (!empty($body['eventEndTime'])) {
            if (strtotime($body['eventEndTime'])) {
                $body['eventEndTime'] = (new DateTime($body['eventEndTime']))->setTimezone(new DateTimeZone('GMT'));
                if (empty($body['eventTime'])) {
                    throw new WedofBadRequestHttpException("Erreur, vous devez renseigner une date de début si vous renseignez la date de fin de l'activité.");
                }
                if ($body['eventTime']->format('Y-m-d') > $body['eventEndTime']->format('Y-m-d')) {
                    throw new WedofBadRequestHttpException("Erreur, la date de fin de l'activité ne peut être antérieure à la date de début.");
                }
            }
        }

        if ($entityClass === CertificationPartner::CLASSNAME) {
            unset($body['qualiopiIndicators']);
        }

        $violations = $this->validateCreateBody($body);

        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }

        $user = null;
        if (!empty($body['userEmail'])) {
            /** @var User $user */
            $user = $userService->getByEmail($body['userEmail']);
            if (!$user) {
                throw new WedofBadRequestHttpException("Erreur, l'utilisateur associé à l'email " . $body['userEmail'] . " n'a pas été trouvé.");
            }
            if (!in_array($user->getMainOrganism(), $organisms)) {
                throw new WedofBadRequestHttpException("Erreur, l'email n'est pas associé à l'organisme.");
            }
        }

        $done = !empty($body['done']) ?? ($body['eventTime'] && $body['eventEndTime']);
        return $activityService->create($body, $user, $entity, true, $done);
    }

    /**
     * @Rest\Get("/api/activities/{entityClass}/{entityId}", requirements={"entityClass"="RegistrationFolder|CertificationFolder|CertificationPartner|Proposal"})
     * @Rest\QueryParam(name="qualiopi", requirements=@Assert\Choice({"false", "true"}), nullable=true, description="Permet de filtrer les activités selon le fait qu'elles soient en lien avec Qualiopi.")
     * @Rest\QueryParam(name="qualiopiIndicators", requirements=@Assert\Type("string"), nullable=true, description="Permet de filtrer les activités selon le ou les indicateur(s) Qualiopi. Valeurs possiblrs comprises entre 1 et 32. Il est possible de demander plusieurs critères en séparant chaque critère par une virgule, ex: '1,2,3'.")
     * @Rest\QueryParam(name="type", requirements=@Assert\Type("string"), default="all", description="Permet de filtrer les activités selon le type - par défaut tous les types sont retournés. Valeurs possibles : 'all', 'create', 'update', 'updateState', 'phone', 'email', 'meeting', 'chat', 'sms', 'examination', 'training', 'cdc', 'remark'. Il est possible de demander plusieurs types en séparant chaque type par une virgule, ex: 'create,update,cdc'. ")
     * @Rest\QueryParam(name="done", requirements=@Assert\Choice({"false", "true"}), nullable=true, description="Permet de filtrer les activités selon leur état 'fait' (true) ou 'à faire' (false).")
     * @Rest\QueryParam(name="format", requirements=@Assert\Type("string"), default="json", description="Permet d'obtenir une liste des activités au format json ou csv. Valeurs possibles : 'json', 'csv'")
     * @Rest\QueryParam(name="csvColumns", requirements=@Assert\Type("string"), nullable=true, description="Permet de choisir les colonnes souhaitées pour l'export des activités au format csv. Valeurs possibles : 'TITRE', 'DESCRIPTION', 'TYPE', 'ORIGINE', 'LIEN', 'CHAMP_MODIFIE', 'ANCIENNE_VALEUR',  'NOUVELLE_VALEUR', 'DATE_DEBUT', 'DATE_FIN', 'DUREE (JOUR)', 'INDICATEURS_QUALIOPI', 'UTILISATEUR', 'DATE_ÉCHEANCE'. ")
     * @Rest\QueryParam(name="limit", requirements="\d+", default="100", description="Nombre d'éléments retourné par requête - par défaut 100.")
     * @Rest\QueryParam(name="page", requirements="\d+", default="1", description="Numéro de page de la requête - par défaut la première.")
     * @IsGranted("ROLE_USER", message="not allowed")
     * @ApiDoc\Areas({"activities", "default"})
     * @OA\Get (
     *     summary="Liste les activités d'un dossier de formation ou de certification ou un partenariat ou une proposition.",
     *     description="Récupère l'ensemble des activités liées à un dossier. L'entityId correspond à l'externalId du dossier de formation ou de l'externalId du dossier de certification ou de l'id d'un partenariat ou du code d'une proposition."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un tableau des activités au format JSON ou CSV selon le paramètre 'format'",
     *     @OA\JsonContent(
     *          type="array",
     *          @OA\Items(ref="#/components/schemas/Activity")
     *     )
     * )
     *
     * @param PaginatorInterface $paginator
     * @param int|string $entityId
     * @param string $entityClass
     * @param ProposalService $proposalService
     * @param CertificationPartnerRepository $certificationPartnerRepository
     * @param RegistrationFolderService $registrationFolderService
     * @param CertificationFolderService $certificationFolderService
     * @param ActivityService $activityService
     * @param ParamFetcherInterface $paramFetcher
     * @return Response
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function list(PaginatorInterface $paginator, $entityId, string $entityClass, ProposalService $proposalService, CertificationPartnerRepository $certificationPartnerRepository, RegistrationFolderService $registrationFolderService, CertificationFolderService $certificationFolderService, ActivityService $activityService, ParamFetcherInterface $paramFetcher, Request $request, EntityManagerInterface $entityManager): Response
    {
        if ($entityClass === RegistrationFolder::CLASSNAME) {
            $entity = $registrationFolderService->getByExternalId($entityId);
            if (!$entity) {
                throw new WedofNotFoundHttpException("Le dossier de formation " . $entityId . " n'a pas été trouvé.");
            }
            if (!$this->isGranted(RegistrationFolderVoter::OWNER_VIEW, $entity)) {
                throw new WedofAccessDeniedHttpException("Erreur, vous n'êtes pas autorisé à lister les activités pour ce dossier de formation");
            }
        } else if ($entityClass === CertificationFolder::CLASSNAME) {
            $entity = $this->retrieveCertificationFolder($entityId, $certificationFolderService);
            if (!$entity) {
                throw new WedofNotFoundHttpException("Le dossier de certification " . $entityId . " n'a pas été trouvé.");
            }
            $entityId = $entity->getId(); // hack because entityId is based on "id" on BDD, we cannot list using externalId as entityId otherwise we will need to do a big migration
            if (!$this->isGranted(CertificationFolderVoter::VIEW, $entity)) {
                throw new WedofAccessDeniedHttpException("Erreur, vous n'êtes pas autorisé à lister les activités pour ce dossier de certification");
            }
        } else if ($entityClass === CertificationPartner::CLASSNAME) {
            $entity = $certificationPartnerRepository->findOneBy(['id' => $entityId]);
            if (!$entity) {
                throw new WedofNotFoundHttpException("Le partenariat " . $entityId . " n'a pas été trouvé.");
            }
            if (!$this->isGranted(CertificationPartnerVoter::VIEW, $entity)) {
                throw new WedofAccessDeniedHttpException("Erreur, vous n'êtes pas autorisé à lister les activités pour ce partenariat");
            }
        } else if ($entityClass === Proposal::CLASSNAME) {
            $entity = $proposalService->getByCode($entityId);
            if (!$entity) {
                throw new WedofNotFoundHttpException("La proposition " . $entityId . " n'a pas été trouvée.");
            }
            if (!$this->isGranted(ProposalVoter::VIEW, $entity)) {
                throw new WedofAccessDeniedHttpException("Erreur, vous n'êtes pas autorisé à lister les activités pour cette proposition");
            }
        } else {
            throw new LogicException("Entity class ne peut être que RegistrationFolder, CertificationFolder ou CertificationPartner !");
        }
        $parameters = $paramFetcher->all(true);
        if (isset($parameters['type'])) {
            $parameters['type'] = explode(",", $parameters['type']);
            foreach ($parameters['type'] as $type) {
                if (!in_array($type, ActivityTypes::valuesTypes())) {
                    throw new WedofBadRequestHttpException("Erreur sur les valeurs renvoyées 'type', elles doivent être : " . join(",", ActivityTypes::valuesTypes()) . ".");
                }
            }
        }
        if ($parameters['format'] === 'csv' || str_contains($request->headers->get('Accept'), 'csv')) {
            set_time_limit(300);
            $availableColumns = ['TITRE', 'DESCRIPTION', 'TYPE', 'LIEN', 'CHAMP_MODIFIÉ', 'ANCIENNE_VALEUR', 'NOUVELLE_VALEUR', 'DATE_DEBUT', 'DATE_FIN', 'DUREE (JOUR)', 'INDICATEURS_QUALIOPI', 'UTILISATEUR', 'DATE_ÉCHEANCE'];

            $page = 1;
            $limit = 100;
            $tempFile = null;
            $entityManager->getConnection()->getConfiguration()->setSQLLogger();
            $data = $paginator->paginate($activityService->listByEntityReturnQueryBuilder($entityId, $entityClass, $entity, $parameters), $page, $limit);
            $tempFile = Tools::convertDataToCSVFile($data, $availableColumns, $parameters['csvColumns'] ?? null, $tempFile);
            $page++;
            $nbIterations = intdiv($data->getTotalItemCount(), $limit) + 1;
            $entityManager->clear();

            while ($page <= $nbIterations) {
                $data = $paginator->paginate($activityService->listByEntityReturnQueryBuilder($entityId, $entityClass, $entity, $parameters), $page, $limit);
                $tempFile = Tools::convertDataToCSVFile($data, $availableColumns, $parameters['csvColumns'] ?? null, $tempFile);
                $page++;
                $entityManager->clear();
            }
            return Tools::getCsvResponse($tempFile, 'activités');
        } else {
            $data = $paginator->paginate($activityService->listByEntityReturnQueryBuilder($entityId, $entityClass, $entity, $parameters), intval($parameters['page']), intval($parameters['limit']));
            $view = $this->view($data->getItems(), 200);
            $view->setHeader("x-total-count", $data->getTotalItemCount());
            $view->setHeader("x-current-page", $data->getCurrentPageNumber());
            $view->setHeader("x-item-per-page", $data->getItemNumberPerPage());
            return $this->handleView($view);
        }
    }

    /**
     * @Rest\Get("/api/activities/me")
     * @IsGranted("ROLE_USER", message="not allowed")
     * @Rest\QueryParam(name="limit", requirements="\d+", default="100", description="Nombre d'éléments retourné par requête - par défaut 100.")
     * @Rest\QueryParam(name="page", requirements="\d+", default="1", description="Numéro de page de la requête - par défaut la première.")
     *
     * @param PaginatorInterface $paginator
     * @param ActivityService $activityService
     * @param ParamFetcherInterface $paramFetcher
     * @return Response
     */
    public function listByUser(PaginatorInterface $paginator, ActivityService $activityService, ParamFetcherInterface $paramFetcher): Response
    {
        /** @var User $user */
        $user = $this->getUser();
        $parameters = $paramFetcher->all(true);
        $data = $paginator->paginate($activityService->listByUser($user), intval($parameters['page']), intval($parameters['limit']));
        $view = $this->view($data->getItems(), 200);
        $view->setHeader("x-total-count", $data->getTotalItemCount());
        $view->setHeader("x-current-page", $data->getCurrentPageNumber());
        $view->setHeader("x-item-per-page", $data->getItemNumberPerPage());
        return $this->handleView($view);
    }

    /**
     * @Rest\Put("/api/activities/{id}")
     * @Rest\View(StatusCode = 200)
     * @IsGranted("ROLE_USER", message="not allowed")
     *
     * @ApiDoc\Areas ({"activities","default"})
     *
     * @OA\Put (
     *     summary="Mettre à jour une activité",
     *     description="Permet de mettre à jour une activité sur un dossier de formation ou de certification ou un partenariat ou une proposition."
     * )
     * @OA\Response(
     *      response=200,
     *      description="Un json contenant les informations de l'activité mise à jour",
     *      @OA\MediaType(mediaType="application/json",
     *          @OA\Schema(ref="#/components/schemas/Activity")
     *      )
     *  )
     *
     * @OA\RequestBody(
     *      @OA\MediaType(mediaType="application/json",
     *          @OA\Schema(ref="#/components/schemas/ActivityUpdateBody")
     *      )
     *  )
     *
     * @param Activity $activity
     * @param Request $request
     * @param UserService $userService
     * @param ActivityService $activityService
     * @param ProposalService $proposalService
     * @param CertificationPartnerService $certificationPartnerService
     * @param CertificationFolderService $certificationFolderService
     * @param RegistrationFolderService $registrationFolderService
     * @return Activity | View
     * @throws Exception
     */
    public function update(Activity $activity, Request $request, UserService $userService, ActivityService $activityService, ProposalService $proposalService, CertificationPartnerService $certificationPartnerService, CertificationFolderService $certificationFolderService, RegistrationFolderService $registrationFolderService)
    {
        $body = json_decode($request->getContent(), true);
        $typeCondition = ['updateState', 'create', 'update', 'file', 'progress', 'cdc'];


        if (in_array(strtolower($activity->getType()), $typeCondition)) {
            throw new WedofAccessDeniedHttpException("Erreur, vous n'êtes pas autorisé à modifier des activités créées automatiquement");
        }

        if (!$this->isGranted(ActivityVoter::EDIT, $activity)) {
            throw new WedofAccessDeniedHttpException("Erreur, vous n'êtes pas autorisé à modifier cette activité ou tâche, contactez le responsable rattaché");
        }
        //Needed in case user change
        switch ($activity->getEntityClass()) {
            case RegistrationFolder::CLASSNAME :
                $entity = $registrationFolderService->getByExternalId($activity->getEntityId());
                if (!$entity) {
                    throw new WedofNotFoundHttpException("Le dossier de formation " . $activity->getEntityId() . " n'a pas été trouvé.");
                }
                $organisms = [$entity->getOrganism()];
                break;
            case CertificationFolder::CLASSNAME :
                $entity = $this->retrieveCertificationFolder($activity->getEntityId(), $certificationFolderService);
                if (!$entity) {
                    throw new WedofNotFoundHttpException("Le dossier de certification " . $activity->getEntityId() . " n'a pas été trouvé.");
                }
                $organisms = [$entity->getPartner()];
                $certifier = $entity->getCertifier();
                if ($certifier !== $entity->getPartner()) {
                    $organisms[] = $certifier;
                }
                break;
            case CertificationPartner::CLASSNAME :
                $entity = $certificationPartnerService->getByEntityId($activity->getEntityId());
                if (!$entity) {
                    throw new WedofNotFoundHttpException("Le partenariat " . $activity->getEntityId() . " n'a pas été trouvé.");
                }
                $organisms = [$entity->getCertifier()];
                break;
            case Proposal::CLASSNAME :
                $entity = $proposalService->getByCode($activity->getEntityId());
                if (!$entity) {
                    throw new WedofNotFoundHttpException("La proposition " . $activity->getEntityId() . " n'a pas été trouvée.");
                }
                $organisms = [$entity->getOrganism()];
                break;
            default:
                throw new LogicException("Entity class ne peut être que RegistrationFolder, CertificationFolder, CertificationPartner ou Proposal !");
        }

        $eventTimeDate = $activity->getEventTime();
        $eventEndTimeDate = $activity->getEventEndTime();

        if (!empty($body['dueDate'])) {
            if (strtotime($body['dueDate'])) {
                $body['dueDate'] = (new DateTime($body['dueDate']))->setTimezone(new DateTimeZone('GMT'));
            }
        }

        if (!empty($body['eventTime'])) {
            if (strtotime($body['eventTime'])) {
                $body['eventTime'] = (new DateTime($body['eventTime']))->setTimezone(new DateTimeZone('GMT'));
                $eventTimeDate = $body['eventTime'];
            }
        }

        if (!empty($body['eventEndTime'])) {
            if (strtotime($body['eventEndTime'])) {
                $body['eventEndTime'] = (new DateTime($body['eventEndTime']))->setTimezone(new DateTimeZone('GMT'));
                if (empty($body['eventTime'])) {
                    throw new WedofBadRequestHttpException("Erreur, vous devez renseigner une date de début si vous renseignez la date de fin de l'activité.");
                }
                if ($body['eventTime']->format('Y-m-d') > $body['eventEndTime']->format('Y-m-d')) {
                    throw new WedofBadRequestHttpException("Erreur, la date de fin de l'activité ne peut être antérieure à la date de début.");
                }
            }
        }

        if (!$eventTimeDate && $eventEndTimeDate) {
            throw new WedofBadRequestHttpException("Erreur, vous devez renseigner une date de début si vous renseignez la date de fin de l'activité.");
        }

        if ($eventTimeDate && $eventEndTimeDate && $eventTimeDate->format('Y-m-d') > $eventEndTimeDate->format('Y-m-d')) {
            throw new WedofBadRequestHttpException("Erreur, la date de fin de l'activité ne peut être antérieure à la date de début.");
        }

        if ($activity->getEntityClass() === CertificationPartner::CLASSNAME) {
            unset($body['qualiopiIndicators']);
        }

        $violations = $this->validateUpdateBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }
        $user = $activity->getUser();
        if (!empty($body['userEmail'])) {
            /** @var User $user */
            $user = $userService->getByEmail($body['userEmail']);
            if (!$user) {
                throw new WedofBadRequestHttpException("Erreur, l'utilisateur associé à l'email " . $body['userEmail'] . " n'a pas été trouvé.");
            }
            if (!in_array($user->getMainOrganism(), $organisms)) {
                throw new WedofBadRequestHttpException("Erreur, l'email n'est pas associé à l'organisme.");
            }
        }
        return $activityService->update($activity, $body, $user);
    }

    /**
     * @Rest\Delete("/api/activities/{id}")
     * @IsGranted(ActivityVoter::EDIT, subject="activity", message="not allowed")
     * @Rest\View(StatusCode = 204)
     *
     * @ApiDoc\Areas({"activities", "default"})
     * @OA\Delete (
     *     summary="Supprimer une activité / tâche",
     *     description="Supprimer une activité / tâche."
     * )
     * @OA\Response(
     *     response=204,
     *     description="Aucun contenu retourné."
     * )
     *
     * @param Activity $activity
     * @param ActivityService $activityService
     */
    public function delete(Activity $activity, ActivityService $activityService)
    {
        $activityService->delete($activity);
    }

    /**
     * @Rest\Post("/api/activities/{id}/move")
     * @Rest\View(StatusCode = 200)
     * @IsGranted(ActivityVoter::EDIT, subject="activity", message="not allowed")
     *
     * @ApiDoc\Areas ({"activities","default"})
     *
     * @OA\Post (
     *     summary="Déplacer une activité/tâche",
     *     description="Permet de déplacer une activité/tâche vers un dossier de formation ou de certification."
     * )
     * @OA\Response(
     *      response=200,
     *      description="Un json contenant les informations de l'activité mise à jour",
     *      @OA\MediaType(mediaType="application/json",
     *          @OA\Schema(ref="#/components/schemas/Activity")
     *      )
     *  )
     *
     * @OA\RequestBody(
     *      @OA\MediaType(mediaType="application/json",
     *          @OA\Schema(ref="#/components/schemas/ActivityMoveBody")
     *      )
     *  )
     *
     * @param Activity $activity
     * @param Request $request
     * @param ActivityService $activityService
     * @param CertificationFolderService $certificationFolderService
     * @param ProposalService $proposalService
     * @param CertificationPartnerService $certificationPartnerService
     * @param RegistrationFolderService $registrationFolderService
     * @return Activity | View
     */
    public function move(Activity $activity,
                         Request $request,
                         ActivityService $activityService,
                         CertificationFolderService $certificationFolderService,
                         ProposalService $proposalService,
                         CertificationPartnerService $certificationPartnerService,
                         RegistrationFolderService $registrationFolderService)
    {
        $body = json_decode($request->getContent(), true);

        $violations = $this->validateMoveBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }

        switch ($body['entityClass']) {
            case RegistrationFolder::CLASSNAME :
                $entity = $registrationFolderService->getByExternalId($body['entityId']);
                if (!$entity) {
                    throw new WedofNotFoundHttpException("Le dossier de formation " . $body['entityId'] . " n'a pas été trouvé.");
                }
                if (!$this->isGranted(RegistrationFolderVoter::EDIT, $entity)) {
                    throw new WedofAccessDeniedHttpException("Erreur, vous n'êtes pas autorisé à agir sur ce dossier de formation");
                }
                break;
            case CertificationFolder::CLASSNAME :
                $entity = $this->retrieveCertificationFolder($body['entityId'], $certificationFolderService);
                if (!$entity) {
                    throw new WedofNotFoundHttpException("Le dossier de certification " . $body['entityId'] . " n'a pas été trouvé.");
                }
                if (!$this->isGranted(CertificationFolderVoter::EDIT, $entity)) {
                    throw new WedofAccessDeniedHttpException("Erreur, vous n'êtes pas autorisé à agir sur ce dossier de certification");
                }
                break;
            case CertificationPartner::CLASSNAME :
                $entity = $certificationPartnerService->getByEntityId($body['entityId']);
                if (!$entity) {
                    throw new WedofNotFoundHttpException("Le partenariat " . $activity->getEntityId() . " n'a pas été trouvé.");
                }
                if (!$this->isGranted(CertificationPartnerVoter::EDIT, $entity)) {
                    throw new WedofAccessDeniedHttpException("Erreur, vous n'êtes pas autorisé à agir sur ce partenariat");
                }
                break;
            case Proposal::CLASSNAME :
                $entity = $proposalService->getByCode($body['entityId']);
                if (!$entity) {
                    throw new WedofNotFoundHttpException("La proposition " . $activity->getEntityId() . " n'a pas été trouvée.");
                }
                if (!$this->isGranted(ProposalVoter::EDIT, $entity)) {
                    throw new WedofAccessDeniedHttpException("Erreur, vous n'êtes pas autorisé à agir sur cette proposition");
                }
                break;
            default:
                throw new LogicException("Ce code ne devrait pas être atteint à cause de la validation du body");
        }

        return $activityService->move($activity, $entity);
    }

    //-------------------
    // METHODES PRIVEES
    //-------------------

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateCreateBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'type' => new Assert\Required([
                new Assert\Choice(ActivityTypes::createOrUpdateValuesTypeToString())
            ]),
            'eventTime' => new Assert\Optional([new Assert\Type('datetime')]),
            'eventEndTime' => new Assert\Optional([new Assert\Type('datetime')]),
            'origin' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 255])]),
            'link' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 1000])]),
            'title' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 255])]),
            'description' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 1000])]),
            'qualiopi' => new Assert\Optional([new Assert\Type('boolean')]), // Not used, legacy to avoid breaking API as extra fields not allowed
            'qualiopiIndicators' => new Assert\Optional([
                new Assert\Type('array'),
                new Assert\All([
                    new Assert\Type('integer'),
                    new Assert\Range(['min' => 1, 'max' => 32])
                ])
            ]),
            'userEmail' => new Assert\Optional([new Assert\Email()]),
            'dueDate' => new Assert\Optional([new Assert\Type('datetime')]),
            'done' => new Assert\Optional([new Assert\Type('bool')]),
        ]);

        return $validator->validate($body, $constraints);
    }

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateUpdateBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'type' => new Assert\Optional([
                new Assert\Choice(ActivityTypes::createOrUpdateValuesTypeToString())
            ]),
            'eventTime' => new Assert\Optional([new Assert\Type('datetime')]),
            'eventEndTime' => new Assert\Optional([new Assert\Type('datetime')]),
            'origin' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 255])]),
            'link' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 1000])]),
            'title' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 255])]),
            'description' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 1000])]),
            'qualiopi' => new Assert\Optional([new Assert\Type('boolean')]), // Not used, legacy to avoid breaking API as extra fields not allowed
            'qualiopiIndicators' => new Assert\Optional([
                new Assert\Type('array'),
                new Assert\All([
                    new Assert\Type('integer'),
                    new Assert\Range(['min' => 1, 'max' => 32])
                ])
            ]),
            'userEmail' => new Assert\Optional([new Assert\Email()]),
            'dueDate' => new Assert\Optional([new Assert\Type('datetime')]),
            'done' => new Assert\Optional([new Assert\Type('bool')])
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateMoveBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            "entityClass" => new Assert\Required([new Assert\Choice([RegistrationFolder::CLASSNAME, CertificationFolder::CLASSNAME, CertificationPartner::CLASSNAME, Proposal::CLASSNAME])]),
            "entityId" => new Assert\Required([new Assert\Type('string')])
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param $externalId
     * @param CertificationFolderService $certificationFolderService
     * @return CertificationFolder|null
     */
    private function retrieveCertificationFolder($externalId, CertificationFolderService $certificationFolderService): ?CertificationFolder
    {
        if (is_numeric($externalId)) {
            $certificationFolder = $certificationFolderService->getById($externalId);
        } else {
            $certificationFolder = $certificationFolderService->getByExternalId($externalId);
        }
        return $certificationFolder;
    }

}
