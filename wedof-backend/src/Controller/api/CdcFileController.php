<?php
// src/Controller/api/CdcFileController.php

namespace App\Controller\api;

use App\Entity\CdcFile;
use App\Entity\Organism;
use App\Entity\User;
use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofConnectionException;
use App\Exception\WedofSubscriptionException;
use App\Library\utils\enums\CdcFileStates;
use App\Library\utils\enums\CertificationFolderCdcStates;
use App\Library\utils\enums\CertificationFolderStates;
use App\Library\utils\SimpleXMLElementTools;
use App\Library\utils\Tools;
use App\Repository\CertificationFolderRepository;
use App\Repository\CertificationRepository;
use App\Security\Voter\CdcFileVoter;
use App\Service\CdcFileService;
use App\Service\CdcXMLService;
use App\Service\CertificationFolderService;
use App\Service\DataProviders\CdcCertifiersApiService;
use App\Service\OrganismService;
use DateTime;
use DateTimeZone;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use ErrorException;
use Exception;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcherInterface;
use FOS\RestBundle\View\View;
use Knp\Component\Pager\PaginatorInterface;
use Nelmio\ApiDocBundle\Annotation as ApiDoc;
use OpenApi\Annotations as OA;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use SimpleXMLElement;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Uid\Uuid;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validation;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Throwable;

/**
 * Class CdcFileController
 * @package App\Controller\api
 *
 * @OA\Tag(name="CdcFile")
 * @ApiDoc\Security(name="accessCode")
 */
class CdcFileController extends AbstractWedofController
{
    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    /**
     * @Rest\Get("/api/cdcFiles")
     * @IsGranted("ROLE_USER", message="not allowed")
     * @Rest\QueryParam(name="state", requirements=@Assert\Type("string"), default="all", description="Permet de n'obtenir que les fichiers dans l'état considéré - par défaut tous les états sont retournés. Valeurs possibles : 'all', 'exported', 'aborted', 'processed'. Il est possible de demander plusieurs états en séparant chaque état par une virgule, ex : 'exported,processed'.")
     * @Rest\QueryParam(name="name", requirements=@Assert\Type("string"), nullable=true, description="Permet de n'obtenir que les fichiers avec le nom correspondant.")
     * @Rest\QueryParam(name="page", requirements="\d+", default="1", description="Numéro de page de la requête - par défaut la première.")
     * @Rest\QueryParam(name="limit", requirements="\d+", default="100", description="Nombre d'éléments retourné par requête - par défaut 100.")
     * @Rest\QueryParam(name="sort", requirements=@Assert\Choice({"id", "name", "createdOn"}), default="createdOn", description="Tri les résultats sur un critère. Valeurs possibles: 'id', 'name', 'createdOn' - par défaut 'createdOn'.")
     * @Rest\QueryParam(name="order", requirements=@Assert\Choice({"asc", "desc"}), default="desc", description="Tri les résultats par ordre ascendant ou descendant - par défaut descendant.")
     *
     * @param ParamFetcherInterface $paramFetcher
     * @param PaginatorInterface $paginator
     * @param CdcFileService $cdcFileService
     * @return Response
     * @throws WedofSubscriptionException
     */
    public function list(ParamFetcherInterface $paramFetcher, PaginatorInterface $paginator, CdcFileService $cdcFileService): Response
    {
        /* @var $user User */
        $user = $this->getUser();
        $subscription = $user->getMainOrganism()->getSubscription();
        if (!$subscription->isAllowCertifiers()) {
            throw new WedofSubscriptionException("Erreur, cette fonctionnalité est limitée aux abonnements Certificateur");
        }
        $parameters = $paramFetcher->all(true);

        if (isset($parameters['state'])) {
            $parameters['state'] = explode(",", $parameters['state']);
            foreach ($parameters['state'] as $state) {
                if (!in_array($state, CdcFileStates::valuesTypes())) {
                    throw new WedofBadRequestHttpException("Erreur sur les valeurs renvoyées 'state', elles doivent être : " . join(",", CdcFileStates::valuesTypes()) . ".");
                }
            }
        }

        $data = $paginator->paginate($cdcFileService->listReturnQueryBuilder($parameters, $user->getMainOrganism()), intval($parameters['page']), intval($parameters['limit']));
        $view = $this->view($data->getItems(), 200);
        $view->setHeader("x-total-count", $data->getTotalItemCount());
        $view->setHeader("x-current-page", $data->getCurrentPageNumber());
        $view->setHeader("x-item-per-page", $data->getItemNumberPerPage());
        return $this->handleView($view);
    }

    /**
     * @Rest\Get("/api/cdcFiles/export/{siret}/{certificationId}")
     * @IsGranted("ROLE_USER", message="not allowed")
     * @Rest\View(StatusCode = 200)
     * @param string $siret
     * @param int $certificationId
     * @param OrganismService $organismService
     * @param CertificationRepository $certificationRepository
     * @param CertificationFolderService $certificationFolderService
     * @return Response
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countCertificationFoldersToExport(string $siret, int $certificationId, OrganismService $organismService, CertificationRepository $certificationRepository, CertificationFolderService $certificationFolderService): Response
    {
        $organism = $organismService->getBySiret($siret); // TODO check / fix security issue: I think only the current organism should be able to do that
        if (!$organism) {
            throw new WedofBadRequestHttpException("Erreur, l'organisme avec le siret " . $siret . " n'existe pas.");
        }
        $certification = $certificationRepository->findOneBy(['id' => $certificationId]);
        if (!$certification) {
            throw new WedofBadRequestHttpException("Erreur, la certification avec l'id " . $certificationId . " n'existe pas.");
        }
        if (!$certification->getCertifiers()->contains($organism)) {
            throw new WedofBadRequestHttpException("Erreur, vous n'avez pas accès à la certification associée à l'id " . $certificationId . " .");
        }
        if ($organism->getSubscription() && !$organism->getSubscription()->isAllowCertifiers()) {
            throw new WedofBadRequestHttpException("Erreur, sur l'abonnement de l'organisme " . $organism->getName() . " .");
        }
        $parameters = ['state' => CertificationFolderStates::SUCCESS()->getValue(), 'cdcCompliant' => true, 'cdcToExport' => true, 'cdcState' => [CertificationFolderCdcStates::NOT_EXPORTED()->getValue(), CertificationFolderCdcStates::PROCESSED_KO()->getValue()]];
        $count = $certificationFolderService->countForOrganismByCertificationForCdc($organism, $certification, $parameters);
        $view = $this->view($count, 200);
        return $this->handleView($view);
    }

    /**
     * @Rest\Get("/api/cdcFiles/errorStats")
     * @IsGranted("ROLE_USER", message="not allowed")
     * @Rest\View(StatusCode = 200)
     * @param CertificationFolderRepository $certificationFolderRepository
     * @return Response
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws WedofSubscriptionException
     */
    public function errorStats(CertificationFolderRepository $certificationFolderRepository): Response
    {
        /* @var $user User */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();
        if (!$organism->getSubscription()->isAllowCertifiers()) {
            throw new WedofSubscriptionException("Erreur, cette fonctionnalité est limitée aux abonnements Certificateur");
        }
        $wedofCdcErrorCount = $certificationFolderRepository->computeCdcErrorCount($organism, false);
        $wedofCdcTotalCount = $certificationFolderRepository->computeCdcTotalCount($organism, false);
        $wedofErrorRate = $wedofCdcTotalCount != 0 ? $wedofCdcErrorCount / $wedofCdcTotalCount * 100 : 0;
        $externalCdcErrorCount = $certificationFolderRepository->computeCdcErrorCount($organism, true);
        $externalCdcTotalCount = $certificationFolderRepository->computeCdcTotalCount($organism, true);
        $externalErrorRate = $externalCdcTotalCount != 0 ? $externalCdcErrorCount / $externalCdcTotalCount * 100 : 0;
        $data = [
            'wedof' => [
                'errorCount' => $wedofCdcErrorCount,
                'totalCount' => $wedofCdcTotalCount,
                'errorRate' => $wedofErrorRate
            ],
            'external' => [
                'errorCount' => $externalCdcErrorCount,
                'totalCount' => $externalCdcTotalCount,
                'errorRate' => $externalErrorRate
            ]
        ];
        $view = $this->view($data, 200);
        return $this->handleView($view);
    }

    /**
     * @Rest\Post("/api/cdcFiles")
     * @Rest\View(StatusCode = 501)
     *
     * @return WedofBadRequestHttpException
     */
    public function create(): WedofBadRequestHttpException
    {
        throw new WedofBadRequestHttpException("Méthode non supportée - Un nouveau fichier XML lié à l'accrochage ne peut être créé.");
    }

    /**
     * @Rest\Put("/api/cdcFiles/{id}")
     * @Rest\View(StatusCode = 200)
     * @param CdcFile $cdcFile
     * @param Request $request
     * @param CdcFileService $cdcFileService
     * @return CdcFile|View
     * @throws Exception
     */
    public function update(CdcFile $cdcFile, Request $request, CdcFileService $cdcFileService)
    {
        /* @var $user User */
        $user = $this->getUser();
        $canUpdate = $this->checkRoles($cdcFile, $user->getMainOrganism());
        if (!$canUpdate) {
            throw new WedofBadRequestHttpException("Erreur, vous ne pouvez pas mettre à jour le fichier.");
        }

        $body = json_decode($request->getContent(), true);
        if (!empty($body)) {
            if (!empty($body['submissionDate']) && strtotime($body['submissionDate'])) {
                $body['submissionDate'] = (new DateTime($body['submissionDate']))->setTimezone(new DateTimeZone("GMT"));
            }
            $violations = $this->validateUpdateBody($body);
            if (count($violations)) {
                return $this->view($violations, Response::HTTP_BAD_REQUEST);
            }
            return $cdcFileService->update($cdcFile, $body);
        }
        return $cdcFile;
    }


    /**
     * @Rest\Delete("/api/cdcFiles/{id}")
     * @Rest\View(StatusCode = 204)
     * @param CdcFile $cdcFile
     * @param CdcFileService $cdcFileService
     * @return void
     * @throws WedofSubscriptionException
     */
    public function delete(CdcFile $cdcFile, CdcFileService $cdcFileService)
    {
        // Delete is synchronous so we need time if there are many CFs
        ini_set('memory_limit', '4096M');
        ini_set('max_execution_time', -1);
        // This was added mainly for admin / experimental purpose, not yet documented or used by the front
        // Only ABORTED files can be deleted, as it deletes all the certificationFolderCdcFiles which cannot be recovered if it was a mistake
        /* @var $user User */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();
        $subscription = $organism->getSubscription();
        if (!$subscription->isAllowCertifiers()) {
            throw new WedofSubscriptionException("Erreur, cette fonctionnalité est limitée aux abonnements Certificateur");
        }
        $canUpdate = $this->checkRoles($cdcFile, $organism);
        if (!$canUpdate) {
            throw new WedofBadRequestHttpException("Erreur, vous ne pouvez pas mettre à jour le fichier.");
        }
        // THIS CONSTRAINT IS VERY IMPORTANT
        // The CF cdcState is updated when the file is aborted
        // We need this update to occur before deletion
        if ($cdcFile->getState() !== CdcFileStates::ABORTED()->getValue()) {
            throw new WedofBadRequestHttpException("Erreur, le fichier ne peut être supprimé qu'à l'état 'Abandonné'.");
        }
        $cdcFileService->delete($cdcFile);
    }

    /**
     * @Rest\Post("/api/cdcFiles/{id}/abort")
     * @Rest\View(StatusCode = 200)
     * @param CdcFile $cdcFile
     * @param CdcFileService $cdcFileService
     * @return CdcFile
     * @throws Exception
     * @throws Throwable
     */
    public function abort(CdcFile $cdcFile, CdcFileService $cdcFileService): CdcFile
    {
        // Abort is synchronous so we need time if there are many CFs
        ini_set('memory_limit', '4096M');
        ini_set('max_execution_time', -1);
        /* @var $user User */
        $user = $this->getUser();
        $canUpdate = $this->checkRoles($cdcFile, $user->getMainOrganism());
        if (!$canUpdate) {
            throw new WedofBadRequestHttpException("Erreur, vous ne pouvez pas mettre à jour le fichier.");
        }

        if ($cdcFile->getState() !== CdcFileStates::EXPORTED()->getValue()) {
            throw new WedofBadRequestHttpException("Erreur, le fichier ne peut être abandonné qu'à l'état 'Exporté'.");
        }
        return $cdcFileService->abort($cdcFile);
    }

    /**
     * This synchronous method is here for dev tests only, it is not publicly documented
     * @Rest\Post("/api/cdcFile/generateXmlForDev/{idCertif}")
     * @Rest\Post("/api/cdcFile/generateXmlForDev")
     * @Rest\View(StatusCode = 200)
     * @Rest\QueryParam(name="noValidation", requirements=@Assert\Choice({"true", "false"}), nullable=true)
     * @param ParamFetcherInterface $paramFetcher
     * @param CertificationRepository $certificationRepository
     * @param CdcFileService $cdcFileService
     * @param CdcXMLService $cdcXMLService
     * @param int|null $idCertif
     * @return array
     * @throws Throwable
     * @throws WedofSubscriptionException
     */
    public function generateXmlForDev(ParamFetcherInterface $paramFetcher, CertificationRepository $certificationRepository, CdcFileService $cdcFileService, CdcXMLService $cdcXMLService, int $idCertif = null): array
    {
        // Generate for dev is synchronous so we need time if there are many CFs
        ini_set('memory_limit', '4096M');
        ini_set('max_execution_time', -1);
        $parameters = $paramFetcher->all(true);
        /* @var $user User */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();
        $subscription = $organism->getSubscription();
        if (!$subscription->isAllowCertifiers()) {
            throw new WedofSubscriptionException("Erreur, cette fonctionnalité est limitée aux abonnements Certificateur");
        }
        $idClient = $organism->getCdcClientId();
        $idContrat = $organism->getCdcContractId();
        if (!$idClient || !$idContrat) {
            throw new WedofBadRequestHttpException("Erreur, afin de générer le fichier XML vous devez remplir l'id client et l'id contrat de votre organisme.");
        }
        if ($idCertif) {
            $certification = $certificationRepository->findOneBy(['id' => $idCertif]);
            if (!$certification) {
                throw new WedofBadRequestHttpException("Erreur, la certification avec l'id " . $idCertif . " n'existe pas.");
            }
            if (!$certification->isCertifier($organism)) {
                throw new WedofBadRequestHttpException("Erreur, vous n'avez pas accès à la certification associée à l'id " . $idCertif . " .");
            }
            $cdcData = $cdcFileService->generateCdcData($organism, false, $certification->getCertifInfo());
        } else {
            $cdcData = $cdcFileService->generateCdcData($organism, false);
        }
        $idFlux = Uuid::v4()->toRfc4122();
        $xmlString = $cdcXMLService->generateCdcXML($cdcData, $idClient, $idClient, $idContrat, $idFlux);
        if (!isset($parameters['noValidation']) || !filter_var($parameters['noValidation'], FILTER_VALIDATE_BOOLEAN)) {
            try {
                SimpleXMLElementTools::validateCdcXML($xmlString, '/../../../data/certifications-cdc-v2.0.0.xsd');
            } catch (Exception $e) {
                if (!empty($e->getMessage())) {
                    throw new WedofBadRequestHttpException("Erreur de validation XSD : " . $e->getMessage());
                } else {
                    throw $e;
                }
            }
        }
        Header('Content-type: text/xml');
        echo $xmlString;
        die();
    }

    /**
     * @Rest\Post("/api/cdcFile/generateXmlFileForCdc/{idCertif}")
     * @Rest\Post("/api/cdcFile/generateXmlFileForCdc")
     * @Rest\View(StatusCode = 204)
     * @Security("is_granted('ROLE_USER')", message="not allowed")
     * @param CdcFileService $cdcFileService
     * @param CertificationRepository $certificationRepository
     * @param int|null $idCertif
     * @return void
     * @throws WedofSubscriptionException
     */
    public function generateXmlFileForCdc(CdcFileService $cdcFileService, CertificationRepository $certificationRepository, int $idCertif = null)
    {
        /* @var $user User */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();
        $subscription = $organism->getSubscription();
        if (!$subscription->isAllowCertifiers()) {
            throw new WedofSubscriptionException("Erreur, cette fonctionnalité est limitée aux abonnements Certificateur");
        }
        $hasDelegation = $organism->getAccrochageDelegationDate() != null;
        if ($hasDelegation && !$this->isGranted('IS_IMPERSONATOR')) {
            throw new WedofBadRequestHttpException("Erreur, vous avez activé l'accrochage automatique, vous ne pouvez donc pas générer le fichier manuellement.");
        }

        $ID_CLIENT = $organism->getCdcClientId();
        $ID_CONTRAT = $organism->getCdcContractId();
        if (!$ID_CLIENT || !$ID_CONTRAT) {
            throw new WedofBadRequestHttpException("Erreur, afin de générer le fichier XML vous devez remplir l'id client et l'id contrat de votre organisme.");
        }
        if ($idCertif) {
            $certification = $certificationRepository->findOneBy(['id' => $idCertif]);
            if (!$certification) {
                throw new WedofBadRequestHttpException("Erreur, la certification avec l'id " . $idCertif . " n'existe pas.");
            }
            if (!$certification->isCertifier($organism)) {
                throw new WedofBadRequestHttpException("Erreur, vous n'avez pas accès à la certification associée à l'id " . $idCertif . " .");
            }
        }
        $cdcFileService->dispatchGenerateCdcXML($user, $hasDelegation, $idCertif);
    }

    /**
     * @Rest\Post("/api/cdcFile/ingestXml/files")
     * @Rest\View(StatusCode = 200)
     * @Security("is_granted('ROLE_USER')", message="not allowed")
     * @param Request $request
     * @param CdcXMLService $cdcXMLService
     * @return JsonResponse
     * @throws Throwable
     * @throws WedofSubscriptionException
     */
    public function ingestXmlFile(Request $request, CdcXMLService $cdcXMLService): JsonResponse
    {
        // Ingest is synchronous so we need time if there are many CFs
        ini_set('memory_limit', '4096M');
        ini_set('max_execution_time', -1);
        /* @var $user User */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();

        $subscription = $organism->getSubscription();
        if (!$subscription->isAllowCertifiers()) {
            throw new WedofSubscriptionException("Erreur, cette fonctionnalité est limitée aux abonnements Certificateur");
        }

        /** @var UploadedFile $file */
        $file = !empty($request->files->all()) ? $request->files->get('file') : $request->get('file');
        $fileContent = $file->getContent();
        if (!$fileContent) {
            throw new WedofBadRequestHttpException("Erreur, le fichier est vide.");
        }

        try {
            SimpleXMLElementTools::validateCdcXML($fileContent, '/../../../data/certifications-cdc-v2.0.0.xsd');
        } catch (Exception $e) {
            throw new WedofBadRequestHttpException("Erreur de validation XSD : " . $e->getMessage());
        }

        $fileOriginalName = $file->getClientOriginalName();
        $fileOriginalNameFormatted = Tools::removeSuffix($fileOriginalName, '.xml');

        $simpleElementXml = new SimpleXMLElement($fileContent);
        $ingestionResults = $cdcXMLService->ingestCdcXML($simpleElementXml, $organism, $user, $fileOriginalNameFormatted);

        return new JsonResponse($ingestionResults);
    }

    /**
     * @Rest\Post("/api/cdcFile/{id}/files")
     * @Rest\View(StatusCode = 204)
     * @param Request $request
     * @param CdcFileService $cdcFileService
     * @param CdcFile $cdcFile
     * @return void
     * @throws WedofSubscriptionException
     */
    public function readReceiptXmlForCdc(Request $request, CdcFileService $cdcFileService, CdcFile $cdcFile)
    {
        ini_set('memory_limit', '4096M');
        ini_set('max_execution_time', -1);
        /* @var $user User */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();
        $canUpdate = $this->checkRoles($cdcFile, $organism);
        if (!$canUpdate) {
            throw new WedofBadRequestHttpException("Erreur, vous ne pouvez pas mettre à jour le fichier.");
        }

        $subscription = $organism->getSubscription();
        if (!$subscription->isAllowCertifiers()) {
            throw new WedofSubscriptionException("Erreur, cette fonctionnalité est limitée aux abonnements Certificateur");
        }
        /** @var UploadedFile $file */
        $file = !empty($request->files->all()) ? $request->files->get('file') : $request->get('file');
        if (!$file->getContent()) {
            throw new WedofBadRequestHttpException("Erreur, aucune donnée n'a été renvoyée.");
        }

        try {
            SimpleXMLElementTools::validateCdcXML($file->getContent(), '/../../../data/accuse-traitement-cdc.xsd');
        } catch (Exception $e) {
            if (!empty($e->getMessage())) {
                throw new WedofBadRequestHttpException("Erreur de validation XSD : " . $e->getMessage());
            } else {
                throw new WedofBadRequestHttpException("Ceci n'est pas un accusé de traitement valide, erreur de validation XSD.");
            }
        }
        $cdcFileService->dispatchReadReceiptXmlForCdc($cdcFile, $file->getContent());
    }

    /**
     * @Rest\Get("/api/cdcFiles/delegatedOrganisms")
     * @IsGranted("ROLE_ADMIN", message="not allowed")
     * @Rest\View(StatusCode = 200)
     * @param CdcCertifiersApiService $cdcCertifiersApiService
     * @return Response
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     * @throws WedofConnectionException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws ErrorException
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    public function delegatedOrganisms(CdcCertifiersApiService $cdcCertifiersApiService): Response
    {
        /* @var $user User */
        $user = $this->getUser();
        $organism = $user->getMainOrganism(); // Should be wedof
        $delegatedOrganisms = $cdcCertifiersApiService->getCertificateurs($organism);
        $view = $this->view($delegatedOrganisms, 200);
        return $this->handleView($view);
    }

    //----------------
    // METHODES PRIVES
    //----------------

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateUpdateBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'allowExtraFields' => true,
            'fields' => [
                'submissionDate' => new Assert\Optional(new Assert\Type('datetime'))
            ]
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param CdcFile $cdcFile
     * @param Organism $organism
     * @return bool
     */
    private function checkRoles(CdcFile $cdcFile, Organism $organism): bool
    {
        return $this->isGranted('IS_IMPERSONATOR') ||
            $this->isGranted(CdcFileVoter::EDIT, $cdcFile) && !$cdcFile->isGeneratedAutomatically() && !$organism->getAccrochageDelegationDate();
    }
}
