<?php
// src/Controller/api/WebhookController.php

namespace App\Controller\api;

use App\Application\Activepieces\ActivepiecesWedofApplication;
use App\Application\Dendreo\DendreoWedofApplication;
use App\Application\Make\MakeWedofApplication;
use App\Application\N8n\N8nWedofApplication;
use App\Application\Webhook\WebhookWedofApplication;
use App\Application\Workflow\WorkflowWedofApplication;
use App\Application\Zapier\ZapierWedofApplication;
use App\Entity\Organism;
use App\Entity\User;
use App\Entity\Webhook;
use App\Event\Certification\CertificationEvents;
use App\Event\CertificationFolder\CertificationFolderEvents;
use App\Event\CertificationFolderFile\CertificationFolderFileEvents;
use App\Event\CertificationFolderSurvey\CertificationFolderSurveyEvents;
use App\Event\CertificationPartnerAudit\CertificationPartnerAuditEvents;
use App\Event\CertificationPartnerFile\CertificationPartnerFileEvents;
use App\Event\Connection\ConnectionEvents;
use App\Event\Evaluation\EvaluationEvents;
use App\Event\Invoice\InvoiceEvents;
use App\Event\Organism\OrganismEvents;
use App\Event\RegistrationFolder\RegistrationFolderEvents;
use App\Event\RegistrationFolder\RegistrationFolderMonitoringEvents;
use App\Event\RegistrationFolderFile\RegistrationFolderFileEvents;
use App\Event\Subscription\SubscriptionEvents;
use App\Exception\WedofAccessDeniedHttpException;
use App\Exception\WedofNotFoundHttpException;
use App\Exception\WedofSubscriptionException;
use App\Library\utils\enums\CertificationFolderStates;
use App\Library\utils\enums\CertificationPartnerStates;
use App\Library\utils\enums\FileStates;
use App\Library\utils\enums\RegistrationFolderBillingStates;
use App\Library\utils\enums\RegistrationFolderControlStates;
use App\Library\utils\enums\RegistrationFolderStates;
use App\Library\utils\Tools;
use App\Security\Voter\OrganismVoter;
use App\Security\Voter\WebhookVoter;
use App\Service\AccessService;
use App\Service\OrganismService;
use App\Service\WebhookService;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\OptimisticLockException;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcherInterface;
use FOS\RestBundle\View\View;
use Knp\Component\Pager\PaginatorInterface;
use Nelmio\ApiDocBundle\Annotation as ApiDoc;
use OpenApi\Annotations as OA;
use ReflectionException;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validation;
use Throwable;

/**
 * Class WebhookController
 * @package App\Controller\api
 *
 * @OA\Tag(name="Webhook")
 * @ApiDoc\Security(name="accessCode")
 */
class WebhookController extends AbstractFOSRestController
{
    //-------------------
    // METHODES PUBLIQUES
    //-------------------
    /**
     * @Rest\Get("/api/webhooks")
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_WEBHOOK:READ')", message="not allowed")
     * @Rest\QueryParam(name="enabled", requirements=@Assert\Choice({"true", "false"}), nullable=true, description="Filtre les résultats en fonction de si le webhook est actif ou non - par défaut, tous les webhooks sont retournés.")
     * @Rest\QueryParam(name="siret", requirements=@Assert\Type("string"), nullable=true, description="Permet de n'obtenir que les webhooks appartenant à l'organisme de siret considéré - par défaut l'organisme de l'utilisateur courant.")
     *
     * @Rest\QueryParam(name="limit", requirements="\d+", default="100", description="Nombre d'éléments retourné par requête - par défaut 100.")
     * @Rest\QueryParam(name="page", requirements="\d+", default="1", description="Numéro de page de la requête - par défaut la première.")
     * @Rest\QueryParam(name="order", requirements=@Assert\Choice({"asc", "desc"}), default="desc", description="Tri les résultats par ordre ascendant ou descendant - par défaut descendant.")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"webhooks", "default"})
     * @OA\Get (
     *     summary="Liste tous les webhooks pour l'organisme de l'utilisateur courant.",
     *     description="Récupère l'ensemble des webhooks de l'utilisateur connecté. NOTA : les paramètres sont cumulatifs et deux paramètres incompatibles renverront un résultat vide. Via OAuth2, cet appel nécessite le scope 'webhook:read'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un tableau de webhooks au format JSON",
     *     @OA\JsonContent(
     *          type="array",
     *          @OA\Items(ref=@ApiDoc\Model(type=Webhook::class))
     *     )
     * )
     * @OA\Parameter (name="enabled", in="query", @OA\Schema (ref="#/components/schemas/Boolean"))
     * @OA\Parameter (name="order", in="query", @OA\Schema (ref="#/components/schemas/Order"))
     *
     * @param ParamFetcherInterface $paramFetcher
     * @param OrganismService $organismService
     * @param WebhookService $webhookService
     * @param PaginatorInterface $paginator
     * @return Response
     */
    public function list(ParamFetcherInterface $paramFetcher, OrganismService $organismService, WebhookService $webhookService, PaginatorInterface $paginator): Response
    {
        /* @var $user User */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();
        $parameters = $paramFetcher->all(true);

        if (!empty($parameters['siret'])) {
            $organism = $organismService->getBySiret($parameters['siret']);
            if (!empty($organism)) {
                if (!$this->isGranted(OrganismVoter::VIEW, $organism)) {
                    throw new WedofAccessDeniedHttpException("Vous n'êtes pas autorisé à avoir accès au contenu de l'organisme associé au siret " . $parameters['siret']);
                }
            } else {
                throw new WedofNotFoundHttpException("L'organisme associé au siret " . $parameters['siret'] . " n'a pas été trouvé.");
            }
        }
        $parameters['type'] = 'webhook'; //only webhook
        $data = $paginator->paginate($webhookService->listReturnQueryBuilder($organism, $parameters), intval($parameters['page']), intval($parameters['limit']));
        $view = $this->view($data->getItems(), 200);
        $view->setHeader("x-total-count", $data->getTotalItemCount());
        $view->setHeader("x-current-page", $data->getCurrentPageNumber());
        $view->setHeader("x-item-per-page", $data->getItemNumberPerPage());
        return $this->handleView($view);
    }

    /**
     * @Rest\Get("/api/webhooks/{id}")
     * @Rest\View(StatusCode = 200)
     * @IsGranted(WebhookVoter::VIEW, subject="webhook", message="not allowed")
     *
     * @ApiDoc\Areas({"webhooks", "default"})
     * @OA\Get (
     *     summary="Récupération d'un webhook.",
     *     description="Récupération d'un webhook par son ID. Via OAuth2, cet appel nécessite le scope 'webhook:read'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations du webhook.",
     *     @ApiDoc\Model(type=Webhook::class))
     *     )
     * )
     * @OA\Parameter(
     *     name="id",
     *     in="path",
     *     description="ID du webhook",
     *     @OA\Schema(type="string")
     * )
     *
     * @param Webhook $webhook
     * @return Webhook
     */
    public function show(Webhook $webhook): Webhook
    {
        return $webhook;
    }

    /**
     * @Rest\Post("/api/webhooks")
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_WEBHOOK:WRITE')", message="not allowed")
     * @Rest\QueryParam(name="siret", requirements="\d{14}", nullable=true, description="Sélectionne l'organisme pour lequel le webhook sera créé - par défaut l'organisme de l'utilisateur courant.")
     * @Rest\View(StatusCode = 201)
     *
     * @ApiDoc\Areas({"webhooks", "default"})
     * @OA\Post (
     *     summary="Création d'un webhook.",
     *     description="Crée un nouveau webhook. Via OAuth2, cet appel nécessite le scope 'webhook:write'."
     * )
     * @OA\Response(
     *     response=201,
     *     description="Un json contenant les informations du webhook nouvellement créé.",
     *     @ApiDoc\Model(type=Webhook::class))
     *     )
     * )
     * @OA\RequestBody(
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(
     *             @OA\Property(property="url", type="string", description="Obligatoire: url de appelée par le webhook lors d'un évènement."),
     *             @OA\Property(property="events", type="array", @OA\Items(type="string"), description="Obligatoire: liste d'évènements déclencheurs du webhook. Les évènements doivent être construit de la façon suivante : 'object.évènement'.
    Objects possibles : 'certification', 'registrationFolder', 'registrationFolderAlert', 'certificationFolder', 'certificationPartner', 'registrationFolderBilling', 'evaluation', 'payment', 'certificationFolderSurvey'. Pour déclencher tous les évènements possibles pour un object utiliser le carte blanche '\*', exemple : 'registrationFolder.\*'.
    Pour les évènements relatifs à 'registrationFolder', voir le schéma 'RegistrationFolderEvent'.
    Pour les évènements relatifs à 'registrationFolderAlert', voir le schéma 'RegistrationFolderAlertEvent'.
    Pour les évènements relatifs à 'certificationFolder', voir le schéma 'CertificationFolderEvent'.
    Pour les évènements relatifs à 'registrationFolderBilling', voir le schéma 'RegistrationFolderBillingEvent'.
    Pour les évènements relatifs à 'registrationFolderControl', voir le schéma 'RegistrationFolderControlEvent'.
    Pour les évènements relatifs à 'certification', voir le schéma 'CertificationEvent'.
    Pour les évènements relatifs à 'evaluation', voir le schéma 'EvaluationEvent'.
    Pour les évènements relatifs à 'payment', voir le schéma 'PaymentEvent'.
    Pour les évènements relatifs à 'certificationPartner', voir le schéma 'CertificationPartnerEvent'.
    Pour les évènements relatifs à 'certificationFolderSurvey', voir le schéma 'CertificationFolderSurveyEvent'.
    "),
     *             @OA\Property(property="secret", type="string", description="Facultatif: permet de sécuriser l'appel au webhook par une chaine de caractères secrète. Par défaut: aucun secret."),
     *             @OA\Property(property="enabled", type="boolean", description="Facultatif: défini si le webhook est activé ou non. Par défaut: webhook activé."),
     *             @OA\Property(property="ignoreSsl", type="boolean", description="Facultatif: permet d'ignorer les alertes SSL (certificat autosigné, expiré...). Par défaut: désactivé.")
     *         )
     *     )
     * )
     * )
     *
     * @param ParamFetcherInterface $paramFetcher
     * @param OrganismService $organismService
     * @param WebhookService $webhookService
     * @param AccessService $accessService
     * @param Request $request
     * @return Webhook|View
     * @throws ORMException
     * @throws ReflectionException
     * @throws Throwable
     * @throws WedofSubscriptionException
     * @throws NonUniqueResultException
     * @throws \Doctrine\ORM\ORMException
     */
    public function create(ParamFetcherInterface $paramFetcher, OrganismService $organismService, WebhookService $webhookService, AccessService $accessService, Request $request)
    {
        /* @var $user User */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();
        $body = json_decode($request->getContent(), true);

        $applicationsToExclude = [
            N8nWedofApplication::getAppId(),
            MakeWedofApplication::getAppId(),
            ActivepiecesWedofApplication::getAppId(),
            DendreoWedofApplication::getAppId(),
            ZapierWedofApplication::getAppId()
        ];

        if (Tools::contains($body['url'], '.processus.wedof.fr') && !$accessService->isApplicationAllowedAndEnabled(WorkflowWedofApplication::getAppId(), $organism)) {
            throw new WedofSubscriptionException("Erreur, l'application Workflow n'est pas active dans Wedof.");
        } else {
            $accessWorkflow = Tools::contains($body['url'], '.processus.wedof.fr') && $accessService->isApplicationAllowedAndEnabled(WorkflowWedofApplication::getAppId(), $organism);
            if (!$accessWorkflow) {
                foreach ($applicationsToExclude as $applicationToExclude) {
                    if (!$accessService->isApplicationAllowedAndEnabled($applicationToExclude, $organism) &&
                        (Tools::contains($body['url'], $applicationToExclude) || Tools::contains(strtolower($request->headers->all()['user-agent'][0]), strtolower($applicationToExclude)))
                    ) {
                        throw new WedofSubscriptionException("Erreur, votre abonnement ne permet pas la création de webhook " . $applicationToExclude . " ou l'application " . $applicationToExclude . " n'est pas active.");
                    }
                }
                if (!$accessService->isApplicationAllowedAndEnabled(WebhookWedofApplication::getAppId(), $organism)) {
                    throw new WedofAccessDeniedHttpException("Votre abonnement ne permet pas l'utilisation de l'application des Webhooks ou vous n'avez pas activé l'application.");
                }
            }
        }


        $parameters = $paramFetcher->all(true);
        if (isset($parameters['siret'])) {
            $organism = $organismService->getBySiret($parameters['siret']);
            if (!empty($organism)) {
                if (!$this->isGranted(OrganismVoter::EDIT, $organism)) {
                    throw new WedofAccessDeniedHttpException("Vous n'êtes pas autorisé à créer un webhook pour l'organisme associé au siret " . $parameters['siret']);
                }
            } else {
                throw new WedofNotFoundHttpException("L'organisme associé au siret " . $parameters['siret'] . " n'a pas été trouvé.");
            }
        }
        $violations = $this->validateCreateBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }
        return $webhookService->create($body, $organism);
    }

    /**
     * @Rest\Put("/api/webhooks/{id}")
     * @IsGranted(WebhookVoter::EDIT, subject="webhook", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"webhooks", "default"})
     * @OA\Put (
     *     summary="Mise à jour d'un webhook.",
     *     description="Met à jour un webhook par son ID. Via OAuth2, cet appel nécessite le scope 'webhook:write'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations webhook mis à jour.",
     *     @ApiDoc\Model(type=Webhook::class))
     *     )
     * )
     * @OA\RequestBody(
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(
     *             @OA\Property(property="events", type="array", @OA\Items(type="string"), description="Obligatoire: liste d'évènements déclencheurs du webhook. Les évènements doivent être construit de la façon suivante : 'object.évènement'.
     *                  Objects possibles : 'certification', 'registrationFolder', 'evaluation'. Pour déclencher tous les évènements possibles pour un object utiliser le carte blanche '\*', exemple : 'registrationFolder.\*'.
     *                  Pour les évènements relatifs à 'registrationFolder', voir le schéma 'RegistrationFolderEvent'.
     *                  Pour les évènements relatifs à 'certification', voir le schéma 'CertificationEvent'.
     *                  Pour les évènements relatifs à 'evaluation', voir le schéma 'EvaluationEvent'.
     *                  Pour les évènements relatifs à 'registrationFolderAlert', voir le schéma 'RegistrationFolderAlertEvent'.
     *                  Pour les évènements relatifs à 'certificationFolder', voir le schéma 'CertificationFolderEvent'.
     *                  Pour les évènements relatifs à 'registrationFolderBilling', voir le schéma 'RegistrationFolderBillingEvent'.
     *                  Pour les évènements relatifs à 'registrationFolderControl', voir le schéma 'RegistrationFolderControlEvent'.
     *                  Pour les évènements relatifs à 'payment', voir le schéma 'PaymentEvent'.
     *                  Pour les évènements relatifs à 'certificationPartner', voir le schéma 'CertificationPartnerEvents'.
     *                  Pour les évènements relatifs à 'certificationFolderSurvey', voir le schéma 'CertificationFolderSurveyEvents'.
     *             "),
     *             @OA\Property(property="secret", type="string", description="Facultatif: permet de sécuriser l'appel au webhook par une chaine de caractères secrète."),
     *             @OA\Property(property="enabled", type="boolean", description="Facultatif: défini si le webhook est activé ou non."),
     *             @OA\Property(property="ignoreSsl", type="boolean", description="Facultatif: permet d'ignorer les alertes SSL (certificat autosigné, expiré...).")
     *         )
     *     )
     * )
     *
     * @param Webhook $webhook
     * @param WebhookService $webhookService
     * @param Request $request
     * @return Webhook|View
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function update(Webhook $webhook, WebhookService $webhookService, Request $request)
    {
        $body = json_decode($request->getContent(), true);
        $violations = $this->validateUpdateBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }
        return $webhookService->update($webhook, $body);
    }

    /**
     * @Rest\Delete("/api/webhooks/{id}")
     * @IsGranted(WebhookVoter::EDIT, subject="webhook", message="not allowed")
     * @Rest\View(StatusCode = 204)
     *
     * @ApiDoc\Areas({"webhooks", "default"})
     * @OA\Delete (
     *     summary="Suppression d'un webhook.",
     *     description="Supprime un webhook par son ID. Via OAuth2, cet appel nécessite le scope 'webhook:write'."
     * )
     * @OA\Response(
     *     response=204,
     *     description="Aucun contenu retourné."
     * )
     *
     * @OA\Parameter(
     *     name="id",
     *     in="path",
     *     description="ID du webhook",
     *     @OA\Schema(type="string")
     * )
     *
     *
     * @param Webhook $webhook
     * @param WebhookService $webhookService
     */
    public function delete(Webhook $webhook, WebhookService $webhookService)
    {
        $webhookService->delete($webhook);
    }

    /**
     * @Rest\Post("/api/webhooks/{id}/disable")
     * @Rest\View(StatusCode = 200)
     * @IsGranted(WebhookVoter::EDIT, subject="webhook", message="not allowed")
     *
     * @ApiDoc\Areas({"webhooks", "default"})
     * @OA\Post (
     *     summary="Désactivation d'un webhook.",
     *     description="Désactive un webhook par son ID. Via OAuth2, cet appel nécessite le scope 'webhook:write'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations du webhook désactivé.",
     *     @ApiDoc\Model(type=Webhook::class))
     *     )
     * )
     *
     * @OA\Parameter(
     *     name="id",
     *     in="path",
     *     description="ID du webhook",
     *     @OA\Schema(type="string")
     * )
     *
     *
     * @param Webhook $webhook
     * @param WebhookService $webhookService
     * @return Webhook
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function disable(Webhook $webhook, WebhookService $webhookService): Webhook
    {
        return $webhookService->update($webhook, ['enabled' => false]);
    }

    /**
     * @Rest\Post("/api/webhooks/{id}/enable")
     * @Rest\View(StatusCode = 200)
     * @IsGranted(WebhookVoter::EDIT, subject="webhook", message="not allowed")
     *
     * @ApiDoc\Areas({"webhooks", "default"})
     * @OA\Post (
     *     summary="Activation d'un webhook.",
     *     description="Active un webhook par son ID. Via OAuth2, cet appel nécessite le scope 'webhook:write'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations du webhook activé.",
     *     @ApiDoc\Model(type=Webhook::class))
     *     )
     * )
     *
     * @OA\Parameter(
     *     name="id",
     *     in="path",
     *     description="ID du webhook",
     *     @OA\Schema(type="string")
     * )
     *
     *
     * @param Webhook $webhook
     * @param WebhookService $webhookService
     * @return Webhook
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function enable(Webhook $webhook, WebhookService $webhookService): Webhook
    {
        return $webhookService->update($webhook, ['enabled' => true]);
    }

    /**
     * @Rest\Get("/api/webhooks-events")
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_WEBHOOK:READ')", message="not allowed")
     * @ApiDoc\Areas({"webhooks", "default"})
     * @OA\Get (
     *     summary="Liste tous les types d'évènements pour le déclenchement des webhooks.",
     *     description="Récupère l'ensemble des types d'évènements registrationFolder, registrationFolderAlert, certificationFolder, registrationFolderBilling, certificationPartner, certification, evaluation, certificationFolderSurvey, organism"
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un objet JSON ayant pour clé le type d'évènement et comme valeur l'ensemble des évènements possibles pour le type donné.",
     * )
     * @Rest\View(StatusCode = 200)
     * @return array
     */
    public function getEvents(): array
    {
        /** @var User $user */
        $user = $this->getUser();
        /** @var Organism $mainOrganism */
        $mainOrganism = $user->getMainOrganism();
        $webhookEvents = [];

        if ($mainOrganism->isTrainingOrganism()) {
            $webhookEvents = array_merge($webhookEvents, [
                'organism' => [OrganismEvents::CPF_CATALOG_UPLOAD_FINISHED, OrganismEvents::CPF_CATALOG_EXPORT_FINISHED],
                'registrationFolder' => array_merge(array_reduce(RegistrationFolderStates::valuesStates(),
                    function ($acc, $state) {
                        // TODO : fix this mess !
                        // There are 3 technical states for the same label in EDOF : "Annulé sans suite" : rejected, rejectedWithoutTitulaireSuite, rejectedWithoutCdcSuite, rejectedWithoutOfSuite
                        // Nothing allows to know why they differ yet
                        // Most are "rejectedWithoutTitulaireSuite"
                        // We could either understand the difference
                        // or make it disappear by storing only one state (ideally "rejected") & have the details somewhere else
                        // but it may be dangerous (change it or not in the raw data as well?)
                        // In the meantime, all of them are sent to the same Webhook, the only one that can be created : "rejectedWithoutTitulaireSuite"
                        if ($state->getValue() != 'all' && !in_array($state->getValue(), [RegistrationFolderStates::REJECTED(), RegistrationFolderStates::REJECTED_WITHOUT_CDC_SUITE(), RegistrationFolderStates::REJECTED_WITHOUT_OF_SUITE()])) {
                            $acc[] = 'registrationFolder.' . $state->getValue();
                        }
                        return $acc;
                    }, [RegistrationFolderEvents::CREATED, RegistrationFolderEvents::UPDATED]),
                    [
                        RegistrationFolderFileEvents::FILE_ADDED,
                        RegistrationFolderFileEvents::FILE_UPDATED,
                        RegistrationFolderFileEvents::FILE_DELETED,
                        RegistrationFolderFileEvents::FILE_SIGNATURE_PARTIALLY,
                        RegistrationFolderFileEvents::FILE_SIGNATURE_COMPLETED,
                        RegistrationFolderFileEvents::FILE_SIGNATURE_DECLINED
                    ], array_reduce(FileStates::valuesStates(),
                        function ($acc, $state) {
                            $acc[] = 'registrationFolderFile.' . $state->getValue();
                            return $acc;
                        }
                    )),
                'registrationFolderAlert' => [
                    RegistrationFolderMonitoringEvents::NOT_ACCEPTED,
                    RegistrationFolderMonitoringEvents::NOT_IN_TRAINING,
                    RegistrationFolderMonitoringEvents::NOT_SERVICE_DONE_DECLARED,
                    RegistrationFolderMonitoringEvents::NOT_VALIDATED
                ],
                'registrationFolderBilling' => array_reduce(RegistrationFolderBillingStates::valuesStates(),
                    function ($acc, $state) {
                        if ($state->getValue() != "all") {
                            $acc[] = 'registrationFolderBilling.' . $state->getValue();
                        }
                        return $acc;
                    }, []),
                'registrationFolderControl' => array_reduce(RegistrationFolderControlStates::valuesStates(),
                    function ($acc, $state) {
                        if ($state->getValue() != "all" && $state->getValue() != RegistrationFolderControlStates::NOT_IN_CONTROL()->getValue()) {
                            $acc[] = 'registrationFolderControl.' . $state->getValue();
                        }
                        return $acc;
                    }, []),
                'evaluation' => [
                    EvaluationEvents::ORGANISM_CHANGED,
                    EvaluationEvents::ORGANISM_NEW,
                    EvaluationEvents::TRAINING_CHANGED,
                    EvaluationEvents::TRAINING_NEW,
                    EvaluationEvents::TRAININGACTION_CHANGED,
                    EvaluationEvents::TRAININGACTION_NEW
                ],
            ]);
        }

        if ($mainOrganism->isCertifierOrganism()) {
            $webhookEvents = array_merge($webhookEvents, [
                'certification' => [
                    CertificationEvents::UPDATED,
                    CertificationEvents::CREATED
                ],
                'certificationFolderSurvey' => [
                    CertificationFolderSurveyEvents::CREATED,
                    CertificationFolderSurveyEvents::INITIAL_EXPERIENCE_ANSWERED,
                    CertificationFolderSurveyEvents::SIX_MONTH_EXPERIENCE_ANSWERED,
                    CertificationFolderSurveyEvents::LONG_TERM_EXPERIENCE_ANSWERED,
                    CertificationFolderSurveyEvents::SIX_MONTH_EXPERIENCE_AVAILABLE,
                    CertificationFolderSurveyEvents::LONG_TERM_EXPERIENCE_AVAILABLE
                ],
                'certificationFolder' => array_merge(array_reduce(CertificationFolderStates::valuesStates(),
                    function ($acc, $state) {
                        if ($state->getValue() != "all") {
                            $acc[] = 'certificationFolder.' . $state->getValue();
                        }
                        return $acc;
                    }, [CertificationFolderEvents::CREATED, CertificationFolderEvents::UPDATED, CertificationFolderEvents::IN_TRAINING_STARTED, CertificationFolderEvents::IN_TRAINING_ENDED, CertificationFolderEvents::ACCROCHAGE_OK, CertificationFolderEvents::ACCROCHAGE_KO, CertificationFolderEvents::MISSING_DATA]),
                    [CertificationFolderFileEvents::FILE_ADDED, CertificationFolderFileEvents::FILE_UPDATED, CertificationFolderFileEvents::FILE_DELETED],
                    array_reduce(FileStates::valuesStates(),
                        function ($acc, $state) {
                            $acc[] = 'certificationFolderFile.' . $state->getValue();
                            return $acc;
                        }
                    )
                ),
                'certificationPartner' => array_merge(array_reduce(CertificationPartnerStates::valuesStates(),
                    function ($acc, $state) {
                        if ($state->getValue() != "all" && $state->getValue() != CertificationPartnerStates::DRAFT()->getValue()) {
                            $acc[] = 'certificationPartner.' . $state->getValue();
                        }
                        return $acc;
                    }, []),
                    [CertificationPartnerFileEvents::FILE_ADDED, CertificationPartnerFileEvents::FILE_UPDATED, CertificationPartnerFileEvents::FILE_DELETED],
                    array_reduce(FileStates::valuesStates(),
                        function ($acc, $state) {
                            $acc[] = 'certificationPartnerFile.' . $state->getValue();
                            return $acc;
                        }
                    ),
                    [InvoiceEvents::CERTIFICATION_PARTNER_CREATED, InvoiceEvents::CERTIFICATION_PARTNER_UPDATED, InvoiceEvents::CERTIFICATION_PARTNER_PAID, InvoiceEvents::CERTIFICATION_PARTNER_DELETED, CertificationPartnerAuditEvents::PENDING_COMPUTATION, CertificationPartnerAuditEvents::COMPUTING, CertificationPartnerAuditEvents::IN_PROGRESS, CertificationPartnerAuditEvents::COMPLETED, CertificationPartnerAuditEvents::COMPLIANT, CertificationPartnerAuditEvents::NON_COMPLIANT, CertificationPartnerAuditEvents::PARTIALLY_COMPLIANT],
                ),
            ]);
        }
        if ($mainOrganism->isReseller()) {
            $webhookEvents = array_merge($webhookEvents, [
                "reseller" => [
                    SubscriptionEvents::CREATED,
                    SubscriptionEvents::UPDATED,
                    ConnectionEvents::INITIALIZE_STARTED,
                    ConnectionEvents::INITIALIZE_COMPLETED,
                    ConnectionEvents::AUTH_REVOKED
                ]
            ]);
        }
        return $webhookEvents;
    }

    //----------------
    // METHODES PRIVES
    //----------------
    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateCreateBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'url' => new Assert\Required([new Assert\NotBlank(), new Assert\Regex(['pattern' => '/^(?!.*(?:127\.0\.0\.1|localhost|\b(?:10|172\.(?:1[6-9]|2\d|3[01])|192\.168)\.\d{1,3}\.\d{1,3})(?::\d+)?\b)(?:https?|http):\/\/(?:www\.)?[a-zA-Z0-9\-\.]+(?:\.[a-zA-Z]{2,})?(?::\d+)?(?:\/[^\s]*)?$/']), new Assert\Length(['max' => 255])]),
            'events' => new Assert\Required([new Assert\NotNull(), new Assert\Type('array'), new Assert\All([new Assert\NotBlank(), new Assert\Type('string')])]),
            'secret' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 255])]),
            'enabled' => new Assert\Optional([new Assert\Type('bool')]),
            'ignoreSsl' => new Assert\Optional([new Assert\Type('bool')]),
            'name' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 255])])
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateUpdateBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'events' => new Assert\Optional([new Assert\NotNull(), new Assert\Type('array'), new Assert\All([new Assert\NotBlank(), new Assert\Type('string')])]),
            'secret' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 255])]),
            'enabled' => new Assert\Optional([new Assert\Type('bool')]),
            'ignoreSsl' => new Assert\Optional([new Assert\Type('bool')]),
            'name' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 255])])
        ]);
        return $validator->validate($body, $constraints);
    }
}
