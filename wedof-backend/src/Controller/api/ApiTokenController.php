<?php
// src/Controller/api/ApiTokenController.php

namespace App\Controller\api;

use App\Entity\ApiToken;
use App\Entity\User;
use App\Exception\WedofBadRequestHttpException;
use App\Security\Voter\ApiTokenVoter;
use App\Service\ApiTokenService;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\View\View;
use Nelmio\ApiDocBundle\Annotation as ApiDoc;
use OpenApi\Annotations as OA;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validation;

class ApiTokenController extends AbstractFOSRestController
{
    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    /**
     * @Rest\Post ("/api/apiTokens")
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_APITOKEN:WRITE')", message="not allowed")
     * @Rest\View(StatusCode = 201)
     *
     * @ApiDoc\Areas({"apiTokens", "default"})
     * @OA\Get (
     *     summary="Créer un nouveau token.",
     *     description="Permet de créer un nouveau token. Via OAuth2, cet appel nécessite le scope 'apitoken:write'"
     * )
     * @OA\Response(
     *     response=201,
     *     description="Un json contenant les informations du nouveau token",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/ApiToken")
     *     )
     * )
     * @OA\RequestBody(
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/ApiTokenCreateBody")
     *     )
     * )
     *
     * @param Request $request
     * @param ApiTokenService $apiTokenService
     * @return ApiToken|View
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function create(Request $request, ApiTokenService $apiTokenService)
    {
        /* @var $user User */
        $user = $this->getUser();

        if (!$user->isOwner()) {
            throw new WedofBadRequestHttpException("Erreur, vous ne pouvez pas créer de token, veuillez contacter votre administrateur.");
        }

        $body = json_decode($request->getContent(), true);
        $violations = $this->validateCreateBody($body);
        if (strtolower($body['name']) === 'jeton principal') {
            throw new WedofBadRequestHttpException("Erreur, vous ne pouvez pas ajouter un token avec le nom " . $body['name'] .".");
        }
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }

        return $apiTokenService->create($user, $body['name']);
    }

    /**
     * @Rest\Delete("/api/apiTokens/{id}")
     * @IsGranted(ApiTokenVoter::EDIT, subject="apiToken", message="not allowed")
     * @Rest\View(StatusCode = 204)
     *
     * @ApiDoc\Areas({"apiTokens", "default"})
     * @OA\Delete (
     *     summary="Supprimer un token",
     *     description="Supprimer un token via l'id. Via OAuth2, cet appel nécessite le scope 'apiToken:write'."
     * )
     * @OA\Response(
     *     response=204,
     *     description="Aucun contenu retourné."
     * )
     * @OA\Parameter(
     *     name="id",
     *     in="path",
     *     description="id du token",
     *     @OA\Schema(type="string")
     * )
     *
     * @param ApiToken $apiToken
     * @param ApiTokenService $apiTokenService
     * @return void
     */
    public function delete(ApiToken $apiToken, ApiTokenService $apiTokenService)
    {
        if (strtolower($apiToken->getName()) === 'jeton principal') {
            throw new WedofBadRequestHttpException("Erreur, vous ne pouvez pas supprimer le token associé au nom " . $apiToken->getName() .".");
        }
        $apiTokenService->delete($apiToken);
    }

    //----------------
    // METHODES PRIVES
    //----------------

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateCreateBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'name' => new Assert\Required([new Assert\Type('string'), new Assert\Length(['max' => 255])]),
        ]);
        return $validator->validate($body, $constraints);
    }

}
