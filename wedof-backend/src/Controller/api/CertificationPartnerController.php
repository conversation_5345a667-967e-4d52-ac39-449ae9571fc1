<?php
// src/Controller/api/CertificationPartnerController.php
namespace App\Controller\api;

use App\Entity\Certification;
use App\Entity\CertificationPartner;
use App\Entity\CertificationPartnerFile;
use App\Entity\Organism;
use App\Entity\User;
use App\Exception\WedofAccessDeniedHttpException;
use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofConnectionException;
use App\Exception\WedofNotFoundHttpException;
use App\Exception\WedofSubscriptionException;
use App\Library\CertificationStatistics;
use App\Library\utils\enums\CertificationPartnerAuditResults;
use App\Library\utils\enums\CertificationPartnerAuditStates;
use App\Library\utils\enums\CertificationPartnerHabilitation;
use App\Library\utils\enums\CertificationPartnerStates;
use App\Library\utils\enums\CertificationSkillType;
use App\Library\utils\enums\CertificationTypes;
use App\Library\utils\enums\CertifierAccessStates;
use App\Library\utils\enums\FileStates;
use App\Library\utils\Tools;
use App\Repository\CertificationPartnerFileRepository;
use App\Repository\CertificationPartnerRepository;
use App\Security\Voter\CertificationPartnerVoter;
use App\Service\CertificationPartnerFileService;
use App\Service\CertificationPartnerService;
use App\Service\CertificationService;
use App\Service\OrganismService;
use App\Service\SkillService;
use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use ErrorException;
use FOS\RestBundle\Context\Context;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcherInterface;
use FOS\RestBundle\View\View;
use Knp\Component\Pager\PaginatorInterface;
use Nelmio\ApiDocBundle\Annotation as ApiDoc;
use OpenApi\Annotations as OA;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Entity;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\ParamConverter;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validation;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Throwable;

/**
 * Class CertificationPartnerController
 * @package App\Controller\api
 *
 * @OA\Tag(name="Certification Partner")
 * @ApiDoc\Security(name="accessCode")
 */
class CertificationPartnerController extends AbstractFOSRestController
{
    /**
     * @Rest\Get("/api/certifications/{certifInfo}/partners/{siret}")
     * @ParamConverter("certification", options={"mapping": {"certifInfo" = "certifInfo"}})
     * @ParamConverter("organism", options={"mapping": {"siret" = "siret"}})
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_CERTIFICATION:READ') or is_granted('ROLE_ADMIN')", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"certificationPartners", "default"})
     * @OA\Get (
     *     summary="Récupération d'un partenariat.",
     *     description="Récupération d'un partenariat par le certifInfo de la certification et du siret du partenaire."
     * )
     *
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations du partenariat",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/CertificationPartner")
     *     )
     * )
     *
     * @param Certification $certification
     * @param Organism $organism
     * @param CertificationPartnerService $certificationPartnerService
     * @return Response
     * @throws WedofSubscriptionException
     */
    public function show(Certification $certification, Organism $organism, CertificationPartnerService $certificationPartnerService): Response
    {
        /* @var $user User */
        $user = $this->getUser();
        $currentOrganism = $user->getMainOrganism();
        if ($certification->isCertifier($currentOrganism) || $this->isGranted('ROLE_ADMIN')) {
            if (!$currentOrganism->getSubscription() || !$currentOrganism->getSubscription()->isAllowCertifiers()) {
                throw new WedofSubscriptionException("Erreur, en tant que certificateur, vous devez avoir un abonnement pour visualiser un partenariat.");
            }
            if ($organism === $currentOrganism) {
                $certificationPartner = $certificationPartnerService->getTransientCertifierSelfCertificationPartner($currentOrganism, $certification);
            } else {
                $certificationPartner = $certificationPartnerService->getByCertificationAndPartner($certification, $organism, !$currentOrganism->getSubscription()->isAllowCertifierPlus());
            }
            // TODO maybe check if partnership state != active then throw error if no subscription option
        } else {
            if ($currentOrganism !== $organism) {
                throw new WedofAccessDeniedHttpException("Vous n'êtes pas autorisé à accéder à ce partenariat");
            }
            $certificationPartner = $certificationPartnerService->getByCertificationAndPartner($certification, $organism, false);
        }
        if (!$certificationPartner) {
            return new Response('', 204); // Empty response instead of 404 in order to be able to use this endpoint in the frontend without prior knowledge of whether there is already a partnership
        }
        $context = (new Context())->addGroup('owner');
        return $this->handleView($this->view($certificationPartner, 200)->setContext($context));
    }

    /**
     * @Rest\Get("/api/partners")
     * @Security("is_granted('ROLE_USER')", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @Rest\QueryParam(name="query", requirements=@Assert\Type("string"), nullable=true, description="Permet d'effectuer une recherche libre sur les champs 'nom de l'organisme partenaire' et son 'siret'.")
     * @Rest\QueryParam(name="limit", requirements="\d+", default="100", description="Nombre d'éléments retourné par requête - par défaut 100.")
     * @Rest\QueryParam(name="page", requirements="\d+", default="1", description="Numéro de page de la requête - par défaut la première.")
     * @Rest\QueryParam(name="sort", requirements=@Assert\Choice({"name", "state"}), default="name", description="Tri les résultats sur un critère. Valeurs possibles: 'name' (nom de l'organisme) - par défaut 'name'.")
     * @Rest\QueryParam(name="order", requirements=@Assert\Choice({"asc", "desc"}), default="desc", description="Tri les résultats par ordre ascendant ou descendant - par défaut descendant.")
     *
     * @param PaginatorInterface $paginator
     * @param CertificationPartnerService $certificationPartnerService
     * @param ParamFetcherInterface $paramFetcher
     * @return Response
     */
    public function listForActivity(PaginatorInterface $paginator, CertificationPartnerService $certificationPartnerService, ParamFetcherInterface $paramFetcher): Response
    {
        /* @var $user User */
        $user = $this->getUser();
        $context = (new Context())->addGroup('owner');
        $parameters = $paramFetcher->all(true);

        $parameters['certifier'] = $user->getMainOrganism();
        $parameters['state'] = CertificationPartnerStates::allExceptDraft();
        $data = $paginator->paginate($certificationPartnerService->listReturnQueryBuilder($parameters), intval($parameters['page']), intval($parameters['limit']));
        $view = $this->view($data->getItems(), 200);
        $view->setHeader("x-total-count", $data->getTotalItemCount());
        $view->setHeader("x-current-page", $data->getCurrentPageNumber());
        $view->setHeader("x-item-per-page", $data->getItemNumberPerPage());
        $view->setContext($context);
        return $this->handleView($view);
    }

    /**
     * @Rest\Get("/api/certifications/{certifInfo}/partners")
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_CERTIFICATION:READ') or is_granted('ROLE_ADMIN')", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"certificationPartners", "default"})
     * @OA\Get (
     *     summary="Liste les partenariats sur une certification.",
     *     description="Récupére l'ensemble des partenariats d'une certification."
     * )
     *
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations du partenariat",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/CertificationPartner")
     *     )
     * )
     *
     * @Rest\QueryParam(name="certifierAccessState", requirements=@Assert\Type("string"), default="all", description="Permet de n'obtenir que les partenariats dans le(s) état(s) de synchronisation considéré(s) - par défaut tous les états sont retournés. Valeurs possibles : 'waiting', 'accepted', 'refused', 'terminated', 'none', 'all'. Disponible uniquement pour les certificateurs.")
     * @Rest\QueryParam(name="connectionIssue", requirements=@Assert\Choice({"true", "false"}), nullable=true, default=null, description="Permet de n'obtenir que les partenariats pour lesquels les connexions présentes un problème. Valeurs possibles : 'true', 'false' - par défaut tous les éléments sont renvoyés.")
     * @Rest\QueryParam(name="compliance", requirements=@Assert\Type("string"), nullable=true, default="all", description="Permet de n'obtenir que les partenariats dans le(s) état(s) de conformité considéré(s) ou lorsqu'un audit est en cours - par défaut tous les états sont retournés. Valeurs possibles : 'all', 'none', 'compliant', 'partiallyCompliant', 'nonCompliant', 'inProgress'.")
     * @Rest\QueryParam(name="state", nullable=true, requirements=@Assert\Type("string"), default="all", description="Permet de n'obtenir que les partenariats dans le(s) état(s) considéré(s) - par défaut tous les états sont retournés. Valeurs possibles : 'all', 'draft', 'processing', 'active', 'aborted, 'refused', 'revoked', 'suspended'.")
     * @Rest\QueryParam(name="certifier", requirements=@Assert\Type("string"), nullable=true, description="Permet de n'obtenir que les partenariats appartenant au certificateur de siret considéré - par défaut le certificateur par défaut de la certification.")
     * @Rest\QueryParam(name="metadata", requirements=@Assert\Type("string"), nullable=true, description="Permet d'effectuer une une valeur d'un 'metadata' format : cle:valeur.")
     * @Rest\QueryParam(name="query", requirements=@Assert\Type("string"), nullable=true, description="Permet d'effectuer une recherche libre sur les champs 'nom de l'organisme partenaire' et son 'siret' et les 'tags' du partenariat.")
     * @Rest\QueryParam(name="limit", requirements="\d+", default="100", description="Nombre d'éléments retourné par requête - par défaut 100.")
     * @Rest\QueryParam(name="page", requirements="\d+", default="1", description="Numéro de page de la requête - par défaut la première.")
     * @Rest\QueryParam(name="sort", requirements=@Assert\Choice({"name", "state", "stateLastUpdate"}), default="stateLastUpdate", description="Tri les résultats sur un critère. Valeurs possibles: 'stateLastUpdate' (date du dernier changement d'état), 'name' (nom de l'organisme), 'state' (état du partenariat) - par défaut 'stateLastUpdate'.")
     * @Rest\QueryParam(name="order", requirements=@Assert\Choice({"asc", "desc"}), default="desc", description="Tri les résultats par ordre ascendant ou descendant - par défaut descendant.")
     *
     * @param Certification $certification
     * @param PaginatorInterface $paginator
     * @param CertificationPartnerService $certificationPartnerService
     * @param OrganismService $organismService
     * @param ParamFetcherInterface $paramFetcher
     * @return Response
     * @throws WedofSubscriptionException
     */
    public function list(Certification $certification, PaginatorInterface $paginator, CertificationPartnerService $certificationPartnerService, OrganismService $organismService, ParamFetcherInterface $paramFetcher): Response
    {
        $parameters = $paramFetcher->all(true);
        if ($parameters['connectionIssue'] !== null) {
            $parameters['connectionIssue'] = filter_var($parameters['connectionIssue'], FILTER_VALIDATE_BOOLEAN);
        }

        /* @var $user User */
        $user = $this->getUser();
        $context = new Context();
        $organism = $user->getMainOrganism();

        if (isset($parameters['certifier'])) {
            $certifier = $organismService->getBySiret($parameters['certifier']);
            if (!$certifier) {
                throw new WedofBadRequestHttpException("L'organisme de siret " . $parameters['certifier'] . " n'a pas été trouvé.");
            }
            if (!$certification->isCertifier($certifier)) {
                throw new WedofBadRequestHttpException("L'organisme de siret " . $parameters['certifier'] . " n'est pas certificateur de la certification " . $certification->getExternalId());
            }
            $parameters['certifier'] = $certifier;
        }
        $certifier = $certifier ?? ($certification->isCertifier($organism) ? $organism : null);

        if (isset($parameters['certifierAccessState'])) {
            $parameters['certifierAccessState'] = explode(",", $parameters['certifierAccessState']);
            if (!in_array('all', $parameters['certifierAccessState']) && !$certification->isCertifier($organism)) {
                throw new WedofAccessDeniedHttpException("Seul le certificateur est autorisé à filtrer sur l'état du certifier access.");
            }
            foreach ($parameters['certifierAccessState'] as $state) {
                if (!in_array($state, CertifierAccessStates::valuesStates())) {
                    throw new WedofBadRequestHttpException("Erreur sur les valeurs renvoyées 'certifierAccessState', elles doivent être : " . join(",", CertifierAccessStates::valuesStates()) . ".");
                }
            }
        }

        if (isset($parameters['state'])) {
            $parameters['state'] = explode(",", $parameters['state']);
            foreach ($parameters['state'] as $state) {
                if (!in_array($state, CertificationPartnerStates::valuesStates())) {
                    throw new WedofBadRequestHttpException("Erreur sur les valeurs renvoyées 'state', elles doivent être : " . join(",", CertificationPartnerStates::valuesStates()) . ".");
                }
            }
        }

        if (isset($parameters['metadata'])) {
            $parameters['metadata'] = explode(":", $parameters['metadata'], 2); //key=value if value contains : it shoulds work too but not if it is part of the key...
            if (sizeof($parameters['metadata']) > 2) {
                throw new WedofBadRequestHttpException("Erreur sur le format 'metadata', il doit être le suivant : cle:valeur");
            }
        }

        if ($certification->isCertifier($organism) || $this->isGranted('ROLE_ADMIN')) {
            if ($organism->getSubscription()->isAllowCertifiers()) {
                $context->addGroup('owner');
                if ($organism->getSubscription()->isAllowCertifierPlus()) {
                    $parameters['state'] = $parameters['state'] ?? CertificationPartnerStates::allExceptDraft();
                } else {
                    $parameters['state'] = [CertificationPartnerStates::ACTIVE()];
                }
            } else {
                throw new WedofSubscriptionException("Erreur, votre abonnement ne permet pas l'accès aux partenaires.");
            }
        } else if ($this->isGranted('ROLE_DATA_PARTNER')) {
            $context->addGroup('Default');
            $parameters['state'] = [CertificationPartnerStates::ACTIVE()];
        } else {
            throw new WedofAccessDeniedHttpException("Vous n'êtes pas autorisé à accéder aux partenaires associés au certifInfo " . $certification->getCertifInfo());
        }

        if (isset($parameters['compliance'])) {
            $parameters['compliance'] = explode(",", $parameters['compliance']);
            foreach ($parameters['compliance'] as $compliance) {
                $complianceValues = CertificationPartnerAuditResults::valuesResults();
                $complianceValues[] = CertificationPartnerAuditStates::IN_PROGRESS()->getValue();
                if (!in_array($compliance, $complianceValues)) {
                    throw new WedofBadRequestHttpException("Erreur sur les valeurs renvoyées 'compliance', elles doivent être : " . join(",", $complianceValues) . ".");
                }
            }
        }

        //manage to display the certifier as partner with all rights
        if ($parameters['query'] && ($certifier->getSiret() === $parameters['query'] || $certifier->getName() === $parameters['query'])) {
            //DTO do not save it
            $data = new CertificationPartner();
            $data->setCertification($certification);
            $data->setState(CertificationPartnerStates::ACTIVE()->getValue());
            $data->setPartner($certifier);
            $data->setCertifier($certifier);
            $data->setHabilitation(CertificationPartnerHabilitation::TRAIN_EVALUATE()->getValue());

            $view = $this->view([$data], 200);
            $view->setHeader("x-total-count", 1);
            $view->setHeader("x-current-page", 1);
            $view->setHeader("x-item-per-page", 1);
        } else {
            $data = $paginator->paginate($certificationPartnerService->listReturnQueryBuilder($parameters, $certification), intval($parameters['page']), intval($parameters['limit']));
            $view = $this->view($data->getItems(), 200);
            $view->setHeader("x-total-count", $data->getTotalItemCount());
            $view->setHeader("x-current-page", $data->getCurrentPageNumber());
            $view->setHeader("x-item-per-page", $data->getItemNumberPerPage());
        }
        $view->setContext($context);
        return $this->handleView($view);
    }

    /**
     * @Rest\Post("/api/certifications/partners/{siret}")
     * @Rest\View(statusCode=201)
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_CERTIFICATION:READ')", message="not allowed")
     *
     * @ApiDoc\Areas({"certificationPartners", "default"})
     * @OA\Post (
     *     summary="Créer un partenariat",
     *     description="Permet de créer un nouveau partenariat."
     * )
     * @OA\Response(
     *     response=201,
     *     description="Un json contenant les informations du nouveau partenariat créé",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/CertificationPartner")
     *     )
     * )
     *
     * @param Organism $organism
     * @param Request $request
     * @param CertificationPartnerService $certificationPartnerService
     * @param CertificationService $certificationService
     * @param CertificationPartnerRepository $certificationPartnerRepository
     * @return Response|View
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     * @throws WedofSubscriptionException
     */
    public function create(Organism $organism, Request $request, CertificationPartnerService $certificationPartnerService, CertificationService $certificationService, CertificationPartnerRepository $certificationPartnerRepository)
    {
        /* @var $user User */
        $user = $this->getUser();
        $currentOrganism = $user->getMainOrganism();
        $body = json_decode($request->getContent(), true);
        $violations = $this->validateRequestBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }
        $certification = $certificationService->getByCertifInfo($body['certifInfo']);
        if (!$certification) {
            throw new WedofNotFoundHttpException("La certification associée au certifInfo " . $body['certifInfo'] . " n'a pas été trouvée.");
        }
        if (!$certification->getEnabled()) {
            throw new WedofNotFoundHttpException("Erreur, il n'est pas possible de réaliser une demande de partenariat sur une certification inactive.");
        }
        $isCertifier = $certification->isCertifier($currentOrganism);
        $certifier = $isCertifier ? $currentOrganism : $certificationService->getDefaultCertifierFromCoCertifiers($certification);
        $isAllowManagePartnership = $certifier && $certifier->getSubscription() && $certifier->getSubscription()->isAllowCertifierPlus();
        if ($isCertifier && $currentOrganism === $organism) {
            throw new WedofBadRequestHttpException("Erreur, en tant que certificateur vous ne pouvez pas vous ajouter vous-même comme partenaire");
        }
        if ($isCertifier && !$isAllowManagePartnership) {
            throw new WedofSubscriptionException("Erreur, en tant que certificateur, vous devez avoir une option dans votre abonnement pour créer manuellement des partenariats.");
        }
        if (!$isCertifier && $currentOrganism !== $organism) {
            throw new WedofAccessDeniedHttpException("Vous n'êtes pas autorisé à créer un partenariat pour cet organisme");
        }
        if ($isCertifier) {
            $certificationPartner = $certificationPartnerRepository->findOneBy([
                'state' => CertificationPartnerStates::DRAFT()->getValue(),
                'partner' => $organism,
                'certification' => $certification
            ]);
            if (!$certificationPartner) {
                $certificationPartner = $certificationPartnerService->create($certification, $organism, $user, $certifier);
            }
            $certificationPartner = $certificationPartnerService->request($certificationPartner, $user);
        } else {
            $certificationPartner = $certificationPartnerService->create($certification, $organism, $user, $certifier);
        }

        $context = (new Context())->addGroup('owner');
        return $this->handleView($this->view($certificationPartner, 201)->setContext($context));
    }

    /**
     * @Rest\Put("/api/certifications/{certifInfo}/partners/{siret}")
     * @Rest\View(statusCode=200)
     * @Entity("certificationPartner", expr="repository.findOneByCertifInfoAndSiret(certifInfo, siret)")
     * @IsGranted(CertificationPartnerVoter::EDIT, subject="certificationPartner")
     *
     * @ApiDoc\Areas({"certificationPartners", "default"})
     * @OA\Put (
     *     summary="Met à jour le partenariat.",
     *     description="Permet de mettre à jour le partenariat."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations du partenariat",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/CertificationPartner")
     *     )
     * )
     * @OA\RequestBody(
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/CertificationPartnerUpdateBody")
     *     )
     * )
     *
     * @param CertificationPartner $certificationPartner
     * @param Request $request
     * @param CertificationPartnerService $certificationPartnerService
     * @param SkillService $skillService
     * @return View|Response
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     * @throws WedofSubscriptionException
     */
    public function update(CertificationPartner $certificationPartner, Request $request, CertificationPartnerService $certificationPartnerService, SkillService $skillService)
    {
        $body = json_decode($request->getContent(), true);
        if (!empty($body['amountHt']) && is_numeric($body['amountHt'])) {
            $body['amountHt'] = (float)($body['amountHt']);
        }

        if (array_key_exists('metadata', $body)) {
            if (is_string($body['metadata'])) {
                $body['metadata'] = json_decode($body['metadata'], true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    throw new WedofBadRequestHttpException("Erreur, le champ metadata doit être un JSON valide");
                }
            }

            if (!is_array($body['metadata']) && $body['metadata'] !== null) {
                throw new WedofBadRequestHttpException("Erreur, le champ metadata doit être un JSON valide");
            }
        }

        $violations = $this->validateUpdateBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }
        /* @var $user User */
        $user = $this->getUser();
        $currentOrganism = $user->getMainOrganism();
        $certification = $certificationPartner->getCertification();
        $isCertifier = $certificationPartner->getCertifier() === $currentOrganism || (!$certificationPartner->getCertifier() && $certification->isCertifier($currentOrganism));
        $certifier = $isCertifier ? $currentOrganism : $certificationPartner->getCertifier();
        $isAllowManagePartnership = $certifier && $certifier->getSubscription() && $certifier->getSubscription()->isAllowCertifierPlus();

        $targetState = $body['state'] ?? null;
        $currentState = $certificationPartner->getState();

        $shouldUpdate = false;
        $isInternalCertification = in_array($certification->getType(), [CertificationTypes::INTERNAL()->getValue(), CertificationTypes::PREVENTION()->getValue()]);

        if ($isInternalCertification) {
            unset($body['pendingRevocation']);
            unset($body['pendingActivation']);
            unset($body['pendingSuspension']);
        }

        if (isset($body['urls'])) {
            if (!$this->isGranted('ROLE_ADMIN')) {
                throw new WedofAccessDeniedHttpException("Erreur, seul un administrateur peut modifier les URLs d'un partenariat.");
            }
            $shouldUpdate = true;
        }

        if (isset($body['comment']) || isset($body['habilitation']) || isset($body['amountHt']) || isset($body['compliance']) || isset($body['tags']) || isset($body['metadata']) || isset($body['trainingsZone']) || isset($body['skillSets'])) {
            if ($isCertifier && !$isAllowManagePartnership) {
                throw new WedofSubscriptionException("Erreur, en tant que certificateur, vous devez avoir une option dans votre abonnement pour modifier les partenariats.");
            } else if (!$isCertifier) {
                if ($currentState !== CertificationPartnerStates::DRAFT()->getValue() && $currentState !== CertificationPartnerStates::PROCESSING()->getValue()) {
                    throw new WedofAccessDeniedHttpException("Erreur, en tant que partenaire, vous n'êtes pas autorisé à modifier le partenariat dans son état actuel");
                }
                if (isset($body['habilitation']) || isset($body['amountHt']) || isset($body['tags']) || isset($body['metadata']) || isset($body['trainingsZone']) || isset($body['skillSets'])) {
                    throw new WedofAccessDeniedHttpException("Erreur, en tant que partenaire, vous n'êtes pas autorisé à modifier les champs : habilitation, amountHt, tags, metadata, trainingsZone, skillSets");
                }
                if (isset($body['compliance']) && $certificationPartner->getCompliance() === CertificationPartnerAuditResults::NONE()->getValue()) {
                    throw new WedofBadRequestHttpException("Erreur, seule la conformité du partenaire après réalisation d'un audit peut être modifiée.");
                }
            }
            if (!$isInternalCertification && $currentState === CertificationPartnerStates::ACTIVE()->getValue() && isset($body['habilitation'])) {
                throw new WedofBadRequestHttpException("Erreur, l'habilitation ne peut pas être modifiée dans Wedof sur un partenariat actif");
            }
            $shouldUpdate = true;
        }

        if (array_key_exists('pendingActivation', $body)) {
            if ($certification->getCpfDateEnd() && ($certification->getCpfDateEnd() < new DateTime())) {
                throw new WedofBadRequestHttpException("Erreur, vous ne pouvez pas modifier le partenariat sur une certification expirée.");
            }
            if (!$isCertifier) {
                throw new WedofAccessDeniedHttpException("Erreur, en tant que partenaire, vous n'êtes pas autorisé à modifier l'attribut pendingActivation");
            }
            if ($body['pendingActivation']) {
                if (!in_array($currentState, [CertificationPartnerStates::PROCESSING(), CertificationPartnerStates::SUSPENDED(), CertificationPartnerStates::REVOKED()])) {
                    throw new WedofBadRequestHttpException("Erreur, l'attribut pendingActivation ne peut être mis à 'true' que si le partenariat est à l'état 'processing', 'suspended' ou 'revoked'");
                } else {
                    $body['pendingRevocation'] = false;
                    $body['pendingSuspension'] = false;
                }
            }
            $shouldUpdate = true;
        }

        if (array_key_exists('pendingRevocation', $body)) {
            if ($certification->getCpfDateEnd() && ($certification->getCpfDateEnd() < new DateTime())) {
                throw new WedofBadRequestHttpException("Erreur, vous ne pouvez pas modifier le partenariat sur une certification expirée.");
            }
            if (!$isCertifier) {
                throw new WedofAccessDeniedHttpException("Erreur, en tant que partenaire, vous n'êtes pas autorisé à modifier l'attribut pendingRevocation");
            }
            if ($body['pendingRevocation']) {
                if ($currentState !== CertificationPartnerStates::ACTIVE()->getValue()) {
                    throw new WedofBadRequestHttpException("Erreur, l'attribut pendingRevocation ne peut être mis à 'true' que si le partenariat est à l'état 'active'");
                } else {
                    $body['pendingActivation'] = false;
                    $body['pendingSuspension'] = false;
                }
            }
            $shouldUpdate = true;
        }

        if (array_key_exists('pendingSuspension', $body)) {
            if ($certification->getCpfDateEnd() && ($certification->getCpfDateEnd() < new DateTime())) {
                throw new WedofBadRequestHttpException("Erreur, vous ne pouvez pas modifier le partenariat sur une certification expirée.");
            }
            if (!$isCertifier) {
                throw new WedofAccessDeniedHttpException("Erreur, en tant que partenaire, vous n'êtes pas autorisé à modifier l'attribut pendingSuspension");
            }
            if ($body['pendingSuspension']) {
                if ($currentState !== CertificationPartnerStates::ACTIVE()->getValue()) {
                    throw new WedofBadRequestHttpException("Erreur, l'attribut pendingSuspension ne peut être mis à 'true' que si le partenariat est à l'état 'active'");
                } else {
                    $body['pendingActivation'] = false;
                    $body['pendingRevocation'] = false;
                }
            }
            $shouldUpdate = true;
        }

        if (isset($body['certifierSiret'])) {
            if ($certifier && $isAllowManagePartnership) {
                if (!$certificationPartner->getCertifier()) {
                    if ($body['certifierSiret'] === $certifier->getSiret()) {
                        $body['certifier'] = $certifier;
                        $shouldUpdate = true;
                    } else {
                        throw new WedofBadRequestHttpException('Erreur, vous ne pouvez pas associer ce partenariat à un autre certificateur que vous-même.');
                    }
                } else {
                    throw new WedofBadRequestHttpException('Erreur, un certificateur est déjà associé à ce partenariat.');
                }
            } else {
                throw new WedofBadRequestHttpException("Erreur, en tant que certificateur, vous devez avoir une option dans votre abonnement pour modifier les partenariats.");
            }
        }

        if (!empty($body['skillSets'])) {
            if (!$certification->isAllowPartialSkillSets()) {
                throw new WedofBadRequestHttpException('Erreur, les skillSets ne peuvent être déclarés que pour une certification dont l\'enseignement peut être divisé par blocs de compétences.');
            }
            $body['skillSets'] = $this->getSkillSets($body['skillSets'], $certificationPartner, $currentOrganism, $skillService);
        }

        if ($shouldUpdate) {
            $certificationPartner = $certificationPartnerService->update($certificationPartner, $body, $user);
        }

        if ($targetState && $targetState !== $currentState) {
            if ($isCertifier && !$isAllowManagePartnership) {
                throw new WedofSubscriptionException("Erreur, en tant que certificateur, vous devez avoir une option dans votre abonnement pour modifier les partenariats.");
            }
            if ($currentState === CertificationPartnerStates::PROCESSING()->getValue() && $targetState === CertificationPartnerStates::ABORTED()->getValue()) {
                $certificationPartner = $certificationPartnerService->abort($certificationPartner, $user);
            } else if ($isCertifier && $currentState === CertificationPartnerStates::PROCESSING()->getValue() && $targetState === CertificationPartnerStates::REFUSED()->getValue()) {
                $certificationPartner = $certificationPartnerService->refuse($certificationPartner, $user);
            } else if ($isCertifier && in_array($currentState, [CertificationPartnerStates::ABORTED(), CertificationPartnerStates::REFUSED()]) && $targetState === CertificationPartnerStates::PROCESSING()->getValue()) {
                $certificationPartner = $certificationPartnerService->reopen($certificationPartner, $user);
            } else if (!$isCertifier && $currentState === CertificationPartnerStates::ABORTED()->getValue() && $targetState === CertificationPartnerStates::PROCESSING()->getValue()) {
                $certificationPartner = $certificationPartnerService->reopen($certificationPartner, $user);
            } else if ($currentState === CertificationPartnerStates::DRAFT()->getValue() && $targetState === CertificationPartnerStates::PROCESSING()->getValue()) {
                $certificationPartner = $certificationPartnerService->request($certificationPartner, $user);
            } else {
                if (!$isInternalCertification) {
                    throw new WedofBadRequestHttpException("Erreur, la transition d'état demandée n'est pas autorisée");
                }
                if (!$isCertifier) {
                    throw new WedofAccessDeniedHttpException("Erreur, en tant que partenaire, vous n'êtes pas autorisé à modifier l'état du partenariat.");
                }
                if (in_array($currentState, [CertificationPartnerStates::SUSPENDED()->getValue(), CertificationPartnerStates::REVOKED()->getValue(), CertificationPartnerStates::PROCESSING()->getValue()])
                    && $targetState === CertificationPartnerStates::ACTIVE()->getValue()) {
                    $certificationPartner = $certificationPartnerService->activate($certificationPartner);
                } else if ($currentState === CertificationPartnerStates::ACTIVE()->getValue()) {
                    if ($targetState === CertificationPartnerStates::SUSPENDED()->getValue()) {
                        $certificationPartner = $certificationPartnerService->suspend($certificationPartner);
                    } else if ($targetState === CertificationPartnerStates::REVOKED()->getValue()) {
                        $certificationPartner = $certificationPartnerService->revoke($certificationPartner);
                    }
                } else {
                    throw new WedofBadRequestHttpException("Erreur, la transition d'état demandée n'est pas autorisée");
                }
            }
        }

        $context = (new Context())->addGroup('owner');
        return $this->handleView($this->view($certificationPartner, 200)->setContext($context));
    }

    /**
     * @Rest\Post("/api/certifications/{certifInfo}/partners/{siret}/reinitialize")
     * @Rest\View(statusCode=200)
     * @Entity("certificationPartner", expr="repository.findOneByCertifInfoAndSiret(certifInfo, siret)")
     * @IsGranted(CertificationPartnerVoter::EDIT, subject="certificationPartner")
     *
     * @ApiDoc\Areas({"certificationPartners", "default"})
     * @OA\Post (
     *      summary="Réinitialiser les données du partenariat dans l'état Demande en traitement(processing)",
     *      description="Permet de réinitialiser les données du partenariat qui est dans l'état Demande en traitement(processing)."
     *  )
     * @OA\Response(
     *      response=200,
     *      description="Un json contenant les informations du partenariat",
     *      @OA\MediaType(mediaType="application/json",
     *          @OA\Schema(ref="#/components/schemas/CertificationPartner")
     *      )
     *  )
     * @OA\RequestBody(
     *      @OA\MediaType(mediaType="application/json",
     *          @OA\Schema(ref="#/components/schemas/CertificationPartnerReinitialize")
     *      )
     *  )
     * @param CertificationPartner $certificationPartner
     * @param Request $request
     * @param CertificationPartnerService $certificationPartnerService
     * @return View|Response
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     * @throws WedofSubscriptionException
     */
    public function reinitialize(CertificationPartner $certificationPartner, Request $request, CertificationPartnerService $certificationPartnerService)
    {
        $body = json_decode($request->getContent(), true);
        if (empty($body)) {
            $body = [];
        }
        $violations = $this->validateReinitializeBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }
        /* @var $user User */
        $user = $this->getUser();
        $currentOrganism = $user->getMainOrganism();
        $isCertifier = $certificationPartner->getCertifier() === $currentOrganism || (!$certificationPartner->getCertifier() && $certificationPartner->getCertification()->isCertifier($currentOrganism));
        $certifier = $isCertifier ? $currentOrganism : $certificationPartner->getCertifier();
        $isAllowManagePartnership = $certifier && $certifier->getSubscription() && $certifier->getSubscription()->isAllowCertifierPlus();

        $currentState = $certificationPartner->getState();

        $data = ['comment' => '', 'deleteFiles' => true];
        if ($isCertifier) {
            if (!$isAllowManagePartnership) {
                throw new WedofSubscriptionException("Erreur, en tant que certificateur, vous devez avoir une option dans votre abonnement pour modifier les partenariats.");
            }
            if ($currentState !== CertificationPartnerStates::PROCESSING()->getValue()) {
                throw new WedofAccessDeniedHttpException("Erreur, en tant que certificateur, il n'est pas possible de modifier le partenariat dans son état actuel");
            }
        } else {
            if ($currentState !== CertificationPartnerStates::PROCESSING()->getValue()) {
                throw new WedofAccessDeniedHttpException("Erreur, en tant que partenaire, vous n'êtes pas autorisé à modifier le partenariat dans son état actuel");
            }
        }

        $certificationPartner = $certificationPartnerService->update($certificationPartner, $data, $user);

        $context = (new Context())->addGroup('owner');
        return $this->handleView($this->view($certificationPartner, 200)->setContext($context));
    }

    /**
     * @Rest\Get("/api/certifications/{certifInfo}/partners/{siret}/details")
     * @ParamConverter("certification", options={"mapping": {"certifInfo" = "certifInfo"}})
     * @ParamConverter("organism", options={"mapping": {"siret" = "siret"}})
     * @IsGranted("ROLE_USER", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"certificationPartners", "default"})
     * @OA\Get (
     *     summary="Liste les statistiques d'un partenariat sur une certification.",
     *     description="Récupére les statistiques d'un partenariat d'une certification."
     * )
     *
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les statistiques du partenariat",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/CertificationPartnerDetails")
     *     )
     * )
     *
     * @param Certification $certification
     * @param Organism $organism
     * @param CertificationPartnerService $certificationPartnerService
     * @return CertificationStatistics
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws WedofSubscriptionException
     */
    public function details(Certification $certification, Organism $organism, CertificationPartnerService $certificationPartnerService): CertificationStatistics
    {
        /* @var $user User */
        $user = $this->getUser();
        $currentOrganism = $user->getMainOrganism();
        if ($certification->isCertifier($currentOrganism)) {
            if (!$currentOrganism->getSubscription() || !$currentOrganism->getSubscription()->isAllowCertifiers()) {
                throw new WedofSubscriptionException("Erreur, en tant que certificateur, vous devez avoir un abonnement pour visualiser un partenariat.");
            }
            if ($organism === $currentOrganism) {
                $certificationPartner = $certificationPartnerService->getTransientCertifierSelfCertificationPartner($currentOrganism, $certification);
            } else {
                $certificationPartner = $certificationPartnerService->getByCertificationAndPartner($certification, $organism, !$currentOrganism->getSubscription()->isAllowCertifierPlus());
            }
            // TODO maybe check if partnership state != active then throw error if no subscription option
        } else {
            if ($currentOrganism !== $organism) {
                throw new WedofAccessDeniedHttpException("Vous n'êtes pas autorisé à accéder à ce partenariat");
            }
            $certificationPartner = $certificationPartnerService->getByCertificationAndPartner($certification, $organism, false);
        }
        if (!$certificationPartner) {
            throw new WedofNotFoundHttpException("Le partenariat n'a pas été trouvé.");
        }
        return $certificationPartnerService->getStatistics($certificationPartner);
    }


    /**
     * @Rest\Delete("/api/certifications/{certifInfo}/partners/{siret}")
     * @IsGranted(CertificationPartnerVoter::EDIT, subject="certificationPartner")
     * @Entity("certificationPartner", expr="repository.findOneByCertifInfoAndSiret(certifInfo, siret)")
     * @Rest\View(StatusCode = 204)
     *
     * @ApiDoc\Areas({"certificationPartners", "default"})
     * @OA\Delete (
     *     summary="Supprimer un partenariat",
     *     description="Supprimer un partenariat à l'état Demande à compléter."
     * )
     * @OA\Response(
     *     response=204,
     *     description="Aucun contenu retourné."
     * )
     *
     * @param CertificationPartner $certificationPartner
     * @param CertificationPartnerService $certificationPartnerService
     * @param CertificationPartnerFileService $certificationPartnerFileService
     * @return void
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function delete(CertificationPartner $certificationPartner, CertificationPartnerService $certificationPartnerService, CertificationPartnerFileService $certificationPartnerFileService)
    {
        /* @var $user User */
        $user = $this->getUser();

        if ($certificationPartner->getState() !== CertificationPartnerStates::DRAFT()->getValue()) {
            throw new WedofBadRequestHttpException("Erreur, le partenariat ne peut être supprimé qu'à l'état 'Demande à compléter'.");
        }
        if ($user->getMainOrganism() !== $certificationPartner->getPartner()) {
            throw new  WedofBadRequestHttpException("Erreur, vous ne pouvez pas supprimer ce partenariat.");
        }
        if ($certificationPartner->getFiles()) {
            foreach ($certificationPartner->getFiles() as $file) {
                $certificationPartnerFileService->delete($file, false);
            }
        }
        $certificationPartnerService->delete($certificationPartner);
    }

    /**
     * @Rest\Get("/api/certifications/{certifInfo}/partners/{siret}/files")
     * @IsGranted(CertificationPartnerVoter::EDIT, subject="certificationPartner")
     * @Entity("certificationPartner", expr="repository.findOneByCertifInfoAndSiret(certifInfo, siret)")
     * @Rest\View(StatusCode = 200)
     *
     * @param CertificationPartner $certificationPartner
     * @return array
     */
    public function listAllCertificationPartnersFile(CertificationPartner $certificationPartner): array
    {
        $user = $this->getUser();
        if (!($user instanceof User)) {
            $user = null;
        }

        $roles = $this->getRolesForAuthenticatedUser($certificationPartner, $user);
        $certificationPartnerFileTypes = $certificationPartner->getCertification()->getCertificationPartnerFileTypes($certificationPartner);

        if (($roles['owner'] || $roles['partner']) && $certificationPartnerFileTypes) {
            $certificationPartnerFiles = $certificationPartner->getFiles();
            $listAllCertificationPartnerFile = [];
            foreach ($certificationPartnerFileTypes as $certificationPartnerFileType) {
                $found = false;
                if ($roles['owner'] || ($certificationPartnerFileType['allowVisibilityPartner'] && $roles['partner'])) {
                    foreach ($certificationPartnerFiles as $certificationPartnerFile) {
                        if ($certificationPartnerFileType['id'] == $certificationPartnerFile->getTypeId()) {
                            $found = true;
                            $listAllCertificationPartnerFile[] = [
                                "name" => $certificationPartnerFileType['name'],
                                "fileName" => $certificationPartnerFile->getFileName(),
                                "typeId" => $certificationPartnerFileType['id'],
                                "state" => $certificationPartnerFile->getState(),
                                "link" => $certificationPartnerFile->getLink() ?? $certificationPartnerFile->getFilePath() ?? null,
                                "requiredToState" => $certificationPartnerFileType['toState'] ?? false,
                                "required" => $certificationPartnerFileType['toState'] != null,
                                "allowUpload" => $roles['owner'] || $certificationPartnerFile->getState() !== FileStates::VALID()->getValue() && $certificationPartnerFileType['allowVisibilityPartner'] && $roles['partner'],
                                "type" => $certificationPartnerFileType['accept'],
                                "signedState" => $certificationPartnerFile->getSignedState()
                            ];
                        }
                    }
                    $FREE_FILE_TYPE_NAME = "Document libre";
                    if (!$found || $certificationPartnerFileType['name'] === $FREE_FILE_TYPE_NAME) {
                        $canUpload = $roles['owner'] ? true : $certificationPartnerFileType['allowUploadPartner'];
                        $listAllCertificationPartnerFile[] = [
                            "name" => $certificationPartnerFileType['name'],
                            "typeId" => $certificationPartnerFileType['id'],
                            "state" => FileStates::toFrStringActivity(FileStates::NOT_SUBMITTED()->getValue()),
                            "requiredToState" => $certificationPartnerFileType['toState'] ?? false,
                            "required" => $certificationPartnerFileType['toState'] != null,
                            "allowUpload" => $canUpload,
                            "type" => $certificationPartnerFileType['accept']
                        ];
                    }
                }
            }
            foreach ($certificationPartnerFiles as $certificationPartnerFile) {
                $registrationFileTypeIds = array_column($certificationPartnerFileTypes, 'id');
                $typeIdExist = in_array($certificationPartnerFile->getTypeId(), $registrationFileTypeIds);
                if (!$typeIdExist) {
                    $listAllCertificationPartnerFile[] = [
                        "name" => 'Aucun',
                        "fileName" => $certificationPartnerFile->getFileName(),
                        "typeId" => $certificationPartnerFile->getTypeId(),
                        "state" => $certificationPartnerFile->getState(),
                        "link" => $certificationPartnerFile->getLink() ?? $certificationPartnerFile->getFilePath() ?? null,
                        "requiredToState" => false,
                        "required" => false,
                        "allowUpload" => !(!$roles['owner'] && $certificationPartnerFile->getState() === FileStates::VALID()->getValue()),
                        "signedState" => $certificationPartnerFile->getSignedState()
                    ];
                }
            }
            return $listAllCertificationPartnerFile;
        } else {
            throw new WedofNotFoundHttpException();
        }
    }

    /**
     * @Rest\Post("/api/certificationPartners/{id}/files")
     * @IsGranted(CertificationPartnerVoter::EDIT, subject="certificationPartner")
     * @Rest\View(StatusCode = 200)
     *
     * @param CertificationPartner $certificationPartner
     * @param Request $request
     * @param CertificationPartnerFileService $certificationPartnerFileService
     * @return CertificationPartnerFile[]|Collection
     * @throws Throwable
     */
    public function uploadFile(CertificationPartner $certificationPartner, Request $request, CertificationPartnerFileService $certificationPartnerFileService): Collection
    {
        $typeId = $request->get('typeId');
        $title = $request->get('title');
        $url = $request->get('fileToDownload');
        if ($url) {
            $isValidUrl = filter_var($url, FILTER_VALIDATE_URL);
            if (!$isValidUrl) {
                throw new WedofBadRequestHttpException("Erreur, le champ 'fileToDownload' n'est pas une URL valide.");
            }
            $fullFileName = pathinfo($url, PATHINFO_FILENAME) . '.' . pathinfo($url, PATHINFO_EXTENSION);
            $newFile = sys_get_temp_dir() . '/' . $fullFileName;
            copy($url, $newFile);
            $mimeType = mime_content_type($newFile);
            $file = new UploadedFile($newFile, $fullFileName, $mimeType, null, true);
        } else {
            $file = !empty($request->files->all()) ? $request->files->get('file') : $request->get('file');
        }
        $fileType = $this->getFileType($certificationPartner->getCertification(), $typeId);
        $fileExtensions = array_key_exists('accept', $fileType) ? explode(',', $fileType['accept']) : ['.*'];
        if (!in_array('.*', $fileExtensions)) {
            if (is_file($file)) {
                $mimeTypes = Tools::fileExtensionsToMimeTypes($fileExtensions);
                $violations = $this->validateFile($file, $mimeTypes);
                if (count($violations)) {
                    throw new WedofBadRequestHttpException("Un document de type " . implode($fileExtensions) . " est attendu.");
                }
            } else if (!in_array('link', $fileExtensions)) {
                throw new WedofBadRequestHttpException("Un document de type " . implode($fileExtensions) . " est attendu.");
            }
        }
        if (!empty($typeId) && !empty($file)) {
            /* @var $user User */
            $user = $this->getUser();
            $roles = $this->getRolesForAuthenticatedUser($certificationPartner, $user);
            $isGenerated = $this->isGranted('ROLE_ADMIN') && !empty($fileType['generated']);
            $certificationPartnerFileService->create($file, intval($typeId), $certificationPartner, $isGenerated, $isGenerated ? null : $user, $roles['partner'], $title);
            $certificationPartnerFiles = $certificationPartner->getFiles();
            $filteredCertificationPartnerFiles = new ArrayCollection();
            foreach ($certificationPartnerFiles as $certificationPartnerFile) {
                $fileType = $this->getFileType($certificationPartner->getCertification(), $certificationPartnerFile->getTypeId());
                if (!empty($fileType)) {
                    if ($roles['owner']) {
                        $filteredCertificationPartnerFiles->add($certificationPartnerFile);
                    } else if ($roles['partner'] && $fileType['allowVisibilityPartner']) {
                        $filteredCertificationPartnerFiles->add($certificationPartnerFile);
                    }
                }
            }
            return $filteredCertificationPartnerFiles;
        } else {
            throw new WedofBadRequestHttpException("Erreur, les attributs 'typeId' et 'file' sont obligatoires");
        }
    }

    /**
     * @Rest\Get("/api/certificationPartners/{id}/files/{certificationPartnerFileId}")
     * @IsGranted(CertificationPartnerVoter::EDIT, subject="certificationPartner")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"certificationPartners", "default"})
     * @OA\Get (
     *        summary="Télécharger un document",
     *        description="Télécharger le document d'un partenariat à l'aide de son id et de l'id du document."
     *    )
     * @OA\Response(
     *        response=200,
     *        description="Document à télécharger.",
     *    )
     * @OA\Parameter(
     *       name="id",
     *       in="path",
     *       description="id du partenaire",
     *       @OA\Schema(type="string")
     *   )
     * @OA\Parameter(
     *        name="certificationPartnerFileId",
     *        in="path",
     *        description="id du document",
     *        @OA\Schema(type="string")
     *    )
     * @param Request $request
     * @param CertificationPartner $certificationPartner
     * @param CertificationPartnerFileService $certificationPartnerFileService
     * @param CertificationPartnerFileRepository $repository
     * @return array|string[]|StreamedResponse
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     * @throws WedofConnectionException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws ErrorException
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    public function downloadFile(Request $request, CertificationPartner $certificationPartner, CertificationPartnerFileService $certificationPartnerFileService, CertificationPartnerFileRepository $repository)
    {
        $certificationPartnerFileId = $request->get('certificationPartnerFileId');
        $certificationPartnerFile = $repository->findOneBy(['certificationPartner' => $certificationPartner, 'id' => $certificationPartnerFileId]);
        if (!$certificationPartnerFile) {
            throw new WedofNotFoundHttpException("Aucun fichier d'id $certificationPartnerFileId n'existe pour le partenariat.");
        }
        $fileTypes = $certificationPartner->getCertification()->getCertificationPartnerFileTypes($certificationPartner);
        $fileTypeIndex = array_search($certificationPartnerFile->getTypeId(), array_column($fileTypes, 'id'));
        if ($fileTypeIndex !== false) {
            $fileType = $fileTypes[$fileTypeIndex];
        } else {
            $fileType = array_merge(Certification::getDefaultCertificationPartnerFileTypeRightsForPartner(), Certification::getDefaultCertificationPartnerFileTypeRightsForAttendee());
        }
        $user = $this->getUser();
        if (!($user instanceof User)) {
            $user = null;
        }
        $roles = $this->getRolesForAuthenticatedUser($certificationPartner, $user);
        if (($roles['owner'] || ($fileType['allowVisibilityPartner'] && $roles['partner'])) && $user) {
            return $certificationPartnerFileService->download($certificationPartnerFile, $user);
        } else {
            throw new WedofNotFoundHttpException();
        }
    }

    /**
     * @Rest\Delete("/api/certificationPartners/{id}/files/{certificationPartnerFileId}")
     * @IsGranted(CertificationPartnerVoter::EDIT, subject="certificationPartner")
     * @Rest\View(StatusCode = 204)
     *
     * @param Request $request
     * @param CertificationPartner $certificationPartner
     * @param CertificationPartnerFileService $certificationPartnerFileService
     * @param CertificationPartnerFileRepository $repository
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function deleteFile(CertificationPartner $certificationPartner, CertificationPartnerFileRepository $repository, Request $request, CertificationPartnerFileService $certificationPartnerFileService)
    {
        $certificationPartnerFileId = $request->get('certificationPartnerFileId');
        $certificationPartnerFile = $repository->findOneBy(['certificationPartner' => $certificationPartner, 'id' => $certificationPartnerFileId]);
        if ($certificationPartnerFile) {
            /* @var $user User */
            $user = $this->getUser();
            $roles = $this->getRolesForAuthenticatedUser($certificationPartner, $user);
            $fileState = $certificationPartnerFile->getState();
            if ($roles['owner'] || ($roles['partner'] && $fileState !== FileStates::VALID()->getValue())) {
                $certificationPartnerFileService->delete($certificationPartnerFile, true, $roles['owner'], $user);
            } else {
                throw new WedofBadRequestHttpException("Erreur, vous n'avez pas les droits pour supprimer le fichier");
            }
        } else {
            throw new WedofNotFoundHttpException();
        }
    }

    /**
     * @Rest\Put("/api/certificationPartners/{id}/files/{certificationPartnerFileId}")
     * @IsGranted(CertificationPartnerVoter::EDIT, subject="certificationPartner", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @param CertificationPartner $certificationPartner
     * @param CertificationPartnerFileRepository $repository
     * @param Request $request
     * @param CertificationPartnerFileService $certificationPartnerFileService
     * @return View
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function updateFile(CertificationPartner $certificationPartner, CertificationPartnerFileRepository $repository, Request $request, CertificationPartnerFileService $certificationPartnerFileService): View
    {
        $certificationPartnerFileId = $request->get('certificationPartnerFileId');
        $certificationPartnerFile = $repository->findOneBy(['certificationPartner' => $certificationPartner, 'id' => $certificationPartnerFileId]);
        if ($certificationPartnerFile) {
            /* @var $user User */
            $user = $this->getUser();
            $roles = $this->getRolesForAuthenticatedUser($certificationPartner, $user);
            if ($roles['owner']) {
                $body = json_decode($request->getContent(), true);
                $violations = $this->validateUpdateFileBody($body);
                if (count($violations)) {
                    return $this->view($violations, Response::HTTP_BAD_REQUEST);
                }
                $certificationPartnerFile = $certificationPartnerFileService->updateState($certificationPartnerFile, $body, $user);
                $context = (new Context())->addGroup('owner');
                return $this->view($certificationPartnerFile, 200)->setContext($context);
            } else {
                throw new WedofBadRequestHttpException("Erreur, vous n'avez pas les droits pour modifier l'état du document");
            }
        } else {
            throw new WedofBadRequestHttpException("Erreur, le document n'a pas été trouvé");
        }
    }

    //----------------
    // METHODES PRIVES
    //----------------

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateRequestBody(array $body): ConstraintViolationListInterface
    {
        $validation = Validation::createValidator();
        $constraints = new Assert\Collection([
            'certifInfo' => new Assert\Required([
                new Assert\NotBlank()
            ])
        ]);
        return $validation->validate($body, $constraints);
    }

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateUpdateBody(array $body): ConstraintViolationListInterface
    {
        $validation = Validation::createValidator();
        $constraints = new Assert\Collection([
            'state' => new Assert\Optional([
                new Assert\Choice([
                    CertificationPartnerStates::DRAFT()->getValue(),
                    CertificationPartnerStates::PROCESSING()->getValue(),
                    CertificationPartnerStates::ACTIVE()->getValue(),
                    CertificationPartnerStates::ABORTED()->getValue(),
                    CertificationPartnerStates::REFUSED()->getValue(),
                    CertificationPartnerStates::SUSPENDED()->getValue(),
                    CertificationPartnerStates::REVOKED()->getValue()
                ])
            ]),
            'habilitation' => new Assert\Optional([
                new Assert\Choice([
                    CertificationPartnerHabilitation::TRAIN()->getValue(),
                    CertificationPartnerHabilitation::EVALUATE()->getValue(),
                    CertificationPartnerHabilitation::TRAIN_EVALUATE()->getValue()
                ])
            ]),
            'comment' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 5000])]),
            'pendingActivation' => new Assert\Optional([new Assert\Type('bool')]),
            'pendingRevocation' => new Assert\Optional([new Assert\Type('bool')]),
            'pendingSuspension' => new Assert\Optional([new Assert\Type('bool')]),
            'deleteFiles' => new Assert\Optional([new Assert\Type('bool')]),
            'certifierSiret' => new Assert\Optional([new Assert\NotBlank(), new Assert\Type('string'), new Assert\Length(['min' => 14, 'max' => 14])]),
            'amountHt' => new Assert\Optional(new Assert\Type('float')),
            'compliance' => new Assert\Optional([
                new Assert\AtLeastOneOf([
                    new Assert\Choice([
                        CertificationPartnerAuditResults::COMPLIANT()->getValue(),
                        CertificationPartnerAuditResults::PARTIALLY_COMPLIANT()->getValue(),
                        CertificationPartnerAuditResults::NON_COMPLIANT()->getValue()
                    ]),
                    new Assert\IsNull()
                ])
            ]),
            'tags' => new Assert\Optional(new Assert\Type('array')),
            'metadata' => new Assert\Optional(new Assert\Type('array')),
            'trainingsZone' => new Assert\Optional([
                new Assert\Type('array'),
                new Assert\All([
                    new Assert\Type('string'),
                    new Assert\Length(5)
                ])
            ]),
            'skillSets' => new Assert\Optional([
                new Assert\Type('array'),
                new Assert\All([
                    new Assert\Type('integer')
                ])
            ]),
            'urls' => new Assert\Optional([
                new Assert\Type('array'),
                new Assert\All([
                    new Assert\Type('string')
                ])
            ])
        ]);
        return $validation->validate($body, $constraints);
    }

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateReinitializeBody(array $body): ConstraintViolationListInterface
    {
        $validation = Validation::createValidator();
        $constraints = new Assert\Collection([
        ]);
        return $validation->validate($body, $constraints);
    }

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateUpdateFileBody(array $body): ConstraintViolationListInterface
    {
        $validation = Validation::createValidator();
        $constraints = new Assert\Collection([
            'state' => new Assert\Optional([
                new Assert\Choice([
                    FileStates::REFUSED()->getValue(),
                    FileStates::VALID()->getValue(),
                    FileStates::TO_REVIEW()->getValue()
                ])
            ]),
            'comment' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 1000])])
        ]);
        return $validation->validate($body, $constraints);
    }

    /**
     * @param $file
     * @param array $mimeTypes
     * @return ConstraintViolationListInterface
     */
    private function validateFile($file, array $mimeTypes): ConstraintViolationListInterface
    {
        if ($file->getMimeType() !== "application/octet-stream") {
            $validation = Validation::createValidator();
            $constraints = new Assert\File(['mimeTypes' => $mimeTypes]);
            $violations = $validation->validate($file, $constraints);
        } else {
            $violations = new ConstraintViolationList();
            if (!in_array($file->getClientMimeType(), $mimeTypes)) {
                $violations->add(new ConstraintViolation("ClientMimeType not found in mimeTypes.", null, [], $file, null, $file->getClientMimeType()));
            }
        }
        return $violations;
    }

    /**
     * @param CertificationPartner $certificationPartner
     * @param User|null $user
     * @return array
     */
    private function getRolesForAuthenticatedUser(CertificationPartner $certificationPartner, User $user = null): array
    {
        if ($this->isGranted('ROLE_ADMIN')) {
            return [
                'partner' => false,
                'owner' => true
            ];
        }
        return [
            'partner' => $user && $user->getMainOrganism() !== $certificationPartner->getCertifier(),
            'owner' => $user && $user->getMainOrganism() === $certificationPartner->getCertifier()
        ];
    }

    /**
     * @param Certification $certification
     * @param $typeId
     * @return array|null
     */
    private function getFileType(Certification $certification, $typeId): ?array
    {
        $fileTypes = $certification->getCertificationPartnerFileTypes();
        $fileTypeIndex = array_search($typeId, array_column($fileTypes, 'id'));
        if ($fileTypeIndex !== false) {
            $fileType = $fileTypes[$fileTypeIndex];
        }
        return $fileType ?? array_merge(Certification::getDefaultCertificationPartnerFileTypeRightsForAttendee(), Certification::getDefaultCertificationPartnerFileTypeRightsForPartner());
    }

    /**
     * @param array $skillSets
     * @param CertificationPartner $certificationPartner
     * @param Organism $currentOrganism
     * @param SkillService $skillService
     * @return array
     */
    private function getSkillSets(array $skillSets, CertificationPartner $certificationPartner, Organism $currentOrganism, SkillService $skillService): array
    {
        $skills = [];
        $certification = $certificationPartner->getCertification();
        foreach ($skillSets as $skillSet) {
            $skill = $skillService->getById($skillSet);
            if (!$skill) {
                throw new WedofBadRequestHttpException("Le skillSet d'ID " . $skillSet . " n'existe pas.");
            }
            if ($skill->getType() !== CertificationSkillType::SKILL_SET()->getValue()) {
                throw new WedofBadRequestHttpException("Le skillSet d'ID " . $skillSet . " n'est pas de type 'skillSet'.");
            }
            if ($skill->getCertification() !== $certification) {
                throw new WedofBadRequestHttpException("La certification liée au skillSet d'ID " . $skillSet . " ne correspond pas à la certification du partenariat.");
            }
            if ($certificationPartner->getCertifier() !== $currentOrganism) {
                throw new WedofBadRequestHttpException("Vous devez être le certificateur de ce partenariat pour déclarer des skillSets.");
            }
            $skills[] = $skill;
        }
        return $skills;
    }
}
