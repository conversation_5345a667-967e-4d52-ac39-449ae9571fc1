<?php
// src/Controller/api/TagController.php
namespace App\Controller\api;

use App\Entity\Tag;
use App\Entity\User;
use App\Exception\WedofAccessDeniedHttpException;
use App\Exception\WedofNotFoundHttpException;
use App\Security\Voter\OrganismVoter;
use App\Service\OrganismService;
use App\Service\TagService;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcherInterface;
use Knp\Component\Pager\PaginatorInterface;
use Nelmio\ApiDocBundle\Annotation as ApiDoc;
use OpenApi\Annotations as OA;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints as Assert;

/**
 * Class EvaluationController
 * @package App\Controller\api
 *
 * @OA\Tag(name="Tag")
 * @ApiDoc\Security(name="accessCode")
 */
class TagController extends AbstractFOSRestController
{
    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    /**
     * @Rest\Get("/api/tags")
     * @Rest\QueryParam(name="siret", requirements="\d{14}", nullable=true, description="Permet de n'obtenir que les tags appartenant à l'organisme de siret considéré - par défaut l'organisme de l'utilisateur courant.")
     * @Rest\QueryParam(name="limit", requirements="\d+", default="100", description="Nombre d'éléments retourné par requête - par défaut 100.")
     * @Rest\QueryParam(name="page", requirements="\d+", default="1", description="Numéro de page de la requête - par défaut la première.")
     * @Rest\QueryParam(name="order", requirements=@Assert\Choice({"asc", "desc"}), default="desc", description="Tri les résultats par ordre ascendant ou descendant - par défaut descendant.")
     * @Rest\QueryParam(name="query", requirements=@Assert\Type("string"), nullable=true, description="Filtre les tags")
     * @IsGranted("ROLE_USER", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"tags", "default"})
     * @OA\Get (
     *     summary="Liste les tags.",
     *     description="Liste tous les tags de l'organisme de l'utilisateur courant."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un tableau des tags au format JSON",
     *     @OA\JsonContent(
     *          type="array",
     *          @OA\Items(ref=@ApiDoc\Model(type=Tag::class))
     *     )
     * )
     * @OA\Parameter (name="order", in="query", @OA\Schema (ref="#/components/schemas/Order"))
     *
     * @param ParamFetcherInterface $paramFetcher
     * @param TagService $tagService
     * @param OrganismService $organismService
     * @param PaginatorInterface $paginator
     * @return Response
     */
    public function list(ParamFetcherInterface $paramFetcher, TagService $tagService, OrganismService $organismService, PaginatorInterface $paginator): Response
    {
        $parameters = $paramFetcher->all(true);
        /** @var User $user */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();
        if (isset($parameters['siret'])) {
            $organism = $organismService->getBySiret($parameters['siret']);
            if ($organism) {
                if (!$this->isGranted(OrganismVoter::VIEW, $organism)) {
                    throw new WedofAccessDeniedHttpException("Non autorisé");
                }
            } else {
                throw new WedofNotFoundHttpException("L'organisme associé au siret " . $parameters['siret'] . " n'a pas été trouvé.");
            }
        }
        $data = $paginator->paginate($tagService->listReturnQueryBuilder($organism, $parameters), intval($parameters['page']), intval($parameters['limit']));
        $view = $this->view($data->getItems(), 200);
        $view->setHeader("x-total-count", $data->getTotalItemCount());
        $view->setHeader("x-current-page", $data->getCurrentPageNumber());
        $view->setHeader("x-item-per-page", $data->getItemNumberPerPage());
        return $this->handleView($view);
    }
}
