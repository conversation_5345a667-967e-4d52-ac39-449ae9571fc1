<?php
// src/Controller/api/CertificationPartnerAuditController.php
namespace App\Controller\api;

use App\Entity\Certification;
use App\Entity\CertificationPartner;
use App\Entity\CertificationPartnerAudit;
use App\Entity\CertificationPartnerAuditTemplate;
use App\Entity\Organism;
use App\Entity\User;
use App\Exception\WedofAccessDeniedHttpException;
use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofConnectionException;
use App\Exception\WedofNotFoundHttpException;
use App\Exception\WedofSubscriptionException;
use App\Library\utils\enums\CertificationPartnerAuditCompliances;
use App\Library\utils\enums\CertificationPartnerAuditCriteriaSeverity;
use App\Library\utils\enums\CertificationPartnerAuditResults;
use App\Library\utils\enums\CertificationPartnerAuditStates;
use App\Library\utils\enums\CertificationPartnerStates;
use App\Library\utils\Tools;
use App\Repository\CertificationPartnerAuditFileRepository;
use App\Service\CertificationPartnerAuditFileService;
use App\Service\CertificationPartnerAuditService;
use App\Service\CertificationPartnerAuditTemplateService;
use App\Service\CertificationPartnerService;
use DateTime;
use DateTimeZone;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use ErrorException;
use Exception;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcherInterface;
use FOS\RestBundle\View\View;
use Knp\Component\Pager\PaginatorInterface;
use Nelmio\ApiDocBundle\Annotation as ApiDoc;
use OpenApi\Annotations as OA;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx as XlsxReader;
use PhpOffice\PhpSpreadsheet\RichText\RichText;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx as XlsxWriter;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Entity;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\ParamConverter;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validation;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Throwable;

/**
 * Class CertificationPartnerAuditController
 * @package App\Controller\api
 *
 * @OA\Tag(name="CertificationPartnerAudit")
 * @ApiDoc\Security(name="accessCode")
 */
class CertificationPartnerAuditController extends AbstractFOSRestController
{

    //----------------
    // METHODES PUBLIQUES
    //----------------

    /**
     * @Rest\Get("/api/certifications/{certifInfo}/partners/{siret}/audits/{id}")
     * @ParamConverter("certification", options={"mapping": {"certifInfo" = "certifInfo"}})
     * @ParamConverter("organism", options={"mapping": {"siret" = "siret"}})
     * @Entity("$certificationPartnerAudit", expr="repository.find(id)")
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_CERTIFICATION:READ')", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"certificationPartnerAudits", "default"})
     * @OA\Get (
     *     summary="Récupération d'un audit sur un partenariat via son id.",
     *     description="Récupération d'un audit sur un partenariat via son id associé au certifInfo de la certification et du siret du partenaire."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations du l'audit",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/CertificationPartnerAudit")
     *     )
     * )
     * @OA\Parameter(
     *     name="id",
     *     in="path",
     *     description="id de l'audit",
     *     @OA\Schema(type="string")
     * )
     *
     * @param CertificationPartnerAudit $certificationPartnerAudit
     * @param Certification $certification
     * @param Organism $organism
     * @param CertificationPartnerService $certificationPartnerService
     * @return CertificationPartnerAudit
     * @throws WedofSubscriptionException
     */
    public function show(CertificationPartnerAudit $certificationPartnerAudit, Certification $certification, Organism $organism, CertificationPartnerService $certificationPartnerService): CertificationPartnerAudit
    {
        /** @var User $user */
        $user = $this->getUser();
        $certificationPartner = $this->getCertificationPartner($certification, $organism, $certificationPartnerService, $user);
        if ($certificationPartner !== $certificationPartnerAudit->getCertificationPartner()) {
            throw new WedofBadRequestHttpException('Erreur, le partenaire ne correspond pas à l\'audit');
        }
        $roles = $this->getRolesForAuthenticatedUser($certificationPartner, $user);
        if ($roles['owner'] || ($roles['partner'] && $certificationPartnerAudit->getState() === CertificationPartnerAuditStates::COMPLETED()->getValue())) {
            return $certificationPartnerAudit;
        } else {
            throw new WedofBadRequestHttpException("Erreur, vous n'avez pas les droits pour voir l'audit associé à l'id " . $certificationPartnerAudit->getId());
        }
    }

    /**
     * @Rest\Get("/api/certifications/{certifInfo}/partners/{siret}/audits")
     * @ParamConverter("certification", options={"mapping": {"certifInfo" = "certifInfo"}})
     * @ParamConverter("organism", options={"mapping": {"siret" = "siret"}})
     * @Rest\QueryParam(name="limit", requirements="\d+", default="100")
     * @Rest\QueryParam(name="state", requirements=@Assert\Type("string"), default="all", description="Permet de n'obtenir que les audits dans l'état considéré - par défaut tous les états sont retournés. Valeurs possibles : 'all', 'pendingComputation', 'computing', 'inProgress', 'completed'. Il est possible de demander plusieurs états en séparant chaque état par une virgule, ex : 'inProgress,completed'.")
     * @Rest\QueryParam(name="page", requirements="\d+", default="1")
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_CERTIFICATION:READ')", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"certificationPartnerAudits", "default"})
     * @OA\Get (
     *     summary="Liste les audits sur un partenariat.",
     *     description="Récupére l'ensemble des audits d'un partenariat de certification."
     * )
     *
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations des audits",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/CertificationPartnerAudit")
     *     )
     * )
     *
     *
     * @param Certification $certification
     * @param Organism $organism
     * @param ParamFetcherInterface $paramFetcher
     * @param CertificationPartnerService $certificationPartnerService
     * @param CertificationPartnerAuditService $certificationPartnerAuditService
     * @param PaginatorInterface $paginator
     * @return Response
     * @throws WedofSubscriptionException
     */
    public function list(Certification $certification, Organism $organism, ParamFetcherInterface $paramFetcher, CertificationPartnerService $certificationPartnerService, CertificationPartnerAuditService $certificationPartnerAuditService, PaginatorInterface $paginator): Response
    {
        $parameters = $paramFetcher->all(true);
        /** @var User $user */
        $user = $this->getUser();
        $certificationPartner = $this->getCertificationPartner($certification, $organism, $certificationPartnerService, $user);
        $isCertifier = $certificationPartner->getCertifier() && $certificationPartner->getCertifier() === $user->getMainOrganism();
        if (!$isCertifier) {
            $parameters['state'] = CertificationPartnerAuditStates::COMPLETED()->getValue();
            $parameters['allowVisibilityPartner'] = true;
        }
        if (isset($parameters['state'])) {
            $parameters['state'] = explode(",", $parameters['state']);
            foreach ($parameters['state'] as $state) {
                if (!in_array($state, CertificationPartnerAuditStates::valuesStates())) {
                    throw new WedofBadRequestHttpException("Erreur sur les valeurs renvoyées 'state', elles doivent être : " . join(",", CertificationPartnerAuditStates::valuesStates()) . ".");
                }
            }
        }
        $data = $paginator->paginate($certificationPartnerAuditService->listReturnQueryBuilder($parameters, $certificationPartner), intval($parameters['page']), intval($parameters['limit']));
        $view = $this->view($data->getItems(), 200);
        $view->setHeader("x-total-count", $data->getTotalItemCount());
        $view->setHeader("x-current-page", $data->getCurrentPageNumber());
        $view->setHeader("x-item-per-page", $data->getItemNumberPerPage());
        return $this->handleView($view);
    }

    /**
     * @Rest\Post("/api/certifications/{certifInfo}/partners/{siret}/audits")
     * @ParamConverter("certification", options={"mapping": {"certifInfo" = "certifInfo"}})
     * @ParamConverter("organism", options={"mapping": {"siret" = "siret"}})
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_CERTIFICATION:READ')", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"certificationPartnerAudits", "default"})
     * @OA\Post (
     *     summary="Créer un audit sur un partenariat de certification",
     *     description="Permet de créer un audit sur un partenariat de certification. Via OAuth2, cet appel nécessite le scope 'certification:read'"
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant l'audit créé",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/CertificationPartnerAudit")
     *     )
     * )
     *
     * @OA\RequestBody(
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/CertificationPartnerAuditCreateBody")
     *     )
     * )
     *
     * @param Certification $certification
     * @param Organism $organism
     * @param Request $request
     * @param CertificationPartnerService $certificationPartnerService
     * @param CertificationPartnerAuditService $certificationPartnerAuditService
     * @param CertificationPartnerAuditTemplateService $certificationPartnerAuditTemplateService
     * @return CertificationPartnerAudit|View
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     * @throws WedofSubscriptionException
     */
    public function create(Certification $certification, Organism $organism, Request $request, CertificationPartnerService $certificationPartnerService, CertificationPartnerAuditService $certificationPartnerAuditService, CertificationPartnerAuditTemplateService $certificationPartnerAuditTemplateService)
    {
        /* @var $user User */
        $user = $this->getUser();
        $currentOrganism = $user->getMainOrganism();
        $certificationPartner = $this->getCertificationPartner($certification, $organism, $certificationPartnerService, $user);
        if ($certificationPartner->getCertifier() !== $currentOrganism) {
            throw new WedofAccessDeniedHttpException('Erreur, seul le certificateur du partenariat peut créer un audit');
        }
        if (!in_array($certificationPartner->getState(), [CertificationPartnerStates::ACTIVE()->getValue(), CertificationPartnerStates::SUSPENDED()->getValue(), CertificationPartnerStates::REVOKED()->getValue()])) {
            throw new WedofBadRequestHttpException("Erreur, un audit ne peut être créer que si le partenariat est à l'état : actif, revoqué ou suspendu.");
        }
        $body = json_decode($request->getContent(), true);
        $violations = $this->validateCreateBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }

        $certificationPartnerAuditTemplate = $certificationPartnerAuditTemplateService->getById($body['templateId']);
        if (!$certificationPartnerAuditTemplate) {
            throw new WedofBadRequestHttpException("Erreur, le modèle d'audit n'a pas été trouvé");
        }
        if ($certificationPartnerAuditTemplate->getCertification() !== $certification) {
            throw new WedofAccessDeniedHttpException("Erreur, le modèle d'audit ne correspond pas à la certification");
        }

        return $certificationPartnerAuditService->create($certificationPartner, $certificationPartnerAuditTemplate, $user);
    }

    /**
     * @Rest\Post("/api/certifications/{certifInfo}/partners/audits")
     * @ParamConverter("certification", options={"mapping": {"certifInfo" = "certifInfo"}})
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_CERTIFICATION:READ')", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"certificationPartnerAudits", "default"})
     * @OA\Post (
     *     summary="Générer un audit général sur les partenaires d'une certification",
     *     description="Permet de générer et clôturer un audit pour chacun des partenariats (actifs) de certification. Il est possible de mettre à jour la conformité du partenaire et suspendre le partenariat en cas de non-conformité. Via OAuth2, cet appel nécessite le scope 'certification:read'"
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant l'audit généré",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/CertificationPartnerAuditGenerateOnPartners")
     *     )
     * )
     *
     * @OA\RequestBody(
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/CertificationPartnerAuditCreateOnPartnersBody")
     *     )
     * )
     *
     * @param Certification $certification
     * @param Request $request
     * @param CertificationPartnerService $certificationPartnerService
     * @param CertificationPartnerAuditService $certificationPartnerAuditService
     * @param CertificationPartnerAuditTemplateService $certificationPartnerAuditTemplateService
     * @return array|View
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function createOnPartners(Certification $certification, Request $request, CertificationPartnerService $certificationPartnerService, CertificationPartnerAuditService $certificationPartnerAuditService, CertificationPartnerAuditTemplateService $certificationPartnerAuditTemplateService)
    {
        ini_set('max_execution_time', -1);
        /* @var $user User */
        $user = $this->getUser();
        $currentOrganism = $user->getMainOrganism();

//        if (!$user->isOwner()) {
//            throw new WedofBadRequestHttpException("Erreur, seul le compte propriétaire de l'organisme peut lancer un audit général");
//        }
//        if ($currentOrganism->getSiret() === '48096959100068') {
//            $now = new DateTime('now');
//            if (((int)$now->format('H')) > 12) {
//                throw new WedofBadRequestHttpException("Erreur, l'audit général doit être lancé le matin");
//            }
//        }

        $body = json_decode($request->getContent(), true);
        $violations = $this->validateCreateOnPartnersBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }

        $complete = filter_var($body['complete'], FILTER_VALIDATE_BOOLEAN);
        $updateCompliance = filter_var($body['updateCompliance'] ?? false, FILTER_VALIDATE_BOOLEAN);
        $suspend = filter_var($body['suspend'] ?? false, FILTER_VALIDATE_BOOLEAN);

        $partnerStates = [CertificationPartnerStates::ACTIVE()];
        if (!empty($body['partnerState'])) {
            $partnerStates = explode(",", $body['partnerState']);
            foreach ($partnerStates as $partnerState) {
                if (!in_array($partnerState, CertificationPartnerStates::valuesStates())) {
                    throw new WedofBadRequestHttpException("Erreur sur les valeurs renvoyées 'partnerState', elles doivent être : " . join(",", CertificationPartnerStates::valuesStates()) . ".");
                }
            }
        }
        $partnerCompliances = [CertificationPartnerAuditResults::ALL()];
        if (!empty($body['partnerCompliance'])) {
            $partnerCompliances = explode(",", $body['partnerCompliance']);
            foreach ($partnerCompliances as $partnerCompliance) {
                if (!in_array($partnerCompliance, CertificationPartnerAuditResults::valuesResults())) {
                    throw new WedofBadRequestHttpException("Erreur sur les valeurs renvoyées 'partnerCompliance', elles doivent être : " . join(",", CertificationPartnerAuditResults::valuesResults()) . ".");
                }
            }
        }
        if (($updateCompliance || $suspend) && !$complete) {
            throw new WedofBadRequestHttpException("Erreur, vous ne pouvez pas mettre à jour la conformité du partenariat ou la suspension du partenariat en cas de non-conformité si l'audit ne peut pas être clôturé");
        }

        $certificationPartnerAuditTemplate = $this->checkAccessOnAuditTemplate($certification, $body['templateId'], $certificationPartnerAuditTemplateService, $currentOrganism);
        $partners = $this->getPartnersForAudit($certification, $currentOrganism, $certificationPartnerService, $partnerStates, $partnerCompliances);
        $certificationPartnerAuditService->createOnPartners($partners, $certificationPartnerAuditTemplate, $user, $complete, $updateCompliance, $suspend);
        return ['count' => count($partners), 'auditTemplate' => $certificationPartnerAuditTemplate];
    }

    /**
     * @Rest\Put("/api/certifications/{certifInfo}/partners/{siret}/audits/{id}")
     * @ParamConverter("certification", options={"mapping": {"certifInfo" = "certifInfo"}})
     * @ParamConverter("organism", options={"mapping": {"siret" = "siret"}})
     * @Entity("$certificationPartnerAudit", expr="repository.find(id)")
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_CERTIFICATION:READ')", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"certificationPartnerAudits", "default"})
     * @OA\Put (
     *     summary="Relancer un audit d'un partenariat de certification",
     *     description="Permet de relancer l'audit d'un partenariat de certification si il a échoué. Via OAuth2, cet appel nécessite le scope 'certification:read'"
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant l'audit modifié",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/CertificationPartnerAudit")
     *     )
     * )
     *
     * @OA\RequestBody(
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/CertificationPartnerAuditUpdateBody")
     *     )
     * )
     *
     * @param CertificationPartnerAudit $certificationPartnerAudit
     * @param Certification $certification
     * @param Organism $organism
     * @param Request $request
     * @param CertificationPartnerService $certificationPartnerService
     * @param CertificationPartnerAuditService $certificationPartnerAuditService
     * @return CertificationPartnerAudit|View
     * @throws WedofSubscriptionException
     * @throws Exception|Throwable
     */
    public function update(CertificationPartnerAudit $certificationPartnerAudit, Certification $certification, Organism $organism, Request $request, CertificationPartnerService $certificationPartnerService, CertificationPartnerAuditService $certificationPartnerAuditService)
    {
        /* @var $user User */
        $user = $this->getUser();
        $certificationPartner = $this->getCertificationPartner($certification, $organism, $certificationPartnerService, $user);
        if ($certificationPartner !== $certificationPartnerAudit->getCertificationPartner()) {
            throw new WedofBadRequestHttpException('Erreur, le partenaire ne correspond pas à l\'audit');
        }
        if (!in_array($certificationPartner->getState(), [CertificationPartnerStates::ACTIVE()->getValue(), CertificationPartnerStates::SUSPENDED()->getValue(), CertificationPartnerStates::REVOKED()->getValue()])) {
            throw new WedofBadRequestHttpException("Erreur, un audit ne peut être créer que si le partenariat est à l'état : actif, revoqué ou suspendu.");
        }
        $currentOrganism = $user->getMainOrganism();
        if ($certificationPartner->getCertifier() !== $currentOrganism) {
            throw new WedofAccessDeniedHttpException('Erreur, seul le certificateur du partenariat peut mettre à jour un audit');
        }

        $body = json_decode($request->getContent(), true);

        $violations = $this->validateUpdateBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }

        if (isset($body['restartAudit']) && $body['restartAudit'] === true) {
            if (!$this->isGranted('IS_IMPERSONATOR')) {
                throw new WedofBadRequestHttpException("Erreur, vous n'avez pas les droits pour relancer un audit.");
            }
            if ($certificationPartnerAudit->getState() !== CertificationPartnerAuditStates::FAILED()->getValue()) {
                throw new WedofBadRequestHttpException("Erreur, vous ne pouvez pas relancer un audit à l'état " . CertificationPartnerAuditStates::toFrString($certificationPartnerAudit->getState()) . ".");
            }
        }

        return $certificationPartnerAuditService->update($certificationPartnerAudit, $body, $user);
    }

    /**
     * @Rest\Get("/app/certifications/{certifInfo}/partners/sampleTrainingTitles")
     * @ParamConverter("certification", options={"mapping": {"certifInfo" = "certifInfo"}})
     * @Security("is_granted('ROLE_USER')", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @param CertificationPartnerAuditService $certificationPartnerAuditService
     * @param Certification $certification
     * @return array
     */
    public function getTrainingTitles(CertificationPartnerAuditService $certificationPartnerAuditService, Certification $certification): array
    {
        /* @var $user User */
        $user = $this->getUser();
        $currentOrganism = $user->getMainOrganism();
        if (!$certification->isCertifier($currentOrganism) && !$this->isGranted('ROLE_ADMIN')) {
            throw new WedofAccessDeniedHttpException('Erreur, seul le certificateur peut utiliser cette fonctionnalité');
        }
        return $certificationPartnerAuditService->getTrainingTitles($certification);
    }

    /**
     * @Rest\Post("/api/certifications/{certifInfo}/partners/{siret}/audits/{id}/generateReport")
     * @ParamConverter("certification", options={"mapping": {"certifInfo" = "certifInfo"}})
     * @ParamConverter("organism", options={"mapping": {"siret" = "siret"}})
     * @Entity("$certificationPartnerAudit", expr="repository.find(id)")
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_CERTIFICATION:READ')", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"certificationPartnerAudits", "default"})
     * @OA\Post (
     *     summary="Générer le rapport d'audit du partenariat de certification",
     *     description="Permet de générer le rapport d'audit du partenariat de certification. Via OAuth2, cet appel nécessite le scope 'certification:read'"
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant l'audit modifié",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/CertificationPartnerAudit")
     *     )
     * )
     *
     * @param CertificationPartnerAudit $certificationPartnerAudit
     * @param Certification $certification
     * @param Organism $organism
     * @param CertificationPartnerService $certificationPartnerService
     * @param CertificationPartnerAuditService $certificationPartnerAuditService
     * @return CertificationPartnerAudit
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     * @throws WedofSubscriptionException
     * @throws WedofConnectionException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws ErrorException
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    public function generateReport(CertificationPartnerAudit $certificationPartnerAudit, Certification $certification, Organism $organism, CertificationPartnerService $certificationPartnerService, CertificationPartnerAuditService $certificationPartnerAuditService): CertificationPartnerAudit
    {
        /* @var $user User */
        $user = $this->getUser();
        $certificationPartner = $this->getCertificationPartner($certification, $organism, $certificationPartnerService, $user);
        if ($certificationPartner !== $certificationPartnerAudit->getCertificationPartner()) {
            throw new WedofBadRequestHttpException('Erreur, le partenaire ne correspond pas à l\'audit');
        }
        $auditTemplate = $certificationPartnerAudit->getTemplate();
        $currentOrganism = $user->getMainOrganism();
        $isCertifier = $certificationPartner->getCertifier() === $currentOrganism;
        $isPartner = $certificationPartner->getPartner() === $currentOrganism;
        if (!$isCertifier && !$isPartner || ($isPartner && !$auditTemplate->isAllowVisibilityPartner())) {
            throw new WedofAccessDeniedHttpException("Erreur, vous n'avez pas les droits pour générer le rapport d'audit");
        }
        if ($isPartner && $certificationPartnerAudit->getState() !== CertificationPartnerAuditStates::COMPLETED()->getValue()) {
            throw new WedofAccessDeniedHttpException("Erreur, vous ne pouvez pas générer le rapport d'audit pour un audit non finalisé");
        }
        $targetFileType = $isPartner || ($isCertifier && $certificationPartnerAudit->getState() === CertificationPartnerAuditStates::COMPLETED()->getValue()) ? 'pdf' : $auditTemplate->getAuditTemplateFileType()['targetFileType'];
        return $certificationPartnerAuditService->generateReport($certificationPartnerAudit, $user, $targetFileType);
    }

    /**
     * @Rest\Post("/api/certifications/{certifInfo}/partners/{siret}/audits/{id}/complete")
     * @ParamConverter("certification", options={"mapping": {"certifInfo" = "certifInfo"}})
     * @ParamConverter("organism", options={"mapping": {"siret" = "siret"}})
     * @Entity("$certificationPartnerAudit", expr="repository.find(id)")
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_CERTIFICATION:READ')", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"certificationPartnerAudits", "default"})
     * @OA\Post (
     *     summary="Finaliser le rapport d'audit du partenariat de certification",
     *     description="Permet de finaliser le rapport d'audit du partenariat de certification. Via OAuth2, cet appel nécessite le scope 'certification:read'"
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant l'audit modifié",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/CertificationPartnerAudit")
     *     )
     * )
     * @OA\RequestBody(
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/CertificationPartnerAuditCompleteBody")
     *     )
     * )
     *
     * @param CertificationPartnerAudit $certificationPartnerAudit
     * @param Certification $certification
     * @param Organism $organism
     * @param Request $request
     * @param CertificationPartnerService $certificationPartnerService
     * @param CertificationPartnerAuditService $certificationPartnerAuditService
     * @return CertificationPartnerAudit|View
     * @throws WedofSubscriptionException
     * @throws Exception|Throwable
     */
    public function complete(CertificationPartnerAudit $certificationPartnerAudit, Certification $certification, Organism $organism, Request $request, CertificationPartnerService $certificationPartnerService, CertificationPartnerAuditService $certificationPartnerAuditService)
    {
        /* @var $user User */
        $user = $this->getUser();
        $certificationPartner = $this->getCertificationPartner($certification, $organism, $certificationPartnerService, $user);
        if ($certificationPartner !== $certificationPartnerAudit->getCertificationPartner()) {
            throw new WedofBadRequestHttpException('Erreur, le partenaire ne correspond pas à l\'audit');
        }
        $currentOrganism = $user->getMainOrganism();
        if ($certificationPartner->getCertifier() !== $currentOrganism) {
            throw new WedofAccessDeniedHttpException('Erreur, seul le certificateur du partenariat peut finaliser un audit');
        }

        $body = json_decode($request->getContent(), true) ?? [];

        if (!empty($body['endDate'])) {
            if (strtotime($body['endDate'])) {
                $body['endDate'] = (new DateTime($body['endDate']))->setTimezone(new DateTimeZone('UTC'));
            }
        }
        if (empty($body['endDate'])) {
            $body['endDate'] = new DateTime();
        }

        $violations = $this->validateCompleteBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }

        return $certificationPartnerAuditService->complete($certificationPartnerAudit, $body, $user);
    }

    /**
     * @Rest\Delete("/api/certifications/partners/audits/{id}")
     * @Rest\View(StatusCode = 204)
     * @Security("is_granted('ROLE_ADMIN') or is_granted('ROLE_ALLOWED_TO_SWITCH') or is_granted('IS_IMPERSONATOR')", message="not allowed")
     *
     * @param CertificationPartnerAudit $certificationPartnerAudit
     * @param CertificationPartnerAuditService $certificationPartnerAuditService
     * @return void
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function delete(CertificationPartnerAudit $certificationPartnerAudit, CertificationPartnerAuditService $certificationPartnerAuditService)
    {
        $certification = $certificationPartnerAudit->getCertificationPartner()->getCertification();
        if (!$certification->isAllowAudits()) {
            throw new WedofSubscriptionException("Erreur, votre abonnement ne permet pas la suppresion d'un audit.");
        }
        $certificationPartnerAuditService->delete($certificationPartnerAudit);
    }

    /**
     * @Rest\Get("/api/certifications/{certifInfo}/partners/{siret}/audits/{id}/files/{idFile}")
     * @ParamConverter("certification", options={"mapping": {"certifInfo" = "certifInfo"}})
     * @ParamConverter("organism", options={"mapping": {"siret" = "siret"}})
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_CERTIFICATION:READ')", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"certificationPartnerAudits", "default"})
     * @OA\Get (
     *       summary="Télécharger le rapport d'audit d'un partenariat de certification",
     *       description="Télécharger le rapport d'audit du partenaire via l'id de l'audit et de l'id du document."
     *   )
     * @OA\Response(
     *       response=200,
     *       description="Document à télécharger.",
     *   )
     * @OA\Parameter(
     *      name="id",
     *      in="path",
     *      description="id de l'audit",
     *      @OA\Schema(type="string")
     *  )
     * @OA\Parameter(
     *       name="idFile",
     *       in="path",
     *       description="id du document",
     *       @OA\Schema(type="string")
     *   )
     *
     * @param CertificationPartnerAudit $certificationPartnerAudit
     * @param CertificationPartnerAuditFileService $certificationPartnerAuditFileService
     * @param CertificationPartnerAuditFileRepository $repository
     * @param int $idFile
     * @param Certification $certification
     * @param Organism $organism
     * @param CertificationPartnerService $certificationPartnerService
     * @return array|string[]|StreamedResponse
     * @throws WedofSubscriptionException
     */
    public function downloadFile(CertificationPartnerAudit $certificationPartnerAudit, CertificationPartnerAuditFileService $certificationPartnerAuditFileService, CertificationPartnerAuditFileRepository $repository, int $idFile, Certification $certification, Organism $organism, CertificationPartnerService $certificationPartnerService)
    {
        /** @var User $user */
        $user = $this->getUser();
        $certificationPartner = $this->getCertificationPartner($certification, $organism, $certificationPartnerService, $user);
        $roles = $this->getRolesForAuthenticatedUser($certificationPartner, $user);
        if ($roles['owner'] || $roles['partner']) {
            $certificationPartnerAuditFile = $repository->findOneBy(['certificationPartnerAudit' => $certificationPartnerAudit, 'id' => $idFile]);
            if ($certificationPartnerAuditFile) {
                return $certificationPartnerAuditFileService->download($certificationPartnerAuditFile);
            } else {
                throw new WedofNotFoundHttpException("Aucun fichier d'id " . $idFile . " n'existe pour l'audit.");
            }
        } else {
            throw new WedofBadRequestHttpException("Erreur, vous n'avez pas les droits pour visualiser le fichier");
        }
    }

    /**
     * @Rest\Get("/api/certifications/{certifInfo}/partners/audits/{templateId}/report")
     * @ParamConverter("certification", options={"mapping": {"certifInfo" = "certifInfo"}})
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_CERTIFICATION:READ')", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"certificationPartnerAudits", "default"})
     * @OA\Get (
     *     summary="Liste les résultats d'un modèle d'audits sur une certification.",
     *     description="Récupère l'ensemble des résultats d'audits à partir d'un modèle d'audit d'une certification."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un tableau de résultats d'audit pour chaque partenaire d'une certification au format CSV",
     * )
     *
     * @param int $templateId
     * @param CertificationPartnerAuditService $certificationPartnerAuditService
     * @param PaginatorInterface $paginator
     * @param CertificationPartnerAuditTemplateService $certificationPartnerAuditTemplateService
     * @param EntityManagerInterface $entityManager
     * @return Response
     * @throws WedofSubscriptionException
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
     */
    public function exportAuditsReport(int $templateId, CertificationPartnerAuditService $certificationPartnerAuditService, PaginatorInterface $paginator, CertificationPartnerAuditTemplateService $certificationPartnerAuditTemplateService, EntityManagerInterface $entityManager): Response
    {
        /** @var User $user */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();

        $template = $certificationPartnerAuditTemplateService->getById($templateId);
        if (!$template) {
            throw new WedofBadRequestHttpException("Erreur, le modèle d'audit associé à l'id " . $templateId . " n'a pas été trouvé.");
        }
        if ($template->getCertifier() !== $organism) {
            throw new WedofBadRequestHttpException("Erreur, vous n'avez pas les droits pour accèder à ce modèle d'audit.");
        }
        if (!$template->getCertification()->isAllowAudits()) {
            throw new WedofSubscriptionException("Erreur, votre abonnement ne permet pas la gestion des audits.");
        }

        $parameters = [
            'states' => [
                CertificationPartnerAuditStates::IN_PROGRESS()->getValue(),
                CertificationPartnerAuditStates::FAILED()->getValue(),
                CertificationPartnerAuditStates::COMPLETED()->getValue()
            ],
            'certificationPartnerStates' => [
                CertificationPartnerStates::ACTIVE()->getValue(),
                CertificationPartnerStates::SUSPENDED()->getValue()
            ],
            'template' => $template,
        ];

        set_time_limit(300);
        $page = 1;
        $limit = 100;
        $nbPages = 1;
        $entityManager->getConnection()->getConfiguration()->setSQLLogger();

        $reader = new XlsxReader();
        $templateFileName = $this->getParameter('kernel.project_dir') . '/data/audit.xlsx';
        $spreadsheet = $reader->load($templateFileName);
        $worksheet = $spreadsheet->getActiveSheet();

        $highestColumn = $worksheet->getHighestColumn(1);

        foreach ($template->getCriterias() as $criteria) {
            // colonne conformité
            $column = ++$highestColumn; // get next column
            $column1Title = $criteria['scopeTitle'];
            $worksheet->insertNewColumnBefore($column)->setCellValue($column . 1, $column1Title);
            $requirement = $criteria['scopeTitle'] . ' ' . $criteria['operationTitle'] . ' ' . $criteria['parameter'];
            $richText = new RichText();
            $richText->createText($requirement);
            $worksheet->getComment($column . 1)->setText($richText);
            // colonne valeur
            $column2 = ++$highestColumn; // get next column
            $column2Title = $criteria['scopeTitle'] . " (Valeur enregistrée)";
            $worksheet->insertNewColumnBefore($column2)->setCellValue($column2 . 1, $column2Title);
            // colonne severity
            $column3 = ++$highestColumn; // get next column
            $worksheet->insertNewColumnBefore($column3)->setCellValue($column3 . 1, "Sévérité");
        }

        $rowIndex = 2;

        while ($page <= $nbPages) {
            $data = $paginator->paginate($certificationPartnerAuditService->listAuditReport($parameters), $page, $limit);
            /** @var CertificationPartnerAudit $certificationPartnerAudit */
            foreach ($data->getItems() as $certificationPartnerAudit) {
                $row = $this->getSpreadsheetRowFromCertificationPartnerAudit($certificationPartnerAudit);
                $columnIndex = 1;
                foreach ($row as $columnName => $cellValue) {
                    $coordinates = [$columnIndex, $rowIndex];
                    if (is_array($cellValue)) {
                        $worksheet->setCellValue($coordinates, implode(', ', $cellValue));
                    } else {
                        $worksheet->setCellValue($coordinates, $cellValue);
                    }
                    if ($columnName === 'resultat' && $cellValue === 'Non conforme') {
                        $worksheet->getStyle($coordinates)->applyFromArray([
                            'fill' => ['color' => ['rgb' => 'C81E1E']],
                            'font' => ['color' => ['rgb' => 'FFFFFF']]
                        ]);
                    }
                    $columnIndex++;
                }
                $rowIndex++;
            }
            $worksheet->getStyle('A1:' . $highestColumn . ($data->getTotalItemCount() + 1))->applyFromArray([
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                        'rgb' => '000000'
                    ]
                ]
            ]);
            if ($page === 1) {
                $nbPages = intdiv($data->getTotalItemCount(), $limit) + 1;
            }
            $page++;
            $entityManager->clear();
        }

        $writer = new XlsxWriter($spreadsheet);
        return Tools::getExcelResponse($writer, 'rapport audits - partenariats actifs / suspendus');
    }

    //----------------
    // METHODES PRIVES
    //----------------

    /**
     * @param Certification $certification
     * @param Organism $organism
     * @param CertificationPartnerService $certificationPartnerService
     * @param User $user
     * @return CertificationPartner
     * @throws WedofSubscriptionException
     */
    private function getCertificationPartner(Certification $certification, Organism $organism, CertificationPartnerService $certificationPartnerService, User $user): CertificationPartner
    {
        // TODO(audit) improve security / probably return error if ceritfier has no subscription ?
        $currentOrganism = $user->getMainOrganism();
        if ($certification->isCertifier($currentOrganism)) {
            if (!$currentOrganism->getSubscription() || !$currentOrganism->getSubscription()->isAllowCertifiers()) {
                throw new WedofSubscriptionException("Erreur, en tant que certificateur, vous devez avoir un abonnement pour visualiser les données d'un partenariat.");
            }
            if (!$certification->isAllowAudits()) {
                throw new WedofSubscriptionException("Erreur, votre abonnement ne permet pas l'accès aux audits.");
            }
            $certificationPartner = $certificationPartnerService->getByCertificationAndPartner($certification, $organism, !$currentOrganism->getSubscription()->isAllowCertifierPlus());
        } else {
            if ($currentOrganism !== $organism) {
                throw new WedofAccessDeniedHttpException("Vous n'êtes pas autorisé à accéder à ce partenariat");
            }
            $certificationPartner = $certificationPartnerService->getByCertificationAndPartner($certification, $organism, false);
        }
        return $certificationPartner;
    }

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateCreateBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'templateId' => new Assert\Required(new Assert\Type('integer')),
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateCreateOnPartnersBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'templateId' => new Assert\Required(new Assert\Type('integer')),
            'complete' => new Assert\Required(new Assert\Type('bool')),
            'updateCompliance' => new Assert\Optional(new Assert\Type('bool')),
            'suspend' => new Assert\Optional(new Assert\Type('bool')),
            'partnerCompliance' => new Assert\Optional(new Assert\Type('string'))
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateUpdateBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'restartAudit' => new Assert\Optional([new Assert\Type('bool')]),
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateCompleteBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'result' => new Assert\Required([new Assert\Choice([CertificationPartnerAuditResults::COMPLIANT()->getValue(), CertificationPartnerAuditResults::PARTIALLY_COMPLIANT()->getValue(), CertificationPartnerAuditResults::NON_COMPLIANT()->getValue()])]),
            'endDate' => new Assert\Required(new Assert\Type('datetime')),
            'notes' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 2500])]),
            'updatePartnerCompliance' => new Assert\Required(new Assert\Type('boolean'))
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param CertificationPartner $certificationPartner
     * @param User|null $user
     * @return array
     */
    private function getRolesForAuthenticatedUser(CertificationPartner $certificationPartner, User $user = null): array
    {
        if ($this->isGranted('ROLE_ADMIN')) {
            return [
                'partner' => false,
                'owner' => true
            ];
        }
        return [
            'partner' => $user && $user->getMainOrganism() !== $certificationPartner->getCertifier(),
            'owner' => $user && $user->getMainOrganism() === $certificationPartner->getCertifier()
        ];
    }

    /**
     * @param CertificationPartnerAudit $certificationPartnerAudit
     * @return array
     */
    private function getSpreadsheetRowFromCertificationPartnerAudit(CertificationPartnerAudit $certificationPartnerAudit): array
    {
        $partner = $certificationPartnerAudit->getCertificationPartner()->getPartner();
        $templateAudit = $certificationPartnerAudit->getTemplate();

        $row = [
            'siret' => $partner->getSiret(),
            'nom' => $partner->getName(),
            'startDate' => $certificationPartnerAudit->getStartDate()->format('d/m/Y'),
            'resultat' => $certificationPartnerAudit->getResult() ? CertificationPartnerAuditResults::toFrString($certificationPartnerAudit->getResult()) : '',
            'commentaire' => $certificationPartnerAudit->getNotes(),
        ];

        $templateCriterias = $templateAudit->getCriterias();
        $criteriasToExport = [];
        foreach ($templateCriterias as $templateCriteria) {
            $criteriasToExport[] = $templateCriteria['code'] . '_' . 'title';
        }
        $evaluatedCriterias = $certificationPartnerAudit->getEvaluatedCriterias();
        foreach ($evaluatedCriterias as $evaluatedCriteria) {
            $prefix = $evaluatedCriteria['code'] . '_' . 'title';
            $evaluatedCriteriaValue = [];
            if (is_array($evaluatedCriteria['value'])) {
                foreach ($evaluatedCriteria['value']['notCompliant'] as $notCompliantValue) {
                    $evaluatedCriteriaValue[] = $notCompliantValue['id'] ?? $notCompliantValue;
                }
            } else {
                $evaluatedCriteriaValue = $evaluatedCriteria['value'];
            }
            if (in_array($prefix, $criteriasToExport)) {
                $row[$prefix . 'compliance'] = CertificationPartnerAuditCompliances::toFrString($evaluatedCriteria['compliance']);
                $row[$prefix . 'value'] = $evaluatedCriteriaValue;
                $row[$prefix . 'severity'] = isset($evaluatedCriteria['severity']) ? CertificationPartnerAuditCriteriaSeverity::toFrString($evaluatedCriteria['severity']) : '';
            }
        }
        return $row;
    }

    /**
     * @param Certification $certification
     * @param int $templateId
     * @param CertificationPartnerAuditTemplateService $certificationPartnerAuditTemplateService
     * @param Organism $organism
     * @return CertificationPartnerAuditTemplate
     * @throws WedofSubscriptionException
     */
    private function checkAccessOnAuditTemplate(Certification $certification, int $templateId, CertificationPartnerAuditTemplateService $certificationPartnerAuditTemplateService, Organism $organism): CertificationPartnerAuditTemplate
    {
        if (!$certification->isAllowAudits()) {
            throw new WedofSubscriptionException("Erreur, votre abonnement ne permet pas la création d'audits.");
        }
        $certificationPartnerAuditTemplate = $certificationPartnerAuditTemplateService->getById($templateId);
        if (!$certificationPartnerAuditTemplate) {
            throw new WedofBadRequestHttpException("Erreur, le modèle d'audit n'a pas été trouvé");
        }
        if ($certificationPartnerAuditTemplate->getCertification() !== $certification) {
            throw new WedofAccessDeniedHttpException("Erreur, le modèle d'audit ne correspond pas à la certification");
        }
        if (!$certification->getCertifiers()->contains($organism)) {
            throw new WedofAccessDeniedHttpException("Erreur, vous n'avez pas accès à cette certification");
        }
        return $certificationPartnerAuditTemplate;
    }


    /**
     * @param Certification $certification
     * @param Organism $organism
     * @param CertificationPartnerService $certificationPartnerService
     * @param array $partnerStates
     * @param array $partnerCompliances
     * @return CertificationPartner[]
     */
    private function getPartnersForAudit(Certification $certification, Organism $organism, CertificationPartnerService $certificationPartnerService, array $partnerStates, array $partnerCompliances): array
    {
        /** @var CertificationPartner[] $partners */
        $partners = $certificationPartnerService->list($certification, [
            "certifier" => $organism,
            "state" => $partnerStates,
            "compliance" => $partnerCompliances
        ])->toArray();
        if (sizeof($partners) == 0) {
            throw new WedofBadRequestHttpException("Erreur, aucun partenaire à auditer n'a été trouvé");
        }
        return $partners;
    }
}
