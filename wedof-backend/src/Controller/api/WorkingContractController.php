<?php
// src/Controller/api/WorkingContractController.php
namespace App\Controller\api;

use App\Entity\User;
use App\Entity\WorkingContract;
use App\Exception\WedofAccessDeniedHttpException;
use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofNotFoundHttpException;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\enums\WorkingContractStates;
use App\Library\utils\enums\WorkingContractTypes;
use App\Security\Voter\CertificationVoter;
use App\Security\Voter\WorkingContractVoter;
use App\Service\CertificationService;
use App\Service\OrganismService;
use App\Service\RegistrationFolderService;
use App\Service\WorkingContractService;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcherInterface;
use Knp\Component\Pager\PaginatorInterface;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints as Assert;

/**
 * Class WorkingContractController
 * @package App\Controller\api
 *
 */
class WorkingContractController extends AbstractFOSRestController
{
    //-------------------
    // METHODES PUBLIQUES
    //-------------------
    /**
     * @Rest\Get("/api/workingContracts/{id}")
     * @IsGranted(WorkingContractVoter::VIEW, subject="workingContract",  message="not allowed")
     * @Rest\View(StatusCode = 200)
     * @param WorkingContract $workingContract
     * @return WorkingContract
     */
    public function show(WorkingContract $workingContract): WorkingContract
    {
        return $workingContract;
    }

    /**
     * @Rest\Get("/api/workingContracts")
     * @IsGranted("ROLE_USER", message="not allowed")
     * @Rest\QueryParam(name="registrationFolderExternalId", requirements=@Assert\Type("string"), nullable=true)
     * @Rest\QueryParam(name="certifInfo", requirements=@Assert\Type("string"), nullable=true, description="Permet de n'obtenir que les dossiers liés à la certification considérée - par défaut tous les dossiers de toutes les certifications sont retournés.")
     * @Rest\QueryParam(name="state", requirements=@Assert\Type("string"), default="all", description="Permet de n'obtenir que les dossiers dans l'état considéré - par défaut tous les états sont retournés. Valeurs possibles : 'all', 'draft', 'sent', 'pendingAcceptation', 'accepted', 'cancelled', 'refused', 'broken', 'completed'. Il est possible de demander plusieurs états en séparant chaque état par une virgule, ex : 'accepted,pendingAcceptation'.")
     * @Rest\QueryParam(name="financer", requirements=@Assert\Type("string"), nullable=true, description="Permet de n'obtenir que les dossiers selon le financeur considéré - par défaut tous les financeurs sont retournés. Valeurs possibles : 'opcoCfaAtlas', 'opcoCfaAfdas', 'opcoCfaEp', 'opcoCfaMobilites', 'opcoCfaAkto', 'opcoCfaOcapiat', 'opcoCfaUniformation', 'opcoCfa2i', 'opcoCfaConstructys', 'opcoCfaSante', 'opcoCfaOpcommerce'. Il est possible de demander plusieurs états en séparant chaque état par une virgule, ex : 'opcoCfaSante,opcoCfaOpcommerce'.")
     * @Rest\QueryParam(name="siret", requirements=@Assert\Type("string"), nullable=true, description="Permet de n'obtenir que les dossiers selon le siret de l'employeur considéré - par défaut tous les employeur sont retournés.")
     * @Rest\QueryParam(name="type", requirements=@Assert\Type("string"), nullable=true, description="Permet de n'obtenir que les dossiers selon le type considéré - par défaut tous les types sont retournés. Valeurs possibles : '11', '21', '22', '23', '31', '32', '33', '34', '35', '36', '37', '38'. Il est possible de demander plusieurs types en séparant chaque type par une virgule, ex : '23,31'.")
     * @Rest\QueryParam(name="limit", requirements="\d+", default="100")
     * @Rest\QueryParam(name="page", requirements="\d+", default="1")
     * @Rest\View(StatusCode = 200)
     * @param WorkingContractService $workingContractService
     * @param ParamFetcherInterface $paramFetcher
     * @param RegistrationFolderService $registrationFolderService
     * @param CertificationService $certificationService
     * @param OrganismService $organismService
     * @param PaginatorInterface $paginator
     * @return Response
     */
    public function list(WorkingContractService $workingContractService, ParamFetcherInterface $paramFetcher, RegistrationFolderService $registrationFolderService, CertificationService $certificationService, OrganismService $organismService, PaginatorInterface $paginator): Response
    {
        $parameters = $paramFetcher->all(true);

        /* @var $user User */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();

        if (!empty($parameters['registrationFolderExternalId'])) {
            $registrationFolder = $registrationFolderService->getByExternalId($parameters['registrationFolderExternalId']);
            if (!$registrationFolder) {
                throw new WedofBadRequestHttpException("Erreur, le dossier de formation n'a pas été trouvé");
            }
            $parameters['registrationFolder'] = $registrationFolder;
        }

        if (isset($parameters['certifInfo'])) {
            $certification = $certificationService->getByCertifInfo($parameters['certifInfo']);
            if (!$certification) {
                throw new WedofNotFoundHttpException("La certification associée au certifInfo " . $parameters['certifInfo'] . " n'a pas été trouvée.");
            } else if (!$this->isGranted(CertificationVoter::VIEW_ADVANCED, $certification)) {
                throw new WedofAccessDeniedHttpException("Erreur, vous n'avez pas les droits pour accéder à la certification associée au certifInfo " . $parameters['certifInfo']);
            }
        }

        if (isset($parameters['state'])) {
            $parameters['state'] = explode(",", $parameters['state']);
            foreach ($parameters['state'] as $state) {
                if (!in_array($state, WorkingContractStates::valuesStates())) {
                    throw new WedofBadRequestHttpException("Erreur sur les valeurs renvoyées 'state', elles doivent être : " . join(",", WorkingContractStates::valuesStates()) . ".");
                }
            }
        }

        if (isset($parameters['financer'])) {
            $parameters['financer'] = explode(",", $parameters['financer']);
            foreach ($parameters['financer'] as $financer) {
                if (!in_array($financer, DataProviders::getOpcoCfaDataProviders())) {
                    throw new WedofBadRequestHttpException("Erreur sur les valeurs renvoyées 'financer', elles doivent être : " . join(",", DataProviders::getOpcoCfaDataProviders()) . ".");
                }
            }
        }

        if (isset($parameters['siret'])) {
            $organism = $organismService->getBySiret($parameters['siret']);
            if (!$organism) {
                throw new WedofBadRequestHttpException("Erreur, l'organisme associé au siret " . $parameters['siret'] . " n'a pas été trouvé");
            } else {
                // todo do we need to check with organism voter ?
                $parameters['employer'] = $organism;
            }
        }

        if (isset($parameters['type'])) {
            $parameters['type'] = explode(",", $parameters['type']);
            foreach ($parameters['type'] as $type) {
                if (!in_array($type, WorkingContractTypes::valuesTypes())) {
                    throw new WedofBadRequestHttpException("Erreur sur les valeurs renvoyées 'type', elles doivent être : " . join(",", WorkingContractTypes::valuesTypes()) . ".");
                }
            }
        }

        $data = $paginator->paginate($workingContractService->listReturnQueryBuilder($organism, $parameters), intval($parameters['page']), intval($parameters['limit']));
        $view = $this->view($data->getItems(), 200);
        $view->setHeader("x-total-count", $data->getTotalItemCount());
        $view->setHeader("x-current-page", $data->getCurrentPageNumber());
        $view->setHeader("x-item-per-page", $data->getItemNumberPerPage());
        return $this->handleView($view);
    }
}
