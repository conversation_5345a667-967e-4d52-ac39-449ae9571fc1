<?php
// src/Controller/api/SessionController.php
namespace App\Controller\api;

use App\Entity\Session;
use App\Entity\User;
use App\Exception\WedofAccessDeniedHttpException;
use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofNotFoundHttpException;
use App\Security\Voter\CertificationVoter;
use App\Security\Voter\OrganismVoter;
use App\Security\Voter\SessionVoter;
use App\Security\Voter\TrainingActionVoter;
use App\Security\Voter\TrainingVoter;
use App\Service\CertificationService;
use App\Service\OrganismService;
use App\Service\SessionService;
use App\Service\TrainingActionService;
use App\Service\TrainingService;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use ErrorException;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcherInterface;
use JMS\Serializer\SerializationContext;
use JMS\Serializer\SerializerInterface;
use Knp\Component\Pager\PaginatorInterface;
use Nelmio\ApiDocBundle\Annotation as ApiDoc;
use OpenApi\Annotations as OA;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints as Assert;
use Throwable;

/**
 * Class SessionController
 * @package App\Controller\api
 *
 * @OA\Tag(name="Session")
 * @ApiDoc\Security(name="accessCode")
 *
 */
class SessionController extends AbstractFOSRestController
{
    //-------------------
    // METHODES PUBLIQUES
    //-------------------
    /**
     * @Rest\Get("/api/sessions/{externalId}", requirements={"externalId"=".+"})
     * @Rest\Get("/app/attendees/sessions/{externalId}", requirements={"externalId"=".+"})
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"sessions", "default"})
     * @OA\Get (
     *     summary="Récupération d'une session.",
     *     description="Récupération d'une session par son ID. Via OAuth2, cet appel nécessite le scope 'session:read'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations de la session recherchée.",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/Session")
     *     )
     * )
     * @OA\Parameter(
     *     name="id",
     *     in="path",
     *     description="ID de la session",
     *     @OA\Schema(type="string")
     * )
     *
     * @param Session $session
     * @param SerializerInterface $serializer
     * @return Response
     */
    public function show(Session $session, SerializerInterface $serializer): Response
    {
        $serializationContext = new SerializationContext();
        $serializationContext->setSerializeNull(true);

        if ($this->isGranted(SessionVoter::VIEW, $session)) {
            $serializationContext->setGroups('Default');
        } else if ($this->isGranted(SessionVoter::CERTIFIER_VIEW, $session)) {
            $serializationContext->setGroups('certifier');
        } else if ($this->isGranted(SessionVoter::ATTENDEE_VIEW, $session)) {
            $serializationContext->setGroups('attendee');
        } else {
            throw new WedofAccessDeniedHttpException("Vous n'êtes pas autorisé à avoir accès au contenu de la session associée à l'externalId " . $session->getExternalId());
        }
        return new Response($serializer->serialize($session, 'json', $serializationContext));
    }

    /**
     * @Rest\Get("/api/sessions")
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_SESSION:READ')", message="not allowed")
     *
     * @Rest\QueryParam(name="siret", requirements="\d{14}", nullable=true)
     * @Rest\QueryParam(name="certifInfo", requirements=@Assert\Type("string"), nullable=true)
     * @Rest\QueryParam(name="trainingId", requirements=".+", nullable=true)
     * @Rest\QueryParam(name="trainingActionId", requirements=".+\/.+", nullable=true)
     *
     * @Rest\QueryParam(name="since", requirements=@Assert\Date, nullable=true, description="Recherche les sessions dont la date de début est supérieure ou égale à la date donnée.")
     * @Rest\QueryParam(name="until", requirements=@Assert\Date, nullable=true, description="Recherche les sessions dont la date de fin est inférieure ou égale à la date donnée.")
     * @Rest\QueryParam(name="query", requirements=@Assert\Type("string"), nullable=true, description="Permet d'effectuer une recherche libre sur les champs 'external id' et 'titre de la formation'.")
     * @Rest\QueryParam(name="sort", requirements=@Assert\Choice({"externalId", "startDate", "endDate"}), default="startDate", description="Tri les résultats sur un critère. Valeurs possibles: 'externalId', 'startDate', 'endDate'.")
     * @Rest\QueryParam(name="limit", requirements="\d+", default="100")
     * @Rest\QueryParam(name="state", requirements=@Assert\Type("string"), nullable=true, description="Permet de n'obtenir que les sessions dans l'état demandé (draft, published, unpublished, archived)")
     * @Rest\QueryParam(name="recruitmentStatus", requirements=@Assert\Type("string"), nullable=true, description="Permet de n'obtenir que les sessions dans le status demandé (opened, closed)")
     * @Rest\QueryParam(name="eligible", requirements=@Assert\Choice({"false", "true"}), nullable=true, description="Permet de n'obtenir que les sessions éligibles (elles sont rendues inéligibles quand la certification expire)")
     *
     * @Rest\QueryParam(name="page", requirements="\d+", default="1")
     * @Rest\QueryParam(name="order", requirements=@Assert\Choice({"asc", "desc"}), default="desc", description="Tri les résultats par ordre ascendant ou descendant - par défaut descendant.")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"sessions", "default"})
     * @OA\Get (
     *     summary="Liste toutes les sessions de l'organisme de l'utilisateur courant. Peut aussi lister les actions de formation de manière filtrée.",
     *     description="Récupère l'ensemble des sessions liées à l'organisme de l'utilisateur connecté. NOTA : les paramètres sont cumulatifs et deux paramètres incompatibles renverront un résultat vide. Via OAuth2, cet appel nécessite le scope 'session:read'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un tableau de sessions au format JSON.",
     *     @OA\JsonContent(
     *          type="array",
     *          @OA\Items(ref="#/components/schemas/Session")
     *     )
     * )
     * @OA\Parameter (name="order", in="query", @OA\Schema (ref="#/components/schemas/Order"))
     *
     * @param ParamFetcherInterface $paramFetcher
     * @param OrganismService $organismService
     * @param CertificationService $certificationService
     * @param TrainingService $trainingService
     * @param TrainingActionService $trainingActionService
     * @param SessionService $sessionService
     * @param PaginatorInterface $paginator
     * @return Response
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function list(ParamFetcherInterface $paramFetcher, OrganismService $organismService, CertificationService $certificationService, TrainingService $trainingService, TrainingActionService $trainingActionService, SessionService $sessionService, PaginatorInterface $paginator): Response
    {
        /* @var $user User */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();
        $parameters = $paramFetcher->all(true);

        if (isset($parameters['siret'])) {
            $organism = $organismService->getBySiret($parameters['siret']);
            if ($organism) {
                if (!$this->isGranted(OrganismVoter::VIEW, $organism)) {
                    throw new WedofAccessDeniedHttpException("Non autorisé");
                }
            } else {
                throw new WedofNotFoundHttpException("L'organisme associé au siret " . $parameters['siret'] . " n'a pas été trouvé.");
            }
        }
        if (isset($parameters['certifInfo'])) {
            $certification = $certificationService->getByCertifInfo($parameters['certifInfo']);
            if (!empty($certification)) {
                if (!$this->isGranted(CertificationVoter::VIEW_ADVANCED, $certification)) {
                    throw new WedofAccessDeniedHttpException("Vous n'êtes pas autorisé à avoir accès au contenu de la certification associé au certifInfo " . $parameters['certifInfo']);
                }
            } else {
                throw new WedofNotFoundHttpException("La certification associée au certifInfo " . $parameters['certifInfo'] . " n'a pas été trouvée.");
            }
        }
        if (isset($parameters['trainingId'])) {
            $training = $trainingService->getByExternalId($parameters['trainingId']);
            if (!empty($training)) {
                if (!$this->isGranted(TrainingVoter::VIEW, $training)) {
                    throw new WedofAccessDeniedHttpException("Vous n'êtes pas autorisé à avoir accès au contenu de la formation associé à l'id " . $parameters['trainingId']);
                }
            } else {
                throw new WedofNotFoundHttpException("La formation associée à l'id " . $parameters['trainingId'] . " n'a pas été trouvée.");
            }
        }
        if (isset($parameters['trainingActionId'])) {
            $trainingAction = $trainingActionService->getByExternalId($parameters['trainingActionId']);
            if ($trainingAction) {
                if (!$this->isGranted(TrainingActionVoter::VIEW, $trainingAction)) {
                    throw new WedofAccessDeniedHttpException("Non autorisé");
                }
            } else {
                throw new WedofNotFoundHttpException("L'action de formation associée à l'id " . $parameters['trainingActionId'] . " n'a pas été trouvée.");
            }
        }

        $data = $paginator->paginate($sessionService->listReturnQueryBuilder($organism, $parameters), intval($parameters['page']), intval($parameters['limit']));
        $view = $this->view($data->getItems(), 200);
        $view->setHeader("x-total-count", $data->getTotalItemCount());
        $view->setHeader("x-current-page", $data->getCurrentPageNumber());
        $view->setHeader("x-item-per-page", $data->getItemNumberPerPage());
        return $this->handleView($view);
    }

    /**
     * @Rest\Post ("/api/sessions")
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_SESSION:WRITE')", message="not allowed")
     * @Rest\View(StatusCode = 501)
     *
     * @return WedofBadRequestHttpException
     */
    public function create(): WedofBadRequestHttpException
    {
        throw new WedofBadRequestHttpException("Méthode non supportée - Une nouvelle session ne peut être créée.");
    }

    /**
     * @Rest\Put("/api/sessions/{externalId}", requirements={"externalId"=".+"})
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_SESSION:WRITE')", message="not allowed")
     * @Rest\View(StatusCode = 501)
     *
     * @param Session $session
     * @return WedofBadRequestHttpException
     */
    public function update(Session $session): WedofBadRequestHttpException
    {
        throw new WedofBadRequestHttpException("Méthode non supportée - La session associée à l'id " . $session->getExternalId() . " ne peut être modifiée.");
    }

    /**
     * @Rest\Delete("/api/sessions/{externalId}", requirements={"externalId"=".+"})
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_SESSION:WRITE')", message="not allowed")
     * @Rest\View(StatusCode = 501)
     *
     * @param Session $session
     * @return WedofBadRequestHttpException
     */
    public function delete(Session $session): WedofBadRequestHttpException
    {
        throw new WedofBadRequestHttpException("Méthode non supportée - La session associée à l'id " . $session->getExternalId() . " ne peut être supprimée.");
    }


    //----------------
    // METHODES PRIVES
    //----------------
}
