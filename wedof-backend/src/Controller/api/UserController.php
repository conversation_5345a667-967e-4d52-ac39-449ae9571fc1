<?php
// src/Controller/api/UserController.php
namespace App\Controller\api;

use App\Entity\CertificationFolder;
use App\Entity\Proposal;
use App\Entity\RegistrationFolder;
use App\Entity\User;
use App\Exception\WedofAccessDeniedHttpException;
use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofNotFoundHttpException;
use App\Security\Voter\OrganismVoter;
use App\Security\Voter\UserVoter;
use App\Service\OrganismService;
use App\Service\UserService;
use App\Service\UserSubscriptionManager;
use BenTools\WebPushBundle\Model\Message\PushMessage;
use BenTools\WebPushBundle\Sender\PushMessageSender;
use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use ErrorException;
use Exception;
use FOS\RestBundle\Context\Context;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcherInterface;
use FOS\RestBundle\View\View;
use Knp\Component\Pager\PaginatorInterface;
use LasseRafn\InitialAvatarGenerator\InitialAvatar;
use Nelmio\ApiDocBundle\Annotation as ApiDoc;
use OpenApi\Annotations as OA;
use Psr\Http\Message\StreamInterface;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Entity;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\ParamConverter;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validation;

/**
 * Class UserController
 * @package App\Controller\api
 *
 * @OA\Tag(name="User")
 * @ApiDoc\Security(name="accessCode")
 *
 */
class UserController extends AbstractFOSRestController
{
    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    //-------------------
    // APP ENDPOINTS
    //-------------------
    /**
     * @Route("/app/public/users/initialAvatar/{name}")
     * @param string $name
     * @return StreamInterface|Response
     */
    public function avatar(string $name)
    {
        $avatar = new InitialAvatar();
        $image = $avatar->name($name)
            ->background('#F1F5F9')
            ->size(200)
            ->color('#000000')
            ->generate();
        return $image->response('png');
    }

    /**
     * @Rest\Get ("/app/public/users/forgotPassword/{email}")
     * @Rest\View(StatusCode = 200)
     *
     * @param string $email On ne convertit pas directement l'email en User pour éviter de déclencher une 404 qui peut donner une indication sur l'existence ou non d'un User
     * @param UserService $userService
     * @return void
     * @throws TransportExceptionInterface
     */
    public function forgotPassword(string $email, UserService $userService): void
    {
        $user = $userService->getByEmail($email);
        if ($user && $user->getMainOrganism() && !$user->getMainOrganism()->getReseller()) {
            $userService->forgotPassword($user);
        }
    }

    /**
     * @Rest\Get ("/app/public/users/sendMagicLink/{email}")
     * @Rest\View(StatusCode = 200)
     *
     * @param string $email On ne convertit pas directement l'email en User pour éviter de déclencher une 404 qui peut donner une indication sur l'existence ou non d'un User
     * @param UserService $userService
     * @return void
     * @throws TransportExceptionInterface
     */
    public function sendMagicLink(string $email, UserService $userService): void
    {
        $user = $userService->getByEmail($email);
        if ($user && $user->getMainOrganism() && !$user->getMainOrganism()->getReseller()) {
            $userService->sendMagicLink($user);
        }
    }

    /**
     * @Rest\Post ("/app/public/users")
     * @Rest\Post ("/app/public/users/add", name="user_add", defaults={"sendAlertOnCreation"=false})
     * @Rest\View (StatusCode = 201)
     * @ParamConverter ("user", converter="fos_rest.request_body", options={"deserializationContext"={"groups"={"create"}}, "validator"={"groups"={"create"}}})
     *
     * @param User $user
     * @param UserService $userService
     * @param ConstraintViolationList $violations
     * @param bool $sendAlertOnCreation
     * @return User|View
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function create(User $user, UserService $userService, ConstraintViolationList $violations, bool $sendAlertOnCreation = true)
    {
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }
        return $userService->create($user, $sendAlertOnCreation);
    }

    //-------------------
    // API ENDPOINTS
    //-------------------
    /**
     * @Rest\Get("/api/users/me")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"users", "default"})
     * @OA\Get (
     *     summary="Profil de l'utilisateur authentifié.",
     *     description="Permet de récupérer le profil de l'utilisateur courant. Via OAuth2, cet appel nécessite le scope 'user:read'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations de l'utilisateur authentifié.",
     *     @ApiDoc\Model(type=User::class))
     *     )
     * )
     *
     * @return UserInterface
     */
    public function me(): UserInterface
    {
        if ($this->isGranted(UserVoter::VIEW, $this->getUser())) {
            return $this->getUser();
        } else {
            throw new WedofAccessDeniedHttpException("Erreur, l'accès à l'utilisateur n'est pas autorisé");
        }
    }

    /**
     * @Rest\Post("/api/users/sub")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"users", "default"})
     * @OA\Post (
     *     summary="Profil de l'utilisateur authentifié.",
     *     description="Permet de récupérer le profil de l'utilisateur courant. Via OAuth2, cet appel nécessite le scope 'user:read'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations de l'utilisateur authentifié.",
     *     @ApiDoc\Model(type=User::class))
     *     )
     * )
     *
     * @param PushMessageSender $sender
     * @param UserSubscriptionManager $manager
     * @param Request $request
     * @return UserInterface
     * @throws ErrorException
     */
    public function sub(PushMessageSender $sender, UserSubscriptionManager $manager, Request $request): UserInterface
    {
        if ($this->isGranted(UserVoter::VIEW, $this->getUser())) {
            $data = json_decode($request->getContent(), true);
            $subscriptions = $manager->findByUser($this->getUser());
            $notification = new PushMessage(json_encode(["notification" => $data]), [
                "TTL" => 2419200, //for Microsoft Windows thing
                "urgency" => "normal"
            ]);
            $responses = $sender->push($notification, $subscriptions);
            foreach ($responses as $response) {
                if ($response->isExpired()) {
                    $manager->delete($response->getSubscription());
                }
            }
            return $this->getUser();
        } else {
            throw new WedofAccessDeniedHttpException("Erreur, l'accès à l'utilisateur n'est pas autorisé");
        }
    }

    /**
     * @Rest\Get("/api/users/{email}")
     * @Rest\View(StatusCode = 200)
     * @IsGranted(UserVoter::VIEW, subject="user",  message="not allowed")
     *
     * @param User $user
     * @return User|object
     */
    public function show(User $user): User
    {
        return $user;
    }

    /**
     * @Rest\Get ("/api/users/siret/{ownedOrganism}")
     * @Entity("user", expr="repository.findOneByOwnedOrganism(ownedOrganism)")
     * @Security("is_granted('ROLE_USER')", message="not allowed")
     * @Rest\View(StatusCode = 200, serializerGroups={"company"} )
     *
     * @param User $user
     * @return User
     */
    public function showByOwnedOrganism(User $user): User
    {
        return $user;
    }

    /**
     * @Rest\Get ("/api/findUser")
     * @Security("is_granted('ROLE_USER')", message="not allowed")
     * @Rest\QueryParam(name="email", requirements=@Assert\Type("string"), nullable=true, description="Permet d'effectuer une recherche libre sur les champs 'email'.")
     * @Rest\View(StatusCode = 200)
     *
     * @param ParamFetcherInterface $paramFetcher
     * @param Request $request
     * @param UserService $userService
     * @return Response
     */
    public function findUser(ParamFetcherInterface $paramFetcher, Request $request, UserService $userService): Response
    {
        $parameters = $paramFetcher->all(true);
        $id = $request->get('id') ?? null;
        if ($id && $this->isGranted("ROLE_ADMIN")) {
            $user = $userService->getById($id);
        } else {
            $user = $userService->getByEmail($parameters['email']);
            if ($user && $user->getMainOrganism()) {
                $user = null;
            }
        }
        $context = new Context();
        $context->addGroup("company");
        $view = $this->view($user === null ? [] : [$user], 200);
        $view->setContext($context);
        return $this->handleView($view);
    }

    /**
     * @Rest\Get("/api/users")
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_USER:READ')", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @Rest\QueryParam(name="siret", requirements=@Assert\Type("string"), nullable=true, description="Siret de l'organisme dont on veut les utilisateurs.")
     * @Rest\QueryParam(name="owner", requirements=@Assert\Choice({"false", "true"}), nullable=true, description="Uniquement l'utilisateur propriétaire de l'organisme")
     * @Rest\QueryParam(name="createdSince", requirements=@Assert\DateTime(), nullable=true, description="Date et heure au format AAAA-MM-JJ hh:mm:ss, 2021-11-25 10:25:57")
     * @Rest\QueryParam(name="createdUntil", requirements=@Assert\DateTime(), nullable=true, description="Date et heure au format AAAA-MM-JJ hh:mm:ss, 2021-11-25 10:25:57")
     *
     * @Rest\QueryParam(name="query", requirements=@Assert\Type("string"), nullable=true, description="Permet d'effectuer une recherche libre sur les champs 'nom', 'prénom', 'email', 'siret', 'nom de l'organisme'.")
     * @Rest\QueryParam(name="limit", requirements="\d+", default="100", description="Nombre d'éléments retourné par requête - par défaut 100.")
     * @Rest\QueryParam(name="page", requirements="\d+", default="1", description="Numéro de page de la requête - par défaut la première.")
     *
     * @param UserService $userService
     * @param OrganismService $organismService
     * @param PaginatorInterface $paginator
     * @param ParamFetcherInterface $paramFetcher
     * @return ArrayCollection|Response
     */
    public function list(UserService $userService, OrganismService $organismService, PaginatorInterface $paginator, ParamFetcherInterface $paramFetcher)
    {
        $parameters = $paramFetcher->all(true);

        if (isset($parameters['siret']) || $this->isGranted("ROLE_ADMIN") || $this->isGranted("ROLE_ALLOWED_TO_SWITCH")) {
            if (isset($parameters['siret'])) {
                $askedOrganism = $organismService->getBySiret($parameters['siret']);
                if (!empty($askedOrganism)) {
                    if ($this->isGranted(OrganismVoter::VIEW, $askedOrganism)) {
                        unset($parameters['siret']);
                        $parameters['organism'] = $askedOrganism;
                    } else {
                        throw new WedofAccessDeniedHttpException("Vous n'êtes pas autorisé à avoir accès au contenu de l'organisme associé au siret " . $parameters['siret']);
                    }
                } else {
                    throw new WedofNotFoundHttpException("L'organisme associé au siret " . $parameters['siret'] . " n'a pas été trouvé.");
                }
            }
            $context = new Context();
            if ($this->isGranted("ROLE_ADMIN") || $this->isGranted("ROLE_ALLOWED_TO_SWITCH")) {
                $context->addGroup("Default");
            } else {
                $context->addGroup("company");
            }
            $data = $paginator->paginate($userService->listReturnQueryBuilder($parameters), intval($parameters['page']), intval($parameters['limit']));
            $view = $this->view($data->getItems(), 200);
            $view->setContext($context);
            $view->setHeader("x-total-count", $data->getTotalItemCount());
            $view->setHeader("x-current-page", $data->getCurrentPageNumber());
            $view->setHeader("x-item-per-page", $data->getItemNumberPerPage());
            return $this->handleView($view);
        } else {
            return new ArrayCollection([$this->getUser()]);
        }
    }

    /**
     * @Rest\Put("/api/users/{email}")
     * @Rest\View(StatusCode = 200)
     * @IsGranted(UserVoter::EDIT, subject="user", message="not allowed")
     *
     * @param User $user
     * @param UserService $userService
     * @param Request $request
     * @return User|View
     * @throws Exception
     */
    public function update(User $user, UserService $userService, Request $request)
    {
        $body = json_decode($request->getContent(), true);
        if (empty($body)) {
            throw new WedofBadRequestHttpException("Erreur, vérifiez le body envoyé.");
        }
        if (isset($body['rgpdMsa'])) {
            if (strtotime($body['rgpdMsa'])) {
                $body['rgpdMsa'] = new DateTime($body['rgpdMsa']);
            }
        }
        $violations = $this->validateUpdateBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }

        return $userService->update($user, $body);
    }

    /**
     * @Rest\Post("/api/users/inviteUser/{email}")
     * @Rest\QueryParam(name="resetPassword", requirements=@Assert\Choice({"true", "false"}), default="false")
     * @Security("is_granted('ROLE_USER')", message="not allowed")
     * @Rest\View(StatusCode = 200)
     * @param UserService $userService
     * @param User $invitedUser
     * @param ParamFetcherInterface $paramFetcher
     * @return User
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws TransportExceptionInterface
     */
    public function inviteUserInSameOrganism(UserService $userService, User $invitedUser, ParamFetcherInterface $paramFetcher): User
    {
        /** @var User $user */
        $user = $this->getUser();
        if (!$user->isOwner()) {
            throw new WedofBadRequestHttpException("Erreur, seul le propriétaire de l'organisme peut inviter d'autres utilisateurs.");
        }
        if ($invitedUser->getMainOrganism()) {
            throw new WedofBadRequestHttpException("Erreur, le compte avec l'email " . $invitedUser->getEmail() . " est déjà associé à un organisme");
        }
        $trainingUsersLimit = $user->getMainOrganism()->getSubscription()->getTrainingUsersLimit();
        $certifierUsersLimit = $user->getMainOrganism()->getSubscription()->getCertifierUsersLimit();
        if ($trainingUsersLimit && $certifierUsersLimit) {
            $usersCount = $userService->countBySiret($user->getMainOrganism()->getSiret());
            if (max([$trainingUsersLimit, $certifierUsersLimit]) <= $usersCount) {
                throw new WedofBadRequestHttpException("Erreur, vous avez atteint la limite du nombre d'utilisateurs sur votre organisme.");
            }
        }
        $parameters = $paramFetcher->all(true);
        $resetPassword = false;
        if (isset($parameters['resetPassword'])) {
            $resetPassword = filter_var($parameters['resetPassword'], FILTER_VALIDATE_BOOLEAN);
        }

        return $userService->inviteUser($user, $invitedUser, $resetPassword);
    }

    /**
     * @Rest\Post("/api/users/revoked/{email}")
     * @Security("is_granted('ROLE_USER')", message="not allowed")
     * @Rest\View(StatusCode = 200, serializerGroups={"company"} )
     * @param UserService $userService
     * @param User $userToDissociate
     * @return User
     */
    public function revokedUserFromOrganism(UserService $userService, User $userToDissociate): User
    {
        /** @var User $user */
        $user = $this->getUser();
        if (!$user->isOwner()) {
            throw new WedofBadRequestHttpException("Erreur, seul le propriétaire de l'organisme peut révoquer un accès.");
        }
        if ($userToDissociate->isOwner()) {
            throw new WedofBadRequestHttpException("Erreur, le propriétaire de l'organisme ne peut être révoqué.");
        }
        if (!$userToDissociate->getMainOrganism() || !in_array($userToDissociate->getMainOrganism(), $user->getOrganisms()->toArray()) || $userToDissociate->getMainOrganism() !== $user->getOwnedOrganism()) {
            throw new WedofBadRequestHttpException("Erreur, le compte avec l'email " . $userToDissociate->getEmail() . " n'est pas associé à votre organisme");
        }

        return $userService->revokedUser($userToDissociate);
    }

    /**
     * @Rest\Delete("/api/users/{email}")
     * @Security("is_granted('ROLE_OAUTH2_USER:WRITE') or is_granted('ROLE_ADMIN')", message="not allowed")
     * @Rest\View(StatusCode = 501)
     *
     * @param User $user
     * @return WedofBadRequestHttpException
     */
    public function delete(User $user): WedofBadRequestHttpException
    {
        throw new WedofBadRequestHttpException("Méthode non supportée - L'utilisateur associé à l'email " . $user->getEmail() . " ne peut être supprimé.");
    }

    //----------------
    // METHODES PRIVES
    //----------------
    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateUpdateBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'email' => new Assert\Optional([new Assert\NotBlank(), new Assert\Email(), new Assert\Length(['max' => 180])]),
            'roles' => new Assert\Optional([
                new Assert\Type('array'),
                new Assert\NotBlank(),
                new Assert\All([
                    new Assert\NotBlank(),
                    new Assert\Regex("/^ROLE_.{1,}/")])
            ]),
            'password' => new Assert\Optional([new Assert\NotBlank(), new Assert\Type('string')]),
            'firstName' => new Assert\Optional([new Assert\NotBlank(), new Assert\Type('string'), new Assert\Length(['max' => 255])]),
            'lastName' => new Assert\Optional([new Assert\NotBlank(), new Assert\Type('string'), new Assert\Length(['max' => 255])]),
            'phoneNumber' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 50])]),
            'address' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 1000])]),
            'mainOrganism' => new Assert\Optional(
                new Assert\Collection([
                    'name' => new Assert\Optional([new Assert\NotBlank(), new Assert\Type('string')]),
                    'siret' => new Assert\Required([new Assert\NotBlank(), new Assert\Type('string'), new Assert\Length(['min' => 14, 'max' => 14])])
                ])
            ),
            'rgpdMsa' => new Assert\Optional([new Assert\NotBlank(), new Assert\Type('datetime')]),
            'filters' => new Assert\Optional([new Assert\Type('array'), new Assert\All([
                new Assert\Collection([
                    'link' => new Assert\Required(new Assert\Type('string')),
                    'name' => new Assert\Required(new Assert\Type('string')),
                    'entityClass' => new Assert\Required(new Assert\Choice([RegistrationFolder::CLASSNAME, CertificationFolder::CLASSNAME, Proposal::CLASSNAME])),
                    'color' => new Assert\Required([new Assert\Type('string'), new Assert\Length(['min' => 7, 'max' => 7])])
                ])
            ])])
        ]);
        return $validator->validate($body, $constraints);
    }
}
