<?php
// src/Controller/api/StatisticController.php
namespace App\Controller\api;

use App\Entity\Certification;
use App\Entity\Training;
use App\Entity\User;
use App\Exception\WedofAccessDeniedHttpException;
use App\Exception\WedofNotFoundHttpException;
use App\Exception\WedofSubscriptionException;
use App\Security\Voter\CertificationVoter;
use App\Security\Voter\OrganismVoter;
use App\Security\Voter\TrainingVoter;
use App\Service\EvaluationService;
use App\Service\OrganismService;
use App\Service\StatisticService;
use BadMethodCallException;
use DateTime;
use Exception;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcherInterface;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints as Assert;

class StatisticController extends AbstractFOSRestController
{
    /**
     * @Rest\Get("/api/statistics/{dataType}", requirements={"dataType"="registrationFolders|evaluations"})
     * @IsGranted("ROLE_USER")
     * @Rest\View(StatusCode = 200)
     * @Rest\QueryParam(name="from", requirements=@Assert\DateTime(), nullable=true)
     * @Rest\QueryParam(name="to", requirements=@Assert\DateTime(), nullable=true)
     * @Rest\QueryParam(name="type", requirements=@Assert\Choice({"data", "count"}), default="data")
     * @Rest\QueryParam(name="unique", requirements=@Assert\Choice({"true", "false"}), default="false")
     * @Rest\QueryParam(name="sort", default=null)
     * @Rest\QueryParam(name="state", default=null)
     * @Rest\QueryParam(name="field", default=null)
     * @Rest\QueryParam(name="limit", default=null)
     * @Rest\QueryParam(name="groupBy", default=null)
     * @Rest\QueryParam(name="function", default=null)
     * @Rest\QueryParam(name="billingState", default=null)
     * @param string $dataType
     * @param StatisticService $statisticService
     * @param ParamFetcherInterface $paramFetcher
     * @return array|int|Response
     * @throws Exception
     */
    public function byDataType(string $dataType, StatisticService $statisticService, ParamFetcherInterface $paramFetcher)
    {
        /** @var User $user */
        $user = $this->getUser();
        if (!$user->getMainOrganism()->getSubscription()->isAllowAnalytics()) {
            throw new WedofSubscriptionException("Erreur, votre abonnement ne permet pas l'accès aux statistiques.");
        }
        return $this->getDataByDataTypeAndObject($dataType, $statisticService, $paramFetcher, $user->getMainOrganism());
    }

    /**
     * @Rest\Get("/api/statistics/training/{externalId}/{dataType}")
     * @IsGranted("ROLE_USER")
     * @IsGranted(TrainingVoter::VIEW, subject="training",  message="not allowed")
     * @Rest\View(StatusCode = 200)
     * @Rest\QueryParam(name="from", requirements=@Assert\DateTime(), nullable=true)
     * @Rest\QueryParam(name="to", requirements=@Assert\DateTime(), nullable=true)
     * @Rest\QueryParam(name="type", requirements=@Assert\Choice({"data", "count"}), default="chartDate")
     * @Rest\QueryParam(name="unique", requirements=@Assert\Choice({"true", "false"}), default="false")
     * @Rest\QueryParam(name="sort", default=null)
     * @Rest\QueryParam(name="state", default=null)
     * @Rest\QueryParam(name="field", default=null)
     * @Rest\QueryParam(name="limit", default=null)
     * @Rest\QueryParam(name="groupBy", default=null)
     * @Rest\QueryParam(name="function", default=null)
     * @Rest\QueryParam(name="billingState", default=null)
     * @param Training $training
     * @param string $dataType
     * @param StatisticService $statisticService
     * @param ParamFetcherInterface $paramFetcher
     * @return array|int|Response
     * @throws Exception
     */
    public function byDataTypePerTraining(Training $training, string $dataType, StatisticService $statisticService, ParamFetcherInterface $paramFetcher)
    {
        /** @var User $user */
        $user = $this->getUser();
        if (!$user->getMainOrganism()->getSubscription()->isAllowAnalytics()) {
            throw new WedofSubscriptionException("Erreur, votre abonnement ne permet pas l'accès aux statistiques.");
        }
        return $this->getDataByDataTypeAndObject($dataType, $statisticService, $paramFetcher, $training);
    }

    /**
     * @Rest\Get("/api/statistics/certification/{certifInfo}/{dataType}")
     * @IsGranted("ROLE_USER")
     * @IsGranted(CertificationVoter::VIEW_ADVANCED, subject="certification",  message="not allowed")
     * @Rest\QueryParam(name="siret", requirements="\d{14}", nullable=true)
     *
     * @Rest\View(StatusCode = 200)
     *
     * @param Certification $certification
     * @param ParamFetcherInterface $paramFetcher
     * @param OrganismService $organismService
     * @param EvaluationService $evaluationService
     * @return array
     * @throws WedofSubscriptionException
     */
    public function byDataTypePerCertification(Certification $certification, ParamFetcherInterface $paramFetcher, OrganismService $organismService, EvaluationService $evaluationService): array
    {
        /* @var $user User */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();
        if (!$organism->getSubscription()->isAllowAnalytics()) {
            throw new WedofSubscriptionException("Erreur, votre abonnement ne permet pas l'accès aux statistiques.");
        }

        $parameters = $paramFetcher->all(true);

        if (isset($parameters['siret'])) {
            $organism = $organismService->getBySiret($parameters['siret']);
            if (!empty($organism)) {
                if (!$this->isGranted(OrganismVoter::VIEW, $organism) && !$this->isGranted(OrganismVoter::CERTIFICATION_VIEW, $organism)) {
                    throw new WedofAccessDeniedHttpException("Vous n'êtes pas autorisé à avoir accès au contenu de l'organisme associé au siret " . $parameters['siret']);
                }
            } else {
                throw new WedofNotFoundHttpException("L'organisme associé au siret " . $parameters['siret'] . " n'a pas été trouvé.");
            }
        }

        return $evaluationService->summarizeForOrganismAndCertification($organism, $certification);
    }

    //-----------------
    // METHODES PRIVEES
    //-----------------

    /**
     * @param string $dataType
     * @param StatisticService $statisticService
     * @param ParamFetcherInterface $paramFetcher
     * @param $object
     * @return array|int|Response
     * @throws Exception
     */
    private function getDataByDataTypeAndObject(string $dataType, StatisticService $statisticService, ParamFetcherInterface $paramFetcher, $object)
    {
        $type = $paramFetcher->get('type');
        $endDate = $paramFetcher->get('to');
        $startDate = $paramFetcher->get('from');
        $options = $paramFetcher->all();

        $options['unique'] = filter_var($options['unique'], FILTER_VALIDATE_BOOLEAN);
        unset($options['type']);
        unset($options['from']);
        unset($options['to']);


        $endDate = $endDate ? new DateTime($endDate) : new DateTime();
        $endDate->setTime(23, 59, 59);
        if ($startDate == null) {
            $startDate = clone $endDate;
            $startDate = $startDate->modify('-30 days');
        } else {
            $startDate = new DateTime($startDate);
        }
        $startDate->setTime(0, 0);

        try {
            switch ($type) {
                case "count":
                    $data = $statisticService->dataBetweenDates($dataType, $object, $startDate, $endDate, $options);
                    break;

                default:
                case "data":
                    $data = $statisticService->dataGroupByDates($dataType, $object, $startDate, $endDate, $options);
                    //variations
                    $count = $statisticService->dataBetweenDates($dataType, $object, $startDate, $endDate, $options);
                    $startDatePreviousPeriod = clone $startDate;
                    $endDatePreviousPeriod = clone $startDate;
                    $startDatePreviousPeriod->modify("- " . $startDate->diff($endDate)->days . " days");
                    $countPreviousPeriod = $statisticService->dataBetweenDates($dataType, $object, $startDatePreviousPeriod, $endDatePreviousPeriod, $options);
                    $variation = $countPreviousPeriod > 0 ? ((100 * ($count - $countPreviousPeriod)) / $countPreviousPeriod) : 0;
                    $data = ["variation" => round($variation, 2), "data" => $data];
                    break;
            }
        } catch (BadMethodCallException $e) {
            echo $e->getMessage();
            return new Response("", 400);
        }

        return $data;
    }
}
