<?php
// src/Controller/api/FileTypeController.php

namespace App\Controller\api;

use App\Application\Document\DocumentWedofApplication;
use App\Entity\Attendee;
use App\Entity\Certification;
use App\Entity\CertificationFolder;
use App\Entity\CertificationFolderFile;
use App\Entity\CertificationPartner;
use App\Entity\Organism;
use App\Entity\RegistrationFolder;
use App\Entity\RegistrationFolderFile;
use App\Entity\User;
use App\Exception\WedofAccessDeniedHttpException;
use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofConnectionException;
use App\Library\utils\enums\CertificationFolderStates;
use App\Library\utils\enums\CertificationPartnerStates;
use App\Library\utils\enums\RegistrationFolderStates;
use App\Library\utils\enums\SubscriptionCertifierTypes;
use App\Library\utils\enums\SubscriptionTrainingTypes;
use App\Repository\CertificationFolderFileRepository;
use App\Repository\CertificationPartnerFileRepository;
use App\Repository\RegistrationFolderFileRepository;
use App\Security\Voter\CertificationVoter;
use App\Security\Voter\OrganismVoter;
use App\Service\AccessService;
use App\Service\CertificationFolderService;
use App\Service\CertificationPartnerService;
use App\Service\CertificationService;
use App\Service\FileTypeService;
use App\Service\OrganismService;
use App\Service\RegistrationFolderService;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\NotSupported;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use ErrorException;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\View\View;
use LogicException;
use Nelmio\ApiDocBundle\Annotation as ApiDoc;
use OpenApi\Annotations as OA;
use Psr\Cache\InvalidArgumentException;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use ReflectionException;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\File\File;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validation;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Throwable;


/**
 * Class FileTypeController
 * @package App\Controller\api
 *
 * @OA\Tag(name="File")
 * @ApiDoc\Security(name="accessCode")
 */
class FileTypeController extends AbstractWedofController
{
    //-------------------
    // METHODES PUBLIQUES
    //-------------------
    /**
     * @Rest\Post("/api/fileTypes/{entityClass}/{entityId}/{field}", requirements={"entityClass"="Certification|Organism", "field"="certificationFolderFileTypes|certificationPartnerFileTypes|registrationFolderFileTypes"})
     * @Security("is_granted('ROLE_USER')", message="not allowed")
     * @Rest\View(StatusCode = 201)
     * @param string $entityClass
     * @param CertificationService $certificationService
     * @param OrganismService $organismService
     * @param FileTypeService $fileTypeService
     * @param $entityId
     * @param string $field
     * @param AccessService $accessService
     * @return array|View
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ReflectionException
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     * @throws \Doctrine\ORM\ORMException
     */
    public function create(string $entityClass, CertificationService $certificationService, OrganismService $organismService, FileTypeService $fileTypeService, $entityId, string $field, AccessService $accessService)
    {
        /** @var User $user */
        $user = $this->getUser();
        $body = $this->getData();
        if (empty($body)) {
            throw new WedofBadRequestHttpException("Veuillez renseigner un body.");
        }
        $generated = !empty($body['generated']);
        if ($entityClass === Certification::CLASSNAME) {
            /** @var Certification $entity */
            $entity = $certificationService->getById($entityId);
            $this->checkAccessOnCertification($user, $entityId, $field, $entity);

            switch ($field) {
                case FileTypeService::CERTIFICATION_FOLDER:
                    if (isset($body['toState'])) {
                        $this->checkStateOnFileType($field, $body['toState'], $generated);
                    }
                    $body = $this->checkGeneratedParams($body, $generated, $accessService, $user);
                    $body = $this->checkAllowAttendeeParams($body, $generated);
                    $body = $this->checkAllowPartnerParams($body, $generated);
                    break;
                case FileTypeService::CERTIFICATION_PARTNER:
                    if (isset($body['toState'])) {
                        $this->checkStateOnFileType($field, $body['toState'], $generated);
                    }
                    $body = $this->removeUnusedProperties($body, ['allowVisibilityAttendee', 'allowUploadAttendee', 'allowSignAttendee']);
                    $body = $this->checkGeneratedParams($body, $generated, $accessService, $user);
                    $body = $this->checkAllowPartnerParams($body, $generated);
                    break;
                case FileTypeService::REGISTRATION_FOLDER:
                    throw new LogicException("La valeur de field '" . $field . "' pour l'entityClass '" . $entityClass . "' n'est pas autorisé.");
                default:
                    throw new LogicException("La valeur de field '" . $field . "' est inconnue.");
            }
        } else if ($entityClass === Organism::CLASSNAME) {
            /** @var Organism $entity */
            $entity = $organismService->getBySiret($entityId);
            $this->checkAccessOnOrganism($user, $entityId, $field, $entity);

            switch ($field) {
                case FileTypeService::REGISTRATION_FOLDER:
                    if (isset($body['toState'])) {
                        $this->checkStateOnFileType($field, $body['toState'], $generated);
                    }
                    $body = $this->removeUnusedProperties($body, ['allowVisibilityPartner', 'allowUploadPartner', 'allowSignPartner']);
                    $body = $this->checkGeneratedParams($body, $generated, $accessService, $user);
                    $body = $this->checkAllowAttendeeParams($body, $generated);
                    break;
                case FileTypeService::CERTIFICATION_FOLDER:
                case FileTypeService::CERTIFICATION_PARTNER:
                    throw new LogicException("La valeur de field '" . $field . "' pour l'entityClass '" . $entityClass . "' n'est pas autorisé.");
                default:
                    throw new LogicException("La valeur de field '" . $field . "' est inconnue.");
            }
        } else {
            throw new LogicException("Entity class ne peut être que Organism ou Certification.");
        }

        $violations = $this->validateCreateBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }
        return $fileTypeService->create($entity, $body, $field, $user->getMainOrganism());
    }

    /**
     * @Rest\Put("/api/fileTypes/{entityClass}/{entityId}/{field}/{idFileType}", requirements={"entityClass"="Certification|Organism", "field"="certificationFolderFileTypes|certificationPartnerFileTypes|registrationFolderFileTypes"})
     * @Rest\View(StatusCode = 200)
     * @Security("is_granted('ROLE_USER')", message="not allowed")
     * @param string $entityClass
     * @param OrganismService $organismService
     * @param CertificationService $certificationService
     * @param FileTypeService $fileTypeService
     * @param $entityId
     * @param int $idFileType
     * @param string $field
     * @param AccessService $accessService
     * @return array|View
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ReflectionException
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     * @throws \Doctrine\ORM\ORMException
     */
    public function update(string $entityClass, OrganismService $organismService, CertificationService $certificationService, FileTypeService $fileTypeService, $entityId, int $idFileType, string $field, AccessService $accessService)
    {
        /** @var User $user */
        $user = $this->getUser();
        $body = $this->getData();
        if (empty($body)) {
            throw new WedofBadRequestHttpException("Veuillez renseigner un body.");
        }
        if ($entityClass === Certification::CLASSNAME) {
            /** @var Certification $entity */
            $entity = $certificationService->getById($entityId);
            $this->checkAccessOnCertification($user, $entityId, $field, $entity);
        } else if ($entityClass === Organism::CLASSNAME) {
            /** @var Organism $entity */
            $entity = $organismService->getBySiret($entityId);
            $this->checkAccessOnOrganism($user, $entityId, $field, $entity);
        } else {
            throw new LogicException("Entity class ne peut être que Organism ou Certification.");
        }
        $files = $entity->{"get" . ucwords($field)}();
        $fileTypeIndex = array_search($idFileType, array_column($files, 'id'));
        if (!isset($fileTypeIndex)) {
            throw new WedofBadRequestHttpException("Erreur, le type de document d'id " . $idFileType . " n'existe pas.");
        }
        $fileType = $files[$fileTypeIndex];

        $generated = !empty($fileType['generated']);

        if (isset($body['toState'])) {
            $this->checkStateOnFileType($field, $body['toState'], $generated);
        }

        $FREE_FILE_TYPE_NAME = "Document libre";
        if ($fileType['name'] === $FREE_FILE_TYPE_NAME) {
            throw new WedofBadRequestHttpException("Erreur, le type de fichier $FREE_FILE_TYPE_NAME ne peut être modifié");
        }

        $isCertificateFileType = $fileType['id'] === Certification::CERTIFICATE_FILE_TYPE_ID && $fileType['name'] === Certification::CERTIFICATE_FILE_TYPE_NAME;

        if ($isCertificateFileType) {
            $violations = $this->validateUpdateBodyWedofFile($body);
        } else {
            switch ($field) {
                case FileTypeService::CERTIFICATION_FOLDER:
                    $body = $this->checkAllowPartnerParams($body, $generated);
                    $body = $this->checkAllowAttendeeParams($body, $generated);
                    break;
                case FileTypeService::CERTIFICATION_PARTNER:
                    $body = $this->removeUnusedProperties($body, ['allowVisibilityAttendee', 'allowUploadAttendee', 'allowSignAttendee']);
                    $body = $this->checkAllowPartnerParams($body, $generated);
                    break;
                case FileTypeService::REGISTRATION_FOLDER:
                    $body = $this->removeUnusedProperties($body, ['allowVisibilityPartner', 'allowUploadPartner', 'allowSignPartner']);
                    $body = $this->checkAllowAttendeeParams($body, $generated);
                    break;
                default:
                    throw new LogicException("On ne devrait jamais arriver ici lors d'un appel update.");
            }

            if ($generated) {
                $applicationAllowedAndEnabled = $accessService->isApplicationAllowedAndEnabled(DocumentWedofApplication::getAppId(), $user->getMainOrganism());
                if (!$applicationAllowedAndEnabled) {
                    throw new WedofBadRequestHttpException("Erreur, votre abonnement ne permet pas l'accès à la génération automatique de documents ou l'application n'est pas activée");
                }
                $currentTemplateFile = $fileType['templateFile'];
                if (isset($body['templateFile']) && ($currentTemplateFile === $body['templateFile'])) {
                    unset($body['templateFile']);
                }
                if (isset($body['templateFile']) && !($body['templateFile'] instanceof File)) {
                    throw new WedofBadRequestHttpException("Erreur, le champ 'templateFile' n'est pas un fichier.");
                }
            }
            $violations = $this->validateUpdateBody($body);
        }

        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }
        return $fileTypeService->update($entity, $body, $field, $fileTypeIndex, $fileType, $user->getMainOrganism());
    }

    /**
     * @Rest\Delete("/api/fileTypes/{entityClass}/{entityId}/{field}/{idFileType}", requirements={"entityClass"="Certification|Organism", "field"="certificationFolderFileTypes|certificationPartnerFileTypes|registrationFolderFileTypes"})
     * @Rest\View(StatusCode = 204)
     * @Security("is_granted('ROLE_USER')", message="not allowed")
     * @param string $entityClass
     * @param OrganismService $organismService
     * @param CertificationService $certificationService
     * @param FileTypeService $fileTypeService
     * @param CertificationFolderFileRepository $certificationFolderFileRepository
     * @param CertificationPartnerFileRepository $certificationPartnerFileRepository
     * @param RegistrationFolderFileRepository $registrationFolderFileRepository
     * @param $entityId
     * @param int $idFileType
     * @param string $field
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function delete(string $entityClass, OrganismService $organismService, CertificationService $certificationService, FileTypeService $fileTypeService, CertificationFolderFileRepository $certificationFolderFileRepository, CertificationPartnerFileRepository $certificationPartnerFileRepository, RegistrationFolderFileRepository $registrationFolderFileRepository, $entityId, int $idFileType, string $field)
    {
        /** @var User $user */
        $user = $this->getUser();
        if ($entityClass === Certification::CLASSNAME) {
            /** @var Certification $entity */
            $entity = $certificationService->getById($entityId);
            $this->checkAccessOnCertification($user, $entityId, $field, $entity);
            if ($field === FileTypeService::CERTIFICATION_FOLDER) {
                $hasFiles = $certificationFolderFileRepository->countFilesWithTypeId($entity, $idFileType) > 0;
            } else if ($field === FileTypeService::CERTIFICATION_PARTNER) {
                $hasFiles = $certificationPartnerFileRepository->countFilesWithTypeId($entity, $idFileType) > 0;
            } else {
                throw new LogicException("Pour l'entité Certification, le field ne peut être que certificationFolderFileTypes ou certificationPartnerFileTypes.");
            }
        } else if ($entityClass === Organism::CLASSNAME) {
            /** @var Organism $entity */
            $entity = $organismService->getBySiret($entityId);
            $this->checkAccessOnOrganism($user, $entityId, $field, $entity);
            $hasFiles = $registrationFolderFileRepository->countFilesWithTypeId($entity, $idFileType) > 0;
        } else {
            throw new LogicException("Entity class ne peut être que Organism ou Certification.");
        }
        if ($hasFiles) {
            throw new WedofBadRequestHttpException("Erreur, il n'est pas possible de supprimer ce type de document car il y a des documents associés.");
        }
        $fileTypes = $entity->{"get" . ucwords($field)}();
        if (!in_array($idFileType, array_column($fileTypes, 'id'))) {
            throw new WedofBadRequestHttpException("Erreur, le type document " . $idFileType . " n'existe pas.");
        } else if ($idFileType === Certification::CERTIFICATE_FILE_TYPE_ID) {
            throw new WedofBadRequestHttpException("Erreur, le type de document 'Parchemin' ne peut pas être supprimé.");
        }
        $fileTypeService->delete($entity, $idFileType, $field);
    }

    /**
     * @Rest\Get("/app/fileTypes/{entityClass}/{entityId}/{field}/{idFileType}", requirements={"entityClass"="Certification|Organism", "field"="certificationFolderFileTypes|registrationFolderFileTypes|certificationPartnerFileTypes"})
     * @Security("is_granted('ROLE_USER')", message="not allowed")
     * @Rest\View(StatusCode = 200)
     * @param FileTypeService $fileTypeService
     * @param string $entityClass
     * @param $entityId
     * @param string $field
     * @param int $idFileType
     * @param OrganismService $organismService
     * @param CertificationService $certificationService
     * @return mixed
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function getTemplateFileExample(FileTypeService $fileTypeService, string $entityClass, $entityId, string $field, int $idFileType, OrganismService $organismService, CertificationService $certificationService): Response
    {
        /** @var User $user */
        $user = $this->getUser();
        if ($entityClass === Certification::CLASSNAME) {
            /** @var Certification $entity */
            $entity = $certificationService->getById($entityId);
            $this->checkAccessOnCertification($user, $entityId, $field, $entity);
        } else if ($entityClass === Organism::CLASSNAME) {
            /** @var Organism $entity */
            $entity = $organismService->getBySiret($entityId);
            $this->checkAccessOnOrganism($user, $entityId, $field, $entity);
        } else {
            throw new LogicException("Entity class ne peut être que Organism ou Certification.");
        }
        $files = $entity->{"get" . ucwords($field)}();
        $fileTypeIndex = array_search($idFileType, array_column($files, 'id'));
        if (!isset($fileTypeIndex)) {
            throw new WedofBadRequestHttpException("Erreur, le type de document d'id " . $idFileType . " n'existe pas.");
        }
        $fileType = $files[$fileTypeIndex];
        if (!isset($fileType['templateFile'])) {
            throw new WedofBadRequestHttpException("Erreur, aucun modèle de document est associé au type de fichier " . $idFileType . ".");
        }
        $response = $fileTypeService->generateExampleDocumentFromTemplate($fileType);
        if (empty($response['statusCode']) || $response['statusCode'] != 200) {
            throw new WedofBadRequestHttpException(json_encode($response['content'], true));
        } else {
            $fileContent = $response['content'];
            $response = new Response($fileContent);
            $response->headers->set('Content-Type', 'application/pdf');
            return $response;
        }
    }

    /**
     * @Rest\Post("/app/fileTypes/{entityClass}/{entityId}/{field}/{idFileType}/{targetEntityClass}/{targetEntityId}/{action}", requirements={"entityClass"="Certification|Organism", "field"="certificationFolderFileTypes|registrationFolderFileTypes|certificationPartnerFileTypes", "targetEntityClass"="RegistrationFolder|CertificationFolder|CertificationPartner", "action"="generate|regenerate"})
     * @Rest\View(StatusCode = 200)
     * @param FileTypeService $fileTypeService
     * @param int $idFileType
     * @param string $targetEntityClass
     * @param string $targetEntityId
     * @param string $action
     * @param AccessService $accessService
     * @param RegistrationFolderService $registrationFolderService
     * @param CertificationFolderService $certificationFolderService
     * @param CertificationPartnerService $certificationPartnerService
     * @return CertificationFolderFile|RegistrationFolderFile
     * @throws ClientExceptionInterface
     * @throws ContainerExceptionInterface
     * @throws ErrorException
     * @throws Exception
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws NotFoundExceptionInterface
     * @throws NotSupported
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ReflectionException
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws \Doctrine\ORM\ORMException
     */
    public function manualGenerateDocument(FileTypeService $fileTypeService, int $idFileType, string $targetEntityClass, string $targetEntityId, string $action, AccessService $accessService, RegistrationFolderService $registrationFolderService, CertificationFolderService $certificationFolderService, CertificationPartnerService $certificationPartnerService)
    {
        // WARNING : {entityClass}/{entityId}/{field} are ignored to be closer to a "download" method in a target controller
        /** @var Attendee|User $user */
        $user = $this->getUser();
        $isAttendee = $user instanceof Attendee;
        // Retrieve target entity
        if ($targetEntityClass === 'RegistrationFolder') {
            /** @var RegistrationFolder $targetEntity */
            $targetEntity = $registrationFolderService->getByEntityId($targetEntityId);
        } else if ($targetEntityClass === 'CertificationFolder') {
            /** @var CertificationFolder $targetEntity */
            $targetEntity = $certificationFolderService->getByEntityId($targetEntityId);
        } else {
            /** @var CertificationPartner $targetEntity */
            $targetEntity = $certificationPartnerService->getByEntityId($targetEntityId);
        }
        if (!$targetEntity) {
            throw new WedofBadRequestHttpException('Error on targetEntity ');
        }
        // Check access on target entity
        $checkAllowVisibilityPartner = false;
        $checkAllowVisibilityAttendee = false;
        if ($targetEntity instanceof CertificationFolder) {
            $certificationFolder = $targetEntity;
            $ownerOrganism = $certificationFolder->getCertifier();
            $fileTypes = $certificationFolder->getCertification()->getCertificationFolderFileTypes();
            if ($isAttendee) {
                $hasAccess = $accessService->hasCertificationFolderAttendeeView($user, $certificationFolder);
                $checkAllowVisibilityAttendee = true;
            } else {
                $hasAccess = $accessService->hasCertificationFolderView($user, $certificationFolder);
                $checkAllowVisibilityPartner = $ownerOrganism !== $user->getMainOrganism() && $certificationFolder->getPartner() === $user->getMainOrganism(); // second condition is not enough, can be both OF and OC
            }
        } else if ($targetEntity instanceof RegistrationFolder) {
            $registrationFolder = $targetEntity;
            $ownerOrganism = $registrationFolder->getOrganism();
            $fileTypes = $registrationFolder->getOrganism()->getRegistrationFolderFileTypes();
            if ($isAttendee) {
                $hasAccess = $accessService->hasRegistrationFolderAttendeeView($user, $registrationFolder);
                $checkAllowVisibilityAttendee = true;
            } else {
                $hasAccess = $accessService->hasRegistrationFolderView($user, $registrationFolder);
            }
        } else if ($targetEntity instanceof CertificationPartner) {
            $certificationPartner = $targetEntity;
            $ownerOrganism = $certificationPartner->getCertifier();
            $fileTypes = $certificationPartner->getCertification()->getCertificationPartnerFileTypes();
            $hasAccess = $accessService->hasCertificationPartnerView($user, $certificationPartner, false);
            $checkAllowVisibilityPartner = $ownerOrganism !== $user->getMainOrganism() && $certificationPartner->getPartner() === $user->getMainOrganism();
        } else {
            throw new WedofBadRequestHttpException('Error entity not recognized');
        }
        if (!$hasAccess && !$this->isGranted('ROLE_ADMIN')) {
            throw new WedofAccessDeniedHttpException("Erreur, vous n'avez pas les droits sur l'entité sur laquelle vous souhaitez générer le document");
        }
        // Retrieve fileType
        $fileTypeIndex = array_search($idFileType, array_column($fileTypes, 'id'));
        if (!isset($fileTypeIndex)) {
            throw new WedofBadRequestHttpException("Erreur, le type de document d'id " . $idFileType . " n'existe pas.");
        }
        $fileType = $fileTypes[$fileTypeIndex];
        // Check subscription options
        $applicationAllowedAndEnabled = $accessService->isApplicationAllowedAndEnabled(DocumentWedofApplication::getAppId(), $ownerOrganism);
        if (!$applicationAllowedAndEnabled) {
            throw new WedofBadRequestHttpException("Erreur, votre abonnement ne permet pas l'accès à la génération automatique de documents ou l'application n'est pas activée");
        }
        if ($fileType['id'] === Certification::CERTIFICATE_FILE_TYPE_ID && (!$targetEntity->getCertification()->isAllowGenerateCertificate() || !$ownerOrganism->getSubscription()->isAllowCertifierPlus())) {
            throw new WedofBadRequestHttpException("Erreur, votre abonnement ne permet pas la génération automatique du parchemin");
        }
        // Check file type
        if (!isset($fileType['templateFile']) && $fileType['id'] !== Certification::CERTIFICATE_FILE_TYPE_ID) { // temporary because there is no template in fileType for certificate so far
            throw new WedofBadRequestHttpException("Erreur, aucun modèle de document est associé au type de fichier.");
        }
        if (!$fileType['generated']) {
            throw new WedofBadRequestHttpException("Erreur, le type de fichier ne permet pas de générer un document.");
        }
        if (!$fileType['enabled']) {
            throw new WedofBadRequestHttpException("Erreur, la génération automatique n'est pas activée sur ce document.");
        }
        $isAllowRegenerate = !isset($fileType['allowRegenerate']) ? true : $fileType['allowRegenerate'];
        if ($action === 'regenerate' && !$isAllowRegenerate) {
            throw new WedofBadRequestHttpException("Erreur, la regénération automatique n'est pas activée.");
        }
        if ($checkAllowVisibilityPartner && empty($fileType['allowVisibilityPartner'])) {
            throw new WedofAccessDeniedHttpException("Erreur, vous n'avez pas accès à ce type de document");
        }
        if ($checkAllowVisibilityAttendee && empty($fileType['allowVisibilityAttendee'])) {
            throw new WedofAccessDeniedHttpException("Erreur, vous n'avez pas accès à ce type de document");
        }
        // GENERATE
        return $fileTypeService->manualGenerateDocumentFromTemplateAndEntity($targetEntity, $fileType, $action, $this->isGranted('ROLE_ADMIN') || $isAttendee ? null : $user, $targetEntityClass);
    }

    /**
     * @Rest\Post("/app/fileTypes/createTemplate/{entityClass}/{entityId}/{field}", requirements={"entityClass"="Certification|Organism", "field"="certificationFolderFileTypes|registrationFolderFileTypes|certificationPartnerFileTypes|certificationPartnerAuditFileTypes"})
     * @Security("is_granted('ROLE_USER')", message="not allowed")
     * @Rest\View(StatusCode = 200)
     * @param string $entityClass
     * @param $entityId
     * @param string $field
     * @param AccessService $accessService
     * @param FileTypeService $fileTypeService
     * @param OrganismService $organismService
     * @param CertificationService $certificationService
     * @return array
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function createTemplate(string $entityClass, $entityId, string $field, AccessService $accessService, FileTypeService $fileTypeService, OrganismService $organismService, CertificationService $certificationService): array
    {
        /** @var User $user */
        $user = $this->getUser();
        if ($entityClass === Certification::CLASSNAME) {
            /** @var Certification $entity */
            $entity = $certificationService->getById($entityId);
            $this->checkAccessOnCertification($user, $entityId, $field, $entity);
        } else if ($entityClass === Organism::CLASSNAME) {
            /** @var Organism $entity */
            $entity = $organismService->getBySiret($entityId);
            $this->checkAccessOnOrganism($user, $entityId, $field, $entity);
        } else {
            throw new LogicException("Entity class ne peut être que Organism ou Certification.");
        }

        $applicationAllowedAndEnabled = $accessService->isApplicationAllowedAndEnabled(DocumentWedofApplication::getAppId(), $user->getMainOrganism());
        if (!$applicationAllowedAndEnabled) {
            throw new WedofBadRequestHttpException("Erreur, votre abonnement ne permet pas la création d'un modèle de document dans Wedof ou l'application n'est pas activée");
        }

        $body = $this->getData();
        if (!isset($body['accept'])) {
            throw new WedofBadRequestHttpException("Erreur, le champ 'accept' est obligatoire");
        }
        if ($body['accept'] === '.doc,.docx') {
            $mimeType = "application/vnd.google-apps.document";
        } else if ($body['accept'] === '.pptx') {
            $mimeType = "application/vnd.google-apps.presentation";
        } else {
            throw new WedofBadRequestHttpException("Erreur, le champ 'accept' ne peut être que '.doc,.docx' ou '.pptx'.");
        }
        $fileName = $this->getFileNameGenerated($user->getMainOrganism()->getSiret(), $entityClass, $field);

        if (isset($body['templateName'])) {
            $result = $fileTypeService->createFromWedofTemplate($body['templateName'], $field, $fileName);
        } else {
            $result = $fileTypeService->createOrUpdateTemplate($mimeType, null, $fileName);
        }

        $createFileType = isset($body['createFileType']) ? $body['createFileType'] : false;
        if ($createFileType) {
            $fileTypeToCreate = [
                'name' => isset($body['templateName']) ? $body['templateName'] : "Nouveau document",
                'googleId' => $result['id'],
                'generated' => true,
                'accept' => ".docx,.doc,.pdf"
            ];
            if ($field === FileTypeService::CERTIFICATION_FOLDER || $field === FileTypeService::REGISTRATION_FOLDER) {
                $fileTypeToCreate['allowVisibilityAttendee'] = false;
                $fileTypeToCreate['allowUploadAttendee'] = false;
                $fileTypeToCreate['allowSignAttendee'] = false;
            }
            if ($field === FileTypeService::CERTIFICATION_PARTNER || $field === FileTypeService::CERTIFICATION_FOLDER) {
                $fileTypeToCreate['allowVisibilityPartner'] = false;
                $fileTypeToCreate['allowUploadPartner'] = false;
                $fileTypeToCreate['allowSignPartner'] = false;
            }
            return $fileTypeService->create($entity, $fileTypeToCreate, $field, $user->getMainOrganism());
        } else {
            return $result;
        }
    }

    /**
     * @Rest\Delete("/app/fileTypes/deleteTemplate/{entityClass}/{field}", requirements={"entityClass"="Certification|Organism", "field"="certificationFolderFileTypes|registrationFolderFileTypes|certificationPartnerFileTypes|certificationPartnerAuditFileTypes"})
     * @Security("is_granted('ROLE_USER')", message="not allowed")
     * @Rest\View(StatusCode = 204)
     *
     * @param string $entityClass
     * @param string $field
     * @param AccessService $accessService
     * @param FileTypeService $fileTypeService
     * @return void
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ReflectionException
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     * @throws \Doctrine\ORM\ORMException
     */
    public function deleteTemplate(string $entityClass, string $field, AccessService $accessService, FileTypeService $fileTypeService): void
    {
        /** @var User $user */
        $user = $this->getUser();
        $applicationAllowedAndEnabled = $accessService->isApplicationAllowedAndEnabled(DocumentWedofApplication::getAppId(), $user->getMainOrganism());
        if (!$applicationAllowedAndEnabled) {
            throw new WedofBadRequestHttpException("Erreur, votre abonnement ne permet pas la suppression d'un modèle de document dans Wedof ou l'application n'est pas activée");
        }
        $fileName = $this->getFileNameGenerated($user->getMainOrganism()->getSiret(), $entityClass, $field);
        $fileTypeService->deleteTemplate($fileName);
    }

    /**
     * @Rest\Get("/app/fileTypes/templates/{field}", requirements={"field"="certificationFolderFileTypes|registrationFolderFileTypes|certificationPartnerFileTypes|certificationPartnerAuditFileTypes|certificationFileTypes"})
     * @Security("is_granted('ROLE_USER')", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @param string $field
     * @param AccessService $accessService
     * @param FileTypeService $fileTypeService
     * @return array
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws ReflectionException
     * @throws \Doctrine\ORM\ORMException
     * @throws InvalidArgumentException
     */
    public function listFileTypesWedof(string $field, AccessService $accessService, FileTypeService $fileTypeService): array
    {
        /** @var User $user */
        $user = $this->getUser();
        $subscription = $user->getMainOrganism()->getSubscription();
        $isPaidTrainingCustomer = in_array(SubscriptionTrainingTypes::from($subscription->getTrainingType()), SubscriptionTrainingTypes::getPaidTrainingTypesForApps());
        $certifierTypes = [SubscriptionCertifierTypes::UNLIMITED()->getValue()];
        if ($subscription->isAllowCertifierPlus()) {
            $certifierTypes[] = SubscriptionCertifierTypes::from($subscription->getCertifierType())->getValue();
        }
        $isPaidCertifierCustomer = in_array($subscription->getCertifierType(), $certifierTypes);
        $applicationAllowedAndEnabled = $accessService->isApplicationAllowedAndEnabled(DocumentWedofApplication::getAppId(), $user->getMainOrganism());
        if (!$applicationAllowedAndEnabled) {
            throw new WedofBadRequestHttpException("Erreur, votre abonnement ne permet pas l'accès aux modèles Wedof");
        }
        $fileTypesWedof = $fileTypeService->listFileTypesWedof($field);
        $getFiles = $this->getFiles();
        foreach ($fileTypesWedof as $fileTypeWedof) {
            $hasAccess = $fileTypeWedof['field'] === FileTypeService::REGISTRATION_FOLDER ? $isPaidTrainingCustomer : $isPaidCertifierCustomer;
            $getFiles[] = [
                "title" => $fileTypeWedof['name'],
                "description" => isset($fileTypeWedof['description']) ? $fileTypeWedof['description'] : null,
                "tags" => ['Modèle prédéfini'],
                "templateName" => 'WedofTemplate - ' . $fileTypeWedof['name'],
                "hasAccess" => $hasAccess,
                "additionalInformations" => ["format" => '.doc,.docx'],
                "display" => true,
            ];
        }
        return $getFiles;
    }

    //-------------------
    // METHODES PRIVEES
    //-------------------

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateCreateBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'fields' => [
                'name' => new Assert\Required([new Assert\NotBlank(), new Assert\Type('string'), new Assert\NotNull(), new Assert\Length(['max' => 255])]),
                'accept' => new Assert\Required([new Assert\NotBlank(), new Assert\Type('string'), new Assert\NotNull(), new Assert\Length(['max' => 255])]),
                'toState' => new Assert\Optional([new Assert\Type('string')]),
                'description' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 1000])]),
                'qualiopiIndicators' => new Assert\Optional([
                    new Assert\Type('array'),
                    new Assert\All([
                        new Assert\Type('integer'),
                        new Assert\Range(['min' => 1, 'max' => 32])
                    ])
                ]),
                'allowVisibilityAttendee' => new Assert\Optional([new Assert\Type('bool')]),
                'allowUploadAttendee' => new Assert\Optional([new Assert\Type('bool')]),
                'allowSignAttendee' => new Assert\Optional([new Assert\Type('bool')]),
                'allowMultiple' => new Assert\Optional([new Assert\Type('bool')]),
                'allowVisibilityPartner' => new Assert\Optional([new Assert\Type('bool')]),
                'allowUploadPartner' => new Assert\Optional([new Assert\Type('bool')]),
                'allowSignPartner' => new Assert\Optional([new Assert\Type('bool')]),
                'generated' => new Assert\Optional([new Assert\Type('bool')]),
                'allowRegenerate' => new Assert\Optional([new Assert\Type('bool')]),
                'enabled' => new Assert\Optional([new Assert\Type('bool')]),
                'templateFile' => new Assert\Optional(),
                'googleId' => new Assert\Optional([new Assert\Type('string')]),
                'tags' => new Assert\Optional(new Assert\Type('array')),
                'certifications' => new Assert\Optional(new Assert\Type('array'))
            ]
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateUpdateBodyWedofFile(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'allowExtraFields' => true,
            'fields' => [
                'description' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 1000])]),
                'qualiopiIndicators' => new Assert\Optional([
                    new Assert\Type('array'),
                    new Assert\All([
                        new Assert\Type('integer'),
                        new Assert\Range(['min' => 1, 'max' => 32])
                    ])
                ]),
                'allowVisibilityAttendee' => new Assert\Optional([new Assert\Type('bool')]),
                'allowVisibilityPartner' => new Assert\Optional([new Assert\Type('bool')]),
            ]
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateUpdateBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'fields' => [
                'name' => new Assert\Optional([new Assert\NotBlank(), new Assert\Type('string'), new Assert\NotNull(), new Assert\Length(['max' => 255])]),
                'accept' => new Assert\Optional([new Assert\NotBlank(), new Assert\Type('string'), new Assert\NotNull(), new Assert\Length(['max' => 255])]),
                'toState' => new Assert\Optional([new Assert\Type('string')]),
                'description' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 1000])]),
                'qualiopiIndicators' => new Assert\Optional([
                    new Assert\Type('array'),
                    new Assert\All([
                        new Assert\Type('integer'),
                        new Assert\Range(['min' => 1, 'max' => 32])
                    ])
                ]),
                'allowVisibilityAttendee' => new Assert\Optional([new Assert\Type('bool')]),
                'allowUploadAttendee' => new Assert\Optional([new Assert\Type('bool')]),
                'allowSignAttendee' => new Assert\Optional([new Assert\Type('bool')]),
                'allowMultiple' => new Assert\Optional([new Assert\Type('bool')]),
                'allowVisibilityPartner' => new Assert\Optional([new Assert\Type('bool')]),
                'allowUploadPartner' => new Assert\Optional([new Assert\Type('bool')]),
                'allowSignPartner' => new Assert\Optional([new Assert\Type('bool')]),
                'generated' => new Assert\Optional([new Assert\Type('bool')]),
                'allowRegenerate' => new Assert\Optional([new Assert\Type('bool')]),
                'enabled' => new Assert\Optional([new Assert\Type('bool')]),
                'templateFile' => new Assert\Optional(),
                'tags' => new Assert\Optional(new Assert\Type('array')),
                'certifications' => new Assert\Optional(new Assert\Type('array')),
            ]
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param User $user
     * @param $entityId
     * @param $field
     * @param Certification|null $entity
     * @return void
     */
    public function checkAccessOnCertification(User $user, $entityId, $field, Certification $entity = null): void
    {
        if ($this->isGranted('ROLE_ADMIN')) {
            return;
        }
        if (!$entity) {
            throw new WedofBadRequestHttpException("Erreur, la certification avec l'id " . $entityId . " n'existe pas.");
        }
        if (!$entity->isCertifier($user->getMainOrganism())) {
            throw new WedofBadRequestHttpException("Erreur, vous n'avez pas accès à la certification associée à l'id " . $entityId . " .");
        }
        if (!$this->isGranted(CertificationVoter::EDIT, $entity)) {
            throw new WedofAccessDeniedHttpException("Erreur, vous n'êtes pas autorisé à ajouter / modifier / supprimer des fichiers sur cette certification");
        }
        if (!in_array($field, [FileTypeService::CERTIFICATION_PARTNER, FileTypeService::CERTIFICATION_FOLDER, FileTypeService::CERTIFICATION_PARTNER_AUDIT])) {
            throw new LogicException("Field ne peut être que certificationFolderFileTypes ou certificationPartnerFileTypes ou certificationPartnerAuditFileTypes.");
        }
    }

    /**
     * @param User $user
     * @param $entityId
     * @param $field
     * @param Organism|null $entity
     * @return void
     */
    public function checkAccessOnOrganism(User $user, $entityId, $field, Organism $entity = null): void
    {
        if ($this->isGranted('ROLE_ADMIN')) {
            return;
        }
        if (!$entity) {
            throw new WedofBadRequestHttpException("Erreur, l'organisme avec l'id " . $entityId . " n'existe pas.");
        }
        if (!$entity->getMainUsers()->contains($user)) {
            throw new WedofBadRequestHttpException("Erreur, vous n'avez pas accès à l'organisme associé à l'id " . $entityId . " .");
        }
        if (!$this->isGranted(OrganismVoter::EDIT, $entity)) {
            throw new WedofAccessDeniedHttpException("Erreur, vous n'êtes pas autorisé à ajouter / modifier / supprimer des fichiers sur cet organisme");
        }
        if ($field !== FileTypeService::REGISTRATION_FOLDER) {
            throw new LogicException("Field ne peut être que registrationFolderFileTypes.");
        }
    }

    /**
     * @param string $siret
     * @param string $entityClass
     * @param string $field
     * @return string
     */
    private function getFileNameGenerated(string $siret, string $entityClass, string $field): string
    {
        return "NewDocument_" . $siret . "_" . $entityClass . "_" . $field;
    }

    /**
     * @param string $field
     * @param string $toState
     * @param bool $isGenerated
     */
    private function checkStateOnFileType(string $field, string $toState, bool $isGenerated): void
    {
        $allowStates = [];
        if ($field === FileTypeService::CERTIFICATION_FOLDER) {
            $allowStates = CertificationFolderStates::valuesStates();
        } else if ($field === FileTypeService::CERTIFICATION_PARTNER) {
            $allowStates = $isGenerated ? [
                CertificationPartnerStates::PROCESSING()->getValue(),
                CertificationPartnerStates::ACTIVE()->getValue(),
                CertificationPartnerStates::ABORTED()->getValue(),
                CertificationPartnerStates::REFUSED()->getValue(),
                CertificationPartnerStates::REVOKED()->getValue(),
                CertificationPartnerStates::SUSPENDED()->getValue()
            ] : [CertificationPartnerStates::PROCESSING()->getValue()];
        } else if ($field === FileTypeService::REGISTRATION_FOLDER) {
            $allowStates = RegistrationFolderStates::valuesStates();
        }
        if (!in_array($toState, $allowStates)) {
            throw new WedofBadRequestHttpException("Erreur sur la valeur retournée 'toState', valeurs possibles : " . join(",", $allowStates) . ".");
        }
    }

    /**
     * @param array $body
     * @param array $propertiesToRemove
     * @return array
     */
    private function removeUnusedProperties(array $body, array $propertiesToRemove): array
    {
        foreach ($propertiesToRemove as $property) {
            if (isset($body[$property])) {
                unset($body[$property]);
            }
        }

        return $body;
    }

    /**
     * @param array $body
     * @param bool $generated
     * @param AccessService $accessService
     * @param User $user
     * @return array
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws ReflectionException
     * @throws \Doctrine\ORM\ORMException
     */
    private function checkGeneratedParams(array $body, bool $generated, AccessService $accessService, User $user): array
    {
        if ($generated) {
            if (!$accessService->isApplicationAllowedAndEnabled(DocumentWedofApplication::getAppId(), $user->getMainOrganism())) {
                throw new WedofBadRequestHttpException("Erreur, votre abonnement ne permet pas l'accès à la génération automatique de documents ou l'application n'est pas activée");
            }
            if (!isset($body['googleId'])) {
                if (!isset($body['templateFile'])) {
                    throw new WedofBadRequestHttpException("Erreur, vous devez ajouter un modèle de fichier si vous souhaitez générer automatiquement le document.");
                }
                if (!($body['templateFile'] instanceof File)) {
                    throw new WedofBadRequestHttpException("Erreur, le champ 'templateFile' n'est pas un fichier.");
                }
            }
            $body['accept'] = ".docx,.doc,.pdf";
            $body['allowRegenerate'] = true;
            $body['enabled'] = true;
        } else {
            $body['generated'] = false;
            $body['allowRegenerate'] = false;
            $body['templateFile'] = null;
            $body['enabled'] = false;
            $body['accept'] = $body['accept'] ?? ".*";
        }

        return $body;
    }

    /**
     * @param array $body
     * @param bool $generated
     * @return array
     */
    private function checkAllowAttendeeParams(array $body, bool $generated): array
    {
        $body['allowVisibilityAttendee'] = $body['allowVisibilityAttendee'] ?? false;
        $body['allowUploadAttendee'] = $generated || !$body['allowVisibilityAttendee'] ? false : ($body['allowUploadAttendee'] ?? false);
        if ($generated) {
            $body['allowSignAttendee'] = !$body['allowVisibilityAttendee'] ? false : ($body['allowSignAttendee'] ?? false);
        } else {
            $this->removeUnusedProperties($body, ['allowSignAttendee']);
        }
        return $body;
    }

    /**
     * @param array $body
     * @param bool $generated
     * @return array
     */
    private function checkAllowPartnerParams(array $body, bool $generated): array
    {
        $body['allowVisibilityPartner'] = $body['allowVisibilityPartner'] ?? true;
        $body['allowUploadPartner'] = $generated || !$body['allowVisibilityPartner'] ? false : ($body['allowUploadPartner'] ?? true);
        if ($generated) {
            $body['allowSignPartner'] = !$body['allowVisibilityPartner'] ? false : ($body['allowSignPartner'] ?? false);
        } else {
            $this->removeUnusedProperties($body, ['allowSignPartner']);
        }
        return $body;
    }

    /**
     * @return array[]
     */
    private function getFiles(): array
    {
        return [
            [
                "title" => 'Démarrer à partir de zéro',
                "description" => 'Créer votre modèle directement dans Wedof',
                "tags" => [],
                "templateName" => 'TemplateInWedof',
                "hasAccess" => true,
                "additionalInformations" => ["format" => '.doc,.docx'],
                "display" => true,
            ],
            [
                "title" => 'À partir d\'un document existant',
                "description" => 'Utilisez un modèle .doc/.docx existant',
                "tags" => [],
                "templateName" => 'New',
                "hasAccess" => true,
                "additionalInformations" => ["format" => '.doc,.docx'],
                "display" => true,
            ],
            [
                "title" => 'À partir d\'un fichier .pptx',
                "description" => 'Choisissez votre parchemin',
                "tags" => [],
                "templateName" => 'New',
                "hasAccess" => true,
                "additionalInformations" => ["format" => '.pptx'],
                "display" => true,
            ],
            [
                "title" => 'Depuis Wedof',
                "description" => 'Créer votre parchemin (.pptx) directement dans Wedof',
                "tags" => [],
                "templateName" => 'TemplateInWedof',
                "hasAccess" => true,
                "additionalInformations" => ["format" => '.pptx'],
                "display" => true,
            ]
        ];
    }
}
