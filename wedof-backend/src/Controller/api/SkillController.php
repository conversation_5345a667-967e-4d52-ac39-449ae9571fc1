<?php
// src/Controller/api/SkillController.php
namespace App\Controller\api;

use App\Entity\Skill;
use App\Entity\User;
use App\Exception\WedofAccessDeniedHttpException;
use App\Exception\WedofBadRequestHttpException;
use App\Library\utils\enums\CertificationSkillType;
use App\Library\utils\enums\CertificationTypes;
use App\Security\Voter\SkillVoter;
use App\Service\CertificationService;
use App\Service\SkillService;
use FOS\RestBundle\Context\Context;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcherInterface;
use FOS\RestBundle\View\View;
use Knp\Component\Pager\PaginatorInterface;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validation;
use Throwable;

/**
 * Class SkillController
 * @package App\Controller\api
 *
 */
class SkillController extends AbstractFOSRestController
{
    //-------------------
    // METHODES PUBLIQUES
    //-------------------
    /**
     * @Rest\Get("/api/skills/{id}")
     * @IsGranted(SkillVoter::VIEW, subject="skill",  message="not allowed")
     * @Rest\View(StatusCode = 200)
     * @param Skill $skill
     * @return Response
     */
    public function show(Skill $skill): Response
    {
        $context = new Context();
        $context->setGroups(['Default', 'fullSkills']);
        $view = $this->view($skill, 200);
        $view->setContext($context);
        return $this->handleView($view);
    }

    /**
     * @Rest\Get("/api/skills")
     * @IsGranted("ROLE_USER", message="not allowed")
     * @Rest\QueryParam(name="certifInfo", requirements=@Assert\Type("string"), nullable=true)
     * @Rest\QueryParam(name="limit", requirements="\d+", default="100")
     * @Rest\QueryParam(name="page", requirements="\d+", default="1")
     * @Rest\QueryParam(name="sort", requirements=@Assert\Choice({"id", "order"}), default="order", description="Tri les résultats sur un critère. Valeurs possibles: 'id' (id de base de donnée), 'order' (ordre de la compétence) - par défaut 'order'.")
     * @Rest\QueryParam(name="order", requirements=@Assert\Choice({"asc", "desc"}), default="asc", description="Tri les résultats par ordre ascendant ou descendant - par défaut descendant.")
     * @Rest\View(StatusCode = 200)
     * @param SkillService $skillService
     * @param ParamFetcherInterface $paramFetcher
     * @param CertificationService $certificationService
     * @param PaginatorInterface $paginator
     * @return Response
     */
    public function list(SkillService $skillService, ParamFetcherInterface $paramFetcher, CertificationService $certificationService, PaginatorInterface $paginator): Response
    {
        $parameters = $paramFetcher->all(true);

        if (empty($parameters['certifInfo'])) {
            throw new WedofBadRequestHttpException("Erreur, vous devez renseigner le paramètre certifInfo.");
        }

        $certification = $certificationService->getByCertifInfo($parameters['certifInfo']);
        if (!$certification) {
            throw new WedofBadRequestHttpException("Erreur, la certification n'a pas été trouvée");
        }
        $parameters['certification'] = $certification;
        $isRSCertification = $certification->getType() === CertificationTypes::RS()->getValue();
        $parameters['type'] = $isRSCertification ? CertificationSkillType::SKILL()->getValue() : CertificationSkillType::SKILL_SET()->getValue();
        $context = new Context();
        $context->setGroups(['Default', 'fullSkills']);
        $data = $paginator->paginate($skillService->listReturnQueryBuilder($parameters), intval($parameters['page']), intval($parameters['limit']));
        $view = $this->view($data->getItems(), 200)->setContext($context);
        $view->setHeader("x-total-count", $data->getTotalItemCount());
        $view->setHeader("x-current-page", $data->getCurrentPageNumber());
        $view->setHeader("x-item-per-page", $data->getItemNumberPerPage());
        return $this->handleView($view);
    }

    /**
     * @Rest\Post ("/api/skills")
     * @IsGranted("ROLE_USER", message="not allowed")
     * @Rest\View(StatusCode = 201)
     * @param SkillService $skillService
     * @param CertificationService $certificationService
     * @param Request $request
     * @return Skill|View
     * @throws Throwable
     */
    public function create(SkillService $skillService, CertificationService $certificationService, Request $request)
    {
        /* @var $user User */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();
        $body = json_decode($request->getContent(), true);
        $violations = $this->validateCreateBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }
        $certification = $certificationService->getByCertifInfo($body['certifInfo']);
        if (!$certification) {
            throw new WedofBadRequestHttpException("Erreur, la certification n'a pas été trouvée");
        }
        if (!$certification->isCertifier($organism)) {
            throw new WedofAccessDeniedHttpException("Erreur, vous n'avez pas les droits pour ajouter une compétence sur la certification avec le certifInfo " . $body['certifInfo']);
        }

        $parentSkill = null;
        if (!empty($body['parentSkillId'])) {
            $parentSkill = $skillService->getById($body['parentSkillId']);
            if (!$parentSkill) {
                throw new WedofBadRequestHttpException("Erreur, aucun bloc de compétences n'a été trouvé avec l'id " . $body['parentSkillId']);
            }
            if ($parentSkill->getType() !== CertificationSkillType::SKILL_SET()->getValue()) {
                throw new WedofBadRequestHttpException("Erreur, vous ne pouvez pas ajouter une compétence sur une compétence qui n'est pas un bloc de compétence.");
            }
            if ($parentSkill->getCertification() !== $certification) {
                throw new WedofBadRequestHttpException("Erreur, le bloc de compétences avec l'id " . $body['parentSkillId'] . " ne correspond pas à la certification.");
            }
        }

        if ($certification->getType() === CertificationTypes::RS()->getValue() && $parentSkill) {
            throw new WedofBadRequestHttpException("Erreur, vous ne pouvez pas ajouter de bloc de compétences sur une certification de type RS.");
        } else if ($certification->getType() === CertificationTypes::RNCP()->getValue() && !$parentSkill) {
            throw new WedofBadRequestHttpException("Erreur, vous ne pouvez pas ajouter de compétences sans bloc de compétences sur une certification de type RNCP.");
        } else if ($certification->getType() === CertificationTypes::INTERNAL()->getValue()) {
            $isSkillSet = isset($body['createParentSkill']) ? filter_var($body['createParentSkill'], FILTER_VALIDATE_BOOLEAN) : false;
            if ($isSkillSet) {
                $body['type'] = CertificationSkillType::SKILL_SET()->getValue();
            }
            if (!$parentSkill && !$isSkillSet) {
                throw new WedofBadRequestHttpException("Erreur, vous ne pouvez pas ajouter de compétences sans bloc de compétences sur une certification interne.");
            }
        } else {
            throw new WedofBadRequestHttpException("Erreur, vous ne pouvez pas ajouter des compétences sur cette certification avec le certifInfo " . $body['certifInfo']);
        }

        return $skillService->create($body, $certification, $parentSkill);
    }

    /**
     * @Rest\Put("/api/skills/{id}")
     * @IsGranted(SkillVoter::EDIT, subject="skill",  message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @param Skill $skill
     * @param SkillService $skillService
     * @param Request $request
     * @return View|Response
     * @throws Throwable
     */
    public function update(Skill $skill, SkillService $skillService, Request $request)
    {
        $certificationType = $skill->getCertification()->getType();
        $isSkillSet = $skill->getType() === CertificationSkillType::SKILL_SET()->getValue();

        if ($isSkillSet && $certificationType === CertificationTypes::RNCP()->getValue()) {
            throw new WedofBadRequestHttpException("Erreur, vous ne pouvez pas modifier un bloc de compétences sur une certification de type RNCP.");
        }

        $body = json_decode($request->getContent(), true);
        if (empty($body)) {
            throw new WedofBadRequestHttpException("Erreur, vérifiez le body envoyé.");
        }
        $violations = $this->validateUpdateBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }

        $certification = $skill->getCertification();
        $parentSkill = $skill->getParentSkill();
        if (!empty($body['parentSkillId'])) {
            $parentSkill = $skillService->getById($body['parentSkillId']);
            if (!$parentSkill) {
                throw new WedofBadRequestHttpException("Erreur, aucun bloc de compétences n'a été trouvé avec l'id " . $body['parentSkillId']);
            }
            if ($parentSkill->getCertification() !== $certification) {
                throw new WedofBadRequestHttpException("Erreur, le bloc de compétences avec l'id " . $body['parentSkillId'] . " ne correspond pas à la certification.");
            }
            if ($parentSkill->getType() !== CertificationSkillType::SKILL_SET()->getValue()) {
                throw new WedofBadRequestHttpException("Erreur, vous ne pouvez pas modifier une compétence sur une compétence qui n'est pas un bloc de compétence.");
            }
        }

        if ($certificationType === CertificationTypes::RS()->getValue() && $parentSkill) {
            throw new WedofBadRequestHttpException("Erreur, il n'est pas possible de modifier une compétence associée à un bloc de compétences sur une certification de type RS.");
        } else if ($certificationType === CertificationTypes::RNCP()->getValue() && !$parentSkill) {
            throw new WedofBadRequestHttpException("Erreur, il n'est pas possible de modifier une compétence sans être associée à un bloc de compétence sur une certification de type RNCP.");
        } else if ($certificationType === CertificationTypes::INTERNAL()->getValue()) {
            if (!$isSkillSet && !$parentSkill) {
                throw new WedofBadRequestHttpException("Erreur, il n'est pas possible de modifier une compétence sans être associée à un bloc de compétence sur une certification interne.");
            }
        }

        $skillUpdated = $skillService->update($skill, $body, $parentSkill);
        $context = new Context();
        $context->setGroups(['Default', 'fullSkills']);
        return $this->handleView($this->view($skillUpdated, 200)->setContext($context));
    }

    /**
     * @Rest\Delete("/api/skills/{id}")
     * @IsGranted(SkillVoter::EDIT, subject="skill",  message="not allowed")
     * @Rest\View(StatusCode = 204)
     *
     * @param Skill $skill
     * @param SkillService $skillService
     * @return void
     * @throws Throwable
     */
    public function delete(Skill $skill, SkillService $skillService)
    {
        $isSkillSet = $skill->getType() === CertificationSkillType::SKILL_SET()->getValue();
        if ($isSkillSet) {
            $certificationType = $skill->getCertification()->getType();
            if ($certificationType === CertificationTypes::RNCP()->getValue()) {
                throw new WedofBadRequestHttpException("Erreur, vous ne pouvez pas supprimer un bloc de compétences sur une certification de type RNCP.");
            } else if ($certificationType === CertificationTypes::INTERNAL()->getValue() && $skill->getIndividualSkills()->count() > 0) {
                throw new WedofBadRequestHttpException("Erreur, vous ne pouvez pas supprimer le bloc de compétences " . $skill->getFullCode() . " car des compétences ont déjà été créées.");
            }
        }
        $skillService->delete($skill, $isSkillSet);
    }


    //----------------
    // METHODES PRIVES
    //----------------

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateCreateBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'allowExtraFields' => true,
            'fields' => [
                'certifInfo' => new Assert\Required([new Assert\NotBlank(), new Assert\Type('string'), new Assert\NotNull(), new Assert\Length(['max' => 255])]),
                'label' => new Assert\Required([new Assert\Type('string'), new Assert\Length(['max' => 255])]),
                'modalities' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 5000])]),
                'description' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 5000])]),
                'parentSkillId' => new Assert\Optional([new Assert\Type('integer')]),
                'createParentSkill' => new Assert\Optional([new Assert\Type('boolean')])
            ]
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateUpdateBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'allowExtraFields' => true,
            'fields' => [
                'label' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 255])]),
                'modalities' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 5000])]),
                'description' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 5000])]),
                'parentSkillId' => new Assert\Optional([new Assert\Type('integer')]),
            ]
        ]);
        return $validator->validate($body, $constraints);
    }
}
