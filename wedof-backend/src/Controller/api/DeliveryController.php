<?php
// src/Controller/api/DeliveryController.php

namespace App\Controller\api;

use App\Entity\CertificationFolder;
use App\Entity\CertificationPartner;
use App\Entity\RegistrationFolder;
use App\Entity\Webhook;
use App\Exception\WedofAccessDeniedHttpException;
use App\Exception\WedofNotFoundHttpException;
use App\Repository\DeliveryRepository;
use App\Repository\RegistrationFolderRepository;
use App\Security\Voter\CertificationFolderVoter;
use App\Security\Voter\CertificationPartnerVoter;
use App\Security\Voter\RegistrationFolderVoter;
use App\Security\Voter\WebhookVoter;
use App\Service\CertificationFolderService;
use App\Service\CertificationPartnerService;
use App\Service\ProcessusMetierService;
use App\Service\WebhookService;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcherInterface;
use Knp\Component\Pager\PaginatorInterface;
use Nelmio\ApiDocBundle\Annotation as ApiDoc;
use OpenApi\Annotations as OA;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Throwable;

/**
 * Class DeliveryController
 * @package App\Controller\api
 *
 * @OA\Tag(name="Delivery")
 * @ApiDoc\Security(name="accessCode")
 */
class DeliveryController extends AbstractFOSRestController
{
    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    /**
     * @Rest\Get("/api/webhooks/{id}/deliveries")
     * @IsGranted(WebhookVoter::VIEW, subject="webhook", message="not allowed")
     * @Rest\QueryParam(name="limit", requirements="\d+", default="25", description="Nombre d'éléments retourné par requête - par défaut 25.")
     * @Rest\QueryParam(name="page", requirements="\d+", default="1", description="Numéro de page de la requête - par défaut la première.")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"webhooks", "default"})
     * @OA\Get (
     *     summary="Récupération des deliveries d'un Webhook.",
     *     description="Renvoie la liste des deliveries récent du Webhook (les anciens sont supprimés après une certaine période)"
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un tableau de deliveries au format JSON",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/Delivery")
     *     )
     * )
     *
     * @param Webhook $webhook
     * @param ParamFetcherInterface $paramFetcher
     * @param PaginatorInterface $paginator
     * @param WebhookService $webhookService
     * @return Response
     */
    public function listByWebhook(Webhook $webhook, ParamFetcherInterface $paramFetcher, PaginatorInterface $paginator, WebhookService $webhookService): Response
    {
        $parameters = $paramFetcher->all(true);
        $data = $paginator->paginate($webhookService->listDeliveriesByWebhookReturnQueryBuilder($webhook), intval($parameters['page']), intval($parameters['limit']));
        $view = $this->view($data->getItems(), 200);
        $view->setHeader("x-total-count", $data->getTotalItemCount());
        $view->setHeader("x-current-page", $data->getCurrentPageNumber());
        $view->setHeader("x-item-per-page", $data->getItemNumberPerPage());
        return $this->handleView($view);
    }

    /**
     * @Rest\Get("/api/deliveries/{entityClass}/{entityId}", requirements={"entityClass"="RegistrationFolder|CertificationFolder|CertificationPartner"})
     * @Rest\QueryParam(name="limit", requirements="\d+", default="25", description="Nombre d'éléments retourné par requête - par défaut 25.")
     * @Rest\QueryParam(name="page", requirements="\d+", default="1", description="Numéro de page de la requête - par défaut la première.")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"webhooks", "default"})
     * @OA\Get (
     *     summary="Récupération des deliveries d'un objet (Dossier de formation ou Dossier de certification ou Partenariat de certification).",
     *     description="Renvoie la liste des deliveries de l'objet choisi. L'entityId correspond à l'externalId du dossier de formation ou de l'externalId du dossier de certification ou l'id du partenariat."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un tableau de deliveries au format JSON",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/Delivery")
     *     )
     * )
     *
     * @param $entityId
     * @param string $entityClass
     * @param ParamFetcherInterface $paramFetcher
     * @param PaginatorInterface $paginator
     * @param DeliveryRepository $deliveryRepository
     * @param RegistrationFolderRepository $registrationFolderRepository
     * @param CertificationFolderService $certificationFolderService
     * @param CertificationPartnerService $certificationPartnerService
     * @return Response
     */
    public function listByEntity($entityId, string $entityClass, ParamFetcherInterface $paramFetcher, PaginatorInterface $paginator, DeliveryRepository $deliveryRepository, RegistrationFolderRepository $registrationFolderRepository, CertificationFolderService $certificationFolderService, CertificationPartnerService $certificationPartnerService): Response
    {
        if ($entityClass === RegistrationFolder::CLASSNAME) {
            $registrationFolder = $registrationFolderRepository->findOneByExternalId($entityId);
            if (!$registrationFolder) {
                throw new WedofNotFoundHttpException("Le dossier de formation " . $entityId . " n'a pas été trouvé.");
            }
            if (!$this->isGranted(RegistrationFolderVoter::OWNER_VIEW, $registrationFolder)) {
                throw new WedofAccessDeniedHttpException("Erreur, vous n'êtes pas autorisé à lister les deliveries pour ce dossier de formation");
            }
        } else if ($entityClass === CertificationFolder::CLASSNAME) {
            $certificationFolder = $this->retrieveCertificationFolder($entityId, $certificationFolderService);
            if (!$certificationFolder) {
                throw new WedofNotFoundHttpException("Le dossier de certification " . $entityId . " n'a pas été trouvé.");
            }
            $entityId = $certificationFolder->getId(); // hack because entityId is based on "id" on BDD, we cannot list using externalId as entityId otherwise we will need to do a big migration
            if (!$this->isGranted(CertificationFolderVoter::VIEW, $certificationFolder)) {
                throw new WedofAccessDeniedHttpException("Erreur, vous n'êtes pas autorisé à lister les deliveries pour ce dossier de certification");
            }
        } else if ($entityClass === CertificationPartner::CLASSNAME) {
            $certificationPartner = $certificationPartnerService->getById($entityId);
            if (!$certificationPartner) {
                throw new WedofNotFoundHttpException("Le partenariat " . $entityId . " n'a pas été trouvé.");
            }
            if (!$this->isGranted(CertificationPartnerVoter::CERTIFIER_EDIT, $certificationPartner)) {
                throw new WedofAccessDeniedHttpException("Erreur, vous n'êtes pas autorisé à lister les deliveries pour ce partenariat");
            }
        }
        $parameters = $paramFetcher->all(true);
        $data = $paginator->paginate($deliveryRepository->listDeliveriesByEntityAndType($entityId, $entityClass, 'webhook'), intval($parameters['page']), intval($parameters['limit']));
        $view = $this->view($data->getItems(), 200);
        $view->setHeader("x-total-count", $data->getTotalItemCount());
        $view->setHeader("x-current-page", $data->getCurrentPageNumber());
        $view->setHeader("x-item-per-page", $data->getItemNumberPerPage());
        return $this->handleView($view);
    }

    /**
     * @Rest\Get("/api/flow-runs/{entityClass}/{entityId}", requirements={"entityClass"="RegistrationFolder|CertificationFolder"})
     * @Rest\View(StatusCode = 200)
     * @ApiDoc\Areas({"webhooks", "default"})
     * @OA\Get (
     *     summary="Récupération des exécutions de processus d'un objet (Dossier de formation ou Dossier de certification).",
     *     description="Renvoie la liste des exécutions de processus de l'objet choisi. L'entityId correspond à l'externalId du dossier."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un tableau d'exécutions de processus au format JSON",
     *     @OA\MediaType(mediaType="application/json")
     * )
     * @param $entityId
     * @param string $entityClass
     * @param RegistrationFolderRepository $registrationFolderRepository
     * @param CertificationFolderService $certificationFolderService
     * @param ProcessusMetierService $processusMetierService
     * @return Response
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    public function listFlowRunsByEntity($entityId, string $entityClass, RegistrationFolderRepository $registrationFolderRepository, CertificationFolderService $certificationFolderService, ProcessusMetierService $processusMetierService): Response
    {
        $entity = null;
        if ($entityClass === RegistrationFolder::CLASSNAME) {
            $entity = $registrationFolderRepository->findOneByExternalId($entityId);
            if (!$entity) {
                throw new WedofNotFoundHttpException("Le dossier de formation " . $entityId . " n'a pas été trouvé.");
            }
            if (!$this->isGranted(RegistrationFolderVoter::OWNER_VIEW, $entity)) {
                throw new WedofAccessDeniedHttpException("Erreur, vous n'êtes pas autorisé à lister les exécutions de processus pour ce dossier de formation");
            }
        } else if ($entityClass === CertificationFolder::CLASSNAME) {
            $entity = $this->retrieveCertificationFolder($entityId, $certificationFolderService);
            if (!$entity) {
                throw new WedofNotFoundHttpException("Le dossier de certification " . $entityId . " n'a pas été trouvé.");
            }
            if (!$this->isGranted(CertificationFolderVoter::VIEW, $entity)) {
                throw new WedofAccessDeniedHttpException("Erreur, vous n'êtes pas autorisé à lister les exécutions de processus pour ce dossier de certification");
            }
        }
        try {
            $flowRuns = $processusMetierService->getFlowRunsByEntity($entity);
            $view = $this->view($flowRuns, 200);
            return $this->handleView($view);
        } catch (\Exception $e) {
            $view = $this->view(['data' => [], 'next' => null, 'previous' => null], 200);
            return $this->handleView($view);
        }
    }

    /**
     * @Rest\Post("/api/webhooks/{id}/delivery/{idDelivery}/retry")
     * @Rest\View(StatusCode = 200)
     * @IsGranted(WebhookVoter::EDIT, subject="webhook", message="not allowed")
     *
     * @ApiDoc\Areas({"webhooks", "default"})
     * @OA\Post (
     *     summary="Rejouer un delivery.",
     *     description="Renvoie le delivery à l'adresse du Webhook"
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations du delivery",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/Delivery")
     *     )
     * )
     *
     * @param Webhook $webhook
     * @param WebhookService $webhookService
     * @param DeliveryRepository $deliveryRepository
     * @param int $idDelivery
     * @return Response
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function retryByWebhook(Webhook $webhook, WebhookService $webhookService, DeliveryRepository $deliveryRepository, int $idDelivery): Response
    {
        // This is a legacy endpoint, as we do not actually need the webhook id to retry the delivery
        // TODO consider deprecating / removing it
        $delivery = $deliveryRepository->findOneBy(["id" => $idDelivery, "webhook" => $webhook]);
        if ($delivery) {
            $webhookService->retry($delivery);
            return new Response(null, Response::HTTP_OK);
        } else {
            throw new WedofNotFoundHttpException("Erreur, le delivery n'a pas été trouvé");
        }
    }

    /**
     * @Rest\Post("/api/deliveries/{idDelivery}/retry")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"webhooks", "default"})
     * @OA\Post (
     *     summary="Rejouer un delivery.",
     *     description="Renvoie le delivery à l'adresse du Webhook"
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations du delivery",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/Delivery")
     *     )
     * )
     *
     * @param WebhookService $webhookService
     * @param DeliveryRepository $deliveryRepository
     * @param int $idDelivery
     * @return Response
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function retry(WebhookService $webhookService, DeliveryRepository $deliveryRepository, int $idDelivery): Response
    {
        $delivery = $deliveryRepository->findOneBy(["id" => $idDelivery]);
        if ($delivery) {
            if (!$this->isGranted(WebhookVoter::EDIT, $delivery->getWebhook())) {
                throw new WedofAccessDeniedHttpException("Erreur, vous n'êtes pas autorisé à rejouer ce delivery");
            }
            $webhookService->retry($delivery);
            if ($delivery->getStatusCode() >= 300) {
                throw new WedofNotFoundHttpException("Erreur, statusCode : " . $delivery->getStatusCode() . ", " . $delivery->getErrorMessage());
            } else {
                return new Response(null, Response::HTTP_OK);
            }
        } else {
            throw new WedofNotFoundHttpException("Erreur, le delivery n'a pas été trouvé");
        }
    }

    //-------------------
    // METHODES PRIVEES
    //-------------------

    /**
     * @param $externalId
     * @param CertificationFolderService $certificationFolderService
     * @return CertificationFolder
     */
    private function retrieveCertificationFolder($externalId, CertificationFolderService $certificationFolderService): CertificationFolder
    {
        if (is_numeric($externalId)) {
            $certificationFolder = $certificationFolderService->getById($externalId);
        } else {
            $certificationFolder = $certificationFolderService->getByExternalId($externalId);
        }
        return $certificationFolder;
    }
}
