<?php
// src/Controller/api/RegistrationFolderController.php

namespace App\Controller\api;

use App\Entity\Attendee;
use App\Entity\Config;
use App\Entity\Organism;
use App\Entity\RegistrationFolder;
use App\Entity\RegistrationFolderFile;
use App\Entity\User;
use App\Exception\ServerException;
use App\Exception\WedofAccessDeniedHttpException;
use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofConnectionException;
use App\Exception\WedofNotFoundHttpException;
use App\Exception\WedofSubscriptionException;
use App\Library\utils\enums\AuthMethodAttendee;
use App\Library\utils\enums\CertificationFolderStates;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\enums\DocumentSignedStates;
use App\Library\utils\enums\FileStates;
use App\Library\utils\enums\MessageStates;
use App\Library\utils\enums\PeriodTypes;
use App\Library\utils\enums\PoleEmploiRegionCode;
use App\Library\utils\enums\RegistrationFolderBillingStates;
use App\Library\utils\enums\RegistrationFolderControlStates;
use App\Library\utils\enums\RegistrationFolderStates;
use App\Library\utils\enums\SessionStates;
use App\Library\utils\Tools;
use App\Repository\RegistrationFolderFileRepository;
use App\Repository\RegistrationFolderRepository;
use App\Security\Voter\CertificationVoter;
use App\Security\Voter\OrganismVoter;
use App\Security\Voter\ProposalVoter;
use App\Security\Voter\RegistrationFolderVoter;
use App\Security\Voter\SessionVoter;
use App\Security\Voter\TrainingActionVoter;
use App\Security\Voter\TrainingVoter;
use App\Service\AttendeeService;
use App\Service\CertificationFolderService;
use App\Service\CertificationService;
use App\Service\ConfigService;
use App\Service\OrganismService;
use App\Service\ProposalService;
use App\Service\RegistrationFolderFileService;
use App\Service\RegistrationFolderReasonService;
use App\Service\RegistrationFolderService;
use App\Service\SessionService;
use App\Service\TrainingActionService;
use App\Service\TrainingService;
use DateTime;
use DateTimeZone;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use ErrorException;
use Exception;
use FOS\RestBundle\Context\Context;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcherInterface;
use FOS\RestBundle\View\View;
use Knp\Component\Pager\PaginatorInterface;
use LogicException;
use Nelmio\ApiDocBundle\Annotation as ApiDoc;
use OpenApi\Annotations as OA;
use ReflectionException;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validation;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Throwable;

/**
 * Class RegistrationFolderController
 * @package App\Controller\api
 *
 * @OA\Tag(name="Registration Folder")
 * @ApiDoc\Security(name="accessCode")
 */
class RegistrationFolderController extends AbstractFOSRestController
{
    private static int $ASYNC_DELAY_TO_ADD = 3000;

    //--------------
    // APP ENDPOINTS
    //--------------
    /**
     * @Rest\Get("/app/public/registrationFolders/{externalId}/authMethodForAttendee")
     * @Rest\View(StatusCode = 200)
     *
     * @param RegistrationFolder $registrationFolder
     * @param CertificationFolderService $certificationFolderService
     * @return array
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function authForAttendee(RegistrationFolder $registrationFolder, CertificationFolderService $certificationFolderService): array
    {
        $attendee = $registrationFolder->getAttendee();
        $certificationFolder = $registrationFolder->getCertificationFolder();
        $alreadyLoggedWithId360 = $attendee->getExternalId();
        $isCPF = $registrationFolder->getType() === DataProviders::CPF()->getValue();
        $cdcExcluded = !empty($certificationFolder) && $certificationFolder->isCdcExcluded();
        $hasProcessedOk = $certificationFolderService->hasNoCpfProcessedOkForAttendee($attendee);
        if ($alreadyLoggedWithId360 || $isCPF || $hasProcessedOk || $cdcExcluded || $attendee->isCdcCompliant()) {
            $method = AuthMethodAttendee::MAGIC_LINK()->getValue();
        } else {
            $method = AuthMethodAttendee::IDENTIFICATION_360()->getValue();
        }
        return ['method' => $method];
    }

    /**
     * @Rest\Get("/app/public/registrationFolders/{externalId}/sendMagicLinkForAttendee/{redirectURL}", requirements={"redirectURL"=".+"})
     * @Rest\View(StatusCode = 200)
     *
     * @param RegistrationFolder $registrationFolder
     * @param AttendeeService $attendeeService
     * @param string $redirectURL
     * @throws ReflectionException
     * @throws TransportExceptionInterface
     */
    public function sendMagicLinkForAttendee(RegistrationFolder $registrationFolder, AttendeeService $attendeeService, string $redirectURL): void
    {
        $attendeeService->sendMagicLink($registrationFolder, RegistrationFolder::CLASSNAME, $redirectURL);
    }

    /**
     * @Rest\Get("/app/registrationFolders/{externalId}/magicLink")
     * @Rest\View(StatusCode = 200)
     *
     * @param RegistrationFolder $registrationFolder
     * @param AttendeeService $attendeeService
     * @return Response
     * @throws ReflectionException
     */
    public function getMagicLink(RegistrationFolder $registrationFolder, AttendeeService $attendeeService): Response
    {
        if ($this->getUser() && $this->isGranted("IS_IMPERSONATOR")) {
            $redirectURL = '/apprenant/formation/dossier/' . $registrationFolder->getExternalId();
            return new Response(json_encode(['url' => ($attendeeService->generateMagicLink($registrationFolder->getAttendee()) . '&redirectURL=' . $redirectURL)]));
        } else {
            return new Response(null, 403);
        }
    }

    /**
     * @Rest\Get("/app/registrationFolders/revenue/{columnId}")
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_REGISTRATIONFOLDER:READ')", message="not allowed")
     * @Rest\QueryParam(name="proposalCode", requirements=@Assert\Type("string"), nullable=true)
     * @Rest\QueryParam(name="siret", requirements=@Assert\Type("string"), nullable=true)
     * @Rest\QueryParam(name="organismType", requirements=@Assert\Choice("self", "partners"), default="self")
     * @Rest\QueryParam(name="type", requirements=@Assert\Type("string"), default="all")
     * @Rest\QueryParam(name="state", requirements=@Assert\Type("string"), default="all")
     * @Rest\QueryParam(name="billingState", requirements=@Assert\Type("string"), default="all")
     * @Rest\QueryParam(name="controlState", requirements=@Assert\Type("string"), default="all")
     * @Rest\QueryParam(name="certificationFolderState", requirements=@Assert\Type("string"), default="all")
     * @Rest\QueryParam(name="certifInfo", requirements=@Assert\Type("string"), nullable=true)
     * @Rest\QueryParam(name="sessionId", requirements=".+\/.+\/.+", nullable=true)
     * @Rest\QueryParam(name="trainingActionId", requirements=".+\/.+", nullable=true)
     * @Rest\QueryParam(name="trainingId", requirements=".+", nullable=true)
     * @Rest\QueryParam(name="messageState", requirements=@Assert\Type("string"), nullable=true)
     * @Rest\QueryParam(name="messageTemplate", requirements=@Assert\Type("string"), nullable=true)
     * @Rest\QueryParam(name="filterOnStateDate", requirements=@Assert\Choice({"lastUpdate", "updatedOn", "createdOn", "notProcessedDate", "validatedDate", "acceptedDate", "inTrainingDate", "terminatedDate", "serviceDoneDeclaredDate", "serviceDoneValidatedDate", "billedDate", "refusedByAttendeeDate", "refusedByOrganismDate", "canceledByAttendeeDate", "canceledByOrganismDate", "canceledByAttendeeNotRealizedDate", "rejectedWithoutTitulaireSuiteDate", "sessionStartDate", "sessionEndDate", "paymentScheduledDate"}), default="lastUpdate")
     * @Rest\QueryParam(name="period", requirements=@Assert\Choice({"nextYear", "previousYear", "currentYear", "rollingYear", "rollingYearFuture", "nextMonth", "previousMonth", "currentMonth", "rollingMonth", "rollingMonthFuture", "nextWeek", "previousWeek", "currentWeek", "rollingWeek", "rollingWeekFuture", "tomorrow", "today", "yesterday", "custom"}), nullable=true, description="Filtre les dossiers de formation selon la période choisie. Valeurs possibles : 'nextYear', 'previousYear', 'currentYear', 'rollingYear', 'rollingYearFuture', 'nextMonth', 'previousMonth', 'currentMonth', 'rollingMonth', 'rollingMonthFuture', 'nextWeek', 'previousWeek', 'currentWeek', 'rollingWeek', 'rollingWeekFuture', 'tomorrow', 'today', 'yesterday', 'custom'.")
     * @Rest\QueryParam(name="since", requirements=@Assert\AtLeastOneOf({@Assert\DateTime(format="Y-m-d\TH:i:sO"), @Assert\Date()}), nullable=true)
     * @Rest\QueryParam(name="until", requirements=@Assert\AtLeastOneOf({@Assert\DateTime(format="Y-m-d\TH:i:sO"), @Assert\Date()}), nullable=true)
     * @Rest\QueryParam(name="tags", requirements=@Assert\Type("string"), nullable=true)
     * @Rest\QueryParam(name="metadata", requirements=@Assert\Type("string"), nullable=true, description="Permet d'effectuer une une valeur d'un 'metadata' format : cle:valeur.")
     * @Rest\QueryParam(name="query", requirements=@Assert\Type("string"), nullable=true)
     * @Rest\QueryParam(name="sort", requirements=@Assert\Choice({"firstName", "lastName", "lastUpdate", "id"}), default="lastUpdate")
     * @Rest\QueryParam(name="limit", requirements="\d+", default="20")
     * @Rest\QueryParam(name="page", requirements="\d+", default="1", description="Numéro de page de la requête - par défaut la première.")
     * @Rest\QueryParam(name="order", requirements=@Assert\Choice({"asc", "desc"}), default="desc")
     * @Rest\View(StatusCode = 200)
     *
     * @param RegistrationFolderService $registrationFolderService
     * @param ProposalService $proposalService
     * @param OrganismService $organismService
     * @param SessionService $sessionService
     * @param TrainingActionService $trainingActionService
     * @param TrainingService $trainingService
     * @param CertificationService $certificationService
     * @param ParamFetcherInterface $paramFetcher
     * @param string $columnId
     * @return float
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     * @throws WedofSubscriptionException
     */
    public function revenueByColumn(RegistrationFolderService $registrationFolderService, ProposalService $proposalService, OrganismService $organismService, SessionService $sessionService, TrainingActionService $trainingActionService, TrainingService $trainingService, CertificationService $certificationService, ParamFetcherInterface $paramFetcher, string $columnId): float
    {
        /* @var $user User */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();
        $parameters = $paramFetcher->all(true);

        $parameters = $this->validateListParameters($user, $organism, $parameters, $proposalService, $organismService, $sessionService, $trainingActionService, $trainingService, $certificationService);

        $subscription = $organism->getSubscription();
        $isAllowAnalytics = $subscription->isAllowAnalytics();
        return $registrationFolderService->revenueByColumn($organism, $parameters, $columnId, $isAllowAnalytics);
    }

    /**
     * @Rest\Get("/app/registrationFolders")
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_REGISTRATIONFOLDER:READ')", message="not allowed")
     * @Rest\QueryParam(name="proposalCode", requirements=@Assert\Type("string"), nullable=true)
     * @Rest\QueryParam(name="siret", requirements=@Assert\Type("string"), nullable=true)
     * @Rest\QueryParam(name="organismType", requirements=@Assert\Choice("self", "partners"), default="self")
     * @Rest\QueryParam(name="type", requirements=@Assert\Type("string"), default="all")
     * @Rest\QueryParam(name="state", requirements=@Assert\Type("string"), default="all")
     * @Rest\QueryParam(name="billingState", requirements=@Assert\Type("string"), default="all")
     * @Rest\QueryParam(name="controlState", requirements=@Assert\Type("string"), default="all")
     * @Rest\QueryParam(name="certificationFolderState", requirements=@Assert\Type("string"), default="all")
     * @Rest\QueryParam(name="certifInfo", requirements=@Assert\Type("string"), nullable=true)
     * @Rest\QueryParam(name="sessionId", requirements=".+\/.+\/.+", nullable=true)
     * @Rest\QueryParam(name="trainingActionId", requirements=".+\/.+", nullable=true)
     * @Rest\QueryParam(name="trainingId", requirements=".+", nullable=true)
     * @Rest\QueryParam(name="messageState", requirements=@Assert\Type("string"), nullable=true)
     * @Rest\QueryParam(name="messageTemplate", requirements=@Assert\Type("string"), nullable=true)
     * @Rest\QueryParam(name="completionRate", requirements=@Assert\Choice("0", "25<", "25<>80", ">80", "100"), nullable=true)
     * @Rest\QueryParam(name="daysSinceLastUpdatedCompletionRate", requirements=@Assert\Type("numeric"), nullable=true)
     * @Rest\QueryParam(name="filterOnStateDate", requirements=@Assert\Choice({"lastUpdate", "updatedOn", "createdOn", "notProcessedDate", "validatedDate", "acceptedDate", "inTrainingDate", "terminatedDate", "serviceDoneDeclaredDate", "serviceDoneValidatedDate", "billedDate", "refusedByAttendeeDate", "refusedByOrganismDate", "canceledByAttendeeDate", "canceledByOrganismDate", "canceledByAttendeeNotRealizedDate", "rejectedWithoutTitulaireSuiteDate", "sessionStartDate", "sessionEndDate", "paymentScheduledDate"}), default="lastUpdate")
     * @Rest\QueryParam(name="period", requirements=@Assert\Choice({"nextYear", "previousYear", "currentYear", "rollingYear", "rollingYearFuture", "nextMonth", "previousMonth", "currentMonth", "rollingMonth", "rollingMonthFuture", "nextWeek", "previousWeek", "currentWeek", "rollingWeek", "rollingWeekFuture", "tomorrow", "today", "yesterday", "custom"}), nullable=true, description="Filtre les dossiers de formation selon la période choisie. Valeurs possibles : 'nextYear', 'previousYear', 'currentYear', 'rollingYear', 'rollingYearFuture', 'nextMonth', 'previousMonth', 'currentMonth', 'rollingMonth', 'rollingMonthFuture', 'nextWeek', 'previousWeek', 'currentWeek', 'rollingWeek', 'rollingWeekFuture', 'tomorrow', 'today', 'yesterday', 'custom'.")
     * @Rest\QueryParam(name="since", requirements=@Assert\AtLeastOneOf({@Assert\DateTime(format="Y-m-d\TH:i:sO"), @Assert\Date()}), nullable=true)
     * @Rest\QueryParam(name="until", requirements=@Assert\AtLeastOneOf({@Assert\DateTime(format="Y-m-d\TH:i:sO"), @Assert\Date()}), nullable=true)
     * @Rest\QueryParam(name="tags", requirements=@Assert\Type("string"), nullable=true)
     * @Rest\QueryParam(name="metadata", requirements=@Assert\Type("string"), nullable=true, description="Permet d'effectuer une une valeur d'un 'metadata' format : cle:valeur.")
     * @Rest\QueryParam(name="query", requirements=@Assert\Type("string"), nullable=true)
     * @Rest\QueryParam(name="columnIds", requirements=@Assert\Type("string"), nullable=false)
     * @Rest\QueryParam(name="sort", requirements=@Assert\Choice({"firstName", "lastName", "lastUpdate", "id"}), default="lastUpdate")
     * @Rest\QueryParam(name="limit", requirements="\d+", default="20")
     * @Rest\QueryParam(name="page", requirements="\d+", default="1", description="Numéro de page de la requête - par défaut la première.")
     * @Rest\QueryParam(name="order", requirements=@Assert\Choice({"asc", "desc"}), default="desc")
     * @Rest\View(StatusCode = 200)
     *
     * @param RegistrationFolderService $registrationFolderService
     * @param ProposalService $proposalService
     * @param OrganismService $organismService
     * @param SessionService $sessionService
     * @param TrainingActionService $trainingActionService
     * @param TrainingService $trainingService
     * @param CertificationService $certificationService
     * @param ParamFetcherInterface $paramFetcher
     * @return Response
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     * @throws WedofSubscriptionException
     */
    public function listByColumn(RegistrationFolderService $registrationFolderService, ProposalService $proposalService, OrganismService $organismService, SessionService $sessionService, TrainingActionService $trainingActionService, TrainingService $trainingService, CertificationService $certificationService, ParamFetcherInterface $paramFetcher): Response
    {
        /* @var $user User */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();
        $parameters = $paramFetcher->all(true);

        $columnIds = explode(',', $parameters['columnIds']);
        unset($parameters['columnIds']);

        $parameters = $this->validateListParameters($user, $organism, $parameters, $proposalService, $organismService, $sessionService, $trainingActionService, $trainingService, $certificationService);

        $subscription = $organism->getSubscription();
        $isAllowAnalytics = $subscription->isAllowAnalytics();
        $data = $registrationFolderService->listByColumn($organism, $parameters, $columnIds, $isAllowAnalytics);

        $context = new Context();
        if ($parameters['organismType'] === 'self') {
            $context->addGroup('owner');
        } else {
            $context->addGroup('Default');
        }
        $view = $this->view($data, 200);
        $view->setContext($context);
        return $this->handleView($view);
    }

    /**
     * @Rest\Get("/app/attendees/registrationFolders/{externalId}/attendeeLink")
     * @IsGranted(RegistrationFolderVoter::ATTENDEE_VIEW, subject="registrationFolder", message="not allowed")
     * @Rest\View(statusCode=200)
     *
     * @param RegistrationFolder $registrationFolder
     * @return string
     */
    public function getAttendeeLink(RegistrationFolder $registrationFolder): string
    {
        $subscription = $registrationFolder->getOrganism()->getSubscription();
        $attendeeLink = '';
        if ($subscription && $subscription->isAllowAnalytics()) {
            $attendeeLink = '/apprenant/formation/dossier/' . $registrationFolder->getExternalId();
        }
        return $attendeeLink;
    }

    //--------------
    // METHODES CRUD
    //--------------
    /**
     * @Rest\Get("/api/registrationFolders/{externalId}")
     * @Rest\Get("/app/attendees/registrationFolders/{externalId}")
     * @Security("is_granted('view', registrationFolder) or is_granted('attendeeView', registrationFolder)", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"registrationFolders", "default"})
     * @OA\Get (
     *     summary="Récupération d'un dossier.",
     *     description="Récupération d'un dossier par son ID. Via OAuth2, cet appel nécessite le scope 'registrationfolder:read'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations du dossier",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/RegistrationFolder")
     *     )
     * )
     * @OA\Parameter(
     *     name="externalId",
     *     in="path",
     *     description="ID du dossier",
     *     @OA\Schema(type="string")
     * )
     *
     * @param RegistrationFolder $registrationFolder
     * @return Response
     */
    public function show(RegistrationFolder $registrationFolder): Response
    {
        $user = $this->getUser();
        $context = new Context();

        if ($user instanceof Attendee) {
            $context->addGroup('attendee');
        } else if ($user instanceof User) {
            $organism = $user->getMainOrganism();
            if ($organism === $registrationFolder->getOrganism() && !$organism->getSubscription()->isRegistrationFoldersNumberLimitExceeded()) {
                $context->addGroup('owner');
            } else {
                $context->addGroup('Default');
            }
        } else {
            throw new LogicException("Classe du user inconnue.");
        }

        $view = $this->view($registrationFolder, 200);
        $view->setContext($context);
        return $this->handleView($view);
    }

    /**
     * @Rest\Get("/api/registrationFolders")
     * @Rest\Get("/funnel/api/registrationFolders", name="listRegistrationFoldersThroughFunnel")
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_SALES') or is_granted('ROLE_OAUTH2_REGISTRATIONFOLDER:READ')", message="not allowed")
     * @Rest\QueryParam(name="proposalCode", requirements=@Assert\Type("string"), nullable=true, description="Permet de n'obtenir que les dossiers associés à la proposition donnée.")
     * @Rest\QueryParam(name="siret", requirements=@Assert\Type("string"), nullable=true, description="Permet de n'obtenir que les dossiers appartenant à l'organisme de siret considéré - par défaut l'organisme de l'utilisateur courant.")
     * @Rest\QueryParam(name="organismType", requirements=@Assert\Choice("self", "partners"), default="self", description="Permet de n'obtenir soit ses propres dossiers (self) soit les dossiers éligibles de ses partenaires (partner).")
     * @Rest\QueryParam(name="type", requirements=@Assert\Type("string"), default="all", description="Permet de n'obtenir que les dossiers dans le type considéré - par défaut tous les types sont retournés. Valeurs possibles : 'all', 'cpf', 'individual', 'poleEmploi'. Il est possible de demander plusieurs types en séparant chaque type par une virgule, ex : 'cpf,individual'.")
     * @Rest\QueryParam(name="state", requirements=@Assert\Type("string"), default="all", description="Permet de n'obtenir que les dossiers dans l'état considéré - par défaut tous les états sont retournés. Valeurs possibles : 'all', 'notProcessed', 'validated', 'waitingAcceptation', 'accepted', 'inTraining', 'terminated', 'serviceDoneDeclared', 'serviceDoneValidated', 'canceledByAttendee', 'canceledByAttendeeNotRealized', 'canceledByOrganism', 'refusedByAttendee', 'refusedByOrganism', 'rejectedWithoutTitulaireSuite', 'rejected', 'rejectedWithoutCdcSuite'. Il est possible de demander plusieurs états en séparant chaque état par une virgule, ex : 'accepted,inTraining'.")
     * @Rest\QueryParam(name="billingState", requirements=@Assert\Type("string"), default="all", description="Permet de n'obtenir que les dossiers dans l'état de facturation considéré - par défaut tous les états sont retournés. Valeurs possibles : 'all', 'notBillable', 'depositWait', 'depositPaid', 'toBill', 'billed', 'paid'. Il est possible de demander plusieurs états en séparant chaque état par une virgule, ex : 'toBill,billed'.")
     * @Rest\QueryParam(name="controlState", requirements=@Assert\Type("string"), default="all", description="Permet de n'obtenir que les dossiers dans l'état de contrôle considéré - par défaut tous les états sont retournés. Valeurs possibles : 'all', 'inControl', 'released', 'notInControl'. Il est possible de demander plusieurs états en séparant chaque état par une virgule, ex : 'inControl,released'.")
     * @Rest\QueryParam(name="certificationFolderState", requirements=@Assert\Type("string"), default="all", description="Permet de n'obtenir que les dossiers dans l'état d'obtention de la certification considéré - par défaut tous les états sont retournés. Valeurs possibles : 'all', 'toRegister', 'refused', 'registered', 'toTake', 'toControl', 'toRetake', 'failed', 'aborted', 'success'. Il est possible de demander plusieurs états en séparant chaque état par une virgule, ex : 'toRegister,toTake'.")
     * @Rest\QueryParam(name="certifInfo", requirements=@Assert\Type("string"), nullable=true, description="Permet de n'obtenir que les dossiers liés à la certification considérée - par défaut tous les dossiers de toutes les certifications sont retournés.")
     * @Rest\QueryParam(name="sessionId", requirements=".+\/.+\/.+", nullable=true, description="Permet de n'obtenir que les dossiers liés à la session considérée - par défaut tous les dossiers de toutes les sessions sont retournés.")
     * @Rest\QueryParam(name="trainingActionId", requirements=".+\/.+", nullable=true, description="Permet de n'obtenir que les dossiers liés à l'action de formation considérée - par défaut tous les dossiers de toutes les actions de formations sont retournés.")
     * @Rest\QueryParam(name="trainingId", requirements=".+", nullable=true, description="Permet de n'obtenir que les dossiers liés à la formation considérée - par défaut tous les dossiers de toutes les formations sont retournés.")
     * @Rest\QueryParam(name="messageState", requirements=@Assert\Type("string"), nullable=true, description="Permet de n'obtenir que les dossiers liés à l'état d'envoi d'un message considéré - par défaut tous les dossiers sont retournés. Valeurs possibles : 'sent', 'notSent', 'notSentUnauthorized', 'notSentEnforcedConditions', 'failed', 'scheduled'.")
     * @Rest\QueryParam(name="messageTemplate", requirements=@Assert\Type("string"), nullable=true, description="Permet de n'obtenir que les dossiers pour lequels un message issue du modèle considéré a été créé - par défaut aucun filtre.")
     * @Rest\QueryParam(name="completionRate", requirements=@Assert\Choice("0", "25<", "25<>80", ">80", "100"), nullable=true, description="Permet de n'obtenir que les dossiers dont le taux d'assiduité est égale à 0, inférieur à 25%, compris entre 25% et 80%, supérieurs à 80%, égal à 100% -par défaut tous les dossiers sont retournés. Valeurs possibles :'0','25<', '25<>80', '>80', '100'.")
     * @Rest\QueryParam(name="daysSinceLastUpdatedCompletionRate", requirements=@Assert\Type("numeric"), nullable=true, description="Permet de n'obtenir que les dossiers pour lesquels le taux d'avancement n'a pas été mis à jour depuis plus de X jours, X étant le nombre de jours. -par défaut tous les dossiers sont retournés.")
     * @Rest\QueryParam(name="format", requirements=@Assert\Type("string"), default="json", description="Permet d'obtenir une liste des dossiers de formation au format json ou csv. Valeurs possibles : 'json', 'csv'")
     * @Rest\QueryParam(name="csvColumns", requirements=@Assert\Type("string"), nullable=true, description="Permet de choisir les colonnes souhaitées pour l'export des dossiers de formation au format csv. Valeurs possibles : 'NUMERO_DOSSIER', 'ESPACE_APPRENANT', 'NOM', 'PRENOM', 'EMAIL', 'TELEPHONE', 'MONTANT_FORMATION',  'INTITULE_FORMATION', 'DUREE_FORMATION', 'CODE_CERTIF', 'INTITULE_CERTIF', 'STATUT_DOSSIER', 'DATE_DEBUT_SESSION', 'DATE_FIN_SESSION', 'NUMERO_FORMATION', 'NUMERO_ACTION', 'NUMERO_SESSION'. ")
     * @Rest\QueryParam(name="filterOnStateDate", requirements=@Assert\Choice({"lastUpdate", "updatedOn", "createdOn", "notProcessedDate", "validatedDate", "acceptedDate", "inTrainingDate", "terminatedDate", "serviceDoneDeclaredDate", "serviceDoneValidatedDate", "billedDate", "refusedByAttendeeDate", "refusedByOrganismDate", "canceledByAttendeeDate", "canceledByOrganismDate", "canceledByAttendeeNotRealizedDate", "rejectedWithoutTitulaireSuiteDate", "sessionStartDate", "sessionEndDate", "paymentScheduledDate"}), default="lastUpdate")
     * @Rest\QueryParam(name="period", requirements=@Assert\Choice({"nextYear", "previousYear", "currentYear", "rollingYear", "rollingYearFuture", "nextMonth", "previousMonth", "currentMonth", "rollingMonth", "rollingMonthFuture", "nextWeek", "previousWeek", "currentWeek", "rollingWeek", "rollingWeekFuture", "tomorrow", "today", "yesterday", "custom"}), nullable=true, description="Filtre les dossiers de formation selon la période choisie. Valeurs possibles : 'nextYear', 'previousYear', 'currentYear', 'rollingYear', 'rollingYearFuture', 'nextMonth', 'previousMonth', 'currentMonth', 'rollingMonth', 'rollingMonthFuture', 'nextWeek', 'previousWeek', 'currentWeek', 'rollingWeek', 'rollingWeekFuture', 'tomorrow', 'today', 'yesterday', 'custom'.")
     * @Rest\QueryParam(name="since", requirements=@Assert\AtLeastOneOf({@Assert\DateTime(format="Y-m-d\TH:i:sO"), @Assert\Date()}), nullable=true, description="Filtre les dossiers dont le 'filterOnStateDate' démarre à la date choisie. Date au format ISO-8601 2021-08-05T09:53:54Z ou Date au format AAAA-MM-JJ, 2021-08-05")
     * @Rest\QueryParam(name="until", requirements=@Assert\AtLeastOneOf({@Assert\DateTime(format="Y-m-d\TH:i:sO"), @Assert\Date()}), nullable=true, description="Filtre les dossiers dont le 'filterOnStateDate' termine à la date choisie. Date au format ISO-8601 2021-08-05T09:53:54Z ou Date au format AAAA-MM-JJ, 2021-08-05")
     * @Rest\QueryParam(name="tags", requirements=@Assert\Type("string"), nullable=true, description="Permet d'effectuer une recherche libre sur le 'tags'.")
     * @Rest\QueryParam(name="metadata", requirements=@Assert\Type("string"), nullable=true, description="Permet d'effectuer une une valeur d'un 'metadata' format : cle:valeur.")
     * @Rest\QueryParam(name="query", requirements=@Assert\Type("string"), nullable=true, description="Permet d'effectuer une recherche libre sur les champs 'nom de l'apprenant', 'prénom de l'apprenant', 'email de l'apprenant', 'id du dossier', 'tags', 'notes' , 'phoneNumber'.")
     * @Rest\QueryParam(name="sort", requirements=@Assert\Choice({"firstName", "lastName", "lastUpdate", "id"}), default="lastUpdate", description="Tri les résultats sur un critère. Valeurs possibles: 'firstName' (prénom de l'apprenant), 'lastName' (nom de l'apprenant), 'lastUpdate' (date de dernière mise à jour du dossier), 'id' (id de base de donnée) - par défaut 'lastUpdate'.")
     * @Rest\QueryParam(name="columnId", requirements=@Assert\Type("string"), nullable=true)
     * @Rest\QueryParam(name="limit", requirements="\d+", default="100", description="Nombre d'éléments retourné par requête - par défaut 100.")
     * @Rest\QueryParam(name="page", requirements="\d+", default="1", description="Numéro de page de la requête - par défaut la première.")
     * @Rest\QueryParam(name="order", requirements=@Assert\Choice({"asc", "desc"}), default="desc", description="Tri les résultats par ordre ascendant ou descendant - par défaut descendant.")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"registrationFolders", "default"})
     * @OA\Get (
     *     summary="Liste tous les dossiers  pour l'organisme de l'utilisateur courant.",
     *     description="Récupère l'ensemble des dossiers de l'organisme de l'utilisateur connecté. NOTA : les paramètres sont cumulatifs et deux paramètres incompatibles renverront un résultat vide. Via OAuth2, cet appel nécessite le scope 'registrationfolder:read'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un tableau de dossiers au format JSON ou CSV selon le paramètre 'format' ",
     *     @OA\JsonContent(
     *          type="array",
     *          @OA\Items(ref="#/components/schemas/RegistrationFolder")
     *     )
     * )
     * @OA\Parameter (name="order", in="query", @OA\Schema (ref="#/components/schemas/Order"))
     *
     * @param RegistrationFolderService $registrationFolderService
     * @param ProposalService $proposalService
     * @param OrganismService $organismService
     * @param SessionService $sessionService
     * @param TrainingActionService $trainingActionService
     * @param TrainingService $trainingService
     * @param CertificationService $certificationService
     * @param PaginatorInterface $paginator
     * @param ParamFetcherInterface $paramFetcher
     * @param Request $request
     * @param EntityManagerInterface $entityManager
     * @return Response
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function list(RegistrationFolderService $registrationFolderService, ProposalService $proposalService, OrganismService $organismService, SessionService $sessionService, TrainingActionService $trainingActionService, TrainingService $trainingService, CertificationService $certificationService, PaginatorInterface $paginator, ParamFetcherInterface $paramFetcher, Request $request, EntityManagerInterface $entityManager): Response
    {
        /* @var $user User */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();
        $parameters = $paramFetcher->all(true);
        if ($this->isGranted('ROLE_SALES') && empty($parameters['proposalCode'])) {
            throw new WedofAccessDeniedHttpException("Vous n'êtes pas autorisé à lister les dossiers de formation");
        }
        $parameters = $this->validateListParameters($user, $organism, $parameters, $proposalService, $organismService, $sessionService, $trainingActionService, $trainingService, $certificationService);
        if ($parameters['format'] === 'csv' || str_contains($request->headers->get('Accept'), 'csv')) {
            set_time_limit(300);
            if ($parameters['organismType'] === 'self') {
                $availableColumns = ['NUMERO_DOSSIER', 'ESPACE_APPRENANT', 'NOM', 'PRENOM', 'EMAIL', 'TELEPHONE', 'MONTANT_FORMATION', 'INTITULE_FORMATION', 'DUREE_FORMATION', 'CODE_CERTIF', 'INTITULE_CERTIF', 'STATUT_DOSSIER', 'DATE_DEBUT_SESSION', 'DATE_FIN_SESSION', 'NUMERO_FORMATION', 'NUMERO_ACTION', 'NUMERO_SESSION'];
            } else {
                $availableColumns = ['NUMERO_DOSSIER', 'NOM', 'PRENOM', 'EMAIL', 'TELEPHONE', 'INTITULE_FORMATION', 'CODE_CERTIF', 'INTITULE_CERTIF', 'STATUT_DOSSIER', 'DATE_DEBUT_SESSION', 'DATE_FIN_SESSION', 'NUMERO_FORMATION', 'NUMERO_ACTION', 'NUMERO_SESSION'];
            }
            $page = 1;
            $limit = 100;
            $tempFile = null;
            $entityManager->getConnection()->getConfiguration()->setSQLLogger(); // for perf in dev, maybe in prod ?
            $data = $paginator->paginate($registrationFolderService->listReturnQueryBuilder($organism, $parameters), $page, $limit);

            $filteredData = [];
            foreach ($data->getItems() as $registrationFolder) {
                if ($registrationFolder->isAllowActions()) {
                    $filteredData[] = $registrationFolder;
                }
            }
            $tempFile = Tools::convertDataToCSVFile($filteredData, $availableColumns, $parameters['csvColumns'] ?? null, $tempFile);
            $page++;
            $nbIterations = intdiv($data->getTotalItemCount(), $limit) + 1;
            $entityManager->clear();

            while ($page <= $nbIterations) {
                $data = $paginator->paginate($registrationFolderService->listReturnQueryBuilder($organism, $parameters), $page, $limit);
                $filteredData = [];
                foreach ($data->getItems() as $registrationFolder) {
                    if ($registrationFolder->isAllowActions()) {
                        $filteredData[] = $registrationFolder;
                    }
                }
                $tempFile = Tools::convertDataToCSVFile($filteredData, $availableColumns, $parameters['csvColumns'] ?? null, $tempFile);
                $page++;
                $entityManager->clear();
            }

            return Tools::getCsvResponse($tempFile, 'registration_folders');
        } else {
            $data = $paginator->paginate($registrationFolderService->listReturnQueryBuilder($organism, $parameters), intval($parameters['page']), intval($parameters['limit']));

            $context = new Context();
            if ($parameters['organismType'] === 'self') {
                $context->addGroup('owner');
            } else {
                $context->addGroup('Default');
            }
            $view = $this->view($data->getItems(), 200);
            $view->setContext($context);
            $view->setHeader("x-total-count", $data->getTotalItemCount());
            $view->setHeader("x-current-page", $data->getCurrentPageNumber());
            $view->setHeader("x-item-per-page", $data->getItemNumberPerPage());
            return $this->handleView($view);
        }
    }

    /**
     * @Rest\Post ("/api/registrationFolders")
     * @Security("is_granted('ROLE_OAUTH2_REGISTRATIONFOLDER:WRITE') or is_granted('ROLE_USER')", message="not allowed")
     * @Rest\View(StatusCode = 201, serializerGroups={"owner"})
     *
     * @ApiDoc\Areas({"registrationFolders", "default"})
     * @OA\Post (
     *     summary="Permet de créer un nouveau dossier hors CPF",
     *     description="Permet de créer un nouveau dossier. Via OAuth2, cet appel nécessite le scope 'registrationfolder:write'"
     * )
     * @OA\Response(
     *     response=201,
     *     description="Un json contenant les informations du nouveau dossier créé",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/RegistrationFolder")
     *     )
     * )
     *
     * @OA\RequestBody(
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/RegistrationFolderCreateBody")
     *     )
     * )
     *
     * @param AttendeeService $attendeeService
     * @param SessionService $sessionService
     * @param Request $request
     * @param RegistrationFolderService $registrationFolderService
     * @param OrganismService $organismService
     * @return RegistrationFolder|View
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     * @throws \Doctrine\ORM\ORMException
     */
    public function create(AttendeeService $attendeeService, SessionService $sessionService, Request $request, RegistrationFolderService $registrationFolderService, OrganismService $organismService)
    {
        /* @var $user User */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();
        if (!$organism->getSubscription()->isAllowRegistrationFolderManualCreate()) {
            throw new WedofSubscriptionException("Erreur, vous avez dépassé la limite du nombre de dossiers de formation permis par votre abonnement.");
        }
        $body = json_decode($request->getContent(), true);
        if ($body['totalTTC'] !== null) {
            $body['totalTTC'] = floatval($body['totalTTC']);
        }
        if ($body['type'] === 'poleEmploi') {
            if (!isset($body['poleEmploiId']) || !isset($body['poleEmploiRegionCode']) || !isset($body['poleEmploiDevis'])) {
                throw new WedofBadRequestHttpException("Erreur, l'id ainsi que le numéro de devis et le code région sont requis pour la création d'un dossier Pôle Emploi.");
            }
            if (!PoleEmploiRegionCode::isValid($body['poleEmploiRegionCode'])) {
                throw new WedofBadRequestHttpException("Erreur, le code region n'est pas valide.");
            }
        }
        $violations = $this->validateCreateBody($body);

        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }

        $attendee = $attendeeService->getById($body['attendeeId']);
        if (!$attendee) {
            throw new WedofNotFoundHttpException("L'apprenant associé à l'id " . $body['attendeeId'] . " n'a pas été trouvé.");
        }

        if ($body['type'] === 'poleEmploi') {
            $attendeeService->createOrUpdate(['poleEmploiId' => $body['poleEmploiId'], 'poleEmploiRegionCode' => $body['poleEmploiRegionCode'], 'email' => $attendee->getEmail()], $attendee);
        }

        $session = $sessionService->getById($body['sessionId']);
        if (!$session) {
            throw new WedofNotFoundHttpException("La session de formation associée à l'id " . $body['sessionId'] . " n'a pas été trouvée.");
        } else if (!$this->isGranted(SessionVoter::VIEW, $session)) {
            throw new WedofAccessDeniedHttpException("Vous n'êtes pas autorisé à avoir accès au contenu de la session associée à l'id " . $body['sessionId']);
        } else if ($session->getState() !== SessionStates::PUBLISHED()->getValue()) {
            throw new WedofBadRequestHttpException("Erreur, la session associée à l'id " . $body['sessionId'] . " n'est pas publiée.");
        }

        $inPartnershipWith = null;
        if (!empty($body['inPartnershipWith'])) {
            $inPartnershipWith = $organismService->getOrganism(['siret' => $body['inPartnershipWith']]);
            if (!$inPartnershipWith) {
                throw new WedofNotFoundHttpException("Le partenaire associé au siret " . $body['inPartnershipWith'] . " n'a pas été trouvé.");
            }
        }
        $type = new DataProviders($body['type']);
        return $registrationFolderService->createOrUpdate($body, $type, null, $session, $organism, $attendee, $inPartnershipWith);
    }

    /**
     * @Rest\Put("/api/registrationFolders/{externalId}")
     * @IsGranted(RegistrationFolderVoter::EDIT, subject="registrationFolder", message="not allowed")
     * @Rest\View(StatusCode = 200, serializerGroups={"owner"})
     *
     * @ApiDoc\Areas({"registrationFolders", "default"})
     * @OA\Put (
     *     summary="Met à jour le contenu du dossier.",
     *     description="Permet de mettre à jour le contenu du dossier, descriptions, tarifs, dates de session... d'un dossier dans l'état 'À traiter'. Via OAuth2, cet appel nécessite le scope 'registrationfolder:write'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations du dossier",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/RegistrationFolder")
     *     )
     * )
     * @OA\Parameter(
     *     name="externalId",
     *     in="path",
     *     description="ID du dossier",
     *     @OA\Schema(type="string")
     * )
     * @OA\RequestBody(
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/RegistrationFolderUpdateBody")
     *     )
     * )
     *
     * @param RegistrationFolder $registrationFolder
     * @param Request $request
     * @param RegistrationFolderService $registrationFolderService
     * @param OrganismService $organismService
     * @return RegistrationFolder|View
     * @throws Throwable
     */
    public function update(RegistrationFolder $registrationFolder, Request $request, RegistrationFolderService $registrationFolderService, OrganismService $organismService)
    {
        $body = json_decode($request->getContent(), true);
        $sessionStartDate = null;
        $sessionEndDate = null;

        if (empty($body)) {
            throw new WedofBadRequestHttpException("Erreur, vérifiez le body envoyé.");
        }
        // Transformations nécessaires à la validation du body
        if (isset($body['priceChange'])) {
            if (isset($body['priceChange']['percent'], $body['priceChange']['price']) || isset($body['priceChange']['percent'], $body['priceChange']['amount']) || isset($body['priceChange']['amount'], $body['priceChange']['price'])) {
                throw new WedofBadRequestHttpException('Erreur, une seule propriété (percent, price, amount) doit être envoyée afin de changer le prix.');
            }
            if (isset($body['priceChange']['percent']) && is_numeric($body['priceChange']['percent'])) {
                $body['priceChange']['percent'] = (float)($body['priceChange']['percent']);
            }
            if (isset($body['priceChange']['price']) && is_numeric($body['priceChange']['price'])) {
                $body['priceChange']['price'] = (float)($body['priceChange']['price']);
            }
        }
        if (isset($body['trainingActionInfo'])) {
            if (!empty($body['trainingActionInfo']['additionalFees']) && is_numeric($body['trainingActionInfo']['additionalFees'])) {
                $body['trainingActionInfo']['additionalFees'] = (float)($body['trainingActionInfo']['additionalFees']);
            }
            if (array_key_exists('sessionStartDate', $body['trainingActionInfo'])) {
                if (strtotime($body['trainingActionInfo']['sessionStartDate'])) {
                    $body['trainingActionInfo']['sessionStartDate'] = (new DateTime($body['trainingActionInfo']['sessionStartDate']))->setTimezone(new DateTimeZone('Europe/Paris')); //Need to be in Paris Timezone for EDOF update
                    $sessionStartDate = $body['trainingActionInfo']['sessionStartDate'];
                } else {
                    unset($body['trainingActionInfo']['sessionStartDate']);
                }
            }
            if (array_key_exists('sessionEndDate', $body['trainingActionInfo'])) {
                if (strtotime($body['trainingActionInfo']['sessionEndDate'])) {
                    $body['trainingActionInfo']['sessionEndDate'] = (new DateTime($body['trainingActionInfo']['sessionEndDate']))->setTimezone(new DateTimeZone('Europe/Paris')); //Need to be in Paris Timezone for EDOF update
                    $sessionEndDate = $body['trainingActionInfo']['sessionEndDate'];
                } else {
                    unset($body['trainingActionInfo']['sessionEndDate']);
                }
            }
            if ($sessionStartDate || $sessionEndDate) {
                $trainingActionInfo = $registrationFolder->getRawData()['trainingActionInfo'];
                $sessionStartDate = $sessionStartDate ?? (DateTime::createFromFormat('d/m/Y', $trainingActionInfo['sessionStartDate']) ?? new DateTime($trainingActionInfo['sessionStartDate']));
                $sessionEndDate = $sessionEndDate ?? (DateTime::createFromFormat('d/m/Y', $trainingActionInfo['sessionEndDate']) ?? new DateTime($trainingActionInfo['sessionEndDate']));
                if (!$sessionStartDate) {
                    throw new WedofBadRequestHttpException("Erreur, la date de début de session (sessionStartDate) doit être renseignée");
                }
                if (!$sessionEndDate) {
                    throw new WedofBadRequestHttpException("Erreur, la date de fin de session (sessionEndDate) doit être renseignée");
                }
                if ($sessionStartDate > $sessionEndDate) {
                    throw new WedofBadRequestHttpException("Erreur, la date de début de session (sessionStartDate) ne peut pas être ultérieure à la date de fin de session (sessionEndDate)");
                }
            }
        }

        if (!empty($body['controlStateDate']) && strtotime($body['controlStateDate'])) {
            $body['controlStateDate'] = (new DateTime($body['controlStateDate']))->setTimezone(new DateTimeZone('Europe/Paris'));
        }

        if (!empty($body['completionRateDate']) && strtotime($body['completionRateDate'])) {
            $body['completionRateDate'] = (new DateTime($body['completionRateDate']))->setTimezone(new DateTimeZone('Europe/Paris'));
        }

        if (array_key_exists('metadata', $body)) {
            if (is_string($body['metadata'])) {
                $body['metadata'] = json_decode($body['metadata'], true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    throw new WedofBadRequestHttpException("Erreur, le champ metadata doit être un JSON valide");
                }
            }

            if (!is_array($body['metadata']) && $body['metadata'] !== null) {
                throw new WedofBadRequestHttpException("Erreur, le champ metadata doit être un JSON valide");
            }
        }

        $violations = $this->validateUpdateBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }

        if ($registrationFolder->getType() === DataProviders::CPF()->getValue()) {
            if (isset($body['trainingActionInfo']['additionalFees'])) {
                if ($body['trainingActionInfo']['additionalFees'] > 0 && !isset($body['trainingActionInfo']['additionalFeesDetails'])) {
                    throw new WedofBadRequestHttpException("Erreur, des informations sont requises si des frais additionnels s'appliquent.");
                }
            }
            if (!isset($body['trainingActionInfo']['additionalFees']) && isset($body['trainingActionInfo']['additionalFeesDetails'])) {
                throw new WedofBadRequestHttpException("Erreur, la valeur pour les frais supplémentaires doit être renseignée si des détails concernant les frais supplémentaires ont été ajoutés.");
            }
            if (isset($body['trainingActionInfo']['teachingModalities'])) {
                if (($body['trainingActionInfo']['teachingModalities'] == "0" || $body['trainingActionInfo']['teachingModalities'] == "1") && !isset($body['trainingActionInfo']['address'])) {
                    throw new WedofBadRequestHttpException("Erreur, l'adresse est requise pour les formations en présentiel.");
                }
            }
        }

        //burk in /api
        if (!empty($body['addedTags']) || !empty($body['removedTags'])) {
            $tags = $body['tags'] ?? Tools::tagsToArray($registrationFolder);
            if (!empty($body['addedTags'])) {
                $tags = array_merge($tags, array_map('strtolower', $body['addedTags']));
            }
            if (!empty($body['removedTags'])) {
                $tags = array_diff($tags, array_map('strtolower', $body['removedTags']));
            }
            $body['tags'] = $tags;
        }

        if (isset($body['inPartnershipWith'])) {
            $body['inPartnershipWith'] = $organismService->getOrganism(['siret' => $body['inPartnershipWith']]);
            if (!$body['inPartnershipWith']) {
                throw new WedofNotFoundHttpException("Le partenaire associé au siret " . $body['inPartnershipWith'] . " n'a pas été trouvé.");
            }
        }
        $filterBody = $this->clean($body);
        return $registrationFolderService->update($registrationFolder, $filterBody);
    }

    /**
     * @Rest\Delete("/api/registrationFolders/{externalId}")
     * @IsGranted(RegistrationFolderVoter::EDIT, subject="registrationFolder", message="not allowed")
     * @Rest\View(StatusCode = 501)
     *
     * @OA\Delete (
     *     summary="Méthode non supportée",
     *     description="Méthode non supportée"
     * )
     * @OA\Response(
     *     response=501,
     *     description="Méthode non supportée"
     * )
     * @OA\Parameter(
     *     name="externalId",
     *     in="path",
     *     description="ID du dossier",
     *     @OA\Schema(type="string")
     * )
     *
     * @param RegistrationFolder $registrationFolder
     * @return void
     */
    public function delete(RegistrationFolder $registrationFolder)
    {
        throw new WedofBadRequestHttpException("Méthode non supportée - Le dossier de formation " . $registrationFolder->getExternalId() . " ne peut être supprimé.", null, 501);
    }

    //---------------
    // METHODES STATE
    //---------------

    /**
     * @Rest\Post("/api/registrationFolders/{externalId}/validate")
     * @IsGranted(RegistrationFolderVoter::EDIT, subject="registrationFolder", message="not allowed")
     * @Rest\View(StatusCode = 200, serializerGroups={"owner"})
     *
     * @ApiDoc\Areas({"registrationFolders", "default"})
     * @OA\Post (
     *     summary="Passe le dossier dans l'état 'validé'.",
     *     description="Passe le dossier dans l'état 'validé' s'il est dans l'état 'À traiter' ou 'validé (en cours d'instruction Pôle emploi)'. Via OAuth2, cet appel nécessite le scope 'registrationfolder:write'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations du dossier validé",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/RegistrationFolder")
     *     )
     * )
     * @OA\Parameter(
     *     name="externalId",
     *     in="path",
     *     description="ID du dossier",
     *     @OA\Schema(type="string")
     * )
     * @OA\RequestBody(
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(
     *             @OA\Property(property="indicativeDuration", type="integer", description="Obligatoire dans le cas d'un dossier avec financement Pôle Emploi et si la durée de la formation n'est pas précisée dans l'action de formation : Durée totale de la formation en heures.", example="70"),
     *             @OA\Property(property="weeklyDuration", type="integer", description="Optionnel : Intensité hebdomadaire de la formation, en heures par semaine.", example="35")
     *         )
     *     )
     * )
     *
     * @param RegistrationFolder $registrationFolder
     * @param RegistrationFolderService $registrationFolderService
     * @param Request $request
     * @return RegistrationFolder|View
     * @throws Throwable
     */
    public function validate(RegistrationFolder $registrationFolder, RegistrationFolderService $registrationFolderService, Request $request)
    {
        $body = json_decode($request->getContent(), true);

        if (!empty($body)) {
            $violations = $this->validateValidateBody($body);
            if (count($violations)) {
                return $this->view($violations, Response::HTTP_BAD_REQUEST);
            }
        }
        return $registrationFolderService->validate($registrationFolder, $body ?? []);
    }

    /**
     * @Rest\Post("/api/registrationFolders/{externalId}/reject")
     * @IsGranted(RegistrationFolderVoter::EDIT, subject="registrationFolder", message="not allowed")
     * @Rest\View(StatusCode = 200, serializerGroups={"owner"})
     *
     * @ApiDoc\Areas({"registrationFolders", "default"})
     * @OA\Post (
     *     summary="Passe le dossier dans l'état 'annulé sans suite'.",
     *     description="Passe le dossier dans l'état 'annulé sans suite' s'il est dans l'état 'validé'. Via OAuth2, cet appel nécessite le scope 'registrationfolder:write'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations du dossier validé",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/RegistrationFolder")
     *     )
     * )
     * @OA\Parameter(
     *     name="externalId",
     *     in="path",
     *     description="ID du dossier",
     *     @OA\Schema(type="string")
     * )
     *
     * @param RegistrationFolder $registrationFolder
     * @param RegistrationFolderService $registrationFolderService
     * @return RegistrationFolder
     * @throws ServerException
     * @throws Throwable
     */
    public function reject(RegistrationFolder $registrationFolder, RegistrationFolderService $registrationFolderService): RegistrationFolder
    {
        return $registrationFolderService->reject($registrationFolder);
    }

    /**
     * @Rest\Post("/api/registrationFolders/{externalId}/attendeeRefuse")
     * @IsGranted(RegistrationFolderVoter::EDIT, subject="registrationFolder", message="not allowed")
     * @Rest\View(StatusCode = 200, serializerGroups={"owner"})
     *
     * @ApiDoc\Areas({"registrationFolders", "default"})
     * @OA\Post (
     *     summary="Passe le dossier dans l'état 'refus titulaire'.",
     *     description="Passe le dossier dans l'état 'refus titulaire' s'il est dans l'état 'validé'. Via OAuth2, cet appel nécessite le scope 'registrationfolder:write'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations du dossier validé",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/RegistrationFolder")
     *     )
     * )
     * @OA\Parameter(
     *     name="externalId",
     *     in="path",
     *     description="ID du dossier",
     *     @OA\Schema(type="string")
     * )
     * @param RegistrationFolder $registrationFolder
     * @param RegistrationFolderService $registrationFolderService
     * @return RegistrationFolder
     * @throws ServerException
     * @throws Throwable
     */
    public function attendeeRefuse(
        RegistrationFolder        $registrationFolder,
        RegistrationFolderService $registrationFolderService
    ): RegistrationFolder
    {
        return $registrationFolderService->attendeeRefuse($registrationFolder);
    }

    /**
     * @Rest\Post("/api/registrationFolders/{externalId}/wait")
     * @IsGranted(RegistrationFolderVoter::EDIT, subject="registrationFolder", message="not allowed")
     * @Rest\View(StatusCode = 200, serializerGroups={"owner"})
     *
     * @ApiDoc\Areas({"registrationFolders", "default"})
     * @OA\Post (
     *     summary="Passe le dossier dans l'état 'validé (en cours d'instruction Pôle emploi)'.",
     *     description="Passe le dossier dans l'état 'validé (en cours d'instruction Pôle emploi)' s'il est dans l'état 'validé'. Via OAuth2, cet appel nécessite le scope 'registrationfolder:write'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations du dossier validé",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/RegistrationFolder")
     *     )
     * )
     * @OA\Parameter(
     *     name="externalId",
     *     in="path",
     *     description="ID du dossier",
     *     @OA\Schema(type="string")
     * )
     *
     * @param RegistrationFolder $registrationFolder
     * @param RegistrationFolderService $registrationFolderService
     * @return RegistrationFolder
     * @throws ServerException
     * @throws Throwable
     */
    public function wait(
        RegistrationFolder        $registrationFolder,
        RegistrationFolderService $registrationFolderService
    ): RegistrationFolder
    {
        return $registrationFolderService->wait($registrationFolder);
    }

    /**
     * @Rest\Post("/api/registrationFolders/{externalId}/accept")
     * @IsGranted(RegistrationFolderVoter::EDIT, subject="registrationFolder", message="not allowed")
     * @Rest\View(StatusCode = 200, serializerGroups={"owner"})
     *
     * @ApiDoc\Areas({"registrationFolders", "default"})
     * @OA\Post (
     *     summary="Passe le dossier dans l'état 'accepté'.",
     *     description="Passe le dossier dans l'état 'accepté' s'il est dans l'état 'validé' ou 'validé (en cours d'instruction Pôle emploi)'. Via OAuth2, cet appel nécessite le scope 'registrationfolder:write'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations du dossier accepté",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/RegistrationFolder")
     *     )
     * )
     * @OA\Parameter(
     *     name="externalId",
     *     in="path",
     *     description="ID du dossier",
     *     @OA\Schema(type="string")
     * )
     *
     * @param RegistrationFolder $registrationFolder
     * @param RegistrationFolderService $registrationFolderService
     * @return RegistrationFolder
     * @throws Throwable
     */
    public function accept(RegistrationFolder $registrationFolder, RegistrationFolderService $registrationFolderService): RegistrationFolder
    {
        return $registrationFolderService->accept($registrationFolder);
    }

    /**
     * @Rest\Post("/api/registrationFolders/{externalId}/inTraining")
     * @IsGranted(RegistrationFolderVoter::EDIT, subject="registrationFolder", message="not allowed")
     * @Rest\View(StatusCode = 200, serializerGroups={"owner"})
     *
     * @ApiDoc\Areas({"registrationFolders", "default"})
     * @OA\Post (
     *     summary="Passe le dossier dans l'état 'en formation'.",
     *     description="Passe le dossier dans l'état 'en formation' s'il est dans l'état 'accepté'. Via OAuth2, cet appel nécessite le scope 'registrationfolder:write'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations du dossier en formation.",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/RegistrationFolder")
     *     )
     * )
     * @OA\Parameter(
     *     name="externalId",
     *     in="path",
     *     description="ID du dossier",
     *     @OA\Schema(type="string")
     * )
     *
     * @OA\RequestBody(
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(
     *             @OA\Property(property="date", type="string", description="Date du passage en formation au format YYYY-MM-DD.", example="2010-01-25")
     *         )
     *     )
     * )
     *
     * @param RegistrationFolder $registrationFolder
     * @param RegistrationFolderService $registrationFolderService
     * @param Request $request
     * @return RegistrationFolder|View
     * @throws Throwable
     */
    public function inTraining(RegistrationFolder $registrationFolder, RegistrationFolderService $registrationFolderService, Request $request)
    {
        $body = json_decode($request->getContent(), true);
        if (!empty($body)) {
            if (isset($body['date'])) {
                if (strtotime($body['date'])) {
                    $body['date'] = (new DateTime($body['date']))->setTimezone(new DateTimeZone('Europe/Paris'));
                }
            }
            $violations = $this->validateInTrainingBody($body);
            if (count($violations)) {
                return $this->view($violations, Response::HTTP_BAD_REQUEST);
            }
        }
        return $registrationFolderService->inTraining($registrationFolder, $body['date'] ?? null);
    }

    /**
     * @Rest\Post("/api/registrationFolders/inTrainingAsync")
     * @Rest\View(StatusCode = 200, serializerGroups={"owner"})
     *
     * @ApiDoc\Areas({"registrationFolders", "default"})
     * @OA\Post (
     *     summary="Passer un Lot de dossiers à l'état 'en formation'.",
     *     description="Procéder au changement de plusieurs dossiers vers l'état 'en formation' s'ils sont dans l'état 'validé'. Via OAuth2, cet appel nécessite le scope 'registrationfolder:write'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations des dossiers qui sont programmés pour passer à l'état 'en formation' et ceux qui ne le seront pas",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/InTrainingRegistrationFoldersAsyncResult")
     *     )
     * )
     * @OA\RequestBody(
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/InTrainingRegistrationFoldersAsync")
     *     )
     * )
     *
     * @param RegistrationFolderService $registrationFolderService
     * @param Request $request
     * @return View|Response
     * @throws Exception
     */
    public function inTrainingAsync(RegistrationFolderService $registrationFolderService, Request $request)
    {
        /* @var $user User */
        $user = $this->getUser();
        $body = json_decode($request->getContent(), true);
        foreach ($body['registrationFolders'] as &$registrationFolderData) {
            if (isset($registrationFolderData['date']) && strtotime($registrationFolderData['date'])) {
                $registrationFolderData['date'] = (new DateTime($registrationFolderData['date']))->setTimezone(new DateTimeZone('Europe/Paris'));
            }
        }
        $violations = $this->validateInTrainingAsyncBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }
        $data = ["result" => []];
        unset($registrationFolderData);
        $delay = 0;
        foreach ($body['registrationFolders'] as $registrationFolderData) {
            $externalId = $registrationFolderData['externalId'];
            $result = ['externalId' => $externalId];
            $registrationFolder = $registrationFolderService->getByExternalId($externalId);
            if ($this->isGranted(RegistrationFolderVoter::EDIT, $registrationFolder)) {
                $scheduled = $registrationFolderService->inTrainingAsync($request->getClientIp(), $user, $registrationFolder, $registrationFolderData['date'] ?? null, $delay);
                $result['state'] = $scheduled ? 'scheduledToInTraining' : 'notScheduledWrongState';
                $delay += self::$ASYNC_DELAY_TO_ADD;
            } else {
                $result['state'] = 'notScheduled';
            }
            $data["result"][] = $result;
        }
        $view = $this->view($data, 200);
        return $this->handleView($view);
    }

    /**
     * @Rest\Post("/api/registrationFolders/{externalId}/terminate")
     * @IsGranted(RegistrationFolderVoter::EDIT, subject="registrationFolder", message="not allowed")
     * @Rest\View(StatusCode = 200, serializerGroups={"owner"})
     *
     * @ApiDoc\Areas({"registrationFolders", "default"})
     * @OA\Post (
     *     summary="Passe le dossier dans l'état 'sortie de formation'.",
     *     description="Passe le dossier dans l'état 'sortie de formation' s'il est dans l'état 'en formation'. Via OAuth2, cet appel nécessite le scope 'registrationfolder:write'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations du dossier dans l'état 'sortie de formation' ",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/RegistrationFolder")
     *     )
     * )
     * @OA\Parameter(
     *     name="externalId",
     *     in="path",
     *     description="ID du dossier",
     *     @OA\Schema(type="string")
     * )
     * @OA\RequestBody(
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(
     *             @OA\Property(property="date", type="date", description="Optionnel : Date du sortie de formation au format YYYY-MM-DD. Par défaut, date du jour.", example="2010-01-25"),
     *             @OA\Property(property="code", type="string", description="Optionnel : Code de sortie de formation (string) - les codes de sortie de formation possibles sont disponibles en appelant /api/registrationFoldersReasons?type=terminated.", example="8"),
     *             @OA\Property(property="absenceDuration", type="float", description="Optionnel : la durée d'une éventuelle absence en heures. 0 si aucune absence.", example=1.57)
     *         )
     *     )
     * )
     *
     * @param RegistrationFolder $registrationFolder
     * @param RegistrationFolderService $registrationFolderService
     * @param RegistrationFolderReasonService $registrationFolderReasonService
     * @param Request $request
     * @return RegistrationFolder|View
     * @throws Throwable
     */
    public function terminate(RegistrationFolder $registrationFolder, RegistrationFolderService $registrationFolderService, RegistrationFolderReasonService $registrationFolderReasonService, Request $request)
    {
        $body = json_decode($request->getContent(), true);

        if (isset($body['absenceDuration']) && is_numeric($body['absenceDuration'])) { // Toutes les propriétés renvoyées par Zapier sont en string...
            $body['absenceDuration'] = (float)$body['absenceDuration'];
        }
        if (isset($body['code']) && is_int($body['code'])) {
            $body['code'] = (string)$body['code'];
        }
        if (isset($body['date']) && strtotime($body['date'])) {
            $body['date'] = (new DateTime($body['date']))->setTimezone(new DateTimeZone('Europe/Paris'));
        }

        if (!empty($body)) {
            $violations = $this->validateTerminateBody($body, $registrationFolderReasonService);
            if (count($violations)) {
                return $this->view($violations, Response::HTTP_BAD_REQUEST);
            }
        }

        $reason = isset($body['code']) ? $registrationFolderReasonService->getReasonByCodeTerminated($body['code']) : null;
        return $registrationFolderService->terminate($registrationFolder, $body['absenceDuration'] ?? null, $reason, $body['date'] ?? null, $this->getUser());
    }

    /**
     * @Rest\Post("/api/registrationFolders/terminateAsync")
     * @Rest\View(StatusCode = 200, serializerGroups={"owner"})
     *
     * @ApiDoc\Areas({"registrationFolders", "default"})
     * @OA\Post (
     *     summary="Passer un Lot de dossiers à l'état 'sortie de formation'.",
     *     description="Procéder au changement de plusieurs dossiers vers l'état 'sortie de formation' s'ils sont dans l'état 'en formation'. Via OAuth2, cet appel nécessite le scope 'registrationfolder:write'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations des dossiers qui sont programmés pour passer à l'état 'sortie de formation' et ceux qui ne le seront pas",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/TerminateRegistrationFoldersAsyncResult")
     *     )
     * )
     * @OA\RequestBody(
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/TerminateRegistrationFoldersAsync")
     *     )
     * )
     *
     * @param RegistrationFolderService $registrationFolderService
     * @param RegistrationFolderReasonService $registrationFolderReasonService
     * @param Request $request
     * @return View|Response
     * @throws Exception
     */
    public function terminateAsync(RegistrationFolderService $registrationFolderService, RegistrationFolderReasonService $registrationFolderReasonService, Request $request)
    {
        /* @var $user User */
        $user = $this->getUser();
        $body = json_decode($request->getContent(), true);
        foreach ($body['registrationFolders'] as &$registrationFolderData) {
            if (isset($registrationFolderData['absenceDuration']) && is_numeric($registrationFolderData['absenceDuration'])) { // Toutes les propriétés renvoyées par Zapier sont en string...
                $registrationFolderData['absenceDuration'] = (float)$registrationFolderData['absenceDuration'];
            }
            if (isset($registrationFolderData['code']) && is_int($registrationFolderData['code'])) {
                $registrationFolderData['code'] = (string)$registrationFolderData['code'];
            }
            if (isset($registrationFolderData['date']) && strtotime($registrationFolderData['date'])) {
                $registrationFolderData['date'] = (new DateTime($registrationFolderData['date']))->setTimezone(new DateTimeZone('Europe/Paris'));
            }
        }
        $violations = $this->validateTerminateAsyncBody($body, $registrationFolderReasonService);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }
        $data = ["result" => []];
        unset($registrationFolderData);
        $delay = 0;
        foreach ($body['registrationFolders'] as $registrationFolderData) {
            $externalId = $registrationFolderData['externalId'];
            $result = ['externalId' => $externalId];
            $registrationFolder = $registrationFolderService->getByExternalId($externalId);
            if ($this->isGranted(RegistrationFolderVoter::EDIT, $registrationFolder)) {
                $scheduled = $registrationFolderService->terminateAsync($request->getClientIp(), $user, $registrationFolder, $registrationFolderData['absenceDuration'] ?? null, $registrationFolderData['code'] ?? null, $registrationFolderData['date'] ?? null, $delay);
                $result['state'] = $scheduled ? 'scheduledToTerminate' : 'notScheduledWrongState';
                $delay += self::$ASYNC_DELAY_TO_ADD;
            } else {
                $result['state'] = 'notScheduled';
            }
            $data["result"][] = $result;
        }
        $view = $this->view($data, 200);
        return $this->handleView($view);
    }

    /**
     * @Rest\Post("/api/registrationFolders/{externalId}/serviceDone")
     * @IsGranted(RegistrationFolderVoter::EDIT, subject="registrationFolder", message="not allowed")
     * @Rest\View(StatusCode = 200, serializerGroups={"owner"})
     *
     * @ApiDoc\Areas({"registrationFolders", "default"})
     * @OA\Post (
     *     summary="Passe le dossier dans l'état 'service fait déclaré'.",
     *     description="Passe le dossier dans l'état 'service fait déclaré' s'il est dans l'état 'sortie de formation' ou dans l'état 'en formation'. Si depuis l'état 'en formation', le passage à l'état intermédiaire 'sortie de formation' se fera automatiquement. Via OAuth2, cet appel nécessite le scope 'registrationfolder:write'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations du dossier dans l'état 'service fait déclaré' ",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/RegistrationFolder")
     *     )
     * )
     * @OA\Parameter(
     *     name="externalId",
     *     in="path",
     *     description="ID du dossier",
     *     @OA\Schema(type="string")
     * )
     * @OA\RequestBody(
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(
     *             @OA\Property(property="forceMajeureAbsence", default="false", type="boolean", description="Optionnel : si absence pour raison de force majeure, 'true', sinon 'false'. Par défaut, 'false'.", example=false),
     *             @OA\Property(property="trainingDuration", type="float", default="0", type="integer", description="Optionnel : précise la durée totale de la formation afin de calculer le % d'absence. Si rien n'est précisé, récupère la durée dans le trainingActionInfo/duration.", example=0),
     *             @OA\Property(property="absenceDuration", type="float", default="0", description="Optionnel : la durée d'une éventuelle absence en heures. 0 si aucune absence. Par défaut, 0. Si la durée d'absence a déjà été indiquée au moment du terminate, il n'est pas nécessaire de la repréciser.", example=1.57),
     *             @OA\Property(property="code", type="string", default="8", description="Optionnel : Code de sortie de formation (string) - les codes de sortie de formation possibles sont disponibles en appelant /api/registrationFoldersReasons?type=terminated. Par défaut, code 8 / réalisation intégrale de la commande. Si le code a déjà été indiqué au moment du terminate, il n'est pas nécessaire de le repréciser.", example="8"),
     *             @OA\Property(property="date", type="date", description="Optionnel : Date du sortie de formation au format YYYY-MM-DD. Par défaut, date du jour. Si la date a déjà été indiquée au moment du terminate, il n'est pas nécessaire de la repréciser.", example="2010-01-25")
     *         )
     *     )
     * )
     *
     * @param RegistrationFolder $registrationFolder
     * @param RegistrationFolderService $registrationFolderService
     * @param RegistrationFolderReasonService $registrationFolderReasonService
     * @param Request $request
     * @return RegistrationFolder|View
     * @throws Throwable
     */
    public function serviceDone(RegistrationFolder $registrationFolder, RegistrationFolderService $registrationFolderService, RegistrationFolderReasonService $registrationFolderReasonService, Request $request)
    {
        $body = json_decode($request->getContent(), true);
        if (isset($body['absenceDuration']) && is_numeric($body['absenceDuration'])) { // Toutes les propriétés renvoyées par Zapier sont en string...
            $body['absenceDuration'] = (float)$body['absenceDuration'];
        }
        if (isset($body['trainingDuration']) && is_numeric($body['trainingDuration'])) { // Toutes les propriétés renvoyées par Zapier sont en string...
            $body['trainingDuration'] = (float)$body['trainingDuration'];
        }
        if (isset($body['code']) && is_int($body['code'])) {
            $body['code'] = (string)$body['code'];
        }
        if (isset($body['date']) && strtotime($body['date'])) {
            $body['date'] = (new DateTime($body['date']))->setTimezone(new DateTimeZone('Europe/Paris'));
        }

        if (!empty($body)) {
            $violations = $this->validateServiceDoneBody($body, $registrationFolderReasonService);
            if (count($violations)) {
                return $this->view($violations, Response::HTTP_BAD_REQUEST);
            }
        }

        $reason = isset($body['code']) ? $registrationFolderReasonService->getReasonByCodeTerminated($body['code']) : null;
        return $registrationFolderService->declareServiceDone($registrationFolder, $body['absenceDuration'] ?? null, $body['forceMajeureAbsence'] ?? null, $reason, $body['date'] ?? null, $body['trainingDuration'] ?? 0.0, $this->getUser());
    }

    /**
     * @Rest\Post("/api/registrationFolders/serviceDoneAsync")
     * @Rest\View(StatusCode = 200, serializerGroups={"owner"})
     *
     * @ApiDoc\Areas({"registrationFolders", "default"})
     * @OA\Post (
     *     summary="Passer un lot de dossiers à l'état 'service fait déclaré'.",
     *     description="Procéder au changement de plusieurs dossiers vers l'état 'service fait déclaré' s'ils sont dans l'état 'sortie de formation' ou dans l'état 'en formation'. Si depuis l'état 'en formation', le passage à l'état intermédiaire 'sortie de formation' se fera automatiquement. Via OAuth2, cet appel nécessite le scope 'registrationfolder:write'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations des dossiers qui sont programmés pour passer à l'état 'service fait déclaré' et ceux qui ne le seront pas",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/ServiceDoneRegistrationFoldersAsyncResult")
     *     )
     * )
     * @OA\RequestBody(
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/ServiceDoneRegistrationFoldersAsync")
     *     )
     * )
     *
     * @param RegistrationFolderService $registrationFolderService
     * @param RegistrationFolderReasonService $registrationFolderReasonService
     * @param Request $request
     * @return View|Response
     * @throws Throwable
     */
    public function serviceDoneAsync(RegistrationFolderService $registrationFolderService, RegistrationFolderReasonService $registrationFolderReasonService, Request $request)
    {
        /* @var $user User */
        $user = $this->getUser();
        $body = json_decode($request->getContent(), true);
        foreach ($body['registrationFolders'] as &$registrationFolderData) {
            if (isset($registrationFolderData['absenceDuration']) && is_numeric($registrationFolderData['absenceDuration'])) { // Toutes les propriétés renvoyées par Zapier sont en string...
                $registrationFolderData['absenceDuration'] = (float)$registrationFolderData['absenceDuration'];
            }
            if (isset($registrationFolderData['trainingDuration']) && is_numeric($registrationFolderData['trainingDuration'])) { // Toutes les propriétés renvoyées par Zapier sont en string...
                $registrationFolderData['trainingDuration'] = (float)$registrationFolderData['trainingDuration'];
            }
            if (isset($registrationFolderData['code']) && is_int($registrationFolderData['code'])) {
                $registrationFolderData['code'] = (string)$registrationFolderData['code'];
            }
            if (isset($registrationFolderData['date']) && strtotime($registrationFolderData['date'])) {
                $registrationFolderData['date'] = (new DateTime($registrationFolderData['date']))->setTimezone(new DateTimeZone('Europe/Paris'));
            }
        }
        $violations = $this->validateServiceDoneAsyncBody($body, $registrationFolderReasonService);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }
        $data = ["result" => []];
        unset($registrationFolderData);
        $delay = 0;
        foreach ($body['registrationFolders'] as $registrationFolderData) {
            $externalId = $registrationFolderData['externalId'];
            $result = ['externalId' => $externalId];
            $registrationFolder = $registrationFolderService->getByExternalId($externalId);
            if ($this->isGranted(RegistrationFolderVoter::EDIT, $registrationFolder)) {
                $scheduled = $registrationFolderService->declareServiceDoneAsync($request->getClientIp(), $user, $registrationFolder, $registrationFolderData['absenceDuration'] ?? null, $registrationFolderData['forceMajeureAbsence'] ?? null, $registrationFolderData['code'] ?? null, $registrationFolderData['date'] ?? null, $registrationFolderData['trainingDuration'] ?? 0.0, $delay);
                $result['state'] = $scheduled ? 'scheduledToServiceDone' : 'notScheduledWrongState';
                $delay += self::$ASYNC_DELAY_TO_ADD;
            } else {
                $result['state'] = 'notScheduled';
            }
            $data["result"][] = $result;
        }
        $view = $this->view($data, 200);
        return $this->handleView($view);
    }

    /**
     * @Rest\Post("/api/registrationFolders/{externalId}/refuse")
     * @IsGranted(RegistrationFolderVoter::EDIT, subject="registrationFolder", message="not allowed")
     * @Rest\View(StatusCode = 200, serializerGroups={"owner"})
     *
     * @ApiDoc\Areas({"registrationFolders", "default"})
     * @OA\Post (
     *     summary="Passe le dossier dans l'état 'refusé'.",
     *     description="Passe le dossier dans l'état 'refusé' s'il est dans l'état 'À traiter'. Via OAuth2, cet appel nécessite le scope 'registrationfolder:write'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations du dossier refusé",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/RegistrationFolder")
     *     )
     * )
     * @OA\Parameter(
     *     name="externalId",
     *     in="path",
     *     description="ID du dossier",
     *     @OA\Schema(type="string")
     * )
     * @OA\RequestBody(
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(
     *             @OA\Property(property="code", type="string", description="Code de refus (string) - les codes de refus possibles sont disponibles en appelant /api/registrationFoldersReasons?type=refused.", example="1"),
     *             @OA\Property(property="description", type="text", description="Optionnel : texte expliquant les raisons du refus.",example="")
     *         )
     *     )
     * )
     *
     * @param RegistrationFolder $registrationFolder
     * @param RegistrationFolderService $registrationFolderService
     * @param RegistrationFolderReasonService $registrationFolderReasonService
     * @param Request $request
     * @return RegistrationFolder|View
     * @throws Throwable
     */
    public function refuse(RegistrationFolder $registrationFolder, RegistrationFolderService $registrationFolderService, RegistrationFolderReasonService $registrationFolderReasonService, Request $request)
    {
        $body = json_decode($request->getContent(), true);
        if (isset($body['code'])) {
            if (is_int($body['code'])) {
                $body['code'] = (string)$body['code'];
            }
        }
        $violations = $this->validateRefuseBody($body, $registrationFolderReasonService);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }
        return $registrationFolderService->refuse($registrationFolder, $registrationFolderReasonService->getReasonByCodeRefused($body['code']), $body['description'] ?? "");
    }

    /**
     * @Rest\Post("/api/registrationFolders/{externalId}/cancel")
     * @IsGranted(RegistrationFolderVoter::EDIT, subject="registrationFolder", message="not allowed")
     * @Rest\View(StatusCode = 200, serializerGroups={"owner"})
     *
     * @ApiDoc\Areas({"registrationFolders", "default"})
     * @OA\Post (
     *     summary="Passe le dossier dans l'état 'annulé'.",
     *     description="Passe le dossier dans l'état 'annulé' s'il est dans l'état 'accepté' ou 'validé'. Via OAuth2, cet appel nécessite le scope 'registrationfolder:write'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations du dossier annulé",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/RegistrationFolder")
     *     )
     * )
     * @OA\Parameter(
     *     name="externalId",
     *     in="path",
     *     description="ID du dossier",
     *     @OA\Schema(type="string")
     * )
     * @OA\RequestBody(
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(
     *             @OA\Property(property="code", type="string", description="Code d'annulation (string) - les codes de refus possibles sont disponibles en appelant /api/registrationFoldersReasons?type=canceled.", example="1"),
     *             @OA\Property(property="description", type="text", description="Optionnel : texte expliquant les raisons de l'annulation.", example="")
     *         )
     *     )
     * )
     *
     * @param RegistrationFolder $registrationFolder
     * @param RegistrationFolderService $registrationFolderService
     * @param RegistrationFolderReasonService $registrationFolderReasonService
     * @param Request $request
     * @return RegistrationFolder|View
     * @throws Throwable
     */
    public function cancel(RegistrationFolder $registrationFolder, RegistrationFolderService $registrationFolderService, RegistrationFolderReasonService $registrationFolderReasonService, Request $request)
    {
        $body = json_decode($request->getContent(), true);
        if (isset($body['code'])) {
            if (is_int($body['code'])) {
                $body['code'] = (string)$body['code'];
            }
        }
        $violations = $this->validateCancelBody($body, $registrationFolderReasonService);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }
        return $registrationFolderService->cancel($registrationFolder, $registrationFolderReasonService->getReasonByCodeCanceled($body['code']), $body['description'] ?? "");
    }

    /**
     * @Rest\Post("/api/registrationFolders/{externalId}/attendeeCancel")
     * @IsGranted(RegistrationFolderVoter::EDIT, subject="registrationFolder", message="not allowed")
     * @Rest\View(StatusCode = 200, serializerGroups={"owner"})
     *
     * @ApiDoc\Areas({"registrationFolders", "default"})
     * @OA\Post (
     *     summary="Passe le dossier dans l'état 'annulé par l'apprenant'.",
     *     description="Passe le dossier dans l'état 'annulé par l'apprenant' s'il est dans l'état 'accepté'. Via OAuth2, cet appel nécessite le scope 'registrationfolder:write'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations du dossier annulé",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/RegistrationFolder")
     *     )
     * )
     * @OA\Parameter(
     *     name="externalId",
     *     in="path",
     *     description="ID du dossier",
     *     @OA\Schema(type="string")
     * )
     *
     * @param RegistrationFolder $registrationFolder
     * @param RegistrationFolderService $registrationFolderService
     * @return RegistrationFolder
     * @throws ServerException
     */
    public function attendeeCancel(RegistrationFolder $registrationFolder, RegistrationFolderService $registrationFolderService): RegistrationFolder
    {
        return $registrationFolderService->attendeeCancel($registrationFolder);
    }

    /**
     * @Rest\Post("/api/registrationFolders/{externalId}/notify/{type}")
     * @IsGranted(RegistrationFolderVoter::EDIT, subject="registrationFolder", message="not allowed")
     * @Rest\View(StatusCode = 200, serializerGroups={"owner"})
     *
     * @param RegistrationFolder $registrationFolder
     * @param RegistrationFolderService $registrationFolderService
     * @param Request $request
     * @return View|void
     * @throws Throwable
     */
    public function notify(RegistrationFolder $registrationFolder, RegistrationFolderService $registrationFolderService, Request $request)
    {
        $body = json_decode($request->getContent(), true);
        $violations = $this->validateNotifyBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }
        $registrationFolderService->notify($registrationFolder, $body['content']);
    }

    //-----------------------
    // METHODES BILLING STATE
    //-----------------------
    /**
     * @Rest\Post ("/api/registrationFolders/{externalId}/billing")
     * @IsGranted(RegistrationFolderVoter::EDIT, subject="registrationFolder", message="not allowed")
     * @Rest\View(StatusCode = 200, serializerGroups={"owner"})
     *
     * @ApiDoc\Areas({"registrationFolders", "default"})
     * @OA\Post (
     *     summary="Facture le dossier.",
     *     description="Facture le dossier dans l'état 'service fait validé'. Via OAuth2, cet appel nécessite le scope 'registrationfolder:write'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations du dossier facturé",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/RegistrationFolder")
     *     )
     * )
     * @OA\Parameter(
     *     name="externalId",
     *     in="path",
     *     description="ID du dossier",
     *     @OA\Schema(type="string")
     * )
     * @OA\RequestBody(
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(
     *             @OA\Property(property="billNumber", type="string", description="Numéro de facture", example="BIL-001"),
     *             @OA\Property(property="vatRate", type="float", description="Facultatif: Permet de forcer un Taux de TVA en %. Par defaut la TVA est calculé à partir des données du dossier", example="20")
     *         )
     *     )
     * )
     *
     *
     * @param RegistrationFolder $registrationFolder
     * @param RegistrationFolderService $registrationFolderService
     * @param Request $request
     * @return RegistrationFolder|View
     * @throws Throwable
     */
    public function billing(RegistrationFolder $registrationFolder, RegistrationFolderService $registrationFolderService, Request $request)
    {
        $body = json_decode($request->getContent(), true);
        $violations = $this->validateBillingBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }
        return $registrationFolderService->bill($registrationFolder, $body['billNumber'], $body['vatRate'] ?? null);
    }

    /**
     * @Rest\Post ("/api/registrationFolders/billingAsync")
     * @Rest\View(StatusCode = 200, serializerGroups={"owner"})
     *
     * @ApiDoc\Areas({"registrationFolders", "default"})
     * @OA\Post (
     *     summary="Passer un lot de dossiers à 'facturé'.",
     *     description="Procéder au changement de plusieurs dossiers vers l'état 'facturé'. Via OAuth2, cet appel nécessite le scope 'registrationfolder:write'."
     * )
     * * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations des dossiers qui sont programmés pour être facturés et ceux qui ne le seront pas",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/BillingRegistrationFoldersAsyncResult")
     *     )
     * )
     * @OA\RequestBody(
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/BillingRegistrationFoldersAsync")
     *     )
     * )
     *
     *
     * @param RegistrationFolderService $registrationFolderService
     * @param Request $request
     * @return View|Response
     */
    public function billingAsync(RegistrationFolderService $registrationFolderService, Request $request)
    {
        $body = json_decode($request->getContent(), true);
        $violations = $this->validateBillingAsyncBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }
        $data = ["result" => []];
        $delay = 0;
        foreach ($body['registrationFolders'] as $registrationFolderData) {
            $externalId = $registrationFolderData['externalId'];
            $result = ['externalId' => $externalId];
            $registrationFolder = $registrationFolderService->getByExternalId($externalId);
            if ($this->isGranted(RegistrationFolderVoter::EDIT, $registrationFolder)) {
                $scheduled = $registrationFolderService->billAsync($request->getClientIp(), $registrationFolder, $registrationFolderData['billNumber'], $delay, $registrationFolderData['vatRate'] ?? null);
                $result['state'] = $scheduled ? 'scheduledToBilling' : 'notScheduledWrongState';
                $delay += self::$ASYNC_DELAY_TO_ADD;
            } else {
                $result['state'] = 'notScheduled';
            }
            $data["result"][] = $result;
        }
        $view = $this->view($data, 200);
        return $this->handleView($view);
    }

    /**
     * @Rest\Post ("/api/registrationFolders/{externalId}/paid")
     * @IsGranted(RegistrationFolderVoter::EDIT, subject="registrationFolder", message="not allowed")
     * @Rest\View(StatusCode = 200, serializerGroups={"owner"})
     *
     * @ApiDoc\Areas({"registrationFolders", "default"})
     * @OA\Post (
     *     summary="Marquer le dossier comme payé (uniquement pour les dossiers hors CPF).",
     *     description="Marquer le dossier (hors CPF) comme payé. Via OAuth2, cet appel nécessite le scope 'registrationfolder:write'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations du dossier",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/RegistrationFolder")
     *     )
     * )
     *
     * @param RegistrationFolder $registrationFolder
     * @param RegistrationFolderService $registrationFolderService
     * @return RegistrationFolder
     * @throws Throwable
     */
    public function paid(RegistrationFolder $registrationFolder, RegistrationFolderService $registrationFolderService): RegistrationFolder
    {
        return $registrationFolderService->paid($registrationFolder);
    }

    //--------------------------
    // AUTRES METHODES PUBLIQUES
    //--------------------------
    /**
     * @Rest\Get ("/funnel/api/registrationFolders/utils/sessionMinDates")
     * @Rest\Get ("/api/registrationFolders/utils/sessionMinDates")
     * @Security("is_granted('ROLE_SALES') or is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_REGISTRATIONFOLDER:READ')", message="not allowed")
     * @Rest\View(StatusCode = 200, serializerGroups={"sessionMinDates"})
     *
     * @ApiDoc\Areas({"registrationFolders", "default"})
     * @OA\Get (
     *     summary="Récupération des dates minimales de début de session.",
     *     description="Récupération des dates minimales de début de session en vue de valider un dossier - règle des 11 jours ouvrés pour l'apprenant pour accepter un dossier."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations de dates de début de session",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/SessionMinDates")
     *     )
     * )
     *
     * @param ConfigService $configService
     * @return Config
     * @throws OptimisticLockException
     * @throws Throwable
     * @throws \Doctrine\ORM\ORMException
     */
    public function getSessionMinDates(ConfigService $configService): Config
    {
        return $configService->getConfig();
    }

    /**
     * @Rest\Post("/api/registrationFolders/refreshAsync")
     * @Security("is_granted('ROLE_OAUTH2_REGISTRATIONFOLDER:WRITE') or is_granted('ROLE_USER')", message="not allowed")
     * @Rest\View(StatusCode = 200, serializerGroups={"owner"})
     *
     * @ApiDoc\Areas({"registrationFolders", "default"})
     * @OA\Post (
     *     summary="Demande le rafraichissement d'un ou plusieurs dossiers de formation.",
     *     description="Demande le rafraichissement d'un ou plusieurs dossiers de formation s'il est dans l'état non final. Via OAuth2, cet appel nécessite le scope 'registrationfolder:write'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations sur la demande de rafraichissement par dossier de formation",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/RefreshRegistrationFoldersAsyncResult")
     *     )
     * )
     * @OA\RequestBody(
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(
     *              ref="#/components/schemas/RefreshRegistrationFoldersAsync")
     *         )
     *     )
     * )
     *
     *
     * @param RegistrationFolderService $registrationFolderService
     * @param Request $request
     * @return View|Response
     */
    public function refreshAsync(RegistrationFolderService $registrationFolderService, Request $request)
    {
        /* @var $user User */
        $user = $this->getUser();
        $body = json_decode($request->getContent(), true);
        $violations = $this->validateRefreshAsyncBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }
        $data = ["result" => []];
        $refreshOnFinalState = $request->get("refreshOnFinalState", false);
        $delay = 0;
        foreach ($body['registrationFolders'] as $registrationFolderData) {
            $externalId = strval($registrationFolderData['externalId']);
            $result = ['externalId' => $externalId];
            $registrationFolder = $registrationFolderService->getByExternalId($externalId);
            if (!$registrationFolder) {
                if (!isset($registrationFolderData['retrieve']) || !$registrationFolderData['retrieve']) {
                    $result['state'] = 'canNotBeScheduled_folderDoesNotExists';
                } else {
                    $registrationFolderService->retrieveAsync($request->getClientIp(), $externalId, $user->getMainOrganism(), $delay);
                    $result['state'] = 'scheduledToCreateIfExist';
                    $delay += self::$ASYNC_DELAY_TO_ADD;
                }
            } else if ($this->isGranted(RegistrationFolderVoter::EDIT, $registrationFolder)) {
                $scheduled = $registrationFolderService->refreshAsync($request->getClientIp(), $registrationFolder, $delay, $refreshOnFinalState);
                $result['state'] = $scheduled ? 'scheduledToRefresh' : 'notScheduledFinalState';
                $delay += self::$ASYNC_DELAY_TO_ADD;
            } else {
                $result['state'] = 'notScheduled';
            }
            $data["result"][] = $result;
        }
        $view = $this->view($data, 200);
        return $this->handleView($view);
    }

    /**
     * @Rest\Post("/api/registrationFolders/{externalId}/files")
     * @Rest\Post("/app/attendees/registrationFolders/{externalId}/files")
     * @Security("is_granted('edit', registrationFolder) or is_granted('attendeeEdit', registrationFolder)", message="not allowed")
     * @Rest\View(StatusCode = 200, serializerGroups={"owner"})
     *
     * @ApiDoc\Areas({"registrationFolders", "default"})
     *
     * @OA\Post (
     *       summary="Envoyer un fichier.",
     *       description="Permet d'envoyer un fichier pour un dossier. Via OAuth2, cet appel nécessite le scope 'registrationFolder:write'."
     *   )
     * @OA\Response(
     *       response=200,
     *       description="Un json contenant la liste des documents au format : ",
     *       @OA\MediaType(mediaType="application/json",
     *           @OA\Schema(ref="#/components/schemas/UploadFileResult")
     *       )
     *   )
     * @OA\Parameter(
     *       name="externalId",
     *       in="path",
     *       description="externalId du dossier",
     *       @OA\Schema(type="string")
     *   )
     * @OA\RequestBody(
     *       @OA\MediaType(mediaType="multipart/form-data",
     *           @OA\Schema(ref="#/components/schemas/UploadFile")
     *       )
     *   )
     *
     * @param RegistrationFolder $registrationFolder
     * @param Request $request
     * @param RegistrationFolderFileService $registrationFolderFileService
     * @return RegistrationFolderFile[]|Collection
     * @throws Throwable
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function uploadFile(RegistrationFolder $registrationFolder, Request $request, RegistrationFolderFileService $registrationFolderFileService): Collection
    {
        $typeId = $request->get('typeId');
        $title = $request->get('title');
        $url = $request->get('fileToDownload');
        $organism = $registrationFolder->getOrganism();
        $registrationFolderFileTypes = $organism->getRegistrationFolderFileTypes();
        if (empty($registrationFolderFileTypes)) {
            throw new WedofBadRequestHttpException("Erreur, l'organisme n'a aucun document de prédéfinis (aucun typeId).");
        }
        $registrationFileTypeIds = array_column($registrationFolderFileTypes, 'id');
        $typeIdExist = in_array($typeId, $registrationFileTypeIds);
        if (!$typeIdExist) {
            throw new WedofBadRequestHttpException("Erreur, le 'typeId' que vous avez inséré n'existe pas.");
        }
        if ($url) {
            $isValidUrl = filter_var($url, FILTER_VALIDATE_URL);
            if (!$isValidUrl) {
                throw new WedofBadRequestHttpException("Erreur, le champ 'fileToDownload' n'est pas une URL valide.");
            }
            $fullFileName = pathinfo($url, PATHINFO_FILENAME) . '.' . pathinfo($url, PATHINFO_EXTENSION);
            $newFile = sys_get_temp_dir() . '/' . $fullFileName;
            copy($url, $newFile);
            $mimeType = mime_content_type($newFile);
            $file = new UploadedFile($newFile, $fullFileName, $mimeType, null, true);
        } else {
            $file = !empty($request->files->all()) ? $request->files->get('file') : $request->get('file');
        }
        $fileType = $this->getFileType($registrationFolder->getOrganism(), $typeId);
        $fileExtensions = array_key_exists('accept', $fileType) ? explode(',', $fileType['accept']) : ['.*'];
        if (!in_array('.*', $fileExtensions)) {
            if (is_file($file)) {
                $mimeTypes = Tools::fileExtensionsToMimeTypes($fileExtensions);
                $violations = $this->validateFile($file, $mimeTypes);
                if (count($violations)) {
                    throw new WedofBadRequestHttpException("Un document de type " . implode($fileExtensions) . " est attendu.");
                }
            } else if (!in_array('link', $fileExtensions)) {
                throw new WedofBadRequestHttpException("Un document de type " . implode($fileExtensions) . " est attendu.");
            }
        }
        if (!empty($fileType) && !empty($file)) {
            $user = $this->getUser();
            if (!($user instanceof User)) {
                $user = null;
            }
            $roles = $this->getRolesForAuthenticatedUser($registrationFolder, $user);
            $isGenerated = $this->isGranted('ROLE_ADMIN') && !empty($fileType['generated']);
            $registrationFolderFileService->create($file, intval($typeId), $registrationFolder, $isGenerated, $isGenerated ? null : $user, $roles['attendee'], $title);
            $newRegistrationFolderFiles = new ArrayCollection();
            $registrationFolderFiles = $registrationFolder->getFiles();
            $organism = $registrationFolder->getOrganism();
            foreach ($registrationFolderFiles as $registrationFolderFile) {
                $fileType = $this->getFileType($organism, $registrationFolderFile->getTypeId());
                if ($roles['attendee'] && $fileType['allowVisibilityAttendee']) {
                    $newRegistrationFolderFiles->add($registrationFolderFile);
                } else if ($roles['owner']) {
                    $newRegistrationFolderFiles->add($registrationFolderFile);
                }
            }
            return $newRegistrationFolderFiles;
        } else {
            throw new WedofBadRequestHttpException("Erreur, les attributs 'typeId' et 'file' sont obligatoires");
        }
    }

    /**
     * @Rest\Get("/api/registrationFolders/{externalId}/files/{registrationFolderFileId}")
     * @Rest\Get("/app/attendees/registrationFolders/{externalId}/files/{registrationFolderFileId}")
     * @Security("is_granted('view', registrationFolder) or is_granted('attendeeView', registrationFolder)", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"registrationFolders", "default"})
     * @OA\Get (
     *        summary="Télécharger un document",
     *        description="Télécharger le document d'un dossier de formation à l'aide de son externalId et de l'id du document."
     *    )
     * @OA\Response(
     *        response=200,
     *        description="Document à télécharger.",
     *    )
     * @OA\Parameter(
     *       name="externalId",
     *       in="path",
     *       description="externalId du dossier",
     *       @OA\Schema(type="string")
     *   )
     * @OA\Parameter(
     *        name="registrationFolderFileId",
     *        in="path",
     *        description="id du document",
     *        @OA\Schema(type="string")
     *    )
     *
     * @param Request $request
     * @param RegistrationFolder $registrationFolder
     * @param RegistrationFolderFileService $registrationFolderFileService
     * @param RegistrationFolderFileRepository $repository
     * @return array|string[]|StreamedResponse
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     * @throws WedofConnectionException
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws \Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface
     */
    public function downloadFile(Request $request, RegistrationFolder $registrationFolder, RegistrationFolderFileService $registrationFolderFileService, RegistrationFolderFileRepository $repository)
    {
        $registrationFolderFileId = $request->get('registrationFolderFileId');
        $registrationFolderFile = $repository->findOneBy(['registrationFolder' => $registrationFolder, 'id' => $registrationFolderFileId]);
        if (!$registrationFolderFile) {
            throw new WedofNotFoundHttpException("Aucun fichier d'id $registrationFolderFileId n'existe pour le dossier.");
        }
        $fileTypes = $registrationFolder->getOrganism()->getRegistrationFolderFileTypes($registrationFolder);
        $fileTypeIndex = array_search($registrationFolderFile->getTypeId(), array_column($fileTypes, 'id'));
        if ($fileTypeIndex !== false) {
            $fileType = $fileTypes[$fileTypeIndex];
        } else {
            $fileType = array_merge(Organism::getDefaultFileTypeRightsForAttendee());
        }
        $user = $this->getUser();
        if (!($user instanceof User)) {
            $user = null;
        }
        $roles = $this->getRolesForAuthenticatedUser($registrationFolder, $user);
        if ($roles['owner'] //owner / certifier
            || ($fileType['allowVisibilityAttendee'] && $roles['attendee'])) //attendee
        {
            return $registrationFolderFileService->download($registrationFolderFile, $this->getUser());
        } else {
            throw new WedofNotFoundHttpException();
        }
    }

    /**
     * @Rest\Delete("/api/registrationFolders/{externalId}/files/{registrationFolderFileId}")
     * @Rest\Delete("/app/attendees/registrationFolders/{externalId}/files/{registrationFolderFileId}")
     * @Security("is_granted('edit', registrationFolder) or is_granted('attendeeEdit', registrationFolder)", message="not allowed")
     * @Rest\View(StatusCode = 204)
     *
     * @param RegistrationFolder $registrationFolder
     * @param RegistrationFolderFileRepository $repository
     * @param Request $request
     * @param RegistrationFolderFileService $registrationFolderFileService
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function deleteFile(RegistrationFolder $registrationFolder, RegistrationFolderFileRepository $repository, Request $request, RegistrationFolderFileService $registrationFolderFileService)
    {
        $registrationFolderFileId = $request->get('registrationFolderFileId');
        $registrationFolderFile = $repository->findOneBy(['registrationFolder' => $registrationFolder, 'id' => $registrationFolderFileId]);
        if ($registrationFolderFile) {
            $user = $this->getUser();
            if (!($user instanceof User)) {
                $user = null;
            }
            $roles = $this->getRolesForAuthenticatedUser($registrationFolder, $user);
            $fileState = $registrationFolderFile->getState();
            if ($roles['owner'] || ($roles['attendee'] && $fileState !== FileStates::VALID()->getValue())) {
                $registrationFolderFileService->delete($registrationFolderFile, !$user, $user);
            } else {
                throw new WedofBadRequestHttpException("Erreur, vous n'avez pas les droits pour supprimer le fichier");
            }
        } else {
            throw new WedofNotFoundHttpException();
        }
    }

    /**
     * @Rest\Put("/api/registrationFolders/{externalId}/files/{registrationFolderFileId}")
     * @Rest\Put("/app/attendees/registrationFolders/{externalId}/files/{registrationFolderFileId}")
     * @Security("is_granted('edit', registrationFolder) or is_granted('attendeeEdit', registrationFolder)", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @param RegistrationFolder $registrationFolder
     * @param RegistrationFolderFileRepository $repository
     * @param Request $request
     * @param RegistrationFolderFileService $registrationFolderFileService
     * @return RegistrationFolderFile|View
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function updateFile(RegistrationFolder $registrationFolder, RegistrationFolderFileRepository $repository, Request $request, RegistrationFolderFileService $registrationFolderFileService)
    {
        $registrationFolderFileId = $request->get('registrationFolderFileId');
        $registrationFolderFile = $repository->findOneBy(['registrationFolder' => $registrationFolder, 'id' => $registrationFolderFileId]);
        if ($registrationFolderFile) {
            /* @var $user User */
            $user = $this->getUser();
            if (!($user instanceof User)) {
                $user = null;
            }
            $roles = $this->getRolesForAuthenticatedUser($registrationFolder, $user);
            //TODO update file from attendee (preserve security from state & comment
            if ($roles['owner']) {
                $body = json_decode($request->getContent(), true);
                $violations = $this->validateUpdateFileBody($body);
                if (count($violations)) {
                    return $this->view($violations, Response::HTTP_BAD_REQUEST);
                }
                return $registrationFolderFileService->updateState($registrationFolderFile, $body, $user);
            } else if ($roles['attendee'] && $registrationFolderFile->getSignedState() != DocumentSignedStates::NOT_REQUIRED()) {
                //allow attendee to update to trigger an update of signedState
                return $registrationFolderFileService->updateState($registrationFolderFile, [], $user);
            } else {
                throw new WedofBadRequestHttpException("Erreur, vous n'avez pas les droits pour modifier l'état du document");
            }
        } else {
            throw new WedofBadRequestHttpException("Erreur, le document n'a pas été trouvé");
        }
    }

    /**
     * @Rest\Get("/app/registrationFolders/{externalId}/getActions")
     * @IsGranted(RegistrationFolderVoter::VIEW, subject="registrationFolder", message="not allowed")
     * @Rest\View(StatusCode = 200)
     * @throws Throwable
     */
    public function getActions(RegistrationFolder $registrationFolder, RegistrationFolderService $registrationFolderService): array
    {
        /** @var User $user */
        $user = $this->getUser();
        return $registrationFolderService->getActions($registrationFolder, $user);
    }


    /**
     * @Rest\Get("/api/registrationFolders/{externalId}/files")
     * @Security("is_granted('view', registrationFolder) or is_granted('attendeeView', registrationFolder)", message="not allowed")
     * @Rest\View(StatusCode = 200)
     * @ApiDoc\Areas({"registrationFolders", "default"})
     * @OA\Get (
     *      summary="Liste des documents",
     *      description="La liste retourne les documents attendus et déjà renseignés."
     *  )
     * @OA\Response(
     *      response=200,
     *      description="Un json contenant les informations des documents du dossier de formation",
     *      @OA\MediaType(mediaType="application/json",
     *          @OA\Schema(ref="#/components/schemas/CertificationRegistrationFolderFile")
     *      )
     *  )
     * @OA\Parameter(
     *      name="externalId",
     *      in="path",
     *      description="externalId du dossier",
     *      @OA\Schema(type="string")
     * )
     * @param RegistrationFolder $registrationFolder
     * @return array
     */
    public function listAllRegistrationFoldersFile(RegistrationFolder $registrationFolder): array
    {
        $user = $this->getUser();
        if (!($user instanceof User)) {
            $user = null;
        }

        $roles = $this->getRolesForAuthenticatedUser($registrationFolder, $user);
        $registrationFolderFileTypes = $registrationFolder->getOrganism()->getRegistrationFolderFileTypes($registrationFolder);

        if (($roles['owner'] || $roles['attendee']) && $registrationFolderFileTypes) {
            $registrationFolderFiles = $registrationFolder->getFiles();
            $listAllRegistrationFoldersFile = [];
            foreach ($registrationFolderFileTypes as $registrationFolderFileType) {
                $found = false;
                if ($roles['owner'] || $registrationFolderFileType['allowVisibilityAttendee']) {
                    foreach ($registrationFolderFiles as $registrationFolderFile) {
                        if ($registrationFolderFileType['id'] == $registrationFolderFile->getTypeId()) {
                            $found = true;
                            $listAllRegistrationFoldersFile[] = [
                                "name" => $registrationFolderFileType['name'],
                                "fileName" => $registrationFolderFile->getFileName(),
                                "typeId" => $registrationFolderFileType['id'],
                                "state" => $registrationFolderFile->getState(),
                                "link" => $registrationFolderFile->getLink() ?? $registrationFolderFile->getFilePath() ?? null,
                                "requiredToState" => $registrationFolderFileType['toState'] ?? false,
                                "required" => $registrationFolderFileType['toState'] != null,
                                "allowUpload" => $roles['owner'] || $registrationFolderFile->getState() !== FileStates::VALID()->getValue() && $registrationFolderFileType['allowUploadAttendee'],
                                "type" => $registrationFolderFileType['accept'],
                                "signedState" => $registrationFolderFile->getSignedState()
                            ];
                        }
                    }
                    $FREE_FILE_TYPE_NAME = "Document libre";
                    if (!$found || $registrationFolderFileType['name'] === $FREE_FILE_TYPE_NAME) {
                        $listAllRegistrationFoldersFile[] = [
                            "name" => $registrationFolderFileType['name'],
                            "typeId" => $registrationFolderFileType['id'],
                            "state" => FileStates::toFrStringActivity(FileStates::NOT_SUBMITTED()->getValue()),
                            "requiredToState" => $registrationFolderFileType['toState'] ?? false,
                            "required" => $registrationFolderFileType['toState'] != null,
                            "allowUpload" => $roles['owner'] ? true : $registrationFolderFileType['allowUploadAttendee'],
                            "type" => $registrationFolderFileType['accept'],
                        ];
                    }
                }
            }

            foreach ($registrationFolderFiles as $registrationFolderFile) {
                $registrationFileTypeIds = array_column($registrationFolderFileTypes, 'id');
                $typeIdExist = in_array($registrationFolderFile->getTypeId(), $registrationFileTypeIds);
                if (!$typeIdExist) {
                    $listAllRegistrationFoldersFile[] = [
                        "name" => 'Aucun',
                        "fileName" => $registrationFolderFile->getFileName(),
                        "typeId" => $registrationFolderFile->getTypeId(),
                        "state" => $registrationFolderFile->getState(),
                        "link" => $registrationFolderFile->getLink() ?? $registrationFolderFile->getFilePath() ?? null,
                        "requiredToState" => false,
                        "required" => false,
                        "allowUpload" => !(!$roles['owner'] && $registrationFolderFile->getState() === FileStates::VALID()->getValue()),
                        "signedState" => $registrationFolderFile->getSignedState()
                    ];
                }
            }
            return $listAllRegistrationFoldersFile;
        } else {
            throw new WedofNotFoundHttpException();
        }
    }

    /**
     * @Rest\Get("/app/registrationFolders/kanban/columnConfigs")
     * @Security("is_granted('ROLE_USER')", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @param RegistrationFolderRepository $registrationFolderRepository
     * @return array
     */
    public function listColumnConfigs(RegistrationFolderRepository $registrationFolderRepository): array
    {
        return $registrationFolderRepository->listColumnConfigs();
    }

    //----------------
    // METHODES PRIVES
    //----------------
    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateInTrainingBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'date' => new Assert\Optional([new Assert\NotBlank(), new Assert\Type('datetime')])
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateInTrainingAsyncBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            "registrationFolders" => new Assert\Required([
                new Assert\Type('array'),
                new Assert\Count(['min' => 1]),
                new Assert\All([
                    new Assert\Collection([
                        'externalId' => new Assert\Required([new Assert\NotBlank(), new Assert\Type('string'), new Assert\Length(['max' => 255])]),
                        'date' => new Assert\Optional([new Assert\NotBlank(), new Assert\Type('datetime')])
                    ])
                ])
            ])
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param array $body
     * @param RegistrationFolderReasonService $registrationFolderReasonService
     * @return ConstraintViolationListInterface
     */
    private function validateTerminateBody(array $body, RegistrationFolderReasonService $registrationFolderReasonService): ConstraintViolationListInterface
    {
        $codes = $registrationFolderReasonService->getAllCodesTerminated();
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'absenceDuration' => new Assert\Optional([new Assert\NotBlank(), new Assert\Type('float')]),
            'date' => new Assert\Optional([new Assert\NotBlank(), new Assert\Type('datetime')]),
            'code' => new Assert\Optional([new Assert\NotBlank(), new Assert\Choice($codes)])
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param array $body
     * @param RegistrationFolderReasonService $registrationFolderReasonService
     * @return ConstraintViolationListInterface
     */
    private function validateTerminateAsyncBody(array $body, RegistrationFolderReasonService $registrationFolderReasonService): ConstraintViolationListInterface
    {
        $codes = $registrationFolderReasonService->getAllCodesTerminated();
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            "registrationFolders" => new Assert\Required([
                new Assert\Type('array'),
                new Assert\Count(['min' => 1]),
                new Assert\All([
                    new Assert\Collection([
                        'externalId' => new Assert\Required([new Assert\NotBlank(), new Assert\Type('string'), new Assert\Length(['max' => 255])]),
                        'date' => new Assert\Optional([new Assert\NotBlank(), new Assert\Type('datetime')]),
                        'code' => new Assert\Optional([new Assert\NotBlank(), new Assert\Choice($codes)]),
                        'absenceDuration' => new Assert\Optional([new Assert\NotBlank(), new Assert\Type('float')])
                    ])
                ])
            ])
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param array $body
     * @param RegistrationFolderReasonService $registrationFolderReasonService
     * @return ConstraintViolationListInterface
     */
    private function validateServiceDoneBody(array $body, RegistrationFolderReasonService $registrationFolderReasonService): ConstraintViolationListInterface
    {
        $codes = $registrationFolderReasonService->getAllCodesTerminated();
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'absenceDuration' => new Assert\Optional([new Assert\NotBlank(), new Assert\Type('float')]),
            'forceMajeureAbsence' => new Assert\Optional([new Assert\Type('bool')]),
            'trainingDuration' => new Assert\Optional([new Assert\Type('float')]),
            'code' => new Assert\Optional([new Assert\NotBlank(), new Assert\Choice($codes)]),
            'date' => new Assert\Optional([new Assert\NotBlank(), new Assert\Type('datetime')])
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param array $body
     * @param RegistrationFolderReasonService $registrationFolderReasonService
     * @return ConstraintViolationListInterface
     */
    private function validateServiceDoneAsyncBody(array $body, RegistrationFolderReasonService $registrationFolderReasonService): ConstraintViolationListInterface
    {
        $codes = $registrationFolderReasonService->getAllCodesTerminated();
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            "registrationFolders" => new Assert\Required([
                new Assert\Type('array'),
                new Assert\Count(['min' => 1]),
                new Assert\All([
                    new Assert\Collection([
                        'externalId' => new Assert\Required([new Assert\NotBlank(), new Assert\Type('string'), new Assert\Length(['max' => 255])]),
                        'absenceDuration' => new Assert\Optional([new Assert\NotBlank(), new Assert\Type('float')]),
                        'forceMajeureAbsence' => new Assert\Optional([new Assert\Type('bool')]),
                        'trainingDuration' => new Assert\Optional([new Assert\Type('float')]),
                        'code' => new Assert\Optional([new Assert\NotBlank(), new Assert\Choice($codes)]),
                        'date' => new Assert\Optional([new Assert\NotBlank(), new Assert\Type('datetime')])
                    ])
                ])
            ])
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param array $body
     * @param RegistrationFolderReasonService $registrationFolderReasonService
     * @return ConstraintViolationListInterface
     */
    private function validateRefuseBody(array $body, RegistrationFolderReasonService $registrationFolderReasonService): ConstraintViolationListInterface
    {
        $codes = $registrationFolderReasonService->getAllCodesRefused();
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'code' => new Assert\Optional([new Assert\NotBlank(), new Assert\Choice($codes)]),
            'description' => new Assert\Optional([new Assert\NotBlank(), new Assert\Type('string'), new Assert\Length(['max' => 255])])
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param array $body
     * @param RegistrationFolderReasonService $registrationFolderReasonService
     * @return ConstraintViolationListInterface
     */
    private function validateCancelBody(array $body, RegistrationFolderReasonService $registrationFolderReasonService): ConstraintViolationListInterface
    {
        $codes = $registrationFolderReasonService->getAllCodesCanceled();
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'code' => new Assert\Optional([new Assert\NotBlank(), new Assert\Choice($codes)]),
            'description' => new Assert\Optional([new Assert\NotBlank(), new Assert\Type('string'), new Assert\Length(['max' => 255])])
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateBillingBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'billNumber' => new Assert\Required([new Assert\NotBlank(), new Assert\Type('string'), new Assert\Length(['max' => 255])]),
            'vatRate' => new Assert\Optional([new Assert\NotBlank(), new Assert\Choice([0, 5.5, 20])])
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateBillingAsyncBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            "registrationFolders" => new Assert\Required([
                new Assert\Type('array'),
                new Assert\Count(['min' => 1]),
                new Assert\All([
                    new Assert\Collection([
                        'externalId' => new Assert\Required([new Assert\NotBlank(), new Assert\Type('string'), new Assert\Length(['max' => 255])]),
                        'billNumber' => new Assert\Required([new Assert\NotBlank(), new Assert\Type('string'), new Assert\Length(['max' => 255])]),
                        'vatRate' => new Assert\Optional([new Assert\NotBlank(), new Assert\Choice([0, 5.5, 20])])
                    ])
                ])
            ])
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateUpdateBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'allowExtraFields' => true,
            'fields' => [
                'priceChange' => new Assert\Optional(
                    new Assert\Collection([
                        'percent' => new Assert\Optional([new Assert\NotBlank(), new Assert\Type('float'), new Assert\GreaterThanOrEqual(-100)]),
                        'price' => new Assert\Optional([new Assert\NotBlank(), new Assert\Type('float'), new Assert\PositiveOrZero()]),
                        'amount' => new Assert\Optional([new Assert\NotBlank(), new Assert\Type('integer')])
                    ])
                ),
                'trainingActionInfo' => new Assert\Optional(
                    new Assert\Collection([
                        'allowExtraFields' => true,
                        'fields' => [
                            // sessionStartDate et sessionEndDate sont écrits dans le trainingActionInfos du rawData : ils ne doivent pas être transformés en objet datetime
                            'sessionStartDate' => new Assert\Optional([new Assert\NotBlank(), new Assert\Type('datetime')]),
                            'sessionEndDate' => new Assert\Optional([new Assert\NotBlank(), new Assert\Type('datetime')]),
                            'indicativeDuration' => new Assert\Optional([new Assert\NotBlank(), new Assert\Type('int')]),
                            'weeklyDuration' => new Assert\Optional([new Assert\Type('integer'), new Assert\Range(['max' => 99])]), //TODO WHY Not blank ?
                            'hoursInCenter' => new Assert\Optional([new Assert\Type('int')]),
                            'hoursInCompany' => new Assert\Optional([new Assert\Type('int')]),
                            'additionalFees' => new Assert\Optional([new Assert\NotBlank(), new Assert\PositiveOrZero()]),
                            'additionalFeesDetails' => new Assert\Optional([new Assert\Type('string')]),
                            'title' => new Assert\Optional([new Assert\NotBlank(), new Assert\Type('string')]),
                            'content' => new Assert\Optional([new Assert\NotBlank(), new Assert\Type('string')]),
                            'expectedResult' => new Assert\Optional([new Assert\NotBlank(), new Assert\Type('string')]),
                            'trainingGoal' => new Assert\Optional([new Assert\NotBlank(), new Assert\Type('string')]),
                            'trainingPaces' => new Assert\Optional([new Assert\NotNull(), new Assert\Type('array'), new Assert\Unique(), new Assert\All([new Assert\Choice(["1", "2", "3", "4", "5", "6", "7"])])]),
                            'teachingModalities' => new Assert\Optional([new Assert\NotBlank(), new Assert\Choice(["0", "1", "2",])]),
                            'typeOfTrainingCourse' => new Assert\Optional([new Assert\NotBlank(), new Assert\Choice([1, 2, 3, 4])]),
                            /*'address' => new Assert\Optional(new Assert\Collection([ //TODO How it can work without this commented?
                                'allowExtraFields' => true,
                                'fields' => [
                                    'number' => new Assert\Optional([new Assert\NotBlank(), new Assert\Type('string')]),
                                    'residence' => new Assert\Optional([new Assert\Type('string')]),
                                    'postBox' => new Assert\Optional([new Assert\Type('string')]),
                                    'roadType' => new Assert\Optional([new Assert\NotBlank(), new Assert\Choice(["ACH", "ALL", "ART", "AV", "BD", "BEGI", "CHE", "CHP", "CHS", "CHT", "CHV", "CITE", "COR", "COTE", "CRS",
                                        "DOM", "DSC", "ECA", "ESP", "ESPA", "FG", "GR", "LOT", "CAR", "HAM", "HLE", "IMP", "LD", "MAIL", "MAR", "MTE", "PARC", "PAS", "PL", "PLT", "PRO", "PRV", "PTR", "QUA", "QUAI",
                                        "RES", "RLE", "ROC", "RPT", "RTD", "RTE", "RUE", "SEN", "SQ", "TPL", "TRA", "VEN", "VLA", "VOIE", "VTE", "VLGE"])]),
                                    'roadName' => new Assert\Optional([new Assert\NotBlank(), new Assert\Type('string')]),
                                    'additionalAddress' => new Assert\Optional([new Assert\Type('string')]),
                                    'zipCode' => new Assert\Optional([new Assert\NotBlank(), new Assert\Type('string'), new Assert\Length(5)]),
                                    'city' => new Assert\Optional([new Assert\NotBlank(), new Assert\Type('string')]),
                                    'repetitionIndex' => new Assert\Optional([new Assert\Choice(["B", "C", "Q", "T"])])
                                ]
                            ]))*/
                        ]
                    ])
                ),
                'tags' => new Assert\Optional(new Assert\Type('array')),
                'addedTags' => new Assert\Optional(new Assert\Type('array')),
                'removedTags' => new Assert\Optional(new Assert\Type('array')),
                'notes' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 255])]),
                'description' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 255])]),
                'inPartnershipWith' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(14)]),
                'completionRate' => new Assert\Optional([new Assert\Type('integer'), new Assert\Range(['min' => 0, 'max' => 100])]),
                'controlState' => new Assert\Optional([new Assert\NotBlank(), new Assert\Choice([RegistrationFolderControlStates::IN_CONTROL()->getValue(), RegistrationFolderControlStates::RELEASED()->getValue(), RegistrationFolderControlStates::NOT_IN_CONTROL()->getValue()]), new Assert\NotNull()]),
                'controlStateDate' => new Assert\Optional([new Assert\NotBlank(), new Assert\Type('datetime')]),
                'metadata' => new Assert\Optional(new Assert\Type('array'))
            ]
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateValidateBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'weeklyDuration' => new Assert\Optional([new Assert\NotBlank(), new Assert\Type('integer'), new Assert\Range(['max' => 99])]),
            'indicativeDuration' => new Assert\Optional([new Assert\NotBlank(), new Assert\Type('integer')])
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateNotifyBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'content' => new Assert\Required([new Assert\NotBlank(), new Assert\Type('string')]),
            'service' => new Assert\Optional([new Assert\NotBlank(), new Assert\Type('string')])
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateCreateBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        if ($body['type'] === 'poleEmploi') {
            $constraints = new Assert\Collection([
                'sessionId' => new Assert\Required([new Assert\NotBlank(), new Assert\Type('integer'), new Assert\NotNull()]),
                'attendeeId' => new Assert\Required([new Assert\NotBlank(), new Assert\Type('integer'), new Assert\NotNull()]),
                'totalTTC' => new Assert\Required([new Assert\NotBlank(), new Assert\Type('float'), new Assert\NotNull(), new Assert\PositiveOrZero()]),
                'type' => new Assert\Required([new Assert\NotBlank(), new Assert\Choice([DataProviders::POLE_EMPLOI()->getValue()]), new Assert\NotNull()]),
                'poleEmploiId' => new Assert\Required([new Assert\Type('string'), new Assert\NotBlank(), new Assert\NotNull(), new Assert\Length(['max' => 255])]),
                'poleEmploiRegionCode' => new Assert\Required([new Assert\Type('string'), new Assert\Length(3), new Assert\Regex(['pattern' => '/^[0-9]{3}$/']), new Assert\NotBlank(), new Assert\NotNull()]),
                'poleEmploiDevis' => new Assert\Required([new Assert\Type('string'), new Assert\NotBlank(), new Assert\NotNull()]),
                'inPartnershipWith' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['min' => 14, 'max' => 14])]),
            ]);
        } else {
            $constraints = new Assert\Collection([
                'sessionId' => new Assert\Required([new Assert\NotBlank(), new Assert\Type('integer'), new Assert\NotNull()]),
                'attendeeId' => new Assert\Required([new Assert\NotBlank(), new Assert\Type('integer'), new Assert\NotNull()]),
                'totalTTC' => new Assert\Required([new Assert\NotBlank(), new Assert\Type('float'), new Assert\NotNull(), new Assert\PositiveOrZero()]),
                'type' => new Assert\Required([new Assert\NotBlank(), new Assert\Choice([DataProviders::INDIVIDUAL()->getValue(), DataProviders::COMPANY()->getValue(), DataProviders::OPCO()->getValue()]), new Assert\NotNull()]),
                'inPartnershipWith' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(14)]),
            ]);
        }
        return $validator->validate($body, $constraints);
    }

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateRefreshAsyncBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            "registrationFolders" => new Assert\Required([
                new Assert\Type('array'),
                new Assert\Count(['min' => 1, "max" => 100]),
                new Assert\All([
                    new Assert\Collection([
                        'externalId' => new Assert\Required([new Assert\NotBlank(), new Assert\Type('string'), new Assert\Length(['max' => 255])]),
                        'retrieve' => new Assert\Optional([new Assert\NotBlank(), new Assert\Type('bool')])
                    ])
                ])
            ])
        ]);
        return $validator->validate($body, $constraints);
    }


    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateUpdateFileBody(array $body): ConstraintViolationListInterface
    {
        $validation = Validation::createValidator();
        $constraints = new Assert\Collection([
            'state' => new Assert\Optional([
                new Assert\Choice([
                    FileStates::REFUSED()->getValue(),
                    FileStates::VALID()->getValue(),
                    FileStates::TO_REVIEW()->getValue()
                ])
            ]),
            'comment' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 1000])])
        ]);
        return $validation->validate($body, $constraints);
    }

    /**
     * @param $file
     * @param array $mimeTypes
     * @return ConstraintViolationListInterface
     */
    private function validateFile($file, array $mimeTypes): ConstraintViolationListInterface
    {
        if ($file->getMimeType() !== "application/octet-stream") {
            $validation = Validation::createValidator();
            $constraints = new Assert\File(['mimeTypes' => $mimeTypes]);
            $violations = $validation->validate($file, $constraints);
        } else {
            $violations = new ConstraintViolationList();
            if (!in_array($file->getClientMimeType(), $mimeTypes)) {
                $violations->add(new ConstraintViolation("ClientMimeType not found in mimeTypes.", null, [], $file, null, $file->getClientMimeType()));
            }
        }
        return $violations;
    }

    /**
     * @param array $body
     * @return array
     */
    private function clean(array $body): array
    {
        $notAllowed = ['externalId', 'trainingId', 'trainingActionId', 'sessionId'];

        if (isset($body['trainingActionInfo'])) {
            $filterBody = array_filter($body['trainingActionInfo'], function ($value) use ($notAllowed) {
                return !in_array($value, $notAllowed);
            }, ARRAY_FILTER_USE_KEY);
            $body['trainingActionInfo'] = $filterBody;
        }
        return $body;
    }

    /**
     * @param User $user
     * @param Organism $organism
     * @param array $parameters
     * @param ProposalService $proposalService
     * @param OrganismService $organismService
     * @param SessionService $sessionService
     * @param TrainingActionService $trainingActionService
     * @param TrainingService $trainingService
     * @param CertificationService $certificationService
     * @return array
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     * @throws WedofSubscriptionException
     */
    private function validateListParameters(User $user, Organism $organism, array $parameters, ProposalService $proposalService, OrganismService $organismService, SessionService $sessionService, TrainingActionService $trainingActionService, TrainingService $trainingService, CertificationService $certificationService): array
    {
        if (!$organism->getSubscription()->isAllowRegistrationFolderReads()) {
            throw new WedofSubscriptionException("Erreur, votre abonnement ne permet pas l'accès aux dossiers de formation.");
        }

        if (isset($parameters['state'])) {
            $parameters['state'] = explode(",", $parameters['state']);
            foreach ($parameters['state'] as $state) {
                if (!in_array($state, RegistrationFolderStates::valuesStates())) {
                    throw new WedofBadRequestHttpException("Erreur sur les valeurs renvoyées 'state', elles doivent être : " . join(",", RegistrationFolderStates::valuesStates()) . ".");
                }
            }
        }
        if (isset($parameters['type'])) {
            $parameters['type'] = explode(",", $parameters['type']);
            foreach ($parameters['type'] as $type) {
                if ($type !== 'all' && !in_array($type, DataProviders::valuesTypes())) {
                    throw new WedofBadRequestHttpException("Erreur sur les valeurs renvoyées 'type', elles doivent être : " . join(",", DataProviders::valuesTypes()) . ".");
                }
            }
        }
        if (isset($parameters['billingState'])) {
            $parameters['billingState'] = explode(",", $parameters['billingState']);
            foreach ($parameters['billingState'] as $billingState) {
                if (!in_array($billingState, RegistrationFolderBillingStates::valuesStates())) {
                    throw new WedofBadRequestHttpException("Erreur sur les valeurs renvoyées 'billingState', elles doivent être : " . join(",", RegistrationFolderBillingStates::valuesStates()) . ".");
                }
            }
        }

        if (isset($parameters['period']) && $parameters['period'] != PeriodTypes::CUSTOM()->getValue()) {
            $getDates = Tools::getSinceAndUntilDates($parameters['period']);
            $parameters['since'] = $getDates['since'];
            $parameters['until'] = $getDates['until'];
        }

        if (isset($parameters['certificationFolderState'])) {
            $parameters['certificationFolderState'] = explode(",", $parameters['certificationFolderState']);
            foreach ($parameters['certificationFolderState'] as $certificationFolderState) {
                if (!in_array($certificationFolderState, CertificationFolderStates::valuesStates())) {
                    throw new WedofBadRequestHttpException("Erreur sur les valeurs renvoyées 'certificationFolderState', elles doivent être : " . join(",", CertificationFolderStates::valuesStates()) . ".");
                }
            }
        }
        if (isset($parameters['controlState'])) {
            $parameters['controlState'] = explode(",", $parameters['controlState']);
            foreach ($parameters['controlState'] as $controlStates) {
                if (!in_array($controlStates, RegistrationFolderControlStates::valuesStates())) {
                    throw new WedofBadRequestHttpException("Erreur sur les valeurs renvoyées 'controlState', elles doivent être : " . join(",", RegistrationFolderControlStates::valuesStates()) . ".");
                }
            }
        }
        if (isset($parameters['messageState'])) {
            $parameters['messageState'] = explode(",", $parameters['messageState']);
            foreach ($parameters['messageState'] as $messageState) {
                if (!in_array($messageState, MessageStates::valuesTypes())) {
                    throw new WedofBadRequestHttpException("Erreur sur les valeurs renvoyées 'messageState', elles doivent être : " . join(",", MessageStates::valuesTypes()) . ".");
                }
            }
        }
        if (isset($parameters['messageTemplate'])) {
            $parameters['messageTemplate'] = explode(",", $parameters['messageTemplate']);
        }
        if (isset($parameters['siret'])) {
            $askedOrganism = $organismService->getBySiret($parameters['siret']);
            if (!empty($askedOrganism)) {
                if (!($this->isGranted(OrganismVoter::VIEW, $askedOrganism) || $this->isGranted(OrganismVoter::CERTIFICATION_VIEW, $askedOrganism))) {
                    throw new WedofAccessDeniedHttpException("Vous n'êtes pas autorisé à avoir accès au contenu de l'organisme associé au siret " . $parameters['siret']);
                } else if ($askedOrganism !== $organism) {
                    $parameters['organismType'] = 'partners';
                    $parameters['partner'] = $askedOrganism;
                }
            } else {
                throw new WedofNotFoundHttpException("L'organisme associé au siret " . $parameters['siret'] . " n'a pas été trouvé.");
            }
        }
        if (isset($parameters['certifInfo'])) {
            $certification = $certificationService->getByCertifInfo($parameters['certifInfo']);
            if (!empty($certification)) {
                if ($this->isGranted(CertificationVoter::VIEW_ADVANCED, $certification)) {
                    $parameters['certifications'] = [$certification];
                } else {
                    throw new WedofAccessDeniedHttpException("Vous n'êtes pas autorisé à avoir accès au contenu de la certification associée au certifInfo " . $parameters['certifInfo']);
                }
            } else {
                throw new WedofNotFoundHttpException("La certification associée au certifInfo " . $parameters['certifInfo'] . " n'a pas été trouvée.");
            }
        }
        if ($parameters['organismType'] === 'partners') {
            $parameters['certifications'] = $parameters['certifications'] ?? $user->getMainOrganism()->getCertifierCertifications();

            if (!in_array('all', $parameters['billingState'])) {
                throw new WedofBadRequestHttpException("Seul le propriétaire du dossier peut filtrer sur les états de facturation");
            }

            if (!empty(array_intersect(['notProcessed', 'validated', 'refusedByOrganism', 'refusedByAttendee'], $parameters['state']))) {
                throw new WedofBadRequestHttpException("Seul le propriétaire du dossier peut accéder aux dossiers dans les états 'À traiter', 'Validé', 'Refusé'");
            }
        }
        if (isset($parameters['proposalCode'])) {
            $parameters['proposal'] = $proposalService->getByCode($parameters['proposalCode']);
            if (!empty($parameters['proposal'])) {
                if (!$this->isGranted(ProposalVoter::VIEW, $parameters['proposal'])) {
                    throw new WedofAccessDeniedHttpException("Vous n'êtes pas autorisé à avoir accès au contenu au dossier associé à la proposition " . $parameters['proposalCode']);
                }
            }
        }
        if (isset($parameters['sessionId'])) {
            $session = $sessionService->getByExternalId($parameters['sessionId']);
            if (!empty($session)) {
                if (!$this->isGranted(SessionVoter::VIEW, $session)) {
                    throw new WedofAccessDeniedHttpException("Vous n'êtes pas autorisé à avoir accès au contenu de la session associée à l'id " . $parameters['sessionId']);
                }
            } else {
                throw new WedofNotFoundHttpException("La session associée à l'id " . $parameters['sessionId'] . " n'a pas été trouvée.");
            }
        }
        if (isset($parameters['trainingActionId'])) {
            $trainingAction = $trainingActionService->getByExternalId($parameters['trainingActionId']);
            if (!empty($trainingAction)) {
                if (!$this->isGranted(TrainingActionVoter::VIEW, $trainingAction)) {
                    throw new WedofAccessDeniedHttpException("Vous n'êtes pas autorisé à avoir accès au contenu de l'action de formation associée à l'id " . $parameters['trainingActionId']);
                }
            } else {
                throw new WedofNotFoundHttpException("L'action de formation associée à l'id " . $parameters['trainingActionId'] . " n'a pas été trouvée.");
            }
        }
        if (isset($parameters['trainingId'])) {
            $training = $trainingService->getByExternalId($parameters['trainingId']);
            if (!empty($training)) {
                if (!$this->isGranted(TrainingVoter::VIEW, $training)) {
                    throw new WedofAccessDeniedHttpException("Vous n'êtes pas autorisé à avoir accès au contenu de la formation associée à l'id " . $parameters['trainingId']);
                }
            } else {
                throw new WedofNotFoundHttpException("La formation associée à l'id " . $parameters['trainingId'] . " n'a pas été trouvée.");
            }
        }
        if (isset($parameters['metadata'])) {
            $parameters['metadata'] = explode(":", $parameters['metadata'], 2); //key=value if value contains : it shoulds work too but not if it is part of the key...
            if (sizeof($parameters['metadata']) > 2) {
                throw new WedofBadRequestHttpException("Erreur sur le format 'metadata', il doit être le suivant : cle:valeur");
            }
        }

        return $parameters;
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @param User|null $user
     * @return array
     */
    private function getRolesForAuthenticatedUser(RegistrationFolder $registrationFolder, User $user = null): array
    {
        if ($this->isGranted('ROLE_ADMIN')) {
            return [
                'attendee' => false,
                'owner' => true
            ];
        }
        return [
            'attendee' => !$user && $this->isGranted(RegistrationFolderVoter::ATTENDEE_EDIT, $registrationFolder),
            'owner' => $user && $user->getMainOrganism() === $registrationFolder->getOrganism()
        ];
    }

    /**
     * @param Organism $organism
     * @param $typeId
     * @return array
     */
    private function getFileType(Organism $organism, $typeId): array
    {
        $fileTypes = $organism->getRegistrationFolderFileTypes();
        $fileTypeIndex = array_search($typeId, array_column($fileTypes, 'id'));
        if ($fileTypeIndex !== false) {
            $fileType = $fileTypes[$fileTypeIndex];
        }
        return $fileType ?? Organism::getDefaultFileTypeRightsForAttendee();
    }
}
