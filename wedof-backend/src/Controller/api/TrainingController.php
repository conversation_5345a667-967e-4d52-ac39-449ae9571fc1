<?php
// src/Controller/api/TrainingController.php
namespace App\Controller\api;

use App\Entity\CertificationPartner;
use App\Entity\Organism;
use App\Entity\Training;
use App\Entity\User;
use App\Exception\WedofAccessDeniedHttpException;
use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofNotFoundHttpException;
use App\Library\utils\enums\CertificationSkillType;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\enums\TrainingComplianceTypes;
use App\Library\utils\Tools;
use App\Repository\TrainingRepository;
use App\Security\Voter\CertificationPartnerVoter;
use App\Security\Voter\CertificationVoter;
use App\Security\Voter\OrganismVoter;
use App\Security\Voter\TrainingVoter;
use App\Service\CertificationPartnerService;
use App\Service\CertificationService;
use App\Service\CertifierAccessService;
use App\Service\OrganismService;
use App\Service\SkillService;
use App\Service\TrainingService;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use ErrorException;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcherInterface;
use FOS\RestBundle\View\View;
use Knp\Component\Pager\PaginatorInterface;
use Nelmio\ApiDocBundle\Annotation as ApiDoc;
use OpenApi\Annotations as OA;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validation;
use Throwable;

/**
 * Class TrainingController
 * @package App\Controller\api
 *
 * @OA\Tag(name="Training")
 * @ApiDoc\Security(name="accessCode")
 *
 */
class TrainingController extends AbstractFOSRestController
{
    //-------------------
    // METHODES PUBLIQUES
    //-------------------
    /**
     * @Rest\Get("/api/training/{externalId}/{dataProvider}", requirements={"externalId"=".+"})
     * @Rest\Get("/api/training/{externalId}", requirements={"externalId"=".+"})
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_TRAINING:READ')", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"trainings", "default"})
     * @OA\Get (
     *     summary="Récupération d'une formation.",
     *     description="Récupération d'une formation par son ID. Via OAuth2, cet appel nécessite le scope 'training:read'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations de la formation recherchée.",
     *     @ApiDoc\Model(type=Training::class))
     *     )
     * )
     * @OA\Parameter(
     *     name="id",
     *     in="path",
     *     description="ID de la formation",
     *     @OA\Schema(type="string")
     * )
     *
     * @param TrainingService $trainingService
     * @param string $externalId
     * @param string|null $dataProvider
     * @return Training|Response
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function show(TrainingService $trainingService, string $externalId, string $dataProvider = null)
    {
        try {
            $_dataProvider = $dataProvider ? DataProviders::from($dataProvider) : null;
        } catch (Throwable $exception) {
            throw new BadRequestHttpException("DataProvider inconnu.");
        }
        $options = $_dataProvider ? ['createIfNotExist' => true] : [];
        try {
            return $trainingService->getByExternalId($externalId, $_dataProvider, $options);
        } catch (ErrorException $e) {
            if (Tools::isNoResultException($e)) {
                return new Response(null, 404);
            } else {
                throw $e;
            }
        }
    }

    /**
     * @Rest\Get("/api/trainings")
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_TRAINING:READ')", message="not allowed")
     * @Rest\View(StatusCode = 200)
     * @Rest\QueryParam(name="certifInfo", requirements=@Assert\Type("string"), nullable=true, description="Permet de n'obtenir que les formations appartenant à la certification considérée - par défaut toutes les certifications.")
     * @Rest\QueryParam(name="siret", requirements=@Assert\Type("string"), nullable=true, description="Permet de n'obtenir que les formations appartenant à l'organisme de siret considéré - par défaut l'organisme de l'utilisateur courant.")
     * @Rest\QueryParam(name="state", requirements=@Assert\Type("string"), nullable=true, description="Permet de n'obtenir que les formations dans l'état demandé (draft, published, unpublished, archived)")
     * @Rest\QueryParam(name="query", requirements=@Assert\Type("string"), nullable=true, description="Permet de filtrer les formations par son nom ou son identifiant")
     * @Rest\QueryParam(name="eligible", requirements=@Assert\Choice({"false", "true"}), nullable=true, description="Permet de n'obtenir que les formations éligibles (elles sont rendues inéligibles quand la certification expire)")
     * @Rest\QueryParam(name="dataProvider", requirements=@Assert\Choice({"cpf", "kairosAif", "opcoCfa", "all"}), default="all", description="Permet de filtrer les formations par financeur.")
     * @Rest\QueryParam(name="limit", requirements="\d+", default="100", description="Nombre d'éléments retourné par requête - par défaut 100.")
     * @Rest\QueryParam(name="page", requirements="\d+", default="1", description="Numéro de page de la requête - par défaut la première.")
     * @Rest\QueryParam(name="order", requirements=@Assert\Choice({"asc", "desc"}), default="desc", description="Tri les résultats par ordre ascendant ou descendant - par défaut descendant.")
     *
     * @ApiDoc\Areas({"trainings", "default"})
     * @OA\Get (
     *     summary="Liste toutes les formations de l'organisme de l'utilisateur courant. Peut aussi lister les formations de manière filtrée.",
     *     description="Récupère l'ensemble des formations liées à l'organisme de l'utilisateur connecté. NOTA : les paramètres sont cumulatifs et deux paramètres incompatibles renverront un résultat vide. Via OAuth2, cet appel nécessite le scope 'training:read'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations d'une formation.",
     *     @ApiDoc\Model(type=Training::class))
     *     )
     * )
     * @OA\Parameter (name="order", in="query", @OA\Schema (ref="#/components/schemas/Order"))
     *
     *
     * @param TrainingRepository $trainingRepository
     * @param OrganismService $organismService
     * @param CertificationService $certificationService
     * @param CertifierAccessService $certifierAccessService
     * @param PaginatorInterface $paginator
     * @param ParamFetcherInterface $paramFetcher
     * @return Response
     * @throws NonUniqueResultException
     */
    public function list(TrainingRepository $trainingRepository, OrganismService $organismService, CertificationService $certificationService, CertifierAccessService $certifierAccessService, PaginatorInterface $paginator, ParamFetcherInterface $paramFetcher): Response
    {
        /* @var $user User */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();
        $parameters = $paramFetcher->all(true);
        $certification = !empty($parameters['certifInfo']) ? $certificationService->getByCertifInfo($parameters['certifInfo']) : null;
        $organismRequested = !empty($parameters['siret']) ? $organismService->getBySiret($parameters['siret']) : null;
        $certificationPartner = $certification && $organismRequested ? $certifierAccessService->getByCertificationAndPartner($certification, $organismRequested) : null;

        if ($organismRequested) {
            if (empty($certificationPartner) && !$this->isGranted(OrganismVoter::VIEW, $organismRequested)) {
                throw new WedofAccessDeniedHttpException("Vous n'êtes pas autorisé à avoir accès au contenu de l'organisme associé au siret " . $parameters['siret']);
            }
        } else if (!empty($parameters['siret'])) {
            throw new WedofNotFoundHttpException("L'organisme associé au siret " . $parameters['siret'] . " n'a pas été trouvé.");
        }

        if ($certification) {
            if (!$this->isGranted(CertificationVoter::VIEW_ADVANCED, $certification)) {
                throw new WedofAccessDeniedHttpException("Vous n'êtes pas autorisé à avoir accès au contenu de la certification associée au certifInfo " . $parameters['certifInfo']);
            }
        } else if (!empty($parameters['certifInfo'])) {
            throw new WedofNotFoundHttpException("La certification associée au certifInfo " . $parameters['certifInfo'] . " n'a pas été trouvée.");
        }

        if ($certificationPartner) {
            if (!$this->isGranted(CertificationPartnerVoter::VIEW, $certificationPartner)) {
                throw new WedofAccessDeniedHttpException("Vous n'êtes pas autorisé à avoir accès au contenu de la certification associée au certifInfo " . $parameters['certifInfo']);
            }
        } else if (!empty($parameters['certifInfo']) && !empty($parameters['siret'])) {
            throw new WedofNotFoundHttpException("La certification associée au certifInfo " . $parameters['certifInfo'] . " n'a pas été trouvée.");
        }

        $data = $paginator->paginate($trainingRepository->findAllReturnQueryBuilder($organism, $parameters), intval($parameters['page']), intval($parameters['limit']));
        $view = $this->view($data->getItems(), 200);
        $view->setHeader("x-total-count", $data->getTotalItemCount());
        $view->setHeader("x-current-page", $data->getCurrentPageNumber());
        $view->setHeader("x-item-per-page", $data->getItemNumberPerPage());
        return $this->handleView($view);
    }

    /**
     * @Rest\Post ("/api/trainings")
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_TRAINING:WRITE')", message="not allowed")
     * @Rest\View(StatusCode = 501)
     *
     * @return WedofBadRequestHttpException
     */
    public function create(): WedofBadRequestHttpException
    {
        throw new WedofBadRequestHttpException("Méthode non supportée - Une nouvelle formation ne peut être créée.");
    }

    /**
     * @Rest\Put("/api/trainings/{externalId}", requirements={"externalId"=".+"})
     * @IsGranted(TrainingVoter::EDIT, subject="training", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @param Training $training
     * @param TrainingService $trainingService
     * @param Request $request
     * @param CertificationPartnerService $certificationPartnerService
     * @param SkillService $skillService
     * @return Training|View
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function update(Training $training, TrainingService $trainingService, Request $request, CertificationPartnerService $certificationPartnerService, SkillService $skillService)
    {
        /* @var $user User */
        $user = $this->getUser();
        $body = json_decode($request->getContent(), true);

        $violations = $this->validateUpdateBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }

        $certification = $training->getCertification();
        $trainingOrganism = $training->getOrganism();
        $certificationPartner = $certificationPartnerService->getByCertificationAndPartner($certification, $trainingOrganism);
        $isCertifier = $certificationPartner->getCertifier() === $user->getMainOrganism();

        if (!empty($body['compliance']) && !$isCertifier) {
            throw new WedofBadRequestHttpException("Erreur, vous n'avez pas les droits pour modifier la conformité de la formation");
        }

        if (!empty($body['skillSets']) && $certification) {
            if ($isCertifier) {
                throw new WedofBadRequestHttpException("Erreur, vous n'avez pas les droits pour modifier les blocs de compétences de la formation");
            }
            if (!$certification->isAllowPartialSkillSets()) {
                throw new WedofBadRequestHttpException('Erreur, les skillSets ne peuvent être déclarés que pour une certification dont l\'enseignement peut être divisé par blocs de compétences.');
            }
            if (!$certificationPartner) {
                throw new WedofBadRequestHttpException('Erreur, les skillSets ne peuvent être déclarés que sur un partenariat existant.');
            }
            $body['skillSets'] = $this->getSkillSets($body['skillSets'], $certificationPartner, $user->getMainOrganism(), $skillService);
        }

        return $trainingService->update($training, $body, $user, $certificationPartner);
    }

    /**
     * @Rest\Delete("/api/trainings/{externalId}", requirements={"externalId"=".+"})
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_TRAINING:WRITE')", message="not allowed")
     * @Rest\View(StatusCode = 501)
     * @param Training $training
     * @return WedofBadRequestHttpException
     */
    public function delete(Training $training): WedofBadRequestHttpException
    {
        throw new WedofBadRequestHttpException("Méthode non supportée - La formation associée à l'id " . $training->getExternalId() . " ne peut être supprimée.");
    }


    //----------------
    // METHODES PRIVES
    //----------------
    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateUpdateBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'allowExtraFields' => true,
            'fields' => [
                'compliance' => new Assert\Optional([new Assert\NotBlank(), new Assert\Choice(TrainingComplianceTypes::valuesStatesToString())]),
                'skillSets' => new Assert\Optional([
                    new Assert\Type('array'),
                    new Assert\All([
                        new Assert\Type('integer')
                    ])
                ])
            ]
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param array $skillSets
     * @param CertificationPartner $certificationPartner
     * @param Organism $currentOrganism
     * @param SkillService $skillService
     * @return array
     */
    private function getSkillSets(array $skillSets, CertificationPartner $certificationPartner, Organism $currentOrganism, SkillService $skillService): array
    {
        $skills = [];
        $certification = $certificationPartner->getCertification();
        foreach ($skillSets as $skillSet) {
            $skill = $skillService->getById($skillSet);
            if (!$skill) {
                throw new WedofBadRequestHttpException("Le skillSet d'ID " . $skillSet . " n'existe pas.");
            }
            if ($skill->getType() !== CertificationSkillType::SKILL_SET()->getValue()) {
                throw new WedofBadRequestHttpException("Le skillSet d'ID " . $skillSet . " n'est pas de type 'skillSet'.");
            }
            if ($skill->getCertification() !== $certification) {
                throw new WedofBadRequestHttpException("La certification liée au skillSet d'ID " . $skillSet . " ne correspond pas à la certification du partenariat.");
            }
            if ($certificationPartner->getPartner() !== $currentOrganism) {
                throw new WedofBadRequestHttpException("Vous devez être le partenaire de ce partenariat pour déclarer des skillSets.");
            }
            if ($certificationPartner->getSkillSets()->count() > 0 && !$certificationPartner->getSkillSets()->contains($skill)) {
                throw new WedofBadRequestHttpException("Le skillSet d'ID " . $skillSet . " n'existe pas sur le partenariat.");
            }
            $skills[] = $skill;
        }
        return $skills;
    }
}
