<?php
// src/Controller/api/InvoiceController.php

namespace App\Controller\api;

use App\Entity\CertificationPartner;
use App\Entity\Invoice;
use App\Exception\WedofAccessDeniedHttpException;
use App\Exception\WedofNotFoundHttpException;
use App\Library\utils\enums\InvoiceStates;
use App\Library\utils\enums\InvoiceTypes;
use App\Library\utils\Tools;
use App\Repository\CertificationPartnerRepository;
use App\Security\Voter\CertificationPartnerVoter;
use App\Service\CertificationPartnerService;
use App\Service\InvoiceService;
use DateTime;
use DateTimeZone;
use Embed\Embed;
use Exception;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcherInterface;
use FOS\RestBundle\View\View;
use Knp\Component\Pager\PaginatorInterface;
use LogicException;
use Nelmio\ApiDocBundle\Annotation as ApiDoc;
use OpenApi\Annotations as OA;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validation;
use Vich\UploaderBundle\Handler\DownloadHandler;


/**
 * Class InvoiceController
 * @package App\Controller\api
 *
 * @OA\Tag(name="Invoice")
 * @ApiDoc\Security(name="accessCode")
 */
class InvoiceController extends AbstractWedofController
{
    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    /**
     * @Rest\Post("/api/invoices/{entityClass}/{entityId}", requirements={"entityClass"="CertificationPartner"})
     * @IsGranted("ROLE_USER", message="not allowed")
     * @Rest\View(StatusCode = 201)
     *
     * @ApiDoc\Areas({"invoices", "default"})
     * @OA\Post(
     *     summary="Créer une nouvelle facture",
     *     description="Permet d'ajouter une nouvelle facture. L'entityId correspond à l'id du partenariat. Via OAuth2, 'certificationPartner:certifierEdit'"
     * )
     * @OA\Response(
     *     response=201,
     *     description="Un json contenant les informations de la facture ajoutée",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/Invoice")
     *     )
     * )
     *
     * @OA\RequestBody(
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/InvoiceCreateBody")
     *     )
     * )
     *
     * @param int|string $entityId
     * @param string $entityClass
     * @param CertificationPartnerService $certificationPartnerService
     * @param InvoiceService $invoiceService
     * @return Invoice | View
     * @throws Exception
     */
    public function create($entityId, string $entityClass, CertificationPartnerService $certificationPartnerService, InvoiceService $invoiceService)
    {
        $body = $this->getData();
        switch ($entityClass) {
            case CertificationPartner::CLASSNAME :
                $entity = $certificationPartnerService->getById($entityId);
                if (!$entity) {
                    throw new WedofNotFoundHttpException("Le partenariat avec l'id " . $entityId . " n'a pas été trouvé.");
                }
                if (!$this->isGranted(CertificationPartnerVoter::CERTIFIER_EDIT, $entity)) {
                    throw new WedofAccessDeniedHttpException("Erreur, vous n'êtes pas autorisé à associer une facture pour ce partenariat");
                }
                break;
            default:
                throw new LogicException("Entity class ne peut être que CertificationPartner !");
        }

        if (isset($body['dueDate']) && strtotime($body['dueDate'])) {
            $body['dueDate'] = (new DateTime($body['dueDate']))
                ->setTime(23, 59, 59)
                ->setTimezone(new DateTimeZone('Europe/Paris'));
        }

        $violations = $this->validateCreateBody($body);

        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }

        return $invoiceService->create($body, $entity);
    }

    /**
     * @Rest\Get("/api/invoices/{id}")
     * @Rest\View(StatusCode = 200)
     * @IsGranted("ROLE_USER", message="not allowed")
     *
     * @ApiDoc\Areas({"invoices", "default"})
     * @OA\Get(
     *     summary="Récupération d'une facture",
     *     description="Récupération d'une facture."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations de la facture",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/Invoice")
     *     )
     * )
     *
     * @OA\Parameter(
     *     name="id",
     *     in="path",
     *     description="Id de la facture",
     *     @OA\Schema(type="string")
     * )
     *
     * @param Invoice $invoice
     * @param CertificationPartnerService $certificationPartnerService
     * @return Invoice
     */
    public function show(Invoice $invoice, CertificationPartnerService $certificationPartnerService): Invoice
    {
        switch ($invoice->getEntityClass()) {
            case CertificationPartner::CLASSNAME :
                $entity = $certificationPartnerService->getById($invoice->getEntityId());
                if (!$entity) {
                    throw new WedofNotFoundHttpException("La facture avec l'id " . $invoice->getId() . " n'a pas été trouvé.");
                }
                if (!$this->isGranted(CertificationPartnerVoter::CERTIFIER_EDIT, $entity)) {
                    throw new WedofAccessDeniedHttpException("Erreur, vous n'êtes pas autorisé à afficher la facture");
                }
                break;
            default:
                throw new LogicException("Entity class ne peut être que CertificationPartner !");
        }
        return $invoice;
    }

    /**
     * @Rest\Get("/api/invoices/{entityClass}/{entityId}", requirements={"entityClass"="CertificationPartner"})
     * @Rest\QueryParam(name="limit", requirements="\d+", default="100", description="Nombre d'éléments retourné par requête - par défaut 100.")
     * @Rest\QueryParam(name="page", requirements="\d+", default="1", description="Numéro de page de la requête - par défaut la première.")
     * @Rest\QueryParam(name="state", requirements=@Assert\Choice({"all", "waitingPayment", "paid", "canceled"}), default="all", description="Permet de n'obtenir que les factures dans l'état considéré - par défaut tous les états sont retournés. Valeurs possibles : 'all', 'waitingPayment', 'paid', 'canceled'. Il est possible de demander plusieurs states en séparant chaque state par une virgule.")
     * @Rest\QueryParam(name="type", requirements=@Assert\Choice({"all", "invoice", "creditNote", "invoiceDeposit"}), default="all", description="Permet de n'obtenir que les factures dans le type considéré - par défaut tous les états sont retournés. Valeurs possibles : 'all', 'invoice', 'creditNote', 'invoiceDeposit'. Il est possible de demander plusieurs states en séparant chaque state par une virgule.")
     * @IsGranted("ROLE_USER", message="not allowed")
     * @ApiDoc\Areas({"invoices", "default"})
     * @OA\Get (
     *     summary="Liste les factures d'un partenariat.",
     *     description="Récupère l'ensemble des factures liées à l'id d'un partenariat."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un tableau des factures au format JSON.",
     *     @OA\JsonContent(
     *          type="array",
     *          @OA\Items(ref="#/components/schemas/Invoice")
     *     )
     * )
     *
     * @param PaginatorInterface $paginator
     * @param int|string $entityId
     * @param string $entityClass
     * @param CertificationPartnerRepository $certificationPartnerRepository
     * @param InvoiceService $invoiceService
     * @param ParamFetcherInterface $paramFetcher
     * @return Response
     */
    public function list(PaginatorInterface $paginator, $entityId, string $entityClass, CertificationPartnerRepository $certificationPartnerRepository, InvoiceService $invoiceService, ParamFetcherInterface $paramFetcher): Response
    {
        if ($entityClass === CertificationPartner::CLASSNAME) {
            $entity = $certificationPartnerRepository->findOneBy(['id' => $entityId]);
            if (!$entity) {
                throw new WedofNotFoundHttpException("Le partenariat " . $entityId . " n'a pas été trouvé.");
            }
            if (!$this->isGranted(CertificationPartnerVoter::VIEW, $entity)) {
                throw new WedofAccessDeniedHttpException("Erreur, vous n'êtes pas autorisé à lister les factures");
            }
        } else {
            throw new LogicException("Entity class ne peut être que CertificationPartner !");
        }
        $parameters = $paramFetcher->all(true);
        $parameters['type'] = $parameters['type'] && $parameters['type'] != 'all' ? $parameters['type'] : null;
        $parameters['state'] = $parameters['state'] && $parameters['state'] != 'all' ? $parameters['state'] : null;
        $data = $paginator->paginate($invoiceService->listByEntity($entityId, $entityClass, $parameters), intval($parameters['page']), intval($parameters['limit']));
        $view = $this->view($data->getItems(), 200);
        $view->setHeader("x-total-count", $data->getTotalItemCount());
        $view->setHeader("x-current-page", $data->getCurrentPageNumber());
        $view->setHeader("x-item-per-page", $data->getItemNumberPerPage());
        return $this->handleView($view);
    }

    /**
     * @Rest\Post("/api/invoices/{id}")
     * @Rest\View(StatusCode = 200)
     * @IsGranted("ROLE_USER", message="not allowed")
     *
     * @ApiDoc\Areas ({"invoices","default"})
     *
     * @OA\Post (
     *     summary="Mettre à jour une facture",
     *     description="Permet de mettre à jour une facture sur un partenariat. L'entityId correspond à l'id du partenariat. Via OAuth2, cet appel nécessite le scope 'certificationPartner:certifierEdit'"
     * )
     * @OA\Response(
     *      response=200,
     *      description="Un json contenant les informations de la facture mise à jour",
     *      @OA\MediaType(mediaType="application/json",
     *          @OA\Schema(ref="#/components/schemas/Invoice")
     *      )
     *  )
     *
     * @OA\RequestBody(
     *      @OA\MediaType(mediaType="application/json",
     *          @OA\Schema(ref="#/components/schemas/InvoiceUpdateBody")
     *      )
     *  )
     *
     * @param Invoice $invoice
     * @param CertificationPartnerService $certificationPartnerService
     * @param InvoiceService $invoiceService
     * @return Invoice | View
     * @throws Exception
     */
    public function update(Invoice $invoice, CertificationPartnerService $certificationPartnerService, InvoiceService $invoiceService)
    {
        $body = $this->getData();
        switch ($invoice->getEntityClass()) {
            case CertificationPartner::CLASSNAME :
                $entity = $certificationPartnerService->getById($invoice->getEntityId());
                if (!$entity) {
                    throw new WedofNotFoundHttpException("La facture avec l'id " . $invoice->getId() . " n'a pas été trouvé.");
                }
                if (!$this->isGranted(CertificationPartnerVoter::CERTIFIER_EDIT, $entity)) {
                    throw new WedofAccessDeniedHttpException("Erreur, vous n'êtes pas autorisé à modifier la facture");
                }
                break;
            default:
                throw new LogicException("Entity class ne peut être que CertificationPartner !");
        }
        if (isset($body['dueDate']) && strtotime($body['dueDate'])) {
            $body['dueDate'] = (new DateTime($body['dueDate']))
                ->setTimezone(new DateTimeZone('Europe/Paris'))
                ->setTime(23, 59, 59);
        }
        $violations = $this->validateUpdateBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }
        return $invoiceService->update($invoice, $body);
    }

    /**
     * @Rest\Get("/api/invoices/{id}/file")
     * @Rest\View(StatusCode = 200)
     *
     * @param Invoice $invoice
     * @param CertificationPartnerService $certificationPartnerService
     * @param DownloadHandler $downloadHandler
     * @return array|string[]|StreamedResponse
     */
    public function download(Invoice $invoice, CertificationPartnerService $certificationPartnerService, DownloadHandler $downloadHandler)
    {
        switch ($invoice->getEntityClass()) {
            case CertificationPartner::CLASSNAME :
                $entity = $certificationPartnerService->getById($invoice->getEntityId());
                if (!$entity) {
                    throw new WedofNotFoundHttpException("La facture avec l'id " . $invoice->getId() . " n'a pas été trouvé.");
                }
                if (!$this->isGranted(CertificationPartnerVoter::VIEW, $entity)) {
                    throw new WedofAccessDeniedHttpException("Erreur, vous n'êtes pas autorisé à télécharger une facture");
                }
                break;
            default:
                throw new LogicException("Entity class ne peut être que CertificationPartner !");
        }
        if ($invoice->getFileType() != 'link') {
            return $downloadHandler->downloadObject($invoice, 'file');
        } else {
            //convert google drive content
            if (Tools::startsWith($invoice->getLink(), 'https://drive.google.com/')
                || Tools::startsWith($invoice->getLink(), 'https://docs.google.com/')) {
                $code = explode('/view', $invoice->getLink());
                $code = isset($code[1]) ? $code[0] : explode('/edit', $invoice->getLink())[0];
                if (!Tools::contains($code, '/folders/')) { //case drive is a folder...
                    $code = '<iframe src="' . $code . '/preview" height="100%" width="100%"></iframe>';
                }
            } else {
                $embed = new Embed();
                try {
                    $code = $embed->get($invoice->getLink())->code;
                } catch (Exception $e) {
                    $code = null;
                }
            }
            if ($code) {
                $code = preg_replace('/height=[\"\'][0-9]+[\"\']/i', 'height="100%"', $code);
                $code = preg_replace('/width=[\"\'][0-9]+[\"\']/i', 'width="100%"', $code);
                return ['html' => $code];
            } else {
                return ['html' => '<div class="w-full"><a href="' . $invoice->getLink() . '" 
                                            class="mat-focus-indicator mat-flat-button mat-button-base mat-primary" target="_blank">
                                            <span class="mat-button-wrapper">Ouvrir le document</span>
                                            <div class="mat-ripple mat-button-ripple"></div>
                                            <div class="mat-button-focus-overlay"></div>
                                        </a></div>'];
            }
        }
    }

    /**
     * @Rest\Post("/api/invoices/{id}/paid")
     * @Rest\View(StatusCode = 200)
     * @IsGranted("ROLE_USER", message="not allowed")
     *
     * @ApiDoc\Areas({"invoices", "default"})
     * @OA\Post(
     *     summary="Marque une facture comme payée",
     *     description="Marque une facture comme payée. Via OAuth2, cet appel nécessite le scope 'certificationPartner:certifierEdit'"
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations de la facture",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/Invoice")
     *     )
     * )
     *
     * @param Invoice $invoice
     * @param CertificationPartnerService $certificationPartnerService
     * @param InvoiceService $invoiceService
     * @return Invoice
     */
    public function paid(Invoice $invoice, CertificationPartnerService $certificationPartnerService, InvoiceService $invoiceService): Invoice
    {
        switch ($invoice->getEntityClass()) {
            case CertificationPartner::CLASSNAME :
                $entity = $certificationPartnerService->getById($invoice->getEntityId());
                if (!$entity) {
                    throw new WedofNotFoundHttpException("La facture avec l'id " . $invoice->getId() . " n'a pas été trouvé.");
                }
                if (!$this->isGranted(CertificationPartnerVoter::CERTIFIER_EDIT, $entity)) {
                    throw new WedofAccessDeniedHttpException("Erreur, vous n'êtes pas autorisé à modifier l'état de la facture");
                }
                return $invoiceService->paid($invoice);
            default:
                throw new LogicException("Entity class ne peut être que CertificationPartner !");
        }
    }

    /**
     * @Rest\Post("/api/invoices/{id}/canceled")
     * @Rest\View(StatusCode = 200)
     * @IsGranted("ROLE_USER", message="not allowed")
     *
     * @ApiDoc\Areas({"invoices", "default"})
     * @OA\Post(
     *     summary="Marque une facture comme annulée",
     *     description="Marque une facture comme annulée. Via OAuth2, cet appel nécessite le scope 'certificationPartner:certifierEdit'"
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations de la facture",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/Invoice")
     *     )
     * )
     *
     * @param Invoice $invoice
     * @param CertificationPartnerService $certificationPartnerService
     * @param InvoiceService $invoiceService
     * @return Invoice
     */
    public function canceled(Invoice $invoice, CertificationPartnerService $certificationPartnerService, InvoiceService $invoiceService): Invoice
    {
        switch ($invoice->getEntityClass()) {
            case CertificationPartner::CLASSNAME :
                $entity = $certificationPartnerService->getById($invoice->getEntityId());
                if (!$entity) {
                    throw new WedofNotFoundHttpException("La facture avec l'id " . $invoice->getId() . " n'a pas été trouvé.");
                }
                if (!$this->isGranted(CertificationPartnerVoter::CERTIFIER_EDIT, $entity)) {
                    throw new WedofAccessDeniedHttpException("Erreur, vous n'êtes pas autorisé à modifier l'état de la facture");
                }
                return $invoiceService->canceled($invoice);
            default:
                throw new LogicException("Entity class ne peut être que CertificationPartner !");
        }
    }

    /**
     * @Rest\Post("/api/invoices/{id}/waitingPayment")
     * @Rest\View(StatusCode = 200)
     * @IsGranted("ROLE_USER", message="not allowed")
     *
     * @ApiDoc\Areas({"invoices", "default"})
     * @OA\Post(
     *     summary="Marque une facture comme en attente de paiement",
     *     description="Marque une facture comme en attente de paiement. Via OAuth2, cet appel nécessite le scope 'certificationPartner:certifierEdit'"
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations de la facture",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/Invoice")
     *     )
     * )
     *
     * @param Invoice $invoice
     * @param CertificationPartnerService $certificationPartnerService
     * @param InvoiceService $invoiceService
     * @return Invoice
     */
    public function waitingPayment(Invoice $invoice, CertificationPartnerService $certificationPartnerService, InvoiceService $invoiceService): Invoice
    {
        switch ($invoice->getEntityClass()) {
            case CertificationPartner::CLASSNAME :
                $entity = $certificationPartnerService->getById($invoice->getEntityId());
                if (!$entity) {
                    throw new WedofNotFoundHttpException("La facture avec l'id " . $invoice->getId() . " n'a pas été trouvé.");
                }
                if (!$this->isGranted(CertificationPartnerVoter::CERTIFIER_EDIT, $entity)) {
                    throw new WedofAccessDeniedHttpException("Erreur, vous n'êtes pas autorisé à modifier l'état de la facture");
                }
                return $invoiceService->waitingPayment($invoice);
            default:
                throw new LogicException("Entity class ne peut être que CertificationPartner !");
        }
    }

    /**
     * @Rest\Delete("/api/invoices/{id}")
     * @Rest\View(StatusCode = 204)
     * @IsGranted("ROLE_USER", message="not allowed")
     *
     * @ApiDoc\Areas({"invoices", "default"})
     * @OA\Delete(
     *     summary="Supprimer une facture",
     *     description="Supprimer une facture. Via OAuth2, cet appel nécessite le scope 'certificationPartner:certifierEdit'"
     * )
     * @OA\Response(
     *     response=204,
     *     description="Aucun contenu retourné."
     * )
     *
     * @param Invoice $invoice
     * @param CertificationPartnerService $certificationPartnerService
     * @param InvoiceService $invoiceService
     */
    public function delete(Invoice $invoice, CertificationPartnerService $certificationPartnerService, InvoiceService $invoiceService)
    {
        switch ($invoice->getEntityClass()) {
            case CertificationPartner::CLASSNAME :
                $entity = $certificationPartnerService->getById($invoice->getEntityId());
                if (!$entity) {
                    throw new WedofNotFoundHttpException("La facture avec l'id " . $invoice->getId() . " n'a pas été trouvé.");
                }
                if (!$this->isGranted(CertificationPartnerVoter::CERTIFIER_EDIT, $entity)) {
                    throw new WedofAccessDeniedHttpException("Erreur, vous n'êtes pas autorisé à supprimer la facture");
                }
                $invoiceService->delete($invoice);
                break;
            default:
                throw new LogicException("Entity class ne peut être que CertificationPartner !");
        }
    }

    //-------------------
    // METHODES PRIVEES
    //-------------------

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateCreateBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'externalId' => new Assert\Required([new Assert\Type('string')]),
            'state' => new Assert\Required([
                new Assert\Choice([
                    InvoiceStates::CANCELED()->getValue(),
                    InvoiceStates::WAITING_PAYMENT()->getValue(),
                    InvoiceStates::PAID()->getValue()
                ])
            ]),
            'type' => new Assert\Required([
                new Assert\Choice([
                    InvoiceTypes::INVOICE()->getValue(),
                    InvoiceTypes::INVOICE_DEPOSIT()->getValue(),
                    InvoiceTypes::CREDIT_NOTE()->getValue()
                ])
            ]),
            'file' => new Assert\Optional([
                new Assert\AtLeastOneOf([
                    new Assert\File(),
                    new Assert\Url()
                ])
            ]),
            'paymentLink' => new Assert\Optional([new Assert\Url()]),
            'dueDate' => new Assert\Optional([new Assert\Type('datetime')]),
            'description' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 5000])]),
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateUpdateBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'externalId' => new Assert\Optional([
                new Assert\NotBlank(),
                new Assert\Type('string')
            ]),
            'state' => new Assert\Optional([
                new Assert\Choice([
                    InvoiceStates::CANCELED()->getValue(),
                    InvoiceStates::WAITING_PAYMENT()->getValue(),
                    InvoiceStates::PAID()->getValue()
                ])
            ]),
            'type' => new Assert\Optional([
                new Assert\Choice([
                    InvoiceTypes::INVOICE()->getValue(),
                    InvoiceTypes::INVOICE_DEPOSIT()->getValue(),
                    InvoiceTypes::CREDIT_NOTE()->getValue()
                ])
            ]),
            'file' => new Assert\Optional([
                new Assert\AtLeastOneOf([
                    new Assert\File(),
                    new Assert\Url()
                ])
            ]),
            'paymentLink' => new Assert\Optional([new Assert\Url()]),
            'dueDate' => new Assert\Optional([new Assert\Type('datetime')]),
            'description' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 5000])]),
        ]);
        return $validator->validate($body, $constraints);
    }
}
