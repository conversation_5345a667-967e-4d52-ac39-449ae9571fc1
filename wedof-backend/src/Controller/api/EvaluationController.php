<?php
// src/Controller/api/EvaluationController.php
namespace App\Controller\api;

use App\Entity\Evaluation;
use App\Entity\User;
use App\Exception\WedofAccessDeniedHttpException;
use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofNotFoundHttpException;
use App\Exception\WedofSubscriptionException;
use App\Repository\EvaluationRepository;
use App\Security\Voter\EvaluationVoter;
use App\Security\Voter\OrganismVoter;
use App\Security\Voter\TrainingActionVoter;
use App\Service\OrganismService;
use App\Service\TrainingActionService;
use Doctrine\Common\Collections\ArrayCollection;
use ErrorException;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcherInterface;
use Knp\Component\Pager\PaginatorInterface;
use Nelmio\ApiDocBundle\Annotation as ApiDoc;
use OpenApi\Annotations as OA;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints as Assert;
use Throwable;

/**
 * Class EvaluationController
 * @package App\Controller\api
 *
 * @OA\Tag(name="Evaluation")
 * @ApiDoc\Security(name="accessCode")
 */
class EvaluationController extends AbstractFOSRestController
{
    //-------------------
    // METHODES PUBLIQUES
    //-------------------
    /**
     * @Rest\Get("/api/evaluations/{id}")
     * @IsGranted(EvaluationVoter::VIEW, subject="evaluation",  message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @OA\Get (
     *     summary="Récupération d'une évaluation.",
     *     description="Récupération d'une évaluation par son ID. Via OAuth2, cet appel nécessite le scope 'evaluation:read'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations d'une évaluation.",
     *     @ApiDoc\Model(type=Evaluation::class))
     *     )
     * )
     * @OA\Parameter(
     *     name="id",
     *     in="path",
     *     description="ID de l'évaluation",
     *     @OA\Schema(type="string")
     * )
     *
     * @param Evaluation $evaluation
     * @return Evaluation
     */
    public function show(Evaluation $evaluation): Evaluation
    {
        return $evaluation;
    }

    /**
     * @Rest\Get("/api/evaluations")
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_EVALUATION:READ')", message="not allowed")
     *
     * @Rest\QueryParam(name="siret", requirements="\d{14}", nullable=true, description="Permet de n'obtenir que les évaluations appartenant à l'organisme de siret considéré - par défaut l'organisme de l'utilisateur courant.")
     * @Rest\QueryParam(name="since", requirements=@Assert\Date(), nullable=true, description="Permet de n'obtenir que les évaluations depuis la date considérée - par défaut aucune restriction de date.")
     * @Rest\QueryParam(name="trainingActionId", requirements=".+", nullable=true, description="Permet de n'obtenir que les évaluations appartenant à l'action de formation considérée - par défaut toutes les actions de formation.")
     * @Rest\QueryParam(name="for", requirements=@Assert\Choice({"organism", "training", "trainingActions", "all"}), default="training",description="Permet de n'obtenir que les évaluations correspondant soit aux formations, soit aux actions de formation, soit à un organisme, soit les 3 - par défaut les évaluations correspondant aux formations.")
     *
     * @Rest\QueryParam(name="limit", requirements="\d+", default="100", description="Nombre d'éléments retourné par requête - par défaut 100.")
     * @Rest\QueryParam(name="page", requirements="\d+", default="1", description="Numéro de page de la requête - par défaut la première.")
     * @Rest\QueryParam(name="order", requirements=@Assert\Choice({"asc", "desc"}), default="desc", description="Tri les résultats par ordre ascendant ou descendant - par défaut descendant.")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"evaluations", "default"})
     * @OA\Get (
     *     summary="Liste toutes les évaluations pour les actions de formations de l'organisme de l'utilisateur courant. Peut aussi lister les évaluations globales d'un organisme.",
     *     description="Récupère l'ensemble des évaluations liées aux actions de formations de l'organisme de l'utilisateur connecté. Permet aussi d'obtenir les évaluations globales d'un organisme - voir le paramètre 'for'. NOTA : les paramètres sont cumulatifs et deux paramètres incompatibles renverront un résultat vide. Via OAuth2, cet appel nécessite le scope 'evaluation:read'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un tableau des évaluations au format JSON",
     *     @OA\JsonContent(
     *          type="array",
     *          @OA\Items(ref=@ApiDoc\Model(type=Evaluation::class))
     *     )
     * )
     * @OA\Parameter (name="order", in="query", @OA\Schema (ref="#/components/schemas/Order"))
     * @OA\Parameter (name="for", in="query", @OA\Schema (ref="#/components/schemas/EvaluationFor"))
     *
     * @param ParamFetcherInterface $paramFetcher
     * @param OrganismService $organismService
     * @param TrainingActionService $trainingActionService
     * @param EvaluationRepository $evaluationRepository
     * @param PaginatorInterface $paginator
     * @return Response
     * @throws ErrorException
     * @throws Throwable
     */
    public function list(ParamFetcherInterface $paramFetcher, OrganismService $organismService, TrainingActionService $trainingActionService, EvaluationRepository $evaluationRepository, PaginatorInterface $paginator): Response
    {
        /* @var $user User */
        $user = $this->getUser();
        if (!$user->getMainOrganism()->getSubscription()->isAllowAnalytics()) {
            throw new WedofSubscriptionException("Erreur, votre abonnement ne permet pas l'accès aux évaluations.");
        }

        $parameters = $paramFetcher->all(true);

        $organism = null;
        if (!empty($parameters['siret'])) {
            $organism = $organismService->getBySiret($parameters['siret']);
            if ($organism) {
                if (!$this->isGranted(OrganismVoter::VIEW, $organism)) {
                    throw new WedofAccessDeniedHttpException("Non autorisé");
                }
            } else {
                throw new WedofNotFoundHttpException("L'organisme associé au siret " . $parameters['siret'] . " n'a pas été trouvé.");
            }
        }

        if (!empty($parameters['trainingActionId'])) {
            $trainingAction = $trainingActionService->getByExternalId($parameters['trainingActionId']);
            if ($trainingAction) {
                if (!$this->isGranted(TrainingActionVoter::VIEW, $trainingAction)) {
                    throw new WedofAccessDeniedHttpException("Non autorisé");
                }
            } else {
                throw new WedofNotFoundHttpException("L'action de formation associée à l'id " . $parameters['trainingActionId'] . " n'a pas été trouvée.");
            }
        }

        $data = $paginator->paginate($evaluationRepository->findAllReturnQueryBuilder($organism ? new ArrayCollection([$organism]) : $user->getOrganisms(), $parameters), intval($parameters['page']), intval($parameters['limit']));
        $view = $this->view($data->getItems(), 200);
        $view->setHeader("x-total-count", $data->getTotalItemCount());
        $view->setHeader("x-current-page", $data->getCurrentPageNumber());
        $view->setHeader("x-item-per-page", $data->getItemNumberPerPage());
        return $this->handleView($view);
    }

    /**
     * @Rest\Post ("/api/evaluations")
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_EVALUATION:WRITE')", message="not allowed")
     *
     * @OA\Post (
     *     summary="Méthode non supportée",
     *     description="Méthode non supportée"
     * )
     * @OA\Response(
     *     response=501,
     *     description="Méthode non supportée"
     * )
     *
     * @return WedofBadRequestHttpException
     */
    public function create(): WedofBadRequestHttpException
    {
        throw new WedofBadRequestHttpException("Méthode non supportée - Une nouvelle évaluation ne peut être créée.");
    }

    /**
     * @Rest\Put("/api/evaluations/{id}", requirements={"externalId"=".+"})
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_EVALUATION:WRITE')", message="not allowed")
     * @Rest\View(StatusCode = 501)
     *
     * @OA\Put (
     *     summary="Méthode non supportée",
     *     description="Méthode non supportée"
     * )
     * @OA\Response(
     *     response=501,
     *     description="Méthode non supportée"
     * )
     * @param Evaluation $evaluation
     * @return WedofBadRequestHttpException
     */
    public function update(Evaluation $evaluation): WedofBadRequestHttpException
    {
        throw new WedofBadRequestHttpException("Méthode non supportée - L'évaluation associée à l'id " . $evaluation->getId() . " ne peut être modifiée.");
    }

    /**
     * @Rest\Delete("/api/evaluations/{id}", requirements={"externalId"=".+"})
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_EVALUATION:WRITE')", message="not allowed")
     * @Rest\View(StatusCode = 501)
     *
     * @OA\Delete  (
     *     summary="Méthode non supportée",
     *     description="Méthode non supportée"
     * )
     * @OA\Response(
     *     response=501,
     *     description="Méthode non supportée"
     * )
     *
     * @param Evaluation $evaluation
     * @return WedofBadRequestHttpException
     */
    public function delete(Evaluation $evaluation): WedofBadRequestHttpException
    {
        throw new WedofBadRequestHttpException("Méthode non supportée - L'évaluation " . $evaluation->getId() . " ne peut être supprimée.");
    }


    //----------------
    // METHODES PRIVES
    //----------------
}
