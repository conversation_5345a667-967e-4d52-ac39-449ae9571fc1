<?php
// src/Controller/api/CertificationFolderSurveyController.php

namespace App\Controller\api;

use App\Entity\CertificationFolderSurvey;
use App\Entity\User;
use App\Exception\WedofAccessDeniedHttpException;
use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofNotFoundHttpException;
use App\Exception\WedofSubscriptionException;
use App\Library\utils\enums\CertificationFolderSurveyExperience;
use App\Library\utils\enums\CertificationFolderSurveyStates;
use App\Library\utils\Tools;
use App\Security\Voter\CertificationFolderSurveyVoter;
use App\Security\Voter\CertificationFolderVoter;
use App\Service\AttendeeExperienceService;
use App\Service\CertificationFolderService;
use App\Service\CertificationFolderSurveyService;
use App\Service\CertificationService;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcherInterface;
use FOS\RestBundle\View\View;
use Knp\Component\Pager\PaginatorInterface;
use Nelmio\ApiDocBundle\Annotation as ApiDoc;
use OpenApi\Annotations as OA;
use PhpOffice\PhpSpreadsheet\Reader\Exception;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx as XlsxReader;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx as XlsxWriter;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validation;
use Throwable;

/**
 * Class CertificationFolderSurveyController
 * @package App\Controller\api
 *
 * @OA\Tag(name="CertificationFolderSurvey")
 * @ApiDoc\Security(name="accessCode")
 */
class CertificationFolderSurveyController extends AbstractWedofController
{
    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    /**
     * @Rest\Get("/api/surveys/{certificationFolderExternalId}")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"surveys", "default"})
     * @OA\Get (
     *     summary="Récupération d'une enquête",
     *     description="Permet de récupérer une enquête associée à un dossier de certification. L'entityId correspond à l'externalId du dossier de certification."
     * )
     * @OA\Response(
     *     response=201,
     *     description="Un json contenant les informations de l'enquête du dossier de certification",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/CertificationFolderSurvey")
     *     )
     * )
     *
     * @OA\Parameter(
     *     name="certificationFolderExternalId",
     *     in="path",
     *     description="ExternalId du dossier de certification",
     *     @OA\Schema(type="string")
     * )
     * @param string $certificationFolderExternalId
     * @param CertificationFolderService $certificationFolderService
     * @return CertificationFolderSurvey|View
     */
    public function show(string $certificationFolderExternalId, CertificationFolderService $certificationFolderService)
    {
        $certificationFolder = $certificationFolderService->getByExternalId($certificationFolderExternalId);
        if (!$certificationFolder) {
            throw new WedofNotFoundHttpException("Le dossier de certification " . $certificationFolderExternalId . " n'a pas été trouvé.");
        }
        if (!$this->isGranted(CertificationFolderSurveyVoter::VIEW, $certificationFolder) && !$this->isGranted(CertificationFolderSurveyVoter::ATTENDEE_VIEW, $certificationFolder)) {
            throw new WedofAccessDeniedHttpException("Erreur, vous n'avez pas les droits pour accéder à l'enquête associée au dossier de certification");
        }
        return $certificationFolder->getSurvey();
    }

    /**
     * @Rest\Get("/api/surveys")
     * @IsGranted("ROLE_USER", message="not allowed")
     * @Rest\QueryParam(name="certifInfo", requirements=@Assert\Type("string"), nullable=true, description="Permet de n'obtenir que les enquêtes liées à la certification considérée - par défaut toutes les enquêtes de toutes les certifications sont retournées.")
     * @Rest\QueryParam(name="state", requirements=@Assert\Type("string"), default="all", description="Permet de n'obtenir que les enquêtes en fonction de l'état considéré - par défaut tous les états sont retournés. Valeurs possibles : 'all', 'created', 'beforeCertificationSuccess', 'afterSixMonthsCertificationSuccess', 'finished'. Il est possible de demander plusieurs états en séparant chaque état par une virgule, ex : 'afterSixMonthsCertificationSuccess,finished'.")
     * @Rest\QueryParam(name="limit", requirements="\d+", default="100", description="Nombre d'éléments retourné par requête - par défaut 100.")
     * @Rest\QueryParam(name="page", requirements="\d+", default="1", description="Numéro de page de la requête - par défaut la première.")
     * @Rest\QueryParam(name="order", requirements=@Assert\Choice({"asc", "desc"}), default="desc", description="Tri les résultats par ordre ascendant ou descendant - par défaut descendant.")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"surveys", "default"})
     * @OA\Get (
     *     summary="Liste les enquêtes selon des critères.",
     *     description="Récupérer l'ensemble des enquêtes de l'organisme de l'utilisateur connecté."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un tableau des enquêtes au format JSON",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/CertificationFolderSurvey")
     *     )
     * )
     *
     * @OA\Parameter (name="order", in="query", @OA\Schema (ref="#/components/schemas/Order"))
     *
     * @param PaginatorInterface $paginator
     * @param ParamFetcherInterface $paramFetcher
     * @param Request $request
     * @param CertificationService $certificationService
     * @param CertificationFolderSurveyService $certificationFolderSurveyService
     * @return Response
     */
    public function list(PaginatorInterface $paginator, ParamFetcherInterface $paramFetcher, Request $request, CertificationService $certificationService, CertificationFolderSurveyService $certificationFolderSurveyService): Response
    {
        /* @var $user User */
        $user = $this->getUser();
        $parameters = $paramFetcher->all(true);

        $allowedCertifications = $certificationService->listReturnQueryBuilder($user->getMainOrganism(), ['organismType' => 'all'])->getQuery()->getResult();
        $parameters['certifications'] = array_map(function ($o) {
            return $o->getCertifInfo();
        }, $allowedCertifications);

        if (isset($parameters['certifInfo'])) {
            $certifications = explode(',', $parameters['certifInfo']);
            foreach ($certifications as $certification) {
                if (!in_array($certification, $parameters['certifications'])) {
                    throw new WedofBadRequestHttpException("Erreur, vous n'avez pas accès à cette certification");
                }
            }
            $parameters['certifications'] = $certifications;
        }

        if (isset($parameters['state'])) {
            $parameters['state'] = explode(",", $parameters['state']);
            foreach ($parameters['state'] as $state) {
                if (!in_array($state, CertificationFolderSurveyStates::valuesStates())) {
                    throw new WedofBadRequestHttpException("Erreur sur les valeurs renvoyées 'state', elles doivent être : " . join(",", CertificationFolderSurveyStates::valuesStates()) . ".");
                }
            }
        }

        $data = $paginator->paginate($certificationFolderSurveyService->listByEntityReturnQueryBuilder($parameters), intval($parameters['page']), intval($parameters['limit']));
        $view = $this->view($data->getItems(), 200);
        $view->setHeader("x-total-count", $data->getTotalItemCount());
        $view->setHeader("x-current-page", $data->getCurrentPageNumber());
        $view->setHeader("x-item-per-page", $data->getItemNumberPerPage());
        return $this->handleView($view);
    }

    /**
     * @Rest\Put("/api/surveys/{certificationFolderExternalId}")
     * @IsGranted("ROLE_ATTENDEE", message="not allowed")
     * @Rest\View(StatusCode = 200)
     * @param string $certificationFolderExternalId
     * @param CertificationFolderService $certificationFolderService
     * @param CertificationFolderSurveyService $certificationFolderSurveyService
     * @param AttendeeExperienceService $attendeeExperienceService
     * @return CertificationFolderSurvey|View
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function update(string $certificationFolderExternalId, CertificationFolderService $certificationFolderService, CertificationFolderSurveyService $certificationFolderSurveyService, AttendeeExperienceService $attendeeExperienceService)
    {
        $body = $this->getData();

        $certificationFolder = $certificationFolderService->getByExternalId($certificationFolderExternalId);
        if (!$certificationFolder) {
            throw new WedofNotFoundHttpException("Le dossier de certification " . $certificationFolderExternalId . " n'a pas été trouvé.");
        }
        if (!$this->isGranted(CertificationFolderVoter::ATTENDEE_EDIT, $certificationFolder)) {
            throw new WedofAccessDeniedHttpException("Erreur, vous n'avez pas les droits pour accéder à l'enquête associée au dossier de certification");
        }

        $violations = $this->validateUpdateSurveyBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }

        $attendeeExperienceId = $body['experience']['attendeeExperienceId'];
        $certificationFolderSurvey = $certificationFolder->getSurvey();
        $attendeeExperience = $attendeeExperienceService->getById($attendeeExperienceId);
        if (!$attendeeExperience) {
            throw new WedofBadRequestHttpException("Erreur, l'expérience associée à l'id " . $attendeeExperienceId . " n'a pas été trouvée.");
        }
        $surveyName = $body['experience']['name'];
        $certificationFolderSurveyService->update($certificationFolderSurvey, $attendeeExperience, $surveyName);
        return $certificationFolderSurvey;
    }

    /**
     * @Rest\Get("/api/surveys/export/{certifInfo}")
     * @IsGranted("ROLE_USER", message="not allowed")
     * @Rest\View(StatusCode = 200)
     * @param string $certifInfo
     * @param CertificationService $certificationService
     * @param CertificationFolderSurveyService $certificationFolderSurveyService
     * @param EntityManagerInterface $entityManager
     * @return Response
     * @throws Exception
     * @throws WedofSubscriptionException
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     */
    public function export(string $certifInfo, CertificationService $certificationService, CertificationFolderSurveyService $certificationFolderSurveyService, EntityManagerInterface $entityManager): Response
    {
        /* @var $user User */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();
        $certification = $certificationService->getByCertifInfo($certifInfo);
        if (!$certification) {
            throw new WedofBadRequestHttpException("Erreur, la certification avec le certifInfo " . $certifInfo . " n'existe pas.");
        }
        if (!$certification->isCertifier($organism)) {
            throw new WedofBadRequestHttpException("Erreur, vous n'avez pas accès à la certification associée au certifInfo " . $certifInfo . ".");
        }
        $subscription = $organism->getSubscription();
        if (!$subscription->isAllowCertifierPlus()) {
            throw new WedofSubscriptionException("Erreur, cette fonctionnalité est limitée aux abonnements Certificateur");
        }
        $parameters = ['toExport' => true, 'organism' => $organism, 'certificationToExport' => $certification];
        $surveysData = $certificationFolderSurveyService->generateDataToExport($parameters);
        return $this->createFranceCompetenceFile($surveysData, $entityManager);
    }

    /**
     * @Rest\Get("/api/surveys/details/{certifInfo}")
     * @IsGranted("ROLE_USER", message="not allowed")
     * @Rest\View(StatusCode = 200)
     * @param string $certifInfo
     * @param CertificationService $certificationService
     * @param CertificationFolderSurveyService $certificationFolderSurveyService
     * @return array
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function details(string $certifInfo, CertificationService $certificationService, CertificationFolderSurveyService $certificationFolderSurveyService): array
    {
        /* @var $user User */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();
        $certification = $certificationService->getByCertifInfo($certifInfo);
        if (!$certification) {
            throw new WedofBadRequestHttpException("Erreur, la certification avec le certifInfo " . $certifInfo . " n'existe pas.");
        }
        if (!$certification->isCertifier($organism)) {
            throw new WedofBadRequestHttpException("Erreur, vous n'avez pas accès à la certification associée au certifInfo " . $certifInfo . ".");
        }
        return $certificationFolderSurveyService->details($certification);
    }

    //----------------
    // METHODES PRIVES
    //----------------

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateUpdateSurveyBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            "experience" => new Assert\Required([
                new Assert\Collection([
                    'name' => new Assert\Required([new Assert\Choice([CertificationFolderSurveyExperience::INITIAL_EXPERIENCE()->getValue(), CertificationFolderSurveyExperience::SIX_MONTH_EXPERIENCE()->getValue(), CertificationFolderSurveyExperience::LONG_TERM_EXPERIENCE()->getValue()])]),
                    'attendeeExperienceId' => new Assert\Required(new Assert\Type('integer')),
                ])
            ]),
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param array $surveysData
     * @param EntityManagerInterface $entityManager
     * @return Response
     * @throws Exception
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     */
    private function createFranceCompetenceFile(array $surveysData, EntityManagerInterface $entityManager): Response
    {
        set_time_limit(300);
        $page = 1;
        $limit = 100;
        $nbPages = 1; // initialized to 1 for first page, then updated according to dynamic data
        $entityManager->getConnection()->getConfiguration()->setSQLLogger(); // for perf in dev, maybe in prod ?

        $reader = new XlsxReader();
        $templateFileName = $this->getParameter('kernel.project_dir') . '/data/FranceCompetence.xlsx';
        $spreadsheet = $reader->load($templateFileName);
        $worksheet = $spreadsheet->getSheet(1);
        $rowIndex = 6;

        while ($page <= $nbPages) {
            foreach ($surveysData as $survey) {
                $row = $survey;
                $columnIndex = 1;
                foreach ($row as $columnName => $cellValue) {
                    $coordinates = [$columnIndex, $rowIndex];
                    $worksheet->setCellValue($coordinates, $cellValue);
                    $columnIndex++;
                }
                $rowIndex++;
            }
            if ($page === 1) {
                $nbPages = intdiv(count($surveysData), $limit) + 1;
            }
            $page++;
            $entityManager->clear();
        }

        $writer = new XlsxWriter($spreadsheet);
        return Tools::getExcelResponse($writer, 'FranceCompetence');
    }
}
