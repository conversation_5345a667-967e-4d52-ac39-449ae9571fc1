<?php
// src/Controller/api/AttendeeController.php

namespace App\Controller\api;

use App\Entity\Attendee;
use App\Entity\AttendeeExperience;
use App\Entity\RegistrationFolder;
use App\Entity\User;
use App\Exception\WedofAccessDeniedHttpException;
use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofNotFoundHttpException;
use App\Library\utils\enums\ActivityTypes;
use App\Library\utils\enums\AttendeeGender;
use App\Library\utils\enums\ContractType;
use App\Library\utils\enums\SituationCertification;
use App\Library\utils\Tools;
use App\Security\Voter\AttendeeVoter;
use App\Security\Voter\CertificationFolderVoter;
use App\Service\ActivityService;
use App\Service\AttendeeExperienceService;
use App\Service\AttendeeService;
use App\Service\CertificationFolderService;
use App\Service\RegistrationFolderService;
use DateTime;
use DateTimeZone;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Exception;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcherInterface;
use FOS\RestBundle\View\View;
use Knp\Component\Pager\PaginatorInterface;
use Nelmio\ApiDocBundle\Annotation as ApiDoc;
use OpenApi\Annotations as OA;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validation;
use Throwable;
use Vich\UploaderBundle\Storage\StorageInterface;

/**
 * Class AttendeeController
 * @package App\Controller\api
 *
 * @OA\Tag(name="Attendee")
 * @ApiDoc\Security(name="accessCode")
 */
class AttendeeController extends AbstractWedofController
{
    //--------------
    // APP ENDPOINTS
    //--------------
    /**
     * @Rest\Get("/app/attendees/me")
     * @IsGranted("ROLE_ATTENDEE", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     */
    public function me(AttendeeService $attendeeService): Attendee
    {
        /** @var Attendee $user */
        $user = $this->getUser();
        return $attendeeService->getById(intval($user->getUserIdentifier()));
    }

    /**
     * @Rest\Put("/app/public/candidate/passport/{certificationFolderExternalId}/checkMatching")
     * @Rest\View(StatusCode = 200)
     *
     * @param AttendeeService $attendeeService
     * @param CertificationFolderService $certificationFolderService
     * @param string $certificationFolderExternalId
     * @return bool|View
     * @throws Exception
     */
    public function checkMatching(AttendeeService $attendeeService, CertificationFolderService $certificationFolderService, string $certificationFolderExternalId)
    {
        $certificationFolder = $certificationFolderService->getByExternalId($certificationFolderExternalId);
        if (!$certificationFolder || !$certificationFolder->getAttendee()) {
            throw new WedofNotFoundHttpException("L'apprenant associé au dossier de certification " . $certificationFolderExternalId . " n'a pas été trouvé.");
        }
        $attendee = $certificationFolder->getAttendee();
        $body = $this->getData();
        if (!empty($body['dateOfBirth'])) {
            if (strtotime($body['dateOfBirth'])) {
                $body['dateOfBirth'] = (new DateTime($body['dateOfBirth']))->setTimezone(new DateTimeZone('Europe/Paris'));
            }
        }
        $violations = $this->validateUpdateBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }
        return $attendeeService->isAttendeeMatching($body, $attendee);
    }

    /**
     * @Rest\Get("/app/attendees/{id}/identificationDocument")
     * @Security("is_granted('ROLE_ADMIN') or is_granted('ROLE_ALLOWED_TO_SWITCH') or is_granted('IS_IMPERSONATOR')", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @param Attendee $attendee
     * @param StorageInterface $storage
     * @return StreamedResponse
     */
    public function identificationDocument(Attendee $attendee, StorageInterface $storage): StreamedResponse
    {
        if ($attendee->getIdentificationDocument()) {
            $type = pathinfo($attendee->getIdentificationDocumentName(), PATHINFO_EXTENSION);
            $fileName = 'identificationDocument-' . iconv('ISO-8859-1', 'ASCII//TRANSLIT', $attendee->getFirstName()) . '-' . iconv('ISO-8859-1', 'ASCII//TRANSLIT', $attendee->getLastName()) . '.' . $type;
            $stream = $storage->resolveStream($attendee, 'identificationDocumentFile');
            $response = new StreamedResponse(static function () use ($stream): void {
                stream_copy_to_stream($stream, fopen('php://output', 'wb'));
            });
            $disposition = $response->headers->makeDisposition(
                ResponseHeaderBag::DISPOSITION_ATTACHMENT,
                $fileName
            );
            $response->headers->set('Content-Disposition', $disposition);
            $response->headers->set('Content-Type', $type === 'pdf' ? 'application/pdf' : 'image/' . $type);
            return $response;
        } else {
            throw new WedofNotFoundHttpException();
        }
    }

    //-------------------
    // METHODES PUBLIQUES
    //-------------------
    /**
     * @param Attendee $attendee
     * @return Attendee
     * @deprecated
     * @Rest\Get("/api/attendees/{phoneNumber}", requirements={"phoneNumber" = "\d{10}"},  name="byPhoneNumber")
     * @IsGranted(AttendeeVoter::VIEW, subject="attendee", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"attendees", "default"})
     * @OA\Get (
     *     summary="Récupération d'un apprenant.",
     *     description="Récupération d'un apprenant par son numéro de téléphone de portable. !! Attention il est possible qu'il existe plus d'un apprenant avec ce numéro de téléphone. Pour obtenir les données de l'apprenant que vous requêtez, préférez le endpoint '/api/attendees/{id}'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations de l'apprenant recherché",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/Attendee")
     *     )
     * )
     *
     * @OA\Parameter(
     *     name="phoneNumber",
     *     in="path",
     *     description="Téléphone portable de l'apprenant",
     *     @OA\Schema(type="string")
     * )
     *
     *
     */
    public function showByPhoneNumber(Attendee $attendee): Attendee
    {
        // Par défaut Symfony prendra le premier apprenant qu'il trouve avec le numéro de téléphone considéré
        return $attendee;
    }

    /**
     * @param Attendee $attendee
     * @return Attendee
     * @deprecated
     * @Rest\Get("/api/attendees/{email}", requirements={"email" = ".*@{1}.*"}, name="byEmail")
     * @IsGranted(AttendeeVoter::VIEW, subject="attendee", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"attendees", "default"})
     * @OA\Get (
     *     summary="Récupération d'un apprenant.",
     *     description="Récupération d'un apprenant par son email. !! Attention il est possible qu'il existe plus d'un apprenant avec cette adresse email. Pour obtenir les données de l'apprenant que vous requêtez, préférez le endpoint '/api/attendees/{id}'. Si vous ne connaissez pas l'ID, utilisez la méthode liste /api/attendees?query={email}."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations de l'apprenant recherché",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/Attendee")
     *     )
     * )
     *
     * @OA\Parameter(
     *     name="email",
     *     in="path",
     *     description="Email de l'apprenant",
     *     @OA\Schema(type="string")
     * )
     *
     *
     */
    public function showByEmail(Attendee $attendee): Attendee
    {
        // Par défaut Symfony prendra le premier apprenant qu'il trouve avec l'email considéré
        return $attendee;
    }

    /**
     * @Rest\Get("/api/attendees/{id}", requirements={"id" = "\d+"}, name="showAttendeeById")
     * @IsGranted(AttendeeVoter::VIEW, subject="attendee", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"attendees", "default"})
     * @OA\Get (
     *     summary="Récupération d'un apprenant.",
     *     description="Récupération d'un apprenant par son identifiant unique."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations de l'apprenant recherché",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/Attendee")
     *     )
     * )
     *
     * @OA\Parameter(
     *     name="id",
     *     in="path",
     *     description="identifiant unique de l'apprenant",
     *     @OA\Schema(type="string")
     * )
     *
     * @param Attendee $attendee
     * @return Attendee
     */
    public function showById(Attendee $attendee): Attendee
    {
        return $attendee;
    }

    /**
     * @Rest\Get ("/app/attendees/find")
     * @Rest\Get ("/api/attendees/find")
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_ATTENDEE:READ')", message="not allowed")
     *
     * @Rest\QueryParam(name="query", requirements=@Assert\Type("string"), nullable=true, description="Permet d'effectuer une recherche libre sur les champs 'phoneNumber' et 'email'.")
     * @Rest\View (StatusCode = 200)
     *
     * @param ParamFetcherInterface $paramFetcher
     * @param AttendeeService $attendeeService
     * @return ArrayCollection
     */
    public function find(ParamFetcherInterface $paramFetcher, AttendeeService $attendeeService): ArrayCollection
    {
        // TODO remove when we have attendees embedded into folders
        // This method returns attendee regardless of Organism, because if we are organism1 and we want to create a folder on an attendee
        // that was already in the database because organism2 has a folder on it, we want to be able to create it anyway, and the only way to do so
        // is to retrieve the attendee coming from the other organism
        // This is BAD! This method should be removed at some point!
        // It should NOT be exposed in the API documentation
        // It returns an array with One or Zero elements because it is consumed by search fields in the UI, that expect arrays
        // also if no user found, returning empty array is cleaner than returning null (can we return just nothing & status 204?)
        $parameters = $paramFetcher->all(true);
        return $attendeeService->listByEmailOrPhone($parameters);
    }

    /**
     * @Rest\Get ("/api/attendees")
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_ATTENDEE:READ')", message="not allowed")
     *
     * @Rest\QueryParam(name="query", requirements=@Assert\Type("string"), nullable=true, description="Permet d'effectuer une recherche libre sur les champs 'prénom', 'nom' et 'email'.")
     * @Rest\QueryParam(name="sort", requirements=@Assert\Choice({"lastName", "firstName"}), default="lastName", description="Tri les résultats sur un critère. Valeurs possibles: 'lastName', 'firstName'.")
     * @Rest\QueryParam(name="order", requirements=@Assert\Choice({"asc", "desc"}), default="asc", description="Tri les résultats par ordre ascendant ou descendant - par défaut ascendant.")
     *
     * @Rest\QueryParam(name="limit", requirements="\d+", default="100", description="Nombre d'éléments retourné par requête - par défaut 100.")
     * @Rest\QueryParam(name="page", requirements="\d+", default="1")
     * @Rest\View (StatusCode = 200)
     *
     * @ApiDoc\Areas({"attendees", "default"})
     * @OA\Get (
     *     summary="Liste tous les apprenants pour l'organisme de l'utilisateur courant",
     *     description="Récupère l'ensemble des apprenants de l'organisme de l'utilisateur connecté."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un tableau d'apprenants au format JSON",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/Attendee")
     *     )
     * )
     *
     * @OA\Parameter (name="order", in="query", @OA\Schema (ref="#/components/schemas/Order"))
     *
     * @param ParamFetcherInterface $paramFetcher
     * @param PaginatorInterface $paginator
     * @param AttendeeService $attendeeService
     * @return Response
     */
    public function list(ParamFetcherInterface $paramFetcher, PaginatorInterface $paginator, AttendeeService $attendeeService): Response
    {
        /** @var User $user */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();

        $parameters = $paramFetcher->all(true);

        $data = $paginator->paginate($attendeeService->listReturnQueryBuilder($organism, $parameters), intval($parameters['page']), intval($parameters['limit']));
        $view = $this->view($data->getItems(), 200);
        $view->setHeader("x-total-count", $data->getTotalItemCount());
        $view->setHeader("x-current-page", $data->getCurrentPageNumber());
        $view->setHeader("x-item-per-page", $data->getItemNumberPerPage());
        return $this->handleView($view);
    }

    /**
     * @Rest\Post ("/api/attendees")
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_ATTENDEE:WRITE')", message="not allowed")
     * @Rest\View(StatusCode = 201)
     *
     * @ApiDoc\Areas({"attendees", "default"})
     * @OA\Post (
     *     summary="Créer un nouvel apprenant",
     *     description="Permet de créer un nouvel apprenant. Via OAuth2, cet appel nécessite le scope 'attendee:write'"
     * )
     * @OA\Response(
     *     response=201,
     *     description="Un json contenant les informations du nouvel apprenant",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/Attendee")
     *     )
     * )
     * @OA\RequestBody(
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/AttendeeCreateBody")
     *     )
     * )
     *
     *
     * @param AttendeeService $attendeeService
     * @return Attendee|View
     * @throws Throwable
     */
    public function create(AttendeeService $attendeeService)
    {
        $body = $this->getData();
        if (!empty($body['dateOfBirth'])) {
            if (strtotime($body['dateOfBirth'])) {
                $body['dateOfBirth'] = (new DateTime($body['dateOfBirth']))->setTimezone(new DateTimeZone('Europe/Paris'));
            }
        }


        if (!empty($body['nameCityOfBirth']) && !isset($body['codeCityOfBirth'])) {
            throw new WedofBadRequestHttpException("Erreur, veuillez renseigner l'attribut 'codeCityOfBirth' correspondant au code Insee de la ville de naissance.");
        }
        if (!empty($body['codeCityOfBirth']) && !isset($body['nameCityOfBirth'])) {
            throw new WedofBadRequestHttpException("Erreur, veuillez renseigner l'attribut 'nameCityOfBirth' correspondant à la ville de naissance.");
        }
        if (!empty($body['codeCountryOfBirth']) && !isset($body['nameCountryOfBirth'])) {
            throw new WedofBadRequestHttpException("Erreur, veuillez renseigner l'attribut 'nameCountryOfBirth' correspondant au pays de naissance.");
        }
        if (!empty($body['nameCountryOfBirth']) && !isset($body['codeCountryOfBirth'])) {
            throw new WedofBadRequestHttpException("Erreur, veuillez renseigner l'attribut 'codeCountryOfBirth' correspondant au code COG du pays de naissance.");
        }
        if (isset($body['codeCountryOfBirth'], $body['nameCountryOfBirth']) && isset($body['nameCityOfBirth'], $body['codeCityOfBirth'])) {
            if ($body['nameCountryOfBirth'] !== 'France' || $body['codeCountryOfBirth'] !== 100) {
                throw new WedofBadRequestHttpException("Erreur, veuillez renseigner les attributs soit d'une ville Française de naissance (nameCityOfBirth, codeCityOfBirth) soit d'un pays de naissance à l'étranger (nameCountryOfBirth, codeCountryOfBirth).");
            }
        }
        if (!empty($body['nameCountryOfBirth']) && $body['nameCountryOfBirth'] === 'France' && !isset($body['nameCityOfBirth'], $body['codeCityOfBirth'])) {
            throw new WedofBadRequestHttpException("Erreur, pour un apprenant né en France vous devez renseigner l'attribut 'nameCityOfBirth' (ville de naissance) et l'attribut 'codeCityOfBirth' (code Insee de la ville de naissance).");
        }

        $violations = $this->validateCreateBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }
        if ($attendeeService->getByEmail($body['email'])) {
            throw new WedofBadRequestHttpException("Erreur, l'apprenant associé à l'email " . $body['email'] . " est déjà créé.");
        }
        if (isset($body['phoneNumber']) && $attendeeService->getByPhone($body['phoneNumber'])) {
            throw new WedofBadRequestHttpException("Erreur, l'apprenant associé au numéro de téléphone " . $body['phoneNumber'] . " est déjà créé.");
        }
        if (!empty($body['address'])) {
            $body['address'] = $this->constructAttendeeAddress($body['address']);
        }
        if (isset($body['nir'])) {
            $this->validateNir($body);
        }
        return $attendeeService->createOrUpdate($body);
    }

    /**
     * @Rest\Route("/api/attendees/{id}", methods={"PUT","POST"})
     * @Rest\Post("/api/attendees/{entityClass}/{entityId}/updateIdentificationData", requirements={"entityClass"="RegistrationFolder|CertificationFolder"})
     * @Rest\Post("/app/public/attendees/{entityClass}/{entityId}/updateIdentificationData", requirements={"entityClass"="RegistrationFolder|CertificationFolder"})
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"attendees", "default"})
     * @OA\Put (
     *     path="/api/attendees/{id}",
     *     summary="Modifier les données d'un apprenant",
     *     description="Permet de modifier les données d'un apprenant. Via OAuth2, cet appel nécessite le scope 'attendee:write'"
     * )
     * @OA\Post (
     *     path="/api/attendees/{id}",
     *     summary="Modifier les données d'un apprenant",
     *     description="Permet de modifier les données d'un apprenant. Via OAuth2, cet appel nécessite le scope 'attendee:write'"
     * )
     * @OA\Post (
     *     path="/api/attendees/{entityClass}/{entityId}/updateIdentificationData",
     *     summary="Modifier les données d'un apprenant depuis une entité",
     *     description="Permet de modifier les données d'un apprenant. Via OAuth2, cet appel nécessite le scope 'attendee:write'"
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations de l'apprenant modifié",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/Attendee")
     *     )
     * )
     * @OA\RequestBody(
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/AttendeeUpdateBody")
     *     )
     * )
     *
     *
     *
     * @param Attendee|null $attendee
     * @param AttendeeService $attendeeService
     * @param CertificationFolderService $certificationFolderService
     * @param RegistrationFolderService $registrationFolderService
     * @param ActivityService $activityService
     * @param string $entityClass
     * @param string $entityId
     * @return Attendee|View
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function update(?Attendee $attendee, AttendeeService $attendeeService, CertificationFolderService $certificationFolderService, RegistrationFolderService $registrationFolderService, ActivityService $activityService, string $entityClass, string $entityId)
    {
        $userOrAttendeeOrNull = $this->getUser();
        $isAttendeeUser = !($userOrAttendeeOrNull instanceof User);
        if (empty($attendee)) {
            if ($entityClass === RegistrationFolder::CLASSNAME) {
                $entity = $registrationFolderService->getByExternalId($entityId);
            } else {
                $entity = $certificationFolderService->getByExternalId($entityId);
            }
            if (!$entity || !$entity->getAttendee()) {
                throw new WedofNotFoundHttpException("L'apprenant associé au dossier " . $entityId . " n'a pas été trouvé.");
            }
            $attendee = $entity->getAttendee();
        }
        if ($isAttendeeUser && !empty($userOrAttendeeOrNull) && $userOrAttendeeOrNull !== $attendee) {
            throw new WedofAccessDeniedHttpException("Erreur, vous n'êtes pas autorisé à modifier un autre apprenant / candidat");
        }
        if (!$isAttendeeUser && !$this->isGranted(AttendeeVoter::EDIT, $attendee)) {
            throw new WedofAccessDeniedHttpException("Erreur, vous n'êtes pas autorisé à modifier cet apprenant/candidat");
        }
        if ($attendee->isReadOnly()) {
            throw new WedofBadRequestHttpException("Erreur, l'apprenant/candidat associé à l'adresse email " . $attendee->getEmail() . " ne peut être modifié car il a été accroché grâce à ses données personnelles.");
        }

        $body = $this->getData();
        if (!empty($body['dateOfBirth'])) {
            if (strtotime($body['dateOfBirth'])) {
                $body['dateOfBirth'] = (new DateTime($body['dateOfBirth']))->setTimezone(new DateTimeZone('Europe/Paris'));
            }
        }

        $codeCityOfBirth = array_key_exists('codeCityOfBirth', $body) ? $body['codeCityOfBirth'] : $attendee->getCodeCityOfBirth();
        $nameCityOfBirth = array_key_exists('nameCityOfBirth', $body) ? $body['nameCityOfBirth'] : $attendee->getNameCityOfBirth();
        $nameCountryOfBirth = array_key_exists('nameCountryOfBirth', $body) ? $body['nameCountryOfBirth'] : $attendee->getNameCountryOfBirth();
        $codeCountryOfBirth = array_key_exists('codeCountryOfBirth', $body) ? $body['codeCountryOfBirth'] : $attendee->getCodeCountryOfBirth();
        if (!empty($body['codeCityOfBirth']) || !empty($body['nameCityOfBirth'])) {
            if (!$nameCityOfBirth) {
                throw new WedofBadRequestHttpException("Erreur, veuillez renseigner l'attribut 'nameCityOfBirth' correspondant à la ville de naissance.");
            }
            if (!$codeCityOfBirth) {
                throw new WedofBadRequestHttpException("Erreur, veuillez renseigner l'attribut 'codeCityOfBirth' correspondant au code Insee de la ville de naissance.");
            }
            if ($nameCountryOfBirth || $codeCountryOfBirth || !empty($body['nameCountryOfBirth']) || !empty($body['codeCountryOfBirth'])) {
                throw new WedofBadRequestHttpException("Erreur, vous ne pouvez pas ajouter les champs 'codeCityOfBirth' ou 'nameCityOfBirth' si les champs 'nameCountryOfBirth' ou 'codeCountryOfBirth' sont déjà remplis.");
            }
        }
        if (!empty($body['codeCountryOfBirth']) || !empty($body['nameCountryOfBirth'])) {
            if (!$nameCountryOfBirth) {
                throw new WedofBadRequestHttpException("Erreur, veuillez renseigner l'attribut 'nameCountryOfBirth' correspondant au pays de naissance.");
            }
            if (!$codeCountryOfBirth) {
                throw new WedofBadRequestHttpException("Erreur, veuillez renseigner l'attribut 'codeCountryOfBirth' correspondant au code COG du pays de naissance.");
            }
            if ($nameCityOfBirth || $codeCityOfBirth || !empty($body['codeCityOfBirth']) || !empty($body['nameCityOfBirth'])) {
                throw new WedofBadRequestHttpException("Erreur, vous ne pouvez pas ajouter les champs 'nameCountryOfBirth' ou 'codeCountryOfBirth' si les champs 'codeCityOfBirth' ou 'nameCityOfBirth' sont déjà remplis.");
            }
        }

        $violations = $this->validateUpdateBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }

        if (!empty($body['address'])) {
            $body['address'] = $this->constructAttendeeAddress($body['address']);
        }

        $needsAttendeeMatching = $isAttendeeUser || !$attendeeService->canUpdateAttendeeManually($attendee);
        if ($needsAttendeeMatching && !$attendeeService->isAttendeeMatching($body, $attendee)) {
            if ($isAttendeeUser) {
                throw new WedofBadRequestHttpException("Les données envoyées sont trop éloignées par rapport à votre dossier.");
            } else {
                throw new WedofBadRequestHttpException("Les données envoyées pour l'apprenant associé au dossier " . $entityId . " sont trop éloignées.");
            }
        }

        if ($isAttendeeUser) {
            if (isset($body['email']) && !empty($body['email'])) {
                if ($attendee->isEmailValidated()) {
                    unset($body['email']);
                } else {
                    $body['emailValidated'] = true;
                }
            }
            if (isset($body['phoneNumber']) && !empty($body['phoneNumber'])) {
                if ($attendee->isPhoneNumberValidated()) {
                    unset($body['phoneNumber']);
                } else {
                    $body['phoneNumberValidated'] = true;
                }
            }
        } else if (isset($body['email']) && $attendee->isEmailValidated()) {
            unset($body['email']);
        } else if (isset($body['phoneNumber']) && $attendee->isPhoneNumberValidated()) {
            unset($body['phoneNumber']);
        }

        if (isset($body['retrievedNir'])) {
            if ($attendee->isNirValidated() || (!$isAttendeeUser && !($this->isGranted(CertificationFolderVoter::EDIT, $entity))) || $entityClass === RegistrationFolder::CLASSNAME ) {
                throw new WedofBadRequestHttpException("Erreur, vous ne pouvez pas modifier le numéro de sécurité sociale");
            } else {
                $attendeeBody = [
                    'gender' => isset($body['gender']) ? $body['gender'] : $attendee->getGender(),
                    'dateOfBirth' => isset($body['dateOfBirth']) ? $body['dateOfBirth'] : $attendee->getDateOfBirth(),
                    'codeCityOfBirth' => isset($body['codeCityOfBirth']) ? $body['codeCityOfBirth'] : $attendee->getCodeCityOfBirth(),
                    'codeCountryOfBirth' => isset($body['codeCountryOfBirth']) ? $body['codeCountryOfBirth'] : $attendee->getCodeCountryOfBirth(),
                    'nir' => $body['retrievedNir']
                ];
                $this->validateNir($attendeeBody);
                $body['nir'] = $body['retrievedNir'];
            }
        }

        $attendee = $attendeeService->createOrUpdate($body, $attendee, true);

        if (!empty($entity)) {
            if ($isAttendeeUser) {
                $title = "Le candidat a mis à jour ses données personnelles";
                $origin = "le Candidat";
            } else {
                $isCertificationFolder = $entityClass === RegistrationFolder::CLASSNAME ? " de l'apprenant " : " du candidat ";
                $title = "Les données " . $isCertificationFolder . " ont été mises à jour avec une pièce d'identité";
                $origin = null;
            }
            $activityService->create([
                'title' => $title,
                'type' => ActivityTypes::UPDATE(),
                'eventTime' => new DateTime(),
                'origin' => $origin
            ], null, $entity, false);
        }

        return $attendee;
    }

    /**
     * @Rest\Get("/api/attendees/{entityClass}/{entityId}/canUpdateManually", requirements={"entityClass"="RegistrationFolder|CertificationFolder"})
     * @Rest\View(StatusCode = 200)
     * @param AttendeeService $attendeeService
     * @param RegistrationFolderService $registrationFolderService
     * @param CertificationFolderService $certificationFolderService
     * @param string $entityClass
     * @param string $entityId
     * @return JsonResponse
     */
    public function canUpdateManually(AttendeeService $attendeeService, RegistrationFolderService $registrationFolderService, CertificationFolderService $certificationFolderService, string $entityClass, string $entityId): JsonResponse
    {
        $showNirDataAttendee = false;
        /** @var User $user */
        $user = $this->getUser();

        if ($entityClass === RegistrationFolder::CLASSNAME) {
            $entity = $registrationFolderService->getByExternalId($entityId);
        } else {
            $entity = $certificationFolderService->getByExternalId($entityId);
            $showNirDataAttendee = $entity->getCertifier() === $user->getMainOrganism();
        }
        if (!$entity) {
            throw new WedofNotFoundHttpException("L'entité n'a pas été trouvée.");
        }
        $attendee = $entity->getAttendee();
        if (!$this->isGranted(AttendeeVoter::EDIT, $attendee)) {
            throw new WedofAccessDeniedHttpException("Erreur, vous n'êtes pas autorisé à consulter les données de cet apprenant/candidat");
        }
        return new JsonResponse([
            'canUpdateManually' => $attendeeService->canUpdateAttendeeManually($attendee),
            'nir' => [
                'canUpdateNir' => $showNirDataAttendee ? ($attendee->isNirValidated() ? false : true) : false,
                'retrievedNir' => $showNirDataAttendee ? $attendee->retrievedNir() : null
            ]
        ]);
    }

    /**
     * @Rest\Get("/api/attendees/{id}/experiences")
     * @Rest\View(StatusCode = 200)
     *
     * @Rest\QueryParam(name="certificationFolderExternalId", nullable=true, description="Permet de lister les expériences liées à un dossier de certification via l'externalId.")
     * @Rest\QueryParam(name="limit", requirements="\d+", default="100", description="Nombre d'éléments retourné par requête - par défaut 100.")
     * @Rest\QueryParam(name="page", requirements="\d+", default="1", description="Numéro de page de la requête - par défaut la première.")
     *
     * @param Attendee $attendee
     * @param PaginatorInterface $paginator
     * @param ParamFetcherInterface $paramFetcher
     * @param AttendeeExperienceService $attendeeExperienceService
     * @param CertificationFolderService $certificationFolderService
     * @return Response
     */
    public function listExperiences(Attendee $attendee, PaginatorInterface $paginator, ParamFetcherInterface $paramFetcher, AttendeeExperienceService $attendeeExperienceService, CertificationFolderService $certificationFolderService): Response
    {
        $parameters = $paramFetcher->all(true);

        if (isset($parameters['certificationFolderExternalId'])) {
            $certificationFolder = $certificationFolderService->getByExternalId($parameters['certificationFolderExternalId']);
            if (!$certificationFolder) {
                throw new WedofBadRequestHttpException("Erreur, le dossier de certification associé à l'externalId " . $parameters['certificationFolderExternalId'] . " n'a pas été trouvé.");
            }
            if (!$this->isGranted(CertificationFolderVoter::VIEW, $certificationFolder)) {
                throw  new WedofBadRequestHttpException("Erreur, vous n'avez pas les droits pour lister les expériences du dossier de certification");
            }
            $parameters['certificationFolder'] = $certificationFolder;
        } else if (!$this->isGranted('ROLE_ATTENDEE')) {
            throw  new WedofBadRequestHttpException("Erreur, vous n'avez pas les droits pour lister les expériences du dossier de certification");
        }

        $data = $paginator->paginate($attendeeExperienceService->listReturnQueryBuilder($parameters, $attendee), intval($parameters['page']), intval($parameters['limit']));

        $view = $this->view($data->getItems(), 200);
        $view->setHeader("x-total-count", $data->getTotalItemCount());
        $view->setHeader("x-current-page", $data->getCurrentPageNumber());
        $view->setHeader("x-item-per-page", $data->getItemNumberPerPage());
        return $this->handleView($view);
    }

    /**
     * @Rest\Post("/api/attendees/{id}/experiences")
     * @IsGranted("ROLE_ATTENDEE", message="not allowed")
     * @Rest\View(StatusCode = 201)
     *
     * @param Attendee $attendee
     * @param AttendeeExperienceService $attendeeExperienceService
     * @param CertificationFolderService $certificationFolderService
     * @return AttendeeExperience|View
     * @throws Exception
     */
    public function createExperience(Attendee $attendee, AttendeeExperienceService $attendeeExperienceService, CertificationFolderService $certificationFolderService)
    {
        $body = $this->getData();
        if (!empty($body['startDate'])) {
            if (strtotime($body['startDate'])) {
                $body['startDate'] = (new DateTime($body['startDate']))->setTimezone(new DateTimeZone('Europe/Paris'));
            }
        }
        if (!empty($body['endDate'])) {
            if (strtotime($body['endDate'])) {
                $body['endDate'] = (new DateTime($body['endDate']))->setTimezone(new DateTimeZone('Europe/Paris'));
            }
            if (empty($body['startDate'])) {
                throw new WedofBadRequestHttpException("Erreur, si vous indiquez une date de fin vous devez indiquer une date de début.");
            }
        }

        $violations = $this->validateCreateExperienceBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }
        $certificationFolder = $certificationFolderService->getByExternalId($body['certificationFolderExternalId']);
        if (!$certificationFolder) {
            throw new WedofBadRequestHttpException("Erreur, le dossier de certification associé à l'externalId " . $body['certificationFolderExternalId'] . " n'a pas été trouvé.");
        }
        if (!$this->isGranted(CertificationFolderVoter::ATTENDEE_VIEW, $certificationFolder)) {
            throw  new WedofBadRequestHttpException("Erreur, vous n'avez pas les droits pour créer une expérience sur ce dossier de certification");
        }
        if ($body['situation'] === SituationCertification::ACTIVE()->getValue()) {
            if (!isset($body['job']) || !isset($body['startDate']) || !isset($body['companyName']) || !isset($body['contractType']) || !isset($body['executiveStatus'])) {
                throw new WedofBadRequestHttpException("Erreur, en tant qu'Actif vous devez remplir les champs : 'job', 'startDate', 'companyName', 'contractType', 'executiveStatus'. ");
            }
        }
        return $attendeeExperienceService->create($body, $attendee);
    }

    /**
     * @Rest\Post("/app/public/attendees/{entityId}/checkMatching")
     * @Rest\View(StatusCode = 200)
     * @param string $entityId
     * @param CertificationFolderService $certificationFolderService
     * @param AttendeeService $attendeeService
     */
    public function updateNir(string $entityId, CertificationFolderService $certificationFolderService, AttendeeService $attendeeService)
    {
        $userOrAttendeeOrNull = $this->getUser();
        $isAttendeeUser = !($userOrAttendeeOrNull instanceof User);
        if (!$isAttendeeUser) {
            throw new WedofAccessDeniedHttpException("Erreur, vous n'êtes pas autorisé à modifier les données du candidat");
        }
        $entity = $certificationFolderService->getByExternalId($entityId);
        if (!$entity) {
            throw new WedofNotFoundHttpException("L'entité n'a pas été trouvée.");
        }
        $attendee = $entity->getAttendee();
        if (!empty($userOrAttendeeOrNull) && $userOrAttendeeOrNull !== $attendee) {
            throw new WedofAccessDeniedHttpException("Erreur, vous n'êtes pas autorisé à modifier un autre apprenant / candidat");
        }
        if ($attendee->isNirValidated()) {
            throw new WedofBadRequestHttpException("Erreur, le numéro de sécurité sociale a déjà été validé");
        }
        $body = $this->getData();
        if (isset($body['firstName']) && (strtolower($attendee->getFirstName()) !== strtolower($body['firstName']))) {
            throw new WedofBadRequestHttpException("Erreur, le prénom inscrit sur la carte vitale (" . $body['firstName'] . ") ne correspond pas au candidat (" . $attendee->getFirstName() . ").");
        }
        if (isset($body['lastName'])) {
            $isMatchingLastName = strtolower($attendee->getLastName()) === strtolower($body['lastName']);
            if (!$isMatchingLastName && (!$attendee->getBirthName() || ($attendee->getBirthName() && strtolower($attendee->getBirthName()) !== strtolower($body['lastName'])))) {
                throw new WedofBadRequestHttpException("Erreur, le nom de famille inscrit sur la carte vitale (" . $body['lastName'] . ") ne correspond pas au candidat (" . $attendee->getLastName() . ").");
            }
        }
        $attendeeBody = [
            'gender' => $attendee->getGender(),
            'dateOfBirth' => $attendee->getDateOfBirth(),
            'codeCityOfBirth' => $attendee->getCodeCityOfBirth(),
            'codeCountryOfBirth' => $attendee->getCodeCountryOfBirth(),
            'nir' => $body['nir']
        ];
        $isNirMatching = $this->validateNir($attendeeBody);
        if ($isNirMatching) {
            $attendee->setNir($body['nir']);
            if (!$attendee->getGender()) {
                $attendeeGender = substr($body['nir'], 0, 1) === "2" ? AttendeeGender::FEMALE()->getValue() : AttendeeGender::MALE()->getValue();
                $attendee->setGender($attendeeGender);
            }
            $attendeeService->save($attendee);
        }
    }

    //----------------
    // METHODES PRIVES
    //----------------

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateCreateBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $phoneNumberAsserts = [new Assert\Type('string'), new Assert\Length(['min' => 7, 'max' => 17]), new Assert\Regex(['pattern' => Tools::MOBILEPHONE_PATTERN])];
        $phoneFixedAsserts = [new Assert\Type('string'), new Assert\Length(['min' => 7, 'max' => 17]), new Assert\Regex(['pattern' => Tools::PHONE_PATTERN])];
        $constraints = new Assert\Collection([
            'lastName' => new Assert\Required([new Assert\NotBlank(), new Assert\Type('string'), new Assert\NotNull(), new Assert\Length(['max' => 255])]),
            'firstName' => new Assert\Required([new Assert\NotBlank(), new Assert\Type('string'), new Assert\NotNull(), new Assert\Length(['max' => 255])]),
            'firstName2' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 255])]),
            'firstName3' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 255])]),
            'email' => new Assert\Required([new Assert\NotBlank(), new Assert\Email(), new Assert\NotNull(), new Assert\Length(['max' => 255])]),
            'phoneNumber' => empty($body['phoneFixed']) ? new Assert\Required($phoneNumberAsserts) : new Assert\Optional($phoneNumberAsserts),
            'phoneFixed' => empty($body['phoneNumber']) ? new Assert\Required($phoneFixedAsserts) : new Assert\Optional($phoneFixedAsserts),
            'dateOfBirth' => new Assert\Optional([new Assert\Type('datetime')]),
            'nameCityOfBirth' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 255])]),
            'codeCityOfBirth' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(5)]),
            'codeCountryOfBirth' => new Assert\Optional([new Assert\Regex(['pattern' => '/^[0-9]{3}$/']), new Assert\Type('integer')]),
            'nameCountryOfBirth' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 255])]),
            'gender' => new Assert\Required([new Assert\Choice([AttendeeGender::MALE()->getValue(), AttendeeGender::FEMALE()->getValue()])]),
            'birthName' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 255])]),
            'identificationDocument' => new Assert\Optional([new Assert\File()]),
            'address' => new Assert\Optional(new Assert\Collection([
                'fields' => [
                    'number' => new Assert\Optional([new Assert\Type('string')]),
                    'repetitionIndexLabel' => new Assert\Optional([new Assert\Choice(["bis", "quinquies", "quater", "ter"])]),
                    'roadTypeLabel' => new Assert\Optional([new Assert\Type('string')]),
                    'roadName' => new Assert\Optional([new Assert\Type('string')]),
                    'zipCode' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(5)]),
                    'city' => new Assert\Optional([new Assert\Type('string')]),
                ]
            ])),
            'nir' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['min' => 15, 'max' => 15])])
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateUpdateBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'allowExtraFields' => true,
            'fields' => [
                'lastName' => new Assert\Required([new Assert\NotBlank(), new Assert\Type('string'), new Assert\NotNull(), new Assert\Length(['max' => 255])]),
                'firstName' => new Assert\Required([new Assert\NotBlank(), new Assert\Type('string'), new Assert\NotNull(), new Assert\Length(['max' => 255])]),
                'firstName2' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 255])]),
                'firstName3' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 255])]),
                'email' => new Assert\Optional([new Assert\NotBlank(), new Assert\Email(), new Assert\NotNull(), new Assert\Length(['max' => 255])]),
                'phoneNumber' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['min' => 7, 'max' => 17]), new Assert\Regex(['pattern' => Tools::MOBILEPHONE_PATTERN])]),
                'phoneFixed' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['min' => 7, 'max' => 17]), new Assert\Regex(['pattern' => Tools::PHONE_PATTERN])]),
                'dateOfBirth' => new Assert\Optional(new Assert\Type('datetime')),
                'nameCityOfBirth' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 255])]),
                'codeCityOfBirth' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(5)]),
                'codeCountryOfBirth' => new Assert\Optional([new Assert\Regex(['pattern' => '/^[0-9]{3}$/']), new Assert\Type('integer')]),
                'nameCountryOfBirth' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 255])]),
                'gender' => new Assert\Optional([new Assert\Choice([AttendeeGender::MALE()->getValue(), AttendeeGender::FEMALE()->getValue()])]),
                'birthName' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 255])]),
                'identificationDocument' => new Assert\Optional([new Assert\File()]),
                'address' => new Assert\Optional(new Assert\Collection([
                    'fields' => [
                        'number' => new Assert\Optional([new Assert\Type('string')]),
                        'repetitionIndexLabel' => new Assert\Optional([new Assert\Choice(["bis", "quinquies", "quater", "ter"])]),
                        'roadTypeLabel' => new Assert\Optional([new Assert\Type('string')]),
                        'roadName' => new Assert\Optional([new Assert\Type('string')]),
                        'zipCode' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(5)]),
                        'city' => new Assert\Optional([new Assert\Type('string')]),
                    ]
                ])),
                'retrievedNir' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['min' => 15, 'max' => 15])])
            ]
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateCreateExperienceBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'certificationFolderExternalId' => new Assert\Required([new Assert\Type('string')]),
            'situation' => new Assert\Required([new Assert\Choice([SituationCertification::ACTIVE()->getValue(), SituationCertification::INACTIVE()->getValue(), SituationCertification::TRAINING()->getValue(), SituationCertification::SEARCHING()->getValue()])]),
            'qualification' => new Assert\Required([new Assert\Type('integer'), new Assert\Length(['min' => 0, 'max' => 8])]),
            'certificationName' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 255])]),
            'job' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 255])]),
            'startDate' => new Assert\Optional(new Assert\Type('datetime')),
            'endDate' => new Assert\Optional(new Assert\Type('datetime')),
            'companyName' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 255])]),
            'salaryYearly' => new Assert\Optional([new Assert\Type('integer')]),
            'executiveStatus' => new Assert\Optional([new Assert\Type('bool')]),
            'contractType' => new Assert\Optional([new Assert\Choice([ContractType::CDD()->getValue(), ContractType::CDI()->getValue(), ContractType::INTERIM()->getValue(), ContractType::INDEPENDANT()->getValue(), ContractType::INACTIF()->getValue()])]),
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param array $body
     * @return array
     */
    private function constructAttendeeAddress(array $body): array
    {
        return [
            "id" => null,
            "city" => $body['city'] ?? null,
            "line4" => null,
            "number" => $body['number'] ?? null,
            "country" => null,
            "postBox" => null,
            "zipCode" => $body['zipCode'] ?? null,
            "roadName" => $body['roadName'] ?? null,
            "roadType" => null,
            "idAddress" => null,
            "residence" => null,
            "countryCode" => null,
            "fullAddress" => null,
            "trainingSite" => null,
            "corporateName" => null,
            "roadTypeLabel" => $body['roadTypeLabel'] ?? null,
            "informationSite" => null,
            "repetitionIndex" => null,
            "subscriptionSite" => null,
            "additionalAddress" => null,
            "repetitionIndexLabel" => $body['repetitionIndexLabel'] ?? null,
            "reducedMobilityAccessCompliant" => null,
            "reducedMobilityAccessModalities" => null
        ];
    }

    /**
     * @param array $body
     * @return bool
     */
    private function validateNir(array $body)
    {
        $nir = $body['nir'];
        if (strlen($nir) !== 15) {
            throw new WedofBadRequestHttpException("Erreur, le numéro de sécurité sociale doit être composé de 15 chiffres");
        }

        // EXEMPLE : 185057800608436
        // 1 => sexe (1 (Homme) ou 2 (Femme))
        $genderNir = substr($nir, 0, 1);
        if ($body['gender']) {
            $genderAttendee = $body['gender'] === AttendeeGender::FEMALE()->getValue() ? "2" : "1";
            if ($genderNir !== $genderAttendee) {
                throw new WedofBadRequestHttpException("Erreur, la civilité ne correspond pas.");
            }
        }
        // 85 => année de naissance
        // 05 => mois de naissance
        $birthYearNir = substr($nir, 1, 2);
        $birthMonthNir = substr($nir, 3, 2);
        if ($body['dateOfBirth']) {
            $birthYearAttendee = substr($body['dateOfBirth']->format('Y'), 2);
            if ($birthYearAttendee !== $birthYearNir) {
                throw new WedofBadRequestHttpException("Erreur, l'année de naissance ne correspond pas.");
            }
            $birthMonthAttendee = $body['dateOfBirth']->format('m');
            if ($birthMonthAttendee !== $birthMonthNir) {
                throw new WedofBadRequestHttpException("Erreur, le mois de naissance ne correspond pas.");
            }
        }
        // 78 => département de naissance / 006 => commune de naissance
        //    => Si né en france (2 chiffres du code du département de naissance (ce code est celui existant au moment de la naissance) + 3 chiffres du code commune officiel de l'Insee)
        //    => Si vous êtes né à l'étranger : les 2 chiffres du code du département sont remplacés par 99 et le code commune par un code Insee du pays de naissance
        //    => Vous êtes né en Algérie, au Maroc ou en Tunisie avant l'indépendance de ces pays : un code spécifique peut figurer à la place du code 99 et du code INSEE du pays concerné (exemples : 91, 92, 93 ou 94 pour l'Algérie, 95 pour le Maroc et 96 pour la Tunisie)
        $birthPlaceNir = substr($nir, 5, 5);
        if (substr($birthPlaceNir, 0, 2) === '99') { // né à l'étranger
            if ($body['codeCityOfBirth']) {
                throw new WedofBadRequestHttpException("Erreur, le lieu de naissance ne correspond pas avec la carte vitale.");
            } else if ($body['codeCountryOfBirth']) {
                if (intval(substr($birthPlaceNir, 2)) !== $body['codeCountryOfBirth']) {
                    throw new WedofBadRequestHttpException("Erreur, le lieu de naissance ne correspond pas avec la carte vitale.");
                }
            }
        } else if (substr($birthPlaceNir, 0, 1) !== '9') { // né en France
            if ($body['codeCountryOfBirth']) {
                throw new WedofBadRequestHttpException("Erreur, le lieu de naissance ne correspond pas avec la carte vitale.");
            } else if ($body['codeCityOfBirth']) {
                if ($birthPlaceNir !== $body['codeCityOfBirth']) {
                    throw new WedofBadRequestHttpException("Erreur, le lieu de naissance ne correspond pas avec la carte vitale.");
                }
            }
        } else if ($body['codeCountryOfBirth'] && substr($birthPlaceNir, 2) !== $body['codeCountryOfBirth']) { // né Algérie, au Maroc ou en Tunisie avant l'indépendance
            throw new WedofBadRequestHttpException("Erreur, le lieu de naissance ne correspond pas avec la carte vitale.");
        }
        // 084 => Numéro d'ordre permettant de distinguer les personnes nées au même lieu à la même période
        // 36 => clé de sécurité
        //    => calcul : 97 – ((Valeur numérique du NIR) modulo 97)
        $securityKeyNir = substr($nir, 13);
        $calculKey = 97 - (substr($nir, 0, -2) % 97);
        if (intval($securityKeyNir) !== $calculKey) {
            throw new WedofBadRequestHttpException("Erreur, la clé de sécurité de la carte vitale ne correspond pas aux données renseignées.");
        }
        return true;
    }
}
