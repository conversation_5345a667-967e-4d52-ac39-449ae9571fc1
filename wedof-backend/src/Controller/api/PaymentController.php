<?php

namespace App\Controller\api;

use App\Entity\Payment;
use App\Entity\User;
use App\Exception\WedofAccessDeniedHttpException;
use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofNotFoundHttpException;
use App\Exception\WedofSubscriptionException;
use App\Library\utils\enums\PaymentStates;
use App\Security\Voter\OrganismVoter;
use App\Security\Voter\RegistrationFolderVoter;
use App\Security\Voter\PaymentVoter;
use App\Service\OrganismService;
use App\Service\PaymentService;
use App\Service\RegistrationFolderService;
use Doctrine\Common\Collections\ArrayCollection;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcherInterface;
use Knp\Component\Pager\PaginatorInterface;
use Nelmio\ApiDocBundle\Annotation as ApiDoc;
use OpenApi\Annotations as OA;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints as Assert;

/**
 * Class PaymentController
 * @package App\Controller\api
 *
 * @OA\Tag(name="Payment")
 * @ApiDoc\Security(name="accessCode")
 *
 */
class PaymentController extends AbstractFOSRestController
{
    //-------------------
    // METHODES PUBLIQUES
    //-------------------
    /**
     * @Rest\Get("/api/payments/{id}")
     * @IsGranted(PaymentVoter::VIEW, subject="payment", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"payments", "default"})
     * @OA\Get (
     *     summary="Récupération d'un paiement d'un dossier de formation.",
     *     description="Récupération d'un paiement par son ID. Via OAuth2, cet appel nécessite le scope 'payment:read'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations d'un paiement.",
     *     @OA\MediaType(mediaType="application/json",
     *          @OA\Schema(ref="#/components/schemas/Payment")
     *     )
     * )
     * @OA\Parameter(
     *     name="id",
     *     in="path",
     *     description="ID du paiment",
     *     @OA\Schema(type="string")
     * )
     * @param Payment $payment
     * @return Payment
     */
    public function show(Payment $payment): Payment
    {
        return $payment;
    }

    /**
     * @Rest\Get("/api/payments")
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_PAYMENT:READ')", message="not allowed")
     *
     * @Rest\QueryParam(name="siret", requirements="\d{14}", nullable=true, description="Permet de n'obtenir que les paiements appartenant à l'organisme de siret considéré - par défaut l'organisme de l'utilisateur courant.")
     * @Rest\QueryParam(name="since", requirements=@Assert\Date(), nullable=true, description="Permet de n'obtenir que les paiements depuis la date considérée - par défaut aucune restriction de date.")
     * @Rest\QueryParam(name="format", requirements=@Assert\Choice({"json","csv"}), default="json", description="Permet de générer la réponse dans différents formats - par défaut JSON.")
     * @Rest\QueryParam(name="type", requirements=@Assert\Choice({"bill","deposit"}), nullable=true, description="Permet de n'obtenir que les paiements pour un type particulier - par défaut aucune restriction de type.")
     * @Rest\QueryParam(name="state", requirements=@Assert\Type("string"), default="all", description="Permet de n'obtenir que les paiements dans un état particulier - par défaut tous les états sont retournés. Valeurs possibles : 'all', 'waiting', 'rejected', 'issued'. Il est possible de demander plusieurs états en séparant chaque état par une virgule, ex : 'rejected,issued'.")
     * @Rest\QueryParam(name="registrationFolderId", requirements=@Assert\Type("string"),  nullable=true, description="Permet de n'obtenir que les paiements pour un dossier particulier - par défaut aucune restriction de dossier.")
     *
     * @Rest\QueryParam(name="limit", requirements="\d+", default="100", description="Nombre d'éléments retourné par requête - par défaut 100.")
     * @Rest\QueryParam(name="page", requirements="\d+", default="1", description="Numéro de page de la requête - par défaut la première.")
     * @Rest\QueryParam(name="order", requirements=@Assert\Choice({"asc", "desc"}), default="desc", description="Tri les résultats par ordre ascendant ou descendant - par défaut descendant.")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"payments", "default"})
     * @OA\Get (
     *     summary="Liste tous les paiements de l'organisme de l'utilisateur courant. Peut aussi lister les paiements de manière filtrée.",
     *     description="Récupère l'ensemble des paiements liées à l'organisme de l'utilisateur connecté. NOTA : les paramètres sont cumulatifs et deux paramètres incompatibles renverront un résultat vide. Via OAuth2, cet appel nécessite le scope 'payment:read'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un tableau des paiements des dossiers de formation au format JSON",
     *     @OA\JsonContent(
     *          type="array",
     *          @OA\Items(ref="#/components/schemas/Payment")
     *     )
     * )
     * @OA\Parameter (name="order", in="query", @OA\Schema (ref="#/components/schemas/Order"))
     * @OA\Parameter (name="format", in="query", @OA\Schema (ref="#/components/schemas/PaymentFormat"))
     * @OA\Parameter (name="type", in="query", @OA\Schema (ref="#/components/schemas/PaymentType"))
     * @OA\Parameter (name="state", in="query", @OA\Schema (ref="#/components/schemas/PaymentState"))
     *
     * @param ParamFetcherInterface $paramFetcher
     * @param RegistrationFolderService $registrationFolderService
     * @param OrganismService $organismService
     * @param PaymentService $paymentService
     * @param PaginatorInterface $paginator
     * @return Response
     * @throws WedofSubscriptionException
     */
    public function list(ParamFetcherInterface $paramFetcher, RegistrationFolderService $registrationFolderService, OrganismService $organismService, PaymentService $paymentService, PaginatorInterface $paginator): Response
    {
        /* @var $user User */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();
        if (!$organism->getSubscription()->isAllowRegistrationFolderReads()) {
            throw new WedofSubscriptionException("Erreur, votre abonnement ne permet pas l'accès aux paiements.");
        }

        $parameters = $paramFetcher->all(true);
        $parameters = $this->validateListParameters($parameters, $organismService, $registrationFolderService);

        $data = $paginator->paginate($paymentService->findAllReturnQueryBuilder(new ArrayCollection([$organism]), $parameters), intval($parameters['page']), intval($parameters['limit']));
        if ($parameters['format'] == 'csv') {
            $response = $this->exportPaymentsAsCSV($data->getItems(), "payments");
            $response->headers->set("x-total-count", $data->getTotalItemCount());
            $response->headers->set("x-current-page", $data->getCurrentPageNumber());
            $response->headers->set("x-item-per-page", $data->getItemNumberPerPage());
            return $response;
        } else {
            $view = $this->view($data->getItems(), 200);
            $view->setHeader("x-total-count", $data->getTotalItemCount());
            $view->setHeader("x-current-page", $data->getCurrentPageNumber());
            $view->setHeader("x-item-per-page", $data->getItemNumberPerPage());
            return $this->handleView($view);
        }
    }

    /**
     * @Rest\Post ("/api/payments")
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_PAYMENT:WRITE')", message="not allowed")
     * @Rest\View(StatusCode = 501)
     * @return WedofBadRequestHttpException
     */
    public function create(): WedofBadRequestHttpException
    {
        throw new WedofBadRequestHttpException("Méthode non supportée - Un nouveau paiement ne peut être créé.");
    }

    /**
     * @Rest\Put ("/api/payments/{id}")
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_PAYMENT:WRITE')", message="not allowed")
     * @Rest\View(StatusCode = 501)
     * @param Payment $payment
     * @return WedofBadRequestHttpException
     */
    public function update(Payment $payment): WedofBadRequestHttpException
    {
        throw new WedofBadRequestHttpException("Méthode non supportée - Le paiement associé au dossier de formation " . $payment->getRegistrationFolder()->getId() . " ne peut être modifié.");
    }

    /**
     * @Rest\Delete ("/api/payments/{id}")
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_PAYMENT:WRITE')", message="not allowed")
     * @Rest\View(StatusCode = 501)
     * @param Payment $payment
     * @return WedofBadRequestHttpException
     */
    public function delete(Payment $payment): WedofBadRequestHttpException
    {
        throw new WedofBadRequestHttpException("Méthode non supportée - Le paiement associé au dossier de formation " . $payment->getRegistrationFolder()->getId() . " ne peut être supprimé.");
    }

//----------------
// METHODES PRIVES
//----------------
    /**
     * @param $payments
     * @param $fileName
     * @return Response
     */
    private function exportPaymentsAsCSV($payments, $fileName): Response
    {
        $array = [];
        foreach ($payments as $payment) {
            /* @var $payment Payment */
            $array[] = [
                'id' => $payment->getId(),
                'type' => $payment->getType(),
                'state' => $payment->getState(),
                'amount' => $payment->getAmount(),
                'billNumber' => $payment->getBillNumber(),
                'registrationFolder' => $payment->getRegistrationFolder()->getExternalId(),
                'organism' => $payment->getOrganism()->getSiret(),
            ];
        }
        $fp = fopen('php://temp', 'w');
        fputcsv($fp, array_keys($array[0]), ';');
        foreach ($array as $row) {
            fputcsv($fp, array_values($row), ';');
        }
        rewind($fp);
        $response = new Response(stream_get_contents($fp));
        fclose($fp);
        $response->headers->set('Content-Type', 'text/csv');
        $response->headers->set('Content-Disposition', 'attachment; filename="' . $fileName . '.csv"');
        return $response;
    }

    /**
     * @param array $parameters
     * @param OrganismService $organismService
     * @param RegistrationFolderService $registrationFolderService
     * @return array
     */
    private function validateListParameters(array $parameters, OrganismService $organismService, RegistrationFolderService $registrationFolderService): array
    {
        if (!empty($parameters['siret'])) {
            $organism = $organismService->getBySiret($parameters['siret']);
            if ($organism) {
                if (!$this->isGranted(OrganismVoter::VIEW, $organism)) {
                    throw new WedofAccessDeniedHttpException("Non autorisé");
                }
            } else {
                throw new WedofNotFoundHttpException("L'organisme associé au siret " . $parameters['siret'] . " n'a pas été trouvé.");
            }
        }

        if (!empty($parameters['registrationFolderId'])) {
            $registrationFolder = $registrationFolderService->getByExternalId($parameters['registrationFolderId']);
            if ($registrationFolder) {
                if (!$this->isGranted(RegistrationFolderVoter::OWNER_VIEW, $registrationFolder)) {
                    throw new WedofAccessDeniedHttpException("Non autorisé");
                }
            } else {
                throw new WedofNotFoundHttpException("Le dossier de formation associé au registrationFolderId " . $parameters['registrationFolderId'] . " n'a pas été trouvé.");
            }
        }

        if (isset($parameters['state'])) {
            $parameters['state'] = explode(",", $parameters['state']);
            foreach ($parameters['state'] as $state) {
                if (!in_array($state, PaymentStates::valuesStates())) {
                    throw new WedofBadRequestHttpException("Erreur sur les valeurs renvoyées 'state', elles doivent être : " . join(",", PaymentStates::valuesStates()) . ".");
                }
            }
        }

        return $parameters;
    }
}
