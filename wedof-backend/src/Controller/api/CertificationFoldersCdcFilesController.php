<?php
// src/Controller/api/CertificationFoldersCdcFilesController.php

namespace App\Controller\api;

use App\Entity\CertificationFoldersCdcFiles;
use App\Exception\WedofAccessDeniedHttpException;
use App\Exception\WedofNotFoundHttpException;
use App\Security\Voter\CertificationFolderVoter;
use App\Service\CertificationFoldersCdcFilesService;
use App\Service\CertificationFolderService;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\Response;

/**
 * Class CertificationFoldersCdcFilesController
 * @package App\Controller\api
 */
class CertificationFoldersCdcFilesController extends AbstractFOSRestController
{
    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    /**
     * @Rest\Get("/api/certificationFoldersCdcFiles/current/{certificationFolderExternalId}", name="showCurrent")
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_CERTIFICATIONFOLDER:READ')", message="not allowed")
     * @Rest\View(StatusCode = 200)
     * @param CertificationFoldersCdcFilesService $certificationFoldersCdcFilesService
     * @param CertificationFolderService $certificationFolderService
     * @param string $certificationFolderExternalId
     * @return Response|CertificationFoldersCdcFiles
     */
    public function showCurrent(CertificationFoldersCdcFilesService $certificationFoldersCdcFilesService, CertificationFolderService $certificationFolderService, string $certificationFolderExternalId)
    {
        $certificationFolder =  $certificationFolderService->getByExternalId($certificationFolderExternalId);
        if (!$certificationFolder) {
            throw new WedofNotFoundHttpException("Le dossier de certification associé à l'identifiant technique " . $certificationFolderExternalId . " n'a pas été trouvé.");
        }
        if (!$this->isGranted(CertificationFolderVoter::VIEW, $certificationFolder)) {
            throw new WedofAccessDeniedHttpException("Vous n'êtes pas autorisé à avoir accès au contenu du dossier de certification associé à l'externalId " . $certificationFolderExternalId);
        }
        $certificationFolderCdcFiles = $certificationFoldersCdcFilesService->findCurrentCertificationFoldersCdcFiles($certificationFolder);
        if (!$certificationFolderCdcFiles) {
            return new Response('', 204); // Empty response instead of 404 as it is more a search than an access to a known resource
        }
        return $certificationFolderCdcFiles;
    }
}
