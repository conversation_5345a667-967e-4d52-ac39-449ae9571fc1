<?php
// src/Controller/api/CertificationController.php
namespace App\Controller\api;

use App\Entity\Attendee;
use App\Entity\Certification;
use App\Entity\CertificationFile;
use App\Entity\Organism;
use App\Entity\User;
use App\Exception\WedofAccessDeniedHttpException;
use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofConnectionException;
use App\Exception\WedofNotFoundHttpException;
use App\Exception\WedofSubscriptionException;
use App\Library\CertificationStatistics;
use App\Library\utils\enums\CertificationExaminationType;
use App\Library\utils\enums\CertificationObtentionSystem;
use App\Library\utils\enums\CertificationPartnerStates;
use App\Library\utils\enums\CertificationTypes;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\Tools;
use App\Repository\CertificationFileRepository;
use App\Security\Voter\CertificationVoter;
use App\Security\Voter\OrganismVoter;
use App\Service\CertificationFileService;
use App\Service\CertificationFolderService;
use App\Service\CertificationPartnerService;
use App\Service\CertificationService;
use App\Service\DataProviders\FranceCompetencesApiService;
use App\Service\MailerService;
use App\Service\OrganismService;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use ErrorException;
use FOS\RestBundle\Context\Context;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcherInterface;
use FOS\RestBundle\View\View;
use Knp\Component\Pager\PaginatorInterface;
use LogicException;
use Nelmio\ApiDocBundle\Annotation as ApiDoc;
use OpenApi\Annotations as OA;
use PhpOffice\PhpSpreadsheet\Reader\Exception;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validation;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Throwable;
use Vich\UploaderBundle\Handler\DownloadHandler;

/**
 * Class CertificationController
 * @package App\Controller\api
 *
 * @OA\Tag(name="Certification")
 * @ApiDoc\Security(name="accessCode")
 */
class CertificationController extends AbstractWedofController
{

    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    /**
     * @Rest\Post("/app/certifications/inviteCertifier/{certifInfo}")
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_CERTIFICATION:READ')", message="not allowed")
     * @Rest\View(StatusCode = 201)
     *
     * @param Certification $certification
     * @param MailerService $mailerService
     * @param Request $request
     * @return View|void
     * @throws TransportExceptionInterface
     */
    public function inviteCertifier(Certification $certification, MailerService $mailerService, Request $request)
    {
        /** @var User $user */
        $user = $this->getUser();
        $body = json_decode($request->getContent(), true);
        $violations = Validation::createValidator()->validate($body, new Assert\Collection([
            'name' => new Assert\Required([new Assert\Type('string'), new Assert\NotBlank()]),
            'email' => new Assert\Required([new Assert\Email()]),
            'phoneNumber' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 50])])
        ]));
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }
        $mailerService->sendInviteCertifierToWedofTeam($certification, $user, $body);
    }

    /**
     * @Rest\Get("/app/certifications/{certifInfo}/certificateTemplateThumbnail")
     * @IsGranted(CertificationVoter::EDIT, subject="certification",  message="not allowed")
     * @Rest\View(StatusCode = 200)
     * @param Certification $certification
     * @param DownloadHandler $downloadHandler
     * @return Response
     */
    public function certificateTemplateThumbnail(Certification $certification, DownloadHandler $downloadHandler): Response
    {
        if ($certification->getCertificateTemplateThumbnailName()) {
            return $downloadHandler->downloadObject($certification, 'certificateTemplateThumbnailFile');
        } else {
            throw new WedofNotFoundHttpException("Le modèle de parchemin n'a pas été trouvé");
        }
    }

    /**
     * @Rest\Post("/app/certifications/{id}/certificateTemplateThumbnail")
     * @IsGranted(CertificationVoter::EDIT, subject="certification",  message="not allowed")
     * @Rest\View(StatusCode = 200)
     * @param Certification $certification
     * @param CertificationService $certificationService
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws WedofConnectionException
     * @throws \Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface
     */
    public function updateCertificateTemplateThumbnail(Certification $certification, CertificationService $certificationService)
    {
        $certificationService->updateCertificateTemplateThumbnail($certification);
    }

    /**
     * @Rest\Get("/api/certifications")
     * @Rest\Get("/app/certifications/lite")
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_CERTIFICATION:READ')", message="not allowed")
     * @Rest\QueryParam(name="organismType", requirements=@Assert\Choice({"all", "certifier", "partner"}), default="all", description="Permet de n'obtenir que les certifications pour lesquelles l'organisme est certificateur, ou que celles pour lesquelles l'organisme est partenaire ou toutes - par défaut toutes.")
     * @Rest\QueryParam(name="siret", requirements="\d{14}", nullable=true, description="Permet de n'obtenir que les certifications appartenant à l'organisme de siret considéré - par défaut l'organisme de l'utilisateur courant.")
     *
     * @Rest\QueryParam(name="query", requirements=@Assert\Type("string"), nullable=true, description="Permet d'effectuer une recherche libre sur les champs 'intitulé de la certification', 'certif info', 'rs code' et 'rncp code'.")
     * @Rest\QueryParam(name="enabled", requirements=@Assert\Choice({"false", "true"}), nullable=true, description="Permet de filtrer les certifications actives / inactives")
     * @Rest\QueryParam(name="certifInfo", requirements=@Assert\Type("string"), nullable=true, description="Permet de filtrer les certifications par certifInfo (numéro de certif info)")
     * @Rest\QueryParam(name="type", requirements=@Assert\Type("string"), nullable=true, description="Permet de filtrer les certifications par type (RS,RNCP)")
     * @Rest\QueryParam(name="certificationPartnerState", requirements=@Assert\Type("string"), nullable=true, description="Permet de filter sur le statut du partenaire lié à la certification. Valeurs possible : 'processing', 'active', 'aborted, 'refused', 'revoked', 'suspended'.")
     * @Rest\QueryParam(name="sort", requirements=@Assert\Choice({"name", "certifInfo", "createdOn", "updatedOn", "cpfDateEnd", "cpfDateStart"}), default="name", description="Tri les résultats sur un critère. Valeurs possibles: 'name' (intitulé de la certification), 'certifInfo' (numéro de certif info), 'updatedOn' (date de dernière mise à jour), , 'createdOn' (date de création) - par défaut 'name'.")
     * @Rest\QueryParam(name="certifierCustomer", requirements=@Assert\Choice({"false", "true"}), nullable=true)
     * @Rest\QueryParam(name="order", requirements=@Assert\Choice({"asc", "desc"}), default="asc", description="Tri les résultats par ordre ascendant ou descendant sur le critère 'sort' - par défaut ascendant.")
     * @Rest\QueryParam(name="limit", requirements="\d+", default="100", description="Nombre d'éléments retourné par requête - par défaut 100.")
     * @Rest\QueryParam(name="page", requirements="\d+", default="1", description="Numéro de page de la requête - par défaut la première.")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"certifications", "default"})
     * @OA\Get (
     *     summary="Liste toutes les certifications pour l'organisme de l'utilisateur courant.",
     *     description="Récupère l'ensemble des certifications de l'organisme de l'utilisateur connecté. NOTA : les paramètres sont cumulatifs et deux paramètres incompatibles renverront un résultat vide. Via OAuth2, cet appel nécessite le scope 'certification:read'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un tableau de certifications au format JSON",
     *     @OA\JsonContent(
     *          type="array",
     *          @OA\Items(ref=@ApiDoc\Model(type=Certification::class))
     *     )
     * )
     * @OA\Parameter (name="organismType", in="query", @OA\Schema (ref="#/components/schemas/OrganismType"))
     * @OA\Parameter (name="order", in="query", @OA\Schema (ref="#/components/schemas/Order"))
     *
     *
     * @param CertificationService $certificationService
     * @param OrganismService $organismService
     * @param PaginatorInterface $paginator
     * @param ParamFetcherInterface $paramFetcher
     * @param Request $request
     * @return Response
     */
    public function list(CertificationService $certificationService, OrganismService $organismService, PaginatorInterface $paginator, ParamFetcherInterface $paramFetcher, Request $request): Response
    {
        /* @var $user User */
        $user = $this->getUser();
        // Il est nécessaire de lister les certifications pour des utilisateurs non client pour afficher la vue par certification des dossiers de certification

        $parameters = $paramFetcher->all(true);

        if (isset($parameters['type'])) {
            $parameters['type'] = explode(',', $parameters['type']);
            foreach ($parameters['type'] as $type) {
                if ($type !== 'all' && !in_array($type, CertificationTypes::valuesTypes())) {
                    throw new WedofBadRequestHttpException("Erreur sur les valeurs renvoyées 'type', elles doivent être : " . join(",", CertificationTypes::valuesTypes()) . ".");
                }
            }
        }

        if (isset($parameters['certificationPartnerState'])) {
            $parameters['certificationPartnerState'] = explode(',', $parameters['certificationPartnerState']);
            foreach ($parameters['certificationPartnerState'] as $certificationPartnerState) {
                if (!in_array($certificationPartnerState, CertificationPartnerStates::valuesStates())) {
                    throw new WedofBadRequestHttpException("Erreur sur les valeurs renvoyées 'certificationPartnerState', elles doivent être : " . join(",", CertificationPartnerStates::valuesStates()) . ".");
                }
                if ($certificationPartnerState === CertificationPartnerStates::DRAFT()->getValue()) {
                    throw new WedofBadRequestHttpException("Erreur, état '" . CertificationPartnerStates::DRAFT()->getValue() . "' 'certificationPartnerState' non supporté");
                }
            }
        }

        if (!empty($parameters['siret'])) {
            $organism = $organismService->getBySiret($parameters['siret']);
            if (!empty($organism)) {
                if (!$this->isGranted(OrganismVoter::VIEW, $organism)) {
                    throw new WedofAccessDeniedHttpException("Vous n'êtes pas autorisé à accéder au contenu de l'organisme associé au siret " . $parameters['siret']);
                }
            } else {
                throw new WedofNotFoundHttpException("L'organisme associé au siret " . $parameters['siret'] . " n'a pas été trouvé.");
            }
        } else if ($this->isGranted('ROLE_DATA_PARTNER') || $this->isGranted('ROLE_ADMIN')) {
            $organism = null;
        } else {
            $organism = $user->getMainOrganism();
        }

        $data = $paginator->paginate($certificationService->listReturnQueryBuilder($organism, $parameters), intval($parameters['page']), intval($parameters['limit']));
        $view = $this->view($data->getItems(), 200);
        if (Tools::contains($request->getRequestUri(), 'lite')) {
            $context = new Context();
            $context->addGroup("lite");
            $view->setContext($context);
        }
        $view->setHeader("x-total-count", $data->getTotalItemCount());
        $view->setHeader("x-current-page", $data->getCurrentPageNumber());
        $view->setHeader("x-item-per-page", $data->getItemNumberPerPage());
        return $this->handleView($view);
    }

    /**
     * @Rest\Get("/app/certifications/{siret}/partnership-ordered")
     * @IsGranted(OrganismVoter::VIEW, subject="organism")
     * @Rest\QueryParam(name="limit", requirements="\d+", default="100", description="Nombre d'éléments retourné par requête - par défaut 100.")
     * @Rest\QueryParam(name="page", requirements="\d+", default="1", description="Numéro de page de la requête - par défaut la première.")
     * @Rest\QueryParam(name="query", requirements=@Assert\Type("string"), nullable=true, description="Permet d'effectuer une recherche libre sur les champs 'intitulé de la certification', 'certif info', 'rs code' et 'rncp code'.")
     * @Rest\QueryParam(name="certificationTypes", map=true, requirements=@Assert\Choice({"RS","RNCP"}), description="Permet de filtrer sur le type de certification. - Valeurs possible: 'RS', 'RNCP'")
     * @Rest\QueryParam(name="state", requirements=@Assert\Type("string"), nullable=true, description="Permet de filter sur le statut du partenaire lié à la certification. Valeurs possible : 'all', 'draft', 'processing', 'active', 'aborted, 'refused', 'revoked', 'suspended'.")
     * @Rest\QueryParam(name="defaultCertifier", requirements=@Assert\Type("string"), nullable=true, description="Permet de filter sur les certifications appartenant à l'organisme.")
     * @Rest\QueryParam(name="isPromoted", requirements=@Assert\Type("string"), nullable=true, description="Permet de filter sur les certifications promues.")
     *
     * @param Organism $organism
     * @param CertificationService $certificationService
     * @param PaginatorInterface $paginator
     * @param ParamFetcherInterface $paramFetcher
     * @return Response
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function partnershipOrderedList(Organism $organism, CertificationService $certificationService, PaginatorInterface $paginator, ParamFetcherInterface $paramFetcher): Response
    {
        $parameters = $paramFetcher->all(true);
        $isPromoted = filter_var($parameters['isPromoted'] ?? false, FILTER_VALIDATE_BOOLEAN);

        // Error with custom order without the option distinct to false
        $states = null;
        if (isset($parameters['state'])) {
            $states = explode(",", $parameters['state']);
            foreach ($states as $state) {
                if (!in_array($state, CertificationPartnerStates::valuesStates())) {
                    throw new WedofBadRequestHttpException("Erreur sur les valeurs renvoyées 'state', elles doivent être : " . join(",", CertificationPartnerStates::valuesStates()) . ".");
                }
            }
        }
        $data = $paginator->paginate($certificationService->listPartnershipOrderedQueryBuilder(
            $organism,
            $parameters['query'] ?? null,
            $parameters['certificationTypes'] ?? [],
            $states,
            $parameters['defaultCertifier'] ?? null,
            $isPromoted,
        ), intval($parameters['page']), intval($parameters['limit']), ['distinct' => false]);
        // map selected count to certification
        $view = $this->view(array_map(function (array $item) {
            return $item[0]->setPartnerCount($item['partnerCount']);
        }, (array)$data->getItems()), 200);

        $context = new Context();
        $context->addGroups(['partnership']);
        $view->setContext($context);

        // on n'utilise pas le $data->getTotalItemCount() habituel car il compte sans distinct => obligation d'utiliser une méthode spécifique pour faire le count correct
        $view->setHeader("x-total-count", $certificationService->countAllPartnershipOrderedQueryBuilder(
            $organism,
            $parameters['query'] ?? null,
            $parameters['certificationTypes'] ?? [],
            $states,
            $parameters['defaultCertifier'] ?? null,
            $isPromoted));
        $view->setHeader("x-current-page", $data->getCurrentPageNumber());
        $view->setHeader("x-item-per-page", $data->getItemNumberPerPage());
        return $this->handleView($view);
    }

    /**
     * @Rest\Get("/api/certifications/{certifInfo}")
     * @Rest\Get("/app/attendees/certifications/{certifInfo}")
     * @Rest\QueryParam(name="refresh", requirements=@Assert\Choice({"false", "true"}), default="false")
     * @Security("is_granted('view', certification) or is_granted('attendeeView', certification)", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"certifications", "default"})
     * @OA\Get (
     *     summary="Récupération d'une certification.",
     *     description="Récupération d'une certification par son numéro 'Certif Info'. Via OAuth2, cet appel nécessite le scope 'certification:read'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations de la certification.",
     *     @ApiDoc\Model(type=Certification::class))
     *     )
     * )
     * @OA\Parameter(
     *     name="certifInfo",
     *     in="path",
     *     description="Le numéro 'Certif Info' de la certification",
     *     @OA\Schema(type="string")
     * )
     *
     * @param Certification $certification
     * @param CertificationService $certificationService
     * @param ParamFetcherInterface $paramFetcher
     * @return Response
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function show(Certification $certification, CertificationService $certificationService, ParamFetcherInterface $paramFetcher): Response
    {
        $user = $this->getUser();
        $context = new Context();

        $parameters = $paramFetcher->all(true);
        $refresh = filter_var($parameters['refresh'], FILTER_VALIDATE_BOOLEAN);
        if ($refresh) {
            $certification = $certificationService->getCertification(['certifInfo' => $certification->getCertifInfo()], ['refresh' => true]);
        }

        if ($user instanceof Attendee) {
            $context->addGroup('attendee');
        } else if ($user instanceof User) {
            if (!$certification->isCertifier($user->getMainOrganism())) {
                $context->addGroup('partnerFiles');
            }
            $context->addGroup('Default');
        } else {
            throw new LogicException("Classe du user inconnue.");
        }

        $view = $this->view($certification, 200);
        $view->setContext($context);
        return $this->handleView($view);
    }

    /**
     * @Rest\Post ("/api/certifications")
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_CERTIFICATION:WRITE')", message="not allowed")
     * @Rest\View(StatusCode = 201)
     *
     * @OA\Post (
     *     summary="Créer une certification",
     *     description="Permet de créer une nouvelle certification interne dans Wedof."
     * )
     * @OA\Response(
     *     response=201,
     *     description="Un json contenant les informations de la nouvelle certification créée",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/Certification")
     *     )
     * )
     *
     * @OA\RequestBody(
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/CertificationCreateBody")
     *     )
     * )
     *
     * @param Request $request
     * @param CertificationService $certificationService
     * @return Certification|View
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     * @throws WedofSubscriptionException
     */
    public function create(Request $request, CertificationService $certificationService)
    {
        /** @var User $user */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();
        if (!$organism->getSubscription()->isAllowCertifierPlus()) {
            throw new WedofSubscriptionException("Erreur, votre abonnement ne permet pas la création d'une nouvelle certification.");
        }
        $body = json_decode($request->getContent(), true);
        $violations = $this->validateCreateBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }
        $certifInfo = Tools::generateRandomString(10, false, false);
        $code = Tools::generateRandomString(4, false, false);
        $body = [
            'enabled' => true,
            'name' => $body['name'],
            'autoRegistering' => false,
            'allowPartnershipRequest' => false,
            'certifiers' => [
                'certifiers' => [
                    'siret' => $organism->getSiret()
                ]
            ],
            'certifInfo' => 'tmp-cert-' . crc32($certifInfo),
            'level' => "<p>aucun</p>",
            'idInfos' => [
                'type' => CertificationTypes::INTERNAL(),
                'code' => $code,
                'link' => isset($body['link']) ? $body['link'] : null
            ],
            'dataProvider' => DataProviders::INTERNAL()
        ];
        return $certificationService->createOrUpdate($body);
    }

    /**
     * @Rest\Route("/app/certifications/{certifInfo}", methods={"POST", "PUT"})
     * @Rest\Put("/api/certifications/{certifInfo}")
     * @IsGranted(CertificationVoter::EDIT, subject="certification",  message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @OA\Put (
     *     summary="Met à jour une certification",
     *     description="Permet de mettre à jour la période de validité et le mode d'obtention de la certification"
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations de la certification.",
     *     @ApiDoc\Model(type=Certification::class))
     * )
     *
     * @param Certification $certification
     * @param CertificationService $certificationService
     * @return Certification|View
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws WedofSubscriptionException
     * @throws \Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface
     */
    public function update(Certification $certification, CertificationService $certificationService)
    {
        $body = $this->getData();
        if (empty($body)) {
            throw new WedofBadRequestHttpException("Veuillez renseigner un body.");
        }
        if (!empty($body['amountHt']) && is_numeric($body['amountHt'])) {
            $body['amountHt'] = (float)($body['amountHt']);
        }
        /** @var User $user */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();
        $subscription = $organism->getSubscription();
        if (!$subscription->isAllowCertifierPlus() && !empty($body['partnershipComment']) && $body['partnershipComment'] != $certification->getPartnershipComment()) {
            throw new WedofSubscriptionException("Erreur, votre abonnement ne permet pas la modification du champ 'partnershipComment'.");
        }
        $violations = $this->validateUpdateBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }
        if (isset($body['link']) && !in_array($certification->getType(), CertificationTypes::INTERNAL()->getValue(), CertificationTypes::PREVENTION()->getValue())) {
            unset($body['link']);
        }
        if (!empty($body['allowPartialSkillSets']) && $certification->getType() !== CertificationTypes::RNCP()->getValue()) {
            throw new WedofBadRequestHttpException('Erreur, l\'enseignement ne peut être divisé par blocs de compétences que sur les certifications RNCP.');
        }
        return $certificationService->update($certification, $organism, $body, $subscription->isAllowCertifierPlus());
    }

    /**
     * @Rest\Delete("/api/certifications/{certifInfo}")
     * @Security("is_granted('ROLE_ADMIN') or is_granted('ROLE_ALLOWED_TO_SWITCH') or is_granted('IS_IMPERSONATOR')", message="not allowed")
     * @Rest\View(StatusCode = 204)
     *
     * @param Certification $certification
     * @param CertificationPartnerService $certificationPartnerService
     * @param CertificationService $certificationService
     * @param CertificationFolderService $certificationFolderService
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function delete(Certification $certification, CertificationPartnerService $certificationPartnerService, CertificationService $certificationService, CertificationFolderService $certificationFolderService)
    {
        if (!in_array($certification->getType(), CertificationTypes::INTERNAL()->getValue(), CertificationTypes::PREVENTION()->getValue())) {
            throw new WedofBadRequestHttpException("Erreur, vous ne pouvez pas supprimer la certification " . $certification->getExternalId() . " associée au certifInfo " . $certification->getCertifInfo());
        }
        $partnerCount = $certificationPartnerService->getPartnersCount($certification, null, false);
        if ($partnerCount > 0) {
            throw new WedofBadRequestHttpException("Erreur, il n'est pas possible de supprimer cette certification car des partenariats ont été créés.");
        }
        $certificationFolders = $certificationFolderService->countByCertification($certification);
        if ($certificationFolders > 0) {
            throw new WedofBadRequestHttpException("Erreur, il n'est pas possible de supprimer cette certification car des dossiers de certification ont été créés.");
        }
        $certificationService->delete($certification);
    }

    /**
     * This synchronous method is here for dev tests only, it is not publicly documented
     * @Rest\Route("/api/certifications/{certifInfo}/synchronizeCertificationPartnersForDev", methods={"POST", "PUT"})
     * @Rest\View(StatusCode = 200)
     * @Security("is_granted('ROLE_ADMIN')", message="not allowed")
     *
     * @param Certification $certification
     * @param FranceCompetencesApiService $franceCompetencesApiService
     * @return void
     *
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws Exception
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws WedofConnectionException
     * @throws TransportExceptionInterface
     */
    public function synchronizeCertificationPartnersForDev(Certification $certification, FranceCompetencesApiService $franceCompetencesApiService): Response
    {
        if (in_array($certification->getType(), [CertificationTypes::INTERNAL()->getValue(), CertificationTypes::PREVENTION()->getValue()])) {
            throw new WedofBadRequestHttpException("Erreur, il n'est pas possible de synchroniser les partenaires sur une certification interne");
        }
        $franceCompetencesApiService->synchronizeCertificationPartners($certification);
        return new Response('Done', 200);
    }

    /**
     * @Rest\Get("/api/certifications/{certifInfo}/certificateTemplate")
     * @IsGranted(CertificationVoter::EDIT, subject="certification",  message="not allowed")
     * @Rest\View(StatusCode = 200)
     * @param Certification $certification
     * @param DownloadHandler $downloadHandler
     * @return Response
     */
    public function certificateTemplate(Certification $certification, DownloadHandler $downloadHandler): Response
    {
        if ($certification->getCertificateTemplate()) {
            return $downloadHandler->downloadObject($certification, 'certificateTemplateFile');
        } else {
            throw new WedofNotFoundHttpException("Le modèle de parchemin n'a pas été trouvé");
        }
    }

    /**
     * @Rest\Post("/api/certifications/{id}/files")
     * @IsGranted(CertificationVoter::EDIT, subject="certification", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @param Certification $certification
     * @param Request $request
     * @param CertificationFileService $certificationFileService
     * @return CertificationFile[]|Collection
     * @throws Throwable
     */
    public function uploadFile(Certification $certification, Request $request, CertificationFileService $certificationFileService): Collection
    {
        $typeId = $request->get('typeId');
        $title = $request->get('title');
        $url = $request->get('fileToDownload');
        if ($url) {
            $isValidUrl = filter_var($url, FILTER_VALIDATE_URL);
            if (!$isValidUrl) {
                throw new WedofBadRequestHttpException("Erreur, le champ 'fileToDownload' n'est pas une URL valide.");
            }
            $fullFileName = pathinfo($url, PATHINFO_FILENAME) . '.' . pathinfo($url, PATHINFO_EXTENSION);
            $newFile = sys_get_temp_dir() . '/' . $fullFileName;
            copy($url, $newFile);
            $mimeType = mime_content_type($newFile);
            $file = new UploadedFile($newFile, $fullFileName, $mimeType, null, true);
        } else {
            $file = !empty($request->files->all()) ? $request->files->get('file') : $request->get('file');
        }
        if (!empty($typeId) && !empty($file)) {
            $certificationFileService->create($file, intval($typeId), $certification, $title);
            return $certification->getFiles();
        } else {
            throw new WedofBadRequestHttpException("Erreur, les attributs 'typeId' et 'file' sont obligatoires");
        }
    }

    /**
     * @Rest\Get("/api/certifications/{id}/files/{certificationFileId}")
     * @IsGranted(CertificationVoter::VIEW, subject="certification")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"certifications", "default"})
     * @OA\Get (
     *      summary="Télécharger un document",
     *      description="Télécharger le document d'une certification à l'aide de son id et de l'id du document."
     *  )
     * @OA\Response(
     *      response=200,
     *      description="Document à télécharger.",
     *  )
     * @OA\Parameter(
     *       name="id",
     *       in="path",
     *       description="id de la certification",
     *       @OA\Schema(type="string")
     *   )
     * @OA\Parameter(
     *        name="certificationFileId",
     *        in="path",
     *        description="id du document",
     *        @OA\Schema(type="string")
     *    )
     * @param Request $request
     * @param Certification $certification
     * @param CertificationFileService $certificationFileService
     * @param CertificationFileRepository $repository
     * @return array|string[]|StreamedResponse
     */
    public function downloadFile(Request $request, Certification $certification, CertificationFileService $certificationFileService, CertificationFileRepository $repository)
    {
        $certificationFileId = $request->get('certificationFileId');
        $certificationFile = $repository->findOneBy(['certification' => $certification, 'id' => $certificationFileId]);
        if ($certificationFile) {
            return $certificationFileService->download($certificationFile);
        } else {
            throw new WedofNotFoundHttpException("Aucun fichier d'id $certificationFileId n'existe pour la certification.");
        }
    }

    /**
     * @Rest\Delete("/api/certifications/{id}/files/{certificationFileId}")
     * @IsGranted(CertificationVoter::EDIT, subject="certification", message="not allowed")
     * @Rest\View(StatusCode = 204)
     *
     * @param Certification $certification
     * @param CertificationFileRepository $repository
     * @param Request $request
     * @param CertificationFileService $certificationFileService
     */
    public function deleteFile(Certification $certification, CertificationFileRepository $repository, Request $request, CertificationFileService $certificationFileService)
    {
        $certificationFileId = $request->get('certificationFileId');
        $certificationFile = $repository->findOneBy(['certification' => $certification, 'id' => $certificationFileId]);
        if ($certificationFile) {
            $certificationFileService->delete($certificationFile);
        } else {
            throw new WedofNotFoundHttpException();
        }
    }

    /**
     * @Rest\Post("/app/certifications/{certifInfo}/activateAudits")
     * @IsGranted(CertificationVoter::EDIT, subject="certification", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @param Certification $certification
     * @param CertificationService $certificationService
     * @return Certification|View
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws WedofSubscriptionException
     * @throws \Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface
     */
    public function activateAudits(Certification $certification, CertificationService $certificationService)
    {
        /** @var User $user */
        $user = $this->getUser();
        $body = $this->getData();
        $violations = $this->validateActivateAuditsBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }
        $isAnnualSubscription = isset($body['isAnnualSubscription']) ? $body['isAnnualSubscription'] : false;
        return $certificationService->activateAudits($certification, $user->getMainOrganism(), $isAnnualSubscription);
    }

    /**
     * @Rest\Post("/app/certifications/{certifInfo}/deactivateAudits")
     * @IsGranted(CertificationVoter::EDIT, subject="certification", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @param Certification $certification
     * @param CertificationService $certificationService
     * @return Certification
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws WedofSubscriptionException
     * @throws \Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface
     */
    public function deactivateAudits(Certification $certification, CertificationService $certificationService): Certification
    {
        /** @var User $user */
        $user = $this->getUser();
        return $certificationService->cancelAudits($certification, $user->getMainOrganism());
    }

    /**
     * @Rest\Get("/api/certifications/{certifInfo}/details")
     * @IsGranted(CertificationVoter::EDIT, subject="certification", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @param Certification $certification
     * @param CertificationService $certificationService
     * @return CertificationStatistics
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws WedofSubscriptionException
     */
    public function details(Certification $certification, CertificationService $certificationService): CertificationStatistics
    {
        /* @var $user User */
        $user = $this->getUser();
        $currentOrganism = $user->getMainOrganism();
        if (!$currentOrganism->getSubscription() || !$currentOrganism->getSubscription()->isAllowCertifiers()) {
            throw new WedofSubscriptionException("Erreur, en tant que certificateur, vous devez avoir un abonnement pour visualiser les statistiques de la certification.");
        }
        return $certificationService->getStatistics($certification);
    }

    //----------------
    // METHODES PRIVES
    //----------------

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateUpdateBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'allowExtraFields' => true,
            'fields' => [
                'amountHt' => new Assert\Optional(new Assert\Type('float')),
                'validityPeriod' => new Assert\Optional(new Assert\Type('integer')),
                'obtentionSystem' => new Assert\Optional([new Assert\Choice([CertificationObtentionSystem::PAR_ADMISSION()->getValue(), CertificationObtentionSystem::PAR_SCORING()->getValue()])]),
                'examinationType' => new Assert\Optional([new Assert\Choice([CertificationExaminationType::MIXTE()->getValue(), CertificationExaminationType::EN_PRESENTIEL()->getValue(), CertificationExaminationType::A_DISTANCE()->getValue()])]), // if filled the CF will inherit on this type
                'partnershipComment' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 5000])]),
                'autoRegistering' => new Assert\Optional(new Assert\Type('bool')),
                'allowPartnershipRequest' => new Assert\Optional(new Assert\Type('bool')),
                'allowGenerateXmlAutomatically' => new Assert\Optional(new Assert\Type('bool')),
                'link' => new Assert\Optional([new Assert\Url(), new Assert\Length(['max' => 255])]),
                'surveyOptional' => new Assert\Optional(new Assert\Type('bool')),
                'allowPartialSkillSets' => new Assert\Optional(new Assert\Type('bool'))
            ]
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateCreateBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'name' => new Assert\Required([new Assert\NotBlank(), new Assert\Type('string')]),
            'link' => new Assert\Optional([new Assert\Url(), new Assert\Length(['max' => 255])])
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateActivateAuditsBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'isAnnualSubscription' => new Assert\Optional([new Assert\Type('boolean')])
        ]);
        return $validator->validate($body, $constraints);
    }
}
