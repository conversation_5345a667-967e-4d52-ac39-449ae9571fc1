<?php
// src/Controller/api/TrainingActionController.php
namespace App\Controller\api;

use App\Entity\TrainingAction;
use App\Entity\User;
use App\Exception\WedofAccessDeniedHttpException;
use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofNotFoundHttpException;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\Tools;
use App\Security\Voter\CertificationVoter;
use App\Security\Voter\OrganismVoter;
use App\Security\Voter\ProposalVoter;
use App\Security\Voter\TrainingVoter;
use App\Service\CertificationService;
use App\Service\DataProviders\BaseApiService;
use App\Service\DataProviders\CpfApiService;
use App\Service\OrganismService;
use App\Service\ProposalService;
use App\Service\TrainingActionService;
use App\Service\TrainingService;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use ErrorException;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcherInterface;
use Knp\Component\Pager\PaginatorInterface;
use Nelmio\ApiDocBundle\Annotation as ApiDoc;
use OpenApi\Annotations as OA;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\Validator\Constraints as Assert;
use Throwable;

/**
 * Class TrainingActionController
 * @package App\Controller\api
 *
 * @OA\Tag(name="TrainingAction")
 * @ApiDoc\Security(name="accessCode")
 *
 */
class TrainingActionController extends AbstractFOSRestController
{
    //-------------------
    // METHODES PUBLIQUES
    //-------------------
    /**
     * @Rest\Get("/api/trainingActions/{externalId}/{dataProvider}", requirements={"externalId"=".+"})
     * @Rest\Get("/api/trainingActions/{externalId}", requirements={"externalId"=".+"})
     * @IsGranted("ROLE_USER", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"trainingActions", "default"})
     * @OA\Get (
     *     summary="Récupération d'une action de formation.",
     *     description="Récupération d'une action de formation par son ID. Via OAuth2, cet appel nécessite le scope 'trainingaction:read'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations d'une action de formation.",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/TrainingAction")
     *     )
     * )
     * @OA\Parameter(
     *     name="externalId",
     *     in="path",
     *     description="ID de l'action de formation",
     *     @OA\Schema(type="string")
     * )
     *
     * @param TrainingActionService $trainingActionService
     * @param string $externalId
     * @param string|null $dataProvider
     * @return TrainingAction|Response
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function show(TrainingActionService $trainingActionService, string $externalId, string $dataProvider = null)
    {
        try {
            $_dataProvider = $dataProvider ? DataProviders::from($dataProvider) : null;
        } catch (Throwable $exception) {
            throw new BadRequestHttpException("DataProvider inconnu.");
        }
        $options = $_dataProvider ? ['createIfNotExist' => true] : [];
        try {
            return $trainingActionService->getByExternalId($externalId, $_dataProvider, $options);
        } catch (ErrorException $e) {
            if (Tools::isNoResultException($e)) {
                return new Response(null, 404);
            } else {
                throw $e;
            }
        }
    }

    /**
     * @Rest\Get("/api/trainingActions")
     * @Rest\Get("/funnel/api/trainingActions", name="listTrainingActionsThroughFunnel")
     * @Security("is_granted('ROLE_SALES') or is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_TRAININGACTION:READ')", message="not allowed")
     * @Rest\View(StatusCode = 200)
     * @Rest\QueryParam(name="proposalCode", requirements=@Assert\Type("string"), nullable=true, description="Permet de n'obtenir que les actions de formations associés à la proposition considérée")
     * @Rest\QueryParam(name="proposalId", requirements=@Assert\Type("string"), nullable=true, description="DEPRECATED Permet de n'obtenir que les actions de formations associés à la proposition considérée")
     * @Rest\QueryParam(name="certifInfo", requirements=@Assert\Type("string"), nullable=true, description="Permet de n'obtenir que les actions de formations appartenant à la certification considérée - par défaut toutes les certifications.")
     * @Rest\QueryParam(name="siret", requirements=@Assert\Type("string"), nullable=true, description="Permet de n'obtenir que les actions de formations appartenant à l'organisme de siret considéré - par défaut l'organisme de l'utilisateur courant.")
     * @Rest\QueryParam(name="trainingId", requirements=@Assert\Type("string"), nullable=true, description="Permet de n'obtenir que les actions de formations associées à la formation considérée.")
     * @Rest\QueryParam(name="state", requirements=@Assert\Type("string"), nullable=true, description="Permet de n'obtenir que les actions de formations dans l'état demandé (draft, published, unpublished, archived)")
     * @Rest\QueryParam(name="eligible", requirements=@Assert\Choice({"false", "true"}), nullable=true, description="Permet de n'obtenir que les actions de formations éligibles (elles sont rendues inéligibles quand la certification expire)")
     * @Rest\QueryParam(name="query", requirements=@Assert\Type("string"), nullable=true, description="Permet de filtrer les actions de formations par le nom de la formation ou son identifiant")
     * @Rest\QueryParam(name="dataProvider", requirements=@Assert\Choice({"cpf", "kairosAif", "opcoCfa", "all"}), default="all", description="Permet de filtrer les actions de formations par financeur.")
     *
     * @Rest\QueryParam(name="limit", requirements="\d+", default="100", description="Nombre d'éléments retourné par requête - par défaut 100.")
     * @Rest\QueryParam(name="page", requirements="\d+", default="1", description="Numéro de page de la requête - par défaut la première.")
     * @Rest\QueryParam(name="order", requirements=@Assert\Choice({"asc", "desc"}), default="desc", description="Tri les résultats par ordre ascendant ou descendant - par défaut descendant.")
     *
     * @ApiDoc\Areas({"trainingActions", "default"})
     * @OA\Get (
     *     summary="Liste toutes les actions de formation de l'organisme de l'utilisateur courant. Peut aussi lister les actions de formation de manière filtrée.",
     *     description="Récupère l'ensemble des actions de formation liées à l'organisme de l'utilisateur connecté. NOTA : les paramètres sont cumulatifs et deux paramètres incompatibles renverront un résultat vide. Via OAuth2, cet appel nécessite le scope 'trainingaction:read'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un tableau d'actions de formation au format JSON.",
     *     @OA\JsonContent(
     *          type="array",
     *          @OA\Items(ref="#/components/schemas/TrainingAction")
     *     )
     * )
     * @OA\Parameter (name="order", in="query", @OA\Schema (ref="#/components/schemas/Order"))
     *
     *
     * @param TrainingActionService $trainingActionService
     * @param OrganismService $organismService
     * @param TrainingService $trainingService
     * @param CertificationService $certificationService
     * @param ProposalService $proposalService
     * @param PaginatorInterface $paginator
     * @param ParamFetcherInterface $paramFetcher
     * @return Response
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function list(TrainingActionService $trainingActionService, OrganismService $organismService, TrainingService $trainingService, CertificationService $certificationService, ProposalService $proposalService, PaginatorInterface $paginator, ParamFetcherInterface $paramFetcher): Response
    {
        /* @var $user User */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();
        $parameters = $paramFetcher->all(true);

        if (isset($parameters['siret'])) {
            $organism = $organismService->getBySiret($parameters['siret']);
            if (!empty($organism)) {
                if (!$this->isGranted(OrganismVoter::VIEW, $organism)) {
                    throw new WedofAccessDeniedHttpException("Vous n'êtes pas autorisé à avoir accès au contenu de l'organisme associé au siret " . $parameters['siret']);
                }
            } else {
                throw new WedofNotFoundHttpException("L'organisme associé au siret " . $parameters['siret'] . " n'a pas été trouvé.");
            }
        }
        if (isset($parameters['certifInfo'])) {
            $certification = $certificationService->getByCertifInfo($parameters['certifInfo']);
            if (!empty($certification)) {
                if (!$this->isGranted(CertificationVoter::VIEW_ADVANCED, $certification)) {
                    throw new WedofAccessDeniedHttpException("Vous n'êtes pas autorisé à avoir accès au contenu de la certification associée au certifInfo " . $parameters['certifInfo']);
                }
            } else {
                throw new WedofNotFoundHttpException("La certification associée au certifInfo " . $parameters['certifInfo'] . " n'a pas été trouvée.");
            }
        }
        if (isset($parameters['trainingId'])) {
            $training = $trainingService->getByExternalId($parameters['trainingId']);
            if ($training) {
                if (!$this->isGranted(TrainingVoter::VIEW, $training)) {
                    throw new WedofAccessDeniedHttpException("Non autorisé");
                }
            } else {
                throw new WedofNotFoundHttpException("La formation associée à l'id " . $parameters['trainingId'] . " n'a pas été trouvée.");
            }
        }
        //DEPRECATED on 17/09/2024
        if (isset($parameters['proposalId'])) {
            $proposal = $proposalService->getById($parameters['proposalId']);
            if (!empty($proposal)) {
                if (!$this->isGranted(ProposalVoter::VIEW, $proposal)) {
                    throw new WedofAccessDeniedHttpException("Vous n'êtes pas autorisé à accéder à la proposition associée à l'id " . $parameters['proposalId']);
                }
            } else {
                throw new WedofNotFoundHttpException("La proposition associée à l'id " . $parameters['proposalId'] . " n'a pas été trouvée.");
            }
        }
        if (isset($parameters['proposalCode'])) {
            $proposal = $proposalService->getByCode($parameters['proposalCode']);
            if (!empty($proposal)) {
                if (!$this->isGranted(ProposalVoter::VIEW, $proposal)) {
                    throw new WedofAccessDeniedHttpException("Vous n'êtes pas autorisé à accéder à la proposition associée à l'id " . $parameters['proposalId']);
                }
            } else {
                throw new WedofNotFoundHttpException("La proposition associée au " . $parameters['proposalCode'] . " n'a pas été trouvée.");
            }
        }

        $dataProvider = $parameters['dataProvider'] != 'all' ? DataProviders::from($parameters['dataProvider']) : null;
        $data = $paginator->paginate($trainingActionService->listReturnQueryBuilder($organism, $parameters), intval($parameters['page']), intval($parameters['limit']));
        $trainingActions = $data->getItems();

        // Try to grab new trainingAction remotely from query
        // TODO should probably be removed / reworked as it works only for CPF
        if (isset($parameters['query']) && trim($parameters['query']) != '' && (!$dataProvider || $dataProvider->getValue() === DataProviders::CPF()->getValue())) {
            $siretPrefix = $organism->getSiret() . '_';
            $query = trim($parameters['query']);
            try {
                if (!$trainingActions) {
                    /** @var CpfApiService $apiService */
                    $apiService = BaseApiService::getCatalogApiServiceByDataProvider(DataProviders::CPF());
                    $trainingId = $apiService->getTrainingExtIdFromTrainingActionExtId($query);
                    $trainingActionId = $apiService->getShortTrainingActionExtId($query);
                    if ($trainingId && $trainingActionId) {
                        $fullExternalId = $siretPrefix . $trainingId . '/' . $siretPrefix . $trainingActionId;
                        $trainingActionCreated = $trainingActionService->getByExternalId($fullExternalId, DataProviders::CPF(), ['createIfNotExist' => true, 'organism' => $organism]);
                        if ($trainingActionCreated) {
                            $data = $paginator->paginate($trainingActionService->listReturnQueryBuilder($organism, $parameters), intval($parameters['page']), intval($parameters['limit']));
                            $trainingActions = $data->getItems();
                        }
                    }
                } else if (count((array)$trainingActions) === 1) {
                    /** @var TrainingAction $trainingAction */
                    $trainingAction = $trainingActions[0];
                    if ($trainingAction->getTraining()->getDataProvider() === DataProviders::CPF()->getValue()) {
                        $fullExternalId = $trainingAction->getExternalId();
                        $lightExternalId = str_replace($siretPrefix, '', $fullExternalId);
                        if ($query === $lightExternalId) {
                            $trainingActionUpdated = $trainingActionService->getByExternalId($fullExternalId, DataProviders::CPF(), ['createIfNotExist' => true, 'organism' => $organism, 'daysMaxSinceLastUpdate' => 0]); // Force refresh regardless of lastUpdate
                            $trainingActions = [$trainingActionUpdated];
                        }
                    }
                }
            } catch (Throwable $t) {
            }
        }

        $view = $this->view($trainingActions, 200);
        $view->setHeader("x-total-count", $data->getTotalItemCount());
        $view->setHeader("x-current-page", $data->getCurrentPageNumber());
        $view->setHeader("x-item-per-page", $data->getItemNumberPerPage());
        return $this->handleView($view);
    }

    /**
     * @Rest\Post ("/api/trainingActions")
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_TRAININGACTION:WRITE')", message="not allowed")
     * @Rest\View(StatusCode = 501)
     *
     * @return WedofBadRequestHttpException
     */
    public function create(): WedofBadRequestHttpException
    {
        throw new WedofBadRequestHttpException("Méthode non supportée - Une nouvelle action de formation ne peut être créée.");
    }

    /**
     * @Rest\Put("/api/trainingActions/{externalId}", requirements={"externalId"=".+"})
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_TRAININGACTION:WRITE')", message="not allowed")
     * @Rest\View(StatusCode = 501)
     *
     * @param TrainingAction $trainingAction
     * @return WedofBadRequestHttpException
     */
    public function update(TrainingAction $trainingAction): WedofBadRequestHttpException
    {
        throw new WedofBadRequestHttpException("Méthode non supportée - L'action de formation associée à l'id " . $trainingAction->getExternalId() . " ne peut être modifiée.");
    }

    /**
     * @Rest\Delete("/api/trainingActions/{externalId}", requirements={"externalId"=".+"})
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_TRAININGACTION:WRITE')", message="not allowed")
     * @Rest\View(StatusCode = 501)
     *
     * @param TrainingAction $trainingAction
     * @return WedofBadRequestHttpException
     */
    public function delete(TrainingAction $trainingAction): WedofBadRequestHttpException
    {
        throw new WedofBadRequestHttpException("Méthode non supportée - L'action de formation associée à l'id " . $trainingAction->getExternalId() . " ne peut être supprimée.");
    }
}
