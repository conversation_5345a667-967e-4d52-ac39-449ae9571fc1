<?php
// src/Controller/api/RegistrationFolderReasonController.php
namespace App\Controller\api;

use App\Entity\RegistrationFolderReason;
use App\Service\RegistrationFolderReasonService;
use Doctrine\Common\Collections\ArrayCollection;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcherInterface;
use Nelmio\ApiDocBundle\Annotation as ApiDoc;
use OpenApi\Annotations as OA;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Validator\Constraints as Assert;

/**
 * Class RegistrationFolderReasonController
 * @package App\Controller\api
 *
 * @OA\Tag(name="Registration Folder Reason")
 * @ApiDoc\Security(name="accessCode")
 */
class RegistrationFolderReasonController extends AbstractController
{
    /**
     * @Rest\Get("/api/registrationFoldersReasons")
     * @Rest\QueryParam(name="type", requirements=@Assert\Choice({"terminated", "refused", "canceled"}), default="terminated", description="Permet de récupérer les raisons liés à l'évènement considéré - par défaut 'terminated'.")
     * @Rest\QueryParam(name="withObsoletes", requirements=@Assert\Choice({"true", "false"}), default="false", description="Inclu aux résultats les raisons obsolètes - par défaut 'false'.")
     * @IsGranted("ROLE_USER", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"registrationFolderReasons", "default"})
     * @OA\Get (
     *     summary="Liste les raisons liées aux actions cancel/refuse/terminate de '/api/registrationFolders'.",
     *     description="Récupère les raisons possible pour le refus d'un dossier, l'annulation d'un dossier ou la fin de formation d'un dossier."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un tableau des raisons au format JSON",
     *     @OA\JsonContent(
     *          type="array",
     *          @OA\Items(ref=@ApiDoc\Model(type=RegistrationFolderReason::class))
     *     )
     * )
     * @OA\Parameter (name="type", in="query", @OA\Schema (ref="#/components/schemas/RegistrationFolderReasonType"))
     *
     * @param RegistrationFolderReasonService $registrationFolderReasonService
     * @param ParamFetcherInterface $paramFetcher
     * @return ArrayCollection: ArrayCollection
     */
    public function list(RegistrationFolderReasonService $registrationFolderReasonService, ParamFetcherInterface $paramFetcher): ArrayCollection
    {
        $parameters = $paramFetcher->all(true);
        $withObsoletes = filter_var($parameters['withObsoletes'], FILTER_VALIDATE_BOOLEAN);
        if ($parameters['type'] === 'terminated') {
            return $registrationFolderReasonService->getAllReasonsTerminated($withObsoletes);
        } else if ($parameters['type'] === 'refused') {
            return $registrationFolderReasonService->getAllReasonsRefused($withObsoletes);
        } else {
            return $registrationFolderReasonService->getAllReasonsCanceled($withObsoletes);
        }
    }
}
