<?php

namespace App\Controller\api;

use App\Entity\Organism;
use App\Entity\Proposal;
use App\Entity\User;
use App\Exception\WedofAccessDeniedHttpException;
use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofNotFoundHttpException;
use App\Exception\WedofSubscriptionException;
use App\Library\utils\enums\ProposalDiscountTypes;
use App\Library\utils\enums\ProposalStates;
use App\Library\utils\Tools;
use App\Repository\ProposalRepository;
use App\Security\Voter\OrganismVoter;
use App\Security\Voter\ProposalVoter;
use App\Service\OrganismService;
use App\Service\ProposalService;
use DateTime;
use DateTimeZone;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use ErrorException;
use Exception;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcherInterface;
use FOS\RestBundle\View\View;
use Knp\Component\Pager\PaginatorInterface;
use Nelmio\ApiDocBundle\Annotation as ApiDoc;
use OpenApi\Annotations as OA;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validation;
use Throwable;

/**
 * Class ProposalController
 * @package App\Controller\api
 *
 * @OA\Tag(name="Proposal")
 * @ApiDoc\Security(name="accessCode")
 *
 */
class ProposalController extends AbstractWedofController
{
//-------------------
    // METHODES PUBLIQUES
    //-------------------
    /**
     * @Rest\Get("/api/proposals/{id}", requirements={"id" = "\d+"}, name="byId")
     * @Rest\Get("/api/proposals/{code}", name="byCode")
     * @Rest\Get("/funnel/api/proposals/{code}", name="getThroughFunnel")
     * @IsGranted(ProposalVoter::VIEW, subject="proposal",  message="not allowed")
     * @Rest\View(statusCode = 200)
     *
     * @ApiDoc\Areas({"proposals", "default"})
     * @OA\Get (
     *     summary="Récupération d'une proposition.",
     *     description="Récupération d'une proposition par son ID. Via OAuth2, cet appel nécessite le scope 'proposal:read'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations de la proposition recherchée.",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/Proposal")
     *     )
     * )
     * @OA\Parameter(
     *     name="id",
     *     in="path",
     *     description="ID de la proposition",
     *     @OA\Schema(type="string")
     * )
     * @param Proposal $proposal
     * @return Proposal
     */
    public function show(Proposal $proposal): Proposal
    {
        return $proposal;
    }

    /**
     * @Rest\Get("/app/proposals/revenue/{columnId}")
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_PROPOSAL:READ')", message="not allowed")
     * @Rest\QueryParam(name="query", requirements=@Assert\Type("string"), nullable=true, description="Permet d'effectuer une recherche libre sur les champs 'firstName', 'lastName', 'email', 'notes', 'description', 'code', 'tags', trainingAction.training.title' et 'trainingAction.externalId'.")
     * @Rest\QueryParam(name="state", requirements=@Assert\Type("string"), default="all", description="Permet de n'obtenir que les propositions dans l'état considéré - par défaut tous les états sont retournés. Valeurs possibles : 'template', 'draft', 'active', 'viewed', 'accepted', 'refused'. ")
     * @Rest\QueryParam(name="metadata", requirements=@Assert\Type("string"), nullable=true, description="Permet d'effectuer une une valeur d'un 'metadata' format : cle:valeur.")
     * @Rest\View(StatusCode = 200)
     *
     * @param ParamFetcherInterface $paramFetcher
     * @param ProposalService $proposalService
     * @param string $columnId
     * @return float
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws WedofSubscriptionException
     */
    public function revenueByColumn(ParamFetcherInterface $paramFetcher, ProposalService $proposalService, string $columnId): float
    {
        /* @var $user User */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();
        $parameters = $paramFetcher->all(true);

        $parameters = array_merge($parameters, ['isIndividual' => true]);
        $parameters = $this->validateListParameters($organism, $parameters);

        return $proposalService->revenueByColumn($organism, $parameters, $columnId);
    }

    /**
     * @Rest\Get("/app/proposals")
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_PROPOSAL:READ')", message="not allowed")
     * @Rest\QueryParam(name="query", requirements=@Assert\Type("string"), nullable=true, description="Permet d'effectuer une recherche libre sur les champs 'firstName', 'lastName', 'email', 'notes', 'description', 'code', 'tags', trainingAction.training.title' et 'trainingAction.externalId'.")
     * @Rest\QueryParam(name="metadata", requirements=@Assert\Type("string"), nullable=true, description="Permet d'effectuer une une valeur d'un 'metadata' format : cle:valeur.")
     * @Rest\QueryParam(name="state", requirements=@Assert\Type("string"), default="all", description="Permet de n'obtenir que les propositions dans l'état considéré - par défaut tous les états sont retournés. Valeurs possibles : 'template', 'draft', 'active', 'viewed', 'accepted', 'refused'. ")
     * @Rest\QueryParam(name="columnIds", requirements=@Assert\Type("string"), nullable=false)
     * @Rest\QueryParam(name="limit", requirements="\d+", default="100")
     * @Rest\QueryParam(name="page", requirements="\d+", default="1")
     * @Rest\QueryParam(name="sort", requirements=@Assert\Choice({"stateLastUpdate", "id"}), default="stateLastUpdate", description="Tri les résultats sur un critère. Valeurs possibles: 'stateLastUpdate' (date du dernier changement d'état), 'id' (id de base de donnée) - par défaut 'stateLastUpdate'.")
     * @Rest\QueryParam(name="order", requirements=@Assert\Choice({"asc", "desc"}), default="desc", description="Tri les résultats par ordre ascendant ou descendant - par défaut descendant.")
     * @Rest\View(StatusCode = 200)
     *
     * @param ParamFetcherInterface $paramFetcher
     * @param ProposalService $proposalService
     * @return Response
     * @throws WedofSubscriptionException
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function listByColumn(ParamFetcherInterface $paramFetcher, ProposalService $proposalService): Response
    {
        /* @var $user User */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();
        $parameters = $paramFetcher->all(true);

        $columnIds = explode(',', $parameters['columnIds']);
        unset($parameters['columnIds']);

        $parameters = array_merge($parameters, ['isIndividual' => true]);
        $parameters = $this->validateListParameters($organism, $parameters);

        $data = $proposalService->listByColumn($organism, $parameters, $columnIds);

        $view = $this->view($data, 200);
        return $this->handleView($view);
    }

    /**
     * @Rest\Get("/api/proposals")
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_PROPOSAL:READ')", message="not allowed")
     * @Rest\QueryParam(name="query", requirements=@Assert\Type("string"), nullable=true, description="Permet d'effectuer une recherche libre sur les champs 'firstName', 'lastName', 'email', 'phoneNumber', 'notes', 'description', 'code', 'tags', trainingAction.training.title' et 'trainingAction.externalId'.")
     * @Rest\QueryParam(name="isIndividual", requirements=@Assert\Choice({"true", "false"}), nullable=true, description="Permet de filter les propositions individuelles des propositions génériques.")
     * @Rest\QueryParam(name="state", requirements=@Assert\Type("string"), default="all", description="Permet de n'obtenir que les propositions dans l'état considéré - par défaut tous les états sont retournés. Valeurs possibles : 'template', 'draft', 'active', 'viewed', 'accepted', 'refused'. ")
     * @Rest\QueryParam(name="metadata", requirements=@Assert\Type("string"), nullable=true, description="Permet d'effectuer une une valeur d'un 'metadata' format : cle:valeur.")
     * @Rest\QueryParam(name="format", requirements=@Assert\Type("string"), default="json", description="Permet d'obtenir une liste des dossiers de certification au format json ou csv. Valeurs possibles : 'json', 'csv'")
     * @Rest\QueryParam(name="limit", requirements="\d+", default="100")
     * @Rest\QueryParam(name="page", requirements="\d+", default="1")
     * @Rest\QueryParam(name="csvColumns", requirements=@Assert\Type("string"), nullable=true, description="Permet de choisir les colonnes souhaitées pour l'export des propositions au format csv. Valeurs possibles : 'CODE', 'PROPOSITION_GENERIQUE_ASSOCIEE', 'DOSSIER_DE_FORMATION', 'STATUT', 'DATE_EXPIRATION', 'TYPE_REDUCTION', 'MONTANT', 'LIMITE_D_UTILISATION', 'NOM', 'PRENOM', 'EMAIL', 'TELEPHONE', 'VALIDATION_AUTOMATIQUE', 'ACTION_DE_FORMATION', 'DATE_DEBUT_SESSION', 'DATE_FIN_SESSION', 'DUREE_DE_FORMATION', 'DESCRIPTION', 'NOTES', 'TAGS' .")
     * @Rest\QueryParam(name="columnId", requirements=@Assert\Type("string"), nullable=true)
     * @Rest\QueryParam(name="sort", requirements=@Assert\Choice({"stateLastUpdate", "id"}), default="stateLastUpdate", description="Tri les résultats sur un critère. Valeurs possibles: 'stateLastUpdate' (date du dernier changement d'état), 'id' (id de base de donnée) - par défaut 'stateLastUpdate'.")
     * @Rest\QueryParam(name="order", requirements=@Assert\Choice({"asc", "desc"}), default="desc", description="Tri les résultats par ordre ascendant ou descendant - par défaut descendant.")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"proposals", "default"})
     * @OA\Get (
     *     summary="Liste toutes les propositions  pour l'organisme de l'utilisateur courant.",
     *     description="Récupère l'ensemble des propositions de l'organisme de l'utilisateur connecté. Via OAuth2, cet appel nécessite le scope 'proposal:read'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un tableau des propositions au format JSON ou CSV selon le paramètre 'format'",
     *     @OA\JsonContent(
     *          type="array",
     *          @OA\Items(ref="#/components/schemas/Proposal")
     *     )
     * )
     *
     * @param EntityManagerInterface $entityManager
     * @param Request $request
     * @param ParamFetcherInterface $paramFetcher
     * @param PaginatorInterface $paginator
     * @param ProposalService $proposalService
     * @return Response
     * @throws WedofSubscriptionException
     */
    public function list(EntityManagerInterface $entityManager, Request $request, ParamFetcherInterface $paramFetcher, PaginatorInterface $paginator, ProposalService $proposalService): Response
    {
        $parameters = $paramFetcher->all(true);

        /* @var $user User */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();

        $parameters = $this->validateListParameters($organism, $parameters);

        if ($parameters['format'] === 'csv' || str_contains($request->headers->get('Accept'), 'csv')) {
            set_time_limit(300);
            $availableColumns = ['CODE', 'PROPOSITION_GENERIQUE_ASSOCIEE', 'DOSSIER_DE_FORMATION', 'STATUT', 'DATE_EXPIRATION', 'LIMITE_D_UTILISATION', 'VALIDATION_AUTOMATIQUE', 'ACTION_DE_FORMATION',
                'ACTION_DE_FORMATION_SELECTIONNEE', 'NOM', 'PRENOM', 'EMAIL', 'TELEPHONE', 'TYPE_REDUCTION', 'MONTANT', 'DATE_DEBUT_SESSION', 'DATE_FIN_SESSION', 'DUREE_DE_FORMATION',
                'DESCRIPTION', 'NOTES', 'TAGS'];

            $page = 1;
            $limit = 100;
            $tempFile = null;
            $entityManager->getConnection()->getConfiguration()->setSQLLogger(); // for perf in dev, maybe in prod ?
            $data = $paginator->paginate($proposalService->listReturnQueryBuilder($organism, $parameters), $page, $limit);
            $tempFile = Tools::convertDataToCSVFile($data, $availableColumns, $parameters['csvColumns'] ?? null, $tempFile);

            $page++;
            $nbIterations = intdiv($data->getTotalItemCount(), $limit) + 1;
            $entityManager->clear();

            while ($page <= $nbIterations) {
                $data = $paginator->paginate($proposalService->listReturnQueryBuilder($organism, $parameters), $page, $limit);
                $tempFile = Tools::convertDataToCSVFile($data, $availableColumns, $parameters['csvColumns'] ?? null, $tempFile);
                $page++;
                $entityManager->clear();
            }

            return Tools::getCsvResponse($tempFile, 'proposal');

        } else {
            $data = $paginator->paginate($proposalService->listReturnQueryBuilder($organism, $parameters), intval($parameters['page']), intval($parameters['limit']));
            $view = $this->view($data->getItems(), 200);
            $view->setHeader("x-total-count", $data->getTotalItemCount());
            $view->setHeader("x-current-page", $data->getCurrentPageNumber());
            $view->setHeader("x-item-per-page", $data->getItemNumberPerPage());
            return $this->handleView($view);
        }
    }


    /**
     * @Rest\Post("/api/proposals")
     * @Rest\Post("/funnel/api/proposals", name="createThroughFunnel")
     * @Security("is_granted('ROLE_SALES') or is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_PROPOSAL:WRITE')", message="not allowed")
     * @Rest\View(StatusCode = 201)
     *
     * @ApiDoc\Areas({"proposals", "default"})
     * @OA\Post (
     *     summary="Créer une nouvelle proposition",
     *     description="Permet de créer une nouvelle proposition. Via OAuth2, cet appel nécessite le scope 'proposal:write'"
     * )
     * @OA\Response(
     *     response=201,
     *     description="Un json contenant les informations de la nouvelle proposition créée",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/Proposal")
     *     )
     * )
     *
     * @OA\RequestBody(
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/ProposalCreateOrUpdateBody")
     *     )
     * )
     *
     *
     * @param ParamFetcherInterface $paramFetcher
     * @param OrganismService $organismService
     * @param ProposalService $proposalService
     * @param Request $request
     * @return Proposal|View
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     * @throws ErrorException
     */
    public function create(ParamFetcherInterface $paramFetcher, OrganismService $organismService, ProposalService $proposalService, Request $request)
    {
        /* @var $user User */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();
        if (!$organism->getSubscription()->isAllowProposals()) {
            throw new WedofSubscriptionException("Erreur, votre abonnement ne permet pas la création d'une proposition.");
        }

        $parameters = $paramFetcher->all(true);
        if (isset($parameters['siret'])) {
            $organism = $organismService->getBySiret($parameters['siret']);
            if (!empty($organism)) {
                if (!$this->isGranted(OrganismVoter::EDIT, $organism)) {
                    throw new WedofAccessDeniedHttpException("Vous n'êtes pas autorisé à créer une proposition pour l'organisme associé au siret " . $parameters['siret']);
                }
            } else {
                throw new WedofNotFoundHttpException("L'organisme associé au siret " . $parameters['siret'] . " n'a pas été trouvé.");
            }
        }
        $body = json_decode($request->getContent(), true);
        if (isset($body['expire'])) {
            if (strtotime($body['expire'])) {
                $body['expire'] = (new DateTime($body['expire']))->setTimezone(new DateTimeZone('Europe/Paris'));
            }
        }
        if (isset($body['sessionStartDate'])) {
            if (strtotime($body['sessionStartDate'])) {
                $body['sessionStartDate'] = new DateTime($body['sessionStartDate']);
            }
        }
        if (isset($body['sessionEndDate'])) {
            if (strtotime($body['sessionEndDate'])) {
                $body['sessionEndDate'] = new DateTime($body['sessionEndDate']);
            }
        }
        if (empty($body['sales']['email']) && $this->isGranted('ROLE_SALES')) {
            $body['sales'] = ['email' => $user->getEmail()];
        }
        if (isset($body['code'])) {
            $proposalExisting = $proposalService->getByCode($body['code']);
            if ($proposalExisting) {
                throw new WedofBadRequestHttpException("Erreur, le code " . $body['code'] . " est déjà utilisé pour une autre proposition, veuillez en choisir un autre.");
            }
        }
        if (!empty($body['metadata'])) {
            try {
                $body['metadata'] = json_decode($body['metadata'], true);
            } catch (Exception $exception) {
                throw new WedofBadRequestHttpException("Erreur, le champs metadata doit être un string au format json valide");
            }
        }
        $violations = $this->validateCreateOrUpdateBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }

        $body['clientIpAddress'] = $request->getClientIp();
        return $proposalService->create($body, $organism);
    }

    /**
     * @Rest\Route("/api/proposals/{id}", requirements={"id" = "\d+"}, name="updateById", methods={"PUT","POST"})
     * @Rest\Route("/api/proposals/{code}", name="updateByCode", methods={"PUT","POST"})
     * @Rest\Put("/funnel/api/proposals/{code}", name="updateThroughFunnel")
     * @IsGranted(ProposalVoter::EDIT, subject="proposal",  message="not allowed")
     * @Rest\View (StatusCode = 200)
     *
     * @ApiDoc\Areas({"proposals", "default"})
     * @OA\Put (
     *     path="/api/proposals/{id}",
     *     summary="Met à jour une proposition via son id",
     *     description="Permet de mettre à jour une proposition."
     * )
     * @OA\Post (
     *       path="/api/proposals/{id}",
     *     summary="Met à jour une proposition via son id",
     *     description="Permet de mettre à jour une proposition."
     * )
     * * @OA\Put (
     *     path="/api/proposals/{code}",
     *     summary="Met à jour une proposition via son code",
     *     description="Permet de mettre à jour une proposition."
     * )
     * @OA\Post (
     *       path="/api/proposals/{code}",
     *     summary="Met à jour une proposition via son code",
     *     description="Permet de mettre à jour une proposition."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations de la proposition",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/Proposal")
     *     )
     * )
     * @OA\Parameter(
     *     name="code",
     *     in="path",
     *     description="code de la proposition",
     *     @OA\Schema(type="string")
     * )
     * @OA\RequestBody(
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/ProposalCreateOrUpdateBody")
     *     )
     * )
     * @param Proposal $proposal
     * @param ProposalService $proposalService
     * @return Proposal|View
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function update(Proposal $proposal, ProposalService $proposalService)
    {
        $body = $this->getData();

        if (array_key_exists('metadata', $body)) {
            if (is_string($body['metadata'])) {
                $body['metadata'] = json_decode($body['metadata'], true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    throw new WedofBadRequestHttpException("Erreur, le champ metadata doit être un JSON valide");
                }
            }

            if (!is_array($body['metadata']) && $body['metadata'] !== null) {
                throw new WedofBadRequestHttpException("Erreur, le champ metadata doit être un JSON valide");
            }
        }
        if ($proposal->getState() == ProposalStates::TEMPLATE() && ($proposal->getIndividualProposals()->count() !== 0)) {
            $violations = $this->validateProposalTemplateUpdateBody($body);
            if (count($violations)) {
                throw new WedofBadRequestHttpException("Erreur, seul le champs metadata peut être modifié pour une proposition générique utilisée");
            }
        } else {
            if (isset($body['expire'])) {
                if (strtotime($body['expire'])) {
                    $body['expire'] = (new DateTime($body['expire']))->setTimezone(new DateTimeZone('Europe/Paris'));
                }
            }
            if (isset($body['sessionStartDate'])) {
                if (strtotime($body['sessionStartDate'])) {
                    $body['sessionStartDate'] = new DateTime($body['sessionStartDate']);
                }
            }
            if (isset($body['sessionEndDate'])) {
                if (strtotime($body['sessionEndDate'])) {
                    $body['sessionEndDate'] = new DateTime($body['sessionEndDate']);
                }
            }

            $violations = $this->validateCreateOrUpdateBody($body);
            if (count($violations)) {
                return $this->view($violations, Response::HTTP_BAD_REQUEST);
            }
        }

        return $proposalService->update($proposal, $body);
    }

    /**
     * @Rest\Delete("/api/proposals/{code}")
     * @Rest\View(StatusCode = 204)
     * @IsGranted(ProposalVoter::EDIT, subject="proposal",  message="not allowed")
     *
     * @ApiDoc\Areas({"proposals", "default"})
     * @OA\Delete (
     *     summary="Supprimer une proposition",
     *     description="Supprimer une proposition via le code. Via OAuth2, cet appel nécessite le scope 'proposal:write'."
     * )
     * @OA\Response(
     *     response=204,
     *     description="Aucun contenu retourné."
     * )
     * @OA\Parameter(
     *     name="code",
     *     in="path",
     *     description="code de la proposition",
     *     @OA\Schema(type="string")
     * )
     *
     * @param Proposal $proposal
     * @param ProposalService $proposalService
     * @return void
     */
    public function delete(Proposal $proposal, ProposalService $proposalService)
    {
        $proposalService->delete($proposal);
    }

    /**
     * @Rest\Post("/api/proposals/{code}/viewed")
     * @Rest\View(StatusCode = 200)
     * @IsGranted(ProposalVoter::EDIT, subject="proposal",  message="not allowed")
     *
     * @ApiDoc\Areas({"proposals", "default"})
     * @OA\Post (
     *     summary="Passe la proposition dans l'état 'lue'.",
     *     description="Passe la proposition dans l'état 'lue'. Via OAuth2, cet appel nécessite le scope 'proposal:write'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant la proposition",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/Proposal")
     *     )
     * )
     * @OA\Parameter(
     *     name="code",
     *     in="path",
     *     description="code de la proposition",
     *     @OA\Schema(type="string")
     * )
     *
     * @param Proposal $proposal
     * @param ProposalService $proposalService
     * @return Proposal
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function viewed(Proposal $proposal, ProposalService $proposalService): Proposal
    {
        $proposalService->viewed($proposal);
        return $proposal;
    }

    /**
     * @Rest\Post("/api/proposals/{code}/active")
     * @Rest\View(StatusCode = 200)
     * @Rest\Post("/funnel/api/proposals/{code}/active", name="activeThroughFunnel")
     * @IsGranted(ProposalVoter::EDIT, subject="proposal",  message="not allowed")
     *
     * @ApiDoc\Areas({"proposals", "default"})
     * @OA\Post (
     *     summary="Passe la proposition dans l'état 'active'.",
     *     description="Passe la proposition dans l'état 'active'. Via OAuth2, cet appel nécessite le scope 'proposal:write'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant la proposition",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/Proposal")
     *     )
     * )
     * @OA\Parameter(
     *     name="code",
     *     in="path",
     *     description="code de la proposition",
     *     @OA\Schema(type="string")
     * )
     *
     * @param Proposal $proposal
     * @param ProposalService $proposalService
     * @return Proposal
     * @throws Throwable
     * @throws Throwable
     */
    public function active(Proposal $proposal, ProposalService $proposalService): Proposal
    {
        $proposalService->active($proposal);
        return $proposal;
    }

    /**
     * @Rest\Post("/api/proposals/{code}/refused")
     * @Rest\View(StatusCode = 200)
     * @Rest\Post("/funnel/api/proposals/{code}/refused", name="refusedThroughFunnel")
     * @IsGranted(ProposalVoter::EDIT, subject="proposal",  message="not allowed")
     *
     * @ApiDoc\Areas({"proposals", "default"})
     * @OA\Post (
     *     summary="Passe la proposition dans l'état 'refused'.",
     *     description="Passe la proposition dans l'état 'refused'. Via OAuth2, cet appel nécessite le scope 'proposal:write'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant la proposition",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/Proposal")
     *     )
     * )
     * @OA\Parameter(
     *     name="code",
     *     in="path",
     *     description="code de la proposition",
     *     @OA\Schema(type="string")
     * )
     *
     * @param Proposal $proposal
     * @param ProposalService $proposalService
     * @return Proposal
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function refused(Proposal $proposal, ProposalService $proposalService): Proposal
    {
        $proposalService->refused($proposal);
        return $proposal;
    }

    /**
     * @Rest\Post("/api/proposals/{code}/draft")
     * @Rest\View(StatusCode = 200)
     * @Rest\Post("/funnel/api/proposals/{code}/draft", name="draftThroughFunnel")
     * @IsGranted(ProposalVoter::EDIT, subject="proposal",  message="not allowed")
     *
     * @ApiDoc\Areas({"proposals", "default"})
     * @OA\Post (
     *     summary="Passe la proposition dans l'état 'draft'.",
     *     description="Passe la proposition dans l'état 'draft'. Via OAuth2, cet appel nécessite le scope 'proposal:write'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant la proposition",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/Proposal")
     *     )
     * )
     * @OA\Parameter(
     *     name="code",
     *     in="path",
     *     description="code de la proposition",
     *     @OA\Schema(type="string")
     * )
     *
     * @param Proposal $proposal
     * @param ProposalService $proposalService
     * @return Proposal
     * @throws ORMException
     * @throws Throwable
     * @throws OptimisticLockException
     */
    public function draft(Proposal $proposal, ProposalService $proposalService): Proposal
    {
        $proposalService->draft($proposal);
        return $proposal;
    }

    /**
     * @Rest\Get("/app/proposals/kanban/columnConfigs")
     * @Security("is_granted('ROLE_USER')", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @param ProposalRepository $proposalRepository
     * @return array
     */
    public function listColumnConfigs(ProposalRepository $proposalRepository): array
    {
        return $proposalRepository->listColumnConfigs();
    }

    //----------------
    // METHODES PRIVES
    //----------------
    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateCreateOrUpdateBody(array $body): ConstraintViolationListInterface
    {
        $phoneNumberAsserts = new Assert\Regex(['pattern' => Tools::MOBILEPHONE_PATTERN]);
        $phoneFixedAsserts = new Assert\Regex(['pattern' => '/^(?:(?:\+|00)33|0)[1-9]\d{8}$/']);
        $forAttendee = $this->isForAnAttendee($body);
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'allowExtraFields' => true,
            'fields' => [
                'firstName' => $forAttendee ? new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 255])]) : new Assert\IsNull(),
                'lastName' => $forAttendee ? new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 255])]) : new Assert\IsNull(),
                'email' => $forAttendee ? new Assert\Required([new Assert\NotBlank(), new Assert\Email(), new Assert\Length(['max' => 255])]) : new Assert\IsNull(),
                'phoneNumber' => $forAttendee ? new Assert\Optional([
                    new Assert\Type('string'),
                    new Assert\Length(['min' => 7, 'max' => 17]),
                    new Assert\AtLeastOneOf([$phoneNumberAsserts, $phoneFixedAsserts])]) :
                    new Assert\IsNull(),
                'limitUsage' => $forAttendee ? new Assert\Required([new Assert\Type('integer'), new Assert\EqualTo(1)]) : new Assert\Optional([new Assert\Type('integer')]),
                'state' => new Assert\Optional([new Assert\Choice([ProposalStates::DRAFT()->getValue(), ProposalStates::ACTIVE()->getValue(), ProposalStates::REFUSED()->getValue(), ProposalStates::TEMPLATE()->getValue()]), new Assert\Length(['max' => 255])]),
                // Code must not be composed solely of numbers otherwise we cannot differentiate the id from the code in show / update routes
                'code' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['min' => 5, 'max' => 15]), new Assert\Regex(['pattern' => '/^(?!^\d+$).*$/'])]),
                'notes' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 5000])]),
                'description' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 5000])]),
                'color' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 255])]),

                'amount' => !empty($body['discountType']) && $body['discountType'] == ProposalDiscountTypes::NONE()->getValue() ? new Assert\IsNull() : new Assert\Required([new Assert\NotBlank(), new Assert\Type('numeric')]),
                'discountType' => new Assert\Required([new Assert\Choice(array_values(ProposalDiscountTypes::toArray()))]),

                'sessionStartDate' => !empty($body['sessionEndDate']) ? new Assert\Required([new Assert\Type("datetime")]) : new Assert\Optional([new Assert\Type("datetime")]),
                'sessionEndDate' => new Assert\Optional([new Assert\Type("datetime")]),
                'expire' => new Assert\Optional([new Assert\Type("datetime")]),

                'sales' => new Assert\Optional(),
                'trainingActions' => new Assert\Optional(),
                'indicativeDuration' => new Assert\Optional([new Assert\Type('integer')]),

                'autoValidate' => new Assert\Optional([new Assert\Type('bool')]),
                'codeRequired' => new Assert\Optional([new Assert\Type('bool')]),
                'tags' => new Assert\Optional(new Assert\Type('array')),
                'customColorScheme' => new Assert\Optional([new Assert\Regex(['pattern' => '/^#[0-9A-Fa-f]*$/']), new Assert\Length(7)]),
                'metadata' => new Assert\Optional(new Assert\Type('array'))
            ]
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateProposalTemplateUpdateBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'fields' => [
                'metadata' => new Assert\Optional(new Assert\Type('array'))
            ]
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param $body
     * @return bool
     */
    private function isForAnAttendee($body): bool
    {
        return !empty($body['firstName']) || !empty($body['lastName']) || !empty($body['email']) || !empty($body['phoneNumber']);
    }

    /**
     * @param Organism $organism
     * @param array $parameters
     * @return array
     * @throws WedofSubscriptionException
     */
    private function validateListParameters(Organism $organism, array $parameters): array
    {
        if (!$organism->getSubscription()->isAllowProposals()) {
            throw new WedofSubscriptionException("Erreur, votre abonnement ne permet pas l'accès aux propositions.");
        }

        if (isset($parameters['state'])) {
            $parameters['state'] = explode(",", $parameters['state']);
            foreach ($parameters['state'] as $state) {
                if (!in_array($state, ProposalStates::valuesStates())) {
                    throw new WedofBadRequestHttpException("Erreur sur les valeurs renvoyées 'state', elles doivent être : " . join(",", ProposalStates::valuesStates()) . ".");
                }
            }
        }

        if (isset($parameters['metadata'])) {
            $parameters['metadata'] = explode(":", $parameters['metadata'], 2); //key=value if value contains : it shoulds work too but not if it is part of the key...
            if (sizeof($parameters['metadata']) > 2) {
                throw new WedofBadRequestHttpException("Erreur sur le format 'metadata', il doit être le suivant : cle:valeur");
            }
        }
        return $parameters;
    }

}
