<?php

namespace App\Controller\api;

use App\Entity\User;
use App\Event\SignDocumentEvents;
use App\Service\SignDocumentService;
use FOS\RestBundle\Controller\Annotations as Rest;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\File\File;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;

class SignDocumentController extends AbstractWedofController
{
    /**
     * @Rest\Get("/api/signDocument/{documentId}")
     * @Rest\View(StatusCode = 200)
     * @IsGranted("ROLE_USER", message="not allowed")
     * @param string $documentId
     * @param EventDispatcherInterface $dispatcher
     * @return Response
     */
    public function show(string $documentId, EventDispatcherInterface $dispatcher): Response
    {
        /* @var $user User */
        $user = $this->getUser();
        $response = new Response();

        $event = new SignDocumentEvents($documentId, $user, $user->getMainOrganism(), []);
        $dispatcher->dispatch($event, SignDocumentEvents::SHOW_DOCUMENT);
        if ($event->getDocument() instanceof File) {
            return $this->file($event->getDocument(), 'document.pdf', ResponseHeaderBag::DISPOSITION_INLINE);
        } else {
            $response->setStatusCode(404);
        }
        return $response;
    }

    /**
     * @Rest\Post("/api/signDocument/{documentId}/preview")
     * @Rest\View(StatusCode = 200)
     * @IsGranted("ROLE_USER", message="not allowed")
     * @param string $documentId
     * @param EventDispatcherInterface $dispatcher
     * @return Response
     */
    public function preview(string $documentId, EventDispatcherInterface $dispatcher): Response
    {
        /* @var $user User */
        $user = $this->getUser();
        $body = $this->getData();
        $response = new Response();

        $event = new SignDocumentEvents($documentId, $user, $user->getMainOrganism(), $body);
        $dispatcher->dispatch($event, SignDocumentEvents::PREVIEW_DOCUMENT);
        if ($event->getDocument() instanceof File) {
            return $this->file($event->getDocument(), 'document.pdf', ResponseHeaderBag::DISPOSITION_INLINE);
        } else {
            $response->setStatusCode(404);
        }
        return $response;
    }

    /**
     * @Rest\Post("/api/signDocument/{documentId}/generateCode")
     * @Rest\View(StatusCode = 200)
     * @IsGranted("ROLE_USER", message="not allowed")
     * @param string $documentId
     * @param SignDocumentService $signDocumentService
     * @param EventDispatcherInterface $dispatcher
     * @return Response|null
     * @throws TransportExceptionInterface
     */
    public function generateCode(string $documentId, SignDocumentService $signDocumentService, EventDispatcherInterface $dispatcher): ?Response
    {
        /* @var $user User */
        $user = $this->getUser();
        $body = $this->getData();
        $response = new Response();

        $event = new SignDocumentEvents($documentId, $user, $user->getMainOrganism(), $body);
        $dispatcher->dispatch($event, SignDocumentEvents::BEFORE_GENERATE_CODE);
        if ($event->isAllowedGenerateCode()) {
            $response->setContent($signDocumentService->generateCode($documentId, $event->getDocumentName(), $user));
        } else {
            $response->setStatusCode(404);
        }
        return $response;
    }

    /**
     * @Rest\Post("/api/signDocument/{documentId}/sign")
     * @Rest\View(StatusCode = 200)
     * @IsGranted("ROLE_USER", message="not allowed")
     * @param string $documentId
     * @param SignDocumentService $signDocumentService
     * @param EventDispatcherInterface $dispatcher
     * @return Response|null
     */
    public function sign(string $documentId, SignDocumentService $signDocumentService, EventDispatcherInterface $dispatcher): ?Response
    {
        /* @var $user User */
        $user = $this->getUser();
        $body = $this->getData();
        $response = new Response();

        $event = new SignDocumentEvents($documentId, $user, $user->getMainOrganism(), []);
        $dispatcher->dispatch($event, SignDocumentEvents::BEFORE_VALIDATE_CODE);
        if ($event->isAllowedValidateCode() && isset($body['code'])) {
            if ($signDocumentService->validateCode($documentId, $body['code'])) {
                $event = new SignDocumentEvents($documentId, $user, $user->getMainOrganism(), $body);
                $dispatcher->dispatch($event, SignDocumentEvents::SIGN_DOCUMENT);
                if ($event->getDocument() instanceof File) {
                    return $response->setContent(true);
                } else {
                    $response->setStatusCode(400);
                }
            }
        } else {
            $response->setStatusCode(404);
        }
        return $response;
    }
}