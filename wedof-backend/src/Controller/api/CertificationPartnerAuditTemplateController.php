<?php
// src/Controller/api/CertificationPartnerAuditTemplateController.php
namespace App\Controller\api;

use App\Entity\CertificationPartnerAuditTemplate;
use App\Entity\User;
use App\Exception\WedofAccessDeniedHttpException;
use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofConnectionException;
use App\Exception\WedofSubscriptionException;
use App\Library\utils\Tools;
use App\Service\CertificationPartnerAuditService;
use App\Service\CertificationPartnerAuditTemplateService;
use App\Service\CertificationService;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use ErrorException;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcherInterface;
use FOS\RestBundle\View\View;
use Knp\Component\Pager\PaginatorInterface;
use Nelmio\ApiDocBundle\Annotation as ApiDoc;
use OpenApi\Annotations as OA;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validation;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Throwable;

/**
 * Class CertificationPartnerAuditTemplateController
 * @package App\Controller\api
 *
 * @OA\Tag(name="CertificationPartnerAuditTemplate")
 * @ApiDoc\Security(name="accessCode")
 */
class CertificationPartnerAuditTemplateController extends AbstractWedofController
{
    //-------------------
    // METHODES PUBLIQUES
    //-------------------
    /**
     * @Rest\Get("/api/certificationPartnerAuditTemplates/{id}", requirements={"id" = "\d+"})
     * @IsGranted("ROLE_USER", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"certificationPartnerAuditTemplate", "default"})
     * @OA\Get (
     *     summary="Récupération d'un modèle d'audit.",
     *     description="Récupération d'un modèle d'audit par son id."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations du modèle d'audit",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/CertificationPartnerAuditTemplate")
     *     )
     * )
     * @OA\Parameter(
     *     name="id",
     *     in="path",
     *     description="id du modèle d'audit",
     *     @OA\Schema(type="string")
     * )
     * @param CertificationPartnerAuditTemplate $certificationPartnerAuditTemplate
     * @return CertificationPartnerAuditTemplate
     * @throws WedofSubscriptionException
     */
    public function show(CertificationPartnerAuditTemplate $certificationPartnerAuditTemplate): CertificationPartnerAuditTemplate
    {
        /* @var $user User */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();
        $subscription = $organism->getSubscription();
        $certification = $certificationPartnerAuditTemplate->getCertification();
        if (!$certification->isCertifier($organism)) {
            throw new WedofAccessDeniedHttpException("Erreur, vous n'avez pas les droits pour consulter ce modèle d'audit");
        }
        if (!$subscription || !$subscription->isAllowCertifierPlus()) {
            throw new WedofSubscriptionException("Erreur, cette fonctionnalité est limitée aux abonnements Certificateur");
        }
        if (!$certification->isAllowAudits()) {
            throw new WedofSubscriptionException("Erreur, votre abonnement ne permet pas l'accès aux audits.");
        }
        return $certificationPartnerAuditTemplate;
    }

    /**
     * @Rest\Get("/api/certificationPartnerAuditTemplates")
     * @IsGranted("ROLE_USER", message="not allowed")
     * @Rest\QueryParam(name="certifInfo", requirements=@Assert\Type("string"), nullable=true)
     * @Rest\QueryParam(name="limit", requirements="\d+", default="100")
     * @Rest\QueryParam(name="page", requirements="\d+", default="1")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"certificationPartnerAuditTemplate", "default"})
     * @OA\Get (
     *     summary="Liste les modèles d'audit selon le critère certifInfo.",
     *     description="Récupère les modèles d'audit en fonction du certifInfo."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un tableau des modèles d'audit au format JSON",
     *     @OA\JsonContent(
     *          type="array",
     *          @OA\Items(ref="#/components/schemas/CertificationPartnerAuditTemplate")
     *     )
     * )
     *
     * @param CertificationPartnerAuditTemplateService $certificationPartnerAuditTemplateService
     * @param ParamFetcherInterface $paramFetcher
     * @param CertificationService $certificationService
     * @param PaginatorInterface $paginator
     * @return Response
     * @throws WedofSubscriptionException
     */
    public function list(CertificationPartnerAuditTemplateService $certificationPartnerAuditTemplateService, ParamFetcherInterface $paramFetcher, CertificationService $certificationService, PaginatorInterface $paginator): Response
    {
        $parameters = $paramFetcher->all(true);

        /* @var $user User */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();
        $subscription = $organism->getSubscription();
        $certification = null;

        if (!empty($parameters['certifInfo'])) {
            $certification = $certificationService->getByCertifInfo($parameters['certifInfo']);
            if (!$certification) {
                throw new WedofBadRequestHttpException("Erreur, la certification n'a pas été trouvée");
            }
            if (!$certification->isCertifier($organism)) {
                throw new WedofAccessDeniedHttpException("Erreur, vous n'avez pas les droits pour lister les modèles d'audit sur la certification avec le certifInfo " . $parameters['certifInfo']);
            }
        }

        if (!$subscription || !$subscription->isAllowCertifierPlus()) {
            throw new WedofSubscriptionException("Erreur, cette fonctionnalité est limitée aux abonnements Certificateur");
        }

        if ($certification && !$certification->isAllowAudits()) {
            throw new WedofSubscriptionException("Erreur, votre abonnement ne permet pas l'accès aux audits.");
        }

        $data = $paginator->paginate($certificationPartnerAuditTemplateService->listReturnQueryBuilder($organism, $certification), intval($parameters['page']), intval($parameters['limit']));
        $view = $this->view($data->getItems(), 200);
        $view->setHeader("x-total-count", $data->getTotalItemCount());
        $view->setHeader("x-current-page", $data->getCurrentPageNumber());
        $view->setHeader("x-item-per-page", $data->getItemNumberPerPage());
        return $this->handleView($view);
    }

    /**
     * @Rest\Post ("/api/certificationPartnerAuditTemplates")
     * @IsGranted("ROLE_USER", message="not allowed")
     * @Rest\View(StatusCode = 201)
     * @param CertificationPartnerAuditTemplateService $certificationPartnerAuditTemplateService
     * @param CertificationService $certificationService
     * @return CertificationPartnerAuditTemplate|View
     * @throws Throwable
     */
    public function create(CertificationPartnerAuditTemplateService $certificationPartnerAuditTemplateService, CertificationService $certificationService)
    {
        /* @var $user User */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();
        $subscription = $organism->getSubscription();
        $body = $this->getData();
        $violations = $this->validateCreateBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }
        $certification = $certificationService->getByCertifInfo($body['certifInfo']);
        if (!$certification) {
            throw new WedofBadRequestHttpException("Erreur, la certification n'a pas été trouvée");
        }
        if (!$certification->isCertifier($organism)) {
            throw new WedofAccessDeniedHttpException("Erreur, vous n'avez pas les droits pour ajouter un modèle d'audit sur la certification avec le certifInfo " . $body['certifInfo']);
        }
        if (!$subscription || !$subscription->isAllowCertifierPlus()) {
            throw new WedofSubscriptionException("Erreur, cette fonctionnalité est limitée aux abonnements Certificateur");
        }
        if (!$certification->isAllowAudits()) {
            throw new WedofSubscriptionException("Erreur, votre abonnement ne permet pas la création d'audits.");
        }
        return $certificationPartnerAuditTemplateService->create($body, $certification, $organism);
    }

    /**
     * @Rest\Put("/api/certificationPartnerAuditTemplates/{id}")
     * @IsGranted("ROLE_USER", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @param CertificationPartnerAuditTemplate $certificationPartnerAuditTemplate
     * @param CertificationPartnerAuditTemplateService $certificationPartnerAuditTemplateService
     * @param Request $request
     * @return CertificationPartnerAuditTemplate|View
     * @throws Throwable
     */
    public function update(CertificationPartnerAuditTemplate $certificationPartnerAuditTemplate, CertificationPartnerAuditTemplateService $certificationPartnerAuditTemplateService, Request $request)
    {
        $body = json_decode($request->getContent(), true);

        /* @var $user User */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();
        $subscription = $organism->getSubscription();
        $certification = $certificationPartnerAuditTemplate->getCertification();
        if (!$certification->isCertifier($organism)) {
            throw new WedofAccessDeniedHttpException("Erreur, vous n'avez pas les droits pour modifier ce modèle d'audit");
        }
        if (!$subscription || !$subscription->isAllowCertifierPlus()) {
            throw new WedofSubscriptionException("Erreur, cette fonctionnalité est limitée aux abonnements Certificateur");
        }
        if (!$certification->isAllowAudits()) {
            throw new WedofSubscriptionException("Erreur, votre abonnement ne permet pas la modification d'un audit.");
        }
        $violations = $this->validateUpdateBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }

        if (isset($body['criterias'])) {
            $duplicatedCodes = Tools::findDuplicatesOnKeyInArray($body['criterias'], 'code');
            if (!empty($duplicatedCodes)) {
                throw new WedofBadRequestHttpException('Erreur, l\'identifiant du critère est déjà utilisé, spécifiez un identifiant différent de ' . implode(', ', $duplicatedCodes));
            }
        }

        return $certificationPartnerAuditTemplateService->update($certificationPartnerAuditTemplate, $body);
    }

    /**
     * @Rest\Delete("/api/certificationPartnerAuditTemplates/{id}")
     * @Rest\View(StatusCode = 204)
     * @IsGranted("ROLE_USER", message="not allowed")
     *
     * @ApiDoc\Areas({"certificationPartnerAuditTemplate", "default"})
     * @OA\Delete (
     *     summary="Supprimer un modèle d'audit",
     *     description="Supprimer un modèle d'audit via son id."
     * )
     * @OA\Response(
     *     response=204,
     *     description="Aucun contenu retourné."
     * )
     * @OA\Parameter(
     *     name="id",
     *     in="path",
     *     description="id du modèle d'audit",
     *     @OA\Schema(type="string")
     * )
     *
     * @param CertificationPartnerAuditTemplate $certificationPartnerAuditTemplate
     * @param CertificationPartnerAuditTemplateService $certificationPartnerAuditTemplateService
     * @param CertificationPartnerAuditService $certificationPartnerAuditService
     * @return void
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     * @throws WedofSubscriptionException
     */
    public function delete(CertificationPartnerAuditTemplate $certificationPartnerAuditTemplate, CertificationPartnerAuditTemplateService $certificationPartnerAuditTemplateService, CertificationPartnerAuditService $certificationPartnerAuditService)
    {
        /* @var $user User */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();
        $certification = $certificationPartnerAuditTemplate->getCertification();
        if (!$certification->isCertifier($organism)) {
            throw new WedofAccessDeniedHttpException("Erreur, vous n'avez pas les droits pour supprimer ce modèle d'audit");
        }
        if (!$certification->isAllowAudits()) {
            throw new WedofSubscriptionException("Erreur, votre abonnement ne permet pas de supprimer un audit.");
        }
        $auditCount = $certificationPartnerAuditService->countByAuditTemplate($certificationPartnerAuditTemplate);
        if ($auditCount > 0) {
            throw new WedofBadRequestHttpException("Erreur, vous ne pouvez pas supprimer le modèle d'audit car des audits ont déjà été réalisés.");
        }
        $certificationPartnerAuditTemplateService->delete($certificationPartnerAuditTemplate);
    }

    /**
     * @Rest\Get("/api/certificationPartnerAuditTemplates/criterias/{certifInfo}")
     * @Rest\View(StatusCode = 200)
     * @IsGranted("ROLE_USER", message="not allowed")
     * @param CertificationPartnerAuditTemplateService $certificationPartnerAuditTemplateService
     * @param CertificationService $certificationService
     * @param string $certifInfo
     * @return array
     */
    public function listCriterias(CertificationPartnerAuditTemplateService $certificationPartnerAuditTemplateService, CertificationService $certificationService, string $certifInfo): array
    {
        /** @var User $user */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();
        $certification = $certificationService->getByCertifInfo($certifInfo);
        if (!$certification || !$certification->getCertifiers()->contains($organism)) {
            throw new WedofBadRequestHttpException("Erreur, la certification n'a pas été trouvée ou vous n'avez pas les droits pour accèder à la certification");
        }
        return $certificationPartnerAuditTemplateService->listCriterias($certification);
    }

    /**
     * @Rest\Post("/api/certificationPartnerAuditTemplates/duplicateTemplate/{id}")
     * @IsGranted("ROLE_USER", message="not allowed")
     * @Rest\View(StatusCode = 201)
     *
     * @ApiDoc\Areas({"certificationPartnerAuditTemplate", "default"})
     * @OA\Post (
     *     summary="Dupliquer un modèle d'audit",
     *     description="Permet de dupliquer un modèle d'audit."
     * )
     * @OA\Response(
     *     response=201,
     *     description="Un json contenant les informations du modèle d'audit dupliqué",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/CertificationPartnerAuditTemplate")
     *     )
     * )
     * @OA\Parameter(
     *     name="id",
     *     in="path",
     *     description="id du modèle d'audit",
     *     @OA\Schema(type="string")
     * )
     *
     * @param CertificationPartnerAuditTemplate $certificationPartnerAuditTemplate
     * @param CertificationService $certificationService
     * @param CertificationPartnerAuditTemplateService $certificationPartnerAuditTemplateService
     * @param Request $request
     * @return array|View
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     * @throws WedofSubscriptionException
     */
    public function duplicate(CertificationPartnerAuditTemplate $certificationPartnerAuditTemplate, CertificationService $certificationService, CertificationPartnerAuditTemplateService $certificationPartnerAuditTemplateService, Request $request)
    {
        $body = json_decode($request->getContent(), true);
        /* @var $user User */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();
        $subscription = $organism->getSubscription();
        $certification = $certificationPartnerAuditTemplate->getCertification();
        if (!$certification->isCertifier($organism)) {
            throw new WedofAccessDeniedHttpException("Erreur, vous n'avez pas les droits pour dupliquer un modèle d'audit");
        }
        if (!$subscription || !$subscription->isAllowCertifierPlus()) {
            throw new WedofSubscriptionException("Erreur, cette fonctionnalité est limitée aux abonnements Certificateur");
        }
        if (!$certification->isAllowAudits()) {
            throw new WedofSubscriptionException("Erreur, votre abonnement ne permet pas de dupliquer un audit.");
        }
        $violations = $this->validateDuplicateBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }
        $targetCertification = $certificationService->getByCertifInfo($body['certifInfo']);
        if (!$targetCertification || !$targetCertification->isCertifier($organism)) {
            throw new WedofAccessDeniedHttpException("Erreur, vous n'avez pas les droits pour accèder à cette certification");
        }
        if (!$targetCertification->isAllowAudits()) {
            throw new WedofSubscriptionException("Erreur, votre abonnement ne permet pas de dupliquer un modèle d'audit sur cette certification");
        }
        if (!$targetCertification->getEnabled()) {
            throw new WedofBadRequestHttpException("Erreur, la certification est inactive");
        }
        $body = [
            'name' => $certificationPartnerAuditTemplate->getName(),
            'allowVisibilityPartner' => $certificationPartnerAuditTemplate->isAllowVisibilityPartner(),
            'criterias' => $certificationPartnerAuditTemplate->getCriterias()
        ];

        return $certificationPartnerAuditTemplateService->create($body, $targetCertification, $organism, $certificationPartnerAuditTemplate->getAuditTemplateFileType()['googleId']);
    }

    //----------------
    // METHODES PRIVES
    //----------------

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateCreateBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'allowExtraFields' => false,
            'fields' => [
                'certifInfo' => new Assert\Required([new Assert\NotBlank(), new Assert\Type('string'), new Assert\NotNull(), new Assert\Length(['max' => 255])]),
                'name' => new Assert\Required([new Assert\Type('string'), new Assert\Length(['max' => 255])]),
                'allowVisibilityPartner' => new Assert\Required([new Assert\Type('bool')]),
                'googleId' => new Assert\Optional([new Assert\Type('string')]),
                'criterias' => new Assert\Optional() // TODO(audit) type ?
            ]
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateUpdateBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'allowExtraFields' => true,
            'fields' => [
                'name' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 255])]),
                'allowVisibilityPartner' => new Assert\Optional([new Assert\Type('bool')]),
                'criterias' => new Assert\Optional() // TODO(audit) type ?
            ]
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateDuplicateBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'fields' => [
                'certifInfo' => new Assert\Required([new Assert\Type('string')])
            ]
        ]);
        return $validator->validate($body, $constraints);
    }
}
