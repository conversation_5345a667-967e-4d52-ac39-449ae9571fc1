<?php

namespace App\Controller;

use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class LoginController
{
    /**
     * @Route("/login", name="login", methods={"GET", "POST"})
     * @param Request $request
     * @return RedirectResponse
     */
    public function login(Request $request): RedirectResponse
    {
        $redirect_url = $request->getSession()->get('_security.main.target_path');
        return new RedirectResponse($_ENV["PATH_BASE_FRONTEND"] . "connexion?redirectURL=" . urlencode($redirect_url));
    }
}
