<?php

namespace App\Controller;

use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Service\ShortenedUrlService;

class ShortenerController extends AbstractFOSRestController
{
    /**
     * @Rest\Get("/{id}", host="s.{domain}", defaults={"domain"="%domain%"},requirements={"domain"="%domain%", "id" = "\w+"})
     * @param Request $request
     * @param ShortenedUrlService $shortenedUrlService
     * @return Response
     */
    public function show(Request $request, ShortenedUrlService $shortenedUrlService): Response
    {
        $url = $shortenedUrlService->openUrl($request->get('id'));
        if (isset($url)) {
            return $this->redirect($url);
        }
        return new Response("", 404);
    }
}
