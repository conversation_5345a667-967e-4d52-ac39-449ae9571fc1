<?php
// src/Controller/AutomatedTasksController.php
namespace App\Controller\auto;

use App\Application\Digiforma\DigiformaWedofApplication;
use App\Application\Digiforma\Message\PullCompletionRateRegistrationFolderDigiforma;
use App\Application\MessageTemplates\Service\MessageService;
use App\Application\Onlineformapro\Message\PullCompletionRateRegistrationFoldersOnlineformapro;
use App\Application\Onlineformapro\OnlineformaproWedofApplication;
use App\Entity\CdcFile;
use App\Entity\Connection;
use App\Entity\Organism;
use App\Entity\RegistrationFolder;
use App\Entity\User;
use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofConnectionException;
use App\Library\utils\enums\ConnectionStates;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\enums\PaymentSortParams;
use App\Library\utils\enums\RegistrationFolderStates;
use App\Library\utils\enums\SubscriptionCertifierTypes;
use App\Library\utils\enums\SubscriptionTrainingTypes;
use App\Library\utils\Tools;
use App\Repository\ConnectionRepository;
use App\Service\ApplicationService;
use App\Service\CatalogService;
use App\Service\CdcFileService;
use App\Service\CertificationFolderSurveyService;
use App\Service\ConfigService;
use App\Service\ConnectionService;
use App\Service\CronTaskManagerService;
use App\Service\DataProviders\AutomatorApiService;
use App\Service\DataProviders\BaseApiService;
use App\Service\DataProviders\KairosAifApiService;
use App\Service\DataProviders\OpcoCfaDockApiService;
use App\Service\FileTypeService;
use App\Service\OrganismService;
use App\Service\RegistrationFolderService;
use App\Service\StripeService;
use App\Service\WebhookService;
use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use ErrorException;
use Exception;
use Psr\Cache\InvalidArgumentException;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use ReflectionException;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\ParamConverter;
use Stripe\Exception\ApiErrorException;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Cache\Adapter\FilesystemAdapter;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Stopwatch\Stopwatch;
use Symfony\Contracts\Cache\ItemInterface;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Throwable;
use Twig\Error\LoaderError;
use Twig\Error\SyntaxError;

class CronController extends AbstractController
{
    const MIN_TOKEN_VALIDITY_TIME_AHEAD = "- 120 seconds";

    private static int $DEFAULT_BATCH_SIZE = 2;

    private static array $BATCH_SIZE = [
        'evaluations' => 1,
        'payments' => 1,
        'catalog' => 2,
        'connection' => 1,
        //states
        'notProcessed' => 10,
        'accepted' => 10,
        'all' => 2,
        'allOpened' => 1,
        'allClosed' => 1,
        'allBilling' => 2,
        'billed' => 1,
        'allOpenedPartners' => 3,
        'allClosedPartners' => 3
    ];

    // In milliseconds
    private static int $DEFAULT_DELAY_TO_ADD = 1000;

    // In milliseconds
    private static array $DELAY_TO_ADD = [
        'evaluations' => 3000,
        'payments' => 2000,
        'catalog' => 3000,
        'connection' => 3000,
        //states,
        'notProcessed' => 1000,
        'accepted' => 1000,
        'all' => 4000,
        'allOpened' => 3000,
        'allClosed' => 3000,
        'allBilling' => 4000,
        'billed' => 2700,
        'allOpenedPartners' => 5000,
        'allClosedPartners' => 5000,
        'workingContracts' => 5000
    ];


    /**
     * @Route("/cron/registrationFolders/{userType}/{dataProvider}/{state}/{limit}/{order}/{skip}/{siret}", name="registrationFolders", requirements={
     *     "state": "all|notProcessed|validated|waitingAcceptation|accepted|inTraining|terminated|serviceDoneDeclared|serviceDoneValidated|canceled|refused|allOpened|allBilling|billed|allClosed",
     *     "limit": "\d+",
     *     "order": "ASC|DESC",
     *     "dataProvider": "cpf|kairosAif"
     * })
     * @param OrganismService $organismService
     * @param CronTaskManagerService $cronTaskManagerService
     * @param string $userType
     * @param string $state
     * @param int $limit
     * @param string|null $siret
     * @param string $order
     * @param int $skip
     * @param string $dataProvider
     * @return Response
     */
    public function registrationFolders(OrganismService $organismService, CronTaskManagerService $cronTaskManagerService, string $userType = 'customer', string $state = 'all', int $limit = 20, string $siret = null, string $order = 'DESC', int $skip = 0, string $dataProvider = 'cpf'): Response
    {
        ini_set('max_execution_time', 300);
        $stopwatch = new Stopwatch(); //watch time to process
        $stopwatch->start('registrationFolders');
        $_dataProvider = $dataProvider ? DataProviders::from($dataProvider) : DataProviders::CPF();
        switch ($userType) {
            case 'partner':
                $subscriptionTrainingTypes = [SubscriptionTrainingTypes::NONE()];
                $subscriptionCertifierTypes = [SubscriptionCertifierTypes::PARTNER()];
                $configKey = $state . 'Partners';
                break;
            case 'free':
                $subscriptionTrainingTypes = [SubscriptionTrainingTypes::FREE()];
                $subscriptionCertifierTypes = SubscriptionCertifierTypes::valuesTypes();
                $configKey = $state;
                break;
            default:
            case 'customer':
                $subscriptionTrainingTypes = array_filter(SubscriptionTrainingTypes::valuesTypes(), static function ($trainingType) {
                    return !$trainingType->equals(SubscriptionTrainingTypes::NONE()) && !$trainingType->equals(SubscriptionTrainingTypes::FREE());
                });
                $subscriptionCertifierTypes = SubscriptionCertifierTypes::valuesTypes();
                $configKey = $state;
                break;
        }
        $organisms = $siret ? [$organismService->getBySiret($siret)] : $organismService->listWithOwnedBy([
            'subscriptionTrainingTypes' => $subscriptionTrainingTypes,
            'subscriptionCertifierTypes' => $subscriptionCertifierTypes,
            'dataProvider' => [
                "isInitialized" => true,
                "type" => $dataProvider,
                "state" => ConnectionStates::ACTIVE(),
            ]
        ]);
        $delay = 0;
        $i = 0;
        $batch_size = self::$BATCH_SIZE[$configKey] ?? self::$DEFAULT_BATCH_SIZE;
        $delay_to_add = self::$DELAY_TO_ADD[$configKey] ?? self::$DEFAULT_DELAY_TO_ADD;
        /** @var Organism $organism */
        foreach ($organisms as $organism) {
            //We don't want to refresh the token with these calls
            if ($organism->getConnectionForDataProvider($_dataProvider)->getRefreshAt() > new DateTime(self::MIN_TOKEN_VALIDITY_TIME_AHEAD)) {
                $cronTaskManagerService->dispatchSynchronizeRegistrationFolders($organism, $_dataProvider, RegistrationFolderStates::from($state), $limit, $skip, $delay, $order);
                $i += 1;
                if ($i % $batch_size === 0) {
                    $delay += $delay_to_add; // Add $DELAY_TO_ADD in milliseconds every $BATCH_SIZE messages (1 message / OF);
                }
            }
        }
        return new Response($stopwatch->stop('registrationFolders'));
    }

    /**
     * optional : ?certifications=RS6551,RS1234
     * @Route("/cron/catalogs/{userType}/{siret}/{dataProvider}", name="catalogs", requirements={"dataProvider": "cpf|kairosAif"})
     * @param OrganismService $organismService
     * @param CronTaskManagerService $cronTaskManagerService
     * @param Request $request
     * @param string $userType
     * @param string|null $siret
     * @param string $dataProvider
     * @return Response
     */
    public function catalogs(OrganismService $organismService, CronTaskManagerService $cronTaskManagerService, Request $request, string $userType = 'customer', string $siret = null, string $dataProvider = 'cpf'): Response
    {
        switch ($userType) {
            case 'partner':
                $subscriptionTrainingTypes = [SubscriptionTrainingTypes::NONE()];
                $subscriptionCertifierTypes = [SubscriptionCertifierTypes::PARTNER()];
                break;
            case 'free':
                $subscriptionTrainingTypes = [SubscriptionTrainingTypes::FREE()];
                $subscriptionCertifierTypes = SubscriptionCertifierTypes::valuesTypes();
                break;
            default:
            case 'customer':
                $subscriptionTrainingTypes = array_filter(SubscriptionTrainingTypes::valuesTypes(), static function ($trainingType) {
                    return !$trainingType->equals(SubscriptionTrainingTypes::NONE()) && !$trainingType->equals(SubscriptionTrainingTypes::FREE());
                });
                $subscriptionCertifierTypes = SubscriptionCertifierTypes::valuesTypes();
                break;
        }
        $_dataProvider = $dataProvider ? DataProviders::from($dataProvider) : DataProviders::CPF();
        $certificationsCodes = $request->query->get('certifications') ? explode(',', str_replace(' ', '', $request->query->get('certifications'))) : [];
        $organisms = $siret ? [$organismService->getBySiret($siret)] : $organismService->listWithOwnedBy([
            'subscriptionTrainingTypes' => $subscriptionTrainingTypes,
            'subscriptionCertifierTypes' => $subscriptionCertifierTypes,
            'dataProvider' => [
                "isInitialized" => true,
                "type" => $dataProvider,
                "state" => ConnectionStates::ACTIVE()
            ]
        ]);
        $delay = 0;
        $i = 0;
        /** @var Organism $organism */
        foreach ($organisms as $organism) {
            $cronTaskManagerService->dispatchSynchronizeCatalogOpenData($organism, $certificationsCodes, $_dataProvider, $delay);
            $i += 1;
            if ($i % self::$BATCH_SIZE['catalog'] === 0) {
                $delay += self::$DELAY_TO_ADD['catalog']; // Add self::$DELAY_TO_ADD in milliseconds every self::$BATCH_SIZE messages (1 message / OF);
            }
        }
        return new Response();
    }

    /**
     * @Route("/cron/catalog/checkCpfUpload")
     * @param OrganismService $organismService
     * @param CatalogService $catalogService
     * @return Response
     */
    public function checkCpfCatalogUpload(OrganismService $organismService, CatalogService $catalogService): Response
    {
        $organisms = $organismService->listCpfCatalogUploadInProgress();
        $sirets = [];
        /** @var Organism $organism */
        foreach ($organisms as $organism) {
            $sirets[] = $organism->getSiret();
            $catalogService->processCpfCatalogUploadReport($organism);
        }
        return new Response(json_encode($sirets));
    }

    /**
     * @Route("/cron/organisms/{action}/{siret}/{limit}/{skip}/{dataProvider}", name="organisms", requirements={
     *     "action": "initialize|diffRegistrationFolders|synchronize|refreshRegistrationFolders|list|monitorRegistrationFolders",
     *     "dataProvider": "cpf|kairosAif"
     * })
     * @param OrganismService $organismService
     * @param CronTaskManagerService $cronTaskManagerService
     * @param WebhookService $webhookService
     * @param Request $request
     * @param int $limit
     * @param int $skip
     * @param string $action
     * @param string|null $siret
     * @param string $dataProvider
     * @return Response
     */
    public function organisms(OrganismService $organismService, CronTaskManagerService $cronTaskManagerService, WebhookService $webhookService, Request $request, int $limit = 100, int $skip = 0, string $action = 'initialize', string $siret = null, string $dataProvider = 'cpf'): Response
    {
        $_dataProvider = $dataProvider ? DataProviders::from($dataProvider) : DataProviders::CPF();
        if ($action === 'refreshRegistrationFolders') {
            $organisms = $siret ? [$organismService->getBySiret($siret)] : $organismService->listWithOwnedBy([
                'dataProvider' => [
                    "isInitialized" => true,
                    "type" => $dataProvider,
                    "state" => ConnectionStates::ACTIVE(),
                ]
            ]);
            /** @var Organism $organism */
            foreach ($organisms as $organism) {
                $cronTaskManagerService->dispatchRefreshRegistrationFolders($organism, $_dataProvider, $limit);
            }
        } else if ($action === 'diffRegistrationFolders' || $action === 'initialize') {
            $organisms = $siret ? [$organismService->getBySiret($siret)] : $organismService->listWithOwnedBy([
                'dataProvider' => [
                    "isInitialized" => $action !== 'initialize',
                    "type" => $dataProvider,
                    "state" => ConnectionStates::ACTIVE(),
                ]
            ]);
            /** @var Organism $organism */
            foreach ($organisms as $organism) {
                $cronTaskManagerService->dispatchStartBatchesRegistrationFolders($organism, $_dataProvider, $limit, $skip, $request->query->getBoolean('disableWedofEvents', true));
            }
        } else if ($action === 'list') {
            $tempFile = Tools::convertDataToCSVFile($organismService->list($limit, $skip), ['NAME', 'SIRET', 'CITY', 'PHONE', 'EMAIL', 'URL', 'INITIALIZED', 'TRAINER', 'CERTIFIER']);
            return Tools::getCsvResponse($tempFile, 'organisms');
        } else if ($action === 'monitorRegistrationFolders') {
            if ($siret) {
                $organisms[] = $organismService->getBySiret($siret);
            } else {
                $webhooks = $webhookService->listForRegistrationFolderAlert();
                $organisms = [];
                foreach ($webhooks as $webhook) {
                    $organism = $webhook->getOrganism();
                    if (!in_array($organism, $organisms)) {
                        $organisms[] = $organism;
                    }
                }
            }
            $cronTaskManagerService->dispatchMonitoringRegistrationFolders($organisms);
        } else {
            $organism = $siret ? $organismService->getBySiret($siret) : null;
            $cronTaskManagerService->dispatchSynchronizeOrganisms($organism);

        }
        return new Response();
    }

    /**
     * @Route("/cron/evaluations/{siret}/{dataProvider}", name="evaluations", requirements={"dataProvider": "cpf|kairosAif"})
     * @param OrganismService $organismService
     * @param CronTaskManagerService $cronTaskManagerService
     * @param string|null $siret
     * @param string $dataProvider
     * @return Response
     */
    public function evaluations(OrganismService $organismService, CronTaskManagerService $cronTaskManagerService, string $siret = null, string $dataProvider = 'cpf'): Response
    {
        $_dataProvider = $dataProvider ? DataProviders::from($dataProvider) : DataProviders::CPF();
        $organisms = $siret ? [$organismService->getBySiret($siret)] : $organismService->listWithOwnedBy([
            'dataProvider' => [
                "isInitialized" => true,
                "type" => $dataProvider,
                "state" => ConnectionStates::ACTIVE(),
            ]
        ]);
        $delay = 0;
        $i = 0;
        /** @var Organism $organism */
        foreach ($organisms as $organism) {
            if ($organism->getConnectionForDataProvider($_dataProvider)->getRefreshAt() > new DateTime(self::MIN_TOKEN_VALIDITY_TIME_AHEAD)) {
                $cronTaskManagerService->dispatchSynchronizeEvaluations($organism, $_dataProvider, $delay);
            }
            $i += 1;
            if ($i % self::$BATCH_SIZE['evaluations'] === 0) {
                $delay += self::$DELAY_TO_ADD['evaluations']; // Add self::$DELAY_TO_ADD in milliseconds every self::$BATCH_SIZE messages (1 message / OF);
            }
        }
        return new Response();
    }

    /**
     * @Route("/cron/workingContracts/{dataProvider}/{siret}", name="workingContracts")
     * @param OrganismService $organismService
     * @param CronTaskManagerService $cronTaskManagerService
     * @param string|null $dataProvider
     * @param string|null $siret
     * @return Response
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException|InvalidArgumentException
     */
    public function workingContracts(OrganismService $organismService, CronTaskManagerService $cronTaskManagerService, string $dataProvider = 'opcoCfaAtlas', string $siret = null): Response
    {
        if (empty($dataProvider)) {
            throw new WedofBadRequestHttpException('Erreur, fournissez un dataProvider');
        }
        $financer = DataProviders::from($dataProvider);
        if (!in_array($financer, DataProviders::getOpcoCfaDataProviders())) {
            throw new WedofBadRequestHttpException('Erreur le dataProvider ' . $dataProvider . " n'est pas un OPCO CFA");
        }
        /** @var OpcoCfaDockApiService $apiService */
        $apiService = BaseApiService::getApiServiceByDataProvider($financer);
        $apiStatus = $apiService->status();
        if ($apiStatus && $apiStatus['status'] === OpcoCfaDockApiService::STATUS_HEALTHY) {
            $subscriptionTrainingTypes = SubscriptionTrainingTypes::getPaidTrainingTypes();
            $organisms = $siret ? [$organismService->getBySiret($siret)] : $organismService->listWithOwnedBy(['subscriptionTrainingTypes' => $subscriptionTrainingTypes]);
            $delay = 0;
            /** @var Organism $organism */
            foreach ($organisms as $organism) {
                $connection = $organism->getConnectionForDataProvider($financer);
                if ($connection && $connection->getState() === ConnectionStates::ACTIVE()->getValue()) {
                    $cronTaskManagerService->dispatchSynchronizeWorkingContracts($connection, $organism, $financer, $delay);
                }
                $delay += self::$DELAY_TO_ADD['workingContracts']; // Delay between organisms for the same OPCO
            }
            return new Response();
        } else {
            return new Response('Error synchronize workingContracts - API ' . $financer->getValue() . ' is unavailable', 400);
        }
    }

    /**
     * @Route("/cron/payments/{userType}/{state}/{siret}/{sort}/{dataProvider}", name="payments", requirements={"dataProvider"="cpf|kairosAif"})
     * @param OrganismService $organismService
     * @param CronTaskManagerService $cronTaskManagerService
     * @param string $userType
     * @param string|null $state
     * @param string|null $siret
     * @param string|null $sort
     * @param string $dataProvider
     * @return Response
     */
    public function payments(OrganismService $organismService, CronTaskManagerService $cronTaskManagerService, string $userType = 'customer', string $state = null, string $siret = null, string $sort = null, string $dataProvider = 'cpf'): Response
    {
        set_time_limit(300); // ajout de suffisamment de temps pour que tous les messages soient bien envoyés
        switch ($userType) {
            case 'partner':
                $subscriptionTrainingTypes = [SubscriptionTrainingTypes::NONE()];
                $subscriptionCertifierTypes = [SubscriptionCertifierTypes::PARTNER()];
                break;
            case 'free':
                $subscriptionTrainingTypes = [SubscriptionTrainingTypes::FREE()];
                $subscriptionCertifierTypes = SubscriptionCertifierTypes::valuesTypes();
                break;
            default:
            case 'customer':
                $subscriptionTrainingTypes = array_filter(SubscriptionTrainingTypes::valuesTypes(), static function ($trainingType) {
                    return !$trainingType->equals(SubscriptionTrainingTypes::NONE()) && !$trainingType->equals(SubscriptionTrainingTypes::FREE());
                });
                $subscriptionCertifierTypes = SubscriptionCertifierTypes::valuesTypes();
                break;
        }
        $organisms = $siret ? [$organismService->getBySiret($siret)] : $organismService->listWithOwnedBy([
            'subscriptionTrainingTypes' => $subscriptionTrainingTypes,
            'subscriptionCertifierTypes' => $subscriptionCertifierTypes,
            'dataProvider' => [
                "isInitialized" => true,
                "type" => $dataProvider,
                "state" => ConnectionStates::ACTIVE()
            ]
        ]);
        $delay = 0;
        $i = 0;
        $_dataProvider = $dataProvider ? DataProviders::from($dataProvider) : DataProviders::CPF();
        $_sort = $sort ? PaymentSortParams::from($sort) : null;
        /** @var Organism $organism */
        foreach ($organisms as $organism) {
            if ($organism->getConnectionForDataProvider($_dataProvider)->getRefreshAt() > new DateTime(self::MIN_TOKEN_VALIDITY_TIME_AHEAD)) {
                $cronTaskManagerService->dispatchSynchronizePayments($organism, $_dataProvider, $state, $delay, $_sort);
                $i += 1;
                if ($i % self::$BATCH_SIZE['payments'] === 0) {
                    $delay += self::$DELAY_TO_ADD['payments']; // Add self::$DELAY_TO_ADD in milliseconds every self::$BATCH_SIZE messages (1 message / OF);
                }
            }
        }
        return new Response();
    }

    /**
     * @Route("/cron/subscriptions/{action}/{siret}", name="subscriptions", requirements={
     *     "action": "sendUsageToStripe"
     * })
     * @param StripeService $stripeService
     * @param OrganismService $organismService
     * @param string $action
     * @param string|null $siret
     * @return Response
     * @throws ApiErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function subscriptions(StripeService $stripeService, OrganismService $organismService, string $action, string $siret = null): Response
    {
        switch ($action) {
            case 'sendUsageToStripe':
                //certificationFolders
                $organisms = $siret ? new ArrayCollection([$organismService->getBySiret($siret)]) : $organismService->listBySubscriptionType(['certifierTypes' => [SubscriptionCertifierTypes::USAGE()->getValue(), SubscriptionCertifierTypes::ACCESS()->getValue()]]);
                $stripeService->sendUsageForCertifiers($organisms);
                //smsSent
                if (!$siret) {
                    $certifiers = $organismService->listBySubscriptionType(['certifierTypes' => SubscriptionCertifierTypes::getPaidCertifierTypes()])->toArray();
                    $organisms = $organismService->listBySubscriptionType(['trainingTypes' => SubscriptionTrainingTypes::getPaidTrainingTypes()])->toArray();
                    $organisms = new ArrayCollection(array_unique(array_merge($certifiers, $organisms)));
                }
                $stripeService->sendSmsUsageForOrganisms($organisms);
                break;
            default:
                throw new WedofBadRequestHttpException('Action ' . $action . ' inconnue');
        }
        return new Response();
    }

    /**
     * @Route("/cron/digiforma/pullingCompletionRateRegistrationFolders", name="digiformaPullingCompletionRateRegistrationFolders", requirements={})
     * @throws Throwable
     */
    public function pullingCompletionRateRegistrationFolders(OrganismService $organismService, ApplicationService $applicationService, RegistrationFolderService $registrationFolderService, MessageBusInterface $messageBus): Response
    {
        $organismsUsingDigiforma = $organismService->listAllUsingApp(DigiformaWedofApplication::getAppId());
        $registrationFolders = [];
        foreach ($organismsUsingDigiforma as $organism) {
            if ($applicationService->isApplicationAllowed(DigiformaWedofApplication::getAppId(), $organism->getSubscription())) {
                $registrationFolderQB = $registrationFolderService->listReturnQueryBuilder($organism, [
                    'state' => [RegistrationFolderStates::IN_TRAINING()],
                    'organismType' => 'self',
                    'type' => ['all'],
                    'billingState' => ['all'],
                    'controlState' => ['all'],
                    'certificationFolderState' => ['all'],
                    'filterOnStateDate' => 'createdOn',
                    'sort' => 'id',
                    'order' => 'ASC',
                    'limit' => null,
                    'offset' => null,
                    'query' => 'digiforma'
                ]);
                $registrationFolders = array_merge($registrationFolders, $registrationFolderQB->getQuery()->getResult());
            }
        }
        foreach ($registrationFolders as $registrationFolder) {
            $messageBus->dispatch(new PullCompletionRateRegistrationFolderDigiforma($registrationFolder->getExternalId()));
        }
        return new Response();
    }

    /**
     * @Route("/cron/onlineformapro/pullingCompletionRateRegistrationFolders", name="onlineformaproPullingCompletionRateRegistrationFolders", requirements={})
     * @throws Throwable
     */
    public function pullingCompletionRateRegistrationFoldersOnlineformapro(OrganismService $organismService, ApplicationService $applicationService, MessageBusInterface $messageBus): Response
    {
        $organismsUsingOnlineformapro = $organismService->listAllUsingApp(OnlineformaproWedofApplication::getAppId());
        foreach ($organismsUsingOnlineformapro as $organism) {
            if ($applicationService->isApplicationAllowed(OnlineformaproWedofApplication::getAppId(), $organism->getSubscription())) {
                $messageBus->dispatch(new PullCompletionRateRegistrationFoldersOnlineformapro($organism->getSiret()));
            }
        }
        return new Response();
    }

    /**
     * @Route("/cron/task/{taskName}/{option}/{dataProvider}", name="task", requirements={
     *     "taskName": "refreshFranceCompetences|refreshCertifications|refreshCertifiers|refreshPartners|refreshCertificationPartners|refreshMinSessionsDates|refreshConnections|pullingNewProcessedReceiptFiles|cdcExportNewCertificationFolders",
     *     "dataProvider": "franceCompetences"
     * })
     * @throws Throwable
     */
    public function task(string $taskName, Request $request, CronTaskManagerService $cronTaskManagerService, OrganismService $organismService, ConnectionService $connectionService, ConfigService $configService, CdcFileService $cdcFileService, string $option = '', string $dataProvider = null): Response
    {
        $response = [];
        switch ($taskName) {
            case 'refreshFranceCompetences':
                ini_set('max_execution_time', -1); // May take long cause NOT ASYNC
                ini_set('memory_limit', '4096M');
                $cronTaskManagerService->dispatchSynchronizeFranceCompetencesFiles($option ?: null);
                break;
            case 'refreshCertifications': // AND PARTNERS (FC => Wedof)
                $cronTaskManagerService->dispatchSynchronizeCertificationsAndPartners($option, $dataProvider ? DataProviders::from($dataProvider) : DataProviders::FRANCE_COMPETENCES());
                break;
            case 'refreshCertifiers':
                $cronTaskManagerService->dispatchSynchronizeCertifiers($option, $dataProvider ? DataProviders::from($dataProvider) : DataProviders::FRANCE_COMPETENCES());
                break;
            case 'refreshPartners':
                // Apparently not scheduled anymore, messages are dispatched through refreshCertifications
                $cronTaskManagerService->dispatchSynchronizePartners($option);
                break;
            case 'refreshCertificationPartners': // Bidirectionnal : FC <=> Wedof
                $cronTaskManagerService->dispatchSynchronizeCertificationPartners($option, $dataProvider ? DataProviders::from($dataProvider) : DataProviders::FRANCE_COMPETENCES());
                break;
            case 'cdcExportNewCertificationFolders':
                $delay = CdcFileService::DELAY;
                $organisms = $option ? [$organismService->getBySiret($option)] : $organismService->listBySubscriptionAllowCertifierPlusAndCdcCertifierConnection();
                /** @var Organism $organism */
                foreach ($organisms as $organism) {
                    $cdcFileService->dispatchGenerateCdcXML($organism->getOwnedBy(), true, null, $delay);
                    $delay += CdcFileService::DELAY;
                    $response[] = [
                        'organism' => [
                            "id" => $organism->getId(),
                            "siret" => $organism->getSiret(),
                            "name" => $organism->getName()
                        ]
                    ];
                }
                break;
            case 'pullingNewProcessedReceiptFiles':
                $cdcFiles = $cdcFileService->listAllCdcFilesByWedofWithoutProcessedReceiptFiles();
                $cdcFilesByOrganisms = [];
                /** @var CdcFile $cdcFile */
                foreach ($cdcFiles as $cdcFile) {
                    if (!isset($organisms[$cdcFile->getOrganism()->getSiret()])) {
                        $cdcFilesByOrganisms[$cdcFile->getOrganism()->getSiret()] = [
                            "files" => [],
                            "fileNames" => [],
                            "organism" => $cdcFile->getOrganism()
                        ];
                    }
                    $cdcFilesByOrganisms[$cdcFile->getOrganism()->getSiret()]['files'][] = $cdcFile;
                    $cdcFilesByOrganisms[$cdcFile->getOrganism()->getSiret()]['fileNames'][] = $cdcFile->getName();
                }
                foreach ($cdcFilesByOrganisms as $cdcFilesByOrganism) {
                    $cdcFileService->findAndProcessNewProcessedReceiptFiles($cdcFilesByOrganism['organism'], $cdcFilesByOrganism['files']);
                    /** @var Organism $organism */
                    $organism = $cdcFilesByOrganism['organism'];
                    $response[] = [
                        'organism' => [
                            "id" => $organism->getId(),
                            "siret" => $organism->getSiret(),
                            "name" => $organism->getName()
                        ],
                        "files" => $cdcFilesByOrganism['fileNames']
                    ];
                }
                break;
            case 'refreshMinSessionsDates':
                $configService->getConfig(true);
                break;
            case 'refreshConnections':
                $force = $request->query->getBoolean('force');
                $async = $request->query->getBoolean('async', true);
                $dataProviders = $request->query->get('dataProviders');
                $connections = $connectionService->listWithParams([
                    'states' => $force ? ConnectionStates::valuesTypes() : [ConnectionStates::ACTIVE(), ConnectionStates::REFRESHING()],
                    'refreshAt' => ($force ? null : new DateTime()),
                    'organism' => ($option ? $organismService->getBySiret($option) : null),
                    'dataProviders' => $dataProviders ? explode(',', $dataProviders) : null
                ]);
                $delay = 0;
                $i = 0;
                /** @var Connection $connection */
                foreach ($connections as $connection) {
                    $result = $cronTaskManagerService->dispatchRefreshConnections($connection, $async, $delay);
                    if (!$async) {
                        $response[$connection->getDataProvider()] = $result ? 'success' : ($connection->getState() !== ConnectionStates::FAILED()->getValue() ? $connection->getMessage() : 'failed');
                    }
                    $i += 1;
                    if ($i % self::$BATCH_SIZE['connection'] === 0) {
                        $delay += self::$DELAY_TO_ADD['connection']; // Add self::$DELAY_TO_ADD in milliseconds every self::$BATCH_SIZE messages (1 message / OF);
                    }
                }
                break;
            default:
                break;
        }
        $response = sizeof($response) > 0 ? json_encode($response) : '';
        return new Response($response, 200, ['content-type' => $response ? 'application/json' : 'text/plain']);
    }


    /**
     * @Route("/slack/registrationFolders/{externalId}/validate/{userId}")
     * @ParamConverter ("user", options={"mapping": {"userId": "id"}})
     *
     * @param RegistrationFolder $registrationFolder
     * @param User $user
     * @param RegistrationFolderService $registrationFolderService
     * @return Response
     */
    public function validateFromSlack(RegistrationFolder $registrationFolder, User $user, RegistrationFolderService $registrationFolderService): Response
    {
        if ($user === $registrationFolder->getOrganism()->getOwnedBy()) {
            try {
                $registrationFolderService->validate($registrationFolder);
            } catch (Throwable $e) {
                return new Response($e->getMessage());
            }
            return new Response("Dossier " . $registrationFolder->getExternalId() . " validé.", 200);
        }
        return new Response("", 403);
    }

    /**
     * @Route("/cron/messages/send", name="sendMessages")
     *
     * @param MessageService $messageService
     * @return Response
     * @throws ContainerExceptionInterface
     * @throws LoaderError
     * @throws NonUniqueResultException
     * @throws NotFoundExceptionInterface
     * @throws ORMException
     * @throws ReflectionException
     * @throws SyntaxError
     * @throws Throwable
     * @throws \Doctrine\ORM\ORMException
     */
    public function sendMessages(MessageService $messageService): Response
    {
        $messageService->sendMessages();
        return new Response();
    }

    /**
     * @Route("/cron/surveys/events", name="sendEvents")
     *
     * @param CertificationFolderSurveyService $certificationFolderSurveyService
     * @return Response
     */
    public function sendEvents(CertificationFolderSurveyService $certificationFolderSurveyService): Response
    {
        $certificationFolderSurveyService->sendEventSurveyAvailable();
        return new Response();
    }

    /**
     * @Route("/cron/getWorkers", name="getWorkers", requirements={})
     * @throws Throwable
     */
    public function getWorkers(): Response
    {
        $workers = explode(',', Tools::getEnvValue('WORKERS'));
        return new Response(json_encode($workers), 200);
    }

    /**
     * @Route("/cron/dataprovider/checkExist", name="checkIfExistAtProvider")
     * @param AutomatorApiService $automatorApiService
     * @param ConnectionService $connectionService
     * @param ConnectionRepository $connectionRepository
     * @param Request $request
     * @return Response
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    public function checkIfExistAtDataProviderForConnection(AutomatorApiService $automatorApiService, ConnectionService $connectionService, ConnectionRepository $connectionRepository, Request $request): Response
    {
        ini_set('memory_limit', '4096M');
        ini_set('max_execution_time', -1);
        $parameters = [
            'existAtDataProvider' => false,
            'dataProviders' => $request->query->get('dataProviders', DataProviders::CPF()->getValue())
        ];
        $allConnections = $connectionRepository->findAllWithParams($parameters);
        /** @var Connection $connection */
        foreach ($allConnections as $connection) {
            try {
                $dataProvider = DataProviders::from($connection->getDataProvider());
                $connectionExist = $automatorApiService->checkExistAtDataProvider($dataProvider, $connection->getOrganism()->getSiret());
                if ($connectionExist && $connectionExist['exist']) {
                    $connection->setExistAtDataProvider(true);
                    $connectionService->save($connection);
                    if ($connection->getCredentials()) {
                        $connectionService->refreshConnection($connection);
                    }
                }
            } catch (Exception $e) {
                $connection->setExistAtDataProvider(false);
            }
        }
        return new Response();
    }

    /**
     * @Route("/cron/countKairosAifRegistrationFoldersBySiret/{siret}", name="countKairosAifRegistrationFoldersBySiret", requirements={
     * })
     * @param OrganismService $organismService
     * @param KairosAifApiService $kairosAifApiService
     * @param string|null $siret
     * @return Response
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function countKairosAifBySiret(OrganismService $organismService, KairosAifApiService $kairosAifApiService, string $siret = null): Response
    {
        $result = [];
        $kagilum = $organismService->getBySiret("53222292400039");
        $organisms = $siret ? [$organismService->getBySiret($siret)] : $organismService->list(null, null);
        foreach ($organisms as $organism) {
            $_siret = $organism->getSiret();
            $result[$_siret] = [];
            foreach (explode(',', "0,1,2,3,4,5,6,7,8,9") as $state) {
                $result[$_siret][$state] = $kairosAifApiService->getRegistrationFoldersCountUsingKagilum($kagilum, $_siret, $state);
            }
        }
        return new Response(json_encode($result));
    }

    /**
     * @Route("/cron/addApeToOrganism/{siret}", name="addApeToOrganism", requirements={})
     * @param OrganismService $organismService
     * @param string|null $siret
     * @return Response
     */
    public function addApeToOrganism(OrganismService $organismService, string $siret = null): Response
    {
        $organisms = $siret ? [$organismService->getBySiret($siret)] : $organismService->list(null, null);
        foreach ($organisms as $organism) {
            $organismService->addApeToOrganism($organism);
        }
        return new Response();
    }

    /**
     * @Route("/cron/cache/{cacheKey}", name="regenerateCache")
     *
     * @param AutomatorApiService $automatorApiService
     * @param string $cacheKey
     * @return void
     * @throws InvalidArgumentException
     */
    public function regenerateCache(AutomatorApiService $automatorApiService, string $cacheKey): Response
    {
        $response = null;
        $cache = new FilesystemAdapter();
        if (in_array($cacheKey, [FileTypeService::CERTIFICATION,
            FileTypeService::CERTIFICATION_FOLDER,
            FileTypeService::CERTIFICATION_PARTNER,
            FileTypeService::CERTIFICATION_PARTNER_AUDIT,
            FileTypeService::REGISTRATION_FOLDER])
        ) {
            $cache->delete($cacheKey);
            $googleDriveFolderId = Tools::getEnvValue(strtoupper($cacheKey) . '_GOOGLEDRIVEFOLDER');
            $response = $cache->get($cacheKey, function (ItemInterface $item) use ($cacheKey, $googleDriveFolderId, $automatorApiService) {
                $item->expiresAfter(15 * 86400); // expire tous les 15 jours
                return $automatorApiService->listFileTypesWedof($cacheKey, $googleDriveFolderId);
            });
        }
        return new Response(json_encode($response));
    }
}
