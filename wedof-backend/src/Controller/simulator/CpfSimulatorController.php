<?php

namespace App\Controller\simulator;

use App\Library\utils\enums\DataProviders;
use App\Library\utils\enums\RegistrationFolderStates;
use App\Library\utils\Tools;
use App\Service\ConnectionService;
use App\Service\CronTaskManagerService;
use App\Service\OrganismService;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\ConnectionException;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use ErrorException;
use Exception;
use FOS\RestBundle\Controller\Annotations as Rest;
use Psr\Log\LoggerInterface;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpClient\HttpClient;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Throwable;

class CpfSimulatorController extends AbstractController
{
    private Connection $connection;
    private LoggerInterface $logger;

    public function __construct(Connection $connection, LoggerInterface $logger)
    {
        $this->connection = $connection;
        $this->logger = $logger;
    }

    /**
     * @Rest\Post("/app/cpfSimulator/eraseDatabase")
     * @Security("is_granted('ROLE_ADMIN')", message="not allowed")
     * @Rest\View(StatusCode = 200)
     * @throws ConnectionException|\Doctrine\DBAL\Driver\Exception
     */
    public function eraseDatabase(): JsonResponse
    {
        if (($_ENV['APP_ENV'] === 'dev' || $_ENV['APP_ENV'] === 'test') && $this->isGranted('ROLE_ADMIN') && ($this->countAllOrganisms() == 1)) {
            try {
                $this->connection->beginTransaction();
                $this->connection->executeQuery('DELETE FROM activity');
                $this->connection->executeQuery('SET FOREIGN_KEY_CHECKS = 0');
                $this->connection->executeQuery('DELETE FROM registration_folder');
                $this->connection->executeQuery('SET FOREIGN_KEY_CHECKS = 1');
                $this->connection->executeQuery('DELETE FROM registration_folder ');
                $this->connection->executeQuery('DELETE FROM registration_folder_history');
                $this->connection->executeQuery('DELETE FROM certification_folder');
                $this->connection->executeQuery('DELETE FROM session');
                $this->connection->executeQuery('DELETE FROM training_action');
                $this->connection->executeQuery('DELETE FROM training');
                $this->connection->executeQuery('DELETE FROM attendee');
                $this->connection->executeQuery('DELETE FROM delivery');
                $this->connection->executeQuery('DELETE FROM webhook');
                $this->connection->executeQuery('DELETE FROM end_point_status');
                $this->connection->executeQuery('DELETE FROM proposal');
                $this->connection->executeQuery('DELETE FROM proposal_tag');
                $this->connection->executeQuery('DELETE FROM proposal_training_action');
                $this->connection->executeQuery('UPDATE connection SET is_initialized = 0 WHERE data_provider = "cpf"');

                //TODO continue
                $this->connection->commit();
                return new JsonResponse(['message' => 'Database erased successfully']);
            } catch (Exception $e) {
                $this->connection->rollBack();
                $this->logger->error($e->getMessage());
                return new JsonResponse(['error' => 'Error deleting rows: ' . $e->getMessage()], 500);
            }
        } else {
            throw new AccessDeniedHttpException('You do not have permission to access this function.');
        }
    }


    /**
     * @Rest\Post("/app/cpfSimulator/resetAutomatorData")
     * @Security("is_granted('ROLE_ADMIN')", message="not allowed")
     * @Rest\View(StatusCode = 200)
     * @return JsonResponse
     * @throws TransportExceptionInterface
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     */
    public function resetAutomatorData(): JsonResponse
    {
        $CPF_Request_URL = Tools::getEnvValue("CPF_API_BASE_URI") . '/private/init';
        $httpClient = HttpClient::create();
        $response = $httpClient->request('POST', $CPF_Request_URL);
        $result = $response->getContent();
        return new JsonResponse($result, 200, [], true);
    }

    /**
     * @Rest\Post("/app/cpfSimulator/initializeOrganism")
     * @throws \Symfony\Component\Mailer\Exception\TransportExceptionInterface
     */
    public function initializeOrganism(CronTaskManagerService $cronTaskManagerService, ConnectionService $connectionService, OrganismService $organismService): JsonResponse
    {
        $cpfDataProvider = DataProviders::CPF();
        $sampleOrganism = $organismService->getBySiret('53222292400039');
        // The code below is copied from AutomatedTaskController /cron/organisms/initialize
        if ($connectionService->hasAccess($sampleOrganism, $cpfDataProvider)) {
            $cronTaskManagerService->dispatchStartBatchesRegistrationFolders($sampleOrganism, $cpfDataProvider, 100, 0, true);
        }
        return new JsonResponse("", 200, [], true);
    }

    /**
     * @Rest\Post("/app/cpfSimulator/synchronizeFolders")
     * @throws \Symfony\Component\Mailer\Exception\TransportExceptionInterface
     */
    public function synchronizeFolders(OrganismService $organismService, ConnectionService $connectionService, CronTaskManagerService $cronTaskManagerService): JsonResponse
    {
        $cpfDataProvider = DataProviders::CPF();
        $organism = $organismService->getBySiret('53222292400039');
        $states = RegistrationFolderStates::ALL();
        if ($connectionService->hasAccess($organism, $cpfDataProvider)) {
            $cronTaskManagerService->dispatchSynchronizeRegistrationFolders($organism, $cpfDataProvider, $states, 100, 0);
        }
        return new JsonResponse("", 200, [], true);
    }

    /**
     * @throws ConnectionException
     * @throws \Doctrine\DBAL\Driver\Exception
     */
    private function countAllOrganisms(): int
    {
        try {
            $this->connection->beginTransaction();
            $result = $this->connection->executeQuery('SELECT COUNT(*) FROM organism');
            $count = $result->fetchOne();
            $this->connection->commit();
            return $count;
        } catch (Exception $e) {
            $this->connection->rollBack();
            return -1;
        }
    }

    /**
     * @Rest\Post("/app/cpfSimulator/acceptFolderByAttendee", methods={"POST"})
     * @Rest\View(StatusCode = 200)
     * @param Request $request
     * @return array|null
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function acceptFolderByAttendee(Request $request): ?array
    {
        $parameters = json_decode($request->getContent(), true);
        $CPF_Request_URL = Tools::getEnvValue("CPF_API_BASE_URI") . '/other/mcf/' . $parameters['id'];
        return $this->cpfSimulatorService->AskNHuitNPost($CPF_Request_URL);
    }

    /**
     * @Rest\Post("/app/cpfSimulator/validateServiceDoneByCdc", methods={"POST"})
     * @Rest\View(StatusCode = 200)
     * @param Request $request
     * @return array|null
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function validateServiceDoneByCdc(Request $request): ?array
    {
        $parameters = json_decode($request->getContent(), true);
        $CPF_Request_URL = Tools::getEnvValue("CPF_API_BASE_URI") . '/other/cdc/' . $parameters['id'];
        return $this->cpfSimulatorService->AskNHuitNPost($CPF_Request_URL);
    }


}
