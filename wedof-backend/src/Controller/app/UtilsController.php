<?php

namespace App\Controller\app;

use App\Controller\api\AbstractWedofController;
use App\Controller\api\FileTypeController;
use App\Entity\Certification;
use App\Entity\Organism;
use App\Entity\User;
use App\Exception\WedofAccessDeniedHttpException;
use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofConnectionException;
use App\Exception\WedofNotFoundHttpException;
use App\Library\utils\Dictionary;
use App\Library\utils\Tools;
use App\Security\Voter\OrganismVoter;
use App\Service\CertificationPartnerAuditTemplateService;
use App\Service\CertificationService;
use App\Service\CityService;
use App\Service\DataProviders\RegistrationFolderInternalApiService;
use App\Service\OrganismService;
use chillerlan\QRCode\Output\QROutputInterface;
use chillerlan\QRCode\QRCode;
use chillerlan\QRCode\QROptions;
use DateTime;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\Exception\NotSupported;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use ErrorException;
use Exception;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcherInterface;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\ContainerInterface;
use Psr\Container\NotFoundExceptionInterface;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Throwable;

class UtilsController extends AbstractWedofController
{

    private RegistrationFolderInternalApiService $registrationFolderInternalApiService;
    private OrganismService $organismService;

    private CertificationService $certificationService;
    private FileTypeController $fileTypeController;

    public function __construct(RegistrationFolderInternalApiService $registrationFolderInternalApiService, OrganismService $organismService, CertificationService $certificationService, FileTypeController $fileTypeController)
    {
        $this->registrationFolderInternalApiService = $registrationFolderInternalApiService;
        $this->organismService = $organismService;
        $this->certificationService = $certificationService;
        $this->fileTypeController = $fileTypeController;
    }

    /**
     * @Rest\Get("/app/public/utils/cities/code/{code}")
     * @Rest\Get("/app/public/utils/cities/name/{name}")
     * @Rest\Get("/app/public/utils/cities/postalCode/{postalCode}")
     * @Rest\Get("/app/public/utils/cities/postalCodes/{postalCodes}")
     * @Rest\View(statusCode=200)
     * @param CityService $cityService
     * @param string|null $code
     * @param string|null $name
     * @param string|null $postalCode
     * @param string|null $postalCodes
     * @return array|null
     */
    public function city(CityService $cityService, string $code = null, string $name = null, string $postalCode = null, string $postalCodes = null): array
    {
        if ($code) {
            return [$cityService->getByCOG($code)];
        } else if ($name) {
            return $cityService->list($name)->toArray();
        } else if ($postalCode) {
            return [$cityService->getByPostalCode($postalCode)];
        } else {
            $postalCodes = explode(",", $postalCodes);
            $result = [];
            foreach ($postalCodes as $postalCode) {
                $result[] = $cityService->getByPostalCode($postalCode);
            }
            return $result;
        }
    }

    /**
     * @Rest\Get("/app/public/utils/countries/code/{code}")
     * @Rest\Get("/app/public/utils/countries/name/{name}")
     * @Rest\View(statusCode=200)
     * @param string|null $code
     * @param string|null $name
     * @return array|null
     */
    public function country(string $code = null, string $name = null): ?array
    {
        return Tools::findCountry($code, $name);
    }

    /**
     * @Rest\Get("/app/utils/dictionary")
     * @Security("is_granted('ROLE_USER')", message="not allowed")
     * @Rest\QueryParam(name="type", requirements=@Assert\Choice({"document", "message"}), default="document")
     * @Rest\QueryParam(name="scope", requirements=@Assert\Choice({"","registrationFolder", "certificationFolder", "certificate", "certificationPartner", "proposal", "audit", "certificationFolderSurvey"}), default="")
     * @Rest\QueryParam(name="contextId", requirements=@Assert\Type("string"), nullable=true, default=null)
     * @Rest\View(StatusCode = 200)
     *
     * @param ParamFetcherInterface $paramFetcher
     * @param CertificationPartnerAuditTemplateService $certificationPartnerAuditTemplateService
     * @param ContainerInterface $container
     * @return array
     * @throws ContainerExceptionInterface
     * @throws NonUniqueResultException
     * @throws NotFoundExceptionInterface
     * @throws NotSupported
     * @throws ORMException
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws \Doctrine\DBAL\Exception
     * @throws \Doctrine\ORM\ORMException
     */
    public function getDictionary(ParamFetcherInterface $paramFetcher, CertificationPartnerAuditTemplateService $certificationPartnerAuditTemplateService, ContainerInterface $container): array
    {
        $parameters = $paramFetcher->all(true);
        $context = [];
        /** @var User $user */
        $user = $this->getUser();
        $context['organism'] = $user ? $user->getMainOrganism() : null;

        if (!empty($parameters['contextId'])) {
            if (is_numeric($parameters['contextId']) && $parameters['scope'] === 'audit' && $context['organism']) {
                $certificationPartnerAuditTemplate = $certificationPartnerAuditTemplateService->getById($parameters['contextId']);
                if (!empty($certificationPartnerAuditTemplate) && $certificationPartnerAuditTemplate->getCertifier() === $context['organism']) {
                    $context['certificationPartnerAuditTemplate'] = $certificationPartnerAuditTemplate;
                }
            } else if ($parameters['type'] === 'document' && $context['organism']) {
                $contextIds = explode("_", $parameters['contextId']);
                if (sizeof($contextIds) == 3) {
                    $entity = null;
                    $field = ($parameters['scope'] === 'certificate' ? 'certificationFolder' : $parameters['scope']) . "FileTypes";
                    if ($contextIds[0] === Certification::CLASSNAME) {
                        /** @var Certification $entity */
                        $entity = $this->certificationService->getById($contextIds[1]);
                        $this->fileTypeController->checkAccessOnCertification($user, $contextIds[1], $field, $entity);
                    } else if ($contextIds[0] === Organism::CLASSNAME && $context['organism']->getSiret() === $contextIds[1]) {
                        /** @var Organism $entity */
                        $entity = $context['organism'];
                        $this->fileTypeController->checkAccessOnOrganism($user, $contextIds[1], $field, $entity);
                    }
                    if ($entity) {
                        $files = $entity->{"get" . ucwords($field)}();
                        $fileTypeIndex = array_search($contextIds[2], array_column($files, 'id'));
                        if (!isset($fileTypeIndex)) {
                            throw new WedofBadRequestHttpException("Erreur, le type de document d'id " . $parameters['contextId'] . " n'existe pas.");
                        }
                        $fileType = $files[$fileTypeIndex];
                        $context['fileType'] = $fileType;
                    }
                }
            }
        }

        return Dictionary::getDictionaryFor($parameters["type"], $parameters['scope'], $context, $container);
    }

    /**
     * @Rest\Get("/api/metadata/keys")
     * @Rest\QueryParam(name="query", requirements=@Assert\Type("string"), nullable=true, description="Filtre les metadatas")
     * @Rest\QueryParam(name="entity", requirements=@Assert\Type("string"), nullable=true, description="Entity concerné")
     * @IsGranted("ROLE_USER", message="not allowed")
     * @Rest\View(StatusCode = 200)
     * @param ParamFetcherInterface $paramFetcher
     * @return array
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws NotSupported
     */
    public function getMetadataKeys(ParamFetcherInterface $paramFetcher): array
    {
        $parameters = $paramFetcher->all(true);
        /** @var User $user */
        $user = $this->getUser();
        $entity = $user ? $user->getMainOrganism() : null;
        if (!empty($entity) && !(empty($entity->getSiret()))) {
            $siret = $entity->getSiret();
            if (!empty($siret)) {
                $organism = $this->organismService->getBySiret($siret);
                if ($organism) {
                    if (!$this->isGranted(OrganismVoter::VIEW, $organism)) {
                        throw new WedofAccessDeniedHttpException("Non autorisé");
                    }
                } else {
                    throw new WedofNotFoundHttpException("L'organisme associé au siret " . $siret . " n'a pas été trouvé.");
                }
            }
        }

        $container = $this->container;
        /* @var $em EntityManager */
        $em = $container->get('doctrine')->getManager();
        $repository = $em->getRepository('App\Entity\\' . $parameters['entity']);
        /** @var Organism $entity */
        return $repository->extractMetadataKeys($entity, $parameters['query']);
    }

    /**
     * @Rest\Get("/app/public/qrCode")
     * @Rest\QueryParam(name="url", description="Url à inserer dans le qrcode")
     * @Rest\QueryParam(name="size", default="200x200", description="taille du qrcode par défaut : 200x200")
     * @Rest\View(StatusCode = 200)
     */
    public function qrCode(ParamFetcherInterface $paramFetcher)
    {
        $parameters = $paramFetcher->all(true);
        $url = Tools::startsWith($parameters['url'], 'http') ? $parameters['url'] : $_ENV["WEDOF_BASE_URI"] . $parameters['url'];
        $options = new QROptions(['outputType' => QROutputInterface::GDIMAGE_PNG]);
        $qrcode = new QRCode($options);
        $QR = imagecreatefrompng($qrcode->render($url));
        header('Content-type: image/png');
        imagepng($QR);
        imagedestroy($QR);
    }

    /**
     * @Rest\Get("/app/public/version")
     * @Rest\View(StatusCode = 200)
     * @return JsonResponse
     * @throws Exception
     */
    public function version(): JsonResponse
    {
        $wedofVersion = $_ENV['VERSION'];
        $buildId = $_ENV['APP_ENV'] == 'dev' ? $_ENV['APP_ENV'] : $_ENV['BUILD_ID'];
        $releaseDate = $_ENV['VERSION_DATE'];
        $data = [
            'wedofVersion' => $wedofVersion . "-" . $buildId,
            'releaseDate' => (new DateTime($releaseDate))->format("d/m/Y"),
        ];
        return new JsonResponse($data);
    }

    /**
     * @Rest\Get("/app/public/logs")
     * @Rest\View(StatusCode = 200)
     * @return JsonResponse
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     * @throws ORMException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws OptimisticLockException
     * @throws ErrorException
     * @throws Throwable
     */
    public function logs(): Response
    {
        if (Tools::getEnvValue('APP_ENV') !== 'dev') {
            throw new WedofBadRequestHttpException("Erreur, l'environnement n'est pas en mode DEV.");

        }
        $logs = $this->registrationFolderInternalApiService->getProdLog();
        if (isset($logs['content'])) {
            throw new WedofBadRequestHttpException("Erreur, aucune données.");
        }
        $fileContent = $logs['content'];
        $response = new Response($fileContent);
        $response->headers->set('Content-Type', 'application/gzip');
        $response->headers->set('Content-Disposition', 'attachment; filename="download.gz"');
        $response->headers->set('Content-Encoding', 'gzip');
        return $response;
    }

    /**
     * @Rest\Get("/app/utils/certification/competencesTransferables")
     * @Rest\QueryParam(name="query", requirements=@Assert\Type("string"), nullable=true)
     * @Rest\View(StatusCode = 200)
     * @Security("is_granted('ROLE_USER')", message="not allowed")
     *
     * @param ParamFetcherInterface $paramFetcher
     * @return array
     */
    public function getCompetenceTransferableCode(ParamFetcherInterface $paramFetcher): array
    {
        $inputFileName = __DIR__ . '/../../../data/competences-transferables.json';
        $json = file_get_contents($inputFileName);
        $array = json_decode($json, true);
        $parameters = $paramFetcher->all(true);

        if (!$parameters['query']) {
            return $array;
        } else {
            $query = $parameters['query'];
            $filteredOptions = array_filter(array_map(function($option) use ($query) {
                $filteredValues = array_filter($option['values'], function($options) use ($query) {
                    return str_contains($options['title'], $query) || $options['code'] === $query;
                });
                $isGroupMatch = str_contains($option['category'], $query) ;
                return $isGroupMatch || count($filteredValues) > 0
                    ? array_merge($option, ['values' => count($filteredValues) > 0 ? $filteredValues : $option['values']])
                    : null;
            }, $array));
            return array_values(array_filter($filteredOptions));
        }
    }
}
