<?php
// src/Controller/app/ConnectionController.php
namespace App\Controller\app;

use App\Controller\api\AbstractWedofController;
use App\Entity\Organism;
use App\Entity\User;
use App\Exception\WedofAccessDeniedHttpException;
use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofConnectionException;
use App\Exception\WedofNotFoundHttpException;
use App\Library\utils\enums\DataProviders;
use App\Security\Voter\OrganismVoter;
use App\Service\ConnectionService;
use App\Service\DataProviders\BaseApiService;
use App\Service\DataProviders\OpcoCfaDockApiService;
use App\Service\OrganismService;
use Doctrine\Common\Collections\ArrayCollection;
use Exception;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcherInterface;
use FOS\RestBundle\View\View;
use Psr\Cache\InvalidArgumentException;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validation;

class ConnectionController extends AbstractWedofController
{
    //-------------------
    // METHODES PUBLIQUES
    //-------------------
    /**
     * @Rest\Get("/app/connections/me")
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_SALES')", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @param ConnectionService $connectionService
     * @return ArrayCollection
     */
    public function me(ConnectionService $connectionService): ArrayCollection
    {
        /** @var User $user */
        $user = $this->getUser();
        $currentOrganism = $user->getMainOrganism();
        return $currentOrganism ? $connectionService->listWithParams(['organism' => $currentOrganism]) : new ArrayCollection();
    }

    /**
     * @Rest\Get("/api/connections")
     * @Rest\QueryParam(name="siret", requirements=@Assert\Type("string"), nullable=true, description="Permet de n'obtenir que les connexion appartenant à l'organisme de siret considéré - par défaut l'organisme de l'utilisateur courant.")
     * @Security("is_granted('ROLE_USER')", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @param ConnectionService $connectionService
     * @param OrganismService $organismService
     * @param ParamFetcherInterface $paramFetcher
     * @return ArrayCollection
     */
    public function list(ConnectionService $connectionService, OrganismService $organismService, ParamFetcherInterface $paramFetcher): ArrayCollection
    {
        /** @var User $user */
        $user = $this->getUser();
        $parameters = $paramFetcher->all(true);
        $currentOrganism = $this->validateOrganismAccess($organismService, $parameters['siret'] ?? $user->getMainOrganism()->getSiret());
        return $connectionService->listWithParams(['organism' => $currentOrganism]);
    }

    /**
     * @Rest\Post("/app/connections/delegation/{dataProvider}")
     * @Rest\Post("/app/connections/auth/{dataProvider}")
     * @IsGranted("ROLE_USER", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @param string $dataProvider
     * @param ConnectionService $connectionService
     * @param Request $request
     * @return bool[]|false[]|View|null
     * @throws Exception
     * @throws TransportExceptionInterface
     */
    public function auth(string $dataProvider, ConnectionService $connectionService, Request $request)
    {
        /** @var User $user */
        $user = $this->getUser();
        if (!$user->getOwnedOrganism()) {
            throw new WedofBadRequestHttpException("Seul l'administrateur de l'organisme peut mettre à jour la connexion.");
        }

        $body = json_decode($request->getContent(), true);
        if (empty($body)) {
            throw new WedofBadRequestHttpException("Erreur, vérifiez vos données.");
        }
        $violations = $this->validateCredentialsBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }
        $access = $connectionService->authenticate($user->getOwnedOrganism(), DataProviders::from($dataProvider), $body, true, true);
        return ['hasAccess' => $access['hasAccess'], 'hasOrganismAccess' => $access['hasOrganismAccess'], 'errorCode' => $access['errorCode'] ?? ''];
    }

    /**
     * @Rest\Post("/app/connections/check/{dataProvider}")
     * @IsGranted("ROLE_USER", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @param string $dataProvider
     * @param ConnectionService $connectionService
     * @param Request $request
     * @return View
     * @throws Exception
     */
    public function check(string $dataProvider, ConnectionService $connectionService, Request $request): View
    {
        /** @var User $user */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();
        if (!$user->getOwnedOrganism()) {
            throw new WedofBadRequestHttpException("Seul l'administrateur de l'organisme peut effectuer cette action");
        }
        $body = json_decode($request->getContent(), true);
        return $this->view(['result' => $connectionService->check($organism, DataProviders::from($dataProvider), $body ?? [])], 200);
    }

    /**
     * @Rest\Post("/app/connections/habilitate/{dataProvider}")
     * @IsGranted("ROLE_USER", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @param string $dataProvider
     * @param ConnectionService $connectionService
     * @param Request $request
     * @return bool
     * @throws WedofConnectionException
     */
    public function habilitate(string $dataProvider, ConnectionService $connectionService, Request $request): bool
    {
        /** @var User $user */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();
        if (!$this->isGranted(OrganismVoter::EDIT, $organism)) {
            throw new WedofBadRequestHttpException("Droits sur l'organisme " . $organism->getSiret() . " insuffisants pour cette action.");
        }
        $body = $this->getData();
        return $connectionService->habilitate($organism, DataProviders::from($dataProvider), $body ?? []);
    }

    /**
     * @Rest\Get("/api/connections/opcoCfaOAuth/{dataProvider}")
     * @Security("is_granted('ROLE_ADMIN')", message="not allowed")
     * @Rest\View(StatusCode = 200)
     * @throws InvalidArgumentException
     */
    public function getOpcoCfaOAuth(string $dataProvider): array
    {
        if (!in_array($dataProvider, DataProviders::getOpcoCfaDataProviders())) {
            throw new WedofBadRequestHttpException('Error, data provider is not opco ' . $dataProvider);
        }
        /** @var OpcoCfaDockApiService $apiService */
        $apiService = BaseApiService::getApiServiceByDataProvider(DataProviders::from($dataProvider));
        $accessToken = $apiService->getOpcoAccessTokenFromMain();
        return ['accessToken' => $accessToken];
    }

    //----------------
    // METHODES PRIVES
    //----------------

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateCredentialsBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'username' => new Assert\Optional([new Assert\NotBlank(), new Assert\Type('string')]),
            'password' => new Assert\Required([new Assert\NotBlank(), new Assert\Type('string')])
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param OrganismService $organismService
     * @param string $siret
     * @return Organism
     */
    private function validateOrganismAccess(OrganismService $organismService, string $siret): Organism
    {
        $organism = $organismService->getBySiret($siret);
        if (!empty($organism)) {
            //handle user \ reseller / admin
            if (!$this->isGranted(OrganismVoter::VIEW, $organism)) {
                throw new WedofAccessDeniedHttpException("Vous n'êtes pas autorisé à avoir accès au contenu de l'organisme associé au siret " . $siret);
            }
        } else {
            throw new WedofNotFoundHttpException("L'organisme associé au siret " . $siret . " n'a pas été trouvé.");
        }
        return $organism;
    }
}
