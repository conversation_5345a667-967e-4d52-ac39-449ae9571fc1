<?php
// src/Controller/app/CertifierAccessController.php
namespace App\Controller\app;

use App\Entity\CertifierAccess;
use App\Entity\User;
use App\Exception\WedofAccessDeniedHttpException;
use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofNotFoundHttpException;
use App\Security\Voter\CertificationVoter;
use App\Security\Voter\CertifierAccessVoter;
use App\Security\Voter\OrganismVoter;
use App\Service\CertificationService;
use App\Service\CertifierAccessService;
use App\Service\MailerService;
use App\Service\OrganismService;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcherInterface;
use FOS\RestBundle\View\View;
use Knp\Component\Pager\PaginatorInterface;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validation;
use Throwable;

class CertifierAccessController extends AbstractFOSRestController
{
    //-------------------
    // METHODES PUBLIQUES
    //-------------------
    /**
     * @Rest\Get("/app/public/certifierAccess/{token}", requirements={"token" = ".+"})
     * @Rest\Get("/app/certifierAccess/{id}")
     * @Rest\View(StatusCode = 200)
     *
     * @param CertifierAccess $certifierAccess
     * @param Request $request
     * @return CertifierAccess
     */
    public function show(CertifierAccess $certifierAccess, Request $request): CertifierAccess
    {
        if (str_contains($request->getRequestUri(), 'public')) {
            return $certifierAccess;
        } else {
            if ($this->isGranted(CertifierAccessVoter::VIEW, $certifierAccess)) {
                return $certifierAccess;
            } else {
                throw new WedofAccessDeniedHttpException("Non autorisé");
            }
        }
    }

    /**
     * @Rest\Get("/app/certifierAccess")
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_CERTIFIERACCESS:READ')", message="not allowed")
     * @Rest\QueryParam(name="accessType", requirements=@Assert\Choice({"all", "partner", "certifier"}), default="all", description="Permet de n'obtenir que les autorisations données par l'organisme ou reçu par l'organisme ou toutes - par défaut toutes.")
     * @Rest\QueryParam(name="siret", requirements="\d{14}", nullable=true, description="Permet de n'obtenir que les certifiers access appartenant à l'organisme de siret considéré - par défaut l'organisme de l'utilisateur courant.")
     * @Rest\QueryParam(name="certifInfo", requirements=@Assert\Type("string"), nullable=true, description="Permet de n'obtenir que les certifications access liés à la certification considérée - par défaut tous les certifications acces de toutes les certifications sont retournés.")
     * @Rest\QueryParam(name="accessState", requirements=@Assert\Choice({"waiting", "accepted", "refused", "terminated", "all"}), default="all", description="Permet de n'obtenir que les autorisations données ou reçues par l'organisme selon l'état considéré. - Par défaut, 'all'.")
     *
     * @Rest\QueryParam(name="query", requirements=@Assert\Type("string"), nullable=true, description="Permet d'effectuer une recherche libre sur les champs 'siret du partenaire ou du certificateur', 'nom de l'organisme partenaire ou certificateur', 'certif Info d'une certification', 'nom d'une certification'.")
     * @Rest\QueryParam(name="sort", requirements=@Assert\Choice({"certifier", "partner", "updatedOn"}), default="updatedOn", description="Tri les résultats sur un critère. Valeurs possibles: 'updatedOn', 'certifier', 'partner' - par défaut 'updatedOn'.")
     * @Rest\QueryParam(name="order", requirements=@Assert\Choice({"asc", "desc"}), default="desc", description="Tri les résultats par ordre ascendant ou descendant - par défaut descendant.")
     * @Rest\QueryParam(name="limit", requirements="\d+", default="100", description="Nombre d'éléments retourné par requête - par défaut 100.")
     * @Rest\QueryParam(name="page", requirements="\d+", default="1", description="Numéro de page de la requête - par défaut la première.")
     * @Rest\View(StatusCode = 200)
     *
     * @param ParamFetcherInterface $paramFetcher
     * @param OrganismService $organismService
     * @param CertifierAccessService $certifierAccessService
     * @param CertificationService $certificationService
     * @param PaginatorInterface $paginator
     * @return Response
     */
    public function list(ParamFetcherInterface $paramFetcher, OrganismService $organismService, CertifierAccessService $certifierAccessService, CertificationService $certificationService, PaginatorInterface $paginator): Response
    {
        /* @var $user User */
        $user = $this->getUser();

        $organism = $user->getMainOrganism();
        $parameters = $paramFetcher->all(true);

        if (!empty($parameters['siret'])) {
            $organism = $organismService->getBySiret($parameters['siret']);
            if (!empty($organism)) {
                if (!$this->isGranted(OrganismVoter::VIEW, $organism)) {
                    throw new WedofAccessDeniedHttpException("Vous n'êtes pas autorisé à avoir accès au contenu de l'organisme associé au siret " . $parameters['siret']);
                }
            } else {
                throw new WedofNotFoundHttpException("L'organisme associé au siret " . $parameters['siret'] . " n'a pas été trouvé.");
            }
        }
        if (!empty($parameters['certifInfo'])) {
            $certification = $certificationService->getByCertifInfo($parameters['certifInfo']);
            if (!empty($certification)) {
                if (!$this->isGranted(CertificationVoter::VIEW_ADVANCED, $certification)) {
                    throw new WedofAccessDeniedHttpException("Vous n'êtes pas autorisé à avoir accès au contenu de la certification associée au certifInfo " . $parameters['certifInfo']);
                }
            } else {
                throw new WedofNotFoundHttpException("La certification associée au certifInfo " . $parameters['certifInfo'] . " n'a pas été trouvée.");
            }
        }
        $data = $paginator->paginate($certifierAccessService->listReturnQueryBuilder($organism, $parameters), intval($parameters['page']), intval($parameters['limit']));
        $view = $this->view($data->getItems(), 200);
        $view->setHeader("x-total-count", $data->getTotalItemCount());
        $view->setHeader("x-current-page", $data->getCurrentPageNumber());
        $view->setHeader("x-item-per-page", $data->getItemNumberPerPage());
        return $this->handleView($view);
    }

    /**
     * @Rest\Post ("/app/certifierAccess")
     * @Rest\View (StatusCode = 201)
     * Security is done in the method with isGranted
     * Il est possible d'ajouter au body les propriétés "recipient_email" et "recipient_name" pour personnaliser l'adresse email et le nom de personne qui recevra un courriel d'invitation
     *
     * @param Request $request
     * @param CertifierAccessService $certifierAccessService
     * @param OrganismService $organismService
     * @return CertifierAccess|View
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws TransportExceptionInterface
     * @throws Throwable
     */
    public function create(Request $request, CertifierAccessService $certifierAccessService, OrganismService $organismService)
    {
        /** @var User $user */
        $user = $this->getUser();
        $body = json_decode($request->getContent(), true);
        $body['sendInvitation'] = $body['sendInvitation'] ?? true; //give ability to generate invitation links only

        $violations = $this->validateCreateBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }

        $partner = $organismService->getBySiret($body['partner']);
        $certifier = $user->getMainOrganism();

        if (empty($partner)) {
            throw new WedofNotFoundHttpException("Le partenaire associé au siret " . $body['partner'] . " n'a pas été trouvé.");
        }

        $certifierAccess = new CertifierAccess();
        $certifierAccess->setPartner($partner);
        $certifierAccess->setCertifier($certifier);

        if ($this->isGranted(CertifierAccessVoter::CREATE, $certifierAccess)) {
            return $certifierAccessService->create($certifierAccess, $user, $body);
        } else {
            throw new WedofAccessDeniedHttpException("Non autorisé");
        }
    }

    /**
     * @Rest\Post ("/app/certifierAccess/{id}/activate")
     * @Rest\View (StatusCode = 200)
     * @IsGranted(CertifierAccessVoter::EDIT, subject="certifierAccess",  message="not allowed")
     *
     * @param CertifierAccess $certifierAccess
     * @param CertifierAccessService $certifierAccessService
     * @return CertifierAccess
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    public function activate(CertifierAccess $certifierAccess, CertifierAccessService $certifierAccessService): CertifierAccess
    {
        /** @var User $user */
        $user = $this->getUser();
        if ($user->getMainOrganism() === $certifierAccess->getPartner()) {
            if (!$certifierAccess->isActive()) {
                return $certifierAccessService->activate($certifierAccess, $user);
            } else {
                throw new WedofBadRequestHttpException("Erreur, l'accès Certifier est déjà activé.");
            }
        } else {
            throw new WedofAccessDeniedHttpException("Seul le partenaire peut activer l'accès.");
        }
    }

    /**
     * @Rest\Post ("/app/public/certifierAccess/{id}/refuse")
     * @Rest\View (StatusCode = 200)
     * pas de vérification sur le refus : l'action peut être effectuée sans nécessiter un compte.
     *
     * @param CertifierAccess $certifierAccess
     * @param CertifierAccessService $certifierAccessService
     * @return CertifierAccess
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    public function refuse(CertifierAccess $certifierAccess, CertifierAccessService $certifierAccessService): CertifierAccess
    {
        if (empty($certifierAccess->getActivatedOn())) { // si date activation => refus de la demande
            return $certifierAccessService->refuse($certifierAccess);
        } else {
            throw new WedofAccessDeniedHttpException("L'accès est déjà activé, utilisez le endpoint /terminate pour le révoquer.");
        }
    }

    /**
     * @Rest\Post ("/app/certifierAccess/{id}/terminate")
     * @IsGranted("ROLE_USER", message="not allowed")
     * @Rest\View (StatusCode = 200)
     * @IsGranted(CertifierAccessVoter::EDIT, subject="certifierAccess",  message="not allowed")
     *
     * @param CertifierAccess $certifierAccess
     * @param CertifierAccessService $certifierAccessService
     * @return CertifierAccess
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function terminate(CertifierAccess $certifierAccess, CertifierAccessService $certifierAccessService): CertifierAccess
    {
        /** @var User $user */
        $user = $this->getUser();
        $mainOrganism = $user->getMainOrganism();
        $isPartner = $mainOrganism === $certifierAccess->getPartner();
        $isCertifier = $mainOrganism === $certifierAccess->getCertifier();
        if ($isPartner || $isCertifier) {
            return $certifierAccessService->terminate($certifierAccess, $user, $isCertifier);
        } else {
            throw new WedofAccessDeniedHttpException("Erreur, vous n'avez pas les droits pour modifier le partage des données.");
        }
    }

    /**
     * @Rest\Post ("/app/certifierAccess/resend/{siret}")
     * @IsGranted("ROLE_USER", message="not allowed")
     * @Rest\View (StatusCode = 200)
     *
     * @param string $siret
     * @param CertifierAccessService $certifierAccessService
     * @param OrganismService $organismService
     * @param MailerService $mailerService
     * @return void
     * @throws TransportExceptionInterface
     */
    public function resendInvitationEmail(string $siret, CertifierAccessService $certifierAccessService, OrganismService $organismService, MailerService $mailerService): string
    {
        $organism = $organismService->getBySiret($siret);

        return $mailerService->sendInvitation($certifierAccessService->listForPartner($organism, 'inactive')->first());
    }

    //----------------
    // METHODES PRIVES
    //----------------

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateCreateBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'recipientName' => new Assert\Optional([new Assert\NotBlank(), new Assert\Type('string'), new Assert\Length(['max' => 255])]),
            'recipientEmail' => new Assert\Optional(new Assert\Email()),
            'sendInvitation' => new Assert\Optional(new Assert\Type('bool')),
            'partner' => new Assert\Required([new Assert\NotBlank(), new Assert\Length(14), new Assert\Regex(['pattern' => '/^[0-9]*$/'])])
        ]);
        return $validator->validate($body, $constraints);
    }
}
