<?php

namespace App\Controller\app;

use App\Controller\api\AbstractWedofController;
use App\Exception\WedofBadRequestHttpException;
use App\Service\AzureAiDocumentService;
use App\Service\Id360Service;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\View\View;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validation;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;

class IdentityController extends AbstractWedofController
{
    //------
    // ID360
    //------
    /**
     * @Rest\Post("/app/public/identities/id360/signInUrl/{parcours}" , name="id360_sign_in_url", requirements={"parcours": "login|passport"})
     * @Rest\View(StatusCode = 200)
     * @param Id360Service $iD360Service
     * @param Request $request
     * @param string $parcours
     * @return array|View
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    public function signInUrl(Id360Service $iD360Service, Request $request, string $parcours = "login")
    {
        $body = json_decode($request->getContent(), true);
        $violations = $this->validateBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }
        return ["url" => $iD360Service->getNewEnrollmentUrl($parcours, $body['redirectURL'] ?? null, $body['id'], $body['browserCallbackUrl'] ?? null)];
    }

    /**
     * @Rest\Post("/app/public/identities/id360/userData/{parcours}" , name="id360_user_data", requirements={"parcours": "login|passport"})
     * @Rest\View(StatusCode = 200)
     * @param Id360Service $iD360Service
     * @param Request $request
     * @param string $parcours
     * @return array|null
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    public function userData(Id360Service $iD360Service, Request $request, string $parcours = "login"): ?array
    {
        $body = json_decode($request->getContent(), true);
        return $iD360Service->getUserData($parcours, $body['token']);
    }

    /**
     * @Rest\Post("/app/identities/id360/auth", name="id360_auth")
     * @Rest\View(StatusCode = 200)
     * @param Request $request
     */
    public function auth(Request $request)
    {
        // ** if you want to *authenticate* the user, then
        // leave this method blank and create a Guard authenticator
    }

    /**
     * @Rest\Post("/app/public/identities/id360/callback", name="id360_callback")
     * @Rest\View(StatusCode = 200)
     * @param Id360Service $iD360Service
     * @param Request $request
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    public function callback(Id360Service $iD360Service, Request $request)
    {
        $body = json_decode($request->getContent(), true);
        $iD360Service->callback($body);
    }

    //-------------
    // ID DOCUMENTS
    //-------------
    /**
     * @Rest\Post("/app/public/identities/idDocument/upload", name="upload_id_document")
     * @Rest\View(StatusCode = 200)
     * @param AzureAiDocumentService $azureAiDocumentService
     * @return array|Response|null
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    public function uploadIdDocument(AzureAiDocumentService $azureAiDocumentService)
    {
        $body = $this->getData();
        if ($body['idDocument']) {

            /** @var UploadedFile $file */
            $file = $body['idDocument'];
            $dest = $file->getPath() . "/" . $file->getClientOriginalName();
            copy($file->getPathname(), $dest);

            try {
                $data = $azureAiDocumentService->extractDataDocument($dest);
            } catch (\Exception $e) {
                throw new WedofBadRequestHttpException("Erreur, le document n'a pas pu être reconnu. Veuillez réessayer avec un document de meilleure qualité ou en recadrant le document.");
            }

            return $azureAiDocumentService->formatDataAsUserData($data);
        } else {
            return new Response("Error no document", 400);
        }
    }

    /**
     * @Rest\Post("/app/public/identities/socialSecurityCard/upload", name="upload_social_security_card")
     * @Rest\View(StatusCode = 200)
     * @param AzureAiDocumentService $azureAiDocumentService
     * @return array|Response|null
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    public function uploadSocialSecurityCard(AzureAiDocumentService $azureAiDocumentService)
    {
        $body = $this->getData();
        if ($body['socialSecurityCard']) {
            /** @var UploadedFile $file */
            $file = $body['socialSecurityCard'];
            $dest = $file->getPath() . "/" . $file->getClientOriginalName();
            copy($file->getPathname(), $dest);
            try {
                $data = $azureAiDocumentService->extractSocialSecurityCard($dest);
            } catch (\Exception $e) {
                throw new WedofBadRequestHttpException("Erreur, le document n'a pas pu être reconnu. Veuillez réessayer avec un document de meilleure qualité ou en recadrant le document.");
            }
            return $azureAiDocumentService->formatSocialSecurityCardData($data);
        } else {
            return new Response("Error no document", 400);
        }
    }

    //-----------------
    // METHODES PRIVEES
    //-----------------
    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'id' => new Assert\Required([new Assert\NotBlank(), new Assert\Type('string')]),
            'redirectURL' => new Assert\Optional([new Assert\Type('string')]),
            'browserCallbackUrl' => new Assert\Optional([new Assert\Type('string')]),
        ]);
        return $validator->validate($body, $constraints);
    }
}
