<?php

namespace App\Controller\app;

use App\Entity\User;
use App\Exception\WedofAccessDeniedHttpException;
use App\Exception\WedofBadRequestHttpException;
use App\Library\utils\enums\SubscriptionCertifierTypes;
use App\Library\utils\enums\SubscriptionTrainingTypes;
use App\Service\StripeService;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcherInterface;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Stripe\Event;
use Stripe\Exception\ApiErrorException;
use Stripe\Exception\SignatureVerificationException;
use Stripe\Webhook;
use Symfony\Component\HttpFoundation\Request;
use Stripe\Subscription as StripeSubscription;
use Throwable;

class StripeController extends AbstractFOSRestController
{
    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    /**
     * @Rest\Get("/app/subscriptions/manage/{type}/{subscriptionType}", requirements={"type"="training|certifier"})
     * @Rest\QueryParam(name="options", nullable=true)
     * @IsGranted("ROLE_USER", message="not allowed")
     * @Rest\View(statusCode=200)
     * @param StripeService $stripeService
     * @param string $type
     * @param ParamFetcherInterface $paramFetcher
     * @param string|null $subscriptionType
     * @return array
     * @throws ApiErrorException
     */
    public function manage(StripeService $stripeService, string $type, ParamFetcherInterface $paramFetcher, string $subscriptionType = null): array
    {
        /** @var $user User */
        $user = $this->getUser();
        $organism = $user->getOwnedOrganism();
        $parameters = $paramFetcher->all(true);

        $options = $parameters['options'] ? json_decode($parameters['options'], true) : [];

        if ($organism) {
            if ($type === 'training') {
                return ["url" => $subscriptionType && (!$organism->getSubscription() ||
                    in_array($organism->getSubscription()->getTrainingType(), [SubscriptionTrainingTypes::NONE()->getValue(), SubscriptionTrainingTypes::FREE()->getValue(), SubscriptionTrainingTypes::TRIAL()->getValue()])) ?
                    $stripeService->createSubscriptionTrainingLink($user, SubscriptionTrainingTypes::from($subscriptionType)) :
                    $stripeService->manageSubscriptionLink($user)];
            } else {
                return ["url" => $subscriptionType && (!$organism->getSubscription() ||
                    in_array($organism->getSubscription()->getCertifierType(), [SubscriptionCertifierTypes::NONE()->getValue(), SubscriptionCertifierTypes::FREE()->getValue(), SubscriptionCertifierTypes::TRIAL()->getValue(), SubscriptionCertifierTypes::PARTNER()->getValue()])) ?
                    $stripeService->createSubscriptionCertifierLink($user, SubscriptionCertifierTypes::from($subscriptionType), $options) :
                    $stripeService->manageSubscriptionLink($user)];
            }
        } else {
            throw new WedofAccessDeniedHttpException("L'utilisateur courant n'a pas l'autorisation d'accéder aux données de l'abonnement de cet organisme.");
        }
    }

    /**
     * @Rest\Post("/app/public/subscriptions/stripeWebhook")
     * @Rest\View(statusCode=200)
     * @param Request $request
     * @param StripeService $stripeService
     * @throws Throwable
     * @throws SignatureVerificationException
     */
    public function webhook(Request $request, StripeService $stripeService)
    {
        $event = Webhook::constructEvent($request->getContent(), $request->headers->get('stripe-signature'), $_ENV['STRIPE_WEBHOOK_SECRET']);

        switch ($event->type) {
            case Event::CUSTOMER_SUBSCRIPTION_CREATED:
            case Event::CUSTOMER_SUBSCRIPTION_UPDATED:
                $stripeSubscription = StripeSubscription::constructFrom($event->data->object);
                switch ($stripeSubscription->status) {
                    case StripeSubscription::STATUS_UNPAID:
                    case StripeSubscription::STATUS_CANCELED:
                    case StripeSubscription::STATUS_INCOMPLETE:
                    case StripeSubscription::STATUS_INCOMPLETE_EXPIRED:
                        $stripeService->handleDeletedSubscription($event);
                        break;
                    case StripeSubscription::STATUS_TRIALING:
                    case StripeSubscription::STATUS_PAST_DUE:
                    case StripeSubscription::STATUS_ACTIVE:
                        $stripeService->handleNewOrUpdatedSubscription($event, $event->type == Event::CUSTOMER_SUBSCRIPTION_CREATED);
                        break;
                }
                break;
            case Event::CUSTOMER_SUBSCRIPTION_DELETED:
                $stripeService->handleDeletedSubscription($event);
                break;
            case Event::INVOICE_PAYMENT_SUCCEEDED:
                $stripeService->handleSuccessPayment($event);
                break;
            case Event::INVOICE_CREATED:
                $stripeService->handleInvoiceCreated($event);
                break;
            case Event::INVOICE_PAYMENT_FAILED:
                $stripeService->handleFailedPayment($event);
                break;
            default:
                throw new WedofBadRequestHttpException("L'évènement Stripe " . $event->type . " n'est pas géré.");
        }
    }

    //-------------------
    // METHODES PRIVEES
    //-------------------
}
