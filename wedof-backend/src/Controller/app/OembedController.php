<?php

namespace App\Controller\app;

use App\Library\utils\Tools;
use App\Repository\CertificationFolderRepository;
use App\Repository\CertificationRepository;
use App\Service\OrganismService;
use App\Service\ProposalService;
use Doctrine\ORM\NonUniqueResultException;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcherInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints as Assert;
use Throwable;

class OembedController extends AbstractFOSRestController
{
    //-------------------
    // METHODES PUBLIQUES
    //-------------------
    /**
     * @Rest\Get("/app/public/oembed")
     * @Rest\View(StatusCode = 200)
     * @Rest\QueryParam(name="url", requirements=@Assert\Type("string"))
     * @Rest\QueryParam(name="format", requirements=@Assert\Choice({"json", "xml"}), default="json")
     * @Rest\QueryParam(name="maxheight", requirements=@Assert\Type("number"), nullable=true)
     * @Rest\QueryParam(name="maxwidth", requirements=@Assert\Type("number"), nullable=true)
     * @param ProposalService $proposalService
     * @param OrganismService $organismService
     * @param CertificationRepository $certificationRepository
     * @param CertificationFolderRepository $certificationFolderRepository
     * @param ParamFetcherInterface $paramFetcher
     * @return Response
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function oembed(ProposalService               $proposalService,
                           OrganismService               $organismService,
                           CertificationRepository       $certificationRepository,
                           CertificationFolderRepository $certificationFolderRepository,
                           ParamFetcherInterface         $paramFetcher): Response
    {
        $parameters = $paramFetcher->all(true);
        $url = $parameters['url'];
        $format = $parameters['format'];

        if ($format === 'xml') {
            return $this->handleView($this->view(null, 501));
        } else {
            $oembed = [
                //"type" => "video,rich,link,photo",
                "version" => "1.0",
                "provider_name" => "Wedof",
                "provider_url" => "https://www.wedof.fr",
                "cache_age" => "3600"
            ];
            switch ($url) {
                case Tools::contains($url, '/funnel/apprenant/proposition/'):
                    preg_match('/[A-Z0-9\s]+/i', explode('/funnel/apprenant/proposition/', $url)[1], $match);
                    $code = $match[0];
                    $organismName = explode('//', explode('.' . $_ENV["DOMAIN"], $url)[0])[1];
                    $organism = $organismService->getOrganism(["subDomain" => $organismName]);
                    if ($organism) {
                        $proposal = $proposalService->getByOrganismAndCode($organism, $code);
                        if ($proposal) {
                            $oembed = FunnelController::getMetadata($proposal);
                        }
                    }
                    break;
                case Tools::contains($url, '/app/public/certificateHolder/'):
                    preg_match('/(.*)\/(.*)\/(.*)/', explode('/app/public/certificateHolder/', $url)[1], $match);
                    $certification = $certificationRepository->findOneBy(['type' => $match[1], 'code' => $match[2]]);
                    if ($certification) {
                        $certificationFolder = $certificationFolderRepository->findOneBy(['certification' => $certification, 'certificateId' => $match[3]]);
                        if ($certificationFolder) {
                            $oembed = CertificateHolderController::getMetadata($certificationFolder);
                        }
                    }
                    break;
                default:
                    break;
            }
        }
        if (isset($oembed['title'])) {
            $oembed['type'] = "rich";
            if (isset($oembed['image'])) {
                $oembed['thumbnail_url'] = $oembed['image'];
                $oembed['thumbnail_width'] = 500;
                $oembed['thumbnail_height'] = 170;
                unset($oembed['image']);
            }
            $oembed['html'] = '<blockquote><a href="' . $url . '">' . $oembed['title'] . '</a></blockquote>';
            $oembed['width'] = $parameters['maxwidth'] ?? 600;
            $oembed['height'] = $parameters['maxheight'] ?? 100;
            return $this->handleView($this->view($oembed, 200));
        } else {
            return $this->handleView($this->view(null, 404));
        }
    }



    //-------------------
    // METHODES PRIVEES
    //-------------------
}
