<?php
// src/Controller/app/ApplicationController.php

namespace App\Controller\app;

use App\Entity\Application;
use App\Entity\User;
use App\Event\Application\ApplicationEvent;
use App\Exception\WedofAccessDeniedHttpException;
use App\Exception\WedofBadRequestHttpException;
use App\Security\Voter\OrganismVoter;
use App\Service\ApplicationService;
use App\Service\OrganismService;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\OptimisticLockException;
use Exception;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;
use League\OAuth2\Client\Provider\Exception\IdentityProviderException;
use ReflectionException;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Stripe\Exception\ApiErrorException;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class ApplicationController extends AbstractFOSRestController
{
    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    /**
     * @Rest\Get("/app/applications", name="app_list")
     * @Rest\Get("/api/applications", name="app_list_api")
     * @Rest\View(statusCode=200)
     * @IsGranted("ROLE_USER", message="not allowed")
     */
    public function list(): ArrayCollection
    {
        /** @var User $user */
        $user = $this->getUser();
        if ($this->isGranted(OrganismVoter::VIEW, $user->getMainOrganism())) {
            return new ArrayCollection($user->getMainOrganism()->getApplications()->toArray());
        } else {
            throw new WedofAccessDeniedHttpException();
        }
    }

    /**
     * @Rest\Get("/app/applications/{appId}", name="app_show")
     * @Rest\Get("/api/applications/{appId}", name="app_show_api")
     * @Rest\View(statusCode=200)
     * @IsGranted("ROLE_USER", message="not allowed")
     * @param string $appId
     * @param ApplicationService $applicationService
     * @param EventDispatcherInterface $dispatcher
     * @return Application|null
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws ReflectionException
     * @throws \Doctrine\ORM\ORMException
     */
    public function show(string $appId, ApplicationService $applicationService, EventDispatcherInterface $dispatcher): ?Application
    {
        /** @var User $user */
        $user = $this->getUser();
        $application = $applicationService->getByOrganismAndAppId($user->getMainOrganism(), $appId);
        if ($this->isGranted(OrganismVoter::VIEW, $application->getOrganism())) {
            $dispatcher->dispatch(new ApplicationEvent($application), ApplicationEvent::SHOW);
        } else {
            throw new WedofAccessDeniedHttpException();
        }
        return $application;
    }

    /**
     * @Rest\Post("/app/applications/{appId}/enable", name="app_enable")
     * @Rest\Post("/api/applications/{appId}/enable", name="app_enable_api")
     * @Rest\View(statusCode=200)
     * @IsGranted("ROLE_USER", message="not allowed")
     * @param string $appId
     * @param ApplicationService $applicationService
     * @param OrganismService $organismService
     * @param Request $request
     * @return Application
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws \Doctrine\ORM\ORMException
     * @throws ReflectionException|ApiErrorException
     */
    public function enable(string $appId, ApplicationService $applicationService, OrganismService $organismService, Request $request): Application
    {
        /** @var User $user */
        $user = $this->getUser();
        $siret = $request->get('siret');
        $pendingEnableDone = $request->get('pendingEnableDone', false);
        if ($this->isGranted('ROLE_ADMIN') && $siret) {
            $organism = $organismService->getBySiret($siret);
        } else {
            $organism = $user->getMainOrganism();
        }
        if ($organism) {
            $application = $applicationService->getByOrganismAndAppId($organism, $appId);
            if ($this->isGranted(OrganismVoter::EDIT, $application->getOrganism())) {
                $applicationService->enable($application, $pendingEnableDone);
            }
            return $application;
        } else {
            throw new WedofAccessDeniedHttpException();
        }
    }

    /**
     * @Rest\Post("/app/applications/{appId}/disable", name="app_disable")
     * @Rest\Post("/api/applications/{appId}/disable", name="app_disable_api")
     * @Rest\View(statusCode=200)
     * @IsGranted("ROLE_USER", message="not allowed")
     * @param string $appId
     * @param ApplicationService $applicationService
     * @param OrganismService $organismService
     * @param Request $request
     * @return Application
     * @throws ApiErrorException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws ReflectionException
     * @throws \Doctrine\ORM\ORMException
     */
    public function disable(string $appId, ApplicationService $applicationService, OrganismService $organismService, Request $request): Application
    {
        /** @var User $user */
        $user = $this->getUser();
        $siret = $request->get('siret');
        $pendingDisableDone = $request->get('pendingEnableDone', false);
        if ($this->isGranted('ROLE_ADMIN') && $siret) {
            $organism = $organismService->getBySiret($siret);

        } else {
            $organism = $user->getMainOrganism();
        }
        if ($organism) {
            $application = $applicationService->getByOrganismAndAppId($organism, $appId);
            if ($this->isGranted(OrganismVoter::EDIT, $application->getOrganism())) {
                $applicationService->disable($application, $pendingDisableDone);
            }
            return $application;
        } else {
            throw new WedofAccessDeniedHttpException();
        }
    }

    /**
     * @Rest\Put("/app/applications/{appId}", name="app_put")
     * @Rest\Put("/api/applications/{appId}", name="app_put_api")
     * @Rest\View
     * @IsGranted("ROLE_USER", message="not allowed")
     * @param Request $request
     * @param string $appId
     * @param ApplicationService $applicationService
     * @return Application
     * @throws NonUniqueResultException
     * @throws OptimisticLockException
     * @throws ORMException|\Doctrine\ORM\ORMException|ReflectionException
     */
    public function update(Request $request, string $appId, ApplicationService $applicationService): Application
    {
        /** @var User $user */
        $user = $this->getUser();
        $application = $applicationService->getByOrganismAndAppId($user->getMainOrganism(), $appId);
        if (!$application->getEnabled()) {
            throw new WedofBadRequestHttpException();
        }
        if ($this->isGranted(OrganismVoter::EDIT, $application->getOrganism())) {
            $body = json_decode($request->getContent(), true);
            $applicationService->updateMetadata($application, $body['metadata']);
        } else {
            throw new WedofAccessDeniedHttpException();
        }
        return $application;
    }

    /**
     * @Rest\Get("/app/applications/{appId}/data/{method}")
     * @Rest\Post("/app/applications/{appId}/data/{method}")
     * @Rest\View
     * @IsGranted("ROLE_USER", message="not allowed")
     * @param string $appId
     * @param ApplicationService $applicationService
     * @param string $method
     * @param Request $request
     * @return mixed
     * @throws NonUniqueResultException
     * @throws OptimisticLockException
     * @throws ORMException|\Doctrine\ORM\ORMException|ReflectionException
     */
    public function data(string $appId, ApplicationService $applicationService, string $method, Request $request)
    {
        /** @var User $user */
        $user = $this->getUser();
        $application = $applicationService->getByOrganismAndAppId($user->getMainOrganism(), $appId, true);
        if ($application && $this->isGranted(OrganismVoter::VIEW, $application->getOrganism())) {
            try {
                $body = $request->getContent();
                return $applicationService->data($application, $method, $body);
            } catch (Exception $e) {
                throw new WedofBadRequestHttpException($e->getMessage());
            }
        } else {
            throw new WedofAccessDeniedHttpException();
        }
    }

    /**
     * @Rest\Get("/app/applications/{appId}/oauth2")
     * @Rest\View
     * @IsGranted("ROLE_USER", message="not allowed")
     * @param ApplicationService $applicationService
     * @param string $appId
     * @return array
     * @throws NonUniqueResultException
     * @throws OptimisticLockException
     * @throws ORMException|\Doctrine\ORM\ORMException|ReflectionException
     */
    public function oAuth2Authorize(ApplicationService $applicationService, string $appId): array
    {
        /** @var User $user */
        $user = $this->getUser();
        $application = $applicationService->getByOrganismAndAppId($user->getMainOrganism(), $appId);
        if ($this->isGranted(OrganismVoter::EDIT, $application->getOrganism())) {
            return ["url" => $applicationService->getOAuth2AuthorizationUrl($application)];
        } else {
            throw new WedofAccessDeniedHttpException();
        }
    }

    /**
     * @Rest\Get("/app/applications/{appId}/oauth2/callback", name="oAuth2Callback")
     * @Rest\View
     * @IsGranted("ROLE_USER", message="not allowed")
     * @param ApplicationService $applicationService
     * @param string $appId
     * @return Application
     * @throws NonUniqueResultException
     * @throws OptimisticLockException
     * @throws ORMException|\Doctrine\ORM\ORMException|IdentityProviderException|ReflectionException
     */
    public function oAuth2Callback(ApplicationService $applicationService, string $appId): Application
    {
        /** @var User $user */
        $user = $this->getUser();
        $application = $applicationService->getByOrganismAndAppId($user->getMainOrganism(), $appId);
        if ($this->isGranted(OrganismVoter::EDIT, $application->getOrganism())) {
            $applicationService->setAccessToken($application);
        } else {
            throw new WedofAccessDeniedHttpException();
        }
        return $application;
    }


    /**
     * @Route("/auth/france-connect", name="oauth2CallbackFakeFc")
     * @Route("/mes-applications/oauth/{appId}", name="oauth2CallbackFake")
     */
    public function oauth2CallbackFake()
    {
    }
}
