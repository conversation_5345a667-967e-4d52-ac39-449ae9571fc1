<?php


namespace App\Controller\app;

use App\Service\UserService;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;

class TrustPilotController extends AbstractFOSRestController
{

    /**
     * TODO make it from DB
     * @Rest\Get("/app/public/trustPilot/{user}/{action}", requirements={"user" = "\d+", "action" = "link|image"}, defaults={"action" = "link"})
     * @Rest\View(StatusCode = 200)
     * @param Request $request
     * @param UserService $userService
     * @return Response
     */
    public function show(Request $request, UserService $userService): Response
    {
        $userId = $request->get('user');
        $action = $request->get('action');
        $user = $userService->getById(intval($userId));
        if ($user) {
            switch ($action) {
                case "image":
                    $url = "https://share.trustpilot.com/images/company-rating?locale=fr-FR&businessUnitId=";
                    if ($user->getId() === 3) {
                        $response = new Response();
                        $disposition = $response->headers->makeDisposition(ResponseHeaderBag::DISPOSITION_INLINE, "");
                        $response->headers->set('Content-Disposition', $disposition);
                        $response->headers->set('Content-Type', 'image/png');
                        $response->setContent(file_get_contents($url . "603c91d6cc01ad00013550c8"));
                        return $response;
                    } else if ($user->getId() === 6) {
                        $response = new Response();
                        $disposition = $response->headers->makeDisposition(ResponseHeaderBag::DISPOSITION_INLINE, "");
                        $response->headers->set('Content-Disposition', $disposition);
                        $response->headers->set('Content-Type', 'image/png');
                        $response->setContent(file_get_contents($url . "61b77275057df728f50711b7"));
                        return $response;
                    }
                    break;
                case "link":
                    if ($user->getId() === 3) {
                        $url = "https://fr.trustpilot.com/review/";
                        return $this->redirect($url . "www.26academy.com");
                    } else if ($user->getId() === 6) {
                        $url = "https://fr.trustpilot.com/review/";
                        return $this->redirect($url . "studi.com");
                    }
                    break;
            }
        }
        return new Response('', 404);
    }
}
