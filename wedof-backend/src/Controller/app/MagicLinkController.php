<?php
// src/Controller/SecurityController.php
namespace App\Controller\app;

use App\Service\AttendeeService;
use App\Service\UserService;
use FOS\RestBundle\Controller\Annotations as Rest;
use LogicException;
use ReflectionException;
use <PERSON><PERSON>\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\Response;

class MagicLinkController extends AbstractController
{
    /**
     * @Route("/app/magic-link-auth", name="login_check") //fake route
     */
    public function check()
    {
        throw new LogicException('This code should never be reached');
        #https://symfony.com/doc/5.4/security/login_link.html
    }


    /**
     * @Rest\Get("/api/generateMagicLink/{email}/{lifetime}", name="generate_magic_link_user")
     * @IsGranted("ROLE_ADMIN", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @throws ReflectionException
     */
    public function generateMagicLink(UserService $userService, string $email, int $lifetime = 600): Response
    {
        $user = $userService->getByEmail($email);
        if ($user) {
            return new Response(json_encode(["link" => $userService->generateMagicLink($user, $lifetime)]));
        } else {
            return new Response(null, 404);
        }
    }

    /**
     * @Rest\Get("/api/generateAttendeeMagicLink/{email}/{lifetime}", name="generate_magic_link_attendee")
     * @IsGranted("ROLE_ADMIN", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @throws ReflectionException
     */
    public function generateAttendeeMagicLink(AttendeeService $attendeeService, string $email, int $lifetime = 600): Response
    {
        $attendee = $attendeeService->getByEmail($email);
        if ($attendee) {
            $link = $attendeeService->generateMagicLink($attendee, $lifetime);
            return new Response(json_encode(["link" => $link]));
        } else {
            return new Response(null, 404);
        }
    }
}