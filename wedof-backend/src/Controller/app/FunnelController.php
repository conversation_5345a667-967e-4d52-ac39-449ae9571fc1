<?php
// src/Controller/app/FunnelController.php
namespace App\Controller\app;

use App\Entity\Organism;
use App\Entity\Proposal;
use App\Entity\RegistrationFolder;
use App\Entity\User;
use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofNotFoundHttpException;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\enums\ProposalStates;
use App\Library\utils\enums\TrainingActionStates;
use App\Library\utils\Tools;
use App\Repository\TrainingActionRepository;
use App\Service\OrganismService;
use App\Service\ProposalService;
use App\Service\RegistrationFolderService;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\OptimisticLockException;
use FOS\RestBundle\Context\Context;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;
use Knp\Component\Pager\Pagination\PaginationInterface;
use Knp\Component\Pager\PaginatorInterface;
use Symfony\Component\HttpFoundation\Cookie;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Throwable;


class FunnelController extends AbstractFOSRestController
{
    /**
     * Must be generated server side (SSR) in order to add meta that can be read by robots (linkedin, facebook, X (Twitter) etc)
     * @Route("/funnel/apprenant/proposition/{code}", host="{organismName}.{domain}", defaults={"domain"="%domain%"},requirements={"domain"="%domain%"}, methods={"GET","HEAD"})
     * @param Request $request
     * @param string $organismName
     * @param OrganismService $organismService
     * @param ProposalService $proposalService
     * @return Response
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function showHtml(Request $request, string $organismName, OrganismService $organismService, ProposalService $proposalService): Response
    {
        $code = $request->get('code');
        $organism = $organismService->getOrganism(["subDomain" => $organismName]);
        if ($organism && $code) {
            $proposal = $proposalService->getActiveIndividualByOrganismAndCode($organism, $code) ?: $proposalService->getGenericByOrganismAndCode($organism, $code);
            if (!$proposal) {
                return new Response($this->generateErrorHTML($organism), 403, ["Content-Type" => "text/html"]);
            } else {
                $data = self::getMetadata($proposal);
                return $this->render('funnel/index.html.twig', [
                    'proposal' => $proposal,
                    'data' => $data,
                    'organism' => $organism,
                    '_env' => $_ENV
                ]);
            }
        }
        throw new WedofBadRequestHttpException();
    }

    /**
     * @Rest\Get("/app/public/funnel/retrieve/{code}", host="{organismName}.{domain}", defaults={"domain"="%domain%"},requirements={"domain"="%domain%"})
     * @Rest\View(StatusCode = 200)
     * @param Request $request
     * @param string $organismName
     * @param OrganismService $organismService
     * @param ProposalService $proposalService
     * @return Response
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function retrieve(Request $request, string $organismName, OrganismService $organismService, ProposalService $proposalService): Response
    {
        /** @var User $user */
        $user = $request->getUser();
        $code = $request->get('code');
        $organism = $organismService->getOrganism(["subDomain" => $organismName]);
        if ($organism && $code) {
            $proposal = $proposalService->getActiveIndividualByOrganismAndCode($organism, $code);
            if ($proposal) {
                if (!$user) { //any logged in customer must not be counted
                    $proposalService->viewed($proposal);
                }
                $cookie = new Cookie('_cart_w', base64_encode($proposal->getCode()), time() + 60 * 60 * 24, '/', null, true, false, true, Cookie::SAMESITE_NONE);
                $view = $this->view($proposal, 200);
                $view->getResponse()->headers->setCookie($cookie);
                $context = new Context();
                $context->addGroup('public');
                $view->setContext($context);
                return $this->handleView($view);
            } else {
                $genericProposal = $proposalService->getGenericByOrganismAndCode($organism, $code);
                if ($genericProposal && $genericProposal->getState() == ProposalStates::TEMPLATE()) {
                    $view = $this->view($genericProposal, 200);
                    return $this->handleView($view);
                }
            }
            throw new WedofNotFoundHttpException("Erreur, aucune proposition active n'a été trouvée pour le code " . $code);
        }
        if (!$organism) {
            throw new WedofBadRequestHttpException('Erreur, aucun organisme trouvé pour le sous domaine ' . $organismName);
        } else {
            throw new WedofBadRequestHttpException("Erreur, il faut fournir un code de proposition");
        }
    }

    /**
     * @Rest\Post("/app/public/funnel/start", host="{organismName}.{domain}", defaults={"domain"="%domain%"},requirements={"domain"="%domain%"})
     * @Rest\View(StatusCode = 200)
     * @param Request $request
     * @param string $organismName
     * @param OrganismService $organismService
     * @param ProposalService $proposalService
     * @return Response
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function start(Request $request, string $organismName, OrganismService $organismService, ProposalService $proposalService): Response
    {
        $body = json_decode($request->getContent(), true);
        $organism = $organismService->getOrganism(["subDomain" => $organismName]);
        if ($organism) {
            $code = $body['genericCode'];
            $genericProposal = $proposalService->getGenericByOrganismAndCode($organism, $code);
            if ($genericProposal) {
                $data = $body['attendee'];
                $data['state'] = ProposalStates::VIEWED();
                $proposal = $proposalService->createFromGenericProposal($genericProposal, $data, $organism);
                $cookie = new Cookie('_cart_w', base64_encode($proposal->getCode()), time() + 60 * 60 * 24, '/', null, true, false, true, Cookie::SAMESITE_NONE);
                $view = $this->view($proposal, 200);
                $view->getResponse()->headers->setCookie($cookie);

                $context = new Context();
                $context->addGroup('public');
                $view->setContext($context);

                return $this->handleView($view);
            } else {
                throw new WedofNotFoundHttpException("Erreur, aucune proposition n'a été trouvée pour le code " . $code);
            }
        } else {
            throw new WedofBadRequestHttpException('Erreur, aucun organisme trouvé pour le sous domaine ' . $organismName);
        }
    }

    /**
     * @Rest\Post("/app/public/funnel/update/{code}", host="{organismName}.{domain}", defaults={"domain"="%domain%"},requirements={"domain"="%domain%"})
     * @Rest\View(StatusCode = 200)
     * @param Request $request
     * @param $organismName
     * @param ProposalService $proposalService
     * @param OrganismService $organismService
     * @return Response
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function update(Request $request, $organismName, ProposalService $proposalService, OrganismService $organismService): Response
    {
        $code = $request->get('code');
        $body = json_decode($request->getContent(), true);
        $organism = $organismService->getOrganism(["subDomain" => $organismName]);
        if ($organism) {
            $proposal = $proposalService->getActiveIndividualByOrganismAndCode($organism, $code);
            if ($proposal) {
                $proposal = $proposalService->update($proposal, $body);
                $view = $this->view($proposal, 200);
                return $this->handleView($view);
            } else {
                throw new WedofNotFoundHttpException("Erreur, aucune proposition individuelle n'a été trouvée pour le code " . $code);
            }
        }
        throw new WedofBadRequestHttpException('Erreur, aucun organisme trouvé pour le sous domaine ' . $organismName);
    }

    /**
     * @Rest\Get("/app/public/funnel/trainingActions", host="{organismName}.{domain}", defaults={"domain"="%domain%"},requirements={"domain"="%domain%"})
     * @Rest\View(StatusCode = 200)
     * @param Request $request
     * @param string $organismName
     * @param OrganismService $organismService
     * @param TrainingActionRepository $trainingActionRepository
     * @param PaginatorInterface $paginator
     * @param ProposalService $proposalService
     * @return Response
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function trainingActions(Request $request, string $organismName, OrganismService $organismService, TrainingActionRepository $trainingActionRepository, PaginatorInterface $paginator, ProposalService $proposalService): Response
    {
        $query = $request->get('query');
        $organism = $organismService->getOrganism(["subDomain" => $organismName]);
        if ($organism) {
            /** @var Proposal $proposal */
            $proposal = $this->getProposalFromCookie($request, $organismService, $proposalService, $organism->getSiret());
            if ($proposal) {
                $data = $this->getTrainingActionsByProposalAndQuery($trainingActionRepository, $paginator, $proposal, $query);
                $view = $this->view($data->getItems(), 200);

                $context = new Context();
                $context->addGroup('public');
                $view->setContext($context);

                $view->setHeader("x-total-count", $data->getTotalItemCount());
                $view->setHeader("x-current-page", $data->getCurrentPageNumber());
                $view->setHeader("x-item-per-page", $data->getItemNumberPerPage());
                return $this->handleView($view);
            } else {
                throw new WedofNotFoundHttpException("Erreur, aucune proposition n'a été trouvée depuis le cookie");
            }
        }
        throw new WedofBadRequestHttpException('Erreur, aucun organisme trouvé pour le sous domaine ' . $organismName);
    }

    /**
     * @Rest\Put("/app/public/funnel/select", host="{organismName}.{domain}", defaults={"domain"="%domain%"},requirements={"domain"="%domain%"})
     * @Rest\View(StatusCode = 200)
     * @param Request $request
     * @param string $organismName
     * @param OrganismService $organismService
     * @param ProposalService $proposalService
     * @return Response
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function select(Request $request, string $organismName, OrganismService $organismService, ProposalService $proposalService): Response
    {
        $body = json_decode($request->getContent(), true);
        $trainingActionData = !empty($body['selectedTrainingAction']) ? $body['selectedTrainingAction'] : null;
        $organism = $organismService->getOrganism(["subDomain" => $organismName]);
        if ($organism && !empty($trainingActionData['externalId'])) {
            /** @var Proposal $proposal */
            $proposal = $this->getProposalFromCookie($request, $organismService, $proposalService, $organism->getSiret());
            if ($proposal && !$proposal->getRegistrationFolder()) {
                $proposal = $proposalService->selectTrainingAction($proposal, $trainingActionData['externalId']);
                $view = $this->view($proposal, 200);
                $context = new Context();
                $context->addGroup('public');
                $view->setContext($context);
                return $this->handleView($view);
            }
        }
        if (!$organism) {
            throw new WedofBadRequestHttpException('Erreur, aucun organisme trouvé pour le sous domaine ' . $organismName);
        } else {
            throw new WedofBadRequestHttpException("Erreur, il faut fournir un externalId d'action de formation");
        }
    }

    /**
     * @Rest\Get("/app/public/funnel/folder/{state}", host="{organismName}.{domain}", defaults={"domain"="%domain%"},requirements={"domain"="%domain%"})
     * @Rest\View(StatusCode = 200)
     * @param Request $request
     * @param string $organismName
     * @param RegistrationFolderService $registrationFolderService
     * @param OrganismService $organismService
     * @param ProposalService $proposalService
     * @param string|null $state
     * @return RegistrationFolder|null
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function folder(Request $request, string $organismName, RegistrationFolderService $registrationFolderService, OrganismService $organismService, ProposalService $proposalService, string $state = null): ?RegistrationFolder
    {
        $organism = $organismService->getOrganism(["subDomain" => $organismName]);
        if ($organism) {
            /** @var Proposal $proposal */
            $proposal = $this->getProposalFromCookie($request, $organismService, $proposalService, $organism->getSiret());
            if ($proposal) {
                $registrationFolder = $proposal->getRegistrationFolder();
                return $registrationFolder ? ($state ? $registrationFolderService->lockedCheckForState($registrationFolder, $state) : $registrationFolder) : null;
            }
        }
        throw new WedofBadRequestHttpException('Erreur, dossier de formation non trouvé pour cette proposition');
    }

    /**
     * @Rest\Get("/app/public/funnel/svg", host="{organismName}.{domain}", defaults={"domain"="%domain%"},requirements={"domain"="%domain%"})
     * @Rest\View(StatusCode = 200)
     * @param Request $request
     * @param string $organismName
     * @param OrganismService $organismService
     * @param ProposalService $proposalService
     * @return Response
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function svg(Request $request, string $organismName, OrganismService $organismService, ProposalService $proposalService): Response
    {
        $organism = $organismService->getOrganism(["subDomain" => $organismName]);
        if ($organism) {
            /** @var Proposal $proposal */
            $proposal = $this->getProposalFromCookie($request, $organismService, $proposalService, $organism->getSiret());
            if ($proposal && $proposal->isValid() && !$proposal->getRegistrationFolder() && $proposal->getSelectedTrainingAction()) {
                $svg = $proposalService->generateAlertText($proposal);
                $headers = [
                    "Cache-Control" => "no-cache, no-store, max-age=0, must-revalidate",
                    "Expires" => "Fri, 06 Sep 1985 09:00:00 GMT",  //my birthday <3
                    "Pragma" => "no-cache"
                ];
                $response = new Response($svg, 200, $headers);
                $response->headers->set('Content-Type', 'image/svg+xml');
                return $response;
            }
        }
        throw new WedofBadRequestHttpException();
    }

    public static function getMetadata(Proposal $proposal): array
    {
        $data = [];
        $data['host'] = $proposal->getOrganism()->getSubDomain() . '.' . $_ENV["DOMAIN"];
        if (!in_array($_ENV["PORT"], ["80", "443"])) {
            $data['host'] = $data['host'] . ":" . $_ENV["PORT"];
        }
        $data['url'] = 'https://' . $data['host'] . '/funnel/apprenant/proposition/' . $proposal->getCode();
        $data['author_name'] = $proposal->getOrganism()->getName();
        $data['author_email'] = $proposal->getOrganism()->getEmails() ? $proposal->getOrganism()->getEmails()[0] : $proposal->getOrganism()->getOwnedBy()->getEmail();
        $data['author_url'] = $proposal->getOrganism()->getUrls() ? $proposal->getOrganism()->getUrls()[0] : 'https://www.wedof.fr';
        $data['title'] = sizeof($proposal->getTrainingActions()) != 1 ? "Inscrivez vous à une formation de " . $data['author_name'] : $proposal->getTrainingActions()[0]->getTraining()->getTitle() . " avec " . $data['author_name'];
        $data['description'] = $proposal->getDescription() ? Tools::stripTagsContent($proposal->getDescription()) : 'Formulaire d\'inscription aux formations de ' . $data['author_name'];
        $data['image'] = 'https://' . $data['host'] . ($proposal->getLogo() ?: '/assets/organisms/' . $proposal->getOrganism()->getSiret() . '.svg');
        return $data;
    }

    //-------------------
    // METHODES PRIVEES
    //-------------------

    /**
     * @param Request $request
     * @param OrganismService $organismService
     * @param ProposalService $proposalService
     * @param string $siret
     * @return Proposal|false
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    private function getProposalFromCookie(Request $request, OrganismService $organismService, ProposalService $proposalService, string $siret)
    {
        if ($siret) {
            $organism = $organismService->getOrganism(["siret" => $siret]);
            $code = $request->cookies->get('_cart_w', false);
            $code = base64_decode($code);
            if ($organism && $code) {
                /** @var Proposal $proposal */
                $proposal = $proposalService->getActiveIndividualByOrganismAndCode($organism, $code);
                return $proposal ?? false;
            }
        }
        return false;
    }

    /**
     * @param TrainingActionRepository $trainingActionRepository
     * @param PaginatorInterface $paginator
     * @param Proposal $proposal
     * @param string|null $query
     * @return PaginationInterface
     */
    private function getTrainingActionsByProposalAndQuery(TrainingActionRepository $trainingActionRepository, PaginatorInterface $paginator, Proposal $proposal, string $query = null): PaginationInterface
    {
        $parameters = ["limit" => 50, "page" => 1];
        if ($proposal->getTrainingActions()->count() > 0) {
            $parameters['in'] = $proposal->getTrainingActions();
        }
        if ($query) {
            $parameters['query'] = $query;
        }
        $parameters['state'] = TrainingActionStates::PUBLISHED()->getValue(); //filter only active
        $parameters['eligible'] = 'true'; //filter only eligible
        $parameters['dataProvider'] = DataProviders::CPF()->getValue();
        return $paginator->paginate($trainingActionRepository->findAllReturnQueryBuilder($proposal->getOrganism(), $parameters), $parameters['page'], $parameters['limit']);
    }

    /**
     * @param Organism $organism
     * @return string
     */
    private function generateErrorHTML(Organism $organism): string
    {
        $organismEmail = count($organism->getEmails()) > 0 ? $organism->getEmails()[0] : null;
        $organismPhone = count($organism->getPhones()) > 0 ? $organism->getPhones()[0] : null;
        $organismLogo = $organism->getLogo();
        $year = date("Y");
        $textError = "<head><link href='https://fonts.googleapis.com/css?family=Roboto:300,400,400i,500,700,900&display=swap' rel='stylesheet'></head>";
        $textError = $textError . "<body><img style='width:350px; display: block; margin-left: auto; margin-right: auto; margin-top: 50px' src='$organismLogo'>";
        $textError = $textError . "<p style='text-align:center; margin-top: 25px; font-family: Roboto;'>Malheureusement, la proposition demandée n'est pas disponible.<br>Pour plus d'informations, veuillez nous contacter.<br>";
        if ($organismEmail) {
            $textError = $textError . "<a href='mailto: $organismEmail'>$organismEmail</a>";
            if ($organismPhone) {
                $textError = $textError . " / ";
            }
        }
        if ($organismPhone) {
            $textError = $textError . "<a href='tel: $organismPhone'>$organismPhone</a>";
        }
        return $textError . "<footer><div><p style='text-align:center; font-family: Roboto; font-size: 12px'>Un service proposé par © <a href='https://www.wedof.fr'>Wedof</a> $year</p></div></footer></p></body>";
    }
}
