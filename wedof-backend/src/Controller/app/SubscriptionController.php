<?php

namespace App\Controller\app;

use App\Application\WedofApplication;
use App\Entity\Subscription;
use App\Entity\User;
use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofSubscriptionException;
use App\Library\utils\enums\SubscriptionCertifierTypes;
use App\Library\utils\enums\SubscriptionTrainingTypes;
use App\Security\SecurityExtended;
use App\Security\Voter\SubscriptionVoter;
use App\Service\SubscriptionService;
use Doctrine\Common\Collections\ArrayCollection;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcherInterface;
use FOS\RestBundle\View\View;
use Knp\Component\Pager\PaginatorInterface;
use Nelmio\ApiDocBundle\Annotation as ApiDoc;
use OpenApi\Annotations as OA;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Entity;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Security\Core\Authentication\Token\SwitchUserToken;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validation;
use Throwable;

/**
 * @OA\Tag(name="Subscription")
 * @ApiDoc\Security(name="accessCode")
 */
class SubscriptionController extends AbstractFOSRestController
{
    //-------------------
    // METHODES PUBLIQUES
    //-------------------
    /**
     * @Rest\Get("/app/subscriptions/me", name="subscription_show_me")
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_SALES')", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @return Subscription
     */
    public function me(): ?Subscription
    {
        /** @var User $user */
        $user = $this->getUser();
        return $user->getMainOrganism() ? $user->getMainOrganism()->getSubscription() : null;
    }

    /**
     * @Rest\Get("/api/subscriptions/me", name="subscription_show_me_data_partner")
     * @IsGranted("ROLE_USER", message="not allowed")
     * @Rest\View(StatusCode = 200, serializerGroups={"dataPartner"})
     *
     * @ApiDoc\Areas({"subscriptions", "default"})
     * @OA\Get (
     *     summary="Abonnement de l'utilisateur authentifié.",
     *     description="Permet de récupérer les informations de l'abonnement de l'utilisateur courant."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations de l'abonnement l'utilisateur authentifié.",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/Subscription")
     *     ))
     * )
     *
     * @return Subscription
     */
    public function meForDataPartner(): ?Subscription
    {
        /** @var User $user */
        $user = $this->getUser();
        return $user->getMainOrganism() ? $user->getMainOrganism()->getSubscription() : null;
    }

    /**
     * @Rest\Get("/app/subscriptions/{id}")
     * @IsGranted(SubscriptionVoter::VIEW, subject="subscription", message="not allowed")
     * @Rest\View(statusCode=200)
     *
     * @param Subscription $subscription
     * @return Subscription
     */
    public function show(Subscription $subscription): Subscription
    {
        return $subscription;
    }

    /**
     * @Rest\Get("/app/subscriptions/siret/{siret}")
     * @Entity("subscription", expr="repository.findBySiret(siret)")
     * @IsGranted(SubscriptionVoter::VIEW, subject="subscription", message="not allowed")
     * @Rest\View(statusCode=200)
     *
     * @param Subscription $subscription
     * @return Subscription
     */
    public function showBySiret(Subscription $subscription): Subscription
    {
        return $subscription;
    }

    /**
     * @Rest\Get("/app/subscriptions")
     * @Rest\View(StatusCode = 200)
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_SUBSCRIPTION:READ')", message="not allowed")
     *
     * @Rest\QueryParam(name="trainingType", requirements=@Assert\Choice(choices=SubscriptionTrainingTypes::VALUES_TYPES_TO_STRING), default="all", description="Recherche sur le type de la souscription associée à l'utilisateur. Toutes par défaut.")
     * @Rest\QueryParam(name="certifierType", requirements=@Assert\Choice(choices=SubscriptionCertifierTypes::VALUES_TYPES_TO_STRING), default="all", description="Recherche sur le type de la souscription associée à l'utilisateur. Toutes par défaut.")
     * @Rest\QueryParam(name="trainingPendingCancellation", requirements=@Assert\Choice("all", "true", "false"), default="all", description="Recherche sur le type de la souscription associée à l'utilisateur. Toutes par défaut.")
     * @Rest\QueryParam(name="certifierPendingCancellation", requirements=@Assert\Choice("all", "true", "false"), default="all", description="Recherche sur le type de la souscription associée à l'utilisateur. Toutes par défaut.")
     * @Rest\QueryParam(name="limit", requirements="\d+", default="100", description="Nombre d'éléments retourné par requête - par défaut 100.")
     * @Rest\QueryParam(name="page", requirements="\d+", default="1", description="Numéro de page de la requête - par défaut la première.")
     *
     * @param ParamFetcherInterface $paramFetcher
     * @param PaginatorInterface $paginator
     * @param SubscriptionService $subscriptionService
     * @return ArrayCollection|Response
     */
    public function list(ParamFetcherInterface $paramFetcher, PaginatorInterface $paginator, SubscriptionService $subscriptionService)
    {
        $parameters = $paramFetcher->all(true);

        if ($this->isGranted("ROLE_ADMIN") || $this->isGranted("ROLE_ALLOWED_TO_SWITCH")) {
            $data = $paginator->paginate($subscriptionService->listReturnQueryBuilder($parameters), intval($parameters['page']), intval($parameters['limit']));
            $view = $this->view($data->getItems(), 200);
            $view->setHeader("x-total-count", $data->getTotalItemCount());
            $view->setHeader("x-current-page", $data->getCurrentPageNumber());
            $view->setHeader("x-item-per-page", $data->getItemNumberPerPage());
            return $this->handleView($view);
        } else {
            /** @var User $user */
            $user = $this->getUser();
            return new ArrayCollection([$user->getMainOrganism()->getSubscription()]);
        }
    }

    /**
     * @Rest\Post("app/subscriptions")
     * @Rest\View(statusCode=201)
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_SUBSCRIPTION:WRITE')", message="not allowed")
     *
     * @param Request $request
     * @param SubscriptionService $subscriptionService
     * @return Subscription|View
     * @throws Throwable
     */
    public function create(Request $request, SubscriptionService $subscriptionService)
    {
        /** @var User $user */
        $user = $this->getUser();
        $subscription = $user->getMainOrganism()->getSubscription();

        if (!$subscription) {
            $body = json_decode($request->getContent(), true);
            $violations = $this->validateCreateBody($body);
            if (count($violations)) {
                return $this->view($violations, Response::HTTP_BAD_REQUEST);
            }

            $subscription = $subscriptionService->create($user->getMainOrganism(), $body);
        }

        return $subscription;
    }

    /**
     * @Rest\Put("/app/subscriptions/{id}")
     * @IsGranted(SubscriptionVoter::EDIT, subject="subscription", message="not allowed")
     * @Rest\View(statusCode=200)
     *
     * @param SubscriptionService $subscriptionService
     * @param SecurityExtended $securityExtended
     * @param Request $request
     * @param Subscription $subscription
     * @return Subscription|View
     * @throws Throwable
     * @throws WedofSubscriptionException
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function update(SubscriptionService $subscriptionService, SecurityExtended $securityExtended, Request $request, Subscription $subscription)
    {
        $user = $this->getUser();
        if ($this->isGranted('IS_IMPERSONATOR')) {
            $token = $this->container->get('security.token_storage')->getToken();
            if ($token instanceof SwitchUserToken) {
                $user = $token->getOriginalToken()->getUser();
            }
        }

        $body = json_decode($request->getContent(), true) ?? [];
        $violations = $this->validateUpdateBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }

        if (isset($body['training'])) {
            $currentTrainingType = $subscription->getTrainingType();
            $alreadyHadTrial = !empty($subscription->getTrainingTrialEndDate());
            $newTrainingType = $body['training']['type'];
            if (!($securityExtended->isGranted($user, 'ROLE_ALLOWED_TO_SWITCH'))
                && ($alreadyHadTrial || $currentTrainingType != SubscriptionTrainingTypes::NONE()->getValue() || $newTrainingType != SubscriptionTrainingTypes::TRIAL()->getValue())
                && (!$subscription->getTrainingPartnership() || !array_key_exists($subscription->getTrainingPartnership(), SubscriptionService::TRAINING_PARTNERSHIP_SUBSCRIPTION))
            ) {
                throw new WedofSubscriptionException("Erreur, vous ne pouvez pas mettre à jour votre abonnement, veuillez contacter le support Wedof à <EMAIL>");
            }
        }

        if (isset($body['certifier'])) {
            $currentCertifierType = $subscription->getCertifierType();
            $alreadyHadTrial = !empty($subscription->getCertifierTrialEndDate());
            $newCertifierType = $body['certifier']['type'];
            if (!($securityExtended->isGranted($user, 'ROLE_ALLOWED_TO_SWITCH'))
                && ($alreadyHadTrial || !in_array($currentCertifierType, [SubscriptionCertifierTypes::NONE(), SubscriptionCertifierTypes::PARTNER()]) || $newCertifierType != SubscriptionCertifierTypes::TRIAL()->getValue())
                && (!$subscription->getCertifierPartnership() || !array_key_exists($subscription->getCertifierPartnership(), SubscriptionService::CERTIFIER_PARTNERSHIP_SUBSCRIPTION))
            ) {
                throw new WedofSubscriptionException("Erreur, vous ne pouvez pas mettre à jour votre abonnement, veuillez contacter le support Wedof à <EMAIL>");
            }
        }

        return $subscriptionService->switch($subscription, $body);
    }

    /**
     * @Rest\Delete("/api/subscriptions/{id}")
     * @Security("is_granted('ROLE_OAUTH2_SUBSCRIPTION:WRITE') or is_granted('ROLE_ADMIN')", message="not allowed")
     * @Rest\View(StatusCode = 501)
     *
     * @param Subscription $subscription
     * @return WedofBadRequestHttpException
     */
    public function delete(Subscription $subscription): WedofBadRequestHttpException
    {
        throw new WedofBadRequestHttpException("Méthode non supportée - L'abonnement d'id " . $subscription->getId() . " ne peut être supprimé.");
    }

    /**
     * @Rest\Put("/app/subscriptions/reseller/{id}")
     * @IsGranted(SubscriptionVoter::EDIT, subject="subscription", message="not allowed")
     * @Rest\View(statusCode=200)
     *
     * @param Subscription $subscription
     * @param SubscriptionService $subscriptionService
     * @return Subscription
     * @throws Throwable
     */
    public function updateFromReseller(Subscription $subscription, SubscriptionService $subscriptionService): Subscription
    {
        $organism = $subscription->getOrganism();
        if ($organism->isReseller()) {
            throw  new WedofBadRequestHttpException("Erreur, en tant que revendeur vous n'êtes pas autorisé à modifier votre abonnement");
        }
        if (!$organism->getReseller()) {
            throw new WedofBadRequestHttpException("Erreur, vous n'avez pas accès à cette fonctionnalité");
        }
        return $subscriptionService->createOrUpdateFromReseller($organism, $organism->getReseller());
    }

    //----------------
    // METHODES PRIVES
    //----------------
    /**
     * @param $body
     * @return ConstraintViolationListInterface
     */
    private function validateCreateBody($body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'training' => new Assert\Optional([
                new Assert\Collection([
                    'type' => new Assert\Optional([
                        new Assert\NotBlank(),
                        new Assert\Choice(SubscriptionTrainingTypes::VALUES_TYPES_TO_STRING)
                    ]),
                    'partnership' => new Assert\Optional([new Assert\Length(['max' => 255])])
                ])
            ]),
            'certifier' => new Assert\Optional([
                new Assert\Collection([
                    'type' => new Assert\Optional([
                        new Assert\NotBlank(),
                        new Assert\Choice(SubscriptionCertifierTypes::VALUES_TYPES_TO_STRING)
                    ]),
                    'partnership' => new Assert\Optional([new Assert\Length(['max' => 255])])
                ])
            ])
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateUpdateBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'training' => new Assert\Optional([
                new Assert\Collection([
                    'type' => new Assert\Required([
                        new Assert\NotBlank(),
                        new Assert\Choice(SubscriptionTrainingTypes::VALUES_TYPES_TO_STRING)
                    ])
                ])

            ]),
            'certifier' => new Assert\Optional([
                new Assert\Collection([
                    'type' => new Assert\Required([
                        new Assert\NotBlank(),
                        new Assert\Choice(SubscriptionCertifierTypes::VALUES_TYPES_TO_STRING)
                    ])
                ])
            ]),
            'workflow' => new Assert\Optional([
                new Assert\Type("boolean")
            ]),
            'signature' => new Assert\Optional([
                new Assert\Type("boolean")
            ])
        ]);
        return $validator->validate($body, $constraints);
    }
}
