<?php

namespace App\Controller\app;

use App\Entity\UserMessage;
use App\Entity\User;
use App\Exception\WedofBadRequestHttpException;
use App\Library\utils\enums\UserMessageTypes;
use App\Service\UserMessageService;
use App\Service\UserService;
use Doctrine\Common\Collections\ArrayCollection;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\View\View;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validation;

class UserMessageController extends AbstractFOSRestController
{
    //--------------
    // METHODES CRUD
    //--------------
    /**
     * @Rest\Get("app/userMessages/global")
     * @Rest\View(statusCode=200)
     *
     * @param UserMessageService $messageService
     * @return ArrayCollection
     */
    public function listGlobals(UserMessageService $messageService): ArrayCollection
    {
        return $messageService->listWithParams(['visible' => true]);
    }

    /**
     * @Rest\Get("app/userMessages/me")
     * @IsGranted("ROLE_USER", message="not allowed")
     * @Rest\View(statusCode=200)
     *
     * @param UserMessageService $messageService
     * @return ArrayCollection
     */
    public function me(UserMessageService $messageService): ArrayCollection
    {
        /** @var User $user */
        $user = $this->getUser();
        return $messageService->listWithParams(['user' => $user, 'visible' => true]);
    }

    /**
     * @Rest\Post("app/userMessages")
     * @Rest\View(statusCode=200)
     * @IsGranted("ROLE_ADMIN")
     *
     * @return UserMessage|View
     */
    public function create(Request $request, UserMessageService $messageService, UserService $userService)
    {
        $toUser = null;
        $body = json_decode($request->getContent(), true) ?? [];
        $violations = $this->validateCreateBody($body);
        if (isset($body['email'])) {
            $toUser = $userService->getByEmail($body['email']);
            if (!$toUser) {
                throw new WedofBadRequestHttpException("Aucun utilisateur existe avec l'email " . $body['email']);
            }
        }
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }
        $text = $body['message'];
        unset($body['message']);
        return $messageService->create($text, $body, $toUser);
    }

    /**
     * @Rest\Put("app/userMessages/{id}")
     * @Rest\View(statusCode=200)
     * @IsGranted("ROLE_ADMIN")
     *
     * @return UserMessage|View
     */
    public function update(UserMessage $message, Request $request, UserMessageService $messageService)
    {
        $body = json_decode($request->getContent(), true) ?? [];
        $violations = $this->validateUpdateBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }

        return $messageService->updateVisibility($message, $body['visibility']);
    }

    //----------------
    // METHODES PRIVES
    //----------------

    /**
     * @param $body
     * @return ConstraintViolationListInterface
     */
    private function validateCreateBody($body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'message' => new Assert\Required([
                new Assert\NotBlank(),
            ]),
            'type' => new Assert\Required([
                new Assert\NotBlank(),
                new Assert\Choice(array_values(UserMessageTypes::toArray()))
            ]),
            'dismissed' => new Assert\Optional([
                new Assert\Type('boolean'),
            ]),
            'showIcon' => new Assert\Optional([
                new Assert\Type('boolean'),
            ]),
            'visible' => new Assert\Optional([
                new Assert\Type('boolean'),
            ]),
            'email' => new Assert\Optional([
                new Assert\Email(),
            ])
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param $body
     * @return ConstraintViolationListInterface
     */
    private function validateUpdateBody($body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'visibility' => new Assert\Optional([
                new Assert\Type('boolean'),
            ])
        ]);
        return $validator->validate($body, $constraints);
    }
}
