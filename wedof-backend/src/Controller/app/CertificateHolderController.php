<?php


namespace App\Controller\app;

use App\Controller\api\AbstractWedofController;
use App\Entity\CertificationFolder;
use App\Library\utils\enums\CertificationFolderStates;
use App\Library\utils\Tools;
use App\Repository\CertificationFolderRepository;
use App\Service\CertificationFolderService;
use App\Service\CertificationService;
use DateTime;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;

class CertificateHolderController extends AbstractWedofController
{
    /**
     * @Route("/app/public/certificateHolder/{externalId}/{certificateId}",requirements={"code"="\w+\d+","certificateId"="\w+"}, methods={"GET","HEAD"})
     * @Route("/app/public/certificateHolder/test", methods={"GET","HEAD"})
     * @param CertificationFolderRepository $certificationFolderRepository
     * @param CertificationService $certificationService
     * @param Request $request
     * @param string|null $externalId
     * @param string|null $certificateId
     * @param CertificationFolderService $certificationFolderService
     * @return Response
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    public function showHtml(CertificationFolderRepository $certificationFolderRepository, CertificationService $certificationService, Request $request, string $externalId = null, string $certificateId = null, CertificationFolderService $certificationFolderService): Response
    {
        $isTest = Tools::contains($request->getPathInfo(), 'test');
        if ($isTest) {
            $data = self::getMetadata();
            return $this->render('validateCertificateHolder/index.html.twig', [
                'certificationType' => "RS",
                'certificationCode' => 1234,
                'certificationName' => "Certification",
                'certifierName' => "Wedof Certification",
                'attendeeName' => "John Doe",
                'issueDate' => new DateTime('now'),
                'gradePass' => null,
                'expirationDate' => null,
                'data' => $data
            ]);
        } else {
            $certification = $certificationService->getByExternalId($externalId);
            $certificationFolder = $certificationFolderRepository->findOneBy(['certification' => $certification, 'certificateId' => $certificateId]);
            $data = self::getMetadata($certificationFolder);
            if (!empty($certificationFolder) && $certificationFolder->getState() === CertificationFolderStates::SUCCESS()->getValue() && $certificationFolder->isFullCertification()) {
                $badgeAssertion = $certificationFolder->getBadgeAssertion();
                $badgeAssertionData = $badgeAssertion ? $certificationFolderService->verifyBadgeAssertion($badgeAssertion) : null;
                $badgeAssertionImageUrl = $badgeAssertionData ? $badgeAssertionData['image']['id'] : null;
                return $this->render('validateCertificateHolder/index.html.twig', [
                    'certificationType' => $certification->getType(),
                    'certificationCode' => $certification->getCode(),
                    'certificationName' => $certification->getName(),
                    'certifierName' => $certificationFolder->getCertifier()->getName(),
                    'attendeeName' => $certificationFolder->getAttendee()->getDisplayName(),
                    'issueDate' => $certificationFolder->getIssueDate(),
                    'gradePass' => $certificationFolder->getGradePass(),
                    'expirationDate' => $certificationFolder->getExpirationDate(),
                    'data' => $data,
                    'badgeAssertion' => $certificationFolder->getBadgeAssertion(),
                    'badgeAssertionImageUrl' => $badgeAssertionImageUrl
                ]);
            } else {
                return new Response('', 404);
            }
        }
    }

    public static function getMetadata(CertificationFolder $certificationFolder = null): array
    {
        if ($certificationFolder) {
            $host = $certificationFolder->getCertifier()->getSubDomain() . "." . $_ENV["DOMAIN"];
            if (!in_array($_ENV["PORT"], ["80", "443"])) {
                $host = $host . ":" . $_ENV["PORT"];
            }
            $certification = $certificationFolder->getCertification();
            $title = $certificationFolder->getAttendee()->getDisplayName() . " a obtenu la certification : " . $certification->getName() . " (" . $certification->getType() . $certification->getCode() . ") !";
            $description = "Découvrez en détail le résultat de " . $certificationFolder->getAttendee()->getDisplayName() . " qui a obtenu la certification : " . $certification->getName() . " (" . $certification->getType() . $certification->getCode() . ") !";
            $certifier = $certificationFolder->getCertifier();
            return [
                "description" => $description,
                "author_name" => $certifier->getName(),
                "author_email" => count($certifier->getEmails()) > 0 ? $certifier->getEmails()[0] : $certifier->getOwnedBy()->getEmail(),
                "url" => 'https://' . $host . '/app/public/certificateHolder/' . $certification->getType() . '/' . $certification->getCode() . '/' . $certificationFolder->getCertificateId(),
                "title" => $title,
                "host" => $host
            ];
        } else {
            return [
                "description" => "Découvrez en détail le résultat du candidat qui a obtenu la certification",
                "author_name" => "Wedof Certification",
                "author_email" => "<EMAIL>",
                "url" => 'https://www.wedof.fr/app/public/certificateHolder/RS/1234/185968752',
                "title" => "Vérifier la réussite du candidat à la certification",
                "host" => $_ENV["DOMAIN"] . ":" . $_ENV["PORT"]
            ];
        }
    }
}