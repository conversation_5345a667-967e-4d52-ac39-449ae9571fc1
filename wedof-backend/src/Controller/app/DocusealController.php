<?php

namespace App\Controller\app;

use App\Application\MessageTemplates\Entity\MessageTemplate;
use App\Application\MessageTemplates\Repository\MessageTemplateRepository;
use App\Application\MessageTemplates\Service\MessageService;
use App\Entity\RegistrationFolderFile;
use App\Exception\WedofConnectionException;
use App\Library\utils\enums\MessageTemplateStates;
use App\Library\utils\Tools;
use App\Service\DataProviders\DocusealApiService;
use App\Service\OrganismService;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\NotSupported;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use ErrorException;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Throwable;
use Twig\Error\LoaderError;
use Twig\Error\SyntaxError;

class DocusealController extends AbstractFOSRestController

{
    /**
     * @Rest\Post("/app/public/docuseal/sendSmsCode")
     * @Rest\QueryParam(name="token", nullable=false)
     * @param ParamFetcher $paramFetcher
     * @param MessageService $messageService
     * @param OrganismService $organismService
     * @param MessageTemplateRepository $messageTemplateRepository
     * @param Request $request
     * @return Response
     * @throws ContainerExceptionInterface
     * @throws Exception
     * @throws LoaderError
     * @throws NotFoundExceptionInterface
     * @throws NotSupported
     * @throws SyntaxError
     * @throws \Doctrine\DBAL\Driver\Exception
     */
    public function sendSmsCode(ParamFetcher $paramFetcher, MessageService $messageService, OrganismService $organismService, MessageTemplateRepository $messageTemplateRepository, Request $request): Response
    {
        $parameters = $paramFetcher->all(true);
        if ($parameters['token'] != Tools::getEnvValue("DOCUSEAL_MASTER_TOKEN")) {
            return new Response(null, 403);
        }
        $body = json_decode($request->getContent(), true);
        preg_match('/##(\d{14})# (.*) (\d*)/m', $body['message'], $matches, PREG_OFFSET_CAPTURE);
        $siret = $matches[1][0];
        $code = $matches[3][0];

        $organism = $organismService->getBySiret($siret);
        /* @var $messageTemplate MessageTemplate */
        $messageTemplate = $messageTemplateRepository->findOneBy([
            'organism' => $organism,
            'state' => [MessageTemplateStates::HIDDEN()->getValue()],
            'type' => ['sms']
        ]);

        $body = [
            "type" => "sms",
            "to" => [$body['to']],
            "body" => "Votre code de vérification : $code",
            "siret" => $siret
        ];

        $messageService->createOrUpdate($messageTemplate, null, '', null, null, true, $body);
        return new Response(null, 200);
    }

    /**
     * @Rest\Post("/app/public/docuseal/webhook")
     * @param Request $request
     * @param DocusealApiService $docusealApiService
     * @return Response
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function webhook(Request $request, DocusealApiService $docusealApiService): Response
    {
        /*$token = $request->headers->get('token');
        if ($token != Tools::getEnvValue("DOCUSEAL_MASTER_TOKEN")) {
            return new Response(null, 403);
        }*/

        $body = json_decode($request->getContent(), true);
        /** @var RegistrationFolderFile $entityFile */
        $entityFile = $docusealApiService->findEntityFileFromSubmissionId($body['data']['submission_id']);
        if (!$entityFile) {
            return new Response(null, 404);
        }
        $docusealApiService->refreshEntityFileSignedStateAndSendEvents($entityFile);
        $docusealApiService->addActivityOnEntity($entityFile, $body);
        return new Response(null, 200);
    }
}
