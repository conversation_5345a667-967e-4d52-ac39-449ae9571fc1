<?php

namespace App\Entity;

use App\Entity\Traits\TimestampableTrait;
use App\Library\utils\Tools;
use App\Repository\CertificationPartnerRepository;
use Beelab\TagBundle\Entity\AbstractTaggable;
use DateTimeInterface;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Hateoas\Configuration\Annotation as Hateoas;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;
use Symfony\Component\Validator\Constraints as Assert;

/**
 * @ORM\Entity(repositoryClass=CertificationPartnerRepository::class)
 * @ORM\Table(name="certifications_partners")
 * @UniqueEntity(fields={"partner", "certification"})
 * @Serializer\ExclusionPolicy("ALL")
 * @ORM\HasLifecycleCallbacks()
 *
 * @Hateoas\Relation("self", href = "expr('/api/certifications/' ~ object.getCertification().getCertifInfo() ~ '/partners/' ~ object.getPartner().getSiret())", exclusion=@Hateoas\Exclusion(excludeIf = "expr(object.getId() === null)", groups={"owner", "Default", "partnership"}))
 * @Hateoas\Relation("partner", href= "expr('/api/organisms/' ~ object.getPartner().getSiret())", attributes={"name" = "expr(object.getPartner().getName())", "siret" = "expr(object.getPartner().getSiret())"}, exclusion=@Hateoas\Exclusion(groups={"owner", "Default", "partnership"}))
 * @Hateoas\Relation("certifier", href= "expr('/api/organisms/' ~ object.getCertifier().getSiret())", attributes={"name" = "expr(object.getCertifier().getName())", "siret" = "expr(object.getCertifier().getSiret())", "hasOwner" = "expr(object.getCertifier().getOwnedBy() !== null)"}, exclusion=@Hateoas\Exclusion(excludeIf = "expr(object.getCertifier() === null)", groups={"owner", "Default", "partnership"}))
 * @Hateoas\Relation("certification", href = "expr('/api/certifications/' ~ object.getCertification().getCertifInfo())", attributes={"name" = "expr(object.getCertification().getName())", "certifInfo" = "expr(object.getCertification().getCertifInfo())", "externalId" = "expr(object.getCertification().getExternalId())", "id" = "expr(object.getCertification().getId())", "enabled" = "expr(object.getCertification().getEnabled())"}, exclusion=@Hateoas\Exclusion(groups={"owner", "Default"}))
 * @Hateoas\Relation("certificationFolders", href= "expr('/api/certificationFolders/?siret=' ~ object.getPartner().getSiret() ~ '&certifInfo=' ~ object.getCertification().getCertifInfo())", exclusion=@Hateoas\Exclusion(groups="owner"))
 * @Hateoas\Relation("audits", href= "expr('/api/certifications/' ~ object.getCertification().getCertifInfo() ~ '/partners/' ~ object.getPartner().getSiret() ~ '/audits?siret=' ~ object.getPartner().getSiret() ~ '&certifInfo=' ~ object.getCertification().getCertifInfo())", exclusion=@Hateoas\Exclusion(groups={"owner", "Default", "partnership"}))
 */
class CertificationPartner extends AbstractTaggable
{
    use TimestampableTrait;

    public const CLASSNAME = 'CertificationPartner';

    /**
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     * @ORM\Column(type="integer")
     * @Serializer\Expose
     * @Serializer\Groups({"owner"})
     */
    private ?int $id = null;

    /**
     * @ORM\ManyToOne(targetEntity=Organism::class, inversedBy="certificationsPartner")
     * @ORM\JoinColumn(nullable=false)
     */
    private Organism $partner;

    /**
     * @ORM\ManyToOne(targetEntity=Certification::class, inversedBy="certificationPartners")
     * @ORM\JoinColumn(nullable=false)
     */
    private Certification $certification;

    /**
     * @ORM\Column(type="string", nullable=true)
     * @Serializer\Expose
     * @Serializer\Groups({"owner", "Default", "partnership"})
     */
    private ?string $habilitation;

    /**
     * @ORM\Column(type="string", length=20, options={"default":"active"})
     * @Assert\Choice({"draft","processing","aborted","active","refused","revoked"})
     * @Serializer\Expose()
     * @Serializer\Groups({"owner", "partnership"})
     */
    private string $state;

    /**
     * @ORM\OneToMany(targetEntity=CertificationPartnerFile::class, mappedBy="certificationPartner", orphanRemoval=true, fetch="EAGER")
     * @Serializer\Expose()
     * @Serializer\Groups({"owner"})
     */
    private Collection $files;

    /**
     * @ORM\OneToOne(targetEntity=CertificationPartnerHistory::class, cascade={"persist", "remove"})
     * @ORM\JoinColumn(nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"owner"})
     */
    private ?CertificationPartnerHistory $history = null;

    /**
     * @ORM\Column(type="boolean")
     * @Serializer\Expose()
     * @Serializer\Groups({"owner", "partnership"})
     */
    private bool $pendingActivation = false;

    /**
     * @ORM\Column(type="boolean")
     * @Serializer\Expose()
     * @Serializer\Groups({"owner", "partnership"})
     */
    private bool $pendingRevocation = false;

    /**
     * @ORM\Column(type="boolean", options={"default" : false})
     * @Serializer\Expose()
     * @Serializer\Groups({"owner", "partnership"})
     */
    private bool $pendingSuspension = false;

    /**
     * @ORM\Column(type="text", nullable=true)
     * @Serializer\Expose
     * @Serializer\Groups({"owner", "partnership"})
     */
    private ?string $comment = null;

    /**
     * @ORM\Column(type="integer", nullable=true)
     * @Serializer\Expose
     * @Serializer\Groups({"owner"})
     */
    private ?int $estimatedRegistrationFoldersCount = null;

    /**
     * @ORM\ManyToOne(targetEntity=Organism::class)
     * @ORM\JoinColumn(nullable=true)
     */
    private ?Organism $certifier = null;

    /**
     * @ORM\Column(type="float", nullable=true)
     * @Serializer\Expose
     * @Serializer\Groups({"owner", "partnership"})
     */
    private ?float $amountHt = null;

    /**
     * @ORM\Column(type="string", length=50, options={"default" : "none"})
     * @Serializer\Expose
     * @Serializer\Groups({"owner", "partnership"})
     */
    private string $compliance = "none";

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"owner", "partnership"})
     */
    private ?DateTimeInterface $complianceLastUpdate = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"owner", "partnership"})
     */
    private ?DateTimeInterface $stateLastUpdate = null;

    /**
     * @ORM\ManyToMany(targetEntity=Tag::Class)
     * Display with serializer to get as array[string] and not as objet
     * CANNOT BE TYPED /!\
     * Don't forget to override setTagsText() and change updatedOn as done below!
     */
    protected $tags; // Type must not be defined (as in base class '\Beelab\TagBundle\Entity\AbstractTaggable')

    /**
     * @ORM\Column(type="json", nullable=true)
     */
    private ?array $metadata = [];

    /**
     * @ORM\Column(type="json", nullable=true)
     * @Serializer\Type("array")
     * @Serializer\Expose()
     * @Serializer\Groups({"owner", "partnership"})
     */
    private ?array $trainingsZone = null;

    /**
     * @ORM\ManyToMany(targetEntity=Skill::class)
     * @Serializer\Expose()
     * @Serializer\Groups({"owner", "partnership"})
     */
    private Collection $skillSets;

    /**
     * @ORM\Column(type="array", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"owner", "partnership"})
     */
    private ?array $urls = [];

    public function __construct()
    {
        $this->files = new ArrayCollection();
        parent::__construct(); // required by tags
        $this->skillSets = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * @return Organism
     */
    public function getPartner(): Organism
    {
        return $this->partner;
    }

    /**
     * @param Organism $partner
     */
    public function setPartner(Organism $partner): void
    {
        $this->partner = $partner;
    }

    /**
     * @return Certification
     */
    public function getCertification(): Certification
    {
        return $this->certification;
    }

    /**
     * @param Certification $certification
     */
    public function setCertification(Certification $certification): void
    {
        $this->certification = $certification;
    }

    /**
     * @return string|null
     */
    public function getHabilitation(): ?string
    {
        return $this->habilitation;
    }

    /**
     * @param string|null $habilitation
     */
    public function setHabilitation(?string $habilitation): void
    {
        $this->habilitation = $habilitation;
    }

    public function getState(): string
    {
        return $this->state;
    }

    public function setState(string $state): self
    {
        $this->state = $state;

        return $this;
    }

    /**
     * @return Collection|CertificationPartnerFile[]
     */
    public function getFiles(): Collection
    {
        return $this->files;
    }

    /**
     * @param CertificationPartnerFile $file
     * @return $this
     */
    public function addFile(CertificationPartnerFile $file): self
    {
        if (!$this->files->contains($file)) {
            $this->files[] = $file;
            $file->setCertificationPartner($this);
        }

        return $this;
    }

    /**
     * @param CertificationPartnerFile $file
     * @return $this
     */
    public function removeFile(CertificationPartnerFile $file): self
    {
        if ($this->files->removeElement($file)) {
            if ($file->getCertificationPartner() === $this) {
                $file->setCertificationPartner(null);
            }
        }

        return $this;
    }

    /**
     * @return CertificationPartnerHistory|null
     */
    public function getHistory(): ?CertificationPartnerHistory
    {
        return $this->history;
    }

    /**
     * @param CertificationPartnerHistory $certificationPartnerHistory
     * @return $this
     */
    public function setHistory(CertificationPartnerHistory $certificationPartnerHistory): self
    {
        $this->history = $certificationPartnerHistory;

        return $this;
    }

    public function getComment(): ?string
    {
        return $this->comment;
    }

    public function setComment(?string $comment): self
    {
        $this->comment = $comment;

        return $this;
    }

    public function isPendingActivation(): bool
    {
        return $this->pendingActivation;
    }

    public function setPendingActivation(bool $pendingActivation): self
    {
        $this->pendingActivation = $pendingActivation;

        return $this;
    }

    public function isPendingRevocation(): bool
    {
        return $this->pendingRevocation;
    }

    public function setPendingRevocation(bool $pendingRevocation): self
    {
        $this->pendingRevocation = $pendingRevocation;

        return $this;
    }

    public function isPendingSuspension(): bool
    {
        return $this->pendingSuspension;
    }

    public function setPendingSuspension(bool $pendingSuspension): self
    {
        $this->pendingSuspension = $pendingSuspension;

        return $this;
    }

    public function getEstimatedRegistrationFoldersCount(): ?int
    {
        return $this->estimatedRegistrationFoldersCount;
    }

    public function setEstimatedRegistrationFoldersCount(?int $estimatedRegistrationFoldersCount): self
    {
        $this->estimatedRegistrationFoldersCount = $estimatedRegistrationFoldersCount;

        return $this;
    }

    public function getCertifier(): ?Organism
    {
        return $this->certifier;
    }

    public function setCertifier(?Organism $certifier): self
    {
        $this->certifier = $certifier;

        return $this;
    }

    public function getEntityId(): string
    {
        return $this->getId();
    }

    public function getPartnerLink(string $shortcut = null): string
    {
        $certifInfo = $this->getCertification()->getCertifInfo();
        $externalId = $this->getCertification()->getExternalId();
        return Tools::getEnvValue('WEDOF_BASE_URI') . '/formation/certifications/catalogue/' . $certifInfo . ($shortcut ? '/' . $shortcut : '') . '?query=' . $externalId;
    }

    public function getCertifierLink(): string
    {
        $certifInfo = $this->getCertification()->getCertifInfo();
        $partnerSiret = $this->getPartner()->getSiret();
        return Tools::getEnvValue('WEDOF_BASE_URI') . '/certification/partenariats/' . $certifInfo . '/' . $partnerSiret;
    }

    public function getAmountHt(): ?float
    {
        return $this->amountHt;
    }

    public function setAmountHt(?float $amountHt): self
    {
        $this->amountHt = $amountHt;

        return $this;
    }

    public function getCompliance(): ?string
    {
        return $this->compliance;
    }

    public function setCompliance(?string $compliance): self
    {
        $this->compliance = $compliance;

        return $this;
    }

    public function getComplianceLastUpdate(): ?DateTimeInterface
    {
        return $this->complianceLastUpdate;
    }

    public function setComplianceLastUpdate(?DateTimeInterface $complianceLastUpdate): self
    {
        $this->complianceLastUpdate = $complianceLastUpdate;

        return $this;
    }

    public function getStateLastUpdate(): ?DateTimeInterface
    {
        return $this->stateLastUpdate;
    }

    public function setStateLastUpdate(?DateTimeInterface $stateLastUpdate): self
    {
        $this->stateLastUpdate = $stateLastUpdate;

        return $this;
    }

    // Required on every taggable so that if only tags are changed, they are persisted
    public function setTagsText(?string $tagsText): void
    {
        if ($tagsText !== $this->getTagsText()) {
            $this->updateUpdatedOnTimestamp();
        }
        parent::setTagsText($tagsText);
    }

    public function getMetadata(): ?array
    {
        return $this->metadata;
    }

    public function setMetadata(?array $metadata): self
    {
        $this->metadata = $metadata;

        return $this;
    }

    /**
     * @return Collection<int, Skill>
     */
    public function getSkillSets(): Collection
    {
        return $this->skillSets;
    }

    public function setSkillSets(iterable $skillSets): self
    {
        $this->skillSets->clear();
        foreach ($skillSets as $skillSet) {
            $this->addSkillSet($skillSet);
        }
        return $this;
    }

    public function addSkillSet(Skill $skillSet): self
    {
        if (!$this->skillSets->contains($skillSet)) {
            $this->skillSets[] = $skillSet;
        }

        return $this;
    }

    public function removeSkillSet(Skill $skillSet): self
    {
        $this->skillSets->removeElement($skillSet);

        return $this;
    }

    public function getTrainingsZone(): ?array
    {
        return $this->trainingsZone;
    }

    public function setTrainingsZone(?array $trainingsZone): self
    {
        $this->trainingsZone = $trainingsZone;

        return $this;
    }

    public function getUrls(): ?array
    {
        return $this->urls;
    }

    public function setUrls(?array $urls): self
    {
        $this->urls = $urls;

        return $this;
    }
}
