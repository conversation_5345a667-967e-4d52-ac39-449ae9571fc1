<?php

namespace App\Entity;

use App\Entity\Traits\TimestampableTrait;
use App\Library\utils\enums\CertificationSkillType;
use App\Repository\SkillRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;

/**
 * @ORM\Entity(repositoryClass=SkillRepository::class)
 * @ORM\HasLifecycleCallbacks()
 * @Serializer\ExclusionPolicy("ALL")
 * @Serializer\Exclude(if="object.getIndividualSkills() === null")
 */
class Skill
{

    use TimestampableTrait;

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Serializer\Expose
     * @Serializer\Groups({"Default", "owner", "partnership"})
     */
    private int $id;

    /**
     * @ORM\ManyToOne(targetEntity=Certification::class, inversedBy="skills")
     * @ORM\JoinColumn(nullable=false)
     */
    private Certification $certification;

    /**
     * @ORM\Column(type="text")
     * @Serializer\Expose
     * @Serializer\Groups({"Default", "owner", "partnership", "attendee"})
     */
    private string $label;

    /**
     * @ORM\Column(name="`order`", type="integer", length=25)
     * @Serializer\Groups({"Default", "owner", "partnership", "attendee"})
     * @Serializer\Expose
     */
    private int $order;

    /**
     * @ORM\Column(type="text", nullable=true)
     * @Serializer\Groups({"Default", "owner", "partnership"})
     * @Serializer\Expose
     */
    private ?string $modalities = null;

    /**
     * @ORM\Column(type="string", length=255, options={"default" : "skill"})
     * @Serializer\Groups({"Default", "owner", "partnership"})
     * @Serializer\Expose
     */
    private string $type = 'skill';

    /**
     * @ORM\ManyToOne(targetEntity=Skill::class, inversedBy="individualSkills")
     */
    private ?Skill $parentSkill = null;

    /**
     * @ORM\OneToMany(targetEntity=Skill::class, mappedBy="parentSkill")
     * @Serializer\Groups({"fullSkills"})
     * @Serializer\Expose
     */
    private Collection $individualSkills;

    /**
     * @ORM\Column(type="text", nullable=true)
     * @Serializer\Groups({"Default", "owner", "partnership"})
     * @Serializer\Expose
     */
    private ?string $description = null;

    public function __construct()
    {
        $this->individualSkills = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getCertification(): ?Certification
    {
        return $this->certification;
    }

    public function setCertification(Certification $certification): self
    {
        $this->certification = $certification;

        return $this;
    }

    public function getLabel(): ?string
    {
        return $this->label;
    }

    public function setLabel(string $label): self
    {
        $this->label = $label;

        return $this;
    }

    public function getOrder(): ?int
    {
        return $this->order;
    }

    public function setOrder(int $order): self
    {
        $this->order = $order;

        return $this;
    }

    public function getModalities(): ?string
    {
        return $this->modalities;
    }

    public function setModalities(?string $modalities): self
    {
        $this->modalities = $modalities;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getParentSkill(): ?self
    {
        return $this->parentSkill;
    }

    public function setParentSkill(?self $parentSkill): self
    {
        $this->parentSkill = $parentSkill;

        return $this;
    }

    /**
     * @return Collection<int, self>
     */
    public function getIndividualSkills(): Collection
    {
        return $this->individualSkills;
    }

    public function addIndividualSkill(self $individualSkill): self
    {
        if (!$this->individualSkills->contains($individualSkill)) {
            $this->individualSkills[] = $individualSkill;
            $individualSkill->setParentSkill($this);
        }

        return $this;
    }

    public function removeIndividualSkill(self $individualSkill): self
    {
        if ($this->individualSkills->removeElement($individualSkill)) {
            // set the owning side to null (unless already changed)
            if ($individualSkill->getParentSkill() === $this) {
                $individualSkill->setParentSkill(null);
            }
        }

        return $this;
    }

    /**
     * @Serializer\VirtualProperty()
     * @Serializer\SerializedName("fullCode")
     * @Serializer\Groups({"Default", "owner", "partnership"})
     * @Serializer\Expose
     */
    public function getFullCode(): string
    {
        $code = $this->getCertification()->getExternalId();
        $parentSkill = $this->getParentSkill();
        $skillOrder = sprintf('%02d', $this->getOrder());

        if ($this->getType() === CertificationSkillType::SKILL()->getValue()) {
            if ($parentSkill) {
                $parentSkillOrder = sprintf('%02d', $parentSkill->getOrder());
                $code .= "BC" . $parentSkillOrder . "C" . $skillOrder;
            } else {
                $code .= "C" . $skillOrder;
            }
        } else {
            $code .= "BC" . $skillOrder;
        }

        return $code;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): self
    {
        $this->description = $description;

        return $this;
    }
}
