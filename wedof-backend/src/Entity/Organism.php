<?php

namespace App\Entity;

use App\Library\utils\enums\DataProviders;
use App\Library\utils\Tools;
use App\Repository\OrganismRepository;
use DateTime;
use DateTimeInterface;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Hateoas\Configuration\Annotation as Hateoas;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;
use PHPUnit\Util\Exception;
use Symfony\Component\HttpFoundation\File\File;
use Vich\UploaderBundle\Mapping\Annotation as Vich;

/**
 * @ORM\Entity(repositoryClass=OrganismRepository::class)
 * @Vich\Uploadable
 * @Serializer\ExclusionPolicy("ALL")
 *
 * @Hateoas\Relation("self", href = "expr('/api/organisms/' ~ object.getSiret())", exclusion=@Hateoas\Exclusion(groups={"app", "Default", "public"}))
 * @Hateoas\Relation("payments", href= "expr('/api/payments?siret=' ~ object.getSiret())", exclusion=@Hateoas\Exclusion(groups={"app", "Default"}))
 * @Hateoas\Relation("certifierCertifications", href = "expr('/api/certifications?organismType=certifier&siret=' ~ object.getSiret())", exclusion=@Hateoas\Exclusion(groups={"app", "Default"}))
 * @Hateoas\Relation("partnersCertifications", href = "expr('/api/certifications?organismType=partner&siret=' ~ object.getSiret())", exclusion=@Hateoas\Exclusion(groups={"app", "Default"}))
 * @Hateoas\Relation("trainings", href = "expr('/api/trainings?siret=' ~ object.getSiret())", exclusion=@Hateoas\Exclusion(groups={"app", "Default"}))
 * @Hateoas\Relation("registrationFolders", href = "expr('/api/registrationFolders?siret=' ~ object.getSiret())", exclusion=@Hateoas\Exclusion(groups={"app", "Default"}))
 * @Hateoas\Relation("evaluations", href = "expr('/api/evaluations?siret=' ~ object.getSiret())", exclusion=@Hateoas\Exclusion(groups={"app", "Default"}))
 * @Hateoas\Relation("subscription", href = "expr('/api/subscriptions/' ~ object.getSubscription().getId())", exclusion=@Hateoas\Exclusion(excludeIf = "expr(object.getSubscription() === null)", groups={"app"}))
 * @Hateoas\Relation("reseller", href = "expr('/api/organisms/' ~ object.getReseller().getSiret())", attributes={"name" = "expr(object.getReseller().getName())", "color" ="expr(object.getReseller().getCustomColorScheme())", "logo"="expr(object.getReseller().getLogo())", "siret"="expr(object.getReseller().getSiret())"}, exclusion=@Hateoas\Exclusion(excludeIf = "expr(object.getReseller() === null)", groups={"app", "Default"}))
 */
class Organism
{
    public const CLASSNAME = 'Organism';

    /**
     * @ORM\Id()
     * @ORM\GeneratedValue()
     * @ORM\Column(type="integer")
     */
    private int $id;

    /**
     * @ORM\Column(type="text")
     * @Serializer\Expose
     * @Serializer\Groups({"app", "Default", "public", "webhook", "attendee"})
     */
    private string $name;

    /**
     * @ORM\Column(type="text", length=255, nullable=true)
     */
    private ?string $name_siret = null;

    /**
     * @ORM\Column(type="string", length=255)
     * @Serializer\Expose
     * @Serializer\Groups({"app", "Default", "public", "attendee"})
     */
    private string $siren;

    /**
     * @ORM\Column(type="string", length=255, unique=true)
     * @Serializer\Expose
     * @Serializer\Groups({"app", "Default", "public", "webhook", "attendee"})
     */
    private string $siret;

    /**
     * @ORM\Column(type="text")
     * @Serializer\Expose
     * @Serializer\Groups({"app", "Default", "public", "attendee"})
     */
    private string $address;

    /**
     * @ORM\Column(type="string", length=255)
     * @Serializer\Expose
     * @Serializer\Groups({"app", "Default", "public", "attendee"})
     */
    private string $city;

    /**
     * @ORM\Column(type="string", length=255)
     * @Serializer\Expose
     * @Serializer\Groups({"app", "Default", "public", "attendee"})
     */
    private string $postalCode;

    /**
     * @ORM\Column(type="datetime")
     */
    private DateTimeInterface $lastUpdate;

    /**
     * @ORM\Column(type="array", nullable=true)
     * @Serializer\Expose
     * @Serializer\Groups({"app", "Default", "public", "attendee"})
     */
    private ?array $phones = [];

    /**
     * @ORM\Column(type="array", nullable=true)
     * @Serializer\Expose
     * @Serializer\Groups({"app", "Default", "public", "attendee"})
     */
    private ?array $emails = [];

    /**
     * @ORM\Column(type="float", nullable=true)
     */
    private ?float $latitude = null;

    /**
     * @ORM\Column(type="float", nullable=true)
     */
    private ?float $longitude = null;

    /**
     * @ORM\Column(type="string", nullable=true)
     * @Serializer\Expose
     * @Serializer\Groups({"app", "Default", "public", "attendee"})
     */
    private ?string $agreement = null;

    /**
     * @ORM\Column(type="array", nullable=true)
     * @Serializer\Expose
     * @Serializer\Groups({"app", "Default", "public", "attendee"})
     */
    private ?array $urls = [];

    /**
     * @ORM\Column(type="boolean")
     */
    private bool $toSync;

    /**
     * @ORM\OneToMany(targetEntity=Payment::class, mappedBy="organism", orphanRemoval=true)
     */
    private Collection $payments;

    /**
     * @ORM\OneToOne(targetEntity=User::class, inversedBy="ownedOrganism", cascade={"persist", "remove"})
     */
    private ?User $ownedBy = null;

    /**
     * @ORM\OneToMany(targetEntity=CertificationPartner::class, mappedBy="partner", fetch="EXTRA_LAZY")
     */
    private Collection $certificationsPartner;

    /**
     * @ORM\ManyToMany(targetEntity=Certification::class, mappedBy="certifiers")
     */
    private Collection $certifierCertifications;

    /**
     * @ORM\OneToMany(targetEntity=Training::class, mappedBy="organism", orphanRemoval=true, fetch="EXTRA_LAZY")
     */
    private Collection $trainings;

    /**
     * @ORM\OneToMany(targetEntity=RegistrationFolder::class, mappedBy="organism", orphanRemoval=true)
     */
    private Collection $registrationFolders;

    /**
     * @ORM\OneToMany(targetEntity=User::class, mappedBy="mainOrganism")
     */
    private Collection $mainUsers;

    /**
     * @ORM\OneToMany(targetEntity=Evaluation::class, mappedBy="organism", orphanRemoval=true)
     */
    private Collection $evaluations;

    /**
     * @ORM\Column(type="boolean")
     */
    private bool $customizedInfos;

    /**
     * @ORM\OneToMany(targetEntity=Proposal::class, mappedBy="organism")
     */
    private Collection $proposals;

    /**
     * @ORM\OneToMany(targetEntity=Application::class, mappedBy="organism", orphanRemoval=true)
     */
    private Collection $applications;

    /**
     * @ORM\Column(type="string", length=255, nullable=true, unique=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"app", "public"})
     */
    private ?string $subDomain = null;

    /**
     * @ORM\Column(type="float", nullable=true)
     * @Serializer\Expose(if="isGrantedAsWebUser()")
     * @Serializer\Groups({"app"})
     */
    private ?float $vat = null;

    /**
     * @ORM\OneToMany(targetEntity=CertifierAccess::class, mappedBy="certifier")
     */
    private Collection $certifierAccesses;

    /**
     * @ORM\OneToMany(targetEntity=CertifierAccess::class, mappedBy="partner")
     */
    private Collection $partnerAccesses;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Expose
     * @Serializer\Groups({"app"})
     */
    private ?string $applicantType = null;

    /**
     * @ORM\Column(type="string", length=20, nullable=true)
     * @Serializer\Expose
     * @Serializer\Groups({"app"})
     */
    private ?string $cdcContractId = null;

    /**
     * @ORM\Column(type="string", length=8, nullable=true)
     * @Serializer\Expose
     * @Serializer\Groups({"app"})
     */
    private ?string $cdcClientId = null;

    /**
     * @ORM\Column(type="boolean")
     */
    private ?bool $cpfUnlisted = false;

    /**
     * @ORM\OneToOne(targetEntity=Subscription::class, mappedBy="organism", cascade={"persist", "remove"})
     */
    private ?Subscription $subscription;

    /**
     * @ORM\OneToMany(targetEntity=Connection::class, mappedBy="organism", orphanRemoval=true)
     */
    private Collection $connections;


    /**
     * Not exposed directly see the getter
     * @ORM\Column(type="json", nullable=true)
     */
    private ?array $registrationFolderFileTypes = [];

    /**
     * @ORM\ManyToOne(targetEntity=Company::class, inversedBy="organisms")
     */
    private ?Company $company = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Expose
     * @Serializer\Groups({"app"})
     */
    private ?DateTimeInterface $accrochageDelegationDate = null;

    /**
     * @Vich\UploadableField(mapping="publicFile", fileNameProperty="logoName")
     * @var File|null
     */
    private ?File $logoFile = null;

    /**
     * @ORM\Column(type="string", nullable=true)
     * @var string|null
     */
    private ?string $logoName = null;


    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Expose
     * @Serializer\Groups({"app", "attendee", "public"})
     * @var string|null
     */
    private ?string $linkedInPageUrl = null;

    /**
     * @ORM\Column(type="integer", nullable=true)
     * @Serializer\Expose
     * @Serializer\Groups({"app", "attendee", "public"})
     */
    private ?int $linkedInOrganisationId = null;

    /**
     * @ORM\Column(type="string", nullable=true)
     * @Serializer\Expose
     * @Serializer\Groups({"app", "attendee", "public"})
     * @var string|null
     */
    private ?string $customColorScheme = null;

    /**
     * @ORM\Column(type="string", length=5, nullable=true)
     * @Serializer\Expose
     * @Serializer\Groups({"app", "public"})
     */
    private ?string $ape = null;

    /**
     * @ORM\ManyToOne(targetEntity=Organism::class)
     */
    private ?Organism $reseller = null;

    /**
     * @ORM\Column(type="json", nullable=true)
     */
    private ?array $resellerMetadata = null;

    /**
     * @ORM\Column(type="boolean", options={"default" : false})
     */
    private bool $trainingOrganism = false;

    /**
     * @ORM\Column(type="json", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"app", "Default"})
     */
    private ?array $sendAs = null;

    /**
     * @ORM\Column(type="json", nullable=true)
     * @Serializer\Expose
     * @Serializer\Groups({"app", "Default"})
     */
    private ?array $metadata = [];

    /**
     * @ORM\Column(type="boolean", options={"default" : false})
     * @Serializer\Expose
     * @Serializer\Groups({"app", "public"})
     */
    private bool $qualiopiTrainingAction = false;

    /**
     * @ORM\Column(type="boolean", options={"default" : false})
     * @Serializer\Expose
     * @Serializer\Groups({"app", "public"})
     */
    private bool $qualiopiBilanCompetences = false;

    /**
     * @ORM\Column(type="boolean", options={"default" : false})
     * @Serializer\Expose
     * @Serializer\Groups({"app", "public"})
     */
    private bool $qualiopiVAE = false;

    /**
     * @ORM\Column(type="boolean", options={"default" : false})
     * @Serializer\Expose
     * @Serializer\Groups({"app", "public"})
     */
    private bool $qualiopiFormationApprentissage = false;

    /**
     * @ORM\Column(type="string", length=15, nullable=true)
     * @Serializer\Expose
     * @Serializer\Groups({"app"})
     */
    private ?string $uaiNumber = null;

    /**
     * @ORM\Column(type="string", nullable=true)
     * @Serializer\Expose
     * @Serializer\Groups({"app"})
     */
    private ?string $billingSoftware = null;

    /**
     * @ORM\Column(type="string", nullable=true)
     * @Serializer\Expose
     * @Serializer\Groups({"app"})
     */
    private ?string $crm = null;

    /**
     * @ORM\Column(type="json", nullable=true)
     * @Serializer\Expose
     * @Serializer\Groups({"app"})
     */
    private ?array $cpfCatalogMetadata = null;

    /**
     * @Vich\UploadableField(mapping="organismFile", fileNameProperty="cpfCatalogUploadReportName")
     */
    private ?File $cpfCatalogUploadReport = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private ?string $cpfCatalogUploadReportName = null;

    /**
     * @ORM\Column(type="boolean", options={"default" : true})
     * @Serializer\Expose
     * @Serializer\Groups({"app"})
     */
    private bool $allowImportCertificationFolders = true;

    public function __construct()
    {
        $this->certificationsPartner = new ArrayCollection();
        $this->certifierCertifications = new ArrayCollection();
        $this->trainings = new ArrayCollection();
        $this->registrationFolders = new ArrayCollection();
        $this->mainUsers = new ArrayCollection();
        $this->payments = new ArrayCollection();
        $this->evaluations = new ArrayCollection();
        $this->proposals = new ArrayCollection();
        $this->applications = new ArrayCollection();
        $this->certifierAccesses = new ArrayCollection();
        $this->partnerAccesses = new ArrayCollection();
        $this->connections = new ArrayCollection();
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getNameSiret(): ?string
    {
        return $this->name_siret;
    }

    public function setNameSiret(?string $name_siret): self
    {
        $this->name_siret = $name_siret;

        return $this;
    }

    public function getSiren(): string
    {
        return $this->siren;
    }

    public function setSiren(string $siren): self
    {
        $this->siren = $siren;

        return $this;
    }

    public function getSiret(): string
    {
        return $this->siret;
    }

    public function setSiret(string $siret): self
    {
        $this->siret = $siret;

        return $this;
    }

    public function getAddress(): string
    {
        return $this->address;
    }

    public function setAddress(string $address): self
    {
        $this->address = $address;

        return $this;
    }

    public function getCity(): string
    {
        return $this->city;
    }

    public function setCity(string $city): self
    {
        $this->city = $city;

        return $this;
    }

    public function getPostalCode(): string
    {
        return $this->postalCode;
    }

    public function setPostalCode(string $postalCode): self
    {
        $this->postalCode = $postalCode;

        return $this;
    }

    /**
     * @Serializer\VirtualProperty()
     * @Serializer\SerializedName("fullAddress")
     * @Serializer\Expose
     * @Serializer\Groups({"app", "Default", "public", "attendee"})
     */
    public function getFullAddress(): string
    {
        return $this->getAddress() . ', ' . $this->getPostalCode() . ' ' . $this->getCity();
    }

    /**
     * @return Collection|CertificationPartner[]
     */
    public function getCertificationsPartner(): Collection
    {
        return $this->certificationsPartner;
    }

    public function addCertificationsPartner(CertificationPartner $certificationsPartner): self
    {
        if (!$this->certificationsPartner->contains($certificationsPartner)) {
            $this->certificationsPartner[] = $certificationsPartner;
        }
        return $this;
    }

    public function removeCertificationsPartner(CertificationPartner $certificationsPartner): self
    {
        if ($this->certificationsPartner->contains($certificationsPartner)) {
            $this->certificationsPartner->removeElement($certificationsPartner);
        }
        return $this;
    }

    /**
     * @return Collection|Certification[]
     */
    public function getCertifierCertifications(): Collection
    {
        return $this->certifierCertifications;
    }

    public function addCertifierCertification(Certification $certifierCertification): self
    {
        if (!$this->certifierCertifications->contains($certifierCertification)) {
            $this->certifierCertifications[] = $certifierCertification;
            $certifierCertification->addCertifier($this);
        }

        return $this;
    }

    public function removeCertifierCertification(Certification $certifierCertification): self
    {
        if ($this->certifierCertifications->contains($certifierCertification)) {
            $this->certifierCertifications->removeElement($certifierCertification);
            $certifierCertification->removeCertifier($this);
        }

        return $this;
    }

    /**
     * @return Collection|Training[]
     */
    public function getTrainings(): Collection
    {
        return $this->trainings;
    }

    public function addTraining(Training $training): self
    {
        if (!$this->trainings->contains($training)) {
            $this->trainings[] = $training;
            $training->setOrganism($this);
        }

        return $this;
    }

    public function removeTraining(Training $training): self
    {
        if ($this->trainings->contains($training)) {
            $this->trainings->removeElement($training);
            // set the owning side to null (unless already changed)
            if ($training->getOrganism() === $this) {
                $training->setOrganism(null);
            }
        }

        return $this;
    }

    public function getLastUpdate(): DateTimeInterface
    {
        return $this->lastUpdate;
    }

    public function setLastUpdate(DateTimeInterface $lastUpdate): self
    {
        $this->lastUpdate = $lastUpdate;

        return $this;
    }

    public function getPhones(): array
    {
        return $this->phones;
    }

    public function setPhones(?array $phones): self
    {
        $this->phones = $phones;

        return $this;
    }

    public function getEmails(): array
    {
        return $this->emails;
    }

    public function setEmails(?array $emails): self
    {
        $this->emails = $emails;

        return $this;
    }

    public function getLatitude(): ?float
    {
        return $this->latitude;
    }

    public function setLatitude(?float $latitude): self
    {
        $this->latitude = $latitude;

        return $this;
    }

    public function getLongitude(): ?float
    {
        return $this->longitude;
    }

    public function setLongitude(?float $longitude): self
    {
        $this->longitude = $longitude;

        return $this;
    }

    public function getAgreement(): ?string
    {
        return $this->agreement;
    }

    public function setAgreement(?string $agreement): self
    {
        $this->agreement = $agreement;

        return $this;
    }

    public function getUrls(): array
    {
        return $this->urls;
    }

    public function setUrls(?array $urls): self
    {
        $this->urls = $urls;

        return $this;
    }

    /**
     * @return Collection|RegistrationFolder[]
     */
    public function getRegistrationFolders(): Collection
    {
        return $this->registrationFolders;
    }

    public function addRegistrationFolder(RegistrationFolder $registrationFolder): self
    {
        if (!$this->registrationFolders->contains($registrationFolder)) {
            $this->registrationFolders[] = $registrationFolder;
            $registrationFolder->setOrganism($this);
        }

        return $this;
    }

    public function removeRegistrationFolder(RegistrationFolder $registrationFolder): self
    {
        if ($this->registrationFolders->contains($registrationFolder)) {
            $this->registrationFolders->removeElement($registrationFolder);
            // set the owning side to null (unless already changed)
            if ($registrationFolder->getOrganism() === $this) {
                $registrationFolder->setOrganism(null);
            }
        }

        return $this;
    }

    public function addMainUser(User $user): self
    {
        $this->mainUsers[] = $user;

        //link Organism to User
        $user->setMainOrganism($this);

        return $this;
    }

    public function removeMainUser(User $user)
    {
        $this->mainUsers->removeElement($user);
    }

    /**
     * @return Collection|User[]
     */
    public function getMainUsers(): Collection
    {
        return $this->mainUsers;
    }

    public function getToSync(): bool
    {
        return $this->toSync;
    }

    public function setToSync(bool $toSync): self
    {
        $this->toSync = $toSync;

        return $this;
    }

    public function toString(): string
    {
        return $this->siret . " " . $this->name;
    }

    /**
     * @return Collection|Payment[]
     */
    public function getPayments(): Collection
    {
        return $this->payments;
    }

    public function addPayment(Payment $payment): self
    {
        if (!$this->payments->contains($payment)) {
            $this->payments[] = $payment;
            $payment->setOrganism($this);
        }

        return $this;
    }

    public function removePayment(Payment $payment): self
    {
        if ($this->payments->contains($payment)) {
            $this->payments->removeElement($payment);
            // set the owning side to null (unless already changed)
            if ($payment->getOrganism() === $this) {
                $payment->setOrganism(null);
            }
        }

        return $this;
    }

    /**
     * @Serializer\VirtualProperty()
     * @Serializer\Expose()
     * @Serializer\Groups({"app", "Default", "public"})
     */
    public function isCertifierOrganism(): bool
    {
        return !$this->getCertifierCertifications()->isEmpty();
    }

    /**
     * @Serializer\VirtualProperty()
     * @Serializer\Expose()
     * @Serializer\Groups({"app", "public"})
     */
    public function hasUsers(): bool
    {
        return $this->getOwnedBy() && $this->getOwnedBy()->getRgpdMsa(); // TODO à supprimer avec le multi user sur un même organisme
    }

    /**
     * @Serializer\VirtualProperty()
     * @Serializer\Expose()
     * @Serializer\Groups({"app", "Default", "public"})
     */
    public function isNonDiffusible(): bool
    {
        // Cf. https://entreprise.api.gouv.fr/catalogue/insee/etablissements_diffusibles
        // Cf. https://entreprise.api.gouv.fr/blog/insee-non-diffusibles
        return $this->getName() === '[ND]';
    }

    public function getOwnedBy(): ?User
    {
        return $this->ownedBy;
    }

    public function setOwnedBy(?User $ownedBy): self
    {
        $this->ownedBy = $ownedBy;

        return $this;
    }

    public function __toString(): string
    {
        return $this->siret . " " . $this->getName();
    }

    /**
     * @return Collection|Evaluation[]
     */
    public function getEvaluations(): Collection
    {
        return $this->evaluations;
    }

    public function addEvaluation(Evaluation $evaluation): self
    {
        if (!$this->evaluations->contains($evaluation)) {
            $this->evaluations[] = $evaluation;
            $evaluation->setOrganism($this);
        }

        return $this;
    }

    public function removeEvaluation(Evaluation $evaluation): self
    {
        if ($this->evaluations->removeElement($evaluation)) {
            // set the owning side to null (unless already changed)
            if ($evaluation->getOrganism() === $this) {
                $evaluation->setOrganism(null);
            }
        }

        return $this;
    }

    public function hasCustomizedInfos(): bool
    {
        return $this->customizedInfos;
    }

    /**
     * @Serializer\VirtualProperty()
     * @Serializer\Expose()
     * @Serializer\Groups({"app"})
     */
    public function hasPartnerAccess(): bool
    {
        return $this->partnerAccesses->count() > 0;
    }

    public function setCustomizedInfos(bool $customizedInfos): self
    {
        $this->customizedInfos = $customizedInfos;

        return $this;
    }

    /**
     * @return Collection|Proposal[]
     */
    public function getProposals(): Collection
    {
        return $this->proposals;
    }

    public function addProposal(Proposal $proposal): self
    {
        if (!$this->proposals->contains($proposal)) {
            $this->proposals[] = $proposal;
            $proposal->setOrganism($this);
        }

        return $this;
    }

    public function removeProposal(Proposal $proposal): self
    {
        if ($this->proposals->removeElement($proposal)) {
            // set the owning side to null (unless already changed)
            if ($proposal->getOrganism() === $this) {
                $proposal->setOrganism(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection|Application[]
     */
    public function getApplications(): Collection
    {
        return $this->applications;
    }

    public function addApplication(Application $application): self
    {
        if (!$this->applications->contains($application)) {
            $this->applications[] = $application;
            $application->setOrganism($this);
        }

        return $this;
    }

    public function removeApplication(Application $application): self
    {
        if ($this->applications->removeElement($application)) {
            // set the owning side to null (unless already changed)
            if ($application->getOrganism() === $this) {
                $application->setOrganism(null);
            }
        }

        return $this;
    }

    public function getSubDomain(): ?string
    {
        return $this->subDomain;
    }

    public function setSubDomain(?string $subDomain): self
    {
        $this->subDomain = $subDomain;

        return $this;
    }

    public function getVat(): ?float
    {
        return $this->vat;
    }

    public function setVat(?float $vat): self
    {
        $this->vat = $vat;

        return $this;
    }

    /**
     * @return Collection|CertifierAccess[]
     */
    public function getCertifierAccesses(): Collection
    {
        return $this->certifierAccesses;
    }

    public function addCertifierAccess(CertifierAccess $certifierAccess): self
    {
        if (!$this->certifierAccesses->contains($certifierAccess)) {
            $this->certifierAccesses[] = $certifierAccess;
            $certifierAccess->setCertifier($this);
        }

        return $this;
    }

    public function removeCertifierAccess(CertifierAccess $certifierAccess): self
    {
        if ($this->certifierAccesses->removeElement($certifierAccess)) {
            // set the owning side to null (unless already changed)
            if ($certifierAccess->getCertifier() === $this) {
                $certifierAccess->setCertifier(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection|CertifierAccess[]
     */
    public function getPartnerAccesses(): Collection
    {
        return $this->partnerAccesses;
    }

    public function addPartnerAccess(CertifierAccess $partnerAccess): self
    {
        if (!$this->partnerAccesses->contains($partnerAccess)) {
            $this->partnerAccesses[] = $partnerAccess;
            $partnerAccess->setPartner($this);
        }

        return $this;
    }

    public function removePartnerAccess(CertifierAccess $partnerAccess): self
    {
        if ($this->partnerAccesses->removeElement($partnerAccess)) {
            // set the owning side to null (unless already changed)
            if ($partnerAccess->getPartner() === $this) {
                $partnerAccess->setPartner(null);
            }
        }

        return $this;
    }

    public function getApplicantType(): ?string
    {
        return $this->applicantType;
    }

    public function setApplicantType(?string $applicantType): self
    {
        $this->applicantType = $applicantType;

        return $this;
    }

    public function getCdcContractId(): ?string
    {
        return $this->cdcContractId;
    }

    public function setCdcContractId(?string $cdcContractId): self
    {
        $this->cdcContractId = $cdcContractId;

        return $this;
    }

    public function getCdcClientId(): ?string
    {
        return $this->cdcClientId;
    }

    public function setCdcClientId(?string $cdcClientId): self
    {
        $this->cdcClientId = $cdcClientId;

        return $this;
    }

    public function isUnlisted(): ?bool
    {
        return $this->cpfUnlisted;
    }

    public function setCpfUnlisted(bool $cpfUnlisted): self
    {
        $this->cpfUnlisted = $cpfUnlisted;
        return $this;
    }

    /**
     * @return Collection|Connection[]>
     */
    public function getConnections(): Collection
    {
        return $this->connections;
    }

    public function addConnection(Connection $connection): self
    {
        if (!$this->connections->contains($connection)) {
            $this->connections[] = $connection;
            $connection->setOrganism($this);
        }

        return $this;
    }

    public function removeConnection(Connection $connection): self
    {
        if ($this->connections->removeElement($connection)) {
            // set the owning side to null (unless already changed)
            if ($connection->getOrganism() === $this) {
                $connection->setOrganism(null);
            }
        }

        return $this;
    }

    public function getConnectionForDataProvider(DataProviders $dataProvider): ?Connection
    {
        return $this->getConnections()->filter(
            fn(Connection $connection) => $connection->getDataProvider() === $dataProvider->getValue()
        )->first() ?: null; // first renvoie false s'il n'y a pas d'élément -_-
    }

    public function getSubscription(): ?Subscription
    {
        return $this->subscription;
    }

    public function setSubscription(?Subscription $subscription): self
    {
        // set the owning side of the relation if necessary
        if ($subscription->getOrganism() !== $this) {
            $subscription->setOrganism($this);
        }

        $this->subscription = $subscription;

        return $this;
    }

    /**
     * @Serializer\VirtualProperty()
     * @Serializer\Expose()
     * @Serializer\SerializedName("registrationFolderFileTypes")
     * @Serializer\Groups({"app", "Default", "webhook"})
     */
    public function getRegistrationFolderFileTypes(RegistrationFolder $registrationFolder = null): array
    {
        $fileTypes = [];
        if ($this->registrationFolderFileTypes) {
            foreach ($this->registrationFolderFileTypes as $fileType) {
                if (!isset($fileType['allowVisibilityAttendee']) || !isset($fileType['allowUploadAttendee'])) {
                    $fileType = array_merge(self::getDefaultFileTypeRightsForAttendee(), $fileType);
                }
                $fileTypes[] = $fileType;
            }
        }
        if ($registrationFolder) {
            $certification = $registrationFolder->getCertification();
            return Tools::filterFileTypes($fileTypes, $registrationFolder->getTags(), $certification ? $certification->getCertifInfo() : null);
        } else {
            return $fileTypes;
        }
    }

    public function setRegistrationFolderFileTypes(?array $registrationFolderFileTypes): self
    {
        $this->registrationFolderFileTypes = $registrationFolderFileTypes;

        return $this;
    }

    /**
     * @param Organism $entity
     * @param array $columns
     * @return array
     * @throws Exception
     */
    public static function getCSVFormat(Organism $entity, array $columns): array
    {
        $fullRow = [
            'NAME' => $entity->getName(),
            'SIRET' => $entity->getSiret(),
            'CITY' => $entity->getCity(),
            'PHONE' => sizeof($entity->getPhones()) > 0 ? array_values($entity->getPhones())[0] : null,
            'EMAIL' => sizeof($entity->getEmails()) > 0 ? array_values($entity->getEmails())[0] : null,
            'URL' => sizeof($entity->getUrls()) > 0 ? array_values($entity->getUrls())[0] : null,
            'TRAINER' => $entity->isTrainingOrganism(),
            'CERTIFIER' => $entity->isCertifierOrganism()
        ];
        $row = [];
        foreach ($columns as $column) {
            $row[] = $fullRow[$column];
        }
        return $row;
    }

    public function getCompany(): ?Company
    {
        return $this->company;
    }

    public function setCompany(?Company $company): self
    {
        $this->company = $company;

        return $this;
    }

    public function getAccrochageDelegationDate(): ?DateTimeInterface
    {
        return $this->accrochageDelegationDate;
    }

    public function setAccrochageDelegationDate(?DateTimeInterface $accrochageDelegationDate): self
    {
        $this->accrochageDelegationDate = $accrochageDelegationDate;

        return $this;
    }

    /**
     * @param File|null $logoFile
     */
    public function setLogoFile(?File $logoFile = null): void
    {
        $this->logoFile = $logoFile;

        if (null !== $logoFile) {
            // It is required that at least one field changes if you are using doctrine
            // otherwise the event listeners won't be called and the file is lost
            $this->setLastUpdate(new DateTime());
        }
    }

    /**
     * @return File|null
     */
    public function getLogoFile(): ?File
    {
        return $this->logoFile;
    }

    /**
     * @param string|null $logoName
     * @return void
     */
    public function setLogoName(?string $logoName): void
    {
        $this->logoName = $logoName;
    }

    /**
     * @return string|null
     */
    public function getLogoName(): ?string
    {
        return $this->logoName;
    }


    /**
     * @return string|null
     */
    public function getCustomColorScheme(): ?string
    {
        return $this->customColorScheme;
    }

    /**
     * @param string|null $customColorScheme
     */
    public function setCustomColorScheme(?string $customColorScheme): void
    {
        $this->customColorScheme = $customColorScheme;
    }

    /**
     * @Serializer\VirtualProperty()
     * @Serializer\SerializedName("logo")
     * @Serializer\Expose()
     * @Serializer\Groups({"app", "Default", "public", "webhook", "attendee"})
     */
    public function getLogo(): ?string
    {
        if ($this->getLogoName()) {
            //should be dynamic based on vich_uploader.yml but how
            return "/files/publicFile/$this->logoName";
        } else {
            return null;
        }
    }

    public function getLinkedInPageUrl(): ?string
    {
        return $this->linkedInPageUrl;
    }

    public function setLinkedInPageUrl(?string $linkedInPageUrl): self
    {
        $this->linkedInPageUrl = $linkedInPageUrl;

        return $this;
    }

    public function getLinkedInOrganisationId(): ?int
    {
        return $this->linkedInOrganisationId;
    }

    public function setLinkedInOrganisationId(?int $linkedInOrganisationId): self
    {
        $this->linkedInOrganisationId = $linkedInOrganisationId;
        return $this;
    }

    public static function getDefaultFileTypeRightsForAttendee(): array
    {
        return ['allowVisibilityAttendee' => false, 'allowUploadAttendee' => false];
    }

    public function getApe(): ?string
    {
        return $this->ape;
    }

    public function setApe(?string $ape): self
    {
        $this->ape = $ape;

        return $this;
    }

    public function getReseller(): ?Organism
    {
        return $this->reseller;
    }

    public function setReseller(?Organism $reseller): self
    {
        $this->reseller = $reseller;

        return $this;
    }

    /**
     * @Serializer\VirtualProperty()
     * @Serializer\Expose
     * @Serializer\Groups({"app", "Default", "public"})
     */
    public function isReseller(): bool
    {
        return !is_null($this->getResellerMetadata());
    }

    public function getResellerMetadata(): ?array
    {
        return $this->resellerMetadata;
    }

    public function setResellerMetadata(?array $resellerMetadata): self
    {
        $this->resellerMetadata = $resellerMetadata;

        return $this;
    }

    /**
     * @Serializer\VirtualProperty()
     * @Serializer\Expose()
     * @Serializer\Groups({"app", "Default"})
     */
    public function getCertificationFileTypes(): array
    {
        return [
            [
                "id" => 1,
                "name" => "Document de partenariat",
                "accept" => ".*",
                "toState" => null,
                "description" => null,
                "allowMultiple" => true,
                "allowVisibilityPublic" => false
            ],
            [
                "id" => 2,
                "name" => "Document public",
                "accept" => ".*",
                "toState" => null,
                "description" => null,
                "allowMultiple" => true,
                "allowVisibilityPublic" => true
            ]
        ];
    }

    /**
     * @Serializer\VirtualProperty()
     * @Serializer\Expose()
     * @Serializer\Groups({"app", "Default", "public"})
     */
    public function isTrainingOrganism(): bool
    {
        return $this->trainingOrganism;
    }

    public function setTrainingOrganism(bool $trainingOrganism): self
    {
        $this->trainingOrganism = $trainingOrganism;

        return $this;
    }

    public function getSendAs(): ?array
    {
        return $this->sendAs;
    }

    public function setSendAs(?array $sendAsEmail): self
    {
        $this->sendAs = $sendAsEmail;

        return $this;
    }

    public function getSendAsName(string $email = null): ?string
    {
        $sendAsName = null;
        if (!empty($this->sendAs)) {
            if ($email) {
                $sendAsName = array_reduce($this->sendAs, function ($acc, $item) use ($email) {
                    if ($item['email'] === $email) {
                        $acc = $item['name'];
                    }
                    return $acc;
                });
            } else {
                $sendAsName = $this->sendAs[0]['name'];
            }
        }
        return $sendAsName;
    }

    public function getSendAsEmail(): ?string
    {
        return !empty($this->sendAs) ? $this->sendAs[0]['email'] : (count($this->emails) > 0 ? $this->emails[0] : null);
    }

    public function getMetadata(): ?array
    {
        return $this->metadata;
    }

    public function setMetadata(?array $metadata): self
    {
        $this->metadata = $metadata;

        return $this;
    }

    public function getQualiopiTrainingAction(): bool
    {
        return $this->qualiopiTrainingAction;
    }

    public function setQualiopiTrainingAction(bool $qualiopiTrainingAction): self
    {
        $this->qualiopiTrainingAction = $qualiopiTrainingAction;

        return $this;
    }

    public function getQualiopiBilanCompetences(): bool
    {
        return $this->qualiopiBilanCompetences;
    }

    public function setQualiopiBilanCompetences(bool $qualiopiBilanCompetences): self
    {
        $this->qualiopiBilanCompetences = $qualiopiBilanCompetences;

        return $this;
    }

    public function getQualiopiVAE(): bool
    {
        return $this->qualiopiVAE;
    }

    public function setQualiopiVAE(bool $qualiopiVAE): self
    {
        $this->qualiopiVAE = $qualiopiVAE;

        return $this;
    }

    public function getQualiopiFormationApprentissage(): bool
    {
        return $this->qualiopiFormationApprentissage;
    }

    public function setQualiopiFormationApprentissage(bool $qualiopiFormationApprentissage): self
    {
        $this->qualiopiFormationApprentissage = $qualiopiFormationApprentissage;

        return $this;
    }

    public function getUaiNumber(): ?string
    {
        return $this->uaiNumber;
    }

    public function setUaiNumber(?string $uaiNumber): self
    {
        $this->uaiNumber = $uaiNumber;

        return $this;
    }

    public function getBillingSoftware(): ?string
    {
        return $this->billingSoftware;
    }

    public function setBillingSoftware(?string $billingSoftware): self
    {
        $this->billingSoftware = $billingSoftware;

        return $this;
    }

    public function getCrm(): ?string
    {
        return $this->crm;
    }

    public function setCrm(?string $crm): self
    {
        $this->crm = $crm;

        return $this;
    }

    public function getCpfCatalogMetadata(): ?array
    {
        return $this->cpfCatalogMetadata;
    }

    public function setCpfCatalogMetadata(?array $cpfCatalogMetadata): self
    {
        $this->cpfCatalogMetadata = $cpfCatalogMetadata;

        return $this;
    }

    public function getCpfCatalogUploadReport(): ?File
    {
        return $this->cpfCatalogUploadReport;
    }

    public function setCpfCatalogUploadReport(?File $cpfCatalogUploadReport): void
    {
        $this->cpfCatalogUploadReport = $cpfCatalogUploadReport;
        if (null !== $cpfCatalogUploadReport) {
            // It is required that at least one field changes if you are using doctrine
            // otherwise the event listeners won't be called and the file is lost
            $this->setLastUpdate(new DateTime());
        }
    }

    public function getCpfCatalogUploadReportName(): ?string
    {
        return $this->cpfCatalogUploadReportName;
    }

    public function setCpfCatalogUploadReportName(?string $cpfCatalogUploadReportName): void
    {
        $this->cpfCatalogUploadReportName = $cpfCatalogUploadReportName;
    }

    public function isAllowImportCertificationFolders(): bool
    {
        return $this->allowImportCertificationFolders;
    }

    public function setAllowImportCertificationFolders(bool $allowImportCertificationFolders): self
    {
        $this->allowImportCertificationFolders = $allowImportCertificationFolders;

        return $this;
    }
}
