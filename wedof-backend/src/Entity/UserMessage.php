<?php

namespace App\Entity;

use App\Repository\UserMessageRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;

/**
 * @ORM\Entity(repositoryClass=UserMessageRepository::class)
 */
class UserMessage
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Serializer\Exclude()
     */
    private int $id;

    /**
     * @ORM\Column(type="text")
     */
    private string $message;

    /**
     * @ORM\Column(type="string", length=10)
     */
    private string $type;

    /**
     * @ORM\Column(type="boolean", nullable=true)
     */
    private ?bool $dismissed = null;

    /**
     * @ORM\Column(type="boolean", options={"default" : false})
     */
    private bool $showIcon = false;

    /**
     * @ORM\Column(type="boolean", options={"default" : false})
     * @Serializer\Exclude()
     */
    private bool $visible = false;

    /**
     * @ORM\ManyToMany(targetEntity=User::class, mappedBy="messages")
     * @Serializer\Exclude()
     */
    private Collection $users;

    public function __construct()
    {
        $this->users = new ArrayCollection();
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getMessage(): string
    {
        return $this->message;
    }

    public function setMessage(string $message): self
    {
        $this->message = $message;

        return $this;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function isDismissed(): ?bool
    {
        return $this->dismissed;
    }

    public function setDismissed(?bool $dismissed): self
    {
        $this->dismissed = $dismissed;

        return $this;
    }

    public function isShowIcon(): bool
    {
        return $this->showIcon;
    }

    public function setShowIcon(bool $showIcon): self
    {
        $this->showIcon = $showIcon;

        return $this;
    }

    public function isVisible(): bool
    {
        return $this->visible;
    }

    public function setVisible(bool $visible): UserMessage
    {
        $this->visible = $visible;

        return $this;
    }

    /**
     * @return Collection<int, User>
     */
    public function getUsers(): Collection
    {
        return $this->users;
    }

    public function addUser(User $user): self
    {
        if (!$this->users->contains($user)) {
            $this->users[] = $user;
            $user->addMessage($this);
        }

        return $this;
    }

    public function removeUser(User $user): self
    {
        if ($this->users->removeElement($user)) {
            $user->removeMessage($this);
        }

        return $this;
    }
}
