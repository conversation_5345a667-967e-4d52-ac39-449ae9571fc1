<?php

namespace App\Entity;

use App\Entity\Traits\TimestampableTrait;
use App\Repository\UserRepository;
use DateTimeInterface;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Hateoas\Configuration\Annotation as Hateoas;
use JMS\Serializer\Annotation as Serializer;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;
use Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Component\Validator\Constraints as Assert;

/**
 * @ORM\Entity(repositoryClass=UserRepository::class)
 * @ORM\Table(indexes={
 *     @ORM\Index(name="email_idx",columns={"email"})
 * })
 * @ORM\HasLifecycleCallbacks()
 * @Serializer\ExclusionPolicy("ALL")
 * @UniqueEntity(fields={"email"}, message="There is already an account with this email")
 * @Hateoas\Relation("self", href = "expr('/api/users/' ~ object.getEmail())", exclusion=@Hateoas\Exclusion(groups={"owner", "Default", "company"}))
 * @Hateoas\Relation("mainOrganism", href = "expr('/api/organisms/' ~ object.getMainOrganism().getSiret())", attributes={"name" = "expr(object.getMainOrganism().getName())", "siret" = "expr(object.getMainOrganism().getSiret())"}, exclusion=@Hateoas\Exclusion(excludeIf = "expr(object.getMainOrganism() === null)", groups={"owner", "Default", "webhook", "company"}))
 */
class User implements UserInterface, PasswordAuthenticatedUserInterface
{
    use TimestampableTrait;

    /**
     * @ORM\Id()
     * @ORM\GeneratedValue()
     * @ORM\Column(type="integer")
     */
    private int $id;

    /**
     * @ORM\Column(type="string", length=180, unique=true)
     * @Assert\NotNull(groups={"create"})
     * @Assert\NotBlank(allowNull="true", groups={"create", "update"})
     * @Assert\Email(groups={"create", "update"})
     * @Serializer\Expose
     * @Serializer\Groups({"create", "Default", "update", "webhook", "company"})
     */
    private string $email;

    /**
     * @ORM\Column(type="json")
     * @Serializer\Type("array")
     * @Serializer\Expose
     * @Serializer\Groups({"create", "update", "Default", "company"})
     */
    private array $roles = [];

    /**
     * @var string|null The hashed password
     * @ORM\Column(type="string")
     * @Assert\NotBlank(allowNull="true", groups={"create", "update"})
     * @Serializer\Expose
     * @Serializer\Groups({"create", "update"})
     */
    private ?string $password = null;

    /**
     * @ORM\ManyToMany(targetEntity=Organism::class)
     * @ORM\JoinColumn(nullable=true)
     */
    private Collection $secondaryOrganisms;

    /**
     * @ORM\Column(type="string", length=255)
     * @Assert\NotNull(groups={"create"})
     * @Assert\NotBlank(allowNull="true", groups={"create", "update"})
     * @Serializer\Expose
     * @Serializer\Groups({"create", "Default", "update", "webhook", "company"})
     */
    private ?string $firstName = null;

    /**
     * @ORM\Column(type="string", length=255)
     * @Assert\NotNull(groups={"create"})
     * @Assert\NotBlank(allowNull="true", groups={"create", "update"})
     * @Serializer\Expose
     * @Serializer\Groups({"create", "Default", "update", "webhook", "company"})
     */
    private ?string $lastName = null;

    /**
     * @ORM\Column(type="string", length=50, nullable=true)
     * @Serializer\Expose
     * @Serializer\Groups({"create", "Default", "update", "webhook", "company"})
     */
    private ?string $phoneNumber = null;

    /**
     * @ORM\Column(type="text", nullable=true)
     * @Serializer\Expose
     * @Serializer\Groups({"create", "Default", "update", "company"})
     */
    private ?string $address = null;

    /**
     * @ORM\ManyToOne(targetEntity=Organism::class, inversedBy="mainUsers")
     * @Serializer\Groups({"update", "Default", "company"})
     * @ORM\JoinColumn(nullable=true, onDelete="SET NULL")
     */
    private ?Organism $mainOrganism = null;

    /**
     * @ORM\Column(type="boolean")
     */
    private bool $isVerified = false;

    /**
     * @ORM\OneToOne(targetEntity=Organism::class, mappedBy="ownedBy", cascade={"persist"})
     */
    private ?Organism $ownedOrganism = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "company"})
     */
    private ?DateTimeInterface $rgpdMsa = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     */
    private ?DateTimeInterface $lastLogin = null;

    /**
     * @ORM\OneToMany(targetEntity=ApiToken::class, mappedBy="user", cascade={"remove"})
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    private Collection $apiTokens;

    /**
     * @ORM\ManyToMany(targetEntity=UserMessage::class, inversedBy="users")
     */
    private Collection $messages;

    /**
     * @ORM\ManyToOne(targetEntity=Company::class, inversedBy="users")
     */
    private ?Company $company = null;

    /**
     * @ORM\Column(type="json", nullable=true)
     */
    private ?array $filters = [];

    /**
     * @return bool
     * @Serializer\VirtualProperty()
     * @Serializer\Groups({"Default", "company"})
     */
    public function isOwner(): bool
    {
        return (bool)$this->getOwnedOrganism();
    }

    /**
     * @Serializer\VirtualProperty()
     * @Serializer\Groups({"Default", "company"})
     */
    public function getName(): string
    {
        return $this->getFirstName() . " " . $this->getLastName();
    }

    public function __construct()
    {
        $this->secondaryOrganisms = new ArrayCollection();
        $this->apiTokens = new ArrayCollection();
        $this->messages = new ArrayCollection();
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getEmail(): string
    {
        return $this->email;
    }

    public function setEmail(string $email): self
    {
        $this->email = $email;

        return $this;
    }

    /**
     * @see UserInterface
     */
    public function getRoles(): array
    {
        $roles = $this->roles;
        // guarantee every user at least has ROLE_USER
        //$roles[] = 'ROLE_USER';
        return array_unique($roles);
    }

    public function setRoles(array $roles): self
    {
        $this->roles = $roles;
        return $this;
    }

    /**
     * @see UserInterface
     */
    public function getPassword(): ?string
    {
        return $this->password;
    }

    public function setPassword(string $password): self
    {
        $this->password = $password;

        return $this;
    }

    /**
     * @see UserInterface
     */
    public function getSalt(): ?string
    {
        // not needed when using the "bcrypt" algorithm in security.yaml
        return null;
    }

    /**
     * @see UserInterface
     */
    public function eraseCredentials()
    {
        // If you store any temporary, sensitive data on the user, clear it here
        // $this->plainPassword = null;
    }

    public function getSirets(): ?ArrayCollection
    {
        $sirets = new ArrayCollection();

        foreach ($this->getOrganisms() as $organism) {
            $sirets[] = $organism->getSiret();
        }

        return $sirets;
    }

    /**
     * @return Collection|Organism[]
     */
    public function getSecondaryOrganisms(): ?Collection
    {
        return $this->secondaryOrganisms;
    }

    public function addSecondaryOrganism(Organism $organism): self
    {
        if (!$this->secondaryOrganisms->contains($organism)) {
            $this->secondaryOrganisms[] = $organism;
        }

        return $this;
    }

    public function removeSecondaryOrganism(Organism $organism): self
    {
        if ($this->secondaryOrganisms->contains($organism)) {
            $this->secondaryOrganisms->removeElement($organism);
        }

        return $this;
    }

    public function removeAllSecondaryOrganisms(): self
    {
        foreach ($this->getSecondaryOrganisms() as $secondaryOrganism) {
            $this->removeSecondaryOrganism($secondaryOrganism);
        }

        return $this;
    }

    public function getFirstName(): ?string
    {
        return $this->firstName;
    }

    public function setFirstName(string $firstName): self
    {
        $this->firstName = $firstName;

        return $this;
    }

    public function getLastName(): ?string
    {
        return $this->lastName;
    }

    public function setLastName(string $lastName): self
    {
        $this->lastName = $lastName;

        return $this;
    }

    public function getPhoneNumber(): ?string
    {
        return $this->phoneNumber;
    }

    public function setPhoneNumber(?string $phoneNumber): self
    {
        $this->phoneNumber = $phoneNumber;

        return $this;
    }

    public function getAddress(): ?string
    {
        return $this->address;
    }

    public function setAddress(?string $address): self
    {
        $this->address = $address;

        return $this;
    }

    public function getMainOrganism(): ?Organism
    {
        return $this->mainOrganism;
    }

    public function setMainOrganism(?Organism $organism): self
    {
        $this->mainOrganism = $organism;

        return $this;
    }

    public function getOrganisms(): ?ArrayCollection
    {
        $allOrganisms = new ArrayCollection();
        if ($this->getMainOrganism()) {
            $allOrganisms->add($this->getMainOrganism());
        }
        if ($this->getSecondaryOrganisms()) {
            $allOrganisms = new ArrayCollection(array_merge($allOrganisms->toArray(), $this->getSecondaryOrganisms()->toArray()));
        }
        return $allOrganisms;
    }

    public function setIsVerified(bool $isVerified): self
    {
        $this->isVerified = $isVerified;

        return $this;
    }

    public function getIsVerified(): bool
    {
        return $this->isVerified;
    }

    public function getOwnedOrganism(): ?Organism
    {
        return $this->ownedOrganism;
    }

    public function setOwnedOrganism(?Organism $ownedOrganism): self
    {
        $this->ownedOrganism = $ownedOrganism;

        // set (or unset) the owning side of the relation if necessary
        $newAuthorizedBy = null === $ownedOrganism ? null : $this;
        if ($ownedOrganism->getOwnedBy() !== $newAuthorizedBy) {
            $ownedOrganism->setOwnedBy($newAuthorizedBy);
        }

        return $this;
    }

    public function getRgpdMsa(): ?DateTimeInterface
    {
        return $this->rgpdMsa;
    }

    public function setRgpdMsa(?DateTimeInterface $rgpdMsa): self
    {
        $this->rgpdMsa = $rgpdMsa;

        return $this;
    }

    /**
     * @return Collection|ApiToken[]
     */
    public function getApiTokens(): Collection
    {
        return $this->apiTokens;
    }

    public function addApiToken(ApiToken $apiToken): self
    {
        if (!$this->apiTokens->contains($apiToken)) {
            $this->apiTokens[] = $apiToken;
            $apiToken->setUser($this);
        }

        return $this;
    }

    public function removeApiToken(ApiToken $apiToken): self
    {
        if ($this->apiTokens->removeElement($apiToken)) {
            // set the owning side to null (unless already changed)
            if ($apiToken->getUser() === $this) {
                $apiToken->setUser(null);
            }
        }

        return $this;
    }

    /**
     * @return string
     */
    public function getUsername(): string
    {
        return $this->getUserIdentifier();
    }

    public function getUserIdentifier(): string
    {
        return $this->getEmail();
    }

    /**
     * @return Collection<int, UserMessage>
     */
    public function getMessages(): Collection
    {
        return $this->messages;
    }

    public function addMessage(UserMessage $message): self
    {
        if (!$this->messages->contains($message)) {
            $this->messages[] = $message;
        }

        return $this;
    }

    public function removeMessage(UserMessage $message): self
    {
        $this->messages->removeElement($message);

        return $this;
    }

    public function getCompany(): ?Company
    {
        return $this->company;
    }

    public function setCompany(?Company $company): self
    {
        $this->company = $company;

        return $this;
    }

    /**
     * @return DateTimeInterface|null
     */
    public function getLastLogin(): ?DateTimeInterface
    {
        return $this->lastLogin;
    }

    /**
     * @param DateTimeInterface|null $lastLogin
     */
    public function setLastLogin(?DateTimeInterface $lastLogin): void
    {
        $this->lastLogin = $lastLogin;
    }

    public function getFilters(): ?array
    {
        return $this->filters;
    }

    public function setFilters(?array $filters): self
    {
        $this->filters = $filters;

        return $this;
    }

}
