<?php

namespace App\Entity;

use App\Library\utils\enums\DataProviders;
use App\Repository\ConnectionRepository;
use App\Service\DataProviders\BaseApiService;
use DateTimeInterface;
use Doctrine\ORM\Mapping as ORM;
use Firebase\JWT\JWT;
use Hateoas\Configuration\Annotation as Hateoas;
use <PERSON>MS\Serializer\Annotation as Serializer;

/**
 * @ORM\Entity(repositoryClass=ConnectionRepository::class)
 * pas de relation self, pas de sens à le faire pour l'instant
 * @Hateoas\Relation("organism", href = "expr('/api/organisms/' ~ object.getOrganism().getSiret())", attributes={"name" = "expr(object.getOrganism().getName())", "siret" = "expr(object.getOrganism().getSiret())"})
 *
 * @Serializer\ExclusionPolicy("ALL")
 */
class Connection
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Serializer\Expose()
     */
    private int $id;

    /**
     * @ORM\Column(type="string", length=255)
     * @Serializer\Expose()
     */
    private string $dataProvider;

    /**
     * @ORM\ManyToOne(targetEntity=Organism::class, inversedBy="connections")
     * @ORM\JoinColumn(nullable=false)
     */
    private Organism $organism;

    /**
     * @ORM\Column(type="text", nullable=true)
     */
    private ?string $encryptedCredentials = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Expose()
     */
    private ?DateTimeInterface $failedAt = null;

    /**
     * @ORM\Column(type="boolean")
     */
    private bool $isTosValidated = false;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     */
    private ?DateTimeInterface $refreshAt = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     */
    private ?DateTimeInterface $expiresOn;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     */
    private ?DateTimeInterface $lastRefresh;

    /**
     * @ORM\Column(type="integer")
     */
    private ?int $authenticationFailedCount = 0;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Expose()
     */
    private ?string $message = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Expose()
     */
    private ?string $messageType = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Expose()
     */
    private ?string $state = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Expose()
     */
    private ?string $type = null;

    /**
     * @ORM\Column(type="boolean", options={"default" : false})
     * @Serializer\Expose()
     */
    private bool $isInitialized = false;

    /**
     * @ORM\Column(type="boolean", options={"default" : false})
     * @Serializer\Expose()
     */
    private bool $existAtDataProvider = false;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getDataProvider(): ?string
    {
        return $this->dataProvider;
    }

    public function setDataProvider(string $dataProvider): self
    {
        $this->dataProvider = $dataProvider;

        return $this;
    }

    public function getOrganism(): ?Organism
    {
        return $this->organism;
    }

    public function setOrganism(?Organism $organism): self
    {
        $this->organism = $organism;

        return $this;
    }

    private function getEncryptedCredentials(): ?string
    {
        return $this->encryptedCredentials;
    }

    private function setEncryptedCredentials(?string $jwt): self
    {
        $this->encryptedCredentials = $jwt;

        return $this;
    }

    public function getFailedAt(): ?DateTimeInterface
    {
        return $this->failedAt;
    }

    public function setFailedAt(?DateTimeInterface $failedAt): self
    {
        $this->failedAt = $failedAt;

        return $this;
    }

    public function isIsTosValidated(): ?bool
    {
        return $this->isTosValidated;
    }

    public function setIsTosValidated(bool $isTosValidated): self
    {
        $this->isTosValidated = $isTosValidated;

        return $this;
    }

    public function getRefreshAt(): ?DateTimeInterface
    {
        return $this->refreshAt;
    }

    public function setRefreshAt(?DateTimeInterface $refreshAt): self
    {
        $this->refreshAt = $refreshAt;

        return $this;
    }

    public function getCredentials(): ?array
    {
        return !empty($this->getEncryptedCredentials()) ?
            (array)JWT::decode($this->getEncryptedCredentials(), $_ENV['JWT_PASSPHRASE'], array('HS256')) :
            null;
    }

    public function setCredentials(array $credentials = null): self
    {
        return $this->setEncryptedCredentials($credentials ? JWT::encode($credentials, $_ENV["JWT_PASSPHRASE"]) : null);
    }

    /**
     * @return string
     * @Serializer\VirtualProperty()
     */
    public function username(): string
    {
        return BaseApiService::getApiServiceByDataProvider(DataProviders::from($this->dataProvider))->getUsername($this);
    }

    /**
     * @return DateTimeInterface|null
     */
    public function getExpiresOn(): ?DateTimeInterface
    {
        return $this->expiresOn;
    }

    /**
     * @param DateTimeInterface|null $expiresOn
     * @return $this
     */
    public function setExpiresOn(?DateTimeInterface $expiresOn): self
    {
        $this->expiresOn = $expiresOn;

        return $this;
    }

    /**
     * @return DateTimeInterface|null
     */
    public function getLastRefresh(): ?DateTimeInterface
    {
        return $this->lastRefresh;
    }

    public function setLastRefresh(?DateTimeInterface $lastRefresh): self
    {
        $this->lastRefresh = $lastRefresh;

        return $this;
    }

    public function getAuthenticationFailedCount(): ?int
    {
        return $this->authenticationFailedCount;
    }

    public function setAuthenticationFailedCount(int $authenticationFailedCount): self
    {
        $this->authenticationFailedCount = $authenticationFailedCount;

        return $this;
    }

    public function getMessage(): ?string
    {
        return $this->message;
    }

    public function setMessage(?string $message): self
    {
        $this->message = $message;

        return $this;
    }

    public function getMessageType(): ?string
    {
        return $this->messageType;
    }

    public function setMessageType(?string $messageType): self
    {
        $this->messageType = $messageType;

        return $this;
    }

    public function getState(): ?string
    {
        return $this->state;
    }

    public function setState(?string $state): self
    {
        $this->state = $state;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(?string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getExistAtDataProvider(): bool
    {
        return $this->existAtDataProvider;
    }

    public function setExistAtDataProvider(bool $existAtProvider): self
    {
        $this->existAtDataProvider = $existAtProvider;

        return $this;
    }

    public function isInitialized(): bool
    {
        return $this->isInitialized;
    }

    public function setIsInitialized(bool $isInitialized): self
    {
        $this->isInitialized = $isInitialized;

        return $this;
    }
}
