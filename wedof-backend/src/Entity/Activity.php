<?php

namespace App\Entity;

use App\Entity\Traits\TimestampableTrait;
use App\Library\utils\enums\ActivityTypes;
use App\Library\utils\enums\QualiopiIndicators;
use App\Repository\ActivityRepository;
use DateTimeInterface;
use Doctrine\ORM\Mapping as ORM;
use Hateoas\Configuration\Annotation as Hateoas;
use J<PERSON>\Serializer\Annotation as Serializer;

/**
 * @implements EntityExportableCSV<Activity>
 * @ORM\Entity(repositoryClass=ActivityRepository::class)
 * @ORM\Table(indexes={@ORM\Index(name="entity",columns={"entity_class", "entity_id"})})
 * @ORM\HasLifecycleCallbacks()
 *
 * @Serializer\ExclusionPolicy("ALL")
 * @Hateoas\Relation("user", href = "expr('/api/users/' ~ object.getUser().getEmail())", attributes={"email" = "expr(object.getUser().getEmail())", "name" = "expr(object.getUser().getName())"}, exclusion=@Hateoas\Exclusion(excludeIf = "expr(object.getUser() === null)"))
 */
class Activity implements EntityExportableCSV
{
    use TimestampableTrait;

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Serializer\Expose
     */
    private int $id;

    /**
     * @ORM\Column(type="string", length=255)
     * @Serializer\Expose
     */
    private string $type;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Expose
     */
    private ?DateTimeInterface $eventTime = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Expose
     */
    private ?DateTimeInterface $eventEndTime = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Expose
     */
    private ?string $origin = null;

    /**
     * @ORM\Column(type="text", nullable=true)
     * @Serializer\Expose
     */
    private ?string $link = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Expose
     */
    private ?string $title = null;

    /**
     * @ORM\Column(type="text", nullable=true)
     * @Serializer\Expose
     */
    private ?string $description = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Expose
     */
    private ?string $field = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Expose
     */
    private ?string $previousValue = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Expose
     */
    private ?string $newValue = null;

    /**
     * @ORM\Column(type="string", length=255)
     * @Serializer\Expose
     */
    private string $entityClass;

    /**
     * @ORM\Column(type="string", length=255)
     * @Serializer\Expose
     */
    private string $entityId;

    /**
     * @ORM\ManyToOne(targetEntity=User::class)
     * @ORM\JoinColumn(nullable=true)
     */
    private ?User $user = null;

    /**
     * @ORM\Column(type="json", nullable=true)
     * @Serializer\Type("array")
     * @Serializer\Expose
     */
    private ?array $qualiopiIndicators = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Expose
     */
    private ?DateTimeInterface $dueDate = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getEventTime(): ?DateTimeInterface
    {
        return $this->eventTime;
    }

    public function setEventTime(?DateTimeInterface $eventTime): self
    {
        $this->eventTime = $eventTime;

        return $this;
    }

    public function getEventEndTime(): ?DateTimeInterface
    {
        return $this->eventEndTime;
    }

    public function setEventEndTime(?DateTimeInterface $eventEndTime): self
    {
        $this->eventEndTime = $eventEndTime;

        return $this;
    }

    public function getOrigin(): ?string
    {
        return $this->origin;
    }

    public function setOrigin(?string $origin): self
    {
        $this->origin = $origin;

        return $this;
    }

    public function getLink(): ?string
    {
        return $this->link;
    }

    public function setLink(?string $link): self
    {
        $this->link = $link;

        return $this;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(?string $title): self
    {
        $this->title = $title;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): self
    {
        $this->description = $description;

        return $this;
    }

    public function getField(): ?string
    {
        return $this->field;
    }

    public function setField(?string $field): self
    {
        $this->field = $field;

        return $this;
    }

    public function getPreviousValue(): ?string
    {
        return $this->previousValue;
    }

    public function setPreviousValue(?string $previousValue): self
    {
        $this->description = $previousValue;

        return $this;
    }

    public function getNewValue(): ?string
    {
        return $this->newValue;
    }

    public function setNewValue(?string $newValue): self
    {
        $this->newValue = $newValue;

        return $this;
    }

    public function getEntityClass(): ?string
    {
        return $this->entityClass;
    }

    public function setEntityClass(string $entityClass): self
    {
        $this->entityClass = $entityClass;

        return $this;
    }

    public function getEntityId(): ?string
    {
        return $this->entityId;
    }

    public function setEntityId(string $entityId): self
    {
        $this->entityId = $entityId;

        return $this;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): self
    {
        $this->user = $user;
        return $this;
    }

    public function getQualiopiIndicators(): ?array
    {
        return $this->qualiopiIndicators;
    }

    public function setQualiopiIndicators(?array $qualiopiIndicators): self
    {
        $this->qualiopiIndicators = $qualiopiIndicators;

        return $this;
    }

    public function getDueDate(): ?DateTimeInterface
    {
        return $this->dueDate;
    }

    public function setDueDate(?DateTimeInterface $dueDate): self
    {
        $this->dueDate = $dueDate;

        return $this;
    }

    public function isDone(): ?bool
    {
        return $this->done;
    }

    public function setDone(bool $done): self
    {
        $this->done = $done;

        return $this;
    }

    /**
     * @param Activity $entity
     * @param array $columns
     * @return array
     */
    public static function getCSVFormat($entity, array $columns): array
    {
        $fullRow = [
            'TITRE' => $entity->getTitle(),
            'DESCRIPTION' => $entity->getDescription(),
            'TYPE' => ActivityTypes::toFrString($entity->getType()),
            'LIEN' => $entity->getLink(),
            'CHAMP_MODIFIÉ' => $entity->getField(),
            'ANCIENNE_VALEUR' => $entity->getPreviousValue(),
            'NOUVELLE_VALEUR' => $entity->getNewValue(),
            'DATE_DEBUT' => $entity->getEventTime() ? $entity->getEventTime()->format('d/m/y à H\hi') : null,
            'DATE_FIN' => $entity->getEventEndTime() ? $entity->getEventEndTime()->format('d/m/y à H\hi') : null,
            'DUREE (JOUR)' => $entity->getEventTime() && $entity->getEventEndTime() && $entity->getUser() ? $entity->getEventTime()->diff($entity->getEventEndTime())->days : null,
            'INDICATEURS_QUALIOPI' => $entity->getQualiopiIndicators() ? implode(',', $entity->getQualiopiIndicators()) : null,
            'UTILISATEUR' => $entity->getUser() ? $entity->getUser()->getName() : $entity->getOrigin(),
            'DATE_ÉCHEANCE' => $entity->getDueDate() ? $entity->getDueDate()->format('d/m/y à H\hi') : null
        ];
        $row = [];
        foreach ($columns as $column) {
            $row[] = $fullRow[$column];
        }
        return $row;
    }
}