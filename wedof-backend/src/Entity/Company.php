<?php

namespace App\Entity;

use App\Repository\CompanyRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass=CompanyRepository::class)
 * @ORM\Table(indexes={@ORM\Index(name="siren_idx",columns={"siren"})})
 */
class Company
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private int $id;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private string $name;

    /**
     * @ORM\Column(type="string", length=20, unique=true)
     */
    private string $siren;

    /**
     * @ORM\Column(type="float", nullable=true)
     */
    private ?float $vat = null;

    /**
     * @ORM\OneToMany(targetEntity=Organism::class, mappedBy="company")
     */
    private Collection $organisms;

    /**
     * @ORM\OneToOne(targetEntity=Organism::class, cascade={"persist", "remove"})
     * @ORM\JoinColumn(nullable=false)
     */
    private Organism $headquarter;

    /**
     * @ORM\OneToOne(targetEntity=Subscription::class, inversedBy="company", cascade={"persist", "remove"})
     */
    private ?Subscription $Subscription = null;

    /**
     * @ORM\OneToMany(targetEntity=User::class, mappedBy="company")
     */
    private Collection $users;

    /**
     * @ORM\OneToMany(targetEntity=Application::class, mappedBy="company")
     */
    private Collection $applications;

    public function __construct()
    {
        $this->organisms = new ArrayCollection();
        $this->users = new ArrayCollection();
        $this->applications = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getSiren(): ?string
    {
        return $this->siren;
    }

    public function setSiren(string $siren): self
    {
        $this->siren = $siren;

        return $this;
    }

    public function getVat(): ?float
    {
        return $this->vat;
    }

    public function setVat(?float $vat): self
    {
        $this->vat = $vat;

        return $this;
    }

    /**
     * @return Collection<int, Organism>
     */
    public function getOrganisms(): Collection
    {
        return $this->organisms;
    }

    public function addOrganism(Organism $organism): self
    {
        if (!$this->organisms->contains($organism)) {
            $this->organisms[] = $organism;
            $organism->setCompany($this);
        }

        return $this;
    }

    public function removeOrganism(Organism $organism): self
    {
        if ($this->organisms->removeElement($organism)) {
            // set the owning side to null (unless already changed)
            if ($organism->getCompany() === $this) {
                $organism->setCompany(null);
            }
        }

        return $this;
    }

    public function getHeadquarter(): ?Organism
    {
        return $this->headquarter;
    }

    public function setHeadquarter(Organism $headquarter): self
    {
        $this->headquarter = $headquarter;

        return $this;
    }

    public function getSubscription(): ?Subscription
    {
        return $this->Subscription;
    }

    public function setSubscription(?Subscription $Subscription): self
    {
        $this->Subscription = $Subscription;

        return $this;
    }

    /**
     * @return Collection<int, User>
     */
    public function getUsers(): Collection
    {
        return $this->users;
    }

    public function addUser(User $user): self
    {
        if (!$this->users->contains($user)) {
            $this->users[] = $user;
            $user->setCompany($this);
        }

        return $this;
    }

    public function removeUser(User $user): self
    {
        if ($this->users->removeElement($user)) {
            // set the owning side to null (unless already changed)
            if ($user->getCompany() === $this) {
                $user->setCompany(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, Application>
     */
    public function getApplications(): Collection
    {
        return $this->applications;
    }

    public function addApplication(Application $application): self
    {
        if (!$this->applications->contains($application)) {
            $this->applications[] = $application;
            $application->setCompany($this);
        }

        return $this;
    }

    public function removeApplication(Application $application): self
    {
        if ($this->applications->removeElement($application)) {
            // set the owning side to null (unless already changed)
            if ($application->getCompany() === $this) {
                $application->setCompany(null);
            }
        }

        return $this;
    }
}
