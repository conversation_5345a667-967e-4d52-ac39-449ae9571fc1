<?php

namespace App\Entity;

use App\Library\utils\Tools;
use App\Repository\CertificationPartnerAuditRepository;
use DateTimeInterface;
use Doctrine\ORM\Mapping as ORM;
use Hateoas\Configuration\Annotation as Hateoas;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;

/**
 * @ORM\Entity(repositoryClass=CertificationPartnerAuditRepository::class)
 *
 * @Hateoas\Relation("self", href = "expr('/api/certifications/' ~ object.getCertificationPartner().getCertification().getCertifInfo() ~ '/partners/' ~ object.getCertificationPartner().getPartner().getSiret() ~ '/audits/' ~ object.getId())", exclusion=@Hateoas\Exclusion(excludeIf = "expr(object.getId() === null)"))
 * @Hateoas\Relation("complete", href = "expr('/api/certifications/' ~ object.getCertificationPartner().getCertification().getCertifInfo() ~ '/partners/' ~ object.getCertificationPartner().getPartner().getSiret() ~ '/audits/' ~ object.getId() ~ '/complete')", exclusion=@Hateoas\Exclusion(excludeIf = "expr(object.getId() === null)"))
 * @Hateoas\Relation("certificationPartnerAuditTemplate", href = "expr('/api/certificationPartnerAuditTemplates/' ~ object.getTemplate().getId())", attributes={"name" = "expr(object.getTemplate().getName())"}, exclusion=@Hateoas\Exclusion(excludeIf = "expr(object.getId() === null)"))
 * @Hateoas\Relation("certification", href = "expr('/api/certifications/' ~ object.getCertificationPartner().getCertification().getCertifInfo())", attributes={"name" = "expr(object.getCertificationPartner().getCertification().getName())", "certifInfo" = "expr(object.getCertificationPartner().getCertification().getCertifInfo())", "externalId" = "expr(object.getCertificationPartner().getCertification().getExternalId())", "id" = "expr(object.getCertificationPartner().getCertification().getId())", "enabled" = "expr(object.getCertificationPartner().getCertification().getEnabled())"}, exclusion=@Hateoas\Exclusion(excludeIf = "expr(object.getId() === null)"))
 * @Hateoas\Relation("partner", href = "expr('/api/organisms/' ~ object.getCertificationPartner().getPartner().getSiret())", attributes={"name" = "expr(object.getCertificationPartner().getPartner().getName())", "siret" = "expr(object.getCertificationPartner().getPartner().getSiret())"}, exclusion=@Hateoas\Exclusion(excludeIf = "expr(object.getCertificationPartner().getPartner() === null)"))
 * @Hateoas\Relation("certifier", href = "expr('/api/organisms/' ~ object.getCertificationPartner().getCertifier().getSiret())", attributes={"name" = "expr(object.getCertificationPartner().getCertifier().getName())", "siret" = "expr(object.getCertificationPartner().getCertifier().getSiret())"}, exclusion=@Hateoas\Exclusion(excludeIf = "expr(object.getCertificationPartner().getCertifier() === null)"))
 * @Serializer\ExclusionPolicy("ALL")
 */
class CertificationPartnerAudit
{

    public const CLASSNAME = 'CertificationPartnerAudit';

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Serializer\Expose
     */
    private int $id;

    /**
     * @ORM\ManyToOne(targetEntity=CertificationPartner::class)
     * @ORM\JoinColumn(nullable=false)
     */
    private CertificationPartner $certificationPartner;

    /**
     * @ORM\ManyToOne(targetEntity=CertificationPartnerAuditTemplate::class)
     * @ORM\JoinColumn(nullable=false)
     */
    private CertificationPartnerAuditTemplate $template;

    /**
     * @ORM\Column(type="string", length=255)
     * @Serializer\Expose
     */
    private string $state;

    /**
     * @ORM\Column(type="json", nullable=true)
     * @Serializer\Expose
     */
    private ?array $evaluatedCriterias = [];

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Expose
     */
    private ?string $result = null;

    /**
     * @ORM\Column(type="datetime")
     * @Serializer\Expose
     */
    private DateTimeInterface $startDate;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Expose
     */
    private ?DateTimeInterface $endDate = null;

    /**
     * @ORM\Column(type="text", nullable=true)
     * @Serializer\Expose
     */
    private ?string $notes = null;

    /**
     * @ORM\OneToOne(targetEntity=CertificationPartnerAuditFile::class, inversedBy="certificationPartnerAudit")
     * @Serializer\Expose
     * TODO(audit): don't expose the whole report ?
     */
    private ?CertificationPartnerAuditFile $report = null;

    /**
     * If length changes, then update code in usages of setErrorMessage(...)
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Expose
     */
    private ?string $errorMessage = null;


    public function getId(): ?int
    {
        return $this->id;
    }

    public function getCertificationPartner(): CertificationPartner
    {
        return $this->certificationPartner;
    }

    public function setCertificationPartner(CertificationPartner $certificationPartner): self
    {
        $this->certificationPartner = $certificationPartner;
        return $this;
    }

    public function getTemplate(): CertificationPartnerAuditTemplate
    {
        return $this->template;
    }

    public function setTemplate(CertificationPartnerAuditTemplate $template): self
    {
        $this->template = $template;

        return $this;
    }

    public function getState(): ?string
    {
        return $this->state;
    }

    public function setState(string $state): self
    {
        $this->state = $state;

        return $this;
    }

    public function getEvaluatedCriterias(): ?array
    {
        return $this->evaluatedCriterias;
    }

    public function setEvaluatedCriterias(?array $evaluatedCriterias): self
    {
        $this->evaluatedCriterias = $evaluatedCriterias;
        return $this;
    }

    public function getResult(): ?string
    {
        return $this->result;
    }

    public function setResult(?string $result): self
    {
        $this->result = $result;

        return $this;
    }

    public function getStartDate(): ?\DateTimeInterface
    {
        return $this->startDate;
    }

    public function setStartDate(\DateTimeInterface $startDate): self
    {
        $this->startDate = $startDate;

        return $this;
    }

    public function getEndDate(): ?\DateTimeInterface
    {
        return $this->endDate;
    }

    public function setEndDate(?\DateTimeInterface $endDate): self
    {
        $this->endDate = $endDate;
        return $this;
    }

    public function getNotes(): ?string
    {
        return $this->notes;
    }

    public function setNotes(?string $notes): self
    {
        $this->notes = $notes;

        return $this;
    }

    public function getReport(): ?CertificationPartnerAuditFile
    {
        return $this->report;
    }

    public function setReport(?CertificationPartnerAuditFile $report): self
    {
        $this->report = $report;

        return $this;
    }

    /**
     * @Serializer\VirtualProperty()
     * @Serializer\SerializedName("partnerLink")
     * @Serializer\Expose()
     */
    public function getPartnerLink(): string
    {
        $certificationPartner = $this->getCertificationPartner();
        $certification = $certificationPartner->getCertification();
        return Tools::getEnvValue('WEDOF_BASE_URI') . '/formation/certifications/catalogue/' . $certification->getCertifInfo() . '/audit?query=' . $certification->getExternalId();
    }

    /**
     * @Serializer\VirtualProperty()
     * @Serializer\SerializedName("certifierLink")
     * @Serializer\Expose()
     */
    public function getCertifierLink(): string
    {
        $certificationPartner = $this->getCertificationPartner();
        $certification = $certificationPartner->getCertification();
        $certifInfo = $certification->getCertifInfo();
        $partnerSiret = $certificationPartner->getPartner()->getSiret();
        return Tools::getEnvValue('WEDOF_BASE_URI') . '/certification/partenariats/' . $certifInfo . '/' . $partnerSiret;
    }

    public function getErrorMessage(): ?string
    {
        return $this->errorMessage;
    }

    public function setErrorMessage(?string $errorMessage): self
    {
        $this->errorMessage = $errorMessage;

        return $this;
    }
}
