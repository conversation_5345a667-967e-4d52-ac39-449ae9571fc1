<?php

namespace App\Entity;

use App\Repository\CertificationFolderSurveyRepository;
use DateTimeInterface;
use Doctrine\ORM\Mapping as ORM;
use J<PERSON>\Serializer\Annotation as Serializer;

/**
 * @ORM\Entity(repositoryClass=CertificationFolderSurveyRepository::class)
 */
class CertificationFolderSurvey
{
    public const CLASSNAME = 'CertificationFolderSurvey';

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private int $id;

    /**
     * @ORM\OneToOne(targetEntity=CertificationFolder::class, inversedBy="survey", cascade={"persist", "remove"})
     * @ORM\JoinColumn(nullable=false)
     * @Serializer\Exclude()
     */
    private CertificationFolder $certificationFolder;

    /**
     * @ORM\ManyToOne(targetEntity=AttendeeExperience::class)
     */
    private ?AttendeeExperience $initialExperience = null;

    /**
     * @ORM\Column(type="date", nullable=true)
     */
    private ?DateTimeInterface $initialExperienceAnsweredDate = null;

    /**
     * @ORM\ManyToOne(targetEntity=AttendeeExperience::class)
     */
    private ?AttendeeExperience $sixMonthExperience = null;

    /**
     * @ORM\Column(type="date", nullable=true)
     */
    private ?DateTimeInterface $sixMonthExperienceAnsweredDate = null;

    /**
     * @ORM\Column(type="date", nullable=true)
     */
    private ?DateTimeInterface $sixMonthExperienceStartDate = null;


    /**
     * @ORM\ManyToOne(targetEntity=AttendeeExperience::class)
     */
    private ?AttendeeExperience $longTermExperience = null;

    /**
     * @ORM\Column(type="date", nullable=true)
     */
    private ?DateTimeInterface $longTermExperienceAnsweredDate = null;

    /**
     * @ORM\Column(type="date", nullable=true)
     */
    private ?DateTimeInterface $longTermExperienceStartDate = null;

    /**
     * @ORM\Column(type="string", length=255, options={"default" : "created"})
     */
    private string $state = 'created';

    public function getEntityId(): string
    {
        return $this->getId();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getCertificationFolder(): CertificationFolder
    {
        return $this->certificationFolder;
    }

    public function setCertificationFolder(CertificationFolder $certificationFolder): self
    {
        $this->certificationFolder = $certificationFolder;

        return $this;
    }

    public function getInitialExperience(): ?AttendeeExperience
    {
        return $this->initialExperience;
    }

    public function setInitialExperience(?AttendeeExperience $initialExperience): self
    {
        $this->initialExperience = $initialExperience;

        return $this;
    }

    public function getInitialExperienceAnsweredDate(): ?DateTimeInterface
    {
        return $this->initialExperienceAnsweredDate;
    }

    public function setInitialExperienceAnsweredDate(?DateTimeInterface $initialExperienceAnsweredDate): self
    {
        $this->initialExperienceAnsweredDate = $initialExperienceAnsweredDate;

        return $this;
    }

    public function getSixMonthExperience(): ?AttendeeExperience
    {
        return $this->sixMonthExperience;
    }

    public function setSixMonthExperience(?AttendeeExperience $sixMonthExperience): self
    {
        $this->sixMonthExperience = $sixMonthExperience;

        return $this;
    }

    public function getSixMonthExperienceAnsweredDate(): ?DateTimeInterface
    {
        return $this->sixMonthExperienceAnsweredDate;
    }

    public function setSixMonthExperienceAnsweredDate(?DateTimeInterface $sixMonthExperienceAnsweredDate): self
    {
        $this->sixMonthExperienceAnsweredDate = $sixMonthExperienceAnsweredDate;

        return $this;
    }

    public function getSixMonthExperienceStartDate(): ?DateTimeInterface
    {
        return $this->sixMonthExperienceStartDate;
    }

    public function setSixMonthExperienceStartDate(?DateTimeInterface $sixMonthExperienceStartDate): self
    {
        $this->sixMonthExperienceStartDate = $sixMonthExperienceStartDate;

        return $this;
    }

    public function getLongTermExperience(): ?AttendeeExperience
    {
        return $this->longTermExperience;
    }

    public function setLongTermExperience(?AttendeeExperience $longTermExperience): self
    {
        $this->longTermExperience = $longTermExperience;

        return $this;
    }

    public function getLongTermExperienceAnsweredDate(): ?DateTimeInterface
    {
        return $this->longTermExperienceAnsweredDate;
    }

    public function setLongTermExperienceAnsweredDate(?DateTimeInterface $longTermExperienceAnsweredDate): self
    {
        $this->longTermExperienceAnsweredDate = $longTermExperienceAnsweredDate;

        return $this;
    }


    public function getLongTermExperienceStartDate(): ?DateTimeInterface
    {
        return $this->longTermExperienceStartDate;
    }

    public function setLongTermExperienceStartDate(?DateTimeInterface $longTermExperienceStartDate): self
    {
        $this->longTermExperienceStartDate = $longTermExperienceStartDate;

        return $this;
    }

    public function getState(): string
    {
        return $this->state;
    }

    public function setState(string $state): self
    {
        $this->state = $state;

        return $this;
    }
}
