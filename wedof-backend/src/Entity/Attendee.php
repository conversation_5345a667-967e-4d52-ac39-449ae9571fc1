<?php

namespace App\Entity;

use App\Library\utils\enums\AttendeeGender;
use App\Repository\AttendeeRepository;
use DateTime;
use DateTimeInterface;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;
use Symfony\Component\HttpFoundation\File\File;
use Symfony\Component\Security\Core\User\UserInterface;
use Vich\UploaderBundle\Mapping\Annotation as Vich;

/**
 * @ORM\Entity(repositoryClass=AttendeeRepository::class)
 * @Vich\Uploadable
 * @ORM\Table(indexes={
 *     @ORM\Index(name="email_idx",columns={"email"}),
 *     @ORM\Index(name="phone_number_idx",columns={"phone_number"}),
 *     @ORM\Index(name="external_id_idx",columns={"external_id"})
 * })
 */
class Attendee implements UserInterface
{

    public const CLASSNAME = 'Attendee';

    /**
     * @ORM\Id()
     * @ORM\GeneratedValue()
     * @ORM\Column(type="integer")
     * @Serializer\Groups({"owner", "Default"})
     */
    private int $id;

    /**
     * @ORM\Column(type="string", length=255)
     * @Serializer\Groups({"owner", "Default"})
     */
    private string $lastName;

    /**
     * @ORM\Column(type="string", length=255)
     * @Serializer\Groups({"owner", "Default"})
     */
    private string $firstName;

    /**
     * @ORM\Column(type="string", length=255)
     * @Serializer\Groups({"owner", "Default"})
     */
    private string $email;

    /**
     * @ORM\Column(type="string", length=20, nullable=true)
     * @Serializer\Groups({"owner", "Default"})
     */
    private ?string $phoneNumber = null;

    /**
     * @ORM\Column(type="string", length=20, nullable=true)
     * @Serializer\Groups({"owner", "Default"})
     */
    private ?string $phoneFixed = null;

    /**
     * @ORM\Column(type="smallint", nullable=true)
     * @Serializer\Groups({"owner", "Default"})
     */
    private ?int $degree = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Groups({"owner", "Default"})
     */
    private ?string $degreeTitle = null;

    /**
     * @ORM\OneToMany(targetEntity="App\Entity\RegistrationFolder", mappedBy="attendee")
     * @Serializer\Exclude()
     */
    private Collection $registrationFolders;

    /**
     * @ORM\OneToMany(targetEntity="App\Entity\CertificationFolder", mappedBy="attendee")
     * @Serializer\Exclude()
     */
    private Collection $certificationFolders;

    /**
     * @ORM\Column(type="json")
     * @Serializer\Exclude()
     */
    private array $rawData = [];

    /**
     * @ORM\Column(type="datetime")
     * @Serializer\Exclude()
     */
    private DateTimeInterface $lastUpdate;

    /**
     * @ORM\Column(type="json", nullable=true)
     * @Serializer\Groups({"owner", "Default"})
     */
    private ?array $address = null;

    /**
     * @ORM\Column(type="date", nullable=true)
     * @Serializer\Groups({"owner", "Default"})
     */
    private ?DateTimeInterface $dateOfBirth = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Groups({"owner", "Default"})
     */
    private ?string $nameCityOfBirth = null;

    /**
     * @ORM\Column(type="string", length=10, nullable=true)
     * @Serializer\Groups({"owner", "Default"})
     */
    private ?string $gender = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Groups({"owner", "Default"})
     */
    private ?string $birthName = null;

    /**
     * @ORM\Column(type="integer", nullable=true)
     * @Serializer\Groups({"owner", "Default"})
     */
    private ?int $codeCountryOfBirth = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private ?string $employmentStatus = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Groups({"owner"})
     */
    private ?string $poleEmploiId = null;

    /**
     * @ORM\Column(type="integer", nullable=true)
     * @Serializer\Groups({"owner"})
     */
    private ?int $poleEmploiDpt = null;

    /**
     * @ORM\Column(type="string", length=5, nullable=true)
     * @Serializer\Groups({"owner", "Default"})
     */
    private ?string $codeCityOfBirth = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Groups({"owner", "Default"})
     */
    private ?string $firstName2 = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Groups({"owner", "Default"})
     */
    private ?string $firstName3 = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Groups({"owner", "Default"})
     */
    private ?string $nameCountryOfBirth = null;

    /**
     * @ORM\Column(type="string", nullable=true)
     * @Serializer\Groups({"owner"})
     */
    private ?string $poleEmploiRegionCode = null;

    /**
     * @ORM\Column(type="boolean", options={"default":false})
     * @Serializer\Groups({"owner", "Default"})
     */
    private bool $readOnly = false;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private ?string $externalId = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     */
    private ?DateTimeInterface $lastLogin = null;

    /**
     * @Vich\UploadableField(mapping="attendeeFile", fileNameProperty="identificationDocumentName")
     * @Serializer\Exclude()
     * @var File|null
     */
    private ?File $identificationDocumentFile = null;

    /**
     * @ORM\Column(type="string", nullable=true)
     * @Serializer\Exclude()
     * @var string|null
     */
    private ?string $identificationDocumentName = null;

    /**
     * @ORM\Column(type="boolean", options={"default":false})
     * @Serializer\Expose()
     * @Serializer\Groups({"owner", "Default"})
     */
    private bool $cdcCompliant = false;

    /**
     * @ORM\OneToMany(targetEntity=AttendeeExperience::class, mappedBy="attendee", orphanRemoval=true)
     */
    private Collection $experiences;

    /**
     * @ORM\Column(type="boolean", options={"default":false})
     * @Serializer\Expose()
     * @Serializer\Groups({"owner", "Default"})
     */
    private bool $emailValidated = false;

    /**
     * @ORM\Column(type="boolean", options={"default":false})
     * @Serializer\Expose()
     * @Serializer\Groups({"owner", "Default"})
     */
    private bool $phoneNumberValidated = false;

    /**
     * @ORM\Column(type="string", length=15, nullable=true)
     * @Serializer\Exclude()
     */
    private ?string $nir = null;

    /**
     * @ORM\Column(type="boolean", options={"default":false})
     * @Serializer\Expose()
     * @Serializer\Groups({"owner", "Default"})
     */
    private bool $nirValidated = false;

    public function __construct()
    {
        $this->registrationFolders = new ArrayCollection();
        $this->certificationFolders = new ArrayCollection();
        $this->experiences = new ArrayCollection();
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getLastName(): string
    {
        return $this->lastName;
    }

    public function setLastName(string $lastName): self
    {
        $this->lastName = $lastName;

        return $this;
    }

    public function getFirstName(): string
    {
        return $this->firstName;
    }

    public function setFirstName(string $firstName): self
    {
        $this->firstName = $firstName;

        return $this;
    }

    public function getEmail(): string
    {
        return $this->email;
    }

    public function setEmail(string $email): self
    {
        $this->email = $email;

        return $this;
    }

    public function getPhoneNumber(): ?string
    {
        return $this->phoneNumber;
    }

    public function setPhoneNumber(?string $phoneNumber): self
    {
        $this->phoneNumber = $phoneNumber;

        return $this;
    }

    public function getPhoneFixed(): ?string
    {
        return $this->phoneFixed;
    }

    public function setPhoneFixed(?string $phoneFixed): self
    {
        $this->phoneFixed = $phoneFixed;

        return $this;
    }

    public function getDegree(): ?int
    {
        return $this->degree;
    }

    public function setDegree(?int $degree): self
    {
        $this->degree = $degree;

        return $this;
    }

    public function getDegreeTitle(): ?string
    {
        return $this->degreeTitle;
    }

    public function setDegreeTitle(?string $degreeTitle): self
    {
        $this->degreeTitle = $degreeTitle;

        return $this;
    }

    public function addRegistrationFolder(RegistrationFolder $registrationFolder): self
    {
        if (!$this->registrationFolders->contains($registrationFolder)) {
            $this->registrationFolders[] = $registrationFolder;
            $registrationFolder->setAttendee($this);
        }

        return $this;
    }

    public function removeRegistrationFolder(RegistrationFolder $registrationFolder): self
    {
        if ($this->registrationFolders->contains($registrationFolder)) {
            $this->registrationFolders->removeElement($registrationFolder);
            // set the owning side to null (unless already changed)
            if ($registrationFolder->getAttendee() === $this) {
                $registrationFolder->setAttendee(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection|RegistrationFolder[]
     */
    public function getRegistrationFolders(): Collection
    {
        return $this->registrationFolders;
    }

    /**
     * @return Collection|CertificationFolder[]
     */
    public function getCertificationFolders(): Collection
    {
        return $this->certificationFolders;
    }

    public function addCertificationFolder(CertificationFolder $certificationFolder): self
    {
        if (!$this->certificationFolders->contains($certificationFolder)) {
            $this->certificationFolders[] = $certificationFolder;
            $certificationFolder->setAttendee($this);
        }

        return $this;
    }

    public function removeCertificationFolder(CertificationFolder $certificationFolder): self
    {
        if ($this->certificationFolders->contains($certificationFolder)) {
            $this->certificationFolders->removeElement($certificationFolder);
            // set the owning side to null (unless already changed)
            if ($certificationFolder->getAttendee() === $this) {
                $certificationFolder->setAttendee(null);
            }
        }

        return $this;
    }

    public function getRawData(): array
    {
        return $this->rawData;
    }

    public function setRawData(array $rawData): self
    {
        $this->rawData = $rawData;

        return $this;
    }

    public function getLastUpdate(): DateTimeInterface
    {
        return $this->lastUpdate;
    }

    public function setLastUpdate(DateTimeInterface $lastUpdate): self
    {
        $this->lastUpdate = $lastUpdate;

        return $this;
    }

    public function getAddress(): ?array
    {
        return $this->address;
    }

    /**
     * @Serializer\VirtualProperty()
     * @Serializer\SerializedName("fullAddress")
     * @Serializer\Expose
     * @Serializer\Groups({"owner", "Default"})
     */
    public function getFullAddress(): string
    {
        if (empty($this->address)) {
            return '';
        }
        $adressFields = ['number', 'repetitionIndexLabel', 'roadTypeLabel', 'roadName', 'zipCode', 'city'];
        $addressValues = [];
        foreach ($adressFields as $field) {
            if (!empty($this->address[$field])) {
                $addressValues[] = $this->address[$field];
            }
        }
        return implode(' ', $addressValues); // Don't put ',' in an address
    }

    public function setAddress(?array $address): self
    {
        $this->address = $address;

        return $this;
    }

    public function getDisplayName(): string
    {
        return $this->getFirstName() . ' ' . $this->getLastName();
    }

    public function getDateOfBirth(): ?DateTimeInterface
    {
        return $this->dateOfBirth;
    }

    public function setDateOfBirth(?DateTimeInterface $dateOfBirth): self
    {
        $this->dateOfBirth = $dateOfBirth;

        return $this;
    }

    public function getNameCityOfBirth(): ?string
    {
        return $this->nameCityOfBirth;
    }

    public function setNameCityOfBirth(?string $nameCityOfBirth): self
    {
        $this->nameCityOfBirth = $nameCityOfBirth;

        return $this;
    }

    public function getGender(): ?string
    {
        return $this->gender;
    }

    public function setGender(?string $gender): self
    {
        $this->gender = $gender;

        return $this;
    }

    public function getBirthName(): ?string
    {
        return $this->birthName;
    }

    public function setBirthName(?string $birthName): self
    {
        $this->birthName = $birthName;

        return $this;
    }

    public function getCodeCountryOfBirth(): ?int
    {
        return $this->codeCountryOfBirth;
    }

    public function setCodeCountryOfBirth(?int $codeCountryOfBirth): self
    {
        $this->codeCountryOfBirth = $codeCountryOfBirth;

        return $this;
    }

    public function getEmploymentStatus(): ?string
    {
        return $this->employmentStatus;
    }

    public function setEmploymentStatus(?string $employmentStatus): self
    {
        $this->employmentStatus = $employmentStatus;

        return $this;
    }

    public function getPoleEmploiId(): ?string
    {
        return $this->poleEmploiId;
    }

    public function setPoleEmploiId(?string $poleEmploiId): self
    {
        $this->poleEmploiId = $poleEmploiId;

        return $this;
    }

    public function getPoleEmploiDpt(): ?int
    {
        return $this->poleEmploiDpt;
    }

    public function setPoleEmploiDpt(?int $poleEmploiDpt): self
    {
        $this->poleEmploiDpt = $poleEmploiDpt;
        return $this;
    }

    public function getCodeCityOfBirth(): ?string
    {
        return $this->codeCityOfBirth;
    }

    public function setCodeCityOfBirth(?string $codeCityOfBirth): self
    {
        $this->codeCityOfBirth = $codeCityOfBirth;

        return $this;
    }

    public function getFirstName2(): ?string
    {
        return $this->firstName2;
    }

    public function setFirstName2(?string $firstName2): self
    {
        $this->firstName2 = $firstName2;

        return $this;
    }

    public function getFirstName3(): ?string
    {
        return $this->firstName3;
    }

    public function setFirstName3(?string $firstName3): self
    {
        $this->firstName3 = $firstName3;

        return $this;
    }

    public function getNameCountryOfBirth(): ?string
    {
        return $this->nameCountryOfBirth;
    }

    public function setNameCountryOfBirth(?string $nameCountryOfBirth): self
    {
        $this->nameCountryOfBirth = $nameCountryOfBirth;

        return $this;
    }

    public function getPoleEmploiRegionCode(): ?string
    {
        return $this->poleEmploiRegionCode;
    }

    public function setPoleEmploiRegionCode(?string $poleEmploiRegionCode): self
    {
        $this->poleEmploiRegionCode = $poleEmploiRegionCode;

        return $this;
    }

    public function isReadOnly(): ?bool
    {
        return $this->readOnly;
    }

    public function setReadOnly(bool $readOnly): self
    {
        $this->readOnly = $readOnly;

        return $this;
    }

    /**
     * @return string[]
     */
    public function getRoles(): array
    {
        return ['ROLE_ATTENDEE'];
    }

    public function getPassword()
    {
        return null;
    }

    public function getSalt()
    {
        return null;
    }

    public function eraseCredentials()
    {
    }

    public function getExternalId(): ?string
    {
        return $this->externalId;
    }

    public function setExternalId(?string $externalId): self
    {
        $this->externalId = $externalId;

        return $this;
    }

    /**
     * @return string
     */
    public function getUsername(): string
    {
        return $this->getUserIdentifier();
    }

    public function getUserIdentifier(): string
    {
        return strval($this->getId());
    }

    public function getLastLogin(): ?DateTimeInterface
    {
        return $this->lastLogin;
    }

    public function setLastLogin(?DateTimeInterface $lastLogin): self
    {
        $this->lastLogin = $lastLogin;

        return $this;
    }

    /**
     * @param File|null $identificationDocumentFile
     */
    public function setIdentificationDocumentFile(?File $identificationDocumentFile = null): void
    {
        $this->identificationDocumentFile = $identificationDocumentFile;

        if (null !== $identificationDocumentFile) {
            // It is required that at least one field changes if you are using doctrine
            // otherwise the event listeners won't be called and the file is lost
            $this->setLastUpdate(new DateTime());
        }
    }

    /**
     * @return File|null
     */
    public function getIdentificationDocumentFile(): ?File
    {
        return $this->identificationDocumentFile;
    }

    /**
     * @param string|null $identificationDocumentName
     * @return void
     */
    public function setIdentificationDocumentName(?string $identificationDocumentName): void
    {
        $this->identificationDocumentName = $identificationDocumentName;
    }

    /**
     * @return string|null
     */
    public function getIdentificationDocumentName(): ?string
    {
        return $this->identificationDocumentName;
    }

    /**
     * @Serializer\VirtualProperty()
     * @Serializer\SerializedName("identificationDocument")
     * @Serializer\Expose()
     * @Serializer\Groups({"owner", "Default"})
     */
    public function getIdentificationDocument(): ?string
    {
        if ($this->getIdentificationDocumentName()) {
            //should be dynamic based on vich_uploader.yml but how
            return pathinfo($this->getIdentificationDocumentName(), PATHINFO_EXTENSION);
        } else {
            return null;
        }
    }

    public function isCdcCompliant(): ?bool
    {
        return $this->cdcCompliant;
    }

    public function refreshCdcCompliant(): self
    {
        if (!$this->getDateOfBirth() || !$this->getGender() ||
            (!$this->getCodeCityOfBirth() && (!$this->getCodeCountryOfBirth() || $this->getCodeCountryOfBirth() === 100))) {
            $this->cdcCompliant = false;
        } else {
            $this->cdcCompliant = true;
        }
        return $this;
    }

    /**
     * @return Collection<int, AttendeeExperience>
     */
    public function getExperiences(): Collection
    {
        return $this->experiences;
    }

    public function addExperience(AttendeeExperience $experience): self
    {
        if (!$this->experiences->contains($experience)) {
            $this->experiences[] = $experience;
            $experience->setAttendee($this);
        }

        return $this;
    }

    public function removeExperience(AttendeeExperience $experience): self
    {
        if ($this->experiences->removeElement($experience)) {
            // set the owning side to null (unless already changed)
            if ($experience->getAttendee() === $this) {
                $experience->setAttendee(null);
            }
        }

        return $this;
    }

    public function getPlaceOfBirth(): string
    {
        $codeCityOfBirth = $this->getCodeCityOfBirth();
        $nameCityOfBirth = $this->getNameCityOfBirth();
        $nameCountryOfBirth = $this->getNameCountryOfBirth();
        if ($codeCityOfBirth && $nameCityOfBirth) {
            return $nameCityOfBirth . ' (' . substr($codeCityOfBirth, 0, 2) . ')';
        } else if ($nameCountryOfBirth) {
            return $nameCountryOfBirth;
        } else {
            return ' ';
        }
    }

    public function isEmailValidated(): ?bool
    {
        return $this->emailValidated;
    }

    public function setEmailValidated(bool $emailValidated): self
    {
        $this->emailValidated = $emailValidated;

        return $this;
    }

    public function isPhoneNumberValidated(): ?bool
    {
        return $this->phoneNumberValidated;
    }

    public function setPhoneNumberValidated(bool $phoneNumberValidated): self
    {
        $this->phoneNumberValidated = $phoneNumberValidated;

        return $this;
    }

    public function getNir(): ?string
    {
        return $this->nir;
    }

    public function setNir(?string $nir): self
    {
        $this->nir = $nir;

        return $this;
    }

    public function retrievedNir(): ?string
    {
        $retrievedNir = $this->isNirValidated() || strlen($this->getNir()) === 15 ? $this->getNir() : null;
        if (!$retrievedNir && $this->getGender()) {
            $retrievedNir .= $this->getGender() === AttendeeGender::FEMALE()->getValue() ? "2" : "1";
            if ($this->getDateOfBirth()) {
                $birthYearAttendee = substr($this->getDateOfBirth()->format('Y'), 2);
                $retrievedNir .= strval($birthYearAttendee) . strval($this->getDateOfBirth()->format('m'));
                if ($this->getCodeCityOfBirth()) {
                    $retrievedNir  .= strval($this->getCodeCityOfBirth());
                } else if ($this->getCodeCountryOfBirth()) {
                    $retrievedNir  .= "99" . $this->getCodeCountryOfBirth();
                }
            }
        }
        return $retrievedNir;
    }

    public function isNirValidated(): ?bool
    {
        return $this->nirValidated;
    }

    public function setNirValidated(bool $nirValidated): self
    {
        $this->nirValidated = $nirValidated;

        return $this;
    }
}
