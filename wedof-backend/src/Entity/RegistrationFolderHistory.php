<?php

namespace App\Entity;

use App\Repository\RegistrationFolderHistoryRepository;
use DateTimeInterface;
use Doctrine\ORM\Mapping as ORM;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;

/**
 * @ORM\Entity(repositoryClass=RegistrationFolderHistoryRepository::class)
 */
class RegistrationFolderHistory
{
    /**
     * @Serializer\Exclude
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private int $id;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Groups({"owner"})
     */
    private ?DateTimeInterface $serviceDoneDeclaredAttendeeDate = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Groups({"owner"})
     */
    private ?DateTimeInterface $billedDate = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Groups({"owner"})
     */
    private ?DateTimeInterface $paidDate = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Groups({"owner", "Default"})
     */
    private ?DateTimeInterface $acceptedDate = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Groups({"owner"})
     */
    private ?DateTimeInterface $rejectedWithoutTitulaireSuiteDate = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Groups({"owner"})
     */
    private ?DateTimeInterface $validatedDate = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Groups({"owner", "Default"})
     */
    private ?DateTimeInterface $inTrainingDate = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Groups({"owner", "Default"})
     */
    private ?DateTimeInterface $terminatedDate = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Groups({"owner"})
     */
    private ?DateTimeInterface $notProcessedDate = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Groups({"owner"})
     */
    private ?DateTimeInterface $refusedByAttendeeDate = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Groups({"owner"})
     */
    private ?DateTimeInterface $refusedByOrganismDate = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Groups({"owner"})
     */
    private ?DateTimeInterface $refusedByFinancerDate = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Groups({"owner"})
     */
    private ?DateTimeInterface $canceledByAttendeeDate = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Groups({"owner"})
     */
    private ?DateTimeInterface $canceledByOrganismDate = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Groups({"owner", "Default"})
     */
    private ?DateTimeInterface $serviceDoneDeclaredDate = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Groups({"owner"})
     */
    private ?DateTimeInterface $serviceDoneValidatedDate = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Groups({"owner"})
     */
    private ?DateTimeInterface $canceledByAttendeeNotRealizedDate = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Groups({"owner"})
     */
    private ?DateTimeInterface $canceledByFinancerDate = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Groups({"owner"})
     */
    private ?DateTimeInterface $inControlDate = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Groups({"owner"})
     */
    private ?DateTimeInterface $releasedDate = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"owner"})
     */
    private ?DateTimeInterface $completionRateLastUpdate = null;

    public function getId(): int
    {
        return $this->id;
    }

    public function getServiceDoneDeclaredAttendeeDate(): ?DateTimeInterface
    {
        return $this->serviceDoneDeclaredAttendeeDate;
    }

    public function setServiceDoneDeclaredAttendeeDate(?DateTimeInterface $serviceDoneDeclaredAttendeeDate): self
    {
        $this->serviceDoneDeclaredAttendeeDate = $serviceDoneDeclaredAttendeeDate;

        return $this;
    }

    public function getBilledDate(): ?DateTimeInterface
    {
        return $this->billedDate;
    }

    public function setBilledDate(?DateTimeInterface $billedDate): self
    {
        $this->billedDate = $billedDate;

        return $this;
    }

    public function getPaidDate(): ?DateTimeInterface
    {
        return $this->paidDate;
    }

    public function setPaidDate(?DateTimeInterface $paidDate): void
    {
        $this->paidDate = $paidDate;
    }

    public function getAcceptedDate(): ?DateTimeInterface
    {
        return $this->acceptedDate;
    }

    public function setAcceptedDate(?DateTimeInterface $acceptedDate): self
    {
        $this->acceptedDate = $acceptedDate;

        return $this;
    }

    public function getRejectedWithoutTitulaireSuiteDate(): ?DateTimeInterface
    {
        return $this->rejectedWithoutTitulaireSuiteDate;
    }

    public function setRejectedWithoutTitulaireSuiteDate(?DateTimeInterface $rejectedWithoutTitulaireSuiteDate): self
    {
        $this->rejectedWithoutTitulaireSuiteDate = $rejectedWithoutTitulaireSuiteDate;

        return $this;
    }

    public function getValidatedDate(): ?DateTimeInterface
    {
        return $this->validatedDate;
    }

    public function setValidatedDate(?DateTimeInterface $validatedDate): self
    {
        $this->validatedDate = $validatedDate;

        return $this;
    }

    public function getInTrainingDate(): ?DateTimeInterface
    {
        return $this->inTrainingDate;
    }

    public function setInTrainingDate(?DateTimeInterface $inTrainingDate): self
    {
        $this->inTrainingDate = $inTrainingDate;

        return $this;
    }

    public function getTerminatedDate(): ?DateTimeInterface
    {
        return $this->terminatedDate;
    }

    public function setTerminatedDate(?DateTimeInterface $terminatedDate): self
    {
        $this->terminatedDate = $terminatedDate;

        return $this;
    }

    public function getNotProcessedDate(): ?DateTimeInterface
    {
        return $this->notProcessedDate;
    }

    public function setNotProcessedDate(?DateTimeInterface $notProcessedDate): self
    {
        $this->notProcessedDate = $notProcessedDate;

        return $this;
    }

    public function getRefusedByAttendeeDate(): ?DateTimeInterface
    {
        return $this->refusedByAttendeeDate;
    }

    public function setRefusedByAttendeeDate(?DateTimeInterface $refusedByAttendeeDate): self
    {
        $this->refusedByAttendeeDate = $refusedByAttendeeDate;

        return $this;
    }

    public function getRefusedByOrganismDate(): ?DateTimeInterface
    {
        return $this->refusedByOrganismDate;
    }

    public function setRefusedByOrganismDate(?DateTimeInterface $refusedByOrganismDate): self
    {
        $this->refusedByOrganismDate = $refusedByOrganismDate;

        return $this;
    }

    public function getRefusedByFinancerDate(): ?DateTimeInterface
    {
        return $this->refusedByFinancerDate;
    }

    public function setRefusedByFinancerDate(?DateTimeInterface $refusedByFinancerDate): self
    {
        $this->refusedByFinancerDate = $refusedByFinancerDate;

        return $this;
    }

    public function getCanceledByAttendeeDate(): ?DateTimeInterface
    {
        return $this->canceledByAttendeeDate;
    }

    public function setCanceledByAttendeeDate(?DateTimeInterface $canceledByAttendeeDate): self
    {
        $this->canceledByAttendeeDate = $canceledByAttendeeDate;

        return $this;
    }

    public function getCanceledByOrganismDate(): ?DateTimeInterface
    {
        return $this->canceledByOrganismDate;
    }

    public function setCanceledByOrganismDate(?DateTimeInterface $canceledByOrganismDate): self
    {
        $this->canceledByOrganismDate = $canceledByOrganismDate;

        return $this;
    }

    public function getServiceDoneDeclaredDate(): ?DateTimeInterface
    {
        return $this->serviceDoneDeclaredDate;
    }

    public function setServiceDoneDeclaredDate(?DateTimeInterface $serviceDoneDeclaredDate): self
    {
        $this->serviceDoneDeclaredDate = $serviceDoneDeclaredDate;

        return $this;
    }

    public function getServiceDoneValidatedDate(): ?DateTimeInterface
    {
        return $this->serviceDoneValidatedDate;
    }

    public function setServiceDoneValidatedDate(?DateTimeInterface $serviceDoneValidatedDate): self
    {
        $this->serviceDoneValidatedDate = $serviceDoneValidatedDate;

        return $this;
    }

    public function getCanceledByAttendeeNotRealizedDate(): ?DateTimeInterface
    {
        return $this->canceledByAttendeeNotRealizedDate;
    }

    public function setCanceledByAttendeeNotRealizedDate(?DateTimeInterface $canceledByAttendeeNotRealizedDate): self
    {
        $this->canceledByAttendeeNotRealizedDate = $canceledByAttendeeNotRealizedDate;

        return $this;
    }

    public function getCanceledByFinancerDate(): ?DateTimeInterface
    {
        return $this->canceledByFinancerDate;
    }

    public function setCanceledByFinancerDate(?DateTimeInterface $canceledByFinancerDate): self
    {
        $this->canceledByFinancerDate = $canceledByFinancerDate;

        return $this;
    }

    public function getInControlDate(): ?\DateTimeInterface
    {
        return $this->inControlDate;
    }

    public function setInControlDate(?\DateTimeInterface $inControlDate): self
    {
        $this->inControlDate = $inControlDate;

        return $this;
    }

    public function getReleasedDate(): ?\DateTimeInterface
    {
        return $this->releasedDate;
    }

    public function setReleasedDate(?\DateTimeInterface $releasedDate): self
    {
        $this->releasedDate = $releasedDate;

        return $this;
    }

    public function getCompletionRateLastUpdate(): ?DateTimeInterface
    {
        return $this->completionRateLastUpdate;
    }

    public function setCompletionRateLastUpdate(DateTimeInterface $completionRateLastUpdate): self
    {
        $this->completionRateLastUpdate = $completionRateLastUpdate;

        return $this;
    }
}
