<?php

namespace App\Entity;

use App\Entity\Traits\TimestampableTrait;
use App\Library\utils\Tools;
use App\Repository\InvoiceRepository;
use DateTimeInterface;
use Doctrine\ORM\Mapping as ORM;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;
use Symfony\Component\HttpFoundation\File\File;
use Vich\UploaderBundle\Mapping\Annotation as Vich;

/**
 * @Vich\Uploadable
 * @ORM\Table(indexes={@ORM\Index(name="entity",columns={"entity_class", "entity_id"})})
 * @ORM\HasLifecycleCallbacks()
 * @Serializer\ExclusionPolicy("ALL")
 * @ORM\Entity(repositoryClass=InvoiceRepository::class)
 */
class Invoice
{
    use TimestampableTrait;

    public const CLASSNAME = 'Invoice';

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Serializer\Expose
     */
    private int $id;

    /**
     * @ORM\Column(type="string", length=255)
     * @Serializer\Expose
     */
    private string $entityClass;

    /**
     * @ORM\Column(type="string", length=255)
     * @Serializer\Expose
     */
    private string $entityId;

    /**
     * @ORM\Column(type="string", length=255)
     * @Serializer\Expose
     */
    private string $externalId;

    /**
     * @ORM\Column(type="string", length=50)
     * @Serializer\Expose
     */
    private string $state;

    /**
     * @ORM\Column(type="string", length=50)
     * @Serializer\Expose
     */
    private string $type;

    /**
     * @Vich\UploadableField(mapping="invoiceFile", fileNameProperty="invoiceFileName")
     * @var File|null
     *
     */
    private ?File $file = null;

    /**
     * @ORM\Column(type="text", nullable=true)
     * Exposed on the getLink
     */
    private ?string $link = null;

    /**
     * @ORM\Column(type="string", nullable=true)
     * @var string|null
     */
    private ?string $invoiceFileName = null;

    /**
     * @ORM\Column(type="text", nullable=true)
     * @Serializer\Expose
     */
    private ?string $paymentLink = null;

    /**
     * @ORM\Column(type="text", nullable=true)
     * @Serializer\Expose
     */
    private ?string $description = null;

    /**
     * @ORM\Column(type="date", nullable=true)
     * @Serializer\Expose
     */
    private ?DateTimeInterface $dueDate = null;

    /**
     * @ORM\Column(type="string", length=255)
     * @Serializer\Expose
     */
    private string $fileType;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getState(): ?string
    {
        return $this->state;
    }

    public function setState(string $state): self
    {
        $this->state = $state;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getFile(): ?File
    {
        return $this->file;
    }

    public function setFile(?File $file): self
    {
        $this->file = $file;

        return $this;
    }

    /**
     * @return string|null
     */
    public function getInvoiceFileName(): ?string
    {
        return $this->invoiceFileName;
    }

    /**
     * @param string|null $invoiceFileName
     */
    public function setInvoiceFileName(?string $invoiceFileName): void
    {
        $this->invoiceFileName = $invoiceFileName;
    }

    /**
     * @return string
     */
    public function getEntityClass(): string
    {
        return $this->entityClass;
    }

    /**
     * @param string $entityClass
     */
    public function setEntityClass(string $entityClass): void
    {
        $this->entityClass = $entityClass;
    }

    /**
     * @return string
     */
    public function getEntityId(): string
    {
        return $this->entityId;
    }

    /**
     * @param string $entityId
     */
    public function setEntityId(string $entityId): void
    {
        $this->entityId = $entityId;
    }

    /**
     * @return string
     */
    public function getExternalId(): string
    {
        return $this->externalId;
    }

    /**
     * @param string $externalId
     */
    public function setExternalId(string $externalId): void
    {
        $this->externalId = $externalId;
    }

    /**
     * @return string|null
     * @Serializer\VirtualProperty()
     * @Serializer\Expose()
     */
    public function getLink(): ?string
    {
        return $this->invoiceFileName ? Tools::getEnvValue('WEDOF_BASE_URI') . "/api/invoices/$this->id/file" : $this->link;
    }

    /**
     * @param string|null $link
     */
    public function setLink(?string $link): void
    {
        $this->link = $link;
    }

    /**
     * @return string|null
     */
    public function getPaymentLink(): ?string
    {
        return $this->paymentLink;
    }

    /**
     * @param string|null $paymentLink
     */
    public function setPaymentLink(?string $paymentLink): void
    {
        $this->paymentLink = $paymentLink;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): self
    {
        $this->description = $description;

        return $this;
    }

    public function getDueDate(): ?DateTimeInterface
    {
        return $this->dueDate;
    }

    public function setDueDate(?DateTimeInterface $dueDate): self
    {
        $this->dueDate = $dueDate;

        return $this;
    }

    /**
     * @return string
     */
    public function getFileType(): string
    {
        return $this->fileType;
    }

    /**
     * @param string|null $fileType
     * @return $this
     */
    public function setFileType(?string $fileType): self
    {
        $this->fileType = (string)$fileType;//https://github.com/dustin10/VichUploaderBundle/issues/1117

        return $this;
    }

}
