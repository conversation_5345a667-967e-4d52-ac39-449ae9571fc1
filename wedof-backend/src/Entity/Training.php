<?php

namespace App\Entity;

use App\Library\utils\AlphaID;
use App\Repository\TrainingRepository;
use Beelab\TagBundle\Entity\AbstractTaggable;
use DateTimeInterface;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Hateoas\Configuration\Annotation as Hateoas;
use J<PERSON>\Serializer\Annotation as Serializer;

/**
 * @ORM\Entity(repositoryClass=TrainingRepository::class)
 * @ORM\Table(indexes={
 *     @ORM\Index(name="external_idx",columns={"external_id"}),
 *     @ORM\Index(name="tState_idx", columns={"state"})
 * })
 *
 * @Hateoas\Relation("self", href = "expr('/api/trainings/' ~ object.getExternalId())")
 * @Hateoas\Relation("certification", href = "expr('/api/certifications/' ~ object.getCertification().getCertifInfo())", attributes={"name" = "expr(object.getCertification().getName())", "certifInfo" = "expr(object.getCertification().getCertifInfo())", "externalId" = "expr(object.getCertification().getExternalId())", "id" = "expr(object.getCertification().getId())", "enabled" = "expr(object.getCertification().getEnabled())"}, exclusion=@Hateoas\Exclusion(excludeIf = "expr(object.getCertification() === null)"))
 * @Hateoas\Relation("organism", href = "expr('/api/organisms/' ~ object.getOrganism().getSiret())", attributes={"name" = "expr(object.getOrganism().getName())", "siret" = "expr(object.getOrganism().getSiret())"}, exclusion=@Hateoas\Exclusion(excludeIf = "expr(object.getOrganism() === null)"))
 * @Hateoas\Relation("trainingActions", href = "expr('/api/trainingActions?trainingId=' ~ object.getExternalId())")
 * *
 * @Serializer\ExclusionPolicy("ALL")
 *
 */
class Training extends AbstractTaggable
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private ?int $id = null;

    /**
     * @ORM\Column(type="text")
     * @Serializer\Expose
     */
    private string $title;

    /**
     * @ORM\Column(type="string", length=255, unique=true)
     * @Serializer\Expose
     */
    private string $externalId;

    /**
     * @ORM\Column(type="datetime")
     * @Serializer\Expose
     */
    private DateTimeInterface $lastUpdate;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private ?string $trackerId = null;

    /**
     * @ORM\ManyToOne(targetEntity=Certification::class, inversedBy="trainings")
     * @ORM\JoinColumn(nullable=true)
     */
    private ?Certification $certification = null;

    /**
     * @ORM\ManyToOne(targetEntity=Organism::class, inversedBy="trainings")
     * @ORM\JoinColumn(nullable=false)
     */
    private Organism $organism;

    /**
     * @ORM\OneToMany(targetEntity=TrainingAction::class, mappedBy="training")
     * @ORM\OrderBy({"lastUpdate" = "DESC"})
     */
    private Collection $trainingActions;

    /**
     * @ORM\OneToMany(targetEntity=Evaluation::class, mappedBy="training")
     * @ORM\OrderBy({"date" = "DESC"})
     */
    private Collection $evaluations;

    /**
     * @ORM\Column(type="json", nullable=true)
     */
    private ?array $rawData = [];

    /**
     * @ORM\ManyToMany(targetEntity="Tag")
     * Display with serializer to get as array[string] and not as objet
     */
    protected $tags;

    /**
     * @ORM\Column(type="string", length=40, options={"default" : "notVerified"})
     * @Serializer\Expose
     */
    private string $compliance = 'notVerified';

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Expose
     */
    private ?DateTimeInterface $lastComplianceUpdate = null;

    /**
     * @ORM\Column(type="string", length=50)
     * @Serializer\Expose
     */
    private string $dataProvider;

    /**
     * @ORM\Column(type="string", length=50)
     * @Serializer\Expose
     */
    private string $state;

    /**
     * @ORM\ManyToMany(targetEntity=Skill::class)
     */
    private Collection $skillSets;

    public function __construct()
    {
        $this->trainingActions = new ArrayCollection();
        $this->evaluations = new ArrayCollection();
        $this->skillSets = new ArrayCollection();
        parent::__construct(); //needed by tags
    }

    public function getTitle(): string
    {
        return $this->title;
    }

    public function setTitle(string $title): self
    {
        $this->title = $title;

        return $this;
    }

    public function getLastUpdate(): DateTimeInterface
    {
        return $this->lastUpdate;
    }

    public function setLastUpdate(DateTimeInterface $lastUpdate): self
    {
        $this->lastUpdate = $lastUpdate;

        return $this;
    }

    public function getExternalId(): string
    {
        return $this->externalId;
    }

    public function setExternalId(string $externalId): self
    {
        $this->externalId = $externalId;

        return $this;
    }

    public function getCertification(): ?Certification
    {
        return $this->certification;
    }

    public function setCertification(?Certification $certification): self
    {
        $this->certification = $certification;

        return $this;
    }

    public function getOrganism(): Organism
    {
        return $this->organism;
    }

    public function setOrganism(Organism $organism): self
    {
        $this->organism = $organism;

        return $this;
    }

    public function getTrackerId(): ?string
    {
        if (!$this->trackerId && $this->id) {
            $this->setTrackerId(AlphaID::generate($this->getId(), false, 5, "wedof"));
        }
        return $this->trackerId;
    }

    public function setTrackerId(string $trackerId): self
    {
        $this->trackerId = $trackerId;

        return $this;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * @return Collection|Evaluation[]
     */
    public function getEvaluations(): Collection
    {
        return $this->evaluations;
    }

    public function addEvaluation(Evaluation $evaluation): self
    {
        if (!$this->evaluations->contains($evaluation)) {
            $this->evaluations[] = $evaluation;
            $evaluation->setTraining($this);
        }

        return $this;
    }

    public function removeEvaluation(Evaluation $evaluation): self
    {
        if ($this->evaluations->removeElement($evaluation)) {
            // set the owning side to null (unless already changed)
            if ($evaluation->getTraining() === $this) {
                $evaluation->setTraining(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection
     */
    public function getTrainingActions(): Collection
    {
        return $this->trainingActions;
    }

    public function addTrainingAction(TrainingAction $trainingAction): self
    {
        if (!$this->trainingActions->contains($trainingAction)) {
            $this->trainingActions[] = $trainingAction;
            $trainingAction->setTraining($this);
        }

        return $this;
    }

    public function removeTrainingAction(TrainingAction $trainingAction): self
    {
        if ($this->trainingActions->removeElement($trainingAction)) {
            // set the owning side to null (unless already changed)
            if ($trainingAction->getTraining() === $this) {
                $trainingAction->setTraining(null);
            }
        }

        return $this;
    }

    public function getRawData(): ?array
    {
        return $this->rawData;
    }

    /**
     * @param array|null $rawData
     * @return $this
     */
    public function setRawData(array $rawData): self
    {
        $this->rawData = $rawData;

        return $this;
    }

    public function getCompliance(): string
    {
        return $this->compliance;
    }

    public function setCompliance(string $compliance): self
    {
        $this->compliance = $compliance;

        return $this;
    }

    public function getLastComplianceUpdate(): ?DateTimeInterface
    {
        return $this->lastComplianceUpdate;
    }

    public function setLastComplianceUpdate(?DateTimeInterface $lastComplianceUpdate): self
    {
        $this->lastComplianceUpdate = $lastComplianceUpdate;

        return $this;
    }

    public function getDataProvider(): string
    {
        return $this->dataProvider;
    }

    public function setDataProvider(string $dataProvider): self
    {
        $this->dataProvider = $dataProvider;
        return $this;
    }

    public function getState(): string
    {
        return $this->state;
    }

    public function setState(string $state): self
    {
        $this->state = $state;

        return $this;
    }

    /**
     * @return Collection<int, Skill>
     */
    public function getSkillSets(): Collection
    {
        return $this->skillSets;
    }

    public function setSkillSets(iterable $skillSets): self
    {
        $this->skillSets->clear();
        foreach ($skillSets as $skillSet) {
            $this->addSkillSet($skillSet);
        }
        return $this;
    }

    public function addSkillSet(Skill $skillSet): self
    {
        if (!$this->skillSets->contains($skillSet)) {
            $this->skillSets[] = $skillSet;
        }

        return $this;
    }

    public function removeSkillSet(Skill $skillSet): self
    {
        $this->skillSets->removeElement($skillSet);

        return $this;
    }
}
