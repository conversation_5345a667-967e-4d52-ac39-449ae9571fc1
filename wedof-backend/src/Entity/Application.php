<?php

namespace App\Entity;

use App\Library\utils\enums\ApplicationStates;
use App\Repository\ApplicationRepository;
use DateTime;
use Doctrine\ORM\Mapping as ORM;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;
use <PERSON><PERSON>\Serializer\Annotation\VirtualProperty;

/**
 * @ORM\Entity(repositoryClass=ApplicationRepository::class)
 * @ORM\Table(name="application",
 *    uniqueConstraints={
 *        @ORM\UniqueConstraint(name="app_unique",
 *            columns={"app_id", "organism_id"})
 *    }
 * )
 * @Serializer\ExclusionPolicy("ALL")
 */
class Application
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Serializer\Expose
     */
    private int $id;

    /**
     * @ORM\Column(type="string", length=255)
     * @Serializer\Expose
     */
    private string $appId;

    /**
     * @ORM\Column(type="string", options={"default" : "disabled"})
     * @Serializer\Expose()
     */
    private string $state = 'disabled';

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Expose()
     */
    private ?DateTime $endDate = null;

    /**
     * @ORM\Column(type="json", nullable=true)
     * @Serializer\Expose
     */
    private ?array $metadata = [];

    /**
     * @ORM\ManyToOne(targetEntity=Organism::class, inversedBy="applications")
     * @ORM\JoinColumn(nullable=false)
     */
    private Organism $organism;

    /**
     * @ORM\Column(type="json", nullable=true)
     */
    private ?array $oauth2 = [];

    /**
     * @ORM\ManyToOne(targetEntity=Company::class, inversedBy="applications")
     */
    private ?Company $company = null;

    public function __construct()
    {
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getAppId(): string
    {
        return $this->appId;
    }

    public function setAppId(string $appId): self
    {
        $this->appId = $appId;

        return $this;
    }

    public function getState(): string
    {
        return $this->state;
    }

    public function setState(string $state): self
    {
        $this->state = $state;

        return $this;
    }

    public function getMetadata(): ?array
    {
        return $this->metadata;
    }

    public function setMetadata(?array $metadata): self
    {
        $this->metadata = $metadata;

        return $this;
    }

    public function getOrganism(): Organism
    {
        return $this->organism;
    }

    public function setOrganism(Organism $organism): self
    {
        $this->organism = $organism;

        return $this;
    }

    public function getOauth2(): array
    {
        return $this->oauth2;
    }

    public function setOauth2(?array $oauth2): self
    {
        $this->oauth2 = $oauth2;

        return $this;
    }

    public function getCompany(): ?Company
    {
        return $this->company;
    }

    public function setCompany(?Company $company): self
    {
        $this->company = $company;

        return $this;
    }

    /**
     * @return DateTime|null
     */
    public function getEndDate(): ?DateTime
    {
        return $this->endDate;
    }

    /**
     * @param DateTime|null $endDate
     */
    public function setEndDate(?DateTime $endDate): void
    {
        $this->endDate = $endDate;
    }

    /**
     * @VirtualProperty()
     * @Serializer\Expose()
     */
    public function getEnabled(): bool
    {
        return in_array($this->getState(), [ApplicationStates::ENABLED()->getValue(),
            ApplicationStates::TRIAL()->getValue(),
            ApplicationStates::PENDING_DISABLE_TRIAL()->getValue(),
            ApplicationStates::PENDING_DISABLE()->getValue()]);
    }
}
