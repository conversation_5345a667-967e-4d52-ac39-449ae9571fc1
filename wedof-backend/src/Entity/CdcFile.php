<?php

namespace App\Entity;

use App\Entity\Traits\TimestampableTrait;
use App\Repository\CdcFileRepository;
use DateTimeInterface;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use J<PERSON>\Serializer\Annotation as Serializer;

/**
 * @ORM\Entity(repositoryClass=CdcFileRepository::class)
 * @ORM\HasLifecycleCallbacks()
 * @Serializer\ExclusionPolicy("ALL")
 */
class CdcFile
{
    use TimestampableTrait;

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Serializer\Expose()
     * @Serializer\Groups({"owner", "Default"})
     */
    private int $id;

    /**
     * @ORM\Column(type="string", length=255, nullable=false)
     * @Serializer\Expose()
     * @Serializer\Groups({"owner", "Default"})
     */
    private string $state;

    /**
     * @ORM\Column(type="string", length=255, nullable=false)
     * @Serializer\Expose()
     * @Serializer\Groups({"owner", "Default"})
     */
    private string $name;

    /**
     * @ORM\Column(type="datetime", nullable=false)
     * @Serializer\Expose()
     * @Serializer\Groups({"owner", "Default"})
     */
    private DateTimeInterface $stateLastUpdate;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"owner", "Default"})
     */
    private ?DateTimeInterface $submissionDate = null;

    /**
     * @ORM\OneToMany(targetEntity=CertificationFoldersCdcFiles::class, mappedBy="cdcFile", orphanRemoval=true)
     */
    private Collection $certificationFoldersCdcFiles;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private ?string $idTraitement = null;

    /**
     * @ORM\ManyToOne(targetEntity=Organism::class)
     * @ORM\JoinColumn(nullable=false)
     */
    private Organism $organism;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private ?string $idFlux = null;

    /**
     * @ORM\Column(type="boolean")
     * @Serializer\Expose()
     * @Serializer\Groups({"owner", "Default"})
     */
    private bool $generatedAutomatically = false;

    /**
     * @ORM\Column(type="boolean", options={"default" : false})
     * @Serializer\Expose()
     * @Serializer\Groups({"owner", "Default"})
     */
    private bool $generatedExternally = false;

    public function __construct()
    {
        $this->certificationFoldersCdcFiles = new ArrayCollection();
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getState(): string
    {
        return $this->state;
    }

    public function setState(string $state): self
    {
        $this->state = $state;

        return $this;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getStateLastUpdate(): DateTimeInterface
    {
        return $this->stateLastUpdate;
    }

    public function setStateLastUpdate(DateTimeInterface $stateLastUpdate): self
    {
        $this->stateLastUpdate = $stateLastUpdate;

        return $this;
    }

    public function getSubmissionDate(): ?DateTimeInterface
    {
        return $this->submissionDate;
    }

    public function setSubmissionDate(?DateTimeInterface $submissionDate): self
    {
        $this->submissionDate = $submissionDate;

        return $this;
    }

    /**
     * @return Collection|CertificationFoldersCdcFiles[]
     */
    public function getCertificationFoldersCdcFiles(): Collection
    {
        return $this->certificationFoldersCdcFiles;
    }

    public function addCertificationFoldersCdcFile(CertificationFoldersCdcFiles $certificationFoldersCdcFile): self
    {
        if (!$this->certificationFoldersCdcFiles->contains($certificationFoldersCdcFile)) {
            $this->certificationFoldersCdcFiles[] = $certificationFoldersCdcFile;
            $certificationFoldersCdcFile->setCdcFile($this);
        }

        return $this;
    }

    public function removeCertificationFoldersCdcFile(CertificationFoldersCdcFiles $certificationFoldersCdcFile): self
    {
        if ($this->certificationFoldersCdcFiles->removeElement($certificationFoldersCdcFile)) {
            // set the owning side to null (unless already changed)
            if ($certificationFoldersCdcFile->getCdcFile() === $this) {
                $certificationFoldersCdcFile->setCdcFile(null);
            }
        }

        return $this;
    }

    public function getIdTraitement(): ?string
    {
        return $this->idTraitement;
    }

    public function setIdTraitement(?string $idTraitement): self
    {
        $this->idTraitement = $idTraitement;

        return $this;
    }

    public function getOrganism(): Organism
    {
        return $this->organism;
    }

    public function setOrganism(Organism $organism): self
    {
        $this->organism = $organism;

        return $this;
    }

    public function getIdFlux(): ?string
    {
        return $this->idFlux;
    }

    public function setIdFlux(?string $idFlux): self
    {
        $this->idFlux = $idFlux;

        return $this;
    }

    public function isGeneratedAutomatically(): ?bool
    {
        return $this->generatedAutomatically;
    }

    public function setGeneratedAutomatically(bool $generatedAutomatically): self
    {
        $this->generatedAutomatically = $generatedAutomatically;

        return $this;
    }

    public function isGeneratedExternally(): ?bool
    {
        return $this->generatedExternally;
    }

    public function setGeneratedExternally(bool $generatedExternally): self
    {
        $this->generatedExternally = $generatedExternally;

        return $this;
    }

}
