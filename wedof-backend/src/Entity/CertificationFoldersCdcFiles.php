<?php

namespace App\Entity;

use App\Repository\CertificationFoldersCdcFilesRepository;
use Doctrine\ORM\Mapping as ORM;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;


/**
 * @ORM\Entity(repositoryClass=CertificationFoldersCdcFilesRepository::class)
 * @Serializer\ExclusionPolicy("ALL")
 */
class CertificationFoldersCdcFiles
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Serializer\Expose()
     * @Serializer\Groups({"owner", "Default"})
     */
    private int $id;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"owner", "Default"})
     */
    private ?string $state = null;

    /**
     * @ORM\ManyToOne(targetEntity=CdcFile::class, inversedBy="certificationFoldersCdcFiles")
     * @ORM\JoinColumn(nullable=false)
     * @Serializer\Expose()
     * @Serializer\Groups({"owner", "Default"})
     */
    private CdcFile $cdcFile;

    /**
     * @ORM\ManyToOne(targetEntity=CertificationFolder::class, inversedBy="certificationFoldersCdcFiles")
     * @ORM\JoinColumn(nullable=false)
     */
    private CertificationFolder $certificationFolder;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"owner", "Default"})
     */
    private ?string $errorMessage = null;

    /**
     * @return int|null
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * @return string|null
     */
    public function getState(): ?string
    {
        return $this->state;
    }

    /**
     * @param string|null $state
     * @return $this
     */
    public function setState(?string $state): self
    {
        $this->state = $state;

        return $this;
    }

    /**
     * @return CdcFile|null
     */
    public function getCdcFile(): ?CdcFile
    {
        return $this->cdcFile;
    }

    /**
     * @param CdcFile $cdcFile
     * @return $this
     */
    public function setCdcFile(CdcFile $cdcFile): self
    {
        $this->cdcFile = $cdcFile;

        return $this;
    }

    /**
     * @return CertificationFolder|null
     */
    public function getCertificationFolder(): ?CertificationFolder
    {
        return $this->certificationFolder;
    }

    /**
     * @param CertificationFolder $certificationFolder
     * @return $this
     */
    public function setCertificationFolder(CertificationFolder $certificationFolder): self
    {
        $this->certificationFolder = $certificationFolder;

        return $this;
    }

    /**
     * @return string|null
     */
    public function getErrorMessage(): ?string
    {
        return $this->errorMessage;
    }

    /**
     * @param string|null $errorMessage
     * @return $this
     */
    public function setErrorMessage(?string $errorMessage): self
    {
        $this->errorMessage = $errorMessage;

        return $this;
    }
}
