<?php

namespace App\Entity;

use App\Entity\Traits\TimestampableTrait;
use App\Library\utils\enums\AttendeeGender;
use App\Library\utils\enums\CertificationFolderAccessModality;
use App\Library\utils\enums\CertificationFolderCdcStates;
use App\Library\utils\enums\CertificationFolderStates;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\enums\PassportType;
use App\Repository\CertificationFolderRepository;
use Beelab\TagBundle\Entity\AbstractTaggable;
use DateTimeInterface;
use DateTimeZone;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Hateoas\Configuration\Annotation as Hateoas;
use JMS\Serializer\Annotation as Serializer;
use Vich\UploaderBundle\Mapping\Annotation as Vich;

/**
 * @implements EntityExportableCSV<CertificationFolder>
 * @Vich\Uploadable
 * @ORM\Entity(repositoryClass=CertificationFolderRepository::class)
 * @ORM\Table(
 *     name="certification_folder",uniqueConstraints={@ORM\UniqueConstraint(name="unique_certificate_id", columns={"certification_id", "certificate_id"})},
 *     indexes={
 *     @ORM\Index(name="cfState_idx",columns={"state"}),
 *     @ORM\Index(name="rfExternalId_idx",columns={"registration_folder_external_id"}),
 *     @ORM\Index(name="stateLastUpdate_idx",columns={"state_last_update"})
 * })
 *
 * @ORM\HasLifecycleCallbacks()
 * @Serializer\ExclusionPolicy("ALL")
 *
 * @Hateoas\Relation("self", href = "expr('/api/certificationFolders/' ~ object.getExternalId())")
 * @Hateoas\Relation("register", href = "expr('/api/certificationFolders/' ~ object.getExternalId() ~ '/register')")
 * @Hateoas\Relation("refuse", href = "expr('/api/certificationFolders/' ~ object.getExternalId() ~ '/refuse')")
 * @Hateoas\Relation("take", href = "expr('/api/certificationFolders/' ~ object.getExternalId() ~ '/take')")
 * @Hateoas\Relation("control", href = "expr('/api/certificationFolders/' ~ object.getExternalId() ~ '/control')")
 * @Hateoas\Relation("retake", href = "expr('/api/certificationFolders/' ~ object.getExternalId() ~ '/retake')")
 * @Hateoas\Relation("fail", href = "expr('/api/certificationFolders/' ~ object.getExternalId() ~ '/fail')")
 * @Hateoas\Relation("success", href = "expr('/api/certificationFolders/' ~ object.getExternalId() ~ '/success')")
 * @Hateoas\Relation("abort", href = "expr('/api/certificationFolders/' ~ object.getExternalId() ~ '/abort')")
 * @Hateoas\Relation("certification", href = "expr('/api/certifications/' ~ object.getCertification().getCertifInfo())", attributes={"name" = "expr(object.getCertification().getName())", "certifInfo" = "expr(object.getCertification().getCertifInfo())", "externalId" = "expr(object.getCertification().getExternalId())", "id" = "expr(object.getCertification().getId())", "enabled" = "expr(object.getCertification().getEnabled())", "allowPartialSkillSets" = "expr(object.getCertification().isAllowPartialSkillSets())"}, exclusion=@Hateoas\Exclusion(groups={"Default", "attendee"}))
 * @Hateoas\Relation("registrationFolder", href = "expr('/api/registrationFolders/' ~ object.getRegistrationFolderExternalId())", attributes={"externalId" = "expr(object.getRegistrationFolderExternalId())", "type" = "expr(object.getDataProvider())", "state" = "expr(object.getRegistrationFolder().getState())"}, exclusion=@Hateoas\Exclusion(excludeIf = "expr(object.getRegistrationFolder() === null)", groups={"Default", "attendee"}))
 * @Hateoas\Relation("partner", href = "expr('/api/organisms/' ~ object.getPartner().getSiret())", attributes={"name" = "expr(object.getPartner().getName())", "siret" = "expr(object.getPartner().getSiret())"}, exclusion=@Hateoas\Exclusion(excludeIf = "expr(object.getPartner() === null)", groups={"Default", "attendee"}))
 * @Hateoas\Relation("certifier", href= "expr('/api/organisms/' ~ object.getCertifier().getSiret())", attributes={"name" = "expr(object.getCertifier().getName())", "siret" = "expr(object.getCertifier().getSiret())"}, exclusion=@Hateoas\Exclusion(groups={"Default", "attendee"}))
 * @Hateoas\Relation("activities", href = "expr('/api/activities/CertificationFolder/' ~ object.getExternalId())")
 * @Hateoas\Relation("survey", href= "expr('/api/surveys/' ~ object.getExternalId() )", exclusion=@Hateoas\Exclusion(groups={"Default", "attendee"}))
 */
class CertificationFolder extends AbstractTaggable implements EntityExportableCSV
{
    use TimestampableTrait;

    public const CLASSNAME = 'CertificationFolder';

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "attendee"})
     */
    private int $id;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "attendee"})
     */
    private ?DateTimeInterface $examinationDate = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "attendee"})
     */
    private ?DateTimeInterface $examinationEndDate = null;

    /**
     * @ORM\Column(type="text", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "attendee"})
     */
    private ?string $examinationPlace = null;

    /**
     * @ORM\Column(type="date", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "attendee"})
     */
    private ?DateTimeInterface $issueDate = null;

    /**
     * @ORM\Column(type="date", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "attendee"})
     */
    private ?DateTimeInterface $expirationDate = null;

    /**
     * @ORM\Column(type="text", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "attendee"})
     */
    private ?string $detailedResult = null;

    /**
     * @ORM\Column(type="text", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "attendee"})
     */
    private ?string $digitalProofLink = null;

    /**
     * @ORM\ManyToOne(targetEntity=Certification::class, inversedBy="certificationFolders")
     * @ORM\JoinColumn(nullable=false)
     */
    private Certification $certification;

    /**
     * @ORM\Column(type="string", length=50)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "attendee"})
     */
    private string $state;

    /**
     * @ORM\OneToMany(targetEntity=CertificationFolderFile::class, mappedBy="certificationFolder", orphanRemoval=true, fetch="EAGER")
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    private Collection $files;

    /**
     * @ORM\Column(type="text", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    private ?string $comment = null;

    /**
     * @ORM\OneToOne(targetEntity=RegistrationFolder::class, mappedBy="certificationFolder", cascade={"persist"})
     * @ORM\JoinColumn(nullable=true)
     */
    private ?RegistrationFolder $registrationFolder = null;

    /**
     * @ORM\OneToOne(targetEntity=CertificationFolderHistory::class, cascade={"persist", "remove"})
     * @ORM\JoinColumn(nullable=false, onDelete="CASCADE")
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "attendee"})
     */
    private CertificationFolderHistory $history;

    /**
     * @ORM\Column(type="datetime")
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "attendee"})
     */
    private DateTimeInterface $stateLastUpdate;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\Attendee", inversedBy="certificationFolders")
     * @ORM\JoinColumn(nullable=false)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    private Attendee $attendee;

    /**
     * @ORM\ManyToOne(targetEntity=Organism::class)
     * @ORM\JoinColumn(nullable=true)
     */
    private ?Organism $partner = null;

    /**
     * @ORM\ManyToMany(targetEntity=Tag::Class)
     * Display with serializer to get as array[string] and not as objet
     * CANNOT BE TYPED /!\
     * Don't forget to override setTagsText() and change updatedOn as done below!
     */
    protected $tags; // Type must not be defined (as in base class '\Beelab\TagBundle\Entity\AbstractTaggable')

    /**
     * @ORM\Column(type="boolean", options={"default" : true})
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    private bool $certifiedData = true;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "attendee"})
     */
    private ?string $examinationType = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "attendee"})
     */
    private ?string $type = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "attendee"})
     */
    private ?string $gradePass = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    private ?string $examinationCenterZipCode = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    private ?string $europeanLanguageLevel = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    private ?string $accessModality = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    private ?string $verbatim = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    private ?string $optionName = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    private ?string $accessModalityVae = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    private ?string $cdcState = null;

    /**
     * @ORM\Column(type="boolean")
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    private bool $cdcToExport = true;

    /**
     * @ORM\Column(type="boolean")
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    private bool $cdcCompliant = false;

    /**
     * @ORM\OneToMany(targetEntity=CertificationFoldersCdcFiles::class, mappedBy="certificationFolder")
     */
    private Collection $certificationFoldersCdcFiles;

    /**
     * @ORM\Column(type="date", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "attendee"})
     */
    private ?DateTimeInterface $enrollmentDate = null;

    /**
     * @ORM\Column(type="float", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    private ?float $amountHt = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true, unique=false)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    private ?string $cdcTechnicalId = null;

    /**
     * @ORM\Column(type="boolean")
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */

    private bool $inTraining = false;

    /**
     * @ORM\Column(type="boolean", options={"default" : false})
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    private bool $cdcExcluded = false;

    /**
     * @ORM\Column(type="string", length=255, nullable=true, unique=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "attendee"})
     */
    private ?string $externalId = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "attendee"})
     */
    private ?string $certificateId = null;

    /**
     * @ORM\Column(type="json", nullable=true)
     */
    private ?array $metadata = [];

    /**
     * @ORM\Column(type="string", length=255, nullable=true, unique=true)
     */
    private ?string $registrationFolderExternalId = null;

    /**
     * @ORM\Column(type="string", length=50, nullable=true)
     */
    private ?string $dataProvider = null;

    /**
     * @ORM\ManyToOne(targetEntity=Organism::class)
     * @ORM\JoinColumn(nullable=false)
     */
    private Organism $certifier;

    /**
     * @ORM\OneToOne(targetEntity=CertificationFolderSurvey::class, mappedBy="certificationFolder", cascade={"persist", "remove"})
     */
    private ?CertificationFolderSurvey $survey = null;

    /**
     * @ORM\Column(type="boolean", options={"default" : false})
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    private bool $tiersTemps = false;

    /**
     * @ORM\ManyToMany(targetEntity=Skill::class)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "attendee"})
     */
    private Collection $skillSets;

    /**
     * @ORM\Column(type="boolean", options={"default" : true})
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "attendee"})
     */
    private bool $fullCertification = true;

    /**
     * @ORM\Column(type="text", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "attendee"})
     */
    private ?string $badgeAssertion = null;

    public function __construct()
    {
        $this->files = new ArrayCollection();
        $this->certificationFoldersCdcFiles = new ArrayCollection();
        $this->skillSets = new ArrayCollection();
        parent::__construct(); // required by tags
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @return DateTimeInterface|null
     */
    public function getExaminationDate(): ?DateTimeInterface
    {
        return $this->examinationDate;
    }

    /**
     * @param DateTimeInterface|null $examinationDate
     * @return $this
     */
    public function setExaminationDate(?DateTimeInterface $examinationDate): self
    {
        $this->examinationDate = $examinationDate;

        return $this;
    }

    /**
     * @return DateTimeInterface|null
     */
    public function getExaminationEndDate(): ?DateTimeInterface
    {
        return $this->examinationEndDate;
    }

    /**
     * @param DateTimeInterface|null $examinationEndDate
     * @return $this
     */
    public function setExaminationEndDate(?DateTimeInterface $examinationEndDate): self
    {
        $this->examinationEndDate = $examinationEndDate;

        return $this;
    }

    /**
     * @return string|null
     */
    public function getExaminationPlace(): ?string
    {
        return $this->examinationPlace;
    }

    /**
     * @param string|null $examinationPlace
     * @return $this
     */
    public function setExaminationPlace(?string $examinationPlace): self
    {
        $this->examinationPlace = $examinationPlace;

        return $this;
    }

    /**
     * @return DateTimeInterface|null
     */
    public function getIssueDate(): ?DateTimeInterface
    {
        return $this->issueDate;
    }

    /**
     * @param DateTimeInterface|null $issueDate
     * @return $this
     */
    public function setIssueDate(?DateTimeInterface $issueDate): self
    {
        $this->issueDate = $issueDate;

        return $this;
    }

    /**
     * @return DateTimeInterface|null
     */
    public function getExpirationDate(): ?DateTimeInterface
    {
        return $this->expirationDate;
    }

    /**
     * @param DateTimeInterface|null $expirationDate
     * @return $this
     */
    public function setExpirationDate(?DateTimeInterface $expirationDate): self
    {
        $this->expirationDate = $expirationDate;

        return $this;
    }

    /**
     * @return string|null
     */
    public function getDetailedResult(): ?string
    {
        return $this->detailedResult;
    }

    /**
     * @param string|null $detailedResult
     * @return $this
     */
    public function setDetailedResult(?string $detailedResult): self
    {
        $this->detailedResult = $detailedResult;

        return $this;
    }

    /**
     * @return string|null
     */
    public function getDigitalProofLink(): ?string
    {
        return $this->digitalProofLink;
    }

    /**
     * @param string|null $digitalProofLink
     * @return $this
     */
    public function setDigitalProofLink(?string $digitalProofLink): self
    {
        $this->digitalProofLink = $digitalProofLink;

        return $this;
    }

    /**
     * @return Certification
     */
    public function getCertification(): Certification
    {
        return $this->certification;
    }

    /**
     * @param Certification $certification
     * @return $this
     */
    public function setCertification(Certification $certification): self
    {
        $this->certification = $certification;

        return $this;
    }

    /**
     * @return string
     */
    public function getState(): string
    {
        return $this->state;
    }

    /**
     * @param string $state
     * @return $this
     */
    public function setState(string $state): self
    {
        $this->state = $state;

        return $this;
    }

    /**
     * @return Collection|CertificationFolderFile[]
     */
    public function getFiles(): Collection
    {
        return $this->files;
    }

    /**
     * @param CertificationFolderFile $file
     * @return $this
     */
    public function addFile(CertificationFolderFile $file): self
    {
        if (!$this->files->contains($file)) {
            $this->files[] = $file;
            $file->setCertificationFolder($this);
        }

        return $this;
    }


    /**
     * @param CertificationFolderFile $file
     * @return $this
     */
    public function removeFile(CertificationFolderFile $file): self
    {
        if ($this->files->removeElement($file)) {
            // set the owning side to null (unless already changed)
            if ($file->getCertificationFolder() === $this) {
                $file->setCertificationFolder(null);
            }
        }

        return $this;
    }

    /**
     * @return string|null
     */
    public function getComment(): ?string
    {
        return $this->comment;
    }

    /**
     * @param string|null $comment
     * @return $this
     */
    public function setComment(?string $comment): self
    {
        $this->comment = $comment;

        return $this;
    }

    /**
     * @return RegistrationFolder|null
     */
    public function getRegistrationFolder(): ?RegistrationFolder
    {
        return $this->registrationFolder;
    }

    /**
     * @param RegistrationFolder|null $registrationFolder
     * @return $this
     */
    public function setRegistrationFolder(?RegistrationFolder $registrationFolder): self
    {
        $this->registrationFolder = $registrationFolder;

        // set (or unset) the owning side of the relation if necessary
        $newCertificationFolder = null === $registrationFolder ? null : $this;
        if ($registrationFolder->getCertificationFolder() !== $newCertificationFolder) {
            $registrationFolder->setCertificationFolder($newCertificationFolder);
        }

        return $this;
    }

    /**
     * @return CertificationFolderHistory
     */
    public function getHistory(): CertificationFolderHistory
    {
        return $this->history;
    }

    /**
     * @param CertificationFolderHistory $certificationFolderHistory
     * @return $this
     */
    public function setHistory(CertificationFolderHistory $certificationFolderHistory): self
    {
        $this->history = $certificationFolderHistory;

        return $this;
    }

    public function getAttendee(): Attendee
    {
        return $this->attendee;
    }

    public function setAttendee(?Attendee $attendee): self
    {
        $this->attendee = $attendee;

        return $this;
    }

    public function getStateLastUpdate(): DateTimeInterface
    {
        return $this->stateLastUpdate;
    }

    public function setStateLastUpdate(DateTimeInterface $stateLastUpdate): self
    {
        $this->stateLastUpdate = $stateLastUpdate;

        return $this;
    }

    public function getPartner(): ?Organism
    {
        return $this->partner;
    }

    public function setPartner(?Organism $partner): self
    {
        $this->partner = $partner;

        return $this;
    }

    // Required on every taggable so that if only tags are changed, they are persisted
    public function setTagsText(?string $tagsText): void
    {
        if ($tagsText !== $this->getTagsText()) {
            $this->updateUpdatedOnTimestamp();
        }
        parent::setTagsText($tagsText);
    }

    /**
     * @param CertificationFolder $entity
     * @param array $columns
     * @return array
     */
    public static function getCSVFormat($entity, array $columns): array
    {
        $attendee = $entity->getAttendee();
        $certification = $entity->getCertification();
        $fullRow = [
            'CERTIFICATION' => $certification->getExternalId() . " " . $certification->getName(),
            'OBTENTION' => $certification->getObtentionSystem(),
            'ORGANISME' => $entity->getPartner() ? $entity->getPartner()->getName() : null,
            'ORGANISME_SIRET' => $entity->getPartner() ? $entity->getPartner()->getSiret() : null,
            'NUMERO_DOSSIER' => $entity->getExternalId(),
            'SEXE' => $attendee->getGender() === AttendeeGender::MALE()->getValue() ? 'M' : ($attendee->getGender() === AttendeeGender::FEMALE()->getValue() ? "F" : null),
            'CANDIDAT' => $attendee->getLastName() . " " . $attendee->getFirstName(),
            'EMAIL' => $attendee->getEmail(),
            'TELEPHONE' => $attendee->getPhoneNumber() ?? $attendee->getPhoneFixed(),
            'STATUT' => CertificationFolderStates::toFrString($entity->getState()),
            'STATUT_ACCROCHAGE' => CertificationFolderCdcStates::toFrString($entity->getCdcState()),
            'DATE_INSCRIPTION' => $entity->getEnrollmentDate() ? $entity->getEnrollmentDate()->format('d/m/Y') : null,
            'EXAMINATION_PLACE' => $entity->getExaminationPlace(),
            'EXAMINATION_DATE' => $entity->getExaminationDate() ? $entity->getExaminationDate()->format('d/m/Y') : null,
            'EXPIRATION_DATE' => $entity->getExpirationDate() ? $entity->getExpirationDate()->format('d/m/Y') : null,
            'COMMENTAIRE' => $entity->getComment(),
            'PREUVE_NUMERIQUE' => $entity->getDigitalProofLink(),
            'RESULTAT' => $entity->getDetailedResult(),
            'OPTION' => $entity->getOptionName(),
            'EXAMINATION_TYPE' => $entity->getExaminationType(),
            'EXAMINATION_CENTER_CODE_POSTAL' => $entity->getExaminationCenterZipCode(),
            'NIVEAU_LANGUE_EUROPEEN' => $entity->getEuropeanLanguageLevel(),
            'MENTION' => $entity->getGradePass(),
            'VERBATIM' => $entity->getVerbatim(),
            'MODALITE_ACCESS' => $entity->getAccessModality(),
            'MODALITE_ACCESS_VAE' => $entity->getAccessModalityVae(),
            'TYPE' => $entity->getType(),
            'DOSSIER_COMPLET' => $entity->isCdcCompliant() ? "Oui" : "Non",
            'STATUT_EXPORTABILITE' => $entity->isCdcToExport() ? 'toExport' : 'doNotExport',
            'DATE_DE_NAISSANCE' => $attendee->getDateOfBirth() ? $attendee->getDateOfBirth()->format('d/m/Y') : null,
            'CODE_INSEE_VILLE_NAISSANCE' => $attendee->getCodeCityOfBirth(),
            'VILLE_NAISSANCE' => $attendee->getNameCityOfBirth(),
            'CODE_PAYS_NAISSANCE' => $attendee->getCodeCountryOfBirth(),
            'PAYS_NAISSANCE' => $attendee->getNameCountryOfBirth(),
            'ESPACE_CANDIDAT' => $entity->getAttendeeLink()
        ];
        $row = [];
        foreach ($columns as $column) {
            $row[] = $fullRow[$column];
        }
        return $row;
    }

    public function isCertifiedData(): ?bool
    {
        return $this->certifiedData;
    }

    public function setCertifiedData(?bool $certifiedData): self
    {
        $this->certifiedData = $certifiedData;

        return $this;
    }

    public function getExaminationType(): ?string
    {
        return $this->examinationType;
    }

    public function setExaminationType(?string $examinationType): self
    {
        $this->examinationType = $examinationType;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(?string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getGradePass(): ?string
    {
        return $this->gradePass;
    }

    public function setGradePass(?string $gradePass): self
    {
        $this->gradePass = $gradePass;

        return $this;
    }

    public function getExaminationCenterZipCode(): ?string
    {
        return $this->examinationCenterZipCode;
    }

    public function setExaminationCenterZipCode(?string $examinationCenterZipCode): self
    {
        $this->examinationCenterZipCode = $examinationCenterZipCode;

        return $this;
    }

    public function getEuropeanLanguageLevel(): ?string
    {
        return $this->europeanLanguageLevel;
    }

    public function setEuropeanLanguageLevel(?string $europeanLanguageLevel): self
    {
        $this->europeanLanguageLevel = $europeanLanguageLevel;

        return $this;
    }

    public function getAccessModality(): ?string
    {
        return $this->accessModality;
    }

    public function setAccessModality(?string $accessModality): self
    {
        $this->accessModality = $accessModality;

        return $this;
    }

    public function getVerbatim(): ?string
    {
        return $this->verbatim;
    }

    public function setVerbatim(?string $verbatim): self
    {
        $this->verbatim = $verbatim;

        return $this;
    }

    public function getOptionName(): ?string
    {
        return $this->optionName;
    }

    public function setOptionName(?string $optionName): self
    {
        $this->optionName = $optionName;

        return $this;
    }

    public function getAccessModalityVae(): ?string
    {
        return $this->accessModalityVae;
    }

    public function setAccessModalityVae(?string $accessModalityVae): self
    {
        $this->accessModalityVae = $accessModalityVae;

        return $this;
    }

    public function getCdcState(): ?string
    {
        return $this->cdcState;
    }

    public function setCdcState(?string $cdcState): self
    {
        $this->cdcState = $cdcState;

        return $this;
    }

    public function isCdcToExport(): bool
    {
        return $this->cdcToExport;
    }

    public function setCdcToExport(bool $cdcToExport): self
    {
        $this->cdcToExport = $cdcToExport;

        return $this;
    }

    public function isCdcCompliant(): bool
    {
        return $this->cdcCompliant;
    }

    public function getMetadata(): ?array
    {
        return $this->metadata;
    }

    public function setMetadata(?array $metadata): self
    {
        $this->metadata = $metadata;

        return $this;
    }


    public function refreshCdcCompliant(): self
    {
        $attendee = $this->getAttendee();
        if ($this->getAccessModality() === CertificationFolderAccessModality::VAE()->getValue() && !$this->getAccessModalityVae()) {
            $this->cdcCompliant = false;
        } else if (!$this->getAccessModality() && $this->getAccessModalityVae()) {
            $this->cdcCompliant = false;
        } else if ((!$this->getRegistrationFolder() || $this->getDataProvider() !== DataProviders::CPF()->getValue()) && !$attendee->isCdcCompliant()) {
            $this->cdcCompliant = false;
        } else {
            $this->cdcCompliant = true;
        }
        return $this;
    }

    /**
     * @return Collection|CertificationFoldersCdcFiles[]
     */
    public function getCertificationFoldersCdcFiles(): Collection
    {
        return $this->certificationFoldersCdcFiles;
    }

    public function addCertificationFoldersCdcFile(CertificationFoldersCdcFiles $certificationFoldersCdcFile): self
    {
        if (!$this->certificationFoldersCdcFiles->contains($certificationFoldersCdcFile)) {
            $this->certificationFoldersCdcFiles[] = $certificationFoldersCdcFile;
            $certificationFoldersCdcFile->setCertificationFolder($this);
        }

        return $this;
    }

    public function removeCertificationFoldersCdcFile(CertificationFoldersCdcFiles $certificationFoldersCdcFile): self
    {
        if ($this->certificationFoldersCdcFiles->removeElement($certificationFoldersCdcFile)) {
            // set the owning side to null (unless already changed)
            if ($certificationFoldersCdcFile->getCertificationFolder() === $this) {
                $certificationFoldersCdcFile->setCertificationFolder(null);
            }
        }

        return $this;
    }

    /**
     * @return DateTimeInterface|null
     */
    public function getEnrollmentDate(): ?DateTimeInterface
    {
        return $this->enrollmentDate;
    }

    /**
     * @param DateTimeInterface|null $enrollmentDate
     * @return $this
     */
    public function setEnrollmentDate(?DateTimeInterface $enrollmentDate): self
    {
        $this->enrollmentDate = $enrollmentDate;

        return $this;
    }

    public function getAmountHt(): ?float
    {
        return $this->amountHt;
    }

    public function setAmountHt(?float $amountHt): self
    {
        $this->amountHt = $amountHt;

        return $this;
    }

    public function getCdcTechnicalId(): ?string
    {
        return $this->cdcTechnicalId;
    }

    public function setCdcTechnicalId(?string $cdcTechnicalId): self
    {
        $this->cdcTechnicalId = $cdcTechnicalId;

        return $this;
    }

    public function isInTraining(): ?bool
    {
        return $this->inTraining;
    }

    public function setInTraining(bool $inTraining): self
    {
        $this->inTraining = $inTraining;

        return $this;
    }

    public function isCdcExcluded(): bool
    {
        return $this->cdcExcluded;
    }

    public function setCdcExcluded(bool $cdcExcluded): self
    {
        $this->cdcExcluded = $cdcExcluded;

        return $this;
    }

    public function getExternalId(): ?string
    {
        return $this->externalId;
    }

    public function setExternalId(?string $externalId): self
    {
        $this->externalId = $externalId;

        return $this;
    }

    public function getCertificateId(): ?string
    {
        return $this->certificateId;
    }

    public function setCertificateId(?string $certificateId): self
    {
        $this->certificateId = $certificateId;

        return $this;
    }

    /**
     * @Serializer\VirtualProperty()
     * @Serializer\SerializedName("attendeeLink")
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "attendee"})
     */
    public function getAttendeeLink(): string
    {
        // This link works only in prod with nginx, real target links are:
        // https://<subdomain>.wedof.fr/candidat/certification/dossier/<externalId>
        // https://<subdomain>.wedof.fr/candidat/certification/dossier/<externalId>/files/<fileId>
        $subdomain = !empty($this->getCertifier()->getSubDomain()) ? ($this->getCertifier()->getSubDomain() . '.') : "";
        $port = isset($_ENV['PORT']) && !in_array($_ENV['PORT'], ['80', '443']) ? (':' . $_ENV['PORT']) : '';
        return 'https://' . $subdomain . $_ENV['DOMAIN'] . $port . '/candidat-' . $this->getExternalId();
    }

    /**
     * @Serializer\VirtualProperty()
     * @Serializer\SerializedName("surveyLink")
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "attendee"})
     */
    public function getSurveyLink(): string
    {
        // This link works only in prod with nginx, real target links are:
        // https://<subdomain>.wedof.fr/candidat/certification/dossier/<externalId>/enquete
        $subdomain = !empty($this->getCertifier()->getSubDomain()) ? ($this->getCertifier()->getSubDomain() . '.') : "";
        $port = isset($_ENV['PORT']) && !in_array($_ENV['PORT'], ['80', '443']) ? (':' . $_ENV['PORT']) : '';
        return 'https://' . $subdomain . $_ENV['DOMAIN'] . $port . '/candidat-' . $this->getExternalId() . '-enquete';
    }

    public function getEntityId(): string
    {
        return $this->getId();
    }

    public function getRegistrationFolderExternalId(): ?string
    {
        return $this->registrationFolderExternalId;
    }

    public function setRegistrationFolderExternalId(?string $registrationFolderExternalId): self
    {
        $this->registrationFolderExternalId = $registrationFolderExternalId;

        return $this;
    }

    public function getDataProvider(): ?string
    {
        return $this->dataProvider;
    }

    public function setDataProvider(?string $dataProvider): self
    {
        $this->dataProvider = $dataProvider;

        return $this;
    }

    public function getCertifier(): Organism
    {
        return $this->certifier;
    }

    public function setCertifier(Organism $certifier): self
    {
        $this->certifier = $certifier;

        return $this;
    }

    /**
     * @Serializer\VirtualProperty()
     * @Serializer\SerializedName("permalink")
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    public function getPermalink(): string
    {
        // This link works only in prod with nginx
        $subdomain = !empty($this->getCertifier()->getSubDomain()) ? ($this->getCertifier()->getSubDomain() . '.') : "";
        $port = isset($_ENV['PORT']) && !in_array($_ENV['PORT'], ['80', '443']) ? (':' . $_ENV['PORT']) : '';
        return 'https://' . $subdomain . $_ENV['DOMAIN'] . $port . '/dossier-certification-' . $this->getExternalId();
    }

    /**
     * @Serializer\VirtualProperty()
     * @Serializer\SerializedName("addToPassportLink")
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    public function getAddToPassportLink(): string
    {
        // This link works only in prod with nginx, real target links is:
        // https://<subdomain>.wedof.fr/candidat/certification/dossier/<externalId>/passeport
        $subdomain = !empty($this->getCertifier()->getSubDomain()) ? ($this->getCertifier()->getSubDomain() . '.') : "";
        $port = isset($_ENV['PORT']) && !in_array($_ENV['PORT'], ['80', '443']) ? (':' . $_ENV['PORT']) : '';
        return 'https://' . $subdomain . $_ENV['DOMAIN'] . $port . '/candidat-' . $this->getExternalId() . "-passeport";
    }

    public function getSurvey(): ?CertificationFolderSurvey
    {
        return $this->survey;
    }

    public function setSurvey(?CertificationFolderSurvey $survey): self
    {
        // unset the owning side of the relation if necessary
        if ($survey === null && $this->survey !== null) {
            $this->survey->setCertificationFolder(null);
        }

        // set the owning side of the relation if necessary
        if ($survey !== null && $survey->getCertificationFolder() !== $this) {
            $survey->setCertificationFolder($this);
        }

        $this->survey = $survey;

        return $this;
    }

    public function isTiersTemps(): ?bool
    {
        return $this->tiersTemps;
    }

    public function setTiersTemps(?bool $tiersTemps): self
    {
        $this->tiersTemps = $tiersTemps;

        return $this;
    }

    /**
     * @return string|null
     */
    public function getExaminationTime(): ?string
    {
        $examinationDate = $this->getExaminationDate();
        return isset($examinationDate) ? $examinationDate->setTimezone(new DateTimeZone('Europe/Paris'))->format('H\hi') : null;
    }

    /**
     * @return string|null
     */
    public function getExaminationEndTime(): ?string
    {
        $examinationEndDate = $this->getExaminationEndDate();
        return isset($examinationEndDate) ? $examinationEndDate->setTimezone(new DateTimeZone('Europe/Paris'))->format('H\hi') : null;
    }

    /**
     * @return Collection<int, Skill>
     */
    public function getSkillSets(): Collection
    {
        return $this->skillSets;
    }

    public function setSkillSets(iterable $skillSets): self
    {
        $this->skillSets->clear();
        foreach ($skillSets as $skillSet) {
            $this->addSkillSet($skillSet);
        }
        return $this;
    }

    public function addSkillSet(Skill $skillSet): self
    {
        if (!$this->skillSets->contains($skillSet)) {
            $this->skillSets[] = $skillSet;
        }

        return $this;
    }

    public function removeSkillSet(Skill $skillSet): self
    {
        $this->skillSets->removeElement($skillSet);

        return $this;
    }

    public function isFullCertification(): bool
    {
        return $this->fullCertification;
    }

    public function setFullCertification(bool $fullCertification): self
    {
        $this->fullCertification = $fullCertification;

        return $this;
    }

    public function isAllSkillSets(): bool
    {
        $certification = $this->getCertification();
        return !$certification->isAllowPartialSkillSets() || $certification->getSkillSets()->count() === $this->getSkillSets()->count();
    }

    public function getSkillSetsName(): string
    {
        if ($this->certification->isAllowPartialSkillSets() && $this->certification->getSkills()->count() > 0) {
            $skillSetsName = [];
            $skillSets = $this->getSkillSets()->count() > 0 ? $this->getSkillSets() : $this->certification->getSkillSets();
            /** @var Skill $skillSet */
            foreach ($skillSets as $skillSet) {
                $code = "BC" . sprintf('%02d', $skillSet->getOrder());
                $skillSetsName[] = $code . " " . $skillSet->getLabel();
            }
            return implode(", ", $skillSetsName);
        } else {
            return '';
        }
    }

    /**
     * @return string|null
     */
    public function getBadgeAssertion(): ?string
    {
        return $this->badgeAssertion;
    }

    /**
     * @param string|null $badgeAssertion
     * @return $this
     */
    public function setBadgeAssertion(?string $badgeAssertion): self
    {
        $this->badgeAssertion = $badgeAssertion;

        return $this;
    }

    /**
     * @Serializer\VirtualProperty()
     * @Serializer\SerializedName("passportType")
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "attendee"})
     */
    public function getPassportType(): string
    {
        return PassportType::COMPETENCES()->getValue();
    }
}
