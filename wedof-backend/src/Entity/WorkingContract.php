<?php

namespace App\Entity;

use App\Entity\Traits\TimestampableTrait;
use App\Repository\WorkingContractRepository;
use DateTimeInterface;
use Doctrine\ORM\Mapping as ORM;
use JMS\Serializer\Annotation as Serializer;
use Hateoas\Configuration\Annotation as Hateoas;

/**
 * @ORM\Entity(repositoryClass=WorkingContractRepository::class)
 * @ORM\Table(indexes={
 *      @ORM\Index(name="financerId_idx",columns={"external_id_financer", "financer"}),
 * })
 * @ORM\HasLifecycleCallbacks()
 * @Serializer\ExclusionPolicy("ALL")
 * @Hateoas\Relation("self", href = "expr('/api/workingContracts/' ~ object.getId())"))
 * @Hateoas\Relation("registrationFolder", href = "expr('/api/registrationFolders/' ~ object.getRegistrationFolder().getExternalId())", attributes={"externalId" = "expr(object.getRegistrationFolder().getExternalId())"}))
 * @Hateoas\Relation("employer", href = "expr('/api/organisms/' ~ object.getEmployer().getSiret())", attributes={"siret" = "expr(object.getEmployer().getSiret())", "name" = "expr(object.getEmployer().getName())"}, exclusion=@Hateoas\Exclusion(excludeIf = "expr(object.getEmployer() === null)"))
 */
class WorkingContract
{
    use TimestampableTrait;

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Serializer\Expose
     */
    private $id;
    /**
     * @ORM\Column(type="string", length=100)
     */
    private string $externalIdFinancer;

    /**
     * @ORM\Column(type="string", length=100, nullable=true)
     * @Serializer\Expose
     */
    private ?string $externalIdTrainingOrganism = null;

    /**
     * @ORM\Column(type="string", length=100, nullable=true)
     * @Serializer\Expose
     */
    private ?string $externalIdDeca = null;

    /**
     * @ORM\ManyToOne(targetEntity=RegistrationFolder::class)
     */
    private RegistrationFolder $registrationFolder;

    /**
     * @ORM\Column(type="string", length=100)
     * @Serializer\Expose
     */
    private string $financer;

    /**
     * @ORM\Column(type="string", length=100)
     * @Serializer\Expose
     */
    private string $state;

    /**
     * @ORM\Column(type="string", length=3)
     * @Serializer\Expose
     */
    private string $type;

    /**
     * @ORM\Column(type="float", nullable=true)
     * @Serializer\Expose
     */
    private ?float $amount = null;

    /**
     * @ORM\Column(type="date")
     * @Serializer\Expose
     */
    private DateTimeInterface $startDate;

    /**
     * @ORM\Column(type="date")
     * @Serializer\Expose
     */
    private DateTimeInterface $endDate;

    /**
     * @ORM\Column(type="date")
     * @Serializer\Expose
     */
    private DateTimeInterface $signedDate;

    /**
     * @ORM\Column(type="date", nullable=true)
     * @Serializer\Expose
     */
    private ?DateTimeInterface $amendmentDate = null;

    /**
     * @ORM\Column(type="date", nullable=true)
     * @Serializer\Expose
     */
    private ?DateTimeInterface $breakingDate = null;

    /**
     * @ORM\Column(type="json")
     */
    private array $rawData = [];

    /**
     * @ORM\Column(type="date")
     * @Serializer\Expose
     */
    private DateTimeInterface $lastUpdate;

    /**
     * @ORM\ManyToOne(targetEntity=Organism::class)
     * @ORM\JoinColumn(nullable=true)
     */
    private ?Organism $employer = null;


    public function getId(): ?int
    {
        return $this->id;
    }

    public function getExternalIdFinancer(): ?string
    {
        return $this->externalIdFinancer;
    }

    public function setExternalIdFinancer(string $externalIdFinancer): self
    {
        $this->externalIdFinancer = $externalIdFinancer;

        return $this;
    }

    public function getExternalIdTrainingOrganism(): ?string
    {
        return $this->externalIdTrainingOrganism;
    }

    public function setExternalIdTrainingOrganism(?string $externalIdTrainingOrganism): self
    {
        $this->externalIdTrainingOrganism = $externalIdTrainingOrganism;

        return $this;
    }

    public function getExternalIdDeca(): ?string
    {
        return $this->externalIdDeca;
    }

    public function setExternalIdDeca(?string $externalIdDeca): self
    {
        $this->externalIdDeca = $externalIdDeca;

        return $this;
    }

    public function getRegistrationFolder(): RegistrationFolder
    {
        return $this->registrationFolder;
    }

    public function setRegistrationFolder(RegistrationFolder $registrationFolder): self
    {
        $this->registrationFolder = $registrationFolder;

        return $this;
    }

    public function getFinancer(): ?string
    {
        return $this->financer;
    }

    public function setFinancer(string $financer): self
    {
        $this->financer = $financer;

        return $this;
    }

    public function getState(): ?string
    {
        return $this->state;
    }

    public function setState(string $state): self
    {
        $this->state = $state;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getAmount(): ?float
    {
        return $this->amount;
    }

    public function setAmount(?float $amount): self
    {
        $this->amount = $amount;

        return $this;
    }

    public function getStartDate(): ?DateTimeInterface
    {
        return $this->startDate;
    }

    public function setStartDate(DateTimeInterface $startDate): self
    {
        $this->startDate = $startDate;

        return $this;
    }

    public function getEndDate(): ?DateTimeInterface
    {
        return $this->endDate;
    }

    public function setEndDate(DateTimeInterface $endDate): self
    {
        $this->endDate = $endDate;

        return $this;
    }

    public function getSignedDate(): ?DateTimeInterface
    {
        return $this->signedDate;
    }

    public function setSignedDate(DateTimeInterface $signedDate): self
    {
        $this->signedDate = $signedDate;

        return $this;
    }

    public function getAmendmentDate(): ?DateTimeInterface
    {
        return $this->amendmentDate;
    }

    public function setAmendmentDate(?DateTimeInterface $amendmentDate): self
    {
        $this->amendmentDate = $amendmentDate;

        return $this;
    }

    public function getBreakingDate(): ?DateTimeInterface
    {
        return $this->breakingDate;
    }

    public function setBreakingDate(?DateTimeInterface $breakingDate): self
    {
        $this->breakingDate = $breakingDate;

        return $this;
    }

    public function getRawData(): ?array
    {
        return $this->rawData;
    }

    public function setRawData(array $rawData): self
    {
        $this->rawData = $rawData;

        return $this;
    }

    public function getLastUpdate(): ?DateTimeInterface
    {
        return $this->lastUpdate;
    }

    public function setLastUpdate(DateTimeInterface $lastUpdate): self
    {
        $this->lastUpdate = $lastUpdate;

        return $this;
    }

    public function getEmployer(): ?Organism
    {
        return $this->employer;
    }

    public function setEmployer(?Organism $employer): self
    {
        $this->employer = $employer;

        return $this;
    }
}
