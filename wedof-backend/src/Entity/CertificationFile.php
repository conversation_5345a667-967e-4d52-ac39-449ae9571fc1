<?php

namespace App\Entity;

use App\Entity\Traits\TimestampableTrait;
use App\Repository\CertificationFileRepository;
use Doctrine\ORM\Mapping as ORM;
use Hateoas\Configuration\Annotation as Hateoas;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;
use Symfony\Component\HttpFoundation\File\File;
use Vich\UploaderBundle\Mapping\Annotation as Vich;


/**
 * @ORM\Entity(repositoryClass=CertificationFileRepository::class)
 * @ORM\HasLifecycleCallbacks()
 * @Serializer\ExclusionPolicy("ALL")
 * @Vich\Uploadable
 *
 * @Hateoas\Relation("certification", href = "expr('/api/certifications/' ~ object.getCertification().getId())", exclusion=@Hateoas\Exclusion(groups={"owner", "Default"}))
 */
class CertificationFile
{

    use TimestampableTrait;

    const CLASSNAME = 'CertificationFile';

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Serializer\Expose
     * @Serializer\Groups({"owner", "Default"})
     */
    private int $id;

    /**
     * @ORM\Column(type="integer")
     * @Serializer\Expose
     * @Serializer\Groups({"owner", "Default"})
     */
    private string $typeId;

    /**
     * @Vich\UploadableField(mapping="certificationFile", fileNameProperty="filePath", originalName="fileName", mimeType="fileType")
     * @var File|null
     * @Serializer\Groups({"owner", "Default"})
     */
    public ?File $file = null;

    /**
     * @ORM\Column(type="string", length=255)
     * @Serializer\Expose
     * @Serializer\Groups({"owner", "Default"})
     */
    private string $fileName;

    /**
     * @ORM\Column(type="string", length=255)
     * @Serializer\Groups({"owner", "Default"})
     */
    private string $filePath;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Groups({"owner", "Default"})
     * @Serializer\Expose
     */
    private ?string $link = null;

    /**
     * @ORM\Column(type="string", length=255)
     * @Serializer\Groups({"owner", "Default"})
     * @Serializer\Expose
     */
    private string $fileType;

    /**
     * @ORM\ManyToOne(targetEntity=Certification::class, inversedBy="files")
     * @ORM\JoinColumn(nullable=false)
     */
    private ?Certification $certification;

    /**
     * @ORM\Column(type="string", length=255)
     * @Serializer\Groups({"owner", "Default"})
     * @Serializer\Expose
     */
    private string $state;

    /**
     * @ORM\Column(type="text", nullable=true)
     * @Serializer\Expose
     * @Serializer\Groups({"owner", "Default"})
     */
    private ?string $comment = null;

    /**
     * @ORM\Column(type="string", length=30, options={"default" : "notGenerated"})
     * @Serializer\Expose()
     * @Serializer\Groups({"owner", "Default"})
     */
    private string $generationState = 'notGenerated';

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @return string|null
     */
    public function getLink(): ?string
    {
        return $this->link;
    }

    /**
     * @param string|null $link
     * @return $this
     */
    public function setLink(?string $link): self
    {
        $this->link = $link;

        return $this;
    }

    /**
     * @return string
     */
    public function getFilePath(): string
    {
        return $this->filePath;
    }

    /**
     * @param string|null $filePath
     * @return $this
     */
    public function setFilePath(?string $filePath): self
    {
        $this->filePath = (string)$filePath; //https://github.com/dustin10/VichUploaderBundle/issues/1117

        return $this;
    }

    /**
     * @return string
     */
    public function getFileName(): string
    {
        return $this->fileName;
    }

    /**
     * @param string|null $fileName
     * @return $this
     */
    public function setFileName(?string $fileName): self
    {
        $this->fileName = (string)$fileName; //https://github.com/dustin10/VichUploaderBundle/issues/1117

        return $this;
    }

    /**
     * @return string
     */
    public function getFileType(): string
    {
        return $this->fileType;
    }

    /**
     * @param string|null $fileType
     * @return $this
     */
    public function setFileType(?string $fileType): self
    {
        $this->fileType = (string)$fileType;//https://github.com/dustin10/VichUploaderBundle/issues/1117

        return $this;
    }

    /**
     * @param File|null $file
     */
    public function setFile(?File $file = null): void
    {
        $this->file = $file;

        if (null !== $file) {
            // It is required that at least one field changes if you are using doctrine
            // otherwise the event listeners won't be called and the file is lost
            $this->updateUpdatedOnTimestamp();
        }
    }

    /**
     * @return Certification|null
     */
    public function getCertification(): ?Certification
    {
        return $this->certification;
    }

    /**
     * @param Certification|null $certification
     * @return $this
     */
    public function setCertification(?Certification $certification): self
    {
        $this->certification = $certification;

        return $this;
    }

    public function getTypeId(): int
    {
        return $this->typeId;
    }

    public function setTypeId(int $typeId): self
    {
        $this->typeId = $typeId;

        return $this;
    }

    public function getState(): string
    {
        return $this->state;
    }

    public function setState(string $state): self
    {
        $this->state = $state;

        return $this;
    }

    public function getComment(): ?string
    {
        return $this->comment;
    }

    public function setComment(?string $comment): self
    {
        $this->comment = $comment;

        return $this;
    }

    public function getEntityId(): string
    {
        return $this->getId();
    }

    public function getGenerationState(): string
    {
        return $this->generationState;
    }

    public function setGenerationState(string $generationState): self
    {
        $this->generationState = $generationState;

        return $this;
    }
}
