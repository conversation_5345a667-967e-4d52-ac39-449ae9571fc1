<?php

namespace App\Entity;

use App\Entity\Traits\TimestampableTrait;
use App\Library\utils\enums\CertificationSkillType;
use App\Library\utils\Tools;
use App\Repository\CertificationRepository;
use DateTime;
use DateTimeInterface;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Hateoas\Configuration\Annotation as Hateoas;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;
use J<PERSON>\Serializer\Annotation\VirtualProperty;
use Symfony\Component\HttpFoundation\File\File;
use Vich\UploaderBundle\Mapping\Annotation as Vich;

/**
 * @ORM\Entity(repositoryClass=CertificationRepository::class)
 * @Vich\Uploadable
 * @ORM\Table(indexes={@ORM\Index(name="certif_info_idx",columns={"certif_info"}), @ORM\Index(name="type_code_idx",columns={"type", "code"})})
 * @ORM\HasLifecycleCallbacks()
 *
 * @Serializer\ExclusionPolicy("ALL")
 * @Hateoas\Relation("self", href = "expr('/api/certifications/' ~ object.getCertifInfo())", exclusion=@Hateoas\Exclusion(groups={"Default", "partnership"}))
 * @Hateoas\Relation("certificationPartners", href = "expr('/api/certifications/' ~ object.getCertifInfo() ~ '/partners')", exclusion=@Hateoas\Exclusion(groups={"Default"}))
 * @Hateoas\Relation("partners", href = "expr('/api/organisms?organismType=partner&certifInfo=' ~ object.getCertifInfo())", exclusion=@Hateoas\Exclusion(groups={"Default"}))
 * @Hateoas\Relation("certifiers", href = "expr('/api/organisms?organismType=certifier&certifInfo=' ~ object.getCertifInfo())", exclusion=@Hateoas\Exclusion(groups={"Default", "partnership"}))
 * @Hateoas\Relation("defaultCertifier", href = "expr('/api/organisms/' ~ object.getDefaultCertifier().getSiret())", attributes={"name" = "expr(object.getDefaultCertifier().getName())", "siret" = "expr(object.getDefaultCertifier().getSiret())","hasOwner" = "expr(object.getDefaultCertifier().getOwnedBy() !== null)"}, exclusion=@Hateoas\Exclusion(excludeIf = "expr(object.getDefaultCertifier() === null)", groups={"Default", "partnership", "attendee"}))
 * @Hateoas\Relation("skills", href = "expr('/api/skills?certifInfo=' ~ object.getCertifInfo())", exclusion=@Hateoas\Exclusion(groups={"Default"}))
 *
 */
class Certification
{
    use TimestampableTrait;

    public const CLASSNAME = 'Certification';
    public const CERTIFICATE_FILE_TYPE_ID = 1001;
    public const CERTIFICATE_FILE_TYPE_NAME = "Parchemin";

    /**
     * @ORM\Id()
     * @ORM\GeneratedValue()
     * @ORM\Column(type="integer")
     * @Serializer\Expose
     * @Serializer\Groups({"public", "Default", "attendee"})
     */
    private int $id;

    /**
     * @ORM\Column(type="text")
     * @Serializer\Expose
     * @Serializer\Groups({"public", "Default", "partnership", "attendee", "lite"})
     */
    private string $name;

    /**
     * @ORM\Column(type="text")
     * @Serializer\Expose
     * @Serializer\Groups({"Default", "partnership"})
     */
    private string $level;

    /**
     * @ORM\Column(type="text", nullable=true)
     * @Serializer\Expose
     * @Serializer\Groups({"Default", "partnership"})
     */
    private ?string $descriptif = null;

    /**
     * @ORM\Column(type="text", nullable=true)
     * @Serializer\Expose
     * @Serializer\Groups({"Default", "partnership"})
     */
    private ?string $objectif = null;

    /**
     * @ORM\Column(type="text", nullable=true)
     */
    private ?string $moreInformationsLink = null;

    /**
     * @ORM\Column(type="array", nullable=true)
     * @Serializer\Expose
     * @Serializer\Groups({"Default", "partnership", "attendee"})
     */
    private ?array $domains = [];

    /**
     * @ORM\Column(type="array", nullable=true)
     * @Serializer\Expose
     * @Serializer\Groups({"Default", "partnership"})
     */
    private ?array $rome = [];

    /**
     * @ORM\Column(type="array", nullable=true)
     * @Serializer\Expose
     * @Serializer\Groups({"Default"})
     */
    private ?array $gfe = [];

    /**
     * @ORM\Column(type="array", nullable=true)
     * @Serializer\Expose
     * @Serializer\Groups({"Default"})
     */
    private ?array $nsf = [];

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Expose
     * @Serializer\Groups({"Default"})
     */
    private ?string $cpf = null;

    /**
     * @ORM\Column(type="date", nullable=true)
     * @Serializer\Expose
     * @Serializer\Groups({"Default"})
     */
    private ?DateTimeInterface $cpfDateStart = null;

    /**
     * @ORM\Column(type="date", nullable=true)
     * @Serializer\Expose
     * @Serializer\Groups({"Default", "partnership"})
     */
    private ?DateTimeInterface $cpfDateEnd = null;

    /**
     * @ORM\Column(type="string", length=255, unique=true)
     * @Serializer\Expose
     * @Serializer\Groups({"public", "Default", "partnership", "attendee", "lite"})
     */
    private ?string $certifInfo = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Expose
     * @Serializer\Groups({"Default"})
     */
    private ?DateTimeInterface $lastExternalUpdate = null;

    /**
     * @ORM\Column(type="boolean")
     * @Serializer\Expose
     * @Serializer\Groups({"Default", "lite", "partnership"})
     */
    private bool $enabled;

    /**
     * @ORM\OneToMany(targetEntity=CertificationPartner::class, mappedBy="certification")
     * @Serializer\Expose
     * @Serializer\Groups({"partnership"})
     */
    private Collection $certificationPartners;

    /**
     * @ORM\ManyToMany(targetEntity=Organism::class, inversedBy="certifierCertifications")
     * @ORM\JoinTable(name="certifications_certifiers")
     */
    private Collection $certifiers;

    /**
     * @ORM\OneToMany(targetEntity=Training::class, mappedBy="certification", orphanRemoval=true)
     */
    private Collection $trainings;

    /**
     * @ORM\OneToMany(targetEntity=RegistrationFolder::class, mappedBy="certification")
     */
    private Collection $registrationFolders;

    /**
     * @ORM\Column(type="integer")
     */
    private int $count;

    /**
     * @ORM\Column(type="integer", nullable=true)
     * @Serializer\Expose()
     */
    private ?int $idFiche = null;

    /**
     * @ORM\OneToMany(targetEntity=CertificationFolder::class, mappedBy="certification")
     */
    private Collection $certificationFolders;

    /**
     * Not exposed directly see the getter
     * @ORM\Column(type="json", nullable=true)
     */
    private ?array $certificationFolderFileTypes = [];

    /**
     * @ORM\Column(type="integer", nullable=true)
     * @Serializer\Expose
     * @Serializer\Groups({"Default"})
     */
    private ?int $validityPeriod = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Expose
     * @Serializer\Groups({"Default", "attendee", "lite"})
     */
    private ?string $type;

    /**
     * @ORM\Column(type="integer", nullable=true)
     * @Serializer\Expose
     * @Serializer\Groups({"Default", "attendee"})
     */
    private ?int $code;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Expose
     * @Serializer\Groups({"Default", "partnership"})
     */
    private ?string $link;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Expose
     * @Serializer\Groups({"Default"})
     */
    private ?string $obtentionSystem = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"owner", "Default"})
     */
    private ?string $examinationType = null;

    /**
     * @ORM\Column(type="boolean")
     * @Serializer\Expose()
     * @Serializer\Groups({"owner", "Default"})
     */
    private bool $autoRegistering = true;

    /**
     * @Serializer\Expose()
     * @Serializer\Groups({"partnership"})
     */
    private int $partnerCount = 0;

    /**
     * @ORM\Column(type="float", nullable=true)
     * @Serializer\Expose()
     */
    private ?float $amountHt = null;

    /**
     * Not exposed directly see the getter
     * @ORM\Column(type="json", nullable=true)
     */
    private ?array $certificationPartnerFileTypes = [];

    /**
     * @ORM\Column(type="text", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "partnership"})
     */
    private ?string $partnershipComment = null;

    /**
     * @ORM\Column(type="boolean")
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "partnership"})
     */
    private bool $allowPartnershipRequest = true;

    /**
     * @ORM\Column(type="boolean", options={"default" : false})
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    private bool $allowGenerateXmlAutomatically = false;

    /**
     * @ORM\Column(type="boolean", options={"default" : false})
     */
    private bool $errorSynchronizePartners = false;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private ?string $certificateTemplateName = null;

    /**
     * @Vich\UploadableField(mapping="certificationCertificateTemplateFile", fileNameProperty="certificateTemplateName")
     * @var File|null
     */
    private ?File $certificateTemplateFile = null;

    /**
     * @Vich\UploadableField(mapping="certificateTemplateThumbnailFile", fileNameProperty="certificateTemplateThumbnailName")
     * @var File|null
     */
    public ?File $certificateTemplateThumbnailFile = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private ?string $certificateTemplateThumbnailName = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Expose
     * @Serializer\Groups({"Default"})
     */
    private ?DateTimeInterface $openDataLastUpdate = null;

    /**
     * @ORM\Column(type="integer", nullable=true)
     * @Serializer\Expose
     * @Serializer\Groups({"Default"})
     */
    private ?int $estimatedRegistrationFoldersCount = null;

    /**
     * @ORM\OneToMany(targetEntity=CertificationFile::class, mappedBy="certification", orphanRemoval=true, fetch="EAGER")
     */
    private Collection $files;

    /**
     * @ORM\OneToMany(targetEntity=Skill::class, mappedBy="certification", orphanRemoval=true)
     */
    private Collection $skills;

    /**
     * @ORM\Column(type="boolean", options={"default" : false})
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "lite"})
     */
    private bool $allowAudits = false;

    /**
     * @ORM\Column(type="boolean", options={"default" : false})
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "lite"})
     */
    private bool $auditsPendingCancellation = false;

    /**
     * @ORM\Column(type="date", nullable=true)
     * @Serializer\Expose
     * @Serializer\Groups({"Default"})
     */
    private ?DateTimeInterface $auditTrialEndDate = null;

    /**
     * @ORM\Column(type="string", length=50, nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "lite"})
     */
    private string $dataProvider;

    /**
     * @ORM\Column(type="boolean", options={"default" : false})
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "attendee"})
     */
    private bool $surveyOptional = false;

    /**
     * @ORM\Column(type="boolean", options={"default" : false})
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "lite"})
     */
    private bool $allowPartialSkillSets = false;

    /**
     * @return array
     */
    public static function getDefaultCertificationFolderFileTypeRightsForPartner(): array
    {
        return ['allowVisibilityPartner' => true, 'allowUploadPartner' => true];
    }

    /**
     * @return array
     */
    public static function getDefaultCertificationFolderFileTypeRightsForAttendee(): array
    {
        return ['allowVisibilityAttendee' => false, 'allowUploadAttendee' => false];
    }

    /**
     * @return array
     */
    public static function getDefaultCertificationPartnerFileTypeRightsForPartner(): array
    {
        return ['allowVisibilityPartner' => true, 'allowUploadPartner' => true];
    }

    /**
     * @return array
     */
    public static function getDefaultCertificationPartnerFileTypeRightsForAttendee(): array
    {
        return ['allowVisibilityAttendee' => false, 'allowUploadAttendee' => false];
    }

    /**
     * @VirtualProperty()
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "partnership"})
     */
    public function isPromoted(): bool
    {
        //@TODO when real property define change filter certificationRepository => findAllPartnershipOrderedQueryBuilder
        $isPromoted = false;
        /** @var Organism $certifier */
        foreach ($this->certifiers as $certifier) {
            $isPromoted = $certifier->getSubscription() && $certifier->getSubscription()->isAllowCertifierPlus();
            if ($isPromoted) {
                break;
            }
        }
        return $isPromoted;
    }

    public function __construct()
    {
        $this->certificationPartners = new ArrayCollection();
        $this->certifiers = new ArrayCollection();
        $this->trainings = new ArrayCollection();
        $this->registrationFolders = new ArrayCollection();
        $this->certificationFolders = new ArrayCollection();
        $this->files = new ArrayCollection();
        $this->skills = new ArrayCollection();
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getLevel(): string
    {
        return $this->level;
    }

    public function setLevel(string $level): self
    {
        $this->level = $level;

        return $this;
    }

    public function getDescriptif(): ?string
    {
        return $this->descriptif;
    }

    public function setDescriptif(?string $descriptif): self
    {
        $this->descriptif = $descriptif;

        return $this;
    }

    public function getObjectif(): ?string
    {
        return $this->objectif;
    }

    public function setObjectif(?string $objectif): self
    {
        $this->objectif = $objectif;

        return $this;
    }

    public function getMoreInformationsLink(): ?string
    {
        return $this->moreInformationsLink;
    }

    public function setMoreInformationsLink(?string $moreInformationsLink): self
    {
        $this->moreInformationsLink = $moreInformationsLink;

        return $this;
    }

    public function getDomains(): array
    {
        return $this->domains;
    }

    public function setDomains(?array $domains): self
    {
        $this->domains = $domains;

        return $this;
    }

    public function getRome(): array
    {
        return $this->rome;
    }

    public function setRome(?array $rome): self
    {
        $this->rome = $rome;

        return $this;
    }

    public function getGfe(): array
    {
        return $this->gfe;
    }

    public function setGfe(?array $gfe): self
    {
        $this->gfe = $gfe;

        return $this;
    }

    public function getNsf(): array
    {
        return $this->nsf;
    }

    public function setNsf(?array $nsf): self
    {
        $this->nsf = $nsf;

        return $this;
    }

    public function getCpf(): ?string
    {
        return $this->cpf;
    }

    public function setCpf(?string $cpf): self
    {
        $this->cpf = $cpf;

        return $this;
    }

    public function getCpfDateStart(): ?DateTimeInterface
    {
        return $this->cpfDateStart;
    }

    public function setCpfDateStart(?DateTimeInterface $cpfDateStart): self
    {
        $this->cpfDateStart = $cpfDateStart;

        return $this;
    }

    public function getCpfDateEnd(): ?DateTimeInterface
    {
        return $this->cpfDateEnd;
    }

    public function setCpfDateEnd(?DateTimeInterface $cpfDateEnd): self
    {
        $this->cpfDateEnd = $cpfDateEnd;

        return $this;
    }

    /**
     * @return Collection|CertificationPartner[]
     */
    public function getCertificationPartners(): Collection
    {
        return $this->certificationPartners;
    }

    public function addCertificationPartners(CertificationPartner $certificationPartners): self
    {
        if (!$this->certificationPartners->contains($certificationPartners)) {
            $this->certificationPartners[] = $certificationPartners;
        }

        return $this;
    }

    public function removeCertificationPartners(CertificationPartner $certificationPartners): self
    {
        if ($this->certificationPartners->contains($certificationPartners)) {
            $this->certificationPartners->removeElement($certificationPartners);
        }
        return $this;
    }

    /**
     * @return Collection|Organism[]
     */
    public function getCertifiers(): Collection
    {
        return $this->certifiers;
    }

    public function addCertifier(Organism $certifier): self
    {
        if (!$this->certifiers->contains($certifier)) {
            $this->certifiers[] = $certifier;
        }

        return $this;
    }

    public function removeCertifier(Organism $certifier): self
    {
        if ($this->certifiers->contains($certifier)) {
            $this->certifiers->removeElement($certifier);
        }

        return $this;
    }

    public function clearCertifiers()
    {
        $this->getCertifiers()->clear();
    }

    /**
     * @return Collection
     */
    public function getTrainings(): Collection
    {
        return $this->trainings;
    }

    public function addTraining(Training $training): self
    {
        if (!$this->trainings->contains($training)) {
            $this->trainings[] = $training;
            $training->setCertification($this);
        }

        return $this;
    }

    public function removeTraining(Training $training): self
    {
        if ($this->trainings->contains($training)) {
            $this->trainings->removeElement($training);
            // set the owning side to null (unless already changed)
            if ($training->getCertification() === $this) {
                $training->setCertification(null);
            }
        }

        return $this;
    }

    public function getCertifInfo(): ?string
    {
        return $this->certifInfo;
    }

    public function setCertifInfo(?string $certifInfo): self
    {
        $this->certifInfo = $certifInfo;

        return $this;
    }

    public function getLastExternalUpdate(): ?DateTimeInterface
    {
        return $this->lastExternalUpdate;
    }

    public function setLastExternalUpdate(?DateTimeInterface $lastExternalUpdate): self
    {
        $this->lastExternalUpdate = $lastExternalUpdate;

        return $this;
    }

    public function getEnabled(): bool
    {
        return $this->enabled;
    }

    public function setEnabled(bool $enabled): self
    {
        $this->enabled = $enabled;

        return $this;
    }

    public function getDefaultCertifier(): ?Organism
    {
        return $this->certifiers->count() > 0 ? $this->certifiers[0] : null;
    }

    public function toString(): string
    {
        return $this->certifInfo . " " . $this->name;
    }

    /**
     * @return Collection|RegistrationFolder[]
     */
    public function getRegistrationFolders(): Collection
    {
        return $this->registrationFolders;
    }

    public function addRegistrationFolder(RegistrationFolder $registrationFolder): self
    {
        if (!$this->registrationFolders->contains($registrationFolder)) {
            $this->registrationFolders[] = $registrationFolder;
            $registrationFolder->setCertification($this);
        }

        return $this;
    }

    public function removeRegistrationFolder(RegistrationFolder $registrationFolder): self
    {
        if ($this->registrationFolders->removeElement($registrationFolder)) {
            // set the owning side to null (unless already changed)
            if ($registrationFolder->getCertification() === $this) {
                $registrationFolder->setCertification(null);
            }
        }

        return $this;
    }

    public function getCount(): int
    {
        return $this->count;
    }

    public function setCount(int $count): self
    {
        $this->count = $count;

        return $this;
    }

    public function getIdFiche(): ?int
    {
        return $this->idFiche;
    }

    public function setIdFiche(?int $idFiche): self
    {
        $this->idFiche = $idFiche;

        return $this;
    }

    /**
     * @return Collection|CertificationFolder[]
     */
    public function getCertificationFolders(): Collection
    {
        return $this->certificationFolders;
    }

    public function addCertificationFolder(CertificationFolder $certificationFolder): self
    {
        if (!$this->certificationFolders->contains($certificationFolder)) {
            $this->certificationFolders[] = $certificationFolder;
            $certificationFolder->setCertification($this);
        }

        return $this;
    }

    public function removeCertificationFolder(CertificationFolder $certificationFolder): self
    {
        if ($this->certificationFolders->removeElement($certificationFolder)) {
            // set the owning side to null (unless already changed)
            if ($certificationFolder->getCertification() === $this) {
                $certificationFolder->setCertification(null);
            }
        }

        return $this;
    }

    /**
     * @Serializer\VirtualProperty()
     * @Serializer\Expose()
     * @Serializer\SerializedName("certificationFolderFileTypes")
     * @Serializer\Groups({"Default"})
     */
    public function getCertificationFolderFileTypes(CertificationFolder $certificationFolder = null): array
    {
        $fileTypes = [];
        $hasCertificateFileType = false;
        if ($this->certificationFolderFileTypes) {
            foreach ($this->certificationFolderFileTypes as $fileType) {
                if ($fileType['id'] === self::CERTIFICATE_FILE_TYPE_ID) {
                    $hasCertificateFileType = true;
                }
                //for backyard compatibility purpose
                if (!isset($fileType['allowVisibilityAttendee']) || !isset($fileType['allowUploadAttendee'])) {
                    $fileType = array_merge(self::getDefaultCertificationFolderFileTypeRightsForAttendee(), $fileType);
                }
                //for backyard compatibility purpose
                if (!isset($fileType['allowVisibilityPartner']) || !isset($fileType['allowUploadPartner'])) {
                    $fileType = array_merge(self::getDefaultCertificationFolderFileTypeRightsForPartner(), $fileType);
                }
                $fileTypes[] = $fileType;
            }
        }
        if (!$hasCertificateFileType) {
            $fileTypes[] = $this->getCertificateFileType();
        }
        if ($certificationFolder) {
            return Tools::filterFileTypes($fileTypes, $certificationFolder->getTags(), $certificationFolder->getCertification()->getCertifInfo());
        } else {
            return $fileTypes;
        }
    }

    public function setCertificationFolderFileTypes(?array $certificationFolderFileTypes): self
    {
        $this->certificationFolderFileTypes = $certificationFolderFileTypes;

        return $this;
    }

    public function getValidityPeriod(): ?int
    {
        return $this->validityPeriod;
    }

    public function setValidityPeriod(?int $validityPeriod): self
    {
        $this->validityPeriod = $validityPeriod;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(?string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getCode(): ?int
    {
        return $this->code;
    }

    public function setCode(?int $code): self
    {
        $this->code = $code;

        return $this;
    }

    public function getLink(): ?string
    {
        return $this->link;
    }

    public function setLink(?string $link): self
    {
        $this->link = $link;

        return $this;
    }

    /**
     * @Serializer\VirtualProperty()
     * @Serializer\Groups({"partnership", "Default", "lite"})
     */
    public function getExternalId(): ?string
    {
        if ($this->getType() && $this->getCode()) {
            return $this->getType() . $this->getCode();
        } else {
            return null;
        }
    }

    public function getObtentionSystem(): ?string
    {
        return $this->obtentionSystem;
    }

    public function setObtentionSystem(?string $obtentionSystem): self
    {
        $this->obtentionSystem = $obtentionSystem;

        return $this;
    }

    public function getExaminationType(): ?string
    {
        return $this->examinationType;
    }

    public function setExaminationType(?string $examinationType): self
    {
        $this->examinationType = $examinationType;

        return $this;
    }

    public function isCdcExportable(): bool
    {
        return (bool)$this->getObtentionSystem();
    }


    public function isAutoRegistering(): bool
    {
        return $this->autoRegistering;
    }

    public function setAutoRegistering(bool $autoRegistering): self
    {
        $this->autoRegistering = $autoRegistering;
        return $this;
    }

    public function isCertifier(Organism $organism): bool
    {
        return $this->certifiers->contains($organism);
    }

    /**
     * @return int
     */
    public function getPartnerCount(): int
    {
        return $this->partnerCount;
    }

    /**
     * @param int $partnerCount
     * @return Certification
     */
    public function setPartnerCount(int $partnerCount): Certification
    {
        $this->partnerCount = $partnerCount;
        return $this;
    }

    public function getAmountHt(): ?float
    {
        return $this->amountHt;
    }

    public function setAmountHt(?float $amountHt): self
    {
        $this->amountHt = $amountHt;

        return $this;
    }

    /**
     * @Serializer\VirtualProperty()
     * @Serializer\Expose()
     * @Serializer\SerializedName("hasMultipleCertifiers")
     */
    public function getHasMultipleCertifiers(): bool
    {
        return $this->getCertifiers()->count() > 1;
    }

    /**
     * @Serializer\VirtualProperty()
     * @Serializer\Expose()
     * @Serializer\SerializedName("certificationPartnerFileTypes")
     * @param CertificationPartner|null $certificationPartner
     * @return array
     */
    public function getCertificationPartnerFileTypes(CertificationPartner $certificationPartner = null): array
    {
        $fileTypes = [];
        if ($this->certificationPartnerFileTypes) {
            foreach ($this->certificationPartnerFileTypes as $fileType) {
                //for backyard compatibility purpose
                if (!isset($fileType['allowVisibilityAttendee']) || !isset($fileType['allowUploadAttendee'])) {
                    $fileType = array_merge(self::getDefaultCertificationPartnerFileTypeRightsForAttendee(), $fileType);
                }
                //for backyard compatibility purpose
                if (!isset($fileType['allowVisibilityPartner']) || !isset($fileType['allowUploadPartner'])) {
                    $fileType = array_merge(self::getDefaultCertificationPartnerFileTypeRightsForPartner(), $fileType);
                }
                $fileTypes[] = $fileType;
            }
        }
        if ($certificationPartner) {
            return Tools::filterFileTypes($fileTypes, $certificationPartner->getTags(), $certificationPartner->getCertification()->getCertifInfo());
        } else {
            return $fileTypes;
        }
    }

    public function setCertificationPartnerFileTypes(?array $certificationPartnerFileTypes): self
    {
        $this->certificationPartnerFileTypes = $certificationPartnerFileTypes;

        return $this;
    }

    public function getPartnershipComment(): ?string
    {
        return $this->partnershipComment;
    }

    public function setPartnershipComment(?string $partnershipComment): self
    {
        $this->partnershipComment = $partnershipComment;

        return $this;
    }

    public function isAllowPartnershipRequest(): bool
    {
        return $this->allowPartnershipRequest;
    }

    public function setAllowPartnershipRequest(bool $allowPartnershipRequest): self
    {
        $this->allowPartnershipRequest = $allowPartnershipRequest;
        return $this;
    }

    public function isAllowGenerateXmlAutomatically(): ?bool
    {
        return $this->allowGenerateXmlAutomatically;
    }

    public function setAllowGenerateXmlAutomatically(?bool $allowGenerateXmlAutomatically): self
    {
        $this->allowGenerateXmlAutomatically = $allowGenerateXmlAutomatically;

        return $this;
    }

    public function hasErrorSynchronizePartners(): ?bool
    {
        return $this->errorSynchronizePartners;
    }

    public function setErrorSynchronizePartners(bool $errorSynchronizePartners): self
    {
        $this->errorSynchronizePartners = $errorSynchronizePartners;

        return $this;
    }

    public function getCertificateTemplateName(): ?string
    {
        return $this->certificateTemplateName;
    }

    public function setCertificateTemplateName(?string $certificateTemplateName = null): self
    {
        $this->certificateTemplateName = $certificateTemplateName;

        return $this;
    }

    /**
     * @param File|null $certificateTemplateFile
     */
    public function setCertificateTemplateFile(?File $certificateTemplateFile = null): void
    {
        $this->certificateTemplateFile = $certificateTemplateFile;
        if (null !== $certificateTemplateFile) {
            $this->setUpdatedOn(new DateTime());
        }
    }

    /**
     * @return File|null
     */
    public function getCertificateTemplateFile(): ?File
    {
        return $this->certificateTemplateFile;
    }

    /**
     * @Serializer\VirtualProperty()
     * @Serializer\SerializedName("certificateTemplate")
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    public function getCertificateTemplate(): ?string
    {
        if ($this->getCertificateTemplateName()) {
            //should be dynamic based on vich_uploader.yml but how
            return "/api/certifications/$this->certifInfo/certificateTemplate";
        } else {
            return null;
        }
    }

    /**
     * @Serializer\VirtualProperty()
     * @Serializer\SerializedName("allowGenerateCertificate")
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    public function isAllowGenerateCertificate(): bool
    {
        $certificateFileType = array_values(array_filter($this->getCertificationFolderFileTypes(), fn(array $fileType) => $fileType['id'] === self::CERTIFICATE_FILE_TYPE_ID))[0];
        return !empty($certificateFileType) && $certificateFileType['generated'] === true && !empty($certificateFileType['googleId']);
    }

    public function getCertificateTemplateThumbnailName(): ?string
    {
        return $this->certificateTemplateThumbnailName;
    }

    public function setCertificateTemplateThumbnailName(?string $certificateTemplateThumbnailName = null): self
    {
        $this->certificateTemplateThumbnailName = (string)($certificateTemplateThumbnailName);

        return $this;
    }

    /**
     * @param File|null $certificateTemplateThumbnailFile
     */
    public function setCertificateTemplateThumbnailFile(?File $certificateTemplateThumbnailFile = null): void
    {
        $this->certificateTemplateThumbnailFile = $certificateTemplateThumbnailFile;
        if (null !== $certificateTemplateThumbnailFile) {
            $this->updateUpdatedOnTimestamp();
        }
    }

    /**
     * @return File|null
     */
    public function getCertificateTemplateThumbnailFile(): ?File
    {
        return $this->certificateTemplateThumbnailFile;
    }

    /**
     * @Serializer\VirtualProperty()
     * @Serializer\SerializedName("certificateTemplateThumbnail")
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    public function getCertificateTemplateThumbnail(): ?string
    {
        if ($this->getCertificateTemplateThumbnailName()) {
            return "/app/certifications/$this->certifInfo/certificateTemplateThumbnail";
        } else {
            return null;
        }
    }

    public function getOpenDataLastUpdate(): ?DateTimeInterface
    {
        return $this->openDataLastUpdate;
    }

    public function setOpenDataLastUpdate(?DateTimeInterface $openDataLastUpdate): self
    {
        $this->openDataLastUpdate = $openDataLastUpdate;

        return $this;
    }

    public function getEstimatedRegistrationFoldersCount(): ?int
    {
        return $this->estimatedRegistrationFoldersCount;
    }

    public function setEstimatedRegistrationFoldersCount(?int $estimatedRegistrationFoldersCount): self
    {
        $this->estimatedRegistrationFoldersCount = $estimatedRegistrationFoldersCount;

        return $this;
    }

    /**
     * @return Collection|CertificationFile[]
     */
    public function getFiles(): Collection
    {
        return $this->files;
    }

    public function addFile(CertificationFile $file): self
    {
        if (!$this->files->contains($file)) {
            $this->files[] = $file;
            $file->setCertification($this);
        }

        return $this;
    }

    public function removeFile(CertificationFile $file): self
    {
        if ($this->files->removeElement($file)) {
            // set the owning side to null (unless already changed)
            if ($file->getCertification() === $this) {
                $file->setCertification(null);
            }
        }

        return $this;
    }

    private function getCertificateFileType(): array
    {
        return [
            "id" => self::CERTIFICATE_FILE_TYPE_ID,
            "generated" => false,
            "enabled" => false,
            "name" => self::CERTIFICATE_FILE_TYPE_NAME,
            "accept" => ".pdf",
            "toState" => "success",
            "description" => null,
            "allowMultiple" => false,
            "allowVisibilityAttendee" => true,
            "allowUploadAttendee" => false,
            "allowSignAttendee" => false,
            "allowVisibilityPartner" => true,
            "allowUploadPartner" => false,
            "allowSignPartner" => false,
            "googleId" => null,
            "templateFile" => null
        ];
    }

    /**
     * @return Collection|Skill[]
     */
    public function getSkills(): Collection
    {
        return $this->skills;
    }

    public function addSkill(Skill $skill): self
    {
        if (!$this->skills->contains($skill)) {
            $this->skills[] = $skill;
            $skill->setCertification($this);
        }

        return $this;
    }

    public function removeSkill(Skill $skill): self
    {
        if ($this->skills->removeElement($skill)) {
            // set the owning side to null (unless already changed)
            if ($skill->getCertification() === $this) {
                $skill->setCertification(null);
            }
        }

        return $this;
    }

    public function isAllowAudits(): ?bool
    {
        return $this->allowAudits;
    }

    public function setAllowAudits(bool $allowAudits): self
    {
        $this->allowAudits = $allowAudits;

        return $this;
    }

    public function isAuditsPendingCancellation(): bool
    {
        return $this->auditsPendingCancellation;
    }

    public function setAuditsPendingCancellation(bool $auditsPendingCancellation): self
    {
        $this->auditsPendingCancellation = $auditsPendingCancellation;

        return $this;
    }

    public function getAuditTrialEndDate(): ?DateTimeInterface
    {
        return $this->auditTrialEndDate;
    }

    public function setAuditTrialEndDate(?DateTimeInterface $auditTrialEndDate): self
    {
        $this->auditTrialEndDate = $auditTrialEndDate;

        return $this;
    }

    public function getDataProvider(): ?string
    {
        return $this->dataProvider;
    }

    public function setDataProvider(string $dataProvider): self
    {
        $this->dataProvider = $dataProvider;

        return $this;
    }

    public function isSurveyOptional(): bool
    {
        return $this->surveyOptional;
    }

    public function setSurveyOptional(bool $surveyOptional): self
    {
        $this->surveyOptional = $surveyOptional;

        return $this;
    }

    public function isAllowPartialSkillSets(): bool
    {
        return $this->allowPartialSkillSets;
    }

    public function setAllowPartialSkillSets(bool $allowPartialSkillSets): self
    {
        $this->allowPartialSkillSets = $allowPartialSkillSets;

        return $this;
    }

    public function getSkillSets(): Collection
    {
        return $this->getSkills()->filter(fn($skill) => $skill->getType() === CertificationSkillType::SKILL_SET()->getValue());
    }
}
