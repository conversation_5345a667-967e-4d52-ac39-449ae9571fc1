<?php

namespace App\Entity;

use App\Repository\DeliveryRepository;
use DateTimeInterface;
use Doctrine\ORM\Mapping as ORM;
use <PERSON>oas\Configuration\Annotation as Hateoas;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;

/**
 * @ORM\Entity(repositoryClass=DeliveryRepository::class)
 * @ORM\Table(indexes={@ORM\Index(name="entity",columns={"entity_class", "entity_id"})})
 * @Hateoas\Relation("webhook", href = "expr('/api/webhooks/' ~ object.getWebhook().getId())",attributes={"name" = "expr(object.getWebhook().getName())"})
 * @Hateoas\Relation("retry", href = "expr('/api/webhooks/' ~ object.getWebhook().getId() ~ '/delivery/' ~ object.getId() ~ '/retry')")
 */
class Delivery
{
    /**
     * @ORM\Id()
     * @ORM\GeneratedValue()
     * @ORM\Column(type="integer")
     */
    private int $id;

    /**
     * @ORM\Column(type="datetime")
     */
    private DateTimeInterface $date;

    /**
     * @ORM\Column(type="text")
     * @Serializer\Exclude()
     */
    private string $content;

    /**
     * @ORM\Column(type="string", length=255, unique=true)
     */
    private string $guid;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Exclude()
     */
    private ?string $host = null;

    /**
     * @ORM\ManyToOne(targetEntity=Webhook::class, inversedBy="deliveries")
     * @ORM\JoinColumn(nullable=false)
     * @Serializer\Exclude()
     */
    private Webhook $webhook;

    /**
     * @ORM\Column(type="integer")
     */
    private int $statusCode;

    /**
     * @ORM\Column(type="text", nullable=true)
     */
    private ?string $errorMessage = null;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private string $event;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Exclude()
     */
    private ?DateTimeInterface $responseAt = null;

    /**
     * @ORM\Column(type="integer")
     * @Serializer\Exclude()
     */
    private int $sentCount = 1;

    /**
     * @ORM\Column(type="string", length=255)
     * @Serializer\Exclude()
     */
    private string $entityClass;

    /**
     * @ORM\Column(type="string", length=255)
     * @Serializer\Exclude()
     */
    private string $entityId;

    /**
     * @ORM\Column(type="integer", nullable=true)
     * @Serializer\Exclude()
     */
    private ?int $authorId = null;


    public function getId(): int
    {
        return $this->id;
    }

    public function getDate(): DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getContent(): string
    {
        return $this->content;
    }

    public function setContent(string $content): self
    {
        $this->content = $content;

        return $this;
    }

    public function getGuid(): string
    {
        return $this->guid;
    }

    public function setGuid(string $guid): self
    {
        $this->guid = $guid;

        return $this;
    }

    public function getWebhook(): Webhook
    {
        return $this->webhook;
    }

    public function setWebhook(Webhook $webhook): self
    {
        $this->webhook = $webhook;

        return $this;
    }

    public function getStatusCode(): int
    {
        return $this->statusCode;
    }

    public function setStatusCode(int $statusCode): self
    {
        $this->statusCode = $statusCode;

        return $this;
    }

    public function getErrorMessage(): ?string
    {
        return $this->errorMessage;
    }

    public function setErrorMessage(?string $errorMessage): self
    {
        $this->errorMessage = $errorMessage;

        return $this;
    }

    public function getEvent(): string
    {
        return $this->event;
    }

    public function setEvent(string $event): self
    {
        $this->event = $event;

        return $this;
    }

    /**
     * @return string
     */
    public function getHost(): ?string
    {
        return $this->host;
    }

    /**
     * @param string|null $host
     * @return Delivery
     */
    public function setHost(?string $host): self
    {
        $this->host = $host;
        return $this;
    }


    /**
     * @return DateTimeInterface|null
     */
    public function getResponseAt(): ?DateTimeInterface
    {
        return $this->responseAt;
    }

    /**
     * @param DateTimeInterface|null $responseAt
     * @return Delivery
     */
    public function setResponseAt(?DateTimeInterface $responseAt): self
    {
        $this->responseAt = $responseAt;
        return $this;
    }

    /**
     * @return int
     */
    public function getSentCount(): int
    {
        return $this->sentCount;
    }

    /**
     * @param int $sentCount
     * @return Delivery
     */
    public function setSentCount(int $sentCount): self
    {
        $this->sentCount = $sentCount;
        return $this;
    }

    /**
     * @return string
     */
    public function getEntityClass(): string
    {
        return $this->entityClass;
    }

    /**
     * @param string $entityClass
     * @return $this
     */
    public function setEntityClass(string $entityClass): self
    {
        $this->entityClass = $entityClass;
        return $this;
    }

    /**
     * @return string
     */
    public function getEntityId(): string
    {
        return $this->entityId;
    }

    /**
     * @param string $entityId
     * @return $this
     */
    public function setEntityId(string $entityId): self
    {
        $this->entityId = $entityId;
        return $this;
    }

    /**
     * @return int
     */
    public function getAuthorId(): ?int
    {
        return $this->authorId;
    }

    /**
     * @param int|null $authorId
     * @return Delivery
     */
    public function setAuthorId(?int $authorId): self
    {
        $this->authorId = $authorId;
        return $this;
    }
}
