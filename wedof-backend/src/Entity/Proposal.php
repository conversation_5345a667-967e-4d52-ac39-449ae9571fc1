<?php

namespace App\Entity;

use App\Entity\Traits\TimestampableTrait;
use App\Library\utils\enums\ProposalDiscountTypes;
use App\Library\utils\enums\ProposalStates;
use App\Library\utils\Tools;
use App\Repository\ProposalRepository;
use App\Service\ProposalService;
use Beelab\TagBundle\Entity\AbstractTaggable;
use DateTimeInterface;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Exception;
use Hateoas\Configuration\Annotation as Hateoas;
use J<PERSON>\Serializer\Annotation as Serializer;
use Symfony\Component\HttpFoundation\File\File;
use Vich\UploaderBundle\Mapping\Annotation as Vich;


/**
 * @implements EntityExportableCSV<Proposal>
 * @Vich\Uploadable
 * @ORM\Entity(repositoryClass=ProposalRepository::class)
 * @ORM\Table(name="proposal", uniqueConstraints={@ORM\UniqueConstraint(name="proposal_unique_org", columns={"code", "organism_id"})}, indexes={@ORM\Index(name="proposalState_idx",columns={"state"})})
 * @ORM\HasLifecycleCallbacks()
 *
 * @Serializer\ExclusionPolicy("ALL")
 * @Hateoas\Relation("self", href = "expr('/api/proposals/' ~ object.getCode())")
 * @Hateoas\Relation("organism", href = "expr('/api/organisms/' ~ object.getOrganism().getSiret())", attributes={"name" = "expr(object.getOrganism().getName())", "siret" = "expr(object.getOrganism().getSiret())"})
 * @Hateoas\Relation("sales", href="expr('/api/users/' ~ object.getSales().getEmail())", attributes={"name" = "expr(object.getSales().getName())", "email" = "expr(object.getSales().getEmail())"}, exclusion=@Hateoas\Exclusion(excludeIf = "expr(object.getSales() === null)", groups={"Default"}))
 * @Hateoas\Relation("registrationFolder", href = "expr('/api/registrationFolder/' ~ object.getRegistrationFolder().getExternalId())", attributes={"type" = "expr(object.getRegistrationFolder().getType())", "state" = "expr(object.getRegistrationFolder().getState())", "externalId" = "expr(object.getRegistrationFolder().getExternalId())"}, exclusion=@Hateoas\Exclusion(excludeIf = "expr(object.getRegistrationFolder() === null)", groups={"Default"}))
 * @Hateoas\Relation("trainingActions", href = "expr('/api/trainingActions?proposalCode=' ~ object.getCode())")
 */
class Proposal extends AbstractTaggable implements EntityExportableCSV
{
    use TimestampableTrait;

    public const CLASSNAME = 'Proposal';

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private int $id;

    /**
     * @ORM\ManyToOne(targetEntity=Organism::class, inversedBy="proposals")
     * @ORM\JoinColumn(nullable=false)
     */
    private Organism $organism;

    /**
     * @ORM\Column(type="float", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "public"})
     */
    private ?float $amount = null;

    /**
     * @ORM\Column(type="string", length=255)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "public"})
     */
    private ?string $discountType = 'none';

    /**
     * @ORM\ManyToMany(targetEntity=TrainingAction::class, inversedBy="proposals")
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "public"})
     */
    private Collection $trainingActions;

    /**
     * @ORM\Column(type="date", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "public"})
     */
    private ?DateTimeInterface $expire = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "public"})
     */
    private ?string $state = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "public"})
     */
    private ?string $firstName = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "public"})
     */
    private ?string $lastName = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "public"})
     */
    private ?string $email = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "public"})
     */
    private ?string $phoneNumber = null;

    /**
     * @ORM\Column(type="string", length=255)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "public"})
     */
    private string $code;

    /**
     * @ORM\Column(type="text", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    private ?string $notes = null;

    /**
     * @ORM\Column(type="integer", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    private ?int $limitUsage = null;

    /**
     * @ORM\Column(type="boolean")
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "public"})
     */
    private bool $autoValidate;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "public"})
     */
    private ?DateTimeInterface $sessionStartDate = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "public"})
     */
    private ?DateTimeInterface $sessionEndDate = null;

    /**
     * @ORM\Column(type="text", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "public"})
     */
    private ?string $description = null;

    /**
     * @ORM\OneToOne(targetEntity=RegistrationFolder::class, inversedBy="proposal", cascade={"persist", "remove"})
     */
    private ?RegistrationFolder $registrationFolder = null;

    /**
     * @ORM\ManyToOne(targetEntity=Proposal::class, inversedBy="individualProposals")
     */
    private ?Proposal $parentProposal = null;

    /**
     * @ORM\OneToMany(targetEntity=Proposal::class, mappedBy="parentProposal")
     */
    private Collection $individualProposals;

    /**
     * @ORM\ManyToOne(targetEntity=TrainingAction::class)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "public"})
     */
    private ?TrainingAction $selectedTrainingAction = null;

    /**
     * @ORM\Column(type="integer", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "public"})
     */
    private ?int $indicativeDuration = null;

    /**
     * @ORM\ManyToMany(targetEntity=Tag::Class)
     * Display with serializer to get as array[string] and not as objet
     * CANNOT BE TYPED /!\
     * Don't forget to override setTagsText() and change updatedOn as done below!
     */
    protected $tags; // Type must not be defined (as in base class '\Beelab\TagBundle\Entity\AbstractTaggable')

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "public"})
     */
    private ?string $customColorScheme = null;

    /**
     * @ORM\Column(type="datetime")
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "public"})
     */
    private DateTimeInterface $stateLastUpdate;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private ?string $clientIpAddress;

    /**
     * @ORM\Column(type="boolean")
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "public"})
     */
    private bool $codeRequired = true;

    /**
     * @Vich\UploadableField(mapping="publicFile", fileNameProperty="logoName")
     * @var File|null
     */
    private ?File $logoFile = null;

    /**
     * @ORM\Column(type="string", length=500, nullable=true)
     * @var string|null
     */
    private ?string $logoName = null;

    /**
     * @ORM\ManyToOne(targetEntity=User::class)
     */
    private ?User $sales = null;

    /**
     * @ORM\Column(type="json", nullable=true)
     * @Serializer\Expose
     * @Serializer\Groups({"Default"})
     */
    private ?array $metadata = [];


    public function __construct()
    {
        $this->trainingActions = new ArrayCollection();
        $this->individualProposals = new ArrayCollection();
        parent::__construct(); //needed by tags
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getOrganism(): Organism
    {
        return $this->organism;
    }

    public function setOrganism(Organism $organism): self
    {
        $this->organism = $organism;

        return $this;
    }

    public function getAmount(): ?float
    {
        return $this->amount;
    }

    public function setAmount(?float $amount): self
    {
        $this->amount = $amount;

        return $this;
    }

    /**
     * @return Collection|TrainingAction[]
     */
    public function getTrainingActions(): Collection
    {
        return $this->trainingActions;
    }

    public function addTrainingAction(TrainingAction $trainingAction): self
    {
        if (!$this->trainingActions->contains($trainingAction)) {
            $this->trainingActions[] = $trainingAction;
        }

        return $this;
    }

    public function removeTrainingAction(TrainingAction $trainingAction): self
    {
        $this->trainingActions->removeElement($trainingAction);

        return $this;
    }

    public function getExpire(): ?DateTimeInterface
    {
        return $this->expire;
    }

    public function setState(?string $state): self
    {
        $this->state = $state;

        return $this;
    }

    public function getState(): ?string
    {
        return $this->state;
    }

    /**
     * @param DateTimeInterface|null $expire
     * @return $this
     */
    public function setExpire(?DateTimeInterface $expire): self
    {
        $this->expire = $expire;

        return $this;
    }

    public function getFirstName(): ?string
    {
        return $this->firstName;
    }

    public function setFirstName(?string $firstName): self
    {
        $this->firstName = $firstName;

        return $this;
    }

    public function getLastName(): ?string
    {
        return $this->lastName;
    }

    public function setLastName(?string $lastName): self
    {
        $this->lastName = $lastName;

        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(?string $email): self
    {
        $this->email = $email;

        return $this;
    }

    public function getPhoneNumber(): ?string
    {
        return $this->phoneNumber;
    }

    public function setPhoneNumber(?string $phoneNumber): self
    {
        $this->phoneNumber = $phoneNumber;

        return $this;
    }

    public function getCode(): string
    {
        return $this->code;
    }

    public function setCode(string $code): self
    {
        $this->code = $code;

        return $this;
    }

    public function getNotes(): ?string
    {
        return $this->notes;
    }

    public function setNotes(?string $notes): self
    {
        $this->notes = $notes;

        return $this;
    }

    public function getDiscountType(): ?string
    {
        return $this->discountType;
    }

    public function setDiscountType(?string $discountType): self
    {
        $this->discountType = $discountType;

        return $this;
    }

    public function getLimitUsage(): ?int
    {
        return $this->limitUsage;
    }

    public function setLimitUsage(?int $limitUsage): self
    {
        $this->limitUsage = $limitUsage;

        return $this;
    }

    public function getIndicativeDuration(): ?int
    {
        return $this->indicativeDuration;
    }

    public function setIndicativeDuration(?int $indicativeDuration): self
    {
        $this->indicativeDuration = $indicativeDuration;

        return $this;
    }

    public function getAutoValidate(): bool
    {
        return $this->autoValidate;
    }

    public function setAutoValidate(bool $autoValidate): self
    {
        $this->autoValidate = $autoValidate;

        return $this;
    }

    public function getSessionStartDate(): ?DateTimeInterface
    {
        return $this->sessionStartDate;
    }

    public function setSessionStartDate(?DateTimeInterface $sessionStartDate): self
    {
        $this->sessionStartDate = $sessionStartDate;

        return $this;
    }

    public function getSessionEndDate(): ?DateTimeInterface
    {
        return $this->sessionEndDate;
    }

    public function setSessionEndDate(?DateTimeInterface $sessionEndDate): self
    {
        $this->sessionEndDate = $sessionEndDate;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): self
    {
        $this->description = $description;

        return $this;
    }

    public function getRegistrationFolder(): ?RegistrationFolder
    {
        return $this->registrationFolder;
    }

    public function setRegistrationFolder(?RegistrationFolder $registrationFolder): self
    {
        $this->registrationFolder = $registrationFolder;

        return $this;
    }

    public function getParentProposal(): ?self
    {
        return $this->parentProposal;
    }

    public function setParentProposal(?self $parentProposal): self
    {
        $this->parentProposal = $parentProposal;

        return $this;
    }

    public function isValid(): bool
    {
        $valid = true;
        if ($this->getExpire() != null && $this->getExpire() < date('now')) {
            $valid = false;
        }

        if ($this->getParentProposal()) {
            $parent = $this->getParentProposal();
            if ($parent->getLimitUsage() != null && $parent->getLimitUsage() <= $parent->getIndividualProposals()->count()) {
                $valid = false;
            }
        }
        if ($this->getLimitUsage() != null && $this->getLimitUsage() <= $this->getIndividualProposals()->count()) {
            $valid = false;
        }
        return $valid;
    }

    /**
     * @return Collection|self[]
     */
    public function getIndividualProposals(): Collection
    {
        return $this->individualProposals;
    }

    public function addIndividualProposal(self $individualProposal): self
    {
        if (!$this->individualProposals->contains($individualProposal)) {
            $this->individualProposals[] = $individualProposal;
            $individualProposal->setParentProposal($this);
        }

        return $this;
    }

    public function removeIndividualProposal(self $individualProposal): self
    {
        if ($this->individualProposals->removeElement($individualProposal)) {
            // set the owning side to null (unless already changed)
            if ($individualProposal->getParentProposal() === $this) {
                $individualProposal->setParentProposal(null);
            }
        }

        return $this;
    }

    /**
     * @return bool
     */
    public function isDeletable(): bool
    {
        return ($this->isIndividual() && $this->registrationFolder === null) || (!$this->isIndividual() && $this->getIndividualProposals()->count() === 0);
    }

    /**
     * @Serializer\VirtualProperty()
     * @Serializer\SerializedName("isIndividual")
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "public"})
     */
    public function isIndividual(): bool
    {
        return $this->email !== null;
    }

    public function getSelectedTrainingAction(): ?TrainingAction
    {
        return $this->selectedTrainingAction;
    }

    public function setSelectedTrainingAction(?TrainingAction $selectedTrainingAction): self
    {
        $this->selectedTrainingAction = $selectedTrainingAction;

        return $this;
    }

    /**
     * @param File|null $logoFile
     */
    public function setLogoFile(?File $logoFile = null): void
    {
        $this->logoFile = $logoFile;

        if (null !== $logoFile) {
            // It is required that at least one field changes if you are using doctrine
            // otherwise the event listeners won't be called and the file is lost
            $this->updateCreatedOnAndUpdatedOnTimestamps();
        }
    }

    /**
     * @return File|null
     */
    public function getLogoFile(): ?File
    {
        return $this->logoFile;
    }

    /**
     * @param string|null $logoName
     * @return void
     */
    public function setLogoName(?string $logoName): void
    {
        $this->logoName = $logoName;
    }

    /**
     * @return string|null
     */
    public function getLogoName(): ?string
    {
        return $this->logoName;
    }

    public function getCustomColorScheme(): ?string
    {
        return $this->customColorScheme;
    }

    public function setCustomColorScheme(?string $customColorScheme): self
    {
        $this->customColorScheme = $customColorScheme;

        return $this;
    }

    public function getStateLastUpdate(): DateTimeInterface
    {
        return $this->stateLastUpdate;
    }

    public function setStateLastUpdate(DateTimeInterface $stateLastUpdate): self
    {
        $this->stateLastUpdate = $stateLastUpdate;

        return $this;
    }

    // Required on every taggable so that if only tags are changed, they are persisted
    public function setTagsText(?string $tagsText): void
    {
        if ($tagsText !== $this->getTagsText()) {
            $this->updateUpdatedOnTimestamp();
        }
        parent::setTagsText($tagsText);
    }

    public function getClientIpAddress(): ?string
    {
        return $this->clientIpAddress;
    }

    public function setClientIpAddress(?string $clientIpAddress): self
    {
        $this->clientIpAddress = $clientIpAddress;

        return $this;
    }

    /**
     * @param Proposal $entity
     * @param array $columns
     * @return array
     * @throws Exception
     */
    public static function getCSVFormat($entity, array $columns): array
    {
        $parentProposal = $entity->getParentProposal();
        $selectedTrainingAction = $entity->getSelectedTrainingAction();
        $trainingActions = $entity->getTrainingActions();
        $externalIds = join(', ', array_column($trainingActions->toArray(), 'externalId'));
        $sessionStartDate = $entity->getSessionStartDate() ? $entity->getSessionStartDate()->format('d/m/Y') : '';
        $sessionEndDate = $entity->getSessionEndDate() ? $entity->getSessionEndDate()->format('d/m/Y') : '';
        $discountType = $entity->getDiscountType() === ProposalDiscountTypes::NONE()->getValue() ? 'Aucune' : ($entity->getDiscountType() === ProposalDiscountTypes::PERCENT()->getValue() ? 'Variation en %' :
            ($entity->getDiscountType() === ProposalDiscountTypes::AMOUNT()->getValue() ? 'Variation en €' : $entity->getDiscountType() === ProposalDiscountTypes::FIXED()->getValue() && 'Nouveau prix'));

        $fullRow = [
            'PROPOSITION_GENERIQUE_ASSOCIEE' => $parentProposal ? $parentProposal->getCode() : '',
            'ACTION_DE_FORMATION' => $externalIds ?: 'Toutes les actions formations',
            'MONTANT' => $entity->getAmount(),
            'TYPE_REDUCTION' => $discountType,
            'STATUT' => ProposalStates::toFrString($entity->getState()),
            'NOM' => $entity->getLastName(),
            'PRENOM' => $entity->getFirstName(),
            'EMAIL' => $entity->getEmail(),
            'TELEPHONE' => $entity->getPhoneNumber(),
            'CODE' => $entity->getCode(),
            'NOTES' => $entity->getNotes(),
            'LIMITE_D_UTILISATION' => $entity->getLimitUsage() ?? 'Illimité',
            'VALIDATION_AUTOMATIQUE' => $entity->getAutoValidate() ? 'Oui' : 'Non',
            'DATE_DEBUT_SESSION' => $sessionStartDate,
            'DATE_FIN_SESSION' => $sessionEndDate,
            'DATE_EXPIRATION' => $entity->getExpire() ? $entity->getExpire()->format('d/m/Y') : null,
            'DUREE_DE_FORMATION' => $entity->getIndicativeDuration(),
            'DESCRIPTION' => $entity->getDescription(),
            'TAGS' => $entity->getTagsText(),
            'ACTION_DE_FORMATION_SELECTIONNEE' => $selectedTrainingAction ? $selectedTrainingAction->getExternalId() : '',
            'DOSSIER_DE_FORMATION' => $entity->getRegistrationFolder() ? $entity->getRegistrationFolder()->getExternalId() : ''
        ];
        $row = [];
        foreach ($columns as $column) {
            $row[] = $fullRow[$column];
        }
        return $row;
    }

    public function isCodeRequired(): bool
    {
        return $this->codeRequired;
    }

    public function setCodeRequired(bool $codeRequired): self
    {
        $this->codeRequired = $codeRequired;
        return $this;
    }

    /**
     * @Serializer\VirtualProperty()
     * @Serializer\SerializedName("logo")
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "public"})
     */
    public function getLogo(): ?string
    {
        if ($this->getLogoName()) {
            //should be dynamic based on vich_uploader.yml but how
            return "/files/publicFile/$this->logoName";
        } else {
            return null;
        }
    }

    public function getSales(): ?User
    {
        return $this->sales;
    }

    public function setSales(?User $sales): self
    {
        $this->sales = $sales;

        return $this;
    }

    public function getEntityId(): string
    {
        return $this->getCode();
    }

    public function getAmountDiscount(): string
    {
        $getAmountDiscount = '';
        if ($this->getAmount() && $this->getDiscountType()) {
            if ($this->getDiscountType() === ProposalDiscountTypes::FIXED()->getValue()) {
                $getAmountDiscount = 'montant modifié à ' . $this->getAmount() . '€ TTC';
            } else if ($this->getDiscountType() === ProposalDiscountTypes::AMOUNT()->getValue()) {
                $getAmountDiscount = 'montant modifié de ' . $this->getAmount() . '€ TTC';
            } else if ($this->getDiscountType() === ProposalDiscountTypes::PERCENT()->getValue()) {
                $getAmountDiscount = 'montant modifié de ' . $this->getAmount() . '%';
            }
        }
        return $getAmountDiscount;
    }

    public function getTrainingActionsResume(): string
    {
        return ProposalService::getTrainingActionsResume($this->getTrainingActions());
    }

    public function getMetadata(): ?array
    {
        return $this->metadata;
    }

    public function setMetadata(?array $metadata): self
    {
        $this->metadata = $metadata;

        return $this;
    }


    /**
     * @Serializer\VirtualProperty()
     * @Serializer\SerializedName("permalink")
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    public function getPermalink(): string
    {
        $subdomain = !empty($this->getOrganism()->getSubDomain()) ? ($this->getOrganism()->getSubDomain() . '.') : "";
        $port = isset($_ENV['PORT']) && !in_array($_ENV['PORT'], ['80', '443']) ? (':' . $_ENV['PORT']) : '';
        return 'https://' . $subdomain . $_ENV['DOMAIN'] . $port . "/proposition-" . $this->getCode();
    }

    /**
     * @Serializer\VirtualProperty()
     * @Serializer\SerializedName("link")
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "public"})
     */
    public function getLink(): string
    {
        return "https://" . ($this->getOrganism()->getSubDomain() . "." . $_ENV['DOMAIN'] . (isset($_ENV['PORT']) ? (':' . $_ENV['PORT']) : '') . "/funnel/apprenant/proposition/") . $this->getCode();
    }

    /**
     * @Serializer\VirtualProperty()
     * @Serializer\SerializedName("link_commercial")
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    public function getLinkCommercial(): string
    {
        return "https://" . ($this->getOrganism()->getSubDomain() . "." . $_ENV['DOMAIN'] . (isset($_ENV['PORT']) ? (':' . $_ENV['PORT']) : '') . "/funnel/commercial/proposition/") . $this->getCode();
    }
}
