<?php

namespace App\Entity;

use App\Entity\Traits\TimestampableTrait;
use App\Library\utils\EntityHashInterface;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\enums\RegistrationFolderStates;
use App\Library\utils\Tools;
use App\Repository\RegistrationFolderRepository;
use App\Service\DataProviders\KairosAifApiService;
use Beelab\TagBundle\Entity\AbstractTaggable;
use DateTime;
use DateTimeInterface;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Exception;
use Hateoas\Configuration\Annotation as Hateoas;
use J<PERSON>\Serializer\Annotation as Serializer;

/**
 * @implements EntityExportableCSV<RegistrationFolder>
 * @ORM\Entity(repositoryClass=RegistrationFolderRepository::class)
 * @ORM\Table(indexes={
 *     @ORM\Index(name="externalId_idx",columns={"external_id", "organism_id", "type"}),
 *     @ORM\Index(name="rfState_idx",columns={"state"})
 * })
 * @ORM\HasLifecycleCallbacks()
 *
 * @Serializer\ExclusionPolicy("ALL")
 * infos propriétaire
 * @Hateoas\Relation("self", href = "expr('/api/registrationFolders/' ~ object.getExternalId())", exclusion=@Hateoas\Exclusion(groups={"owner", "Default"}))
 * @Hateoas\Relation("validate", href = "expr('/api/registrationFolders/' ~ object.getExternalId() ~ '/validate')", exclusion=@Hateoas\Exclusion(groups={"owner"}))
 * @Hateoas\Relation("inTraining", href = "expr('/api/registrationFolders/' ~ object.getExternalId() ~ '/inTraining')", exclusion=@Hateoas\Exclusion(groups={"owner"}))
 * @Hateoas\Relation("terminate", href = "expr('/api/registrationFolders/' ~ object.getExternalId() ~ '/terminate')", exclusion=@Hateoas\Exclusion(groups={"owner"}))
 * @Hateoas\Relation("serviceDone", href = "expr('/api/registrationFolders/' ~ object.getExternalId() ~ '/serviceDone')", exclusion=@Hateoas\Exclusion(groups={"owner"}))
 * @Hateoas\Relation("refuse", href = "expr('/api/registrationFolders/' ~ object.getExternalId() ~ '/refuse')", exclusion=@Hateoas\Exclusion(groups={"owner"}))
 * @Hateoas\Relation("cancel", href = "expr('/api/registrationFolders/' ~ object.getExternalId() ~ '/cancel')", exclusion=@Hateoas\Exclusion(groups={"owner"}))
 * @Hateoas\Relation("billing", href = "expr('/api/registrationFolders/' ~ object.getExternalId() ~ '/billing')", exclusion=@Hateoas\Exclusion(groups={"owner"}))
 * @Hateoas\Relation("session", href = "expr('/api/sessions/' ~ object.getSession().getExternalId())", exclusion=@Hateoas\Exclusion(groups={"owner", "Default", "attendee"}))
 * @Hateoas\Relation("organism", href = "expr('/api/organisms/' ~ object.getOrganism().getSiret())", attributes={"name" = "expr(object.getOrganism().getName())", "siret" = "expr(object.getOrganism().getSiret())"}, exclusion=@Hateoas\Exclusion(groups={"owner", "Default", "attendee"}))
 * @Hateoas\Relation("payments", href = "expr('/api/payments?registrationFolderId=' ~ object.getExternalId())", exclusion=@Hateoas\Exclusion(groups={"owner"}))
 * @Hateoas\Relation("trainingAction", href = "expr('/api/trainingActions/' ~ object.getSession().getTrainingAction().getExternalId())", exclusion=@Hateoas\Exclusion(groups={"owner"}))
 * @Hateoas\Relation("proposal", href = "expr('/api/proposals/' ~ object.getProposal().getCode())", attributes={"code" = "expr(object.getProposal().getCode())"}, exclusion=@Hateoas\Exclusion(excludeIf = "expr(object.getProposal() === null)", groups={"owner"}))
 * @Hateoas\Relation("certification", href = "expr('/api/certifications/' ~ object.getCertification().getCertifInfo())", attributes={"name" = "expr(object.getCertification().getName())", "certifInfo" = "expr(object.getCertification().getCertifInfo())", "externalId" = "expr(object.getCertification().getExternalId())", "id" = "expr(object.getCertification().getId())", "enabled" = "expr(object.getCertification().getEnabled())"}, exclusion=@Hateoas\Exclusion(excludeIf = "expr(object.getCertification() === null)", groups={"owner", "Default", "attendee"}))
 * @Hateoas\Relation("certificationFolder", href = "expr('/api/certificationFolders/' ~ object.getCertificationFolder().getExternalId())", attributes={"externalId" = "expr(object.getCertificationFolder().getExternalId())"}, exclusion=@Hateoas\Exclusion(excludeIf = "expr(object.getCertificationFolder() === null)", groups={"owner", "Default", "attendee"}))
 * @Hateoas\Relation("inPartnershipWith", href = "expr('/api/organisms/' ~ object.getInPartnershipWith().getSiret())", attributes={"name" = "expr(object.getInPartnershipWith().getName())", "siret" = "expr(object.getInPartnershipWith().getSiret())"}, exclusion=@Hateoas\Exclusion(excludeIf = "expr(object.getInPartnershipWith() === null)", groups={"owner", "Default"}))
 * @Hateoas\Relation("activities", href = "expr('/api/activities/RegistrationFolder/' ~ object.getExternalId())", exclusion=@Hateoas\Exclusion(groups={"owner"})))
 * @Hateoas\Relation("workingContracts", href = "expr('/api/workingContracts?registrationFolderExternalId=' ~ object.getExternalId())", exclusion=@Hateoas\Exclusion(excludeIf = "expr(object.getType() !== 'opcoCfa')", groups={"owner"})))
 * @Hateoas\Relation("currentWorkingContract", href = "expr('/api/workingContracts/' ~ object.getCurrentWorkingContract().getId())", attributes={"financer" = "expr(object.getCurrentWorkingContract().getFinancer())", "state" = "expr(object.getCurrentWorkingContract().getState())", "idDeca" = "expr(object.getCurrentWorkingContract().getExternalIdDeca())", "idTrainingOrganism" = "expr(object.getCurrentWorkingContract().getExternalIdTrainingOrganism())"}, exclusion=@Hateoas\Exclusion(excludeIf = "expr(object.getType() !== 'opcoCfa')", groups={"owner"}))
 **/
class RegistrationFolder extends AbstractTaggable implements EntityHashInterface, EntityExportableCSV
{
    use TimestampableTrait;

    public const CLASSNAME = 'RegistrationFolder';

    /**
     * @ORM\Id()
     * @ORM\GeneratedValue()
     * @ORM\Column(type="integer")
     */
    private int $id;


    /**
     * @ORM\Column(type="string", length=50)
     * @Serializer\Expose()
     * @Serializer\Groups({"owner", "Default", "attendee"})
     */
    private string $type;

    /**
     * @ORM\Column(type="datetime")
     * @Serializer\Expose()
     * @Serializer\Groups({"owner", "Default", "attendee"})
     */
    private ?DateTimeInterface $lastUpdate = null;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\Attendee", inversedBy="registrationFolders")
     * @ORM\JoinColumn(nullable=false)
     * @Serializer\Expose()
     * @Serializer\Groups({"owner", "Default"})
     */
    private Attendee $attendee;

    /**
     * @ORM\Column(type="json")
     */
    private array $rawData = [];

    /**
     * @ORM\Column(type="string", length=50)
     * @Serializer\Expose()
     * @Serializer\Groups({"owner", "Default", "attendee"})
     */
    private ?string $state = null;

    /**
     * @ORM\Column(type="string", length=50)
     * @Serializer\Expose()
     * @Serializer\Groups({"owner"})
     */
    private ?string $attendeeState = null;

    /**
     * @ORM\Column(type="string", length=50)
     * @Serializer\Expose()
     * @Serializer\Groups({"owner"})
     */
    private ?string $billingState = null;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\Session", inversedBy="registrationFolders")
     * @ORM\JoinColumn(nullable=false)
     */
    private Session $session;

    /**
     * @ORM\Column(type="string", length=255, unique=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"owner", "Default", "attendee"})
     */
    private string $externalId;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\Organism", inversedBy="registrationFolders")
     * @ORM\JoinColumn(nullable=false)
     */
    private Organism $organism;

    /**
     * @ORM\Column(type="string", length=10, nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"owner"})
     */
    private ?string $billId = null;

    /**
     * @ORM\Column(type="string", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"owner"})
     */
    private ?string $billNumber = null;

    /**
     * @ORM\Column(type="float", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"owner"})
     */
    private ?float $amountHtNet = null;

    /**
     * @ORM\Column(type="float", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"owner"})
     */
    private ?float $amountToInvoice = null;

    /**
     * @ORM\Column(type="float", nullable=true)
     */
    private ?float $amountVatToInvoice = null;

    /**
     * @ORM\Column(type="float", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"owner"})
     */
    private ?float $amountCGU = null;

    /**
     * @ORM\Column(type="float", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"owner"})
     */
    private ?float $amountTtc = null;

    /**
     * @ORM\Column(type="float", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"owner"})
     */
    private ?float $amountHt = null;

    /**
     * @ORM\Column(type="float", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"owner"})
     */
    private ?float $vatHtAmount5 = null;

    /**
     * @ORM\Column(type="float", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"owner"})
     */
    private ?float $vatAmount5 = null;

    /**
     * @ORM\Column(type="float", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"owner"})
     */
    private ?float $vatHtAmount20 = null;

    /**
     * @ORM\Column(type="float", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"owner"})
     */
    private ?float $vatAmount20 = null;

    /**
     * @ORM\OneToMany(targetEntity=Payment::class, mappedBy="registrationFolder", orphanRemoval=true)
     */
    private Collection $payments;

    /**
     * @ORM\ManyToOne(targetEntity=Certification::class, inversedBy="registrationFolders")
     * @ORM\JoinColumn(nullable=true)
     */
    private ?Certification $certification = null;

    /**
     * @ORM\OneToOne(targetEntity=RegistrationFolderHistory::class, cascade={"persist", "remove"})
     * @ORM\JoinColumn(nullable=false)
     * @Serializer\Expose()
     * @Serializer\Groups({"owner", "Default", "attendee"})
     */
    private RegistrationFolderHistory $history;

    /**
     * Set in EntityEventListener
     * @var array
     */
    private array $trainingActionInfo;

    /**
     * @ORM\OneToOne(targetEntity=Proposal::class, mappedBy="registrationFolder", cascade={"persist", "remove"})
     */
    private ?Proposal $proposal = null;

    /**
     * @ORM\OneToMany(targetEntity=RegistrationFolderFile::class, mappedBy="registrationFolder", orphanRemoval=true, fetch="EAGER")
     * @Serializer\Expose()
     * @Serializer\Groups({"owner"})
     */
    private Collection $files;

    /**
     * @ORM\OneToOne(targetEntity=CertificationFolder::class, inversedBy="registrationFolder", cascade={"persist", "remove"})
     */
    private ?CertificationFolder $certificationFolder = null;

    /**
     * @ORM\ManyToMany(targetEntity=Tag::Class)
     * Display with serializer to get as array[string] and not as objet
     * CANNOT BE TYPED /!\
     * Don't forget to override setTagsText() and change updatedOn as done below!
     */
    protected $tags; // Type must not be defined (as in base class '\Beelab\TagBundle\Entity\AbstractTaggable')

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"owner"})
     */
    private ?string $notes = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"owner"})
     */
    private ?string $description = null;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\Organism")
     */
    private ?Organism $inPartnershipWith = null;

    /**
     * @ORM\Column(type="boolean", options={"default":false})
     */
    private bool $missingData = false;

    /**
     * @ORM\Column(type="integer", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"owner", "Default", "attendee"})
     */
    private ?int $completionRate = null;

    /**
     * @ORM\Column(type="string", length=255)
     * @Serializer\Expose()
     * @Serializer\Groups({"owner"})
     */
    private ?string $controlState;

    /**
     * @ORM\Column(type="float", nullable=true)
     */
    private ?float $absenceDuration = null;

    /**
     * @ORM\ManyToOne(targetEntity=RegistrationFolderReason::class)
     */
    private ?RegistrationFolderReason $terminatedReason = null;

    /**
     * @ORM\Column(type="json", nullable=true)
     * @Serializer\Expose
     * @Serializer\Groups({"owner"})
     */
    private ?array $metadata = [];

    /**
     * @ORM\OneToOne(targetEntity=WorkingContract::class, cascade={"persist", "remove"})
     */
    private ?WorkingContract $currentWorkingContract = null;

    public function __construct()
    {
        $this->payments = new ArrayCollection();
        $this->files = new ArrayCollection();
        parent::__construct(); //needed by tags
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getLastUpdate(): ?DateTimeInterface
    {
        return $this->lastUpdate;
    }

    public function setLastUpdate(DateTimeInterface $lastUpdate): self
    {
        $this->lastUpdate = $lastUpdate;

        return $this;
    }

    public function getAttendee(): ?Attendee
    {
        return $this->attendee;
    }

    public function setAttendee(?Attendee $attendee): self
    {
        $this->attendee = $attendee;

        return $this;
    }

    public function getRawData(): array
    {
        return $this->rawData;
    }

    public function setRawData(array $rawData): self
    {
        $this->rawData = $rawData;

        return $this;
    }

    public function getState(): ?string
    {
        return $this->state;
    }

    public function setState(string $state): self
    {
        $this->state = $state;

        return $this;
    }

    public function getSession(): Session
    {
        return $this->session;
    }

    public function setSession(Session $session): self
    {
        $this->session = $session;

        return $this;
    }

    public function getExternalId(): string
    {
        return $this->externalId;
    }

    public function setExternalId(string $externalId): self
    {
        $this->externalId = $externalId;

        return $this;
    }

    public function getOrganism(): ?Organism
    {
        return $this->organism;
    }

    public function setOrganism(?Organism $organism): self
    {
        $this->organism = $organism;

        return $this;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;
        return $this;
    }

    public function getBillId(): ?string
    {
        return $this->billId;
    }

    public function setBillId(?string $billId): self
    {
        $this->billId = $billId;

        return $this;
    }

    public function getBillNumber(): ?string
    {
        return $this->billNumber;
    }

    public function setBillNumber(?string $billNumber): self
    {
        $this->billNumber = $billNumber;

        return $this;
    }

    public function getAmountHtNet(): ?float
    {
        return $this->amountHtNet;
    }

    public function setAmountHtNet(?float $amountHtNet): self
    {
        $this->amountHtNet = $amountHtNet;

        return $this;
    }

    public function getAmountToInvoice(): ?float
    {
        return $this->amountToInvoice;
    }

    public function setAmountToInvoice(?float $amountToInvoice): self
    {
        $this->amountToInvoice = $amountToInvoice;

        return $this;
    }

    public function getAmountCGU(): ?float
    {
        return $this->amountCGU;
    }

    public function setAmountCGU(?float $amountCGU): self
    {
        $this->amountCGU = $amountCGU;

        return $this;
    }

    public function getAmountTtc(): ?float
    {
        return $this->amountTtc;
    }

    public function setAmountTtc(?float $amountTtc): self
    {
        $this->amountTtc = $amountTtc;

        return $this;
    }

    public function getAmountHt(): ?float
    {
        return $this->amountHt;
    }

    public function setAmountHt(?float $amountHt): self
    {
        $this->amountHt = $amountHt;

        return $this;
    }

    public function getVatHtAmount5(): ?float
    {
        return $this->vatHtAmount5;
    }

    public function setVatHtAmount5(?float $vatHtAmount5): self
    {
        $this->vatHtAmount5 = $vatHtAmount5;

        return $this;
    }

    public function getVatAmount5(): ?float
    {
        return $this->vatAmount5;
    }

    public function setVatAmount5(?float $vatAmount5): self
    {
        $this->vatAmount5 = $vatAmount5;

        return $this;
    }

    public function getVatHtAmount20(): ?float
    {
        return $this->vatHtAmount20;
    }

    public function setVatHtAmount20(?float $vatHtAmount20): self
    {
        $this->vatHtAmount20 = $vatHtAmount20;

        return $this;
    }

    public function getVatAmount20(): ?float
    {
        return $this->vatAmount20;
    }

    public function setVatAmount20(?float $vatAmount20): self
    {
        $this->vatAmount20 = $vatAmount20;

        return $this;
    }

    /**
     * @return Collection|Payment[]
     */
    public function getPayments(): Collection
    {
        return $this->payments;
    }

    public function addPayment(Payment $payment): self
    {
        if (!$this->payments->contains($payment)) {
            $this->payments[] = $payment;
            $payment->setRegistrationFolder($this);
        }

        return $this;
    }

    public function removePayment(Payment $payment): self
    {
        if ($this->payments->contains($payment)) {
            $this->payments->removeElement($payment);
            // set the owning side to null (unless already changed)
            if ($payment->getRegistrationFolder() === $this) {
                $payment->setRegistrationFolder(null);
            }
        }

        return $this;
    }

    public function getTrainingActionInfo(): array
    {
        return $this->trainingActionInfo;
    }

    public function setTrainingActionInfo(array $trainingActionInfo): self
    {
        $this->trainingActionInfo = $trainingActionInfo;
        return $this;
    }

    public function getCertification(): ?Certification
    {
        return $this->certification;
    }

    public function setCertification(?Certification $certification): self
    {
        $this->certification = $certification;

        return $this;
    }

    public function getAttendeeState(): ?string
    {
        return $this->attendeeState;
    }

    public function setAttendeeState(string $attendeeState): self
    {
        $this->attendeeState = $attendeeState;

        return $this;
    }

    public function getBillingState(): ?string
    {
        return $this->billingState;
    }

    public function setBillingState(string $billingState): self
    {
        $this->billingState = $billingState;

        return $this;
    }

    public function getHistory(): RegistrationFolderHistory
    {
        return $this->history;
    }

    public function setHistory(RegistrationFolderHistory $history): self
    {
        $this->history = $history;

        return $this;
    }

    public function getProposal(): ?Proposal
    {
        return $this->proposal;
    }

    public function setProposal(?Proposal $proposal): self
    {
        $this->proposal = $proposal;

        // set (or unset) the owning side of the relation if necessary
        $newRegistrationFolder = null === $proposal ? null : $this;
        if ($proposal->getRegistrationFolder() !== $newRegistrationFolder) {
            $proposal->setRegistrationFolder($newRegistrationFolder);
        }
        $this->copyTags($proposal);

        return $this;
    }

    public function getEntityHash(): string
    {
        return $this->getLastUpdate()->getTimestamp() . $this->getState() . ($this->getCertificationFolder() ? $this->getCertificationFolder()->getState() : 'no-certification-folder') . $this->getBillingState() . $this->getAttendeeState();
    }

    public function getLink(): ?string
    {
        if ($this->getType() === DataProviders::CPF()->getValue()) {
            return $_ENV["CPF_REGISTRATION_FOLDER_CONSULTATION_URI"] . "/" . $this->getExternalId();
        } else {
            return null;
        }
    }

    /**
     * @return Collection|RegistrationFolderFile[]
     */
    public function getFiles(): Collection
    {
        return $this->files;
    }

    public function addFile(RegistrationFolderFile $file): self
    {
        if (!$this->files->contains($file)) {
            $this->files[] = $file;
            $file->setRegistrationFolder($this);
        }

        return $this;
    }

    public function removeFile(RegistrationFolderFile $file): self
    {
        if ($this->files->removeElement($file)) {
            // set the owning side to null (unless already changed)
            if ($file->getRegistrationFolder() === $this) {
                $file->setRegistrationFolder(null);
            }
        }

        return $this;
    }

    public function getCertificationFolder(): ?CertificationFolder
    {
        return $this->certificationFolder;
    }

    public function setCertificationFolder(?CertificationFolder $certificationFolder): self
    {
        $this->certificationFolder = $certificationFolder;
        return $this;
    }

    public function copyTags(AbstractTaggable $objectWithTags)
    {
        /** @var Tag $tag */
        foreach ($objectWithTags->getTags() as $tag) {
            if (!$this->hasTag($tag)) {
                $this->addTag($tag);
            }
        }
        $this->setTagsText($this->getTagsText());
    }

    public function getNotes(): ?string
    {
        return $this->notes;
    }

    public function setNotes(?string $notes): self
    {
        $this->notes = $notes;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): self
    {
        $this->description = $description;

        return $this;
    }

    public function getInPartnershipWith(): ?Organism
    {
        return $this->inPartnershipWith;
    }

    public function setInPartnershipWith(?Organism $inPartnershipWith): self
    {
        $this->inPartnershipWith = $inPartnershipWith;

        return $this;
    }

    // Required on every taggable so that if only tags are changed, they are persisted
    public function setTagsText(?string $tagsText): void
    {
        if ($tagsText !== $this->getTagsText()) {
            $this->updateUpdatedOnTimestamp();
        }
        parent::setTagsText($tagsText);
    }

    /**
     * @return bool
     * @Serializer\VirtualProperty()
     * @Serializer\Groups({"owner"})
     */
    public function isAttendeeJobSeeker(): bool
    {
        return $this->getType() == DataProviders::CPF()->getValue()
            && !empty($this->getRawData()['attendee']['statusPoleEmployment'])
            && in_array($this->getRawData()['attendee']['statusPoleEmployment'], [1, 2, 3]);
    }

    /**
     * @param RegistrationFolder $entity
     * @param array $columns
     * @return array
     * @throws Exception
     */
    public static function getCSVFormat($entity, array $columns): array
    {
        $trainingActionInfo = $entity->getTrainingActionInfo();
        $sessionRawData = $entity->getSession()->getRawData();
        $folderCertification = $entity->getCertification();
        $attendee = $entity->getAttendee();
        $startDate = DateTime::createFromFormat('d/m/Y', $trainingActionInfo['sessionStartDate']) ?: new DateTime($trainingActionInfo['sessionStartDate']);
        $startDateFormatted = $startDate->format('d/m/Y');
        $endDate = DateTime::createFromFormat('d/m/Y', $trainingActionInfo['sessionEndDate']) ?: new DateTime($trainingActionInfo['sessionEndDate']);
        $endDateFormatted = $endDate->format('d/m/Y');
        $fullRow = [
            'NUMERO_DOSSIER' => $entity->getExternalId(),
            'NOM' => $attendee->getLastName(),
            'PRENOM' => $attendee->getFirstName(),
            'EMAIL' => $attendee->getEmail(),
            'TELEPHONE' => $attendee->getPhoneNumber() ?? $attendee->getPhoneFixed(),
            'MONTANT_FORMATION' => $trainingActionInfo['totalIncl'],
            'INTITULE_FORMATION' => $trainingActionInfo['title'],
            'DUREE_FORMATION' => $trainingActionInfo['duration'] ?? '',
            'CODE_CERTIF' => $folderCertification ? $folderCertification->getCertifInfo() : '',
            'INTITULE_CERTIF' => $folderCertification ? $folderCertification->getName() : '',
            'STATUT_DOSSIER' => RegistrationFolderStates::toFrString($entity->getState()),
            'DATE_DEBUT_SESSION' => $startDateFormatted,
            'DATE_FIN_SESSION' => $endDateFormatted,
            'NUMERO_FORMATION' => $sessionRawData['trainingId'] ?? '',
            'NUMERO_ACTION' => $sessionRawData['actionId'] ?? '',
            'NUMERO_SESSION' => $sessionRawData['id'] ?? '',
            'ESPACE_APPRENANT' => $entity->getAttendeeLink()
        ];
        $row = [];
        foreach ($columns as $column) {
            $row[] = $fullRow[$column];
        }
        return $row;
    }

    public function isMissingData(): ?bool
    {
        return $this->missingData;
    }

    public function setMissingData(bool $missingData): self
    {
        $this->missingData = $missingData;

        return $this;
    }

    public function getAmountVatToInvoice(): ?float
    {
        return $this->amountVatToInvoice;
    }

    public function setAmountVatToInvoice(?float $amountVatToInvoice): self
    {
        $this->amountVatToInvoice = $amountVatToInvoice;

        return $this;
    }

    public function getCompletionRate(): ?int
    {
        return $this->completionRate;
    }

    public function setCompletionRate(int $completionRate): self
    {
        $this->completionRate = $completionRate;

        return $this;
    }

    public function getControlState(): ?string
    {
        return $this->controlState;
    }

    public function setControlState(?string $controlState): self
    {
        $this->controlState = $controlState;

        return $this;
    }

    public function getAbsenceDuration(): ?float
    {
        return $this->absenceDuration;
    }

    public function setAbsenceDuration(?float $absenceDuration): self
    {
        $this->absenceDuration = $absenceDuration;

        return $this;
    }

    public function getTerminatedReason(): ?RegistrationFolderReason
    {
        return $this->terminatedReason;
    }

    public function setTerminatedReason(?RegistrationFolderReason $terminatedReason): self
    {
        $this->terminatedReason = $terminatedReason;

        return $this;
    }

    public function getMetadata(): ?array
    {
        return $this->metadata;
    }

    public function setMetadata(?array $metadata): self
    {
        $this->metadata = $metadata;
        return $this;
    }


    /**
     * @Serializer\VirtualProperty()
     * @Serializer\SerializedName("attendeeLink")
     * @Serializer\Expose()
     * @Serializer\Groups({"owner"})
     */
    public function getAttendeeLink(): string
    {
        // This link works only in prod with nginx, real target links are:
        // https://<subdomain>.wedof.fr/apprenant/formation/dossier/<externalId>
        // https://<subdomain>.wedof.fr/apprenant/formation/dossier/<externalId>/files/<fileId>;
        $subdomain = !empty($this->getOrganism()->getSubDomain()) ? ($this->getOrganism()->getSubDomain() . '.') : "";
        $port = isset($_ENV['PORT']) && !in_array($_ENV['PORT'], ['80', '443']) ? (':' . $_ENV['PORT']) : '';
        return 'https://' . $subdomain . $_ENV['DOMAIN'] . $port . '/apprenant-' . $this->getExternalId();
    }

    /**
     * @Serializer\VirtualProperty()
     * @Serializer\SerializedName("surveyLink")
     * @Serializer\Expose()
     * @Serializer\Groups({"owner"})
     */
    public function getSurveyLink(): string
    {
        // This link works only in prod with nginx, real target links are:
        // https://<subdomain>.wedof.fr/apprenant/formation/dossier/<externalId>/enquete
        $subdomain = !empty($this->getOrganism()->getSubDomain()) ? ($this->getOrganism()->getSubDomain() . '.') : "";
        $port = isset($_ENV['PORT']) && !in_array($_ENV['PORT'], ['80', '443']) ? (':' . $_ENV['PORT']) : '';
        return 'https://' . $subdomain . $_ENV['DOMAIN'] . $port . '/apprenant-' . $this->getExternalId() . '-enquete';
    }

    /**
     * @Serializer\VirtualProperty()
     * @Serializer\SerializedName("dataProviderId")
     * @Serializer\Expose()
     * @Serializer\Groups({"owner"})
     */
    public function getDataProviderId(): ?string
    {
        $dataProviderId = null;
        $externalId = $this->getExternalId();
        if (!empty($externalId)) {
            switch ($this->getType()) {
                case DataProviders::CPF():
                    $dataProviderId = $externalId;
                    break;
                case DataProviders::KAIROS_AIF():
                    $dataProviderId = Tools::removePrefix($externalId, KairosAifApiService::EXTERNAL_ID_PREFIX);
                    break;
                default:
            }
        }
        return $dataProviderId;
    }

    public function getEntityId(): string
    {
        return $this->getExternalId();
    }

    /**
     * @Serializer\VirtualProperty()
     * @Serializer\SerializedName("permalink")
     * @Serializer\Expose()
     * @Serializer\Groups({"owner"})
     */
    public function getPermalink(): string
    {
        // This link works only in prod with nginx
        $subdomain = !empty($this->getOrganism()->getSubDomain()) ? ($this->getOrganism()->getSubDomain() . '.') : "";
        $port = isset($_ENV['PORT']) && !in_array($_ENV['PORT'], ['80', '443']) ? (':' . $_ENV['PORT']) : '';
        return 'https://' . $subdomain . $_ENV['DOMAIN'] . $port . '/dossier-formation-' . $this->getExternalId();
    }

    /**
     * @Serializer\VirtualProperty()
     * @Serializer\Expose()
     * @Serializer\Groups({"owner", "Default"})
     */
    public function isAllowActions(): bool
    {
        $organism = $this->getOrganism();
        $subscription = $organism->getSubscription();
        return $subscription && !($subscription->isRegistrationFoldersNumberLimitExceeded() && $subscription->getLastRegistrationFolderId() && $subscription->getLastRegistrationFolderId() < $this->id);
    }

    public function getTrainingSkillSets(): Collection
    {
        return $this->getSession()->getTrainingAction()->getTraining()->getSkillSets();
    }

    public function getSessionCity(): string
    {
        $city = "À distance";
        $trainingActionRawData = $this->getSession()->getTrainingAction()->getRawData();
        if (isset($trainingActionRawData['trainingAddress']['city'])) {
            $city = $trainingActionRawData['trainingAddress']['city'];
        }
        return $city;
    }

    public function getCompletedHours(): ?float
    {
        $trainingDuration = $this->getTrainingActionInfo()['duration'];
        $absenceDuration = $this->getAbsenceDuration() ?? $this->computeAbsenceDuration();

        return $trainingDuration !== null && $absenceDuration !== null ? $trainingDuration - $absenceDuration : null;
    }

    public function computeAbsenceDuration(array $rawData = null): ?float
    {
        $absenceDuration = null;
        $trainingActionInfo = !empty($rawData) ? $rawData['trainingActionInfo'] : $this->rawData['trainingActionInfo'];
        $dureeAbsenceOF = !empty($trainingActionInfo['dureeAbsenceOF']) ? $trainingActionInfo['dureeAbsenceOF'] : null;

        if ($dureeAbsenceOF !== null) {
            $absenceUnitMap = [
                1 => 1,
                2 => 4,
                3 => 8
            ];
            $absenceUnit = !empty($trainingActionInfo['uniteAbsenceOF']) ? $trainingActionInfo['uniteAbsenceOF'] : 1;
            $absenceDuration = $dureeAbsenceOF * $absenceUnitMap[$absenceUnit];
        } else if (!$this->getTerminatedReason() || !empty($trainingActionInfo['reason'])) { // implique que la formation est terminée et que l'on peut s'appuyer sur le completionRate
            $trainingDuration = $trainingActionInfo['indicativeDuration'] ?? ($trainingActionInfo['averageDuration'] ?? null);
            $completionRate = $this->getCompletionRate() ?? ($trainingActionInfo['trainingCompletionRate'] ?? null);
            $absenceDuration = $trainingDuration != null && $completionRate != null ? round($trainingDuration * (100 - $completionRate) / 100) : null;
        }

        return $absenceDuration;
    }

    /**
     * @return int
     * @throws Exception
     * @Serializer\VirtualProperty()
     * @Serializer\Groups({"owner", "Default", "attendee"})
     */
    public function getExpectedCompletionRate(): ?int
    {
        $now = new DateTime('now');
        $startDate = DateTime::createFromFormat('d/m/Y', $this->trainingActionInfo['sessionStartDate']) ?: new DateTime($this->trainingActionInfo['sessionStartDate']);
        $endDate = DateTime::createFromFormat('d/m/Y', $this->trainingActionInfo['sessionEndDate']) ?: new DateTime($this->trainingActionInfo['sessionEndDate']);
        if ($startDate && $endDate && $startDate <= new DateTime('now')) {
            if ($endDate <= $now || $endDate->diff($startDate)->format("%a") == 0) {
                return 100;
            } else {
                $total = $endDate->diff($startDate)->format("%a");
                $left = $endDate->diff($now)->format("%a");
                return intval(round($left * 100 / $total));
            }
        } else {
            return 0;
        }
    }

    public function getCurrentWorkingContract(): ?WorkingContract
    {
        return $this->currentWorkingContract;
    }

    public function setCurrentWorkingContract(?WorkingContract $currentWorkingContract): self
    {
        $this->currentWorkingContract = $currentWorkingContract;

        return $this;
    }
}
