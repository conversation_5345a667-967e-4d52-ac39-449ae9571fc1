<?php

namespace App\Entity;

use App\Repository\CertificationPartnerHistoryRepository;
use DateTimeInterface;
use Doctrine\ORM\Mapping as ORM;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;

/**
 * @ORM\Entity(repositoryClass=CertificationPartnerHistoryRepository::class)
 */
class CertificationPartnerHistory
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Serializer\Exclude
     */
    private int $id;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Groups({"owner"})
     */
    private ?DateTimeInterface $draftDate = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Groups({"owner"})
     */
    private ?DateTimeInterface $processingDate = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Groups({"owner"})
     */
    private ?DateTimeInterface $activeDate = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Groups({"owner"})
     */
    private ?DateTimeInterface $abortedDate = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Groups({"owner"})
     */
    private ?DateTimeInterface $refusedDate = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Groups({"owner"})
     */
    private ?DateTimeInterface $revokedDate = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Groups({"owner"})
     */
    private ?DateTimeInterface $suspendedDate = null;

    public function getId(): int
    {
        return $this->id;
    }

    public function getDraftDate(): ?DateTimeInterface
    {
        return $this->draftDate;
    }

    public function setDraftDate(?DateTimeInterface $draftDate): self
    {
        $this->draftDate = $draftDate;
        return $this;
    }

    public function getProcessingDate(): ?DateTimeInterface
    {
        return $this->processingDate;
    }

    public function setProcessingDate(?DateTimeInterface $processingDate): self
    {
        $this->processingDate = $processingDate;
        return $this;
    }

    public function getActiveDate(): ?DateTimeInterface
    {
        return $this->activeDate;
    }

    public function setActiveDate(?DateTimeInterface $activeDate): self
    {
        $this->activeDate = $activeDate;
        return $this;
    }

    public function getAbortedDate(): ?DateTimeInterface
    {
        return $this->abortedDate;
    }

    public function setAbortedDate(?DateTimeInterface $abortedDate): self
    {
        $this->abortedDate = $abortedDate;
        return $this;
    }

    public function getRefusedDate(): ?DateTimeInterface
    {
        return $this->refusedDate;
    }

    public function setRefusedDate(?DateTimeInterface $refusedDate): self
    {
        $this->refusedDate = $refusedDate;
        return $this;
    }

    public function getRevokedDate(): ?DateTimeInterface
    {
        return $this->revokedDate;
    }

    public function setRevokedDate(?DateTimeInterface $revokedDate): self
    {
        $this->revokedDate = $revokedDate;
        return $this;
    }

    public function getSuspendedDate(): ?DateTimeInterface
    {
        return $this->suspendedDate;
    }

    public function setSuspendedDate(?DateTimeInterface $suspendedDate): self
    {
        $this->suspendedDate = $suspendedDate;
        return $this;
    }
}
