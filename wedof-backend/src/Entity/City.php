<?php

namespace App\Entity;

use App\Repository\CityRepository;
use Doctrine\ORM\Mapping as ORM;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;

/**
 * @ORM\Entity(repositoryClass=CityRepository::class)
 */
class City
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Serializer\Exclude()
     */
    private int $id;

    /**
     * @ORM\Column(type="string", length=70)
     */
    private string $name;

    /**
     * @ORM\Column(type="string", length=10)
     */
    private string $cog;

    /**
     * @ORM\Column(type="string", length=10, nullable=true)
     */
    private ?string $postalCode = null;

    public function getId(): int
    {
        return $this->id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getCog(): string
    {
        return $this->cog;
    }

    public function getPostalCode(): ?string
    {
        return $this->postalCode;
    }
}
