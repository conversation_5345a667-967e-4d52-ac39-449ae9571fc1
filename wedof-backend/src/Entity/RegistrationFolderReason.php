<?php

namespace App\Entity;

use App\Repository\RegistrationFolderReasonRepository;
use Doctrine\ORM\Mapping as ORM;
use <PERSON><PERSON>s\Configuration\Annotation as Hateoas;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;

/**
 * @ORM\Entity(repositoryClass=RegistrationFolderReasonRepository::class)
 * @Serializer\ExclusionPolicy("ALL")
 * @Hateoas\Relation("refused", href = "expr('/api/registrationFoldersReasons?type=refused')")
 * @Hateoas\Relation("canceled", href = "expr('/api/registrationFoldersReasons?type=canceled')")
 * @Hateoas\Relation("terminated", href = "expr('/api/registrationFoldersReasons?type=terminated')")
 */
class RegistrationFolderReason
{
    public const CATEGORY_TERMINATED = 'quit';
    public const CATEGORY_REFUSED = 'deny';
    public const CATEGORY_CANCELED = 'cancel';

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     *
     */
    private int $id;

    /**
     * @ORM\Column(type="string", length=255)
     * @Serializer\Expose
     */
    private string $category;

    /**
     * @ORM\Column(type="string", length=255)
     * @Serializer\Expose
     */
    private string $code;

    /**
     * @ORM\Column(type="text")
     * @Serializer\Expose
     */
    private string $label;

    /**
     * @Serializer\Expose
     * @Serializer\SerializedName("obsolete")
     * @ORM\Column(type="boolean")
     */
    private bool $obsolescence;

    public function getId(): int
    {
        return $this->id;
    }

    public function getCategory(): ?string
    {
        return $this->category;
    }

    public function setCategory(string $category): self
    {
        $this->category = $category;

        return $this;
    }

    public function getCode(): ?string
    {
        return $this->code;
    }

    public function setCode(string $code): self
    {
        $this->code = $code;

        return $this;
    }

    public function getLabel(): ?string
    {
        return $this->label;
    }

    public function setLabel(string $label): self
    {
        $this->label = $label;

        return $this;
    }

    public function getObsolescence(): ?bool
    {
        return $this->obsolescence;
    }

    public function setObsolescence(bool $obsolescence): self
    {
        $this->obsolescence = $obsolescence;

        return $this;
    }
}
