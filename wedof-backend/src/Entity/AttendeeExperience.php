<?php

namespace App\Entity;

use App\Entity\Traits\TimestampableTrait;
use App\Repository\AttendeeExperienceRepository;
use DateTimeInterface;
use Doctrine\ORM\Mapping as ORM;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;

/**
 * @ORM\Entity(repositoryClass=AttendeeExperienceRepository::class)
 * @ORM\HasLifecycleCallbacks()
 */
class AttendeeExperience
{

    use TimestampableTrait;

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private int $id;

    /**
     * @ORM\ManyToOne(targetEntity=Attendee::class, inversedBy="experiences")
     * @ORM\JoinColumn(nullable=false)
     * @Serializer\Exclude()
     */
    private Attendee $attendee;

    /**
     * @ORM\Column(type="integer", nullable=true)
     */
    private ?int $qualification = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private ?string $certificationName = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private ?string $job = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private ?string $companyName = null;

    /**
     * @ORM\Column(type="integer", nullable=true)
     */
    private ?int $salaryYearly = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private ?string $situation = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private ?string $contractType = null;

    /**
     * @ORM\Column(type="boolean", nullable=true)
     */
    private ?bool $executiveStatus = null;

    /**
     * @ORM\Column(type="date", nullable=true)
     */
    private ?DateTimeInterface $startDate = null;

    /**
     * @ORM\Column(type="date", nullable=true)
     */
    private ?DateTimeInterface $endDate = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getAttendee(): ?Attendee
    {
        return $this->attendee;
    }

    public function setAttendee(?Attendee $attendee): self
    {
        $this->attendee = $attendee;

        return $this;
    }

    public function getQualification(): ?int
    {
        return $this->qualification;
    }

    public function setQualification(?int $qualification): self
    {
        $this->qualification = $qualification;

        return $this;
    }

    public function getCertificationName(): ?string
    {
        return $this->certificationName;
    }

    public function setCertificationName(?string $certificationName): self
    {
        $this->certificationName = $certificationName;

        return $this;
    }

    public function getJob(): ?string
    {
        return $this->job;
    }

    public function setJob(?string $job): self
    {
        $this->job = $job;

        return $this;
    }

    public function getCompanyName(): ?string
    {
        return $this->companyName;
    }

    public function setCompanyName(?string $companyName): self
    {
        $this->companyName = $companyName;

        return $this;
    }

    public function getSalaryYearly(): ?int
    {
        return $this->salaryYearly;
    }

    public function setSalaryYearly(?int $salaryYearly): self
    {
        $this->salaryYearly = $salaryYearly;

        return $this;
    }

    public function getSituation(): ?string
    {
        return $this->situation;
    }

    public function setSituation(?string $situation): self
    {
        $this->situation = $situation;

        return $this;
    }

    public function getContractType(): ?string
    {
        return $this->contractType;
    }

    public function setContractType(?string $contractType): self
    {
        $this->contractType = $contractType;

        return $this;
    }

    public function isExecutiveStatus(): ?bool
    {
        return $this->executiveStatus;
    }

    public function setExecutiveStatus(?bool $executiveStatus): self
    {
        $this->executiveStatus = $executiveStatus;

        return $this;
    }

    public function getStartDate(): ?DateTimeInterface
    {
        return $this->startDate;
    }

    public function setStartDate(?DateTimeInterface $startDate): self
    {
        $this->startDate = $startDate;

        return $this;
    }

    public function getEndDate(): ?DateTimeInterface
    {
        return $this->endDate;
    }

    public function setEndDate(?DateTimeInterface $endDate): self
    {
        $this->endDate = $endDate;

        return $this;
    }
}
