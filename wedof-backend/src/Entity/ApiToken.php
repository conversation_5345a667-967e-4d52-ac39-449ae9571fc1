<?php

namespace App\Entity;

use App\Repository\ApiTokenRepository;
use DateTimeInterface;
use Doctrine\ORM\Mapping as ORM;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;

/**
 * @ORM\Entity(repositoryClass=ApiTokenRepository::class)
 * @Serializer\ExclusionPolicy("ALL")
 */
class ApiToken
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Serializer\Expose
     */
    private int $id;

    /**
     * @ORM\Column(type="string", length=255)
     * @Serializer\Expose
     */
    private string $token;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\User", inversedBy="apiTokens")
     * @ORM\JoinColumn(nullable=false)
     */
    private User $user;


    /**
     * @ORM\Column(type="string", length=255)
     * @Serializer\Expose
     */
    private string $name;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Expose
     */
    private ?DateTimeInterface $lastUsed = null;

    public function getId(): int
    {
        return $this->id;
    }

    public function getToken(): string
    {
        return $this->token;
    }

    public function setToken(string $token): self
    {
        $this->token = $token;

        return $this;
    }

    public function getUser(): User
    {
        return $this->user;
    }

    public function setUser(User $user): self
    {
        $this->user = $user;

        return $this;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getLastUsed(): ?DateTimeInterface
    {
        return $this->lastUsed;
    }

    public function setLastUsed(?DateTimeInterface $lastUsed): self
    {
        $this->lastUsed = $lastUsed;

        return $this;
    }
}
