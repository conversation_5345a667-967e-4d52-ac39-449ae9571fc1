<?php

namespace App\Entity;

use App\Repository\PaymentRepository;
use DateTimeInterface;
use Doctrine\ORM\Mapping as ORM;
use <PERSON><PERSON>s\Configuration\Annotation as Hateoas;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;

/**
 * @ORM\Entity(repositoryClass=PaymentRepository::class)
 *
 * @Hateoas\Relation("self", href = "expr('/api/payments/' ~ object.getId())")
 * @Hateoas\Relation("registrationFolder", href = "expr('/api/registrationFolders/' ~ object.getRegistrationFolder().getExternalId())", attributes={"externalId" = "expr(object.getRegistrationFolder().getExternalId())", "type" = "expr(object.getRegistrationFolder().getType())", "state" = "expr(object.getRegistrationFolder().getState())"})
 * @Serializer\ExclusionPolicy("ALL")
 */
class Payment
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Serializer\Expose
     */
    private int $id;

    /**
     * @ORM\Column(type="string", length=255)
     * @Serializer\Expose
     */
    private string $state;

    /**
     * @ORM\Column(type="float")
     * @Serializer\Expose
     */
    private float $amount;

    /**
     * @ORM\Column(type="date", nullable=true)
     * @Serializer\Expose
     */
    private ?DateTimeInterface $scheduledDate = null;

    /**
     * @ORM\Column(type="string", length=255)
     * @Serializer\Expose
     */
    private string $billNumber;

    /**
     * @ORM\Column(type="integer", nullable=true)
     */
    private ?int $transactionNumber = null;

    /**
     * @ORM\Column(type="string", length=255)
     * @Serializer\Expose
     */
    private string $type;

    /**
     * @ORM\Column(type="json")
     */
    private array $rawData = [];

    /**
     * @ORM\Column(type="date")
     * @Serializer\Expose
     */
    private DateTimeInterface $lastUpdate;

    /**
     * @ORM\ManyToOne(targetEntity=Organism::class, inversedBy="payments")
     * @ORM\JoinColumn(nullable=false)
     */
    private Organism $organism;

    /**
     * @ORM\ManyToOne(targetEntity=RegistrationFolder::class, inversedBy="payments")
     * @ORM\JoinColumn(nullable=false)
     */
    private RegistrationFolder $registrationFolder;

    public function getId(): int
    {
        return $this->id;
    }

    public function getRegistrationFolder(): RegistrationFolder
    {
        return $this->registrationFolder;
    }

    public function setRegistrationFolder(RegistrationFolder $registrationFolder): self
    {
        $this->registrationFolder = $registrationFolder;

        return $this;
    }

    public function getState(): string
    {
        return $this->state;
    }

    public function setState(string $state): self
    {
        $this->state = $state;

        return $this;
    }

    public function getAmount(): float
    {
        return $this->amount;
    }

    public function setAmount(float $amount): self
    {
        $this->amount = $amount;

        return $this;
    }

    public function getScheduledDate(): ?DateTimeInterface
    {
        return $this->scheduledDate;
    }

    public function setScheduledDate(?DateTimeInterface $scheduledDate): self
    {
        $this->scheduledDate = $scheduledDate;

        return $this;
    }

    public function getBillNumber(): string
    {
        return $this->billNumber;
    }

    public function setBillNumber(string $billNumber): self
    {
        $this->billNumber = $billNumber;

        return $this;
    }

    public function getTransactionNumber(): ?int
    {
        return $this->transactionNumber;
    }

    public function setTransactionNumber(?int $transactionNumber): self
    {
        $this->transactionNumber = $transactionNumber;

        return $this;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getRawData(): array
    {
        return $this->rawData;
    }

    public function setRawData(array $rawData): self
    {
        $this->rawData = $rawData;

        return $this;
    }

    public function getLastUpdate(): DateTimeInterface
    {
        return $this->lastUpdate;
    }

    public function setLastUpdate(DateTimeInterface $lastUpdate): self
    {
        $this->lastUpdate = $lastUpdate;

        return $this;
    }

    public function getOrganism(): Organism
    {
        return $this->organism;
    }

    public function setOrganism(Organism $organism): self
    {
        $this->organism = $organism;

        return $this;
    }
}
