<?php

namespace App\Entity;

use App\Entity\Traits\TimestampableTrait;
use App\Repository\RegistrationFolderFileRepository;
use Doctrine\ORM\Mapping as ORM;
use Hateoas\Configuration\Annotation as Hateoas;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;
use Symfony\Component\HttpFoundation\File\File;
use Vich\UploaderBundle\Mapping\Annotation as Vich;

/**
 * @ORM\Entity(repositoryClass=RegistrationFolderFileRepository::class)
 * @ORM\HasLifecycleCallbacks()
 * @Serializer\ExclusionPolicy("ALL")
 * @Vich\Uploadable
 *
 * @Hateoas\Relation("registrationFolder", href = "expr('/api/registrationFolders/' ~ object.getRegistrationFolder().getExternalId())", exclusion=@Hateoas\Exclusion(groups={"owner", "Default"}))
 */
class RegistrationFolderFile
{
    use TimestampableTrait;

    const CLASSNAME = 'RegistrationFolderFile';

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Serializer\Expose
     * @Serializer\Groups({"owner", "Default", "attendee"})
     */
    private int $id;

    /**
     * @ORM\Column(type="integer")
     * @Serializer\Expose
     * @Serializer\Groups({"owner", "Default", "attendee"})
     */
    private string $typeId;

    /**
     * @Vich\UploadableField(mapping="registrationFolderFile", fileNameProperty="filePath", originalName="fileName", mimeType="fileType")
     * @var File|null
     * @Serializer\Groups({"owner", "Default", "attendee"})
     */
    public ?File $file = null;

    /**
     * @ORM\Column(type="string", length=255)
     * @Serializer\Expose
     * @Serializer\Groups({"owner", "Default", "attendee"})
     */
    private string $fileName;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Groups({"owner", "Default", "attendee"})
     */
    private ?string $filePath = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Expose
     * @Serializer\Groups({"owner", "Default", "attendee"})
     */
    private ?string $link = null;

    /**
     * @ORM\Column(type="string", length=255)
     * @Serializer\Expose
     * @Serializer\Groups({"owner", "Default", "attendee"})
     */
    private string $fileType;

    /**
     * @ORM\ManyToOne(targetEntity=RegistrationFolder::class, inversedBy="files")
     * @ORM\JoinColumn(nullable=false)
     */
    private ?RegistrationFolder $registrationFolder;

    /**
     * @ORM\Column(type="string", length=255)
     * @Serializer\Expose
     * @Serializer\Groups({"owner", "Default", "attendee"})
     */
    private string $state;

    /**
     * @ORM\Column(type="text", nullable=true)
     * @Serializer\Expose
     * @Serializer\Groups({"owner", "Default", "attendee"})
     */
    private ?string $comment = null;

    /**
     * @ORM\Column(type="string", length=30, options={"default" : "notGenerated"})
     * @Serializer\Expose()
     * @Serializer\Groups({"owner", "Default", "attendee"})
     */
    private string $generationState = 'notGenerated';

    /**
     * @ORM\Column(type="string", length=30, options={"default" : "notRequired"})
     * @Serializer\Expose()
     * @Serializer\Groups({"owner", "Default", "attendee"})
     */
    private string $signedState = 'notRequired';

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @return int
     */
    public function getTypeId(): int
    {
        return $this->typeId;
    }

    /**
     * @param int $typeId
     * @return $this
     */
    public function setTypeId(int $typeId): self
    {
        $this->typeId = $typeId;

        return $this;
    }

    /**
     * @return string|null
     */
    public function getFilePath(): ?string
    {
        return $this->filePath;
    }

    /**
     * @param string|null $filePath
     * @return $this
     */
    public function setFilePath(?string $filePath): self
    {
        $this->filePath = $filePath;

        return $this;
    }

    /**
     * @return string|null
     */
    public function getLink(): ?string
    {
        return $this->link;
    }

    /**
     * @param string|null $link
     * @return $this
     */
    public function setLink(?string $link): self
    {
        $this->link = $link;

        return $this;
    }

    /**
     * @return string
     */
    public function getFileName(): string
    {
        return $this->fileName;
    }

    /**
     * @param string|null $fileName
     * @return $this
     */
    public function setFileName(?string $fileName): self
    {
        $this->fileName = (string)$fileName; //https://github.com/dustin10/VichUploaderBundle/issues/1117

        return $this;
    }

    /**
     * @return string
     */
    public function getFileType(): string
    {
        return $this->fileType;
    }

    /**
     * @param string|null $fileType
     * @return $this
     */
    public function setFileType(?string $fileType): self
    {
        $this->fileType = (string)$fileType;//https://github.com/dustin10/VichUploaderBundle/issues/1117

        return $this;
    }

    /**
     * @param File|null $file
     */
    public function setFile(?File $file = null): void
    {
        $this->file = $file;

        if (null !== $file) {
            // It is required that at least one field changes if you are using doctrine
            // otherwise the event listeners won't be called and the file is lost
            $this->updateUpdatedOnTimestamp();
        }
    }

    /**
     * @return RegistrationFolder|null
     */
    public function getRegistrationFolder(): ?RegistrationFolder
    {
        return $this->registrationFolder;
    }

    /**
     * @param RegistrationFolder|null $registrationFolder
     * @return $this
     */
    public function setRegistrationFolder(?RegistrationFolder $registrationFolder): self
    {
        $this->registrationFolder = $registrationFolder;

        return $this;
    }

    public function getState(): string
    {
        return $this->state;
    }

    public function setState(string $state): self
    {
        $this->state = $state;

        return $this;
    }

    public function getComment(): ?string
    {
        return $this->comment;
    }

    public function setComment(?string $comment): self
    {
        $this->comment = $comment;

        return $this;
    }

    /**
     * @Serializer\VirtualProperty()
     * @Serializer\SerializedName("permalink")
     * @Serializer\Expose()
     * @Serializer\Groups({"owner", "Default", "attendee"})
     */
    public function getPermalink(): string
    {
        $registrationFolder = $this->getRegistrationFolder();
        $subdomain = !empty($registrationFolder->getOrganism()->getSubDomain()) ? ($registrationFolder->getOrganism()->getSubDomain() . '.') : "";
        $port = isset($_ENV['PORT']) && !in_array($_ENV['PORT'], ['80', '443']) ? (':' . $_ENV['PORT']) : '';
        return 'https://' . $subdomain . $_ENV['DOMAIN'] . $port . '/apprenant-' . $this->registrationFolder->getExternalId() . '-document-' . $this->getTypeId();
    }

    public function getEntityId(): string
    {
        return $this->getId();
    }

    public function getGenerationState(): string
    {
        return $this->generationState;
    }

    public function setGenerationState(string $generationState): self
    {
        $this->generationState = $generationState;

        return $this;
    }

    public function getSignedState(): string
    {
        return $this->signedState;
    }

    public function setSignedState(string $signedState): self
    {
        $this->signedState = $signedState;

        return $this;
    }
}
