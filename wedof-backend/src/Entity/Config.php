<?php

namespace App\Entity;

use App\Repository\ConfigRepository;
use DateTimeInterface;
use Doctrine\ORM\Mapping as ORM;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;

/**
 * @ORM\Entity(repositoryClass=ConfigRepository::class)
 *
 * @Serializer\ExclusionPolicy("ALL")
 */
class Config
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private int $id;

    /**
     * @ORM\Column(type="date", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"sessionMinDates"})
     */
    private ?DateTimeInterface $cpfSessionMinDate;

    /**
     * @ORM\Column(type="date", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"sessionMinDates"})
     */
    private ?DateTimeInterface $poleEmploiSessionMinDate;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     */
    private ?DateTimeInterface $lastUpdate;

    /**
     * @ORM\Column(type="boolean")
     */
    private bool $cpfApiEnabled = true;

    /**
     * @ORM\Column(type="boolean")
     */
    private bool $mesDemarchesAuthEnabled = true;

    /**
     * @ORM\Column(type="boolean", options={"default" : true})
     */
    private bool $mesDemarchesAuthFromWedofEnabled = true;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getCpfSessionMinDate(): ?DateTimeInterface
    {
        return $this->cpfSessionMinDate;
    }

    public function setCpfSessionMinDate(?DateTimeInterface $cpfSessionMinDate): self
    {
        $this->cpfSessionMinDate = $cpfSessionMinDate;

        return $this;
    }

    public function getPoleEmploiSessionMinDate(): ?DateTimeInterface
    {
        return $this->poleEmploiSessionMinDate;
    }

    public function setPoleEmploiSessionMinDate(?DateTimeInterface $poleEmploiSessionMinDate): self
    {
        $this->poleEmploiSessionMinDate = $poleEmploiSessionMinDate;

        return $this;
    }

    public function getLastUpdate(): ?DateTimeInterface
    {
        return $this->lastUpdate;
    }

    public function setLastUpdate(?DateTimeInterface $lastUpdate): self
    {
        $this->lastUpdate = $lastUpdate;

        return $this;
    }

    public function isCpfApiEnabled(): bool
    {
        return $this->cpfApiEnabled;
    }

    public function setCpfApiEnabled(bool $cpfApiEnabled): self
    {
        $this->cpfApiEnabled = $cpfApiEnabled;

        return $this;
    }

    public function isMesDemarchesAuthEnabled(): bool
    {
        return $this->mesDemarchesAuthEnabled;
    }

    public function setMesDemarchesAuthEnabled(bool $mesDemarchesAuthEnabled): self
    {
        $this->mesDemarchesAuthEnabled = $mesDemarchesAuthEnabled;

        return $this;
    }

    public function isMesDemarchesAuthFromWedofEnabled(): bool
    {
        return $this->mesDemarchesAuthFromWedofEnabled;
    }

    public function setMesDemarchesAuthFromWedofEnabled(bool $mesDemarchesAuthFromWedofEnabled): self
    {
        $this->mesDemarchesAuthFromWedofEnabled = $mesDemarchesAuthFromWedofEnabled;

        return $this;
    }
}
