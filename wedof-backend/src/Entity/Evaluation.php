<?php

namespace App\Entity;

use App\Repository\EvaluationRepository;
use DateTimeInterface;
use Doctrine\ORM\Mapping as ORM;
use Hateoas\Configuration\Annotation as Hateoas;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;

/**
 * @ORM\Table(name="evaluation",
 *    uniqueConstraints={
 *        @ORM\UniqueConstraint(name="evaluation_date_training_unique", columns={"training_action_id", "date"})
 *    }
 * )
 * @ORM\Entity(repositoryClass=EvaluationRepository::class)
 * @Serializer\ExclusionPolicy("ALL")
 *
 * @Hateoas\Relation("self", href = "expr('/api/evaluations/' ~ object.getId())")
 * @Hateoas\Relation("training", href = "expr('/api/training/' ~ object.getTraining().getExternalId())", exclusion=@Hateoas\Exclusion(excludeIf = "expr(object.getTraining() === null)"))
 * @Hateoas\Relation("trainingAction", href = "expr('/api/trainingActions/' ~ object.getTrainingAction().getExternalId())", exclusion=@Hateoas\Exclusion(excludeIf = "expr(object.getTrainingAction() === null)"))
 * @Hateoas\Relation("organism", href = "expr('/api/organisms/' ~ object.getOrganism().getSiret())", attributes={"name" = "expr(object.getOrganism().getName())", "siret" = "expr(object.getOrganism().getSiret())"})
 */
class Evaluation
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Serializer\Expose()
     */
    private int $id;

    /**
     * @ORM\Column(type="float")
     * @Serializer\Expose()
     */
    private float $averageRating;

    /**
     * @ORM\Column(type="integer", nullable=true)
     * @Serializer\Expose()
     */
    private ?int $reviewNumber = null;

    /**
     * @ORM\Column(type="float", nullable=true)
     * @Serializer\Expose()
     */
    private ?float $ratingQuestion1 = null;

    /**
     * @ORM\Column(type="float", nullable=true)
     * @Serializer\Expose()
     */
    private ?float $ratingQuestion2 = null;

    /**
     * @ORM\Column(type="float", nullable=true)
     * @Serializer\Expose()
     */
    private ?float $ratingQuestion3 = null;

    /**
     * @ORM\Column(type="float", nullable=true)
     * @Serializer\Expose()
     */
    private ?float $ratingQuestion4 = null;

    /**
     * @ORM\Column(type="float", nullable=true)
     * @Serializer\Expose()
     */
    private ?float $ratingQuestion5 = null;

    /**
     * @ORM\Column(type="date")
     * @Serializer\Expose()
     */
    private DateTimeInterface $date;

    /**
     * @ORM\ManyToOne(targetEntity=Training::class, inversedBy="evaluations")
     * @ORM\JoinColumn(nullable=true)
     */
    private ?Training $training = null;

    /**
     * @ORM\ManyToOne(targetEntity=TrainingAction::class, inversedBy="evaluations")
     * @ORM\JoinColumn(nullable=true)
     */
    private ?TrainingAction $trainingAction = null;

    /**
     * @ORM\ManyToOne(targetEntity=Organism::class, inversedBy="evaluations")
     * @ORM\JoinColumn(nullable=false)
     */
    private Organism $organism;

    public function getId(): int
    {
        return $this->id;
    }

    public function getAverageRating(): float
    {
        return $this->averageRating;
    }

    public function setAverageRating(float $averageRating): self
    {
        $this->averageRating = $averageRating;

        return $this;
    }

    public function getReviewNumber(): ?int
    {
        return $this->reviewNumber;
    }

    public function setReviewNumber(?int $reviewNumber): self
    {
        $this->reviewNumber = $reviewNumber;

        return $this;
    }

    public function getRatingQuestion1(): ?float
    {
        return $this->ratingQuestion1;
    }

    public function setRatingQuestion1(?float $ratingQuestion1): self
    {
        $this->ratingQuestion1 = $ratingQuestion1;

        return $this;
    }

    public function getRatingQuestion2(): ?float
    {
        return $this->ratingQuestion2;
    }

    public function setRatingQuestion2(?float $ratingQuestion2): self
    {
        $this->ratingQuestion2 = $ratingQuestion2;

        return $this;
    }

    public function getRatingQuestion3(): ?float
    {
        return $this->ratingQuestion3;
    }

    public function setRatingQuestion3(?float $ratingQuestion3): self
    {
        $this->ratingQuestion3 = $ratingQuestion3;

        return $this;
    }

    public function getRatingQuestion4(): ?float
    {
        return $this->ratingQuestion4;
    }

    public function setRatingQuestion4(?float $ratingQuestion4): self
    {
        $this->ratingQuestion4 = $ratingQuestion4;

        return $this;
    }

    public function getRatingQuestion5(): ?float
    {
        return $this->ratingQuestion5;
    }

    public function setRatingQuestion5(?float $ratingQuestion5): self
    {
        $this->ratingQuestion5 = $ratingQuestion5;

        return $this;
    }

    public function getDate(): DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getTraining(): ?Training
    {
        return $this->training;
    }

    public function setTraining(?Training $training): self
    {
        $this->training = $training;

        return $this;
    }

    public function getTrainingAction(): ?TrainingAction
    {
        return $this->trainingAction;
    }

    public function setTrainingAction(?TrainingAction $trainingAction): self
    {
        $this->trainingAction = $trainingAction;

        return $this;
    }

    public function getOrganism(): Organism
    {
        return $this->organism;
    }

    public function setOrganism(Organism $organism): self
    {
        $this->organism = $organism;

        return $this;
    }
}
