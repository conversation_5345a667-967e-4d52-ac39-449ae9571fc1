<?php

namespace App\Entity;

use App\Library\utils\AlphaID;
use App\Repository\TrainingActionRepository;
use DateTimeInterface;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Hateoas\Configuration\Annotation as Hateoas;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;

/**
 * @ORM\Entity(repositoryClass=TrainingActionRepository::class)
 * @ORM\Table(indexes={
 *     @ORM\Index(name="external_idx",columns={"external_id"}),
 *     @ORM\Index(name="taState_idx", columns={"state"})
 * })
 *
 * @Serializer\ExclusionPolicy("ALL")
 *
 * @Hateoas\Relation("self", href = "expr('/api/trainingActions/' ~ object.getExternalId())")
 * @Hateoas\Relation("sessions", href = "expr('/api/sessions?trainingActionId=' ~ object.getExternalId())")
 * @Hateoas\Relation("evaluations", href = "expr('/api/evaluations?trainingActionId=' ~ object.getExternalId())")
 * @Hateoas\Relation("training", href = "expr('/api/trainings/' ~ object.getTraining().getExternalId())", exclusion=@Hateoas\Exclusion(excludeIf = "expr(object.getTraining() === null)"))
 */
class TrainingAction
{
    /**
     * @ORM\Id()
     * @ORM\GeneratedValue()
     * @ORM\Column(type="integer")
     */
    private ?int $id = null;

    /**
     * @ORM\Column(type="datetime")
     * @Serializer\Expose
     * @Serializer\Groups({"Default"})
     */
    private DateTimeInterface $lastUpdate;

    /**
     * @ORM\Column(type="string", length=255, unique=true)
     * @Serializer\Expose
     * @Serializer\Groups({"Default", "public"})
     */
    private string $externalId;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private ?string $trackerId = null; // NOT generated since Kairos work in jan 2024

    /**
     * @ORM\Column(type="json")
     */
    private array $rawData = [];

    /**
     * @ORM\ManyToOne(targetEntity=Training::class, inversedBy="trainingActions")
     * @ORM\JoinColumn(nullable=false)
     */
    private Training $training;

    /**
     * @ORM\OneToMany(targetEntity=Session::class, mappedBy="trainingAction")
     */
    private Collection $sessions;

    /**
     * @ORM\OneToMany(targetEntity=Evaluation::class, mappedBy="trainingAction")
     * @ORM\OrderBy({"date" = "DESC"})
     */
    private Collection $evaluations;

    /**
     * @ORM\ManyToMany(targetEntity=Proposal::class, mappedBy="trainingActions")
     */
    private Collection $proposals;

    /**
     * @ORM\Column(type="string", length=50)
     * @Serializer\Expose
     * @Serializer\Groups({"Default", "public"})
     */
    private string $state;

    public function __construct()
    {
        $this->sessions = new ArrayCollection();
        $this->evaluations = new ArrayCollection();
        $this->proposals = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getLastUpdate(): DateTimeInterface
    {
        return $this->lastUpdate;
    }

    public function setLastUpdate(DateTimeInterface $lastUpdate): self
    {
        $this->lastUpdate = $lastUpdate;

        return $this;
    }

    public function getExternalId(): string
    {
        return $this->externalId;
    }

    public function setExternalId(string $externalId): self
    {
        $this->externalId = $externalId;

        return $this;
    }

    /**
     * @return Collection|Session[]
     */
    public function getSessions(): Collection
    {
        return $this->sessions;
    }

    public function addSession(Session $session): self
    {
        if (!$this->sessions->contains($session)) {
            $this->sessions[] = $session;
            $session->setTrainingAction($this);
        }

        return $this;
    }

    public function removeSession(Session $session): self
    {
        if ($this->sessions->contains($session)) {
            $this->sessions->removeElement($session);
            // set the owning side to null (unless already changed)
            if ($session->getTrainingAction() === $this) {
                $session->setTrainingAction(null);
            }
        }

        return $this;
    }

    public function getRawData(): array
    {
        return $this->rawData;
    }

    public function setRawData(array $rawData): self
    {
        $this->rawData = $rawData;

        return $this;
    }

    /**
     * @return Collection|Evaluation[]
     */
    public function getEvaluations(): Collection
    {
        return $this->evaluations;
    }

    public function addEvaluation(Evaluation $evaluation): self
    {
        if (!$this->evaluations->contains($evaluation)) {
            $this->evaluations[] = $evaluation;
            $evaluation->setTrainingAction($this);
        }

        return $this;
    }

    public function removeEvaluation(Evaluation $evaluation): self
    {
        if ($this->evaluations->removeElement($evaluation)) {
            // set the owning side to null (unless already changed)
            if ($evaluation->getTrainingAction() === $this) {
                $evaluation->setTrainingAction(null);
            }
        }

        return $this;
    }

    public function getTrackerId(): ?string
    {
        if (!$this->trackerId && $this->id) {
            $this->setTrackerId(AlphaID::generate($this->getId(), false, 5, $this->getTraining()->getTrackerId()));
        }
        return $this->trackerId;
    }

    public function setTrackerId(?string $trackerId): self
    {
        $this->trackerId = $trackerId;

        return $this;
    }

    public function getTraining(): Training
    {
        return $this->training;
    }

    public function setTraining(Training $training): self
    {
        $this->training = $training;
        return $this;
    }

    /**
     * @return Collection|Proposal[]
     */
    public function getProposals(): Collection
    {
        return $this->proposals;
    }

    public function addProposal(Proposal $proposal): self
    {
        if (!$this->proposals->contains($proposal)) {
            $this->proposals[] = $proposal;
        }

        return $this;
    }

    public function removeProposal(Proposal $proposal): self
    {
        $this->proposals->removeElement($proposal);

        return $this;
    }

    public function getState(): string
    {
        return $this->state;
    }

    public function setState(string $state): self
    {
        $this->state = $state;

        return $this;
    }
}
