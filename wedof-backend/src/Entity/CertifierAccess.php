<?php

namespace App\Entity;

use App\Entity\Traits\TimestampableTrait;
use App\Library\utils\enums\CertifierAccessStates;
use App\Repository\CertifierAccessRepository;
use DateTimeInterface;
use Doctrine\ORM\Mapping as ORM;
use Hateoas\Configuration\Annotation as Hateoas;
use J<PERSON>\Serializer\Annotation as Serializer;
use <PERSON><PERSON>\Serializer\Annotation\Accessor;
use <PERSON>MS\Serializer\Annotation\VirtualProperty;

/**
 * @ORM\Entity(repositoryClass=CertifierAccessRepository::class)
 * @ORM\HasLifecycleCallbacks()
 *
 * @ORM\Table(name="certifier_access",uniqueConstraints={@ORM\UniqueConstraint(name="unique_idx", columns={"certifier_id", "partner_id"})})
 *
 * @Serializer\ExclusionPolicy("ALL")
 * @Hateoas\Relation("self", href = "expr('/app/certifierAccess/' ~ object.getId())")
 * @Hateoas\Relation("partner", href= "expr('/api/organisms/' ~ object.getPartner().getSiret())", attributes={"name" = "expr(object.getPartner().getName())", "siret" = "expr(object.getPartner().getSiret())"}))
 * @Hateoas\Relation("certifier", href= "expr('/api/organisms/' ~ object.getCertifier().getSiret())", attributes={"name" = "expr(object.getCertifier().getName())", "siret" = "expr(object.getCertifier().getSiret())"}))
 */
class CertifierAccess
{
    use TimestampableTrait;

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Serializer\Expose
     */
    private int $id;

    /**
     * @ORM\ManyToOne(targetEntity=Organism::class, inversedBy="certifierAccesses")
     * @ORM\JoinColumn(nullable=false)
     */
    private Organism $certifier;

    /**
     * @ORM\ManyToOne(targetEntity=Organism::class, inversedBy="partnerAccesses")
     * @ORM\JoinColumn(nullable=false)
     */
    private Organism $partner;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Expose
     */
    private ?DateTimeInterface $activatedOn = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Expose
     */
    private ?DateTimeInterface $terminatedOn = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private ?string $token = null;

    /**
     * @Accessor(getter="getState")
     * @Serializer\Expose
     */
    private ?string $state = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private ?string $email = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Expose
     */
    private ?string $fullName = null;

    public function getId(): int
    {
        return $this->id;
    }

    public function getCertifier(): Organism
    {
        return $this->certifier;
    }

    public function setCertifier(Organism $certifier): self
    {
        $this->certifier = $certifier;

        return $this;
    }

    public function getPartner(): Organism
    {
        return $this->partner;
    }

    public function setPartner(Organism $partner): self
    {
        $this->partner = $partner;

        return $this;
    }

    public function getActivatedOn(): ?DateTimeInterface
    {
        return $this->activatedOn;
    }

    public function setActivatedOn(?DateTimeInterface $activatedOn): self
    {
        $this->activatedOn = $activatedOn;

        return $this;
    }

    public function getTerminatedOn(): ?DateTimeInterface
    {
        return $this->terminatedOn;
    }

    public function setTerminatedOn(?DateTimeInterface $terminatedOn): self
    {
        $this->terminatedOn = $terminatedOn;

        return $this;
    }

    public function getToken(): ?string
    {
        return $this->token;
    }

    public function setToken(?string $token): self
    {
        $this->token = $token;

        return $this;
    }

    public function isActive(): bool
    {
        return !empty($this->getActivatedOn()) && empty($this->getTerminatedOn());
    }

    public function getState(): CertifierAccessStates
    {
        if ($this->activatedOn) {
            if ($this->terminatedOn) {
                return CertifierAccessStates::TERMINATED();
            } else {
                return CertifierAccessStates::ACCEPTED();
            }
        } else if ($this->terminatedOn) {
            return CertifierAccessStates::REFUSED();
        }
        return CertifierAccessStates::WAITING();
    }

    /**
     * @VirtualProperty()
     * @Serializer\Expose()
     */
    public function getEmail(): ?string
    {
        // donne l'email de l'utilisateur lié au partenaire s'il existe déjà afin de le pré-renseigner dans l'interface de connexion
        $partnerOwner = $this->getPartner()->getOwnedBy();
        $email = $this->email;
        if ($partnerOwner) {
            $email = $partnerOwner->getEmail();
        }
        return $email;
    }

    /**
     * @VirtualProperty()
     * @Serializer\Expose()
     */
    public function getInviteLink(): ?string
    {
        return $this->getToken() ? $_ENV['WEDOF_BASE_URI'] . '/invitation?token=' . $this->getToken() : null;
    }

    public function setEmail(?string $email): self
    {
        $this->email = $email;

        return $this;
    }

    public function getFullName(): ?string
    {
        return $this->fullName;
    }

    public function setFullName(?string $fullName): self
    {
        $this->fullName = $fullName;

        return $this;
    }
}
