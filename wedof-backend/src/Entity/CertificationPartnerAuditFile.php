<?php

namespace App\Entity;

use App\Entity\Traits\TimestampableTrait;
use App\Repository\CertificationPartnerAuditFileRepository;
use Doctrine\ORM\Mapping as ORM;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;
use Symfony\Component\HttpFoundation\File\File;
use Vich\UploaderBundle\Mapping\Annotation as Vich;

/**
 * @ORM\Entity(repositoryClass=CertificationPartnerAuditFileRepository::class)
 * @ORM\HasLifecycleCallbacks()
 * @Serializer\ExclusionPolicy("ALL")
 * @Vich\Uploadable
 */
class CertificationPartnerAuditFile
{
    use TimestampableTrait;

    const CLASSNAME = 'CertificationPartnerAuditFile';

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Serializer\Expose
     */
    private int $id;

    /**
     * @ORM\Column(type="integer")
     * @Serializer\Expose
     */
    private string $typeId;

    /**
     * @Vich\UploadableField(mapping="certificationPartnerAuditFile", fileNameProperty="filePath", originalName="fileName", mimeType="fileType")
     * @var File|null
     */
    public ?File $file = null;

    /**
     * @ORM\Column(type="string", length=255)
     * @Serializer\Expose
     */
    private string $fileName;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private string $filePath;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Expose
     */
    private ?string $link = null;

    /**
     * @ORM\Column(type="string", length=255)
     * @Serializer\Expose
     */
    private string $fileType;

    /**
     * @ORM\OneToOne(targetEntity=CertificationPartnerAudit::class, mappedBy="report")
     * @ORM\JoinColumn(nullable=true)
     */
    private ?CertificationPartnerAudit $certificationPartnerAudit = null;

    /**
     * @ORM\Column(type="text", nullable=true)
     * @Serializer\Expose
     */
    private ?string $comment = null;

    /**
     * @ORM\Column(type="string", length=30, options={"default" : "notGenerated"})
     * @Serializer\Expose()
     */
    private string $generationState = 'notGenerated';

    /**
     * @ORM\Column(type="string", length=30, options={"default" : "notRequired"})
     * @Serializer\Expose()
     */
    private string $signedState = 'notRequired';

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @return string|null
     */
    public function getLink(): ?string
    {
        return $this->link;
    }

    /**
     * @param string|null $link
     * @return $this
     */
    public function setLink(?string $link): self
    {
        $this->link = $link;

        return $this;
    }

    /**
     * @return string
     */
    public function getFilePath(): string
    {
        return $this->filePath;
    }

    /**
     * @param string|null $filePath
     * @return $this
     */
    public function setFilePath(?string $filePath): self
    {
        $this->filePath = (string)$filePath; //https://github.com/dustin10/VichUploaderBundle/issues/1117

        return $this;
    }

    /**
     * @return string
     */
    public function getFileName(): string
    {
        return $this->fileName;
    }

    /**
     * @param string|null $fileName
     * @return $this
     */
    public function setFileName(?string $fileName): self
    {
        $this->fileName = (string)$fileName; //https://github.com/dustin10/VichUploaderBundle/issues/1117

        return $this;
    }

    /**
     * @return string
     */
    public function getFileType(): string
    {
        return $this->fileType;
    }

    /**
     * @param string|null $fileType
     * @return $this
     */
    public function setFileType(?string $fileType): self
    {
        $this->fileType = (string)$fileType;//https://github.com/dustin10/VichUploaderBundle/issues/1117

        return $this;
    }

    /**
     * @param File|null $file
     */
    public function setFile(?File $file = null): void
    {
        $this->file = $file;

        if (null !== $file) {
            // It is required that at least one field changes if you are using doctrine
            // otherwise the event listeners won't be called and the file is lost
            $this->updateUpdatedOnTimestamp();
        }
    }


    /**
     * @return CertificationPartnerAudit|null
     */
    public function getCertificationPartnerAudit(): ?CertificationPartnerAudit
    {
        return $this->certificationPartnerAudit;
    }

    /**
     * @param CertificationPartnerAudit|null $certificationPartnerAudit
     * @return $this
     */
    public function setCertificationPartnerAudit(?CertificationPartnerAudit $certificationPartnerAudit): self
    {
        $this->certificationPartnerAudit = $certificationPartnerAudit;

        return $this;
    }

    public function getTypeId(): int
    {
        return $this->typeId;
    }

    public function setTypeId(int $typeId): self
    {
        $this->typeId = $typeId;

        return $this;
    }

    public function getComment(): ?string
    {
        return $this->comment;
    }

    public function setComment(?string $comment): self
    {
        $this->comment = $comment;

        return $this;
    }

    public function getGenerationState(): string
    {
        return $this->generationState;
    }

    public function setGenerationState(string $generationState): self
    {
        $this->generationState = $generationState;

        return $this;
    }

    public function getSignedState(): string
    {
        return $this->signedState;
    }

    public function setSignedState(string $signedState): self
    {
        $this->signedState = $signedState;

        return $this;
    }

}
