<?php

namespace App\Entity\Traits;

use DateTime;
use DateTimeInterface;
use Exception;
use Doctrine\ORM\Mapping as ORM;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;

/**
 * Trait TimestampableTrait
 * @package App\Entity\Trait
 */
trait TimestampableTrait
{
    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"owner", "Default", "webhook"})
     */
    private ?DateTimeInterface $createdOn = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"owner", "Default"})
     */
    private ?DateTimeInterface $updatedOn = null;

    /**
     * @return DateTimeInterface|null
     * @throws Exception
     */
    public function getCreatedOn(): ?DateTimeInterface
    {
        return $this->createdOn;
    }

    /**
     * @param DateTimeInterface $createdOn
     * @return $this
     */
    public function setCreatedOn(DateTimeInterface $createdOn): self
    {
        $this->createdOn = $createdOn;

        return $this;
    }

    /**
     * @return DateTimeInterface|null
     */
    public function getUpdatedOn(): ?DateTimeInterface
    {
        return $this->updatedOn;
    }

    /**
     * @param DateTimeInterface $updatedOn
     * @return $this
     */
    public function setUpdatedOn(DateTimeInterface $updatedOn): self
    {
        $this->updatedOn = $updatedOn;

        return $this;
    }

    /**
     * @ORM\PrePersist()
     */
    public function updateCreatedOnAndUpdatedOnTimestamps(): void
    {
        $now = new DateTime();
        $this->setCreatedOn($now);
        $this->setUpdatedOn($now);
    }

    /**
     * @ORM\PreUpdate()
     */
    public function updateUpdatedOnTimestamp(): void
    {
        $this->setUpdatedOn(new DateTime());
    }
}
