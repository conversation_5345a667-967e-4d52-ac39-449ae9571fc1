<?php

namespace App\Entity;

use App\Repository\CertificationFolderHistoryRepository;
use DateTimeInterface;
use Doctrine\ORM\Mapping as ORM;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;

/**
 * @ORM\Entity(repositoryClass=CertificationFolderHistoryRepository::class)
 */
class CertificationFolderHistory
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Serializer\Exclude
     */
    private int $id;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Groups({"owner", "Default"})
     */
    private ?DateTimeInterface $toTakeDate = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Groups({"owner", "Default"})
     */
    private ?DateTimeInterface $failedDate = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Groups({"owner", "Default"})
     */
    private ?DateTimeInterface $successDate = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Groups({"owner", "Default"})
     */
    private ?DateTimeInterface $toRegisterDate = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Groups({"owner", "Default"})
     */
    private ?DateTimeInterface $registeredDate = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Groups({"owner", "Default"})
     */
    private ?DateTimeInterface $abortedDate = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Groups({"owner", "Default"})
     */
    private ?DateTimeInterface $toControlDate = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Groups({"owner", "Default"})
     */
    private ?DateTimeInterface $refusedDate = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Groups({"owner", "Default"})
     */
    private ?DateTimeInterface $toRetakeDate = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Groups({"owner", "Default"})
     */
    private ?DateTimeInterface $inTrainingStartedDate = null;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Serializer\Groups({"owner", "Default"})
     */
    private ?DateTimeInterface $inTrainingEndedDate = null;

    public function getId(): int
    {
        return $this->id;
    }

    public function getToTakeDate(): ?DateTimeInterface
    {
        return $this->toTakeDate;
    }

    public function setToTakeDate(?DateTimeInterface $toTakeDate): self
    {
        $this->toTakeDate = $toTakeDate;

        return $this;
    }

    public function getFailedDate(): ?DateTimeInterface
    {
        return $this->failedDate;
    }

    public function setFailedDate(?DateTimeInterface $failedDate): self
    {
        $this->failedDate = $failedDate;

        return $this;
    }

    public function getSuccessDate(): ?DateTimeInterface
    {
        return $this->successDate;
    }

    public function setSuccessDate(?DateTimeInterface $successDate): self
    {
        $this->successDate = $successDate;

        return $this;
    }

    public function getToRegisterDate(): ?DateTimeInterface
    {
        return $this->toRegisterDate;
    }

    public function setToRegisterDate(?DateTimeInterface $toRegisterDate): self
    {
        $this->toRegisterDate = $toRegisterDate;

        return $this;
    }

    public function getRegisteredDate(): ?DateTimeInterface
    {
        return $this->registeredDate;
    }

    public function setRegisteredDate(?DateTimeInterface $registeredDate): self
    {
        $this->registeredDate = $registeredDate;

        return $this;
    }

    public function getAbortedDate(): ?DateTimeInterface
    {
        return $this->abortedDate;
    }

    public function setAbortedDate(?DateTimeInterface $abortedDate): self
    {
        $this->abortedDate = $abortedDate;

        return $this;
    }

    public function getToControlDate(): ?DateTimeInterface
    {
        return $this->toControlDate;
    }

    public function setToControlDate(?DateTimeInterface $toControlDate): self
    {
        $this->toControlDate = $toControlDate;

        return $this;
    }

    public function getRefusedDate(): ?DateTimeInterface
    {
        return $this->refusedDate;
    }

    public function setRefusedDate(?DateTimeInterface $refusedDate): self
    {
        $this->refusedDate = $refusedDate;

        return $this;
    }

    public function getToRetakeDate(): ?DateTimeInterface
    {
        return $this->toRetakeDate;
    }

    public function setToRetakeDate(?DateTimeInterface $toRetakeDate): self
    {
        $this->toRetakeDate = $toRetakeDate;

        return $this;
    }

    public function getInTrainingStartedDate(): ?DateTimeInterface
    {
        return $this->inTrainingStartedDate;
    }

    public function setInTrainingStartedDate(?DateTimeInterface $inTrainingStartedDate): self
    {
        $this->inTrainingStartedDate = $inTrainingStartedDate;

        return $this;
    }

    public function getInTrainingEndedDate(): ?DateTimeInterface
    {
        return $this->inTrainingEndedDate;
    }

    public function setInTrainingEndedDate(?DateTimeInterface $inTrainingEndedDate): self
    {
        $this->inTrainingEndedDate = $inTrainingEndedDate;

        return $this;
    }
}
