<?php

namespace App\Entity;

use App\Repository\CertificationPartnerAuditTemplateRepository;
use Doctrine\ORM\Mapping as ORM;
use Hateoas\Configuration\Annotation as Hateoas;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;

/**
 * @ORM\Entity(repositoryClass=CertificationPartnerAuditTemplateRepository::class)
 *
 * @Hateoas\Relation("certification", href = "expr('/api/certifications/' ~ object.getCertification().getCertifInfo())", attributes={"name" = "expr(object.getCertification().getName())", "certifInfo" = "expr(object.getCertification().getCertifInfo())", "externalId" = "expr(object.getCertification().getExternalId())", "id" = "expr(object.getCertification().getId())", "enabled" = "expr(object.getCertification().getEnabled())"})
 * @Serializer\ExclusionPolicy("ALL")
 */
class CertificationPartnerAuditTemplate
{

    public const CLASSNAME = 'CertificationPartnerAuditTemplate';

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Serializer\Expose()
     */
    private int $id;

    /**
     * @ORM\ManyToOne(targetEntity=Certification::class)
     * @ORM\JoinColumn(nullable=false)
     */
    private Certification $certification;

    /**
     * @ORM\Column(type="string", length=255)
     * @Serializer\Expose()
     */
    private string $name;

    /**
     * @ORM\Column(type="boolean", options={"default": false})
     * @Serializer\Expose()
     */
    private bool $allowVisibilityPartner = false;

    /**
     * @ORM\Column(type="json", nullable=true)
     * @Serializer\Expose()
     */
    private ?array $criterias = [];

    /**
     * @ORM\Column(type="json", nullable=true)
     * @Serializer\Expose()
     */
    private ?array $auditTemplateFileType = [];

    /**
     * @ORM\ManyToOne(targetEntity=Organism::class)
     * @ORM\JoinColumn(nullable=false)
     */
    private Organism $certifier;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getCertification(): Certification
    {
        return $this->certification;
    }

    public function setCertification(Certification $certification): self
    {
        $this->certification = $certification;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function isAllowVisibilityPartner(): bool
    {
        return $this->allowVisibilityPartner;
    }

    public function setAllowVisibilityPartner(bool $allowVisibilityPartner): self
    {
        $this->allowVisibilityPartner = $allowVisibilityPartner;

        return $this;
    }

    public function getCriterias(): ?array
    {
        return $this->criterias;
    }

    public function setCriterias(?array $criterias): self
    {
        $this->criterias = $criterias;
        return $this;
    }

    public function getAuditTemplateFileType(): array
    {
        return $this->auditTemplateFileType;
    }

    public function setAuditTemplateFileType(?array $auditTemplateFileType): self
    {
        $this->auditTemplateFileType = $auditTemplateFileType;

        return $this;
    }

    public function getCertifier(): Organism
    {
        return $this->certifier;
    }

    public function setCertifier(Organism $certifier): self
    {
        $this->certifier = $certifier;

        return $this;
    }

}
