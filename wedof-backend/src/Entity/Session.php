<?php

namespace App\Entity;

use App\Repository\SessionRepository;
use DateTimeInterface;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Hateoas\Configuration\Annotation as Hateoas;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;

/**
 * @ORM\Entity(repositoryClass=SessionRepository::class)
 * @ORM\Table(indexes={
 *      @ORM\Index(name="external_idx",columns={"external_id"}),
 *      @ORM\Index(name="sState_idx", columns={"state"})
 *  })
 *
 * @Serializer\ExclusionPolicy("ALL")
 *
 * @Hateoas\Relation("self", href = "expr('/api/sessions/' ~ object.getExternalId())", exclusion=@Hateoas\Exclusion(groups={"Default", "certifier"}))
 * @Hateoas\Relation("trainingAction", href = "expr('/api/trainingActions/' ~ object.getTrainingAction().getExternalId())", exclusion=@Hateoas\Exclusion(groups={"Default"}))
 * @Hateoas\Relation("registrationFolders", href = "expr('/api/registrationFolders?sessionId=' ~ object.getExternalId())", exclusion=@Hateoas\Exclusion(groups={"Default"}))
 */
class Session
{
    /**
     * @ORM\Id()
     * @ORM\GeneratedValue()
     * @ORM\Column(type="integer")
     * @Serializer\Expose
     * @Serializer\Groups({"Default"})
     */
    private int $id;

    /**
     * @ORM\Column(type="json")
     */
    private array $rawData = [];

    /**
     * @ORM\OneToMany(targetEntity=RegistrationFolder::class, mappedBy="session")
     */
    private Collection $registrationFolders;

    /**
     * @ORM\ManyToOne(targetEntity=TrainingAction::class, inversedBy="sessions")
     * @ORM\JoinColumn(nullable=false)
     */
    private TrainingAction $trainingAction;

    /**
     * @ORM\Column(type="string", length=255, unique=true)
     * @Serializer\Expose
     * @Serializer\Groups({"certifier", "Default", "attendee"})
     */
    private string $externalId;

    /**
     * @ORM\Column(type="date", nullable=true)
     * @Serializer\Expose
     * @Serializer\Groups({"Default", "attendee"})
     */
    private ?DateTimeInterface $startDate = null;

    /**
     * @ORM\Column(type="date", nullable=true)
     * @Serializer\Expose
     * @Serializer\Groups({"Default", "attendee"})
     */
    private ?DateTimeInterface $endDate = null;

    /**
     * @ORM\Column(type="string", length=50)
     * @Serializer\Expose
     * @Serializer\Groups({"certifier", "Default", "attendee"})
     */
    private string $state;

    public function __construct()
    {
        $this->registrationFolders = new ArrayCollection();
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getRawData(): array
    {
        return $this->rawData;
    }

    public function setRawData(array $rawData): self
    {
        $this->rawData = $rawData;

        return $this;
    }

    /**
     * @return Collection|RegistrationFolder[]
     */
    public function getRegistrationFolders(): Collection
    {
        return $this->registrationFolders;
    }

    public function addRegistrationFolder(RegistrationFolder $registrationFolder): self
    {
        if (!$this->registrationFolders->contains($registrationFolder)) {
            $this->registrationFolders[] = $registrationFolder;
            $registrationFolder->setSession($this);
        }

        return $this;
    }

    public function removeRegistrationFolder(RegistrationFolder $registrationFolder): self
    {
        if ($this->registrationFolders->contains($registrationFolder)) {
            $this->registrationFolders->removeElement($registrationFolder);
            // set the owning side to null (unless already changed)
            if ($registrationFolder->getSession() === $this) {
                $registrationFolder->setSession(null);
            }
        }

        return $this;
    }

    public function getExternalId(): string
    {
        return $this->externalId;
    }

    public function setExternalId(string $externalId): self
    {
        $this->externalId = $externalId;

        return $this;
    }

    public function getTrainingAction(): TrainingAction
    {
        return $this->trainingAction;
    }

    public function setTrainingAction(TrainingAction $trainingAction): self
    {
        $this->trainingAction = $trainingAction;

        return $this;
    }

    public function getStartDate(): ?DateTimeInterface
    {
        return $this->startDate;
    }

    public function setStartDate(DateTimeInterface $startDate): self
    {
        $this->startDate = $startDate;

        return $this;
    }

    public function getEndDate(): ?DateTimeInterface
    {
        return $this->endDate;
    }

    public function setEndDate(DateTimeInterface $endDate): self
    {
        $this->endDate = $endDate;

        return $this;
    }

    public function getState(): string
    {
        return $this->state;
    }

    public function setState(string $state): self
    {
        $this->state = $state;

        return $this;
    }
}
