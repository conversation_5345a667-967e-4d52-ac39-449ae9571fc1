<?php

namespace App\Entity;

use App\Repository\ShortenedUrlRepository;
use DateTimeInterface;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass=ShortenedUrlRepository::class)
 */
class ShortenedUrl
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private int $id;

    /**
     * @ORM\Column(type="text")
     */
    private string $url;

    /**
     * @ORM\Column(type="integer", nullable=true, options={"default"=0})
     */
    private int $countOpen = 0;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     */
    private ?DateTimeInterface $lastOpen = null;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private string $entityId;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private string $entityClass;

    /**
     * @ORM\Column(type="string", length=255, unique=true)
     */
    private string $hashKey;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getUrl(): ?string
    {
        return $this->url;
    }

    public function setUrl(string $url): self
    {
        $this->url = $url;

        return $this;
    }

    public function getCountOpen(): ?int
    {
        return $this->countOpen;
    }

    public function setCountOpen(?int $countOpen): self
    {
        $this->countOpen = $countOpen;

        return $this;
    }

    public function getLastOpen(): ?DateTimeInterface
    {
        return $this->lastOpen;
    }

    public function setLastOpen(?DateTimeInterface $lastOpen): self
    {
        $this->lastOpen = $lastOpen;

        return $this;
    }

    public function getEntityId(): ?string
    {
        return $this->entityId;
    }

    public function setEntityId(string $entityId): self
    {
        $this->entityId = $entityId;

        return $this;
    }

    public function getEntityClass(): ?string
    {
        return $this->entityClass;
    }

    public function setEntityClass(string $entityClass): self
    {
        $this->entityClass = $entityClass;

        return $this;
    }

    public function getHashKey(): string
    {
        return $this->hashKey;
    }

    public function setHashKey(string $hashKey): self
    {
        $this->hashKey = $hashKey;

        return $this;
    }
}
