<?php

namespace App\Entity;

use App\Application\Signature\SignatureWedofApplication;
use App\Application\Workflow\WorkflowWedofApplication;
use App\Entity\Traits\TimestampableTrait;
use App\Library\utils\enums\SubscriptionTrainingTypes;
use App\Repository\SubscriptionRepository;
use DateTimeInterface;
use Doctrine\ORM\Mapping as ORM;
use Hateoas\Configuration\Annotation as Hateoas;
use JMS\Serializer\Annotation as Serializer;

/**
 * @ORM\Entity(repositoryClass=SubscriptionRepository::class)
 * @ORM\HasLifecycleCallbacks()
 * @Hateoas\Relation("self", href = "expr('/app/subscriptions/' ~ object.getId())")
 * @Hateoas\Relation("organism", href = "expr('/api/organisms/' ~ object.getOrganism().getSiret())", attributes={"name" = "expr(object.getOrganism().getName())", "siret" = "expr(object.getOrganism().getSiret())"}, exclusion=@Hateoas\Exclusion(groups={"webhook", "Default"}))
 * @Serializer\ExclusionPolicy("ALL")
 */
class Subscription
{
    use TimestampableTrait;

    public static array $SUBSCRIPTION_OPTIONS_APPS = [WorkflowWedofApplication::class, SignatureWedofApplication::class];

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    private int $id;

    /**
     * @ORM\Column(type="string", length=255, nullable=true, options={"charset":"utf8mb4","collation":"utf8mb4_unicode_ci"})
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "webhook", "dataPartner"})
     */
    private ?string $trainingType = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "webhook"})
     */
    private ?string $certifierType = null;

    /**
     * @ORM\Column(type="date", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "webhook"})
     */
    private ?DateTimeInterface $trainingStartDate = null;

    /**
     * @ORM\Column(type="date", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "webhook"})
     */
    private ?DateTimeInterface $certifierStartDate = null;

    /**
     * @ORM\Column(type="date", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "webhook", "dataPartner"})
     */
    private ?DateTimeInterface $trainingPeriodStartDate = null;

    /**
     * @ORM\Column(type="date", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "webhook"})
     */
    private ?DateTimeInterface $certifierPeriodStartDate = null;

    /**
     * @ORM\Column(type="date", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "webhook", "dataPartner"})
     */
    private ?DateTimeInterface $trainingPeriodEndDate = null;

    /**
     * @ORM\Column(type="date", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "webhook"})
     */
    private ?DateTimeInterface $certifierPeriodEndDate = null;

    /**
     * @ORM\Column(type="boolean")
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "webhook", "dataPartner"})
     */
    private bool $trainingPendingCancellation = false;

    /**
     * @ORM\Column(type="boolean")
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "webhook"})
     */
    private bool $certifierPendingCancellation = false;

    /**
     * @ORM\Column(type="integer")
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "webhook", "dataPartner"})
     */
    private int $registrationFoldersNumberCount = 0;

    /**
     * @ORM\Column(type="integer", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "webhook", "dataPartner"})
     */
    private ?int $registrationFoldersNumberLimit = null;

    /**
     * @ORM\Column(type="integer", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default", "webhook"})
     */
    private ?int $certificationFoldersNumberAnnualLimit = null;

    /**
     * @ORM\Column(type="boolean")
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    private bool $allowApi = true;

    /**
     * @ORM\Column(type="boolean")
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    private bool $allowAnalytics = false;

    /**
     * @ORM\Column(type="boolean")
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    private bool $allowProposals = false;

    /**
     * @ORM\Column(type="boolean")
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    private bool $allowCertifiers = false;

    /**
     * @ORM\Column(type="integer")
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    private int $certificationFoldersNumberCount = 0;

    /**
     * @ORM\Column(type="integer")
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    private int $smsSentNumberCount = 0;

    /**
     * @ORM\Column(type="date", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    private ?DateTimeInterface $smsSentNumberPeriodStartDate = null;

    /**
     * @ORM\Column(type="date", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    private ?DateTimeInterface $smsSentNumberPeriodEndDate = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private ?string $stripeCustomerId = null;

    /**
     * @ORM\OneToOne(targetEntity=Organism::class, inversedBy="subscription", cascade={"persist", "remove"})
     * @ORM\JoinColumn(nullable=false, onDelete="CASCADE")
     */
    private Organism $organism;

    /**
     * @ORM\Column(type="string", length=255, nullable=true, options={"charset":"utf8mb4","collation":"utf8mb4_unicode_ci"})
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    private ?string $trainingPartnership = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    private ?string $certifierPartnership = null;

    /**
     * @ORM\Column(type="integer", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    private ?int $trainingUsersLimit = null;

    /**
     * @ORM\Column(type="boolean")
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    private bool $allowRegistrationFolders = false;

    /**
     * @ORM\Column(type="boolean", options={"default"=false})
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    private bool $allowCertifierPlus = false;

    /**
     * @ORM\OneToOne(targetEntity=Company::class, mappedBy="Subscription", cascade={"persist", "remove"})
     */
    private ?Company $company = null;

    /**
     * @ORM\Column(type="boolean", options={"default"=false})
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    private bool $allowCertifierBetaFeatures = false;

    /**
     * @ORM\Column(type="integer", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    private ?int $certificationFoldersNumberCap = null;

    /**
     * @ORM\Column(type="float", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    private ?float $certificationFolderPrice = null;

    /**
     * @ORM\Column(type="integer", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    private ?int $certifierUsersLimit = null;

    /**
     * @ORM\Column(type="date", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    private ?DateTimeInterface $registrationFolderNumberPeriodStartDate = null;

    /**
     * @ORM\Column(type="date", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    private ?DateTimeInterface $registrationFolderNumberPeriodEndDate = null;

    /**
     * @ORM\Column(type="date", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    private ?DateTimeInterface $certificationFoldersNumberPeriodStartDate = null;

    /**
     * @ORM\Column(type="date", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    private ?DateTimeInterface $certificationFoldersNumberPeriodEndDate = null;


    /**
     * @ORM\Column(type="date", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    private ?DateTimeInterface $trainingTrialEndDate = null;

    /**
     * @ORM\Column(type="date", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    private ?DateTimeInterface $certifierTrialEndDate = null;

    /**
     * @ORM\Column(type="integer", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    private ?int $lastRegistrationFolderId = null;

    /**
     * @ORM\Column(type="integer", options={"default" : 0})
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    private int $requestCount = 0;

    /**
     * @ORM\Column(type="boolean", options={"default"=false})
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    private bool $allowPaidUsage = false;

    /**
     * @ORM\Column(type="integer", nullable=true)
     * @Serializer\Expose()
     * @Serializer\Groups({"Default"})
     */
    private ?int $certificationFoldersNumberAnnualCount = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getTrainingType(): ?string
    {
        return $this->trainingType;
    }

    public function setTrainingType(string $trainingType): self
    {
        $this->trainingType = $trainingType;

        return $this;
    }

    public function getCertifierType(): ?string
    {
        return $this->certifierType;
    }

    public function setCertifierType(string $certifierType): self
    {
        $this->certifierType = $certifierType;

        return $this;
    }

    public function getTrainingStartDate(): ?DateTimeInterface
    {
        return $this->trainingStartDate;
    }

    public function setTrainingStartDate(DateTimeInterface $trainingStartDate): self
    {
        $this->trainingStartDate = $trainingStartDate;

        return $this;
    }

    public function getCertifierStartDate(): ?DateTimeInterface
    {
        return $this->certifierStartDate;
    }

    public function setCertifierStartDate(DateTimeInterface $certifierStartDate): self
    {
        $this->certifierStartDate = $certifierStartDate;

        return $this;
    }

    public function getTrainingPeriodStartDate(): ?DateTimeInterface
    {
        return $this->trainingPeriodStartDate;
    }

    public function setTrainingPeriodStartDate(DateTimeInterface $trainingPeriodStartDate): self
    {
        $this->trainingPeriodStartDate = $trainingPeriodStartDate;

        return $this;
    }

    public function getCertifierPeriodStartDate(): ?DateTimeInterface
    {
        return $this->certifierPeriodStartDate;
    }

    public function setCertifierPeriodStartDate(DateTimeInterface $certifierPeriodStartDate): self
    {
        $this->certifierPeriodStartDate = $certifierPeriodStartDate;

        return $this;
    }

    public function getTrainingPeriodEndDate(): ?DateTimeInterface
    {
        return $this->trainingPeriodEndDate;
    }

    public function setTrainingPeriodEndDate(DateTimeInterface $trainingPeriodEndDate): self
    {
        $this->trainingPeriodEndDate = $trainingPeriodEndDate;

        return $this;
    }

    public function getCertifierPeriodEndDate(): ?DateTimeInterface
    {
        return $this->certifierPeriodEndDate;
    }

    public function setCertifierPeriodEndDate(DateTimeInterface $certifierPeriodEndDate): self
    {
        $this->certifierPeriodEndDate = $certifierPeriodEndDate;

        return $this;
    }

    public function isTrainingPendingCancellation(): ?bool
    {
        return $this->trainingPendingCancellation;
    }

    public function setTrainingPendingCancellation(?bool $trainingPendingCancellation): self
    {
        $this->trainingPendingCancellation = $trainingPendingCancellation;

        return $this;
    }

    public function isCertifierPendingCancellation(): ?bool
    {
        return $this->certifierPendingCancellation;
    }

    public function setCertifierPendingCancellation(?bool $certifierPendingCancellation): self
    {
        $this->certifierPendingCancellation = $certifierPendingCancellation;

        return $this;
    }

    public function getRegistrationFoldersNumberCount(): int
    {
        return $this->registrationFoldersNumberCount;
    }

    public function setRegistrationFoldersNumberCount(int $registrationFoldersNumberCount): self
    {
        $this->registrationFoldersNumberCount = $registrationFoldersNumberCount;

        return $this;
    }

    public function getRegistrationFoldersNumberLimit(): ?int
    {
        return $this->registrationFoldersNumberLimit;
    }

    public function setRegistrationFoldersNumberLimit(?int $registrationFoldersNumberLimit): self
    {
        $this->registrationFoldersNumberLimit = $registrationFoldersNumberLimit;

        return $this;
    }

    public function getCertificationFoldersNumberAnnualLimit(): ?int
    {
        return $this->certificationFoldersNumberAnnualLimit;
    }

    public function setCertificationFoldersNumberAnnualLimit(?int $certificationFoldersNumberAnnualLimit): self
    {
        $this->certificationFoldersNumberAnnualLimit = $certificationFoldersNumberAnnualLimit;

        return $this;
    }

    public function isAllowApi(): ?bool
    {
        return $this->allowApi;
    }

    public function setAllowApi(?bool $allowApi): self
    {
        $this->allowApi = $allowApi;

        return $this;
    }

    public function isAllowAnalytics(): ?bool
    {
        return $this->allowAnalytics;
    }

    public function setAllowAnalytics(?bool $allowAnalytics): self
    {
        $this->allowAnalytics = $allowAnalytics;

        return $this;
    }

    public function isAllowProposals(): ?bool
    {
        return $this->allowProposals;
    }

    public function setAllowProposals(?bool $allowProposals): self
    {
        $this->allowProposals = $allowProposals;

        return $this;
    }

    public function isAllowCertifiers(): ?bool
    {
        return $this->allowCertifiers;
    }

    public function setAllowCertifiers(?bool $allowCertifiers): self
    {
        $this->allowCertifiers = $allowCertifiers;

        return $this;
    }

    /**
     * @Serializer\VirtualProperty()
     * @Serializer\Groups({"Default"})
     */
    public function isAllowRegistrationFolderManualCreate(): bool
    {
        return !$this->isRegistrationFoldersNumberLimitReached();
    }

    /**
     * @Serializer\VirtualProperty()
     * @Serializer\Groups({"Default"})
     */
    public function isAllowCertificationFolderManualCreate(): bool
    {
        return !$this->isCertificationFoldersNumberLimitReached();
    }

    /**
     * @Serializer\VirtualProperty()
     * @Serializer\Groups({"Default"})
     */
    public function isAllowRegistrationFolderReads(): bool
    {
        return $this->getTrainingType() !== SubscriptionTrainingTypes::NONE()->getValue();
    }

    /**
     * @Serializer\VirtualProperty()
     * @Serializer\Groups({"Default"})
     */
    public function isRegistrationFoldersNumberLimitExceeded(): bool
    {
        return $this->getRegistrationFoldersNumberLimit() !== null && $this->getRegistrationFoldersNumberCount() > $this->getRegistrationFoldersNumberLimit();
    }

    /**
     * @Serializer\VirtualProperty()
     * @Serializer\Groups({"Default"})
     */
    public function isRegistrationFoldersNumberLimitReached(): bool
    {
        return $this->getRegistrationFoldersNumberLimit() !== null && $this->getRegistrationFoldersNumberCount() >= $this->getRegistrationFoldersNumberLimit();
    }

    /**
     * @Serializer\VirtualProperty()
     * @Serializer\Groups({"Default"})
     */
    public function isCertificationFoldersNumberLimitReached(): bool
    {
        return $this->getCertificationFoldersNumberAnnualLimit() !== null && $this->getCertificationFoldersNumberAnnualCount() >= $this->getCertificationFoldersNumberAnnualLimit();
    }

    /**
     * @param string $webhookEventName
     * @return bool|null
     */
    public function isAllowWebhook(string $webhookEventName): bool
    {
        $eventEntity = explode('.', $webhookEventName)[0];
        $isRegistrationFolderWebhook = in_array($eventEntity, ['registrationFolder', 'registrationFolderBilling', 'registrationFolderAlert', 'payment']);
        if ($isRegistrationFolderWebhook) {
            return $this->isAllowApi()
                && !$this->isRegistrationFoldersNumberLimitExceeded()
                && in_array($this->trainingType, SubscriptionTrainingTypes::getPaidTrainingTypes());
        }
        return $this->isAllowApi();
    }

    public function getCertificationFoldersNumberCount(): int
    {
        return $this->certificationFoldersNumberCount;
    }

    public function setCertificationFoldersNumberCount(int $certificationFoldersNumberCount): self
    {
        $this->certificationFoldersNumberCount = $certificationFoldersNumberCount;

        return $this;
    }

    public function getSmsSentNumberCount(): int
    {
        return $this->smsSentNumberCount;
    }

    public function setSmsSentNumberCount(int $smsSentNumberCount): self
    {
        $this->smsSentNumberCount = $smsSentNumberCount;

        return $this;
    }

    public function getSmsSentNumberPeriodStartDate(): ?DateTimeInterface
    {
        return $this->smsSentNumberPeriodStartDate;
    }

    public function setSmsSentNumberPeriodStartDate(?DateTimeInterface $smsSentNumberPeriodStartDate): self
    {
        $this->smsSentNumberPeriodStartDate = $smsSentNumberPeriodStartDate;

        return $this;
    }

    public function getSmsSentNumberPeriodEndDate(): ?DateTimeInterface
    {
        return $this->smsSentNumberPeriodEndDate;
    }

    public function setSmsSentNumberPeriodEndDate(?DateTimeInterface $smsSentNumberPeriodEndDate): self
    {
        $this->smsSentNumberPeriodEndDate = $smsSentNumberPeriodEndDate;

        return $this;
    }

    public function getStripeCustomerId(): ?string
    {
        return $this->stripeCustomerId;
    }

    public function setStripeCustomerId(?string $stripeCustomerId): Subscription
    {
        $this->stripeCustomerId = $stripeCustomerId;
        return $this;
    }

    /**
     * @Serializer\VirtualProperty()
     * @Serializer\Groups({"Default"})
     * @return bool
     */
    public function isStripeCustomer(): bool
    {
        return $this->getStripeCustomerId() !== null;
    }

    public function getOrganism(): Organism
    {
        return $this->organism;
    }

    public function setOrganism(Organism $organism): self
    {
        $this->organism = $organism;

        return $this;
    }

    public function getTrainingPartnership(): ?string
    {
        return $this->trainingPartnership;
    }

    public function setTrainingPartnership(?string $trainingPartnership): self
    {
        $this->trainingPartnership = $trainingPartnership;

        return $this;
    }

    public function getCertifierPartnership(): ?string
    {
        return $this->certifierPartnership;
    }

    public function setCertifierPartnership(?string $certifierPartnership): self
    {
        $this->certifierPartnership = $certifierPartnership;

        return $this;
    }

    public function getTrainingUsersLimit(): ?int
    {
        return $this->trainingUsersLimit;
    }

    public function setTrainingUsersLimit(?int $trainingUsersLimit): self
    {
        $this->trainingUsersLimit = $trainingUsersLimit;

        return $this;
    }

    public function isAllowRegistrationFolders(): ?bool
    {
        return $this->allowRegistrationFolders;
    }

    public function setAllowRegistrationFolders(?bool $allowRegistrationFolders): self
    {
        $this->allowRegistrationFolders = $allowRegistrationFolders;

        return $this;
    }

    public function isAllowCertifierPlus(): bool
    {
        return $this->allowCertifierPlus;
    }

    public function setAllowCertifierPlus(bool $allowCertifierPlus): self
    {
        $this->allowCertifierPlus = $allowCertifierPlus;

        return $this;
    }

    public function getCompany(): ?Company
    {
        return $this->company;
    }

    public function setCompany(?Company $company): self
    {
        // unset the owning side of the relation if necessary
        if ($company === null && $this->company !== null) {
            $this->company->setSubscription(null);
        }

        // set the owning side of the relation if necessary
        if ($company !== null && $company->getSubscription() !== $this) {
            $company->setSubscription($this);
        }

        $this->company = $company;

        return $this;
    }

    public function isAllowCertifierBetaFeatures(): bool
    {
        return $this->allowCertifierBetaFeatures;
    }

    public function setAllowCertifierBetaFeatures(bool $allowCertifierBetaFeatures): self
    {
        $this->allowCertifierBetaFeatures = $allowCertifierBetaFeatures;

        return $this;
    }

    public function getCertificationFoldersNumberCap(): ?int
    {
        return $this->certificationFoldersNumberCap;
    }

    public function setCertificationFoldersNumberCap(?int $certificationFoldersNumberCap): self
    {
        $this->certificationFoldersNumberCap = $certificationFoldersNumberCap;

        return $this;
    }

    public function getCertificationFolderPrice(): ?float
    {
        return $this->certificationFolderPrice;
    }

    public function setCertificationFolderPrice(?float $certificationFolderPrice): self
    {
        $this->certificationFolderPrice = $certificationFolderPrice;

        return $this;
    }

    public function getCertifierUsersLimit(): ?int
    {
        return $this->certifierUsersLimit;
    }

    public function setCertifierUsersLimit(?int $certifierUsersLimit): self
    {
        $this->certifierUsersLimit = $certifierUsersLimit;

        return $this;
    }

    public function getRegistrationFolderNumberPeriodStartDate(): ?DateTimeInterface
    {
        return $this->registrationFolderNumberPeriodStartDate;
    }

    public function setRegistrationFolderNumberPeriodStartDate(?DateTimeInterface $registrationFolderNumberPeriodStartDate): self
    {
        $this->registrationFolderNumberPeriodStartDate = $registrationFolderNumberPeriodStartDate;

        return $this;
    }

    public function getRegistrationFolderNumberPeriodEndDate(): ?DateTimeInterface
    {
        return $this->registrationFolderNumberPeriodEndDate;
    }

    public function setRegistrationFolderNumberPeriodEndDate(?DateTimeInterface $registrationFolderNumberPeriodEndDate): self
    {
        $this->registrationFolderNumberPeriodEndDate = $registrationFolderNumberPeriodEndDate;

        return $this;
    }

    public function getCertificationFoldersNumberPeriodStartDate(): ?DateTimeInterface
    {
        return $this->certificationFoldersNumberPeriodStartDate;
    }

    public function setCertificationFoldersNumberPeriodStartDate(?DateTimeInterface $certificationFoldersNumberPeriodStartDate): self
    {
        $this->certificationFoldersNumberPeriodStartDate = $certificationFoldersNumberPeriodStartDate;

        return $this;
    }

    public function getCertificationFoldersNumberPeriodEndDate(): ?DateTimeInterface
    {
        return $this->certificationFoldersNumberPeriodEndDate;
    }

    public function setCertificationFoldersNumberPeriodEndDate(?DateTimeInterface $certificationFoldersNumberPeriodEndDate): self
    {
        $this->certificationFoldersNumberPeriodEndDate = $certificationFoldersNumberPeriodEndDate;

        return $this;
    }

    public function getTrainingTrialEndDate(): ?DateTimeInterface
    {
        return $this->trainingTrialEndDate;
    }

    public function setTrainingTrialEndDate(?DateTimeInterface $trainingTrialEndDate): self
    {
        $this->trainingTrialEndDate = $trainingTrialEndDate;

        return $this;
    }

    public function getCertifierTrialEndDate(): ?DateTimeInterface
    {
        return $this->certifierTrialEndDate;
    }

    public function setCertifierTrialEndDate(?DateTimeInterface $certifierTrialEndDate): self
    {
        $this->certifierTrialEndDate = $certifierTrialEndDate;

        return $this;
    }

    public function getLastRegistrationFolderId(): ?int
    {
        return $this->lastRegistrationFolderId;
    }

    public function setLastRegistrationFolderId(?int $lastRegistrationFolderId): self
    {
        $this->lastRegistrationFolderId = $lastRegistrationFolderId;

        return $this;
    }

    public function getRequestCount(): int
    {
        return $this->requestCount;
    }

    public function setRequestCount(int $requestCount): self
    {
        $this->requestCount = $requestCount;

        return $this;
    }

    public function isAllowPaidUsage(): bool
    {
        return $this->allowPaidUsage;
    }

    public function setAllowPaidUsage(bool $allowPaidUsage): self
    {
        $this->allowPaidUsage = $allowPaidUsage;

        return $this;
    }

    public function getCertificationFoldersNumberAnnualCount(): ?int
    {
        return $this->certificationFoldersNumberAnnualCount;
    }

    public function setCertificationFoldersNumberAnnualCount(?int $certificationFoldersNumberAnnualCount): self
    {
        $this->certificationFoldersNumberAnnualCount = $certificationFoldersNumberAnnualCount;

        return $this;
    }
}
