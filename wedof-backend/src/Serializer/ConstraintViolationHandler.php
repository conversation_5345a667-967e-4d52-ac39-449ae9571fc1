<?php

namespace App\Serializer;

use <PERSON><PERSON>\Serializer\GraphNavigatorInterface;
use <PERSON><PERSON>\Serializer\Handler\SubscribingHandlerInterface;
use <PERSON><PERSON>\Serializer\SerializationContext;
use <PERSON><PERSON>\Serializer\Visitor\SerializationVisitorInterface;
use <PERSON><PERSON><PERSON>ny\Component\Validator\ConstraintViolationList;

/**
 * Class ConstraintViolationHandler
 * @package App\Serializer
 * Overrides the default JMS JMS\Serializer\Handler\ConstraintViolationHandler
 * In order to follow rfc7807 (https://datatracker.ietf.org/doc/html/rfc7807)
 */
final class ConstraintViolationHandler implements SubscribingHandlerInterface
{
    /**
     * @return array
     */
    public static function getSubscribingMethods()
    {
        return [
            [
                'direction' => GraphNavigatorInterface::DIRECTION_SERIALIZATION,
                'type' => ConstraintViolationList::class,
                'format' => 'json',
                'method' => 'serializeListToJson',
                'priority' => -915 // Required to be sure to take precedence over SubscribingHandlerInterface
            ]
        ];
    }

    /**
     * @param SerializationVisitorInterface $visitor
     * @param ConstraintViolationList $violationList
     * @param array $type
     * @param SerializationContext $context
     * @return array
     */
    public function serializeListToJson(SerializationVisitorInterface $visitor, ConstraintViolationList $violationList, array $type, SerializationContext $context)
    {
        $violations = [];
        $messages = [];
        foreach ($violationList as $violation) {
            $propertyPath = $violation->getPropertyPath();
            $violationEntry = [
                'propertyPath' => $propertyPath,
                'title' => $violation->getMessage(),
                'code' => $violation->getCode(),
            ];
            $violations[] = $violationEntry;
            $prefix = $propertyPath ? sprintf('%s: ', $propertyPath) : '';
            $messages[] = $prefix . $violation->getMessage();
        }
        $detail = implode("\n", $messages);
        // Follows https://datatracker.ietf.org/doc/html/rfc7807
        return [
            'type' => 'https://symfony.com/errors/validation',
            'title' => 'Validation Failed',
            'status' => 400,
            'detail' => $detail,
            'violations' => $violations
        ];
    }
}
