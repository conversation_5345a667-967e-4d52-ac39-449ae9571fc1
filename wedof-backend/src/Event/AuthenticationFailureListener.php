<?php

namespace App\Event;

use App\Exception\WedofAccessDeniedHttpException;
use <PERSON><PERSON>\Serializer\SerializerInterface;
use Lexik\Bundle\JWTAuthenticationBundle\Event\AuthenticationFailureEvent;
use Symfony\Component\ErrorHandler\Exception\FlattenException;
use Symfony\Component\HttpFoundation\Response;

class AuthenticationFailureListener
{

    /** @var SerializerInterface */
    private SerializerInterface $serializer;

    public function __construct(SerializerInterface $serializer)
    {
        $this->serializer = $serializer;
    }

    /**
     * @param AuthenticationFailureEvent $event
     */
    public function onAuthenticationFailureResponse(AuthenticationFailureEvent $event)
    {
        $exception = new WedofAccessDeniedHttpException('Adresse email ou mot de passe incorrect. Cliquez sur mot de passe oublié pour réinitialiser votre connexion.');
        $flattenException = FlattenException::create($exception);
        $response = new Response(
            $this->serializer->serialize($flattenException, 'json'), // Call FOS\RestBundle\Serializer\Normalizer\FlattenExceptionHandler
            Response::HTTP_FORBIDDEN
        );
        $event->setResponse($response);
    }
}
