<?php

namespace App\Event;

use App\Entity\Organism;
use App\Entity\User;
use Symfony\Component\HttpFoundation\File\File;
use Symfony\Contracts\EventDispatcher\Event;

class SignDocumentEvents extends Event
{

    public const SHOW_DOCUMENT = "SHOW_DOCUMENT";
    public const SIGN_DOCUMENT = "SIGN_DOCUMENT";

    public const PREVIEW_DOCUMENT = "PREVIEW_DOCUMENT";
    public const BEFORE_GENERATE_CODE = "CAN_GENERATE_CODE";
    public const BEFORE_VALIDATE_CODE = "CAN_VALIDATE_CODE";
    private string $documentId;

    private Organism $organism;

    /**
     * @var User
     */
    private User $user;

    /**
     * @var mixed|null
     */
    private array $params;

    private ?File $document;
    private bool $allowedValidateCode;

    private bool $allowedGenerateCode;
    private ?string $documentName;

    /**
     * @return Organism
     */
    public function getOrganism(): Organism
    {
        return $this->organism;
    }

    public function __construct(string $documentId,
                                User   $user, Organism $organism,
                                array  $params = null)
    {
        $this->organism = $organism;
        $this->user = $user;
        $this->documentId = $documentId;
        $this->allowedGenerateCode = false;
        $this->allowedValidateCode = false;
        $this->document = null;
        $this->documentName = null;
        $this->params = $params;
    }

    public static function getEvents(): array
    {
        return array(
            self::SHOW_DOCUMENT,
            self::SIGN_DOCUMENT,
            self::PREVIEW_DOCUMENT,
            self::BEFORE_GENERATE_CODE,
            self::BEFORE_VALIDATE_CODE
        );
    }

    /**
     * @return string
     */
    public function getDocumentId(): string
    {
        return $this->documentId;
    }

    /**
     * @return mixed|null
     */
    public function getParams()
    {
        return $this->params;
    }

    /**
     * @return bool
     */
    public function isAllowedGenerateCode(): bool
    {
        return $this->allowedGenerateCode;
    }

    /**
     * @param bool $allowedGenerateCode
     */
    public function setAllowedGenerateCode(bool $allowedGenerateCode): void
    {
        $this->allowedGenerateCode = $allowedGenerateCode;
    }

    /**
     * @return bool
     */
    public function isAllowedValidateCode(): bool
    {
        return $this->allowedValidateCode;
    }

    /**
     * @param bool $allowedValidateCode
     */
    public function setAllowedValidateCode(bool $allowedValidateCode): void
    {
        $this->allowedValidateCode = $allowedValidateCode;
    }

    /**
     * @return File|null
     */
    public function getDocument(): ?File
    {
        return $this->document;
    }

    /**
     * @param File|null $document
     */
    public function setDocument(?File $document): void
    {
        $this->document = $document;
    }

    /**
     * @param string|null $documentName
     * @return void
     */
    public function setDocumentName(?string $documentName): void
    {
        $this->documentName = $documentName;
    }

    /**
     * @return string|null
     */
    public function getDocumentName(): ?string
    {
        return $this->documentName;
    }

    /**
     * @return User
     */
    public function getUser(): User
    {
        return $this->user;
    }
}