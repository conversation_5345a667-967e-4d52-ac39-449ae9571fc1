<?php
// src/Event/Session/SessionSerializeSubscriber.php
namespace App\Event\Session;

use App\Entity\Session;
use App\Service\CertificationService;
use App\Service\RegistrationFolderService;
use App\Service\TrainingService;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use <PERSON><PERSON>\Serializer\EventDispatcher\EventSubscriberInterface;
use <PERSON><PERSON>\Serializer\EventDispatcher\ObjectEvent;
use J<PERSON>\Serializer\JsonSerializationVisitor;
use J<PERSON>\Serializer\Metadata\StaticPropertyMetadata;

class SessionSerializeSubscriber implements EventSubscriberInterface
{
    private RegistrationFolderService $registrationFolderService;
    private TrainingService $trainingService;
    private CertificationService $certificationService;

    public function __construct(RegistrationFolderService $registrationFolderService, TrainingService $trainingService, CertificationService $certificationService)
    {
        $this->registrationFolderService = $registrationFolderService;
        $this->trainingService = $trainingService;
        $this->certificationService = $certificationService;
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return array(
            array(
                'event' => 'serializer.post_serialize',
                'method' => 'onPostSerialize',
                'class' => Session::class, // if no class, subscribe to every serialization,
                'format' => 'json'
            ),
        );
    }

    /**
     * @param ObjectEvent $event
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function onPostSerialize(ObjectEvent $event): void
    {
        /** @var Session $session */
        $session = $event->getObject();
        /** @var JsonSerializationVisitor $visitor */
        $visitor = $event->getVisitor();

        $city = "À distance";
        if (isset($session->getTrainingAction()->getRawData()['trainingAddress']['city'])) {
            $city = $session->getTrainingAction()->getRawData()['trainingAddress']['city'];
        }
        $sessionInfo['city'] = $city;
        if (!in_array('attendee', $event->getContext()->getAttribute('groups'))) {
            // groupe de serialisation "Default" => groupe du propriétaire / autre groupe "certifier"
            if (in_array('Default', $event->getContext()->getAttribute('groups'))) {
                $sessionInfo['registrationFolderCount'] = $this->registrationFolderService->countBySession($session);
                $sessionInfo['activeAttendeeCount'] = $this->registrationFolderService->countActiveAttendeesForSession($session);
                $sessionInfo['trainingTitle'] = $this->trainingService->getTitleFromSession($session);
                $sessionInfo['certificationName'] = $this->certificationService->getNameFromSession($session);
                $sessionInfo['totalTvaTTC'] = $session->getTrainingAction()->getRawData()['totalTvaTTC'] ?? '';
            }
        }
        $visitor->visitProperty(new StaticPropertyMetadata('', 'sessionInfo', null), $sessionInfo);
    }
}
