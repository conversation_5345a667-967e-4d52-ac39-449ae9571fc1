<?php

namespace App\Event;

use App\Entity\User;
use App\Service\SubscriptionService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpKernel\Event\ControllerEvent;
use Symfony\Component\Security\Core\Security;

class KernelControllerEventListener
{
    private Security $security;
    private SubscriptionService $subscriptionService;
    private EntityManagerInterface $entityManager;

    public function __construct(Security $security, SubscriptionService $subscriptionService, EntityManagerInterface $entityManager)
    {
        $this->security = $security;
        $this->subscriptionService = $subscriptionService;
        $this->entityManager = $entityManager;
    }

    public function onKernelController(ControllerEvent $event)
    {
        $headers = $event->getRequest()->headers->all();
        if ($this->security->isGranted('IS_IMPERSONATOR') && isset($headers['admin-view']) && $headers['admin-view'][0] === 'true') {
            /** @var User $user */
            $user = $this->security->getUser();
            $this->entityManager->getUnitOfWork()->markReadOnly($user->getMainOrganism()->getSubscription());
            $this->subscriptionService->mutateIntoAdminSubscription($user->getMainOrganism()->getSubscription());
        }
    }
}