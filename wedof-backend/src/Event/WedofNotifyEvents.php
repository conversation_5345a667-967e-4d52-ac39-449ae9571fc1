<?php

namespace App\Event;

use App\Entity\CertificationFolder;
use App\Entity\RegistrationFolder;

class WedofNotifyEvents
{
    private $entity;
    private string $to;
    private string $content;

    /**
     * @param RegistrationFolder|CertificationFolder $entity
     * @param string $to
     * @param string $content
     */
    public function __construct($entity, string $to, string $content)
    {
        $this->entity = $entity;
        $this->to = $to;
        $this->content = $content;
    }

    /**
     * @return string
     */
    public function getTo(): string
    {
        return $this->to;
    }

    /**
     * @return string
     */
    public function getContent(): string
    {
        return $this->content;
    }

    /**
     * @return CertificationFolder|RegistrationFolder
     */
    public function getEntity()
    {
        return $this->entity;
    }
}
