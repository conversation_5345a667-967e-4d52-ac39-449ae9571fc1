<?php
// src/Event/Subscription/SubscriptionSerializeSubscriber.php
namespace App\Event\Subscription;

use App\Entity\Subscription;
use App\Library\utils\enums\SubscriptionCertifierTypes;
use App\Service\ApplicationService;
use <PERSON><PERSON>\Serializer\EventDispatcher\EventSubscriberInterface;
use <PERSON><PERSON>\Serializer\EventDispatcher\ObjectEvent;
use <PERSON><PERSON>\Serializer\JsonSerializationVisitor;
use <PERSON><PERSON>\Serializer\Metadata\StaticPropertyMetadata;
use ReflectionException;

class SubscriptionSerializeSubscriber implements EventSubscriberInterface
{
    private ApplicationService $applicationService;

    public function __construct(ApplicationService $applicationService)
    {
        $this->applicationService = $applicationService;
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return array(
            array(
                'event' => 'serializer.post_serialize',
                'method' => 'onPostSerialize',
                'class' => Subscription::class, // if no class, subscribe to every serialization,
                'format' => 'json'
            ),
        );
    }

    /**
     * @param ObjectEvent $event
     * @throws ReflectionException
     */
    public function onPostSerialize(ObjectEvent $event): void
    {
        if (!in_array('dataPartner', $event->getContext()->getAttribute('groups'))) {
            /** @var Subscription $subscription */
            $subscription = $event->getObject();
            /** @var JsonSerializationVisitor $visitor */
            $visitor = $event->getVisitor();

            $allowedApps = $this->applicationService->listAllowedAppIds($subscription);
            $visitor->visitProperty(new StaticPropertyMetadata('', 'allowedApps', null), $allowedApps);
        }
    }
}
