<?php

namespace App\Event\Subscription;

use App\Entity\Subscription;
use Symfony\Contracts\EventDispatcher\Event;

class SubscriptionEvents extends Event
{
    public const CREATED = 'subscription.created';
    public const UPDATED = 'subscription.updated';
    public const TRIAL_STARTED = 'subscription.trialStarted';
    public const REQUEST_COUNT_RESET = 'subscription.requestCountReset';

    private Subscription $subscription;

    public function __construct(Subscription $subscription)
    {
        $this->subscription = $subscription;
    }

    /**
     * @return Subscription
     */
    public function getSubscription(): Subscription
    {
        return $this->subscription;
    }
}