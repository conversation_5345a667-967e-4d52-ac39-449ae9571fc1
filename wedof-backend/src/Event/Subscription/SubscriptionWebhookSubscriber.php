<?php

namespace App\Event\Subscription;

use App\Message\SendWebhook;
use App\Service\OrganismService;
use App\Service\WebhookService;
use Doctrine\Common\Collections\ArrayCollection;
use <PERSON><PERSON>\Serializer\SerializationContext;
use <PERSON><PERSON>\Serializer\SerializerInterface;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Security\Core\Authentication\Token\SwitchUserToken;
use Symfony\Component\Security\Core\Security;

class SubscriptionWebhookSubscriber implements EventSubscriberInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    private SerializerInterface $serializer;
    private MessageBusInterface $messageBus;
    private OrganismService $organismService;
    private WebhookService $webhookService;
    private Security $security;

    //-------------------
    // METHODES PUBLIQUES
    //-------------------
    public function __construct(SerializerInterface $serializer, MessageBusInterface $messageBus, OrganismService $organismService, WebhookService $webhookService, Security $security)
    {
        $this->serializer = $serializer;
        $this->messageBus = $messageBus;
        $this->organismService = $organismService;
        $this->webhookService = $webhookService;
        $this->security = $security;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    public static function getSubscribedEvents(): array
    {
        return [
            SubscriptionEvents::CREATED => 'webhookOnSubscription',
            SubscriptionEvents::UPDATED => 'webhookOnSubscription',
            SubscriptionEvents::TRIAL_STARTED => 'webhookOnSubscription'
        ];
    }

    /**
     * @param SubscriptionEvents $event
     * @param string $eventName
     */
    public function webhookOnSubscription(SubscriptionEvents $event, string $eventName): void
    {
        $this->logger->debug("[Webhook Subscription] " . self::class . " event $eventName");
        $subscription = $event->getSubscription();

        // Set a default organism as at this point the user is not linked to an organism yet
        // Pick Wedof if exists, otherwise pick Kag (helpful for dev envs)
        $organisms = new ArrayCollection();
        $reseller = $subscription->getOrganism()->getReseller();
        if ($reseller) {
            $organisms->add($reseller);
        }
        $organism = $this->organismService->getBySiret('90301927100019') ?? $this->organismService->getBySiret('53222292400039');
        if ($organism) {
            $organisms->add($organism);
        }
        $webhooks = $this->webhookService->listByOrganismsAndEventAndType($organisms, $eventName, []);
        if (sizeof($webhooks) > 0) {
            $this->logger->debug("[Webhook Subscription] " . sizeof($webhooks) . " to send");
            $serializationContext = new SerializationContext();
            $serializationContext->setSerializeNull(true);
            $serializationContext->setGroups('webhook');
            $payload = $this->serializer->serialize($subscription, 'json', $serializationContext);
            $authorId = null;
            $token = $this->security->getToken();
            if ($token && $token->getUser()) {
                $author = $token instanceof SwitchUserToken ? $token->getOriginalToken()->getUser() : $token->getUser();
                $authorId = $author->getId();
            }
            foreach ($webhooks as $webhook) {
                $message = new SendWebhook($webhook->getId(), $eventName, $payload, get_class($event->getSubscription()), $event->getSubscription()->getId(), $authorId);
                $this->messageBus->dispatch($message);
                $this->logger->debug("[Webhook Subscription] event $eventName sent !");
            }
        } else {
            $this->logger->debug("[Webhook Subscription] no event to send");
        }
    }

    //-------------------
    // METHODES PRIVEES
    //-------------------
}
