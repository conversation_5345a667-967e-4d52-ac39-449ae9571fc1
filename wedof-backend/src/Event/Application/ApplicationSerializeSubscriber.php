<?php
// src/Event/Application/ApplicationSerializeSubscriber.php
namespace App\Event\Application;

use App\Entity\Application;
use <PERSON><PERSON>\Serializer\EventDispatcher\EventSubscriberInterface;
use <PERSON><PERSON>\Serializer\EventDispatcher\ObjectEvent;
use <PERSON><PERSON>\Serializer\JsonSerializationVisitor;
use <PERSON><PERSON>\Serializer\Metadata\StaticPropertyMetadata;

class ApplicationSerializeSubscriber implements EventSubscriberInterface
{
    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return array(
            array(
                'event' => 'serializer.post_serialize',
                'method' => 'onPostSerialize',
                'class' => Application::class, // if no class, subscribe to every serialization,
                'format' => 'json'
            ),
        );
    }

    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    /**
     * @param ObjectEvent $event
     */
    public function onPostSerialize(ObjectEvent $event): void
    {
        /** @var Application $application */
        $application = $event->getObject();
        /** @var JsonSerializationVisitor $visitor */
        $visitor = $event->getVisitor();
        $metadataCleaned = $application->getMetadata();
        if (!empty($metadataCleaned['credentials'])) { //global exclusion
            unset($metadataCleaned['credentials']);
        }
        $visitor->visitProperty(new StaticPropertyMetadata('', 'metadata', null), $metadataCleaned);

    }
}
