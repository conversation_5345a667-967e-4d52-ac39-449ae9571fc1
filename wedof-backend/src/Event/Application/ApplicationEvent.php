<?php

namespace App\Event\Application;

use App\Entity\Application;
use Symfony\Contracts\EventDispatcher\Event;

class ApplicationEvent extends Event
{
    public const SHOW = 'application.show';

    public const ENABLED = 'application.enabled';
    public const BEFORE_ENABLE = 'application.before_enable';

    public const DISABLED = 'application.disabled';
    public const BEFORE_DISABLE = 'application.before_disable';
    public const PENDING_DISABLE = 'application.pending_disable';
    public const PENDING_DISABLE_TRIAL = 'application.pending_disable_trial';
    public const TRIAL_STARTED = 'application.trial_start';
    public const TRIAL_ENDED = 'application.trial_ended';
    public const UPDATED_METADATA = 'application.metadata';
    public const NEW_OAUTH = 'application.new_oauth';
    public const REFRESH_OAUTH = 'application.refresh_oauth';

    protected Application $application;
    protected ?array $previousMetadata;

    public function __construct(Application $application, array $previousMetadata = null)
    {
        $this->application = $application;
        $this->previousMetadata = $previousMetadata;
    }

    public function getApplication(): Application
    {
        return $this->application;
    }

    public function getPreviousMetadata(): ?array
    {
        return $this->previousMetadata;
    }
}
