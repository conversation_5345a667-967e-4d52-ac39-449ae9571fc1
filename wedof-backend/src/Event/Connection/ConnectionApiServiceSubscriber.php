<?php

namespace App\Event\Connection;

use App\Library\utils\enums\DataProviders;
use App\Service\DataProviders\BaseApiService;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

class ConnectionApiServiceSubscriber implements EventSubscriberInterface
{
    public function __construct(){}

    public static function getSubscribedEvents(): array
    {
        return [
            ConnectionEvents::AUTH_SUCCESS => 'startInitialization',
            ConnectionEvents::INITIALIZE_COMPLETED => 'initializeCompleted'
        ];
    }

    /**
     * @param ConnectionEvents $event
     * @param string $eventName
     * @return void
     */
    public function startInitialization(ConnectionEvents $event, string $eventName): void
    {
        $apiService = BaseApiService::getApiServiceByDataProvider(DataProviders::from($event->getConnection()->getDataProvider()));
        $apiService->startInitialization($event->getConnection());
    }

    /**
     * @param ConnectionEvents $event
     * @param string $eventName
     * @return void
     */
    public function initializeCompleted(ConnectionEvents $event, string $eventName): void
    {
        $apiService = BaseApiService::getApiServiceByDataProvider(DataProviders::from($event->getConnection()->getDataProvider()));
        $apiService->initializeCompleted($event->getConnection());
    }
}