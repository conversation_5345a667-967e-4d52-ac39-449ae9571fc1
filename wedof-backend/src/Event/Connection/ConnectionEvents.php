<?php

namespace App\Event\Connection;

use App\Entity\Connection;
use Symfony\Contracts\EventDispatcher\Event;

class ConnectionEvents extends Event
{
    public const AUTH_SUCCESS = 'connection.authSuccess';
    public const AUTH_FAIL = 'connection.authFail';
    public const INITIALIZE_STARTED = 'connection.initializeStarted';
    public const INITIALIZE_COMPLETED = 'connection.initialized';
    public const AUTH_REVOKED = 'connection.revoked';

    protected Connection $connection;

    public function __construct(Connection $connection)
    {
        $this->connection = $connection;
    }

    /**
     * @return Connection
     */
    public function getConnection(): Connection
    {
        return $this->connection;
    }
}