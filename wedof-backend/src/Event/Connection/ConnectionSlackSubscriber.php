<?php

namespace App\Event\Connection;

use App\Library\utils\Tools;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Throwable;

class ConnectionSlackSubscriber implements EventSubscriberInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;

    public function setLogger(LoggerInterface $logger)
    {
        $this->logger = $logger;
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return [
            ConnectionEvents::AUTH_SUCCESS => 'sendSlackOnAuthSuccess',
            ConnectionEvents::AUTH_FAIL => 'sendSlackOnAuthFail'
        ];
    }

    /**
     * @param ConnectionEvents $event
     * @param string $eventName
     */
    public function sendSlackOnAuthSuccess(ConnectionEvents $event, string $eventName): void
    {
        if (!$_ENV["COMMERCIAL_SLACK_URI"]) {
            return;
        }
        $connection = $event->getConnection();
        try {
            $organism = $connection->getOrganism();
            $reseller = $organism->getReseller() ? " (Reseller : " . $organism->getReseller()->getName() . ")" : null;
            Tools::getHttpClient()->request('POST', $_ENV["COMMERCIAL_SLACK_URI"], ['json' => [
                'blocks' => [
                    0 => [
                        'type' => 'section',
                        'text' => [
                            'type' => 'mrkdwn',
                            'text' => "*Synchronisation " . strtoupper($connection->getDataProvider()) . " réussie !*"
                        ]
                    ],
                    1 => [
                        'type' => 'section',
                        'text' => [
                            'type' => 'mrkdwn',
                            'text' => "La connexion " . strtoupper($connection->getDataProvider()) . " (" . $connection->getId() . ") vient d'être activée pour l'organisme " . $organism->getName() . $reseller
                        ]
                    ]
                ]
            ]]);
        } catch (Throwable $e) {
            $this->logger->error($e->getMessage());
        }
    }

    /**
     * @param ConnectionEvents $event
     * @param string $eventName
     */
    public function sendSlackOnAuthFail(ConnectionEvents $event, string $eventName): void
    {
        if (!$_ENV["COMMERCIAL_SLACK_URI"]) {
            return;
        }
        $connection = $event->getConnection();
        try {
            Tools::getHttpClient()->request('POST', $_ENV["COMMERCIAL_SLACK_URI"], ['json' => [
                'blocks' => [
                    0 => [
                        'type' => 'section',
                        'text' => [
                            'type' => 'mrkdwn',
                            'text' => "*Un organisme a perdu sa connexion " . strtoupper($connection->getDataProvider()) . " !*"
                        ]
                    ],
                    1 => [
                        'type' => 'section',
                        'text' => [
                            'type' => 'mrkdwn',
                            'text' => "L'organisme " . $connection->getOrganism()->getName() . " a perdu sa connexion " . strtoupper($connection->getDataProvider()) . " (" . $connection->getId() . ")"
                        ]
                    ]
                ]
            ]]);
        } catch (Throwable $e) {
            $this->logger->error($e->getMessage());
        }
    }
}