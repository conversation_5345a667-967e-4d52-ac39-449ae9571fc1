<?php

namespace App\Event\Connection;

use App\Message\SendWebhook;
use App\Service\OrganismService;
use App\Service\WebhookService;
use Doctrine\Common\Collections\ArrayCollection;
use <PERSON><PERSON>\Serializer\SerializationContext;
use <PERSON><PERSON>\Serializer\SerializerInterface;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Messenger\MessageBusInterface;

class ConnectionWebhookSubscriber implements EventSubscriberInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    private SerializerInterface $serializer;
    private MessageBusInterface $messageBus;
    private OrganismService $organismService;
    private WebhookService $webhookService;

    public function __construct(SerializerInterface $serializer, OrganismService $organismService, MessageBusInterface $messageBus, WebhookService $webhookService)
    {
        $this->serializer = $serializer;
        $this->messageBus = $messageBus;
        $this->organismService = $organismService;
        $this->webhookService = $webhookService;
    }

    public function setLogger(LoggerInterface $logger)
    {
        $this->logger = $logger;
    }

    public static function getSubscribedEvents(): array
    {
        return [
            ConnectionEvents::AUTH_SUCCESS => 'webhookOnConnection',
            ConnectionEvents::INITIALIZE_STARTED => 'webhookOnConnection',
            ConnectionEvents::INITIALIZE_COMPLETED => 'webhookOnConnection',
            ConnectionEvents::AUTH_REVOKED => 'webhookOnConnection'
        ];
    }

    public function webhookOnConnection(ConnectionEvents $event, string $eventName): void
    {
        $this->logger->debug("[Webhook Connection] " . self::class . " event $eventName");
        // Set a default organism as at this point the user is not linked to an organism yet
        // Pick Wedof if exists, otherwise pick Kag (helpful for dev envs)
        $organisms = new ArrayCollection();
        if (in_array($eventName, [ConnectionEvents::INITIALIZE_STARTED, ConnectionEvents::INITIALIZE_COMPLETED, ConnectionEvents::AUTH_REVOKED])) {
            $reseller = $event->getConnection()->getOrganism()->getReseller();
            if ($reseller) {
                $organisms->add($reseller);
            }
        }
        $organism = $this->organismService->getBySiret('90301927100019') ?? $this->organismService->getBySiret('53222292400039');
        if ($organism) {
            $organisms->add($organism);
        }
        $webhooks = $this->webhookService->listByOrganismsAndEventAndType($organisms, $eventName);
        if (sizeof($webhooks) > 0) {
            $this->logger->debug("[Webhook Connection] " . sizeof($webhooks) . " to send");
            $serializationContext = new SerializationContext();
            $serializationContext->setSerializeNull(true);
            $serializationContext->setGroups('Default');
            $payload = $this->serializer->serialize($event->getConnection(), 'json', $serializationContext);
            foreach ($webhooks as $webhook) {
                $message = new SendWebhook($webhook->getId(), $eventName, $payload, get_class($event->getConnection()), $event->getConnection()->getId());
                $this->messageBus->dispatch($message);
                $this->logger->debug("[Webhook Connection] event $eventName sent !");
            }
        } else {
            $this->logger->debug("[Webhook Connection] no event to send");
        }
    }

}