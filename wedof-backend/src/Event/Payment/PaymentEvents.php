<?php

namespace App\Event\Payment;

use App\Entity\Payment;
use Symfony\Contracts\EventDispatcher\Event;

class PaymentEvents extends Event
{
    public const DEPOSIT_WAITING = 'payment.depositWaiting';
    public const DEPOSIT_REJECTED = 'payment.depositRejected';
    public const DEPOSIT_ISSUED = 'payment.depositIssued';
    public const ISSUED = 'payment.issued';
    public const WAITING = 'payment.waiting';
    public const REJECTED = 'payment.rejected';

    protected Payment $payment;

    public function __construct(Payment $payment)
    {
        $this->payment = $payment;
    }

    public function getPayment(): Payment
    {
        return $this->payment;
    }
}