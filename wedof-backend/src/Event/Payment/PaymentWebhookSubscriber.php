<?php

namespace App\Event\Payment;

use App\Entity\Webhook;
use App\Message\SendWebhook;
use App\Service\WebhookService;
use Doctrine\Common\Collections\ArrayCollection;
use <PERSON><PERSON>\Serializer\SerializationContext;
use <PERSON><PERSON>\Serializer\SerializerInterface;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Messenger\MessageBusInterface;

class PaymentWebhookSubscriber implements EventSubscriberInterface, LoggerAwareInterface
{

    private LoggerInterface $logger;
    private WebhookService $webhookService;
    private MessageBusInterface $messageBus;
    private SerializerInterface $serializer;

    public function __construct(WebhookService $webhookService, MessageBusInterface $messageBus, SerializerInterface $serializer)
    {
        $this->webhookService = $webhookService;
        $this->messageBus = $messageBus;
        $this->serializer = $serializer;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    public static function getSubscribedEvents(): array
    {
        return [
            PaymentEvents::DEPOSIT_WAITING => 'webhookOnPayment',
            PaymentEvents::DEPOSIT_REJECTED => 'webhookOnPayment',
            PaymentEvents::DEPOSIT_ISSUED => 'webhookOnPayment',
            PaymentEvents::ISSUED => 'webhookOnPayment',
            PaymentEvents::WAITING => 'webhookOnPayment',
            PaymentEvents::REJECTED => 'webhookOnPayment'
        ];
    }

    public function webhookOnPayment(PaymentEvents $event, string $eventName)
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        $this->logger->info(self::class . " event $eventName");
        $webhooks = $this->webhookService->listByOrganismsAndEventAndType(new ArrayCollection([$event->getPayment()->getOrganism()]), $eventName);
        if (sizeof($webhooks) > 0) {
            $serializationContext = new SerializationContext();
            $serializationContext->setSerializeNull(true);
            $serializationContext->setGroups("owner");
            $payload = $this->serializer->serialize($event->getPayment(), 'json', $serializationContext);
            /** @var Webhook $webhook */
            foreach ($webhooks as $webhook) {
                $message = new SendWebhook($webhook->getId(), $eventName, $payload, get_class($event->getPayment()), $event->getPayment()->getId());
                $this->messageBus->dispatch($message);
            }
        }
    }
}
