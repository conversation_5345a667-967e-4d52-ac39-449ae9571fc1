<?php
// src/Event/Certification/CertificationSerializeSubscriber.php
namespace App\Event\Certification;

use App\Entity\Certification;
use App\Entity\Organism;
use App\Entity\User;
use App\Library\utils\enums\CertificationPartnerStates;
use App\Service\AccessService;
use App\Service\CertificationFolderService;
use App\Service\CertificationPartnerService;
use App\Service\OrganismService;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use JMS\Serializer\EventDispatcher\EventSubscriberInterface;
use J<PERSON>\Serializer\EventDispatcher\ObjectEvent;
use J<PERSON>\Serializer\JsonSerializationVisitor;
use JMS\Serializer\Metadata\StaticPropertyMetadata;
use Symfony\Component\Security\Core\Security;

class CertificationSerializeSubscriber implements EventSubscriberInterface
{
    private Security $security;
    private AccessService $accessService;
    private CertificationFolderService $certificationFolderService;
    private CertificationPartnerService $certificationPartnerService;

    public function __construct(Security $security, AccessService $accessService, OrganismService $organismService, CertificationPartnerService $certificationPartnerService, CertificationFolderService $certificationFolderService)
    {
        $this->security = $security;
        $this->accessService = $accessService;
        $this->certificationFolderService = $certificationFolderService;
        $this->certificationPartnerService = $certificationPartnerService;
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return array(
            array(
                'event' => 'serializer.post_serialize',
                'method' => 'onPostSerialize',
                'class' => Certification::class, // if no class, subscribe to every serialization,
                'format' => 'json'
            ),
        );
    }

    /**
     * @param ObjectEvent $event
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function onPostSerialize(ObjectEvent $event): void
    {
        $serializationGroups = $event->getContext()->getAttribute('groups');

        if (!in_array('attendee', $serializationGroups) && !in_array('lite', $serializationGroups)) {
            /** @var User $user */
            $user = $this->security->getUser();
            /** @var Certification $certification */
            $certification = $event->getObject();
            /** @var JsonSerializationVisitor $visitor */
            $visitor = $event->getVisitor();
            $organism = $user->getMainOrganism();

            $hasPartnershipGroup = in_array('partnership', $serializationGroups);
            $certificationInfos = [];
            if (!$hasPartnershipGroup) {
                $certificationInfos['partnerCount'] = $this->certificationPartnerService->getPartnersCount($certification);
            }
            if ($this->accessService->hasCertificationCdcView($user, $certification)) {
                $visitor->visitProperty(new StaticPropertyMetadata('', 'isCdcExportable', null), $certification->isCdcExportable());
                if (!$organism->getSubscription()->isAllowCertifierPlus()) {
                    $estimation = $certification->getEstimatedRegistrationFoldersCount();
                    if ($estimation < 100) {
                        $estimation = ceil($estimation / 10) * 10;
                    } else if ($estimation < 1000) {
                        $estimation = ceil($estimation / 100) * 100;
                    } else if ($estimation < 10000) {
                        $estimation = ceil($estimation / 1000) * 1000;
                    } else if ($estimation < 100000) {
                        $estimation = ceil($estimation / 10000) * 10000;
                    } else {
                        $estimation = ceil($estimation / 100000) * 100000;
                    }
                    $visitor->visitProperty(new StaticPropertyMetadata('', 'estimatedRegistrationFoldersCount', null), $estimation > 0 ? "- de $estimation" : "0");
                }
                $certificationInfos['folderCount'] = $this->certificationFolderService->countByCertification($certification);
            } else {
                $visitor->visitProperty(new StaticPropertyMetadata('', 'estimatedRegistrationFoldersCount', null), null);
            }
            $visitor->visitProperty(new StaticPropertyMetadata('', 'state', null), $certification->getEnabled() ? 'active' : 'inactive');
            $visitor->visitProperty(new StaticPropertyMetadata('', 'certificationInfo', null), $certificationInfos);
            $this->serializeFiles($certification, $organism, $visitor);
        }
    }

    //-------------------
    // METHODES PRIVEES
    //-------------------

    /**
     * @param Certification $certification
     * @param Organism $organism
     * @param JsonSerializationVisitor $visitor
     * @return void
     */
    private function serializeFiles(Certification $certification, Organism $organism, JsonSerializationVisitor $visitor): void
    {
        $serializedFiles = [];

        if ($certification->isCertifier($organism)) {
            $serializedFiles = $certification->getFiles();
        } else {
            $certificationPartner = $this->certificationPartnerService->getByCertificationAndPartner($certification, $organism, false);
            if ($certificationPartner && in_array($certificationPartner->getState(), [CertificationPartnerStates::ACTIVE(), CertificationPartnerStates::SUSPENDED(), CertificationPartnerStates::REVOKED()])) {
                $serializedFiles = $certification->getFiles();
            } else {
                $fileTypes = $organism->getCertificationFileTypes();
                foreach ($certification->getFiles() as $file) {
                    $fileTypeIndex = array_search($file->getTypeId(), array_column($fileTypes, 'id'));
                    if ($fileTypeIndex !== false) {
                        $allowVisibilityPublic = $fileTypes[$fileTypeIndex]['allowVisibilityPublic'] ?? false;
                        if ($allowVisibilityPublic) {
                            $serializedFiles[] = $file;
                        }
                    }
                }
            }
        }

        $visitor->visitProperty(new StaticPropertyMetadata('', 'files', null), $serializedFiles);
    }
}