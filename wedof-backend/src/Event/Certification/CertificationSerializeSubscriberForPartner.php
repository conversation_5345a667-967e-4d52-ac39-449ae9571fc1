<?php

namespace App\Event\Certification;

use App\Entity\Certification;
use <PERSON><PERSON>\Serializer\EventDispatcher\EventSubscriberInterface;
use <PERSON><PERSON>\Serializer\EventDispatcher\ObjectEvent;
use <PERSON><PERSON>\Serializer\JsonSerializationVisitor;
use <PERSON><PERSON>\Serializer\Metadata\StaticPropertyMetadata;

class CertificationSerializeSubscriberForPartner implements EventSubscriberInterface
{
    public static function getSubscribedEvents(): array
    {
        return array(
            array(
                'event' => 'serializer.post_serialize',
                'method' => 'onPostSerialize',
                'class' => Certification::class, // if no class, subscribe to every serialization,
                'format' => 'json'
            ),
        );
    }

    public function onPostSerialize(ObjectEvent $event)
    {
        /** @var Certification $certification */
        $certification = $event->getObject();
        /** @var JsonSerializationVisitor $visitor */
        $visitor = $event->getVisitor();
        if (in_array('partnerFiles', $event->getContext()->getAttribute('groups'))) {
            $certificationFolderFileTypes = $certification->getCertificationFolderFileTypes();
            $filteredCertificationFolderFileTypes = [];
            if ($certificationFolderFileTypes) {
                foreach ($certificationFolderFileTypes as $certificationFolderFileType) {
                    if ($certificationFolderFileType['allowVisibilityPartner'] === true) {
                        $filteredCertificationFolderFileTypes[] = $certificationFolderFileType;
                    }
                }
            }
            $certificationPartnerFileTypes = $certification->getCertificationPartnerFileTypes();
            $filteredCertificationPartnerFileTypes = [];
            if ($certificationPartnerFileTypes) {
                foreach ($certificationPartnerFileTypes as $certificationPartnerFileType) {
                    if ($certificationPartnerFileType['allowVisibilityPartner'] === true) {
                        $filteredCertificationPartnerFileTypes[] = $certificationPartnerFileType;
                    }
                }
            }
            $visitor->visitProperty(new StaticPropertyMetadata('', 'certificationFolderFileTypes', null), $filteredCertificationFolderFileTypes);
            $visitor->visitProperty(new StaticPropertyMetadata('', 'certificationPartnerFileTypes', null), $filteredCertificationPartnerFileTypes);
        }
        $visitor->visitProperty(new StaticPropertyMetadata('', 'state', null), $certification->getEnabled() ? 'active' : 'inactive');
    }
}