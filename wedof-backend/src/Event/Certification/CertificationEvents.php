<?php

namespace App\Event\Certification;

use App\Entity\Certification;
use App\Entity\Organism;
use Symfony\Contracts\EventDispatcher\Event;

class CertificationEvents extends Event
{
    public const CREATED = 'certification.created';
    public const UPDATED = 'certification.updated';
    public const AUDITS_ENABLED = 'certification.auditsEnabled';
    public const AUDITS_DISABLED = 'certification.auditsDisabled';
    public const AUDITS_ANNUAL_SUBSCRIPTION = 'certification.auditsAnnualSubscription';

    protected Certification $certification;
    protected ?Organism $organism;

    public function __construct(Certification $certification, Organism $organism = null)
    {
        $this->certification = $certification;
        $this->organism = $organism;
    }

    public function getCertification(): Certification
    {
        return $this->certification;
    }

    /**
     * @return mixed
     */
    public function getOrganism(): Organism
    {
        return $this->organism;
    }
}
