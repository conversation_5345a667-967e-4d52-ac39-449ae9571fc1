<?php

namespace App\Event\Certification;

use App\Entity\Webhook;
use App\Message\SendWebhook;
use App\Service\WebhookService;
use <PERSON><PERSON>\Serializer\SerializationContext;
use <PERSON><PERSON>\Serializer\SerializerInterface;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Messenger\MessageBusInterface;

class CertificationWebhookSubscriber implements EventSubscriberInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    private WebhookService $webhookService;
    private MessageBusInterface $messageBus;
    private SerializerInterface $serializer;

    public function __construct(WebhookService $webhookService, MessageBusInterface $messageBus, SerializerInterface $serializer)
    {
        $this->webhookService = $webhookService;
        $this->messageBus = $messageBus;
        $this->serializer = $serializer;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    public static function getSubscribedEvents(): array
    {
        return [
            CertificationEvents::CREATED => 'webhookOnCertification',
            CertificationEvents::UPDATED => 'webhookOnCertification'
        ];
    }

    public function webhookOnCertification(CertificationEvents $event, string $eventName)
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        $this->logger->info(self::class . " event $eventName");
        $webhooks = $this->webhookService->listByOrganismsAndEventAndType($event->getCertification()->getCertifiers(), $eventName);
        if (sizeof($webhooks) > 0) {
            $serializationContext = new SerializationContext();
            $serializationContext->setSerializeNull(true);
            $serializationContext->setGroups("owner");
            $payload = $this->serializer->serialize($event->getCertification(), 'json', $serializationContext);
            /** @var Webhook $webhook */
            foreach ($webhooks as $webhook) {
                $message = new SendWebhook($webhook->getId(), $eventName, $payload, get_class($event->getCertification()), $event->getCertification()->getId());
                $this->messageBus->dispatch($message);
            }
        }
    }

    public function webhookOnCertificationOrganisms(CertificationEvents $event, string $eventName)
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        $this->logger->info(self::class . " event $eventName");
        $webhooks = $this->webhookService->listByOrganismsAndEventAndType($event->getCertification()->getCertifiers(), $eventName);
        if (sizeof($webhooks) > 0) {
            $serializationContext = new SerializationContext();
            $serializationContext->setSerializeNull(true);
            $serializationContext->setGroups("owner");
            $payload = $this->serializer->serialize(["certification" => $event->getCertification(), "organism" => $event->getOrganism()], 'json', $serializationContext);
            /** @var Webhook $webhook */
            foreach ($webhooks as $webhook) {
                $message = new SendWebhook($webhook->getId(), $eventName, $payload, get_class($event->getCertification()), $event->getCertification()->getId());
                $this->messageBus->dispatch($message);
            }
        }
    }
}
