<?php

namespace App\Event\Certification;

use App\Entity\Certification;
use <PERSON><PERSON>\Serializer\EventDispatcher\EventSubscriberInterface;
use <PERSON><PERSON>\Serializer\EventDispatcher\ObjectEvent;
use <PERSON><PERSON>\Serializer\JsonSerializationVisitor;
use <PERSON><PERSON>\Serializer\Metadata\StaticPropertyMetadata;

class CertificationSerializeSubscriberFor<PERSON>ttendee implements EventSubscriberInterface
{

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return array(
            array(
                'event' => 'serializer.post_serialize',
                'method' => 'onPostSerialize',
                'class' => Certification::class, // if no class, subscribe to every serialization,
                'format' => 'json'
            ),
        );
    }

    public function onPostSerialize(ObjectEvent $event): void
    {
        if (in_array('attendee', $event->getContext()->getAttribute('groups'))) {
            /** @var Certification $certification */
            $certification = $event->getObject();
            /** @var JsonSerializationVisitor $visitor */
            $visitor = $event->getVisitor();

            $fileTypes = $certification->getCertificationFolderFileTypes();
            $filteredCertificationFolderFileTypes = [];
            if ($fileTypes) {
                foreach ($fileTypes as $fileType) {
                    if (isset($fileType['allowVisibilityAttendee']) && $fileType['allowVisibilityAttendee'] === true) {
                        $filteredCertificationFolderFileTypes[] = $fileType;
                    }
                }
            }

            $visitor->visitProperty(new StaticPropertyMetadata('', 'certificationFolderFileTypes', null), $filteredCertificationFolderFileTypes);
        }
    }
}