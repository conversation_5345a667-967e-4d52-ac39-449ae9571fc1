<?php
// src/Event/TrainingAction/TrainingActionSerializeSubscriber.php
namespace App\Event\TrainingAction;

use App\Entity\TrainingAction;
use App\Service\TrainingService;
use <PERSON><PERSON>\Serializer\EventDispatcher\EventSubscriberInterface;
use <PERSON><PERSON>\Serializer\EventDispatcher\ObjectEvent;
use <PERSON><PERSON>\Serializer\JsonSerializationVisitor;
use <PERSON><PERSON>\Serializer\Metadata\StaticPropertyMetadata;

class TrainingActionSerializeSubscriber implements EventSubscriberInterface
{
    private TrainingService $trainingService;

    public function __construct(TrainingService $trainingService)
    {
        $this->trainingService = $trainingService;
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return array(
            array(
                'event' => 'serializer.post_serialize',
                'method' => 'onPostSerialize',
                'class' => TrainingAction::class, // if no class, subscribe to every serialization,
                'format' => 'json'
            ),
        );
    }

    /**
     * @param ObjectEvent $event
     */
    public function onPostSerialize(ObjectEvent $event): void
    {
        /** @var TrainingAction $trainingAction */
        $trainingAction = $event->getObject();
        /** @var JsonSerializationVisitor $visitor */
        $visitor = $event->getVisitor();

        $trainingTitle = $this->trainingService->getTitleFromTrainingAction($trainingAction);

        $visitor->visitProperty(new StaticPropertyMetadata('', 'trainingTitle', null), $trainingTitle);
        if (isset($trainingAction->getRawData()['totalTvaTTC'])) {
            $visitor->visitProperty(new StaticPropertyMetadata('', 'totalTvaTTC', null), $trainingAction->getRawData()['totalTvaTTC']);
        }
        $visitor->visitProperty(new StaticPropertyMetadata('', 'indicativeDuration', null), $trainingAction->getRawData()['duration'] ?? $trainingAction->getRawData()['averageLearningTime'] ?? null);
    }
}
