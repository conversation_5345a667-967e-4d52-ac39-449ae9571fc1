<?php


namespace App\Event\CertificationPartnerAuditTemplate;


use App\Entity\CertificationPartnerAuditTemplate;
use App\Library\utils\enums\CertificationPartnerAuditStates;
use App\Service\CertificationPartnerAuditService;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use J<PERSON>\Serializer\EventDispatcher\EventSubscriberInterface;
use <PERSON><PERSON>\Serializer\EventDispatcher\ObjectEvent;
use J<PERSON>\Serializer\JsonSerializationVisitor;
use J<PERSON>\Serializer\Metadata\StaticPropertyMetadata;

class CertificationPartnerAuditTemplateSerializeSubscriber implements EventSubscriberInterface
{
    private CertificationPartnerAuditService $certificationPartnerAuditService;

    public function __construct(CertificationPartnerAuditService $certificationPartnerAuditService)
    {
        $this->certificationPartnerAuditService = $certificationPartnerAuditService;
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return array(
            array(
                'event' => 'serializer.post_serialize',
                'method' => 'onPostSerialize',
                'class' => CertificationPartnerAuditTemplate::class, // if no class, subscribe to every serialization,
                'format' => 'json'
            ),
        );
    }

    /**
     * @param ObjectEvent $event
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function onPostSerialize(ObjectEvent $event): void
    {
        /** @var CertificationPartnerAuditTemplate $certificationPartnerAuditTemplate */
        $certificationPartnerAuditTemplate = $event->getObject();
        /** @var JsonSerializationVisitor $visitor */
        $visitor = $event->getVisitor();
        $auditCount = [
            'pendingComputationOrComputing' => $this->certificationPartnerAuditService->countByAuditTemplate($certificationPartnerAuditTemplate,
                [ CertificationPartnerAuditStates::PENDING_COMPUTATION()->getValue(), CertificationPartnerAuditStates::COMPUTING()->getValue()]),
            'all' => $this->certificationPartnerAuditService->countByAuditTemplate($certificationPartnerAuditTemplate)
        ];
        $visitor->visitProperty(new StaticPropertyMetadata('', 'auditCount', null), $auditCount);
        $canCreateAudit = $this->certificationPartnerAuditService->listReturnQueryBuilder(
                [
                    'templateId' => $certificationPartnerAuditTemplate->getId(),
                    'state' => [CertificationPartnerAuditStates::COMPUTING()->getValue(), CertificationPartnerAuditStates::PENDING_COMPUTATION()->getValue()]
                ]
            )->getQuery()->getResult() === [];
        $visitor->visitProperty(new StaticPropertyMetadata('', 'allowCreateAudit', null), $canCreateAudit);
    }
}
