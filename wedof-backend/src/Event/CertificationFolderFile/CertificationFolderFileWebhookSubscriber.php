<?php

namespace App\Event\CertificationFolderFile;

use App\Entity\Webhook;
use App\Library\utils\enums\FileStates;
use App\Message\SendWebhook;
use App\Service\WebhookService;
use Doctrine\Common\Collections\ArrayCollection;
use Exception;
use <PERSON><PERSON>\Serializer\SerializationContext;
use <PERSON><PERSON>\Serializer\SerializerInterface;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Messenger\MessageBusInterface;

class CertificationFolderFileWebhookSubscriber implements EventSubscriberInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    private WebhookService $webhookService;
    private MessageBusInterface $messageBus;
    private SerializerInterface $serializer;

    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    public function __construct(WebhookService $webhookService, MessageBusInterface $messageBus, SerializerInterface $serializer)
    {
        $this->webhookService = $webhookService;
        $this->messageBus = $messageBus;
        $this->serializer = $serializer;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    public static function getSubscribedEvents(): array
    {
        $event = array(
            CertificationFolderFileEvents::FILE_ADDED => 'webhookOnCertificationFolderFile',
            CertificationFolderFileEvents::FILE_UPDATED => 'webhookOnCertificationFolderFile',
            CertificationFolderFileEvents::FILE_DELETED => 'webhookOnCertificationFolderFile',
        );
        foreach (FileStates::valuesStates() as $state) {
            $event['certificationFolderFile.' . $state->getValue()] = 'webhookOnCertificationFolderFile';
        }
        return $event;
    }

    /**
     * @param CertificationFolderFileEvents $event
     * @param string $eventName
     * @throws Exception
     */
    public function webhookOnCertificationFolderFile(CertificationFolderFileEvents $event, string $eventName): void
    {

        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        $certificationFolderFile = $event->getCertificationFolderFile();
        $certificationFolder = $certificationFolderFile->getCertificationFolder();
        try {
            $this->logger->debug("[Webhook CertificationFolderFile] " . self::class . " event $eventName");
            $organisms = [$certificationFolder->getCertifier()];
            if ($certificationFolder->getPartner()) {
                $organisms[] = $certificationFolder->getPartner();
            }
            $webhooks = $this->webhookService->listByOrganismsAndEventAndType(new ArrayCollection($organisms), $eventName, []);
            if (sizeof($webhooks) > 0) {
                $serializationContext = new SerializationContext();
                $serializationContext->setSerializeNull(true);
                $serializationContext->setGroups("owner");
                $this->logger->debug("[Webhook CertificationFolderFile] " . sizeof($webhooks) . " to send");
                $payload = $this->serializer->serialize($certificationFolderFile, 'json', $serializationContext);
                /** @var Webhook $webhook */
                foreach ($webhooks as $webhook) {
                    $message = new SendWebhook($webhook->getId(), $eventName, $payload, get_class($certificationFolderFile), $certificationFolderFile->getId());
                    $this->messageBus->dispatch($message);
                }
            } else {
                $this->logger->debug("[Webhook CertificationFolderFile] no event to send");
            }
        } catch (Exception $e) {
            $this->logger->error("[Error webhookOnCertificationFolderFile " . $certificationFolderFile->getId() . "] at Line " . $e->getLine() . " " . $e->getFile() . " " . $e->getMessage());
        }
    }

    //-------------------
    // METHODES PRIVEES
    //-------------------
}
