<?php


namespace App\Event\CertificationFolderFile;

use App\Entity\CertificationFolderFile;
use Symfony\Contracts\EventDispatcher\Event;

class CertificationFolderFileEvents extends Event
{
    public const FILE_ADDED = "certificationFolderFile.added";
    public const FILE_UPDATED = "certificationFolderFile.updated";
    public const FILE_DELETED = "certificationFolderFile.deleted";

    protected CertificationFolderFile $certificationFolderFile;

    public function __construct(CertificationFolderFile $certificationFolderFile)
    {
        $this->certificationFolderFile = $certificationFolderFile;
    }

    /**
     * @return CertificationFolderFile
     */
    public function getCertificationFolderFile(): CertificationFolderFile
    {
        return $this->certificationFolderFile;
    }

}