<?php

namespace App\Event\Organism;

use App\Entity\Organism;
use <PERSON><PERSON>\Serializer\EventDispatcher\EventSubscriberInterface;
use <PERSON><PERSON>\Serializer\EventDispatcher\ObjectEvent;
use <PERSON><PERSON>\Serializer\JsonSerializationVisitor;
use <PERSON><PERSON>\Serializer\Metadata\StaticPropertyMetadata;

class OrganismSerializeSubscriberForAttendee implements EventSubscriberInterface
{

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return array(
            array(
                'event' => 'serializer.post_serialize',
                'method' => 'onPostSerialize',
                'class' => Organism::class, // if no class, subscribe to every serialization,
                'format' => 'json'
            ),
        );
    }

    //-------------------
    // METHODES PUBLIQUES
    //-------------------
    /**
     * @param ObjectEvent $event
     */
    public function onPostSerialize(ObjectEvent $event): void
    {
        if (in_array('attendee', $event->getContext()->getAttribute('groups'))) {
            /** @var Organism $organism */
            $organism = $event->getObject();
            /** @var JsonSerializationVisitor $visitor */
            $visitor = $event->getVisitor();

            $this->serializeFileTypesForAttendee($organism, $visitor);
        }
    }

    //-------------------
    // METHODES PRIVEES
    //-------------------
    /**
     * @param Organism $organism
     * @param JsonSerializationVisitor $visitor
     */
    private function serializeFileTypesForAttendee(Organism $organism, JsonSerializationVisitor $visitor): void
    {
        $fileTypes = $organism->getRegistrationFolderFileTypes();
        $attendeeFileTypes = [];
        if ($fileTypes) {
            foreach ($fileTypes as $fileType) {
                if (isset($fileType['allowVisibilityAttendee']) && $fileType['allowVisibilityAttendee'] === true) {
                    $attendeeFileTypes[] = $fileType;
                }
            }
        }

        $visitor->visitProperty(new StaticPropertyMetadata('', 'registrationFolderFileTypes', null), $attendeeFileTypes);
    }
}