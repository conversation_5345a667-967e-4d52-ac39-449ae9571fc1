<?php

namespace App\Event\Organism;

use App\Entity\Organism;
use Symfony\Contracts\EventDispatcher\Event;

class OrganismEvents extends Event
{
    public const CREATED = 'organism.created';
    public const CPF_CATALOG_UPLOAD_FINISHED = 'organism.cpfCatalogUploadFinished';
    public const CPF_CATALOG_EXPORT_FINISHED = 'organism.cpfCatalogExportFinished';

    protected Organism $organism;

    public function __construct(Organism $organism)
    {
        $this->organism = $organism;
    }

    /**
     * @return Organism
     */
    public function getOrganism(): Organism
    {
        return $this->organism;
    }
}