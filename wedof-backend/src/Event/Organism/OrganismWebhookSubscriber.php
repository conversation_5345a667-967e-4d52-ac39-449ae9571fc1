<?php

namespace App\Event\Organism;

use App\Message\SendWebhook;
use App\Service\OrganismService;
use App\Service\WebhookService;
use Doctrine\Common\Collections\ArrayCollection;
use <PERSON><PERSON>\Serializer\SerializationContext;
use <PERSON><PERSON>\Serializer\SerializerInterface;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Messenger\MessageBusInterface;

class OrganismWebhookSubscriber implements EventSubscriberInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    private SerializerInterface $serializer;
    private MessageBusInterface $messageBus;
    private OrganismService $organismService;
    private WebhookService $webhookService;

    //-------------------
    // METHODES PUBLIQUES
    //-------------------
    public function __construct(SerializerInterface $serializer, MessageBusInterface $messageBus, OrganismService $organismService, WebhookService $webhookService)
    {
        $this->serializer = $serializer;
        $this->messageBus = $messageBus;
        $this->organismService = $organismService;
        $this->webhookService = $webhookService;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    public static function getSubscribedEvents(): array
    {
        return [
            OrganismEvents::CREATED => 'webhookOnOrganismWedof', // Admin wedof
            OrganismEvents::CPF_CATALOG_UPLOAD_FINISHED => 'webhookOnOrganism',
            OrganismEvents::CPF_CATALOG_EXPORT_FINISHED => 'webhookOnOrganism',
        ];
    }

    public function webhookOnOrganism(OrganismEvents $event, string $eventName): void
    {
        $this->logger->debug("[Webhook Organism] " . self::class . " event $eventName");
        $organism = $event->getOrganism();
        $webhooks = $this->webhookService->listByOrganismsAndEventAndType(new ArrayCollection([$organism]), $eventName);
        if (sizeof($webhooks) > 0) {
            $this->logger->debug("[Webhook organism] " . sizeof($webhooks) . " to send for event " . $eventName);
            $serializationContext = new SerializationContext();
            $serializationContext->setSerializeNull(true);
            $data = [];
            if ($eventName === OrganismEvents::CPF_CATALOG_UPLOAD_FINISHED) {
                $data = $this->organismService->getCpfCatalogUploadStatus($organism);
            } else if ($eventName === OrganismEvents::CPF_CATALOG_EXPORT_FINISHED) {
                $cpfCatalogMetadata = $organism->getCpfCatalogMetadata();
                $data = $cpfCatalogMetadata['export'] ?? [];;
                // TODO add XML / report etc, no idea how
            }
            $payload = $this->serializer->serialize($data, 'json', $serializationContext);
            foreach ($webhooks as $webhook) {
                $message = new SendWebhook($webhook->getId(), $eventName, $payload, get_class($event->getOrganism()), $event->getOrganism()->getId());
                $this->messageBus->dispatch($message);
                $this->logger->debug("[Webhook Organism] event $eventName sent !");
            }
        } else {
            $this->logger->debug("[Webhook Organism] no webhook registered to event " . $eventName . " to call");
        }
    }

    /**
     * @param OrganismEvents $event
     * @param string $eventName
     */
    public function webhookOnOrganismWedof(OrganismEvents $event, string $eventName): void
    {
        $this->logger->debug("[Webhook Organism] " . self::class . " event $eventName");
        // Pick Wedof if exists, otherwise pick Kag (helpful for dev envs)
        $organism = $this->organismService->getBySiret('90301927100019') ?? $this->organismService->getBySiret('53222292400039');
        $webhooks = $this->webhookService->listByOrganismsAndEventAndType($organism ? new ArrayCollection([$organism]) : new ArrayCollection(), $eventName);
        if (sizeof($webhooks) > 0) {
            $this->logger->debug("[Webhook organism] " . sizeof($webhooks) . " to send for event " . $eventName);
            $serializationContext = new SerializationContext();
            $serializationContext->setSerializeNull(true);
            $serializationContext->setGroups('webhook');
            $payload = $this->serializer->serialize($event->getOrganism(), 'json', $serializationContext);
            foreach ($webhooks as $webhook) {
                $message = new SendWebhook($webhook->getId(), $eventName, $payload, get_class($event->getOrganism()), $event->getOrganism()->getId());
                $this->messageBus->dispatch($message);
                $this->logger->debug("[Webhook Organism] event $eventName sent !");
            }
        } else {
            $this->logger->debug("[Webhook Organism] no webhook registered to event " . $eventName . " to call");
        }
    }
}
