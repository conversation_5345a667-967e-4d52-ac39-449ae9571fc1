<?php


namespace App\Event\Organism;


use App\Entity\Organism;
use App\Entity\User;
use App\Library\utils\enums\SubscriptionTrainingTypes;
use App\Service\CertifierAccessService;
use <PERSON><PERSON>\Serializer\EventDispatcher\EventSubscriberInterface;
use <PERSON><PERSON>\Serializer\EventDispatcher\ObjectEvent;
use <PERSON><PERSON>\Serializer\JsonSerializationVisitor;
use <PERSON>MS\Serializer\Metadata\StaticPropertyMetadata;
use Symfony\Component\Security\Core\Security;
use Vich\UploaderBundle\Templating\Helper\UploaderHelper;

class OrganismSerializeSubscriber implements EventSubscriberInterface
{
    private Security $security;
    private CertifierAccessService $certifierAccessService;
    private UploaderHelper $helper;

    public function __construct(Security $security, CertifierAccessService $certifierAccessService, UploaderHelper $helper)
    {
        $this->security = $security;
        $this->certifierAccessService = $certifierAccessService;
        $this->helper = $helper;
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return array(
            array(
                'event' => 'serializer.post_serialize',
                'method' => 'onPostSerialize',
                'class' => Organism::class, // if no class, subscribe to every serialization,
                'format' => 'json'
            ),
        );
    }

    /**
     * @param ObjectEvent $event
     */
    public function onPostSerialize(ObjectEvent $event): void
    {
        $user = $this->security->getUser();
        /** @var Organism $organism */
        $organism = $event->getObject();
        /** @var JsonSerializationVisitor $visitor */
        $visitor = $event->getVisitor();
        if ($this->security->isGranted('IS_IMPERSONATOR') || $this->security->isGranted('ROLE_ALLOWED_TO_SWITCH')) {
            // For debugging and support purposes
            $visitor->visitProperty(new StaticPropertyMetadata('', 'id', null), $organism->getId());
        }
        //TODO: Remove when OAuth is ready
        if ($user instanceof User) {
            $currentOrganism = $user->getMainOrganism();
            if ($currentOrganism
                && $currentOrganism->isReseller()
                && $organism->getReseller() === $currentOrganism
                && $organism->getSubscription()
                && $organism->getSubscription()->getTrainingType() !== SubscriptionTrainingTypes::NONE()->getValue()) {
                $targetOrganismUser = $organism->getOwnedBy();
                if ($targetOrganismUser) {
                    $apiTokens = $targetOrganismUser->getApiTokens();
                    $apiToken = count($apiTokens) > 0 ? $apiTokens[0]->getToken() : null;
                    $visitor->visitProperty(new StaticPropertyMetadata('', 'apiToken', null), $apiToken);
                }
            }
        }
        $visitor->visitProperty(new StaticPropertyMetadata('', 'hasCertifierAccess', null), $this->certifierAccessService->listForPartner($organism, 'inactive')->count() > 0);
    }
}
