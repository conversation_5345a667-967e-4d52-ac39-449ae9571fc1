<?php

namespace App\Event;

use Exception;
use Symfony\Contracts\EventDispatcher\Event;

class MonitoringEvents extends Event
{
    public const API_PROVIDER_ERROR = 'ErrorCpfApi';
    public const API_PROVIDER_AUTH_DOWN = 'AuthDown';
    public const API_PROVIDER_AUTH_UP = 'AuthUp';
    public const API_PROVIDER_RFOLDER_ERROR = 'FolderError';
    public const CRON_ERROR = 'ErrorCron';
    public const SEND_MESSAGE = 'sendSlackMessage';

    protected ?Exception $exception;
    protected ?string $message;

    public function __construct(Exception $exception = null, string $message = null)
    {
        $this->exception = $exception;
        $this->message = $message;
    }

    /**
     * @return Exception|null
     */
    public function getException(): ?Exception
    {
        return $this->exception;
    }

    /**
     * @return string|null
     */
    public function getMessage(): ?string
    {
        return $this->message;
    }

    public static function getEvents(): array
    {
        return array(
            self::SEND_MESSAGE,
            self::CRON_ERROR,
            self::API_PROVIDER_ERROR,
            self::API_PROVIDER_AUTH_DOWN,
            self::API_PROVIDER_AUTH_UP,
            self::API_PROVIDER_RFOLDER_ERROR
        );
    }
}