<?php

namespace App\Event;

use App\Exception\WedofConnectionException;
use App\Library\utils\enums\ConnectionStates;
use App\Library\utils\enums\ConnectionTypes;
use App\Library\utils\enums\DataProviders;
use App\Service\ConnectionService;
use App\Service\DataProviders\CdcCertifiersApiService;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use ErrorException;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\File\File;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Throwable;

class CdcDelegationDocumentSubscriber implements EventSubscriberInterface, LoggerAwareInterface
{
    private const DOCUMENT_ID = "cdcCertifierDelegationDocument";
    const DOCUMENT_NAME = 'Délégation accrochage certificateur';
    private LoggerInterface $logger;
    private CdcCertifiersApiService $cdcCertifiersApiService;
    private ConnectionService $connectionService;

    public function __construct(
        ConnectionService       $connectionService,
        CdcCertifiersApiService $cdcCertifiersApiService
    )
    {
        $this->connectionService = $connectionService;
        $this->cdcCertifiersApiService = $cdcCertifiersApiService;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    public static function getSubscribedEvents(): array
    {
        return array(
            SignDocumentEvents::SHOW_DOCUMENT => 'showDocument',
            SignDocumentEvents::SIGN_DOCUMENT => 'signDocument',
            SignDocumentEvents::PREVIEW_DOCUMENT => 'previewDocument',
            SignDocumentEvents::BEFORE_GENERATE_CODE => 'allowGenerateCode',
            SignDocumentEvents::BEFORE_VALIDATE_CODE => 'allowValidateCode',
        );
    }

    /**
     * @param SignDocumentEvents $event
     * @return void
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function showDocument(SignDocumentEvents $event): void
    {
        if ($event->getDocumentId() === self::DOCUMENT_ID) {
            $this->logger->info("[cdcDelegationDocument][" . $event->getDocumentId() . "] showDocument");
            $result = $this->cdcCertifiersApiService->showDelegationDocument($event->getOrganism());
            $event->setDocument($result['content']);
            $event->stopPropagation();
        }
    }

    /**
     * @param SignDocumentEvents $event
     * @return void
     * @throws WedofConnectionException
     * @throws ORMException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws OptimisticLockException
     * @throws ErrorException
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     * @throws Throwable
     */
    public function previewDocument(SignDocumentEvents $event): void
    {
        if ($event->getDocumentId() === self::DOCUMENT_ID) {
            $this->logger->info("[cdcDelegationDocument][" . $event->getDocumentId() . "] previewDocument");
            $result = $this->cdcCertifiersApiService->generateDelegationDocument($event->getOrganism(), $event->getUser(), $event->getParams());
            $tempFile = tempnam(sys_get_temp_dir(), "delegation-cdc");
            file_put_contents($tempFile, $result['content']);
            $event->setDocument(new File($tempFile));
            $event->stopPropagation();
        }
    }

    /**
     * @param SignDocumentEvents $event
     * @return void
     * @throws WedofConnectionException
     * @throws ORMException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws OptimisticLockException
     * @throws ErrorException
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     * @throws Throwable
     */
    public function signDocument(SignDocumentEvents $event): void
    {
        if ($event->getDocumentId() === self::DOCUMENT_ID) {
            $this->logger->info("[cdcDelegationDocument][" . $event->getDocumentId() . "] previewDocument");
            $result = $this->cdcCertifiersApiService->generateDelegationDocument($event->getOrganism(), $event->getUser(), $event->getParams(), false);
            $tempFile = tempnam(sys_get_temp_dir(), "delegation-cdc");
            file_put_contents($tempFile, $result['content']);
            $event->setDocument(new File($tempFile));
            $event->stopPropagation();
        }
    }

    /**
     * @param SignDocumentEvents $event
     * @return void
     */
    public function allowGenerateCode(SignDocumentEvents $event): void
    {
        if ($event->getDocumentId() === self::DOCUMENT_ID) {
            $this->logger->info("[cdcDelegationDocument][" . $event->getDocumentId() . "] allow generate code");
            $event->setAllowedGenerateCode(true);
            $event->setDocumentName(self::DOCUMENT_NAME);
            $event->stopPropagation();
            //update connection
            $connection = $event->getOrganism()->getConnectionForDataProvider(DataProviders::CDC_CERTIFIERS()) ?? $this->connectionService->create($event->getOrganism(), DataProviders::CDC_CERTIFIERS());
            $connection->setType(ConnectionTypes::HABILITATION());
            $connection->setState(ConnectionStates::IN_PROGRESS());
            $connection->setMessageType("info");
            $connection->setMessage("Signature de la délégation: en attente");
            $connection->setCredentials([
                "params" => $event->getParams()
            ]);
            $this->connectionService->save($connection);
        }
    }

    /**
     * @param SignDocumentEvents $event
     * @return void
     */
    public function allowValidateCode(SignDocumentEvents $event): void
    {
        if ($event->getDocumentId() === self::DOCUMENT_ID) {
            $this->logger->info("[cdcDelegationDocument][" . $event->getDocumentId() . "] allow validate code");
            $event->setAllowedValidateCode(true);
            $event->setDocumentName(self::DOCUMENT_NAME);
            $event->stopPropagation();
        }
    }
}