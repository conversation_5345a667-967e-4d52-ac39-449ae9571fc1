<?php
// src/Event/CertificationFolder/CertificationFolderSerializeSubscriberForDefault.php
namespace App\Event\CertificationFolder;

use App\Entity\CertificationFolder;
use App\Entity\User;
use App\Event\TagSerializeSubscriber;
use <PERSON><PERSON>\Serializer\EventDispatcher\EventSubscriberInterface;
use <PERSON><PERSON>\Serializer\EventDispatcher\ObjectEvent;
use <PERSON><PERSON>\Serializer\JsonSerializationVisitor;
use <PERSON>MS\Serializer\Metadata\StaticPropertyMetadata;
use Symfony\Component\Security\Core\Security;

class CertificationFolderSerializeSubscriberFor<PERSON><PERSON>ault extends TagSerializeSubscriber implements EventSubscriberInterface
{
    private Security $security;

    public function __construct(Security $security)
    {
        $this->security = $security;
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return array(
            array(
                'event' => 'serializer.post_serialize',
                'method' => 'onPostSerialize',
                'class' => CertificationFolder::class, // if no class, subscribe to every serialization,
                'format' => 'json'
            ),
        );
    }

    //-------------------
    // METHODES PUBLIQUES
    //-------------------


    /**
     * @param ObjectEvent $event
     */
    public function onPostSerialize(ObjectEvent $event): void
    {
        if (in_array('Default', $event->getContext()->getAttribute('groups'), false)) {
            /** @var CertificationFolder $certificationFolder */
            $certificationFolder = $event->getObject();
            /** @var JsonSerializationVisitor $visitor */
            $visitor = $event->getVisitor();
            /** @var User $user */
            $user = $this->security->getUser();
            $visitor->visitProperty(new StaticPropertyMetadata('', 'attendee', null), $certificationFolder->getAttendee());
            if (($user && $user->getMainOrganism() === $certificationFolder->getCertifier()) || in_array('Owner', $event->getContext()->getAttribute('groups'), false)) {
                $visitor->visitProperty(new StaticPropertyMetadata('', 'metadata', null), $certificationFolder->getMetadata());
                $this->serializeTags($certificationFolder, $visitor);
            }
        }
    }
}
