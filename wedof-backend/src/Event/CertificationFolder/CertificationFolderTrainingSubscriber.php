<?php

namespace App\Event\CertificationFolder;

use App\Message\PropagateSkillSetsFromTraining;
use App\Service\TrainingService;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Throwable;

class CertificationFolderTrainingSubscriber implements EventSubscriberInterface
{
    private TrainingService $trainingService;
    private MessageBusInterface $messageBus;


    public function __construct(TrainingService $trainingService, MessageBusInterface $messageBus)
    {
        $this->trainingService = $trainingService;
        $this->messageBus = $messageBus;
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return [CertificationFolderEvents::UPDATED => 'propagateSkillSets'];
    }

    /**
     * @param CertificationFolderEvents $event
     * @return void
     * @throws Throwable
     */
    public function propagateSkillSets(CertificationFolderEvents $event): void
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        $certificationFolder = $event->getCertificationFolder();
        if ($certificationFolder->getCertification()->isAllowPartialSkillSets() && $certificationFolder->getSkillSets()->count() > 0) {
            $registrationFolder = $certificationFolder->getRegistrationFolder();
            $training = $registrationFolder ? $registrationFolder->getSession()->getTrainingAction()->getTraining() : null;
            if ($training && $training->getSkillSets()->count() === 0) {
                $training = $this->trainingService->update($training, ['skillSets' => $certificationFolder->getSkillSets()]);
                $this->messageBus->dispatch(new PropagateSkillSetsFromTraining($training->getId()));
            }
        }
    }
}