<?php

namespace App\Event\CertificationFolder;

use App\Entity\Webhook;
use App\Library\utils\enums\CertificationFolderStates;
use App\Message\SendWebhook;
use App\Service\WebhookService;
use Doctrine\Common\Collections\ArrayCollection;
use <PERSON><PERSON>\Serializer\SerializationContext;
use <PERSON><PERSON>\Serializer\SerializerInterface;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Messenger\MessageBusInterface;

class CertificationFolderWebhookSubscriber implements EventSubscriberInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    private WebhookService $webhookService;
    private MessageBusInterface $messageBus;
    private SerializerInterface $serializer;

    public function __construct(WebhookService $webhookService, MessageBusInterface $messageBus, SerializerInterface $serializer)
    {
        $this->webhookService = $webhookService;
        $this->messageBus = $messageBus;
        $this->serializer = $serializer;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    //-------------------
    // METHODES PUBLIQUES
    //-------------------
    public static function getSubscribedEvents(): array
    {
        $events = array(
            CertificationFolderEvents::CREATED => 'webhookOnCertificationFolder',
            CertificationFolderEvents::UPDATED => 'webhookOnCertificationFolder',
            CertificationFolderEvents::IN_TRAINING_STARTED => 'webhookOnCertificationFolder',
            CertificationFolderEvents::IN_TRAINING_ENDED => 'webhookOnCertificationFolder',
            CertificationFolderEvents::ACCROCHAGE_OK => 'webhookOnCertificationFolder',
            CertificationFolderEvents::ACCROCHAGE_KO => 'webhookOnCertificationFolder',
            CertificationFolderEvents::MISSING_DATA => 'webhookOnCertificationFolder'
        );
        foreach (CertificationFolderStates::valuesStates() as $state) {
            $events["certificationFolder." . $state->getValue()] = 'webhookOnCertificationFolder';
        }
        return $events;
    }

    /**
     * @param CertificationFolderEvents $event
     * @param string $eventName
     */
    public function webhookOnCertificationFolder(CertificationFolderEvents $event, string $eventName): void
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        $this->logger->debug("[Webhook CertificationFolder] " . self::class . " event $eventName");
        $certificationFolder = $event->getCertificationFolder();
        $organisms = [$certificationFolder->getCertifier()];
        if ($certificationFolder->getPartner()) {
            array_push($organisms, $certificationFolder->getPartner());
        }
        $webhooks = $this->webhookService->listByOrganismsAndEventAndType(new ArrayCollection($organisms), $eventName);
        if (sizeof($webhooks) > 0) {
            $this->logger->debug("[Webhook CertificationFolder] " . sizeof($webhooks) . " to send");
            $serializationContext = new SerializationContext();
            $serializationContext->setSerializeNull(true);
            $serializationContext->setGroups("Default");
            $payloadCertifier = null;
            $payload = $this->serializer->serialize($certificationFolder, 'json', $serializationContext);
            /** @var Webhook $webhook */
            foreach ($webhooks as $webhook) {
                if ($webhook->getOrganism()->getId() === $certificationFolder->getCertifier()->getId()) {
                    if (!$payloadCertifier) {
                        $serializationContextOwner = new SerializationContext();
                        $serializationContextOwner->setSerializeNull(true);
                        $serializationContextOwner->setGroups(["Default", "Owner"]); //in order to serialize tags & metadata in all cases
                        $payloadCertifier = $this->serializer->serialize($certificationFolder, 'json', $serializationContextOwner);
                    }
                    $message = new SendWebhook($webhook->getId(), $eventName, $payloadCertifier, get_class($certificationFolder), $certificationFolder->getId());
                } else {
                    $message = new SendWebhook($webhook->getId(), $eventName, $payload, get_class($certificationFolder), $certificationFolder->getId());
                }
                $this->messageBus->dispatch($message);
            }
        } else {
            $this->logger->debug("[Webhook CertificationFolder] no event to send");
        }
    }

    //-----------------
    // METHODES PRIVEES
    //-----------------
}
