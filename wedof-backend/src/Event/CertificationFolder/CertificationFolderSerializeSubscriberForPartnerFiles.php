<?php
// src/Event/CertificationFolder/CertificationFolderSerializeSubscriberForAttendee.php
namespace App\Event\CertificationFolder;

use App\Entity\CertificationFolder;
use <PERSON><PERSON>\Serializer\EventDispatcher\EventSubscriberInterface;
use <PERSON><PERSON>\Serializer\EventDispatcher\ObjectEvent;
use <PERSON><PERSON>\Serializer\JsonSerializationVisitor;
use <PERSON><PERSON>\Serializer\Metadata\StaticPropertyMetadata;

class CertificationFolderSerializeSubscriberForPartnerFiles implements EventSubscriberInterface
{
    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return array(
            array(
                'event' => 'serializer.post_serialize',
                'method' => 'onPostSerialize',
                'class' => CertificationFolder::class, // if no class, subscribe to every serialization,
                'format' => 'json'
            ),
        );
    }

    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    /**
     * @param ObjectEvent $event
     */
    public function onPostSerialize(ObjectEvent $event): void
    {
        if (in_array('partnerFiles', $event->getContext()->getAttribute('groups'))) {
            /** @var CertificationFolder $certificationFolder */
            $certificationFolder = $event->getObject();
            /** @var JsonSerializationVisitor $visitor */
            $visitor = $event->getVisitor();

            $this->serializeFiles($certificationFolder, $visitor);
        }
    }

    //-------------------
    // METHODES PRIVEES
    //-------------------

    /**
     * @param CertificationFolder $certificationFolder
     * @param JsonSerializationVisitor $visitor
     */
    private function serializeFiles(CertificationFolder $certificationFolder, JsonSerializationVisitor $visitor): void
    {
        $newFiles = [];
        $certificationFolderFiles = $certificationFolder->getFiles();
        $certification = $certificationFolder->getCertification();
        $fileTypes = $certification->getCertificationFolderFileTypes();
        foreach ($certificationFolderFiles as $certificationFolderFile) {
            $fileTypeIndex = array_search($certificationFolderFile->getTypeId(), array_column($fileTypes, 'id'));
            if ($fileTypeIndex !== false) {
                $fileType = $fileTypes[$fileTypeIndex];
                $allowVisibilityPartner = $fileType['allowVisibilityPartner'] ?? null;
                $allowUploadPartner = $fileType['allowUploadPartner'] ?? null;
                $allowSignPartner = $fileType['allowSignPartner'] ?? null;
                if ($allowVisibilityPartner) {
                    $file = [
                        'fileName' => $certificationFolderFile->getFileName(),
                        'fileType' => $certificationFolderFile->getFileType(),
                        'generationState' => $certificationFolderFile->getGenerationState(),
                        'id' => $certificationFolderFile->getId(),
                        'link' => $certificationFolderFile->getLink(),
                        'permalink' => $certificationFolderFile->getPermalink(),
                        'typeId' => $certificationFolderFile->getTypeId(),
                        'state' => $certificationFolderFile->getState()
                    ];
                    if ($allowUploadPartner) {
                        $file['comment'] = $certificationFolderFile->getComment();
                    }
                    if ($allowSignPartner) {
                        $file['signedState'] = $certificationFolderFile->getSignedState();
                    }
                    $newFiles[] = $file;
                }
            }
        }
        $visitor->visitProperty(new StaticPropertyMetadata('', 'files', null), $newFiles);

    }
}
