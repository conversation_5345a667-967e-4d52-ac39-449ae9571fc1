<?php

namespace App\Event\CertificationFolder;

use App\Service\SubscriptionService;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;


class CertificationFolderSubscriptionSubscriber implements EventSubscriberInterface
{
    private SubscriptionService $subscriptionService;

    public function __construct(SubscriptionService $subscriptionService)
    {
        $this->subscriptionService = $subscriptionService;
    }

    public static function getSubscribedEvents(): array
    {
        return [
            CertificationFolderEvents::CREATED => 'refreshCertificationFoldersNumberCount',
            CertificationFolderEvents::DELETED => 'refreshCertificationFoldersNumberCount'
        ];
    }

    /**
     * @param CertificationFolderEvents $certificationFolderEvents
     * @throws NoResultException
     * @throws NonUniqueResultException|\DateMalformedStringException
     */
    public function refreshCertificationFoldersNumberCount(CertificationFolderEvents $certificationFolderEvents): void
    {
        $this->subscriptionService->updateCertificationFoldersNumberCount($certificationFolderEvents->getCertificationFolder()->getCertifier()->getSubscription());
    }
}