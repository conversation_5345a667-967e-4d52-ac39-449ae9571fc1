<?php

namespace App\Event\CertificationFolder;

use App\Entity\CertificationFolder;
use Symfony\Contracts\EventDispatcher\Event;

class CertificationFolderEvents extends Event
{
    public const CREATED = 'certificationFolder.created';
    public const UPDATED = 'certificationFolder.updated';
    public const DELETED = 'certificationFolder.deleted';
    public const IN_TRAINING_STARTED = 'certificationFolder.inTrainingStarted';
    public const IN_TRAINING_ENDED = 'certificationFolder.inTrainingEnded';
    public const ACCROCHAGE_OK = 'certificationFolder.accrochageOk';
    public const ACCROCHAGE_KO = 'certificationFolder.accrochageKo';
    public const MISSING_DATA = 'certificationFolder.missingData'; // données manquantes pour l'accrochage

    protected CertificationFolder $certificationFolder;

    public function __construct(CertificationFolder $certificationFolder)
    {
        $this->certificationFolder = $certificationFolder;
    }

    /**
     * @return CertificationFolder
     */
    public function getCertificationFolder(): CertificationFolder
    {
        return $this->certificationFolder;
    }
}
