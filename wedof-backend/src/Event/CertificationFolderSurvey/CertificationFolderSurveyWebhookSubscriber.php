<?php

namespace App\Event\CertificationFolderSurvey;

use App\Entity\Webhook;
use App\Message\SendWebhook;
use App\Service\WebhookService;
use <PERSON><PERSON>\Serializer\SerializationContext;
use <PERSON><PERSON>\Serializer\SerializerInterface;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Messenger\MessageBusInterface;

class CertificationFolderSurveyWebhookSubscriber implements EventSubscriberInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    private WebhookService $webhookService;
    private MessageBusInterface $messageBus;
    private SerializerInterface $serializer;

    public function __construct(WebhookService $webhookService, MessageBusInterface $messageBus, SerializerInterface $serializer)
    {
        $this->webhookService = $webhookService;
        $this->messageBus = $messageBus;
        $this->serializer = $serializer;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    public static function getSubscribedEvents(): array
    {
        return [
            CertificationFolderSurveyEvents::CREATED => 'webhookOnCertificationFolderSurvey',
            CertificationFolderSurveyEvents::INITIAL_EXPERIENCE_ANSWERED => 'webhookOnCertificationFolderSurvey',
            CertificationFolderSurveyEvents::SIX_MONTH_EXPERIENCE_ANSWERED => 'webhookOnCertificationFolderSurvey',
            CertificationFolderSurveyEvents::LONG_TERM_EXPERIENCE_ANSWERED => 'webhookOnCertificationFolderSurvey',
            CertificationFolderSurveyEvents::SIX_MONTH_EXPERIENCE_AVAILABLE => 'webhookOnCertificationFolderSurvey',
            CertificationFolderSurveyEvents::LONG_TERM_EXPERIENCE_AVAILABLE => 'webhookOnCertificationFolderSurvey'
        ];
    }

    public function webhookOnCertificationFolderSurvey(CertificationFolderSurveyEvents $event, string $eventName)
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        $this->logger->info(self::class . " event $eventName");
        $certificationFolderSurvey = $event->getCertificationFolderSurvey();
        $certificationFolder = $certificationFolderSurvey->getCertificationFolder();
        $webhooks = $this->webhookService->listByOrganismsAndEventAndType($certificationFolder->getCertification()->getCertifiers(), $eventName);
        if (sizeof($webhooks) > 0) {
            $serializationContext = new SerializationContext();
            $serializationContext->setSerializeNull(true);
            $serializationContext->setGroups("Default");
            $payload = $this->serializer->serialize($event->getCertificationFolderSurvey(), 'json', $serializationContext);
            /** @var Webhook $webhook */
            foreach ($webhooks as $webhook) {
                $message = new SendWebhook($webhook->getId(), $eventName, $payload, get_class($event->getCertificationFolderSurvey()), $event->getCertificationFolderSurvey()->getId());
                $this->messageBus->dispatch($message);
            }
        }
    }
}
