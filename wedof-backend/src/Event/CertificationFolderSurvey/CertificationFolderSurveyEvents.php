<?php


namespace App\Event\CertificationFolderSurvey;

use App\Entity\CertificationFolderSurvey;
use Symfony\Contracts\EventDispatcher\Event;

class CertificationFolderSurveyEvents extends Event
{
    public const CREATED = "certificationFolderSurvey.created";
    public const INITIAL_EXPERIENCE_ANSWERED = "certificationFolderSurvey.initialExperienceAnswered";
    public const SIX_MONTH_EXPERIENCE_ANSWERED = "certificationFolderSurvey.sixMonthExperienceAnswered";
    public const LONG_TERM_EXPERIENCE_ANSWERED = "certificationFolderSurvey.longTermExperienceAnswered";
    public const SIX_MONTH_EXPERIENCE_AVAILABLE = "certificationFolderSurvey.sixMonthExperienceAvailable";
    public const LONG_TERM_EXPERIENCE_AVAILABLE = "certificationFolderSurvey.longTermExperienceAvailable";

    protected CertificationFolderSurvey $certificationFolderSurvey;

    public function __construct(CertificationFolderSurvey $certificationFolderSurvey)
    {
        $this->certificationFolderSurvey = $certificationFolderSurvey;
    }

    /**
     * @return CertificationFolderSurvey
     */
    public function getCertificationFolderSurvey(): CertificationFolderSurvey
    {
        return $this->certificationFolderSurvey;
    }

}