<?php


namespace App\Event\RegistrationFolderFile;


use App\Entity\RegistrationFolderFile;
use Symfony\Contracts\EventDispatcher\Event;

class RegistrationFolderFileEvents extends Event
{
    protected RegistrationFolderFile $registrationFolderFile;

    public const FILE_ADDED = "registrationFolderFile.added";
    public const FILE_UPDATED = "registrationFolderFile.updated";
    public const FILE_DELETED = "registrationFolderFile.deleted";
    public const FILE_SIGNATURE_PARTIALLY = "registrationFolderFile.signaturePartially";
    public const FILE_SIGNATURE_COMPLETED = "registrationFolderFile.signatureCompleted";
    public const FILE_SIGNATURE_DECLINED = "registrationFolderFile.signatureDeclined";

    //etc...

    public function __construct(RegistrationFolderFile $registrationFolderFile)
    {
        $this->registrationFolderFile = $registrationFolderFile;
    }

    /**
     * @return RegistrationFolderFile
     */
    public function getRegistrationFolderFile(): RegistrationFolderFile
    {
        return $this->registrationFolderFile;
    }
}