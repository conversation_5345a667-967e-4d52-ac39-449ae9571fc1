<?php

namespace App\Event\RegistrationFolderFile;

use App\Library\utils\enums\FileStates;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

class RegistrationFolderFileInterceptSubscriber implements EventSubscriberInterface
{

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents()
    {
        $events = array(
            RegistrationFolderFileEvents::FILE_ADDED => ['allowEventPropagation', 100],
            RegistrationFolderFileEvents::FILE_UPDATED => ['allowEventPropagation', 100],
            RegistrationFolderFileEvents::FILE_DELETED => ['allowEventPropagation', 100]
        );
        foreach (FileStates::valuesStates() as $state) {
            $events['registrationFolderFile.' . $state->getValue()] = ['allowEventPropagation', 100];
        }
        return $events;
    }

    /**
     * @param RegistrationFolderFileEvents $event
     * @param string $eventName
     * @return void
     */
    public function allowEventPropagation(RegistrationFolderFileEvents $event, string $eventName): void
    {
        $registrationFolder = $event->getRegistrationFolderFile()->getRegistrationFolder();

        if (!$registrationFolder->isAllowActions()) {
            $event->stopPropagation();
        }
    }
}