<?php

namespace App\Event\RegistrationFolderFile;

use App\Entity\Webhook;
use App\Library\utils\enums\FileStates;
use App\Message\SendWebhook;
use App\Service\WebhookService;
use Doctrine\Common\Collections\ArrayCollection;
use Exception;
use <PERSON><PERSON>\Serializer\SerializationContext;
use <PERSON><PERSON>\Serializer\SerializerInterface;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Messenger\MessageBusInterface;

class RegistrationFolderFileWebhookSubscriber implements EventSubscriberInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    private WebhookService $webhookService;
    private MessageBusInterface $messageBus;
    private SerializerInterface $serializer;

    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    public function __construct(WebhookService $webhookService, MessageBusInterface $messageBus, SerializerInterface $serializer)
    {
        $this->webhookService = $webhookService;
        $this->messageBus = $messageBus;
        $this->serializer = $serializer;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    public static function getSubscribedEvents(): array
    {
        $events = [
            RegistrationFolderFileEvents::FILE_ADDED => 'webhookOnRegistrationFolderFile',
            RegistrationFolderFileEvents::FILE_UPDATED => 'webhookOnRegistrationFolderFile',
            RegistrationFolderFileEvents::FILE_DELETED => 'webhookOnRegistrationFolderFile',
        ];
        foreach (FileStates::valuesStates() as $state) {
            $events['registrationFolderFile.' . $state->getValue()] = "webhookOnRegistrationFolderFile";
        }
        return $events;
    }

    /**
     * @param RegistrationFolderFileEvents $event
     * @param string $eventName
     * @throws Exception
     */
    public function webhookOnRegistrationFolderFile(RegistrationFolderFileEvents $event, string $eventName): void
    {

        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        $registrationFolderFile = $event->getRegistrationFolderFile();
        try {
            $this->logger->debug("[Webhook RegistrationFolderFile] " . self::class . " event $eventName");
            $organism = $event->getRegistrationFolderFile()->getRegistrationFolder()->getOrganism();
            $webhooks = $this->webhookService->listByOrganismsAndEventAndType(new ArrayCollection([$organism]), $eventName, []);
            if (sizeof($webhooks) > 0) {
                $serializationContext = new SerializationContext();
                $serializationContext->setSerializeNull(true);
                $serializationContext->setGroups("owner");
                $this->logger->debug("[Webhook RegistrationFolderFile] " . sizeof($webhooks) . " to send");
                $payload = $this->serializer->serialize($registrationFolderFile, 'json', $serializationContext);
                /** @var Webhook $webhook */
                foreach ($webhooks as $webhook) {
                    $message = new SendWebhook($webhook->getId(), $eventName, $payload, get_class($registrationFolderFile), $registrationFolderFile->getId());
                    $this->messageBus->dispatch($message);
                }
            } else {
                $this->logger->debug("[Webhook RegistrationFolderFile] no event to send");
            }
        } catch (Exception $e) {
            $this->logger->error("[Error webhookOnRegistrationFolderFile " . $registrationFolderFile->getId() . "] at Line " . $e->getLine() . " " . $e->getFile() . " " . $e->getMessage());
        }
    }

    //-------------------
    // METHODES PRIVEES
    //-------------------
}
