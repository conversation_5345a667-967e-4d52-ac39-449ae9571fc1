<?php


namespace App\Event\Invoice;

use App\Entity\CertificationPartner;
use App\Entity\Invoice;
use Symfony\Contracts\EventDispatcher\Event;

class InvoiceEvents extends Event
{
    public const CERTIFICATION_PARTNER_CREATED = "certificationPartnerInvoice.created";
    public const CERTIFICATION_PARTNER_UPDATED = "certificationPartnerInvoice.updated";
    public const CERTIFICATION_PARTNER_DELETED = "certificationPartnerInvoice.deleted";
    public const CERTIFICATION_PARTNER_PAID = "certificationPartnerInvoice.paid";

    protected Invoice $invoice;
    protected CertificationPartner $certificationPartner;

    public function __construct(Invoice $invoice, CertificationPartner $certificationPartner)
    {
        $this->invoice = $invoice;
        $this->certificationPartner = $certificationPartner;
    }

    /**
     * @return Invoice
     */
    public function getInvoice(): Invoice
    {
        return $this->invoice;
    }

    /**
     * @return CertificationPartner
     */
    public function getCertificationPartner(): CertificationPartner
    {
        return $this->certificationPartner;
    }
}