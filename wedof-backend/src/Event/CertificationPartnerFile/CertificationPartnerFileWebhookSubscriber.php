<?php

namespace App\Event\CertificationPartnerFile;

use App\Entity\Webhook;
use App\Library\utils\enums\FileStates;
use App\Message\SendWebhook;
use App\Service\WebhookService;
use Doctrine\Common\Collections\ArrayCollection;
use Exception;
use <PERSON><PERSON>\Serializer\SerializationContext;
use <PERSON><PERSON>\Serializer\SerializerInterface;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Messenger\MessageBusInterface;

class CertificationPartnerFileWebhookSubscriber implements EventSubscriberInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    private WebhookService $webhookService;
    private MessageBusInterface $messageBus;
    private SerializerInterface $serializer;

    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    public function __construct(WebhookService $webhookService, MessageBusInterface $messageBus, SerializerInterface $serializer)
    {
        $this->webhookService = $webhookService;
        $this->messageBus = $messageBus;
        $this->serializer = $serializer;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    public static function getSubscribedEvents(): array
    {
        $events = [
            CertificationPartnerFileEvents::FILE_ADDED => 'webhookOnCertificationPartnerFile',
            CertificationPartnerFileEvents::FILE_DELETED => 'webhookOnCertificationPartnerFile',
            CertificationPartnerFileEvents::FILE_UPDATED => 'webhookOnCertificationPartnerFile',
        ];
        foreach (FileStates::valuesStates() as $state) {
            $events['certificationPartnerFile.' . $state->getValue()] = 'webhookOnCertificationPartnerFile';
        }
        return $events;
    }

    /**
     * @param CertificationPartnerFileEvents $event
     * @param string $eventName
     * @throws Exception
     */
    public function webhookOnCertificationPartnerFile(CertificationPartnerFileEvents $event, string $eventName): void
    {

        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        $certificationPartnerFile = $event->getCertificationPartnerFile();
        $certificationPartner = $certificationPartnerFile->getCertificationPartner();
        try {
            $this->logger->debug("[Webhook CertificationPartnerFile] " . self::class . " event $eventName");
            $organisms = $certificationPartner->getCertifier() ? [$certificationPartner->getCertifier()] : $certificationPartner->getCertification()->getCertifiers();
            if ($organisms) {
                $organisms[] = $certificationPartner->getPartner();
            } else {
                $organisms = [$certificationPartner->getPartner()];
            }
            $webhooks = $this->webhookService->listByOrganismsAndEventAndType(new ArrayCollection($organisms), $eventName, []);
            if (sizeof($webhooks) > 0) {
                $serializationContext = new SerializationContext();
                $serializationContext->setSerializeNull(true);
                $serializationContext->setGroups("owner");
                $this->logger->debug("[Webhook CertificationPartnerFile] " . sizeof($webhooks) . " to send");
                $payload = $this->serializer->serialize($certificationPartnerFile, 'json', $serializationContext);
                /** @var Webhook $webhook */
                foreach ($webhooks as $webhook) {
                    $message = new SendWebhook($webhook->getId(), $eventName, $payload, get_class($certificationPartnerFile), $certificationPartnerFile->getId());
                    $this->messageBus->dispatch($message);
                }
            } else {
                $this->logger->debug("[Webhook CertificationPartnerFile] no event to send");
            }
        } catch (Exception $e) {
            $this->logger->error("[Error webhookOnCertificationPartnerFile " . $certificationPartnerFile->getId() . "] at Line " . $e->getLine() . " " . $e->getFile() . " " . $e->getMessage());
        }
    }

    //-------------------
    // METHODES PRIVEES
    //-------------------
}
