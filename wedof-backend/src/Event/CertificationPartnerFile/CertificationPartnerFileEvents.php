<?php


namespace App\Event\CertificationPartnerFile;

use App\Entity\CertificationPartnerFile;
use Symfony\Contracts\EventDispatcher\Event;

class CertificationPartnerFileEvents extends Event
{
    public const FILE_ADDED = "certificationPartnerFile.added";
    public const FILE_UPDATED = "certificationPartnerFile.updated";
    public const FILE_DELETED = "certificationPartnerFile.deleted";

    protected CertificationPartnerFile $certificationPartnerFile;

    public function __construct(CertificationPartnerFile $certificationPartnerFile)
    {
        $this->certificationPartnerFile = $certificationPartnerFile;
    }

    /**
     * @return CertificationPartnerFile
     */
    public function getCertificationPartnerFile(): CertificationPartnerFile
    {
        return $this->certificationPartnerFile;
    }
}