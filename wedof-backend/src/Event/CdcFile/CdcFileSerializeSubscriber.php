<?php
// src/Event/CdcFile/CdcFileSerializeSubscriber.php
namespace App\Event\CdcFile;

use App\Entity\CdcFile;
use App\Service\CdcFileService;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use <PERSON><PERSON>\Serializer\EventDispatcher\EventSubscriberInterface;
use J<PERSON>\Serializer\EventDispatcher\ObjectEvent;
use J<PERSON>\Serializer\JsonSerializationVisitor;
use <PERSON><PERSON>\Serializer\Metadata\StaticPropertyMetadata;

class CdcFileSerializeSubscriber implements EventSubscriberInterface
{
    private CdcFileService $cdcFileService;

    public function __construct(CdcFileService $cdcFileService)
    {
        $this->cdcFileService = $cdcFileService;
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return array(
            array(
                'event' => 'serializer.post_serialize',
                'method' => 'onPostSerialize',
                'class' => CdcFile::class, // if no class, subscribe to every serialization,
                'format' => 'json'
            ),
        );
    }

    /**
     * @param ObjectEvent $event
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function onPostSerialize(ObjectEvent $event): void
    {
        /** @var CdcFile $cdcFile */
        $cdcFile = $event->getObject();
        /** @var JsonSerializationVisitor $visitor */
        $visitor = $event->getVisitor();
        $hasCertificationFolderError = $this->cdcFileService->hasCertificationFolderError($cdcFile);
        $visitor->visitProperty(new StaticPropertyMetadata('', 'hasCertificationFolderError', null), $hasCertificationFolderError);
    }
}
