<?php

namespace App\Event;

use App\Library\utils\Tools;
use Beelab\TagBundle\Entity\AbstractTaggable;
use <PERSON>MS\Serializer\JsonSerializationVisitor;
use <PERSON>MS\Serializer\Metadata\StaticPropertyMetadata;

class TagSerializeSubscriber
{
    protected function serializeTags(AbstractTaggable $object, JsonSerializationVisitor $visitor)
    {
        $visitor->visitProperty(new StaticPropertyMetadata('', 'tags', null), Tools::tagsToArray($object));
    }
}
