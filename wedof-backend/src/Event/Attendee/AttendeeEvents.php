<?php


namespace App\Event\Attendee;

use App\Entity\Attendee;
use Symfony\Contracts\EventDispatcher\Event;

class AttendeeEvents extends Event
{

    public const CREATED = 'attendee.created';
    public const UPDATED = 'attendee.updated';

    protected Attendee $attendee;

    public function __construct(Attendee $attendee)
    {
        $this->attendee = $attendee;
    }

    /**
     * @return Attendee
     */
    public function getAttendee(): Attendee
    {
        return $this->attendee;
    }
}
