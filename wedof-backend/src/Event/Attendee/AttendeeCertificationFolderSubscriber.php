<?php


namespace App\Event\Attendee;


use App\Library\utils\enums\CertificationFolderCdcStates;
use App\Repository\CertificationFolderRepository;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Throwable;

class AttendeeCertificationFolderSubscriber implements EventSubscriberInterface
{
    private CertificationFolderRepository $certificationFolderRepository;

    public function __construct(CertificationFolderRepository $certificationFolderRepository)
    {
        $this->certificationFolderRepository = $certificationFolderRepository;
    }

    /**
     * @return array
     */
    public static function getSubscribedEvents(): array
    {
        return [
            AttendeeEvents::UPDATED => 'refreshCertificationFolders'
        ];
    }

    /**
     * @param AttendeeEvents $event
     * @throws Throwable
     */
    public function refreshCertificationFolders(AttendeeEvents $event): void
    {
        $attendee = $event->getAttendee();
        foreach ($attendee->getCertificationFolders() as $certificationFolder) {
            if ($certificationFolder->getCdcState() === CertificationFolderCdcStates::PROCESSED_KO()->getValue() && !$certificationFolder->isCdcToExport() && $certificationFolder->isFullCertification()) {
                $certificationFolder->setCdcToExport(true); // Give the certification folder another chance at being "accroched"
            }
            $this->certificationFolderRepository->save($certificationFolder); // Refresh CDC Compliant
        }
    }
}
