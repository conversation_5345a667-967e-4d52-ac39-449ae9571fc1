<?php

namespace App\Event\Evaluation;

use App\Entity\Webhook;
use App\Message\SendWebhook;
use App\Service\WebhookService;
use Doctrine\Common\Collections\ArrayCollection;
use <PERSON><PERSON>\Serializer\SerializationContext;
use <PERSON><PERSON>\Serializer\SerializerInterface;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Messenger\MessageBusInterface;

class EvaluationWebhookSubscriber implements EventSubscriberInterface, LoggerAwareInterface
{

    private LoggerInterface $logger;
    private WebhookService $webhookService;
    private MessageBusInterface $messageBus;
    private SerializerInterface $serializer;

    public function __construct(WebhookService $webhookService, MessageBusInterface $messageBus, SerializerInterface $serializer)
    {
        $this->webhookService = $webhookService;
        $this->messageBus = $messageBus;
        $this->serializer = $serializer;
    }

    public static function getSubscribedEvents(): array
    {
        return [
            EvaluationEvents::ORGANISM_NEW => 'webhookOnEvaluation',
            EvaluationEvents::ORGANISM_CHANGED => 'webhookOnEvaluation',
            EvaluationEvents::TRAININGACTION_NEW => 'webhookOnEvaluation',
            EvaluationEvents::TRAININGACTION_CHANGED => 'webhookOnEvaluation'
        ];
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    public function webhookOnEvaluation(EvaluationEvents $event, string $eventName)
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        $this->logger->info(self::class . " event $eventName");
        $webhooks = $this->webhookService->listByOrganismsAndEventAndType(new ArrayCollection([$event->getEvaluation()->getOrganism()]), $eventName);
        if (sizeof($webhooks) > 0) {
            $serializationContext = new SerializationContext();
            $serializationContext->setSerializeNull(true);
            $serializationContext->setGroups("owner");
            $payload = $this->serializer->serialize($event->getEvaluation(), 'json', $serializationContext);
            /** @var Webhook $webhook */
            foreach ($webhooks as $webhook) {
                $message = new SendWebhook($webhook->getId(), $eventName, $payload, get_class($event->getEvaluation()), $event->getEvaluation()->getId());
                $this->messageBus->dispatch($message);
            }
        }
    }
}
