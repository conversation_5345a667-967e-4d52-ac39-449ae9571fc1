<?php

namespace App\Event\Evaluation;

use App\Entity\Evaluation;
use Symfony\Contracts\EventDispatcher\Event;

class EvaluationEvents extends Event
{
    public const TRAININGACTION_NEW = 'evaluation.trainingActionNew';
    public const TRAININGACTION_CHANGED = 'evaluation.trainingActionChanged';
    public const TRAINING_NEW = 'evaluation.trainingNew';
    public const TRAINING_CHANGED = 'evaluation.trainingChanged';
    public const ORGANISM_NEW = 'evaluation.organismNew';
    public const ORGANISM_CHANGED = 'evaluation.organismChanged';

    protected Evaluation $evaluation;
    protected ?Evaluation $previousEvaluation;

    public function __construct(Evaluation $evaluation, Evaluation $previousEvaluation = null)
    {
        $this->evaluation = $evaluation;
        $this->previousEvaluation = $previousEvaluation;
    }

    /**
     * @return Evaluation
     */
    public function getEvaluation(): Evaluation
    {
        return $this->evaluation;
    }

    /**
     * @return Evaluation|null
     */
    public function getPreviousEvaluation(): ?Evaluation
    {
        return $this->previousEvaluation;
    }
}
