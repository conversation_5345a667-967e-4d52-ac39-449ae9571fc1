<?php

namespace App\Event\User;

use App\Entity\User;
use Symfony\Contracts\EventDispatcher\Event;

class UserEvents extends Event
{
    public const CREATED = 'user.created';
    public const SIGN_UP_COMPLETE = 'user.signUpComplete';
    public const INVITED = 'user.invited';

    protected User $user;

    public function __construct(User $user)
    {
        $this->user = $user;
    }

    /**
     * @return User
     */
    public function getUser(): User
    {
        return $this->user;
    }
}