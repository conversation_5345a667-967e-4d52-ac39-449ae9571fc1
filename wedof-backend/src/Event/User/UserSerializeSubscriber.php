<?php

namespace App\Event\User;


use App\Entity\User;
use <PERSON><PERSON>\Serializer\EventDispatcher\EventSubscriberInterface;
use <PERSON><PERSON>\Serializer\EventDispatcher\ObjectEvent;
use <PERSON><PERSON>\Serializer\JsonSerializationVisitor;
use <PERSON><PERSON>\Serializer\Metadata\StaticPropertyMetadata;
use <PERSON>ymfony\Component\Security\Core\Security;

class UserSerializeSubscriber implements EventSubscriberInterface
{
    private Security $security;

    public function __construct(Security $security)
    {
        $this->security = $security;
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return array(
            array(
                'event' => 'serializer.post_serialize',
                'method' => 'onPostSerialize',
                'class' => User::class, // if no class, subscribe to every serialization,
                'format' => 'json'
            ),
        );
    }

    /**
     * @param ObjectEvent $event
     */
    public function onPostSerialize(ObjectEvent $event): void
    {
        /** @var User $user */
        $user = $this->security->getUser();
        $subject = $event->getObject();

        if (!empty($user)) {
            /** @var JsonSerializationVisitor $visitor */
            $visitor = $event->getVisitor();
            if ($user->getId() == $subject->getId()) {
                $canImpersonate = $this->security->isGranted('ROLE_ALLOWED_TO_SWITCH');
                if ($canImpersonate) {
                    $visitor->visitProperty(new StaticPropertyMetadata('', 'can_impersonate', null), true);
                }
                $isImpersonator = $this->security->isGranted('IS_IMPERSONATOR');
                if ($isImpersonator) {
                    $visitor->visitProperty(new StaticPropertyMetadata('', 'is_impersonator', null), true);
                }
                if (!empty(array_intersect(['Default', 'update'], $event->getContext()->getAttribute('groups')))) {
                    $organism = $subject->getMainOrganism();
                    if ($organism) {
                        $siret = $organism->getSiret();
                        $filters = $subject->getFilters() && array_key_exists($siret, $subject->getFilters()) ? $subject->getFilters()[$siret] : null;
                        $visitor->visitProperty(new StaticPropertyMetadata('', 'filters', null), $filters);
                    }
                }
            }
        }
    }
}
