<?php

namespace App\Event\User;

use App\Message\SendWebhook;
use App\Service\OrganismService;
use App\Service\WebhookService;
use Doctrine\Common\Collections\ArrayCollection;
use <PERSON><PERSON>\Serializer\SerializationContext;
use <PERSON><PERSON>\Serializer\SerializerInterface;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Messenger\MessageBusInterface;

class UserWebhookSubscriber implements EventSubscriberInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    private SerializerInterface $serializer;
    private MessageBusInterface $messageBus;
    private OrganismService $organismService;
    private WebhookService $webhookService;

    //-------------------
    // METHODES PUBLIQUES
    //-------------------
    public function __construct(SerializerInterface $serializer, MessageBusInterface $messageBus, OrganismService $organismService, WebhookService $webhookService)
    {
        $this->serializer = $serializer;
        $this->messageBus = $messageBus;
        $this->organismService = $organismService;
        $this->webhookService = $webhookService;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    public static function getSubscribedEvents(): array
    {
        return [
            UserEvents::CREATED => 'webhookOnUser',
            UserEvents::SIGN_UP_COMPLETE => 'webhookOnUser',
            UserEvents::INVITED => 'webhookOnUser'
        ];
    }

    /**
     * @param UserEvents $event
     * @param string $eventName
     */
    public function webhookOnUser(UserEvents $event, string $eventName): void
    {
        $this->logger->debug("[Webhook User] " . self::class . " event $eventName");
        // Set a default organism as at this point the user is not linked to an organism yet
        // Pick Wedof if exists, otherwise pick Kag (helpful for dev envs)
        $organism = $this->organismService->getBySiret('90301927100019') ?? $this->organismService->getBySiret('53222292400039');
        $webhooks = $this->webhookService->listByOrganismsAndEventAndType($organism ? new ArrayCollection([$organism]) : new ArrayCollection(), $eventName, []);
        if (sizeof($webhooks) > 0) {
            $this->logger->debug("[Webhook User] " . sizeof($webhooks) . " to send");
            $serializationContext = new SerializationContext();
            $serializationContext->setSerializeNull(true);
            $serializationContext->setGroups('webhook');
            $payload = $this->serializer->serialize($event->getUser(), 'json', $serializationContext);
            foreach ($webhooks as $webhook) {
                $message = new SendWebhook($webhook->getId(), $eventName, $payload, get_class($event->getUser()), $event->getUser()->getId());
                $this->messageBus->dispatch($message);
                $this->logger->debug("[Webhook User] event $eventName sent !");
            }
        } else {
            $this->logger->debug("[Webhook User] no event to send");
        }
    }

    //-------------------
    // METHODES PRIVEES
    //-------------------
}
