<?php

namespace App\Event\Proposal;

use App\Entity\Proposal;
use Symfony\Contracts\EventDispatcher\Event;

class ProposalEvents extends Event
{
    public const CREATED = 'proposal.created';
    public const UPDATED = 'proposal.updated';
    public const DRAFT = 'proposal.draft';
    public const ACTIVE = 'proposal.active';
    public const VIEWED = 'proposal.viewed';
    public const ACCEPTED = 'proposal.accepted';
    public const REFUSED = 'proposal.refused';
    public const DELETED = 'proposal.deleted';

    //other events are mapped with Proposal.* -> ProposalStates

    protected Proposal $proposal;

    public function __construct(Proposal $proposal)
    {
        $this->proposal = $proposal;
    }

    /**
     * @return Proposal
     */
    public function getProposal(): Proposal
    {
        return $this->proposal;
    }
}
