<?php

namespace App\Event\Proposal;

use App\Entity\Webhook;
use App\Library\utils\enums\ProposalStates;
use App\Message\SendWebhook;
use App\Service\WebhookService;
use Doctrine\Common\Collections\ArrayCollection;
use Exception;
use <PERSON><PERSON>\Serializer\SerializationContext;
use <PERSON><PERSON>\Serializer\SerializerInterface;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Messenger\MessageBusInterface;

class ProposalWebhookSubscriber implements EventSubscriberInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    private WebhookService $webhookService;
    private MessageBusInterface $messageBus;
    private SerializerInterface $serializer;

    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    public function __construct(WebhookService $webhookService, MessageBusInterface $messageBus, SerializerInterface $serializer)
    {
        $this->webhookService = $webhookService;
        $this->messageBus = $messageBus;
        $this->serializer = $serializer;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    public static function getSubscribedEvents(): array
    {
        $events = array(
            ProposalEvents::CREATED => 'webhookOnProposal',
            ProposalEvents::UPDATED => 'webhookOnProposal',
            ProposalEvents::DELETED => 'webhookOnProposal',
        );
        foreach (ProposalStates::valuesStates() as $state) {
            if ($state->getValue() !== ProposalStates::TEMPLATE()->getValue() && $state->getValue() !== ProposalStates::ALL()->getValue()) {
                $events["proposal." . $state->getValue()] = 'webhookOnProposal';
            }
        }
        return $events;
    }

    /**
     * @param ProposalEvents $event
     * @param string $eventName
     * @throws Exception
     */
    public function webhookOnProposal(ProposalEvents $event, string $eventName): void
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        try {
            $this->logger->debug("[Webhook Proposal] " . self::class . " event $eventName");
            $organism = $event->getProposal()->getOrganism();
            $eventNameToSearch = $eventName;
            $webhooks = $this->webhookService->listByOrganismsAndEventAndType(new ArrayCollection([$organism]), $eventNameToSearch, []);
            if (sizeof($webhooks) > 0) {
                $serializationContext = new SerializationContext();
                $serializationContext->setSerializeNull(true);
                $serializationContext->setGroups("owner");
                $this->logger->debug("[Webhook Proposal] " . sizeof($webhooks) . " to send");
                $payload = $this->serializer->serialize($event->getProposal(), 'json', $serializationContext);
                /** @var Webhook $webhook */
                foreach ($webhooks as $webhook) {
                    $message = new SendWebhook($webhook->getId(), $eventName, $payload, get_class($event->getProposal()), $event->getProposal()->getId());
                    $this->messageBus->dispatch($message);
                }
            } else {
                $this->logger->debug("[Webhook Proposal] no event to send");
            }
        } catch (Exception $e) {
            $this->logger->error("[Error webhookOnProposal " . $event->getProposal()->getId() . "] at Line " . $e->getLine() . " " . $e->getFile() . " " . $e->getMessage());
        }
    }

    //-------------------
    // METHODES PRIVEES
    //-------------------
}
