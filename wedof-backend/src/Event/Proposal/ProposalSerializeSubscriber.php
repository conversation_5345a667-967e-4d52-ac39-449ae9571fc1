<?php

namespace App\Event\Proposal;


use App\Entity\Proposal;
use App\Entity\User;
use App\Event\TagSerializeSubscriber;
use App\Service\AccessService;
use App\Service\TrainingService;
use <PERSON><PERSON>\Serializer\EventDispatcher\EventSubscriberInterface;
use <PERSON><PERSON>\Serializer\EventDispatcher\ObjectEvent;
use <PERSON><PERSON>\Serializer\JsonSerializationVisitor;
use <PERSON>MS\Serializer\Metadata\StaticPropertyMetadata;
use Symfony\Component\Security\Core\Security;

class ProposalSerializeSubscriber extends TagSerializeSubscriber implements EventSubscriberInterface
{
    private Security $security;
    private AccessService $accessService;
    private TrainingService $trainingService;

    public function __construct(Security $security, AccessService $accessService, TrainingService $trainingService)
    {
        $this->security = $security;
        $this->accessService = $accessService;
        $this->trainingService = $trainingService;
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return array(
            array(
                'event' => 'serializer.post_serialize',
                'method' => 'onPostSerialize',
                'class' => Proposal::class, // if no class, subscribe to every serialization,
                'format' => 'json'
            ),
        );
    }

    /**
     * @param ObjectEvent $event
     */
    public function onPostSerialize(ObjectEvent $event): void
    {
        /** @var User $user */
        $user = $this->security->getUser();
        /** @var Proposal $proposal */
        $proposal = $event->getObject();
        /** @var JsonSerializationVisitor $visitor */
        $visitor = $event->getVisitor();

        $_trainingActions = [];
        foreach ($proposal->getTrainingActions() as $trainingAction) {
            $_trainingActions[] = [
                "externalId" => $trainingAction->getExternalId(),
                "trainingTitle" => $this->trainingService->getTitleFromTrainingAction($trainingAction)
            ];
        }

        $visitor->visitProperty(new StaticPropertyMetadata('', 'trainingActions', null), $_trainingActions);


        if ($user) {
            if ($this->accessService->hasProposalView($user, $proposal)) {
                $used = $proposal->isIndividual() ? ($proposal->getRegistrationFolder() === null ? 0 : 1) : $proposal->getIndividualProposals()->count();
                $visitor->visitProperty(new StaticPropertyMetadata('', 'usedCount', null), $used);
                $this->serializeTags($proposal, $visitor);
            }
        }
    }
}
