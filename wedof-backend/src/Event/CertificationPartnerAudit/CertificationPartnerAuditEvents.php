<?php


namespace App\Event\CertificationPartnerAudit;

use App\Entity\CertificationPartner;
use App\Entity\CertificationPartnerAudit;
use Symfony\Contracts\EventDispatcher\Event;

class CertificationPartnerAuditEvents extends Event
{
    public const PENDING_COMPUTATION = 'certificationPartnerAudit.pendingComputation';
    public const COMPUTING = 'certificationPartnerAudit.computing';
    public const IN_PROGRESS = 'certificationPartnerAudit.inProgress';
    public const COMPLETED = 'certificationPartnerAudit.completed';
    public const COMPLIANT = 'certificationPartnerAudit.compliant';
    public const NON_COMPLIANT = 'certificationPartnerAudit.nonCompliant';
    public const PARTIALLY_COMPLIANT = 'certificationPartnerAudit.partiallyCompliant';

    protected CertificationPartnerAudit $certificationPartnerAudit;
    protected CertificationPartner $certificationPartner;

    public function __construct(CertificationPartnerAudit $certificationPartnerAudit, CertificationPartner $certificationPartner)
    {
        $this->certificationPartnerAudit = $certificationPartnerAudit;
        $this->certificationPartner = $certificationPartner;
    }

    /**
     * @return CertificationPartnerAudit
     */
    public function getCertificationPartnerAudit(): CertificationPartnerAudit
    {
        return $this->certificationPartnerAudit;
    }

    /**
     * @return CertificationPartner
     */
    public function getCertificationPartner(): CertificationPartner
    {
        return $this->certificationPartner;
    }
}
