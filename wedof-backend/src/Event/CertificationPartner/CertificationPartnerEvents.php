<?php


namespace App\Event\CertificationPartner;

use App\Entity\CertificationPartner;
use App\Library\utils\enums\CertificationPartnerStates;
use Symfony\Contracts\EventDispatcher\Event;

class CertificationPartnerEvents extends Event
{

    public const CREATED = 'certificationPartner.created';
    public const UPDATED = 'certificationPartner.updated';
    public const PENDING_ACTIVATION_CHANGED = 'certificationPartner.pendingActivationChanged';
    public const PENDING_REVOCATION_CHANGED = 'certificationPartner.pendingRevocationChanged';
    public const PENDING_SUSPENSION_CHANGED = 'certificationPartner.pendingSuspensionChanged';

    protected CertificationPartner $certificationPartner;
    protected ?CertificationPartnerStates $previousState;
    protected array $parameters;

    public function __construct(CertificationPartner $certificationPartner, CertificationPartnerStates $previousState = null, array $parameters = [])
    {
        $this->certificationPartner = $certificationPartner;
        $this->previousState = $previousState;
        $this->parameters = $parameters;
    }

    /**
     * @return CertificationPartner
     */
    public function getCertificationPartner(): CertificationPartner
    {
        return $this->certificationPartner;
    }

    /**
     * @return CertificationPartnerStates|null
     */
    public function getPreviousState(): ?CertificationPartnerStates
    {
        return $this->previousState;
    }

    /**
     * @return array
     */
    public function getParameters(): array
    {
        return $this->parameters;
    }
}
