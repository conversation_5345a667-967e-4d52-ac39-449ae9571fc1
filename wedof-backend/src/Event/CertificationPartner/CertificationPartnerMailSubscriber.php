<?php


namespace App\Event\CertificationPartner;


use App\Entity\User;
use App\Library\utils\enums\CertificationPartnerStates;
use App\Service\MailerService;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;
use Symfony\Component\Security\Core\Security;
use Throwable;

class CertificationPartnerMailSubscriber implements EventSubscriberInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    private Security $security;
    private MailerService $mailerService;

    public function __construct(MailerService $mailerService, Security $security)
    {
        $this->mailerService = $mailerService;
        $this->security = $security;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    /**
     * @return array|string[]
     */
    public static function getSubscribedEvents(): array
    {
        return [
            CertificationPartnerEvents::PENDING_ACTIVATION_CHANGED => 'sendPendingActivationChangedEmail',
            CertificationPartnerEvents::PENDING_REVOCATION_CHANGED => 'sendPendingRevocationChangedEmail',
            CertificationPartnerEvents::PENDING_SUSPENSION_CHANGED => 'sendPendingSuspensionChangedEmail',
            "certificationPartner." . CertificationPartnerStates::PROCESSING()->getValue() => 'sendProcessingEmail',
            "certificationPartner." . CertificationPartnerStates::REFUSED()->getValue()  => 'sendRefusedEmail',
            "certificationPartner." . CertificationPartnerStates::ACTIVE()->getValue()  => 'sendActiveEmail',
            "certificationPartner." . CertificationPartnerStates::ABORTED()->getValue()  => 'sendAbortedEmail',
            "certificationPartner." . CertificationPartnerStates::SUSPENDED()->getValue() => 'sendSuspendedEmail',
            "certificationPartner." . CertificationPartnerStates::REVOKED()->getValue()  => 'sendRevokedEmail'
        ];
    }

    /**
     * @param CertificationPartnerEvents $event
     * @return void
     * @throws TransportExceptionInterface
     */
    public function sendProcessingEmail(CertificationPartnerEvents $event): void
    {
        /** @var User $user */
        $user = $this->security->getUser();
        $certificationPartner = $event->getCertificationPartner();
        $previousState = $event->getPreviousState();
        $certification = $certificationPartner->getCertification();
        $isCertifier = $certification->isCertifier($user->getMainOrganism());
        $certifiers = $isCertifier ? [$user->getMainOrganism()] : [$certificationPartner->getCertifier()];
        if (empty($certifiers)) {
            $certifiers = $certificationPartner->getCertification()->getCertifiers();
        }
        if ($previousState) {
            if (in_array($previousState->getValue(), [CertificationPartnerStates::ABORTED(), CertificationPartnerStates::REFUSED()])) {
                if ($isCertifier) {
                    $this->mailerService->sendCertificationPartnerReopenToPartner($certificationPartner, $certifiers[0]);
                } else {
                    // For the moment it can happen only if coming from aborted, as partner cannot reopen from refused
                    foreach ($certifiers as $certifier) {
                        $this->mailerService->sendCertificationPartnerReopenToCertifier($certificationPartner, $user, $certifier);
                    }
                }
            } else if ($previousState->getValue() === CertificationPartnerStates::DRAFT()->getValue()) {
                if ($isCertifier) {
                    $this->mailerService->sendCertificationPartnerProcessingToPartner($certificationPartner, $certifiers[0]);
                } else {
                    $sendToWedof = true;
                    foreach ($certifiers as $certifier) {
                        if ($certifier && $certifier->getSubscription() && $certifier->getSubscription()->isAllowCertifierPlus() && $certifier->getOwnedBy()) {
                            $this->mailerService->sendCertificationPartnerProcessingToCertifier($certificationPartner, $user, $certifier);
                            $sendToWedof = false;
                        }
                    }
                    if ($sendToWedof) {
                        $this->mailerService->sendCertificationPartnerProcessingToWedofTeam($certificationPartner, $user);
                    }
                }
            }
        }
    }

    /**
     * @param CertificationPartnerEvents $event
     * @return void
     * @throws TransportExceptionInterface
     */
    public function sendAbortedEmail(CertificationPartnerEvents $event): void
    {
        /** @var User $user */
        $user = $this->security->getUser();
        $certificationPartner = $event->getCertificationPartner();
        $certification = $certificationPartner->getCertification();
        if ($certification->isCertifier($user->getMainOrganism())) {
            $this->mailerService->sendCertificationPartnerAbortedToPartner($certificationPartner, $user->getMainOrganism());
        } else if ($certificationPartner->getCertifier()) {
            $this->mailerService->sendCertificationPartnerAbortedToCertifier($certificationPartner, $user);
        }
    }

    /**
     * @param CertificationPartnerEvents $event
     * @return void
     * @throws TransportExceptionInterface
     */
    public function sendRefusedEmail(CertificationPartnerEvents $event): void
    {
        $certificationPartner = $event->getCertificationPartner();
        $this->mailerService->sendCertificationPartnerRefused($certificationPartner);
    }

    /**
     * @param CertificationPartnerEvents $event
     * @return void
     * @throws TransportExceptionInterface
     */
    public function sendPendingActivationChangedEmail(CertificationPartnerEvents $event): void
    {
        $certificationPartner = $event->getCertificationPartner();
        $this->mailerService->sendCertificationPartnerPendingActivationChanged($certificationPartner);
    }

    /**
     * @param CertificationPartnerEvents $event
     * @return void
     * @throws TransportExceptionInterface
     */
    public function sendPendingRevocationChangedEmail(CertificationPartnerEvents $event): void
    {
        $certificationPartner = $event->getCertificationPartner();
        $this->mailerService->sendCertificationPartnerPendingRevocationChanged($certificationPartner);
    }

    /**
     * @param CertificationPartnerEvents $event
     * @return void
     * @throws TransportExceptionInterface
     */
    public function sendPendingSuspensionChangedEmail(CertificationPartnerEvents $event): void
    {
        $certificationPartner = $event->getCertificationPartner();
        $this->mailerService->sendCertificationPartnerPendingSuspensionChanged($certificationPartner);
    }

    /**
     * @param CertificationPartnerEvents $event
     * @return void
     */
    public function sendActiveEmail(CertificationPartnerEvents $event): void
    {
        try {
            $certificationPartner = $event->getCertificationPartner();
            $certifiers = $certificationPartner->getCertifier() !== null ? [$certificationPartner->getCertifier()] : $certificationPartner->getCertification()->getCertifiers();
            $sendToPartner = false;
            foreach ($certifiers as $certifier) {
                if ($certifier && $certifier->getSubscription() && $certifier->getSubscription()->isAllowCertifierPlus()) {
                    $this->mailerService->sendCertificationPartnerActiveToCertifier($certificationPartner, $certifier);
                    $sendToPartner = true;
                }
            }
            if ($sendToPartner) {
                $this->mailerService->sendCertificationPartnerActiveToPartner($certificationPartner);
            }
        } catch (Throwable $e) {
            $this->logger->error('[certificationPartner] - Activation Mail error - ' . $e->getMessage());
        }
    }

    /**
     * @param CertificationPartnerEvents $event
     * @return void
     */
    public function sendSuspendedEmail(CertificationPartnerEvents $event): void
    {
        try {
            $certificationPartner = $event->getCertificationPartner();
            $certifiers = $certificationPartner->getCertifier() !== null ? [$certificationPartner->getCertifier()] : $certificationPartner->getCertification()->getCertifiers();
            $sendToPartner = false;
            foreach ($certifiers as $certifier) {
                if ($certifier && $certifier->getSubscription() && $certifier->getSubscription()->isAllowCertifierPlus()) {
                    $this->mailerService->sendCertificationPartnerSuspendedToCertifier($certificationPartner, $certifier);
                    $sendToPartner = true;
                }
            }
            if ($sendToPartner) {
                $this->mailerService->sendCertificationPartnerSuspendedToPartner($certificationPartner, $event->getParameters());
            }
        } catch (Throwable $exception) {
            $this->logger->error('[certificationPartner] - Suspension Mail error - ' . $exception->getMessage());
        }
    }

    /**
     * @param CertificationPartnerEvents $event
     * @return void
     */
    public function sendRevokedEmail(CertificationPartnerEvents $event): void
    {
        try {
            $certificationPartner = $event->getCertificationPartner();
            $certifiers = $certificationPartner->getCertifier() !== null ? [$certificationPartner->getCertifier()] : $certificationPartner->getCertification()->getCertifiers();
            $sendToPartner = false;
            foreach ($certifiers as $certifier) {
                if ($certifier && $certifier->getSubscription() && $certifier->getSubscription()->isAllowCertifierPlus()) {
                    $this->mailerService->sendCertificationPartnerRevokedToCertifier($certificationPartner, $certifier, $event->getParameters());
                    $sendToPartner = true;
                }
            }
            if ($sendToPartner) {
                $this->mailerService->sendCertificationPartnerRevokedToPartner($certificationPartner, $event->getParameters());
            }
        } catch (Throwable $e) {
            $this->logger->error('[certificationPartner] - Revocation Mail error - ' . $e->getMessage());
        }
    }
}

