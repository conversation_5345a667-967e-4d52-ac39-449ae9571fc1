<?php


namespace App\Event\CertificationPartner;


use App\Entity\CertificationPartner;
use App\Entity\User;
use App\Event\TagSerializeSubscriber;
use App\Library\utils\enums\CertifierAccessStates;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\enums\TrainingActionStates;
use App\Library\utils\enums\TrainingStates;
use App\Repository\TrainingActionRepository;
use App\Repository\TrainingRepository;
use App\Service\CertificationFolderService;
use App\Service\CertifierAccessService;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use JMS\Serializer\EventDispatcher\EventSubscriberInterface;
use JMS\Serializer\EventDispatcher\ObjectEvent;
use JMS\Serializer\JsonSerializationVisitor;
use J<PERSON>\Serializer\Metadata\StaticPropertyMetadata;
use Symfony\Component\Security\Core\Security;

class CertificationPartnerSerializeSubscriber extends TagSerializeSubscriber implements EventSubscriberInterface
{

    private Security $security;
    private TrainingRepository $trainingRepository;
    private TrainingActionRepository $trainingActionRepository;
    private CertifierAccessService $certifierAccessService;
    private CertificationFolderService $certificationFolderService;

    public function __construct(CertificationFolderService $certificationFolderService, Security $security, CertifierAccessService $certifierAccessService, TrainingRepository $trainingRepository, TrainingActionRepository $trainingActionRepository)
    {
        $this->security = $security;
        $this->trainingRepository = $trainingRepository;
        $this->trainingActionRepository = $trainingActionRepository;
        $this->certifierAccessService = $certifierAccessService;
        $this->certificationFolderService = $certificationFolderService;
    }


    //-------------------
    // METHODES PUBLIQUES
    //-------------------
    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return array(
            array(
                'event' => 'serializer.post_serialize',
                'method' => 'onPostSerialize',
                'class' => CertificationPartner::class, // if no class, subscribe to every serialization,
                'format' => 'json'
            ),
        );
    }

    /**
     * @param ObjectEvent $event
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function onPostSerialize(ObjectEvent $event): void
    {
        /** @var User $user */
        $user = $this->security->getUser();
        if ($user) {
            $organism = $user->getMainOrganism();
        }
        /** @var CertificationPartner $certificationPartner */
        $certificationPartner = $event->getObject();
        $visitor = $event->getVisitor();

        // Informations à destination du certificateur uniquement
        $isCertifier = isset($organism) && $certificationPartner->getCertifier() === $organism;
        if ($isCertifier && in_array('owner', $event->getContext()->getAttribute('groups'))) {
            $partner = $certificationPartner->getPartner();
            $certification = $certificationPartner->getCertification();
            /** @var JsonSerializationVisitor $visitor */
            $certifierAccess = $this->certifierAccessService->getByOrganisms($organism, $partner, 'all');
            $certificationFoldersCount = $this->certificationFolderService->countForPartnerAndCertification($partner, $certification);
            $visitor->visitProperty(new StaticPropertyMetadata('', 'certificationFoldersCount', null), $certificationFoldersCount);
            $certifierAccessState = $certifierAccess ? $certifierAccess->getState() : CertifierAccessStates::NONE();
            $visitor->visitProperty(new StaticPropertyMetadata('', 'certifierAccessState', null), $certifierAccessState->getValue());
            if ($certifierAccessState->getValue() === CertifierAccessStates::ACCEPTED()->getValue()) {
                $dataProviders = DataProviders::requiredConnectionsDataProvidersForTrainingOrganisms();
                $connections = [];
                foreach ($dataProviders as $dataProvider) {
                    // Pas forcément très performant, mais permet de donner aussi de l'information sur des connexions inexistantes, contrairement à une requête en BDD
                    $connection = $partner->getConnectionForDataProvider($dataProvider);
                    if ($connection) {
                        $state = $connection->getExistAtDataProvider() ? $connection->getState() : 'accountNotExist';
                    } else {
                        $state = 'accountNotExist';
                    }
                    $connections[] = [
                        "name" => $dataProvider->getValue(),
                        "state" => $state,
                        "failedAt" => $connection ? $connection->getFailedAt() : null
                    ];
                }
                $visitor->visitProperty(new StaticPropertyMetadata('', 'connections', null), $connections);
                $visitor->visitProperty(new StaticPropertyMetadata('', 'activeTrainingsCount', null), $this->trainingRepository->countByCertificationPartnerAndStates($certificationPartner, [TrainingStates::PUBLISHED()->getValue()]));
                $visitor->visitProperty(new StaticPropertyMetadata('', 'activeTrainingActionsCount', null), $this->trainingActionRepository->countByCertificationAndByOrganismAndStates($certification, $partner, [TrainingActionStates::PUBLISHED()->getValue()]));
                $visitor->visitProperty(new StaticPropertyMetadata('', 'archivedTrainingsCount', null), $this->trainingRepository->countByCertificationPartnerAndStates($certificationPartner, [TrainingStates::ARCHIVED()->getValue()]));
            }
            if (!$organism->getSubscription()->isAllowCertifierPlus()) {
                $estimation = $certificationPartner->getEstimatedRegistrationFoldersCount();
                if ($estimation < 100) {
                    $estimation = ceil($estimation / 10) * 10;
                } else if ($estimation < 1000) {
                    $estimation = ceil($estimation / 100) * 100;
                } else if ($estimation < 10000) {
                    $estimation = ceil($estimation / 1000) * 1000;
                } else if ($estimation < 100000) {
                    $estimation = ceil($estimation / 10000) * 10000;
                } else {
                    $estimation = ceil($estimation / 100000) * 100000;
                }
                $visitor->visitProperty(new StaticPropertyMetadata('', 'estimatedRegistrationFoldersCount', null), $estimation > 0 ? "- de $estimation" : "0");
                $visitor->visitProperty(new StaticPropertyMetadata('', 'compliance', null), null);
            }
            $visitor->visitProperty(new StaticPropertyMetadata('', 'metadata', null), $certificationPartner->getMetadata());
            $this->serializeTags($certificationPartner, $visitor);
        } else {
            $visitor->visitProperty(new StaticPropertyMetadata('', 'certificationFoldersCount', null), null);
            $visitor->visitProperty(new StaticPropertyMetadata('', 'certifierAccessState', null), null);
            $visitor->visitProperty(new StaticPropertyMetadata('', 'connections', null), null);
            $visitor->visitProperty(new StaticPropertyMetadata('', 'activeTrainingsCount', null), null);
            $visitor->visitProperty(new StaticPropertyMetadata('', 'activeTrainingActionsCount', null), null);
            $visitor->visitProperty(new StaticPropertyMetadata('', 'archivedTrainingsCount', null), null);
            $visitor->visitProperty(new StaticPropertyMetadata('', 'estimatedRegistrationFoldersCount', null), null);
        }
    }

    //-----------------
    // METHODES PRIVEES
    //-----------------
}
