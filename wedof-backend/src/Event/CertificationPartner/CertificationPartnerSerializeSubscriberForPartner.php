<?php


namespace App\Event\CertificationPartner;


use App\Entity\CertificationPartner;
use App\Entity\User;
use <PERSON><PERSON>\Serializer\EventDispatcher\EventSubscriberInterface;
use <PERSON><PERSON>\Serializer\EventDispatcher\ObjectEvent;
use <PERSON><PERSON>\Serializer\JsonSerializationVisitor;
use <PERSON><PERSON>\Serializer\Metadata\StaticPropertyMetadata;
use Symfony\Component\Security\Core\Security;

class CertificationPartnerSerializeSubscriberForPartner implements EventSubscriberInterface
{

    private Security $security;

    public function __construct(Security $security)
    {
        $this->security = $security;
    }


    //-------------------
    // METHODES PUBLIQUES
    //-------------------
    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return array(
            array(
                'event' => 'serializer.post_serialize',
                'method' => 'onPostSerialize',
                'class' => CertificationPartner::class, // if no class, subscribe to every serialization,
                'format' => 'json'
            ),
        );
    }

    /**
     * @param ObjectEvent $event
     */
    public function onPostSerialize(ObjectEvent $event): void
    {
        /** @var User $user */
        $user = $this->security->getUser();
        if ($user) {
            $organism = $user->getMainOrganism();
        }
        /** @var CertificationPartner $certificationPartner */
        $certificationPartner = $event->getObject();
        /** @var JsonSerializationVisitor $visitor */
        $visitor = $event->getVisitor();

        $isPartner = isset($organism) && $certificationPartner->getCertifier() !== $organism && $certificationPartner->getPartner() === $organism;
        // certifier and partner share the same group
        if ($isPartner && in_array('owner', $event->getContext()->getAttribute('groups'))) {
            $this->serializeFiles($certificationPartner, $visitor);
        }
    }

    //-------------------
    // METHODES PRIVEES
    //-------------------

    /**
     * @param CertificationPartner $certificationPartner
     * @param JsonSerializationVisitor $visitor
     */
    private function serializeFiles(CertificationPartner $certificationPartner, JsonSerializationVisitor $visitor): void
    {
        $newFiles = [];
        $certificationPartnerFiles = $certificationPartner->getFiles();
        $certification = $certificationPartner->getCertification();
        $fileTypes = $certification->getCertificationPartnerFileTypes();
        foreach ($certificationPartnerFiles as $certificationPartnerFile) {
            $fileTypeIndex = array_search($certificationPartnerFile->getTypeId(), array_column($fileTypes, 'id'));
            if ($fileTypeIndex !== false) {
                $fileType = $fileTypes[$fileTypeIndex];
                $allowVisibilityPartner = $fileType['allowVisibilityPartner'] ?? null;
                $allowUploadPartner = $fileType['allowUploadPartner'] ?? null;
                $allowSignPartner = $fileType['allowSignPartner'] ?? null;
                if ($allowVisibilityPartner) {
                    $file = [
                        'fileName' => $certificationPartnerFile->getFileName(),
                        'fileType' => $certificationPartnerFile->getFileType(),
                        'generationState' => $certificationPartnerFile->getGenerationState(),
                        'id' => $certificationPartnerFile->getId(),
                        'link' => $certificationPartnerFile->getLink(),
                        'typeId' => $certificationPartnerFile->getTypeId(),
                        'state' => $certificationPartnerFile->getState()
                    ];
                    if ($allowUploadPartner) {
                        $file['comment'] = $certificationPartnerFile->getComment();
                    }
                    if ($allowSignPartner) {
                        $file['signedState'] = $certificationPartnerFile->getSignedState();
                    }
                    $newFiles[] = $file;
                }
            }
        }
        $visitor->visitProperty(new StaticPropertyMetadata('', 'files', null), $newFiles);
    }
}
