<?php

namespace App\Event\CertificationPartner;

use App\Entity\Webhook;
use App\Library\utils\enums\CertificationPartnerStates;
use App\Message\SendWebhook;
use App\Service\WebhookService;
use Doctrine\Common\Collections\ArrayCollection;
use <PERSON><PERSON>\Serializer\SerializationContext;
use <PERSON><PERSON>\Serializer\SerializerInterface;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Messenger\MessageBusInterface;

class CertificationPartnerWebhookSubscriber implements EventSubscriberInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    private WebhookService $webhookService;
    private MessageBusInterface $messageBus;
    private SerializerInterface $serializer;

    public function __construct(WebhookService $webhookService, MessageBusInterface $messageBus, SerializerInterface $serializer)
    {
        $this->webhookService = $webhookService;
        $this->messageBus = $messageBus;
        $this->serializer = $serializer;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    //-------------------
    // METHODES PUBLIQUES
    //-------------------
    public static function getSubscribedEvents(): array
    {
        $events = [];
        foreach (CertificationPartnerStates::valuesStates() as $state) {
            if ($state->getValue() !== CertificationPartnerStates::DRAFT()->getValue() && $state->getValue() !== CertificationPartnerStates::ALL()->getValue()) {
                $events["certificationPartner." . $state->getValue()] = 'webhookOnCertificationPartner';
            }
        }
        return $events;
    }

    /**
     * @param CertificationPartnerEvents $event
     * @param string $eventName
     */
    public function webhookOnCertificationPartner(CertificationPartnerEvents $event, string $eventName)
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        $this->logger->info(self::class . " event $eventName");
        $certificationPartner = $event->getCertificationPartner();
        $certifiers = $certificationPartner->getCertifier() ? [$certificationPartner->getCertifier()] : $certificationPartner->getCertification()->getCertifiers();
        if ($certifiers) {
            foreach ($certifiers as $certifier) {
                $webhooks = $this->webhookService->listByOrganismsAndEventAndType(new ArrayCollection([$certifier]), $eventName);
                if (sizeof($webhooks) > 0) {
                    if ($certifier->getSubscription() && !$certifier->getSubscription()->isAllowCertifierPlus()) {
                        $payload = '{"error": "Pour recevoir les données du partenariat mis à jour, souscrivez à l\'option de gestion des partenaires sur votre abonnement."}';
                    } else {
                        $serializationContext = new SerializationContext();
                        $serializationContext->setSerializeNull(true);
                        $serializationContext->setGroups("owner");
                        $payload = $this->serializer->serialize($certificationPartner, 'json', $serializationContext);
                    }
                    /** @var Webhook $webhook */
                    foreach ($webhooks as $webhook) {
                        $message = new SendWebhook($webhook->getId(), $eventName, $payload, get_class($certificationPartner), $certificationPartner->getId());
                        $this->messageBus->dispatch($message);
                    }
                }
            }
        }
    }

    //-----------------
    // METHODES PRIVEES
    //-----------------
}
