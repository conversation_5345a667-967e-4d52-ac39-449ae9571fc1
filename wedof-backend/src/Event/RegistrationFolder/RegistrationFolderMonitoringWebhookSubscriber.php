<?php

namespace App\Event\RegistrationFolder;

use App\Entity\Webhook;
use App\Message\SendWebhook;
use App\Service\WebhookService;
use Doctrine\Common\Collections\ArrayCollection;
use <PERSON><PERSON>\Serializer\SerializationContext;
use <PERSON><PERSON>\Serializer\SerializerInterface;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Messenger\MessageBusInterface;

class RegistrationFolderMonitoringWebhookSubscriber implements EventSubscriberInterface, LoggerAwareInterface
{

    private LoggerInterface $logger;
    private WebhookService $webhookService;
    private MessageBusInterface $messageBus;
    private SerializerInterface $serializer;

    public function __construct(WebhookService $webhookService, MessageBusInterface $messageBus, SerializerInterface $serializer)
    {
        $this->webhookService = $webhookService;
        $this->messageBus = $messageBus;
        $this->serializer = $serializer;
    }

    public static function getSubscribedEvents(): array
    {
        return [
            RegistrationFolderMonitoringEvents::NOT_VALIDATED => 'webhookOnMonitoredRegistrationFolder',
            RegistrationFolderMonitoringEvents::NOT_IN_TRAINING => 'webhookOnMonitoredRegistrationFolder',
            RegistrationFolderMonitoringEvents::NOT_ACCEPTED => 'webhookOnMonitoredRegistrationFolder',
            RegistrationFolderMonitoringEvents::NOT_SERVICE_DONE_DECLARED => 'webhookOnMonitoredRegistrationFolder',
        ];
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    public function webhookOnMonitoredRegistrationFolder(RegistrationFolderMonitoringEvents $event, string $eventName)
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        $this->logger->info(self::class . " event $eventName");
        $webhooks = $this->webhookService->listByOrganismsAndEventAndType(new ArrayCollection([$event->getRegistrationFolder()->getOrganism()]), $eventName);
        $this->logger->debug("[Webhook RegistrationFolder Monitoring] " . sizeof($webhooks) . " event found : $eventName");
        if (sizeof($webhooks) > 0) {
            $serializationContext = new SerializationContext();
            $serializationContext->setSerializeNull(true);
            $serializationContext->setGroups("owner");
            $payload = $this->serializer->serialize($event->getRegistrationFolder(), 'json', $serializationContext);
            /** @var Webhook $webhook */
            foreach ($webhooks as $webhook) {
                $message = new SendWebhook($webhook->getId(), $eventName, $payload, get_class($event->getRegistrationFolder()), $event->getRegistrationFolder()->getExternalId());
                $this->messageBus->dispatch($message);
            }
        }
    }
}
