<?php

namespace App\Event\RegistrationFolder;

use App\Service\ProposalService;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Exception;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

class RegistrationFolderProposalSubscriber implements EventSubscriberInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    private ProposalService $proposalService;

    public function __construct(ProposalService $proposalService)
    {
        $this->proposalService = $proposalService;
    }

    /**
     * @return array|string[]
     */
    public static function getSubscribedEvents(): array
    {
        return [
            'registrationFolder.notProcessed' => ['applyProposalOnRegistrationFolder', -1000] //be the last event to be executed
        ];
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    /**
     * @param RegistrationFolderEvents $event
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws \ErrorException
     * @throws \Throwable
     */
    public function applyProposalOnRegistrationFolder(RegistrationFolderEvents $event)
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        $registrationFolder = $event->getRegistrationFolder();
        $this->logger->debug('[Webhook RegistrationFolderProposalSubscriber] maybe apply ' . $registrationFolder->getExternalId());
        $proposal = $this->proposalService->getProposalToApply($registrationFolder);
        if ($proposal) {
            try {
                $this->logger->debug('[Webhook RegistrationFolderProposalSubscriber] found proposal to apply ' . $proposal->getCode());
                $this->proposalService->applyOnRegistrationFolder($proposal, $registrationFolder);
                $this->logger->debug('[Webhook RegistrationFolderProposalSubscriber] proposal applied ' . $proposal->getCode());
            } catch (Exception $e) {
                $this->proposalService->update($proposal, ['description' => "Erreur lors de l'application de cette proposition vérifiez les données"]);
            }
        }
    }
}
