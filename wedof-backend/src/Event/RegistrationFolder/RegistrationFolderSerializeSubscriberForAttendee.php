<?php
// src/Event/RegistrationFolder/RegistrationFolderSerializeSubscriberForAttendee.php

namespace App\Event\RegistrationFolder;


use App\Entity\RegistrationFolder;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\Tools;
use App\Service\DataProviders\BaseApiService;
use J<PERSON>\Serializer\EventDispatcher\EventSubscriberInterface;
use <PERSON><PERSON>\Serializer\EventDispatcher\ObjectEvent;
use J<PERSON>\Serializer\JsonSerializationVisitor;
use J<PERSON>\Serializer\Metadata\StaticPropertyMetadata;

class RegistrationFolderSerializeSubscriberForAttendee implements EventSubscriberInterface
{
    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return array(
            array(
                'event' => 'serializer.post_serialize',
                'method' => 'onPostSerialize',
                'class' => RegistrationFolder::class, // if no class, subscribe to every serialization,
                'format' => 'json'
            ),
        );
    }

    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    /**
     * @param ObjectEvent $event
     */
    public function onPostSerialize(ObjectEvent $event): void
    {
        if (in_array('attendee', $event->getContext()->getAttribute('groups'))) {
            /** @var RegistrationFolder $registrationFolder */
            $registrationFolder = $event->getObject();
            /** @var JsonSerializationVisitor $visitor */
            $visitor = $event->getVisitor();

            $this->serializeFiles($registrationFolder, $visitor);
            $this->serializeTrainingAction($registrationFolder, $visitor);
        }
    }

    //-------------------
    // METHODES PRIVEES
    //-------------------

    /**
     * @param RegistrationFolder $registrationFolder
     * @param JsonSerializationVisitor $visitor
     */
    private function serializeFiles(RegistrationFolder $registrationFolder, JsonSerializationVisitor $visitor): void
    {
        $newFiles = [];
        $registrationFolderFiles = $registrationFolder->getFiles();
        $organism = $registrationFolder->getOrganism();
        $fileTypes = $organism->getRegistrationFolderFileTypes();
        foreach ($registrationFolderFiles as $registrationFolderFile) {
            $fileTypeIndex = array_search($registrationFolderFile->getTypeId(), array_column($fileTypes, 'id'));
            if ($fileTypeIndex !== false) {
                $fileType = $fileTypes[$fileTypeIndex];
                $allowVisibilityAttendee = $fileType['allowVisibilityAttendee'] ?? null;
                $allowUploadAttendee = $fileType['allowUploadAttendee'] ?? null;
                $allowSignAttendee = $fileType['allowSignAttendee'] ?? null;
                if ($allowVisibilityAttendee) {
                    $file = [
                        'fileName' => $registrationFolderFile->getFileName(),
                        'fileType' => $registrationFolderFile->getFileType(),
                        'generationState' => $registrationFolderFile->getGenerationState(),
                        'id' => $registrationFolderFile->getId(),
                        'link' => $registrationFolderFile->getLink(),
                        'permalink' => $registrationFolderFile->getPermalink(),
                        'typeId' => $registrationFolderFile->getTypeId(),
                        'state' => $registrationFolderFile->getState()
                    ];
                    if ($allowUploadAttendee) {
                        $file['comment'] = $registrationFolderFile->getComment();
                    }
                    if ($allowSignAttendee) {
                        $file['signedState'] = $registrationFolderFile->getSignedState();
                    }
                    $newFiles[] = $file;
                }
            }
        }
        $visitor->visitProperty(new StaticPropertyMetadata('', 'files', null), $newFiles);
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @param JsonSerializationVisitor $visitor
     */
    private function serializeTrainingAction(RegistrationFolder $registrationFolder, JsonSerializationVisitor $visitor): void
    {
        $trainingActionInfo = $registrationFolder->getRawData()['trainingActionInfo'];
        $startDate = Tools::generateDateStartOrEndOfDay($trainingActionInfo['sessionStartDate']);
        $endDate = Tools::generateDateStartOrEndOfDay($trainingActionInfo['sessionEndDate']);
        $dataProvider = DataProviders::from($registrationFolder->getType());
        $infos = array(
            'externalLink' => BaseApiService::getCatalogApiServiceByDataProvider($dataProvider)->getTrainingActionExternalLink($registrationFolder->getSession()->getTrainingAction()),
            'sessionStartDate' => $startDate ?: $trainingActionInfo['sessionStartDate'],
            'sessionEndDate' => $endDate ?: $trainingActionInfo['sessionEndDate'],
            'title' => $trainingActionInfo['title'],
            'indicativeDuration' => $trainingActionInfo['indicativeDuration'],
            'teachingModalities' => $trainingActionInfo['teachingModalities'] ?? null,
            'hoursInCenter' => $trainingActionInfo['hoursInCenter'] ?? null,
            'hoursInCompany' => $trainingActionInfo['hoursInCompany'] ?? null,
            'totalExcl' => $trainingActionInfo['totalExcl'] ?? null
        );

        $visitor->visitProperty(new StaticPropertyMetadata('', 'trainingActionInfo', null), $infos);
    }
}