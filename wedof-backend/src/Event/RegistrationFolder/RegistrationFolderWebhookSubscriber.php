<?php

namespace App\Event\RegistrationFolder;

use App\Entity\Webhook;
use App\Library\utils\enums\RegistrationFolderAttendeeStates;
use App\Library\utils\enums\RegistrationFolderBillingStates;
use App\Library\utils\enums\RegistrationFolderControlStates;
use App\Library\utils\enums\RegistrationFolderStates;
use App\Message\SendWebhook;
use App\Service\WebhookService;
use Doctrine\Common\Collections\ArrayCollection;
use Exception;
use JMS\Serializer\SerializationContext;
use JMS\Serializer\SerializerInterface;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Messenger\Stamp\DelayStamp;

class RegistrationFolderWebhookSubscriber implements EventSubscriberInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    private WebhookService $webhookService;
    private MessageBusInterface $messageBus;
    private SerializerInterface $serializer;

    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    public function __construct(WebhookService $webhookService, MessageBusInterface $messageBus, SerializerInterface $serializer)
    {
        $this->webhookService = $webhookService;
        $this->messageBus = $messageBus;
        $this->serializer = $serializer;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    public static function getSubscribedEvents(): array
    {
        $events = array(
            RegistrationFolderEvents::CREATED => 'webhookOnRegistrationFolder',
            RegistrationFolderEvents::UPDATED => 'webhookOnRegistrationFolder',
        );
        foreach (RegistrationFolderStates::valuesStates() as $state) {
            $events['registrationFolder.' . $state->getValue()] = 'webhookOnRegistrationFolder';
        }
        foreach (RegistrationFolderBillingStates::valuesStates() as $state) {
            $events['registrationFolderBilling.' . $state->getValue()] = 'webhookOnRegistrationFolder';
        }
        foreach (RegistrationFolderAttendeeStates::valuesStates() as $state) {
            $events['registrationFolderAttendee.' . $state->getValue()] = 'webhookOnRegistrationFolder';
        }
        foreach (RegistrationFolderControlStates::valuesStates() as $state) {
            $events['registrationFolderControl.' . $state->getValue()] = 'webhookOnRegistrationFolder';
        }
        return $events;
    }

    /**
     * @param RegistrationFolderEvents $event
     * @param string $eventName
     * @throws Exception
     */
    public function webhookOnRegistrationFolder(RegistrationFolderEvents $event, string $eventName): void
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        try {
            $this->logger->debug("[Webhook RegistrationFolder] " . self::class . " event $eventName");
            $organism = $event->getRegistrationFolder()->getOrganism();
            $eventNameToSearch = $eventName;
            // TODO : fix this mess !
            // There are 3 technical states for the same label in EDOF : "Annulé sans suite" : rejected, rejectedWithoutTitulaireSuite, rejectedWithoutCdcSuite
            // Nothing allows to know why they differ yet
            // Most are "rejectedWithoutTitulaireSuite"
            // We could either understand the difference
            // or make it disappear by storing only one state (ideally "rejected") & have the details somewhere else
            // but it may be dangerous (change it or not in the raw data as well?)
            // In the meantime, all of them are sent to the same Webhook, the only one that can be created : "rejectedWithoutTitulaireSuite"
            if (in_array($eventName, ['registrationFolder.' . RegistrationFolderStates::REJECTED()->getValue(), 'registrationFolder.' . RegistrationFolderStates::REJECTED_WITHOUT_CDC_SUITE()->getValue(), 'registrationFolder.' . RegistrationFolderStates::REJECTED_WITHOUT_OF_SUITE()->getValue()])) {
                $eventNameToSearch = 'registrationFolder.' . RegistrationFolderStates::REJECTED_WITHOUT_TITULAIRE_SUITE()->getValue();
            }
            $webhooks = $this->webhookService->listByOrganismsAndEventAndType(new ArrayCollection([$organism]), $eventNameToSearch, []);
            if (sizeof($webhooks) > 0) {
                $serializationContext = new SerializationContext();
                $serializationContext->setSerializeNull(true);
                $serializationContext->setGroups("owner");
                $this->logger->debug("[Webhook RegistrationFolder] " . sizeof($webhooks) . " to send");
                $payload = $this->serializer->serialize($event->getRegistrationFolder(), 'json', $serializationContext);
                /** @var Webhook $webhook */
                foreach ($webhooks as $webhook) {
                    $message = new SendWebhook($webhook->getId(), $eventName, $payload, get_class($event->getRegistrationFolder()), $event->getRegistrationFolder()->getExternalId());
                    if ($event->getDelay()) {
                        $dispatch = new Envelope($message, [
                            new DelayStamp($event->getDelay())
                        ]);
                    } else {
                        $dispatch = $message;
                    }
                    $this->messageBus->dispatch($dispatch);
                }
            } else {
                $this->logger->debug("[Webhook RegistrationFolder] no event to send");
            }
        } catch (Exception $e) {
            $this->logger->error("[Error webhookOnRegistrationFolder " . $event->getRegistrationFolder()->getExternalId() . "] at Line " . $e->getLine() . " " . $e->getFile() . " " . $e->getMessage());
        }
    }

    //-------------------
    // METHODES PRIVEES
    //-------------------
}
