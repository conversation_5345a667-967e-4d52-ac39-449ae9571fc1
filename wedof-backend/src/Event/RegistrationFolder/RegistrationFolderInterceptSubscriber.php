<?php

namespace App\Event\RegistrationFolder;

use App\Event\RegistrationFolderFile\RegistrationFolderFileEvents;
use App\Library\utils\enums\FileStates;
use App\Library\utils\enums\RegistrationFolderAttendeeStates;
use App\Library\utils\enums\RegistrationFolderBillingStates;
use App\Library\utils\enums\RegistrationFolderControlStates;
use App\Library\utils\enums\RegistrationFolderStates;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

class RegistrationFolderInterceptSubscriber implements EventSubscriberInterface
{

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents()
    {
        $events = array(
            RegistrationFolderEvents::CREATED => ['allowEventPropagation', 100],
            RegistrationFolderEvents::UPDATED => ['allowEventPropagation', 100],
            RegistrationFolderEvents::PROPOSAL_APPLIED => ['allowEventPropagation', 100],
            RegistrationFolderMonitoringEvents::NOT_VALIDATED => ['allowEventPropagation', 100],
            RegistrationFolderMonitoringEvents::NOT_IN_TRAINING => ['allowEventPropagation', 100],
            RegistrationFolderMonitoringEvents::NOT_ACCEPTED => ['allowEventPropagation', 100],
            RegistrationFolderMonitoringEvents::NOT_SERVICE_DONE_DECLARED => ['allowEventPropagation', 100]
        );
        foreach (RegistrationFolderStates::valuesStates() as $state) {
            $events["registrationFolder." . $state->getValue()] = ['allowEventPropagation', 100];
        }
        foreach (RegistrationFolderBillingStates::valuesStates() as $state) {
            $events["registrationFolderBilling." . $state->getValue()] = ['allowEventPropagation', 100];
        }
        foreach (RegistrationFolderAttendeeStates::valuesStates() as $state) {
            $events["registrationFolderAttendee." . $state->getValue()] = ['allowEventPropagation', 100];
        }
        foreach (RegistrationFolderControlStates::valuesStates() as $state) {
            $events["registrationFolderControl." . $state->getValue()] = ['allowEventPropagation', 100];
        }
        return $events;
    }

    /**
     * @param RegistrationFolderEvents|RegistrationFolderMonitoringEvents $event
     * @param string $eventName
     * @return void
     */
    public function allowEventPropagation($event, string $eventName): void
    {
        $registrationFolder = $event->getRegistrationFolder();

        if (!$registrationFolder->isAllowActions()) {
            $event->stopPropagation();
        }
    }
}