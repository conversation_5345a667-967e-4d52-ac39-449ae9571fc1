<?php
// src/Event/RegistrationFolder/RegistrationFolderSerializeSubscriber.php
namespace App\Event\RegistrationFolder;

use App\Entity\RegistrationFolder;
use App\Library\utils\Tools;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\Persistence\Mapping\MappingException;
use <PERSON><PERSON>\Serializer\EventDispatcher\EventSubscriberInterface;
use <PERSON><PERSON>\Serializer\EventDispatcher\ObjectEvent;
use <PERSON><PERSON>\Serializer\JsonSerializationVisitor;
use <PERSON><PERSON>\Serializer\Metadata\ClassMetadata;
use <PERSON><PERSON>\Serializer\Metadata\StaticPropertyMetadata;
use ReflectionException;

class RegistrationFolderSerializeSubscriberForAccess implements EventSubscriberInterface
{
    private EntityManagerInterface $em;

    public function __construct(EntityManagerInterface $entityManager)
    {
        //Used to access getFieldsName method from metadata
        $this->em = $entityManager;
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return array(
            array(
                'event' => 'serializer.post_serialize',
                'method' => 'onPostSerialize',
                'class' => RegistrationFolder::class, // if no class, subscribe to every serialization,
                'format' => 'json',
                'priority' => 100
            ),
        );
    }

    //-------------------
    // METHODES PUBLIQUES
    //-------------------
    /**
     * @param ObjectEvent $event
     * @throws MappingException
     * @throws ReflectionException
     */
    public function onPostSerialize(ObjectEvent $event): void
    {
        /** @var RegistrationFolder $registrationFolder */
        $registrationFolder = $event->getObject();
        if (!$registrationFolder->isAllowActions()) {

            $context = $event->getContext();
            $type = $event->getType();
            /** @var ClassMetadata $metadata */
            $metadata = $context->getMetadataFactory()->getMetadataForClass($type['name']);

            /** @var JsonSerializationVisitor $visitor */
            $visitor = $event->getVisitor();

            $data = $visitor->endVisitingObject($metadata, $registrationFolder, $type);
            $visitor->startVisitingObject($metadata, $registrationFolder, $type);

            $this->obfuscate($type['name'], $data);


            foreach ($data as $field => $value) {
                $visitor->visitProperty(new StaticPropertyMetadata($type['name'], $field, $value), $value);
            }

            $event->stopPropagation();
        }
    }

    /**
     * @param string $type
     * @param array $data
     * @return void
     * @throws MappingException
     * @throws ReflectionException
     */
    private function obfuscate(string $type, array &$data)
    {
        $ignoredField = ['createdOn', 'updatedOn', 'lastUpdate', 'state', 'controlState', 'type', 'completionRate'];
        $emMetadata = $this->em->getMetadataFactory()->getMetadataFor($type);
        $fields = $emMetadata->getFieldNames();
        $associations = $emMetadata->getAssociationNames();

        foreach ($fields as $field) {
            if (in_array($field, $ignoredField)) {
                continue;
            }
            $data[$field] = Tools::obfuscateString($field);
        }
        foreach ($associations as $association) {
            if (isset($data[$association]) && $association === "attendee") {
                foreach ($data[$association] as $key => $field) {
                    if (!empty($field) && $field !== "") {
                        if (is_array($field)) {
                            $data[$association][$key] = [];
                        } else {
                            $data[$association][$key] = Tools::obfuscateString($field);
                        }

                    }
                }
            } else {
                $data[$association] = null;
            }
        }

        //Required link
        $data['_links'] = [
            'certification' => null,
            'session' => [
                'href' => null
            ]
        ];
        $data['attendeeLink'] = null;
        $data['permalink'] = null;
        $data['surveyLink'] = null;
        //Required trainingActionInfo
        $data['trainingActionInfo'] = [
            "title" => '****'
        ];
        $data['tags'] = [];
        $data['completionRate'] = null;
        $data['billingState'] = 'notBillable';
    }
}