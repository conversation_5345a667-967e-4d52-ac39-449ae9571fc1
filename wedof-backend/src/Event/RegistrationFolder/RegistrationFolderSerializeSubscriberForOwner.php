<?php
// src/Event/RegistrationFolder/RegistrationFolderSerializeSubscriberForOwner.php
namespace App\Event\RegistrationFolder;

use App\Entity\RegistrationFolder;
use App\Event\TagSerializeSubscriber;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\Tools;
use App\Service\DataProviders\BaseApiService;
use App\Service\PaymentService;
use Doctrine\ORM\NonUniqueResultException;
use JMS\Serializer\EventDispatcher\EventSubscriberInterface;
use J<PERSON>\Serializer\EventDispatcher\ObjectEvent;
use J<PERSON>\Serializer\JsonSerializationVisitor;
use J<PERSON>\Serializer\Metadata\StaticPropertyMetadata;

class RegistrationFolderSerializeSubscriberForOwner extends TagSerializeSubscriber implements EventSubscriberInterface
{
    private PaymentService $paymentService;

    public function __construct(PaymentService $paymentService)
    {
        $this->paymentService = $paymentService;
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return array(
            array(
                'event' => 'serializer.post_serialize',
                'method' => 'onPostSerialize',
                'class' => RegistrationFolder::class, // if no class, subscribe to every serialization,
                'format' => 'json'
            ),
        );
    }

    //-------------------
    // METHODES PUBLIQUES
    //-------------------
    /**
     * @param ObjectEvent $event
     * @throws NonUniqueResultException
     */
    public function onPostSerialize(ObjectEvent $event): void
    {
        if (in_array('owner', $event->getContext()->getAttribute('groups'))) {
            /** @var RegistrationFolder $registrationFolder */
            $registrationFolder = $event->getObject();
            /** @var JsonSerializationVisitor $visitor */
            $visitor = $event->getVisitor();

            $this->serializeTags($registrationFolder, $visitor);
            $this->serializeTrainingActionInfo($registrationFolder, $visitor);
        }
    }

    //-------------------
    // METHODES PRIVEES
    //-------------------

    /**
     * @param RegistrationFolder $registrationFolder
     * @param JsonSerializationVisitor $visitor
     * @throws NonUniqueResultException
     */
    private function serializeTrainingActionInfo(RegistrationFolder $registrationFolder, JsonSerializationVisitor $visitor): void
    {
        $trainingActionInfo = $registrationFolder->getRawData()['trainingActionInfo'];
        $trainingActionInfo['quitReason'] = $trainingActionInfo['reason'] ?? ($registrationFolder->getTerminatedReason() ? $registrationFolder->getTerminatedReason()->getCode() : null);
        unset($trainingActionInfo['reason']);
        $startDate = Tools::generateDateStartOrEndOfDay($trainingActionInfo['sessionStartDate']);
        $endDate = Tools::generateDateStartOrEndOfDay($trainingActionInfo['sessionEndDate']);
        $apiService = BaseApiService::getCatalogApiServiceByDataProvider(DataProviders::from($registrationFolder->getType()));
        $infos = array(
            'externalId' => $registrationFolder->getRawData()['trainingActionId'],
            'externalLink' => $apiService->getTrainingActionExternalLink($registrationFolder->getSession()->getTrainingAction()),
            'sessionId' => $apiService->getSessionExternalId($registrationFolder->getRawData()['trainingId'], $registrationFolder->getRawData()['trainingActionId'], $registrationFolder->getRawData()['trainingActionInfo']['sessionId']),
            'trainingActionId' => $apiService->getTrainingActionExternalId($registrationFolder->getRawData()['trainingId'], $registrationFolder->getRawData()['trainingActionId']),
            'trainingId' => $registrationFolder->getRawData()['trainingId'],
            'sessionEndDate' => $endDate ?: $trainingActionInfo['sessionEndDate'],
            'sessionStartDate' => $startDate ?: $trainingActionInfo['sessionStartDate'],
            'trainingCompletionRate' => $registrationFolder->getCompletionRate(), //tmp will be deprecated
            'dureeAbsenceOF' => $registrationFolder->getAbsenceDuration() ?? $registrationFolder->computeAbsenceDuration(),
            'uniteAbsenceOF' => $registrationFolder->getAbsenceDuration() ? 1 : ($trainingActionInfo['uniteAbsenceOF'] ?? 1)
        );
        $infos = Tools::multidimensionalArrayReplace($trainingActionInfo, $infos);
        $visitor->visitProperty(new StaticPropertyMetadata('', 'trainingActionInfo', null), $infos);
        $visitor->visitProperty(new StaticPropertyMetadata('', 'externalLink', null), $apiService->getRegistrationFolderExternalLink($registrationFolder));
        $visitor->visitProperty(new StaticPropertyMetadata('', 'paymentDate', null), $this->paymentService->getLastPaymentDateForRegistrationFolder($registrationFolder));
    }
}
