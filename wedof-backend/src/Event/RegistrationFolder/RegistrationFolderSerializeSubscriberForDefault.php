<?php

// src/Event/RegistrationFolder/RegistrationFolderSerializeSubscriberForDefault.php
namespace App\Event\RegistrationFolder;


use App\Entity\RegistrationFolder;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\Tools;
use App\Service\DataProviders\BaseApiService;
use J<PERSON>\Serializer\EventDispatcher\EventSubscriberInterface;
use <PERSON><PERSON>\Serializer\EventDispatcher\ObjectEvent;
use J<PERSON>\Serializer\JsonSerializationVisitor;
use <PERSON><PERSON>\Serializer\Metadata\StaticPropertyMetadata;

class RegistrationFolderSerializeSubscriberForDefault implements EventSubscriberInterface
{
    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return array(
            array(
                'event' => 'serializer.post_serialize',
                'method' => 'onPostSerialize',
                'class' => RegistrationFolder::class, // if no class, subscribe to every serialization,
                'format' => 'json'
            ),
        );
    }

    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    /**
     * @param ObjectEvent $event
     */
    public function onPostSerialize(ObjectEvent $event): void
    {
        if (in_array('Default', $event->getContext()->getAttribute('groups'))) {
            /** @var RegistrationFolder $registrationFolder */
            $registrationFolder = $event->getObject();
            /** @var JsonSerializationVisitor $visitor */
            $visitor = $event->getVisitor();

            $this->serializeTrainingActionInfoForCertifier($registrationFolder, $visitor);
        }
    }

    //-------------------
    // METHODES PRIVEES
    //-------------------

    /**
     * @param RegistrationFolder $registrationFolder
     * @param JsonSerializationVisitor $visitor
     */
    private function serializeTrainingActionInfoForCertifier(RegistrationFolder $registrationFolder, JsonSerializationVisitor $visitor): void
    {
        $trainingActionInfo = $registrationFolder->getRawData()['trainingActionInfo'];
        $startDate = Tools::generateDateStartOrEndOfDay($trainingActionInfo['sessionStartDate']);
        $endDate = Tools::generateDateStartOrEndOfDay($trainingActionInfo['sessionEndDate']);
        $dataProvider = DataProviders::from($registrationFolder->getType());
        $infos = array(
            'title' => $trainingActionInfo['title'],
            'externalLink' => BaseApiService::getCatalogApiServiceByDataProvider($dataProvider)->getTrainingActionExternalLink($registrationFolder->getSession()->getTrainingAction()),
            'sessionEndDate' => $endDate ?: $trainingActionInfo['sessionEndDate'],
            'sessionStartDate' => $startDate ?: $trainingActionInfo['sessionStartDate'],
            'indicativeDuration' => $trainingActionInfo['indicativeDuration'],
            'trainingCompletionRate' => $registrationFolder->getCompletionRate(), //tmp will be deprecated
            'quitReason' => $trainingActionInfo['reason'] ?? ($registrationFolder->getTerminatedReason() ? $registrationFolder->getTerminatedReason()->getCode() : null),
        );

        $visitor->visitProperty(new StaticPropertyMetadata('', 'trainingActionInfo', null), $infos);
    }
}