<?php

namespace App\Event\RegistrationFolder;

use App\Service\SubscriptionService;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;


class RegistrationFolderSubscriptionSubscriber implements EventSubscriberInterface
{
    private SubscriptionService $subscriptionService;

    public function __construct(SubscriptionService $subscriptionService)
    {
        $this->subscriptionService = $subscriptionService;
    }

    public static function getSubscribedEvents(): array
    {
        return ['registrationFolder.created' => 'foldersCount'];
    }

    /**
     * @param RegistrationFolderEvents $registrationFolderEvents
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function foldersCount(RegistrationFolderEvents $registrationFolderEvents): void
    {
        $registrationFolder = $registrationFolderEvents->getRegistrationFolder();
        $subscription = $registrationFolder->getOrganism()->getSubscription();
        if ($subscription && $subscription->getRegistrationFolderNumberPeriodStartDate()) {
            $this->subscriptionService->updateRegistrationFoldersNumberCount($subscription, $registrationFolder->getId());
        }
    }
}
