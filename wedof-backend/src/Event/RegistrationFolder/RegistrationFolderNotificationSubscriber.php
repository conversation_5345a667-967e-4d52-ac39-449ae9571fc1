<?php

namespace App\Event\RegistrationFolder;

use App\Library\utils\enums\RegistrationFolderStates;
use App\Service\NotificationService;
use Exception;
use <PERSON><PERSON>\Serializer\SerializationContext;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

class RegistrationFolderNotificationSubscriber implements EventSubscriberInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    private NotificationService $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    public static function getSubscribedEvents(): array
    {
        return array(
            RegistrationFolderEvents::CREATED => 'notificationOnRegistrationFolder',
            RegistrationFolderEvents::UPDATED => 'notificationOnRegistrationFolder',
        );
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    public function notificationOnRegistrationFolder(RegistrationFolderEvents $event, string $eventName): void
    {
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }

        try {
            $this->logger->debug("[Notification RegistrationFolder] " . self::class . " event $eventName");
            $organism = $event->getRegistrationFolder()->getOrganism();
            $eventNameToSearch = $eventName;
            // TODO : fix this mess !
            // There are 3 technical states for the same label in EDOF : "Annulé sans suite" : rejected, rejectedWithoutTitulaireSuite, rejectedWithoutCdcSuite
            // Nothing allows to know why they differ yet
            // Most are "rejectedWithoutTitulaireSuite"
            // We could either understand the difference
            // or make it disappear by storing only one state (ideally "rejected") & have the details somewhere else
            // but it may be dangerous (change it or not in the raw data as well?)
            // In the meantime, all of them are sent to the same Webhook, the only one that can be created : "rejectedWithoutTitulaireSuite"
            if (in_array($eventName, ['registrationFolder.' . RegistrationFolderStates::REJECTED()->getValue(), 'registrationFolder.' . RegistrationFolderStates::REJECTED_WITHOUT_CDC_SUITE()->getValue(), 'registrationFolder.' . RegistrationFolderStates::REJECTED_WITHOUT_OF_SUITE()->getValue()])) {
                $eventNameToSearch = 'registrationFolder.' . RegistrationFolderStates::REJECTED_WITHOUT_TITULAIRE_SUITE()->getValue();
            }
            $topics = [];
            //$topics = $this->notificationService->listByOrganismsAndEventAndType(new ArrayCollection([$organism]), $eventNameToSearch, []);
            if (sizeof($topics) > 0) {
                $serializationContext = new SerializationContext();
                $serializationContext->setSerializeNull(true);
                $serializationContext->setGroups("owner");
                $this->logger->debug("[Notification RegistrationFolder] " . sizeof($topics) . " to send");
                /** @var string $topic */
                //foreach ($topics as $topic) {
                //$message = new SendWebhook($webhook->getId(), $eventName, $payload, get_class($event->getRegistrationFolder()), $event->getRegistrationFolder()->getExternalId());
                //$this->messageBus->dispatch($message);
                //}
            } else {
                $this->logger->debug("[Notification RegistrationFolder] no event to send");
            }
        } catch (Exception $e) {
            $this->logger->error("[Error notificationOnRegistrationFolder " . $event->getRegistrationFolder()->getExternalId() . "] at Line " . $e->getLine() . " " . $e->getFile() . " " . $e->getMessage());
        }
    }
}