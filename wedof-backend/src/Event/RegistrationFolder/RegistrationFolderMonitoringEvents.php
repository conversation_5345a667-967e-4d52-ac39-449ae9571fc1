<?php

namespace App\Event\RegistrationFolder;

use App\Entity\RegistrationFolder;
use Symfony\Contracts\EventDispatcher\Event;

class RegistrationFolderMonitoringEvents extends Event
{
    public const NOT_VALIDATED = 'registrationFolderAlert.notValidated';
    public const NOT_ACCEPTED = 'registrationFolderAlert.notAccepted';
    public const NOT_IN_TRAINING = 'registrationFolderAlert.notInTraining';
    public const NOT_SERVICE_DONE_DECLARED = 'registrationFolderAlert.notServiceDoneDeclared';

    protected RegistrationFolder $registrationFolder;
    protected int $timeRemaining;

    public function __construct(RegistrationFolder $registrationFolder, int $timeRemaining)
    {
        $this->registrationFolder = $registrationFolder;
        $this->timeRemaining = $timeRemaining;
    }

    /**
     * @return RegistrationFolder
     */
    public function getRegistrationFolder(): RegistrationFolder
    {
        return $this->registrationFolder;
    }

    public function getTimeRemaining(): int
    {
        return $this->timeRemaining;
    }
}