<?php

namespace App\Event\RegistrationFolder;

use App\Entity\RegistrationFolder;
use Symfony\Contracts\EventDispatcher\Event;

class RegistrationFolderEvents extends Event
{
    public const CREATED = 'registrationFolder.created';
    public const UPDATED = 'registrationFolder.updated';
    public const PROPOSAL_APPLIED = 'registrationFolder.proposalApplied';

    //other events are mapped with RegistrationFolder.* -> RegistrationFolderStates
    // RegistrationFolderBilling.* -> RegistrationFolderBillingStates
    // RegistrationFolderAttendee.* -> RegistrationFolderAttendeeStates

    protected RegistrationFolder $registrationFolder;
    protected int $delay;

    /**
     * @param RegistrationFolder $registrationFolder
     * @param int $delay in milliseconds
     */
    public function __construct(RegistrationFolder $registrationFolder, int $delay = 0)
    {
        $this->registrationFolder = $registrationFolder;
        $this->delay = 0;
    }

    /**
     * @return RegistrationFolder
     */
    public function getRegistrationFolder(): RegistrationFolder
    {
        return $this->registrationFolder;
    }

    /**
     * @return int
     */
    public function getDelay(): int
    {
        return $this->delay;
    }
}
