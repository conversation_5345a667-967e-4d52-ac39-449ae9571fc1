<?php

namespace App\Event\RegistrationFolder;

use App\Entity\RegistrationFolderHistory;
use App\Library\utils\enums\ActivityTypes;
use App\Library\utils\enums\RegistrationFolderBillingStates;
use App\Library\utils\enums\RegistrationFolderStates;
use App\Service\ActivityService;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Exception;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Throwable;

class RegistrationFolderActivitySubscriber implements EventSubscriberInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    private ActivityService $activityService;

    public function __construct(ActivityService $activityService)
    {
        $this->activityService = $activityService;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    public static function getSubscribedEvents(): array
    {
        return ['registrationFolderBilling.' . RegistrationFolderBillingStates::TO_BILL()->getValue() => 'activityOnRegistrationFolder'];
    }

    /**
     * @param RegistrationFolderEvents $event
     * @param string $eventName
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function activityOnRegistrationFolder(RegistrationFolderEvents $event, string $eventName): void
    {
        try {
            $registrationFolder = $event->getRegistrationFolder();
            $stateDates = [];

            foreach (RegistrationFolderStates::valuesStatesToString() as $state) {
                $getMethodName = "get" . ucwords($state) . "Date";
                if (method_exists(RegistrationFolderHistory::class, $getMethodName)) {
                    $stateDates[] = $registrationFolder->getHistory()->{$getMethodName}();
                }
            }

            $this->activityService->create([
                'title' => "Le dossier de formation est facturable",
                'type' => ActivityTypes::UPDATE_STATE(),
                'eventTime' => max($stateDates),
                'field' => 'billingState',
                'newValue' => 'toBill'
            ], null, $registrationFolder);
        } catch (Exception $e) {
            $this->logger->error("[Error activityOnRegistrationFolder " . $event->getRegistrationFolder()->getExternalId() . "] at Line " . $e->getLine() . " " . $e->getFile() . " " . $e->getMessage());
        }
    }
}
