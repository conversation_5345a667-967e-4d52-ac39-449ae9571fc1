<?php
// src/Event/Training/TrainingSerializeSubscriber.php
namespace App\Event\Training;

use App\Entity\Training;
use App\Event\TagSerializeSubscriber;
use App\Library\utils\enums\TrainingActionStates;
use App\Repository\TrainingActionRepository;
use App\Service\TrainingService;
use <PERSON><PERSON>\Serializer\EventDispatcher\EventSubscriberInterface;
use JMS\Serializer\EventDispatcher\ObjectEvent;
use J<PERSON>\Serializer\JsonSerializationVisitor;
use J<PERSON>\Serializer\Metadata\StaticPropertyMetadata;

class TrainingSerializeSubscriber extends TagSerializeSubscriber implements EventSubscriberInterface
{
    private TrainingActionRepository $trainingActionRepository;
    private TrainingService $trainingService;

    public function __construct(TrainingActionRepository $trainingActionRepository, TrainingService $trainingService)
    {
        $this->trainingActionRepository = $trainingActionRepository;
        $this->trainingService = $trainingService;
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return array(
            array(
                'event' => 'serializer.post_serialize',
                'method' => 'onPostSerialize',
                'class' => Training::class, // if no class, subscribe to every serialization,
                'format' => 'json'
            ),
        );
    }

    /**
     * @param ObjectEvent $event
     */
    public function onPostSerialize(ObjectEvent $event): void
    {
        /** @var Training $training */
        $training = $event->getObject();
        /** @var JsonSerializationVisitor $visitor */
        $visitor = $event->getVisitor();

        $arrayTrainingsActionsExternalIdsOnline = [];
        $trainingActionsOnline = $this->trainingActionRepository->getExternalIdsByTraining($training, ['state' => TrainingActionStates::PUBLISHED()->getValue(), 'modality' => 'online']);
        foreach ($trainingActionsOnline as $trainingActionOnline) {
            $arrayTrainingsActionsExternalIdsOnline[] = $trainingActionOnline;
        }
        $onlineLink = $this->trainingService->getTrainingMcfLink(false, $trainingActionsOnline, $training);

        $arrayTrainingsActionsExternalIdsOnSite = [];
        $trainingActionsOnSite = $this->trainingActionRepository->getExternalIdsByTraining($training, ['state' => TrainingActionStates::PUBLISHED()->getValue(), 'modality' => 'onsite']);
        foreach ($trainingActionsOnSite as $trainingActionOnSite) {
            $arrayTrainingsActionsExternalIdsOnSite[] = $trainingActionOnSite;
        }
        $onsiteLink = $this->trainingService->getTrainingMcfLink(true, $trainingActionsOnSite, $training);

        $visitor->visitProperty(new StaticPropertyMetadata('', 'activeTrainingActions', null), [
            'online' => $arrayTrainingsActionsExternalIdsOnline,
            'onlineLink' => $onlineLink,
            'onsite' => $arrayTrainingsActionsExternalIdsOnSite,
            'onsiteLink' => $onsiteLink]);
        $visitor->visitProperty(new StaticPropertyMetadata('', 'catalog', null), "MCF");
        if (in_array('owner', $event->getContext()->getAttribute('groups'))) {
            $this->serializeTags($training, $visitor);
        }
    }
}
