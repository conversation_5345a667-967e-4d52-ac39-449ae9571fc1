<?php

namespace App\Message;

use App\Library\utils\enums\DataProviders;
use Doctrine\Common\Collections\ArrayCollection;

class StartBatchesRegistrationFolders
{
    private DataProviders $dataProvider;
    private ?string $siret;
    private int $skip;
    private int $limit;
    private bool $disableWedofEvents;
    private ?ArrayCollection $nextMessages;

    /**
     * @param DataProviders $dataProvider
     * @param string|null $siret
     * @param int $limit
     * @param int $skip
     * @param bool $disableWedofEvents
     * @param ArrayCollection|null $nextMessages
     */
    public function __construct(DataProviders $dataProvider, string $siret = null, int $limit = 100, int $skip = 0, bool $disableWedofEvents = true, ArrayCollection $nextMessages = null)
    {
        $this->dataProvider = $dataProvider;
        $this->siret = $siret;
        $this->skip = $skip;
        $this->limit = $limit;
        $this->nextMessages = $nextMessages;
        $this->disableWedofEvents = $disableWedofEvents;
    }

    /**
     * @return DataProviders
     */
    public function getDataProvider(): DataProviders
    {
        return $this->dataProvider;
    }

    /**
     * @return string
     */
    public function getSiret(): ?string
    {
        return $this->siret;
    }

    /**
     * @return int
     */
    public function getLimit(): int
    {
        return $this->limit;
    }

    /**
     * @return int
     */
    public function getSkip(): int
    {
        return $this->skip;
    }

    /**
     * @return bool
     */
    public function getDisableWedofEvents(): bool
    {
        return $this->disableWedofEvents;
    }

    /**
     * @return ArrayCollection|null
     */
    public function getNextMessages(): ?ArrayCollection
    {
        return $this->nextMessages;
    }
}
