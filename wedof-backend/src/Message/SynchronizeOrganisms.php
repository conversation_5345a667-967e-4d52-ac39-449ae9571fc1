<?php

namespace App\Message;

use Doctrine\Common\Collections\ArrayCollection;

class SynchronizeOrganisms
{
    private ?int $skip;
    private ?int $limit;
    private ?string $siret;
    private ?ArrayCollection $nextMessages;

    /**
     * @param ArrayCollection|null $nextMessages
     */
    public function __construct(int $skip = null, int $limit = null, string $siret = null, ArrayCollection $nextMessages = null)
    {
        $this->skip = $skip;
        $this->limit = $limit;
        $this->siret = $siret;
        $this->nextMessages = $nextMessages;
    }

    /**
     * @return int
     */
    public function getSkip(): ?int
    {
        return $this->skip;
    }

    /**
     * @return int
     */
    public function getLimit(): ?int
    {
        return $this->limit;
    }

    /**
     * @return string
     */
    public function getSiret(): ?string
    {
        return $this->siret;
    }

    /**
     * @return ArrayCollection|null
     */
    public function getNextMessages(): ?ArrayCollection
    {
        return $this->nextMessages;
    }
}
