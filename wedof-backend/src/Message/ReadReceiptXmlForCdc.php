<?php


namespace App\Message;


use Doctrine\Common\Collections\ArrayCollection;

class ReadReceiptXmlForCdc
{

    private int $idCdcFile;
    private array $passageById;
    private string $idTraitement;
    private ?ArrayCollection $nextMessages;

    public function __construct(int $idCdcFile, array $passageById, string $idTraitement, ArrayCollection $nextMessages = null)
    {
        $this->idCdcFile = $idCdcFile;
        $this->passageById = $passageById;
        $this->idTraitement = $idTraitement;
        $this->nextMessages = $nextMessages;
    }

    /**
     * @return int
     */
    public function getIdCdcFile(): int
    {
        return $this->idCdcFile;
    }

    /**
     * @return array
     */
    public function getPassageById(): array
    {
        return $this->passageById;
    }

    /**
     * @return string
     */
    public function getIdTraitement(): string
    {
        return $this->idTraitement;
    }

    /**
     * @return ArrayCollection|null
     */
    public function getNextMessages(): ?ArrayCollection
    {
        return $this->nextMessages;
    }
}
