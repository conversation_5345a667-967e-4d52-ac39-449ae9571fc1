<?php

namespace App\Message;

use App\Library\utils\enums\DataProviders;
use Doctrine\Common\Collections\ArrayCollection;

class SynchronizeRegistrationFolder extends MessageWithIpAddress
{
    private string $siret;
    private string $externalId;
    private ?DataProviders $dataProvider;
    private ?ArrayCollection $nextMessages;

    /**
     * @param string $ipAddress
     * @param string $externalId
     * @param string $siret
     * @param DataProviders|null $dataProvider
     * @param ArrayCollection|null $nextMessages
     */
    public function __construct(string $ipAddress, string $externalId, string $siret, DataProviders $dataProvider = null, ArrayCollection $nextMessages = null)
    {
        parent::__construct($ipAddress);
        $this->siret = $siret;
        $this->externalId = $externalId;
        $this->dataProvider = $dataProvider;
        $this->nextMessages = $nextMessages;
    }

    /**
     * @return ArrayCollection|null
     */
    public function getNextMessages(): ?ArrayCollection
    {
        return $this->nextMessages;
    }

    /**
     * @return string
     */
    public function getExternalId(): string
    {
        return $this->externalId;
    }

    /**
     * @return string
     */
    public function getSiret(): string
    {
        return $this->siret;
    }

    /**
     * @return DataProviders
     */
    public function getDataProvider(): DataProviders
    {
        return $this->dataProvider;
    }
}
