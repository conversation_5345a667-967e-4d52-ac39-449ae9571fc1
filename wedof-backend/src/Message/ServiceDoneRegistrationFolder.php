<?php

namespace App\Message;

use DateTime;
use Doctrine\Common\Collections\ArrayCollection;

class ServiceDoneRegistrationFolder extends MessageWithIpAddress
{
    private string $externalId;
    private ?float $absenceDuration;
    private ?bool $forceMajeureAbsence;
    private float $trainingDuration;
    private ?string $codeReason;
    private ?DateTime $terminatedDate;
    private ?ArrayCollection $nextMessages;
    private int $userId;

    /**
     * @param string $ipAddress
     * @param string $externalId
     * @param float|null $absenceDuration
     * @param bool $forceMajeureAbsence
     * @param string|null $codeReason
     * @param float $trainingDuration
     * @param DateTime|null $terminateDate
     * @param int|null $userId
     * @param ArrayCollection|null $nextMessages
     */
    public function __construct(string $ipAddress, string $externalId, ?float $absenceDuration, ?bool $forceMajeureAbsence, ?string $codeReason, float $trainingDuration, int $userId, ?DateTime $terminateDate, ArrayCollection $nextMessages = null)
    {
        parent::__construct($ipAddress);
        $this->externalId = $externalId;
        $this->absenceDuration = $absenceDuration;
        $this->forceMajeureAbsence = $forceMajeureAbsence;
        $this->codeReason = $codeReason;
        $this->trainingDuration = $trainingDuration;
        $this->nextMessages = $nextMessages;
        $this->userId = $userId;
        $this->terminatedDate = $terminateDate;
    }

    /**
     * @return ArrayCollection|null
     */
    public function getNextMessages(): ?ArrayCollection
    {
        return $this->nextMessages;
    }

    /**
     * @return string
     */
    public function getExternalId(): string
    {
        return $this->externalId;
    }

    /**
     * @return float
     */
    public function getAbsenceDuration(): ?float
    {
        return $this->absenceDuration;
    }

    /**
     * @return bool
     */
    public function isForceMajeureAbsence(): ?bool
    {
        return $this->forceMajeureAbsence;
    }

    /**
     * @return float
     */
    public function getTrainingDuration(): float
    {
        return $this->trainingDuration;
    }

    /**
     * @return string|null
     */
    public function getCodeReason(): ?string
    {
        return $this->codeReason;
    }

    /**
     * @return int
     */
    public function getUserId(): int
    {
        return $this->userId;
    }

    /**
     * @return DateTime|null
     */
    public function getTerminatedDate(): ?DateTime
    {
        return $this->terminatedDate;
    }
}
