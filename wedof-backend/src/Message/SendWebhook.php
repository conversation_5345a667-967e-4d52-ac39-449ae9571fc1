<?php

namespace App\Message;

use Doctrine\Common\Collections\ArrayCollection;

class SendWebhook
{

    private int $webhookId;
    private string $eventName;
    private string $payload;
    private string $entityClass;
    private string $entityId;
    private string $guid;
    private ?int $authorId;
    private ?ArrayCollection $nextMessages;

    /**
     * @param int $webhookId
     * @param string $eventName
     * @param string $payload
     * @param string $entityClass
     * @param string $entityId
     * @param int|null $authorId
     * @param ArrayCollection|null $nextMessages
     */
    public function __construct(int $webhookId, string $eventName, string $payload, string $entityClass, string $entityId, int $authorId = null, ArrayCollection $nextMessages = null)
    {
        $this->webhookId = $webhookId;
        $this->eventName = $eventName;
        $this->payload = $payload;
        $entityClass = explode('\\', $entityClass);
        $this->entityClass = end($entityClass);
        $this->entityId = $entityId;
        $this->guid = hash_hmac("sha256", $payload, $webhookId . $eventName);
        $this->nextMessages = $nextMessages;
        $this->authorId = $authorId;
    }


    /**
     * @return int
     */
    public function getWebhookId(): int
    {
        return $this->webhookId;
    }

    /**
     * @return string
     */
    public function getEventName(): string
    {
        return $this->eventName;
    }

    /**
     * @return string
     */
    public function getPayload(): string
    {
        return $this->payload;
    }

    /**
     * @return string
     */
    public function getGuid(): string
    {
        return $this->guid;
    }


    /**
     * @return string
     */
    public function getEntityClass(): string
    {
        return $this->entityClass;
    }

    /**
     * @return string
     */
    public function getEntityId(): string
    {
        return $this->entityId;
    }

    /**
     * @return ArrayCollection|null
     */
    public function getNextMessages(): ?ArrayCollection
    {
        return $this->nextMessages;
    }

    /**
     * @return int|null
     */
    public function getAuthorId(): ?int
    {
        return $this->authorId;
    }
}
