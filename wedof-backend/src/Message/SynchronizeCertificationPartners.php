<?php

namespace App\Message;

use App\Library\utils\enums\DataProviders;
use Doctrine\Common\Collections\ArrayCollection;

class SynchronizeCertificationPartners
{
    private string $certifInfo;
    private string $siret;
    private ?int $retryCounter;
    private DataProviders $dataProvider;
    private ?ArrayCollection $nextMessages;

    /**
     * @param string $certifInfo
     * @param string $siret
     * @param DataProviders $dataProvider
     * @param int|null $retryCounter
     * @param ArrayCollection|null $nextMessages
     */
    public function __construct(string $certifInfo, string $siret, DataProviders $dataProvider, ?int $retryCounter, ArrayCollection $nextMessages = null)
    {
        $this->certifInfo = $certifInfo;
        $this->siret = $siret;
        $this->dataProvider = $dataProvider;
        $this->retryCounter = $retryCounter;
        $this->nextMessages = $nextMessages;
    }

    /**
     * @return string
     */
    public function getCertifInfo(): string
    {
        return $this->certifInfo;
    }

    /**
     * @return string
     */
    public function getSiret(): string
    {
        return $this->siret;
    }

    /**
     * @return DataProviders
     */
    public function getDataProvider(): DataProviders
    {
        return $this->dataProvider;
    }

    /**
     * @return int|null
     */
    public function getRetryCounter(): ?int
    {
        return $this->retryCounter;
    }

    /**
     * @return ArrayCollection|null
     */
    public function getNextMessages(): ?ArrayCollection
    {
        return $this->nextMessages;
    }
}