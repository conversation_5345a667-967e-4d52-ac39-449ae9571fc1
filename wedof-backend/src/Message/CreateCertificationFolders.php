<?php

namespace App\Message;

use Doctrine\Common\Collections\ArrayCollection;

class CreateCertificationFolders
{
    private string $siret;
    private int $certificationId;
    private ?ArrayCollection $nextMessages;

    /**
     * @param string $siret
     * @param int $certificationId
     * @param ArrayCollection|null $nextMessages
     */
    public function __construct(string $siret, int $certificationId, ArrayCollection $nextMessages = null)
    {
        $this->siret = $siret;
        $this->certificationId = $certificationId;
        $this->nextMessages = $nextMessages;
    }

    /**
     * @return string
     */
    public function getSiret(): string
    {
        return $this->siret;
    }

    /**
     * @return string
     */
    public function getCertificationId(): string
    {
        return $this->certificationId;
    }

    /**
     * @return ArrayCollection|null
     */
    public function getNextMessages(): ?ArrayCollection
    {
        return $this->nextMessages;
    }
}
