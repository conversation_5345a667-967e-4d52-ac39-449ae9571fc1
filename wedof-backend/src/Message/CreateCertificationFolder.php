<?php

namespace App\Message;

use Doctrine\Common\Collections\ArrayCollection;

class CreateCertificationFolder
{
    private string $externalId;
    private ?ArrayCollection $nextMessages;

    /**
     * @param string $externalId
     * @param ArrayCollection|null $nextMessages
     */
    public function __construct(string $externalId, ArrayCollection $nextMessages = null)
    {
        $this->externalId = $externalId;
        $this->nextMessages = $nextMessages;
    }

    /**
     * @return string
     */
    public function getExternalId(): string
    {
        return $this->externalId;
    }

    /**
     * @return ArrayCollection|null
     */
    public function getNextMessages(): ?ArrayCollection
    {
        return $this->nextMessages;
    }
}
