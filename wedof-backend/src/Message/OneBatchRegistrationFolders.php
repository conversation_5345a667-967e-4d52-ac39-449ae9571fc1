<?php

namespace App\Message;

use App\Library\utils\enums\DataProviders;
use Doctrine\Common\Collections\ArrayCollection;

class OneBatchRegistrationFolders
{
    private DataProviders $dataProvider;
    private ?string $siret;
    private int $skip;
    private int $limit;
    private int $retry;
    private bool $followRateLimit;
    private int $cachedTotalRegistrationFolders;
    private bool $disableWedofEvents;
    private ?ArrayCollection $nextMessages;

    /**
     * @param DataProviders $dataProvider
     * @param string|null $siret
     * @param int $limit
     * @param int $skip
     * @param int $cachedTotalRegistrationFolders
     * @param bool $disableWedofEvents
     * @param bool $followRateLimit
     * @param int $retry
     * @param ArrayCollection|null $nextMessages
     */
    public function __construct(DataProviders $dataProvider, string $siret = null, int $limit = 100, int $skip = 0, int $cachedTotalRegistrationFolders = 0, bool $disableWedofEvents = true, bool $followRateLimit = true, int $retry = 0, ArrayCollection $nextMessages = null)
    {
        $this->dataProvider = $dataProvider;
        $this->siret = $siret;
        $this->skip = $skip;
        $this->limit = $limit;
        $this->cachedTotalRegistrationFolders = $cachedTotalRegistrationFolders;
        $this->disableWedofEvents = $disableWedofEvents;
        $this->nextMessages = $nextMessages;
        $this->followRateLimit = $followRateLimit;
        $this->retry = $retry;
    }

    /**
     * @return DataProviders
     */
    public function getDataProvider(): DataProviders
    {
        return $this->dataProvider;
    }

    /**
     * @return string
     */
    public function getSiret(): ?string
    {
        return $this->siret;
    }

    /**
     * @return int
     */
    public function getLimit(): int
    {
        return $this->limit;
    }

    /**
     * @return int
     */
    public function getSkip(): int
    {
        return $this->skip;
    }

    /**
     * @return bool
     */
    public function getDisableWedofEvents(): bool
    {
        return $this->disableWedofEvents;
    }

    /**
     * @return int
     */
    public function getCachedTotalRegistrationFolders(): int
    {
        return $this->cachedTotalRegistrationFolders;
    }

    /**
     * @return int
     */
    public function getRetry(): int
    {
        return $this->retry;
    }

    /**
     * @return bool
     */
    public function getFollowRateLimit(): bool
    {
        return $this->followRateLimit;
    }

    /**
     * @return ArrayCollection|null
     */
    public function getNextMessages(): ?ArrayCollection
    {
        return $this->nextMessages;
    }
}
