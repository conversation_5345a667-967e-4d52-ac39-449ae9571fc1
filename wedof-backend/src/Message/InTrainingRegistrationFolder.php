<?php

namespace App\Message;

use DateTime;
use Doctrine\Common\Collections\ArrayCollection;

class InTrainingRegistrationFolder extends MessageWithIpAddress
{
    private string $externalId;
    private int $userId;
    private ?DateTime $inTrainingDate;
    private ?ArrayCollection $nextMessages;

    public function __construct(string $ipAddress, string $externalId, ?DateTime $inTrainingDate, int $userId, ArrayCollection $nextMessages = null)
    {
        parent::__construct($ipAddress);
        $this->externalId = $externalId;
        $this->inTrainingDate = $inTrainingDate;
        $this->userId = $userId;
        $this->nextMessages = $nextMessages;
    }

    /**
     * @return string
     */
    public function getExternalId(): string
    {
        return $this->externalId;
    }

    /**
     * @return DateTime|null
     */
    public function getInTrainingDate(): ?DateTime
    {
        return $this->inTrainingDate;
    }

    /**
     * @return int
     */
    public function getUserId(): int
    {
        return $this->userId;
    }

    /**
     * @return ArrayCollection|null
     */
    public function getNextMessages(): ?ArrayCollection
    {
        return $this->nextMessages;
    }
}