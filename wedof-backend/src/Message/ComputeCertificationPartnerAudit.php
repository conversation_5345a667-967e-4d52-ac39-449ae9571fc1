<?php

namespace App\Message;

use Doctrine\Common\Collections\ArrayCollection;

class ComputeCertificationPartnerAudit
{
    private int $certificationPartnerAuditId;
    private int $userId;
    private bool $complete;
    private bool $updateCompliance;
    private bool $suspend;
    private bool $hasValidCatalog = true;
    private ?ArrayCollection $nextMessages;

    public function __construct(int $certificationPartnerAuditId, int $userId, bool $complete, bool $updateCompliance, bool $suspend, ArrayCollection $nextMessages = null)
    {
        $this->certificationPartnerAuditId = $certificationPartnerAuditId;
        $this->userId = $userId;
        $this->complete = $complete;
        $this->updateCompliance = $updateCompliance;
        $this->suspend = $suspend;
        $this->nextMessages = $nextMessages;
    }

    /**
     * @return int
     */
    public function getCertificationPartnerAuditId(): int
    {
        return $this->certificationPartnerAuditId;
    }

    /**
     * @return int
     */
    public function getUserId(): int
    {
        return $this->userId;
    }

    /**
     * @return bool
     */
    public function getComplete(): bool
    {
        return $this->complete;
    }

    /**
     * @return bool
     */
    public function getUpdateCompliance(): bool
    {
        return $this->updateCompliance;
    }

    /**
     * @return bool
     */
    public function getSuspend(): bool
    {
        return $this->suspend;
    }

    /**
     * @return bool
     */
    public function hasValidCatalog(): bool
    {
        return $this->hasValidCatalog;
    }

    /**
     * @param bool $hasValidCatalog
     * @return void
     */
    public function setHasValidCatalog(bool $hasValidCatalog): void
    {
        $this->hasValidCatalog = $hasValidCatalog;
    }

    /**
     * @return ArrayCollection|null
     */
    public function getNextMessages(): ?ArrayCollection
    {
        return $this->nextMessages;
    }
}