<?php

namespace App\Message;

use App\Library\utils\enums\DataProviders;
use Doctrine\Common\Collections\ArrayCollection;

class SynchronizeEvaluations
{
    private string $siret;
    private DataProviders $dataProvider;
    private ?ArrayCollection $nextMessages;

    /**
     * @param string $siret
     * @param DataProviders $dataProvider
     * @param ArrayCollection|null $nextMessages
     */
    public function __construct(string $siret, DataProviders $dataProvider, ArrayCollection $nextMessages = null)
    {
        $this->siret = $siret;
        $this->dataProvider = $dataProvider;
        $this->nextMessages = $nextMessages;

    }

    /**
     * @return string
     */
    public function getSiret(): string
    {
        return $this->siret;
    }

    /**
     * @return DataProviders
     */
    public function getDataProvider(): DataProviders
    {
        return $this->dataProvider;
    }

    /**
     * @return ArrayCollection|null
     */
    public function getNextMessages(): ?ArrayCollection
    {
        return $this->nextMessages;
    }
}
