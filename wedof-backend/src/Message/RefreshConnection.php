<?php

namespace App\Message;

use DateTimeInterface;
use Doctrine\Common\Collections\ArrayCollection;

class RefreshConnection
{
    private int $idConnection;
    private ?ArrayCollection $nextMessages;

    /**
     * @param int $idConnection
     * @param ArrayCollection|null $nextMessages
     */
    public function __construct(int $idConnection, ArrayCollection $nextMessages = null)
    {
        $this->idConnection = $idConnection;
        $this->nextMessages = $nextMessages;
    }

    /**
     * @return int
     */
    public function getIdConnection(): int
    {
        return $this->idConnection;
    }

    /**
     * @return ArrayCollection|null
     */
    public function getNextMessages(): ?ArrayCollection
    {
        return $this->nextMessages;
    }
}