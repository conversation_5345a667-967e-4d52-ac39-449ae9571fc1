<?php

namespace App\Message;

use DateTime;
use Doctrine\Common\Collections\ArrayCollection;

class BillRegistrationFolder extends MessageWithIpAddress
{
    private string $billNumber;
    private ?float $vatRate;
    private ?ArrayCollection $nextMessages;
    private string $externalId;

    /**
     * @param string $ipAddress
     * @param string $externalId
     * @param string $billNumber
     * @param float|null $vatRate
     * @param ArrayCollection|null $nextMessages
     */
    public function __construct(string $ipAddress, string $externalId, string $billNumber, float $vatRate = null, ArrayCollection $nextMessages = null)
    {
        parent::__construct($ipAddress);
        $this->externalId = $externalId;
        $this->billNumber = $billNumber;
        $this->vatRate = $vatRate;
        $this->nextMessages = $nextMessages;

    }

    /**
     * @return ArrayCollection|null
     */
    public function getNextMessages(): ?ArrayCollection
    {
        return $this->nextMessages;
    }

    /**
     * @return string
     */
    public function getExternalId(): string
    {
        return $this->externalId;
    }

    /**
     * @return string
     */
    public function getBillNumber(): string
    {
        return $this->billNumber;
    }

    /**
     * @return float
     */
    public function getVatRate(): ?float
    {
        return $this->vatRate;
    }


}
