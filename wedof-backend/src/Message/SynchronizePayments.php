<?php

namespace App\Message;

use App\Library\utils\enums\DataProviders;
use App\Library\utils\enums\PaymentSortParams;
use Doctrine\Common\Collections\ArrayCollection;

class SynchronizePayments
{
    private string $siret;
    private DataProviders $dataProvider;
    private ?string $state;
    private ?PaymentSortParams $sort;
    private ?string $registrationFolderExternalId;
    private ?ArrayCollection $nextMessages;

    /**
     * @param string $siret
     * @param DataProviders $dataProvider
     * @param string|null $state
     * @param PaymentSortParams|null $sort
     * @param string|null $registrationFolderExternalId
     * @param ArrayCollection|null $nextMessages
     */
    public function __construct(string $siret, DataProviders $dataProvider, string $state = null, PaymentSortParams $sort = null, string $registrationFolderExternalId = null, ArrayCollection $nextMessages = null)
    {
        $this->siret = $siret;
        $this->dataProvider = $dataProvider;
        $this->state = $state;
        $this->sort = $sort;
        $this->registrationFolderExternalId = $registrationFolderExternalId;
        $this->nextMessages = $nextMessages;
    }

    /**
     * @return string
     */
    public function getSiret(): string
    {
        return $this->siret;
    }

    /**
     * @return DataProviders
     */
    public function getDataProvider(): DataProviders
    {
        return $this->dataProvider;
    }

    /**
     * @return string|null
     */
    public function getState(): ?string
    {
        return $this->state;
    }

    /**
     * @return PaymentSortParams|null
     */
    public function getSort(): ?PaymentSortParams
    {
        return $this->sort;
    }

    /**
     * @return string|null
     */
    public function getRegistrationFolderExternalId(): ?string
    {
        return $this->registrationFolderExternalId;
    }

    /**
     * @return ArrayCollection|null
     */
    public function getNextMessages(): ?ArrayCollection
    {
        return $this->nextMessages;
    }
}
