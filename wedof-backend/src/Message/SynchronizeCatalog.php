<?php

namespace App\Message;

use App\Library\utils\enums\DataProviders;
use Doctrine\Common\Collections\ArrayCollection;

class SynchronizeCatalog
{
    private string $siret;
    private DataProviders $dataProvider;
    private ?array $trainingStatuses;
    private array $certificationCodes;
    private bool $useOpenData;
    private array $options;
    private ?ArrayCollection $nextMessages;

    /**
     * @param string $siret
     * @param DataProviders $dataProvider
     * @param array|null $trainingStatuses
     * @param array|null $certificationCodes
     * @param bool $useOpenData
     * @param array $options
     * @param ArrayCollection|null $nextMessages
     */
    public function __construct(string $siret, DataProviders $dataProvider, array $trainingStatuses = null, ?array $certificationCodes = [], bool $useOpenData = true, array $options = [], ArrayCollection $nextMessages = null)
    {
        $this->siret = $siret;
        $this->dataProvider = $dataProvider;
        $this->certificationCodes = [];
        if ($certificationCodes) {
            $array = [];
            foreach ($certificationCodes as $certificationId) {
                $array[] = strtolower($certificationId);
            }
            $this->certificationCodes = $array;
        }
        $this->trainingStatuses = $trainingStatuses;
        $this->useOpenData = $useOpenData;
        $this->options = $options;
        $this->nextMessages = $nextMessages;
    }

    /**
     * @return string
     */
    public function getSiret(): string
    {
        return $this->siret;
    }

    /**
     * @return DataProviders
     */
    public function getDataProvider(): DataProviders
    {
        return $this->dataProvider;
    }

    /**
     * @return array|null
     */
    public function getCertificationCodes(): array
    {
        return $this->certificationCodes;
    }

    /**
     * @return array|int[]|null
     */
    public function getTrainingStatuses(): ?array
    {
        return $this->trainingStatuses;
    }

    /**
     * @return bool
     */
    public function useOpenData(): bool
    {
        return $this->useOpenData;
    }

    /**
     * @return array
     */
    public function getOptions(): array
    {
        return $this->options;
    }

    /**
     * @return ArrayCollection|null
     */
    public function getNextMessages(): ?ArrayCollection
    {
        return $this->nextMessages;
    }
}
