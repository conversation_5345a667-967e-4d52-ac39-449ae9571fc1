<?php

namespace App\Message;

use Doctrine\Common\Collections\ArrayCollection;

class SynchronizePartners
{
    private string $certifInfo;
    private ?ArrayCollection $nextMessages;

    /**
     * @param ArrayCollection|null $nextMessages
     */
    public function __construct(string $certifInfo, ArrayCollection $nextMessages = null)
    {
        $this->certifInfo = $certifInfo;
        $this->nextMessages = $nextMessages;
    }

    /**
     * @return string
     */
    public function getCertifInfo(): string
    {
        return $this->certifInfo;
    }

    /**
     * @return ArrayCollection|null
     */
    public function getNextMessages(): ?ArrayCollection
    {
        return $this->nextMessages;
    }
}