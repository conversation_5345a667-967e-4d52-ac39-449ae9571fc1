<?php

namespace App\Message;

use Doctrine\Common\Collections\ArrayCollection;

class GenerateCpfCatalogXML
{
    private int $userId;
    private ?ArrayCollection $nextMessages;

    public function __construct(int $userId, ArrayCollection $nextMessages = null)
    {
        $this->userId = $userId;
        $this->nextMessages = $nextMessages;
    }

    /**
     * @return string
     */
    public function getUserId(): string
    {
        return $this->userId;
    }

    /**
     * @return ArrayCollection|null
     */
    public function getNextMessages(): ?ArrayCollection
    {
        return $this->nextMessages;
    }
}