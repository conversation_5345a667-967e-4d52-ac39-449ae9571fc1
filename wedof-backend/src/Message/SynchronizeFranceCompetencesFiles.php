<?php

namespace App\Message;

use App\Library\utils\enums\CertificationTypes;
use Doctrine\Common\Collections\ArrayCollection;

class SynchronizeFranceCompetencesFiles
{
    private CertificationTypes $type;
    private ?ArrayCollection $nextMessages;

    /**
     * @param ArrayCollection|null $nextMessages
     */
    public function __construct(CertificationTypes $type, ArrayCollection $nextMessages = null)
    {
        $this->type = $type;
        $this->nextMessages = $nextMessages;

    }

    /**
     * @return CertificationTypes
     */
    public function getType(): CertificationTypes
    {
        return $this->type;
    }

    /**
     * @return ArrayCollection|null
     */
    public function getNextMessages(): ?ArrayCollection
    {
        return $this->nextMessages;
    }
}
