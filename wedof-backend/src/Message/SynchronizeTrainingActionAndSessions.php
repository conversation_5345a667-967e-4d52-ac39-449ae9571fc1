<?php

namespace App\Message;

use App\Library\utils\enums\DataProviders;
use Doctrine\Common\Collections\ArrayCollection;

class SynchronizeTrainingActionAndSessions
{
    private int $trainingId;
    private string $trainingActionExternalId;
    private DataProviders $dataProvider;
    private array $trainingActionRawData;
    private array $options;
    private bool $isFullOrganismRefresh;
    private ?ArrayCollection $nextMessages;

    /**
     * @param int $trainingId
     * @param string $trainingActionExternalId
     * @param DataProviders $dataProvider
     * @param array|null $trainingActionRawData
     * @param array $options
     * @param bool $isFullOrganismRefresh
     * @param ArrayCollection|null $nextMessages
     */
    public function __construct(int $trainingId, string $trainingActionExternalId, DataProviders $dataProvider, array $trainingActionRawData, array $options = [], bool $isFullOrganismRefresh = false, ArrayCollection $nextMessages = null)
    {
        $this->trainingId = $trainingId;
        $this->trainingActionExternalId = $trainingActionExternalId;
        $this->dataProvider = $dataProvider;
        $this->trainingActionRawData = $trainingActionRawData;
        $this->options = $options;
        $this->isFullOrganismRefresh = $isFullOrganismRefresh;
        $this->nextMessages = $nextMessages;
    }

    /**
     * @return int
     */
    public function getTrainingId(): int
    {
        return $this->trainingId;
    }

    /**
     * @return string
     */
    public function getTrainingActionExternalId(): string
    {
        return $this->trainingActionExternalId;
    }

    /**
     * @return DataProviders
     */
    public function getDataProvider(): DataProviders
    {
        return $this->dataProvider;
    }

    /**
     * @return array
     */
    public function getTrainingActionRawData(): array
    {
        return $this->trainingActionRawData;
    }

    /**
     * @return array
     */
    public function getOptions(): array
    {
        return $this->options;
    }

    /**
     * @return bool
     */
    public function getIsFullOrganismRefresh(): bool
    {
        return $this->isFullOrganismRefresh;
    }

    /**
     * @return ArrayCollection|null
     */
    public function getNextMessages(): ?ArrayCollection
    {
        return $this->nextMessages;
    }

}
