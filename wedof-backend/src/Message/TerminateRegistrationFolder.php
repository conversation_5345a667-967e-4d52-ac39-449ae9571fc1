<?php

namespace App\Message;

use DateTime;
use Doctrine\Common\Collections\ArrayCollection;

class TerminateRegistrationFolder extends MessageWithIpAddress
{
    private ?string $codeReason;
    private ?DateTime $terminatedDate;
    private string $externalId;
    private ?float $absenceDuration;
    private ?ArrayCollection $nextMessages;
    private int $userId;

    /**
     * @param string $ipAddress
     * @param string $externalId
     * @param string|null $codeReason
     * @param DateTime|null $terminatedDate
     * @param float|null $absenceDuration
     * @param int $userId
     * @param ArrayCollection|null $nextMessages
     */
    public function __construct(string $ipAddress, string $externalId, ?string $codeReason, ?DateTime $terminatedDate, ?float $absenceDuration, int $userId, ArrayCollection $nextMessages = null)
    {
        parent::__construct($ipAddress);
        $this->externalId = $externalId;
        $this->codeReason = $codeReason;
        $this->terminatedDate = $terminatedDate;
        $this->absenceDuration = $absenceDuration;
        $this->nextMessages = $nextMessages;
        $this->userId = $userId;
    }

    /**
     * @return ArrayCollection|null
     */
    public function getNextMessages(): ?ArrayCollection
    {
        return $this->nextMessages;
    }

    /**
     * @return string
     */
    public function getExternalId(): string
    {
        return $this->externalId;
    }

    /**
     * @return string|null
     */
    public function getCodeReason(): ?string
    {
        return $this->codeReason;
    }

    /**
     * @return DateTime|null
     */
    public function getTerminatedDate(): ?DateTime
    {
        return $this->terminatedDate;
    }

    /**
     * @return int|null
     */
    public function getAbsenceDuration(): ?int
    {
        return $this->absenceDuration;
    }

    /**
     * @return int
     */
    public function getUserId(): int
    {
        return $this->userId;
    }
}
