<?php

namespace App\Message;

use App\Entity\Organism;
use App\Library\utils\enums\DataProviders;
use Doctrine\Common\Collections\ArrayCollection;

class MonitorRegistrationFolders
{
    private ?ArrayCollection $nextMessages;
    private string $siret;


    /**
     * @param ArrayCollection|null $nextMessages
     */
    public function __construct(Organism $organism, ArrayCollection $nextMessages = null)
    {
        $this->siret = $organism->getSiret();
        $this->nextMessages = $nextMessages;
    }

    /**
     * @return ArrayCollection|null
     */
    public function getNextMessages(): ?ArrayCollection
    {
        return $this->nextMessages;
    }

    /**
     * @return Organism
     */
    public function getSiret(): string
    {
        return $this->siret;
    }
}
