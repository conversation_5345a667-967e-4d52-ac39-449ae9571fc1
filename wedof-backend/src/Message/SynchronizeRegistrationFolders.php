<?php

namespace App\Message;

use App\Library\utils\enums\DataProviders;
use App\Library\utils\enums\RegistrationFolderSortParams;
use App\Library\utils\enums\RegistrationFolderStates;
use Doctrine\Common\Collections\ArrayCollection;

class SynchronizeRegistrationFolders
{
    private DataProviders $dataProvider;
    private string $siret;
    private RegistrationFolderStates $state;
    private RegistrationFolderSortParams $sort;
    private int $skip;
    private int $limit;
    private string $order;
    private bool $autoDiscover;
    private ?int $totalFoldersToInitialize;
    private ?ArrayCollection $nextMessages;

    /**
     * @param string $siret
     * @param DataProviders $dataProvider
     * @param RegistrationFolderStates|null $state
     * @param RegistrationFolderSortParams|null $sort
     * @param int $limit
     * @param int $skip
     * @param string $order
     * @param bool $autoDiscover
     * @param int|null $totalFoldersToInitialize
     * @param ArrayCollection|null $nextMessages
     */
    public function __construct(string $siret, DataProviders $dataProvider, RegistrationFolderStates $state = null, RegistrationFolderSortParams $sort = null, int $limit = 20, int $skip = 0, string $order = 'DESC', bool $autoDiscover = true, int $totalFoldersToInitialize = null, ArrayCollection $nextMessages = null)
    {
        $this->dataProvider = $dataProvider;
        $this->siret = $siret;
        $this->state = $state ?? RegistrationFolderStates::ALL();
        $this->sort = $sort ?? RegistrationFolderSortParams::DATE_DERNIERE_ACTION();
        $this->skip = $skip;
        $this->limit = $limit;
        $this->order = $order;
        $this->autoDiscover = $autoDiscover;
        $this->totalFoldersToInitialize = $totalFoldersToInitialize;
        $this->nextMessages = $nextMessages;
    }

    /**
     * @return bool
     */
    public function isAutoDiscover(): bool
    {
        return $this->autoDiscover;
    }

    /**
     * @return DataProviders
     */
    public function getDataProvider(): DataProviders
    {
        return $this->dataProvider;
    }

    /**
     * @return int|null
     */
    public function getTotalFoldersToInitialize(): ?int
    {
        return $this->totalFoldersToInitialize;
    }

    /**
     * @return string
     */
    public function getOrder(): string
    {
        return $this->order;
    }

    /**
     * @return int
     */
    public function getLimit(): int
    {
        return $this->limit;
    }

    /**
     * @return int
     */
    public function getSkip(): int
    {
        return $this->skip;
    }

    /**
     * @return string
     */
    public function getSiret(): string
    {
        return $this->siret;
    }

    /**
     * @return RegistrationFolderStates
     */
    public function getState(): RegistrationFolderStates
    {
        return $this->state;
    }

    /**
     * @return RegistrationFolderSortParams
     */
    public function getSort(): RegistrationFolderSortParams
    {
        return $this->sort;
    }

    /**
     * @return ArrayCollection|null
     */
    public function getNextMessages(): ?ArrayCollection
    {
        return $this->nextMessages;
    }
}
