<?php

namespace App\Message;

use Doctrine\Common\Collections\ArrayCollection;

class PropagateSkillSetsFromTraining
{
    private int $trainingId;
    private ?ArrayCollection $nextMessages;

    public function __construct(int $trainingId, ArrayCollection $nextMessages = null)
    {
        $this->trainingId = $trainingId;
        $this->nextMessages = $nextMessages;
    }

    /**
     * @return int
     */
    public function getTrainingId(): int
    {
        return $this->trainingId;
    }

    /**
     * @return ArrayCollection
     */
    public function getNextMessages(): ArrayCollection
    {
        return $this->nextMessages;
    }
}