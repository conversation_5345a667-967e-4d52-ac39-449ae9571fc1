<?php

namespace App\Message;

use Doctrine\Common\Collections\ArrayCollection;

class SynchronizeCatalogForAudit
{
    private ComputeCertificationPartnerAudit $computeCertificationPartnerAudit;
    private string $siret;
    private string $certificationCode;
    private ?ArrayCollection $nextMessages;

    /**
     * @param ComputeCertificationPartnerAudit $computeCertificationPartnerAudit
     * @param string $siret
     * @param string $certificationCode
     * @param ArrayCollection|null $nextMessages
     */
    public function __construct(ComputeCertificationPartnerAudit $computeCertificationPartnerAudit, string $siret, string $certificationCode, ArrayCollection $nextMessages = null)
    {
        $this->computeCertificationPartnerAudit = $computeCertificationPartnerAudit;
        $this->siret = $siret;
        $this->certificationCode = $certificationCode;
        $this->nextMessages = $nextMessages;
    }

    /**
     * @return ComputeCertificationPartnerAudit
     */
    public function getComputeCertificationPartnerAudit(): ComputeCertificationPartnerAudit
    {
        return $this->computeCertificationPartnerAudit;
    }

    /**
     * @return string
     */
    public function getSiret(): string
    {
        return $this->siret;
    }

    /**
     * @return string
     */
    public function getCertificationCode(): string
    {
        return $this->certificationCode;
    }

    /**
     * @return ArrayCollection|null
     */
    public function getNextMessages(): ?ArrayCollection
    {
        return $this->nextMessages;
    }
}