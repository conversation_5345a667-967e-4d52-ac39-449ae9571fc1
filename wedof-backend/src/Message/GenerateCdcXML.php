<?php

namespace App\Message;

use Doctrine\Common\Collections\ArrayCollection;

class GenerateCdcXML
{
    private string $userEmail;
    private bool $isAutomatedGeneration;
    private ?int $idCertif;
    private ?ArrayCollection $nextMessages;

    public function __construct(string $userEmail, bool $isAutomatedGeneration, int $idCertif = null, ArrayCollection $nextMessages = null)
    {
        $this->userEmail = $userEmail;
        $this->nextMessages = $nextMessages;
        $this->idCertif = $idCertif;
        $this->isAutomatedGeneration = $isAutomatedGeneration;
    }

    /**
     * @return string
     */
    public function getUserEmail(): string
    {
        return $this->userEmail;
    }

    /**
     * @return bool
     */
    public function isIsAutomatedGeneration(): bool
    {
        return $this->isAutomatedGeneration;
    }

    /**
     * @return int|null
     */

    public function getIdCertif(): ?int
    {
        return $this->idCertif;
    }

    /**
     * @return ArrayCollection|null
     */
    public function getNextMessages(): ?ArrayCollection
    {
        return $this->nextMessages;
    }
}