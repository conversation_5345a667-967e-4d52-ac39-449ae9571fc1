<?php

namespace App\Message;

use App\Library\utils\enums\DataProviders;
use Doctrine\Common\Collections\ArrayCollection;

class SynchronizeWorkingContracts
{
    private string $siret;
    private DataProviders $financer;
    private ?ArrayCollection $nextMessages;

    /**
     * @param string $siret
     * @param DataProviders $financer
     * @param ArrayCollection|null $nextMessages
     */
    public function __construct(string $siret, DataProviders $financer, ArrayCollection $nextMessages = null)
    {
        $this->siret = $siret;
        $this->financer = $financer;
        $this->nextMessages = $nextMessages;
    }

    /**
     * @return string
     */
    public function getSiret(): string
    {
        return $this->siret;
    }

    /**
     * @return DataProviders
     */
    public function getFinancer(): DataProviders
    {
        return $this->financer;
    }

    /**
     * @return ArrayCollection|null
     */
    public function getNextMessages(): ?ArrayCollection
    {
        return $this->nextMessages;
    }
}
