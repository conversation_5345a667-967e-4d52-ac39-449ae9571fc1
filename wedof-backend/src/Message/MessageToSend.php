<?php

namespace App\Message;

use Doctrine\Common\Collections\ArrayCollection;

class MessageToSend
{
    private int $messageId;
    private ?ArrayCollection $nextMessages;

    public function __construct(int $messageId, ArrayCollection $nextMessages = null)
    {
        $this->messageId = $messageId;
        $this->nextMessages = $nextMessages;
    }

    /**
     * @return int
     */
    public function getMessageId(): int
    {
        return $this->messageId;
    }

    /**
     * @return ArrayCollection|null
     */
    public function getNextMessages(): ?ArrayCollection
    {
        return $this->nextMessages;
    }
}