<?php

namespace App\Message;

use Doctrine\Common\Collections\ArrayCollection;

class SynchronizeCertifiers
{
    private string $certifInfo;
    private array $updateCertifiersData;
    private ?ArrayCollection $nextMessages;

    /**
     * @param ArrayCollection|null $nextMessages
     */
    public function __construct(string $certifInfo, array $updateCertifiersData = [], ArrayCollection $nextMessages = null)
    {
        $this->certifInfo = $certifInfo;
        $this->updateCertifiersData = $updateCertifiersData;
        $this->nextMessages = $nextMessages;
    }

    /**
     * @return string
     */
    public function getCertifInfo(): string
    {
        return $this->certifInfo;
    }

    /**
     * @return array
     */
    public function getUpdateCertifiersData(): array
    {
        return $this->updateCertifiersData;
    }

    /**
     * @return ArrayCollection|null
     */
    public function getNextMessages(): ?ArrayCollection
    {
        return $this->nextMessages;
    }
}