<?php


namespace App\Message;


use Doctrine\Common\Collections\ArrayCollection;

class UpdateCertificationFolder
{
    private int $id;
    private float $amountHt;
    private ?ArrayCollection $nextMessages;

    /**
     * @param int $id
     * @param float $amountHt
     * @param ArrayCollection|null $nextMessages
     */
    public function __construct(int $id, float $amountHt, ArrayCollection $nextMessages = null)  // TODO set organism param
    {
        $this->id = $id;
        $this->amountHt = $amountHt;
        $this->nextMessages = $nextMessages;
    }

    /**
     * @return ArrayCollection|null
     */
    public function getNextMessages(): ?ArrayCollection
    {
        return $this->nextMessages;
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @return float
     */
    public function getAmountHt(): float
    {
        return $this->amountHt;
    }

}
