<?php

namespace App\Message;

use Doctrine\Common\Collections\ArrayCollection;

class SynchronizeCertification
{
    private string $certifInfo;
    private array $updateCertificationData;
    private ?ArrayCollection $nextMessages;

    /**
     * @param ArrayCollection|null $nextMessages
     */
    public function __construct(string $certifInfo, array $updateCertificationData = [], ArrayCollection $nextMessages = null)
    {
        $this->certifInfo = $certifInfo;
        $this->updateCertificationData = $updateCertificationData;
        $this->nextMessages = $nextMessages;
    }

    /**
     * @return string
     */
    public function getCertifInfo(): string
    {
        return $this->certifInfo;
    }

    public function getUpdateCertificationData(): array
    {
        return $this->updateCertificationData;
    }

    /**
     * @return ArrayCollection|null
     */
    public function getNextMessages(): ?ArrayCollection
    {
        return $this->nextMessages;
    }
}