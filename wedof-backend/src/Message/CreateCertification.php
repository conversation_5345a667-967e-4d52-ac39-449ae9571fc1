<?php

namespace App\Message;

use Doctrine\Common\Collections\ArrayCollection;

class CreateCertification
{
    private string $certifInfo;
    private array $franceCompetencesData;
    private ?ArrayCollection $nextMessages;

    /**
     * @param ArrayCollection|null $nextMessages
     */
    public function __construct(string $certifInfo, array $franceCompetencesData, ArrayCollection $nextMessages = null)
    {
        $this->certifInfo = $certifInfo;
        $this->franceCompetencesData = $franceCompetencesData;
        $this->nextMessages = $nextMessages;
    }

    /**
     * @return string
     */
    public function getCertifInfo(): string
    {
        return $this->certifInfo;
    }

    /**
     * @return array
     */
    public function getFranceCompetencesData(): array
    {
        return $this->franceCompetencesData;
    }

    /**
     * @return ArrayCollection|null
     */
    public function getNextMessages(): ?ArrayCollection
    {
        return $this->nextMessages;
    }
}