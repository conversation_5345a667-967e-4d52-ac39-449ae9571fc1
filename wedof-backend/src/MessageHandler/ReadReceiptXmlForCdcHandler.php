<?php


namespace App\MessageHandler;


use App\Message\ReadReceiptXmlForCdc;
use App\Service\CdcFileService;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Throwable;

class ReadReceiptXmlForCdcHandler extends WithNextMessage implements MessageHandlerInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    private CdcFileService $cdcFileService;

    public function __construct(MessageBusInterface $messageBus, CdcFileService $cdcFileService)
    {
        parent::__construct($messageBus);
        $this->cdcFileService = $cdcFileService;
    }

    public function __invoke(ReadReceiptXmlForCdc $readReceiptXmlForCdc)
    {
        try {
            $this->cdcFileService->processXmlReceipt($readReceiptXmlForCdc->getIdCdcFile(), $readReceiptXmlForCdc->getPassageById(), $readReceiptXmlForCdc->getIdTraitement());
            $this->nextMessages($readReceiptXmlForCdc->getNextMessages());
        } catch (Throwable $e) {
            $this->logger->error($e);
        }
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
