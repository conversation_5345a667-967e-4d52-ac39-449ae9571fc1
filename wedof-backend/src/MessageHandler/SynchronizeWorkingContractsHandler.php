<?php

namespace App\MessageHandler;

use App\Message\SynchronizeWorkingContracts;
use App\Service\DataProviders\WorkingContractApiService;
use App\Service\OrganismService;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Throwable;

class SynchronizeWorkingContractsHandler extends WithNextMessage implements MessageHandlerInterface, LoggerAwareInterface
{
    private OrganismService $organismService;
    private WorkingContractApiService $workingContractApiService;
    private LoggerInterface $logger;

    public function __construct(OrganismService $organismService, WorkingContractApiService $workingContractApiService, MessageBusInterface $messageBus)
    {
        parent::__construct($messageBus);
        $this->organismService = $organismService;
        $this->workingContractApiService = $workingContractApiService;
    }

    public function __invoke(SynchronizeWorkingContracts $synchronizeWorkingContracts)
    {
        try {
            $organism = $this->organismService->getBySiret($synchronizeWorkingContracts->getSiret());
            $this->workingContractApiService->synchronizeByOrganismAndFinancer($organism, $synchronizeWorkingContracts->getFinancer());
            $this->nextMessages($synchronizeWorkingContracts->getNextMessages());
        } catch (Throwable $e) {
            $this->logger->error($e);
        }
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}