<?php

namespace App\MessageHandler;

use App\Entity\Organism;
use App\Library\utils\enums\RegistrationFolderSortParams;
use App\Library\utils\enums\RegistrationFolderStates;
use App\Message\OneBatchRegistrationFolders;
use App\Message\StartBatchesRegistrationFolders;
use App\Service\ConnectionService;
use App\Service\DataProviders\BaseApiService;
use App\Service\OrganismService;
use App\Service\RegistrationFolderService;
use Doctrine\Common\Collections\ArrayCollection;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Throwable;

class StartBatchesRegistrationFoldersHandler extends WithNextMessage implements MessageHandlerInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    private OrganismService $organismService;
    private ConnectionService $connectionService;
    private RegistrationFolderService $registrationFolderService;

    public function __construct(OrganismService $organismService, ConnectionService $connectionService, RegistrationFolderService $registrationFolderService, MessageBusInterface $messageBus)
    {
        parent::__construct($messageBus);
        $this->organismService = $organismService;
        $this->connectionService = $connectionService;
        $this->registrationFolderService = $registrationFolderService;
    }

    /**
     * @param StartBatchesRegistrationFolders $startBatchesRegistrationFolders
     */
    public function __invoke(StartBatchesRegistrationFolders $startBatchesRegistrationFolders)
    {
        try {
            $apiService = BaseApiService::getRegistrationFolderApiServiceByDataProvider($startBatchesRegistrationFolders->getDataProvider());
            if ($startBatchesRegistrationFolders->getDisableWedofEvents()) {
                $_SERVER['NO_DISPATCH_WEDOF_EVENTS'] = true;
            }
            $organisms = $startBatchesRegistrationFolders->getSiret() ? new ArrayCollection([$this->organismService->getBySiret($startBatchesRegistrationFolders->getSiret())]) : [];
            if ($organisms->count() > 0) {
                /** @var Organism $organism */
                foreach ($organisms as $organism) {
                    $this->logger->info("[startBatchesRegistrationFolders][" . $organism->getSiret() . "] diff started");

                    $countProvider = $apiService->getRegistrationFoldersCount($organism, RegistrationFolderStates::ALL(), RegistrationFolderSortParams::ETAT(), ["maxRetry" => 3, "requestDispatchMonitoringEvent" => false]);
                    $this->logger->info("[startBatchesRegistrationFolders] $countProvider registrationFolders total data provider " . $startBatchesRegistrationFolders->getDataProvider()->getValue());
                    $countWEDOF = $this->registrationFolderService->countByOrganismAndDataProvider($organism, $startBatchesRegistrationFolders->getDataProvider());
                    $this->logger->info("[startBatchesRegistrationFolders] $countWEDOF registrationFolders total wedof");
                    $connection = $organism->getConnectionForDataProvider($startBatchesRegistrationFolders->getDataProvider());
                    if ($countProvider > 0 && $countWEDOF < $countProvider) {
                        //$skipMultiple = ceil($startBatchesRegistrationFolders->getSkip() / max($startBatchesRegistrationFolders->getLimit(), 1));
                        $skipMultiple = ceil($startBatchesRegistrationFolders->getSkip() / $startBatchesRegistrationFolders->getLimit());
                        $message = new OneBatchRegistrationFolders(
                            $startBatchesRegistrationFolders->getDataProvider(),
                            $organism->getSiret(),
                            $startBatchesRegistrationFolders->getLimit(),
                            $skipMultiple * $startBatchesRegistrationFolders->getLimit(),
                            $countProvider,
                            $startBatchesRegistrationFolders->getDisableWedofEvents());
                        $this->messageBus->dispatch($message);
                    } else if (!$connection->isInitialized()) {
                        if ($startBatchesRegistrationFolders->getDisableWedofEvents()) {
                            $_SERVER['NO_DISPATCH_WEDOF_EVENTS'] = false;
                        }
                        $this->connectionService->finishInitialize($connection);
                    }
                }
            }
        } catch (Throwable $e) {
            $this->logger->error($e);
        } finally {
            //reset
            if ($startBatchesRegistrationFolders->getDisableWedofEvents()) {
                $_SERVER['NO_DISPATCH_WEDOF_EVENTS'] = false;
            }
        }
        $this->nextMessages($startBatchesRegistrationFolders->getNextMessages());
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
