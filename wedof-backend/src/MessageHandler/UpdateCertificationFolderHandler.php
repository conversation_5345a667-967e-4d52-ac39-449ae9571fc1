<?php


namespace App\MessageHandler;


use App\Message\UpdateCertificationFolder;
use App\Service\CertificationFolderService;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Throwable;

class UpdateCertificationFolderHandler extends WithNextMessage implements MessageHandlerInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    private CertificationFolderService $certificationFolderService;

    public function __construct(CertificationFolderService $certificationFolderService, MessageBusInterface $messageBus)
    {
        parent::__construct($messageBus);
        $this->certificationFolderService = $certificationFolderService;
    }


    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }


    public function __invoke(UpdateCertificationFolder $updateCertificationFolder)
    {
        try {
            $certificationFolder = $this->certificationFolderService->getById($updateCertificationFolder->getId());
            if ($certificationFolder) {
                $this->certificationFolderService->update($certificationFolder, ['amountHt' => $updateCertificationFolder->getAmountHt()]); // TODO set organism param
            }
            $this->nextMessages($updateCertificationFolder->getNextMessages());
        } catch (Throwable $e) {
            $this->logger->error($e);
        }
    }

}
