<?php

namespace App\MessageHandler;

use App\Message\SynchronizeCertificationPartners;
use App\Service\CertificationService;
use App\Service\ConnectionService;
use App\Service\DataProviders\BaseApiService;
use App\Service\MailerService;
use App\Service\OrganismService;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Throwable;

class SynchronizeCertificationPartnersHandler extends WithNextMessage implements MessageHandlerInterface, LoggerAwareInterface
{
    private ConnectionService $connectionService;
    private CertificationService $certificationService;
    private MailerService $mailerService;
    private OrganismService $organismService;

    private LoggerInterface $logger;

    public function __construct(CertificationService $certificationService, ConnectionService $connectionService, OrganismService $organismService, MessageBusInterface $messageBus, MailerService $mailerService)
    {
        parent::__construct($messageBus);
        $this->connectionService = $connectionService;
        $this->certificationService = $certificationService;
        $this->mailerService = $mailerService;
        $this->organismService = $organismService;
    }

    public function __invoke(SynchronizeCertificationPartners $synchronizeCertificationPartners)
    {
        $certifier = $this->organismService->getBySiret($synchronizeCertificationPartners->getSiret());
        $certification = $this->certificationService->getByCertifInfo($synchronizeCertificationPartners->getCertifInfo());
        $dataProvider = $synchronizeCertificationPartners->getDataProvider();
        $retryCounter = $synchronizeCertificationPartners->getRetryCounter();
        try {
            if (!$certification->hasErrorSynchronizePartners() && $this->connectionService->hasAccess($certifier, $dataProvider, true)) {
                $certificationApiService = BaseApiService::getCertificationApiServiceByDataProvider($dataProvider);
                $certificationApiService->synchronizeCertificationPartners($certification, $certifier, $retryCounter);
            }
            $this->nextMessages($synchronizeCertificationPartners->getNextMessages());
        } catch (Throwable $e) {
            $this->logger->error($e);
            try {
                $this->certificationService->update($certification, $certifier, ['errorSynchronizePartners' => true]);
                $this->mailerService->sendUpdateCertificationPartnersIssueToWedofTeam($certification, "Exception : " . $e->getMessage());
            } catch (Throwable $e) {}
        }
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
