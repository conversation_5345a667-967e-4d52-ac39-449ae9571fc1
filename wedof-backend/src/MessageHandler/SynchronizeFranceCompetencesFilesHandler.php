<?php

namespace App\MessageHandler;

use App\Message\SynchronizeFranceCompetencesFiles;
use App\Service\DataProviders\FranceCompetencesApiService;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Throwable;

class SynchronizeFranceCompetencesFilesHandler extends WithNextMessage implements MessageHandlerInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    private FranceCompetencesApiService $franceCompetencesApiService;

    public function __construct(FranceCompetencesApiService $franceCompetencesApiService, MessageBusInterface $messageBus)
    {
        parent::__construct($messageBus);
        $this->franceCompetencesApiService = $franceCompetencesApiService;
    }

    /**
     * @param SynchronizeFranceCompetencesFiles $franceCompetencesFiles
     */
    public function __invoke(SynchronizeFranceCompetencesFiles $franceCompetencesFiles)
    {
        // NOT ASYNC !!!
        try {
            $this->franceCompetencesApiService->refreshDataFiles($franceCompetencesFiles->getType());
            $this->franceCompetencesApiService->addNewCertifications($franceCompetencesFiles->getType());
            $this->nextMessages($franceCompetencesFiles->getNextMessages());
        } catch (Throwable $e) {
            $this->logger->error($e);
        }
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
