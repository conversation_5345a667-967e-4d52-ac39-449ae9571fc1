<?php

namespace App\MessageHandler;

use App\Message\InTrainingRegistrationFolder;
use App\Service\RegistrationFolderService;
use App\Service\UserService;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Throwable;

class InTrainingRegistrationFolderHandler extends WithNextMessage implements LoggerAwareInterface, MessageHandlerInterface
{
    private LoggerInterface $logger;
    private RegistrationFolderService $registrationFolderService;
    private UserService $userService;

    public function __construct(RegistrationFolderService $registrationFolderService, UserService $userService, MessageBusInterface $messageBus)
    {
        parent::__construct($messageBus);
        $this->registrationFolderService = $registrationFolderService;
        $this->userService = $userService;
    }

    /**
     * @inheritDoc
     */
    public function setLogger(LoggerInterface $logger)
    {
        $this->logger = $logger;
    }

    public function __invoke(InTrainingRegistrationFolder $inTrainingRegistrationFolder)
    {
        $user = $this->userService->getById($inTrainingRegistrationFolder->getUserId());
        try {
            $registrationFolder = $this->registrationFolderService->getByExternalId($inTrainingRegistrationFolder->getExternalId());
            if ($registrationFolder) {
                $inTrainingDate = $inTrainingRegistrationFolder->getInTrainingDate();
                $_SERVER['OVERRIDE_CLIENT_ADDR'] = $inTrainingRegistrationFolder->getIpAddress();
                $this->registrationFolderService->inTraining($registrationFolder, $inTrainingDate, $user);
                unset($_SERVER['OVERRIDE_CLIENT_ADDR']);
            }
            $this->nextMessages($inTrainingRegistrationFolder->getNextMessages());
        } catch (Throwable $e) {
            $this->logger->error($e);
        }
    }
}