<?php

namespace App\MessageHandler;

use App\Message\CreateCertificationFolder;
use App\Service\CertificationFolderService;
use App\Service\RegistrationFolderService;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Throwable;

class CreateCertificationFolderHandler extends WithNextMessage implements MessageHandlerInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    private CertificationFolderService $certificationFolderService;
    private RegistrationFolderService $registrationFolderService;

    public function __construct(CertificationFolderService $certificationFolderService, RegistrationFolderService $registrationFolderService, MessageBusInterface $messageBus)
    {
        parent::__construct($messageBus);
        $this->certificationFolderService = $certificationFolderService;
        $this->registrationFolderService = $registrationFolderService;
    }

    /**
     * @param CreateCertificationFolder $createCertificationFolder
     */
    public function __invoke(CreateCertificationFolder $createCertificationFolder)
    {
        try {
            $registrationFolder = $this->registrationFolderService->getByExternalId($createCertificationFolder->getExternalId());
            $certificationFolder = $this->certificationFolderService->createFromRegistrationFolder($registrationFolder);
            $this->certificationFolderService->updateFromRegistrationFolder($certificationFolder, $registrationFolder);
            $this->nextMessages($createCertificationFolder->getNextMessages());
        } catch (Throwable $e) {
            $this->logger->error($e);
        }
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
