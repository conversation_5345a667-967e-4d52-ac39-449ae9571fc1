<?php

namespace App\MessageHandler;

use App\Message\SynchronizeRegistrationFolder;
use App\Service\RegistrationFolderService;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Throwable;

class SynchronizeRegistrationFolderHandler extends WithNextMessage implements MessageHandlerInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    private RegistrationFolderService $registrationFolderService;

    public function __construct(RegistrationFolderService $registrationFolderService, MessageBusInterface $messageBus)
    {
        parent::__construct($messageBus);
        $this->registrationFolderService = $registrationFolderService;
    }

    /**
     * @param SynchronizeRegistrationFolder $synchronizeRegistrationFolder
     */
    public function __invoke(SynchronizeRegistrationFolder $synchronizeRegistrationFolder)
    {
        try {
            $registrationFolder = $this->registrationFolderService->getByExternalId($synchronizeRegistrationFolder->getExternalId());
            $_SERVER['OVERRIDE_CLIENT_ADDR'] = $synchronizeRegistrationFolder->getIpAddress();
            if (!$registrationFolder) {
                $this->registrationFolderService->retrieve($synchronizeRegistrationFolder->getExternalId(), $synchronizeRegistrationFolder->getSiret(), $synchronizeRegistrationFolder->getDataProvider());
            }
            if ($registrationFolder) {
                $this->registrationFolderService->refresh($registrationFolder);
            }
            unset($_SERVER['OVERRIDE_CLIENT_ADDR']);
            $this->nextMessages($synchronizeRegistrationFolder->getNextMessages());
        } catch (Throwable $e) {
            $this->logger->error($e);
        }
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
