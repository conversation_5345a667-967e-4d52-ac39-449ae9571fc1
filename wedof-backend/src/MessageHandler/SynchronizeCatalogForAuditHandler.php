<?php

namespace App\MessageHandler;

use App\Exception\WedofBadRequestHttpException;
use App\Library\utils\enums\CertificationPartnerAuditStates;
use App\Library\utils\enums\DataProviders;
use App\Message\SynchronizeCatalogForAudit;
use App\Service\CatalogService;
use App\Service\CertificationPartnerAuditService;
use App\Service\ConnectionService;
use App\Service\OrganismService;
use Doctrine\Common\Collections\ArrayCollection;
use Exception;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Messenger\Stamp\DelayStamp;
use Throwable;

class SynchronizeCatalogForAudit<PERSON><PERSON>ler extends WithNextMessage implements MessageHandlerInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    private CatalogService $catalogService;
    private CertificationPartnerAuditService $certificationPartnerAuditService;
    private ConnectionService $connectionService;
    private OrganismService $organismService;

    public function __construct(MessageBusInterface $messageBus, CatalogService $catalogService, CertificationPartnerAuditService $certificationPartnerAuditService, ConnectionService $connectionService, OrganismService $organismService)
    {
        parent::__construct($messageBus);
        $this->catalogService = $catalogService;
        $this->certificationPartnerAuditService = $certificationPartnerAuditService;
        $this->connectionService = $connectionService;
        $this->organismService = $organismService;
    }

    /**
     * @param SynchronizeCatalogForAudit $synchronizeCatalog
     * @return void
     * @throws Throwable
     */
    public function __invoke(SynchronizeCatalogForAudit $synchronizeCatalog)
    {
        $this->logger->debug("[SynchronizeCatalogForAudit] Start " . $synchronizeCatalog->getSiret());
        $computeAuditMessage = $synchronizeCatalog->getComputeCertificationPartnerAudit();
        $certificationPartnerAudit = $this->certificationPartnerAuditService->getById($computeAuditMessage->getCertificationPartnerAuditId());
        try {
            if ($certificationPartnerAudit->getState() !== CertificationPartnerAuditStates::PENDING_COMPUTATION()->getValue()) { // Avoid duplicate operation due to race condition
                throw new WedofBadRequestHttpException('Erreur, le rafraîchissement du catalogue ne peut pas être effectué dans cet état de l\'audit ' . $certificationPartnerAudit->getState() . ' ' . $synchronizeCatalog->getSiret());
            }
            $dataProvider = DataProviders::CPF();
            $organism = $this->organismService->getBySiret($synchronizeCatalog->getSiret());
            if ($this->connectionService->hasAccess($organism, $dataProvider)) {
                $this->catalogService->synchronizeForDataProvider($organism, $dataProvider, [$synchronizeCatalog->getCertificationCode()], null, ['daysMaxSinceLastUpdate' => 0]);
                $delayInSeconds = 5400; // 1.5 hours so that training actions have time to be loaded
                $computeAuditMessage = new Envelope($computeAuditMessage, [
                    new DelayStamp($delayInSeconds * 1000)
                ]);
            } else {
                $this->logger->debug("[SynchronizeCatalogForAudit] Non applicable : no CPF access " . $synchronizeCatalog->getSiret());
                $computeAuditMessage->setHasValidCatalog(false);
            }
            $this->logger->debug("[SynchronizeCatalogForAudit] Completed - Process ComputePartnerAuditMessage " . $synchronizeCatalog->getSiret());
            $this->nextMessages(new ArrayCollection([$computeAuditMessage]));
        } catch (Throwable $e) {
            $this->logger->error("[SynchronizeCatalogForAudit] Failure during sync " . $synchronizeCatalog->getSiret());
            $this->logger->error($e);
            $this->certificationPartnerAuditService->failed($certificationPartnerAudit, 'Une erreur est survenue lors de la synchronisation du catalogue');
        }
    }

    /**
     * @param LoggerInterface $logger
     * @return void
     */
    public function setLogger(LoggerInterface $logger)
    {
        $this->logger = $logger;
    }
}