<?php

namespace App\MessageHandler;

use Doctrine\Common\Collections\ArrayCollection;
use Symfony\Component\Messenger\MessageBusInterface;

class WithNextMessage
{
    protected MessageBusInterface $messageBus;

    public function __construct(MessageBusInterface $messageBus)
    {
        $this->messageBus = $messageBus;
    }

    protected function nextMessages(ArrayCollection $messages = null)
    {
        if ($messages != null) {
            foreach ($messages as $message) {
                $this->messageBus->dispatch($message);
            }
        }
    }
}
