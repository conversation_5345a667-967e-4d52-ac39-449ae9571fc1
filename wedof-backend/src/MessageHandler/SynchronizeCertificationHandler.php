<?php

namespace App\MessageHandler;

use App\Message\SynchronizeCertification;
use App\Service\CertificationService;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Throwable;

class SynchronizeCertificationHandler extends WithNextMessage implements MessageHandlerInterface, LoggerAwareInterface
{
    private CertificationService $certificationService;
    private LoggerInterface $logger;

    public function __construct(CertificationService $certificationService, MessageBusInterface $messageBus)
    {
        parent::__construct($messageBus);
        $this->certificationService = $certificationService;
    }

    public function __invoke(SynchronizeCertification $synchronizeCertification)
    {
        try {
            if (!empty($synchronizeCertification->getUpdateCertificationData())) {
                $this->certificationService->getCertification(['certifInfo' => $synchronizeCertification->getCertifInfo()], ['refresh' => true, 'certificationData' => $synchronizeCertification->getUpdateCertificationData()]);
            }
            $this->nextMessages($synchronizeCertification->getNextMessages());
        } catch (Throwable $e) {
            $this->logger->error($e);
        }
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}