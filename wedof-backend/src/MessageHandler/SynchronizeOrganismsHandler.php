<?php

namespace App\MessageHandler;

use App\Message\SynchronizeOrganisms;
use App\Service\OrganismService;
use Doctrine\Common\Collections\ArrayCollection;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Throwable;

class SynchronizeOrganismsHandler extends WithNextMessage implements MessageHandlerInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    private OrganismService $organismService;

    public function __construct(OrganismService $organismService, MessageBusInterface $messageBus)
    {
        parent::__construct($messageBus);
        $this->organismService = $organismService;
    }

    /**
     * @param SynchronizeOrganisms $synchronizeOrganisms
     */
    public function __invoke(SynchronizeOrganisms $synchronizeOrganisms)
    {
        try {
            $organisms = $synchronizeOrganisms->getSiret() ?
                new ArrayCollection([$this->organismService->getBySiret($synchronizeOrganisms->getSiret())])
                : $this->organismService->listToSync($synchronizeOrganisms->getSkip(), $synchronizeOrganisms->getLimit());
            if ($organisms->count() > 0) {
                $current = 0;
                foreach ($organisms as $organism) {
                    $this->logger->info("[SynchronizeOrganisms][" . $organism->getSiret() . "] synchronize");
                    $organismRawData = $this->organismService->getOrganismData(['siret' => $organism->getSiret()]);
                    $this->organismService->createOrUpdate($organismRawData);
                    $current++;
                }
                $this->logger->info("[SynchronizeOrganisms] $current organisms updated");
            } else {
                $this->logger->info("[SynchronizeOrganisms] 0 organisms updated");
            }
            $this->nextMessages($synchronizeOrganisms->getNextMessages());
        } catch (Throwable $e) {
            $this->logger->error($e);
        }
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
