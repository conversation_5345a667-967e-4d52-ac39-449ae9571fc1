<?php

namespace App\MessageHandler;

use App\Message\SynchronizeCertifiers;
use App\Service\CertificationService;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Throwable;

class SynchronizeCertifiersH<PERSON>ler extends WithNextMessage implements MessageHandlerInterface, LoggerAwareInterface
{
    private CertificationService $certificationService;
    private LoggerInterface $logger;

    public function __construct(CertificationService $certificationService, MessageBusInterface $messageBus)
    {
        parent::__construct($messageBus);
        $this->certificationService = $certificationService;
    }

    public function __invoke(SynchronizeCertifiers $synchronizeCertifiers)
    {
        try {
            $certification = $this->certificationService->getByCertifInfo($synchronizeCertifiers->getCertifInfo());
            //update certifiers
            $this->certificationService->updateCertifiersForCertification($certification, $synchronizeCertifiers->getUpdateCertifiersData());
            $this->nextMessages($synchronizeCertifiers->getNextMessages());
        } catch (Throwable $e) {
            $this->logger->error($e);
        }
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}