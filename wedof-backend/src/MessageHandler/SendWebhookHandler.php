<?php

namespace App\MessageHandler;

use App\Message\SendWebhook;
use App\Repository\WebhookRepository;
use App\Service\WebhookService;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Throwable;

class SendWebhookHandler extends WithNextMessage implements MessageHandlerInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    private WebhookService $webhookService;
    private WebhookRepository $webhookRepository;

    public function __construct(WebhookService $webhookService, WebhookRepository $webhookRepository, MessageBusInterface $messageBus)
    {
        parent::__construct($messageBus);
        $this->webhookService = $webhookService;
        $this->webhookRepository = $webhookRepository;
    }

    /**
     * @param SendWebhook $sendWebhook
     * @throws OptimisticLockException
     * @throws Throwable
     * @throws ORMException
     */
    public function __invoke(SendWebhook $sendWebhook)
    {
        $this->logger->info('SendWebhook message received');
        $webhook = $this->webhookRepository->find($sendWebhook->getWebhookId());
        if ($webhook) {
            $this->webhookService->send($webhook, $sendWebhook->getEventName(), $sendWebhook->getPayload(), $sendWebhook->getEntityClass(), $sendWebhook->getEntityId(), $sendWebhook->getAuthorId(), $sendWebhook->getGuid());
        }
        $this->nextMessages($sendWebhook->getNextMessages());
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
