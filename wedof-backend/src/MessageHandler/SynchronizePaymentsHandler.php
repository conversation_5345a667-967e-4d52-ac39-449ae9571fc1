<?php

namespace App\MessageHandler;

use App\Entity\Organism;
use App\Library\utils\enums\PaymentStates;
use App\Message\SynchronizePayments;
use App\Service\ConnectionService;
use App\Service\DataProviders\BaseApiService;
use App\Service\DataProviders\CpfApiService;
use App\Service\OrganismService;
use App\Service\PaymentService;
use App\Service\RegistrationFolderService;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\OptimisticLockException;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Throwable;

class SynchronizePaymentsHandler extends WithNextMessage implements MessageHandlerInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    private OrganismService $organismService;
    private PaymentService $paymentService;
    private ConnectionService $connectionService;
    private RegistrationFolderService $registrationFolderService;

    public function __construct(OrganismService $organismService, ConnectionService $connectionService, PaymentService $paymentService, MessageBusInterface $messageBus, RegistrationFolderService $registrationFolderService)
    {
        parent::__construct($messageBus);
        $this->organismService = $organismService;
        $this->paymentService = $paymentService;
        $this->connectionService = $connectionService;
        $this->registrationFolderService = $registrationFolderService;
    }

    /**
     * @param SynchronizePayments $synchronizePayments
     */
    public function __invoke(SynchronizePayments $synchronizePayments)
    {
        try {
            $dataProvider = $synchronizePayments->getDataProvider();
            /** @var CpfApiService $apiService */
            $apiService = BaseApiService::getApiServiceByDataProvider($dataProvider);
            //on passe par l'organisme pour éviter de répéter les mêmes actions pour des utilisateurs qui seraient en multi organismes
            $organism = $this->organismService->getOrganism(['siret' => $synchronizePayments->getSiret()]);
            if (!$this->connectionService->hasAccess($organism, $dataProvider)) {
                $this->logger->info("[SynchronizePayments] no user found for this siret " . $synchronizePayments->getSiret() . " cancel");
                return;
            }
            if ($synchronizePayments->getRegistrationFolderExternalId()) {
                $registrationFolder = $this->registrationFolderService->getByExternalId($synchronizePayments->getRegistrationFolderExternalId());
                if (!$registrationFolder || $registrationFolder->getOrganism() !== $organism) {
                    $this->logger->info("[SynchronizePayments] no registration folder found for this siret " . $synchronizePayments->getSiret() . " external id " . $synchronizePayments->getRegistrationFolderExternalId());
                    return;
                }
            }
            $this->logger->info("[SynchronizePayments] started for siret " . $synchronizePayments->getSiret());
            if ($synchronizePayments->getState() && $synchronizePayments->getState() !== PaymentStates::ALL()->getValue()) {
                $states = [PaymentStates::from($synchronizePayments->getState())];
            } else {
                $states = [];
                foreach (PaymentStates::valuesStates() as $state) {
                    if ($state->getValue() !== PaymentStates::ALL()->getValue()) {
                        $states[] = $state;
                    }
                }
            }
            foreach ($states as $state) {
                $orderBy = (PaymentStates::ISSUED()->equals($state)) ? ['scheduledDate' => 'DESC'] : null;
                $lastPayment = empty($registrationFolder) ? $this->paymentService->getLastPaymentByOrganismAndState($organism, $state, $orderBy) : null;
                $this->logger->info("[SynchronizePayments] state " . $state->getValue() . " " . ($lastPayment ? $lastPayment->getId() : 0));
                $paymentsRawData = $apiService->getPaymentsRawData($organism, $state, $lastPayment, $synchronizePayments->getSort(), 1, 50, $registrationFolder ?? null);
                $this->batchCreateOrUpdate($paymentsRawData, $organism);
                $this->logger->info("[SynchronizePayments] state " . $state->getValue() . " " . sizeof($paymentsRawData) . " payments synced");
            }
            $this->nextMessages($synchronizePayments->getNextMessages());
        } catch (Throwable $e) {
            $this->logger->error($e);
        }
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    /**
     * @param array $paymentsRawData
     * @param Organism $organism
     * @return void
     * @throws Throwable
     * @throws ORMException
     * @throws NonUniqueResultException
     * @throws OptimisticLockException
     */
    private function batchCreateOrUpdate(array $paymentsRawData, Organism $organism)
    {
        $paymentsRawData = array_reverse($paymentsRawData);
        foreach ($paymentsRawData as $paymentRawData) {
            if (!empty($paymentRawData['registrationFolderId'])) {
                $this->logger->info("[SynchronizePayments] sync " . $paymentRawData['registrationFolderId'] . " on payment type " . $paymentRawData['type'] . " state " . $paymentRawData['status']);
                $this->paymentService->createOrUpdate($paymentRawData, $organism);
            }
        }
    }
}
