<?php

namespace App\MessageHandler;

use App\Event\RegistrationFolder\RegistrationFolderMonitoringEvents;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\enums\RegistrationFolderStates;
use App\Library\utils\Tools;
use App\Message\MonitorRegistrationFolders;
use App\Service\OrganismService;
use App\Service\RegistrationFolderService;
use App\Service\WebhookService;
use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use LogicException;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Throwable;

class MonitorRegistrationFoldersHandler extends WithNextMessage implements MessageHandlerInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;

    private EventDispatcherInterface $dispatcher;
    private RegistrationFolderService $registrationFolderService;
    private OrganismService $organismService;
    private WebhookService $webhookService;

    public function __construct(RegistrationFolderService $registrationFolderService, OrganismService $organismService, WebhookService $webhookService, EventDispatcherInterface $dispatcher, MessageBusInterface $messageBus)
    {
        parent::__construct($messageBus);
        $this->registrationFolderService = $registrationFolderService;
        $this->organismService = $organismService;
        $this->webhookService = $webhookService;
        $this->dispatcher = $dispatcher;
    }

    /**
     * @param MonitorRegistrationFolders $monitorRegistrationFolders
     */
    public function __invoke(MonitorRegistrationFolders $monitorRegistrationFolders)
    {
        $organism = $this->organismService->getBySiret($monitorRegistrationFolders->getSiret());
        $monitorNotValidated = $this->webhookService->listByOrganismsAndEventAndType(new ArrayCollection([$organism]), RegistrationFolderMonitoringEvents::NOT_VALIDATED)->count() > 0;
        $monitorNotAccepted = $this->webhookService->listByOrganismsAndEventAndType(new ArrayCollection([$organism]), RegistrationFolderMonitoringEvents::NOT_ACCEPTED)->count() > 0;
        $monitorNotInTraining = $this->webhookService->listByOrganismsAndEventAndType(new ArrayCollection([$organism]), RegistrationFolderMonitoringEvents::NOT_IN_TRAINING)->count() > 0;
        $monitorNotServiceDoneDeclared = $this->webhookService->listByOrganismsAndEventAndType(new ArrayCollection([$organism]), RegistrationFolderMonitoringEvents::NOT_SERVICE_DONE_DECLARED)->count() > 0;
        $states = [];
        if ($monitorNotValidated) {
            $states[] = RegistrationFolderStates::NOT_PROCESSED()->getValue();
        }
        if ($monitorNotAccepted) {
            $states[] = RegistrationFolderStates::VALIDATED()->getValue();
        }
        if ($monitorNotInTraining) {
            $states[] = RegistrationFolderStates::ACCEPTED()->getValue();
        }
        if ($monitorNotServiceDoneDeclared) {
            $states[] = RegistrationFolderStates::IN_TRAINING()->getValue();
        }
        $registrationFolders = $this->registrationFolderService->listByOrganismAndStatesAndType($organism, $states, DataProviders::CPF());

        try {
            foreach ($registrationFolders as $registrationFolder) {
                switch ($registrationFolder->getState()) {
                    // Dossier pas validé par l'organisme ?
                    case RegistrationFolderStates::NOT_PROCESSED()->getValue():
                        /** @var DateTime $limitDate */
                        $limitDate = clone $registrationFolder->getLastUpdate();
                        $limitDate = Tools::addWeekDays($limitDate, 2, $registrationFolder->getLastUpdate()->format('H'), $registrationFolder->getLastUpdate()->format('i'));
                        if ($limitDate > new DateTime()) {
                            $interval = $limitDate->diff(new DateTime());
                            $diffHours = $interval->days * 24 + $interval->h;
                            if ($diffHours == 24 || $diffHours == 12 || $diffHours == 6) {
                                $this->dispatcher->dispatch(new RegistrationFolderMonitoringEvents($registrationFolder, $diffHours), RegistrationFolderMonitoringEvents::NOT_VALIDATED);
                            }
                        }
                        break;
                    // Dossier non accepté par l'apprenant
                    case RegistrationFolderStates::VALIDATED()->getValue():
                        /** @var DateTime $limitDate */
                        $limitDate = clone $registrationFolder->getLastUpdate();
                        $limitDate = Tools::addWeekDays($limitDate, 4, $registrationFolder->getLastUpdate()->format('H'), $registrationFolder->getLastUpdate()->format('i'));
                        if ($limitDate > new DateTime()) {
                            $interval = $limitDate->diff(new DateTime());
                            $diffHours = $interval->days * 24 + $interval->h;
                            if ($diffHours == 24 || $diffHours == 12 || $diffHours == 6) {
                                $this->dispatcher->dispatch(new RegistrationFolderMonitoringEvents($registrationFolder, $diffHours), RegistrationFolderMonitoringEvents::NOT_ACCEPTED);
                            }
                        }
                        break;
                    // Dossier pas en formation alors que session démarrée
                    case RegistrationFolderStates::ACCEPTED()->getValue():
                        $sessionStartDate = Tools::createDateFromString($registrationFolder->getRawData()['trainingActionInfo']['sessionStartDate']);
                        if ($sessionStartDate < new DateTime()) {
                            $limitDate = clone $sessionStartDate;
                            $limitDate = Tools::addWeekDays($limitDate, 3, $sessionStartDate->format('H'), $sessionStartDate->format('i'));
                            if ($limitDate > new DateTime()) {
                                $interval = $limitDate->diff(new DateTime());
                                $diffHours = $interval->days * 24 + $interval->h;
                                if ($diffHours == 24 || $diffHours == 12 || $diffHours == 6) {
                                    $this->dispatcher->dispatch(new RegistrationFolderMonitoringEvents($registrationFolder, $diffHours), RegistrationFolderMonitoringEvents::NOT_IN_TRAINING);
                                }
                            }

                        }
                        break;
                    // Dossier pas service fait non déclaré alors que session terminée
                    case RegistrationFolderStates::IN_TRAINING()->getValue():
                        $sessionEndDate = Tools::createDateFromString($registrationFolder->getRawData()['trainingActionInfo']['sessionEndDate']);
                        if ($sessionEndDate < new DateTime()) {
                            $limitDate = clone $sessionEndDate;
                            $limitDate = Tools::addWeekDays($limitDate, 3, $sessionEndDate->format('H'), $sessionEndDate->format('i'));
                            if ($limitDate > new DateTime()) {
                                $interval = $limitDate->diff(new DateTime());
                                $diffHours = $interval->days * 24 + $interval->h;
                                if ($diffHours == 24 || $diffHours == 12 || $diffHours == 6) {
                                    $this->dispatcher->dispatch(new RegistrationFolderMonitoringEvents($registrationFolder, $diffHours), RegistrationFolderMonitoringEvents::NOT_SERVICE_DONE_DECLARED);
                                }
                            }
                        }
                        break;
                    default:
                        throw new LogicException("[MonitorRegistrationFoldersHandler] : Ce code n'aurait pas du être atteint.");
                }
            }
            $this->nextMessages($monitorRegistrationFolders->getNextMessages());
        } catch (Throwable $e) {
            $this->logger->error($e);
        }
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
