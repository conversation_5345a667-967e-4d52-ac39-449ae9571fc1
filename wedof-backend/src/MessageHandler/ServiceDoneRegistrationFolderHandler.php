<?php

namespace App\MessageHandler;

use App\Message\ServiceDoneRegistrationFolder;
use App\Service\RegistrationFolderReasonService;
use App\Service\RegistrationFolderService;
use App\Service\UserService;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Throwable;

class ServiceDoneRegistrationFolderHandler extends WithNextMessage implements MessageHandlerInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    private RegistrationFolderService $registrationFolderService;
    private RegistrationFolderReasonService $registrationFolderReasonService;
    private UserService $userService;

    public function __construct(RegistrationFolderService $registrationFolderService, RegistrationFolderReasonService $registrationFolderReasonService, UserService $userService, MessageBusInterface $messageBus)
    {
        parent::__construct($messageBus);
        $this->registrationFolderService = $registrationFolderService;
        $this->registrationFolderReasonService = $registrationFolderReasonService;
        $this->userService = $userService;
    }

    /**
     * @param ServiceDoneRegistrationFolder $declareServiceDoneRegistrationFolder
     */
    public function __invoke(ServiceDoneRegistrationFolder $declareServiceDoneRegistrationFolder)
    {
        $user = $this->userService->getById($declareServiceDoneRegistrationFolder->getUserId());
        try {
            $registrationFolder = $this->registrationFolderService->getByExternalId($declareServiceDoneRegistrationFolder->getExternalId());
            if ($registrationFolder) {
                $_SERVER['OVERRIDE_CLIENT_ADDR'] = $declareServiceDoneRegistrationFolder->getIpAddress();
                $reason = $declareServiceDoneRegistrationFolder->getCodeReason() ? $this->registrationFolderReasonService->getReasonByCodeTerminated($declareServiceDoneRegistrationFolder->getCodeReason()) : null;
                $this->registrationFolderService->declareServiceDone($registrationFolder, $declareServiceDoneRegistrationFolder->getAbsenceDuration(), $declareServiceDoneRegistrationFolder->isForceMajeureAbsence(), $reason, $declareServiceDoneRegistrationFolder->getTerminatedDate(), $declareServiceDoneRegistrationFolder->getTrainingDuration(), $user);
                unset($_SERVER['OVERRIDE_CLIENT_ADDR']);
            }
            $this->nextMessages($declareServiceDoneRegistrationFolder->getNextMessages());
        } catch (Throwable $e) {
            $this->logger->error($e);
        }
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
