<?php

namespace App\MessageHandler;

use App\Exception\WedofBadRequestHttpException;
use App\Library\utils\enums\CertificationPartnerAuditStates;
use App\Message\ComputeCertificationPartnerAudit;
use App\Service\CertificationPartnerAuditService;
use App\Service\UserService;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Throwable;


class ComputeCertificationPartnerAuditHandler extends WithNextMessage implements MessageHandlerInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    private CertificationPartnerAuditService $certificationPartnerAuditService;
    private UserService $userService;

    public function __construct(MessageBusInterface $messageBus, CertificationPartnerAuditService $certificationPartnerAuditService, UserService $userService)
    {
        parent::__construct($messageBus);
        $this->certificationPartnerAuditService = $certificationPartnerAuditService;
        $this->userService = $userService;
    }

    public function __invoke(ComputeCertificationPartnerAudit $computeCertificationPartnerAudit)
    {
        $user = $this->userService->getById($computeCertificationPartnerAudit->getUserId());
        $certificationPartnerAudit = $this->certificationPartnerAuditService->getById($computeCertificationPartnerAudit->getCertificationPartnerAuditId());
        try {
            if ($certificationPartnerAudit->getState() !== CertificationPartnerAuditStates::PENDING_COMPUTATION()->getValue()) { // Avoid duplicate operation due to race condition
                throw new WedofBadRequestHttpException('Erreur, l\'audit ne peut pas être calculé dans cet état ' . $certificationPartnerAudit->getState());
            }
            $certificationPartnerAudit = $this->certificationPartnerAuditService->compute($certificationPartnerAudit, $computeCertificationPartnerAudit->hasValidCatalog());
            if ($certificationPartnerAudit->getState() === CertificationPartnerAuditStates::IN_PROGRESS()->getValue() && $computeCertificationPartnerAudit->getComplete()) {
                $this->certificationPartnerAuditService->completeAutomatically($certificationPartnerAudit, $user, $computeCertificationPartnerAudit->getUpdateCompliance(), $computeCertificationPartnerAudit->getSuspend());
            }
            $this->nextMessages($computeCertificationPartnerAudit->getNextMessages());
        } catch (Throwable $e) {
            $this->certificationPartnerAuditService->failed($certificationPartnerAudit, 'Erreur lors de la collecte des données');
            $this->logger->error('[Audit] Error ' . $computeCertificationPartnerAudit->getCertificationPartnerAuditId());
            $this->logger->error($e);
        }
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
