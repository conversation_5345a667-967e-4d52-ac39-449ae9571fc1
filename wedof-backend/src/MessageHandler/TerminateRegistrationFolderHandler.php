<?php

namespace App\MessageHandler;

use App\Message\TerminateRegistrationFolder;
use App\Service\RegistrationFolderReasonService;
use App\Service\RegistrationFolderService;
use App\Service\UserService;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Throwable;

class TerminateRegistrationFolderHandler extends WithNextMessage implements MessageHandlerInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    private RegistrationFolderReasonService $registrationFolderReasonService;
    private RegistrationFolderService $registrationFolderService;
    private UserService $userService;

    public function __construct(RegistrationFolderService $registrationFolderService, RegistrationFolderReasonService $registrationFolderReasonService, UserService $userService, MessageBusInterface $messageBus)
    {
        parent::__construct($messageBus);
        $this->registrationFolderService = $registrationFolderService;
        $this->registrationFolderReasonService = $registrationFolderReasonService;
        $this->userService = $userService;
    }

    /**
     * @param TerminateRegistrationFolder $terminateRegistrationFolder
     */
    public function __invoke(TerminateRegistrationFolder $terminateRegistrationFolder)
    {
        $user = $this->userService->getById($terminateRegistrationFolder->getUserId());
        try {
            $registrationFolder = $this->registrationFolderService->getByExternalId($terminateRegistrationFolder->getExternalId());
            if ($registrationFolder) {
                $reason = $terminateRegistrationFolder->getCodeReason() ? $this->registrationFolderReasonService->getReasonByCodeTerminated($terminateRegistrationFolder->getCodeReason()) : null;
                $terminatedDate = $terminateRegistrationFolder->getTerminatedDate();
                $_SERVER['OVERRIDE_CLIENT_ADDR'] = $terminateRegistrationFolder->getIpAddress();
                $this->registrationFolderService->terminate($registrationFolder, $terminateRegistrationFolder->getAbsenceDuration(), $reason, $terminatedDate, $user);
                unset($_SERVER['OVERRIDE_CLIENT_ADDR']);
            }
            $this->nextMessages($terminateRegistrationFolder->getNextMessages());
        } catch (Throwable $e) {
            $this->logger->error($e);
        }
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
