<?php

namespace App\MessageHandler;

use App\Library\utils\enums\DataProviders;
use App\Message\SynchronizeCatalog;
use App\Service\CatalogService;
use App\Service\ConnectionService;
use App\Service\OrganismService;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Throwable;

class SynchronizeCatalogHandler extends WithNextMessage implements MessageHandlerInterface, LoggerAwareInterface
{
    private OrganismService $organismService;
    private ConnectionService $connectionService;
    private CatalogService $catalogService;
    private LoggerInterface $logger;

    public function __construct(OrganismService $organismService, ConnectionService $connectionService, CatalogService $catalogService, MessageBusInterface $messageBus)
    {
        parent::__construct($messageBus);
        $this->organismService = $organismService;
        $this->connectionService = $connectionService;
        $this->catalogService = $catalogService;
    }

    public function __invoke(SynchronizeCatalog $synchronizeCatalog)
    {
        try {
            $organism = $this->organismService->getBySiret($synchronizeCatalog->getSiret());
            $dataProvider = $synchronizeCatalog->getDataProvider();
            if ($organism && $this->connectionService->hasAccess($organism, $dataProvider)) {
                if ($synchronizeCatalog->useOpenData() && $dataProvider->equals(DataProviders::CPF())) {
                    $this->logger->debug('[SynchronizeCatalog] Start SynchronizeCatalog OpenData ' . $synchronizeCatalog->getSiret());
                    $this->catalogService->synchronizeCpfWithOpenData($organism, $synchronizeCatalog->getCertificationCodes());
                } else {
                    $this->logger->error('[SynchronizeCatalog] Start SynchronizeCatalog All ' . $synchronizeCatalog->getSiret());
                    $this->catalogService->synchronizeForDataProvider($organism, $synchronizeCatalog->getDataProvider(), $synchronizeCatalog->getCertificationCodes(), $synchronizeCatalog->getTrainingStatuses(), $synchronizeCatalog->getOptions());
                }
            }
            $this->nextMessages($synchronizeCatalog->getNextMessages());
        } catch (Throwable $e) {
            $this->logger->error('[SynchronizeCatalog] Error SynchronizeCatalog ' . $synchronizeCatalog->getSiret());
            $this->logger->error($e);
        }
    }

    /**
     * @param LoggerInterface $logger
     * @return void
     */
    public function setLogger(LoggerInterface $logger)
    {
        $this->logger = $logger;
    }
}
