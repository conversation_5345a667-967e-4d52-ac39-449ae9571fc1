<?php

namespace App\MessageHandler;

use App\Message\BillRegistrationFolder;
use App\Service\RegistrationFolderService;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Throwable;

class BillRegistrationFolderHandler extends WithNextMessage implements MessageHandlerInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    private RegistrationFolderService $registrationFolderService;

    public function __construct(RegistrationFolderService $registrationFolderService, MessageBusInterface $messageBus)
    {
        parent::__construct($messageBus);
        $this->registrationFolderService = $registrationFolderService;
    }

    /**
     * @param BillRegistrationFolder $billRegistrationFolder
     */
    public function __invoke(BillRegistrationFolder $billRegistrationFolder)
    {
        try {
            $registrationFolder = $this->registrationFolderService->getByExternalId($billRegistrationFolder->getExternalId());
            if ($registrationFolder) {
                $_SERVER['OVERRIDE_CLIENT_ADDR'] = $billRegistrationFolder->getIpAddress();
                $this->registrationFolderService->bill($registrationFolder, $billRegistrationFolder->getBillNumber(), $billRegistrationFolder->getVatRate());
                unset($_SERVER['OVERRIDE_CLIENT_ADDR']);
            }
            $this->nextMessages($billRegistrationFolder->getNextMessages());
        } catch (Throwable $e) {
            $this->logger->error($e);
        }
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
