<?php

namespace App\MessageHandler;

use App\Message\SynchronizeEvaluations;
use App\Service\ConnectionService;
use App\Service\DataProviders\BaseApiService;
use App\Service\DataProviders\CpfApiService;
use App\Service\EvaluationService;
use App\Service\OrganismService;
use App\Service\TrainingActionService;
use App\Service\TrainingService;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Throwable;

class SynchronizeEvaluationsH<PERSON>ler extends WithNextMessage implements MessageHandlerInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    private OrganismService $organismService;
    private EvaluationService $evaluationService;
    private TrainingService $trainingService;
    private TrainingActionService $trainingActionService;
    private ConnectionService $connectionService;

    public function __construct(OrganismService $organismService, ConnectionService $connectionService, EvaluationService $evaluationService, TrainingService $trainingService, TrainingActionService $trainingActionService, MessageBusInterface $messageBus)
    {
        parent::__construct($messageBus);
        $this->organismService = $organismService;
        $this->connectionService = $connectionService;
        $this->evaluationService = $evaluationService;
        $this->trainingService = $trainingService;
        $this->trainingActionService = $trainingActionService;
    }

    /**
     * @param SynchronizeEvaluations $synchronizeEvaluations
     */
    public function __invoke(SynchronizeEvaluations $synchronizeEvaluations)
    {
        try {
            /** @var CpfApiService $apiService */
            $apiService = BaseApiService::getApiServiceByDataProvider($synchronizeEvaluations->getDataProvider());
            //on passe par l'organisme pour éviter de répéter les mêmes actions pour des utilisateurs qui seraient en multi organismes
            $organism = $this->organismService->getOrganism(['siret' => $synchronizeEvaluations->getSiret()]);
            if (!$this->connectionService->hasAccess($organism, $synchronizeEvaluations->getDataProvider())) {
                $this->logger->info("[SynchronizeEvaluations] no connection found for this siret " . $synchronizeEvaluations->getSiret() . " and data provider " . $synchronizeEvaluations->getDataProvider()->getValue() . " cancel");
                return;
            }

            $this->logger->info("[SynchronizeEvaluations] started for siret " . $synchronizeEvaluations->getSiret());
            $evaluationsRawData = $apiService->getEvaluationsRawData($organism);
            $evaluationsTrainingsRawData = [];
            $evaluationsOrganismsRawData = [
                "_sum" => 0,
                "_trainings" => 0,
                'NOTE_MOYENNE' => 0
            ];
            foreach ($evaluationsRawData as $evaluationRawData) {
                if (!isset($evaluationsTrainingsRawData[$evaluationRawData["NUMERO_FORMATION"]])) {
                    $evaluationsTrainingsRawData[$evaluationRawData["NUMERO_FORMATION"]] = [
                        'NOTE_MOYENNE' => $evaluationRawData["NOTE_MOYENNE_FORMATION"],
                        'NB_AVIS' => intval($evaluationRawData["NB_AVIS_ACTION"])
                    ];
                    //only active formation count for organism evaluation...
                    if ($evaluationRawData['STATUT_FORMATION'] == 'Active') {
                        $evaluationsOrganismsRawData['_sum'] += floatval(str_replace(",", ".", $evaluationRawData["NOTE_MOYENNE_FORMATION"]));
                        $evaluationsOrganismsRawData['_trainings'] += 1;
                        $evaluationsOrganismsRawData['NOTE_MOYENNE'] = $evaluationsOrganismsRawData['_sum'] / $evaluationsOrganismsRawData['_trainings'];
                    }
                } else {
                    $evaluationsTrainingsRawData[$evaluationRawData["NUMERO_FORMATION"]]['NB_AVIS'] += intval($evaluationRawData["NB_AVIS_ACTION"]);
                }
                $trainingAction = $this->trainingActionService->getByExternalId($apiService->getTrainingActionExternalId($evaluationRawData["NUMERO_FORMATION"], $evaluationRawData["NUMERO_ACTION"]), $synchronizeEvaluations->getDataProvider(), ['createIfNotExist' => true]);
                if ($trainingAction) {
                    $this->evaluationService->create($evaluationRawData, $trainingAction);
                } else {
                    $this->logger->error("[SynchronizeEvaluations] trainingAction not found...");
                }
            }
            foreach ($evaluationsTrainingsRawData as $trainingId => $values) {
                $training = $this->trainingService->getByExternalId($trainingId, $synchronizeEvaluations->getDataProvider(), ['createIfNotExist' => true]);
                if ($training) {
                    $this->logger->info("[SynchronizeEvaluations] training found...");
                    $this->evaluationService->createForTraining($values, $training);
                } else {
                    $this->logger->error("[SynchronizeEvaluations] training not found...");
                }
            }
            $this->evaluationService->createForOrganism($evaluationsOrganismsRawData, $organism);
            $this->logger->info("[SynchronizeEvaluations] " . sizeof($evaluationsRawData) . " evaluations synced");

            $this->nextMessages($synchronizeEvaluations->getNextMessages());
        } catch (Throwable $e) {
            $this->logger->error($e);
        }
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
