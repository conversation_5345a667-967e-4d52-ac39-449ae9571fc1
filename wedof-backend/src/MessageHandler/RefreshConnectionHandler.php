<?php

namespace App\MessageHandler;

use App\Exception\WedofConnectionException;
use App\Message\RefreshConnection;
use App\Service\ConnectionService;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Throwable;

class RefreshConnectionHandler extends WithNextMessage implements MessageHandlerInterface, LoggerAwareInterface
{
    private ConnectionService $connectionService;
    private LoggerInterface $logger;

    public function __construct(ConnectionService $connectionService, MessageBusInterface $messageBus)
    {
        parent::__construct($messageBus);
        $this->connectionService = $connectionService;
    }

    /**
     * @param RefreshConnection $refreshConnection
     * @throws WedofConnectionException
     */
    public function __invoke(RefreshConnection $refreshConnection)
    {
        $connection = $this->connectionService->getById($refreshConnection->getIdConnection());
        if (!$connection) {
            throw new WedofConnectionException("Aucune connection du type " . $connection->getDataProvider() . " existant pour l'organisme " . $connection->getOrganism()->getSiret());
        }
        try {
            if (!$this->connectionService->refreshConnection($connection)) {
                $this->logger->info("[RefreshConnection][" . $connection->getOrganism()->getSiret() . "] " . $connection->getDataProvider() . " Access not refreshed (401, 403, 503...)");
            }
        } catch (Throwable $e) {
            $this->logger->error($e);
            throw new WedofConnectionException("[RefreshConnection][" . $connection->getOrganism()->getSiret() . "] " . $connection->getDataProvider() . " Access" . $e->getMessage()); //in order to retry the task
        }
        $this->nextMessages($refreshConnection->getNextMessages());
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}