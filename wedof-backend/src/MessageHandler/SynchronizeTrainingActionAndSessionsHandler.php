<?php

namespace App\MessageHandler;

use App\Library\utils\enums\SessionStates;
use App\Library\utils\enums\TrainingActionStates;
use App\Message\SynchronizeTrainingActionAndSessions;
use App\Repository\SessionRepository;
use App\Repository\TrainingActionRepository;
use App\Service\CatalogService;
use App\Service\DataProviders\BaseApiService;
use App\Service\DataProviders\CpfApiService;
use App\Service\DataProviders\KairosAifApiService;
use App\Service\SessionService;
use App\Service\TrainingActionService;
use App\Service\TrainingService;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use ErrorException;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Throwable;

class SynchronizeTrainingActionAndSessionsHandler extends WithNextMessage implements MessageHandlerInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    private TrainingActionService $trainingActionService;
    private TrainingActionRepository $trainingActionRepository;
    private TrainingService $trainingService;
    private SessionService $sessionService;
    private SessionRepository $sessionRepository;
    private CatalogService $catalogService;

    public function __construct(TrainingService $trainingService, TrainingActionService $trainingActionService, TrainingActionRepository $trainingActionRepository, MessageBusInterface $messageBus, SessionService $sessionService, SessionRepository $sessionRepository, CatalogService $catalogService)
    {
        parent::__construct($messageBus);
        $this->trainingActionService = $trainingActionService;
        $this->trainingActionRepository = $trainingActionRepository;
        $this->trainingService = $trainingService;
        $this->sessionService = $sessionService;
        $this->sessionRepository = $sessionRepository;
        $this->catalogService = $catalogService;
    }

    /**
     * @throws Throwable
     * @throws ORMException|ErrorException|OptimisticLockException
     */
    public function __invoke(SynchronizeTrainingActionAndSessions $synchronizeTrainingActionAndSessions)
    {
        $options = $synchronizeTrainingActionAndSessions->getOptions();
        $rawData = $synchronizeTrainingActionAndSessions->getTrainingActionRawData();
        $fullRefresh = $synchronizeTrainingActionAndSessions->getIsFullOrganismRefresh();
        if (empty($rawData)) {
            $this->trainingActionService->getByExternalId($synchronizeTrainingActionAndSessions->getTrainingActionExternalId(), $synchronizeTrainingActionAndSessions->getDataProvider(), array_merge(['createIfNotExist' => true, 'breakDeepCall' => true, 'daysMaxSinceLastUpdate' => 1], $options));
        } else {
            /** @var CpfApiService|KairosAifApiService $apiService */
            $apiService = BaseApiService::getCatalogApiServiceByDataProvider($synchronizeTrainingActionAndSessions->getDataProvider());
            $training = $this->trainingService->getById($synchronizeTrainingActionAndSessions->getTrainingId());
            $organism = $training->getOrganism();
            $trainingActionBeforeUpdate = $this->trainingActionRepository->findOneByExternalId($rawData['externalId']);
            $trainingActionStateBeforeUpdate = $trainingActionBeforeUpdate ? $trainingActionBeforeUpdate->getState() : null;
            $trainingAction = $this->trainingActionService->createOrUpdate($rawData, $training, ['noRefresh' => true]);  // Raw data comes from above so no need to get it again
            // If archived before and after, don't sync children
            if ($trainingActionStateBeforeUpdate !== TrainingActionStates::ARCHIVED()->getValue() || $trainingAction->getState() !== TrainingActionStates::ARCHIVED()->getValue()) {
                $sessionsRawData = $apiService->getSessionsRawData($trainingAction);
                if (!empty($options['fullSessions'])) {
                    try {
                        $sessionsFullRawData = [];
                        foreach ($sessionsRawData as $sessionPartialRawData) {
                            if (isset($sessionsRawData['statusLabel']) && $sessionsRawData['statusLabel'] === 'ARCHIVED') {
                                $existingSession = $this->sessionRepository->findOneByExternalId($sessionsRawData['externalId']);
                            }
                            // If archived before and after, don't sync details
                            if (isset($existingSession) && $existingSession->getState() === SessionStates::ARCHIVED()->getValue()) {
                                $sessionsFullRawData[] = $sessionsRawData;
                            } else {
                                $sessionsFullRawData[] = $apiService->getSessionRawData($trainingAction, $sessionPartialRawData['id']);
                            }
                        }
                        $sessionsRawData = $sessionsFullRawData;
                    } catch (Throwable $exception) {
                        if ($fullRefresh) {
                            $this->catalogService->manageCpfCatalogSynchronizeError($organism, $exception);
                        }
                        $this->logger->error('[SynchronizeCatalog] Error sync full sessions: ' . $exception->getMessage());
                    }
                }
                $this->sessionService->createOrUpdateSessions($sessionsRawData, $trainingAction);
            }
            if ($fullRefresh) {
                $this->catalogService->updateCpfCatalogSynchronizeProgress($organism, 1);
            }
            try {
                $apiService->updateEvaluationsForTrainingAction($trainingAction);
            } catch (Throwable $t) {
                $this->logger->error('[SynchronizeCatalog] updateEvaluationsForTrainingAction: ' . $t->getMessage()); // Don't throw so that we avoid replaying the message
            }
        }
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    /**
     * @return LoggerInterface
     */
    public function getLogger(): LoggerInterface
    {
        return $this->logger;
    }
}
