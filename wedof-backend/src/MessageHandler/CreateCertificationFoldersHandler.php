<?php

namespace App\MessageHandler;

use App\Entity\RegistrationFolder;
use App\Message\CreateCertificationFolder;
use App\Message\CreateCertificationFolders;
use App\Repository\CertificationRepository;
use App\Repository\RegistrationFolderRepository;
use App\Service\CertificationFolderService;
use App\Service\OrganismService;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Throwable;

class CreateCertificationFoldersHandler extends WithNextMessage implements MessageHandlerInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    private CertificationFolderService $certificationFolderService;
    private OrganismService $organismService;
    private CertificationRepository $certificationRepository;
    private RegistrationFolderRepository $registrationFolderRepository;

    public function __construct(CertificationFolderService $certificationFolderService, OrganismService $organismService, CertificationRepository $certificationRepository, RegistrationFolderRepository $registrationFolderRepository, MessageBusInterface $messageBus)
    {
        parent::__construct($messageBus);
        $this->certificationFolderService = $certificationFolderService;
        $this->organismService = $organismService;
        $this->certificationRepository = $certificationRepository;
        $this->registrationFolderRepository = $registrationFolderRepository;
    }

    /**
     * @param CreateCertificationFolders $createCertificationFolders
     */
    public function __invoke(CreateCertificationFolders $createCertificationFolders)
    {
        try {
            $certificationId = $createCertificationFolders->getCertificationId();
            $siret = $createCertificationFolders->getSiret();
            $organism = $this->organismService->getBySiret($siret);
            $certification = $this->certificationRepository->find($certificationId);
            $registrationFolders = $this->registrationFolderRepository->findAllForCertificationFolderCreation($certification, $organism);
            /** @var RegistrationFolder $registrationFolder */
            foreach ($registrationFolders as $registrationFolder) {
                $this->messageBus->dispatch(new CreateCertificationFolder($registrationFolder->getExternalId()));
            }
            $this->nextMessages($createCertificationFolders->getNextMessages());
        } catch (Throwable $e) {
            $this->logger->error($e);
        }
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
