<?php

namespace App\MessageHandler;

use App\Entity\CertificationFolder;
use App\Message\PropagateSkillSetsFromTraining;
use App\Repository\CertificationFolderRepository;
use App\Repository\TrainingRepository;
use App\Service\CertificationFolderService;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Throwable;

class PropagateSkillSetsFromTrainingHandler extends WithNextMessage implements MessageHandlerInterface, LoggerAwareInterface
{
    private CertificationFolderRepository $certificationFolderRepository;
    private CertificationFolderService $certificationFolderService;
    private LoggerInterface $logger;
    private TrainingRepository $trainingRepository;

    public function __construct(CertificationFolderRepository $certificationFolderRepository, CertificationFolderService $certificationFolderService, MessageBusInterface $messageBus, TrainingRepository $trainingRepository)
    {
        parent::__construct($messageBus);
        $this->certificationFolderRepository = $certificationFolderRepository;
        $this->certificationFolderService = $certificationFolderService;
        $this->trainingRepository = $trainingRepository;
    }

    /**
     * @param PropagateSkillSetsFromTraining $propagateSkillSetsFromTraining
     * @throws Throwable
     */
    public function __invoke(PropagateSkillSetsFromTraining $propagateSkillSetsFromTraining)
    {
        try {
            $trainingId = $propagateSkillSetsFromTraining->getTrainingId();
            $training = $this->trainingRepository->find($trainingId);
            $skillSets = $training->getSkillSets();
            $certificationFolders = $this->certificationFolderRepository->findAllByTraining($training);
            /** @var CertificationFolder $certificationFolder */
            foreach ($certificationFolders as $certificationFolder) {
                $this->certificationFolderService->setSkillSets($certificationFolder, $skillSets, true);
                $this->certificationFolderService->save($certificationFolder);
            }
            $this->nextMessages($propagateSkillSetsFromTraining->getNextMessages());
        } catch (Throwable $e) {
            $this->logger->error($e);
        }
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}