<?php

namespace App\MessageHandler;

use App\Library\utils\enums\DataProviders;
use App\Message\SynchronizePartners;
use App\Service\CertificationService;
use App\Service\DataProviders\BaseApiService;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Throwable;

class SynchronizePartnersHandler extends WithNextMessage implements MessageHandlerInterface, LoggerAwareInterface
{
    private CertificationService $certificationService;
    private LoggerInterface $logger;

    public function __construct(CertificationService $certificationService, MessageBusInterface $messageBus)
    {
        parent::__construct($messageBus);
        $this->certificationService = $certificationService;
    }

    public function __invoke(SynchronizePartners $synchronizePartners)
    {
        try {
            $certification = $this->certificationService->getByCertifInfo($synchronizePartners->getCertifInfo());
            $dataProvider = DataProviders::from($certification->getDataProvider());
            $certificationApiService = BaseApiService::getCertificationApiServiceByDataProvider($dataProvider);
            $certificationApiService->updatePartnersForCertification($certification);
            $this->nextMessages($synchronizePartners->getNextMessages());
        } catch (Throwable $e) {
            $this->logger->error($e);
        }
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}