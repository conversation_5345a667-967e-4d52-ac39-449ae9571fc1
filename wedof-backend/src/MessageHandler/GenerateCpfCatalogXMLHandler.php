<?php

namespace App\MessageHandler;

use App\Event\Organism\OrganismEvents;
use App\Library\utils\SimpleXMLElementTools;
use App\Message\GenerateCpfCatalogXML;
use App\Service\CatalogService;
use App\Service\CatalogXMLService;
use App\Service\MailerService;
use App\Service\OrganismService;
use App\Service\UserService;
use DateTime;
use Exception;
use Psr\EventDispatcher\EventDispatcherInterface;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Filesystem\Filesystem;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Throwable;

class GenerateCpfCatalogXMLHandler extends WithNextMessage implements MessageHandlerInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    private UserService $userService;
    private MailerService $mailerService;
    private CatalogXMLService $catalogXMLService;
    private OrganismService $organismService;
    private CatalogService $catalogService;
    private EventDispatcherInterface $dispatcher;

    public function __construct(MessageBusInterface $messageBus, UserService $userService, MailerService $mailerService, CatalogXMLService $catalogXMLService, OrganismService $organismService, CatalogService $catalogService, EventDispatcherInterface $dispatcher)
    {
        parent::__construct($messageBus);
        $this->userService = $userService;
        $this->mailerService = $mailerService;
        $this->catalogXMLService = $catalogXMLService;
        $this->organismService = $organismService;
        $this->catalogService = $catalogService;
        $this->dispatcher = $dispatcher;
    }

    public function __invoke(GenerateCpfCatalogXML $generateCpfCatalogXML)
    {
        $user = $this->userService->getById($generateCpfCatalogXML->getUserId());
        try {
            $organism = $user->getMainOrganism();
            $cpfCatalogMetadata = $organism->getCpfCatalogMetadata();
            if ($cpfCatalogMetadata['export']['state'] !== 'inProgress') {
                throw new Exception("[CatalogXML] erreur l'export est à l'état " . $cpfCatalogMetadata['export']['state']);
            }
            $result = $this->catalogXMLService->generateCpfCatalogXML($organism);
            $baseFileName = $organism->getSiret() . '_catalog_' . date('Ymd\TH_i_s'); // No 'dash' as they remove them
            $basePath = __DIR__ . "/../../data/catalogXML/";
            // XML file
            $xmlString = $result['xml'];
            SimpleXMLElementTools::validateCdcXML($xmlString, '/../../../data/lheo_v5r2.xsd');
            $filesystem = new Filesystem();
            $fileName = $baseFileName . '.xml';
            $xmlFilePath = $basePath . $fileName;
            $filesystem->dumpFile($xmlFilePath, $xmlString);
            // Report
            $report = $result['report'];
            $separator = ';';
            $reportFilePath = $basePath . $baseFileName . '_report.csv';
            $reportFile = fopen($reportFilePath, 'w');
            foreach ($report as $reportRow) {
                fputcsv($reportFile, array_values($reportRow), $separator);
            }
            fclose($reportFile);
            // Mail
            $this->mailerService->sendCpfCatalogXMLFile($user, $xmlFilePath, $reportFilePath);
            $cpfCatalogMetadata['export']['fileName'] = $fileName;
            $cpfCatalogMetadata['export']['endDate'] = (new DateTime('now'))->format('Y-m-d\TH:i:s.u\Z');
            $cpfCatalogMetadata['export']['state'] = 'done';
            $organism->setCpfCatalogMetadata($cpfCatalogMetadata);
            $this->organismService->save($organism);
            $this->dispatcher->dispatch(new OrganismEvents($organism), OrganismEvents::CPF_CATALOG_EXPORT_FINISHED);
            $this->nextMessages($generateCpfCatalogXML->getNextMessages());
        } catch (Throwable $e) {
            $this->logger->error($e);
            try {
                $this->catalogService->manageCpfCatalogExportError($organism, $e);
            } catch (Throwable $t) {
            }
        }
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
