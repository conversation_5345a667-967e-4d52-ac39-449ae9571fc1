<?php

namespace App\MessageHandler;

use App\Event\MonitoringEvents;
use App\Exception\WedofCpfBackendException;
use App\Library\utils\enums\RegistrationFolderSortParams;
use App\Library\utils\enums\RegistrationFolderStates;
use App\Message\OneBatchRegistrationFolders;
use App\Service\ConnectionService;
use App\Service\DataProviders\BaseApiService;
use App\Service\OrganismService;
use App\Service\RegistrationFolderService;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Throwable;

class OneBatchRegistrationFoldersHandler extends WithNextMessage implements MessageHandlerInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    private OrganismService $organismService;
    private ConnectionService $connectionService;
    private EventDispatcherInterface $dispatcher;
    private RegistrationFolderService $registrationFolderService;

    public function __construct(OrganismService $organismService, ConnectionService $connectionService, RegistrationFolderService $registrationFolderService, EventDispatcherInterface $dispatcher, MessageBusInterface $messageBus)
    {
        parent::__construct($messageBus);
        $this->dispatcher = $dispatcher;
        $this->organismService = $organismService;
        $this->connectionService = $connectionService;
        $this->registrationFolderService = $registrationFolderService;
    }

    /**
     * @param OneBatchRegistrationFolders $oneBatchRegistrationFolders
     */
    public function __invoke(OneBatchRegistrationFolders $oneBatchRegistrationFolders)
    {
        $nextMessageSent = false;
        $organism = $this->organismService->getBySiret($oneBatchRegistrationFolders->getSiret());
        try {
            $apiService = BaseApiService::getRegistrationFolderApiServiceByDataProvider($oneBatchRegistrationFolders->getDataProvider());
            if ($oneBatchRegistrationFolders->getDisableWedofEvents()) {
                $_SERVER['NO_DISPATCH_WEDOF_EVENTS'] = true;
            }
            $rawData = $apiService->getRegistrationFoldersRawData($organism, [
                'state' => RegistrationFolderStates::ALL(),
                'skip' => $oneBatchRegistrationFolders->getSkip(),
                'limit' => $oneBatchRegistrationFolders->getLimit(),
                'order' => 'ASC',
                'sort' => RegistrationFolderSortParams::DATE_DEBUT_SESSION()
            ]);
            $externalIdsProvider = array_column($rawData, 'id');
            $missingExternalIds = array_diff($externalIdsProvider, $this->registrationFolderService->listExternalIdsForOrganismAndDataProvider($organism, $oneBatchRegistrationFolders->getDataProvider()));
            if (count($missingExternalIds) > 0) {
                $this->logger->info("[OneBatchRegistrationFolders] missing: " . join(',', $missingExternalIds));
                foreach ($missingExternalIds as $externalId) {
                    $this->registrationFolderService->createFromRawData($oneBatchRegistrationFolders->getDataProvider(), $rawData[array_search($externalId, $externalIdsProvider)]);
                }
            }
            $newCountWEDOF = $this->registrationFolderService->countByOrganismAndDataProvider($organism, $oneBatchRegistrationFolders->getDataProvider());
            $connection = $organism->getConnectionForDataProvider($oneBatchRegistrationFolders->getDataProvider());
            $connectionInitialized = $connection->isInitialized();
            if ($newCountWEDOF >= $oneBatchRegistrationFolders->getCachedTotalRegistrationFolders() && !$connectionInitialized
                || sizeof($rawData) === 0
            ) {
                if ($oneBatchRegistrationFolders->getDisableWedofEvents()) {
                    $_SERVER['NO_DISPATCH_WEDOF_EVENTS'] = false;
                }
                $this->connectionService->finishInitialize($connection);
                $nextMessageSent = true;
            } else if (!$connectionInitialized && $oneBatchRegistrationFolders->getCachedTotalRegistrationFolders() > 0) {
                $this->logger->debug("[OneBatchRegistrationFolders] remaining: " . ($oneBatchRegistrationFolders->getCachedTotalRegistrationFolders() - $newCountWEDOF) . " to finish initialize");
                //next message
                $message = new OneBatchRegistrationFolders(
                    $oneBatchRegistrationFolders->getDataProvider(),
                    $organism->getSiret(),
                    $oneBatchRegistrationFolders->getLimit(),
                    $oneBatchRegistrationFolders->getSkip() + $oneBatchRegistrationFolders->getLimit(),
                    $oneBatchRegistrationFolders->getCachedTotalRegistrationFolders(),
                    $oneBatchRegistrationFolders->getDisableWedofEvents());
                $this->messageBus->dispatch($message);
                $nextMessageSent = true;
            }
        } catch (Throwable $e) {
            $this->logger->error($e);
        } finally {
            if (!$nextMessageSent && $oneBatchRegistrationFolders->getRetry() < 3) {
                $message = new OneBatchRegistrationFolders(
                    $oneBatchRegistrationFolders->getDataProvider(),
                    $organism->getSiret(),
                    $oneBatchRegistrationFolders->getLimit(),
                    $oneBatchRegistrationFolders->getSkip(),
                    $oneBatchRegistrationFolders->getCachedTotalRegistrationFolders(),
                    $oneBatchRegistrationFolders->getDisableWedofEvents(),
                    true,
                    $oneBatchRegistrationFolders->getRetry() + 1);
                $this->messageBus->dispatch($message);
            } else if (!$nextMessageSent) {
                $this->dispatcher->dispatch(new MonitoringEvents(new WedofCpfBackendException("Problème d'initialisation ou rafraichissements des dossiers pour  : " . $organism->getSiret())), MonitoringEvents::API_PROVIDER_ERROR);
            }
            if ($oneBatchRegistrationFolders->getDisableWedofEvents()) {
                $_SERVER['NO_DISPATCH_WEDOF_EVENTS'] = false;
            }
        }
        $this->nextMessages($oneBatchRegistrationFolders->getNextMessages());
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
