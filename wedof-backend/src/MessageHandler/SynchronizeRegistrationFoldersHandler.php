<?php

namespace App\MessageHandler;

use App\Event\MonitoringEvents;
use App\Exception\WedofCpfBackendException;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\enums\RegistrationFolderBillingStates;
use App\Library\utils\enums\RegistrationFolderSortParams;
use App\Library\utils\enums\RegistrationFolderStates;
use App\Message\SynchronizeRegistrationFolders;
use App\Message\SynchronizeStateRegistrationFolders;
use App\Service\ConnectionService;
use App\Service\DataProviders\BaseApiService;
use App\Service\OrganismService;
use App\Service\RegistrationFolderService;
use PHPUnit\Exception;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Throwable;

class SynchronizeRegistrationFoldersHandler extends WithNextMessage implements MessageHandlerInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    private OrganismService $organismService;
    private EventDispatcherInterface $dispatcher;
    private RegistrationFolderService $registrationFolderService;
    private ConnectionService $connectionService;

    public function __construct(OrganismService $organismService, ConnectionService $connectionService, RegistrationFolderService $registrationFolderService, MessageBusInterface $messageBus, EventDispatcherInterface $dispatcher)
    {
        parent::__construct($messageBus);
        $this->organismService = $organismService;
        $this->connectionService = $connectionService;
        $this->registrationFolderService = $registrationFolderService;
        $this->dispatcher = $dispatcher;
    }

    /**
     * @param SynchronizeRegistrationFolders $synchronizeRegistrationFolders
     */
    public function __invoke(SynchronizeRegistrationFolders $synchronizeRegistrationFolders)
    {
        try {
            $apiService = BaseApiService::getRegistrationFolderApiServiceByDataProvider($synchronizeRegistrationFolders->getDataProvider());
            //on passe par l'organisme pour éviter de répéter les mêmes actions pour des utilisateurs qui seraient en multi organismes
            $organism = $this->organismService->getOrganism(['siret' => $synchronizeRegistrationFolders->getSiret()]);
            if (!$this->connectionService->hasAccess($organism, $synchronizeRegistrationFolders->getDataProvider())) {
                $this->logger->info("[SynchronizeFolders] no " . $synchronizeRegistrationFolders->getDataProvider() . " valid connection for this siret " . $synchronizeRegistrationFolders->getSiret() . " cancel");
                return;
            }
            $sort = $synchronizeRegistrationFolders->getSort();

            $registrationFoldersRawData = $apiService->getRegistrationFoldersRawData($organism, [
                'state' => $synchronizeRegistrationFolders->getState(),
                'skip' => $synchronizeRegistrationFolders->getSkip(),
                'limit' => $synchronizeRegistrationFolders->getLimit(),
                'order' => $synchronizeRegistrationFolders->getOrder(),
                'sort' => $synchronizeRegistrationFolders->getSort()
            ], [
                'maxRetry' => 1
            ]);

            $total = 0;
            $new = 0;
            $updated = 0;

            foreach ($registrationFoldersRawData as $registrationFolderRawData) {
                try {
                    if (in_array($synchronizeRegistrationFolders->getState(), [RegistrationFolderStates::TO_BILL(), RegistrationFolderStates::BILLED()])) {
                        //query with state allBilling on cpf returns different objects than the other states queries.. so
                        //we need to match update patterns to keep code "clean" (lol)
                        $registrationFolderRawData['title'] = null;
                        $registrationFolderRawData['currentBillingState'] = RegistrationFolderStates::TO_BILL()->equals($synchronizeRegistrationFolders->getState()) ? RegistrationFolderBillingStates::TO_BILL()->getValue() : null;
                    }
                    if (!$this->registrationFolderService->checkRegistrationFolderExistsByExternalId($registrationFolderRawData['id'])) {
                        $this->registrationFolderService->createFromRawData(DataProviders::from($registrationFolderRawData['type']), $registrationFolderRawData);
                        $new++;
                    } else {
                        if ($this->registrationFolderService->updateFromRawData(DataProviders::from($registrationFolderRawData['type']), $registrationFolderRawData)) {
                            $updated++;
                        }
                    }
                    $total++;
                } catch (Exception $exception) {
                    $this->dispatcher->dispatch(new MonitoringEvents(new WedofCpfBackendException("Problème de synchronisation du dossier : " . $registrationFolderRawData['id'])), MonitoringEvents::API_PROVIDER_RFOLDER_ERROR);
                }
            }

            if (
                (($synchronizeRegistrationFolders->isAutoDiscover() && $sort->equals(RegistrationFolderSortParams::DATE_DERNIERE_ACTION()) && ($updated > 0 || $new > 0))
                    || //if at least one change detected we go further...
                    (!$sort->equals(RegistrationFolderSortParams::DATE_DERNIERE_ACTION()) && count($registrationFoldersRawData) > 0))
            ) {
                $message = new SynchronizeStateRegistrationFolders(
                    $synchronizeRegistrationFolders->getSiret(),
                    $synchronizeRegistrationFolders->getDataProvider(),
                    $synchronizeRegistrationFolders->getState(),
                    $synchronizeRegistrationFolders->getSort(),
                    $synchronizeRegistrationFolders->getLimit(),
                    ($synchronizeRegistrationFolders->getSkip() + $synchronizeRegistrationFolders->getLimit()));
                $this->messageBus->dispatch($message);
                $this->logger->info("[SynchronizeFolders][" . $organism->getName() . "] updated: $updated, new: $new, total: $total Registrations Folders changed go further... $sort");
            } else {
                if ($synchronizeRegistrationFolders->getTotalFoldersToInitialize()) {
                    $count = $this->registrationFolderService->countByOrganismAndDataProvider($organism, $synchronizeRegistrationFolders->getDataProvider());
                    if ($count >= $synchronizeRegistrationFolders->getTotalFoldersToInitialize()) {
                        $this->connectionService->finishInitialize($organism->getConnectionForDataProvider($synchronizeRegistrationFolders->getDataProvider()));
                    }
                }
                $this->logger->info("[SynchronizeFolders][" . $organism->getName() . "] " . $new . " new / " . $updated . " updated / " . $total . " total - Registrations Folders");
            }
            $this->nextMessages($synchronizeRegistrationFolders->getNextMessages());
        } catch (Throwable $e) {
            $this->logger->error($e);
        }
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
