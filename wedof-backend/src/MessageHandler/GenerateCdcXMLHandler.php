<?php

namespace App\MessageHandler;

use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofCdcBackendException;
use App\Exception\WedofSubscriptionException;
use App\Library\utils\SimpleXMLElementTools;
use App\Library\utils\Tools;
use App\Message\GenerateCdcXML;
use App\Repository\CertificationRepository;
use App\Service\CdcFileService;
use App\Service\CdcXMLService;
use App\Service\MailerService;
use App\Service\UserService;
use DateTime;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Filesystem\Filesystem;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Uid\Uuid;
use Throwable;


class GenerateCdcXMLHandler extends WithNextMessage implements MessageHandlerInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;
    private CertificationRepository $certificationRepository;
    private UserService $userService;
    private MailerService $mailerService;
    private CdcFileService $cdcFileService;
    private CdcXMLService $cdcXMLService;

    public function __construct(MessageBusInterface $messageBus, UserService $userService, MailerService $mailerService, CertificationRepository $certificationRepository, CdcFileService $cdcFileService, CdcXMLService $cdcXMLService)
    {
        parent::__construct($messageBus);
        $this->userService = $userService;
        $this->certificationRepository = $certificationRepository;
        $this->mailerService = $mailerService;
        $this->cdcFileService = $cdcFileService;
        $this->cdcXMLService = $cdcXMLService;
    }

    public function __invoke(GenerateCdcXML $generateCdcXML)
    {
        try {
            $user = $this->userService->getByEmail($generateCdcXML->getUserEmail());
            $organism = $user->getMainOrganism();
            $isAutomatedGeneration = $generateCdcXML->isIsAutomatedGeneration();
            try {
                $idClient = $organism->getCdcClientId();
                $idContrat = $organism->getCdcContractId();
                if (!$idClient || !$idContrat) {
                    throw new WedofBadRequestHttpException("Erreur, afin de générer le fichier XML vous devez remplir l'id client et l'id contrat de votre organisme dans votre profil Wedof.");
                }
                $idClientDepositor = $isAutomatedGeneration && $this->cdcFileService->isWedofDeposantForOrganism($organism) ? Tools::getEnvValue('WEDOF_ID_CLIENT') : $idClient;
                $idCertif = $generateCdcXML->getIdCertif();
                if ($idCertif) {
                    $certification = $this->certificationRepository->findOneBy(['id' => $idCertif]);
                    if (!$certification) {
                        throw new WedofBadRequestHttpException("Erreur, la certification avec l'id " . $idCertif . " n'existe pas.");
                    }
                    if (!$certification->isCertifier($user->getMainOrganism())) {
                        throw new WedofBadRequestHttpException("Erreur, vous n'avez pas accès à la certification associée à l'id " . $idCertif . " .");
                    }
                    $cdcData = $this->cdcFileService->generateCdcData($organism, $isAutomatedGeneration, $certification->getCertifInfo());
                } else {
                    $cdcData = $this->cdcFileService->generateCdcData($organism, $isAutomatedGeneration);
                }
                if (empty($cdcData)) {
                    throw new WedofBadRequestHttpException("Erreur, soit la certification demandée ne contient pas les données obligatoires (par ex. Mode d'obtention), soit aucun dossier complet à l'état Réussi n'a été trouvé, le fichier n'a donc pas pu être généré.");
                }
                $idFlux = Uuid::v4()->toRfc4122();
                $stats = array_map(function ($certificationContainer) {
                    return $certificationContainer['certificationStats'];
                }, $cdcData);
                $xmlString = $this->cdcXMLService->generateCdcXML($cdcData, $idClient, $idClientDepositor, $idContrat, $idFlux);
                SimpleXMLElementTools::validateCdcXML($xmlString, '/../../../data/certifications-cdc-v2.0.0.xsd');
                $fileName = $organism->getSiret() . '_';
                $fileName .= $idCertif ? $this->certificationRepository->findOneBy(['id' => $idCertif])->getExternalId() . '_' : '';
                $fileName .= date('Y-m-d\TH_i_s') . "_" . $idFlux;
                $fileNamePath = __DIR__ . "/../../data/cdcXML/" . $fileName . ".xml";
                $filesystem = new Filesystem();
                $filesystem->dumpFile($fileNamePath, $xmlString);
                $foldersExported = [];
                foreach ($stats as $stat) {
                    $foldersExported[] = $stat['foldersExported'];
                }
                $foldersExportedMerged = array_merge(...$foldersExported);
                $cdcFile = $this->cdcFileService->create($fileName, $foldersExportedMerged, $organism, $idFlux, $isAutomatedGeneration);
                // For the moment it may still be relevant to send it to the wedof team for debug even if $isAutomatedGeneration
                // If we remove the mail, the we should have a way to backup the generated files
                $this->mailerService->sendCdcXMLFile($organism, $user, $fileNamePath, $stats, $isAutomatedGeneration);
                if ($isAutomatedGeneration) {
                    $isSuccess = $this->cdcFileService->sendCdcXMLFileToCDC($organism, $fileName . ".xml", $fileNamePath);
                    if ($isSuccess) {
                        $this->cdcFileService->update($cdcFile, ['submissionDate' => new DateTime()]);
                    }
                }
            } catch (WedofBadRequestHttpException|WedofSubscriptionException $e) {
                $externalIdCertification = $generateCdcXML->getIdCertif() ? $this->certificationRepository->findOneBy(['id' => $generateCdcXML->getIdCertif()])->getExternalId() : null;
                $this->logger->error('Error XML CDC accrochage génération');
                $this->logger->error($e);
                $this->mailerService->sendCdcXMLError($organism, $user, $e->getMessage(), $isAutomatedGeneration, $externalIdCertification);
            } catch (WedofCdcBackendException $e) {
                $this->logger->error('Error XML CDC accrochage dépôt');
                $this->logger->error($e);
                $this->mailerService->sendCdcXMLError($organism, $user, "Erreur, un problème inattendu s'est produit lors du dépôt du fichier XML.", $isAutomatedGeneration);
            } catch (Throwable $e) {
                $this->logger->error('Error XML CDC accrochage inconnue');
                $this->logger->error($e);
                $this->mailerService->sendCdcXMLError($organism, $user, "Erreur, un problème inattendu s'est produit lors de la génération de votre fichier XML. Merci de vous rapprocher de notre équipe de support.", $isAutomatedGeneration);
            } finally {
                $this->nextMessages($generateCdcXML->getNextMessages());
            }
        } catch (Throwable $e) {
            $this->logger->error($e);
        }
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
