<?php

namespace App\MessageHandler;

use App\Message\CreateCertification;
use App\Service\CertificationService;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Throwable;

class CreateCertificationHandler extends WithNextMessage implements MessageHandlerInterface, LoggerAwareInterface
{
    private CertificationService $certificationService;
    private LoggerInterface $logger;

    public function __construct(CertificationService $certificationService, MessageBusInterface $messageBus)
    {
        parent::__construct($messageBus);
        $this->certificationService = $certificationService;
    }

    public function __invoke(CreateCertification $createCertification)
    {
        try {
            $this->certificationService->getCertification(['certifInfo' => $createCertification->getCertifInfo()], ['refresh' => true, 'franceCompetencesData' => $createCertification->getFranceCompetencesData()]);
            $this->nextMessages($createCertification->getNextMessages());
        } catch (Throwable $e) {
            $this->logger->error($e);
        }
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}