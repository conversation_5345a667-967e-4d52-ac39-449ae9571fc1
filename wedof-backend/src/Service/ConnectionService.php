<?php
// src/Service/ConnectionService.php
namespace App\Service;

use App\Entity\Connection;
use App\Entity\Organism;
use App\Event\Connection\ConnectionEvents;
use App\Event\MonitoringEvents;
use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofConnectionException;
use App\Library\utils\enums\ConnectionStates;
use App\Library\utils\enums\ConnectionTypes;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\enums\UserMessageTypes;
use App\Repository\ConnectionRepository;
use App\Service\DataProviders\BaseApiService;
use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Exception;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;

class ConnectionService
{
    private ConnectionRepository $connectionRepository;
    private MailerService $mailerService;
    private EventDispatcherInterface $dispatcher;

    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    /**
     * @param ConnectionRepository $connectionRepository
     * @param MailerService $mailerService
     * @param EventDispatcherInterface $dispatcher
     */
    public function __construct(ConnectionRepository $connectionRepository, MailerService $mailerService, EventDispatcherInterface $dispatcher)
    {
        $this->connectionRepository = $connectionRepository;
        $this->mailerService = $mailerService;
        $this->dispatcher = $dispatcher;
    }

    /**
     * @param Connection $connection
     * @return Connection
     */
    public function save(Connection $connection): Connection
    {
        return $this->connectionRepository->save($connection);
    }

    /**
     * @param Organism $organism
     * @param DataProviders $dataProvider
     * @param bool $forceRefresh
     * @return bool
     * @throws Exception|TransportExceptionInterface
     */
    public function hasAccess(Organism $organism, DataProviders $dataProvider, bool $forceRefresh = false): bool
    {

        $apiService = BaseApiService::getApiServiceByDataProvider($dataProvider);
        if ($apiService->requiresAuthentication()) {
            $connection = $organism->getConnectionForDataProvider($dataProvider);
            if (!$connection || !$connection->getCredentials() || $connection->getState() !== ConnectionStates::ACTIVE()->getValue()) {
                return false;
            }
            if ($forceRefresh || $connection->getState() === ConnectionStates::ACTIVE()->getValue()) {
                if ($forceRefresh || $connection->getExpiresOn() <= new DateTime()) {
                    return (bool)$this->authenticate($organism, $dataProvider, $connection->getCredentials(), false)['hasOrganismAccess'];
                } else {
                    return true;
                }
            } else {
                return false;
            }
        } else {
            return true;
        }
    }

    /**
     * @param Organism $organism
     * @param DataProviders $dataProvider
     * @param array $credentials
     * @param bool $checkOrganismAccess
     * @param bool $manual
     * @return array
     * @throws Exception
     * @throws TransportExceptionInterface
     */
    public function authenticate(Organism $organism, DataProviders $dataProvider, array $credentials, bool $checkOrganismAccess = true, bool $manual = false): array
    {
        $connection = $organism->getConnectionForDataProvider($dataProvider) ?? $this->create($organism, $dataProvider);
        $connection->setCredentials(); // remise des IDs à null
        $connection->setCredentials($credentials); // présauvegarde des identifiants
        $this->save($connection);
        $success = false;
        $previousState = $connection->getState();
        try {
            $access = BaseApiService::getApiServiceByDataProvider($dataProvider)->authenticate($connection, $checkOrganismAccess);
            $success = $access['hasAccess'] && $access['hasOrganismAccess'];
        } catch (WedofConnectionException $e) {
            $access = ['exception' => $e];
        }
        if ($success) {
            $connection->setExpiresOn($access['expiresOn']);
            $connection->setRefreshAt($access['refreshAt']);
            $connection->setLastRefresh($access['lastRefresh']);
            $connection->setFailedAt(null);
            $connection->setAuthenticationFailedCount(0);
            $connection->setState(ConnectionStates::ACTIVE()->getValue());
            $connection->setMessage(null);
            $connection->setExistAtDataProvider(true);
            if (isset($access['type'])) {
                $connection->setType($access['type']);
            }
            $connection->setMessageType(null);
        } else {
            $state = null;
            $maxAttempts = BaseApiService::getApiServiceByDataProvider(DataProviders::from($connection->getDataProvider()))->getMaxAttemptsBeforeStop();
            if (!empty($access['error'])) {
                switch ($access['error']) {
                    case "revoked":
                        $state = ConnectionStates::REVOKED();
                        $connection->setAuthenticationFailedCount($maxAttempts) - 1; //+1 in the next count
                        $connection->setMessageType(UserMessageTypes::ERROR()->getValue());
                        break;
                    case "unknown-siret":
                        $state = ConnectionStates::IN_PROGRESS();
                        // This error is CPF/EFP Specific, it's ugly but way more useful to help solve the issue
                        $connection->setMessage("Votre propre compte EFP n'a pas accès à EDOF. Obtenez cet accès puis contactez-nous pour activer l'habilitation.");
                        $connection->setAuthenticationFailedCount($maxAttempts - 1); //+1 in the next count
                        $connection->setMessageType(UserMessageTypes::WARN()->getValue());
                        break;
                    case "not-ready":
                        $state = ConnectionStates::IN_PROGRESS();
                        // This error is CPF/EFP Specific, it's ugly but way more useful to help solve the issue
                        $connection->setMessage("Vous n'avez pas fini d'initilialiser l'accès EDOF sur votre propre compte EFP. Finalisez votre inscription puis contactez-nous pour activer l'habilitation.");
                        $connection->setAuthenticationFailedCount($maxAttempts - 1); //+1 in the next count
                        $connection->setMessageType(UserMessageTypes::WARN()->getValue());
                        break;
                    case "wrong-siret":
                        $state = ConnectionStates::FAILED();
                        $connection->setMessage("L'habilitation porte sur le mauvais SIRET");
                        $connection->setAuthenticationFailedCount($maxAttempts - 1); //+1 in the next count
                        $connection->setMessageType(UserMessageTypes::ERROR()->getValue());
                        break;
                    case "insufficient-habilitation-level":
                        $state = ConnectionStates::FAILED();
                        $connection->setMessage("L'habilitation ne possède pas le rôle requis (GESTIONNAIRE EDOF)");
                        $connection->setAuthenticationFailedCount($maxAttempts - 1); //+1 in the next count
                        $connection->setMessageType(UserMessageTypes::ERROR()->getValue());
                        break;
                    case "no-habilitation-level":
                    case "wrong-habilitation-level":
                        $state = ConnectionStates::FAILED();
                        $connection->setMessage("L'habilitation ne possède pas un des rôles requis (LECTEUR EDOF ou GESTIONNAIRE EDOF)");
                        $connection->setAuthenticationFailedCount($maxAttempts - 1); //+1 in the next count
                        $connection->setMessageType(UserMessageTypes::ERROR()->getValue());
                        break;
                    case "credentials":
                        $state = ConnectionStates::FAILED();
                        $connection->setMessage("Mauvais identifiant / mot de passe");
                        $connection->setAuthenticationFailedCount($maxAttempts - 1); //+1 in the next count
                        $connection->setMessageType(UserMessageTypes::ERROR()->getValue());
                        break;
                    case "other":
                        $state = ConnectionStates::FAILED();
                        $connection->setMessage("Connexion échouée");
                        $connection->setAuthenticationFailedCount($maxAttempts - 1); //+1 in the next count
                        $connection->setMessageType(UserMessageTypes::ERROR()->getValue());
                        break;
                }
            }
            $count = ($connection->getAuthenticationFailedCount() ?? 0) + 1;
            $connection->setAuthenticationFailedCount($count);
            if (($count >= $maxAttempts && !isset($access['exception'])) || $manual) {
                $connection->setExpiresOn(null);
                $connection->setRefreshAt(null);
                $connection->setLastRefresh(null);
                $connection->setFailedAt(new DateTime());
                $connection->setState($state ?? ConnectionStates::FAILED());
                if (!$manual && $connection->getDataProvider() !== DataProviders::CPF()->getValue()) {
                    $this->mailerService->sendEmailOnConnectionFailed($connection);
                }
                $this->dispatcher->dispatch(new ConnectionEvents($connection), ConnectionEvents::AUTH_FAIL);
            } else {
                $date = new DateTime();
                $date->modify('+' . ($count) . ' minute'); //wait incrementally
                $connection->setRefreshAt($date);
                $connection->setExpiresOn($date);
                $connection->setLastRefresh(new DateTime());
                $connection->setState($state ?? ConnectionStates::ACTIVE());
                if ($state === ConnectionStates::REVOKED()) {
                    $this->dispatcher->dispatch(new ConnectionEvents($connection), ConnectionEvents::AUTH_REVOKED);
                }
            }
        }
        $this->save($connection);
        if ($previousState !== ConnectionStates::ACTIVE()->getValue() && $connection->getState() === ConnectionStates::ACTIVE()->getValue() && !$connection->isInitialized()) {
            $this->dispatcher->dispatch(new ConnectionEvents($connection), ConnectionEvents::AUTH_SUCCESS);
        }
        if (isset($access['exception'])) {
            throw $access['exception'];
        } else {
            return $access;
        }
    }


    /**
     * @param Organism $organism
     * @param DataProviders $dataProvider
     * @param array $params
     * @return bool|string
     */
    public function check(Organism $organism, DataProviders $dataProvider, array $params = [])
    {
        if (empty($params)) {
            $connection = $organism->getConnectionForDataProvider($dataProvider) ?? false;
            if ($connection && in_array($connection->getState(), [ConnectionStates::INACTIVE(), ConnectionStates::FAILED()])) {
                $params = $connection->getCredentials();
            }
        }
        $result = BaseApiService::getApiServiceByDataProvider($dataProvider)->checkBeforeHabilitate($organism, $params);
        if (isset($result['error'])) {
            throw new WedofBadRequestHttpException(json_encode($result), null, 400);
        }
        return $result;
    }

    /**
     * @param Organism $organism
     * @param DataProviders $dataProvider
     * @param array $params
     * @return bool
     * @throws WedofConnectionException
     */
    public function habilitate(Organism $organism, DataProviders $dataProvider, array $params = []): bool
    {
        $connection = $organism->getConnectionForDataProvider($dataProvider) ?? $this->create($organism, $dataProvider);
        $params = !empty($params) ? $params : $connection->getCredentials();
        if (empty($params)) {
            throw new WedofBadRequestHttpException(json_encode(['error' => 'credentials']), null, 400);
        }
        return BaseApiService::getApiServiceByDataProvider($dataProvider)->habilitate($organism, $connection, $params);
    }

    /**
     * @throws Exception|TransportExceptionInterface
     */
    public function delegate(Organism $organism, DataProviders $dataProvider, array $credentials, bool $checkOrganismAccess = true, bool $manual = false): array
    {
        return $this->authenticate($organism, $dataProvider, $credentials, $checkOrganismAccess, $manual);
    }

    /**
     * @param Connection $connection
     * @return bool
     * @throws TransportExceptionInterface
     */
    public function refreshConnection(Connection $connection): bool
    {
        $_SERVER['REFRESH_CONNECTION_CONTEXT'] = true;
        $success = false;
        try {
            $access = $this->authenticate($connection->getOrganism(), DataProviders::from($connection->getDataProvider()), $connection->getCredentials());
            $success = $access['hasAccess'] && $access['hasOrganismAccess'];
        } catch (Exception $e) {
            //don't stop ever and fix is re
            if ($connection->getState() === ConnectionStates::ACTIVE()->getValue() && $connection->getRefreshAt() <= new DateTime()) {
                $this->resetRefresh($connection);
            }
        } finally {
            $_SERVER['REFRESH_CONNECTION_CONTEXT'] = false;
            return $success;
        }
    }

    /**
     * @param int $id
     * @return Connection|null
     */
    public function getById(int $id): ?Connection
    {
        return $this->connectionRepository->find($id);
    }

    /**
     * @param array $parameters
     * @return ArrayCollection
     */
    public function listWithParams(array $parameters): ArrayCollection
    {
        return $this->connectionRepository->findAllWithParams($parameters);
    }

    /**
     * @param Connection $connection
     * @return void
     */
    public function finishInitialize(Connection $connection)
    {
        $connection->setIsInitialized(true);
        $this->save($connection);
        $this->dispatcher->dispatch(new ConnectionEvents($connection), ConnectionEvents::INITIALIZE_COMPLETED);
        $this->dispatcher->dispatch(new MonitoringEvents(null, "Fin de l'initialisation des dossiers pour l'organisme " . $connection->getOrganism()->getSiret() . " et le financeur " . $connection->getDataProvider()), MonitoringEvents::SEND_MESSAGE);
    }

    /**
     * @param Organism $organism
     * @param DataProviders $dataProvider
     * @return Connection
     */
    public function create(Organism $organism, DataProviders $dataProvider): Connection
    {
        $connexion = new Connection();
        $connexion->setDataProvider($dataProvider->getValue());
        if (ConnectionTypes::getConnectionTypeFromDataProvider($dataProvider)) {
            $connexion->setType(ConnectionTypes::getConnectionTypeFromDataProvider($dataProvider)->getValue());
        }
        $connexion->setState(ConnectionStates::INACTIVE()->getValue());
        $connexion->setIsTosValidated(false);
        $connexion->setIsInitialized(false);
        $connexion->setExistAtDataProvider(false);
        $organism->addConnection($connexion);

        return $this->save($connexion);
    }

    /**
     * @param Connection $connection
     * @return void
     */
    public function resetRefresh(Connection $connection): void
    {
        $connection->setExpiresOn(new DateTime('now')); //force refreshAt and refreshConnection will try authenticate
        $connection->setRefreshAt(new DateTime('now')); //force refreshAt and refreshConnection will try authenticate
        $this->save($connection);
    }

    //-------------------
    // METHODES PRIVEES
    //-------------------
}
