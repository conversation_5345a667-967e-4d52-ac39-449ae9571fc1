<?php
// src/Service/CertificationPartnerAuditService.php
namespace App\Service;

use App\Entity\Certification;
use App\Entity\CertificationFolder;
use App\Entity\CertificationPartner;
use App\Entity\CertificationPartnerAudit;
use App\Entity\CertificationPartnerAuditFile;
use App\Entity\CertificationPartnerAuditTemplate;
use App\Entity\Organism;
use App\Entity\Training;
use App\Entity\TrainingAction;
use App\Entity\User;
use App\Event\CertificationPartnerAudit\CertificationPartnerAuditEvents;
use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofConnectionException;
use App\Library\CertificationStatistics;
use App\Library\utils\enums\ActivityTypes;
use App\Library\utils\enums\CertificationPartnerAuditCompliances;
use App\Library\utils\enums\CertificationPartnerAuditCriteriaSeverity;
use App\Library\utils\enums\CertificationPartnerAuditResults;
use App\Library\utils\enums\CertificationPartnerAuditStates;
use App\Library\utils\enums\CertificationPartnerStates;
use App\Library\utils\enums\CertificationTypes;
use App\Library\utils\enums\ConnectionStates;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\enums\TrainingActionStates;
use App\Library\utils\enums\TrainingComplianceTypes;
use App\Library\utils\enums\TrainingStates;
use App\Library\utils\Tools;
use App\Message\ComputeCertificationPartnerAudit;
use App\Message\SynchronizeCatalogForAudit;
use App\Repository\CertificationPartnerAuditRepository;
use App\Repository\TrainingActionRepository;
use App\Repository\TrainingRepository;
use App\Service\DataProviders\AiApiService;
use App\Service\DataProviders\AutomatorApiService;
use DateTime;
use DateTimeZone;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\DBAL\Driver\Exception;
use Doctrine\ORM\Exception\NotSupported;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\QueryBuilder;
use ErrorException;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Messenger\Stamp\DelayStamp;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Throwable;

class CertificationPartnerAuditService implements LoggerAwareInterface
{
    private CertificationPartnerAuditRepository $certificationPartnerAuditRepository;
    private CertificationPartnerAuditFileService $certificationPartnerAuditFileService;
    private CertificationFolderSurveyService $certificationFolderSurveyService;
    private CertificationPartnerService $certificationPartnerService;
    private CertificationFolderService $certificationFolderService;
    private TrainingActionRepository $trainingActionRepository;
    private CertifierAccessService $certifierAccessService;
    private TrainingActionService $trainingActionService;
    private AutomatorApiService $automatorApiService;
    private TrainingRepository $trainingRepository;
    private TrainingService $trainingService;
    private ActivityService $activityService;
    private FileTypeService $fileTypeService;
    private AiApiService $aiApiService;
    private MessageBusInterface $messageBus;
    private EventDispatcherInterface $dispatcher;
    private LoggerInterface $logger;

    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    public function __construct(CertificationPartnerAuditRepository $certificationPartnerAuditRepository, CertificationPartnerAuditFileService $certificationPartnerAuditFileService, CertificationFolderSurveyService $certificationFolderSurveyService, CertificationPartnerService $certificationPartnerService, CertificationFolderService $certificationFolderService, CertifierAccessService $certifierAccessService, AutomatorApiService $automatorApiService, TrainingRepository $trainingRepository, ActivityService $activityService, TrainingService $trainingService, FileTypeService $fileTypeService, AiApiService $aiApiService, MessageBusInterface $messageBus, TrainingActionService $trainingActionService, TrainingActionRepository $trainingActionRepository, EventDispatcherInterface $dispatcher)
    {
        $this->certificationPartnerAuditRepository = $certificationPartnerAuditRepository;
        $this->certificationPartnerAuditFileService = $certificationPartnerAuditFileService;
        $this->certificationFolderSurveyService = $certificationFolderSurveyService;
        $this->certificationPartnerService = $certificationPartnerService;
        $this->certificationFolderService = $certificationFolderService;
        $this->trainingActionRepository = $trainingActionRepository;
        $this->certifierAccessService = $certifierAccessService;
        $this->trainingActionService = $trainingActionService;
        $this->automatorApiService = $automatorApiService;
        $this->trainingRepository = $trainingRepository;
        $this->trainingService = $trainingService;
        $this->activityService = $activityService;
        $this->fileTypeService = $fileTypeService;
        $this->aiApiService = $aiApiService;
        $this->messageBus = $messageBus;
        $this->dispatcher = $dispatcher;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    /**
     * used in Message & notifications
     * @param int $entityId
     * @return CertificationPartner|null
     */
    public function getByEntityId(int $entityId): ?CertificationPartner
    {
        return $this->certificationPartnerService->getByEntityId($entityId);
    }


    /**
     * used in Messages & notifications
     * @param int $id
     * @return CertificationPartnerAudit|null
     */
    public function getById(int $id): ?CertificationPartnerAudit
    {
        return $this->certificationPartnerAuditRepository->find($id);
    }

    /**
     * @param $partners
     * @param CertificationPartnerAuditTemplate $certificationPartnerAuditTemplate
     * @param User $user
     * @param bool $complete
     * @param bool $updateCompliance
     * @param bool $suspend
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function createOnPartners($partners, CertificationPartnerAuditTemplate $certificationPartnerAuditTemplate, User $user, bool $complete, bool $updateCompliance, bool $suspend)
    {
        $DELAY_IN_SECONDS_BETWEEN_STARTS = 0;
        $delay = 0;
        foreach ($partners as $partner) {
            $this->create($partner, $certificationPartnerAuditTemplate, $user, $delay, $complete, $updateCompliance, $suspend);
            $delay += ($DELAY_IN_SECONDS_BETWEEN_STARTS * 1000);
        }
    }

    /**
     * @param CertificationPartner $certificationPartner
     * @param CertificationPartnerAuditTemplate $certificationPartnerAuditTemplate
     * @param User $user
     * @param int $delay
     * @param bool $complete
     * @param bool $updateCompliance
     * @param bool $suspend
     * @return CertificationPartnerAudit
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function create(CertificationPartner $certificationPartner, CertificationPartnerAuditTemplate $certificationPartnerAuditTemplate, User $user, int $delay = 0, bool $complete = false, bool $updateCompliance = false, bool $suspend = false): CertificationPartnerAudit
    {
        $certificationPartnerAudit = new CertificationPartnerAudit();
        $certificationPartnerAudit->setCertificationPartner($certificationPartner);
        $certificationPartnerAudit->setTemplate($certificationPartnerAuditTemplate);
        $certificationPartnerAudit->setState(CertificationPartnerAuditStates::PENDING_COMPUTATION()->getValue());
        $certificationPartnerAudit->setStartDate(new DateTime());

        $certificationPartnerAudit = $this->certificationPartnerAuditRepository->save($certificationPartnerAudit);
        $this->sendEvent($certificationPartnerAudit, CertificationPartnerAuditEvents::PENDING_COMPUTATION);

        $this->activityService->create([
            'title' => 'Un audit ' . $certificationPartnerAuditTemplate->getName() . ' a commencé',
            'type' => ActivityTypes::FILE(),
            'eventTime' => new DateTime()
        ], $user, $certificationPartner, false);

        $this->computeAudit($certificationPartnerAudit, $user, $delay, $complete, $updateCompliance, $suspend); // Careful this is async
        return $certificationPartnerAudit;
    }

    /**
     * @param CertificationPartnerAudit $certificationPartnerAudit
     * @param array $body
     * @param User $user
     * @return CertificationPartnerAudit
     */
    public function update(CertificationPartnerAudit $certificationPartnerAudit, array $body, User $user): CertificationPartnerAudit
    {
        if (isset($body['restartAudit']) && $body['restartAudit'] === true) {
            $certificationPartnerAudit->setErrorMessage(null);
            $certificationPartnerAudit->setState(CertificationPartnerAuditStates::PENDING_COMPUTATION()->getValue());
            $certificationPartnerAudit = $this->certificationPartnerAuditRepository->save($certificationPartnerAudit);
            $this->sendEvent($certificationPartnerAudit, CertificationPartnerAuditEvents::PENDING_COMPUTATION);
            $this->computeAudit($certificationPartnerAudit, $user); // Careful this is async
        }
        return $this->certificationPartnerAuditRepository->save($certificationPartnerAudit);
    }

    /**
     * @param CertificationPartnerAudit $certificationPartnerAudit
     * @param string $error
     */
    public function failed(CertificationPartnerAudit $certificationPartnerAudit, string $error)
    {
        $certificationPartnerAudit->setState(CertificationPartnerAuditStates::FAILED()->getValue());
        $certificationPartnerAudit->setErrorMessage(substr($error, 0, 254)); // TODO change if datatype of errorMessage changes
        $this->certificationPartnerAuditRepository->save($certificationPartnerAudit);
    }

    /**
     * @param CertificationPartnerAudit $certificationPartnerAudit
     * @param bool $hasValidCatalog
     * @return CertificationPartnerAudit
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function compute(CertificationPartnerAudit $certificationPartnerAudit, bool $hasValidCatalog): CertificationPartnerAudit
    {
        if ($certificationPartnerAudit->getCertificationPartner()->getCertification()->isAllowAudits()) {
            $certificationPartnerAudit->setState(CertificationPartnerAuditStates::COMPUTING()->getValue());
            $this->certificationPartnerAuditRepository->save($certificationPartnerAudit);
            $this->sendEvent($certificationPartnerAudit, CertificationPartnerAuditEvents::COMPUTING);
            $evaluatedCriterias = $this->evaluateCriterias($certificationPartnerAudit, $hasValidCatalog);
            $certificationPartnerAudit->setEvaluatedCriterias($evaluatedCriterias);
            $certificationPartnerAudit->setState(CertificationPartnerAuditStates::IN_PROGRESS()->getValue());
            $this->certificationPartnerAuditRepository->save($certificationPartnerAudit);
            $this->sendEvent($certificationPartnerAudit, CertificationPartnerAuditEvents::IN_PROGRESS);
        } else {
            $certificationPartnerAudit->setState(CertificationPartnerAuditStates::FAILED()->getValue());
            $this->certificationPartnerAuditRepository->save($certificationPartnerAudit);
            $this->logger->error("Error no stripe subscription found ou created to audit usage");
        }
        return $certificationPartnerAudit;
    }

    /**
     * @param CertificationPartnerAudit $certificationPartnerAudit
     * @param User $user
     * @param string $targetFileType
     * @return CertificationPartnerAudit
     * @throws ClientExceptionInterface
     * @throws ContainerExceptionInterface
     * @throws ErrorException
     * @throws Exception
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws NotFoundExceptionInterface
     * @throws NotSupported
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     * @throws \Doctrine\DBAL\Exception
     */
    public function generateReport(CertificationPartnerAudit $certificationPartnerAudit, User $user, string $targetFileType): CertificationPartnerAudit
    {
        if (!empty($certificationPartnerAudit->getReport())) {
            throw new WedofBadRequestHttpException("Erreur, l'audit a déjà un rapport");
        }
        $certificationPartnerAuditTemplate = $certificationPartnerAudit->getTemplate();
        $editableReportFileType = $certificationPartnerAuditTemplate->getAuditTemplateFileType();
        $fileName = $editableReportFileType['name'] . ' ' . $certificationPartnerAudit->getStartDate()->format('d-m-Y'); // TODO(audit) customizable name
        $auditContext = $this->generateAuditContext($certificationPartnerAudit);
        $reportFile = $this->generateReportFile($certificationPartnerAudit, $editableReportFileType, $user, $fileName, $auditContext, $targetFileType);
        $certificationPartnerAudit->setReport($reportFile);
        return $this->certificationPartnerAuditRepository->save($certificationPartnerAudit);
    }

    /**
     * @param CertificationPartnerAudit $certificationPartnerAudit
     * @param array $body
     * @param User $user
     * @return CertificationPartnerAudit
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function complete(CertificationPartnerAudit $certificationPartnerAudit, array $body, User $user): CertificationPartnerAudit
    {
        if ($certificationPartnerAudit->getState() !== CertificationPartnerAuditStates::IN_PROGRESS()->getValue()) {
            throw new WedofBadRequestHttpException('Erreur, l\'audit ne peut pas être finalisé dans cet état');
        }
        $report = $certificationPartnerAudit->getReport();
        if ($report) {
            $certificationPartnerAudit = $this->certificationPartnerAuditFileService->convertAuditReportToPdf($certificationPartnerAudit, $user); // Dot it first so that if it fails, the operation stops
        }
        $auditResult = CertificationPartnerAuditResults::from($body['result'])->getValue();
        $certificationPartner = $certificationPartnerAudit->getCertificationPartner();
        if ($body['updatePartnerCompliance']) {
            $certificationPartner->setCompliance($auditResult);
            $certificationPartner->setComplianceLastUpdate(new DateTime());
        }
        $certificationPartnerAuditTemplate = $certificationPartnerAudit->getTemplate();
        $certificationPartnerAudit->setResult($auditResult);
        $certificationPartnerAudit->setEndDate($body['endDate']);
        $certificationPartnerAudit->setNotes($body['notes'] ?? null);
        $certificationPartnerAudit->setState(CertificationPartnerAuditStates::COMPLETED()->getValue());
        $certificationPartnerAudit = $this->certificationPartnerAuditRepository->save($certificationPartnerAudit);
        switch ($auditResult) {
            case CertificationPartnerAuditResults::COMPLIANT()->getValue():
                $this->sendEvent($certificationPartnerAudit, CertificationPartnerAuditEvents::COMPLIANT);
                break;
            case CertificationPartnerAuditResults::NON_COMPLIANT()->getValue():
                $this->sendEvent($certificationPartnerAudit, CertificationPartnerAuditEvents::NON_COMPLIANT);
                break;
            case CertificationPartnerAuditResults::PARTIALLY_COMPLIANT()->getValue():
                $this->sendEvent($certificationPartnerAudit, CertificationPartnerAuditEvents::PARTIALLY_COMPLIANT);
                break;
        }
        $this->activityService->create([
            'title' => "Un audit " . $certificationPartnerAuditTemplate->getName() . " s'est terminé avec le résultat " . CertificationPartnerAuditResults::toFrString($auditResult),
            'type' => ActivityTypes::FILE(),
            'eventTime' => new DateTime()
        ], $user, $certificationPartner, false);
        $this->sendEvent($certificationPartnerAudit, CertificationPartnerAuditEvents::COMPLETED);
        return $certificationPartnerAudit;
    }

    /**
     * @param array $parameters
     * @param CertificationPartner|null $certificationPartner
     * @return QueryBuilder
     */
    public function listReturnQueryBuilder(array $parameters, CertificationPartner $certificationPartner = null): QueryBuilder
    {
        return $this->certificationPartnerAuditRepository->findAllReturnQueryBuilder($parameters, $certificationPartner);
    }

    /**
     * @param array $parameters
     * @return QueryBuilder
     */
    public function listAuditReport(array $parameters): QueryBuilder
    {
        return $this->certificationPartnerAuditRepository->listAuditReport($parameters);
    }

    /**
     * @param Certification $certification
     * @return array
     */
    public function getTrainingTitles(Certification $certification): array
    {
        return $this->aiApiService->getTrainingTitles($certification);
    }

    /**
     * @param CertificationPartnerAudit $certificationPartnerAudit
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function delete(CertificationPartnerAudit $certificationPartnerAudit)
    {
        $reportFile = $certificationPartnerAudit->getReport();
        if ($reportFile) {
            if ($certificationPartnerAudit->getState() === CertificationPartnerAuditStates::IN_PROGRESS()->getValue()) {
                $link = $reportFile->getLink();
                $googleId = explode('https://docs.google.com/document/d/', $link)[1]; // HACK
                $googleId = explode('/', $googleId)[0]; // HACK
                $response = $this->automatorApiService->deleteDocumentTemplate('template-document-delete', $googleId);
                if (empty($response['statusCode']) || $response['statusCode'] != 200) {
                    throw new WedofBadRequestHttpException(json_decode($response['content'], true));
                }
            }
            $certificationPartnerAudit->setReport(null);
            $certificationPartnerAudit = $this->certificationPartnerAuditRepository->save($certificationPartnerAudit);
            $this->certificationPartnerAuditFileService->delete($reportFile);
        }
        $this->certificationPartnerAuditRepository->delete($certificationPartnerAudit);
    }

    /**
     * @param CertificationPartnerAuditTemplate $certificationPartnerAuditTemplate
     * @param array|null $states
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countByAuditTemplate(CertificationPartnerAuditTemplate $certificationPartnerAuditTemplate, array $states = null): int
    {
        return $this->certificationPartnerAuditRepository->countByAuditTemplate($certificationPartnerAuditTemplate, $states);
    }

    /**
     * @param CertificationPartnerAudit $certificationPartnerAudit
     * @return array
     */
    public function getDataToFetchForAudit(CertificationPartnerAudit $certificationPartnerAudit): array
    {
        $needsTrainings = false;
        $needsTrainingActions = false;
        $needsCatalog = false;
        $certificationPartnerAuditTemplate = $certificationPartnerAudit->getTemplate();
        $scopes = array_column($certificationPartnerAuditTemplate->getCriterias(), 'scope');
        foreach ($scopes as $scope) {
            if ($scope === 'trainingsPrerequisites' || $scope === 'trainingsTeachingMethod' || $scope === 'trainingsZone') {
                $needsTrainingActions = true;
            } else if ($scope === 'trainingsCount' || $scope === 'trainingActionsCount') {
                $needsCatalog = true;
            } else if (Tools::startsWith($scope, 'trainings')) {
                $needsTrainings = true;
            }
        }
        $needsCatalog = $needsCatalog || $needsTrainings || $needsTrainingActions;
        return [
            'trainings' => $needsTrainings,
            'trainingActions' => $needsTrainingActions,
            'catalog' => $needsCatalog
        ];
    }

    /**
     * @param CertificationPartnerAudit $certificationPartnerAudit
     * @param User $user
     * @param bool $updateCompliance
     * @param bool $suspend
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function completeAutomatically(CertificationPartnerAudit $certificationPartnerAudit, User $user, bool $updateCompliance, bool $suspend)
    {
        $certificationPartner = $certificationPartnerAudit->getCertificationPartner();
        $certification = $certificationPartner->getCertification();

        $completeBody = [
            'result' => $this->evaluateResult($certificationPartnerAudit->getEvaluatedCriterias())->getValue(),
            'endDate' => new DateTime(),
            'updatePartnerCompliance' => $updateCompliance
        ];
        $certificationPartnerAudit = $this->complete($certificationPartnerAudit, $completeBody, $user);

        if ($suspend && $certificationPartner->getState() === CertificationPartnerStates::ACTIVE()->getValue() && $certificationPartnerAudit->getResult() === CertificationPartnerAuditResults::NON_COMPLIANT()->getValue()) {
            if ($certification->getEnabled() && ($certification->getType() === CertificationTypes::RS()->getValue() || $certification->getType() === CertificationTypes::RNCP()->getValue())) {
                $this->certificationPartnerService->update($certificationPartner, ['pendingSuspension' => true], null, ['origin' => 'audit']);
            } else if (in_array($certification->getType(), [CertificationTypes::INTERNAL()->getValue(), CertificationTypes::PREVENTION()->getValue()])) {
                $this->certificationPartnerService->suspend($certificationPartner, ['origin' => 'audit']);
            }
        }
    }

    //----------------
    // METHODES PRIVES
    //----------------

    /**
     * @param CertificationPartnerAudit $certificationPartnerAudit
     * @param bool $hasValidCatalog
     * @return array
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    private function evaluateCriterias(CertificationPartnerAudit $certificationPartnerAudit, bool $hasValidCatalog): array
    {
        // Load data records
        $certificationPartner = $certificationPartnerAudit->getCertificationPartner();
        $certificationPartnerAuditTemplate = $certificationPartnerAudit->getTemplate();
        $certification = $certificationPartner->getCertification();
        $templateCriterias = $certificationPartnerAuditTemplate->getCriterias();
        $certificationStatistics = $this->certificationPartnerService->getStatistics($certificationPartner);
        $certifier = $certificationPartner->getCertifier();
        $partner = $certificationPartner->getPartner();
        $dataToFetchForAudit = $this->getDataToFetchForAudit($certificationPartnerAudit);
        $trainings = $dataToFetchForAudit['trainings'] && $hasValidCatalog ? $this->trainingService->getPublishedTrainingsForOrganismAndCertification($partner, $certification) : null;
        $trainingActions = $dataToFetchForAudit['trainingActions'] && $hasValidCatalog ? $this->trainingActionService->getPublishedTrainingActionsForOrganismAndCertification($partner, $certification) : null;
        // Extract data + evaluate
        $evaluatedCriterias = [];
        foreach ($templateCriterias as $templateCriteria) {
            $criteria = $templateCriteria; // This is a copy
            $value = $this->getCriteriaValue($templateCriteria['scope'], $certifier, $partner, $certificationPartner, $certificationStatistics, $trainings, $trainingActions); // Extract data
            $complianceAndReturnValue = $this->getComplianceAndReturnValue($value, $templateCriteria); // Evaluate
            $criteria['value'] = $complianceAndReturnValue['returnValue'];
            $criteria['compliance'] = $complianceAndReturnValue['compliance']->getValue();
            $evaluatedCriterias[] = $criteria;
        }
        return $evaluatedCriterias;
    }

    /**
     * @param string $scope
     * @param Organism $certifier
     * @param Organism $partner
     * @param CertificationPartner $certificationPartner
     * @param CertificationStatistics $certificationStatistics
     * @param ArrayCollection|null $trainings
     * @param ArrayCollection|null $trainingActions
     * @return int|mixed
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    private function getCriteriaValue(string $scope, Organism $certifier, Organism $partner, CertificationPartner $certificationPartner, CertificationStatistics $certificationStatistics, ?ArrayCollection $trainings, ?ArrayCollection $trainingActions)
    {
        $certification = $certificationPartner->getCertification();
        switch ($scope) {
            case 'wedofConnexionCpf':
                $connection = $partner->getConnectionForDataProvider(DataProviders::CPF());
                $value = !empty($connection) && in_array($connection->getState(), [ConnectionStates::ACTIVE()->getValue(), ConnectionStates::REFRESHING()->getValue()]);
                break;
            case 'wedofConnexionKairos':
                $connection = $partner->getConnectionForDataProvider(DataProviders::KAIROS_AIF());
                $value = !empty($connection) && in_array($connection->getState(), [ConnectionStates::ACTIVE()->getValue(), ConnectionStates::REFRESHING()->getValue()]);
                break;
            case 'wedofAccess':
                $certifierAccess = $this->certifierAccessService->getByOrganisms($certifier, $partner);
                $value = !empty($certifierAccess);
                break;
            case 'qualiopiVAE':
                $value = $partner->getQualiopiVAE();
                break;
            case 'qualiopiTrainingAction':
                $value = $partner->getQualiopiTrainingAction();
                break;
            case 'qualiopiBilanCompetences':
                $value = $partner->getQualiopiBilanCompetences();
                break;
            case 'qualiopiFormationApprentissage':
                $value = $partner->getQualiopiFormationApprentissage();
                break;
            case 'trainingsCount':
                $value = $this->trainingRepository->countByCertificationPartnerAndStates($certificationPartner, [TrainingStates::PUBLISHED()->getValue()]);
                break;
            case 'trainingActionsCount':
                $value = $this->trainingActionRepository->countByCertificationAndByOrganismAndStates($certification, $partner, [TrainingActionStates::PUBLISHED()->getValue()]);
                break;
            case 'successRate':
                $value = Tools::properRound($certificationStatistics->getSuccessRate());
                break;
            case 'takeRate':
                $value = Tools::properRound($certificationStatistics->getTakeRate());
                break;
            case 'takeRate1Month':
                $value = Tools::properRound($certificationStatistics->getTakeRateAfterDelay()['1Month']);
                break;
            case 'takeRate3Months':
                $value = Tools::properRound($certificationStatistics->getTakeRateAfterDelay()['3Months']);
                break;
            case 'takeRate6Months':
                $value = Tools::properRound($certificationStatistics->getTakeRateAfterDelay()['6Months']);
                break;
            case 'abortRate':
                $value = Tools::properRound($certificationStatistics->getAbortRate());
                break;
            case 'abortRateBeforeTraining':
                $value = Tools::properRound($certificationStatistics->getAbortRateExploded()['beforeTrainingRate']);
                break;
            case 'abortRateInTraining':
                $value = Tools::properRound($certificationStatistics->getAbortRateExploded()['inTrainingRate']);
                break;
            case 'abortRateAfterTraining':
                $value = Tools::properRound($certificationStatistics->getAbortRateExploded()['afterTrainingRate']);
                break;
            case 'trainingsAverageRating':
                $value = Tools::properRound($certificationStatistics->getEvaluation()['averageRating'] ?? null, 2);
                break;
            case 'trainingsPricingMin':
                $value = $certificationStatistics->getPricing()['min'] ?? null;
                break;
            case 'trainingsPricingMax':
                $value = $certificationStatistics->getPricing()['max'] ?? null;
                break;
            case 'trainingsPricingAverage':
                $value = Tools::properRound($certificationStatistics->getPricing()['average'] ?? null, 2);
                break;
            case 'trainingsDurationMin':
                $value = $certificationStatistics->getDuration()['min'] ?? null;
                break;
            case 'trainingsDurationMax':
                $value = $certificationStatistics->getDuration()['max'] ?? null;
                break;
            case 'trainingsDurationAverage':
                $value = Tools::properRound($certificationStatistics->getDuration()['average'] ?? null);
                break;
            // ---- trainingAction more ideas
            // admissionInformation? pb : choix prédéfinis
            case 'trainingsTitle':
            case 'trainingsTitleAI':
                $value = [];
                /** @var Training $training */
                foreach ($trainings as $training) {
                    $value[] = [
                        'id' => $training->getExternalId(),
                        'value' => $training->getTitle()
                    ];
                }
                break;
            case 'trainingsGoal':
                $value = [];
                /** @var Training $training */
                foreach ($trainings as $training) {
                    $value[] = [
                        'id' => $training->getExternalId(),
                        'value' => $training->getRawData()['goal'] ?? '' // TODO(audit): careful, if public data then no goal + html to manage
                    ];
                }
                break;
            case 'trainingsContent':
                $value = [];
                /** @var Training $training */
                foreach ($trainings as $training) {
                    $value[] = [
                        'id' => $training->getExternalId(),
                        'value' => $training->getRawData()['content'] ?? '' // TODO(audit): careful, if public data then no content + html to manage
                    ];
                }
                break;
            case 'trainingsContentSummary':
                $value = [];
                /** @var Training $training */
                foreach ($trainings as $training) {
                    $value[] = [
                        'id' => $training->getExternalId(),
                        'value' => $training->getRawData()['contentSummary'] ?? '' // TODO(audit): careful, if public data then no content + html to manage
                    ];
                }
                break;
            case 'trainingsExpectedResults':
                $value = [];
                /** @var Training $training */
                foreach ($trainings as $training) {
                    $value[] = [
                        'id' => $training->getExternalId(),
                        'value' => $training->getRawData()['expectedResults'] ?? '' // TODO(audit): careful, if public data then no content + html to manage
                    ];
                }
                break;
            case 'trainingsCompliance':
                $value = [];
                /** @var Training $training */
                foreach ($trainings as $training) {
                    $value[] = [
                        'id' => $training->getExternalId(),
                        'value' => $training->getCompliance() === TrainingComplianceTypes::COMPLIANT()->getValue()
                    ];
                }
                break;
            case 'trainingsPrerequisites':
                $value = [];
                $baseUrl = "https://www.moncompteformation.gouv.fr/espace-prive/html/#/formation/recherche/";
                /** @var TrainingAction $trainingAction */
                foreach ($trainingActions as $trainingAction) {
                    $value[] = [
                        'id' => $trainingAction->getExternalId(),
                        'value' => $trainingAction->getRawData()['specificConditionsAndPrerequisites'] ?? '',
                        'link' => $baseUrl . $trainingAction->getExternalId()
                    ];
                }
                break;
            case 'trainingsTeachingMethod':
                $value = [];
                $baseUrl = "https://www.moncompteformation.gouv.fr/espace-prive/html/#/formation/recherche/";
                /** @var TrainingAction $trainingAction */
                foreach ($trainingActions as $trainingAction) {
                    $teachingMethod = isset($trainingAction->getRawData()['teachingMethod']) ? $trainingAction->getRawData()['teachingMethod'] : '';
                    $value[] = [
                        'id' => $trainingAction->getExternalId(),
                        'value' => $teachingMethod ? ($teachingMethod === '0' ? 'presentiel' : ($teachingMethod === '1' ? 'mixte' : 'distanciel')) : '',
                        'link' => $baseUrl . $trainingAction->getExternalId()
                    ];
                }
                break;
            case 'trainingsZone':
                $value = [];
                $baseUrl = "https://www.moncompteformation.gouv.fr/espace-prive/html/#/formation/recherche/";
                $certificationPartnerTrainingsZone = $certificationPartner->getTrainingsZone();
                if ($certificationPartnerTrainingsZone) {
                    /** @var TrainingAction $trainingAction */
                    foreach ($trainingActions as $trainingAction) {
                        if (isset($trainingAction->getRawData()['teachingMethod']) && $trainingAction->getRawData()['teachingMethod'] === '0') {
                            $value[] = [
                                'id' => $trainingAction->getExternalId(),
                                'value' => isset($trainingAction->getRawData()['trainingAddress']['zipCode']) ? $trainingAction->getRawData()['trainingAddress']['zipCode'] : '',
                                'link' => $baseUrl . $trainingAction->getExternalId(),
                                'compareWith' => $certificationPartnerTrainingsZone
                            ];
                        }
                    }
                }
                break;
            case 'skillSets':
                $value = [];
                if ($certification->isAllowPartialSkillSets() && $certificationPartner->getSkillSets()->count() > 0) {
                    $skillSetIds = array_map(function ($skillSet) {
                        return $skillSet->getId();
                    }, $certificationPartner->getSkillSets()->toArray());
                    $parameters = [
                        'certifications' => [$certification->getCertifInfo()],
                        'partners' => $partner->getSiret()
                    ];
                    $certificationFolders = $this->certificationFolderService->listReturnQueryBuilder($certifier, $parameters)->getQuery()->getResult();
                    /** @var CertificationFolder $certificationFolder */
                    foreach ($certificationFolders as $certificationFolder) {
                        $value[] = [
                            'id' => $certificationFolder->getExternalId(),
                            'value' => array_map(function ($skillSet) {
                                return $skillSet->getId();
                            }, $certificationFolder->getSkillSets()->toArray()),
                            'link' => $certificationFolder->getPermalink(),
                            'compareWith' => $skillSetIds
                        ];
                    }
                }
                break;
            case 'initialExperienceAnsweredRate':
                $value = $this->certificationFolderSurveyService->details($certification, $partner)['initialExperienceAnsweredRate'];
                break;
            case 'sixMonthExperienceAnsweredRate':
                $value = $this->certificationFolderSurveyService->details($certification, $partner)['sixMonthExperienceAnsweredRate'];
                break;
            case 'longTermExperienceAnsweredRate':
                $value = $this->certificationFolderSurveyService->details($certification, $partner)['longTermExperienceAnsweredRate'];
                break;
            default:
                throw new WedofBadRequestHttpException("Erreur évaluation d'audit : le scope $scope n'est pas supporté");
        }
        return $value;
    }

    /**
     * @param $value
     * @param array $criteria
     * @return array
     */
    private function getComplianceAndReturnValue($value, array $criteria): array
    {
        if ($criteria['scope'] === 'trainingsTitleAI' && is_array($value) && count($value) > 0) {
            $hasNonCompliant = false;
            $hasCompliant = false;
            $hasPartiallyCompliant = false;
            $returnValue = [
                'total' => count($value),
                'notCompliant' => []
            ];
            $titles = [];
            $titleMap = [];
            foreach ($value as $item) {
                $titles[] = $item['value'];
                $titleMap[$item['value']] = $item;
            }
            $trainingData = $criteria['parameter'];
            $trainingDataMinimumThreshold = 10;
            if (!empty($trainingData) && count($trainingData) >= $trainingDataMinimumThreshold) {
                $this->logger->info("[Audit][evaluateTrainingTitleWithAI] Évaluation groupée de " . count($titles) . " titres avec " . count($trainingData) . " exemples");
                $data = $this->aiApiService->evaluateTrainingTitleWithAI($titles, $trainingData);
                if (isset($data)) {
                    foreach ($data as $evaluation) {
                        if (isset($evaluation['titre'], $evaluation['decision'])) {
                            $title = $evaluation['titre'];
                            $decision = $evaluation['decision'];
                            $justification = $evaluation['justification'];
                            if (isset($titleMap[$title])) {
                                $item = $titleMap[$title];
                                if ($decision) {
                                    $hasCompliant = true;
                                } else {
                                    $hasNonCompliant = true;
                                    if (isset($item['link'])) {
                                        $returnValue['notCompliant'][] = [
                                            'id' => $item['id'],
                                            'link' => $item['link']
                                        ];
                                    } else {
                                        $returnValue['notCompliant'][] = [
                                            'id' => $item['id'],
                                            'justification' => $justification
                                        ];
                                    }
                                }
                            }
                        }
                    }
                } else {
                    $this->logger->warning("[Audit][evaluateTrainingTitleWithAI] Unexpected response format from AI API");
                    if ($data === true) {
                        $hasCompliant = true;
                    } else if ($data === false) {
                        $hasNonCompliant = true;
                        foreach ($value as $item) {
                            if (isset($item['link'])) {
                                $returnValue['notCompliant'][] = [
                                    'id' => $item['id'],
                                    'link' => $item['link']
                                ];
                            } else {
                                $returnValue['notCompliant'][] = $item['id'];
                            }
                        }
                    }
                }
            }
            if ($hasNonCompliant) {
                $compliance = CertificationPartnerAuditCompliances::NON_COMPLIANT(); // At least 1 NON_COMPLIANT => NON COMPLIANT
            } else if ($hasPartiallyCompliant) {
                $compliance = CertificationPartnerAuditCompliances::PARTIALLY_COMPLIANT(); // At least 1 PARTIALLY_COMPLIANT & no NON_COMPLIANT => PARTIALLY_COMPLIANT
            } else if ($hasCompliant) {
                $compliance = CertificationPartnerAuditCompliances::COMPLIANT(); // A least 1 COMPLIANT an no PARTIALLY_COMPLIANT / NON_COMPLIANT => COMPLIANT, even if there are NOT_APPLICABLE
            } else {
                $compliance = CertificationPartnerAuditCompliances::NOT_APPLICABLE(); // Only NOT_APPLICABLE => NOT_APPLICABLE
            }
            return ['returnValue' => $returnValue, 'compliance' => $compliance];
        } else if (is_array($value)) {
            if (count($value) > 0) {
                $hasNonCompliant = false;
                $hasCompliant = false;
                $hasPartiallyCompliant = false;
                $returnValue = [
                    'total' => count($value),
                    'notCompliant' => []
                ];
                foreach ($value as $item) {
                    $parameter = isset($item['compareWith']) ? $item['compareWith'] : $criteria['parameter'];
                    $compliance = $this->evaluateCompliance($item['value'], $criteria['operation'], $parameter);
                    switch ($compliance->getValue()) {
                        case CertificationPartnerAuditCompliances::NON_COMPLIANT()->getValue():
                            $hasNonCompliant = true;
                            if (isset($item['link'])) {
                                $returnValue['notCompliant'][] = [
                                    'id' => $item['id'],
                                    'link' => $item['link']
                                ];
                            } else {
                                $returnValue['notCompliant'][] = $item['id'];
                            }
                            break;
                        case CertificationPartnerAuditCompliances::COMPLIANT()->getValue():
                            $hasCompliant = true;
                            break;
                        case CertificationPartnerAuditCompliances::PARTIALLY_COMPLIANT()->getValue():
                            $hasPartiallyCompliant = true;
                            break;
                    }
                }
                if ($hasNonCompliant) {
                    $compliance = CertificationPartnerAuditCompliances::NON_COMPLIANT(); // At least 1 NON_COMPLIANT => NON COMPLIANT
                } else if ($hasPartiallyCompliant) {
                    $compliance = CertificationPartnerAuditCompliances::PARTIALLY_COMPLIANT(); // At least 1 PARTIALLY_COMPLIANT & no NON_COMPLIANT => PARTIALLY_COMPLIANT
                } else if ($hasCompliant) {
                    $compliance = CertificationPartnerAuditCompliances::COMPLIANT(); // A least 1 COMPLIANT an no PARTIALLY_COMPLIANT / NON_COMPLIANT => COMPLIANT, even if there are NOT_APPLICABLE
                } else {
                    $compliance = CertificationPartnerAuditCompliances::NOT_APPLICABLE(); // Only NOT_APPLICABLE => NOT_APPLICABLE
                }
            } else {
                $returnValue = null;
                $compliance = CertificationPartnerAuditCompliances::NOT_APPLICABLE(); // No value => NOT_APPLICABLE
            }
        } else {
            $compliance = $this->evaluateCompliance($value, $criteria['operation'], $criteria['parameter']);
            if ($value === true) {
                $returnValue = $criteria['operationValues'] && $criteria['operationValues'][$criteria['operation']] ? $criteria['operationValues']['isTrue'] : 'Oui';
            } else if ($value === false) {
                $returnValue = $criteria['operationValues'] && $criteria['operationValues'][$criteria['operation']] ? $criteria['operationValues']['isFalse'] : 'Non';
            } else {
                $returnValue = $value;
            }
        }
        return ['returnValue' => $returnValue, 'compliance' => $compliance];
    }

    /**
     * @param $value
     * @param string $operation
     * @param $parameter
     * @return CertificationPartnerAuditCompliances
     */
    private function evaluateCompliance($value, string $operation, $parameter): CertificationPartnerAuditCompliances
    {
        // TODO enum for operations
        $AI = 'ai';
        $CONTAINS = 'contains';
        $CONTAINS_ALL = 'containsAll';
        $NOT_CONTAINS = 'notContains';
        $NOT_CONTAINS_ANY = 'notContainsAny';
        $EQUALS_STRING = 'eqs';
        $NOT_EQUALS_STRING = 'neqs';
        $MATCHES = 'matches';
        $EQUALS_NUMBER = 'eq';
        $NOT_EQUALS_NUMBER = 'neq';
        $LESS_THAN = 'lt';
        $LESS_THAN_EQUAL = 'lte';
        $GREATER_THAN = 'gt';
        $GREATER_THAN_EQUAL = 'gte';
        $IS_TRUE = 'isTrue';
        $IS_FALSE = 'isFalse';
        $CONTAINS_ARRAY = 'containsArray';
        $NOT_CONTAINS_ARRAY = 'notContainsArray';
        switch ($operation) {
            case $CONTAINS:
                $isCompliant = str_contains(mb_strtolower($value), mb_strtolower($parameter));
                break;
            case $CONTAINS_ALL:
                $lowerCaseValue = mb_strtolower($value);
                $terms = array_map('trim', explode(',', $parameter));
                $isCompliant = Tools::array_all($terms, function ($term) use ($lowerCaseValue) {
                    return str_contains($lowerCaseValue, mb_strtolower($term));
                });
                break;
            case $NOT_CONTAINS:
                $isCompliant = !str_contains(mb_strtolower($value), mb_strtolower($parameter));
                break;
            case $NOT_CONTAINS_ANY:
                $lowerCaseValue = mb_strtolower($value);
                $terms = array_map('trim', explode(',', $parameter));
                $isCompliant = Tools::array_all($terms, function ($term) use ($lowerCaseValue) {
                    return empty($term) || !str_contains($lowerCaseValue, mb_strtolower($term)); // needs empty check because str_contains(toto, '') === true
                });
                break;
            case $MATCHES:
                $isCompliant = preg_match('/' . $parameter . '/i', $value) === 1;
                break;
            case $EQUALS_NUMBER:
            case $EQUALS_STRING: // TODO(audit) maybe trim / normalize ?
                $isCompliant = $value == $parameter;
                break;
            case $NOT_EQUALS_NUMBER:
            case $NOT_EQUALS_STRING: // TODO(audit) maybe trim / normalize ?
                $isCompliant = $value != $parameter;
                break;
            case $LESS_THAN:
                if (isset($value)) {
                    $isCompliant = $value < $parameter;
                }
                break;
            case $LESS_THAN_EQUAL:
                if (isset($value)) {
                    $isCompliant = $value <= $parameter;
                }
                break;
            case $GREATER_THAN:
                if (isset($value)) {
                    $isCompliant = $value > $parameter;
                }
                break;
            case $GREATER_THAN_EQUAL:
                if (isset($value)) {
                    $isCompliant = $value >= $parameter;
                }
                break;
            case $IS_TRUE:
                $isCompliant = $value == true; // truthiness
                break;
            case $IS_FALSE:
                $isCompliant = $value == false; // truthiness
                break;
            case $CONTAINS_ARRAY:
                if (is_array($value)) {
                    $isCompliant = true;
                    foreach ($value as $val) {
                        if (!in_array($val, $parameter)) {
                            $isCompliant = false;
                            break;
                        }
                    }
                } else {
                    $isCompliant = in_array($value, $parameter);
                }
                break;
            case $NOT_CONTAINS_ARRAY:
                if (is_array($value)) {
                    $isCompliant = true;
                    foreach ($value as $val) {
                        if (in_array($val, $parameter)) {
                            $isCompliant = false;
                            break;
                        }
                    }
                } else {
                    $isCompliant = !in_array($value, $parameter);
                }
                break;
            case $AI:
                $trainingData = $parameter;
                $trainingDataMinimumThreshold = 10;
                if (!empty($trainingData) && count($trainingData) >= $trainingDataMinimumThreshold) {
                    $this->logger->info("[Audit][evaluateTrainingTitleWithAI] Évaluation donnée AI " . $value . " avec " . count($trainingData) . " exemples");
                    $isCompliant = $this->aiApiService->evaluateTrainingTitleWithAI($value, $trainingData); // TODO generic
                }
                break;
            default:
                throw new WedofBadRequestHttpException("Erreur évaluation d'audit : l'operation $operation n'est pas supportée");
        }
        if (isset($isCompliant)) {
            return $isCompliant ? CertificationPartnerAuditCompliances::COMPLIANT() : CertificationPartnerAuditCompliances::NON_COMPLIANT();
        } else {
            return CertificationPartnerAuditCompliances::NOT_APPLICABLE();
        }
    }

    /**
     * @param CertificationPartnerAudit $certificationPartnerAudit
     * @return array
     */
    private function generateAuditContext(CertificationpartnerAudit $certificationPartnerAudit): array
    {
        $evaluatedCriterias = $certificationPartnerAudit->getEvaluatedCriterias();
        $nbCriteriasCompliant = 0;
        $nbCriteriasNonCompliant = 0;
        foreach ($evaluatedCriterias as $evaluatedCriteria) {
            $compliance = $evaluatedCriteria['compliance'];
            if ($compliance === CertificationPartnerAuditCompliances::COMPLIANT()->getValue()) {
                $nbCriteriasCompliant++;
            } else if ($compliance === CertificationPartnerAuditCompliances::NON_COMPLIANT()->getValue()) {
                $nbCriteriasNonCompliant++;
            }
        }
        $variables = [
            'startDate' => $certificationPartnerAudit->getStartDate()->setTimezone(new DateTimeZone('Europe/Paris'))->format("d/m/Y"), // TODO(audit): check if right to use timezone
            'nbCriterias' => count($evaluatedCriterias),
            'nbCriteriasCompliant' => $nbCriteriasCompliant,
            'nbCriteriasNonCompliant' => $nbCriteriasNonCompliant
        ];
        if ($certificationPartnerAudit->getState() === CertificationPartnerAuditStates::COMPLETED()->getValue()) {
            $variables['result'] = CertificationPartnerAuditResults::toFrString($certificationPartnerAudit->getResult());
        }
        $iconsByCompliance = [
            CertificationPartnerAuditCompliances::COMPLIANT()->getValue() => '✅',
            CertificationPartnerAuditCompliances::PARTIALLY_COMPLIANT()->getValue() => '🟠',
            CertificationPartnerAuditCompliances::NON_COMPLIANT()->getValue() => '❌',
            CertificationPartnerAuditCompliances::NOT_APPLICABLE()->getValue() => '❕',
        ];
        foreach ($evaluatedCriterias as $evaluatedCriteria) {
            $prefix = $evaluatedCriteria['code'] . '_';
            $requirement = $evaluatedCriteria['scopeTitle'] . ' ' . $evaluatedCriteria['operationTitle'];
            if ($evaluatedCriteria['parameter'] !== null && $evaluatedCriteria['operation'] != 'ai') { // TODO enum for operations
                $requirement = $requirement . ' ' . $evaluatedCriteria['parameter'];
            }
            $variables[$prefix . 'title'] = $evaluatedCriteria['title'];
            $variables[$prefix . 'complianceIcon'] = $iconsByCompliance[$evaluatedCriteria['compliance']];
            $variables[$prefix . 'compliance'] = CertificationPartnerAuditCompliances::toFrString($evaluatedCriteria['compliance']);
            $variables[$prefix . 'requirement'] = $requirement;
            $variables[$prefix . 'advice'] = !empty($evaluatedCriteria['advice']) ? $evaluatedCriteria['advice'] : '';
            if (is_array($evaluatedCriteria['value'])) {
                if (gettype($evaluatedCriteria['value']['notCompliant'][0]) === 'string') {
                    $value = $evaluatedCriteria['value']['notCompliant'];
                } else {
                    $stringValues = [];
                    foreach ($evaluatedCriteria['value']['notCompliant'] as $valueNotCompliant) {
                        $stringEntries = [];
                        foreach ($valueNotCompliant as $key => $value) {
                            if (isset($value) && $value !== '') {
                                $stringEntries[] = $key . ' : ' . $value;
                            }
                        }
                        $stringValues[] = join(' & ', $stringEntries);
                    }
                    $value = join(', ', $stringValues);
                }
            } else {
                $value = $evaluatedCriteria['value'];
            }
            $variables[$prefix . 'value'] = $value;
        }
        return [
            'audit' => $variables
        ];
    }

    /**
     * @param CertificationPartnerAudit $certificationPartnerAudit
     * @param array $fileType
     * @param User $user
     * @param string $fileName
     * @param array $auditContext
     * @param string $targetFileType
     * @return CertificationPartnerAuditFile
     * @throws ClientExceptionInterface
     * @throws ContainerExceptionInterface
     * @throws ErrorException
     * @throws Exception
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws NotFoundExceptionInterface
     * @throws NotSupported
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     * @throws \Doctrine\DBAL\Exception
     */
    private function generateReportFile(CertificationPartnerAudit $certificationPartnerAudit, array $fileType, User $user, string $fileName, array $auditContext, string $targetFileType): CertificationPartnerAuditFile
    {
        $certificationPartner = $certificationPartnerAudit->getCertificationPartner();
        $response = $this->fileTypeService->generateDocumentFromTemplateAndEntity($certificationPartner, $fileType, $auditContext, $certificationPartner->getCertifier(), $targetFileType);
        if (empty($response['statusCode']) || $response['statusCode'] != 200) {
            throw new WedofBadRequestHttpException(json_encode($response['content'], true));
        }
        $content = $response['content'];
        $decodedContent = json_decode($content, true);
        if (isset($decodedContent['documentLink'])) {
            return $this->certificationPartnerAuditFileService->create($decodedContent['documentLink'], $fileType['id'], $certificationPartnerAudit, true, $user, false, $fileName);
        } else if ($targetFileType === 'pdf') {
            return $this->fileTypeService->createFileWithContent($content, $fileType, $certificationPartnerAudit, $this->certificationPartnerAuditFileService, $user);
        } else {
            throw new WedofBadRequestHttpException('Erreur dans la génération du fichier');
        }
    }

    /**
     * @param CertificationPartnerAudit $certificationPartnerAudit
     * @param string $eventName
     */
    private function sendEvent(CertificationPartnerAudit $certificationPartnerAudit, string $eventName): void
    {
        $certificationPartner = $certificationPartnerAudit->getCertificationPartner();
        $event = new CertificationPartnerAuditEvents($certificationPartnerAudit, $certificationPartner);
        $this->dispatcher->dispatch($event, $eventName);
        $this->logger->info("[" . $certificationPartnerAudit->getId() . "][event] CertificationPartnerAudit event dispatched $eventName ");
    }

    /**
     * @param CertificationPartnerAudit $certificationPartnerAudit
     * @param User $user
     * @param int $delay
     * @param bool $complete
     * @param bool $updateCompliance
     * @param bool $suspend
     * @return void
     */
    private function computeAudit(CertificationPartnerAudit $certificationPartnerAudit, User $user, int $delay = 0, bool $complete = false, bool $updateCompliance = false, bool $suspend = false): void
    {
        $computeCertificationPartnerAudit = new ComputeCertificationPartnerAudit($certificationPartnerAudit->getId(), $user->getId(), $complete, $updateCompliance, $suspend);
        $this->messageBus->dispatch($computeCertificationPartnerAudit);
    }

    /**
     * @param array $evaluatedCriterias
     * @return CertificationPartnerAuditResults
     */
    private function evaluateResult(array $evaluatedCriterias): CertificationPartnerAuditResults
    {
        // If change here, consider also changing the front resultGuessing() function
        $result = CertificationPartnerAuditResults::COMPLIANT();
        foreach ($evaluatedCriterias as $evaluatedCriteria) {
            if (isset($evaluatedCriteria['severity']) && $evaluatedCriteria['severity'] !== CertificationPartnerAuditCriteriaSeverity::NONE()->getValue()) { // Only take into account majeure / mineure
                $severity = $evaluatedCriteria['severity'];
                $compliance = $evaluatedCriteria['compliance'];
                if ($compliance === CertificationPartnerAuditCompliances::NON_COMPLIANT()->getValue()) {
                    // Majeure + NC => NC (stop there)
                    if ($severity === CertificationPartnerAuditCriteriaSeverity::MAJEURE()->getValue()) {
                        $result = CertificationPartnerAuditResults::NON_COMPLIANT(); // Cannot be overwritten so break
                        break;
                    }
                    // Mineure + NC => PC (can be overwritten)
                    $result = CertificationPartnerAuditResults::PARTIALLY_COMPLIANT(); // Can be overwritten
                } else if ($compliance === CertificationPartnerAuditCompliances::NOT_APPLICABLE()->getValue()) {
                    // (Majeure OR Mineure) + NA => PC (can be overwritten)
                    $result = CertificationPartnerAuditResults::PARTIALLY_COMPLIANT(); // Can be overwritten
                }
            }
        }
        return $result;
    }
}
