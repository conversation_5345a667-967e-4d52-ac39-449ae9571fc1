<?php

namespace App\Service;

use App\Entity\Formacode;
use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManagerInterface;
use Exception;

class FormacodeService
{
    private EntityManagerInterface $entityManager;

    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    /**
     * FormacodeService constructor.
     * @param EntityManagerInterface $entityManager
     */
    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    /**
     * @param array $rawData
     * @return Formacode
     * @throws Exception
     */
    public function getFormacode(array $rawData): Formacode
    {
        $formacode = $this->entityManager->getRepository("App:Formacode")->findOneByCode($rawData['code']);
        if (!$formacode) {
            $formacode = $this->createOrUpdateFormacode($rawData);
        }
        return $formacode;
    }

    /**
     * @param integer|null $offset
     * @param integer|null $limit
     * @param bool $onlyActive
     * @return ArrayCollection
     */
    public function getFormacodes(int $offset = null, int $limit = null, bool $onlyActive = false): ArrayCollection
    {
        if ($onlyActive) {
            return $this->entityManager->getRepository(Formacode::class)->findAllWithTrainingActions($offset, $limit);
        } else {
            return new ArrayCollection($this->entityManager->getRepository(Formacode::class)->findAll());
        }
    }

    /**
     * @param $rawData
     * @return Formacode
     * @throws Exception
     */
    public function createOrUpdateFormacode($rawData): Formacode
    {
        $formacode = $this->entityManager->getRepository("App:Formacode")->findOneByCode($rawData['code']);
        if (!$formacode) {
            $formacode = new Formacode();
        }
        $formacode->setCode($rawData['code']);
        $formacode->setName($rawData['name']);
        $formacode->setVisible($rawData['visible']);
        $formacode->setLastUpdate(new DateTime());
        $formacode->setCount($rawData['count'] ?: 0);
        if (!$this->entityManager->contains($formacode)) {
            $this->entityManager->persist($formacode);
        }
        $this->entityManager->flush();
        return $formacode;
    }
}
