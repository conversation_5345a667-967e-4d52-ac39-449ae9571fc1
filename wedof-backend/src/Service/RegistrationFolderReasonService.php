<?php
// src/Service/RegistrationFolderReasonService.php
namespace App\Service;

use App\Entity\RegistrationFolderReason;
use App\Repository\RegistrationFolderReasonRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Exception;

class RegistrationFolderReasonService
{
    private RegistrationFolderReasonRepository $registrationFolderReasonRepository;

    //-------------------
    // METHODES PUBLIQUES
    //-------------------
    public function __construct(RegistrationFolderReasonRepository $registrationFolderReasonRepository)
    {
        $this->registrationFolderReasonRepository = $registrationFolderReasonRepository;
    }

    /**
     * @param array $reasonRawData
     * @param string $category
     * @return RegistrationFolderReason
     * @throws Exception
     */
    public function create(array $reasonRawData, string $category): RegistrationFolderReason
    {
        $reason = $this->registrationFolderReasonRepository->findOneByCodeAndCategory($reasonRawData['code'], $category);
        if (empty($reason)) {
            $reason = new RegistrationFolderReason();
            $reason->setCategory($category);
            $reason->setCode($reasonRawData['code']);
            $reason->setLabel($reasonRawData['label']);
            $reason->setObsolescence($this->convertObsolescenceStringInBool($reasonRawData['obsolescence']));
            $this->registrationFolderReasonRepository->save($reason);
        }
        return $reason;
    }

    /**
     * @param string $code
     * @return RegistrationFolderReason|null
     */
    public function getReasonByCodeTerminated(string $code): ?RegistrationFolderReason
    {
        return $this->registrationFolderReasonRepository->findOneByCodeAndCategory($code, RegistrationFolderReason::CATEGORY_TERMINATED);
    }

    /**
     * @param string $code
     * @return RegistrationFolderReason|null
     */
    public function getReasonByCodeRefused(string $code): ?RegistrationFolderReason
    {
        return $this->registrationFolderReasonRepository->findOneByCodeAndCategory($code, RegistrationFolderReason::CATEGORY_REFUSED);
    }

    /**
     * @param string $code
     * @return RegistrationFolderReason|null
     */
    public function getReasonByCodeCanceled(string $code): ?RegistrationFolderReason
    {
        return $this->registrationFolderReasonRepository->findOneByCodeAndCategory($code, RegistrationFolderReason::CATEGORY_CANCELED);
    }

    /**
     * @param bool $withObsoletes
     * @return ArrayCollection
     */
    public function getAllReasonsTerminated(bool $withObsoletes = false): ArrayCollection
    {
        return $this->registrationFolderReasonRepository->findAllByCategoryAndObsolescence(RegistrationFolderReason::CATEGORY_TERMINATED, $withObsoletes);
    }

    /**
     * @param bool $withObsoletes
     * @return ArrayCollection
     */
    public function getAllReasonsRefused(bool $withObsoletes = false): ArrayCollection
    {
        return $this->registrationFolderReasonRepository->findAllByCategoryAndObsolescence(RegistrationFolderReason::CATEGORY_REFUSED, $withObsoletes);
    }

    /**
     * @param bool $withObsoletes
     * @return ArrayCollection
     */
    public function getAllReasonsCanceled(bool $withObsoletes = false): ArrayCollection
    {
        return $this->registrationFolderReasonRepository->findAllByCategoryAndObsolescence(RegistrationFolderReason::CATEGORY_CANCELED, $withObsoletes);
    }

    /**
     * @return array
     */
    public function getAllCodesTerminated(): array
    {
        return $this->registrationFolderReasonRepository->findCodesByCategoryAndObsolescence(RegistrationFolderReason::CATEGORY_TERMINATED, false);
    }

    /**
     * @return array
     */
    public function getAllCodesRefused(): array
    {
        return $this->registrationFolderReasonRepository->findCodesByCategoryAndObsolescence(RegistrationFolderReason::CATEGORY_REFUSED, false);
    }

    /**
     * @return array
     */
    public function getAllCodesCanceled(): array
    {
        return $this->registrationFolderReasonRepository->findCodesByCategoryAndObsolescence(RegistrationFolderReason::CATEGORY_CANCELED, false);
    }

    //----------------
    // METHODES PRIVES
    //----------------
    /**
     * @param string $obsolescenceString
     * @return bool
     * @throws Exception
     */
    private function convertObsolescenceStringInBool(string $obsolescenceString): bool
    {
        if ($obsolescenceString == "N" || $obsolescenceString == "Non") {
            return false;
        } else if ($obsolescenceString == "O" || $obsolescenceString == "Oui") {
            return true;
        } else {
            throw new Exception("RegistrationFolderReasonService : obsolescence string conversion failed");
        }
    }
}
