<?php
// src/Service/StripeService.php
namespace App\Service;

use App\Application\WedofApplication;
use App\Entity\Application;
use App\Entity\Certification;
use App\Entity\Organism;
use App\Entity\Subscription;
use App\Entity\User;
use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofSubscriptionException;
use App\Library\utils\enums\SubscriptionCertifierTypes;
use App\Library\utils\enums\SubscriptionTrainingTypes;
use App\Library\utils\Tools;
use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Exception;
use LogicException;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Stripe\Collection;
use Stripe\Event as StripeEvent;
use Stripe\Exception\ApiErrorException;
use Stripe\Invoice;
use Stripe\InvoiceLineItem;
use Stripe\StripeClient;
use Stripe\Subscription as StripeSubscription;
use Stripe\SubscriptionItem;
use Stripe\UsageRecordSummary;
use Throwable;

class StripeService implements LoggerAwareInterface
{
    const SUCCESS_URL = "/accueil";
    const CANCEL_URL = "/accueil";
    const RETURN_URL = "/accueil";
    const PAYMENT_METHODS = ['card', 'sepa_debit'];
    const PRODUCT_SMS = 'sms';
    const AUDITS_PRICING = 9900;
    const AUDITS_LINE_DESCRIPTION = 'Abonnement audit pour certification ';
    const SMS_USAGE = [
        'prod' => [
            'month' => "price_1Pnh3KLLgg6l7qY916U1fSjO"
        ],
        'test' => [
            'month' => "price_1Po5MJLLgg6l7qY9xPhwwHVB"
        ],
        'dev' => [
            'month' => "price_1Po5MJLLgg6l7qY9xPhwwHVB"
        ]
    ];

    const AUDIT_SUBSCRIPTION_PRODUCT = [
        'prod' => "prod_Rd1503nt17xiDi",
        'test' => "prod_Rd0kPNzm9ZqbQH",
        'dev' => "prod_Rd0kPNzm9ZqbQH"
    ];

    const TRAINING_SUBSCRIPTIONS = [
        'prod' => [
            'access' => 'price_1OuH1aLLgg6l7qY97kXcrxra',
            'access_year' => 'price_1P3bbcLLgg6l7qY94fT0mqFq',
            'access_plus' => 'price_1R8zm3LLgg6l7qY97ZOKNwsd',
            'access_plus_year' => 'price_1R8znbLLgg6l7qY9itnCDxoc',
            'api' => [
                //first price is the latest price available
                'price_1ROvEDLLgg6l7qY91AgaeZq0', //199€
                'price_1OEyJVLLgg6l7qY9mV0I9fuj', //149€
            ],
            'api_year' => [
                //first price is the latest price available
                'price_1ROvFhLLgg6l7qY9z6hXM5hz', //1990€
                'price_1P3dW4LLgg6l7qY9bcslMIfb', //1490€
            ],
            'essential' => [
                //first price is the latest price available
                'price_1ROvN9LLgg6l7qY9RGFlRbxs', //249€
                'price_1OEyK1LLgg6l7qY9ssj4hgkT' //199€
            ],
            'essential_year' => [
                //first price is the latest price available
                'price_1ROvP8LLgg6l7qY9bOe94GOM', //2490€
                'price_1P3dSILLgg6l7qY9tFqJ1iaM' //2049€
            ],
            'standard' => 'price_1OEyKZLLgg6l7qY9Xlrj7NlV',
            'standard_year' => 'price_1P3dXdLLgg6l7qY9q5gKeMft',
            'premium' => 'price_1OEyKyLLgg6l7qY97tx3yxof',
            'premium_year' => 'price_1P3dZ4LLgg6l7qY9qdOF5Uuf',
        ],
        'test' => [
            'access' => 'price_1OuBzkLLgg6l7qY9uVVRDHpC',
            'access_year' => 'price_1PHOFNLLgg6l7qY9XLdR4PHa',
            'api' => 'price_1MUnu7LLgg6l7qY9UQJlUdgh',
            'api_year' => 'price_1PHO7MLLgg6l7qY9dZdyylPJ',
            'essential' => 'price_1MUo0uLLgg6l7qY9L1s0IB56',
            'essential_year' => 'price_1PHOEZLLgg6l7qY9dFAJx5c3',
            'standard' => 'price_1MUnwjLLgg6l7qY9eDB78hcy',
            'standard_year' => 'price_1PHOArLLgg6l7qY9EWn4tyIH',
            'premium' => 'price_1MUnziLLgg6l7qY9DpDNFxH1',
            'premium_year' => 'price_1PHOBxLLgg6l7qY9B07sa32G'
        ],
        'dev' => [
            'access' => 'price_1OuBzkLLgg6l7qY9uVVRDHpC',
            'access_year' => 'price_1PHOFNLLgg6l7qY9XLdR4PHa',
            'api' => 'price_1MUnu7LLgg6l7qY9UQJlUdgh',
            'api_year' => 'price_1PHO7MLLgg6l7qY9dZdyylPJ',
            'essential' => 'price_1MUo0uLLgg6l7qY9L1s0IB56',
            'essential_year' => 'price_1PHOEZLLgg6l7qY9dFAJx5c3',
            'standard' => 'price_1MUnwjLLgg6l7qY9eDB78hcy',
            'standard_year' => 'price_1PHOArLLgg6l7qY9EWn4tyIH',
            'premium' => 'price_1MUnziLLgg6l7qY9DpDNFxH1',
            'premium_year' => 'price_1PHOBxLLgg6l7qY9B07sa32G'
        ]
    ];

    const CERTIFIER_SUBSCRIPTIONS = [
        'prod' => [
            'fixedFees' => 'price_1MC2x7LLgg6l7qY9doBK4rkN', // frais de mise en service à 990€
            'fixedFeesAccess' => 'price_1PlRWFLLgg6l7qY9bQUkd5DB', // frais de mise en service à 495€
//            'fixedFees' => 'price_1NBEDvLLgg6l7qY9F3g00FuF', // frais de mise en service offerts
            'access_100_8.9' => 'price_1RUSQELLgg6l7qY9XunExpBi',
            'access_300_8.9' => 'price_1PlRZkLLgg6l7qY9llVoWuIf',
            'usage_300_9.9' => 'price_1O4KUCLLgg6l7qY93uSNMIkq',
            'usage_300_8.9' => 'price_1O59iwLLgg6l7qY9GH2OT0eQ',
            'usage_500_8.9' => 'price_1RP78tLLgg6l7qY9HzwTozIC',
            'usage_500_9.9' => 'price_1RP79mLLgg6l7qY9MbxsPanz',
            'unlimited' => 'price_1O3LMSLLgg6l7qY9VRMyP9gJ',
            'certifierPlus' => 'price_1O3LirLLgg6l7qY9WxGWtjhD',
            'certifierPlusAccess' => 'price_1PlRT0LLgg6l7qY9q5CnRbjx',
        ],
        'test' => [
            'fixedFees' => 'price_1O54dSLLgg6l7qY9TBdmUT9P',
            'usage_300_9.9' => 'price_1O54SjLLgg6l7qY96G2qsGAA',
            'usage_300_8.9' => 'price_1O54TuLLgg6l7qY9kOc6KbsK',
            'unlimited' => 'price_1MYs4GLLgg6l7qY9f0SIMtxc',
            'certifierPlus' => 'price_1O3LqlLLgg6l7qY9Mz7JXj8T'
        ],
        'dev' => [
            'fixedFees' => 'price_1O54dSLLgg6l7qY9TBdmUT9P',
            'fixedFeesAccess' => 'price_1PlbqLLLgg6l7qY9XNGi3SrN', // frais de mise en service à 495€
            'access_100_8.9' => 'price_1PlbsWLLgg6l7qY9s44R0Nrh',
            'access_300_8.9' => 'price_1PlbsWLLgg6l7qY9s44R0Nrh',
            'usage_300_9.9' => 'price_1O54SjLLgg6l7qY96G2qsGAA',
            'usage_300_8.9' => 'price_1O54TuLLgg6l7qY9kOc6KbsK',
            'unlimited' => 'price_1MYs4GLLgg6l7qY9f0SIMtxc',
            'certifierPlus' => 'price_1O3LqlLLgg6l7qY9Mz7JXj8T',
            'certifierPlusAccess' => 'price_1PlbpfLLgg6l7qY9kBMnGPug'
        ]
    ];

    private SubscriptionService $subscriptionService;
    private StripeClient $stripeClient;
    private UserMessageService $userMessageService;
    private LoggerInterface $logger;

    //-------------------
    // METHODES PUBLIQUES
    //-------------------
    private CertificationService $certificationService;

    public function __construct(SubscriptionService $subscriptionService, UserMessageService $userMessageService, CertificationService $certificationService)
    {
        $this->subscriptionService = $subscriptionService;
        $this->userMessageService = $userMessageService;
        $this->stripeClient = new StripeClient(Tools::getEnvValue('STRIPE_API_SECRET_KEY'));
        $this->certificationService = $certificationService;
    }

    /**
     * @param LoggerInterface $logger
     */
    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    /**
     * @param User $user
     * @param SubscriptionTrainingTypes $subscriptionTrainingType
     * @return string
     * @throws ApiErrorException
     */
    public function createSubscriptionTrainingLink(User $user, SubscriptionTrainingTypes $subscriptionTrainingType): string
    {
        if (is_array(self::TRAINING_SUBSCRIPTIONS[Tools::getEnv()][$subscriptionTrainingType->getValue()])) {
            $priceToUse = self::TRAINING_SUBSCRIPTIONS[Tools::getEnv()][$subscriptionTrainingType->getValue()][0]; //first element is the last price
        } else {
            $priceToUse = self::TRAINING_SUBSCRIPTIONS[Tools::getEnv()][$subscriptionTrainingType->getValue()];
        }
        return $this->createSubscriptionLink(
            $user,
            [$priceToUse]
        );
    }

    /**
     * @param User $user
     * @param SubscriptionCertifierTypes $subscriptionCertifierType
     * @param array $options
     * @return string
     * @throws ApiErrorException
     */
    public function createSubscriptionCertifierLink(User $user, SubscriptionCertifierTypes $subscriptionCertifierType, array $options): string
    {
        $certifierSubscriptions = self::CERTIFIER_SUBSCRIPTIONS[Tools::getEnv()];
        if (!SubscriptionCertifierTypes::USAGE()->equals($subscriptionCertifierType) && !SubscriptionCertifierTypes::ACCESS()->equals($subscriptionCertifierType)) {
            throw new WedofBadRequestHttpException("Pour toute souscription en dehors des offres à la consommation, merci de nous contacter à <EMAIL>.");
        }
        if (SubscriptionCertifierTypes::ACCESS()->equals($subscriptionCertifierType)) {
            $subscriptions[] = $certifierSubscriptions['fixedFeesAccess'];
            $subscriptions[] = $certifierSubscriptions['certifierPlusAccess'];
        } else {
            $subscriptions[] = $certifierSubscriptions['fixedFees'];
            if ($options['certifierPlus']) {
                $subscriptions[] = $certifierSubscriptions['certifierPlus'];
            }
        }
        if (array_key_exists($subscriptionCertifierType->getValue() . '_' . $options['quantity'] . '_' . $options['price'], $certifierSubscriptions)) {
            $subscriptions[] = $certifierSubscriptions[$subscriptionCertifierType->getValue() . '_' . $options['quantity'] . '_' . $options['price']];
        } else {
            throw new WedofBadRequestHttpException("Il n'existe pas de tarification à la consommation pour " . $options['quantity'] . " dossiers à " . $options['price'] . "€HT.");
        }

        return $this->createSubscriptionLink(
            $user,
            $subscriptions
        );
    }

    /**
     * @param User $user
     * @return string
     * @throws ApiErrorException
     */
    public function manageSubscriptionLink(User $user): string
    {
        $customerId = $user->getMainOrganism()->getSubscription()->getStripeCustomerId();
        if (!$customerId) {
            $customerId = $this->createCustomer($user);
        }
        $session = $this->stripeClient->billingPortal->sessions->create([
            'customer' => $customerId,
            'return_url' => Tools::getEnvValue('WEDOF_BASE_URI') . self::RETURN_URL
        ]);
        return $session->url;
    }

    /**
     * @param ArrayCollection $certifiers
     * @return void
     * @throws ApiErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function sendUsageForCertifiers(ArrayCollection $certifiers): void
    {
        /** @var Organism $certifier */
        foreach ($certifiers as $certifier) {
            $this->sendUsageForCertifier($certifier);
        }
    }

    /**
     * @param ArrayCollection $organisms
     * @return void
     * @throws ApiErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function sendSmsUsageForOrganisms(ArrayCollection $organisms): void
    {
        /** @var Organism $organism */
        foreach ($organisms as $organism) {
            $subscription = $organism->getSubscription();
            if (($subscription->getSmsSentNumberPeriodEndDate() === null && $subscription->getSmsSentNumberCount() > 0)
                || ($subscription->getSmsSentNumberPeriodEndDate() !== null && ($subscription->getSmsSentNumberCount() > 0 || $subscription->getSmsSentNumberPeriodEndDate() < new DateTime()))) {
                $this->sendSmsUsageForOrganism($subscription);
            }
        }
    }

    /**
     * @param StripeEvent $event
     * @param bool $newSubscription
     * @return void
     * @throws Throwable
     * @throws WedofSubscriptionException
     */
    public function handleNewOrUpdatedSubscription(StripeEvent $event, bool $newSubscription)
    {
        $certifierSubscriptions = self::CERTIFIER_SUBSCRIPTIONS[Tools::getEnv()];
        $trainingSubscriptions = self::TRAINING_SUBSCRIPTIONS[Tools::getEnv()];
        $auditSubscriptionProduct = self::AUDIT_SUBSCRIPTION_PRODUCT[Tools::getEnv()];
        $stripeSubscription = StripeSubscription::constructFrom($event->data->object);
        $startDate = new DateTime();
        $startDate->setTimestamp($stripeSubscription->current_period_start);
        $endDate = new DateTime();
        // lorsqu'un abonnement est annulé avant la date de fin de la période, il faut s'appuyer sur la date du cancel_at (la current_period_end ne change pas)
        $endDate->setTimestamp($stripeSubscription->cancel_at !== null ? $stripeSubscription->cancel_at : $stripeSubscription->current_period_end);
        $pendingCancellation = $stripeSubscription->cancel_at_period_end || $stripeSubscription->cancel_at !== null; // le cancel_at_period_end n'indique que si l'annulation a lieu à la fin de période en cours. Dans le cas d'une annulation avant, il faut s'appuyer sur la date cancel_at.

        $subscriptionTrainingType = $this->findSubscriptionType($stripeSubscription->items->data[0]->plan->id, $trainingSubscriptions);
        $subscription = $this->subscriptionService->getOneByStripeCustomerId($stripeSubscription->customer);
        if ($subscriptionTrainingType) {
            $subscriptionParams = [
                'training' => [
                    'type' => explode('_2023', $subscriptionTrainingType)[0], //we may have _2023 keep only the name part
                    'trainingPendingCancellation' => $pendingCancellation,
                    'trainingPeriodStartDate' => $startDate,
                    'trainingPeriodEndDate' => $endDate
                ]];
        } else {
            $subscriptionCertifierTypes = [];
            foreach ($stripeSubscription->items->data as $item) {
                $subscriptionCertifierTypes[$item->plan->id] = $this->findSubscriptionType($item->plan->id, $certifierSubscriptions);
            }
            if (!empty($subscriptionCertifierTypes)) {
                $subscriptionParams = ['certifier' => []];
                foreach ($subscriptionCertifierTypes as $priceId => $subscriptionCertifierType) {
                    $explodedArray = explode('_', $subscriptionCertifierType);
                    switch ($explodedArray[0]) {
                        case SubscriptionCertifierTypes::USAGE()->getValue():
                        case SubscriptionCertifierTypes::ACCESS()->getValue():
                            $subscriptionParams['certifier'] = array_merge($subscriptionParams['certifier'], [
                                'type' => $explodedArray[0],
                                'certifierPendingCancellation' => $pendingCancellation,
                                'certifierPeriodStartDate' => $startDate,
                                'certifierPeriodEndDate' => $endDate,
                                'certificationFoldersNumberCap' => $explodedArray[1],
                                'certificationFoldersNumberAnnualLimit' => SubscriptionCertifierTypes::USAGE()->getValue() == $explodedArray[0] ? null : intval($explodedArray[1]),
                                'certificationFolderPrice' => $explodedArray[2]
                            ]);
                            break;
                        case SubscriptionCertifierTypes::UNLIMITED()->getValue():
                            $subscriptionParams['certifier'] = array_merge($subscriptionParams['certifier'], [
                                'type' => SubscriptionCertifierTypes::UNLIMITED()->getValue(),
                                'certifierPendingCancellation' => $pendingCancellation,
                                'certifierPeriodStartDate' => $startDate,
                                'certifierPeriodEndDate' => $endDate,
                                'allowCertifierPlus' => true,
                                'certifierUsersLimit' => null // Maybe useless because duplicate of SubscriptionService?
                            ]);
                            break;
                        case 'certifierPlus':
                        case 'certifierPlusAccess':
                            $subscriptionParams['certifier'] = array_merge($subscriptionParams['certifier'], [
                                'allowCertifierPlus' => true,
                                'certifierUsersLimit' => null
                            ]);
                            // facturation des audits
                            $nextInvoice = $this->stripeClient->invoices->upcoming([
                                'customer' => $stripeSubscription->customer,
                                'subscription' => $stripeSubscription->id
                            ]);
                            $certificationsToInvoice = $this->listCertificationsToInvoice($nextInvoice->lines, $subscription->getOrganism());
                            foreach ($certificationsToInvoice as $certification) {
                                $this->stripeClient->invoiceItems->create([
                                    'customer' => $stripeSubscription->customer,
                                    'description' => 'Abonnement audit pour certification ' . $certification['certification']->getExternalId(),
                                    'subscription' => $stripeSubscription->id,
                                    'price_data' => [
                                        'product' => $auditSubscriptionProduct,
                                        'tax_behavior' => 'exclusive',
                                        'currency' => 'eur',
                                        'unit_amount' => self::AUDITS_PRICING
                                    ],
                                    'period' => [
                                        'start' => $certification['periodStartDate'],
                                        'end' => $certification['periodEndDate']
                                    ]
                                ]);
                            }
                            break;
                        default:
                            break;
                    }
                }
            } else {
                throw new WedofBadRequestHttpException("Le price " . $stripeSubscription->items->data[0]->plan->id . " n'est pas reconnu.");
            }
        }

        $this->subscriptionService->switch($subscription, $subscriptionParams, $newSubscription);
    }

    /**
     * @param StripeEvent $event
     * @return void
     * @throws Throwable
     */
    public function handleDeletedSubscription(StripeEvent $event): void
    {
        $certifierSubscriptions = self::CERTIFIER_SUBSCRIPTIONS[Tools::getEnv()];
        $trainingSubscriptions = self::TRAINING_SUBSCRIPTIONS[Tools::getEnv()];
        $stripeSubscription = StripeSubscription::constructFrom($event->data->object);
        $subscription = $this->subscriptionService->getOneByStripeCustomerId($stripeSubscription->customer);
        if ($this->findSubscriptionType($stripeSubscription->items->data[0]->plan->id, $trainingSubscriptions)) {
            $this->subscriptionService->switch($subscription, ['training' => ['type' => SubscriptionTrainingTypes::NONE()->getValue()]]);
        } else if ($this->findSubscriptionType($stripeSubscription->items->data[0]->plan->id, $certifierSubscriptions)) {
            $this->subscriptionService->switch($subscription, ['certifier' => ['type' => SubscriptionCertifierTypes::NONE()->getValue()]]);
        } else {
            throw new WedofBadRequestHttpException("Le price " . $stripeSubscription->items->data[0]->plan->id . " n'est pas reconnu.");
        }
    }

    /**
     * @param StripeEvent $event
     * @return void
     * @throws WedofSubscriptionException
     */
    public function handleFailedPayment(StripeEvent $event): void
    {
        $stripeInvoice = Invoice::constructFrom($event->data->object);
        $messages = $this->userMessageService->listWithParams(['user' => $this->subscriptionService->getOneByStripeCustomerId($stripeInvoice->customer)->getOrganism()->getOwnedBy(),
            'content' => 'La facture n°' . $stripeInvoice->number . ' présente']);
        if ($messages->count() === 0) {
            // TODO déclenchement d'un évènement, N8N + slack + airtable ?
            $message = 'La facture n°' . $stripeInvoice->number . ' présente un retard de paiement. Merci de régulariser la situation au plus vite - <a href="' . $stripeInvoice->hosted_invoice_url . '" target="_blank">Payez maintenant</a>';
            $options = [
                'type' => 'warning',
                'showIcon' => true,
                'visible' => true
            ];
            $this->userMessageService->create($message, $options, $this->subscriptionService->getOneByStripeCustomerId($stripeInvoice->customer)->getOrganism()->getOwnedBy());
        }
    }

    /**
     * @param StripeEvent $event
     * @return void
     * @throws WedofSubscriptionException
     */
    public function handleSuccessPayment(StripeEvent $event): void
    {
        $stripeInvoice = Invoice::constructFrom($event->data->object);
        $messages = $this->userMessageService->listWithParams(['user' => $this->subscriptionService->getOneByStripeCustomerId($stripeInvoice->customer)->getOrganism()->getOwnedBy(),
            'content' => 'La facture n°' . $stripeInvoice->number . ' présente']);
        foreach ($messages as $message) {
            $this->userMessageService->delete($message);
        }
    }

    /**
     * @param Subscription $subscription
     * @param Application $application
     * @param bool $proration
     * @return bool|StripeSubscription
     * @throws ApiErrorException
     */
    public function addSubscriptionOption(Subscription $subscription, Application $application, bool $proration = true)
    {
        $optionPrices = $this->getSubscriptionOptionPricesFor($application->getAppId());
        $isPaidCustomer = $this->subscriptionService->isPaidCustomer($subscription);
        if (!$isPaidCustomer) {
            return false;
        }
        $stripeSubscription = $this->findStripeSubscriptionWithOption($subscription, $optionPrices);
        if (!$stripeSubscription) {
            $stripeSubscriptions = $this->stripeClient->subscriptions->all(['customer' => $subscription->getStripeCustomerId(), 'limit' => '1']);
            if ($stripeSubscriptions->count() > 0) {
                $stripeSubscription = $stripeSubscriptions->first();
                $interval = $this->getIntervalFromSubscription($stripeSubscription);
                if ($interval) {
                    $params = [
                        'quantity' => 1,
                        'subscription' => $stripeSubscription->id,
                        'price' => $optionPrices[$interval],
                        //don't prorate if already paid for the current period but in pending_disable (enabled => pending_disable => enabled on same period)
                        'proration_behavior' => $proration ? 'create_prorations' : 'none'
                    ];
                    if (!empty($optionPrices['discount'][$interval])) {
                        $params['discounts'] = [['coupon' => $optionPrices['discount'][$interval]]];
                    }
                    $item = $this->stripeClient->subscriptionItems->create($params);
                    if ($item->id != null) {
                        return $stripeSubscription;
                    }
                }
            }
        }
        return false;
    }

    /**
     * @param Subscription $subscription
     * @param Application $application
     * @return bool|StripeSubscription
     * @throws ApiErrorException
     */
    public function removeSubscriptionOption(Subscription $subscription, Application $application)
    {
        $optionPrices = $this->getSubscriptionOptionPricesFor($application->getAppId());
        $stripeSubscription = $this->findStripeSubscriptionWithOption($subscription, $optionPrices);
        if ($stripeSubscription) {
            $interval = $this->getIntervalFromSubscription($stripeSubscription);
            foreach ($stripeSubscription->items->data as $item) {
                if ($item->price->id == $optionPrices[$interval]) {
                    if ($this->stripeClient->subscriptionItems->delete($item->id, [
                        'proration_behavior' => 'none' //never refund if they pay they pay (but they can use it until period end)
                    ])->isDeleted()) {
                        return $stripeSubscription;
                    }
                }
            }
        }
        return false;
    }

    /**
     * @param Subscription $subscription
     * @param string $product
     * @return StripeSubscription|null
     * @throws ApiErrorException
     * @throws Exception
     */
    public function findOrCreateStripeSubscriptionWithProductUsage(Subscription $subscription, string $product): ?StripeSubscription
    {
        if (!$subscription->getStripeCustomerId()) {
            return null;
        }
        $stripeSubscription = null;
        switch ($product) {
            case self::PRODUCT_SMS:
                $price = self::SMS_USAGE[Tools::getEnv()]['month'];
                break;
            default:
                throw new LogicException("Error, product unknown");
        }
        $stripeSubscriptions = $this->stripeClient->subscriptions->all(['customer' => $subscription->getStripeCustomerId(), 'price' => $price, 'limit' => '1']);
        if ($stripeSubscriptions->count() == 0) {
            $stripeSubscriptions = $this->stripeClient->subscriptions->all(['customer' => $subscription->getStripeCustomerId()]);
            foreach ($stripeSubscriptions as $_stripeSubscription) {
                if ($this->getIntervalFromSubscription($_stripeSubscription) === 'month') {
                    $stripeSubscription = $_stripeSubscription;
                    $this->stripeClient->subscriptionItems->create([
                        'subscription' => $_stripeSubscription->id,
                        'price' => $price
                    ]);
                    break;
                }
            }
            if (!$stripeSubscription) {
                try {
                    $customer = $this->stripeClient->customers->retrieve(
                        $subscription->getStripeCustomerId(),
                        ['expand' => ['subscriptions']]
                    );
                    $stripeSubscription = $this->stripeClient->subscriptions->create([
                        'customer' => $customer->id,
                        'items' => [
                            ['price' => $price]
                        ]
                    ]);
                } catch (Exception $e) {
                    $this->logger->warning("Erreur creation stripe subscription pour les " . $product . " sur cette souscription " . $subscription->getId());
                }
            }
        } else {
            $stripeSubscription = $stripeSubscriptions->first();
        }
        if ($stripeSubscription) {
            $currentPeriodStart = new DateTime('@' . $stripeSubscription->current_period_start);
            $currentPeriodEnd = new DateTime('@' . $stripeSubscription->current_period_end);
            if ($product === self::PRODUCT_SMS && (!$subscription->getSmsSentNumberPeriodStartDate() || ($currentPeriodStart)->getTimestamp() != $subscription->getSmsSentNumberPeriodStartDate()->getTimestamp())) {
                $subscription->setSmsSentNumberPeriodStartDate($currentPeriodStart);
                $subscription->setSmsSentNumberPeriodEndDate($currentPeriodEnd);
                $this->subscriptionService->save($subscription);
            }
        }
        return $stripeSubscription;
    }

    /**
     * @param Certification $certification
     * @param Organism $certifier
     * @param bool $billProrata
     * @return void
     * @throws ApiErrorException
     */
    public function createAuditSubscriptionOnCertification(Certification $certification, Organism $certifier, bool $billProrata): void
    {
        $certifierSubscriptions = self::CERTIFIER_SUBSCRIPTIONS[Tools::getEnv()];
        $auditSubscriptionProduct = self::AUDIT_SUBSCRIPTION_PRODUCT[Tools::getEnv()];
        if (!$certification->getAuditTrialEndDate()) {
            $stripeCustomerId = $certifier->getSubscription()->getStripeCustomerId();
            $customer = $this->stripeClient->customers->retrieve(
                $stripeCustomerId,
                ['expand' => ['subscriptions']]
            );
            $stripeSubscriptions = $customer->subscriptions->all();
            foreach ($stripeSubscriptions as $stripeSubscription) {
                $items = $stripeSubscription->items->all();
                /** @var SubscriptionItem $item */
                foreach ($items as $item) {
                    if ($item->price->id === $certifierSubscriptions['certifierPlus'] || $item->price->id === $certifierSubscriptions['certifierPlusAccess']) {
                        if ($billProrata) {
                            // on crée une ligne de facturation au prorata du mois en cours
                            $price = round(self::AUDITS_PRICING * ($stripeSubscription->current_period_end - (new DateTime())->getTimestamp()) / ($stripeSubscription->current_period_end - $stripeSubscription->current_period_start));
                            $this->stripeClient->invoiceItems->create([
                                'customer' => $stripeCustomerId,
                                'description' => 'Abonnement audit pour certification ' . $certification->getExternalId(),
                                'subscription' => $item->subscription,
                                'price_data' => [
                                    'product' => $auditSubscriptionProduct,
                                    'tax_behavior' => 'exclusive',
                                    'currency' => 'eur',
                                    'unit_amount' => $price
                                ],
                                'period' => [
                                    'start' => (new DateTime())->getTimestamp(),
                                    'end' => $stripeSubscription->current_period_end
                                ]
                            ]);
                        }

                        // on crée une ligne de facturation au tarif plein pour paiement à échoir du mois suivant
                        $nextInvoice = $this->stripeClient->invoices->upcoming([
                            'customer' => $stripeCustomerId,
                            'subscription' => $stripeSubscription->id
                        ]);
                        /** @var InvoiceLineItem $line */
                        foreach ($nextInvoice->lines->data as $line) {
                            if ($line->price->id === $certifierSubscriptions['certifierPlus'] || $line->price->id === $certifierSubscriptions['certifierPlusAccess']) {
                                $this->stripeClient->invoiceItems->create([
                                    'customer' => $stripeCustomerId,
                                    'description' => self::AUDITS_LINE_DESCRIPTION . $certification->getExternalId(),
                                    'subscription' => $item->subscription,
                                    'price_data' => [
                                        'product' => $auditSubscriptionProduct,
                                        'tax_behavior' => 'exclusive',
                                        'currency' => 'eur',
                                        'unit_amount' => self::AUDITS_PRICING
                                    ],
                                    'period' => [
                                        'start' => $line->period->start,
                                        'end' => $line->period->end
                                    ]
                                ]);
                                break 3; // break des 3 foreach
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * @param Certification $certification
     * @param Organism $certifier
     * @return void
     * @throws ApiErrorException
     */
    public function removeAuditSubscriptionOnCertification(Certification $certification, Organism $certifier): void
    {
        $certifierSubscriptions = self::CERTIFIER_SUBSCRIPTIONS[Tools::getEnv()];
        $stripeCustomerId = $certifier->getSubscription()->getStripeCustomerId();
        $customer = $this->stripeClient->customers->retrieve(
            $stripeCustomerId,
            ['expand' => ['subscriptions']]
        );
        $stripeSubscriptions = $customer->subscriptions->all();
        $lineDescription = self::AUDITS_LINE_DESCRIPTION . $certification->getExternalId();
        foreach ($stripeSubscriptions as $stripeSubscription) {
            $items = $stripeSubscription->items->all();
            /** @var SubscriptionItem $item */
            foreach ($items as $item) {
                if ($item->price->id === $certifierSubscriptions['certifierPlus'] || $item->price->id === $certifierSubscriptions['certifierPlusAccess']) {
                    $nextInvoice = $this->stripeClient->invoices->upcoming([
                        'customer' => $stripeCustomerId,
                        'subscription' => $stripeSubscription->id
                    ]);
                    foreach ($nextInvoice->lines->data as $line) {
                        // on regarde les lignes précréées donc celles qui ont le tarif plein. On laisse les autres lignes qui sont des proratas à payer
                        if ($line->type === 'invoiceitem' && $line->description === $lineDescription && $line->amount === self::AUDITS_PRICING) {
                            $this->stripeClient->invoiceItems->delete($line->invoice_item);
                            break 3; // break des 3 foreach
                        }
                    }
                }
            }
        }
    }

    /**
     * @param StripeEvent $event
     * @return void
     * @throws ApiErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     * @throws WedofSubscriptionException
     */
    public function handleInvoiceCreated(StripeEvent $event): void
    {
        $stripeInvoice = Invoice::constructFrom($event->data->object);

        if ($stripeInvoice->billing_reason === 'subscription_cycle') {
            $subscription = $this->subscriptionService->getOneByStripeCustomerId($stripeInvoice->customer);
            foreach ($subscription->getOrganism()->getCertifierCertifications() as $certification) {
                if ($certification->isAuditsPendingCancellation()) {
                    $this->certificationService->deactivateAudits($certification, $this->subscriptionService->getOneByStripeCustomerId($stripeInvoice->customer)->getOrganism());
                }
            }
        }
    }

    //-------------------
    // METHODES PRIVEES
    //-------------------

    /**
     * @param User $user
     * @return string|null
     * @throws ApiErrorException
     */
    private function createCustomer(User $user): ?string
    {
        $organism = $user->getMainOrganism();
        $subscription = $organism->getSubscription();
        $stripeCustomerId = $subscription->getStripeCustomerId();
        if ($stripeCustomerId) {
            return $stripeCustomerId;
        }
        $params = [
            'email' => $user->getEmail(),
            'name' => $organism->getName() ?? $user->getName(),
            'address' => [
                'city' => $organism->getCity(),
                'country' => "FR",
                'line1' => $organism->getName(),
                'line2' => $organism->getAddress(),
                'postal_code' => $organism->getPostalCode()
            ],
            'phone' => $organism->getPhones()[0] ?? null
        ];
        $customer = $this->stripeClient->customers->create($params);
        if ($customer->id) {
            $subscription->setStripeCustomerId($customer->id);
            $this->subscriptionService->save($subscription);
            return $customer->id;
        } else {
            throw new WedofBadRequestHttpException("Une erreur Stripe est survenue");
        }
    }

    /**
     * @param User $user
     * @param string[] $subscriptions
     * @return string|null
     * @throws ApiErrorException
     */
    private function createSubscriptionLink(User $user, array $subscriptions): ?string
    {
        $customerId = $user->getMainOrganism()->getSubscription()->getStripeCustomerId();
        if (!$customerId) {
            $customerId = $this->createCustomer($user);
        }
        $sessionParams = [
            'success_url' => Tools::getEnvValue('WEDOF_BASE_URI') . self::SUCCESS_URL,
            'cancel_url' => Tools::getEnvValue('WEDOF_BASE_URI') . self::CANCEL_URL,
            'payment_method_types' => self::PAYMENT_METHODS,
            'tax_id_collection' => [
                'enabled' => true,
            ],
            'customer_update' => [
                'name' => 'auto',
                'address' => 'auto',
            ],
            'automatic_tax' => [
                'enabled' => true,
            ],
            'line_items' => [
                array_map(function (string $subscription) {
                    $price = ['price' => $subscription];
                    if (!in_array($subscription, $this->getUsagePriceIds())) {
                        $price['quantity'] = 1;
                    }
                    return $price;
                }, $subscriptions)
            ],
            'mode' => 'subscription',
            'allow_promotion_codes' => true,
            'customer' => $customerId
        ];
        $session = $this->stripeClient->checkout->sessions->create($sessionParams);
        return $session->url;
    }

    /**
     * @param Organism $certifier
     * @return void
     * @throws ApiErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    private function sendUsageForCertifier(Organism $certifier): void
    {
        $certifierSubscriptions = self::CERTIFIER_SUBSCRIPTIONS[Tools::getEnv()];
        $subscription = $this->subscriptionService->updateCertificationFoldersNumberCount($certifier->getSubscription());
        if ($subscription->getStripeCustomerId()) {
            $customer = $this->stripeClient->customers->retrieve(
                $subscription->getStripeCustomerId(),
                ['expand' => ['subscriptions']]
            );
            $stripeSubscriptions = $customer->subscriptions->all();
            /** @var StripeSubscription $subscription */
            foreach ($stripeSubscriptions as $stripeSubscription) {
                $items = $stripeSubscription->items->all();
                /** @var SubscriptionItem $item */
                foreach ($items as $item) {
                    $price_name = $subscription->getCertifierType() . '_' . $subscription->getCertificationFoldersNumberCap() . '_' . $subscription->getCertificationFolderPrice();
                    if ($item->price->id === $certifierSubscriptions[$price_name]) {
                        $certificationFoldersNumberCount = $subscription->getCertificationFoldersNumberCount();
                        $usageRecordSummaries = $this->stripeClient->subscriptionItems->allUsageRecordSummaries($item->id, ['limit' => 1]);
                        $lastUsage = UsageRecordSummary::constructFrom($usageRecordSummaries->data[0]);
                        if ($certificationFoldersNumberCount != $lastUsage->total_usage) {
                            $this->stripeClient->subscriptionItems->createUsageRecord(
                                $item->id,
                                [
                                    'quantity' => $certificationFoldersNumberCount,
                                    'action' => 'set'
                                ]
                            );
                        }
                        break;
                    }
                }
            }
        } else {
            $this->logger->warning("Pas de stripeCustomerId pour la souscription d'id " . $subscription->getId());
        }
    }

    /**
     * @param Subscription $subscription
     * @return void
     * @throws ApiErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    private function sendSmsUsageForOrganism(Subscription $subscription): void
    {
        $stripeSubscription = $this->findOrCreateStripeSubscriptionWithProductUsage($subscription, self::PRODUCT_SMS);
        $subscription = $this->subscriptionService->updateSmsSentNumberCount($subscription);
        if ($stripeSubscription) {
            $smsSentNumberCount = $subscription->getSmsSentNumberCount();
            $items = $stripeSubscription->items->all();
            /** @var SubscriptionItem $item */
            foreach ($items as $item) {
                if ($item->price->id === self::SMS_USAGE[Tools::getEnv()]['month']) {
                    $usageRecordSummaries = $this->stripeClient->subscriptionItems->allUsageRecordSummaries($item->id, ['limit' => 1]);
                    $lastUsage = UsageRecordSummary::constructFrom($usageRecordSummaries->data[0]);
                    if ($smsSentNumberCount != $lastUsage->total_usage) {
                        $this->stripeClient->subscriptionItems->createUsageRecord(
                            $item->id,
                            [
                                'quantity' => $smsSentNumberCount,
                                'action' => 'set'
                            ]
                        );
                    }
                    break;
                }
            }
        }
    }

    /**
     * @return array
     */
    private function getUsagePriceIds(): array
    {
        $usagePriceIds = [];
        foreach (self::CERTIFIER_SUBSCRIPTIONS[Tools::getEnv()] as $key => $priceId) {
            if (preg_match("/^usage_/", $key) || preg_match("/^access_/", $key)) {
                $usagePriceIds[] = $priceId;
            }
        }
        return $usagePriceIds;
    }

    /**
     * @param StripeSubscription $stripeSubscription
     * @return string|null
     */
    private function getIntervalFromSubscription(StripeSubscription $stripeSubscription): ?string
    {
        $interval = null;
        foreach ($stripeSubscription->items->data as $item) {
            if ($item->plan && $item->plan->interval) {
                $interval = $item->plan->interval;
            }
        }
        return $interval;
    }

    /**
     * @param Subscription $subscription
     * @param array $optionPrices
     * @return StripeSubscription|null
     * @throws ApiErrorException
     */
    private function findStripeSubscriptionWithOption(Subscription $subscription, array $optionPrices): ?StripeSubscription
    {
        $stripeSubscriptions = $this->stripeClient->subscriptions->all(['customer' => $subscription->getStripeCustomerId(), 'price' => $optionPrices['month']]);
        if ($stripeSubscriptions->count() === 0) {
            $stripeSubscriptions = $this->stripeClient->subscriptions->all(['customer' => $subscription->getStripeCustomerId(), 'price' => $optionPrices['year']]);
        }
        return $stripeSubscriptions->count() > 0 ? $stripeSubscriptions->first() : null;
    }

    /**
     * @param StripeEvent $event
     * @param array $optionPrices
     * @return bool
     */
    private function hadOptionInSubscription(StripeEvent $event, array $optionPrices): bool
    {
        //$event->data->values()[1] => previous_attributes
        try {
            if (!empty($event->data->values()[1]->toArray()['items']['data'])) {
                $items = $event->data->values()[1]->toArray()['items']['data'];
                foreach ($items as $item) {
                    if (in_array($item['price']['id'], $optionPrices)) {
                        return true;
                    }
                }
            }
        } catch (Exception $e) {
            return false;
        }
        return false;
    }

    /**
     * @param Collection $invoiceLines
     * @param Organism $organism
     * @return array
     */
    private function listCertificationsToInvoice(Collection $invoiceLines, Organism $organism): array
    {
        $certificationsToInvoice = [];
        $periodStartDate = null;
        $periodEndDate = null;
        $certifierSubscriptions = self::CERTIFIER_SUBSCRIPTIONS[Tools::getEnv()];

        // on vérifie qu'un invoiceItem sur l'abonnement certificateur existe et on récupère ses dates
        foreach ($invoiceLines->data as $line) {
            if ($line->price->id === $certifierSubscriptions['certifierPlus'] || $line->price->id === $certifierSubscriptions['certifierPlusAccess']) {
                $periodStartDate = $line->period->start;
                $periodEndDate = $line->period->end;
                break;
            }
        }
        // on regarde pour toutes les certifs si on a déjà un invoiceItem et si non on crée un tableau avec les infos nécessaires à la création de l'invoiceItem
        if ($periodStartDate && $periodEndDate) {
            foreach ($organism->getCertifierCertifications() as $certification) {
                $lineDescription = self::AUDITS_LINE_DESCRIPTION . $certification->getExternalId();
                if ($certification->isAllowAudits() && !$certification->getAuditTrialEndDate() && !$certification->isAuditsPendingCancellation()) {
                    $alreadyHasAuditInvoiceItem = false;
                    /** @var InvoiceLineItem $line */
                    foreach ($invoiceLines->data as $line) {
                        if ($line->type === 'invoiceitem' && $line->description === $lineDescription && $line->amount === self::AUDITS_PRICING) {
                            $alreadyHasAuditInvoiceItem = true;
                            break;
                        }
                    }
                } else {
                    $alreadyHasAuditInvoiceItem = true; // on ne crée rien pour ces certifs
                }
                if (!$alreadyHasAuditInvoiceItem) {
                    $certificationsToInvoice[] = [
                        'certification' => $certification,
                        'periodStartDate' => $periodStartDate,
                        'periodEndDate' => $periodEndDate
                    ];
                }
            }
        }

        return $certificationsToInvoice;
    }

    /**
     * @param $optionName
     * @return array[]
     */
    private function getSubscriptionOptionPricesFor($optionName): array
    {
        /** @var WedofApplication $subscriptionOption */
        foreach (Subscription::$SUBSCRIPTION_OPTIONS_APPS as $subscriptionOption) {
            if (method_exists($subscriptionOption, 'availablePrices')
                && $subscriptionOption::getAppId() == $optionName) {
                return $subscriptionOption::availablePrices();
            }
        }
        return [];
    }

    /**
     * @param string $id
     * @param $subscriptionTypes
     * @return int|string|null
     */
    private function findSubscriptionType(string $id, $subscriptionTypes): ?string
    {
        foreach ($subscriptionTypes as $key => $el) {
            if ((is_array($el) && in_array($id, $el)) || $el == $id) {
                return $key;
            }
        }
        return null;
    }
}
