<?php

namespace App\Service;

use App\Application\MessageTemplates\Service\MessageService;
use App\Entity\Attendee;
use App\Entity\Certification;
use App\Entity\CertificationFolder;
use App\Entity\CertificationFolderHistory;
use App\Entity\CertificationFoldersCdcFiles;
use App\Entity\CertifierAccess;
use App\Entity\Organism;
use App\Entity\RegistrationFolder;
use App\Entity\Subscription;
use App\Entity\User;
use App\Event\CertificationFolder\CertificationFolderEvents;
use App\Exception\WedofAccessDeniedHttpException;
use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofConnectionException;
use App\Library\utils\Dictionary;
use App\Library\utils\enums\ActivityTypes;
use App\Library\utils\enums\CertificationFolderAccessModality;
use App\Library\utils\enums\CertificationFolderCdcStates;
use App\Library\utils\enums\CertificationFoldersCdcFilesStates;
use App\Library\utils\enums\CertificationFolderStates;
use App\Library\utils\enums\CertificationFolderType;
use App\Library\utils\enums\CertificationPartnerHabilitation;
use App\Library\utils\enums\CertificationTypes;
use App\Library\utils\enums\DocumentGenerationStates;
use App\Library\utils\enums\RegistrationFolderStates;
use App\Library\utils\Tools;
use App\Message\CreateCertificationFolders;
use App\Message\UpdateCertificationFolder;
use App\Repository\CertificationFolderHistoryRepository;
use App\Repository\CertificationFolderRepository;
use App\Repository\DeliveryRepository;
use App\Service\DataProviders\AutomatorApiService;
use DateInterval;
use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\QueryBuilder;
use ErrorException;
use Exception;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\ContainerInterface;
use Psr\Container\NotFoundExceptionInterface;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\File\File;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Throwable;

class CertificationFolderService implements LoggerAwareInterface
{
    private LoggerInterface $logger;
    private EventDispatcherInterface $dispatcher;
    private AccessService $accessService;
    private CertifierAccessService $certifierAccessService;
    private CertificationFolderRepository $certificationFolderRepository;
    private CertificationPartnerService $certificationPartnerService;
    private MessageBusInterface $messageBus;
    private ActivityService $activityService;
    private AutomatorApiService $automatorApiService;
    private CertificationFolderFileService $certificationFolderFileService;
    private ContainerInterface $container;
    private CertificationFoldersCdcFilesService $certificationFoldersCdcFilesService;
    private DeliveryRepository $deliveryRepository;
    private CertificationFolderHistoryRepository $certificationFolderHistoryRepository;
    private CertificationFolderSurveyService $surveyService;

    public function __construct(EventDispatcherInterface             $dispatcher,
                                CertificationFolderRepository        $certificationFolderRepository,
                                CertificationPartnerService          $certificationPartnerService,
                                CertifierAccessService               $certifierAccessService,
                                AccessService                        $accessService,
                                MessageBusInterface                  $messageBus,
                                ActivityService                      $activityService,
                                AutomatorApiService                  $automatorApiService,
                                CertificationFolderFileService       $certificationFolderFileService,
                                ContainerInterface                   $container,
                                CertificationFoldersCdcFilesService  $certificationFoldersCdcFilesService,
                                DeliveryRepository                   $deliveryRepository,
                                CertificationFolderHistoryRepository $certificationFolderHistoryRepository,
                                CertificationFolderSurveyService     $surveyService
    )
    {
        $this->dispatcher = $dispatcher;
        $this->accessService = $accessService;
        $this->certifierAccessService = $certifierAccessService;
        $this->certificationFolderRepository = $certificationFolderRepository;
        $this->certificationPartnerService = $certificationPartnerService;
        $this->messageBus = $messageBus;
        $this->activityService = $activityService;
        $this->automatorApiService = $automatorApiService;
        $this->certificationFolderFileService = $certificationFolderFileService;
        $this->container = $container;
        $this->certificationFoldersCdcFilesService = $certificationFoldersCdcFilesService;
        $this->deliveryRepository = $deliveryRepository;
        $this->certificationFolderHistoryRepository = $certificationFolderHistoryRepository;
        $this->surveyService = $surveyService;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    //-------------------
    // METHODES PUBLIQUES
    //-------------------
    /**
     * @param Organism $certifier
     * @param array $parameters
     * @return QueryBuilder
     */
    public function listReturnQueryBuilder(Organism $certifier, array $parameters): QueryBuilder
    {
        return $this->certificationFolderRepository->findAllReturnQueryBuilder($certifier, $parameters);
    }

    /**
     * @param Organism|null $organism
     * @param array $parameters
     * @param string $columnId
     * @return float
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function revenueByColumn(?Organism $organism, array $parameters, string $columnId): float
    {
        if (in_array(CertificationFolderStates::ALL()->getValue(), $parameters['state'])) {
            $parameters['state'] = CertificationFolderStates::valuesStatesToString(); // Hack because of "all" fake state
        }
        if (in_array(CertificationFolderCdcStates::ALL()->getValue(), $parameters['cdcState'])) {
            $parameters['cdcState'] = CertificationFolderCdcStates::valuesStatesToString(); // Hack because of "all" fake state
        }
        $parameters = Tools::computeKanbanColumnParameters($parameters, $this->certificationFolderRepository->listColumnConfigs(), $columnId);
        $qb = $this->certificationFolderRepository->findAllReturnQueryBuilder($organism, $parameters);
        return $this->certificationFolderRepository->getRevenue($qb, $organism);
    }

    /**
     * @param User $user
     * @param array $parameters
     * @param array $columnIds
     * @return ArrayCollection
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function listByColumn(User $user, array $parameters, array $columnIds): ArrayCollection
    {
        return $this->certificationFolderRepository->findAllByColumn($user->getMainOrganism(), $parameters, $columnIds);
    }

    /**
     * @param Certification $certification
     * @param string $firstName
     * @param string $lastName
     * @return ArrayCollection
     */
    public function listByCertificationAndAttendeeNames(Certification $certification, string $firstName, string $lastName): ArrayCollection
    {
        return $this->certificationFolderRepository->findAllByCertificationAndAttendeeNames($certification, $firstName, $lastName);
    }

    /**
     * @param Certification $certification
     * @param string $cdcTechnicalId
     * @return ArrayCollection
     */
    public function listByCertificationAndTechnicalId(Certification $certification, string $cdcTechnicalId): ArrayCollection
    {
        return $this->certificationFolderRepository->findAllByCertificationAndTechnicalId($certification, $cdcTechnicalId);
    }

    /**
     * @param Certification $certification
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countByCertification(Certification $certification): int
    {
        return $this->certificationFolderRepository->countForCertification($certification);
    }

    /**
     * @param Organism $partner
     * @param Certification $certification
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countForPartnerAndCertification(Organism $partner, Certification $certification): int
    {
        return $this->certificationFolderRepository->countForPartnerAndCertification($partner, $certification);
    }

    /**
     * @param CertificationFolder $certificationFolder
     * @param User|null $user
     * @return CertificationFolder
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function examToRegister(CertificationFolder $certificationFolder, User $user): CertificationFolder
    {
        $body = ["state" => CertificationFolderStates::TO_REGISTER()];

        switch ($certificationFolder->getState()) {
            case CertificationFolderStates::REFUSED()->getValue():
                $certificationFolder = $this->internalUpdate($certificationFolder, $body, $user);
                break;
            case CertificationFolderStates::REGISTERED()->getValue():
                if ($this->accessService->hasCertificationFolderCertifierEdit($user, $certificationFolder)) {
                    $certificationFolder = $this->internalUpdate($certificationFolder, $body, $user);
                } else {
                    throw new WedofAccessDeniedHttpException("Seul le certificateur peut revenir à l'état 'toRegister' depuis l'état 'registered'");
                }
                break;
            case CertificationFolderStates::ABORTED()->getValue():
                if ($this->findLastStateBeforeAbort($certificationFolder) == CertificationFolderStates::TO_REGISTER()) {
                    $certificationFolder = $this->internalUpdate($certificationFolder, $body, $user);
                } else {
                    throw new WedofAccessDeniedHttpException("Erreur, il n'est pas possible de revenir à l'état 'toRegister' car ce n'était pas le dernier état avant l'abandon du dossier.");
                }
                break;
            default:
                throw new WedofBadRequestHttpException("Erreur, le dossier de certification doit être à l'état 'registered', 'refused' ou 'aborted' pour revenir à l'état 'toRegister'.");
        }
        return $certificationFolder;
    }

    /**
     * @param CertificationFolder $certificationFolder
     * @param User|null $user
     * @param array $body
     * @param bool $ignoreMissingFiles
     * @return CertificationFolder
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function examRegistered(CertificationFolder $certificationFolder, User $user = null, array $body = [], bool $ignoreMissingFiles = false): CertificationFolder
    {
        $isCertifier = $user && $certificationFolder->getCertifier() === $user->getMainOrganism();
        if (!$ignoreMissingFiles && !$isCertifier) {
            $missingFiles = $this->checkForRequiredFilesOnState($certificationFolder, CertificationFolderStates::REGISTERED());
            if ($missingFiles) {
                throw new WedofBadRequestHttpException("Erreur, des fichiers requis sont manquants pour pouvoir changer d'état : " . CertificationFolderStates::REGISTERED()->getValue() . ": " . join(", ", $missingFiles));
            }
        }
        $body = array_merge(["state" => CertificationFolderStates::REGISTERED()], $body);

        switch ($certificationFolder->getState()) {
            case CertificationFolderStates::TO_REGISTER()->getValue():
                if ($certificationFolder->getCertification()->isAutoRegistering() || ($user && $this->accessService->hasCertificationFolderCertifierEdit($user, $certificationFolder))) {
                    $certificationFolder = $this->internalUpdate($certificationFolder, $body, $user);
                    $registrationFolder = $certificationFolder->getRegistrationFolder();
                    // Automatic toTake if it has a registration folder in a specific state
                    if ($registrationFolder) {
                        if ((in_array($registrationFolder->getState(), [RegistrationFolderStates::SERVICE_DONE_DECLARED()->getValue(), RegistrationFolderStates::SERVICE_DONE_VALIDATED()->getValue()]) && $registrationFolder->getCompletionRate() >= 81)
                            || ($registrationFolder->getState() === RegistrationFolderStates::TERMINATED()->getValue() &&
                                (($registrationFolder->getTerminatedReason() && $registrationFolder->getTerminatedReason()->getCode() === '8') || $registrationFolder->getRawData()['trainingActionInfo']['reason'] == '8')) // double égalité entre reason et le code car au moment de l'écriture de ce code reason est une string dans le rawData, il pourrait devenir un int côté CDC
                        ) { // si modif ici modifier dans le updateFromRegistrationFolder aussi
                            $certificationFolder = $this->examToTake($certificationFolder, null, [], $ignoreMissingFiles);
                        }
                        $isRegistrationFolderInTraining = $registrationFolder->getState() == RegistrationFolderStates::IN_TRAINING();
                        if ($certificationFolder->isInTraining() != $isRegistrationFolderInTraining) {
                            $this->internalUpdate($certificationFolder, ['inTraining' => $isRegistrationFolderInTraining]);
                        }
                    }
                } else if ($user) {
                    throw new WedofAccessDeniedHttpException("Seul le certificateur peut enregistrer un dossier de certification.");
                }
                break;
            case CertificationFolderStates::TO_TAKE()->getValue():
                if ($this->accessService->hasCertificationFolderCertifierEdit($user, $certificationFolder)) {
                    $certificationFolder = $this->internalUpdate($certificationFolder, $body, $user);
                } else {
                    throw new WedofAccessDeniedHttpException("Seul le certificateur peut revenir à l'état 'registered' depuis l'état 'toTake'");
                }
                break;
            case CertificationFolderStates::ABORTED()->getValue():
                if ($this->findLastStateBeforeAbort($certificationFolder) == CertificationFolderStates::REGISTERED()) {
                    $certificationFolder = $this->internalUpdate($certificationFolder, $body, $user);
                } else {
                    throw new WedofAccessDeniedHttpException("Erreur, il n'est pas possible de revenir à l'état 'registered' car ce n'était pas le dernier état avant l'abandon du dossier.");
                }
                break;
            default:
                throw new WedofBadRequestHttpException("Erreur, le dossier de certification doit être à l'état 'toRegister' pour être enregistré ou à l'état 'toTake' ou 'aborted' pour revenir à l'état 'registered'.");
        }

        return $certificationFolder;
    }

    /**
     * @param CertificationFolder $certificationFolder
     * @param User $user
     * @param array $body
     * @param bool $ignoreMissingFiles
     * @return CertificationFolder
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function examRefused(CertificationFolder $certificationFolder, User $user, array $body = [], bool $ignoreMissingFiles = false): CertificationFolder
    {
        $isCertifier = $certificationFolder->getCertifier() === $user->getMainOrganism();
        if (!$ignoreMissingFiles && !$isCertifier) {
            $missingFiles = $this->checkForRequiredFilesOnState($certificationFolder, CertificationFolderStates::REFUSED());
            if ($missingFiles) {
                throw new WedofBadRequestHttpException("Erreur, des fichiers requis sont manquants pour pouvoir changer d'état : " . CertificationFolderStates::REFUSED()->getValue() . ": " . join(", ", $missingFiles));
            }
        }
        $body = array_merge(["state" => CertificationFolderStates::REFUSED()], $body);

        switch ($certificationFolder->getState()) {
            case CertificationFolderStates::TO_REGISTER()->getValue():
                if ($this->accessService->hasCertificationFolderCertifierEdit($user, $certificationFolder)) {
                    $certificationFolder = $this->internalUpdate($certificationFolder, $body, $user);
                } else {
                    throw new WedofAccessDeniedHttpException("Seul le certificateur peut refuser le dossier de certification.");
                }
                break;
            default:
                throw new WedofBadRequestHttpException("Erreur, un dossier de certification doit être à l'état 'toRegister' pour être refusé.");
        }

        return $certificationFolder;
    }

    /**
     * @param CertificationFolder $certificationFolder
     * @param User|null $user
     * @param array $body
     * @param bool $ignoreMissingFiles
     * @return CertificationFolder
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function examToTake(CertificationFolder $certificationFolder, User $user = null, array $body = [], bool $ignoreMissingFiles = false): CertificationFolder
    {
        $isCertifier = $user && $certificationFolder->getCertifier() === $user->getMainOrganism();
        if (!$ignoreMissingFiles && !$isCertifier) {
            $missingFiles = $this->checkForRequiredFilesOnState($certificationFolder, CertificationFolderStates::TO_TAKE());
            if ($missingFiles) {
                throw new WedofBadRequestHttpException("Erreur, des fichiers requis sont manquants pour pouvoir changer d'état : " . CertificationFolderStates::TO_TAKE()->getValue() . ": " . join(", ", $missingFiles));
            }
        }
        $body = array_merge(["state" => CertificationFolderStates::TO_TAKE()], $body);

        switch ($certificationFolder->getState()) {
            case CertificationFolderStates::REGISTERED()->getValue():
                $registrationFolder = $certificationFolder->getRegistrationFolder();
                if ($registrationFolder && !in_array($registrationFolder->getState(), [RegistrationFolderStates::IN_TRAINING(), RegistrationFolderStates::TERMINATED(), RegistrationFolderStates::SERVICE_DONE_DECLARED(), RegistrationFolderStates::SERVICE_DONE_VALIDATED()])) {
                    throw new WedofBadRequestHttpException("Erreur, le dossier de formation associé au dossier de certification n'est pas dans l'état attendu ('inTraining', 'terminated', 'serviceDoneDeclared' ou 'serviceDoneValidated')");
                }
                $certificationFolder = $this->internalUpdate($certificationFolder, $body, $user);
                break;
            case CertificationFolderStates::TO_CONTROL()->getValue():
                if ($this->accessService->hasCertificationFolderCertifierEdit($user, $certificationFolder)) {
                    $certificationFolder = $this->internalUpdate($certificationFolder, $body, $user);
                } else {
                    throw new WedofBadRequestHttpException("Erreur, seul le certificateur ou le partenaire habilité à évaluer peuvent faire revenir le dossier de certification à l'état 'toTake'.");
                }
                break;
            case CertificationFolderStates::ABORTED()->getValue():
                if ($this->findLastStateBeforeAbort($certificationFolder) == CertificationFolderStates::TO_TAKE()) {
                    $certificationFolder = $this->internalUpdate($certificationFolder, $body, $user);
                } else {
                    throw new WedofAccessDeniedHttpException("Erreur, il n'est pas possible de revenir à l'état 'toTake' car ce n'était pas le dernier état avant l'abandon du dossier.");
                }
                break;
            default:
                throw new WedofBadRequestHttpException("Erreur, un dossier de certification doit être à l'état 'registered' pour demander le passage de l'examen ou à l'état 'toControl' ou 'aborted' pour revenir à l'état 'toTake'.");
        }

        return $certificationFolder;
    }

    /**
     * @param CertificationFolder $certificationFolder
     * @param User $user
     * @param array $body
     * @param bool $ignoreMissingFiles
     * @return CertificationFolder
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function examToControl(CertificationFolder $certificationFolder, User $user, array $body = [], bool $ignoreMissingFiles = false): CertificationFolder
    {
        $isCertifier = $certificationFolder->getCertifier() === $user->getMainOrganism();
        if (!$ignoreMissingFiles && !$isCertifier) {
            $missingFiles = $this->checkForRequiredFilesOnState($certificationFolder, CertificationFolderStates::TO_CONTROL());
            if ($missingFiles) {
                throw new WedofBadRequestHttpException("Erreur, des fichiers requis sont manquants pour pouvoir changer d'état : " . CertificationFolderStates::TO_CONTROL()->getValue() . ": " . join(", ", $missingFiles));
            }
            // check examinationType not empty here so we can ingest an xml file generated form outside Wedof and not getting an error while doing it if 'modalitePassageExamen' is not filled
            if (empty($body['examinationType']) && empty($certificationFolder->getExaminationType())) {
                throw new WedofBadRequestHttpException("Erreur, le type d'examen doit être renseigné pour contrôler l'examen : 'A_DISTANCE' ou 'EN_PRESENTIEL' ou 'MIXTE'.");
            }
        }

        $body = array_merge(["state" => CertificationFolderStates::TO_CONTROL()], $body);
        foreach ($this->getPropertiesUpdatableOnlyOnSuccessStateChange() as $successProperty) {
            $body[$successProperty] = null;
            if ($successProperty === 'certificateGenerationState') {
                $body[$successProperty] = DocumentGenerationStates::NOT_GENERATED()->getValue();
            }
        }

        switch ($certificationFolder->getState()) {
            case CertificationFolderStates::TO_TAKE()->getValue():
            case CertificationFolderStates::TO_RETAKE()->getValue():
                if ($this->accessService->hasCertificationFolderEvaluate($user, $certificationFolder)) {
                    $certificationFolder = $this->internalUpdate($certificationFolder, $body, $user);
                } else {
                    throw new WedofBadRequestHttpException("Erreur, seul le certificateur ou le partenaire habilité à évaluer peuvent déclarer un dossier à l'état 'toControl'.");
                }
                break;
            case CertificationFolderStates::FAILED()->getValue():
            case CertificationFolderStates::SUCCESS()->getValue():
                if ($this->accessService->hasCertificationFolderCertifierEdit($user, $certificationFolder)) {
                    $certificationFolder = $this->internalUpdate($certificationFolder, $body, $user);
                } else {
                    throw new WedofAccessDeniedHttpException("Seul le certificateur peut revenir à l'état 'toControl'.");
                }
                break;
            default:
                throw new WedofBadRequestHttpException("Erreur, le dossier de certification doit être à l'état 'toTake' ou 'toRetake' pour être contrôlé ou à l'état 'failed' ou 'success' pour revenir à l'état 'toControl'.");
        }

        return $certificationFolder;
    }

    /**
     * @param CertificationFolder $certificationFolder
     * @param User $user
     * @param array $body
     * @param bool $ignoreMissingFiles
     * @return CertificationFolder
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function examSuccess(CertificationFolder $certificationFolder, User $user, array $body, bool $ignoreMissingFiles = false): CertificationFolder
    {
        $isCertifier = $certificationFolder->getCertifier() === $user->getMainOrganism();
        if (!$ignoreMissingFiles && !$isCertifier) {
            $missingFiles = $this->checkForRequiredFilesOnState($certificationFolder, CertificationFolderStates::SUCCESS());
            if ($missingFiles) {
                throw new WedofBadRequestHttpException("Erreur, des fichiers requis sont manquants pour pouvoir changer d'état : " . CertificationFolderStates::SUCCESS()->getValue() . ": " . join(", ", $missingFiles));
            }
        }
        if (empty($body['issueDate'])) {
            throw new WedofBadRequestHttpException("Erreur, la date de délivrance (issueDate) est obligatoire");
        }
        $body = array_merge(["state" => CertificationFolderStates::SUCCESS()], $body);

        switch ($certificationFolder->getState()) {
            case CertificationFolderStates::TO_CONTROL()->getValue():
                if ($this->accessService->hasCertificationFolderCertifierEdit($user, $certificationFolder)) {
                    $certification = $certificationFolder->getCertification();
                    if (!empty($certification->getValidityPeriod()) && $certificationFolder->getExpirationDate() === null) {
                        $expirationDate = clone $body['issueDate'];
                        $expirationDate->add(new DateInterval('P' . $certification->getValidityPeriod() . 'Y'));
                        $body['expirationDate'] = $expirationDate;
                    } else if (isset($body['expirationDate'])) {
                        unset($body['expirationDate']);
                    }

                    if (!$certificationFolder->isCdcToExport() && count($certificationFolder->getCertificationFoldersCdcFiles())) {
                        // permet de repasser à toExport si le dossier à été modifié aps un état 'success'
                        /**  @var CertificationFoldersCdcFiles $certificationFoldersCdcFiles */
                        foreach ($certificationFolder->getCertificationFoldersCdcFiles() as $certificationFolderCdcFile) {
                            if ($certificationFolderCdcFile->getState() === CertificationFoldersCdcFilesStates::PROCESSED_OK()->getValue()) {
                                $body['cdcToExport'] = true;
                            }
                        }
                    }

                    $certificationFolder = $this->internalUpdate($certificationFolder, $body, $user);
                    if (!$certificationFolder->isCdcCompliant()) {
                        $this->dispatcher->dispatch(new CertificationFolderEvents($certificationFolder), CertificationFolderEvents::MISSING_DATA);
                    }
                    $this->surveyService->updateExperienceStartDates($body['issueDate'], $certificationFolder->getSurvey());
                } else {
                    throw new WedofAccessDeniedHttpException("Seul le certificateur peut déclarer l'examen comme réussi.");
                }
                break;
            default:
                throw new WedofBadRequestHttpException("Erreur, le dossier de certification doit être à l'état 'toControl' pour être déclaré comme réussi.");
        }

        return $certificationFolder;
    }

    /**
     * @param CertificationFolder $certificationFolder
     * @param User $user
     * @param array $body
     * @param bool $ignoreMissingFiles
     * @return CertificationFolder
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function examToRetake(CertificationFolder $certificationFolder, User $user, array $body = [], bool $ignoreMissingFiles = false): CertificationFolder
    {
        $isCertifier = $certificationFolder->getCertifier() === $user->getMainOrganism();
        if (!$ignoreMissingFiles && !$isCertifier) {
            $missingFiles = $this->checkForRequiredFilesOnState($certificationFolder, CertificationFolderStates::TO_RETAKE());
            if ($missingFiles) {
                throw new WedofBadRequestHttpException("Erreur, des fichiers requis sont manquants pour pouvoir changer d'état : " . CertificationFolderStates::TO_RETAKE()->getValue() . ": " . join(", ", $missingFiles));
            }
        }
        $body = array_merge(["state" => CertificationFolderStates::TO_RETAKE()], $body);

        switch ($certificationFolder->getState()) {
            case CertificationFolderStates::TO_CONTROL()->getValue():
            case CertificationFolderStates::FAILED()->getValue():
                if ($this->accessService->hasCertificationFolderCertifierEdit($user, $certificationFolder)) {
                    $certificationFolder = $this->internalUpdate($certificationFolder, $body, $user);
                } else {
                    throw new WedofAccessDeniedHttpException("Seul le certificateur peut demander à repasser l'examen.");
                }
                break;
            case CertificationFolderStates::ABORTED()->getValue():
                if ($this->findLastStateBeforeAbort($certificationFolder) == CertificationFolderStates::TO_RETAKE()) {
                    $certificationFolder = $this->internalUpdate($certificationFolder, $body, $user);
                } else {
                    throw new WedofAccessDeniedHttpException("Erreur, il n'est pas possible de revenir à l'état 'toRetake' car ce n'était pas le dernier état avant l'abandon du dossier.");
                }
                break;
            default:
                throw new WedofBadRequestHttpException("Erreur, un dossier de certification doit être à l'état 'toControl', 'failed' afin de redemander le passage de l'examen ou dans l'état 'aborted' pour revenir à l'état 'toRetake'.");
        }

        return $certificationFolder;
    }

    /**
     * @param CertificationFolder $certificationFolder
     * @param User $user
     * @param array $body
     * @param bool $ignoreMissingFiles
     * @return CertificationFolder
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function examFailed(CertificationFolder $certificationFolder, User $user, array $body = [], bool $ignoreMissingFiles = false): CertificationFolder
    {
        $isCertifier = $certificationFolder->getCertifier() === $user->getMainOrganism();
        if (!$ignoreMissingFiles && !$isCertifier) {
            $missingFiles = $this->checkForRequiredFilesOnState($certificationFolder, CertificationFolderStates::FAILED());
            if ($missingFiles) {
                throw new WedofBadRequestHttpException("Erreur, des fichiers requis sont manquants pour pouvoir changer d'état : " . CertificationFolderStates::FAILED()->getValue() . ": " . join(", ", $missingFiles));
            }
        }
        $body = array_merge(["state" => CertificationFolderStates::FAILED()], $body);

        switch ($certificationFolder->getState()) {
            case CertificationFolderStates::TO_CONTROL()->getValue():
                if ($this->accessService->hasCertificationFolderCertifierEdit($user, $certificationFolder)) {
                    $certificationFolder = $this->internalUpdate($certificationFolder, $body, $user);
                } else {
                    throw new WedofAccessDeniedHttpException("Seul le certificateur peut déclarer l'examen comme échoué.");
                }
                break;
            default:
                throw new WedofBadRequestHttpException("Erreur, un dossier de certification doit être dans l'état 'toControl' pour pouvoir être déclaré comme échoué.");
        }

        return $certificationFolder;
    }

    /**
     * @param CertificationFolder $certificationFolder
     * @param User|null $user
     * @param array $body
     * @param bool $ignoreMissingFiles
     * @return CertificationFolder
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function examAborted(CertificationFolder $certificationFolder, User $user = null, array $body = [], bool $ignoreMissingFiles = false): CertificationFolder
    {
        $isCertifier = $user && $certificationFolder->getCertifier() === $user->getMainOrganism();
        if (!$ignoreMissingFiles && !$isCertifier) {
            $missingFiles = $this->checkForRequiredFilesOnState($certificationFolder, CertificationFolderStates::ABORTED());
            if ($missingFiles) {
                throw new WedofBadRequestHttpException("Erreur, des fichiers requis sont manquants pour pouvoir changer d'état : " . CertificationFolderStates::ABORTED()->getValue() . ": " . join(", ", $missingFiles));
            }
        }
        $body = array_merge(["state" => CertificationFolderStates::ABORTED()], $body);

        if (in_array($certificationFolder->getState(), CertificationFolderStates::abortableStates())) {
            $registrationFolder = $certificationFolder->getRegistrationFolder();
            if ($registrationFolder && $registrationFolder->getState() == RegistrationFolderStates::IN_TRAINING()) {
                throw new WedofBadRequestHttpException("Erreur, il n'est pas possible d'abandonner un dossier de certification alors que le dossier de formation associé est à l'état 'inTraining'");
            }
            $certificationFolder = $this->internalUpdate($certificationFolder, $body, $user);
        } else {
            throw new WedofBadRequestHttpException("Erreur, le dossier de certification doit être dans l'état " . implode(",", CertificationFolderStates::abortableStates()) . " pour être déclaré comme abandonné.");
        }

        return $certificationFolder;
    }

    /**
     * @param CertificationFolder $certificationFolder
     * @param float $amoutHt
     * @return CertificationFolder
     */
    public function updateAsync(CertificationFolder $certificationFolder, float $amoutHt): CertificationFolder
    {
        $message = new UpdateCertificationFolder($certificationFolder->getId(), $amoutHt);
        $this->messageBus->dispatch($message);
        return $certificationFolder;
    }

    /**
     * @param CertificationFolder $certificationFolder
     * @param array $rawData
     * @param Organism|null $organism
     * @param User|null $user
     * @return CertificationFolder
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function update(CertificationFolder $certificationFolder, array $rawData, Organism $organism = null, User $user = null): CertificationFolder
    {
        unset($rawData['state']); // Ensure that state is not updated from there, proper methods should be used instead
        foreach (['issueDate', 'digitalProofLink', 'gradePass', 'expirationDate', 'europeanLanguageLevel', 'detailedResult'] as $successProperty) {
            unset($rawData[$successProperty]);
        }

        $data = Tools::filterDataToUpdateOnObject($certificationFolder, $rawData, $this->getUpdatableProperties());
        // Hack because of timezone mess: these dates come as Europe/Paris from the controller but as UTC from the database, so equality can only be checked by format
        // This can be removed once we use UTC in controller
        if (!empty($data['enrollmentDate']) && !empty($certificationFolder->getEnrollmentDate()) && $data['enrollmentDate']->format('Y-m-d') === $certificationFolder->getEnrollmentDate()->format('Y-m-d')) {
            unset($data['enrollmentDate']);
        }

        if (!empty($data['certificateId']) && $this->getOneByCertificationAndCertificateId($certificationFolder->getCertification(), $data['certificateId'])) {
            throw new WedofBadRequestHttpException("Un autre dossier portant sur la certification " . $certificationFolder->getCertification()->getExternalId() . " possède l'ID de parchemin " . $data['certificateId'] . ".");
        }

        $certification = $certificationFolder->getCertification();
        if (!$organism || !$certification->isCertifier($organism)) {
            $partnerRestrictedFields = [];
            $isPartnerAndCanEvaluate = false;
            if ($organism) {
                $certificationPartner = $this->certificationPartnerService->getByCertificationAndPartner($certification, $organism);
                $isPartnerAndCanEvaluate = isset($certificationPartner) && in_array($certificationPartner->getHabilitation(), [CertificationPartnerHabilitation::TRAIN_EVALUATE(), CertificationPartnerHabilitation::EVALUATE()]);
            }
            if ($isPartnerAndCanEvaluate) {
                $partnerRestrictedFields = ['enrollmentDate', 'examinationDate', 'examinationEndDate', 'examinationType', 'examinationPlace'];
                if (in_array($certificationFolder->getState(), [CertificationFolderStates::TO_REGISTER(), CertificationFolderStates::REGISTERED(), CertificationFolderStates::TO_TAKE(), CertificationFolderStates::TO_CONTROL(), CertificationFolderStates::TO_RETAKE()])) {
                    $partnerRestrictedFields[] = 'comment';
                }
            } else if (in_array($certificationFolder->getState(), [CertificationFolderStates::TO_REGISTER(), CertificationFolderStates::REGISTERED(), CertificationFolderStates::TO_TAKE()])) {
                $partnerRestrictedFields[] = 'comment';
            }
            foreach (array_keys($data) as $changedProperty) {
                if (!in_array($changedProperty, $partnerRestrictedFields)) {
                    throw new WedofBadRequestHttpException("Erreur, le champ '" . $changedProperty . "' ne peut pas être mis à jour par un partenaire.");
                }
            }
        }

        // The following are checked only when regular update, they should not be done in internalUpdate (internal is used for state change & forceUpdate from XMl / CSV)
        $stateRestrictedFields = null;
        switch ($certificationFolder->getState()) {
            case CertificationFolderStates::SUCCESS()->getValue():
                $stateRestrictedFields = ['comment', 'tags', 'amountHt', 'cdcExcluded', 'certificate', 'certificateId', 'metadata', 'partner', 'skillSets', 'badgeAssertion'];
                break;
            case CertificationFolderStates::FAILED()->getValue():
            case CertificationFolderStates::ABORTED()->getValue():
                $stateRestrictedFields = ['comment', 'tags', 'amountHt', 'metadata', 'partner', 'skillSets'];
                break;
            case CertificationFolderStates::REFUSED()->getValue():
                $stateRestrictedFields = ['comment', 'tags', 'amountHt', 'type', 'metadata', 'partner', 'skillSets'];
                break;
            default;
        }
        if (isset($stateRestrictedFields)) {
            foreach (array_keys($data) as $changedProperty) {
                if (!in_array($changedProperty, $stateRestrictedFields)) {
                    throw new WedofBadRequestHttpException("Erreur, le champ '" . $changedProperty . "' ne peut pas être mis à jour à l'état '" . $certificationFolder->getState() . "'");
                }
            }
        }

        return $this->internalUpdate($certificationFolder, $data, $user);
    }

    /**
     * @param Certification $certification
     * @param Attendee $attendee
     * @param Organism|null $partner
     * @param array $body
     * @param RegistrationFolder|null $registrationFolder
     * @param User|null $user
     * @param Organism|null $certifier
     * @return CertificationFolder|bool
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function create(Certification $certification, Attendee $attendee, ?Organism $partner, array $body, RegistrationFolder $registrationFolder = null, User $user = null, Organism $certifier = null)
    {
        $amount = null;
        $certificationPartner = null;
        if (isset($partner)) {
            $certificationPartner = $this->certificationPartnerService->getByCertificationAndPartner($certification, $partner, false);
            if ($certificationPartner && $certificationPartner->getAmountHt()) {
                $amount = $certificationPartner->getAmountHt();
            }
        }

        if (!$certifier && isset($partner)) {
            if ($certificationPartner && $certificationPartner->getCertifier()) {
                $certifier = $certificationPartner->getCertifier();
            } else if ($certification->isCertifier($partner)) {
                $certifier = $partner;
            } else {
                $certifier = $certification->getDefaultCertifier();
            }
        }
        $certifierUser = $certifier ? $certifier->getOwnedBy() : null;
        $certifierSubscription = $certifier ? $certifier->getSubscription() : null;
        $isCertifierOrPartnerWithActiveAccess = $certifier && (($user && $certifier === $user->getMainOrganism()) || (!$partner || $partner === $certifier || $this->certifierAccessService->getByOrganisms($certifier, $partner)));
        if ($certifier && $certifierUser && $certifierSubscription && $certifierSubscription->isAllowCertifiers() && $isCertifierOrPartnerWithActiveAccess) {

            $certificationFolder = new CertificationFolder();
            $certificationFolder->setState(CertificationFolderStates::TO_REGISTER());
            $certificationFolder->setCertification($certification);
            $certificationFolder->setAttendee($attendee);
            $certificationFolder->setPartner($partner);
            $certificationFolder->setCertifiedData(true);
            $certificationFolder->setHistory(new CertificationFolderHistory());
            $certificationFolder->setStateLastUpdate(new DateTime()); // valorisation temporaire pour permettre la sauvegarde en BDD - rectifié dans le setHistoryAndCreateActivity
            $certificationFolder->setCdcState(CertificationFolderCdcStates::NOT_EXPORTED()->getValue());
            $certificationFolder->setCertifier($certifier);

            if ($registrationFolder) {
                $certificationFolder->setRegistrationFolder($registrationFolder);
                $certificationFolder->setRegistrationFolderExternalId($registrationFolder->getExternalId());
                $certificationFolder->setDataProvider($registrationFolder->getType());
                $certificationFolder->setType(CertificationFolderType::OF());
            } else {
                $certificationFolder->setType($body['type'] ?? CertificationFolderType::OF());
            }

            if ($certification->getExaminationType()) {
                $certificationFolder->setExaminationType($certification->getExaminationType());
            }

            if (in_array($certification->getType(), [CertificationTypes::INTERNAL()->getValue(), CertificationTypes::PREVENTION()->getValue()])) {
                $certificationFolder->setCdcExcluded(true);
                $certificationFolder->setCdcToExport(false);
            } else {
                $certificationFolder->setCdcToExport(true);
            }

            if ($amount) {
                $certificationFolder->setAmountHt($amount);
            } else if ($certification->getAmountHt() && $certifier !== $partner) {
                $certificationFolder->setAmountHt($certification->getAmountHt());
            }

            $properties = array('optionName', 'accessModality', 'accessModalityVae', 'tags', 'enrollmentDate', 'tiersTemps');

            foreach ($properties as $property) {
                if ($property === 'tags' && isset($body['tags'])) {
                    $certificationFolder->setTagsText(implode(', ', $body['tags']));
                } else if (key_exists($property, $body)) {
                    $setMethodName = "set" . ucwords($property);
                    $certificationFolder->{$setMethodName}($body[$property]);
                }
            }

            if ($certificationFolder->getEnrollmentDate()) {
                Tools::throwIfDateNotContemporary($certificationFolder->getEnrollmentDate());
            }

            if ($certification->isAllowPartialSkillSets()) {
                $skillSets = [];
                if ($registrationFolder && $registrationFolder->getTrainingSkillSets()->count() > 0) {
                    $skillSets = $registrationFolder->getTrainingSkillSets();
                } else if (isset($body['skillSets'])) {
                    $skillSets = $body['skillSets'];
                }
                $certificationFolder = $certificationFolder->setSkillSets($skillSets);
                if (!$certificationFolder->isAllSkillSets()) {
                    $certificationFolder->setFullCertification(false);
                    $certificationFolder->setCdcToExport(false);
                }
            }

            $this->save($certificationFolder);

            //must be save before to use generated id
            $idd = $certificationFolder->getPartner() ? $certificationFolder->getPartner()->getId() : $certificationFolder->getCertifier()->getId();
            $externalId = $certificationFolder->getCertification()->getCode() . '-' . crc32($idd . $certificationFolder->getId());
            $certificationFolder->setExternalId($externalId);
            $certificationFolder->setSurvey($this->surveyService->create($certificationFolder));
            $this->save($certificationFolder);

            $this->setHistoryAndCreateActivityForStateChange($certificationFolder, $user, true);
            $this->sendEvents($certificationFolder, CertificationFolderEvents::CREATED, $certificationFolder->getState()); // We know inTraining did not change as it is initialized to false and updated in updateFromRegistrationFolder
            if ($certification->isAutoRegistering() && $certificationFolder->getState() === CertificationFolderStates::TO_REGISTER()->getValue()) {
                if (!$partner || $partner === $certifier || empty($this->checkForRequiredFilesOnState($certificationFolder, CertificationFolderStates::REGISTERED()))) {
                    try {
                        $certificationFolder = $this->examRegistered($certificationFolder);
                    } catch (WedofBadRequestHttpException $e) {
                        //try but if condition failed on next state don't stop creation (missing file for example..)
                    }
                }
            }
            return $certificationFolder;
        } else {
            return false;
        }
    }

    /**
     * @param Organism $certifier
     * @return void
     * @throws Throwable
     */
    public function createFromRegistrationFolders(Organism $certifier)
    {
        $certifierAccesses = $this->certifierAccessService->listForCertifier($certifier);
        /** @var CertifierAccess $certifierAccess */
        foreach ($certifierAccesses as $certifierAccess) {
            $partner = $certifierAccess->getPartner();
            if ($partner !== $certifier) { // Sometimes they are partner of themselves on France Competences
                $this->createFromRegistrationFoldersForPartner($certifier, $partner);
            }
        }
        $this->createFromRegistrationFoldersForPartner($certifier, $certifier);
    }

    /**
     * @param Organism $certifier
     * @param Organism $partner
     * @return void
     */
    public function createFromRegistrationFoldersForPartner(Organism $certifier, Organism $partner)
    {
        $certifications = $certifier->getCertifierCertifications()->toArray();
        /** @var CertifierAccess $certifierAccess */
        foreach ($certifications as $certification) {
            $this->messageBus->dispatch(new CreateCertificationFolders($partner->getSiret(), $certification->getId()));
        }
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @return CertificationFolder|false|null
     * @throws Throwable
     */
    public function createFromRegistrationFolder(RegistrationFolder $registrationFolder)
    {
        $certificationFolder = $registrationFolder->getCertificationFolder();
        if (empty($certificationFolder) && $registrationFolder->getCertification()) {
            $certificationFolder = $this->create($registrationFolder->getCertification(), $registrationFolder->getAttendee(), $registrationFolder->getOrganism(), [], $registrationFolder);
        }
        return $certificationFolder;
    }

    /**
     * @param CertificationFolder $certificationFolder
     * @param RegistrationFolder $registrationFolder
     * @return void
     * @throws Throwable
     */
    public function updateFromRegistrationFolder(CertificationFolder $certificationFolder, RegistrationFolder $registrationFolder)
    {
        try {
            // si modif ici modifier dans le examRegistered aussi
            if ($certificationFolder->getState() === CertificationFolderStates::REGISTERED()->getValue() &&
                ((in_array($registrationFolder->getState(), [RegistrationFolderStates::SERVICE_DONE_DECLARED()->getValue(), RegistrationFolderStates::SERVICE_DONE_VALIDATED()->getValue()]) && $registrationFolder->getCompletionRate() >= 81)
                    || ($registrationFolder->getState() === RegistrationFolderStates::TERMINATED()->getValue() && ($registrationFolder->getTerminatedReason() && $registrationFolder->getTerminatedReason()->getCode() === '8') || $registrationFolder->getRawData()['trainingActionInfo']['reason'] == '8')) // code est un string
            ) {
                $this->examToTake($certificationFolder);
            } else if (in_array($registrationFolder->getState(), [RegistrationFolderStates::CANCELED_BY_ATTENDEE_NOT_REALIZED(), RegistrationFolderStates::CANCELED_BY_ORGANISM()])) {
                $newComment = $certificationFolder->getComment() ? $certificationFolder->getComment() . "\n" : '';
                $newComment .= RegistrationFolderStates::toFrString($registrationFolder->getState());
                $this->examAborted($certificationFolder, null, ['comment' => $newComment]);
            }
        } catch (Throwable $e) {
            // Legitimate error may happen if missing docs
            $this->logger->error($e);
        }
        $registrationFolderInTraining = $registrationFolder->getState() == RegistrationFolderStates::IN_TRAINING();
        if ($certificationFolder->isInTraining() != $registrationFolderInTraining) {
            $this->internalUpdate($certificationFolder, ['inTraining' => $registrationFolderInTraining]);
        }
    }

    /**
     * @param Subscription $subscription
     * @param bool $useCertificationFoldersDates
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws \DateMalformedStringException
     */
    public function countForCertifierSubscription(Subscription $subscription, bool $useCertificationFoldersDates = false): int
    {
        return $this->certificationFolderRepository->countForCertifierSubscription($subscription, $useCertificationFoldersDates);
    }

    /**
     * @param int $entityId
     * @return CertificationFolder|null
     */
    public function getById(int $entityId): ?CertificationFolder
    {
        return $this->certificationFolderRepository->find($entityId);
    }

    /**
     * @param string $registrationFolderExternalId
     * @return void
     */
    public function getByRegistrationFolderExternalId(string $registrationFolderExternalId): ?CertificationFolder
    {
        return $this->certificationFolderRepository->findOneBy(['registrationFolderExternalId' => $registrationFolderExternalId]);
    }

    /**
     * @param string $externalId
     * @return void
     */
    public function getByExternalId(string $externalId): ?CertificationFolder
    {
        return $this->certificationFolderRepository->findOneBy(['externalId' => $externalId]);
    }

    /**
     * @param string $entityId
     * @return CertificationFolder|null
     */
    public function getByEntityId(string $entityId): ?CertificationFolder
    {
        return is_numeric($entityId) ? $this->getById(intval($entityId)) : $this->getByExternalId($entityId);
    }

    /**
     * @param Attendee $attendee
     * @param Certification $certification
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countForAttendeeAndCertification(Attendee $attendee, Certification $certification): int
    {
        return $this->certificationFolderRepository->countForAttendeeAndCertification($attendee, $certification);
    }

    /**
     * @param Organism $organism
     * @param Certification $certification
     * @param array $parameters
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countForOrganismByCertificationForCdc(Organism $organism, Certification $certification, array $parameters): int
    {
        return $this->certificationFolderRepository->countForOrganismByCertificationForCdc($organism, $certification, $parameters);
    }

    /**
     * This method allows updating a CF from a state to another (same or higher) and its fields
     * @param CertificationFolder $certificationFolder
     * @param array $certificationFolderData
     * @param CertificationFolderStates $targetState
     * @param User $user
     * @param array $options
     * @return CertificationFolder
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function forceUpdate(CertificationFolder $certificationFolder, array $certificationFolderData, CertificationFolderStates $targetState, User $user, array $options = []): CertificationFolder
    {
        $options = array_merge([
            'ignoreMissingFiles' => false,
            'preserveExistingData' => true
        ], $options);

        if (CertificationFolderStates::TO_RETAKE()->equals($targetState)) {
            throw new Exception("Erreur, le passage à l'état À répasser n'est pas supporté dans l'import pour le moment");
        }
        if ($certificationFolder->getState() === CertificationFolderStates::TO_RETAKE()->getValue()) {
            throw new Exception("Erreur, la mise à jour de dossier de certification étant à l'état À répasser n'est pas supportée dans l'import pour le moment");
        }
        $isSameState = ($certificationFolder->getState() === $targetState->getValue());
        if (!$isSameState && !CertificationFolderStates::isForwardTransition($certificationFolder->getState(), $targetState)) {
            throw new Exception("Erreur, l'état visé " . $targetState->getValue() . " ne peut pas être atteint depuis l'état actuel " . $certificationFolder->getState() . " via l'import");
        }
        // Prepare data
        $certificationFolderData = Tools::filterDataToUpdateOnObject($certificationFolder, $certificationFolderData, $this->getUpdatableProperties()); // keep only where getter exists AND actual changes
        $certificationFolderData = array_filter($certificationFolderData, fn($value) => $value !== null && $value != ''); // keep only where submitted value is not null (=> cannot "remove" values with this method)
        if ($options['preserveExistingData']) {
            $certificationFolderData = array_filter($certificationFolderData, function ($value, $key) use ($certificationFolder) {
                $ucKey = ucwords($key);
                $allowWriteNewData = false;
                if ($ucKey === 'Tags') {
                    $allowWriteNewData = true;
                }
                if (method_exists($certificationFolder, "get" . $ucKey) && ($certificationFolder->{"get" . $ucKey}() === null || $certificationFolder->{"get" . $ucKey}() === '')) {
                    $allowWriteNewData = true;
                }
                if (method_exists($certificationFolder, "is" . $ucKey) && $certificationFolder->{"is" . $ucKey}() === null) {
                    $allowWriteNewData = true;
                }
                return $allowWriteNewData;
            }, ARRAY_FILTER_USE_BOTH);
        }
        if (isset($certificationFolderData['cdcTechnicalId']) && (string)$certificationFolder->getId() === $certificationFolderData['cdcTechnicalId']) {
            unset($certificationFolderData['cdcTechnicalId']); // No need to set cdcTechnicalId if identical to certificationFolder->getId()
        }
        // Update
        if ($isSameState) {
            $certificationFolder = $this->internalUpdate($certificationFolder, $certificationFolderData, $user);
        } else if ($targetState->equals(CertificationFolderStates::REFUSED())) {
            $certificationFolder = $this->examRefused($certificationFolder, $user, $certificationFolderData, $options['ignoreMissingFiles']);
        } else if ($targetState->equals(CertificationFolderStates::ABORTED())) {
            $certificationFolder = $this->examAborted($certificationFolder, $user, $certificationFolderData, $options['ignoreMissingFiles']);
        } else {
            // As aborted / refused are excluded and we know here that we are in a forward state, we know the CF must be at least become REGISTERED
            if ($certificationFolder->getState() === CertificationFolderStates::TO_REGISTER()->getValue()) {
                $certificationFolder = $this->examRegistered($certificationFolder, $user, [], $options['ignoreMissingFiles']);
            }
            // Here we know the CF is already either REGISTERED, TO_TAKE, TO_CONTROL, FAILED or SUCCESS so it can be updated almost fully apart from Success / Fail ones
            $certificationFolderUpdateData = $certificationFolderData;
            $certificationFolderSuccessData = null;
            $certificationFolderFailedData = null;
            if ($targetState->equals(CertificationFolderStates::SUCCESS())) {
                $certificationFolderSuccessProperties = $this->getPropertiesUpdatableOnlyOnSuccessStateChange();
                $certificationFolderUpdateData = array_filter($certificationFolderData, function ($value, $key) use ($certificationFolderSuccessProperties) {
                    return !in_array($key, $certificationFolderSuccessProperties);
                }, ARRAY_FILTER_USE_BOTH);
                $certificationFolderSuccessData = array_filter($certificationFolderData, function ($value, $key) use ($certificationFolderSuccessProperties) {
                    return in_array($key, $certificationFolderSuccessProperties);
                }, ARRAY_FILTER_USE_BOTH);
            } else if ($targetState->equals(CertificationFolderStates::FAILED())) {
                $certificationFolderFailedProperties = ['detailedResult', 'europeanLanguageLevel'];
                $certificationFolderUpdateData = array_filter($certificationFolderData, function ($value, $key) use ($certificationFolderFailedProperties) {
                    return !in_array($key, $certificationFolderFailedProperties);
                }, ARRAY_FILTER_USE_BOTH);
                $certificationFolderFailedData = array_filter($certificationFolderData, function ($value, $key) use ($certificationFolderFailedProperties) {
                    return in_array($key, $certificationFolderFailedProperties);
                }, ARRAY_FILTER_USE_BOTH);
            }
            $certificationFolder = $this->internalUpdate($certificationFolder, $certificationFolderUpdateData, $user);
            // Upper states if required (be careful, examRegistered may have put it "above" the desired state through automatic toTake
            if ($certificationFolder->getState() !== $targetState->getValue() && !($certificationFolder->getState() === CertificationFolderStates::TO_TAKE()->getValue() && $targetState->equals(CertificationFolderStates::REGISTERED()))) {
                // We know that we must at least toTake
                if ($certificationFolder->getState() === CertificationFolderStates::REGISTERED()->getValue()) {
                    $certificationFolder = $this->examToTake($certificationFolder, $user, [], $options['ignoreMissingFiles']);
                }
                if (!$targetState->equals(CertificationFolderStates::TO_TAKE())) {
                    if ($certificationFolder->getState() === CertificationFolderStates::TO_TAKE()->getValue()) {
                        $certificationFolder = $this->examToControl($certificationFolder, $user, [], $options['ignoreMissingFiles']);
                    }
                    if ($targetState->equals(CertificationFolderStates::SUCCESS())) {
                        $certificationFolder = $this->examSuccess($certificationFolder, $user, $certificationFolderSuccessData, $options['ignoreMissingFiles']);
                    } else if ($targetState->equals(CertificationFolderStates::FAILED())) {
                        $certificationFolder = $this->examFailed($certificationFolder, $user, $certificationFolderFailedData, $options['ignoreMissingFiles']);
                    }
                }
            }
        }
        return $certificationFolder;
    }

    /**
     * @param Certification $certification
     * @param string $certificateId
     * @return CertificationFolder|null
     */
    public function getOneByCertificationAndCertificateId(Certification $certification, string $certificateId): ?CertificationFolder
    {
        return $this->certificationFolderRepository->findOneByCertificationAndCertificateId($certification, $certificateId);
    }

    /**
     * @param CertificationFolder $certificationFolder
     * @param array $fileType
     * @param bool $manual
     * @return void
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function generateCertificate(CertificationFolder $certificationFolder, array $fileType): ?array
    {
        $this->logger->info("[CertificationFolder] " . $certificationFolder->getId() . " generate certificate");
        $this->save($certificationFolder);
        $certification = $certificationFolder->getCertification();
        $container = $this->container;
        $computedContext = Dictionary::getComputedContext($certificationFolder, null, null, null, $container, false, 'document', $certificationFolder->getCertifier());
        $identifiant = explode('-', $certificationFolder->getExternalId())[1]; // need to be overwrited as it is not be set before but in the workflow itself
        $computedContext['']['identifiant'] = $identifiant;
        $certifier = $certificationFolder->getCertifier();
        $parameters = [
            'uid' => $fileType['googleId'],
            'fileTypeId' => $fileType['id'],
            'userCertifierEmail' => $certifier->getMainUsers()[0]->getEmail(),
            'externalId' => $certificationFolder->getExternalId(),
            'id' => $certificationFolder->getId(),
            'certificateUrl' => 'https://www.wedof.fr/app/public/qrCode?url=/app/public/certificateHolder/' . $certification->getExternalId() . '/' . $identifiant,
            'computedContext' => $computedContext
        ];
        $certificateGenerated = $this->automatorApiService->generateCertificate($parameters, $certifier, 'application/json');
        if ($certificateGenerated) {
            $certificationFolder = $certificationFolder->setCertificateId($identifiant);
            if (!$certificationFolder->getDigitalProofLink()) {
                $digitalProofLink = 'https://www.wedof.fr/app/public/certificateHolder/' . $certification->getExternalId() . '/' . $identifiant;
                $certificationFolder->setDigitalProofLink($digitalProofLink);
            }
            $this->save($certificationFolder);
        }
        return $certificateGenerated;
    }

    /**
     * @param CertificationFolder $certificationFolder
     * @return bool
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function isAttendeeDataUpdateNeeded(CertificationFolder $certificationFolder): bool
    {
        return $certificationFolder->getCdcState() !== CertificationFolderCdcStates::PROCESSED_OK()->getValue()
            && (!$certificationFolder->getAttendee()->isCdcCompliant() || !$certificationFolder->isCdcToExport())
            && $this->certificationFolderRepository->countAllNotCpfProcessedOkForAttendee($certificationFolder->getAttendee()) === 0;
    }

    /**
     * @param Attendee $attendee
     * @return bool
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function hasNoCpfProcessedOkForAttendee(Attendee $attendee): bool
    {
        return $this->certificationFolderRepository->countAllNotCpfProcessedOkForAttendee($attendee) > 0;
    }

    /**
     * @param CertificationFolder $certificationFolder
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function delete(CertificationFolder $certificationFolder)
    {
        if ($certificationFolder->getRegistrationFolder()) {
            throw new WedofBadRequestHttpException("Suppression impossible : le dossier de certification est associé à un dossier de formation");
        }

        $files = $this->certificationFolderFileService->listByEntityId($certificationFolder);
        if ($files) {
            foreach ($files as $file) {
                $this->certificationFolderFileService->delete($file, null, true);
            }
        }
        /** @var MessageService $messageService */
        $messageService = $this->container->get(MessageService::class);
        $messages = $messageService->listByEntityId(CertificationFolder::CLASSNAME, $certificationFolder->getId());
        if ($messages) {
            foreach ($messages as $message) {
                $messageService->delete($message);
            }
        }
        $deliveries = $this->deliveryRepository->findBy(['entityClass' => CertificationFolder::CLASSNAME, 'entityId' => $certificationFolder->getId()]);
        if ($deliveries) {
            foreach ($deliveries as $delivery) {
                $this->deliveryRepository->delete($delivery);
            }
        }
        $activities = $this->activityService->listByEntity(CertificationFolder::CLASSNAME, $certificationFolder->getId());
        if ($activities) {
            foreach ($activities as $activity) {
                $this->activityService->delete($activity);
            }
        }
        if ($certificationFolder->getCdcState() !== CertificationFolderCdcStates::NOT_EXPORTED()->getValue()) {
            $cfCdcFiles = $this->certificationFoldersCdcFilesService->listByEntity($certificationFolder);
            if ($cfCdcFiles) {
                foreach ($cfCdcFiles as $cfCdcFile) {
                    $this->certificationFoldersCdcFilesService->delete($cfCdcFile);
                }
            }
        }
        $certificationFolderToDelete = (clone $certificationFolder);
        $this->surveyService->delete($certificationFolder->getSurvey()); // suppression en cascade du CertificationFolderSurvey
        $this->certificationFolderHistoryRepository->delete($certificationFolder->getHistory()); // suppression en cascade du CertificationFolder
        $this->sendEvents($certificationFolderToDelete, CertificationFolderEvents::DELETED);
    }

    /**
     * @param CertificationFolder $certificationFolder
     * @return CertificationFolder
     * @throws Throwable
     */
    public function save(CertificationFolder $certificationFolder): CertificationFolder
    {
        return $this->certificationFolderRepository->save($certificationFolder);
    }

    /**
     * @param CertificationFolder $certificationFolder
     * @param array|Collection $skillsSets
     * @param bool $createActivity
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function setSkillSets(CertificationFolder $certificationFolder, $skillsSets, bool $createActivity = false)
    {
        $oldSkillSets = $certificationFolder->getSkillSets();
        if (count($oldSkillSets) === 0 && count($skillsSets) === 0) {
            return;
        }
        $certificationFolder = $certificationFolder->setSkillSets($skillsSets);
        $isAllSkillSets = $certificationFolder->isAllSkillSets();
        if ($certificationFolder->isFullCertification() !== $isAllSkillSets) {
            $certificationFolder->setFullCertification($isAllSkillSets);
            $certificationFolder->setCdcToExport($isAllSkillSets);
        }
        if ($createActivity) {
            $this->activityService->create([
                'title' => 'Les blocs de compétences ont été mis à jour',
                'type' => ActivityTypes::UPDATE(),
                'eventTime' => new DateTime(),
                'field' => 'skillSets'
            ], null, $certificationFolder, false);
        }
    }


    /**
     * @param string $badgeAssertion
     * @return bool|array
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    public function verifyBadgeAssertion(string $badgeAssertion)
    {
        $this->logger->info("[Verify badge Assertion]: $badgeAssertion");
        $response = Tools::getHttpClient()->request("GET", $badgeAssertion);
        if ($response->getStatusCode() !== 200) {
            return false;
        }
        $content = json_decode($response->getContent(), true);
        $isAssertion = isset($content['type']) && Tools::contains($content['type'], 'Assertion');
        $hasImageUrl = isset($content['image']['id']);
        $isRevoked = isset($content['revoked']) && $content['revoked'] === true;
        if (!$isAssertion || !$hasImageUrl || $isRevoked) {
            return false;
        }
        if (isset($content['expires'])) {
            $expiresDate = (new DateTime($content['expires']))->format('Y-m-d');
            $today = (new DateTime('now'))->format('Y-m-d');
            if ($expiresDate < $today) {
                return false;
            }
        }
        return $content;
    }

    //-----------------
    // METHODES PRIVEES
    //-----------------

    /**
     * @param CertificationFolder $certificationFolder
     * @param array $rawData
     * @param User|null $user
     * @return CertificationFolder
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    private function internalUpdate(CertificationFolder $certificationFolder, array $rawData = [], User $user = null): CertificationFolder
    {
        if (isset($rawData['tags'])) {
            $rawData['tags'] = array_map('strtolower', $rawData['tags']); // Required to enforce lower + avoid bug with lower / upper
        }

        if (array_key_exists('partner', $rawData)
            && ($user && $user->getMainOrganism() === $certificationFolder->getCertifier())) {
            $certificationFolder->setPartner($rawData['partner']);
        }

        $data = Tools::filterDataToUpdateOnObject($certificationFolder, $rawData, $this->getUpdatableProperties());
        $targetState = $data['state'] ?? $certificationFolder->getState();
        $previousState = $certificationFolder->getState();

        if ((isset($data['detailedResult']) || isset($data['europeanLanguageLevel'])) && !in_array($targetState, [CertificationFolderStates::FAILED(), CertificationFolderStates::TO_RETAKE(), CertificationFolderStates::SUCCESS()])) {
            throw new WedofBadRequestHttpException("Erreur, 'detailedResult', 'europeanLanguageLevel' ne sont modifiables que dans les états : 'failed', 'success', 'toRetake'.");
        }
        if ((isset($data['issueDate']) || isset($data['expirationDate']) || isset($data['gradePass']) || isset($data['digitalProofLink']) || isset($data['badgeAssertion'])) && $targetState != CertificationFolderStates::SUCCESS()) {
            throw new WedofBadRequestHttpException("Erreur, 'issueDate', 'expirationDate', 'gradePass', 'digitalProofLink' et 'badgeAssertion' ne sont modifiables qu'à l'état : 'success'.");
        }

        $accessModalityValue = array_key_exists('accessModality', $data) ? $data['accessModality'] : $certificationFolder->getAccessModality();
        $accessModalityVaeValue = array_key_exists('accessModalityVae', $data) ? $data['accessModalityVae'] : $certificationFolder->getAccessModalityVae();
        if ($accessModalityValue === CertificationFolderAccessModality::VAE()->getValue() && !$accessModalityVaeValue) {
            throw new WedofBadRequestHttpException("Erreur, vous devez préciser le type de VAE en renseignant 'accessModalityVae'.");
        }
        if ($accessModalityValue !== CertificationFolderAccessModality::VAE()->getValue() && $accessModalityVaeValue) {
            throw new WedofBadRequestHttpException("Erreur, vous ne pouvez pas préciser 'accessModalityVae' si 'accessModality' n'est pas de type VAE.");
        }

        $enrollmentDate = array_key_exists('enrollmentDate', $data) ? $data['enrollmentDate'] : $certificationFolder->getEnrollmentDate();
        $examinationDate = array_key_exists('examinationDate', $data) ? $data['examinationDate'] : $certificationFolder->getExaminationDate();
        $examinationEndDate = array_key_exists('examinationEndDate', $data) ? $data['examinationEndDate'] : $certificationFolder->getExaminationEndDate();
        $issueDate = array_key_exists('issueDate', $data) ? $data['issueDate'] : $certificationFolder->getIssueDate();
        // We compare the formats in string to avoid comparing on schedules which poses problem during the control
        if ($enrollmentDate && $examinationDate && $enrollmentDate->format('Y-m-d') > $examinationDate->format('Y-m-d')) {
            throw new WedofBadRequestHttpException("Erreur, la date de passage d'examen (examinationDate) ne peut être antérieure à la date d'inscription (enrollmentDate). ");
        }
        if ($examinationDate && $examinationEndDate && $examinationDate->format('Y-m-d') > $examinationEndDate->format('Y-m-d')) {
            throw new WedofBadRequestHttpException("Erreur, la date de fin de passage d'examen (examinationEndDate) ne peut être antérieure à la date de passage d'examen (examinationDate). ");
        }
        if ($examinationEndDate && $issueDate && $examinationEndDate->format('Y-m-d') > $issueDate->format('Y-m-d')) {
            throw new WedofBadRequestHttpException("Erreur, la date de délivrance de la certification (issueDate) ne peut être antérieure à la date de fin de passage d'examen (examinationEndDate). ");
        }
        if ($issueDate) {
            $today = new DateTime();
            if ($issueDate->format('Y-m-d') > $today->format('Y-m-d')) {
                throw new WedofBadRequestHttpException("Erreur, la date de délivrance de la certification (issueDate) ne peut être postérieure à la date du jour. ");
            }
        }
        Tools::throwIfDateNotContemporary($enrollmentDate);
        Tools::throwIfDateNotContemporary($examinationDate);
        Tools::throwIfDateNotContemporary($examinationEndDate);
        Tools::throwIfDateNotContemporary($issueDate);

        foreach ($this->getUpdatableProperties() as $property) {
            if ($property === 'tiersTemps' && isset($data['tiersTemps'])) {
                $certificationFolder->setTiersTemps($data['tiersTemps']);
            } else if ($property === 'metadata' && isset($data['metadata'])) {
                $certificationFolder->setMetadata(empty($data['metadata']) ? null : Tools::removeSpacesInKeys($data['metadata']));
            } else if ($property === 'tags' && isset($data['tags'])) {
                $certificationFolder->setTagsText(implode(', ', $data['tags']));
            } else if ($property === 'state' && isset($data['state'])) { // state is not nullable property
                $certificationFolder->setState($data['state']);
            } else if ($property === 'certificate') {
                if (isset($data['certificate']) && $data['certificate'] instanceof File) {
                    if (!$certificationFolder->getCertification()->isAllowGenerateCertificate()) {
                        $this->certificationFolderFileService->create($data['certificate'], Certification::CERTIFICATE_FILE_TYPE_ID, $certificationFolder, false, $user);
                    } else {
                        throw new WedofBadRequestHttpException("Il n'est pas possible d'ajouter un parchemin lorsque la génération automatique est activée.");
                    }
                } else if (array_key_exists('certificate', $data) && $data['certificate'] === null) {
                    throw new WedofBadRequestHttpException("Pour supprimer un parchemin, veuillez utiliser DELETE /api/certificationFolders/{externalId}/files/" . Certification::CERTIFICATE_FILE_TYPE_ID);
                }
            } else if ($property === 'skillSets') {
                if (isset($data['skillSets']) && (!$certificationFolder->getRegistrationFolder() || $certificationFolder->getRegistrationFolder()->getTrainingSkillSets()->count() === 0)) {
                    $this->setSkillSets($certificationFolder, $data['skillSets']);
                }
            } else if (array_key_exists($property, $data)) {
                $setMethodName = "set" . ucwords($property);
                $certificationFolder->{$setMethodName}($data[$property]);
            }
        }

        $newState = $certificationFolder->getState();
        $stateChanged = $previousState !== $newState;
        if ($stateChanged) {
            $this->setHistoryAndCreateActivityForStateChange($certificationFolder, $user);
        }
        $inTrainingEventsToSend = $this->setInTrainingChange($certificationFolder);
        $certificationFolder->setInTraining($inTrainingEventsToSend === [CertificationFolderEvents::IN_TRAINING_STARTED]);
        $this->save($certificationFolder);
        $this->sendEvents($certificationFolder, CertificationFolderEvents::UPDATED, $stateChanged ? $newState : null, $inTrainingEventsToSend);
        return $certificationFolder;
    }

    /**
     * @return array
     */
    private function getUpdatableProperties(): array
    {
        return array('partner', 'amountHt', 'examinationDate', 'examinationEndDate', 'examinationPlace', 'examinationType', 'issueDate', 'expirationDate', 'detailedResult',
            'digitalProofLink', 'state', 'comment', 'tags', 'type', 'gradePass', 'examinationCenterZipCode', 'verbatim', 'optionName',
            'europeanLanguageLevel', 'accessModality', 'accessModalityVae', 'enrollmentDate', 'cdcToExport', 'cdcTechnicalId', 'certifiedData', 'inTraining', 'cdcExcluded', 'certificate', 'certificateId', 'certificateGenerationState', 'metadata', 'tiersTemps', 'skillSets', 'badgeAssertion');
    }

    /**
     * @return array
     */
    private function getPropertiesUpdatableOnlyOnSuccessStateChange(): array
    {
        return ['issueDate', 'digitalProofLink', 'gradePass', 'expirationDate', 'europeanLanguageLevel', 'detailedResult', 'certificate', 'certificateId', 'certificateGenerationState', 'badgeAssertion'];
    }

    /**
     * @param CertificationFolder $certificationFolder
     * @param string $originalEventName
     * @param string|null $newState
     * @param array $inTrainingEvents
     */
    private function sendEvents(CertificationFolder $certificationFolder, string $originalEventName, string $newState = null, array $inTrainingEvents = []): void
    {
        $event = new CertificationFolderEvents($certificationFolder);
        if (!empty($newState)) {
            $stateEventName = 'certificationFolder.' . $newState;
            $this->dispatcher->dispatch($event, $stateEventName);
            $this->logger->info("[" . $certificationFolder->getId() . "][event] CertificationFolder event dispatched: $stateEventName");
        }
        foreach ($inTrainingEvents as $inTrainingEvent) {
            $this->dispatcher->dispatch($event, $inTrainingEvent);
            $this->logger->info("[" . $certificationFolder->getId() . "][event] CertificationFolder event dispatched: $inTrainingEvent");
        }
        $this->dispatcher->dispatch($event, $originalEventName);
        $this->logger->info("[" . $certificationFolder->getId() . "][event] CertificationFolder dispatched: $originalEventName");
    }

    /**
     * @param CertificationFolder $certificationFolder
     * @return string
     * @throws Exception
     */
    private function findLastStateBeforeAbort(CertificationFolder $certificationFolder): string
    {
        $dateArray = [
            [
                'label' => CertificationFolderStates::TO_REGISTER()->getValue(),
                'date' => $certificationFolder->getHistory()->getToRegisterDate()
            ],
            [
                'label' => CertificationFolderStates::REGISTERED()->getValue(),
                'date' => $certificationFolder->getHistory()->getRegisteredDate()
            ],
            [
                'label' => CertificationFolderStates::TO_TAKE()->getValue(),
                'date' => $certificationFolder->getHistory()->getToTakeDate()
            ],
            [
                'label' => CertificationFolderStates::TO_RETAKE()->getValue(),
                'date' => $certificationFolder->getHistory()->getToRetakeDate()
            ]
        ];

        usort($dateArray, function ($a, $b) {
            $ad = $a['date'];
            $bd = $b['date'];

            if ($ad == $bd) {
                return 0;
            }

            return $ad > $bd ? -1 : 1;
        });

        return $dateArray[0]['label'];
    }

    /**
     * @param CertificationFolder $certificationFolder
     * @param CertificationFolderStates $state
     * @return array
     */
    private function checkForRequiredFilesOnState(CertificationFolder $certificationFolder, CertificationFolderStates $state): array
    {
        $certificationFolderFileTypes = $certificationFolder->getCertification()->getCertificationFolderFileTypes();
        $missingFileTypes = Tools::getMissingFileTypesForState($certificationFolderFileTypes, $certificationFolder->getFiles(), $state->getValue());
        return array_column($missingFileTypes, 'name');
    }

    /**
     * @param CertificationFolder $certificationFolder
     * @param User|null $user
     * @param bool $creation
     * @return void
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    private function setHistoryAndCreateActivityForStateChange(CertificationFolder $certificationFolder, User $user = null, bool $creation = false): void
    {
        $newState = $certificationFolder->getState();
        $registrationFolder = $certificationFolder->getRegistrationFolder();
        $history = $certificationFolder->getHistory();
        $changeDate = ($newState !== CertificationFolderStates::TO_REGISTER()->getValue() || !$registrationFolder || !$registrationFolder->getHistory()->getAcceptedDate())
            ? new DateTime()
            : $registrationFolder->getHistory()->getAcceptedDate();
        if (($history->getToRegisterDate() && $changeDate->getTimestamp() === $history->getToRegisterDate()->getTimestamp())
            || ($history->getRegisteredDate() && $changeDate->getTimestamp() === $history->getRegisteredDate()->getTimestamp())) { // gérer les passages automatiques d'état
            $changeDate = $changeDate->modify('+1 second');
        }
        $history->{"set" . ucwords($newState) . "Date"}($changeDate);

        $certificationFolder->setStateLastUpdate($changeDate);

        if ($creation) {
            $this->createCreateActivity($certificationFolder, $user);
        } else {
            $this->createUpdateStateActivity($certificationFolder, $user);
        }
    }

    /**
     * @param CertificationFolder $certificationFolder
     * @return array
     */
    private function setInTrainingChange(CertificationFolder $certificationFolder): array
    {
        $events = [];
        if (!$registrationFolder = $certificationFolder->getRegistrationFolder()) {
            return $events;
        }

        $history = $certificationFolder->getHistory();
        $registrationHistory = $registrationFolder->getHistory();
        $initialInTrainingStartedDate = $history->getInTrainingStartedDate();
        $initialInTrainingEndedDate = $history->getInTrainingEndedDate();
        $history->setInTrainingStartedDate($registrationHistory->getInTrainingDate());
        $history->setInTrainingEndedDate($registrationHistory->getTerminatedDate());

        if (!$initialInTrainingStartedDate && $history->getInTrainingStartedDate() !== $initialInTrainingStartedDate) {
            $events[] = CertificationFolderEvents::IN_TRAINING_STARTED;
        }
        if (!$initialInTrainingEndedDate && $history->getInTrainingEndedDate() !== $initialInTrainingEndedDate) {
            $events[] = CertificationFolderEvents::IN_TRAINING_ENDED;
        }

        return $events;
    }

    /**
     * @param CertificationFolder $certificationFolder
     * @param User|null $user
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    private function createCreateActivity(CertificationFolder $certificationFolder, User $user = null): void
    {
        $this->activityService->create([
            'title' => 'Le dossier de certification a été créé',
            'type' => ActivityTypes::CREATE(),
            'eventTime' => $certificationFolder->getHistory()->getToRegisterDate(),
            'field' => 'state',
            'newValue' => CertificationFolderStates::TO_REGISTER()->getValue(),
            'origin' => $user ? null : 'Wedof'
        ], $user, $certificationFolder, false);
    }

    /**
     * @param CertificationFolder $certificationFolder
     * @param User|null $user
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    private function createUpdateStateActivity(CertificationFolder $certificationFolder, User $user = null): void
    {
        $stateString = $certificationFolder->getState();

        $this->activityService->create([
            'title' => "Le dossier de certification est passé à l'état " . CertificationFolderStates::toFrString($stateString),
            'type' => ActivityTypes::UPDATE_STATE(),
            'eventTime' => new DateTime(),
            'field' => 'state',
            'newValue' => $stateString,
            'origin' => $user ? null : 'Wedof'
        ], $user, $certificationFolder, false);
    }

    /**
     * @param User $user
     * @param CertificationFolder $certificationFolder
     * @return array
     */
    public function getActions(CertificationFolder $certificationFolder, User $user): array
    {
        $nextStatesByState = [
            CertificationFolderStates::TO_REGISTER()->getValue() => [
                CertificationFolderStates::REFUSED()->getValue() => ['isCertifier'],
                CertificationFolderStates::REGISTERED()->getValue() => ['isCertifier'],
                CertificationFolderStates::ABORTED()->getValue() => ['canAbort']
            ],
            CertificationFolderStates::REFUSED()->getValue() => [
                CertificationFolderStates::TO_REGISTER()->getValue() => [] //empty array necessary
            ],
            CertificationFolderStates::REGISTERED()->getValue() => [
                CertificationFolderStates::TO_REGISTER()->getValue() => ['isCertifier'],
                CertificationFolderStates::TO_TAKE()->getValue() => ['isTrainingStarted'],
                CertificationFolderStates::ABORTED()->getValue() => ['canAbort']
            ],
            CertificationFolderStates::TO_TAKE()->getValue() => [
                CertificationFolderStates::REGISTERED()->getValue() => ['isCertifier'],
                CertificationFolderStates::TO_CONTROL()->getValue() => ['canEvaluate'],
                CertificationFolderStates::SUCCESS()->getValue() => ['hasRequiredDocumentsForToControl', 'isCertifier'],
                CertificationFolderStates::FAILED()->getValue() => ['hasRequiredDocumentsForToControl', 'isCertifier'],
                CertificationFolderStates::ABORTED()->getValue() => ['canAbort']
            ],
            CertificationFolderStates::TO_CONTROL()->getValue() => [
                CertificationFolderStates::TO_TAKE()->getValue() => ['isCertifier'],
                CertificationFolderStates::TO_RETAKE()->getValue() => ['isCertifier'],
                CertificationFolderStates::FAILED()->getValue() => ['isCertifier'],
                CertificationFolderStates::SUCCESS()->getValue() => ['isCertifier']
            ],
            CertificationFolderStates::TO_RETAKE()->getValue() => [
                CertificationFolderStates::TO_CONTROL()->getValue() => ['canEvaluate'],
                CertificationFolderStates::ABORTED()->getValue() => ['canEvaluate']
            ],
            CertificationFolderStates::FAILED()->getValue() => [
                CertificationFolderStates::TO_CONTROL()->getValue() => ['canEvaluate'],
                CertificationFolderStates::TO_RETAKE()->getValue() => ['canEvaluate']
            ],
            CertificationFolderStates::ABORTED()->getValue() => [
                CertificationFolderStates::TO_REGISTER()->getValue() => ['isPreviousState'],
                CertificationFolderStates::REGISTERED()->getValue() => ['isPreviousState'],
                CertificationFolderStates::TO_TAKE()->getValue() => ['isPreviousState'],
                CertificationFolderStates::TO_RETAKE()->getValue() => ['isPreviousState', 'canEvaluate']
            ],
            CertificationFolderStates::SUCCESS()->getValue() => [
                CertificationFolderStates::TO_CONTROL()->getValue() => ['isCertifier']
            ]
        ];
        $nextStates = $nextStatesByState[$certificationFolder->getState()];
        return $this->checkNextStatesConditions($certificationFolder, $user, $nextStates);
    }

    /**
     * @param User $user
     * @param CertificationFolder $certificationFolder
     * @param array $nextStates
     * @return array
     */
    private function checkNextStatesConditions(CertificationFolder $certificationFolder, User $user, array $nextStates): array
    {
        $certification = $certificationFolder->getCertification();
        $certificationFolderFileTypes = $certification->getCertificationFolderFileTypes($certificationFolder) ?? [];
        $organism = $user->getMainOrganism();
        $isCertifier = $certificationFolder->getCertifier() === $organism;
        $nextStatesConditions = [];
        foreach ($nextStates as $nextState => $conditionTypes) {
            $nextStateConditions = [];
            //always check for hasRequiredDocuments
            $conditionTypes[] = 'hasRequiredDocuments';
            foreach ($conditionTypes as $conditionType) {
                $tooltip = '';
                $allowAction = true;
                switch ($conditionType) {
                    case 'isCertifier':
                        if (!$isCertifier) {
                            $tooltip = "Vous n'avez pas l'habilitation pour effectuer ce changement d'état.";
                            $allowAction = false;
                        }
                        break;
                    case 'canEvaluate':
                        if ($isCertifier) {
                            $canEvaluate = true;
                        } else {
                            $certificationPartner = $this->certificationPartnerService->getByCertificationAndPartner($certification, $organism);
                            $canEvaluate = $certificationPartner && in_array($certificationPartner->getHabilitation(), [CertificationPartnerHabilitation::EVALUATE()->getValue(), CertificationPartnerHabilitation::TRAIN_EVALUATE()->getValue()]);
                        }
                        if (!$canEvaluate) {
                            $tooltip = "Vous n'avez pas l'habilitation pour effectuer ce changement d'état.";
                            $allowAction = false;
                        }
                        break;
                    case 'isTrainingStarted':
                        $validStates = [
                            RegistrationFolderStates::IN_TRAINING()->getValue(),
                            RegistrationFolderStates::TERMINATED()->getValue(),
                            RegistrationFolderStates::SERVICE_DONE_DECLARED()->getValue(),
                            RegistrationFolderStates::SERVICE_DONE_VALIDATED()->getValue(),
                        ];
                        $isTrainingStarted = empty($certificationFolder->getRegistrationFolder()) || in_array($certificationFolder->getRegistrationFolder()->getState(), $validStates);
                        if (!$isTrainingStarted) {
                            $tooltip = "Le dossier de formation correspondant n'est pas encore rentré en formation.";
                            $allowAction = false;
                        }
                        break;
                    case 'hasRequiredDocuments':
                        $missingFileTypes = Tools::getMissingFileTypesForState($certificationFolderFileTypes, $certificationFolder->getFiles(), $nextState);
                        $hasRequiredDocuments = count($missingFileTypes) === 0;
                        if (!$hasRequiredDocuments) {
                            if ($isCertifier) {
                                $tooltip = "Attention, des documents requis à l'état " . CertificationFolderStates::toFrString($nextState) . " n'ont pas été déposés.";
                            } else {
                                $tooltip = "Des documents requis à l'état " . CertificationFolderStates::toFrString($nextState) . " n'ont pas été déposés.";
                                $allowAction = false;
                            }
                        }
                        break;
                    case 'hasRequiredDocumentsForToControl': // Hack
                        $targetState = CertificationFolderStates::TO_CONTROL()->getValue();
                        $missingFileTypes = Tools::getMissingFileTypesForState($certificationFolderFileTypes, $certificationFolder->getFiles(), $targetState);
                        $hasRequiredDocumentsForTargetState = count($missingFileTypes) === 0;
                        if (!$hasRequiredDocumentsForTargetState) {
                            if ($isCertifier) {
                                $tooltip = "Attention, des documents requis à l'état " . CertificationFolderStates::toFrString($targetState) . " n'ont pas été déposés.";
                            } else {
                                $tooltip = "Des documents requis à l'état " . CertificationFolderStates::toFrString($targetState) . " n'ont pas été déposés.";
                                $allowAction = false;
                            }
                        }
                        break;
                    case 'canAbort':
                        $canAbort = empty($certificationFolder->getRegistrationFolder()) || $certificationFolder->getRegistrationFolder()->getState() !== RegistrationFolderStates::IN_TRAINING()->getValue();
                        if (!$canAbort) {
                            $tooltip = "Le dossier de formation lié à ce dossier de certification est dans l'état en formation.";
                            $allowAction = false;
                        }
                        break;
                    case 'isPreviousState':
                        if (!$this->isPreviousState($certificationFolder->getHistory(), $nextState)) {
                            $tooltip = "Ce dossier ne peut être déplacer à cet état.";
                            $allowAction = false;
                        }
                        break;
                }
                $nextStateConditions[] = ['type' => $conditionType, 'allowAction' => $allowAction, 'tooltip' => $tooltip];
            }
            $nextStatesConditions[$nextState] = $nextStateConditions;
        }
        return $nextStatesConditions;
    }

    /**
     * @param CertificationFolderHistory $certificationFolderHistory
     * @param string $nextState
     * @return bool
     */
    private function isPreviousState(CertificationFolderHistory $certificationFolderHistory, string $nextState): bool
    {
        $stateDates = [
            ['state' => CertificationFolderStates::TO_REGISTER()->getValue(), 'date' => $certificationFolderHistory->getToRegisterDate()],
            ['state' => CertificationFolderStates::REGISTERED()->getValue(), 'date' => $certificationFolderHistory->getRegisteredDate()],
            ['state' => CertificationFolderStates::TO_TAKE()->getValue(), 'date' => $certificationFolderHistory->getToTakeDate()],
            ['state' => CertificationFolderStates::TO_RETAKE()->getValue(), 'date' => $certificationFolderHistory->getToRetakeDate()],
        ];
        usort($stateDates, function ($a, $b) {
            return $a['date'] > $b['date'] ? -1 : 1;
        });
        // Check if the next state was also the previous state (which means a "revert")
        return $stateDates[0]['state'] === $nextState;
    }
}
