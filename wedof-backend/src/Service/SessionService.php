<?php
// src/Service/SessionService.php
namespace App\Service;

use App\Entity\Organism;
use App\Entity\Session;
use App\Entity\Training;
use App\Entity\TrainingAction;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\enums\SessionStates;
use App\Library\utils\Tools;
use App\Repository\SessionRepository;
use App\Service\DataProviders\BaseApiService;
use App\Service\DataProviders\CpfApiService;
use App\Service\DataProviders\KairosAifApiService;
use App\Service\DataProviders\OpcoCfaApiService;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\QueryBuilder;
use Throwable;

class SessionService
{

    private EntityManagerInterface $entityManager;
    private SessionRepository $sessionRepository;

    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    public function __construct(EntityManagerInterface $entityManager, SessionRepository $sessionRepository)
    {
        $this->entityManager = $entityManager;
        $this->sessionRepository = $sessionRepository;
    }

    /**
     * @param array $rawData
     * @param TrainingAction $trainingAction
     * @return Session
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function createOrUpdate(array $rawData, TrainingAction $trainingAction): Session
    {
        $dataProvider = DataProviders::from($trainingAction->getTraining()->getDataProvider());
        /** @var CpfApiService|KairosAifApiService|OpcoCfaApiService $apiService */
        $apiService = BaseApiService::getCatalogApiServiceByDataProvider($dataProvider);
        $session = $this->getByExternalId($rawData['externalId']);
        if ($session === null) {
            $session = new Session();
            $session->setExternalId($rawData['externalId']);
            $session->setTrainingAction($trainingAction);
        }
        if (isset($rawData['beginDate']) && $rawData['beginDate'] != null) {
            $session->setStartDate(Tools::createDateFromString($rawData['beginDate']));
        }
        if (isset($rawData['endDate']) && $rawData['endDate'] != null) {
            $session->setEndDate(Tools::createDateFromString($rawData['endDate']));
        }
        $session->setState($apiService->getSessionState($rawData, $trainingAction)->getValue());
        $session->setRawData($rawData);

        $this->sessionRepository->save($session);
        return $session;
    }

    /**
     * @param array $rawData
     * @param TrainingAction $trainingAction
     * @return ArrayCollection
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function createOrUpdateSessions(array $rawData, TrainingAction $trainingAction): ArrayCollection
    {
        $sessions = [];
        foreach ($rawData as $sessionRawData) {
            $sessions[] = $this->createOrUpdate($sessionRawData, $trainingAction);
        }
        return new ArrayCollection($sessions);
    }

    /**
     * @param string $externalId
     * @return Session|null
     */
    public function getByExternalId(string $externalId): ?Session
    {
        return $this->sessionRepository->findOneByExternalId($externalId);
    }

    /**
     * @param int $id
     * @return Session|null
     */
    public function getById(int $id): ?Session
    {
        return $this->sessionRepository->find($id);
    }

    /**
     * @param Organism $organism
     * @param array $parameters
     * @return QueryBuilder
     */
    public function listReturnQueryBuilder(Organism $organism, array $parameters): QueryBuilder
    {
        return $this->sessionRepository->findAllReturnQueryBuilder($organism, $parameters);
    }

    /**
     * @param Training $training
     * @param SessionStates $targetState
     * @return void
     */
    public function markAsFromTraining(Training $training, SessionStates $targetState)
    {
        $this->sessionRepository->markAsFromTraining($training, $targetState);
        $this->entityManager->flush();
    }

    /**
     * @param Training $training
     * @param array $externalIds
     * @return void
     */
    public function markAsDeletedFromTrainingExcludeTrainingActionExternalIds(Training $training, array $externalIds)
    {
        $this->sessionRepository->markAsDeletedFromTrainingExcludeTrainingActionExternalIds($training, $externalIds);
    }

    /**
     * @param ArrayCollection $trainingActionIds
     * @param SessionStates $targetState
     * @return ArrayCollection
     */
    public function markAsFromTrainingActionIds(ArrayCollection $trainingActionIds, SessionStates $targetState): ArrayCollection
    {
        return $this->sessionRepository->markAsFromTrainingActionIds($trainingActionIds, $targetState);
    }

    /**
     * @param Organism $organism
     * @return ArrayCollection
     */
    public function markAsArchivedAllExpired(Organism $organism): ArrayCollection
    {
        return $this->sessionRepository->markAsArchivedAllExpired($organism);
    }

    /**
     * @param array $trainingActionExternalIds
     * @return ArrayCollection
     */
    public function markAsDeletedFromTrainingActionIds(array $trainingActionExternalIds): ArrayCollection
    {
        return $this->sessionRepository->markAsDeletedFromTrainingActionExternalIds($trainingActionExternalIds);
    }

    //----------------
    // METHODES PRIVEES
    //----------------
}
