<?php
// src/Service/AttendeeService.php
namespace App\Service;

use App\Entity\Attendee;
use App\Entity\CertificationFolder;
use App\Entity\Organism;
use App\Entity\RegistrationFolder;
use App\Event\Attendee\AttendeeEvents;
use App\Exception\WedofBadRequestHttpException;
use App\Library\utils\enums\AttendeeGender;
use App\Library\utils\Tools;
use App\Repository\AttendeeRepository;
use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\QueryBuilder;
use Exception;
use Faker\Factory;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use ReflectionException;
use ReflectionProperty;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\File\File;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;
use Symfony\Component\Security\Http\LoginLink\LoginLinkHandlerInterface;

class AttendeeService implements LoggerAwareInterface
{

    private LoggerInterface $logger;
    private EventDispatcherInterface $dispatcher;
    private AttendeeRepository $attendeeRepository;
    private MailerService $mailer;
    private LoginLinkHandlerInterface $loginLinkHandler;

    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    public function __construct(EventDispatcherInterface $dispatcher,
                                AttendeeRepository $attendeeRepository,
                                LoginLinkHandlerInterface $loginLinkHandler,
                                MailerService $mailer)
    {
        $this->dispatcher = $dispatcher;
        $this->attendeeRepository = $attendeeRepository;
        $this->loginLinkHandler = $loginLinkHandler;
        $this->mailer = $mailer;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    /**
     * @param array $rawData
     * @param Attendee|null $attendee
     * @param bool $refreshAllProperties
     * @return Attendee
     * @throws Exception
     */
    public function createOrUpdate(array $rawData, Attendee $attendee = null, bool $refreshAllProperties = false): Attendee
    {
        $properties = array('phoneNumber', 'phoneFixed', 'address', 'degree', 'degreeTitle', 'dateOfBirth', 'nameCityOfBirth', 'codeCountryOfBirth', 'birthName', 'gender', 'employmentStatus', 'poleEmploiId', 'poleEmploiRegionCode', 'codeCityOfBirth', 'firstName2', 'firstName3', 'nameCountryOfBirth', 'identificationDocument', 'phoneNumberValidated', 'emailValidated', 'nir');
        if ($refreshAllProperties) {
            $properties = array_merge($properties, ['email', 'lastName', 'firstName', 'externalId']);
        }

        if (!empty($rawData['dateOfBirth'])) {
            $year = $rawData['dateOfBirth']->format('Y');
            if ($year < 1900 || $year > 2024) {
                throw new WedofBadRequestHttpException("Erreur, l'année de naissance doit être comprise entre 1900 et 2024");
            }
        }

        $attendee = $attendee ?? $this->getByRawData($rawData);
        $isNewAttendee = false;
        if ($attendee === null) {
            $this->logger->info("New attendee " . $rawData['email']);
            $attendee = new Attendee();
            $attendee->setFirstName(Tools::toTitleCase($rawData['firstName'] ?? ""));
            $attendee->setLastName(strtoupper($rawData['lastName'] ?? ""));
            $attendee->setEmail(strtolower($rawData['email']));
            $isNewAttendee = true;
        }

        //remove France => NOT NEEDED
        if (isset($rawData['codeCountryOfBirth']) && in_array($rawData['codeCountryOfBirth'], [99100, 100])) {
            $rawData['codeCountryOfBirth'] = null;
            $rawData['nameCountryOfBirth'] = null;
        }

        if (!$attendee->getGender() && !isset($rawData['gender'])) {
            if (isset($rawData['address']['corporateName'])) {
                $rawData['gender'] = preg_match("/^M /", $rawData['address']['corporateName']) ?
                    AttendeeGender::MALE()->getValue() : (preg_match("/^Mme /", $rawData['address']['corporateName']) ?
                        AttendeeGender::FEMALE()->getValue() : null);
            }
        }

        if (!$isNewAttendee) {
            $propertiesToHistorize = ['firstName', 'firstName2', 'firstName3', 'lastName', 'birthName', 'gender', 'dateOfBirth', 'nameCityOfBirth', 'codeCityOfBirth', 'nameCountryOfBirth', 'codeCountryOfBirth', 'phoneNumber', 'email', 'nir'];
            $oldValues = [];
            foreach ($propertiesToHistorize as $propertyToHistorize) {
                if (key_exists($propertyToHistorize, $rawData)) {
                    $getMethodName = 'get' . ucwords($propertyToHistorize);
                    $newValue = $rawData[$propertyToHistorize];
                    $oldValue = $attendee->{$getMethodName}();
                    if ($newValue != $oldValue) {
                        $oldValues[$propertyToHistorize] = $oldValue;
                    }
                }
            }
            $historyEntry = [
                'updatedOn' => (new DateTime())->format('Y-m-d\TH:i:s.u\Z'),
                'oldValues' => $oldValues
            ];
        }

        foreach ($properties as $property) {
            if ($property === "identificationDocument") {
                if (isset($rawData['identificationDocument']) && $rawData['identificationDocument'] instanceof File) {
                    $attendee->setIdentificationDocumentFile($rawData['identificationDocument']);
                } else if (array_key_exists('identificationDocument', $rawData) && $rawData['identificationDocument'] === null) {
                    $attendee->setIdentificationDocumentFile();
                    $attendee->setIdentificationDocumentName(null);
                    $attendee->setLastUpdate(new DateTime());
                }
            } else if (key_exists($property, $rawData)) {
                $setMethodName = "set" . ucwords($property);
                if (in_array($property, ['firstName2', 'firstName3', 'birthName']) && isset($rawData[$property])) {
                    $attendee->{$setMethodName}(Tools::toTitleCase($rawData[$property]));
                } else {
                    $attendee->{$setMethodName}($rawData[$property]);
                }
            }
        }

        if ($_ENV['APP_ENV'] == 'test' && $isNewAttendee) {
            $faker = Factory::create();
            $attendee->setFirstName($faker->firstName());
            $attendee->setLastName($faker->lastName());
            $attendee->setEmail($faker->email());
            $attendee->setPhoneNumber($faker->phoneNumber());
        }

        $rawData = array_merge($attendee->getRawData(), $rawData);
        if (isset($historyEntry)) {
            if (!key_exists('historyEntries', $rawData)) {
                $rawData['historyEntries'] = [];
            }
            $rawData['historyEntries'][] = $historyEntry;
        }
        $attendee->setRawData($rawData);
        $attendee->setLastUpdate(new DateTime());

        $this->save($attendee);
        $this->dispatcher->dispatch(new AttendeeEvents($attendee), $isNewAttendee ? AttendeeEvents::CREATED : AttendeeEvents::UPDATED);
        return $attendee;
    }


    /**
     * @param Attendee $attendee
     * @return Attendee
     */

    public function save(Attendee $attendee): Attendee
    {
        return $this->attendeeRepository->save($attendee);
    }

    /**
     * @param CertificationFolder|RegistrationFolder $folder
     * @param string|null $redirectUrl
     * @param string $entity
     * @return void
     * @throws ReflectionException
     * @throws TransportExceptionInterface
     */
    public function sendMagicLink($folder, string $entity, string $redirectUrl): void
    {
        $url = $this->generateMagicLink($folder->getAttendee()) . '&redirectURL=' . $redirectUrl;
        $this->mailer->sendAttendeeMagicLink($folder, $url, $entity);
    }

    /**
     * @throws ReflectionException
     */
    public function generateMagicLink(Attendee $attendee, $lifetime = null)
    {
        $reflection = new ReflectionProperty($this->loginLinkHandler, "options");
        $reflection->setAccessible(true);
        $options = $reflection->getValue($this->loginLinkHandler);
        if ($lifetime) {
            $reflection->setValue($this->loginLinkHandler, array_merge($options, ['lifetime' => $lifetime]));
        }

        $magicLink = $this->loginLinkHandler->createLoginLink($attendee)->getUrl();

        if ($lifetime) {
            $reflection->setValue($this->loginLinkHandler, $options);
        }
        //replace app/magic-link-auth => to auth/magic-link-auth to handle auth using angular
        return str_replace('app/', 'auth/', $magicLink);
    }

    /**
     * @param string $email
     * @return Attendee
     */
    public function getByEmail(string $email): ?Attendee
    {
        return $this->attendeeRepository->findOneByEmail($email);
    }

    /**
     * @param string $phoneNumber
     * @return Attendee|null
     */
    public function getByPhone(string $phoneNumber): ?Attendee
    {
        return $this->attendeeRepository->findOneByPhone($phoneNumber);
    }

    /**
     * @param array $parameters
     * @return ArrayCollection
     */
    public function listByEmailOrPhone(array $parameters): ArrayCollection
    {
        return $this->attendeeRepository->findByEmailOrPhone($parameters);
    }

    /**
     * @param int $id
     * @return Attendee
     */
    public function getById(int $id): ?Attendee
    {
        return $this->attendeeRepository->find($id);
    }

    /**
     * @param string $externalId
     * @return Attendee|null
     */
    public function getByExternalId(string $externalId): ?Attendee
    {
        return $this->attendeeRepository->findOneByExternalId($externalId);
    }

    /**
     * @param array $rawData
     * @return Attendee|null
     * @throws NonUniqueResultException
     * @throws Exception
     */
    public function getByRawData(array $rawData): ?Attendee
    {
        if (isset($rawData["isKairosAif"]) && $rawData["isKairosAif"] === true) {
            $poleEmploiId = $rawData['poleEmploiId'] ?? "";
            $attendee = $this->getByPoleEmploiId($poleEmploiId);

            if ($attendee !== null) {
                return $attendee;
            }

            /** @var Attendee[] $attendees */
            $attendees = $this->listByEmailOrPhone(["query" => $rawData["email"]]);

            if (sizeof($attendees) > 0) {
                $attendeeMatchingEntries = [];
                foreach ($attendees as $attendeeToMatch) {
                    $attendeeMatchingEntries[] = [
                        'score' => $this->isAttendeeMatchingPercentage($rawData, $attendeeToMatch),
                        'attendee' => $attendeeToMatch
                    ];
                }

                usort($attendeeMatchingEntries, function ($attendeeMatchingEntry1, $attendeeMatchingEntry2) {
                    return $attendeeMatchingEntry2['score'] <=> $attendeeMatchingEntry1['score'];
                });

                $MINIMUM_SCORE = 100;
                $bestMatches = array_filter($attendeeMatchingEntries, function ($attendeeMatchingEntry) use ($MINIMUM_SCORE) {
                    return $attendeeMatchingEntry['score'] >= $MINIMUM_SCORE;
                });

                if (sizeof($bestMatches) === 1) {
                    return $bestMatches[0]['attendee'];
                }

                if (sizeof($bestMatches) >= 1) {
                    usort($bestMatches, function ($attendeeMatchingEntry1, $attendeeMatchingEntry2) {
                        /** @var Attendee $attendee1 */
                        $attendee1 = $attendeeMatchingEntry1['attendee'];
                        /** @var Attendee $attendee2 */
                        $attendee2 = $attendeeMatchingEntry2['attendee'];
                        return $attendee2->getLastUpdate() <=> $attendee1->getLastUpdate();
                    });
                    return $bestMatches[0]['attendee'];
                }
            }
        }

        return $this->attendeeRepository->findOneByRawData($rawData);
    }

    /**
     * @param string $poleEmploiId
     * @return Attendee|null
     */
    public function getByPoleEmploiId(string $poleEmploiId): ?Attendee
    {
        return $this->attendeeRepository->findByOnePoleEmploiId($poleEmploiId);
    }

    /**
     * @param Organism $organism
     * @param array $parameters
     * @return QueryBuilder
     */
    public function listReturnQueryBuilder(Organism $organism, array $parameters): QueryBuilder
    {
        return $this->attendeeRepository->findAllReturnQueryBuilder($organism, $parameters);
    }

    /**
     * @param array $targetAttendee
     * @param Attendee $originalAttendee
     * @param int $nbAllowedDifferences
     * @param int $mimimumPercentMatching
     * @return bool
     * @throws Exception
     */
    public function isAttendeeMatching(array $targetAttendee, Attendee $originalAttendee, int $nbAllowedDifferences = 3, int $mimimumPercentMatching = 60): bool
    {
        $hasBigDifference = false;
        $nbDifferences = 0;
        $similarities = $this->getAttendeeMatchingSimilarities($targetAttendee, $originalAttendee);
        foreach ($similarities as $similarity) {
            if ($similarity < 100) {
                $nbDifferences++;
                if ($similarity < $mimimumPercentMatching) {
                    $hasBigDifference = true;
                }
            }
        }
        return !$hasBigDifference && $nbDifferences <= $nbAllowedDifferences;
    }

    /**
     * @param Attendee $attendee
     * @return bool
     */
    public function canUpdateAttendeeManually(Attendee $attendee): bool
    {
        // Get the pairs rather than a count because this allow customization
        // e.g. (8987, null) + (8987, 2132) are currently different but we may want to count them only as one
        $ownerPairs = $this->attendeeRepository->findAllDistinctOwnerPairs($attendee);
        return count($ownerPairs) <= 1;
    }

    /**
     * @param array $targetAttendee
     * @param Attendee $originalAttendee
     * @return float|int
     * @throws Exception
     */
    private function isAttendeeMatchingPercentage(array $targetAttendee, Attendee $originalAttendee)
    {
        $similarities = $this->getAttendeeMatchingSimilarities($targetAttendee, $originalAttendee);
        return (array_sum($similarities) / count($similarities));
    }

    /**
     * @param array $targetAttendee
     * @param Attendee $originalAttendee
     * @return array
     * @throws Exception
     */
    private function getAttendeeMatchingSimilarities(array $targetAttendee, Attendee $originalAttendee): array
    {
        $similarities = [];
        // For dates, 60% of 10 chars = 4 substitutions allowed (e.g. reverse month and day)
        // For other fields, it depends on length
        $properties = ['firstName', 'birthName', 'firstName2', 'firstName3', 'dateOfBirth'];
        foreach ($properties as $property) {
            if (isset($targetAttendee[$property])) {
                $targetAttendeeValue = $targetAttendee[$property];
                $originalAttendeeValue = $originalAttendee->{"get" . ucwords($property)}();
                if (!empty($targetAttendeeValue) && !empty($originalAttendeeValue)) {
                    if ($property == 'dateOfBirth') {
                        if (is_array($targetAttendeeValue)) {
                            $targetAttendeeValue = (new DateTime($targetAttendeeValue["date"]));
                        }
                        $targetAttendeeValue = $targetAttendeeValue->format('Y-m-d');
                        $originalAttendeeValue = $originalAttendeeValue->format('Y-m-d');
                    }
                    if (in_array($property, ['firstName', 'birthName', 'firstName2', 'firstName3'])) {
                        $possibleSimilarities = [];
                        $possibleSimilarities[] = Tools::computeStringSimilarity($targetAttendeeValue, $originalAttendee->getLastName());
                        $possibleSimilarities[] = Tools::computeStringSimilarity($targetAttendeeValue, $originalAttendee->getFirstName());
                        if (!empty($originalAttendee->getFirstName2())) {
                            $possibleSimilarities[] = Tools::computeStringSimilarity($targetAttendeeValue, $originalAttendee->getFirstName2());
                        }
                        if (!empty($originalAttendee->getFirstName3())) {
                            $possibleSimilarities[] = Tools::computeStringSimilarity($targetAttendeeValue, $originalAttendee->getFirstName3());
                        }
                        if (!empty($originalAttendee->getBirthName())) {
                            $possibleSimilarities[] = Tools::computeStringSimilarity($targetAttendeeValue, $originalAttendee->getBirthName());
                        }
                        $similarities[] = max($possibleSimilarities);
                    } else {
                        $similarities[] = Tools::computeStringSimilarity($targetAttendeeValue, $originalAttendeeValue);
                    }
                }
            }
        }
        return $similarities;
    }
}
