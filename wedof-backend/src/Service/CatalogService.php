<?php

namespace App\Service;

use App\Entity\Certification;
use App\Entity\Organism;
use App\Entity\Session;
use App\Entity\Training;
use App\Event\Organism\OrganismEvents;
use App\Exception\WedofConnectionException;
use App\Library\utils\enums\CertificationTypes;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\enums\SessionStates;
use App\Library\utils\enums\TrainingActionStates;
use App\Library\utils\enums\TrainingStates;
use App\Library\utils\Tools;
use App\Message\GenerateCpfCatalogXML;
use App\Message\SynchronizeTrainingActionAndSessions;
use App\Repository\OrganismRepository;
use App\Repository\TrainingRepository;
use App\Service\DataProviders\BaseApiService;
use App\Service\DataProviders\CpfApiService;
use App\Service\DataProviders\KairosAifApiService;
use App\Service\DataProviders\OpenDataApiService;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use ErrorException;
use Exception;
use Psr\EventDispatcher\EventDispatcherInterface;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Throwable;

class CatalogService implements LoggerAwareInterface
{
    private TrainingService $trainingService;
    private TrainingRepository $trainingRepository;
    private TrainingActionService $trainingActionService;
    private SessionService $sessionService;
    private LoggerInterface $logger;
    private EntityManagerInterface $entityManager;
    private MessageBusInterface $messageBus;
    private OpenDataApiService $openDataApiService;
    private CertificationService $certificationService;
    private OrganismService $organismService;
    private EventDispatcherInterface $dispatcher;
    private MailerService $mailerService;
    private OrganismRepository $organismRepository;

    public function __construct(TrainingService          $trainingService,
                                TrainingRepository       $trainingRepository,
                                TrainingActionService    $trainingActionService,
                                SessionService           $sessionService,
                                EntityManagerInterface   $entityManager,
                                OpenDataApiService       $openDataApiService,
                                CertificationService     $certificationService,
                                MessageBusInterface      $messageBus,
                                OrganismService          $organismService,
                                EventDispatcherInterface $dispatcher,
                                MailerService      $mailerService,
                                OrganismRepository $organismRepository
    )
    {
        $this->trainingService = $trainingService;
        $this->trainingRepository = $trainingRepository;
        $this->trainingActionService = $trainingActionService;
        $this->sessionService = $sessionService;
        $this->entityManager = $entityManager;
        $this->openDataApiService = $openDataApiService;
        $this->certificationService = $certificationService;
        $this->messageBus = $messageBus;
        $this->organismService = $organismService;
        $this->dispatcher = $dispatcher;
        $this->mailerService = $mailerService;
        $this->organismRepository = $organismRepository;
    }

    /**
     * @param LoggerInterface $logger
     */
    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    /**
     * @param DataProviders $dataProvider
     * @param Organism $organism
     * @param array $registrationFolderRawData
     * @return Session
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws ErrorException
     * @throws Throwable
     */
    public function createSessionFromRegistrationFolderRawData(DataProviders $dataProvider, Organism $organism, array $registrationFolderRawData): Session
    {
        $trainingAction = null;
        $apiService = BaseApiService::getCatalogApiServiceByDataProvider($dataProvider);
        $sessionSeemsDeleted = false;

        try {
            $training = $this->trainingService->getByExternalId($apiService->getTrainingExternalId($registrationFolderRawData['trainingId']), $dataProvider, ['createIfNotExist' => true, "organism" => $organism]);
        } catch (Exception $e) {
            $training = null;
            $sessionSeemsDeleted = true;
        }
        //fallback...
        if (!$training) {
            try {
                $training = $this->trainingService->getByExternalId($apiService->getTrainingExternalId($registrationFolderRawData['trainingId']), $dataProvider, ["organism" => $organism, 'createIfNotExist' => true, 'refresh' => true, 'noRefreshChildren' => true]);
            } catch (Exception $e) {
                $training = null;
                $sessionSeemsDeleted = true;
            }
            if (!$training) {
                $sessionSeemsDeleted = true;
                $registrationFolderRawData['trainingId'] = $registrationFolderRawData['trainingId'] . "_DEL_EDOF"; //keep id available
                $trainingRawData = $apiService->getTrainingRawDataFromRawData($registrationFolderRawData);
                $certification = $apiService->retrieveCertificationFromRawData($registrationFolderRawData);
                $training = $this->trainingService->createOrUpdate($trainingRawData, $organism, $certification);
                $registrationFolderRawData['trainingActionId'] = $registrationFolderRawData['trainingActionId'] . "_DEL_EDOF"; //keep id available
                $trainingActionRawData = $apiService->getTrainingActionRawDataFromRawData($registrationFolderRawData);
                $trainingAction = $this->trainingActionService->createOrUpdate($trainingActionRawData, $training);
            }
        }
        if (!$trainingAction) {
            try {
                $trainingAction = $this->trainingActionService->getByExternalId($apiService->getTrainingActionExternalId($registrationFolderRawData['trainingId'], $registrationFolderRawData['trainingActionId']), $dataProvider, ['createIfNotExist' => true, "organism" => $organism]);
            } catch (Exception $e) {
                $sessionSeemsDeleted = true;
            }
            if (!$trainingAction) {
                $sessionSeemsDeleted = true;
                $registrationFolderRawData['trainingActionId'] = $registrationFolderRawData['trainingActionId'] . "_DEL_EDOF"; //keep id available
                $trainingActionRawData = $apiService->getTrainingActionRawDataFromRawData($registrationFolderRawData);
                $trainingAction = $this->trainingActionService->createOrUpdate($trainingActionRawData, $training);
            }
        }
        $session = $this->sessionService->getByExternalId($apiService->getSessionExternalId($registrationFolderRawData['trainingId'], $registrationFolderRawData['trainingActionId'], $registrationFolderRawData['trainingActionInfo']['sessionId']));
        if (!$session) {
            if (!$sessionSeemsDeleted) {
                try {
                    $sessionRawData = $apiService->getSessionRawData($trainingAction, $registrationFolderRawData['trainingActionInfo']['sessionId']);
                } catch (Exception $e) {
                    $sessionRawData = null;
                }
            } else {
                $sessionRawData = null;
            }
            if (!$sessionRawData || !isset($sessionRawData['id'])) {
                //fallback...
                //data doesn't exist on Cpf anymore try to get minimum of data...
                $registrationFolderRawData['trainingActionInfo']['sessionId'] = $registrationFolderRawData['trainingActionInfo']['sessionId'] . "_DELETED"; //keep id available
                $sessionRawData = $apiService->getSessionRawDataFromRawData($registrationFolderRawData);
            }
            $session = $this->sessionService->createOrUpdate($sessionRawData, $trainingAction);
        }

        return $session;
    }

    /**
     * @param Organism $organism
     * @param array $certificationCodes
     * @return void
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function synchronizeCpfWithOpenData(Organism $organism, array $certificationCodes): void
    {
        $key = 'externalId';
        $dataProvider = DataProviders::CPF();
        $certifications = $this->listCertificationsFromCodes($certificationCodes);

        $openDataPublishedTrainings = $this->openDataApiService->getTrainingsRawData([
            'siret' => $organism->getSiret(),
            'certifInfos' => array_map(function (Certification $certification) {
                return $certification->getCertifInfo();
            }, $certifications)
        ]);

        $wedofPublishedFCTrainings = $this->trainingService->listPublished($organism, $certifications, ['dataProvider' => DataProviders::CPF(), 'certificationTypes' => CertificationTypes::franceCompetencesTypeValues()])->toArray();
        // on met à jour systématiquement à jour les formations sur des certifications CPF => non présente sur l'openData
        $wedofPublishedCPFTrainings = array_column($this->trainingService->listPublished($organism, $certifications, ['dataProvider' => DataProviders::CPF(), 'certificationTypes' => [CertificationTypes::CPF()->getValue()]])->toArray(), $key);

        $trainingsToUpdate = [];
        $addedTrainingIds = array_column(Tools::arrayDiffAssocKey($openDataPublishedTrainings, $wedofPublishedFCTrainings, $key), $key);
        $removedTrainingIds = array_column(Tools::arrayDiffAssocKey($wedofPublishedFCTrainings, $openDataPublishedTrainings, $key), $key);

        $commonTrainingIds = array_column(Tools::arrayIntersectAssocKey($openDataPublishedTrainings, $wedofPublishedFCTrainings, $key), $key);
        foreach ($commonTrainingIds as $externalId) {
            $openData = array_values(array_filter($openDataPublishedTrainings, fn(array $arr) => $arr[$key] === $externalId))[0];
            $wedof = array_values(array_filter($wedofPublishedFCTrainings, fn(array $arr) => $arr[$key] === $externalId))[0];

            if ($openData['nbTrainingActions'] != $wedof['nbTrainingActions'] || $openData['nbSessions'] != $wedof['nbSessions']) {
                $trainingsToUpdate[] = $externalId;
            }
        }

        $this->logger->debug("[SynchronizeCatalog][" . $organism->getName() . "][" . $organism->getSiret() . "]" . count($addedTrainingIds) . " formations ajoutées : " . implode(',', $addedTrainingIds));
        $this->logger->debug("[SynchronizeCatalog][" . $organism->getName() . "][" . $organism->getSiret() . "]" . count($trainingsToUpdate) . " formations RS/RNCP à mettre à jour : " . implode(',', $trainingsToUpdate));
        $this->logger->debug("[SynchronizeCatalog][" . $organism->getName() . "][" . $organism->getSiret() . "]" . count($wedofPublishedCPFTrainings) . " formations hors RS/RNCP à mettre à jour : " . implode(',', $wedofPublishedCPFTrainings));
        $this->logger->debug("[SynchronizeCatalog][" . $organism->getName() . "][" . $organism->getSiret() . "]" . count($removedTrainingIds) . " formations dépubliées : " . implode(',', $removedTrainingIds));

        /** @var string $trainingExternalId */
        foreach (array_merge($addedTrainingIds, $trainingsToUpdate, $removedTrainingIds, $wedofPublishedCPFTrainings) as $trainingExternalId) {
            $apiService = BaseApiService::getCatalogApiServiceByDataProvider($dataProvider);
            $training = $this->trainingService->getByExternalId($trainingExternalId, $dataProvider, ['createIfNotExist' => true, 'refresh' => true, 'noRefreshChildren' => true, 'daysMaxSinceLastUpdate' => 1, 'statusLabel' => 'ACTIVE']);
            $trainingActionsRawData = $apiService->getTrainingActionsRawData($training);
            foreach ($trainingActionsRawData as $trainingActionRawData) {
                try {
                    $trainingActionExternalId = $apiService->getTrainingActionExternalId($trainingActionRawData['trainingId'], $trainingActionRawData['id']);
                    $this->messageBus->dispatch(new SynchronizeTrainingActionAndSessions($training->getId(), $trainingActionExternalId, $dataProvider, $trainingActionRawData));
                } catch (Throwable $exception) {
                    $this->logger->error('[SynchronizeCatalog] error : ' . $exception->getMessage());
                }
            }
            $this->markAsDeletedNotUpdatedTrainingActionsAndChildren($training, $trainingActionsRawData);
            $this->entityManager->clear();
        }
        $this->markAsNotUpdatedTrainingsAndChildren($removedTrainingIds, $organism);
    }

    /**
     * @param Organism $organism
     * @param DataProviders $dataProvider
     * @param array|null $certificationCodes
     * @param array|null $trainingStatuses
     * @param array $options
     * @return void
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     * @throws \Symfony\Component\Mailer\Exception\TransportExceptionInterface
     */
    public function synchronizeForDataProvider(Organism $organism, DataProviders $dataProvider, ?array $certificationCodes, ?array $trainingStatuses, array $options = []): void
    {
        /** @var CpfApiService|KairosAifApiService $apiService */
        $apiService = BaseApiService::getApiServiceByDataProvider($dataProvider);
        $trainingsRawData = $apiService->getTrainingsRawData($organism, $trainingStatuses);
        $fullRefresh = true;
        if (!empty($certificationCodes)) {
            $trainingsRawData = array_filter($trainingsRawData, function ($trainingRawData) use ($certificationCodes) {
                return in_array($trainingRawData['codeReconnaissance'], $certificationCodes);
            });
            $fullRefresh = false;
        }
        if (empty($trainingsRawData)) { // Eviter de marquer comme supprimé tout le catalogue d'un OF parce qu'EDOF a un soucis.
            return;
        }
        if ($fullRefresh) {
            $nbActionsTotal = 0;
            foreach ($trainingsRawData as $trainingRawData) {
                $nbActionsTotal += $trainingRawData['actionCount'];
            }
            $cpfCatalogMetadata = $organism->getCpfCatalogMetadata();
            $cpfCatalogMetadata['synchronize'] = [
                'startDate' => (new DateTime('now'))->format('Y-m-d\TH:i:s.u\Z'),
                'state' => 'inProgress',
                'nbActionsDone' => 0,
                'nbActionsTotal' => $nbActionsTotal
            ];
            $organism->setCpfCatalogMetadata($cpfCatalogMetadata);
            $this->organismService->save($organism);
        }
        foreach ($trainingsRawData as $trainingRawData) {
            try {
                $trainingExternalId = $trainingRawData['id'];
                $trainingBeforeUpdate = $this->trainingRepository->findOneBy(array('externalId' => $trainingExternalId));
                $trainingStateBeforeUpdate = $trainingBeforeUpdate ? $trainingBeforeUpdate->getState() : null;
                $training = $this->trainingService->getByExternalId($trainingExternalId, $dataProvider, array_merge(['createIfNotExist' => true, 'refresh' => true, 'noRefreshChildren' => true, 'daysMaxSinceLastUpdate' => 1, 'statusLabel' => $trainingRawData['statusLabel']], $options));
                // If archived before and after, don't sync children
                if ($training->getState() !== TrainingStates::DELETED()->getValue() && ($trainingStateBeforeUpdate !== TrainingStates::ARCHIVED()->getValue() || $training->getState() !== TrainingStates::ARCHIVED()->getValue())) {
                    try {
                        $externalIds = [];
                        $trainingActionsRawData = $apiService->getTrainingActionsRawData($training);
                        foreach ($trainingActionsRawData as $trainingActionRawData) {
                            $externalId = $apiService->getTrainingActionExternalId($trainingActionRawData['trainingId'], $trainingActionRawData['id']);
                            $this->messageBus->dispatch(new SynchronizeTrainingActionAndSessions($training->getId(), $externalId, $dataProvider, $trainingActionRawData, $options, $fullRefresh));
                            $externalIds[] = $externalId;
                        }
                        $this->trainingActionService->markAsDeletedFromTrainingExcludeTrainingActionExternalIds($training, $externalIds);
                        $this->markAsDeletedNotUpdatedTrainingActionsAndChildren($training, $trainingActionsRawData);
                    } catch (Throwable $e) {
                        $this->logger->error('[SynchronizeCatalog] Error sync actions: ' . $organism->getSiret() . ' ' . $e->getMessage());
                        if ($fullRefresh) {
                            throw $e; // We don't want partial refresh
                        }
                    }
                } else if ($fullRefresh) {
                    $organism = $this->updateCpfCatalogSynchronizeProgress($training->getOrganism(), $trainingRawData['actionCount']); // Count as done so total is updated correctly (be careful, $training->getOrganism() is on purpose to get fresh entity)
                }
                $this->entityManager->clear();
            } catch (Throwable $e) {
                if ($fullRefresh) {
                    $organism = $this->manageCpfCatalogSynchronizeError($organism, $e);
                }
                $this->logger->error('[SynchronizeCatalog] Error sync trainings: ' . $organism->getSiret() . ' ' . $e->getMessage());
            }
        }
        $certifications = $this->listCertificationsFromCodes($certificationCodes);
        $wedofPublishedCPFTrainings = $this->trainingService->listPublished($organism, $certifications, ['dataProvider' => $dataProvider])->toArray();
        $nonUpdatedTrainingExternalIds = array_column(Tools::arrayDiffAssocKey($wedofPublishedCPFTrainings, $trainingsRawData, 'externalId'), 'externalId');
        $this->markAsDeletedNotUpdatedTrainingsAndChildren($nonUpdatedTrainingExternalIds);
        $this->logger->debug(count($trainingsRawData) . " formations for statuses = " . join(',', ($trainingStatuses ?? [])));
    }

    /**
     * @param Organism $organism
     * @param int $newActionsDone
     * @return void
     * @throws \Doctrine\DBAL\Exception
     */
    public function updateCpfCatalogSynchronizeProgress(Organism $organism, int $newActionsDone): Organism
    {
        $organism = $this->organismRepository->addToCpfCatalogSynchronizeNbActionsCount($organism, $newActionsDone);
        $cpfCatalogMetadata = $organism->getCpfCatalogMetadata();
        // TODO(CatalogXML): there is a race condition here when called from a worker, as several can read $cpfCatalogMetadata['synchronize']['state'] === 'done' at the same time
        if ($cpfCatalogMetadata['synchronize']['state'] === 'done') {
            $cpfCatalogMetadata['synchronize']['endDate'] = (new DateTime('now'))->format('Y-m-d\TH:i:s.u\Z');
            if (isset($cpfCatalogMetadata['export']['state']) && $cpfCatalogMetadata['export']['state'] === 'pendingSynchronize') {
                $cpfCatalogMetadata['export']['state'] = 'inProgress';
                $organism->setCpfCatalogMetadata($cpfCatalogMetadata);
                $this->organismService->save($organism);
                $this->logger->debug('[CatalogXML] Synchronize is done, dispatch GenerateCpfCatalogXML' . $organism->getSiret());
                $this->messageBus->dispatch(new GenerateCpfCatalogXML($organism->getOwnedBy()->getId()));
            } else {
                $organism->setCpfCatalogMetadata($cpfCatalogMetadata);
                $this->organismService->save($organism);
            }
        }
        return $organism;
    }

    /**
     * @param Organism $organism
     * @return void
     */
    public function processCpfCatalogUploadReport(Organism $organism)
    {
        try {
            /** @var CpfApiService $cpfApiService */
            $cpfApiService = BaseApiService::getApiServiceByDataProvider(DataProviders::CPF());
            if (!$cpfApiService->isCatalogUploadInProgress($organism)) { // Detect that the process is over
                $reportData = $cpfApiService->getLastestCatalogUploadReport($organism);
                if (!$reportData) {
                    throw new Exception('Error, no matching report found for organism ' . $organism->getSiret());
                }
                $reportFileName = $reportData['fileName'];
                $tmpReportPath = sys_get_temp_dir() . '/' . $reportFileName;
                file_put_contents($tmpReportPath, $reportData['content']);
                $reportFile = new UploadedFile($tmpReportPath, $reportFileName, 'text/csv', null, true);
                $organism->setCpfCatalogUploadReport($reportFile);
                $cpfCatalogMetadata = $organism->getCpfCatalogMetadata();
                $cpfCatalogMetadata['upload']['endDate'] = (new DateTime('now'))->format('Y-m-d\TH:i:s.u\Z');
                $cpfCatalogMetadata['upload']['reportFileName'] = $reportFileName;
                $cpfCatalogMetadata['upload']['state'] = 'done';
                $organism->setCpfCatalogMetadata($cpfCatalogMetadata);
                $this->organismService->save($organism);
                $this->dispatcher->dispatch(new OrganismEvents($organism), OrganismEvents::CPF_CATALOG_UPLOAD_FINISHED);
                $this->mailerService->sendCpfCatalogUploadReport($organism->getOwnedBy(), $reportFile->getRealPath(), $reportFileName);
            }
        } catch (Throwable $t) {
            $this->logger->error('[CatalogXML] Error processCpfCatalogUploadReport for organism ' . $organism->getSiret());
            $this->logger->error($t->getMessage());
        }
    }

    /**
     * @param Organism $organism
     * @param Throwable $t
     * @return void
     * @throws \Symfony\Component\Mailer\Exception\TransportExceptionInterface
     */
    public function manageCpfCatalogSynchronizeError(Organism $organism, Throwable $t): Organism
    {
        $cpfCatalogMetadata = $organism->getCpfCatalogMetadata();
        $cpfCatalogMetadata['synchronize']['state'] = 'failed';
        $cpfCatalogMetadata['synchronize']['endDate'] = (new DateTime('now'))->format('Y-m-d\TH:i:s.u\Z');
        $organism->setCpfCatalogMetadata($cpfCatalogMetadata);
        $organism = $this->organismService->save($organism);
        if (isset($cpfCatalogMetadata['export']['state']) && $cpfCatalogMetadata['export']['state'] === 'pendingSynchronize') {
            $organism = $this->manageCpfCatalogExportError($organism, $t);
        }
        return $organism;
    }

    /**
     * @param Organism $organism
     * @param Throwable $t
     * @return void
     * @throws \Symfony\Component\Mailer\Exception\TransportExceptionInterface
     */
    public function manageCpfCatalogExportError(Organism $organism, Throwable $t): Organism
    {
        $cpfCatalogMetadata = $organism->getCpfCatalogMetadata();
        $cpfCatalogMetadata['export']['state'] = 'failed';
        $cpfCatalogMetadata['export']['endDate'] = (new DateTime('now'))->format('Y-m-d\TH:i:s.u\Z');
        $organism->setCpfCatalogMetadata($cpfCatalogMetadata);
        $organism = $this->organismService->save($organism);
        $user = $organism->getOwnedBy();
        $this->mailerService->sendCpfCatalogXMLErrorToWedof($user, $t->getMessage());
        $this->mailerService->sendCpfCatalogXMLErrorToCustomer($user);
        return $organism;
    }

    //----------------
    // METHODES PRIVES
    //----------------

    /**
     * @param array $externalIds
     * @param Organism $organism
     * @return void
     */
    private function markAsNotUpdatedTrainingsAndChildren(array $externalIds, Organism $organism): void
    {
        $trainingIdsMarkAsUnpublished = $this->trainingService->markAsUnpublishedNotUpdated($externalIds);
        $this->logger->debug("Training Ids marked as unpublished : " . implode(", ", $trainingIdsMarkAsUnpublished->toArray()));
        $trainingActionIdsMarkAsUnpublished = $this->trainingActionService->markAsFromTrainingIds($trainingIdsMarkAsUnpublished, TrainingActionStates::UNPUBLISHED());
        $this->logger->debug("Training Action Ids marked as unpublished : " . implode(", ", $trainingActionIdsMarkAsUnpublished->toArray()));
        $sessionIdsMarkAsUnpublished = $this->sessionService->markAsFromTrainingActionIds($trainingActionIdsMarkAsUnpublished, SessionStates::UNPUBLISHED());
        $this->logger->debug("Session Ids marked as unpublished : " . implode(", ", $sessionIdsMarkAsUnpublished->toArray()));

        $this->markAsDeletedNotUpdatedTrainingsAndChildren($externalIds);

        $sessionExpired = $this->sessionService->markAsArchivedAllExpired($organism);
        $this->logger->debug("Expired session Ids marked as archived : " . implode(", ", $sessionExpired->toArray()));
    }

    /**
     * @param array $externalIds
     * @return void
     */
    private function markAsDeletedNotUpdatedTrainingsAndChildren(array $externalIds)
    {
        $trainingIdsMarkedAsDeleted = $this->trainingService->markAsDeletedNotUpdated($externalIds);
        $this->logger->debug("Training Ids marked as deleted : " . implode(", ", $trainingIdsMarkedAsDeleted->toArray()));
        $trainingActionIdsMarkAsDeleted = $this->trainingActionService->markAsFromTrainingIds($trainingIdsMarkedAsDeleted, TrainingActionStates::DELETED());
        $this->logger->debug("Training Action Ids marked as deleted : " . implode(", ", $trainingActionIdsMarkAsDeleted->toArray()));
        $sessionIdsMarkAsDeleted = $this->sessionService->markAsFromTrainingActionIds($trainingActionIdsMarkAsDeleted, SessionStates::DELETED());
        $this->logger->debug("Session Ids marked as deleted : " . implode(", ", $sessionIdsMarkAsDeleted->toArray()));
    }

    /**
     * @param Training $training
     * @param array $trainingActionsRawData
     * @return void
     */
    private function markAsDeletedNotUpdatedTrainingActionsAndChildren(Training $training, array $trainingActionsRawData): void
    {
        if (!empty($trainingActionsRawData)) { // check pour éviter de marquer comme supprimé des actions parce qu'EDOF a un soucis.
            $wedofTrainingActionExternalIds = $this->trainingActionService->listPublishedFromTraining($training)->toArray();
            $nonUpdatedTrainingActionExternalIds = array_column(Tools::arrayDiffAssocKey($wedofTrainingActionExternalIds, $trainingActionsRawData, 'externalId'), 'externalId');
            $this->trainingActionService->markAsDeletedFromTrainingActionExternalIds($nonUpdatedTrainingActionExternalIds);
            $this->logger->debug("TrainingAction Ids marked as deleted : " . implode(", ", $nonUpdatedTrainingActionExternalIds));
            $sessionDeletedIds = $this->sessionService->markAsDeletedFromTrainingActionIds($nonUpdatedTrainingActionExternalIds);
            $this->logger->debug("Session Ids marked as deleted : " . implode(", ", $sessionDeletedIds->toArray()));
        }
    }

    /**
     * @param array|null $certificationCodes
     * @return array
     */
    private function listCertificationsFromCodes(?array $certificationCodes): array
    {
        return array_filter(array_map(function ($code) {
            if (str_starts_with($code, CertificationTypes::RS()->getValue())) {
                $certification = $this->certificationService->getByTypeAndCode(CertificationTypes::RS(), str_replace(CertificationTypes::RS()->getValue(), '', $code));
            } else {
                $certification = $this->certificationService->getByTypeAndCode(CertificationTypes::RNCP(), str_replace(CertificationTypes::RNCP()->getValue(), '', $code));
            }
            return $certification;
        }, $certificationCodes), fn($item) => $item !== null);
    }
}