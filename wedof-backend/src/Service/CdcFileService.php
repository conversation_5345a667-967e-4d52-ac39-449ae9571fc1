<?php

// src/Service/CdcFileService.php
namespace App\Service;


use App\Entity\CdcFile;
use App\Entity\Certification;
use App\Entity\CertificationFolder;
use App\Entity\Organism;
use App\Entity\User;
use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofConnectionException;
use App\Exception\WedofCpfBackendException;
use App\Exception\WedofSubscriptionException;
use App\Library\utils\enums\AttendeeGender;
use App\Library\utils\enums\CdcFileStates;
use App\Library\utils\enums\CertificationFolderAccessModality;
use App\Library\utils\enums\CertificationFolderCdcStates;
use App\Library\utils\enums\CertificationFoldersCdcFilesStates;
use App\Library\utils\enums\CertificationFolderStates;
use App\Library\utils\enums\CertificationTypes;
use App\Library\utils\enums\ConnectionTypes;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\enums\OrganismApplicantType;
use App\Library\utils\enums\SubscriptionCertifierTypes;
use App\Library\utils\Tools;
use App\Message\GenerateCdcXML;
use App\Message\ReadReceiptXmlForCdc;
use App\Repository\CdcFileRepository;
use App\Repository\CertificationFolderRepository;
use App\Service\DataProviders\CdcCertifiersApiService;
use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\QueryBuilder;
use ErrorException;
use Exception;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Messenger\Stamp\DelayStamp;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Throwable;

class CdcFileService
{
    public const DELAY = 180000;
    private MailerService $mailerService;
    private CdcFileRepository $cdcFileRepository;
    private EntityManagerInterface $entityManager;
    private MessageBusInterface $messageBus;
    private CertificationService $certificationService;
    private CertificationFolderService $certificationFolderService;
    private CertificationFolderRepository $certificationFolderRepository;
    private CertificationFoldersCdcFilesService $certificationFoldersCdcFilesService;
    private CdcCertifiersApiService $cdcCertifiersApiService;

    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    public function __construct(MailerService                       $mailerService,
                                CdcFileRepository                   $cdcFileRepository,
                                CertificationFolderRepository       $certificationFolderRepository,
                                CertificationFolderService          $certificationFolderService,
                                MessageBusInterface                 $messageBus,
                                CertificationService                $certificationService,
                                EntityManagerInterface              $entityManager,
                                CdcCertifiersApiService             $cdcCertifiersApiService,
                                CertificationFoldersCdcFilesService $certificationFoldersCdcFilesService
    )
    {
        $this->mailerService = $mailerService;
        $this->cdcFileRepository = $cdcFileRepository;
        $this->certificationFolderRepository = $certificationFolderRepository;
        $this->certificationFolderService = $certificationFolderService;
        $this->cdcCertifiersApiService = $cdcCertifiersApiService;
        $this->certificationFoldersCdcFilesService = $certificationFoldersCdcFilesService;
        $this->entityManager = $entityManager;
        $this->messageBus = $messageBus;
        $this->certificationService = $certificationService;
    }

    /**
     * @param string $fileNamePath
     * @param array $certificationFoldersIdentifier
     * @param Organism $organism
     * @param string $idFlux
     * @param bool $generatedAutomatically
     * @param bool $generatedExternally
     * @param DateTime|null $createdOn
     * @return CdcFile
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function create(string $fileNamePath, array $certificationFoldersIdentifier, Organism $organism, string $idFlux, bool $generatedAutomatically, bool $generatedExternally = false, DateTime $createdOn = null): CdcFile
    {
        $cdcFile = new CdcFile();
        $cdcFile->setName($fileNamePath);
        $cdcFile->setState(CdcFileStates::EXPORTED());
        $cdcFile->setStateLastUpdate($createdOn ?? new DateTime());
        $cdcFile->setOrganism($organism);
        $cdcFile->setIdFlux($idFlux);
        $cdcFile->setGeneratedAutomatically($generatedAutomatically);
        $cdcFile->setGeneratedExternally($generatedExternally);
        $this->save($cdcFile);
        if ($createdOn) {
            // TimestampableTrait set value to new DateTime() for createdOn with updateCreatedOnAndUpdatedOnTimestamps().
            // Need to do 2 setCreatedOn & save to override the $createdOn value
            $cdcFile->setCreatedOn($createdOn);
            $this->save($cdcFile);
        }
        if ($certificationFoldersIdentifier) {
            foreach ($certificationFoldersIdentifier as $certificationFolderIdentifier) {
                /** @var CertificationFolder $certificationFolder */
                $certificationFolder = $this->certificationFolderRepository->findOneBy(['id' => $certificationFolderIdentifier]);
                if (!$certificationFolder) {
                    $certificationFolder = $this->certificationFolderRepository->findOneBy(['cdcTechnicalId' => $certificationFolderIdentifier]);
                }
                $this->certificationFoldersCdcFilesService->create($cdcFile, $certificationFolder);
            }
        }
        $this->entityManager->flush();
        return $cdcFile;
    }

    /**
     * @param CdcFile $cdcFile
     * @param array $data
     * @return CdcFile
     * @throws Exception
     */
    public function update(CdcFile $cdcFile, array $data): CdcFile
    {
        $previousState = $cdcFile->getState();
        $properties = array('state', 'idTraitement', 'submissionDate');
        foreach ($properties as $property) {
            if (key_exists($property, $data)) {
                $setMethodName = "set" . ucwords($property);
                $cdcFile->{$setMethodName}($data[$property]);
            }
        }
        if ($cdcFile->getState() !== $previousState) {
            $cdcFile->setStateLastUpdate(new DateTime());
        }
        $this->save($cdcFile);
        return $cdcFile;
    }

    /**
     * @param CdcFile $cdcFile
     * @return void
     */
    public function delete(CdcFile $cdcFile): void
    {
        $this->cdcFileRepository->delete($cdcFile);
    }

    /**
     * @param CdcFile $cdcFile
     * @return CdcFile
     * @throws Exception
     * @throws Throwable
     */
    public function abort(CdcFile $cdcFile): CdcFile
    {
        foreach ($cdcFile->getCertificationFoldersCdcFiles() as $certificationFolderCdcFile) {
            $this->certificationFoldersCdcFilesService->abort($certificationFolderCdcFile);
        }
        return $this->update($cdcFile, ['state' => CdcFileStates::ABORTED()->getValue()]);
    }

    /**
     * @param Organism $organism
     * @param string|null $certifInfo
     * @param bool $isAutomatedGeneration
     * @return array
     * @throws WedofSubscriptionException
     */
    public function generateCdcData(Organism $organism, bool $isAutomatedGeneration, string $certifInfo = null): array
    {
        $subscription = $organism->getSubscription();
        if (!$subscription->isAllowCertifiers()) {
            throw new WedofSubscriptionException("Erreur, cette fonctionnalité est limitée aux abonnements Certificateur");
        }
        $NATURE_DEPOSANT_CERTIFICATEUR = $isAutomatedGeneration && $this->isWedofDeposantForOrganism($organism) ? OrganismApplicantType::TIERS_CONFIANCE()->getValue() : $organism->getApplicantType();
        $certificationParameters = [
            'organismType' => 'certifier',
            'allowGenerateXmlAutomatically' => $isAutomatedGeneration,
            'certifInfo' => $certifInfo ?? null,
            'certificationTypes' => [CertificationTypes::RS()->getValue(), CertificationTypes::RNCP()->getValue()]
        ];
        $certifications = $this->certificationService->listReturnQueryBuilder($organism, $certificationParameters)->getQuery()->getResult();
        $cdcData = [];
        foreach ($certifications as $certification) {
            /* @var $certification Certification */
            if ($certification->isCdcExportable() && (!$certifInfo || $certification->getCertifInfo() === $certifInfo)) {
                $certificationData = [
                    'type' => $certification->getType(),
                    'code' => $certification->getExternalId(),
                    'natureDeposant' => $NATURE_DEPOSANT_CERTIFICATEUR
                ];
                $certificationStats = [
                    'externalId' => $certification->getExternalId(),
                    'certifInfo' => $certification->getCertifInfo(),
                    'nbFoldersProcessed' => 0,
                    'nbFoldersNotProcessed' => 0,
                    'nbFoldersCPF' => 0,
                    'nbFoldersNotCPF' => 0,
                    'foldersExported' => [],
                    'foldersNotExported' => []
                ];
                $certificationFolderParameters = [
                    'state' => [CertificationFolderStates::SUCCESS()->getValue()],
                    'certifications' => [$certification->getCertifInfo()],
                    'cdcToExport' => true,
                    'cdcState' => $isAutomatedGeneration ? [CertificationFolderCdcStates::NOT_EXPORTED()->getValue(), CertificationFolderCdcStates::PROCESSED_KO()->getValue()] : [CertificationFolderCdcStates::NOT_EXPORTED()->getValue(), CertificationFolderCdcStates::EXPORTED()->getValue(), CertificationFolderCdcStates::PROCESSED_KO()->getValue()],
                    'cdcExcluded' => false,
                    'filterOnStateDate' => 'stateLastUpdate',
                    'sort' => 'successDate',
                    'order' => 'desc',
                    'certifier' => $organism
                ];
                $query = $this->certificationFolderService->listReturnQueryBuilder($organism, $certificationFolderParameters)->getQuery();
                if ($subscription->getCertifierType() === SubscriptionCertifierTypes::TRIAL()->getValue()) {
                    $certificationFolders = $query->setMaxResults(2)->getResult();
                } else {
                    $certificationFolders = $query->getResult();
                }
                $certificationFoldersData = [];
                foreach ($certificationFolders as $certificationFolder) {
                    /* @var $certificationFolder CertificationFolder */
                    if ($certificationFolder->isCdcCompliant()) {
                        $certificationStats['foldersExported'][] = $certificationFolder->getId();
                        $certificationStats['nbFoldersProcessed']++;
                        $certificationFolderData = [];
                        $registrationFolder = $certificationFolder->getRegistrationFolder();
                        $attendee = $certificationFolder->getAttendee();
                        // For renewable certifications, the same user can get the certification serveral times
                        $certificationFolderData['idTechnique'] = $certificationFolder->getCdcTechnicalId() ?: $certificationFolder->getId();
                        $certificationFolderData['urlPreuve'] = $certificationFolder->getDigitalProofLink();
                        $certificationFolderData['libelleOption'] = $certificationFolder->getOptionName();
                        $certificationFolderData['obtentionCertification'] = $certification->getObtentionSystem();
                        $certificationFolderData['donneeCertifiee'] = $certificationFolder->isCertifiedData() ? "true" : "false";
                        $certificationFolderData['dateDebutExamen'] = $certificationFolder->getExaminationDate() ? $certificationFolder->getExaminationDate()->format('Y-m-d') : null;
                        $certificationFolderData['dateFinExamen'] = $certificationFolder->getExaminationEndDate() != null ? $certificationFolder->getExaminationEndDate()->format('Y-m-d') : $certificationFolderData['dateDebutExamen'];
                        $certificationFolderData['modalitePassageExamen'] = $certificationFolder->getExaminationType();
                        $certificationFolderData['codePostalCentreExamen'] = $certificationFolder->getExaminationCenterZipCode();
                        $certificationFolderData['dateDebutValidite'] = $certificationFolder->getIssueDate()->format('Y-m-d');
                        $certificationFolderData['dateFinValidite'] = $certificationFolder->getExpirationDate() != null ? $certificationFolder->getExpirationDate()->format('Y-m-d') :
                            ($certification->getValidityPeriod() ? date('Y-m-d', strtotime($certificationFolder->getIssueDate()->format('Y-m-d') . ' + ' . $certification->getValidityPeriod() . ' years')) : null);
                        $certificationFolderData['presenceNiveauLangueEuro'] = $certificationFolder->getEuropeanLanguageLevel() ? "true" : "false";
                        $certificationFolderData['niveauLangueEuropeen'] = $certificationFolder->getEuropeanLanguageLevel();
                        $certificationFolderData['presenceNiveauNumeriqueEuro'] = "false";
                        $certificationFolderData['scoring'] = $certificationFolder->getDetailedResult();
                        $certificationFolderData['mentionValidee'] = $certificationFolder->getGradePass();
                        $certificationFolderData['verbatim'] = $certificationFolder->getVerbatim();
                        $modalitesInscription = [
                            'modaliteAcces' => $certificationFolder->getAccessModality(),
                            'voieAccessVAE' => $certificationFolder->getAccessModality() === CertificationFolderAccessModality::VAE()->getValue() ? $certificationFolder->getAccessModalityVae() : null,
                            'initiativeInscription' => $certificationFolder->getType(),
                            'dateInscription' => $certificationFolder->getEnrollmentDate() ? $certificationFolder->getEnrollmentDate()->format('Y-m-d') : null
                        ];
                        $certificationFolderData['modalitesInscription'] = $modalitesInscription;
                        if ($registrationFolder && $registrationFolder->getType() === DataProviders::CPF()->getValue()) {
                            $certificationStats['nbFoldersCPF']++;
                            $attendeeFromRawData = $registrationFolder->getRawData()['attendee'];
                            $dossierFormation = [
                                'idDossier' => $registrationFolder->getExternalId(),
                                'nomTitulaire' => $attendeeFromRawData['lastName'],
                                'prenom1Titulaire' => $attendeeFromRawData['firstName'],
                            ];
                            $certificationFolderData['dossierFormation'] = $dossierFormation;
                        } else {
                            $certificationStats['nbFoldersNotCPF']++;
                            $titulaire = [
                                'nomNaissance' => !empty($attendee->getBirthName()) ? $attendee->getBirthName() : $attendee->getLastName(),
                                'nomUsage' => $attendee->getLastName(),
                                'prenom1' => $attendee->getFirstName(),
                                'prenom2' => $attendee->getFirstName2(),
                                'prenom3' => $attendee->getFirstName3(),
                                'anneeNaissance' => $attendee->getDateOfBirth()->format('Y'),
                                'moisNaissance' => $attendee->getDateOfBirth()->format('n'),
                                'jourNaissance' => $attendee->getDateOfBirth()->format('j'),
                                'sexe' => $attendee->getGender() === AttendeeGender::MALE()->getValue() ? 'M' : ($attendee->getGender() === AttendeeGender::FEMALE()->getValue() ? 'F' : null)
                            ];
                            if ($attendee->getCodeCityOfBirth()) {
                                $titulaire['codeInsee'] = $attendee->getCodeCityOfBirth();
                                $titulaire['libelleCommuneNaissance'] = $attendee->getNameCityOfBirth();
                                $titulaire['codePaysNaissance'] = null; // Don't put France here because CDC won't accept it
                                $titulaire['libellePaysNaissance'] = null; // Don't put France here because CDC won't accept it
                            } else {
                                $titulaire['codeInsee'] = 99 . $attendee->getCodeCountryOfBirth();
                                $titulaire['libelleCommuneNaissance'] = null;
                                $titulaire['codePaysNaissance'] = $attendee->getCodeCountryOfBirth();
                                $titulaire['libellePaysNaissance'] = $attendee->getNameCountryOfBirth();
                            }
                            $certificationFolderData['titulaire'] = $titulaire;
                        }
                        $certificationFoldersData[] = $certificationFolderData;
                    } else {
                        $certificationStats['nbFoldersNotProcessed']++;
                        $certificationStats['foldersNotExported'][] = $certificationFolder->getId();
                    }
                }
                if (!empty($certificationFoldersData)) {
                    $certificationData['certificationFoldersData'] = $certificationFoldersData;
                    $certificationContainer = [
                        'certificationData' => $certificationData,
                        'certificationStats' => $certificationStats,
                    ];
                    $cdcData[] = $certificationContainer;
                }
            }
        }
        return $cdcData;
    }

    /**
     * @param User $user
     * @param bool $isAutomatedGeneration
     * @param int|null $idCertif
     * @param int|null $delay
     */
    public function dispatchGenerateCdcXML(User $user, bool $isAutomatedGeneration, int $idCertif = null, int $delay = 0): void
    {
        $message = new GenerateCdcXML($user->getEmail(), $isAutomatedGeneration, $idCertif ?? null);
        $envelope = new Envelope($message, [
            new DelayStamp($delay)
        ]);
        $this->messageBus->dispatch($envelope);
    }

    /**
     * @param CdcFile $cdcFile
     * @param string $receipt
     * @param int|null $delay
     */
    public function dispatchReadReceiptXmlForCdc(CdcFile $cdcFile, string $receipt, int $delay = 0): void
    {
        $xmlArray = Tools::xmlToArray(simplexml_load_string($receipt));
        $idFlux = $xmlArray['idFlux'];
        if ($idFlux && $cdcFile->getIdFlux() && ($idFlux !== $cdcFile->getIdFlux())) {
            throw new WedofBadRequestHttpException("Erreur, l'idFlux de l'accusé de traitement ne correspond pas à l'idFlux de votre fichier XML.");
        }
        $idTraitement = $xmlArray['idTraitement'];
        $passagesById = $this->getCdcXMLService()->readReceiptXmlForCdc($xmlArray);
        $message = new ReadReceiptXmlForCdc($cdcFile->getId(), $passagesById, $idTraitement);
        $envelope = new Envelope($message, [
            new DelayStamp($delay)
        ]);
        $this->messageBus->dispatch($envelope);
    }

    /**
     * @param int $idCdcFile
     * @param array $passagesById
     * @param string $idTraitement
     * @throws Throwable
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function processXmlReceipt(int $idCdcFile, array $passagesById, string $idTraitement): void
    {
        $cdcFile = $this->cdcFileRepository->findOneBy(['id' => $idCdcFile]);
        $countSuccess = 0;
        $countFail = 0;
        foreach ($cdcFile->getCertificationFoldersCdcFiles() as $certificationFolderCdcFile) {
            $certificationFolder = $certificationFolderCdcFile->getCertificationFolder();
            $originalState = $certificationFolderCdcFile->getState();
            $certificationFolderTechnicalId = $certificationFolder->getCdcTechnicalId() ?: $certificationFolder->getId(); // Priority for technicalId if set
            // Don't override processedOk as CDC may have returned processedOk in the receipt, then process it again for the same XML and override the receipt with processedKo (cause already processed)
            if (key_exists($certificationFolderTechnicalId, $passagesById) && $originalState !== CertificationFoldersCdcFilesStates::PROCESSED_OK()->getValue()) {
                $passage = $passagesById[$certificationFolderTechnicalId];
                $newState = $passage['status'];
                if ($newState !== $originalState) {
                    if ($newState === CertificationFoldersCdcFilesStates::PROCESSED_OK()->getValue()) {
                        $this->certificationFoldersCdcFilesService->processedOk($certificationFolderCdcFile);
                    } else if ($newState === CertificationFoldersCdcFilesStates::PROCESSED_KO()->getValue()) {
                        $this->certificationFoldersCdcFilesService->processedKo($certificationFolderCdcFile, $passage['errorMessage']);
                    }
                }
                $isCertificationFolderCdcStateError = $certificationFolderCdcFile->getCertificationFolder()->getCdcState() === CertificationFolderCdcStates::PROCESSED_KO()->getValue();
                $isCertificationFolderCdcStateError ? $countFail += 1 : $countSuccess += 1;
            }
        }
        if ($cdcFile->isGeneratedAutomatically()) {
            $this->mailerService->sendRecapitulativeXmlTreatment($cdcFile, ['processedOk' => $countSuccess, 'processedKo' => $countFail]);
        }
        $this->update($cdcFile, ['state' => CdcFileStates::PROCESSED()->getValue(), 'idTraitement' => $idTraitement]);
    }

    /**
     * @param string $idFlux
     * @return CdcFile
     */
    public function findOneByIdFlux(string $idFlux): ?CdcFile
    {
        return $this->cdcFileRepository->findOneBy(['idFlux' => $idFlux]);
    }

    /**
     * @param array $parameters
     * @param Organism $organism
     * @return QueryBuilder
     */
    public function listReturnQueryBuilder(array $parameters, Organism $organism): QueryBuilder
    {
        return $this->cdcFileRepository->findAllReturnQueryBuilder($parameters, $organism);
    }

    /**
     * @param CdcFile $cdcFile
     * @return bool
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function hasCertificationFolderError(CdcFile $cdcFile): bool
    {
        return $this->certificationFoldersCdcFilesService->hasCertificationFolderError($cdcFile);
    }

    /**
     * @param Organism $organism
     * @param string $fileName
     * @param string $fileNamePath
     * @return bool
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function sendCdcXMLFileToCDC(Organism $organism, string $fileName, string $fileNamePath): bool
    {
        return $this->cdcCertifiersApiService->sendCdcXMLFile($organism, $fileName, $fileNamePath);
    }

    //----------------
    // METHODES PRIVES
    //----------------

    /**
     * @param CdcFile $cdcFile
     */
    private function save(CdcFile $cdcFile)
    {
        $this->cdcFileRepository->save($cdcFile);
    }

    /**
     * @return ArrayCollection
     */
    public function listAllCdcFilesByWedofWithoutProcessedReceiptFiles(): ArrayCollection
    {
        return $this->cdcFileRepository->findAllCdcFilesByWedofWithoutProcessedReceiptFiles();
    }

    /**
     * @throws ORMException
     * @throws WedofCpfBackendException
     * @throws ClientExceptionInterface
     * @throws OptimisticLockException
     * @throws Throwable
     * @throws ServerExceptionInterface
     * @throws NonUniqueResultException
     * @throws RedirectionExceptionInterface
     * @throws TransportExceptionInterface
     * @throws NoResultException
     * @throws ErrorException
     */
    public function findAndProcessNewProcessedReceiptFiles(Organism $organism, array $cdcFiles)
    {
        $delay = self::DELAY;
        $isWedofDeposant = $this->isWedofDeposantForOrganism($organism);
        $cdcFilesRawData = $this->cdcCertifiersApiService->getCdcFilesRawDataPaginated($organism, $isWedofDeposant, ['limit' => 50]); // Limit was 10 but we may deposit more than that now
        /** @var CdcFile $cdcFile */
        foreach ($cdcFiles as $cdcFile) {
            $found = array_search($cdcFile->getName() . ".xml", array_column($cdcFilesRawData, 'nomFichierPartenaire'));
            if ($found !== false && in_array($cdcFilesRawData[$found]['traitement']['statut'], ['TERMINE_OK', 'TERMINE_KO_FONCTIONNEL'])) {
                $cdcFileRawData = $cdcFilesRawData[$found];
                //dispatch here
                $array = $this->cdcCertifiersApiService->getProcessedReceiptFile($cdcFileRawData['traitement']['idTraitement'], $organism);
                if (isset($array['content'])) {
                    $this->dispatchReadReceiptXmlForCdc($cdcFile, $array['content'], $delay);
                    $delay += self::DELAY;
                }
            }
        }
    }

    //----------------
    // METHODES PRIVES
    //----------------


    /**
     * Otherwise circular dependency error
     * @return CdcXMLService
     */
    private function getCdcXMLService(): CdcXMLService
    {
        global $kernel;
        $container = $kernel->getContainer();
        /** @var CdcXMLService $service */
        $service = $container->get("App\Service\CdcXMLService", ContainerInterface::NULL_ON_INVALID_REFERENCE);
        return $service;
    }

    /**
     * @param Organism $organism
     * @return bool
     */
    public function isWedofDeposantForOrganism(Organism $organism): bool
    {
        $connection = $organism->getConnectionForDataProvider(DataProviders::CDC_CERTIFIERS());
        return ($connection && $connection->getType() === ConnectionTypes::HABILITATION()->getValue());
    }
}
