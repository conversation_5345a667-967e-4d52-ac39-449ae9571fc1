<?php
// src/Service/ApplicationService.php
namespace App\Service;

use App\Application\oauth2\WedofApplicationOAuth2Provider;
use App\Application\WedofApplication;
use App\Entity\Application;
use App\Entity\Organism;
use App\Entity\Subscription;
use App\Event\Application\ApplicationEvent;
use App\Exception\FailureAccessTokenException;
use App\Exception\MissingAccessTokenException;
use App\Library\utils\enums\ApplicationStates;
use App\Library\utils\Tools;
use App\Repository\ApplicationRepository;
use BadMethodCallException;
use DateTime;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\OptimisticLockException;
use Exception;
use KnpU\OAuth2ClientBundle\Client\ClientRegistry;
use KnpU\OAuth2ClientBundle\Client\OAuth2ClientInterface;
use League\OAuth2\Client\Provider\Exception\IdentityProviderException;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use ReflectionClass;
use ReflectionException;
use RuntimeException;
use Stripe\Exception\ApiErrorException;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\Finder\Finder;

class ApplicationService implements LoggerAwareInterface
{
    private LoggerInterface $logger;
    private ApplicationRepository $applicationRepository;
    private ClientRegistry $oauth2Client;
    private EventDispatcherInterface $dispatcher;
    private ContainerInterface $container;

    public function __construct(ContainerInterface $container, EventDispatcherInterface $dispatcher, ApplicationRepository $applicationRepository, ClientRegistry $oauth2Client)
    {
        $this->dispatcher = $dispatcher;
        $this->oauth2Client = $oauth2Client;
        $this->applicationRepository = $applicationRepository;
        $this->container = $container;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    /**
     * @param Application $application
     * @param array $metadata
     * @param bool $dispatch
     * @return Application
     */
    public function updateMetadata(Application $application, array $metadata, bool $dispatch = true): Application
    {
        $previousMetadata = $application->getMetadata();
        $application->setMetadata($metadata);
        $this->applicationRepository->save($application);
        if ($dispatch) {
            $this->dispatcher->dispatch(new ApplicationEvent($application, $previousMetadata), ApplicationEvent::UPDATED_METADATA);
        }
        return $application;
    }

    /**
     * @param Application $application
     * @param string $method
     * @param $body
     * @return mixed|null
     */
    public function data(Application $application, string $method, $body)
    {
        /** @var WedofApplication $appService */
        $name = Tools::dashesToCamelCase($application->getAppId(), true);
        $appService = $this->container->get("App\Application\\" . $name . "\\" . $name . "WedofApplication");
        if (in_array($method, $appService->availableMethods()) && method_exists($appService, $method)) {
            return $appService->$method($application, $body ? json_decode($body, true) : null, $this);
        } else {
            throw new BadMethodCallException();
        }
    }

    /**
     * @param Application $application
     * @param bool $pendingEnableDone
     * @return string
     * @throws ApiErrorException
     * @throws ReflectionException
     */
    public function enable(Application $application, bool $pendingEnableDone = false): string
    {
        $subscription = $application->getOrganism()->getSubscription();
        /** @var StripeService $stripeService */
        $stripeService = $this->container->get("App\Service\StripeService");
        //event can set the state of the app like pending_enable & pending_enable_trial
        $this->dispatcher->dispatch(new ApplicationEvent($application), ApplicationEvent::BEFORE_ENABLE);
        /** @var WedofApplication $appClass */
        $appClass = $this->getWedofApplicationClass($application->getAppId());
        $prices = $appClass::availablePrices();
        $startTrial = !empty($prices['trial']) && $application->getState() == ApplicationStates::DISABLED() && $application->getEndDate() == null;
        $hasTrialEnded = ($application->getState() == ApplicationStates::TRIAL() && $application->getEndDate() <= new DateTime('now'));
        $events = [];
        if ($startTrial || ($pendingEnableDone && $application->getState() == ApplicationStates::PENDING_ENABLE_TRIAL())) {
            $application->setState(ApplicationStates::TRIAL());
            $application->setEndDate((new DateTime('now'))->modify($prices['trial']));
            $events[] = ApplicationEvent::TRIAL_STARTED;
            $events[] = ApplicationEvent::ENABLED;
        } else
            if ($application->getState() == ApplicationStates::PENDING_DISABLE_TRIAL()) {
                $application->setState(ApplicationStates::TRIAL());
            } else if ($application->getState() == ApplicationStates::PENDING_DISABLE()) {
                $application->setState(ApplicationStates::ENABLED());
                $application->setEndDate(null);
                if (!empty($prices)) {
                    $stripeService->addSubscriptionOption($subscription, $application, false);
                }
            } else
                if ($hasTrialEnded
                    || ($pendingEnableDone && $application->getState() == ApplicationStates::PENDING_ENABLE())
                    || $application->getState() == ApplicationStates::DISABLED()) {
                    $application->setState(ApplicationStates::ENABLED());
                    $application->setEndDate(null);
                    if (!empty($prices)) {
                        $stripeService->addSubscriptionOption($subscription, $application);
                    }
                    if ($hasTrialEnded) {
                        $events[] = ApplicationEvent::TRIAL_ENDED;
                    }
                    $events[] = ApplicationEvent::ENABLED;
                }
        //always save, there are maybe changed from beforeEnable
        $this->applicationRepository->save($application);
        if (!empty($events)) {
            foreach ($events as $event) {
                $this->dispatcher->dispatch(new ApplicationEvent($application), $event);
            }
        }
        return $application->getState();
    }

    /**
     * @param Application $application
     * @param bool $pendingDisableDone
     * @return string
     * @throws ApiErrorException
     * @throws ReflectionException
     * @throws Exception
     */
    public function disable(Application $application, bool $pendingDisableDone = false): string
    {
        $subscription = $application->getOrganism()->getSubscription();
        $stripeService = $this->container->get("App\Service\StripeService");
        $this->dispatcher->dispatch(new ApplicationEvent($application), ApplicationEvent::BEFORE_DISABLE);
        /** @var WedofApplication $appClass */
        $appClass = $this->getWedofApplicationClass($application->getAppId());
        $prices = $appClass::availablePrices();
        $events = [];
        /** @var WedofApplication $appClass */
        if ($application->getState() == ApplicationStates::TRIAL() && $application->getEndDate() >= new DateTime()) {
            $application->setState(ApplicationStates::PENDING_DISABLE_TRIAL());
            $events[] = ApplicationEvent::PENDING_DISABLE;
            $events[] = ApplicationEvent::PENDING_DISABLE_TRIAL;
        } else if ($application->getState() == ApplicationStates::ENABLED() && !empty($prices)) {
            /** @var StripeService $stripeService */
            $stripeSubscription = $stripeService->removeSubscriptionOption($subscription, $application);
            if ($stripeSubscription) {
                $currentPeriodEnd = new DateTime('@' . $stripeSubscription->current_period_end);
                $application->setEndDate($currentPeriodEnd);
                if ($currentPeriodEnd >= new DateTime('now')) {
                    $application->setState(ApplicationStates::PENDING_DISABLE());
                    $events[] = ApplicationEvent::PENDING_DISABLE;
                } else {
                    $application->setState(ApplicationStates::DISABLED());
                    $events[] = ApplicationEvent::PENDING_DISABLE;
                    $events[] = ApplicationEvent::DISABLED;
                }
            } else {
                $application->setState(ApplicationStates::DISABLED());
                $application->setEndDate(new DateTime('now'));
                $events[] = ApplicationEvent::PENDING_DISABLE;
                $events[] = ApplicationEvent::DISABLED;
            }
        } else if ($pendingDisableDone || !$application->getEndDate() || $application->getEndDate() <= new DateTime()) {
            $application->setState(ApplicationStates::DISABLED());
            $application->setEndDate(new DateTime('now'));
            $this->updateMetadata($application, []);
            $events[] = ApplicationEvent::DISABLED;
        }
        //always save, there are maybe changed from beforeDisable
        $this->applicationRepository->save($application);
        if (!empty($events)) {
            foreach ($events as $event) {
                $this->dispatcher->dispatch(new ApplicationEvent($application), $event);
            }
        }
        return $application->getState();
    }

    /**
     * @param Organism $organism
     * @param string $appId
     * @param bool|null $enabled
     * @param bool $createIfNotExist
     * @return Application|null
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws \Doctrine\ORM\ORMException
     * @throws ReflectionException
     */
    public function getByOrganismAndAppId(Organism $organism, string $appId, bool $enabled = null, bool $createIfNotExist = true): ?Application
    {
        $organismApplication = $this->applicationRepository->findOneByOrganismAndAppId($organism, $appId, $enabled);
        if (!$organismApplication && $createIfNotExist && !$enabled) { //if not exist we initiate the application
            /** @var WedofApplication $appClass */
            $appClass = $this->getWedofApplicationClass($appId);
            $organismApplication = new Application();
            $organismApplication->setAppId($appId);
            $organismApplication->setOrganism($organism);
            $organismApplication->setState(ApplicationStates::DISABLED());
            $organismApplication = $this->applicationRepository->save($organismApplication);
        }
        return $organismApplication;
    }

    /**
     * @param Application $application
     * @return string
     */
    public function getOAuth2AuthorizationUrl(Application $application): string
    {
        return $this->getOAuth2Client($application)->getOAuth2Provider()->getAuthorizationUrl();
    }

    /**
     * @param Application $application
     * @return OAuth2ClientInterface
     */
    public function getOAuth2Client(Application $application): OAuth2ClientInterface
    {
        $client = $this->oauth2Client->getClient('wedof_' . $application->getAppId());
        /** @var WedofApplicationOAuth2Provider $provider */
        $provider = $client->getOAuth2Provider();
        $provider->configure($application);
        return $client;
    }

    /**
     * @param Application $application
     * @return string|null
     * @throws IdentityProviderException
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function getAccessToken(Application $application): string
    {
        $accessToken = null;
        $oauth2 = $application->getOauth2();
        if (!empty($oauth2['access_token']) && !empty($oauth2['expires'])) { //access token that can expires
            $expires = new DateTime();
            $expires->setTimestamp($oauth2['expires']);
            if ($expires > (new DateTime())->modify('+1 hour')) { //take time security
                $accessToken = $oauth2['access_token'];
            }
        } else if (!empty($oauth2['access_token'])) { //token without "known" expiration
            $accessToken = $oauth2['access_token'];
        }
        if ($accessToken) {
            return $accessToken;
        } else if (!empty($oauth2['refresh_token']) && $this->refreshToken($application)) {
            return $this->getAccessToken($application);
        } else {
            throw new MissingAccessTokenException();
        }
    }

    /**
     * @param Application $application
     * @return bool
     */
    public function refreshToken(Application $application): bool
    {
        $client = $this->getOAuth2Client($application);
        $oauth2 = $application->getOauth2();
        if (!empty($oauth2['refresh_token'])) {
            try {
                $accessToken = $client->refreshAccessToken($oauth2['refresh_token']);
                if ($accessToken->getToken()) {
                    $oauth2 = array_merge($oauth2, $accessToken->jsonSerialize());
                    $application->setOauth2($oauth2);
                    $this->applicationRepository->save($application);
                    $this->dispatcher->dispatch(new ApplicationEvent($application), ApplicationEvent::REFRESH_OAUTH);
                    return true;
                }
            } catch (Exception $e) {
                $this->logger->error($e->getMessage());
                $this->logger->error($e->getTraceAsString());
                throw new FailureAccessTokenException($e);
            }
        }
        return false;
    }

    /**
     * @param Application $application
     * @return bool
     * @throws IdentityProviderException
     */
    public function setAccessToken(Application $application): bool
    {
        $client = $this->getOAuth2Client($application);
        try {
            $accessToken = $client->getAccessToken(); //from request context
            if ($accessToken->getToken()) {
                $oauth2 = $accessToken->jsonSerialize();
                $application->setOauth2($oauth2);
                $this->applicationRepository->save($application);
                $this->dispatcher->dispatch(new ApplicationEvent($application), ApplicationEvent::NEW_OAUTH);
                return true;
            }
        } catch (Exception $e) {
            $this->logger->error($e->getMessage());
            $this->logger->error($e->getTraceAsString());
            throw $e;
        }
        return false;
    }

    /**
     * @param Subscription $subscription
     * @param bool $autoActivatedOnly
     * @return array of AppId
     * @throws ReflectionException
     */
    public function listAllowedAppIds(Subscription $subscription, bool $autoActivatedOnly = false): array
    {
        $allowedApplications = [];

        $wedofApplicationReflector = new ReflectionClass(WedofApplication::class);
        $wedofApplicationDirectory = dirname($wedofApplicationReflector->getFileName());
        $wedofApplicationNameSpace = $wedofApplicationReflector->getNamespaceName();

        $finderFiles = Finder::create()->files()->in($wedofApplicationDirectory)->name('*.php');

        foreach ($finderFiles->files() as $finderFile) {
            $directoriesAndFileName = explode($wedofApplicationDirectory, $finderFile->getRealpath()); // on récupère le DossierSpecifique\NomDuFichier.php (ex: CertifCpf\CertifCpfWedofApplication.php)
            $fileNameAndExtension = explode('.', array_pop($directoriesAndFileName)); // on retire l'extension .php
            $className = $wedofApplicationNameSpace . array_shift($fileNameAndExtension); // on rajoute le namespace pour reconstituer le nom de la classe (ex App\Application\CertifCpf\CertifCpfWedofApplication.php)
            $className = str_replace(DIRECTORY_SEPARATOR, '\\', $className); // formatage

            $reflectedClass = new ReflectionClass($className);
            if ($reflectedClass->isSubclassOf($wedofApplicationReflector)) {
                $allowedSubscriptionTypes = $className::getAllowedSubscriptionTypes($subscription);
                $organism = $subscription->getOrganism();
                if (!$autoActivatedOnly || $className::autoActivate()) {
                    if ($organism->isReseller() && isset($allowedSubscriptionTypes['reseller']) && $allowedSubscriptionTypes['reseller'] === true) {
                        $allowedApplications[] = $className::getAppId();
                    } else if (in_array($subscription->getTrainingType(), $allowedSubscriptionTypes['training']) || (isset($allowedSubscriptionTypes['certifier']) && in_array($subscription->getCertifierType(), $allowedSubscriptionTypes['certifier']))) {
                        $allowedApplications[] = $className::getAppId();
                    }
                }
            }
        }

        return $allowedApplications;
    }

    /**
     * @param string $appId
     * @return string of AppId
     * @throws ReflectionException
     */
    public function getWedofApplicationClass(string $appId): string
    {
        $wedofApplicationReflector = new ReflectionClass(WedofApplication::class);
        $wedofApplicationNameSpace = $wedofApplicationReflector->getNamespaceName();
        //message-templates => messageTemplates
        if (Tools::contains($appId, '-')) {
            $values = explode('-', $appId);
            $appClassName = join('', array_map(fn($k, $v): string => ($k > 0 ? ucfirst($v) : $v), array_keys($values), $values));

        } else {
            $appClassName = $appId;
        }
        $className = $wedofApplicationNameSpace . "\\" . ucfirst($appClassName) . "\\" . ucfirst($appClassName) . "WedofApplication"; // on rajoute le namespace pour reconstituer le nom de la classe (ex App\Application\CertifCpf\CertifCpfWedofApplication.php)
        $reflectedClass = new ReflectionClass($className);
        if ($reflectedClass->isSubclassOf($wedofApplicationReflector)) {
            return $className;
        } else {
            throw new RuntimeException("No Wedof Application found");
        }
    }

    /**
     * @param Subscription $subscription
     * @param string $appId
     * @return bool
     * @throws ReflectionException
     */
    public function isApplicationAllowed(string $appId, Subscription $subscription): bool
    {
        return in_array($appId, $this->listAllowedAppIds($subscription));
    }

    /**
     * @param Organism $organism
     * @param bool|null $enabled
     * @return array
     */
    public function listAppIdsForOrganism(Organism $organism, bool $enabled = null): array
    {
        return $this->applicationRepository->findAppIdsForOrganism($organism, $enabled);
    }
}
