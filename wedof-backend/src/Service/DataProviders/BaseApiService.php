<?php


namespace App\Service\DataProviders;

use App\Entity\Certification;
use App\Entity\Connection;
use App\Entity\EndPointStatus;
use App\Entity\Organism;
use App\Entity\RegistrationFolder;
use App\Entity\User;
use App\Event\MonitoringEvents;
use App\Exception\WedofConflictHttpException;
use App\Exception\WedofConnectionException;
use App\Library\utils\enums\ConnectionStates;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\enums\RegistrationFolderStates;
use App\Library\utils\Tools;
use App\Repository\EndPointStatusRepository;
use App\Service\ConfigService;
use App\Service\ConnectionService;
use DateTime;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use ErrorException;
use Exception;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpClient\CurlHttpClient;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Mime\Part\DataPart;
use Symfony\Component\Mime\Part\Multipart\FormDataPart;
use Symfony\Component\RateLimiter\RateLimiterFactory;
use Symfony\Component\Security\Core\Security;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Throwable;

abstract class BaseApiService implements InterfaceAuthenticationApiService, LoggerAwareInterface
{

    protected Security $security;
    protected ConfigService $configService;
    protected RequestStack $requestStack;
    protected ConnectionService $connectionService;
    protected DataProviders $dataProvider;
    protected EndPointStatusRepository $endPointStatusRepository;
    protected EventDispatcherInterface $dispatcher;
    protected LoggerInterface $logger;
    protected ?RateLimiterFactory $rateLimiter;

    public function __construct(
        DataProviders            $dataProvider,
        ConfigService            $configService,
        ConnectionService        $connectionService,
        RequestStack             $requestStack,
        EndPointStatusRepository $endPointStatusRepository,
        EventDispatcherInterface $dispatcher,
        LoggerInterface          $logger,
        Security                 $security)
    {
        $this->configService = $configService;
        $this->connectionService = $connectionService;
        $this->dataProvider = $dataProvider;
        $this->requestStack = $requestStack;
        $this->endPointStatusRepository = $endPointStatusRepository;
        $this->dispatcher = $dispatcher;
        $this->logger = $logger;
        $this->security = $security;
        $this->rateLimiter = null;
    }

    /**
     * @param DataProviders|null $dataProvider
     * @param string|null $entityClassName
     * @return BaseApiService
     */
    public static function getApiServiceByDataProvider(?DataProviders $dataProvider, string $entityClassName = null): BaseApiService
    {
        $apiService = null;
        // gestion du service pour les dossiers internes (individual, opco sans connexion, pole emploi sans connexion et company)
        if (!is_null($dataProvider) && in_array($dataProvider->getValue(), DataProviders::getInternalToString())) {
            $dataProvider = DataProviders::INTERNAL();
        }
        global $kernel;
        $container = $kernel->getContainer();
        if (is_null($dataProvider) || $dataProvider->equals(DataProviders::INTERNAL())) {
            if (is_null($entityClassName)) {
                $apiService = $container->get(InternalApiService::class);
            } else {
                $apiService = $container->get('App\Service\DataProviders\\' . $entityClassName . ucfirst($dataProvider->getValue()) . 'ApiService', ContainerInterface::NULL_ON_INVALID_REFERENCE);
            }
        }
        if (!$apiService) {
            $apiService = $container->get("App\Service\DataProviders\\" . ucfirst($dataProvider->getValue()) . "ApiService", ContainerInterface::NULL_ON_INVALID_REFERENCE);
        }
        if (!$apiService) {
            $apiService = $container->get("App\Application\\" . ucfirst($dataProvider->getValue()) . "\Service\\" . ucfirst($dataProvider->getValue()) . "ApiService", ContainerInterface::NULL_ON_INVALID_REFERENCE);
        }
        if (!$apiService) {
            $apiService = $container->get(InternalApiService::class);
        }
        /** @var BaseApiService $apiService */
        return $apiService;
    }

    /**
     * @param DataProviders|null $dataProvider
     * @return InterfaceRegistrationFolderApiService
     */
    public static function getRegistrationFolderApiServiceByDataProvider(?DataProviders $dataProvider): InterfaceRegistrationFolderApiService
    {
        /** @var InterfaceRegistrationFolderApiService $apiService */
        $apiService = self::getApiServiceByDataProvider($dataProvider, RegistrationFolder::CLASSNAME);
        return $apiService;
    }

    /**
     * @param DataProviders|null $dataProvider
     * @return InterfaceCatalogApiService
     */
    public static function getCatalogApiServiceByDataProvider(?DataProviders $dataProvider): InterfaceCatalogApiService
    {
        /** @var InterfaceCatalogApiService $apiService */
        $apiService = self::getApiServiceByDataProvider($dataProvider, RegistrationFolder::CLASSNAME);
        return $apiService;
    }

    /**
     * @param DataProviders|null $dataProviders
     * @return CertificationInternalApiService
     */
    public static function getCertificationApiServiceByDataProvider(?DataProviders $dataProviders): InterfaceCertificationApiService
    {
        /** @var CertificationInternalApiService $apiService */
        $apiService = self::getApiServiceByDataProvider($dataProviders, Certification::CLASSNAME);
        return $apiService;
    }


    /**
     * @param LoggerInterface $logger
     */
    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    /**
     * @param Connection $connection
     * @return void
     */
    public function startInitialization(Connection $connection): void
    {
        $connection->setIsInitialized(true);
        $this->connectionService->save($connection);
    }

    /**
     * @param Connection $connection
     * @return void
     */
    public function initializeCompleted(Connection $connection): void
    {
    }

    /**
     * @param array $rawData
     * @param string $actionLabel
     * @param RegistrationFolderStates $state
     * @param Organism $organism
     * @param string|null $authorEmail
     * @param bool $isCurrentState
     * @param DateTime|null $datetime
     * @return array
     */
    protected function addStateToRawData(array $rawData, string $actionLabel, RegistrationFolderStates $state, Organism $organism, string $authorEmail = null, bool $isCurrentState = true, DateTime $datetime = null): array
    {
        if (!$authorEmail) {
            /** @var User $currentUser */
            $currentUser = $this->security->getUser();
            $authorEmail = $currentUser ? $currentUser->getEmail() : $organism->getOwnedBy()->getEmail();
        }
        $rawData['history'] ??= [];
        $rawData['states'] ??= [];

        array_unshift($rawData['history'], [
            "date" => ($datetime ?? new DateTime('now'))->format('Y-m-d\TH:i:s.v\Z'),
            "label" => $actionLabel,
            "author" => $authorEmail,
            "state" => $state->getValue()
        ]);

        $rawData = array_merge($rawData, [
            'states' => array_merge([
                $state->getValue() => [
                    'date' => ($datetime ?? new DateTime('now'))->format('Y-m-d\TH:i:s.v\Z')
                ]
            ], $rawData['states'])
        ]);
        if ($isCurrentState) {
            $rawData = array_merge($rawData, ['currentState' => $state->getValue()]);
        }
        return $rawData;
    }

    /**
     * @param array $responseHeaders
     * @return array
     */
    protected function formatResponseHeaders(array $responseHeaders): array
    {
        return $responseHeaders;
    }

    /**
     * @param DataProviders $dataProvider
     * @param string $key
     * @return mixed
     */
    protected static function getEnvApiConfig(DataProviders $dataProvider, string $key)
    {
        return Tools::getEnvValue(strtoupper($dataProvider->getValue()) . '_' . $key);
    }

    /**
     * @param string $url
     * @param array $options
     * @return array|null
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    protected function sendRequest(string $url, array $options = []): ?array
    {
        //track client ip caller
        $excludeIps = ['localhost', '127.0.0.1', '::1'];
        $excludeIps = array_merge($excludeIps, $this->configService->getWorkersIps());
        $clientIp = $_SERVER['OVERRIDE_CLIENT_ADDR'] ?? ($this->requestStack->getCurrentRequest() ? $this->requestStack->getCurrentRequest()->getClientIp() : null);
        $clientIp = !in_array($clientIp, $excludeIps) ? $clientIp : null;

        $options = array_merge([
            "_retry" => 0,
            "headers" => [],
            "method" => "GET",
            "organism" => null,
            "parameters" => [],
            "forceRetry" => false,
            "useDownload" => false,
            "access_token" => null,
            "fullResponse" => false,
            "retryOn200Null" => true,
            "publicFakingData" => false,
            "throw_404_exception" => true,
            "publicFakingDataAuth" => false,
            "dispatchMonitoringEvent" => true,
            'content-type' => 'application/json',
            'clientIpAddress' => $clientIp ?? null,
            "cat_api_status" => "api",
            "maxRetry" => self::getEnvApiConfig($this->dataProvider, 'API_MAX_RETRY')
        ], $options);

        $config = $this->configService->getConfig();
        $checkApiEnabledName = "is" . ucfirst($this->dataProvider) . "ApiEnabled";
        if (method_exists($config, $checkApiEnabledName) && !$config->$checkApiEnabledName()) {
            throw new ErrorException($this->dataProvider . " API is offline", 503);
        }

        $url = Tools::fixWeirdSymbols($url);
        $this->logger->debug($url);

        /** @var Connection $connection */
        $connection = null;
        $hasAuthenticationAlready = !empty($options['access_token']) || !empty($options['cookie']) || !empty($options['authenticationHeaderValue']);
        if ($options['organism'] && !$hasAuthenticationAlready) {
            /** @var Connection $connection */
            $connection = $options['organism']->getConnectionForDataProvider($this->dataProvider);
            if (!$connection) {
                throw new WedofConnectionException("Aucune connection du type " . $this->dataProvider->getValue() . " existant pour l'organisme " . $options['organism']->getSiret());
            }
            if ($connection->getState() !== ConnectionStates::ACTIVE()->getValue() && $connection->getCredentials()) {
                throw new WedofConnectionException("Erreur de connexion à l'API " . $this->dataProvider->getValue() . " vérifiez votre login/mot de passe pour l'organisme " . $options['organism']->getSiret());
            }
            $options = $this->setAuthentication($options);
        }

        $clientOptions = ['timeout' => self::getEnvApiConfig($this->dataProvider, 'API_TIMEOUT')];
        if (!empty($options['access_token'])) {
            $clientOptions['auth_bearer'] = $options['access_token'];
        }
        if (!empty($options['auth_basic'])) {
            $clientOptions['auth_basic'] = $options['auth_basic'];
        }
        if (!empty($options['cookie'])) {
            $clientOptions['headers']['Cookie'] = $options['cookie'];
        }
        if (!empty($options['authenticationHeaderName']) && !empty($options['authenticationHeaderValue'])) {
            $clientOptions['headers'][$options['authenticationHeaderName']] = $options['authenticationHeaderValue'];
        }
//        // May be useful for dev, but never use in prod!!
//        if (!empty($options['insecure'])) {
//            $clientOptions['verify_peer'] = false;
//            $clientOptions['verify_host'] = false;
//        }

        $client = new CurlHttpClient($clientOptions);

        $statusCode = null;
        $endPointStatus = null;
        if ($options['cat_api_status']) {
            $endPointStatusParams = !empty($options['parameters']) ? $options['parameters'] : [];
            if (!empty($endPointStatusParams['password'])) {
                $endPointStatusParams['password'] = "***";
            }
            $endPointStatusParams = (!empty($endPointStatusParams) && $options['method'] != "GET") ? ['json' => json_encode($endPointStatusParams)] : $endPointStatusParams;
            $endPointStatus = new EndPointStatus();
            $endPointStatus->setUrl($url);
            $endPointStatus->setHost(gethostname());
            $endPointStatus->setClientIp($clientIp);
            $endPointStatus->setOrganism($options['organism'] ?? null);
            $endPointStatus->setMethod($options['method']);
            $endPointStatus->setParams($endPointStatusParams);
            $endPointStatus->setDate(new DateTime());
            $endPointStatus->setCategory($this->dataProvider . "-" . $options['cat_api_status']);
        }
        $response = null;
        $responseHeaders = [];
        try {
            $result = null;
            sleep($options['_retry']); //slow down requests if we start to get errors
            $array = [
                'headers' => array_merge([
                    'User-Agent' => "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                ], $options['headers'])
            ];
            if ($options['publicFakingData']) {
                $array['headers']['Content-Type'] = 'application/json';
                $array['headers']['Cookie'] = 'JSESSIONID=eatMyMagicCookie';
            }
            if ($options['publicFakingDataAuth']) {
                $array['headers']['Content-Type'] = 'application/json';
                $array['headers']['Cookie'] = 'JSESSIONID=6D9BDD6EBF1850D77155E11290F1C0D3.xsl6eihm_b4';
            }
            if (!empty($options['files'])) {
                $options['method'] = "POST";
                $formFields = $options['parameters'] ?? []; //regular params must be strings or arrays
                foreach ($options['files'] as $key => $value) { //files params
                    if (is_array($value)) {
                        $formFields[$key] = DataPart::fromPath($value['path'], $value['name'], $value['contentType']);
                    } else {
                        $formFields[$key] = DataPart::fromPath($value);
                    }
                }
                $formData = new FormDataPart($formFields);
                $requestHeaders = $formData->getPreparedHeaders()->toArray();
                foreach ($requestHeaders as $header) {
                    $header = explode(':', $header);
                    $array["headers"][$header[0]] = trim($header[1]);
                }
                $array['body'] = $formData->bodyToIterable();
            } else if (!empty($options['parameters']) && $options['method'] != "GET") {
                if ($options['content-type'] == 'application/json') {
                    if (is_string($options['parameters'])) {
                        // Already serialized
                        $array['body'] = $options['parameters'];
                        $array['headers']['Content-Type'] = 'application/json';
                    } else {
                        // Serialize to JSON + put content type automatically
                        $array['json'] = $options['parameters'];
                    }
                } else if ($options['content-type'] == 'text/xml') {
                    $array['body'] = $options['parameters']['body'];
                    $array['headers']['Content-Type'] = 'text/xml';
                } else {
                    $array['body'] = $options['parameters'];
                    if (isset($options['content-type'])) {
                        $array['headers']['Content-Type'] = $options['content-type'];
                    }
                }
            } else if (!empty($options['parameters'])) {
                $array['query'] = $options['parameters'];
            }
            $this->logger->debug("[sendRequest][" . (!empty($options['organism']) ? $options['organism']->getName() : '..') . "] $url with " . $options['method']);

            if ($options['method'] != "GET"
                && !Tools::isEnvIn(['prod', 'test'])
                && !str_contains($url, 'automator.wedof.fr/webhook/edof')
                && !Tools::getEnvValue(strtoupper($this->dataProvider) . '_SEND_REQUEST_POST_DEV', false)) {
                $client->reset();
                unset($options['organism']); //else fail to print_r
                $this->logger->info("Would send request with " . print_r($options, true));
                return $options['parameters']; //may not be the good result of the simulated request
            }

            if ($options['useDownload']) {
                // Header management seems wrong, here how it should be done :
                // $headerString = '';
                // foreach ($headers as $key => $value) {
                //     $headerString .= "$key: $value\r\n";
                // }
                $array = array('http' => array(
                    'headers' => $array['headers']['User-Agent'],
                    'method' => $options['method'],
                    'ignore_errors' => true
                ));
                $context = stream_context_create($array);
                $data = @file_get_contents($url, false, $context);
                if ($data === FALSE) {
                    return null;
                } else {
                    return ["content" => $data];
                }
            }
            //<== case we manage the rateLimit internally
            if ($this->rateLimiter && $connection) {
                $this->checkRateLimit($connection);
            }
            //==>
            $response = $client->request($options['method'], $url, $array);
            $responseHeaders = $response->getHeaders(false);
            $responseHeaders = $this->formatResponseHeaders($responseHeaders);
            $statusCode = $response->getStatusCode();
            if ($statusCode != 404 || $options['throw_404_exception']) {
                if ($options['fullResponse']) {
                    return [
                        "url" => $response->getInfo('url'),
                        "statusCode" => $response->getStatusCode(),
                        "content" => $response->getContent(false),
                        "headers" => $responseHeaders,
                        "allHeaders" => $response->getInfo('response_headers')
                    ];
                } else {
                    if (isset($responseHeaders['content-type']) &&
                        (Tools::contains($responseHeaders['content-type'][0], 'application/json') || Tools::contains($responseHeaders['content-type'][0], 'application/ld+json'))) {
                        $result = json_decode($response->getContent(), true);
                    } else {
                        $response->getContent();//<=== getContent will throw an exception
                        $result = ["response" => true];
                    }
                }
            }
            $client->reset();
            if ($endPointStatus) {
                $endPointStatus->setStatusCode($statusCode);
                $endPointStatus->setSuccess(true);
                $this->endPointStatusRepository->save($endPointStatus);
            }
        } catch (Throwable $e) {
            $client->reset();
            if ($endPointStatus) {
                $endPointStatus->setSuccess(false);
                $endPointStatus->setStatusCode($statusCode ?: -1); //sometimes...
                $this->endPointStatusRepository->save($endPointStatus);
            }
            //give a try to that simple fix
            if ($statusCode == 429) {
                $now = new DateTime();
                $reset = new DateTime();
                if (!empty($responseHeaders['X-RateLimit-Reset'][0])) {
                    $reset->setTimestamp($responseHeaders['X-RateLimit-Reset'][0]);
                    $interval = $reset->diff($now);
                    $this->logger->info("X-RateLimit-Limit {$responseHeaders['X-RateLimit-Limit'][0]}");
                    $this->logger->info("X-RateLimit-Remaining {$responseHeaders['X-RateLimit-Remaining'][0]}");
                    $this->logger->info("X-RateLimit-Reset {$responseHeaders['X-RateLimit-Reset'][0]}");
                    $endPointStatus->setParams($endPointStatus->getParams());
                    $this->endPointStatusRepository->save($endPointStatus);
                    $this->logger->info('Limit reached - wait ' . $interval->s . 'sec');
                    sleep($interval->s + 1);
                } else {
                    sleep(10); // arbitrary sleep
                }
                unset($options['cookie']); // In case auth has expired in the meantime, maybe generalize it to all retries
                unset($options['access_token']); // In case auth has expired in the meantime, maybe generalize it to all retries
                $result = $this->retryRequest($url, $options);
            } else if ($statusCode == 418 || $statusCode == 404) { //no results
                throw new ErrorException($e->getMessage(), $statusCode);
            } else if ($statusCode == 409) {
                $result = json_decode($response->getContent(false), true);
                throw new WedofConflictHttpException("Erreur EDOF: " . ($result && isset($result['message']) ? $result['message'] : " un problème non identifié est survenu. S'il persiste, merci de contacter nos services de support."));
            } else if ($statusCode == 403 && isset($options['organism'])) {
                if ($options['_retry'] == 0 && $this->setActiveOrganism($options['organism'])) {
                    $result = $this->retryRequest($url, $options);
                } else {
                    if ($connection) {
                        $this->connectionService->resetRefresh($connection);
                    }
                    if ($options['dispatchMonitoringEvent']) {
                        $j = new ErrorException("Requête vers " . $this->dataProvider . " " . ($options['organism'] ? ("pour " . $options['organism']->getName() . " (" . $options['organism']->getSiret() . ")") : '') . " : $url en " . $options['method'] . ($options['parameters'] ? " et avec les paramètres : " . $this->obfuscatePassword(json_encode($options['parameters'])) : '') . " a générée une 403. \nErreur " . ($error ?? $e->getMessage()), $statusCode, 1, __FILE__, __LINE__, $e);
                        $this->dispatcher->dispatch(new MonitoringEvents($j), MonitoringEvents::API_PROVIDER_ERROR);
                    }
                }
            } else if ($statusCode == 401 && isset($options['organism'])) {
                if ($options['_retry'] == 0 && $this->connectionService->hasAccess($options['organism'], $this->dataProvider)) {
                    unset($options['cookie']);
                    unset($options['access_token']);
                    $result = $this->retryRequest($url, $options);
                } else {
                    if ($connection && $connection->getExpiresOn() >= new DateTime()) {
                        $this->connectionService->resetRefresh($connection);
                    }
                    if ($options['dispatchMonitoringEvent']) {
                        $j = new ErrorException("Requête vers " . $this->dataProvider . " " . ($options['organism'] ? ("pour " . $options['organism']->getName() . " (" . $options['organism']->getSiret() . ")") : '') . " : $url en " . $options['method'] . ($options['parameters'] ? " et avec les paramètres : " . $this->obfuscatePassword(json_encode($options['parameters'])) : '') . " a générée une 401. \nErreur " . ($error ?? $e->getMessage()), $statusCode, 1, __FILE__, __LINE__, $e);
                        $this->dispatcher->dispatch(new MonitoringEvents($j), MonitoringEvents::API_PROVIDER_ERROR);
                    }
                }
            } else if ($this->canRetryRequest($options)) {
                if ($statusCode != 503) {
                    $this->logger->error($e->getTraceAsString());
                }
                $result = $this->retryRequest($url, $options);
            } else {
                $this->logger->error("[sendRequest][" . (!empty($options['organism']) ? $options['organism']->getName() : '..') . "] " . $e->getMessage());
                $error = $response->getContent(false); //maybe we have an error message;
                if ($error) {
                    try {
                        $error = json_decode($error, true);
                        $error = $error['message'] ?? $error['text'] ?? $error['error'] ?? null;
                    } catch (Exception $exception) {
                    }
                }
                $this->logger->error("[sendRequest][" . (!empty($options['organism']) ? $options['organism']->getName() : '..') . "] Data provider error " . ($error ?? $e->getMessage()));
                if (!$error) {
                    $i = new ErrorException("La requête vers " . $this->dataProvider . " " . ($options['organism'] ? ("pour " . $options['organism']->getName() . " (" . $options['organism']->getSiret() . ")") : '') . " : $url en " . $options['method'] . ($options['parameters'] ? " et avec les paramètres : " . $this->obfuscatePassword(json_encode($options['parameters'])) : '') . " n'a pu aboutir. \nErreur " . $e->getMessage(), $statusCode, 1, __FILE__, __LINE__, $e);
                } else {
                    $i = new WedofConnectionException("Data provider ($this->dataProvider) erreur : " . $error);
                }
                if ($options['dispatchMonitoringEvent']) {
                    $j = new ErrorException("Requête vers " . $this->dataProvider . " " . ($options['organism'] ? ("pour " . $options['organism']->getName() . " (" . $options['organism']->getSiret() . ")") : '') . " : $url en " . $options['method'] . ($options['parameters'] ? " et avec les paramètres : " . $this->obfuscatePassword(json_encode($options['parameters'])) : '') . " n'a pu aboutir. \nErreur " . ($error ?? $e->getMessage()), $statusCode, 1, __FILE__, __LINE__, $e);
                    $this->dispatcher->dispatch(new MonitoringEvents($j), MonitoringEvents::API_PROVIDER_ERROR);
                }
                throw $i;
            }
        }
        //retry if we get an empty reponse
        if ($options['retryOn200Null'] && $result === null && $statusCode == 200 && $this->canRetryRequest($options)) {
            return $this->retryRequest($url, $options);
        } else {
            return $result;
        }
    }

    protected function isHttpContext(): bool
    {
        return $this->requestStack->getCurrentRequest() !== null; // Returns true if on main, returns false if in worker
    }

    /**
     * @param $url
     * @param $options
     * @return array|null
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    private function retryRequest($url, &$options): ?array
    {
        $options['_retry']++;
        $this->logger->warning("[sendRequest] An error occured when requesting " . $this->dataProvider . " $url using " . $options['method'] . " with params : " . ($options['parameters'] ? " et avec les paramètres : " . print_r($options['parameters'], true) : '') . " retrying... " . $options['_retry'] . "/" . $options['maxRetry']);
        return $this->sendRequest($url, $options);
    }

    /**
     * @param array $options
     * @return bool
     */
    private function canRetryRequest(array $options): bool
    {
        return ($options['forceRetry'] || !in_array($options['method'], ['POST', 'PUT'])) && $options['_retry'] < $options['maxRetry'];
    }

    /**
     * @param Connection $connection
     */
    private function checkRateLimit(Connection $connection): void
    {
        if ($this->rateLimiter) {
            $limiter = $this->rateLimiter->create($connection->getId());
            $rateLimit = $limiter->consume();
            if (false === $rateLimit->isAccepted()) {
                $wait = $rateLimit->getRetryAfter()->format('U.u') - microtime(true);
                if ($wait <= 0) {
                    return;
                }
                $this->logger->info('[RATE_LIMIT][' . $connection->getDataProvider() . '] limit reached - wait ' . $wait . 'sec');
                $wait = (int)($wait * 1e6);
                usleep($wait);
            }
        }
    }

    /**
     * @param string $message
     * @return string
     */
    private function obfuscatePassword(string $message): string
    {
        return preg_replace('/"password":".*",/', '"password":"*****",', $message);
    }
}
