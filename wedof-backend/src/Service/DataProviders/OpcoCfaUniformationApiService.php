<?php

namespace App\Service\DataProviders;

use App\Entity\Organism;
use App\Library\utils\enums\DataProviders;
use App\Repository\EndPointStatusRepository;
use App\Repository\UserRepository;
use App\Service\ConfigService;
use App\Service\ConnectionService;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Security\Core\Security;

// Swagger recette : https://apicfa.uat.uniformation.fr/swagger/index.html ATTENTION IP restricted + ssl issue
// Swagger prod : https://api.uniformation.fr/swagger/index.html
class OpcoCfaUniformationApiService extends OpcoCfaDockApiService
{
    /**
     * @param ConnectionService $connectionService
     * @param RequestStack $requestStack
     * @param ConfigService $configService
     * @param EventDispatcherInterface $dispatcher
     * @param LoggerInterface $logger
     * @param EndPointStatusRepository $endPointStatusRepository
     * @param Security $security
     * @param UserRepository $userRepository
     */
    public function __construct(ConnectionService        $connectionService,
                                RequestStack             $requestStack,
                                ConfigService            $configService,
                                EventDispatcherInterface $dispatcher,
                                LoggerInterface          $logger,
                                EndPointStatusRepository $endPointStatusRepository,
                                Security                 $security,
                                UserRepository $userRepository
    )
    {
        parent::__construct(DataProviders::OPCO_CFA_UNIFORMATION(), $configService, $connectionService, $requestStack, $endPointStatusRepository, $dispatcher, $logger, $security, $userRepository);
    }

    /**
     * @param Organism $organism
     * @param array|null $params
     * @return array
     */
    public function checkBeforeHabilitate(Organism $organism, array $params = null): array
    {
        return []; // See https://www.cfadock.fr/portail_developpeur#/tabs/implementations "Accrochage CFA key Info"
    }

    // Because https://api.uniformation.fr/api-cfa/v1/status returns 401 even with Bearer (maybe it requires cfa key too?)
    public function status(bool $needsAuth = false): array
    {
        return [
            'status' => self::STATUS_HEALTHY
        ];
    }
}
