<?php

namespace App\Service\DataProviders;

use App\Entity\Certification;
use App\Entity\Organism;
use App\Entity\RegistrationFolder;
use App\Entity\RegistrationFolderReason;
use App\Entity\Session;
use App\Entity\User;
use App\Library\utils\enums\DataProviders;
use DateTime;

interface InterfaceRegistrationFolderApiService
{
    /**
     * @param Organism $organism
     * @param string $registrationFolderId
     * @param array|null $registrationFolderDataToMerge
     * @param string|null $overrideCurrentBillingState
     * @return array|null
     */
    public function getRegistrationFolderRawData(Organism $organism, string $registrationFolderId, array $registrationFolderDataToMerge = null, string $overrideCurrentBillingState = null): ?array;

    /**
     * @param Organism $organism
     * @param array $parameters
     * @param array $options
     * @return array
     */
    public function getRegistrationFoldersRawData(Organism $organism, array $parameters = [], array $options = []): array;

    /**
     * @param RegistrationFolder $registrationFolder
     * @param array $data
     * @return RegistrationFolder
     */
    public function validateRegistrationFolder(RegistrationFolder $registrationFolder, array $data = []): RegistrationFolder;


    /**
     * @param RegistrationFolder $registrationFolder
     * @return RegistrationFolder
     */
    public function acceptRegistrationFolder(RegistrationFolder $registrationFolder): RegistrationFolder;

    /**
     * @param RegistrationFolder $registrationFolder
     * @return RegistrationFolder
     */
    public function rejectRegistrationFolder(RegistrationFolder $registrationFolder): RegistrationFolder;

    /**
     * @param RegistrationFolder $registrationFolder
     * @return RegistrationFolder
     */
    public function attendeeRefuseRegistrationFolder(RegistrationFolder $registrationFolder): RegistrationFolder;

    /**
     * @param RegistrationFolder $registrationFolder
     * @return RegistrationFolder
     */
    public function attendeeCancelRegistrationFolder(RegistrationFolder $registrationFolder): RegistrationFolder;

    /**
     * @param RegistrationFolder $registrationFolder
     * @return RegistrationFolder
     */
    public function waitRegistrationFolder(RegistrationFolder $registrationFolder): RegistrationFolder;

    /**
     * @param RegistrationFolder $registrationFolder
     * @param DateTime $inTrainingDate
     * @return RegistrationFolder
     */
    public function inTrainingRegistrationFolder(RegistrationFolder $registrationFolder, DateTime $inTrainingDate): RegistrationFolder;

    /**
     * @param RegistrationFolder $registrationFolder
     * @param DateTime $terminatedDate
     * @param User|null $user
     * @return RegistrationFolder
     */
    public function terminateRegistrationFolder(RegistrationFolder $registrationFolder, DateTime $terminatedDate, User $user = null): RegistrationFolder;

    /**
     * @param RegistrationFolder $registrationFolder
     * @param float $absenceDuration
     * @param int $completionRate
     * @param bool $forceMajeureAbsence
     * @param RegistrationFolderReason $reason
     * @param User|null $user
     * @return RegistrationFolder
     */
    public function declareServiceDoneRegistrationFolder(RegistrationFolder $registrationFolder, float $absenceDuration, int $completionRate, bool $forceMajeureAbsence, RegistrationFolderReason $reason, User $user = null): RegistrationFolder;

    /**
     * @param RegistrationFolder $registrationFolder
     * @param RegistrationFolderReason $reason
     * @param string $description
     * @return RegistrationFolder
     */
    public function refuseRegistrationFolder(RegistrationFolder $registrationFolder, RegistrationFolderReason $reason, string $description): RegistrationFolder;

    /**
     * @param RegistrationFolder $registrationFolder
     * @param RegistrationFolderReason $reason
     * @param string $description
     * @return RegistrationFolder
     */
    public function cancelRegistrationFolder(RegistrationFolder $registrationFolder, RegistrationFolderReason $reason, string $description): RegistrationFolder;

    /**
     * @param RegistrationFolder $registrationFolder
     * @return RegistrationFolder
     */
    public function refreshRegistrationFolder(RegistrationFolder $registrationFolder): ?RegistrationFolder;

    /**
     * @param String $externalId
     * @param Organism $organism
     * @return RegistrationFolder
     */
    public function retrieveRegistrationFolder(string $externalId, Organism $organism): ?RegistrationFolder;

    /**
     * @param Session $session
     * @param array $rawData
     * @param Organism $organism
     * @return array
     */
    public function preCreateRegistrationFolder(Session $session, array $rawData, Organism $organism): array;

    /**
     * @param array $rawData
     * @param DataProviders $dataProvider
     * @return string
     */
    public function generateRegistrationFolderId(array $rawData, DataProviders $dataProvider): string;

    /**
     * @param RegistrationFolder $registrationFolder
     * @param string $oFBillNumber
     * @param float|null $vatRate
     * @return RegistrationFolder
     */
    public function billRegistrationFolder(RegistrationFolder $registrationFolder, string $oFBillNumber, float $vatRate = null): RegistrationFolder;

    /**
     * @param RegistrationFolder $registrationFolder
     * @return RegistrationFolder
     */
    public function paidRegistrationFolder(RegistrationFolder $registrationFolder): RegistrationFolder;

    /**
     * @param RegistrationFolder $registrationFolder
     * @param array $data
     * @return RegistrationFolder|null
     */
    public function updateRegistrationFolder(RegistrationFolder $registrationFolder, array $data): RegistrationFolder;

    /**
     * @param Organism $organism
     * @param $state
     * @param $sort
     * @param array $options
     * @return int
     */
    public function getRegistrationFoldersCount(Organism $organism, $state = null, $sort = null, array $options = array()): int;

    /**
     * @param RegistrationFolder $registrationFolder
     * @return string
     */
    public function getRegistrationFolderExternalLink(RegistrationFolder $registrationFolder): string;

    /**
     * @return array
     */
    public function getActionsRulesForRegistrationFolder(): array;

    /**
     * @param array $registrationFolderRawData
     * @return Certification|null
     */
    public function retrieveCertificationFromRawData(array $registrationFolderRawData): ?Certification;
}