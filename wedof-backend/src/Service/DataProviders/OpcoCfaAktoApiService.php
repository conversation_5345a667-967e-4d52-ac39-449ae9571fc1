<?php

namespace App\Service\DataProviders;

use App\Library\utils\enums\DataProviders;
use App\Repository\EndPointStatusRepository;
use App\Repository\UserRepository;
use App\Service\ConfigService;
use App\Service\ConnectionService;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Security\Core\Security;

// Swagger recette : https://cfa-ws-rec.akto.fr/SorApiEchangeCFA/index.html
// Swagger prod : https://cfa-ws.akto.fr/SorApiEchangeCFA/index.html
class OpcoCfaAktoApiService extends OpcoCfaDockApiService
{
    /**
     * @param ConnectionService $connectionService
     * @param RequestStack $requestStack
     * @param ConfigService $configService
     * @param EventDispatcherInterface $dispatcher
     * @param LoggerInterface $logger
     * @param EndPointStatusRepository $endPointStatusRepository
     * @param Security $security
     * @param UserRepository $userRepository
     */
    public function __construct(ConnectionService        $connectionService,
                                RequestStack             $requestStack,
                                ConfigService            $configService,
                                EventDispatcherInterface $dispatcher,
                                LoggerInterface          $logger,
                                EndPointStatusRepository $endPointStatusRepository,
                                Security                 $security,
                                UserRepository $userRepository
    )
    {
        parent::__construct(DataProviders::OPCO_CFA_AKTO(), $configService, $connectionService, $requestStack, $endPointStatusRepository, $dispatcher, $logger, $security, $userRepository);
    }
}
