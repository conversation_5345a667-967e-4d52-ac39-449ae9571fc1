<?php

namespace App\Service\DataProviders;

use App\Entity\Activity;
use App\Entity\Certification;
use App\Entity\CertificationPartner;
use App\Entity\Connection;
use App\Entity\Organism;
use App\Exception\WedofAccessDeniedHttpException;
use App\Exception\WedofConnectionException;
use App\Library\utils\enums\CertificationPartnerHabilitation;
use App\Library\utils\enums\CertificationPartnerStates;
use App\Library\utils\enums\CertificationSkillType;
use App\Library\utils\enums\CertificationTypes;
use App\Library\utils\enums\ConnectionStates;
use App\Library\utils\enums\ConnectionTypes;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\Tools;
use App\Message\CreateCertification;
use App\Message\SynchronizeCertificationPartners;
use App\Repository\EndPointStatusRepository;
use App\Service\ActivityService;
use App\Service\CertificationPartnerService;
use App\Service\CertificationService;
use App\Service\ConfigService;
use App\Service\ConnectionService;
use App\Service\OrganismService;
use DateTime;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use ErrorException;
use Exception;
use Firebase\JWT\JWT;
use LogicException;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx as XlsxReader;
use PhpOffice\PhpSpreadsheet\Writer\Xls;
use Psr\Log\LoggerInterface;
use simplehtmldom\HtmlDocument;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Security\Core\Security;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Throwable;
use ZipArchive;

class FranceCompetencesApiService extends BaseApiService implements InterfaceCertificationApiService
{

    const MAX_ATTEMPTS_SYNCHRONIZE_PARTNERS = 3;

    private CertificationService $certificationService;
    private OrganismService $organismService;
    private CertificationPartnerService $certificationPartnerService;
    private MessageBusInterface $messageBus;

    private string $projectDir;
    private ?array $franceCompetencesDataRS = null;
    private ?array $franceCompetencesDataRNCP = null;
    private ActivityService $activityService;

    /**
     * FranceCompetencesApiService constructor.
     * @param ConfigService $configService
     * @param RequestStack $requestStack
     * @param EventDispatcherInterface $dispatcher
     * @param LoggerInterface $logger
     * @param EndPointStatusRepository $endPointStatusRepository
     * @param ConnectionService $connectionService
     * @param OrganismService $organismService
     * @param CertificationPartnerService $certificationPartnerService
     * @param CertificationService $certificationService
     * @param Security $security
     * @param MessageBusInterface $messageBus
     * @param string $projectDir
     * @param ActivityService $activityService
     */
    public function __construct(ConfigService               $configService,
                                RequestStack                $requestStack,
                                EventDispatcherInterface    $dispatcher,
                                LoggerInterface             $logger,
                                EndPointStatusRepository    $endPointStatusRepository,
                                ConnectionService           $connectionService,
                                OrganismService             $organismService,
                                CertificationPartnerService $certificationPartnerService,
                                CertificationService        $certificationService,
                                Security                    $security,
                                MessageBusInterface         $messageBus,
                                string                      $projectDir,
                                ActivityService $activityService)
    {
        parent::__construct(DataProviders::FRANCE_COMPETENCES(), $configService, $connectionService, $requestStack, $endPointStatusRepository, $dispatcher, $logger, $security);
        $this->certificationService = $certificationService;
        $this->organismService = $organismService;
        $this->certificationPartnerService = $certificationPartnerService;
        $this->messageBus = $messageBus;
        $this->projectDir = $projectDir;
        $this->activityService = $activityService;
    }

    /**
     * @param array $options
     * @return array
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    public function setAuthentication(array $options = []): array
    {
        if ($options['organism']) {
            /** @var Organism $organism */
            $organism = $options['organism'];
            $connection = $organism->getConnectionForDataProvider($this->dataProvider);
            $credentials = $connection->getCredentials();
            if (isset($credentials['token'])) {
                $token = $credentials['token'];
            } else {
                // Fallback to full auth
                // Not sure it is needed
                $authData = $this->getAuth($connection); // TODO Here we don't checkOrganism, why ? It would trigger 2 more requests (getUserInfos) so maybe performance reasons
                $token = $authData['token'] ?? null;
            }
            if (!$token) {
                throw new WedofAccessDeniedHttpException();
            }
            $options = array_merge($options, $this->getRequestOptionsFromToken($token));
        }
        return $options;
    }

    /**
     * @param Connection $connection
     * @param bool $checkOrganismAccess
     * @return array
     * @throws ORMException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws OptimisticLockException
     * @throws ErrorException
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     * @throws Throwable
     */
    public function authenticate(Connection $connection, bool $checkOrganismAccess = true): array
    {
        $authData = $this->getAuth($connection);
        if (!empty($authData['token'])) {
            $token = $authData['token'];
            $userId = $authData['user']['id'] ?? null;
            $userInfos = $this->getUserInfos($token, $userId);
            $credentials = $connection->getCredentials();
            $credentials['token'] = $token;
            if ($userId) {
                $credentials['userId'] = $userId; // Not used at the moment but could prevent unecessary FC requests
            }
            $connection->setCredentials($credentials);
            $this->connectionService->save($connection);
            if ($checkOrganismAccess) {
                $hasOrganismAccess = $this->checkOrganismAccess($connection->getOrganism(), $userInfos);
            } else {
                $hasOrganismAccess = true;
            }
            if ($hasOrganismAccess) {
                $lastRefresh = new DateTime();
                $expiresOn = clone $lastRefresh;
                $refreshAt = clone $lastRefresh;
                $decoded = JWT::jsonDecode(JWT::urlsafeB64Decode(explode('.', $token)[1])); //extract jwt data from token
                $expiresOn->setTimestamp($decoded->exp);
                $refreshAt->setTimestamp($decoded->exp)->modify("- 3600 seconds");
                return [
                    "hasAccess" => true,
                    "hasOrganismAccess" => true,
                    "lastRefresh" => $lastRefresh,
                    "refreshAt" => $refreshAt,
                    "expiresOn" => $expiresOn
                ];
            }
            return [
                "hasAccess" => true,
                "hasOrganismAccess" => false
            ];
        }
        return [
            "hasAccess" => false,
            "hasOrganismAccess" => false
        ];
    }

    public function getUsername(Connection $connection): string
    {
        return $connection->getCredentials()['username'] ?? '';
    }

    /**
     * @return bool
     */
    public function requiresAuthentication(): bool
    {
        return true;
    }

    /**
     * @param Organism $organism
     * @return bool
     */
    public function setActiveOrganism(Organism $organism): bool
    {
        // TODO check if it makes sense to do that here
        // I (Nicolas) feat that sendRequests call setActiveOrganisme that calls getUserInfos that calls sendRequest... etc.
//        $connection = $organism->getConnectionForDataProvider($this->dataProvider);
//        $token = $connection->getCredentials()['token'];
//        if ($connection->getCredentials() && $token) {
//            $userInfos = $this->getUserInfos($token);
//            return $this->checkOrganismAccess($organism, $userInfos);
//        }
        return false;
    }

    public function getMaxAttemptsBeforeStop(): int
    {
        return 3;
    }

    /**
     * @param Certification $certification
     * @param Organism|null $certifier
     * @param bool $handleCertifProErrors
     * @return bool
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
     */
    public function updateCertificationPartners(Certification $certification, Organism $certifier = null, bool $handleCertifProErrors = true): bool
    {
        $certifier = $certifier ?? ($this->organismService->getCertifierWithActiveConnectionForCertification($certification) ?? $certification->getDefaultCertifier());
        $file = $this->generateXlsPartners($certification);
        $options = [
            'cat_api_status' => "upload-xlsx-file",
            'organism' => $certifier, // setting organism will trigger a setAuthentication
            'files' => [
                "file" => $file
            ]
        ];
        $this->logger->debug("[CertifPro] " . $certification->getExternalId() . " - Partners list: " . $file);
        $result = $this->sendRequest(Tools::getEnvValue("FRANCECOMPETENCES_BASE_URI") . "dossiers/{$certification->getIdFiche()}/partenaires-excel/true", $options);
        unlink($file);
        $success = empty($result);

        if (!$success) {
            $this->logger->debug("[CertifPro] " . $certification->getExternalId() . " - Error result: " . print_r($result, true));
            if ($handleCertifProErrors) {
                for ($i = 0; $i < count($result); $i++) {
                    if ($result[$i]['type'] === 'LIGNE_ERREUR_SIRET_CESSE') {
                        $partner = $this->organismService->getOrganism(["siret" => $result[$i]['siret']]);
                        $certificationPartner = $this->certificationPartnerService->getByCertificationAndPartner($certification, $partner, false);
                        $this->logger->info("[CertifPro][" . $certification->getCertifInfo() . "] revoke partnership - établissement fermé");
                        if ($certificationPartner->getState() === CertificationPartnerStates::ACTIVE()->getValue()) {
                            $this->certificationPartnerService->revoke($certificationPartner, ['cause' => 'siretClosed']);
                        } else {
                            $this->certificationPartnerService->update($certificationPartner, ['pendingActivation' => false]); // If we are trying to activate a closed SIRET
                        }
                    } else {
                        throw new LogicException("Erreur CertifPro inconnue : " . json_encode($result[$i]));
                    }
                }
                $success = $this->updateCertificationPartners($certification, $certifier, false);
            } else {
                throw new Exception("[CertifPro] Still unhandled errors while trying to update certification partners " . json_encode($result));
            }
        }

        return $success;
    }

    /**
     * @param Organism $organism
     * @param Connection $connection
     * @param array $params
     * @return bool
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    public function habilitate(Organism $organism, Connection $connection, array $params): bool
    {
        $payload = [
            'siret' => $organism->getSiret(),
            'email' => $params['username'],
            'password' => $params['password'],
            'organismName' => $organism->getName(),
            "connectionId" => $connection->getId()
        ];

        $options = [
            'fullResponse' => true,
            'parameters' => $payload,
            'method' => 'POST',
            'headers' => [
                Tools::getEnvValue('AUTOMATOR_AUTH_HEADER') => Tools::getEnvValue('AUTOMATOR_AUTH')
            ]
        ];

        $response = $this->sendRequest('https://automator.wedof.fr/webhook/create-habilitation-fc', $options);
        if ($response && $response['statusCode'] == 200) {
            $connection->setState(ConnectionStates::IN_PROGRESS());
            $connection->setMessage('En cours de création');
            $connection->setMessageType('info');
            $connection->setIsTosValidated(false);
            $connection->setType(ConnectionTypes::HABILITATION());
            $this->connectionService->save($connection);
            return true;
        } else {
            return false;
        }
    }

    /**
     * @param Organism $organism
     * @param array|null $params
     * @return string|string[]|true
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    public function checkBeforeHabilitate(Organism $organism, array $params = null)
    {
        if (isset($params['mfa'])) {
            $preAuthData = $this->getPreAuthMfa($params, $params['mfa']);
        } else {
            $preAuthData = $this->getPreAuth($params);
        }
        /** errors possible before login **/
        if ($preAuthData === null) {
            return ["error" => "other"];
        }
        if (isset($preAuthData['error'])) {
            return ["error" => strtolower($preAuthData['error']), "value1" => $params['username']];
        }
        if (isset($preAuthData['compteBloque']) && $preAuthData['compteBloque'] === true) {
            return ["error" => "locked", "value1" => $params['username']];

        }
        if (isset($preAuthData['mfaRequired']) && $preAuthData['mfaRequired'] === true) {
            return "mfa";
        }
        /** errors possible after login **/
        $authData = $this->getAuthLogin($params);
        if (!isset($authData['user']['habilitationDepot'])) {
            return ["error" => "habilitation"];
        }
        $sirets = array_column($authData['user']['habilitationDepot'], 'siret');
        $key = array_search($organism->getSiret(), $sirets);
        if ($key === false) {
            return ["error" => "siret", "value1" => $organism->getSiret()];
        }
        if (!$authData['user']['habilitationDepot'][$key]['isSiretOpen']) {
            return ["error" => "siret.closed", "value1" => $organism->getSiret()];
        }
        if (!$authData['user']['habilitationDepot'][$key]['isGestionnaireEntite']) {
            return ["error" => "gestionnaire", "value1" => $organism->getSiret()];
        }
        return true;
    }

    /**
     * @param string $certifInfo
     * @return array
     */
    public function getCertifiersDataForUpdate(string $certifInfo): array
    {
        $franceCompetencesData = $this->getDataByCertifInfo($certifInfo);
        $certifierDataForUpdate = [];

        if (!empty($franceCompetencesData['CERTIFICATEURS'])) {
            if (!empty($franceCompetencesData['CERTIFICATEURS']['CERTIFICATEUR']['SIRET_CERTIFICATEUR'])) { // unique certificateur
                $certifierDataForUpdate[] = $franceCompetencesData['CERTIFICATEURS']['CERTIFICATEUR']['SIRET_CERTIFICATEUR'];
            } else {
                foreach ($franceCompetencesData['CERTIFICATEURS']['CERTIFICATEUR'] as $certifierRawData) { // plusieurs certificateurs
                    if (isset($certifierRawData['SIRET_CERTIFICATEUR'])) {
                        $certifierDataForUpdate[] = $certifierRawData['SIRET_CERTIFICATEUR'];
                    }
                }
            }
        }

        return $certifierDataForUpdate;
    }

    /**
     * @param string $certifInfo
     * @return array
     */
    public function getCertificationDataForUpdate(string $certifInfo): array
    {
        $certification = $this->certificationService->getByCertifInfo($certifInfo);
        $updateData = [];

        $franceCompetencesData = $this->getDataByCertifInfo($certifInfo);

        if ($certification) {
            if (!empty($franceCompetencesData['DATE_FIN_ENREGISTREMENT'])) {
                if (empty($certification->getCpfDateEnd()) || $franceCompetencesData['DATE_FIN_ENREGISTREMENT'] != $certification->getCpfDateEnd()->format('d/m/Y')) {
                    $updateData['cpf']['dateEnd'] = DateTime::createFromFormat('Y-m-d', date('Y-m-d', strtotime(str_replace('/', '-', $franceCompetencesData['DATE_FIN_ENREGISTREMENT']))));
                }
            }
            if (!empty($franceCompetencesData['ACTIF'])) {
                $enabled = $franceCompetencesData['ACTIF'] === 'Oui';
                if ($certification->getEnabled() != $enabled) {
                    $updateData['enabled'] = $enabled;
                }
            }
            if (empty($certification->getIdFiche())) {
                $updateData['idFiche'] = $franceCompetencesData["ID_FICHE"];
            }
        }

        if (!empty($updateData)) {
            $updateData['certifInfo'] = $certifInfo;
        }

        return $updateData;
    }

    /**
     * @param Certification $certification
     * @param Organism|null $certifier
     * @param int|null $retryCounter
     * @return void
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
     * @throws \Symfony\Component\Mailer\Exception\TransportExceptionInterface
     */
    public function synchronizeCertificationPartners(Certification $certification, Organism $certifier = null, ?int $retryCounter = null): void
    {
        $this->updatePartnersForCertification($certification);
        if ($this->updateCertificationPartners($certification, $certifier)) {
            $this->updatePartnersForCertification($certification);
            if ($this->certificationService->hasCertificationPartnersToUpdate($certification)) {
                $dataProvider = DataProviders::from($certification->getDataProvider());
                if ($this->connectionService->hasAccess($certifier, $dataProvider, true) && $retryCounter && $retryCounter < self::MAX_ATTEMPTS_SYNCHRONIZE_PARTNERS) {
                    $this->messageBus->dispatch(new SynchronizeCertificationPartners($certification->getCertifInfo(), $certifier->getSiret(), $dataProvider, $retryCounter + 1));
                } else {
                    throw new Exception("Erreur, il y a toujours des pending après l'update");
                }
            }
        } else {
            throw new Exception("Erreur, retournée par France Compétences");
        }
    }

    /**
     * @param Certification $certification
     * @return void
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function updatePartnersForCertification(Certification $certification): void
    {
        $this->logger->info("[checkPartners][" . $certification->getCertifInfo() . "] checking for partners");
        if (!$certification->getIdFiche()) {
            $this->logger->info("[checkPartners][" . $certification->getCertifInfo() . "] no id fiche can't check for partners");
        } else {
            $arrContextOptions = null;
            if (in_array($_ENV['APP_ENV'], ['dev'])) {
                $arrContextOptions = array(
                    "ssl" => array(
                        "verify_peer" => false,
                        "verify_peer_name" => false,
                    ),
                );
            }
            // on crée un fichier temporaire parce que le XlsxReader s'appuie sur un fichier pour fonctionner
            $franceCompetenceTempFile = sys_get_temp_dir() . '/tempfile' . $certification->getCertifInfo() . '_' . time() . '.xlsx';
            file_put_contents($franceCompetenceTempFile, file_get_contents('https://certifpro.francecompetences.fr/webapp/services/edition/exportPartenaireSp/' . $certification->getIdFiche() . '/true', false, stream_context_create($arrContextOptions)));

            $reader = new XlsxReader();
            $reader->setReadDataOnly(true);
            $spreadsheet = $reader->load($franceCompetenceTempFile);
            $worksheet = $spreadsheet->getActiveSheet();
            $startRow = 2;
            $endRow = $worksheet->getHighestDataRow('B');

            $franceCompetencesPartners = [];
            for ($row = $startRow; $row <= $endRow; $row++) {
                $siretCell = $worksheet->getCell('B' . $row);
                $roleCell = $worksheet->getCell('C' . $row);
                if ($siretCell->getValue()) { //Something it can be null in that shitty file from FC
                    $franceCompetencesPartners[] = $siretCell->getValue();

                    switch ($roleCell) {
                        case CertificationPartnerHabilitation::toFrString(CertificationPartnerHabilitation::EVALUATE()->getValue()):
                            $habilitations[$siretCell->getValue()] = CertificationPartnerHabilitation::EVALUATE();
                            break;
                        case CertificationPartnerHabilitation::toFrString(CertificationPartnerHabilitation::TRAIN()->getValue()):
                            $habilitations[$siretCell->getValue()] = CertificationPartnerHabilitation::TRAIN();
                            break;
                        case CertificationPartnerHabilitation::toFrString(CertificationPartnerHabilitation::TRAIN_EVALUATE()->getValue()):
                            $habilitations[$siretCell->getValue()] = CertificationPartnerHabilitation::TRAIN_EVALUATE();
                            break;
                        default:
                            throw new LogicException('Role de partenaire inconnu !');
                    }
                }
            }
            // suppression du fichier temporaire
            unlink($franceCompetenceTempFile);

            $knownActivePartners = $this->certificationPartnerService->getPartnersSiret($certification, [CertificationPartnerStates::ACTIVE()->getValue()]);
            $knownNonActivePartners = $this->certificationPartnerService->getPartnersSiret($certification, array_diff(CertificationPartnerStates::keys(), [CertificationPartnerStates::ACTIVE()->getKey()]));
            $newPartners = array_diff($franceCompetencesPartners, [...$knownActivePartners, ...$knownNonActivePartners]);
            $partnersToActive = array_diff($franceCompetencesPartners, [...$knownActivePartners, ...$newPartners]);
            $formerPartners = array_diff($knownActivePartners, $franceCompetencesPartners);
            $partnersToUpdate = array_intersect($franceCompetencesPartners, $knownActivePartners);

            foreach ($newPartners as $newPartner) {
                $partner = $this->organismService->getOrganism(["siret" => $newPartner]);
                if ($partner) {
                    $this->logger->info("[checkPartners][" . $certification->getCertifInfo() . "] create and activate partnership");
                    $certifier = $this->certificationService->getDefaultCertifierFromCoCertifiers($certification);
                    $certificationPartner = $this->certificationPartnerService->create($certification, $partner, null, $certifier);
                    $habilitation = $habilitations[$newPartner];
                    $this->certificationPartnerService->activate($certificationPartner, $habilitation);
                    if (!$partner->isTrainingOrganism()) {
                        $this->organismService->markAsTrainingOrganism($partner);
                    }
                }
            }

            foreach ($partnersToActive as $partnerToActive) {
                $partner = $this->organismService->getOrganism(["siret" => $partnerToActive]);
                $certificationPartner = $this->certificationPartnerService->getByCertificationAndPartner($certification, $partner, false);
                if ($certificationPartner) {
                    $this->logger->info("[checkPartners][" . $certification->getCertifInfo() . "] activate partnership");
                    $habilitation = $habilitations[$partnerToActive];
                    $this->certificationPartnerService->activate($certificationPartner, $habilitation);
                    if (!$partner->isTrainingOrganism()) {
                        $this->organismService->markAsTrainingOrganism($partner);
                    }
                }
            }

            foreach ($formerPartners as $formerPartner) {
                $organism = $this->organismService->getOrganism(["siret" => $formerPartner]);
                $certificationPartner = $this->certificationPartnerService->getByCertificationAndPartner($certification, $organism);
                if ($certificationPartner) {
                    if ($certificationPartner->isPendingSuspension()) {
                        $this->logger->info("[checkPartners][" . $certification->getCertifInfo() . "] suspend partnership");
                        $parameters = [];
                        /** @var Activity $lastUpdateActivity */
                        $lastUpdateActivity = $this->activityService->listByEntity(CertificationPartner::CLASSNAME,
                            $certificationPartner->getEntityId(),
                            ['criterias' => ['type' => 'update'], 'orderBy' => ['eventEndTime' => 'DESC'], 'limit' => 1])[0];
                        if ($lastUpdateActivity->getField() === 'pendingSuspension' && $lastUpdateActivity->getOrigin() === 'audit' && $lastUpdateActivity->getNewValue() === '1') { // valeur booléenne stockée comme string en BDD
                            $parameters = ['origin' => 'audit'];
                        }
                        $this->certificationPartnerService->suspend($certificationPartner, $parameters);
                    } else {
                        $this->logger->info("[checkPartners][" . $certification->getCertifInfo() . "] revoke partnership");
                        $this->certificationPartnerService->revoke($certificationPartner);
                    }
                }
            }

            foreach ($partnersToUpdate as $partnerToUpdate) {
                $partner = $this->organismService->getOrganism(["siret" => $partnerToUpdate]);
                $certificationPartner = $this->certificationPartnerService->getByCertificationAndPartner($certification, $partner);
                if ($certificationPartner) {
                    $habilitation = $habilitations[$partnerToUpdate];
                    if ($habilitation != $certificationPartner->getHabilitation()) {
                        $this->logger->info("[checkPartners][" . $certification->getCertifInfo() . "] update partnership habilitation");
                        $this->certificationPartnerService->update($certificationPartner, ['habilitation' => $habilitation]);
                    }
                }
            }

            $this->logger->info("[checkPartners][" . $certification->getCertifInfo() . "] Updated certification");
        }
    }

    /**
     * @param CertificationTypes $type
     * @throws Throwable
     */
    public function addNewCertifications(CertificationTypes $type): void
    {
        $this->logger->debug("addNewCertificationsFromFranceCompetencesFile");
        $certificationRawData = $this->getAllDataForType($type);
        $certifInfosList = $this->getCertifInfoList();

        foreach ($certificationRawData as $fiche) {
            $code = str_replace($type->getValue(), '', $fiche['NUMERO_FICHE']);
            $certification = $this->certificationService->getByTypeAndCode($type, $code);
            $enabled = $fiche['ACTIF'] == 'Oui';
            if (is_null($certification)) {
                $this->logger->debug('certification inconnue ' . $fiche['NUMERO_FICHE']);
                try {
                    $franceCompetenceData = $this->getSimplifiedRawData($fiche, $type);
                    $needle = $fiche['INTITULE'];
                    $key = array_key_last(array_filter(array_column($certifInfosList, 'title'), function ($haystack) use ($needle) {
                        return (str_contains($haystack, $needle));
                    }));
                    $certifInfo = $key ? $certifInfosList[$key]['certifInfo'] : null;
                    if (!$certifInfo || $this->certificationService->getByCertifInfo($certifInfo) !== null) {
                        $certifInfo = uniqid("tmp-");
                    }
                    $this->messageBus->dispatch(new CreateCertification($certifInfo, $franceCompetenceData));
                    $this->logger->debug('message envoyé pour ' . $fiche['NUMERO_FICHE']);
                } catch (Exception $e) {
                    $this->logger->error($e->getMessage());
                }
                //state actif can change so we need to update it before selecting only active certifications...
            } else if ($certification && $certification->getEnabled() != $enabled) {
                $franceCompetenceData = $this->getSimplifiedRawData($fiche, $type);
                $this->messageBus->dispatch(new CreateCertification($certification->getCertifInfo(), $franceCompetenceData));
            }
        }
    }

    /**
     * @param CertificationTypes|null $type
     */
    public function refreshDataFiles(CertificationTypes $type = null)
    {
        $contextOptions = stream_context_create(array(
            'ssl' => array(
                'verify_peer' => true,
                'cafile' => $this->projectDir . "/data/cacert-bundle.pem"
            )
        ));

        $this->logger->info("[refreshFranceCompetencesFiles] started");
        $data = json_decode(file_get_contents("https://data.gouv.fr/datasets/5eebbc067a14b6fecc9c9976/rdf.json", false, $contextOptions), true);

        $data_rs = array_filter($data['@graph'], function ($arr2) {
            return isset($arr2['title']) && Tools::startsWith($arr2['title'], "export-fiches-rs") && $arr2['format'] == 'zip';
        });
        $data_rncp = array_filter($data['@graph'], function ($arr2) {
            return isset($arr2['title']) && Tools::startsWith($arr2['title'], "export-fiches-rncp") && $arr2['format'] == 'zip';
        });


        // RS
        if (!$type || $type->getValue() === CertificationTypes::RS()->getValue()) {
            usort($data_rs, [Tools::class, 'sortArrayByDate']);
            if (!file_exists(__DIR__ . "/../../../data/" . $data_rs[0]['title'])) {
                $this->logger->info("[refreshFranceCompetencesFiles] grab last file RS");
                foreach (glob($this->projectDir . "/data/export-fiches-rs*") as $file) { // suppression de l'ancien fichier
                    unlink($file);
                }
                file_put_contents($this->projectDir . "/data/" . $data_rs[0]['title'], file_get_contents($data_rs[0]['accessURL'], false, $contextOptions));
                $zip = new ZipArchive();
                if ($zip->open($this->projectDir . "/data/" . $data_rs[0]['title']) === TRUE) {
                    $zip->extractTo($this->projectDir . "/data/export_rs/");
                    rename($this->projectDir . "/data/export_rs/" . $zip->getNameIndex(0), $this->projectDir . "/data/export_rs.xml");
                    $zip->close();
                    rmdir($this->projectDir . "/data/export_rs/");
                    $this->resetFranceCompetencesDataRS();
                }
            } else {
                $this->logger->info("[refreshFranceCompetencesFiles] no need to grab RS file, no update");
            }
        }

        // RNCP
        if (!$type || $type->getValue() === CertificationTypes::RNCP()->getValue()) {
            usort($data_rncp, [Tools::class, 'sortArrayByDate']);
            if (!file_exists(__DIR__ . "/../../../data/" . $data_rncp[0]['title'])) {
                $this->logger->info("[refreshFranceCompetencesFiles] grab last file RNCP");
                foreach (glob($this->projectDir . "/data/export-fiches-rncp*") as $file) { // suppression de l'ancien fichier
                    unlink($file);
                }
                file_put_contents($this->projectDir . "/data/" . $data_rncp[0]['title'], file_get_contents($data_rncp[0]['accessURL'], false, $contextOptions));
                $zip = new ZipArchive();
                if ($zip->open($this->projectDir . "/data/" . $data_rncp[0]['title']) === TRUE) {
                    $zip->extractTo($this->projectDir . "/data/export_rncp/");
                    rename($this->projectDir . "/data/export_rncp/" . $zip->getNameIndex(0), $this->projectDir . "/data/export_rncp.xml");
                    $zip->close();
                    rmdir($this->projectDir . "/data/export_rncp/");
                    $this->resetFranceCompetencesDataRNCP();
                }
            } else {
                $this->logger->info("[refreshFranceCompetencesFiles] no need to grab RNCP file, no update");
            }
        }
    }

    /**
     * @param string $certifInfo
     * @param array $franceCompetencesData
     * @return array|null
     */
    public function getCertificationRawData(string $certifInfo, array $franceCompetencesData = []): ?array
    {
        $certificationRawData = ["certifInfo" => $certifInfo, 'dataProvider' => DataProviders::FRANCE_COMPETENCES()];
        $certificationRawData = $this->mergeWithFranceCompetencesData($franceCompetencesData, $certificationRawData);
        try {
            $this->logger->info("[getCertificationData][$certifInfo] Start grabbing data");
            $content = $this->organismService->sendCarifRequest("GET", "/formations/certification-$certifInfo.html");
            $html = new HtmlDocument();
            $html->load($content);
            //no data on carif giving up
            if (empty($html->find('h1.pb-2', 0))) {
                return $certificationRawData;
            }
            $certificationRawData['name'] = $html->find('h1.pb-2', 0)->plaintext;
            foreach ($html->find('.container.mt-3.mb-3 > .row > .pt-2.pb-4.col-sm-9 > .row') as $data) {
                $title = "";
                if (sizeof($data->find('.pt-2.col-sm-3.border-right'))) {
                    $title = $data->find('.pt-2.col-sm-3.border-right', 0)->plaintext;
                }
                if ($title == "Niveau de qualification") {
                    $certificationRawData['level'] = $data->find(".pt-2.col-sm-9", 0)->plaintext;
                } else if ($title == "Descriptif") {
                    $certificationRawData['descriptif'] = $data->find(".pt-2.col-sm-9.text-justify", 0)->plaintext;
                } else if ($title == "Objectif") {
                    $certificationRawData['objectif'] = $data->find(".pt-2.col-sm-9.text-justify", 0)->innertext;
                } else if ($title == "RNCP") {
                    $link = $data->find(".pt-2.col-sm-9.text-justify a", 0);
                    if ($link) {
                        $certificationRawData['idInfos'] = [
                            "type" => CertificationTypes::RNCP(),
                            "code" => str_replace("https://www.francecompetences.fr/recherche/rncp/", "", $link->href),
                            "link" => $link->href
                        ];
                        $this->logger->info("[getCertificationData][$certifInfo] is RNCP");
                    }
                } else if ($title == "Répertoire spécifique") {
                    $this->logger->info("[getCertificationData][$certifInfo] is RS");
                    $link = $data->find(".pt-2.col-sm-9.text-justify a", 0);
                    $certificationRawData['idInfos'] = [
                        "type" => CertificationTypes::RS(),
                        "code" => str_replace("https://www.francecompetences.fr/recherche/rs/", "", $link->href),
                        "link" => $link->href
                    ];
                } else if ($title == "Pour en savoir plus") {
                    $certificationRawData['link'] = $data->find(".pt-2.col-sm-9.text-justify > a", 0)->plaintext;
                } else if ($title == "Domaine(s) de formation") {
                    $domains = [];
                    foreach ($data->find(".pt-2.col-sm-9.text-justify > ul.m-0.p-0 > li") as $domain) {
                        $domain = explode(" : ", $domain->innertext);
                        $domains[] = [
                            "code" => $domain[0],
                            "name" => $domain[1]
                        ];
                    }
                    $certificationRawData['domains'] = $domains;
                } else if ($title == "Lien(s) vers les métiers (ROME)") {
                    $romes = [];
                    foreach ($data->find(".pt-2.col-sm-9.text-justify > ul.m-0.p-0 > li > a") as $rome) {
                        $romeText = explode(" : ", $rome->innertext);
                        $romes[] = [
                            "code" => $romeText[0],
                            "link" => $rome->href,
                            "name" => $romeText[1]
                        ];
                    }
                    $certificationRawData['rome'] = $romes;
                } else if ($title == "Groupes formation emploi (GFE)") {
                    $gfes = [];
                    foreach ($data->find(".pt-2.col-sm-9.text-justify > ul.m-0.p-0 > li") as $gfe) {
                        $gfe = explode(" : ", $gfe->innertext);
                        $gfes[] = [
                            "code" => $gfe[0],
                            "name" => $gfe[1]
                        ];
                    }
                    $certificationRawData['gfe'] = $gfes;
                } else if ($title == "Domaine de spécialité (NSF)" && empty($certificationRawData['nsf'])) {
                    $nsf = explode(' : ', $data->find(".pt-2.col-sm-9", 0)->innertext);
                    $certificationRawData['nsf'] = [[
                        "code" => $nsf[0],
                        "name" => $nsf[1]
                    ]];
                } else if ($title == "Eligibilité au Compte Personnel de Formation (CPF)") {
                    $cpf = $data->find("table tr td");
                    $certificationRawData['cpf'] = [[
                        "code" => $cpf[0]->plaintext,
                    ]];
                    if (sizeof($cpf) >= 2 && $cpf[1]->plaintext != "") {
                        $certificationRawData['cpf']['dateStart'] = DateTime::createFromFormat('Y-m-d', date('Y-m-d', strtotime(str_replace('/', '-', $cpf[1]->plaintext))));
                    }
                    if (sizeof($cpf) >= 3 && $cpf[2]->plaintext != "") {
                        $certificationRawData['cpf']['dateEnd'] = DateTime::createFromFormat('Y-m-d', date('Y-m-d', strtotime(str_replace('/', '-', $cpf[2]->plaintext))));
                    }
                }
            }
            $grabFromWeb = false;

            if (!empty($certificationRawData['idInfos'])) {
                $code = $certificationRawData['idInfos']['type']->getValue() . $certificationRawData['idInfos']['code'];
                $data = !empty($franceCompetencesData) ? $franceCompetencesData : array_values(array_filter($this->getAllDataForType($certificationRawData['idInfos']['type']), function ($fiche) use ($code) {
                    return $fiche['NUMERO_FICHE'] == $code;
                }))[0];
                if ($data) {
                    $this->logger->info("[getCertificationData][$certifInfo][" . $certificationRawData['idInfos']['type']->getValue() . "] $code found in local data");
                    $certificationRawData = $this->mergeWithFranceCompetencesData($data, $certificationRawData);
                } else {
                    $this->logger->info("[getCertificationData][$certifInfo][" . $certificationRawData['idInfos']['type']->getValue() . "] $code not found in local data");
                    $grabFromWeb = $certificationRawData['idInfos']['link'];
                }
            }

            if ($grabFromWeb) {
                //merge with data from France Competences
                $this->logger->info("[getCertificationData][$certifInfo] start grabbing data from $grabFromWeb");
                $response = Tools::getHttpClient()->request("GET", $grabFromWeb, ["verify_peer" => false, "verify_host" => false]);
                $html->load($response->getContent());
                //merge nsf
                foreach ($html->find('.code > div > ul > li') as $code) {
                    $code = explode(" : ", $code->innertext);
                    if (!empty($certificationRawData['nsf']) && !in_array($code[0], array_column($certificationRawData['nsf'], 'code'))) {
                        $certificationRawData['nsf'][] = [
                            "code" => trim($code[0]),
                            "name" => trim($code[1])
                        ];
                    }
                }
                $certificationRawData['certifiers'] = [];
                foreach ($html->getElementsById('#collapseOne', 0)->find('.panel-body .table tr') as $line) {
                    $cols = $line->find('td');
                    if (sizeof($cols) > 0) {
                        $certificationRawData['certifiers'][] = [
                            'name' => trim($cols[0]->innertext),
                            'siret' => trim($cols[1]->innertext)
                        ];
                    }
                }
                $this->logger->info("[getCertificationData][$certifInfo] found certifiers: " . sizeof($certificationRawData['certifiers']));
                //get partenaires
                $certificationRawData['partners'] = [];
                foreach ($html->getElementsById('#collapseEight', 0)->find('h5.title') as $title) {
                    if (strpos($title->innertext, 'Organisme(s) préparant')) {
                        $table = $title->next_sibling();
                        foreach ($table->find('tr') as $line) {
                            $cols = $line->find('td');
                            if (sizeof($cols) > 0) {
                                $certificationRawData['partners'][] = [
                                    'name' => trim($cols[0]->innertext),
                                    'role' => $cols[1]->innertext
                                ];
                            }
                        }
                    }
                }
                $this->logger->info("[getCertificationData][$certifInfo] found partners: " . sizeof($certificationRawData['partners']));
                //get validity
                if (sizeof($html->find('.code > div > span')) > 0) {
                    //fucking incorrect html as input
                    $date = $html->find('.code > div > span', 0)->plaintext;
                    $date = preg_replace("/[^A-Za-z0-9 -]/", '', $date);
                    $date = str_replace("br", '', $date);
                    $certificationRawData['cpf']['dateEnd'] = $certificationRawData['cpf']['dateEnd'] ?? DateTime::createFromFormat('Y-m-d', date('Y-m-d', strtotime($date)));
                }
            }
        } catch (Throwable $th) {
            $this->logger->error($th->getMessage());
            $this->logger->error($th->getTraceAsString());
            $this->logger->error("[getCertificationData][$certifInfo] error certification not found or website error)");
            return null;
        }
        return $certificationRawData;
    }

    //----------------
    // METHODES PRIVES
    //----------------

    /**
     * @param Connection|null $connection
     * @return array|null
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    private function getAuth(Connection $connection): ?array
    {
        $credentials = $connection->getDataProvider() === $this->dataProvider->getValue() ? $connection->getCredentials() : null;
        if (!$credentials || !isset($credentials['username'])) {
            throw new ErrorException("A user or France Competences credentials must be provided to use the France Competences API");
        }
        // We don't trigger actual Auth if we know it will not work
        $preAuthData = $this->getPreAuth($credentials);
        if ($preAuthData === null) {
            return null;
        }
        if (isset($preAuthData['error'])) {
            // TODO do something else with $preAuthData['error'] ?
            $this->logger->debug("Erreur FranceCompétences sur la connexion " . $connection->getId() . " " . $preAuthData['error']);
        }
        if (isset($preAuthData['compteBloque']) && $preAuthData['compteBloque'] === true) {
            $this->logger->debug("Erreur FranceCompétences : compteBloque sur la connexion " . $connection->getId());
            return null; // TODO display dedicated message or send email ?
        }
        if (isset($preAuthData['mfaRequired']) && $preAuthData['mfaRequired'] === true) {
            $this->logger->debug("Erreur FranceCompétences : mfaRequired sur la connexion " . $connection->getId());
            return null; // TODO display dedicated message or send email ?
        }
        return $this->getAuthLogin($credentials);
    }

    /**
     * @param array $credentials
     * @return array|null
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    private function getPreAuth(array $credentials): ?array
    {
        $options = [
            "method" => "POST",
            "parameters" => [
                "login" => $credentials["username"],
                "motDePasseCo" => $credentials["password"]
            ],
            "fullResponse" => true,
            "content-type" => "application/json"
        ];
        try {
            $response = $this->sendRequest(Tools::getEnvValue("FRANCECOMPETENCES_PREAUTH_URI"), $options);
            if (isset($response['content'])) {
                return json_decode($response['content'], true);
            }
        } catch (Exception $e) {
            $this->logger->error($e->getMessage());
        }
        return null;
    }

    /**
     * @param array $credentials
     * @param string $mfaCode
     * @return array|null
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    private function getPreAuthMfa(array $credentials, string $mfaCode): ?array
    {
        $options = [
            "method" => "POST",
            "parameters" => [
                "login" => $credentials["username"],
                "motDePasseCo" => $credentials["password"],
                "mfaCode" => $mfaCode
            ],
            "fullResponse" => true,
            "content-type" => "application/json"
        ];
        try {
            $response = $this->sendRequest(Tools::getEnvValue("FRANCECOMPETENCES_AUTH_MFA_URI"), $options);
            if (isset($response['content'])) {
                return json_decode($response['content'], true);
            }
        } catch (Exception $e) {
            $this->logger->error($e->getMessage());
        }
        return null;
    }

    /**
     * @param array $credentials
     * @param string $profile
     * @return array|null
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    private function getAuthLogin(array $credentials, string $profile = "DEPOSANT"): ?array
    {
        $options = [
            "method" => "POST",
            "parameters" => [
                "login" => $credentials["username"],
                "motDePasseCo" => $credentials["password"],
                "profile" => $profile
            ],
            "fullResponse" => true,
            "content-type" => "application/json"
        ];
        try {
            $response = $this->sendRequest(Tools::getEnvValue("FRANCECOMPETENCES_AUTH_URI"), $options);
            if (isset($response['content'])) {
                return json_decode($response['content'], true);
            }
        } catch (Exception $e) {
            $this->logger->error($e->getMessage());
        }
        return null;
    }

    /**
     * @param string $token
     * @param string $id
     * @return array
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    private function getUserInfos(string $token, string $id): array
    {
        $options = $this->getRequestOptionsFromToken($token);
        return $this->sendRequest(Tools::getEnvValue("FRANCECOMPETENCES_BASE_URI") . "comptes/" . $id, $options);
    }

    /**
     * @param Organism|null $getOrganism
     * @param array $userInfos
     * @return bool
     */
    private function checkOrganismAccess(?Organism $getOrganism, array $userInfos): bool
    {
        $hasOrganismAccess = false;
        foreach ($userInfos['habilitations'] as $habilitation) {
            if ($habilitation['entiteSiret'] === $getOrganism->getSiret()) {
                $hasOrganismAccess = true;
            }
        }
        return $hasOrganismAccess;
    }

    /**
     * @param string $token
     * @return array
     */
    private function getRequestOptionsFromToken(string $token): array
    {
        return [
            'authenticationHeaderName' => 'X-Authorization',
            'authenticationHeaderValue' => 'Bearer ' . $token
        ];
    }

    /**
     * @param string $file
     */
    private function loadFranceCompetencesDataRS(string $file = __DIR__ . "/../../../data/export_rs.xml")
    {
        if (file_exists($file)) {
            ini_set('memory_limit', '2048M');
            $xml = simplexml_load_file($file, "SimpleXMLElement", LIBXML_NOCDATA);
            $json = json_encode($xml);
            $this->franceCompetencesDataRS = json_decode($json, TRUE)['FICHE'];
            $this->logger->info("[loadFranceCompetencesDataRS] loaded: " . sizeof($this->franceCompetencesDataRS));
        } else {
            $this->logger->info("[loadFranceCompetencesDataRS] not found");
            $this->refreshDataFiles(CertificationTypes::RS());
            $this->loadFranceCompetencesDataRS();
        }
    }

    /**
     * @param string $file
     */
    private function loadFranceCompetencesDataRNCP(string $file = __DIR__ . "/../../../data/export_rncp.xml")
    {
        if (file_exists($file)) {
            ini_set('memory_limit', '4096M');
            $xml = simplexml_load_file($file, "SimpleXMLElement", LIBXML_NOCDATA);
            $json = json_encode($xml);
            $this->franceCompetencesDataRNCP = json_decode($json, TRUE)['FICHE'];
            $this->logger->info("[franceCompetencesDataRNCP] loaded: " . sizeof($this->franceCompetencesDataRNCP));
        } else {
            $this->logger->info("[franceCompetencesDataRNCP] not found");
            $this->refreshDataFiles(CertificationTypes::RNCP());
            $this->franceCompetencesDataRNCP = [];
        }
    }

    /**
     * @param CertificationTypes $type
     * @return array
     */
    private function getAllDataForType(CertificationTypes $type): array
    {
        $franceCompetencesData = [];

        switch ($type->getValue()) {
            case CertificationTypes::RS()->getValue():
                if ($this->franceCompetencesDataRS === null) {
                    $this->loadFranceCompetencesDataRS();
                }
                $franceCompetencesData = $this->franceCompetencesDataRS;
                break;
            case CertificationTypes::RNCP()->getValue():
                if ($this->franceCompetencesDataRNCP === null) {
                    $this->loadFranceCompetencesDataRNCP();
                }
                $franceCompetencesData = $this->franceCompetencesDataRNCP;
                break;
            default:
                break;
        }

        return $franceCompetencesData;
    }

    /**
     * @return void
     */
    private function resetFranceCompetencesDataRS(): void
    {
        $this->franceCompetencesDataRS = [];
    }

    /**
     * @return void
     */
    private function resetFranceCompetencesDataRNCP(): void
    {
        $this->franceCompetencesDataRNCP = [];
    }

    /**
     * @param string $certifInfo
     * @return array
     */
    private function getDataByCertifInfo(string $certifInfo): array
    {
        $certification = $this->certificationService->getByCertifInfo($certifInfo);
        if ($certification && $certification->getType()) {
            $franceCompetencesData = $this->getAllDataForType(new CertificationTypes($certification->getType()));
            $indexOfCertification = array_search($certification->getExternalId(), array_column($franceCompetencesData, 'NUMERO_FICHE'));
            return $indexOfCertification === false ? [] : $franceCompetencesData[$indexOfCertification];
        } else {
            return [];
        }
    }

    /**
     * @param Certification $certification
     * @return string
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
     */
    private function generateXlsPartners(Certification $certification): string
    {
        /** @var CertificationPartner[] $partners */
        $partners = $this->certificationPartnerService->list($certification, [
            "state" => [
                CertificationPartnerStates::ACTIVE(),
                CertificationPartnerStates::SUSPENDED(),
                CertificationPartnerStates::REVOKED(),
                CertificationPartnerStates::PROCESSING()
            ] //processing, suspended & revoked to retrieve all in pendingActivation / pendingRevocation
        ]);

        $inputFileType = 'Xls';
        $inputFileName = $this->projectDir . '/data/organismes_partenaires.xls';
        $reader = IOFactory::createReader($inputFileType);
        $spreadsheet = $reader->load($inputFileName);
        $sheet = $spreadsheet->getActiveSheet();
        $i = 2;
        foreach ($partners as $partner) {
            if ($partner->isPendingActivation()
                || ($partner->getState() === CertificationPartnerStates::ACTIVE()->getValue() && !$partner->isPendingRevocation() && !$partner->isPendingSuspension())) {
                $sheet->setCellValue('A' . $i, $partner->getPartner()->getSiret());
                switch ($partner->getHabilitation()) {
                    case 'evaluate':
                        $hab = "Habilitation pour organiser l’évaluation";
                        break;
                    case 'train_evaluate':
                        $hab = "Habilitation pour former et organiser l’évaluation";
                        break;
                    default:
                        $hab = "Habilitation pour former";
                        break;
                }
                $sheet->setCellValue('B' . $i, $hab);
                $i++;
            }
        }
        //no scientific notation for sirets...
        $writer = new Xls($spreadsheet);
        $uid = uniqid('', true);
        $uid = str_replace('.', '_', $uid);
        $fileName = sys_get_temp_dir() . "/organismes_partenaires_{$certification->getIdFiche()}_$uid.xls";
        $writer->save($fileName);
        return $fileName;
    }

    /**
     * @param array $franceCompetencesData
     * @param CertificationTypes $type
     * @return array
     */
    private function getSimplifiedRawData(array $franceCompetencesData, CertificationTypes $type): array
    {
        $simplifiedFranceCompetencesData = [
            'INTITULE' => $franceCompetencesData['INTITULE'],
            'ETAT_FICHE' => $franceCompetencesData['ETAT_FICHE'],
            'ID_FICHE' => $franceCompetencesData['ID_FICHE'],
            'ACTIF' => $franceCompetencesData['ACTIF']
        ];

        $simplifiedFranceCompetencesData['CODE'] = str_replace($type->getValue(), '', $franceCompetencesData['NUMERO_FICHE']);
        $simplifiedFranceCompetencesData['TYPE'] = $type;
        $simplifiedFranceCompetencesData['LINK'] = "https://www.francecompetences.fr/recherche/" . strtolower($simplifiedFranceCompetencesData['TYPE']->getValue()) . "/" . $simplifiedFranceCompetencesData['CODE'] . "/";

        if (!empty($franceCompetencesData['FORMACODES'])) $simplifiedFranceCompetencesData['FORMACODES'] = $franceCompetencesData['FORMACODES'];
        if (!empty($franceCompetencesData['CODES_NSF'])) $simplifiedFranceCompetencesData['CODES_NSF'] = $franceCompetencesData['CODES_NSF'];
        if (!empty($franceCompetencesData['CERTIFICATEURS'])) $simplifiedFranceCompetencesData['CERTIFICATEURS'] = $franceCompetencesData['CERTIFICATEURS'];
        if (!empty($franceCompetencesData['PARTENAIRES'])) $simplifiedFranceCompetencesData['PARTENAIRES'] = $franceCompetencesData['PARTENAIRES'];
        if (!empty($franceCompetencesData['DATE_DECISION'])) $simplifiedFranceCompetencesData['DATE_DECISION'] = $franceCompetencesData['DATE_DECISION'];
        if (!empty($franceCompetencesData['DATE_FIN_ENREGISTREMENT'])) $simplifiedFranceCompetencesData['DATE_FIN_ENREGISTREMENT'] = $franceCompetencesData['DATE_FIN_ENREGISTREMENT'];
        if (!empty($franceCompetencesData['DUREE_VALIDITE'])) $simplifiedFranceCompetencesData['DUREE_VALIDITE'] = $franceCompetencesData['DUREE_VALIDITE'];
        if (!empty($franceCompetencesData['OBJECTIFS_CONTEXTE'])) $simplifiedFranceCompetencesData['OBJECTIFS_CONTEXTE'] = $franceCompetencesData['OBJECTIFS_CONTEXTE'];
        if (!empty($franceCompetencesData['CAPACITES_ATTESTEES'])) $simplifiedFranceCompetencesData['CAPACITES_ATTESTEES'] = $franceCompetencesData['CAPACITES_ATTESTEES'];
        if (!empty($franceCompetencesData['NIVEAU_MAITRISE_COMPETENCES'])) $simplifiedFranceCompetencesData['NIVEAU_MAITRISE_COMPETENCES'] = $franceCompetencesData['NIVEAU_MAITRISE_COMPETENCES'];
        if (!empty($franceCompetencesData['ANCIENNE_CERTIFICATION'])) $simplifiedFranceCompetencesData['ANCIENNE_CERTIFICATION'] = $franceCompetencesData['ANCIENNE_CERTIFICATION'];
        if (!empty($franceCompetencesData['BLOCS_COMPETENCES'])) $simplifiedFranceCompetencesData['BLOCS_COMPETENCES'] = $franceCompetencesData['BLOCS_COMPETENCES'];

        return $simplifiedFranceCompetencesData;
    }

    /**
     * @param $data
     * @param $rawData
     * @return mixed
     */
    private function mergeWithFranceCompetencesData($data, $rawData)
    {
        $rawData['name'] = $data['INTITULE'];
        $rawData['enabled'] = $data['ACTIF'] === 'Oui';
        $rawData['idFiche'] = $data['ID_FICHE'];
        if (!empty($data['FORMACODES'])) {
            $rawData['domains'] = [];
            $array = isset($data['FORMACODES']['FORMACODE'][0]) ? $data['FORMACODES']['FORMACODE'] : $data['FORMACODES'];
            foreach ($array as $domain) {
                $rawData['domains'][] = [
                    "code" => $domain['CODE'],
                    "name" => $domain['LIBELLE']
                ];
            }
        }
        if (empty($rawData['level'])) {
            $rawData['level'] = 'Sans équivalence de niveau';
        }
        if (empty($rawData['idInfos'])) {
            $rawData['idInfos'] = [];
        }
        if (!empty($data['CODE'])) {
            $rawData['idInfos']['code'] = $data['CODE'];
        }
        if (!empty($data['TYPE'])) {
            $rawData['idInfos']['type'] = $data['TYPE'];
        }
        if (!empty($data['LINK'])) {
            $rawData['idInfos']['link'] = $data['LINK'];
        }
        if (!empty($data['CODES_NSF'])) {
            $rawData['nsf'] = [];
            $array = isset($data['CODES_NSF']['NSF'][0]) ? $data['CODES_NSF']['NSF'] : $data['CODES_NSF'];
            foreach ($array as $nsf) {
                $rawData['nsf'][] = [
                    "code" => $nsf['CODE'],
                    "name" => $nsf['LIBELLE']
                ];
            }
        }
        if (!empty($data['CERTIFICATEURS'])) {
            $rawData['certifiers'] = [];
            $array = isset($data['CERTIFICATEURS']['CERTIFICATEUR'][0]) ? $data['CERTIFICATEURS']['CERTIFICATEUR'] : $data['CERTIFICATEURS'];
            foreach ($array as $certifier) {
                $certif = [];
                if (isset($certifier['SIRET_CERTIFICATEUR'])) {
                    $certif['siret'] = $certifier['SIRET_CERTIFICATEUR'];
                    if (isset($certifier['NOM_CERTIFICATEUR'])) {
                        $certif['name'] = $certifier['NOM_CERTIFICATEUR'];
                    }
                    $rawData['certifiers'][] = $certif;
                }
            }
        }
        if (!empty($data['PARTENAIRES'])) {
            $rawData['partners'] = [];
            $array = isset($data['PARTENAIRES']['PARTENAIRE'][0]) ? $data['PARTENAIRES']['PARTENAIRE'] : $data['PARTENAIRES'];
            foreach ($array as $partner) {
                $certif = [];
                if (isset($partner['SIRET_PARTENAIRE'])) {
                    $certif['siret'] = $partner['SIRET_PARTENAIRE'];
                    if (isset($partner['NOM_PARTENAIRE'])) {
                        $certif['name'] = $partner['NOM_PARTENAIRE'];
                    }
                    if (isset($partner['HABILITATION_PARTENAIRE'])) {
                        $certif['role'] = $partner['HABILITATION_PARTENAIRE'];
                    }
                    $rawData['partners'][] = $certif;
                }
            }
        }
        if (!empty($data['DATE_FIN_ENREGISTREMENT'])) {
            $rawData['cpf']['dateEnd'] = $rawData['cpf']['dateEnd'] ?? DateTime::createFromFormat('Y-m-d', date('Y-m-d', strtotime(str_replace('/', '-', $data['DATE_FIN_ENREGISTREMENT']))));
        }
        if (!empty($data['DATE_DECISION'])) {
            $rawData['cpf']['dateStart'] = $rawData['cpf']['dateStart'] ?? DateTime::createFromFormat('Y-m-d', date('Y-m-d', strtotime(str_replace('/', '-', $data['DATE_DECISION']))));
        }
        if (!empty($data['DUREE_VALIDITE'])) {
            $rawData['validityPeriod'] = $data['DUREE_VALIDITE'];
        }
        if (!empty($data['OBJECTIFS_CONTEXTE'])) {
            $rawData['objectif'] = $data['OBJECTIFS_CONTEXTE'];
        }
        if (!empty($data['CAPACITES_ATTESTEES'])) {
            $rawData['descriptif'] = $data['CAPACITES_ATTESTEES'];
        }
        if (!empty($data['NIVEAU_MAITRISE_COMPETENCES'])) {
            $rawData['level'] = $data['NIVEAU_MAITRISE_COMPETENCES'];
        }

        if (!empty($data['BLOCS_COMPETENCES'])) {
            $rawData['skillSets'] = [];
            foreach ($data['BLOCS_COMPETENCES']['BLOC_COMPETENCES'] as $skillSet) {
                $skillSetRawData = [];
                if (isset($skillSet['CODE'])) {
                    $skillSetRawData['type'] = CertificationSkillType::SKILL_SET()->getValue();
                    $skillSetRawData['order'] = (int)explode('BC', $skillSet['CODE'])[1];
                    if (isset($skillSet['LIBELLE'])) {
                        $skillSetRawData['label'] = $skillSet['LIBELLE'];
                    }
                    if (isset($skillSet['LISTE_COMPETENCES'])) {
                        $skillSetRawData['description'] = $skillSet['LISTE_COMPETENCES'];
                    }
                    if (isset($skillSet['MODALITES_EVALUATION'])) {
                        $skillSetRawData['modalities'] = $skillSet['MODALITES_EVALUATION'];
                    }
                    $rawData['skillSets'][] = $skillSetRawData;
                }
            }
        }

        return $rawData;
    }

    /**
     * @return array
     * @throws Throwable
     */
    private function getCertifInfoList(): array
    {
        $content = $this->organismService->sendCarifRequest("GET", "/modules/custom/offreinfo/data/certifications.json");
        $certifList = json_decode($content, true)["data"];
        $list = [];
        foreach ($certifList as $certif) {
            if (is_numeric($certif['id'])) {
                $list[] = ["certifInfo" => $certif['id'], "title" => $certif['text']];
            }
        }
        return $list;
    }
}
