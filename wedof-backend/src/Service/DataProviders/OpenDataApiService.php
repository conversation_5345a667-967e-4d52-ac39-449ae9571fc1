<?php

namespace App\Service\DataProviders;

use App\Entity\Connection;
use App\Entity\Organism;
use App\Exception\WedofConflictHttpException;
use App\Exception\WedofConnectionException;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\Tools;
use App\Repository\EndPointStatusRepository;
use App\Service\ConfigService;
use App\Service\ConnectionService;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use ErrorException;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Security\Core\Security;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Throwable;

class OpenDataApiService extends BaseApiService
{
    public function __construct(ConfigService $configService, ConnectionService $connectionService, RequestStack $requestStack, EndPointStatusRepository $endPointStatusRepository, EventDispatcherInterface $dispatcher, LoggerInterface $logger, Security $security)
    {
        parent::__construct(DataProviders::OPEN_DATA(), $configService, $connectionService, $requestStack, $endPointStatusRepository, $dispatcher, $logger, $security);
    }

    /**
     * @param array $whereParameters
     * @return array
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function getTrainingsRawData(array $whereParameters): array
    {
        $offset = 0;
        $limit = 5000;
        $whereStatement = '';

        $requestURL = Tools::getEnvValue('OPEN_DATA_CDC_API_BASE_URI') . "/datasets/moncompteformation_catalogueformation/records";
        $requestURL .= '?offset=' . $offset . '&limit=' . $limit;

        if (!empty($whereParameters)) {

            if (!empty($whereParameters['siret'])) {
                $whereStatement = 'siret=' . $whereParameters['siret'];
            }
            if (!empty($whereParameters['certifInfos'])) {
                $whereStatement .= $whereStatement !== '' ? ' and (' : '(';
                $length = count($whereParameters['certifInfos']);
                for ($i = 0; $i < $length; $i++) {
                    $whereStatement .= 'code_certifinfo=' . $whereParameters['certifInfos'][$i];
                    $whereStatement .= $i + 1 === $length ? ')' : ' or ';
                }
            }
        }

        $options = [
            'cat_api_status' => "open-data-training",
            "throw_404_exception" => false,
            'authenticationHeaderName' => Tools::getEnvValue('OPEN_DATA_CDC_API_AUTH_HEADER'),
            'authenticationHeaderValue' => "Apikey " . Tools::getEnvValue('OPEN_DATA_CDC_API_AUTH'),
            'parameters' => [
                'where' => $whereStatement,
                'group_by' => 'numero_formation as externalId',
                'select' => "sum(nb_action) as nbTrainingActions, sum(nb_session_active) as nbSessions"
            ]
        ];

        return $this->sendRequest($requestURL, $options)['results'];
    }

    /**
     * @inheritDoc
     */
    public function getMaxAttemptsBeforeStop(): int
    {
        return 3;
    }

    /**
     * @inheritDoc
     */
    public function authenticate(Connection $connection, bool $checkOrganismAccess = true): array
    {
        throw new WedofConflictHttpException("Pas d'implémentation de la fonction authenticate dans l'OpenDataApiService");
    }

    /**
     * @inheritDoc
     */
    public function checkBeforeHabilitate(Organism $organism, array $params = null)
    {
    }

    /**
     * @inheritDoc
     */
    public function habilitate(Organism $organism, Connection $connection, array $params): bool
    {
        return false;
    }

    /**
     * @inheritDoc
     */
    public function getUsername(Connection $connection): string
    {
        return "";
    }

    /**
     * @inheritDoc
     */
    public function requiresAuthentication(): bool
    {
        return false;
    }

    /**
     * @inheritDoc
     */
    public function setActiveOrganism(Organism $organism): bool
    {
        return true;
    }

    /**
     * @inheritDoc
     */
    public function setAuthentication(array $options = []): array
    {
        return $options;
    }

    //----------------
    // METHODES PRIVES
    //----------------
}