<?php

namespace App\Service\DataProviders;

use App\Entity\Certification;
use App\Entity\Organism;

class CertificationInternalApiService extends InternalApiService implements InterfaceCertificationApiService
{
    /**
     * @param string $certifInfo
     * @return array
     */
    public function getCertifiersDataForUpdate(string $certifInfo): array
    {
        return [];
    }

    /**
     * @param string $certifInfo
     * @return array
     */
    public function getCertificationDataForUpdate(string $certifInfo): array
    {
        return [];
    }

    /**
     * @param Certification $certification
     * @param Organism|null $certifier
     * @param int|null $retryCounter
     * @return void
     */
    public function synchronizeCertificationPartners(Certification $certification, Organism $certifier = null, ?int $retryCounter = null): void
    {
    }

    /**
     * @param Certification $certification
     * @return void
     */
    public function updatePartnersForCertification(Certification $certification): void
    {
    }
}