<?php

namespace App\Service\DataProviders;

use App\Entity\Organism;
use App\Exception\WedofConnectionException;
use App\Library\utils\enums\DataProviders;
use App\Repository\EndPointStatusRepository;
use App\Repository\UserRepository;
use App\Service\ConfigService;
use App\Service\ConnectionService;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use ErrorException;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Security\Core\Security;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Throwable;

// Swagger recette : https://cfa-apicfadock-rec.opco-sante.fr/swagger/index.html
// Swagger prod : https://cfa-apicfadock.opco-sante.fr/swagger/index.html
class OpcoCfaSanteApiService extends OpcoCfaDockApiService
{
    /**
     * @param ConnectionService $connectionService
     * @param RequestStack $requestStack
     * @param ConfigService $configService
     * @param EventDispatcherInterface $dispatcher
     * @param LoggerInterface $logger
     * @param EndPointStatusRepository $endPointStatusRepository
     * @param Security $security
     * @param UserRepository $userRepository
     */
    public function __construct(ConnectionService        $connectionService,
                                RequestStack             $requestStack,
                                ConfigService            $configService,
                                EventDispatcherInterface $dispatcher,
                                LoggerInterface          $logger,
                                EndPointStatusRepository $endPointStatusRepository,
                                Security                 $security,
                                UserRepository $userRepository
    )
    {
        parent::__construct(DataProviders::OPCO_CFA_SANTE(), $configService, $connectionService, $requestStack, $endPointStatusRepository, $dispatcher, $logger, $security, $userRepository);
    }

    /**
     * @param Organism $organism
     * @param array|null $params
     * @return array
     */
    public function checkBeforeHabilitate(Organism $organism, array $params = null): array
    {
        return []; // See https://www.cfadock.fr/portail_developpeur#/tabs/implementations "Accrochage CFA key Info"
    }

    // Because https://cfa-apicfadock-rec.opco-sante.fr/v1/status requires Bearer
    public function status(bool $needsAuth = true): array
    {
        return parent::status($needsAuth);
    }

    /**
     * Need to override because weird parameter format + weird raw data GRRR
     * @param Organism $organism
     * @param array $externalIdsFinancerChunk
     * @param bool $repeatListParameter
     * @return array
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function listRawFoldersByChunk(Organism $organism, array $externalIdsFinancerChunk, bool $repeatListParameter = false): array
    {
        $rawFolders = parent::listRawFoldersByChunk($organism, $externalIdsFinancerChunk, $repeatListParameter); // Don't repeat parameter for sante
        // Remove buggy data set by SANTE...
        return array_map(function (array $rawFolder) {
            unset($rawFolder['status']);
            unset($rawFolder['code']);
            unset($rawFolder['description']);
            return $rawFolder;
        }, $rawFolders);
    }
}
