<?php


namespace App\Service\DataProviders;

use App\Entity\Connection;
use App\Entity\Organism;
use App\Exception\WedofConnectionException;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\Tools;
use App\Repository\EndPointStatusRepository;
use App\Repository\UserRepository;
use App\Service\ConfigService;
use App\Service\ConnectionService;
use DateInterval;
use DateTime;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use ErrorException;
use Exception;
use Psr\Cache\InvalidArgumentException;
use Psr\Log\LoggerInterface;
use Symfony\Component\Cache\Adapter\FilesystemAdapter;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Security\Core\Security;
use Symfony\Contracts\Cache\ItemInterface;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Throwable;

/**
 * This service is a parent for all OPCOs based on CfaDock generic API (https://www.cfadock.fr/portail_developpeur).
 * It is the basis for WorkingContract retrieval.
 * It does not know about RFs
 */
abstract class OpcoCfaDockApiService extends BaseApiService
{
    const WEDOF_APPLICATION = 'Wedof';
    const FOLDERS_CHUNK_SIZE = 50;
    const ENDPOINT_CFA_KEY_INFO_V2 = 'v2/cfakeyinfo';
    const ENDPOINT_DOSSIERS_V1 = 'v1/dossiers';
    const ENDPOINT_DOSSIERS_ETATS_V1 = 'v1/dossiers/etats';
    const ENDPOINT_DOSSIERS_LISTE_V1 = 'v1/dossiers/liste';
    const ENDPOINT_STATUS_V1 = 'v1/status';
    const STATUS_HEALTHY = 'healthy';

    protected UserRepository $userRepository;

    public function __construct(
        DataProviders            $dataProvider,
        ConfigService            $configService,
        ConnectionService        $connectionService,
        RequestStack             $requestStack,
        EndPointStatusRepository $endPointStatusRepository,
        EventDispatcherInterface $dispatcher,
        LoggerInterface          $logger,
        Security       $security,
        UserRepository $userRepository
    )
    {
        parent::__construct($dataProvider, $configService, $connectionService, $requestStack, $endPointStatusRepository, $dispatcher, $logger, $security);
        $this->userRepository = $userRepository;
    }

    //-------------------
    // METHODES CONNEXION HERITEES
    //-------------------

    /**
     * @return int
     */
    public function getMaxAttemptsBeforeStop(): int
    {
        return 3;
    }

    /**
     * @throws ORMException
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     * @throws WedofConnectionException
     * @throws OptimisticLockException
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws NonUniqueResultException
     * @throws ErrorException
     * @throws NoResultException
     * @throws InvalidArgumentException
     */
    public function checkBeforeHabilitate(Organism $organism, array $params = null): array
    {
        // Be careful this endpoint is not available for every OPCO, see https://www.cfadock.fr/portail_developpeur#/tabs/implementations "Accrochage CFA key Info"
        // If not available => override in ApiService
        $accessToken = $this->getOpcoAccessToken();
        $apiKey = $params['password'] ?? null;
        $options = [
            'access_token' => $accessToken, // Bearer
            'headers' => [
                'X-API-KEY' => $apiKey, // Required for some Opcos (Mobilité)
                'EDITEUR' => self::WEDOF_APPLICATION,
                'LOGICIEL' => self::WEDOF_APPLICATION,
                'VERSION' => Tools::getEnvValue('VERSION')
            ],
            'parameters' => [
                'siren' => $organism->getSiren(),
                'XAPIKEY' => $apiKey,
                'cfaKey' => $apiKey // TODO it's here only because for some reason Mobilités did not use the right param name, remove when fixed GRRR
            ],
            'fullResponse' => true,
            'cat_api_status' => 'status'
        ];
        $response = $this->sendRequest(self::getEnvApiConfig($this->dataProvider, 'BASE_URI') . self::ENDPOINT_CFA_KEY_INFO_V2, $options);
        $statusCode = $response['statusCode'];
        if ($statusCode === 200) {
            $responseData = json_decode($response['content'], true);
            $dateFinValidite = $responseData['dateFinValidite'];
            if ($dateFinValidite < (new DateTime('now'))->format('Y-m-d')) {
                $result = ['error' => 'expired'];
            } else {
                $result = [];
            }
        } else {
            $this->logger->error('Error OPCO ' . $this->dataProvider->getValue() . ' organism ' . $organism->getSiret() . ' checkBeforeHabilitate ' . $response['statusCode'] . ' ' . $response['content']);
            $result = ['error' => $statusCode === 300 ? 'notfound' : 'other'];
        }
        return $result;
    }

    /**
     * @param Connection $connection
     * @param bool $checkOrganismAccess
     * @return true[]
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     * @throws InvalidArgumentException
     */
    public function authenticate(Connection $connection, bool $checkOrganismAccess = true): array
    {
        $credentials = $connection->getCredentials();
        if (!isset($credentials['password'])) {
            throw new WedofConnectionException("La clé d'API CFA est requise");
        }
        $apiKey = $credentials['password'];
        $accessToken = $this->getOpcoAccessToken();
        $options = [
            'access_token' => $accessToken,
            'headers' => [
                'X-API-KEY' => $apiKey,
                'EDITEUR' => self::WEDOF_APPLICATION,
                'LOGICIEL' => self::WEDOF_APPLICATION,
                'VERSION' => Tools::getEnvValue('VERSION')
            ],
            'fullResponse' => true,
            'cat_api_status' => 'status'
        ];
        $success = $this->testAuthentication($options);
        if ($success) {
            $expiresOn = (new DateTime('now'))->add(new DateInterval('P20Y')); // TODO opco expiration of OAuth?
            return [
                'hasAccess' => true,
                'hasOrganismAccess' => true,
                'expiresOn' => $expiresOn,
                'refreshAt' => $expiresOn,
                'lastRefresh' => new DateTime('now'),
            ];
        } else {
            $this->logger->error('Error OPCO ' . $this->dataProvider->getValue() . ' organism ' . $connection->getOrganism()->getSiret() . ' authenticate failed');
            return [
                'hasAccess' => false,
                'hasOrganismAccess' => false,
                'error' => 'other'
            ];
        }
    }

    /**
     * @param array $options
     * @return array
     * @throws ClientExceptionInterface
     * @throws InvalidArgumentException
     * @throws NonUniqueResultException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     * @throws \Symfony\Component\Mailer\Exception\TransportExceptionInterface
     * @throws NoResultException
     */
    public function setAuthentication(array $options = []): array
    {
        if ($options['organism']) {
            /** @var Organism $organism */
            $organism = $options['organism'];
            $connection = $organism->getConnectionForDataProvider($this->dataProvider);
            if ($this->connectionService->hasAccess($organism, $this->dataProvider)) { // TODO(opco) check what it entails
                $credentials = $connection->getCredentials();
                $accessToken = $this->getOpcoAccessToken();
                $apiKey = $credentials['password'];
                $options = array_merge($options, [
                    'access_token' => $accessToken
                ]);
                $options['headers'] = array_merge($options['headers'], [
                    'X-API-KEY' => $apiKey,
                    'EDITEUR' => self::WEDOF_APPLICATION,
                    'LOGICIEL' => self::WEDOF_APPLICATION,
                    'VERSION' => Tools::getEnvValue('VERSION')
                ]);
            }
        }
        return $options;
    }

    /**
     * @param Connection $connection
     * @return void
     * @throws Exception
     */
    public function startInitialization(Connection $connection): void
    {
        // DO NOTHING AS WE HAVE TO WAIT A SPECIFIC HOUR. Initialization will be done through the daily CRON
    }

    /**
     * @param Organism $organism
     * @param Connection $connection
     * @param array $params
     * @return bool
     */
    public function habilitate(Organism $organism, Connection $connection, array $params): bool
    {
        return false;
    }

    /**
     * @return bool
     */
    public function requiresAuthentication(): bool
    {
        return true;
    }

    /**
     * @param Connection $connection
     * @return string
     */
    public function getUsername(Connection $connection): string
    {
        return "Clé d'API";
    }


    public function setActiveOrganism(Organism $organism): bool
    {
        return true;
    }

    //-------------------
    // METHODES CONNEXION / OAUTH
    //-------------------

    /**
     * @param $options
     * @return bool
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    protected function testAuthentication($options): bool
    {
        $endpoint = self::ENDPOINT_DOSSIERS_ETATS_V1; // Only endpoint that is available for all Opcos and do not require specific parameters
        $response = $this->sendRequest(self::getEnvApiConfig($this->dataProvider, 'BASE_URI') . $endpoint, $options);
        return $response['statusCode'] < 300;
    }

    /**
     * @return string
     * @throws ClientExceptionInterface
     * @throws InvalidArgumentException
     * @throws NonUniqueResultException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     * @throws NoResultException
     */
    public function getOpcoAccessToken(): string
    {
        return $this->isHttpContext() ? $this->getOpcoAccessTokenFromMain() : $this->getOpcoAccessTokenFromWorker();
    }

    /**
     * @return string
     * @throws InvalidArgumentException
     */
    public function getOpcoAccessTokenFromMain(): string
    {
        $cache = new FilesystemAdapter();
        $cacheKey = $this->dataProvider->getValue() . '_OAUTH_ACCESS_TOKEN';
        $this->logger->debug('Opco ' . $this->dataProvider->getValue() . ' get OAuth Access Token from Main');
        return $cache->get($cacheKey, function (ItemInterface $item) {
            $this->logger->debug('Opco ' . $this->dataProvider->getValue() . ' cache miss => get fresh OAuth');
            $oAuth = $this->getFreshOAuth();
            $expiresAt = new DateTime();
            $expiresAt->setTimestamp($oAuth['expires'])->modify('-1 minute'); // 1 minute margin just in case
            $item->expiresAt($expiresAt);
            return $oAuth['access_token'];
        });
    }

    /**
     * @return string
     * @throws ClientExceptionInterface
     * @throws NonUniqueResultException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     * @throws NoResultException
     */
    public function getOpcoAccessTokenFromWorker(): string
    {
        $this->logger->debug('Opco ' . $this->dataProvider->getValue() . ' get OAuth Access Token from Worker => call Main API');
        $url = Tools::getEnvValue('WEDOF_BASE_URI') . '/api/connections/opcoCfaOAuth/' . $this->dataProvider->getValue();
        $wedofAdminToken = $this->userRepository->getAdminToken();
        $options = [
            'headers' => [
                'X-API-KEY' => $wedofAdminToken,
                'Accept' => 'application/json'
            ]
        ];
        $response = Tools::getHttpClient()->request('GET', $url, $options);
        $oAuth = json_decode($response->getContent(), true);
        return $oAuth['accessToken'];
    }

    /**
     * @return array
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    protected function getFreshOAuth(): array
    {
        $authUri = self::getEnvApiConfig($this->dataProvider, 'AUTH_URI');
        $clientId = self::getEnvApiConfig($this->dataProvider, 'CLIENT_ID');
        $clientSecret = self::getEnvApiConfig($this->dataProvider, 'CLIENT_SECRET');
        $scope = self::getEnvApiConfig($this->dataProvider, 'SCOPE');
        $parameters = [
            'grant_type' => 'client_credentials',
        ];
        if (!empty($scope)) {
            $parameters['scope'] = $scope;
        }
        $options = [
            'method' => 'POST',
            'content-type' => 'application/x-www-form-urlencoded', // TODO(opco): try json
            'auth_basic' => [$clientId, $clientSecret], // TODO(opco): try putting them in payload
            'parameters' => $parameters,
            'cat_api_status' => 'oauth',
            'headers' => [
                'EDITEUR' => self::WEDOF_APPLICATION,
                'LOGICIEL' => self::WEDOF_APPLICATION,
                'VERSION' => Tools::getEnvValue('VERSION')
            ],
        ];
        $result = $this->sendRequest($authUri, $options);
        if (empty($result['access_token'])) {
            throw new WedofConnectionException('Error OAuth OPCO ' . $this->dataProvider->getValue());
        }
        $access_token = $result['access_token'];
        $access_token_data = Tools::unsecureDecodeJWT($access_token);
        $expires = $access_token_data['exp']; // TIMESTAMP
        $this->logger->debug('Opco ' . $this->dataProvider->getValue() . ' fresh OAuth retrieved, will expire on ' . $expires);
        return [
            'access_token' => $access_token,
            'expires' => $expires
        ];
    }

    //-------------------
    // API OPCO
    //-------------------

    /**
     * @param bool $needsAuth
     * @return array
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws InvalidArgumentException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function status(bool $needsAuth = false): array
    {
        $accessToken = ''; // empty string required so no auth
        if ($needsAuth) {
            $accessToken = $this->getOpcoAccessToken();
        }
        $options = [
            'access_token' => $accessToken,
            'cat_api_status' => 'status',
            'headers' => [
                'EDITEUR' => self::WEDOF_APPLICATION,
                'LOGICIEL' => self::WEDOF_APPLICATION,
                'VERSION' => Tools::getEnvValue('VERSION')
            ],
        ];
        return $this->sendRequest(self::getEnvApiConfig($this->dataProvider, 'BASE_URI') . self::ENDPOINT_STATUS_V1, $options);
    }

    /**
     * @param Organism $organism
     * @return array
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function listRawFolderStates(Organism $organism): array
    {
        // Returns
        // No pagination!! TODO(opco): consider increasing memory
        // - numeroInterne: string,
        // - etat: string
        // - numeroExterne: string (optional)
        // - numeroDeca: string (optional)
        $options = [
            'organism' => $organism,
            'cat_api_status' => 'folders'
        ];
        return $this->sendRequest(self::getEnvApiConfig($this->dataProvider, 'BASE_URI') . self::ENDPOINT_DOSSIERS_ETATS_V1, $options);
    }

    /**
     * @param Organism $organism
     * @param array $externalIdsFinancerChunk
     * @param bool $repeatListParameter
     * @return array
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function listRawFoldersByChunk(Organism $organism, array $externalIdsFinancerChunk, bool $repeatListParameter = true): array
    {
        if ($repeatListParameter) {
            // HACK because API expects: ?numerosInternes=123&numerosInternes=456&numerosInternes=789
            // but CurlHttpClient only generate: ?numerosInternes[0]=123&numerosInternes[1]=456&numerosInternes[2]=789
            // PHP does not allow having twice the same key in an array
            // so we cannot use the "parameters" option but rather create the URL manually
            $numerosInternesHackParam = '?numerosInternes=' . implode('&numerosInternes=', $externalIdsFinancerChunk);
        } else {
            // HACK for Sante because API expects ?numerosInternes=123,456
            $numerosInternesHackParam = '?numerosInternes=' . implode(',', $externalIdsFinancerChunk);
        }
        $options = [
            'organism' => $organism,
            'cat_api_status' => 'folders'
        ];
        $response = $this->sendRequest(self::getEnvApiConfig($this->dataProvider, 'BASE_URI') . self::ENDPOINT_DOSSIERS_LISTE_V1 . $numerosInternesHackParam, $options);
        $rawFolders = $response['dossiers'];
        if (count($rawFolders) != count($externalIdsFinancerChunk)) {
            throw new WedofConnectionException('Error OPCO ' . $this->dataProvider->getValue() . ' organism ' . $organism->getSiret() . ' listRawFoldersByChunk count of result don\'t match request');
        }
        return array_values(array_filter($rawFolders,
            fn(array $rawFolder) => $rawFolder['cerfa']['organismeFormation']['siret'] === $organism->getSiret()) // VERY IMPORTANT
        );
    }

    /**
     * JUST FOR RECETTE AT THE MOMENT
     * @param Organism $organism
     * @param array $rawFolder
     * @return void
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function createFolder(Organism $organism, array $rawFolder): array
    {
        $options = [
            'method' => 'POST',
            'content-type' => "application/json",
            'parameters' => $rawFolder,
            'organism' => $organism,
            'cat_api_status' => 'folders',
            'fullResponse' => true
        ];
        return $this->sendRequest(self::getEnvApiConfig($this->dataProvider, 'BASE_URI') . self::ENDPOINT_DOSSIERS_V1, $options);
    }
}
