<?php

namespace App\Service\DataProviders;

use App\Entity\Certification;
use App\Entity\Connection;
use App\Entity\Organism;
use App\Entity\User;
use App\Exception\WedofConflictHttpException;
use App\Exception\WedofConnectionException;
use App\Library\utils\enums\DataProviders;
use App\Repository\EndPointStatusRepository;
use App\Service\CertificationPartnerService;
use App\Service\ConfigService;
use App\Service\ConnectionService;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use ErrorException;
use Psr\Container\ContainerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\File\File;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Security\Core\Security;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Throwable;
use const true;

class AutomatorApiService extends BaseApiService
{
    public CertificationPartnerService $certificationPartnerService;
    public ContainerInterface $container;

    /**
     * @param ConfigService $configService
     * @param ConnectionService $connectionService
     * @param RequestStack $requestStack
     * @param EndPointStatusRepository $endPointStatusRepository
     * @param EventDispatcherInterface $dispatcher
     * @param CertificationPartnerService $certificationPartnerService
     * @param ContainerInterface $container
     * @param LoggerInterface $logger
     * @param Security $security
     */
    public function __construct(
        ConfigService $configService,
        ConnectionService $connectionService,
        RequestStack $requestStack,
        EndPointStatusRepository $endPointStatusRepository,
        EventDispatcherInterface $dispatcher,
        CertificationPartnerService $certificationPartnerService,
        ContainerInterface $container,
        LoggerInterface $logger,
        Security $security)
    {
        parent::__construct(DataProviders::AUTOMATOR(), $configService, $connectionService, $requestStack, $endPointStatusRepository, $dispatcher, $logger, $security);
        $this->certificationPartnerService = $certificationPartnerService;
        $this->container = $container;
    }

    /**
     * @param array $parameters
     * @param Organism|null $organism
     * @param string|null $contentType
     * @return array|null
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function generateCertificate(array $parameters, Organism $organism = null, string $contentType = null): ?array
    {
        $options = [
            'authenticationHeaderName' => $_ENV['AUTOMATOR_AUTH_HEADER'],
            'authenticationHeaderValue' => $_ENV['AUTOMATOR_AUTH'],
            'fullResponse' => true,
            'parameters' => $parameters,
            'method' => 'POST',
        ];
        if ($contentType) {
            $options = array_merge($options, ['content-type' => $contentType]);
        }
        if ($organism) {
            $options = array_merge($options, ['organism' => $organism]);
        }
        return $this->sendRequest($_ENV['AUTOMATOR_BASE_URI'] . 'generate-certificate', $options);
    }

    /**
     * @param string $siret
     * @return array|null
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function getExtraDataOrganism(string $siret): ?array
    {
        $options = [
            'authenticationHeaderName' => $_ENV['AUTOMATOR_AUTH_HEADER'],
            'authenticationHeaderValue' => $_ENV['AUTOMATOR_AUTH'],
            'parameters' => ["siret" => $siret],
            'content-type' => "application/json",
            'method' => 'POST'
        ];
        return $this->sendRequest($_ENV['AUTOMATOR_BASE_URI'] . 'get-extra-data-organism', $options);
    }

    /**
     * @param Certification $certification
     * @param Organism $certifier
     * @return array|null
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    public function generateCertificateTemplate(Certification $certification, Organism $certifier): ?array
    {
        $options = [
            'authenticationHeaderName' => $_ENV['AUTOMATOR_AUTH_HEADER'],
            'authenticationHeaderValue' => $_ENV['AUTOMATOR_AUTH'],
            'fullResponse' => true,
            'parameters' => [
                'siret' => $certifier->getSiret(),
                'certifInfo' => $certification->getCertifInfo(),
                'name' => $certification->getExternalId()
            ],
            'method' => 'POST',
            'organism' => $certifier,
            'files' => [
                'file' => $certification->getCertificateTemplateFile()->getPath() . "/" . $certification->getCertificateTemplateFile()->getFilename(),
            ],
        ];
        return $this->sendRequest($_ENV['AUTOMATOR_BASE_URI'] . 'template-certificate', $options);
    }

    /**
     * @param string $googleId
     * @param string $certifInfo
     * @return array|null
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function updateCertificateTemplateThumbnail(string $googleId, string $certifInfo): ?array
    {
        $options = [
            'authenticationHeaderName' => $_ENV['AUTOMATOR_AUTH_HEADER'],
            'authenticationHeaderValue' => $_ENV['AUTOMATOR_AUTH'],
            'fullResponse' => true,
            'parameters' => [
                'uid' => $googleId,
                'certifInfo' => $certifInfo
            ],
            'method' => 'POST'
        ];
        return $this->sendRequest($_ENV['AUTOMATOR_BASE_URI'] . 'template-thumbnail', $options);
    }

    /**
     * @param array $parameters
     * @param Organism $organism
     * @param File $template
     * @return array|null
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function generateDocumentTemplate(array $parameters, Organism $organism, File $template): ?array
    {
        $options = [
            'authenticationHeaderName' => $_ENV['AUTOMATOR_AUTH_HEADER'],
            'authenticationHeaderValue' => $_ENV['AUTOMATOR_AUTH'],
            'fullResponse' => true,
            'parameters' => $parameters,
            'method' => 'POST',
            'organism' => $organism,
            'files' => [
                'file' => $template
            ]
        ];
        return $this->sendRequest($_ENV['AUTOMATOR_BASE_URI'] . 'template-document', $options);
    }

    /**
     * @param string $path
     * @param string|null $googleId
     * @param string|null $fileName
     * @return array|true[]|null
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function deleteDocumentTemplate(string $path, string $googleId = null, string $fileName = null): array
    {
        $options = [
            'authenticationHeaderName' => $_ENV['AUTOMATOR_AUTH_HEADER'],
            'authenticationHeaderValue' => $_ENV['AUTOMATOR_AUTH'],
            'fullResponse' => true,
            'parameters' => [
                'id' => $googleId,
                'fileName' => $fileName
            ],
            'method' => 'POST',
        ];
        return $this->sendRequest($_ENV['AUTOMATOR_BASE_URI'] . $path, $options);
    }

    /**
     * @param array $fileType
     * @param $computedContext
     * @param string $targetFileType
     * @return array|null
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function generateDocumentFromTemplateAndContext(array $fileType, $computedContext, string $targetFileType = 'pdf'): ?array
    {
        $options = [
            'authenticationHeaderName' => $_ENV['AUTOMATOR_AUTH_HEADER'],
            'authenticationHeaderValue' => $_ENV['AUTOMATOR_AUTH'],
            'fullResponse' => true,
            'parameters' => [
                'uid' => $fileType['googleId'],
                'fileName' => $fileType['name'],
                'fileTypeId' => $fileType['id'],
                'targetFileType' => $targetFileType,
                'computedContext' => json_encode($computedContext)
            ],
            'method' => 'POST',
        ];
        return $this->sendRequest($_ENV['AUTOMATOR_BASE_URI'] . 'template-document/generate', $options);
    }

    /**
     * @param string|null $mimeType
     * @param string|null $googleId
     * @param string|null $fileName
     * @return array|null
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function createOrUpdateTemplate(string $mimeType = null, string $googleId = null, string $fileName = null): ?array
    {
        $options = [
            'authenticationHeaderName' => $_ENV['AUTOMATOR_AUTH_HEADER'],
            'authenticationHeaderValue' => $_ENV['AUTOMATOR_AUTH'],
            'fullResponse' => true,
            'parameters' => [
                'googleId' => $googleId,
                'fileName' => $fileName,
                'mimeType' => $mimeType
            ],
            'method' => 'POST',
        ];
        return $this->sendRequest($_ENV['AUTOMATOR_BASE_URI'] . 'template-document/scratch', $options);
    }

    /**
     * @param string $templateName
     * @param string $folderName
     * @param string|null $fileName
     * @return array|null
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function duplicateFromTemplate(string $templateName, string $folderName, string $fileName): ?array
    {
        $options = [
            'authenticationHeaderName' => $_ENV['AUTOMATOR_AUTH_HEADER'],
            'authenticationHeaderValue' => $_ENV['AUTOMATOR_AUTH'],
            'fullResponse' => true,
            'parameters' => [
                'templateName' => $templateName,
                'folderName' => $folderName,
                'fileName' => $fileName
            ],
            'method' => 'POST',
        ];
        return $this->sendRequest($_ENV['AUTOMATOR_BASE_URI'] . 'duplicate-templateWedofFileTypes', $options);
    }

    /**
     * @param string $googleIdToDuplicate
     * @param string|null $fileName
     * @return array|null
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function duplicateFromTemplateGoogleId(string $googleIdToDuplicate, string $fileName): ?array
    {
        $options = [
            'authenticationHeaderName' => $_ENV['AUTOMATOR_AUTH_HEADER'],
            'authenticationHeaderValue' => $_ENV['AUTOMATOR_AUTH'],
            'fullResponse' => true,
            'parameters' => [
                'googleIdToDuplicate' => $googleIdToDuplicate,
                'fileName' => $fileName
            ],
            'method' => 'POST',
        ];
        return $this->sendRequest($_ENV['AUTOMATOR_BASE_URI'] . 'template-document/duplicate', $options);
    }

    /**
     * @param string $link
     * @param string $fileName
     * @return array|null
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function generatePdfFromLink(string $link, string $fileName): ?array
    {
        $options = [
            'authenticationHeaderName' => $_ENV['AUTOMATOR_AUTH_HEADER'],
            'authenticationHeaderValue' => $_ENV['AUTOMATOR_AUTH'],
            'fullResponse' => true,
            'parameters' => [
                'link' => $link,
                'fileName' => $fileName
            ],
            'method' => 'POST',
        ];
        return $this->sendRequest($_ENV['AUTOMATOR_BASE_URI'] . 'file-convert', $options);
    }

    /**
     * @param string $field
     * @param string $folderId
     * @return array|null
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function listFileTypesWedof(string $field, string $folderId): ?array
    {
        $options = [
            'authenticationHeaderName' => $_ENV['AUTOMATOR_AUTH_HEADER'],
            'authenticationHeaderValue' => $_ENV['AUTOMATOR_AUTH'],
            'fullResponse' => true,
            'parameters' => [
                'folderId' => $folderId,
                'field' => $field
            ],
            'method' => 'GET',
        ];
        return $this->sendRequest($_ENV['AUTOMATOR_BASE_URI'] . 'templateWedofFileTypes', $options);
    }

    /**
     * @throws ORMException
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     * @throws WedofConnectionException
     * @throws OptimisticLockException
     * @throws TransportExceptionInterface
     * @throws Throwable
     * @throws ServerExceptionInterface
     * @throws NonUniqueResultException
     * @throws NoResultException
     * @throws ErrorException
     */
    public function getLinkedInPageUrlInfos(?Organism $organism): ?array
    {
        $options = [
            'authenticationHeaderName' => $_ENV['AUTOMATOR_AUTH_HEADER'],
            'authenticationHeaderValue' => $_ENV['AUTOMATOR_AUTH'],
            'parameters' => [
                'organisationUrl' => $organism->getLinkedInPageUrl(),
            ],
            'method' => 'POST',
            'organism' => $organism
        ];
        return $this->sendRequest($_ENV['AUTOMATOR_BASE_URI'] . 'get-linkedin-organisation-id', $options);
    }

    /**
     * @param Organism $organism
     * @param User $user
     * @param array $body
     * @return array
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function importCertificationFolders(Organism $organism, User $user, array $body): array
    {
        $options = [
            'authenticationHeaderName' => $_ENV['KASTORR_AUTH_HEADER'],
            'authenticationHeaderValue' => $_ENV['KASTORR_AUTH'],
            'parameters' => [
                'siret' => $organism->getSiret(),
                'name' => $organism->getName(),
                'userName' => $user->getName(),
                'userEmail' => $user->getEmail(),
                'apiKey' => $user->getApiTokens()[0]->getToken()
            ],
            'method' => 'POST',
            'organism' => $organism,
            'files' => [
                'file' => $body['file']
            ],
        ];
        return $this->sendRequest($_ENV['KASTORR_BASE_URI'] . 'import-certification-folders', $options);
    }

    /**
     * @param DataProviders $dataProvider
     * @param string $siret
     * @return array
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function checkExistAtDataProvider(DataProviders $dataProvider, string $siret): array
    {
        $options = [
            'authenticationHeaderName' => $_ENV['AUTOMATOR_AUTH_HEADER'],
            'authenticationHeaderValue' => $_ENV['AUTOMATOR_AUTH'],
            'parameters' => [
                'dataprovider' => $dataProvider->getValue(),
                'siret' => $siret,
            ],
            'method' => 'GET',
        ];
        return $this->sendRequest($_ENV['AUTOMATOR_BASE_URI'] . 'dataprovider/checkExist', $options);
    }

    /// BASE API SERVICE STUFF

    public function getMaxAttemptsBeforeStop(): int
    {
        return 3;
    }

    public function authenticate(Connection $connection, bool $checkOrganismAccess = true): array
    {
        throw new WedofConflictHttpException("Pas d'implémentation de la fonction authenticate dans AutomatorApiService");
    }

    public function checkBeforeHabilitate(Organism $organism, array $params = null)
    {
    }

    public function habilitate(Organism $organism, Connection $connection, array $params): bool
    {
        return true;
    }

    public function getUsername(Connection $connection): string
    {
        return '';
    }

    public function requiresAuthentication(): bool
    {
        return false;
    }

    public function setActiveOrganism(Organism $organism): bool
    {
        return true;
    }

    public function setAuthentication(array $options = []): array
    {
        return [];
    }

    /**
     * @param Organism $organism
     * @return array
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function getOrganismUrls(Organism $organism): array
    {
        $urls = [];
        $siret = $organism->getSiret();

        if (empty($siret)) {
            return $urls;
        }

        $options = [
            'content-type' => "application/json",
            'parameters' => [
                'siret' => $siret
            ],
            'method' => 'GET',
        ];

        $response = $this->sendRequest($_ENV['KASTORR_BASE_URI'] . '/getUrls', $options);

        if (is_array($response) && !empty($response)) {
            foreach ($response as $item) {
                if (isset($item['output']) && isset($item['output']['urls']) && is_array($item['output']['urls'])) {
                    foreach ($item['output']['urls'] as $apiUrl) {
                        if (!empty($apiUrl)) {
                            $urls[] = $apiUrl;
                        }
                    }
                }
            }
        }

        return $urls;
    }
}
