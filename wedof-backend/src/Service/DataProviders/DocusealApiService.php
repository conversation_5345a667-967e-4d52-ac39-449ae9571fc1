<?php

namespace App\Service\DataProviders;

use App\Application\Signature\SignatureWedofApplication;
use App\Entity\Attendee;
use App\Entity\CertificationFolderFile;
use App\Entity\CertificationPartnerFile;
use App\Entity\Connection;
use App\Entity\Organism;
use App\Entity\RegistrationFolderFile;
use App\Entity\User;
use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofConflictHttpException;
use App\Exception\WedofConnectionException;
use App\Library\utils\enums\ActivityTypes;
use App\Library\utils\enums\ConnectionStates;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\enums\DocumentSignedStates;
use App\Library\utils\Tools;
use App\Repository\CertificationFolderFileRepository;
use App\Repository\CertificationPartnerFileRepository;
use App\Repository\EndPointStatusRepository;
use App\Repository\RegistrationFolderFileRepository;
use App\Service\AccessService;
use App\Service\ActivityService;
use App\Service\ApplicationService;
use App\Service\AttendeeService;
use App\Service\CertificationFolderFileService;
use App\Service\CertificationPartnerFileService;
use App\Service\ConfigService;
use App\Service\ConnectionService;
use App\Service\RegistrationFolderFileService;
use App\Service\UserService;
use DateTime;
use DateTimeZone;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use ErrorException;
use Exception;
use FilesystemIterator;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\ContainerInterface;
use Psr\Container\NotFoundExceptionInterface;
use Psr\Log\LoggerInterface;
use ReflectionException;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\File\File;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Security\Core\Security;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Throwable;

class DocusealApiService extends BaseApiService
{
    private ContainerInterface $container;
    private AccessService $accessService;
    private CertificationPartnerFileRepository $certificationPartnerFileRepository;
    private RegistrationFolderFileRepository $registrationFolderFileRepository;
    private CertificationFolderFileRepository $certificationFolderFileRepository;
    private ActivityService $activityService;
    private AttendeeService $attendeeService;
    private UserService $userService;
    private ApplicationService $applicationService;

    public function __construct(CertificationPartnerFileRepository $certificationPartnerFileRepository,
                                RegistrationFolderFileRepository   $registrationFolderFileRepository,
                                CertificationFolderFileRepository  $certificationFolderFileRepository,
                                ConfigService                      $configService,
                                ConnectionService                  $connectionService,
                                RequestStack                       $requestStack,
                                EndPointStatusRepository           $endPointStatusRepository,
                                EventDispatcherInterface           $dispatcher,
                                LoggerInterface                    $logger,
                                Security                           $security,
                                ContainerInterface                 $container,
                                AccessService                      $accessService,
                                ActivityService                    $activityService,
                                AttendeeService                    $attendeeService,
                                UserService                        $userService,
                                ApplicationService                 $applicationService)
    {
        parent::__construct(DataProviders::DOCUSEAL(), $configService, $connectionService, $requestStack, $endPointStatusRepository, $dispatcher, $logger, $security);
        $this->container = $container;
        $this->accessService = $accessService;
        $this->certificationPartnerFileRepository = $certificationPartnerFileRepository;
        $this->registrationFolderFileRepository = $registrationFolderFileRepository;
        $this->certificationFolderFileRepository = $certificationFolderFileRepository;
        $this->activityService = $activityService;
        $this->attendeeService = $attendeeService;
        $this->userService = $userService;
        $this->applicationService = $applicationService;
    }

    /**
     * @inheritDoc
     */
    public function getMaxAttemptsBeforeStop(): int
    {
        return 3;
    }

    /**
     * @inheritDoc
     */
    public function authenticate(Connection $connection, bool $checkOrganismAccess = true): array
    {
        throw new WedofConflictHttpException("Pas d'implémentation de la fonction authenticate dans DocusealApiService");
    }

    /**
     * @inheritDoc
     */
    public function checkBeforeHabilitate(Organism $organism, array $params = null)
    {
    }

    /**
     * @inheritDoc
     */
    public function habilitate(Organism $organism, Connection $connection, array $params): bool
    {
        return false;
    }

    /**
     * @inheritDoc
     */
    public function getUsername(Connection $connection): string
    {
        return "";
    }

    /**
     * @inheritDoc
     */
    public function requiresAuthentication(): bool
    {
        return false;
    }

    /**
     * @inheritDoc
     */
    public function setActiveOrganism(Organism $organism): bool
    {
        return true;
    }

    /**
     * @inheritDoc
     */
    public function setAuthentication(array $options = []): array
    {
        return $options;
    }


    /**
     * @throws ORMException
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     * @throws WedofConnectionException
     * @throws OptimisticLockException
     * @throws TransportExceptionInterface
     * @throws Throwable
     * @throws ServerExceptionInterface
     * @throws NonUniqueResultException
     * @throws NoResultException
     * @throws ErrorException
     */
    public function createSignatureSettingsForm(Organism $organism)
    {
        $_submitters[] = [
            "role" => "owner",
            "name" => $organism->getOwnedBy()->getName(),
            "email" => $organism->getOwnedBy()->getEmail(),
            "phone" => $organism->getOwnedBy()->getPhoneNumber() ? "+33" . substr($organism->getOwnedBy()->getPhoneNumber(), 1) : null, //E.164 standard
            "send_sms" => false,
            "send_email" => false,
            "external_id" => 'owner_' . $organism->getOwnedBy()->getId()
        ];
        $options = [
            "method" => "POST",
            'parameters' => [
                "template_id" => 183,
                "submitters" => $_submitters,
                "send_sms" => false,
                "send_email" => false,
                "order" => "preserved",
                "expire_at" => null
            ],
            "authenticationHeaderName" => "X-Auth-Token",
            "authenticationHeaderValue" => Tools::getEnvValue('DOCUSEAL_MASTER_TOKEN')
        ];
        $submission = $this->sendRequest(Tools::getEnvValue("DOCUSEAL_BASE_URL") . "/api/submissions", $options)[0];
        return $submission['slug'];
    }

    /**
     * @param Organism $organism
     * @param RegistrationFolderFile|CertificationFolderFile|CertificationPartnerFile $entityFile
     * @return bool
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function deleteSubmission(Organism $organism, $entityFile): bool
    {
        $options = [
            "method" => "GET",
            "authenticationHeaderName" => "X-Auth-Token",
            "authenticationHeaderValue" => $this->getApiKey($organism)
        ];
        $submission = $this->sendRequest(Tools::getEnvValue("DOCUSEAL_BASE_URL") . "/api/submissions/" . $entityFile->getLink(), $options);
        if (isset($submission['template'])) {
            $options = [
                "method" => "DELETE",
                "authenticationHeaderName" => "X-Auth-Token",
                "authenticationHeaderValue" => $this->getApiKey($organism)
            ];
            $response = $this->sendRequest(Tools::getEnvValue("DOCUSEAL_BASE_URL") . "/api/submissions/" . $entityFile->getLink(), $options);
            if (isset($response['archived_at'])) {
                //one line to delete template no need to create a private method
                $response = $this->sendRequest(Tools::getEnvValue("DOCUSEAL_BASE_URL") . "/api/templates/" . $submission['template']['id'], $options);
                if (isset($response['archived_at'])) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * @throws ORMException
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     * @throws WedofConnectionException
     * @throws OptimisticLockException
     * @throws TransportExceptionInterface
     * @throws Throwable
     * @throws ServerExceptionInterface
     * @throws NonUniqueResultException
     * @throws ErrorException
     * @throws NoResultException
     */
    public function createSubmission(Organism $organism, UploadedFile $file, array $submitters = []): array
    {
        $template = $this->createTemplate($organism, $file);
        $submittersFinal = [];
        /** @var User $submitter */
        foreach ($submitters as $submitter) {
            //enforce that we put submitters that need to do something check on role when document is generated and roleId when document is uploaded only
            if (in_array($submitter['role'], array_column($template['submitters'], 'name')) || in_array($submitter['roleId'], array_column($template['submitters'], 'name'))) {
                $submittersFinal[] = [
                    "role" => $submitter["role"],
                    "name" => $submitter["roleId"] == 'attendee' ? $submitter["user"]->getDisplayName() : $submitter["user"]->getName(),
                    "email" => $submitter["user"]->getEmail(),
                    "phone" => $submitter["user"]->getPhoneNumber() ? "+33" . substr($submitter["user"]->getPhoneNumber(), 1) : null, //E.164 standard
                    "send_sms" => false,
                    "send_email" => false,
                    "external_id" => $submitter["roleId"] . '_' . $submitter["user"]->getId()
                ];
            }
        }

        //special treatment for owner maybe we pre-fill content check on role when document is generated and roleId when document is uploaded only
        if (in_array($organism->getName(), array_column($template['submitters'], 'name')) || in_array('owner', array_column($template['submitters'], 'name'))) {
            $ownerSubmitter = [
                "role" => $organism->getName(),
                "name" => $organism->getOwnedBy()->getName(),
                "email" => $organism->getOwnedBy()->getEmail(),
                "phone" => $organism->getOwnedBy()->getPhoneNumber() ? "+33" . substr($organism->getOwnedBy()->getPhoneNumber(), 1) : null, //E.164 standard
                "send_sms" => false,
                "send_email" => false,
                "external_id" => 'owner_' . $organism->getOwnedBy()->getId()
            ];
            $submittersFinal[] = $ownerSubmitter;
        }

        if (sizeof($submittersFinal) == 0) {
            $this->deleteTemplate($organism, $template['id']);
            throw new WedofBadRequestHttpException("Le document n'a pas le ou les champs de signature requis");
        }

        $options = [
            "method" => "POST",
            'parameters' => [
                "template_id" => $template['id'],
                "submitters" => $submittersFinal,
                "send_sms" => false,
                "send_email" => false,
                "order" => "preserved",
                "expire_at" => null
            ],
            "authenticationHeaderName" => "X-Auth-Token",
            "authenticationHeaderValue" => $this->getApiKey($organism)
        ];
        return $this->sendRequest(Tools::getEnvValue("DOCUSEAL_BASE_URL") . "/api/submissions", $options);
    }

    /**
     * @param RegistrationFolderFile|CertificationFolderFile|CertificationPartnerFile $entityFile
     * @param User| Attendee $user
     * @return array|true[]|null
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function retrieveSubmitter(Organism $organism, $entityFile, $user): ?array
    {
        $options = [
            "method" => "GET",
            "authenticationHeaderName" => "X-Auth-Token",
            "authenticationHeaderValue" => $this->getApiKey($organism)
        ];
        $result = $this->sendRequest(Tools::getEnvValue("DOCUSEAL_BASE_URL") . "/api/submissions/" . $entityFile->getLink(), $options);
        $role = $user instanceof Attendee ? $user->getDisplayName() : ($user->getMainOrganism() === $organism ? $organism->getName() : $user->getMainOrganism()->getName());
        $submitter = Tools::array_find_returns_array($result['submitters'], function (array $submitter) use ($role, $user) {
            return
                (($role == 'attendee' && $submitter['role'] === $role) || ($role != 'attendee'))
                && (int)(explode('_', $submitter['external_id']))[1] == $user->getId();
        });
        return !empty($submitter) ? $submitter[0] : null;
    }

    /**
     * @param RegistrationFolderFile|CertificationFolderFile|CertificationPartnerFile $entityFile
     * @return DocumentSignedStates
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function checkSubmissionSignedState($entityFile): DocumentSignedStates
    {
        $organism = self::getOrganismFromEntityFile($entityFile);
        $options = [
            "method" => "GET",
            "authenticationHeaderName" => "X-Auth-Token",
            "authenticationHeaderValue" => $this->getApiKey($organism)
        ];
        $result = $this->sendRequest(Tools::getEnvValue("DOCUSEAL_BASE_URL") . "/api/submissions/" . $entityFile->getLink(), $options);
        $completed = 0;
        $ownerSubmitter = null;
        foreach ($result['submitters'] as $submitter) {
            if ($submitter['status'] == 'completed') {
                $completed++;
            }
            if ($submitter['role'] === $organism->getName()
                && $submitter['status'] != 'completed') {
                $ownerSubmitter = $submitter;
            }
        }
        //owner is the last to do maybe autocomplete
        if ($ownerSubmitter && $completed == count($result['submitters']) - 1) {
            $fields = [];
            $autoCompleted = true;
            $template = $this->getTemplate($organism, $result['template']['id']);
            $roleIndex = in_array($organism->getName(), array_column($template['submitters'], 'name'));
            $ownerFields = array_filter($template['fields'], fn(array $field) => $field['submitter_uuid'] === $template['submitters'][$roleIndex]['uuid']);
            $application = $this->applicationService->getByOrganismAndAppId($organism, SignatureWedofApplication::getAppId(), true, false);
            $ownerSubmitter = $this->getSubmitter($organism, $ownerSubmitter['id']);
            foreach (['Signature' => 'signature', 'Paraphe' => 'initials', 'Tampon' => 'stamp'] as $fieldName => $fieldId) {
                $alreadyExistingValue = null;
                $fieldAutoValue = in_array($fieldName, array_column($ownerFields, 'name'));
                if ($fieldAutoValue && !empty($application->getMetadata()[$fieldId])) {
                    $fieldAutoExistIndex = array_search($fieldName, array_column($ownerSubmitter['values'], 'field'));
                    if ($fieldAutoExistIndex !== false && !empty($ownerSubmitter['values'][$fieldAutoExistIndex]['value'])) {
                        $alreadyExistingValue = $ownerSubmitter['values'][$fieldAutoExistIndex]['value'];
                    }
                    $fields[] = [
                        "name" => $fieldName,
                        "default_value" => $alreadyExistingValue ?: $application->getMetadata()[$fieldId],
                        "readonly" => true
                    ];
                } else if ($fieldAutoValue && empty($application->getMetadata()[$fieldId])) {
                    $autoCompleted = false; //missing a value, disable auto complete on webhook
                }
            }
            $finish = $this->updateSubmitter($organism, $ownerSubmitter['id'], ["completed" => $autoCompleted, "fields" => $fields]);
            if (!empty($finish['id']) && $autoCompleted) {
                return DocumentSignedStates::COMPLETED();
            }
        }
        return $completed == 0 ? DocumentSignedStates::NONE() : ($completed < count($result['submitters']) ? DocumentSignedStates::PARTIALLY() : DocumentSignedStates::COMPLETED());
    }

//----------------
// METHODES PRIVES
//----------------
    /**
     * @throws ORMException
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     * @throws WedofConnectionException
     * @throws OptimisticLockException
     * @throws TransportExceptionInterface
     * @throws Throwable
     * @throws ServerExceptionInterface
     * @throws NonUniqueResultException
     * @throws ErrorException
     * @throws NoResultException
     */
    private function createTemplate(Organism $organism, UploadedFile $file): array
    {
        $data = file_get_contents($file->getPath() . "/" . $file->getFilename());
        $encoded = base64_encode($data);
        $options = [
            "method" => "POST",
            'parameters' => [
                "name" => $file->getClientOriginalName(),
                "file" => $encoded
            ],
            "authenticationHeaderName" => "X-Auth-Token",
            "authenticationHeaderValue" => $this->getApiKey($organism)
        ];

        $response = $this->sendRequest(Tools::getEnvValue("DOCUSEAL_BASE_URL") . "/api/templates/pdf", $options);
        if (isset($response['id'])) {
            $submitters = $response['submitters'];
            usort($submitters, function ($a, $b) {
                $aOrder = $a['name'] == 'owner' ? 1 : ($a['name'] == 'attendee' ? 2 : 3);
                $bOrder = $b['name'] == 'owner' ? 1 : ($b['name'] == 'attendee' ? 2 : 3);
                return $aOrder < $bOrder;
            });
            $fields = $response['fields'];
            foreach ($submitters as $submitter) {
                array_unshift($fields, [
                    'uuid' => uuid_create(),
                    'submitter_uuid' => $submitter['uuid'],
                    'name' => "",
                    'area' => [],
                    'type' => 'phone',
                    'required' => true
                ]);
            }
            $options = [
                "method" => "PUT",
                'parameters' => [
                    "submitters" => $submitters,
                    "fields" => $fields
                ],
                "authenticationHeaderName" => "X-Auth-Token",
                "authenticationHeaderValue" => $this->getApiKey($organism)
            ];
            $result = $this->sendRequest(Tools::getEnvValue("DOCUSEAL_BASE_URL") . "/api/templates/" . $response['id'], $options);
            if ($result['id']) {
                return $this->getTemplate($organism, $result['id']);
            }
        }
        throw new WedofBadRequestHttpException("Le modèle de document à signer est incorrect.");
    }

    /**
     * @param Organism $organism
     * @param int $id
     * @return array|null
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    private function getTemplate(Organism $organism, int $id): ?array
    {
        $options = [
            "method" => "GET",
            "authenticationHeaderName" => "X-Auth-Token",
            "authenticationHeaderValue" => $this->getApiKey($organism)
        ];
        return $this->sendRequest(Tools::getEnvValue("DOCUSEAL_BASE_URL") . "/api/templates/" . $id, $options);
    }

    /**
     * @param Organism $organism
     * @param int $id
     * @return array|null
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    private function deleteTemplate(Organism $organism, int $id): ?array
    {
        $options = [
            "method" => "DELETE",
            "authenticationHeaderName" => "X-Auth-Token",
            "authenticationHeaderValue" => $this->getApiKey($organism),
        ];
        return $this->sendRequest(Tools::getEnvValue("DOCUSEAL_BASE_URL") . "/api/templates/" . $id, $options);
    }

    /**
     * @param RegistrationFolderFile|CertificationFolderFile|CertificationPartnerFile $entityFile
     * @throws ClientExceptionInterface
     * @throws ContainerExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws NotFoundExceptionInterface
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function setSignedFile($entityFile)
    {
        $options = [
            "method" => "GET",
            'parameters' => [
                "include" => "combined_document_url",
            ],
            "authenticationHeaderName" => "X-Auth-Token",
            "authenticationHeaderValue" => Tools::getEnvValue("DOCUSEAL_MASTER_TOKEN")
        ];
        $response = $this->sendRequest(Tools::getEnvValue("DOCUSEAL_BASE_URL") . "/api/submissions/" . $entityFile->getLink(), $options);
        $options = [
            "method" => "GET",
            "useDownload" => true
        ];
        $response = $this->sendRequest($response['combined_document_url'], $options);
        $uuid = uuid_create();
        $dir = sys_get_temp_dir() . '/' . $uuid;
        mkdir($dir);
        $fileName = filter_var($entityFile->getFileName(), FILTER_SANITIZE_SPECIAL_CHARS);
        $path = $dir . '/' . $fileName . '.pdf';
        file_put_contents($path, $response['content']);
        $newFile = new File($path);
        $newUploadedFile = new UploadedFile($newFile->getRealPath(), $newFile->getFilename(), $newFile->getMimeType(), null, true);
        $entityFile->setLink(null);
        $entityFile->setFilePath(null);
        $entityFile->setFileType(null);
        $entityFile->setFileName(null);
        $entityFile->setFile($newUploadedFile);
        /** @var RegistrationFolderFileService|CertificationFolderFileService|CertificationPartnerFileService $entityFileService */
        $entityFileService = $this->container->get('App\Service\\' . (explode('Entity\\', get_class($entityFile))[1]) . 'Service');
        $entityFileService->save($entityFile);
        if ((new FilesystemIterator($dir))->valid()) {
            unlink($path);
        }
        rmdir($dir);
    }

    /**
     * @param Organism $organism
     * @return bool
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function addTenant(Organism $organism): bool
    {
        $connection = $organism->getConnectionForDataProvider($this->dataProvider);
        if (!$connection) {
            $connection = $this->connectionService->create($organism, $this->dataProvider);
            $options = [
                'authenticationHeaderName' => $_ENV['KASTORR_AUTH_HEADER'],
                'authenticationHeaderValue' => $_ENV['KASTORR_AUTH'],
                'parameters' => [
                    'siret' => $organism->getSiret(),
                ],
                'method' => 'POST',
            ];
            $data = $this->sendRequest($_ENV['KASTORR_BASE_URI'] . 'docuseal-add-tenant', $options);
            if (isset($data['apiKey'])) {
                $connection->setCredentials($data);
                $connection->setState(ConnectionStates::ACTIVE());
                $connection->setIsTosValidated(true);
                $connection->setExpiresOn((new DateTime())->setDate(2199, 12, 31));
                $connection->setRefreshAt((new DateTime())->setDate(2199, 12, 31));
                $this->connectionService->save($connection);
            } else {
                throw new WedofConnectionException("Enable connection backend signature error");
            }
        } else {
            $connection->setState(ConnectionStates::ACTIVE());
            $connection->setIsTosValidated(true);
            $connection->setExpiresOn((new DateTime())->setDate(2199, 12, 31));
            $connection->setRefreshAt((new DateTime())->setDate(2199, 12, 31));
            $this->connectionService->save($connection);
        }
        return true;
    }

    /**
     * @param Organism $organism
     * @return string
     * @throws WedofConnectionException
     */
    private function getApiKey(Organism $organism): string
    {
        $connection = $organism->getConnectionForDataProvider($this->dataProvider);
        if (!$connection || $connection->getState() != ConnectionStates::ACTIVE()->getValue()) {
            throw new WedofConnectionException("Connection to backend signature isn't active");
        }
        return $connection->getCredentials()['apiKey'];
    }

    /**
     * @throws ORMException
     * @throws \Doctrine\ORM\ORMException
     * @throws ClientExceptionInterface
     * @throws OptimisticLockException
     * @throws Throwable
     * @throws ServerExceptionInterface
     * @throws NonUniqueResultException
     * @throws RedirectionExceptionInterface
     * @throws WedofConnectionException
     * @throws TransportExceptionInterface
     * @throws ReflectionException
     * @throws ErrorException
     * @throws NoResultException
     */
    public function setToSignIfRequired(Organism $organism, $entity, $entityFile, $uploadedFile, $fileType)
    {
        if ($this->accessService->isApplicationAllowedAndEnabled(SignatureWedofApplication::getAppId(), $organism)) {
            $submitters = [];
            if (!empty($fileType['allowSignAttendee'])) {
                $submitters = array_merge($submitters, [[
                    "user" => $entity->getAttendee(),
                    "role" => $entity->getAttendee()->getDisplayName(),
                    "roleId" => "attendee"
                ]]);
            }
            if (!empty($fileType['allowSignPartner']) && $entity->getPartner()) {
                if ($entity->getPartner()->getOwnedBy()) {
                    $submitters = array_merge($submitters, [[
                        "user" => $entity->getPartner()->getOwnedBy(),
                        "role" => $entity->getPartner()->getName(),
                        "roleId" => "partner"
                    ]]);
                } else {
                    throw new WedofBadRequestHttpException("Erreur, afin de pouvoir générer le document à signer, l'organisme partenaire doit disposer d'un compte Wedof");
                }
            }
            if (!empty($submitters)) {
                $result = $this->createSubmission($organism, $uploadedFile, $submitters);
                $fileSignUrl = $result[0]['submission_id']; //will allow dynamic sign link managed by Wedof using user authentication
                $entityFile->setFile();
                $entityFile->setLink($fileSignUrl);
                $entityFile->setFilePath($fileSignUrl);
                $entityFile->setFileType("sign");
                $entityFile->setSignedState(DocumentSignedStates::NONE()->getValue());
                $entityFile->setFileName(htmlspecialchars_decode($uploadedFile->getClientOriginalName(), ENT_QUOTES));
                return $entityFile;
            }
        }
        return $entityFile;
    }

    /**
     * @param $entityFile
     * @param array|null $submitter
     * @return string[]
     * @throws Exception
     */
    public
    function displayEmbeddedSignForm($entityFile, ?array $submitter): array
    {
        if (!$submitter) {
            return ['html' => '<div class="w-full h-full content-center">
                                            <h3 class="text-center">Document en attente de signature</h3>
                                            <div class="mat-ripple mat-button-ripple"></div>
                                            <div class="mat-button-focus-overlay"></div>
                                     </div>'];
        } else if ($submitter['status'] == 'completed' && $entityFile->getSignedState() !== DocumentSignedStates::COMPLETED()->getValue()) {
            return ['html' => '<div class="w-full h-full content-center">
                                            <h3 class="text-center">Document signé le ' . (new DateTime($submitter['completed_at']))->setTimezone(new DateTimeZone('Europe/Paris'))->format('d/m/y H:i') . '<br/> En attente de l\'ensemble des signatures</h3>
                                            <div class="mat-ripple mat-button-ripple"></div>
                                            <div class="mat-button-focus-overlay"></div>
                                     </div>'];
        } else {
            $i18n = ['form_has_been_completed' => 'Document signé avec succès'];
            return ['html' => self::getEmbedCode($submitter['slug'], $i18n)];
        }
    }

    /**
     * @param string $submission_id
     * @return CertificationFolderFile|CertificationPartnerFile|RegistrationFolderFile|null
     */
    public
    function findEntityFileFromSubmissionId(string $submission_id)
    {
        $entityFile = $this->registrationFolderFileRepository->findOneBy(["fileType" => "sign", "link" => $submission_id]);
        if (!$entityFile) {
            $entityFile = $this->certificationFolderFileRepository->findOneBy(["fileType" => "sign", "link" => $submission_id]);
        }
        if (!$entityFile) {
            $entityFile = $this->certificationPartnerFileRepository->findOneBy(["fileType" => "sign", "link" => $submission_id]);
        }
        return $entityFile;
    }

    /**
     * @param $entityFile
     * @return void
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function refreshEntityFileSignedStateAndSendEvents($entityFile)
    {
        $refresh = false;
        $signedState = $this->checkSubmissionSignedState($entityFile);
        if ($signedState != $entityFile->getSignedState()) {
            $refresh = true;
            $entityFile->setSignedState($signedState);
            if (DocumentSignedStates::COMPLETED()->equals($signedState) && $entityFile->getLink()) {
                $this->setSignedFile($entityFile);
            }
        }
        if ($refresh) {
            $entityClass = get_class($entityFile);
            /** @var RegistrationFolderFileService|CertificationFolderFileService|CertificationPartnerFileService $entityFileService */
            $entityFileService = $this->container->get('App\Service\\' . (explode('Entity\\', $entityClass)[1]) . 'Service');
            $eventConstant = constant('App\Event\\' . (explode('Entity\\', $entityClass)[1]) . '\\' . (explode('Entity\\', $entityClass)[1]) . "Events::FILE_SIGNATURE_" . strtoupper($signedState));

            if ($entityFile instanceof RegistrationFolderFile) {
                $this->registrationFolderFileRepository->save($entityFile);
                if ($eventConstant) {
                    $entityFileService->sendEventRegistrationFolderFile($entityFile, $eventConstant);
                }
            } else if ($entityFile instanceof CertificationFolderFile) {
                $this->certificationFolderFileRepository->save($entityFile);
                if ($eventConstant) {
                    $entityFileService->sendEventCertificationFolderFile($entityFile, $eventConstant);
                }
            } else if ($entityFile instanceof CertificationPartnerFile) {
                $this->certificationPartnerFileRepository->save($entityFile);
                if ($eventConstant) {
                    $entityFileService->sendEventCertificationPartnerFile($entityFile, $eventConstant);
                }
            }
        }
    }

    /**
     * @throws Throwable
     * @throws NonUniqueResultException
     * @throws NoResultException
     */
    public function addActivityOnEntity($entityFile, $data)
    {
        $i18n = [
            'form.viewed' => " ouvert par ",
            'form.started' => " - processus de signature commencé par ",
            'form.completed' => " signé par ",
            'form.declined' => " refusé par "
        ];
        $user = null;
        $eventTime = new DateTime($data['timestamp']);
        $userData = explode('_', $data['data']['external_id']);
        if (!empty($userData)) {
            if ($userData[0] === 'attendee') {
                $user = $this->attendeeService->getById($userData[1]);
            } else {
                $user = $this->userService->getById($userData[1]);
            }
        }
        if ($user) {
            $eventTitle = $entityFile->getFileName() . $i18n[$data['event_type']] . ($user instanceof Attendee ? $user->getDisplayName() : $user->getName());
            if ($entityFile instanceof RegistrationFolderFile) {
                $this->activityService->create([
                    'title' => $eventTitle,
                    'type' => ActivityTypes::from('file'),
                    'eventTime' => $eventTime,
                    'origin' => 'Wedof'
                ], null, $entityFile->getRegistrationFolder());
            } else if ($entityFile instanceof CertificationFolderFile) {
                $this->activityService->create([
                    'title' => $eventTitle,
                    'type' => ActivityTypes::from('file'),
                    'eventTime' => $eventTime,
                    'origin' => 'Wedof'
                ], null, $entityFile->getCertificationFolder());
                $this->certificationFolderFileRepository->save($entityFile);
            } else if ($entityFile instanceof CertificationPartnerFile) {
                $this->activityService->create([
                    'title' => $eventTitle,
                    'type' => ActivityTypes::from('file'),
                    'eventTime' => $eventTime,
                    'origin' => 'Wedof'
                ], null, $entityFile->getCertificationPartner());
            }
        }
    }

    /**
     * @param string $slug
     * @param array $i18n
     * @return string
     */
    public static function getEmbedCode(string $slug, array $i18n = []): string
    {
        return '<docuseal-form
                style="height:50%; display:block;"
                id="signForm"
                data-allow-to-resubmit="false"
                data-with-send-copy-button="false"
                data-with-download-button="false"
                data-i18n=\'' . json_encode($i18n) . '\'
                data-with-title="false"
                data-src="' . Tools::getEnvValue("DOCUSEAL_BASE_URL") . '/s/' . $slug . '"
                data-custom-css="#expand_form_button, #submit_form_button, #type_text_button { border:none; border-radius:4px; } #expand_form_button[disabled], #submit_form_button[disabled] { background-color: #97a6ba61; color: #97a6ba; } #expand_form_button, #submit_form_button { background-color: #5850ec; color: #f0f5ff; } #expand_form_button:hover, #submit_form_button:not([disabled]):hover { background-color: #5648d6;} #type_text_button, .tooltip > label.btn { background-color: #e5e7eb; color: #27303f; border:none; border-radius:4px; } #type_text_button:hover, .tooltip > label.btn:hover { background-color: #d1d3d8; } #form_container {box-shadow:none; background-color:#fff; border-color:#e2e8f0; border-radius: 4px;} .tooltip { --tooltip-color:#3c4452; --tooltip-text-color:#FFF } :host { --b3:214 20% 66%; --rounded-btn: 6px; }">
        </docuseal-form>';
    }

    /**
     * @throws Exception
     */
    private static function getOrganismFromEntityFile($entityFile)
    {
        if ($entityFile instanceof RegistrationFolderFile) {
            return $entityFile->getRegistrationFolder()->getOrganism();
        } else if ($entityFile instanceof CertificationFolderFile) {
            return $entityFile->getCertificationFolder()->getCertifier();
        } else if ($entityFile instanceof CertificationPartnerFile) {
            return $entityFile->getCertificationPartner()->getCertifier();
        }
        throw new Exception("");
    }

    /**
     * @throws ORMException
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     * @throws WedofConnectionException
     * @throws OptimisticLockException
     * @throws TransportExceptionInterface
     * @throws Throwable
     * @throws ServerExceptionInterface
     * @throws NonUniqueResultException
     * @throws NoResultException
     * @throws ErrorException
     */
    private function updateSubmitter($organism, $id, $parameters): array
    {
        $options = [
            "method" => "PUT",
            "authenticationHeaderName" => "X-Auth-Token",
            "authenticationHeaderValue" => $this->getApiKey($organism),
            "parameters" => $parameters
        ];
        return $this->sendRequest(Tools::getEnvValue("DOCUSEAL_BASE_URL") . "/api/submitters/" . $id, $options);
    }

    /**
     * @throws ORMException
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     * @throws WedofConnectionException
     * @throws OptimisticLockException
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws NonUniqueResultException
     * @throws NoResultException
     * @throws ErrorException
     */
    private function getSubmitter(?Organism $organism, $id): ?array
    {
        $options = [
            "method" => "GET",
            "authenticationHeaderName" => "X-Auth-Token",
            "authenticationHeaderValue" => $this->getApiKey($organism),
        ];
        return $this->sendRequest(Tools::getEnvValue("DOCUSEAL_BASE_URL") . "/api/submitters/" . $id, $options);
    }

    /**
     * @param $fieldUrl
     * @return false|string
     */
    public function downloadFieldFile($fieldUrl)
    {
        $array = array('http' => array(
            'header' => 'X-Auth-Token: ' . Tools::getEnvValue('DOCUSEAL_MASTER_TOKEN') . "\r\n",
            'method' => "GET",
            "ignore_errors" => true
        ));
        $context = stream_context_create($array);
        return @file_get_contents($fieldUrl, false, $context);
    }
}
