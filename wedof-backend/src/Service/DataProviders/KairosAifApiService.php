<?php

namespace App\Service\DataProviders;

use App\Entity\Certification;
use App\Entity\Connection;
use App\Entity\Organism;
use App\Entity\RegistrationFolder;
use App\Entity\RegistrationFolderReason;
use App\Entity\Session;
use App\Entity\Training;
use App\Entity\TrainingAction;
use App\Entity\User;
use App\Event\Connection\ConnectionEvents;
use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofConnectionException;
use App\Library\utils\enums\AttendeeGender;
use App\Library\utils\enums\ConnectionStates;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\enums\RegistrationFolderAttendeeStates;
use App\Library\utils\enums\RegistrationFolderBillingStates;
use App\Library\utils\enums\RegistrationFolderStates;
use App\Library\utils\enums\SessionStates;
use App\Library\utils\enums\SubscriptionTrainingTypes;
use App\Library\utils\enums\TrainingActionStates;
use App\Library\utils\enums\TrainingStates;
use App\Library\utils\Tools;
use App\Repository\EndPointStatusRepository;
use App\Service\CertificationService;
use App\Service\ConfigService;
use App\Service\ConnectionService;
use App\Service\CronTaskManagerService;
use App\Service\RegistrationFolderService;
use App\Service\SessionService;
use App\Service\UserService;
use Datetime;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use DOMDocument;
use DOMXPath;
use ErrorException;
use Exception;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ContainerInterface as Container;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\RateLimiter\RateLimiterFactory;
use Symfony\Component\Security\Core\Security;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Throwable;

class KairosAifApiService extends BaseApiService implements InterfaceRegistrationFolderApiService, InterfaceCatalogApiService
{
    const EXTERNAL_ID_PREFIX = 'AIF-';
    const DELAY_BEFORE_REJECTED_WITHOUT_TITULAIRE_SUITE = 60;
    private SessionService $sessionService;
    private RegistrationFolderService $registrationFolderService;
    private CertificationService $certificationService;
    private Container $container;
    private array $_cachedResponses = [];
    private UserService $userService;

    /**
     * @param ConnectionService $connectionService
     * @param RequestStack $requestStack
     * @param ConfigService $configService
     * @param EventDispatcherInterface $dispatcher
     * @param LoggerInterface $logger
     * @param EndPointStatusRepository $endPointStatusRepository
     * @param SessionService $sessionService
     * @param RegistrationFolderService $registrationFolderService
     * @param CertificationService $certificationService
     * @param Container $container
     * @param Security $security
     * @param RateLimiterFactory $kairosAifApiLimiter
     * @param UserService $userService
     */
    public function __construct(ConnectionService         $connectionService,
                                RequestStack              $requestStack,
                                ConfigService             $configService,
                                EventDispatcherInterface  $dispatcher,
                                LoggerInterface           $logger,
                                EndPointStatusRepository  $endPointStatusRepository,
                                SessionService            $sessionService,
                                RegistrationFolderService $registrationFolderService,
                                CertificationService      $certificationService,
                                Container                 $container,
                                Security                  $security,
                                RateLimiterFactory        $kairosAifApiLimiter,
                                UserService               $userService
    )
    {
        parent::__construct(DataProviders::KAIROS_AIF(), $configService, $connectionService, $requestStack, $endPointStatusRepository, $dispatcher, $logger, $security);
        $this->sessionService = $sessionService;
        $this->registrationFolderService = $registrationFolderService;
        $this->certificationService = $certificationService;
        $this->container = $container;
        $this->rateLimiter = $kairosAifApiLimiter;
        $this->userService = $userService;
    }

    /**
     * @param array $options
     * @return array
     * @throws \Symfony\Component\Mailer\Exception\TransportExceptionInterface
     */
    public function setAuthentication(array $options = []): array
    {
        if ($options['organism']) {
            /** @var Organism $organism */
            $organism = $options['organism'];
            $connection = $organism->getConnectionForDataProvider($this->dataProvider);
            if ($this->connectionService->hasAccess($organism, $this->dataProvider)) {
                $credentials = $connection->getCredentials();
                $options["headers"] = array_merge($options['headers'], ["Authorization" => "Bearer " . $credentials["tokenKairosNew"],]);
            }
            $options["headers"] = array_merge($options['headers'], ["Referer" => "https://www.portail-emploi.fr/ihm-kairos/", "Sec-Fetch-Dest" => "empty", "Sec-Fetch-Mode" => "cors", "Sec-Fetch-Site" => "same-origin", "User-Agent" => "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", 'sec-ch-ua' => '"Not.A/Brand";v="8", "Chromium";v="114", "Google Chrome";v="114"', 'sec-ch-ua-mobile' => "?0", 'sec-ch-ua-platform' => "macOS",]);
        }

        return $options;
    }

    /**
     * @return bool
     */
    public function requiresAuthentication(): bool
    {
        return true;
    }

    /**
     * @param Connection $connection
     * @return string
     */
    public function getUsername(Connection $connection): string
    {
        return $connection->getCredentials()['username'] ?? '';
    }

    /**
     * @param Connection $connection
     * @param bool $checkOrganismAccess
     * @return array
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     * @throws Throwable
     */
    public function authenticate(Connection $connection, bool $checkOrganismAccess = true): array
    {
        return ["hasAccess" => false, "hasOrganismAccess" => false, "expiresOn" => null, "refreshAt" => null, "lastRefresh" => new DateTime('now'), "errorCode" => "disabled"];
        $credentials = $connection->getCredentials();
        try {
            $data = $this->authenticate_step_1();
            if ($data) {
                $cookie = $data['cookie'];
                $data = $this->authenticate_step_2($credentials, $data['formdata'], $cookie);
                if ($data) {
                    if ($data['siret'] == $connection->getOrganism()->getSiret()) {
                        //final auth kairos
                        $data_kairos = $this->authenticate_step_3($data['url_kairos'], $cookie);
                        $url = $data_kairos['url'];
                        unset($data_kairos['url']);
                        //kairos old
                        $authData = $this->authenticate_step_4($url, $data_kairos, $cookie);
                        if ($authData) {
                            $authData['viewState'] = $this->initViewState($authData, $data_kairos);
                        }
                        //kairos new
                        $authData['tokenKairosNew'] = $this->authenticate_step_5($data['url_kairos_new'], $cookie);
                        $response = $this->sendRequest(Tools::getEnvValue("KAIROSAIF_NEW_API_FORMATION") . "token/partenaire", ['access_token' => $authData['tokenKairosNew']]);
                        if ($response['codeOrganisme'] == $connection->getOrganism()->getSiret()) {
                            $connection->setCredentials(array_merge($credentials, $authData));
                            $this->connectionService->save($connection);

                            return ["hasAccess" => true, "hasOrganismAccess" => true, "expiresOn" => (new DateTime('now'))->modify('+29min'), //to change ?
                                "refreshAt" => (new DateTime('now'))->modify('+29min'), "lastRefresh" => new DateTime('now')];
                        } else {
                            return ["hasAccess" => true, "hasOrganismAccess" => false, "expiresOn" => null, "refreshAt" => null, "lastRefresh" => new DateTime('now'), "errorCode" => "kairos-new"];
                        }
                    } else {
                        return ["hasAccess" => true, "hasOrganismAccess" => false, "expiresOn" => null, "refreshAt" => null, "lastRefresh" => new DateTime('now'), "errorCode" => "siret"];
                    }
                }
            }
        } catch (Exception $e) {
            $this->logger->error($e->getMessage());
            $errorCode = "credentials";
            if (str_contains($e->getMessage(), "Votre mot de passe est expiré.")) {
                $errorCode = "reset-password";
            }
            //@todo update le mot de passe manuellement ?
            return ["hasAccess" => false, "hasOrganismAccess" => false, "expiresOn" => null, "refreshAt" => null, "lastRefresh" => new DateTime('now'), "errorCode" => $errorCode];
        }
        return ["hasAccess" => false, "hasOrganismAccess" => false, "expiresOn" => null, "refreshAt" => null, "lastRefresh" => new DateTime('now'), "errorCode" => "credentials"];
    }

    /**
     * @param Organism $organism
     * @return bool
     */
    public function setActiveOrganism(Organism $organism): bool
    {
        return true;
    }

    /**
     * @return int
     */
    public function getMaxAttemptsBeforeStop(): int
    {
        return 2;
    }

    /**
     * @param Organism $organism
     * @param Connection $connection
     * @param array $params
     * @return bool
     */
    public function habilitate(Organism $organism, Connection $connection, array $params): bool
    {
        return false;
    }

    /**
     * @param Organism $organism
     * @param array|null $params
     * @return false
     */
    public function checkBeforeHabilitate(Organism $organism, array $params = null): bool
    {
        return false;
    }

    /**
     * @param string $trainingId
     * @return string
     */
    public function getTrainingExternalId(string $trainingId): string
    {
        return $trainingId;
    }

    /**
     * @param string $trainingId
     * @param string $trainingActionId
     * @return string
     */
    public function getTrainingActionExternalId(string $trainingId, string $trainingActionId): string
    {
        return $trainingActionId;
    }

    /**
     * @param string $trainingId
     * @param string $trainingActionId
     * @param string $sessionId
     * @return string
     */
    public function getSessionExternalId(string $trainingId, string $trainingActionId, string $sessionId): string
    {
        return $sessionId;
    }

    /**
     * @param array $registrationFolderRawData
     * @return array
     */
    public function getTrainingRawDataFromRawData(array $registrationFolderRawData): array
    {
        $rawData = [];
        $rawData["title"] = $registrationFolderRawData['trainingActionInfo']['title'];
        $rawData["id"] = $registrationFolderRawData['trainingId'];
        $rawData["externalId"] = $registrationFolderRawData['trainingId'];
        $rawData["trainingId"] = $registrationFolderRawData['trainingId'];
        $rawData["statusCode"] = 0;
        $rawData["statusLabel"] = 'published';
        $rawData["dataProvider"] = $registrationFolderRawData['type'];
        $rawData["totalTvaTTC"] = null; // Info inutile au niveau de la formation, non ?
        $rawData["certifInfo"] = $registrationFolderRawData["certifInfo"];
        $rawData["type"] = null;
        $rawData["code"] = null;
        $rawData["numeroInterCarifFormation"] = $registrationFolderRawData["numeroInterCarifFormation"];

        return $rawData;
    }

    /**
     * @param array $rawData
     * @param Certification|null $certification
     * @return TrainingStates
     */
    public function getTrainingState(array $rawData, ?Certification $certification): TrainingStates
    {
        return !empty($rawData['statusLabel']) ? TrainingStates::from($rawData['statusLabel']) : TrainingStates::PUBLISHED();
    }

    /**
     * @param array $rawData
     * @param Training $training
     * @return TrainingActionStates
     */
    public function getTrainingActionState(array $rawData, Training $training): TrainingActionStates
    {
        return !empty($rawData['statusLabel']) ? TrainingActionStates::from($rawData['statusLabel']) : TrainingActionStates::PUBLISHED();
    }

    /**
     * @param array $rawData
     * @param TrainingAction $trainingAction
     * @return SessionStates
     */
    public function getSessionState(array $rawData, TrainingAction $trainingAction): SessionStates
    {
        return !empty($rawData['statusLabel']) ? SessionStates::from($rawData['statusLabel']) : SessionStates::PUBLISHED();
    }

    /**
     * @param array $registrationFolderRawData
     * @return Certification|null
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function retrieveCertificationFromRawData(array $registrationFolderRawData): ?Certification
    {
        if (!empty($registrationFolderRawData['certifInfo'])) {
            $params = ['certifInfo' => $registrationFolderRawData['certifInfo']];
            $certification = $this->certificationService->getCertification($params);
        } else {
            $certification = null;
        }
        return $certification;
    }

    /**
     * @param array $registrationFolderRawData
     * @return array
     */
    public function getTrainingActionRawDataFromRawData(array $registrationFolderRawData): array
    {
        $rawData = [];
        $rawData["id"] = $registrationFolderRawData['trainingActionId'];
        $rawData["externalId"] = $registrationFolderRawData['trainingActionId'];
        $rawData["trainingId"] = $registrationFolderRawData['trainingId'];
        $rawData['totalTvaTTC'] = $registrationFolderRawData['trainingActionInfo']['totalIncl'];
        $rawData["statusCode"] = $registrationFolderRawData['statusCode'];
        $rawData["statusLabel"] = $registrationFolderRawData['statusLabel'];
        $rawData["numeroInterCarifAction"] = $registrationFolderRawData["numeroInterCarifAction"];
        $rawData["averageLearningTime"] = null; // TODO si ya moyen de renseigner

        return $rawData;
    }

    /**
     * @param array $registrationFolderRawData
     * @return array
     */
    public function getSessionRawDataFromRawData(array $registrationFolderRawData): array
    {
        $rawData = [];
        $rawData["externalId"] = $registrationFolderRawData['trainingActionInfo']['sessionId'];
        $rawData["trainingId"] = $registrationFolderRawData['trainingId'];
        $rawData["actionId"] = $registrationFolderRawData['trainingActionId'];
        $rawData["id"] = $registrationFolderRawData['trainingActionInfo']['sessionId'];
        $rawData["endDate"] = $registrationFolderRawData['trainingActionInfo']['sessionEndDate'];
        $rawData["beginDate"] = $registrationFolderRawData['trainingActionInfo']['sessionStartDate'];
        $rawData["statusCode"] = $registrationFolderRawData['statusCode'];
        $rawData["statusLabel"] = $registrationFolderRawData['statusLabel'];
        $rawData["numeroInterCarifSession"] = $registrationFolderRawData["numeroInterCarifSession"];

        return $rawData;
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @return string
     */
    public function getRegistrationFolderExternalLink(RegistrationFolder $registrationFolder): string
    {
        return 'https://www.portail-emploi.fr/portail-tap/mireconnexion';//why not bis
    }

    /**
     * @param TrainingAction $trainingAction
     * @return string
     */
    public function getTrainingActionExternalLink(TrainingAction $trainingAction): string
    {
        return 'https://candidat.pole-emploi.fr/formations/detail/' . $trainingAction->getExternalId();
    }

    /**
     * @param string $externalId
     * @return string
     */
    public function getSiretFromExternalId(string $externalId): string
    {
        return ''; // TODO si c'est possible. ça semble compliqué, pour chaque appel kairos on a besoin de l'organisme
    }

    /**
     * @param Organism $organism
     * @param string $registrationFolderId
     * @param array|null $registrationFolderDataToMerge
     * @param string|null $overrideCurrentBillingState
     * @return array|null
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function getRegistrationFolderRawData(Organism $organism, string $registrationFolderId, array $registrationFolderDataToMerge = null, string $overrideCurrentBillingState = null): ?array
    {
        $kairosId = self::getKairosExternalId($registrationFolderId);
        $versionDevis = $kairosId[1] ?? 1;
        $try = 0;
        do {
            $result = $this->sendRequest(Tools::getEnvValue("KAIROSAIF_NEW_API_FORMATION") . "devis/liste",
                [
                    "parameters" => [
                        "siret" => $organism->getSiret(),
                        "numeroDevis" => $kairosId[0],
                        "listeStatut" => "0,1,2,3,4,5,6,7,8,9",
                        "listeType" => "AIF",
                        "versionDevis" => $versionDevis
                    ],
                    "organism" => $organism,
                    "cache" => true
                ]);
            $try++;
            $versionDevis++;
        } while (empty($result[0]) && $try !== 5);

        if (!empty($result[0])) {
            $rawData = $this->prepareRegistrationFolderRawData($organism, $result[0]);
            return $this->getRegistrationFolderDetailsRawData($organism, $rawData);
        } else {
            return [];
        }
    }

    /**
     * @param Organism $organism
     * @param array $parameters
     * @param array $options
     * @return array
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function getRegistrationFoldersRawData(Organism $organism, array $parameters = [], array $options = []): array
    {
        //reset cache;
        $this->_cachedResponses = [];

        $options = array_merge([
            "withDetails" => true,
            "onlyItems" => true,
            "requestDispatchMonitoringEvent" => true,
            "maxRetry" => Tools::getEnvValue('KAIROSAIF_API_MAX_RETRY')],
            $options);

        $limit = $parameters['skip'] !== 0 ? $parameters['skip'] + 19 : $parameters['limit']; // TODO bizarre ce 19 en dur, ça devrait pas être limit plutôt ?
        $skip = $parameters['skip'];

        $range = $skip . "-" . $limit; // max range between = 20. Exemple 0-19;

        $state = !isset($parameters['state']) ? "0,1,2,3,4,5,6,7,8,9" : $this->getKairosStateFromRegistrationFolderStates($parameters['state']);

        $parametersRequest = [
            "range" => $range
        ];

        if ($organism->getConnectionForDataProvider($this->dataProvider)->isInitialized()) {
            $lastUpdatedregistrationFolder = $this->registrationFolderService->getLastUpdatedByOrganismAndDataProvider($organism, $this->dataProvider);
            if ($lastUpdatedregistrationFolder->getLastUpdate()) {
                $parametersRequest["dateModificationDevis"] = $lastUpdatedregistrationFolder->getLastUpdate()->format('Y-m-d H:i:s');
            }
        }
        $criteresTri = "-dateModificationDevis";
        if ($parameters['state']->getValue() === RegistrationFolderStates::NOT_PROCESSED()->getValue()) {
            $criteresTri = "-dateCreationDevis";
        }
        if ($parameters['state']->getValue() === RegistrationFolderStates::ALL()->getValue()) {
            $criteresTri = "dateCreationDevis";
        }

        $result = $this->sendRequest(Tools::getEnvValue("KAIROSAIF_NEW_API_FORMATION") . "devis/liste", [
                "parameters" => array_merge([
                    "siretOrganisme" => $organism->getSiret(),
                    "listeStatut" => $state,
                    "listeType" => "AIF",
                    "criteresTri" => $criteresTri
                ], $parametersRequest),
                "organism" => $organism,
                "cache" => true
            ]
        );

        $registrationFoldersRawData = [];
        if ($options['withDetails']) {
            foreach ($result as $registrationFolder) {
                $this->logger->debug("[KairosAifApiService] registration folder details for : " . $registrationFolder["numeroDevis"]);
                $registrationFoldersRawData[] = $this->getRegistrationFolderRawData($organism, self::getKairosExternalIdWithVersionDevis($registrationFolder["numeroDevis"], $registrationFolder["versionDevis"]));
            }
        } else {
            foreach ($result as $registrationFolder) {
                $registrationFoldersRawData[] = $this->prepareRegistrationFolderRawData($organism, $registrationFolder);
            }
        }

        return $registrationFoldersRawData;
    }

    /**
     * Donne le nombre de registration folders existant pour un organisme
     *
     * @param Organism $organism
     * @param null $state
     * @param null $sort
     * @param array $options
     * @return int
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function getRegistrationFoldersCount(Organism $organism, $state = null, $sort = null, array $options = array()): int
    {
        $result = $this->sendRequest(Tools::getEnvValue("KAIROSAIF_NEW_API_FORMATION") . "devis/liste", [
                "parameters" => [
                    "siretOrganisme" => $organism->getSiret(),
                    "listeType" => "AIF",
                    "listeStatut" => "0,1,2,3,4,5,6,7,8,9",
                    "range" => "0-0",
                ],
                "organism" => $organism,
                "fullResponse" => true]
        );

        return explode("/", $result["headers"]["content-range"][0])[1];
    }

    /**
     * @param Organism|null $organism
     * @param string $siret
     * @param string $states
     * @return int
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function getRegistrationFoldersCountUsingKagilum(?Organism $organism, string $siret, string $states = "0,1,2,3,4,5,6,7,8,9"): int
    {
        $result = $this->sendRequest(Tools::getEnvValue("KAIROSAIF_NEW_API_FORMATION") . "devis/liste", [
                "parameters" => [
                    "siretOrganisme" => $siret,
                    "listeType" => "AIF",
                    "listeStatut" => $states,
                    "range" => "0-1",
                ],
                "organism" => $organism,
                "fullResponse" => true]
        );
        $range = explode("/", $result["headers"]["content-range"][0]);
        return $range[1] ?? 0;
    }

    /**
     * @param Session $session
     * @param array $rawData
     * @param Organism $organism
     * @return array
     */
    public function preCreateRegistrationFolder(Session $session, array $rawData, Organism $organism): array
    {
        return $rawData;
    }

    /**
     * @param array $rawData
     * @param DataProviders $dataProvider
     * @return string
     */
    public function generateRegistrationFolderId(array $rawData, DataProviders $dataProvider): string
    {
        // I think we cannot concatenate prefix here
        // Because we need to have it already in prepareRegistrationFolderRawData that maps Kairos -> Wedof raw data
        // So the $rawData received here already contains the prefix

        $externalId = Tools::removePrefix($rawData["id"], self::EXTERNAL_ID_PREFIX);

        return self::EXTERNAL_ID_PREFIX . $externalId;
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @param array $data
     * @return RegistrationFolder
     */
    public function validateRegistrationFolder(RegistrationFolder $registrationFolder, array $data = []): RegistrationFolder
    {
        throw new WedofBadRequestHttpException("Erreur, méthode non supportée");
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @return RegistrationFolder
     */
    public function acceptRegistrationFolder(RegistrationFolder $registrationFolder): RegistrationFolder
    {
        throw new WedofBadRequestHttpException("Erreur, méthode non supportée");
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @return RegistrationFolder
     */
    public function rejectRegistrationFolder(RegistrationFolder $registrationFolder): RegistrationFolder
    {
        throw new WedofBadRequestHttpException("Erreur, méthode non supportée");
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @return RegistrationFolder
     */
    public function attendeeRefuseRegistrationFolder(RegistrationFolder $registrationFolder): RegistrationFolder
    {
        throw new WedofBadRequestHttpException("Erreur, méthode non supportée");
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @return RegistrationFolder
     */
    public function attendeeCancelRegistrationFolder(RegistrationFolder $registrationFolder): RegistrationFolder
    {
        throw new WedofBadRequestHttpException("Erreur, méthode non supportée");
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @return RegistrationFolder
     */
    public function waitRegistrationFolder(RegistrationFolder $registrationFolder): RegistrationFolder
    {
        throw new WedofBadRequestHttpException("Erreur, méthode non supportée");
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @return RegistrationFolder
     */
    public function paidRegistrationFolder(RegistrationFolder $registrationFolder): RegistrationFolder
    {
        throw new WedofBadRequestHttpException("Erreur, méthode non supportée");
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @param Datetime $inTrainingDate
     * @param User|null $user
     * @return RegistrationFolder
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws Throwable
     * @throws \Doctrine\ORM\ORMException
     */
    public function inTrainingRegistrationFolder(RegistrationFolder $registrationFolder, DateTime $inTrainingDate, User $user = null): RegistrationFolder
    {
        $rawData = $this->addStateToRawData($registrationFolder->getRawData(), "Déclaration Entrée en Formation", RegistrationFolderStates::IN_TRAINING(), $registrationFolder->getOrganism());

        //override set date
        $rawData['states'] = array_merge($rawData['states'], [
            RegistrationFolderStates::IN_TRAINING()->getValue() => [
                'date' => $inTrainingDate->format('Y-m-d'),
            ]
        ]);

        return $this->registrationFolderService->createOrUpdate($rawData, DataProviders::from($registrationFolder->getType()), $registrationFolder->getExternalId());
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @param Datetime $terminatedDate
     * @param User|null $user
     * @return RegistrationFolder
     * @throws ErrorException
     * @throws ORMException
     * @throws Throwable
     * @throws \Doctrine\ORM\ORMException
     */
    public function terminateRegistrationFolder(RegistrationFolder $registrationFolder, DateTime $terminatedDate, User $user = null): RegistrationFolder
    {
        $rawData = $this->addStateToRawData($registrationFolder->getRawData(), "Déclaration Sortie de formation", RegistrationFolderStates::TERMINATED(), $registrationFolder->getOrganism(), $user->getEmail());
        //override set date
        $rawData['states'] = array_merge($rawData['states'], [
            RegistrationFolderStates::TERMINATED()->getValue() => [
                'date' => $terminatedDate->format('Y-m-d'),
            ]
        ]);

        return $this->registrationFolderService->createOrUpdate($rawData, DataProviders::from($registrationFolder->getType()), $registrationFolder->getExternalId());
    }

    /**
     * @throws Throwable
     * @throws \Doctrine\ORM\ORMException
     * @throws ORMException
     * @throws ErrorException
     */
    public function declareServiceDoneRegistrationFolder(RegistrationFolder $registrationFolder, float $absenceDuration, int $completionRate, bool $forceMajeureAbsence, RegistrationFolderReason $reason, User $user = null): RegistrationFolder
    {
        $rawData = $this->addStateToRawData($registrationFolder->getRawData(), "Déclaration service fait", RegistrationFolderStates::SERVICE_DONE_DECLARED(), $registrationFolder->getOrganism(), $user->getEmail());
        $rawData['trainingActionInfo'] = array_merge($rawData['trainingActionInfo'], [
            'trainingCompletionRate' => $completionRate,
            'forceMajeurCaseOf' => $forceMajeureAbsence,
            'uniteAbsenceOF' => "1",
            'dureeAbsenceOF' => $absenceDuration,
            'reason' => $reason->getCode()
        ]);
        $registrationFolder = $this->registrationFolderService->createOrUpdate($rawData, DataProviders::from($registrationFolder->getType()), $registrationFolder->getExternalId());
        return $this->validateServiceDoneRegistrationFolder($registrationFolder);
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @param RegistrationFolderReason $reason
     * @param string $description
     * @return RegistrationFolder
     */
    public function refuseRegistrationFolder(RegistrationFolder $registrationFolder, RegistrationFolderReason $reason, string $description): RegistrationFolder
    {
        throw new WedofBadRequestHttpException("Erreur, méthode non supportée");
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @param RegistrationFolderReason $reason
     * @param string $description
     * @return RegistrationFolder
     */
    public function cancelRegistrationFolder(RegistrationFolder $registrationFolder, RegistrationFolderReason $reason, string $description): RegistrationFolder
    {
        throw new WedofBadRequestHttpException("Erreur, méthode non supportée");
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @return RegistrationFolder|null
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function refreshRegistrationFolder(RegistrationFolder $registrationFolder): ?RegistrationFolder
    {
        return $this->getByExternalId($registrationFolder->getExternalId(), $registrationFolder->getOrganism(), ['refresh' => true]);
    }

    /**
     * @param string $externalId
     * @param Organism $organism
     * @return RegistrationFolder|null
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function retrieveRegistrationFolder(string $externalId, Organism $organism): ?RegistrationFolder
    {
        return $this->getByExternalId($externalId, $organism);
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @param string $oFBillNumber
     * @param float|null $vatRate
     * @return RegistrationFolder
     */
    public function billRegistrationFolder(RegistrationFolder $registrationFolder, string $oFBillNumber, float $vatRate = null): RegistrationFolder
    {
        return $registrationFolder;
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @param array $data
     * @return RegistrationFolder
     */
    public function updateRegistrationFolder(RegistrationFolder $registrationFolder, array $data): RegistrationFolder
    {
        return $registrationFolder;
    }

    /**
     * @param Organism $organism
     * @param string $attendeeId
     * @return array|null
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function getAttendeeRawData(Organism $organism, string $attendeeId): ?array
    {
        $data = $this->sendRequest(
            Tools::getEnvValue("KAIROSAIF_NEW_API_INDIVIDU_V2") . "individu/coordonnees?idNational=$attendeeId",
            [
                "organism" => $organism,
                "cache" => true
            ]);

        if (empty($data)) {
            return [];
        }

        $datav4 = $this->sendRequest(
            Tools::getEnvValue("KAIROSAIF_NEW_API_INDIVIDU_V4") . "individus?identifiantrsin=" . $data["identifiantsigma"] . "&codetp=" . $data["codeterritoire"] . "&demandeuremploiuniquement=false",
            [
                "organism" => $organism,
                "cache" => true
            ]);

        if (empty($datav4)) {
            return [];
        }

        $data = array_merge($data, array_filter($datav4[0], function ($value, $key) {
            return $value !== null;
        }
            , ARRAY_FILTER_USE_BOTH));

        $data["idNationalDE"] = $attendeeId;

        return $this->prepareAttendeeRawData($data);
    }

    /**
     * @param Organism $organism
     * @param string $trainingId
     * @param array $options
     * @return array|null
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function getTrainingRawData(Organism $organism, string $trainingId, array $options = []): ?array
    {
        $data = $this->sendRequest(
            Tools::getEnvValue("KAIROSAIF_NEW_API_FORMATION") . "actions/action/$trainingId?tousFinancements=true",
            [
                "organism" => $organism,
                "cache" => true
            ]);

        if (empty($data)) {
            return [];
        }

        return $this->prepareTrainingRawData($data);
    }

    /**
     * @param array $trainingRawData
     * @return Certification|null
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function retrieveCertificationFromTrainingRawData(array $trainingRawData): ?Certification
    {
        return $this->certificationService->getCertification([
            'certifInfo' => $trainingRawData['certifInfo'],
        ]);
    }

    /**
     * @param Organism $organism
     * @param array|null $trainingStatuses
     * @return array|null
     */
    public function getTrainingsRawData(Organism $organism, array $trainingStatuses = null): ?array
    {
        return null;
    }

    /**
     * @param Training $training
     * @param string $trainingActionId
     * @return array|null
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function getTrainingActionRawData(Training $training, string $trainingActionId): ?array
    {
        $data = $this->sendRequest(
            Tools::getEnvValue("KAIROSAIF_NEW_API_FORMATION") . "actions/action/$trainingActionId?tousFinancements=true",
            [
                "organism" => $training->getOrganism(),
                "cache" => true
            ]);

        if (empty($data)) {
            return [];
        }

        return $this->prepareTrainingActionRawData($data);
    }

    /**
     * @param Training $training
     * @return array|null
     */
    public function getTrainingActionsRawData(Training $training): ?array
    {
        return null;
    }

    /**
     * @param TrainingAction $trainingAction
     * @return array|null
     */
    public function getSessionsRawData(TrainingAction $trainingAction): ?array
    {
        return [];
    }

    /**
     * @param TrainingAction $trainingAction
     * @param string $sessionId
     * @return array|null
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function getSessionRawData(TrainingAction $trainingAction, string $sessionId): ?array
    {
        $data = $this->sendRequest(
            Tools::getEnvValue("KAIROSAIF_NEW_API_FORMATION") . "session/$sessionId",
            [
                "organism" => $trainingAction->getTraining()->getOrganism(),
                "cache" => true
            ]);

        if (empty($data)) {
            return null;
        }
        return $this->prepareSessionRawData($data);
    }

    /**
     * @param TrainingAction $trainingAction
     * @return void
     */
    public function updateEvaluationsForTrainingAction(TrainingAction $trainingAction): void
    {
        // TODO: Implement updateEvaluationsForTrainingAction() method.
    }

    /**
     * @param string $externalId
     * @return string|null
     */
    public function getTrainingExtIdFromSessionExtId(string $externalId): ?string
    {
        $session = $this->sessionService->getByExternalId($externalId);

        if ($session) {
            return $session->getRawData()["actionId"];
        }
        return "";
    }

    /**
     * @param string $externalId
     * @return string|null
     * @throws Throwable
     */
    public function getTrainingExtIdFromTrainingActionExtId(string $externalId): ?string
    {
        return $externalId;
    }

    /**
     * @param string $externalId
     * @return string|null
     */
    public function getShortTrainingActionExtId(string $externalId): ?string
    {
        return $externalId;
    }

    /**
     * @param Connection $connection
     * @return void
     */
    public function startInitialization(Connection $connection): void
    {
        if (str_contains($connection->getCredentials()['username'], 'wedof')) {
            if (!$connection->isInitialized()) {
                $organism = $connection->getOrganism();
                /** @var CronTaskManagerService $cronTaskManagerService */
                $cronTaskManagerService = $this->container->get(CronTaskManagerService::class);
                $cronTaskManagerService->dispatchStartBatchesRegistrationFolders($organism, $this->dataProvider, 19, 0);
                $this->dispatcher->dispatch(new ConnectionEvents($connection), ConnectionEvents::INITIALIZE_STARTED);

                if (in_array($organism->getSubscription()->getTrainingType(), [
                    SubscriptionTrainingTypes::STANDARD()->getValue(),
                    SubscriptionTrainingTypes::ESSENTIAL()->getValue(),
                    SubscriptionTrainingTypes::PREMIUM()->getValue(),
                ])) {
                    /** @var $users User[] */
                    $users = $this->userService->listReturnQueryBuilder(["organism" => $organism->getId()])->getQuery()->getResult();
                    foreach ($users as $user) {
                        $filters = $user->getFilters();
                        $filterKairos = $user->getFilters() && $filters[$organism->getSiret()] ? array_filter($filters[$organism->getSiret()], function ($var) {
                            return $var["link"] === "type=kairosAif&filterOnStateDate=lastUpdate";
                        }) : [];
                        if (sizeof($filterKairos) === 0) {
                            $filters[$organism->getSiret()][] = [
                                "link" => "type=kairosAif&filterOnStateDate=lastUpdate",
                                "name" => "Kairos AIF",
                                "color" => "#" . substr(md5(rand(1, 100)), 0, 6),
                                "entityClass" => "RegistrationFolder",
                            ];
                            $user->setFilters($filters);
                            $this->userService->save($user);
                        }
                    }
                }
            }
        } else {
            $connection->setState(ConnectionStates::IN_PROGRESS()->getValue()); // on repasse à inProgress le temps de faire les démarches de création d'habilitation auprès de Kairos
            /** @var ConnectionService $connectionService */
            $connectionService = $this->container->get(ConnectionService::class);
            $connectionService->save($connection);
            $organism = $connection->getOrganism();
            if (!$_ENV["COMMERCIAL_SLACK_URI"]) {
                return;
            }
            if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
                return;
            }
            try {
                Tools::getHttpClient()->request('POST', $_ENV["COMMERCIAL_SLACK_URI"], ['json' => [
                    'blocks' => [
                        0 => [
                            'type' => 'section',
                            'text' => [
                                'type' => 'mrkdwn',
                                'text' => "<@vbarrier> <@U01NG9CQ9LN> <@U01N801A77Z> Alerte : un client a renseigné des identifiants KairosAIF"
                            ]
                        ],
                        1 => [
                            'type' => 'section',
                            'text' => [
                                'type' => 'mrkdwn',
                                'text' => "Nom : " . $organism->getName() . ", Siret : " . $organism->getSiret()
                            ]
                        ]
                    ]
                ]]);
            } catch (Throwable $e) {
            }
        }
    }

    /**
     * @param Connection $connection
     * @return void
     */
    public function initializeCompleted(Connection $connection): void
    {
    }

    /**
     * @return array
     */
    public function getActionsRulesForRegistrationFolder(): array
    {
        return [
            RegistrationFolderStates::NOT_PROCESSED()->getValue() => [], //empty array necessary
            RegistrationFolderStates::VALIDATED()->getValue() => [],
            RegistrationFolderStates::WAITING_ACCEPTATION()->getValue() => [],
            RegistrationFolderStates::ACCEPTED()->getValue() => [
                RegistrationFolderStates::IN_TRAINING()->getValue() => []
            ],
            RegistrationFolderStates::IN_TRAINING()->getValue() => [
                RegistrationFolderStates::TERMINATED()->getValue() => [],
                RegistrationFolderStates::SERVICE_DONE_DECLARED()->getValue() => []
            ],
            RegistrationFolderStates::TERMINATED()->getValue() => [
                RegistrationFolderStates::SERVICE_DONE_DECLARED()->getValue() => []
            ],
            RegistrationFolderStates::SERVICE_DONE_DECLARED()->getValue() => [
                RegistrationFolderStates::SERVICE_DONE_VALIDATED()->getValue() => [],
            ],
            RegistrationFolderStates::SERVICE_DONE_VALIDATED()->getValue() => [
//                RegistrationFolderStates::TO_BILL()->getValue() => ['billable']
            ],
            RegistrationFolderStates::CANCELED_BY_ATTENDEE()->getValue() => [],
            RegistrationFolderStates::CANCELED_BY_ATTENDEE_NOT_REALIZED()->getValue() => [],
            RegistrationFolderStates::CANCELED_BY_ORGANISM()->getValue() => [],
            RegistrationFolderStates::CANCELED_BY_FINANCER()->getValue() => [],
            RegistrationFolderStates::REFUSED_BY_ATTENDEE()->getValue() => [],
            RegistrationFolderStates::REFUSED_BY_ORGANISM()->getValue() => [],
            RegistrationFolderStates::REFUSED_BY_FINANCER()->getValue() => [],
            RegistrationFolderStates::REJECTED_WITHOUT_TITULAIRE_SUITE()->getValue() => []
        ];
    }

    //-------------------
    // METHODES PRIVEES
    //-------------------

    /**
     * @param string $url
     * @param array $options
     * @return array|null
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    protected function sendRequest(string $url, array $options = []): ?array
    {
        $useCache = $options['cache'] ?? false;
        if ($useCache) {
            $hash = md5($url . ($options['method'] ?? 'GET') . (json_encode($options['parameters'] ?? [])) . ($options['organism'] ? $options['organism']->getSiret() : ''));
            if (!isset($this->_cachedResponses[$hash])) {
                $this->logger->info("[KairosAifApiService] Cache miss for $url with hash" . $hash);
                $this->_cachedResponses[$hash] = parent::sendRequest($url, $options);
            } else {
                $this->logger->info("[KairosAifApiService] Cache hit for $url with hash" . $hash);
            }
            return $this->_cachedResponses[$hash];
        }
        return parent::sendRequest($url, $options);
    }

    /**
     * @return string|false
     * @throws ORMException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws OptimisticLockException
     * @throws ErrorException
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     * @throws Throwable
     */
    private function authenticate_step_1(): array
    {
        $response = $this->sendRequest(Tools::getEnvValue("KAIROSAIF_PRE_AUTH_URI"), ['fullResponse' => true]);
        $dom = new DOMDocument();
        libxml_use_internal_errors(true);
        $dom->loadHTML($response['content']);
        $xpath = new DOMXPath($dom);
        $tag = $xpath->query('//input[@name="t:formdata"]')[0];
        if (!empty($tag->getAttribute('value'))) {
            return ["formdata" => $tag->getAttribute('value'), "cookie" => explode(';', $response['headers']['set-cookie'][0])[0] . ';'];
        } else {
            return false;
        }
    }

    /**
     * @param array $credentials
     * @param string $formdata
     * @param string $cookie
     * @return array|false
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    private function authenticate_step_2(array $credentials, string $formdata, string &$cookie): array
    {
        $data = ["t:submit" => '["BTconnecter","BTconnecter"]', "LOGutilisateur" => $credentials['username'], "LOGmotdepasse" => $credentials['password'], "t:formdata" => $formdata, "BTconnecter" => "Se connecter",];
        $response = $this->sendRequest(
            self::getEnvApiConfig($this->dataProvider, 'AUTH_URI'),
            ['method' => 'POST', 'cookie' => $cookie, 'parameters' => $data, 'fullResponse' => true, 'content-type' => 'application/x-www-form-urlencoded']);
        $dom = new DOMDocument();
        libxml_use_internal_errors(true);
        $dom->loadHTML($response['content']);
        $xpath = new DOMXPath($dom);
        $isResetPasswordForm = $xpath->query('//p[contains(text(),"Votre mot de passe est expiré. Merci d\'en saisir un nouveau.")]')->length > 0;
        if ($isResetPasswordForm) {
            throw new WedofBadRequestHttpException("Votre mot de passe est expiré. Merci d\'en saisir un nouveau sur la plateforme Kairos.");
        }

        $tag = $xpath->query('//a[contains(@title,"Accéder à l\'application KAIROS (nouvelle fenêtre)")]')[0];
        if (!empty($tag->getAttribute('href'))) {
            $url_kairos = $tag->getAttribute('href');
            $url_kairos = 'https://www.portail-emploi.fr' . explode("','KAIROS'", explode("window.open('", $url_kairos)[1])[0];
            $url_kairos = explode('&typetoken', $url_kairos)[0] . "&typetoken=NET%20ENTREPRISE";
            $cookie = explode('/securise;', $response['url'])[1] . ';' . $cookie;
            $tag = $xpath->query('//a[contains(@title,"Accéder à l\'application KAIROS (nouvelle fenêtre)")]/text()')[0];
            $siret = substr(str_replace(' ', '', $tag->textContent), 9, 14);

            $tag = $xpath->query('//a[contains(@title,"Accéder à l\'application KAIROS nouvelle génération (nouvelle fenêtre)")]')[0];
            if (!empty($tag->getAttribute('href'))) {
                $url_kairos_new = $tag->getAttribute('href');
                $url_kairos_new = 'https://www.portail-emploi.fr' . explode("','KAIROS_nouvelle_generation'", explode("window.open('", $url_kairos_new)[1])[0];
                $url_kairos_new = explode('&typetoken', $url_kairos_new)[0] . "&typetoken=NET%20ENTREPRISE";
                return ['url_kairos' => $url_kairos, 'url_kairos_new' => $url_kairos_new, "siret" => $siret];
            }
        }
        throw new WedofBadRequestHttpException("Impossible d'accéder à l\'application Kairos");
    }

    /**
     * @param string $url
     * @param string $cookie
     * @return array
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    private function authenticate_step_3(string $url, string $cookie): array
    {
        $try = 0;
        do {
            $response = $this->sendRequest($url, ['cookie' => $cookie, 'fullResponse' => true]);
            $try++;
        } while (Tools::contains($response['content'], "window.opener.location.reload()") && $try <= 3);
        $dom = new DOMDocument();
        libxml_use_internal_errors(true);
        $dom->loadHTML($response['content']);
        $xpath = new DOMXPath($dom);
        if ($xpath->query('//input[@name="idmedia"]')->length > 0) {
            return ["idmedia" => $xpath->query('//input[@name="idmedia"]')[0]->getAttribute('value'), "BM_ACCES_WEB" => $xpath->query('//input[@name="BM_ACCES_WEB"]')[0]->getAttribute('value'), "ticket" => $xpath->query('//input[@name="ticket"]')[0]->getAttribute('value'), "login" => $xpath->query('//input[@name="login"]')[0]->getAttribute('value'), "appli" => $xpath->query('//input[@name="appli"]')[0]->getAttribute('value'), "url" => $xpath->query('//form')[0]->getAttribute('action')];
        } else {
            throw new WedofBadRequestHttpException("Une erreur est survenue");
        }
    }

    /**
     * @param string $url
     * @param array $params
     * @param string $cookie
     * @return array|false
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    private function authenticate_step_4(string $url, array $params, string $cookie)
    {
        $response = $this->sendRequest($url, ['method' => 'POST', 'cookie' => $cookie, 'parameters' => $params, 'fullResponse' => true, 'content-type' => 'application/x-www-form-urlencoded']);
        if (Tools::contains($response['content'], 'document.location="frameset_IE.jsf?')) {
            $session = explode('";', explode('document.location="frameset_IE.jsf?FW_ID_MULTISESSION=', $response['content'])[1])[0];
            $authData = ["session_count" => explode("_", $session)[0]];
            $cookiesData = [];
            foreach ($response['allHeaders'] as $header) {
                if (Tools::contains($header, "JSESSIONID") && empty($authData['session'])) {
                    $authData['session'] = explode(';', str_replace('Set-Cookie: JSESSIONID=', '', $header))[0];
                }
                if (Tools::startsWith($header, "Set-Cookie") && !Tools::contains($header, "expires")) {
                    $cookiesData[] = explode(';', str_replace('Set-Cookie: ', '', $header))[0];
                }
            }

            $authData["session_query"] = str_replace($authData['session_count'] . '_' . $authData['session'] . '!', '', $session);
            $authData["cookie"] = join(';', $cookiesData);
            return $authData;
        } else {
            return false;
        }
    }

    /**
     * @param array $authentication
     * @param array $params
     * @return string|false
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    private function initViewState(array $authentication, array $params = [])
    {
        $url = Tools::getEnvValue("KAIROSAIF_BASE_URI") . "FTPF_GECA_ListerSessionsFormation.ListeSessionsFormation.jsf?FW_ID_MULTISESSION=" . $authentication['session_count'] . "_" . $authentication['session'] . "!" . $authentication['session_query'];
        $response = $this->sendRequest($url, ['method' => 'POST', 'cookie' => $authentication['cookie'], 'parameters' => $params, 'fullResponse' => true, 'content-type' => 'application/x-www-form-urlencoded']);
        return self::getViewState($response['content']) ?? false;
    }

    /**
     * @param $html
     * @return string|null
     */
    private static function getViewState($html): ?string
    {
        $dom = new DOMDocument();
        libxml_use_internal_errors(true);
        $dom->loadHTML($html);
        $xpath = new DOMXPath($dom);
        return $xpath->query('//input[@name="javax.faces.ViewState"]') ? $xpath->query('//input[@name="javax.faces.ViewState"]')[0]->getAttribute('value') : null;
    }

    /**
     * @param $url
     * @param $cookie
     * @return string
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    private function authenticate_step_5($url, $cookie): string
    {
        $try = 0;
        do {
            $response = $this->sendRequest($url, ['cookie' => $cookie, 'fullResponse' => true]);
            $try++;
        } while (Tools::contains($response['content'], "window.opener.location.reload()") && $try <= 3);
        try {
            return 'partenaire' . urldecode(explode('ticket=', explode('&login=', $response['url'])[0])[1]);
        } catch (Exception $e) {
            throw new WedofBadRequestHttpException("Une erreur est survenue");
        }
    }

    /**
     * @param string $externalId
     * @param Organism $organism
     * @param array $options
     * @return RegistrationFolder|null
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    private function getByExternalId(string $externalId, Organism $organism, array $options = []): ?RegistrationFolder
    {
        $options = array_merge(["createIfNotExist" => true, "refresh" => false, "rawData" => null], $options);
        $registrationFolder = $this->registrationFolderService->getByExternalId($externalId);
        if ((!$registrationFolder && $options['createIfNotExist']) || $options['refresh']) {
            $rawData = !empty($options['rawData']) ? $this->getRegistrationFolderDetailsRawData($organism, $options['rawData']) : $this->getRegistrationFolderRawData($organism, $externalId);
            if ($registrationFolder) {
                return $this->registrationFolderService->updateFromRawData(DataProviders::from($rawData['type']), $rawData, $options['refresh']);
            } else {
                return $this->registrationFolderService->createFromRawData(DataProviders::from($rawData['type']), $rawData);
            }
        }
        return $registrationFolder;
    }

    /**
     * @param Organism $organism
     * @param array $rawData
     * @return array
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    private function getRegistrationFolderDetailsRawData(Organism $organism, array $rawData): array
    {
        $externalId = $rawData["id"];
        $kairosId = self::getKairosExternalId($externalId);
        $registrationFolder = $this->registrationFolderService->getByExternalId($externalId);

        $data = $this->sendRequest(
            Tools::getEnvValue("KAIROSAIF_NEW_API_FORMATION") . "devis/" . $kairosId[0] . "/version/" . $rawData["versionDevis"],
            [
                "organism" => $organism,
                "cache" => true
            ]
        );
        if (!empty($data)) {
            if (is_null($registrationFolder)) {
                try {
                    $dataAction = $this->sendRequest(Tools::getEnvValue("KAIROSAIF_NEW_API_FORMATION") . "actions/action/" . $data["apercuSessionFormation"]["idAction"] . "?tousFinancements=true",
                        [
                            "organism" => $organism,
                            "cache" => true
                        ]);
                } catch (Exception $exception) {
                    $dataAction = [
                        "certifiante" => false,
                        "contenuFormation" => $data["donneeSessionFormation"]["contenuFormation"],
                        "resultatsAttendus" => $data["apercuSessionFormation"]["objectifFormation"],
                        "modaliteEnseignement" => ["libelleLong" => $data['apercuSessionFormation']['codeModaliteEnseignement'] === 2 ? 'distance' : 'présentiel'],
                        "formation" => [
                            "idFormation" => $data['apercuSessionFormation']['numeroInterCarifFormation'],
                            "intituleFormation" => $data['apercuSessionFormation']['intituleFormation']
                        ],
                        "organismeFormation" => [
                            "nom" => $data['apercuSessionFormation']['nomOrganismeFormation'],
                            "coordonnees" => [
                                "adresse" => [
                                    "ville" => $data["organismeFormation"]["coordonneesOrganisme"]["adresse"]["ville"],
                                    "adresseLigne1" => $data["organismeFormation"]["coordonneesOrganisme"]["adresse"]["adresseLigne1"],
                                    "codePostal" => $data["organismeFormation"]["coordonneesOrganisme"]["adresse"]["codePostal"]
                                ]
                            ]
                        ]
                    ];
                }
            } else {
                $dataAction = $registrationFolder->getRawData()["kairosTrainingActionRawData"];
            }
            if (!empty($dataAction)) {
                $rawData["kairosTrainingActionRawData"] = $dataAction;
                $rawData["kairosRegistrationFolderRawData"] = $data;
                $rawData["financement"] = $data["conventionDevis"]["financement"] ?? null;
                $rawData["title"] = $data["apercuSessionFormation"]["intituleFormation"];
                $rawData["totalTTC"] = $data["conventionDevis"]["montantDevisTTC"] ?? null;
                $rawData["currentState"] = $this->getRegistrationFolderStateFromKairosState($data, $registrationFolder)->getValue();
                $rawData["poleEmploiDpt"] = $data["codeGeo"];
                $rawData["poleEmploiDevis"] = $data["numeroDevis"];
                $rawData["idFormation"] = $dataAction["formation"]["idFormation"];
                $rawData["trainingId"] = $data["apercuSessionFormation"]["idAction"];
                $rawData["trainingActionId"] = $data["apercuSessionFormation"]["idAction"];

                //libelle from the DE side are different
                $rawData['statusValidationDevisDE'] = $data["statutValidationDevis"];

                $rawData["certifInfo"] = "";
                if ($dataAction["certifiante"] === true) {
                    $rawData["certifInfo"] = $dataAction["certifications"][0]["codeCertifInfo"];
                }

                $rawData["trainingActionInfo"]["title"] = $dataAction["formation"]["intituleFormation"];
                $rawData["trainingActionInfo"]["address"] = [
                    "vat" => null,
                    "address" => [
                        "id" => $organism->getId(),
                        "city" => $dataAction["organismeFormation"]["coordonnees"]["adresse"]["ville"],
                        "line4" => $dataAction["organismeFormation"]["coordonnees"]["adresse"]["adresseLigne1"],
                        "number" => null,
                        "country" => "FRANCE",
                        "postBox" => null,
                        "zipCode" => $dataAction["organismeFormation"]["coordonnees"]["adresse"]["codePostal"],
                        "roadName" => null,
                        "roadType" => null,
                        "idAddress" => null,
                        "residence" => null,
                        "countryCode" => "FR",
                        "fullAddress" => implode(" ", [
                            $dataAction["organismeFormation"]["coordonnees"]["adresse"]["adresseLigne1"],
                            $dataAction["organismeFormation"]["coordonnees"]["adresse"]["codePostal"],
                            $dataAction["organismeFormation"]["coordonnees"]["adresse"]["ville"]
                        ]),
                        "trainingSite" => str_contains($dataAction["modaliteEnseignement"]["libelleLong"], 'distance') ?? true,
                        "corporateName" => $dataAction["organismeFormation"]["nom"],
                        "roadTypeLabel" => null,
                        "informationSite" => false,
                        "repetitionIndex" => null,
                        "subscriptionSite" => false,
                        "additionalAddress" => null,
                        "repetitionIndexLabel" => null,
                        "reducedMobilityAccessCompliant" => null,
                        "reducedMobilityAccessModalities" => null
                    ],
                    "teachingModalities" => str_contains($dataAction["modaliteEnseignement"]["libelleLong"], 'distance') ? 2 : 0,
                ];
                $rawData["trainingActionInfo"]["content"] = $dataAction["contenuFormation"];
                $rawData["trainingActionInfo"]["sessionId"] = $data["apercuSessionFormation"]["idSession"];
                $rawData["trainingActionInfo"]["totalExcl"] = $data["conventionDevis"]["montantDevisTTC"];
                $rawData["trainingActionInfo"]["totalIncl"] = $data["conventionDevis"]["montantDevisTTC"];
                $rawData["trainingActionInfo"]["vatExclTax5"] = null;
                $rawData["trainingActionInfo"]["vatInclTax5"] = null;
                $rawData["trainingActionInfo"]["trainingGoal"] = $data["apercuSessionFormation"]["objectifFormation"];
                $rawData["trainingActionInfo"]["vatExclTax20"] = null;
                $rawData["trainingActionInfo"]["vatInclTax20"] = null;
                $rawData["trainingActionInfo"]["trainingPaces"] = [];
                $rawData["trainingActionInfo"]["trainingCompletionRate"] = null;
                $rawData["trainingActionInfo"]["additionalFees"] = 0;
                $rawData["trainingActionInfo"]["expectedResult"] = $dataAction["resultatsAttendus"];
                $rawData["trainingActionInfo"]["indicativeDuration"] = $data["conventionDevis"]["horaireInscription"]["dureeTotale"];
                $rawData["trainingActionInfo"]["hoursInCenter"] = $data["conventionDevis"]["horaireInscription"]["nombreHeureCentre"];
                $rawData["trainingActionInfo"]["hoursInCompany"] = $data["conventionDevis"]["horaireInscription"]["nombreHeureEntreprise"];
                $rawData["trainingActionInfo"]["numeroInterCarifAction"] = $data["apercuSessionFormation"]["numeroInterCarifAction"];

                $rawData["numeroInterCarifAction"] = $data["apercuSessionFormation"]["numeroInterCarifAction"];
                $rawData["numeroInterCarifFormation"] = $data["apercuSessionFormation"]["numeroInterCarifFormation"];
                $rawData["numeroInterCarifSession"] = $data["apercuSessionFormation"]["numeroInterCarifSession"];

                $rawData["currentBillingState"] = RegistrationFolderBillingStates::NOT_BILLABLE()->getValue();
                $rawData["currentAttendeeState"] = RegistrationFolderAttendeeStates::SERVICE_DONE_NOT_DECLARED()->getValue();

                $rawData["attendee"] = $this->getAttendeeRawData($organism, $data["idNationalDE"]);

                $rawData["states"] ??= [];
                $rawData["history"] = [];

                if (isset($data["dateSignatureOF"])) {
                    $rawData = $this->addStateToRawData($rawData, "Devis créé", RegistrationFolderStates::NOT_PROCESSED(), $organism, "Organisme", false, Tools::createDateFromString($data["dateSignatureOF"]));
                }
                if (isset($data["signatureDE"]) && $data["signatureDE"]["accord"] === true) {
                    $rawData = $this->addStateToRawData($rawData, "Devis validé par le DE", RegistrationFolderStates::VALIDATED(), $organism, "Demandeur d'emploi", false, Tools::createDateFromString($data["signatureDE"]["dateSignature"]));
                }
                if (isset($data["signatureConseiller"]) && $data["signatureConseiller"]["accord"] === true) {
                    $rawData = $this->addStateToRawData($rawData, "Devis accepté par PE", RegistrationFolderStates::ACCEPTED(), $organism, "Pôle Emploi (Conseiller)", false, Tools::createDateFromString($data["signatureConseiller"]["dateSignature"]));
                }
                if (isset($data["signatureDirecteur"]) && $data["signatureDirecteur"]["accord"] === true) {
                    $rawData = $this->addStateToRawData($rawData, "Devis accepté par PE", RegistrationFolderStates::ACCEPTED(), $organism, "Pôle Emploi (Directeur)", false, Tools::createDateFromString($data["signatureDirecteur"]["dateSignature"]));
                }
                if (isset($rawData["motifRefusDevis"])) {
                    $motif = "Motif donné au Demandeur d'Emploi : " . $rawData["motifRefusDevis"]["libelleLong"];
                    $state = $this->getRegistrationFolderStateFromKairosState($data, $registrationFolder);
                    $date = isset($data["signatureConseiller"]) ? Tools::createDateFromString($data["signatureConseiller"]["dateSignature"]) : (isset($data["signatureDirecteur"]) ? Tools::createDateFromString($data["signatureDirecteur"]["dateSignature"]) : new DateTime());
                    $rawData = $this->addStateToRawData($rawData, $motif, $state, $organism, "", false, $date);
                }

                $rawData["changingStateDate"] = $data["dateModificationDevis"] ?? $data["dateCreationDevis"];

                return $rawData;
            }
        }
        return [];
    }

    /**
     * @param Organism $organism
     * @param array $data
     * @return array
     */
    private function prepareRegistrationFolderRawData(Organism $organism, array $data): array
    {
        $rawData = $data;
        $rawData["id"] = self::getKairosExternalIdWithVersionDevis($data["numeroDevis"], $data["versionDevis"]);
        $rawData["versionDevis"] = $data["versionDevis"];
        $rawData["type"] = $this->dataProvider->getValue();
        $rawData["trainingActionInfo"]["sessionStartDate"] = $data["dateDebutSession"];
        $rawData["trainingActionInfo"]["sessionEndDate"] = $data["dateFinSession"];
        $rawData["trainingActionInfo"]["weeklyDuration"] = $data["dureeTotale"];
        $rawData["statusCode"] = 0;
        $rawData["statusLabel"] = 'published';
        $rawData["siret"] = $organism->getSiret();
        $rawData["currentBillingState"] = RegistrationFolderBillingStates::NOT_BILLABLE()->getValue();
        $rawData["currentAttendeeState"] = RegistrationFolderAttendeeStates::SERVICE_DONE_NOT_DECLARED()->getValue();

        return $rawData;
    }

    /**
     * @param RegistrationFolderStates $state
     * @return string
     */
    private function getKairosStateFromRegistrationFolderStates(RegistrationFolderStates $state): string
    {
        switch ($state) {
            case RegistrationFolderStates::VALIDATED():
                return "1,5";//En attente de decision du DE x2
            case RegistrationFolderStates::CANCELED():
                return "2";//Annulé
            case RegistrationFolderStates::WAITING_ACCEPTATION():
                return "3,6";//Accepté par le DE en cours de traitement par Pole Emploi x2
            case RegistrationFolderStates::REFUSED_BY_ATTENDEE():
                return "4";//Refusé par le DE
            case RegistrationFolderStates::REFUSED_BY_FINANCER():
            case RegistrationFolderStates::CANCELED_BY_FINANCER():
                return "7,9";//Refusé par Pole Emploi, Refusé par Pole Emploi x2
            case RegistrationFolderStates::ACCEPTED():
                return "8";//Accepté par Pole Emploi
            case RegistrationFolderStates::NOT_PROCESSED():
                return "0";//??
            case RegistrationFolderStates::ALL():
            default:
                return "0,1,2,3,4,5,6,7,8,9";
        }
    }

    /**
     * @param array $data
     * @param RegistrationFolder|null $registrationFolder
     * @return RegistrationFolderStates
     * @throws Exception
     */
    private function getRegistrationFolderStateFromKairosState(array $data, RegistrationFolder $registrationFolder = null): RegistrationFolderStates
    {
        $state = $data["statutValidationDevis"]["code"];
        switch ($state) {
            case "1":
            case "5"://En attente d'informations complémentaires
                $date = Tools::createDateFromString($data["dateModificationDevis"] ?? $data["dateCreationDevis"]);
                $diff = $date->diff(new Datetime());
                if ($diff->days >= self::DELAY_BEFORE_REJECTED_WITHOUT_TITULAIRE_SUITE) {
                    return RegistrationFolderStates::REJECTED_WITHOUT_TITULAIRE_SUITE();
                }
                return RegistrationFolderStates::VALIDATED();
            case "2"://Annulé
                return RegistrationFolderStates::CANCELED();
            case "3"://Accepté par le DE en cours de traitement par Pole Emploi
            case "6"://Accepté par le DE en cours de traitement par Pole Emploi
                return RegistrationFolderStates::WAITING_ACCEPTATION();
            case "4"://Refusé par le DE
                return RegistrationFolderStates::REFUSED_BY_ATTENDEE();
            case "7"://Refusé par Pole Emploi
            case "9"://Refusé par Pole Emploi
                return RegistrationFolderStates::REFUSED_BY_FINANCER();
            case "8"://Accepté par Pole Emploi
                if (is_null($registrationFolder) || in_array($registrationFolder->getState(), [
                        RegistrationFolderStates::VALIDATED(),
                        RegistrationFolderStates::REJECTED_WITHOUT_TITULAIRE_SUITE(),
                        RegistrationFolderStates::NOT_PROCESSED()
                    ])) {
                    return RegistrationFolderStates::ACCEPTED();
                } else {
                    //local state wedof
                    return RegistrationFolderStates::from($registrationFolder->getState());
                }
            case "0"://??
            default:
                if (!is_null($registrationFolder)) {
                    return RegistrationFolderStates::from($registrationFolder->getState());
                }
                return RegistrationFolderStates::NOT_PROCESSED();
        }
    }

    /**
     * @param array $data
     * @return array
     */
    private function prepareTrainingRawData(array $data): array
    {
        $rawData = [];
        $rawData["id"] = $data['idAction'];
        $rawData["externalId"] = $data['idAction'];
        $rawData["trainingId"] = $data['formation']["idFormation"];
        $rawData["title"] = $data['formation']["intituleFormation"];
        $rawData["totalTvaTTC"] = null;
        $rawData["statusCode"] = '0';
        $rawData["statusLabel"] = 'published';
        $rawData["certifInfo"] = $data["certifiante"] === true ? $data["certifications"][0]["codeCertifInfo"] : "";
        $rawData["code"] = null;
        $rawData["type"] = null;
        $rawData["dataProvider"] = $this->dataProvider->getValue();

        return $rawData;
    }

    /**
     * @param array $data
     * @return array
     */
    private function prepareTrainingActionRawData(array $data): array
    {
        $rawData = [];
        $rawData["id"] = $data['idAction'];
        $rawData["externalId"] = $data['idAction'];
        $rawData["trainingId"] = $data['formation']["idFormation"];
        $rawData["totalTvaTTC"] = null;
        $rawData["statusCode"] = 0;
        $rawData["statusLabel"] = 'published';
        $rawData["averageLearningTime"] = null;

        return $rawData;
    }

    /**
     * @param array $data
     * @return array|null
     */
    private function prepareSessionRawData(array $data): ?array
    {
        $rawData = [];
        $rawData["externalId"] = $data['idSession'];
        $rawData["trainingId"] = $data['action']['formation']['idFormation'];
        $rawData["actionId"] = $data['action']['idAction'];
        $rawData["id"] = $data['idSession'];
        $rawData["endDate"] = $data['dateFinSession'];
        $rawData["beginDate"] = $data['dateDebutSession'];
        $rawData["statusCode"] = 0;
        $rawData["statusLabel"] = 'published';

        return $rawData;
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @return RegistrationFolder
     * @throws ErrorException
     * @throws ORMException
     * @throws Throwable
     */
    private function validateServiceDoneRegistrationFolder(RegistrationFolder $registrationFolder): RegistrationFolder
    {
        $rawData = $this->addStateToRawData($registrationFolder->getRawData(), "Service fait validé", RegistrationFolderStates::SERVICE_DONE_VALIDATED(), $registrationFolder->getOrganism());
        $rawData['currentBillingState'] = RegistrationFolderBillingStates::TO_BILL()->getValue();
        return $this->registrationFolderService->createOrUpdate($rawData, DataProviders::from($registrationFolder->getType()), $registrationFolder->getExternalId());
    }

    /**
     * @param string $kairosExternalId
     * @return array
     */
    private function getKairosExternalId(string $kairosExternalId): array
    {
        $kairosExternalIdWithoutPrefix = Tools::removePrefix($kairosExternalId, self::EXTERNAL_ID_PREFIX);

        return explode('-', $kairosExternalIdWithoutPrefix);
    }

    /**
     * @param string $numeroDevis
     * @param string $versionDevis
     * @return string
     */
    private function getKairosExternalIdWithVersionDevis(string $numeroDevis, string $versionDevis): string
    {
        return $numeroDevis . '-' . $versionDevis;
    }

    /**
     * @param array $data
     * @return array
     * @throws Exception
     */
    private function prepareAttendeeRawData(array $data): array
    {
        $rawData = [];
        $rawData["birthName"] = $data["nomnaissance"];
        $rawData["lastName"] = $data["nomusage"] && $data["nomusage"] !== "" ? $data["nomusage"] : $data["nomnaissance"];
        $rawData["firstName"] = $data["prenom"];
        $rawData["dateOfBirth"] = Tools::createDateFromString($data["dateNaissance"]);
        $rawData["displayName"] = $rawData["lastName"] . "." . $rawData["firstName"];
        $rawData["email"] = $data["courriel"] ?? null;
        $rawData["phoneNumber"] = $data["telephone1"] ?? null;
        $rawData["phoneFixed"] = $data["telephone2"] ?? null;
        $rawData["registrationFolders"] = null;
        $rawData["certificationFolders"] = null;
        $rawData["poleEmploiId"] = $data["identifiantsigma"] ?? null;
        $rawData["poleEmploiRegionCode"] = $data["codeterritoire"] ?? null;
        $rawData["idsSpe"] = $data["idSPE"] ?? null;
        $rawData["readOnly"] = false;
        $rawData["gender"] = $data["civilite"] === 'M.' ? AttendeeGender::MALE()->getValue() : AttendeeGender::FEMALE()->getValue();
        $rawData["externalId"] = null;
        $rawData["address"] = [
            "number" => "",
            "roadName" => $data["adresse"] ?? null,
            "zipcode" => $data["codePostal"] ?? null,
            "city" => $data["commune"] ?? null,
        ];
        $rawData["isKairosAif"] = true;

        return $rawData;
    }
}
