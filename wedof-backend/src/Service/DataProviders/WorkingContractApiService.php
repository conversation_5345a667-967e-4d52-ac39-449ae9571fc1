<?php


namespace App\Service\DataProviders;

use App\Entity\Organism;
use App\Entity\RegistrationFolder;
use App\Entity\WorkingContract;
use App\Exception\WedofConnectionException;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\enums\WorkingContractStates;
use App\Library\utils\Tools;
use App\Service\ConnectionService;
use App\Service\OrganismService;
use App\Service\WorkingContractService;
use DateTime;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use ErrorException;
use Exception;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Throwable;

class WorkingContractApiService implements LoggerAwareInterface
{
    private LoggerInterface $logger;
    private WorkingContractService $workingContractService;
    private ConnectionService $connectionService;
    private OpcoCfaApiService $opcoCfaApiService;
    private OrganismService $organismService;

    public function __construct(
        WorkingContractService $workingContractService,
        ConnectionService $connectionService,
        OpcoCfaApiService      $opcoCfaApiService,
        OrganismService        $organismService
    )
    {
        $this->workingContractService = $workingContractService;
        $this->connectionService = $connectionService;
        $this->opcoCfaApiService = $opcoCfaApiService;
        $this->organismService = $organismService;
    }

    /**
     * @param LoggerInterface $logger
     */
    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    /**
     * @param Organism $organism
     * @param DataProviders $financer
     * @return void
     * @throws WedofConnectionException
     * @throws ORMException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws OptimisticLockException
     * @throws ErrorException
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     * @throws Throwable
     */
    public function synchronizeByOrganismAndFinancer(Organism $organism, DataProviders $financer)
    {
        $this->logger->info('[opco] Start synchronize ' . $organism->getSiret() . ' financer ' . $financer->getValue());
        /** @var OpcoCfaDockApiService $apiService */
        $apiService = BaseApiService::getApiServiceByDataProvider($financer);

        $this->logger->info('[opco] List folder states ' . $organism->getSiret() . ' financer ' . $financer->getValue());
        $dataToSynchronize = $this->computeDataToSynchronize($organism, $apiService, $financer);
        $externalIdsFinancerToCreate = $dataToSynchronize['externalIdsFinancerToCreate'];
        $workingContractsByExternalIdFinancer = $dataToSynchronize['workingContractsByExternalIdFinancer'];

        $this->logger->info('[opco] Syncronize new folders ' . $organism->getSiret() . ' financer ' . $financer->getValue());
        $this->synchronizeNewRawFolders($organism, $apiService, $financer, $externalIdsFinancerToCreate);
        $this->logger->info('[opco] Syncronize existing folders ' . $organism->getSiret() . ' financer ' . $financer->getValue());
        $this->synchronizeExistingWorkingContracts($organism, $apiService, $workingContractsByExternalIdFinancer);
    }

    //-------------------
    // METHODES PRIVEES
    //-------------------

    /**
     * @param Organism $organism
     * @param OpcoCfaDockApiService $apiService
     * @param DataProviders $financer
     * @param array $externalIdsFinancerToCreate
     * @return void
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    private function synchronizeNewRawFolders(Organism $organism, OpcoCfaDockApiService $apiService, DataProviders $financer, array $externalIdsFinancerToCreate)
    {
        $newRawFolders = $this->fetchFolders($organism, $apiService, $externalIdsFinancerToCreate);
        foreach ($newRawFolders as $newRawFolder) {
            try {
                $this->manageNewRawFolder($organism, $newRawFolder, $financer);
            } catch (Throwable $t) {
                $this->logger->error('[opco] Error opco ' . $financer . ' organism ' . $organism->getSiret() . ' ' . $t->getMessage());
            }
        }
        $connection = $organism->getConnectionForDataProvider($financer);
        if (!$connection->isInitialized()) {
            $this->logger->info('[opco] Finish initialize ' . $organism->getSiret() . ' financer ' . $financer->getValue());
            $this->connectionService->finishInitialize($connection);
        }
    }

    /**
     * @param Organism $organism
     * @param OpcoCfaDockApiService $apiService
     * @param array $workingContractsByExternalIdFinancer
     * @return void
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    private function synchronizeExistingWorkingContracts(Organism $organism, OpcoCfaDockApiService $apiService, array $workingContractsByExternalIdFinancer)
    {
        $existingExternaldIdsFinancer = array_keys($workingContractsByExternalIdFinancer);
        $rawFolders = $this->fetchFolders($organism, $apiService, $existingExternaldIdsFinancer);
        foreach ($rawFolders as $rawFolder) {
            try {
                $workingContract = $workingContractsByExternalIdFinancer[$rawFolder['cerfa']['numeroInterne']];
                $workingContractId = $workingContract['id'];
                $this->updateWorkingContractFromRawFolder($workingContractId, $rawFolder);
            } catch (Throwable $t) {
                $this->logger->error('[opco] Error opco organism ' . $organism->getSiret() . ' ' . $t->getMessage());
            }
        }
    }

    /**
     * @param Organism $organism
     * @param array $rawFolder
     * @param DataProviders $financer
     * @return void
     * @throws Throwable
     */
    private function manageNewRawFolder(Organism $organism, array $rawFolder, DataProviders $financer)
    {
        $registrationFolder = $this->opcoCfaApiService->createOrFindRegistrationFolderFromRawFolder($rawFolder, $organism);
        $workingContract = $this->createWorkingContractFromRawFolder($rawFolder, $financer, $registrationFolder);
        $this->opcoCfaApiService->updateRegistrationFolderFromWorkingContract($registrationFolder, $workingContract);
    }

    /**
     * @param Organism $organism
     * @param OpcoCfaDockApiService $apiService
     * @param DataProviders $financer
     * @return array
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    private function computeDataToSynchronize(Organism $organism, OpcoCfaDockApiService $apiService, DataProviders $financer): array
    {
        $rawFolderStates = $apiService->listRawFolderStates($organism);
        $externalIdsFinancer = array_column($rawFolderStates, 'numeroInterne');
        $existingWorkingContracts = $this->workingContractService->listByExternalIdsFinancer($externalIdsFinancer, $financer, $organism);
        $workingContractsByExternalIdFinancer = Tools::groupBy($existingWorkingContracts, 'externalIdFinancer');
        $existingExternaldIdsFinancer = array_keys($workingContractsByExternalIdFinancer);
        return [
            'externalIdsFinancerToCreate' => array_diff($externalIdsFinancer, $existingExternaldIdsFinancer), // array of externalIdFinancer
            'workingContractsByExternalIdFinancer' => $workingContractsByExternalIdFinancer // associative array of [externalIdFinancer => [id, externalIdFinancer, state, lastUpdate]]
        ];
    }

    /**
     * @param Organism $organism
     * @param OpcoCfaDockApiService $apiService
     * @param array $externalIdsFinancer
     * @return array
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    private function fetchFolders(Organism $organism, OpcoCfaDockApiService $apiService, array $externalIdsFinancer): array
    {
        $externalIdsFinancerChunks = array_chunk($externalIdsFinancer, OpcoCfaDockApiService::FOLDERS_CHUNK_SIZE);
        $newRawFolders = [];
        sleep(1); // TODO(opco) do proper rate limiter
        foreach ($externalIdsFinancerChunks as $externalIdsFinancerChunk) {
            $newContractsChunk = $apiService->listRawFoldersByChunk($organism, $externalIdsFinancerChunk);
            $newRawFolders = array_merge($newRawFolders, $newContractsChunk);
        }
        return $newRawFolders;
    }

    /**
     * @param array $rawFolder
     * @param DataProviders $financer
     * @param RegistrationFolder $registrationFolder
     * @return WorkingContract
     * @throws Exception|Throwable
     */
    private function createWorkingContractFromRawFolder(array $rawFolder, DataProviders $financer, RegistrationFolder $registrationFolder): WorkingContract
    {
        $rawCerfa = $rawFolder['cerfa'];
        $employer = $this->organismService->getOrganism(['siret' => $rawCerfa['employeur']['siret']]);
        $workingContractData = $this->getWorkingContractDataFromRawFolder($rawFolder);
        $workingContractData['externalIdFinancer'] = $rawCerfa['numeroInterne'];
        $workingContractData['registrationFolder'] = $registrationFolder;
        $workingContractData['financer'] = $financer;
        $workingContractData['employer'] = $employer;
        return $this->workingContractService->create($workingContractData);
    }

    /**
     * @param int $workingContractId
     * @param array $rawFolder
     * @return WorkingContract
     * @throws Exception|Throwable
     */
    private function updateWorkingContractFromRawFolder(int $workingContractId, array $rawFolder): WorkingContract
    {
        $workingContract = $this->workingContractService->getById($workingContractId);
        if ($this->hasFolderChangedSinceLastUpdate($workingContract, $rawFolder)) {
            $workingContractData = $this->getWorkingContractDataFromRawFolder($rawFolder);
            $workingContract = $this->workingContractService->update($workingContract, $workingContractData);
            $registrationFolder = $workingContract->getRegistrationFolder();
            $this->opcoCfaApiService->updateRegistrationFolderFromWorkingContract($registrationFolder, $workingContract);
        }
        return $workingContract;
    }

    /**
     * @param array $rawFolder
     * @return array
     * @throws Exception
     */
    private function getWorkingContractDataFromRawFolder(array $rawFolder): array
    {
        $rawCerfa = $rawFolder['cerfa'];
        $rawContrat = $rawCerfa['contrat'];
        return [
            'externalIdTrainingOrganism' => $rawCerfa['numeroExterne'],
            'externalIdDeca' => $rawContrat['noContrat'],
            'state' => $this->getWorkingContractStateFromRawState($rawCerfa['etat'])->getValue(),
            'type' => $rawContrat['typeContratApp'],
            'amount' => $rawFolder['engagement'],
            'startDate' => new DateTime($rawContrat['dateDebutContrat']),
            'endDate' => new DateTime($rawContrat['dateFinContrat']),
            'signedDate' => $rawContrat['dateConclusion'] ? new DateTime($rawContrat['dateConclusion']) : null,
            'amendmentDate' => $rawContrat['dateEffetAvenant'] ? new DateTime($rawContrat['dateEffetAvenant']) : null,
            'breakingDate' => $rawContrat['dateRupture'] ? new DateTime($rawContrat['dateRupture']) : null,
            'rawData' => $rawFolder
        ];
    }

    private function getWorkingContractStateFromRawState(string $rawState): WorkingContractStates
    {
        return [
            'TRANSMIS' => WorkingContractStates::SENT(),
            'EN_COURS_INSTRUCTION' => WorkingContractStates::PENDING_ACCEPTATION(),
            'ENGAGE' => WorkingContractStates::ACCEPTED(),
            'ANNULE' => WorkingContractStates::CANCELLED(),
            'REFUSE' => WorkingContractStates::REFUSED(),
            'RUPTURE' => WorkingContractStates::BROKEN(),
            'SOLDE' => WorkingContractStates::COMPLETED()
        ][$rawState];
    }

    /**
     * @param WorkingContract $workingContract
     * @param array $rawFolder
     * @return bool
     */
    private function hasFolderChangedSinceLastUpdate(WorkingContract $workingContract, array $rawFolder): bool
    {
        return crc32(json_encode($workingContract->getRawData())) !== crc32(json_encode($rawFolder));
    }

    /**
     * DUMMY DATA JUST FOR RECETTE AT THE MOMENT
     * @param Organism $organism
     * @param DataProviders $financer
     * @return array
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function createNewWorkingContract(Organism $organism, DataProviders $financer): array
    {
        $rawEmployeurOcapiat = [
            'denomination' => 'CER FRANCE CANTAL', // OK $employer->getName()
            'siret' => '77907621500093', // OK $employer->getSiret()
            'naf' => '6920Z', // OK $employer->getApe()
            'nombreDeSalaries' => 200, // TODO Maybe additional data (range) https://recherche-entreprises.api.gouv.fr/search?q={{ $json.body.siret }}
            'codeIdcc' => '7020', // TODO Maybe additional data (array) https://recherche-entreprises.api.gouv.fr/search?q={{ $json.body.siret }}
            'telephone' => '**********', // $employer->getPhones()[0]
            'courriel' => '<EMAIL>', // $employer->getEmails()[0]
            'adresse' => [
                'adresse1' => 'ZA LA DINOTTE', // OK $employer->getAddress()
                'codePostal' => '15200', // OK $employer->getCity()
                'commune' => 'MAURIAC' // OK $employer->getPostalCode()
            ],
            'typeEmployeur' => 13, // TODO enum
            'caisseComplementaire' => 'AGIRC-AGRICA',// TODO
            'attestationEligibilite' => true, // TODO
            'attestationPieces' => true // TODO
        ];
        $rawEmployeurEP = [
            'denomination' => 'FONCIA MARSEILLE', // OK $employer->getName()
            'siret' => '06780391600245', // OK $employer->getSiret()
            'naf' => '6831Z', // OK $employer->getApe()
            'nombreDeSalaries' => 200, // TODO Maybe additional data (range) https://recherche-entreprises.api.gouv.fr/search?q={{ $json.body.siret }}
            'codeIdcc' => '1527', // TODO Maybe additional data (array) https://recherche-entreprises.api.gouv.fr/search?q={{ $json.body.siret }}
            'telephone' => '**********', // $employer->getPhones()[0]
            'courriel' => '<EMAIL>', // $employer->getEmails()[0]
            'adresse' => [
                'adresse1' => '32 COURS PIERRE PUGET', // OK $employer->getAddress()
                'codePostal' => '13006', // OK $employer->getCity()
                'commune' => 'MARSEILLE' // OK $employer->getPostalCode()
            ],
            'typeEmployeur' => 12, // TODO enum
            'caisseComplementaire' => 'HUMANIS',// TODO
            'attestationEligibilite' => true, // TODO
            'attestationPieces' => true, // TODO
            'regimeSpecifique' => false, // TODO SPECIFIC EP
            'employeurSpecifique' => 0 // TODO SPECIFIC EP
        ];
        $rawApprenti = [
            'nom' => 'DABAT', // OK $attendee->getBirthName(),
            'nomUsage' => 'DABAT', // OK $attendee->getLastName(),
            'prenom' => 'PAULINE', // OK $attendee->getFirstName(),
            'sexe' => 'F', // OK $attendee->getGender() === AttendeeGender::MALE()->getValue() ? 'M' : ($attendee->getGender() === AttendeeGender::FEMALE()->getValue() ? "F" : null),
            'nationalite' => 1, // TODO enum or maybe other source
            'dateNaissance' => '1998-03-16T00:00:00Z', // OK $attendee->getBirthDate() avec format bizarre,
            'departementNaissance' => '65', // OK from $attendee->getCodeCityOfBirth(),
            'communeNaissance' => 'TARBES', // OK $attendee->getCityOfBirth(),
            'nir' => '2XXXXXXXXXXXXX', // TODO
            'regimeSocial' => 2, // TODO enum
            'handicap' => false, // TODO
            'situationAvantContrat' => 4, // TODO enum
            'diplome' => 69, // TODO enum or maybe rawData degree
            'derniereClasse' => 1, // TODO enum
            'diplomePrepare' => 79, // TODO
            'intituleDiplomePrepare' => 'Master Marketing et Commerce Opérationnel', // TODO
            'telephone' => '0102030405', // OK $attendee->getPhoneNumber() ou phoneFixed ?,
            'courriel' => '<EMAIL>', // OK $attendee->getEmail(),
            'adresse' => [
                'adresse1' => '12 rue de la république', // OK from $attendee->getAddress()
                'codePostal' => '31500', // OK from $attendee->getAddress()
                'commune' => 'TOULOUSE' // OK from $attendee->getAddress()
            ],
            'inscriptionSportifDeHautNiveau' => false, // TODO
            'projetCreationRepriseEntreprise' => false // TODO
        ];
        $rawMaitre1 = [
            'nom' => 'Jean', // TODO
            'prenom' => 'Dupont', // TODO
            'dateNaissance' => '1988-01-01T00:00:00Z', // TODO
            'courriel' => '<EMAIL>' // TODO
        ];
        $rawFormation = [
            'rncp' => '35961', // OK $certification->getCode(),
            'codeDiplome' => '16X31217', // TODO (je l'avais trouvé sur l'open data du carif je crois)
            'typeDiplome' => 79, // TODO enum
            'intituleQualification' => 'Manager de Business Unit', // OK $certification->getName() mais peut être pas à jour (chercher sur l'open data carif ?)
            'dateDebutFormation' => '2025-03-14T00:00:00Z', // TODO
            'dateFinFormation' => '2026-06-26T00:00:00Z', // TODO
            'dureeFormation' => 402 // TODO ou maybe estimation ?
        ];
        $rawContrat = [
            'modeContractuel' => 1, // TODO enum
            'typeContratApp' => 11, // TODO enum
            'dateDebutContrat' => '2025-03-14T00:00:00Z', // TODO
            'dateFormationPratiqueEmployeur' => '2025-03-14T00:00:00Z', // TODO is it really required ?
            'dateConclusion' => '2025-01-10T00:00:00Z', // TODO
            'dateFinContrat' => '2026-06-26T00:00:00Z', // TODO
            'lieuSignatureContrat' => 'TOULOUSE', //TODO
            'dureeTravailHebdoHeures' => 35, // TODO
            'dureeTravailHebdoMinutes' => 0, // TODO
            'travailRisque' => false, // TODO
            'salaireEmbauche' => 1378.19, // TODO
            'remunerationsAnnuelles' => [
                [
                    'ordre' => '1.1', // TODO auto ?
                    'dateDebut' => '2025-03-14T00:00:00Z', // TODO
                    'dateFin' => '2026-06-26T00:00:00Z', // TODO
                    'taux' => 78, // TODO
                    'typeSalaire' => 'SMIC' // TODO
                ]
            ]
        ];
        $rawOrganismeFormation = [
            'formationInterne' => false, // TODO presque tjrs faux j'imagine
            'denomination' => 'STUDI', // OK $organism->getName() si à jour ?
            'siret' => '91114836900018', // OK $organism->getSiret()
            'uaiCfa' => '0756369R', // OK si on l'a $organism->getUaiNumber()
            'adresse' => [
                'adresse1' => '155 RUE DE CHARONNE', // OK $organism->getAddress(),
                'codePostal' => '75011', // OK $organism->getPostalCode(),
                'commune' => 'PARIS 11' // OK $organism->getCity(),
            ],
            'lieuFormationIdentique' => true // TODO requis mais nullable très chelou
        ];
        $rawVersionCerfa = '10103*10'; // TODO ENUM WTF
        $rawEmployeur = $financer->getValue() === DataProviders::OPCO_CFA_OCAPIAT()->getValue() ? $rawEmployeurOcapiat : $rawEmployeurEP;
        $rawFolder = [
            'cerfa' => [
                'employeur' => $rawEmployeur, // Doit être sur l'opco concerné
                'apprenti' => $rawApprenti, // NIR doit exister et matcher le nom
                'maitre1' => $rawMaitre1,
                'formation' => $rawFormation,
                'contrat' => $rawContrat,
                'organismeFormation' => $rawOrganismeFormation,
                'versionCERFA' => $rawVersionCerfa
            ]
        ];
        /** @var OpcoCfaDockApiService $apiService */
        $apiService = BaseApiService::getApiServiceByDataProvider($financer);
        $result = $apiService->createFolder($organism, $rawFolder);
        $this->logger->error(print_r($result, true));
        return $result;
    }
}
