<?php


namespace App\Service\DataProviders;


use App\Entity\Connection;
use App\Entity\Organism;
use App\Entity\Session;
use App\Library\utils\enums\DataProviders;
use Throwable;

class PoleEmploiApiService extends RegistrationFolderInternalApiService implements InterfaceRegistrationFolderApiService
{

    //-------------------
    // METHODES PUBLIQUES
    //-------------------
    /**
     * @param Session $session
     * @param array $rawData
     * @param Organism $organism
     * @return array
     * @throws Throwable
     */
    public function preCreateRegistrationFolder(Session $session, array $rawData, Organism $organism): array
    {
        $preCreateRawData = parent::preCreateRegistrationFolder($session, $rawData, $organism);
        $rawDataPoleEmploi = [
            'poleEmploiDevis' => $rawData['poleEmploiDevis']
        ];
        return array_merge($preCreateRawData, $rawDataPoleEmploi);
    }


    /**
     * @param array $rawData
     * @param DataProviders $dataProvider
     * @return string
     */
    public function generateRegistrationFolderId(array $rawData, DataProviders $dataProvider): string
    {
        // Override the parent so we don't call it
        $prefix = self::getDataProviderPrefix($dataProvider->getValue());
        return $prefix . trim($rawData['poleEmploiDevis']);
    }

    /**
     * @return int
     */
    public function getMaxAttemptsBeforeStop(): int
    {
        return 3;
    }

    /**
     * @param Organism $organism
     * @param Connection $connection
     * @param array $params
     * @return bool
     */
    public function habilitate(Organism $organism, Connection $connection, array $params): bool
    {
        return false;
    }

    /**
     * @param Organism $organism
     * @param array|null $params
     * @return string|bool
     */
    public function checkBeforeHabilitate(Organism $organism, array $params = null): bool
    {
        return false;
    }
}