<?php

namespace App\Service\DataProviders;

use App\Entity\Connection;
use App\Entity\Organism;
use App\Exception\WedofConnectionException;

interface InterfaceAuthenticationApiService
{

    /**
     * @return int
     */
    public function getMaxAttemptsBeforeStop(): int;

    /**
     * @param Connection $connection
     * @param bool $checkOrganismAccess
     * @return array
     * @throws WedofConnectionException
     */
    public function authenticate(Connection $connection, bool $checkOrganismAccess = true): array;


    /**
     * @param Organism $organism
     * @param array|null $params
     * @return bool|array|string
     */
    public function checkBeforeHabilitate(Organism $organism, array $params = null);

    /**
     * @param Organism $organism
     * @param Connection $connection
     * @param array $params
     * @return bool
     * @throws WedofConnectionException
     */
    public function habilitate(Organism $organism, Connection $connection, array $params): bool;

    /**
     * @param Connection $connection
     * @return string
     */
    public function getUsername(Connection $connection): string;

    /**
     * @return bool
     */
    public function requiresAuthentication(): bool;

    /**
     * @param Organism $organism
     * @return bool
     */
    public function setActiveOrganism(Organism $organism): bool;

    /**
     * @param array $options
     * @return array
     */
    public function setAuthentication(array $options = []): array;

    /**
     * @param Connection $connection
     * @return void
     */
    public function startInitialization(Connection $connection): void;

    /**
     * @param Connection $connection
     * @return void
     */
    public function initializeCompleted(Connection $connection): void;
}
