<?php
// src/Service/CpfApiService.php
// <PERSON> was here
namespace App\Service\DataProviders;

use App\Entity\Certification;
use App\Entity\Connection;
use App\Entity\Organism;
use App\Entity\Payment;
use App\Entity\RegistrationFolder;
use App\Entity\RegistrationFolderReason;
use App\Entity\Session;
use App\Entity\Training;
use App\Entity\TrainingAction;
use App\Entity\User;
use App\Event\Connection\ConnectionEvents;
use App\Event\MonitoringEvents;
use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofConnectionException;
use App\Exception\WedofCpfBackendException;
use App\Library\utils\enums\ConnectionStates;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\enums\PaymentSortParams;
use App\Library\utils\enums\PaymentStates;
use App\Library\utils\enums\PaymentTypes;
use App\Library\utils\enums\RegistrationFolderAttendeeStates;
use App\Library\utils\enums\RegistrationFolderBillingStates;
use App\Library\utils\enums\RegistrationFolderControlStates;
use App\Library\utils\enums\RegistrationFolderSortParams;
use App\Library\utils\enums\RegistrationFolderStates;
use App\Library\utils\enums\SessionStates;
use App\Library\utils\enums\SolicitationFundingType;
use App\Library\utils\enums\SubscriptionTrainingTypes;
use App\Library\utils\enums\TrainingActionStates;
use App\Library\utils\enums\TrainingStates;
use App\Library\utils\SimpleXMLElementTools;
use App\Library\utils\Tools;
use App\Message\SynchronizeCatalog;
use App\Message\SynchronizePayments;
use App\Repository\EndPointStatusRepository;
use App\Service\CertificationService;
use App\Service\ConfigService;
use App\Service\ConnectionService;
use App\Service\CronTaskManagerService;
use App\Service\EvaluationService;
use App\Service\OrganismService;
use App\Service\RegistrationFolderService;
use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use ErrorException;
use Exception;
use Lcobucci\JWT\Parser;
use LogicException;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ContainerInterface as Container;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpClient\CurlHttpClient;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Security\Core\Security;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Throwable;

class CpfApiService extends BaseApiService implements InterfaceRegistrationFolderApiService, InterfaceCatalogApiService
{
    const MAX_LOGIN_ATTEMPTS = 3;
    private const HABILITATION_EDOF_LECTEUR = "LECTEUR";
    private const HABILITATION_EDOF_GESTIONNAIRE = "GESTIONNAIRE";

    private static array $UPDATABLE_REGISTRATIONFOLDER_TRAININGACTIONINFO_FIELDS = [
        'title',
        'trainingGoal',
        'sessionStartDate',
        'sessionEndDate',
        'teachingModalities',
        'expectedResult',
        'content',
        'trainingPace',
        'additionalFees',
        'additionalFeesDetails',
        'vat',
        'totalIncl',
        'totalExcl',
        'vatInclTax5',
        'vatExclTax5',
        'vatInclTax20',
        'vatExclTax20',
        'weeklyDuration',
        'indicativeDuration',
        'hoursInCenter',
        'hoursInCompany',
        'address',
        'typeOfTrainingCourse'
    ];

    private static array $LABEL_TO_STATE = [
        "Envoi demande d'inscription" => ["Titulaire" => "notProcessed"],
        "Proposition de l'organisme" => ["Organisme" => "validated"],
        "Validation de la proposition" => ["Titulaire" => "accepted"],
        "Déclaration service fait" => ["Organisme" => "serviceDoneDeclared"],
        "Service fait validé" => ["CDC" => "serviceDoneValidated"],
        "Annulé par la CDC" => ["CDC" => "canceledByCDC"],
        "Refus de la proposition" => ["Titulaire" => "refusedByAttendee"],
        "Refus de la demande d'inscription" => ["Organisme" => "refusedByOrganism"]
    ];

    private RegistrationFolderService $registrationFolderService;
    private OrganismService $organismService;
    private CertificationService $certificationService;
    private MessageBusInterface $messageBus;
    private Container $container;

    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    /**
     * CpfApiService constructor.
     * @param OrganismService $organismService
     * @param RegistrationFolderService $registrationFolderService
     * @param EndPointStatusRepository $endPointStatusRepository
     * @param EventDispatcherInterface $dispatcher
     * @param LoggerInterface $logger
     * @param ConfigService $configService
     * @param RequestStack $requestStack
     * @param Container $container
     * @param ConnectionService $connectionService
     * @param CertificationService $certificationService
     * @param MessageBusInterface $messageBus
     * @param Security $security
     */
    public function __construct(
        OrganismService           $organismService,
        RegistrationFolderService $registrationFolderService,
        EndPointStatusRepository  $endPointStatusRepository,
        EventDispatcherInterface  $dispatcher,
        LoggerInterface           $logger,
        ConfigService             $configService,
        RequestStack              $requestStack,
        Container                 $container,
        ConnectionService         $connectionService,
        CertificationService      $certificationService,
        MessageBusInterface       $messageBus,
        Security $security)
    {
        parent::__construct(DataProviders::CPF(), $configService, $connectionService, $requestStack, $endPointStatusRepository, $dispatcher, $logger, $security);
        $this->registrationFolderService = $registrationFolderService;
        $this->organismService = $organismService;
        $this->container = $container;
        $this->certificationService = $certificationService;
        $this->messageBus = $messageBus;
    }

    /**
     * @param LoggerInterface $logger
     */
    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    /**
     * Legacy PUT/POST to EDOF catalog
     * @param Organism $organism
     * @param array $data
     * @return array|null
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function createTraining(Organism $organism, array $data): array
    {
        $rawdata = [];
        $this->logger->info("create training data");
        foreach ($data as $key => $value) {
            if ($key != "id") {
                $rawdata[$key] = $value;
            }
        }
        $CPF_Request_URL = Tools::getEnvValue("CPF_API_BASE_URI") . '/private/siret/' . $organism->getSiret() . '/trainings?wantedStatus=DRAFT';
        $options = [
            "organism" => $organism,
            'method' => 'POST',
            'parameters' => $rawdata,
            'cat_api_status' => "private-training"
        ];
        return $this->sendRequest($CPF_Request_URL, $options);
    }

    /**
     *  Legacy PUT/POST to EDOF catalog
     * @param Training $training
     * @param $data
     * @return array|null
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function updateTraining(Training $training, $data): array
    {
        $rawdata = $this->getPrivateTrainingRawData($training->getOrganism(), $this->getTrainingShortExternalIdFromTraining($training));
        $this->logger->info($training->getExternalId() . " update training data");
        foreach ($data as $key => $value) {
            $rawdata[$key] = $value;
        }
        $CPF_Request_URL = Tools::getEnvValue("CPF_API_BASE_URI") . '/private/siret/' . $training->getOrganism()->getSiret() . '/trainings/' . $this->getTrainingShortExternalIdFromTraining($training) . "?wantedStatus=" . $rawdata['statusLabel'];
        $options = [
            'method' => 'PUT',
            "organism" => $training->getOrganism(),
            'parameters' => $rawdata,
            'cat_api_status' => "private-training"
        ];
        return $this->sendRequest($CPF_Request_URL, $options);
    }

    /**
     * Legacy PUT/POST to EDOF catalog
     * @param Training $training
     * @param string $state
     * @return array|null
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function updateTrainingState(Training $training, string $state): ?array
    {
        $rawdata = $this->getPrivateTrainingRawData($training->getOrganism(), $this->getTrainingShortExternalIdFromTraining($training));
        $this->logger->info($training->getExternalId() . " update training data");
        $CPF_Request_URL = Tools::getEnvValue("CPF_API_BASE_URI") . '/private/siret/' . $training->getOrganism()->getSiret() . '/trainings/' . $this->getTrainingShortExternalIdFromTraining($training) . "?wantedStatus=" . $state;
        $options = [
            'method' => 'PUT',
            'parameters' => $rawdata,
            "organism" => $training->getOrganism(),
            'cat_api_status' => "private-training"
        ];
        return $this->sendRequest($CPF_Request_URL, $options);
    }

    /**
     * Legacy PUT/POST to EDOF catalog
     * @param Training $training
     * @param array $data
     * @return array|false|null
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function createTrainingAction(Training $training, array $data)
    {
        //check if exist first
        $CPF_Request_URL = Tools::getEnvValue("CPF_API_BASE_URI") . '/private/siret/' . $training->getOrganism()->getSiret() . '/trainings/' . $this->getTrainingShortExternalIdFromTraining($training) . '/actions/' . $data['shortId'];
        $options = [
            'method' => 'GET',
            'cat_api_status' => "private-trainingAction",
            "organism" => $training->getOrganism(),
            'retryOn200Null' => false
        ];
        $result = $this->sendRequest($CPF_Request_URL, $options);
        if (isset($result['response']) && $result['response'] === true) {
            $this->logger->debug("trainingAction don't exist continue...");
        } else {
            $this->logger->debug("trainingAction exist stop...");
            return false;
        }
        //initialize it with nothing why don't know but works better
        $CPF_Request_URL = Tools::getEnvValue("CPF_API_BASE_URI") . '/private/siret/' . $training->getOrganism()->getSiret() . '/trainings/' . $this->getTrainingShortExternalIdFromTraining($training) . '/actions?wantedStatus=DRAFT';
        $options = [
            'method' => 'POST',
            'cat_api_status' => "private-trainingAction",
            'parameters' => ["shortId" => $data['shortId']],
            "organism" => $training->getOrganism()
        ];
        $rawdata = $this->sendRequest($CPF_Request_URL, $options);
        if ($rawdata['id']) {
            $this->logger->debug("create trainingAction data");
            foreach ($data as $key => $value) {
                if ($key != "id") {
                    $rawdata[$key] = $value;
                }
            }
            $rawdata['trainingId'] = $training->getExternalId();
            $rawdata['trainingShortId'] = $this->getTrainingShortExternalIdFromTraining($training);
            $CPF_Request_URL = Tools::getEnvValue("CPF_API_BASE_URI") . '/private/siret/' . $training->getOrganism()->getSiret() . '/trainings/' . $this->getTrainingShortExternalIdFromTraining($training) . '/actions?wantedStatus=DRAFT';
            $options = [
                'method' => 'POST',
                'cat_api_status' => "private-trainingAction",
                'parameters' => $rawdata,
                "organism" => $training->getOrganism()
            ];
            return $this->sendRequest($CPF_Request_URL, $options);
        } else {
            $this->logger->error("trainingAction not created...");
            return false;
        }
    }

    /**
     * Legacy PUT/POST to EDOF catalog
     * @param TrainingAction $trainingAction
     * @param $data
     * @return array|null
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function updateTrainingAction(TrainingAction $trainingAction, $data): array
    {
        $rawdata = $this->getPrivateTrainingActionRawData($trainingAction->getTraining(), $this->getShortTrainingActionExternalIdFromTrainingAction($trainingAction));
        $this->logger->info($trainingAction->getExternalId() . " update training action data");
        foreach ($data as $key => $value) {
            $rawdata[$key] = $value;
        }
        $finalData = [];
        $fields = ["shortId", "modificationDate", "creationDate", "trainingAddress", "minLevelMandatory", "haveSpecificConditionsAndPrerequisites", "specificConditionsAndPrerequisites", "admissionInformation", "admissionModality", "recruitmentInformation", "geographicalPerimeter", "informationRecruitmentPerimeter", "language", "jobGroup", "tvaHT0", "tvaHT5_5", "tvaTTC5_5", "tvaHT20", "tvaTTC20", "totalTvaHT", "totalTvaTTC", "additionalFees", "certificationFees", "additionalFeesDetails", "scheduleType", "teachingModality", "teachingMethod", "averageLearningTime", "numberOfHoursInCenter", "numberOfHoursInCompany", "paces", "inOutModality", "modalitiesPeopleDisabilities", "accessibilityActionForPeopleWithReducedMobility", "informationDates", "trainingContact", "informationContact", "informationAddress", "urlAction", "restauration", "accommodation", "transport", "other", "tariffConstruction"];
        foreach ($fields as $field) {
            $finalData[$field] = $rawdata[$field];
        }
        $CPF_Request_URL = Tools::getEnvValue("CPF_API_BASE_URI") . '/private/siret/' . $trainingAction->getTraining()->getOrganism()->getSiret() . '/trainings/' . $this->getShortTrainingExternalIdFromTrainingAction($trainingAction) . "/actions/" . $this->getShortTrainingActionExternalIdFromTrainingAction($trainingAction) . "?wantedStatus=" . $rawdata['statusLabel'];
        $options = [
            'method' => 'PUT',
            "organism" => $trainingAction->getTraining()->getOrganism(),
            'cat_api_status' => "private-trainingAction",
            "parameters" => $finalData
        ];
        return $this->sendRequest($CPF_Request_URL, $options);
    }

    /**
     * Legacy PUT/POST to EDOF catalog
     * @param TrainingAction $trainingAction
     * @param string $state
     * @return array|null
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function updateTrainingActionState(TrainingAction $trainingAction, string $state): array
    {
        $rawdata = $this->getPrivateTrainingActionRawData($trainingAction->getTraining(), $this->getShortTrainingActionExternalIdFromTrainingAction($trainingAction));
        $this->logger->info($trainingAction->getExternalId() . " update training action data");
        $CPF_Request_URL = Tools::getEnvValue("CPF_API_BASE_URI") . '/private/siret/' . $trainingAction->getTraining()->getOrganism()->getSiret() . '/trainings/' . $this->getShortTrainingExternalIdFromTrainingAction($trainingAction) . "/actions/" . $this->getShortTrainingActionExternalIdFromTrainingAction($trainingAction) . "?wantedStatus=" . $state;
        $options = [
            'method' => 'PUT',
            'cat_api_status' => "private-trainingAction",
            "organism" => $trainingAction->getTraining()->getOrganism(),
            "parameters" => $rawdata
        ];
        return $this->sendRequest($CPF_Request_URL, $options);
    }

    /**
     * Legacy PUT/POST to EDOF catalog
     * @param TrainingAction $trainingAction
     * @param array $_rawdata
     * @return array|null
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function createSession(TrainingAction $trainingAction, array $_rawdata): array
    {
        $rawdata = [];
        $this->logger->info("create session data");
        foreach ($_rawdata as $key => $value) {
            if ($key != "id") {
                $rawdata[$key] = $value;
            }
        }
        $CPF_Request_URL = Tools::getEnvValue("CPF_API_BASE_URI") . '/private/siret/' . $trainingAction->getTraining()->getOrganism()->getSiret() . '/trainings/' . $this->getTrainingShortExternalIdFromTraining($trainingAction->getTraining()) . '/actions/' . $this->getShortTrainingActionExternalIdFromTrainingAction($trainingAction) . '/sessions?wantedStatus=DRAFT';
        $options = [
            'method' => 'POST',
            'parameters' => $rawdata,
            "organism" => $trainingAction->getTraining()->getOrganism(),
            'cat_api_status' => "private-session"
        ];
        return $this->sendRequest($CPF_Request_URL, $options);
    }

    /**
     * Legacy PUT/POST to EDOF catalog
     * @param Session $session
     * @param $state
     * @return array|null
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function updateSessionState(Session $session, $state): array
    {
        if ($state == "OPENED") {
            $state = "ACTIVE&wantedRecruitmentStatus=OPENED";
        } else if ($state == "CLOSED") {
            $state = "ACTIVE&wantedRecruitmentStatus=CLOSED";
        }
        $rawdata = $this->getPrivateSessionRawData($session->getTrainingAction(), $this->getSessionShortExternalIdFromSession($session));
        $CPF_Request_URL = Tools::getEnvValue("CPF_API_BASE_URI") . '/private/siret/' . $session->getTrainingAction()->getTraining()->getOrganism()->getSiret() . '/trainings/' . $this->getShortTrainingExternalIdFromSession($session) . "/actions/" . $this->getShortTrainingActionExternalIdFromSession($session) . "/sessions/" . $this->getSessionShortExternalIdFromSession($session) . "?wantedStatus=" . $state;
        $options = [
            'method' => 'PUT',
            'cat_api_status' => "private-session",
            "organism" => $session->getTrainingAction()->getTraining()->getOrganism(),
            "parameters" => $rawdata
        ];
        return $this->sendRequest($CPF_Request_URL, $options);
    }

    /**
     * @param Organism $organism
     * @param array $parameters
     * @param array $options
     * @return array
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function getRegistrationFoldersRawData(Organism $organism, array $parameters = array(), array $options = array()): array
    {
        $options = array_merge(["withDetails" => false, "onlyItems" => true, "requestDispatchMonitoringEvent" => true, "maxRetry" => Tools::getEnvValue('CPF_API_MAX_RETRY')], $options);
        $limit = !isset($parameters['limit']) ? 0 : $parameters['limit'];
        $skip = !isset($parameters['skip']) ? 0 : $parameters['skip'];
        $state = !isset($parameters['state']) ? RegistrationFolderStates::ALL() : $parameters['state'];
        $order = !isset($parameters['order']) ? 'DESC' : $parameters['order'];
        $filter = !isset($parameters['filter']) ? $this->getFilterForState($state) : $parameters['filter'];
        $CPF_Request_URL = Tools::getEnvValue("CPF_API_BASE_URI") . "/private/siret/" . $organism->getSiret() . "/organisms/current/registration-folders" . "?filter=" . $filter . "&limit=" . $limit . "&skip=" . $skip . "&state=" . $state . "&order=" . $order;
        $CPF_Request_URL = !isset($parameters['sort']) ? $CPF_Request_URL : $CPF_Request_URL . "&sort=" . $parameters['sort'];

        $_options = [
            "parameters" => $parameters,
            'cat_api_status' => "private-registrationFolders-list",
            "organism" => $organism,
            "dispatchMonitoringEvent" => $options['requestDispatchMonitoringEvent'],
            "maxRetry" => $options['maxRetry']
        ];

        $registrationFoldersRawData = $this->sendRequest($CPF_Request_URL, $_options);
        foreach ($registrationFoldersRawData['items'] as &$registrationFolderRawData) {
            $registrationFolderRawData['siret'] = $organism->getSiret();
            $registrationFolderRawData['type'] = $this->dataProvider->getValue();
        }
        if ($options['onlyItems']) {
            $registrationFoldersRawData = $registrationFoldersRawData['items'];
        }
        if ($options['withDetails']) {
            $this->logger->debug("registration folders  with details: " . sizeof($registrationFoldersRawData));
            foreach ($registrationFoldersRawData as &$registrationFolderRawData) {
                $this->logger->debug("registration folder details for : " . $registrationFolderRawData['id']);
                $registrationFolderRawData = $this->getRegistrationFolderRawData($organism, $registrationFolderRawData['id'], $registrationFolderRawData, $registrationFolderRawData['currentBillingState']);
            }
        }
        return $registrationFoldersRawData;
    }

    /**
     * @param Organism $organism
     * @param string $registrationFolderId
     * @param array|null $registrationFolderDataToMerge
     * @param string|null $overrideCurrentBillingState
     * @return array|null
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function getRegistrationFolderRawData(Organism $organism, string $registrationFolderId, array $registrationFolderDataToMerge = null, string $overrideCurrentBillingState = null): ?array
    {
        $CPF_Request_URL = Tools::getEnvValue("CPF_API_BASE_URI") . "/private/siret/" . $organism->getSiret() . "/organisms/current/registration-folders/" . $this->cleanId($registrationFolderId);
        $options = [
            "organism" => $organism,
            'cat_api_status' => "private-registrationFolder"
        ];

        if (isset($registrationFolderDataToMerge['billingDate'])) {
            if (DateTime::createFromFormat('d/m/Y', $registrationFolderDataToMerge['billingDate'])) {
                $registrationFolderDataToMerge['billingDate'] = date("Y-m-d", strtotime(str_replace('/', '-', $registrationFolderDataToMerge['billingDate'])));
            }
        }

        try {
            $rawData = $this->sendRequest($CPF_Request_URL, $options);
            //keep maximum data
            if ($registrationFolderDataToMerge) {
                foreach ($rawData as $key => $value) {
                    if (!empty($registrationFolderDataToMerge[$key]) && empty($value)) {
                        $rawData[$key] = $registrationFolderDataToMerge[$key];
                    }
                }
            }
            $rawData['missingData'] = false;
        } catch (Exception $e) {
            if ($registrationFolderDataToMerge) {
                $rawData = $registrationFolderDataToMerge;
                $rawData['missingData'] = true;
            } else {
                throw $e;
            }
        }

        // gérer le fait que l'état "canceledByFinancer" s'appelle "canceledByCDC" pour les dossiers CPF
        if ($rawData['currentState'] === RegistrationFolderStates::CANCELED_BY_CDC()->getValue()) {
            $rawData['currentState'] = RegistrationFolderStates::CANCELED_BY_FINANCER()->getValue();
            $rawData['states'][RegistrationFolderStates::CANCELED_BY_FINANCER()->getValue()] = $rawData['states'][RegistrationFolderStates::CANCELED_BY_CDC()->getValue()];
        }

        try {
            $rawData = $this->getRegistrationFolderDetailsRawData($organism, $rawData, $overrideCurrentBillingState);
        } catch (Exception $e) {
            if ($e->getCode() === 500) { //weird
                $rawData['missingBillingData'] = true;
            }
        }
        $rawData["type"] = DataProviders::CPF()->getValue();//@todo mettre ici ou dans les fonctions de l'interface InterfaceCatalogApiService ?
        $rawData["siret"] = $organism->getSiret();

        return $rawData;
    }

    /**
     * @param Organism $organism
     * @param array $rawData
     * @param string|null $overrideCurrentBillingState
     * @return array
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function getRegistrationFolderDetailsRawData(Organism $organism, array $rawData, string $overrideCurrentBillingState = null): array
    {
        //override billingState with billingState from another context
        if ($overrideCurrentBillingState) {
            $rawData['currentBillingState'] = $overrideCurrentBillingState;
        }

        //toBill or Billed state
        if (!$overrideCurrentBillingState && (in_array($rawData['currentState'], RegistrationFolderStates::billableStates()) && isset($rawData['idSuiviFinancement']))) {
            $CPF_Request_URL = Tools::getEnvValue("CPF_API_BASE_URI") . "/private/siret/" . $organism->getSiret() . "/organisms/current/bills/lite?idSuiviFinancement=" . $rawData['idSuiviFinancement'];
            $options = [
                "organism" => $organism,
                'cat_api_status' => "private-registrationFolder"
            ];
            $arrayBilling = $this->sendRequest($CPF_Request_URL, $options);
            if (isset($arrayBilling['currentState'])) {
                $rawData['currentBillingState'] = $arrayBilling['currentState'];
            }
        }

        // conversion du "etatBlocage" de la CDC en "controlState"
        $rawData['controlState'] = $this->getControlStateFromRawData($rawData)->getValue();

        //serviceDoneDeclared OF / Attendee state / Control date
        $rawData['currentAttendeeState'] = RegistrationFolderAttendeeStates::SERVICE_DONE_NOT_DECLARED()->getValue();

        // ajout de la clé pour valorisation de l'historique pour l'état rejectedWithoutTitulaireSuite
        $rawData['states']['rejectedWithoutTitulaireSuite'] = $rawData['states']['rejected'] ?? null;

        if (DateTime::createFromFormat('d/m/Y', $rawData['states']['notProcessed']['date'])) {
            // conversion des dates du format d/m/Y vers le format Y-m-y qui peut être converti en datetime dans le RFService / setHistoryMapping
            $states = [];
            foreach ($rawData['states'] as $state => $value) {
                if (isset($value['date'])) {
                    $states[$state]['date'] = date("Y-m-d\TH:i:s\Z", strtotime(str_replace('/', '-', $value['date'])));
                }
            }
            $rawData['states'] = array_merge($rawData['states'], $states);
        }

        if (!empty($rawData['history'])) {
            foreach ($rawData['history'] as $historyEntry) {
                if (isset($historyEntry['label']) && !empty($historyEntry['date'])) {
                    // l'auteur est parfois manquant (null) pour les dossiers en contrôle
                    if ($historyEntry['label'] === 'Dossier en contrôle') {
                        $rawData['inControlDate'] = new DateTime($historyEntry['date']); // conversion en datetime dès maintenant pour simplifier le code générique du RFService
                        if ($rawData['controlState'] === RegistrationFolderControlStates::RELEASED()->getValue()) {
                            $rawData['releasedDate'] = (new DateTime($historyEntry['date']))->modify('+1 minute'); // en l'absence d'une date explicite pour le passage à RELEASED
                        }
                    }

                    if (isset($historyEntry['author'])) {
                        if (isset(self::$LABEL_TO_STATE[$historyEntry['label']][$historyEntry['author']])) {
                            $rawData['states'][self::$LABEL_TO_STATE[$historyEntry['label']][$historyEntry['author']]]['date'] = $historyEntry['date'];
                        } else if ($historyEntry['label'] === 'Annulation du dossier') {
                            switch ($historyEntry['author']) {
                                case "CDC":
                                    if ($rawData['currentState'] === 'rejectedWithoutTitulaireSuite') {
                                        $rawData['states']['rejectedWithoutTitulaireSuite']['date'] = $historyEntry['date'];
                                    } else if ($rawData['currentState'] === 'rejected') {
                                        $rawData['states']['rejected']['date'] = $historyEntry['date'];
                                    }
                                    break;
                                case "Titulaire":
                                    if ($rawData['currentState'] === 'canceledByAttendee') {
                                        $rawData['states']['canceledByAttendee']['date'] = $historyEntry['date'];
                                    } else if ($rawData['currentState'] === 'canceledByAttendeeNotRealized') {
                                        $rawData['states']['canceledByAttendeeNotRealized']['date'] = $historyEntry['date'];
                                    }
                                    break;
                                case "Organisme":
                                    $rawData['states']['canceledByOrganism']['date'] = $historyEntry['date'];
                                    break;
                                default:
                                    break;
                            }
                        } else if ($historyEntry['label'] === 'Déclaration service fait titulaire') { // ancienne façon de faire de la CDC
                            $rawData['currentAttendeeState'] = RegistrationFolderAttendeeStates::SERVICE_DONE_DECLARED()->getValue();
                            $rawData['currentAttendeeStateDate'] = $historyEntry['date'];
                        } else if ($historyEntry['label'] === 'Déclaration service fait' && $historyEntry['author'] === 'Titulaire') { // nouvelle façon de faire de la CDC
                            $rawData['currentAttendeeState'] = RegistrationFolderAttendeeStates::SERVICE_DONE_DECLARED()->getValue();
                            $rawData['currentAttendeeStateDate'] = $historyEntry['date'];
                        }
                    }
                }
            }
        }

        //otherwise not billable..
        if (empty($rawData['currentBillingState'])) {
            $rawData['currentBillingState'] = RegistrationFolderBillingStates::NOT_BILLABLE()->getValue();
        }

        $rawData = $this->mergeWithBillingData($organism, $rawData);
        $rawData["organismSiret"] = $organism->getSiret();
        return $this->mergeWithSolicitationData($rawData);
    }

    /**
     * Donne le nombre de registration folders existant pour un organisme
     *
     * @param Organism $organism
     * @param null $state
     * @param null $sort
     * @param array $options
     * @return int
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function getRegistrationFoldersCount(Organism $organism, $state = null, $sort = null, array $options = array()): int
    {
        $options = array_merge(["onlyItems" => false, 'requestDispatchMonitoringEvent' => true], $options);
        $registrationFoldersRawData = $this->getRegistrationFoldersRawData($organism, [
            'state' => $state ?: RegistrationFolderStates::ALL(),
            'limit' => 1,
            'sort' => $sort ?: RegistrationFolderSortParams::ETAT()
        ], $options);
        return $registrationFoldersRawData['numberOfItems'];
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @param array $data
     * @return RegistrationFolder
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function updateRegistrationFolder(RegistrationFolder $registrationFolder, array $data): RegistrationFolder
    {
        $rawData = $registrationFolder->getRawData();
        if ($registrationFolder->getState() == RegistrationFolderStates::NOT_PROCESSED() && isset($data['trainingActionInfo']) && isset($rawData['trainingActionInfo'])) {
            $cpfUpdate = false;
            foreach (self::$UPDATABLE_REGISTRATIONFOLDER_TRAININGACTIONINFO_FIELDS as $field) {
                if (isset($data['trainingActionInfo'][$field]) && $data['trainingActionInfo'][$field] != $rawData['trainingActionInfo'][$field]) {
                    $cpfUpdate = true;
                    $this->logger->debug("$field changed! from " . $rawData['trainingActionInfo'][$field] . " to " . (is_array($data['trainingActionInfo'][$field]) ? 'array values..' : $data['trainingActionInfo'][$field]));
                    $rawData['trainingActionInfo'][$field] = $data['trainingActionInfo'][$field];
                }
            }
            if (array_key_exists('states', $rawData)) {
                unset($rawData['states']);
            }
            if ($cpfUpdate) {
                $CPF_Request_URL = Tools::getEnvValue("CPF_API_BASE_URI") . '/private/siret/' . $registrationFolder->getOrganism()->getSiret() . '/organisms/current/registration-folders/' . $registrationFolder->getExternalId();
                $options = [
                    'method' => 'PUT',
                    "organism" => $registrationFolder->getOrganism(),
                    "parameters" => $rawData,
                    'cat_api_status' => "private-registrationFolderUpdate"
                ];
                if ($this->sendRequest($CPF_Request_URL, $options)) { // in this case the request return true / false (CPF fault..)
                    return $this->refreshRegistrationFolder($registrationFolder);
                } else {
                    throw new WedofBadRequestHttpException("Erreur sur la mise à jour, veuillez réessayer.");
                }
            }
        }
        return $registrationFolder;
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @param array $data
     * @return RegistrationFolder
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function validateRegistrationFolder(RegistrationFolder $registrationFolder, array $data = []): RegistrationFolder
    {
        $rawData = $registrationFolder->getRawData();
        $trainingActionInfo = $rawData['trainingActionInfo'];
        $data['indicativeDuration'] = $data['indicativeDuration'] ?? ($trainingActionInfo['indicativeDuration'] ?? null);
        if (empty($data['indicativeDuration']) && $registrationFolder->isAttendeeJobSeeker()) {
            throw new WedofBadRequestHttpException("Erreur, la durée de formation 'indicativeDuration' doit être renseignée pour valider un dossier Pole Emploi.");
        }
        $data['weeklyDuration'] = $data['weeklyDuration'] ?? ($trainingActionInfo['weeklyDuration'] ?? null);
        $sessionStartDate = DateTime::createFromFormat('d/m/Y', $trainingActionInfo['sessionStartDate']) ?: new DateTime($trainingActionInfo['sessionStartDate']);
        if (empty($data['weeklyDuration']) && !empty($data['indicativeDuration'])) {
            $sessionEndDate = DateTime::createFromFormat('d/m/Y', $trainingActionInfo['sessionEndDate']) ?: new DateTime($trainingActionInfo['sessionEndDate']);
            $weeks = ceil($sessionEndDate->diff($sessionStartDate)->days / 7);
            $data['weeklyDuration'] = ceil($data['indicativeDuration'] / ($weeks > 0 ? $weeks : 1));
        }
        if ($sessionStartDate && $sessionStartDate < $this->configService->getConfig()->getCpfSessionMinDate()) {
            throw new WedofBadRequestHttpException("Erreur, la date de début de la formation ne respecte pas les 11 jours ouvrés de délai de rétractation.");
        }
        if ($data['weeklyDuration'] != $trainingActionInfo['indicativeDuration'] ||
            $data['weeklyDuration'] != $trainingActionInfo['weeklyDuration'] ||
            $data['indicativeDuration'] != $trainingActionInfo['indicativeDuration']) {
            $data['trainingActionInfo'] = $data['trainingActionInfo'] ?? [];
            $data['trainingActionInfo']['indicativeDuration'] = $data['indicativeDuration'] ?? $trainingActionInfo['indicativeDuration'];
            unset($data['indicativeDuration']);
            $data['trainingActionInfo']['weeklyDuration'] = $data['weeklyDuration'] ?? $trainingActionInfo['weeklyDuration'];
            unset($data['weeklyDuration']);
            $registrationFolder = $this->registrationFolderService->update($registrationFolder, $data);
        }
        $parameters = [
            "state" => RegistrationFolderStates::UPDATE_TO_VALIDATED()
        ];
        return $this->changeFromStateToState($registrationFolder, RegistrationFolderStates::UPDATE_TO_VALIDATED(), $parameters);
    }


    /**
     * @param RegistrationFolder $registrationFolder
     * @return RegistrationFolder
     */
    public function acceptRegistrationFolder(RegistrationFolder $registrationFolder): RegistrationFolder
    {
        throw new WedofBadRequestHttpException("Erreur, un dossier CPF ne peut pas être accepté.");
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @return RegistrationFolder
     */
    public function rejectRegistrationFolder(RegistrationFolder $registrationFolder): RegistrationFolder
    {
        throw new WedofBadRequestHttpException("Erreur, un dossier CPF ne peut pas être rejeté.");
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @return RegistrationFolder
     */
    public function attendeeRefuseRegistrationFolder(RegistrationFolder $registrationFolder): RegistrationFolder
    {
        throw new WedofBadRequestHttpException("Erreur, un dossier CPF ne peut pas être refusé par un apprenant.");
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @return RegistrationFolder
     */
    public function attendeeCancelRegistrationFolder(RegistrationFolder $registrationFolder): RegistrationFolder
    {
        throw new WedofBadRequestHttpException("Erreur, un dossier CPF ne peut pas être refusé par un apprenant.");
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @return RegistrationFolder
     */
    public function waitRegistrationFolder(RegistrationFolder $registrationFolder): RegistrationFolder
    {
        throw new WedofBadRequestHttpException("Erreur, un dossier CPF ne peut pas être mis en attente.");
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @return RegistrationFolder
     */
    public function paidRegistrationFolder(RegistrationFolder $registrationFolder): RegistrationFolder
    {
        throw new WedofBadRequestHttpException("Erreur, un dossier CPF ne peut pas être marquer comme payé.");
    }

    /**
     * @param Session $session
     * @param array $rawData
     * @param Organism $organism
     * @return array
     */
    public function preCreateRegistrationFolder(Session $session, array $rawData, Organism $organism): array
    {
        return $rawData;
    }

    /**
     * @param array $rawData
     * @param DataProviders $dataProvider
     * @return string
     * @throws WedofCpfBackendException
     */
    public function generateRegistrationFolderId(array $rawData, DataProviders $dataProvider): string
    {
        if (empty($rawData['id'])) {
            throw new WedofCpfBackendException('Error CPF no externalId in rawData ' . print_r($rawData, true));
        }
        return $rawData['id'];
    }

    /**
     * Entrée en formation
     * @param RegistrationFolder $registrationFolder
     * @param DateTime $inTrainingDate
     * @param User|null $user
     * @return RegistrationFolder
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function inTrainingRegistrationFolder(RegistrationFolder $registrationFolder, DateTime $inTrainingDate, User $user = null): RegistrationFolder
    {
        $parameters = [
            'state' => RegistrationFolderStates::UPDATE_TO_IN_TRAINING(),
            'date' => $inTrainingDate->format('d/m/Y')
        ];
        return $this->changeFromStateToState($registrationFolder, RegistrationFolderStates::UPDATE_TO_IN_TRAINING(), $parameters);
    }

    /**
     * Sortie de formation
     * @param RegistrationFolder $registrationFolder
     * @param DateTime $terminatedDate
     * @param User|null $user
     * @return RegistrationFolder
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function terminateRegistrationFolder(RegistrationFolder $registrationFolder, DateTime $terminatedDate, User $user = null): RegistrationFolder
    {
        $rawData = $registrationFolder->getRawData();

        array_unshift($rawData['history'], [
            "date" => (new DateTime('now'))->format('Y-m-d\TH:i:s.v\Z'),
            "label" => "Déclaration Sortie de formation",
            "author" => "Organisme"
        ]);
        $rawData['states'][RegistrationFolderStates::TERMINATED()->getValue()] = ['date' => $terminatedDate->format('Y-m-d')];
        $rawData['currentState'] = RegistrationFolderStates::TERMINATED()->getValue();

        return $this->registrationFolderService->createOrUpdate($rawData, DataProviders::from($registrationFolder->getType()), $registrationFolder->getExternalId());
    }

    /**
     * Declare service done
     * @param RegistrationFolder $registrationFolder
     * @param float $absenceDuration
     * @param int $completionRate
     * @param bool $forceMajeureAbsence
     * @param RegistrationFolderReason $reason
     * @param User|null $user
     * @return RegistrationFolder
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function declareServiceDoneRegistrationFolder(RegistrationFolder $registrationFolder, float $absenceDuration, int $completionRate, bool $forceMajeureAbsence, RegistrationFolderReason $reason, User $user = null): RegistrationFolder
    {
        $parameters = [
            "state" => RegistrationFolderStates::UPDATE_TO_TERMINATED(),
            "completed" => !($absenceDuration),
            "forceMajeureAbsence" => $forceMajeureAbsence,
            "absenceDuration" => $absenceDuration,
            "absenceUnit" => "1", // [{"code":"1","label":"Heure(s)","obsolescence":null},{"code":"2","label":"Demi-journée(s)","obsolescence":null},{"code":"3","label":"Journée(s)","obsolescence":null}]
            "achievementRate" => $completionRate,
            'terminatedReason' => $reason->getCode(),
            'terminatedDate' => (new DateTime($registrationFolder->getRawData()['states']['terminated']['date']))->format('d/m/Y')
        ];
        return $this->changeFromStateToState($registrationFolder, RegistrationFolderStates::UPDATE_TO_SERVICE_DONE_DECLARED(), $parameters);
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @param RegistrationFolderReason $reason
     * @param string $description
     * @return RegistrationFolder
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function refuseRegistrationFolder(RegistrationFolder $registrationFolder, RegistrationFolderReason $reason, string $description): RegistrationFolder
    {
        $parameters = [
            'state' => RegistrationFolderStates::UPDATE_TO_REFUSED(),
            'description' => $description,
            'reason' => [
                "code" => $reason->getCode(),
                "label" => $reason->getLabel()
            ]
        ];
        return $this->changeFromStateToState($registrationFolder, RegistrationFolderStates::REFUSED_BY_ORGANISM(), $parameters);
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @param RegistrationFolderReason $reason
     * @param string $description
     * @return RegistrationFolder
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function cancelRegistrationFolder(RegistrationFolder $registrationFolder, RegistrationFolderReason $reason, string $description): RegistrationFolder
    {
        $parameters = [
            'state' => RegistrationFolderStates::UPDATE_TO_CANCELED(),
            'description' => $description,
            'reason' => [
                "code" => $reason->getCode(),
                "label" => $reason->getLabel()
            ]
        ];
        if ($reason->getCode() == 4) { //non présentation du participant traitement spécifique
            $parameters['description'] = ""; //normalement pas de possibilité de mettre de commentaire
            return $this->changeFromStateToState($registrationFolder, RegistrationFolderStates::CANCELED_BY_ATTENDEE_NOT_REALIZED(), $parameters);
        } else {
            return $this->changeFromStateToState($registrationFolder, RegistrationFolderStates::CANCELED_BY_ORGANISM(), $parameters);
        }
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @param string $oFBillNumber
     * @param float|null $vatRate
     * @return RegistrationFolder
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function billRegistrationFolder(RegistrationFolder $registrationFolder, string $oFBillNumber, float $vatRate = null): RegistrationFolder
    {
        $organism = $registrationFolder->getOrganism();
        $connection = $organism->getConnectionForDataProvider($this->dataProvider);
        if (!$connection) {
            throw new WedofConnectionException("Aucune connection du type " . $this->dataProvider->getValue() . " existant pour l'organisme " . $organism->getSiret());
        }
        $vatRate ??= $organism->getVat();
        $registrationFolderRawData = $registrationFolder->getRawData();
        $ofRawData = $this->getUserInfos($connection);
        $organismRawData = [];
        foreach ($ofRawData['company']['organisms'] as $organismData) {
            if ($organismData['corporateName'] == $registrationFolderRawData['trainingActionInfo']['companyName']) {
                $organismRawData = $organismData;
                break;
            }
        }
        $CPF_Request_URL = Tools::getEnvValue("CPF_API_BASE_URI") . "/private/siret/" . $organism->getSiret() . "/organisms/current/bills/" . $registrationFolder->getBillId() . "/validate";
        $this->logger->info($registrationFolderRawData['id'] . " start billRegistrationFolder");
        $this->logger->info($registrationFolderRawData['id'] . " associate with $oFBillNumber");

        $parameters = [];
        //price computation
        if ($vatRate === 0.0 || ($vatRate === null && empty($registrationFolder->getVatAmount5()) && empty($registrationFolder->getVatAmount20()))) { //no tva
            $priceDeterminated = true;
            $parameters["amountHtNet"] = $registrationFolder->getAmountCGU();
        } else if ($vatRate === 5.5 || ($vatRate === null && !empty($registrationFolder->getVatAmount5()) && empty($registrationFolder->getVatAmount20()))) { //tva 5.5
            $priceDeterminated = true;
            $parameters["vatHtAmount5"] = round($registrationFolder->getAmountCGU() / 1.055, 2);
            $parameters["vatAmount5"] = $registrationFolder->getAmountCGU() - $parameters["vatHtAmount5"];
        } else if ($vatRate === 20.0 || ($vatRate === null && empty($registrationFolder->getVatAmount5()) && !empty($registrationFolder->getVatAmount20()))) { //tva 20
            $priceDeterminated = true;
            $parameters["vatHtAmount20"] = round($registrationFolder->getAmountCGU() / 1.2, 2);
            $parameters["vatAmount20"] = $registrationFolder->getAmountCGU() - $parameters["vatHtAmount20"];
        } else {
            $priceDeterminated = false; //no match don't guess
        }

        if (!$priceDeterminated) {
            throw new WedofBadRequestHttpException("Erreur, le prix ne correspond pas.");
        }

        $parameters = array_merge([
            "comment" => null,
            "amountHtNet" => 0,
            "vatHtAmount5" => 0,
            "vatAmount5" => 0,
            "vatHtAmount20" => 0,
            "vatAmount20" => 0,
            "billNumber" => $oFBillNumber,
            "corporateName" => $organismRawData['corporateName'],
            "nameAttendee" => ucfirst(strtolower($registrationFolderRawData['attendee']['firstName'])) . " " . $registrationFolderRawData['attendee']['lastName'],
            "trainingActionInfo" => $registrationFolderRawData['trainingActionInfo']['title'],
            "sessionStartDate" => $registrationFolderRawData['trainingActionInfo']['sessionStartDate'],
            "sessionEndDate" => $registrationFolderRawData['trainingActionInfo']['sessionEndDate'],
            "trainingCompletionRate" => (string)$registrationFolderRawData['trainingActionInfo']['trainingCompletionRate'],
            "orderNumber" => $registrationFolderRawData['id'],
            "beginDate" => $registrationFolderRawData['beginDate'],
            "endDate" => $registrationFolderRawData['endDate'],
            "siret" => $organismRawData['siret'],
            "id" => $registrationFolder->getBillId(),
            "dateInvoice" => null,
            "amountTtc" => $registrationFolder->getAmountTtc(),
            "amountCGU" => $registrationFolder->getAmountCGU(),
            "totalAmountExTax" => $registrationFolder->getAmountHt(),
            "amountDepositReceived" => null,
            "datepayment" => null,
            "organismeAddress" => $organismRawData['address']['fullAddress'],
            "amountToInvoice" => $registrationFolder->getAmountToInvoice(),
            "billAddress" => $organismRawData['address']
        ], $parameters);

        $options = [
            "method" => "POST",
            "parameters" => $parameters,
            "organism" => $organism,
            'cat_api_status' => "private-registrationFolderUpdate-billing"
        ];

        $this->sendRequest($CPF_Request_URL, $options);
        if ($this->getRegistrationFolderBillingRawData($organism, $registrationFolderRawData['idSuiviFinancement'])['billNumber'] != $oFBillNumber) {
            throw new WedofBadRequestHttpException("[" . $registrationFolder->getExternalId() . "][billing] Le dossier de formation ne peut pas être facturé.");
        } else {
            $registrationFolder->getHistory()->setBilledDate(new DateTime());
            return $this->refreshRegistrationFolder($this->registrationFolderService->save($registrationFolder));
        }
    }


    /**
     * @param Organism $organism
     * @param string $idSuiviFinancement
     * @return array|null
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function getRegistrationFolderBillingRawData(Organism $organism, string $idSuiviFinancement): ?array
    {
        $CPF_Request_URL = Tools::getEnvValue("CPF_API_BASE_URI") . "/private/siret/" . $organism->getSiret() . "/organisms/current/bills?idSuiviFinancement=" . $this->cleanId($idSuiviFinancement);
        $options = [
            "organism" => $organism,
            'cat_api_status' => "private-registrationFolder-billing"
        ];
        return $this->sendRequest($CPF_Request_URL, $options);
    }

    /**
     * @param TrainingAction $trainingAction
     * @return array|null
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    private function getPublicSessionsRawData(TrainingAction $trainingAction): ?array
    {
        $options = [
            "cat_api_status" => "public-session",
            "maxRetry" => Tools::getEnvValue('CPF_API_MAX_RETRY'),
            "publicFakingData" => true
        ];
        $sessionsRawData = $this->sendRequest(Tools::getEnvValue('CPF_ACTION_FORMATION_API_PUBLIC_URI') . "/public/formations/" . $trainingAction->getTraining()->getExternalId() . "/actions/" . explode("/", $trainingAction->getExternalId())[1] . "/sessions", $options);
        return $this->prepareSessionRawDataFromPublicData($sessionsRawData);
    }

    /**
     * @param Training $training
     * @param string $trainingActionId
     * @return array
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    private function getPublicTrainingActionRawData(Training $training, string $trainingActionId): ?array
    {
        $CPF_Request_URL = Tools::getEnvValue("CPF_ACTION_FORMATION_API_PUBLIC_URI") . "/public/formations/" . $this->cleanId($training->getExternalId()) . "/actions/" . $this->cleanId($trainingActionId);
        $options = [
            "cat_api_status" => "public-trainingAction",
            "throw_404_exception" => false
        ];
        $trainingActionRawData = $this->sendRequest($CPF_Request_URL, $options);
        if (isset($trainingActionRawData) && !isset($trainingActionRawData['functionnalKey'])) {
            $trainingActionRawData['id'] = $trainingActionRawData['numeroAction'];
            $trainingActionRawData['trainingId'] = $trainingActionRawData['formation']['numeroFormation'];
            $trainingActionRawData['externalId'] = $this->getTrainingActionExternalId($trainingActionRawData['trainingId'], $trainingActionRawData['id']);
            $trainingActionRawData['totalTvaTTC'] = $trainingActionRawData['totalPriceTTC'];
            $trainingActionRawData['averageLearningTime'] = $trainingActionRawData['duration'];
            $trainingActionRawData['statusCode'] = '0';
            $trainingActionRawData['statusLabel'] = 'ACTIVE';
            return $trainingActionRawData;
        } else {
            return null;
        }
    }

    /**
     * @param string $trainingId
     * @param string $trainingActionId
     * @return array|null
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function getPublicTrainingRawData(string $trainingId, string $trainingActionId): ?array
    {
        $CPF_Request_URL = Tools::getEnvValue("CPF_ACTION_FORMATION_API_PUBLIC_URI") . "/public/formations/" . $this->cleanId($trainingId) . "/actions/" . $this->cleanId($trainingActionId);

        $options = ['cat_api_status' => "public-training", "throw_404_exception" => false];
        $data = $this->sendRequest($CPF_Request_URL, $options);
        if (isset($data['formation'])) {
            $trainingRawData = $data['formation'];
            $trainingRawData['organism'] = $this->getOrganismRawDataFromTrainingData($data);
            $trainingRawData['certifInfo'] = $trainingRawData['codeDiplomeCertification'] ?? null;
            $trainingRawData['externalId'] = $trainingRawData['id'] = $trainingRawData['numeroFormation'] ?? null;
            $trainingRawData['statusCode'] = '0';
            $trainingRawData['statusLabel'] = 'ACTIVE';
            $trainingRawData['code'] = !empty($trainingRawData['codeRncp']) ? $trainingRawData['codeRncp'] : $trainingRawData['codeInventaire'];
            $trainingRawData['type'] = !empty($trainingRawData['codeRncp']) ? 'RCNP' : 'RS';
            $trainingRawData['dataProvider'] = DataProviders::CPF()->getValue();
            return $trainingRawData;
        } else {
            return null;
        }
    }

    /**
     * @param TrainingAction $trainingAction
     * @return array|null
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    private function getPrivateSessionsRawData(TrainingAction $trainingAction): ?array
    {
        $options = [
            'cat_api_status' => "private-sessions",
            'organism' => $trainingAction->getTraining()->getOrganism()
        ];
        $sessionsRawData = $this->sendRequest(Tools::getEnvValue("CPF_API_BASE_URI") . "/private/siret/" . $trainingAction->getTraining()->getOrganism()->getSiret() . "/trainings/" . $this->getShortTrainingExternalIdFromTrainingAction($trainingAction) . "/actions/" . $this->getShortTrainingActionExternalIdFromTrainingAction($trainingAction) . "/sessions", $options);
        return $this->prepareSessionsRawDataFromPrivateData($sessionsRawData);
    }

    /**
     * @param TrainingAction $trainingAction
     * @param string $sessionId
     * @return array|null
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function getPrivateSessionRawData(TrainingAction $trainingAction, string $sessionId): ?array
    {
        $CPF_Request_URL = Tools::getEnvValue("CPF_API_BASE_URI") . "/private/siret/" . $trainingAction->getTraining()->getOrganism()->getSiret() . "/trainings/" . $this->cleanId($this->getShortTrainingExternalIdFromTrainingAction($trainingAction)) . "/actions/" . $this->cleanId($this->getShortTrainingActionExternalIdFromTrainingAction($trainingAction)) . "/sessions/" . $this->cleanId($this->getShortExternalId($sessionId));
        $options = [
            'organism' => $trainingAction->getTraining()->getOrganism(),
            'cat_api_status' => "private-session"
        ];
        $rawData = $this->sendRequest($CPF_Request_URL, $options);
        return isset($rawData['trainingId']) ? $this->prepareSessionRawDataFromPrivateData($rawData) : null;
    }

    /**
     * @param Training $training
     * @param string $trainingActionId (without siret)
     * @return null|array
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    private function getPrivateTrainingActionRawData(Training $training, string $trainingActionId): ?array
    {
        $CPF_Request_URL = Tools::getEnvValue("CPF_API_BASE_URI") . "/private/siret/" . $training->getOrganism()->getSiret() . "/trainings/" . $this->cleanId($this->getTrainingShortExternalIdFromTraining($training)) . "/actions/" . $this->cleanId($trainingActionId);
        $options = [
            'cat_api_status' => "private-trainingAction",
            'organism' => $training->getOrganism(),
            "throw_404_exception" => false
        ];
        $data = $this->sendRequest($CPF_Request_URL, $options);
        if ($data && !isset($data['response']) && !isset($data['functionnalKey'])) {
            $data['externalId'] = $this->getTrainingActionExternalId($data['trainingId'], $data['id']);
        } else {
            $data = null;
        }
        return $data;
    }

    /**
     * @param Training $training
     * @return array
     * @throws Throwable
     */
    private function getPrivateTrainingActionsRawData(Training $training): array
    {
        $CPF_Request_URL = Tools::getEnvValue("CPF_API_BASE_URI") . "/private/siret/" . $training->getOrganism()->getSiret() . "/trainings/" . $this->cleanId($this->getTrainingShortExternalIdFromTraining($training)) . "/actions";
        $options = [
            'cat_api_status' => "private-trainingActions",
            'organism' => $training->getOrganism(),
            "throw_404_exception" => false
        ];
        try {
            $trainingActionsRawData = $this->sendRequest($CPF_Request_URL, $options);
        } catch (Throwable $exception) {
            $trainingActionsRawData = [];
        } finally {
            return $this->prepareTrainingActionsRawDataFromPrivateData($trainingActionsRawData);
        }
    }

    /**
     * @param Organism $organism
     * @param array|null $trainingStatuses
     * @return array
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    private function getPrivateTrainingsRawData(Organism $organism, array $trainingStatuses = null): array
    {
        //todo custom the status
        $trainingsRawData = [];
        $trainingStatuses = $trainingStatuses ?? [0, /*8,*/
            7, 9, 3];
        /*
         * 0 => active
         * 8 => deleted
         * 7 => validated
         * 9 => draft
         * 3 => archived
         */
        foreach ($trainingStatuses as $status) {
            $items = 20;
            $i = 1;
            do {
                $CPF_Request_URL = Tools::getEnvValue("CPF_API_BASE_URI") . "/private/siret/" . $organism->getSiret() . "/organisms/current/trainings";
                $options = [
                    'cat_api_status' => "private-training",
                    'organism' => $organism,
                    'parameters' => [
                        "firstElementIndex" => $i,
                        "numberOfElements" => $items,
                        "trainingStatusCode" => $status
                    ]
                ];
                $data = $this->sendRequest($CPF_Request_URL, $options);
                if ($data['trainings']) {
                    $trainingsRawData = array_merge($trainingsRawData, $data['trainings']);
                }
                $i += $items;
            } while ($i < $data['totalTrainingsCount']);
        }
        return $trainingsRawData;
    }

    /**
     * @param Organism $organism
     * @param string $trainingId (without siret)
     * @return null|array
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function getPrivateTrainingRawData(Organism $organism, string $trainingId): ?array
    {
        $CPF_Request_URL = Tools::getEnvValue("CPF_API_BASE_URI") . "/private/siret/" . $organism->getSiret() . "/trainings/" . $this->cleanId($trainingId);

        $options = [
            'organism' => $organism,
            'cat_api_status' => "private-training",
            "throw_404_exception" => false
        ];
        $data = $this->sendRequest($CPF_Request_URL, $options);
        $data = ($data && !isset($data['functionnalKey'])) ? (isset($data['response']) ? null : $data) : null;
        if ($data) {
            $data['title'] = $data['title'] ?? ""; //sometimes title is empty on EDOF...
            $data['dataProvider'] = DataProviders::CPF()->getValue();
            $data['externalId'] = $data['id'];
            $data['certifInfo'] = null;
            $data['type'] = $data['codeReferentiel'];
            $data['code'] = substr($data['codeReconnaissance'], strlen($data['type']));
        }

        return $data;
    }

    /**
     * @param Organism $organism
     * @param string $trainingId
     * @param array $options
     * @return array|null
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     * @throws \Symfony\Component\Mailer\Exception\TransportExceptionInterface
     */
    public function getTrainingRawData(Organism $organism, string $trainingId, array $options = []): ?array
    {
        $trainingRawData = null;
        if ($this->connectionService->hasAccess($organism, $this->dataProvider)) {
            $trainingRawData = $this->getPrivateTrainingRawData($organism, $this->getShortExternalId($trainingId));
        }
        if (!$trainingRawData && isset($options['trainingActionId'])) {
            $trainingRawData = $this->getPublicTrainingRawData($trainingId, $options['trainingActionId']);
        }
        return $trainingRawData;
    }

    /**
     * @param Organism $organism
     * @param array|null $trainingStatuses
     * @return array|null
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     * @throws \Symfony\Component\Mailer\Exception\TransportExceptionInterface
     */
    public function getTrainingsRawData(Organism $organism, array $trainingStatuses = null): ?array
    {
        $rawData = null;
        if ($this->connectionService->hasAccess($organism, $this->dataProvider)) {
            $rawData = $this->getPrivateTrainingsRawData($organism, $trainingStatuses);
            foreach ($rawData as &$data) {
                $data['title'] = $data['title'] ?? ""; //sometimes title is empty on EDOF...
                $data['dataProvider'] = DataProviders::CPF()->getValue();
                $data['externalId'] = $data['id'];
                $data['certifInfo'] = null;
                $data['type'] = $data['codeReferentiel'];
                $data['code'] = substr($data['codeReconnaissance'], strlen($data['type']));
            }
        }
        return $rawData;
    }

    /**
     * @param Training $training
     * @param string $trainingActionId
     * @return array|null
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     * @throws \Symfony\Component\Mailer\Exception\TransportExceptionInterface
     */
    public function getTrainingActionRawData(Training $training, string $trainingActionId): ?array
    {
        $trainingActionRawData = null;
        if ($this->connectionService->hasAccess($training->getOrganism(), $this->dataProvider)) {
            $trainingActionRawData = $this->getPrivateTrainingActionRawData($training, $this->getShortExternalId($trainingActionId));
        }
        if (empty($trainingActionRawData['trainingId'])) {
            $trainingActionRawData = $this->getPublicTrainingActionRawData($training, $trainingActionId);
        }
        return $trainingActionRawData;
    }

    /**
     * @param Training $training
     * @return array|null
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function getTrainingActionsRawData(Training $training): ?array
    {
        return $this->getPrivateTrainingActionsRawData($training);
    }

    /**
     * @param TrainingAction $trainingAction
     * @return array|null
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function getSessionsRawData(TrainingAction $trainingAction): ?array
    {
        if ($this->connectionService->hasAccess($trainingAction->getTraining()->getOrganism(), $this->dataProvider) && $trainingAction->getRawData()['statusLabel'] != "DELETED") {
            $sessionsRawData = $this->getPrivateSessionsRawData($trainingAction);
        } else {
            $sessionsRawData = $this->getPublicSessionsRawData($trainingAction);
        }
        return $sessionsRawData;
    }

    /**
     * @param TrainingAction $trainingAction
     * @param string $sessionId
     * @return array|null
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function getSessionRawData(TrainingAction $trainingAction, string $sessionId): ?array
    {
        return $this->getPrivateSessionRawData($trainingAction, $sessionId);
    }

    /**
     * @param TrainingAction $trainingAction
     * @return void
     * @throws Throwable
     */
    public function updateEvaluationsForTrainingAction(TrainingAction $trainingAction): void
    {
        $rawData = $trainingAction->getRawData();
        if (isset($rawData['moyennesNotationParQuestion']) && $rawData['nombreEvaluations'] != null) {
            /** @var EvaluationService $evaluationService */
            $evaluationService = $this->container->get(EvaluationService::class);
            $evaluationService->create(
                ["NB_AVIS_ACTION" => $rawData['nombreEvaluations'],
                    "NOTE_Q1" => $rawData['moyennesNotationParQuestion'][0]['evaluation'],
                    "NOTE_Q2" => $rawData['moyennesNotationParQuestion'][1]['evaluation'],
                    "NOTE_Q3" => $rawData['moyennesNotationParQuestion'][2]['evaluation'],
                    "NOTE_Q4" => $rawData['moyennesNotationParQuestion'][3]['evaluation'],
                    "NOTE_Q5" => $rawData['moyennesNotationParQuestion'][4]['evaluation'],
                    "NOTE_MOYENNE_ACTION" => $rawData['moyennesNotationParQuestion'][5]['evaluation'],
                ], $trainingAction);
        }
    }

    /**
     * @param Organism $organism
     * @param PaymentStates|null $state
     * @param Payment|null $lastPaymentRef
     * @param PaymentSortParams|null $sort
     * @param int $page
     * @param int $occurrences
     * @param RegistrationFolder|null $registrationFolder
     * @return array
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function getPaymentsRawData(Organism $organism, PaymentStates $state = null, Payment $lastPaymentRef = null, PaymentSortParams $sort = null, int $page = 1, int $occurrences = 50, ?RegistrationFolder $registrationFolder = null): array
    {
        $state = $state ?: PaymentStates::ALL();
        switch ($state) {
            case PaymentStates::REJECTED():
                $filter = 'rejected';
                break;
            case PaymentStates::WAITING():
                $filter = 'waiting';
                break;
            case PaymentStates::ISSUED():
                $filter = 'versed';
                break;
            default:
            case PaymentStates::ALL():
                $filter = '';
                break;
        }
        $params = [
            'filter' => $filter,
            'max' => $occurrences,
            'page' => ($page - 1),
            'order' => 'DESC',
            'sort' => $sort !== null ? $sort->getValue() : PaymentSortParams::BILLING_DATE()->getValue()
        ];
        $CPF_Request_URL = Tools::getEnvValue("CPF_API_BASE_URI") . "/private/siret/" . $organism->getSiret() . "/organisms/current/factures?sort=" . $params['sort'] . "&filter=" . $params['filter'] . "&max=" . $params['max'] . "&page=" . $params['page'] . "&order=" . $params['order'];
        if ($registrationFolder) {
            $CPF_Request_URL .= '&dossier=' . $registrationFolder->getExternalId();
        }
        $options = [
            'organism' => $organism,
            'cat_api_status' => "private-payments"
        ];
        $data = $this->sendRequest($CPF_Request_URL, $options);
        $payments = $data['items'];
        $grabMore = true;
        //migrate data format && limit grab
        foreach ($payments as &$payment) {
            $payment['type'] = $payment['typeFacture'] === 'FACTURE' ? PaymentTypes::BILL()->getValue() : PaymentTypes::DEPOSIT()->getValue();
            $payment['status'] = $payment['status'] === 'EN_ATTENTE' ? PaymentStates::WAITING()->getValue() : ($payment['status'] === 'VERSE' ? PaymentStates::ISSUED()->getValue() : PaymentStates::REJECTED()->getValue());
            $payment['amount'] = $payment['montant'] ?? 0.0;
            $payment['billNumber'] = $payment['numeroFacture'];
            $payment['registrationFolderId'] = $payment['numeroDossier'];
            $payment['scheduledDate'] = $payment['echance'] ? str_replace('/', '-', $payment['echance']) : null; //TODO fix typo when they fixed the typo....
            $payment['billingDate'] = str_replace('/', '-', $payment['emission']);
            unset($payment['typeFacture']);
            unset($payment['montant']);
            unset($payment['numeroFacture']);
            unset($payment['numeroDossier']);
            unset($payment['echance']);
            unset($payment['emission']);
            if ($state !== PaymentStates::ALL() && $lastPaymentRef) {
                if ($payment['registrationFolderId'] === $lastPaymentRef->getRegistrationFolder()->getExternalId()) { // IF several payments on same RF, is there an issue?
                    $grabMore = false;
                    break;
                }
            }
        }
        $payments = array_filter($payments, function ($payment) {
            return !empty($payment['registrationFolderId']);
        });
        if ($grabMore && ($data['numberOfItems'] > $occurrences * $page) && sizeof($payments) > 0 && !$registrationFolder) {
            $page = $page + 1;
            $this->logger->debug("Grabbing next $occurrences (page $page) of " . $data['numberOfItems'] . " " . $state->getValue() . ($lastPaymentRef ? " until " . $lastPaymentRef->getId() : ''));
            $payments = array_merge($payments, $this->getPaymentsRawData($organism, $state, $lastPaymentRef, $sort, $page, $occurrences));
        }
        return array_unique($payments, SORT_REGULAR);
    }

    /**
     * @param Organism $organism
     * @return array
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException|Throwable
     */
    public function getEvaluationsRawData(Organism $organism): array
    {
        $options = [
            'method' => 'GET',
            'organism' => $organism
        ];
        $data = $this->sendRequestForFile(Tools::getEnvValue('CPF_API_BASE_URI') . "/private/siret/" . $organism->getSiret() . "/rating-csv", $options);
        return Tools::csvToArray($data);
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @return RegistrationFolder
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function refreshRegistrationFolder(RegistrationFolder $registrationFolder): RegistrationFolder
    {
        return $this->getRegistrationFolderByExternalId($registrationFolder->getExternalId(), $registrationFolder->getOrganism(), ['refresh' => true]);
    }

    /**
     * @param string $externalId
     * @param Organism $organism
     * @return RegistrationFolder
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function retrieveRegistrationFolder(string $externalId, Organism $organism): RegistrationFolder
    {
        return $this->getRegistrationFolderByExternalId($externalId, $organism);
    }

    /**
     * @param string $trainingActionExternalId
     * @return DateTime
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function getPoleEmploiSessionMinDate(string $trainingActionExternalId): DateTime
    {
        $options = [
            "cat_api_status" => "public-trainingAction",
            "maxRetry" => Tools::getEnvValue('CPF_API_MAX_RETRY'),
            "publicFakingData" => true
        ];
        $CPF_Request_URL = Tools::getEnvValue("CPF_ACTION_FORMATION_API_PUBLIC_URI") . "/public/formations/" . preg_replace('/.*\//', '\0\1actions/\2', $trainingActionExternalId);
        $trainingActionRawData = $this->sendRequest($CPF_Request_URL, $options);

        return new DateTime($trainingActionRawData['minDateSessionPoleEmploi']);
    }

    /**
     * @param Connection $connection
     * @return string
     */
    public function getUsername(Connection $connection): string
    {
        return '';
    }

    /**
     * @param Organism $organism
     * @param array|null $params
     * @return array
     */
    public function checkBeforeHabilitate(Organism $organism, array $params = null): array
    {
        throw new WedofBadRequestHttpException(json_encode(['error' => 'efp']), null, 400);
    }

    /**
     * @param Organism $organism
     * @param Connection $connection
     * @param array $params
     * @return bool
     */
    public function habilitate(Organism $organism, Connection $connection, array $params): bool
    {
        return false;
    }

    /**
     * @param string $trainingId
     * @return string
     */
    public function getTrainingExternalId(string $trainingId): string
    {
        return $trainingId;
    }

    /**
     * @param string $trainingId
     * @param string $trainingActionId
     * @return string
     */
    public function getTrainingActionExternalId(string $trainingId, string $trainingActionId): string
    {
        return $trainingId . "/" . $trainingActionId;
    }

    /**
     * @param TrainingAction $trainingAction
     * @return string
     */
    public function getTrainingActionExternalLink(TrainingAction $trainingAction): string
    {
        return 'https://www.moncompteformation.gouv.fr/espace-prive/html/#/formation/recherche/' . $trainingAction->getExternalId();
    }

    /**
     * @param string $trainingId
     * @param string $trainingActionId
     * @param string $sessionId
     * @return string
     */
    public function getSessionExternalId(string $trainingId, string $trainingActionId, string $sessionId): string
    {
        return $trainingId . "/" . $trainingActionId . "/" . $sessionId;
    }

    /**
     * @param array $rawData
     * @param Certification|null $certification
     * @return TrainingStates
     */
    public function getTrainingState(array $rawData, ?Certification $certification): TrainingStates
    {
        $cpfState = !empty($rawData['statusLabel']) ? $rawData['statusLabel'] : 'ACTIVE';

        switch ($cpfState) {
            case 'DRAFT':
            case 'VALIDATED':
                $state = TrainingStates::DRAFT();
                break;
            case 'ACTIVE':
                if ((isset($rawData['certificationDereferencee']) && $rawData['certificationDereferencee']) // certification déréférencée
                    || (isset($rawData['certificationQualiopi']) && !$rawData['certificationQualiopi']) // organisme pas qualiopi
                    || (isset($rawData['stateHabilitation']) && $rawData['stateHabilitation'] !== 'ACTIF') // organisme déshabilité par certificateur
                    || !$certification
                    || !$certification->getEnabled()
                ) {
                    $state = TrainingStates::UNPUBLISHED();
                } else {
                    $state = TrainingStates::PUBLISHED();
                }
                break;
            case 'ARCHIVED':
            case 'DELETED':
                $state = TrainingStates::from(strtolower($cpfState));
                break;
            default:
                throw new LogicException("État EDOF inconnu !");
        }
        return $state;
    }

    /**
     * @param array $rawData
     * @param Training $training
     * @return TrainingActionStates
     */
    public function getTrainingActionState(array $rawData, Training $training): TrainingActionStates
    {
        if (in_array($training->getState(), [TrainingStates::UNPUBLISHED()->getValue(), TrainingStates::ARCHIVED()->getValue(), TrainingStates::DELETED()->getValue()])) {
            $state = TrainingActionStates::from($training->getState());
        } else {
            $cpfState = !empty($rawData['statusLabel']) ? $rawData['statusLabel'] : 'ACTIVE';
            switch ($cpfState) {
                case 'DRAFT':
                case 'VALIDATED':
                    $state = TrainingActionStates::DRAFT();
                    break;
                case 'ACTIVE':
                    $state = TrainingActionStates::PUBLISHED();
                    break;
                case 'ARCHIVED':
                case 'DELETED':
                    $state = TrainingActionStates::from(strtolower($cpfState));
                    break;
                default:
                    throw new LogicException("État EDOF inconnu !");
            }
        }

        return $state;
    }

    /**
     * @param array $rawData
     * @param TrainingAction $trainingAction
     * @return SessionStates
     * @throws Exception
     */
    public function getSessionState(array $rawData, TrainingAction $trainingAction): SessionStates
    {
        if (in_array($trainingAction->getState(), [TrainingActionStates::UNPUBLISHED()->getValue(), TrainingActionStates::ARCHIVED()->getValue(), TrainingActionStates::DELETED()->getValue()])) {
            $state = SessionStates::from($trainingAction->getState());
        } else {
            $cpfState = !empty($rawData['statusLabel']) ? $rawData['statusLabel'] : 'ACTIVE';
            switch ($cpfState) {
                case 'DRAFT':
                case 'VALIDATED':
                    $state = SessionStates::DRAFT();
                    break;
                case 'ACTIVE':
                    $state = (isset($rawData['beginDate']) && Tools::createDateFromString($rawData['beginDate']) < new DateTime()) ? SessionStates::UNPUBLISHED() : SessionStates::PUBLISHED();
                    break;
                case 'ARCHIVED':
                case 'DELETED':
                    $state = SessionStates::from(strtolower($cpfState));
                    break;
                default:
                    throw new LogicException("État EDOF inconnu !");
            }
        }

        return $state;
    }

    /**
     * @return mixed|null
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    public function sendRequestAsAttendee($username, $password, $method, $url)
    {
        $login_url = 'https://www.moncompteformation.gouv.fr/idp/504f525441494c2d43504132/local/login/titulaire';
        $params = [
            'headers' => [
                'User-Agent' => "'Mozilla/5.0 (Windows; U; Windows NT 6.1; en-US)AppleWebKit/534.10 (KHTML, like Gecko) Chrome/8.0.552.224 Safari/534.10'"
            ]
        ];
        $credentials = array_merge($params, ['body' => [
            'j_username' => $username,
            'j_password' => $password
        ]]);
        $httpClient = new CurlHttpClient();
        $login = $httpClient->request('POST', $login_url, $credentials);

        if ($login->getStatusCode() == 200) {
            $data = array_merge($params, ['headers' => [
                'Cookie' => $login->getHeaders()['set-cookie']
            ]]);
            $response = $httpClient->request($method, "https://www.moncompteformation.gouv.fr/espace-prive/sl6-portail-web" . $url, $data);
            return $response->getStatusCode() === 200 ? strpos($response->getHeaders()['content-type'][0], 'application/json') !== false ? json_decode($response->getContent(), true) : $response->getContent() : null;
        } else {
            return null;
        }
    }


    /**
     * @param array $options
     * @return array
     * @throws Throwable
     */
    public function setAuthentication(array $options = []): array
    {
        if ($options['organism']) {
            /** @var Organism $organism */
            $organism = $options['organism'];
            $connection = $organism->getConnectionForDataProvider($this->dataProvider);
            if ($this->connectionService->hasAccess($organism, DataProviders::CPF())) {
                $credentials = $connection->getCredentials();
                $options['access_token'] = $credentials['access_token'];
            }
            $options['headers'] = array_merge($options['headers'], [
                'User-Agent' => "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36",
                'sec-ch-ua' => '"Not.A/Brand";v="8", "Chromium";v="114", "Google Chrome";v="114"',
                'sec-ch-ua-mobile' => "?0",
                'sec-ch-ua-platform' => "macOS",
                "Referer" => "https://of.moncompteformation.gouv.fr/espace-prive/html/",
                "Sec-Fetch-Dest" => "empty",
                "Sec-Fetch-Mode" => "cors",
                "Sec-Fetch-Site" => "same-origin"
            ]);
        }
        return $options;
    }

    /**
     * @return bool
     */
    public function requiresAuthentication(): bool
    {
        return true;
    }

    /**
     * @param Connection $connection
     * @param bool $checkOrganismAccess
     * @return array|false[]
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function authenticate(Connection $connection, bool $checkOrganismAccess = true): array
    {
        $oldCredentials = $connection->getCredentials();
        $config = $this->configService->getConfig();
        if (!$config->isMesDemarchesAuthEnabled() || !$config->isCpfApiEnabled()) {
            throw new WedofConnectionException("EDOF API is offline, please wait", 503);
        }
        $tokenData = false;
        //try refresh
        if (isset($oldCredentials['userInfos']) && $connection->getRefreshAt() > (new DateTime())->modify('+1500 second')) {
            try {
                $options = [
                    "method" => "POST",
                    "cat_api_status" => "refresh-auth",
                    "access_token" => $oldCredentials['access_token'],
                    'content-type' => 'application/x-www-form-urlencoded',
                    'dispatchMonitoringEvent' => false,
                    "parameters" => [
                        "grant_type" => "refresh_token",
                        "scope" => "openid profile email address phone offline_access portail",
                        "refresh_token" => $oldCredentials['refresh_token'],
                        "client_id" => Tools::getEnvValue('CPF_CLIENT_ID')
                    ]
                ];
                $this->logger->debug("[CPF Access Token][" . $connection->getId() . "] starting authenticate refresh");
                $tokenData = $this->sendRequest(Tools::getEnvValue('CPF_AUTH_REFRESH_URI'), $options);
                $tokenData = array_merge($oldCredentials, $tokenData);
            } catch (Exception $e) {
            }
        }
        //try auth
        if (!$tokenData || !$tokenData['access_token']) {
            if ($config->isMesDemarchesAuthFromWedofEnabled() || !empty($_SERVER['REFRESH_CONNECTION_CONTEXT'])) {
                $tokenData = $this->getTokenData($connection);
            } else {
                throw new WedofConnectionException("EDOF API is offline, please retry soon", 503);
            }
        }
        if (isset($tokenData['access_token'])) {
            $userInfos = $tokenData['userInfos'] ?? false;
            //must be an array
            $userInfos = is_object($userInfos) ? json_decode(json_encode($userInfos), true) : $userInfos;
            //missing contact
            if ($userInfos && $userInfos['status'] === 'CONTACT_OF_DECLARATION') {
                $tokenData['error'] = "not-ready";
            } //Need validate TOS
            else if ($userInfos && $userInfos['status'] === 'VALIDATE_TOS') {
                $connection->setIsTosValidated(false);
                $result = $this->validateTOS($connection, $tokenData['access_token']);
                $connection->setIsTosValidated(isset($result['response']));
            } //account not initialized
            else if ($userInfos && $userInfos['status'] === 'UNKNOWN_SIRET') {
                //bug on the very first time we are logged-in this may happen, retry and we will get the status
                $userInfos = $this->getUserInfosWithAccessToken($connection, $tokenData['access_token']);
                if ($userInfos['status']) {
                    $tokenData['error'] = "unknown-siret";
                }
            }
            //first connection
            if ($userInfos && $userInfos['status'] === 'PRIMO_CONNECT') {
                if ($userInfos['selectedSiret'] === $connection->getOrganism()->getSiret()) {
                    //on first time
                    $this->acceptPrimoConnect($connection, $tokenData['access_token']);
                    $connection->setIsTosValidated(true);
                } else {
                    $tokenData['error'] = "wrong-siret";
                }
            }
            //check habilitation type
            if ($userInfos) {
                $organism = $connection->getOrganism();
                $siret = $organism->getSiret();
                $subscription = $organism->getSubscription();
                if (empty($userInfos['habilitationSet'][$siret])) {
                    //TODO remove this on friday 16/05
                    $userInfos['habilitationSet'][$siret] = [self::HABILITATION_EDOF_GESTIONNAIRE];
                }
                $habilitationEdofLevels = $userInfos['habilitationSet'][$siret];
                if (in_array($subscription->getTrainingType(), SubscriptionTrainingTypes::getPaidTrainingTypes()) && !in_array(self::HABILITATION_EDOF_GESTIONNAIRE, $habilitationEdofLevels)) {
                    $tokenData['error'] = "insufficient-habilitation-level";
                } else if (!in_array(self::HABILITATION_EDOF_GESTIONNAIRE, $habilitationEdofLevels) && !in_array(self::HABILITATION_EDOF_LECTEUR, $habilitationEdofLevels)) {
                    $tokenData['error'] = "wrong-habilitation-level";
                }
            } else {
                $tokenData['error'] = "no-habilitation-level";
            }
            $hasOrganismAccess = !isset($tokenData['error'])
                && (!$checkOrganismAccess || $this->checkOrganismAccess($connection->getOrganism(), $userInfos));
            if ($hasOrganismAccess) {
                $credentials = [
                    'type' => $oldCredentials['type'] ?? "delegation",
                    'username' => $oldCredentials['username'],
                    'password' => $oldCredentials['password'],
                    'access_token' => $tokenData['access_token'],
                    'refresh_token' => $tokenData['refresh_token'],
                    'id_token' => $tokenData['id_token'],
                    'tokenData' => $tokenData,
                    'habilitationLevel' => isset($userInfos['habilitationSet']) ? $userInfos['habilitationSet'][$connection->getOrganism()->getSiret()] : null,
                    'userInfos' => $userInfos ?? $oldCredentials['userInfos'] ?? []
                ];
                //new refresh_token/access_token at
                $expireOn = (new DateTime())->setTimestamp(strtotime('+' . $tokenData['expires_in'] . ' second'));
                //refresh_token expire at
                if (isset($tokenData['id_token'])) {
                    $token = (new Parser())->parse((string)$tokenData['id_token']);
                    $refreshAt = $token->claims()->get('exp');
                    $refreshAt->modify("-1200 second"); //better to authenticate before real expiration
                } else {
                    $refreshAt = $connection->getRefreshAt();
                }
                if ($expireOn > $refreshAt) { //can't be more than max refresh
                    $expireOn = $refreshAt;
                }
                $expireOn->modify("-60 second"); //better to refresh before real expiration
                $connection->setExpiresOn($expireOn);
                $connection->setRefreshAt($refreshAt);
                $connection->setCredentials($credentials);
                $connection->setIsTosValidated(true);
                $connection->setState(ConnectionStates::ACTIVE()->getValue());
                $this->connectionService->save($connection);
                $this->updateOrganismInfos($connection);
                //  $this->logger->debug("[CPF Access Token][" . $connection->getId() . "] authenticate success");
                return [
                    "hasAccess" => true,
                    "hasOrganismAccess" => true,
                    "expiresOn" => $expireOn,
                    "refreshAt" => $refreshAt,
                    "lastRefresh" => new DateTime()
                ];
            }
            return [
                "hasAccess" => true,
                "hasOrganismAccess" => false,
                "error" => $tokenData['error'] ?? null
            ];
        }

        if (!empty($tokenData['error'])) {
            $this->logger->info("[CPF Access Token][" . $connection->getId() . "] authenticate error: " . $tokenData['error']);
            return [
                "hasAccess" => false,
                "hasOrganismAccess" => false,
                "error" => $tokenData['error']
            ];
        } else {
            throw new WedofConnectionException("Une erreur est survenue pendant l'authentification");
        }
    }

    /**
     * @param Organism $organism
     * @param array $ofInformations
     * @return bool
     */
    public function checkOrganismAccess(Organism $organism, array $ofInformations): bool
    {
        $hasAccess = false;
        if (!empty($ofInformations['company'])) {
            foreach ($ofInformations['company']['organisms'] as $organismRawData) {
                if (!$organismRawData['unknownSiret']) { //exist but no edof account
                    $authorizedOrganism = $this->organismService->getBySiret($organismRawData['siret']);
                    if ($authorizedOrganism === $organism) {
                        $hasAccess = true;
                        break;
                    }
                }
            }
        }
        return $hasAccess;
    }

    /**
     * @param Organism $organism
     * @return bool
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function setActiveOrganism(Organism $organism): bool
    {
        $connection = $organism->getConnectionForDataProvider($this->dataProvider);
        if (!$connection) {
            throw new WedofConnectionException("Aucune connection du type " . $this->dataProvider->getValue() . " existant pour l'organisme " . $organism->getSiret());
        }
        $siret = $this->getUserInfos($connection)['selectedSiret'];
        if ($siret != $organism->getSiret()) {
            $this->logger->debug("[setCurrentOrganism][" . $organism->getSiret() . "] not correct selected organism: $siret");
            $this->logger->debug("[setCurrentOrganism][" . $organism->getSiret() . "] update current organism to: " . $organism->getSiret());
            $this->updateUserInfos($connection, array('selectedSiret' => $organism->getSiret()));
            $siret = $this->getUserInfos($connection)['selectedSiret'];
        }
        return $siret === $organism->getSiret();
    }

    public function getMaxAttemptsBeforeStop(): int
    {
        return self::MAX_LOGIN_ATTEMPTS;
    }

    /**
     * @param string $externalId
     * @return string|null
     */
    public function getTrainingExtIdFromTrainingActionExtId(string $externalId): ?string
    {
        return $this->getTrainingExtIdFromSessionExtIdOrTrainingActionExtId($externalId);
    }

    /**
     * @param string $externalId
     * @return string|null
     */
    public function getTrainingExtIdFromSessionExtId(string $externalId): ?string
    {
        return $this->getTrainingExtIdFromSessionExtIdOrTrainingActionExtId($externalId);
    }

    /**
     * @param string $externalId
     * @return string|null
     */
    public function getShortTrainingActionExtId(string $externalId): ?string
    {
        $val = explode('/', $externalId);
        return $val[1] ?? null;
    }

    /**
     * @param array $registrationFolderRawData
     * @return Certification|null
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function retrieveCertificationFromRawData(array $registrationFolderRawData): ?Certification
    {
        if (!empty($registrationFolderRawData['codeReconnaissance']) && !empty($registrationFolderRawData['codeReferentiel'])) {
            if ($registrationFolderRawData['codeReferentiel'] === 'CERTIFINFO') {
                $params = [
                    'certifInfo' => $registrationFolderRawData['codeReconnaissance']
                ];
            } else { // RS, RNCP, CPF, ELU
                $params = [
                    'type' => $registrationFolderRawData['codeReferentiel'],
                    'code' => str_replace($registrationFolderRawData['codeReferentiel'], '', $registrationFolderRawData['codeReconnaissance'])
                ];
            }
            $certification = $this->certificationService->getCertification($params);
        } else {
            $certification = null;
        }
        return $certification;
    }

    /**
     * @param array $trainingRawData
     * @return Certification|null
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function retrieveCertificationFromTrainingRawData(array $trainingRawData): ?Certification
    {
        return $this->certificationService->getCertification(['code' => $trainingRawData['code'],
            'type' => $trainingRawData['type']]);
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @return string
     */
    public function getRegistrationFolderExternalLink(RegistrationFolder $registrationFolder): string
    {
        return 'https://of.moncompteformation.gouv.fr/espace-prive/html/#/dossiers/detail/' . $registrationFolder->getExternalId();
    }

    /**
     * @param string $externalId
     * @return string
     */
    public function getSiretFromExternalId(string $externalId): string
    {
        return substr($externalId, 0, 14);
    }

    /**
     * @param array $registrationFolderRawData
     * @return array
     */
    public function getTrainingRawDataFromRawData(array $registrationFolderRawData): array
    {
        return [
            "title" => $registrationFolderRawData['trainingActionInfo']['title'],
            "id" => $registrationFolderRawData['trainingId'],
            "dataProvider" => $registrationFolderRawData['type'],
            "externalId" => $registrationFolderRawData['trainingId'],
            "statusCode" => '8',
            "statusLabel" => 'DELETED',
            "certifInfo" => null,
            "type" => null,
            "code" => null
        ];
    }

    /**
     * @param array $registrationFolderRawData
     * @return array
     */
    public function getTrainingActionRawDataFromRawData(array $registrationFolderRawData): array
    {
        return [
            "externalId" => $this->getTrainingActionExternalId($registrationFolderRawData['trainingId'], $registrationFolderRawData['trainingActionId']),
            "id" => $registrationFolderRawData['trainingActionId'],
            "trainingId" => $registrationFolderRawData['trainingId'],
            'totalTvaTTC' => $registrationFolderRawData['trainingActionInfo']['totalIncl'],
            "averageLearningTime" => $registrationFolderRawData['trainingActionInfo']['indicativeDuration'],
            "statusCode" => $registrationFolderRawData['trainingActionInfo']['etatAction'],
            "statusLabel" => isset($registrationFolderRawData['trainingActionInfo']['etatAction']) ? $this->getStatusLabelFromStatusCode($registrationFolderRawData['trainingActionInfo']['etatAction']) : 'DELETED'
        ];
    }

    /**
     * @param array $registrationFolderRawData
     * @return array
     */
    public function getSessionRawDataFromRawData(array $registrationFolderRawData): array
    {
        return [
            "trainingId" => $registrationFolderRawData['trainingId'],
            "actionId" => $registrationFolderRawData['trainingActionId'],
            "id" => $registrationFolderRawData['trainingActionInfo']['sessionId'],
            "endDate" => $registrationFolderRawData['trainingActionInfo']['sessionEndDate'],
            "beginDate" => $registrationFolderRawData['trainingActionInfo']['sessionStartDate'],
            "statusCode" => '8',
            "statusLabel" => 'DELETED',
            "externalId" => $this->getSessionExternalId($registrationFolderRawData['trainingId'], $registrationFolderRawData['trainingActionId'], $registrationFolderRawData['trainingActionInfo']['sessionId'])
        ];
    }

    /**
     * @param Connection $connection
     * @return void
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function startInitialization(Connection $connection): void
    {
        if (!$connection->isInitialized() && !$this->hasActivityOnCpf($connection->getOrganism())) {
            /** @var CronTaskManagerService $cronTaskManagerService */
            $cronTaskManagerService = $this->container->get("App\Service\CronTaskManagerService");
            $cronTaskManagerService->dispatchStartBatchesRegistrationFolders($connection->getOrganism(), $this->dataProvider, 100, 0);
            $this->dispatcher->dispatch(new ConnectionEvents($connection), ConnectionEvents::INITIALIZE_STARTED);
        }
    }

    /**
     * @param Connection $connection
     * @return void
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function initializeCompleted(Connection $connection): void
    {
        $siret = $connection->getOrganism()->getSiret();
        $folderCount = $this->registrationFolderService->countByOrganismAndDataProvider($connection->getOrganism(), $this->dataProvider);
        $nextMessages = $folderCount > 0 ? new ArrayCollection([new SynchronizePayments($siret, $this->dataProvider)]) : null;
        $this->messageBus->dispatch(new SynchronizeCatalog($siret, $this->dataProvider, [0, 3, 7, 9], null, true, [], $nextMessages)); //don't get deleted trainings
    }


    public function getActionsRulesForRegistrationFolder(): array
    {
        return [
            RegistrationFolderStates::NOT_PROCESSED()->getValue() => [
                RegistrationFolderStates::VALIDATED()->getValue() => [], //empty array necessary
                RegistrationFolderStates::REFUSED_BY_ORGANISM()->getValue() => [],
            ],
            RegistrationFolderStates::VALIDATED()->getValue() => [],
            RegistrationFolderStates::WAITING_ACCEPTATION()->getValue() => [],
            RegistrationFolderStates::ACCEPTED()->getValue() => [
                RegistrationFolderStates::IN_TRAINING()->getValue() => [],
                RegistrationFolderStates::CANCELED_BY_ATTENDEE_NOT_REALIZED()->getValue() => [],
                RegistrationFolderStates::CANCELED_BY_ORGANISM()->getValue() => []
            ],
            RegistrationFolderStates::IN_TRAINING()->getValue() => [
                RegistrationFolderStates::TERMINATED()->getValue() => [],
                RegistrationFolderStates::SERVICE_DONE_DECLARED()->getValue() => []
            ],
            RegistrationFolderStates::TERMINATED()->getValue() => [
                RegistrationFolderStates::SERVICE_DONE_DECLARED()->getValue() => []
            ],
            RegistrationFolderStates::SERVICE_DONE_DECLARED()->getValue() => [],
            RegistrationFolderStates::SERVICE_DONE_VALIDATED()->getValue() => [
                RegistrationFolderStates::TO_BILL()->getValue() => ['billable']
            ],
            RegistrationFolderStates::CANCELED_BY_ATTENDEE()->getValue() => [],
            RegistrationFolderStates::CANCELED_BY_ATTENDEE_NOT_REALIZED()->getValue() => [],
            RegistrationFolderStates::CANCELED_BY_ORGANISM()->getValue() => [],
            RegistrationFolderStates::CANCELED_BY_FINANCER()->getValue() => [],
            RegistrationFolderStates::REFUSED_BY_ATTENDEE()->getValue() => [],
            RegistrationFolderStates::REFUSED_BY_ORGANISM()->getValue() => [],
            RegistrationFolderStates::REFUSED_BY_FINANCER()->getValue() => [],
            RegistrationFolderStates::REJECTED_WITHOUT_TITULAIRE_SUITE()->getValue() => []
        ];
    }

    protected function formatResponseHeaders(array $responseHeaders): array
    {
        // rename headers to match standard
        //https://datatracker.ietf.org/doc/draft-ietf-httpapi-ratelimit-headers/
        if (isset($responseHeaders['x-rate-limit-remaining'])) {
            $responseHeaders['X-RateLimit-Remaining'] = $responseHeaders['x-rate-limit-remaining'];
            $responseHeaders['X-RateLimit-Limit'] = $responseHeaders['x-rate-limit-limit'] ?? null;
            $responseHeaders['X-RateLimit-Reset'] = $responseHeaders['x-rate-limit-reset'] ?? null;
            $responseHeaders['X-RateLimit-Retry-At'] = $responseHeaders['x-rate-limit-retry-at'] ?? null;
            unset($responseHeaders['x-rate-limit-limit']);
            unset($responseHeaders['x-rate-limit-remaining']);
            unset($responseHeaders['x-rate-limit-reset']);
            unset($responseHeaders['x-rate-limit-retry-at']);
        }
        return $responseHeaders;
    }

    /**
     * @param Organism $organism
     * @param UploadedFile $originalFile
     * @return Organism
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     * @throws \Symfony\Component\Mailer\Exception\TransportExceptionInterface
     */
    public function uploadCatalogXml(Organism $organism, UploadedFile $originalFile): Organism
    {
        if (!$this->connectionService->hasAccess($organism, $this->dataProvider)) {
            throw new WedofBadRequestHttpException('Erreur, une connexion EDOF active est requise');
        }
        $fileName = $originalFile->getClientOriginalName();
        $content = mb_convert_encoding($originalFile->getContent(), 'UTF-8', 'ISO-8859-1'); // Ensure modifications in UTF-8 + THEY SEND IT WITH UTF-8 GRRRRRRRRR
        $minifiedContent = preg_replace( // Minify like it is done in the frontend of EDOF
            [
                '/<![ \r\n\t]*(--([^\-]|[\r\n]|-[^\-])*--[ \r\n\t]*)>/', // Remove comments
                '/[ \r\n\t]+xmlns/',                                     // Normalize xmlns spacing
                '/>\s*</'                                                // Remove whitespace between tags
            ],
            [
                '',
                ' xmlns',
                '><'
            ],
            $content
        );
        SimpleXMLElementTools::validateCdcXML(mb_convert_encoding($minifiedContent, 'ISO-8859-1', 'UTF-8'), '/../../../data/lheo_v5r2.xsd'); // Check again just in case, needs proper encoding for char length
        $tempPath = tempnam(sys_get_temp_dir(), $fileName);
        file_put_contents($tempPath, $minifiedContent);
        $CPF_Request_URL = Tools::getEnvValue('CPF_API_BASE_URI') . '/private/siret/' . $organism->getSiret() . '/catalog-upload';
        $options = [
            'method' => 'POST',
            'fullResponse' => true,
            'organism' => $organism,
            'files' => [
                'file' => [
                    'path' => $tempPath,
                    'name' => $fileName,
                    'contentType' => 'application/xml' // Don't specify encoding as THEY SEND IT WITH UTF-8 GRRRRRRRRR
                ]
            ],
            'cat_api_status' => 'private-catalog-upload'
        ];
        $response = $this->sendRequest($CPF_Request_URL, $options);
        $statusCode = $response['statusCode'];
        if ($statusCode !== 201) {
            $this->logger->error('[CatalogXML] error organism: ' . $organism->getSiret() . ' status: ' . $statusCode . ' content: ' . $response['content']);
            throw new WedofCpfBackendException('Erreur, le fichier du catalogue a été refusé par EDOF');
        }
        $cpfCatalogMetadata = $organism->getCpfCatalogMetadata();
        $cpfCatalogMetadata['upload'] = [
            'startDate' => (new DateTime('now'))->format('Y-m-d\TH:i:s.u\Z'),
            'state' => 'inProgress',
            'fileName' => $fileName
        ];
        $organism->setCpfCatalogMetadata($cpfCatalogMetadata);
        $this->organismService->save($organism);
        return $organism;
    }

    /**
     * @throws ORMException
     * @throws RedirectionExceptionInterface
     * @throws WedofCpfBackendException
     * @throws ClientExceptionInterface
     * @throws WedofConnectionException
     * @throws OptimisticLockException
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws NonUniqueResultException
     * @throws NoResultException
     * @throws ErrorException
     */
    public function isCatalogUploadInProgress(Organism $organism): bool
    {
        $CPF_Request_URL = Tools::getEnvValue('CPF_API_BASE_URI') . '/private/siret/' . $organism->getSiret() . '/upload-config';
        $options = [
            'method' => 'GET',
            'organism' => $organism,
            'cat_api_status' => 'private-catalog-upload'
        ];
        $result = $this->sendRequest($CPF_Request_URL, $options);
        return !empty($result['reportExpected']);
    }

    /**
     * @param Organism $organism
     * @return array|null
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     * @throws WedofCpfBackendException
     */
    public function getLastestCatalogUploadReport(Organism $organism): ?array
    {
        $cpfCatalogMetadata = $organism->getCpfCatalogMetadata();
        $reports = $this->listUploadCatalogXmlReports($organism);
        $sentFileName = str_replace('-', '', $cpfCatalogMetadata['upload']['fileName']);
        $this->logger->debug("[CatalogXML] getLastestCatalogUploadReport for fileName " . $sentFileName . " for organism " . $organism->getSiret());
        $report = Tools::array_find($reports, function ($report) use ($organism, $sentFileName) {
            $reconstructedFileName = $organism->getSiret() . $report['name'] . '.xml'; // Reconstruct original filename from report info
            return $reconstructedFileName === $sentFileName; // Somehow they remove the dashes from the original fileName
        });
        if (!empty($report)) {
            $response = $this->getUploadCatalogXmlReport($organism, $report['uri']);
            return [
                'content' => $response['content'],
                'fileName' => $report['uri']
            ];
        }
        return null;
    }

    //----------------
    // METHODES PRIVES
    //----------------

    /**
     * @param string $externalId
     * @param Organism $organism
     * @param array $options
     * @return RegistrationFolder|null
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    private function getRegistrationFolderByExternalId(string $externalId, Organism $organism, array $options = []): ?RegistrationFolder
    {
        $options = array_merge(["createIfNotExist" => true, "refresh" => false, "rawData" => null], $options);
        $registrationFolder = $this->registrationFolderService->getByExternalId($externalId);
        if ((!$registrationFolder && $options['createIfNotExist']) || $options['refresh']) {
            $rawData = !empty($options['rawData']) ? $this->getRegistrationFolderDetailsRawData($organism, $options['rawData']) : $this->getRegistrationFolderRawData($organism, $externalId);
            if ($registrationFolder) {
                return $this->registrationFolderService->updateFromRawData(DataProviders::from($rawData['type']), $rawData, $options['refresh']);
            } else {
                return $this->registrationFolderService->createFromRawData(DataProviders::from($rawData['type']), $rawData);
            }
        }
        return $registrationFolder;
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @param RegistrationFolderStates $toState
     * @param array $parameters
     * @return RegistrationFolder
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    private function changeFromStateToState(RegistrationFolder $registrationFolder, RegistrationFolderStates $toState, array $parameters): RegistrationFolder
    {
        $this->logger->info($registrationFolder->getExternalId() . " start change state to: $toState");
        $CPF_Request_URL = Tools::getEnvValue("CPF_API_BASE_URI") . '/private/siret/' . $registrationFolder->getOrganism()->getSiret() . '/organisms/current/registration-folders/' . $registrationFolder->getExternalId() . '/status';
        $options = [
            "method" => "PUT",
            "parameters" => $parameters,
            "organism" => $registrationFolder->getOrganism(),
            "cat_api_status" => "private-registrationFolderUpdate-state"
        ];
        $rawData = $this->sendRequest($CPF_Request_URL, $options);
        $rawData = $this->getRegistrationFolderDetailsRawData($registrationFolder->getOrganism(), $rawData);
        return $this->registrationFolderService->createOrUpdate($rawData, DataProviders::from($registrationFolder->getType()), $registrationFolder->getExternalId());
    }

    /**
     * @param Connection $connection
     * @return array|null
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     * @throws Exception
     * @throws Throwable
     */
    private function getTokenData(Connection $connection): ?array
    {
        $credentials = $connection->getDataProvider() === $this->dataProvider->getValue() ? $connection->getCredentials() : null;
        if (!$credentials || !isset($credentials['username'])) {
            throw new ErrorException("A user or cpf credentials must be provided to use the CPF API");
        }
        try {
            $tokenData = $this->getUrlAccessTokenEFP([
                "username" => $credentials['username'],
                "password" => $credentials['password'],
                "siret" => $connection->getOrganism()->getSiret()
            ]);
        } catch (WedofConnectionException $e) {
            $tokenData = null;
        }
        return $tokenData;
    }

    /**
     * @param string $tokenUrl
     * @return array
     */
    private function parseTokenUrl(string $tokenUrl): array
    {
        $query = parse_url($tokenUrl, PHP_URL_FRAGMENT);
        parse_str($query, $params);
        return $params;
    }

    /**
     * @param string $url
     * @param array $options
     * @return string|null
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    private function sendRequestForFile(string $url, array $options = []): ?string
    {
        $config = $this->configService->getConfig();
        if (!$config->isCpfApiEnabled()) {
            throw new WedofCpfBackendException("EDOF API is offline", 503);
        }

        $options = array_merge(['method' => 'GET', "organism" => null, 'access_token' => null], $options);

        //if organism is set it will override token value
        if ($options['organism']) {
            /** @var Connection $connection */
            $connection = $options['organism']->getConnectionForDataProvider($this->dataProvider);
            if (!$connection) {
                throw new WedofConnectionException("Aucune connection du type " . $this->dataProvider->getValue() . " existant pour l'organisme " . $options['organism']->getSiret());
            }
            if ($connection->getState() !== ConnectionStates::ACTIVE()->getValue() && $connection->getCredentials()) {
                throw new WedofConnectionException("Erreur de connexion à l'API " . $this->dataProvider->getValue() . " vérifiez que l'habilitation est active pour l'organisme " . $options['organism']->getSiret());
            }
            $options = $this->setAuthentication($options);
        }

        //TODO options parameters
        $array = array('http' => array(
            'headers' => [
                'User-Agent' => "'Mozilla/5.0 (Windows; U; Windows NT 6.1; en-US)AppleWebKit/534.10 (KHTML, like Gecko) Chrome/8.0.552.224 Safari/534.10'"
            ],
            'method' => $options['method'],
            'header' => 'Authorization: Bearer ' . $options['access_token'],
            "ignore_errors" => true
        ));

        $context = stream_context_create($array);
        $data = @file_get_contents($url, false, $context);
        if ($data === FALSE) {
            return null;
        } else {
            return $data;
        }
    }

    /**
     * @param string $id
     * @return string
     */
    private function cleanId(string $id): string
    {
        return rawurlencode(preg_replace('/[\r\n\t\f]+/', '', $id));
    }

    /**
     * @param $data
     * @return array
     */
    private function getOrganismRawDataFromTrainingData($data): array
    {
        $organismData = [
            "siren" => substr($data['siretOrganisme'], 0, 9),
            "siret" => $data['siretOrganisme'],
            "name" => $data['raisonSocialeOrganisme'],
            "emails" => [$data['contactInformation']['courrier']],
            "phones" => $data['contactInformation']['telephones'],
        ];
        if (!empty($data['contactInformation']['address']['city'])) {
            $organismData['address'] = $data['contactInformation']['address']['numeroVoie'];
            if (!empty($data['address']['numeroVoie'])) {
                $organismData['address'] .= " " . $data['contactInformation']['address']['numeroVoie'];
            }
            if (!empty($data['contactInformation']['address']['codeIndiceRepetition'])) {
                $organismData['address'] .= " " . $data['contactInformation']['address']['codeIndiceRepetition'];
            }
            if (!empty($data['contactInformation']['address']['codeTypeVoie'])) {
                $organismData['address'] .= " " . $data['contactInformation']['address']['codeTypeVoie'];
            }
            if (!empty($data['contactInformation']['address']['libelleVoie'])) {
                $organismData['address'] .= " " . $data['contactInformation']['address']['libelleVoie'];
            }
            if (!empty($data['contactInformation']['address']['complementAdresse'])) {
                $organismData['address'] .= " " . $data['contactInformation']['address']['complementAdresse'];
            }
            if (!empty($data['contactInformation']['address']['complementIdentification'])) {
                $organismData['address'] .= " " . $data['contactInformation']['address']['complementIdentification'];
            }
            if (!empty($data['contactInformation']['address']['numLibelleVoie'])) {
                $organismData['address'] .= " " . $data['contactInformation']['address']['numLibelleVoie'];
            }
            if (!empty($data['contactInformation']['address']['lieuDit'])) {
                $organismData['address'] .= " " . $data['contactInformation']['address']['lieuDit'];
            }
            $organismData['city'] = $data['contactInformation']['address']['city'];
            $organismData['postalCode'] = $data['contactInformation']['address']['postalCode'];
        }
        return $organismData;
    }

    /**
     * @param Connection $connection
     * @return array|null
     * @throws ORMException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws OptimisticLockException
     * @throws ErrorException
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     * @throws Throwable
     */
    private function getUserInfos(Connection $connection): ?array
    {
        $CPF_Request_URL = Tools::getEnvValue("CPF_API_BASE_URI") . '/private/siret/' . $connection->getOrganism()->getSiret() . '/collaborators/me';
        $options = [
            'cat_api_status' => "private-profile",
            "access_token" => $connection->getCredentials()['access_token']
        ];
        return $this->sendRequest($CPF_Request_URL, $options);
    }

    /**
     * @throws ORMException
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     * @throws OptimisticLockException
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws NonUniqueResultException
     * @throws ErrorException
     * @throws NoResultException
     */
    private function getUserInfosWithAccessToken(Connection $connection, $access_token): ?array
    {
        $CPF_Request_URL = Tools::getEnvValue("CPF_API_BASE_URI") . '/private/siret/' . $connection->getOrganism()->getSiret() . '/collaborators/me';
        $options = [
            'cat_api_status' => "private-profile",
            "access_token" => $access_token
        ];
        return $this->sendRequest($CPF_Request_URL, $options);
    }

    /**
     * @param Connection $connection
     * @param string $accessToken
     * @return void
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    private function acceptPrimoConnect(Connection $connection, string $accessToken): void
    {
        $CPF_Request_URL = Tools::getEnvValue("CPF_API_BASE_URI") . '/private/siret/' . $connection->getOrganism()->getSiret() . '/collaborators';
        $options = [
            'method' => 'POST',
            'parameters' => ['status' => 'PRIMO_CONNECT'],
            'cat_api_status' => "private-profile",
            "access_token" => $accessToken
        ];
        $this->sendRequest($CPF_Request_URL, $options);
    }

    /**
     * @param Connection $connection
     * @param string $accessToken
     * @return array|true[]
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    private function validateTOS(Connection $connection, string $accessToken): array
    {
        $CPF_Request_URL = Tools::getEnvValue("CPF_API_BASE_URI") . '/private/siret/' . $connection->getOrganism()->getSiret() . '/collaborators/me/tos/validate';
        $options = [
            'method' => 'POST',
            'cat_api_status' => "validate-tos",
            "access_token" => $accessToken
        ];
        return $this->sendRequest($CPF_Request_URL, $options);
    }

    /**
     * @param Connection $connection
     * @param array $parameters
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    private function updateUserInfos(Connection $connection, array $parameters)
    {
        $CPF_Request_URL = Tools::getEnvValue("CPF_API_BASE_URI") . '/private/siret/' . $connection->getOrganism()->getSiret() . '/collaborators/me';
        $options = [
            'method' => 'PUT',
            "parameters" => $parameters,
            'cat_api_status' => "private-profile",
            'access_token' => $connection->getCredentials()['access_token']
        ];
        $values = $this->sendRequest($CPF_Request_URL, $options);
        if ($values) {
            $credentials = $connection->getCredentials();
            $credentials['userInfos'] = $values;
            $connection->setCredentials($credentials);
            $this->connectionService->save($connection);
        }
    }

    /**
     * EDOF order them by date DESC
     * Result is array of :
     * {
     *  "name": "_catalog_20250611T15_04_30",
     *  "uri": "53222292400039_catalog_20250611T15_04_30.20250618131450_CR_20250618140056.csv",
     *  "date": "18/06/2025 à 14h00"
     * }
     * @throws ORMException
     * @throws RedirectionExceptionInterface
     * @throws WedofCpfBackendException
     * @throws ClientExceptionInterface
     * @throws WedofConnectionException
     * @throws OptimisticLockException
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws NonUniqueResultException
     * @throws NoResultException
     * @throws ErrorException
     */
    private function listUploadCatalogXmlReports(Organism $organism): ?array
    {
        $CPF_Request_URL = Tools::getEnvValue('CPF_API_BASE_URI') . '/private/siret/' . $organism->getSiret() . '/report-files';
        $options = [
            'method' => 'GET',
            'organism' => $organism,
            'cat_api_status' => 'private-catalog-upload'
        ];
        return $this->sendRequest($CPF_Request_URL, $options);
    }

    /**
     * @param Organism $organism
     * @param string $reportFileName
     * @return array|true[]|null
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    private function getUploadCatalogXmlReport(Organism $organism, string $reportFileName): ?array
    {
        $CPF_Request_URL = Tools::getEnvValue('CPF_API_BASE_URI') . '/private/siret/' . $organism->getSiret() . '/REPORT/' . $reportFileName;
        $options = [
            'method' => 'GET',
            'organism' => $organism,
            'cat_api_status' => 'private-catalog-upload',
            'fullResponse' => true
        ];
        return $this->sendRequest($CPF_Request_URL, $options);
    }

    /**
     * @param Organism $organism
     * @param $rawData
     * @return mixed
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    private function mergeWithBillingData(Organism $organism, $rawData)
    {
        if (!empty($rawData['idSuiviFinancement'])) {
            $billingRawData = $this->getRegistrationFolderBillingRawData($organism, $rawData['idSuiviFinancement']);
            if (!empty($billingRawData['billId'])) {
                $rawData['__billingRawData'] = $billingRawData;
            }
        }
        return $rawData;
    }

    /**
     * @param array $rawData
     * @return mixed
     */
    private function mergeWithSolicitationData(array $rawData): array
    {
        if (!empty($rawData['trainingActionInfo']['solicitations'])) {
            foreach ($rawData['trainingActionInfo']['solicitations'] as $solicitation) {
                if ($rawData['currentState'] === RegistrationFolderStates::VALIDATED()->getValue() && $solicitation['status'] === 'inProgress' && $solicitation['fundingType'] === SolicitationFundingType::FINANCEUR()->getValue()) {
                    $rawData['currentState'] = RegistrationFolderStates::WAITING_ACCEPTATION()->getValue();
                    //TODO store fundingType
                    $this->logger->info("[" . $rawData['id'] . "][solicitation] RegistrationFolder is in state: " . $rawData['currentState']);
                    break;
                }
            }
        }
        return $rawData;
    }

    /**
     * @param string $state
     * @return string
     */
    private function getFilterForState(string $state): string
    {
        switch ($state) {
            case RegistrationFolderStates::ALL_OPENED():
            case RegistrationFolderStates::NOT_PROCESSED()->getValue():
            case RegistrationFolderStates::VALIDATED()->getValue():
            case RegistrationFolderStates::ACCEPTED()->getValue():
            case RegistrationFolderStates::IN_TRAINING()->getValue():
            case RegistrationFolderStates::TERMINATED()->getValue():
            case RegistrationFolderStates::SERVICE_DONE_DECLARED()->getValue():
            case RegistrationFolderStates::SERVICE_DONE_VALIDATED()->getValue():
                $filter = 'opened';
                break;
            case RegistrationFolderStates::ALL_CLOSED():
            case RegistrationFolderStates::REFUSED()->getValue():
            case RegistrationFolderStates::CANCELED()->getValue():
            case RegistrationFolderStates::BILLED()->getValue():
                $filter = 'closed';
                break;
            case RegistrationFolderStates::TO_BILL()->getValue():
                $filter = 'billing';
                break;
            default:
                $filter = 'all';
        }
        return $filter;
    }

    /**
     * @throws ORMException
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     * @throws WedofConnectionException
     * @throws OptimisticLockException
     * @throws TransportExceptionInterface
     * @throws Throwable
     * @throws ServerExceptionInterface
     * @throws NonUniqueResultException
     * @throws ErrorException
     * @throws NoResultException
     */
    private function getUrlAccessTokenEFP(array $credentials): array
    {
        // HERE
        $CPF_Request_URL = Tools::getEnvValue("KASTORR_BASE_URI") . "auth-edof";
        $options = [
            'method' => 'POST',
            'parameters' => $credentials,
            'cat_api_status' => "auth",
            'authenticationHeaderName' => Tools::getEnvValue("KASTORR_AUTH_HEADER"),
            'authenticationHeaderValue' => Tools::getEnvValue("KASTORR_AUTH"),
        ];
        $data = $this->sendRequest($CPF_Request_URL, $options);
        if (isset($data['access_token'])) {
            return $data;
        } else if (isset($data['error']) && $data['error'] === "credentials") {
            return ["error" => "credentials"];
        } else if (isset($data['error']) && $data['error'] === "revoked") {//TODO
            return ["error" => "revoked"];
        } else if (isset($data['error']) && $data['error'] === "captcha") {
            return ["error" => "captcha"];
        } else {
            $e = new WedofConnectionException($data['trace'] ?? ('CpfApiService:getUrlAccessTokenEFP ' . ($credentials['siret'] ?? '') . ' ' . var_dump($data)));
            $this->dispatcher->dispatch(new MonitoringEvents($e), MonitoringEvents::API_PROVIDER_ERROR);
            throw $e;
        }
    }

    /**
     * @param Connection $connection
     * @return void
     */
    private function updateOrganismInfos(Connection $connection): void
    {
        $organism = $connection->getOrganism();
        $userInfos = $connection->getCredentials()['userInfos'];

        $key = array_search($organism->getSiret(), array_column($userInfos->company->organisms, 'siret'));
        if ($key !== false && !empty($userInfos->company->organisms[$key])) {
            $organismData = $userInfos->company->organisms[$key];
            //check and update listing organism status
            $unlisted = $userInfos->company->topSanction ?? false; //global
            if (!$unlisted) {
                $unlisted = $organismData->topSanction ?? false;
            }
            $organism->setCpfUnlisted($unlisted);
            //try to find agreement if we don't have yet
            if (!$organism->getAgreement() && isset($organismData->nda)) {
                $organism->setAgreement($organismData->nda);
            }
            $this->organismService->save($organism);
        }
    }

    /**
     * @param array $rawData
     * @return RegistrationFolderControlStates
     */
    private function getControlStateFromRawData(array $rawData): RegistrationFolderControlStates
    {
        if (isset($rawData['etatBlocage'])) {
            switch ($rawData['etatBlocage']) {
                case 'BLOQUE':
                    $controlState = RegistrationFolderControlStates::IN_CONTROL();
                    break;
                case 'DEBLOQUE':
                    $controlState = RegistrationFolderControlStates::RELEASED();
                    break;
                case 'NON_BLOQUE':
                default:
                    $controlState = RegistrationFolderControlStates::NOT_IN_CONTROL();
                    break;
            }
        } else {
            $controlState = RegistrationFolderControlStates::NOT_IN_CONTROL();
        }

        return $controlState;
    }

    /**
     * @param Organism $organism
     * @return bool
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    private function hasActivityOnCpf(Organism $organism): bool
    {
        $time5minAgo = (new DateTime('now'))->modify('- 5 minutes');
        $urlCpf = 'https://of.moncompteformation.gouv.fr/edof-api/v1/api/private/siret/' . $organism->getSiret() . '/organisms/current/registration-folders';
        $qb = $this->endPointStatusRepository->createQueryBuilder('eps');

        $qb->select('count(eps.id)')
            ->where($qb->expr()->eq('eps.organism', ':organism'))
            ->andWhere($qb->expr()->gt('eps.date', ':time5minAgo'))
            ->andWhere($qb->expr()->like('eps.url', ':urlCpf'))
            ->setParameter('organism', $organism)
            ->setParameter('time5minAgo', $time5minAgo)
            ->setParameter('urlCpf', $urlCpf . '%');

        return $qb->getQuery()->getSingleScalarResult() > 0;
    }

    /**
     * @param array $sessionsRawData
     * @return array
     */
    private function prepareSessionRawDataFromPublicData(array $sessionsRawData): array
    {
        foreach ($sessionsRawData as &$sessionRawData) {
            $sessionRawData["trainingId"] = $sessionRawData['formationNumber'];
            $sessionRawData["actionId"] = $sessionRawData['actionNumber'];
            $sessionRawData["id"] = $sessionRawData['sessionNumber'];
            $sessionRawData["beginDate"] = $sessionRawData['period']['startDate'];
            $sessionRawData["endDate"] = $sessionRawData['period']['endDate'];
            $sessionRawData['statusCode'] = '0';
            $sessionRawData['statusLabel'] = 'ACTIVE';
            $sessionRawData['externalId'] = $this->getSessionExternalId($sessionRawData['trainingId'], $sessionRawData['actionId'], $sessionRawData['id']);
        }
        return $sessionsRawData;
    }

    /**
     * @param array $rawData
     * @return array
     */
    private function prepareSessionRawDataFromPrivateData(array $rawData): array
    {
        $rawData['externalId'] = $this->getSessionExternalId($rawData['trainingId'], $rawData['actionId'], $rawData['id']);
        return $rawData;
    }

    /**
     * @param array $sessionsRawData
     * @return array
     */
    private function prepareSessionsRawDataFromPrivateData(array $sessionsRawData): array
    {
        foreach ($sessionsRawData as &$sessionRawData) {
            $sessionRawData['externalId'] = $this->getSessionExternalId($sessionRawData['trainingId'], $sessionRawData['actionId'], $sessionRawData['id']);
        }
        return $sessionsRawData;
    }

    /**
     * @param array $trainingActionsRawData
     * @return array
     */
    private function prepareTrainingActionsRawDataFromPrivateData(array $trainingActionsRawData): array
    {
        foreach ($trainingActionsRawData as &$trainingActionRawData) {
            $trainingActionRawData['externalId'] = $this->getTrainingActionExternalId($trainingActionRawData['trainingId'], $trainingActionRawData['id']);
        }
        return $trainingActionsRawData;
    }

    /**
     * @param string $statusCode
     * @return string|null
     */
    private function getStatusLabelFromStatusCode(string $statusCode): ?string
    {
        $statusLabels = [
            "0" => "ACTIVE",
            "8" => "DELETED",
            "7" => "VALIDATED",
            "9" => "DRAFT",
            "3" => "ARCHIVED"
        ];
        return $statusLabels[$statusCode] ?? null;
    }

    /**
     * @param string $externalId
     * @return string
     */
    private function getTrainingExtIdFromSessionExtIdOrTrainingActionExtId(string $externalId): string
    {
        return explode('/', $externalId)[0];
    }

    /**
     * @param string $externalId
     * @return string
     */
    private function getSirenFromExternalId(string $externalId): string
    {
        return substr($externalId, 0, 9);
    }

    /**
     * @param string $externalId
     * @return string|null
     */
    private function getShortExternalId(string $externalId): ?string
    {
        return substr($externalId, 15);
    }

    /**
     * @param Session $session
     * @return string
     */
    private function getShortTrainingExternalIdFromSession(Session $session): string
    {
        return $this->getShortExternalId(explode("/", $session->getExternalId())[0]);
    }

    /**
     * @param Session $session
     * @return string
     */
    private function getShortTrainingActionExternalIdFromSession(Session $session): string
    {
        return $this->getShortExternalId(explode("/", $session->getExternalId())[1]);
    }

    /**
     * @param Session $session
     * @return string
     */
    private function getSessionShortExternalIdFromSession(Session $session): string
    {
        return $this->getShortExternalId(explode("/", $session->getExternalId())[2]);
    }

    /**
     * @param Training $training
     * @return string
     */
    private function getTrainingShortExternalIdFromTraining(Training $training): string
    {
        return $this->getShortExternalId($training->getExternalId());
    }

    /**
     * @param TrainingAction $trainingAction
     * @return string
     */
    private function getShortTrainingExternalIdFromTrainingAction(TrainingAction $trainingAction): string
    {
        return $this->getShortExternalId(explode("/", $trainingAction->getExternalId())[0]);
    }

    /**
     * @param TrainingAction $trainingAction
     * @return string
     */
    private function getShortTrainingActionExternalIdFromTrainingAction(TrainingAction $trainingAction): string
    {
        return $this->getShortExternalId(explode("/", $trainingAction->getExternalId())[1]);
    }
}
