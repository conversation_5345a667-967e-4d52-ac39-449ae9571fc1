<?php

namespace App\Service\DataProviders;

use App\Entity\Certification;
use App\Entity\Organism;
use App\Entity\Training;
use App\Entity\TrainingAction;
use App\Library\utils\enums\SessionStates;
use App\Library\utils\enums\TrainingActionStates;
use App\Library\utils\enums\TrainingStates;

interface InterfaceCatalogApiService
{
    /**
     * @param Organism $organism
     * @param string $trainingId
     * @param array $options
     * @return array|null
     */
    public function getTrainingRawData(Organism $organism, string $trainingId, array $options = []): ?array;

    /**
     * @param Organism $organism
     * @param array|null $trainingStatuses
     * @return array|null
     */
    public function getTrainingsRawData(Organism $organism, array $trainingStatuses = null): ?array;

    /**
     * @param array $registrationFolderRawData
     * @return array
     */
    public function getTrainingRawDataFromRawData(array $registrationFolderRawData): array;

    /**
     * @param Training $training
     * @param string $trainingActionId
     * @return array|null
     */
    public function getTrainingActionRawData(Training $training, string $trainingActionId): ?array;

    /**
     * @param Training $training
     * @return array|null
     */
    public function getTrainingActionsRawData(Training $training): ?array;

    /**
     * @param array $registrationFolderRawData
     * @return array
     */
    public function getTrainingActionRawDataFromRawData(array $registrationFolderRawData): array;

    /**
     * @param TrainingAction $trainingAction
     * @return array|null
     */
    public function getSessionsRawData(TrainingAction $trainingAction): ?array;

    /**
     * @param TrainingAction $trainingAction
     * @param string $sessionId
     * @return array|null
     */
    public function getSessionRawData(TrainingAction $trainingAction, string $sessionId): ?array;

    /**
     * @param array $registrationFolderRawData
     * @return array
     */
    public function getSessionRawDataFromRawData(array $registrationFolderRawData): array;

    /**
     * @param TrainingAction $trainingAction
     * @return void
     */
    public function updateEvaluationsForTrainingAction(TrainingAction $trainingAction): void;

    /**
     * @param string $trainingId
     * @return string
     */
    public function getTrainingExternalId(string $trainingId): string;

    /**
     * @param string $trainingId
     * @param string $trainingActionId
     * @return string
     */
    public function getTrainingActionExternalId(string $trainingId, string $trainingActionId): string;

    /**
     * @param string $trainingId
     * @param string $trainingActionId
     * @param string $sessionId
     * @return string
     */
    public function getSessionExternalId(string $trainingId, string $trainingActionId, string $sessionId): string;

    /**
     * @param string $externalId
     * @return string|null
     */
    public function getTrainingExtIdFromTrainingActionExtId(string $externalId): ?string;

    /**
     * @param string $externalId
     * @return string|null
     */
    public function getTrainingExtIdFromSessionExtId(string $externalId): ?string;

    /**
     * @param string $externalId
     * @return string|null
     */
    public function getShortTrainingActionExtId(string $externalId): ?string;

    /**
     * @param array $trainingRawData
     * @return Certification|null
     */
    public function retrieveCertificationFromTrainingRawData(array $trainingRawData): ?Certification;

    /**
     * @param string $externalId
     * @return string
     */
    public function getSiretFromExternalId(string $externalId): string;

    /**
     * @param TrainingAction $trainingAction
     * @return string
     */
    public function getTrainingActionExternalLink(TrainingAction $trainingAction): string;

    /**
     * @param array $rawData
     * @param Certification|null $certification
     * @return TrainingStates
     */
    public function getTrainingState(array $rawData, ?Certification $certification): TrainingStates;

    /**
     * @param array $rawData
     * @param Training $training
     * @return TrainingActionStates
     */
    public function getTrainingActionState(array $rawData, Training $training): TrainingActionStates;

    /**
     * @param array $rawData
     * @param TrainingAction $trainingAction
     * @return SessionStates
     */
    public function getSessionState(array $rawData, TrainingAction $trainingAction): SessionStates;
}
