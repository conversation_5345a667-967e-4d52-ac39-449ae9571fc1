<?php

namespace App\Service\DataProviders;

use App\Entity\Connection;
use App\Entity\Organism;
use App\Library\utils\enums\DataProviders;
use App\Repository\EndPointStatusRepository;
use App\Service\ConfigService;
use App\Service\ConnectionService;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Security\Core\Security;

class PassportPreventionApiService extends BaseApiService
{

    /**
     * @param EventDispatcherInterface $dispatcher
     * @param ConfigService $configService
     * @param RequestStack $requestStack
     * @param LoggerInterface $logger
     * @param EndPointStatusRepository $endPointStatusRepository
     * @param ConnectionService $connectionService
     * @param Security $security
     */
    public function __construct(EventDispatcherInterface $dispatcher,
                                ConfigService $configService,
                                RequestStack $requestStack,
                                LoggerInterface $logger,
                                EndPointStatusRepository $endPointStatusRepository,
                                ConnectionService $connectionService,
                                Security $security)
    {
        parent::__construct(DataProviders::PASSPORT_PREVENTION(),
            $configService, $connectionService, $requestStack,
            $endPointStatusRepository, $dispatcher, $logger, $security);
    }

    /**
     * @param LoggerInterface $logger
     */
    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    public function getMaxAttemptsBeforeStop(): int
    {
        return 3;
    }

    /**
     * @param Connection $connection
     * @param bool $checkOrganismAccess
     * @return array
     */
    public function authenticate(Connection $connection, bool $checkOrganismAccess = true): array
    {
        return [];
    }

    /**
     * @param Organism $organism
     * @param array|null $params
     * @return string
     */
    public function checkBeforeHabilitate(Organism $organism, array $params = null): string
    {
        return '';
    }

    /**
     * @param Organism $organism
     * @param Connection $connection
     * @param array $params
     * @return bool
     */
    public function habilitate(Organism $organism, Connection $connection, array $params): bool
    {
        return false;
    }

    public function getUsername(Connection $connection): string
    {
        return '';
    }

    public function requiresAuthentication(): bool
    {
        return true;
    }

    public function setActiveOrganism(Organism $organism): bool
    {
        return false;
    }

    public function setAuthentication(array $options = []): array
    {
        return $options;
    }


}