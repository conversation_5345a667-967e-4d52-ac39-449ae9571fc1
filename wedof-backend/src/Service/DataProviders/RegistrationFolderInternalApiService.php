<?php


namespace App\Service\DataProviders;

use App\Entity\Certification;
use App\Entity\Organism;
use App\Entity\RegistrationFolder;
use App\Entity\RegistrationFolderReason;
use App\Entity\Session;
use App\Entity\Training;
use App\Entity\TrainingAction;
use App\Entity\User;
use App\Exception\WedofConnectionException;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\enums\RegistrationFolderAttendeeStates;
use App\Library\utils\enums\RegistrationFolderBillingStates;
use App\Library\utils\enums\RegistrationFolderStates;
use App\Library\utils\enums\SessionStates;
use App\Library\utils\enums\TrainingActionStates;
use App\Library\utils\enums\TrainingStates;
use App\Library\utils\Tools;
use App\Repository\EndPointStatusRepository;
use App\Service\ConfigService;
use App\Service\ConnectionService;
use App\Service\RegistrationFolderService;
use DateTime;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use ErrorException;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Security\Core\Security;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Throwable as ThrowableAlias;

class RegistrationFolderInternalApiService extends InternalApiService implements InterfaceRegistrationFolderApiService, InterfaceCatalogApiService
{
    protected RegistrationFolderService $registrationFolderService;

    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    /**
     *  RegistrationFolderInternalApiService constructor.
     * @param ConfigService $configService
     * @param ConnectionService $connectionService
     * @param RequestStack $requestStack
     * @param EndPointStatusRepository $endPointStatusRepository
     * @param EventDispatcherInterface $dispatcher
     * @param LoggerInterface $logger
     * @param Security $security
     * @param RegistrationFolderService $registrationFolderService
     */
    public function __construct(ConfigService             $configService,
                                ConnectionService         $connectionService,
                                RequestStack              $requestStack,
                                EndPointStatusRepository  $endPointStatusRepository,
                                EventDispatcherInterface  $dispatcher,
                                LoggerInterface           $logger,
                                Security                  $security,
                                RegistrationFolderService $registrationFolderService)
    {
        parent::__construct($configService, $connectionService, $requestStack, $endPointStatusRepository, $dispatcher, $logger, $security);
        $this->registrationFolderService = $registrationFolderService;
    }

    /**
     * @param string $type
     * @return string
     */
    public static function getDataProviderPrefix(string $type): string
    {
        return strtoupper($type[0]); // TODO maybe add dash (-)
    }

    /**
     * @param Organism $organism
     * @param array $parameters
     * @param array $options
     * @return array
     */
    public function getRegistrationFoldersRawData(Organism $organism, array $parameters = [], array $options = []): array
    {
        return [];
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @param array $data
     * @return RegistrationFolder
     * @throws ErrorException
     * @throws ORMException
     * @throws ThrowableAlias
     */
    public function validateRegistrationFolder(RegistrationFolder $registrationFolder, array $data = []): RegistrationFolder
    {
        switch ($registrationFolder->getState()) {
            case RegistrationFolderStates::WAITING_ACCEPTATION()->getValue():
                $actionLabel = "Refus financeur";
                break;
            case RegistrationFolderStates::NOT_PROCESSED()->getValue():
            default:
                $actionLabel = "Proposition de l'organisme";
        }
        $rawData = $this->addStateToRawData($registrationFolder->getRawData(), $actionLabel, RegistrationFolderStates::VALIDATED(), $registrationFolder->getOrganism());
        return $this->registrationFolderService->createOrUpdate($rawData, DataProviders::from($registrationFolder->getType()), $registrationFolder->getExternalId());
    }


    /**
     * @param RegistrationFolder $registrationFolder
     * @return RegistrationFolder
     * @throws ErrorException
     * @throws ORMException
     * @throws ThrowableAlias
     */
    public function acceptRegistrationFolder(RegistrationFolder $registrationFolder): RegistrationFolder
    {
        switch ($registrationFolder->getState()) {
            case RegistrationFolderStates::WAITING_ACCEPTATION():
                $actionLabel = "Acceptation financeur";
                break;
            case RegistrationFolderStates::VALIDATED():
            default:
                $actionLabel = "Validation de la proposition";
        }
        $rawData = $this->addStateToRawData($registrationFolder->getRawData(), $actionLabel, RegistrationFolderStates::ACCEPTED(), $registrationFolder->getOrganism());
        return $this->registrationFolderService->createOrUpdate($rawData, DataProviders::from($registrationFolder->getType()), $registrationFolder->getExternalId());
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @param DateTime $inTrainingDate
     * @param User|null $user
     * @return RegistrationFolder
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws ThrowableAlias
     * @throws \Doctrine\ORM\ORMException
     */
    public function inTrainingRegistrationFolder(RegistrationFolder $registrationFolder, DateTime $inTrainingDate, User $user = null): RegistrationFolder
    {
        $rawData = $this->addStateToRawData($registrationFolder->getRawData(), "Déclaration Entrée en Formation", RegistrationFolderStates::IN_TRAINING(), $registrationFolder->getOrganism(), $user ? $user->getEmail() : null);

        //override the set date
        $rawData['states'] = array_merge($rawData['states'], [
            RegistrationFolderStates::IN_TRAINING()->getValue() => [
                'date' => $inTrainingDate->format('Y-m-d'),
            ]
        ]);

        return $this->registrationFolderService->createOrUpdate($rawData, DataProviders::from($registrationFolder->getType()), $registrationFolder->getExternalId());

    }


    /**
     * @param RegistrationFolder $registrationFolder
     * @param DateTime $terminatedDate
     * @param User|null $user
     * @return RegistrationFolder
     * @throws ErrorException
     * @throws ORMException
     * @throws ThrowableAlias
     * @throws \Doctrine\ORM\ORMException
     */
    public function terminateRegistrationFolder(RegistrationFolder $registrationFolder, DateTime $terminatedDate, User $user = null): RegistrationFolder
    {
        $rawData = $this->addStateToRawData($registrationFolder->getRawData(), "Déclaration Sortie de formation", RegistrationFolderStates::TERMINATED(), $registrationFolder->getOrganism(), $user ? $user->getEmail() : null);

        //override the set date
        $rawData['states'] = array_merge($rawData['states'], [
            RegistrationFolderStates::TERMINATED()->getValue() => [
                'date' => $terminatedDate->format('Y-m-d'),
            ]
        ]);

        return $this->registrationFolderService->createOrUpdate($rawData, DataProviders::from($registrationFolder->getType()), $registrationFolder->getExternalId());
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @param float $absenceDuration
     * @param int $completionRate
     * @param bool $forceMajeureAbsence
     * @param RegistrationFolderReason $reason
     * @param User|null $user
     * @return RegistrationFolder
     * @throws ErrorException
     * @throws ORMException
     * @throws ThrowableAlias
     * @throws \Doctrine\ORM\ORMException
     */
    public function declareServiceDoneRegistrationFolder(RegistrationFolder $registrationFolder, float $absenceDuration, int $completionRate, bool $forceMajeureAbsence, RegistrationFolderReason $reason, User $user = null): RegistrationFolder
    {
        $rawData = $this->addStateToRawData($registrationFolder->getRawData(), "Déclaration service fait", RegistrationFolderStates::SERVICE_DONE_DECLARED(), $registrationFolder->getOrganism(), $user ? $user->getEmail() : null);
        $rawData['trainingActionInfo'] = array_merge($rawData['trainingActionInfo'], [
            'trainingCompletionRate' => $completionRate,
            'forceMajeurCaseOf' => $forceMajeureAbsence,
            'uniteAbsenceOF' => "1",
            'dureeAbsenceOF' => $absenceDuration,
            'reason' => $reason->getCode()
        ]);
        $registrationFolder = $this->registrationFolderService->createOrUpdate($rawData, DataProviders::from($registrationFolder->getType()), $registrationFolder->getExternalId());
        return $this->validateServiceDoneRegistrationFolder($registrationFolder);
    }


    /**
     * @param RegistrationFolder $registrationFolder
     * @param RegistrationFolderReason $reason
     * @param string $description
     * @return RegistrationFolder
     * @throws ErrorException
     * @throws ORMException
     * @throws ThrowableAlias
     */
    public function refuseRegistrationFolder(RegistrationFolder       $registrationFolder,
                                             RegistrationFolderReason $reason,
                                             string                   $description): RegistrationFolder
    {
        $rawData = $this->addStateToRawData($registrationFolder->getRawData(), "Refus du dossier par l'organisme de formation", RegistrationFolderStates::REFUSED_BY_ORGANISM(), $registrationFolder->getOrganism());
        $rawData['cancellation'] = $rawData['cancellation'] ?? [];
        $rawData['cancellation'] = array_merge($rawData['cancellation'], [
            "codeCgu" => null,
            "codeMotif" => $reason->getCode(),
            "commentaire" => $description . " " . $reason->getLabel(),
            "motifAnnulation" => null
        ]);
        return $this->registrationFolderService->createOrUpdate($rawData, DataProviders::from($registrationFolder->getType()), $registrationFolder->getExternalId());
    }


    /**
     * @param RegistrationFolder $registrationFolder
     * @param RegistrationFolderReason $reason
     * @param string $description
     * @return RegistrationFolder
     * @throws ErrorException
     * @throws ORMException
     * @throws ThrowableAlias
     */
    public function cancelRegistrationFolder(RegistrationFolder $registrationFolder, RegistrationFolderReason $reason, string $description): RegistrationFolder
    {
        $rawData = $this->addStateToRawData($registrationFolder->getRawData(), "Annulation du dossier", $reason->getCode() == 4 ? RegistrationFolderStates::CANCELED_BY_ATTENDEE_NOT_REALIZED() : RegistrationFolderStates::CANCELED_BY_ORGANISM(), $registrationFolder->getOrganism());
        $rawData['cancellation'] = $rawData['cancellation'] ?? [];
        $rawData['cancellation'] = array_merge($rawData['cancellation'], [
            "codeCgu" => null,
            "codeMotif" => $reason->getCode(),
            "commentaire" => $description . " " . $reason->getLabel(),
            "motifAnnulation" => null
        ]);
        return $this->registrationFolderService->createOrUpdate($rawData, DataProviders::from($registrationFolder->getType()), $registrationFolder->getExternalId());
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @return RegistrationFolder
     * @throws ErrorException
     * @throws ORMException
     * @throws ThrowableAlias
     * @throws \Doctrine\ORM\ORMException
     */
    public function rejectRegistrationFolder(RegistrationFolder $registrationFolder): RegistrationFolder
    {
        $rawData = $this->addStateToRawData($registrationFolder->getRawData(), 'Rejet du dossier', RegistrationFolderStates::REJECTED_WITHOUT_TITULAIRE_SUITE(), $registrationFolder->getOrganism());

        return $this->registrationFolderService->createOrUpdate($rawData, DataProviders::from($registrationFolder->getType()), $registrationFolder->getExternalId());
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @return RegistrationFolder
     * @throws ErrorException
     * @throws ORMException
     * @throws ThrowableAlias
     * @throws \Doctrine\ORM\ORMException
     */
    public function attendeeRefuseRegistrationFolder(RegistrationFolder $registrationFolder): RegistrationFolder
    {
        $rawData = $this->addStateToRawData($registrationFolder->getRawData(), 'Refus du dossier par l\'apprenant', RegistrationFolderStates::REFUSED_BY_ATTENDEE(), $registrationFolder->getOrganism());

        return $this->registrationFolderService->createOrUpdate($rawData, DataProviders::from($registrationFolder->getType()), $registrationFolder->getExternalId());
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @return RegistrationFolder
     * @throws ErrorException
     * @throws ORMException
     * @throws ThrowableAlias
     * @throws \Doctrine\ORM\ORMException
     */
    public function attendeeCancelRegistrationFolder(RegistrationFolder $registrationFolder): RegistrationFolder
    {
        $rawData = $this->addStateToRawData($registrationFolder->getRawData(), 'Annulation du dossier par l\'apprenant', RegistrationFolderStates::CANCELED_BY_ATTENDEE_NOT_REALIZED(), $registrationFolder->getOrganism());

        return $this->registrationFolderService->createOrUpdate($rawData, DataProviders::from($registrationFolder->getType()), $registrationFolder->getExternalId());
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @return RegistrationFolder
     * @throws ErrorException
     * @throws ORMException
     * @throws ThrowableAlias
     * @throws \Doctrine\ORM\ORMException
     */
    public function waitRegistrationFolder(RegistrationFolder $registrationFolder): RegistrationFolder
    {
        $rawData = $this->addStateToRawData($registrationFolder->getRawData(), 'En attente de l\'acceptation du dossier', RegistrationFolderStates::WAITING_ACCEPTATION(), $registrationFolder->getOrganism());

        return $this->registrationFolderService->createOrUpdate($rawData, DataProviders::from($registrationFolder->getType()), $registrationFolder->getExternalId());
    }


    /**
     * @param Session $session
     * @param array $rawData
     * @param Organism $organism
     * @return array
     * @throws ThrowableAlias
     */
    public function preCreateRegistrationFolder(Session $session, array $rawData, Organism $organism): array
    {
        /** @var User $user */
        $user = $this->security->getUser();

        $sessionId = explode("/", $session->getExternalId())[2];
        $trainingAction = $session->getTrainingAction();
        $trainingActionId = explode("/", $trainingAction->getExternalId())[1];
        $training = $trainingAction->getTraining();
        $trainingId = $training->getExternalId();
        $todayDate = (new DateTime('now'))->format('Y-m-d\TH:i:s.v\Z');
        return array_merge($rawData, [
            'trainingActionInfo' => [
                'totalIncl' => $rawData['totalTTC'],
                'totalExcl' => Tools::convertTtcToHt($rawData['totalTTC'], $organism),
                'vat' => $organism->getVat() === 0.0 || $organism->getVat() === null ? $rawData['totalTTC'] : null,
                'vatExclTax5' => $organism->getVat() === 5.5 ? Tools::convertTtcToHt($rawData['totalTTC'], $organism) : null,
                'vatInclTax5' => $organism->getVat() === 5.5 ? $rawData['totalTTC'] : null,
                'vatExclTax20' => $organism->getVat() === 20.0 ? Tools::convertTtcToHt($rawData['totalTTC'], $organism) : null,
                'vatInclTax20' => $organism->getVat() === 20.0 ? $rawData['totalTTC'] : null,
                'indicativeDuration' => $trainingAction->getRawData()['averageLearningTime'],
                'sessionStartDate' => $session->getStartDate() ? $session->getStartDate()->format('Y-m-d\TH:i:s.u\Z') : null,
                'sessionEndDate' => $session->getEndDate() ? $session->getEndDate()->format('Y-m-d\TH:i:s.u\Z') : null,
                'sessionId' => $sessionId,
                'title' => $training->getTitle(),
                'address' => $trainingAction->getRawData()['trainingAddress'],
                'trainingGoal' => $training->getRawData()['goal'],
                'content' => $training->getRawData()['content'],
                'expectedResult' => $training->getRawData()['expectedResults'],
                'weeklyDuration' => $trainingAction->getRawData()['averageLearningTime'],
                'additionalFees' => $trainingAction->getRawData()['additionalFees'] ?? 0,
                'trainingPaces' => $trainingAction->getRawData()['paces'],
                'teachingModalities' => $trainingAction->getRawData()['teachingMethod'],
                'trainingCompletionRate' => null
            ],
            'history' => [
                [
                    "date" => $todayDate,
                    "label" => "Création du dossier",
                    "author" => $user->getEmail()
                ]
            ],
            'states' => [
                'notProcessed' => [
                    'date' => $todayDate
                ]
            ],
            'trainingId' => $trainingId,
            'trainingActionId' => $trainingActionId,
            'currentState' => RegistrationFolderStates::NOT_PROCESSED()->getValue(),
            'currentBillingState' => RegistrationFolderBillingStates::NOT_BILLABLE()->getValue(),
            'currentAttendeeState' => RegistrationFolderAttendeeStates::SERVICE_DONE_NOT_DECLARED()->getValue(),
            'changingStateDate' => $todayDate
        ]);
    }

    /**
     * @param array $rawData
     * @param DataProviders $dataProvider
     * @return string
     */
    public function generateRegistrationFolderId(array $rawData, DataProviders $dataProvider): string
    {
        $prefix = self::getDataProviderPrefix($dataProvider->getValue());
        return uniqid($prefix);
    }


    /**
     * @param RegistrationFolder $registrationFolder
     * @return RegistrationFolder|null
     */
    public function refreshRegistrationFolder(RegistrationFolder $registrationFolder): ?RegistrationFolder
    {
        return $registrationFolder;
    }

    /* ------------------------------------------------------------------------------------------------------------------------------------------------*/
    /* ------------------------------------------------------------------------------------------------------------------------------------------------*/


    /**
     * @param RegistrationFolder $registrationFolder
     * @param array $data
     * @return RegistrationFolder|null
     * @throws ErrorException
     * @throws ORMException
     * @throws ThrowableAlias
     */
    public function updateRegistrationFolder(RegistrationFolder $registrationFolder, array $data): RegistrationFolder
    {
        return $this->registrationFolderService->createOrUpdate($data, DataProviders::from($registrationFolder->getType()), $registrationFolder->getExternalId());
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @param string $oFBillNumber
     * @param float|null $vatRate
     * @return RegistrationFolder
     * @throws ErrorException
     * @throws ORMException
     * @throws ThrowableAlias
     */
    public function billRegistrationFolder(RegistrationFolder $registrationFolder, string $oFBillNumber, float $vatRate = null): RegistrationFolder
    {
        $rawData = array_merge($registrationFolder->getRawData(), [
            '__billingRawData' => [
                'billNumber' => $oFBillNumber
            ],
            'currentBillingState' => 'billed',
            'billingDate' => (new DateTime('now'))->format('Y-m-d\TH:i:s.v\Z')
        ]);
        return $this->registrationFolderService->createOrUpdate($rawData, DataProviders::from($registrationFolder->getType()), $registrationFolder->getExternalId());
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @return RegistrationFolder
     * @throws ErrorException
     * @throws ORMException
     * @throws ThrowableAlias
     */
    public function paidRegistrationFolder(RegistrationFolder $registrationFolder): RegistrationFolder
    {
        $rawData = array_merge($registrationFolder->getRawData(), [
            'currentBillingState' => RegistrationFolderBillingStates::PAID()->getValue()
        ]);
        return $this->registrationFolderService->createOrUpdate($rawData, DataProviders::from($registrationFolder->getType()), $registrationFolder->getExternalId());
    }

    public function retrieveRegistrationFolder(string $externalId, Organism $organism): ?RegistrationFolder
    {
        return null;
    }

    //-------------------
    // METHODES PRIVEES
    //-------------------

    /**
     * @param RegistrationFolder $registrationFolder
     * @return RegistrationFolder
     * @throws ErrorException
     * @throws ORMException
     * @throws ThrowableAlias
     */
    private function validateServiceDoneRegistrationFolder(RegistrationFolder $registrationFolder): RegistrationFolder
    {
        $rawData = $this->addStateToRawData($registrationFolder->getRawData(), "Service fait validé", RegistrationFolderStates::SERVICE_DONE_VALIDATED(), $registrationFolder->getOrganism());
        $rawData['currentBillingState'] = RegistrationFolderBillingStates::TO_BILL()->getValue();
        return $this->registrationFolderService->createOrUpdate($rawData, DataProviders::from($registrationFolder->getType()), $registrationFolder->getExternalId());
    }

    /**
     * @return array|null
     * @throws ErrorException
     * @throws ORMException
     * @throws ThrowableAlias
     * @throws WedofConnectionException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws OptimisticLockException
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    public function getProdLog(): ?array
    {
        return $this->sendRequest(Tools::getEnvValue('AUTOMATOR_BASE_URI') . 'get-prod-log-zip', [
            'headers' => [
                "x-auth" => Tools::getEnvValue('AUTOMATOR_AUTH'),
                "content-type" => 'application/zip',
            ],
            'fullResponse' => true,
        ]);
    }

    /**
     * @param Organism $organism
     * @param null $state
     * @param null $sort
     * @param array $options
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function getRegistrationFoldersCount(Organism $organism, $state = null, $sort = null, array $options = array()): int
    {
        return $this->registrationFolderService->countByOrganismAndDataProvider($organism, $this->dataProvider);
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @return string
     */
    public function getRegistrationFolderExternalLink(RegistrationFolder $registrationFolder): string
    {
        return '';
    }

    public function getRegistrationFolderRawData(Organism $organism, string $registrationFolderId, array $registrationFolderDataToMerge = null, string $overrideCurrentBillingState = null): ?array
    {
        return null;
    }

    public function getTrainingRawData(Organism $organism, string $trainingId, array $options = []): ?array
    {
        return null;
    }

    public function getTrainingsRawData(Organism $organism, array $trainingStatuses = null): ?array
    {
        return null;
    }

    public function getTrainingActionRawData(Training $training, string $trainingActionId): ?array
    {
        return null;
    }

    public function getTrainingActionsRawData(Training $training): ?array
    {
        return null;
    }

    public function getSessionsRawData(TrainingAction $trainingAction): ?array
    {
        return null;
    }

    public function getSessionRawData(TrainingAction $trainingAction, string $sessionId): ?array
    {
        return null;
    }

    public function updateEvaluationsForTrainingAction(TrainingAction $trainingAction): void
    {
        // TODO: Implement updateEvaluationsForTrainingAction() method.
    }

    public function getTrainingExternalId(string $trainingId): string
    {
        return $trainingId;
    }

    public function getTrainingActionExternalId(string $trainingId, string $trainingActionId): string
    {
        return $trainingId . "/" . $trainingActionId; // At the moment Internal relies on EDOF trainings
    }

    public function getSessionExternalId(string $trainingId, string $trainingActionId, string $sessionId): string
    {
        return $trainingId . "/" . $trainingActionId . "/" . $sessionId; // At the moment Internal relies on EDOF trainings
    }

    public function getTrainingExtIdFromTrainingActionExtId(string $externalId): ?string
    {
        return '';
    }

    public function getTrainingExtIdFromSessionExtId(string $externalId): ?string
    {
        return '';
    }

    public function getShortTrainingActionExtId(string $externalId): ?string
    {
        return '';
    }

    public function retrieveCertificationFromTrainingRawData(array $trainingRawData): ?Certification
    {
        return null;
    }

    public function getTrainingActionExternalLink(TrainingAction $trainingAction): string
    {
        return '';
    }

    public function getTrainingState(array $rawData, ?Certification $certification): TrainingStates
    {
        return !empty($rawData['statusLabel']) ? TrainingStates::from($rawData['statusLabel']) : TrainingStates::PUBLISHED();
    }

    public function getTrainingActionState(array $rawData, Training $training): TrainingActionStates
    {
        return !empty($rawData['statusLabel']) ? TrainingActionStates::from($rawData['statusLabel']) : TrainingActionStates::PUBLISHED();
    }

    public function getSessionState(array $rawData, TrainingAction $trainingAction): SessionStates
    {
        return !empty($rawData['statusLabel']) ? SessionStates::from($rawData['statusLabel']) : SessionStates::PUBLISHED();
    }

    public function getSiretFromExternalId(string $externalId): string
    {
        return '';
    }

    public function getTrainingRawDataFromRawData(array $registrationFolderRawData): array
    {
        return [];
    }

    public function getTrainingActionRawDataFromRawData(array $registrationFolderRawData): array
    {
        return [];
    }

    public function getSessionRawDataFromRawData(array $registrationFolderRawData): array
    {
        return [];
    }

    public function getActionsRulesForRegistrationFolder(): array
    {
        return [
            RegistrationFolderStates::NOT_PROCESSED()->getValue() => [
                RegistrationFolderStates::VALIDATED()->getValue() => [], //empty array necessary
                RegistrationFolderStates::REFUSED_BY_ORGANISM()->getValue() => [],
            ],
            RegistrationFolderStates::VALIDATED()->getValue() => [
                RegistrationFolderStates::ACCEPTED()->getValue() => [],
                RegistrationFolderStates::WAITING_ACCEPTATION()->getValue() => [],
                RegistrationFolderStates::REFUSED_BY_ATTENDEE()->getValue() => [],
                RegistrationFolderStates::REJECTED_WITHOUT_TITULAIRE_SUITE()->getValue() => []
            ],
            RegistrationFolderStates::WAITING_ACCEPTATION()->getValue() => [
                RegistrationFolderStates::ACCEPTED()->getValue() => [],
                RegistrationFolderStates::VALIDATED()->getValue() => [],
            ],
            RegistrationFolderStates::ACCEPTED()->getValue() => [
                RegistrationFolderStates::IN_TRAINING()->getValue() => [],
                RegistrationFolderStates::CANCELED_BY_ORGANISM()->getValue() => [],
                RegistrationFolderStates::CANCELED_BY_ATTENDEE_NOT_REALIZED()->getValue() => []
            ],
            RegistrationFolderStates::IN_TRAINING()->getValue() => [
                RegistrationFolderStates::TERMINATED()->getValue() => [],
                RegistrationFolderStates::SERVICE_DONE_DECLARED()->getValue() => []
            ],
            RegistrationFolderStates::TERMINATED()->getValue() => [
                RegistrationFolderStates::SERVICE_DONE_DECLARED()->getValue() => []
            ],
            RegistrationFolderStates::SERVICE_DONE_DECLARED()->getValue() => [
                RegistrationFolderStates::SERVICE_DONE_VALIDATED()->getValue() => [],
            ],
            RegistrationFolderStates::SERVICE_DONE_VALIDATED()->getValue() => [
                RegistrationFolderStates::TO_BILL()->getValue() => ['billable'],
                RegistrationFolderBillingStates::PAID()->getValue() => ['isBilledState']
            ],
            RegistrationFolderStates::CANCELED_BY_ATTENDEE()->getValue() => [],
            RegistrationFolderStates::CANCELED_BY_ATTENDEE_NOT_REALIZED()->getValue() => [],
            RegistrationFolderStates::CANCELED_BY_ORGANISM()->getValue() => [],
            RegistrationFolderStates::CANCELED_BY_FINANCER()->getValue() => [],
            RegistrationFolderStates::REFUSED_BY_ATTENDEE()->getValue() => [],
            RegistrationFolderStates::REFUSED_BY_ORGANISM()->getValue() => [],
            RegistrationFolderStates::REFUSED_BY_FINANCER()->getValue() => [],
            RegistrationFolderStates::REJECTED_WITHOUT_TITULAIRE_SUITE()->getValue() => []
        ];
    }

    public function retrieveCertificationFromRawData(array $registrationFolderRawData): ?Certification
    {
        return null;
    }
}
