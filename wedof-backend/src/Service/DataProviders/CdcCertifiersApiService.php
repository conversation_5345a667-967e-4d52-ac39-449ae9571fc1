<?php

namespace App\Service\DataProviders;

use App\Entity\Connection;
use App\Entity\Organism;
use App\Entity\User;
use App\Event\MonitoringEvents;
use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofCdcBackendException;
use App\Exception\WedofConnectionException;
use App\Library\utils\enums\ConnectionStates;
use App\Library\utils\enums\ConnectionTypes;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\Tools;
use App\Repository\EndPointStatusRepository;
use App\Service\BrowserlessService;
use App\Service\ConfigService;
use App\Service\ConnectionService;
use App\Service\OrganismService;
use DateTime;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use ErrorException;
use Exception;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Security\Core\Security;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Throwable;
use Vich\UploaderBundle\Storage\StorageInterface;

//WIP
class CdcCertifiersApiService extends BaseApiService
{
    const BROWSERLESS_LOGIN_SCRIPT = "module.exports=async({page,context})=>{let steps=['init'];let getTokenData=async function(page){const client=await page.target().createCDPSession();await client.send('Network.clearBrowserCookies');await client.send('Network.clearBrowserCache');steps.push('clear');await page.goto('https://certificateurs.moncompteformation.gouv.fr/idp/oauth/pm5/authorize?response_type=code&client_id=66b0f244-c2d0-414b-842b-505f75891a40&state=bHM3UWI5V1pEYU4uRkFVLklILmtFN1F6amswOWhHMGF3ZDItdC5sUDdod29Y&redirect_uri=https%3A%2F%2Fcertificateurs.moncompteformation.gouv.fr%2Fespace-prive&scope=openid%20profile%20PM5_READ%20PM5_WRITE%20PC5_READ%20PC5_WRITE&code_challenge=_sFNG90Orgu5nLT9JC-kpil9jsX7jvEYUfDVH2ls-xI&code_challenge_method=S256&nonce=bHM3UWI5V1pEYU4uRkFVLklILmtFN1F6amswOWhHMGF3ZDItdC5sUDdod29Y',{waitUntil:'domcontentloaded'});steps.push('goto');var token=null;page.on('response',async(response)=>{if(response.url()=='https://certificateurs.moncompteformation.gouv.fr/idp/oauth/pm5/token'){token=await response.json()}});await page.waitForSelector('#mat-input-0', {timeout:5000});await page.type('#mat-input-0',context.username);steps.push('password');await page.type('#mat-input-1',context.password);await page.click('form button[type=submit]');await page.waitForTimeout(5000);if(token){return{data:token,type:'application/json'}}else{try{await page.waitForSelector('.mat-simple-snackbar > span');let errorMessage=await(await(await page.$('.mat-simple-snackbar > span')).getProperty('textContent')).jsonValue();steps.push('error');return{data:{error:errorMessage.toString().replace(/[\\t\\n\\r]/gm,''),steps:steps.join(' - ')},type:'application/json'}}catch(e){return false}}};steps.push('token');let tokenData=await getTokenData(page);if(tokenData){return tokenData}else if(tokenData===false){steps.push(await page.url());steps.push('token 2');tokenData=await getTokenData(page);}if(tokenData){return tokenData}else{try{steps.push(await page.url());let screenshot=null;screenshot=await page.screenshot({encoding:'base64',fullPage:true}).then(function(data){let base64Encode='data:image/png;base64,'+data;return base64Encode});steps.push('screenshot');return{data:{error:'Erreur inconnue',steps:steps.join(' - '),screenshot:screenshot,screenshotName:context.username},type:'application/json'}}catch(e){return{data:{error:e,steps:steps.join(' - ')},type:'application/json'}}}};";
    private BrowserlessService $browserlessService;
    private OrganismService $organismService;
    private StorageInterface $storage;

    /**
     * cdcCertifiers constructor.
     * @param EventDispatcherInterface $dispatcher
     * @param ConfigService $configService
     * @param RequestStack $requestStack
     * @param LoggerInterface $logger
     * @param BrowserlessService $browserlessService
     * @param EndPointStatusRepository $endPointStatusRepository
     * @param ConnectionService $connectionService
     * @param OrganismService $organismService
     * @param StorageInterface $storage
     * @param Security $security
     */
    public function __construct(EventDispatcherInterface $dispatcher,
                                ConfigService            $configService,
                                RequestStack             $requestStack,
                                LoggerInterface          $logger,
                                BrowserlessService       $browserlessService,
                                EndPointStatusRepository $endPointStatusRepository,
                                ConnectionService        $connectionService,
                                OrganismService          $organismService,
                                StorageInterface         $storage,
                                Security                 $security)
    {
        parent::__construct(DataProviders::CDC_CERTIFIERS(),
            $configService, $connectionService, $requestStack,
            $endPointStatusRepository, $dispatcher, $logger, $security);
        $this->browserlessService = $browserlessService;
        $this->organismService = $organismService;
        $this->storage = $storage;
    }

    /**
     * @param LoggerInterface $logger
     */
    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    /**
     * @param $organismOrToken
     * @return array|null
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function getUserInfos($organismOrToken): ?array
    {
        $CDC_Request_URL = Tools::getEnvValue("CDCCERTIFIERS_BASE_URI") . '/idp/oauth/pm5/user_info';
        $options = [
            'cat_api_status' => "private-profile",
            "access_token" => is_string($organismOrToken) ? $organismOrToken : null,
            "organism" => !is_string($organismOrToken) ? $organismOrToken : null
        ];
        return $this->sendRequest($CDC_Request_URL, $options);
    }

    /**
     * @param $organismOrToken
     * @return array|null
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function getCertificateurs($organismOrToken): ?array
    {
        $CDC_Request_URL = Tools::getEnvValue("CDCCERTIFIERS_BASE_URI") . '/certif-api/PC5/v1/organisme/deposant/certificateurs';
        $options = [
            'cat_api_status' => "private-profile",
            "access_token" => is_string($organismOrToken) ? $organismOrToken : null,
            "organism" => !is_string($organismOrToken) ? $organismOrToken : null
        ];
        // returns
        //  "bcr": "03VKRXXX",
        //  "designation": "KAGILUM",
        //  "numeroContrat": "MCFCER000XXX"
        return $this->sendRequest($CDC_Request_URL, $options);
    }

    /**
     * @param Organism $organism
     * @param array $params
     * @return array|null
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    private function processDelegationDocuments(Organism $organism, array $params): ?array
    {
        ini_set('max_execution_time', 300);
        $connection = $organism->getConnectionForDataProvider(DataProviders::CDC_CERTIFIERS());
        if (isset($params['logo']) && $params['logo'] instanceof UploadedFile) {
            $this->organismService->createOrUpdate(['logo' => $params['logo']], $organism);
        }
        $files = [
            'kbis' => $params['kbis'],
            'logo' => $this->storage->resolvePath($organism, 'logoFile')
        ];
        $options = [
            'authenticationHeaderName' => Tools::getEnvValue('AUTOMATOR_AUTH_HEADER'),
            'authenticationHeaderValue' => Tools::getEnvValue('AUTOMATOR_AUTH'),
            'fullResponse' => true,
            'parameters' => [
                "json" => json_encode([
                    'siret' => $organism->getSiret(),
                    'organism' => $organism->getName(),
                    'referentLastName' => $params['lastName'],
                    'referentFirstName' => $params['firstName'],
                    'referentEmail' => $params['email'],
                    'shortName' => $params['shortName'] ?? null,
                    'commercialName' => $params['commercialName'] ?? null,
                    'folderId' => $connection->getCredentials()['folderId']
                ])
            ],
            'method' => 'POST',
            'cat_api_status' => 'cdc-certifiers-delegation',
            'files' => $files
        ];
        return $this->sendRequest(Tools::getEnvValue('AUTOMATOR_BASE_URI') . 'send-delegation-cdc-certifiers', $options);
    }

    /**
     * @throws ORMException
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     * @throws WedofConnectionException
     * @throws OptimisticLockException
     * @throws TransportExceptionInterface
     * @throws Throwable
     * @throws ServerExceptionInterface
     * @throws NonUniqueResultException
     * @throws NoResultException
     * @throws ErrorException
     */
    public function generateDelegationDocument(Organism $organism, User $user, array $params, bool $preview = true): ?array
    {
        if ($organism->getAccrochageDelegationDate()) {
            throw new WedofBadRequestHttpException("Erreur, l'accrochage automatique est déjà activé pour votre organisme.");
        }
        $subscription = $organism->getSubscription();
        if (!$subscription->isAllowCertifiers() && !$subscription->isAllowCertifierPlus()) {
            throw new WedofBadRequestHttpException("Erreur, votre abonnement ne permet pas l'accès à cette fonctionnalité.");
        }
        $options = [
            'authenticationHeaderName' => Tools::getEnvValue('AUTOMATOR_AUTH_HEADER'),
            'authenticationHeaderValue' => Tools::getEnvValue('AUTOMATOR_AUTH'),
            'fullResponse' => true,
            'parameters' => [
                "owner" => [
                    "firstName" => $params['ownerFirstName'] ?? $user->getFirstName(),
                    "lastName" => $params['ownerLastName'] ?? $user->getLastName(),
                    "email" => $params['ownerEmail'] ?? $user->getEmail()
                ],
                'siret' => $organism->getSiret(),
                'organism' => $organism->getName(),
            ],
            'method' => 'POST',
            'cat_api_status' => 'cdc-certifiers-delegation'
        ];
        $workflow = $preview ? 'preview-delegation-document' : 'sign-delegation-document';
        $result = $this->sendRequest(Tools::getEnvValue('AUTOMATOR_BASE_URI') . $workflow, $options);
        //update connection
        $connection = $organism->getConnectionForDataProvider(DataProviders::CDC_CERTIFIERS()) ?? $this->connectionService->create($organism, DataProviders::CDC_CERTIFIERS());
        $connection->setState(ConnectionStates::IN_PROGRESS());
        $connection->setMessageType("info");
        $connection->setMessage("Délégation d'accrochage en cours de création");
        $connection->setCredentials([
            "folderId" => $preview ? null : $result['headers']["drive-folder-id"][0],
            "params" => $params
        ]);
        $this->connectionService->save($connection);
        return $result;
    }

    /**
     * @throws ORMException
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     * @throws WedofConnectionException
     * @throws OptimisticLockException
     * @throws TransportExceptionInterface
     * @throws Throwable
     * @throws ServerExceptionInterface
     * @throws NonUniqueResultException
     * @throws NoResultException
     * @throws ErrorException
     */
    public function showDelegationDocument(Organism $organism): ?array
    {
        if (!$organism->getAccrochageDelegationDate()) {
            throw new WedofBadRequestHttpException("Erreur, l'accrochage automatique n'est activé pour votre organisme.");
        }
        $subscription = $organism->getSubscription();
        if (!$subscription->isAllowCertifiers() && !$subscription->isAllowCertifierPlus()) {
            throw new WedofBadRequestHttpException("Erreur, votre abonnement ne permet pas l'accès à cette fonctionnalité.");
        }
        $options = [
            'authenticationHeaderName' => Tools::getEnvValue('AUTOMATOR_AUTH_HEADER'),
            'authenticationHeaderValue' => Tools::getEnvValue('AUTOMATOR_AUTH'),
            'fullResponse' => true,
            'parameters' => [
                'siret' => $organism->getSiret(),
            ],
            'method' => 'POST',
            'cat_api_status' => 'cdc-certifiers-delegation'
        ];
        return $this->sendRequest(Tools::getEnvValue('AUTOMATOR_BASE_URI') . 'show-delegation-document', $options);
    }

    public function getMaxAttemptsBeforeStop(): int
    {
        return 3;
    }

    /**
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function authenticate(Connection $connection, bool $checkOrganismAccess = true): array
    {
        $organism = $connection->getOrganism();
        $connection = $organism->getConnectionForDataProvider($this->dataProvider);
        $oldCredentials = $connection->getCredentials();

        $data = $this->browserlessService->callFunction(self::BROWSERLESS_LOGIN_SCRIPT, $connection->getCredentials());
        if (isset($data['access_token'])) {
            $dataUserInfo = $this->getUserInfos($data['access_token']);
            $isWedofAccount = $oldCredentials['username'] === Tools::getEnvValue('CDCCERTIFIERS_WEDOF_USERNAME');
            if ($isWedofAccount || $dataUserInfo['urn:spc:siret'] === $organism->getSiret()) {
                if (!$isWedofAccount) {
                    $organism->setCdcClientId($dataUserInfo['urn:spc:pr_id']);
                } else {
                    $bcrCertificateurs = $this->getCertificateurs($data['access_token']);
                    $found = array_search(trim($organism->getName()), array_column($bcrCertificateurs, 'designation'));
                    if ($found !== false) {
                        $organism->setCdcClientId($bcrCertificateurs[$found]['bcr']);
                    }
                }
                $credentials = [
                    'type' => $isWedofAccount ? ConnectionTypes::HABILITATION()->getValue() : ConnectionTypes::DELEGATION()->getValue(),
                    'username' => $oldCredentials['username'],
                    'password' => $oldCredentials['password'],
                    'access_token' => $data['access_token'],
                    'tokenData' => $data,
                ];
                $expireOn = new DateTime();
                $expireOn->setTimestamp(strtotime('+' . $data['expires_in'] . ' second'));
                $credentials['tokenExpireOn'] = $expireOn->format('Y-m-d H:i:s');
                $connection->setCredentials($credentials);
                return [
                    'type' => $isWedofAccount ? ConnectionTypes::HABILITATION()->getValue() : ConnectionTypes::DELEGATION()->getValue(),
                    'username' => $connection->getCredentials()['username'],
                    'password' => $connection->getCredentials()['password'],
                    'access_token' => $data['access_token'],
                    'tokenData' => $data,
                    "hasAccess" => true,
                    "hasOrganismAccess" => true,
                    "lastRefresh" => new DateTime(),
                    "refreshAt" => new DateTime($credentials['tokenExpireOn']),
                    "expiresOn" => new DateTime($credentials['tokenExpireOn'])
                ];
            } else {
                return ['hasAccess' => true, 'hasOrganismAccess' => false, 'error' => 'wrong-siret'];
            }
        } else if (isset($data['error']) && $data['error'] === "Mauvais identifiant / mot de passe.") {
            return ['hasAccess' => false, 'hasOrganismAccess' => false, 'error' => 'credentials'];
        } else {
            $e = new WedofCdcBackendException($data['trace'] ?? ('CdcCertifiersApiService:authenticate ' . $organism->getSiret()));
            $this->dispatcher->dispatch(new MonitoringEvents($e), MonitoringEvents::API_PROVIDER_ERROR);
            return ['hasAccess' => false, 'hasOrganismAccess' => false, 'exception' => $e];
        }
    }

    /**
     * @param Organism $organism
     * @param array|null $params
     * @return string
     */
    public function checkBeforeHabilitate(Organism $organism, array $params = null): string
    {
        if (!isset($params) || !isset($params['kbis']) || !isset($params['logo']) || empty($params['email']) || empty($params['lastName']) || empty($params['firstName'])) {
            return "form";
        } else {
            return "sign";
        }
    }

    /**
     * @throws ORMException
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     * @throws WedofConnectionException
     * @throws OptimisticLockException
     * @throws TransportExceptionInterface
     * @throws Throwable
     * @throws ServerExceptionInterface
     * @throws NonUniqueResultException
     * @throws NoResultException
     * @throws ErrorException
     */
    public function habilitate(Organism $organism, Connection $connection, array $params): bool
    {
        $connection = $organism->getConnectionForDataProvider(DataProviders::CDC_CERTIFIERS());
        $connection->setState(ConnectionStates::IN_PROGRESS());
        $connection->setMessageType("info");
        $connection->setMessage("Délégation en cours d'envoi");
        $this->connectionService->save($connection);
        $result = $this->processDelegationDocuments($organism, $params);
        if (isset($result['content']['success'])) {
            $date = new DateTime();
            $organism->setAccrochageDelegationDate($date);
            $connection->setMessage("Délégation envoyé le " . $date->format("d/m/y H:i"));
            $connection->setLastRefresh($date);
            $connection->setRefreshAt(null);
            $connection->setFailedAt(null);
            $connection->setIsTosValidated(true);
            $credentials = $connection->getCredentials();
            $credentials['sentAt'] = $result['content']['sentAt'];
            $connection->setCredentials($credentials);
            $this->connectionService->save($connection);
            return true;
        } else {
            $connection->setState(ConnectionStates::FAILED());
            $connection->setMessageType("error");
            $connection->setMessage("Erreur lors de l'envoi de la délégation");
            $connection->setFailedAt(new DateTime());
            return false;
        }
    }

    public function getUsername(Connection $connection): string
    {
        return $connection->getCredentials()['username'] ?? '';
    }

    public function requiresAuthentication(): bool
    {
        return true;
    }

    public function setActiveOrganism(Organism $organism): bool
    {
        return false;
    }

    /**
     * @throws Exception|\Symfony\Component\Mailer\Exception\TransportExceptionInterface
     */
    public function setAuthentication(array $options = []): array
    {
        if ($options['organism']) {
            /** @var Organism $organism */
            $organism = $options['organism'];
            $connection = $organism->getConnectionForDataProvider($this->dataProvider);
            if ($this->connectionService->hasAccess($organism, $this->dataProvider)) {
                $credentials = $connection->getCredentials();
                $options['access_token'] = $credentials['access_token'];
            }
        }
        return $options;
    }

    /**
     * @throws RedirectionExceptionInterface
     * @throws WedofCdcBackendException
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    public function getCdcFilesRawDataPaginated(Organism $organism, bool $isWedofDeposant = false, array $parameters = array(), array $options = array()): array
    {
        $options = array_merge([
            "requestDispatchMonitoringEvent" => true,
            "onlyItems" => true,
            "maxRetry" => Tools::getEnvValue('CDCCERTIFIERS_API_MAX_RETRY')
        ], $options);

        $limit = $parameters['limit'] ?? 10;
        $skip = $parameters['skip'] ?? 1;
        $order = $parameters['order'] ?? "DESC"; // DESC OR ASC
        $sort = $parameters['sort'] ?? "DATE_DEPOT"; //DATE_DEPOT or STATUT_TRAITEMENT or NOM_FICHIER_PARTENAIRE

        $_options = [
            'parameters' => $parameters,
            'cat_api_status' => "private-files",
            "dispatchMonitoringEvent" => $options['requestDispatchMonitoringEvent'],
            "maxRetry" => $options['maxRetry'],
            "organism" => $organism,
        ];
        $idClient = $isWedofDeposant ? Tools::getEnvValue('WEDOF_ID_CLIENT') : $organism->getCdcClientId();
        $CDC_Request_URL = Tools::getEnvValue("CDCCERTIFIERS_BASE_URI") . "/certif-api/PC5/v1/fichiers?bcrOrganisme=" . $idClient . "&sortBy=$sort&sortOrder=$order&page=$skip&pageSize=$limit";
        $filesRawData = $this->sendRequest($CDC_Request_URL, $_options);

        if ($options['onlyItems']) {
            $filesRawData = $filesRawData['fichiers'];
        }
        return $filesRawData;
    }

    /**
     * @param int $fileId
     * @param Organism $organism
     * @param array $options
     * @return array|string|true[]|null
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    public function getConfirmationReceiptFile(int $fileId, Organism $organism, array $options = []): ?array
    {
        $options = array_merge(["requestDispatchMonitoringEvent" => true, "maxRetry" => Tools::getEnvValue('CDCCERTIFIERS_API_MAX_RETRY')], $options);
        $_options = [
            'cat_api_status' => "get-receipt-file-url",
            "organism" => $organism,
            'content-type' => 'text/plain',
            "dispatchMonitoringEvent" => $options['requestDispatchMonitoringEvent'],
            "maxRetry" => $options['maxRetry'],
            'fullResponse' => true,
        ];
        $url = Tools::getEnvValue('CDCCERTIFIERS_BASE_URI') . "/certif-api/PC5/v1/fichiers/$fileId/accuse-reception/url";
        $fileUrl = $this->sendRequest($url, $_options)['content'];
        //get file
        $_options = [
            'cat_api_status' => "get-receipt-file",
            "organism" => $organism,
            'useDownload' => true,
            'content-type' => 'text/plain',
            "dispatchMonitoringEvent" => $options['requestDispatchMonitoringEvent'],
            "maxRetry" => $options['maxRetry']
        ];
        return $this->sendRequest($fileUrl, $_options);
    }

    /**
     * @param int $fileId
     * @param Organism $organism
     * @param array $options
     * @return array|null
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function getProcessedReceiptFile(int $fileId, Organism $organism, array $options = []): ?array
    {
        $options = array_merge(["requestDispatchMonitoringEvent" => true, "maxRetry" => Tools::getEnvValue('CDCCERTIFIERS_API_MAX_RETRY')], $options);
        $_options = [
            'cat_api_status' => "get-processed-receipt-file-url",
            "organism" => $organism,
            'content-type' => 'text/plain',
            "dispatchMonitoringEvent" => $options['requestDispatchMonitoringEvent'] ?? true,
            "maxRetry" => $options['maxRetry'],
            'fullResponse' => true,
        ];
        $url = Tools::getEnvValue('CDCCERTIFIERS_BASE_URI') . "/certif-api/PC5/v1/traitements/$fileId/accuse-traitement/url";
        $fileUrl = $this->sendRequest($url, $_options)['content'];
        //get file
        $_options = [
            'cat_api_status' => "get-processed-receipt-file",
            "organism" => $organism,
            'useDownload' => true,
            'content-type' => 'text/plain',
            "dispatchMonitoringEvent" => $options['requestDispatchMonitoringEvent'],
            "maxRetry" => $options['maxRetry']
        ];
        return $this->sendRequest($fileUrl, $_options);
    }

    /**
     * @throws ORMException
     * @throws ClientExceptionInterface
     * @throws OptimisticLockException
     * @throws Throwable
     * @throws ServerExceptionInterface
     * @throws NonUniqueResultException
     * @throws RedirectionExceptionInterface
     * @throws WedofConnectionException
     * @throws TransportExceptionInterface
     * @throws NoResultException
     * @throws ErrorException
     */
    public function sendCdcXMLFile(Organism $organism, string $fileName, string $filePath, array $options = []): bool
    {
        $options = array_merge([
            "requestDispatchMonitoringEvent" => true,
        ], $options);

        $_options = [
            'parameters' => [
                "bcrCertificateur" => $organism->getCdcClientId(),
                "nomFichier" => $fileName
            ],
            "method" => "POST",
            'cat_api_status' => "prepare-upload-file",
            "dispatchMonitoringEvent" => $options['requestDispatchMonitoringEvent'],
            "maxRetry" => 0,
            "fullResponse" => true,
            "organism" => $organism,
        ];
        $result = $this->sendRequest(Tools::getEnvValue('CDCCERTIFIERS_BASE_URI') . "/certif-api/PC5/v1/fichiers", $_options);
        if (isset($result['headers']['x-url-to-upload-file'][0])) {
            $uploadUrl = $result['headers']['x-url-to-upload-file'][0];
            $this->logger->debug($uploadUrl);
            $options = [
                'content-type' => 'text/xml',
                "fullResponse" => true,
                'parameters' => [
                    "body" => file_get_contents($filePath, FILE_USE_INCLUDE_PATH),
                ],
                'cat_api_status' => "upload-xml-file",
                "method" => "PUT"
            ];
            $result = $this->sendRequest($uploadUrl, $options);
            if (!isset($result['statusCode']) || $result['statusCode'] !== 200) {
                throw new WedofCdcBackendException("Erreur lors du depot du fichier XML CDC");
            } else {
                return true;
            }
        } else {
            throw new WedofCdcBackendException("Erreur lors de la préparation du depot du fichier XML CDC");
        }
    }
}