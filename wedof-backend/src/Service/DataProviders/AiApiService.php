<?php

namespace App\Service\DataProviders;

use App\Entity\Certification;
use App\Entity\Connection;
use App\Entity\Organism;
use App\Exception\WedofConflictHttpException;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\Tools;
use App\Repository\EndPointStatusRepository;
use App\Service\ConfigService;
use App\Service\ConnectionService;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Security\Core\Security;
use Throwable;

class AiApiService extends BaseApiService
{
    private string $baseUri;

    /**
     * @param ConfigService $configService
     * @param ConnectionService $connectionService
     * @param RequestStack $requestStack
     * @param EndPointStatusRepository $endPointStatusRepository
     * @param EventDispatcherInterface $dispatcher
     * @param LoggerInterface $logger
     * @param Security $security
     */
    public function __construct(
        ConfigService            $configService,
        ConnectionService        $connectionService,
        RequestStack             $requestStack,
        EndPointStatusRepository $endPointStatusRepository,
        EventDispatcherInterface $dispatcher,
        LoggerInterface          $logger,
        Security                 $security
    )
    {
        parent::__construct(
            DataProviders::AI(),
            $configService,
            $connectionService,
            $requestStack,
            $endPointStatusRepository,
            $dispatcher,
            $logger,
            $security
        );
        $this->baseUri = Tools::getEnvValue('KASTORR_BASE_URI');
    }

    /**
     * @param Certification $certification
     * @return array
     */
    public function getTrainingTitles(Certification $certification): array
    {
        $endpoint = 'getTitles';
        $url = $this->baseUri . $endpoint;
        try {
            $options = [
                'authenticationHeaderName' => $_ENV['KASTORR_AUTH_HEADER'],
                'authenticationHeaderValue' => $_ENV['KASTORR_AUTH'],
                'method' => 'POST',
                'parameters' => [
                    'certificationId' => $certification->getId()
                ],
            ];
            $data = $this->sendRequest($url, $options);
            if (isset($data['titles'])) {
                return $data['titles'];
            }
            return [];
        } catch (Throwable $e) {
            $this->logger->error("[AiApiService] Error getting training titles: " . $e->getMessage());
            return [];
        }
    }

    /**
     * @param array $trainingTitles
     * @param array $trainingData
     * @return array|bool|null
     */
    public function evaluateTrainingTitleWithAI(array $trainingTitles, array $trainingData)
    {
        if (empty($trainingData)) {
            return true;
        }
        try {
            $examples = [];
            foreach ($trainingData as $data) {
                if (isset($data['title'], $data['isConform'], $data['justification'])) {
                    $examples[] = [
                        'title' => $data['title'],
                        'answer' => $data['isConform'],
                        'justification' => $data['justification']
                    ];
                }
            }
            $endpoint = 'evaluateTitles';
            $url = $this->baseUri . $endpoint;
            $this->logger->info("[AiApiService] Calling AI evaluation API: $url with " . count($trainingTitles) . " training titles");
            $options = [
                'authenticationHeaderName' => $_ENV['KASTORR_AUTH_HEADER'],
                'authenticationHeaderValue' => $_ENV['KASTORR_AUTH'],
                'method' => 'POST',
                'parameters' => [
                    'exemples' => $examples,
                    'user-input' => $trainingTitles
                ],
            ];
            $data = $this->sendRequest($url, $options);
            if (isset($data['evaluations'])) {
                return $data['evaluations'];
            }
            $this->logger->warning("[AiApiService] Invalid response from AI evaluation API");
            return null; // Non applicable
        } catch (Throwable $e) {
            $this->logger->error("[AiApiService] Error evaluating training titles with AI: " . $e->getMessage());
            return null; // Non applicable
        }
    }

    /// BASE API SERVICE STUFF

    public function getMaxAttemptsBeforeStop(): int
    {
        return 3;
    }

    public function authenticate(Connection $connection, bool $checkOrganismAccess = true): array
    {
        throw new WedofConflictHttpException("Pas d'implémentation de la fonction authenticate dans AiApiService");
    }

    public function checkBeforeHabilitate(Organism $organism, array $params = null)
    {
    }

    public function habilitate(Organism $organism, Connection $connection, array $params): bool
    {
        return true;
    }

    public function getUsername(Connection $connection): string
    {
        return '';
    }

    public function requiresAuthentication(): bool
    {
        return false;
    }

    public function setActiveOrganism(Organism $organism): bool
    {
        return true;
    }

    public function setAuthentication(array $options = []): array
    {
        return [];
    }
}
