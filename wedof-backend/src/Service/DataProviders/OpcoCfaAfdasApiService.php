<?php

namespace App\Service\DataProviders;

use App\Entity\Organism;
use App\Exception\WedofConnectionException;
use App\Library\utils\enums\DataProviders;
use App\Repository\EndPointStatusRepository;
use App\Repository\UserRepository;
use App\Service\ConfigService;
use App\Service\ConnectionService;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use ErrorException;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Security\Core\Security;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Throwable;

class OpcoCfaAfdasApiService extends OpcoCfaDockApiService
{
    /**
     * @param ConnectionService $connectionService
     * @param RequestStack $requestStack
     * @param ConfigService $configService
     * @param EventDispatcherInterface $dispatcher
     * @param LoggerInterface $logger
     * @param EndPointStatusRepository $endPointStatusRepository
     * @param Security $security
     * @param UserRepository $userRepository
     */
    public function __construct(ConnectionService        $connectionService,
                                RequestStack             $requestStack,
                                ConfigService            $configService,
                                EventDispatcherInterface $dispatcher,
                                LoggerInterface          $logger,
                                EndPointStatusRepository $endPointStatusRepository,
                                Security                 $security,
                                UserRepository $userRepository
    )
    {
        parent::__construct(DataProviders::OPCO_CFA_AFDAS(), $configService, $connectionService, $requestStack, $endPointStatusRepository, $dispatcher, $logger, $security, $userRepository);
    }

    /**
     * @param Organism $organism
     * @param array|null $params
     * @return array
     */
    public function checkBeforeHabilitate(Organism $organism, array $params = null): array
    {
        return []; // See https://www.cfadock.fr/portail_developpeur#/tabs/implementations "Accrochage CFA key Info"
    }

    /**
     * @param $options
     * @return bool
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    protected function testAuthentication($options): bool
    {
        $endpoint = self::ENDPOINT_DOSSIERS_ETATS_V1; // No better endpoint available for afdas
        $response = $this->sendRequest(self::getEnvApiConfig($this->dataProvider, 'BASE_URI') . $endpoint, $options); // random endpoint to check auth
        return $response['statusCode'] < 300 || $this->isFakeErrorNoData($response);
    }

    // Because https://api-cfa.afdas.com/v1/status returns 404
    public function status(bool $needsAuth = false): array
    {
        return [
            'status' => self::STATUS_HEALTHY
        ];
    }

    /**
     * @param Organism $organism
     * @return array
     * @throws ClientExceptionInterface
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     * @throws ErrorException
     * @throws Throwable
     */
    public function listRawFolderStates(Organism $organism): array
    {
        $options = [
            'organism' => $organism,
            'cat_api_status' => 'contracts',
            'fullResponse' => true,
        ];
        $response = $this->sendRequest(self::getEnvApiConfig($this->dataProvider, 'BASE_URI') . self::ENDPOINT_DOSSIERS_ETATS_V1, $options);
        if ($response['statusCode'] < 300) {
            return json_decode($response['content'], true);
        } else if ($this->isFakeErrorNoData($response)) {
            return []; // Force return empty instead of error
        } else {
            throw new WedofConnectionException('Error OPCO ' . $this->dataProvider->getValue() . ' organism ' . $organism->getSiret() . ' listRawFolderStates ' . $response['statusCode'] . ' ' . $response['content']);
        }
    }

    /**
     * The API may return an error instead of 200 when result is empty
     * @param array $response
     * @return bool
     */
    private function isFakeErrorNoData(array $response): bool
    {
        $isFakeError = false;
        if (isset($response['content'])) {
            $content = json_decode($response['content'], true);
            $errorNoFolder = 'aucun dossier';
            $isFakeError = isset($content['erreurs'][0]['description']) && str_contains($content['erreurs'][0]['description'], $errorNoFolder);
        }
        return $isFakeError;
    }
}
