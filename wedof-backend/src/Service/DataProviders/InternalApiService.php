<?php

namespace App\Service\DataProviders;

use App\Entity\Connection;
use App\Entity\Organism;
use App\Exception\WedofConflictHttpException;
use App\Library\utils\enums\DataProviders;
use App\Repository\EndPointStatusRepository;
use App\Service\ConfigService;
use App\Service\ConnectionService;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Security\Core\Security;

class InternalApiService extends BaseApiService
{
    public function __construct(ConfigService             $configService,
                                ConnectionService         $connectionService,
                                RequestStack              $requestStack,
                                EndPointStatusRepository  $endPointStatusRepository,
                                EventDispatcherInterface  $dispatcher,
                                LoggerInterface           $logger,
                                Security                  $security)
    {
        parent::__construct(DataProviders::INTERNAL(), $configService, $connectionService, $requestStack, $endPointStatusRepository, $dispatcher, $logger, $security);
    }

    /**
     * @param Connection $connection
     * @param bool $checkOrganismAccess
     * @return true[]
     */
    public function authenticate(Connection $connection, bool $checkOrganismAccess = true): array
    {
        throw new WedofConflictHttpException("Pas d'implémentation de la fonction authenticate dans InternalApiService");
    }

    /**
     * @param array $options
     * @return array
     */
    public function setAuthentication(array $options = []): array
    {
        return $options;
    }

    /**
     * @param Connection $connection
     * @return string
     */
    public function getUsername(Connection $connection): string
    {
        return '';
    }

    /**
     * @return bool
     */
    public function requiresAuthentication(): bool
    {
        return false;
    }

    /**
     * @param Organism $organism
     * @return bool
     */
    public function setActiveOrganism(Organism $organism): bool
    {
        return true;
    }

    /**
     * @return int
     */
    public function getMaxAttemptsBeforeStop(): int
    {
        return 1; //not used
    }

    /**
     * @param Organism $organism
     * @param Connection $connection
     * @param array $params
     * @return bool
     */
    public function habilitate(Organism $organism, Connection $connection, array $params): bool
    {
        return false;
    }

    /**
     * @param Organism $organism
     * @param array|null $params
     * @return false
     */
    public function checkBeforeHabilitate(Organism $organism, array $params = null): bool
    {
        return false;
    }
}