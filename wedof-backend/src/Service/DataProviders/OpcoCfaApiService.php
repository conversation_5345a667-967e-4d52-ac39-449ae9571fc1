<?php


namespace App\Service\DataProviders;

use App\Entity\Attendee;
use App\Entity\Certification;
use App\Entity\Organism;
use App\Entity\RegistrationFolder;
use App\Entity\Session;
use App\Entity\WorkingContract;
use App\Exception\WedofBadRequestHttpException;
use App\Library\utils\enums\AttendeeGender;
use App\Library\utils\enums\CertificationTypes;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\enums\RegistrationFolderAttendeeStates;
use App\Library\utils\enums\RegistrationFolderBillingStates;
use App\Library\utils\enums\RegistrationFolderStates;
use App\Library\utils\enums\TrainingStates;
use App\Library\utils\enums\WorkingContractStates;
use App\Library\utils\Tools;
use App\Repository\EndPointStatusRepository;
use App\Repository\RegistrationFolderRepository;
use App\Repository\SessionRepository;
use App\Service\AttendeeService;
use App\Service\CertificationService;
use App\Service\CityService;
use App\Service\ConfigService;
use App\Service\ConnectionService;
use App\Service\RegistrationFolderService;
use App\Service\SessionService;
use App\Service\TrainingActionService;
use App\Service\TrainingService;
use DateTime;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use ErrorException;
use Exception;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Security\Core\Security;
use Throwable;

/**
 * RF service for type OPCO_CFA
 * Does the mapping between WorkingContract and RF
 * + shell around InternalApiService for local management of RF
 */
class OpcoCfaApiService extends RegistrationFolderInternalApiService
{
    const EXTERNAL_ID_PREFIX = 'CFA-';

    private AttendeeService $attendeeService;
    private CertificationService $certificationService;
    private TrainingService $trainingService;
    private TrainingActionService $trainingActionService;
    private SessionService $sessionService;
    private CityService $cityService;
    private RegistrationFolderRepository $registrationFolderRepository;
    private SessionRepository $sessionRepository;

    /**
     * @param ConfigService $configService
     * @param ConnectionService $connectionService
     * @param RequestStack $requestStack
     * @param EndPointStatusRepository $endPointStatusRepository
     * @param EventDispatcherInterface $dispatcher
     * @param LoggerInterface $logger
     * @param Security $security
     * @param RegistrationFolderService $registrationFolderService
     * @param AttendeeService $attendeeService
     * @param CertificationService $certificationService
     * @param TrainingService $trainingService
     * @param TrainingActionService $trainingActionService
     * @param SessionService $sessionService
     * @param CityService $cityService
     * @param RegistrationFolderRepository $registrationFolderRepository
     * @param SessionRepository $sessionRepository
     */
    public function __construct(ConfigService                $configService,
                                ConnectionService            $connectionService,
                                RequestStack                 $requestStack,
                                EndPointStatusRepository     $endPointStatusRepository,
                                EventDispatcherInterface     $dispatcher,
                                LoggerInterface              $logger,
                                Security                     $security,
                                RegistrationFolderService    $registrationFolderService,
                                AttendeeService              $attendeeService,
                                CertificationService         $certificationService,
                                TrainingService              $trainingService,
                                TrainingActionService        $trainingActionService,
                                SessionService               $sessionService,
                                CityService $cityService,
                                RegistrationFolderRepository $registrationFolderRepository,
                                SessionRepository            $sessionRepository)
    {
        parent::__construct($configService, $connectionService, $requestStack, $endPointStatusRepository, $dispatcher, $logger, $security, $registrationFolderService);
        $this->attendeeService = $attendeeService;
        $this->certificationService = $certificationService;
        $this->trainingService = $trainingService;
        $this->trainingActionService = $trainingActionService;
        $this->sessionService = $sessionService;
        $this->cityService = $cityService;
        $this->registrationFolderRepository = $registrationFolderRepository;
        $this->sessionRepository = $sessionRepository;
    }

    //-------------------
    // METHODES HERITEES
    //-------------------

    /**
     * @param array $rawData
     * @param DataProviders $dataProvider
     * @return string
     */
    public function generateRegistrationFolderId(array $rawData, DataProviders $dataProvider): string
    {
        return uniqid(self::EXTERNAL_ID_PREFIX);
    }

    /**
     * @param Session $session
     * @param array $rawData
     * @param Organism $organism
     * @return array
     */
    public function preCreateRegistrationFolder(Session $session, array $rawData, Organism $organism): array
    {
        $sessionId = $session->getExternalId();
        $trainingAction = $session->getTrainingAction();
        $trainingActionId = $trainingAction->getExternalId();
        $training = $trainingAction->getTraining();
        $trainingId = $training->getExternalId();
        $todayDate = (new DateTime('now'))->format('Y-m-d\TH:i:s.v\Z');
        $vat = $organism->getVat();
        return array_merge($rawData, [
            'trainingActionInfo' => [
                'totalIncl' => $rawData['totalTTC'],
                'totalExcl' => Tools::convertTtcToHt($rawData['totalTTC'], $organism),
                'vat' => $vat === 0.0 || $vat === null ? $rawData['totalTTC'] : null,
                'vatExclTax5' => $vat === 5.5 ? Tools::convertTtcToHt($rawData['totalTTC'], $organism) : null,
                'vatInclTax5' => $vat === 5.5 ? $rawData['totalTTC'] : null,
                'vatExclTax20' => $vat === 20.0 ? Tools::convertTtcToHt($rawData['totalTTC'], $organism) : null,
                'vatInclTax20' => $vat === 20.0 ? $rawData['totalTTC'] : null,
                'indicativeDuration' => null,
                'sessionStartDate' => null,
                'sessionEndDate' => null,
                'sessionId' => $sessionId,
                'title' => $training->getTitle(),
                'address' => null,
                'trainingGoal' => null,
                'content' => null,
                'expectedResult' => null,
                'weeklyDuration' => null,
                'additionalFees' => null,
                'trainingPaces' => null,
                'teachingModalities' => null,
                'trainingCompletionRate' => null
            ],
            'history' => [
                [
                    "date" => $todayDate,
                    "label" => "Création du dossier",
                    "author" => $organism->getOwnedBy()->getEmail()
                ]
            ],
            'states' => [
                'notProcessed' => [
                    'date' => $todayDate
                ]
            ],
            'trainingId' => $trainingId,
            'trainingActionId' => $trainingActionId,
            'currentState' => RegistrationFolderStates::NOT_PROCESSED()->getValue(),
            'currentBillingState' => RegistrationFolderBillingStates::NOT_BILLABLE()->getValue(),
            'currentAttendeeState' => RegistrationFolderAttendeeStates::SERVICE_DONE_NOT_DECLARED()->getValue(),
            'changingStateDate' => $todayDate
        ]);
    }

    //-------------------
    // METHODES OPCO PUBLIQUES
    //-------------------

    /**
     * @param array $rawFolder
     * @param Organism $organism
     * @return RegistrationFolder|null
     * @throws Exception|Throwable
     */
    public function createOrFindRegistrationFolderFromRawFolder(array $rawFolder, Organism $organism): ?RegistrationFolder
    {
        $certification = $this->findCertification($rawFolder);
        if (!$certification) {
            throw new WedofBadRequestHttpException('Certification not found');
        }
        $session = $this->findSession($organism, $certification);
        if (empty($session)) {
            $session = $this->createSession($organism, $certification);
        }
        $attendee = $this->createOrUpdateAttendee($rawFolder); // TODO(opco): maybe better find using e.g. birthName / birthDate etc
        $registrationFolder = $this->findRegistrationFolder($attendee, $session);
        if (empty($registrationFolder)) {
            $registrationFolder = $this->createRegistrationFolder($rawFolder, $session, $organism, $attendee);
        }
        return $registrationFolder;
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @param WorkingContract $workingContract
     * @return RegistrationFolder
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws Throwable
     */
    public function updateRegistrationFolderFromWorkingContract(RegistrationFolder $registrationFolder, WorkingContract $workingContract): RegistrationFolder
    {
        if ($this->shouldBeCurrentContract($registrationFolder, $workingContract)) {
            $registrationFolder->setCurrentWorkingContract($workingContract);
            $registrationFolder = $this->registrationFolderService->save($registrationFolder);
        }
        // Gather Data
        $workingContractTrainingDates = $this->getTrainingDates($workingContract->getRawData());
        $sessionStartDate = $workingContractTrainingDates['startDate'];
        $sessionEndDate = $workingContractTrainingDates['endDate'];
        $sessionStartDateString = $sessionStartDate->format('d/m/Y');
        $sessionEndDateString = $sessionEndDate->format('d/m/Y');
        $shouldBeAccepted = false;
        if (in_array($workingContract->getState(), [WorkingContractStates::ACCEPTED(), WorkingContractStates::COMPLETED(), WorkingContractStates::BROKEN()])) {
            $shouldBeAccepted = true;
        }
        // Update data
        $rawData = $registrationFolder->getRawData();
        $trainingActionInfo = $rawData['trainingActionInfo'];
        $needsToAccept = $shouldBeAccepted && in_array($registrationFolder->getState(), [RegistrationFolderStates::NOT_PROCESSED(), RegistrationFolderStates::WAITING_ACCEPTATION(), RegistrationFolderStates::VALIDATED()]);
        $needsToUpdateDates = false;
        if ($trainingActionInfo['sessionStartDate'] != $sessionStartDateString || $trainingActionInfo['sessionEndDate'] != $sessionEndDateString) {
            $trainingActionInfo['sessionStartDate'] = $sessionStartDateString;
            $trainingActionInfo['sessionEndDate'] = $sessionEndDateString;
            $trainingActionInfo['indicativeDuration'] = Tools::getNumberWeekDays($sessionStartDate, $sessionEndDate) * 7;
            $needsToUpdateDates = true;
        }
        if ($needsToUpdateDates || $needsToAccept) {
            if ($needsToUpdateDates) {
                $rawData['trainingActionInfo'] = $trainingActionInfo;
            }
            if ($needsToAccept) {
                $rawData['currentState'] = RegistrationFolderStates::ACCEPTED();
            }
            $registrationFolder = $this->registrationFolderService->createOrUpdate($rawData, DataProviders::OPCO_CFA(), $registrationFolder->getExternalId());
        }
        return $registrationFolder;
    }

    //-------------------
    // METHODES OPCO PRIVEES
    //-------------------

    /**
     * @param $rawFolder
     * @return Certification|null
     */
    private function findCertification($rawFolder): ?Certification
    {
        $fullCode = $rawFolder['cerfa']['formation']['rncp'];
        $type = CertificationTypes::RNCP();
        $code = Tools::removePrefix($fullCode, $type->getValue());
        return $this->certificationService->getByTypeAndCode($type, $code); // TODO(opco): manage duplicates (take the active first?)
    }

    /**
     * @param Organism $organism
     * @param Certification $certification
     * @return Session|null
     * @throws NonUniqueResultException
     */
    private function findSession(Organism $organism, Certification $certification): ?Session
    {
        return $this->sessionRepository->findOneByCertificationAndDataProvider($organism, $certification, DataProviders::OPCO_CFA());
    }

    /**
     * @param Organism $organism
     * @param Certification $certification
     * @return Session
     * @throws ErrorException
     * @throws ORMException
     * @throws Throwable
     * @throws OptimisticLockException
     */
    private function createSession(Organism $organism, Certification $certification): Session
    {
        $trainingTitle = $certification->getName();
        $id = DataProviders::OPCO_CFA()->getValue() . '-' . $certification->getExternalId();
        $statusCode = '0';
        $statusLabel = TrainingStates::PUBLISHED()->getValue(); // Try to be the same as CPF
        $totalTvaTTC = 0; // As it is shared among all aprentices, it make no sense to have a price there
        $trainingRawData = [
            'id' => $id,
            'title' => $trainingTitle,
            'externalId' => $id,
            'trainingId' => $id,
            'statusCode' => $statusCode,
            'statusLabel' => $statusLabel,
            'dataProvider' => DataProviders::OPCO_CFA()->getValue(),
            'certifInfo' => $certification->getCertifInfo(),
            'type' => $certification->getType(),
            'code' => $certification->getCode(),
            'codeReconnaissance' => $certification->getExternalId()
        ];
        $training = $this->trainingService->createOrUpdate($trainingRawData, $organism, $certification);
        $trainingActionRawData = [
            'id' => $id,
            'externalId' => $id,
            'trainingId' => $id,
            'totalTvaTTC' => $totalTvaTTC,
            'statusCode' => $statusCode,
            'statusLabel' => $statusLabel,
            'averageLearningTime' => null
        ];
        $trainingAction = $this->trainingActionService->createOrUpdate($trainingActionRawData, $training);
        $sessionRawData = [
            'id' => $id,
            'externalId' => $id,
            'trainingId' => $id,
            'actionId' => $id,
            'endDate' => null,
            'beginDate' => null,
            'statusCode' => $statusCode,
            'statusLabel' => $statusLabel,
        ];
        return $this->sessionService->createOrUpdate($sessionRawData, $trainingAction);
    }

    /**
     * @param array $rawFolder
     * @return Attendee
     * @throws Exception
     */
    private function createOrUpdateAttendee(array $rawFolder): Attendee
    {
        $apprentiRawData = $rawFolder['cerfa']['apprenti'];
        $attendeeRawData = $this->getAttendeeRawData($apprentiRawData);
        return $this->attendeeService->createOrUpdate($attendeeRawData);
    }

    private function findRegistrationFolder(Attendee $attendee, Session $session): ?RegistrationFolder
    {
        return $this->registrationFolderRepository->findOneBy([
            'attendee' => $attendee,
            'session' => $session
        ]);
    }

    /**
     * @param array $rawFolder
     * @param $session
     * @param $organism
     * @param $attendee
     * @return void
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws Throwable
     * @throws \Doctrine\ORM\ORMException
     */
    private function createRegistrationFolder(array $rawFolder, $session, $organism, $attendee): RegistrationFolder
    {
        $rawData = [
            'totalTTC' => 0
        ];
        return $this->registrationFolderService->createOrUpdate($rawData, DataProviders::OPCO_CFA(), null, $session, $organism, $attendee);
    }

    /**
     * @param array $apprentiRawData
     * @return array
     * @throws Exception
     */
    private function getAttendeeRawData(array $apprentiRawData): array
    {
        $rawData = [];
        $rawData['birthName'] = $apprentiRawData['nom'];
        $rawData['lastName'] = !empty($apprentiRawData['nomUsage']) ? $apprentiRawData['nomUsage'] : $rawData['birthName'];
        $rawData['firstName'] = $apprentiRawData['prenom'];
        $rawData['dateOfBirth'] = Tools::createDateFromString($apprentiRawData["dateNaissance"]);
        $city = $this->cityService->getByNameAndDepartement($apprentiRawData['communeNaissance'], $apprentiRawData['departementNaissance']);
        if ($city) {
            $rawData['codeCityOfBirth'] = $city->getCog();
            $rawData['nameCityOfBirth'] = $city->getName();
        }
        // TODO(opco) diplome
        $rawData['displayName'] = $rawData['lastName'] . '.' . $rawData['firstName']; // TODO(opco) WTF is displayName ?
        $rawData['email'] = $apprentiRawData['courriel'] ?? null;
        $phoneNumber = $apprentiRawData['telephone'] ?? null;
        $rawData["phoneNumber"] = (Tools::startsWith($phoneNumber, '06') || Tools::startsWith($phoneNumber, '07')) ? $phoneNumber : null;
        $rawData["phoneFixed"] = empty($rawData["phoneNumber"]) ? $phoneNumber : null;

        $rawData["registrationFolders"] = null;
        $rawData["certificationFolders"] = null;
        $rawData["poleEmploiId"] = null;
        $rawData["poleEmploiRegionCode"] = null;
        $rawData["readOnly"] = false;
        $rawData["gender"] = $apprentiRawData["sexe"] === 'M' ? AttendeeGender::MALE()->getValue() : AttendeeGender::FEMALE()->getValue();
        $rawData["externalId"] = null;
        $adresseData = $apprentiRawData['adresse'];
        $rawData["address"] = [
            "number" => "",
            "roadName" => $adresseData["adresse1"] ?? null, // TODO(opco): split number and manage adresse2
            "zipcode" => $adresseData["codePostal"] ?? null,
            "city" => $adresseData["commune"] ?? null,
        ];

        return $rawData;
    }

    /**
     * @param array $rawFolder
     * @return array
     * @throws Exception
     */
    private function getTrainingDates(array $rawFolder): array
    {
        $rawCerfa = $rawFolder['cerfa'];
        $rawFormation = $rawCerfa['formation'];
        $rawContrat = $rawCerfa['contrat'];
        $dateDebutFormation = new DateTime($rawFormation['dateDebutFormation']);
        $dateDebutContrat = new DateTime($rawContrat['dateDebutContrat']);
        $dateFinFormation = new DateTime($rawFormation['dateFinFormation']);
        $dateFinContrat = new DateTime($rawContrat['dateFinContrat']);
        return [
            'startDate' => $dateDebutContrat->getTimestamp() < $dateDebutFormation->getTimestamp() ? $dateDebutContrat : $dateDebutFormation,
            'endDate' => $dateFinContrat->getTimestamp() > $dateFinFormation->getTimestamp() ? $dateFinContrat : $dateFinFormation
        ];
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @param WorkingContract $candidateWorkingContract
     * @return bool
     */
    private function shouldBeCurrentContract(RegistrationFolder $registrationFolder, WorkingContract $candidateWorkingContract): bool
    {
        $currentWorkingContract = $registrationFolder->getCurrentWorkingContract();
        if ($currentWorkingContract === $candidateWorkingContract) {
            return false;
        }
        if (empty($currentWorkingContract)) {
            return true;
        }
        if (in_array($candidateWorkingContract, [WorkingContractStates::DRAFT(), WorkingContractStates::REFUSED()])) {
            return false;
        }
        return $candidateWorkingContract->getStartDate() > $currentWorkingContract->getStartDate();
    }
}