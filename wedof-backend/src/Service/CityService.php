<?php

namespace App\Service;

use App\Entity\City;
use App\Repository\CityRepository;
use Doctrine\Common\Collections\ArrayCollection;

class CityService
{
    private CityRepository $cityRepository;

    public function __construct(CityRepository $cityRepository)
    {
        $this->cityRepository = $cityRepository;
    }

    /**
     * @param string $name
     * @return City|null
     */
    public function getByName(string $name): ?City
    {
        return $this->cityRepository->findOneByName($this->computeName($name));
    }

    /**
     * @param string $name
     * @param string $departement
     * @return City|null
     */
    public function getByNameAndDepartement(string $name, string $departement): ?City
    {
        return $this->cityRepository->findOneByNameAndDepartement($this->computeName($name), $departement);
    }

    /**
     * @param string $cog
     * @return City|null
     */
    public function getByCOG(string $cog): ?City
    {
        return $this->cityRepository->findOneBy(['cog' => $cog]);
    }

    /**
     * @param $postalCode $cog
     * @return City|null
     */
    public function getByPostalCode(string $postalCode): ?City
    {
        return $this->cityRepository->findOneBy(['postalCode' => $postalCode]);
    }

    /**
     * @param string $name
     * @return ArrayCollection
     */
    public function list(string $name): ArrayCollection
    {
        return $this->cityRepository->findAllByName($name);
    }

    /**
     * @param string $name
     * @return string
     */
    private function computeName(string $name): string
    {
        if (str_contains($name, 'ARRONDISSEMEN')) { // sans le T final car il arrive que le mot soit tronqué
            $cityName = explode(' ', $name)[0];
            $arrondissement = explode(' ', $name)[1];
            return $cityName . ' ' . $arrondissement . ($arrondissement === '1' ? 'er' : 'e') . ' Arrondissement';
        } else {
            return $name;
        }
    }
}