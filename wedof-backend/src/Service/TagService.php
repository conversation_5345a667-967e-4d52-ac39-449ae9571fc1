<?php
// src/Service/TagService.php
namespace App\Service;

use App\Entity\Organism;
use App\Repository\TagRepository;
use Doctrine\ORM\QueryBuilder;

class TagService
{
    private TagRepository $tagRepository;

    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    public function __construct(TagRepository $tagRepository)
    {
        $this->tagRepository = $tagRepository;
    }

    /**
     * @param Organism $organism
     * @param array $parameters
     * @return QueryBuilder
     */
    public function listReturnQueryBuilder(Organism $organism, array $parameters): QueryBuilder
    {
        return $this->tagRepository->findAllReturnQueryBuilder($organism, $parameters);
    }
}
