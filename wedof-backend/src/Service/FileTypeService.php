<?php
// src/Service/FileTypeService.php
namespace App\Service;

use App\Entity\Certification;
use App\Entity\CertificationFolder;
use App\Entity\CertificationFolderFile;
use App\Entity\CertificationPartner;
use App\Entity\CertificationPartnerAudit;
use App\Entity\CertificationPartnerAuditFile;
use App\Entity\CertificationPartnerFile;
use App\Entity\Organism;
use App\Entity\RegistrationFolder;
use App\Entity\RegistrationFolderFile;
use App\Entity\User;
use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofConnectionException;
use App\Library\utils\Dictionary;
use App\Library\utils\enums\CertificationFolderStates;
use App\Library\utils\enums\CertificationPartnerStates;
use App\Library\utils\enums\RegistrationFolderStates;
use App\Library\utils\Tools;
use App\Repository\CertificationFolderFileRepository;
use App\Repository\CertificationPartnerFileRepository;
use App\Repository\RegistrationFolderFileRepository;
use App\Service\DataProviders\AutomatorApiService;
use Doctrine\DBAL\Driver\Exception;
use Doctrine\ORM\Exception\NotSupported;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use ErrorException;
use FilesystemIterator;
use Psr\Cache\InvalidArgumentException;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\ContainerInterface;
use Psr\Container\NotFoundExceptionInterface;
use Symfony\Component\Cache\Adapter\FilesystemAdapter;
use Symfony\Component\HttpFoundation\File\File;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Contracts\Cache\ItemInterface;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Throwable;

class FileTypeService
{
    const REGISTRATION_FOLDER = 'registrationFolderFileTypes';
    const CERTIFICATION_FOLDER = 'certificationFolderFileTypes';
    const CERTIFICATION_PARTNER = 'certificationPartnerFileTypes';
    const CERTIFICATION_PARTNER_AUDIT = 'certificationPartnerAuditFileTypes';
    const CERTIFICATION = 'certificationFileTypes';

    private CertificationService $certificationService;
    private OrganismService $organismService;
    private CertificationFolderFileRepository $certificationFolderFileRepository;
    private RegistrationFolderFileRepository $registrationFolderFileRepository;
    private CertificationPartnerFileRepository $certificationPartnerFileRepository;
    private AutomatorApiService $automatorApiService;
    private ContainerInterface $container;

    public function __construct(CertificationService $certificationService, CertificationFolderFileRepository $certificationFolderFileRepository, RegistrationFolderFileRepository $registrationFolderFileRepository, CertificationPartnerFileRepository $certificationPartnerFileRepository, OrganismService $organismService, AutomatorApiService $automatorApiService, ContainerInterface $container)
    {
        $this->certificationService = $certificationService;
        $this->organismService = $organismService;
        $this->certificationFolderFileRepository = $certificationFolderFileRepository;
        $this->registrationFolderFileRepository = $registrationFolderFileRepository;
        $this->certificationPartnerFileRepository = $certificationPartnerFileRepository;
        $this->automatorApiService = $automatorApiService;
        $this->container = $container;
    }

    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    /**
     * @param object $entity
     * @param array $body
     * @param string $field
     * @param Organism $organism
     * @return array
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws WedofConnectionException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws ErrorException
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     * @throws Throwable
     */
    public function create(object $entity, array $body, string $field, Organism $organism): array
    {
        $getMethodName = "get" . ucwords($field);
        $setMethodName = "set" . ucwords($field);

        $fileTypes = $entity->{$getMethodName}() ?? [];
        $maxTypeIdFromFiles = 0;
        if ($entity instanceof Certification) {
            if ($field === self::CERTIFICATION_FOLDER) {
                $maxTypeIdFromFiles = $this->certificationFolderFileRepository->findMaxTypeId($entity);
            } else if ($field === self::CERTIFICATION_PARTNER) {
                $maxTypeIdFromFiles = $this->certificationPartnerFileRepository->findMaxTypeId($entity);
            }
        } else if ($entity instanceof Organism && $field === self::REGISTRATION_FOLDER) {
            $maxTypeIdFromFiles = $this->registrationFolderFileRepository->findMaxTypeId($entity);
        }

        if ($entity instanceof Certification && $field === self::CERTIFICATION_FOLDER) {
            $fileTypeWithoutCertificate = array_filter($fileTypes, function ($fileType) {
                return $fileType['id'] !== Certification::CERTIFICATE_FILE_TYPE_ID;
            });
            $maxTypeIdFromFileTypes = $fileTypes && count($fileTypeWithoutCertificate) >= 1 ? end($fileTypeWithoutCertificate)['id'] : 0;
        } else {
            $maxTypeIdFromFileTypes = $fileTypes ? end($fileTypes)['id'] : 0;
        }
        $maxTypeId = max($maxTypeIdFromFiles, $maxTypeIdFromFileTypes);
        $newFileType = [
            'id' => $maxTypeId + 1,
            'name' => $body['name'],
            'accept' => $body['accept'],
            'toState' => $body['toState'] ?? null,
            'description' => $body['description'] ?? null,
            'qualiopiIndicators' => $body['qualiopiIndicators'] ?? null,
            'allowMultiple' => $body['allowMultiple'] ?? false,
            'tags' => $body['tags'] ?? null,
            'certifications' => $body['certifications'] ?? null,
        ];
        if ($field === self::CERTIFICATION_FOLDER || $field === self::REGISTRATION_FOLDER) {
            $newFileType['allowVisibilityAttendee'] = $body['allowVisibilityAttendee'];
            $newFileType['allowUploadAttendee'] = $body['allowUploadAttendee'];
            if (isset($body['allowSignAttendee'])) {
                $newFileType['allowSignAttendee'] = $body['allowSignAttendee'];
            }
        }
        if ($field === self::CERTIFICATION_PARTNER || $field === self::CERTIFICATION_FOLDER) {
            $newFileType['allowVisibilityPartner'] = $body['allowVisibilityPartner'];
            $newFileType['allowUploadPartner'] = $body['allowUploadPartner'];
            if (isset($body['allowSignPartner'])) {
                $newFileType['allowSignPartner'] = $body['allowSignPartner'];
            }
        }

        $generated = $body['generated'];
        $newFileType['generated'] = $generated;
        $newFileType['allowRegenerate'] = $generated;

        if ($generated === true) {
            $newFileType['enabled'] = true;
            if ($entity instanceof Certification) {
                $newFileType['allowUploadPartner'] = false;
            }
            if ($field === self::CERTIFICATION_FOLDER || $field === self::REGISTRATION_FOLDER) {
                $newFileType['allowUploadAttendee'] = false;
            }
            if (isset($body['templateFile'])) {
                $generatedTemplate = $this->generateDocumentTemplate($entity, $organism, $newFileType['id'], $body['templateFile'], $field);
                $newFileType['templateFile'] = $generatedTemplate['name'];
                $newFileType["googleId"] = $generatedTemplate['googleId'];
            } else if (isset($body['googleId'])) {
                $fileName = $organism->getSiret() . '_' . $entity::CLASSNAME . '_' . $field . '_' . $entity->getId() . '_' . $newFileType['id'];
                $generatedTemplate = $this->createOrUpdateTemplate(null, $body['googleId'], $fileName);
                $newFileType['templateFile'] = $generatedTemplate['name'];
                $newFileType["googleId"] = $body['googleId'];
            }
        }

        $fileTypes[] = $newFileType;
        $entity->{$setMethodName}($fileTypes);
        if ($entity instanceof Certification) {
            $this->certificationService->save($entity);
        } else if ($entity instanceof Organism) {
            $this->organismService->save($entity);
        }

        return $newFileType;
    }

    /**
     * @param object $entity
     * @param array $body
     * @param string $field
     * @param int $fileTypeIndex
     * @param array $fileType
     * @param Organism $organism
     * @return array
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function update(object $entity, array $body, string $field, int $fileTypeIndex, array $fileType, Organism $organism): array
    {

        $getMethodName = "get" . ucwords($field);
        $setMethodName = "set" . ucwords($field);

        $fileTypes = $entity->{$getMethodName}();
        $properties = ['name', 'description', 'toState', 'accept', 'qualiopiIndicators', 'tags', 'certifications'];

        if ($field === self::REGISTRATION_FOLDER) {
            $fileTypePropertiesToAdd = ['allowVisibilityAttendee', 'allowUploadAttendee', 'allowSignAttendee', 'allowRegenerate', 'enabled'];
            $properties = array_merge($properties, $fileTypePropertiesToAdd);
        } else if ($field === self::CERTIFICATION_FOLDER) {
            $fileTypePropertiesToAdd = ['allowVisibilityAttendee', 'allowUploadAttendee', 'allowSignAttendee', 'allowRegenerate', 'enabled', 'allowVisibilityPartner', 'allowUploadPartner', 'allowSignPartner'];
            $properties = array_merge($properties, $fileTypePropertiesToAdd);
        } else if ($field === self::CERTIFICATION_PARTNER) {
            $fileTypePropertiesToAdd = ['allowRegenerate', 'enabled', 'allowVisibilityPartner', 'allowUploadPartner', 'allowSignPartner'];
            $properties = array_merge($properties, $fileTypePropertiesToAdd);
        }

        foreach ($properties as $property) {
            if (key_exists($property, $body)) {
                $fileType[$property] = $body[$property];
            }
        }

        $generated = !empty($fileType['generated']);
        if ($generated) {
            if ($entity instanceof Certification) {
                $fileType['allowUploadPartner'] = false;
            }
            if ($field === self::REGISTRATION_FOLDER || $field === self::CERTIFICATION_FOLDER) {
                $fileType['allowUploadAttendee'] = false;
            }
            if (isset($body['templateFile']) && $body['templateFile'] instanceof File) {
                $generatedTemplate = $this->generateDocumentTemplate($entity, $organism, $fileType['id'], $body['templateFile'], $field);
                $fileType['templateFile'] = $generatedTemplate['name']; // Shouldn't have changed, but just in case...
                $fileType["googleId"] = $generatedTemplate['googleId']; // Shouldn't have changed, but just in case...
            }
        }

        $fileTypes[$fileTypeIndex] = $fileType;
        $entity->{$setMethodName}($fileTypes);

        if ($entity instanceof Certification) {
            $this->certificationService->save($entity);
        } else if ($entity instanceof Organism) {
            $this->organismService->save($entity);
        }
        return $entity->{$getMethodName}()[$fileTypeIndex]; // Ensure that we return what has been saved
    }

    /**
     * @param object $entity
     * @param int $idFile
     * @param string $field
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function delete(object $entity, int $idFile, string $field): void
    {
        $getMethodName = "get" . ucwords($field);
        $setMethodName = "set" . ucwords($field);

        $fileTypes = $entity->{$getMethodName}();
        $newFileTypes = [];
        foreach ($fileTypes as $fileType) {
            if ($fileType['id'] !== $idFile) {
                $newFileTypes[] = $fileType;
            } else if (!empty($fileType['generated'])) {
                $response = $this->automatorApiService->deleteDocumentTemplate('template-document-delete', $fileType['googleId']);
                if (empty($response['statusCode']) || $response['statusCode'] != 200) {
                    throw new WedofBadRequestHttpException(json_decode($response['content'], true));
                }
            }

        }
        $entity->{$setMethodName}($newFileTypes);
        if ($entity instanceof Certification) {
            $this->certificationService->save($entity);
        } else if ($entity instanceof Organism) {
            $this->organismService->save($entity);
        }
    }

    /**
     * @param $entity
     * @param array $fileType
     * @param array $additonalContext
     * @param Organism|null $mainOrganismFromEntity
     * @param string $targetFileType
     * @return array|null
     * @throws ClientExceptionInterface
     * @throws ContainerExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws NotFoundExceptionInterface
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     * @throws Exception
     * @throws \Doctrine\DBAL\Exception
     * @throws NotSupported
     */
    public function generateDocumentFromTemplateAndEntity($entity, array $fileType, array $additonalContext = [], Organism $mainOrganismFromEntity = null, string $targetFileType = 'pdf'): ?array
    {
        $container = $this->container;
        $computedContext = Dictionary::getComputedContext($entity, null, null, null, $container, false, 'document', $mainOrganismFromEntity);
        $computedContext = array_merge($computedContext, $additonalContext);
        return $this->automatorApiService->generateDocumentFromTemplateAndContext($fileType, $computedContext, $targetFileType);
    }

    /**
     * @param RegistrationFolder|CertificationFolder|CertificationPartner $entity
     * @param array $fileType
     * @param string $action
     * @param User|null $user
     * @param string $targetEntityClass
     * @return CertificationFolderFile|CertificationPartnerAuditFile|CertificationPartnerFile|RegistrationFolderFile
     * @throws ClientExceptionInterface
     * @throws ContainerExceptionInterface
     * @throws ErrorException
     * @throws Exception
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws NotFoundExceptionInterface
     * @throws NotSupported
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     * @throws \Doctrine\DBAL\Exception
     */
    public function manualGenerateDocumentFromTemplateAndEntity($entity, array $fileType, string $action, ?User $user, string $targetEntityClass)
    {
        /** @var RegistrationFolderFileService|CertificationFolderFileService|CertificationPartnerFileService $targetEntityFileService */
        $entityFileService = $this->container->get('App\Service\\' . $targetEntityClass . 'FileService');
        /** @var RegistrationFolderService|CertificationFolderService|CertificationPartnerService $targetEntityService */
        $entityService = $this->container->get('App\Service\\' . $targetEntityClass . 'Service');

        $existingFile = $entityFileService->getByTypeId($entity, $fileType['id']);
        if ($existingFile && $action === 'generate' && empty($fileType['allowMultiple'])) {
            throw new WedofBadRequestHttpException("Erreur, l'action 'generate' n'est pas possible car un fichier existe déjà.");
        }

        if ($fileType['toState']) {
            $toState = $fileType['toState'];
            $entityState = $entity->getState();
            if ($entity instanceof CertificationFolder) {
                $allowState = CertificationFolderStates::isForwardTransition($toState, CertificationFolderStates::from($entityState));
            } else if ($entity instanceof RegistrationFolder) {
                $allowState = RegistrationFolderStates::isForwardTransition($toState, RegistrationFolderStates::from($entityState));
            } else {
                $allowState = CertificationPartnerStates::isForwardTransition($toState, CertificationPartnerStates::from($entityState));
            }
            if (!$allowState && $entityState !== $toState) {
                throw new WedofBadRequestHttpException("Erreur, le dossier n'est pas dans le bon état pour pouvoir être généré.");
            }
        }

        $fileName = null;
        if ($fileType['id'] === Certification::CERTIFICATE_FILE_TYPE_ID && $fileType['name'] === Certification::CERTIFICATE_FILE_TYPE_NAME) {
            if (!$entity->isFullCertification()) {
                throw new WedofBadRequestHttpException("Erreur, il n'est pas possible de générer le parchemin pour un dossier ne portant pas sur la totalité d'une certification.");
            }
            $response = $entityService->generateCertificate($entity, $fileType);
            $fileName = "parchemin-" . $entity->getExternalId();
        } else {
            if ($entity instanceof RegistrationFolder) {
                $mainOrganismFromEntity = $entity->getOrganism();
            } else {
                $mainOrganismFromEntity = $entity->getCertifier();
            }
            $response = $this->generateDocumentFromTemplateAndEntity($entity, $fileType, [], $mainOrganismFromEntity);
            if (empty($response['statusCode']) || $response['statusCode'] != 200) {
                throw new WedofBadRequestHttpException(json_encode($response['content'], true));
            }
        }

        $content = $response['content'];
        $decodedContent = json_decode($content, true);

        if (isset($decodedContent['documentLink'])) {
            return $entityFileService->create($decodedContent['documentLink'], $fileType['id'], $entity, true, $user, false, $fileType['name']);
        } else {
            return $this->createFileWithContent($content, $fileType, $entity, $entityFileService, $user, $fileName);
        }
    }

    /**
     * @param array $fileType
     * @return array|null
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function generateExampleDocumentFromTemplate(array $fileType): ?array
    {
        $computedContext = Dictionary::getExampleContextFromDictionary('document');
        if ($fileType['id'] === Certification::CERTIFICATE_FILE_TYPE_ID && $fileType['name'] === Certification::CERTIFICATE_FILE_TYPE_NAME) {
            $parameters = [
                'uid' => $fileType['googleId'],
                'fileTypeId' => $fileType['id'],
                'externalId' => "1234-987654",
                'id' => 987562,
                'certificateUrl' => "https://www.wedof.fr/app/public/qrCode?url=/app/public/certificateHolder/test",
                'computedContext' => $computedContext,
            ];
            return $this->automatorApiService->generateCertificate($parameters);
        } else {
            return $this->automatorApiService->generateDocumentFromTemplateAndContext($fileType, $computedContext);
        }
    }

    /**
     * @param string|null $mimeType
     * @param string|null $googleId
     * @param string|null $fileName
     * @return mixed
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function createOrUpdateTemplate(string $mimeType = null, string $googleId = null, string $fileName = null): array
    {
        $creating = !$googleId;
        $response = $this->automatorApiService->createOrUpdateTemplate($mimeType, $googleId, $fileName);
        if (empty($response['statusCode']) || $response['statusCode'] != 200) {
            throw new WedofBadRequestHttpException(json_encode($response['content'], true));
        } else {
            $content = json_decode($response['content'], true);
            return $creating ? ['id' => $content['id']] : ['name' => $content['name']];
        }
    }

    /**
     * @param string $templateName
     * @param string $field
     * @param string|null $fileName
     * @return mixed
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function createFromWedofTemplate(string $templateName, string $field, string $fileName): array
    {
        if ($field === self::CERTIFICATION_FOLDER) {
            $folderName = CertificationFolder::CLASSNAME;
        } else if ($field === self::REGISTRATION_FOLDER) {
            $folderName = RegistrationFolder::CLASSNAME;
        } else if ($field === self::CERTIFICATION_PARTNER) {
            $folderName = CertificationPartner::CLASSNAME;
        } else if ($field === self::CERTIFICATION_PARTNER_AUDIT) {
            $folderName = CertificationPartnerAudit::CLASSNAME;
        } else if ($field === self::CERTIFICATION) {
            $folderName = Certification::CLASSNAME;
        } else {
            throw new WedofBadRequestHttpException("Erreur, field n'a pas été trouvé");
        }
        $response = $this->automatorApiService->duplicateFromTemplate($templateName, $folderName, $fileName);
        if (empty($response['statusCode']) || $response['statusCode'] != 200) {
            throw new WedofBadRequestHttpException(json_encode($response['content'], true));
        } else {
            $content = json_decode($response['content'], true);
            $result = ['id' => $content['id']];
            if ($field === self::CERTIFICATION_PARTNER_AUDIT) {
                $result['criterias'] = $content['criterias'];
            }
            return $result;
        }
    }

    /**
     * @param string $fileName
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function deleteTemplate(string $fileName): void
    {
        $response = $this->automatorApiService->deleteDocumentTemplate('template-document/scratch/delete', null, $fileName);
        if (empty($response['statusCode']) || $response['statusCode'] != 200) {
            throw new WedofBadRequestHttpException(json_encode($response['content'], true));
        }
    }

    /**
     * @param string $field
     * @return array
     * @throws InvalidArgumentException
     */
    public function listFileTypesWedof(string $field): array
    {
        $cache = new FilesystemAdapter();
        $googleDriveFolderId = Tools::getEnvValue(strtoupper($field) . '_GOOGLEDRIVEFOLDER');
        $response = $cache->get($field, function (ItemInterface $item) use ($field, $googleDriveFolderId) {
            $item->expiresAfter(15 * 86400); // expire tous les 15 jours - mise à jour par workflow n8n
            return $this->automatorApiService->listFileTypesWedof($field, $googleDriveFolderId);
        });
        if (empty($response['statusCode']) || $response['statusCode'] != 200) {
            throw new WedofBadRequestHttpException(json_encode($response['content'], true));
        }
        $content = json_decode($response['content'], true);
        return $content['fileTypes'];
    }

    /**
     * @param string $googleIdToDuplicate
     * @param string $fileName
     * @return array
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function duplicateFromTemplateGoogleId(string $googleIdToDuplicate, string $fileName): array
    {
        $response = $this->automatorApiService->duplicateFromTemplateGoogleId($googleIdToDuplicate, $fileName);
        if (empty($response['statusCode']) || $response['statusCode'] != 200) {
            throw new WedofBadRequestHttpException(json_encode($response['content'], true));
        } else {
            $content = json_decode($response['content'], true);
            return ['name' => $content['name'], 'googleId' => $content['googleId']];
        }
    }

    //-------------------
    // METHODES PRIVEES
    //-------------------

    /**
     * @param $entity
     * @param Organism $organism
     * @param int $fileTypeId
     * @param File $template
     * @param string $field
     * @return array
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    private function generateDocumentTemplate($entity, Organism $organism, int $fileTypeId, File $template, string $field): array
    {
        $parameters = [
            'name' => $organism->getSiret() . '_' . $entity::CLASSNAME . '_' . $field . '_' . $entity->getId() . '_' . $fileTypeId,
            'mimeType' => $template->getMimeType()
        ];
        $response = $this->automatorApiService->generateDocumentTemplate($parameters, $organism, $template);
        if (empty($response['statusCode']) || $response['statusCode'] != 200) {
            throw new WedofBadRequestHttpException(json_encode($response['content'], true));
        } else {
            $content = json_decode($response['content'], true);
            return ['name' => $content['name'], 'googleId' => ($content['googleId'] ?? $content['id'])];
        }
    }

    /**
     * @param $content
     * @param $fileType
     * @param $entity
     * @param $entityFileService
     * @param $user
     * @param $title
     * @return CertificationFolderFile|CertificationPartnerFile|RegistrationFolderFile|CertificationPartnerAuditFile
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function createFileWithContent($content, $fileType, $entity, $entityFileService, $user, $title = null)
    {
        $uuid = uuid_create();
        //https://www.php.net/manual/en/function.sys-get-temp-dir.php  || Use sys_get_temp_dir since the /tmp can be modified.
        $dir = sys_get_temp_dir() . '/' . $uuid;
        mkdir($dir);

        $fileName = $title ?: str_replace('/', '', $fileType['name']);
        $fileName = filter_var($fileName, FILTER_SANITIZE_SPECIAL_CHARS);

        $path = $dir . '/' . $fileName . '.pdf';
        file_put_contents($path, $content);

        $newFile = new File($path);
        //Force 'test' to true because we aren't in a true "uploadedFile" case.  https://www.php.net/manual/en/function.is-uploaded-file.php
        $newUploadedFile = new UploadedFile($newFile->getRealPath(), $newFile->getFilename(), $newFile->getMimeType(), null, true);
        //Activity & UpdateFile event are created inside entityFileService->create()
        /** @var RegistrationFolderFileService|CertificationFolderFileService|CertificationPartnerFileService|CertificationPartnerAuditFileService $entityFileService */
        $file = $entityFileService->create($newUploadedFile, $fileType['id'], $entity, true, $user);
        //if file isn't deleted yet
        if ((new FilesystemIterator($dir))->valid()) {
            unlink($path);
        }
        //Be carefull to not delete the wrong folder here. The file is moved and renamed by Symfony(UploadedFile) before the rmdir.
        rmdir($dir);
        return $file;
    }
}
