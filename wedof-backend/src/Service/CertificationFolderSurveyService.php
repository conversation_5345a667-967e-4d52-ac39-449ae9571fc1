<?php

namespace App\Service;

use App\Entity\AttendeeExperience;
use App\Entity\Certification;
use App\Entity\CertificationFolder;
use App\Entity\CertificationFolderSurvey;
use App\Entity\Organism;
use App\Event\CertificationFolderSurvey\CertificationFolderSurveyEvents;
use App\Exception\WedofBadRequestHttpException;
use App\Library\utils\enums\ActivityTypes;
use App\Library\utils\enums\CertificationFolderAccessModality;
use App\Library\utils\enums\CertificationFolderStates;
use App\Library\utils\enums\CertificationFolderSurveyExperience;
use App\Library\utils\enums\CertificationFolderSurveyStates;
use App\Library\utils\enums\ContractType;
use App\Library\utils\enums\SituationCertification;
use App\Library\utils\Tools;
use App\Repository\CertificationFolderSurveyRepository;
use DateTime;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\QueryBuilder;
use Exception;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Throwable;

class CertificationFolderSurveyService implements LoggerAwareInterface
{
    private LoggerInterface $logger;
    private EventDispatcherInterface $dispatcher;
    private CertificationFolderSurveyRepository $surveyRepository;
    private ActivityService $activityService;

    public function __construct(EventDispatcherInterface $dispatcher, CertificationFolderSurveyRepository $surveyRepository, ActivityService $activityService)
    {
        $this->dispatcher = $dispatcher;
        $this->surveyRepository = $surveyRepository;
        $this->activityService = $activityService;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    /**
     * @param int $entityId
     * @return CertificationFolderSurvey|null
     */
    public function getByEntityId(int $entityId): ?CertificationFolderSurvey
    {
        return $this->surveyRepository->find($entityId);
    }

    /**
     * @param CertificationFolder $certificationFolder
     * @return CertificationFolderSurvey
     */
    public function create(CertificationFolder $certificationFolder): CertificationFolderSurvey
    {
        $survey = new CertificationFolderSurvey();
        $survey->setCertificationFolder($certificationFolder);
        $this->surveyRepository->save($survey);
        $this->sendEvent($survey, CertificationFolderSurveyEvents::CREATED);
        return $survey;
    }

    /**
     * @param DateTime $issueDate
     * @param CertificationFolderSurvey $certificationFolderSurvey
     * @throws Exception
     */
    public function updateExperienceStartDates(DateTime $issueDate, CertificationFolderSurvey $certificationFolderSurvey): void
    {
        $sixMonthExperienceStart = date('Y-m-d', strtotime($issueDate->format('Y-m-d') . ' + ' . 6 . ' months'));
        $certificationFolderSurvey->setSixMonthExperienceStartDate(new DateTime($sixMonthExperienceStart));
        $longTermExperienceStart = date('Y-m-d', strtotime($issueDate->format('Y-m-d') . ' + ' . 1 . ' year'));
        $certificationFolderSurvey->setLongTermExperienceStartDate(new DateTime($longTermExperienceStart));
        $this->save($certificationFolderSurvey);
    }

    /**
     * @param CertificationFolderSurvey $certificationFolderSurvey
     * @param AttendeeExperience $attendeeExperience
     * @param string $surveyName
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function update(CertificationFolderSurvey $certificationFolderSurvey, AttendeeExperience $attendeeExperience, string $surveyName)
    {
        $certificationFolder = $certificationFolderSurvey->getCertificationFolder();

        if ($certificationFolder->getState() !== CertificationFolderStates::SUCCESS()->getValue() && $surveyName !== CertificationFolderSurveyExperience::INITIAL_EXPERIENCE()->getValue()) {
            throw new WedofBadRequestHttpException("Erreur, ce questionnaire est réservé aux titulaires ayant obtenu la certification");
        }
        if ($attendeeExperience->getAttendee() !== $certificationFolder->getAttendee()) {
            throw new WedofBadRequestHttpException("Erreur, l'apprenant ne correspond pas à l'apprenant associé au dossier de certification");
        }

        $getMethodName = "get" . ucwords($surveyName);
        if ($certificationFolderSurvey->{$getMethodName}($surveyName)) {
            throw new WedofBadRequestHttpException("Erreur, " . $surveyName . " est déjà renseignée.");
        }

        $today = (new DateTime('now'))->format('Y-m-d');

        if ($surveyName === CertificationFolderSurveyExperience::SIX_MONTH_EXPERIENCE()->getValue()) {
            if (!$certificationFolderSurvey->getInitialExperience()) {
                throw new WedofBadRequestHttpException("Erreur, vous ne pouvez pas ajouter de 'sixMonthExperience' sans avoir de 'initialExperience'");
            }
            $getSixMonthsDate = $certificationFolderSurvey->getSixMonthExperienceStartDate()->format('Y-m-d');
            if ($today < $getSixMonthsDate) {
                throw new WedofBadRequestHttpException("Erreur, vous ne pourrez répondre à cette enquête qu'à partir du " . $getSixMonthsDate . ".");
            }
        }
        if ($surveyName === CertificationFolderSurveyExperience::LONG_TERM_EXPERIENCE()->getValue()) {
            if (!$certificationFolderSurvey->getInitialExperience() || !$certificationFolderSurvey->getSixMonthExperience()) {
                throw new WedofBadRequestHttpException("Erreur, vous ne pouvez pas ajouter de 'longTermExperience' sans avoir de 'initialExperience' ou de 'sixMonthExperience'");
            }
            $getLongTermeDate = $certificationFolderSurvey->getLongTermExperienceStartDate()->format('Y-m-d');
            if ($today < $getLongTermeDate) {
                throw new WedofBadRequestHttpException("Erreur, vous ne pourrez répondre à cette enquête qu'à partir du " . $getLongTermeDate . ".");
            }
        }

        $setMethodName = "set" . ucwords($surveyName);
        $certificationFolderSurvey->{$setMethodName}($attendeeExperience);

        $setMethodDateName = "set" . ucwords($surveyName) . "AnsweredDate";
        $certificationFolderSurvey->{$setMethodDateName}(new DateTime());

        if ($surveyName === CertificationFolderSurveyExperience::INITIAL_EXPERIENCE()->getValue()) {
            $event = CertificationFolderSurveyEvents::INITIAL_EXPERIENCE_ANSWERED;
            $state = CertificationFolderSurveyStates::BEFORE_CERTIFICATION_SUCCESS()->getValue();
        } else if ($surveyName === CertificationFolderSurveyExperience::SIX_MONTH_EXPERIENCE()->getValue()) {
            $event = CertificationFolderSurveyEvents::SIX_MONTH_EXPERIENCE_ANSWERED;
            $state = CertificationFolderSurveyStates::AFTER_SIX_MONTHS_CERTIFICATION_SUCCESS()->getValue();
        } else {
            $event = CertificationFolderSurveyEvents::LONG_TERM_EXPERIENCE_ANSWERED;
            $state = CertificationFolderSurveyStates::FINISHED()->getValue();
        }
        $certificationFolderSurvey->setState($state);
        $this->save($certificationFolderSurvey);

        $this->sendEvent($certificationFolderSurvey, $event);
        $this->createActivity($certificationFolderSurvey->getCertificationFolder(), $surveyName);
    }

    /**
     * @param array $parameters
     * @return QueryBuilder
     */
    public function listByEntityReturnQueryBuilder(array $parameters): QueryBuilder
    {
        return $this->surveyRepository->findAllReturnQueryBuilder($parameters);
    }

    /**
     * @param array $parameters
     * @return array
     */
    public function generateDataToExport(array $parameters): array
    {
        $certificationFoldersSurvey = $this->surveyRepository->findAllReturnQueryBuilder($parameters)->getQuery()->getResult();
        $surveysData = [];
        $today = new DateTime();

        foreach ($certificationFoldersSurvey as $certificationFolderSurvey) {
            /** @var CertificationFolderSurvey $certificationFolderSurvey */
            $initialExperience = $certificationFolderSurvey->getInitialExperience();
            $sixMonthExperience = $certificationFolderSurvey->getSixMonthExperience();
            $longTermExperience = $certificationFolderSurvey->getLongTermExperience();
            $certificationFolder = $certificationFolderSurvey->getCertificationFolder();
            $attendee = $certificationFolder->getAttendee();
            $partner = $certificationFolder->getPartner();
            $issueDate = $certificationFolder->getIssueDate();

            $surveyData = [];
            $surveyData['issueDate'] = $issueDate ? $issueDate->format('d/m/Y') : null;
            $surveyData['issueDateYear'] = $issueDate ? $issueDate->format('Y') : null;
            $surveyData['optionName'] = $certificationFolder->getOptionName();
            $surveyData['candidate'] = $attendee->getFirstName() . " " . $attendee->getLastName();
            $surveyData['partnerName'] = $partner ? $partner->getName() : null;

            if ($initialExperience) {
                $surveyData['initialExperienceQualification'] = $initialExperience->getQualification() ? Tools::getQualificationTitleFromNumber($initialExperience->getQualification()) : null;
                $surveyData['initialExperienceCertification'] = $initialExperience->getCertificationName();
                $surveyData['initialExperienceJob'] = $initialExperience->getJob();
                $initialExperienceExperienceYears = null;
                if ($initialExperience->getStartDate()) {
                    $endDate = $initialExperience->getEndDate();
                    $startDate = $initialExperience->getStartDate();
                    if ($endDate) {
                        $initialExperienceExperienceYears = $startDate->diff($endDate)->y;
                    } else {
                        $initialExperienceExperienceYears = $startDate->diff($today)->y;
                    }
                }
                $surveyData['initialExperienceExperienceYears'] = $initialExperienceExperienceYears;
                $surveyData['initialExperienceCompanyName'] = $initialExperience->getCompanyName();
                $surveyData['initialExperienceSalaryYearly'] = $initialExperience->getSalaryYearly();
                $surveyData['initialExperienceSituation'] = $initialExperience->getSituation() ? SituationCertification::toFrStringForFranceCompetences($initialExperience->getSituation()) : null;
            }
            $surveyData['certificationFolderAccess'] = $certificationFolder->getAccessModality() ? CertificationFolderAccessModality::toFrString($certificationFolder->getAccessModality()) : null;
            if ($sixMonthExperience) {
                $isActiveSixMonthExperienceExperience = $sixMonthExperience->getSituation() && $sixMonthExperience->getSituation() === SituationCertification::ACTIVE()->getValue();
                $surveyData['sixMonthExperienceSituation'] = $sixMonthExperience->getSituation() ? SituationCertification::toFrStringForFranceCompetences($sixMonthExperience->getSituation()) : null;
                $surveyData['sixMonthExperienceJob'] = $isActiveSixMonthExperienceExperience ? $sixMonthExperience->getJob() : null;
                $surveyData['sixMonthExperienceContractType'] = $isActiveSixMonthExperienceExperience && $sixMonthExperience->getContractType() ? ContractType::toFrString($sixMonthExperience->getContractType()) : null;
                $surveyData['sixMonthExperienceCompanyName'] = $isActiveSixMonthExperienceExperience ? $sixMonthExperience->getCompanyName() : null;
                $surveyData['sixMonthExperienceExecutiveStatus'] = $isActiveSixMonthExperienceExperience ? $sixMonthExperience->isExecutiveStatus() ? 'Oui' : 'Non' : null;
                $surveyData['sixMonthExperienceSalaryYearly'] = $isActiveSixMonthExperienceExperience ? $sixMonthExperience->getSalaryYearly() : null;
                $surveyData['sixMonthDate'] = $certificationFolderSurvey->getSixMonthExperienceAnsweredDate()->format('d/m/Y');
            }
            if ($longTermExperience) {
                $isActiveLongTermExperience = $longTermExperience->getSituation() && $longTermExperience->getSituation() === SituationCertification::ACTIVE()->getValue();
                $surveyData['longTermExperienceSituation'] = $longTermExperience->getSituation() ? SituationCertification::toFrStringForFranceCompetences($longTermExperience->getSituation()) : null;
                $surveyData['longTermExperienceJob'] = $isActiveLongTermExperience ? $longTermExperience->getJob() : null;
                $surveyData['longTermExperienceContractType'] = $isActiveLongTermExperience && $longTermExperience->getContractType() ? ContractType::toFrString($longTermExperience->getContractType()) : null;
                $surveyData['longTermExperienceCompanyName'] = $isActiveLongTermExperience ? $longTermExperience->getCompanyName() : null;
                $surveyData['longTermExperienceExecutiveStatus'] = $isActiveLongTermExperience ? $longTermExperience->isExecutiveStatus() ? 'Oui' : 'Non' : null;
                $surveyData['longTermExperienceSalaryYearly'] = $isActiveLongTermExperience ? $longTermExperience->getSalaryYearly() : null;
                $surveyData['longTermExperienceAfterIssueDate'] = $issueDate->diff($today)->y;
                $surveyData['longTermExperienceDate'] = $certificationFolderSurvey->getLongTermExperienceAnsweredDate()->format('d/m/Y');
            }
            $surveysData[] = $surveyData;
            $this->save($certificationFolderSurvey);
        }
        return $surveysData;
    }

    /**
     * @param CertificationFolderSurvey $certificationFolderSurvey
     */
    public function delete(CertificationFolderSurvey $certificationFolderSurvey): void
    {
        $this->surveyRepository->delete($certificationFolderSurvey);
    }

    public function sendEventSurveyAvailable(): void
    {
        $sixMonthSurveys = $this->surveyRepository->findAllReturnQueryBuilder(['raiseEventSixMonthExperienceAvailable' => true])->getQuery()->getResult();
        if ($sixMonthSurveys) {
            foreach ($sixMonthSurveys as $sixMonthSurvey) {
                $this->sendEvent($sixMonthSurvey, CertificationFolderSurveyEvents::SIX_MONTH_EXPERIENCE_AVAILABLE);
            }
        }
        $longTermSurveys = $this->surveyRepository->findAllReturnQueryBuilder(['raiseEventLongTermExperienceAvailable' => true])->getQuery()->getResult();
        if ($longTermSurveys) {
            foreach ($longTermSurveys as $longTermSurvey) {
                $this->sendEvent($longTermSurvey, CertificationFolderSurveyEvents::LONG_TERM_EXPERIENCE_AVAILABLE);
            }
        }
    }

    /**
     * @param Certification $certification
     * @param Organism|null $partner
     * @return array
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function details(Certification $certification, Organism $partner = null): array
    {
        $totalSurveys = $this->surveyRepository->countForCertification($certification, $partner);
        $countByState['total'] = $totalSurveys;
        $countByState['beforeCertificationSuccess'] = $this->surveyRepository->countForExperience($certification, 'initialExperience', $partner);
        $countByState['afterSixMonthsCertificationSuccess'] = $this->surveyRepository->countForExperience($certification, 'sixMonthExperience', $partner);
        $countByState['finished'] = $this->surveyRepository->countForExperience($certification, 'longTermExperience', $partner);
        $countByState['canAnswerSixMonths'] = $this->surveyRepository->countForExperience($certification, 'sixMonthExperience', $partner, true);
        $countByState['canAnswerLongTerm'] = $this->surveyRepository->countForExperience($certification, 'longTermExperience', $partner, true);
        $countByAnswered = [
            'initialExperienceAnsweredRate' => $this->surveyRepository->countForExperienceAnsweredRate($certification, 'initialExperience', $partner) * $totalSurveys / 100,
            'sixMonthExperienceAnsweredRate' => $this->surveyRepository->countForExperienceAnsweredRate($certification, 'sixMonthExperience', $partner) * $totalSurveys / 100,
            'longTermExperienceAnsweredRate' => $this->surveyRepository->countForExperienceAnsweredRate($certification, 'longTermExperience', $partner) * $totalSurveys / 100
        ];
        return array_merge($countByState, $countByAnswered);
    }

    //----------------
    // METHODES PRIVEES
    //----------------

    /**
     * @param CertificationFolderSurvey $survey
     * @return CertificationFolderSurvey
     */
    private function save(CertificationFolderSurvey $survey): CertificationFolderSurvey
    {
        return $this->surveyRepository->save($survey);
    }

    /**
     * @param CertificationFolder $certificationFolder
     * @param string $experienceName
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    private function createActivity(CertificationFolder $certificationFolder, string $experienceName)
    {
        $data = [
            'title' => "Le candidat a ajouté une nouvelle expérience correspondant à " . CertificationFolderSurveyExperience::toFrString($experienceName),
            'description' => null,
            'type' => ActivityTypes::CDC(),
            'eventTime' => new DateTime(),
            'origin' => "le Candidat"
        ];

        $this->activityService->create($data, null, $certificationFolder, false);
    }

    /**
     * @param CertificationFolderSurvey $certificationFolderSurvey
     * @param string $eventName
     */
    private function sendEvent(CertificationFolderSurvey $certificationFolderSurvey, string $eventName): void
    {
        $event = new CertificationFolderSurveyEvents($certificationFolderSurvey);
        $this->dispatcher->dispatch($event, $eventName);
        $this->logger->info("[" . $certificationFolderSurvey->getId() . "][event] sendEventCertificationFolderSurvey event dispatched $eventName ");
    }
}