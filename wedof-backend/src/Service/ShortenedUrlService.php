<?php

namespace App\Service;

use App\Entity\ShortenedUrl;
use App\Library\utils\Tools;
use App\Repository\ShortenedUrlRepository;
use DateTime;

class ShortenedUrlService
{
    const URL_PREFIX = 'https://s.wedof.fr/';

    private ShortenedUrlRepository $shortenedUrlRepository;

    public function __construct(ShortenedUrlRepository $shortenedUrlRepository)
    {
        $this->shortenedUrlRepository = $shortenedUrlRepository;
    }

    /**
     * @param string $url
     * @param string $entityClass
     * @param string $entityId
     * @return ShortenedUrl
     */
    public function createOrRetrieve(string $url, string $entityClass, string $entityId): ShortenedUrl
    {
        $hashKey = $this->computeHashKey($url, $entityClass, $entityId);
        $shortenedUrl = $this->shortenedUrlRepository->findOneByHashKey($hashKey);
        if (!isset($shortenedUrl)) {
            $shortenedUrl = new ShortenedUrl();
            $shortenedUrl->setUrl($url);
            $shortenedUrl->setCountOpen(0);
            $shortenedUrl->setEntityId($entityId);
            $shortenedUrl->setEntityClass($entityClass);
            $shortenedUrl->setHashKey($hashKey);
            $shortenedUrl = $this->shortenedUrlRepository->save($shortenedUrl);
        }
        return $shortenedUrl;
    }

    /**
     * @param string $originalUrl
     * @param string $entityClass
     * @param string $entityId
     * @return string
     */
    public function shortenUrl(string $originalUrl, string $entityClass, string $entityId): string
    {
        if (Tools::startsWith($originalUrl, self::URL_PREFIX)) {
            return $originalUrl;
        }
        $shortenedUrl = $this->createOrRetrieve($originalUrl, $entityClass, $entityId);
        return self::URL_PREFIX . $shortenedUrl->getHashKey();
    }

    /**
     * @param array $regexes
     * @param string $text
     * @param string $entityClass
     * @param string $entityId
     * @return string
     */
    public function shortenUrlsInText(array $regexes, string $text, string $entityClass, string $entityId): string
    {
        foreach ($regexes as $regex) {
            preg_match_all($regex, $text, $matches);
            $originalUrls = $matches[0];
            foreach ($originalUrls as $originalUrl) {
                if (!Tools::startsWith($originalUrl, 'https://', true) && !Tools::startsWith($originalUrl, 'http://', true)) { // Allow "pseudo" urls without protocol
                    $targetUrl = 'https://' . $originalUrl;
                } else {
                    $targetUrl = $originalUrl;
                }
                $shortUrl = $this->shortenUrl($targetUrl, $entityClass, $entityId);
                if ($shortUrl !== $originalUrl) {
                    $text = Tools::strReplaceFirst($originalUrl, $shortUrl, $text); // Replace first only to avoid replacing what would be the part of a larger URL appearing later in the text
                }
            }
        }
        return $text;
    }

    /**
     * @param $hashKey
     * @return string|null
     */
    public function openUrl($hashKey): ?string
    {
        $shortenedUrl = $this->shortenedUrlRepository->findOneByHashKey($hashKey);
        $url = null;
        if (isset($shortenedUrl)) {
            $shortenedUrl->setLastOpen(new DateTime());
            $oldCountOpen = $shortenedUrl->getCountOpen();
            $shortenedUrl->setCountOpen($oldCountOpen + 1);
            $this->shortenedUrlRepository->save($shortenedUrl);
            $url = $shortenedUrl->getUrl();
        }
        return $url;
    }

    //-----------------
    // METHODES PRIVEES
    //-----------------

    /**
     * @param string $url
     * @param string $entityClass
     * @param string $entityId
     * @return string
     */
    private function computeHashKey(string $url, string $entityClass, string $entityId): string
    {
        // Ensure unicity by entity + url (link will be the same for the same entity and same url)
        $md5Hash = md5($entityClass . $entityId . $url);
        return hash('crc32', $md5Hash);
    }
}
