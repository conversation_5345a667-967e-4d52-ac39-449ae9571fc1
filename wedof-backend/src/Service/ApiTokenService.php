<?php
// src/Service/ApiTokenService.php
namespace App\Service;

use App\Entity\ApiToken;
use App\Entity\User;
use App\Repository\ApiTokenRepository;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Exception;

class ApiTokenService
{
    private ApiTokenRepository $apiTokenRepository;

    public function __construct(ApiTokenRepository $apiTokenRepository)
    {
        $this->apiTokenRepository = $apiTokenRepository;
    }

    /**
     * @param User $user
     * @param string $name
     * @return ApiToken
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Exception
     */
    public function create(User $user, string $name): ApiToken
    {
        $apiToken = new ApiToken();
        $apiToken->setUser($user);
        $apiToken->setName($name);
        $apiToken->setToken(bin2hex(random_bytes(20)));

        return $this->apiTokenRepository->save($apiToken);
    }

    /**
     * @param ApiToken $apiToken
     */
    public function delete(ApiToken $apiToken)
    {
        $this->apiTokenRepository->delete($apiToken);
    }
}
