<?php
// src/Service/RegistrationFolderHistoryService.php
namespace App\Service;

use App\Entity\RegistrationFolderHistory;
use App\Repository\RegistrationFolderHistoryRepository;
use Doctrine\ORM\Exception\ORMException;

class RegistrationFolderHistoryService
{
    private RegistrationFolderHistoryRepository $registrationFolderHistoryRepository;

    public function __construct(RegistrationFolderHistoryRepository $registrationFolderHistoryRepository)
    {
        $this->registrationFolderHistoryRepository = $registrationFolderHistoryRepository;
    }

    /**
     * @return RegistrationFolderHistory
     * @throws ORMException|\Doctrine\ORM\ORMException
     */
    public function create(): RegistrationFolderHistory
    {
        $registrationFolderHistory = new RegistrationFolderHistory();
        return $this->registrationFolderHistoryRepository->save($registrationFolderHistory);
    }
}
