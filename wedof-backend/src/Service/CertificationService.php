<?php
// src/Service/CertificationService.php
namespace App\Service;

use App\Entity\Certification;
use App\Entity\CertificationPartner;
use App\Entity\Organism;
use App\Entity\Session;
use App\Event\Certification\CertificationEvents;
use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofConnectionException;
use App\Exception\WedofSubscriptionException;
use App\Library\CertificationStatistics;
use App\Library\utils\enums\CertificationPartnerAuditResults;
use App\Library\utils\enums\CertificationPartnerStates;
use App\Library\utils\enums\CertificationSkillType;
use App\Library\utils\enums\CertificationTypes;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\enums\OrganismApplicantType;
use App\Library\utils\enums\SubscriptionCertifierTypes;
use App\Library\utils\Tools;
use App\Repository\CertificationFolderRepository;
use App\Repository\CertificationRepository;
use App\Repository\RegistrationFolderRepository;
use App\Service\DataProviders\AutomatorApiService;
use App\Service\DataProviders\BaseApiService;
use App\Service\DataProviders\FranceCompetencesApiService;
use Datetime;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\QueryBuilder;
use ErrorException;
use Exception;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\File\File;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Throwable;

class CertificationService implements LoggerAwareInterface
{
    private LoggerInterface $logger;
    private EventDispatcherInterface $dispatcher;
    private CertificationRepository $certificationRepository;
    private MailerService $mailerService;
    private OrganismService $organismService;
    private CertificationPartnerService $certificationPartnerService;
    private AutomatorApiService $automatorApiService;
    private SkillService $skillService;
    private CertificationFolderRepository $certificationFolderRepository;
    private RegistrationFolderRepository $registrationFolderRepository;

    private EntityManagerInterface $entityManager;
    private ContainerInterface $container;

    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    public function __construct(
        EventDispatcherInterface      $dispatcher,
        MailerService                 $mailerService,
        OrganismService               $organismService,
        AutomatorApiService           $automatorApiService,
        CertificationPartnerService   $certificationPartnerService,
        CertificationRepository       $certificationRepository,
        EntityManagerInterface        $entityManager,
        ContainerInterface            $container,
        SkillService                  $skillService,
        CertificationFolderRepository $certificationFolderRepository,
        RegistrationFolderRepository  $registrationFolderRepository)
    {
        $this->dispatcher = $dispatcher;
        $this->mailerService = $mailerService;
        $this->organismService = $organismService;
        $this->automatorApiService = $automatorApiService;
        $this->certificationPartnerService = $certificationPartnerService;
        $this->certificationRepository = $certificationRepository;
        $this->entityManager = $entityManager;
        $this->container = $container;
        $this->skillService = $skillService;
        $this->certificationFolderRepository = $certificationFolderRepository;
        $this->registrationFolderRepository = $registrationFolderRepository;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    /**
     * @param Certification $certification
     * @return Organism
     */
    public function getDefaultCertifierFromCoCertifiers(Certification $certification): ?Organism
    {
        $certifiers = $certification->getCertifiers();
        $defaultCertifier = null;

        if ($certifiers->count() === 1) {
            $defaultCertifier = $certifiers[0];
        } else if ($certifiers->count() > 1) {
            $nbCertifierCustomer = 0;
            foreach ($certifiers as $certifier) {
                if ($certifier->getSubscription() && $certifier->getSubscription()->isAllowCertifiers()) {
                    $nbCertifierCustomer++;
                    $defaultCertifier = $certifier;
                }
            }
            if ($nbCertifierCustomer > 1) { // dans le cas où on a plus d'un certificateur client, ils doivent choisir
                $defaultCertifier = null;
            }
        }
        return $defaultCertifier;
    }

    /**
     * @param array $params
     * @param array $options
     * @return Certification|object|null
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function getCertification(array $params, array $options = [])
    {
        $options = array_merge(['refresh' => false], $options);
        $certification = $this->certificationRepository->findOneBy($params);
        if ((empty($certification) || $options['refresh']) && !empty($params['certifInfo'])) {
            $this->logger->info("[getCertification][" . $params['certifInfo'] . "] Certification doesn't exist or refresh needed");
            /** @var FranceCompetencesApiService $certificationApiService */
            $certificationApiService = BaseApiService::getCertificationApiServiceByDataProvider(DataProviders::FRANCE_COMPETENCES());
            $certificationRawData = $options['certificationData'] ?? $certificationApiService->getCertificationRawData($params['certifInfo'], $options['franceCompetencesData'] ?? []);
            if ($certificationRawData == null) {
                $this->logger->error("[getCertification][" . $params['certifInfo'] . "] no certification found");
                return null;
            }
            $certification = $this->createOrUpdate($certificationRawData);
        }
        return $certification;
    }

    /**
     * @param string $certifInfo
     * @return Certification|null
     */
    public function getByCertifInfo(string $certifInfo): ?Certification
    {
        return $this->certificationRepository->findOneByCertifInfo($certifInfo);
    }

    /**
     * @param int $id
     * @return Certification|null
     */
    public function getById(int $id): ?Certification
    {
        return $this->certificationRepository->find($id);
    }

    /**
     * @param string $externalId
     * @return Certification|null
     */
    public function getByExternalId(string $externalId): ?Certification
    {
        $externalIdParts = preg_split('/(?<=[a-z])(?=[0-9]+)/i', $externalId);
        $type = CertificationTypes::from(strtoupper($externalIdParts[0]));
        $code = (string)$externalIdParts[1];
        return $this->getByTypeAndCode($type, $code);
    }

    /**
     * @param CertificationTypes $type
     * @param string $code
     * @return Certification|null
     */
    public function getByTypeAndCode(CertificationTypes $type, string $code): ?Certification
    {
        return $this->certificationRepository->findOneByTypeAndCode($type, $code);
    }

    /**
     * @param array $certificationRawData
     * @return Certification
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function createOrUpdate(array $certificationRawData): Certification
    {
        $certification = $this->certificationRepository->findOneBy(array('certifInfo' => $certificationRawData['certifInfo']));
        if ($certification === null) {
            $certification = new Certification();
            $certification->setCertifInfo($certificationRawData['certifInfo']);
            $certification->setCount(0);
        }
        if (!empty($certificationRawData['count'])) {
            $certification->setCount($certificationRawData['count']);
        }
        if (!empty($certificationRawData['name'])) {
            $certification->setName($certificationRawData['name']);
        }
        if (!empty($certificationRawData['level'])) {
            $certification->setLevel($certificationRawData['level']);
        }
        if (!empty($certificationRawData['descriptif'])) {
            $certification->setDescriptif($certificationRawData['descriptif']);
        }
        if (!empty($certificationRawData['objectif'])) {
            $certification->setObjectif($certificationRawData['objectif']);
        }
        if (!empty($certificationRawData['certifiers'])) {
            foreach ($certificationRawData['certifiers'] as $certifierRawData) {
                $certifier = $this->organismService->getOrganism(['siret' => $certifierRawData['siret']]);
                if ($certifier) {
                    $certification->addCertifier($certifier);
                }
            }
        }
        if (!empty($certificationRawData['idInfos'])) {
            $certification->setType($certificationRawData['idInfos']['type']->getValue());
            $certification->setCode($certificationRawData['idInfos']['code']);
            $certification->setLink($certificationRawData['idInfos']['link']);
        }
        if (!empty($certificationRawData['link'])) {
            $certification->setMoreInformationsLink($certificationRawData['link']);
        }
        if (!empty($certificationRawData['domains'])) {
            $certification->setDomains($certificationRawData['domains']);
        }
        if (!empty($certificationRawData['rome'])) {
            if (isset($certificationRawData['rome']['code'])) {
                // Remove <i class="fa fa-external-link"></i>
                $certificationRawData['rome']['code'] = trim(strip_tags($certificationRawData['rome']['code']));
            }
            $certification->setRome($certificationRawData['rome']);
        }
        if (!empty($certificationRawData['gfe'])) {
            $certification->setGfe($certificationRawData['gfe']);
        }
        if (!empty($certificationRawData['nsf'])) {
            $certification->setNsf($certificationRawData['nsf']);
        }
        if (isset($certificationRawData['enabled'])) {
            $certification->setEnabled($certificationRawData['enabled']);
        } else {
            $certification->setEnabled(true);
        }
        if (!empty($certificationRawData['cpf'])) {
            if (!empty($certificationRawData['cpf'][0]['code'])) {
                $certification->setCpf($certificationRawData['cpf'][0]['code']);
            }
            if (!empty($certificationRawData['cpf']['dateStart'])) {
                $certification->setCpfDateStart($certificationRawData['cpf']['dateStart']);
            }
            if (!empty($certificationRawData['cpf']['dateEnd'])) {
                $certification->setCpfDateEnd($certificationRawData['cpf']['dateEnd']);
            }
        }
        if (!empty($certificationRawData['validityPeriod']) && $certification->getValidityPeriod() === null) {
            $certification->setValidityPeriod($certificationRawData['validityPeriod']);
        }
        if (!empty($certificationRawData['idFiche'])) {
            $certification->setIdFiche($certificationRawData['idFiche']);
        }
        if (!empty($certificationRawData['dataProvider'])) {
            $certification->setDataProvider($certificationRawData['dataProvider']->getValue());
        }

        $certification->setLastExternalUpdate(new DateTime());
        if (!$this->entityManager->contains($certification)) {
            $this->save($certification);
            $event = new CertificationEvents($certification);
            $this->dispatcher->dispatch($event, CertificationEvents::CREATED);
            $this->logger->info("[createUpdateCertification][" . $certification->getCertifInfo() . "] created with id " . $certification->getId());
        } else {
            $this->save($certification);
            $this->logger->info("[createUpdateCertification][" . $certification->getCertifInfo() . "] updated");
            $this->dispatcher->dispatch(new CertificationEvents($certification), CertificationEvents::UPDATED);
        }

        if (isset($certificationRawData['skillSets'])) {
            foreach ($certificationRawData['skillSets'] as $skillSet) {
                $skill = $this->skillService->getByCertificationAndTypeAndOrder($certification, CertificationSkillType::SKILL_SET()->getValue(), $skillSet['order']);
                if (!$skill) {
                    $this->skillService->create($skillSet, $certification);
                } else {
                    $this->skillService->update($skill, $skillSet);
                }
            }
        }

        return $certification;
    }

    /**
     * @param Certification $certification
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function updateCertificateTemplateThumbnail(Certification $certification)
    {
        $fileTypes = $certification->getCertificationFolderFileTypes();
        $certificateFileTypeIndex = array_search(Certification::CERTIFICATE_FILE_TYPE_ID, array_column($fileTypes, 'id'));
        $certificateFileType = $fileTypes[$certificateFileTypeIndex];
        if (empty($certificateFileType['googleId'])) {
            throw new WedofBadRequestHttpException("Erreur, la mise à jour de la miniature du parchemin est impossible");
        }
        $response = $this->automatorApiService->updateCertificateTemplateThumbnail($certificateFileType['googleId'], $certification->getCertifInfo());
        if (empty($response['statusCode']) || $response['statusCode'] != 200) {
            $error = json_decode($response['content'], true);
            throw new WedofBadRequestHttpException("Erreur, $error");
        }
    }

    /**
     * @param Certification $certification
     * @param Organism $certifier
     * @param array $body
     * @param bool $subscriptionAllowGenerateCertificate
     * @return Certification
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    public function update(Certification $certification, Organism $certifier, array $body, bool $subscriptionAllowGenerateCertificate = false): Certification
    {
        $properties = array('validityPeriod', 'obtentionSystem', 'examinationType', 'amountHt', 'partnershipComment', 'allowPartnershipRequest', 'allowGenerateXmlAutomatically', 'errorSynchronizePartners', 'certificateTemplate', 'certificateTemplateThumbnail', 'autoRegistering', 'auditsPendingCancellation', 'allowAudits', 'link', 'surveyOptional', 'allowPartialSkillSets');
        $updateCertificateFileType = false;
        $deleteCertificateFileType = false;
        $error = null;

        if (!empty($body['metadata'])) {
            $body['metadata'] = Tools::removeSpacesInKeys($body['metadata']);
        }

        foreach ($properties as $property) {
            if ($property === 'certificateTemplate') {
                if (isset($body['certificateTemplate']) && $body['certificateTemplate'] instanceof File) {
                    $updateCertificateFileType = $subscriptionAllowGenerateCertificate;
                    $certification->setCertificateTemplateFile($body['certificateTemplate']); // TODO à supprimer
                } else if (array_key_exists('certificateTemplate', $body) && $body['certificateTemplate'] === null) {
                    $deleteCertificateFileType = true;
                    $certification->setCertificateTemplateFile(); // TODO à supprimer
                    $certification->setCertificateTemplateName(); // TODO à supprimer
                    $certification->setCertificateTemplateThumbnailName();
                    $certification->setCertificateTemplateThumbnailFile();
                    $certification->setUpdatedOn(new DateTime());
                }
            } else if ($property === 'certificateTemplateThumbnail') {
                if (isset($body['certificateTemplateThumbnail']) && $body['certificateTemplateThumbnail'] instanceof File) {
                    $certification->setCertificateTemplateThumbnailFile($body['certificateTemplateThumbnail']);
                } else if (array_key_exists('certificateTemplateThumbnail', $body) && $body['certificateTemplateThumbnail'] === null) {
                    $certification->setCertificateTemplateThumbnailFile();
                    $certification->setCertificateTemplateThumbnailName();
                    $certification->setUpdatedOn(new DateTime());
                }
            } else if (key_exists($property, $body)) {
                $setMethodName = "set" . ucwords($property);
                if (method_exists($certification, $setMethodName)) {
                    $certification->{$setMethodName}($body[$property]);
                }
            }
        }

        if ($updateCertificateFileType || $deleteCertificateFileType) {
            $fileTypes = $certification->getCertificationFolderFileTypes();
            $certificateFileTypeIndex = array_search(Certification::CERTIFICATE_FILE_TYPE_ID, array_column($fileTypes, 'id'));
            $certificateFileType = $fileTypes[$certificateFileTypeIndex];

            if ($updateCertificateFileType) {
                if (!empty($certificateFileType['googleId'])) { // Suppression du template précédent
                    $this->automatorApiService->deleteDocumentTemplate('template-certificate-delete', $certificateFileType['googleId'], $certificateFileType['templateFile']);
                }
                $response = $this->automatorApiService->generateCertificateTemplate($certification, $certifier);
                if (empty($response['statusCode']) || $response['statusCode'] != 200) {
                    // Don't save file if there is an error
                    $certification->setCertificateTemplateFile(); // TODO à supprimer
                    $certification->setCertificateTemplateName(); // TODO à supprimer
                    $certification->setCertificateTemplateThumbnailFile();
                    $certification->setCertificateTemplateThumbnailName();
                    $error = json_decode($response['content'], true);
                    if ($error == null) { // not a JSON error so not a "business" error so don't return it, just log it
                        $this->logger->error('Error generating certificate: ' . $response['content']);
                        $error = 'contactez le support';
                    } else {
                        $error = $error['message'];
                    }
                } else {
                    $content = json_decode($response['content'], true);
                    $certificateFileType['googleId'] = $content['googleId'];
                    $certificateFileType['templateFile'] = $certification->getExternalId();
                    $certificateFileType['generated'] = true;
                    $certificateFileType['enabled'] = true;
                }
            } else if (!empty($certificateFileType['googleId'])) { // deletion
                $response = $this->automatorApiService->deleteDocumentTemplate('template-certificate-delete', $certificateFileType['googleId'], $certificateFileType['templateFile']);
                if (empty($response['statusCode']) || $response['statusCode'] != 200) {
                    $this->logger->error('Error deleting certificate: ' . $response['content']); // not a Business error, so don't return it, only log it
                    $error = 'contactez le support';
                } else {
                    $certificateFileType['generated'] = false;
                    $certificateFileType['enabled'] = false;
                    $certificateFileType['googleId'] = null;
                    $certificateFileType['templateFile'] = null;
                }
            }
            $fileTypes[$certificateFileTypeIndex] = $certificateFileType;
            $certification->setCertificationFolderFileTypes($fileTypes);
            $certification->setUpdatedOn(new DateTime());
        }

        $this->save($certification);
        if ($error) {
            throw new WedofBadRequestHttpException("Erreur, " . $error);
        }
        if (isset($body['allowAudits'])) {
            $this->dispatcher->dispatch(new CertificationEvents($certification, $certifier), $certification->isAllowAudits() ? CertificationEvents::AUDITS_ENABLED : CertificationEvents::AUDITS_DISABLED);
        }
        $this->dispatcher->dispatch(new CertificationEvents($certification), CertificationEvents::UPDATED);

        return $certification;
    }

    /**
     * @param Certification $certification
     * @return Certification
     */
    public function save(Certification $certification): Certification
    {
        return $this->certificationRepository->save($certification);
    }

    /**
     * @param Organism|null $organism
     * @param array $parameters
     * @return QueryBuilder
     */
    public function listReturnQueryBuilder(?Organism $organism, array $parameters): QueryBuilder
    {
        return $this->certificationRepository->findAllReturnQueryBuilder($organism, $parameters);
    }

    /**
     * @param Organism $organism
     * @param string|null $query
     * @param array $certificationTypes
     * @param array|null $states
     * @param string|null $certifierSiret
     * @param bool $isPromoted
     * @return QueryBuilder
     */
    public function listPartnershipOrderedQueryBuilder(Organism $organism, ?string $query, array $certificationTypes, ?array $states, ?string $certifierSiret, bool $isPromoted = false): QueryBuilder
    {
        return $this->certificationRepository->findAllPartnershipOrderedQueryBuilder($organism, $query, $certificationTypes, $states, $certifierSiret, $isPromoted);
    }

    /**
     * @param Organism $organism
     * @param string|null $query
     * @param array $certificationTypes
     * @param array|null $states
     * @param string|null $certifierSiret
     * @param bool $isPromoted
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countAllPartnershipOrderedQueryBuilder(Organism $organism, ?string $query, array $certificationTypes, ?array $states, ?string $certifierSiret, bool $isPromoted = false): int
    {
        return $this->certificationRepository->countAllPartnershipOrderedQueryBuilder($organism, $query, $certificationTypes, $states, $certifierSiret, $isPromoted);
    }

    /**
     * @param Session $session
     * @return string
     */
    public function getNameFromSession(Session $session): ?string
    {
        $certification = $session->getTrainingAction()->getTraining()->getCertification();
        return $certification ? $certification->getName() : null;
    }

    /**
     * @return array
     * @throws Exception
     */
    public function listUpdatableCertifInfo(): array
    {
        return array_map(function ($row) {
            return $row['certifInfo'];
        }, $this->certificationRepository->findAllUpdatableCertifInfo());
    }

    /**
     * @param DataProviders $dataProvider
     * @return array
     * @throws Exception
     */
    public function listWithCertificationPartnersToUpdate(DataProviders $dataProvider): array
    {
        return array_map(function ($row) {
            return $row['certifInfo'];
        }, $this->certificationRepository->findAllWithCertificationPartnersListToUpdate($dataProvider));
    }

    /**
     * @param Certification $certification
     * @param array $updateCertifiersData
     * @return void
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     * @throws \Doctrine\ORM\ORMException
     */
    public function updateCertifiersForCertification(Certification $certification, array $updateCertifiersData): void
    {
        $this->logger->info("[checkCertifiers][" . $certification->getCertifInfo() . "] checking for certifiers");
        $knownCertifiers = array_map(function ($certifier) {
            return $certifier->getSiret();
        }, $certification->getCertifiers()->toArray());
        $newCertifiers = array_diff($updateCertifiersData, $knownCertifiers);
        $formerCertifiers = array_diff($knownCertifiers, $updateCertifiersData);
        $certifiersToBeRemovedFromPartnership = [];

        foreach ($newCertifiers as $newCertifier) {
            $certifier = $this->organismService->getOrganism(["siret" => $newCertifier]);
            if ($certifier) {
                $certification->addCertifier($certifier);
                $certifier->setApplicantType(OrganismApplicantType::CERTIFICATEUR()->getValue());
                $this->logger->info("[checkCertifiers][" . $certification->getCertifInfo() . "] certifier added " . $certifier->getSiret());
            }
        }

        foreach ($formerCertifiers as $formerCertifier) {
            $certifier = $this->organismService->getOrganism(["siret" => $formerCertifier]);
            if ($certifier) {
                if ($certifier->getSubscription() && in_array($certifier->getSubscription()->getCertifierType(), SubscriptionCertifierTypes::getPaidCertifierTypes())) {
                    $this->mailerService->sendAlertOnCertifierRemoved($certification, $certifier);
                } else {
                    $certification->removeCertifier($certifier);
                    $this->logger->info("[checkCertifiers][" . $certification->getCertifInfo() . "] certifier removed " . $certifier->getSiret());
                    if ($this->certificationPartnerService->getPartnersCount($certification, $certifier) > 0) {
                        $certifiersToBeRemovedFromPartnership[] = $certifier;
                    }
                }
            }
        }

        /** @var Organism $certifier */
        foreach ($certifiersToBeRemovedFromPartnership as $certifier) {
            if ($certification->getCertifiers()->count() === 1) {
                $this->switchCertifiersOnCertificationPartners($certification, $certifier, $certification->getCertifiers()[0]);
            } else if ($certification->getCertifiers()->count() > 1) {
                $newSiretCertifier = null;
                foreach ($certification->getCertifiers() as $activeCertifier) {
                    if ($activeCertifier->getSiren() === $certifier->getSiren()) {
                        $newSiretCertifier = $activeCertifier;
                        break;
                    }
                }
                $this->switchCertifiersOnCertificationPartners($certification, $certifier, $newSiretCertifier);
            } else {
                $this->switchCertifiersOnCertificationPartners($certification, $certifier, null);
            }
        }

        $this->save($certification);
        $this->logger->info("[checkCertifiers][" . $certification->getCertifInfo() . "] Updated certification");
    }

    /**
     * @param Certification $certification
     * @param Organism $certifier
     * @param bool $isAnnualSubscription
     * @return Certification
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofSubscriptionException
     */
    public function activateAudits(Certification $certification, Organism $certifier, bool $isAnnualSubscription): Certification
    {
        if ($isAnnualSubscription) {
            $this->dispatcher->dispatch(new CertificationEvents($certification, $certifier), CertificationEvents::AUDITS_ANNUAL_SUBSCRIPTION);
        } else {
            if ($certification->isAuditsPendingCancellation()) {
                // pas de facturation du prorata : auditsPendingCancelling === true implique qu'il y a eu une facturation préalable
                $certification = $this->update($certification, $certifier, ['auditsPendingCancellation' => false]);
                $this->container->get(StripeService::class)->createAuditSubscriptionOnCertification($certification, $certifier, false);
            } else if (!$certification->isAllowAudits()) {
                try {
                    $this->container->get(StripeService::class)->createAuditSubscriptionOnCertification($certification, $certifier, true);
                    $certification = $this->update($certification, $certifier, ['allowAudits' => true]);
                } catch (Exception $exception) {
                    throw new WedofSubscriptionException("L'abonnement Stripe n'a pu être créé, veuillez contacter le support.");
                }
            }
        }
        return $certification;
    }

    /**
     * @param Certification $certification
     * @param Organism $certifier
     * @return Certification
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofSubscriptionException
     */
    public function cancelAudits(Certification $certification, Organism $certifier): Certification
    {
        if ($certification->isAllowAudits() && !$certification->isAuditsPendingCancellation()) {
            try {
                $this->container->get(StripeService::class)->removeAuditSubscriptionOnCertification($certification, $certifier);
                $certification = $this->update($certification, $certifier, ['auditsPendingCancellation' => true]);
            } catch (Exception $exception) {
                throw new WedofSubscriptionException("L'abonnement Stripe n'a pu être annulé, veuillez contacter le support.");
            }
        }

        return $certification;
    }

    /**
     * @param Certification $certification
     * @param Organism $certifier
     * @return Certification
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function deactivateAudits(Certification $certification, Organism $certifier): Certification
    {
        /** @var CertificationPartnerAuditTemplateService $certificationPartnerAuditTemplateService */
        $certificationPartnerAuditTemplateService = $this->container->get(CertificationPartnerAuditTemplateService::class);
        $auditTemplates = $certificationPartnerAuditTemplateService->listReturnQueryBuilder($certifier, $certification)->getQuery()->getResult();
        /** @var CertificationPartnerAuditService $certificationPartnerAuditService */
        $certificationPartnerAuditService = $this->container->get(CertificationPartnerAuditService::class);

        if (count($auditTemplates) >= 1) {
            foreach ($auditTemplates as $auditTemplate) {
                $audits = $certificationPartnerAuditService->listReturnQueryBuilder(['state' => ['all'], 'templateId' => $auditTemplate->getId()])->getQuery()->getResult();
                if (count($audits) >= 1) {
                    foreach ($audits as $audit) {
                        $certificationPartnerAuditService->delete($audit);
                    }
                }
                $certificationPartnerAuditTemplateService->delete($auditTemplate);
            }
        }

        $certificationPartners = $certification->getCertificationPartners();
        if (count($certificationPartners) >= 1) {
            foreach ($certificationPartners as $certificationPartner) {
                if ($certificationPartner->getCompliance()) {
                    $this->certificationPartnerService->update($certificationPartner, ['compliance' => CertificationPartnerAuditResults::NONE()->getValue()]);
                }
            }
        }

        return $this->update($certification, $certifier, ['auditsPendingCancellation' => false, 'allowAudits' => false]);
    }

    /**
     * @param Certification $certification
     */
    public function delete(Certification $certification): void
    {
        $this->certificationRepository->delete($certification);
    }

    /**
     * @param Certification $certification
     * @return CertificationStatistics
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function getStatistics(Certification $certification): CertificationStatistics
    {
        return new CertificationStatistics($certification, $this->certificationFolderRepository);
    }

    /**
     * @param Certification $certification
     * @return bool
     */
    public function hasCertificationPartnersToUpdate(Certification $certification): bool
    {
        return $this->certificationRepository->hasCertificationPartnersToUpdate($certification);
    }

    //----------------
    // METHODES PRIVES
    //----------------

    /**
     * @param Certification $certification
     * @param Organism $oldCertifier
     * @param Organism|null $newCertifier
     * @return void
     */
    private function switchCertifiersOnCertificationPartners(Certification $certification, Organism $oldCertifier, ?Organism $newCertifier): void
    {
        /** @var CertificationPartner $certificationPartner */
        foreach ($this->certificationPartnerService->list($certification, [
            'certifier' => $oldCertifier,
            'state' => CertificationPartnerStates::allExceptDraft()
        ]) as $certificationPartner) {
            $certificationPartner->setCertifier($newCertifier);
        }
    }
}
