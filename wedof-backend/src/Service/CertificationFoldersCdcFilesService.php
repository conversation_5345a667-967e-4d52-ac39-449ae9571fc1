<?php

// src/Service/CertificationFoldersCdcFilesService.php

namespace App\Service;


use App\Entity\CdcFile;
use App\Entity\CertificationFolder;
use App\Entity\CertificationFoldersCdcFiles;
use App\Event\CertificationFolder\CertificationFolderEvents;
use App\Library\utils\enums\ActivityTypes;
use App\Library\utils\enums\CertificationFolderCdcStates;
use App\Library\utils\enums\CertificationFoldersCdcFilesStates;
use App\Library\utils\enums\DataProviders;
use App\Repository\CertificationFolderRepository;
use App\Repository\CertificationFoldersCdcFilesRepository;
use DateTime;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Throwable;

class CertificationFoldersCdcFilesService
{
    private ActivityService $activityService;
    private CertificationFoldersCdcFilesRepository $certificationFoldersCdcFilesRepository;
    private CertificationFolderRepository $certificationFolderRepository;
    private EventDispatcherInterface $dispatcher;

    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    public function __construct(ActivityService $activityService, CertificationFoldersCdcFilesRepository $certificationFoldersCdcFilesRepository, CertificationFolderRepository $certificationFolderRepository, EventDispatcherInterface $dispatcher)
    {
        $this->activityService = $activityService;
        $this->certificationFoldersCdcFilesRepository = $certificationFoldersCdcFilesRepository;
        $this->certificationFolderRepository = $certificationFolderRepository;
        $this->dispatcher = $dispatcher;
    }

    /**
     * @param CdcFile $cdcFile
     * @param CertificationFolder $certificationFolder
     * @return CertificationFoldersCdcFiles
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function create(CdcFile $cdcFile, CertificationFolder $certificationFolder): CertificationFoldersCdcFiles
    {
        $certificationFolderCdcFile = new CertificationFoldersCdcFiles();
        $certificationFolderCdcFile->setState(CertificationFoldersCdcFilesStates::EXPORTED());
        $certificationFolderCdcFile->setCertificationFolder($certificationFolder);
        $certificationFolderCdcFile->setCdcFile($cdcFile);
        $this->save($certificationFolderCdcFile);
        $this->activityService->create([
            'title' => "Le dossier a été exporté " . ($cdcFile->isGeneratedAutomatically() ? "automatiquement " : "") . "dans un fichier XML pour la CDC.",
            'type' => ActivityTypes::CDC()->getValue(),
            'eventTime' => $cdcFile->getCreatedOn(),
            'origin' => $cdcFile->isGeneratedAutomatically() ? 'Wedof' : null
        ], null, $certificationFolder);
        // EXPORTED beats only NOT_EXPORTED
        if ($certificationFolder->getCdcState() === CertificationFolderCdcStates::NOT_EXPORTED()->getValue()) {
            $certificationFolder->setCdcState(CertificationFolderCdcStates::EXPORTED()->getValue());
            $this->certificationFolderRepository->save($certificationFolder);
        }
        return $certificationFolderCdcFile;
    }

    /**
     * @param CertificationFoldersCdcFiles $certificationFolderCdcFile
     * @return CertificationFoldersCdcFiles
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function processedOk(CertificationFoldersCdcFiles $certificationFolderCdcFile): CertificationFoldersCdcFiles
    {
        $certificationFolderCdcFile->setState(CertificationFoldersCdcFilesStates::PROCESSED_OK()->getValue());
        $certificationFolderCdcFile->setErrorMessage(null);
        $this->save($certificationFolderCdcFile);
        $certificationFolder = $certificationFolderCdcFile->getCertificationFolder();

        if (!$certificationFolder->getRegistrationFolder() || $certificationFolder->getRegistrationFolder()->getType() !== DataProviders::CPF()->getValue()) {
            $certificationFolder->getAttendee()->setReadOnly(true);
        }

        $this->activityService->create([
            'title' => "Le dossier a été accroché avec succès auprès de la CDC.",
            'type' => ActivityTypes::CDC()->getValue(),
            'eventTime' => new DateTime()
        ], null, $certificationFolder);
        $certificationFolder->setCdcToExport(false); // Avoid exporting again a folder that is OK
        // PROCESSED_OK beats everything
        $certificationFolder->setCdcState(CertificationFolderCdcStates::PROCESSED_OK()->getValue());
        $this->certificationFolderRepository->save($certificationFolder);
        $this->dispatcher->dispatch(new CertificationFolderEvents($certificationFolder), CertificationFolderEvents::ACCROCHAGE_OK);
        return $certificationFolderCdcFile;
    }

    /**
     * @param CertificationFoldersCdcFiles $certificationFolderCdcFile
     * @param string $errorMessage
     * @return CertificationFoldersCdcFiles
     * @throws Throwable
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function processedKo(CertificationFoldersCdcFiles $certificationFolderCdcFile, string $errorMessage): CertificationFoldersCdcFiles
    {
        $certificationFolderCdcFile->setState(CertificationFoldersCdcFilesStates::PROCESSED_KO()->getValue());
        $certificationFolderCdcFile->setErrorMessage($errorMessage);
        $this->save($certificationFolderCdcFile);
        $certificationFolder = $certificationFolderCdcFile->getCertificationFolder();
        $this->activityService->create([
            'title' => "Le dossier a été refusé par la CDC.",
            'type' => ActivityTypes::CDC()->getValue(),
            'eventTime' => new DateTime(),
            'description' => $errorMessage,
        ], null, $certificationFolder);
        // PROCESSED_KO beats everything but PROCESSED_OK
        if ($certificationFolder->getCdcState() !== CertificationFolderCdcStates::PROCESSED_OK()->getValue()) {
            $certificationFolder->setCdcState(CertificationFolderCdcStates::PROCESSED_KO()->getValue());
            $cdcFile = $certificationFolderCdcFile->getCdcFile();
            if (!$cdcFile->isGeneratedExternally()) {
                $certificationFolder->setCdcToExport(false); // Avoid exporting again unless modified
            }
            $this->certificationFolderRepository->save($certificationFolder);
            $this->dispatcher->dispatch(new CertificationFolderEvents($certificationFolder), CertificationFolderEvents::ACCROCHAGE_KO);
        }
        $this->certificationFolderRepository->save($certificationFolder);
        return $certificationFolderCdcFile;
    }


    /**
     * @param CertificationFoldersCdcFiles $certificationFolderCdcFile
     * @return CertificationFoldersCdcFiles
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function abort(CertificationFoldersCdcFiles $certificationFolderCdcFile): CertificationFoldersCdcFiles
    {
        $certificationFolderCdcFile->setState(CertificationFoldersCdcFilesStates::ABORTED()->getValue());
        $certificationFolderCdcFile->setErrorMessage(null);
        $this->save($certificationFolderCdcFile);
        $certificationFolder = $certificationFolderCdcFile->getCertificationFolder();
        $this->activityService->create([
            'title' => "Un export XML pour ce dossier a été abandonné.",
            'type' => ActivityTypes::CDC()->getValue(),
            'eventTime' => new DateTime()
        ], null, $certificationFolder);
        // NOT_EXPORTED only beats EXPORTED if it is the only one
        if ($certificationFolder->getCdcState() === CertificationFolderCdcStates::EXPORTED()->getValue()) {
            $currentCertificationFoldersCdcFiles = $this->findCurrentCertificationFoldersCdcFiles($certificationFolder);
            if (!$currentCertificationFoldersCdcFiles) {
                $certificationFolder->setCdcState(CertificationFolderCdcStates::NOT_EXPORTED()->getValue());
                $this->certificationFolderRepository->save($certificationFolder);
            }
        }
        return $certificationFolderCdcFile;
    }

    /**
     * @param CertificationFolder $certificationFolder
     * @return CertificationFoldersCdcFiles|null
     */
    public function findCurrentCertificationFoldersCdcFiles(CertificationFolder $certificationFolder)
    {
        /* @var $currentCertificationFoldersCdcFiles CertificationFoldersCdcFiles */
        $currentCertificationFoldersCdcFiles = null;
        $certificationFoldersCdcFilesList = $this->certificationFoldersCdcFilesRepository->findAllNotAbortedOrderedBySubmissionDateDesc($certificationFolder);
        foreach ($certificationFoldersCdcFilesList as $certificationFoldersCdcFiles) {
            if ($certificationFoldersCdcFiles->getState() === CertificationFoldersCdcFilesStates::PROCESSED_OK()->getValue()) {
                // Latest PROCESSED_OK always wins
                $currentCertificationFoldersCdcFiles = $certificationFoldersCdcFiles;
                break;
            } else if ($certificationFoldersCdcFiles->getState() === CertificationFoldersCdcFilesStates::PROCESSED_KO()->getValue() &&
                (!$currentCertificationFoldersCdcFiles || $currentCertificationFoldersCdcFiles->getState() !== CertificationFoldersCdcFilesStates::PROCESSED_KO()->getValue())) {
                // PROCESSED_KO wins over already found file only if no more recent PROCESSED_KO has already been found
                $currentCertificationFoldersCdcFiles = $certificationFoldersCdcFiles;
            } else if ($certificationFoldersCdcFiles->getState() === CertificationFoldersCdcFilesStates::EXPORTED()->getValue() && !$currentCertificationFoldersCdcFiles) {
                // EXPORTED wins only over no file already found
                $currentCertificationFoldersCdcFiles = $certificationFoldersCdcFiles;
            }
        }
        return $currentCertificationFoldersCdcFiles;
    }

    /**
     * @param CdcFile $cdcFile
     * @return bool
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function hasCertificationFolderError(CdcFile $cdcFile): bool
    {
        return $this->certificationFoldersCdcFilesRepository->hasErrorCertificationFolders($cdcFile);
    }

    /**
     * @param CertificationFolder $certificationFolder
     * @return array|null
     */
    public function listByEntity(CertificationFolder $certificationFolder): ?array
    {
        return $this->certificationFoldersCdcFilesRepository->findBy(['certificationFolder' => $certificationFolder]);
    }

    /**
     * @param CertificationFoldersCdcFiles $certificationFoldersCdcFile
     */
    public function delete(CertificationFoldersCdcFiles $certificationFoldersCdcFile)
    {
        $this->certificationFoldersCdcFilesRepository->delete($certificationFoldersCdcFile);
    }

    //----------------
    // METHODES PRIVES
    //----------------

    /**
     * @param CertificationFoldersCdcFiles $certificationFolderCdcFile
     */
    private function save(CertificationFoldersCdcFiles $certificationFolderCdcFile)
    {
        $this->certificationFoldersCdcFilesRepository->save($certificationFolderCdcFile);
    }
}
