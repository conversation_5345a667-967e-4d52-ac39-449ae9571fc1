<?php
// src/Service/ActivityService.php
namespace App\Service;

use App\Entity\Activity;
use App\Entity\CertificationFolder;
use App\Entity\Proposal;
use App\Entity\RegistrationFolder;
use App\Entity\User;
use App\Exception\WedofBadRequestHttpException;
use App\Library\utils\enums\ActivityTypes;
use App\Library\utils\Tools;
use App\Repository\ActivityRepository;
use DateTime;
use DateTimeZone;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\QueryBuilder;
use Symfony\Component\DependencyInjection\ContainerInterface as Container;
use Symfony\Component\Security\Core\Security;
use Throwable;

class ActivityService
{
    private ActivityRepository $activityRepository;
    private Container $container;
    protected Security $security;

    const WEDOF_USER_ID = 77;
    const UPDATABLE_PROPERTIES = ['origin', 'link', 'title', 'description', 'previousValue', 'newValue', 'type', 'eventTime', 'eventEndTime', 'field', 'qualiopiIndicators', 'dueDate'];
    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    public function __construct(
        ActivityRepository $activityRepository,
        Container          $container, Security $security)
    {
        $this->activityRepository = $activityRepository;
        $this->container = $container;
        $this->security = $security;
    }

    /**
     * @param Activity $activity
     * @return void
     */
    public function delete(Activity $activity): void
    {
        $this->activityRepository->delete($activity);
    }

    /**
     * @param array $data
     * @param User|null $user
     * @param object $entity
     * @param bool $initializeActivitiesFromHistory
     * @param bool $done
     * @return Activity
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function create(array $data, ?User $user, object $entity, bool $initializeActivitiesFromHistory = true, bool $done = true): Activity
    {
        if ($initializeActivitiesFromHistory) { // Required to prevent infinite loop (as initializeActivitiesFromHistory calls create)
            $this->initializeActivitiesFromHistory($entity);
        }

        $activity = new Activity();
        if ($user && $user->getId() !== self::WEDOF_USER_ID) { // on enregistre pas les actions réalisées par Wedof comme étant de l'utilisateur Wedof mais origin 'Wedof'
            $activity->setUser($user);
        } else if (array_key_exists('origin', $data) && $data['origin'] === null) {
            $data['origin'] = 'Wedof';
        }
        if ($done) {
            $data['eventTime'] = $data['eventTime'] ?? (new DateTime())->setTimezone(new DateTimeZone("GMT"));
            $data['eventEndTime'] = $data['eventEndTime'] ?? $data['eventTime'];
            unset($data['done']);
        }
        if ($entity instanceof RegistrationFolder) {
            $activity->setEntityId($entity->getExternalId());
        } else if ($entity instanceof Proposal) {
            $activity->setEntityId($entity->getCode());
        } else {
            $activity->setEntityId($entity->getId());
        }

        $activity->setEntityClass(Tools::getClassName($entity));

        foreach (self::UPDATABLE_PROPERTIES as $property) {
            if (key_exists($property, $data)) {
                $setMethodName = "set" . ucwords($property);
                $activity->{$setMethodName}($data[$property]);
            }
        }
        return $this->save($activity);
    }

    /**
     * @param int|string $entityId
     * @param string $entityClass
     * @param object $entity
     * @param array $parameters
     * @return QueryBuilder
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function listByEntityReturnQueryBuilder($entityId, string $entityClass, object $entity, array $parameters): QueryBuilder
    {
        $this->initializeActivitiesFromHistory($entity);
        return $this->activityRepository->findAllByEntityReturnQueryBuilder($entityId, $entityClass, $parameters);
    }

    /**
     * @param string $entityClass
     * @param int $id
     * @param array $parameters
     * @return array|null
     */
    public function listByEntity(string $entityClass, int $id, array $parameters = []): ?array
    {
        $criterias = ['entityClass' => $entityClass, 'entityId' => $id];
        if (isset($parameters['criterias'])) {
            $criterias = array_merge($criterias, $parameters['criterias']);
        }
        return $this->activityRepository->findBy($criterias,$parameters['orderBy'] ?? null, $parameters['limit'] ?? null);
    }

    /**
     * @param User $user
     * @return QueryBuilder
     */
    public function listByUser(User $user): QueryBuilder
    {
        return $this->activityRepository->findAllByUserReturnQueryBuilder($user);
    }

    /**
     * @param int|string $entityId
     * @param string $entityClass
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countByEntity($entityId, string $entityClass): int
    {
        return $this->activityRepository->countByEntity($entityId, $entityClass);
    }

    /**
     * @param Activity $activity
     * @param array $data
     * @param User|null $user
     * @return Activity
     */
    public function update(Activity $activity, array $data, ?User $user): Activity
    {
        if ($user && $user->getId() != self::WEDOF_USER_ID) {
            $activity->setUser($user);
        } else if (array_key_exists('origin', $data) && $data['origin'] === null) {
            $data['origin'] = 'Wedof';
        }
        if (isset($data["type"]) && empty($data['type'])) {
            throw new WedofBadRequestHttpException("Erreur, vous devez renseigner un type.");
        } else if (isset($data["type"]) && !in_array($data['type'], ActivityTypes::valuesTypes())) {
            throw new WedofBadRequestHttpException("Erreur sur les valeurs renvoyées 'type', elles doivent être : " . join(",", ActivityTypes::valuesTypes()) . ".");
        }
        if (!empty($data['done'])) {
            $data['eventTime'] = $data['eventTime'] ?? (new DateTime())->setTimezone(new DateTimeZone("GMT"));
            $data['eventEndTime'] = $data['eventEndTime'] ?? $data['eventTime'];
            unset($data['done']);
        }
        foreach (self::UPDATABLE_PROPERTIES as $property) {
            if (key_exists($property, $data)) {
                $setMethodName = "set" . ucwords($property);
                $activity->{$setMethodName}($data[$property]);
            }
        }
        return $this->save($activity);
    }

    /**
     * @param Activity $activity
     * @param RegistrationFolder|CertificationFolder $entity
     * @return Activity
     */
    public function move(Activity $activity, $entity): Activity
    {
        $activity->setEntityClass($entity::CLASSNAME);
        $activity->setEntityId($entity->getEntityId());

        return $this->save($activity);
    }

    /**
     * @param object $entity
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    private function initializeActivitiesFromHistory(object $entity)
    {
        if ($entity instanceof RegistrationFolder && $this->countByEntity($entity->getExternalId(), Tools::getClassName($entity)) === 0) {
            /** @var RegistrationFolderService $registrationFolderService */
            $registrationFolderService = $this->container->get(RegistrationFolderService::class);
            $registrationFolderService->initializeActivitiesFromHistory($entity);
        }
    }

    /**
     * @param Activity $activity
     * @return Activity
     */
    private function save(Activity $activity): Activity
    {
        return $this->activityRepository->save($activity);
    }
}
