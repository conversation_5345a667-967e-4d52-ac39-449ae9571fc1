<?php
// src/Service/MailerService.php
namespace App\Service;

use App\Entity\CdcFile;
use App\Entity\Certification;
use App\Entity\CertificationFolder;
use App\Entity\CertificationPartner;
use App\Entity\CertifierAccess;
use App\Entity\Connection;
use App\Entity\Organism;
use App\Entity\RegistrationFolder;
use App\Entity\User;
use App\Entity\Webhook;
use App\Library\utils\enums\ConnectionTypes;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\enums\SubscriptionCertifierTypes;
use App\Library\utils\Tools;
use Exception;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Bridge\Twig\Mime\TemplatedEmail;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Email;

class MailerService implements LoggerAwareInterface
{
    private MailerInterface $mailer;
    private LoggerInterface $logger;

    public function __construct(MailerInterface $mailer)
    {
        $this->mailer = $mailer;
    }

    //-------------------
    // METHODES PUBLIQUES
    //-------------------
    /**
     * @param LoggerInterface $logger
     */
    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    //----------
    // APPLICATIONS
    //----------
    /**
     * @param Webhook $webhook
     * @return void
     * @throws TransportExceptionInterface
     */
    public function sendDeactivateWebhook(Webhook $webhook): void
    {
        $organism = $webhook->getOrganism();
        $user = $organism->getReseller() ? $organism->getReseller()->getMainUsers()[0] : $organism->getMainUsers()[0];
        $type = Tools::contains($webhook->getUrl(), 'zapier') ? 'zapier' : $webhook->getType();
        $events = "";
        foreach ($webhook->getEvents() as $event) {
            $events .= $event . ", ";
        }
        $email = (new TemplatedEmail())
            ->from(Tools::getFromEmail(Tools::getEnvValue('SENDER_EMAIL_NAME', 'Wedof'), Tools::getEnvValue('SENDER_EMAIL')))
            ->to($user->getEmail())
            ->priority(Email::PRIORITY_HIGH)
            ->subject("[Wedof] Votre webhook a été désactivé")
            ->htmlTemplate('emails/application/deactivate_webhook_email.html.twig')
            ->textTemplate('emails/application/deactivate_webhook_email.txt.twig')
            ->context([
                'recipientName' => $user->getFirstName() . " " . $user->getLastName(),
                'type' => $type,
                'events' => $events,
                'organismName' => $organism->getName(),
                'webhookName' => $webhook->getName(),
                'webhookUrl' => $webhook->getUrl()
            ]);
        if ($webhook->getType() === 'salesforce') {
            $email->bcc(Tools::getEnvValue('SUPPORT_EMAIL'));
        }

        $this->send($email);
    }

    //----------
    // CERTIFIER
    //----------
    /**
     * @param CertifierAccess $certifierAccess
     * @param array $recipientEmail
     * @return string
     * @throws TransportExceptionInterface
     */
    public function sendInvitation(CertifierAccess $certifierAccess, array $recipientEmail = []): string
    {
        $invitationUrl = $certifierAccess->getInviteLink();
        $certifier = $certifierAccess->getCertifier();
        $recipientEmail = !empty($recipientEmail) ? $recipientEmail : [$certifierAccess->getEmail()];

        if (!empty($certifier->getSendAs())) {
            $senderEmailFromName = $certifier->isNonDiffusible() ? null : $certifier->getSendAsName();
            $senderEmail = $certifier->getSendAsEmail();
            $replyTo = $senderEmail;
        } else {
            $senderEmailFromName = Tools::getEnvValue('SENDER_EMAIL_NAME', 'Wedof');
            $senderEmail = Tools::getEnvValue('SENDER_EMAIL');
            $replyTo = $this->getPublicOrganismEmail($certifier);
        }

        $email = (new TemplatedEmail())
            ->from(Tools::getFromEmail($senderEmailFromName, $senderEmail))
            ->to(...$recipientEmail)
            ->replyTo($replyTo)
            ->priority(Email::PRIORITY_HIGH)
            ->subject("[Wedof] Demande de partage des données de vos dossiers de formation avec " . $certifier->getName())
            ->htmlTemplate('emails/partner/invitation_email.html.twig')
            ->textTemplate('emails/partner/invitation_email.txt.twig')
            ->context([
                'senderName' => $certifier->getOwnedBy()->getName(),
                'recipientName' => $recipientName ?? $certifierAccess->getFullName(),
                'invitationUrl' => $invitationUrl,
                'senderOrganismName' => $certifier->getName()
            ]);

        foreach ($certifierAccess->getCertifier()->getMainUsers() as $certifierUser) {
            $email->addCc($certifierUser->getEmail());
        }

        $this->send($email, true);
        return Tools::obfuscateEmailAddress($certifierAccess->getEmail());
    }


    /**
     * @param CertifierAccess $certifierAccess
     * @throws TransportExceptionInterface
     */
    public function sendAcceptInvitation(CertifierAccess $certifierAccess): void
    {
        foreach ($certifierAccess->getCertifier()->getMainUsers() as $recipient) {
            $email = (new TemplatedEmail())
                ->from(Tools::getFromEmail(Tools::getEnvValue('SENDER_EMAIL_NAME', 'Wedof'), Tools::getEnvValue('SENDER_EMAIL')))
                ->to($recipient->getEmail())
                ->priority(Email::PRIORITY_HIGH)
                ->subject("[Wedof] L'organisme " . $certifierAccess->getPartner()->getName() . " a autorisé l'accès à ses dossiers")
                ->htmlTemplate('emails/certifier/invitation_accept_email.html.twig')
                ->textTemplate('emails/certifier/invitation_accept_email.txt.twig')
                ->context([
                    'recipientName' => $recipient->getName(),
                    'partnerOrganismName' => $certifierAccess->getPartner()->getName()
                ]);

            $this->send($email);
        }
    }

    /**
     * @param CertifierAccess $certifierAccess
     * @throws TransportExceptionInterface
     */
    public function sendRefuseInvitation(CertifierAccess $certifierAccess): void
    {
        foreach ($certifierAccess->getCertifier()->getMainUsers() as $recipient) {
            $email = (new TemplatedEmail())
                ->from(Tools::getFromEmail(Tools::getEnvValue('SENDER_EMAIL_NAME', 'Wedof'), Tools::getEnvValue('SENDER_EMAIL')))
                ->to($recipient->getEmail())
                ->priority(Email::PRIORITY_HIGH)
                ->subject("[Wedof] L'organisme " . $certifierAccess->getPartner()->getName() . " a refusé l'accès à ses dossiers")
                ->htmlTemplate('emails/certifier/invitation_refuse_email.html.twig')
                ->textTemplate('emails/certifier/invitation_refuse_email.txt.twig')
                ->context([
                    'recipientName' => $recipient->getName(),
                    'partnerOrganismName' => $certifierAccess->getPartner()->getName()
                ]);

            $this->send($email);
        }
    }

    /**
     * @param CertifierAccess $certifierAccess
     * @throws TransportExceptionInterface
     */
    public function sendTerminateInvitationToCertifier(CertifierAccess $certifierAccess): void
    {
        foreach ($certifierAccess->getCertifier()->getMainUsers() as $recipient) {
            $email = (new TemplatedEmail())
                ->from(Tools::getFromEmail(Tools::getEnvValue('SENDER_EMAIL_NAME', 'Wedof'), Tools::getEnvValue('SENDER_EMAIL')))
                ->to($recipient->getEmail())
                ->priority(Email::PRIORITY_HIGH)
                ->subject("[Wedof] L'organisme " . $certifierAccess->getPartner()->getName() . " a révoqué l'accès à ses dossiers")
                ->htmlTemplate('emails/certifier/invitation_terminate_email.html.twig')
                ->textTemplate('emails/certifier/invitation_terminate_email.txt.twig')
                ->context([
                    'recipientName' => $recipient->getName(),
                    'partnerOrganismName' => $certifierAccess->getPartner()->getName()
                ]);

            $this->send($email);
        }
    }

    /**
     * @param CertifierAccess $certifierAccess
     * @throws TransportExceptionInterface
     */
    public function sendTerminateInvitationToPartner(CertifierAccess $certifierAccess): void
    {
        $certifier = $certifierAccess->getCertifier();
        $partner = $certifierAccess->getPartner();
        if (!empty($certifier->getSendAs())) {
            $senderEmailFromName = $certifier->isNonDiffusible() ? null : $certifier->getSendAsName();
            $senderEmail = $certifier->getSendAsEmail();
            $replyTo = $senderEmail;
        } else {
            $senderEmailFromName = Tools::getEnvValue('SENDER_EMAIL_NAME', 'Wedof');
            $senderEmail = Tools::getEnvValue('SENDER_EMAIL');
            $replyTo = $this->getPublicOrganismEmail($certifier);
        }
        foreach ($partner->getMainUsers() as $recipient) {
            $email = (new TemplatedEmail())
                ->from(Tools::getFromEmail($senderEmailFromName, $senderEmail))
                ->to($recipient->getEmail())
                ->replyTo($replyTo)
                ->priority(Email::PRIORITY_HIGH)
                ->subject("[Wedof] Le certificateur " . $certifier->getName() . " a révoqué l'accès à vos dossiers")
                ->htmlTemplate('emails/certifier/invitation_terminate_email_to_partner.html.twig')
                ->textTemplate('emails/certifier/invitation_terminate_email_to_partner.txt.twig')
                ->context([
                    'recipientName' => $recipient->getName(),
                    'certifierName' => $certifier->getName()
                ]);

            $this->send($email);
        }
    }

    /**
     * @param Certification $certification
     * @param Organism $certifier
     * @return void
     * @throws TransportExceptionInterface
     */
    public function sendAlertOnCertifierRemoved(Certification $certification, Organism $certifier): void
    {
        $email = (new TemplatedEmail())
            ->from(Tools::getFromEmail(Tools::getEnvValue('SENDER_EMAIL_NAME', 'Wedof'), Tools::getEnvValue('SENDER_EMAIL')))
            ->to(Tools::getEnvValue('SUPPORT_EMAIL'))
            ->priority(Email::PRIORITY_HIGH)
            ->subject("[Wedof Internal] Certificateur retiré d'une certification")
            ->htmlTemplate('emails/certifier/alert_removed_certifier_email.html.twig')
            ->textTemplate('emails/certifier/alert_removed_certifier_email.txt.twig')
            ->context([
                'certifierName' => $certifier->getName(),
                'certifierSiret' => $certifier->getSiret(),
                'certificationExternalId' => $certification->getExternalId()
            ]);

        $this->send($email);
    }

    //------
    // FILES
    //------

    /**
     * @param Organism $organism
     * @param User $user
     * @param string $pathToFile
     * @param array $stats
     * @param bool $isAutomatedGeneration
     * @throws TransportExceptionInterface
     */
    public function sendCdcXMLFile(Organism $organism, User $user, string $pathToFile, array $stats, bool $isAutomatedGeneration): void
    {
        if ($isAutomatedGeneration) {
            $email = (new TemplatedEmail())
                ->from(Tools::getFromEmail(Tools::getEnvValue('SENDER_EMAIL_NAME', 'Wedof'), Tools::getEnvValue('SENDER_EMAIL')))
                ->to(Tools::getEnvValue('SUPPORT_EMAIL'))
                ->priority(Email::PRIORITY_HIGH)
                ->subject("[Wedof][Accrochage automatique] Le fichier XML de " . $organism->getName())
                ->htmlTemplate('emails/certifier/accrochage_automatic_wedof_xml_email.html.twig')
                ->textTemplate('emails/certifier/accrochage_automatic_wedof_xml_email.txt.twig')
                ->attachFromPath($pathToFile)
                ->context([
                    'stats' => $stats,
                    'organismName' => $organism->getName(),
                    'organismSiret' => $organism->getSiret()
                ]);
            $this->send($email);
        } else {
            $isTrialSubscription = $organism->getSubscription()->getCertifierType() === SubscriptionCertifierTypes::TRIAL()->getValue();
            $email = (new TemplatedEmail())
                ->from(Tools::getFromEmail(Tools::getEnvValue('SENDER_EMAIL_NAME', 'Wedof'), Tools::getEnvValue('SENDER_EMAIL')))
                ->to($user->getEmail())
                ->bcc(Tools::getEnvValue('SUPPORT_EMAIL'))
                ->priority(Email::PRIORITY_HIGH)
                ->subject("[Wedof][Accrochage certificateur] Votre fichier XML")
                ->htmlTemplate('emails/certifier/accrochage_xml_email.html.twig')
                ->textTemplate('emails/certifier/accrochage_xml_email.txt.twig')
                ->attachFromPath($pathToFile)
                ->context([
                    'recipientName' => $user->getName(),
                    'stats' => $stats,
                    'isInTrialSubscription' => $isTrialSubscription
                ]);
            $this->send($email);
        }
    }

    /**
     * @param Organism $organism
     * @param User $user
     * @param string $errorMessage
     * @param string|null $externalIdCertification
     * @param bool $isAutomatedGeneration
     * @throws TransportExceptionInterface
     */
    public function sendCdcXMLError(Organism $organism, User $user, string $errorMessage, bool $isAutomatedGeneration, string $externalIdCertification = null): void
    {
        $template = $isAutomatedGeneration ? 'emails/certifier/accrochage_automatic_wedof_error_email' : 'emails/certifier/accrochage_error_email';
        $email = (new TemplatedEmail())
            ->from(Tools::getFromEmail(Tools::getEnvValue('SENDER_EMAIL_NAME', 'Wedof'), Tools::getEnvValue('SENDER_EMAIL')))
            ->bcc(Tools::getEnvValue('SUPPORT_EMAIL'))
            ->priority(Email::PRIORITY_HIGH)
            ->htmlTemplate($template . '.html.twig')
            ->textTemplate($template . '.txt.twig');

        if ($isAutomatedGeneration) {
            $email
                ->to(Tools::getEnvValue('SUPPORT_EMAIL'))
                ->subject("[Wedof][Accrochage automatique][" . $organism->getName() . "] Erreur lors de la génération ou le dépôt du XML")
                ->context([
                    'externalIdCertification' => $externalIdCertification,
                    'errorMessage' => $errorMessage,
                    'organismName' => $organism->getName(),
                    'organismSiret' => $organism->getSiret()
                ]);
        } else {
            $email
                ->to($user->getEmail())
                ->subject("[Wedof][Accrochage certificateur] Erreur lors de la génération du fichier XML")
                ->context([
                    'recipientName' => $user->getName(),
                    'externalIdCertification' => $externalIdCertification,
                    'errorMessage' => $errorMessage
                ]);
        }
        $this->send($email);
    }

    /**
     * @param CdcFile $cdcFile
     * @param array $stats
     * @throws TransportExceptionInterface
     */
    public function sendRecapitulativeXmlTreatment(CdcFile $cdcFile, array $stats): void
    {
        $organism = $cdcFile->getOrganism();
        $user = $organism->getMainUsers()[0];
        $email = (new TemplatedEmail())
            ->from(Tools::getFromEmail(Tools::getEnvValue('SENDER_EMAIL_NAME', 'Wedof'), Tools::getEnvValue('SENDER_EMAIL')))
            ->to($user->getEmail())
            ->bcc(Tools::getEnvValue('SUPPORT_EMAIL'))
            ->priority(Email::PRIORITY_HIGH)
            ->subject("[Wedof][Accrochage automatique] Bilan quotidien")
            ->htmlTemplate('emails/certifier/receipt_xml_accrochage_automatic_email.html.twig')
            ->textTemplate('emails/certifier/receipt_xml_accrochage_automatic_email.txt.twig')
            ->context([
                'user' => $user->getName(),
                'organism' => $organism->getName(),
                'stats' => $stats,
                'idCdcFile' => $cdcFile->getId()
            ]);

        $this->send($email);
    }

    /**
     * @param User $user
     * @param string $reportFilePath
     * @param string $reportFileName
     * @throws TransportExceptionInterface
     */
    public function sendCpfCatalogUploadReport(User $user, string $reportFilePath, string $reportFileName): void
    {
        $email = (new TemplatedEmail())
            ->from(Tools::getFromEmail(Tools::getEnvValue('SENDER_EMAIL_NAME', 'Wedof'), Tools::getEnvValue('SENDER_EMAIL')))
            ->to($user->getEmail())
            ->bcc(Tools::getEnvValue('SUPPORT_EMAIL'))
            ->priority(Email::PRIORITY_HIGH)
            ->subject("[Wedof][Catalogue EDOF] Import réussi")
            ->htmlTemplate('emails/training/catalog_upload_report_email.html.twig')
            ->textTemplate('emails/training/catalog_upload_report_email.txt.twig')
            ->attachFromPath($reportFilePath, $reportFileName)
            ->context([
                'recipientName' => $user->getName(),
            ]);
        $this->send($email);
    }

    /**
     * @param User $user
     * @param string $xmlFilePath
     * @param string $reportFilePath
     * @throws TransportExceptionInterface
     */
    public function sendCpfCatalogXMLFile(User $user, string $xmlFilePath, string $reportFilePath): void
    {
        $email = (new TemplatedEmail())
            ->from(Tools::getFromEmail(Tools::getEnvValue('SENDER_EMAIL_NAME', 'Wedof'), Tools::getEnvValue('SENDER_EMAIL')))
            ->to($user->getEmail())
            ->bcc(Tools::getEnvValue('SUPPORT_EMAIL'))
            ->priority(Email::PRIORITY_HIGH)
            ->subject("[Wedof][Catalogue EDOF] Votre fichier XML")
            ->htmlTemplate('emails/training/catalog_export_email.html.twig')
            ->textTemplate('emails/training/catalog_export_email.txt.twig')
            ->attachFromPath($xmlFilePath)
            ->attachFromPath($reportFilePath)
            ->context([
                'recipientName' => $user->getName(),
            ]);
        $this->send($email);
    }

    /**
     * @param User $user
     * @param string $errorMessage
     * @throws TransportExceptionInterface
     */
    public function sendCpfCatalogXMLErrorToWedof(User $user, string $errorMessage): void
    {
        $email = (new TemplatedEmail())
            ->from(Tools::getFromEmail(Tools::getEnvValue('SENDER_EMAIL_NAME', 'Wedof'), Tools::getEnvValue('SENDER_EMAIL')))
            ->to(Tools::getEnvValue('SUPPORT_EMAIL'))
            ->priority(Email::PRIORITY_HIGH)
            ->subject("[Wedof][Catalogue EDOF] Erreur fichier XML")
            ->htmlTemplate('emails/training/catalog_export_error_wedof.html.twig')
            ->textTemplate('emails/training/catalog_export_error_wedof.txt.twig')
            ->context([
                'userEmail' => $user->getEmail(),
                'errorMessage' => $errorMessage
            ]);
        $this->send($email);
    }


    /**
     * @param User $user
     * @throws TransportExceptionInterface
     */
    public function sendCpfCatalogXMLErrorToCustomer(User $user): void
    {
        $email = (new TemplatedEmail())
            ->from(Tools::getFromEmail(Tools::getEnvValue('SENDER_EMAIL_NAME', 'Wedof'), Tools::getEnvValue('SENDER_EMAIL')))
            ->to($user->getEmail())
            ->bcc(Tools::getEnvValue('SUPPORT_EMAIL'))
            ->priority(Email::PRIORITY_HIGH)
            ->subject("[Wedof][Catalogue EDOF] Erreur dans l'export XML")
            ->htmlTemplate('emails/training/catalog_export_error_customer.html.twig')
            ->textTemplate('emails/training/catalog_export_error_customer.txt.twig')
            ->context([
                'recipientName' => $user->getName(),
            ]);
        $this->send($email);
    }

    //--------
    // PARTNER
    //--------

    /**
     * @param Certification $certification
     * @param User $user
     * @param array $body
     * @return void
     * @throws TransportExceptionInterface
     */
    public function sendInviteCertifierToWedofTeam(Certification $certification, User $user, array $body): void
    {
        $templatePath = 'emails/certifier/invite_certifier_wedofteam_email';
        $defaultCertifier = $certification->getDefaultCertifier();
        $email = (new TemplatedEmail())
            ->from(Tools::getFromEmail(Tools::getEnvValue('SENDER_EMAIL_NAME', 'Wedof'), Tools::getEnvValue('SENDER_EMAIL')))
            ->to(Tools::getEnvValue('SUPPORT_EMAIL'))
            ->priority(Email::PRIORITY_HIGH)
            ->subject("[Wedof Internal] Invitation d'un certificateur sur Wedof par un partenaire")
            ->htmlTemplate($templatePath . '.html.twig')
            ->textTemplate($templatePath . '.txt.twig')
            ->context([
                'userName' => $user->getName(),
                'userEmail' => $user->getEmail(),
                'userOrganismName' => $user->getMainOrganism()->getName(),
                'certificationName' => $certification->getName(),
                'certificationCertifInfo' => $certification->getCertifInfo(),
                'certificationExternalId' => $certification->getExternalId(),
                'certificationCertifierName' => $defaultCertifier->getName(),
                'certificationCertifierContactName' => $body['name'],
                'certificationCertifierContactEmail' => $body['email'],
                'certificationCertifierContactPhone' => $body['phoneNumber'] ?? null,
                'certificationCertifierEmail' => $defaultCertifier->getEmails()[0] ?? "pas d'adresse email connue",
                'certificationCertifierPhone' => $defaultCertifier->getPhones()[0] ?? "pas de téléphone connu"
            ]);

        $this->send($email);
    }

    /**
     * @param CertificationPartner $certificationPartner
     * @param User $user
     * @return void
     * @throws TransportExceptionInterface
     */
    public function sendCertificationPartnerProcessingToWedofTeam(CertificationPartner $certificationPartner, User $user): void
    {
        $templatePath = 'emails/certificationPartner/certification_partner_processing_wedofteam_email';
        $organism = $user->getMainOrganism();
        $certification = $certificationPartner->getCertification();
        $ownedBy = $certification->getDefaultCertifier()->getOwnedBy();
        $comment = $certificationPartner->getComment();
        $email = (new TemplatedEmail())
            ->from(Tools::getFromEmail(Tools::getEnvValue('SENDER_EMAIL_NAME', 'Wedof'), Tools::getEnvValue('SENDER_EMAIL')))
            ->to(Tools::getEnvValue('SUPPORT_EMAIL'))
            ->priority(Email::PRIORITY_HIGH)
            ->subject("[Wedof Internal] Nouvelle demande de partenariat sur la certification " . $certification->getExternalId())
            ->htmlTemplate($templatePath . '.html.twig')
            ->textTemplate($templatePath . '.txt.twig')
            ->context([
                'userName' => $user->getName(),
                'userEmail' => $user->getEmail(),
                'userOrganismName' => $organism->getName(),
                'userOrganismSiret' => $organism->getSiret(),
                'certificationName' => $certification->getName(),
                'certificationCertifInfo' => $certification->getCertifInfo(),
                'certificationExternalId' => $certification->getExternalId(),
                'certificationCertifierName' => $certification->getDefaultCertifier()->getName(),
                'certificationCertifierUserName' => $ownedBy ? $ownedBy->getName() : null,
                'certificationCertifierUserEmail' => $ownedBy ? $ownedBy->getEmail() : null,
                'comment' => $comment,
            ]);
        $this->send($email);
    }

    /**
     * @param CertificationPartner $certificationPartner
     * @param User $partnerUser
     * @param Organism $certifier
     * @return void
     * @throws TransportExceptionInterface
     */
    public function sendCertificationPartnerProcessingToCertifier(CertificationPartner $certificationPartner, User $partnerUser, Organism $certifier): void
    {
        $certification = $certificationPartner->getCertification();
        $certifierUser = $certifier->getOwnedBy();
        $partner = $certificationPartner->getPartner();
        $comment = $certificationPartner->getComment();
        $templatePath = 'emails/certificationPartner/certification_partner_processing_certifier_email';
        $email = (new TemplatedEmail())
            ->from(Tools::getFromEmail(Tools::getEnvValue('SENDER_EMAIL_NAME', 'Wedof'), Tools::getEnvValue('SENDER_EMAIL')))
            ->to($certifierUser->getEmail()) // TODO check if we should send to other emails (e.g. the ones on the organism)
            ->priority(Email::PRIORITY_HIGH)
            ->subject("[Wedof] Nouvelle demande de partenariat sur la certification " . $certification->getExternalId())
            ->htmlTemplate($templatePath . '.html.twig')
            ->textTemplate($templatePath . '.txt.twig')
            ->context([
                'partnerUser' => $partnerUser,
                'partnerOrganismName' => $partner->getName(),
                'certificationFullName' => $this->getCertificationFullName($certificationPartner),
                'certifierUserName' => $certifierUser->getName(),
                'comment' => $comment,
                'certifierPartnerLink' => $this->getCertifierLinkFromCertificationPartner($certificationPartner)
            ]);
        $this->send($email);
    }

    /**
     * @param CertificationPartner $certificationPartner
     * @param Organism $certifier
     * @throws TransportExceptionInterface
     */
    public function sendCertificationPartnerProcessingToPartner(CertificationPartner $certificationPartner, Organism $certifier): void
    {
        $templatePath = 'emails/certificationPartner/certification_partner_processing_partner_email';
        $partnerUser = $certificationPartner->getPartner()->getOwnedBy();
        if ($partnerUser) {
            $certification = $certificationPartner->getCertification();
            $email = (new TemplatedEmail())
                ->from(Tools::getFromEmail(Tools::getEnvValue('SENDER_EMAIL_NAME', 'Wedof'), Tools::getEnvValue('SENDER_EMAIL')))
                ->to($partnerUser->getEmail())
                ->replyTo($this->getPublicOrganismEmail($certifier))
                ->priority(Email::PRIORITY_HIGH)
                ->subject("[Wedof] Votre demande de partenariat sur la certification " . $certification->getExternalId() . " est disponible sur Wedof")
                ->htmlTemplate($templatePath . '.html.twig')
                ->textTemplate($templatePath . '.txt.twig')
                ->context([
                    'certificationFullName' => $this->getCertificationFullName($certificationPartner),
                    'partnerUserName' => $partnerUser->getName(),
                    'certifierOrganismName' => $certifier->getName(),
                    'certifierPartnerLink' => $certificationPartner->getPartnerLink()
                ]);
            $this->send($email);
        }
    }

    /**
     * @param CertificationPartner $certificationPartner
     * @param User $partnerUser
     * @return void
     * @throws TransportExceptionInterface
     */
    public function sendCertificationPartnerAbortedToCertifier(CertificationPartner $certificationPartner, User $partnerUser): void
    {
        $certification = $certificationPartner->getCertification();
        $certifierUser = $certificationPartner->getCertifier()->getOwnedBy();
        $partner = $certificationPartner->getPartner();
        $templatePath = 'emails/certificationPartner/certification_partner_aborted_certifier_email';
        $email = (new TemplatedEmail())
            ->from(Tools::getFromEmail(Tools::getEnvValue('SENDER_EMAIL_NAME', 'Wedof'), Tools::getEnvValue('SENDER_EMAIL')))
            ->to($certifierUser->getEmail()) // TODO check if we should send to other emails (e.g. the ones on the organism)
            ->priority(Email::PRIORITY_HIGH)
            ->subject("[Wedof] Demande de partenariat abandonnée sur la certification " . $certification->getExternalId())
            ->htmlTemplate($templatePath . '.html.twig')
            ->textTemplate($templatePath . '.txt.twig')
            ->context([
                'partnerUser' => $partnerUser,
                'partnerOrganismName' => $partner->getName(),
                'certificationFullName' => $this->getCertificationFullName($certificationPartner),
                'certifierUserName' => $certifierUser->getName(),
                'certifierPartnerLink' => $this->getCertifierLinkFromCertificationPartner($certificationPartner)
            ]);
        $this->send($email);
    }

    /**
     * @param CertificationPartner $certificationPartner
     * @param Organism $certifier
     * @throws TransportExceptionInterface
     */
    public function sendCertificationPartnerAbortedToPartner(CertificationPartner $certificationPartner, Organism $certifier): void
    {
        $templatePath = 'emails/certificationPartner/certification_partner_aborted_partner_email';
        $partnerUser = $certificationPartner->getPartner()->getOwnedBy();
        if ($partnerUser) {
            $certification = $certificationPartner->getCertification();
            $email = (new TemplatedEmail())
                ->from(Tools::getFromEmail(Tools::getEnvValue('SENDER_EMAIL_NAME', 'Wedof'), Tools::getEnvValue('SENDER_EMAIL')))
                ->to($partnerUser->getEmail())
                ->replyTo($this->getPublicOrganismEmail($certifier))
                ->priority(Email::PRIORITY_HIGH)
                ->subject("[Wedof] Demande de partenariat abandonnée sur la certification " . $certification->getExternalId())
                ->htmlTemplate($templatePath . '.html.twig')
                ->textTemplate($templatePath . '.txt.twig')
                ->context([
                    'certificationFullName' => $this->getCertificationFullName($certificationPartner),
                    'partnerUserName' => $partnerUser->getName(),
                    'certifierOrganismName' => $certifier->getName(),
                    'certifierPartnerLink' => $certificationPartner->getPartnerLink()
                ]);
            $this->send($email);
        }
    }

    /**
     * @param CertificationPartner $certificationPartner
     * @return void
     * @throws TransportExceptionInterface
     * @throws Exception
     */
    public function sendCertificationPartnerRefused(CertificationPartner $certificationPartner): void
    {
        $partnerUser = $certificationPartner->getPartner()->getOwnedBy();
        $templatePath = 'emails/certificationPartner/certification_partner_refused_email';
        if ($partnerUser && $partnerUser->getEmail()) {
            $certification = $certificationPartner->getCertification();
            $email = (new TemplatedEmail())
                ->from(Tools::getFromEmail(Tools::getEnvValue('SENDER_EMAIL_NAME', 'Wedof'), Tools::getEnvValue('SENDER_EMAIL')))
                ->to($partnerUser->getEmail()) // TODO check if we should send to other emails (e.g. the ones on the organism)
                ->replyTo($this->getPublicOrganismEmail($certificationPartner->getCertifier()))
                ->priority(Email::PRIORITY_HIGH)
                ->subject("[Wedof] Refus de votre demande de partenariat sur la certification " . $certification->getExternalId())
                ->htmlTemplate($templatePath . '.html.twig')
                ->textTemplate($templatePath . '.txt.twig')
                ->context([
                    'certificationFullName' => $this->getCertificationFullName($certificationPartner),
                    'partnerUserName' => $partnerUser->getName(),
                    'certifierPartnerLink' => $certificationPartner->getPartnerLink()
                ]);

            $this->send($email);
        }
    }

    /**
     * @param CertificationPartner $certificationPartner
     * @param User $partnerUser
     * @param Organism $certifier
     * @return void
     * @throws TransportExceptionInterface
     */
    public function sendCertificationPartnerReopenToCertifier(CertificationPartner $certificationPartner, User $partnerUser, Organism $certifier): void
    {
        $certification = $certificationPartner->getCertification();
        $certifierUser = $certifier->getOwnedBy();
        $partner = $certificationPartner->getPartner();
        $templatePath = 'emails/certificationPartner/certification_partner_reopen_certifier_email';
        $email = (new TemplatedEmail())
            ->from(Tools::getFromEmail(Tools::getEnvValue('SENDER_EMAIL_NAME', 'Wedof'), Tools::getEnvValue('SENDER_EMAIL')))
            ->to($certifierUser->getEmail()) // TODO check if we should send to other emails (e.g. the ones on the organism)
            ->priority(Email::PRIORITY_HIGH)
            ->subject("[Wedof] Réouverture d'une demande de partenariat sur la certification " . $certification->getExternalId())
            ->htmlTemplate($templatePath . '.html.twig')
            ->textTemplate($templatePath . '.txt.twig')
            ->context([
                'partnerUser' => $partnerUser,
                'partnerOrganismName' => $partner->getName(),
                'certificationFullName' => $this->getCertificationFullName($certificationPartner),
                'certifierUserName' => $certifierUser->getName(),
                'certifierPartnerLink' => $this->getCertifierLinkFromCertificationPartner($certificationPartner)
            ]);
        $this->send($email);
    }

    /**
     * @param CertificationPartner $certificationPartner
     * @param Organism $certifier
     * @return void
     * @throws TransportExceptionInterface
     */
    public function sendCertificationPartnerReopenToPartner(CertificationPartner $certificationPartner, Organism $certifier): void
    {
        $partnerUser = $certificationPartner->getPartner()->getOwnedBy();
        $templatePath = 'emails/certificationPartner/certification_partner_reopen_partner_email';
        if ($partnerUser && $partnerUser->getEmail()) {
            $certification = $certificationPartner->getCertification();
            $email = (new TemplatedEmail())
                ->from(Tools::getFromEmail(Tools::getEnvValue('SENDER_EMAIL_NAME', 'Wedof'), Tools::getEnvValue('SENDER_EMAIL')))
                ->to($partnerUser->getEmail()) // TODO check if we should send to other emails (e.g. the ones on the organism)
                ->replyTo($this->getPublicOrganismEmail($certifier))
                ->priority(Email::PRIORITY_HIGH)
                ->subject("[Wedof] Réouverture de votre demande de partenariat sur la certification " . $certification->getExternalId())
                ->htmlTemplate($templatePath . '.html.twig')
                ->textTemplate($templatePath . '.txt.twig')
                ->context([
                    'certificationFullName' => $this->getCertificationFullName($certificationPartner),
                    'partnerUserName' => $partnerUser->getName(),
                    'certifierPartnerLink' => $certificationPartner->getPartnerLink()
                ]);
            $this->send($email);
        }
    }

    /**
     * @param CertificationPartner $certificationPartner
     * @return void
     * @throws TransportExceptionInterface
     * @throws Exception
     */
    public function sendCertificationPartnerPendingActivationChanged(CertificationPartner $certificationPartner): void
    {
        $partnerUser = $certificationPartner->getPartner()->getOwnedBy();
        $templatePath = 'emails/certificationPartner/certification_partner_pending_activation_changed_email';
        if ($partnerUser && $partnerUser->getEmail()) {
            $pendingActivation = $certificationPartner->isPendingActivation();
            $certification = $certificationPartner->getCertification();
            $email = (new TemplatedEmail())
                ->from(Tools::getFromEmail(Tools::getEnvValue('SENDER_EMAIL_NAME', 'Wedof'), Tools::getEnvValue('SENDER_EMAIL')))
                ->to($partnerUser->getEmail()) // TODO check if we should send to other emails (e.g. the ones on the organism)
                ->replyTo($this->getPublicOrganismEmail($certificationPartner->getCertifier()))
                ->priority(Email::PRIORITY_HIGH)
                ->subject("[Wedof] Votre partenariat sur la certification " . $certification->getExternalId() . ($pendingActivation ? " est " : " n'est plus ") . "en cours d'activation")
                ->htmlTemplate($templatePath . '.html.twig')
                ->textTemplate($templatePath . '.txt.twig')
                ->context([
                    'certificationFullName' => $this->getCertificationFullName($certificationPartner),
                    'partnerUserName' => $partnerUser->getName(),
                    'pendingActivation' => $pendingActivation,
                    'certifierPartnerLink' => $certificationPartner->getPartnerLink()
                ]);
            $this->send($email);
        }
    }

    /**
     * @param CertificationPartner $certificationPartner
     * @return void
     * @throws TransportExceptionInterface
     */
    public function sendCertificationPartnerPendingSuspensionChanged(CertificationPartner $certificationPartner): void
    {
        $partnerUser = $certificationPartner->getPartner()->getOwnedBy();
        $templatePath = 'emails/certificationPartner/certification_partner_pending_suspension_changed_email';
        if ($partnerUser && $partnerUser->getEmail()) {
            $pendingSuspension = $certificationPartner->isPendingSuspension();
            $certification = $certificationPartner->getCertification();
            $email = (new TemplatedEmail())
                ->from(Tools::getFromEmail(Tools::getEnvValue('SENDER_EMAIL_NAME', 'Wedof'), Tools::getEnvValue('SENDER_EMAIL')))
                ->to($partnerUser->getEmail()) // TODO check if we should send to other emails (e.g. the ones on the organism)
                ->replyTo($this->getPublicOrganismEmail($certificationPartner->getCertifier()))
                ->priority(Email::PRIORITY_HIGH)
                ->subject("[Wedof] Votre partenariat sur la certification " . $certification->getExternalId() . ($pendingSuspension ? " est " : " n'est plus ") . "en cours de suspension")
                ->htmlTemplate($templatePath . '.html.twig')
                ->textTemplate($templatePath . '.txt.twig')
                ->context([
                    'certificationFullName' => $this->getCertificationFullName($certificationPartner),
                    'partnerUserName' => $partnerUser->getName(),
                    'pendingSuspension' => $pendingSuspension,
                    'certifierPartnerLink' => $certificationPartner->getPartnerLink()
                ]);
            $this->send($email);
        }
    }


    /**
     * @param CertificationPartner $certificationPartner
     * @return void
     * @throws TransportExceptionInterface
     * @throws Exception
     */
    public function sendCertificationPartnerPendingRevocationChanged(CertificationPartner $certificationPartner): void
    {
        $partnerUser = $certificationPartner->getPartner()->getOwnedBy();
        $templatePath = 'emails/certificationPartner/certification_partner_pending_revocation_changed_email';
        if ($partnerUser && $partnerUser->getEmail()) {
            $pendingRevocation = $certificationPartner->isPendingRevocation();
            $certification = $certificationPartner->getCertification();
            $email = (new TemplatedEmail())
                ->from(Tools::getFromEmail(Tools::getEnvValue('SENDER_EMAIL_NAME', 'Wedof'), Tools::getEnvValue('SENDER_EMAIL')))
                ->to($partnerUser->getEmail()) // TODO check if we should send to other emails (e.g. the ones on the organism)
                ->replyTo($this->getPublicOrganismEmail($certificationPartner->getCertifier()))
                ->priority(Email::PRIORITY_HIGH)
                ->subject("[Wedof] Votre partenariat sur la certification " . $certification->getExternalId() . ($pendingRevocation ? " est " : " n'est plus ") . "en cours de révocation")
                ->htmlTemplate($templatePath . '.html.twig')
                ->textTemplate($templatePath . '.txt.twig')
                ->context([
                    'certificationFullName' => $this->getCertificationFullName($certificationPartner),
                    'partnerUserName' => $partnerUser->getName(),
                    'pendingRevocation' => $pendingRevocation,
                    'certifierPartnerLink' => $certificationPartner->getPartnerLink()
                ]);
            $this->send($email);
        }
    }

    /**
     * @param CertificationPartner $certificationPartner
     * @param Organism $certifier
     * @return void
     * @throws TransportExceptionInterface
     */
    public function sendCertificationPartnerActiveToCertifier(CertificationPartner $certificationPartner, Organism $certifier): void
    {
        $certifierUser = $certifier->getOwnedBy();
        $partner = $certificationPartner->getPartner();
        $templatePath = 'emails/certificationPartner/certification_partner_active_certifier_email';
        if ($certifierUser && $certifierUser->getEmail()) {
            $certification = $certificationPartner->getCertification();
            $email = (new TemplatedEmail())
                ->from(Tools::getFromEmail(Tools::getEnvValue('SENDER_EMAIL_NAME', 'Wedof'), Tools::getEnvValue('SENDER_EMAIL')))
                ->to($certifierUser->getEmail()) // TODO check if we should send to other emails (e.g. the ones on the organism)
                ->priority(Email::PRIORITY_HIGH)
                ->subject("[Wedof] Le partenariat de l'organisme " . $partner->getName() . " sur votre certification " . $certification->getExternalId() . " est actif")
                ->htmlTemplate($templatePath . '.html.twig')
                ->textTemplate($templatePath . '.txt.twig')
                ->context([
                    'certificationFullName' => $this->getCertificationFullName($certificationPartner),
                    'certifierUserName' => $certifierUser->getName(),
                    'partnerOrganismName' => $partner->getName(),
                    'certifierPartnerLink' => $this->getCertifierLinkFromCertificationPartner($certificationPartner)
                ]);
            $this->send($email);
        }
    }

    /**
     * @param CertificationPartner $certificationPartner
     * @return void
     * @throws TransportExceptionInterface
     * @throws Exception
     */
    public function sendCertificationPartnerActiveToPartner(CertificationPartner $certificationPartner): void
    {
        $partnerUser = $certificationPartner->getPartner()->getOwnedBy();
        $templatePath = 'emails/certificationPartner/certification_partner_active_partner_email';
        if ($partnerUser && $partnerUser->getEmail()) {
            $certification = $certificationPartner->getCertification();
            $certifierEmail = $this->getPublicOrganismEmail($certificationPartner->getCertifier());
            $email = (new TemplatedEmail())
                ->from(Tools::getFromEmail(Tools::getEnvValue('SENDER_EMAIL_NAME', 'Wedof'), Tools::getEnvValue('SENDER_EMAIL')))
                ->to($partnerUser->getEmail()) // TODO check if we should send to other emails (e.g. the ones on the organism)
                ->priority(Email::PRIORITY_HIGH)
                ->subject("[Wedof] Votre partenariat sur la certification " . $certification->getExternalId() . " est actif")
                ->htmlTemplate($templatePath . '.html.twig')
                ->textTemplate($templatePath . '.txt.twig')
                ->context([
                    'certificationFullName' => $this->getCertificationFullName($certificationPartner),
                    'partnerUserName' => $partnerUser->getName(),
                    'certifierPartnerLink' => $certificationPartner->getPartnerLink()
                ]);
            if ($certifierEmail) {
                $email->replyTo($certifierEmail);
            }
            $this->send($email);
        }
    }

    /**
     * @param CertificationPartner $certificationPartner
     * @param Organism $certifier
     * @return void
     * @throws TransportExceptionInterface
     */
    public function sendCertificationPartnerSuspendedToCertifier(CertificationPartner $certificationPartner, Organism $certifier): void
    {
        $certifierUser = $certifier->getOwnedBy();
        $partner = $certificationPartner->getPartner();
        $templatePath = 'emails/certificationPartner/certification_partner_suspended_certifier_email';
        if ($certifierUser && $certifierUser->getEmail()) {
            $certification = $certificationPartner->getCertification();
            $email = (new TemplatedEmail())
                ->from(Tools::getFromEmail(Tools::getEnvValue('SENDER_EMAIL_NAME', 'Wedof'), Tools::getEnvValue('SENDER_EMAIL')))
                ->to($certifierUser->getEmail()) // TODO check if we should send to other emails (e.g. the ones on the organism)
                ->priority(Email::PRIORITY_HIGH)
                ->subject("[Wedof] Le partenariat de l'organisme " . $partner->getName() . " sur votre certification " . $certification->getExternalId() . " est suspendu")
                ->htmlTemplate($templatePath . '.html.twig')
                ->textTemplate($templatePath . '.txt.twig')
                ->context([
                    'certificationFullName' => $this->getCertificationFullName($certificationPartner),
                    'certifierUserName' => $certifierUser->getName(),
                    'partnerOrganismName' => $partner->getName(),
                    'certifierPartnerLink' => $this->getCertifierLinkFromCertificationPartner($certificationPartner),
                    'partnerSiret' => $partner->getSiret()
                ]);
            $this->send($email);
        }
    }

    /**
     * @param CertificationPartner $certificationPartner
     * @param array $parameters
     * @return void
     * @throws TransportExceptionInterface
     */
    public function sendCertificationPartnerSuspendedToPartner(CertificationPartner $certificationPartner, array $parameters = []): void
    {
        $partnerUser = $certificationPartner->getPartner()->getOwnedBy();
        $templatePath = 'emails/certificationPartner/certification_partner_suspended_partner_email';
        if ($partnerUser && $partnerUser->getEmail()) {
            $certification = $certificationPartner->getCertification();
            $certifierEmail = $this->getPublicOrganismEmail($certificationPartner->getCertifier());
            $email = (new TemplatedEmail())
                ->from(Tools::getFromEmail(Tools::getEnvValue('SENDER_EMAIL_NAME', 'Wedof'), Tools::getEnvValue('SENDER_EMAIL')))
                ->to($partnerUser->getEmail()) // TODO check if we should send to other emails (e.g. the ones on the organism)
                ->priority(Email::PRIORITY_HIGH)
                ->subject("[Wedof] Votre partenariat sur la certification " . $certification->getExternalId() . " est suspendu")
                ->htmlTemplate($templatePath . '.html.twig')
                ->textTemplate($templatePath . '.txt.twig')
                ->context([
                    'certificationFullName' => $this->getCertificationFullName($certificationPartner),
                    'partnerUserName' => $partnerUser->getName(),
                    'certifierPartnerLink' => $certificationPartner->getPartnerLink(isset($parameters['origin']) && $parameters['origin'] === 'audit' ? 'audit' : null),
                    'partnerSiret' => $certificationPartner->getPartner()->getSiret(),
                    'certifierEmail' => $certifierEmail,
                    'origin' => $parameters['origin'] ?? ''
                ]);
            if ($certifierEmail) {
                $email->replyTo($certifierEmail);
            }
            $this->send($email);
        }
    }

    /**
     * @param CertificationPartner $certificationPartner
     * @param Organism $certifier
     * @param array $parameters
     * @return void
     * @throws TransportExceptionInterface
     */
    public function sendCertificationPartnerRevokedToCertifier(CertificationPartner $certificationPartner, Organism $certifier, array $parameters = []): void
    {
        $certifierUser = $certifier->getOwnedBy();
        $partner = $certificationPartner->getPartner();
        $templatePath = 'emails/certificationPartner/certification_partner_revoked_certifier_email';
        if ($certifierUser && $certifierUser->getEmail()) {
            $certification = $certificationPartner->getCertification();
            $email = (new TemplatedEmail())
                ->from(Tools::getFromEmail(Tools::getEnvValue('SENDER_EMAIL_NAME', 'Wedof'), Tools::getEnvValue('SENDER_EMAIL')))
                ->to($certifierUser->getEmail()) // TODO check if we should send to other emails (e.g. the ones on the organism)
                ->priority(Email::PRIORITY_HIGH)
                ->subject("[Wedof] Le partenariat de l'organisme " . $partner->getName() . " sur votre certification " . $certification->getExternalId() . " est révoqué")
                ->htmlTemplate($templatePath . '.html.twig')
                ->textTemplate($templatePath . '.txt.twig')
                ->context([
                    'certificationFullName' => $this->getCertificationFullName($certificationPartner),
                    'certifierUserName' => $certifierUser->getName(),
                    'partnerOrganismName' => $partner->getName(),
                    'certifierPartnerLink' => $this->getCertifierLinkFromCertificationPartner($certificationPartner),
                    'partnerSiret' => $partner->getSiret(),
                    'cause' => $parameters['cause'] ?? ''
                ]);
            $this->send($email);
        }
    }

    /**
     * @param CertificationPartner $certificationPartner
     * @param array $parameters
     * @return void
     * @throws TransportExceptionInterface
     */
    public function sendCertificationPartnerRevokedToPartner(CertificationPartner $certificationPartner, array $parameters = []): void
    {
        $partnerUser = $certificationPartner->getPartner()->getOwnedBy();
        $templatePath = 'emails/certificationPartner/certification_partner_revoked_partner_email';
        if ($partnerUser && $partnerUser->getEmail()) {
            $certification = $certificationPartner->getCertification();
            $certifierEmail = $this->getPublicOrganismEmail($certificationPartner->getCertifier());
            $email = (new TemplatedEmail())
                ->from(Tools::getFromEmail(Tools::getEnvValue('SENDER_EMAIL_NAME', 'Wedof'), Tools::getEnvValue('SENDER_EMAIL')))
                ->to($partnerUser->getEmail()) // TODO check if we should send to other emails (e.g. the ones on the organism)
                ->priority(Email::PRIORITY_HIGH)
                ->subject("[Wedof] Votre partenariat sur la certification " . $certification->getExternalId() . " est révoqué")
                ->htmlTemplate($templatePath . '.html.twig')
                ->textTemplate($templatePath . '.txt.twig')
                ->context([
                    'certificationFullName' => $this->getCertificationFullName($certificationPartner),
                    'partnerUserName' => $partnerUser->getName(),
                    'certifierPartnerLink' => $certificationPartner->getPartnerLink(),
                    'partnerSiret' => $certificationPartner->getPartner()->getSiret(),
                    'certifierEmail' => $certifierEmail,
                    'cause' => empty($parameters) ? '' : ($parameters['cause'] ?? '')
                ]);
            if ($certifierEmail) {
                $email->replyTo($certifierEmail);
            }
            $this->send($email);
        }
    }


    /**
     * @param Certification $certification
     * @param string $message
     * @return void
     * @throws TransportExceptionInterface
     */
    public function sendUpdateCertificationPartnersIssueToWedofTeam(Certification $certification, string $message): void
    {
        $templatePath = 'emails/technical/update_certification_partners_issue';
        $email = (new TemplatedEmail())
            ->from(Tools::getFromEmail(Tools::getEnvValue('SENDER_EMAIL_NAME', 'Wedof'), Tools::getEnvValue('SENDER_EMAIL')))
            ->to(Tools::getEnvValue('SUPPORT_EMAIL'))
            ->priority(Email::PRIORITY_HIGH)
            ->subject("[Wedof Internal] Erreur sur la synchro des partenaires d'une certification")
            ->htmlTemplate($templatePath . '.html.twig')
            ->context([
                'certificationName' => $certification->getName(),
                'certificationCertifInfo' => $certification->getCertifInfo(),
                'certificationExternalId' => $certification->getExternalId(),
                'certificationCertifierName' => $certification->getDefaultCertifier()->getName(),
                'message' => $message
            ]);

        $this->send($email);
    }

    private function getCertificationFullName(CertificationPartner $certificationPartner): string
    {
        $certification = $certificationPartner->getCertification();
        return $certification->getExternalId() . ' "' . $certification->getName() . '"';
    }

    private function getCertifierLinkFromCertificationPartner(CertificationPartner $certificationPartner): string
    {
        $certifInfo = $certificationPartner->getCertification()->getCertifInfo();
        $partnerSiret = $certificationPartner->getPartner()->getSiret();
        return Tools::getEnvValue('WEDOF_BASE_URI') . '/certification/partenariats/' . $certifInfo . '/' . $partnerSiret;
    }

    //----------
    // TECHNICAL
    //----------
    /**
     * @throws TransportExceptionInterface
     */
    public function sendAlertOnSessionMinDates(): void
    {
        $email = (new TemplatedEmail())
            ->from(Tools::getFromEmail(Tools::getEnvValue('SENDER_EMAIL_NAME', 'Wedof'), Tools::getEnvValue('SENDER_EMAIL')))
            ->to(Tools::getEnvValue('SUPPORT_EMAIL'))
            ->priority(Email::PRIORITY_HIGH)
            ->subject("[Wedof Internal] Problème sur le calcul du sessionMinDates")
            ->htmlTemplate('emails/technical/alert_session_min_dates_email.html.twig')
            ->textTemplate('emails/technical/alert_session_min_dates_email.txt.twig');

        $this->send($email);
    }

    //-----
    // CONNECTION
    //-----

    /**
     * @param Connection $connection
     * @throws TransportExceptionInterface
     */
    public function sendEmailOnConnectionFailed(Connection $connection): void
    {
        $organism = $connection->getOrganism();
        $dataProviderName = DataProviders::toFrString($connection->getDataProvider());
        $user = $organism->getOwnedBy();
        $context = [
            'userName' => $user->getName(),
            'dataProviderName' => $dataProviderName,
            'organismName' => $organism->getName(),
            'organismSiret' => $organism->getSiret(),
            'userEmail' => $connection->getType() === ConnectionTypes::HABILITATION()->getValue() ? Tools::getEnvValue('SUPPORT_EMAIL') : $user->getEmail(),
            'redirectLinkDataProvider' => DataProviders::from($connection->getDataProvider())
        ];

        $email = (new TemplatedEmail())
            ->from(Tools::getFromEmail(Tools::getEnvValue('SENDER_EMAIL_NAME', 'Wedof'), Tools::getEnvValue('SENDER_EMAIL')))
            ->to($connection->getType() === ConnectionTypes::HABILITATION()->getValue() ? Tools::getEnvValue('SUPPORT_EMAIL') : $user->getEmail())
            ->priority(Email::PRIORITY_HIGH)
            ->subject("[Wedof] Rétablissez la synchronisation " . $dataProviderName)
            ->htmlTemplate('emails/connection/connection_failed_email.html.twig')
            ->textTemplate('emails/connection/connection_failed_email.txt.twig')
            ->context($context);
        $this->send($email);
    }

    //-----
    // USER
    //-----

    /**
     * @param User $user
     * @param string $code
     * @param string $documentName
     * @throws TransportExceptionInterface
     */
    public function sendUserCode(User $user, string $code, string $documentName): void
    {
        $email = (new TemplatedEmail())
            ->from(Tools::getFromEmail(Tools::getEnvValue('SENDER_EMAIL_NAME', 'Wedof'), Tools::getEnvValue('SENDER_EMAIL')))
            ->to($user->getEmail())
            ->priority(Email::PRIORITY_HIGH)
            ->subject("[Wedof] " . $code . " est votre code")
            ->htmlTemplate('emails/user/code_email.html.twig')
            ->textTemplate('emails/user/code_email.txt.twig')
            ->context([
                'user' => $user->getName(),
                'code' => $code,
                'documentName' => $documentName
            ]);

        $this->send($email, true);
    }

    /**
     * @param User $user
     * @param string $newPassword
     * @throws TransportExceptionInterface
     */
    public function sendNewPassword(User $user, string $newPassword): void
    {
        $email = (new TemplatedEmail())
            ->from(Tools::getFromEmail(Tools::getEnvValue('SENDER_EMAIL_NAME', 'Wedof'), Tools::getEnvValue('SENDER_EMAIL')))
            ->to($user->getEmail())
            ->priority(Email::PRIORITY_HIGH)
            ->subject("[Wedof] Votre nouveau mot de passe")
            ->htmlTemplate('emails/user/forgot_password_email.html.twig')
            ->textTemplate('emails/user/forgot_password_email.txt.twig')
            ->context([
                'recipientName' => $user->getName(),
                'recipientEmail' => $user->getEmail(),
                'recipientOrganism' => $user->getMainOrganism() ? $user->getMainOrganism()->getName() : null,
                'newPassword' => $newPassword
            ]);

        $this->send($email, true);
    }

    /**
     * @param User $user
     * @param string $link
     * @throws TransportExceptionInterface
     */
    public function sendMagicLink(User $user, string $link): void
    {
        $email = (new TemplatedEmail())
            ->from(Tools::getFromEmail(Tools::getEnvValue('SENDER_EMAIL_NAME', 'Wedof'), Tools::getEnvValue('SENDER_EMAIL')))
            ->to($user->getEmail())
            ->priority(Email::PRIORITY_HIGH)
            ->subject("[Wedof] Lien magique de connexion à Wedof")
            ->htmlTemplate('emails/user/magic_link_request_email.html.twig')
            ->textTemplate('emails/user/magic_link_request_email.txt.twig')
            ->context([
                'recipientName' => $user->getName(),
                'magicLink' => $link
            ]);

        $this->send($email, true);
    }

    /**
     * @param RegistrationFolder|CertificationFolder $folder
     * @param string $link
     * @param string $entityClass
     * @throws TransportExceptionInterface
     */
    public function sendAttendeeMagicLink($folder, string $link, string $entityClass): void
    {
        $attendee = $folder->getAttendee();
        $attendeeLink = $folder->getAttendeeLink();
        $name = $attendee->getFirstName() && $attendee->getLastName() ? $attendee->getFirstName() . ' ' . $attendee->getLastName() : '';
        if ($entityClass === CertificationFolder::CLASSNAME) {
            $contextName = 'candidat';
            $organism = $folder->getCertifier();
        } else {
            $contextName = 'apprenant';
            $organism = $folder->getOrganism();
        }
        if (!empty($organism->getSendAs())) {
            $senderEmailFromName = $organism->isNonDiffusible() ? null : $organism->getSendAsName();
            $senderEmail = $organism->getSendAsEmail();
            $replyTo = $senderEmail;
        } else {
            $senderEmailFromName = Tools::getEnvValue('SENDER_EMAIL_NAME', 'Wedof');
            $senderEmail = Tools::getEnvValue('SENDER_EMAIL');
            $replyTo = $this->getPublicOrganismEmail($organism);
        }
        $email = (new TemplatedEmail())
            ->from(Tools::getFromEmail($senderEmailFromName, $senderEmail))
            ->to($attendee->getEmail())
            ->replyTo($replyTo)
            ->priority(Email::PRIORITY_HIGH)
            ->subject("Votre lien magique de connexion à votre espace " . $contextName)
            ->htmlTemplate('emails/attendee/magic_link_request_email.html.twig')
            ->textTemplate('emails/attendee/magic_link_request_email.txt.twig')
            ->context([
                'recipientName' => $name,
                'entity' => $contextName,
                'magicLink' => $link,
                'attendeeLink' => $attendeeLink
            ]);
        // $this->logger->error('Magic link for ' . $contextName . ' ' . $name . ' : ' . $link);
        $this->send($email, true);
    }


    /**
     * @param User $user
     * @param User $invitedUser
     * @param string $organismName
     * @param string|null $newPassword
     * @throws TransportExceptionInterface
     */
    public function sendInvitationToNewUser(User $user, User $invitedUser, string $organismName, string $newPassword = null): void
    {
        $subject = $newPassword ? "[Wedof] Votre compte a été créé sur Wedof" : "[Wedof] Votre compte Wedof a été associé à un organisme";
        $email = (new TemplatedEmail())
            ->from(Tools::getFromEmail(Tools::getEnvValue('SENDER_EMAIL_NAME', 'Wedof'), Tools::getEnvValue('SENDER_EMAIL')))
            ->to($invitedUser->getEmail())
            ->priority(Email::PRIORITY_HIGH)
            ->subject($subject)
            ->htmlTemplate('emails/user/invite_user_email.html.twig')
            ->textTemplate('emails/user/invite_user_email.txt.twig')
            ->context([
                'user' => $user->getName(),
                'invitedUserEmail' => $invitedUser->getEmail(),
                'invitedUser' => $invitedUser->getName(),
                'organism' => $organismName,
                'password' => $newPassword
            ]);

        $this->send($email, true);
    }

    /**
     * @param User $user
     * @param User $invitedUser
     * @throws TransportExceptionInterface
     */
    public function sendConfirmationInvitedUserToAdmin(User $user, User $invitedUser): void
    {
        $email = (new TemplatedEmail())
            ->from(Tools::getFromEmail(Tools::getEnvValue('SENDER_EMAIL_NAME', 'Wedof'), Tools::getEnvValue('SENDER_EMAIL')))
            ->to($user->getEmail())
            ->priority(Email::PRIORITY_HIGH)
            ->subject("[Wedof] Vous avez créé et associé un utilisateur à votre organisme")
            ->htmlTemplate('emails/user/confirmation_invite_user_email.html.twig')
            ->textTemplate('emails/user/confirmation_invite_user_email.txt.twig')
            ->context([
                'user' => $user->getName(),
                'invitedUserEmail' => $invitedUser->getEmail(),
                'invitedUser' => $invitedUser->getName(),
                'role' => in_array('ROLE_SALES', $user->getRoles()) ? 'commercial' : 'utilisateur'
            ]);

        $this->send($email, true);
    }

    /**
     * @param TemplatedEmail $email
     * @param bool $reThrowException
     * @return void
     * @throws TransportExceptionInterface
     */
    public function send(TemplatedEmail $email, bool $reThrowException = false): void
    {
        if ($_ENV['APP_ENV'] != 'prod') {
            $this->logger->info("would send an email to " . $email->getTo()[0]->getAddress() . ", change it to " . $_ENV['TEST_EMAIL']);
            $email->to($_ENV['TEST_EMAIL']);
            $email->cc($_ENV['TEST_EMAIL']);
            $email->bcc($_ENV['TEST_EMAIL']);
        }

        try {
            $this->mailer->send($email);
        } catch (TransportExceptionInterface $e) {
            $this->logger->error($e->getMessage());
            $this->logger->error("[Send email failure] : could not send email to " . $email->getTo()[0]->getAddress());
            if ($reThrowException) {
                throw $e;
            }
        }
    }


    //----------------
    // METHODES PRIVES
    //----------------

    /**
     * email to provide to other parties, so organism first, then user as fallback
     * @param Organism|null $organism
     * @return string|null
     */
    private function getPublicOrganismEmail(?Organism $organism): ?string
    {
        $email = null;
        if (!empty($organism)) {
            if (!empty($organism->getEmails())) {
                $email = $organism->getEmails()[0]; // Maybe check in sendAs first
            } else if (!empty($organism->getOwnedBy())) {
                $email = $organism->getOwnedBy()->getEmail();
            }
        }
        return $email;
    }
}
