<?php


namespace App\Service;

use App\Entity\Attendee;
use App\Entity\CertificationPartner;
use App\Entity\CertificationPartnerFile;
use App\Entity\User;
use App\Event\CertificationPartnerFile\CertificationPartnerFileEvents;
use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofConnectionException;
use App\Library\utils\enums\ActivityTypes;
use App\Library\utils\enums\DocumentGenerationStates;
use App\Library\utils\enums\FileStates;
use App\Library\utils\Tools;
use App\Repository\CertificationPartnerFileRepository;
use App\Service\DataProviders\DocusealApiService;
use DateTime;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use Embed\Embed;
use ErrorException;
use Exception;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Throwable;
use Vich\UploaderBundle\Handler\DownloadHandler;

class CertificationPartnerFileService implements LoggerAwareInterface
{

    private CertificationPartnerFileRepository $certificationPartnerFileRepository;
    private DownloadHandler $downloadHandler;
    private ActivityService $activityService;
    private EventDispatcherInterface $dispatcher;
    private LoggerInterface $logger;
    private DocusealApiService $docusealApiService;

    /**
     * @param CertificationPartnerFileRepository $certificationPartnerFileRepository
     * @param DownloadHandler $downloadHandler
     * @param ActivityService $activityService
     * @param EventDispatcherInterface $dispatcher
     * @param DocusealApiService $docusealApiService
     */
    public function __construct(CertificationPartnerFileRepository $certificationPartnerFileRepository, DownloadHandler $downloadHandler, ActivityService $activityService, EventDispatcherInterface $dispatcher, DocusealApiService $docusealApiService)
    {
        $this->downloadHandler = $downloadHandler;
        $this->dispatcher = $dispatcher;
        $this->certificationPartnerFileRepository = $certificationPartnerFileRepository;
        $this->activityService = $activityService;
        $this->docusealApiService = $docusealApiService;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    /**
     * @param CertificationPartner $certificationPartner
     * @param int $typeId
     * @return CertificationPartnerFile|null
     */
    public function getByTypeId(CertificationPartner $certificationPartner, int $typeId): ?CertificationPartnerFile
    {
        return $this->certificationPartnerFileRepository->findOneBy(['certificationPartner' => $certificationPartner, 'typeId' => $typeId]);
    }

    /**
     * @param $file
     * @param int $typeId
     * @param CertificationPartner $certificationPartner
     * @param bool $generated
     * @param User|null $user
     * @param string|null $title
     * @param bool $isPartner
     * @return CertificationPartnerFile
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function create($file, int $typeId, CertificationPartner $certificationPartner, bool $generated = false, User $user = null, bool $isPartner = false, string $title = null): CertificationPartnerFile
    {
        $certification = $certificationPartner->getCertification();
        $partnershipFileTypes = $certification->getCertificationPartnerFileTypes();
        $fileTypeIndex = array_search($typeId, array_column($partnershipFileTypes, 'id'));
        if ($fileTypeIndex >= 0) {
            $fileType = $partnershipFileTypes[$fileTypeIndex];
            if (!$generated && isset($fileType["allowUploadPartner"]) && $fileType["allowUploadPartner"] === false && $isPartner) {
                throw new WedofBadRequestHttpException("Erreur, en tant que partenaire vous n'avez pas les droits pour déposer ce fichier.");
            }
            $certificationPartnerFiles = $this->certificationPartnerFileRepository->findBy(['typeId' => $typeId, 'certificationPartner' => $certificationPartner]);
            $isUpdatedFile = false;
            if ($certificationPartnerFiles && empty($fileType['allowMultiple'])) {
                $isUpdatedFile = true;
                foreach ($certificationPartnerFiles as $certificationPartnerFile) {
                    if ($isPartner && $certificationPartnerFile->getState() === FileStates::VALID()->getValue()) {
                        throw new WedofBadRequestHttpException("Erreur, vous ne pouvez pas modifier un document qui a été validé.");
                    }
                    $this->certificationPartnerFileRepository->delete($certificationPartnerFile);
                }
            }
            $certificationPartnerFile = new CertificationPartnerFile();
            $certificationPartnerFile->setTypeId($typeId);
            if (is_string($file)) {
                $certificationPartnerFile->setLink($file);
                $certificationPartnerFile->setFilePath($file);
                $certificationPartnerFile->setFileType("link");
                try {
                    $embed = new Embed();
                    $title = $title ?: $embed->get($file)->title; // This tries to resolve the URL => must be publicly accessible
                } catch (Exception $exception) {
                    $title = $file;
                }
                $certificationPartnerFile->setFileName($title);
            } else {
                $certificationPartnerFile->setFile($file);
            }
            if ($generated) {
                $certificationPartnerFile->setGenerationState(DocumentGenerationStates::GENERATED()->getValue());
                $certificationPartnerFile->setState(FileStates::VALID()->getValue());
            } else {
                $certificationPartnerFile->setState($isPartner ? FileStates::TO_REVIEW()->getValue() : FileStates::VALID()->getValue());
            }
            $certificationPartnerFile = $this->docusealApiService->setToSignIfRequired($certificationPartner->getCertifier(), $certificationPartner, $certificationPartnerFile, $file, $fileType);
            $certificationPartner->addFile($certificationPartnerFile);
            $this->save($certificationPartnerFile);
            $data = [
                'title' => "Le document {$certificationPartnerFile->getFilename()} a été " . ($isUpdatedFile ? 'remplacé' : 'ajouté'),
                'description' => $generated ? 'Généré automatiquement' : null,
                'type' => ActivityTypes::FILE(),
                'eventTime' => new DateTime(),
                'origin' => $isPartner ? 'le Partenaire' : null
            ];
            $this->activityService->create($data, $user, $certificationPartner, false);
            $this->sendEventCertificationPartnerFile($certificationPartnerFile, $isUpdatedFile ? CertificationPartnerFileEvents::FILE_UPDATED : CertificationPartnerFileEvents::FILE_ADDED);
            return $certificationPartnerFile;
        } else {
            throw new WedofBadRequestHttpException("Erreur, ce type de fichier " . $typeId . " n'est pas autorisé pour ce partenariat");
        }
    }

    /**
     * @param CertificationPartnerFile $certificationPartnerFile
     * @param User| Attendee $user
     * @return array|string[]|StreamedResponse
     * @throws ClientExceptionInterface
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     * @throws ErrorException
     */
    public function download(CertificationPartnerFile $certificationPartnerFile, $user)
    {
        if ($certificationPartnerFile->getFileType() == 'sign') {
            $submitter = $this->docusealApiService->retrieveSubmitter($certificationPartnerFile->getCertificationPartner()->getCertifier(), $certificationPartnerFile, $user);
            return $this->docusealApiService->displayEmbeddedSignForm($certificationPartnerFile, $submitter);
        } else if ($certificationPartnerFile->getFileType() != 'link') {
            return $this->downloadHandler->downloadObject($certificationPartnerFile, 'file');
        } else {
            //convert google drive content
            if (Tools::startsWith($certificationPartnerFile->getLink(), 'https://drive.google.com/')
                || Tools::startsWith($certificationPartnerFile->getLink(), 'https://docs.google.com/')) {
                $isGoogleForm = str_contains($certificationPartnerFile->getLink(), 'https://docs.google.com/forms/');
                $code = explode('/view', $certificationPartnerFile->getLink());
                $code = isset($code[1]) ? $code[0] : explode('/edit', $certificationPartnerFile->getLink())[0];
                if (!Tools::contains($code, '/folders/')) { //case drive is a folder...
                    $extension = $isGoogleForm ? '/viewform' : '/preview';
                    $code = '<iframe src="' . $code . $extension . '" height="100%" width="100%"></iframe>';
                }
            } else {
                $embed = new Embed();
                try {
                    $code = $embed->get($certificationPartnerFile->getLink())->code;
                } catch (Exception $e) {
                    $code = null;
                }
            }
            if ($code) {
                $code = preg_replace('/height=[\"\'][0-9]+[\"\']/i', 'height="100%"', $code);
                $code = preg_replace('/width=[\"\'][0-9]+[\"\']/i', 'width="100%"', $code);
                return ['html' => $code];
            } else {
                return ['html' => '<div class="w-full"><a href="' . $certificationPartnerFile->getLink() . '" 
                                            class="mat-focus-indicator mat-flat-button mat-button-base mat-primary" target="_blank">
                                            <span class="mat-button-wrapper">Ouvrir le document</span>
                                            <div class="mat-ripple mat-button-ripple"></div>
                                            <div class="mat-button-focus-overlay"></div>
                                        </a></div>'];
            }
        }
    }

    /**
     * @param CertificationPartnerFile $certificationPartnerFile
     * @param bool $createActivity
     * @param bool $isOwner
     * @param User|null $user
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function delete(CertificationPartnerFile $certificationPartnerFile, bool $createActivity, bool $isOwner = false, User $user = null)
    {
        $certificationPartner = $certificationPartnerFile->getCertificationPartner();
        $oldCertificationPartnerFile = clone $certificationPartnerFile;
        $title = "Le document {$certificationPartnerFile->getFilename()} a été supprimé" . ($isOwner ? "" : " par le partenaire");
        $this->certificationPartnerFileRepository->delete($certificationPartnerFile);
        $this->sendEventCertificationPartnerFile($oldCertificationPartnerFile, CertificationPartnerFileEvents::FILE_DELETED);
        if ($createActivity && $user) {
            $this->activityService->create([
                'title' => $title,
                'type' => ActivityTypes::FILE(),
                'eventTime' => new DateTime()
            ], $user, $certificationPartner, false);
        }
    }

    /**
     * @param CertificationPartnerFile $certificationPartnerFile
     * @param array $body
     * @param User $user
     * @return CertificationPartnerFile
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function updateState(CertificationPartnerFile $certificationPartnerFile, array $body, User $user): CertificationPartnerFile
    {
        $properties = ['state', 'comment'];
        $previousState = $certificationPartnerFile->getState();
        foreach ($properties as $property) {
            if (key_exists($property, $body)) {
                $setMethodName = "set" . ucwords($property);
                $certificationPartnerFile->{$setMethodName}($body[$property]);
            }
        }
        $this->save($certificationPartnerFile);
        $certificationPartner = $certificationPartnerFile->getCertificationPartner();
        $eventName = "certificationPartnerFile." . $certificationPartnerFile->getState();
        if (!empty($body['comment']) && $previousState === $certificationPartnerFile->getState()) {
            $eventName = CertificationPartnerFileEvents::FILE_UPDATED;
        }

        if (isset($body['state'])) {
            $this->sendEventCertificationPartnerFile($certificationPartnerFile, $eventName);
            $this->activityService->create([
                'title' => "Le document {$certificationPartnerFile->getFilename()} " . FileStates::toFrStringActivity($body['state']),
                'description' => $body['comment'] ?? null,
                'type' => ActivityTypes::FILE(),
                'eventTime' => new DateTime()
            ], $user, $certificationPartner, false);
        }
        return $certificationPartnerFile;
    }

    /**
     * used by Messages & notifications
     * @param int $entityId
     * @return CertificationPartnerFile|null
     */
    public function getByEntityId(int $entityId): ?CertificationPartnerFile
    {
        return $this->certificationPartnerFileRepository->find($entityId);
    }

    /**
     * @param CertificationPartnerFile $certificationPartnerFile
     */
    public function save(CertificationPartnerFile $certificationPartnerFile)
    {
        $this->certificationPartnerFileRepository->save($certificationPartnerFile);
    }

    /**
     * @param CertificationPartnerFile $certificationPartnerFile
     * @param string $eventName
     */
    public function sendEventCertificationPartnerFile(CertificationPartnerFile $certificationPartnerFile, string $eventName): void
    {
        $event = new CertificationPartnerFileEvents($certificationPartnerFile);
        $this->dispatcher->dispatch($event, $eventName);
        $this->logger->info("[" . $certificationPartnerFile->getId() . " typeId " . $certificationPartnerFile->getTypeId() . "][event] CertificationPartnerFile event dispatched $eventName ");
    }
    //----------------
    // METHODES PRIVES
    //----------------
}
