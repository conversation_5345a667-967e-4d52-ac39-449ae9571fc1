<?php

namespace App\Service;

use App\Entity\Application;
use App\Entity\CertificationFolder;
use App\Entity\RegistrationFolder;
use App\Entity\User;
use App\Library\utils\Tools;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Component\HttpClient\CurlHttpClient;
use Symfony\Component\Security\Core\Security;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;

class ProcessusMetierService
{
    private Security $security;
    private EntityManagerInterface $entityManager;

    public function __construct(Security $security, EntityManagerInterface $entityManager)
    {
        $this->security = $security;
        $this->entityManager = $entityManager;
    }

    /**
     * @param RegistrationFolder|CertificationFolder $entity
     * @return array|null
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    public function getFlowRunsByEntity($entity): array
    {
        $allFlowRuns = ['data' => [], 'next' => null, 'previous' => null];
        $workflowApplication = $this->getWorkflowApplicationForEntity($entity);
        if ($workflowApplication) {
            $authData = $this->getAuthDataForApplication($workflowApplication);
            if ($authData && !empty($authData['projectId']) && !empty($authData['token'])) {
                $workflowRuns = $this->getFlowRuns($workflowApplication, $authData['projectId'], $entity->getExternalId(), $authData['token']);
                if (isset($workflowRuns['data']) && is_array($workflowRuns['data'])) {
                    $allFlowRuns['data'] = array_merge($allFlowRuns['data'], $workflowRuns['data']);
                }
            }
        }
        $activepiecesApplication = $this->getActivepiecesApplicationForEntity($entity);
        if ($activepiecesApplication) {
            $activepiecesRuns = $this->getActivepiecesFlowRunsByEntity($entity, $activepiecesApplication);
            if (isset($activepiecesRuns['data']) && is_array($activepiecesRuns['data'])) {
                $allFlowRuns['data'] = array_merge($allFlowRuns['data'], $activepiecesRuns['data']);
            }
        }
        if (!empty($allFlowRuns['data'])) {
            usort($allFlowRuns['data'], function ($a, $b) {
                return strtotime($b['startTime']) - strtotime($a['startTime']);
            });
        }
        return $allFlowRuns;
    }

    /**
     * @param Application $application
     * @param string $projectId
     * @param string $tags
     * @param string $token
     * @return array|null
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    private function getFlowRuns(Application $application, string $projectId, string $tags, string $token): array
    {
        return $this->getFlowRunsFromApi($this->getBaseUrl($application), $projectId, $tags, $token, 'workflow', $application);
    }

    /**
     * @param RegistrationFolder|CertificationFolder $entity
     * @return Application|null
     */
    public function getWorkflowApplicationForEntity($entity): ?Application
    {
        return $this->getApplicationForEntity($entity, 'workflow');
    }

    /**
     * @param RegistrationFolder|CertificationFolder $entity
     * @param string $appId
     * @param callable|null $additionalCheck
     * @return Application|null
     */
    private function getApplicationForEntity($entity, string $appId, ?callable $additionalCheck = null): ?Application
    {
        if ($entity instanceof CertificationFolder) {
            $organism = $entity->getCertifier();
        } else {
            $organism = $entity->getOrganism();
        }

        foreach ($organism->getApplications() as $application) {
            if ($application->getAppId() === $appId && $application->getEnabled()) {
                if ($additionalCheck === null || $additionalCheck($application)) {
                    return $application;
                }
            }
        }
        return null;
    }

    /**
     * @param RegistrationFolder|CertificationFolder $entity
     * @return Application|null
     */
    public function getActivepiecesApplicationForEntity($entity): ?Application
    {
        return $this->getApplicationForEntity($entity, 'activepieces', function (Application $application) {
            $metadata = $application->getMetadata();
            return !empty($metadata['domain']) && !empty($metadata['apiKey']);
        });
    }

    /**
     * @param Application $application
     * @return string
     */
    private function getBaseUrl(Application $application): string
    {
        if ($application->getAppId() === 'activepieces') {
            $metadata = $application->getMetadata();
            return rtrim($metadata['domain'] ?? '', '/');
        }
        return 'https://' . $application->getOrganism()->getSubDomain() . '.' . Tools::getEnvValue('WORKFLOW_DOMAIN_SUFFIX');
    }

    /**
     * @param RegistrationFolder|CertificationFolder $entity
     * @param Application $application
     * @return array|null
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    public function getActivepiecesFlowRunsByEntity($entity, Application $application): array
    {
        $authData = $this->getAuthDataForApplication($application);
        if (!$authData || empty($authData['projectId']) || empty($authData['token'])) {
            return ['data' => [], 'next' => null, 'previous' => null];
        }
        return $this->getFlowRunsFromApi($this->getBaseUrl($application), $authData['projectId'], $entity->getExternalId(), $authData['token'], 'activepieces', $application);
    }

    /**
     * @param string $baseUrl
     * @param string $projectId
     * @param string $tags
     * @param string $token
     * @param string $source
     * @param Application|null $application
     * @param bool $isRetry
     * @return array
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    private function getFlowRunsFromApi(string $baseUrl, string $projectId, string $tags, string $token, string $source = 'workflow', ?Application $application = null, bool $isRetry = false): array
    {
        try {
            $client = new CurlHttpClient();
            $url = rtrim($baseUrl, '/') . '/api/v1/flow-runs';
            $queryParams = [
                'projectId' => $projectId,
                'tags' => $tags
            ];
            if ($source === 'activepieces') {
                $queryParams['limit'] = 50;
            }
            $response = $client->request('GET', $url, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $token,
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json'
                ],
                'query' => $queryParams
            ]);
            if (($response->getStatusCode() === 401 || $response->getStatusCode() === 403) && !$isRetry && $application) {
                $this->clearCachedCredentials($application);
                $newAuthData = $this->getAuthDataForApplication($application);
                if ($newAuthData && !empty($newAuthData['projectId']) && !empty($newAuthData['token'])) {
                    return $this->getFlowRunsFromApi($baseUrl, $newAuthData['projectId'], $tags, $newAuthData['token'], $source, $application, true);
                }
            }
            if ($response->getStatusCode() !== 200) {
                return ['data' => [], 'next' => null, 'previous' => null];
            }
            $data = json_decode($response->getContent(), true);
            if ($source === 'activepieces') {
                return $this->transformActivepiecesData($data, $projectId, $baseUrl);
            }
            return $data ?: ['data' => [], 'next' => null, 'previous' => null];
        } catch (Exception $e) {
            return ['data' => [], 'next' => null, 'previous' => null];
        }
    }

    /**
     * @param array $data
     * @param string $projectId
     * @param string $baseUrl
     * @return array
     */
    private function transformActivepiecesData(array $data, string $projectId, string $baseUrl): array
    {
        $transformedData = [];
        if (isset($data['data']) && is_array($data['data'])) {
            foreach ($data['data'] as $run) {
                $transformedData[] = [
                    'id' => $run['id'] ?? '',
                    'projectId' => $run['projectId'] ?? $projectId,
                    'flowDisplayName' => $run['flowDisplayName'] ?? ($run['flowId'] ?? 'Unknown Flow'),
                    'status' => $this->mapActivepiecesStatus($run['status'] ?? 'UNKNOWN'),
                    'startTime' => $run['startTime'] ?? ($run['created'] ?? date('c')),
                    'duration' => $run['duration'] ?? 0,
                    'tasks' => $run['tasks'] ?? 1,
                    'source' => 'activepieces',
                    'baseUrl' => $baseUrl
                ];
            }
        }
        return [
            'data' => $transformedData,
            'next' => $data['next'] ?? null,
            'previous' => $data['previous'] ?? null
        ];
    }

    /**
     * @param string $status
     * @return string
     */
    private function mapActivepiecesStatus(string $status): string
    {
        $statusMap = [
            'SUCCEEDED' => 'SUCCEEDED',
            'FAILED' => 'FAILED',
            'RUNNING' => 'RUNNING',
            'PAUSED' => 'PAUSED',
            'STOPPED' => 'STOPPED',
            'COMPLETED' => 'SUCCEEDED',
            'ERROR' => 'FAILED',
            'PENDING' => 'RUNNING'
        ];
        return $statusMap[strtoupper($status)] ?? 'STOPPED';
    }

    /**
     * @param Application $application
     * @return array|null
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    private function getAuthDataForApplication(Application $application): ?array
    {
        $metadata = $application->getMetadata();
        if ($application->getAppId() === 'activepieces') {
            $apiKey = $metadata['apiKey'] ?? null;
            $projectId = $metadata['projectId'] ?? null;

            if (!$apiKey) {
                return null;
            }
            if (!$projectId) {
                $projectId = $this->fetchAndCacheActivepiecesProjectId($application);
                if (!$projectId) {
                    return null;
                }
            }
            return [
                'projectId' => $projectId,
                'token' => $apiKey
            ];
        }
        $cachedProjectId = $metadata['projectId'] ?? null;
        $cachedToken = $metadata['token'] ?? null;
        if ($cachedProjectId && $cachedToken) {
            return [
                'projectId' => $cachedProjectId,
                'token' => $cachedToken
            ];
        }
        return $this->authenticateAndCacheWorkflow($application);
    }

    /**
     * @param Application $application
     * @param string $username
     * @return array|null
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    private function signin(Application $application, string $username): ?array
    {
        $client = new CurlHttpClient();
        $response = $client->request('POST', $this->getBaseUrl($application) . "/api/v1/authentication/sign-in", [
            'headers' => [
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ],
            'body' => json_encode([
                "email" => $username,
                "password" => Tools::getEnvValue('WORKFLOW_PASSWORD')
            ])
        ]);
        return $response->getStatusCode() == 200 ? json_decode($response->getContent(), true) : null;
    }

    /**
     * @param Application $application
     * @return string|null
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    private function fetchAndCacheActivepiecesProjectId(Application $application): ?string
    {
        if ($application->getAppId() !== 'activepieces') {
            return null;
        }
        $metadata = $application->getMetadata();
        $domain = $metadata['domain'] ?? null;
        $apiKey = $metadata['apiKey'] ?? null;
        if (!$domain || !$apiKey) {
            return null;
        }
        try {
            $client = new CurlHttpClient();
            $url = rtrim($domain, '/') . '/api/v1/projects';
            $response = $client->request('GET', $url, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $apiKey,
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json'
                ]
            ]);
            if ($response->getStatusCode() === 200) {
                $data = json_decode($response->getContent(), true);
                if (isset($data['data']) && is_array($data['data']) && count($data['data']) > 0) {
                    $projectId = $data['data'][0]['id'];
                    $metadata['projectId'] = $projectId;
                    $application->setMetadata($metadata);
                    $this->entityManager->persist($application);
                    $this->entityManager->flush();
                    return $projectId;
                }
            }
        } catch (Exception $e) {
            // fail
        }
        return null;
    }

    /**
     * @param Application $application
     * @return array|null
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    private function authenticateAndCacheWorkflow(Application $application): ?array
    {
        if ($application->getAppId() !== 'workflow') {
            return null;
        }
        /** @var User $user */
        $user = $this->security->getUser();
        $userAuth = $this->signin($application, $user->getEmail());
        if (!empty($userAuth['token']) && !empty($userAuth['projectId'])) {
            $this->cacheWorkflowCredentials($application, $userAuth);
            return $userAuth;
        }
        $adminAuth = $this->signin($application, Tools::getEnvValue('WORKFLOW_ADMIN_USERNAME'));
        if (!empty($adminAuth['token']) && !empty($adminAuth['projectId'])) {
            $this->cacheWorkflowCredentials($application, $adminAuth);
            return $adminAuth;
        }
        return null;
    }

    /**
     * @param Application $application
     * @param array $authData
     * @return void
     */
    private function cacheWorkflowCredentials(Application $application, array $authData): void
    {
        $metadata = $application->getMetadata() ?? [];
        $metadata['projectId'] = $authData['projectId'];
        $metadata['token'] = $authData['token'];
        $application->setMetadata($metadata);
        $this->entityManager->persist($application);
        $this->entityManager->flush();
    }

    /**
     * @param Application $application
     * @return void
     */
    private function clearCachedCredentials(Application $application): void
    {
        $metadata = $application->getMetadata() ?? [];
        if ($application->getAppId() === 'workflow') {
            unset($metadata['projectId']);
            unset($metadata['token']);
        } elseif ($application->getAppId() === 'activepieces') {
            unset($metadata['projectId']);
        }
        $application->setMetadata($metadata);
        $this->entityManager->persist($application);
        $this->entityManager->flush();
    }
}
