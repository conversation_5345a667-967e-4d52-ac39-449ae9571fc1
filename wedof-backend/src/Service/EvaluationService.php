<?php

namespace App\Service;

use App\Entity\Certification;
use App\Entity\Evaluation;
use App\Entity\Organism;
use App\Entity\Training;
use App\Entity\TrainingAction;
use App\Event\Evaluation\EvaluationEvents;
use App\Repository\EvaluationRepository;
use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Throwable;


class EvaluationService implements LoggerAwareInterface
{
    private OrganismService $organismService;
    private EvaluationRepository $evaluationRepository;
    private LoggerInterface $logger;
    private EventDispatcherInterface $dispatcher;
    private EntityManagerInterface $entityManager;

    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    public function __construct(EntityManagerInterface $entityManager, EventDispatcherInterface $dispatcher, EvaluationRepository $evaluationRepository, OrganismService $organismService)
    {
        $this->entityManager = $entityManager;
        $this->dispatcher = $dispatcher;
        $this->organismService = $organismService;
        $this->evaluationRepository = $evaluationRepository;
    }

    /**
     * @param LoggerInterface $logger
     */
    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    /**
     * @param array $rawData
     * @param Organism $organism
     * @return Evaluation|null
     */
    public function createForOrganism(array $rawData, Organism $organism): ?Evaluation
    {
        $evaluationDate = new DateTime();
        $lastEvaluation = $this->evaluationRepository->findOneBy(['organism' => $organism, 'trainingAction' => null, 'training' => null], ['id' => 'DESC']);
        if ($lastEvaluation == null || ($lastEvaluation->getDate()->format('Y-m-d') != $evaluationDate->format('Y-m-d'))) {
            $evaluation = new Evaluation();
            $evaluation->setOrganism($organism);
            $evaluation->setDate(new DateTime());
        } else {
            $evaluation = $lastEvaluation;
        }
        //round down right way...
        $eval = floor(floatval(str_replace(",", ".", $rawData['NOTE_MOYENNE'])) * 100) / 100;
        $evaluation->setAverageRating($eval);
        try {
            $this->evaluationRepository->saveOrUpdate($evaluation);
            if ($lastEvaluation == null || $lastEvaluation->getAverageRating() != $evaluation->getAverageRating()) {
                $_event = new EvaluationEvents($evaluation, $lastEvaluation);
                $eventName = $lastEvaluation ? EvaluationEvents::ORGANISM_CHANGED : EvaluationEvents::ORGANISM_NEW;
                $this->dispatcher->dispatch($_event, $eventName);
                $this->logger->info("[" . $evaluation->getId() . "][event] Evaluation organism event dispatched: $eventName");
            }
        } catch (Throwable $e) {
            $this->logger->info("[evaluation][createForOrganism][event] Evaluation save error");
            $this->logger->debug($e->getMessage());
            $this->entityManager = $this->checkAndRefreshEntityManager();
            return null;
        }
        return $evaluation;
    }

    /**
     * @param array $rawData
     * @param Training $training
     * @return Evaluation|null
     */
    public function createForTraining(array $rawData, Training $training): ?Evaluation
    {
        $evaluationDate = new DateTime();
        $lastEvaluation = $this->evaluationRepository->findOneBy(['training' => $training], ['id' => 'DESC']);
        if ($lastEvaluation == null || ($lastEvaluation->getDate()->format('Y-m-d') != $evaluationDate->format('Y-m-d'))) {
            $evaluation = new Evaluation();
            $evaluation->setAverageRating(floatval(str_replace(",", ".", $rawData['NOTE_MOYENNE'])));
            $evaluation->setReviewNumber($rawData["NB_AVIS"]);
            $evaluation->setOrganism($training->getOrganism());
            $evaluation->setTraining($training);
            $evaluation->setDate($evaluationDate);
            try {
                $this->evaluationRepository->saveOrUpdate($evaluation);
                if ($lastEvaluation == null || $lastEvaluation->getReviewNumber() != $evaluation->getReviewNumber()) {
                    $_event = new EvaluationEvents($evaluation, $lastEvaluation);
                    $eventName = $lastEvaluation ? EvaluationEvents::TRAINING_CHANGED : EvaluationEvents::TRAINING_NEW;
                    $this->dispatcher->dispatch($_event, $eventName);
                    $this->logger->info("[" . $evaluation->getId() . "][event] Evaluation training event dispatched: $eventName");
                }
            } catch (Throwable $e) {
                $this->logger->info("[evaluation][createForTraining][event] Evaluation save error");
                $this->logger->debug($e->getMessage());
                $this->entityManager = $this->checkAndRefreshEntityManager();
                return null;
            }
        } else {
            //maybe we will need to do thing in the future... but for now skip
            $evaluation = $lastEvaluation;
        }
        return $evaluation;
    }

    /**
     * @param array $rawData
     * @param TrainingAction|null $trainingAction
     * @return Evaluation|null
     * @throws Throwable
     */
    public function create(array $rawData, TrainingAction $trainingAction): ?Evaluation
    {
        $evaluationDate = new DateTime();
        $lastEvaluation = $this->evaluationRepository->findOneBy(['trainingAction' => $trainingAction], ['id' => 'DESC']); //only use $date when sent as param
        if ($lastEvaluation == null || ($lastEvaluation->getDate()->format('Y-m-d') != $evaluationDate->format('Y-m-d'))) {
            $evaluation = new Evaluation();
            $evaluation->setAverageRating(floatval(str_replace(",", ".", $rawData['NOTE_MOYENNE_ACTION'])));
            $evaluation->setReviewNumber($rawData["NB_AVIS_ACTION"]);
            if (isset($rawData["NOTE_Q1"])) {
                $evaluation->setRatingQuestion1(floatval(str_replace(',', '.', $rawData["NOTE_Q1"])));
                $evaluation->setRatingQuestion2(floatval(str_replace(',', '.', $rawData["NOTE_Q2"])));
                $evaluation->setRatingQuestion3(floatval(str_replace(',', '.', $rawData["NOTE_Q3"])));
                $evaluation->setRatingQuestion4(floatval(str_replace(',', '.', $rawData["NOTE_Q4"])));
                $evaluation->setRatingQuestion5(floatval(str_replace(',', '.', $rawData["NOTE_Q5"])));
            }
            $evaluation->setOrganism($this->organismService->getOrganismFromEntity($trainingAction));
            $evaluation->setTrainingAction($trainingAction);
            $evaluation->setDate($evaluationDate);
            try {
                $this->evaluationRepository->saveOrUpdate($evaluation);
                if ($lastEvaluation == null || $lastEvaluation->getReviewNumber() != $evaluation->getReviewNumber()) {
                    $_event = new EvaluationEvents($evaluation, $lastEvaluation);
                    $eventName = $lastEvaluation ? EvaluationEvents::TRAININGACTION_CHANGED : EvaluationEvents::TRAININGACTION_NEW;
                    $this->dispatcher->dispatch($_event, $eventName);
                    $this->logger->info("[" . $evaluation->getId() . "][event] Evaluation trainingAction event dispatched: $eventName");
                }
                return $evaluation;
            } catch (Throwable $e) {
                $this->logger->info("[evaluation][create][event] Evaluation save error");
                $this->logger->debug($e->getMessage());
                $this->entityManager = $this->checkAndRefreshEntityManager();
                return null;
            }
        } else {
            //maybe we will need to do thing in the future... but for now skip
            $evaluation = $lastEvaluation;
        }
        return $evaluation;
    }

    /**
     * @param Organism $organism
     * @return bool
     * @throws Throwable
     */
    public function computeAllEvaluationsForOrganism(Organism $organism): bool
    {
        $dateToCompute = (new DateTime())->setTime(0, 0);
        $taEvaluations = $this->evaluationRepository->findAllReturnQueryBuilder(new ArrayCollection([$organism]), ["for" => "trainingActions", "date" => $dateToCompute])->getQuery()->getResult();
        $tEvaluations = [];
        $orgEvaluation = ['SUM_NOTES' => 0, 'SUM_TRAININGS' => 0];
        if (!empty($taEvaluations)) {
            /** @var Evaluation $taEvaluation */
            foreach ($taEvaluations as $taEvaluation) {
                $trainingId = $taEvaluation->getTrainingAction()->getTraining()->getExternalId();
                if (!isset($tEvaluations[$trainingId])) {
                    $tEvaluations[$trainingId] = ['NB_AVIS' => 0, 'NOTE' => 0, 'SUM_NOTES' => 0, 'TRAINING' => $taEvaluation->getTrainingAction()->getTraining()];
                }
                $tEvaluations[$trainingId]['NB_AVIS'] += $taEvaluation->getReviewNumber();
                $tEvaluations[$trainingId]['SUM_NOTES'] += $taEvaluation->getAverageRating() * $taEvaluation->getReviewNumber();
            }
            foreach ($tEvaluations as $trainingId => $evalData) {
                $evalData['NOTE_MOYENNE'] = floor(floatval($evalData['SUM_NOTES'] / $evalData['NB_AVIS']) * 100) / 100;
                $training = $evalData['TRAINING'];
                unset($evalData['SUM_NOTES']);
                unset($evalData['TRAINING']);
                $orgEvaluation['SUM_NOTES'] += $evalData['NOTE_MOYENNE'];
                $orgEvaluation['SUM_TRAININGS'] += 1;
                $this->logger->info("[computeEvaluationsForOrganism][" . $organism->getSiret() . "][training][" . $trainingId . "]" . print_r($evalData, true));
                $this->createForTraining($evalData, $training);
            }
            $orgEvaluation['NOTE_MOYENNE'] = floor(floatval($orgEvaluation['SUM_NOTES'] / $orgEvaluation['SUM_TRAININGS']) * 100) / 100;
            unset($orgEvaluation['SUM_NOTES']);
            unset($orgEvaluation['SUM_TRAININGS']);
            $this->logger->info("[computeEvaluationsForOrganism][" . $organism->getSiret() . "][global] " . print_r($orgEvaluation, true));
            $this->createForOrganism($orgEvaluation, $organism);
            return true;
        } else {
            $this->logger->info("[computeEvaluationsForOrganism][" . $organism->getSiret() . "] no evaluations for that day or at all");
            return false;
        }
    }

    /**
     * @param Organism $organism
     * @param Certification $certification
     * @param array $parameters
     * @return ArrayCollection
     */
    public function listForOrganismAndCertification(Organism $organism, Certification $certification, array $parameters): ArrayCollection
    {
        return $this->evaluationRepository->findAllForOrganismAndCertification($organism, $certification, $parameters);
    }

    /**
     * @param Organism $organism
     * @param Certification $certification
     * @return array
     */
    public function summarizeForOrganismAndCertification(Organism $organism, Certification $certification): array
    {
        $evaluations = $this->listForOrganismAndCertification($organism, $certification, ['scope' => 'latest']);
        $result = [];

        if ($evaluations->count() > 0) {
            $sumRatings = 0;
            $sumReviews = 0;
            /** @var Evaluation $evaluation */
            foreach ($evaluations as $evaluation) {
                $sumRatings += $evaluation->getAverageRating();
                $sumReviews += $evaluation->getReviewNumber();
            }

            $result = ['averageRating' => round($sumRatings / $evaluations->count(), 2), 'reviewCount' => $sumReviews];
        }

        return $result;
    }

    /**
     * @return EntityManagerInterface
     */
    protected function checkAndRefreshEntityManager(): EntityManagerInterface
    {
        if (!$this->entityManager->isOpen()) {
            return $this->entityManager->create(
                $this->entityManager->getConnection(),
                $this->entityManager->getConfiguration(),
                $this->entityManager->getEventManager()
            );
        } else {
            return $this->entityManager;
        }
    }
}
