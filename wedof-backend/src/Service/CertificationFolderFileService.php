<?php

namespace App\Service;

use App\Entity\Attendee;
use App\Entity\Certification;
use App\Entity\CertificationFolder;
use App\Entity\CertificationFolderFile;
use App\Entity\User;
use App\Event\CertificationFolderFile\CertificationFolderFileEvents;
use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofConnectionException;
use App\Library\utils\enums\ActivityTypes;
use App\Library\utils\enums\CertificationFolderStates;
use App\Library\utils\enums\DocumentGenerationStates;
use App\Library\utils\enums\DocumentSignedStates;
use App\Library\utils\enums\FileStates;
use App\Library\utils\Tools;
use App\Repository\CertificationFolderFileRepository;
use App\Service\DataProviders\DocusealApiService;
use DateTime;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use Embed\Embed;
use ErrorException;
use Exception;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Throwable;
use Vich\UploaderBundle\Handler\DownloadHandler;

class CertificationFolderFileService implements LoggerAwareInterface
{
    private ActivityService $activityService;
    private CertificationFolderFileRepository $certificationFolderFileRepository;
    private DownloadHandler $downloadHandler;
    private EventDispatcherInterface $dispatcher;
    private LoggerInterface $logger;
    private DocusealApiService $docusealApiService;

    /**
     * CertificationFolderFileService constructor.
     * @param ActivityService $activityService
     * @param DocusealApiService $docusealApiService
     * @param CertificationFolderFileRepository $certificationFolderFileRepository
     * @param DownloadHandler $downloadHandler
     * @param EventDispatcherInterface $dispatcher
     */
    public function __construct(ActivityService $activityService, DocusealApiService $docusealApiService, CertificationFolderFileRepository $certificationFolderFileRepository, DownloadHandler $downloadHandler, EventDispatcherInterface $dispatcher)
    {
        $this->activityService = $activityService;
        $this->downloadHandler = $downloadHandler;
        $this->dispatcher = $dispatcher;
        $this->certificationFolderFileRepository = $certificationFolderFileRepository;
        $this->docusealApiService = $docusealApiService;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    //----------------
    // METHODES PUBLIQUES
    //----------------

    /**
     * used by Messages & notifications
     * @param int $entityId
     * @return CertificationFolderFile|null
     */
    public function getByEntityId(int $entityId): ?CertificationFolderFile
    {
        return $this->certificationFolderFileRepository->find($entityId);
    }

    /**
     * @param CertificationFolder $certificationFolder
     * @return array
     */
    public function listByEntityId(CertificationFolder $certificationFolder): ?array
    {
        return $this->certificationFolderFileRepository->findBy(['certificationFolder' => $certificationFolder]);
    }

    /**
     * @param CertificationFolder $certificationFolder
     * @param int $typeId
     * @return CertificationFolderFile|null
     */
    public function getByTypeId(CertificationFolder $certificationFolder, int $typeId): ?CertificationFolderFile
    {
        return $this->certificationFolderFileRepository->findOneBy(['certificationFolder' => $certificationFolder, 'typeId' => $typeId]);
    }

    /**
     * @param $file
     * @param int $typeId
     * @param CertificationFolder $certificationFolder
     * @param bool $generated
     * @param User|null $user
     * @param bool $isAttendee
     * @param string|null $title
     * @return CertificationFolderFile
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function create($file, int $typeId, CertificationFolder $certificationFolder, bool $generated = false, User $user = null, bool $isAttendee = false, string $title = null): CertificationFolderFile
    {
        $certification = $certificationFolder->getCertification();
        $fileTypes = $certification->getCertificationFolderFileTypes();
        $fileTypeIndex = array_search($typeId, array_column($fileTypes, 'id'));
        $isPartner = false;
        if ($user) {
            $isPartner = $this->isPartner($certification, $user);
        }

        $fileType = $fileTypes[$fileTypeIndex];
        if (!$generated) {
            if (empty($fileType['allowUploadAttendee']) && $isAttendee) {
                throw new WedofBadRequestHttpException("Erreur, en tant que candidat vous ne pouvez pas déposer ce fichier.");
            }
            if (empty($fileType["allowUploadPartner"]) && $isPartner) {
                throw new WedofBadRequestHttpException("Erreur, en tant que partenaire vous ne pouvez pas déposer ce fichier.");
            }
        }
        $canUploadFile = $this->canUploadFile($certificationFolder, $fileType['toState'] ?? null);
        if ($canUploadFile) {
            $certificationFolderFiles = $this->certificationFolderFileRepository->findBy(['typeId' => $typeId, 'certificationFolder' => $certificationFolder]);
            $isUpdatedFile = false;
            if ($certificationFolderFiles && empty($fileType['allowMultiple'])) {
                $isUpdatedFile = true;
                foreach ($certificationFolderFiles as $certificationFolderFile) {
                    if (($isAttendee || $isPartner) && $certificationFolderFile->getState() === FileStates::VALID()->getValue()) {
                        throw new WedofBadRequestHttpException("Erreur, vous ne pouvez pas modifier un document qui a été validé.");
                    }
                    $this->certificationFolderFileRepository->delete($certificationFolderFile);
                }
            }
            $certificationFolderFile = new CertificationFolderFile();
            $certificationFolderFile->setTypeId($typeId);
            if (is_string($file)) {
                $certificationFolderFile->setLink($file);
                $certificationFolderFile->setFilePath($file);
                $certificationFolderFile->setFileType("link");
                try {
                    $embed = new Embed();
                    $title = $title ?: $embed->get($file)->title; // This tries to resolve the URL => must be publicly accessible
                } catch (Exception $exception) {
                    $title = $file;
                }
                $certificationFolderFile->setFileName($title);
            } else {
                $certificationFolderFile->setFile($file);
            }
            if ($generated) {
                $certificationFolderFile->setGenerationState(DocumentGenerationStates::GENERATED()->getValue());
                $certificationFolderFile->setState(FileStates::VALID()->getValue());
            } else {
                $certificationFolderFile->setState($isAttendee || $isPartner ? FileStates::TO_REVIEW()->getValue() : FileStates::VALID()->getValue());
            }
            $certificationFolderFile = $this->docusealApiService->setToSignIfRequired($certificationFolder->getCertifier(), $certificationFolder, $certificationFolderFile, $file, $fileType);
            $certificationFolder->addFile($certificationFolderFile);
            $this->save($certificationFolderFile);
            if ($isAttendee) {
                $activityOrigin = "le Candidat";
            } else if ($isPartner) {
                $activityOrigin = "le Partenaire";
            }
            $data = [
                'title' => "Le document {$certificationFolderFile->getFilename()} a été " . ($isUpdatedFile ? 'remplacé' : 'ajouté'),
                'description' => $generated ? 'Généré automatiquement' : null,
                'type' => ActivityTypes::FILE(),
                'eventTime' => new DateTime(),
                'qualiopiIndicators' => $fileType['qualiopiIndicators'] ?? null,
                'origin' => $activityOrigin ?? null
            ];

            $this->activityService->create($data, $isAttendee ? null : $user, $certificationFolder, false);
            $this->sendEventCertificationFolderFile($certificationFolderFile, $isUpdatedFile ? CertificationFolderFileEvents::FILE_UPDATED : CertificationFolderFileEvents::FILE_ADDED);
            return $certificationFolderFile;
        } else {
            throw new WedofBadRequestHttpException("Erreur, cette opération est impossible car le dossier de certification est à l'état " . $certificationFolder->getState());
        }
    }

    /**
     * @param CertificationFolderFile $certificationFolderFile
     * @param User| Attendee $user
     * @return array|string[]|StreamedResponse
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function download(CertificationFolderFile $certificationFolderFile, $user)
    {
        if ($certificationFolderFile->getFileType() == 'sign') {
            $submitter = $this->docusealApiService->retrieveSubmitter($certificationFolderFile->getCertificationFolder()->getCertifier(), $certificationFolderFile, $user);
            return $this->docusealApiService->displayEmbeddedSignForm($certificationFolderFile, $submitter);
        } else if ($certificationFolderFile->getFileType() != 'link') {
            return $this->downloadHandler->downloadObject($certificationFolderFile, 'file');
        } else {
            //convert google drive content
            if (Tools::startsWith($certificationFolderFile->getLink(), 'https://drive.google.com/')
                || Tools::startsWith($certificationFolderFile->getLink(), 'https://docs.google.com/')) {
                $isGoogleForm = str_contains($certificationFolderFile->getLink(), 'https://docs.google.com/forms/');
                $code = explode('/view', $certificationFolderFile->getLink());
                $code = isset($code[1]) ? $code[0] : explode('/edit', $certificationFolderFile->getLink())[0];
                if (!Tools::contains($code, '/folders/')) { //case drive is a folder...
                    $extension = $isGoogleForm ? '/viewform' : '/preview';
                    $code = '<iframe src="' . $code . $extension . '" height="100%" width="100%"></iframe>';
                }
            } else {
                $embed = new Embed();
                try {
                   $code = $embed->get($certificationFolderFile->getLink())->code;
                } catch (Exception $e) {
                    $code = null;
                }
            }
            if ($code) {
                $code = preg_replace('/height=[\"\'][0-9]+[\"\']/i', 'height="100%"', $code);
                $code = preg_replace('/width=[\"\'][0-9]+[\"\']/i', 'width="100%"', $code);
                return ['html' => $code];
            } else {
                return ['html' => '<div class="w-full"><a href="' . $certificationFolderFile->getLink() . '" 
                                            class="mat-focus-indicator mat-flat-button mat-button-base mat-primary" target="_blank">
                                            <span class="mat-button-wrapper">Ouvrir le document</span>
                                            <div class="mat-ripple mat-button-ripple"></div>
                                            <div class="mat-button-focus-overlay"></div>
                                        </a></div>'];
            }
        }
    }

    /**
     * @param CertificationFolderFile $certificationFolderFile
     * @param User|null $user
     * @param bool $bypassChecksAndActivity
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function delete(CertificationFolderFile $certificationFolderFile, User $user = null, bool $bypassChecksAndActivity = false)
    {
        $certificationFolder = $certificationFolderFile->getCertificationFolder();
        $certifier = $certificationFolder->getCertifier();
        $certification = $certificationFolder->getCertification();
        $fileTypes = $certification->getCertificationFolderFileTypes();
        $fileTypeIndex = array_search($certificationFolderFile->getTypeId(), array_column($fileTypes, 'id'));
        $isPartner = false;
        if ($user) {
            $isPartner = $this->isPartner($certification, $user);
        }
        $title = "Le document {$certificationFolderFile->getFilename()} a été supprimé";
        if ($isPartner) {
            $title .= " par le partenaire";
        } else if (!$user) {
            $title .= " par le candidat";
        }

        $oldCertificationFolderFile = clone $certificationFolderFile;
        if ($bypassChecksAndActivity || $fileTypeIndex === false) { // Old files with deleted filetypes..
            $this->certificationFolderFileRepository->delete($certificationFolderFile);
        } else {
            $fileType = $fileTypes[$fileTypeIndex];
            if (empty($fileType['allowUploadAttendee']) && !$user) {
                throw new WedofBadRequestHttpException("Erreur, en tant que candidat vous n'avez pas accès à ce fichier");
            }
            if (empty($fileType['allowUploadPartner']) && $isPartner) {
                throw new WedofBadRequestHttpException("Erreur, en tant que partenaire vous n'avez pas accès à ce fichier");
            }
            $currentState = $certificationFolderFile->getCertificationFolder()->getState();
            $indexOfCurrentState = array_search($currentState, CertificationFolderStates::valuesStates());
            $indexOfToState = isset($fileType['toState']) ? array_search($fileType['toState'], CertificationFolderStates::valuesStates()) : null;
            $generated = !empty($fileType['generated']);
            if ($generated || !$indexOfToState || $indexOfCurrentState < $indexOfToState) {
                if ($certificationFolderFile->getSignedState() !== DocumentSignedStates::NOT_REQUIRED()->getValue()) {
                    $this->docusealApiService->deleteSubmission($certifier, $certificationFolderFile);
                }
                $this->certificationFolderFileRepository->delete($certificationFolderFile);
            } else {
                // TODO translate state
                throw new WedofBadRequestHttpException("Erreur, cette opération est impossible car le document est requis par le dossier de certification à l'état " . $currentState);
            }
        }
        if (!$bypassChecksAndActivity) {
            $this->sendEventCertificationFolderFile($oldCertificationFolderFile, CertificationFolderFileEvents::FILE_DELETED);
            $this->activityService->create([
                'title' => $title,
                'type' => ActivityTypes::FILE(),
                'eventTime' => new DateTime(),
                'origin' => !$user ? "le Candidat" : null
            ], $user, $certificationFolder, false);
        }
    }

    /**
     * @param Certification $certification
     * @param User $user
     * @return bool
     */
    public function isPartner(Certification $certification, User $user): bool
    {
        return !$certification->isCertifier($user->getMainOrganism());
    }

    /**
     * @param CertificationFolderFile $certificationFolderFile
     * @param array $body
     * @param User $user
     * @return CertificationFolderFile
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function updateState(CertificationFolderFile $certificationFolderFile, array $body, User $user): CertificationFolderFile
    {
        $properties = ['state', 'comment'];
        $previousState = $certificationFolderFile->getState();
        if ($certificationFolderFile->getGenerationState() !== DocumentGenerationStates::NOT_GENERATED()->getValue()) {
            if (isset($body['state'])) {
                unset($body['state']);
            }
            if ($certificationFolderFile->getTypeId() === Certification::CERTIFICATE_FILE_TYPE_ID) {
                unset($body['comment']);
            }
        }
        foreach ($properties as $property) {
            if (key_exists($property, $body)) {
                $setMethodName = "set" . ucwords($property);
                $certificationFolderFile->{$setMethodName}($body[$property]);
            }
        }
        $this->save($certificationFolderFile);
        $certificationFolder = $certificationFolderFile->getCertificationFolder();
        if (isset($body['state'])) {
            $eventName = 'certificationFolderFile.' . $certificationFolderFile->getState();
            if (!empty($body['comment']) && $certificationFolderFile->getState() === $previousState) {
                $eventName = CertificationFolderFileEvents::FILE_UPDATED;
            }
            $this->sendEventCertificationFolderFile($certificationFolderFile, $eventName);
            $this->activityService->create([
                'title' => "Le document {$certificationFolderFile->getFilename()} " . FileStates::toFrStringActivity($body['state']),
                'description' => $body['comment'] ?? null,
                'type' => ActivityTypes::FILE(),
                'eventTime' => new DateTime()
            ], $user, $certificationFolder, false);
        }
        return $certificationFolderFile;
    }

    /**
     * @param CertificationFolderFile $certificationFolderFile
     */
    public function save(CertificationFolderFile $certificationFolderFile)
    {
        $this->certificationFolderFileRepository->save($certificationFolderFile);
    }


    /**
     * @param CertificationFolderFile $certificationFolderFile
     * @param string $eventName
     */
    public function sendEventCertificationFolderFile(CertificationFolderFile $certificationFolderFile, string $eventName): void
    {
        $event = new CertificationFolderFileEvents($certificationFolderFile);
        $this->dispatcher->dispatch($event, $eventName);
        $this->logger->info("[" . $certificationFolderFile->getId() . " typeId " . $certificationFolderFile->getTypeId() . "][event] CertificationFolderFileState event dispatched $eventName ");
    }
    //----------------
    // METHODES PRIVES
    //----------------

    private function canUploadFile(CertificationFolder $certificationFolder, string $toState = null): bool
    {
        // needed to check if CF.state = toControl because the next state could be success or failed or toRetake (there are on the same level these 3).
        // However with the method getNextCertificationFolderStatesValues the next state after toControl is toRetake
        return !array_key_exists($toState, CertificationFolderStates::valuesStates()) ||
            (($toState === CertificationFolderStates::SUCCESS()->getValue() ||
                    $toState === CertificationFolderStates::FAILED()->getValue() ||
                    $toState === CertificationFolderStates::TO_RETAKE()->getValue())
                && $certificationFolder->getState() === CertificationFolderStates::TO_CONTROL()->getValue()) ||
            ($toState === $this->getNextCertificationFolderStatesValue($certificationFolder));
    }

    /**
     * @param $certificationFolder
     * @return string|null
     */
    private function getNextCertificationFolderStatesValue($certificationFolder): ?string
    {
        $currentState = CertificationFolderStates::from($certificationFolder->getState());
        $indexOf = array_search($currentState, CertificationFolderStates::valuesStates());
        if ($indexOf < (count(CertificationFolderStates::valuesStates()) - 1)) {
            return CertificationFolderStates::valuesStates()[$indexOf + 1];
        } else {
            return null;
        }
    }
}
