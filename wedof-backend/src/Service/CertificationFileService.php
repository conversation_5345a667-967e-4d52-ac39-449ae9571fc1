<?php

namespace App\Service;

use App\Entity\Certification;
use App\Entity\CertificationFile;
use App\Exception\WedofBadRequestHttpException;
use App\Library\utils\enums\FileStates;
use App\Library\utils\Tools;
use App\Repository\CertificationFileRepository;
use Embed\Embed;
use Exception;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Vich\UploaderBundle\Handler\DownloadHandler;

class CertificationFileService
{
    private CertificationFileRepository $certificationFileRepository;
    private DownloadHandler $downloadHandler;

    /**
     * CertificationFileService constructor.
     * @param CertificationFileRepository $certificationFileRepository
     * @param DownloadHandler $downloadHandler
     */
    public function __construct(CertificationFileRepository $certificationFileRepository, DownloadHandler $downloadHandler)
    {
        $this->downloadHandler = $downloadHandler;
        $this->certificationFileRepository = $certificationFileRepository;
    }

    //----------------
    // METHODES PUBLIQUES
    //----------------

    /**
     * @param $file
     * @param int $typeId
     * @param Certification $certification
     * @param string|null $title
     * @return CertificationFile
     */
    public function create($file, int $typeId, Certification $certification, string $title = null): CertificationFile
    {
        $organism = $certification->getDefaultCertifier();
        $fileTypes = $organism->getCertificationFileTypes();
        $fileTypeIndex = array_search($typeId, array_column($fileTypes, 'id'));
        if ($fileTypeIndex >= 0) {
            $certificationFile = new CertificationFile();
            $certificationFile->setTypeId($typeId);
            if (is_string($file)) {
                $certificationFile->setLink($file);
                $certificationFile->setFilePath($file);
                $certificationFile->setFileType("link");
                try {
                    $embed = new Embed();
                    $title = $title ?: $embed->get($file)->title; // This tries to resolve the URL => must be publicly accessible
                } catch (Exception $exception) {
                    $title = $file;
                }
                $certificationFile->setFileName($title);
            } else {
                $certificationFile->setFile($file);
            }
            $certificationFile->setState(FileStates::VALID());
            $certification->addFile($certificationFile);
            $this->save($certificationFile);
            return $certificationFile;
        } else {
            throw new WedofBadRequestHttpException("Erreur, ce type de fichier " . $typeId . " n'est pas autorisé pour cette certification");
        }
    }

    /**
     * @param CertificationFile $certificationFile
     * @return array|string[]|StreamedResponse
     */
    public function download(CertificationFile $certificationFile)
    {
        if ($certificationFile->getFileType() != 'link') {
            return $this->downloadHandler->downloadObject($certificationFile, 'file');
        } else {
            //convert google drive content
            if (Tools::startsWith($certificationFile->getLink(), 'https://drive.google.com/')
                || Tools::startsWith($certificationFile->getLink(), 'https://docs.google.com/')) {
                $code = explode('/view', $certificationFile->getLink());
                $code = isset($code[1]) ? $code[0] : explode('/edit', $certificationFile->getLink())[0];
                if (!Tools::contains($code, '/folders/')) { //case drive is a folder...
                    $code = '<iframe src="' . $code . '/preview" height="100%" width="100%"></iframe>';
                }
            } else {
                $embed = new Embed();
                try {
                    $code = $embed->get($certificationFile->getLink())->code;
                } catch (Exception $e) {
                    $code = null;
                }
            }
            if ($code) {
                $code = preg_replace('/height=[\"\'][0-9]+[\"\']/i', 'height="100%"', $code);
                $code = preg_replace('/width=[\"\'][0-9]+[\"\']/i', 'width="100%"', $code);
                return ['html' => $code];
            } else {
                return ['html' => '<div class="w-full"><a href="' . $certificationFile->getLink() . '" 
                                            class="mat-focus-indicator mat-flat-button mat-button-base mat-primary" target="_blank">
                                            <span class="mat-button-wrapper">Ouvrir le document</span>
                                            <div class="mat-ripple mat-button-ripple"></div>
                                            <div class="mat-button-focus-overlay"></div>
                                        </a></div>'];
            }
        }
    }

    /**
     * @param CertificationFile $certificationFile
     */
    public function delete(CertificationFile $certificationFile)
    {
        $this->certificationFileRepository->delete($certificationFile);
    }

    //----------------
    // METHODES PRIVES
    //----------------

    /**
     * @param CertificationFile $certificationFile
     */
    private function save(CertificationFile $certificationFile)
    {
        $this->certificationFileRepository->save($certificationFile);
    }

}
