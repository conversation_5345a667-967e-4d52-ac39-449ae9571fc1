<?php
// src/Service/InvoiceService.php
namespace App\Service;

use App\Entity\CertificationPartner;
use App\Entity\Invoice;
use App\Event\Invoice\InvoiceEvents;
use App\Exception\WedofBadRequestHttpException;
use App\Library\utils\enums\InvoiceStates;
use App\Library\utils\Tools;
use App\Repository\InvoiceRepository;
use DateTime;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\QueryBuilder;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;

class InvoiceService implements LoggerAwareInterface
{
    private InvoiceRepository $invoiceRepository;
    private string $projectDir;
    private EventDispatcherInterface $dispatcher;
    private LoggerInterface $logger;
    private CertificationPartnerService $certificationPartnerService;

    const UPDATABLE_PROPERTIES = ['externalId', 'file', 'state', 'type', 'paymentLink', 'link', 'dueDate', 'description', 'fileType'];

    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    public function __construct(InvoiceRepository $invoiceRepository, string $projectDir, EventDispatcherInterface $dispatcher, CertificationPartnerService $certificationPartnerService)
    {
        $this->invoiceRepository = $invoiceRepository;
        $this->projectDir = $projectDir;
        $this->dispatcher = $dispatcher;
        $this->certificationPartnerService = $certificationPartnerService;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    /**
     * used by Messages & notifications
     * @param int $entityId
     * @return CertificationPartner|null
     */
    public function getByEntityId(int $entityId): ?CertificationPartner
    {
        return $this->certificationPartnerService->getByEntityId($entityId);
    }

    /**
     * used by Messages & notifications
     * @param int $invoiceId
     * @return Invoice|null
     */
    public function getById(int $invoiceId): ?Invoice
    {
        return $this->invoiceRepository->find($invoiceId);
    }

    /**
     * @param Invoice $invoice
     * @return Invoice
     */
    private function save(Invoice $invoice): Invoice
    {
        return $this->invoiceRepository->save($invoice);
    }

    /**
     * @param Invoice $invoice
     * @return void
     */
    public function delete(Invoice $invoice): void
    {
        $invoiceToDelete = clone $invoice;
        $this->invoiceRepository->delete($invoice);
        $this->sendEvent($invoiceToDelete, InvoiceEvents::CERTIFICATION_PARTNER_DELETED);
    }

    /**
     * @param array $data
     * @param object $entity
     * @return Invoice
     */
    public function create(array $data, object $entity): Invoice
    {
        $invoice = new Invoice();
        $invoice->setEntityId($entity->getId());
        $invoice->setEntityClass(Tools::getClassName($entity));

        if (isset($data['file']) && is_string($data['file'])) {
            $data['link'] = $data['file'];
            $data['fileType'] = 'link';
            unset($data['file']);
        } else if (isset($data['file']) && is_file($data['file'])) {
            $data['link'] = null;
            $data['fileType'] = 'application/pdf';
        }

        foreach (self::UPDATABLE_PROPERTIES as $property) {
            if (key_exists($property, $data)) {
                $setMethodName = "set" . ucwords($property);
                $invoice->{$setMethodName}($data[$property]);
            }
        }
        $this->save($invoice);
        $this->sendEvent($invoice, InvoiceEvents::CERTIFICATION_PARTNER_CREATED);
        return $invoice;
    }

    /**
     * @param int|string $entityId
     * @param string $entityClass
     * @param array $parameters
     * @return QueryBuilder
     */
    public function listByEntity($entityId, string $entityClass, array $parameters): QueryBuilder
    {
        return $this->invoiceRepository->findAllByEntityReturnQueryBuilder($entityId, $entityClass, $parameters);
    }

    /**
     * @param int|string $entityId
     * @param string $entityClass
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countByEntity($entityId, string $entityClass): int
    {
        return $this->invoiceRepository->countByEntity($entityId, $entityClass);
    }

    /**
     * @param Invoice $invoice
     * @param array $data
     * @return Invoice
     */
    public function update(Invoice $invoice, array $data): Invoice
    {
        if (isset($data['file']) && ($invoice->getLink() !== $data['file'])) {
            if (file_exists(__DIR__ . "/../../data/invoiceFiles/" . $invoice->getInvoiceFileName()) && $invoice->getFileType() !== 'link') {
                $file = $this->projectDir . "/data/invoiceFiles/" . $invoice->getInvoiceFileName();
                unlink($file);
            }
            $invoice->setFile(null);
            $invoice->setInvoiceFileName(null);
            $invoice->setUpdatedOn(new DateTime());
            if (is_string($data['file'])) {
                $data['link'] = $data['file'];
                unset($data['file']);
                $data['fileType'] = 'link';
            } else if (is_file($data['file'])) {
                $data['link'] = null;
                $data['fileType'] = 'application/pdf';
            }
        }

        foreach (self::UPDATABLE_PROPERTIES as $property) {
            if (key_exists($property, $data)) {
                $setMethodName = "set" . ucwords($property);
                $invoice->{$setMethodName}($data[$property]);
            }
        }
        $this->save($invoice);
        $this->sendEvent($invoice, InvoiceEvents::CERTIFICATION_PARTNER_UPDATED);
        return $invoice;
    }

    /**
     * @param Invoice $invoice
     * @return Invoice
     */
    public function paid(Invoice $invoice): Invoice
    {
        if ($invoice->getState() !== InvoiceStates::PAID()->getValue()) {
            $invoice->setState(InvoiceStates::PAID());
            $this->save($invoice);
            $this->sendEvent($invoice, InvoiceEvents::CERTIFICATION_PARTNER_PAID);
            return $invoice;
        } else {
            throw new WedofBadRequestHttpException("Facture déjà marquée comme payée");
        }
    }

    /**
     * @param Invoice $invoice
     * @return Invoice
     */
    public function canceled(Invoice $invoice): Invoice
    {
        if ($invoice->getState() !== InvoiceStates::CANCELED()->getValue()) {
            return $this->update($invoice, ['state' => InvoiceStates::CANCELED()]);
        } else {
            throw new WedofBadRequestHttpException("Facture déjà marquée comme annulée");
        }
    }

    /**
     * @param Invoice $invoice
     * @return Invoice
     */
    public function waitingPayment(Invoice $invoice): Invoice
    {
        if ($invoice->getState() !== InvoiceStates::WAITING_PAYMENT()->getValue()) {
            return $this->update($invoice, ['state' => InvoiceStates::WAITING_PAYMENT()]);
        } else {
            throw new WedofBadRequestHttpException("Facture déjà marquée comme en attente de paiement");
        }
    }

    //----------------
    // METHODES PRIVES
    //----------------

    /**
     * @param Invoice $invoice
     * @param string $eventName
     */
    private function sendEvent(Invoice $invoice, string $eventName): void
    {
        if ($invoice->getEntityClass() === CertificationPartner::CLASSNAME) {
            $certificationPartner = $this->getByEntityId($invoice->getEntityId());
            $event = new InvoiceEvents($invoice, $certificationPartner);
            $this->dispatcher->dispatch($event, $eventName);
            $this->logger->info("[" . $certificationPartner->getId() . "][event] InvoiceEvent event dispatched $eventName ");
        }
    }
}
