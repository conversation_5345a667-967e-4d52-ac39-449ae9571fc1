<?php
// src/Service/TrainingService.php
namespace App\Service;

use App\Entity\Certification;
use App\Entity\CertificationPartner;
use App\Entity\Organism;
use App\Entity\Session;
use App\Entity\Training;
use App\Entity\TrainingAction;
use App\Entity\User;
use App\Library\utils\enums\ActivityTypes;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\enums\SessionStates;
use App\Library\utils\enums\TrainingActionStates;
use App\Library\utils\enums\TrainingComplianceTypes;
use App\Library\utils\enums\TrainingStates;
use App\Library\utils\Tools;
use App\Repository\TrainingActionRepository;
use App\Repository\TrainingRepository;
use App\Service\DataProviders\BaseApiService;
use App\Service\DataProviders\CpfApiService;
use App\Service\DataProviders\KairosAifApiService;
use App\Service\DataProviders\OpcoCfaApiService;
use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use ErrorException;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ContainerInterface as Container;
use Throwable;

class TrainingService implements LoggerAwareInterface
{

    private LoggerInterface $logger;
    private Container $container;
    private ActivityService $activityService;
    private EntityManagerInterface $entityManager;
    private OrganismService $organismService;
    private TrainingRepository $trainingRepository;
    private TrainingActionRepository $trainingActionRepository;

    public function __construct(EntityManagerInterface $entityManager, OrganismService $organismService, TrainingRepository $trainingRepository, TrainingActionRepository $trainingActionRepository, ActivityService $activityService, Container $container)
    {
        $this->entityManager = $entityManager;
        $this->organismService = $organismService;
        $this->trainingRepository = $trainingRepository;
        $this->trainingActionRepository = $trainingActionRepository;
        $this->activityService = $activityService;
        $this->container = $container;
    }

    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    /**
     * @param LoggerInterface $logger
     */
    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    /**
     * @param int $id
     * @return Training|null
     */
    public function getById(int $id): ?Training
    {
        return $this->trainingRepository->find($id);
    }

    /**
     * @param string $externalId
     * @param array $options
     * @param DataProviders|null $dataProvider
     * @return Training|null
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function getByExternalId(string $externalId, DataProviders $dataProvider = null, array $options = array()): ?Training
    {
        $options = array_merge(['createIfNotExist' => false, 'refresh' => false, 'noRefreshChildren' => false, 'trainingActionId' => null, 'daysMaxSinceLastUpdate' => 2, 'organism' => null], $options); // TODO check daysMaxSinceLastUpdate
        // passage par le container nécessaire pour éviter les références circulaires avec Training Action et Certification
        /** @var TrainingActionService $trainingActionService */
        $trainingActionService = $this->container->get('App\Service\TrainingActionService');
        /** @var SessionService $sessionService */
        $sessionService = $this->container->get('App\Service\SessionService');
        $training = $this->trainingRepository->findOneByExternalId($externalId);

        //no need to refresh it
        if ($training && in_array($training->getState(), [TrainingStates::DELETED()->getValue(), TrainingStates::ARCHIVED()->getValue()]) && (!isset($options['statusLabel']) || $options['statusLabel'] != 'ACTIVE')) {
            return $training;
        }

        $dataProvider = $training ? DataProviders::from($training->getDataProvider()) : $dataProvider;

        //if new or last_update at least $daysMaxSinceLastUpdate days old, grab also all actions when private
        if ($dataProvider && ((!$training && $options['createIfNotExist']) || ($training != null && ($options['refresh'] || $training->getLastUpdate()->diff(new DateTime())->d >= $options['daysMaxSinceLastUpdate'])))) {
            /** @var CpfApiService|KairosAifApiService $apiService */
            $apiService = BaseApiService::getCatalogApiServiceByDataProvider($dataProvider);
            $organism = $options['organism'] ?? $this->organismService->getOrganism(['siret' => $apiService->getSiretFromExternalId($externalId)]);

            if ($organism) {
                $trainingRawData = $apiService->getTrainingRawData($organism, $externalId, $options);
                if (!empty($trainingRawData['id'])) {
                    if ($training) { //to break the loop
                        $training->setLastUpdate(new DateTime());
                    }

                    $certification = $apiService->retrieveCertificationFromTrainingRawData($trainingRawData);
                    $training = $this->createOrUpdate($trainingRawData, $organism, $certification);

                    if ($trainingRawData['statusLabel'] == "DELETED") {
                        $trainingActionService->markAsFromTraining($training, TrainingActionStates::DELETED());
                        $sessionService->markAsFromTraining($training, SessionStates::DELETED());
                    } else if (!$options['noRefreshChildren'] && !empty($trainingRawData['actionCount'])) {
                        $actions = $apiService->getTrainingActionsRawData($training);

                        $externalIds = [];
                        foreach ($actions as $action) {
                            $externalId = $apiService->getTrainingActionExternalId($action['trainingId'], $action['id']);
                            $trainingActionService->getByExternalId($externalId, $dataProvider, ['createIfNotExist' => true, 'refresh' => isset($options['breakDeepCall']) && $options['breakDeepCall'] ? false : $options['refresh'], 'daysMaxSinceLastUpdate' => $options['daysMaxSinceLastUpdate']]); //generate trainingAction if not exist
                            $externalIds[] = $externalId;
                        }
                        $trainingActionService->markAsDeletedFromTrainingExcludeTrainingActionExternalIds($training, $externalIds);
                        $this->entityManager->refresh($training); //to get trainingsActions data associated
                    }
                }
            }
        }
        return $training;
    }

    /**
     * @param array $rawData
     * @param Organism $organism
     * @param Certification|null $certification
     * @return Training
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function createOrUpdate(array $rawData, Organism $organism, ?Certification $certification): Training
    {
        $dataProvider = DataProviders::from($rawData["dataProvider"]);
        /** @var CpfApiService|KairosAifApiService|OpcoCfaApiService $apiService */
        $apiService = BaseApiService::getCatalogApiServiceByDataProvider($dataProvider);
        $training = $this->getByExternalId($rawData['id']);

        if ($training === null) {
            $training = new Training();
            $training->setExternalId($rawData['id']);
            $training->setOrganism($organism);
            $training->setCompliance(TrainingComplianceTypes::NOT_VERIFIED());
            $this->entityManager->persist($training);
        }
        if (Tools::shouldUpdateCpfRawData($training, $rawData, 'resumeContenu')) {
            $training->setRawData($rawData);
        }
        $training->setState($apiService->getTrainingState($rawData, $certification)->getValue());
        $training->setDataProvider($dataProvider);
        $training->setCertification($certification);
        $training->setTitle($rawData['title']);
        $training->setLastUpdate(new DateTime());
        if (!$this->entityManager->contains($training)) {
            $this->entityManager->persist($training);
        }
        $this->save($training);
        $training->getTrackerId();
        $this->save($training);
        if (!$organism->isTrainingOrganism()) {
            $organism->setTrainingOrganism(true);
            $this->organismService->save($organism);
        }
        return $training;
    }

    /**
     * @param Training $training
     * @param array $body
     * @param User|null $user
     * @param CertificationPartner|null $certificationPartner
     * @return Training
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function update(Training $training, array $body, User $user = null, CertificationPartner $certificationPartner = null): Training
    {
        $valuableProperties = ['compliance', 'skillSets'];
        // attention 'compliance' ne doit être mis à jour que par le certificateur, si on ajoute une nouvelle propriété il faudra faire le check des droits ici
        foreach ($valuableProperties as $valuableProperty) {
            if (isset($body['skillSets']) && $valuableProperty === 'skillSets') {
                $training->setSkillSets($body['skillSets']);
            } else if (isset($body[$valuableProperty]) && method_exists($training, "set" . ucfirst($valuableProperty))) {
                $training->{"set" . ucfirst($valuableProperty)}($body[$valuableProperty]);
                $training->setLastComplianceUpdate(new DateTime());
                // si on ajoute une nouvelle propriété à $valuableProperties faire attention à ne mettre à jour lastComplianceUpdate que si on a la propriété 'compliance'
                if ($certificationPartner) {
                    $this->createActivity($user, $certificationPartner, $training, $body[$valuableProperty]);
                }
            }
        }
        return $this->save($training);
    }

    /**
     * @param Session $session
     * @return string
     */
    public function getTitleFromSession(Session $session): string
    {
        return $session->getTrainingAction()->getTraining()->getTitle();
    }

    /**
     * @param TrainingAction $trainingAction
     * @return string
     */
    public function getTitleFromTrainingAction(TrainingAction $trainingAction): string
    {
        return $trainingAction->getTraining()->getTitle();
    }

    /**
     * @param Organism $organism
     * @return ArrayCollection
     */
    public function getPublishedTrainingsForOrganism(Organism $organism): ArrayCollection
    {
        $parameters = [
            "order" => "asc",
            "state" => TrainingStates::PUBLISHED()->getValue()
        ];
        return $this->trainingRepository->findAllWithParams($organism, $parameters);
    }

    /**
     * @param Organism $organism
     * @param Certification $certification
     * @param string $dataProvider
     * @return ArrayCollection
     */
    public function getPublishedTrainingsForOrganismAndCertification(Organism $organism, Certification $certification, string $dataProvider = 'cpf'): ArrayCollection
    {
        $parameters = [
            "order" => "asc",
            "state" => TrainingStates::PUBLISHED()->getValue(),
            "certifInfo" => $certification->getCertifInfo(),
            "dataProvider" => $dataProvider
        ];
        return $this->trainingRepository->findAllWithParams($organism, $parameters);
    }

    /**
     * @param bool $isOnSiteTrainingAction
     * @param array $trainingActionsExternalIdsList
     * @param Training $training
     * @return string
     */
    public function getTrainingMcfLink(bool $isOnSiteTrainingAction, array $trainingActionsExternalIdsList, Training $training): string
    {
        $length = count($trainingActionsExternalIdsList);
        $certification = $training->getCertification();
        $mcfLink = '';

        if ($certification && $length >= 1) {
            $baseURL = 'https://www.moncompteformation.gouv.fr/espace-prive/html/#/formation/recherche/';
            if ($length === 1) {
                $mcfLink = $baseURL . $trainingActionsExternalIdsList[0]["externalId"];
            } else if ($length > 1) {
                $organismName = !empty($training->getOrganism()->getConnectionForDataProvider(DataProviders::CPF())->getCredentials()['userInfos']->company->organisms[0]->tradeName) ?
                    $training->getOrganism()->getConnectionForDataProvider(DataProviders::CPF())->getCredentials()['userInfos']->company->organisms[0]->tradeName :
                    $training->getOrganism()->getName();
                $occurences = min($length, 10);
                if ($isOnSiteTrainingAction) {
                    $ou = [
                        'modality' => 'EN_CENTRE_MIXTE',
                        'type' => 'CP',
                        'ville' => ['nom' => 'TOULOUSE', 'codePostal' => '31000']
                    ];
                } else {
                    $ou = [
                        'modality' => 'A_DISTANCE',
                        'type' => 'CP'
                    ];
                }
                $query = [
                    'ou' => $ou,
                    'nomOrganisme' => mb_strtoupper($organismName), // mb or not mb
                    'quoi' => $training->getTitle(),
                    'debutPagination' => 1,
                    'nombreOccurences' => $occurences,
                    'certifications' => [$certification->getExternalId()],
                ];
                if ($isOnSiteTrainingAction) {
                    $query['distance'] = 50000;
                }
                $url = $baseURL . 'modalite/' . ($isOnSiteTrainingAction ? 'localite/' : '') . 'resultats?q=';
                $encodedQuery = rawurlencode(json_encode($query));
                $mcfLink = $url . $encodedQuery;
                // https://www.moncompteformation.gouv.fr/espace-prive/html/#/formation/recherche/modalite/resultats?q=
                //{"ou":{"modality":"A_DISTANCE","type":"CP"},
                // "nomOrganisme":"KAGILUM","quoi": "Nom de la formation", "debutPagination": 1, "nombreOccurences": 5,"certifications":["RS5695"]}

                // https://www.moncompteformation.gouv.fr/espace-prive/html/#/formation/recherche/modalite/localite/resultats?q={"ou":{"modality":"EN_CENTRE_MIXTE","type":"CP",
                // "ville":{"nom":"TOULOUSE","codePostal":"31000"}},"nomOrganisme":"KAGILUM",
                // "quoi": "Nom de la formation", "debutPagination": 1, "nombreOccurences": 5,"certifications":["RS5695"],"distance":50000}
            }
        }

        return $mcfLink;
    }

    /**
     * @param Organism $organism
     * @param array $certifications
     * @param array $options
     * @return ArrayCollection
     */
    public function listPublished(Organism $organism, array $certifications, array $options = []): ArrayCollection
    {
        return $this->trainingRepository->findAllPublished($organism, $certifications, $options);
    }

    /**
     * @param array $externalIds
     * @return ArrayCollection
     */
    public function markAsUnpublishedNotUpdated(array $externalIds): ArrayCollection
    {
        return $this->trainingRepository->markAsUnpublishedNotUpdatedTrainings($externalIds);
    }

    /**
     * @param array $externalIds
     * @return ArrayCollection
     */
    public function markAsDeletedNotUpdated(array $externalIds): ArrayCollection
    {
        return $this->trainingRepository->markAsDeletedNotUpdatedTrainings($externalIds);
    }

    //-----------------
    // METHODES PRIVEES
    //-----------------

    /**
     * @param Training $training
     * @return Training
     * @throws Throwable
     */
    private function save(Training $training): Training
    {
        return $this->trainingRepository->save($training);
    }

    /**
     * @param User $user
     * @param CertificationPartner $certificationPartner
     * @param Training $training
     * @param string $complianceType
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    private function createActivity(User $user, CertificationPartner $certificationPartner, Training $training, string $complianceType): void
    {
        $trainingActionsOnline = $this->trainingActionRepository->getExternalIdsByTraining($training, ['state' => TrainingActionStates::PUBLISHED()->getValue(), 'modality' => 'online']);
        $trainingActionsOnSite = $this->trainingActionRepository->getExternalIdsByTraining($training, ['state' => TrainingActionStates::PUBLISHED()->getValue(), 'modality' => 'onsite']);
        if (count($trainingActionsOnSite) >= 1) {
            $trainingActionLink = $this->getTrainingMcfLink(true, $trainingActionsOnSite, $training);
        } else {
            $trainingActionLink = $this->getTrainingMcfLink(false, $trainingActionsOnline, $training);
        }
        $this->activityService->create([
            'title' => 'La formation ' . $training->getTitle() . ' a été déclarée comme ' . TrainingComplianceTypes::toFrString($complianceType),
            'type' => ActivityTypes::REMARK(),
            'eventTime' => new DateTime(),
            'link' => $trainingActionLink,
            'origin' => 'Wedof'
        ], $user, $certificationPartner, false);
    }
}
