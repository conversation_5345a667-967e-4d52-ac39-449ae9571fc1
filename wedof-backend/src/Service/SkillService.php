<?php
// src/Service/SkillService.php
namespace App\Service;

use App\Entity\Certification;
use App\Entity\Skill;
use App\Library\utils\enums\CertificationSkillType;
use App\Library\utils\enums\CertificationTypes;
use App\Repository\SkillRepository;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\QueryBuilder;
use Throwable;

class SkillService
{

    private SkillRepository $skillRepository;

    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    public function __construct(SkillRepository $skillRepository)
    {
        $this->skillRepository = $skillRepository;
    }

    /**
     * @param int $id
     * @return Skill|null
     */
    public function getById(int $id): ?Skill
    {
        return $this->skillRepository->findOneBy(['id' => $id]);
    }

    /**
     * @param array $body
     * @param Certification $certification
     * @param Skill|null $parentSkill
     * @return Skill
     * @throws Throwable
     */
    public function create(array $body, Certification $certification, Skill $parentSkill = null): Skill
    {
        $body = array_merge(['type' => CertificationSkillType::SKILL()->getValue()], $body);

        $skill = new Skill();
        $skill->setCertification($certification);
        if (isset($body['createParentSkill']) && $body['createParentSkill'] === true) {
            $order = $this->createOrder($certification, $parentSkill, true);
        } else {
            $order = $body['order'] ?? $this->createOrder($certification, $parentSkill);
        }
        $skill->setOrder($order);
        $skill->setLabel($body['label']);
        $skill->setModalities($body['modalities'] ?? null);
        $skill->setDescription($body['description'] ?? null);
        $skill->setType($body['type']);
        if ($parentSkill) {
            $skill->setParentSkill($parentSkill);
        }
        $this->save($skill);
        return $skill;
    }


    /**
     * @param Skill $skill
     * @param array $body
     * @param Skill|null $parentSkill
     * @return Skill
     * @throws Throwable
     */
    public function update(Skill $skill, array $body, Skill $parentSkill = null): Skill
    {
        $properties = ['label', 'modalities', 'description'];
        foreach ($properties as $property) {
            if (key_exists($property, $body)) {
                $setMethodName = "set" . ucwords($property);
                $skill->{$setMethodName}($body[$property]);
            }
        }
        if ($parentSkill) {
            $skill->setParentSkill($parentSkill);
        }
        $this->save($skill);
        return $skill;
    }

    /**
     * @param array $parameters
     * @return QueryBuilder
     */
    public function listReturnQueryBuilder(array $parameters): QueryBuilder
    {
        return $this->skillRepository->findAllReturnQueryBuilder($parameters);
    }

    /**
     * @param Certification $certification
     * @param string $type
     * @param int $order
     * @return Skill|null
     */
    public function getByCertificationAndTypeAndOrder(Certification $certification, string $type, int $order): ?Skill
    {
        return $this->skillRepository->findOneBy([
            'certification' => $certification,
            'type' => $type,
            'order' => $order
        ]);
    }

    /**
     * @param Skill $skill
     * @param bool $isSkillSet
     * @return void
     * @throws Throwable
     */
    public function delete(Skill $skill, bool $isSkillSet = false)
    {
        $order = $skill->getOrder();
        $certification = $skill->getCertification();
        $params = [
            'skillOrder' => $skill->getOrder(),
            'certification' => $certification,
            'type' => $isSkillSet ? CertificationSkillType::SKILL_SET()->getValue() : CertificationSkillType::SKILL()->getValue()
        ];
        if ($certification->getType() === CertificationTypes::RNCP()->getValue()) {
            $params = array_merge(['parentSkill' => $skill->getParentSkill()], $params);
        }
        $nextSkills = $this->skillRepository->listByParameters($params);
        $this->skillRepository->delete($skill);
        if ($nextSkills && sizeof($nextSkills) > 0) {
            /** @var Skill $nextSkill */
            foreach ($nextSkills as $key => $nextSkill) {
                $nextSkill->setOrder($order + $key);
                $this->save($nextSkill);
            }
        }
    }

    /**
     * @param Skill $skill
     * @return Skill
     * @throws Throwable
     */
    public function save(Skill $skill): Skill
    {
        return $this->skillRepository->save($skill);
    }

    //----------------
    // METHODES PRIVEES
    //----------------


    /**
     * @param Certification $certification
     * @param Skill|null $parentSkill
     * @param bool $createParentSkill
     * @return string
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    private function createOrder(Certification $certification, Skill $parentSkill = null, bool $createParentSkill = false): string
    {
        $lastCode = $this->skillRepository->getLastOrder($certification, $parentSkill, $createParentSkill);
        if ($lastCode) {
            $codeToInsert = $lastCode + 1;
            $order = sprintf('%02d', $codeToInsert);
        } else {
            $order = 1;
        }
        return $order;
    }
}
