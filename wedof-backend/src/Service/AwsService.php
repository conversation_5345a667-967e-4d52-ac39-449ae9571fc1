<?php

namespace App\Service;

use Aws\Result;
use Aws\Ses\SesClient;

class AwsService
{
    const VERIFY_EMAIL_IDENTITY_TEMPLATE = 'VerifyEmailIdentityTemplate';

    private SesClient $sesClient;

    public function __construct()
    {
        $this->sesClient = new SesClient([
            'region' => 'eu-west-1',
            'credentials' => [
                'key' => $_ENV['AWS_ACCESS_KEY_ID'],
                'secret' => $_ENV['AWS_SECRET_ACCESS_KEY']
            ]
        ]);
    }

    /**
     * @param string $email
     * @return Result
     */
    public function verifyEmailIdentity(string $email): Result
    {
        return $this->sesClient->sendCustomVerificationEmail([
            'EmailAddress' => $email,
            'TemplateName' => self::VERIFY_EMAIL_IDENTITY_TEMPLATE
        ]);
    }

    /**
     * @param string $email
     * @return Result
     */
    public function deleteEmailIdentity(string $email): Result
    {
        return $this->sesClient->deleteIdentity([
            'Identity' => $email
        ]);
    }

    /**
     * @param array $pendingEmails
     * @return array
     */
    public function listNewEmailIdentities(array $pendingEmails): array
    {
        $response = $this->sesClient->getIdentityVerificationAttributes([
            'Identities' => $pendingEmails
        ]);

        return array_keys(array_filter($response->get('VerificationAttributes'),
            fn(array $email) => $email['VerificationStatus'] === 'Success'));
    }


    /**
     * THIS METHOD IS NOT CALLED ON PURPOSE! IT'S HERE SO DEVS CAN CALL IT MANUALLY TO CHANGE EMAIL TEMPLATE
     * @return void
     */
    public function setIdentityVerificationEmailTemplate(): void
    {
        $this->sesClient->updateCustomVerificationEmailTemplate(
            [
                "TemplateName" => "VerifyEmailIdentityTemplate",
                "FromEmailAddress" => "<EMAIL>",
                "TemplateSubject" => "Confirmez votre expéditeur Wedof",
                "TemplateContent" => "
<html>
<head></head>
<body style='font-family:Arial, sans-serif;'>
<p>Bonjour</p>
<p>Vous recevez ce message car vous souhaitez utiliser cette adresse email comme expéditeur depuis Wedof.</p>
<p>Afin de confirmer que vous êtes bien à l'origine de cette demande, cliquez sur le lien ci-dessous.</p>
<p>Si vous n'êtes pas à l'origine de cette demande, contactez le support en répondant à cet email.</p>
Lien d'activation :
</body>
</html>
                ",
                "SuccessRedirectionURL" => "https://www.wedof.fr/app/public/organisms/verifyIdentitySuccess",
                "FailureRedirectionURL" => "https://www.wedof.fr/app/public/organisms/verifyIdentityFailure"
            ]
        );
    }
}