<?php
// src/Service/SessionService.php
namespace App\Service;

use App\Entity\Organism;
use App\Entity\Payment;
use App\Entity\RegistrationFolder;
use App\Entity\Session;
use App\Event\Payment\PaymentEvents;
use App\Library\utils\enums\PaymentStates;
use App\Library\utils\enums\PaymentTypes;
use App\Repository\PaymentRepository;
use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException as NonUniqueResultExceptionAlias;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\QueryBuilder;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpClient\Exception\ServerException;
use Throwable;

class PaymentService implements LoggerAwareInterface
{

    private PaymentRepository $paymentRepository;
    private RegistrationFolderService $registrationFolderService;
    private LoggerInterface $logger;
    private EventDispatcherInterface $dispatcher;

    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    /**
     * PaymentService constructor.
     * @param EventDispatcherInterface $dispatcher
     * @param PaymentRepository $paymentRepository
     * @param RegistrationFolderService $registrationFolderService
     */
    public function __construct(EventDispatcherInterface $dispatcher, PaymentRepository $paymentRepository, RegistrationFolderService $registrationFolderService)
    {
        $this->dispatcher = $dispatcher;
        $this->registrationFolderService = $registrationFolderService;
        $this->paymentRepository = $paymentRepository;
    }

    /**
     * @param LoggerInterface $logger
     */
    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    /**
     * @param array $rawData
     * @param Organism $organism
     * @return Payment
     * @throws NonUniqueResultExceptionAlias
     * @throws ServerException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function createOrUpdate(array $rawData, Organism $organism): ?Payment
    {
        $payment = $this->paymentRepository->findOneByRegistrationFolderIdAndType(["registrationFolderId" => $rawData['registrationFolderId'], "type" => $rawData['type']]);
        $registrationFolder = $this->registrationFolderService->getByExternalId($rawData['registrationFolderId']);
        $currentState = null;
        if ($payment === null) {
            //it happens...
            if ($registrationFolder && isset($rawData['billNumber']) && $rawData['billNumber'] != null) {
                $payment = new Payment();
                $payment->setAmount($rawData['amount']);
                $payment->setBillNumber($rawData['billNumber']);
                $payment->setType($rawData['type']);
                $payment->setRawData($rawData);
                $payment->setOrganism($organism);
                $payment->setRegistrationFolder($registrationFolder);
            } else {
                return null;
            }
        } else {
            $payment->setAmount($rawData['amount']);
            $payment->setRawData($rawData);
            $currentState = $payment->getState();
        }
        //can change during waiting
        if (isset($rawData['scheduledDate'])) {
            $scheduledDate = new DateTime();
            $scheduledDate->setTimestamp(strtotime($rawData['scheduledDate']));
            $payment->setScheduledDate($scheduledDate);
        } else {
            $payment->setScheduledDate(null);
        }

        $stateChanged = ($currentState == null || $currentState != strtolower($rawData['status']));
        if ($stateChanged) {
            $payment->setState(strtolower($rawData['status']));
            $payment->setLastUpdate(new DateTime());
        }

        $this->paymentRepository->save($payment);

        if ($stateChanged) {
            //payment event
            if (in_array($payment->getState(), [PaymentStates::ISSUED()->getValue(), PaymentStates::REJECTED()->getValue(), PaymentStates::WAITING()->getValue()])) {
                $_event = new PaymentEvents($payment);
                if ($payment->getType() == PaymentTypes::BILL()) {
                    $eventName = "payment." . $payment->getState();
                } else {
                    $eventName = "payment.deposit" . ucfirst($payment->getState());
                }
                $this->dispatcher->dispatch($_event, $eventName);
                $this->logger->info("[payment][" . $payment->getId() . "][event] Payment event dispatched: $eventName");
            }
        }
        $this->registrationFolderService->updateBillingStateAndDates($registrationFolder, $payment);
        return $payment;
    }

    /**
     * @param Organism $organism
     * @param PaymentStates $state
     * @param null $orderBy
     * @return Session|null
     */
    public function getLastPaymentByOrganismAndState(Organism $organism, PaymentStates $state, $orderBy = null): ?Payment
    {
        return $this->paymentRepository->findOneByOrganismAndState($organism, $state, $orderBy);
    }

    /**
     * @param ArrayCollection $organisms
     * @param array $parameters
     * @return QueryBuilder
     */
    public function findAllReturnQueryBuilder(ArrayCollection $organisms, array $parameters): QueryBuilder
    {
        return $this->paymentRepository->findAllReturnQueryBuilder($organisms, $parameters);
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @return DateTime
     * @throws NonUniqueResultExceptionAlias
     */
    public function getLastPaymentDateForRegistrationFolder(RegistrationFolder $registrationFolder): ?DateTime
    {
        return $this->paymentRepository->findLastPaymentDateForRegistrationFolder($registrationFolder);
    }
}
