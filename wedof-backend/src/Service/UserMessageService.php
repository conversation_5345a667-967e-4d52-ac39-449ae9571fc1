<?php

namespace App\Service;

use App\Entity\UserMessage;
use App\Repository\UserMessageRepository;
use Doctrine\Common\Collections\ArrayCollection;

class UserMessageService
{
    private UserMessageRepository $messageRepository;

    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    public function __construct(UserMessageRepository $messageRepository)
    {
        $this->messageRepository = $messageRepository;
    }

    /**
     * @param array $parameters
     * @return ArrayCollection
     */
    public function listWithParams(array $parameters): ArrayCollection
    {
        return $this->messageRepository->findAllWithParams($parameters);
    }

    /**
     * @param $text
     * @param $user
     * @param array $options
     * @return UserMessage
     */
    public function create($text, array $options = [], $user = null): UserMessage
    {
        $message = new UserMessage();
        $message
            ->setMessage($text)
            ->setType($options['type']);
        if (isset($options['dismissed'])) {
            $message->setDismissed($options['dismissed']);
        }
        if (isset($options['showIcon'])) {
            $message->setShowIcon($options['showIcon']);
        }
        if (isset($options['visible'])) {
            $message->setVisible($options['visible']);
        }
        if ($user) {
            $message->addUser($user);
        }
        return $this->save($message);
    }

    /**
     * @param UserMessage $message
     * @param bool $visibility
     * @return UserMessage
     */
    public function updateVisibility(UserMessage $message, bool $visibility): UserMessage
    {
        $message->setVisible($visibility);
        return $this->save($message);
    }

    /**
     * @param UserMessage $message
     * @return UserMessage
     */
    public function save(UserMessage $message): UserMessage
    {
        return $this->messageRepository->save($message);
    }

    /**
     * @param UserMessage $message
     * @return bool
     */
    public function delete(UserMessage $message): bool
    {
        return $this->messageRepository->delete($message);
    }

    //----------------
    // METHODES PRIVES
    //----------------
}
