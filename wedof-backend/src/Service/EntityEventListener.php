<?php

namespace App\Service;

use App\Entity\RegistrationFolder;
use App\Entity\User;
use Doctrine\ORM\Event\LifecycleEventArgs;

class EntityEventListener
{
    //-------------------
    // METHODES PUBLIQUES
    //-------------------
    /**
     * @param LifecycleEventArgs $args
     * @return RegistrationFolder|mixed|object
     */
    public function postLoad(LifecycleEventArgs $args)
    {
        $entity = $args->getObject();
        switch (true) {
            case $entity instanceof RegistrationFolder:
                $this->registrationFolderPostLoad($entity);
                break;
        }
        return $entity;
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @return RegistrationFolder
     */
    public static function processRegistrationFolderPostLoad(RegistrationFolder $registrationFolder): RegistrationFolder
    {
        $trainingActionInfo = $registrationFolder->getRawData()['trainingActionInfo'];
        $infos = array(
            'duration' => $trainingActionInfo['indicativeDuration'] ?? ($trainingActionInfo['averageDuration'] ?? null),
            'sessionStartDate' => $trainingActionInfo['sessionStartDate'],
            'sessionEndDate' => $trainingActionInfo['sessionEndDate'],
            'title' => $trainingActionInfo['title'],
            'totalIncl' => $trainingActionInfo['totalIncl'],
        );
        $registrationFolder->setTrainingActionInfo($infos);
        return $registrationFolder;
    }

    //----------------
    // METHODES PRIVES
    //----------------

    /**
     * @param RegistrationFolder $registrationFolder
     */
    private function registrationFolderPostLoad(RegistrationFolder $registrationFolder): void
    {
        self::processRegistrationFolderPostLoad($registrationFolder);
    }
}
