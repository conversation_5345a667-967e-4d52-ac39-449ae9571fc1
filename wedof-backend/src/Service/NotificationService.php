<?php

namespace App\Service;

use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class NotificationService
{
    private HttpClientInterface $client;

    public function __construct(HttpClientInterface $client)
    {
        $this->client = $client;
    }

    /**
     * @param $topic
     * @param $title
     * @param $content
     * @return void
     * @throws TransportExceptionInterface
     */
    public function sendNotification($topic, $title, $content)
    {
        $this->client->request('POST', '' . 'VotreTopic', [ // changez avec les infos de votre serveur
            'headers' => [
                'Content-Type' => 'text/plain',
                'Title' => 'Mon Super Titre',
            ],
            'body' => 'Mon Super message'
        ]);
    }
}