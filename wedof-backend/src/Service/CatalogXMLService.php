<?php

namespace App\Service;

use App\Entity\Certification;
use App\Entity\Organism;
use App\Entity\Session;
use App\Entity\Training;
use App\Entity\TrainingAction;
use App\Library\utils\enums\SessionStates;
use App\Library\utils\enums\TrainingActionStates;
use App\Library\utils\enums\TrainingStates;
use App\Library\utils\Tools;
use App\Repository\SessionRepository;
use App\Repository\TrainingActionRepository;
use App\Repository\TrainingRepository;
use DateTime;
use DOMDocument;
use Exception;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use SimpleXMLElement;
use Throwable;

class CatalogXMLService implements LoggerAwareInterface
{
    private const DATE_FORMAT = 'Ymd';

    private LoggerInterface $logger;

    private TrainingRepository $trainingRepository;
    private TrainingActionRepository $trainingActionRepository;
    private SessionRepository $sessionRepository;


    public function __construct(TrainingRepository       $trainingRepository,
                                TrainingActionRepository $trainingActionRepository,
                                SessionRepository $sessionRepository
    )
    {
        $this->trainingRepository = $trainingRepository;
        $this->trainingActionRepository = $trainingActionRepository;
        $this->sessionRepository = $sessionRepository;
    }

    /**
     * @param LoggerInterface $logger
     */
    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    /**
     * @param Organism $organism
     * @return void
     * @throws Exception
     */
    public function generateCpfCatalogXML(Organism $organism): array
    {
        $trainings = $this->trainingRepository->findAllForCpfXml($organism);
        $this->logger->debug('[CatalogXml] Starting export of organism ' . $organism->getSiret() . ' on ' . count($trainings) . ' trainings');
        $xmlRoot = new SimpleXMLElement('<lheo xmlns="https://www.of.moncompteformation.gouv.fr"></lheo>');
        $report = [];
        $data = [
            'offres' => array_map(function ($training) use ($organism, &$report) {
                return $this->getTrainingAsArray($organism, $training, $report);
            }, $trainings)
        ];
        Tools::arrayToXml($xmlRoot, $data);
        $this->logger->debug('[CatalogXml] End of export of organism ' . $organism->getSiret());
        $encoding = 'ISO-8859-1';
        $dom = new DOMDocument("1.0", $encoding); //encoding will be overrided by loadXML but let it there for knowning purpose
        $dom->preserveWhiteSpace = false;
        $dom->formatOutput = true;
        $dom->loadXML($xmlRoot->asXML());
        $dom->encoding = $encoding; //add back encoding
        usort($report, function ($reportRow1, $reportRow2) {
            return $reportRow2['result'] <=> $reportRow1['result'];
        });
        array_unshift($report, ['OBJET', 'NUMERO_FORMATION', 'NUMERO_ACTION', 'NUMERO_SESSION', 'CERTIFICATION', 'STATUT', 'COMMENTAIRE', 'LIEN']);
        return [
            'xml' => $dom->saveXML(),
            'report' => $report
        ];
    }

    //-----------------
    // METHODES PRIVÉES
    //-----------------

    /**
     * @param Organism $organism
     * @param Training $training
     * @param array $report
     * @return array
     * @throws Exception
     */
    private function getTrainingAsArray(Organism $organism, Training $training, array &$report): array
    {
        $certification = $training->getCertification();
        $trainingActions = $this->trainingActionRepository->findAllByTrainingForCpfXml($organism, $training->getExternalId());
        $trainingActionsArrays = array_map(function ($trainingAction) use ($organism, &$report, $certification) {
            return $this->getTrainingActionAsArray($organism, $trainingAction, $certification, $report);
        }, $trainingActions);
        $trainingActionsArrays = array_values(array_filter($trainingActionsArrays, fn($trainingActionArray) => $trainingActionArray['_if']));

        $rawData = $training->getRawData();
        $this->logger->debug('[CatalogXml] Starting export of training ' . $training->getId());

        if (isset($rawData['presenceBlocsCompetences']) || isset($rawData['blocsCompetences']) || isset($rawData['blocCompetencesPersonalized'])) {
            throw new Exception('Erreur le training a des blocs de compétences mais ils ne sont pas encore supportés par la moulinette. Il faut les ajouter. Training id: ' . $training->getExternalId());
        }

        $include = true;
        if ($training->getState() === TrainingStates::ARCHIVED()->getValue()) { // Shouldn't happen
            $include = false;
            $this->addReportLine($report, $training, $certification, 'EXCLU', "La formation est archivée");
        } else if ($training->getState() === TrainingStates::DRAFT()->getValue()) {
            $include = false;
            $this->addReportLine($report, $training, $certification, 'EXCLU', "La formation est en brouillon");
        } else if ($training->getState() !== TrainingStates::PUBLISHED()->getValue()) {
            $include = false;
            $this->addReportLine($report, $training, $certification, 'EXCLU', "La formation est n'est pas publiée");
        } else if (count($trainingActionsArrays) <= 0) {
            $include = false;
            $this->addReportLine($report, $training, $certification, 'EXCLU', "La formation n'a pas d'actions de formation publiées");
        }
        if ($include) {
            $this->addReportLine($report, $training, $certification);
        }
        return [
            '_if' => $include,
            '_key' => 'formation',
            '_attributes' => [
                'numero' => $rawData['shortId'],
                'datemaj' => date(self::DATE_FORMAT), // Doesn't seem to be used
                'datecrea' => $this->getFormattedDate($rawData['creationDate']) // It is erased by the current date when importing... Seems that they inverted dates in the processing of the XML file
            ],
            '_value' => [
                'intitule-formation' => [
                    '_cdata' => true,
                    '_value' => Tools::normalizeUnicodeChars($rawData['title']),
                ],
                'objectif-formation' => [
                    '_cdata' => true,
                    '_value' => Tools::normalizeUnicodeChars($rawData['goal'])
                ],
                'resultats-attendus' => [
                    '_cdata' => true,
                    '_value' => Tools::normalizeUnicodeChars($rawData['expectedResults'])
                ],
                'contenu-formation' => [
                    '_cdata' => true,
                    '_value' => Tools::normalizeUnicodeChars($rawData['content'])
                ],
                'parcours-de-formation' => $rawData['courseType'],
                'objectif-general-formation' => $rawData['mainGoal'],
                'certification' => [
                    'code-' . $certification->getType() => $certification->getExternalId() // TODO(catalogXml) from rawData instead? upside: we don't rely on wedof objects but rather on EDOF data, downside: not present in old rawdata in dev
                ],
                ...$trainingActionsArrays,
                'organisme-formation-responsable' => [
                    'SIRET-organisme-formation' => [
                        'SIRET' => $organism->getSiret()
                    ]
                ],
                'extras' => [
                    '_attributes' => ['info' => 'formation'],
                    '_value' => [
                        'extra' => [
                            '_attributes' => ['info' => 'resume-contenu'],
                            '_cdata' => true,
                            '_value' => Tools::normalizeUnicodeChars($rawData['contentSummary'])
                        ]
                        // TODO(catalogXml) add blocs competences
                    ]
                ]
            ]
        ];
    }

    /**
     * @param Organism $organism
     * @param TrainingAction $trainingAction
     * @param Certification $certification
     * @param array $report
     * @return array
     */
    private function getTrainingActionAsArray(Organism $organism, TrainingAction $trainingAction, Certification $certification, array &$report): array
    {
        $this->logger->debug('[CatalogXml] Starting export of training action ' . $trainingAction->getId());

        $sessions = $this->sessionRepository->findAllByActionForCpfXml($organism, $trainingAction->getExternalId());
        $sessionsArrays = array_map(function ($session) use ($organism, $trainingAction, &$report, $certification) {
            return $this->getSessionAsArray($organism, $session, $trainingAction, $certification, $report);
        }, $sessions);
        $sessionsArrays = array_values(array_filter($sessionsArrays, fn($sessionArray) => $sessionArray['_if']));

        $rawData = $trainingAction->getRawData();
        $trainingAddress = !empty($rawData['trainingAddress']) ? $this->getAddressFromRawData($rawData['trainingAddress']) : ['_if' => false];
        $geographicalPerimeter = $rawData['geographicalPerimeter'];
        if (isset($geographicalPerimeter) && $geographicalPerimeter === '7') {
            // LHEO & XSD says "Autres" is 0, EDOF says it's '7' (raw data and pdf). Don't know which one is right but for XSD I choose '0'
            // http://lheo.gouv.fr/2.3/lheo/dict-perimetre-recrutement.html#dict-perimetre-recrutement
            $geographicalPerimeter = '0';
        }

        $include = true;
        if ($trainingAction->getState() === TrainingActionStates::ARCHIVED()->getValue()) { // Shouldn't happen
            $include = false;
            $this->addReportLine($report, $trainingAction, $certification, 'EXCLU', "L'action de formation est archivée");
        } else if ($trainingAction->getState() === TrainingActionStates::DRAFT()->getValue()) {
            $include = false;
            $this->addReportLine($report, $trainingAction, $certification, 'EXCLU', "L'action de formation est en brouillon");
        } else if ($trainingAction->getState() !== TrainingActionStates::PUBLISHED()->getValue()) {
            $include = false;
            $this->addReportLine($report, $trainingAction, $certification, 'EXCLU', "L'action de formation n'est pas publiée");
        } else if (count($sessionsArrays) <= 0) {
            $include = false;
            $this->addReportLine($report, $trainingAction, $certification, 'EXCLU', "L'action de formation n'a pas de sessions publiées");
        }
        if ($include) {
            $this->addReportLine($report, $trainingAction, $certification);
        }

        return [
            '_if' => $include,
            '_key' => 'action',
            '_attributes' => [
                'numero' => $rawData['shortId'],
                'datemaj' => date(self::DATE_FORMAT), // Doesn't seem to be used
                'datecrea' => $this->getFormattedDate($rawData['creationDate']) // It is erased by the current date when importing... Seems that they inverted dates in the processing of the XML file
            ],
            '_value' => [
                'niveau-entree-obligatoire' => $rawData['minLevelMandatory'],
                'modalites-enseignement' => $rawData['teachingMethod'],
                'conditions-specifiques' => [
                    '_ifNotEmpty' => true,
                    '_cdata' => true,
                    '_value' => Tools::normalizeUnicodeChars($rawData['specificConditionsAndPrerequisites'])
                ],
                'lieu-de-formation' => [
                    'coordonnees' => $this->getCoordonneesFromRawData($rawData['trainingContact'], $trainingAddress)
                ],
                'modalites-entrees-sorties' => $rawData['inOutModality'],
                'url-action' => [
                    '_if' => !empty($rawData['urlAction']),
                    '_value' => [
                        'urlweb' => $rawData['urlAction'],
                    ]
                ],
                ...$sessionsArrays,
                'adresse-information' => [
                    '_if' => !empty($rawData['informationAddress']),
                    '_value' => [
                        'adresse' => $this->getAddressFromRawData($rawData['informationAddress'])
                    ]
                ],
                ...(array_map(fn($informationDate) => [
                    '_if' => !empty($informationDate),
                    '_key' => 'date-information',
                    '_value' => [
                        'date' => $this->getFormattedDate($informationDate) // Don't know the format...
                    ]
                ], $rawData['informationDates'])),
                'restauration' => [
                    '_ifNotEmpty' => true,
                    '_cdata' => true,
                    '_value' => Tools::normalizeUnicodeChars($rawData['restauration'])
                ],
                'hebergement' => [
                    '_ifNotEmpty' => true,
                    '_cdata' => true,
                    '_value' => Tools::normalizeUnicodeChars($rawData['accommodation'])
                ],
                'transport' => [
                    '_ifNotEmpty' => true,
                    '_cdata' => true,
                    '_value' => Tools::normalizeUnicodeChars($rawData['transport'])
                ],
                'acces-handicapes' => [
                    '_cdata' => true,
                    '_value' => Tools::normalizeUnicodeChars($rawData['accessibilityActionForPeopleWithReducedMobility'])
                ],
                'langue-formation' => $rawData['language'],
                'modalites-recrutement' => [
                    '_cdata' => true,
                    '_ifNotEmpty' => true,
                    '_value' => Tools::normalizeUnicodeChars($rawData['recruitmentInformation'])
                ],
                'modalites-pedagogiques' => [
                    '_cdata' => true,
                    '_ifNotEmpty' => true,
                    '_value' => Tools::normalizeUnicodeChars($rawData['teachingModality'])
                ],
                'code-perimetre-recrutement' => [
                    '_ifNotEmpty' => true,
                    '_value' => $geographicalPerimeter
                ],
                'infos-perimetre-recrutement' => [
                    '_cdata' => true,
                    '_ifNotEmpty' => true,
                    '_value' => Tools::normalizeUnicodeChars($rawData['informationRecruitmentPerimeter'])
                ],
                'nombre-heures-centre' => [
                    '_ifNotEmpty' => true,
                    '_value' => $rawData['numberOfHoursInCenter']
                ],
                'nombre-heures-entreprise' => [
                    '_ifNotEmpty' => true,
                    '_value' => $rawData['numberOfHoursInCompany']
                ],
                'extras' => [
                    '_attributes' => ['info' => 'action'],
                    '_value' => [
                        [
                            '_key' => 'extra',
                            '_attributes' => ['info' => 'contact-information'],
                            '_value' => [
                                'coordonnees' => $this->getCoordonneesFromRawData($rawData['informationContact'])
                            ]
                        ],
                        [
                            '_ifNotEmpty' => true,
                            '_key' => 'extra',
                            '_attributes' => ['info' => 'modalites-handicap'],
                            '_cdata' => true,
                            '_value' => Tools::normalizeUnicodeChars($rawData['modalitiesPeopleDisabilities'])
                        ],
                        [
                            '_ifNotEmpty' => true,
                            '_key' => 'extra',
                            '_attributes' => ['info' => 'info-admission'],
                            '_cdata' => true,
                            '_value' => Tools::normalizeUnicodeChars($rawData['admissionInformation'])
                        ],
                        [
                            '_key' => 'extras',
                            '_attributes' => ['info' => 'codes-modalites-admission'],
                            '_value' => array_map(fn($admissionModality) => [
                                '_key' => 'extra',
                                '_attributes' => ['info' => 'code-modalites-admission'],
                                '_value' => $admissionModality

                            ], $rawData['admissionModality'] ?? [])
                        ],
                        [
                            '_ifNotEmpty' => true,
                            '_key' => 'extra',
                            '_attributes' => ['info' => 'code-gfe'],
                            '_value' => $rawData['jobGroup']
                        ],
                        [
                            '_ifNotEmpty' => true,
                            '_key' => 'extra',
                            '_attributes' => ['info' => 'code-type-horaires'],
                            '_value' => $rawData['scheduleType']
                        ],
                        [
                            '_ifNotEmpty' => true,
                            '_key' => 'extra',
                            '_attributes' => ['info' => 'duree-apprentissage'],
                            '_value' => $rawData['averageLearningTime']
                        ],
                        [
                            '_key' => 'extras',
                            '_attributes' => ['info' => 'codes-rythme-formation'],
                            '_value' => array_map(fn($paces) => [
                                '_key' => 'extra',
                                '_attributes' => ['info' => 'code-rythme-formation'],
                                '_value' => $paces

                            ], $rawData['paces'] ?? [])
                        ],
                        [
                            '_ifNotEmpty' => true,
                            '_key' => 'extra',
                            '_attributes' => ['info' => 'frais-anpec'], // anpec means "additionnel non pris en charge"
                            '_value' => $rawData['additionalFees']
                        ],
                        [
                            '_key' => 'extra',
                            '_attributes' => ['info' => 'frais-certif-inclus-frais-anpec'],
                            '_value' => $rawData['certificationFees']
                        ],
                        [
                            '_ifNotEmpty' => true,
                            '_key' => 'extra',
                            '_attributes' => ['info' => 'detail-frais-anpec'],
                            '_cdata' => true,
                            '_value' => Tools::normalizeUnicodeChars($rawData['additionalFeesDetails'])
                        ],
                        [
                            '_ifNotEmpty' => true,
                            '_key' => 'extra',
                            '_attributes' => ['info' => 'code-modele-economique'],
                            '_value' => '' // TODO(catalogXml) find what is this data
                        ],
                        [
                            '_ifNotEmpty' => true,
                            '_key' => 'extra',
                            '_attributes' => ['info' => 'autres-services'],
                            '_cdata' => true,
                            '_value' => Tools::normalizeUnicodeChars($rawData['other'])
                        ],
                        [
                            '_if' => !empty($rawData['tvaHT0']),
                            '_key' => 'extras',
                            '_attributes' => ['info' => 'frais-pedagogiques'],
                            '_value' => [
                                [
                                    '_key' => 'extra',
                                    '_attributes' => ['info' => 'taux-tva'],
                                    '_value' => '0.0'
                                ],
                                [
                                    '_key' => 'extra',
                                    '_attributes' => ['info' => 'frais-ht'],
                                    '_value' => $rawData['tvaHT0']
                                ],
                                [
                                    '_key' => 'extra',
                                    '_attributes' => ['info' => 'frais-ttc'],
                                    '_value' => $rawData['tvaHT0']
                                ],
                            ]
                        ],
                        [
                            '_if' => !empty($rawData['tvaTTC5_5']),
                            '_key' => 'extras',
                            '_attributes' => ['info' => 'frais-pedagogiques'],
                            '_value' => [
                                [
                                    '_key' => 'extra',
                                    '_attributes' => ['info' => 'taux-tva'],
                                    '_value' => '5.5'
                                ],
                                [
                                    '_key' => 'extra',
                                    '_attributes' => ['info' => 'frais-ht'],
                                    '_value' => $rawData['tvaHT5_5']
                                ],
                                [
                                    '_key' => 'extra',
                                    '_attributes' => ['info' => 'frais-ttc'],
                                    '_value' => $rawData['tvaTTC5_5']
                                ],
                            ]
                        ],
                        [
                            '_if' => !empty($rawData['tvaTTC20']),
                            '_key' => 'extras',
                            '_attributes' => ['info' => 'frais-pedagogiques'],
                            '_value' => [
                                [
                                    '_key' => 'extra',
                                    '_attributes' => ['info' => 'taux-tva'],
                                    '_value' => '20.0'
                                ],
                                [
                                    '_key' => 'extra',
                                    '_attributes' => ['info' => 'frais-ht'],
                                    '_value' => $rawData['tvaHT20']
                                ],
                                [
                                    '_key' => 'extra',
                                    '_attributes' => ['info' => 'frais-ttc'],
                                    '_value' => $rawData['tvaTTC20']
                                ],
                            ]
                        ],
                        [
                            '_key' => 'extra',
                            '_attributes' => ['info' => 'existence-prerequis'],
                            '_value' => $rawData['haveSpecificConditionsAndPrerequisites']
                        ]
                    ]
                ]
            ]
        ];
    }

    /**
     * @param Organism $organism
     * @param Session $session
     * @param TrainingAction $trainingAction
     * @param Certification $certification
     * @param array $report
     * @return array
     */
    private function getSessionAsArray(Organism $organism, Session $session, TrainingAction $trainingAction, Certification $certification, array &$report): array
    {
        $rawData = $session->getRawData();
        if (!isset($rawData['shortId'])) {
            $this->logger->error('[CatalogXml] Error on session export, public data found instead of private ' . $session->getId());
            $this->addReportLine($report, $session, $certification, 'EXCLU', 'Les données dans Wedof ne sont pas à jour');
            return ['_if' => false];
        }

        $include = true;
        if ($session->getState() === SessionStates::ARCHIVED()->getValue()) { // Shouldn't happen
            $include = false;
            $this->addReportLine($report, $session, $certification, 'EXCLU', "La session est archivée");
        } else if ($session->getState() === SessionStates::DRAFT()->getValue()) {
            $include = false;
            $this->addReportLine($report, $session, $certification, 'EXCLU', "La session est en brouillon");
        } else if ($session->getState() !== SessionStates::PUBLISHED()->getValue()) {
            $include = false;
            $this->addReportLine($report, $session, $certification, 'EXCLU', "La session n'est pas publiée");
        }
        if ($include) {
            $this->addReportLine($report, $session, $certification);
        }

        $trainingActionRawData = $trainingAction->getRawData();
        $rawAdress = $rawData['subscriptionAddress'];
        $subscriptionContact = $rawData['subscriptionContact'];
        return [
            '_if' => $include,
            '_key' => 'session',
            '_attributes' => [
                'numero' => $rawData['shortId'],
                'datemaj' => date(self::DATE_FORMAT), // Doesn't seem to be used
                'datecrea' => $this->getFormattedDate($trainingActionRawData['creationDate']) // It is erased by the current date when importing... Seems that they inverted dates in the processing of the XML file
            ],
            '_value' => [
                'periode' => [
                    '_if' => !empty($rawData['beginDate']) || !empty($rawData['endDate']),
                    '_value' => [
                        'debut' => [
                            '_ifNoEmpty' => true,
                            '_value' => $this->getFormattedDate($rawData['beginDate'])
                        ],
                        'fin' => [
                            '_ifNoEmpty' => true,
                            '_value' => $this->getFormattedDate($rawData['endDate'])
                        ]
                    ]
                ],
                'adresse-inscription' => [
                    'adresse' => $this->getAddressFromRawData($rawAdress)
                ],
                'etat-recrutement' => $rawData['recruitmentStatusCode'],
                'extras' => [
                    '_attributes' => ['info' => 'session'],
                    '_value' => [
                        [
                            '_key' => 'extra',
                            '_attributes' => ['info' => 'contact-inscription'],
                            '_value' => [
                                'coordonnees' => $this->getCoordonneesFromRawData($subscriptionContact)
                            ]
                        ],
                        [
                            '_key' => 'extra',
                            '_attributes' => ['info' => 'garantie'],
                            '_value' => $rawData['guaranteed']
                        ],
                        [
                            '_ifNotEmpty' => true,
                            '_key' => 'extra',
                            '_attributes' => ['info' => 'modalites-particulieres'],
                            '_cdata' => true,
                            '_value' => Tools::normalizeUnicodeChars($rawData['particularities'])
                        ]
                    ]
                ]
            ]
        ];
    }

    /**
     * @param array|null $rawContact
     * @param array $address
     * @return array
     */
    private function getCoordonneesFromRawData(?array $rawContact, array $address = ['_if' => false]): ?array
    {
        if (empty($rawContact)) {
            return null;
        }
        return [
            '_attributes' => ['numero' => $rawContact['id']], // Does not matter as the contact is recognized by values
            '_value' => [
                'nom' => $rawContact['lastName'] ?? $rawContact['lastname'], // hack for old rawData
                'prenom' => $rawContact['firstName'] ?? $rawContact['firstname'], // hack for old rawData
                'adresse' => $address,
                'telfixe' => [
                    'numtel' => $this->removeWhitespaces($rawContact['homePhoneNumber']),
                ],
                'portable' => [
                    '_if' => !empty($rawContact['phoneNumber']),
                    '_value' => [
                        'numtel' => $this->removeWhitespaces($rawContact['phoneNumber'])
                    ]
                ],
                'courriel' => $rawContact['email'],
                'extras' => [
                    '_if' => !empty($rawContact['job']),
                    '_attributes' => ['info' => 'coordonnees'], // Be careful doc both says 'coordonnees' & 'coordonnee'
                    '_value' => [
                        'extra' => [
                            '_attributes' => ['info' => 'fonction'],
                            '_value' => $rawContact['job']
                        ]
                    ]
                ]
            ]
        ];
    }

    /**
     * @param array|null $rawAdress
     * @return array
     */
    private function getAddressFromRawData(?array $rawAdress): ?array
    {
        if (empty($rawAdress)) {
            return null;
        }
        $isFrance = empty($rawAdress['countryCode']) || $rawAdress['countryCode'] === 'FR';
        return [
            '_if' => isset($rawAdress['zipCode']),
            '_attributes' => ['numero' => $rawAdress['idAddress']], // Name given to the address, allow reusing the existing address
            '_value' => [
                [
                    '_key' => 'ligne',
                    '_cdata' => true,
                    '_value' => Tools::normalizeUnicodeChars($rawAdress['corporateName'])
                ],
                [
                    '_ifNotEmpty' => true,
                    '_key' => 'ligne',
                    '_cdata' => true,
                    '_value' => Tools::normalizeUnicodeChars($rawAdress['additionalAddress']) // TODO(catalogXml) should this line be included if empty but next one is not? so that the order is respected?
                ],
                [
                    '_ifNotEmpty' => true,
                    '_key' => 'ligne',
                    '_cdata' => true,
                    '_value' => Tools::normalizeUnicodeChars($rawAdress['residence'])
                ],
                'codepostal' => [
                    '_ifNotEmpty' => true,
                    '_value' => $rawAdress['zipCode'] // Here only if FR
                ],
                'ville' => [
                    '_if' => $isFrance && !empty($rawAdress['city']),
                    '_value' => $rawAdress['city']
                ],
                'pays' => [
                    '_if' => !$isFrance && !empty($rawAdress['countryCode']),
                    '_value' => $rawAdress['countryCode']
                ],
                'geolocalisation' => [
                    '_if' => false // TODO what do to with that ?
                ],
                'extras' => [
                    '_attributes' => ['info' => 'adresse'],
                    '_value' => [
                        [
                            '_ifNotEmpty' => true,
                            '_key' => 'extra',
                            '_attributes' => ['info' => 'ligne5-adresse'],
                            '_cdata' => true,
                            '_value' => Tools::normalizeUnicodeChars($isFrance ? $rawAdress['postBox'] : $rawAdress['city'])
                        ], [
                            '_ifNotEmpty' => true,
                            '_key' => 'extra',
                            '_attributes' => ['info' => 'numero-voie'],
                            '_value' => $rawAdress['number']
                        ], [
                            '_ifNotEmpty' => true,
                            '_key' => 'extra',
                            '_attributes' => ['info' => 'code-indice-repetition'],
                            '_value' => $rawAdress['repetitionIndex']
                        ], [
                            '_ifNotEmpty' => true,
                            '_key' => 'extra',
                            '_attributes' => ['info' => 'code-nature-voie'],
                            '_value' => $rawAdress['roadType']
                        ], [
                            '_ifNotEmpty' => true,
                            '_key' => 'extra',
                            '_attributes' => ['info' => 'libelle-voie'],
                            '_value' => $rawAdress['roadName']
                        ], [
                            '_ifNotEmpty' => true,
                            '_key' => 'extra',
                            '_attributes' => ['info' => 'accessibilite-batimentaire'],
                            '_cdata' => true,
                            '_value' => Tools::normalizeUnicodeChars($rawAdress['reducedMobilityAccessModalities'])
                        ], [
                            '_key' => 'extra',
                            '_attributes' => ['info' => 'conformite-reglementaire'],
                            '_value' => $rawAdress['reducedMobilityAccessCompliant']
                        ],
                    ]
                ]
            ]
        ];
    }

    /**
     * @param string|null $value
     * @return string|null
     */
    private function removeWhitespaces(?string $value): ?string
    {
        if (empty($value)) {
            return $value;
        }
        return preg_replace('/\s+/', '', $value);
    }

    /**
     * FROM d/m/y or ISO
     * TO AAAAMMJJ
     * @param string|null $date
     * @return string|null
     */
    private function getFormattedDate(?string $date): ?string
    {
        if (isset($date)) {
            $time = DateTime::createFromFormat('d/m/Y', $date) ? DateTime::createFromFormat('d/m/Y', $date)->getTimestamp() : strtotime($date);
            return date(self::DATE_FORMAT, $time);
        } else {
            return null;
        }
    }

    /**
     * @param array $report
     * @param Training|TrainingAction|Session $object
     * @param Certification $certification
     * @param string $result
     * @param string $comment
     * @return void
     */
    private function addReportLine(array &$report, $object, Certification $certification, string $result = 'OK', string $comment = '')
    {
        $trainingId = '';
        $trainingActionId = '';
        $sessionId = '';
        $objectType = '';
        $baseUrl = "https://of.moncompteformation.gouv.fr/espace-prive/html/#/catalogue";
        $rawData = $object->getRawData();
        $certificationCode = $certification->getExternalId();
        try {
            if ($object instanceof Training) {
                $objectType = 'formation';
                $trainingId = $rawData['shortId'] ?? '';
                $link = "$baseUrl/consultation-formation/$trainingId";
            } else if ($object instanceof TrainingAction) {
                $objectType = 'action';
                $trainingId = $rawData['trainingShortId'] ?? '';
                $trainingActionId = $rawData['shortId'] ?? '';
                $link = "$baseUrl/consultation-action/$trainingId/$trainingActionId";
            } else if ($object instanceof Session) {
                $objectType = 'session';
                $trainingId = $rawData['trainingShortId'] ?? explode('_', $rawData['formationNumber'], 2)[1];
                $trainingActionId = $rawData['actionShortId'] ?? explode('_', $rawData['actionNumber'], 2)[1];
                $sessionId = $rawData['shortId'] ?? explode('_', $rawData['sessionNumber'], 2)[1];
                $link = "$baseUrl/consultation-session/$trainingId/$trainingActionId/$sessionId";
            }
        } catch (Throwable $t) {
        }
        $report[] = [
            'object' => $objectType,
            'trainingId' => $trainingId,
            'trainingActionId' => $trainingActionId,
            'sessionId' => $sessionId,
            'certificationCode' => $certificationCode,
            'result' => $result,
            'comment' => $comment,
            'link' => $link
        ];
    }
}