<?php


namespace App\Service;

use App\Entity\CertificationPartnerAudit;
use App\Entity\CertificationPartnerAuditFile;
use App\Entity\User;
use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofConnectionException;
use App\Library\utils\enums\DocumentGenerationStates;
use App\Library\utils\enums\DocumentSignedStates;
use App\Library\utils\Tools;
use App\Repository\CertificationPartnerAuditFileRepository;
use App\Repository\CertificationPartnerAuditRepository;
use App\Service\DataProviders\AutomatorApiService;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use Embed\Embed;
use ErrorException;
use Exception;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Throwable;
use Vich\UploaderBundle\Handler\DownloadHandler;

class CertificationPartnerAuditFileService
{

    private CertificationPartnerAuditFileRepository $certificationPartnerAuditFileRepository;
    private DownloadHandler $downloadHandler;
    private AutomatorApiService $automatorApiService;
    private FileTypeService $fileTypeService;
    private CertificationPartnerAuditRepository $certificationPartnerAuditRepository;

    /**
     * @param CertificationPartnerAuditFileRepository $certificationPartnerAuditFileRepository
     * @param DownloadHandler $downloadHandler
     * @param AutomatorApiService $automatorApiService
     * @param FileTypeService $fileTypeService
     * @param CertificationPartnerAuditRepository $certificationPartnerAuditRepository
     */
    public function __construct(CertificationPartnerAuditFileRepository $certificationPartnerAuditFileRepository, DownloadHandler $downloadHandler, AutomatorApiService $automatorApiService, FileTypeService $fileTypeService, CertificationPartnerAuditRepository $certificationPartnerAuditRepository)
    {
        $this->downloadHandler = $downloadHandler;
        $this->certificationPartnerAuditFileRepository = $certificationPartnerAuditFileRepository;
        $this->automatorApiService = $automatorApiService;
        $this->fileTypeService = $fileTypeService;
        $this->certificationPartnerAuditRepository = $certificationPartnerAuditRepository;
    }

    /**
     * @param $file
     * @param int $typeId
     * @param CertificationPartnerAudit $certificationPartnerAudit
     * @param bool $generated
     * @param User|null $user
     * @param bool $isPartner
     * @param string|null $title
     * @return CertificationPartnerAuditFile
     */
    public function create($file, int $typeId, CertificationPartnerAudit $certificationPartnerAudit, bool $generated = false, User $user = null, bool $isPartner = false, string $title = null): CertificationPartnerAuditFile
    {
        $auditTemplate = $certificationPartnerAudit->getTemplate();
        $fileType = $auditTemplate->getAuditTemplateFileType();
        if ($isPartner) {
            throw new WedofBadRequestHttpException("Erreur, en tant que partenaire vous n'avez pas les droits pour déposer ce fichier.");
        }
        $certificationPartnerAuditFiles = $this->certificationPartnerAuditFileRepository->findBy(['typeId' => $typeId, 'certificationPartnerAudit' => $certificationPartnerAudit]);
        if ($certificationPartnerAuditFiles && empty($fileType['allowMultiple'])) {
            foreach ($certificationPartnerAuditFiles as $certificationPartnerAuditFile) {
                $this->certificationPartnerAuditFileRepository->delete($certificationPartnerAuditFile);
            }
        }
        $certificationPartnerAuditFile = new CertificationPartnerAuditFile();
        $certificationPartnerAuditFile->setTypeId($typeId);
        if (is_string($file)) {
            $certificationPartnerAuditFile->setLink($file);
            $certificationPartnerAuditFile->setFilePath($file);
            $certificationPartnerAuditFile->setFileType("link");
            try {
                $embed = new Embed();
                $title = $title ?: $embed->get($file)->title; // This tries to resolve the URL => must be publicly accessible
            } catch (Exception $exception) {
                $title = $file;
            }
            $certificationPartnerAuditFile->setFileName($title);
        } else {
            $certificationPartnerAuditFile->setFile($file);
        }
        if ($generated) {
            $certificationPartnerAuditFile->setGenerationState(DocumentGenerationStates::GENERATED()->getValue());
        }
        if (isset($fileType['allowSignPartner']) && $fileType['allowSignPartner'] === true) {
            $certificationPartnerAuditFile->setSignedState(DocumentSignedStates::NONE()->getValue());
        }
        $certificationPartnerAuditFile->setCertificationPartnerAudit($certificationPartnerAudit);
        $this->save($certificationPartnerAuditFile);
        return $certificationPartnerAuditFile;
    }

    /**
     * @param CertificationPartnerAuditFile $certificationPartnerAuditFile
     * @return array|string[]|StreamedResponse
     */
    public function download(CertificationPartnerAuditFile $certificationPartnerAuditFile)
    {
        if ($certificationPartnerAuditFile->getFileType() != 'link') {
            return $this->downloadHandler->downloadObject($certificationPartnerAuditFile, 'file');
        } else {
            //convert google drive content
            if (Tools::startsWith($certificationPartnerAuditFile->getLink(), 'https://drive.google.com/')
                || Tools::startsWith($certificationPartnerAuditFile->getLink(), 'https://docs.google.com/')) {
                $isGoogleForm = str_contains($certificationPartnerAuditFile->getLink(), 'https://docs.google.com/forms/');
                $code = explode('/view', $certificationPartnerAuditFile->getLink());
                $code = isset($code[1]) ? $code[0] : explode('/edit', $certificationPartnerAuditFile->getLink())[0];
                if (!Tools::contains($code, '/folders/')) { //case drive is a folder...
                    $extension = $isGoogleForm ? '/viewform' : '/preview';
                    $code = '<iframe src="' . $code . $extension . '" height="100%" width="100%"></iframe>';
                }
            } else {
                $embed = new Embed();
                $code = $embed->get($certificationPartnerAuditFile->getLink())->code;
            }
            if ($code) {
                $code = preg_replace('/height=[\"\'][0-9]+[\"\']/i', 'height="100%"', $code);
                $code = preg_replace('/width=[\"\'][0-9]+[\"\']/i', 'width="100%"', $code);
                return ['html' => $code];
            } else {
                return ['html' => '<div class="w-full"><a href="' . $certificationPartnerAuditFile->getLink() . '" 
                                            class="mat-focus-indicator mat-flat-button mat-button-base mat-primary" target="_blank">
                                            <span class="mat-button-wrapper">Ouvrir le document</span>
                                            <div class="mat-ripple mat-button-ripple"></div>
                                            <div class="mat-button-focus-overlay"></div>
                                        </a></div>'];
            }
        }
    }

    /**
     * @param CertificationPartnerAuditFile $certificationPartnerAuditFile
     * @throws Throwable
     */
    public function delete(CertificationPartnerAuditFile $certificationPartnerAuditFile)
    {
        $this->certificationPartnerAuditFileRepository->delete($certificationPartnerAuditFile);
    }

    /**
     * @param CertificationPartnerAudit $certificationPartnerAudit
     * @param User $user
     * @return CertificationPartnerAudit
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function convertAuditReportToPdf(CertificationPartnerAudit $certificationPartnerAudit, User $user): CertificationPartnerAudit
    {
        $editableReportFile = $certificationPartnerAudit->getReport();
        $auditTemplate = $certificationPartnerAudit->getTemplate();
        $auditTemplateFileType = $auditTemplate->getAuditTemplateFileType();

        $response = $this->automatorApiService->generatePdfFromLink($editableReportFile->getLink(), $editableReportFile->getFileName());
        if (empty($response['statusCode']) || $response['statusCode'] != 200) {
            throw new WedofBadRequestHttpException(json_encode($response['content'], true));
        }
        // delete previous report with link and replace it by the pdf report
        $certificationPartnerAudit->setReport(null);
        $this->certificationPartnerAuditRepository->save($certificationPartnerAudit);
        $this->certificationPartnerAuditFileRepository->delete($editableReportFile); // n8n has already deleted the gdoc
        $content = $response['content'];
        $pdfReportFile = $this->fileTypeService->createFileWithContent($content, $auditTemplateFileType, $certificationPartnerAudit, $this, $user);
        $certificationPartnerAudit->setReport($pdfReportFile);
        return $this->certificationPartnerAuditRepository->save($certificationPartnerAudit);
    }

    //----------------
    // METHODES PRIVES
    //----------------

    /**
     * @param CertificationPartnerAuditFile $certificationPartnerAuditFile
     */
    private function save(CertificationPartnerAuditFile $certificationPartnerAuditFile)
    {
        $this->certificationPartnerAuditFileRepository->save($certificationPartnerAuditFile);
    }
}
