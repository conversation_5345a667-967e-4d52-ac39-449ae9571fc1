<?php
// src/Service/RegistrationFolderService.php
namespace App\Service;

use App\Entity\Attendee;
use App\Entity\Connection;
use App\Entity\Organism;
use App\Entity\Payment;
use App\Entity\RegistrationFolder;
use App\Entity\RegistrationFolderHistory;
use App\Entity\RegistrationFolderReason;
use App\Entity\Session;
use App\Entity\Subscription;
use App\Entity\User;
use App\Event\RegistrationFolder\RegistrationFolderEvents;
use App\Exception\ServerException;
use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofConflictHttpException;
use App\Exception\WedofConnectionException;
use App\Exception\WedofNotFoundHttpException;
use App\Library\utils\enums\ActivityTypes;
use App\Library\utils\enums\ConnectionStates;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\enums\PaymentStates;
use App\Library\utils\enums\PaymentTypes;
use App\Library\utils\enums\RegistrationFolderAttendeeStates;
use App\Library\utils\enums\RegistrationFolderBillingStates;
use App\Library\utils\enums\RegistrationFolderControlStates;
use App\Library\utils\enums\RegistrationFolderStates;
use App\Library\utils\enums\SolicitationFundingType;
use App\Library\utils\Tools;
use App\Message\BillRegistrationFolder;
use App\Message\InTrainingRegistrationFolder;
use App\Message\ServiceDoneRegistrationFolder;
use App\Message\SynchronizePayments;
use App\Message\SynchronizeRegistrationFolder;
use App\Message\TerminateRegistrationFolder;
use App\Repository\RegistrationFolderRepository;
use App\Service\DataProviders\BaseApiService;
use DateTime;
use DateTimeZone;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\QueryBuilder;
use ErrorException;
use Exception;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\Lock\LockFactory;
use Symfony\Component\Lock\Store\FlockStore;
use Symfony\Component\Lock\Store\SemaphoreStore;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Messenger\Stamp\DelayStamp;
use Symfony\Component\Security\Core\Security;
use Throwable;

class RegistrationFolderService implements LoggerAwareInterface
{
    private LoggerInterface $logger;
    private Security $security;
    private MessageBusInterface $messageBus;
    private EntityManagerInterface $entityManager;
    private EventDispatcherInterface $dispatcher;

    private ActivityService $activityService;
    private OrganismService $organismService;
    private CertificationFolderService $certificationFolderService;
    private RegistrationFolderRepository $registrationFolderRepository;
    private RegistrationFolderReasonService $registrationFolderReasonService;
    private RegistrationFolderHistoryService $registrationFolderHistoryService;
    private CatalogService $catalogService;
    private AttendeeService $attendeeService;
    private ?LockFactory $lockFactory;

    //-------------------
    // METHODES PUBLIQUES
    //-------------------
    private CityService $cityService;

    /**
     * @param Security $security
     * @param MessageBusInterface $messageBus
     * @param EntityManagerInterface $entityManager
     * @param EventDispatcherInterface $dispatcher
     * @param ActivityService $activityService
     * @param OrganismService $organismService
     * @param CertificationFolderService $certificationFolderService
     * @param RegistrationFolderRepository $registrationFolderRepository
     * @param RegistrationFolderHistoryService $registrationFolderHistoryService
     * @param RegistrationFolderReasonService $registrationFolderReasonService
     * @param AttendeeService $attendeeService
     * @param CatalogService $catalogService
     * @param CityService $cityService
     */
    public function __construct(
        Security                         $security,
        MessageBusInterface              $messageBus,
        EntityManagerInterface           $entityManager,
        EventDispatcherInterface         $dispatcher,
        ActivityService                  $activityService,
        OrganismService                  $organismService,
        CertificationFolderService       $certificationFolderService,
        RegistrationFolderRepository     $registrationFolderRepository,
        RegistrationFolderHistoryService $registrationFolderHistoryService,
        RegistrationFolderReasonService  $registrationFolderReasonService,
        AttendeeService                  $attendeeService,
        CatalogService                   $catalogService,
        CityService                      $cityService
    )
    {
        $this->security = $security;
        $this->dispatcher = $dispatcher;
        $this->messageBus = $messageBus;
        $this->entityManager = $entityManager;
        $this->activityService = $activityService;
        $this->organismService = $organismService;
        $this->certificationFolderService = $certificationFolderService;
        $this->registrationFolderRepository = $registrationFolderRepository;
        $this->registrationFolderReasonService = $registrationFolderReasonService;
        $this->registrationFolderHistoryService = $registrationFolderHistoryService;
        $this->catalogService = $catalogService;
        $this->attendeeService = $attendeeService;
        $this->cityService = $cityService;
        if (extension_loaded("sysvsem")) {
            $store = $_ENV['LOCK_DSN'] == 'semaphore' ? new SemaphoreStore() : new FlockStore();
            $this->lockFactory = new LockFactory($store);
        } else {
            $this->lockFactory = null;
        }
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @param array $registrationFolderRawData
     * @return string|null
     * @throws Exception
     */
    public function updateNeeded(RegistrationFolder $registrationFolder, array $registrationFolderRawData): ?string
    {
        // $this->logger->debug("[" . $registrationFolderRawData['id'] . "][updateNeeded] Checking if update needed for Registration folder");

        //check state change
        $newRegistrationFolderState = $registrationFolderRawData['currentState'];
        $currentRegistrationFolderState = $registrationFolder->getState();

        //check billing change
        $currentRegistrationFolderBillingState = $registrationFolder->getBillingState();
        $newRegistrationFolderBillingState = $registrationFolderRawData['currentBillingState'] ?? $currentRegistrationFolderBillingState;

        //check attendee state change
        $currentRegistrationFolderAttendeeState = $registrationFolder->getAttendeeState();
        $newRegistrationFolderAttendeeState = $registrationFolderRawData['currentAttendeeState'] ?? $currentRegistrationFolderAttendeeState;

        $needUpdate = null;

        if ($newRegistrationFolderBillingState != $currentRegistrationFolderBillingState) {
            //no wayback trigger update, doesn't make sense (case https://cloud.icescrum.com/p/WEDOFF/#/taskBoard/214933/task/1149028)
            $orderedStates = RegistrationFolderBillingStates::valuesStates();
            if (array_search(RegistrationFolderBillingStates::from($newRegistrationFolderBillingState), $orderedStates) > array_search(RegistrationFolderBillingStates::from($currentRegistrationFolderBillingState), $orderedStates)) {
                $this->logger->debug("[" . $registrationFolderRawData['id'] . "][updateNeeded] Registration folder billing state from " . $currentRegistrationFolderBillingState . " to " . $newRegistrationFolderBillingState);
                $needUpdate = "billingState $currentRegistrationFolderBillingState -> $newRegistrationFolderBillingState";
            }
        } else if (!empty($registrationFolderRawData['trainingActionInfo']['solicitations'])) {
            //check state change from
            $rawData = $this->mergeWithSolicitationData($registrationFolderRawData);
            $newRegistrationFolderState = $rawData['currentState'];
            $i = 0;
            //check solicitation state change
            foreach ($registrationFolderRawData['trainingActionInfo']['solicitations'] as $solicitation) {
                $currentSolicitationStatus = !empty($registrationFolder->getRawData()['trainingActionInfo']['solicitations'][$i]) ? $registrationFolder->getRawData()['trainingActionInfo']['solicitations'][$i]['status'] : null;
                if ($solicitation['status'] != $currentSolicitationStatus) {
                    $this->logger->debug("[" . $registrationFolderRawData['id'] . "][updateNeeded] Registration folder solicitation state from " . $currentSolicitationStatus . " to " . $solicitation['status']);
                    $needUpdate = "solicitation $currentSolicitationStatus -> " . $solicitation['status'];
                    break;
                }
                $i++;
            }
        }

        if (!$needUpdate && $newRegistrationFolderState != $currentRegistrationFolderState) {
            $this->logger->debug("[" . $registrationFolderRawData['id'] . "][updateNeeded] Registration folder from " . $currentRegistrationFolderState . " to " . $newRegistrationFolderState);
            $needUpdate = "state $currentRegistrationFolderState -> $newRegistrationFolderState";
        }
        if (!$needUpdate && $newRegistrationFolderAttendeeState != $currentRegistrationFolderAttendeeState) {
            $this->logger->debug("[" . $registrationFolderRawData['id'] . "][updateNeeded] Registration folder attendee state changed");
            $needUpdate = "attendeeState $currentRegistrationFolderAttendeeState -> $newRegistrationFolderAttendeeState";
        }
        if (!$needUpdate) {
            $lastUpdate = $registrationFolder->getLastUpdate()->setTimeZone(new DateTimeZone('UTC'));
            $lastUpdate = $lastUpdate->setTime((int)$lastUpdate->format('G'), (int)$lastUpdate->format('i'), (int)$lastUpdate->format('s')); //remove micro seconds;
            $newLastUpdate = (new DateTime($registrationFolderRawData['changingStateDate']))->setTimeZone(new DateTimeZone('UTC'));
            $newLastUpdate = $newLastUpdate->setTime((int)$newLastUpdate->format('G'), (int)$newLastUpdate->format('i'), (int)$newLastUpdate->format('s')); //remove micro seconds;
            if ($lastUpdate < $newLastUpdate) {
                $lastUpdate = $lastUpdate->format('Y-m-d H:i:s');
                $newLastUpdate = $newLastUpdate->format('Y-m-d H:i:s');
                $this->logger->debug("[" . $registrationFolderRawData['id'] . "][updateNeeded] Registration folder lastUpdate changed $lastUpdate -> $newLastUpdate");
                $needUpdate = "lastUpdate";
            }
        }
        return $needUpdate;
    }

    /**
     * @param array $rawData
     * @param DataProviders $dataProvider
     * @param string|null $externalId
     * @param Session|null $session
     * @param Organism|null $organism
     * @param Attendee|null $attendee
     * @param Organism|null $inPartnershipWith
     * @param bool $triggerUpdate
     * @return RegistrationFolder
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws Throwable
     * @throws \Doctrine\ORM\ORMException
     */
    public function createOrUpdate(array $rawData, DataProviders $dataProvider, string $externalId = null, Session $session = null, Organism $organism = null, Attendee $attendee = null, Organism $inPartnershipWith = null, bool $triggerUpdate = false): RegistrationFolder
    {
        $apiService = BaseApiService::getRegistrationFolderApiServiceByDataProvider($dataProvider);
        $externalId = $externalId ?? $apiService->generateRegistrationFolderId($rawData, $dataProvider);
        $registrationFolder = $this->getByExternalId($externalId);
        if ($registrationFolder === null) {
            if ($session === null || $organism === null || $attendee === null) {
                throw new ErrorException("type and session and organism are required to create a new registration folder");
            }
            $registrationFolder = new RegistrationFolder();
            $registrationFolder->setType($dataProvider);
            $rawData = $apiService->preCreateRegistrationFolder($session, $rawData, $organism);
            $registrationFolder->setExternalId($externalId);
            $registrationFolder->setOrganism($organism);
            $registrationFolder->setSession($session);
            $registrationFolder->setCertification($session->getTrainingAction()->getTraining()->getCertification());
            $registrationFolder->setAttendee($attendee);
            $registrationFolder->setHistory($this->registrationFolderHistoryService->create());
            $registrationFolder->setBillingState(RegistrationFolderBillingStates::NOT_BILLABLE()->getValue());
            $registrationFolder->setAttendeeState(RegistrationFolderAttendeeStates::SERVICE_DONE_NOT_DECLARED()->getValue());
            $registrationFolder->setControlState($rawData['controlState'] ?? RegistrationFolderControlStates::NOT_IN_CONTROL()->getValue());
            $registrationFolder->copyTags($session->getTrainingAction()->getTraining());
            if ($inPartnershipWith) {
                $registrationFolder->setInPartnershipWith($inPartnershipWith);
            }
        }
        // lorsque le numéro du facture vient des paiements
        if (!empty($rawData['billNumber'])) {
            $registrationFolder->setBillNumber($rawData['billNumber']);
        }
        if (!empty($rawData['__billingRawData'])) {
            $registrationFolder->setAmountTtc($rawData['trainingActionInfo']['vatInclTax20'] + $rawData['trainingActionInfo']['vatInclTax5'] + $rawData['trainingActionInfo']['vat']);
            $registrationFolder->setAmountHt($rawData['trainingActionInfo']['vatExclTax5'] + $rawData['trainingActionInfo']['vatExclTax20'] + $rawData['trainingActionInfo']['vat']);
            $registrationFolder->setBillId($rawData['__billingRawData']['billId'] ?? null);
            $registrationFolder->setBillNumber($rawData['__billingRawData']['billNumber']);
            $registrationFolder->setAmountCGU($rawData['__billingRawData']['amountCGU'] ?? null);
            $registrationFolder->setAmountHtNet($rawData['__billingRawData']['amountHtNet'] ?? null);
            $registrationFolder->setAmountToInvoice($rawData['__billingRawData']['amountToInvoice'] ?? null);
            $vatToInvoice = (($rawData['__billingRawData']['amountToInvoice'] ?? 0) * $rawData['trainingActionInfo']['vatExclTax20'] ? 20.0 : ($rawData['trainingActionInfo']['vatInclTax5'] ? 5.5 : 0.0)) / 100;
            $registrationFolder->setAmountVatToInvoice($vatToInvoice);
            $registrationFolder->setVatAmount5($rawData['trainingActionInfo']['vatInclTax5'] - $rawData['trainingActionInfo']['vatExclTax5']);
            $registrationFolder->setVatHtAmount5($rawData['trainingActionInfo']['vatExclTax5']);
            $registrationFolder->setVatAmount20($rawData['trainingActionInfo']['vatInclTax20'] - $rawData['trainingActionInfo']['vatExclTax20']);
            $registrationFolder->setVatHtAmount20($rawData['trainingActionInfo']['vatExclTax20']);
            unset($rawData['__billingRawData']); // don't hold tmp stuff
        }
        $createProgressActivity = false;
        if (!empty($rawData['trainingActionInfo']['trainingCompletionRate'])
            && $rawData['trainingActionInfo']['trainingCompletionRate'] != $registrationFolder->getCompletionRate()) {
            $registrationFolder->setCompletionRate($rawData['trainingActionInfo']['trainingCompletionRate']);
            $createProgressActivity = true;
        }
        if (!$registrationFolder->getTerminatedReason() && !empty($rawData['trainingActionInfo']['reason'])) {
            $registrationFolder->setTerminatedReason($this->registrationFolderReasonService->getReasonByCodeTerminated($rawData['trainingActionInfo']['reason']));
        }
        if ($registrationFolder->getAbsenceDuration() === null) {
            $registrationFolder->setAbsenceDuration($registrationFolder->computeAbsenceDuration($rawData));
        }
        // gestion des rares cas où le rawData de dossiers EDOF / CPF est vide
        $registrationFolder->setRawData(!array_key_exists('id', $rawData) && $registrationFolder->getType() === DataProviders::CPF()->getValue() ? $registrationFolder->getRawData() : $rawData);
        $registrationFolder = EntityEventListener::processRegistrationFolderPostLoad($registrationFolder);
        //state or lastUpdate is the trigger
        $lastUpdate = $registrationFolder->getLastUpdate();
        $previousState = $registrationFolder->getState();
        $previousBillingState = $registrationFolder->getBillingState();
        $previousAttendeeState = $registrationFolder->getAttendeeState();
        $previousControlState = $registrationFolder->getControlState();

        if (!empty($rawData['history'])) {
            usort($rawData['history'], function ($a, $b) {
                if (!isset($a['date'])) {
                    return true;
                } else if (!isset($b['date'])) {
                    return false;
                }
                return strtotime($a['date']) < strtotime($b['date']);
            });
        }
        $lastUpdateFromHistory = isset($rawData['history'][0]['date']) ? new Datetime($rawData['history'][0]['date']) : $lastUpdate;
        if (isset($rawData['changingStateDate']) && (new Datetime($rawData['changingStateDate']) < new DateTime())) { //don't take case in futur
            $lastUpdateFromHistory = $lastUpdateFromHistory > new DateTime($rawData['changingStateDate']) ? $lastUpdateFromHistory : new DateTime($rawData['changingStateDate']);
        }

        $lastUpdateChanged = !$lastUpdate || $lastUpdateFromHistory > $lastUpdate;

        $updateBillingState = isset($rawData['currentBillingState'])
            && array_search($rawData['currentBillingState'], RegistrationFolderBillingStates::valuesStates()) > array_search($previousBillingState, RegistrationFolderBillingStates::valuesStates());


        $updateAttendeeState = isset($rawData['currentAttendeeState'])
            && array_search($rawData['currentAttendeeState'], RegistrationFolderAttendeeStates::valuesStates()) > array_search($previousAttendeeState, RegistrationFolderAttendeeStates::valuesStates());

        $updateControlState = isset($rawData['controlState']) && $rawData['controlState'] != $previousControlState;

        //case we have an empty history
        if ($registrationFolder->getHistory()->getNotProcessedDate() === null) {
            $triggerUpdate = true;
        }

        if ($rawData['currentState'] != $previousState
            || $updateAttendeeState
            || $updateBillingState
            || $updateControlState
            || $triggerUpdate
            || $lastUpdateChanged
        ) {
            $registrationFolder->setLastUpdate($lastUpdateFromHistory ?: new DateTime());
            $registrationFolder->setState($rawData['currentState'] ?? $previousState);
            if ($updateBillingState) {
                $registrationFolder->setBillingState($rawData['currentBillingState']);
            }
            if ($updateAttendeeState) {
                $registrationFolder->setAttendeeState($rawData['currentAttendeeState']);
            }
            if ($updateControlState) {
                $registrationFolder->setControlState($rawData['controlState']);
            }
            // Update missing data information with raw data value
            if (isset($rawData['missingData']) && $rawData['missingData']) {
                $registrationFolder->setMissingData($rawData['missingData']);
            }
            $created = !$this->entityManager->contains($registrationFolder);
            $this->setHistoryMapping($registrationFolder);
            $this->save($registrationFolder);
            $this->sendUpdateEvent($registrationFolder, $created);
        }
        if ($registrationFolder->getState() != $previousState) {
            $this->sendStateEvent($registrationFolder, $previousState);
            $certificationFolder = $registrationFolder->getCertificationFolder();
            if (!$certificationFolder && $registrationFolder->getCertification() && in_array($registrationFolder->getState(), RegistrationFolderStates::certifierAuthorizedStates())) {
                $certificationFolder = $this->certificationFolderService->createFromRegistrationFolder($registrationFolder);
            }
            if ($certificationFolder) {
                $this->certificationFolderService->updateFromRegistrationFolder($certificationFolder, $registrationFolder);
            }
        }
        if ($updateBillingState) {
            $this->sendBillingStateEvent($registrationFolder, $previousBillingState);
        }
        if ($updateAttendeeState) {
            $this->sendAttendeeStateEvent($registrationFolder, $previousAttendeeState);
        }
        if ($updateControlState) {
            $this->sendControlStateEvent($registrationFolder);
        }
        if ($createProgressActivity) {
            $this->createProgressActivity($registrationFolder);
        }
        return $registrationFolder;
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @param string $billNumber
     * @param float|null $vatRate
     * @return RegistrationFolder
     * @throws Throwable
     */
    public function bill(RegistrationFolder $registrationFolder, string $billNumber, float $vatRate = null): RegistrationFolder
    {
        if ($registrationFolder->getBillingState() == RegistrationFolderBillingStates::TO_BILL()->getValue()) {
            try {
                //check if billNumber already exist, it must not
                $dataProvider = DataProviders::from($registrationFolder->getType());
                $invoiceExistForFolder = $this->registrationFolderRepository->findOneBy(['billNumber' => $billNumber, 'organism' => $registrationFolder->getOrganism(), 'type' => $dataProvider]);
                if ($invoiceExistForFolder) {
                    throw new WedofConflictHttpException("Erreur, le numéro de facture : $billNumber est déjà associé au dossier de formation : {$invoiceExistForFolder->getExternalId()} sur le Data Provider $dataProvider");
                }

                $apiService = BaseApiService::getRegistrationFolderApiServiceByDataProvider(DataProviders::from($registrationFolder->getType()));
                $apiService->billRegistrationFolder($registrationFolder, $billNumber, $vatRate);
                return $registrationFolder;
            } catch (Exception $exception) {
                $this->logger->error($exception);
                if ($exception instanceof ErrorException && Tools::isNoResultException($exception)) {
                    throw new WedofNotFoundHttpException();
                } else if ($exception instanceof WedofConflictHttpException || $exception instanceof WedofConnectionException) {
                    throw new WedofBadRequestHttpException($exception->getMessage());
                } else if ($exception instanceof WedofBadRequestHttpException) {
                    throw $exception;
                }
                throw new ServerException("Erreur, un problème serveur est survenu dans la mise à jour du dossier. S'il persiste, merci de contacter nos services de support.");
            }
        } else {
            throw new WedofBadRequestHttpException("Erreur, cette opération est impossible car l'état de facturation du dossier est à l'état " . $registrationFolder->getBillingState());
        }
    }

    /**
     * @param string $ipAddress
     * @param RegistrationFolder $registrationFolder
     * @param int $delay
     * @param string $billNumber
     * @param float|null $vatRate
     * @return bool
     */
    public function billAsync(string $ipAddress, RegistrationFolder $registrationFolder, string $billNumber, int $delay = 0, float $vatRate = null): bool
    {
        if ($registrationFolder->getBillingState() == RegistrationFolderBillingStates::TO_BILL()->getValue()) {
            $message = new BillRegistrationFolder($ipAddress, $registrationFolder->getExternalId(), $billNumber, $vatRate);
            $envelope = new Envelope($message, [
                new DelayStamp($delay)
            ]);
            $this->messageBus->dispatch($envelope);
            return true;
        } else {
            return false;
        }
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @return RegistrationFolder
     * @throws Throwable
     */
    public function paid(RegistrationFolder $registrationFolder): RegistrationFolder
    {
        if (!in_array($registrationFolder->getType(), DataProviders::getInternalToString())) {
            throw new WedofBadRequestHttpException("Erreur, vous ne pouvez pas marquer comme payé un dossier de formation de type " . $registrationFolder->getDataProviderId() . ".");
        }
        if ($registrationFolder->getBillingState() == RegistrationFolderBillingStates::BILLED()->getValue()) {
            try {
                $apiService = BaseApiService::getRegistrationFolderApiServiceByDataProvider(DataProviders::from($registrationFolder->getType()));
                $registrationFolder = $apiService->paidRegistrationFolder($registrationFolder);
                return $registrationFolder;
            } catch (Exception $exception) {
                $this->logger->error($exception);
                if ($exception instanceof ErrorException && Tools::isNoResultException($exception)) {
                    throw new WedofNotFoundHttpException();
                } else if ($exception instanceof WedofConflictHttpException || $exception instanceof WedofConnectionException) {
                    throw new WedofBadRequestHttpException($exception->getMessage());
                } else if ($exception instanceof WedofBadRequestHttpException) {
                    throw $exception;
                }
                throw new ServerException("Erreur, un problème serveur est survenu dans la mise à jour du dossier. S'il persiste, merci de contacter nos services de support.");
            }
        } else {
            throw new WedofBadRequestHttpException("Erreur, cette opération est impossible car l'état de facturation du dossier est à l'état " . $registrationFolder->getBillingState());
        }
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @param array $data
     * @return RegistrationFolder
     * @throws Throwable
     */
    public function validate(RegistrationFolder $registrationFolder, array $data = []): RegistrationFolder
    {
        if ($this->isRegistrationFolderInStates($registrationFolder, [RegistrationFolderStates::NOT_PROCESSED(), RegistrationFolderStates::WAITING_ACCEPTATION()])) {
            $trainingActionInfo = $registrationFolder->getRawData()['trainingActionInfo'];
            if (is_null($trainingActionInfo['sessionStartDate'])) {
                throw new WedofBadRequestHttpException("Erreur, la date de début de session (sessionStartDate) doit être renseignée");
            }
            if (is_null($trainingActionInfo['sessionEndDate'])) {
                throw new WedofBadRequestHttpException("Erreur, la date de fin de session (sessionEndDate) doit être renseignée");
            }
            $startDate = DateTime::createFromFormat('d/m/Y', $trainingActionInfo['sessionStartDate']) ?: strtotime($trainingActionInfo['sessionStartDate']);
            $endDate = DateTime::createFromFormat('d/m/Y', $trainingActionInfo['sessionEndDate']) ?: strtotime($trainingActionInfo['sessionEndDate']);
            if ($startDate > $endDate) {
                throw new WedofBadRequestHttpException("Erreur, la date de début de session (sessionStartDate) ne peut pas être ultérieure à la date de fin de session (sessionEndDate)");
            }
            try {
                $this->logger->debug("[validate][" . $registrationFolder->getType() . "][" . $registrationFolder->getExternalId() . "] started");
                $apiService = BaseApiService::getRegistrationFolderApiServiceByDataProvider(DataProviders::from($registrationFolder->getType()));
                $registrationFolder = $apiService->validateRegistrationFolder($registrationFolder, $data);
                $this->logger->debug("[validate][" . $registrationFolder->getType() . "][" . $registrationFolder->getExternalId() . "] finished");
                return $registrationFolder;
            } catch (Exception $exception) {
                $this->logger->error($exception);
                if ($exception instanceof ErrorException && Tools::isNoResultException($exception)) {
                    throw new WedofNotFoundHttpException();
                } else if ($exception instanceof WedofConflictHttpException || $exception instanceof WedofConnectionException) {
                    throw new WedofBadRequestHttpException($exception->getMessage());
                } else if ($exception instanceof WedofBadRequestHttpException) {
                    throw $exception;
                }
                throw new ServerException("Erreur, un problème serveur est survenu dans la mise à jour du dossier. S'il persiste, merci de contacter nos services de support.");
            }
        } else {
            throw new WedofBadRequestHttpException("Erreur, cette opération est impossible car le dossier de formation est à l'état " . $registrationFolder->getState());
        }
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @return RegistrationFolder
     * @throws ServerException
     * @throws Throwable
     */
    public function reject(RegistrationFolder $registrationFolder): RegistrationFolder
    {
        if ($this->isRegistrationFolderInState($registrationFolder, RegistrationFolderStates::VALIDATED())) {
            try {
                $this->logger->debug("[reject][" . $registrationFolder->getType() . "][" . $registrationFolder->getExternalId() . "] started");
                $apiService = BaseApiService::getRegistrationFolderApiServiceByDataProvider(DataProviders::from($registrationFolder->getType()));
                $registrationFolder = $apiService->rejectRegistrationFolder($registrationFolder);
                $this->logger->debug("[reject][" . $registrationFolder->getType() . "][" . $registrationFolder->getExternalId() . "] finished");
                return $registrationFolder;
            } catch (Exception $exception) {
                $this->logger->error($exception);
                if ($exception instanceof ErrorException && Tools::isNoResultException($exception)) {
                    throw new WedofNotFoundHttpException();
                } else if ($exception instanceof WedofConflictHttpException) {
                    throw new WedofBadRequestHttpException($exception->getMessage());
                } else if ($exception instanceof WedofBadRequestHttpException) {
                    throw $exception;
                }
                throw new ServerException("Erreur, un problème serveur est survenu dans la mise à jour du dossier. S'il persiste, merci de contacter nos services de support.");
            }
        } else {
            throw new WedofBadRequestHttpException("Erreur, cette opération est impossible car le dossier de formation est à l'état " . $registrationFolder->getState());
        }
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @return RegistrationFolder
     * @throws ServerException
     * @throws Throwable
     */
    public function attendeeRefuse(RegistrationFolder $registrationFolder): RegistrationFolder
    {
        if ($this->isRegistrationFolderInState($registrationFolder, RegistrationFolderStates::VALIDATED())) {
            try {
                $this->logger->debug("[attendee-refuse][" . $registrationFolder->getType() . "][" . $registrationFolder->getExternalId() . "] started");
                $apiService = BaseApiService::getRegistrationFolderApiServiceByDataProvider(DataProviders::from($registrationFolder->getType()));
                $registrationFolder = $apiService->attendeeRefuseRegistrationFolder($registrationFolder);
                $this->logger->debug("[attendee-refuse][" . $registrationFolder->getType() . "][" . $registrationFolder->getExternalId() . "] finished");
                return $registrationFolder;
            } catch (Exception $exception) {
                $this->logger->error($exception);
                if ($exception instanceof ErrorException && Tools::isNoResultException($exception)) {
                    throw new WedofNotFoundHttpException();
                } else if ($exception instanceof WedofConflictHttpException) {
                    throw new WedofBadRequestHttpException($exception->getMessage());
                } else if ($exception instanceof WedofBadRequestHttpException) {
                    throw $exception;
                }
                throw new ServerException("Erreur, un problème serveur est survenu dans la mise à jour du dossier. S'il persiste, merci de contacter nos services de support.");
            }
        } else {
            throw new WedofBadRequestHttpException("Erreur, cette opération est impossible car le dossier de formation est à l'état " . $registrationFolder->getState());
        }
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @return RegistrationFolder
     * @throws ServerException
     * @throws Throwable
     */
    public function wait(RegistrationFolder $registrationFolder): RegistrationFolder
    {
        if ($this->isRegistrationFolderInState($registrationFolder, RegistrationFolderStates::VALIDATED())) {
            try {
                $this->logger->debug("[reject][" . $registrationFolder->getType() . "][" . $registrationFolder->getExternalId() . "] started");
                $apiService = BaseApiService::getRegistrationFolderApiServiceByDataProvider(DataProviders::from($registrationFolder->getType()));
                $registrationFolder = $apiService->waitRegistrationFolder($registrationFolder);
                $this->logger->debug("[reject][" . $registrationFolder->getType() . "][" . $registrationFolder->getExternalId() . "] finished");
                return $registrationFolder;
            } catch (Exception $exception) {
                $this->logger->error($exception);
                if ($exception instanceof ErrorException && Tools::isNoResultException($exception)) {
                    throw new WedofNotFoundHttpException();
                } else if ($exception instanceof WedofConflictHttpException) {
                    throw new WedofBadRequestHttpException($exception->getMessage());
                } else if ($exception instanceof WedofBadRequestHttpException) {
                    throw $exception;
                }
                throw new ServerException("Erreur, un problème serveur est survenu dans la mise à jour du dossier. S'il persiste, merci de contacter nos services de support.");
            }
        } else {
            throw new WedofBadRequestHttpException("Erreur, cette opération est impossible car le dossier de formation est à l'état " . $registrationFolder->getState());
        }
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @return RegistrationFolder
     * @throws Throwable
     */
    public function accept(RegistrationFolder $registrationFolder): RegistrationFolder
    {
        if ($this->isRegistrationFolderInStates($registrationFolder, [RegistrationFolderStates::VALIDATED(), RegistrationFolderStates::WAITING_ACCEPTATION()])) {
            try {
                $this->logger->debug("[accept][" . $registrationFolder->getType() . "][" . $registrationFolder->getExternalId() . "] started");
                $apiService = BaseApiService::getRegistrationFolderApiServiceByDataProvider(DataProviders::from($registrationFolder->getType()));
                $registrationFolder = $apiService->acceptRegistrationFolder($registrationFolder);
                $this->logger->debug("[accept][" . $registrationFolder->getType() . "][" . $registrationFolder->getExternalId() . "] finished");
                return $registrationFolder;
            } catch (Exception $exception) {
                $this->logger->error($exception);
                if ($exception instanceof ErrorException && Tools::isNoResultException($exception)) {
                    throw new WedofNotFoundHttpException();
                } else if ($exception instanceof WedofConflictHttpException || $exception instanceof WedofConnectionException) {
                    throw new WedofBadRequestHttpException($exception->getMessage());
                } else if ($exception instanceof WedofBadRequestHttpException) {
                    throw $exception;
                }
                throw new ServerException("Erreur, un problème serveur est survenu dans la mise à jour du dossier. S'il persiste, merci de contacter nos services de support.");
            }
        } else {
            throw new WedofBadRequestHttpException("Erreur, cette opération est impossible car le dossier de formation est à l'état " . $registrationFolder->getState());
        }
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @param array|null $body
     * @return RegistrationFolder
     * @throws Throwable
     */
    public function lockedValidate(RegistrationFolder $registrationFolder, array $body = []): RegistrationFolder
    {
        $externalId = $registrationFolder->getExternalId();
        $lock = $this->lockFactory ? $this->lockFactory->createLock($externalId) : null;
        if ($lock === null || ($lock && $lock->acquire(true))) {
            $this->logger->debug("lock acquired for registrationFolder " . $externalId . " -> remainingTime: " . ($lock ? $lock->getRemainingLifetime() : 'lock is null'));
            $registrationFolder = $this->validate($registrationFolder, $body);
            if ($lock) {
                $this->logger->debug("lock released");
                $lock->release();
            }
        }
        return $registrationFolder;
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @param DateTime|null $inTrainingDate
     * @param User|null $user
     * @return RegistrationFolder
     * @throws ServerException
     */
    public function inTraining(RegistrationFolder $registrationFolder, DateTime $inTrainingDate = null, User $user = null): RegistrationFolder
    {
        if ($this->isRegistrationFolderInState($registrationFolder, RegistrationFolderStates::ACCEPTED())) {
            try {
                $this->logger->debug("[inTraining][" . $registrationFolder->getType() . "][" . $registrationFolder->getExternalId() . "] started");
                $trainingActionInfo = $registrationFolder->getTrainingActionInfo();
                $sessionStartDate = DateTime::createFromFormat('d/m/Y', $trainingActionInfo['sessionStartDate']) ?: (new DateTime($trainingActionInfo['sessionStartDate']))->setTimezone(new DateTimeZone('Europe/Paris'));
                $nowDate = (new DateTime("now"))->setTimezone(new DateTimeZone('Europe/Paris'));
                $inTrainingDate = $inTrainingDate ?: ($nowDate > $sessionStartDate ? $nowDate : $sessionStartDate);
                $apiService = BaseApiService::getRegistrationFolderApiServiceByDataProvider(DataProviders::from($registrationFolder->getType()));
                $registrationFolder = $apiService->inTrainingRegistrationFolder($registrationFolder, $inTrainingDate, $user);
                $this->logger->debug("[inTraining][" . $registrationFolder->getType() . "][" . $registrationFolder->getExternalId() . "] finished");
                return $registrationFolder;
            } catch (Exception $exception) {
                $this->logger->error($exception);
                if ($exception instanceof ErrorException && Tools::isNoResultException($exception)) {
                    throw new WedofNotFoundHttpException();
                } else if ($exception instanceof WedofConflictHttpException || $exception instanceof WedofConnectionException) {
                    throw new WedofBadRequestHttpException($exception->getMessage());
                } else if ($exception instanceof WedofBadRequestHttpException) {
                    throw $exception;
                }
                throw new ServerException("Erreur, un problème serveur est survenu dans la mise à jour du dossier. S'il persiste, merci de contacter nos services de support.");
            }
        } else {
            throw new WedofBadRequestHttpException("Erreur, cette opération est impossible car le dossier de formation est à l'état " . $registrationFolder->getState());
        }

    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @param float|null $absenceDuration
     * @param RegistrationFolderReason|null $reason
     * @param DateTime|null $terminatedDate
     * @param User|null $user
     * @return RegistrationFolder
     * @throws ServerException
     */
    public function terminate(RegistrationFolder $registrationFolder, ?float $absenceDuration, ?RegistrationFolderReason $reason, ?DateTime $terminatedDate, User $user = null): RegistrationFolder
    {
        if ($this->isRegistrationFolderInState($registrationFolder, RegistrationFolderStates::IN_TRAINING())) {
            try {
                $this->logger->debug("[terminate][" . $registrationFolder->getType() . "][" . $registrationFolder->getExternalId() . "] started");
                $trainingActionInfo = $registrationFolder->getTrainingActionInfo();
                $sessionEndDate = DateTime::createFromFormat('d/m/Y', $trainingActionInfo['sessionEndDate']) ?: (new DateTime($trainingActionInfo['sessionEndDate']))->setTimezone(new DateTimeZone('Europe/Paris'));
                $nowDate = (new DateTime("now"))->setTimezone(new DateTimeZone('Europe/Paris'));
                $terminatedDate = $terminatedDate ?: ($nowDate < $sessionEndDate ? $nowDate : $sessionEndDate);
                if ($reason) {
                    $registrationFolder->setTerminatedReason($reason);
                }
                if ($absenceDuration !== null) {
                    $registrationFolder->setAbsenceDuration($absenceDuration);
                }
                if ($reason && $absenceDuration !== null) {
                    if ($reason->getCode() === "8" && $absenceDuration > 0) {
                        throw new WedofBadRequestHttpException("Erreur, vous ne pouvez pas déclarer le service fait avec une absence et en utilisant le code de sortie 8. Veuillez utiliser un code d'absence compatible : /api/registrationFoldersReasons?type=terminated");
                    }
                    if ($reason->getCode() !== "8" && $absenceDuration === 0.0) {
                        throw new WedofBadRequestHttpException("Erreur, le motif de sortie choisit implique une durée d'absence.");
                    }
                }
                $this->save($registrationFolder);

                $apiService = BaseApiService::getRegistrationFolderApiServiceByDataProvider(DataProviders::from($registrationFolder->getType()));
                $registrationFolder = $apiService->terminateRegistrationFolder($registrationFolder, $terminatedDate, $user);
                $this->logger->debug("[terminate][" . $registrationFolder->getType() . "][" . $registrationFolder->getExternalId() . "] finished");
                return $registrationFolder;
            } catch (Exception $exception) {
                $this->logger->error($exception);
                if ($exception instanceof ErrorException && Tools::isNoResultException($exception)) {
                    throw new WedofNotFoundHttpException();
                } else if ($exception instanceof WedofConflictHttpException || $exception instanceof WedofConnectionException) {
                    throw new WedofBadRequestHttpException($exception->getMessage());
                } else if ($exception instanceof WedofBadRequestHttpException) {
                    throw $exception;
                }
                throw new ServerException("Erreur, un problème serveur est survenu dans la mise à jour du dossier. S'il persiste, merci de contacter nos services de support.");
            }
        } else {
            throw new WedofBadRequestHttpException("Erreur, cette opération est impossible car le dossier de formation est à l'état " . $registrationFolder->getState());
        }

    }

    /**
     * @param string $ipAddress
     * @param User $user
     * @param RegistrationFolder $registrationFolder
     * @param DateTime|null $inTrainingDate
     * @param int $delay
     * @return bool
     */
    public function inTrainingAsync(string $ipAddress, User $user, RegistrationFolder $registrationFolder, DateTime $inTrainingDate = null, int $delay = 0): bool
    {
        if ($this->isRegistrationFolderInState($registrationFolder, RegistrationFolderStates::ACCEPTED())) {
            $message = new InTrainingRegistrationFolder($ipAddress, $registrationFolder->getExternalId(), $inTrainingDate, $user->getId());
            $envelope = new Envelope($message, [
                new DelayStamp($delay)
            ]);
            $this->messageBus->dispatch($envelope);
        } else {
            return false;
        }
        return true;
    }

    /**
     * @param string $ipAddress
     * @param User $user
     * @param RegistrationFolder $registrationFolder
     * @param float|null $absenceDuration
     * @param string|null $codeReason
     * @param DateTime|null $terminatedDate
     * @param int $delay
     * @return bool
     */
    public function terminateAsync(string $ipAddress, User $user, RegistrationFolder $registrationFolder, ?float $absenceDuration, ?string $codeReason, DateTime $terminatedDate = null, int $delay = 0): bool
    {
        if ($this->isRegistrationFolderInState($registrationFolder, RegistrationFolderStates::IN_TRAINING())) {
            $message = new TerminateRegistrationFolder($ipAddress, $registrationFolder->getExternalId(), $codeReason, $terminatedDate, $absenceDuration, $user->getId());
            $envelope = new Envelope($message, [
                new DelayStamp($delay)
            ]);
            $this->messageBus->dispatch($envelope);
        } else {
            return false;
        }
        return true;
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @param float|null $absenceDuration
     * @param bool $forceMajeureAbsence
     * @param RegistrationFolderReason|null $reason
     * @param DateTime|null $terminatedDate
     * @param float $trainingDuration
     * @param User|null $user
     * @return RegistrationFolder
     * @throws ServerException
     */
    public function declareServiceDone(RegistrationFolder $registrationFolder, ?float $absenceDuration, ?bool $forceMajeureAbsence, ?RegistrationFolderReason $reason, ?DateTime $terminatedDate, float $trainingDuration = 0.0, User $user = null): RegistrationFolder
    {
        $reason = $reason ?: $registrationFolder->getTerminatedReason() ?: $this->registrationFolderReasonService->getReasonByCodeTerminated("8");
        $absenceDuration = $absenceDuration !== null ? $absenceDuration : ($registrationFolder->getAbsenceDuration() ?: 0.0);
        $forceMajeureAbsence = $forceMajeureAbsence ?? $reason->getCode() === "18";
        $duration = $trainingDuration !== 0.0 ? $trainingDuration : (float)$registrationFolder->getTrainingActionInfo()["duration"];

        if ($this->isRegistrationFolderInState($registrationFolder, RegistrationFolderStates::IN_TRAINING())) {
            $registrationFolder = $this->terminate($registrationFolder, $absenceDuration, $reason, $terminatedDate, $user);
        }

        if ($this->isRegistrationFolderInState($registrationFolder, RegistrationFolderStates::TERMINATED())) {
            try {
                $this->logger->debug("[serviceDone][" . $registrationFolder->getType() . "][" . $registrationFolder->getExternalId() . "] started");
                if ($reason->getCode() === "8" && $absenceDuration > 0) {
                    throw new WedofBadRequestHttpException("Erreur, vous ne pouvez pas déclarer le service fait avec une absence et en utilisant le code de sortie 8. Veuillez utiliser un code d'absence compatible : /api/registrationFoldersReasons?type=terminated");
                }
                if ($reason->getCode() === "18" && !$forceMajeureAbsence) {
                    throw new WedofBadRequestHttpException("Erreur, le code de sortie 18 implique une absence pour force majeure.");
                } else if ($forceMajeureAbsence && $reason->getCode() != "18") {
                    throw new WedofBadRequestHttpException("Erreur, en cas d'absence pour force majeure, veuillez sélectionner le code de sortie 18.");
                }
                if ($reason->getCode() !== "8" && $absenceDuration === 0.0) {
                    throw new WedofBadRequestHttpException("Erreur, le motif de sortie choisit implique une durée d'absence.");
                }
                if ($absenceDuration !== 0.0 && $duration === 0.0) {
                    throw new WedofBadRequestHttpException("Erreur, la durée de formation (trainingDuration) doit être supérieure à 0 pour déclarer le service fait");
                }
                if (!empty($absenceDuration) && !empty($duration) && $absenceDuration > $duration) {
                    throw new WedofBadRequestHttpException("Erreur, la durée d'absence (" . $absenceDuration . ") ne peut excéder la durée de la formation : " . $duration);
                }
                // arrondi à l'inférieur pour le cas où 300 heures de formation avec 1 heure d'absence => taux de 99.6% : impossible de déclarer 100% de réalisation avec 1 heure d'absence => il faut déclarer 99%
                $completionRate = $absenceDuration ? (100 * ($duration - $absenceDuration) / $duration) : 100; //do it always in hours
                $completionRate = (int)round($completionRate); // achievement rounded example : 80,2 => 80 // 80,4 => 80 // 80,5 => 81 // 80,6 => 81
                $completionRate = $completionRate == 0 && $absenceDuration != $duration ? 1 : $completionRate; // completionRate doit être != 0 si absenceDuration != duration
                $completionRate = $absenceDuration && $completionRate === 100 ? 99 : $completionRate;// completionRate doit être != 100 si absenceDuration != duration
                $registrationFolder->setTerminatedReason($reason);
                $registrationFolder->setAbsenceDuration($absenceDuration);
                $this->save($registrationFolder);

                $apiService = BaseApiService::getRegistrationFolderApiServiceByDataProvider(DataProviders::from($registrationFolder->getType()));
                $registrationFolder = $apiService->declareServiceDoneRegistrationFolder($registrationFolder, $absenceDuration, $completionRate, $forceMajeureAbsence, $reason, $user);
                $this->logger->debug("[serviceDone][" . $registrationFolder->getType() . "][" . $registrationFolder->getExternalId() . "] finished");
                return $registrationFolder;
            } catch (Exception $exception) {
                $this->logger->error($exception);
                if ($exception instanceof ErrorException && Tools::isNoResultException($exception)) {
                    throw new WedofNotFoundHttpException();
                } else if ($exception instanceof WedofConflictHttpException || $exception instanceof WedofConnectionException) {
                    throw new WedofBadRequestHttpException($exception->getMessage());
                } else if ($exception instanceof WedofBadRequestHttpException) {
                    throw $exception;
                }
                throw new ServerException("Erreur, un problème serveur est survenu dans la mise à jour du dossier. S'il persiste, merci de contacter nos services de support.");
            }
        } else {
            throw new WedofBadRequestHttpException("Erreur, cette opération est impossible car le dossier de formation est à l'état " . $registrationFolder->getState());
        }
    }

    /**
     * @param string $ipAddress
     * @param User $user
     * @param RegistrationFolder $registrationFolder
     * @param float|null $absenceDuration
     * @param bool $forceMajeureAbsence
     * @param string|null $codeReason
     * @param DateTime|null $terminatedDate
     * @param float $trainingDuration
     * @param int $delay
     * @return bool
     */
    public function declareServiceDoneAsync(string $ipAddress, User $user, RegistrationFolder $registrationFolder, ?float $absenceDuration, ?bool $forceMajeureAbsence, ?string $codeReason, ?DateTime $terminatedDate, float $trainingDuration = 0.0, int $delay = 0): bool
    {
        if ($this->isRegistrationFolderInState($registrationFolder, RegistrationFolderStates::TERMINATED()) ||
            $this->isRegistrationFolderInState($registrationFolder, RegistrationFolderStates::IN_TRAINING())) {
            $message = new ServiceDoneRegistrationFolder($ipAddress, $registrationFolder->getExternalId(), $absenceDuration, $forceMajeureAbsence, $codeReason, $trainingDuration, $user->getId(), $terminatedDate);
            $envelope = new Envelope($message, [
                new DelayStamp($delay)
            ]);
            $this->messageBus->dispatch($envelope);
        } else {
            return false;
        }
        return true;
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @param RegistrationFolderReason $reason
     * @param string $description
     * @return RegistrationFolder
     * @throws Throwable
     */
    public function refuse(RegistrationFolder $registrationFolder, RegistrationFolderReason $reason, string $description = ""): RegistrationFolder
    {
        if ($this->isRegistrationFolderInState($registrationFolder, RegistrationFolderStates::NOT_PROCESSED())) {
            try {
                $this->logger->debug("[refuse][" . $registrationFolder->getType() . "][" . $registrationFolder->getExternalId() . "] started");

                $apiService = BaseApiService::getRegistrationFolderApiServiceByDataProvider(DataProviders::from($registrationFolder->getType()));
                $registrationFolder = $apiService->refuseRegistrationFolder($registrationFolder, $reason, $description);
                $this->logger->debug("[refuse][" . $registrationFolder->getType() . "][" . $registrationFolder->getExternalId() . "] finished");
                return $registrationFolder;
            } catch (Exception $exception) {
                $this->logger->error($exception);
                if ($exception instanceof ErrorException && Tools::isNoResultException($exception)) {
                    throw new WedofNotFoundHttpException();
                } else if ($exception instanceof WedofConflictHttpException || $exception instanceof WedofConnectionException) {
                    throw new WedofBadRequestHttpException($exception->getMessage());
                } else if ($exception instanceof WedofBadRequestHttpException) {
                    throw $exception;
                }
                throw new ServerException("Erreur, un problème serveur est survenu dans la mise à jour du dossier. S'il persiste, merci de contacter nos services de support.");
            }
        } else {
            throw new WedofBadRequestHttpException("Erreur, cette opération est impossible car le dossier de formation est à l'état " . $registrationFolder->getState());
        }
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @param RegistrationFolderReason $reason
     * @param string $description
     * @return RegistrationFolder
     * @throws Throwable
     */
    public function cancel(RegistrationFolder $registrationFolder, RegistrationFolderReason $reason, string $description = ""): RegistrationFolder
    {
        if ($this->isRegistrationFolderInStates($registrationFolder, [RegistrationFolderStates::ACCEPTED()])) {
            try {
                $this->logger->debug("[cancel][" . $registrationFolder->getType() . "][" . $registrationFolder->getExternalId() . "] started");
                $this->logger->debug("[cancel][" . $reason->getCode() . "]" . " Reason code");
                if ($reason->getCode() === '4') {
                    $trainingActionInfo = $registrationFolder->getTrainingActionInfo();
                    $sessionStartDate = DateTime::createFromFormat('d/m/Y', $trainingActionInfo['sessionStartDate']) ?: (new DateTime($trainingActionInfo['sessionStartDate']));
                    if (new DateTime("now") < $sessionStartDate) {
                        throw new WedofBadRequestHttpException("Erreur, cette opération n'est pas possible, la formation n'a pas encore commencé.");
                    }
                }

                $apiService = BaseApiService::getRegistrationFolderApiServiceByDataProvider(DataProviders::from($registrationFolder->getType()));
                $registrationFolder = $apiService->cancelRegistrationFolder($registrationFolder, $reason, $description);
                $this->logger->debug("[cancel][" . $registrationFolder->getType() . "][" . $registrationFolder->getExternalId() . "] finished");
                return $registrationFolder;
            } catch (Exception $exception) {
                $this->logger->error($exception);
                if ($exception instanceof ErrorException && Tools::isNoResultException($exception)) {
                    throw new WedofNotFoundHttpException();
                } else if ($exception instanceof WedofConflictHttpException || $exception instanceof WedofConnectionException) {
                    throw new WedofBadRequestHttpException($exception->getMessage());
                } else if ($exception instanceof WedofBadRequestHttpException) {
                    throw $exception;
                }
                throw new ServerException("Erreur, un problème serveur est survenu dans la mise à jour du dossier. S'il persiste, merci de contacter nos services de support.");
            }
        } else {
            throw new WedofBadRequestHttpException("Erreur, cette opération est impossible car le dossier de formation est à l'état " . $registrationFolder->getState());
        }
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @return RegistrationFolder
     * @throws ServerException
     */
    public function attendeeCancel(RegistrationFolder $registrationFolder): RegistrationFolder
    {
        if ($this->isRegistrationFolderInState($registrationFolder, RegistrationFolderStates::ACCEPTED())) {
            try {
                $this->logger->debug("[cancel][" . $registrationFolder->getType() . "][" . $registrationFolder->getExternalId() . "] started");

                $apiService = BaseApiService::getRegistrationFolderApiServiceByDataProvider(DataProviders::from($registrationFolder->getType()));
                $registrationFolder = $apiService->attendeeCancelRegistrationFolder($registrationFolder);
                $this->logger->debug("[cancel][" . $registrationFolder->getType() . "][" . $registrationFolder->getExternalId() . "] finished");
                return $registrationFolder;
            } catch (Exception $exception) {
                $this->logger->error($exception);
                if ($exception instanceof ErrorException && Tools::isNoResultException($exception)) {
                    throw new WedofNotFoundHttpException();
                } else if ($exception instanceof WedofConflictHttpException || $exception instanceof WedofConnectionException) {
                    throw new WedofBadRequestHttpException($exception->getMessage());
                } else if ($exception instanceof WedofBadRequestHttpException) {
                    throw $exception;
                }
                throw new ServerException("Erreur, un problème serveur est survenu dans la mise à jour du dossier. S'il persiste, merci de contacter nos services de support.");
            }
        } else {
            throw new WedofBadRequestHttpException("Erreur, cette opération est impossible car le dossier de formation est à l'état " . $registrationFolder->getState());
        }
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @param array $data
     * @param bool $saveUpdateEvent
     * @return RegistrationFolder
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ServerException
     * @throws Throwable
     */
    public function update(RegistrationFolder $registrationFolder, array $data, bool $saveUpdateEvent = true): RegistrationFolder
    {
        // Must be done before multidimensionalArrayReplace otherwise tags can be overwritten, is this a bug in multidimensionalArrayReplace?
        if (isset($data['tags'])) {
            $data['tags'] = array_map('strtolower', $data['tags']); // Required to enforce lower + avoid bug with lower / upper
            $registrationFolder->setTagsText(implode(', ', $data['tags']));
            unset($data['tags']);
        }

        $createProgressActivity = false;
        if (isset($data['completionRate'])
            && ($data['completionRate'] !== $registrationFolder->getCompletionRate() || (isset($data['completionRateDate']) && $data['completionRateDate'] != $registrationFolder->getHistory()->getCompletionRateLastUpdate()))
            && in_array($registrationFolder->getState(), [RegistrationFolderStates::ACCEPTED(), RegistrationFolderStates::IN_TRAINING(), RegistrationFolderStates::TERMINATED()])) {
            $registrationFolder->setCompletionRate($data['completionRate']);
            $registrationFolder->getHistory()->setCompletionRateLastUpdate($data['completionRateDate'] ?? new DateTime('now'));
            $createProgressActivity = true;
        } else {
            unset($data['completionRate']);
        }

        if (isset($data['controlState'])) {
            if ($registrationFolder->getType() === DataProviders::CPF()->getValue()) { // don't want override etatBlocage from edof
                unset($data['controlState']);
            } else {
                if ($data['controlState'] === RegistrationFolderControlStates::IN_CONTROL()->getValue()) {
                    $data['inControlDate'] = $data['controlStateDate'] ?? new DateTime('now');
                } else if ($data['controlState'] === RegistrationFolderControlStates::RELEASED()->getValue()) {
                    $data['releasedDate'] = $data['controlStateDate'] ?? new DateTime('now');
                }
            }
        }
        if (isset($data['controlStateDate'])) {
            unset($data['controlStateDate']);
        }

        if (isset($data['metadata'])) {
            $registrationFolder->setMetadata(empty($data['metadata']) ? null : Tools::removeSpacesInKeys($data['metadata']));
        }

        if (isset($data['trainingActionInfo']) || array_key_exists('priceChange', $data)) {
            $apiService = BaseApiService::getRegistrationFolderApiServiceByDataProvider(DataProviders::from($registrationFolder->getType()));
            $registrationFolderRawData = $apiService->refreshRegistrationFolder($registrationFolder)->getRawData();
            $isWeeklyDurationDefined = !empty($data['trainingActionInfo']['weeklyDuration']);
            $isIndicativeDurationChanged = !empty($data['trainingActionInfo']['indicativeDuration']);
            if (isset($data['priceChange'])) {
                $newPrice = ['trainingActionInfo' => []];
                if ($registrationFolderRawData['trainingActionInfo']['vatExclTax20'] > 0) {
                    if (!empty($data['priceChange']['percent'])) { // -20% or +20% on TTC price
                        $newPrice['trainingActionInfo']['vatInclTax20'] = round($registrationFolderRawData['trainingActionInfo']['vatInclTax20'] + $registrationFolderRawData['trainingActionInfo']['vatInclTax20'] * $data['priceChange']['percent'] / 100, 2);
                    } else if (!empty($data['priceChange']['price'])) { //set new price TTC
                        $newPrice['trainingActionInfo']['vatInclTax20'] = $data['priceChange']['price'];
                    } else if (!empty($data['priceChange']['amount'])) { // -100 or +100€ on TTC price
                        $newPrice['trainingActionInfo']['vatInclTax20'] = $registrationFolderRawData['trainingActionInfo']['vatInclTax20'] + $data['priceChange']['amount'];
                    } else {
                        $newPrice['trainingActionInfo']['vatInclTax20'] = $registrationFolderRawData['trainingActionInfo']['vatInclTax20'];
                    }
                    $newPrice['trainingActionInfo']['vatExclTax20'] = round($newPrice['trainingActionInfo']['vatInclTax20'] / 1.20, 2);
                    $newPrice['trainingActionInfo']['totalExcl'] = $newPrice['trainingActionInfo']['vatExclTax20'];
                    $newPrice['trainingActionInfo']['totalIncl'] = $newPrice['trainingActionInfo']['vatInclTax20'];
                    $newPrice['trainingActionInfo']['vat'] = null;
                    $newPrice['trainingActionInfo']['vatExclTax5'] = null;
                    $newPrice['trainingActionInfo']['vatInclTax5'] = null;
                } else if ($registrationFolderRawData['trainingActionInfo']['vatExclTax5'] > 0) {
                    if (!empty($data['priceChange']['percent'])) { // -20% or +20% on TTC price
                        $newPrice['trainingActionInfo']['vatInclTax5'] = round($registrationFolderRawData['trainingActionInfo']['vatInclTax5'] + $registrationFolderRawData['trainingActionInfo']['vatInclTax5'] * $data['priceChange']['percent'] / 100, 2);
                    } else if (!empty($data['priceChange']['price'])) { //set new price TTC
                        $newPrice['trainingActionInfo']['vatInclTax5'] = $data['priceChange']['price'];
                    } else if (!empty($data['priceChange']['amount'])) { // -100 or +100€ on TTC price
                        $newPrice['trainingActionInfo']['vatInclTax5'] = $registrationFolderRawData['trainingActionInfo']['vatInclTax5'] + $data['priceChange']['amount'];
                    } else {
                        $newPrice['trainingActionInfo']['vatInclTax5'] = $registrationFolderRawData['trainingActionInfo']['vatInclTax5'];
                    }
                    $newPrice['trainingActionInfo']['vatExclTax5'] = round($newPrice['trainingActionInfo']['vatInclTax5'] / 1.05, 2);
                    $newPrice['trainingActionInfo']['totalExcl'] = $newPrice['trainingActionInfo']['vatExclTax5'];
                    $newPrice['trainingActionInfo']['totalIncl'] = $newPrice['trainingActionInfo']['vatInclTax5'];
                    $newPrice['trainingActionInfo']['vat'] = null;
                    $newPrice['trainingActionInfo']['vatExclTax20'] = null;
                    $newPrice['trainingActionInfo']['vatInclTax20'] = null;
                } else if ($registrationFolderRawData['trainingActionInfo']['vat'] > 0) {
                    if (!empty($data['priceChange']['percent'])) { // -20% or +20%
                        $newPrice['trainingActionInfo']['vat'] = round($registrationFolderRawData['trainingActionInfo']['vat'] + $registrationFolderRawData['trainingActionInfo']['vat'] * $data['priceChange']['percent'] / 100, 2);
                    } else if (!empty($data['priceChange']['price'])) { //set new price TTC
                        $newPrice['trainingActionInfo']['vat'] = $data['priceChange']['price'];
                    } else if (!empty($data['priceChange']['amount'])) { // -100 or +100€
                        $newPrice['trainingActionInfo']['vat'] = $registrationFolderRawData['trainingActionInfo']['vat'] + $data['priceChange']['amount'];
                    } else {
                        $newPrice['trainingActionInfo']['vat'] = $registrationFolderRawData['trainingActionInfo']['vat'];
                    }
                    $newPrice['trainingActionInfo']['totalExcl'] = $newPrice['trainingActionInfo']['vat'];
                    $newPrice['trainingActionInfo']['totalIncl'] = $newPrice['trainingActionInfo']['vat'];
                    $newPrice['trainingActionInfo']['vatInclTax5'] = null;
                    $newPrice['trainingActionInfo']['vatExclTax5'] = null;
                    $newPrice['trainingActionInfo']['vatExclTax20'] = null;
                    $newPrice['trainingActionInfo']['vatInclTax20'] = null;
                }
                if ($registrationFolder->getType() === DataProviders::CPF()->getValue()) {
                    $trainingActionRawData = $registrationFolder->getSession()->getTrainingAction()->getRawData();
                    $initialPrice = $trainingActionRawData['totalTvaTTC'] ?? $trainingActionRawData['totalPriceTTC']; // totalTvaTTC => rawData privé du trainingAction / totalPriceTTC => rawData public du trainingAction
                    if ($newPrice['trainingActionInfo']['totalIncl'] > $initialPrice * 1.15) {
                        throw new WedofBadRequestHttpException("Erreur, le prix du dossier ne peut pas dépasser 15% plus cher que le prix défini sur la session correspondante.");
                    }
                }
                $data = Tools::multidimensionalArrayReplace($data, $newPrice);
                unset($data['priceChange']);
            }

            if (isset($data['description']) && $data['description'] != $registrationFolder->getDescription()) {
                $description = $data['description'];

                //clean old description
                str_replace($registrationFolder->getDescription() . " ", '', $registrationFolderRawData['trainingActionInfo']['trainingGoal']);
                str_replace($registrationFolder->getDescription() . " ", '', $registrationFolderRawData['trainingActionInfo']['expectedResult']);
                str_replace($registrationFolder->getDescription() . " ", '', $registrationFolderRawData['trainingActionInfo']['content']);

                //add new description
                if (strlen($registrationFolderRawData['trainingActionInfo']['trainingGoal'] . $description) <= 3000) {
                    $data['trainingActionInfo']['trainingGoal'] = $description . " " . $registrationFolderRawData['trainingActionInfo']['trainingGoal'];
                } else if (strlen($registrationFolderRawData['trainingActionInfo']['expectedResult'] . $description) <= 3000) {
                    $data['trainingActionInfo']['expectedResult'] = $description . " " . $registrationFolderRawData['trainingActionInfo']['expectedResult'];
                } else if (strlen($registrationFolderRawData['trainingActionInfo']['content'] . $description) <= 3000) {
                    $data['trainingActionInfo']['content'] = $description . " " . $registrationFolderRawData['trainingActionInfo']['content'];
                }
            }
            if (!empty($data['trainingActionInfo']['address']) && $registrationFolderRawData['trainingActionInfo']['address'] === null) {
                // MultidimensionalArrayReplace utilise tier_parse qui pète une exception si à gauche on a null et à droite un array
                // Si le dossier n'a pas d'adresse, on l'initialise à tableau vide pour permettre sa mise à jour et éviter l'exception
                // C'est un HACK ! Ca pourrait arriver sur d'autres champs... il faudrait probablement généraliser ce traitement
                $registrationFolderRawData['trainingActionInfo']['address'] = [];
            }
            $data = Tools::multidimensionalArrayReplace($registrationFolderRawData, $data);

            if ($data['trainingActionInfo']['address'] == null) {
                $data['trainingActionInfo']['address'] = ['id' => null];
            }
            if ($isIndicativeDurationChanged && !$isWeeklyDurationDefined && !empty($data['trainingActionInfo']['sessionStartDate']) && !empty($data['trainingActionInfo']['sessionEndDate'])) {
                $sessionStartDate = $data['trainingActionInfo']['sessionStartDate'] instanceof DateTime ? $data['trainingActionInfo']['sessionStartDate'] : new DateTime($data['trainingActionInfo']['sessionStartDate']);
                $sessionEndDate = $data['trainingActionInfo']['sessionEndDate'] instanceof DateTime ? $data['trainingActionInfo']['sessionEndDate'] : new DateTime($data['trainingActionInfo']['sessionEndDate']);
                $weeks = ceil($sessionEndDate->diff($sessionStartDate)->days / 7);
                $data['trainingActionInfo']['weeklyDuration'] = ceil($data['trainingActionInfo']['indicativeDuration'] / ($weeks > 0 ? $weeks : 1));
            }
            if (!empty($data['trainingActionInfo']['sessionStartDate']) && $data['trainingActionInfo']['sessionStartDate'] instanceof DateTime) {
                $data['trainingActionInfo']['sessionStartDate'] = $data['trainingActionInfo']['sessionStartDate']->format('d/m/Y');
            }
            if (!empty($data['trainingActionInfo']['sessionEndDate']) && $data['trainingActionInfo']['sessionEndDate'] instanceof DateTime) {
                $data['trainingActionInfo']['sessionEndDate'] = $data['trainingActionInfo']['sessionEndDate']->format('d/m/Y');
            }
            if (!empty($data['trainingActionInfo']['hoursInCenter']) && !empty($data['trainingActionInfo']['hoursInCompany'])) {
                $totalHoursCenterAndCompany = $data['trainingActionInfo']['hoursInCenter'] + $data['trainingActionInfo']['hoursInCompany'];
                if ($totalHoursCenterAndCompany > $data['trainingActionInfo']['indicativeDuration']) {
                    throw new WedofBadRequestHttpException("Erreur, la durée de formation en centre et en entreprise ne peut excéder la durée totale de formation.");
                }
            }
            if (!empty($data['trainingActionInfo']['hoursInCenter']) && $data['trainingActionInfo']['hoursInCenter'] > $data['trainingActionInfo']['indicativeDuration']) {
                throw new WedofBadRequestHttpException("Erreur, la durée de formation en centre ne peut excéder la durée totale de formation.");
            }
            if (!empty($data['trainingActionInfo']['hoursInCompany']) && $data['trainingActionInfo']['hoursInCompany'] > $data['trainingActionInfo']['indicativeDuration']) {
                throw new WedofBadRequestHttpException("Erreur, la durée de formation en entreprise ne peut excéder la durée totale de formation.");
            }
            try {
                $apiService = BaseApiService::getRegistrationFolderApiServiceByDataProvider(DataProviders::from($registrationFolder->getType()));
                $registrationFolder = $apiService->updateRegistrationFolder($registrationFolder, $data);
            } catch (Exception $exception) {
                $this->logger->error($exception);
                if ($exception instanceof ErrorException && Tools::isNoResultException($exception)) {
                    throw new WedofNotFoundHttpException();
                } else if ($exception instanceof WedofConflictHttpException || $exception instanceof WedofConnectionException) {
                    throw new WedofBadRequestHttpException($exception->getMessage());
                } else if ($exception instanceof WedofBadRequestHttpException) {
                    throw $exception;
                }
                throw new ServerException("Erreur, un problème serveur est survenu dans la mise à jour du dossier. S'il persiste, merci de contacter nos services de support.");
            }
        }

        if (isset($data['notes'])) {
            $registrationFolder->setNotes($data['notes']);
        }

        $registrationFolder->setDescription(isset($data['description']) && $data['description'] != "0" ? $data['description'] : null);

        if (array_key_exists('inPartnershipWith', $data)
            && in_array($registrationFolder->getState(), [RegistrationFolderStates::NOT_PROCESSED()->getValue(), RegistrationFolderStates::VALIDATED()->getValue()])
            && ($data['inPartnershipWith'] instanceof Organism || $data['inPartnershipWith'] === null)
        ) {
            $registrationFolder->setInPartnershipWith($data['inPartnershipWith']);
            $data['inPartnershipWith'] = $data['inPartnershipWith'] != null ? $data['inPartnershipWith']->getSiret() : null;
        }

        $this->save($registrationFolder);
        if ($saveUpdateEvent) {
            $this->sendUpdateEvent($registrationFolder);
        }
        if ($createProgressActivity) {
            $this->createProgressActivity($registrationFolder);
        }

        return $registrationFolder;
    }

    /**
     * @param string $externalId
     * @return bool
     */
    public function checkRegistrationFolderExistsByExternalId(string $externalId): bool
    {
        return $this->registrationFolderRepository->count(["externalId" => $externalId]) == 1;
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @param string $state
     * @return RegistrationFolder|null
     * @throws Throwable
     */
    public function lockedCheckForState(RegistrationFolder $registrationFolder, string $state): ?RegistrationFolder
    {
        $externalId = $registrationFolder->getExternalId();
        $lock = $this->lockFactory ? $this->lockFactory->createLock($externalId) : null;
        if ($lock === null || ($lock && $lock->acquire())) {
            $this->logger->debug("lock acquired for registrationFolder " . $externalId . " -> remainingTime: " . ($lock ? $lock->getRemainingLifetime() : 'lock is null'));
            $registrationFolder = $this->getByExternalId($externalId); //refresh
            $indexFolderState = array_search($registrationFolder->getState(), RegistrationFolderStates::valuesStates());
            $indexState = array_search($state, RegistrationFolderStates::valuesStates());
            if ($indexFolderState < $indexState) { //only if it is before & not equal
                $apiService = BaseApiService::getRegistrationFolderApiServiceByDataProvider(DataProviders::from($registrationFolder->getType()));
                $registrationFolder = $apiService->refreshRegistrationFolder($registrationFolder);
                $this->save($registrationFolder);
                return $registrationFolder;
            }
            if ($lock) {
                $this->logger->debug("lock released");
                $lock->release();
            }
        } else {
            $this->logger->debug("lock not acquired for registrationFolder so send then the last version " . $externalId);
        }
        return $registrationFolder;
    }

    /**
     * @param string $externalId
     * @return RegistrationFolder|null
     */
    public function getByExternalId(string $externalId): ?RegistrationFolder
    {
        //Maybe we should do getByExternal on each apiService???
        return $this->registrationFolderRepository->findOneByExternalId($externalId);
    }

    /**
     * @param Organism $organism
     * @param DataProviders $dataProvider
     * @return RegistrationFolder|null
     * @throws NonUniqueResultException
     */
    public function getLastUpdatedByOrganismAndDataProvider(Organism $organism, DataProviders $dataProvider): ?RegistrationFolder
    {
        return $this->registrationFolderRepository->findLastUpdatedByOrganismAndDataProvider($organism, $dataProvider);
    }

    /**
     * @param string $externalId
     * @return RegistrationFolder|null
     */
    public function getByExternalIdAndStateUpdatable(string $externalId): ?RegistrationFolder
    {
        return $this->registrationFolderRepository->findOneByExternalIdAndStateUpdatable($externalId);
    }

    /**
     * @param string $entityId
     * @return RegistrationFolder|null
     */
    public function getByEntityId(string $entityId): ?RegistrationFolder
    {
        return $this->getByExternalId($entityId);
    }

    /**
     * @param Organism $organism
     * @param array $states
     * @param DataProviders $type
     * @return ArrayCollection
     */
    public function listByOrganismAndStatesAndType(Organism $organism, array $states, DataProviders $type): ArrayCollection
    {
        return $this->registrationFolderRepository->findAllByOrganismAndStatesAndType($organism, $states, $type->getValue());
    }

    /**
     * @param Organism $organism
     * @param array $parameters
     * @return QueryBuilder
     */
    public function listReturnQueryBuilder(Organism $organism, array $parameters): QueryBuilder
    {
        return $this->registrationFolderRepository->findAllReturnQueryBuilder($organism, $parameters);
    }

    /**
     * @param Organism $organism
     * @param array $parameters
     * @param array $columnIds
     * @param bool $isAllowAnalytics
     * @return ArrayCollection
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function listByColumn(Organism $organism, array $parameters, array $columnIds, bool $isAllowAnalytics): ArrayCollection
    {
        return $this->registrationFolderRepository->findAllByColumn($organism, $parameters, $columnIds, $isAllowAnalytics);
    }

    /**
     * @param Organism|null $organism
     * @param array $parameters
     * @param string $columnId
     * @param bool $isAllowAnalytics
     * @return float
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function revenueByColumn(?Organism $organism, array $parameters, string $columnId, bool $isAllowAnalytics): float
    {
        // For revenue, we need to compute the parameters here even if it is also done in findAllReturnQueryBuilder
        if (in_array(RegistrationFolderStates::ALL()->getValue(), $parameters['state'])) {
            $parameters['state'] = RegistrationFolderStates::valuesStatesToString(); // Hack because of "all" fake state
        }
        if (in_array(RegistrationFolderBillingStates::ALL()->getValue(), $parameters['billingState'])) {
            $parameters['billingState'] = RegistrationFolderBillingStates::valuesStatesToString(); // Hack because of "all" fake state
        }
        $parameters = Tools::computeKanbanColumnParameters($parameters, $this->registrationFolderRepository->listColumnConfigs(), $columnId);
        $qb = $this->registrationFolderRepository->findAllReturnQueryBuilder($organism, $parameters);
        $states = $parameters['state'];
        return $this->registrationFolderRepository->getRevenueByStates($qb, $states, $isAllowAnalytics);
    }

    /**
     * @param Organism|null $organism
     * @param DataProviders $dataProvider
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countByOrganismAndDataProvider(Organism $organism, DataProviders $dataProvider): int
    {
        return $this->registrationFolderRepository->countByOrganismAndDataProvider($organism, $dataProvider);
    }

    /**
     * @param Session $session
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countBySession(Session $session): int
    {
        return $this->registrationFolderRepository->countBySessionAndStates($session);
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @return RegistrationFolder
     */
    public function save(RegistrationFolder $registrationFolder): RegistrationFolder
    {
        return $this->registrationFolderRepository->save($registrationFolder);
    }

    /**
     * @param Session $session
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countActiveAttendeesForSession(Session $session): int
    {
        return $this->registrationFolderRepository->countBySessionAndStates($session, RegistrationFolderStates::activeAttendeeStates());
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @return RegistrationFolder
     */
    public function refresh(RegistrationFolder $registrationFolder): RegistrationFolder
    {
        $apiService = BaseApiService::getRegistrationFolderApiServiceByDataProvider(DataProviders::from($registrationFolder->getType()));
        $registrationFolder = $apiService->refreshRegistrationFolder($registrationFolder);
        $this->save($registrationFolder);
        return $registrationFolder;
    }

    /**
     * @param string $ipAddress
     * @param RegistrationFolder $registrationFolder
     * @param int $delay
     * @param bool $refreshOnFinalState
     * @return bool
     */
    public function refreshAsync(string $ipAddress, RegistrationFolder $registrationFolder, int $delay = 0, bool $refreshOnFinalState = false): bool
    {
        if ($refreshOnFinalState || !in_array(RegistrationFolderStates::from($registrationFolder->getState()), RegistrationFolderStates::finalStates())) {
            $organism = $registrationFolder->getOrganism();
            $dataProvider = DataProviders::from($registrationFolder->getType());
            if ($registrationFolder->getType() === DataProviders::CPF()->getValue()) {
                $nextMessages = new ArrayCollection([new SynchronizePayments($organism->getSiret(), $dataProvider, null, null, $registrationFolder->getExternalId())]);
            }
            $message = new SynchronizeRegistrationFolder($ipAddress, $registrationFolder->getExternalId(), $organism->getSiret(), $dataProvider, $nextMessages ?? null);
            $envelope = new Envelope($message, [
                new DelayStamp($delay)
            ]);
            $this->messageBus->dispatch($envelope);
            return true;
        } else {
            return false;
        }
    }

    /**
     * @param string $externalId
     * @param string $siret
     * @param DataProviders $dataProvider
     * @return RegistrationFolder
     */
    public function retrieve(string $externalId, string $siret, DataProviders $dataProvider): RegistrationFolder
    {
        $apiService = BaseApiService::getRegistrationFolderApiServiceByDataProvider($dataProvider);
        return $apiService->retrieveRegistrationFolder($externalId, $this->organismService->getBySiret($siret));
    }

    /**
     * @param string $ipAddress
     * @param string $externalId
     * @param Organism $organism
     * @param int $delay
     * @return bool
     */
    public function retrieveAsync(string $ipAddress, string $externalId, Organism $organism, int $delay = 0): bool
    {
        /** @var Connection $connection */
        foreach ($organism->getConnections() as $connection) { // on cherche le dossier pour toutes les connexions actives de l'organisme
            if ($connection->getState() === ConnectionStates::ACTIVE()->getValue()) {
                $message = new SynchronizeRegistrationFolder($ipAddress, $externalId, $organism->getSiret(), DataProviders::from($connection->getDataProvider()));
                $envelope = new Envelope($message, [
                    new DelayStamp($delay)
                ]);
                $this->messageBus->dispatch($envelope);
            }
        }
        return true;
    }


    public function notify(RegistrationFolder $registrationFolder, $content): void
    {
        $this->logger->debug("[" . $registrationFolder->getExternalId() . "][event] RegistrationFolder sendNotification");
        //$this->dispatcher->dispatch(new WedofNotifyEvents($registrationFolder->getOrganism(), $registrationFolder, $content), "sendSmsMessage");
        $this->logger->debug("[" . $registrationFolder->getExternalId() . "][event] RegistrationFolder dispatched: sendNotification");
    }

    /**
     * @return RegistrationFolder|null
     * @throws NonUniqueResultException
     */
    public function getLastUpdatedNotProcessed(): ?RegistrationFolder
    {
        return $this->registrationFolderRepository->findLastUpdatedNotProcessedCPF();
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function initializeActivitiesFromHistory(RegistrationFolder $registrationFolder): void
    {
        $this->createActivityFromHistory($registrationFolder);
        foreach (RegistrationFolderStates::valuesStates() as $state) {
            if ($state->getValue() !== RegistrationFolderStates::NOT_PROCESSED()->getValue()) {
                $this->createUpdateStateActivityFromHistory($registrationFolder, $state);
            }
        }
        foreach ([RegistrationFolderAttendeeStates::SERVICE_DONE_DECLARED()] as $state) {
            $this->createUpdateAttendeeStateActivityFromHistory($registrationFolder, $state);
        }
        foreach ([RegistrationFolderBillingStates::BILLED(), RegistrationFolderBillingStates::PAID()] as $state) {
            $this->createUpdateBillingStateActivityFromHistory($registrationFolder, $state);
        }
        foreach ([RegistrationFolderControlStates::IN_CONTROL(), RegistrationFolderControlStates::RELEASED()] as $state) {
            $this->createUpdateControlStateActivity($registrationFolder, $state);
        }
    }

    /**
     * @param Subscription $subscription
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countForSubscription(Subscription $subscription): int
    {
        return $this->registrationFolderRepository->computeOnFieldBetweenDates(
            $subscription->getOrganism(),
            "count", 'id',
            Tools::convertDateTimeInterfaceToDateTime($subscription->getRegistrationFolderNumberPeriodStartDate()),
            Tools::convertDateTimeInterfaceToDateTime($subscription->getRegistrationFolderNumberPeriodEndDate()),
            [RegistrationFolderStates::NOT_PROCESSED()]
        );
    }

    /**
     * @param Organism $organism
     * @param DataProviders $dataProvider
     * @return array
     */
    public function listExternalIdsForOrganismAndDataProvider(Organism $organism, DataProviders $dataProvider): array
    {
        return $this->registrationFolderRepository->findAllExternalIdsForOrganismAndDataProvider($organism, $dataProvider);
    }

    /**
     * @param Attendee $attendee
     * @param Organism $organism
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countForAttendeeAndOrganism(Attendee $attendee, Organism $organism): int
    {
        return $this->registrationFolderRepository->countForAttendeeAndOrganism($attendee, $organism);
    }

    /**
     * @param Attendee $attendee
     * @param Session $session
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countForAttendeeAndSession(Attendee $attendee, Session $session): int
    {
        return $this->registrationFolderRepository->countForAttendeeAndSession($attendee, $session);
    }

    //-------------------
    // METHODES PRIVEES
    //-------------------

    /**
     * @param RegistrationFolder $registrationFolder
     * @param bool $created
     */
    private function sendUpdateEvent(RegistrationFolder $registrationFolder, bool $created = false): void
    {
        $event = $created ? RegistrationFolderEvents::CREATED : RegistrationFolderEvents::UPDATED;

        $this->logger->debug("[" . $registrationFolder->getExternalId() . "][event] RegistrationFolder $event");
        $this->dispatcher->dispatch(new RegistrationFolderEvents($registrationFolder), $event);
        $this->logger->debug("[" . $registrationFolder->getExternalId() . "][event] RegistrationFolder dispatched: $event");
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @param string|null $previousState
     */
    private function sendStateEvent(RegistrationFolder $registrationFolder, ?string $previousState): void
    {
        $states = RegistrationFolderStates::valuesStates();
        $index = $previousState ? array_search($previousState, $states) : array_search(RegistrationFolderStates::NOT_PROCESSED(), $states);
        $index2 = array_search($registrationFolder->getState(), $states);
        //events states in order
        if ($index2 > $index && ($index + 1 < $index2)) {
            if ($previousState) {
                $eventsToSend = array_slice($states, $index + 1, $index2 - $index);
            } else {
                $eventsToSend = array_slice($states, $index, ($index2 - $index) + 1);
            }
            //skip if WAITING_ACCEPTATION -> ACCEPTED or more
            if (in_array(RegistrationFolderStates::WAITING_ACCEPTATION()->getValue(), $eventsToSend) && sizeof($eventsToSend) > 1 && $eventsToSend[sizeof($eventsToSend) - 1] != RegistrationFolderStates::WAITING_ACCEPTATION()->getValue()) {
                unset($eventsToSend[array_search(RegistrationFolderStates::WAITING_ACCEPTATION()->getValue(), $eventsToSend)]);
            }
        } else {
            $eventsToSend = [$registrationFolder->getState()];
        }
        $i = 0;
        foreach ($eventsToSend as $eventToSend) {
            $event = new RegistrationFolderEvents($registrationFolder, $i);
            $eventName = "registrationFolder." . $eventToSend;
            $this->dispatcher->dispatch($event, $eventName);
            $this->logger->debug("[" . $registrationFolder->getExternalId() . "][event] RegistrationFolder event dispatched: $eventName");
            $i += 1000;
        }
    }

    /**
     * @param RegistrationFolder $registrationFolder
     */
    private function sendControlStateEvent(RegistrationFolder $registrationFolder): void
    {
        // On envoie pas d'évènement sur le notInControl qui est le statut par défaut
        if ($registrationFolder->getControlState() != RegistrationFolderControlStates::NOT_IN_CONTROL()->getValue()) {
            $event = new RegistrationFolderEvents($registrationFolder);
            $eventName = "registrationFolderControl." . $registrationFolder->getControlState();
            $this->dispatcher->dispatch($event, $eventName);
            $this->logger->debug("[" . $registrationFolder->getExternalId() . "][event] RegistrationFolder event dispatched: $eventName");
        }
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @param string|null $previousState
     */
    private function sendBillingStateEvent(RegistrationFolder $registrationFolder, ?string $previousState): void
    {
        $states = RegistrationFolderBillingStates::valuesStates();
        $index = $previousState ? array_search($previousState, $states) : array_search(RegistrationFolderBillingStates::NOT_BILLABLE(), $states);
        $index2 = array_search($registrationFolder->getBillingState(), $states);
        //events states in order
        if ($index2 > $index && ($index + 1 < $index2)) {
            if ($previousState) {
                $eventsToSend = array_slice($states, $index + 1, $index2 - $index);
            } else {
                $eventsToSend = array_slice($states, $index, ($index2 - $index) + 1);
            }
            //skip if DEPOSIT_WAIT if more than on event to send
            if (in_array(RegistrationFolderBillingStates::DEPOSIT_WAIT()->getValue(), $eventsToSend) && sizeof($eventsToSend) > 1 && $eventsToSend[sizeof($eventsToSend) - 1] != RegistrationFolderBillingStates::DEPOSIT_WAIT()->getValue()) {
                unset($eventsToSend[array_search(RegistrationFolderBillingStates::DEPOSIT_WAIT()->getValue(), $eventsToSend)]);
            }
            //skip if DEPOSIT_PAID if more than on event to send
            if (in_array(RegistrationFolderBillingStates::DEPOSIT_PAID()->getValue(), $eventsToSend) && sizeof($eventsToSend) > 1 && $eventsToSend[sizeof($eventsToSend) - 1] != RegistrationFolderBillingStates::DEPOSIT_PAID()->getValue()) {
                unset($eventsToSend[array_search(RegistrationFolderBillingStates::DEPOSIT_PAID()->getValue(), $eventsToSend)]);
            }
        } else {
            $eventsToSend = [$registrationFolder->getBillingState()];
        }

        foreach ($eventsToSend as $eventToSend) {
            $event = new RegistrationFolderEvents($registrationFolder);
            $eventName = "registrationFolderBilling." . $eventToSend;
            $this->dispatcher->dispatch($event, $eventName);
            $this->logger->debug("[" . $registrationFolder->getExternalId() . "][event] RegistrationFolder event dispatched: $eventName");
        }
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @param string|null $previousState
     */
    private function sendAttendeeStateEvent(RegistrationFolder $registrationFolder, ?string $previousState): void
    {
        $states = RegistrationFolderAttendeeStates::valuesStates();
        $index = $previousState ? array_search($previousState, $states) : array_search(RegistrationFolderAttendeeStates::SERVICE_DONE_NOT_DECLARED()->getValue(), $states);
        $index2 = array_search($registrationFolder->getAttendeeState(), $states);
        //events states in order
        if ($index2 > $index && ($index + 1 < $index2)) {
            if ($previousState) {
                $eventsToSend = array_slice($states, $index + 1, $index2 - $index);
            } else {
                $eventsToSend = array_slice($states, $index, ($index2 - $index) + 1);
            }
        } else {
            $eventsToSend = [$registrationFolder->getAttendeeState()];
        }
        foreach ($eventsToSend as $eventToSend) {
            $event = new RegistrationFolderEvents($registrationFolder);
            $eventName = "registrationFolderAttendee." . $eventToSend;
            $this->dispatcher->dispatch($event, $eventName);
            $this->logger->debug("[" . $registrationFolder->getExternalId() . "][event] RegistrationFolder event dispatched: $eventName");
        }
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @param RegistrationFolderStates $state
     * @return bool
     */
    private function isRegistrationFolderInState(RegistrationFolder $registrationFolder, RegistrationFolderStates $state): bool
    {
        return $registrationFolder->getState() === $state->getValue();
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @param RegistrationFolderStates[] $states
     * @return bool
     */
    private function isRegistrationFolderInStates(RegistrationFolder $registrationFolder, array $states): bool
    {
        return count(
                array_filter(
                    $states,
                    fn(RegistrationFolderStates $state) => $this->isRegistrationFolderInState(
                        $registrationFolder,
                        $state
                    )
                )
            ) > 0;
    }

    /**
     *  TODO REMOVE ME QUICCCCCCKKKLLLLYYY
     * @param array $rawData
     * @return mixed
     */
    private function mergeWithSolicitationData(array $rawData): array
    {
        if (!empty($rawData['trainingActionInfo']['solicitations'])) {
            foreach ($rawData['trainingActionInfo']['solicitations'] as $solicitation) {
                if ($rawData['currentState'] === RegistrationFolderStates::VALIDATED()->getValue() && $solicitation['status'] == 'inProgress' && $solicitation['fundingType'] === SolicitationFundingType::FINANCEUR()->getValue()) {
                    $rawData['currentState'] = RegistrationFolderStates::WAITING_ACCEPTATION()->getValue();
                    //TODO store fundingType
                    $this->logger->debug("[" . $rawData['id'] . "][solicitation] RegistrationFolder is in state: " . $rawData['currentState']);
                    break;
                }
            }
        }
        return $rawData;
    }

    /**
     * @param RegistrationFolder $registrationFolder
     */
    private function setHistoryMapping(RegistrationFolder $registrationFolder): void
    {
        try {
            $newStates = [];
            $newAttendeeStates = [];
            $newBillingStates = [];
            $newControlStates = [];
            $rawData = $registrationFolder->getRawData();
            $statesRawData = $rawData['states'];
            $registrationFolderHistory = $registrationFolder->getHistory();
            $states = RegistrationFolderStates::valuesStates();
            $existsActivities = $this->activityService->countByEntity($registrationFolder->getExternalId(), Tools::getClassName($registrationFolder)) != 0;
            foreach ($states as $state) {
                $stateString = $state->getValue();
                $fieldStateDateName = $stateString . 'Date';
                if (property_exists(RegistrationFolderHistory::class, $fieldStateDateName)) {
                    $currentValue = $registrationFolderHistory->{"get" . ucfirst($fieldStateDateName)}();
                    if (!$currentValue && !empty($statesRawData[$stateString]['date'])) {
                        $newStates[] = $state;
                        $dateFromRawData = Tools::createDateFromString($statesRawData[$stateString]['date']);
                        $registrationFolderHistory->{"set" . ucfirst($fieldStateDateName)}($dateFromRawData);
                    }
                }
            }
            if (!$registrationFolderHistory->getBilledDate() && in_array($registrationFolder->getBillingState(), [
                    RegistrationFolderBillingStates::BILLED()->getValue(),
                    RegistrationFolderBillingStates::PAID()->getValue()
                ])) {
                $maxDate = max(
                    $registrationFolderHistory->getCanceledByAttendeeNotRealizedDate(),
                    $registrationFolderHistory->getCanceledByOrganismDate(),
                    $registrationFolderHistory->getServiceDoneValidatedDate()
                );
                // Add 1 more second if empty or before the max
                $nextMaxDate = $maxDate ? (clone $maxDate)->modify('+1second') : null;
                $newDate = !empty($rawData['billingDate']) ? new DateTime($rawData['billingDate']) : $nextMaxDate;
                if ($newDate) {
                    $billedDate = $newDate < $maxDate ? $nextMaxDate : $newDate;
                    $registrationFolderHistory->setBilledDate($billedDate);
                    $newBillingStates[] = RegistrationFolderBillingStates::BILLED();
                }
            }
            if (!$registrationFolderHistory->getPaidDate() && !empty($rawData['paidDate'])) {
                $registrationFolderHistory->setPaidDate(new DateTime($rawData['paidDate']));
                $newBillingStates[] = RegistrationFolderBillingStates::PAID();
            }
            if (!$registrationFolderHistory->getServiceDoneDeclaredAttendeeDate() && !empty($rawData['currentAttendeeStateDate'])) {
                $registrationFolderHistory->setServiceDoneDeclaredAttendeeDate(new DateTime($rawData['currentAttendeeStateDate']));
                $newAttendeeStates[] = RegistrationFolderAttendeeStates::SERVICE_DONE_DECLARED();
            }
            if (isset($rawData['inControlDate'])) {
                $registrationFolderHistory->setInControlDate($rawData['inControlDate']);
                if ($registrationFolder->getControlState() === RegistrationFolderControlStates::IN_CONTROL()->getValue()) {
                    $newControlStates[] = RegistrationFolderControlStates::IN_CONTROL();
                }
                unset($rawData['inControlDate']); // on ne conserve pas la clé dans le rawData sauvegardé pour éviter des problèmes lors d'une prochaine mise à jour du dossier
            }
            if (isset($rawData['releasedDate'])) {
                // pour les dossiers CPF pas de valorisation de la releaseDate alors si le dossier est nouveau = pas d'activité => inControlDate + 1min / si a
                $rawData['releasedDate'] = $registrationFolder->getType() === DataProviders::CPF()->getValue() ? ($existsActivities ? new DateTime('now') : $rawData['releasedDate']) : $rawData['releasedDate'];
                $registrationFolderHistory->setReleasedDate($rawData['releasedDate']);
                unset($rawData['releasedDate']); // on ne conserve pas la clé dans le rawData sauvegardé pour éviter des problèmes lors d'une prochaine mise à jour du dossier
                if ($registrationFolder->getControlState() === RegistrationFolderControlStates::RELEASED()->getValue()) {
                    $newControlStates[] = RegistrationFolderControlStates::RELEASED();
                }
            }
            $registrationFolder->setRawData($rawData);

            if (!$existsActivities) {
                $this->initializeActivitiesFromHistory($registrationFolder);
            } else {
                foreach ($newStates as $state) {
                    $this->createUpdateStateActivityFromHistory($registrationFolder, $state);
                }
                foreach ($newBillingStates as $state) {
                    $this->createUpdateBillingStateActivityFromHistory($registrationFolder, $state);
                }
                foreach ($newAttendeeStates as $state) {
                    $this->createUpdateAttendeeStateActivityFromHistory($registrationFolder, $state);
                }
                foreach ($newControlStates as $state) {
                    $this->createUpdateControlStateActivity($registrationFolder, $state);
                }
            }
        } catch (Throwable $t) {
            $this->logger->error("Erreur initialisation des activités du RF " . $registrationFolder->getExternalId() . " " . $t->getMessage());
        }
    }

    /**
     * @throws OptimisticLockException
     * @throws ServerException
     * @throws Throwable
     * @throws ORMException
     */
    public function updateBillingStateAndDates(RegistrationFolder $registrationFolder, ?Payment $payment): void
    {
        $update = false;
        $rawData = $registrationFolder->getRawData();
        if ($payment->getType() === PaymentTypes::BILL()->getValue()) {
            if (!$registrationFolder->getBillNumber()) {
                $update = true;
                $rawData['billNumber'] = $payment->getBillNumber();
            }
            if ($payment->getState() === PaymentStates::WAITING()->getValue() && !$registrationFolder->getHistory()->getBilledDate()) {
                $update = true;
                $rawData['currentBillingState'] = RegistrationFolderBillingStates::BILLED()->getValue();
                $rawData['billingDate'] = $payment->getRawData()['billingDate'];
            } else if ($payment->getState() === PaymentStates::ISSUED()->getValue() && !$registrationFolder->getHistory()->getPaidDate()) {
                $update = true;
                $rawData['currentBillingState'] = RegistrationFolderBillingStates::PAID()->getValue();
                $rawData['billingDate'] = $payment->getRawData()['billingDate']; //sometimes it is empty fo we fix it here
                $rawData['paidDate'] = $payment->getScheduledDate() ? $payment->getScheduledDate()->format('d-m-Y') : null;
            }
        } else if ($payment->getType() == PaymentTypes::DEPOSIT()->getValue()) {
            $billingState = $payment->getState() == PaymentStates::ISSUED() ? RegistrationFolderBillingStates::DEPOSIT_PAID() : RegistrationFolderBillingStates::DEPOSIT_WAIT();
            if ($registrationFolder->getBillingState() != $billingState->getValue()) {
                $update = true;
                $rawData['currentBillingState'] = $billingState->getValue();
            }
        }
        if ($update) {
            $this->createOrUpdate($rawData, DataProviders::from($registrationFolder->getType()), $registrationFolder->getExternalId(), null, null, null, null, true);
        }
    }

    /**
     * @param DataProviders $dataProvider
     * @param array $registrationFolderRawData
     * @return RegistrationFolder
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     * @throws \Doctrine\ORM\ORMException
     */
    public function createFromRawData(DataProviders $dataProvider, array $registrationFolderRawData): RegistrationFolder
    {
        $organism = $this->organismService->getBySiret($registrationFolderRawData["siret"]);
        $apiService = BaseApiService::getRegistrationFolderApiServiceByDataProvider($dataProvider);

        $this->logger->info("[$dataProvider][" . $organism->getName() . "][" . $registrationFolderRawData['id'] . "] create new");
        $registrationFolderRawData = $apiService->getRegistrationFolderRawData($organism, $registrationFolderRawData['id'], $registrationFolderRawData, $registrationFolderRawData['currentBillingState']);

        $session = $this->catalogService->createSessionFromRegistrationFolderRawData($dataProvider, $organism, $registrationFolderRawData);

        if (!$registrationFolderRawData['attendee']['email']) {
            $registrationFolderRawData['attendee']['email'] = Tools::cleanString($registrationFolderRawData['attendee']["displayName"]) . '@deleted.fr';
        }
        $attendee = $this->attendeeService->createOrUpdate($this->prepareAttendeeRawData($registrationFolderRawData['attendee']));
        return $this->createOrUpdate($registrationFolderRawData, $dataProvider, null, $session, $organism, $attendee);
    }


    /**
     * @param DataProviders $dataProvider
     * @param array $registrationFolderRawData
     * @param bool $forceRefresh
     * @return RegistrationFolder|null
     * @throws ErrorException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws Throwable
     * @throws \Doctrine\ORM\ORMException
     */
    public function updateFromRawData(DataProviders $dataProvider, array $registrationFolderRawData, bool $forceRefresh = false): ?RegistrationFolder
    {
        $organism = $this->organismService->getBySiret($registrationFolderRawData["siret"]);
        $apiService = BaseApiService::getRegistrationFolderApiServiceByDataProvider($dataProvider);
        $externalId = $apiService->generateRegistrationFolderId($registrationFolderRawData, $dataProvider);

        $registrationFolder = $forceRefresh ? $this->getByExternalId($externalId) : $this->getByExternalIdAndStateUpdatable($externalId);
        if ($registrationFolder != null) {
            if (str_contains($registrationFolder->getAttendee()->getEmail(), '@deleted.fr') && $registrationFolderRawData['attendee']['email']) {
                $attendee = $this->attendeeService->getByRawData($registrationFolderRawData['attendee']);
                if ($attendee) {
                    $registrationFolder->setAttendee($attendee);
                    $this->save($registrationFolder);
                } else {
                    $this->attendeeService->createOrUpdate($this->prepareAttendeeRawData($registrationFolderRawData['attendee']), $registrationFolder->getAttendee(), true);
                }
                $this->logger->debug("[$dataProvider][" . $organism->getName() . "][" . $registrationFolderRawData['id'] . "] update attendee");
            }
            $needUpdate = $this->updateNeeded($registrationFolder, $registrationFolderRawData);
            if ($needUpdate != null || $forceRefresh) {
                $this->logger->debug("[$dataProvider][" . $organism->getName() . "][" . $registrationFolderRawData['id'] . "] update: $needUpdate");
                // si title != null alors on a déjà les infos complètes du dossier et il n'est pas nécessaire de les redemander
                $registrationFolderRawData = $registrationFolderRawData['title'] === null ? $apiService->getRegistrationFolderRawData($organism, $registrationFolderRawData['id'], $registrationFolderRawData, $registrationFolderRawData['currentBillingState']) : $registrationFolderRawData;
                if ((!$registrationFolder->getAttendee()->getDateOfBirth() && !empty($registrationFolderRawData['attendee']['birthDate'])) ||
                    ((!$registrationFolder->getAttendee()->getCodeCityOfBirth() || !$registrationFolder->getAttendee()->getCodeCountryOfBirth()) && !empty($registrationFolderRawData['attendee']['birthPlace']))
                ) {
                    $this->attendeeService->createOrUpdate($this->prepareAttendeeRawData($registrationFolderRawData['attendee']), $registrationFolder->getAttendee());
                    $this->logger->debug("[$dataProvider][" . $organism->getName() . "][" . $registrationFolderRawData['id'] . "] update attendee birthname / birthplace");
                }
                return $this->createOrUpdate($registrationFolderRawData, $dataProvider, $externalId);
            }
            /*else {
                $this->logger->debug("[$dataProvider][" . $organism->getName() . "][" . $registrationFolderRawData['id'] . "] update not needed");
            }*/
        } else {
            $this->logger->debug("[$dataProvider][" . $organism->getName() . "][" . $registrationFolderRawData['id'] . "] in a final state");
        }
        return null;
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @param User $user
     * @return array
     */
    public function getActions(RegistrationFolder $registrationFolder, User $user): array
    {
        $organism = $user->getMainOrganism();
        $subscription = $organism->getSubscription();
        // Manual checks instead of using isGranted or accessService because we want :
        // - to check the subscription (not in accessService) exists and isAllowRegistrationFolders
        // - not to check the connection (would potentially alter the connexion, e.g. trigger a refresh, which is not desirable in a read only situation)
        // also removing the menu when the connexion is down may be confusing ?
        if ($organism === $registrationFolder->getOrganism() && !empty($subscription) && $subscription->isAllowRegistrationFolders()) {
            $apiService = BaseApiService::getRegistrationFolderApiServiceByDataProvider(DataProviders::from($registrationFolder->getType()));
            $rules = $apiService->getActionsRulesForRegistrationFolder();
            $rulesToApply = $rules[$registrationFolder->getState()] ?? [];
            return $this->checkActionsRules($registrationFolder, $rulesToApply);
        } else {
            // Return empty array here rather than in controller ensures that everyone using the service will have proper permission checks based on user
            // Also, returning 403 in controller is bad because it breaks the front when user can view the folder but not edit it
            return [];
        }
    }

    //----------------
    // METHODES PRIVES
    //----------------

    /**
     * @param RegistrationFolder $registrationFolder
     * @param array $nextStates
     * @return array
     */
    private function checkActionsRules(RegistrationFolder $registrationFolder, array $nextStates): array
    {
        $registrationFolderFileTypes = $registrationFolder->getOrganism()->getRegistrationFolderFileTypes($registrationFolder);
        $nextStatesConditions = [];
        foreach ($nextStates as $nextState => $conditionTypes) {
            $tooltip = null;
            $nextStateConditions = [];
            //always check for required documents
            $conditionTypes[] = 'hasRequiredDocuments';
            $allowAction = true;
            foreach ($conditionTypes as $conditionType) {
                switch ($conditionType) {
                    case 'hasRequiredDocuments':
                        $missingFileTypes = Tools::getMissingFileTypesForState($registrationFolderFileTypes, $registrationFolder->getFiles(), $nextState);
                        $hasRequiredDocuments = count($missingFileTypes) === 0;
                        if (!$hasRequiredDocuments) {
                            $tooltip = "Attention, des documents requis à l'état " . RegistrationFolderStates::toFrString($nextState) . " n'ont pas été déposés.";
                        }
                        break;
                    case "billable":
                        if ($registrationFolder->getBillingState() === RegistrationFolderBillingStates::BILLED()->getValue()) {
                            $tooltip = "Le dossier a déjà été facturé, voici l'état actuel de la facturation du dossier : " . RegistrationFolderBillingStates::toFrString($registrationFolder->getBillingState());
                            $allowAction = false;
                        } else if ($registrationFolder->getBillingState() !== RegistrationFolderBillingStates::TO_BILL()->getValue()) {
                            $tooltip = "La facturation n'est pas possible, voici l'état actuel de la facturation du dossier : " . RegistrationFolderBillingStates::toFrString($registrationFolder->getBillingState());
                            $allowAction = false;
                        }
                        break;
                    case "isBilledState":
                        if (!in_array($registrationFolder->getType(), DataProviders::getInternalToString())) {
                            $tooltip = "Marquer comme payé n'est autorisé que pour les dossiers hors CPF";
                            $allowAction = false;
                        } else if ($registrationFolder->getBillingState() !== RegistrationFolderBillingStates::BILLED()->getValue()) {
                            $tooltip = "La facturation n'est pas possible, voici l'état actuel de la facturation du dossier : " . RegistrationFolderBillingStates::toFrString($registrationFolder->getBillingState());
                            $allowAction = false;
                        }
                        break;
                }
                $nextStateConditions[] = ['type' => $conditionType, 'allowAction' => $allowAction, 'tooltip' => $tooltip];
            }
            $nextStatesConditions[$nextState] = $nextStateConditions;
        }
        return $nextStatesConditions;
    }


    /**
     * @param RegistrationFolder $registrationFolder
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    private function createActivityFromHistory(RegistrationFolder $registrationFolder): void
    {
        /** @var User $user */
        $user = $this->security->getUser();
        $stateDatesFromRawDataHistory = $this->getStateDatesFromRawDataHistory($registrationFolder);
        // Fallback to rfh, which may be less precise, if this is the only thing we have
        $notProcessedDate = $stateDatesFromRawDataHistory[RegistrationFolderStates::NOT_PROCESSED()->getValue()] ?? $registrationFolder->getHistory()->getNotProcessedDate();
        if ($notProcessedDate) {
            $this->activityService->create([
                'title' => 'Le dossier de formation a été créé',
                'description' => null,
                'type' => ActivityTypes::CREATE(),
                'eventTime' => $notProcessedDate,
            ], $user, $registrationFolder, false);
        }
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @param RegistrationFolderStates $state
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    private function createUpdateStateActivityFromHistory(RegistrationFolder $registrationFolder, RegistrationFolderStates $state): void
    {
        /** @var User $user */
        $user = $this->security->getUser();
        $stateString = $state->getValue();
        $stateDatesFromRawDataHistory = $this->getStateDatesFromRawDataHistory($registrationFolder);
        if (!empty($stateDatesFromRawDataHistory[$stateString])) {
            $stateChangeDate = $stateDatesFromRawDataHistory[$stateString];
        } else {
            // Fallback to rfh, which may be less precise, if this is the only thing we have
            $history = $registrationFolder->getHistory();
            $fieldStateDateName = $stateString . 'Date';
            if (property_exists(RegistrationFolderHistory::class, $fieldStateDateName)) {
                $stateChangeDate = $history->{"get" . ucfirst($fieldStateDateName)}();
            }
        }
        if (!empty($stateChangeDate)) {
            $this->activityService->create([
                'title' => "Le dossier de formation est passé à l'état " . RegistrationFolderStates::toFrString($stateString),
                'description' => $this->getStateLabelFromRawDataHistory($registrationFolder, $state),
                'type' => ActivityTypes::UPDATE_STATE(),
                'eventTime' => $stateChangeDate,
                'field' => 'state',
                'newValue' => $stateString
            ], $user, $registrationFolder, false);
        }
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    private function createProgressActivity(RegistrationFolder $registrationFolder): void
    {
        /** @var User $user */
        $user = $this->security->getUser();
        $this->activityService->create([
            'title' => "Le taux de réalisation est passé à " . $registrationFolder->getCompletionRate() . "%",
            'type' => ActivityTypes::PROGRESS(),
            'eventTime' => new DateTime(),
            'field' => 'completionRate',
            'newValue' => $registrationFolder->getCompletionRate()
        ], $user, $registrationFolder, false);

    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @param RegistrationFolderAttendeeStates $state
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    private function createUpdateAttendeeStateActivityFromHistory(RegistrationFolder $registrationFolder, RegistrationFolderAttendeeStates $state): void
    {
        /** @var User $user */
        $user = $this->security->getUser();
        $history = $registrationFolder->getHistory();
        $stateString = $state->getValue();
        if ($stateString === RegistrationFolderAttendeeStates::SERVICE_DONE_DECLARED()->getValue() && $history->getServiceDoneDeclaredAttendeeDate()) {
            $this->activityService->create([
                'title' => "L'apprenant a déclaré son service fait",
                'type' => ActivityTypes::UPDATE_STATE(),
                'eventTime' => $history->getServiceDoneDeclaredAttendeeDate(),
                'field' => 'attendeeState',
                'newValue' => $stateString
            ], $user, $registrationFolder, false);
        }
    }


    /**
     * @param RegistrationFolder $registrationFolder
     * @param RegistrationFolderBillingStates $state
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    private function createUpdateBillingStateActivityFromHistory(RegistrationFolder $registrationFolder, RegistrationFolderBillingStates $state): void
    {
        /** @var User $user */
        $user = $this->security->getUser();
        $history = $registrationFolder->getHistory();
        $stateString = $state->getValue();
        if ($stateString === RegistrationFolderBillingStates::BILLED()->getValue() && $history->getBilledDate()) {
            $this->activityService->create([
                'title' => "Le dossier de formation a été facturé",
                'type' => ActivityTypes::UPDATE_STATE(),
                'eventTime' => $history->getBilledDate(),
                'field' => 'billingState',
                'newValue' => $stateString
            ], $user, $registrationFolder, false);
        }
        if ($stateString === RegistrationFolderBillingStates::PAID()->getValue() && $history->getPaidDate()) {
            $this->activityService->create([
                'title' => "Le dossier de formation a été payé",
                'type' => ActivityTypes::UPDATE_STATE(),
                'eventTime' => $history->getPaidDate(),
                'field' => 'billingState',
                'newValue' => $stateString
            ], $user, $registrationFolder, false);
        }
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @param RegistrationFolderControlStates $state
     * @return void
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    private function createUpdateControlStateActivity(RegistrationFolder $registrationFolder, RegistrationFolderControlStates $state): void
    {
        /** @var User $user */
        $user = $this->security->getUser();
        $history = $registrationFolder->getHistory();
        if ($state->equals(RegistrationFolderControlStates::IN_CONTROL()) && $history->getInControlDate()) {
            $this->activityService->create([
                'title' => 'Le dossier de formation est en contrôle par le financeur',
                'type' => ActivityTypes::UPDATE_STATE(),
                'eventTime' => $history->getInControlDate(),
                'field' => 'controlState',
                'newValue' => $state->getValue()
            ], $user, $registrationFolder, false);
        }
        if ($state->equals(RegistrationFolderControlStates::RELEASED()) && $history->getReleasedDate()) {
            $this->activityService->create([
                'title' => 'Le dossier de formation a été contrôlé par le financeur',
                'type' => ActivityTypes::UPDATE_STATE(),
                'eventTime' => $history->getReleasedDate(),
                'field' => 'controlState',
                'newValue' => $state->getValue()
            ], $user, $registrationFolder, false);
        }
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @return array
     * @throws Exception
     */
    private function getStateDatesFromRawDataHistory(RegistrationFolder $registrationFolder): array
    {
        $historyDateByState = [];
        $rawData = $registrationFolder->getRawData();
        if (!empty($rawData['history'])) {
            foreach ($rawData['history'] as $historyEntry) {
                if (isset($historyEntry['label']) && !empty($historyEntry['date'])) {
                    $label = $historyEntry['label'];
                    $date = new DateTime($historyEntry['date']);
                    $author = $historyEntry['author'] ?? null;
                    if ($label === "Création du dossier") { // ça aurait pu être "envoi de la demande d'inscription" mais ça ne marche pas pour les dossiers hors cpf
                        $historyDateByState[RegistrationFolderStates::NOT_PROCESSED()->getValue()] = $date;
                    } else if ($label === "Proposition de l'organisme") {
                        $historyDateByState[RegistrationFolderStates::VALIDATED()->getValue()] = $date;
                    } else if ($label === 'Mise à disposition abondement PE') {
                        $historyDateByState[RegistrationFolderStates::WAITING_ACCEPTATION()->getValue()] = $date;
                    } else if ($label === 'Validation de la proposition') {
                        $historyDateByState[RegistrationFolderStates::ACCEPTED()->getValue()] = $date;
                    } else if ($label === 'Déclaration Entrée en Formation') {
                        $historyDateByState[RegistrationFolderStates::IN_TRAINING()->getValue()] = $date;
                    } else if ($label === 'Déclaration Sortie de formation') {
                        $historyDateByState[RegistrationFolderStates::TERMINATED()->getValue()] = $date;
                    } else if ($label === 'Déclaration service fait OF') { // ancienne façon de faire de la CDC
                        $historyDateByState[RegistrationFolderStates::SERVICE_DONE_DECLARED()->getValue()] = $date;
                    } else if ($label === 'Déclaration service fait' && $author === 'Organisme') { // nouvelle façon de faire de la CDC
                        $historyDateByState[RegistrationFolderStates::SERVICE_DONE_DECLARED()->getValue()] = $date;
                    } else if ($label === 'Service fait validé') {
                        $historyDateByState[RegistrationFolderStates::SERVICE_DONE_VALIDATED()->getValue()] = $date;
                    } else if ($label === 'Refus de la proposition') {
                        $historyDateByState[RegistrationFolderStates::REFUSED_BY_ATTENDEE()->getValue()] = $date;
                    } else if ($label === "Refus de la demande d'inscription") {
                        $historyDateByState[RegistrationFolderStates::REFUSED_BY_ORGANISM()->getValue()] = $date;
                    } else if ($label === 'Annulation du dossier' && $author) {
                        if ($historyEntry['author'] === 'Titulaire') {
                            $currentState = $rawData['currentState'];
                            if ($currentState === RegistrationFolderStates::CANCELED_BY_ATTENDEE()->getValue()) {
                                $historyDateByState[RegistrationFolderStates::CANCELED_BY_ATTENDEE()->getValue()] = $date;
                            } else if ($currentState === RegistrationFolderStates::CANCELED_BY_ATTENDEE_NOT_REALIZED()->getValue()) {
                                $historyDateByState[RegistrationFolderStates::CANCELED_BY_ATTENDEE_NOT_REALIZED()->getValue()] = $date;
                            }
                        } else if ($historyEntry['author'] === 'Organisme') {
                            $historyDateByState[RegistrationFolderStates::CANCELED_BY_ORGANISM()->getValue()] = $date;
                        } else if ($historyEntry['author'] === 'CDC') {
                            $historyDateByState[RegistrationFolderStates::REJECTED_WITHOUT_TITULAIRE_SUITE()->getValue()] = $date;
                        }
                    }
                }
            }
        }
        return $historyDateByState;
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @param RegistrationFolderStates $state
     * @return string|null
     */
    private function getStateLabelFromRawDataHistory(RegistrationFolder $registrationFolder, RegistrationFolderStates $state): ?string
    {
        $rawData = $registrationFolder->getRawData();
        if (!empty($rawData['history'])) {
            $found = array_filter($rawData['history'], function ($history) use ($state) {
                return isset($history['state']) && $history['state'] === $state->getValue();
            });
            return $found[0]['label'] ?? null;
        }
        return null;
    }

    /**
     * @param array $attendeeRawData
     * @return array
     */
    private function prepareAttendeeRawData(array $attendeeRawData): array
    {
        if (isset($attendeeRawData['birthDate'])) {
            $attendeeRawData['dateOfBirth'] = (DateTime::createFromFormat('d/m/Y', $attendeeRawData['birthDate']))->setTimezone(new \DateTimeZone('Europe/Paris'));
        }

        if (isset($attendeeRawData['birthPlace'])) {

            $city = $this->cityService->getByName($attendeeRawData['birthPlace']);

            if ($city) {
                $attendeeRawData['codeCityOfBirth'] = $city->getCog();
                $attendeeRawData['nameCityOfBirth'] = $city->getName();
            } else {
                $country = Tools::findCountry(null, $attendeeRawData['birthPlace']);
                if ($country) {
                    $attendeeRawData['codeCountryOfBirth'] = $country['cog'];
                    $attendeeRawData['nameCountryOfBirth'] = $country['name'];
                }
            }
        }

        return $attendeeRawData;
    }
}
