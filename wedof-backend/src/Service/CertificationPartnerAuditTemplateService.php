<?php
// src/Service/CertificationPartnerAuditTemplateService.php
namespace App\Service;

use App\Entity\Certification;
use App\Entity\CertificationPartnerAuditTemplate;
use App\Entity\Organism;
use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofConnectionException;
use App\Library\utils\enums\CertificationTypes;
use App\Repository\CertificationPartnerAuditTemplateRepository;
use App\Service\DataProviders\AutomatorApiService;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\QueryBuilder;
use ErrorException;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Throwable;

class CertificationPartnerAuditTemplateService
{

    private FileTypeService $fileTypeService;
    private AutomatorApiService $automatorApiService;
    private CertificationPartnerAuditTemplateRepository $certificationPartnerAuditTemplateRepository;

    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    public function __construct(CertificationPartnerAuditTemplateRepository $certificationPartnerAuditTemplateRepository, AutomatorApiService $automatorApiService, FileTypeService $fileTypeService)
    {
        $this->certificationPartnerAuditTemplateRepository = $certificationPartnerAuditTemplateRepository;
        $this->automatorApiService = $automatorApiService;
        $this->fileTypeService = $fileTypeService;
    }

    /**
     * @param int $id
     * @return CertificationPartnerAuditTemplate|null
     */
    public function getById(int $id): ?CertificationPartnerAuditTemplate
    {
        return $this->certificationPartnerAuditTemplateRepository->findOneBy(['id' => $id]);
    }

    /**
     * @param array $body
     * @param Certification $certification
     * @param Organism $organism
     * @param string|null $googleIdToDuplicate
     * @return array|CertificationPartnerAuditTemplate
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function create(array $body, Certification $certification, Organism $organism, string $googleIdToDuplicate = null)
    {
        $certificationPartnerAuditTemplate = new CertificationPartnerAuditTemplate();
        $certificationPartnerAuditTemplate->setCertification($certification);
        $certificationPartnerAuditTemplate->setCertifier($organism);
        $certificationPartnerAuditTemplate->setName($body['name']);
        $certificationPartnerAuditTemplate->setAllowVisibilityPartner($body['allowVisibilityPartner']);
        if (!empty($body['criterias'])) {
            $certificationPartnerAuditTemplate->setCriterias($body['criterias']);
        }
        $this->certificationPartnerAuditTemplateRepository->save($certificationPartnerAuditTemplate);
        // save auditTemplate first to get entity for generating templateFile
        $templateFileType = $this->getFileType($certificationPartnerAuditTemplate, $body, $organism, $googleIdToDuplicate);
        $certificationPartnerAuditTemplate->setAuditTemplateFileType($templateFileType);
        $certificationPartnerAuditTemplate = $this->certificationPartnerAuditTemplateRepository->save($certificationPartnerAuditTemplate);
        if ($googleIdToDuplicate) {
            return [
                'id' => $templateFileType['googleId'],
                'criterias' => $certificationPartnerAuditTemplate->getCriterias(),
            ];
        } else {
            return $certificationPartnerAuditTemplate;
        }
    }


    /**
     * @param CertificationPartnerAuditTemplate $certificationPartnerAuditTemplate
     * @param array $body
     * @return CertificationPartnerAuditTemplate
     * @throws Throwable
     */
    public function update(CertificationPartnerAuditTemplate $certificationPartnerAuditTemplate, array $body): CertificationPartnerAuditTemplate
    {
        $properties = ['name', 'allowVisibilityPartner', 'criterias'];

        if ($certificationPartnerAuditTemplate->getName() !== $body['name'] ||
            $certificationPartnerAuditTemplate->isAllowVisibilityPartner() !== $body['allowVisibilityPartner']) {
            $auditTemplateFileType = $certificationPartnerAuditTemplate->getAuditTemplateFileType();
            foreach (['name', 'allowVisibilityPartner'] as $auditTemplateFileTypeProperty) {
                if (key_exists($auditTemplateFileTypeProperty, $body)) {
                    $auditTemplateFileType[$auditTemplateFileTypeProperty] = $body[$auditTemplateFileTypeProperty];
                }
            }
            $certificationPartnerAuditTemplate->setAuditTemplateFileType($auditTemplateFileType);
        }

        foreach ($properties as $property) {
            if (key_exists($property, $body)) {
                $setMethodName = "set" . ucwords($property);
                $certificationPartnerAuditTemplate->{$setMethodName}($body[$property]);
            }
        }
        return $this->certificationPartnerAuditTemplateRepository->save($certificationPartnerAuditTemplate);
    }

    /**
     * @param Certification|null $certification
     * @param Organism $certifier
     * @return QueryBuilder
     */
    public function listReturnQueryBuilder(Organism $certifier, Certification $certification = null): QueryBuilder
    {
        return $this->certificationPartnerAuditTemplateRepository->findAllReturnQueryBuilder($certifier, $certification);
    }

    /**
     * @param CertificationPartnerAuditTemplate $certificationPartnerAuditTemplate
     * @return void
     * @throws Throwable
     */
    public function delete(CertificationPartnerAuditTemplate $certificationPartnerAuditTemplate)
    {
        // TODO(audit) check all the things
        $googleIdAuditFileType = $certificationPartnerAuditTemplate->getAuditTemplateFileType()['googleId'];
        $response = $this->automatorApiService->deleteDocumentTemplate('template-document-delete', $googleIdAuditFileType);
        if (empty($response['statusCode']) || $response['statusCode'] != 200) {
            throw new WedofBadRequestHttpException(json_decode($response['content'], true));
        }
        $this->certificationPartnerAuditTemplateRepository->delete($certificationPartnerAuditTemplate);
    }

    /**
     * @param Certification $certification
     * @return array[]
     */
    public function listCriterias(Certification $certification): array
    {
        return array_values(array_filter($this->getCriterias(), function ($criteria) use ($certification) {
            if ((isset($criteria['certificationTypes']) && !in_array($certification->getType(), $criteria['certificationTypes'])) ||
                (isset($criteria['certificationOptions']) && !$certification->{$criteria['certificationOptions']}())) {
                return !$criteria;
            } else {
                return $criteria;
            }
        }));
    }

    //-------------------
    // METHODES PRIVEES
    //-------------------

    /**
     * @param $entity
     * @param array $body
     * @param Organism $organism
     * @param string|null $googleIdToDuplicate
     * @return array
     * @throws Throwable
     * @throws WedofConnectionException
     * @throws ORMException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws OptimisticLockException
     * @throws ErrorException
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    private function getFileType($entity, array $body, Organism $organism, string $googleIdToDuplicate = null): array
    {
        $fileName = $organism->getSiret() . '_' . $entity::CLASSNAME . '_' . 'auditTemplateFileTypes' . '_' . $entity->getId() . '_' . 1;
        if ($googleIdToDuplicate) {
            $response = $this->fileTypeService->duplicateFromTemplateGoogleId($googleIdToDuplicate, $fileName);
        } else {
            $response = $this->fileTypeService->createOrUpdateTemplate(null, $body['googleId'], $fileName);
            $response["googleId"] = $body['googleId'];
        }
        return [
            'id' => 1,
            'name' => $body['name'],
            'generated' => true,
            'allowRegenerate' => true,
            'enabled' => true,
            'description' => null,
            'allowMultiple' => true,
            'allowVisibilityPartner' => $body['allowVisibilityPartner'],
            'allowUploadPartner' => false,
            'allowSignPartner' => false,
            'toState' => null,
            'templateFile' => $fileName,
            'googleId' => $response['googleId'],
            'targetFileType' => 'link',
            'accept' => ".docx,.doc,.pdf"
        ];
    }

    /**
     * @return array[]
     */
    private function getCriterias(): array
    {
        return [
            // Criteres évalués par IA
            [
                'key' => 'trainingsTitleAI',
                'type' => 'custom',
                'category' => 'aiEvaluated',
                'certificationTypes' => [CertificationTypes::RNCP()->getValue(), CertificationTypes::RS()->getValue()]
            ],
            // Connexions et partage de données
            [
                'key' => 'wedofConnexionCpf',
                'type' => 'boolean',
                'category' => 'wedofConnectionAndAccess',
                'operationValues' => [
                    'isTrue' => 'active',
                    'isFalse' => 'inactive'
                ]
            ], /*[
                'key' => 'wedofConnexionKairos',
                'type' => 'boolean',
                'category' => 'wedofConnectionAndAccess',
                'operationValues' => [
                    'isTrue' => 'active',
                    'isFalse' => 'inactive'
                ]
            ],*/ [
                'key' => 'wedofAccess',
                'type' => 'boolean',
                'category' => 'wedofConnectionAndAccess',
                'operationValues' => [
                    'isTrue' => 'actif',
                    'isFalse' => 'inactif'
                ]
            ],
            // Qualiopi
            [
                'key' => 'qualiopiVAE',
                'type' => 'boolean',
                'category' => 'qualiopi',
                'operationValues' => [
                    'isTrue' => 'active',
                    'isFalse' => 'inactive'
                ]
            ],
            [
                'key' => 'qualiopiTrainingAction',
                'type' => 'boolean',
                'category' => 'qualiopi',
                'operationValues' => [
                    'isTrue' => 'active',
                    'isFalse' => 'inactive'
                ]
            ],
            [
                'key' => 'qualiopiBilanCompetences',
                'type' => 'boolean',
                'category' => 'qualiopi',
                'operationValues' => [
                    'isTrue' => 'active',
                    'isFalse' => 'inactive'
                ]
            ],
            [
                'key' => 'qualiopiFormationApprentissage',
                'type' => 'boolean',
                'category' => 'qualiopi',
                'operationValues' => [
                    'isTrue' => 'active',
                    'isFalse' => 'inactive'
                ]
            ],
            // Formations
            [
                'key' => 'trainingsCount',
                'type' => 'number',
                'category' => 'trainings',
                'certificationTypes' => [CertificationTypes::RNCP()->getValue(), CertificationTypes::RS()->getValue()]
            ], [
                'key' => 'trainingActionsCount',
                'type' => 'number',
                'category' => 'trainings',
                'certificationTypes' => [CertificationTypes::RNCP()->getValue(), CertificationTypes::RS()->getValue()]
            ], [
                'key' => 'trainingsTitle',
                'type' => 'string',
                'category' => 'trainings',
                'certificationTypes' => [CertificationTypes::RNCP()->getValue(), CertificationTypes::RS()->getValue()]
            ], [
                'key' => 'trainingsGoal',
                'type' => 'string',
                'category' => 'trainings',
                'certificationTypes' => [CertificationTypes::RNCP()->getValue(), CertificationTypes::RS()->getValue()]
            ], [
                'key' => 'trainingsContent',
                'type' => 'string',
                'category' => 'trainings',
                'certificationTypes' => [CertificationTypes::RNCP()->getValue(), CertificationTypes::RS()->getValue()]
            ], [
                'key' => 'trainingsContentSummary',
                'type' => 'string',
                'category' => 'trainings',
                'certificationTypes' => [CertificationTypes::RNCP()->getValue(), CertificationTypes::RS()->getValue()]
            ], [
                'key' => 'trainingsExpectedResults',
                'type' => 'string',
                'category' => 'trainings',
                'certificationTypes' => [CertificationTypes::RNCP()->getValue(), CertificationTypes::RS()->getValue()]
            ], [
                'key' => 'trainingsPrerequisites',
                'type' => 'string',
                'category' => 'trainings',
                'certificationTypes' => [CertificationTypes::RNCP()->getValue(), CertificationTypes::RS()->getValue()]
            ], [
                'key' => 'trainingsCompliance',
                'type' => 'boolean',
                'category' => 'trainings',
                'operationValues' => [
                    'isTrue' => 'conforme',
                    'isFalse' => 'non-conforme'
                ],
                'certificationTypes' => [CertificationTypes::RNCP()->getValue(), CertificationTypes::RS()->getValue()]
            ], [
                'key' => 'trainingsPricingMin',
                'type' => 'number',
                'category' => 'trainings',
                'certificationTypes' => [CertificationTypes::RNCP()->getValue(), CertificationTypes::RS()->getValue()]
            ], [
                'key' => 'trainingsPricingMax',
                'type' => 'number',
                'category' => 'trainings',
                'certificationTypes' => [CertificationTypes::RNCP()->getValue(), CertificationTypes::RS()->getValue()]
            ], [
                'key' => 'trainingsPricingAverage',
                'type' => 'number',
                'category' => 'trainings',
                'certificationTypes' => [CertificationTypes::RNCP()->getValue(), CertificationTypes::RS()->getValue()]
            ], [
                'key' => 'trainingsDurationMin',
                'type' => 'number',
                'category' => 'trainings',
                'certificationTypes' => [CertificationTypes::RNCP()->getValue(), CertificationTypes::RS()->getValue()]
            ], [
                'key' => 'trainingsDurationMax',
                'type' => 'number',
                'category' => 'trainings',
                'certificationTypes' => [CertificationTypes::RNCP()->getValue(), CertificationTypes::RS()->getValue()]
            ], [
                'key' => 'trainingsDurationAverage',
                'type' => 'number',
                'category' => 'trainings',
                'certificationTypes' => [CertificationTypes::RNCP()->getValue(), CertificationTypes::RS()->getValue()]
            ], [
                'key' => 'trainingsAverageRating',
                'type' => 'number',
                'category' => 'trainings',
                'certificationTypes' => [CertificationTypes::RNCP()->getValue(), CertificationTypes::RS()->getValue()]
            ], [
                'key' => 'trainingsTeachingMethod',
                'type' => 'select',
                'category' => 'trainings',
                'certificationTypes' => [CertificationTypes::RNCP()->getValue(), CertificationTypes::RS()->getValue()],
                'operationValues' => [
                    'distanciel' => 'Distanciel',
                    'presentiel' => 'Présentiel',
                    'mixte' => 'Mixte'
                ],
            ], [
                'key' => 'trainingsZone',
                'type' => 'selectArray',
                'category' => 'trainings',
                'certificationTypes' => [CertificationTypes::RNCP()->getValue(), CertificationTypes::RS()->getValue()],
                'operationValues' => [
                    'containsArray' => 'conforme à la zone géographique prédéfinie dans le partenariat',
                    'notContainsArray' => 'non conforme à la zone géographique prédéfinie dans le partenariat'
                ],
            ],
            // Examens de certification
            [
                'key' => 'takeRate',
                'type' => 'number',
                'category' => 'certificationFolders'
            ], [
                'key' => 'takeRate1Month',
                'type' => 'number',
                'category' => 'certificationFolders'
            ], [
                'key' => 'takeRate3Months',
                'type' => 'number',
                'category' => 'certificationFolders'
            ], [
                'key' => 'takeRate6Months',
                'type' => 'number',
                'category' => 'certificationFolders'
            ], [
                'key' => 'successRate',
                'type' => 'number',
                'category' => 'certificationFolders'
            ],
            [
                'key' => 'skillSets',
                'type' => 'selectArray',
                'category' => 'certificationFolders',
                'certificationTypes' => [CertificationTypes::RNCP()->getValue()],
                'certificationOptions' => 'isAllowPartialSkillSets',
                'operationValues' => [
                    'containsArray' => 'conforme aux blocs de compétences prédéfinis dans le partenariat',
                    'notContainsArray' => 'non conforme aux blocs de compétences prédéfinis dans le partenariat'
                ],
            ],
            // Taux de réponse aux questionnaires
            [
                'key' => 'initialExperienceAnsweredRate',
                'type' => 'number',
                'category' => 'certificationFolderSurveys'
            ], [
                'key' => 'sixMonthExperienceAnsweredRate',
                'type' => 'number',
                'category' => 'certificationFolderSurveys'
            ], [
                'key' => 'longTermExperienceAnsweredRate',
                'type' => 'number',
                'category' => 'certificationFolderSurveys'
            ],
            // Taux d'abandon
            [
                'key' => 'abortRate',
                'type' => 'number',
                'category' => 'abortCertificationFolders'
            ], [
                'key' => 'abortRateBeforeTraining',
                'type' => 'number',
                'category' => 'abortCertificationFolders'
            ], [
                'key' => 'abortRateInTraining',
                'type' => 'number',
                'category' => 'abortCertificationFolders'
            ], [
                'key' => 'abortRateAfterTraining',
                'type' => 'number',
                'category' => 'abortCertificationFolders'
            ],
        ];
    }
}
