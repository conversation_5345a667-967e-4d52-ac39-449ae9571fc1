<?php

namespace App\Service;

use DateTime;
use Exception;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpClient\CurlHttpClient;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;

//--> good compressor for function call : https://javascriptcompressor.com/ don't forget to double \ the regex

class BrowserlessService implements LoggerAwareInterface
{
    private LoggerInterface $logger;
    private string $projectDir;

    const AGENTS = [
        '*************:3011',
        '**************:3012',
        '**************:3013',
        '**************:3014',
        '*************:3015',
        '*************:3016',
        '************:3018'
    ];

    public function __construct(string $projectDir)
    {
        $this->projectDir = $projectDir;
    }

    /**
     * @param LoggerInterface $logger
     */
    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    /**
     * @param string $url
     * @param array $options
     * @param string|null $agent
     * @return array|false|null
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    public function makeScreenshot(string $url, array $options = [], string $agent = null): array
    {
        $options['url'] = $url;
        return $this->request("screenshot", $options, $agent);
    }

    /**
     * @param string $function
     * @param array $context
     * @param string|null $agent
     * @return array|false|null
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    public function callFunction(string $function, array $context, string $agent = null)
    {
        $data = array("code" => $function, "context" => $context);
        return $this->request("function", $data, $agent);
    }

    /**
     * @param string $action
     * @param array $data
     * @param string|null $agent
     * @return false|mixed|null
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    private function request(string $action, array $data, string $agent = null)
    {
        $agent = $agent ?? $this->selectAgent();
        $url = "$agent";
        $this->logger->info($url);
        $client = new CurlHttpClient();
        $array = [
            'json' => $data,
            'headers' => [
                'Cache-Control' => 'no-cache',
                'Content-Type' => 'application/json'
            ]
        ];
        try {
            $response = $client->request('POST', "http://$url/" . $action . "?token=" . $_ENV["BROWSERLESS_TOKEN"] . "&stealth", $array);
            $response = $response->getContent();
            $response = json_decode($response, true);
            if ($response) {
                if (isset($response['error'])) {
                    $response['trace'] = "\n - Erreur : " . ($response['error']);
                    $response['trace'] .= "\n - Agent: http://$agent";
                    $response['trace'] .= "\n - Trace : " . ($response['steps'] ?? "");
                    if (isset($response['screenshot'])) {
                        $filename = (new DateTime())->format('d-m-Y-H-m-s') . ' - ' . $response['screenshotName'];
                        $filepath = $this->saveScreenshot($response['screenshot'], $filename);
                        $response['trace'] .= "\n - Screenshot: scp root@" . gethostname() . ":" . $response['screenshot'] . " . ";
                        $response['screenshot'] = $filepath;
                    }
                }
                return $response;
            } else {
                return null;
            }
        } catch (Exception $e) {
            $this->logger->error($e->getMessage());
            return false;
        }
    }

    /**
     * @param $screenshot
     * @param $filename
     * @return string
     */
    private function saveScreenshot($screenshot, $filename): string
    {
        $dir = $this->projectDir . '/data/screenshots/';
        if (!file_exists($dir)) {
            mkdir($dir);
        }
        list($type, $data) = explode(';', $screenshot);
        list(, $screenshot) = explode(',', $screenshot);
        $screenshot = base64_decode($screenshot);
        file_put_contents($dir . $filename . '.png', $screenshot);
        return $dir . $filename . '.png';
    }

    /**
     * @return string
     */
    private function selectAgent(): string
    {
        return self::AGENTS[array_rand(self::AGENTS)];
    }
}