<?php

namespace App\Service;

use App\Entity\Attendee;
use App\Entity\RegistrationFolder;
use App\Entity\RegistrationFolderFile;
use App\Entity\User;
use App\Event\RegistrationFolderFile\RegistrationFolderFileEvents;
use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofConnectionException;
use App\Library\utils\enums\ActivityTypes;
use App\Library\utils\enums\DocumentGenerationStates;
use App\Library\utils\enums\DocumentSignedStates;
use App\Library\utils\enums\FileStates;
use App\Library\utils\enums\RegistrationFolderStates;
use App\Library\utils\Tools;
use App\Repository\RegistrationFolderFileRepository;
use App\Service\DataProviders\DocusealApiService;
use DateTime;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use Embed\Embed;
use ErrorException;
use Exception;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Throwable;
use Vich\UploaderBundle\Handler\DownloadHandler;

class RegistrationFolderFileService implements LoggerAwareInterface
{
    private RegistrationFolderFileRepository $registrationFolderFileRepository;
    private DownloadHandler $downloadHandler;
    private ActivityService $activityService;
    private LoggerInterface $logger;
    private EventDispatcherInterface $dispatcher;
    private DocusealApiService $docusealApiService;

    /**
     * RegistrationFolderFileService constructor.
     * @param ActivityService $activityService
     * @param RegistrationFolderFileRepository $registrationFolderFileRepository
     * @param DownloadHandler $downloadHandler
     * @param EventDispatcherInterface $dispatcher
     * @param DocusealApiService $docusealApiService
     */
    public function __construct(ActivityService $activityService, RegistrationFolderFileRepository $registrationFolderFileRepository, DownloadHandler $downloadHandler, EventDispatcherInterface $dispatcher, DocusealApiService $docusealApiService)
    {
        $this->dispatcher = $dispatcher;
        $this->downloadHandler = $downloadHandler;
        $this->registrationFolderFileRepository = $registrationFolderFileRepository;
        $this->activityService = $activityService;
        $this->docusealApiService = $docusealApiService;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    //----------------
    // METHODES PUBLIQUES
    //----------------

    /**
     * @param RegistrationFolder $registrationFolder
     * @param int $typeId
     * @return RegistrationFolderFile|null
     */
    public function getByTypeId(RegistrationFolder $registrationFolder, int $typeId): ?RegistrationFolderFile
    {
        return $this->registrationFolderFileRepository->findOneBy(['registrationFolder' => $registrationFolder, 'typeId' => $typeId]);
    }

    /**
     * @param $file
     * @param int $typeId
     * @param RegistrationFolder $registrationFolder
     * @param bool $generated
     * @param User|null $user
     * @param bool $isAttendee
     * @param string|null $title
     * @return RegistrationFolderFile
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function create($file, int $typeId, RegistrationFolder $registrationFolder, bool $generated = false, User $user = null, bool $isAttendee = false, string $title = null): RegistrationFolderFile
    {
        $organism = $registrationFolder->getOrganism();
        $fileTypes = $organism->getRegistrationFolderFileTypes();
        $fileTypeIndex = array_search($typeId, array_column($fileTypes, 'id'));
        if ($fileTypeIndex >= 0) {
            $fileType = $fileTypes[$fileTypeIndex];
            if (!$generated && isset($fileType['allowUploadAttendee']) && !$fileType['allowUploadAttendee'] && $isAttendee) {
                throw new WedofBadRequestHttpException("Erreur, en tant qu'apprenant vous n'avez pas accès à ce fichier.");
            }
            $canUploadFile = $this->canUploadFile($registrationFolder, $fileType['toState'] ?? null);
            if ($canUploadFile) {
                $registrationFolderFiles = $this->registrationFolderFileRepository->findBy(['typeId' => $typeId, 'registrationFolder' => $registrationFolder]);
                $isUpdatedFile = false;
                if ($registrationFolderFiles && empty($fileType['allowMultiple'])) {
                    $isUpdatedFile = true;
                    foreach ($registrationFolderFiles as $registrationFolderFile) {
                        if ($isAttendee && $registrationFolderFile->getState() === FileStates::VALID()->getValue()) {
                            throw new WedofBadRequestHttpException("Erreur, vous ne pouvez pas modifier un document qui a été validé.");
                        }
                        $this->registrationFolderFileRepository->delete($registrationFolderFile);
                    }
                }
                $registrationFolderFile = new RegistrationFolderFile();
                $registrationFolderFile->setTypeId($typeId);
                if (is_string($file)) {
                    $registrationFolderFile->setLink($file);
                    $registrationFolderFile->setFilePath($file);
                    $registrationFolderFile->setFileType("link");
                    try {
                        $embed = new Embed();
                        $title = $title ?: $embed->get($file)->title; // This tries to resolve the URL => must be publicly accessible
                    } catch (Exception $exception) {
                        $title = $file;
                    }
                    $registrationFolderFile->setFileName($title);
                } else {
                    $registrationFolderFile->setFile($file);
                }
                if ($generated) {
                    $registrationFolderFile->setGenerationState(DocumentGenerationStates::GENERATED()->getValue());
                    $registrationFolderFile->setState(FileStates::VALID()->getValue());
                } else {
                    $registrationFolderFile->setState($isAttendee ? FileStates::TO_REVIEW()->getValue() : FileStates::VALID()->getValue());
                }
                $registrationFolderFile = $this->docusealApiService->setToSignIfRequired($organism, $registrationFolder, $registrationFolderFile, $file, $fileType);
                $registrationFolder->addFile($registrationFolderFile);
                $this->save($registrationFolderFile);
                $data = [
                    'title' => "Le document {$registrationFolderFile->getFileName()} a été " . ($isUpdatedFile ? 'remplacé' : 'ajouté'),
                    'description' => $generated ? 'Généré automatiquement' : null,
                    'type' => ActivityTypes::FILE(),
                    'eventTime' => new DateTime(),
                    'qualiopiIndicators' => $fileType['qualiopiIndicators'] ?? null,
                    'origin' => $isAttendee ? "l'Apprenant" : null
                ];
                $this->activityService->create($data, $isAttendee ? null : $user, $registrationFolder, false);
                $this->sendEventRegistrationFolderFile($registrationFolderFile, $isUpdatedFile ? RegistrationFolderFileEvents::FILE_UPDATED : RegistrationFolderFileEvents::FILE_ADDED);
                return $registrationFolderFile;
            } else {
                throw new WedofBadRequestHttpException("Erreur, cette opération est impossible car le dossier de formation est à l'état " . $registrationFolder->getState());
            }
        } else {
            throw new WedofBadRequestHttpException("Erreur, ce type de fichier " . $typeId . " n'est pas autorisé pour ce dossier de formation");
        }
    }

    /**
     * @param RegistrationFolderFile $registrationFolderFile
     * @param User|Attendee $user
     * @return array|string[]|StreamedResponse
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     * @throws WedofConnectionException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws ErrorException
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    public function download(RegistrationFolderFile $registrationFolderFile, $user = null)
    {
        if ($registrationFolderFile->getFileType() == 'sign') {
            $submitter = $this->docusealApiService->retrieveSubmitter($registrationFolderFile->getRegistrationFolder()->getOrganism(), $registrationFolderFile, $user);
            return $this->docusealApiService->displayEmbeddedSignForm($registrationFolderFile, $submitter);
        } else if ($registrationFolderFile->getFileType() != 'link') {
            return $this->downloadHandler->downloadObject($registrationFolderFile, 'file');
        } else {
            //convert google drive content
            if (Tools::startsWith($registrationFolderFile->getLink(), 'https://drive.google.com/')
                || Tools::startsWith($registrationFolderFile->getLink(), 'https://docs.google.com/')) {
                $isGoogleForm = str_contains($registrationFolderFile->getLink(), 'https://docs.google.com/forms/');
                $code = explode('/view', $registrationFolderFile->getLink());
                $code = isset($code[1]) ? $code[0] : explode('/edit', $registrationFolderFile->getLink())[0];
                if (!Tools::contains($code, '/folders/')) { //case drive is a folder...
                    $extension = $isGoogleForm ? '/viewform' : '/preview';
                    $code = '<iframe src="' . $code . $extension . '" height="100%" width="100%"></iframe>';
                }
            } else {
                $embed = new Embed();
                try {
                    $code = $embed->get($registrationFolderFile->getLink())->code;
                } catch (Exception $e) {
                    $code = null;
                }
            }
            if ($code) {
                $code = preg_replace('/height=[\"\'][0-9]+[\"\']/i', 'height="100%"', $code);
                $code = preg_replace('/width=[\"\'][0-9]+[\"\']/i', 'width="100%"', $code);
                return ['html' => $code];
            } else {
                return ['html' => '<div class="w-full"><a href="' . $registrationFolderFile->getLink() . '" 
                                            class="mat-focus-indicator mat-flat-button mat-button-base mat-primary" target="_blank">
                                            <span class="mat-button-wrapper">Ouvrir le document</span>
                                            <div class="mat-ripple mat-button-ripple"></div>
                                            <div class="mat-button-focus-overlay"></div>
                                        </a></div>'];
            }
        }
    }

    /**
     * @param RegistrationFolderFile $registrationFolderFile
     * @param bool $isAttendee
     * @param User|null $user
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function delete(RegistrationFolderFile $registrationFolderFile, bool $isAttendee, User $user = null)
    {
        $title = "Le document {$registrationFolderFile->getFilename()} a été supprimé" . ($isAttendee ? " par l'apprenant" : "");
        $registrationFolder = $registrationFolderFile->getRegistrationFolder();
        $organism = $registrationFolder->getOrganism();
        $fileTypes = $organism->getRegistrationFolderFileTypes();
        $fileTypeIndex = array_search($registrationFolderFile->getTypeId(), array_column($fileTypes, 'id'));

        $oldRegistrationFolderFile = clone $registrationFolderFile;

        if ($fileTypeIndex !== false) {
            $fileType = $fileTypes[$fileTypeIndex];
            if (isset($fileType['allowUploadAttendee']) && !$fileType['allowUploadAttendee'] && $isAttendee) {
                throw new WedofBadRequestHttpException("Erreur, en tant que candidat vous n'avez pas accès à ce fichier");
            }
            $currentState = $registrationFolderFile->getRegistrationFolder()->getState();
            $indexOfCurrentState = array_search($currentState, RegistrationFolderStates::valuesStates());
            $indexOfToState = isset($fileType['toState']) ? array_search($fileType['toState'], RegistrationFolderStates::valuesStates()) : null;
            $generated = !empty($fileType['generated']);
            if ($generated || !$indexOfToState || $indexOfCurrentState < $indexOfToState) {
                if ($registrationFolderFile->getSignedState() !== DocumentSignedStates::NOT_REQUIRED()->getValue()) {
                    $this->docusealApiService->deleteSubmission($organism, $registrationFolderFile);
                }
                $this->registrationFolderFileRepository->delete($registrationFolderFile);
            } else {
                // TODO translate state
                throw new WedofBadRequestHttpException("Erreur, cette opération est impossible car le document est requis par le dossier de formation à l'état " . $currentState);
            }
        } else {
            $this->registrationFolderFileRepository->delete($registrationFolderFile); //old files with deleted filetypes..
        }

        $this->sendEventRegistrationFolderFile($oldRegistrationFolderFile, RegistrationFolderFileEvents::FILE_DELETED);
        $this->activityService->create([
            'title' => $title,
            'type' => ActivityTypes::FILE(),
            'eventTime' => new DateTime(),
            'origin' => $isAttendee ? "l'Apprenant" : null
        ], $user, $registrationFolder, false);
    }

    /**
     * @param RegistrationFolderFile $registrationFolderFile
     * @param array $body
     * @param User|null $user
     * @return RegistrationFolderFile
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function updateState(RegistrationFolderFile $registrationFolderFile, array $body, User $user = null): RegistrationFolderFile
    {
        $properties = ['state', 'comment'];
        $previousState = $registrationFolderFile->getState();
        if ($registrationFolderFile->getGenerationState() !== DocumentGenerationStates::NOT_GENERATED()->getValue() && isset($body['state'])) {
            unset($body['state']);
        }
        foreach ($properties as $property) {
            if (key_exists($property, $body)) {
                $setMethodName = "set" . ucwords($property);
                $registrationFolderFile->{$setMethodName}($body[$property]);
            }
        }
        $this->save($registrationFolderFile);
        $registrationFolder = $registrationFolderFile->getRegistrationFolder();
        if (isset($body['state'])) {
            $eventName = "registrationFolderFile." . $registrationFolderFile->getState();
            if (!empty($body['comment']) && $previousState == $registrationFolderFile->getState()) {
                $eventName = RegistrationFolderFileEvents::FILE_UPDATED;
            }
            $this->sendEventRegistrationFolderFile($registrationFolderFile, $eventName);
            $this->activityService->create([
                'title' => "Le document {$registrationFolderFile->getFilename()} " . FileStates::toFrStringActivity($body['state']),
                'description' => $body['comment'] ?? null,
                'type' => ActivityTypes::FILE(),
                'eventTime' => new DateTime()
            ], $user, $registrationFolder, false);
        }
        return $registrationFolderFile;
    }

    /**
     * used by Messages & notifications
     * @param int $entityId
     * @return RegistrationFolderFile|null
     */
    public function getByEntityId(int $entityId): ?RegistrationFolderFile
    {
        return $this->registrationFolderFileRepository->find($entityId);
    }

    /**
     * @param RegistrationFolderFile $registrationFolderFile
     */
    public function save(RegistrationFolderFile $registrationFolderFile)
    {
        $this->registrationFolderFileRepository->save($registrationFolderFile);
    }

    /**
     * @param RegistrationFolderFile $registrationFolderFile
     * @param string $eventName
     */
    public function sendEventRegistrationFolderFile(RegistrationFolderFile $registrationFolderFile, string $eventName): void
    {
        $event = new RegistrationFolderFileEvents($registrationFolderFile);
        $this->dispatcher->dispatch($event, $eventName);
        $this->logger->info("[" . $registrationFolderFile->getId() . " typeId " . $registrationFolderFile->getTypeId() . "][event] RegistrationFolderFileState event dispatched $eventName ");
    }

    //----------------
    // METHODES PRIVES
    //----------------

    private function canUploadFile(RegistrationFolder $registrationFolder, string $toState = null): bool
    {
        return !array_key_exists($toState, RegistrationFolderStates::valuesStates())
            || ($toState === $this->getNextRegistrationFolderStatesValue($registrationFolder));
    }

    /**
     * @param $registrationFolder
     * @return string|null
     */
    private function getNextRegistrationFolderStatesValue($registrationFolder): ?string
    {
        $currentState = RegistrationFolderStates::from($registrationFolder->getState());
        $indexOf = array_search($currentState, RegistrationFolderStates::valuesStates());
        if ($indexOf < (count(RegistrationFolderStates::valuesStates()) - 1)) {
            return RegistrationFolderStates::valuesStates()[$indexOf + 1];
        } else {
            return null;
        }
    }
}
