<?php


namespace App\Service;


use App\Entity\Activity;
use App\Entity\Certification;
use App\Entity\CertificationPartner;
use App\Entity\CertificationPartnerHistory;
use App\Entity\Organism;
use App\Entity\User;
use App\Event\CertificationPartner\CertificationPartnerEvents;
use App\Exception\WedofBadRequestHttpException;
use App\Library\CertificationStatistics;
use App\Library\utils\enums\ActivityTypes;
use App\Library\utils\enums\CertificationPartnerAuditResults;
use App\Library\utils\enums\CertificationPartnerHabilitation;
use App\Library\utils\enums\CertificationPartnerStates;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\Tools;
use App\Repository\CertificationFolderRepository;
use App\Repository\CertificationPartnerFileRepository;
use App\Repository\CertificationPartnerRepository;
use App\Repository\RegistrationFolderRepository;
use App\Repository\TrainingActionRepository;
use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\QueryBuilder;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Throwable;

class CertificationPartnerService implements LoggerAwareInterface
{
    private LoggerInterface $logger;
    private EventDispatcherInterface $dispatcher;
    private EvaluationService $evaluationService;
    private CertificationPartnerRepository $certificationPartnerRepository;
    private CertificationPartnerFileRepository $certificationPartnerFileRepository;
    private CertificationFolderRepository $certificationFolderRepository;
    private TrainingActionRepository $trainingActionRepository;
    private ActivityService $activityService;
    private RegistrationFolderRepository $registrationFolderRepository;

    //-------------------
    // METHODES PUBLIQUES
    //-------------------
    private ValidatorInterface $validator;

    public function __construct(EventDispatcherInterface $dispatcher,
                                CertificationPartnerRepository $certificationPartnerRepository,
                                EvaluationService $evaluationService,
                                CertificationFolderRepository $certificationFolderRepository,
                                CertificationPartnerFileRepository $certificationPartnerFileRepository,
                                TrainingActionRepository $trainingActionRepository,
                                ValidatorInterface $validator,
                                ActivityService $activityService,
                                RegistrationFolderRepository $registrationFolderRepository)
    {
        $this->dispatcher = $dispatcher;
        $this->evaluationService = $evaluationService;
        $this->certificationPartnerRepository = $certificationPartnerRepository;
        $this->certificationFolderRepository = $certificationFolderRepository;
        $this->certificationPartnerFileRepository = $certificationPartnerFileRepository;
        $this->trainingActionRepository = $trainingActionRepository;
        $this->validator = $validator;
        $this->activityService = $activityService;
        $this->registrationFolderRepository = $registrationFolderRepository;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    /**
     * @param Certification $certification
     * @param Organism $organism
     * @param User|null $user
     * @param Organism|null $certifier
     * @return CertificationPartner
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function create(Certification $certification, Organism $organism, User $user = null, Organism $certifier = null): CertificationPartner
    {
        $state = CertificationPartnerStates::DRAFT();
        $certificationPartner = new CertificationPartner();
        $certificationPartner->setPartner($organism);
        $certificationPartner->setState($state->getValue());
        $certificationPartner->setStateLastUpdate(new DateTime());
        $certificationPartner->setHabilitation(CertificationPartnerHabilitation::TRAIN()->getValue());
        $certificationPartner->setCertification($certification);
        $certificationPartner->setCertifier($certifier);
        $urls = Tools::retrieveWebsite($organism);
        if (count($urls) > 0) {
            $certificationPartner->setUrls($urls);
        }
        $certificationPartnerHistory = new CertificationPartnerHistory();
        $certificationPartnerHistory->setDraftDate(new DateTime());
        $certificationPartner->setHistory($certificationPartnerHistory);
        $errors = $this->validator->validate($certificationPartner);
        if (count($errors) > 0) {
            throw new WedofBadRequestHttpException((string)$errors);
        }
        $this->save($certificationPartner);
        $this->sendEvent($certificationPartner, CertificationPartnerEvents::CREATED);
        $this->sendStateEvent($certificationPartner, $state->getValue());
        $this->createCreateActivity($certificationPartner, $user);
        return $certificationPartner;
    }

    /**
     * @param CertificationPartner $certificationPartner
     * @param User $user
     * @return CertificationPartner
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function request(CertificationPartner $certificationPartner, User $user): CertificationPartner
    {
        $isPartner = $user->getMainOrganism() === $certificationPartner->getPartner();
        if ($isPartner) {
            $missingFiles = $this->checkForRequiredFiles(CertificationPartnerStates::PROCESSING(), $certificationPartner);
            if ($missingFiles) {
                throw new WedofBadRequestHttpException("Erreur, des fichiers requis sont manquants pour pouvoir changer d'état : " . join(", ", $missingFiles));
            }
        }
        // TODO check if transition relevant if certifier does not have "promote" permissions
        $this->updateState($certificationPartner, CertificationPartnerStates::PROCESSING()->getValue(), $user);
        return $certificationPartner;
    }

    /**
     * @param CertificationPartner $certificationPartner
     * @param User|null $user
     * @return CertificationPartner
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function abort(CertificationPartner $certificationPartner, User $user): CertificationPartner
    {
        if ($certificationPartner->getState() !== CertificationPartnerStates::PROCESSING()->getValue()) {
            throw new WedofBadRequestHttpException('Le statut doit être en état "demande en cours" pour pouvoir faire cette action');
        }
        $this->updateState($certificationPartner, CertificationPartnerStates::ABORTED()->getValue(), $user);
        return $certificationPartner;
    }

    /**
     * @param CertificationPartner $certificationPartner
     * @param User|null $user
     * @return CertificationPartner
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function refuse(CertificationPartner $certificationPartner, User $user): CertificationPartner
    {
        if ($certificationPartner->getState() !== CertificationPartnerStates::PROCESSING()->getValue()) {
            throw new WedofBadRequestHttpException('Le statut doit être en état "demande en cours" pour pouvoir faire cette action');
        }
        $this->updateState($certificationPartner, CertificationPartnerStates::REFUSED()->getValue(), $user);
        return $certificationPartner;
    }

    /**
     * @param CertificationPartner $certificationPartner
     * @param User|null $user
     * @return CertificationPartner
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function reopen(CertificationPartner $certificationPartner, User $user): CertificationPartner
    {
        if (!in_array($certificationPartner->getState(), [CertificationPartnerStates::ABORTED(), CertificationPartnerStates::REFUSED()])) {
            throw new WedofBadRequestHttpException('Le statut doit être en état "abandonné" ou "refusé" pour pouvoir faire cette action');
        }
        $this->updateState($certificationPartner, CertificationPartnerStates::PROCESSING()->getValue(), $user);
        return $certificationPartner;
    }

    /**
     * @param CertificationPartner $certificationPartner
     * @param CertificationPartnerHabilitation|null $habilitation
     * @return CertificationPartner
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function activate(CertificationPartner $certificationPartner, CertificationPartnerHabilitation $habilitation = null): CertificationPartner
    {
        $body = $habilitation ? ['habilitation' => $habilitation] : null;
        if ($certificationPartner->getCompliance() !== CertificationPartnerAuditResults::NONE()->getValue()) {
            $body['compliance'] = CertificationPartnerAuditResults::NONE()->getValue();
        }
        if ($body) {
            $this->internalUpdate($certificationPartner, $body);
        }
        $this->updateState($certificationPartner, CertificationPartnerStates::ACTIVE()->getValue());
        return $certificationPartner;
    }

    /**
     * @param CertificationPartner $certificationPartner
     * @param array $parameters
     * @return CertificationPartner
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function suspend(CertificationPartner $certificationPartner, array $parameters = []): CertificationPartner
    {
        if ($certificationPartner->getState() !== CertificationPartnerStates::ACTIVE()->getValue()) {
            throw new WedofBadRequestHttpException('Le statut doit être en état "actif" pour pouvoir faire cette action');
        }
        $this->updateState($certificationPartner, CertificationPartnerStates::SUSPENDED()->getValue(), null, $parameters);
        return $certificationPartner;
    }

    /**
     * @param CertificationPartner $certificationPartner
     * @param array $parameters
     * @return CertificationPartner
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function revoke(CertificationPartner $certificationPartner, array $parameters = []): CertificationPartner
    {
        if ($certificationPartner->getState() !== CertificationPartnerStates::ACTIVE()->getValue()) {
            throw new WedofBadRequestHttpException('Le statut doit être en état "actif" pour pouvoir faire cette action');
        }
        $this->updateState($certificationPartner, CertificationPartnerStates::REVOKED()->getValue(), null, $parameters);
        return $certificationPartner;
    }

    /**
     * @param CertificationPartner $certificationPartner
     * @param array $data
     * @param User|null $user
     * @param array $parameters
     * @return CertificationPartner
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function update(CertificationPartner $certificationPartner, array $data = [], User $user = null, array $parameters = []): CertificationPartner
    {
        // TODO more fields / more checks (e.g. if habilitation is of known type)
        $this->internalUpdate($certificationPartner, $data, $user, $parameters);
        return $certificationPartner;
    }

    /**
     * @param Certification $certification
     * @param Organism|null $certifier
     * @param bool $activeOnly
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function getPartnersCount(Certification $certification, Organism $certifier = null, bool $activeOnly = true): int
    {
        return $this->certificationPartnerRepository->countPartners($certification, $activeOnly, $certifier);
    }

    /**
     * @param Certification $certification
     * @param array|null $states
     * @return array
     */
    public function getPartnersSiret(Certification $certification, ?array $states = null): array
    {
        $filters = ['certification' => $certification];
        if ($states) {
            $filters['state'] = $states;
        }
        $certificationPartners = $this->certificationPartnerRepository->findBy($filters);
        return array_map(function ($row) {
            $organism = $row->getPartner();
            return $organism->getSiret();
        }, $certificationPartners);
    }

    /**
     * @param array $parameters
     * @param Certification|null $certification
     * @return QueryBuilder
     */
    public function listReturnQueryBuilder(array $parameters, Certification $certification = null): QueryBuilder
    {
        return $this->certificationPartnerRepository->findAllReturnQueryBuilder($parameters, $certification);
    }

    /**
     * @param Certification $certification
     * @param array $parameters
     * @return ArrayCollection
     */
    public function list(Certification $certification, array $parameters): ArrayCollection
    {
        return new ArrayCollection($this->listReturnQueryBuilder($parameters, $certification)->getQuery()->getResult());
    }

    /**
     * @param Organism $partner
     * @param array $certifications
     * @param bool $activeOnly
     * @return ArrayCollection
     */
    public function listByPartner(Organism $partner, array $certifications = [], bool $activeOnly = true): ArrayCollection
    {
        return $this->certificationPartnerRepository->findAllByPartner($partner, $certifications, $activeOnly);
    }

    /**
     * @param Organism $certifier
     * @param Organism $partner
     * @return ArrayCollection
     */
    public function listByOrganisms(Organism $certifier, Organism $partner): ArrayCollection
    {
        return $this->certificationPartnerRepository->findAllByOrganisms($certifier, $partner);
    }

    /**
     * @param Certification $certification
     * @param Organism|null $partner
     * @param bool $activeOnly
     * @return CertificationPartner|null
     */
    public function getByCertificationAndPartner(Certification $certification, ?Organism $partner, bool $activeOnly = true): ?CertificationPartner
    {
        return $partner ? $this->certificationPartnerRepository->findOneByCertificationAndPartner($certification, $partner, $activeOnly) : null;
    }

    /**
     * @param CertificationPartner $certificationPartner
     * @return CertificationStatistics
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function getStatistics(CertificationPartner $certificationPartner): CertificationStatistics
    {
        $certification = $certificationPartner->getCertification();
        $organism = $certificationPartner->getPartner();
        return new CertificationStatistics($certification, $this->certificationFolderRepository, $organism, $this->evaluationService, $this->trainingActionRepository);
    }

    /**
     * @param Organism $certifier
     * @param Certification $certification
     * @return CertificationPartner
     */
    public function getTransientCertifierSelfCertificationPartner(Organism $certifier, Certification $certification): CertificationPartner
    {
        $certificationPartner = new CertificationPartner();
        $certificationPartner->setCertification($certification);
        $certificationPartner->setPartner($certifier);
        $certificationPartner->setCertifier($certifier);
        $certificationPartner->setState(CertificationPartnerStates::ACTIVE()->getValue());
        $certificationPartner->setHabilitation(CertificationPartnerHabilitation::TRAIN_EVALUATE());
        return $certificationPartner;
    }

    /**
     * @param CertificationPartner $certificationPartner
     * @return void
     */
    public function delete(CertificationPartner $certificationPartner): void
    {
        $this->certificationPartnerRepository->delete($certificationPartner);
    }

    /**
     * @param int $entityId
     * @return CertificationPartner|null
     */
    public function getByEntityId(int $entityId): ?CertificationPartner
    {
        return $this->getById($entityId);
    }

    //----------------
    // METHODES PRIVES
    //----------------
    /**
     * @param CertificationPartner $certificationPartner
     * @return CertificationPartner
     */
    private function save(CertificationPartner $certificationPartner): CertificationPartner
    {
        return $this->certificationPartnerRepository->save($certificationPartner);
    }

    /**
     * @param CertificationPartner $certificationPartner
     * @param string $state
     * @param User|null $user
     * @param array $parameters
     * @return void
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    private function updateState(CertificationPartner $certificationPartner, string $state, User $user = null, array $parameters = []): void
    {
        $previousState = CertificationPartnerStates::from($certificationPartner->getState());
        $certificationPartner->setState($state);
        $certificationPartner->setStateLastUpdate(new DateTime());
        $certificationPartner->setPendingActivation(false); // This prevents changing state and putting pending at true at the same time, but it is worth it as it ensures consistency
        $certificationPartner->setPendingRevocation(false); // This prevents changing state and putting pending at true at the same time, but it is worth it as it ensures consistency
        $certificationPartner->setPendingSuspension(false); // This prevents changing state and putting pending at true at the same time, but it is worth it as it ensures consistency
        if (!$certificationPartner->getHistory()) {
            $certificationPartner->setHistory(new CertificationPartnerHistory());
        }
        $certificationPartner->getHistory()->{"set" . ucwords($state . "Date")}(new DateTime());
        $this->save($certificationPartner);
        $this->sendStateEvent($certificationPartner, $state, $previousState, $parameters);
        $this->sendEvent($certificationPartner, CertificationPartnerEvents::UPDATED);
        $this->createUpdateStateActivity($certificationPartner, $user, $parameters);
    }

    /**
     * @param CertificationPartner $certificationPartner
     * @param array $data
     * @param User|null $user
     * @param array|null $parameters
     * @return void
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    private function internalUpdate(CertificationPartner $certificationPartner, array $data = [], User $user = null, array $parameters = null): void
    {
        $habilitationChanged = false;
        $pendingActivationChanged = false;
        $pendingRevocationChanged = false;
        $pendingSuspensionChanged = false;
        $otherFieldChanged = false;
        $reinitialize = false;
        if (isset($data['tags'])) {
            $data['tags'] = array_map('strtolower', $data['tags']); // Required to enforce lower + avoid bug with lower / upper
            $certificationPartner->setTagsText(implode(', ', $data['tags']));
        }
        if (isset($data["metadata"])) {
            $certificationPartner->setMetadata(empty($data['metadata']) ? null : Tools::removeSpacesInKeys($data['metadata']));
        }
        foreach (['comment', 'amountHt', 'compliance', 'trainingsZone'] as $property) {
            if (key_exists($property, $data)) {
                $getMethodName = 'get' . ucwords($property);
                $currentValue = $certificationPartner->{$getMethodName}();
                $newValue = $data[$property];
                if ($currentValue !== $newValue) {
                    $setMethodName = "set" . ucwords($property);
                    $certificationPartner->{$setMethodName}($data[$property]);
                    $otherFieldChanged = true;
                }
            }
        }
        if (key_exists('compliance', $data)) {
            $certificationPartner->setComplianceLastUpdate(new DateTime());
        }
        if (key_exists('habilitation', $data) && $data['habilitation'] !== $certificationPartner->getHabilitation()) {
            $habilitationChanged = true;
            $certificationPartner->setHabilitation($data['habilitation']);
        }
        if (key_exists('pendingActivation', $data) && $data['pendingActivation'] !== $certificationPartner->isPendingActivation()) {
            $pendingActivationChanged = true;
            $certificationPartner->setPendingActivation($data['pendingActivation']);
        }
        if (key_exists('pendingRevocation', $data) && $data['pendingRevocation'] !== $certificationPartner->isPendingRevocation()) {
            $pendingRevocationChanged = true;
            $certificationPartner->setPendingRevocation($data['pendingRevocation']);
        }
        if (key_exists('pendingSuspension', $data) && $data['pendingSuspension'] !== $certificationPartner->isPendingSuspension()) {
            $pendingSuspensionChanged = true;
            $certificationPartner->setPendingSuspension($data['pendingSuspension']);
        }
        if (key_exists('deleteFiles', $data) && $data['deleteFiles'] && $certificationPartner->getState() == CertificationPartnerStates::PROCESSING()->getValue()) {
            $this->deleteFiles($certificationPartner);
            $reinitialize = true;
        }
        if (isset($data['certifier'])) {
            $certificationPartner->setCertifier($data['certifier']);
        }
        if (key_exists('skillSets', $data)) {
            $certificationPartner->setSkillSets($data['skillSets']);
        }
        if (key_exists('urls', $data)) {
            $certificationPartner->setUrls($data['urls']);
        }
        $this->save($certificationPartner);

        $this->sendEvent($certificationPartner, CertificationPartnerEvents::UPDATED);
        if ($otherFieldChanged) {
            $this->createUpdateActivity($certificationPartner, $user);
        }
        if ($habilitationChanged) {
            $this->createUpdateHabilitationActivity($certificationPartner, $user);
        }
        if ($pendingActivationChanged) {
            $this->createPendingActivationActivity($certificationPartner, $user);
            $this->sendEvent($certificationPartner, CertificationPartnerEvents::PENDING_ACTIVATION_CHANGED);
        }
        if ($pendingRevocationChanged) {
            $this->createPendingRevocationActivity($certificationPartner, $user);
            $this->sendEvent($certificationPartner, CertificationPartnerEvents::PENDING_REVOCATION_CHANGED);
        }
        if ($pendingSuspensionChanged) {
            $this->createPendingSuspensionActivity($certificationPartner, $user, $parameters);
            $this->sendEvent($certificationPartner, CertificationPartnerEvents::PENDING_SUSPENSION_CHANGED);
        }
        if ($reinitialize) {
            $this->createReinitializeActivity($certificationPartner, $user);
        }
    }

    /**
     * @param CertificationPartner $certificationPartner
     * @return void
     */
    private function deleteFiles(CertificationPartner $certificationPartner): void
    {
        $certificationPartnerFiles = $this->certificationPartnerFileRepository->findBy(['certificationPartner' => $certificationPartner]);
        if (!empty($certificationPartnerFiles)) {
            foreach ($certificationPartnerFiles as $certificationPartnerFile) {
                $this->certificationPartnerFileRepository->delete($certificationPartnerFile);
            }
        }
    }

    /**
     * @param CertificationPartner $certificationPartner
     * @param string $eventName
     */
    private function sendEvent(CertificationPartner $certificationPartner, string $eventName): void
    {
        $event = new CertificationPartnerEvents($certificationPartner);
        $this->dispatcher->dispatch($event, $eventName);
        $partner = $certificationPartner->getPartner();
        $certification = $certificationPartner->getCertification();
        $this->logger->info("[" . $certification->getCertifInfo() . "][event] CertificationPartner event dispatched $eventName for " . $partner->getName());
    }

    /**
     * @param CertificationPartner $certificationPartner
     * @param string $state
     * @param CertificationPartnerStates|null $previousState
     * @param array $parameters
     */
    private function sendStateEvent(CertificationPartner $certificationPartner, string $state, CertificationPartnerStates $previousState = null, array $parameters = []): void
    {
        $event = new CertificationPartnerEvents($certificationPartner, $previousState, $parameters);
        $eventName = 'certificationPartner.' . $state;
        $this->dispatcher->dispatch($event, $eventName);
        $partner = $certificationPartner->getPartner();
        $certification = $certificationPartner->getCertification();
        $this->logger->info("[" . $certification->getCertifInfo() . "][event] CertificationPartner event dispatched $eventName for " . $partner->getName());
    }

    /**
     * @param CertificationPartner $certificationPartner
     * @param User|null $user
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    private function createCreateActivity(CertificationPartner $certificationPartner, User $user = null): void
    {
        if ($user) {
            $currentOrganism = $user->getMainOrganism();
            $organismType = $certificationPartner->getCertification()->isCertifier($currentOrganism) ? 'certificateur' : 'de formation ' . $currentOrganism->getName();
            $title = "Une demande de partenariat a été créé par l'organisme " . $organismType;
        } else {
            $title = "Un partenariat a été créé depuis France Compétences";
        }
        $this->activityService->create([
            'title' => $title,
            'type' => ActivityTypes::CREATE()->getValue(),
            'eventTime' => new DateTime(),
            'origin' => $user ? null : 'France Compétences'
        ], $user, $certificationPartner, false);
    }

    /**
     * @param CertificationPartner $certificationPartner
     * @param User|null $user
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    private function createUpdateActivity(CertificationPartner $certificationPartner, User $user = null): void
    {
        $this->activityService->create([
            'title' => "Le partenariat a été mis à jour",
            'type' => ActivityTypes::UPDATE()->getValue(),
            'eventTime' => new DateTime(),
            'origin' => $user ? null : 'France Compétences'
        ], $user, $certificationPartner, false);
    }

    /**
     * @param CertificationPartner $certificationPartner
     * @param User|null $user
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    private function createReinitializeActivity(CertificationPartner $certificationPartner, User $user = null): void
    {
        $this->activityService->create([
            'title' => "Les données du partenariat ont été réinitialisés",
            'type' => ActivityTypes::UPDATE()->getValue(),
            'eventTime' => new DateTime(),
            'origin' => $user ? null : 'France Compétences'
        ], $user, $certificationPartner, false);
    }

    /**
     * @param CertificationPartner $certificationPartner
     * @param User|null $user
     * @param array $parameters
     * @return Activity
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    private function createUpdateStateActivity(CertificationPartner $certificationPartner, ?User $user, array $parameters): Activity
    {
        $state = $certificationPartner->getState();
        $title = "Le partenariat est passé à l'état " . CertificationPartnerStates::toFrString($state);
        if (isset($parameters['cause']) && $parameters['cause'] === 'siretClosed') {
            $title .= ' parce que le SIRET de cet établissement est fermé';
        }
        return $this->activityService->create([
            'title' => $title,
            'type' => ActivityTypes::UPDATE_STATE()->getValue(),
            'eventTime' => new DateTime(),
            'field' => 'state',
            'newValue' => $state,
            'origin' => $user ? null : ($parameters['origin'] ?? DataProviders::toFrString($certificationPartner->getCertification()->getDataProvider()))
        ], $user, $certificationPartner, false);
    }

    /**
     * @param CertificationPartner $certificationPartner
     * @param User|null $user
     * @return Activity
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    private function createUpdateHabilitationActivity(CertificationPartner $certificationPartner, User $user = null): Activity
    {
        $habilitation = $certificationPartner->getHabilitation();
        return $this->activityService->create([
            'title' => "L'habilitation du partenariat est passée à " . CertificationPartnerHabilitation::toFrString($habilitation),
            'type' => ActivityTypes::UPDATE()->getValue(),
            'eventTime' => new DateTime(),
            'field' => 'habilitation',
            'newValue' => $habilitation,
            'origin' => $user ? null : 'France Compétences'
        ], $user, $certificationPartner, false);
    }

    /**
     * @param CertificationPartner $certificationPartner
     * @param User|null $user
     * @return Activity
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    private function createPendingActivationActivity(CertificationPartner $certificationPartner, User $user = null): Activity
    {
        $pendingActivation = $certificationPartner->isPendingActivation();
        return $this->activityService->create([
            'title' => $pendingActivation ? "Le partenariat est en cours d'activation" : "Le partenariat n'est plus en cours d'activation",
            'type' => ActivityTypes::UPDATE()->getValue(),
            'eventTime' => new DateTime(),
            'field' => 'pendingActivation',
            'newValue' => $pendingActivation,
            'oldValue' => !$pendingActivation,
            'origin' => $user ? null : 'France Compétences'
        ], $user, $certificationPartner, false);
    }

    /**
     * @param CertificationPartner $certificationPartner
     * @param User|null $user
     * @param array $parameters
     * @return Activity
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    private function createPendingSuspensionActivity(CertificationPartner $certificationPartner, User $user = null, array $parameters = []): Activity
    {
        $pendingSuspension = $certificationPartner->isPendingSuspension();
        return $this->activityService->create([
            'title' => $pendingSuspension ? "Le partenariat est en cours de suspension" : "Le partenariat n'est plus en cours de suspension",
            'type' => ActivityTypes::UPDATE()->getValue(),
            'eventTime' => new DateTime(),
            'field' => 'pendingSuspension',
            'newValue' => $pendingSuspension,
            'oldValue' => !$pendingSuspension,
            'origin' => $parameters['origin'] ?? null
        ], $user, $certificationPartner, false);
    }

    /**
     * @param CertificationPartner $certificationPartner
     * @param User|null $user
     * @return Activity
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    private function createPendingRevocationActivity(CertificationPartner $certificationPartner, User $user = null): Activity
    {
        $pendingRevocation = $certificationPartner->isPendingRevocation();
        return $this->activityService->create([
            'title' => $pendingRevocation ? "Le partenariat est en cours de révocation" : "Le partenariat n'est plus en cours de révocation",
            'type' => ActivityTypes::UPDATE()->getValue(),
            'eventTime' => new DateTime(),
            'field' => 'pendingRevocation',
            'newValue' => $pendingRevocation,
            'oldValue' => !$pendingRevocation,
            'origin' => $user ? null : 'France Compétences'
        ], $user, $certificationPartner, false);
    }

    /**
     * @param CertificationPartnerStates $state
     * @param CertificationPartner $certificationPartner
     * @return array
     */
    private function checkForRequiredFiles(CertificationPartnerStates $state, CertificationPartner $certificationPartner): array
    {
        $fileTypes = $certificationPartner->getCertification()->getCertificationPartnerFileTypes($certificationPartner);
        $missingFileTypes = Tools::getMissingFileTypesForState($fileTypes, $certificationPartner->getFiles(), $state->getValue());
        return array_column($missingFileTypes, 'name');
    }

    /**
     * @param int $entityId
     * @return CertificationPartner|null
     */
    public function getById(int $entityId): ?CertificationPartner
    {
        return $this->certificationPartnerRepository->find($entityId);
    }
}
