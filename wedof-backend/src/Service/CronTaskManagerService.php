<?php
// src/Service/CronTaskManagerService.php
namespace App\Service;

use App\Entity\Connection;
use App\Entity\Organism;
use App\Event\Connection\ConnectionEvents;
use App\Library\utils\enums\CertificationTypes;
use App\Library\utils\enums\ConnectionStates;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\enums\PaymentSortParams;
use App\Library\utils\enums\RegistrationFolderStates;
use App\Message\MonitorRegistrationFolders;
use App\Message\RefreshConnection;
use App\Message\StartBatchesRegistrationFolders;
use App\Message\SynchronizeCatalog;
use App\Message\SynchronizeCertification;
use App\Message\SynchronizeCertificationPartners;
use App\Message\SynchronizeCertifiers;
use App\Message\SynchronizeEvaluations;
use App\Message\SynchronizeFranceCompetencesFiles;
use App\Message\SynchronizeNewRegistrationFolders;
use App\Message\SynchronizeOrganisms;
use App\Message\SynchronizePartners;
use App\Message\SynchronizePayments;
use App\Message\SynchronizeStateRegistrationFolders;
use App\Message\SynchronizeWorkingContracts;
use App\Service\DataProviders\BaseApiService;
use App\Service\DataProviders\InterfaceCertificationApiService;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Exception;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Messenger\Stamp\DelayStamp;
use Throwable;

class CronTaskManagerService implements LoggerAwareInterface
{
    private LoggerInterface $logger;
    private MessageBusInterface $messageBus;
    private CertificationService $certificationService;
    private ConnectionService $connectionService;
    private OrganismService $organismService;
    private EventDispatcherInterface $dispatcher;

    /**
     * CronTaskManagerService constructor.
     * @param MessageBusInterface $messageBus
     * @param CertificationService $certificationService
     * @param ConnectionService $connectionService
     * @param OrganismService $organismService
     * @param EventDispatcherInterface $dispatcher
     */
    public function __construct(MessageBusInterface $messageBus, CertificationService $certificationService, ConnectionService $connectionService, OrganismService $organismService, EventDispatcherInterface $dispatcher)
    {
        $this->messageBus = $messageBus;
        $this->connectionService = $connectionService;
        $this->certificationService = $certificationService;
        $this->organismService = $organismService;
        $this->dispatcher = $dispatcher;
    }

    //-------------------
    // METHODES PUBLIQUES
    //-------------------
    /**
     * @param LoggerInterface $logger
     */
    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    /**
     * @param Organism $organism
     * @param DataProviders $dataProvider
     * @param RegistrationFolderStates $state
     * @param int $limit
     * @param int $skip
     * @param int $delay
     * @param string $order
     */
    public function dispatchSynchronizeRegistrationFolders(Organism $organism, DataProviders $dataProvider, RegistrationFolderStates $state, int $limit, int $skip = 0, int $delay = 0, string $order = 'DESC')
    {
        if (RegistrationFolderStates::NOT_PROCESSED()->equals($state)) {
            $message = new SynchronizeNewRegistrationFolders($organism->getSiret(), $dataProvider, $state, null, $limit, $skip, $order);
        } else {
            $message = new SynchronizeStateRegistrationFolders($organism->getSiret(), $dataProvider, $state, null, $limit, $skip, $order);
        }
        $envelope = new Envelope($message, [
            new DelayStamp($delay)
        ]);
        $this->messageBus->dispatch($envelope);
    }

    /**
     * @param Organism $organism
     * @param DataProviders $dataProvider
     * @param $limit
     * @param $skip
     * @param bool $disableWedofEvents
     */
    public function dispatchStartBatchesRegistrationFolders(Organism $organism, DataProviders $dataProvider, $limit, $skip, bool $disableWedofEvents = true): void
    {
        $message = new StartBatchesRegistrationFolders($dataProvider, $organism->getSiret(), $limit, $skip, $disableWedofEvents);
        $this->messageBus->dispatch($message);
    }

    /**
     * @param Organism $organism
     * @param DataProviders $dataProvider
     * @param int $foldersLimit
     */
    public function dispatchRefreshRegistrationFolders(Organism $organism, DataProviders $dataProvider, int $foldersLimit = 500)
    {
        $apiService = BaseApiService::getRegistrationFolderApiServiceByDataProvider($dataProvider);
        $count = $apiService->getRegistrationFoldersCount($organism, RegistrationFolderStates::ALL(), null, ["maxRetry" => 3, "requestDispatchMonitoringEvent" => false]);
        if ($count > 0) {
            $pages = ceil($count / $foldersLimit);
            for ($i = 0; $i <= $pages; $i++) {
                $message = new SynchronizeStateRegistrationFolders($organism->getSiret(), $dataProvider, RegistrationFolderStates::ALL(), null, $foldersLimit, $foldersLimit * $i, 'DESC', false, $count);
                $envelope = new Envelope($message, [
                    new DelayStamp(2000 * $i) //give space
                ]);
                $this->messageBus->dispatch($envelope);
            }
        }
    }

    /**
     * @param Organism $organism
     * @param array|null $certificationCodes
     * @param DataProviders $dataProvider
     * @param int $delay
     */
    public function dispatchSynchronizeCatalogOpenData(Organism $organism, ?array $certificationCodes, DataProviders $dataProvider, int $delay = 0)
    {
        $message = new SynchronizeCatalog($organism->getSiret(), $dataProvider, [], $certificationCodes, true);
        $envelope = new Envelope($message, [
            new DelayStamp($delay)
        ]);
        $this->messageBus->dispatch($envelope);
        $this->logger->debug("[SynchronizeCatalog] message sent for organism " . $organism->getName() . " " . $organism->getSiret());
    }

    /**
     * @param Organism $organism
     * @param DataProviders $dataProvider
     * @param string|null $state
     * @param int $delay
     * @param PaymentSortParams|null $sort
     */
    public function dispatchSynchronizePayments(Organism $organism, DataProviders $dataProvider, ?string $state, int $delay, ?PaymentSortParams $sort)
    {
        $message = new SynchronizePayments($organism->getSiret(), $dataProvider, $state, $sort);
        $envelope = new Envelope($message, [
            new DelayStamp($delay)
        ]);
        $this->messageBus->dispatch($envelope);
    }

    /**
     * @param Organism $organism
     * @param DataProviders $dataProvider
     * @param int $delay
     */
    public function dispatchSynchronizeEvaluations(Organism $organism, DataProviders $dataProvider, int $delay = 0)
    {
        $message = new SynchronizeEvaluations($organism->getSiret(), $dataProvider);
        $envelope = new Envelope($message, [
            new DelayStamp($delay)
        ]);
        $this->messageBus->dispatch($envelope);
    }

    /**
     * @param Organism|null $organism
     * @return void
     */
    public function dispatchSynchronizeOrganisms(Organism $organism = null)
    {
        $message = new SynchronizeOrganisms(null, null, $organism ? $organism->getSiret() : null);
        $this->messageBus->dispatch($message);
    }

    /**
     * @return void
     */
    public function dispatchSynchronizeFranceCompetencesFiles(string $type = null)
    {
        // NOT ASYNC !!!
        $this->logger->debug("start dispatchSynchronizeFranceCompetencesFiles " . ($type ?: "all"));
        $types = $type ? [CertificationTypes::from(strtoupper($type))] : CertificationTypes::franceCompetencesTypes();
        foreach ($types as $aType) {
            $this->logger->debug("start dispatchSynchronizeFranceCompetencesFiles " . $aType->getValue());
            $message = new SynchronizeFranceCompetencesFiles($aType);
            $envelope = new Envelope($message, [
                new DelayStamp(1000)
            ]);
            $this->messageBus->dispatch($envelope);
            $this->logger->debug("end dispatchSynchronizeFranceCompetencesFiles " . $aType->getValue());
        }
        $this->logger->debug("end dispatchSynchronizeFranceCompetencesFiles " . ($type ?: "all"));
    }

    /**
     * @param string $certifInfo
     * @param DataProviders $dataProvider
     * @throws Exception
     */
    public function dispatchSynchronizeCertificationsAndPartners(string $certifInfo, DataProviders $dataProvider)
    {
        $certificationApiService = BaseApiService::getCertificationApiServiceByDataProvider($dataProvider);
        if (empty($certifInfo)) {
            set_time_limit(600); // la création des messages peut excéder les 30 secondes max d'exécution
            /** @var string $certifInfo */
            foreach ($this->certificationService->listUpdatableCertifInfo() as $certifInfo) {
                $this->messageBus->dispatch($this->getSynchronizeCertificationAndPartnersMessage($certifInfo, $certificationApiService, $certificationApiService->getCertificationDataForUpdate($certifInfo)));
            }
        } else {
            $this->messageBus->dispatch($this->getSynchronizeCertificationAndPartnersMessage($certifInfo, $certificationApiService,$certificationApiService->getCertificationDataForUpdate($certifInfo)));
        }
    }

    /**
     * @param string $certifInfo
     * @param DataProviders $dataProvider
     * @throws Exception
     */
    public function dispatchSynchronizeCertifiers(string $certifInfo, DataProviders $dataProvider)
    {
        $certificationApiService = BaseApiService::getCertificationApiServiceByDataProvider($dataProvider);
        if (empty($certifInfo)) {
            /** @var string $certifInfo */
            foreach ($this->certificationService->listUpdatableCertifInfo() as $certifInfo) {
                $this->messageBus->dispatch(new SynchronizeCertifiers($certifInfo, $certificationApiService->getCertifiersDataForUpdate($certifInfo)));
            }
        } else {
            $this->messageBus->dispatch(new SynchronizeCertifiers($certifInfo, $certificationApiService->getCertifiersDataForUpdate($certifInfo)));
        }
    }

    /**
     * @param string $certifInfo
     * @throws Exception
     */
    public function dispatchSynchronizePartners(string $certifInfo)
    {
        if (empty($certifInfo)) {
            /** @var string $certifInfo */
            foreach ($this->certificationService->listUpdatableCertifInfo() as $certifInfo) {
                $this->messageBus->dispatch(new SynchronizePartners($certifInfo));
            }
        } else {
            $this->messageBus->dispatch(new SynchronizePartners($certifInfo));
        }
    }

    /**
     * @param string $certifInfo
     * @param DataProviders $dataProvider
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    public function dispatchSynchronizeCertificationPartners(string $certifInfo, DataProviders $dataProvider)
    {
        if (empty($certifInfo)) {
            /** @var string $certifInfo */
            foreach ($this->certificationService->listWithCertificationPartnersToUpdate($dataProvider) as $certifInfo) {
                $certification = $this->certificationService->getCertification(['certifInfo' => $certifInfo]);
                $certifier = $this->organismService->getCertifierWithActiveConnectionForCertification($certification) ?? $certification->getDefaultCertifier();
                if ($this->connectionService->hasAccess($certifier, $dataProvider, true)) {
                    $this->messageBus->dispatch(new SynchronizeCertificationPartners($certifInfo, $certifier->getSiret(), $dataProvider, 1));
                }
            }
        } else {
            $certification = $this->certificationService->getCertification(['certifInfo' => $certifInfo]);
            $dataProvider = DataProviders::from($certification->getDataProvider());
            $certifier = $this->organismService->getCertifierWithActiveConnectionForCertification($certification) ?? $certification->getDefaultCertifier();
            if ($this->connectionService->hasAccess($certifier, $dataProvider, true)) {
                $this->messageBus->dispatch(new SynchronizeCertificationPartners($certifInfo, $certifier->getSiret(), $dataProvider, 1));
            }
        }
    }

    /**
     * @param Connection $connection
     * @param bool $async
     * @param int $delay
     * @return bool
     * @throws TransportExceptionInterface
     */
    public function dispatchRefreshConnections(Connection $connection, bool $async = true, int $delay = 0): bool
    {
        $this->logger->debug("refresh connection: {$connection->getId()}");
        $connection->setState(ConnectionStates::REFRESHING()->getValue());
        $this->connectionService->save($connection);
        if ($async) {
            $message = new RefreshConnection($connection->getId());
            $envelope = new Envelope($message, [
                new DelayStamp($delay)
            ]);
            $this->messageBus->dispatch($envelope);
        } else {
            return $this->connectionService->refreshConnection($connection);
        }
        return true;
    }

    /**
     * @param array $organisms
     * @param int $delay
     * @return void
     */
    public function dispatchMonitoringRegistrationFolders(array $organisms, int $delay = 0)
    {
        foreach ($organisms as $organism) {
            $message = new MonitorRegistrationFolders($organism);
            $envelope = new Envelope($message, [new DelayStamp($delay)]);
            $this->messageBus->dispatch($envelope);
        }
    }

    /**
     * @param Connection $connection
     * @param Organism $organism
     * @param DataProviders $dataProvider
     * @param int $delay
     * @return void
     */
    public function dispatchSynchronizeWorkingContracts(Connection $connection, Organism $organism, DataProviders $dataProvider, int $delay = 0)
    {
        $message = new SynchronizeWorkingContracts($organism->getSiret(), $dataProvider);
        $envelope = new Envelope($message, [
            new DelayStamp($delay)
        ]);
        $this->messageBus->dispatch($envelope);
        if (!$connection->isInitialized()) {
            $this->dispatcher->dispatch(new ConnectionEvents($connection), ConnectionEvents::INITIALIZE_STARTED);
        }
    }

    //-----------------
    // METHODES PRIVEES
    //-----------------
    /**
     * @param string $certifInfo
     * @param InterfaceCertificationApiService $certificationApiService
     * @param array $updateCertificationData
     * @return SynchronizeCertification
     */
    private function getSynchronizeCertificationAndPartnersMessage(string $certifInfo, InterfaceCertificationApiService $certificationApiService, array $updateCertificationData = []): SynchronizeCertification
    {
        $nextMessages = [new SynchronizeCertifiers($certifInfo, $certificationApiService->getCertifiersDataForUpdate($certifInfo)), new SynchronizePartners($certifInfo)];
        return new SynchronizeCertification($certifInfo, $updateCertificationData, new ArrayCollection($nextMessages));
    }
}
