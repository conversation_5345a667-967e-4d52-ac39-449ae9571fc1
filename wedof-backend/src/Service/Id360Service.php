<?php

namespace App\Service;

use App\Exception\WedofBadRequestHttpException;
use App\Library\utils\enums\AttendeeGender;
use App\Library\utils\Tools;
use DateTime;
use Doctrine\ORM\NonUniqueResultException;
use Exception;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpClient\CurlHttpClient;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;

class Id360Service implements LoggerAwareInterface
{
    private static string $STATUS_OK = "OK";

    private AttendeeService $attendeeService;
    private LoggerInterface $logger;
    private CityService $cityService;

    public function __construct(AttendeeService $attendeeService, CityService $cityService)
    {
        $this->attendeeService = $attendeeService;
        $this->cityService = $cityService;
    }

    /**
     * @param LoggerInterface $logger
     */
    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    /**
     * @param string $parcours
     * @param string|null $redirectURL
     * @param string|null $clientReference
     * @param string|null $browserCallbackUrl
     * @return string
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     * @throws Exception
     */
    public function getNewEnrollmentUrl(string $parcours, string $redirectURL = null, string $clientReference = null, string $browserCallbackUrl = null): string
    {
        $baseUri = Tools::getEnvValue('WEDOF_BASE_URI');
        $processUuid = Tools::getEnvValue('ID360_' . strtoupper($parcours) . '_PROCESS_UUID', 'login');
        $redirectURL = $redirectURL ? ('?redirectURL=' . $redirectURL) : "";
        $client = $this->getHttpClient();
        $browserCallbackUrl = $browserCallbackUrl ?? (Tools::getEnvValue('ID360_' . strtoupper($parcours) . '_BROWSER_CALLBACK', false) ?? null);
        $options = [
            "headers" => [
                "Authorization: Token " . $this->getLoginToken()
            ],
            "json" => [
                "callback_url" => Tools::getEnvValue('ID360_' . strtoupper($parcours) . '_BACKEND_CALLBACK', false)
                    ? $baseUri . Tools::getEnvValue('ID360_' . strtoupper($parcours) . '_BACKEND_CALLBACK')
                    : null,
                "client_reference" => $clientReference ?? null,
                "browser_callback_url" => $browserCallbackUrl ? $baseUri . $browserCallbackUrl . $redirectURL : null,
            ]
        ];
        $response = $client->request("POST", Tools::getEnvValue('ID360_API_URL') . "/process/" . $processUuid . "/enrollment", $options);
        $apiKey = json_decode($response->getContent(), true)["api_key"];
        return Tools::getEnvValue('ID360_BASE_URL') . "/static/process_ui/index.html#/enrollment/" . $apiKey;
    }

    /**
     * @param $data
     * @return bool
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     * @throws Exception
     */
    public function callback($data): bool
    {
        //final callback
        if ($data['status'] == self::$STATUS_OK) {
            $reportId = $data['id'];
            try {
                $report = $this->getReport($reportId);
                if ($report['process_uuid'] == $_ENV['ID360_LOGIN_PROCESS_UUID']) { //process login
                    $uuid = $this->getUuid($report);
                    //try to get the married_name
                    if ($report['identity']['gender'] == 'F' && !empty($report['external_methods']['id_num']['results']['id_num_out_token'][0]['payload']['preferred_username'])) {
                        $report['identity']['married_name'] = $report['external_methods']['id_num']['results']['id_num_out_token'][0]['payload']['preferred_username'];
                    }
                    return $this->updateAttendee($report['identity'], $uuid);
                }
            } catch (Exception $e) {
                $this->logger->error($e->getTraceAsString());
            }
        }
        return false;
    }

    /**
     * @param $token
     * @return false|int
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     * @throws Exception
     */
    public function getIdFromToken($token)
    {
        try {
            $reportStatus = $this->getReportStatus($token);
            $report = $this->getReport($reportStatus['id']);
            if ($report['status'] == self::$STATUS_OK && $report['process_uuid'] == $_ENV['ID360_LOGIN_PROCESS_UUID']) { //process login
                $uuid = $this->getUuid($report);
                $attendee = $this->attendeeService->getByExternalId($uuid);
                return $attendee ? $attendee->getId() : false;
            }
        } catch (Exception $e) {
            $this->logger->error($e->getTraceAsString());
        }
        return false;
    }

    /**
     * @param $parcours
     * @param $token
     * @return array|null
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     * @throws Exception
     */
    public function getUserData($parcours, $token): ?array
    {
        $reportStatus = $this->getReportStatus($token);
        $report = $this->getReport($reportStatus['id']);
        if ($report['status'] == self::$STATUS_OK && $report['process_uuid'] == Tools::getEnvValue('ID360_' . strtoupper($parcours) . '_PROCESS_UUID')) {
            $identity = $report['identity'];
            $names = $identity['first_names'];
            $isFrance = $identity['birth_country'] === "99100";
            $cityBirth = $isFrance ? $this->cityService->getByCOG($identity['birth_place']) : null;
            $countryBirth = $isFrance ? null : Tools::findCountry($identity['birth_country']);
            return [
                'gender' => $identity['gender'] === 'M' ? 'male' : 'female',
                'firstName' => $identity['first_name'],
                'lastName' => $identity['name'],
                'firstName2' => count($names) >= 1 ? $names[1] : null,
                'firstName3' => count($names) >= 2 ? $names[2] : null,
                'dateOfBirth' => $identity['birth_date'],
                'birthName' => $identity['birth_name'],
                'codeCountryOfBirth' => !$isFrance && $countryBirth ? $countryBirth['cog'] : null,
                'nameCountryOfBirth' => !$isFrance && $countryBirth ? $countryBirth['name'] : null,
                'nameCityOfBirth' => $isFrance && $cityBirth ? $cityBirth->getName() : null,
                'codeCityOfBirth' => $isFrance && $cityBirth ? $cityBirth->getCog() : null
            ];
        } else {
            throw new WedofBadRequestHttpException("id360 error");
        }
    }

    /**
     * @param $data
     * @param $uuid
     * @return bool
     * @throws NonUniqueResultException
     * @throws \DateMalformedStringException
     * @throws Exception
     */
    private function updateAttendee($data, $uuid): bool
    {
        $attendee = $this->attendeeService->getByExternalId($uuid);
        if (!$attendee && !empty($data['email']) && !empty($data['first_names'][0]) && !empty($data['name'])) {
            $attendee = $this->attendeeService->getByRawData([
                'email' => $data['email'],
                'firstName' => $data['first_names'][0],
                'lastName' => $data['name']
            ]);
            if (!$attendee && isset($data['married_name'])) {
                $attendee = $this->attendeeService->getByRawData([
                    'email' => $data['email'],
                    'firstName' => $data['first_names'][0],
                    'lastName' => $data['married_name']
                ]);
            }
        }
        if (!$attendee && !empty($data['phone_number'])) {
            if (Tools::startsWith($data['phone_number'], '+33')) {
                $attendee = $this->attendeeService->getByPhone(str_replace('+33', '0', $data['phone_number']));
            }
            if (!$attendee) {
                $attendee = $this->attendeeService->getByPhone($data['phone_number']);
            }
        }
        if ($attendee) {
            $isFrance = $data['birth_country'] === "99100";
            if (isset($data['birth_country'])) {
                $country = Tools::findCountry(intval($data['birth_country']));
                $data['birth_country'] = $country ?? null;
            }
            if (isset($data['birth_place'])) {
                $city = $this->cityService->getByCOG($data['birth_place']);
                $data['birth_place'] = $city ?? null;
            }
            $data = [
                "externalId" => $uuid,
                "gender" => in_array(strtolower($data['gender']), ['male', 'm']) ? AttendeeGender::MALE()->getValue() : AttendeeGender::FEMALE()->getValue(),
                "firstName" => $data['first_names'][0] ?? $attendee->getFirstName(),
                "firstName2" => $data['first_names'][1] ?? $attendee->getFirstName2(),
                "firstName3" => $data['first_names'][2] ?? $attendee->getFirstName3(),
                "lastName" => isset($data['married_name']) ? strtoupper($data['married_name']) : (isset($data['name']) ? strtoupper($data['name']) : $attendee->getLastName()),
                "birthName" => isset($data['birth_name']) ? strtoupper($data['birth_name']) : (isset($data['name']) ? strtoupper($data['name']) : $attendee->getBirthName()),
                "dateOfBirth" => isset($data['birth_date']) ? new DateTime($data['birth_date']) : $attendee->getDateOfBirth(),
                "codeCountryOfBirth" => isset($data['birth_country']) ? $data['birth_country']['cog'] : $attendee->getCodeCountryOfBirth(),
                "nameCountryOfBirth" => isset($data['birth_country']) ? $data['birth_country']['name'] : $attendee->getNameCountryOfBirth(),
                "codeCityOfBirth" => $isFrance && isset($data['birth_place']) ? $data['birth_place']->getCog() : $attendee->getCodeCityOfBirth(),
                "nameCityOfBirth" => $isFrance && isset($data['birth_place']) ? $data['birth_place']->getName() : $attendee->getNameCityOfBirth()
            ];
            if (!$attendee->isEmailValidated() && isset($data['email']) && $data['email'] === $attendee->getEmail()) {
                $data["emailValidated"] = true;
            }
            if (!$attendee->isPhoneNumberValidated() && isset($data['phone_number']) && $attendee->getPhoneNumber() && $data['phone_number'] === $attendee->getPhoneNumber()) {
                $data["phoneNumberValidated"] = true;
            }
            $this->attendeeService->createOrUpdate($data, $attendee, true);
            return true;
        }
        return false;
    }

    /**
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     * @throws Exception
     */
    private function getLoginToken(): string
    {
        try {
            $client = $this->getHttpClient();
            $options = [
                "json" => [
                    "username" => $_ENV['ID360_USERNAME'],
                    "password" => $_ENV['ID360_PASSWORD'],
                ]
            ];
            $response = $client->request("POST", $_ENV['ID360_API_URL'] . "/user/login/", $options);
            return json_decode($response->getContent(), true)["token"];
        } catch (Exception $e) {
            $this->logger->error($e->getTraceAsString());
            throw new WedofBadRequestHttpException("Internal server error");
        }
    }

    /**
     * @param $reportId
     * @return mixed
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     * @throws Exception
     */
    private function getReport($reportId): array
    {
        try {
            $client = $this->getHttpClient();
            $options = [
                "headers" => [
                    "Authorization: Token " . $this->getLoginToken()
                ]
            ];
            $response = $client->request("GET", Tools::getEnvValue('ID360_API_URL') . "/enrollment/" . $reportId . "/report", $options);
            return json_decode($response->getContent(), true);
        } catch (Exception $e) {
            $this->logger->error($e->getTraceAsString());
            throw new WedofBadRequestHttpException("Internal server error");
        }
    }

    /**
     * @param $token
     * @return mixed
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     * @throws Exception
     */
    private function getReportStatus($token): array
    {
        try {
            $client = $this->getHttpClient();
            $response = $client->request("GET", Tools::getEnvValue('ID360_API_URL') . "/enrollment/status/" . $token);
            return json_decode($response->getContent(), true);
        } catch (Exception $e) {
            $this->logger->error($e->getTraceAsString());
            throw new WedofBadRequestHttpException("Internal server error");
        }
    }

    /**
     * @return CurlHttpClient
     */
    private function getHttpClient(): CurlHttpClient
    {
        $options = [
            'headers' => [
                'Content-Type: application/json',
                'Accept: application/json',
            ]
        ];
        if ($_ENV['APP_ENV'] == 'dev') {
            $options = array_merge($options,
                array(
                    "verify_peer" => false
                ));
        }
        return new CurlHttpClient($options);
    }

    /**
     * @param $report
     * @return string
     */
    private function getUuid($report): string
    {
        return $report['external_methods']['id_num']['results']['id_num_out_token'][0]['payload']['sub'];
    }
}