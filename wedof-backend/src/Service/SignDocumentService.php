<?php

namespace App\Service;

use App\Entity\User;
use App\Exception\WedofBadRequestHttpException;
use App\Library\utils\Tools;
use DateTime;
use Exception;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;

class SignDocumentService
{
    private MailerService $mailerService;
    private RequestStack $requestStack;

    public function __construct(
        MailerService $mailerService,
        RequestStack  $requestStack
    )
    {
        $this->mailerService = $mailerService;
        $this->requestStack = $requestStack;
    }

    /**
     * @param string $documentId
     * @param string $documentName
     * @param User $user
     * @return bool
     * @throws TransportExceptionInterface
     * @throws Exception
     */
    public function generateCode(string $documentId, string $documentName, User $user): bool
    {
        $session = $this->requestStack->getSession();
        $code = Tools::generateRandomString(6, true, false);
        $session->set('code_' . $documentId, $code);
        $time = new DateTime('now');
        $session->set('time_' . $documentId, $time);
        $this->mailerService->sendUserCode($user, $code, $documentName);
        return true;
    }

    /**
     * @param string $code
     * @param string $documentId
     * @return bool
     */
    public function validateCode(string $documentId, string $code): bool
    {
        $session = $this->requestStack->getSession();
        $sessionCode = $session->get('code_' . $documentId);
        $result = false;
        if ($sessionCode) {
            $maxLifeCode = $session->get('time_' . $documentId)->modify('+10 minutes')->format("Y-m-d H:i:s");
            if ((new DateTime('now'))->format("Y-m-d H:i:s") > $maxLifeCode) {
                $session->remove('time_' . $documentId);
                $session->remove('code_' . $documentId);
                throw new WedofBadRequestHttpException("Erreur, le code a expiré");
            }
            $result = $session->get('code_' . $documentId) === $code;
            if ($result) {
                $session->remove('time_' . $documentId);
                $session->remove('code_' . $documentId);
            }
        }
        return $result;
    }
}