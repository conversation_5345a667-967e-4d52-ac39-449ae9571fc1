<?php

namespace App\Service;

use App\Entity\Organism;
use App\Entity\RegistrationFolder;
use App\Entity\WorkingContract;
use App\Library\utils\enums\DataProviders;
use App\Repository\WorkingContractRepository;
use DateTime;
use Doctrine\ORM\QueryBuilder;

class WorkingContractService
{
    const UPDATABLE_PROPERTIES = ['externalIdTrainingOrganism', 'externalIdDeca', 'state', 'type', 'amount', 'startDate', 'endDate', 'signedDate', 'amendmentDate', 'breakingDate', 'rawData'];

    private WorkingContractRepository $workingContractRepository;

    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    public function __construct(WorkingContractRepository $workingContractRepository)
    {
        $this->workingContractRepository = $workingContractRepository;
    }

    /**
     * @param array $data
     * @return WorkingContract
     */
    public function create(array $data): WorkingContract
    {
        $workingContract = new WorkingContract();
        $properties = array_merge([
            'externalIdFinancer',
            'registrationFolder',
            'financer',
            'employer'
        ], self::UPDATABLE_PROPERTIES);
        foreach ($properties as $property) {
            if (key_exists($property, $data)) {
                $setMethodName = "set" . ucwords($property);
                $workingContract->{$setMethodName}($data[$property]);
            }
        }
        $workingContract->setLastUpdate(new DateTime());
        $this->workingContractRepository->save($workingContract);
        return $workingContract;
    }


    /**
     * @param WorkingContract $workingContract
     * @param array $data
     * @return WorkingContract
     */
    public function update(WorkingContract $workingContract, array $data): WorkingContract
    {
        foreach (self::UPDATABLE_PROPERTIES as $property) {
            if (key_exists($property, $data)) {
                $setMethodName = "set" . ucwords($property);
                $workingContract->{$setMethodName}($data[$property]);
            }
        }
        $workingContract->setLastUpdate(new DateTime());
        $this->workingContractRepository->save($workingContract);
        return $workingContract;
    }

    /**
     * @param Organism $organism
     * @param array $parameters
     * @return QueryBuilder
     */
    public function listReturnQueryBuilder(Organism $organism, array $parameters): QueryBuilder
    {
        return $this->workingContractRepository->findAllReturnQueryBuilder($organism, $parameters);
    }


    public function listByRegistrationFolder(RegistrationFolder $registrationFolder): array
    {
        return $this->workingContractRepository->findBy(['registrationFolder' => $registrationFolder]);
    }

    /**
     * @param array $externalIdsFinancer
     * @param DataProviders $financer
     * @param Organism $organism
     * @return array
     */
    public function listByExternalIdsFinancer(array $externalIdsFinancer, DataProviders $financer, Organism $organism): array
    {
        return $this->workingContractRepository->findAllIdsByExternalIdsFinancer($externalIdsFinancer, $financer, $organism);
    }

    /**
     * @param int $id
     * @return WorkingContract
     */
    public function getById(int $id): ?WorkingContract
    {
        return $this->workingContractRepository->find($id);
    }
}
