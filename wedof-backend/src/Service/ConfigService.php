<?php

namespace App\Service;

use App\Entity\Config;
use App\Library\utils\enums\DataProviders;
use App\Repository\ConfigRepository;
use App\Service\DataProviders\CpfApiService;
use App\Service\DataProviders\BaseApiService;
use DateTime;
use DateTimeZone;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use Exception;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ContainerInterface as Container;
use Throwable;

class ConfigService implements LoggerAwareInterface
{
    private LoggerInterface $logger;

    public function setLogger(LoggerInterface $logger)
    {
        $this->logger = $logger;
    }

    private ConfigRepository $configRepository;
    private Container $container;
    private MailerService $mailerService;
    private array $workersIps;

    public function __construct(ConfigRepository $configRepository, Container $container, MailerService $mailerService)
    {
        $this->configRepository = $configRepository;
        $this->container = $container;
        $this->mailerService = $mailerService;
        $this->workersIps = explode(',', $_ENV['WORKERS']);
    }

    //-------------------
    // METHODES PUBLIQUES
    //-------------------
    /**
     * @param bool $refresh
     * @return Config
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function getConfig(bool $refresh = false): Config
    {
        $config = $this->configRepository->findConfig();
        if ($refresh) {
            $config = $this->refreshSessionMinDates($config);
        }
        return $config;
    }

    /**
     * @param Config $config
     * @return Config
     */
    public function save(Config $config): Config
    {
        return $this->configRepository->save($config);
    }

    //----------------
    // METHODES PRIVES
    //----------------
    /**
     * @param Config $config
     * @return Config
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     * @throws ORMException
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    private function refreshSessionMinDates(Config $config): Config
    {
        if ($_ENV['APP_ENV'] === 'prod') {
            /** @var RegistrationFolderService $registrationFolderService */
            $registrationFolderService = $this->container->get('App\Service\RegistrationFolderService');
            /** @var CpfApiService $cpfApiService */
            $cpfApiService = BaseApiService::getApiServiceByDataProvider(DataProviders::CPF());
            try {
                // En attendant que la date soit correcte côté apprenant
                $registrationFolder = $registrationFolderService->getLastUpdatedNotProcessed();
                $registrationFolderService->refresh($registrationFolder);
                $config->setCpfSessionMinDate((new DateTime($registrationFolder->getRawData()['minDate']))->setTimezone(new DateTimeZone('Europe/Paris')));

                // TODO A réactiver lorsque la date côté apprenant sera OK et supprimer la façon de faire précédente
//                $data = $cpfApiService->sendRequestAsAttendee($_ENV['CPF_ATTENDEE_VB_LOGIN'], $_ENV['CPF_ATTENDEE_VB_PASSWORD'], 'GET', '/private/dossier/' . $_ENV['CPF_ATTENDEE_VB_SESSION_MIN_DATE']);
//                $cpfSessionMinDate = (new DateTime($data['dateMinimaleEntreeFormation']))->setTimezone(new DateTimeZone('Europe/Paris'));
//                $config->setCpfSessionMinDate(date('N', $cpfSessionMinDate->getTimestamp()) <= 5 ? $cpfSessionMinDate : $cpfSessionMinDate->modify('+1 weekday')); // si le weekend on modifie pour le prochain jour ouvré

                $config->setPoleEmploiSessionMinDate($cpfApiService->getPoleEmploiSessionMinDate($_ENV['SESSION_MIN_DATE_TRAINING_ACTION'])->setTimezone(new DateTimeZone('Europe/Paris')));
            } catch (Exception $exception) {
                if ($exception->getCode() === 404) {
                    $this->logger->error($exception->getMessage());
                    $this->mailerService->sendAlertOnSessionMinDates();
                } else {
                    throw $exception;
                }
            }
        } else {
            $config->setCpfSessionMinDate(new DateTime('+14 days'));
            $config->setPoleEmploiSessionMinDate(new DateTime('+ 28 days'));
        }
        $config->setLastUpdate(new DateTime());
        $this->configRepository->save($config);

        return $config;
    }

    /**
     * @return array|false|string[]
     */
    public function getWorkersIps()
    {
        return $this->workersIps;
    }
}