<?php
// src/Service/StatisticService.php
namespace App\Service;

use App\Entity\Organism;
use App\Entity\Training;
use App\Exception\WedofBadRequestHttpException;
use App\Library\utils\enums\RegistrationFolderStates;
use App\Repository\EvaluationRepository;
use App\Repository\RegistrationFolderRepository;
use DateTime;

class StatisticService
{
    private RegistrationFolderRepository $registrationFolderRepository;
    private EvaluationRepository $evaluationRepository;

    public function __construct(RegistrationFolderRepository $registrationFolderRepository, EvaluationRepository $evaluationRepository)
    {
        $this->evaluationRepository = $evaluationRepository;
        $this->registrationFolderRepository = $registrationFolderRepository;
    }

    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    /**
     * @param string $dataType
     * @param Training|Organism $object
     * @param DateTime $startDate
     * @param DateTime $endDate
     * @param array $options
     * @return array
     */
    public function dataGroupByDates(string $dataType, $object, DateTime $startDate, DateTime $endDate, array $options = []): array
    {
        $paramsFromDates = $this->getParamsFromDates($startDate, $endDate);
        switch ($dataType) {

            case "registrationFolders":
                $repository = $this->registrationFolderRepository;
                $methodName = "computeOnField";
                $function = isset($options['function']) && in_array($options['function'], ['sum', 'average', 'count']) ? $options['function'] : 'count';
                $field = isset($options['field']) && in_array($options['field'], ['amountToInvoice', 'id', 'trainingActionInfo.totalExcl']) ? $options['field'] : 'id';
                $billingStates = !empty($options['billingState']) ? explode(',', $options['billingState']) : array();
                $states = $billingStates ? [RegistrationFolderStates::SERVICE_DONE_VALIDATED()] : (!empty($options['state']) ? explode(',', $options['state']) : array());
                $args = [
                    $object,
                    $function,
                    $field,
                    $paramsFromDates['format'],
                    $startDate,
                    $endDate,
                    $states,
                    $billingStates
                ];
                break;
            /*case "evaluations":
                $repository = $this->evaluationRepository;
                $mode = isset($options['mode']) && in_array($options['mode'], ['count', 'average']) ? $options['mode'] : 'count';
                $methodName = "${mode}Evaluations";
                break;*/
            default:
                throw new WedofBadRequestHttpException("Erreur, donnée inconnue, valeur possible : registrationFolders.");
        }
        $data = $repository ? call_user_func_array(array($repository, "${methodName}GroupByDates"), $args) : [];
        return $this->fillGapData($paramsFromDates['intervals'], $data);
    }

    /**
     * @param string $dataType
     * @param $object
     * @param DateTime $startDate
     * @param DateTime $endDate
     * @param array $options
     * @return array|int
     */
    public function dataBetweenDates(string $dataType, $object, DateTime $startDate, DateTime $endDate, array $options = [])
    {
        $args = [$object, $startDate, $endDate];
        switch ($dataType) {
            case "registrationFolders":
                $repository = $this->registrationFolderRepository;
                $methodName = "computeOnField";
                $function = isset($options['function']) && in_array($options['function'], ['sum', 'average', 'count']) ? $options['function'] : 'count';
                $field = isset($options['field']) && in_array($options['field'], ['amountToInvoice', 'id']) ? $options['field'] : 'id';
                $billingStates = !empty($options['billingState']) ? explode(',', $options['billingState']) : array();
                $states = $billingStates ? [RegistrationFolderStates::SERVICE_DONE_VALIDATED()] : (!empty($options['state']) ? explode(',', $options['state']) : array());
                $args = [
                    $object,
                    $function,
                    $field,
                    $startDate,
                    $endDate,
                    $states,
                    $billingStates
                ];
                break;
            case "evaluations":
                $repository = $this->evaluationRepository;
                $mode = isset($options['mode']) && in_array($options['mode'], ['count', 'average']) ? $options['mode'] : 'count';
                $methodName = "${mode}Evaluations";
                break;
            default:
                throw new WedofBadRequestHttpException("Erreur, donnée inconnue, valeur possible : registrationFolders, evaluations.");
        }
        return $repository ? call_user_func_array(array($repository, "${methodName}BetweenDates"), $args) : 0;
    }

    //-----------------
    // METHODES PRIVEES
    //-----------------

    /**
     * @param array $intervals
     * @param array $data
     * @return array
     */
    private function fillGapData(array $intervals, array $data): array
    {
        foreach ($data as $interval_data) {
            foreach ($intervals as &$interval) {
                if ($interval_data['label'] == $interval['label']) {
                    $interval['value'] = intval($interval_data['value']);
                }
            }
        }
        return $intervals;
    }

    /**
     * @param DateTime $startDate
     * @param DateTime $endDate
     * @return array
     */
    private function getParamsFromDates(DateTime $startDate, DateTime $endDate): array
    {
        $intervalDate = clone $startDate;
        $intervals = [];
        $diff = $startDate->diff($endDate);
        $groupByFct = $diff->days <= 1 ? "HOUR" : ($diff->days <= 30 ? "DAY" : ($diff->days <= 180 ? "WEEK" : ($diff->y <= 4 ? "MONTH" : "YEAR")));

        switch ($groupByFct) {
            case "HOUR":
                $format = "%Hh";
                for ($i = 0; $i <= $diff->h; $i++) {
                    array_push($intervals, ['label' => $intervalDate->format('H\h'), 'value' => 0]);
                    $intervalDate->modify("+ 1 hour");
                }
                break;
            case "DAY":
                $format = "%d/%m";
                for ($i = 0; $i <= $diff->days; $i++) {
                    array_push($intervals, ['label' => $intervalDate->format('d/m'), 'value' => 0]);
                    $intervalDate->modify("+ 1 day");
                }
                break;
            case "WEEK":
                $format = "S%u";
                while ($intervalDate->format('Y-W') <= $endDate->format('Y-W')) { // Use format rather than interval to be sure to include the current week
                    array_push($intervals, ['label' => $intervalDate->format('\SW'), 'value' => 0]);
                    $intervalDate->modify("+ 7 days");
                }
                break;
            case "MONTH":
                $format = "%M %Y";
                while ($intervalDate->format('Y-m') <= $endDate->format('Y-m')) { // Use format rather than interval to be sure to include the current month
                    array_push($intervals, ['label' => $intervalDate->format('F Y'), 'value' => 0]);
                    $intervalDate->modify("+ 1 month");
                }
                break;
            default:
                $format = "%Y";
                while ($intervalDate->format('Y') <= $endDate->format('Y')) { // Use format rather than interval to be sure to include the current year
                    array_push($intervals, ['label' => $intervalDate->format('Y'), 'value' => 0]);
                    $intervalDate->modify("+ 1 year");
                }
                break;
        }
        return ['groupBy' => $groupByFct, 'format' => $format, 'intervals' => $intervals, 'diffDays' => $diff->days];
    }
}
