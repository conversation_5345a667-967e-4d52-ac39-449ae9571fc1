<?php
// src/Service/OrganismService.php
namespace App\Service;

use App\Application\MessageTemplates\Repository\MessageTemplateRepository;
use App\Application\MessageTemplates\Service\MessageTemplateService;
use App\Entity\Certification;
use App\Entity\Organism;
use App\Entity\Session;
use App\Entity\TrainingAction;
use App\Entity\User;
use App\Event\Organism\OrganismEvents;
use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofConnectionException;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\enums\OrganismApplicantType;
use App\Library\utils\Tools;
use App\Message\SynchronizeCatalog;
use App\Repository\OrganismRepository;
use App\Service\DataProviders\AutomatorApiService;
use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\QueryBuilder;
use ErrorException;
use Exception;
use Psr\EventDispatcher\EventDispatcherInterface;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use simplehtmldom\HtmlDocument;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\HttpFoundation\File\File;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Throwable;
use Vich\UploaderBundle\Storage\StorageInterface;

class OrganismService implements LoggerAwareInterface
{
    private LoggerInterface $logger;
    private EventDispatcherInterface $dispatcher;
    private OrganismRepository $organismRepository;
    private ConnectionService $connectionService;
    private AwsService $awsService;
    private MessageBusInterface $messageBus;
    private MessageTemplateRepository $messageTemplateRepository;
    private ContainerInterface $container;
    private StorageInterface $storage;

    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    public function __construct(OrganismRepository $organismRepository, ConnectionService $connectionService, AwsService $awsService, EventDispatcherInterface $dispatcher, MessageTemplateRepository $messageTemplateRepository, ContainerInterface $container, MessageBusInterface $messageBus, StorageInterface $storage)
    {
        $this->dispatcher = $dispatcher;
        $this->organismRepository = $organismRepository;
        $this->connectionService = $connectionService;
        $this->awsService = $awsService;
        $this->messageTemplateRepository = $messageTemplateRepository;
        $this->container = $container;
        $this->messageBus = $messageBus;
        $this->storage = $storage;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    /**
     * @param int|null $offset
     * @param int|null $limit
     * @return ArrayCollection
     */
    public function listToSync(int $offset = null, int $limit = null): ArrayCollection
    {
        return $this->organismRepository->findByToSync($offset, $limit);
    }

    /**
     * @param array $params
     * @param false $refresh
     * @return Organism|null
     * @throws Throwable
     */
    public function getOrganism(array $params, bool $refresh = false): ?Organism
    {
        $params = array_merge(['createIfNotExist' => true], $params);
        /** @var Organism $organism */
        $searchParams = isset($params['siret']) ? ['siret' => $params['siret']] : (isset($params['subDomain']) ? ['subDomain' => $params['subDomain']] : (isset($params['name']) ? ['name' => $params['name']] : null));
        $organism = $searchParams ? $this->organismRepository->findOneByCriteria($searchParams) : null;
        if (((!$organism && $params['createIfNotExist']) || $refresh) && (isset($params['siret']) || isset($params['name']))) {
            $this->logger->info("[getOrganism][" . ($params['siret'] ?? $params['name']) . "] Organism doesn't exist or refresh");
            $rawData = $this->getOrganismData($params);
            return $rawData ? $this->createOrUpdate($rawData) : ($organism ?? null);
        } else if (!$organism) {
            return null;
        }
        return $organism;
    }

    /**
     * @param array $rawData
     * @param Organism|null $organism
     * @param bool $changeOriginUser
     * @return Organism|null
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    public function createOrUpdate(array $rawData, Organism $organism = null, bool $changeOriginUser = false): ?Organism
    {
        $create = false;
        if (isset($rawData['siret']) || $organism != null) {
            if (empty($organism)) $organism = $this->organismRepository->findOneBySiret($rawData['siret']);
            if ($organism === null) {
                if ($rawData['siret'] != '00000000000000') {
                    $create = true;
                    $this->logger->info("[createOrUpdateOrganism][" . $rawData['siret'] . "] organism doesn't exist create it");
                    $organism = new Organism();
                    $organism->setSiren(!empty($rawData['siren']) ? $rawData['siren'] : substr($rawData['siret'], 0, 9));
                    $organism->setSiret($rawData['siret']);
                    $organism->setToSync(1);
                    $organism->setCustomizedInfos(false);
                } else {
                    return null;
                }
            } else {
                $organism->setToSync(0);
            }
            if (isset($rawData['name_siret'])) {
                $organism->setNameSiret($rawData['name_siret']);
            }
            if (isset($rawData['name'])) {
                $organism->setName($rawData['name']);
            } else if (!$organism->getName()) {
                $organism->setName($rawData['name_siret'] ?? "");
            }
            if (!$organism->hasCustomizedInfos() || $changeOriginUser) {
                if (isset($rawData['emails'])) {
                    $organism->setEmails(array_filter($rawData['emails'], function ($value) {
                        return !empty($value);
                    }));
                }
                if (isset($rawData['phones'])) {
                    $organism->setPhones(array_filter($rawData['phones'], function ($value) {
                        return !empty($value);
                    }));
                }
                if (isset($rawData['urls'])) {
                    $organism->setUrls(array_filter($rawData['urls'], function ($value) {
                        return !empty($value);
                    }));
                }
                if (isset($rawData['address'])) {
                    $organism->setAddress($rawData['address']);
                } else if (!$organism->getAddress()) {
                    $organism->setAddress(""); //not nullable constrainte
                }
                if (isset($rawData['city'])) {
                    $organism->setCity($rawData['city']);
                } else if (!$organism->getCity()) {
                    $organism->setCity(""); //not nullable constrainte
                }
                if (isset($rawData['postalCode'])) {
                    $organism->setPostalCode($rawData['postalCode']);
                } else if (!$organism->getPostalCode()) {
                    $organism->setPostalCode(""); //not nullable constrainte
                }
                if (isset($rawData['agreement'])) {
                    $organism->setAgreement($rawData['agreement']);
                }
                if (isset($rawData['vat'])) {
                    $organism->setVat($rawData['vat']);
                }
                if (isset($rawData['cdcClientId'])) {
                    $organism->setCdcClientId($rawData['cdcClientId']);
                }
                if (isset($rawData['cdcContractId'])) {
                    $organism->setCdcContractId($rawData['cdcContractId']);
                }
                if (isset($rawData['accrochageDelegationDate'])) {
                    $organism->setAccrochageDelegationDate($rawData['accrochageDelegationDate']);
                }
                if ($organism->isCertifierOrganism() && empty($organism->getApplicantType())) {
                    $organism->setApplicantType(OrganismApplicantType::CERTIFICATEUR()->getValue());
                }
            }
            if (isset($rawData['latitude'])) {
                $organism->setLatitude($rawData['latitude']);
            }
            if (isset($rawData['longitude'])) {
                $organism->setLongitude($rawData['longitude']);
            }
            if (isset($rawData['ape'])) {
                $organism->setApe($rawData['ape']);
            }
            if (isset($rawData['reseller']) && $organism->getReseller() === null) {
                $organism->setReseller($rawData['reseller']);
            }
            if ($changeOriginUser) {
                $organism->setCustomizedInfos(true);
            }
            if (isset($rawData['uaiNumber'])) {
                $organism->setUaiNumber($rawData['uaiNumber']);
            }
            if (isset($rawData['billingSoftware'])) {
                $organism->setBillingSoftware($rawData['billingSoftware']);
            }
            if (isset($rawData['crm'])) {
                $organism->setCrm($rawData['crm']);
            }
            if (isset($rawData["linkedInPageUrl"])) {
                $rawData['linkedInPageUrl'] = explode('?', $rawData['linkedInPageUrl'])[0];
                $organism->setLinkedInPageUrl($rawData['linkedInPageUrl']);
                if ($rawData['linkedInPageUrl'] != null) {
                    try {
                        $automatorApiService = $this->getAutomatorService();
                        $data = $automatorApiService->getLinkedInPageUrlInfos($organism);
                        if (!empty($data['organisationId'])) {
                            $organism->setLinkedInOrganisationId($data['organisationId']);
                        }
                    } catch (Exception $e) {
                    }
                } else {
                    $organism->setLinkedInOrganisationId(null);
                }
            }
            if (isset($rawData["customColorScheme"])) {
                $organism->setCustomColorScheme($rawData['customColorScheme']);
            }
            if (isset($rawData['logo']) && $rawData['logo'] instanceof File) {
                $organism->setLogoFile($rawData['logo']);
            } else if (array_key_exists('logo', $rawData) && $rawData['logo'] === null) {
                //It will not delete the file in the filesystem
                //It should be done by something else (someone?)
                $organism->setLogoFile();
                $organism->setLogoName(null);
                $organism->setLastUpdate(new DateTime());
            }
            if (!$organism->getSubDomain()) {
                $computedSubDomain = $this->computeSubdomainForOrganism($organism);
                $subDomainAlreadyTaken = !empty($this->organismRepository->findOneBy(["subDomain" => $computedSubDomain]));
                if (!$subDomainAlreadyTaken) {
                    $organism->setSubDomain($computedSubDomain);
                }
            }
            if (isset($rawData["metadata"])) {
                $organism->setMetadata(empty($rawData['metadata']) ? null : Tools::removeSpacesInKeys($rawData['metadata']));
            }

            $this->checkDataProvidersExistForOrganism($organism);

            if ($organism->getAgreement() === null) {
                try {
                    $_data = $this->getAutomatorService()->getExtraDataOrganism($organism->getSiret());
                    if (!empty($_data['nda'])) {
                        $organism->setAgreement($_data['nda']);
                    }
                } catch (Exception $e) {
                    $this->logger->error('Automator or recherche-entreprises.api.gouv.fr seems offline ' . $organism->getSiret());
                }
            }

            if (!$organism->isTrainingOrganism() && $organism->getAgreement()) {
                $organism->setTrainingOrganism(true);
            }

            $organism->setLastUpdate(new DateTime());
            $this->organismRepository->save($organism);
            if ($create) {
                $this->dispatcher->dispatch(new OrganismEvents($organism), OrganismEvents::CREATED);
            }
            return $organism;
        } else {
            return null;
        }
    }

    /**
     * @param array $data
     * @return array|null
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    public function getOrganismData(array $data): ?array
    {
        //grab data from Siren
        if (!empty($data['siret'])) {
            $data = $this->fetchDataFromAPISireneWithSiret($data);
            if (!$data) {
                return null;
            }
        } else if ($data['name']) {
            $siret = $this->getSiretFromAPISirene($data['name']);
            $siret = $siret ?: $this->getSiretFromSocieteCom($data['name']);
            if ($siret) {
                $data['siret'] = $siret;
                $data = $this->fetchDataFromAPISireneWithSiret($data);
                if (!$data) {
                    return null;
                }
            }
        }

        //grab data from carif then if we have empty fields...
        $term = !empty($data['siret']) ? $data['siret'] : $data['name'];
        try {
            $tr = $this->organismExistInCarif($term);
            if (!$tr && empty($data['siret'])) { //give another try
                $term = str_replace('MADAME ', '', $term);
                $term = str_replace('MONSIEUR ', '', $term);
                $tr = $this->organismExistInCarif($term);
            }
            if ($tr) {
                $data = $this->fetchDataFromCarif($tr, $data);
            }
        } catch (Exception $e) {
            $this->logger->error('Carif seems offline when grabbing data for ' . $term);
        }
        if (!isset($data['name']) && isset($data['name_siret'])) {
            $data['name'] = $data['name_siret'];
        }
        return $data;
    }

    /**
     * @param Organism $organism
     * @return Organism|null
     */
    public function addApeToOrganism(Organism $organism): ?Organism
    {
        $data = $this->fetchDataFromAPISireneWithSiret(["siret" => $organism->getSiret()]);
        if (!$data || !isset($data['ape'])) {
            return null;
        }

        $organism->setApe($data['ape']);

        return $this->save($organism);
    }

    /**
     * @param string $siret
     * @return Organism|null
     */
    public function getBySiret(string $siret): ?Organism
    {
        return $this->organismRepository->findOneBySiret($siret);
    }

    /**
     * @param array $params
     * @return ArrayCollection
     */
    public function listWithOwnedBy(array $params = []): ArrayCollection
    {
        return $this->organismRepository->findAllWithOwnedBy($params);
    }

    /**
     * @param array $params
     * @return ArrayCollection
     */
    public function listBySubscriptionType(array $params): ArrayCollection
    {
        return $this->organismRepository->findAllBySubscriptionType($params);
    }

    public function listBySubscriptionAllowCertifierPlusAndCdcCertifierConnection(): ArrayCollection
    {
        $organisms = $this->organismRepository->findAllBySubscriptionAllowCertifierPlusAndCdcCertifierConnection();
        $ids = $organisms->map(function ($organism) {
            return $organism->getName() . ' (' . $organism->getId() . ')';
        });
        $this->logger->error("List organisms for automatic XML CDC accrochage:");
        $this->logger->error(print_r($ids, true));
        return $organisms;
    }

    /**
     * @param Organism $organism
     * @return Organism
     */
    public function save(Organism $organism): Organism
    {
        return $this->organismRepository->save($organism);
    }

    /**
     * @param $object
     * @return Organism|null
     * @throws Throwable
     */
    public function getOrganismFromEntity($object): ?Organism
    {
        $organism = null;
        if ($object instanceof Session) {
            $organism = $object->getTrainingAction()->getTraining()->getOrganism();
        } else if ($object instanceof TrainingAction) {
            $organism = $object->getTraining()->getOrganism();
        }
        return $organism;
    }

    /**
     * @param string $method
     * @param string $url
     * @param array $params
     * @param int $maxRetries
     * @return string|null
     */
    public function sendCarifRequest(string $method, string $url, array $params = [], int $maxRetries = 2): ?string
    {
        $try = 1;
        $options = [
            'timeout' => Tools::getEnvValue('HTTP_CLIENT_TIMEOUT'),
            'headers' => [
                'User-Agent' => "'Mozilla/5.0 (Windows; U; Windows NT 6.1; en-US)AppleWebKit/534.10 (KHTML, like Gecko) Chrome/8.0.552.224 Safari/534.10'"
            ]
        ];
        if ($params) {
            if ($method == "GET") {
                $options['query'] = $params;
            } else {
                $options['body'] = $params;
            }
        }
        while ($try <= $maxRetries) {
            $statusCode = null;
            try {
                $response = Tools::getHttpClient()->request($method, Tools::getEnvValue('CARIFOREF_BASE_URL') . $url, $options);
                $statusCode = $response->getStatusCode();
                return $response->getContent();
            } catch (Throwable $e) {
                if ($statusCode == 404) {
                    return null; //no results
                }
                $this->logger->error($e->getMessage());
                $this->logger->error($e->getTraceAsString());
                $this->logger->info("[sendRequestCarif] sleep for 60sec $try/$maxRetries");
                $try += 1;
                if ($try <= $maxRetries) {
                    sleep(60);
                }
            }
        }
        return null;
    }

    /**
     * @return Organism[]
     */
    public function list($limit, $offset): array
    {
        return $this->organismRepository->findBy([], null, $limit, $offset);
    }

    /**
     * @param array $parameters
     * @return QueryBuilder
     */
    public function listReturnQueryBuilder(array $parameters): QueryBuilder
    {
        return $this->organismRepository->findAllReturnQueryBuilder($parameters);
    }

    /**
     * @param Organism $organism
     * @param array $parameters
     * @return QueryBuilder
     */
    public function listInPartnershipWith(Organism $organism, array $parameters): QueryBuilder
    {
        return $this->organismRepository->findAllInPartnershipWith($organism, $parameters);
    }

    /**
     * @param string $appId
     * @return ArrayCollection
     */
    public function listAllUsingApp(string $appId): ArrayCollection
    {
        return $this->organismRepository->findAllUsingApp($appId);
    }


    /**
     * @param Organism $organism
     * @param array $parameters
     * @return QueryBuilder
     */
    public function listPotentialCertificationFolderHolders(Organism $organism, array $parameters): QueryBuilder
    {
        return $this->organismRepository->findAllPotentialCertificationFolderHolders($organism, $parameters);
    }

    /**
     * @return ArrayCollection
     */
    public function listCpfCatalogUploadInProgress(): ArrayCollection
    {
        return $this->organismRepository->findAllUploadCatalogInProgress();
    }

    /**
     * @param Organism $organism
     * @return string
     */
    public function computeSubdomainForOrganism(Organism $organism): string
    {
        if (!$organism->getSubDomain()) {
            $subdomain = null;
            if (sizeof($organism->getEmails()) > 0) {
                $subdomain = substr($organism->getEmails()[0], strpos($organism->getEmails()[0], '@') + 1);
                $subdomain = explode('.', $subdomain)[0];
                if ($this->organismRepository->findBy(['subDomain' => $subdomain])) {
                    $subdomain = null;
                }
            }
            if (!$subdomain && $organism->getName()) {
                $subdomain = $organism->getName();
                if ($this->organismRepository->findBy(['subDomain' => $subdomain])) {
                    $subdomain = null;
                }
            }
            if (!$subdomain && $organism->getNameSiret()) {
                $subdomain = $organism->getNameSiret();
                if ($this->organismRepository->findBy(['subDomain' => $subdomain])) {
                    $subdomain = null;
                }
            }
            if (!$subdomain) {
                $subdomain = $organism->getSiret();
            }
            return preg_replace('/[^a-z0-9\-_]/', '', strtolower($subdomain));
        } else {
            return $organism->getSubDomain();
        }
    }

    /**
     * @param Certification $certification
     * @return Organism|null
     */
    public function getCertifierWithActiveConnectionForCertification(Certification $certification): ?Organism
    {
        return $this->organismRepository->findCertifierWithActiveConnectionForCertification($certification);
    }

    /**
     * @param Organism $organism
     * @param string $email
     * @return void
     */
    public function verifySendAs(Organism $organism, string $email): void
    {
        if (!in_array($email, $organism->getEmails())) {
            throw new WedofBadRequestHttpException("Erreur, seul un email de l'organisme peut être ajouté comme expéditeur");
        }
        if ($this->isSendAsEmail($organism, $email)) {
            throw new WedofBadRequestHttpException("Erreur, l'adresse email est déjà un expéditeur");
        }
        $response = $this->awsService->verifyEmailIdentity($email);
        if ($response->get('@metadata')['statusCode'] !== 200) {
            throw new WedofBadRequestHttpException("Une erreur inconnue s'est produite, contactez le support Wedof");
        }
        $organism->setSendAs([]);
        $this->save($organism);
    }

    /**
     * @param Organism $organism
     * @param string $email
     * @return void
     */
    public function deleteSendAs(Organism $organism, string $email): void
    {
        if (!$this->isSendAsEmail($organism, $email)) {
            throw new WedofBadRequestHttpException("Erreur, cette adresse n'est pas un expéditeur de l'organisme");
        }
        $response = $this->awsService->deleteEmailIdentity($email);
        if ($response->get('@metadata')['statusCode'] !== 200) {
            throw new WedofBadRequestHttpException("Une erreur inconnue s'est produite, contactez le support Wedof");
        }
        $organism->setSendAs(null);
        $messageTemplates = $this->messageTemplateRepository->findBy([
            'organism' => $organism,
            'sendAs' => $email
        ]);
        $messageTemplateService = $this->container->get(MessageTemplateService::class);
        foreach ($messageTemplates as $messageTemplate) {
            $messageTemplateService->update($messageTemplate, ['sendAs' => null]);
        }
        $this->save($organism);
    }

    /**
     * @param Organism $organism
     * @param string $email
     * @return bool
     */
    public function isSendAsEmail(Organism $organism, string $email): bool
    {
        // petite siouxerie : si on a le sendAsName pour un email ça veut dire que l'email est bien enregistré comme sendAs
        return $organism->getSendAsName($email) !== null;
    }

    /**
     * @return string|null
     */
    public function verifySendAsSuccess(): ?string
    {
        $organisms = $this->organismRepository->findAllPendingSendAsEmail();
        /** @var string[] $pendingSendAsEmails */
        $pendingSendAsEmails = $organisms->map(function (Organism $organism) {
            return $organism->getEmails()[0];
        })->toArray();
        $newSendAsEmails = $this->awsService->listNewEmailIdentities($pendingSendAsEmails);

        foreach ($newSendAsEmails as $newSendAsEmail) {
            /** @var Organism $organism */
            $organism = $organisms->filter(
                fn(Organism $organism) => $organism->getEmails()[0] === $newSendAsEmail
            )->first();
            $this->addVerifiedSendAsEmail($organism, $newSendAsEmail);
        }

        return count($newSendAsEmails) === 1 ? $newSendAsEmails[0] : null;
    }

    /**
     * @param Organism $organism
     */
    public function markAsTrainingOrganism(Organism $organism): void
    {
        $organism->setTrainingOrganism(true);
        $this->save($organism);
    }

    /**
     * @param Organism $organism
     * @param DataProviders $dataProvider
     */
    public function refreshCatalog(Organism $organism, DataProviders $dataProvider): void
    {
        $message = new SynchronizeCatalog($organism->getSiret(), $dataProvider, null, [], false, ['daysMaxSinceLastUpdate' => 0, 'fullSessions' => true]);
        $this->messageBus->dispatch($message);
    }

    /**
     * @param Organism $organism
     * @param User $user
     * @param array $body
     * @return Organism
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     * @throws ORMException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws OptimisticLockException
     * @throws ErrorException
     */
    public function importCertificationFolders(Organism $organism, User $user, array $body): Organism
    {
        $organism->setAllowImportCertificationFolders(false);
        $this->save($organism);
        $automatorApiService = $this->getAutomatorService();
        $automatorApiService->importCertificationFolders($organism, $user, $body);
        return $organism;
    }

    /**
     * @param Organism $organism
     * @return array
     */
    public function getCpfCatalogUploadStatus(Organism $organism): array
    {
        $cpfCatalogMetadata = $organism->getCpfCatalogMetadata();
        $uploadStatus = $cpfCatalogMetadata['upload'] ?? null;
        if ($uploadStatus && $uploadStatus['state'] === 'done' && $organism->getCpfCatalogUploadReportName()) {
            $realFilePath = $this->storage->resolvePath($organism, 'cpfCatalogUploadReport');
            $uploadStatus['report'] = Tools::csvToArray(file_get_contents($realFilePath), ';', true);
        }
        return $uploadStatus;
    }

    //----------------
    // METHODES PRIVES
    //----------------

    /**
     * @param $tr
     * @param array $data
     * @return array
     */
    private function fetchDataFromCarif($tr, array $data): array
    {
        //the result page grab first result
        $cells = $tr->find('td');
        $data['name_siret'] = $cells[2]->plaintext;

        if (empty($data['phones'])) {
            $data['phones'] = [];
        }
        if (!in_array($cells[8]->plaintext, $data['phones'])) {
            $data['phones'][] = str_replace(" ", "", $cells[8]->plaintext);
        }
        if (empty($data['emails'])) {
            $data['emails'] = [];
        }
        if (!in_array($cells[9]->plaintext, $data['emails'])) {
            $data['emails'][] = str_replace(" ", "", $cells[9]->plaintext);
        }

        if (empty($data['city'])) {
            $data['city'] = trim($cells[3]->plaintext);
        }
        if (empty($data['postalCode'])) {
            $data['postalCode'] = trim($cells[4]->plaintext);
        }
        if (empty($data['address'])) {
            $data['address'] = trim($cells[9]->plaintext);
        }
        if (empty($data['urls'])) {
            $data['urls'] = [];
        }
        if (!in_array($cells[10]->plaintext, $data['urls'])) {
            $data['urls'][] = str_replace(" ", "", $cells[10]->plaintext);
        }
        if (empty($data['latitude'])) {
            $data['latitude'] = trim($cells[11]->plaintext);
        }
        if (empty($data['longitude'])) {
            $data['longitude'] = trim($cells[12]->plaintext);
        }
        if (empty($data['agreement'])) {
            //the details page
            $content = $this->sendCarifRequest("GET", $cells[1]->plaintext);
            $html = new HtmlDocument();
            $html->load($content);
            $t = $html->find('.container .col-sm-8 h4', 0);
            if ($t != null && $t->next_sibling() != null && $t->next_sibling()->next_sibling() != null) {
                $data['agreement'] = str_replace(" ", "", preg_replace("/[^0-9]/", "", $t->next_sibling()->next_sibling()->plaintext));
            }
        }
        return $data;
    }

    /**
     * @param array $data
     * @return array|null
     */
    private function fetchDataFromAPISireneWithSiret(array $data): ?array
    {
        $this->logger->info("[SearchSireneAPI][" . $data['siret'] . "] Searching for organism on Sirene");
        $content = $this->sendSireneRequest('siret/' . $data['siret']);
        if ($content) {
            $this->logger->info("[SearchSireneAPI][" . $data['siret'] . "] found organism");
            $responseData = json_decode($content);
            if ($responseData->etablissement) {
                $responseData = $responseData->etablissement;
                if (empty($data['siren'])) {
                    $data['siren'] = str_replace(" ", "", $responseData->siren);
                }
                if (empty($data['name_siret'])) {
                    $data['name_siret'] = !empty(trim($responseData->periodesEtablissement[0]->denominationUsuelleEtablissement)) ? trim($responseData->periodesEtablissement[0]->denominationUsuelleEtablissement) : trim($responseData->uniteLegale->denominationUniteLegale);
                }
                if (empty($data['name'])) {
                    $data['name_siret'] = !empty(trim($responseData->periodesEtablissement[0]->denominationUsuelleEtablissement)) ? trim($responseData->periodesEtablissement[0]->denominationUsuelleEtablissement) : trim($responseData->uniteLegale->denominationUniteLegale);
                }
                if (empty($data['name_siret']) && $responseData->uniteLegale->categorieJuridiqueUniteLegale === "1000") {
                    $data['name_siret'] = !empty(trim($responseData->periodesEtablissement[0]->enseigne1Etablissement)) ? trim($responseData->periodesEtablissement[0]->enseigne1Etablissement) :
                        ($responseData->uniteLegale->sexeUniteLegale === 'F' ? 'MADAME' : 'MONSIEUR') . " " . trim($responseData->uniteLegale->prenom1UniteLegale) . " " . trim($responseData->uniteLegale->nomUniteLegale);
                }
                if (empty($data['address'])) {
                    $data['address'] = "";
                    if ($responseData->adresseEtablissement->numeroVoieEtablissement) {
                        $data['address'] .= " " . trim($responseData->adresseEtablissement->numeroVoieEtablissement);
                    }
                    if ($responseData->adresseEtablissement->indiceRepetitionEtablissement) {
                        $data['address'] .= " " . trim($responseData->adresseEtablissement->indiceRepetitionEtablissement);
                    }
                    if ($responseData->adresseEtablissement->typeVoieEtablissement) {
                        $data['address'] .= " " . trim($responseData->adresseEtablissement->typeVoieEtablissement);
                    }
                    if ($responseData->adresseEtablissement->libelleVoieEtablissement) {
                        $data['address'] .= " " . trim($responseData->adresseEtablissement->libelleVoieEtablissement);
                    }
                }
                if (empty($data['postalCode'])) {
                    $data['postalCode'] = str_replace(" ", "", $responseData->adresseEtablissement->codePostalEtablissement);
                }
                if (empty($data['city'])) {
                    $data['city'] = trim($responseData->adresseEtablissement->libelleCommuneEtablissement);
                }
                if (empty($data['ape'])) {
                    $data['ape'] = str_replace(".", "", $responseData->uniteLegale->activitePrincipaleUniteLegale);
                }
            }
            return $data;
        } else {
            $this->logger->info("[SearchSireneAPI][" . $data['siret'] . "] organism not found on Sirene");
            return null;
        }
    }

    /**
     * @param string $name
     * @return string|null
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    private function getSiretFromAPISirene(string $name): ?string
    {
        $this->logger->info("[SearchSireneAPI][" . $name . "] Searching for siret organism on Sirene");
        $params = [
            "q" => "raisonSociale:\"" . strtoupper($name) . "\"",
            "champs" => "siret,etablissementSiege"
        ];
        $content = $this->sendSireneRequest('siret', $params);
        if ($content) {
            $this->logger->info("[SearchSireneAPI][" . $name . "] found siret on Sirene");
            $responseData = json_decode($content);
            $etablissements = array_values(array_filter($responseData->etablissements, function ($etb) {
                return $etb->etablissementSiege === true;
            }));
            if (sizeof($etablissements)) {
                return $etablissements[0]->siret;
            }
        } else {
            if (strpos($name, "(")) {
                $this->logger->info("[SearchSireneAPI][" . $name . "] not found siret on Sirene but retry without text after (");
                $name = explode("(", $name)[0];
                return $this->getSiretFromAPISirene($name);
            } else {
                $this->logger->info("[SearchSireneAPI][" . $name . "] not found siret on Sirene");
            }
        }
        return null;
    }

    /**
     * @param string $name
     * @return string|null
     */
    private function getSiretFromSocieteCom(string $name): ?string
    {
        $name = Tools::removeAccent($name);
        $name = explode("(", $name)[0];
        $this->logger->info("[searchSocieteCom][$name] Searching for siret organism on Societe.com");
        $params = ["query" => [
            "nom" => $name,
            "exa" => "on"
        ]];
        $content = $this->sendSocieteComRequest('GET', "/cgi-bin/liste", $params);
        if ($content) {
            $html = new HtmlDocument();
            $html->load($content);
            $allR = $html->find('#liste .txt-no-underline');
            if (sizeof($allR) >= 1) {
                $this->logger->info("[searchSocieteCom][$name] found organism");
                $content_details = $this->sendSocieteComRequest('GET', $allR[0]->href);
                if ($content_details) {
                    $html = new HtmlDocument();
                    $html->load($content_details);
                    $trs = $html->find('#rensjur tr');
                    foreach ($trs as $tr) {
                        $tds = $tr->find("td");
                        if (!empty($tds) && $tds[0]->plaintext === "SIRET (siege)") {
                            return str_replace(" ", "", $tds[1]->plaintext);
                        }
                    }
                }
            }
        }
        return null;
    }

    private function organismExistInCarif(string $term)
    {
        $params = [
            "region" => "",
            "code-departements" => "",
            "lib-organisme" => $term,
            "lib-commune" => "",
            "form_build_id" => "form-Uu8uw7hzC1dhvG8FbCEZKGwW6qvE1pUuxTL6RxwXxgs",
            "form_id" => "simple_search_organisme_form",
            "op" => "Soumettre"
        ];
        $this->logger->info("[searchOnCarif][$term] Searching for organism in carif");
        $content = $this->sendCarifRequest("POST", "/formations/recherche-organismes.html", $params, 1);
        if ($content) {
            $html = new HtmlDocument();
            $html->load($content);
            $allTR = $html->find('.table.organismes tr.formation');
            if (sizeof($allTR) >= 1) {
                $this->logger->info("[searchOnCarif][$term] found organism");
                foreach ($allTR as $tr) {
                    if ($tr->find('td', 2)->plaintext == $term) {
                        return $tr;
                    }
                }
                return $allTR[0];
            }
        }
        return null;
    }

    /**
     * @param string $method
     * @param string $url
     * @param array $params
     * @return string|null
     */
    private function sendSocieteComRequest(string $method, string $url, array $params = []): ?string
    {
        $try = 1;
        $maxTries = 5;
        $options = [
            'timeout' => Tools::getEnvValue('HTTP_CLIENT_TIMEOUT'),
            'headers' => [
                'User-Agent' => "'Mozilla/5.0 (Windows; U; Windows NT 6.1; en-US)AppleWebKit/534.10 (KHTML, like Gecko) Chrome/8.0.552.224 Safari/534.10'"
            ]
        ];
        if ($params) {
            if ($method == "GET") {
                $options['query'] = $params;
            } else {
                $options['body'] = $params;
            }
        }
        while ($try <= $maxTries) {
            $statusCode = null;
            try {

                $response = Tools::getHttpClient()->request($method, "https://www.societe.com" . $url, $options);
                $statusCode = $response->getStatusCode();
                return $response->getContent();
            } catch (Throwable $e) {
                if ($statusCode == 404) {
                    return null; //no results
                }
                $this->logger->info("[sendSocieteComRequest] sleep for 60sec $try/$maxTries");
                sleep(60);
                $try += 1;
            }
        }
        return null;
    }

    /**
     * @param string $url
     * @param array $params
     * @return string|null
     */
    private function sendSireneRequest(string $url, array $params = []): ?string
    {
        $try = 1;
        $maxTries = 2;
        $defaultWaitBeforeTry = 60;
        $fullUrl = Tools::getEnvValue('INSEE_SIREN_BASE_URL') . $url;
        $options = [
            'headers' => [
                'X-INSEE-Api-Key-Integration' => Tools::getEnvValue("INSEE_SIREN_API_KEY")
            ],
            'timeout' => Tools::getEnvValue('HTTP_CLIENT_TIMEOUT'),
            'query' => $params
        ];
        while ($try <= $maxTries) {
            $statusCode = null;
            try {
                $response = Tools::getHttpClient()->request('GET', $fullUrl, $options);
                $statusCode = $response->getStatusCode();
                return $response->getContent();
            } catch (Throwable $e) {
                if ($statusCode == 404 || $statusCode == 403) {
                    return null; //no results
                } else {
                    $this->logger->info("[sendSireneRequest] returned statusCode: $statusCode");
                }
                $this->logger->info("[sendSireneRequest] sleep for (" . ($defaultWaitBeforeTry * $try) . ")sec $try/$maxTries");
                sleep($defaultWaitBeforeTry * $try);
                $try += 1;
            }
        }
        return null;
    }

    /**
     * Otherwise circular dependency error
     * @return AutomatorApiService
     */
    private function getAutomatorService(): AutomatorApiService
    {
        global $kernel;
        $container = $kernel->getContainer();
        /** @var AutomatorApiService $service */
        $service = $container->get("App\Service\DataProviders\AutomatorApiService", ContainerInterface::NULL_ON_INVALID_REFERENCE);
        return $service;
    }

    /**
     * @param Organism $organism
     * @return void
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    private function checkDataProvidersExistForOrganism(Organism $organism): void
    {
        $dataProviders = [];
        if ($organism->isCertifierOrganism() && $organism->isTrainingOrganism()) {
            $dataProviders = DataProviders::requiredConnectionsDataProvidersForAll();
        } else if ($organism->isCertifierOrganism()) {
            $dataProviders = DataProviders::requiredConnectionsDataProvidersForCertifiers();
        } else if ($organism->isTrainingOrganism()) {
            $dataProviders = DataProviders::requiredConnectionsDataProvidersForTrainingOrganisms();
        }
        foreach ($dataProviders as $dataProvider) {
            $connection = $organism->getConnectionForDataProvider($dataProvider) ?? $this->connectionService->create($organism, $dataProvider);
            if (!$connection->getExistAtDataProvider()) {
                try {
                    $automatorApiService = $this->getAutomatorService();
                    $providerExist = $automatorApiService->checkExistAtDataProvider($dataProvider, $organism->getSiret());
                } catch (Exception $e) {
                } finally {
                    $connection->setExistAtDataProvider($providerExist['exist'] ?? false);
                }
            }
        }
    }

    /**
     * @param Organism $organism
     * @param string $email
     * @return void
     */
    private function addVerifiedSendAsEmail(Organism $organism, string $email)
    {
        if (in_array($email, $organism->getEmails())) {
            $organism->setSendAs([
                ['email' => $email, 'name' => $organism->getName()]
            ]);
            $this->save($organism);
        }
    }
}
