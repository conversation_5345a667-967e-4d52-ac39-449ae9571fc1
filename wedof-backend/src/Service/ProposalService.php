<?php

namespace App\Service;

use App\Entity\Organism;
use App\Entity\Proposal;
use App\Entity\RegistrationFolder;
use App\Entity\TrainingAction;
use App\Entity\User;
use App\Event\Proposal\ProposalEvents;
use App\Event\RegistrationFolder\RegistrationFolderEvents;
use App\Exception\WedofBadRequestHttpException;
use App\Library\utils\AlphaID;
use App\Library\utils\enums\ActivityTypes;
use App\Library\utils\enums\ProposalDiscountTypes;
use App\Library\utils\enums\ProposalStates;
use App\Library\utils\enums\RegistrationFolderStates;
use App\Library\utils\Tools;
use App\Repository\ProposalRepository;
use DateTime;
use DateTimeZone;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\QueryBuilder;
use ErrorException;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\File\File;
use Symfony\Component\Security\Core\Security;
use Throwable;

class ProposalService implements LoggerAwareInterface
{
    private LoggerInterface $logger;
    private SvgService $svgService;
    private ActivityService $activityService;
    private EventDispatcherInterface $dispatcher;
    private ProposalRepository $proposalRepository;
    private TrainingActionService $trainingActionService;
    private RegistrationFolderService $registrationFolderService;
    private UserService $userService;
    private Security $security;

    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    public function __construct(EventDispatcherInterface $dispatcher, ActivityService $activityService, SvgService $svgService, ProposalRepository $proposalRepository, TrainingActionService $trainingActionService, RegistrationFolderService $registrationFolderService, UserService $userService, Security $security)
    {
        $this->svgService = $svgService;
        $this->dispatcher = $dispatcher;
        $this->proposalRepository = $proposalRepository;
        $this->activityService = $activityService;
        $this->trainingActionService = $trainingActionService;
        $this->registrationFolderService = $registrationFolderService;
        $this->userService = $userService;
        $this->security = $security;
    }

    /**
     * @param Proposal $genericProposal
     * @param array $data
     * @param Organism $organism
     * @return Proposal
     * @throws Throwable
     */
    public function createFromGenericProposal(Proposal $genericProposal, array $data, Organism $organism): Proposal
    {
        $proposal = $this->proposalRepository->findByParentProposalAndEmailAndUsed($genericProposal, $data['email'], false);
        if (!$proposal) {
            $data = array_merge([
                'limitUsage' => 1,
                'state' => ProposalStates::DRAFT(),
                'trainingActions' => $genericProposal->getTrainingActions(),
                'amount' => $genericProposal->getAmount(),
                'discountType' => $genericProposal->getDiscountType(),
                'autoValidate' => $genericProposal->getAutoValidate(),
                'sessionStartDate' => $genericProposal->getSessionStartDate(),
                'sessionEndDate' => $genericProposal->getSessionEndDate(),
                'notes' => $genericProposal->getNotes(),
                'description' => !empty($data['description']) ? $data['description'] : $genericProposal->getDescription(),
                'expire' => $genericProposal->getExpire(),
                'logo' => $genericProposal->getLogoFile(),
                'customColorScheme' => $genericProposal->getCustomColorScheme(),
                'sales' => $genericProposal->getSales() ? ["email" => $genericProposal->getSales()->getEmail()] : null,
                'stateLastUpdate' => $genericProposal->setStateLastUpdate(new DateTime()),
                'clientIpAddress' => $genericProposal->getClientIpAddress()
            ], $data);
            $data['tags'] = empty($data['tags']) ? $genericProposal->getTagNames() : array_merge($genericProposal->getTagNames(), $data['tags']);
            return $this->create($data, $organism, $genericProposal);
        } else {
            return $proposal;
        }
    }

    /**
     * @param array $data
     * @param Organism $organism
     * @param Proposal|null $parentProposal
     * @return Proposal
     * @throws ErrorException
     * @throws ORMException
     * @throws Throwable
     * @throws OptimisticLockException
     */
    public function create(array $data, Organism $organism, Proposal $parentProposal = null): Proposal
    {
        $proposal = new Proposal();

        $proposal->setCreatedOn(new DateTime());
        $proposal->setOrganism($organism);
        $proposal->setClientIpAddress($data['clientIpAddress']);

        if (!empty($data['email'])) {
            $proposal->setFirstName($data['firstName']);
            $proposal->setLastName($data['lastName']);
            $proposal->setEmail($data['email']);
            $proposal->setPhoneNumber($data['phoneNumber']);
            $proposal->setLimitUsage(1);
            $proposal->setState($data['state'] ?? ProposalStates::DRAFT());
            $proposal->setCodeRequired(true);
        } else {
            $proposal->setState(ProposalStates::TEMPLATE());
            $proposal->setFirstName(null);
            $proposal->setLastName(null);
            $proposal->setEmail(null);
            $proposal->setPhoneNumber(null);
            $proposal->setLimitUsage(!empty($data['limitUsage']) ? $data['limitUsage'] : null);
            $proposal->setCodeRequired($data['codeRequired'] ?? true);
        }
        $proposal->setDiscountType($data['discountType']);
        $proposal->setAmount($data['discountType'] === ProposalDiscountTypes::NONE()->getValue() ? null : $data['amount']);

        if (!empty($data['trainingActions'])) {
            foreach ($data['trainingActions'] as $trainingAction) {
                if (is_array($trainingAction) && isset($trainingAction['externalId'])) {
                    $trainingAction = $this->trainingActionService->getByExternalId($trainingAction['externalId']);
                    if (!$trainingAction) {
                        throw new WedofBadRequestHttpException("Erreur, l'action de formation n'a pas été trouvé");
                    }
                }
                if ($trainingAction && $trainingAction->getTraining()->getOrganism()->getId() === $organism->getId()) {
                    $proposal->addTrainingAction($trainingAction);
                }
            }
            if ($proposal->getTrainingActions()->count() === 1) {
                $proposal->setSelectedTrainingAction($proposal->getTrainingActions()[0]);
            }
        }

        if (!empty($data['sales']['email'])) {
            $users = $this->userService->listReturnQueryBuilder(['organism' => $proposal->getOrganism(), 'query' => $data['sales']['email']])->getQuery()->getResult();
            if (sizeof($users) == 1) {
                $proposal->setSales($users[0]);
            }
        }

        $proposal->setIndicativeDuration(!empty($data['indicativeDuration']) ? $data['indicativeDuration'] : null);

        if (!empty($data['sessionStartDate'])) {
            /** @var DateTime $sessionStartDate */
            $sessionStartDate = $data['sessionStartDate'];
            $proposal->setSessionStartDate($sessionStartDate);
        }

        if (!empty($data['sessionEndDate'])) {
            /** @var DateTime $sessionEndDate */
            $sessionEndDate = $data['sessionEndDate'];
            $proposal->setSessionEndDate($sessionEndDate);
        }

        if (!empty($data['expire'])) {
            /** @var DateTime $expiration */
            $expiration = $data['expire'];
            $proposal->setExpire($expiration);
        }

        $proposal->setAutoValidate(!empty($data['autoValidate']) ?? true);
        $proposal->setNotes(!empty($data['notes']) ? $data['notes'] : null);
        $proposal->setDescription(!empty($data['description']) ? $data['description'] : null);
        $proposal->setParentProposal($parentProposal);

        if (!$proposal->isIndividual() || $proposal->getParentProposal()) {
            $proposal->setCustomColorScheme(!empty($data['customColorScheme']) ? $data['customColorScheme'] : null);
            if (isset($data['logo']) && $data['logo'] instanceof File) {
                $proposal->setLogoFile($data['logo']);
            }
        }

        $isRandomCode = empty($data['code']);
        $code = $isRandomCode ? AlphaID::generate((new DateTime())->getTimestamp(), false, 7, $organism->getSiret()) : $data['code'];
        $proposal->setCode($code);

        if (empty($data['tags'])) {
            $data['tags'] = [];
        } else {
            $data['tags'] = array_map('strtolower', $data['tags']); // Required to enforce lower + avoid bug with lower / upper
        }
        if (!$proposal->isIndividual() && !$isRandomCode) { // Weird way to add a tag because there is no addTag(string) on tagInterface, we need to use setTagsText a single time
            $data['tags'][] = strtolower($code);
        }
        $proposal->setTagsText(join(",", $data['tags']));
        $proposal->setStateLastUpdate(new DateTime());
        if (isset($data['metadata'])) {
            $proposal->setMetadata(Tools::removeSpacesInKeys($data['metadata']));
        }
        $this->save($proposal);
        if ($parentProposal) {
            $this->createCreateActivityProposals($proposal, $parentProposal);
        } else {
            $this->createCreateActivityManually($proposal);
        }
        $this->sendEvent($proposal, ProposalEvents::CREATED);
        return $proposal;
    }

    /**
     * @param Proposal $proposal
     * @param array $data
     * @return Proposal
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function update(Proposal $proposal, array $data): Proposal
    {
        if ($proposal->getState() == ProposalStates::TEMPLATE() && ($proposal->getIndividualProposals()->count() !== 0)) {
            if (isset($data['metadata'])) {
                $proposal->setMetadata(empty($data['metadata']) ? null : Tools::removeSpacesInKeys($data['metadata']));
            }
        } else {
            $proposal->setFirstName($data['firstName'] ?? $proposal->getFirstName());
            $proposal->setLastName($data['lastName'] ?? $proposal->getLastName());
            $proposal->setPhoneNumber($data['phoneNumber'] ?? $proposal->getPhoneNumber());
            $proposal->setEmail($data['email'] ?? $proposal->getEmail());
            if (isset($data['tags'])) {
                $data['tags'] = array_map('strtolower', $data['tags']); // Required to enforce lower + avoid bug with lower / upper
                $proposal->setTagsText(join(",", $data['tags']));
            }
            if (isset($data['discountType']) && array_key_exists('amount', $data)) {
                $proposal->setDiscountType($data['discountType'] ?: $proposal->getDiscountType());
                $proposal->setAmount($data['discountType'] === ProposalDiscountTypes::NONE()->getValue() ? null : $data['amount']);
            }

            if (isset($data['sessionStartDate'])) {
                /** @var DateTime $sessionStartDate */
                $sessionStartDate = $data['sessionStartDate'];
                $proposal->setSessionStartDate($sessionStartDate);
            } else if (array_key_exists('sessionStartDate', $data)) {
                $proposal->setSessionStartDate(null);
            }

            if (isset($data['sessionEndDate'])) {
                /** @var DateTime $sessionEndDate */
                $sessionEndDate = $data['sessionEndDate'];
                $proposal->setSessionEndDate($sessionEndDate);
            } else if (array_key_exists('sessionEndDate', $data)) {
                $proposal->setSessionEndDate(null);
            }

            if (isset($data['expire'])) {
                /** @var DateTime $expiration */
                $expiration = $data['expire'];
                $proposal->setExpire($expiration);
            } else if (array_key_exists('expire', $data)) {
                $proposal->setExpire(null);
            }

            if (isset($data['trainingActions'])) {
                $proposal->getTrainingActions()->clear();
                foreach ($data['trainingActions'] as $trainingAction) {
                    if (is_array($trainingAction) && isset($trainingAction['externalId'])) {
                        $trainingAction = $this->trainingActionService->getByExternalId($trainingAction['externalId']);
                    }
                    if ($trainingAction && $trainingAction->getTraining()->getOrganism()->getId() === $proposal->getOrganism()->getId()) {
                        $proposal->addTrainingAction($trainingAction);
                    }
                }
                if ($proposal->getTrainingActions()->count() === 1) {
                    $proposal->setSelectedTrainingAction($proposal->getTrainingActions()[0]);
                } else {
                    $proposal->setSelectedTrainingAction(null);
                }
            }
            if (!$proposal->isIndividual()) {
                if (isset($data["customColorScheme"])) {
                    $proposal->setCustomColorScheme($data['customColorScheme']);
                }
                if (isset($data['logo']) && $data['logo'] instanceof File) {
                    $proposal->setLogoFile($data['logo']);
                } else if (array_key_exists('logo', $data) && $data['logo'] === null) {
                    //It will not delete the file in the filesystem
                    //It should be done by something else (someone?)
                    $proposal->setLogoFile();
                    $proposal->setLogoName(null);
                    $proposal->updateCreatedOnAndUpdatedOnTimestamps();
                }
            }
            $proposal->setIndicativeDuration(!empty($data['indicativeDuration']) ? $data['indicativeDuration'] : null);
            $proposal->setAutoValidate($data['autoValidate'] ?? $proposal->getAutoValidate());
            $proposal->setNotes($data['notes'] ?? $proposal->getNotes());
            $proposal->setDescription($data['description'] ?? $proposal->getDescription());
            $proposal->setLimitUsage($data['email'] ? 1 : ($data['limitUsage'] ?: null));
            if (isset($data['metadata'])) {
                $proposal->setMetadata(empty($data['metadata']) ? null : Tools::removeSpacesInKeys($data['metadata']));
            }
            if (!empty($data['sales']['email'])) {
                $users = $this->userService->listReturnQueryBuilder(['organism' => $proposal->getOrganism(), 'query' => $data['sales']['email']])->getQuery()->getResult();
                if (sizeof($users) == 1) {
                    $proposal->setSales($users[0]);
                }
            }
        }
        $this->save($proposal);
        $this->sendEvent($proposal, ProposalEvents::UPDATED);
        return $proposal;
    }

    /**
     * @param Proposal $proposal
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function viewed(Proposal $proposal)
    {
        if ($proposal->getState() === ProposalStates::ACTIVE()) {
            $proposal->setState(ProposalStates::VIEWED());
            $proposal->setStateLastUpdate(new DateTime());
            $this->save($proposal);
            $this->createUpdateStateActivity($proposal);
            $this->sendEvent($proposal, ProposalEvents::VIEWED);
        }
    }

    /**
     * @param Proposal $proposal
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function active(Proposal $proposal)
    {
        $proposal->setState($proposal->isIndividual() ? ProposalStates::ACTIVE() : ProposalStates::TEMPLATE());
        $proposal->setStateLastUpdate(new DateTime());
        $this->save($proposal);
        $this->createUpdateStateActivity($proposal);
        $this->sendEvent($proposal, ProposalEvents::ACTIVE);
    }

    /**
     * @param Proposal $proposal
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function refused(Proposal $proposal)
    {
        $proposal->setState(ProposalStates::REFUSED());
        $proposal->setStateLastUpdate(new DateTime());
        $this->save($proposal);
        $this->createUpdateStateActivity($proposal);
        $this->sendEvent($proposal, ProposalEvents::REFUSED);
    }

    /**
     * @param Proposal $proposal
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function draft(Proposal $proposal)
    {
        $proposal->setState(ProposalStates::DRAFT());
        $proposal->setStateLastUpdate(new DateTime());
        $this->save($proposal);
        $this->createUpdateStateActivity($proposal);
        $this->sendEvent($proposal, ProposalEvents::DRAFT);
    }

    /**
     * @param Proposal $proposal
     * @param string $trainingActionId
     * @return Proposal
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function selectTrainingAction(Proposal $proposal, string $trainingActionId): Proposal
    {
        $trainingActions = $proposal->getTrainingActions();
        /** @var TrainingAction $selectedTrainingAction */
        $selectedTrainingAction = $this->trainingActionService->getByExternalId($trainingActionId);

        if (!$proposal->getRegistrationFolder()
            && $selectedTrainingAction->getTraining()->getOrganism()->getSiret() === $proposal->getOrganism()->getSiret()
            && ($trainingActions->count() === 0 || $trainingActions->contains($selectedTrainingAction))) {
            $proposal->setSelectedTrainingAction($selectedTrainingAction);
        }
        $this->save($proposal);
        return $proposal;
    }

    /**
     * @param Organism|null $organism
     * @param string $code
     * @return Proposal|null
     * @throws NonUniqueResultException
     */
    public function getByOrganismAndCode(?Organism $organism, string $code): ?Proposal
    {
        return $this->proposalRepository->findByOrganismAndCode($organism, $code);
    }

    /**
     * @param string $code
     * @return Proposal|null
     */
    public function getByCode(string $code): ?Proposal
    {
        return $this->proposalRepository->findOneBy(['code' => $code]);
    }

    /**
     * @param Organism|null $organism
     * @param string $code
     * @return Proposal|null
     * @throws NonUniqueResultException
     */
    public function getActiveIndividualByOrganismAndCode(?Organism $organism, string $code): ?Proposal
    {
        return $this->proposalRepository->findIndividualByOrganismAndCodeAndInStates($organism, $code, [
            ProposalStates::ACTIVE()->getValue(),
            ProposalStates::VIEWED()->getValue(),
            ProposalStates::ACCEPTED()->getValue()
        ]);
    }

    /**
     * @param Organism|null $organism
     * @param string $code
     * @return Proposal|null
     * @throws NonUniqueResultException
     */
    public function getGenericByOrganismAndCode(?Organism $organism, string $code): ?Proposal
    {
        return $this->proposalRepository->findGenericByOrganismAndCode($organism, $code);
    }

    /**
     * @param Organism $organism
     * @param array $parameters
     * @return QueryBuilder
     */
    public function listReturnQueryBuilder(Organism $organism, array $parameters): QueryBuilder
    {
        return $this->proposalRepository->findAllReturnQueryBuilder($organism, $parameters);
    }

    /**
     * @param Organism $organism
     * @param array $parameters
     * @param array $columnIds
     * @return ArrayCollection
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function listByColumn(Organism $organism, array $parameters, array $columnIds): ArrayCollection
    {
        return $this->proposalRepository->findAllByColumn($organism, $parameters, $columnIds);
    }

    /**
     * @param Organism|null $organism
     * @param array $parameters
     * @param string $columnId
     * @return float
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function revenueByColumn(?Organism $organism, array $parameters, string $columnId): float
    {
        if (in_array(ProposalStates::ALL()->getValue(), $parameters['state'])) {
            $parameters['state'] = ProposalStates::valuesStatesToString();  // Hack because of "all" fake state
        }
        $parameters = Tools::computeKanbanColumnParameters($parameters, $this->proposalRepository->listColumnConfigs(), $columnId);
        $qb = $this->proposalRepository->findAllReturnQueryBuilder($organism, $parameters);
        $states = $parameters['state'];
        return $this->proposalRepository->getRevenueByStates($qb, $states, $organism->getVat() ?? 0);
    }

    /**
     * @param Proposal $proposal
     * @return void
     */
    public function delete(Proposal $proposal)
    {
        if (!$proposal->isDeletable()) {
            throw new WedofBadRequestHttpException("Erreur, une proposition utilisée ne peut pas être supprimée.");
        } else {
            $proposalToDelete = (clone $proposal)->setState('deleted');
            $this->proposalRepository->delete($proposal);
            $this->sendEvent($proposalToDelete, ProposalEvents::DELETED);
        }
    }

    /**
     * @param string $entityId
     * @return Proposal|null
     */
    public function getByEntityId(string $entityId): ?Proposal
    {
        return $this->getByCode($entityId);
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @return Proposal|null
     * @throws Throwable
     */
    public function getProposalToApply(RegistrationFolder $registrationFolder): ?Proposal
    {
        if ($registrationFolder->getState() === RegistrationFolderStates::NOT_PROCESSED()->getValue()) {
            $proposal = $this->proposalRepository->findByRegistrationFolder($registrationFolder);
            //give a try to an generic proposal without code required
            if (!$proposal) {
                $genericProposal = $this->proposalRepository->findByNoCodeRequired($registrationFolder);
                if ($genericProposal) {
                    $data = [
                        'firstName' => $registrationFolder->getAttendee()->getFirstName(),
                        'lastName' => $registrationFolder->getAttendee()->getLastName(),
                        'email' => $registrationFolder->getAttendee()->getEmail(),
                        'phoneNumber' => $registrationFolder->getAttendee()->getPhoneNumber(),
                        'state' => ProposalStates::ACTIVE()
                    ];
                    $proposal = $this->createFromGenericProposal($genericProposal, $data, $registrationFolder->getOrganism());
                }
            }
            return $proposal;

        } else {
            return null;
        }
    }

    /**
     * @param string $proposalId
     * @return Proposal
     */
    public function getById(string $proposalId): ?Proposal
    {
        return $this->proposalRepository->findById($proposalId);
    }

    /**
     * @param Proposal $proposal
     * @param RegistrationFolder $registrationFolder
     * @return RegistrationFolder
     */
    public function applyOnRegistrationFolder(Proposal $proposal, RegistrationFolder $registrationFolder): RegistrationFolder
    {
        // **** RESPONSIBILITY STUFF **** //
        //Override the client addr to send responsibility to the owner of the Proposal
        if ($proposal->getClientIpAddress()) {
            $_SERVER['OVERRIDE_CLIENT_ADDR'] = $proposal->getClientIpAddress();
        }
        // **** RESPONSIBILITY STUFF **** //

        $data = [];
        switch ($proposal->getDiscountType()) {
            case ProposalDiscountTypes::FIXED()->getValue():
                $data['priceChange'] = array(
                    'price' => $proposal->getAmount(),
                    'note' => 'Code : ' . $proposal->getCode() . ' appliqué. Montant modifié à ' . $proposal->getAmount() . '€ TTC'
                );
                break;
            case ProposalDiscountTypes::AMOUNT()->getValue():
                $data['priceChange'] = array(
                    'amount' => $proposal->getAmount(),
                    'note' => 'Code : ' . $proposal->getCode() . ' appliqué. Montant modifié de ' . $proposal->getAmount() . '€ TTC'
                );
                break;
            case ProposalDiscountTypes::PERCENT()->getValue():
                $data['priceChange'] = array(
                    'percent' => $proposal->getAmount(),
                    'note' => 'Code : ' . $proposal->getCode() . ' appliqué. Montant modifié de ' . $proposal->getAmount() . '%'
                );
                break;
            case ProposalDiscountTypes::NONE()->getValue():
            default:
                //nothing to do
                break;
        }
        $data['trainingActionInfo'] = [];
        if ($proposal->getSessionStartDate()) {
            $data['trainingActionInfo']['sessionStartDate'] = $proposal->getSessionStartDate()->setTimezone(new DateTimeZone('Europe/Paris'));
        } else if ($registrationFolder->getSession()->getStartDate()) { //try to always send a startDate
            $data['trainingActionInfo']['sessionStartDate'] = $registrationFolder->getSession()->getStartDate()->setTimezone(new DateTimeZone('Europe/Paris'));
        }
        if ($proposal->getSessionEndDate()) {
            $data['trainingActionInfo']['sessionEndDate'] = $proposal->getSessionEndDate()->setTimezone(new DateTimeZone('Europe/Paris'));
        } else if ($registrationFolder->getSession()->getEndDate()) { //try to always send an endDate
            $data['trainingActionInfo']['sessionEndDate'] = $registrationFolder->getSession()->getEndDate()->setTimezone(new DateTimeZone('Europe/Paris'));
        }
        if ($proposal->getIndicativeDuration()) {
            $data['trainingActionInfo']['indicativeDuration'] = $proposal->getIndicativeDuration();
        }
        try {
            $this->logger->debug("[ProposalService][" . $registrationFolder->getExternalId() . "][" . $proposal->getCode() . "] set proposal");
            $registrationFolder->setProposal($proposal);
            $proposal->setState(ProposalStates::ACCEPTED());
            $proposal->setStateLastUpdate(new DateTime());
            $this->logger->debug("[ProposalService][" . $registrationFolder->getExternalId() . "][" . $proposal->getCode() . "] update");
            $data['tags'] = array_merge($registrationFolder->getTagNames(), $proposal->getTagNames());
            if ($proposal->getNotes()) {
                $data['notes'] = $proposal->getNotes();
                if ($registrationFolder->getNotes()) {
                    $data['notes'] .= " \n ###################### \n " . $registrationFolder->getNotes();
                }
            }
            $registrationFolder = $this->registrationFolderService->update($registrationFolder, $data);
            $this->activityService->create([
                'title' => "Une proposition a été appliquée au dossier de formation.",
                'type' => ActivityTypes::UPDATE()->getValue(),
                'eventTime' => new DateTime(),
                'link' => $proposal->getPermalink()
            ], null, $registrationFolder);
            $this->activityService->create([
                'title' => "Un dossier de formation a été créé.",
                'type' => ActivityTypes::UPDATE()->getValue(),
                'eventTime' => new DateTime(),
                'link' => $registrationFolder->getPermalink()
            ], null, $proposal);
            $this->createUpdateStateActivity($proposal); // update accepted state proposal
            if ($proposal->getAutoValidate()) {
                $this->logger->debug("[ProposalService][" . $registrationFolder->getExternalId() . "][" . $proposal->getCode() . "] autoValidate");
                $registrationFolder = $this->registrationFolderService->lockedValidate($registrationFolder);
                $this->logger->debug("[ProposalService][" . $registrationFolder->getExternalId() . "][" . $proposal->getCode() . "] validate done");
            }
            $this->logger->info("[" . $registrationFolder->getExternalId() . "][event] RegistrationFolder " . RegistrationFolderEvents::PROPOSAL_APPLIED);
            $this->dispatcher->dispatch(new RegistrationFolderEvents($registrationFolder), RegistrationFolderEvents::PROPOSAL_APPLIED);
            $this->logger->info("[" . $registrationFolder->getExternalId() . "][event] RegistrationFolder dispatched: " . RegistrationFolderEvents::PROPOSAL_APPLIED);
        } catch (ErrorException | Throwable $e) {
            $this->logger->debug("[ProposalService][" . $registrationFolder->getExternalId() . "][" . $proposal->getCode() . "] an error happened " . $e->getTraceAsString());
            throw new WedofBadRequestHttpException();
        } finally {
            // **** RESPONSIBILITY STUFF **** //
            if (isset($_SERVER['OVERRIDE_CLIENT_ADDR'])) {
                unset($_SERVER['OVERRIDE_CLIENT_ADDR']);
            }
            // **** RESPONSIBILITY STUFF **** //
        }

        return $registrationFolder;
    }

    /**
     * @param LoggerInterface $logger
     */
    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    /**
     * @param Proposal $proposal
     * @param int $width
     * @param int $height
     * @return string
     */
    public function generateAlertText(Proposal $proposal, int $width = 320, int $height = 400): string
    {
        return $this->svgService->alert($this->getProposalAlertText($proposal), $width, $height);
    }

    /**
     * @param Collection|null $trainingActions
     * @return string
     */
    public static function getTrainingActionsResume(Collection $trainingActions = null): string
    {
        if ($trainingActions->count() > 0) {
            global $kernel;
            $container = $kernel->getContainer();
            /** @var TrainingService $service */
            $trainingService = $container->get("App\Service\TrainingService", ContainerInterface::NULL_ON_INVALID_REFERENCE);
            if ($trainingActions->count() === 1) {
                $getTrainingActions = "valable pour l'action de formation " . $trainingService->getTitleFromTrainingAction($trainingActions[0]);
            } else {
                $getTrainingActions = "valable pour ";
                foreach ($trainingActions as $trainingAction) {
                    $getTrainingActions .= $trainingService->getTitleFromTrainingAction($trainingAction) . '; ';
                }
            }
        } else {
            $getTrainingActions = "valable pour toutes actions de formation";
        }
        return $getTrainingActions;
    }

    //-----------------
    // METHODES PRIVEES
    //-----------------

    /**
     * @param Proposal $proposal
     * @throws Throwable
     */
    private function save(Proposal $proposal): void
    {
        $this->proposalRepository->save($proposal);
    }

    /**
     * @param Proposal $proposal
     * @return string
     */
    private function getProposalAlertText(Proposal $proposal): string
    {
        $text = '<p>Bonne nouvelle <b>' . $proposal->getFirstName() . ' ' . $proposal->getLastName() . '</b> vous bénéficiez d\'une proposition personnalisée';
        if ($proposal->getExpire()) {
            $text .= ' valable jusqu\'au <b>' . $proposal->getExpire()->format("d/m/Y") . '</b>';
        }
        $text .= ' vous permettant ';
        if ($proposal->getDiscountType() === ProposalDiscountTypes::FIXED()->getValue()) {
            $text .= ' de profiter d\'un tarif personnalisé de <b>' . $proposal->getAmount() . '</b>€ TTC à valoir sur';
        } else if ($proposal->getDiscountType() === ProposalDiscountTypes::PERCENT()->getValue()) {
            if ($proposal->getAmount() >= 0) {
                $text .= ' de profiter d\'un <b>tarif personnalisé (+' . $proposal->getAmount() . '%)</b> à valoir sur';
            } else {
                $text .= ' de profiter d\'une <b>remise (' . $proposal->getAmount() . '%)</b> à valoir sur';
            }
        } else if ($proposal->getDiscountType() === ProposalDiscountTypes::AMOUNT()->getValue()) {
            if ($proposal->getAmount() >= 0) {
                $text .= ' de profiter d\'un <b>tarif personnalisé (+' . $proposal->getAmount() . '€ TTC)</b> à valoir sur';
            } else {
                $text .= ' de profiter d\'une <b>remise  (-' . $proposal->getAmount() . '€ TTC)</b> à valoir sur';
            }
        } else if ($proposal->getDiscountType() === ProposalDiscountTypes::NONE()->getValue()) {
            $text .= ' de vous inscrire à ';
        }
        if ($proposal->getSelectedTrainingAction()) {
            $text .= ' cette formation* ';
            if ($proposal->getSessionStartDate()) {
                $text .= ' commencant le <b>' . $proposal->getSessionStartDate()->format("") . '</b>';
            }
            if ($proposal->getSessionEndDate()) {
                $text .= ' et se terminant le <b>' . $proposal->getSessionEndDate()->format("") . '</b>';
            }
        }
        return $text . "</p>";
    }

    /**
     * @param Proposal $proposal
     * @param String $eventName
     * @return void
     */
    private function sendEvent(Proposal $proposal, string $eventName): void
    {
        $this->dispatcher->dispatch(new ProposalEvents($proposal), $eventName);
        $this->logger->info("[" . $proposal->getCode() . "][event] Proposal event dispatched $eventName for " . $proposal->getOrganism());
    }

    /**
     * @param Proposal $proposal
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    private function createUpdateStateActivity(Proposal $proposal): void
    {
        /** @var User $user */
        $user = $this->security->getUser();
        $state = $proposal->getState();
        $this->activityService->create([
            'title' => "La proposition est passée à l'état " . ProposalStates::toFrString($state),
            'type' => ActivityTypes::UPDATE_STATE(),
            'eventTime' => new DateTime(),
            'field' => 'state',
            'newValue' => $state,
            'origin' => $user ? null : 'Wedof'
        ], $user, $proposal, false);
    }

    /**
     * @param Proposal $proposal
     * @param Proposal|null $parentProposal
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    private function createCreateActivityProposals(Proposal $proposal, Proposal $parentProposal): void
    {
        /** @var User $user */
        $user = $this->security->getUser();
        $this->activityService->create([
            'title' => 'La proposition a été créée depuis la proposition générique ' . $parentProposal->getCode(),
            'type' => ActivityTypes::CREATE(),
            'eventTime' => new DateTime(),
            'field' => 'state',
            'newValue' => $proposal->getState(),
            'link' => $parentProposal->getPermalink(),
            'origin' => $user ? null : 'Wedof'
        ], $user, $proposal, false);
        $this->createUpdateStateActivity($proposal); // set activity for view state

        $this->activityService->create([
            'title' => 'Une proposition individuelle ' . $proposal->getCode() . ' a été créée',
            'type' => ActivityTypes::CREATE(),
            'eventTime' => new DateTime(),
            'field' => 'state',
            'newValue' => $proposal->getState(),
            'link' => $proposal->getPermalink(),
            'origin' => $user ? null : 'Wedof'
        ], $user, $parentProposal, false);
    }

    /**
     * @param Proposal $proposal
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    private function createCreateActivityManually(Proposal $proposal): void
    {
        /** @var User $user */
        $user = $this->security->getUser();
        $this->activityService->create([
            'title' => 'La proposition a été créée',
            'type' => ActivityTypes::CREATE(),
            'eventTime' => new DateTime(),
            'field' => 'state',
            'newValue' => $proposal->getState(),
            'origin' => $user ? null : 'Wedof'
        ], $user, $proposal, false);
    }
}
