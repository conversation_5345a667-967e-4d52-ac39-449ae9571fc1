<?php

namespace App\Service;

use App\Exception\WedofBadRequestHttpException;
use App\Library\utils\enums\AttendeeGender;
use App\Library\utils\Tools;
use Exception;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpClient\CurlHttpClient;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;

class AzureAiDocumentService implements LoggerAwareInterface
{
    private LoggerInterface $logger;

    private string $projectDir;
    private CityService $cityService;

    public function __construct(string $projectDir, CityService $cityService)
    {
        $this->projectDir = $projectDir;
        $this->cityService = $cityService;
    }

    /**
     * @param LoggerInterface $logger
     */
    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    /**
     * @param string $filepath
     * @param string $modelId
     * @return array|null
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    public function extractDataDocument(string $filepath, $modelId = "prebuilt-idDocument"): ?array
    {
        $apiKey = Tools::getEnvValue("AZURE_DOC_AI_API_KEY");
        $endPoint = Tools::getEnvValue("AZURE_DOC_AI_ENDPOINT");
        $publicTmpDir = Tools::getEnvValue("AZURE_PUBLIC_TMP_DIR");

        $error = false;
        $extractedData = null;

        if (!is_dir($this->projectDir . $publicTmpDir)) {
            mkdir($this->projectDir . $publicTmpDir);
        }

        $fileInfos = pathinfo($filepath);
        $fileName = md5($fileInfos['filename'] . time());
        $extension = $fileInfos['extension'];
        $tmpFile = $this->projectDir . $publicTmpDir . $fileName . "." . $extension;

        try {
            $httpClient = $this->getHttpClient();
            $defaultOptions = [
                "headers" => [
                    "Ocp-Apim-Subscription-Key" => $apiKey,
                ]
            ];
            //copy file as a tmp file with public visibility
            if (copy($filepath, $tmpFile)) {
                $urlSource = Tools::getEnvValue("WEDOF_BASE_URI") . $publicTmpDir . $fileName . "." . $extension;
                $this->logger->info($urlSource);
                $options = array_merge($defaultOptions, ["json" => ["urlSource" => $urlSource]]);
                $this->logger->info("$endPoint/formrecognizer/documentModels/$modelId:analyze?api-version=2023-07-31");
                $response = $httpClient->request("POST", "$endPoint/formrecognizer/documentModels/$modelId:analyze?api-version=2023-07-31", $options);
                if ($response->getStatusCode() === 202 && !empty($response->getHeaders()["operation-location"][0])) {
                    $operationLocation = $response->getHeaders()["operation-location"][0];
                    //max 60sec data otherwise just stop
                    for ($i = 0; $i < 30; $i++) {
                        $response = $httpClient->request("GET", $operationLocation, $defaultOptions);
                        $data = json_decode($response->getContent(), true);
                        if (in_array($data['status'], ['running', 'notStarted'])) {
                            //wait
                            sleep(2);
                        } else if ($data['status'] === 'succeeded'
                            && !empty($data['analyzeResult']['documents'][0]['docType'])
                            && in_array($data['analyzeResult']['documents'][0]['docType'], ['idDocument.driverLicense', 'idDocument.passport', 'idDocument.nationalIdentityCard', 'idDocument.residencePermit'])) {
                            //send data back
                            $extractedData = [
                                "type" => $data['analyzeResult']['documents'][0]['docType'],
                                "fields" => $data['analyzeResult']['documents'][0]['fields']
                            ];
                            break;
                        } else {
                            //an error occurred
                            $error = "error-4";
                            break;
                        }
                    }
                    if (!$extractedData && !$error) {
                        //time elapsed
                        $error = "error-3";
                    }
                }
                if (!$extractedData && !$error) {
                    //a very strange error occurred
                    $error = $error ?? "error-1";
                }
            } else {
                $error = "error-5";
            }
        } catch (Exception $e) {
            $error = "error-unknown";
        } finally {
            unlink($tmpFile);
            if ($extractedData) {
                return $extractedData;
            } else {
                $this->logger->debug("Azure Document recognition error " . $error);
                throw new WedofBadRequestHttpException($error ? "Document recognition error $error" : "Document recognition error");
            }
        }
    }

    private function getHttpClient(): CurlHttpClient
    {
        $options = [
            'headers' => [
                'Content-Type: application/json',
                'Accept: application/json',
            ]
        ];
        if ($_ENV['APP_ENV'] == 'dev') {
            $options = array_merge($options,
                array(
                    "verify_peer" => false
                ));
        }
        return new CurlHttpClient($options);
    }

    /**
     * @param string $filepath
     * @param string $modelId
     * @return array|null
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    public function extractSocialSecurityCard(string $filepath, string $modelId = "socialSecurityCard"): ?array
    {
        $apiKey = Tools::getEnvValue("AZURE_DOC_AI_API_KEY");
        $endPoint = Tools::getEnvValue("AZURE_DOC_AI_ENDPOINT");
        $publicTmpDir = Tools::getEnvValue("AZURE_PUBLIC_TMP_DIR");
        $error = false;
        $extractedData = null;
        if (!is_dir($this->projectDir . $publicTmpDir)) {
            mkdir($this->projectDir . $publicTmpDir);
        }
        $fileInfos = pathinfo($filepath);
        $fileName = md5($fileInfos['filename'] . time());
        $extension = $fileInfos['extension'];
        $tmpFile = $this->projectDir . $publicTmpDir . $fileName . "." . $extension;
        try {
            $httpClient = $this->getHttpClient();
            $defaultOptions = [
                "headers" => [
                    "Ocp-Apim-Subscription-Key" => $apiKey,
                ]
            ];
            //copy file as a tmp file with public visibility
            if (copy($filepath, $tmpFile)) {
                $urlSource = Tools::getEnvValue("WEDOF_BASE_URI") . $publicTmpDir . $fileName . "." . $extension;
                $this->logger->info($urlSource);
                $options = array_merge($defaultOptions, ["json" => ["urlSource" => $urlSource]]);
                $this->logger->info("$endPoint/documentintelligence/documentModels/$modelId:analyze?api-version=2024-11-30");
                $response = $httpClient->request("POST", "$endPoint/documentintelligence/documentModels/$modelId:analyze?api-version=2024-11-30", $options);
                if ($response->getStatusCode() === 202 && !empty($response->getHeaders()["operation-location"][0])) {
                    $operationLocation = $response->getHeaders()["operation-location"][0];
                    //max 60sec data otherwise just stop
                    for ($i = 0; $i < 30; $i++) {
                        $response = $httpClient->request("GET", $operationLocation, $defaultOptions);
                        $data = json_decode($response->getContent(), true);
                        if (in_array($data['status'], ['running', 'notStarted'])) {
                            //wait
                            sleep(2);
                        } else if ($data['status'] === 'succeeded'
                            && !empty($data['analyzeResult']['documents'][0]['docType'])
                            && $data['analyzeResult']['documents'][0]['docType'] === 'socialSecurityCard') {
                            //send data back
                            $extractedData = [
                                "type" => $data['analyzeResult']['documents'][0]['docType'],
                                "fields" => $data['analyzeResult']['documents'][0]['fields']
                            ];
                            break;
                        } else {
                            //an error occurred
                            $error = "error-4";
                            break;
                        }
                    }
                    if (!$extractedData && !$error) {
                        //time elapsed
                        $error = "error-3";
                    }
                }
                if (!$extractedData && !$error) {
                    //a very strange error occurred
                    $error = $error ?? "error-1";
                }
            } else {
                $error = "error-5";
            }
        } catch (Exception $e) {
            $error = "error-unknown";
        } finally {
            unlink($tmpFile);
            if ($extractedData) {
                return $extractedData;
            } else {
                $this->logger->debug("Azure Document recognition error " . $error);
                throw new WedofBadRequestHttpException($error ? "Document recognition error $error" : "Document recognition error");
            }
        }
    }

    /**
     * @param array|null $data
     * @return array
     */
    public function formatSocialSecurityCardData(?array $data): array
    {
        $firstName = null;
        $lastName = null;
        $socialSecurityNumber = null;
        if (!empty($data['fields']['firstName']['content'])) {
            $firstName = trim($data['fields']['firstName']['content']);
        }
        if (!empty($data['fields']['lastName']['content'])) {
            $lastName = trim($data['fields']['lastName']['content']);
        }
        if (!empty($data['fields']['socialSecurityNumber']['content'])) {
            $socialSecurityNumber = str_replace(" ", "", trim($data['fields']['socialSecurityNumber']['content']));
        }
        return [
            "firstName" => $firstName,
            "lastName" => $lastName,
            "nir" => $socialSecurityNumber
        ];
    }

    /**
     * @param array|null $data
     * @return array
     */
    public function formatDataAsUserData(?array $data): array
    {
        $codeCityOfBirth = null;
        $nameCityOfBirth = null;
        $postalCode = null;
        if (!empty($data['fields']['PlaceOfBirth']['content'])) {
            $city = $this->cityService->getByName(trim($data['fields']['PlaceOfBirth']['content']));
            if (!empty($city)) {
                $codeCityOfBirth = $city->getCog();
                $nameCityOfBirth = $city->getName();
                $postalCode = $city->getPostalCode();
            }
        }
        $codeCountryOfBirth = null;
        $nameCountryOfBirth = null;
        if (!empty($data['fields']['CountryRegion']['content'])) {
            $country = Tools::findCountry(null, null, trim($data['fields']['CountryRegion']['content']));
            if ($country) {
                $codeCountryOfBirth = $country['cog'];
                $nameCountryOfBirth = $country['name'];
            }
        }
        $dateOfBirth = null;
        if (!empty($data['fields']["DateOfBirth"]['valueDate'])) {
            $dateOfBirth = $data['fields']["DateOfBirth"]['valueDate'];
        }
        $gender = null;
        if (!empty($data['fields']['Sex']['content'])) {
            $gender = $data['fields']['Sex']['content'] === "M" ? AttendeeGender::MALE()->getValue() : ($data['fields']['Sex']['content'] === "F" ? AttendeeGender::FEMALE()->getValue() : null);
        }
        $firstNames = explode(",", str_replace('*', '', $data['fields']['FirstName']['content']));
        $lastNames = explode("ép.", str_replace('*', '', $data['fields']['LastName']['content']));
        return [
            "firstName" => !empty($firstNames[0]) ? trim($firstNames[0]) : null,
            "firstName2" => !empty($firstNames[1]) ? trim($firstNames[1]) : null,
            "firstName3" => !empty($firstNames[2]) ? trim($firstNames[2]) : null,
            "lastName" => !empty($lastNames[1]) ? trim($lastNames[1]) : (!empty($lastNames[0]) ? trim($lastNames[0]) : null),
            "birthName" => !empty($lastNames[0]) ? trim($lastNames[0]) : null,
            "gender" => $gender,
            "dateOfBirth" => $dateOfBirth,
            "nameCityOfBirth" => $codeCountryOfBirth === 100 ? $nameCityOfBirth : null,
            "codeCityOfBirth" => $codeCountryOfBirth === 100 ? $codeCityOfBirth : null,
            "postalCode" => $codeCountryOfBirth === 100 ? $postalCode : null,
            "nameCountryOfBirth" => $codeCountryOfBirth !== 100 ? $nameCountryOfBirth : null,
            "codeCountryOfBirth" => $codeCountryOfBirth !== 100 ? $codeCountryOfBirth : null
        ];
    }
}