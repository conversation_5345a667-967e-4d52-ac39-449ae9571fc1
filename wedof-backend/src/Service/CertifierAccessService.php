<?php
// src/Service/CertifierAccessService.php
namespace App\Service;

use App\Entity\Certification;
use App\Entity\CertifierAccess;
use App\Entity\Organism;
use App\Entity\User;
use App\Exception\WedofBadRequestHttpException;
use App\Library\utils\enums\ActivityTypes;
use App\Library\utils\enums\CertifierAccessStates;
use App\Library\utils\enums\SubscriptionCertifierTypes;
use App\Library\utils\Tools;
use App\Repository\CertifierAccessRepository;
use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\QueryBuilder;
use Symfony\Component\DependencyInjection\ContainerInterface as Container;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Throwable;

class CertifierAccessService
{
    private CertifierAccessRepository $certifierAccessRepository;
    private ActivityService $activityService;
    private CertificationPartnerService $certificationPartnerService;
    private MailerService $mailerService;
    private Container $container;

    public function __construct(CertifierAccessRepository $certifierAccessRepository, ActivityService $activityService, CertificationPartnerService $certificationPartnerService, MailerService $mailerService, Container $container)
    {
        $this->certificationPartnerService = $certificationPartnerService;
        $this->certifierAccessRepository = $certifierAccessRepository;
        $this->activityService = $activityService;
        $this->mailerService = $mailerService;
        $this->container = $container;
    }

    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    /**
     * @param CertifierAccess $certifierAccess
     * @param User $user
     * @param array $parameters
     * @return CertifierAccess
     * @throws ClientExceptionInterface
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws \Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface
     */
    public function create(CertifierAccess $certifierAccess, User $user, array $parameters = []): CertifierAccess
    {
        $oldCertifierAccess = $this->certifierAccessRepository->findOneByOrganisms($certifierAccess->getCertifier(), $certifierAccess->getPartner(), 'any');
        if ($oldCertifierAccess) {
            if ($oldCertifierAccess->getState() === CertifierAccessStates::ACCEPTED()->getValue()) {
                throw new WedofBadRequestHttpException("Erreur, l'accès est déjà accepté, il ne peut donc pas être recréé.");
            }
            $this->certifierAccessRepository->delete($oldCertifierAccess);
        }
        if (!empty($parameters['sendInvitation'])) {
            if (!empty($parameters['recipientEmail'])) {
                $parameters['recipientEmail'] = str_replace(' ', '', $parameters['recipientEmail']);
                if (Tools::contains($parameters['recipientEmail'], ';')) {
                    $parameters['recipientEmail'] = explode(";", $parameters['recipientEmail']);
                } else if (Tools::contains($parameters['recipientEmail'], ',')) {
                    $parameters['recipientEmail'] = explode(',', $parameters['recipientEmail']);
                } else {
                    $parameters['recipientEmail'] = [$parameters['recipientEmail']];
                }
            } else {
                $parameters['recipientEmail'] = [$certifierAccess->getPartner()->getEmails()[0]];
            }
            $recipientEmail = $parameters['recipientEmail'][0];
            $recipientName = !empty($parameters['recipientName']) ? $parameters['recipientName'] : '';
            $certifierAccess->setEmail($recipientEmail);
            $certifierAccess->setFullName($recipientName);
        } else {
            $parameters['recipientEmail'] = null;
        }
        $certifierAccess->setToken($this->generateToken($certifierAccess));
        $this->certifierAccessRepository->save($certifierAccess);
        //send invitation after save certifierAccess object
        if ($certifierAccess->getCertifier() === $certifierAccess->getPartner()) {
            $certifierAccess = $this->activate($certifierAccess, $user);
        } else if (!empty($parameters['recipientEmail'])) {
            //$parameters['recipientEmail'] is an array
            $this->mailerService->sendInvitation($certifierAccess, $parameters['recipientEmail']);
        }
        return $certifierAccess;
    }

    /**
     * @param CertifierAccess $certifierAccess
     * @param User $user
     * @return CertifierAccess
     * @throws NonUniqueResultException
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws NoResultException
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws \Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface
     */
    public function activate(CertifierAccess $certifierAccess, User $user): CertifierAccess
    {
        $currentDate = new DateTime();
        $certifierAccess->setActivatedOn($currentDate);
        $certifierAccess->setTerminatedOn(null);
        $certifierAccess->setToken(null);
        $this->certifierAccessRepository->save($certifierAccess);
        $this->createActivity($certifierAccess, $user);
        $this->mailerService->sendAcceptInvitation($certifierAccess);
        $partner = $certifierAccess->getPartner();
        if ($partner->getSubscription()->getCertifierType() === SubscriptionCertifierTypes::NONE()->getValue()) {
            /** @var SubscriptionService $subscriptionService */
            $subscriptionService = $this->container->get("App\Service\SubscriptionService");
            $subscriptionService->switch($partner->getSubscription(), ['certifier' => ['type' => SubscriptionCertifierTypes::PARTNER()->getValue()]]);
        }
        /** @var CertificationFolderService $certificationFolderService */
        $certificationFolderService = $this->container->get("App\Service\CertificationFolderService");
        $certificationFolderService->createFromRegistrationFoldersForPartner($certifierAccess->getCertifier(), $partner);
        return $certifierAccess;
    }

    /**
     * @param CertifierAccess $certifierAccess
     * @return CertifierAccess
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    public function refuse(CertifierAccess $certifierAccess): CertifierAccess
    {
        $currentDate = new DateTime();
        $certifierAccess->setTerminatedOn($currentDate);
        $certifierAccess->setToken(null);
        $this->certifierAccessRepository->save($certifierAccess);
        $this->createActivity($certifierAccess, null);
        $this->mailerService->sendRefuseInvitation($certifierAccess);
        return $certifierAccess;
    }

    /**
     * @param CertifierAccess $certifierAccess
     * @param User $user
     * @param bool $isCertifier
     * @return CertifierAccess
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     * @throws TransportExceptionInterface
     */
    public function terminate(CertifierAccess $certifierAccess, User $user, bool $isCertifier): CertifierAccess
    {
        $currentDate = new DateTime();
        $certifierAccess->setTerminatedOn($currentDate);
        $this->certifierAccessRepository->save($certifierAccess);
        $this->createActivity($certifierAccess, $user, $isCertifier);
        if ($isCertifier) {
            $this->mailerService->sendTerminateInvitationToPartner($certifierAccess);
        } else {
            $this->mailerService->sendTerminateInvitationToCertifier($certifierAccess);
        }
        return $certifierAccess;
    }

    /**
     * @param Organism $organism
     * @param array $parameters
     * @return QueryBuilder
     */
    public function listReturnQueryBuilder(Organism $organism, array $parameters): QueryBuilder
    {
        return $this->certifierAccessRepository->findAllReturnQueryBuilder($organism, $parameters);
    }

    /**
     * @param Certification $certification
     * @param Organism $organism
     * @param string $state
     * @return CertifierAccess|null
     * @throws NonUniqueResultException
     */
    public function getByCertificationAndPartner(Certification $certification, Organism $organism, string $state = 'active'): ?CertifierAccess
    {
        if ($certification->isCertifier($organism)) {
            $certifierAccess = new CertifierAccess();
            $certifierAccess->setCertifier($organism);
            $certifierAccess->setPartner($organism);
            $certifierAccess->setActivatedOn(new DateTime());
            return $certifierAccess;
        }
        return $this->certifierAccessRepository->findOneByCertificationAndPartner($certification, $organism, $state);
    }

    /**
     * @param Organism $certifier
     * @param Organism $partner
     * @param string $state
     * @return CertifierAccess|null
     * @throws NonUniqueResultException
     */
    public function getByOrganisms(Organism $certifier, Organism $partner, string $state = 'active'): ?CertifierAccess
    {
        return $this->certifierAccessRepository->findOneByOrganisms($certifier, $partner, $state);
    }

    /**
     * @param Organism $certifier
     * @param string $state
     * @return ArrayCollection
     */
    public function listForCertifier(Organism $certifier, string $state = 'active'): ArrayCollection
    {
        return $this->certifierAccessRepository->findAllForCertifier($certifier, $state);
    }

    /**
     * @param Organism $partner
     * @param string $state
     * @return ArrayCollection
     */
    public function listForPartner(Organism $partner, string $state = 'active'): ArrayCollection
    {
        return $this->certifierAccessRepository->findAllForPartner($partner, $state);
    }

    /**
     * @param Organism $organism
     * @param string $state
     * @return ArrayCollection
     */
    public function listForOrganism(Organism $organism, string $state = 'active'): ArrayCollection
    {
        return $this->certifierAccessRepository->findAllForOrganism($organism, $state);
    }

    //-------------------
    // METHODES PRIVEES
    //-------------------

    /**
     * @param CertifierAccess $certifierAccess
     * @return string
     */
    private function generateToken(CertifierAccess $certifierAccess): string
    {
        return md5(date('l jS \of F Y h:i:s A') . $certifierAccess->getPartner()->getSiret() . $certifierAccess->getCertifier()->getSiret());
    }

    /**
     * @param CertifierAccess $certifierAccess
     * @param User|null $user
     * @param bool $isCertifier
     * @return void
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    private function createActivity(CertifierAccess $certifierAccess, ?User $user, bool $isCertifier = false)
    {
        $title = CertifierAccessStates::ACCEPTED()->equals($certifierAccess->getState()) ? "Le partage des données a été accepté." :
            (CertifierAccessStates::REFUSED()->equals($certifierAccess->getState()) ? "Le partage des données a été refusé." : "Le partage des données a été révoqué.");

        $certificationPartners = $this->certificationPartnerService->listByOrganisms($certifierAccess->getCertifier(), $certifierAccess->getPartner());
        foreach ($certificationPartners as $certificationPartner) {
            $origin = 'Partenaire';
            if ($isCertifier) {
                $origin = 'Certificateur';
            } else if ($user) {
                $origin = null;
            }
            $this->activityService->create([
                'title' => $title,
                'type' => ActivityTypes::UPDATE()->getValue(),
                'eventTime' => new DateTime(),
                'origin' => $origin
            ], $user, $certificationPartner, false);
        }
    }
}
