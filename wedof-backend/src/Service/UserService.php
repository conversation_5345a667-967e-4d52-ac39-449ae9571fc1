<?php

namespace App\Service;

use App\Entity\Organism;
use App\Entity\User;
use App\Event\User\UserEvents;
use App\Exception\WedofBadRequestHttpException;
use App\Library\utils\Tools;
use App\Repository\UserRepository;
use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\QueryBuilder;
use Exception;
use ReflectionException;
use ReflectionProperty;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Security\Http\LoginLink\LoginLinkHandlerInterface;

class UserService
{
    private UserRepository $userRepository;
    private UserPasswordHasherInterface $passwordHasher;
    private OrganismService $organismService;
    private ApiTokenService $apiTokenService;
    private MailerService $mailer;
    private EntityManagerInterface $entityManager;
    private EventDispatcherInterface $dispatcher;
    private LoginLinkHandlerInterface $loginLinkHandler;

    const DEFAULT_FILTERS = '[
        {
          "link": "state=success&filterOnStateDate=stateLastUpdate&cdcCompliant=false&cdcExcluded=false",
          "name": "⚠️ Données accrochage manquantes",
          "color": "#9671a8",
          "entityClass": "CertificationFolder"
        },
        {
          "link": "filterOnStateDate=stateLastUpdate&cdcState=processedKo",
          "name": "🚨 Accrochés avec erreur",
          "color": "#f89ba9",
          "entityClass": "CertificationFolder"
        },
        {
          "link": "filterOnStateDate=lastUpdate&controlState=inControl",
          "name": "🕵🏻 En contrôle",
          "color": "#f89ba9",
          "entityClass": "RegistrationFolder"
        },
        {
          "link": "billingState=billed&filterOnStateDate=lastUpdate&controlState=",
          "name": "🧾 Facturés",
          "color": "#e2d7d0",
          "entityClass": "RegistrationFolder"
        },
        {
          "link": "billingState=paid&filterOnStateDate=lastUpdate",
          "name": "💰 Payés",
          "color": "#d2ffdf",
          "entityClass": "RegistrationFolder"
        }
    ]';

    public function __construct(
        LoginLinkHandlerInterface   $loginLinkHandler,
        EventDispatcherInterface    $dispatcher,
        UserRepository              $userRepository,
        UserPasswordHasherInterface $passwordEncoder,
        OrganismService             $organismService,
        ApiTokenService             $apiTokenService,
        MailerService               $mailer,
        EntityManagerInterface      $entityManager
    )
    {
        $this->loginLinkHandler = $loginLinkHandler;
        $this->userRepository = $userRepository;
        $this->passwordHasher = $passwordEncoder;
        $this->organismService = $organismService;
        $this->apiTokenService = $apiTokenService;
        $this->mailer = $mailer;
        $this->entityManager = $entityManager;
        $this->dispatcher = $dispatcher;
    }

    //-------------------
    // METHODES PUBLIQUES
    //-------------------
    /**
     * @param User $user
     * @param bool $sendAlertOnCreation
     * @return User
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Exception
     */
    public function create(User $user, bool $sendAlertOnCreation): User
    {
        $user->setRoles(array_filter($user->getRoles(), function ($value) {
            return !in_array($value, ['ROLE_ADMIN', 'ROLE_ALLOWED_TO_SWITCH']);
        }));
        $existingUser = $this->userRepository->findOneByEmail($user->getEmail());
        if ($existingUser) {
            if ($existingUser->getRgpdMsa()) {
                throw new WedofBadRequestHttpException("Erreur, l'utilisateur associé à l'email " . $user->getEmail() . " existe déjà.");
            } else { // possibilité d'écraser un utilisateur qui n'a pas fini son inscription
                if ($existingUser->getMainOrganism()) {
                    $existingUser->getMainOrganism()->setOwnedBy(null);
                }
                // This will not work in one case: the user has been revoked after having activities on an organism
                // Indeed, revocation sets RgpdMsa at null so we try deleting it
                // But foreign key constraints prevent deletion
                $this->userRepository->delete($existingUser);
            }
        }
        $user->setFirstName(Tools::toTitleCase($user->getFirstName()));
        $user->setLastName(strtoupper($user->getLastName()));
        $password = $user->getPassword() ?? Tools::generateRandomString(12);
        $this->setPassword($user, $password);
        $this->save($user);
        $this->apiTokenService->create($user, "Jeton principal");
        $this->entityManager->refresh($user); //be sure relations are set..
        if ($sendAlertOnCreation) {
            $this->dispatcher->dispatch(new UserEvents($user), UserEvents::CREATED);
        }
        return $user;
    }

    /**
     * @param User $user
     * @param array $body
     * @return User
     */
    public function update(User $user, array $body): User
    {
        if (key_exists('email', $body)) {
            if ($body['email'] != $user->getEmail() && !empty($this->getByEmail($body['email']))) {
                throw new WedofBadRequestHttpException("Erreur, l'utilisateur associé à l'email " . $body['email'] . " existe déjà.");
            }
        }
        if (!empty($body['roles'])) {
            $rolesToExclude = [];
            if (!in_array('ROLE_ADMIN', $user->getRoles())) {
                $rolesToExclude[] = 'ROLE_ADMIN';
            }
            if (!in_array('ROLE_ALLOWED_TO_SWITCH', $user->getRoles())) {
                $rolesToExclude[] = 'ROLE_ALLOWED_TO_SWITCH';
            }
            $user->setRoles(array_filter($body['roles'], function ($value) use ($rolesToExclude) {
                return !in_array($value, $rolesToExclude);
            }));
        }
        $properties = array('email', 'firstName', 'lastName', 'phoneNumber', 'address');
        foreach ($properties as $property) {
            if (key_exists($property, $body)) {
                $setMethodName = "set" . ucwords($property);
                $user->{$setMethodName}($body[$property]);
            }
        }
        if (key_exists('password', $body)) {
            $this->setPassword($user, $body['password']);
        }
        if (!empty($body['mainOrganism']['siret'])) {
            if (empty($user->getMainOrganism())) {
                /** @var Organism $organism */
                $organism = $this->organismService->getBySiret($body['mainOrganism']['siret']);
                if (!$organism->getOwnedBy()) {
                    $user->setOwnedOrganism($organism);
                    $user->setMainOrganism($organism);
                    $user->setFilters([$user->getMainOrganism()->getSiret() => json_decode(self::DEFAULT_FILTERS)]);
                    $this->save($user);
                    $this->entityManager->refresh($user);
                } else {
                    throw new WedofBadRequestHttpException("Seul le propriétaire peut ajouter un utilisateur à cet organisme.");
                }
            } else {
                throw new WedofBadRequestHttpException("Erreur, l'utilisateur " . $user->getEmail() . " est déjà associé à un organisme principal.");
            }
        }
        if (key_exists('rgpdMsa', $body) && $user->getRgpdMsa() === null) {
            $user->setRgpdMsa($body['rgpdMsa']);
            $this->dispatcher->dispatch(new UserEvents($user), UserEvents::SIGN_UP_COMPLETE);
        }
        if (isset($body['filters'])) {
            $userFilters = $user->getFilters();
            $userFilters[$user->getMainOrganism()->getSiret()] = $body['filters'];
            $user->setFilters($userFilters);
        }

        return $this->save($user);
    }

    /**
     * @param User $user
     * @param User $invitedUser
     * @param bool $resetPassword
     * @return User
     * @throws Exception
     * @throws TransportExceptionInterface
     */
    public function inviteUser(User $user, User $invitedUser, bool $resetPassword): User
    {
        $invitedUser->setMainOrganism($user->getOwnedOrganism());
        $invitedUser->setRgpdMsa(new DateTime('now'));
        $invitedUser->setFilters([$user->getMainOrganism()->getSiret() => json_decode(self::DEFAULT_FILTERS)]);
        $newPassword = null;
        if ($resetPassword) {
            $newPassword = Tools::generateRandomString(12);
            $this->setPassword($invitedUser, $newPassword);
        }
        $this->mailer->sendInvitationToNewUser($user, $invitedUser, $user->getOwnedOrganism()->getName(), $newPassword);
        $this->mailer->sendConfirmationInvitedUserToAdmin($user, $invitedUser);
        $invitedUser = $this->save($invitedUser);
        $this->dispatcher->dispatch(new UserEvents($user), UserEvents::INVITED);
        return $invitedUser;
    }

    /**
     * @param User $invitedUser
     * @return User
     */
    public function revokedUser(User $invitedUser): User
    {
        $invitedUser->setMainOrganism(null);
        $invitedUser->setRgpdMsa(null);
        return $this->save($invitedUser);
    }

    /**
     * @return ArrayCollection|null
     */
    public function list(): ?ArrayCollection
    {
        return $this->userRepository->findAllUsers();
    }

    /**
     * @param array $parameters
     * @return QueryBuilder
     */
    public function listReturnQueryBuilder(array $parameters): QueryBuilder
    {
        return $this->userRepository->findAllReturnQueryBuilder($parameters);
    }

    /**
     * @param string $siret
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countBySiret(string $siret): int
    {
        return $this->userRepository->countBySiret($siret);
    }

    /**
     * @param string $email
     * @return User|null
     */
    public function getByEmail(string $email): ?User
    {
        return $this->userRepository->findOneByEmail($email);
    }

    /**
     * @param int $id
     * @return User|null
     */
    public function getById(int $id): ?User
    {
        return $this->userRepository->findOneById($id);
    }

    /**
     * @param User $user
     * @return void
     * @throws TransportExceptionInterface
     * @throws Exception
     */
    public function forgotPassword(User $user): void
    {
        $newPassword = Tools::generateRandomString(12);
        $this->setPassword($user, $newPassword);
        $this->save($user);
        $this->mailer->sendNewPassword($user, $newPassword);
    }

    /**
     * @param User $user
     * @return void
     * @throws TransportExceptionInterface
     * @throws Exception
     */
    public function sendMagicLink(User $user): void
    {
        $this->mailer->sendMagicLink($user, $this->generateMagicLink($user));
    }

    /**
     * @throws ReflectionException
     */
    public function generateMagicLink(User $user, $lifetime = null)
    {
        $reflection = new ReflectionProperty($this->loginLinkHandler, "options");
        $reflection->setAccessible(true);
        $options = $reflection->getValue($this->loginLinkHandler);
        if ($lifetime) {
            $reflection->setValue($this->loginLinkHandler, array_merge($options, ['lifetime' => $lifetime]));
        }

        $magicLink = $this->loginLinkHandler->createLoginLink($user)->getUrl();

        if ($lifetime) {
            $reflection->setValue($this->loginLinkHandler, $options);
        }
        //replace app/magic-link-auth => to auth/magic-link-auth to handle auth using angular
        return str_replace('app/', 'auth/', $magicLink);
    }

    /**
     * @param User $user
     * @return User
     */
    public function save(User $user): User
    {
        return $this->userRepository->save($user);
    }

    //----------------
    // METHODES PRIVES
    //----------------
    /**
     * @param User $user
     * @param string $password
     * @return void
     */
    private function setPassword(User $user, string $password): void
    {
        $user->setPassword($this->passwordHasher->hashPassword($user, $password));
    }
}
