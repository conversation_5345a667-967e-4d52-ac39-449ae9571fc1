<?php

// src/Service/CdcXMLService.php
namespace App\Service;

use App\Entity\Organism;
use App\Entity\User;
use App\Exception\WedofBadRequestHttpException;
use App\Library\utils\enums\CertificationFolderCdcStates;
use App\Library\utils\enums\CertificationFolderGradePass;
use App\Library\utils\enums\CertificationFolderStates;
use App\Library\utils\enums\CertificationTypes;
use App\Library\utils\Tools;
use DateTime;
use DateTimeInterface;
use DateTimeZone;
use DOMDocument;
use Exception;
use SimpleXMLElement;
use Throwable;

class CdcXMLService
{
    private CdcFileService $cdcFileService;
    private CertificationService $certificationService;
    private CertificationFoldersCdcFilesService $certificationFoldersCdcFilesService;
    private CertificationFolderService $certificationFolderService;

    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    public function __construct(
        CdcFileService                      $cdcFileService,
        CertificationService                $certificationService,
        CertificationFoldersCdcFilesService $certificationFoldersCdcFilesService,
        CertificationFolderService          $certificationFolderService
    )
    {
        $this->cdcFileService = $cdcFileService;
        $this->certificationService = $certificationService;
        $this->certificationFoldersCdcFilesService = $certificationFoldersCdcFilesService;
        $this->certificationFolderService = $certificationFolderService;
    }

    /**
     * @param array $cdcData
     * @param String $idClient
     * @param string $idClientDepositor
     * @param String $idContrat
     * @param string $idFlux
     * @return string
     * @throws Exception
     */
    public function generateCdcXML(array $cdcData, string $idClient, string $idClientDepositor, string $idContrat, string $idFlux): string
    {
        function setNodeData(SimpleXMLElement $xParent, array $data, array $elementNames)
        {
            foreach ($elementNames as $elementName) {
                $xParent->{$elementName} = $data[$elementName]; // dynamic property (->) is used because it properly escapes '&' whereas addChild() does not
            }
        }

        $xmlRoot = new SimpleXMLElement('<cpf:flux xmlns:cpf="urn:cdc:cpf:pc5:schema:1.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"></cpf:flux>');
        $xmlRoot->idFlux = $idFlux;
        $now = new DateTime();
        $xmlRoot->horodatage = $now->format(DateTimeInterface::ATOM);
        $xEmetteur = $xmlRoot->addChild('emetteur');
        $xEmetteur->idClient = $idClientDepositor; // idClient de l'entité qui dépose le fichier
        $xCertificateurs = $xEmetteur->addChild('certificateurs');
        $xCertificateur = $xCertificateurs->addChild('certificateur');
        $xCertificateur->idClient = $idClient; // idClient du certificateur
        $xCertificateur->idContrat = $idContrat;
        $xCertifications = $xCertificateur->addChild('certifications');
        foreach ($cdcData as $certificationContainer) {
            $certificationData = $certificationContainer['certificationData'];
            $xCertification = $xCertifications->addChild('certification');
            setNodeData($xCertification, $certificationData, ['type', 'code', 'natureDeposant']);
            $xPassagesCertifications = $xCertification->addChild('passageCertifications');
            foreach ($certificationData['certificationFoldersData'] as $certificationFolderData) {
                $xPassageCertification = $xPassagesCertifications->addChild('passageCertification');
                setNodeData($xPassageCertification, $certificationFolderData, ['idTechnique']);
                if ($certificationFolderData['urlPreuve']) {
                    setNodeData($xPassageCertification, $certificationFolderData, ['urlPreuve']);
                }
                if ($certificationFolderData['libelleOption']) {
                    setNodeData($xPassageCertification, $certificationFolderData, ['libelleOption']);
                }
                setNodeData($xPassageCertification, $certificationFolderData, ['obtentionCertification', 'donneeCertifiee']);
                if ($certificationFolderData['dateDebutExamen']) {
                    setNodeData($xPassageCertification, $certificationFolderData, ['dateDebutExamen']);
                }
                if ($certificationFolderData['dateFinExamen']) {
                    setNodeData($xPassageCertification, $certificationFolderData, ['dateFinExamen']);
                }
                if ($certificationFolderData['modalitePassageExamen']) {
                    setNodeData($xPassageCertification, $certificationFolderData, ['modalitePassageExamen']);
                }
                if ($certificationFolderData['codePostalCentreExamen']) {
                    setNodeData($xPassageCertification, $certificationFolderData, ['codePostalCentreExamen']);
                }
                setNodeData($xPassageCertification, $certificationFolderData, ['dateDebutValidite']);
                $xValidityEnd = $xPassageCertification->addChild('dateFinValidite', $certificationFolderData['dateFinValidite']);
                if (!$certificationFolderData['dateFinValidite']) {
                    $xValidityEnd->addAttribute('fakeNamespace:xsi:nil', 'true');
                }
                setNodeData($xPassageCertification, $certificationFolderData, ['presenceNiveauLangueEuro']);
                if ($certificationFolderData['niveauLangueEuropeen']) {
                    setNodeData($xPassageCertification, $certificationFolderData, ['niveauLangueEuropeen']);
                }
                setNodeData($xPassageCertification, $certificationFolderData, ['presenceNiveauNumeriqueEuro']);
                $xScoring = $xPassageCertification->addChild('scoring', $certificationFolderData['scoring']);
                if ($certificationFolderData['scoring'] === null || strlen(trim($certificationFolderData['scoring'])) === 0) {
                    $xScoring->addAttribute('fakeNamespace:xsi:nil', 'true');
                }
                $xMentionValidee = $xPassageCertification->addChild('mentionValidee', $certificationFolderData['mentionValidee']);
                if (!$certificationFolderData['mentionValidee']) {
                    $xMentionValidee->addAttribute('fakeNamespace:xsi:nil', 'true');
                }
                if ($certificationFolderData['verbatim']) {
                    setNodeData($xPassageCertification, $certificationFolderData, ['verbatim']);
                }
                $xModalitesInscription = $xPassageCertification->addChild('modalitesInscription');
                $xModaliteAcces = $xModalitesInscription->addChild('modaliteAcces', $certificationFolderData['modalitesInscription']['modaliteAcces']);
                if (!$certificationFolderData['modalitesInscription']['modaliteAcces']) {
                    $xModaliteAcces->addAttribute('fakeNamespace:xsi:nil', 'true');
                }
                if ($certificationFolderData['modalitesInscription']['voieAccessVAE']) {
                    $xModalitesInscription->voieAccessVAE = $certificationFolderData['modalitesInscription']['voieAccessVAE'];
                }
                if ($certificationFolderData['modalitesInscription']['initiativeInscription']) {
                    $xModalitesInscription->initiativeInscription = $certificationFolderData['modalitesInscription']['initiativeInscription'];
                }
                if ($certificationFolderData['modalitesInscription']['dateInscription']) {
                    $xModalitesInscription->dateInscription = $certificationFolderData['modalitesInscription']['dateInscription'];
                }
                $xIdentificationTitulaire = $xPassageCertification->addChild('identificationTitulaire');
                if (isset($certificationFolderData['dossierFormation'])) {
                    $xDossierFormation = $xIdentificationTitulaire->addChild('dossierFormation');
                    setNodeData($xDossierFormation, $certificationFolderData['dossierFormation'], ['idDossier', 'nomTitulaire', 'prenom1Titulaire']);
                } else {
                    $xTitulaire = $xIdentificationTitulaire->addChild('titulaire');
                    setNodeData($xTitulaire, $certificationFolderData['titulaire'], ['nomNaissance', 'nomUsage', 'prenom1']);
                    if ($certificationFolderData['titulaire']['prenom2']) {
                        setNodeData($xTitulaire, $certificationFolderData['titulaire'], ['prenom2']);
                    }
                    if ($certificationFolderData['titulaire']['prenom3']) {
                        setNodeData($xTitulaire, $certificationFolderData['titulaire'], ['prenom3']);
                    }
                    setNodeData($xTitulaire, $certificationFolderData['titulaire'], ['anneeNaissance', 'moisNaissance', 'jourNaissance', 'sexe']);
                    $xCodeCommuneNaissance = $xTitulaire->addChild('codeCommuneNaissance');
                    $xCodeInseeNaissance = $xCodeCommuneNaissance->addChild('codeInseeNaissance');
                    $xCodeInseeNaissance->codeInsee = $certificationFolderData['titulaire']['codeInsee'];
                    if ($certificationFolderData['titulaire']['libelleCommuneNaissance']) {
                        setNodeData($xTitulaire, $certificationFolderData['titulaire'], ['libelleCommuneNaissance']);
                    }
                    if ($certificationFolderData['titulaire']['codePaysNaissance']) {
                        setNodeData($xTitulaire, $certificationFolderData['titulaire'], ['codePaysNaissance']);
                    }
                    if ($certificationFolderData['titulaire']['libellePaysNaissance']) {
                        setNodeData($xTitulaire, $certificationFolderData['titulaire'], ['libellePaysNaissance']);
                    }
                }
            }
        }
        $dom = new DOMDocument("1.0", "UTF-8"); //encoding will be overrided by loadXML but let it there for knowning purpose
        $dom->preserveWhiteSpace = false;
        $dom->formatOutput = true;
        $dom->loadXML($xmlRoot->asXML());
        $dom->encoding = "UTF-8"; //add back encoding
        return $dom->saveXML();
    }

    /**
     * @param SimpleXMLElement $simpleElementXml
     * @param Organism $certifier
     * @param User $user
     * @param string $fileName
     * @return array
     * @throws Throwable
     */
    public function ingestCdcXML(SimpleXMLElement $simpleElementXml, Organism $certifier, User $user, string $fileName): array
    {
        $_SERVER['NO_DISPATCH_WEDOF_EVENTS'] = true;

        $simpleElementXmlFlux = $simpleElementXml->children('cpf', true);
        $horodatage = ((new DateTime($simpleElementXmlFlux->horodatage))->setTimezone(new DateTimeZone('GMT')));
        $idFlux = (string)$simpleElementXmlFlux->idFlux;

        $certificationFoldersCdcFileList = [];
        $cdcFile = $this->cdcFileService->findOneByIdFlux($idFlux);
        $externalPrefix = 'external-';
        $fullFileName = $externalPrefix . $fileName;
        if (!$cdcFile) {
            $cdcFile = $this->cdcFileService->create($fullFileName, [], $certifier, $idFlux, false, true, $horodatage);
        } else if ($cdcFile->getName() != $fullFileName) {
            $existingFileName = Tools::removePrefix($cdcFile->getName(), $externalPrefix);
            throw new WedofBadRequestHttpException("Erreur, le fichier fourni n'a pas le même nom qu'un fichier déjà fourni à Wedof avec le même idFlux, idFlux:  " . $idFlux . ", fichier déjà fourni : " . $existingFileName . ".xml");
        } else {
            $certificationFoldersCdcFile = $cdcFile->getCertificationFoldersCdcFiles();
            foreach ($certificationFoldersCdcFile as $certificationFolderCdcFile) {
                // id des dossiers déjà importés pour ce XML
                $certificationFoldersCdcFileList[] = $certificationFolderCdcFile->getCertificationFolder()->getId();
            }
        }

        $certificationsNode = $simpleElementXmlFlux->emetteur->certificateurs->certificateur->certifications->children('cpf', true);
        $ingestionResults = [
            'idFlux' => $idFlux,
            'ok' => [],
            'ko' => [],
            'dejaTraites' => [],
            'certificationsNonTrouvees' => [],
            'mauvaisCertificateur' => []
        ];

        foreach ($certificationsNode as $certificationNode) {
            $type = CertificationTypes::from(strtoupper($certificationNode->type));
            $code = str_replace($type, '', $certificationNode->code);
            $certification = $this->certificationService->getByTypeAndCode($type, $code);
            if (!$certification || !$certification->isCertifier($certifier)) {
                $ingestionResults['certificationsNonTrouvees'][] = $type . $code;
                continue;
            }
            $passageCertificationsNode = $certificationNode->passageCertifications->children('cpf', true);
            if (!$certification->getObtentionSystem()) {
                $this->certificationService->update($certification, $certifier, ['obtentionSystem' => $passageCertificationsNode[0]->obtentionCertification]);
            }
            foreach ($passageCertificationsNode as $passageCertificationNode) {
                $identificationTitulaireNode = $passageCertificationNode->identificationTitulaire;
                $technicalIdFromXml = (string)$passageCertificationNode->idTechnique;
                if ($identificationTitulaireNode->dossierFormation) {
                    // Try to find a folder that matches external id + certification
                    $dossierFormationNode = $identificationTitulaireNode->dossierFormation;
                    $certificationFolder = $this->certificationFolderService->getByRegistrationFolderExternalId($dossierFormationNode->idDossier);
                    if (!$certificationFolder) {
                        $ingestionResults['ko'][] = [
                            "idTechnique" => $technicalIdFromXml,
                            'erreur' => 'Aucun dossier de certification trouvé pour le numéro de dossier CPF',
                            "registrationFolderExternalId" => (string)$dossierFormationNode->idDossier
                        ];
                        continue;
                    }
                    if ($certificationFolder->getCertification() !== $certification) {
                        $ingestionResults['ko'][] = [
                            "idTechnique" => $technicalIdFromXml,
                            "erreur" => 'La certification du dossier de certification ne correspond pas à celle du XML',
                            "certificationIdFromCertificationFolder" => $certificationFolder->getCertification()->getId(),
                            "certificationIdFromXml" => $certification->getId(),
                            "registrationFolderExternalId" => $certificationFolder->getRegistrationFolderExternalId()
                        ];
                        continue;
                    }
                    $matchedBy = 'Dossier trouvé par numéro de dossier CPF';
                } else {
                    // Try to find 1 folder that matches technicalId + certification (if more than 1 => error)
                    $certificationFolders = $this->certificationFolderService->listByCertificationAndTechnicalId($certification, $technicalIdFromXml);
                    if ($certificationFolders->count() > 1) {
                        $ingestionResults['ko'][] = [
                            "idTechnique" => $technicalIdFromXml,
                            'erreur' => "Plusieurs dossiers de certification ont été trouvés sur cette certification pour cet idTechnique",
                        ];
                        continue;
                    }
                    if ($certificationFolders->count() === 1) {
                        $certificationFolder = $certificationFolders->get(0);
                        $matchedBy = 'Dossier trouvé par idTechnique / cdcTechnicalId';
                    } else {
                        // Try to find 1 folder that matches first name + last name + certification (if more than 1 => error)
                        $titulaireNode = $identificationTitulaireNode->titulaire;
                        $firstName = (string)$titulaireNode->prenom1;
                        $lastName = (string)$titulaireNode->nomUsage ?: (string)$titulaireNode->nomNaissance;
                        $certificationFolders = $this->certificationFolderService->listByCertificationAndAttendeeNames($certification, $firstName, $lastName);
                        if ($certificationFolders->count() > 1) {
                            $ingestionResults['ko'][] = [
                                "idTechnique" => $technicalIdFromXml,
                                'erreur' => "Plusieurs dossiers de certification ont été trouvés pour l'apprenant",
                                'attendeeFirstName' => $firstName,
                                'attendeeLastName' => $lastName
                            ];
                            continue;
                        }
                        if ($certificationFolders->count() < 1) {
                            $ingestionResults['ko'][] = [
                                "idTechnique" => $technicalIdFromXml,
                                'erreur' => "Aucun dossier de certification trouvé pour l'apprenant",
                                'attendeeFirstName' => $firstName,
                                'attendeeLastName' => $lastName
                            ];
                            continue;
                        }
                        $certificationFolder = $certificationFolders->get(0);
                        $matchedBy = "Dossier trouvé par Nom / prénom du titulaire - contrôlez qu'il s'agit de la bonne personne.";
                    }
                }
                if ($certificationFolder->getCertifier() !== $certifier) {
                    $ingestionResults['mauvaisCertificateur'][] = [
                        "idTechnique" => $technicalIdFromXml,
                        "erreur" => 'Le certificateur de cette certification ne correspond pas à votre organisme',
                        "certificationIdFromCertificationFolder" => $certificationFolder->getCertification()->getId(),
                        "certificationIdFromXml" => $certification->getId(),
                    ];
                    continue;
                }
                if (in_array($certificationFolder->getId(), $certificationFoldersCdcFileList)) { // on match l'id du CF avec le tableau des id pour voir si le dossier a été ingéré précedemment
                    $ingestionResults['dejaTraites'][] = $technicalIdFromXml;
                    continue;
                }
                $certificationFolderData = $this->getCertificationFolderDataFromPassageCertificationNode($passageCertificationNode);
                try {
                    // Don't overrride fields if currently exported or processedOk
                    $preserveExistingData = in_array($certificationFolder->getCdcState(), [CertificationFolderCdcStates::EXPORTED(), CertificationFolderCdcStates::PROCESSED_OK()]);
                    $options = [
                        'preserveExistingData' => $preserveExistingData,
                        'ignoreMissingFiles' => true
                    ];
                    $certificationFolder = $this->certificationFolderService->forceUpdate($certificationFolder, $certificationFolderData, CertificationFolderStates::SUCCESS(), $user, $options);
                    if ($certificationFolder->getState() === CertificationFolderStates::SUCCESS()->getValue()) {
                        $this->certificationFoldersCdcFilesService->create($cdcFile, $certificationFolder);
                        $ingestionResults['ok'][] = [
                            "idTechnique" => $technicalIdFromXml,
                            "certificationFolderExternalId" => $certificationFolder->getExternalId(),
                            "registrationFolderExternalId" => $certificationFolder->getRegistrationFolderExternalId(),
                            "matchedBy" => $matchedBy
                        ];
                    } else {
                        $registrationFolder = $certificationFolder->getRegistrationFolder();
                        $ingestionResults['ko'][] = [
                            "idTechnique" => $technicalIdFromXml,
                            'erreur' => "L'état Réussi n'a pas été atteint à cause d'une erreur inconnue",
                            "certificationFolderExternalId" => $certificationFolder->getExternalId(),
                            "certificationFolderState" => $certificationFolder->getState(),
                            "registrationFolderExternalId" => $certificationFolder->getRegistrationFolderExternalId(),
                            "registrationFolderState" => $registrationFolder ? $registrationFolder->getState() : null
                        ];
                    }
                } catch (Throwable $t) {
                    $registrationFolder = $certificationFolder->getRegistrationFolder();
                    $ingestionResults['ko'][] = [
                        "idTechnique" => $technicalIdFromXml,
                        "erreur" => "Erreur technique à faire parvenir au support : " . $t->getMessage(),
                        "certificationFolderExternalId" => $certificationFolder->getExternalId(),
                        "certificationFolderState" => $certificationFolder->getState(),
                        "registrationFolderExternalId" => $certificationFolder->getRegistrationFolderExternalId(),
                        "registrationFolderState" => $registrationFolder ? $registrationFolder->getState() : null
                    ];
                }
            }
        }
        return $ingestionResults;
    }

    /**
     * @param array $xmlArray
     * @return array
     */
    public function readReceiptXmlForCdc(array $xmlArray): array
    {
        $passagesById = [];
        $passagesKOExists = array_key_exists('passage', $xmlArray['PassagesKO']);
        if ($passagesKOExists) {
            $passagesKO = $xmlArray['PassagesKO']['passage'];
            $passageKoArray = array_key_exists('idTechnique', $passagesKO) ? [$passagesKO] : $passagesKO;
            foreach ($passageKoArray as $passage) {
                $idTechnique = $passage['idTechnique'];
                $passagesById[$idTechnique] = [
                    'status' => 'processedKo',
                    'errorMessage' => $passage['messageErreur']
                ];
            }
        }
        $passagesOKExists = array_key_exists('idTechnique', $xmlArray['PassagesOK']);
        if ($passagesOKExists) {
            $passagesOK = $xmlArray['PassagesOK']['idTechnique'];
            $passageOkArray = gettype($passagesOK) === 'string' ? [$passagesOK] : $passagesOK;
            foreach ($passageOkArray as $passage) {
                $passagesById[$passage] = [
                    'status' => 'processedOk',
                    'errorMessage' => null
                ];
            }
        }
        return $passagesById;
    }

    //----------------
    // METHODES PRIVES
    //----------------

    /**
     * @param $passageCertificationNode
     * @return array
     * @throws Exception
     */
    private function getCertificationFolderDataFromPassageCertificationNode($passageCertificationNode): array
    {
        return [
            'enrollmentDate' => key_exists('dateInscription', $passageCertificationNode->modalitesInscription) ? (new DateTime($passageCertificationNode->modalitesInscription->dateInscription))->setTimezone(new DateTimeZone('GMT')) : null,
            'examinationDate' => key_exists('dateDebutExamen', $passageCertificationNode) ? (new DateTime($passageCertificationNode->dateDebutExamen))->setTimezone(new DateTimeZone('GMT')) : null,
            'examinationEndDate' => key_exists('dateFinExamen', $passageCertificationNode) ? (new DateTime($passageCertificationNode->dateFinExamen))->setTimezone(new DateTimeZone('GMT')) : null,
            'examinationCenterZipCode' => key_exists('codePostalCentreExamen', $passageCertificationNode) ? $passageCertificationNode->codePostalCentreExamen : null,
            'examinationType' => key_exists('modalitePassageExamen', $passageCertificationNode) ? $passageCertificationNode->modalitePassageExamen : null,
            'type' => key_exists('initiativeInscription', $passageCertificationNode->modalitesInscription) ? $passageCertificationNode->modalitesInscription->initiativeInscription : null,
            'verbatim' => key_exists('verbatim', $passageCertificationNode) ? $passageCertificationNode->verbatim : null,
            'optionName' => key_exists('libelleOption', $passageCertificationNode) ? $passageCertificationNode->libelleOption : null,
            'accessModality' => $passageCertificationNode->modalitesInscription->modaliteAcces->attributes('xsi', true)->nil ? null : $passageCertificationNode->modalitesInscription->modaliteAcces,
            'accessModalityVae' => key_exists('voieAccessVAE', $passageCertificationNode->modalitesInscription) ? $passageCertificationNode->modalitesInscription->voieAccessVAE : null,
            'europeanLanguageLevel' => key_exists('niveauLangueEuropeen', $passageCertificationNode) ? $passageCertificationNode->niveauLangueEuropeen : null,
            'certifiedData' => $passageCertificationNode->donneeCertifiee === "true",
            'issueDate' => key_exists('dateDebutValidite', $passageCertificationNode) ? (new DateTime($passageCertificationNode->dateDebutValidite))->setTimezone(new DateTimeZone('GMT')) : null,
            'expirationDate' => $passageCertificationNode->dateFinValidite->attributes('xsi', true)->nil ? null : (new DateTime($passageCertificationNode->dateFinValidite))->setTimezone(new DateTimeZone('GMT')),
            'digitalProofLink' => key_exists('urlPreuve', $passageCertificationNode) ? $passageCertificationNode->urlPreuve : null,
            'detailedResult' => $passageCertificationNode->scoring->attributes('xsi', true)->nil ? null : $passageCertificationNode->scoring,
            'gradePass' => $passageCertificationNode->mentionValidee->attributes('xsi', true)->nil ? null : CertificationFolderGradePass::from((string)$passageCertificationNode->mentionValidee),
            'cdcTechnicalId' => (string)$passageCertificationNode->idTechnique
        ];
    }
}
