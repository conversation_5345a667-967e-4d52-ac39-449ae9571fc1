<?php

// src/Service/AttendeeExperienceService.php
namespace App\Service;

use App\Entity\Attendee;
use App\Entity\AttendeeExperience;
use App\Repository\AttendeeExperienceRepository;
use Doctrine\ORM\QueryBuilder;

class AttendeeExperienceService
{

    private AttendeeExperienceRepository $attendeeExperienceRepository;

    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    public function __construct(AttendeeExperienceRepository $attendeeExperienceRepository  )
    {
      $this->attendeeExperienceRepository = $attendeeExperienceRepository;
    }

    /**
     * @param int $id
     * @return AttendeeExperience|null
     */
    public function getById(int $id): ?AttendeeExperience
    {
        return $this->attendeeExperienceRepository->find($id);
    }

    /**
     * @param array $parameters
     * @param Attendee $attendee
     * @return QueryBuilder
     */
    public function listReturnQueryBuilder(array $parameters, Attendee $attendee): QueryBuilder
    {
        return $this->attendeeExperienceRepository->findAllReturnQueryBuilder($parameters, $attendee);
    }

    /**
     * @param array $body
     * @param Attendee $attendee
     * @return AttendeeExperience
     */
    public function create(array $body, Attendee $attendee): AttendeeExperience
    {
        $properties = ['qualification', 'certificationName', 'job', 'startDate', 'endDate', 'companyName', 'salaryYearly', 'executiveStatus', 'situation', 'contractType'];

        $attendeeExperience = new AttendeeExperience();
        $attendeeExperience->setAttendee($attendee);

        foreach ($properties as $property) {
            if (key_exists($property, $body)) {
                $setMethodName = "set" . ucwords($property);
                $attendeeExperience->{$setMethodName}($body[$property]);
            }
        }
        return $this->save($attendeeExperience);
    }

    //----------------
    // METHODES PRIVEES
    //----------------

    /**
     * @param AttendeeExperience $attendeeExperience
     * @return AttendeeExperience
     */
    private function save(AttendeeExperience $attendeeExperience): AttendeeExperience
    {
        return $this->attendeeExperienceRepository->save($attendeeExperience);
    }

}
