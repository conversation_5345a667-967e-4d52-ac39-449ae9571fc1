<?php
// src/Service/AccessService.php

namespace App\Service;

use App\Entity\Activity;
use App\Entity\ApiToken;
use App\Entity\Attendee;
use App\Entity\Certification;
use App\Entity\CertificationFolder;
use App\Entity\CertificationPartner;
use App\Entity\CertifierAccess;
use App\Entity\Evaluation;
use App\Entity\Organism;
use App\Entity\Payment;
use App\Entity\Proposal;
use App\Entity\RegistrationFolder;
use App\Entity\Session;
use App\Entity\Skill;
use App\Entity\Subscription;
use App\Entity\Training;
use App\Entity\TrainingAction;
use App\Entity\User;
use App\Entity\Webhook;
use App\Entity\WorkingContract;
use App\Exception\WedofBadRequestHttpException;
use App\Library\utils\enums\CertificationPartnerHabilitation;
use App\Library\utils\enums\CertificationPartnerStates;
use App\Library\utils\enums\DataProviders;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use ReflectionException;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;

class AccessService
{
    private ConnectionService $connectionService;
    private ApplicationService $applicationService;
    private CertifierAccessService $certifierAccessService;
    private CertificationPartnerService $certificationPartnerService;
    private ContainerInterface $container;


    public function __construct(CertificationPartnerService $certificationPartnerService, ApplicationService $applicationService, CertifierAccessService $certifierAccessService, ConnectionService $connectionService, ContainerInterface $container)
    {
        $this->connectionService = $connectionService;
        $this->applicationService = $applicationService;
        $this->certifierAccessService = $certifierAccessService;
        $this->certificationPartnerService = $certificationPartnerService;
        $this->container = $container;
    }

    //---------
    // Activity
    //---------

    /**
     * @param User $user
     * @param Activity $activity
     * @return bool
     */
    public function hasActivityEdit(User $user, Activity $activity): bool
    {
        return $user->getMainOrganism() === $activity->getUser()->getMainOrganism() && !in_array($activity->getType(), ['updateState', 'create', 'update', 'file', 'progress', 'cdc']);
    }

    //---------
    // ApiToken
    //---------

    /**
     * @param User $user
     * @param ApiToken $apiToken
     * @return bool
     */
    public function hasApiTokenEdit(User $user, ApiToken $apiToken): bool
    {
        return $user === $apiToken->getUser();
    }

    //------------
    // Application
    //------------

    /**
     * @param string $appId
     * @param Organism $organism
     * @return bool
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws ReflectionException
     * @throws \Doctrine\ORM\ORMException
     */
    public function isApplicationAllowedAndEnabled(string $appId, Organism $organism): bool
    {
        $application = $this->applicationService->getByOrganismAndAppId($organism, $appId, null, false);
        return $application && $application->getEnabled()
            && $this->applicationService->isApplicationAllowed($appId, $organism->getSubscription());
    }

    //---------
    // Attendee
    //---------
    /**
     * @param User $user
     * @param Attendee $attendee
     * @return bool
     */
    public function hasAttendeeView(User $user, Attendee $attendee): bool
    {
        $hasAccess = false;
        /** @var RegistrationFolder $registrationFolder */
        foreach ($attendee->getRegistrationFolders() as $registrationFolder) {
            if ($registrationFolder->getOrganism() === $user->getMainOrganism()) {
                $hasAccess = true;
                break;
            }
        }
        if (!$hasAccess) {
            /** @var CertificationFolder $certificationFolder */
            foreach ($attendee->getCertificationFolders() as $certificationFolder) {
                if ($certificationFolder->getPartner() === $user->getMainOrganism() || $certificationFolder->getCertification()->isCertifier($user->getMainOrganism())) {
                    $hasAccess = true;
                    break;
                }
            }
        }

        return $hasAccess;
    }

    /**
     * @param User $user
     * @param Attendee $attendee
     * @return bool
     */
    public function hasAttendeeEdit(User $user, Attendee $attendee): bool
    {
        return $this->hasAttendeeView($user, $attendee);
    }

    //--------------
    // Certification
    //--------------
    /**
     * @param User $user
     * @param Certification $certification
     * @return bool
     * @throws NonUniqueResultException
     */
    public function hasCertificationAdvancedView(User $user, Certification $certification): bool
    {
        $hasAccess = $certification->isCertifier($user->getMainOrganism()) || $this->isPartnerFranceCompetences($certification, $user->getMainOrganism());

        if (!$hasAccess) {
            foreach ($certification->getCertifiers() as $certifier) {
                if (!empty($this->certifierAccessService->getByOrganisms($certifier, $user->getMainOrganism(), 'all'))) {
                    $hasAccess = true;
                    break;
                }
            }
        }

        return $hasAccess;
    }

    /**
     * @param Attendee|null $user
     * @param Certification $certification
     * @return bool
     * @throws NonUniqueResultException
     * @throws NoResultException
     */
    public function hasCertificationAttendeeView(?Attendee $user, Certification $certification): bool
    {
        /** @var CertificationFolderService $certificationFolderService */
        $certificationFolderService = $this->container->get(CertificationFolderService::class);
        return $certificationFolderService->countForAttendeeAndCertification($user, $certification) > 0;
    }

    /**
     * @param User $user
     * @param Certification $certification
     * @return bool
     */
    public function hasCertificationCdcView(User $user, Certification $certification): bool
    {
        return $certification->isCertifier($user->getMainOrganism());
    }

    /**
     * @param User $user
     * @param Certification $certification
     * @return bool
     */
    public function hasCertificationEdit(User $user, Certification $certification): bool
    {
        return $certification->isCertifier($user->getMainOrganism());
    }

    /**
     * @param User $user
     * @param Certification $certification
     * @return bool
     * @throws NonUniqueResultException
     */
    public function hasCertifiersView(User $user, Certification $certification): bool
    {
        return $this->hasCertificationAdvancedView($user, $certification);
    }

    /**
     * @param User $user
     * @param Certification $certification
     * @return bool
     */
    public function hasPartnersView(User $user, Certification $certification): bool
    {
        return $certification->isCertifier($user->getMainOrganism());
    }

    /**
     * @param User $user
     * @param CertificationFolder $certificationFolder
     * @return bool
     */
    public function hasCertificationFolderEvaluate(User $user, CertificationFolder $certificationFolder): bool
    {
        $canEvaluate = $user->getMainOrganism() === $certificationFolder->getCertifier();
        if (!$canEvaluate) {
            $certificationPartner = $this->certificationPartnerService->getByCertificationAndPartner($certificationFolder->getCertification(), $user->getMainOrganism());
            $canEvaluate = $certificationPartner && in_array($certificationPartner->getHabilitation(), [CertificationPartnerHabilitation::EVALUATE()->getValue(), CertificationPartnerHabilitation::TRAIN_EVALUATE()->getValue()]);
        }

        return $canEvaluate;
    }

    //---------------------
    // Certification Folder
    //---------------------
    /**
     * @param User $user
     * @param CertificationFolder $certificationFolder
     * @return bool
     */
    public function hasCertificationFolderView(User $user, CertificationFolder $certificationFolder): bool
    {
        // Reuse the same methods as currently viewing and editing the folder require the same permissions
        // Ideally the content of these methods would be extracted into dedicated private methods
        return $this->hasCertificationFolderPartnerEdit($user, $certificationFolder) || $this->hasCertificationFolderCertifierEdit($user, $certificationFolder);
    }

    /**
     * @param User $user
     * @return bool
     */
    public function hasCertificationFoldersView(User $user): bool
    {
        $hasAccess = $user->getMainOrganism()->getSubscription()->isAllowCertifiers();

        if (!$hasAccess) {
            $certifierAccesses = $this->certifierAccessService->listForPartner($user->getMainOrganism());
            /** @var CertifierAccess $certifierAccess */
            foreach ($certifierAccesses as $certifierAccess) {
                if ($certifierAccess->getCertifier()->getSubscription()->isAllowCertifiers()) {
                    $hasAccess = true;
                    break;
                }
            }
        }

        return $hasAccess;
    }

    /**
     * @param User $user
     * @param CertificationFolder $certificationFolder
     * @return bool
     */
    public function hasCertificationFolderCertifierEdit(User $user, CertificationFolder $certificationFolder): bool
    {
        $organism = $user->getMainOrganism();
        return $certificationFolder->getCertifier() === $organism && $organism->getSubscription()->isAllowCertifiers();
    }

    /**
     * @param Attendee $user
     * @param CertificationFolder $certificationFolder
     * @return bool
     */
    public function hasCertificationFolderAttendeeView(Attendee $user, CertificationFolder $certificationFolder): bool
    {
        return $this->hasCertificationFolderAttendeeEdit($user, $certificationFolder);
    }

    /**
     * @param Attendee $user
     * @param CertificationFolder $certificationFolder
     * @return bool
     */
    public function hasCertificationFolderAttendeeEdit(Attendee $user, CertificationFolder $certificationFolder): bool
    {
        return $user->getUserIdentifier() === $certificationFolder->getAttendee()->getUserIdentifier();
    }

    /**
     * @param User $user
     * @param CertificationFolder $certificationFolder
     * @return bool
     */
    public function hasCertificationFolderPartnerEdit(User $user, CertificationFolder $certificationFolder): bool
    {
        return $user->getMainOrganism() === $certificationFolder->getPartner() && $certificationFolder->getCertifier()->getSubscription() && $certificationFolder->getCertifier()->getSubscription()->isAllowCertifiers();
    }

    //----------------------
    // Certification Partner
    //----------------------
    /**
     * @param User $user
     * @param CertificationPartner $certificationPartner
     * @param bool $activeOnly
     * @return bool
     */
    public function hasCertificationPartnerView(User $user, CertificationPartner $certificationPartner, bool $activeOnly = true): bool
    {
        $organism = $user->getMainOrganism();
        if ($activeOnly && $certificationPartner->getState() !== CertificationPartnerStates::ACTIVE()->getValue()) {
            return false;
        }
        return $certificationPartner->getPartner() === $organism
            || ($organism->getSubscription()->isAllowCertifiers() && $certificationPartner->getCertification()->isCertifier($organism));
    }

    /**
     * @param User $user
     * @param CertificationPartner $certificationPartner
     * @return bool
     */
    public function hasCertificationPartnerCertifierEdit(User $user, CertificationPartner $certificationPartner): bool
    {
        $organism = $user->getMainOrganism();
        return $certificationPartner->getCertifier() === $organism && $organism->getSubscription() && $organism->getSubscription()->isAllowCertifierPlus();
    }

    //-----------------
    // Certifier Access
    //-----------------
    /**
     * @param User $user
     * @param CertifierAccess $certifierAccess
     * @return bool
     */
    public function hasCertifierAccessView(User $user, CertifierAccess $certifierAccess): bool
    {
        $organism = $user->getMainOrganism();
        return (($organism === $certifierAccess->getCertifier() && $organism->getSubscription()->isAllowCertifiers()) || $organism === $certifierAccess->getPartner());
    }

    /**
     * @param User $user
     * @param CertifierAccess $certifierAccess
     * @return bool
     */
    public function hasCertifierAccessEdit(User $user, CertifierAccess $certifierAccess): bool
    {
        return $this->hasCertifierAccessView($user, $certifierAccess);
    }

    /**
     * @param User $user
     * @param CertifierAccess $certifierAccess
     * @return bool
     */
    public function hasCertifierAccessCreate(User $user, CertifierAccess $certifierAccess): bool
    {
        // vérification de souscription faite au niveau du CertifierAccessVoter
        $certificationPartners = $this->certificationPartnerService->listByPartner($certifierAccess->getPartner(), $user->getMainOrganism()->getCertifierCertifications()->toArray(), false);

        return $certificationPartners->count() > 0 || $certifierAccess->getCertifier() === $certifierAccess->getPartner();
    }

    //---------
    // Proposal
    //---------
    /**
     * @param User $user
     * @param Proposal $proposal
     * @return bool
     */
    public function hasProposalView(User $user, Proposal $proposal): bool
    {
        return $proposal->getOrganism() === $user->getMainOrganism();
    }

    /**
     * @param User $sales
     * @param Proposal $proposal
     * @return bool
     */
    public function hasProposalViewForSales(User $sales, Proposal $proposal): bool
    {
        return $proposal->getSales() === $sales;
    }

    /**
     * @param User $user
     * @param Proposal $proposal
     * @return bool
     */
    public function hasProposalEdit(User $user, Proposal $proposal): bool
    {
        return $proposal->getOrganism() === $user->getMainOrganism();
    }

    /**
     * @param User $sales
     * @param Proposal $proposal
     * @return bool
     */
    public function hasProposalEditForSales(User $sales, Proposal $proposal): bool
    {
        return $proposal->getSales() === $sales;
    }
    //-----------
    // Evaluation
    //-----------
    /**
     * @param User $user
     * @param Evaluation $evaluation
     * @return bool
     */
    public function hasEvaluationView(User $user, Evaluation $evaluation): bool
    {
        return $evaluation->getOrganism() === $user->getMainOrganism();
    }

    //---------
    // Organism
    //---------
    /**
     * @param User $user
     * @param Organism $organism
     * @return bool
     */
    public function hasOrganismView(User $user, Organism $organism): bool
    {
        return $user->getMainOrganism() === $organism || $user->getMainOrganism() === $organism->getReseller();
    }

    /**
     * @param Attendee $user
     * @param Organism $organism
     * @return bool
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function hasOrganismAttendeeView(Attendee $user, Organism $organism): bool
    {
        /** @var RegistrationFolderService $registrationFolderService */
        $registrationFolderService = $this->container->get(RegistrationFolderService::class);
        return $registrationFolderService->countForAttendeeAndOrganism($user, $organism) > 0;
    }

    /**
     * @param User $user
     * @param Organism $organism
     * @return bool
     */
    public function hasOrganismCertificationView(User $user, Organism $organism): bool
    {
        $hasAccess = $user->getMainOrganism() === $organism;
        if (!$hasAccess) {
            foreach ($user->getMainOrganism()->getCertifierCertifications() as $certification) {
                if ($this->isPartnerFranceCompetences($certification, $organism)) {
                    $hasAccess = true;
                    break;
                }
            }
        }
        return $hasAccess;
    }

    /**
     * @param User $user
     * @param Organism $organism
     * @return bool
     */
    public function hasOrganismEdit(User $user, Organism $organism): bool
    {
        return $user->getMainOrganism() === $organism || $user->getMainOrganism() === $organism->getReseller();
    }

    //--------
    // Payment
    //--------
    /**
     * @param User $user
     * @param Payment $payment
     * @return bool
     */
    public function hasPaymentAccess(User $user, Payment $payment): bool
    {
        return $payment->getOrganism() === $user->getMainOrganism();
    }

    //--------------------
    // Registration Folder
    //--------------------
    /**
     * @param User $user
     * @param RegistrationFolder $registrationFolder
     * @return bool
     * @throws NonUniqueResultException
     */
    public function hasRegistrationFolderView(User $user, RegistrationFolder $registrationFolder): bool
    {
        $hasAccess = false;

        $organism = $user->getMainOrganism();
        if ($organism === $registrationFolder->getOrganism()) {
            $hasAccess = true;
        } else if ($registrationFolder->getCertification() && $organism->getSubscription()->isAllowCertifiers() && $registrationFolder->getCertification()->isCertifier($organism)) {
            $hasAccess = !empty($this->certifierAccessService->getByOrganisms($organism, $registrationFolder->getOrganism()));
        }

        return $hasAccess;
    }

    /**
     * @param User $user
     * @param RegistrationFolder $registrationFolder
     * @return bool
     * @throws TransportExceptionInterface
     */
    public function hasRegistrationFolderEdit(User $user, RegistrationFolder $registrationFolder): bool
    {
        $hasAccess = $registrationFolder->getOrganism() === $user->getMainOrganism();

        if ($hasAccess && !$registrationFolder->isAllowActions()) {
            throw new WedofBadRequestHttpException("Erreur, vous avez atteint la limite de dossiers autorisée.");
        }

        if ($hasAccess && !$this->connectionService->hasAccess($registrationFolder->getOrganism(), DataProviders::from($registrationFolder->getType()))) {
            // Don't throw WedofConnectionException as it is not serialized in prod, it is swallowed
            throw new WedofBadRequestHttpException("Impossible d'agir sur le dossier : connexion " . $registrationFolder->getType() . " inactive.");
        }

        return $hasAccess;
    }

    /**
     * @param User $user
     * @param RegistrationFolder $registrationFolder
     * @return bool
     */
    public function hasRegistrationFolderOwnerView(User $user, RegistrationFolder $registrationFolder): bool
    {
        return $user->getMainOrganism() === $registrationFolder->getOrganism();
    }

    /**
     * @param Attendee $user
     * @param RegistrationFolder $registrationFolder
     * @return bool
     */
    public function hasRegistrationFolderAttendeeView(Attendee $user, RegistrationFolder $registrationFolder): bool
    {
        return $this->hasRegistrationFolderAttendeeEdit($user, $registrationFolder);
    }

    /**
     * @param Attendee $user
     * @param RegistrationFolder $registrationFolder
     * @return bool
     */
    public function hasRegistrationFolderAttendeeEdit(Attendee $user, RegistrationFolder $registrationFolder): bool
    {
        return $user->getUserIdentifier() === $registrationFolder->getAttendee()->getUserIdentifier();
    }

    //--------
    // Session
    //--------
    /**
     * @param User $user
     * @param Session $session
     * @return bool
     */
    public function hasSessionView(User $user, Session $session): bool
    {
        return $session->getTrainingAction()->getTraining()->getOrganism() === $user->getMainOrganism();
    }

    /**
     * @param User $user
     * @param Session $session
     * @return bool
     * @throws NonUniqueResultException
     */
    public function hasSessionCertifierView(User $user, Session $session): bool
    {
        $hasAccess = false;
        $certification = $session->getTrainingAction()->getTraining()->getCertification();

        if ($user->getMainOrganism()->getCertifierCertifications()->contains($certification)) {
            $hasAccess = !empty($this->certifierAccessService->getByOrganisms($user->getMainOrganism(), $session->getTrainingAction()->getTraining()->getOrganism()));
        }

        return $hasAccess;
    }

    /**
     * @param Attendee $user
     * @param Session $session
     * @return bool
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function hasSessionAttendeeView(Attendee $user, Session $session): bool
    {
        /** @var RegistrationFolderService $registrationFolderService */
        $registrationFolderService = $this->container->get(RegistrationFolderService::class);
        return $registrationFolderService->countForAttendeeAndSession($user, $session) > 0;
    }

    //--------
    // Skill
    //--------

    /**
     * @param User $user
     * @param Skill $skill
     * @return bool
     */
    public function hasSkillView(User $user, Skill $skill): bool
    {
        return true;
    }

    /**
     * @param User $user
     * @param Skill $skill
     * @return bool
     */
    public function hasSkillEdit(User $user, Skill $skill): bool
    {
        $certification = $skill->getCertification();
        return $certification->isCertifier($user->getMainOrganism());
    }

    //--------
    // WorkingContract
    //--------

    /**
     * @param User $user
     * @param WorkingContract $workingContract
     * @return bool
     */
    public function hasWorkingContractView(User $user, WorkingContract $workingContract): bool
    {
        $registrationFolder = $workingContract->getRegistrationFolder();
        $organism = $user->getMainOrganism();
        return $registrationFolder->getOrganism() === $organism;
    }

    //---------
    // Training
    //---------
    /**
     * @param User $user
     * @param Training $training
     * @return bool
     */
    public function hasTrainingView(User $user, Training $training): bool
    {
        return $training->getOrganism() === $user->getMainOrganism();
    }

    /**
     * @param User $user
     * @param Training $training
     * @return bool
     */
    public function hasTrainingEdit(User $user, Training $training): bool
    {
        $hasAccess = $training->getOrganism() === $user->getMainOrganism();

        if (!$hasAccess) {
            $hasAccess = $training->getCertification()->isCertifier($user->getMainOrganism());
        }

        return $hasAccess;
    }

    //----------------
    // Training Action
    //----------------
    /**
     * @param User $user
     * @param TrainingAction $trainingAction
     * @return bool
     */
    public function hasTrainingActionView(User $user, TrainingAction $trainingAction): bool
    {
        return $trainingAction->getTraining()->getOrganism() === $user->getMainOrganism();
    }

    //-----
    // User
    //-----
    /**
     * @param User $user
     * @param User $subjectUser
     * @return bool
     */
    public function hasUserView(User $user, User $subjectUser): bool
    {
        return $user === $subjectUser || in_array($subjectUser->getMainOrganism(), $user->getOrganisms()->toArray());
    }

    /**
     * @param User $user
     * @param User $subjectUser
     * @return bool
     */
    public function hasUserEdit(User $user, User $subjectUser): bool
    {
        return $user === $subjectUser || $subjectUser->getMainOrganism()->getSiret() === $user->getOwnedOrganism()->getSiret();
    }

    //--------
    // Webhook
    //--------
    /**
     * @param User $user
     * @param Webhook $webhook
     * @return bool
     */
    public function hasWebhookView(User $user, Webhook $webhook): bool
    {
        return $webhook->getOrganism() === $user->getMainOrganism();
    }

    /**
     * @param User $user
     * @param Webhook $webhook
     * @return bool
     */
    public function hasWebhookEdit(User $user, Webhook $webhook): bool
    {
        return $webhook->getOrganism() === $user->getMainOrganism();
    }

    //-----------------
    // METHODES PRIVEES
    //-----------------

    /**
     * @param Certification $certification
     * @param Organism $organism
     * @return bool
     */
    private function isPartnerFranceCompetences(Certification $certification, Organism $organism): bool
    {
        return $this->certificationPartnerService->getByCertificationAndPartner($certification, $organism) != null;
    }

    /**
     * @param User $user
     * @param Subscription $subscription
     * @return bool
     */
    public function hasSubscriptionView(User $user, Subscription $subscription): bool
    {
        return $user->getMainOrganism()->getSubscription() === $subscription;
    }

    /**
     * @param User $user
     * @param Subscription $subscription
     * @return bool
     */
    public function hasSubscriptionEdit(User $user, Subscription $subscription): bool
    {
        return $user->getMainOrganism()->getSubscription() === $subscription;
    }
}
