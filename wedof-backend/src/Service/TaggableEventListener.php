<?php

namespace App\Service;

use Beelab\TagBundle\Entity\AbstractTaggable;
use Doctrine\ORM\Event\LifecycleEventArgs;

class TaggableEventListener
{
    //-------------------
    // METHODES PUBLIQUES
    //-------------------
    /**
     * @param LifecycleEventArgs $args
     */
    public function postLoad(LifecycleEventArgs $args): void
    {
        $entity = $args->getEntity();
        if ($entity instanceof AbstractTaggable) {
            $entity->setTagsText($entity->getTagsText()); //keep tags loaded
        }
    }
}
