<?php

// src/Service/WebhookService.php
namespace App\Service;

use App\Application\Activepieces\ActivepiecesWedofApplication;
use App\Application\Dendreo\DendreoWedofApplication;
use App\Application\Make\MakeWedofApplication;
use App\Application\N8n\N8nWedofApplication;
use App\Application\Salesforce\SalesforceWedofApplication;
use App\Application\Slack\SlackWedofApplication;
use App\Application\Webhook\WebhookWedofApplication;
use App\Application\Workflow\WorkflowWedofApplication;
use App\Application\Zapier\ZapierWedofApplication;
use App\Entity\Delivery;
use App\Entity\Organism;
use App\Entity\Webhook;
use App\Event\MonitoringEvents;
use App\Exception\WedofBadRequestHttpException;
use App\Library\utils\Tools;
use App\Repository\DeliveryRepository;
use App\Repository\WebhookRepository;
use Datetime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\QueryBuilder;
use Exception;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use ReflectionException;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\Response;
use Throwable;

class WebhookService implements LoggerAwareInterface
{
    const NO_STATUS_CODE = -1;
    const EVENT_TYPES = ['certification', 'registrationFolder', 'registrationFolderBilling', 'registrationFolderControl', 'registrationFolderAlert', 'evaluation', 'certificationFolder', 'certificationPartner', 'registrationFolderFile', 'certificationFolderFile', 'certificationPartnerFile', "subscription", "connection", 'payment', 'certificationFolderSurvey', 'certificationPartnerInvoice', 'certificationPartnerAudit', 'organism'];
    const TYPE = ['webhook'];
    private EntityManagerInterface $entityManager;
    private LoggerInterface $logger;
    private WebhookRepository $webhookRepository;
    private ApplicationService $applicationService;
    private DeliveryRepository $deliveryRepository;
    private MailerService $mailerService;
    private AccessService $accessService;
    private EventDispatcherInterface $dispatcher;


    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    public function __construct(EntityManagerInterface $entityManager, DeliveryRepository $deliveryRepository, ApplicationService $applicationService, WebhookRepository $webhookRepository, MailerService $mailerService, AccessService $accessService, EventDispatcherInterface $dispatcher)
    {
        $this->applicationService = $applicationService;
        $this->entityManager = $entityManager;
        $this->webhookRepository = $webhookRepository;
        $this->deliveryRepository = $deliveryRepository;
        $this->mailerService = $mailerService;
        $this->accessService = $accessService;
        $this->dispatcher = $dispatcher;
    }

    /**
     * @param LoggerInterface $logger
     */
    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    /**
     * @param array $data
     * @param Organism $organism
     * @return Webhook
     * @throws Throwable
     */
    public function create(array $data, Organism $organism): Webhook
    {
        $events = Tools::cleanEvents($data['events'], self::EVENT_TYPES);
        if (empty($events)) {
            throw new WedofBadRequestHttpException("Erreur, seuls les évènements suivants sont possible : " . implode(", ", self::EVENT_TYPES) . ".");
        }
        $existingWebhooks = $this->webhookRepository->findBy([
            'url' => $data['url'],
            'organism' => $organism
        ]);
        if (count($existingWebhooks) !== 0 && $existingWebhooks[0]->getEvents() === $events) {
            throw new WedofBadRequestHttpException("Un webhook portant sur la même URL et les mêmes évènements existe déjà.");
        }

        $webhook = new Webhook();
        $webhook->setUrl($data['url']);
        $webhook->setEvents($events);
        $webhook->setEnabled($data['enabled'] ?? true);
        $webhook->setOrganism($organism);
        $webhook->setIgnoreSsl($data['ignoreSsl'] ?? false);
        $webhook->setSecret($data['secret'] ?? null);
        $webhook->setName($data['name'] ?? null);
        $webhook->setType($data['type'] ?? "webhook");
        $webhook->setOptions(!empty($data['options']) ? $data['options'] : []);

        $webhookApp = $this->applicationService->getByOrganismAndAppId($organism, WebhookWedofApplication::getAppId());
        if (!$webhookApp->getEnabled()) {
            $this->applicationService->enable($webhookApp);
        }

        return $this->webhookRepository->save($webhook);
    }

    /**
     * @param Webhook $webhook
     * @return void
     */
    public function delete(Webhook $webhook): void
    {
        $this->webhookRepository->delete($webhook);
    }

    /**
     * @param Webhook $webhook
     * @param $data
     * @return Webhook
     * @throws Throwable
     */
    public function update(Webhook $webhook, $data): Webhook
    {
        if (isset($data['events'])) {
            $events = Tools::cleanEvents($data['events'], self::EVENT_TYPES);
            if (empty($events)) {
                throw new WedofBadRequestHttpException("Erreur, seuls les évènements suivants sont possible : " . implode(", ", self::EVENT_TYPES) . ".");
            }
            $webhook->setEvents($events);
        }

        if (isset($data['enabled'])) {
            $webhook->setEnabled($data['enabled']);
        }
        if (isset($data['ignoreSsl'])) {
            $webhook->setIgnoreSsl($data['ignoreSsl']);
        }
        if (array_key_exists('secret', $data)) { // allow null
            $webhook->setSecret($data['secret']);
        }
        if (isset($data['options'])) {
            $webhook->setOptions($data['options']);
        }
        if (array_key_exists('name', $data)) { // allow null
            $webhook->setName($data['name']);
        }

        return $this->webhookRepository->save($webhook);
    }

    /**
     * @param Webhook $webhook
     * @param string $eventName
     * @param string $payload
     * @param string $entityClass
     * @param string $entityId
     * @param int|null $authorId
     * @param bool $retry
     * @param string|null $guid
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function send(Webhook $webhook, string $eventName, string $payload, string $entityClass, string $entityId, int $authorId = null, string $guid = null, bool $retry = false): void
    {
        $_options = [
            'timeout' => 20,
            'headers' => [
                'Content-Type' => 'application/json',
                'User-Agent' => 'Wedof Webhook',
                'X-Wedof-Event' => $eventName
            ]
        ];

        if ($authorId) {
            $_options['headers']['X-Wedof-AuthorId'] = $authorId;
        }
        $response = array("delivery" => null);
        $options = $_options; //copy
        if ($webhook->getSecret()) {
            $signature = hash_hmac("sha512", $payload, $webhook->getSecret());
            $options['headers']['X-Wedof-Signature'] = $signature;
            $this->logger->debug("webhook (id: " . $webhook->getId() . ") - secret set with $signature)");
        }
        $webhookOptions = $webhook->getOptions();
        $originalWebhookUrl = $webhook->getUrl();
        if (!empty($webhookOptions['useAccessToken'])) {
            try {
                $application = $this->applicationService->getByOrganismAndAppId($webhook->getOrganism(), $webhook->getType());
                $accessToken = $this->applicationService->getAccessToken($application);
                $options['headers']['Authorization'] = "Bearer " . $accessToken;
            } catch (Exception $e) {
                if ($webhook->getEnabled()) {
                    $webhook->setCountErrors(10);
                    $webhook->setEnabled(false);
                    $this->logger->debug($e->getMessage());
                    $this->mailerService->sendDeactivateWebhook($webhook);
                }
                return;
            }
        }
        if (!empty($webhookOptions['timeout'])) {
            $options['timeout'] = $webhookOptions['timeout'];
        }
        if (!empty($webhookOptions['X-Auth'])) {
            $options['headers']['X-Auth'] = $webhookOptions['X-Auth'];
        }
        if (!empty($webhookOptions['automatorProxyEndpoint'])) {
            $options['headers']['X-Target-Url'] = $originalWebhookUrl;
            $options['headers'][$_ENV['AUTOMATOR_AUTH_HEADER']] = $_ENV['AUTOMATOR_AUTH'];
            $options['timeout'] = $_ENV['AUTOMATOR_API_TIMEOUT'];
            $originalWebhookUrl = $_ENV['AUTOMATOR_BASE_URI'] . $webhookOptions['automatorProxyEndpoint'];
        }
        try {
            $delivered = $this->deliveryRepository->findOneBy(['guid' => $guid]);
            if ($delivered != null) {
                if (!$retry) {
                    $this->logger->debug("webhook with this payload and event already sent already sent " . $guid);
                    return;
                } else {
                    $response["delivery"] = $delivered; //re send the delivery and keep only the last try
                    $response["delivery"]->setSentCount($response["delivery"]->getSentCount() + 1);
                    $response["delivery"]->setDate(new Datetime());
                }
            } else {
                $response["delivery"] = new Delivery();
                $response["delivery"]->setEvent($eventName);
                $response["delivery"]->setContent($payload);
                $response["delivery"]->setWebhook($webhook);
                $response["delivery"]->setDate(new Datetime());
                $response["delivery"]->setGuid($guid);
                $response["delivery"]->setStatusCode(-1);
                $response["delivery"]->setHost(gethostname());
                $response["delivery"]->setEntityClass($entityClass);
                $response["delivery"]->setEntityId($entityId);
                $response["delivery"]->setAuthorId($authorId);
                $this->deliveryRepository->save($response["delivery"]);
            }
            $options["verify_peer"] = !$webhook->getIgnoreSsl();
            $options['headers']['X-Wedof-Delivery'] = $response["delivery"]->getGuid();

            $targetUrl = $originalWebhookUrl;
            if (!Tools::isEnvIn(['prod'])) {
                $options['headers']['X-Wedof-Target'] = $originalWebhookUrl;
                $targetUrl = $_ENV["CATCHALL_WEBHOOK_URI"];
                $this->logger->debug("webhook catchall not in prod (id: " . $webhook->getId() . ") send to CATCHALL_WEBHOOK_URI: " . $_ENV["CATCHALL_WEBHOOK_URI"] . " instead of prod url: " . $originalWebhookUrl);
            }

            if ($targetUrl) {
                $organism = $webhook->getOrganism();
                if ($organism->getSubscription()->isAllowWebhook($eventName)) {
                    $response["request"] = $this->checkRestrictions($organism, $webhook, $originalWebhookUrl) ?: Tools::getHttpClient($options)->request('POST', $targetUrl, ['body' => $payload]);
                    $this->logger->debug("webhook sent (id: " . $webhook->getId() . ") to " . $targetUrl);
                } else {
                    $message = [
                        'code' => 403,
                        'message' => "Webhook non envoyé car vous êtes arrivé aux limites de votre souscription actuelle.",
                        'hint' => "Souscrivez à une offre supérieure pour pouvoir renvoyer vos webhooks non envoyés."
                    ];
                    $response["request"] = new Response(json_encode($message), 403);
                }
            } else {
                $this->logger->debug("webhook not sent (id: " . $webhook->getId() . ") url is NULL ");
            }
        } catch (Throwable $e) {
            $this->logger->error("[webhook][" . $webhook->getId() . "] Request failed");
            $this->logger->error("[webhook][" . $webhook->getId() . "] \n " . $e->getMessage());
            $this->logger->error("[webhook][" . $webhook->getId() . "] \n " . $e->getTraceAsString());
            $response["delivery"]->setErrorMessage($e->getMessage() . " \n " . $e->getTraceAsString());
        } finally {
            $hookAndResponse = ["webhook" => $webhook, "response" => $response, "request" => null, "content" => null, "code" => self::NO_STATUS_CODE];
        }

        try {
            if (!empty($hookAndResponse["response"]["request"])) { //case request failed / already delivered
                $hookAndResponse["code"] = $hookAndResponse["response"]["request"]->getStatusCode(); //can throw an error / timeout
                $hookAndResponse["content"] = $hookAndResponse["code"] >= 300 ? $hookAndResponse["response"]["request"]->getContent(false) : null;
            }
        } catch (Throwable $e) {
            $this->logger->error("[webhook][" . $hookAndResponse["webhook"]->getId() . "] Response failed");
            $this->logger->error("[webhook][" . $hookAndResponse["webhook"]->getId() . "] \n " . $e->getMessage());
            $this->logger->error("[webhook][" . $hookAndResponse["webhook"]->getId() . "] \n " . $e->getTraceAsString());
            $hookAndResponse["content"] = $hookAndResponse["content"] ?: ($e->getMessage() . " \n " . $e->getTraceAsString());
        } finally {
            /** @var Webhook $respWebhook */
            $respWebhook = $hookAndResponse["webhook"];
            if ($hookAndResponse["code"] >= 300) {
                if (!empty($respWebhook->getOptions()['useAccessToken']) && in_array($hookAndResponse["code"], [401, 403])) {
                    $refreshedAccessToken = false;
                    try {
                        $application = $this->applicationService->getByOrganismAndAppId($respWebhook->getOrganism(), $respWebhook->getType());
                        $refreshedAccessToken = $this->applicationService->refreshToken($application);
                    } catch (Exception $e) {
                        $this->logger->debug($e->getMessage());
                    } finally {
                        if ($refreshedAccessToken && !$retry) { //be sure to retry on once;
                            $hookAndResponse["code"] = self::NO_STATUS_CODE;
                        } else {
                            if ($respWebhook->getEnabled() && Tools::contains($respWebhook->getCountErrors(), '.processus.wedof.fr')) {
                                $this->dispatcher->dispatch(new MonitoringEvents(null, "Erreur processus metiers pour l'organisme  {$respWebhook->getOrganism()->getSiret()} - {$respWebhook->getOrganism()->getName()} webhook: {$respWebhook->getUrl()}"), MonitoringEvents::SEND_MESSAGE);
                            }
                            if ($respWebhook->getCountErrors() >= 9) {
                                //don't disable workflow deliveries
                                if ($respWebhook->getEnabled() && !Tools::contains($respWebhook->getCountErrors(), '.processus.wedof.fr')) {
                                    $respWebhook->setEnabled(false);
                                    $this->mailerService->sendDeactivateWebhook($webhook);
                                }
                            }
                            $respWebhook->setCountErrors($respWebhook->getCountErrors() + 1);
                        }
                    }
                } else if ($hookAndResponse["code"] == 410) { //410 = no more used
                    $this->logger->info("webhook invalid (id: " . $respWebhook->getId() . ") to " . $respWebhook->getUrl());
                    $respWebhook->setEnabled(false);
                } else {
                    if (Tools::isEnvIn(['prod'])) {
                        if ($respWebhook->getEnabled() && Tools::contains($respWebhook->getCountErrors(), '.processus.wedof.fr')) {
                            $this->dispatcher->dispatch(new MonitoringEvents(null, "Erreur processus metiers pour l'organisme  {$respWebhook->getOrganism()->getSiret()} - {$respWebhook->getOrganism()->getName()} webhook: {$respWebhook->getUrl()}"), MonitoringEvents::SEND_MESSAGE);
                        }
                        if ($respWebhook->getCountErrors() >= 9) {
                            //don't disable workflow deliveries
                            if ($respWebhook->getEnabled() && !Tools::contains($respWebhook->getCountErrors(), '.processus.wedof.fr')) {
                                $respWebhook->setEnabled(false);
                                $this->mailerService->sendDeactivateWebhook($webhook);
                            }
                        }
                        $respWebhook->setCountErrors($respWebhook->getCountErrors() + 1);
                    }
                }
            } else if ($hookAndResponse["code"] >= 200 && $respWebhook->getCountErrors() > 0) {
                $respWebhook->setCountErrors(0);
            }
            if ($hookAndResponse["code"] != self::NO_STATUS_CODE || $retry) { //got an answer or already in a retry from manual or NO_STATUS_CODE retry
                $hookAndResponse["response"]["delivery"]->setResponseAt(new Datetime());
                $hookAndResponse["response"]["delivery"]->setStatusCode($hookAndResponse["code"]);
                $hookAndResponse["response"]["delivery"]->setErrorMessage($hookAndResponse["content"]);
                $this->deliveryRepository->save($hookAndResponse["response"]["delivery"]);
            }
        }
        $this->entityManager->flush();
        //auto retry one time when no answer
        if ($hookAndResponse["code"] == self::NO_STATUS_CODE && !$retry && !empty($hookAndResponse['response']['delivery'])) {
            /** @var Delivery $delivery */
            $delivery = $hookAndResponse['response']['delivery'];
            /** @var Webhook $webhook */
            $webhook = $hookAndResponse['webhook'];
            $this->logger->error("retry for delivery " . $delivery->getGuid());
            $this->send($webhook, $delivery->getEvent(), $delivery->getContent(), $delivery->getEntityClass(), $delivery->getEntityId(), $delivery->getAuthorId(), $delivery->getGuid(), true);
        }
    }

    /**
     * @param Delivery $delivery
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function retry(Delivery $delivery)
    {
        $this->send($delivery->getWebhook(), $delivery->getEvent(), $delivery->getContent(), $delivery->getEntityClass(), $delivery->getEntityId(), $delivery->getAuthorId(), $delivery->getGuid(), true);
    }

    /**
     * @param Organism $organism
     * @param array $parameters
     * @return QueryBuilder
     */
    public function listReturnQueryBuilder(Organism $organism, array $parameters): QueryBuilder
    {
        return $this->webhookRepository->findAllReturnQueryBuilder($organism, $parameters);
    }

    /**
     * @param Webhook $webhook
     * @return QueryBuilder
     */
    public function listDeliveriesByWebhookReturnQueryBuilder(Webhook $webhook): QueryBuilder
    {
        $qb = $this->deliveryRepository->createQueryBuilder("deliv");
        $qb->where($qb->expr()->eq('deliv.webhook', ':webhook'));
        $qb->setParameter('webhook', $webhook);
        $qb->orderBy("deliv.date", "DESC");
        return $qb;
    }

    /**
     * @param Collection $organisms
     * @param string $eventName
     * @param array $options ("type"=> "webhook",  "onlyEnabled"=>true)
     * @return ArrayCollection
     */
    public function listByOrganismsAndEventAndType(Collection $organisms, string $eventName, array $options = array()): ArrayCollection
    {
        $options = array_merge(["type" => "webhook", "onlyEnabled" => true], $options);

        return $this->webhookRepository->findByOrganismsAndEventAndType($organisms, $eventName, $options);
    }

    /**
     * @return ArrayCollection
     */
    public function listForRegistrationFolderAlert(): ArrayCollection
    {
        return $this->webhookRepository->findAllForRegistrationFolderAlert();
    }

    //-----------------
    // METHODES PRIVEES
    //-----------------
    /**
     * @param Organism $organism
     * @param Webhook $webhook
     * @param string $originalWebhookUrl
     * @return Response|null
     * @throws ORMException
     * @throws NonUniqueResultException
     * @throws \Doctrine\ORM\ORMException
     * @throws ReflectionException
     */
    public function checkRestrictions(Organism $organism, Webhook $webhook, string $originalWebhookUrl): ?Response
    {
        $response = null;

        $restrictions = [
            N8nWedofApplication::getAppId() => Tools::contains($originalWebhookUrl, "n8n"),
            ActivepiecesWedofApplication::getAppId() => Tools::contains($originalWebhookUrl, "activepieces"),
            MakeWedofApplication::getAppId() => Tools::contains($originalWebhookUrl, "make"),
            ZapierWedofApplication::getAppId() => Tools::contains($originalWebhookUrl, "zapier"),
            DendreoWedofApplication::getAppId() => Tools::contains($originalWebhookUrl, "dendreo"),
            SalesforceWedofApplication::getAppId() => $webhook->getType() === "salesforce",
            SlackWedofApplication::getAppId() => Tools::contains($originalWebhookUrl, "slack"),
            WorkflowWedofApplication::getAppId() => Tools::contains($originalWebhookUrl, '.processus.wedof.fr')
        ];

        foreach ($restrictions as $app => $isApp) {
            if ($isApp) {
                if (!$this->accessService->isApplicationAllowedAndEnabled($app, $organism)) {
                    $message = [
                        'code' => 403,
                        'message' => "Webhook " . $app . " non envoyé car l'application " . $app . " n'est pas active ou votre abonnement ne permet pas l'utilisation de " . $app . ".",
                        'hint' => "Réactivez l'application " . $app . " ou souscrivez à une offre supérieure pour pouvoir renvoyer vos webhooks non envoyés."
                    ];
                    $response = new Response(json_encode($message), 403);
                }
                break;
            }
        }

        return $response;
    }
}
