<?php
// src/Service/SerializerService.php

namespace App\Service;

use App\Entity\User;
use League\Bundle\OAuth2ServerBundle\Security\Authentication\Token\OAuth2Token;
use Symfony\Component\ExpressionLanguage\ExpressionFunction;
use Symfony\Component\ExpressionLanguage\ExpressionFunctionProviderInterface;
use Symfony\Component\Security\Core\Security;

class SerializerService implements ExpressionFunctionProviderInterface
{
    private Security $security;
    private AccessService $accessService;

    public function __construct(Security $security, AccessService $accessService)
    {
        $this->security = $security;
        $this->accessService = $accessService;
    }

    /**
     * @return ExpressionFunction[]
     */
    public function getFunctions(): array
    {
        return [
            new ExpressionFunction('hasRegistrationFolderAccess', function () {},
                function (array $context) {
                    /** @var User $user */
                    $user = $this->security->getUser();
                    if ($user) {
                        return $this->accessService->hasRegistrationFolderView($user, $context['object']);
                    } else { //s'il n'y a pas d'utilisateur connecté c'est qu'il s'agit d'une sérialisation à la demande des webhook dans le cadre des crontasks
                        return true;
                    }
                }
            ),
            new ExpressionFunction('isGrantedAsWebUser', function () {},
                function () {
                    if ($this->security->getToken()) {
                        return !$this->security->getToken() instanceof OAuth2Token;
                    } else { //s'il n'y a pas d'utilisateur connecté c'est qu'il s'agit d'une sérialisation à la demande des webhook dans le cadre des crontasks
                        return false;
                    }
                }
            )
        ];
    }
}
