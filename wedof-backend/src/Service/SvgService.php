<?php

namespace App\Service;

use App\Library\utils\Tools;

class SvgService
{

    public const STYLE_FONT_SSPRO = __DIR__ . '/../Library/utils/svg-css/font-source-sans-pro.css';
    public const STYLE_FONT_SSPROBOLD = __DIR__ . '/../Library/utils/svg-css/font-source-sans-pro-semi-bold.css';
    public const STYLE_FONT_MATERIAL = __DIR__ . '/../Library/utils/svg-css/font-material-icons.css';
    public const STYLE_BOOTSTRAP = __DIR__ . '/../Library/utils/svg-css/bootstrap.css';
    public const STYLE_MCF = __DIR__ . '/../Library/utils/svg-css/mcf-styles.css';
    public const STYLE_ALERTS = __DIR__ . '/../Library/utils/svg-css/mcf-styles-alerts.css';

    public function __construct()
    {
    }

    //-------------------
    // METHODES PUBLIQUES
    //-------------------
    /**
     * @param string $content
     * @param int $width
     * @param int $height
     * @param string $type
     * @param string $typeIcon
     * @return string
     */
    public function alert(string $content, int $width, int $height, string $type = 'success', string $typeIcon = 'info'): string
    {
        $alertContent = $this->alertContent($content, $type, $typeIcon);
        return $this->svg($alertContent, $width, $height);
    }

    /**
     * @param string $content
     * @param string $type
     * @param string $typeIcon
     * @return string
     */
    public function alertContent(string $content, string $type = 'success', string $typeIcon = 'info'): string
    {
        return '<cpf-alert type="' . strtoupper($type) . '" style="padding-top: 1px;display: block;">
            <div class="mt-2 cpf-alert cpf-alert-' . $type . '">' . $content . '
                <mat-icon _ngcontent-xtn-c166="" class="mat-icon notranslate material-icons mat-icon-no-color" data-mat-icon-type="font">' . $typeIcon . '</mat-icon>
            </div>
        </cpf-alert>';
    }

    /**
     * @param $content
     * @param int $width
     * @param int $height
     * @param array $filesStyles
     * @param bool $debug
     * @return string
     */

    public function svg($content, int $width = 0, int $height = 0, array $filesStyles = array(), bool $debug = false): string
    {
        $filesStyles = array_merge([
            self::STYLE_FONT_SSPRO,
            self::STYLE_FONT_SSPROBOLD,
            self::STYLE_BOOTSTRAP,
            self::STYLE_MCF,
            self::STYLE_FONT_MATERIAL
        ], $filesStyles);

        if (Tools::contains($content, "cpf-alert")) {
            array_push($filesStyles, self::STYLE_ALERTS);
        }

        if (Tools::contains($content, "mat-icon")) {
            array_push($filesStyles, self::STYLE_FONT_MATERIAL);
        }

        ob_start();
        if ($debug) {
            ?>
            <html xmlns="http://www.w3.org/1999/xhtml" lang="fr">
            <head>
                <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
                <style type="text/css">
                    <?php
                          foreach ($filesStyles as $style){
                              echo file_get_contents($style);
                          }
                    ?>
                </style>
                <title>debug</title>
            </head>
            <body style="font-family: Source Sans Pro,serif;">
            <?php echo $content; ?>
            </body>
            </html>
            <?php
        } else { ?>
            <svg version="1.1"
                <?php echo(is_integer($width) ? "width=\"" . $width . "px\"" : '') ?>
                <?php echo(is_integer($height) ? "height=\"" . $height . "px\"" : '') ?>
                <?php echo(is_integer($width) && is_integer($height) ? "viewBox=\"0 0 $width $height\"" : '') ?>
                 xmlns="http://www.w3.org/2000/svg"
                 preserveAspectRatio="xMinYMin meet">
                <defs>
                    <style type="text/css">
                        <?php
                              foreach ($filesStyles as $style){
                                  echo file_get_contents($style);
                              }
                        ?>
                    </style>
                </defs>
                <foreignObject x="0" y="0" width="100%" height="100%">
                    <html xmlns="http://www.w3.org/1999/xhtml" lang="fr">
                    <body style="font-family: Source Sans Pro,serif;">
                    <?php echo $content; ?>
                    </body>
                    </html>
                </foreignObject>
            </svg>
            <?php
        }
        return ob_get_clean();
    }
}
