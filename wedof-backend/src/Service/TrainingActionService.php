<?php
// src/Service/TrainingActionService.php
namespace App\Service;

use App\Entity\Certification;
use App\Entity\Organism;
use App\Entity\Training;
use App\Entity\TrainingAction;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\enums\TrainingActionStates;
use App\Library\utils\enums\TrainingStates;
use App\Repository\TrainingActionRepository;
use App\Service\DataProviders\BaseApiService;
use App\Service\DataProviders\CpfApiService;
use App\Service\DataProviders\KairosAifApiService;
use App\Service\DataProviders\OpcoCfaApiService;
use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\QueryBuilder;
use ErrorException;
use Throwable;


class TrainingActionService
{
    private EntityManagerInterface $entityManager;
    private SessionService $sessionService;
    private TrainingService $trainingService;
    private TrainingActionRepository $trainingActionRepository;

    //-------------------
    // METHODES PUBLIQUES
    //-------------------
    public function __construct(EntityManagerInterface $entityManager, SessionService $sessionService, TrainingService $trainingService, TrainingActionRepository $trainingActionRepository)
    {
        $this->entityManager = $entityManager;
        $this->sessionService = $sessionService;
        $this->trainingService = $trainingService;
        $this->trainingActionRepository = $trainingActionRepository;
    }

    /**
     * @param string $externalId
     * @param DataProviders|null $dataProvider
     * @param array $options
     * @return TrainingAction|null
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function getByExternalId(string $externalId, DataProviders $dataProvider = null, array $options = array()): ?TrainingAction
    {
        $options = array_merge(['createIfNotExist' => false, 'refresh' => false, 'daysMaxSinceLastUpdate' => 2, 'noRefresh' => false], $options); // TODO check daysMaxSinceLastUpdate value
        if (isset($options['breakDeepCall'])) {
            $options['refresh'] = !$options['breakDeepCall'];
        }
        $trainingAction = $this->trainingActionRepository->findOneByExternalId($externalId);
        if ($options['noRefresh']) {
            return $trainingAction;
        }
        //no need to refresh it
        if ($trainingAction && in_array($trainingAction->getState(), [TrainingActionStates::DELETED()->getValue(), TrainingActionStates::ARCHIVED()->getValue()])) {
            return $trainingAction;
        }

        //update sessions and evaluations if last_update at least 2 day old by default
        if ((!$trainingAction && $options['createIfNotExist']) || ($trainingAction != null && ($options['refresh'] || $trainingAction->getLastUpdate()->diff(new DateTime())->d >= $options['daysMaxSinceLastUpdate']))) {
            $dataProvider = $trainingAction ? DataProviders::from($trainingAction->getTraining()->getDataProvider()) : $dataProvider;
            if ($dataProvider) {
                /** @var CpfApiService|KairosAifApiService $apiService */
                $apiService = BaseApiService::getCatalogApiServiceByDataProvider($dataProvider);
                $trainingActionId = $apiService->getShortTrainingActionExtId($externalId);
                if ($trainingAction) {
                    $training = $trainingAction->getTraining();
                } else {
                    $trainingId = $apiService->getTrainingExtIdFromTrainingActionExtId($externalId);
                    $options['trainingActionId'] = $trainingActionId;
                    $training = $this->trainingService->getByExternalId($trainingId, $dataProvider, $options); // Why not internal get from internal id ? maybe to create training if we don't have it ?
                }
                if ($training) {
                    $trainingActionRawData = $apiService->getTrainingActionRawData($training, $trainingActionId);
                    if ($trainingActionRawData) {
                        if ($trainingAction) {
                            $trainingAction->setLastUpdate(new DateTime());
                        }
                        $trainingAction = $this->createOrUpdate($trainingActionRawData, $training);
                        $sessionsRawData = $apiService->getSessionsRawData($trainingAction);
                        $this->sessionService->createOrUpdateSessions($sessionsRawData, $trainingAction);
                        $apiService->updateEvaluationsForTrainingAction($trainingAction);
                    }
                    $this->entityManager->flush();
                }
            }
        }
        return $trainingAction;
    }

    /**
     * @param Organism $organism
     * @param array $parameters
     * @return QueryBuilder
     */
    public function listReturnQueryBuilder(Organism $organism, array $parameters): QueryBuilder
    {
        return $this->trainingActionRepository->findAllReturnQueryBuilder($organism, $parameters);
    }

    /**
     * @param array $rawData
     * @param Training $training
     * @param array $options
     * @return TrainingAction
     * @throws ErrorException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Throwable
     */
    public function createOrUpdate(array $rawData, Training $training, array $options = []): TrainingAction
    {
        $dataProvider = DataProviders::from($training->getDataProvider());
        /** @var CpfApiService|KairosAifApiService|OpcoCfaApiService $apiService */
        $apiService = BaseApiService::getCatalogApiServiceByDataProvider($dataProvider);
        $externalId = $rawData['externalId'];
        $trainingAction = $this->getByExternalId($externalId, $dataProvider, $options);
        if ($trainingAction === null) {
            $trainingAction = new TrainingAction();
            $trainingAction->setTraining($training);
            $trainingAction->setExternalId($externalId);
        }
        $trainingAction->setState($apiService->getTrainingActionState($rawData, $training)->getValue());
        $trainingAction->setRawData($rawData);
        $trainingAction->setLastUpdate(new DateTime());

        return $this->save($trainingAction);
    }

    /**
     * @param TrainingAction $trainingAction
     * @return TrainingAction
     */
    public function save(TrainingAction $trainingAction): TrainingAction
    {
        return $this->trainingActionRepository->save($trainingAction);
    }

    /**
     * @param Training $training
     * @param TrainingActionStates $targetState
     * @return void
     */
    public function markAsFromTraining(Training $training, TrainingActionStates $targetState)
    {
        $this->trainingActionRepository->markAsFromTraining($training, $targetState);
        $this->entityManager->flush();
    }

    /**
     * @param Training $training
     * @param array $trainingActionsExternalIds
     * @return void
     */
    public function markAsDeletedFromTrainingExcludeTrainingActionExternalIds(Training $training, array $trainingActionsExternalIds)
    {
        $this->trainingActionRepository->markAsDeletedFromTrainingExcludeTrainingActionExternalIds($training, $trainingActionsExternalIds);
        $this->sessionService->markAsDeletedFromTrainingExcludeTrainingActionExternalIds($training, $trainingActionsExternalIds);
        $this->entityManager->flush();
    }

    /**
     * @param Organism $organism
     * @param Certification $certification
     * @param string $dataProvider
     * @return ArrayCollection
     */
    public function getPublishedTrainingActionsForOrganismAndCertification(Organism $organism, Certification $certification, string $dataProvider = 'cpf'): ArrayCollection
    {
        $parameters = [
            "order" => "asc",
            "state" => TrainingActionStates::PUBLISHED()->getValue(),
            "trainingState" => TrainingStates::PUBLISHED()->getValue(), // Sometimes state is not cascaded due to a bug, so here we ensure that both are published
            "certifInfo" => $certification->getCertifInfo(),
            "dataProvider" => $dataProvider
        ];
        return $this->trainingActionRepository->findAllWithParams($organism, $parameters);
    }

    /**
     * @param ArrayCollection $trainingIds
     * @param TrainingActionStates $targetState
     * @return ArrayCollection
     */
    public function markAsFromTrainingIds(ArrayCollection $trainingIds, TrainingActionStates $targetState): ArrayCollection
    {
        return $this->trainingActionRepository->markAsFromTrainingIds($trainingIds, $targetState);
    }

    /**
     * @param Training $training
     * @return ArrayCollection
     */
    public function listPublishedFromTraining(Training $training): ArrayCollection
    {
        return $this->trainingActionRepository->findAllPublishedFromTraining($training);
    }

    /**
     * @param array $externalIds
     * @return void
     */
    public function markAsDeletedFromTrainingActionExternalIds(array $externalIds): void
    {
        $this->trainingActionRepository->markAsDeletedFromTrainingActionExternalIds($externalIds);
    }

    //----------------
    // METHODES PRIVEES
    //----------------
}
