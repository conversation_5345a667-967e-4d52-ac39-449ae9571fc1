<?php

namespace App\Service;

use App\Application\MessageTemplates\Service\MessageService;
use App\Entity\Organism;
use App\Entity\Subscription;
use App\Event\Subscription\SubscriptionEvents;
use App\Exception\WedofConnectionException;
use App\Exception\WedofSubscriptionException;
use App\Library\utils\enums\SubscriptionCertifierTypes;
use App\Library\utils\enums\SubscriptionTrainingTypes;
use App\Library\utils\Tools;
use App\Repository\SubscriptionRepository;
use DateTime;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\QueryBuilder;
use ErrorException;
use Exception;
use Stripe\Exception\ApiErrorException;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Throwable;

class SubscriptionService
{
    // en minuscule pour éviter la casse
    // en cas de modif de partnership, penser à le reporter dans le front
    public const TRAINING_PARTNERSHIP_SUBSCRIPTION = [
        'dendreo' => 'api'
    ];

    public const CERTIFIER_PARTNERSHIP_SUBSCRIPTION = [];

    private SubscriptionRepository $subscriptionRepository;
    private EventDispatcherInterface $dispatcher;
    private CertificationFolderService $certificationFolderService;
    private RegistrationFolderService $registrationFolderService;
    private OrganismService $organismService;
    private ApplicationService $applicationService;

    private MessageService $messageService;

    public function __construct(SubscriptionRepository $subscriptionRepository, EventDispatcherInterface $dispatcher, CertificationFolderService $certificationFolderService, RegistrationFolderService $registrationFolderService, OrganismService $organismService, ApplicationService $applicationService, MessageService $messageService)
    {
        $this->subscriptionRepository = $subscriptionRepository;
        $this->dispatcher = $dispatcher;
        $this->certificationFolderService = $certificationFolderService;
        $this->registrationFolderService = $registrationFolderService;
        $this->organismService = $organismService;
        $this->applicationService = $applicationService;
        $this->messageService = $messageService;
    }

    //-------------------
    // METHODES PUBLIQUES
    //-------------------
    /**
     * @param Organism $organism
     * @param array $parameters
     * @return Subscription
     * @throws Throwable
     */
    public function create(Organism $organism, array $parameters): Subscription
    {
        $trialStarted = false;
        $subscription = new Subscription();
        $subscription->setOrganism($organism);
        // strtolower pour éviter la casse
        $subscription->setTrainingPartnership(!empty($parameters['training']['partnership']) ? strtolower($parameters['training']['partnership']) : null);
        $subscription->setCertifierPartnership(!empty($parameters['certifier']['partnership']) ? strtolower($parameters['certifier']['partnership']) : null);

        $isAllowedTrainingType = !empty($parameters['training']['type']) && in_array($parameters['training']['type'], [SubscriptionTrainingTypes::NONE()->getValue(), SubscriptionTrainingTypes::TRIAL()->getValue()]);
        $isKnownTrainingPartnership = array_key_exists($subscription->getTrainingPartnership(), self::TRAINING_PARTNERSHIP_SUBSCRIPTION);
        $isAllowedCertifierType = !empty($parameters['certifier']['type']) && in_array($parameters['certifier']['type'], [SubscriptionCertifierTypes::NONE(), SubscriptionCertifierTypes::TRIAL(), SubscriptionCertifierTypes::PARTNER()]);
        $isKnownCertifierPartnership = array_key_exists($subscription->getCertifierPartnership(), self::CERTIFIER_PARTNERSHIP_SUBSCRIPTION);

        if ($organism->isTrainingOrganism()) {
            if ($subscription->getTrainingPartnership() && $isKnownTrainingPartnership && !isset($parameters['training']['type'])) {
                $parameters['training']['type'] = self::TRAINING_PARTNERSHIP_SUBSCRIPTION[$subscription->getTrainingPartnership()];
            } else if (!isset($parameters['training']) ||
                !isset($parameters['training']['type']) ||
                (!$subscription->getTrainingPartnership() && !$isAllowedTrainingType) ||
                ($subscription->getTrainingPartnership() && !$isKnownTrainingPartnership)
            ) {
                $parameters['training']['type'] = SubscriptionTrainingTypes::TRIAL()->getValue();
                $trialStarted = true;
            }
        } else {
            $parameters['training']['type'] = SubscriptionTrainingTypes::NONE()->getValue();
        }

        if ($organism->isCertifierOrganism()) {
            if ($subscription->getCertifierPartnership() && $isKnownCertifierPartnership && !isset($parameters['certifier']['type'])) {
                $parameters['certifier']['type'] = self::CERTIFIER_PARTNERSHIP_SUBSCRIPTION[$subscription->getCertifierPartnership()];
            } else if (!isset($parameters['certifier']) ||
                !isset($parameters['certifier']['type']) ||
                (!$subscription->getCertifierPartnership() && !$isAllowedCertifierType) ||
                ($subscription->getCertifierPartnership() && !$isKnownCertifierPartnership)
            ) {
                $parameters['certifier']['type'] = SubscriptionCertifierTypes::TRIAL()->getValue();
                $trialStarted = true;
            }
        } else {
            $parameters['certifier']['type'] = SubscriptionCertifierTypes::NONE()->getValue();
        }

        $subscription = $this->update($subscription, $parameters);

        $this->dispatcher->dispatch(new SubscriptionEvents($subscription), SubscriptionEvents::CREATED);
        if ($trialStarted) {
            $this->dispatcher->dispatch(new SubscriptionEvents($subscription), SubscriptionEvents::TRIAL_STARTED);
        }
        if ($subscription->isAllowCertifiers()) {
            $this->certificationFolderService->createFromRegistrationFolders($organism);
        }

        return $subscription;
    }

    /**
     * @param Subscription $subscription
     * @param array $subscriptionValues
     * @param bool $newSubscription
     * @return Subscription
     * @throws Throwable
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    public function switch(Subscription $subscription, array $subscriptionValues, bool $newSubscription = false): Subscription
    {
        $wasAllowCertifiers = $subscription->isAllowCertifiers();
        $organism = $subscription->getOrganism();
        $subscription = $this->update($subscription, $subscriptionValues);

        $this->dispatcher->dispatch(new SubscriptionEvents($subscription), SubscriptionEvents::UPDATED);
        if (!$wasAllowCertifiers && $subscription->isAllowCertifiers()) {
            $this->certificationFolderService->createFromRegistrationFolders($organism);
        }
        if ($subscription->isAllowProposals() && !$organism->getSubDomain()) {
            //trigger create subdomain
            $this->organismService->createOrUpdate([], $organism);
        }
        if ((!empty($subscriptionValues['training']['type']) && $subscriptionValues['training']['type'] == SubscriptionTrainingTypes::TRIAL()->getValue())
            ||
            (!empty($subscriptionValues['certifier']['type']) && $subscriptionValues['certifier']['type'] == SubscriptionCertifierTypes::TRIAL()->getValue())) {
            $this->dispatcher->dispatch(new SubscriptionEvents($subscription), SubscriptionEvents::TRIAL_STARTED);
        }
        /** @var string $appId */
        foreach ($this->applicationService->listAllowedAppIds($subscription, true) as $appId) {
            $this->applicationService->enable($this->applicationService->getByOrganismAndAppId($organism, $appId));
        }
        if (!$newSubscription) {
            $activeAppIds = $this->applicationService->listAppIdsForOrganism($organism, true);
            foreach ($activeAppIds as $appId) {
                if (!$this->applicationService->isApplicationAllowed($appId, $subscription)) {
                    $this->applicationService->disable($this->applicationService->getByOrganismAndAppId($organism, $appId));
                }
            }
        }
        return $subscription;
    }

    /**
     * @param Subscription $subscription
     * @return Subscription
     */
    public function save(Subscription $subscription): Subscription
    {
        return $this->subscriptionRepository->save($subscription);
    }

    /**
     * @param array $parameters
     * @return QueryBuilder
     */
    public function listReturnQueryBuilder(array $parameters): QueryBuilder
    {
        return $this->subscriptionRepository->findAllReturnQueryBuilder($parameters);
    }

    /**
     * @param Subscription $subscription
     * @return Subscription
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function updateCertificationFoldersNumberCount(Subscription $subscription): Subscription
    {
        $subscription->setCertificationFoldersNumberCount($this->certificationFolderService->countForCertifierSubscription($subscription));
        if (SubscriptionCertifierTypes::ACCESS()->getValue() === $subscription->getCertifierType()) {
            $subscription->setCertificationFoldersNumberAnnualCount($this->certificationFolderService->countForCertifierSubscription($subscription, true));
            if ($subscription->isCertificationFoldersNumberLimitReached()) {
                $certificationFoldersNumberAnnualCount = $subscription->getCertificationFoldersNumberAnnualCount();
                $certificationFoldersNumberAnnualLimit = $subscription->getCertificationFoldersNumberAnnualLimit();
                if ($certificationFoldersNumberAnnualLimit + 1 === $certificationFoldersNumberAnnualCount || $certificationFoldersNumberAnnualCount % 25 === 0) {
                    $this->sendSlackAlert($subscription);
                }
            }
        }
        return $this->save($subscription);
    }

    /**
     * @param Subscription $subscription
     * @return Subscription
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function updateSmsSentNumberCount(Subscription $subscription): Subscription
    {
        $smsSentNumberCount = $this->messageService->countSmsForSubscription($subscription);
        $subscription->setSmsSentNumberCount($smsSentNumberCount);
        return $this->save($subscription);
    }

    /**
     * @param Subscription|null $subscription
     * @param int|null $folderId
     * @return Subscription
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function updateRegistrationFoldersNumberCount(Subscription $subscription, int $folderId = null): Subscription
    {
        $registrationFoldersNumberCount = $this->registrationFolderService->countForSubscription($subscription);
        $subscription->setRegistrationFoldersNumberCount($registrationFoldersNumberCount);
        if ($folderId && $subscription->getRegistrationFoldersNumberLimit() && $registrationFoldersNumberCount === $subscription->getRegistrationFoldersNumberLimit()) {
            $subscription->setLastRegistrationFolderId($folderId);
        }
        return $this->save($subscription);
    }

    /**
     * @param string $stripeCustomerId
     * @return Subscription
     * @throws WedofSubscriptionException
     */
    public function getOneByStripeCustomerId(string $stripeCustomerId): Subscription
    {
        $subscription = $this->subscriptionRepository->findOneBy(['stripeCustomerId' => $stripeCustomerId]);
        if (!$subscription) {
            throw new WedofSubscriptionException("Aucun abonnement trouvé pour le stripeCustomerId " . $stripeCustomerId);
        }
        return $subscription;
    }

    /**
     * @param Subscription $subscription
     * @param bool $checkTrainingType
     * @param bool $checkCertifierType
     * @return void
     */
    public function isPaidCustomer(Subscription $subscription, bool $checkTrainingType = true, bool $checkCertifierType = true): bool
    {
        $paidCustomer = false;
        if ($checkTrainingType) {
            $paidCustomer = in_array($subscription->getTrainingType(), SubscriptionTrainingTypes::getPaidTrainingTypes());
        }
        if (!$paidCustomer && $checkCertifierType) {
            $paidCustomer = in_array($subscription->getCertifierType(), SubscriptionCertifierTypes::getPaidCertifierTypes());
        }
        return $paidCustomer;
    }

    /**
     * @param Organism $organism
     * @param Organism $reseller
     * @return Subscription
     * @throws Throwable
     */
    public function createOrUpdateFromReseller(Organism $organism, Organism $reseller): Subscription
    {
        $metadata = $reseller->getResellerMetadata();
        return $this->switch($organism->getSubscription(), [
            "training" => ["type" => $metadata['trainingTypeSubscription']['trainingType'] ?? SubscriptionTrainingTypes::NONE()],
            "certifier" => ["type" => $metadata['certifierTypeSubscription']['certifierType'] ?? SubscriptionCertifierTypes::NONE()]
        ]);
    }

    /**
     * @param Subscription $subscription
     */
    public function updateRequestCount(Subscription $subscription): void
    {
        $requestCount = $subscription->getRequestCount();
        $subscription->setRequestCount($requestCount + 1);
        $this->save($subscription);
    }

    /**
     * @param Subscription $subscription
     * @return void
     */
    public function mutateIntoAdminSubscription(Subscription $subscription): void
    {
        $subscription->setTrainingType(SubscriptionTrainingTypes::PREMIUM());
        $subscription->setAllowApi(true);
        $subscription->setAllowAnalytics(true);
        $subscription->setAllowProposals(true);
        $subscription->setAllowRegistrationFolders(true);
        if ($subscription->getOrganism()->isCertifierOrganism()) {
            $subscription->setCertifierType(SubscriptionCertifierTypes::UNLIMITED());
            $subscription->setAllowCertifiers(true);
            $subscription->setAllowCertifierPlus(true);
            $subscription->setAllowCertifierBetaFeatures(true);
        }
    }

    //----------------
    // METHODES PRIVES
    //----------------
    /**
     * @param string $subscriptionTrainingType
     * @return array
     * contrairement à ce que intellij peut dire cette méthode est bien utilisée dans le update
     * @throws Exception
     */
    private function getSubscriptionTrainingTypeProperties(string $subscriptionTrainingType): array
    {
        if (in_array($subscriptionTrainingType, SubscriptionTrainingTypes::valuesTypes())) {
            $type = $subscriptionTrainingType;
            $subtype = 'default';
        } else {
            $typeExploded = Tools::explodeLast('_', $subscriptionTrainingType);
            $type = $typeExploded[0];
            $subtype = $typeExploded[1];
        }
        $propertiesByTypeAndSubtype = [
            SubscriptionTrainingTypes::NONE()->getValue() => [
                'default' => [
                    'trainingType' => SubscriptionTrainingTypes::NONE()->getValue(),
                    'registrationFoldersNumberLimit' => 0,
                    'allowApi' => true,
                    'allowProposals' => false,
                    'allowAnalytics' => false,
                    'trainingPendingCancellation' => false,
                    'allowRegistrationFolders' => false,
                    'trainingUsersLimit' => 1,
                    'allowPaidUsage' => false
                ]
            ],
            SubscriptionTrainingTypes::TRIAL()->getValue() => [
                'default' => [
                    'period' => "15 days",
                    'trainingType' => SubscriptionTrainingTypes::TRIAL()->getValue(),
                    'registrationFoldersNumberLimit' => null,
                    'registrationFolderNumberPeriod' => "15 days",
                    'allowApi' => true,
                    'allowProposals' => true,
                    'allowAnalytics' => true,
                    'trainingPendingCancellation' => true,
                    'allowRegistrationFolders' => true,
                    'trainingUsersLimit' => null,
                    'isTrial' => true,
                    'allowPaidUsage' => false
                ]
            ],
            SubscriptionTrainingTypes::FREE()->getValue() => [
                'default' => [
                    'period' => "1 month",
                    'trainingType' => SubscriptionTrainingTypes::FREE()->getValue(),
                    'registrationFoldersNumberLimit' => 10,
                    'registrationFolderNumberPeriod' => "1 month",
                    'allowApi' => true,
                    'allowProposals' => false,
                    'allowAnalytics' => false,
                    'trainingPendingCancellation' => false,
                    'allowRegistrationFolders' => false,
                    'trainingUsersLimit' => 1,
                    'allowPaidUsage' => false
                ]
            ],
            SubscriptionTrainingTypes::API()->getValue() => [
                'default' => [
                    'period' => "1 month",
                    'trainingType' => SubscriptionTrainingTypes::API()->getValue(),
                    'registrationFoldersNumberLimit' => null,
                    'registrationFolderNumberPeriod' => "1 month",
                    'allowApi' => true,
                    'allowProposals' => false,
                    'allowAnalytics' => false,
                    'trainingPendingCancellation' => false,
                    'allowRegistrationFolders' => false,
                    'trainingUsersLimit' => 1,
                    'allowPaidUsage' => true
                ],
                'year' => [
                    'period' => "1 year",
                    'trainingType' => SubscriptionTrainingTypes::API()->getValue(),
                    'registrationFoldersNumberLimit' => null,
                    'registrationFolderNumberPeriod' => "1 month",
                    'allowApi' => true,
                    'allowProposals' => false,
                    'allowAnalytics' => false,
                    'trainingPendingCancellation' => false,
                    'allowRegistrationFolders' => false,
                    'trainingUsersLimit' => 1,
                    'allowPaidUsage' => true
                ]
            ],
            SubscriptionTrainingTypes::ESSENTIAL()->getValue() => [
                'default' => [
                    'period' => "1 month",
                    'trainingType' => SubscriptionTrainingTypes::ESSENTIAL()->getValue(),
                    'registrationFoldersNumberLimit' => null,
                    'registrationFolderNumberPeriod' => "1 month",
                    'allowApi' => true,
                    'allowProposals' => false,
                    'allowAnalytics' => false,
                    'trainingPendingCancellation' => false,
                    'allowRegistrationFolders' => true,
                    'trainingUsersLimit' => 5,
                    'allowPaidUsage' => true
                ],
                'year' => [
                    'period' => "1 year",
                    'trainingType' => SubscriptionTrainingTypes::ESSENTIAL()->getValue(),
                    'registrationFoldersNumberLimit' => null,
                    'registrationFolderNumberPeriod' => "1 month",
                    'allowApi' => true,
                    'allowProposals' => false,
                    'allowAnalytics' => false,
                    'trainingPendingCancellation' => false,
                    'allowRegistrationFolders' => true,
                    'trainingUsersLimit' => 5,
                    'allowPaidUsage' => true
                ]
            ],
            SubscriptionTrainingTypes::ACCESS()->getValue() => [
                'default' => [
                    'period' => "1 month",
                    'trainingType' => SubscriptionTrainingTypes::ACCESS()->getValue(),
                    'registrationFoldersNumberLimit' => 50,
                    'registrationFolderNumberPeriod' => '1 year',
                    'allowApi' => true,
                    'allowProposals' => false,
                    'allowAnalytics' => true,
                    'trainingPendingCancellation' => false,
                    'allowRegistrationFolders' => true,
                    'trainingUsersLimit' => null,
                    'allowPaidUsage' => true
                ],
                'year' => [
                    'period' => "1 year",
                    'trainingType' => SubscriptionTrainingTypes::ACCESS()->getValue(),
                    'registrationFoldersNumberLimit' => 50,
                    'registrationFolderNumberPeriod' => '1 year',
                    'allowApi' => true,
                    'allowProposals' => false,
                    'allowAnalytics' => true,
                    'trainingPendingCancellation' => false,
                    'allowRegistrationFolders' => true,
                    'trainingUsersLimit' => null,
                    'allowPaidUsage' => true
                ]
            ],
            SubscriptionTrainingTypes::ACCESS_PLUS()->getValue() => [
                'default' => [
                    'period' => "1 month",
                    'trainingType' => SubscriptionTrainingTypes::ACCESS_PLUS()->getValue(),
                    'registrationFoldersNumberLimit' => 150,
                    'registrationFolderNumberPeriod' => '1 year',
                    'allowApi' => true,
                    'allowProposals' => false,
                    'allowAnalytics' => true,
                    'trainingPendingCancellation' => false,
                    'allowRegistrationFolders' => true,
                    'trainingUsersLimit' => null,
                    'allowPaidUsage' => true
                ],
                'year' => [
                    'period' => "1 year",
                    'trainingType' => SubscriptionTrainingTypes::ACCESS_PLUS()->getValue(),
                    'registrationFoldersNumberLimit' => 150,
                    'registrationFolderNumberPeriod' => '1 year',
                    'allowApi' => true,
                    'allowProposals' => false,
                    'allowAnalytics' => true,
                    'trainingPendingCancellation' => false,
                    'allowRegistrationFolders' => true,
                    'trainingUsersLimit' => null,
                    'allowPaidUsage' => true
                ]
            ],
            SubscriptionTrainingTypes::STANDARD()->getValue() => [
                'default' => [
                    'period' => "1 month",
                    'trainingType' => SubscriptionTrainingTypes::STANDARD()->getValue(),
                    'registrationFoldersNumberLimit' => null,
                    'registrationFolderNumberPeriod' => "1 month",
                    'allowApi' => true,
                    'allowProposals' => false,
                    'allowAnalytics' => true,
                    'trainingPendingCancellation' => false,
                    'allowRegistrationFolders' => true,
                    'trainingUsersLimit' => null,
                    'allowPaidUsage' => true
                ],
                'year' => [
                    'period' => "1 year",
                    'trainingType' => SubscriptionTrainingTypes::STANDARD()->getValue(),
                    'registrationFoldersNumberLimit' => null,
                    'registrationFolderNumberPeriod' => "1 month",
                    'allowApi' => true,
                    'allowProposals' => false,
                    'allowAnalytics' => true,
                    'trainingPendingCancellation' => false,
                    'allowRegistrationFolders' => true,
                    'trainingUsersLimit' => null,
                    'allowPaidUsage' => true
                ]
            ],
            SubscriptionTrainingTypes::PREMIUM()->getValue() => [
                'default' => [
                    'period' => "1 month",
                    'trainingType' => SubscriptionTrainingTypes::PREMIUM()->getValue(),
                    'registrationFoldersNumberLimit' => null,
                    'registrationFolderNumberPeriod' => "1 month",
                    'allowApi' => true,
                    'allowProposals' => true,
                    'allowAnalytics' => true,
                    'trainingPendingCancellation' => false,
                    'allowRegistrationFolders' => true,
                    'trainingUsersLimit' => null,
                    'allowPaidUsage' => true
                ],
                'year' => [
                    'period' => "1 year",
                    'trainingType' => SubscriptionTrainingTypes::PREMIUM()->getValue(),
                    'registrationFoldersNumberLimit' => null,
                    'registrationFolderNumberPeriod' => "1 month",
                    'allowApi' => true,
                    'allowProposals' => true,
                    'allowAnalytics' => true,
                    'trainingPendingCancellation' => false,
                    'allowRegistrationFolders' => true,
                    'trainingUsersLimit' => null,
                    'allowPaidUsage' => true
                ]
            ]
        ];
        if (!isset($propertiesByTypeAndSubtype[$type][$subtype])) {
            throw new Exception('Erreur subscriptionTrainingType inconnu : ' . $type . ' - ' . $subtype . ' (' . $subscriptionTrainingType . ')');
        }
        return $propertiesByTypeAndSubtype[$type][$subtype];
    }

    /**
     * @param string $subscriptionCertifierType
     * @return array
     * contrairement à ce que intellij peut dire cette méthode est bien utilisée dans le update
     * @throws Exception
     */
    private function getSubscriptionCertifierTypeProperties(string $subscriptionCertifierType): array
    {
        $propertiesByType = [
            SubscriptionCertifierTypes::NONE()->getValue() => [
                'certifierType' => SubscriptionCertifierTypes::NONE()->getValue(),
                'allowApi' => true,
                'allowCertifiers' => false,
                'certifierPendingCancellation' => false,
                'allowCertifierPlus' => false,
                'allowCertifierBetaFeatures' => false,
                'certificationFoldersNumberCap' => null,
                'certificationFoldersNumberAnnualLimit' => null,
                'certificationFolderPrice' => null,
                'certifierUsersLimit' => 1,
                'allowPaidUsage' => false
            ],
            SubscriptionCertifierTypes::TRIAL()->getValue() => [
                'period' => "15 days",
                'certifierType' => SubscriptionCertifierTypes::TRIAL()->getValue(),
                'allowApi' => true,
                'allowCertifiers' => true,
                'certifierPendingCancellation' => true,
                'allowCertifierPlus' => true,
                'allowCertifierBetaFeatures' => true,
                'certificationFoldersNumberCap' => 500,
                'certificationFolderPrice' => null,
                'certifierUsersLimit' => null,
                'certificationFoldersNumberAnnualLimit' => null,
                'certificationFoldersNumberPeriod' => '15 days',
                'isTrial' => true,
                'allowPaidUsage' => false
            ],
            //not used anymore
            SubscriptionCertifierTypes::FREE()->getValue() => [
                'certifierType' => SubscriptionCertifierTypes::FREE()->getValue(),
                'allowApi' => true,
                'allowCertifiers' => false,
                'certifierPendingCancellation' => false,
                'allowCertifierPlus' => false,
                'allowCertifierBetaFeatures' => false,
                'certificationFoldersNumberCap' => null,
                'certificationFolderPrice' => null,
                'certificationFoldersNumberAnnualLimit' => null,
                'certifierUsersLimit' => 1,
                'allowPaidUsage' => true
            ],
            SubscriptionCertifierTypes::USAGE()->getValue() => [
                'period' => "1 month",
                'certifierType' => SubscriptionCertifierTypes::USAGE()->getValue(),
                'allowApi' => true,
                'allowCertifiers' => true,
                'certifierPendingCancellation' => false,
                'allowCertifierPlus' => false, //overrided by another price in stripe
                'allowCertifierBetaFeatures' => true,
                'certificationFoldersNumberCap' => 500, // le certificationFoldersNumberCap est déterminé par le stripeService
                'certificationFoldersNumberAnnualLimit' => null,
                'certificationFoldersNumberPeriod' => '1 month',
                'allowPaidUsage' => true,
                'certifierUsersLimit' => 1 //overrided by another price in stripe
            ],
            SubscriptionCertifierTypes::ACCESS()->getValue() => [
                'period' => "1 month",
                'certifierType' => SubscriptionCertifierTypes::ACCESS()->getValue(),
                'allowApi' => true,
                'allowCertifiers' => true,
                'certifierPendingCancellation' => false,
                'allowCertifierPlus' => false, //overrided by another price in stripe
                'allowCertifierBetaFeatures' => true,
                'certificationFoldersNumberAnnualLimit' => 100, // le certificationFoldersNumberAnnualLimit est déterminé par le stripeService 100 ou 300 actuellement
                'certificationFoldersNumberCap' => 100, // le certificationFoldersNumberCap est déterminé par le stripeService 100 ou 300 actuellement
                'certificationFoldersNumberPeriod' => '1 year',
                'allowPaidUsage' => true,
                'certifierUsersLimit' => 1 //overrided by another price in stripe
            ],
            SubscriptionCertifierTypes::UNLIMITED()->getValue() => [
                'period' => "1 year",
                'certifierType' => SubscriptionCertifierTypes::UNLIMITED()->getValue(),
                'allowApi' => true,
                'allowCertifiers' => true,
                'certifierPendingCancellation' => false,
                'allowCertifierPlus' => true,
                'allowCertifierBetaFeatures' => true,
                'certificationFoldersNumberCap' => 0,
                'certificationFolderPrice' => null,
                'certificationFoldersNumberAnnualLimit' => null,
                'certificationFoldersNumberPeriod' => '1 year',
                'certifierUsersLimit' => null,
                'allowPaidUsage' => true
            ],
            SubscriptionCertifierTypes::PARTNER()->getValue() => [
                'certifierType' => SubscriptionCertifierTypes::PARTNER()->getValue(),
                'allowApi' => true,
                'allowCertifiers' => false,
                'certifierPendingCancellation' => false,
                'allowCertifierPlus' => false,
                'allowCertifierBetaFeatures' => false,
                'certificationFoldersNumberCap' => null,
                'certificationFolderPrice' => null,
                'certificationFoldersNumberAnnualLimit' => null,
                'certifierUsersLimit' => 1,
                'allowPaidUsage' => false
            ]
        ];
        if (!isset($propertiesByType[$subscriptionCertifierType])) {
            throw new Exception('Erreur subscriptionCertifierType inconnu : ' . $subscriptionCertifierType);
        }
        return $propertiesByType[$subscriptionCertifierType];
    }

    /**
     * @param Subscription $subscription
     * @param array $subscriptionValues
     * @return Subscription
     * @throws ClientExceptionInterface
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofSubscriptionException
     * @throws WedofConnectionException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws ErrorException
     * @throws ApiErrorException
     */
    private function update(Subscription $subscription, array $subscriptionValues = []): Subscription
    {
        $blackListedProperties = ['stripeCustomerId'];
        $fromTrial = $subscription->getTrainingType() === SubscriptionTrainingTypes::TRIAL()->getValue();

        foreach (['training', 'certifier'] as $scope) {
            $subscriptionProperties = !empty($subscriptionValues[$scope]['type']) ? $this->{'getSubscription' . $scope . 'TypeProperties'}($subscriptionValues[$scope]['type']) : null;
            if ($subscriptionProperties) {
                // Init dates if new, reinit dates if switch
                $previousStartDate = $subscription->{'get' . ucfirst($scope) . 'StartDate'}();
                $previousType = $subscription->{'get' . ucfirst($scope) . 'Type'}();
                $newType = $subscriptionProperties[$scope . 'Type'];
                if (!$previousStartDate || $previousType != $newType) {
                    $subscription->{'set' . ucfirst($scope) . 'StartDate'}(new DateTime('now'));
                    $subscription->{'set' . ucfirst($scope) . 'PeriodStartDate'}(new DateTime('now'));
                    $periodEndDate = !empty($subscriptionProperties['period']) ? ("+" . $subscriptionProperties['period']) : "2199-12-31"; //handle no period
                    $subscription->{'set' . ucfirst($scope) . 'PeriodEndDate'}(new DateTime($periodEndDate));
                    if (!empty($subscriptionProperties['isTrial'])) {
                        $subscription->{'set' . ucfirst($scope) . 'TrialEndDate'}(new DateTime($periodEndDate));
                    }
                }
                // Apply default properties for scope + type
                foreach ($subscriptionProperties as $key => $value) {
                    if ($key === 'certificationFoldersNumberAnnualLimit' && $subscription->getCertificationFoldersNumberAnnualLimit() && $subscription->getCertificationFoldersNumberAnnualLimit() > $value) { // jurisprudence ALLOKOM
                        $value = $subscription->getCertificationFoldersNumberAnnualLimit();
                    }
                    if (method_exists($subscription, "set" . ucfirst($key))) {
                        $subscription->{"set" . ucfirst($key)}($value);
                    }
                }
            }
            // Apply custom values
            if (isset($subscriptionValues[$scope])) {
                foreach ($subscriptionValues[$scope] as $key => $value) {
                    if (method_exists($subscription, "set" . ucfirst($key)) && !in_array($key, $blackListedProperties)) {
                        $subscription->{"set" . ucfirst($key)}($value);
                    }
                }
            }
            // Manage counts
            if ($scope === 'training') {
                $this->dispatcher->dispatch(new SubscriptionEvents($subscription), SubscriptionEvents::REQUEST_COUNT_RESET);
                $subscription->setRequestCount(0);
                if (isset($subscriptionProperties['registrationFolderNumberPeriod'])
                    && (
                        ($subscription->getRegistrationFolderNumberPeriodEndDate() && $subscription->getRegistrationFolderNumberPeriodEndDate()->format('Y-m-d') <= (new DateTime('now'))->format('Y-m-d'))
                        || !$subscription->getRegistrationFolderNumberPeriodEndDate()
                        || $fromTrial
                    )
                ) {
                    $subscription->setRegistrationFolderNumberPeriodStartDate(new DateTime('now'));
                    $subscription->setRegistrationFolderNumberPeriodEndDate(new DateTime('+' . $subscriptionProperties['registrationFolderNumberPeriod']));
                    $subscription->setLastRegistrationFolderId(null);
                    $this->updateRegistrationFoldersNumberCount($subscription);
                }
            } else if ($scope === 'certifier') {
                if (isset($subscriptionProperties['certificationFoldersNumberPeriod'])
                    && (
                        ($subscription->getCertificationFoldersNumberPeriodEndDate() && $subscription->getCertificationFoldersNumberPeriodEndDate()->format('Y-m-d') <= (new DateTime('now'))->format('Y-m-d'))
                        || !$subscription->getCertificationFoldersNumberPeriodEndDate()
                        || $fromTrial
                    )
                ) {
                    $subscription->setCertificationFoldersNumberPeriodStartDate(new DateTime('now'));
                    $subscription->setCertificationFoldersNumberPeriodEndDate(new DateTime('+' . $subscriptionProperties['certificationFoldersNumberPeriod']));
                }
                $this->updateCertificationFoldersNumberCount($subscription);
            }
        }

        return $this->save($subscription);
    }

    /**
     * @param Subscription $subscription
     * @return void
     */
    private function sendSlackAlert(Subscription $subscription): void
    {
        $organism = $subscription->getOrganism();
        if (!$_ENV["COMMERCIAL_SLACK_URI"]) {
            return;
        }
        if (!empty($_SERVER['NO_DISPATCH_WEDOF_EVENTS'])) {
            return;
        }
        try {
            Tools::getHttpClient()->request('POST', $_ENV["COMMERCIAL_SLACK_URI"], ['json' => [
                'blocks' => [
                    0 => [
                        'type' => 'section',
                        'text' => [
                            'type' => 'mrkdwn',
                            'text' => "Alerte : un client certificateur ACCESS a dépassé son plafond de dossiers ! <@U07EN26KRPD> <@U07BEGQBX52>"
                        ]
                    ],
                    1 => [
                        'type' => 'section',
                        'text' => [
                            'type' => 'mrkdwn',
                            'text' => "Nom : " . $organism->getName() . ", Siret : " . $organism->getSiret()
                        ]
                    ],
                    2 => [
                        'type' => 'section',
                        'text' => [
                            'type' => 'mrkdwn',
                            'text' => "Nombre de dossiers de certification créés : " . $subscription->getCertificationFoldersNumberAnnualCount() . "/" . $subscription->getCertificationFoldersNumberAnnualLimit() . " sur la période terminant le " . $subscription->getCertificationFoldersNumberPeriodEndDate()->format('d-m-Y')
                        ]
                    ]
                ]
            ]]);
        } catch (Throwable $e) {
        }
    }
}


