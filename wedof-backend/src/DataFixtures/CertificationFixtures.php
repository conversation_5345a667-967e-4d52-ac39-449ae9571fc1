<?php

namespace App\DataFixtures;

use App\Entity\Certification;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Common\DataFixtures\OrderedFixtureInterface;
use Doctrine\Persistence\ObjectManager;
use function Zenstruck\Foundry\faker;

class CertificationFixtures extends Fixture implements OrderedFixtureInterface
{
    public function getOrder(): int
    {
        return 3;
    }

    public function load(ObjectManager $manager)
    {
        $certification = new Certification();

        $certification->setName(faker()->word());

        $certification->setLevel(faker()->word());
        $certification->setCertifInfo(faker()->word());
        $certification->setEnabled(true);
        $certification->setCount(faker()->randomDigit());

        $this->setReference('certification', $certification);

        $manager->persist($certification);
        $manager->flush();
    }
}
