<?php

namespace App\DataFixtures;

use App\Entity\Organism;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Common\DataFixtures\OrderedFixtureInterface;
use Doctrine\Persistence\ObjectManager;
use function Zenstruck\Foundry\faker;

class OrganismFixtures extends Fixture implements OrderedFixtureInterface
{
    public function getOrder(): int
    {
        return 1;
    }

    public function load(ObjectManager $manager)
    {
        $organism = new Organism();

        $organism->setName(faker()->word());
        $organism->setSiren(faker()->randomNumber(9));
        $organism->setSiret(faker()->numerify('##############'));
        $organism->setAddress(faker()->address());
        $organism->setCity(faker()->city());
        $organism->setPostalCode(faker()->postcode());
        $organism->setLastUpdate(new \DateTime('now'));
        $organism->setToSync(false);
        $organism->setCustomizedInfos(false);

        $this->setReference('organism', $organism);

        $manager->persist($organism);
        $manager->flush();
    }
}
