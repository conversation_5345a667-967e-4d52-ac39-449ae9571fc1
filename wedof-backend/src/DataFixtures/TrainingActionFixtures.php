<?php

namespace App\DataFixtures;

use App\Entity\Training;
use App\Entity\TrainingAction;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Common\DataFixtures\OrderedFixtureInterface;
use Doctrine\Persistence\ObjectManager;

class TrainingActionFixtures extends Fixture implements OrderedFixtureInterface
{
    public function getOrder(): int
    {
        return 5;
    }

    public function load(ObjectManager $manager)
    {
        /** @var Training $training */
        $training = $this->getReference('training');

        $rawData = [
            'id' => [
                'numeroAction' => '97695081184154_usages_numerique',
                'numeroFormation' => '97695081184154_usages_numerique'
            ],
            'url' => '',
            'siren' => '687287004',
            'siret' => '97695081184154',
            'title' => 'Accompagnement à la citoyenneté numérique',
            'ville' => null,
            'distance' => null,
            'duration' => null,
            'codePostal' => null,
            'modalityCode' => '2',
            'prixTotalTTC' => 1200,
            'accessibility' => null,
            'niveauDiplome' => null,
            'intitulesDiplome' => [
                'Accompagnement à la citoyenneté numérique'
            ],
            'codesDiplomeCertification' => [
                100703
            ]
        ];

        $trainingAction = new TrainingAction();

        $trainingAction->setLastUpdate(new \DateTime('now'));
        $trainingAction->setExternalId('97695081184154_usages_numerique/97695081184154_usages_numerique');
        $trainingAction->setRawData($rawData);
        $trainingAction->setTraining($training);

        $this->setReference('trainingAction', $trainingAction);

        $manager->persist($trainingAction);
        $manager->flush();
    }
}
