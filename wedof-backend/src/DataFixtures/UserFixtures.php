<?php

namespace App\DataFixtures;

use App\Entity\ApiToken;
use App\Entity\Organism;
use App\Entity\User;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Common\DataFixtures\OrderedFixtureInterface;
use Doctrine\Persistence\ObjectManager;
use Symfony\Component\Security\Core\Encoder\UserPasswordEncoderInterface;
use function Zenstruck\Foundry\faker;

class UserFixtures extends Fixture implements OrderedFixtureInterface
{
    private UserPasswordEncoderInterface $passwordEncoder;

    public function __construct(UserPasswordEncoderInterface $passwordEncoder)
    {
        $this->passwordEncoder = $passwordEncoder;
    }

    public function getOrder()
    {
        return 2;
    }

    public function load(ObjectManager $manager)
    {
        $user = new User();

        $user->setEmail('<EMAIL>');

        $user->setPassword($this->passwordEncoder->encodePassword(
            $user,
            'test'
        ));

        $user->setFirstName(faker()->firstName());
        $user->setLastName(faker()->lastName());
        $user->setRgpdMsa(new \DateTime('now'));
        $user->setRoles(array('ROLE_USER'));

        /** @var Organism $organism */
        $organism = $this->getReference('organism');

        $user->setMainOrganism($organism);

        $apiToken = new ApiToken();

        $apiToken->setName(faker()->word());
        $apiToken->setUser($user);
        $apiToken->setToken(faker()->word());

        $this->setReference('user', $user);

        $manager->persist($user);
        $manager->persist($apiToken);
        $manager->flush();
    }
}
