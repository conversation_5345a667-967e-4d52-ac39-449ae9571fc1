<?php

namespace App\DataFixtures;

use App\Entity\Organism;
use App\Entity\Session;
use App\Factory\AttendeeFactory;
use App\Factory\CertificationFolderFactory;
use App\Factory\RegistrationFolderFactory;
use App\Factory\RegistrationFolderHistoryFactory;
use App\Library\utils\enums\RegistrationFolderAttendeeStates;
use App\Library\utils\enums\RegistrationFolderBillingStates;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Common\DataFixtures\OrderedFixtureInterface;
use Doctrine\Persistence\ObjectManager;

class RegistrationFolderFixtures extends Fixture implements OrderedFixtureInterface
{
    public function getOrder(): int
    {
        return 7;
    }

    public function load(ObjectManager $manager)
    {
        $rawData = array(
            'id' => '4292563923',
            'title' => '0',
            'reason' => '0',
            'states' =>
                array(
                    'billed' => null,
                    'accepted' =>
                        array(
                            'date' => '2021-04-20T12:40:33.000Z',
                        ),
                    'rejected' => null,
                    'validated' =>
                        array(
                            'date' => '2021-04-19T12:43:10.000Z',
                        ),
                    'inTraining' =>
                        array(
                            'date' => '2021-05-03T00:00:00.000Z',
                        ),
                    'terminated' =>
                        array(
                            'date' => '2021-05-03T00:00:00.000Z',
                        ),
                    'notProcessed' =>
                        array(
                            'date' => '2021-04-19T12:41:45.000Z',
                        ),
                    'refusedByAttendee' => null,
                    'refusedByOrganism' => null,
                    'canceledByAttendee' => null,
                    'canceledByOrganism' => null,
                    'serviceDoneDeclared' => null,
                    'serviceDoneValidated' =>
                        array(
                            'date' => '2021-05-06T23:16:42.000Z',
                        ),
                    'canceledByAttendeeNotRealized' => null,
                ),
            'endDate' => '2021-05-03T00:00:00.000Z',
            'history' =>
                array(
                    0 =>
                        array(
                            'date' => '2021-05-06T23:16:42.000Z',
                            'label' => 'Cloture totale',
                            'author' => null,
                        ),
                    1 =>
                        array(
                            'date' => '2021-05-04T18:21:22.000Z',
                            'label' => 'Déclaration service fait titulaire',
                            'author' => '42HRA742',
                        ),
                    2 =>
                        array(
                            'date' => '2021-05-04T15:37:21.000Z',
                            'label' => 'Déclaration service fait OF',
                            'author' => '53222292400039',
                        ),
                    3 =>
                        array(
                            'date' => '2021-05-04T15:36:06.000Z',
                            'label' => 'Sortie de Formation',
                            'author' => '53222292400039',
                        ),
                    4 =>
                        array(
                            'date' => '2021-05-03T21:12:13.000Z',
                            'label' => 'Entrée en Formation',
                            'author' => '53222292400039',
                        ),
                    5 =>
                        array(
                            'date' => '2021-04-20T12:40:33.000Z',
                            'label' => 'Validation',
                            'author' => null,
                        ),
                    6 =>
                        array(
                            'date' => '2021-04-20T12:39:45.000Z',
                            'label' => 'Abondement titulaire',
                            'author' => '42HRA742',
                        ),
                    7 =>
                        array(
                            'date' => '2021-04-19T12:43:10.000Z',
                            'label' => 'Proposition OF',
                            'author' => '53222292400039',
                        ),
                    8 =>
                        array(
                            'date' => '2021-04-19T12:41:45.000Z',
                            'label' => 'Modification',
                            'author' => '42HRA742',
                        ),
                    9 =>
                        array(
                            'date' => '2021-04-19T12:41:45.000Z',
                            'label' => 'Inscription',
                            'author' => '42HRA742',
                        ),
                    10 =>
                        array(
                            'date' => '2021-04-19T12:41:12.000Z',
                            'label' => 'Création',
                            'author' => '42HRA742',
                        ),
                ),
            'attendee' =>
                array(
                    'email' => '<EMAIL>',
                    'degree' => 7,
                    'address' =>
                        array(
                            'id' => null,
                            'city' => 'SENS',
                            'line4' => null,
                            'number' => '1',
                            'country' => null,
                            'postBox' => null,
                            'zipCode' => '89100',
                            'roadName' => 'test',
                            'roadType' => 'RUE',
                            'idAddress' => null,
                            'residence' => null,
                            'countryCode' => null,
                            'fullAddress' => null,
                            'trainingSite' => null,
                            'corporateName' => 'Mme Test Test',
                            'roadTypeLabel' => 'Rue',
                            'informationSite' => null,
                            'repetitionIndex' => null,
                            'subscriptionSite' => null,
                            'additionalAddress' => null,
                            'repetitionIndexLabel' => null,
                            'reducedMobilityAccessCompliant' => null,
                            'reducedMobilityAccessModalities' => null,
                        ),
                    'lastName' => 'Test',
                    'firstName' => 'Test',
                    'phoneFixed' => null,
                    'degreeTitle' => 'Diplôme ou titre à finalité professionnelle de niveau Bac+5  (Master, DEA, DESS, diplôme d\'ingénieur)',
                    'displayName' => 'Test Test',
                    'phoneNumber' => '0700000000',
                ),
            'beginDate' => '2021-05-03T00:00:00.000Z',
            'trainingId' => '53222292400039_wensei-scrum-psc2',
            'billingDate' => null,
            'description' => '0',
            'cancellation' => null,
            'currentState' => 'serviceDoneValidated',
            'paymentState' => null,
            'refusedMotif' => null,
            'hasBeenOpened' => false,
            'trainingActionId' => '53222292400039_wensei-scrum-psc2-online',
            'changingStateDate' => '2021-05-06T23:16:42.000Z',
            'statutFacturation' => null,
            'idSuiviFinancement' => 30290604,
            'trainingActionInfo' =>
                array(
                    'vat' => 0,
                    'title' => 'Professionnel Scrum Certifié : Product Owner / Scrum Master',
                    'reason' => '8',
                    'address' => null,
                    'content' => '<h5>l\'Agilité c\'est quoi ?</h5><ul><li>Les racines de l\'Agilité</li><li>Le manifeste Agile & ses 12 principes</li><li>Un cycle de vie différent</li></ul><h5>La méthode Scrum</h5><ul><li>Les 3 piliers de Scrum</li><li>L\'équipe Scrum, les 3 rôles (Product Owner, Scrum Master, Équipier)</li><li>Les responsabilités de chaque rôle</li><li>Les pratiques d\'ingénierie agiles du logiciel</li></ul><h5>Les rôles de Scrum en détail</h5><ul><li>Un facilitateur et un garant de la méthode, le Scrum Master</li><li>Celui qui définit la vision produit, le Product Owner</li><li>Responsabilités croisées entre Scrum Master et Product Owner</li><li><b>Atelier en ligne : Définition de la vision produit & rédaction de Features</b></li></ul><h5>Le Backlog de produit</h5>
<ul><li>Cycle de vie d\'une User Story</li><li>Rédiger les Users Stories à partir des Features</li><li>Découper, détailler et prioriser les Users Stories dans le Backlog de produit</li><li>Business Value et Effort quelles différences ?</li><li><b>Atelier en ligne : Rédaction / découpage de Users Stories et écriture des critères d\'acceptations</b></li></ul><h5>Gestion du backlog et préparation du Sprint</h5><ul><li>Techniques d\'estimations des Users Stories Priorisation & plans (Périmètre fixé, Date fixée)</li><li>Vélocité estimée & mesurée de l\'équipe Définition de prêt et de fini</li><li><b>Atelier en ligne : Animation session estimation par Planning poker</b></li></ul>
<h5>Démarrage et déroulé du Sprint</h5><ul><li>Planification de sprint</li><li>Le Daily Scrum : 3 questions quotidiennes<li>Gestion des impondérables</li>
<li>L\'avancement du sprint</li><li>Les tests dans un contexte Agile</li><li><b>Atelier en ligne : Session de planification de sprint : Création des tâches</b></li><li>Innovation game : Scrum from Hell</li></ul><h5>Prendre en compte le Feedback et fin de Sprint</h5><ul><li>Prendre en compte le changement L\'intérêt de la démo et des incréments partiels</li><li>S\'améliorer à intervalles réguliers</li>
<li>La "sprint retrospective"</li><li>Innovation game : Animation d\'un rétrospective avec le Speed boat</li></ul><h5>Les outils de suivi du Product Owner</h5><ul><li>Suivre le projet</li><li>Indicateurs et Key Performance Indicators (KPI)</li><li>Suivre la qualité</li><li>Comprendre les graphiques : Burndown / Burnup etc.. de Scrum et Kanban</li></ul><h5>Préparation à la certification</h5><ul><li>Préconisations et mode d\'emploi, les pièges à éviter</li></ul>',
                    'sessionId' => '53222292400039_scrum-online-mai',
                    'totalExcl' => 658.33,
                    'totalIncl' => 790,
                    'companyName' => 'KAGILUM',
                    'vatExclTax5' => 0,
                    'vatInclTax5' => 0,
                    'teachingFees' => 790,
                    'trainingGoal' => '<img src="https://www.wensei.com/edof/psc.png" width="25%" class="float-left"><p>L\'objectif est de donner aux participants aspirant à tenir le rôle de Scrum Master ou Product Owner des bases solides sur Scrum et ses principes fondamentaux pour qu\'ils puissent décider comment en tirer profit dans leur réalité. Par exemple :</p> 
<ul>
<li>Connaître les principes, méthodes et techniques Agiles</li>
<li>Acquérir les compétences pour <b>gérer et motiver les équipes</b></li>
<li>Aider à la <b>collaboration entre acteurs</b> du projet</li>
<li>Faciliter le travail de l\'équipe et du PO</li>
<li>Savoir animer les différentes <b>cérémonies Scrum</b></li>
<li>Comprendre la notion de <b>servant Leadership</b></li>
<li><b>Collaborer avec l\'équipe</b> de développement, le Scrum master et les stakeholders</li>
<li>Etre en capacité à <b>commencer une activité</b> de Scrum Master ou Product Owner</li>
  <img src="https://www.wensei.com/edof/values1.png" width="40%" class="float-right">
<li>Plus globalement les participants apprennent pourquoi certaines décisions sont meilleures que d\'autres, en quoi certaines favorisent l\'Agilité tandis que d\'autres nous entraînent de nouveau vers un modèle en cascade.</li>
  <li>Ils apprendront aussi comment utiliser les indicateurs de productivité de Scrum pour mesurer les résultats de leurs décisions et comment optimiser ces résultats.</li>
  <li><b>Passage de la certifications PSC inclus (Professionnel Scrum Certifié, titre reconnu par l\'état)</b> dans le coût de la formation :</li>
</ul>
<div class="text-center">
  <img src="https://www.wensei.com/edof/psc.svg" width="20%" class="ml-4 m-3">
</div>
<div class="mt-3">
  <h5 class="mb-3"><strong>Une question ? Besoin de conseils ? Parlez à un formateur</strong></h5>
  <div class="d-flex flex-wrap list-infos mb-3">
    <div class="panel-body-section-icon">
      <span class="material-icons mat-icon notranslate mat-icon-no-color">chat</span>
      <p><a href="https://www.wensei.com/fr/training">Discutez en ligne</a></p>
    </div>
    <div class="panel-body-section-icon">
      <span class="material-icons mat-icon notranslate mat-icon-no-color">call</span>
      <p><a href="https://www.ringover.me/X6dOeNPMdjCg4MU">Appelez nous</a></p>
    </div>
    <div class="panel-body-section-icon">
      <span class="material-icons mat-icon notranslate mat-icon-no-color">alternate_email</span>
      <p><a href="mailto:<EMAIL>">Envoyez un email</a></p>
    </div>
  </div>
</div>',
                    'vatExclTax20' => 658.33,
                    'vatInclTax20' => 790,
                    'hoursInCenter' => 0,
                    'solicitations' =>
                        array(
                            0 =>
                                array(
                                    'amount' => 290,
                                    'status' => 'validated',
                                    'returnDate' => '2021-04-20T12:40:33.000Z',
                                    'fundingType' => 29,
                                    'refusalReason' => null,
                                    'submissionDate' => '2021-04-20T12:39:45.000Z',
                                ),
                        ),
                    'trainingPaces' =>
                        array(
                            0 => '1',
                            1 => '7',
                        ),
                    'additionalFees' => 0,
                    'dureeAbsenceOF' => 0,
                    'expectedResult' => '<ul> <li>Obtenir la certification Professionnel Scrum certifié (PSC)</li> <li>Réussir la certification PSM1, PSPO1 de chez Scrum.org (Certification en supplément)</li> <li>Être prêt à intégrer une équipe Scrum en tant que Scrum Master, Product Owner ou Développeur Agile</li> <li>Pouvoir postuler à des offres de Scrum Master, Product Owner</li> <li>Savoir faire la différence entre du Dark Scrum et du Scrum</li> <li>Comprendre l\'état d\'esprit de l\'Agilité</li> <li>Replacer l\'humain au centre des projets</li> <li>Faire de meilleurs produits, plus utile avec une meilleure satisfaction utilisateur</li> </ul> Compétences évaluées à l\'issue de la formation : <ul> <li>Identifier le rôle et les fonctions du Product Owner.</li> <li>Identifier le rôle et les fonctions du Scrum Master.</li> <li>Identifier le rôle et les fonctions des membres de l\'équipe.</li> <li>Identifier le rôle et les fonctions des Stackholders.</li> <li>Planifier, initier et conduire un projet Scrum.</li> <li>Identifier, impliquer et faire collaborer les différents intervenants dans un projet Scrum.</li> <li>Mesurer l\'impact de l\'implication et de l\'investissement du Product Owner sur le projet.</li> <li>Créer un environnement propice à la créativité et à la performance de l\'équipe Scrum.</li> <li>Manager une nouvelle relation fournisseur.</li> <li>Établir la planification d\'une version de produit.</li> <li>Gérer et motiver les équipes Scrum dans la planification des sprints, les revues et les rétrospectives.</li> <li>Définir une vision du produit final, maîtriser les méthodes de planification des user stories dans le backlog (planning poker notamment).</li> <li>Découper chaque user story en tâches.</li> <li>Évaluer ses tâches grâce à la technique du "planning poker". Communiquer avec son équipe via les "Mêlées quotidiennes".</li> <li>Organiser son travail grâce au "tableau des tâches". Analyser la santé de son projet grâce aux indicateurs Scrum. Livrer des morceaux de projets, de manière itérative, grâce aux sessions de travail organisées en "sprints" de courte durée.</li> <li>Gérer son projet et la communication avec le client final de manière plus efficace grâce aux courtes itérations, aux indicateurs et aux retours d\'expérience des équipes</li> <ul>',
                    'hoursInCompany' => 0,
                    'sessionEndDate' => '2021-05-04T00:00:00.000Z',
                    'uniteAbsenceOF' => 1,
                    'averageDuration' => null,
                    'sessionStartDate' => '2021-05-03T00:00:00.000Z',
                    'forceMajeurCaseOf' => false,
                    'indicativeDuration' => 14,
                    'teachingModalities' => '2',
                    'trainingReleaseDate' => '2021-05-03T00:00:00.000Z',
                    'typeOfTrainingCourse' => 1,
                    'validatedServiceDone' => true,
                    'additionalFeesDetails' => null,
                    'isConditionsPrerequisites' => false,
                    'conditionsPrerequisitesDetails' => null,
                ),
            'completionRate' => 100,
            'currentBillingState' => 'toBill',
            'currentAttendeeState' => 'serviceDoneDeclared',
            'currentAttendeeStateDate' =>
                array(
                    'date' => '2021-05-04 18:21:22.000000',
                    'timezone' => 'Z',
                    'timezone_type' => 2,
                ),
        );

        /** @var Session $session */
        $session = $this->getReference('session');

        /** @var Organism $organism */
        $organism = $this->getReference('organism');

        RegistrationFolderFactory::new()
            ->withAttributes(
                [
                    'history' => RegistrationFolderHistoryFactory::new(),
                    'attendee' => AttendeeFactory::new(),
                    'session' => $session,
                    'organism' => $organism,
                    'rawData' => $rawData,
                    'attendeeState' => RegistrationFolderAttendeeStates::SERVICE_DONE_NOT_DECLARED()->getValue(),
                    'billingState' => RegistrationFolderBillingStates::PAID()->getValue(),
                    'certificationFolder' => CertificationFolderFactory::new()
                ])
            ->createMany(20);

        $manager->flush();
    }
}
