<?php

namespace App\DataFixtures;

use App\Entity\Certification;
use App\Entity\Organism;
use App\Entity\Training;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Common\DataFixtures\OrderedFixtureInterface;
use Doctrine\Persistence\ObjectManager;

class TrainingFixtures extends Fixture implements OrderedFixtureInterface
{
    public function getOrder(): int
    {
        return 4;
    }

    public function load(ObjectManager $manager)
    {
        /** @var Certification $certification */
        $certification = $this->getReference('certification');

        /** @var Organism $organism */
        $organism = $this->getReference('organism');

        $training = new Training();
        $training->setCertification($certification);
        $training->setOrganism($organism);
        $training->setTitle('training');
        $training->setExternalId('97695081184154_usages_numerique');
        $training->setLastUpdate(new \DateTime('now'));

        $this->setReference('training', $training);

        $manager->persist($training);
        $manager->flush();
    }
}
