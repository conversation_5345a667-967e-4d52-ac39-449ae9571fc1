<?php

namespace App\DataFixtures;

use App\Entity\Session;
use App\Entity\TrainingAction;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Common\DataFixtures\OrderedFixtureInterface;
use Doctrine\Persistence\ObjectManager;

class SessionFixtures extends Fixture implements OrderedFixtureInterface
{
    public function getOrder(): int
    {
        return 6;
    }

    public function load(ObjectManager $manager)
    {
        $rawData = [
            'id' => '97695081184154_usages_numerique',
            'period' => [
                'endDate' => null,
                'startDate' => null
            ],
            'endDate' => null,
            'beginDate' => null,
            'actionNumber' => '97695081184154_usages_numerique',
            'sessionNumber' => '97695081184154_usages_numerique',
            'formationNumber' => '97695081184154_usages_numerique',
            'libelleGarantie' => 'Non',
            'subscriptionPeriod' => [
                'endDate' => null,
                'startDate' => null
            ],
            'eligibleToPoleEmploiAbondement' => false
        ];

        $session = new Session();

        /** @var TrainingAction $trainingAction */
        $trainingAction = $this->getReference('trainingAction');

        $session->setTrainingAction($trainingAction);
        $session->setRawData($rawData);
        // id training / id training action / id session
        $session->setExternalId('97695081184154_usages_numerique/97695081184154_usages_numerique/97695081184154_usages_numerique');

        $date = new \DateTime('now');
        $session->setStartDate(new \DateTime('now'));
        $date->modify('+1 day');
        $session->setEndDate($date);

        $this->setReference('session', $session);

        $manager->persist($session);
        $manager->flush();
    }
}
