<?php

namespace App\Security;

use App\Entity\Attendee;
use App\Entity\User;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Security\Http\Authenticator\InteractiveAuthenticatorInterface;
use Symfony\Component\Security\Http\Event\LoginSuccessEvent;

class UserLastLoginSubscriber implements EventSubscriberInterface, LoggerAwareInterface
{

    private LoggerInterface $logger;

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    private EntityManagerInterface $em;

    public function __construct(EntityManagerInterface $em)
    {
        $this->em = $em;
    }

    public function onLoginSuccess(LoginSuccessEvent $event)
    {
        if (!!$event->getRequest()->get("impersonate", false)) {
            $user = $event->getUser();
            if ($user instanceof User && $event->getAuthenticator() instanceof InteractiveAuthenticatorInterface && $event->getAuthenticator()->isInteractive()) {
                $user->setLastLogin(new DateTime());
                $this->em->persist($user);
                $this->em->flush();
            } else if ($user instanceof Attendee && $event->getAuthenticator() instanceof InteractiveAuthenticatorInterface && $event->getAuthenticator()->isInteractive()) {
                $user->setLastLogin(new DateTime());
                $this->em->persist($user);
                $this->em->flush();
            }
        }
    }

    public static function getSubscribedEvents(): array
    {
        return [
            LoginSuccessEvent::class => ['onLoginSuccess', 10]
        ];
    }
}
