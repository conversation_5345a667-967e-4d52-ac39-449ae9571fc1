<?php

namespace App\Security;

use App\Service\Id360Service;
use Lexik\Bundle\JWTAuthenticationBundle\Security\Http\Authentication\AuthenticationFailureHandler;
use Lexik\Bundle\JWTAuthenticationBundle\Security\Http\Authentication\AuthenticationSuccessHandler;
use Symfony\Component\HttpFoundation\Request;

use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Exception\AuthenticationException;

use Symfony\Component\Security\Core\User\UserProviderInterface;
use Symfony\Component\Security\Http\Authenticator\AbstractAuthenticator;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\UserBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\SelfValidatingPassport;
use Symfony\Component\Security\Http\HttpUtils;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;

class Id360Authenticator extends AbstractAuthenticator
{

    private Id360Service $id360Service;
    private HttpUtils $httpUtils;
    private string $checkPath;
    private UserProviderInterface $userProvider;
    private AuthenticationSuccessHandler $successHandler;
    private AuthenticationFailureHandler $failureHandler;

    public function __construct(Id360Service $id360Service, HttpUtils $httpUtils, string $checkPath, UserProviderInterface $userProvider, AuthenticationSuccessHandler $successHandler, AuthenticationFailureHandler $failureHandler)
    {
        $this->id360Service = $id360Service;
        $this->httpUtils = $httpUtils;
        $this->checkPath = $checkPath;
        $this->userProvider = $userProvider;
        $this->successHandler = $successHandler;
        $this->failureHandler = $failureHandler;
    }

    public function supports(Request $request): bool
    {
        return $this->httpUtils->checkRequestPath($request, $this->checkPath);
    }

    public function getCredentials(Request $request): array
    {
        return ["token" => json_decode($request->getContent())->token];
    }

    /**
     * @param Request $request
     * @return SelfValidatingPassport
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    public function authenticate(Request $request): SelfValidatingPassport
    {
        try {
            $credentials = $this->getCredentials($request);
        } catch (BadRequestHttpException $e) {
            $request->setRequestFormat('json');
            throw $e;
        }
        $id = $this->id360Service->getIdFromToken($credentials['token']);
        if (!$id) {
            throw new AuthenticationException("Attendee not found");
        }
        return new SelfValidatingPassport(new UserBadge($id, [$this->userProvider, 'loadUserByIdentifier']));
    }

    public function onAuthenticationSuccess(Request $request, TokenInterface $token, string $firewallName): ?Response
    {
        return $this->successHandler->onAuthenticationSuccess($request, $token);
    }

    public function onAuthenticationFailure(Request $request, AuthenticationException $exception): ?Response
    {
        return $this->failureHandler->onAuthenticationFailure($request, $exception);
    }
}
