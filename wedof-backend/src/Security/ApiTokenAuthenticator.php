<?php

namespace App\Security;

use App\Exception\WedofSubscriptionException;
use App\Repository\ApiTokenRepository;
use DateTime;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Exception\AuthenticationException;
use Symfony\Component\Security\Core\Exception\CustomUserMessageAuthenticationException;
use Symfony\Component\Security\Http\Authenticator\AbstractAuthenticator;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\UserBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\SelfValidatingPassport;

class ApiTokenAuthenticator extends AbstractAuthenticator
{
    private ApiTokenRepository $apiTokenRepository;
    /**
     * ApiTokenAuthenticator constructor.
     * @param ApiTokenRepository $apiTokenRepository
     */
    public function __construct(ApiTokenRepository $apiTokenRepository)
    {
        $this->apiTokenRepository = $apiTokenRepository;
    }

    /**
     * @param Request $request
     * @return bool
     */
    public function supports(Request $request): bool
    {
        return isset($_SERVER['X-API-KEY']) || $request->headers->has('X-API-KEY') || $request->query->has('X-API-KEY');
    }

    /**
     * @param Request $request
     * @return SelfValidatingPassport
     * @throws WedofSubscriptionException
     */
    public function authenticate(Request $request): SelfValidatingPassport
    {
        $token = $_SERVER['X-API-KEY'] ?? ($request->headers->has('X-API-KEY') ? $request->headers->get('X-API-KEY') : $request->query->get('X-API-KEY'));
        $token = $this->apiTokenRepository->findOneBy(['token' => $token]);
        if (empty($token)) {
            throw new CustomUserMessageAuthenticationException('Invalid API Token');
        }
        if (!$token->getUser()->getMainOrganism()->getSubscription()->isAllowApi()) {
            throw new WedofSubscriptionException("Votre abonnement ne permet pas d'utiliser l'API. Choisissez un autre abonnement afin de l'utiliser.");
        }
        $token->setLastUsed(new DateTime());
        $this->apiTokenRepository->save($token);
        return new SelfValidatingPassport(new UserBadge($token->getUser()->getEmail()));
    }

    /**
     * @param Request $request
     * @param AuthenticationException $exception
     * @return JsonResponse
     */
    public function onAuthenticationFailure(Request $request, AuthenticationException $exception): JsonResponse
    {
        return new JsonResponse(['message' => $exception->getMessageKey()], Response::HTTP_UNAUTHORIZED);
    }

    /**
     * @param Request $request
     * @param TokenInterface $token
     * @param string $firewallName
     * @return Response|null
     */
    public function onAuthenticationSuccess(Request $request, TokenInterface $token, string $firewallName): ?Response
    {
        return null;
    }
}
