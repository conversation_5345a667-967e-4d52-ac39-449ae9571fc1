<?php

namespace App\Security;

use App\Entity\Attendee;
use App\Service\AttendeeService;
use Lexik\Bundle\JWTAuthenticationBundle\Security\Http\Authentication\AuthenticationSuccessHandler;
use Symfony\Component\Security\Http\Authentication\AuthenticationSuccessHandlerInterface;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class LoginLinkSuccessHandler implements AuthenticationSuccessHandlerInterface
{
    private AuthenticationSuccessHandler $customSuccessHandler;
    private AttendeeService $attendeeService;

    public function __construct(AuthenticationSuccessHandler $customSuccessHandler, AttendeeService $attendeeService)
    {
        $this->customSuccessHandler = $customSuccessHandler;
        $this->attendeeService = $attendeeService;
    }

    public function onAuthenticationSuccess(Request $request, TokenInterface $token): Response
    {
        if (in_array('ROLE_ATTENDEE', $token->getUser()->getRoles())) {
            /** @var Attendee $attendee */
            $attendee = $token->getUser();
            if (!$request->get("impersonate", false)) {
                $attendee->setEmailValidated(true);
            }
            $this->attendeeService->save($attendee);
        }

        return $this->customSuccessHandler->onAuthenticationSuccess($request, $token);
    }
}
