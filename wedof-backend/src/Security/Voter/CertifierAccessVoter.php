<?php

namespace App\Security\Voter;

use App\Entity\CertifierAccess;
use App\Entity\User;
use App\Service\AccessService;
use LogicException;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;
use Symfony\Component\Security\Core\Security;

class CertifierAccessVoter extends Voter
{
    const VIEW = 'view';
    const EDIT = 'edit';
    const CREATE = 'create';

    private Security $security;
    private AccessService $accessService;

    public function __construct(Security $security, AccessService $accessService)
    {
        $this->security = $security;
        $this->accessService = $accessService;
    }

    /**
     * @inheritDoc
     */
    protected function supports($attribute, $subject): bool
    {
        return $subject instanceof CertifierAccess;
    }

    /**
     * @inheritDoc
     */
    protected function voteOnAttribute($attribute, $subject, TokenInterface $token): bool
    {
        if ($this->security->isGranted('ROLE_ADMIN')) {
            return true;
        }

        $accessGranted = false;
        /** @var CertifierAccess $certifierAccess */
        $certifierAccess = $subject;
        /** @var $user User */
        $user = $token->getUser();

        switch ($attribute) {
            case self::VIEW:
                if ($this->security->isGranted('ROLE_USER') || $this->security->isGranted('ROLE_OAUTH2_CERTIFIERACCESS:READ')) {
                    $accessGranted = $this->accessService->hasCertifierAccessView($user, $certifierAccess);
                }
                break;
            case self::EDIT:
                if ($this->security->isGranted('ROLE_USER') || $this->security->isGranted('ROLE_OAUTH2_CERTIFIERACCESS:WRITE')) {
                    $accessGranted = $this->accessService->hasCertifierAccessEdit($user, $certifierAccess);
                }
                break;
            case self::CREATE:
                if ($user->getMainOrganism()->getSubscription()->isAllowCertifiers() && ($this->security->isGranted('ROLE_USER') || $this->security->isGranted('ROLE_OAUTH2_CERTIFIERACCESS:WRITE'))) {
                    $accessGranted = $this->accessService->hasCertifierAccessCreate($user, $certifierAccess);
                }
                break;
            default:
                throw new LogicException("CertifierAccess Voter can only respond on \"view\", \"edit\" and \"create\" attributes.");
        }
        return $accessGranted;
    }
}