<?php
// src/Security/Voter/RegistrationFolderVoter.php
namespace App\Security\Voter;

use App\Entity\Attendee;
use App\Entity\Certification;
use App\Entity\User;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use LogicException;
use App\Service\AccessService;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;
use Symfony\Component\Security\Core\Security;

class CertificationVoter extends Voter
{
    const VIEW = 'view';
    const VIEW_ADVANCED = 'viewAdvanced';
    const EDIT = 'edit';
    const ATTENDEE_VIEW = 'attendeeView';

    private Security $security;
    private AccessService $accessService;

    public function __construct(Security $security, AccessService $accessService)
    {
        $this->security = $security;
        $this->accessService = $accessService;
    }

    protected function supports($attribute, $subject): bool
    {
        return $subject instanceof Certification;
    }

    /**
     * @param $attribute
     * @param mixed $subject
     * @param TokenInterface $token
     * @return bool
     * @throws NonUniqueResultException
     * @throws NoResultException
     */
    protected function voteOnAttribute($attribute, $subject, TokenInterface $token): bool
    {
        if ($this->security->isGranted('ROLE_ADMIN') || $this->security->isGranted('ROLE_DATA_PARTNER')) {
            return true;
        }

        $accessGranted = false;
        /** @var $certification Certification */
        $certification = $subject;

        switch ($attribute) {
            case self::VIEW:
                // Everybody logged in can display the certification
                $accessGranted = $this->security->isGranted('ROLE_USER') || $this->security->isGranted('ROLE_OAUTH2_CERTIFICATION:READ');
                break;
            case self::VIEW_ADVANCED:
                // Only partners & certifier can see more details
                /** @var $user User */
                $user = $token->getUser();
                if ($this->security->isGranted('ROLE_USER') || $this->security->isGranted('ROLE_OAUTH2_CERTIFICATION:READ')) {
                    $accessGranted = $this->accessService->hasCertificationAdvancedView($user, $certification);
                }
                break;
            case self::EDIT:
                /** @var $user User */
                $user = $token->getUser();
                if (($this->security->isGranted('ROLE_USER') || $this->security->isGranted('ROLE_OAUTH2_CERTIFICATION:WRITE')) && $user->getMainOrganism()->getSubscription()->isAllowCertifiers()) {
                    $accessGranted = $this->accessService->hasCertificationEdit($user, $certification);
                }
                break;
            case self::ATTENDEE_VIEW:
                /** @var $user Attendee */
                $user = $token->getUser();
                if ($this->security->isGranted('ROLE_ATTENDEE')) {
                    $accessGranted = $this->accessService->hasCertificationAttendeeView($user, $certification);
                }
                break;
            default:
                throw new LogicException("Certification Voter can only respond on \"view\", \"viewAdvanced\", \"attendeeView\" and \"edit\" attribute.");
        }

        return $accessGranted;
    }
}
