<?php
// src/Security/Voter/TrainingActionVoter.php
namespace App\Security\Voter;

use App\Entity\TrainingAction;
use App\Entity\User;
use App\Service\AccessService;
use LogicException;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;
use Symfony\Component\Security\Core\Security;


class TrainingActionVoter extends Voter
{
    const VIEW = 'view';

    private Security $security;
    private AccessService $accessService;

    public function __construct(AccessService $accessService, Security $security)
    {
        $this->accessService = $accessService;
        $this->security = $security;
    }

    protected function supports($attribute, $subject): bool
    {
        return $subject instanceof TrainingAction;
    }

    /**
     * @param $attribute
     * @param mixed $subject
     * @param TokenInterface $token
     * @return bool
     */
    protected function voteOnAttribute($attribute, $subject, TokenInterface $token): bool
    {
        if ($this->security->isGranted('ROLE_ADMIN')) {
            return true;
        }

        $accessGranted = false;
        /** @var $user User */
        $user = $token->getUser();


        /** @var $trainingAction TrainingAction */
        $trainingAction = $subject;

        switch ($attribute) {
            case self::VIEW:
                if ($this->security->isGranted('ROLE_USER') || $this->security->isGranted('ROLE_OAUTH2_TRAININGACTION:READ')) {
                    $accessGranted = $this->accessService->hasTrainingActionView($user, $trainingAction);
                }
                break;
            default:
                throw new LogicException("TrainingAction Voter can only respond on \"view\" attributes.");
        }

        return $accessGranted;
    }
}
