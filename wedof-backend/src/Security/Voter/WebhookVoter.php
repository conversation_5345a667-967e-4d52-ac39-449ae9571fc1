<?php
// src/Security/Voter/WebhookVoter.php
namespace App\Security\Voter;

use App\Entity\User;
use App\Entity\Webhook;
use App\Service\AccessService;
use LogicException;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;
use Symfony\Component\Security\Core\Security;

class WebhookVoter extends Voter
{
    const VIEW = 'view';
    const EDIT = 'edit';

    private Security $security;
    private AccessService $accessService;

    public function __construct(Security $security, AccessService $accessService)
    {
        $this->accessService = $accessService;
        $this->security = $security;
    }

    protected function supports($attribute, $subject): bool
    {
        return $subject instanceof Webhook;
    }

    /**
     * @param $attribute
     * @param mixed $subject
     * @param TokenInterface $token
     * @return bool
     */
    protected function voteOnAttribute($attribute, $subject, TokenInterface $token): bool
    {
        if ($this->security->isGranted('ROLE_ADMIN')) {
            return true;
        }

        $accessGranted = false;
        /** @var $user User */
        $user = $token->getUser();

        /** @var $webhook Webhook */
        $webhook = $subject;

        switch ($attribute) {
            case self::VIEW:
                if ($this->security->isGranted('ROLE_USER') || $this->security->isGranted('ROLE_OAUTH2_WEBHOOK:READ')) {
                    $accessGranted = $this->accessService->hasWebhookView($user, $webhook);
                }
                break;
            case self::EDIT:
                if ($this->security->isGranted('ROLE_USER') || $this->security->isGranted('ROLE_OAUTH2_WEBHOOK:WRITE')) {
                    $accessGranted = $this->accessService->hasWebhookEdit($user, $webhook);
                }
                break;
            default:
                throw new LogicException("Webhook Voter can only respond on \"view\", \"edit\" and \"create\" attributes.");
        }
        return $accessGranted;
    }
}
