<?php
// src/Security/Voter/UserVoter.php
namespace App\Security\Voter;

use App\Entity\User;
use App\Service\AccessService;
use LogicException;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;
use Symfony\Component\Security\Core\Security;

class UserVoter extends Voter
{
    const VIEW = 'view';
    const EDIT = 'edit';

    private Security $security;
    private AccessService $accessService;

    public function __construct(AccessService $accessService, Security $security)
    {
        $this->accessService = $accessService;
        $this->security = $security;
    }

    /**
     * @inheritDoc
     */
    protected function supports($attribute, $subject): bool
    {
        return $subject instanceof User;
    }

    /**
     * @inheritDoc
     */
    protected function voteOnAttribute($attribute, $subject, TokenInterface $token): bool
    {
        if ($this->security->isGranted('ROLE_ADMIN')) {
            return true;
        }

        $accessGranted = false;
        /** @var $subjectUser User */
        $subjectUser = $subject;
        /** @var $user User */
        $user = $token->getUser();

        switch ($attribute) {
            case "ROLE_ALLOWED_TO_SWITCH":
                $accessGranted = $this->security->isGranted('ROLE_ALLOWED_TO_SWITCH');
                break;
            case self::VIEW:
                if ($this->security->isGranted('ROLE_USER') || $this->security->isGranted('ROLE_SALES') || $this->security->isGranted('ROLE_OAUTH2_USER:READ')) {
                    $accessGranted = $this->accessService->hasUserView($user, $subjectUser);
                }
                break;
            case self::EDIT:
                if ($this->security->isGranted('ROLE_USER') || $this->security->isGranted('ROLE_OAUTH2_USER:WRITE')) {
                    $accessGranted = $this->accessService->hasUserEdit($user, $subjectUser);
                }
                break;
            default:
                throw new LogicException("User Voter can only respond on \"view\" and \"edit\" attributes.");
        }
        return $accessGranted;
    }
}
