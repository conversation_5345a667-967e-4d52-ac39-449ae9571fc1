<?php
// src/Security/Voter/SkillVoter.php
namespace App\Security\Voter;

use App\Entity\Skill;
use App\Entity\User;
use App\Service\AccessService;
use LogicException;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;
use Symfony\Component\Security\Core\Security;

class SkillVoter extends Voter
{
    const VIEW = 'view';
    const EDIT = 'edit';

    private Security $security;
    private AccessService $accessService;

    public function __construct(Security $security, AccessService $accessService)
    {
        $this->security = $security;
        $this->accessService = $accessService;
    }

    protected function supports($attribute, $subject): bool
    {
        return $subject instanceof Skill;
    }

    /**
     * @param $attribute
     * @param mixed $subject
     * @param TokenInterface $token
     * @return bool
     */
    protected function voteOnAttribute($attribute, $subject, TokenInterface $token): bool
    {
        if ($this->security->isGranted('ROLE_ADMIN')) {
            return true;
        }

        $accessGranted = false;
        /** @var $skill Skill */
        $skill = $subject;

        switch ($attribute) {
            case self::VIEW:
                /** @var $user User */
                $user = $token->getUser();
                if ($this->security->isGranted('ROLE_USER')) {
                    $accessGranted = $this->accessService->hasSkillView($user, $skill);
                }
                break;
            case self::EDIT:
                /** @var $user User */
                $user = $token->getUser();
                if ($this->security->isGranted('ROLE_USER')) {
                    $accessGranted = $this->accessService->hasSkillEdit($user, $skill);
                }
                break;
            default:
                throw new LogicException("Skill Voter can only respond on \"view\" or \"edit\" attribute.");
        }

        return $accessGranted;
    }
}
