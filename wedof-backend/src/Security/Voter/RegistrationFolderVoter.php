<?php
// src/Security/Voter/RegistrationFolderVoter.php
namespace App\Security\Voter;

use App\Entity\Attendee;
use App\Entity\RegistrationFolder;
use App\Entity\User;
use App\Service\AccessService;
use Doctrine\ORM\NonUniqueResultException;
use LogicException;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;
use Symfony\Component\Security\Core\Security;

class RegistrationFolderVoter extends Voter
{
    const VIEW = 'view';
    const EDIT = 'edit';
    const ATTENDEE_VIEW = 'attendeeView';
    const ATTENDEE_EDIT = 'attendeeEdit';
    const OWNER_VIEW = 'ownerView';

    private Security $security;
    private AccessService $accessService;

    public function __construct(AccessService $accessService, Security $security)
    {
        $this->accessService = $accessService;
        $this->security = $security;
    }

    protected function supports($attribute, $subject): bool
    {
        return $subject instanceof RegistrationFolder;
    }

    /**
     * @param $attribute
     * @param mixed $subject
     * @param TokenInterface $token
     * @return bool
     * @throws NonUniqueResultException
     * @throws TransportExceptionInterface
     */
    protected function voteOnAttribute($attribute, $subject, TokenInterface $token): bool
    {
        if ($this->security->isGranted('ROLE_ADMIN')) {
            return true;
        }

        $accessGranted = false;

        /** @var $registrationFolder RegistrationFolder */
        $registrationFolder = $subject;

        switch ($attribute) {
            case self::VIEW:
                /** @var $user User */
                $user = $token->getUser();
                if (($this->security->isGranted('ROLE_USER') || $this->security->isGranted('ROLE_OAUTH2_REGISTRATIONFOLDER:READ'))) {
                    $accessGranted = $this->accessService->hasRegistrationFolderView($user, $registrationFolder);
                }
                break;
            case self::EDIT:
                /** @var $user User */
                $user = $token->getUser();
                if ($this->security->isGranted('ROLE_USER') || $this->security->isGranted('ROLE_OAUTH2_REGISTRATIONFOLDER:WRITE')) {
                    $accessGranted = $this->accessService->hasRegistrationFolderEdit($user, $registrationFolder);
                }
                break;
            case self::OWNER_VIEW:
                /** @var $user User */
                $user = $token->getUser();
                if (($this->security->isGranted('ROLE_USER') || $this->security->isGranted('ROLE_OAUTH2_REGISTRATIONFOLDER:READ'))) {
                    $accessGranted = $this->accessService->hasRegistrationFolderOwnerView($user, $registrationFolder);
                }
                break;
            case self::ATTENDEE_VIEW:
                /** @var $user Attendee */
                $user = $token->getUser();
                if ($this->security->isGranted('ROLE_ATTENDEE')) {
                    $accessGranted = $this->accessService->hasRegistrationFolderAttendeeView($user, $registrationFolder);
                }
                break;
            case self::ATTENDEE_EDIT:
                /** @var $user Attendee */
                $user = $token->getUser();
                if ($this->security->isGranted('ROLE_ATTENDEE')) {
                    $accessGranted = $this->accessService->hasRegistrationFolderAttendeeEdit($user, $registrationFolder);
                }
                break;

            default:
                throw new LogicException("RegistrationFolder Voter can only respond on \"view\", \"ownerView\", \"edit\" and \"attendeeView\" attributes.");
        }
        return $accessGranted;
    }
}
