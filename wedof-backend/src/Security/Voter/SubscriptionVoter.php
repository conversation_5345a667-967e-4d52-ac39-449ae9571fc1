<?php

namespace App\Security\Voter;

use App\Entity\Subscription;
use App\Entity\User;
use App\Service\AccessService;
use LogicException;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;
use Symfony\Component\Security\Core\Security;

class SubscriptionVoter extends Voter
{
    public const EDIT = 'edit';
    public const VIEW = 'view';
    private Security $security;
    private AccessService $accessService;

    public function __construct(Security $security, AccessService $accessService)
    {
        $this->security = $security;
        $this->accessService = $accessService;
    }

    protected function supports(string $attribute, $subject): bool
    {
        return $subject instanceof Subscription;
    }

    protected function voteOnAttribute(string $attribute, $subject, TokenInterface $token): bool
    {
        if ($this->security->isGranted('ROLE_ADMIN')) {
            return true;
        }

        $accessGranted = false;
        /** @var $user User */
        $user = $token->getUser();

        /** @var Subscription $subscription */
        $subscription = $subject;

        // ... (check conditions and return true to grant permission) ...
        switch ($attribute) {
            case self::EDIT:
                if ($this->security->isGranted('ROLE_USER') || $this->security->isGranted('ROLE_OAUTH2_SUBSCRIPTION:WRITE')) {
                    $accessGranted = $this->accessService->hasSubscriptionEdit($user, $subscription);
                }
                break;
            case self::VIEW:
                if ($this->security->isGranted('ROLE_USER') || $this->security->isGranted('ROLE_OAUTH2_SUBSCRIPTION:READ')) {
                    $accessGranted = $this->accessService->hasSubscriptionView($user, $subscription);
                }
                break;
            default:
                throw new LogicException("Subscription Voter can only respond on \"view\" and \"edit\" attribute.");
        }

        return $accessGranted;
    }
}
