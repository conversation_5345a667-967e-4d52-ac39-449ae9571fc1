<?php
// src/Security/Voter/RegistrationFolderVoter.php
namespace App\Security\Voter;

use App\Entity\Proposal;
use App\Entity\User;
use App\Service\AccessService;
use LogicException;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;
use Symfony\Component\Security\Core\Security;

class ProposalVoter extends Voter
{
    const VIEW = 'view';
    const EDIT = 'edit';

    private Security $security;
    private AccessService $accessService;

    public function __construct(Security $security, AccessService $accessService)
    {
        $this->security = $security;
        $this->accessService = $accessService;
    }

    protected function supports($attribute, $subject): bool
    {
        return $subject instanceof Proposal;
    }

    /**
     * @param $attribute
     * @param mixed $subject
     * @param TokenInterface $token
     * @return bool
     */
    protected function voteOnAttribute($attribute, $subject, TokenInterface $token): bool
    {
        if ($this->security->isGranted('ROLE_ADMIN')) {
            return true;
        }

        $accessGranted = false;
        /** @var $user User */
        $user = $token->getUser();


        /** @var $proposal Proposal */
        $proposal = $subject;
        $isAllowProposals = $user->getMainOrganism()->getSubscription()->isAllowProposals();
        switch ($attribute) {
            case self::VIEW:
                if ($isAllowProposals && ($this->security->isGranted('ROLE_USER') || $this->security->isGranted('ROLE_OAUTH2_PROPOSAL:READ'))) {
                    $accessGranted = $this->accessService->hasProposalView($user, $proposal);
                } else if ($isAllowProposals && ($this->security->isGranted('ROLE_SALES'))) {
                    $accessGranted = $this->accessService->hasProposalViewForSales($user, $proposal);
                }
                break;
            case self::EDIT:
                if ($isAllowProposals && ($this->security->isGranted('ROLE_USER') || $this->security->isGranted('ROLE_OAUTH2_PROPOSAL:WRITE'))) {
                    $accessGranted = $this->accessService->hasProposalEdit($user, $proposal);
                } else if ($isAllowProposals && ($this->security->isGranted('ROLE_SALES'))) {
                    $accessGranted = $this->accessService->hasProposalEditForSales($user, $proposal);
                }
                break;
            default:
                throw new LogicException("Proposal Voter can only respond on \"view\", \"edit\" attributes.");
        }
        return $accessGranted;
    }
}
