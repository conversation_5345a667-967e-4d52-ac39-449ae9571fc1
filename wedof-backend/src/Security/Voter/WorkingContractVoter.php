<?php
// src/Security/Voter/WorkingContractVoter.php
namespace App\Security\Voter;

use App\Entity\User;
use App\Entity\WorkingContract;
use App\Service\AccessService;
use LogicException;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;
use Symfony\Component\Security\Core\Security;

class WorkingContractVoter extends Voter
{
    const VIEW = 'view';

    private Security $security;
    private AccessService $accessService;

    public function __construct(Security $security, AccessService $accessService)
    {
        $this->security = $security;
        $this->accessService = $accessService;
    }

    protected function supports($attribute, $subject): bool
    {
        return $subject instanceof WorkingContract;
    }

    /**
     * @param $attribute
     * @param mixed $subject
     * @param TokenInterface $token
     * @return bool
     */
    protected function voteOnAttribute($attribute, $subject, TokenInterface $token): bool
    {
        if ($this->security->isGranted('ROLE_ADMIN')) {
            return true;
        }

        $accessGranted = false;
        /** @var $workingContract WorkingContract */
        $workingContract = $subject;

        switch ($attribute) {
            case self::VIEW:
                /** @var $user User */
                $user = $token->getUser();
                if ($this->security->isGranted('ROLE_USER')) {
                    $accessGranted = $this->accessService->hasWorkingContractView($user, $workingContract);
                }
                break;
            default:
                throw new LogicException("WorkingContract Voter can only respond on \"view\" attribute.");
        }

        return $accessGranted;
    }
}
