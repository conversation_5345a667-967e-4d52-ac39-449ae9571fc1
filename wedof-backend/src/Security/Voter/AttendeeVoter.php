<?php
// src/Security/Voter/AttendeeVoter.php
namespace App\Security\Voter;

use App\Entity\Attendee;
use App\Entity\User;
use App\Service\AccessService;
use LogicException;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;
use Symfony\Component\Security\Core\Security;

class AttendeeVoter extends Voter
{
    const VIEW = 'view';
    const EDIT = 'edit';

    private Security $security;
    private AccessService $accessService;

    public function __construct(Security $security, AccessService $accessService)
    {
        $this->security = $security;
        $this->accessService = $accessService;
    }

    protected function supports($attribute, $subject): bool
    {
        return $subject instanceof Attendee;
    }

    /**
     * @param string $attribute
     * @param mixed $subject
     * @param TokenInterface $token
     * @return bool
     */
    protected function voteOnAttribute(string $attribute, $subject, TokenInterface $token): bool
    {
        if ($this->security->isGranted('ROLE_ADMIN')) {
            return true;
        }

        $accessGranted = false;
        /** @var $user User */
        $user = $token->getUser();

        /** @var Attendee $attendee */
        $attendee = $subject;

        switch ($attribute) {
            case self::VIEW:
                 if ($this->security->isGranted('ROLE_USER') || $this->security->isGranted('ROLE_OAUTH2_ATTENDEE:READ')) {
                    $accessGranted = $this->accessService->hasAttendeeView($user, $attendee);
                }
                break;
            case self::EDIT:
                if ($this->security->isGranted('ROLE_USER') || $this->security->isGranted('ROLE_OAUTH2_ATTENDEE:WRITE')) {
                    $accessGranted = $this->accessService->hasAttendeeEdit($user, $attendee);
                }
                break;
            default:
                throw new LogicException("Attendee Voter can only respond on \"view\" and \"edit\" attributes.");
        }
        return $accessGranted;
    }
}
