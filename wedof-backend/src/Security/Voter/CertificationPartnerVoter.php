<?php

namespace App\Security\Voter;

use App\Entity\CertificationPartner;
use App\Entity\User;
use LogicException;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;
use Symfony\Component\Security\Core\Security;

class CertificationPartnerVoter extends Voter
{
    const VIEW = 'view';
    const EDIT = 'edit';
    const CERTIFIER_EDIT = 'certifierEdit';

    private Security $security;

    public function __construct(Security $security)
    {
        $this->security = $security;
    }

    protected function supports(string $attribute, $subject): bool
    {
        return $subject instanceof CertificationPartner;
    }

    protected function voteOnAttribute(string $attribute, $subject, TokenInterface $token): bool
    {
        if ($this->security->isGranted('ROLE_ADMIN')) {
            return true;
        }

        $accessGranted = false;

        /** @var $user User */
        $user = $token->getUser();

        /** @var $certificationPartner CertificationPartner */
        $certificationPartner = $subject;

        $certification = $certificationPartner->getCertification();
        $currentOrganism = $user->getMainOrganism();

        switch ($attribute) {
            case self::VIEW:
            case self::EDIT:
                if ($this->security->isGranted('ROLE_USER') || $this->security->isGranted('ROLE_OAUTH2_CERTIFICATION:READ')) {
                    $accessGranted = $certificationPartner->getCertifier() === $currentOrganism || (!$certificationPartner->getCertifier() && $certification->isCertifier($currentOrganism)) || $certificationPartner->getPartner() === $currentOrganism;
                }
                break;
            case self::CERTIFIER_EDIT:
                if ($this->security->isGranted('ROLE_USER') || $this->security->isGranted('ROLE_OAUTH2_CERTIFICATION:READ')) {
                    $accessGranted = ($certificationPartner->getCertifier() === $currentOrganism || (!$certificationPartner->getCertifier() && $certification->isCertifier($currentOrganism))) && $currentOrganism->getSubscription() && $currentOrganism->getSubscription()->isAllowCertifierPlus();
                }
                break;
            default:
                throw new LogicException("CertificationPartner Voter can only respond on \"view\", \"edit\" and \"certifierEdit\" attribute.");
        }

        return $accessGranted;
    }
}
