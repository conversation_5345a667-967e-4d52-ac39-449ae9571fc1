<?php
// src/Security/Voter/SessionVoter.php
namespace App\Security\Voter;

use App\Entity\Attendee;
use App\Entity\Session;
use App\Entity\User;
use App\Service\AccessService;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use LogicException;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;
use Symfony\Component\Security\Core\Security;

class SessionVoter extends Voter
{
    const VIEW = 'view';
    const CERTIFIER_VIEW = 'certifierView';
    const ATTENDEE_VIEW = 'attendeeView';

    private Security $security;
    private AccessService $accessService;

    public function __construct(AccessService $accessService, Security $security)
    {
        $this->accessService = $accessService;
        $this->security = $security;
    }

    protected function supports($attribute, $subject): bool
    {
        return $subject instanceof Session;
    }

    /**
     * @param $attribute
     * @param mixed $subject
     * @param TokenInterface $token
     * @return bool
     * @throws NonUniqueResultException
     * @throws NoResultException
     */
    protected function voteOnAttribute($attribute, $subject, TokenInterface $token): bool
    {
        if ($this->security->isGranted('ROLE_ADMIN')) {
            return true;
        }

        $accessGranted = false;
        /** @var $session Session */
        $session = $subject;

        switch ($attribute) {
            case self::VIEW:
                /** @var $user User */
                $user = $token->getUser();
                if ($this->security->isGranted('ROLE_USER') || $this->security->isGranted('ROLE_OAUTH2_SESSION:READ')) {
                    $accessGranted = $this->accessService->hasSessionView($user, $session);
                }
                break;
            case self::CERTIFIER_VIEW:
                /** @var $user User */
                $user = $token->getUser();
                if (($this->security->isGranted('ROLE_USER') || $this->security->isGranted('ROLE_OAUTH2_SESSION:READ')) && $user->getMainOrganism()->getSubscription()->isAllowCertifiers() ) {
                    $accessGranted = $this->accessService->hasSessionCertifierView($user, $session);
                }
                break;
            case self::ATTENDEE_VIEW:
                /** @var $user Attendee */
                $user = $token->getUser();
                if ($this->security->isGranted('ROLE_ATTENDEE')) {
                    $accessGranted = $this->accessService->hasSessionAttendeeView($user, $session);
                }
                break;

            default:
                throw new LogicException("Session Voter can only respond on \"view\", \"attendeeView\", \"edit\"  attribute.");
        }

        return $accessGranted;
    }
}
