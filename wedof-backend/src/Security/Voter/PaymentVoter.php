<?php
// src/Security/Voter/PaymentVoter.php
namespace App\Security\Voter;

use App\Entity\Payment;
use App\Entity\User;
use App\Service\AccessService;
use LogicException;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;
use Symfony\Component\Security\Core\Security;

class PaymentVoter extends Voter
{
    const VIEW = 'view';

    private Security $security;
    private AccessService $accessService;

    public function __construct(AccessService $accessService, Security $security)
    {
        $this->security = $security;
        $this->accessService = $accessService;
    }

    protected function supports($attribute, $subject): bool
    {
        return $subject instanceof Payment;
    }

    /**
     * @param $attribute
     * @param mixed $subject
     * @param TokenInterface $token
     * @return bool
     */
    protected function voteOnAttribute($attribute, $subject, TokenInterface $token): bool
    {
        if ($this->security->isGranted('ROLE_ADMIN')) {
            return true;
        }

        $accessGranted = false;
        /** @var $user User */
        $user = $token->getUser();

        /** @var $payment Payment */
        $payment = $subject;

        switch ($attribute) {
            case self::VIEW:
                if ($user->getMainOrganism()->getSubscription()->isAllowRegistrationFolderReads() && ($this->security->isGranted('ROLE_USER') || $this->security->isGranted('ROLE_OAUTH2_PAYMENT:READ'))) {
                    $accessGranted = $this->accessService->hasPaymentAccess($user, $payment);
                }
                break;
            default:
                throw new LogicException("Payment Voter can only respond on \"view\" attribute.");
        }

        return $accessGranted;
    }
}
