<?php
// src/Security/Voter/ActivityVoter.php
namespace App\Security\Voter;

use App\Entity\Activity;
use App\Entity\User;
use App\Service\AccessService;
use LogicException;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;
use Symfony\Component\Security\Core\Security;

class ActivityVoter extends Voter
{
    const EDIT = 'edit';

    private Security $security;
    private AccessService $accessService;

    public function __construct(Security $security, AccessService $accessService)
    {
        $this->security = $security;
        $this->accessService = $accessService;
    }

    protected function supports($attribute, $subject): bool
    {
        return $subject instanceof Activity;
    }

    /**
     * @param string $attribute
     * @param mixed $subject
     * @param TokenInterface $token
     * @return bool
     */
    protected function voteOnAttribute(string $attribute, $subject, TokenInterface $token): bool
    {
        if ($this->security->isGranted('ROLE_ADMIN')) {
            return true;
        }

        $accessGranted = false;
        /** @var $user User */
        $user = $token->getUser();

        /** @var Activity $activity */
        $activity = $subject;

        switch ($attribute) {
            case self::EDIT:
                if ($this->security->isGranted('ROLE_USER') || $this->security->isGranted('ROLE_OAUTH2_ACTIVITY:WRITE')) {
                    $accessGranted = $this->accessService->hasActivityEdit($user, $activity);
                }
                break;
            default:
                throw new LogicException("Activity Voter can only respond on \"edit\" attribute.");
        }
        return $accessGranted;
    }
}
