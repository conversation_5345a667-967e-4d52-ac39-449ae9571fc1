<?php
// src/Security/Voter/ApiTokenVoter.php
namespace App\Security\Voter;

use App\Entity\ApiToken;
use App\Entity\User;
use App\Service\AccessService;
use LogicException;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;
use Symfony\Component\Security\Core\Security;

class ApiTokenVoter extends Voter
{
    const EDIT = 'edit';

    private Security $security;
    private AccessService $accessService;

    public function __construct(Security $security, AccessService $accessService)
    {
        $this->accessService = $accessService;
        $this->security = $security;
    }

    protected function supports($attribute, $subject): bool
    {
        return $subject instanceof ApiToken;
    }

    /**
     * @param $attribute
     * @param mixed $subject
     * @param TokenInterface $token
     * @return bool
     */
    protected function voteOnAttribute($attribute, $subject, TokenInterface $token): bool
    {
        if ($this->security->isGranted('ROLE_ADMIN')) {
            return true;
        }

        $accessGranted = false;
        /** @var $user User */
        $user = $token->getUser();

        /** @var $apiToken ApiToken */
        $apiToken = $subject;

        switch ($attribute) {
            case self::EDIT:
                if ($this->security->isGranted('ROLE_USER') || $this->security->isGranted('ROLE_OAUTH2_APITOKEN:WRITE')) {
                    $accessGranted = $this->accessService->hasApiTokenEdit($user, $apiToken);
                }
                break;
           default:
                throw new LogicException("ApiToken Voter can only respond on \"edit\"  attributes.");
        }
        return $accessGranted;
    }
}
