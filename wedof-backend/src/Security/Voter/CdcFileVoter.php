<?php

namespace App\Security\Voter;

use App\Entity\CdcFile;
use App\Entity\User;
use LogicException;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;
use Symfony\Component\Security\Core\Security;

class CdcFileVoter extends Voter
{
    const VIEW = 'view';
    const EDIT = 'edit';

    private Security $security;

    public function __construct(Security $security)
    {
        $this->security = $security;
    }

    protected function supports(string $attribute, $subject): bool
    {
        return $subject instanceof CdcFile;
    }

    protected function voteOnAttribute(string $attribute, $subject, TokenInterface $token): bool
    {
        if ($this->security->isGranted('ROLE_ADMIN')) {
            return true;
        }

        $accessGranted = false;

        /** @var $user User */
        $user = $token->getUser();
        /** @var $cdcFile CdcFile */
        $cdcFile = $subject;

        $cdcFileOrganism = $cdcFile->getOrganism();
        $currentOrganism = $user->getMainOrganism();

        switch ($attribute) {
            case self::VIEW:
            case self::EDIT:
                if ($this->security->isGranted('ROLE_USER') || $this->security->isGranted('ROLE_OAUTH2_CERTIFICATION:WRITE')) {
                    $accessGranted = $currentOrganism->getSiret() === $cdcFileOrganism->getSiret();
                }
                break;
            default:
                throw new LogicException("CdcFileVoter Voter can only respond on \"view\" and \"edit\".");
        }

        return $accessGranted;
    }
}
