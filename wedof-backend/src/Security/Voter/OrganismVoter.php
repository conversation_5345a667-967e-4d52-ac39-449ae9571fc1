<?php
// src/Security/Voter/OrganismVoter.php
namespace App\Security\Voter;

use App\Entity\Attendee;
use App\Entity\Organism;
use App\Entity\User;
use App\Service\AccessService;
use Exception;
use LogicException;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;
use Symfony\Component\Security\Core\Security;

class OrganismVoter extends Voter
{
    const VIEW = 'view';
    const CERTIFICATION_VIEW = 'certificationView'; // vérification que l'organisme de l'utilisateur courant est certificateur d'une des certifications qu'utilise l'organisme auquel on veut accéder - pas de demande requise
    const EDIT = 'edit';
    const ATTENDEE_VIEW = 'attendeeView';

    private Security $security;
    private AccessService $accessService;

    public function __construct(AccessService $accessService, Security $security)
    {
        $this->accessService = $accessService;
        $this->security = $security;
    }

    protected function supports($attribute, $subject): bool
    {
        return $subject instanceof Organism;
    }

    /**
     * @param $attribute
     * @param mixed $subject
     * @param TokenInterface $token
     * @return bool
     * @throws Exception
     */
    protected function voteOnAttribute($attribute, $subject, TokenInterface $token): bool
    {
        if ($this->security->isGranted('ROLE_ADMIN')) {
            return true;
        }

        $accessGranted = false;
        /** @var Organism $organism */
        $organism = $subject;

        switch ($attribute) {
            case self::VIEW:
                /** @var $user User */
                $user = $token->getUser();
                if ($this->security->isGranted('ROLE_USER') || $this->security->isGranted('ROLE_SALES') || $this->security->isGranted('ROLE_OAUTH2_ORGANISM:READ')) {
                    $accessGranted = $this->accessService->hasOrganismView($user, $organism);
                }
                break;
            case self::EDIT:
                /** @var $user User */
                $user = $token->getUser();
                if ($this->security->isGranted('ROLE_USER') || $this->security->isGranted('ROLE_OAUTH2_ORGANISM:WRITE')) {
                    $accessGranted = $this->accessService->hasOrganismEdit($user, $organism);
                }
                break;
            case self::CERTIFICATION_VIEW:
                /** @var $user User */
                $user = $token->getUser();
                if (($this->security->isGranted('ROLE_USER') || $this->security->isGranted('ROLE_OAUTH2_ORGANISM:READ')) && $user->getMainOrganism()->getSubscription()->isAllowCertifiers()) {
                    $accessGranted = $this->accessService->hasOrganismCertificationView($user, $organism);
                }
                break;
            case self::ATTENDEE_VIEW:
                /** @var $user Attendee */
                $user = $token->getUser();
                if ($this->security->isGranted('ROLE_ATTENDEE')) {
                    $accessGranted = $this->accessService->hasOrganismAttendeeView($user, $organism);
                }
                break;
            default:
                throw new LogicException("Organism Voter can only respond on \"view\", \"certificationView\", \"attendeeView\" and \"edit\" attributes.");
        }
        return $accessGranted;
    }
}
