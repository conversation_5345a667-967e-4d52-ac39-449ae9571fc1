<?php

namespace App\Security\Voter;

use App\Entity\Evaluation;
use App\Entity\User;
use App\Service\AccessService;
use LogicException;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;
use Symfony\Component\Security\Core\Security;

class EvaluationVoter extends Voter
{
    const VIEW = 'view';

    private Security $security;
    private AccessService $accessService;

    public function __construct(AccessService $accessService, Security $security)
    {
        $this->accessService = $accessService;
        $this->security = $security;
    }


    protected function supports($attribute, $subject): bool
    {
        return $subject instanceof Evaluation;
    }

    protected function voteOnAttribute($attribute, $subject, TokenInterface $token): bool
    {
        if ($this->security->isGranted('ROLE_ADMIN')) {
            return true;
        }

        $accessGranted = false;
        /** @var $user User */
        $user = $token->getUser();

        if ($user->getMainOrganism()->getSubscription()->isAllowAnalytics()) {
            /** @var $evaluation Evaluation */
            $evaluation = $subject;

            switch ($attribute) {
                case self::VIEW:
                    if ($this->security->isGranted('ROLE_USER') || $this->security->isGranted('ROLE_OAUTH2_EVALUATION:READ')) {
                        $accessGranted = $this->accessService->hasEvaluationView($user, $evaluation);
                    }
                    break;
                default:
                    throw new LogicException("Evaluation Voter can only respond on \"view\" attribute.");
            }
        }
        return $accessGranted;
    }
}
