<?php
// src/Security/Voter/CertificationFolderSurveyVoter.php
namespace App\Security\Voter;

use App\Entity\Attendee;
use App\Entity\CertificationFolderSurvey;
use App\Entity\User;
use App\Service\AccessService;
use LogicException;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;
use Symfony\Component\Security\Core\Security;

class CertificationFolderSurveyVoter extends Voter
{
    const VIEW = 'view';
    const EDIT = 'edit';
    const ATTENDEE_VIEW = 'attendeeView';
    const ATTENDEE_EDIT = 'attendeeEdit';

    private Security $security;
    private AccessService $accessService;

    public function __construct(AccessService $accessService, Security $security)
    {
        $this->accessService = $accessService;
        $this->security = $security;
    }

    protected function supports($attribute, $subject): bool
    {
        return $subject instanceof CertificationFolderSurvey;
    }

    /**
     * @param $attribute
     * @param mixed $subject
     * @param TokenInterface $token
     * @return bool
     */
    protected function voteOnAttribute($attribute, $subject, TokenInterface $token): bool
    {
        if ($this->security->isGranted('ROLE_ADMIN')) {
            return true;
        }

        $accessGranted = false;

        /** @var $certificationFolderSurvey CertificationFolderSurvey */
        $certificationFolderSurvey = $subject;
        $certificationFolder = $certificationFolderSurvey->getCertificationFolder();

        switch ($attribute) {
            case self::VIEW:
                /** @var $user User */
                $user = $token->getUser();
                if ($this->security->isGranted('ROLE_USER') || $this->security->isGranted('ROLE_OAUTH2_CERTIFICATIONFOLDER:READ')) {
                    $accessGranted = $this->accessService->hasCertificationFolderView($user, $certificationFolder);
                }
                break;
            case self::EDIT:
                /** @var $user User */
                $user = $token->getUser();
                if ($this->security->isGranted('ROLE_USER') || $this->security->isGranted('ROLE_OAUTH2_CERTIFICATIONFOLDER:WRITE')) {
                    $accessGranted = $this->accessService->hasCertificationFolderCertifierEdit($user, $certificationFolder) || $this->accessService->hasCertificationFolderPartnerEdit($user, $certificationFolder);
                }
                break;
            case self::ATTENDEE_VIEW:
                /** @var $user Attendee */
                $user = $token->getUser();
                if ($this->security->isGranted('ROLE_ATTENDEE')) {
                    $accessGranted = $this->accessService->hasCertificationFolderAttendeeView($user, $certificationFolder);
                }
                break;
            case self::ATTENDEE_EDIT:
                /** @var $user Attendee */
                $user = $token->getUser();
                if ($this->security->isGranted('ROLE_ATTENDEE')) {
                    $accessGranted = $this->accessService->hasCertificationFolderAttendeeEdit($user, $certificationFolder);
                }
                break;
            default:
                throw new LogicException("CertificationFolderSurvey Voter can only respond on \"view\", \"edit\" and \"attendeeView\" attributes.");
        }
        return $accessGranted;
    }
}
