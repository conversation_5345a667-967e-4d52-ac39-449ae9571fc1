<?php

namespace App\Security;

use League\Bundle\OAuth2ServerBundle\Event\AuthorizationRequestResolveEvent;
use League\Bundle\OAuth2ServerBundle\OAuth2Events;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Security\Core\Security;

final class AuthorizationCodeSubscriber implements EventSubscriberInterface
{
    private Security $security;
    private UrlGeneratorInterface $urlGenerator;
    private RequestStack $requestStack;

    public function __construct(Security $security, UrlGeneratorInterface $urlGenerator, RequestStack $requestStack)
    {
        $this->security = $security;
        $this->urlGenerator = $urlGenerator;
        $this->requestStack = $requestStack;
    }

    public function onAuthorizationRequestResolve(AuthorizationRequestResolveEvent $event)
    {
        if (null !== ($user = $this->security->getUser())) {
            $event->setUser($user);
            $event->resolveAuthorization(true);
        } else {
            $event->setResponse(new RedirectResponse($this->urlGenerator->generate('login', ['returnUrl' => $this->requestStack->getMainRequest()->getUri()])));
        }
    }

    public static function getSubscribedEvents(): array
    {
        return array(OAuth2Events::AUTHORIZATION_REQUEST_RESOLVE => 'onAuthorizationRequestResolve');
    }
}
