<?php

namespace App\Security;

use App\Entity\User;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\Tools;
use App\Service\RegistrationFolderService;
use App\Service\SubscriptionService;
use DateTime;
use DeviceDetector\Cache\PSR6Bridge;
use DeviceDetector\ClientHints;
use DeviceDetector\DeviceDetector;
use Exception;
use Symfony\Component\Cache\Adapter\ApcuAdapter;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\Event\ResponseEvent;
use Symfony\Component\HttpKernel\Exception\TooManyRequestsHttpException;
use Symfony\Component\RateLimiter\LimiterInterface;
use Symfony\Component\RateLimiter\RateLimit;
use Symfony\Component\RateLimiter\RateLimiterFactory;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Security\Core\Security;
use Symfony\Component\Security\Core\User\UserInterface;

class RateLimiterSubscriber implements EventSubscriberInterface
{
    private TokenStorageInterface $tokenStorage;
    private Security $security;

    private RegistrationFolderService $registrationFolderService;
    private SubscriptionService $subscriptionService;

    /** really used */
    private RateLimiterFactory $cpfApiLimiter;
    /** really used */
    private RateLimiterFactory $freeApiLimiter;
    /** really used */
    private RateLimiterFactory $freeAsyncApiLimiter;
    /** really used */
    private RateLimiterFactory $customerApiLimiter;
    /** really used */
    private RateLimiterFactory $customerAsyncApiLimiter;
    /** really used */
    private RateLimiterFactory $adminApiLimiter;
    private static string $RATE_LIMIT_STATE = 'RATE_LIMIT_STATE';

    public function __construct(
        TokenStorageInterface $tokenStorage,
        Security              $security,
        SubscriptionService   $subscriptionService,
        RegistrationFolderService $registrationFolderService,
        RateLimiterFactory    $cpfApiLimiter,
        RateLimiterFactory    $freeApiLimiter,
        RateLimiterFactory    $freeAsyncApiLimiter,
        RateLimiterFactory    $customerApiLimiter,
        RateLimiterFactory    $customerAsyncApiLimiter,
        RateLimiterFactory    $adminApiLimiter)
    {
        $this->tokenStorage = $tokenStorage;
        $this->security = $security;
        $this->cpfApiLimiter = $cpfApiLimiter;
        $this->freeApiLimiter = $freeApiLimiter;
        $this->freeAsyncApiLimiter = $freeAsyncApiLimiter;
        $this->customerApiLimiter = $customerApiLimiter;
        $this->customerAsyncApiLimiter = $customerAsyncApiLimiter;
        $this->adminApiLimiter = $adminApiLimiter;
        $this->subscriptionService = $subscriptionService;
        $this->registrationFolderService = $registrationFolderService;
    }

    /**
     * @return string[]
     */
    public static function getSubscribedEvents(): array
    {
        if ($_ENV['APP_ENV'] === 'dev') {
            return []; // Rate limit is very resource intensive so disable it in dev
        } else {
            return [
                RequestEvent::class => 'onKernelRequest',
                ResponseEvent::class => 'onKernelResponse'
            ];
        }
    }

    /**
     * @param RequestEvent $event
     * @throws Exception
     */
    public function onKernelRequest(RequestEvent $event): void
    {
        if ($event->isMainRequest()) {
            $rateLimiter = $this->getRateLimiterForRequest($event->getRequest());
            if ($rateLimiter) {
                $rateLimit = $rateLimiter->consume();
                $event->getRequest()->attributes->set(self::$RATE_LIMIT_STATE, $rateLimit);
                if (false === $rateLimit->isAccepted()) {
                    $headers = $this->getRateLimitHeaders($rateLimit);
                    throw new TooManyRequestsHttpException($headers['X-RateLimit-Reset'], "Too many requests, slow down", null, 429, $headers);
                }
            }
            $this->updateRequestCount($event->getRequest());
        }
    }

    /**
     * @param ResponseEvent $event
     * @throws Exception
     */
    public function onKernelResponse(ResponseEvent $event): void
    {
        if (!$event->isMainRequest() || !$event->getRequest()->attributes->get(self::$RATE_LIMIT_STATE)) {
            return;
        }
        $rateLimit = $event->getRequest()->attributes->get(self::$RATE_LIMIT_STATE);
        $response = $event->getResponse();
        $response->headers->add($this->getRateLimitHeaders($rateLimit));
    }

    /**
     * @param Request $request
     */
    private function updateRequestCount(Request $request): void
    {
        /** @var UserInterface $user */
        $user = $this->tokenStorage->getToken() ? $this->tokenStorage->getToken()->getUser() : null;
        if (!$this->security->isGranted('ROLE_ADMIN') && $user && ($user instanceof User) && !$this->isUsingWebApp($request)) {
            $organism = $user->getMainOrganism();
            if ($organism && $organism->getSubscription()) {
                $this->subscriptionService->updateRequestCount($organism->getSubscription());
            }
        }
    }

    /**
     * @param Request $request
     * @return LimiterInterface|null
     */
    private function getRateLimiterForRequest(Request $request): ?LimiterInterface
    {
        /** @var UserInterface $user */
        $user = $this->tokenStorage->getToken() ? $this->tokenStorage->getToken()->getUser() : null;
        /** @var RateLimiterFactory $rateLimiter */
        $rateLimiter = null;
        if ($this->security->isGranted('ROLE_ADMIN')) {
            $rateLimiter = $this->adminApiLimiter;
        } else if ($user && !$this->isUsingWebApp($request)) {
            $isAsyncEndpoint = Tools::contains($request->getPathInfo(), 'Async');
            $subscription = $user instanceof User ? $user->getMainOrganism()->getSubscription() : null;
            $isPaidTraining = $subscription ? $this->subscriptionService->isPaidCustomer($subscription, true, false) : false;
            $isPaidCertifier = $subscription ? $this->subscriptionService->isPaidCustomer($subscription, false, true) : false;
            // api specific training routes...
            if (Tools::startsWith($request->getPathInfo(), '/api/registrationFolders')) {
                $rateLimiterName = ($isPaidTraining ? 'customer' : 'free') . ($isAsyncEndpoint ? 'Async' : '') . 'ApiLimiter';
                //propagate the limit from EDOF to customer
                if ($isPaidTraining && !$isAsyncEndpoint && $request->getMethod() != Request::METHOD_GET && $request->get('externalId')) {
                    $registrationFolder = $this->registrationFolderService->getByExternalId($request->get('externalId'));
                    if ($registrationFolder && $registrationFolder->getType() === DataProviders::CPF()->getValue()) {
                        $rateLimiterName = strtolower(DataProviders::CPF()->getValue()) . 'ApiLimiter';
                    }
                }
                $rateLimiter = $this->{$rateLimiterName};
            } // api specific certifier routes... (shouldn't it also include certificationPartners, audit etc. ?)
            else if (Tools::startsWith($request->getPathInfo(), '/api/certificationFolders')) {
                $rateLimiterName = ($isPaidCertifier ? 'customer' : 'free') . ($isAsyncEndpoint ? 'Async' : '') . 'ApiLimiter';
                $rateLimiter = $this->{$rateLimiterName};
            } // other api routes... or app routes for hackers
            else if (Tools::startsWith($request->getPathInfo(), '/api') || Tools::startsWith($request->getPathInfo(), '/app')) {
                $rateLimiterName = (($isPaidTraining || $isPaidCertifier) ? 'customer' : 'free') . ($isAsyncEndpoint ? 'Async' : '') . 'ApiLimiter';
                $rateLimiter = $this->{$rateLimiterName};
            }
        }
        if (!$user || !$rateLimiter) {
            //return $rateLimiter->create($request->getClientIp());
            return null;
        } else {
            return $rateLimiter->create($user->getUserIdentifier());
        }
    }

    /**
     * @param RateLimit $rateLimit
     * @return array
     */
    private function getRateLimitHeaders(RateLimit $rateLimit): array
    {
        $resetIn = $rateLimit->getRetryAfter()->getTimestamp() - (new DateTime())->getTimestamp();
        return [
            //https://datatracker.ietf.org/doc/draft-ietf-httpapi-ratelimit-headers/
            'X-RateLimit-Limit' => $rateLimit->getLimit(),
            'X-RateLimit-Remaining' => $rateLimit->getRemainingTokens(),
            'X-RateLimit-Reset' => max($resetIn, 0),
            //extra header
            'X-RateLimit-Retry-At' => $rateLimit->getRetryAfter()->getTimestamp(),
        ];
    }

    /**
     * @param Request $request
     * @return bool
     */
    private function isUsingWebApp(Request $request): bool
    {
        $useApiKey = isset($_SERVER['X-API-KEY']) || $request->headers->has('X-API-KEY') || $request->query->has('X-API-KEY');
        if ($useApiKey) {
            return false;
        } //try to detect a web scraping call and threat them as api call
        else if (Tools::startsWith($request->getPathInfo(), '/api') || Tools::startsWith($request->getPathInfo(), '/app')) {
            $userAgent = $_SERVER['HTTP_USER_AGENT'];
            $clientHints = ClientHints::factory($_SERVER);
            $dd = new DeviceDetector($userAgent, $clientHints);
            if (ApcuAdapter::isSupported()) {
                $dd->setCache(new PSR6Bridge(new ApcuAdapter()));
            }
            $dd->discardBotInformation();
            $dd->parse();
            return $dd->isBrowser() && !$dd->isBot();
        } else {
            return true;
        }
    }
}