<?php

namespace App\Security;

use App\Entity\User;
use Symfony\Bundle\SecurityBundle\Security\FirewallMap;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Security\Core\Authentication\Token\UsernamePasswordToken;
use Symfony\Component\Security\Core\Authorization\AccessDecisionManagerInterface;

class SecurityExtended
{

    protected RequestStack $requestStack;
    private AccessDecisionManagerInterface $accessDecisionManager;
    private FirewallMap $firewallMap;

    public function __construct(
        AccessDecisionManagerInterface $accessDecisionManager,
        RequestStack                   $requestStack,
        FirewallMap                    $firewallMap)
    {
        $this->firewallMap = $firewallMap;
        $this->requestStack = $requestStack;
        $this->accessDecisionManager = $accessDecisionManager;
    }

    public function isGranted(User $user, $attribute, $subject = null): bool
    {
        $firewall = $this->firewallMap->getFirewallConfig($this->requestStack->getCurrentRequest());
        $token = new UsernamePasswordToken($user, $firewall->getName(), $user->getRoles());
        return ($this->accessDecisionManager->decide($token, [$attribute], $subject));
    }

}
