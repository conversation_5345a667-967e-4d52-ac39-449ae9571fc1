<?php

namespace App\Repository;

use App\Entity\Organism;
use App\Entity\Webhook;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method Webhook|null find($id, $lockMode = null, $lockVersion = null)
 * @method Webhook|null findOneBy(array $criteria, array $orderBy = null)
 * @method Webhook[]    findAll()
 * @method Webhook[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class WebhookRepository extends ServiceEntityRepository
{

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Webhook::class);
    }

    /**
     * @param Webhook $webhook
     * @return Webhook
     */
    public function save(Webhook $webhook): Webhook
    {
        if (!$this->_em->contains($webhook)) {
            $this->_em->persist($webhook);
        }
        $this->_em->flush();
        return $webhook;
    }

    /**
     * @param Webhook $webhook
     * @return void
     */
    public function delete(Webhook $webhook): void
    {
        $this->_em->remove($webhook);
        $this->_em->flush();
    }

    /**
     * @param Collection $organisms
     * @param string $eventName
     * @param array $options
     * @return ArrayCollection
     */
    public function findByOrganismsAndEventAndType(Collection $organisms, string $eventName, array $options): ArrayCollection
    {
        $ids = $organisms->count() > 0 ? $organisms->map(function (Organism $organism) {
            return $organism->getId();
        }) : new ArrayCollection();

        if ($ids->count() == 0) {
            return new ArrayCollection();
        }

        $qb = $this->createQueryBuilder('web')
            ->andWhere('web.events LIKE :event OR web.events LIKE :wildcard OR web.events LIKE :subwildcard')
            ->andWhere('web.type LIKE :type')
            ->setParameter('type', $options['type']);
        if ($options['onlyEnabled'] === true) {
            $qb->andWhere('web.enabled = 1');
        }
        $qb->innerJoin('web.organism', 'org')
            ->andWhere($qb->expr()->in('org.id', join(",", $ids->toArray())))
            ->setParameter('event', '%"' . $eventName . '"%')
            ->setParameter('wildcard', serialize(array('*')))
            ->setParameter('subwildcard', '%"' . explode('.', $eventName)[0] . '.*"%');

        return new ArrayCollection($qb->getQuery()->getResult());
    }

    /**
     * @return ArrayCollection
     */
    public function findAllForRegistrationFolderAlert(): ArrayCollection
    {
        $qb = $this->createQueryBuilder('web')
            ->andWhere("web.events LIKE '%registrationFolderAlert%' OR web.events LIKE :wildcard")
            ->andWhere("web.type LIKE :type")
            ->andWhere('web.enabled = 1')
            ->setParameter('type', 'webhook')
            ->setParameter('wildcard', serialize(array('*')));
        return new ArrayCollection($qb->getQuery()->getResult());
    }

    /**
     * @param Organism $organism
     * @param array $parameters
     * @return QueryBuilder
     */
    public function findAllReturnQueryBuilder(Organism $organism, array $parameters): QueryBuilder
    {
        $qb = $this->createQueryBuilder('web');

        $qb->where($qb->expr()->eq('web.organism', ':organism'))
            ->setParameter('organism', $organism);
        if (!empty($parameters['enabled'])) {
            if ($parameters['enabled'] == 'true') {
                $qb->andWhere('web.enabled = 1');
            } else {
                $qb->andWhere('web.enabled = 0');
            }
        }
        if (!empty($parameters['type'])) {
            $qb->andWhere($qb->expr()->eq('web.type', ':type'))
                ->setParameter('type', $parameters['type']);
            if ($parameters['type'] == 'webhook') { //TODO remove when zapier app
                $qb->andWhere("web.url NOT LIKE '%https://hooks.zapier.com%'");
            }
        }
        $qb->orderBy('web.updatedOn', $parameters['order']);
        $qb->addOrderBy('web.id', 'ASC');
        return $qb;
    }
}
