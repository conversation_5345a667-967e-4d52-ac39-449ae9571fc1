<?php

namespace App\Repository;

use App\Entity\Certification;
use App\Entity\CertificationPartnerFile;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<CertificationPartnerFile>
 *
 * @method CertificationPartnerFile|null find($id, $lockMode = null, $lockVersion = null)
 * @method CertificationPartnerFile|null findOneBy(array $criteria, array $orderBy = null)
 * @method CertificationPartnerFile[]    findAll()
 * @method CertificationPartnerFile[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class CertificationPartnerFileRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, CertificationPartnerFile::class);
    }

    /**
     * @param CertificationPartnerFile $certificationPartnerFile
     * @return CertificationPartnerFile
     */
    public function save(CertificationPartnerFile $certificationPartnerFile): CertificationPartnerFile
    {
        if (!$this->_em->contains($certificationPartnerFile)) {
            $this->_em->persist($certificationPartnerFile);
        }
        $this->_em->flush();
        return $certificationPartnerFile;
    }

    /**
     * @param CertificationPartnerFile $certificationPartnerFile
     */
    public function delete(CertificationPartnerFile $certificationPartnerFile): void
    {
        $this->_em->remove($certificationPartnerFile);
        $this->_em->flush();
    }

    /**
     * @param Certification $certification
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function findMaxTypeId(Certification $certification): int
    {
        $qb = $this->createQueryBuilder('file');
        $qb->select('MAX(file.typeId)')
            ->join('file.certificationPartner', 'certificationPartner')
            ->join('certificationPartner.certification', 'certification')
            ->andWhere($qb->expr()->eq('certification', ':certification'))
            ->setParameter('certification', $certification);
        return $qb->getQuery()->getSingleScalarResult() ?? 0;
    }

    /**
     * @param Certification|null $certification
     * @param int $typeId
     * @return float|int|mixed|string
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countFilesWithTypeId(?Certification $certification, int $typeId)
    {
        $qb = $this->createQueryBuilder('cpFile');
        $qb->select('count(cpFile.typeId)')
            ->join('cpFile.certificationPartner', 'certificationPartner')
            ->join('certificationPartner.certification', 'certification')
            ->andWhere($qb->expr()->eq('certification', ':certification'))
            ->andWhere($qb->expr()->eq('cpFile.typeId', ':typeId'))
            ->setParameter('certification', $certification)
            ->setParameter('typeId', $typeId);
        return $qb->getQuery()->getSingleScalarResult() ?? 0;
    }
}
