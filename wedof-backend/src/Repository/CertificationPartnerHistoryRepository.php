<?php

namespace App\Repository;

use App\Entity\CertificationPartnerHistory;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method CertificationPartnerHistory|null find($id, $lockMode = null, $lockVersion = null)
 * @method CertificationPartnerHistory|null findOneBy(array $criteria, array $orderBy = null)
 * @method CertificationPartnerHistory[]    findAll()
 * @method CertificationPartnerHistory[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class CertificationPartnerHistoryRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, CertificationPartnerHistory::class);
    }
}
