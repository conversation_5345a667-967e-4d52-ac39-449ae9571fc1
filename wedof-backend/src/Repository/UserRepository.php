<?php

namespace App\Repository;

use App\Controller\api\UserController;
use App\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Component\Security\Core\Exception\UnsupportedUserException;
use Symfony\Component\Security\Core\User\PasswordUpgraderInterface;
use Symfony\Component\Security\Core\User\UserInterface;


/**
 * @method User|null find($id, $lockMode = null, $lockVersion = null)
 * @method User|null findOneBy(array $criteria, array $orderBy = null)
 * @method User[]    findAll()
 * @method User[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class UserRepository extends ServiceEntityRepository implements PasswordUpgraderInterface
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, User::class);
    }

    /**
     * Used to upgrade (rehash) the user's password automatically over time.
     * @param UserInterface $user
     * @param string $newHashedPassword
     */
    public function upgradePassword(UserInterface $user, string $newHashedPassword): void
    {
        if (!$user instanceof User) {
            throw new UnsupportedUserException(sprintf('Instances of "%s" are not supported.', get_class($user)));
        }
        $user->setPassword($newHashedPassword);
        $this->_em->persist($user);
        $this->_em->flush();
    }

    /**
     * @param UserInterface $user
     * @return User
     */
    public function save(UserInterface $user): User
    {
        if (!$user instanceof User) {
            throw new UnsupportedUserException(sprintf('Instances of "%s" are not supported.', get_class($user)));
        }
        if (!$this->_em->contains($user) && empty($user->getRoles())) {
            $user->setRoles(array("ROLE_USER"));
        }
        $this->_em->persist($user);
        $this->_em->flush();
        return $user;
    }

    /**
     * @param UserInterface $user
     */
    public function delete(UserInterface $user): void
    {
        if (!$user instanceof User) {
            throw new UnsupportedUserException(sprintf('Instances of "%s" are not supported.', get_class($user)));
        }
        $this->_em->remove($user);
        $this->_em->flush();
    }

    // TODO faire la jointure avec Attendee pour récupérer les attendees lié à l'utilisateur passé en paramètre
    // Passage par une table intermédiaire gérée par doctrine user_attendee
    // Il doit exister des méthodes du querybuilder pour se simplifier la vie
    /*public function getAttendeesByUserQuery(UserInterface $user): QueryBuilder
    {
        $queryBuilder = $this->_em->createQueryBuilder();
        return $queryBuilder;
    }*/

    /**
     * @return ArrayCollection
     */
    public function findAllUsers(): ArrayCollection
    {
        return new ArrayCollection($this->findAll());
    }

    /**
     * @param string $siret
     * @return int|mixed|string|null
     * @throws NonUniqueResultException
     * méthode utilisée dans les annotations ici : @link UserController::showByOwnedOrganism()
     */
    public function findOneByOwnedOrganism(string $siret)
    {
        $qb = $this->createQueryBuilder('user');
        $qb->join('user.ownedOrganism', 'ownedOrganism')
            ->andWhere($qb->expr()->eq('ownedOrganism.siret', ':siret'))
            ->setParameter('siret', $siret);
        return $qb->getQuery()->getOneOrNullResult();
    }

    /**
     * @param array $parameters
     * @return QueryBuilder
     */
    public function findAllReturnQueryBuilder(array $parameters): QueryBuilder
    {
        $qb = $this->createQueryBuilder('user');

        if (isset($parameters['active'])) {
            $qb->andWhere($qb->expr()->eq('user.cpfLoginIssue', ':inactive'))
                ->setParameter('inactive', !$parameters['active']);
        }
        if (isset($parameters['owner'])) {
            $qb->andWhere($qb->expr()->eq('user.isOwner', ':onlyOwner'))
                ->setParameter('owner', true);
        }
        if (isset($parameters['ownedOrganism'])) {
            $qb->join('user.ownedOrganism', 'ownedOrganism')
                ->andWhere($qb->expr()->eq('ownedOrganism', ':ownedOrganism'))
                ->setParameter('ownedOrganism', $parameters['ownedOrganism']);
        }
        if (isset($parameters['organism'])) {
            $qb->join('user.mainOrganism', 'userOrganism')
                ->andWhere($qb->expr()->eq('userOrganism', ':userOrganism'))
                ->setParameter('userOrganism', $parameters['organism']);
        }
        if (!empty($parameters['createdSince'])) {
            $qb->andWhere($qb->expr()->gte('user.createdOn', ':createdSince'))
                ->setParameter('createdSince', $parameters['createdSince']);
        }
        if (!empty($parameters['createdUntil'])) {
            $qb->andWhere($qb->expr()->lte('user.createdOn', ':createdUntil'))
                ->setParameter('createdUntil', $parameters['createdUntil']);
        }

        if (isset($parameters['query'])) {
            $qb->join('user.mainOrganism', 'org')
                ->andWhere($qb->expr()->orX(
                    $qb->expr()->like('org.name', ':query'),
                    $qb->expr()->like('org.name_siret', ':query'),
                    $qb->expr()->like('org.siret', ':query'),
                    $qb->expr()->like('user.email', ':query'),
                    $qb->expr()->like('user.lastName', ':query'),
                    $qb->expr()->like('user.firstName', ':query')
                ))
                ->setParameter('query', '%' . trim($parameters['query']) . '%');
        }
        return $qb;
    }

    /**
     * @param string $siret
     * @return int
     * @throws NonUniqueResultException
     * @throws NoResultException
     */
    public function countBySiret(string $siret): int
    {
        $qb = $this->createQueryBuilder('user');
        $qb->select('count(user.id)')
            ->join('user.mainOrganism', 'mainOrg')
            ->andWhere($qb->expr()->eq('mainOrg.siret', ':siret'))
            ->setParameter('siret', $siret);
        return $qb->getQuery()->getSingleScalarResult();
    }

    /**
     * @param array $parameters
     * @return ArrayCollection
     */
    public function findAllWithParams(array $parameters): ArrayCollection
    {
        return new ArrayCollection($this->findAllReturnQueryBuilder($parameters)->getQuery()->getResult());
    }

    /**
     * @param string $email
     * @return User|null
     */
    public function findOneByEmail(string $email): ?User
    {
        return $this->findOneBy(['email' => $email]);
    }

    /**
     * @param int $id
     * @return User|null
     */
    public function findOneById(int $id): ?User
    {
        return $this->findOneBy(['id' => $id]);
    }

    /**
     * DON'T USE IT OR BE VERY CAREFUL WITH THAT THING!!!!
     * @return string
     * @throws NonUniqueResultException
     * @throws NoResultException
     */
    public function getAdminToken(): string
    {
        $qb = $this->createQueryBuilder('user');
        $qb->select('apiTokens.token')
            ->join('user.apiTokens', 'apiTokens')
            ->andWhere('JSON_CONTAINS(user.roles, :roleAdmin) = 1')
            ->setParameter('roleAdmin', '"ROLE_ADMIN"') // DOUBLE QUOTES required to make it JSON string value
            ->setMaxResults(1);
        return $qb->getQuery()->getSingleScalarResult();
    }
}
