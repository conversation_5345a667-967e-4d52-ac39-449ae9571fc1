<?php

namespace App\Repository;

use App\Entity\Invoice;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Invoice>
 *
 * @method Invoice|null find($id, $lockMode = null, $lockVersion = null)
 * @method Invoice|null findOneBy(array $criteria, array $orderBy = null)
 * @method Invoice[]    findAll()
 * @method Invoice[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class InvoiceRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Invoice::class);
    }

    /**
     * @param Invoice $invoice
     * @return Invoice
     */
    public function save(Invoice $invoice): Invoice
    {
        if (!$this->_em->contains($invoice)) {
            $this->_em->persist($invoice);
        }
        $this->_em->flush();
        return $invoice;
    }

    /**
     * @param Invoice $invoice
     */
    public function delete(Invoice $invoice): void
    {
        $this->_em->remove($invoice);
        $this->_em->flush();
    }

    /**
     * @param int $entityId
     * @param string $entityClass
     * @param array $parameters
     * @return QueryBuilder
     */
    public function findAllByEntityReturnQueryBuilder(int $entityId, string $entityClass, array $parameters): QueryBuilder
    {
        $qb = $this->createQueryBuilder('inv');

        $qb->where($qb->expr()->eq('inv.entityClass', ':entityClass'))
            ->setParameter('entityClass', $entityClass)
            ->andWhere($qb->expr()->eq("inv.entityId", ':entityId'))
            ->setParameter('entityId', $entityId);

        if (isset($parameters['state'])) {
            $qb->andWhere($qb->expr()->eq("inv.state", ':state'))
                ->setParameter('state', $parameters['state']);
        }
        if (isset($parameters['type'])) {
            $qb->andWhere($qb->expr()->eq("inv.type", ':type'))
                ->setParameter('type', $parameters['type']);
        }
        $qb->orderby('inv.createdOn', 'DESC');
        $qb->addOrderBy('inv.id', 'DESC');
        return $qb;
    }

    /**
     * @param int $entityId
     * @param string $entityClass
     * @return float|int|mixed|string
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countByEntity(int $entityId, string $entityClass)
    {
        $qb = $this->createQueryBuilder('inv');
        $qb->select('count(inv . id)')
            ->andWhere($qb->expr()->eq('inv . entityClass', ':entityClass'))
            ->setParameter('entityClass', $entityClass)
            ->andWhere($qb->expr()->eq("inv.entityId", ':entityId'))
            ->setParameter('entityId', $entityId);
        return $qb->getQuery()->getSingleScalarResult();
    }
}
