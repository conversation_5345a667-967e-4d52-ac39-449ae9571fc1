<?php

namespace App\Repository;

use App\Entity\Application;
use App\Entity\Certification;
use App\Entity\CertificationFolder;
use App\Entity\CertificationPartner;
use App\Entity\Connection;
use App\Entity\Organism;
use App\Entity\Subscription;
use App\Library\utils\enums\ApplicationStates;
use App\Library\utils\enums\CertificationFolderCdcStates;
use App\Library\utils\enums\CertificationFolderStates;
use App\Library\utils\enums\CertificationPartnerStates;
use App\Library\utils\enums\ConnectionStates;
use App\Library\utils\enums\DataProviders;
use DateTime;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\DBAL\Driver\Exception as ExceptionAlias;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method Organism|null find($id, $lockMode = null, $lockVersion = null)
 * @method Organism|null findOneBy(array $criteria, array $orderBy = null)
 * @method Organism[]    findAll()
 * @method Organism[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class OrganismRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Organism::class);
    }

    /**
     * @param string $siret
     * @return Organism|null
     */
    public function findOneBySiret(string $siret): ?Organism
    {
        return $this->findOneBy(array("siret" => $siret));
    }

    /**
     * @param array $parameters
     * @return Organism|null
     */
    public function findOneByCriteria(array $parameters): ?Organism
    {
        return $this->findOneBy($parameters);
    }

    /**
     * @param int|null $offset
     * @param int|null $limit
     * @return ArrayCollection
     */
    public function findByToSync(int $offset = null, int $limit = null): ArrayCollection
    {
        $qb = $this->createQueryBuilder('org');
        $qb->where($qb->expr()->eq('org.toSync', true));
        $qb->setFirstResult($offset);
        $qb->setMaxResults($limit);
        return new ArrayCollection($qb->getQuery()->getResult());
    }

    /**
     * @param array $params
     * @return ArrayCollection
     */
    public function findAllWithOwnedBy(array $params = []): ArrayCollection
    {
        $qb = $this->createQueryBuilder('org');
        $qb->where($qb->expr()->isNotNull('org.ownedBy'));

        if (!empty($params['subscriptionTrainingTypes']) || !empty($params['subscriptionCertifierTypes'])) {
            $qb->join('org.subscription', 'sub');
            if (!empty($params['subscriptionTrainingTypes'])) {
                $qb->andWhere($qb->expr()->in('sub.trainingType', ':trainingTypes'));
                $qb->setParameter('trainingTypes', $params['subscriptionTrainingTypes']);
            }
            if (!empty($params['subscriptionCertifierTypes'])) {
                $qb->andWhere($qb->expr()->in('sub.certifierType', ':certifierTypes'));
                $qb->setParameter('certifierTypes', $params['subscriptionCertifierTypes']);
            }
        }

        if (!empty($params['dataProvider'])) {
            $qb->join('org.connections', 'connection')
                ->andWhere($qb->expr()->eq('connection.dataProvider', ':dataProviderType'))
                ->andWhere($qb->expr()->eq('connection.isInitialized', ':dataProviderIsInitialized'))
                ->andWhere($qb->expr()->eq('connection.state', ':dataProviderState'))
                ->setParameter('dataProviderType', $params['dataProvider']['type'])
                ->setParameter('dataProviderIsInitialized', $params['dataProvider']['isInitialized'] ?? false)
                ->setParameter('dataProviderState', $params['dataProvider']['state'] ?? ConnectionStates::ACTIVE());
        }

        return new ArrayCollection($qb->getQuery()->getResult());
    }

    /**
     * @param string $appId
     * @return ArrayCollection
     */
    public function findAllUsingApp(string $appId): ArrayCollection
    {
        $qb = $this->createQueryBuilder('org');
        $qb->innerJoin(Application::class, 'application', Join::WITH, 'application.organism=org');
        $qb->Where('application.appId like :appId')
            ->andWhere($qb->expr()->in('application.state', ':state'))
            ->setParameter('state', [
                ApplicationStates::ENABLED()->getValue(),
                ApplicationStates::TRIAL()->getValue(),
                ApplicationStates::PENDING_DISABLE_TRIAL()->getValue(),
                ApplicationStates::PENDING_DISABLE()->getValue()
            ])
            ->setParameter('appId', $appId);
        return new ArrayCollection($qb->getQuery()->getResult());
    }

    /**
     * @param array $params
     * @return ArrayCollection
     */
    public function findAllBySubscriptionType(array $params): ArrayCollection
    {
        $qb = $this->createQueryBuilder('org');
        $qb->join('org.subscription', 'sub');
        if (isset($params['trainingTypes'])) {
            $qb->andWhere($qb->expr()->in('sub.trainingType', ':trainingTypes'))
                ->setParameter('trainingTypes', $params['trainingTypes']);
        }
        if (isset($params['certifierTypes'])) {
            $qb->andWhere($qb->expr()->in('sub.certifierType', ':certifierTypes'))
                ->setParameter('certifierTypes', $params['certifierTypes']);
        }
        return new ArrayCollection($qb->getQuery()->getResult());
    }

    /**
     * @return ArrayCollection
     */
    public function findAllBySubscriptionAllowCertifierPlusAndCdcCertifierConnection(): ArrayCollection
    {
        $qb = $this->createQueryBuilder('org');
        $qb->select('org');
        $qb->innerJoin(Subscription::class, 'sub', Join::WITH, $qb->expr()->eq('sub.organism', 'org'))
            ->innerJoin(Connection::class, 'connection', Join::WITH, $qb->expr()->eq('connection.organism', 'sub.organism'))
            ->join('org.certifierCertifications', 'certification')
            ->innerJoin(CertificationFolder::class, 'certificationFolder', Join::WITH, $qb->expr()->andX(
                $qb->expr()->eq('certificationFolder.certification', 'certification'),
                $qb->expr()->eq('certificationFolder.certifier', 'org'),
            ))
            ->andWhere($qb->expr()->eq('sub.allowCertifierPlus', ':allowCertifierPlus'))
            ->andWhere($qb->expr()->gt('sub.certifierPeriodEndDate', ':date'))
            ->andWhere($qb->expr()->eq('connection.dataProvider', ':connectionDataProvider'))
            ->andWhere($qb->expr()->eq('connection.state', ':connectionState'))
            ->andWhere($qb->expr()->eq('certification.allowGenerateXmlAutomatically', ':allowGenerateXmlAutomatically'))
            ->andWhere($qb->expr()->isNotNull('certification.obtentionSystem')) //be sure certification is exportable
            ->andWhere($qb->expr()->in('certificationFolder.cdcState', ':cdcStates'))
            ->andWhere($qb->expr()->eq('certificationFolder.state', ':certificationFolderState'))
            ->andWhere($qb->expr()->eq('certificationFolder.cdcToExport', ':cdcToExport'))
            ->andWhere($qb->expr()->eq('certificationFolder.cdcCompliant', ':cdcCompliant'))
            ->andWhere($qb->expr()->eq('certificationFolder.cdcExcluded', ':cdcExcluded'))
            ->having('count(certificationFolder.id) > 0')
            ->groupBy('org.id')
            ->setParameter('allowCertifierPlus', true)
            ->setParameter('date', new DateTime())
            ->setParameter('allowGenerateXmlAutomatically', true)
            ->setParameter('connectionDataProvider', DataProviders::CDC_CERTIFIERS())
            ->setParameter('connectionState', ConnectionStates::ACTIVE()->getValue())
            ->setParameter('certificationFolderState', CertificationFolderStates::SUCCESS()->getValue())
            ->setParameter('cdcStates', [CertificationFolderCdcStates::NOT_EXPORTED()->getValue(), CertificationFolderCdcStates::PROCESSED_KO()->getValue()])
            ->setParameter('cdcToExport', true)
            ->setParameter('cdcCompliant', true)
            ->setParameter('cdcExcluded', false);
        return new ArrayCollection($qb->getQuery()->getResult());
    }

    /**
     * @return ArrayCollection
     */
    public function findAllUploadCatalogInProgress(): ArrayCollection
    {
        $qb = $this->createQueryBuilder('org');
        $qb->andWhere($qb->expr()->isNotNull('org.cpfCatalogMetadata'))
            ->andWhere($qb->expr()->eq("JSON_EXTRACT(org.cpfCatalogMetadata, '$.upload.state')", ':stateInProgress'))
            ->setParameter('stateInProgress', 'inProgress');
        return new ArrayCollection($qb->getQuery()->getResult());
    }

    /**
     * @param Organism $organism
     * @return Organism
     */
    public function save(Organism $organism): Organism
    {
        if (!$this->_em->contains($organism)) {
            $this->_em->persist($organism);
        }
        $this->_em->flush();
        return $organism;
    }


    /**
     * @param Organism $organism
     * @param array $parameters
     * @return QueryBuilder
     */
    public function findAllInPartnershipWith(Organism $organism, array $parameters): QueryBuilder
    {
        $subQb = $this->createQueryBuilder('organismPartner');
        $subQb->select('organismPartner.id')
            ->from('App\Entity\Organism', 'currentOrganism')
            ->join('currentOrganism.registrationFolders', 'currentOrganismRf')
            ->where($subQb->expr()->eq('currentOrganismRf.inPartnershipWith', 'organismPartner'))
            ->andWhere($subQb->expr()->eq('currentOrganism.id', ':currentOrganismId'))
            ->distinct();
        // Hack because doctrine would not let us count two different organisms :
        // "Cannot count query which selects two FROM components, cannot make distinction"
        $qb = $this->createQueryBuilder('organismPartner2');
        $qb->where($qb->expr()->in('organismPartner2.id', $subQb->getDQL()))
            ->setParameter('currentOrganismId', $organism->getId());

        if (!empty($parameters['query'])) {
            $qb->andWhere(
                $qb->expr()->orX(
                    $qb->expr()->like('organismPartner2.name', ':query'),
                    $qb->expr()->like('organismPartner2.siret', ':query')
                ))
                ->setParameter('query', '%' . trim($parameters['query']) . '%');
        }
        $qb->orderBy('organismPartner2.' . $parameters['sort'], $parameters['order']);
        $qb->addOrderBy('organismPartner2.id', 'ASC');
        return $qb;
    }

    /**
     * @param Organism $organism
     * @param array $parameters
     * @return QueryBuilder
     */
    public function findAllPotentialCertificationFolderHolders(Organism $organism, array $parameters): QueryBuilder
    {
        $qb = $this->createQueryBuilder('org');
        $qb
            ->leftJoin(CertificationPartner::class, 'cp', Join::WITH, $qb->expr()->andX( // left join to have the ability to add current organism
                $qb->expr()->eq('org.id', 'cp.partner'),
                $qb->expr()->eq('cp.certifier', ':currentOrganismId'),
                $qb->expr()->in('cp.state', ':cpStates')
            ))
            // either partner or current organism
            ->andWhere($qb->expr()->orX(
                $qb->expr()->eq('org.id', ':currentOrganismId'),
                $qb->expr()->isNotNull('cp.partner')
            ))
            ->setParameter('currentOrganismId', $organism->getId())
            ->setParameter('cpStates', [CertificationPartnerStates::ACTIVE(), CertificationPartnerStates::SUSPENDED(), CertificationPartnerStates::REVOKED()])
            ->distinct();
        if (!empty($parameters['query'])) {
            $qb->andWhere(
                $qb->expr()->orX(
                    $qb->expr()->like('org.name', ':query'),
                    $qb->expr()->like('org.siret', ':query')
                ))
                ->setParameter('query', '%' . trim($parameters['query']) . '%');
        }
        $qb->orderBy('org.' . $parameters['sort'], $parameters['order']);
        $qb->addOrderBy('org.id', 'ASC');
        return $qb;
    }


    /**
     * @param array $parameters
     * @return QueryBuilder
     */
    public function findAllReturnQueryBuilder(array $parameters): QueryBuilder
    {
        $qb = $this->createQueryBuilder('org');

        if (isset($parameters['organismType'])) {
            $orXOrganismType = $qb->expr()->orX();
            foreach ($parameters['organismType'] as $organismType) {
                switch ($organismType) {
                    case "all":
                        if (!empty($parameters['certification'])) {
                            $qb->leftJoin('org.certifierCertifications', 'cc')
                                ->leftJoin('org.certificationsPartner', 'cp');
                            $orXOrganismType->add(
                                $qb->expr()->orX($qb->expr()->eq('cc', ':certification'),
                                    $qb->expr()->eq('cp.certification', ':certification')));
                            $qb->setParameter('certification', $parameters['certification']);
                        }
                        break;
                    case "certifier":
                        $qb->leftJoin('org.certifierCertifications', 'cc');
                        $orXOrganismType->add($qb->expr()->eq('cc.id', ':certification'));
                        $qb->setParameter('certification', $parameters['certification']->getId());
                        break;
                    case "partner":
                        $qb->innerJoin('org.certificationsPartner', 'cp', Join::WITH, $qb->expr()->eq('org.id', 'cp.partner'))
                            ->andWhere($qb->expr()->eq('cp.certification', ':certification'))
                            ->setParameter('certification', $parameters['certification']);
                        break;
                }
            }
            $qb->andWhere($orXOrganismType);
        }
        if (!empty($parameters['reseller'])) {
            $qb->andWhere(
                $qb->expr()->eq('org.reseller', ':organismReseller'))
                ->setParameter('organismReseller', $parameters['reseller']);
        }
        if (!empty($parameters['query'])) {
            $qb->andWhere(
                $qb->expr()->orX(
                    $qb->expr()->like('org.name', ':query'),
                    $qb->expr()->like('org.siret', ':query')
                ))
                ->setParameter('query', '%' . trim($parameters['query']) . '%');
        }
        if (!empty($parameters['isTrainingOrganism'])) {
            $isTrainingOrganism = filter_var($parameters['isTrainingOrganism'], FILTER_VALIDATE_BOOLEAN);
            $qb->andWhere($qb->expr()->eq('org.trainingOrganism', ':isTraining'))
                ->setParameter('isTraining', $isTrainingOrganism);
        }
        if (!empty($parameters['isCertifierOrganism'])) {
            $isCertifierOrganism = filter_var($parameters['isCertifierOrganism'], FILTER_VALIDATE_BOOLEAN);
            if ($isCertifierOrganism) {
                $qb->innerJoin('org.certifierCertifications', 'cc2');
            } else {
                $qb->leftJoin('org.certifierCertifications', 'cc2')
                    ->andWhere($qb->expr()->isNull('cc2.id'));
            }
        }
        if (array_key_exists('sendAs', $parameters)) {
            if ($parameters['sendAs'] !== null) {
                $qb->andWhere($qb->expr()->isNotNull('org.sendAs'));
            } else {
                $qb->andWhere($qb->expr()->isNull('org.sendAs'));
            }
        }
        $qb->orderBy('org.' . $parameters['sort'], $parameters['order']);
        $qb->addOrderBy('org.id', 'ASC');
        return $qb;
    }

    /**
     * @param Certification $certification
     * @return Organism|null
     */
    public function findCertifierWithActiveConnectionForCertification(Certification $certification): ?Organism
    {
        $qb = $this->createQueryBuilder('organism');

        $qb->join('organism.connections', 'connection')
            ->join('organism.certifierCertifications', 'certification')
            ->andWhere($qb->expr()->eq('connection.dataProvider', ':dataProvider'))
            ->andWhere($qb->expr()->eq('connection.state', ':active'))
            ->andWhere($qb->expr()->eq('certification', ':certification'))
            ->setParameter('dataProvider', $certification->getDataProvider())
            ->setParameter('active', ConnectionStates::ACTIVE()->getValue())
            ->setParameter('certification', $certification);

        $result = $qb->getQuery()->setMaxResults(1)->getResult();
        return $result ? $result[0] : null;
    }

    /**
     * @return ArrayCollection
     */
    public function findAllPendingSendAsEmail(): ArrayCollection
    {
        $qb = $this->createQueryBuilder('org');
        $qb->where($qb->expr()->eq('JSON_LENGTH(org.sendAs)', 0));
        return new ArrayCollection($qb->getQuery()->getResult());
    }

    /**
     * @param Organism $organism
     * @param string $query
     * @return array
     * @throws Exception
     * @throws ExceptionAlias
     */
    public function extractMetadataKeys(Organism $organism, string $query = ''): array
    {
        $conn = $this->getEntityManager()->getConnection();
        $sql = "SELECT DISTINCT JSON_UNQUOTE(json_key)  metadata FROM organism, JSON_TABLE( JSON_KEYS(metadata), '$[*]' COLUMNS(json_key JSON PATH '$') ) t WHERE organism.metadata IS NOT NULL AND organism.id = :organism_id";
        if (!empty($query)) {
            $sql .= " AND JSON_UNQUOTE(json_key) LIKE :query";
        }
        $stmt = $conn->prepare($sql);
        $stmt->bindValue('organism_id', $organism->getId());
        if (!empty($query)) {
            $stmt->bindValue('query', '%' . $query . '%');
        }
        $result = $stmt->executeQuery();
        $metadata = [];
        foreach ($result->fetchAllAssociative() as $row) {
            $metadata[] = $row['metadata'];
        }
        return $metadata;
    }

    /**
     * Use raw SQL to ensure atomicity
     * @param Organism $organism
     * @param int $increment
     * @return void
     * @throws Exception
     */
    public function addToCpfCatalogSynchronizeNbActionsCount(Organism $organism, int $increment): Organism
    {
        $conn = $this->getEntityManager()->getConnection();
        $sql = <<<SQL
            UPDATE organism
            SET cpf_catalog_metadata = JSON_SET(
                cpf_catalog_metadata, 
                '$.synchronize.nbActionsDone', JSON_EXTRACT(cpf_catalog_metadata, '$.synchronize.nbActionsDone') + :increment,
                '$.synchronize.state', IF(JSON_EXTRACT(cpf_catalog_metadata, '$.synchronize.nbActionsDone') + :increment = JSON_EXTRACT(cpf_catalog_metadata, '$.synchronize.nbActionsTotal'), 'done', JSON_UNQUOTE(JSON_EXTRACT(cpf_catalog_metadata, '$.synchronize.state')))
            )
            WHERE id = :organismId
            AND JSON_EXTRACT(cpf_catalog_metadata, '$.synchronize.state') = 'inProgress'
        SQL;
        $conn->executeStatement($sql, ['organismId' => $organism->getId(), 'increment' => $increment]);
        $this->getEntityManager()->refresh($organism); // Ensure that we have the updated data
        return $organism;
    }
}
