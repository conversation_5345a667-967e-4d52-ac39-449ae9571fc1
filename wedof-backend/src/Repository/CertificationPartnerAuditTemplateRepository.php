<?php

namespace App\Repository;

use App\Entity\Certification;
use App\Entity\CertificationPartnerAuditTemplate;
use App\Entity\Organism;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<CertificationPartnerAuditTemplate>
 *
 * @method CertificationPartnerAuditTemplate|null find($id, $lockMode = null, $lockVersion = null)
 * @method CertificationPartnerAuditTemplate|null findOneBy(array $criteria, array $orderBy = null)
 * @method CertificationPartnerAuditTemplate[]    findAll()
 * @method CertificationPartnerAuditTemplate[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class CertificationPartnerAuditTemplateRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, CertificationPartnerAuditTemplate::class);
    }

    /**
     * @param CertificationPartnerAuditTemplate $certificationPartnerAuditTemplate
     * @return CertificationPartnerAuditTemplate
     */
    public function save(CertificationPartnerAuditTemplate $certificationPartnerAuditTemplate): CertificationPartnerAuditTemplate
    {
        if (!$this->_em->contains($certificationPartnerAuditTemplate)) {
            $this->_em->persist($certificationPartnerAuditTemplate);
        }
        $this->_em->flush();
        return $certificationPartnerAuditTemplate;
    }

    /**
     * @param Certification|null $certification
     * @param Organism $certifier
     * @return QueryBuilder
     */
    public function findAllReturnQueryBuilder(Organism $certifier, Certification $certification = null): QueryBuilder
    {
        $qb = $this->createQueryBuilder('cpat');

        $qb->andWhere($qb->expr()->eq('cpat.certifier', ':certifier'))
            ->setParameter('certifier', $certifier);

        if ($certification) {
            $qb->andWhere($qb->expr()->in('cpat.certification', ':certification'))
                ->setParameter('certification', $certification);
        }

        $qb->addOrderBy('cpat.id', 'ASC');

        return $qb;
    }

    /**
     * @param CertificationPartnerAuditTemplate $certificationPartnerAuditTemplate
     */
    public function delete(CertificationPartnerAuditTemplate $certificationPartnerAuditTemplate)
    {
        $this->_em->remove($certificationPartnerAuditTemplate);
        $this->_em->flush();
    }
}
