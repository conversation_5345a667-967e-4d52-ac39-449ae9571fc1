<?php

namespace App\Repository;

use App\Entity\CertificationFolderHistory;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Throwable;

/**
 * @method CertificationFolderHistory|null find($id, $lockMode = null, $lockVersion = null)
 * @method CertificationFolderHistory|null findOneBy(array $criteria, array $orderBy = null)
 * @method CertificationFolderHistory[]    findAll()
 * @method CertificationFolderHistory[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class CertificationFolderHistoryRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, CertificationFolderHistory::class);
    }

    /**
     * @param CertificationFolderHistory $certificationFolderHistory
     * @return void
     * @throws Throwable
     */
    public function delete(CertificationFolderHistory $certificationFolderHistory): void
    {
        $this->_em->remove($certificationFolderHistory);
        $this->_em->flush();
    }
}
