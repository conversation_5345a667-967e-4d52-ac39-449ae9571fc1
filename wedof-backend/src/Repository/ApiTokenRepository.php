<?php

namespace App\Repository;

use App\Entity\ApiToken;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method ApiToken|null find($id, $lockMode = null, $lockVersion = null)
 * @method ApiToken|null findOneBy(array $criteria, array $orderBy = null)
 * @method ApiToken[]    findAll()
 * @method ApiToken[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ApiTokenRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ApiToken::class);
    }

    /**
     * @param ApiToken $apiToken
     * @return ApiToken
     */
    public function save(ApiToken $apiToken): ApiToken
    {
        if (!$this->_em->contains($apiToken)) {
            $this->_em->persist($apiToken);
        }
        $this->_em->flush();
        return $apiToken;
    }

    /**
     * @param ApiToken $apiToken
     */
    public function delete(ApiToken $apiToken)
    {
        $this->_em->remove($apiToken);
        $this->_em->flush();
    }
}
