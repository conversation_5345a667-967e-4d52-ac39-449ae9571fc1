<?php

namespace App\Repository;

use App\Entity\CertificationPartnerAuditFile;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<CertificationPartnerAuditFile>
 *
 * @method CertificationPartnerAuditFile|null find($id, $lockMode = null, $lockVersion = null)
 * @method CertificationPartnerAuditFile|null findOneBy(array $criteria, array $orderBy = null)
 * @method CertificationPartnerAuditFile[]    findAll()
 * @method CertificationPartnerAuditFile[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class CertificationPartnerAuditFileRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, CertificationPartnerAuditFile::class);
    }

    /**
     * @param CertificationPartnerAuditFile $certificationPartnerAuditFile
     * @return CertificationPartnerAuditFile
     */
    public function save(CertificationPartnerAuditFile $certificationPartnerAuditFile): CertificationPartnerAuditFile
    {
        if (!$this->_em->contains($certificationPartnerAuditFile)) {
            $this->_em->persist($certificationPartnerAuditFile);
        }
        $this->_em->flush();
        return $certificationPartnerAuditFile;
    }

    /**
     * @param CertificationPartnerAuditFile $certificationPartnerAuditFile
     */
    public function delete(CertificationPartnerAuditFile $certificationPartnerAuditFile): void
    {
        $this->_em->remove($certificationPartnerAuditFile);
        $this->_em->flush();
    }

}
