<?php

namespace App\Repository;

use App\Entity\Config;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method Config|null find($id, $lockMode = null, $lockVersion = null)
 * @method Config|null findOneBy(array $criteria, array $orderBy = null)
 * @method Config[]    findAll()
 * @method Config[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ConfigRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Config::class);
    }

    /**
     * @return Config
     */
    public function findConfig(): Config
    {
        return $this->find(1);
    }

    /**
     * @param Config $configuration
     * @return Config
     */
    public function save(Config $configuration): Config
    {
        if (!$this->_em->contains($configuration)) {
            $this->_em->persist($configuration);
        }
        $this->_em->flush();
        return $configuration;
    }
}
