<?php

namespace App\Repository;

use App\Entity\Application;
use App\Entity\Organism;
use App\Library\utils\enums\ApplicationStates;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method Application|null find($id, $lockMode = null, $lockVersion = null)
 * @method Application|null findOneBy(array $criteria, array $orderBy = null)
 * @method Application[]    findAll()
 * @method Application[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ApplicationRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Application::class);
    }

    /**
     * @param Application $application
     * @return Application
     */
    public function save(Application $application): Application
    {
        if (!$this->_em->contains($application)) {
            $this->_em->persist($application);
        }
        $this->_em->flush();
        return $application;
    }

    /**
     * @param Organism $organism
     * @param string $appId
     * @param bool|null $enabled
     * @return Application|null
     * @throws NonUniqueResultException
     */
    public function findOneByOrganismAndAppId(Organism $organism, string $appId, bool $enabled = null): ?Application
    {
        $qb = $this->createQueryBuilder("a");
        $qb->Where('a.appId like :appId')
            ->andWhere($qb->expr()->eq('a.organism', ':organism'))
            ->setParameter('appId', $appId)
            ->setParameter('organism', $organism);
        if ($enabled != null) {
            $qb->andWhere($qb->expr()->in('a.state', ':state'));
            if ($enabled === true) {
                $states = [
                    ApplicationStates::ENABLED()->getValue(),
                    ApplicationStates::TRIAL()->getValue(),
                    ApplicationStates::PENDING_DISABLE_TRIAL()->getValue(),
                    ApplicationStates::PENDING_DISABLE()->getValue()
                ];
            } else {
                $states = [
                    ApplicationStates::DISABLED(),
                    ApplicationStates::PENDING_ENABLE_TRIAL(),
                    ApplicationStates::PENDING_ENABLE()
                ];
            }
            $qb->setParameter('state', $states);
        }
        return $qb->getQuery()->getOneOrNullResult();
    }

    /**
     * @param Organism $organism
     * @param bool|null $enabled
     * @return array
     */
    public function findAppIdsForOrganism(Organism $organism, ?bool $enabled): array
    {
        $qb = $this->createQueryBuilder('a');
        $qb->select('a.appId')
            ->andWhere($qb->expr()->eq('a.organism', ':organism'))
            ->setParameter('organism', $organism);
        if ($enabled != null) {
            $qb->andWhere($qb->expr()->in('a.state', ':state'));
            if ($enabled === true) {
                $states = [
                    ApplicationStates::ENABLED()->getValue(),
                    ApplicationStates::TRIAL()->getValue(),
                    ApplicationStates::PENDING_DISABLE_TRIAL()->getValue(),
                    ApplicationStates::PENDING_DISABLE()->getValue()
                ];
            } else {
                $states = [
                    ApplicationStates::DISABLED(),
                    ApplicationStates::PENDING_ENABLE_TRIAL(),
                    ApplicationStates::PENDING_ENABLE()
                ];
            }
            $qb->setParameter('state', $states);
        }
        return $qb->getQuery()->getSingleColumnResult();
    }
}
