<?php

namespace App\Repository;

use App\Entity\Certification;
use App\Entity\Organism;
use App\Entity\Training;
use App\Entity\TrainingAction;
use App\Library\utils\enums\TrainingActionStates;
use App\Library\utils\Tools;
use DateTime;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method TrainingAction|null find($id, $lockMode = null, $lockVersion = null)
 * @method TrainingAction|null findOneBy(array $criteria, array $orderBy = null)
 * @method TrainingAction[]    findAll()
 * @method TrainingAction[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class TrainingActionRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, TrainingAction::class);
    }

    /**
     * @param $externalId
     * @return TrainingAction|null
     */
    public function findOneByExternalId($externalId): ?TrainingAction
    {
        return $this->findOneBy(array('externalId' => $externalId));
    }

    /**
     * @param TrainingAction $trainingAction
     * @return TrainingAction
     */
    public function save(TrainingAction $trainingAction): TrainingAction
    {
        if (!$this->_em->contains($trainingAction)) {
            $this->_em->persist($trainingAction);
        }
        $this->_em->flush();
        return $trainingAction;
    }

    /**
     * @param Training $training
     * @param array $options
     * @return float|int|array|string
     */
    public function getExternalIdsByTraining(Training $training, array $options = [])
    {
        $qb = $this->createQueryBuilder('ta');
        $qb->select('ta.externalId');
        $qb->join('ta.training', 't')
            ->andWhere($qb->expr()->eq('t.id', $training->getId()));

        if (isset($options['state'])) {
            $qb->andWhere($qb->expr()->eq('ta.state', ':state'))
                ->setParameter('state', $options['state']);
        }

        if (isset($options['modality'])) {
            if ($options['modality'] === 'online') {
                $qb->andWhere($qb->expr()->eq("JSON_TYPE(JSON_EXTRACT(ta.rawData, '$.trainingAddress'))", "'NULL'"));
            } else {
                $qb->andWhere($qb->expr()->neq("JSON_TYPE(JSON_EXTRACT(ta.rawData, '$.trainingAddress'))", "'NULL'"));
            }
        }

        return $qb->getQuery()->getArrayResult();
    }

    /**
     * @param Organism $organism
     * @param array $parameters
     * @return QueryBuilder
     */
    public function findAllReturnQueryBuilder(Organism $organism, array $parameters): QueryBuilder
    {
        $qb = $this->createQueryBuilder('ta');
        $qb->join('ta.training', 'training');

        $qb->where($qb->expr()->eq('training.organism', ':organism'))
            ->setParameter('organism', $organism);

        if (!empty($parameters['trainingId'])) {
            $qb->andWhere($qb->expr()->eq('training.externalId', ':training'))
                ->setParameter('training', $parameters['trainingId']);
        }
        if (!empty($parameters['certifInfo']) || isset($parameters['eligible'])) {
            $qb->join('training.certification', 'cert');
            if (!empty($parameters['certifInfo'])) {
                $qb->andWhere($qb->expr()->eq('cert.certifInfo', ':certifInfo'))
                    ->setParameter('certifInfo', $parameters['certifInfo']);
            }
            if (isset($parameters['eligible'])) {
                $eligible = filter_var($parameters['eligible'], FILTER_VALIDATE_BOOLEAN);
                $now = new DateTime('now');
                if ($eligible) {
                    $qb->andWhere($qb->expr()->gte('cert.cpfDateEnd', ':now'));
                } else {
                    $qb->andWhere($qb->expr()->lt('cert.cpfDateEnd', ':now'));
                }
                $qb->setParameter('now', $now);
            }
        }
        if (!empty($parameters['query']) && !empty(trim($parameters['query']))) {
            //maybe we have an externalId   trainingId/trainingActionId => siret_trainingId/siret_trainingActionId
            if (Tools::contains($parameters['query'], "/")) {
                $qb->andWhere($qb->expr()->orX(
                    $qb->expr()->like('training.title', ':query'),
                    $qb->expr()->like('LOWER(ta.externalId)', ':query'), // utf8_bin in prod, which is case sensitive
                    $qb->expr()->like('LOWER(ta.externalId)', ':query_with_siret') // utf8_bin in prod, which is case sensitive
                ));
                $query = trim(strtolower($parameters['query']));
                $query_siret = $organism->getSiret() . "_" . str_replace('/', '/' . $organism->getSiret() . "_", $query);
                $qb->setParameter('query_with_siret', '%' . $query_siret . '%');
            } else {
                $qb->andWhere($qb->expr()->orX(
                    $qb->expr()->like('training.title', ':query'),
                    $qb->expr()->like('LOWER(ta.externalId)', ':query') // utf8_bin in prod, which is case sensitive
                ));
            }
            $qb->setParameter('query', '%' . trim(strtolower($parameters['query'])) . '%');
        }
        if (!empty($parameters['in'])) {
            $trainingActionsIds = $parameters['in']->map(function ($trainingAction) {
                return $trainingAction->getId();
            })->getValues();
            $qb->andWhere('ta.id IN (:trainingActionsIds)')
                ->setParameter('trainingActionsIds', $trainingActionsIds);
        }
        if (isset($parameters['proposalId'])) {
            $qb->join('ta.proposals', 'proposal')
                ->andWhere($qb->expr()->eq('proposal.id', ':proposal'))
                ->setParameter('proposal', $parameters['proposalId']);
        }
        if (isset($parameters['state'])) {
            $states = $parameters['state'];
            if (!is_array($parameters['state'])) {
                $states = explode(',', $parameters['state']); // SHOULD NOT BE HERE
            }
            $qb->andWhere($qb->expr()->in('ta.state', ':states'))
                ->setParameter('states', $states);
        }
        if (isset($parameters['trainingState'])) {
            $trainingStates = $parameters['trainingState'];
            $qb->andWhere($qb->expr()->in('training.state', ':trainingStates'))
                ->setParameter('trainingStates', $trainingStates);
        }
        if (isset($parameters['dataProvider']) && $parameters['dataProvider'] != 'all') {
            $qb->andWhere($qb->expr()->eq('training.dataProvider', ':dataProvider'))
                ->setParameter('dataProvider', $parameters['dataProvider']);
        }

        $qb->orderBy('ta.id', $parameters['order'] ?? 'ASC');

        return $qb;
    }

    /**
     * @param Organism $organism
     * @param array $parameters
     * @return ArrayCollection
     */
    public function findAllWithParams(Organism $organism, array $parameters): ArrayCollection
    {
        return new ArrayCollection($this->findAllReturnQueryBuilder($organism, $parameters)->getQuery()->getResult());
    }

    /**
     * @param Organism $organism
     * @param string $trainingExternalId
     * @return array
     */
    public function findAllByTrainingForCpfXml(Organism $organism, string $trainingExternalId): array
    {
        $qb = $this->createQueryBuilder('ta');
        $qb->join('ta.training', 't')
            ->where($qb->expr()->eq('t.organism', ':organism'))
            ->setParameter('organism', $organism)
            ->andWhere($qb->expr()->eq('t.externalId', ':training'))
            ->setParameter('training', $trainingExternalId)
            ->andWhere("JSON_EXTRACT(ta.rawData, '$.statusLabel') IN ('ACTIVE', 'DRAFT', 'VALIDATED')")
            ->andWhere($qb->expr()->neq('ta.state', ':stateDeleted'))
            ->setParameter('stateDeleted', TrainingActionStates::DELETED()->getValue())
            ->addOrderBy('ta.id', 'ASC');
        return $qb->getQuery()->getResult();
    }


    /**
     * @param Certification $certification
     * @param string $siret
     * @param string $dataProvider
     * @return array
     */
    public function getPricingStats(Certification $certification, string $siret, string $dataProvider = 'cpf'): array
    {
        $qb = $this->createQueryBuilder('ta');
        $qb->select("MIN(JSON_EXTRACT(ta.rawData, '$.totalTvaHT')) as min, MAX(JSON_EXTRACT(ta.rawData, '$.totalTvaHT')) as max, AVG(JSON_EXTRACT(ta.rawData, '$.totalTvaHT')) as average")
            ->join('ta.training', 't')
            ->join('t.organism', 'org')
            ->andWhere($qb->expr()->eq('org.siret', ':siret'))
            ->setParameter('siret', $siret)
            ->andWhere($qb->expr()->eq('ta.state', ':state'))
            ->andWhere($qb->expr()->eq('t.certification', ':certification'))
            ->andWhere($qb->expr()->eq('t.dataProvider', ':dataProvider'))
            ->setParameter('certification', $certification)
            ->setParameter('state', TrainingActionStates::PUBLISHED()->getValue())
            ->setParameter('dataProvider', $dataProvider);

        return $qb->getQuery()->getResult()[0];
    }

    /**
     * @param Certification $certification
     * @param string $siret
     * @param string $dataProvider
     * @return array
     */
    public function getDurationStats(Certification $certification, string $siret, string $dataProvider = 'cpf'): array
    {
        $qb = $this->createQueryBuilder('ta');
        $qb->select("MIN(JSON_EXTRACT(ta.rawData, '$.averageLearningTime')) as min, MAX(JSON_EXTRACT(ta.rawData, '$.averageLearningTime')) as max, AVG(JSON_EXTRACT(ta.rawData, '$.averageLearningTime')) as average")
            ->join('ta.training', 't')
            ->join('t.organism', 'org')
            ->andWhere($qb->expr()->eq('org.siret', ':siret'))
            ->andWhere($qb->expr()->eq('ta.state', ':state'))
            ->setParameter('siret', $siret)
            ->andWhere($qb->expr()->eq('t.certification', ':certification'))
            ->andWhere($qb->expr()->neq("JSON_TYPE(JSON_EXTRACT(ta.rawData, '$.averageLearningTime'))", "'NULL'")) // Only way to check if not json null (not the same as MySQL NULL...)
            ->andWhere($qb->expr()->eq('t.dataProvider', ':dataProvider'))
            ->setParameter('certification', $certification)
            ->setParameter('state', TrainingActionStates::PUBLISHED()->getValue())
            ->setParameter('dataProvider', $dataProvider);
        return $qb->getQuery()->getResult()[0];
    }

    /**
     * @param Training $training
     * @param TrainingActionStates $targetState
     */
    public function markAsFromTraining(Training $training, TrainingActionStates $targetState): void
    {
        $qb = $this->createQueryBuilder('ta');
        $qb->update()
            ->set('ta.lastUpdate', ':lastUpdate')
            ->set('ta.state', ':state')
            ->where($qb->expr()->eq('ta.training', ':training'))
            ->andWhere($qb->expr()->neq('ta.state', ':state'))
            ->setParameter('state', $targetState->getValue())
            ->setParameter('lastUpdate', new DateTime())
            ->setParameter('training', $training);
        $qb->getQuery()->execute();
    }

    /**
     * @param Training $training
     * @param array $externalIds
     * @return void
     */
    public function markAsDeletedFromTrainingExcludeTrainingActionExternalIds(Training $training, array $externalIds): void
    {
        $qb = $this->createQueryBuilder('ta');
        $qb->update()
            ->set('ta.lastUpdate', ":lastUpdate")
            ->set('ta.state', ':state')
            ->where($qb->expr()->eq('ta.training', ':training'))
            ->andWhere($qb->expr()->neq('ta.state', ':state'))
            ->andWhere($qb->expr()->notIn("ta.externalId", ":externalIds"))
            ->setParameter('state', TrainingActionStates::DELETED()->getValue())
            ->setParameter('lastUpdate', new DateTime())
            ->setParameter('training', $training)
            ->setParameter(':externalIds', $externalIds);
        $qb->getQuery()->execute();
    }

    /**
     * @param ArrayCollection $trainingIds
     * @param TrainingActionStates $targetState
     * @return ArrayCollection
     */
    public function markAsFromTrainingIds(ArrayCollection $trainingIds, TrainingActionStates $targetState): ArrayCollection
    {
        $qb = $this->createQueryBuilder('trainingAction');
        $qb->select("trainingAction.id")
            ->join('trainingAction.training', 'training')
            ->andWhere($qb->expr()->in('training.id', ':trainingIds'))
            ->setParameter('trainingIds', $trainingIds);
        $trainingActionToMark = new ArrayCollection(array_column($qb->getQuery()->getArrayResult(), 'id'));

        $qbu = $this->createQueryBuilder('trainingAction');
        $qbu->update()
            ->set('trainingAction.state', ':state')
            ->set('trainingAction.lastUpdate', ':lastUpdate')
            ->where($qbu->expr()->in('trainingAction.id', ':ids'))
            ->setParameter('state', $targetState->getValue())
            ->setParameter('lastUpdate', new DateTime('now'))
            ->setParameter('ids', $trainingActionToMark);
        $qbu->getQuery()->execute();

        return $trainingActionToMark;
    }

    /**
     * @param Certification $certification
     * @param Organism $organism
     * @param array $states
     * @param string $dataProvider
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countByCertificationAndByOrganismAndStates(Certification $certification, Organism $organism, array $states = [], string $dataProvider = 'cpf'): int
    {
        $qb = $this->createQueryBuilder('ta');
        $qb->select($qb->expr()->count('ta'));
        $qb->join('ta.training', 't')
            ->join('t.organism', 'org')
            ->join('t.certification', 'cert')
            ->where($qb->expr()->in('org.id', $organism->getId()))
            ->andWhere($qb->expr()->in('cert.id', $certification->getId()))
            ->andWhere($qb->expr()->eq('t.dataProvider', ':dataProvider'))
            ->setParameter('dataProvider', $dataProvider);
        if ($states) {
            $qb->andWhere($qb->expr()->in('t.state', ':trainingState'))
                ->setParameter('trainingState', $states); // THIS IS A HACK based on the fact that TrainingActionStates and TrainingStates are alike
            $qb->andWhere($qb->expr()->in('ta.state', ':state'))
                ->setParameter('state', $states);
        }
        return $qb->getQuery()->getSingleScalarResult();
    }

    /**
     * @param Training $training
     * @return ArrayCollection
     */
    public function findAllPublishedFromTraining(Training $training): ArrayCollection
    {
        $qb = $this->createQueryBuilder('trainingAction');
        $qb->select('trainingAction.externalId')
            ->where($qb->expr()->eq('trainingAction.state', ':published'))
            ->andWhere($qb->expr()->eq('trainingAction.training', ':training'))
            ->setParameter('published', TrainingActionStates::PUBLISHED()->getValue())
            ->setParameter('training', $training);

        return new ArrayCollection($qb->getQuery()->getResult());
    }

    /**
     * @param array $externalIds
     * @return void
     */
    public function markAsDeletedFromTrainingActionExternalIds(array $externalIds): void
    {
        $qb = $this->createQueryBuilder('trainingAction');
        $qb->update()
            ->set('trainingAction.state', ':state')
            ->set('trainingAction.lastUpdate', ':lastUpdate')
            ->where($qb->expr()->in('trainingAction.externalId', ':externalIds'))
            ->setParameter('state', TrainingActionStates::DELETED()->getValue())
            ->setParameter('lastUpdate', new DateTime('now'))
            ->setParameter('externalIds', $externalIds);
        $qb->getQuery()->execute();
    }
}
