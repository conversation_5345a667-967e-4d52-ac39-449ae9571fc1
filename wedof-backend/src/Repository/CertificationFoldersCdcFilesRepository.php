<?php

namespace App\Repository;

use App\Entity\CdcFile;
use App\Entity\CertificationFolder;
use App\Entity\CertificationFoldersCdcFiles;
use App\Library\utils\enums\CdcFileStates;
use App\Library\utils\enums\CertificationFoldersCdcFilesStates;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method CertificationFoldersCdcFiles|null find($id, $lockMode = null, $lockVersion = null)
 * @method CertificationFoldersCdcFiles|null findOneBy(array $criteria, array $orderBy = null)
 * @method CertificationFoldersCdcFiles[]    findAll()
 * @method CertificationFoldersCdcFiles[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class CertificationFoldersCdcFilesRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, CertificationFoldersCdcFiles::class);
    }

    /**
     * @param CertificationFoldersCdcFiles $certificationFoldersCdcFiles
     * @return CertificationFoldersCdcFiles
     */
    public function save(CertificationFoldersCdcFiles $certificationFoldersCdcFiles): CertificationFoldersCdcFiles
    {
        if (!$this->_em->contains($certificationFoldersCdcFiles)) {
            $this->_em->persist($certificationFoldersCdcFiles);
        }
        $this->_em->flush();
        return $certificationFoldersCdcFiles;
    }

    /**
     * @param CertificationFoldersCdcFiles $certificationFoldersCdcFiles
     */
    public function delete(CertificationFoldersCdcFiles $certificationFoldersCdcFiles): void
    {
        $this->_em->remove($certificationFoldersCdcFiles);
        $this->_em->flush();
    }

    /**
     * @param CertificationFolder $certificationFolder
     * @return CertificationFoldersCdcFiles[]
     */
    public function findAllNotAbortedOrderedBySubmissionDateDesc(CertificationFolder $certificationFolder): array
    {
        $qb = $this->createQueryBuilder('cfCdcFile');
        $qb->join('cfCdcFile.cdcFile', 'cdcFile')
            ->andWhere($qb->expr()->eq('cfCdcFile.certificationFolder', ':certificationFolder'))
            ->andWhere($qb->expr()->neq('cdcFile.state', ':cdcFileStateAborted'))
            ->setParameter('certificationFolder', $certificationFolder)
            ->setParameter('cdcFileStateAborted', CdcFileStates::ABORTED()->getValue())
            ->addOrderBy('cdcFile.submissionDate', 'DESC');
        return $qb->getQuery()->getResult();
    }

    /**
     * @param CdcFile $cdcFile
     * @return bool
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function hasErrorCertificationFolders(CdcFile $cdcFile): bool
    {
        $qb = $this->createQueryBuilder('cfsCdcFile');
        $qb->select('count(cfsCdcFile.id)')
            ->join('cfsCdcFile.cdcFile', 'cdcFile')
            ->andWhere($qb->expr()->eq('cdcFile', ':cdcFile'))
            ->andWhere($qb->expr()->eq('cdcFile.state', ':cdcFileState'))
            ->andWhere($qb->expr()->eq('cfsCdcFile.state', ':cfsCdcFileState'))
            ->andWhere($qb->expr()->notLike('cfsCdcFile.errorMessage', ':cfsCdcFileErrorMessage'))
            ->setParameter('cdcFile', $cdcFile)
            ->setParameter('cdcFileState', CdcFileStates::PROCESSED()->getValue())
            ->setParameter('cfsCdcFileState', CertificationFoldersCdcFilesStates::PROCESSED_KO()->getValue())
            ->setParameter('cfsCdcFileErrorMessage', '%' . " existe déjà en base");
        return $qb->getQuery()->getSingleScalarResult() > 0;
    }
}
