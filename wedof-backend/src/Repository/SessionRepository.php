<?php

namespace App\Repository;

use App\Entity\Certification;
use App\Entity\Organism;
use App\Entity\Session;
use App\Entity\Training;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\enums\SessionStates;
use DateTime;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;
use Throwable;

/**
 * @method Session|null find($id, $lockMode = null, $lockVersion = null)
 * @method Session|null findOneBy(array $criteria, array $orderBy = null)
 * @method Session[]    findAll()
 * @method Session[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class SessionRepository extends ServiceEntityRepository
{
    //-------------------
    // METHODES PUBLIQUES
    //-------------------
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Session::class);
    }

    /**
     * @param $externalId
     * @return Session|null
     */
    public function findOneByExternalId($externalId): ?Session
    {
        return $this->findOneBy(array('externalId' => $externalId));
    }

    /**
     * @param Organism $organism
     * @param array $parameters
     * @return QueryBuilder
     */
    public function findAllReturnQueryBuilder(Organism $organism, array $parameters): QueryBuilder
    {
        $qb = $this->createQueryBuilder('s');
        $qb->join('s.trainingAction', 'ta')
            ->join('ta.training', 't')
            ->where($qb->expr()->eq('t.organism', ':organism'))
            ->setParameter('organism', $organism);

        if (isset($parameters['certifInfo']) || isset($parameters['eligible'])) {
            $qb->join('t.certification', 'cert');
            if (isset($parameters['certifInfo'])) {
                $qb->andWhere($qb->expr()->eq('cert.certifInfo', ':certifInfo'))
                    ->setParameter('certifInfo', $parameters['certifInfo']);
            }
            if (isset($parameters['eligible'])) {
                $eligible = filter_var($parameters['eligible'], FILTER_VALIDATE_BOOLEAN);
                $now = new DateTime('now');
                if ($eligible) {
                    $qb->andWhere($qb->expr()->gte('cert.cpfDateEnd', ':now'));
                } else {
                    $qb->andWhere($qb->expr()->lt('cert.cpfDateEnd', ':now'));
                }
                $qb->setParameter('now', $now);
            }
        }
        if (isset($parameters['trainingActionId'])) {
            $qb->andWhere($qb->expr()->eq('ta.externalId', ':trainingActionId'))
                ->setParameter('trainingActionId', $parameters['trainingActionId']);
        }
        if (isset($parameters['trainingId'])) {
            $qb->andWhere($qb->expr()->eq('t.externalId', ':trainingId'))
                ->setParameter('trainingId', $parameters['trainingId']);
        }
        if (isset($parameters['since'])) {
            $qb->andWhere($qb->expr()->gte('s.startDate', ':sinceDate'))
                ->setParameter('sinceDate', $parameters['since']);
        }
        if (isset($parameters['until'])) {
            $qb->andWhere($qb->expr()->lte('s.endDate', ':untilDate'))
                ->setParameter('untilDate', $parameters['until']);
        }
        if (isset($parameters['query'])) {
            if (in_array(strtolower($parameters['query']), ['distance', 'online'])) {
                $qb->andWhere("JSON_TYPE(JSON_EXTRACT(ta.rawData, '$.trainingAddress')) = 'NULL'"); // Only way to check if not json null (not the same as MySQL NULL...)
            } else {
                $qb->andWhere($qb->expr()->orX(
                    $qb->expr()->like("LOWER(s.externalId)", ':query'), // utf8_bin in prod, which is case sensitive
                    $qb->expr()->like("t.title", ':query'),
                    $qb->expr()->like("LOWER(JSON_EXTRACT(ta.rawData, '$.trainingAddress.city'))", ':query') // LIKE on JSON_EXTRACT is case sensitive
                ))
                    ->setParameter('query', '%' . trim(strtolower($parameters['query'])) . '%');
            }
        }
        if (isset($parameters['state'])) {
            $states = $parameters['state'];
            if (!is_array($parameters['state'])) {
                $states = explode(',', $parameters['state']); // SHOULD NOT BE HERE
            }
            $qb->andWhere($qb->expr()->in('s.state', ':states'))
                ->setParameter('states', $states);
        }
        if (isset($parameters['recruitmentStatus'])) {
            $recruitmentStatuses = explode(',', strtoupper($parameters['recruitmentStatus']));
            //only when multiple values we add ""
            if (sizeof($recruitmentStatuses) > 1) {
                for ($i = 0; $i < sizeof($recruitmentStatuses); $i++) {
                    $recruitmentStatuses[$i] = '"' . $recruitmentStatuses[$i] . '"';
                }
            }
            $qb->andWhere("JSON_EXTRACT(s.rawData, '$.recruitmentStatusLabel') IN (:recruitmentStatus)")
                ->setParameter('recruitmentStatus', $recruitmentStatuses);
        }

        if (isset($parameters['sort'])) {
            $qb->orderBy('s.' . $parameters['sort'], $parameters['order'] ?? 'ASC');
        }
        $qb->addOrderBy('s.id', 'ASC');

        return $qb;
    }

    /**
     * @param Organism $organism
     * @param string $trainingActionExternalId
     * @return array
     */
    public function findAllByActionForCpfXml(Organism $organism, string $trainingActionExternalId): array
    {
        $qb = $this->createQueryBuilder('s');
        $qb->join('s.trainingAction', 'ta')
            ->join('ta.training', 't')
            ->where($qb->expr()->eq('t.organism', ':organism'))
            ->setParameter('organism', $organism)
            ->andWhere($qb->expr()->eq('ta.externalId', ':trainingActionId'))
            ->setParameter('trainingActionId', $trainingActionExternalId)
            ->andWhere("JSON_EXTRACT(s.rawData, '$.statusLabel') IN ('ACTIVE', 'DRAFT', 'VALIDATED')")
            ->andWhere($qb->expr()->neq('s.state', ':stateDeleted'))
            ->setParameter('stateDeleted', SessionStates::DELETED()->getValue())
            ->addOrderBy('s.id', 'ASC');
        return $qb->getQuery()->getResult();
    }

    /**
     * @param Session $session
     * @return Session
     * @throws Throwable
     */
    public function save(Session $session): Session
    {
        if (!$this->_em->contains($session)) {
            $this->_em->persist($session);
        }
        $this->_em->flush();
        return $session;
    }

    /**
     * @param Training $training
     * @param SessionStates $targetState
     * @return void
     */
    public function markAsFromTraining(Training $training, SessionStates $targetState): void
    {
        $qb = $this->createQueryBuilder('s');
        $qb->select('s.id')
            ->join('s.trainingAction', 'ta')
            ->join('ta.training', 't')
            ->where($qb->expr()->eq('t.id', ':id'))
            ->andWhere($qb->expr()->neq('s.state', ':state'))
            ->setParameter('state', $targetState->getValue())
            ->setParameter('id', $training->getId());

        $this->markAs(array_column($qb->getQuery()->getScalarResult(), 'id'), $targetState);
    }

    /**
     * @param Training $training
     * @param array $externalIds
     * @return void
     */
    public function markAsDeletedFromTrainingExcludeTrainingActionExternalIds(Training $training, array $externalIds): void
    {
        $qb = $this->createQueryBuilder('s');
        $targetState = SessionStates::DELETED();
        $qb->select('s.id')
            ->join('s.trainingAction', 'ta')
            ->join('ta.training', 't')
            ->where($qb->expr()->eq('t.id', ':id'))
            ->andWhere($qb->expr()->notIn('ta.externalId', ':externalIds'))
            ->andWhere($qb->expr()->neq('s.state', ':state'))
            ->setParameter('state', $targetState->getValue())
            ->setParameter('id', $training->getId())
            ->setParameter('externalIds', $externalIds);

        $this->markAs(array_column($qb->getQuery()->getScalarResult(), 'id'), $targetState);
    }

    /**
     * @param array $sessionIds
     * @param SessionStates $targetState
     * @return void
     */
    private function markAs(array $sessionIds, SessionStates $targetState): void
    {
        $qb = $this->createQueryBuilder('session');
        $qb->update()
            ->set('session.state', ':state')
            ->where($qb->expr()->in('session.id', ':ids'))
            ->setParameter('state', $targetState->getValue())
            ->setParameter('ids', $sessionIds);
        $qb->getQuery()->execute();
    }

    /**
     * @param ArrayCollection $trainingActionIds
     * @param SessionStates $targetState
     * @return ArrayCollection
     */
    public function markAsFromTrainingActionIds(ArrayCollection $trainingActionIds, SessionStates $targetState): ArrayCollection
    {
        $qb = $this->createQueryBuilder('session');
        $qb->select("session.id")
            ->join('session.trainingAction', 'trainingAction')
            ->andWhere($qb->expr()->in('trainingAction.id', ':trainingActionIds'))
            ->setParameter('trainingActionIds', $trainingActionIds);
        $sessionsToMark = new ArrayCollection(array_column($qb->getQuery()->getArrayResult(), 'id'));

        $qbu = $this->createQueryBuilder('session');
        $qbu->update()
            ->set('session.state', ':state')
            ->where($qbu->expr()->in('session.id', ':ids'))
            ->setParameter('state', $targetState->getValue())
            ->setParameter('ids', $sessionsToMark);
        $qbu->getQuery()->execute();

        return $sessionsToMark;
    }

    /**
     * @param Organism $organism
     * @return ArrayCollection
     */
    public function markAsArchivedAllExpired(Organism $organism): ArrayCollection
    {
        $qb = $this->createQueryBuilder('session');
        $qb->select("session.id")
            ->where($qb->expr()->eq('session.state', ':state'))
            ->andWhere($qb->expr()->lt('session.startDate', ':now'))
            ->setParameter('state', SessionStates::PUBLISHED()->getValue())
            ->setParameter('now', new DateTime('now'));
        $qb->join('session.trainingAction', 'trainingAction')
            ->join('trainingAction.training', 'training')
            ->andWhere($qb->expr()->eq('training.organism', ':organism'))
            ->setParameter('organism', $organism);
        $sessionExpired = new ArrayCollection(array_column($qb->getQuery()->getArrayResult(), 'id'));

        $qbu = $this->createQueryBuilder('session');
        $qbu->update()
            ->set('session.state', ':state')
            ->where($qbu->expr()->in('session.id', ':ids'))
            ->setParameter('state', SessionStates::ARCHIVED())
            ->setParameter('ids', $sessionExpired);
        $qbu->getQuery()->execute();

        return $sessionExpired;
    }

    /**
     * @param Organism $organism
     * @param Certification $certification
     * @param DataProviders $dataProvider
     * @return Session|null
     * @throws NonUniqueResultException
     */
    public function findOneByCertificationAndDataProvider(Organism $organism, Certification $certification, DataProviders $dataProvider): ?Session
    {
        $qb = $this->createQueryBuilder('s');
        $qb->join('s.trainingAction', 'ta')
            ->join('ta.training', 't')
            ->andWhere($qb->expr()->eq('t.organism', ':organism'))
            ->andWhere($qb->expr()->eq('t.certification', ':certification'))
            ->andWhere($qb->expr()->eq('t.dataProvider', ':dataProvider'))
            ->setParameter('organism', $organism)
            ->setParameter('certification', $certification)
            ->setParameter('dataProvider', $dataProvider->getValue())
            ->setMaxResults(1);

        return $qb->getQuery()->getOneOrNullResult();
    }

    /**
     * @param array $trainingActionExternalIds
     * @return ArrayCollection
     */
    public function markAsDeletedFromTrainingActionExternalIds(array $trainingActionExternalIds): ArrayCollection
    {
        $qb = $this->createQueryBuilder('session');
        $qb->select('session.id')
            ->join('session.trainingAction', 'trainingAction')
            ->where($qb->expr()->in('trainingAction.externalId', ':trainingActionExternalIds'))
            ->andWhere($qb->expr()->eq('session.state', ':published'))
            ->setParameter('trainingActionExternalIds', $trainingActionExternalIds)
            ->setParameter('published', SessionStates::PUBLISHED()->getValue());
        $sessionToDelete = new ArrayCollection(array_column($qb->getQuery()->getArrayResult(), 'id'));

        $qbu = $this->createQueryBuilder('session');
        $qbu->update()
            ->set('session.state', ':state')
            ->where($qbu->expr()->in('session.id', ':ids'))
            ->setParameter('state', SessionStates::DELETED()->getValue())
            ->setParameter('ids', $sessionToDelete);
        $qbu->getQuery()->execute();

        return $sessionToDelete;
    }
}
