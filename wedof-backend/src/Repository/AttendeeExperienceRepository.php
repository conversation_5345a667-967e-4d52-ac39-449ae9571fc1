<?php

namespace App\Repository;

use App\Entity\Attendee;
use App\Entity\AttendeeExperience;
use App\Entity\CertificationFolder;
use App\Entity\CertificationFolderSurvey;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<AttendeeExperience>
 *
 * @method AttendeeExperience|null find($id, $lockMode = null, $lockVersion = null)
 * @method AttendeeExperience|null findOneBy(array $criteria, array $orderBy = null)
 * @method AttendeeExperience[]    findAll()
 * @method AttendeeExperience[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class AttendeeExperienceRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, AttendeeExperience::class);
    }

    /**
     * @param AttendeeExperience $attendeeExperience
     * @return AttendeeExperience
     */
    public function save(AttendeeExperience $attendeeExperience): AttendeeExperience
    {
        if (!$this->_em->contains($attendeeExperience)) {
            $this->_em->persist($attendeeExperience);
        }
        $this->_em->flush();
        return $attendeeExperience;
    }

    /**
     * @param array $parameters
     * @param Attendee $attendee
     * @return QueryBuilder
     */
    public function findAllReturnQueryBuilder(array $parameters, Attendee $attendee): QueryBuilder
    {
        $qb = $this->createQueryBuilder('attendeeExperience');

        if (isset($parameters['certificationFolder'])) {
            $qb
                ->join(CertificationFolderSurvey::class, 'certificationFolderSurvey', Join::WITH, $qb->expr()->orX(
                    $qb->expr()->eq('certificationFolderSurvey.initialExperience', 'attendeeExperience'),
                    $qb->expr()->eq("certificationFolderSurvey.sixMonthExperience", 'attendeeExperience'),
                    $qb->expr()->eq("certificationFolderSurvey.longTermExperience", 'attendeeExperience')
                ))
                ->join(CertificationFolder::class, 'certificationFolder', Join::WITH, $qb->expr()->eq(
                    'certificationFolderSurvey.certificationFolder', 'certificationFolder'
                ))
                ->andWhere($qb->expr()->eq('certificationFolder', ':certificationFolder'))
                ->setParameter('certificationFolder', $parameters['certificationFolder']);
        }

        $qb->andWhere($qb->expr()->eq('attendeeExperience.attendee', ':attendee'))
            ->setParameter('attendee', $attendee);

        $qb->orderBy("attendeeExperience.createdOn", "DESC");
        $qb->addOrderBy('attendeeExperience.id', 'DESC');

        return $qb;
    }
}
