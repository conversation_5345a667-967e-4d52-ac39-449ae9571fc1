<?php

namespace App\Repository;

use App\Entity\UserMessage;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Message>
 *
 * @method UserMessage|null find($id, $lockMode = null, $lockVersion = null)
 * @method UserMessage|null findOneBy(array $criteria, array $orderBy = null)
 * @method UserMessage[]    findAll()
 * @method UserMessage[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class UserMessageRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, UserMessage::class);
    }

    /**
     * @param UserMessage $message
     * @return UserMessage
     */
    public function save(UserMessage $message): UserMessage
    {
        if (!$this->_em->contains($message)) {
            $this->_em->persist($message);
        }
        $this->_em->flush();
        return $message;
    }

    /**
     * @param UserMessage $message
     * @return bool
     */
    public function delete(UserMessage $message): bool
    {
        if ($this->_em->contains($message)) {
            $this->_em->remove($message);
            $this->_em->flush();
        }
        return true;
    }

    /**
     * @param array $parameters
     * @return ArrayCollection
     */
    public function findAllWithParams(array $parameters): ArrayCollection
    {
        $qb = $this->createQueryBuilder('m');
        $qb->leftJoin('m.users', 'u');

        if (isset($parameters['visible'])) {
            $qb->andWhere($qb->expr()->eq('m.visible', ':visible'))
                ->setParameter('visible', $parameters['visible']);
        }

        if (isset($parameters['user'])) {
            $qb->andWhere($qb->expr()->eq('u', ':user'))
                ->setParameter('user', $parameters['user']);
        } else {
            $qb->andWhere($qb->expr()->isNull('u'));
        }

        if (isset($parameters['content'])) {
            $qb->andWhere($qb->expr()->like('m.message', ':content'))
                ->setParameter('content', '%' . $parameters['content'] . '%');
        }

        return new ArrayCollection($qb->getQuery()->getResult());
    }
}
