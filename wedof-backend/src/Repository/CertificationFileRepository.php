<?php

namespace App\Repository;

use App\Entity\CertificationFile;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<CertificationFile>
 *
 * @method CertificationFile|null find($id, $lockMode = null, $lockVersion = null)
 * @method CertificationFile|null findOneBy(array $criteria, array $orderBy = null)
 * @method CertificationFile[]    findAll()
 * @method CertificationFile[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class CertificationFileRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, CertificationFile::class);
    }

    /**
     * @param CertificationFile $certificationFile
     * @return CertificationFile
     */
    public function save(CertificationFile $certificationFile): CertificationFile
    {
        if (!$this->_em->contains($certificationFile)) {
            $this->_em->persist($certificationFile);
        }
        $this->_em->flush();
        return $certificationFile;
    }

    /**
     * @param CertificationFile $certificationFile
     */
    public function delete(CertificationFile $certificationFile): void
    {
        $this->_em->remove($certificationFile);
        $this->_em->flush();
    }

}
