<?php

namespace App\Repository;

use App\Entity\Connection;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Connection>
 *
 * @method Connection|null find($id, $lockMode = null, $lockVersion = null)
 * @method Connection|null findOneBy(array $criteria, array $orderBy = null)
 * @method Connection[]    findAll()
 * @method Connection[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ConnectionRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Connection::class);
    }

    /**
     * @param Connection $connection
     * @return Connection
     */
    public function save(Connection $connection): Connection
    {
        if (!$this->_em->contains($connection)) {
            $this->_em->persist($connection);
        }
        $this->_em->flush();
        return $connection;
    }

    /**
     * @param Connection $connection
     * @return void
     */
    public function delete(Connection $connection): void
    {
        $this->_em->remove($connection);
        $this->_em->flush();
    }

    /**
     * @param array $parameters
     * @return ArrayCollection
     */
    public function findAllWithParams(array $parameters): ArrayCollection
    {
        $qb = $this->createQueryBuilder('connection');

        if (!empty($parameters['organism'])) {
            $qb->andWhere($qb->expr()->eq('connection.organism', ':organism'))
                ->setParameter('organism', $parameters['organism']);
        }
        if (!empty($parameters['dataProviders'])) {
            $qb->andWhere($qb->expr()->in('connection.dataProvider', ':dataProviders'))
                ->setParameter('dataProviders', $parameters['dataProviders']);
        }
        if (!empty($parameters['states'])) {
            $qb->andWhere($qb->expr()->in('connection.state', ':states'))
                ->setParameter('states', $parameters['states']);
        }
        if (isset($parameters['existAtDataProvider'])) {
            $qb->andWhere($qb->expr()->in('connection.existAtDataProvider', ':existAtDataProvider'))
                ->setParameter('existAtDataProvider', $parameters['existAtDataProvider']);
        }
        if (!empty($parameters['refreshAt'])) {
            $qb->andWhere($qb->expr()->lt('connection.refreshAt', ':refreshAt'))
                ->setParameter('refreshAt', $parameters['refreshAt']);
        }

        return new ArrayCollection($qb->getQuery()->getResult());
    }
}
