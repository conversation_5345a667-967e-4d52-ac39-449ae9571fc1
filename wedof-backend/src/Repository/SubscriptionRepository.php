<?php

namespace App\Repository;

use App\Controller\app\SubscriptionController;
use App\Entity\Subscription;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method Subscription|null create(array $criteria, array $orderBy = null)
 * @method Subscription|null findOneBy(array $criteria, ?array $orderBy = null)
 */
class SubscriptionRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Subscription::class);
    }

    /**
     * @param Subscription $subscription
     * @return Subscription
     */
    public function save(Subscription $subscription): Subscription
    {
        if (!$this->_em->contains($subscription)) {
            $this->_em->persist($subscription);
        }
        $this->_em->flush();
        return $subscription;
    }

    /**
     * @param array $parameters
     * @return QueryBuilder
     */
    public function findAllReturnQueryBuilder(array $parameters): QueryBuilder
    {
        $qb = $this->createQueryBuilder('sub');
        if (isset($parameters['trainingType']) && $parameters['trainingType'] != 'all') {
            $qb->andWhere($qb->expr()->eq('sub.trainingType', ':trainingType'))
                ->setParameter('trainingType', $parameters['trainingType']);
        }
        if (isset($parameters['trainingPendingCancellation']) && $parameters['trainingPendingCancellation'] != 'all') {
            $parameters['trainingPendingCancellation'] = filter_var($parameters['trainingPendingCancellation'], FILTER_VALIDATE_BOOLEAN);
            $qb->andWhere($qb->expr()->eq('sub.trainingPendingCancellation', ':trainingPendingCancellation'))
                ->setParameter('trainingPendingCancellation', $parameters['trainingPendingCancellation']);
        }
        if (isset($parameters['certifierType']) && $parameters['certifierType'] != 'all') {
            $qb->andWhere($qb->expr()->eq('sub.certifierType', ':certifierType'))
                ->setParameter('certifierType', $parameters['certifierType']);
        }
        if (isset($parameters['certifierPendingCancellation']) && $parameters['certifierPendingCancellation'] != 'all') {
            $parameters['certifierPendingCancellation'] = filter_var($parameters['certifierPendingCancellation'], FILTER_VALIDATE_BOOLEAN);
            $qb->andWhere($qb->expr()->eq('sub.certifierPendingCancellation', ':certifierPendingCancellation'))
                ->setParameter('certifierPendingCancellation', $parameters['certifierPendingCancellation']);
        }
        return $qb;
    }

    /**
     * @param string $siret
     * @return Subscription
     * @throws NonUniqueResultException
     *
     * méthode utilisée dans les annotations ici : @link SubscriptionController::showBySiret()
     */
    public function findBySiret(string $siret): ?Subscription
    {
        $qb = $this->createQueryBuilder('sub')
            ->join('sub.organism', 'org');
        $qb->where($qb->expr()->eq('org.siret', ':siret'))
            ->setParameter('siret', $siret);
        return $qb->getQuery()->getOneOrNullResult();
    }

}
