<?php

namespace App\Repository;

use App\Entity\Certification;
use App\Entity\CertificationPartner;
use App\Entity\CertificationPartnerAudit;
use App\Entity\CertifierAccess;
use App\Entity\Connection;
use App\Entity\Organism;
use App\Library\utils\enums\CertificationPartnerAuditStates;
use App\Library\utils\enums\CertificationPartnerStates;
use App\Library\utils\enums\ConnectionStates;
use App\Library\utils\enums\ConnectionTypes;
use App\Library\utils\enums\DataProviders;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\DBAL\Driver\Exception as ExceptionAlias;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method CertificationPartner|null find($id, $lockMode = null, $lockVersion = null)
 * @method CertificationPartner|null findOneBy(array $criteria, array $orderBy = null)
 * @method CertificationPartner[]    findAll()
 * @method CertificationPartner[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class CertificationPartnerRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, CertificationPartner::class);
    }

    /**
     * @param CertificationPartner $certificationPartner
     * @return CertificationPartner
     */
    public function save(CertificationPartner $certificationPartner): CertificationPartner
    {
        if (!$this->_em->contains($certificationPartner)) {
            $this->_em->persist($certificationPartner);
        }
        $this->_em->flush();
        return $certificationPartner;
    }

    /**
     * @param CertificationPartner $certificationPartner
     */
    public function delete(CertificationPartner $certificationPartner): void
    {
        $this->_em->remove($certificationPartner);
        $this->_em->flush();
    }

    /**
     * @param Certification $certification
     * @param Organism|null $certifier
     * @param bool $activeOnly
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countPartners(Certification $certification, bool $activeOnly, Organism $certifier = null): int
    {
        $qb = $this->createQueryBuilder('cp');
        $qb->select('count(cp.partner)')
            ->where($qb->expr()->eq('cp.certification', ':certification'))
            ->setParameter('certification', $certification);
        if ($certifier) {
            $qb->andWhere($qb->expr()->eq('cp.certifier', ':certifier'))
                ->setParameter('certifier', $certifier);
        }
        if ($activeOnly) {
            $qb->andWhere($qb->expr()->eq('cp.state', ':cpActive'))
                ->setParameter('cpActive', CertificationPartnerStates::ACTIVE()->getValue());
        }
        return $qb->getQuery()->getSingleScalarResult();
    }

    /**
     * @param Organism $partner
     * @param array $certifications
     * @param bool $activeOnly
     * @return ArrayCollection
     */
    public function findAllByPartner(Organism $partner, array $certifications = [], bool $activeOnly = true): ArrayCollection
    {
        $qb = $this->createQueryBuilder('cp');
        $qb->where($qb->expr()->eq('cp.partner', ':partner'))
            ->setParameter('partner', $partner);
        if (!empty($certifications)) {
            $qb->andWhere($qb->expr()->in('cp.certification', ':certifications'))
                ->setParameter('certifications', $certifications);
        }
        if ($activeOnly) {
            $qb->andWhere($qb->expr()->eq('cp.state', ':cpActive'))
                ->setParameter('cpActive', CertificationPartnerStates::ACTIVE()->getValue());
        }
        return new ArrayCollection($qb->getQuery()->getResult());
    }

    /**
     * @param Organism $certifier
     * @param Organism $partner
     * @return ArrayCollection
     */
    public function findAllByOrganisms(Organism $certifier, Organism $partner): ArrayCollection
    {
        $qb = $this->createQueryBuilder('certificationPartner');
        $qb->where($qb->expr()->eq('certificationPartner.certifier', ':certifier'))
            ->andWhere($qb->expr()->eq('certificationPartner.partner', ':partner'))
            ->setParameter('certifier', $certifier)
            ->setParameter('partner', $partner);
        return new ArrayCollection($qb->getQuery()->getResult());
    }

    /**
     * @param Certification $certification
     * @param Organism $organism
     * @param bool $activeOnly
     * @return CertificationPartner|null
     */
    public function findOneByCertificationAndPartner(Certification $certification, Organism $organism, bool $activeOnly = true): ?CertificationPartner
    {
        $params = ['certification' => $certification, 'partner' => $organism];
        if ($activeOnly) {
            $params['state'] = CertificationPartnerStates::ACTIVE()->getValue();
        }
        return $this->findOneBy($params);
    }

    /**
     * @param string $certifInfo
     * @param string $siret
     * @return CertificationPartner|null
     * @throws NonUniqueResultException
     * USED by magic calls DO NOT DELETE !
     */
    public function findOneByCertifInfoAndSiret(string $certifInfo, string $siret): ?CertificationPartner
    {
        $qb = $this->createQueryBuilder('cp');

        $qb->join('cp.certification', 'c')
            ->join('cp.partner', 'p')
            ->where($qb->expr()->eq('c.certifInfo', ':certifInfo'))
            ->andWhere($qb->expr()->eq('p.siret', ':siret'))
            ->setParameter('certifInfo', $certifInfo)
            ->setParameter('siret', $siret);

        return $qb->getQuery()->getOneOrNullResult();
    }

    /**
     * @param array $parameters
     * @param Certification|null $certification
     * @return QueryBuilder
     */
    public function findAllReturnQueryBuilder(array $parameters, Certification $certification = null): QueryBuilder
    {
        $qb = $this->createQueryBuilder('cp')
            ->join('cp.partner', 'partner');

        if ($certification) {
            $qb->andWhere($qb->expr()->eq('cp.certification', ':certification'))
                ->setParameter('certification', $certification);
        }

        if (!empty($parameters['certifier'])) {
            $qb->andWhere($qb->expr()->eq('cp.certifier', ':certifier'))
                ->setParameter('certifier', $parameters['certifier']);
        }

        if (!empty($parameters['state']) && !in_array('all', $parameters['state'])) {
            $qb->andWhere($qb->expr()->in('cp.state', ':state'))
                ->setParameter('state', $parameters['state']);
        }

        if (!empty($parameters['compliance']) && !in_array('all', $parameters['compliance'])) {
            $hasComplianceInProgress = in_array(CertificationPartnerAuditStates::IN_PROGRESS()->getValue(), $parameters['compliance']);
            if ($hasComplianceInProgress) {
                // we can filter compliance to know if there are inProgress audits
                // we need to extract inProgress value because it's relative to CertificationPartnerAuditStates while compliance checks CertificationPartnerAuditResults
                // "inProgress" doesn't exist in CertificationPartnerAuditResults so we need remove it for $parameters['compliance']
                $indexInProgress = array_search(CertificationPartnerAuditStates::IN_PROGRESS()->getValue(), $parameters['compliance']);
                unset($parameters['compliance'][$indexInProgress]);
                $qb->innerJoin(
                    CertificationPartnerAudit::class,
                    'cpAudit',
                    Join::WITH,
                    $qb->expr()->eq('cpAudit.certificationPartner', 'cp.id'));
                $qb->andWhere($qb->expr()->orX(
                    $qb->expr()->eq('cpAudit.state', ':inProgressState'),
                    $qb->expr()->in('cp.compliance', ':compliance')
                ));
                $qb->setParameter('inProgressState', CertificationPartnerAuditStates::IN_PROGRESS()->getValue());
                $qb->setParameter('compliance', $parameters['compliance']);
            } else {
                $qb->andWhere($qb->expr()->in('cp.compliance', ':compliance'))
                    ->setParameter('compliance', $parameters['compliance']);
            }
        }

        if (isset($parameters['connectionIssue'])) {
            $subQB = $this->_em->createQueryBuilder();
            $subQB->select('connection.id')
                ->from(Connection::class, ' connection')
                ->where($subQB->expr()->eq('connection.dataProvider', ':dataProvider'))
                ->andWhere($subQB->expr()->eq('connection.type', ':type'))
                ->andWhere($subQB->expr()->eq('connection.state', ':connectionState'))
                ->andWhere($subQB->expr()->eq(' connection.organism', 'partner'));

            if ($parameters['connectionIssue']) {
                $qb->andWhere($qb->expr()->not($qb->expr()->exists($subQB)));
            } else {
                $qb->andWhere($qb->expr()->exists($subQB));
            }

            $qb->setParameter('dataProvider', DataProviders::CPF()->getValue())
                ->setParameter('type', ConnectionTypes::HABILITATION()->getValue())
                ->setParameter('connectionState', ConnectionStates::ACTIVE()->getValue());
        }

        if (!empty($parameters['certifierAccessState']) && !in_array('all', $parameters['certifierAccessState'])) {
            if (in_array('none', $parameters['certifierAccessState'])) {
                $qb
                    ->leftJoin(CertifierAccess::class, 'ca', Join::WITH, $qb->expr()->andX(
                        $qb->expr()->eq('ca.certifier', 'cp.certifier'),
                        $qb->expr()->eq('ca.partner', 'cp.partner')
                    ))
                    ->andWhere($qb->expr()->isNull('ca.certifier'));
            } else {
                $qb
                    ->join(CertifierAccess::class, 'ca', Join::WITH, $qb->expr()->andX(
                        $qb->expr()->eq('ca.certifier', 'cp.certifier'),
                        $qb->expr()->eq('ca.partner', 'cp.partner')
                    ));
                $orXCertifierAccessStates = $qb->expr()->orX();
                if (in_array('waiting', $parameters['certifierAccessState'])) {
                    $orXCertifierAccessStates->add(
                        $qb->expr()->andX($qb->expr()->isNull('ca.activatedOn'), $qb->expr()->isNull('ca.terminatedOn'))
                    );
                }
                if (in_array('accepted', $parameters['certifierAccessState'])) {
                    $orXCertifierAccessStates->add(
                        $qb->expr()->andX($qb->expr()->isNotNull('ca.activatedOn'), $qb->expr()->isNull('ca.terminatedOn'))
                    );
                }
                if (in_array('refused', $parameters['certifierAccessState'])) {
                    $orXCertifierAccessStates->add(
                        $qb->expr()->andX($qb->expr()->isNull('ca.activatedOn'), $qb->expr()->isNotNull('ca.terminatedOn'))
                    );
                }
                if (in_array('terminated', $parameters['certifierAccessState'])) {
                    $orXCertifierAccessStates->add(
                        $qb->expr()->andX($qb->expr()->isNotNull('ca.activatedOn'), $qb->expr()->isNotNull('ca.terminatedOn'))
                    );
                }
                $qb->andWhere($orXCertifierAccessStates);
            }
        }

        if (!empty($parameters['metadata']) && sizeof($parameters['metadata']) <= 2) {
            if (!empty($parameters['metadata'][1])) {
                $metadata = '"' . $parameters['metadata'][0] . '": "' . $parameters['metadata'][1] . '"';  //one value with metadata key
            } else {
                $metadata = '"' . $parameters['metadata'][0] . '":'; //all values with metadata key
            }
            $qb->andWhere($qb->expr()->like('cp.metadata', ':metadata'));
            $qb->setParameter('metadata', '%' . $metadata . '%');
        }

        if (isset($parameters['query'])) {
            $qb->leftJoin('cp.tags', 'tag')
                ->andWhere($qb->expr()->orX(
                    $qb->expr()->like('partner.siret', ':siret'),
                    $qb->expr()->like('partner.name', ':query'),
                    $qb->expr()->like('tag.name', ':query')
                ))
                ->setParameter('siret', $parameters['query'])
                ->setParameter('query', '%' . $parameters['query'] . '%');
        }

        if (isset($parameters['sort']) && in_array($parameters['sort'], ['stateLastUpdate', 'name', 'state'])) {
            if ($parameters['sort'] === "stateLastUpdate") {
                $qb->orderBy('cp.stateLastUpdate', $parameters['order']);
            } else {
                $qb->orderBy(($parameters['sort'] === 'state' ? 'cp.' : 'partner.') . $parameters['sort'], ($parameters['order'] ?? 'DESC'));
            }
        }
        $qb->addOrderBy('cp.id', 'ASC');
        return $qb;
    }

    /**
     * @return QueryBuilder
     */
    public function countActivePartnersSubQuery(): QueryBuilder
    {
        $qb = $this->createQueryBuilder('cp');
        $qb->select('count(cp.partner)')
            ->where($qb->expr()->eq('cp.certification', 'c'))
            ->andWhere($qb->expr()->eq('cp.state', ':cpActive'));
        return $qb;
    }

    /**
     * @param Organism $organism
     * @param string $query
     * @return array
     * @throws Exception
     * @throws ExceptionAlias
     */
    public function extractMetadataKeys(Organism $organism, string $query = ''): array
    {
        $conn = $this->getEntityManager()->getConnection();
        $sql = "SELECT DISTINCT JSON_UNQUOTE(json_key) as metadata FROM certifications_partners, json_table( json_keys(metadata), '$[*]' COLUMNS(json_key JSON PATH '$') ) t WHERE certifications_partners.metadata is not null AND certifications_partners.certifier_id = :organism_id";
        if (!empty($query)) {
            $sql .= " AND JSON_UNQUOTE(json_key) LIKE :query";
        }
        $stmt = $conn->prepare($sql);
        $stmt->bindValue('organism_id', $organism->getId());
        if (!empty($query)) {
            $stmt->bindValue('query', '%' . $query . '%');
        }
        $result = $stmt->executeQuery();
        $metadata = [];
        foreach ($result->fetchAllAssociative() as $row) {
            $metadata[] = $row['metadata'];
        }
        return $metadata;
    }
}
