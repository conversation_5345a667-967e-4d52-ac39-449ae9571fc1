<?php

namespace App\Repository;

use App\Application\MessageTemplates\Entity\Message;
use App\Entity\Attendee;
use App\Entity\Certification;
use App\Entity\CertifierAccess;
use App\Entity\Organism;
use App\Entity\RegistrationFolder;
use App\Entity\Session;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\enums\PaymentStates;
use App\Library\utils\enums\RegistrationFolderAttendeeStates;
use App\Library\utils\enums\RegistrationFolderBillingStates;
use App\Library\utils\enums\RegistrationFolderStates;
use App\Library\utils\Tools;
use DateTime;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\DBAL\Driver\Exception as ExceptionAlias;
use Doctrine\ORM\AbstractQuery;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;
use LogicException;

/**
 * @method RegistrationFolder|null find($id, $lockMode = null, $lockVersion = null)
 * @method RegistrationFolder|null findOneBy(array $criteria, array $orderBy = null)
 * @method RegistrationFolder[]    findAll()
 * @method RegistrationFolder[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class RegistrationFolderRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, RegistrationFolder::class);
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @return RegistrationFolder
     */
    public function save(RegistrationFolder $registrationFolder): RegistrationFolder
    {
        if (!$this->_em->contains($registrationFolder)) {
            $this->_em->persist($registrationFolder);
        }
        $this->_em->flush();
        return $registrationFolder;
    }

    /**
     * @param string $externalId
     * @return RegistrationFolder|null
     */
    public function findOneByExternalId(string $externalId): ?RegistrationFolder
    {
        return $this->findOneBy(array('externalId' => $externalId));
    }

    /**
     * @param $externalId
     * @return RegistrationFolder|null
     */
    public function findOneByExternalIdAndStateUpdatable($externalId): ?RegistrationFolder
    {
        return $this->findOneBy(array('externalId' => $externalId, 'attendeeState' => RegistrationFolderAttendeeStates::updatableStates(), 'state' => RegistrationFolderStates::updatableStates(), 'billingState' => RegistrationFolderBillingStates::updatableStates()));
    }

    /**
     * @param Organism $organism
     * @param DataProviders $dataProvider
     * @return RegistrationFolder|null
     * @throws NonUniqueResultException
     */
    public function findLastUpdatedByOrganismAndDataProvider(Organism $organism, DataProviders $dataProvider): ?RegistrationFolder
    {
        $qb = $this->createQueryBuilder('rf');
        $qb->where($qb->expr()->eq('rf.organism', ':organism'))
            ->andWhere($qb->expr()->eq('rf.type', ':type'))
            ->setParameter('organism', $organism)
            ->setParameter('type', $dataProvider->getValue());
        $qb->orderBy('rf.lastUpdate', 'DESC')
            ->setMaxResults(1);

        return $qb->getQuery()->getOneOrNullResult();
    }

    /**
     * @param Organism $organism
     * @param array $parameters
     * @return QueryBuilder
     */
    public function findAllReturnQueryBuilder(Organism $organism, array $parameters): QueryBuilder
    {
        if (isset($parameters['columnId'])) {
            $columnId = $parameters['columnId'];
            $columnParameters = $parameters; // Affectation copies the array - We use a copy to avoid overriding $parameters
            if (in_array(RegistrationFolderStates::ALL()->getValue(), $parameters['state'])) {
                $columnParameters['state'] = RegistrationFolderStates::valuesStatesToString(); // Hack because of "all" fake state
            }
            if (in_array(RegistrationFolderBillingStates::ALL()->getValue(), $parameters['billingState'])) {
                $columnParameters['billingState'] = RegistrationFolderBillingStates::valuesStatesToString(); // Hack because of "all" fake state
            }
            $parameters = Tools::computeKanbanColumnParameters($columnParameters, $this->listColumnConfigs(), $columnId);
        }
        $joinMessageTemplate = false;
        $joinTags = false;
        $qb = $this->createQueryBuilder('rf');

        if ($parameters['organismType'] === 'self') {
            $qb->where($qb->expr()->eq('rf.organism', ':organism'))
                ->setParameter('organism', $organism);
        } else if (!empty($parameters['partner'])) { // accès aux dossiers d'un partenaire
            $qb->join('rf.organism', 'org')
                ->join(CertifierAccess::class, 'ca', Join::WITH, 'rf.organism = :partner')
                ->andWhere($qb->expr()->isNotNull('rf.certificationFolder'))
                ->andWhere($qb->expr()->eq('ca.certifier', ':certifier'))
                ->andWhere($qb->expr()->eq('ca.partner', ':partner'))
                ->andWhere($qb->expr()->isNotNull('ca.activatedOn'))
                ->andWhere('CASE WHEN ca.terminatedOn IS NOT NULL THEN ca.terminatedOn ELSE :now END >= rf.createdOn ')
                ->setParameter('certifier', $organism)
                ->setParameter('partner', $parameters['partner'])
                ->setParameter('now', new DateTime());
        } else { // accès aux dossiers de tous les partenaires
            $qb->join('rf.organism', 'org')
                ->join(CertifierAccess::class, 'ca', Join::WITH, 'rf.organism = ca.partner')
                ->andWhere($qb->expr()->isNotNull('rf.certificationFolder'))
                ->andWhere($qb->expr()->eq('ca.certifier', ':certifier'))
                ->andWhere($qb->expr()->isNotNull('ca.activatedOn'))
                ->andWhere('CASE WHEN ca.terminatedOn IS NOT NULL THEN ca.terminatedOn ELSE :now END >= rf.createdOn ')
                ->setParameter('now', new DateTime())
                ->setParameter('certifier', $organism);
        }

        if (!in_array('all', $parameters['state'])) {
            $qb->andWhere($qb->expr()->in('rf.state', ':state'))
                ->setParameter('state', $parameters['state']);
        }
        if (!in_array('all', $parameters['type'])) {
            $qb->andWhere($qb->expr()->in('rf.type', ':type'))
                ->setParameter('type', $parameters['type']);
        }
        if (!in_array('all', $parameters['billingState'])) {
            $qb->andWhere($qb->expr()->in('rf.billingState', ':billingState'))
                ->setParameter('billingState', $parameters['billingState']);
        }
        if (!in_array('all', $parameters['controlState'])) {
            $qb->andWhere($qb->expr()->in('rf.controlState', ':controlState'))
                ->setParameter('controlState', $parameters['controlState']);
        }
        if (!in_array('all', $parameters['certificationFolderState'])) {
            $qb->join('rf.certificationFolder', 'cf')
                ->andWhere($qb->expr()->in('cf.state', ':certificationFolderState'))
                ->setParameter('certificationFolderState', $parameters['certificationFolderState']);
        }
        if (isset($parameters['messageState'])) {
            $qb->andWhere($qb->expr()->in('message.state', ':messageState'))
                ->setParameter('messageState', $parameters['messageState']);
            $joinMessageTemplate = true;
        }
        if (isset($parameters['messageTemplate'])) {
            $qb->andWhere($qb->expr()->in('messageTemplate.id', ':messageTemplateId'))
                ->setParameter('messageTemplateId', $parameters['messageTemplate']);
            $joinMessageTemplate = true;
        }
        if (!empty($parameters['certifications'])) {
            $qb->andWhere($qb->expr()->in('rf.certification', ':certifications'))
                ->setParameter('certifications', $parameters['certifications']);
        }
        if (!empty($parameters['sessionId'])) {
            $qb->join('rf.session', 'se1')
                ->andWhere($qb->expr()->eq('se1.externalId', ':sessionId'))
                ->setParameter('sessionId', $parameters['sessionId']);
        }
        if (!empty($parameters['proposalCode'])) {
            $qb->join('rf.proposal', 'p')
                ->andWhere(
                    $qb->expr()->orX(
                        $qb->expr()->eq('p', ':proposal'),
                        $qb->expr()->eq('p.parentProposal', ':proposal')
                    )
                )
                ->setParameter('proposal', $parameters['proposal']);
        }
        if (!empty($parameters['trainingActionId'])) {
            $qb->join('rf.session', 'se2')
                ->join('se2.trainingAction', 'ta1')
                ->andWhere($qb->expr()->eq('ta1.externalId', ':trainingActionId'))
                ->setParameter('trainingActionId', $parameters['trainingActionId']);
        }
        if (!empty($parameters['trainingId'])) {
            $qb->join('rf.session', 'se3')
                ->join('se3.trainingAction', 'ta2')
                ->join('ta2.training', 't')
                ->andWhere($qb->expr()->eq('t.externalId', ':trainingId'))
                ->setParameter('trainingId', $parameters['trainingId']);
        }
        if (isset($parameters['completionRate'])) {
            switch ($parameters['completionRate']) {
                case '0':
                    $qb->andWhere($qb->expr()->eq('rf.completionRate', 0));
                    break;
                case '25<':
                    $qb->andWhere($qb->expr()->lt('rf.completionRate', 25));
                    break;
                case '25<>80':
                    $qb->andWhere($qb->expr()->between('rf.completionRate', 25, 80));
                    break;
                case '>80':
                    $qb->andWhere($qb->expr()->gt('rf.completionRate', 80));
                    break;
                case '100':
                    $qb->andWhere($qb->expr()->eq('rf.completionRate', 100));
                    break;
                default:
                    throw new LogicException("Valeur pour completionRate : " . $parameters['completionRate'] . " inconnu.");
            }
        }
        if (!empty($parameters['metadata']) && sizeof($parameters['metadata']) <= 2) {
            if (!empty($parameters['metadata'][1])) {
                $metadata = '"' . $parameters['metadata'][0] . '": "' . $parameters['metadata'][1] . '"';  //one value with metadata key
            } else {
                $metadata = '"' . $parameters['metadata'][0] . '":'; //all values with metadata key
            }
            $qb->andWhere($qb->expr()->like('rf.metadata', ':metadata'));
            $qb->setParameter('metadata', '%' . $metadata . '%');
        }
        if ($parameters['filterOnStateDate'] === 'paymentScheduledDate') {
            $qb->join('rf.payments', 'payments')
                ->andWhere($qb->expr()->eq('payments.state', ':stateWaiting'))
                ->setParameter('stateWaiting', PaymentStates::WAITING()->getValue());
            $filterOnStateDate = 'payments.scheduledDate';
        } else if (in_array($parameters['filterOnStateDate'], ['sessionStartDate', 'sessionEndDate'])) {
            $filterOnStateDate = "STRTODATE(JSON_UNQUOTE(JSON_EXTRACT(rf.rawData, '$.trainingActionInfo." . $parameters['filterOnStateDate'] . "')),'%d/%m/%Y')";
        } else if (in_array($parameters['filterOnStateDate'], ['lastUpdate', 'createdOn', 'updatedOn'])) {
            $filterOnStateDate = 'rf.' . $parameters['filterOnStateDate'];
            if (!empty($parameters['daysSinceLastUpdatedCompletionRate'])) {
                $qb->join('rf.history', 'rfh')
                    ->andWhere($qb->expr()->lte('rfh.completionRateLastUpdate', ':lastUpdatedCompletionRate'))
                    ->andWhere($qb->expr()->isNotNull('rfh.completionRateLastUpdate'))
                    ->setParameter('lastUpdatedCompletionRate', (new DateTime())->modify('-' . $parameters['daysSinceLastUpdatedCompletionRate'] . 'days'));
            }
        } else {
            $qb->join('rf.history', 'rfh');
            $filterOnStateDate = 'rfh.' . $parameters['filterOnStateDate'];
            if (!empty($parameters['daysSinceLastUpdatedCompletionRate'])) {
                $qb->andWhere($qb->expr()->lte('rfh.completionRateLastUpdate', ':lastUpdatedCompletionRate'))
                    ->andWhere($qb->expr()->isNotNull('rfh.completionRateLastUpdate'))
                    ->setParameter('lastUpdatedCompletionRate', (new DateTime())->modify('-' . $parameters['daysSinceLastUpdatedCompletionRate'] . 'days'));
            }
        }
        if (!empty($parameters['since'])) {
            $qb->andWhere($qb->expr()->gte($filterOnStateDate, ':since'))
                ->setParameter('since', $parameters['since']);
        }
        if (!empty($parameters['until'])) {
            $qb->andWhere($qb->expr()->lte($filterOnStateDate, ':until'))
                ->setParameter('until', $parameters['until']);
        }
        if (!empty($parameters['tags'])) {
            $tags = explode(",", $parameters['tags']);
            $orX = $qb->expr()->orX();
            for ($i = 0; $i < sizeof($tags); $i++) {
                $orX->add($qb->expr()->orX($qb->expr()->eq('tags.name', ':tags' . $i)));
                $qb->setParameter('tags' . $i, $tags[$i]);
            }
            $qb->andWhere($orX);
            $joinTags = true;
        }
        if (!empty($parameters['query'])) {
            $qb->join('rf.attendee', 'att')
                ->andWhere($qb->expr()->orX(
                    $qb->expr()->like('att.email', ':query'),
                    $qb->expr()->like('att.lastName', ':query'),
                    $qb->expr()->like('att.firstName', ':query'),
                    $qb->expr()->like('att.phoneNumber', ':query'),
                    $qb->expr()->like("CONCAT(att . firstName, ' ', att . lastName)", ':query'),
                    $qb->expr()->like('rf.externalId', ':query'),
                    $qb->expr()->like('rf.notes', ':query'),
                    $qb->expr()->like('tags.name', ':query')
                ))
                ->setParameter('query', '%' . trim($parameters['query']) . '%');
            $joinTags = true;
        }
        if (in_array($parameters['sort'], ['lastName', 'firstName'])) {
            if (empty($parameters['query'])) { // jointure seulement si elle n'est pas déjà faite par le parametre 'query'
                $qb->join('rf.attendee', 'att');
            }
            $qb->orderBy('att.' . $parameters['sort'], $parameters['order']);
        } else {
            $qb->orderBy('rf.' . $parameters['sort'], $parameters['order']);
        }
        $qb->addOrderBy('rf.id', 'ASC');

        if ($joinMessageTemplate) {
            $qb->join(Message::class, 'message', Join::WITH, $qb->expr()->andX(
                $qb->expr()->eq('message.entityClass', ':registrationFolderClassName'),
                $qb->expr()->eq('message.entityId', 'rf.externalId')
            ))
                ->setParameter('registrationFolderClassName', RegistrationFolder::CLASSNAME);
            $qb->join('message.messageTemplate', 'messageTemplate');
        }
        if ($joinTags) {
            $qb->leftJoin('rf.tags', 'tags');
        }
        return $qb;
    }

    /**
     * @param Organism $organism
     * @param array $parameters
     * @param array $columnIds
     * @param bool $isAllowAnalytics
     * @return ArrayCollection
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function findAllByColumn(Organism $organism, array $parameters, array $columnIds, bool $isAllowAnalytics): ArrayCollection
    {
        $columns = [];
        foreach ($columnIds as $columnId) {
            $columnParameters = $parameters; // Affectation copies the array - We use a copy to avoid overriding $parameters
            $columnParameters['columnId'] = $columnId;
            $qb = $this->findAllReturnQueryBuilder($organism, $columnParameters);
            $aliases = $qb->getAllAliases();
            if (in_array('t', $aliases)) {
                $qb
                    ->addSelect('se3')
                    ->addSelect('ta2');
            } elseif (in_array('ta1', $aliases)) {
                $qb
                    ->addSelect('se2')
                    ->addSelect('ta1')
                    ->leftJoin('ta1.training', 't');
            } elseif (in_array('se1', $aliases)) {
                $qb
                    ->addSelect('se1')
                    ->addSelect('ta')
                    ->leftJoin('se1.trainingAction', 'ta')
                    ->leftJoin('ta.training', 't');
            } else {
                $qb
                    ->addSelect('se')
                    ->addSelect('ta')
                    ->leftJoin('rf.session', 'se')
                    ->leftJoin('se.trainingAction', 'ta')
                    ->leftJoin('ta.training', 't');
            }
            if (!in_array('rfh', $aliases)) {
                $qb
                    ->leftJoin('rf.history', 'rfh');
            }
            if (!in_array('att', $aliases)) {
                $qb
                    ->leftJoin('rf.attendee', 'att');
            }
            if (!in_array('p', $aliases)) {
                $qb
                    ->leftJoin('rf.proposal', 'p');
            }
            $qb
                ->addSelect('t')
                ->addSelect('rfh')
                ->addSelect('att')
                ->addSelect('p');
            $items = (clone $qb)->distinct()->setMaxResults($columnParameters['limit'])->getQuery()->getResult();
            $total = (int)(clone $qb)->select($qb->expr()->count('DISTINCT(rf.id)'))->getQuery()->getSingleScalarResult();

            // For revenue, we need to compute the parameters here even if it is also done in findAllReturnQueryBuilder
            if (in_array(RegistrationFolderStates::ALL()->getValue(), $columnParameters['state'])) {
                $columnParameters['state'] = RegistrationFolderStates::valuesStatesToString(); // Hack because of "all" fake state
            }
            if (in_array(RegistrationFolderBillingStates::ALL()->getValue(), $parameters['billingState'])) {
                $columnParameters['billingState'] = RegistrationFolderBillingStates::valuesStatesToString(); // Hack because of "all" fake state
            }
            $mergedParameters = Tools::computeKanbanColumnParameters($columnParameters, $this->listColumnConfigs(), $columnId);
            $states = $mergedParameters['state'];
            $revenue = $this->getRevenueByStates(clone $qb, $states, $isAllowAnalytics);

            $columns[] = [
                'columnId' => $columnId,
                'items' => $items,
                'total' => $total,
                'revenue' => $revenue
            ];
        }

        return new ArrayCollection(['columns' => $columns]);
    }

    /**
     * @param Organism|null $organism
     * @param DataProviders $dataProvider
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countByOrganismAndDataProvider(Organism $organism, DataProviders $dataProvider): int
    {
        $qb = $this->createQueryBuilder('rf');
        $qb->select('count(rf.id)')
            ->where($qb->expr()->eq('rf.organism', ':organism'))
            ->andWhere($qb->expr()->eq('rf.type', ':dataProvider'))
            ->setParameter('organism', $organism)
            ->setParameter('dataProvider', $dataProvider->getValue());
        return $qb->getQuery()->getSingleScalarResult();
    }

    /**
     * @param Session $session
     * @param array|null $states
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countBySessionAndStates(Session $session, array $states = null): int
    {
        $qb = $this->createQueryBuilder('rf');
        $qb->select('count(rf.id)');
        $qb->andWhere($qb->expr()->eq('rf.session', ':session'))
            ->setParameter('session', $session);
        if ($states) {
            $qb->andWhere($qb->expr()->in('rf.state', ':states'))
                ->setParameter('states', $states);
        }
        return $qb->getQuery()->getSingleScalarResult();
    }

    /**
     * @param Organism $organism
     * @param array $states
     * @param string $type
     * @return ArrayCollection
     */
    public function findAllByOrganismAndStatesAndType(Organism $organism, array $states, string $type): ArrayCollection
    {
        $qb = $this->createQueryBuilder('rf');
        $qb->andWhere($qb->expr()->eq('rf.organism', ':organism'))
            ->andWhere($qb->expr()->eq('rf.type', ':type'))
            ->andWhere($qb->expr()->in('rf.state', ':states'))
            ->setParameter('organism', $organism)
            ->setParameter('type', $type)
            ->setParameter('states', $states);
        return new ArrayCollection($qb->getQuery()->getResult());
    }

    /**
     * @param Certification $certification
     * @param Organism $organism
     * @return ArrayCollection
     */
    public function findAllForCertificationFolderCreation(Certification $certification, Organism $organism): ArrayCollection
    {
        $qb = $this->createQueryBuilder('rf');
        $qb->andWhere($qb->expr()->eq('rf.certification', ':certification'))
            ->andWhere($qb->expr()->eq('rf.organism', ':organism'))
            ->andWhere($qb->expr()->in('rf.state', ':state'))
            ->andWhere($qb->expr()->isNull('rf.certificationFolder'))
            ->setParameter('certification', $certification)
            ->setParameter('organism', $organism)
            ->setParameter('state', RegistrationFolderStates::certifierAuthorizedStates());
        return new ArrayCollection($qb->getQuery()->getResult());
    }

    /**
     * @param $object (Training or Organism)
     * @param string $function
     * @param string $field
     * @param string $groupLabel
     * @param DateTime $startDate
     * @param DateTime $endDate
     * @param array $states
     * @param array $billingStates
     * @return int|mixed|string
     */
    public function computeOnFieldGroupByDates($object, string $function, string $field, string $groupLabel, DateTime $startDate, DateTime $endDate, array $states = array(), array $billingStates = array())
    {
        if ($field == 'trainingActionInfo.totalExcl') {
            $field = 'JSON_EXTRACT(rf.rawData, \'$.' . $field . '\')';
        } else {
            $field = 'rf.' . $field;
        }
        $dataPoints = [];
        if (sizeof($states) > 0) {
            foreach ($states as $state) {
                $historyField = 'history.' . $state . 'Date';
                $qb = $this->createQueryBuilder('rf');
                $qb->join('rf.history', 'history')
                    ->select('DATE_FORMAT(' . $historyField . ', \'' . $groupLabel . '\') as label, ' . strtoupper($function) . '(' . $field . ') as value');
                if ($object instanceof Organism) {
                    $qb->where('rf.organism = :organism');
                } else {
                    $qb->join('rf.session', 'se')
                        ->join('se.trainingAction', 'ta')
                        ->where('ta.training = :training');
                }
                $qb->andWhere($qb->expr()->between($historyField, ':startDate', ':endDate'));
                if (!empty($billingStates)) {
                    $qb->andWhere($qb->expr()->in('rf.billingState', ':billingStates'))
                        ->setParameter('billingStates', $billingStates);
                }
                $qb->setParameter('startDate', $startDate)
                    ->setParameter('endDate', $endDate);
                if ($object instanceof Organism) {
                    $qb->setParameter('organism', $object);
                } else {
                    $qb->setParameter('training', $object);
                }
                $newDataPoints = $qb->groupBy('label')
                    ->getQuery()
                    ->getResult(AbstractQuery::HYDRATE_ARRAY);
                // This is very imperative code that looks quite bad but it's very hard to do better due to weird PHP semantics of assignment by value / reference
                foreach ($newDataPoints as $newDataPoint) {
                    $found = false;
                    foreach ($dataPoints as &$dataPoint) {
                        if ($dataPoint['label'] === $newDataPoint["label"]) {
                            $dataPoint['value'] += $newDataPoint['value'];
                            $found = true;
                        }
                    }
                    if (!$found) {
                        array_push($dataPoints, $newDataPoint);
                    }
                }
            }
        } else {
            $qb = $this->createQueryBuilder('rf');
            $qb->select('DATE_FORMAT(rf.createdOn, \'' . $groupLabel . '\') as label, ' . strtoupper($function) . '(' . $field . ') as value');
            if ($object instanceof Organism) {
                $qb->where('rf.organism = :organism');
            } else {
                $qb->join('rf.session', 'se')
                    ->join('se.trainingAction', 'ta')
                    ->where('ta.training = :training');
            }
            $qb->andWhere('rf.createdOn >= :startDate')
                ->andWhere('rf.createdOn <= :endDate');
            $qb->setParameter('startDate', $startDate)
                ->setParameter('endDate', $endDate);
            if ($object instanceof Organism) {
                $qb->setParameter('organism', $object);
            } else {
                $qb->setParameter('training', $object);
            }

            $dataPoints = $qb->groupBy('label')
                ->getQuery()
                ->getResult(AbstractQuery::HYDRATE_ARRAY);
        }

        return $dataPoints;
    }

    /**
     * @param $object (Training or Organism)
     * @param string $function
     * @param string $field
     * @param DateTime $startDate
     * @param DateTime $endDate
     * @param array $states
     * @param array $billingStates
     * @return int|mixed|string
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function computeOnFieldBetweenDates($object, string $function, string $field, DateTime $startDate, DateTime $endDate, array $states = array(), array $billingStates = array())
    {
        if ($field == 'trainingActionInfo.totalExcl') {
            $field = 'JSON_EXTRACT(rf.rawData, \'$.' . $field . '\')';
        } else {
            $field = 'rf.' . $field;
        }
        if (sizeof($states) > 0) {
            $result = 0;
            foreach ($states as $state) {
                $historyField = 'history.' . $state . 'Date';
                $qb = $this->createQueryBuilder('rf');
                $qb->join('rf.history', 'history')
                    ->select('COALESCE(' . (strtoupper($function)) . '(' . $field . '),0) as value');
                if ($object instanceof Organism) {
                    $qb->where('rf.organism = :organism');
                } else {
                    $qb->join('rf.session', 'se')
                        ->join('se.trainingAction', 'ta')
                        ->where('ta.training = :training');
                }

                $qb->andWhere($qb->expr()->between($historyField, ':startDate', ':endDate'));

                if (!empty($billingStates)) {
                    $qb->andWhere($qb->expr()->in('rf.billingState', ':billingStates'))
                        ->setParameter('billingStates', $billingStates);
                }
                $qb->setParameter('startDate', $startDate)
                    ->setParameter('endDate', $endDate);
                if ($object instanceof Organism) {
                    $qb->setParameter('organism', $object);
                } else {
                    $qb->setParameter('training', $object);
                }
                $result += $qb->getQuery()
                    ->getSingleScalarResult();
            }
        } else {
            $qb = $this->createQueryBuilder('rf');
            $qb->select('COALESCE(' . (strtoupper($function)) . '(' . $field . '),0) as value');
            if ($object instanceof Organism) {
                $qb->where('rf.organism = :organism');
            } else {
                $qb->join('rf.session', 'se')
                    ->join('se.trainingAction', 'ta')
                    ->where('ta.training = :training');
            }
            $qb->andWhere('rf.createdOn >= :startDate')
                ->andWhere('rf.createdOn <= :endDate');
            $qb->setParameter('startDate', $startDate)
                ->setParameter('endDate', $endDate);
            if ($object instanceof Organism) {
                $qb->setParameter('organism', $object);
            } else {
                $qb->setParameter('training', $object);
            }
            $result = $qb->getQuery()
                ->getSingleScalarResult();
        }
        return $result;
    }

    /**
     * @return RegistrationFolder
     * @throws NonUniqueResultException
     */
    public function findLastUpdatedNotProcessedCPF(): ?RegistrationFolder
    {
        $qb = $this->createQueryBuilder('rf');
        $qb->where($qb->expr()->eq('rf.state', ':state'))
            ->andWhere($qb->expr()->eq('rf.type', ':type'))
            ->setParameter('state', 'notProcessed')
            ->setParameter('type', 'cpf');
        $qb->orderBy('rf.lastUpdate', 'DESC')
            ->setMaxResults(1);

        return $qb->getQuery()->getOneOrNullResult();
    }

    /**
     * @param QueryBuilder $qb
     * @param array $states
     * @param bool $isAllowAnalytics
     * @return float
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function getRevenueByStates(QueryBuilder $qb, array $states, bool $isAllowAnalytics): float
    {
        // TODO KANBAN better manage multiple states
        if (!$isAllowAnalytics) {
            $revenueByState = 0;
        } else if (count(array_intersect($states, [RegistrationFolderStates::SERVICE_DONE_VALIDATED()->getValue(), RegistrationFolderStates::CANCELED_BY_ATTENDEE_NOT_REALIZED()->getValue(), RegistrationFolderStates::CANCELED_BY_ORGANISM()->getValue()]))) {
            $revenueByState = $qb
                    ->select('SUM(rf.amountToInvoice - rf.amountVatToInvoice)')
                    ->andWhere($qb->expr()->isNotNull('rf.amountToInvoice'))
                    ->andWhere($qb->expr()->isNotNull('rf.amountVatToInvoice'))
                    ->getQuery()->getSingleScalarResult() ?? 0;
        } else {
            $revenueByState = $qb
                    ->select("SUM(CAST(JSON_EXTRACT(rf . rawData, '$.trainingActionInfo.totalExcl') as DECIMAL(10, 2)))")
                    ->andWhere($qb->expr()->neq("JSON_EXTRACT(rf . rawData, '$.trainingActionInfo.totalExcl')", ':null'))
                    ->setParameter('null', 'null')
                    ->getQuery()->getSingleScalarResult() ?? 0;
        }
        return $revenueByState;
    }

    /**
     * @param Organism $organism
     * @param DataProviders $dataProvider
     * @return array
     */
    public function findAllExternalIdsForOrganismAndDataProvider(Organism $organism, DataProviders $dataProvider): array
    {
        $qb = $this->createQueryBuilder('rf');
        $qb->select('rf.externalId')
            ->andWhere($qb->expr()->eq('rf.type', ':type'))
            ->andWhere($qb->expr()->eq('rf.organism', ':organism'))
            ->setParameter('type', $dataProvider->getValue())
            ->setParameter('organism', $organism);

        return array_column($qb->getQuery()->getResult(), 'externalId');
    }

    /**
     * @param Attendee $attendee
     * @param Organism $organism
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countForAttendeeAndOrganism(Attendee $attendee, Organism $organism): int
    {
        $qb = $this->createQueryBuilder('rf');
        $qb->select($qb->expr()->count('rf.id'))
            ->where($qb->expr()->eq('rf.organism', ':organism'))
            ->andWhere($qb->expr()->eq('rf.attendee', ':attendee'))
            ->setParameter('organism', $organism)
            ->setParameter('attendee', $attendee);

        return $qb->getQuery()->getSingleScalarResult();
    }

    /**
     * @param Attendee $attendee
     * @param Session $session
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countForAttendeeAndSession(Attendee $attendee, Session $session): int
    {
        $qb = $this->createQueryBuilder('rf');
        $qb->select($qb->expr()->count('rf.id'))
            ->where($qb->expr()->eq('rf.session', ':session'))
            ->andWhere($qb->expr()->eq('rf.attendee', ':attendee'))
            ->setParameter('session', $session)
            ->setParameter('attendee', $attendee);

        return $qb->getQuery()->getSingleScalarResult();
    }

    /**
     * @param Organism $organism
     * @param string $query
     * @return array
     * @throws ExceptionAlias
     * @throws \Doctrine\DBAL\Exception
     */
    public function extractMetadataKeys(Organism $organism, string $query = ''): array
    {
        $conn = $this->getEntityManager()->getConnection();
        $sql = "SELECT DISTINCT JSON_UNQUOTE(json_key) as metadata FROM registration_folder, json_table( json_keys(metadata), '$[*]' COLUMNS(json_key JSON PATH '$') ) t WHERE registration_folder.metadata is not null AND registration_folder.organism_id = :organism_id";
        if (!empty($query)) {
            $sql .= " AND JSON_UNQUOTE(json_key) LIKE :query";
        }
        $stmt = $conn->prepare($sql);
        $stmt->bindValue('organism_id', $organism->getId());
        if (!empty($query)) {
            $stmt->bindValue('query', '%' . $query . '%');
        }
        $result = $stmt->executeQuery();
        $metadata = [];
        foreach ($result->fetchAllAssociative() as $row) {
            $metadata[] = $row['metadata'];
        }
        return $metadata;
    }

    /**
     * @return array
     */
    public function listColumnConfigs(): array
    {
        return [
            [
                'columnId' => RegistrationFolderStates::NOT_PROCESSED()->getValue(),
                'filter' => ['state' => [RegistrationFolderStates::NOT_PROCESSED()->getValue()]],
                'title' => RegistrationFolderStates::toFrString(RegistrationFolderStates::NOT_PROCESSED()->getValue())
            ],
            [
                'columnId' => 'allValidated',
                'filter' => [
                    'state' => [
                        RegistrationFolderStates::VALIDATED()->getValue(),
                        RegistrationFolderStates::WAITING_ACCEPTATION()->getValue()
                    ]
                ],
                'title' => "En attente d'acceptation",
            ],
            [
                'parentColumnId' => 'allValidated',
                'columnId' => RegistrationFolderStates::VALIDATED()->getValue(),
                'filter' => ['state' => [RegistrationFolderStates::VALIDATED()->getValue()]],
                'title' => "Apprenant"
            ],
            [
                'parentColumnId' => 'allValidated',
                'columnId' => RegistrationFolderStates::WAITING_ACCEPTATION()->getValue(),
                'filter' => ['state' => [RegistrationFolderStates::WAITING_ACCEPTATION()->getValue()]],
                'title' => "Financeur"
            ],
            [
                'columnId' => RegistrationFolderStates::ACCEPTED()->getValue(),
                'filter' => ['state' => [RegistrationFolderStates::ACCEPTED()->getValue()]],
                'title' => RegistrationFolderStates::toFrString(RegistrationFolderStates::ACCEPTED()->getValue())
            ],
            [
                'columnId' => RegistrationFolderStates::IN_TRAINING()->getValue(),
                'filter' => ['state' => [RegistrationFolderStates::IN_TRAINING()->getValue()]],
                'title' => RegistrationFolderStates::toFrString(RegistrationFolderStates::IN_TRAINING()->getValue())
            ],
            [
                'parentColumnId' => RegistrationFolderStates::IN_TRAINING()->getValue(),
                'columnId' => RegistrationFolderStates::IN_TRAINING()->getValue() . 'since3days',
                'filter' => ['state' => [RegistrationFolderStates::IN_TRAINING()->getValue()], 'daysSinceLastUpdatedCompletionRate' => '3'],

                'title' => 'Avanc. > 3j'
            ],
            [
                'parentColumnId' => RegistrationFolderStates::IN_TRAINING()->getValue(),
                'columnId' => RegistrationFolderStates::IN_TRAINING()->getValue() . '25',
                'filter' => ['state' => [RegistrationFolderStates::IN_TRAINING()->getValue()], 'completionRate' => '25<'],
                'title' => "Avanc. < 25%"
            ],
            [
                'parentColumnId' => RegistrationFolderStates::IN_TRAINING()->getValue(),
                'columnId' => RegistrationFolderStates::IN_TRAINING()->getValue() . '25<>80',
                'filter' => ['state' => [RegistrationFolderStates::IN_TRAINING()->getValue()], 'completionRate' => '25<>80'],
                'title' => "25% < Avanc. < 80%"
            ],
            [
                'parentColumnId' => RegistrationFolderStates::IN_TRAINING()->getValue(),
                'columnId' => RegistrationFolderStates::IN_TRAINING()->getValue() . '>80',
                'filter' => ['state' => [RegistrationFolderStates::IN_TRAINING()->getValue()], 'completionRate' => '>80'],
                'title' => "Avanc. > 80%"
            ],
            [
                'columnId' => RegistrationFolderStates::TERMINATED()->getValue(),
                'filter' => ['state' => [RegistrationFolderStates::TERMINATED()->getValue()]],
                'title' => RegistrationFolderStates::toFrString(RegistrationFolderStates::TERMINATED()->getValue())
            ],
            [
                'columnId' => RegistrationFolderStates::SERVICE_DONE_DECLARED()->getValue(),
                'filter' => ['state' => [RegistrationFolderStates::SERVICE_DONE_DECLARED()->getValue()]],
                'title' => RegistrationFolderStates::toFrString(RegistrationFolderStates::SERVICE_DONE_DECLARED()->getValue())
            ],
            [
                'columnId' => RegistrationFolderStates::SERVICE_DONE_VALIDATED()->getValue(),
                'filter' => ['state' => [RegistrationFolderStates::SERVICE_DONE_VALIDATED()->getValue()]],
                'title' => RegistrationFolderStates::toFrString(RegistrationFolderStates::SERVICE_DONE_VALIDATED()->getValue())
            ],
            [
                'parentColumnId' => RegistrationFolderStates::SERVICE_DONE_VALIDATED()->getValue(),
                'columnId' => RegistrationFolderStates::SERVICE_DONE_VALIDATED()->getValue() . RegistrationFolderBillingStates::TO_BILL()->getValue(),
                'filter' => [
                    'state' => [RegistrationFolderStates::SERVICE_DONE_VALIDATED()->getValue()],
                    'billingState' => [RegistrationFolderBillingStates::TO_BILL()->getValue()]
                ],
                'title' => RegistrationFolderBillingStates::toFrString(RegistrationFolderBillingStates::TO_BILL()->getValue())
            ],
            [
                'parentColumnId' => RegistrationFolderStates::SERVICE_DONE_VALIDATED()->getValue(),
                'columnId' => RegistrationFolderStates::SERVICE_DONE_VALIDATED()->getValue() . RegistrationFolderBillingStates::BILLED()->getValue(),
                'filter' => [
                    'state' => [RegistrationFolderStates::SERVICE_DONE_VALIDATED()->getValue()],
                    'billingState' => [RegistrationFolderBillingStates::BILLED()->getValue()]
                ],
                'title' => RegistrationFolderBillingStates::toFrString(RegistrationFolderBillingStates::BILLED()->getValue())
            ],
            [
                'parentColumnId' => RegistrationFolderStates::SERVICE_DONE_VALIDATED()->getValue(),
                'columnId' => RegistrationFolderStates::SERVICE_DONE_VALIDATED()->getValue() . RegistrationFolderBillingStates::PAID()->getValue(),
                'filter' => [
                    'state' => [RegistrationFolderStates::SERVICE_DONE_VALIDATED()->getValue()],
                    'billingState' => [RegistrationFolderBillingStates::PAID()->getValue()]
                ],
                'title' => RegistrationFolderBillingStates::toFrString(RegistrationFolderBillingStates::PAID()->getValue())
            ],
            [
                'columnId' => 'allCanceled',
                'filter' => [
                    'state' => [
                        RegistrationFolderStates::CANCELED_BY_ATTENDEE()->getValue(),
                        RegistrationFolderStates::CANCELED_BY_ORGANISM()->getValue(),
                        RegistrationFolderStates::CANCELED_BY_FINANCER()->getValue(),
                        RegistrationFolderStates::REJECTED()->getValue(),
                        RegistrationFolderStates::REJECTED_WITHOUT_CDC_SUITE()->getValue(),
                        RegistrationFolderStates::REJECTED_WITHOUT_TITULAIRE_SUITE()->getValue(),
                        RegistrationFolderStates::CANCELED_BY_ATTENDEE_NOT_REALIZED()->getValue()
                    ]
                ],
                'title' => "Annulé",
            ],
            [
                'parentColumnId' => 'allCanceled',
                'columnId' => RegistrationFolderStates::CANCELED_BY_ATTENDEE()->getValue(),
                'filter' => ['state' => [RegistrationFolderStates::CANCELED_BY_ATTENDEE()->getValue()]],
                'title' => "Apprenant"
            ],
            [
                'parentColumnId' => 'allCanceled',
                'columnId' => RegistrationFolderStates::CANCELED_BY_ORGANISM()->getValue(),
                'filter' => ['state' => [RegistrationFolderStates::CANCELED_BY_ORGANISM()->getValue()]],
                'title' => "Organisme"
            ],
            [
                'parentColumnId' => 'allCanceled',
                'columnId' => RegistrationFolderStates::CANCELED_BY_FINANCER()->getValue(),
                'filter' => ['state' => [RegistrationFolderStates::CANCELED_BY_FINANCER()->getValue()]],
                'title' => "Financeur"
            ],
            [
                'parentColumnId' => 'allCanceled',
                'columnId' => RegistrationFolderStates::REJECTED_WITHOUT_TITULAIRE_SUITE()->getValue(),
                'filter' => [
                    'state' => [
                        RegistrationFolderStates::REJECTED_WITHOUT_TITULAIRE_SUITE()->getValue(),
                        RegistrationFolderStates::REJECTED()->getValue(),
                        RegistrationFolderStates::REJECTED_WITHOUT_CDC_SUITE()->getValue()
                    ]
                ], // TODO this is a hack to make "rejected" appear in the Kanban, pending story 1067 that will delete this state
                'title' => "Annulé sans suite"
            ],
            [
                'parentColumnId' => 'allCanceled',
                'columnId' => RegistrationFolderStates::CANCELED_BY_ATTENDEE_NOT_REALIZED()->getValue(),
                'filter' => ['state' => [RegistrationFolderStates::CANCELED_BY_ATTENDEE_NOT_REALIZED()->getValue()]],
                'title' => "Apprenant non réalisé"
            ],
            [
                'parentColumnId' => 'allCanceled',
                'columnId' => RegistrationFolderStates::CANCELED_BY_ATTENDEE()->getValue() .
                    RegistrationFolderStates::CANCELED_BY_ORGANISM()->getValue() .
                    RegistrationFolderStates::CANCELED_BY_FINANCER()->getValue() .
                    RegistrationFolderStates::REJECTED_WITHOUT_TITULAIRE_SUITE()->getValue() .
                    RegistrationFolderStates::CANCELED_BY_ATTENDEE_NOT_REALIZED()->getValue() .
                    RegistrationFolderBillingStates::TO_BILL()->getValue(),
                'filter' => [
                    'state' => [
                        RegistrationFolderStates::CANCELED_BY_ATTENDEE()->getValue(),
                        RegistrationFolderStates::CANCELED_BY_ORGANISM()->getValue(),
                        RegistrationFolderStates::CANCELED_BY_FINANCER()->getValue(),
                        RegistrationFolderStates::REJECTED_WITHOUT_TITULAIRE_SUITE()->getValue(),
                        RegistrationFolderStates::CANCELED_BY_ATTENDEE_NOT_REALIZED()->getValue()
                    ],
                    'billingState' => [RegistrationFolderBillingStates::TO_BILL()->getValue()]
                ],
                'title' => RegistrationFolderBillingStates::toFrString(RegistrationFolderBillingStates::TO_BILL()->getValue())
            ],
            [
                'parentColumnId' => 'allCanceled',
                'columnId' => RegistrationFolderStates::CANCELED_BY_ATTENDEE()->getValue() .
                    RegistrationFolderStates::CANCELED_BY_ORGANISM()->getValue() .
                    RegistrationFolderStates::CANCELED_BY_FINANCER()->getValue() .
                    RegistrationFolderStates::REJECTED_WITHOUT_TITULAIRE_SUITE()->getValue() .
                    RegistrationFolderStates::CANCELED_BY_ATTENDEE_NOT_REALIZED()->getValue() .
                    RegistrationFolderBillingStates::BILLED()->getValue(),
                'filter' => [
                    'state' => [
                        RegistrationFolderStates::CANCELED_BY_ATTENDEE()->getValue(),
                        RegistrationFolderStates::CANCELED_BY_ORGANISM()->getValue(),
                        RegistrationFolderStates::CANCELED_BY_FINANCER()->getValue(),
                        RegistrationFolderStates::REJECTED_WITHOUT_TITULAIRE_SUITE()->getValue(),
                        RegistrationFolderStates::CANCELED_BY_ATTENDEE_NOT_REALIZED()->getValue()
                    ],
                    'billingState' => [RegistrationFolderBillingStates::BILLED()->getValue()]
                ],
                'title' => RegistrationFolderBillingStates::toFrString(RegistrationFolderBillingStates::BILLED()->getValue())
            ],
            [
                'parentColumnId' => 'allCanceled',
                'columnId' => RegistrationFolderStates::CANCELED_BY_ATTENDEE()->getValue() .
                    RegistrationFolderStates::CANCELED_BY_ORGANISM()->getValue() .
                    RegistrationFolderStates::CANCELED_BY_FINANCER()->getValue() .
                    RegistrationFolderStates::REJECTED_WITHOUT_TITULAIRE_SUITE()->getValue() .
                    RegistrationFolderStates::CANCELED_BY_ATTENDEE_NOT_REALIZED()->getValue() .
                    RegistrationFolderBillingStates::PAID()->getValue(),
                'filter' => [
                    'state' => [
                        RegistrationFolderStates::CANCELED_BY_ATTENDEE()->getValue(),
                        RegistrationFolderStates::CANCELED_BY_ORGANISM()->getValue(),
                        RegistrationFolderStates::CANCELED_BY_FINANCER()->getValue(),
                        RegistrationFolderStates::REJECTED_WITHOUT_TITULAIRE_SUITE()->getValue(),
                        RegistrationFolderStates::CANCELED_BY_ATTENDEE_NOT_REALIZED()->getValue()
                    ],
                    'billingState' => [RegistrationFolderBillingStates::PAID()->getValue()]
                ],
                'title' => RegistrationFolderBillingStates::toFrString(RegistrationFolderBillingStates::PAID()->getValue())
            ],
            [
                'columnId' => 'allRefused',
                'filter' => [
                    'state' => [
                        RegistrationFolderStates::REFUSED_BY_ATTENDEE()->getValue(),
                        RegistrationFolderStates::REFUSED_BY_ORGANISM()->getValue(),
                        RegistrationFolderStates::REFUSED_BY_FINANCER()->getValue()
                    ]
                ],
                'title' => 'Refusé',
            ],
            [
                'parentColumnId' => 'allRefused',
                'columnId' => RegistrationFolderStates::REFUSED_BY_ATTENDEE()->getValue(),
                'filter' => ['state' => [RegistrationFolderStates::REFUSED_BY_ATTENDEE()->getValue()]],
                'title' => 'Apprenant',
            ],
            [
                'parentColumnId' => 'allRefused',
                'columnId' => RegistrationFolderStates::REFUSED_BY_ORGANISM()->getValue(),
                'filter' => ['state' => [RegistrationFolderStates::REFUSED_BY_ORGANISM()->getValue()]],
                'title' => 'Organisme',
            ],
            [
                'parentColumnId' => 'allRefused',
                'columnId' => RegistrationFolderStates::REFUSED_BY_FINANCER()->getValue(),
                'filter' => ['state' => [RegistrationFolderStates::REFUSED_BY_FINANCER()->getValue()]],
                'title' => 'Financeur',
            ],
        ];
    }
}
