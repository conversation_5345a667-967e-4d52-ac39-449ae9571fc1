<?php

namespace App\Repository;

use App\Entity\Certification;
use App\Entity\CertificationPartner;
use App\Entity\CertifierAccess;
use App\Entity\Organism;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;
use LogicException;
use Throwable;

/**
 * @method CertifierAccess|null find($id, $lockMode = null, $lockVersion = null)
 * @method CertifierAccess|null findOneBy(array $criteria, array $orderBy = null)
 * @method CertifierAccess[]    findAll()
 * @method CertifierAccess[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class CertifierAccessRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, CertifierAccess::class);
    }

    /**
     * @param CertifierAccess $certifierAccess
     * @return CertifierAccess
     * @throws Throwable
     */
    public function save(CertifierAccess $certifierAccess): CertifierAccess
    {
        if (!$this->_em->contains($certifierAccess)) {
            $this->_em->persist($certifierAccess);
        }
        $this->_em->flush();
        return $certifierAccess;
    }

    /**
     * @param Organism $organism
     * @param array $parameters
     * @return QueryBuilder
     */
    public function findAllReturnQueryBuilder(Organism $organism, array $parameters): QueryBuilder
    {
        $qb = $this->createQueryBuilder('ca');

        if (isset($parameters['accessType'])) {
            switch ($parameters['accessType']) {
                case "partner" :
                    $qb->where($qb->expr()->eq('ca.partner', ':partner'))
                        ->setParameter('partner', $organism);
                    break;
                case "certifier" :
                    $qb->where($qb->expr()->eq('ca.certifier', ':certifier'))
                        ->setParameter('certifier', $organism);
                    break;
                case "all" :
                    $qb->where($qb->expr()->eq('ca.partner', ':partner'))
                        ->orWhere($qb->expr()->eq('ca.certifier', ':certifier'))
                        ->setParameter('partner', $organism)
                        ->setParameter('certifier', $organism);
                    break;
            }
        }
        if (isset($parameters['accessState'])) {
            switch ($parameters['accessState']) {
                case 'all':
                    break;
                case 'waiting':
                    $qb->andWhere($qb->expr()->andX($qb->expr()->isNull('ca.activatedOn'), $qb->expr()->isNull('ca.terminatedOn')));
                    break;
                case 'accepted':
                    $qb->andWhere($qb->expr()->andX($qb->expr()->isNotNull('ca.activatedOn'), $qb->expr()->isNull('ca.terminatedOn')));
                    break;
                case 'refused':
                    $qb->andWhere($qb->expr()->andX($qb->expr()->isNull('ca.activatedOn'), $qb->expr()->isNotNull('ca.terminatedOn')));
                    break;
                case 'terminated':
                    $qb->andWhere($qb->expr()->andX($qb->expr()->isNotNull('ca.activatedOn'), $qb->expr()->isNotNull('ca.terminatedOn')));
                    break;
                default:
                    throw new LogicException("Unexpected accessState !");
            }
        }
        if (isset($parameters['certifInfo'])) {
            $subQB = $this->_em->createQueryBuilder();
            $subQB->select('org')
                ->from(Organism::class, 'org')
                ->leftJoin('org.certifierCertifications', 'cc')
                ->where($subQB->expr()->eq('cc.certifInfo', ':certifInfo'))
                ->setParameter('certifInfo', $parameters['certifInfo']);

            $qb->andWhere($qb->expr()->in('ca.certifier', ':certifiers'))
                ->setParameter('certifiers', $subQB->getQuery()->getResult());
        }
        if (isset($parameters['query'])) {
            $qb->join('ca.certifier', 'oc')
                ->join('ca.partner', 'op')
                ->andWhere($qb->expr()->orX(
                    $qb->expr()->like('op.siret', ':query'),
                    $qb->expr()->like('oc.siret', ':query'),
                    $qb->expr()->like('op.name', ':query'),
                    $qb->expr()->like('oc.name', ':query')
                ))
                ->setParameter('query', '%' . trim($parameters['query']) . '%');
        }
        $qb->orderBy('ca.' . $parameters['sort'], $parameters['order']);
        $qb->addOrderBy('ca.id', 'ASC');
        return $qb;
    }

    /**
     * @param Organism $certifier
     * @param Organism $partner
     * @param string $state
     * @return CertifierAccess|null
     * @throws NonUniqueResultException
     */
    public function findOneByOrganisms(Organism $certifier, Organism $partner, string $state = 'active'): ?CertifierAccess
    {
        $qb = $this->createQueryBuilder('ca');
        $qb->where($qb->expr()->eq('ca.certifier', ':certifier'))
            ->andWhere($qb->expr()->eq('ca.partner', ':partner'))
            ->setParameter('certifier', $certifier)
            ->setParameter('partner', $partner);

        switch ($state) {
            case "active" :
                $qb->andWhere($qb->expr()->andX($qb->expr()->isNotNull('ca.activatedOn'), $qb->expr()->isNull('ca.terminatedOn')));
                break;
            case "inactive" :
                $qb->andWhere($qb->expr()->orX($qb->expr()->isNull('ca.activatedOn'), $qb->expr()->isNotNull('ca.terminatedOn')));
                break;
            default :
                //all or null
                break;
        }

        return $qb->getQuery()->getOneOrNullResult();
    }

    /**
     * @param Certification $certification
     * @param Organism $partner
     * @param string $state
     * @return CertifierAccess|null
     * @throws NonUniqueResultException
     */
    public function findOneByCertificationAndPartner(Certification $certification, Organism $partner, string $state = 'active'): ?CertifierAccess
    {
        $subQB = $this->_em->createQueryBuilder();
        $subQB->select('org')
            ->from(Organism::class, 'org')
            ->leftJoin('org.certifierCertifications', 'cc')
            ->where($subQB->expr()->eq('cc.certifInfo', ':certifInfo'))
            ->setParameter('certifInfo', $certification->getCertifInfo());

        $qb = $this->createQueryBuilder('ca');
        $qb->join(CertificationPartner::class, 'cp', Join::WITH, 'cp.partner = ca.partner')
            ->join('cp.certification', 'c')
            ->where($qb->expr()->eq('ca.partner', ':partner'))
            ->andWhere($qb->expr()->eq('cp.certification', ':certification'))
            ->andWhere($qb->expr()->in('ca.certifier', ':certifiers'))
            ->setParameter('partner', $partner)
            ->setParameter('certification', $certification)
            ->setParameter('certifiers', $subQB->getQuery()->getResult());

        switch ($state) {
            case "active" :
                $qb->andWhere($qb->expr()->andX($qb->expr()->isNotNull('ca.activatedOn'), $qb->expr()->isNull('ca.terminatedOn')));
                break;
            case "inactive" :
                $qb->andWhere($qb->expr()->orX($qb->expr()->isNull('ca.activatedOn'), $qb->expr()->isNotNull('ca.terminatedOn')));
                break;
            case "all" :
                break;
        }

        return $qb->getQuery()->getOneOrNullResult();
    }

    /**
     * @param Organism $certifier
     * @param string $state
     * @return ArrayCollection
     */
    public function findAllForCertifier(Organism $certifier, string $state = 'active'): ArrayCollection
    {
        $qb = $this->createQueryBuilder('ca');
        $qb->where($qb->expr()->eq('ca.certifier', ':certifier'))
            ->setParameter('certifier', $certifier);

        switch ($state) {
            case "active" :
                $qb->andWhere($qb->expr()->andX($qb->expr()->isNotNull('ca.activatedOn'), $qb->expr()->isNull('ca.terminatedOn')));
                break;
            case "inactive" :
                $qb->andWhere($qb->expr()->orX($qb->expr()->isNull('ca.activatedOn'), $qb->expr()->isNotNull('ca.terminatedOn')));
                break;
        }

        return new ArrayCollection($qb->getQuery()->getResult());
    }

    /**
     * @param Organism $partner
     * @param string $state
     * @return ArrayCollection
     */
    public function findAllForPartner(Organism $partner, string $state = 'active'): ArrayCollection
    {
        $qb = $this->createQueryBuilder('ca');
        $qb->where($qb->expr()->eq('ca.partner', ':partner'))
            ->setParameter('partner', $partner);

        switch ($state) {
            case "active" :
                $qb->andWhere($qb->expr()->andX($qb->expr()->isNotNull('ca.activatedOn'), $qb->expr()->isNull('ca.terminatedOn')));
                break;
            case "inactive" :
                $qb->andWhere($qb->expr()->orX($qb->expr()->isNull('ca.activatedOn'), $qb->expr()->isNotNull('ca.terminatedOn')));
                break;
            case "all":
            default:
                break;
        }

        return new ArrayCollection($qb->getQuery()->getResult());
    }

    /**
     * @param Organism $organism
     * @param string $state
     * @return ArrayCollection
     */
    public function findAllForOrganism(Organism $organism, string $state = 'active'): ArrayCollection
    {
        $qb = $this->createQueryBuilder('ca');
        $qb->where($qb->expr()->eq('ca.certifier', ':organism'))
            ->orWhere($qb->expr()->eq('ca.partner', ':organism'))
            ->setParameter('organism', $organism);

        switch ($state) {
            case "active" :
                $qb->andWhere($qb->expr()->andX($qb->expr()->isNotNull('ca.activatedOn'), $qb->expr()->isNull('ca.terminatedOn')));
                break;
            case "inactive" :
                $qb->andWhere($qb->expr()->orX($qb->expr()->isNull('ca.activatedOn'), $qb->expr()->isNotNull('ca.terminatedOn')));
                break;
            case "all":
            default:
                break;
        }

        return new ArrayCollection($qb->getQuery()->getResult());
    }

    /**
     * @param CertifierAccess $certifierAccess
     */
    public function delete(CertifierAccess $certifierAccess)
    {
        $this->_em->remove($certifierAccess);
        $this->_em->flush();
    }

    /**
     * @return QueryBuilder
     */
    public function certifierOrganismSubQuery(): QueryBuilder
    {
        $qb = $this->createQueryBuilder('ca');

        return $qb
            ->where($qb->expr()->eq('ca.certifier', 'certifiers')) // Refers to 'certifiers' from parent query
            ->andWhere($qb->expr()->isNull('ca.terminatedOn'))
            ->andWhere($qb->expr()->eq('ca.partner', ':organism'));
    }
}
