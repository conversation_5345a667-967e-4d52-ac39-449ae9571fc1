<?php

namespace App\Repository;

use App\Entity\CdcFile;
use App\Entity\Connection;
use App\Entity\Organism;
use App\Entity\Subscription;
use App\Library\utils\enums\CdcFileStates;
use App\Library\utils\enums\ConnectionStates;
use App\Library\utils\enums\DataProviders;
use DateTime;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method CdcFile|null find($id, $lockMode = null, $lockVersion = null)
 * @method CdcFile|null findOneBy(array $criteria, array $orderBy = null)
 * @method CdcFile[]    findAll()
 * @method CdcFile[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class CdcFileRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, CdcFile::class);
    }

    /**
     * @param CdcFile $cdcFile
     * @return CdcFile
     */
    public function save(CdcFile $cdcFile): CdcFile
    {
        if (!$this->_em->contains($cdcFile)) {
            $this->_em->persist($cdcFile);
        }
        $this->_em->flush();
        return $cdcFile;
    }

    /**
     * @param CdcFile $cdcFile
     */
    public function delete(CdcFile $cdcFile): void
    {
        $this->_em->remove($cdcFile);
        $this->_em->flush();
    }

    /**
     * @param array $parameters
     * @param Organism $organism
     * @return QueryBuilder
     */
    public function findAllReturnQueryBuilder(array $parameters, Organism $organism): QueryBuilder
    {
        $qb = $this->createQueryBuilder('cdcFile');

        $qb->andWhere($qb->expr()->eq('cdcFile.organism', ':organism'))
            ->setParameter('organism', $organism);

        if (!in_array('all', $parameters['state'])) {
            $qb->andWhere($qb->expr()->in('cdcFile.state', ':state'))
                ->setParameter('state', $parameters['state']);
        }

        if (!empty($parameters['name'])) {
            $qb->andWhere($qb->expr()->eq('cdcFile.name', ':name'))
                ->setParameter('name', $parameters['name']);
        }

        $qb->orderBy('cdcFile.' . $parameters['sort'], $parameters['order']);
        $qb->addOrderBy('cdcFile.id', 'DESC');

        return $qb;
    }

    /**
     * @return ArrayCollection
     */
    public function findAllCdcFilesByWedofWithoutProcessedReceiptFiles(): ArrayCollection
    {
        $qb = $this->createQueryBuilder('cdcFile');
        $qb->innerJoin(Subscription::class, 'sub', Join::WITH, 'sub.organism=cdcFile.organism')
            ->innerJoin(Connection::class, 'connection', Join::WITH, 'connection.organism=cdcFile.organism')
            ->andWhere($qb->expr()->eq('sub.allowCertifierPlus', ':allowCertifierPlus'))
            ->andWhere($qb->expr()->gt('sub.certifierPeriodEndDate', ':date'))
            ->andWhere($qb->expr()->eq('cdcFile.state', ':cdcState'))
            ->andWhere($qb->expr()->eq('cdcFile.generatedAutomatically', ':generatedAutomatically'))
            ->andWhere($qb->expr()->isNull('cdcFile.idTraitement'))
            ->andWhere($qb->expr()->eq('connection.dataProvider', ':connectionDataProvider'))
            ->andWhere($qb->expr()->eq('connection.state', ':connectionState'))
            ->setParameter('allowCertifierPlus', true)
            ->setParameter('date', new DateTime())
            ->setParameter('cdcState', CdcFileStates::EXPORTED())
            ->setParameter('generatedAutomatically', true)
            ->setParameter('connectionDataProvider', DataProviders::CDC_CERTIFIERS())
            ->setParameter('connectionState', ConnectionStates::ACTIVE()->getValue());
        return new ArrayCollection($qb->getQuery()->getResult());
    }
}
