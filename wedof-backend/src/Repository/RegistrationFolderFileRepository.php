<?php

namespace App\Repository;

use App\Entity\Organism;
use App\Entity\RegistrationFolderFile;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method RegistrationFolderFile|null find($id, $lockMode = null, $lockVersion = null)
 * @method RegistrationFolderFile|null findOneBy(array $criteria, array $orderBy = null)
 * @method RegistrationFolderFile[]    findAll()
 * @method RegistrationFolderFile[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class RegistrationFolderFileRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, RegistrationFolderFile::class);
    }


    /**
     * @param RegistrationFolderFile $registrationFolderFile
     * @return RegistrationFolderFile
     */
    public function save(RegistrationFolderFile $registrationFolderFile): RegistrationFolderFile
    {
        if (!$this->_em->contains($registrationFolderFile)) {
            $this->_em->persist($registrationFolderFile);
        }
        $this->_em->flush();
        return $registrationFolderFile;
    }

    /**
     * @param RegistrationFolderFile $registrationFolderFile
     */
    public function delete(RegistrationFolderFile $registrationFolderFile): void
    {
        $this->_em->remove($registrationFolderFile);
        $this->_em->flush();
    }

    /**
     * @param Organism $organism
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function findMaxTypeId(Organism $organism): int
    {
        $qb = $this->createQueryBuilder('rfFile');
        $qb->select('MAX(rfFile.typeId)')
            ->join('rfFile.registrationFolder', 'registrationFolder')
            ->join('registrationFolder.organism', 'organism')
            ->andWhere($qb->expr()->eq('organism', ':organism'))
            ->setParameter('organism', $organism);
        return $qb->getQuery()->getSingleScalarResult() ?? 0;
    }

    /**
     * @param Organism $organism
     * @param int $typeId
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countFilesWithTypeId(Organism $organism, int $typeId): int
    {
        $qb = $this->createQueryBuilder('rfFile');
        $qb->select('count(rfFile.typeId)')
            ->join('rfFile.registrationFolder', 'registrationFolder')
            ->join('registrationFolder.organism', 'organism')
            ->andWhere($qb->expr()->eq('organism', ':organism'))
            ->andWhere($qb->expr()->eq('rfFile.typeId', ':typeId'))
            ->setParameter('organism', $organism)
            ->setParameter('typeId', $typeId);
        return $qb->getQuery()->getSingleScalarResult() ?? 0;
    }
}
