<?php

namespace App\Repository;

use App\Entity\Certification;
use App\Entity\CertificationPartner;
use App\Entity\Organism;
use App\Entity\Training;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\enums\SessionStates;
use App\Library\utils\enums\TrainingActionStates;
use App\Library\utils\enums\TrainingStates;
use DateTime;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;
use Throwable;

/**
 * @method Training|null find($id, $lockMode = null, $lockVersion = null)
 * @method Training|null findOneBy(array $criteria, array $orderBy = null)
 * @method Training[]    findAll()
 * @method Training[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class TrainingRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Training::class);
    }

    /**
     * @param Training $training
     * @return Training
     * @throws Throwable
     */
    public function save(Training $training): Training
    {
        if (!$this->_em->contains($training)) {
            $this->_em->persist($training);
        }
        $this->_em->flush();
        return $training;
    }

    /**
     * @param Organism $organism
     * @param array $parameters
     * @return QueryBuilder
     */
    public function findAllReturnQueryBuilder(Organism $organism, array $parameters): QueryBuilder
    {
        $qb = $this->createQueryBuilder('t');

        if (!empty($parameters['siret'])) {
            $qb->join('t.organism', 'org')
                ->where($qb->expr()->eq('org.siret', ':siret'))
                ->setParameter('siret', $parameters['siret']);
        } else {
            $qb->where($qb->expr()->eq('t.organism', ':organism'))
                ->setParameter('organism', $organism);
        }

        if (!empty($parameters['certifInfo']) || isset($parameters['eligible'])) {
            $qb->join('t.certification', 'cert');
            if (!empty($parameters['certifInfo'])) {
                $qb->andWhere($qb->expr()->eq('cert.certifInfo', ':certifInfo'))
                    ->setParameter('certifInfo', $parameters['certifInfo']);
            }
            if (isset($parameters['eligible'])) {
                $eligible = filter_var($parameters['eligible'], FILTER_VALIDATE_BOOLEAN);
                $now = new DateTime('now');
                if ($eligible) {
                    $qb->andWhere($qb->expr()->gte('cert.cpfDateEnd', ':now'));
                } else {
                    $qb->andWhere($qb->expr()->lt('cert.cpfDateEnd', ':now'));
                }
                $qb->setParameter('now', $now);
            }
        }

        if (isset($parameters['state'])) {
            $states = $parameters['state'];
            if (!is_array($parameters['state'])) {
                $states = explode(',', $parameters['state']); // SHOULD NOT BE HERE
            }
            $qb->andWhere($qb->expr()->in('t.state', ':states'))
                ->setParameter('states', $states);
        }

        if (!empty($parameters['query'])) {
            $qb->andWhere($qb->expr()->orX(
                $qb->expr()->like('t.title', ':query'),
                $qb->expr()->like('LOWER(t.externalId)', ':query') // utf8_bin in prod, which is case sensitive
            ));
            $qb->setParameter('query', '%' . trim(strtolower($parameters['query'])) . '%');
        }

        if (isset($parameters['dataProvider']) && $parameters['dataProvider'] != 'all') {
            $qb->andWhere($qb->expr()->eq('t.dataProvider', ':dataProvider'))
                ->setParameter('dataProvider', $parameters['dataProvider']);
        }

        $qb->orderBy('t.id', $parameters['order'] ?? 'ASC');

        return $qb;
    }

    /**
     * @param Organism $organism
     * @return array
     */
    public function findAllForCpfXml(Organism $organism): array
    {
        $qb = $this->createQueryBuilder('t');
        $qb->where($qb->expr()->eq('t.organism', ':organism'))
            ->setParameter('organism', $organism)
            ->andWhere($qb->expr()->eq('t.dataProvider', ':dataProvider'))
            ->setParameter('dataProvider', DataProviders::CPF()->getValue())
            ->andWhere("JSON_EXTRACT(t.rawData, '$.statusLabel') IN ('ACTIVE', 'DRAFT', 'VALIDATED')")
            ->andWhere($qb->expr()->neq('t.state', ':stateDeleted'))
            ->setParameter('stateDeleted', TrainingStates::DELETED()->getValue())
            // No need to filter actually, as if they fail the rest will succeed
            // ->andWhere("JSON_EXTRACT(t.rawData, '$.stateHabilitation') = 'ACTIF'")
            // ->andWhere("JSON_UNQUOTE(JSON_EXTRACT(t.rawData, '$.certificationQualiopi')) = 'true'") // HACK because true is not a json value
            // ->andWhere("JSON_UNQUOTE(JSON_EXTRACT(t.rawData, '$.eligible')) = 'true'") // HACK because true is not a json value
            ->addOrderBy('t.id', 'ASC');
        return $qb->getQuery()->getResult();
    }

    /**
     * @param string $externalId
     * @return Training|null
     */
    public function findOneByExternalId(string $externalId): ?Training
    {
        return $this->findOneBy(array('externalId' => $externalId));
    }

    /**
     * @param Organism $organism
     * @param array $parameters
     * @return ArrayCollection
     */
    public function findAllWithParams(Organism $organism, array $parameters): ArrayCollection
    {
        return new ArrayCollection($this->findAllReturnQueryBuilder($organism, $parameters)->getQuery()->getResult());
    }

    /**
     * @param Certification $certification
     * @param Organism $organism
     * @param array $states
     * @param string $dataProvider
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countByCertificationAndByOrganismAndStates(Certification $certification, Organism $organism, array $states = [], string $dataProvider = 'cpf'): int
    {
        $qb = $this->createQueryBuilder('t');
        $qb->select($qb->expr()->count('t'));
        $qb->join('t.organism', 'org')
            ->join('t.certification', 'cert')
            ->where($qb->expr()->in('org.id', $organism->getId()))
            ->andWhere($qb->expr()->in('cert.id', $certification->getId()))
            ->andWhere($qb->expr()->eq('t.dataProvider', ':dataProvider'))
            ->setParameter('dataProvider', $dataProvider);
        if ($states) {
            $qb->andWhere($qb->expr()->in('t.state', ':state'))
                ->setParameter('state', $states);
        }
        return $qb->getQuery()->getSingleScalarResult();
    }

    /**
     * @param CertificationPartner $certificationPartner
     * @param array $states
     * @param string $dataProvider
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countByCertificationPartnerAndStates(CertificationPartner $certificationPartner, array $states, string $dataProvider = 'cpf'): int
    {
        return $this->countByCertificationAndByOrganismAndStates($certificationPartner->getCertification(), $certificationPartner->getPartner(), $states, $dataProvider);
    }

    /**
     * @param Organism $organism
     * @param array $certifications
     * @param array $options
     * @return ArrayCollection
     */
    public function findAllPublished(Organism $organism, array $certifications, array $options = []): ArrayCollection
    {
        $qb = $this->createQueryBuilder('training');
        $qb->select("training.externalId, 
                COUNT(DISTINCT trainingActions.id) as nbTrainingActions, 
                COUNT(DISTINCT sessions.id) as nbSessions")
            ->leftJoin('training.trainingActions', 'trainingActions', Join::WITH, $qb->expr()->eq(
                'trainingActions.state', ':trainingActionState'
            ))
            ->leftJoin('trainingActions.sessions', 'sessions', Join::WITH, $qb->expr()->eq(
                'sessions.state', ':sessionState'
            ))
            ->andWhere($qb->expr()->eq('training.state', ':trainingState'))
            ->setParameter('trainingState', TrainingStates::PUBLISHED()->getValue())
            ->setParameter('trainingActionState', TrainingActionStates::PUBLISHED()->getValue())
            ->setParameter('sessionState', SessionStates::PUBLISHED()->getValue())
            ->groupBy('training.id');

        $qb->andWhere($qb->expr()->eq('training.organism', ':organism'))
            ->setParameter('organism', $organism);

        if (!empty($certifications)) {
            $qb->andWhere($qb->expr()->in('training.certification', ':certifications'))
                ->setParameter('certifications', $certifications);
        } else if (isset($options['certificationTypes'])) {
            $qb->join('training.certification', 'certification')
                ->andWhere($qb->expr()->in('certification.type', ':types'))
                ->setParameter('types', $options['certificationTypes']);
        }

        if (isset($options['dataProvider'])) {
            $qb->andWhere($qb->expr()->eq('training.dataProvider', ':dataProvider'))
                ->setParameter('dataProvider', $options['dataProvider']->getValue());
        }

        return new ArrayCollection($qb->getQuery()->getResult());
    }

    /**
     * @param array $externalIds
     * @return ArrayCollection
     */
    public function markAsUnpublishedNotUpdatedTrainings(array $externalIds): ArrayCollection
    {
        $qb = $this->createQueryBuilder('training');
        $qb->select("training.id")
            ->andWhere($qb->expr()->eq('training.state', ':state'))
            ->andWhere($qb->expr()->in('training.externalId', ':externalIds'))
            ->andWhere($qb->expr()->gt('training.lastUpdate', ':oneDayAgo'))
            ->setParameter('state', TrainingStates::PUBLISHED()->getValue())
            ->setParameter('externalIds', $externalIds)
            ->setParameter('oneDayAgo', (new DateTime('now'))->modify('- 1 day'));
        $trainingToMark = new ArrayCollection(array_column($qb->getQuery()->getArrayResult(), 'id'));

        $qbu = $this->createQueryBuilder('training');
        $qbu->update()
            ->set('training.state', ':state')
            ->set('training.lastUpdate', ':lastUpdate')
            ->where($qbu->expr()->in('training.id', ':ids'))
            ->setParameter('state', TrainingStates::UNPUBLISHED()->getValue())
            ->setParameter('lastUpdate', new DateTime('now'))
            ->setParameter('ids', $trainingToMark);
        $qbu->getQuery()->execute();

        return $trainingToMark;
    }

    /**
     * @param array $externalIds
     * @return ArrayCollection
     */
    public function markAsDeletedNotUpdatedTrainings(array $externalIds): ArrayCollection
    {
        $qb = $this->createQueryBuilder('training');
        $qb->select("training.id")
            ->andWhere($qb->expr()->eq('training.state', ':state'))
            ->andWhere($qb->expr()->in('training.externalId', ':externalIds'))
            ->setParameter('state', TrainingStates::PUBLISHED()->getValue())
            ->setParameter('externalIds', $externalIds);
        $trainingToMark = new ArrayCollection(array_column($qb->getQuery()->getArrayResult(), 'id'));

        $qbu = $this->createQueryBuilder('training');
        $qbu->update()
            ->set('training.state', ':state')
            ->set('training.lastUpdate', ':lastUpdate')
            ->where($qbu->expr()->in('training.id', ':ids'))
            ->setParameter('state', TrainingStates::DELETED()->getValue())
            ->setParameter('lastUpdate', new DateTime('now'))
            ->setParameter('ids', $trainingToMark);
        $qbu->getQuery()->execute();

        return $trainingToMark;
    }
}
