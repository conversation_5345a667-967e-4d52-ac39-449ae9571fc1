<?php

namespace App\Repository;

use App\Entity\Certification;
use App\Entity\Evaluation;
use App\Entity\Organism;
use App\Entity\Training;
use DateTime;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\AbstractQuery;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;
use Exception;


/**
 * @method Evaluation|null find($id, $lockMode = null, $lockVersion = null)
 * @method Evaluation|null findOneBy(array $criteria, array $orderBy = null)
 * @method Evaluation[]    findAll()
 * @method Evaluation[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class EvaluationRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Evaluation::class);
    }

    /**
     * @param Evaluation $evaluation
     * @return Evaluation
     * @throws Exception
     */
    public function saveOrUpdate(Evaluation $evaluation): Evaluation
    {
        if (!$this->_em->contains($evaluation)) {
            $this->_em->persist($evaluation);
        }
        $this->_em->flush();
        return $evaluation;
    }

    /**
     * @param Organism $organism
     * @param string $groupLabel
     * @param DateTime $startDate
     * @param DateTime|null $endDate
     * @return array
     */
    public function countEvaluationsByOrganismGroupByDates(Organism $organism, string $groupLabel, DateTime $startDate, DateTime $endDate): array
    {
        $query = $this->createQueryBuilder('eval');
        $query = $query->select('DATE_FORMAT(eval.date, \'' . $groupLabel . '\') as label, eval.reviewNumber as value')
            ->where('eval.organism = :organism')
            ->andWhere('eval.date >= :startDate')
            ->andWhere('eval.date <= :endDate')
            ->andWhere('eval.training is null')
            ->andWhere('eval.trainingAction is null')
            ->setParameter('startDate', $startDate)
            ->setParameter('endDate', $endDate)
            ->setParameter('organism', $organism)
            ->orderBy('eval.date', 'asc')
            ->getQuery();
        return $query->getResult(AbstractQuery::HYDRATE_ARRAY);
    }

    /**
     * @param Organism $organism
     * @param string $groupLabel
     * @param DateTime $startDate
     * @param DateTime|null $endDate
     * @return array
     */
    public function averageEvaluationsByOrganismGroupByDates(Organism $organism, string $groupLabel, DateTime $startDate, DateTime $endDate): array
    {
        $query = $this->createQueryBuilder('eval')
            ->select('DATE_FORMAT(eval.date, \'' . $groupLabel . '\') as label, AVG(eval.averageRating) as value')
            ->where('eval.organism = :organism')
            ->andWhere('eval.date >= :startDate')
            ->andWhere('eval.date <= :endDate')
            ->andWhere('eval.training is null')
            ->andWhere('eval.trainingAction is null')
            ->setParameter('startDate', $startDate)
            ->setParameter('endDate', $endDate)
            ->setParameter('organism', $organism)
            ->groupBy('label')
            ->getQuery();
        return $query->getResult(AbstractQuery::HYDRATE_ARRAY);
    }

    /**
     * @param Organism $organism
     * @param DateTime $startDate
     * @param DateTime|null $endDate
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countEvaluationsByOrganismBetweenDates(Organism $organism, DateTime $startDate, DateTime $endDate): int
    {
        $query = $this->createQueryBuilder('eval')
            ->select('COALESCE(SUM(eval.reviewNumber),0) as value')
            ->where('eval.organism = :organism')
            ->andWhere('eval.date >= :startDate')
            ->andWhere('eval.date <= :endDate')
            ->andWhere('eval.training is null')
            ->andWhere('eval.trainingAction is null')
            ->setParameter('startDate', $startDate)
            ->setParameter('endDate', $endDate)
            ->setParameter('organism', $organism)
            ->getQuery();
        return $query->getSingleScalarResult();
    }

    /**
     * @param Organism $organism
     * @param DateTime $startDate
     * @param DateTime|null $endDate
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function averageEvaluationsByOrganismBetweenDates(Organism $organism, DateTime $startDate, DateTime $endDate): int
    {
        $query = $this->createQueryBuilder('eval')
            ->select('COALESCE(AVG(eval.averageRating)) as value')
            ->where('eval.organism = :organism')
            ->andWhere('eval.date >= :startDate')
            ->andWhere('eval.date <= :endDate')
            ->andWhere('eval.training is null')
            ->andWhere('eval.trainingAction is null')
            ->setParameter('startDate', $startDate)
            ->setParameter('endDate', $endDate)
            ->setParameter('organism', $organism)
            ->getQuery();
        return $query->getSingleScalarResult();
    }


    /**
     * @param Training $training
     * @param string $groupLabel
     * @param DateTime $startDate
     * @param DateTime|null $endDate
     * @return array
     */
    public function countEvaluationsByTrainingGroupByDates(Training $training, string $groupLabel, DateTime $startDate, DateTime $endDate): array
    {
        $query = $this->createQueryBuilder('eval')
            ->select('DATE_FORMAT(eval.date, \'' . $groupLabel . '\') as label, SUM(eval.reviewNumber) as value')
            ->where('eval.training = :training')
            ->andWhere('eval.date >= :startDate')
            ->andWhere('eval.date <= :endDate')
            ->setParameter('startDate', $startDate)
            ->setParameter('endDate', $endDate)
            ->setParameter('training', $training)
            ->groupBy('label')
            ->getQuery();
        return $query->getResult(AbstractQuery::HYDRATE_ARRAY);
    }

    /**
     * @param Training $training
     * @param string $groupLabel
     * @param DateTime $startDate
     * @param DateTime|null $endDate
     * @return array
     */
    public function averageEvaluationsByTrainingGroupByDates(Training $training, string $groupLabel, DateTime $startDate, DateTime $endDate): array
    {
        $query = $this->createQueryBuilder('eval')
            ->select('DATE_FORMAT(eval.date, \'' . $groupLabel . '\') as label, eval.averageRating as value')
            ->where('eval.training = :training')
            ->andWhere('eval.date >= :startDate')
            ->andWhere('eval.date <= :endDate')
            ->setParameter('startDate', $startDate)
            ->setParameter('endDate', $endDate)
            ->setParameter('training', $training)
            ->groupBy('label')
            ->getQuery();
        return $query->getResult(AbstractQuery::HYDRATE_ARRAY);
    }

    /**
     * @param Training $training
     * @param DateTime $startDate
     * @param DateTime|null $endDate
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countEvaluationsByTrainingBetweenDates(Training $training, DateTime $startDate, DateTime $endDate): int
    {
        $query = $this->createQueryBuilder('eval')
            ->select('COALESCE(SUM(eval.reviewNumber),0) as value')
            ->where('eval.training = :training')
            ->andWhere('eval.date >= :startDate')
            ->andWhere('eval.date <= :endDate')
            ->setParameter('startDate', $startDate)
            ->setParameter('endDate', $endDate)
            ->setParameter('training', $training)
            ->getQuery();
        return $query->getSingleScalarResult();
    }

    /**
     * @param Training $training
     * @param DateTime $startDate
     * @param DateTime|null $endDate
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function averageEvaluationsByTrainingBetweenDates(Training $training, DateTime $startDate, DateTime $endDate): int
    {
        $query = $this->createQueryBuilder('eval')
            ->select('AVG(eval.averageRating) as value')
            ->where('eval.training = :training')
            ->andWhere('eval.date >= :startDate')
            ->andWhere('eval.date <= :endDate')
            ->setParameter('startDate', $startDate)
            ->setParameter('endDate', $endDate)
            ->setParameter('training', $training)
            ->getQuery();
        return $query->getSingleScalarResult();
    }

    /**
     * @param Collection $organisms
     * @param array $parameters
     * @return QueryBuilder
     */
    public function findAllReturnQueryBuilder(Collection $organisms, array $parameters): QueryBuilder
    {
        $ids = $organisms->map(function (Organism $organism) {
            return $organism->getId();
        });
        $qb = $this->createQueryBuilder('evaluation');
        $qb->join('evaluation.organism', 'org')
            ->where($qb->expr()->in('org.id', join(",", $ids->toArray())));

        if (isset($parameters['for']) && $parameters['for'] == 'training') {
            $qb->andWhere($qb->expr()->isNotNull('evaluation.training'));
        } else if (isset($parameters['for']) && $parameters['for'] == 'trainingActions') {
            $qb->andWhere($qb->expr()->isNotNull('evaluation.trainingAction'));
        } else if (isset($parameters['for']) && $parameters['for'] == 'organism') {
            $qb->andWhere($qb->expr()->isNull('evaluation.trainingAction'));
            $qb->andWhere($qb->expr()->isNull('evaluation.training'));
        }
        if (isset($parameters['since'])) {
            $qb->andWhere($qb->expr()->gte('evaluation.date', ':since'))
                ->setParameter('since', $parameters['since']);
        }
        if (isset($parameters['date'])) {
            $qb->andWhere($qb->expr()->eq('evaluation.date', ':date'))
                ->setParameter('date', $parameters['date']);
        }
        if (isset($parameters['trainingActionId'])) {
            $ta = $qb->join('evaluation.trainingAction', 'ta');
            $ta->andWhere($ta->expr()->like('ta.externalId', ':trainingActionId'))
                ->setParameter('trainingActionId', $parameters['trainingActionId']);
        }
        if (isset($parameters['order'])) {
            $qb->orderBy('evaluation.date', $parameters['order']);
        }
        $qb->addOrderBy('evaluation.id', 'ASC');
        return $qb;
    }

    /**
     * @param Organism $organism
     * @param Certification $certification
     * @param array $parameters
     * @return ArrayCollection
     */
    public function findAllForOrganismAndCertification(Organism $organism, Certification $certification, array $parameters): ArrayCollection
    {
        $qb = $this->createQueryBuilder('eval');
        $qb->where($qb->expr()->eq('eval.organism', ':organism'))
            ->innerJoin('eval.training', 't')
            ->andWhere($qb->expr()->eq('t.certification', ':certification'))
            ->setParameter('organism', $organism)
            ->setParameter('certification', $certification);

        if (isset($parameters['scope'])) {
            if ($parameters['scope'] == 'latest') {
                $subQb = $this->createQueryBuilder('e');
                $subQbDQL = $subQb->select($subQb->expr()->max('e.date'))
                    ->where($subQb->expr()->eq('e.training', 't'))
                    ->getDQL();

                $qb->andWhere($qb->expr()->eq('eval.date', sprintf('(%s)', $subQbDQL)));
            }
        }

        return new ArrayCollection($qb->getQuery()->getResult());
    }
}
