<?php

namespace App\Repository;

use App\Entity\RegistrationFolderReason;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method RegistrationFolderReason|null find($id, $lockMode = null, $lockVersion = null)
 * @method RegistrationFolderReason|null findOneBy(array $criteria, array $orderBy = null)
 * @method RegistrationFolderReason[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class RegistrationFolderReasonRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, RegistrationFolderReason::class);
    }

    /**
     * @param string $code
     * @param string $category
     * @return RegistrationFolderReason|null
     */
    public function findOneByCodeAndCategory(string $code, string $category): ?RegistrationFolderReason
    {
        return $this->findOneBy(['code' => $code, 'category' => $category]);
    }

    /**
     * @param string $category
     * @param bool $withObsoletes
     * @return ArrayCollection
     */
    public function findAllByCategoryAndObsolescence(string $category, bool $withObsoletes): ArrayCollection
    {
        $qb = $this->createQueryBuilder('rfr');
        $qb->where($qb->expr()->like('rfr.category', ':category'))
            ->setParameter('category', $category);
        if (!$withObsoletes) {
            $qb->andWhere($qb->expr()->like('rfr.obsolescence', ':obsolescence'))
                ->setParameter('obsolescence', false);
        }

        return new ArrayCollection($qb->getQuery()->getResult());
    }

    /**
     * @param string $category
     * @param bool $obsolescence
     * @return array
     */
    public function findCodesByCategoryAndObsolescence(string $category, bool $obsolescence): array
    {
        $qb = $this->createQueryBuilder('rfr');
        $qb->select('rfr.code')
            ->where($qb->expr()->like('rfr.category', ':category'))
            ->andWhere($qb->expr()->like('rfr.obsolescence', ':obsolescence'))
            ->setParameter('category', $category)
            ->setParameter('obsolescence', $obsolescence);
        return array_column($qb->getQuery()->getResult(), 'code');
    }

    /**
     * @param RegistrationFolderReason $reason
     * @return RegistrationFolderReason
     */
    public function save(RegistrationFolderReason $reason): RegistrationFolderReason
    {
        $this->_em->persist($reason);
        $this->_em->flush($reason);
        return ($reason);
    }

    /**
     * @return ArrayCollection
     */
    public function findAll(): ArrayCollection
    {
        return new ArrayCollection(parent::findAll());
    }
}
