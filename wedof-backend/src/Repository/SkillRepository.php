<?php

namespace App\Repository;

use App\Entity\Certification;
use App\Entity\Skill;
use App\Library\utils\enums\CertificationSkillType;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;
use Throwable;

/**
 * @extends ServiceEntityRepository<Skill>
 *
 * @method Skill|null find($id, $lockMode = null, $lockVersion = null)
 * @method Skill|null findOneBy(array $criteria, array $orderBy = null)
 * @method Skill[]    findAll()
 * @method Skill[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class SkillRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Skill::class);
    }

    /**
     * @param Skill $skill
     * @return Skill
     * @throws Throwable
     */
    public function save(Skill $skill): Skill
    {
        if (!$this->_em->contains($skill)) {
            $this->_em->persist($skill);
        }
        $this->_em->flush();
        return $skill;
    }

    /**
     * @param Skill $skill
     */
    public function delete(Skill $skill)
    {
        $this->_em->remove($skill);
        $this->_em->flush();
    }

    /**
     * @param array $parameters
     * @return QueryBuilder
     */
    public function findAllReturnQueryBuilder(array $parameters): QueryBuilder
    {
        $qb = $this->createQueryBuilder('skill');
        if (!empty($parameters['certification'])) {
            $qb->andWhere($qb->expr()->eq('skill.certification', ':certification'))
                ->setParameter('certification', $parameters['certification']);
        }
        if (!empty($parameters['parentSkill'])) {
            $qb->andWhere($qb->expr()->eq('skill.parentSkill', ':parentSkill'))
                ->setParameter('parentSkill', $parameters['parentSkill']);
        }

        $qb->andWhere($qb->expr()->in('skill.type', ':type'))
            ->setParameter('type', $parameters['type']);

        if (!empty($parameters['skillOrder'])) {
            $qb->andWhere($qb->expr()->gt('skill.order', ':order'))
                ->setParameter('order', $parameters['skillOrder']);
        }

        if (isset($parameters['sort']) && isset($parameters['order'])) {
            $qb->orderBy('skill.' . $parameters['sort'], $parameters['order']);
            $qb->addOrderBy('skill.id', 'ASC');
        }
        return $qb;
    }

    /**
     * @param array $parameters
     * @return ArrayCollection
     */
    public function listByParameters(array $parameters): ArrayCollection
    {
        return new ArrayCollection($this->findAllReturnQueryBuilder($parameters)->getQuery()->getResult());
    }


    /**
     * @param Certification $certification
     * @param Skill|null $parentSkill
     * @param bool $createParentSkill
     * @return int|null
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function getLastOrder(Certification $certification, Skill $parentSkill = null, bool $createParentSkill = false): ?int
    {
        $qb = $this->createQueryBuilder('sk');
        $qb->select('MAX(sk.order)')
            ->where($qb->expr()->eq('sk.certification', ':certification'))
            ->setParameter('certification', $certification);
        if ($createParentSkill) {
            $qb->andWhere($qb->expr()->eq('sk.type', ':type'))
                ->setParameter('type', CertificationSkillType::SKILL_SET()->getValue());
        }
        if ($parentSkill) {
            $qb->andWhere($qb->expr()->eq('sk.parentSkill', ':parentSkill'))
                ->setParameter('parentSkill', $parentSkill);
        }
        return $qb->getQuery()->getSingleScalarResult() ?? null;
    }
}
