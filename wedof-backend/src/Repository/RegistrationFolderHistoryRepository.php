<?php

namespace App\Repository;

use App\Entity\RegistrationFolderHistory;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method RegistrationFolderHistory|null find($id, $lockMode = null, $lockVersion = null)
 * @method RegistrationFolderHistory|null findOneBy(array $criteria, array $orderBy = null)
 * @method RegistrationFolderHistory[]    findAll()
 * @method RegistrationFolderHistory[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class RegistrationFolderHistoryRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, RegistrationFolderHistory::class);
    }

    /**
     * @param RegistrationFolderHistory $registrationFolderHistory
     * @return RegistrationFolderHistory
     */
    public function save(RegistrationFolderHistory $registrationFolderHistory): RegistrationFolderHistory
    {
        if (!$this->_em->contains($registrationFolderHistory)) {
            $this->_em->persist($registrationFolderHistory);
        }
        $this->_em->flush();
        return $registrationFolderHistory;
    }
}
