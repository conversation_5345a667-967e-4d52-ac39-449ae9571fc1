<?php

namespace App\Repository;

use App\Entity\Organism;
use App\Entity\Payment;
use App\Entity\RegistrationFolder;
use App\Library\utils\enums\PaymentStates;
use DateTime;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\AbstractQuery;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;
use Exception;

/**
 * @method Payment|null find($id, $lockMode = null, $lockVersion = null)
 * @method Payment|null findOneBy(array $criteria, array $orderBy = null)
 * @method Payment[]    findAll()
 * @method Payment[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class PaymentRepository extends ServiceEntityRepository
{
    /**
     * PaymentRepository constructor.
     * @param ManagerRegistry $registry
     */
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Payment::class);
    }

    /**
     * @param Payment $payment
     * @return Payment
     */
    public function save(Payment $payment): Payment
    {
        if (!$this->_em->contains($payment)) {
            $this->_em->persist($payment);
        }
        $this->_em->flush();
        return $payment;
    }

    /**
     * @param $externalId
     * @return Payment|null
     */
    public function findOneByExternalId($externalId): ?Payment
    {
        return $this->findOneBy(array('externalId' => $externalId));
    }

    /**
     * @param Organism $organism
     * @param PaymentStates $state
     * @param null $orderBy
     * @return Payment|null
     */
    public function findOneByOrganismAndState(Organism $organism, PaymentStates $state, $orderBy = null): ?Payment
    {
        $payment = null;
        $orderBy = $orderBy ? array_merge($orderBy, ['id' => 'DESC']) : ['lastUpdate' => 'DESC', 'id' => 'DESC'];
        if (PaymentStates::WAITING()->equals($state)) {
            // traitement particulier pour les Waiting afin de mettre à jour la scheduled_date
            $payment = $this->findOneBy(['state' => $state->getValue(), 'organism' => $organism, 'scheduledDate' => null], ['id' => 'ASC']);
        }
        if (!$payment) {
            $payment = $this->findOneBy(['state' => $state->getValue(), 'organism' => $organism], $orderBy);
        }
        return $payment;
    }

    /**
     * @param array $parameters
     * @return Payment|null
     * @throws NonUniqueResultException
     */
    public function findOneByRegistrationFolderIdAndType(array $parameters): ?Payment
    {
        $qb = $this->createQueryBuilder('pm');
        $qb->andWhere($qb->expr()->like('pm.type', ':type'))
            ->setParameter('type', strtoupper($parameters['type']));
        $qb->join('pm.registrationFolder', 'rf')
            ->andWhere($qb->expr()->like('rf.externalId', ':registrationFolderId'))
            ->setParameter('registrationFolderId', $parameters['registrationFolderId'])
            ->orderBy('pm.id', 'ASC')
            ->setMaxResults(1); // MORE RESILIENT TO ACCIDENTAL DUPLICATES
        return $qb->getQuery()->getOneOrNullResult();
    }

    /**
     * @param ArrayCollection $organisms
     * @param array $parameters
     * @return QueryBuilder
     */
    public function findAllReturnQueryBuilder(ArrayCollection $organisms, array $parameters): QueryBuilder
    {
        $ids = $organisms->map(function (Organism $organism) {
            return $organism->getId();
        });
        $qb = $this->createQueryBuilder('pm');

        if (!in_array('all', $parameters['state'])) {
            $qb->andWhere($qb->expr()->in('pm.state', ':state'))
                ->setParameter('state', $parameters['state']);
        }
        if (!empty($parameters['type'])) {
            $qb->andWhere($qb->expr()->like('pm.type', ':type'))
                ->setParameter('type', strtoupper($parameters['type']));
        }
        if (!empty($parameters['since'])) {
            $qb->andWhere($qb->expr()->gte('pm.lastUpdate', ':lastUpdate'))
                ->setParameter('lastUpdate', $parameters['since']);
        }
        if (!empty($parameters['registrationFolderId'])) {
            $qb->join('pm.registrationFolder', 'rf')
                ->andWhere($qb->expr()->like('rf.externalId', ':registrationFolderId'))
                ->setParameter('registrationFolderId', $parameters['registrationFolderId']);
        }
        $qb->join('pm.organism', 'org')
            ->andWhere($qb->expr()->in('org.id', join(",", $ids->toArray())));
        $qb->orderBy('pm.id', $parameters['order']);

        return $qb;
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @return DateTime
     * @throws NonUniqueResultException
     * @throws Exception
     */
    public function findLastPaymentDateForRegistrationFolder(RegistrationFolder $registrationFolder): ?DateTime
    {
        $qb = $this->createQueryBuilder('payment');
        $qb->select('payment.scheduledDate')
            ->andWhere($qb->expr()->eq('payment.registrationFolder', ':registrationFolder'))
            ->setParameter('registrationFolder', $registrationFolder)
            ->orderBy('payment.scheduledDate', 'DESC')
            ->setMaxResults(1);
        $stringDate = $qb->getQuery()->getOneOrNullResult(AbstractQuery::HYDRATE_SINGLE_SCALAR);
        return $stringDate ? new DateTime($stringDate) : null;
    }
}
