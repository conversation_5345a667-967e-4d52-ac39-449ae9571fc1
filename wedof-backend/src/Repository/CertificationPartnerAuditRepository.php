<?php

namespace App\Repository;

use App\Entity\CertificationPartner;
use App\Entity\CertificationPartnerAudit;
use App\Entity\CertificationPartnerAuditTemplate;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<CertificationPartnerAudit>
 *
 * @method CertificationPartnerAudit|null find($id, $lockMode = null, $lockVersion = null)
 * @method CertificationPartnerAudit|null findOneBy(array $criteria, array $orderBy = null)
 * @method CertificationPartnerAudit[]    findAll()
 * @method CertificationPartnerAudit[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class CertificationPartnerAuditRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, CertificationPartnerAudit::class);
    }

    /**
     * @param CertificationPartnerAudit $certificationPartnerAudit
     * @return CertificationPartnerAudit
     */
    public function save(CertificationPartnerAudit $certificationPartnerAudit): CertificationPartnerAudit
    {
        if (!$this->_em->contains($certificationPartnerAudit)) {
            $this->_em->persist($certificationPartnerAudit);
        }
        $this->_em->flush();
        return $certificationPartnerAudit;
    }

    /**
     * @param array $parameters
     * @param CertificationPartner|null $certificationPartner
     * @return QueryBuilder
     */
    public function findAllReturnQueryBuilder(array $parameters, CertificationPartner $certificationPartner = null): QueryBuilder
    {
        $qb = $this->createQueryBuilder('cpa');

        if ($certificationPartner) {
            $qb->andWhere($qb->expr()->in('cpa.certificationPartner', ':certificationPartner'))
                ->setParameter('certificationPartner', $certificationPartner);
        }

        if (!in_array('all', $parameters['state'])) {
            $qb->andWhere($qb->expr()->in('cpa.state', ':state'))
                ->setParameter('state', $parameters['state']);
        }

        if (isset($parameters['templateId'])) {
            $qb->andWhere($qb->expr()->in('cpa.template', ':templateId'))
                ->setParameter('templateId', $parameters['templateId']);
        }

        if (isset($parameters['allowVisibilityPartner']) && $parameters['allowVisibilityPartner'] === true ) {
            $qb->join('cpa.template', 'auditTemplate')
                ->andWhere($qb->expr()->eq('auditTemplate.allowVisibilityPartner', ':allowVisibilityPartner'))
                ->setParameter('allowVisibilityPartner', $parameters['allowVisibilityPartner']);
        }

        $qb->addOrderBy('cpa.startDate', 'DESC');
        $qb->addOrderBy('cpa.id', 'DESC');

        return $qb;
    }

    /**
     * @param CertificationPartnerAudit $certificationPartnerAudit
     */
    public function delete(CertificationPartnerAudit $certificationPartnerAudit)
    {
        $this->_em->remove($certificationPartnerAudit);
        $this->_em->flush();
    }

    /**
     * @param CertificationPartnerAuditTemplate $cpat
     * @param array|null $states
     * @return float|int|mixed|string
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countByAuditTemplate(CertificationPartnerAuditTemplate $cpat, array $states = null)
    {
        $qb = $this->createQueryBuilder('certificationPartnerAudit');
        $qb->select('count(certificationPartnerAudit.id)')
            ->andWhere($qb->expr()->eq('certificationPartnerAudit.template', ':template'))
            ->setParameter('template', $cpat);
        if ($states) {
            $qb->andWhere($qb->expr()->in('certificationPartnerAudit.state', ':state'))
                ->setParameter('state', $states);
        }
        return $qb->getQuery()->getSingleScalarResult();
    }

    /**
     * @param array $parameters
     * @return QueryBuilder
     */
    public function listAuditReport(array $parameters): QueryBuilder
    {
        $subQuery = $this->createQueryBuilder('cpa');
        $subQuery->select('MAX(cpa.id)')
            ->join('cpa.certificationPartner', 'certificationPartner')
            ->where($subQuery->expr()->in('cpa.state', ':states'))
            ->andWhere($subQuery->expr()->eq('cpa.template', ':template'))
            ->andWhere($subQuery->expr()->in('certificationPartner.state', ':certificationPartnerStates'))
            ->groupBy('cpa.certificationPartner')
            ->orderBy('MAX(cpa.startDate)', 'DESC');

        $qb = $this->createQueryBuilder('cpAudit');
        $qb->where($qb->expr()->in('cpAudit.id', $subQuery->getDQL()))
            ->setParameter('states', $parameters['states'])
            ->setParameter('template', $parameters['template'])
            ->setParameter('certificationPartnerStates', $parameters['certificationPartnerStates']);

        return $qb;
    }
}
