<?php

namespace App\Repository;

use App\Entity\Formacode;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method Formacode|null find($id, $lockMode = null, $lockVersion = null)
 * @method Formacode|null findOneBy(array $criteria, array $orderBy = null)
 * @method Formacode[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class FormacodeRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Formacode::class);
    }

    /**
     * @param integer $code
     * @return Formacode|null
     */
    public function findOneByCode(int $code): ?Formacode
    {
        return $this->findOneBy(array("code" => $code));
    }

    /**
     * @param int|null $offset
     * @param int|null $limit
     * @return ArrayCollection
     */
    public function findAllWithTrainingActions(int $offset = null, int $limit = null): ArrayCollection
    {
        $q = $this->createQueryBuilder('f')
            ->where('f.count >= 0')
            ->setMaxResults($limit)
            ->setFirstResult($offset)
            ->getQuery();
        return new ArrayCollection($q->getResult());
    }

    /**
     * @param int|null $offset
     * @param int|null $limit
     * @return ArrayCollection
     */
    public function findAll(int $offset = null, int $limit = null): ArrayCollection
    {
        return new ArrayCollection($this->findBy(array(), array(), $limit, $offset));
    }
}
