<?php

namespace App\Repository;

use App\Entity\Certification;
use App\Entity\CertificationFolderSurvey;
use App\Entity\Organism;
use App\Library\utils\enums\CertificationFolderStates;
use DateTime;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<CertificationFolderSurvey>
 *
 * @method CertificationFolderSurvey|null find($id, $lockMode = null, $lockVersion = null)
 * @method CertificationFolderSurvey|null findOneBy(array $criteria, array $orderBy = null)
 * @method CertificationFolderSurvey[]    findAll()
 * @method CertificationFolderSurvey[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class CertificationFolderSurveyRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, CertificationFolderSurvey::class);
    }

    /**
     * @param CertificationFolderSurvey $survey
     * @return CertificationFolderSurvey
     */
    public function save(CertificationFolderSurvey $survey): CertificationFolderSurvey
    {
        if (!$this->_em->contains($survey)) {
            $this->_em->persist($survey);
        }
        $this->_em->flush();
        return $survey;
    }

    /**
     * @param CertificationFolderSurvey $survey
     */
    public function delete(CertificationFolderSurvey $survey): void
    {
        $this->_em->remove($survey);
        $this->_em->flush();
    }

    /**
     * @param array $parameters
     * @return QueryBuilder
     */
    public function findAllReturnQueryBuilder(array $parameters): QueryBuilder
    {
        $today = (new DateTime('now'))->format('Y-m-d');

        $qb = $this->createQueryBuilder('cfSurvey');
        $qb->join('cfSurvey.certificationFolder', 'certificationFolder');
        $qb->join('certificationFolder.certification', 'certification');

        if (isset($parameters['certifications'])) {
            $qb->andWhere($qb->expr()->in('certification.certifInfo', ':certifications'))
                ->setParameter('certifications', $parameters['certifications']);
        }

        if (isset($parameters['state']) && !in_array('all', $parameters['state'])) {
            $qb->andWhere($qb->expr()->in('cfSurvey.state', ':state'))
                ->setParameter('state', $parameters['state']);
        }

        if (isset($parameters['raiseEventSixMonthExperienceAvailable']) && $parameters['raiseEventSixMonthExperienceAvailable'] === true) {
            $qb->andWhere($qb->expr()->eq('cfSurvey.sixMonthExperienceStartDate', ':startDate'))
                ->setParameter('startDate', $today);
        }

        if (isset($parameters['raiseEventLongTermExperienceAvailable']) && $parameters['raiseEventLongTermExperienceAvailable'] === true) {
            $qb->andWhere($qb->expr()->eq('cfSurvey.longTermExperienceStartDate', ':startDate'))
                ->setParameter('startDate', $today);
        }

        if (isset($parameters['toExport']) && $parameters['toExport'] === true) {
            $qb->andWhere($qb->expr()->eq('certification', ':certification'))
                ->andWhere($qb->expr()->eq('certificationFolder.certifier', ':certifier'))
                ->andWhere($qb->expr()->notIn('certificationFolder.state', ':certificationFolderState'))
                ->andWhere($qb->expr()->isNotNull('cfSurvey.initialExperience'))
                ->setParameter('certification', $parameters['certificationToExport'])
                ->setParameter('certificationFolderState', [CertificationFolderStates::ABORTED()->getValue(), CertificationFolderStates::REFUSED()->getValue(), CertificationFolderStates::TO_REGISTER()->getValue()])
                ->setParameter('certifier', $parameters['organism']);
        }

        $qb->addOrderBy('cfSurvey.id', 'ASC');

        return $qb;
    }

    /**
     * @param Certification $certification
     * @param Organism|null $partner
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countForCertification(Certification $certification, Organism $partner = null): int
    {
        $qb = $this->createQueryBuilder('survey');
        $qb->select('count(survey.id)')
            ->join('survey.certificationFolder', 'certificationFolder')
            ->join('certificationFolder.certification', 'certification')
            ->andWhere($qb->expr()->notIn('certificationFolder.state', ':certificationFolderState'))
            ->andWhere($qb->expr()->eq('certification', ':certification'))
            ->setParameter('certificationFolderState', [CertificationFolderStates::ABORTED()->getValue(), CertificationFolderStates::REFUSED()->getValue(), CertificationFolderStates::TO_REGISTER()->getValue()])
            ->setParameter('certification', $certification);
        if ($partner) {
            $qb->andWhere($qb->expr()->eq('certificationFolder.partner', ':partner'))
                ->setParameter('partner', $partner);
        }
        return $qb->getQuery()->getSingleScalarResult();
    }

    /**
     * @param Certification $certification
     * @param string $experienceName
     * @param Organism|null $partner
     * @param bool $canAnswer
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countForExperience(Certification $certification, string $experienceName, Organism $partner = null, bool $canAnswer = false): int
    {
        $qb = $this->createQueryBuilder('survey');
        $qb->select('count(survey.id)')
            ->join('survey.certificationFolder', 'certificationFolder')
            ->join('certificationFolder.certification', 'certification')
            ->andWhere($qb->expr()->eq('certification', ':certification'))
            ->setParameter('certification', $certification)
            ->andWhere($qb->expr()->notIn('certificationFolder.state', ':certificationFolderState'))
            ->setParameter('certificationFolderState', [CertificationFolderStates::ABORTED()->getValue(), CertificationFolderStates::REFUSED()->getValue(), CertificationFolderStates::TO_REGISTER()->getValue()]);
        if ($canAnswer) {
            $today = (new DateTime('now'))->format('Y-m-d');
            $qb->andWhere($qb->expr()->lte('survey.' . $experienceName . 'StartDate', ':startDate'))
                ->andWhere($qb->expr()->isNull('survey.' . $experienceName . 'AnsweredDate'))
                ->setParameter('startDate', $today);
        } else {
            $qb->andWhere($qb->expr()->isNotNull('survey.' . $experienceName));
        }
        if ($partner) {
            $qb->andWhere($qb->expr()->eq('certificationFolder.partner', ':partner'))
                ->setParameter('partner', $partner);
        }
        return $qb->getQuery()->getSingleScalarResult();
    }

    /**
     * @param Certification $certification
     * @param string $experienceName
     * @param Organism|null $partner
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countForExperienceAnsweredRate(Certification $certification, string $experienceName, Organism $partner = null): int
    {
        $qb = $this->createQueryBuilder('survey');
        $qb->select('count(survey.id)')
            ->join('survey.certificationFolder', 'certificationFolder')
            ->join('certificationFolder.certification', 'certification')
            ->andWhere($qb->expr()->eq('certification', ':certification'))
            ->setParameter('certification', $certification)
            ->andWhere($qb->expr()->isNotNull('survey.' . $experienceName . 'AnsweredDate'));
        if ($partner) {
            $qb->andWhere($qb->expr()->eq('certificationFolder.partner', ':partner'))
                ->setParameter('partner', $partner);
        }
        return $qb->getQuery()->getSingleScalarResult();
    }
}
