<?php

namespace App\Repository;

use App\Entity\City;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<City>
 *
 * @method City|null find($id, $lockMode = null, $lockVersion = null)
 * @method City|null findOneBy(array $criteria, array $orderBy = null)
 * @method City[]    findAll()
 * @method City[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class CityRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, City::class);
    }

    /**
     * @param string $name
     * @return City|null
     */
    public function findOneByName(string $name): ?City
    {
        $city = null;

        $qb = $this->createQueryBuilder('city');
        $qb->where($qb->expr()->eq("SUBSTRING(REPLACE(REPLACE(city.name, '-', ' '), '''', ' '), 1, 30)", ':name')) // l'échappement de l'apostrophe est l'apostrophe
            ->setParameter('name', $name);
        try {
            $city = $qb->getQuery()->getOneOrNullResult();
        } catch (NonUniqueResultException $exception) {
        } finally {
            return $city;
        }
    }

    /**
     * @param string $name
     * @param string $departement
     * @return City|null
     */
    public function findOneByNameAndDepartement(string $name, string $departement): ?City
    {
        $city = null;

        $qb = $this->createQueryBuilder('city');
        $qb
            ->andWhere($qb->expr()->eq("SUBSTRING(REPLACE(REPLACE(city.name, '-', ' '), '''', ' '), 1, 30)", ':name')) // l'échappement de l'apostrophe est l'apostrophe
            ->andWhere($qb->expr()->like("city.postalCode", ':departement')) // l'échappement de l'apostrophe est l'apostrophe
            ->setParameter('name', $name)
            ->setParameter('departement', $departement . '%');
        try {
            $city = $qb->getQuery()->getOneOrNullResult();
        } catch (NonUniqueResultException $exception) {
        } finally {
            return $city;
        }
    }

    /**
     * @param string $name
     * @return void
     */
    public function findAllByName(string $name): ArrayCollection
    {
        $qb = $this->createQueryBuilder('city');
        $qb->where($qb->expr()->like('city.name', ':name'))
            ->setParameter('name', '%' . $name . '%');

        return new ArrayCollection($qb->getQuery()->getResult());
    }
}
