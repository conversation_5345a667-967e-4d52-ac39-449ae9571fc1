<?php

namespace App\Repository;

use App\Entity\ShortenedUrl;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<ShortenedUrl>
 *
 * @method ShortenedUrl|null find($id, $lockMode = null, $lockVersion = null)
 * @method ShortenedUrl|null findOneBy(array $criteria, array $orderBy = null)
 * @method ShortenedUrl[]    findAll()
 * @method ShortenedUrl[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ShortenedUrlRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ShortenedUrl::class);
    }

    public function save(ShortenedUrl $shortenedUrl): ShortenedUrl
    {
        if (!$this->_em->contains($shortenedUrl)) {
            $this->_em->persist($shortenedUrl);
        }
        $this->_em->flush();
        return $shortenedUrl;
    }

    public function findOneByHashKey(string $key): ?ShortenedUrl
    {
        return $this->findOneBy(['hashKey' => $key]);
    }
}
