<?php

namespace App\Repository;

use App\Application\MessageTemplates\Entity\Message;
use App\Entity\Attendee;
use App\Entity\Certification;
use App\Entity\CertificationFolder;
use App\Entity\CertificationFoldersCdcFiles;
use App\Entity\CertificationFolderSurvey;
use App\Entity\CertificationPartner;
use App\Entity\Organism;
use App\Entity\Subscription;
use App\Entity\Training;
use App\Library\utils\enums\CertificationFolderCdcStates;
use App\Library\utils\enums\CertificationFoldersCdcFilesStates;
use App\Library\utils\enums\CertificationFolderStates;
use App\Library\utils\enums\CertificationTypes;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\enums\RegistrationFolderStates;
use App\Library\utils\Tools;
use DateTime;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\DBAL\Driver\Exception;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;
use LogicException;
use Throwable;

/**
 * @method CertificationFolder|null find($id, $lockMode = null, $lockVersion = null)
 * @method CertificationFolder|null findOneBy(array $criteria, array $orderBy = null)
 * @method CertificationFolder[]    findAll()
 * @method CertificationFolder[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class CertificationFolderRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, CertificationFolder::class);
    }

    //-------------------
    // METHODES PUBLIQUES
    //-------------------

    /**
     * @param CertificationFolder $certificationFolder
     * @return CertificationFolder
     * @throws Throwable
     */
    public function save(CertificationFolder $certificationFolder): CertificationFolder
    {
        $certificationFolder->refreshCdcCompliant();
        if (!$this->_em->contains($certificationFolder)) {
            $this->_em->persist($certificationFolder);
        }
        $this->_em->flush();
        return $certificationFolder;
    }

    /**
     * @param Organism $currentOrganism
     * @param array $parameters
     * @return QueryBuilder
     */
    public function findAllReturnQueryBuilder(Organism $currentOrganism, array $parameters): QueryBuilder
    {
        if (isset($parameters['columnId'])) {
            $columnId = $parameters['columnId'];
            $columnParameters = $parameters; // Affectation copies the array - We use a copy to avoid overriding $parameters
            if (in_array(CertificationFolderStates::ALL()->getValue(), $parameters['state'])) {
                $columnParameters['state'] = CertificationFolderStates::valuesStatesToString(); // Hack because of "all" fake state
            }
            if (in_array(CertificationFolderCdcStates::ALL()->getValue(), $parameters['cdcState'])) {
                $columnParameters['cdcState'] = CertificationFolderCdcStates::valuesStatesToString(); // Hack because of "all" fake state
            }
            $parameters = Tools::computeKanbanColumnParameters($columnParameters, $this->listColumnConfigs(), $columnId);
        }

        $joinRegistrationFolder = false;
        $joinRegistrationFolderHistory = false;
        $joinCertificationFolderHistory = false;
        $joinCdcFile = false;
        $joinMessageTemplate = false;
        $joinSurvey = false;
        $joinCertificationPartner = false;
        $joinCertificationPartnerSkills = false;
        $joinCertificationFolderSkills = false;
        $joinTags = false;

        $qb = $this->createQueryBuilder('cf');
        $qb->leftJoin('cf.partner', 'partner');
        $qb->join('cf.certifier', 'certifier');
        $qb->join('certifier.subscription', 'subscription');
        $qb->join('cf.certification', 'certification'); // obligatoire pour la getRevenueByState

        $qb->andWhere($qb->expr()->orX(
            $qb->expr()->eq('cf.partner', ':currentOrganismId'),
            $qb->expr()->eq('cf.certifier', ':currentOrganismId')
        ))->setParameter('currentOrganismId', $currentOrganism->getId());
        $qb->andWhere($qb->expr()->eq('subscription.allowCertifiers', true));

        if (isset($parameters['partners'])) {
            $qb->andWhere($qb->expr()->in('partner.siret', ':partners'))
                ->setParameter('partners', $parameters['partners']);
        }

        if (isset($parameters['certifications'])) {
            $qb->andWhere($qb->expr()->in('certification.certifInfo', ':certifications'))
                ->setParameter('certifications', $parameters['certifications']);
        }

        if (isset($parameters['skillSets']) && !in_array('all', $parameters['skillSets'])) {
            $qb->andWhere(
                $qb->expr()->orX(
                    $qb->expr()->in('cfSkills.id', ':skillSets'),
                    $qb->expr()->andX(
                        $qb->expr()->isNull('cfSkills.id'),
                        $qb->expr()->isNull('cpSkills.id')
                    )
                )
            )->setParameter('skillSets', $parameters['skillSets']);
            $joinCertificationPartner = true;
            $joinCertificationPartnerSkills = true;
            $joinCertificationFolderSkills = true;
        }

        if (isset($parameters['certifier'])) {
            $qb->andWhere(
                $qb->expr()->eq('certifier', ':certifier')
            )->setParameter('certifier', $parameters['certifier']);
        }

        if (isset($parameters['state']) && !in_array('all', $parameters['state'])) {
            $qb->andWhere($qb->expr()->in('cf.state', ':state'))
                ->setParameter('state', $parameters['state']);
        }

        if (isset($parameters['cdcState']) && !in_array('all', $parameters['cdcState'])) {
            $qb->andWhere($qb->expr()->in('cf.cdcState', ':cdcState'))
                ->setParameter('cdcState', $parameters['cdcState']);
        }

        if (isset($parameters['withoutRegistrationFolder']) && $parameters['withoutRegistrationFolder'] === true) {
            $qb->andWhere($qb->expr()->isNull('cf.registrationFolder'));
        }

        if (isset($parameters['registrationFolderState'])) {
            $qb->andWhere($qb->expr()->in('rf.state', ':registrationFolderState'))
                ->setParameter('registrationFolderState', $parameters['registrationFolderState']);
            $joinRegistrationFolder = true;
        }

        if (isset($parameters['registrationFolderType'])) {
            $qb->andWhere($qb->expr()->in('rf.type', ':registrationFolderType'))
                ->setParameter('registrationFolderType', $parameters['registrationFolderType']);
            $joinRegistrationFolder = true;
        }

        if (isset($parameters['registrationFolderCompletionRate'])) {
            switch ($parameters['registrationFolderCompletionRate']) {
                case '>80':
                    $qb->andWhere($qb->expr()->gt('rf.completionRate', 80));
                    break;
                case '<80':
                    $qb->andWhere(
                        $qb->expr()->orX(
                            $qb->expr()->lte('rf.completionRate', 80),
                            $qb->expr()->andX(
                                $qb->expr()->isNull('rf.completionRate'),
                                $qb->expr()->isNotNull('rf.id')
                            )
                        )
                    );
                    break;
                default:
                    throw new LogicException("Erreur");
            }
            $joinRegistrationFolder = true;
        }

        if (isset($parameters['messageState'])) {
            $qb->andWhere($qb->expr()->in('message.state', ':messageState'))
                ->setParameter('messageState', $parameters['messageState']);
            $joinMessageTemplate = true;
            $joinSurvey = true;
        }
        if (isset($parameters['messageTemplate'])) {
            $qb->andWhere($qb->expr()->in('messageTemplate.id', ':messageTemplateId'))
                ->setParameter('messageTemplateId', $parameters['messageTemplate']);
            $joinMessageTemplate = true;
            $joinSurvey = true;
        }

        if (isset($parameters['survey'])) {
            if (in_array('initialExperienceStartDate', $parameters['survey'])) {
                $qb->andWhere($qb->expr()->isNull('survey.initialExperience'));
            }
            if (in_array('sixMonthExperienceStartDate', $parameters['survey'])) {
                $qb->andWhere($qb->expr()->lte('survey.sixMonthExperienceStartDate', ':startDate'))
                    ->andWhere($qb->expr()->isNull('survey.sixMonthExperienceAnsweredDate'))
                    ->setParameter('startDate', (new DateTime('now'))->format('Y-m-d'));
            }
            if (in_array('longTermExperienceStartDate', $parameters['survey'])) {
                $qb->andWhere($qb->expr()->lte('survey.longTermExperienceStartDate', ':startDate'))
                    ->andWhere($qb->expr()->isNull('survey.longTermExperienceAnsweredDate'))
                    ->setParameter('startDate', (new DateTime('now'))->format('Y-m-d'));
            }
            if (in_array('initialExperienceAnsweredDate', $parameters['survey'])) {
                $qb->andWhere($qb->expr()->isNotNull('survey.initialExperienceAnsweredDate'));
            }
            if (in_array('sixMonthExperienceAnsweredDate', $parameters['survey'])) {
                $qb->andWhere($qb->expr()->isNotNull('survey.sixMonthExperienceAnsweredDate'));
            }
            if (in_array('longTermExperienceAnsweredDate', $parameters['survey'])) {
                $qb->andWhere($qb->expr()->isNotNull('survey.longTermExperienceAnsweredDate'));
            }
            $qb->andWhere($qb->expr()->notIn('cf.state', ':excludeState'))
                ->setParameter('excludeState', [CertificationFolderStates::TO_REGISTER()->getValue(), CertificationFolderStates::ABORTED()->getValue(), CertificationFolderStates::REFUSED()->getValue()]);
            $joinSurvey = true;
        }

        $filterOnStateDate = 'cf.stateLastUpdate';
        if (isset($parameters['filterOnStateDate']) && $parameters['filterOnStateDate'] != 'stateLastUpdate') {
            if ($parameters['filterOnStateDate'] === 'wedofInvoice') {
                $qb->andWhere($qb->expr()->gt('cfh.toRegisterDate', 'subscription.certifierStartDate'));
                $filterOnStateDate = 'cf.createdOn';
                $joinCertificationFolderHistory = true;
            } else if (in_array($parameters['filterOnStateDate'], ['examinationDate', 'examinationEndDate', 'enrollmentDate'])) {
                $filterOnStateDate = 'cf.' . $parameters['filterOnStateDate'];
            } else if (!Tools::contains($parameters['filterOnStateDate'], 'RegistrationFolder')) {
                $filterOnStateDate = 'cfh.' . $parameters['filterOnStateDate'];
                $joinCertificationFolderHistory = true;
            } else {
                if ($parameters['filterOnStateDate'] === 'sessionStartDateRegistrationFolderDate') {
                    $filterOnStateDate = "STRTODATE(JSON_UNQUOTE(JSON_EXTRACT(rf.rawData, '$.trainingActionInfo.sessionStartDate')),'%d/%m/%Y')";
                } else if ($parameters['filterOnStateDate'] === 'sessionEndDateRegistrationFolderDate') {
                    $filterOnStateDate = "STRTODATE(JSON_UNQUOTE(JSON_EXTRACT(rf.rawData, '$.trainingActionInfo.sessionEndDate')),'%d/%m/%Y')";
                } else {
                    $filterOnStateDate = 'rfh.' . str_replace('RegistrationFolderState', '', $parameters['filterOnStateDate']);
                    $joinRegistrationFolderHistory = true;
                }
                $joinRegistrationFolder = true;
            }
        }

        if (isset($parameters['since'])) {
            $qb->andWhere($qb->expr()->gte($filterOnStateDate, ':since'))
                ->setParameter('since', $parameters['since']);
        }
        if (isset($parameters['until'])) {
            $qb->andWhere($qb->expr()->lte($filterOnStateDate, ':until'))
                ->setParameter('until', $parameters['until']);
        }

        if (isset($parameters['cdcCompliant'])) {
            $cdcCompliant = filter_var($parameters['cdcCompliant'], FILTER_VALIDATE_BOOLEAN);
            $qb->andWhere($qb->expr()->eq('cf.cdcCompliant', ':cdcCompliant'))
                ->setParameter('cdcCompliant', $cdcCompliant);
        }

        if (isset($parameters['cdcToExport'])) {
            $cdcToExport = filter_var($parameters['cdcToExport'], FILTER_VALIDATE_BOOLEAN);
            $qb->andWhere($qb->expr()->eq('cf.cdcToExport', ':cdcToExport'))
                ->setParameter('cdcToExport', $cdcToExport);
        }

        if (isset($parameters['cdcExcluded'])) {
            $cdcExcluded = filter_var($parameters['cdcExcluded'], FILTER_VALIDATE_BOOLEAN);
            $qb->andWhere($qb->expr()->eq('cf.cdcExcluded', ':cdcExcluded'))
                ->setParameter('cdcExcluded', $cdcExcluded);
        }

        if (isset($parameters['cdcFile'])) {
            $qb->andWhere($qb->expr()->eq('cdcFile.id', ':cdcFile'))
                ->setParameter('cdcFile', $parameters['cdcFile']);
            $joinCdcFile = true;
        }

        if (!empty($parameters['metadata']) && sizeof($parameters['metadata']) <= 2) {
            if (!empty($parameters['metadata'][1])) {
                $metadata = '"' . $parameters['metadata'][0] . '": "' . $parameters['metadata'][1] . '"';  //one value with metadata key
            } else {
                $metadata = '"' . $parameters['metadata'][0] . '":'; //all values with metadata key
            }
            $qb->andWhere($qb->expr()->like('cf.metadata', ':metadata'));
            $qb->setParameter('metadata', '%' . $metadata . '%');
        }

        if (!empty($parameters['tags'])) {
            $tags = explode(",", $parameters['tags']);
            $orX = $qb->expr()->orX();
            for ($i = 0; $i < sizeof($tags); $i++) {
                $orX->add($qb->expr()->orX($qb->expr()->eq('tags.name', ':tags' . $i)));
                $qb->setParameter('tags' . $i, $tags[$i]);
            }
            $qb->andWhere($orX);
            $joinTags = true;
        }

        if (!empty($parameters['query'])) {
            $query = trim($parameters['query']);
            if (preg_match('/^(CERT|RS|RNCP)\d{3,7}$/', strtoupper($query))) {
                $type = null;
                if (Tools::startsWith(strtoupper($query), 'RS')) {
                    $query = preg_replace("/\s+/", "", substr($query, strlen('RS')));
                    $type = CertificationTypes::RS()->getValue();
                } else if (Tools::startsWith(strtoupper($query), 'RNCP')) {
                    $query = preg_replace("/\s+/", "", substr($query, strlen('RNCP')));
                    $type = CertificationTypes::RNCP()->getValue();
                } else if (Tools::startsWith(strtoupper($query), 'CERT')) {
                    $query = preg_replace("/\s+/", "", substr($query, strlen('CERT')));
                    $type = CertificationTypes::INTERNAL()->getValue();
                } else if (Tools::startsWith(strtoupper($query), 'PREVENTION')) {
                    $query = preg_replace("/\s+/", "", substr($query, strlen('PREVENTION')));
                    $type = CertificationTypes::PREVENTION()->getValue();
                }
                $qb->andWhere($qb->expr()->andX($qb->expr()->like('certification.code', ':query'), $qb->expr()->eq('certification.type', ':type')));
                $qb->setParameter('type', $type);
            } else if (Tools::startsWith(strtoupper($query), 'CERTIFINFO')) {
                $query = preg_replace("/\s+/", "", substr($query, strlen('CERTIFINFO')));
                $qb->andWhere($qb->expr()->like('certification.certifInfo', ':query'));
            } else {
                $qb->join('cf.attendee', 'att')
                    ->andWhere($qb->expr()->orX(
                        $qb->expr()->like('att.email', ':query'),
                        $qb->expr()->like('att.lastName', ':query'),
                        $qb->expr()->like('att.firstName', ':query'),
                        $qb->expr()->like('att.phoneNumber', ':query'),
                        $qb->expr()->like("CONCAT(att.firstName, ' ', att.lastName)", ':query'),
                        $qb->expr()->like('cf.id', ':query'),
                        $qb->expr()->like('cf.externalId', ':query'),
                        $qb->expr()->like('cf.comment', ':query'),
                        $qb->expr()->like('certification.name', ':query'),
                        $qb->expr()->like('certification.code', ':query'),
                        $qb->expr()->like('certification.certifInfo', ':query'),
                        $qb->expr()->like('partner.name', ':query'),
                        $qb->expr()->like('tags.name', ':query')
                    ));
                $joinTags = true;
            }
            $qb->setParameter('query', '%' . $query . '%');
        }
        if (isset($parameters['sort']) && in_array($parameters['sort'], ['stateLastUpdate', 'id', 'successDate'])) {
            if ($parameters['sort'] === "successDate") {
                $qb->orderBy('cfh.' . $parameters['sort'], $parameters['order']);
                $joinCertificationFolderHistory = true;
            } else {
                $qb->orderBy('cf.' . $parameters['sort'], $parameters['order']);
            }
        }
        $qb->addOrderBy('cf.id', 'ASC');

        if ($joinCertificationFolderHistory) {
            $qb->join('cf.history', 'cfh');
        }
        if ($joinRegistrationFolder) {
            $qb->leftJoin('cf.registrationFolder', 'rf');
        }
        if ($joinRegistrationFolderHistory) {
            $qb->join('rf.history', 'rfh');
        }
        if ($joinCdcFile) {
            $qb->leftJoin('cf.certificationFoldersCdcFiles', 'cfCdcFiles');
            $qb->leftJoin('cfCdcFiles.cdcFile', 'cdcFile');
        }
        if ($joinSurvey) {
            $qb->join('cf.survey', 'survey');
        }
        if ($joinMessageTemplate) {
            $qb->join(Message::class, 'message', Join::WITH, $qb->expr()->andX(
                "(
                    (message.entityClass = :certificationFolderClassName AND message.entityId = cf.id) OR
                    (message.entityClass = :entityClassSurvey AND message.entityId = survey.id)
                 )"
            ))
                ->setParameter('certificationFolderClassName', CertificationFolder::CLASSNAME)
                ->setParameter('entityClassSurvey', CertificationFolderSurvey::CLASSNAME);
            $qb->join('message.messageTemplate', 'messageTemplate');
        }
        if ($joinCertificationPartner) {
            $qb->leftJoin(CertificationPartner::class, 'cp', Join::WITH, $qb->expr()->andX(
                $qb->expr()->eq('cp.partner', 'partner'),
                $qb->expr()->eq('cp.certifier', 'certifier'),
                $qb->expr()->in('cp.certification', 'certification')
            ));
            if ($joinCertificationPartnerSkills) {
                $qb->leftJoin('cp.skillSets', 'cpSkills');
            }
        }
        if ($joinCertificationFolderSkills) {
            $qb->leftJoin('cf.skillSets', 'cfSkills');
        }
        if ($joinTags) {
            $qb->leftJoin('cf.tags', 'tags');
        }
        return $qb;
    }

    /**
     * @param Organism $currentOrganism
     * @param array $parameters
     * @param array $columnIds
     * @return ArrayCollection
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function findAllByColumn(Organism $currentOrganism, array $parameters, array $columnIds): ArrayCollection
    {
        $columns = [];
        foreach ($columnIds as $columnId) {
            $columnParameters = $parameters; // Affectation copies the array - We use a copy to avoid overriding $parameters
            $columnParameters['columnId'] = $columnId;
            if ($columnParameters['allowed']) {
                $qb = $this->findAllReturnQueryBuilder($currentOrganism, $columnParameters);
                $items = (clone $qb)->distinct()->setMaxResults($columnParameters['limit'])->getQuery()->getResult();
                $total = (int)(clone $qb)->select($qb->expr()->count('DISTINCT(cf.id)'))->getQuery()->getSingleScalarResult();
                if (in_array(CertificationFolderStates::ALL()->getValue(), $columnParameters['state'])) {
                    $columnParameters['state'] = CertificationFolderStates::valuesStatesToString(); // Hack because of "all" fake state
                }
                if (in_array(CertificationFolderCdcStates::ALL()->getValue(), $parameters['cdcState'])) {
                    $columnParameters['cdcState'] = CertificationFolderCdcStates::valuesStatesToString(); // Hack because of "all" fake state
                }
                $revenue = $currentOrganism->isCertifierOrganism() && $currentOrganism->getSubscription()->isAllowCertifiers() ? $this->getRevenue(clone $qb, $currentOrganism) : 0;
            } else {
                $items = [];
                $total = 0;
                $revenue = 0;
            }
            $columns[] = [
                'columnId' => $columnId,
                'items' => $items,
                'total' => $total,
                'revenue' => $revenue
            ];
        }
        return new ArrayCollection(['columns' => $columns]);
    }

    /**
     * @param QueryBuilder $qb
     * @param Organism $organism
     * @return float
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function getRevenue(QueryBuilder $qb, Organism $organism): float
    {
        return $qb
                ->select('SUM(COALESCE(cf.amountHt, certification.amountHt))')
                ->join('certification.certifiers', 'certifiers')
                ->andWhere($qb->expr()->eq('certifiers', ':organism'))
                ->andWhere($qb->expr()->neq('cf.partner', ':organism'))
                ->andWhere($qb->expr()->orX(
                    $qb->expr()->isNotNull('cf.amountHt'),
                    $qb->expr()->isNotNull('certification.amountHt')
                ))
                ->setParameter('organism', $organism)
                ->getQuery()->getSingleScalarResult() ?? 0;
    }

    /**
     * @param Certification $certification
     * @param string|null $siret
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countForCertification(Certification $certification, string $siret = null): int
    {
        $qb = $this->createQueryBuilder('cf');
        $qb->select('count(cf.id)')
            ->andWhere($qb->expr()->eq('cf.certification', ':certification'))
            ->setParameter('certification', $certification);

        if ($siret) {
            $qb->join('cf.partner', 'org')
                ->andWhere($qb->expr()->eq('org.siret', ':siret'))
                ->setParameter('siret', $siret);
        }
        return $qb->getQuery()->getSingleScalarResult();
    }

    /**
     * @param Certification $certification
     * @param string|null $siret
     * @return array
     */
    public function countForCertificationByState(Certification $certification, string $siret = null): array
    {
        $qb = $this->createQueryBuilder('cf');
        $qb->select('cf.state, count(cf.id) as count')
            ->andWhere($qb->expr()->eq('cf.certification', ':certification'))
            ->setParameter('certification', $certification);
        if ($siret) {
            $qb->join('cf.partner', 'org')
                ->andWhere($qb->expr()->eq('org.siret', ':siret'))
                ->setParameter('siret', $siret);
        }
        $qb->groupBy('cf.state');
        return $qb->getQuery()->getResult();
    }

    /**
     * @param Certification $certification
     * @param string|null $siret
     * @return array
     */
    public function countForCertificationByStateAbortedExploded(Certification $certification, string $siret = null): array
    {
        $qb = $this->createQueryBuilder('cf');
        $qb->select('count(cf.id) as all,
                           sum(CASE WHEN rf.completionRate > 80 then 1 else 0 end) AS afterTraining,
                           sum(CASE WHEN (rf.completionRate > 0 AND rf.completionRate <= 80) then 1 else 0 end) AS inTraining,
                           sum(CASE WHEN (rf.completionRate = 0 OR rf.completionRate is NULL) then 1 else 0 end) AS beforeTraining')
            ->join('cf.registrationFolder', 'rf')
            ->andWhere($qb->expr()->eq('cf.state', ':state'))
            ->andWhere($qb->expr()->eq('cf.certification', ':certification'))
            ->setParameter('state', CertificationFolderStates::ABORTED()->getValue())
            ->setParameter('certification', $certification);
        if ($siret) {
            $qb->join('cf.partner', 'org')
                ->andWhere($qb->expr()->eq('org.siret', ':siret'))
                ->setParameter('siret', $siret);
        }
        return $qb->getQuery()->getResult()[0];
    }

    /**
     * @param Certification $certification
     * @param string|null $siret
     * @param string $delay
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countToTakeForCertificationAfterDelay(Certification $certification, string $delay, string $siret = null): int
    {
        $startDate = (new DateTime())->modify('- ' . $delay);
        $qb = $this->createQueryBuilder('cf');
        $qb->select('count(cf.id)')
            ->join('cf.history', 'cfh')
            ->andWhere($qb->expr()->eq('cf.state', ':state'))
            ->andWhere($qb->expr()->eq('cf.certification', ':certification'))
            ->andWhere($qb->expr()->lte('cfh.toTakeDate', ':startDate'))
            ->setParameter('state', CertificationFolderStates::TO_TAKE()->getValue())
            ->setParameter('certification', $certification)
            ->setParameter('startDate', $startDate);
        if ($siret) {
            $qb->join('cf.partner', 'org')
                ->andWhere($qb->expr()->eq('org.siret', ':siret'))
                ->setParameter('siret', $siret);
        }
        return $qb->getQuery()->getSingleScalarResult();
    }

    /**
     * @param Organism $partner
     * @param Certification $certification
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countForPartnerAndCertification(Organism $partner, Certification $certification): int
    {
        $qb = $this->createQueryBuilder('cf');
        $qb->select('count(cf.id)')
            ->andWhere($qb->expr()->eq('cf.partner', ':partner'))
            ->andWhere($qb->expr()->eq('cf.certification', ':certification'))
            ->setParameter('partner', $partner)
            ->setParameter('certification', $certification);

        return $qb->getQuery()->getSingleScalarResult();
    }

    /**
     * @param Subscription $subscription
     * @param bool $useCertificationFoldersDates
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countForCertifierSubscription(Subscription $subscription, bool $useCertificationFoldersDates = false): int
    {
        if ($useCertificationFoldersDates) {
            $periodStartDate = $subscription->getCertificationFoldersNumberPeriodStartDate();
            $periodEndDate = new DateTime();
            $periodEndDate->setTimestamp($subscription->getCertificationFoldersNumberPeriodEndDate()->getTimestamp())->modify('- 1 second'); // Prevent overlap with next month period
        } else {
            $periodStartDate = $subscription->getCertifierPeriodStartDate();
            $periodEndDate = new DateTime();
            $periodEndDate->setTimestamp($subscription->getCertifierPeriodEndDate()->getTimestamp())->modify('- 1 second'); // Prevent overlap with next month period
        }

        $qb = $this->createQueryBuilder('cf');
        $qb->select($qb->expr()->count('cf.id'))
            ->join('cf.history', 'history')
            ->join('cf.certifier', 'certifier')
            ->where($qb->expr()->between('cf.createdOn', ':periodStartDate', ':periodEndDate'))
            ->andWhere($qb->expr()->gt('history.toRegisterDate', ':startDate'))
            ->andWhere($qb->expr()->eq('certifier', ':certifier'))
            ->setParameter('periodStartDate', $periodStartDate)
            ->setParameter('periodEndDate', $periodEndDate)
            ->setParameter('startDate', $subscription->getCertifierStartDate())
            ->setParameter('certifier', $subscription->getOrganism());

        return $qb->getQuery()->getSingleScalarResult();
    }

    /**
     * @param Attendee $attendee
     * @param Certification $certification
     * @return float|int|mixed|string
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countForAttendeeAndCertification(Attendee $attendee, Certification $certification)
    {
        $qb = $this->createQueryBuilder('cf');
        $qb->select($qb->expr()->count('cf.id'))
            ->where($qb->expr()->eq('cf.attendee', ':attendee'))
            ->andWhere($qb->expr()->eq('cf.certification', ':certification'))
            ->setParameter('attendee', $attendee)
            ->setParameter('certification', $certification);

        return $qb->getQuery()->getSingleScalarResult();
    }

    /**
     * @param Organism $organism
     * @param Certification $certification
     * @param array $parameters
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countForOrganismByCertificationForCdc(Organism $organism, Certification $certification, array $parameters): int
    {
        $cdcCompliant = filter_var($parameters['cdcCompliant'], FILTER_VALIDATE_BOOLEAN);
        $cdcToExport = filter_var($parameters['cdcToExport'], FILTER_VALIDATE_BOOLEAN);

        $qb = $this->createQueryBuilder('cf');
        $qb->select($qb->expr()->count('cf.id'))
            ->join('cf.certification', 'certification')
            ->join('certification.certifiers', 'certifiers')
            ->where($qb->expr()->eq('certifiers', ':organism'))
            ->andWhere($qb->expr()->eq('certification', ':certification'))
            ->andWhere($qb->expr()->eq('cf.state', ':state'))
            ->andWhere($qb->expr()->in('cf.cdcState', ':cdcState'))
            ->andWhere($qb->expr()->eq('cf.cdcCompliant', ':cdcCompliant'))
            ->andWhere($qb->expr()->eq('cf.cdcToExport', ':cdcToExport'))
            ->setParameter('organism', $organism)
            ->setParameter('certification', $certification)
            ->setParameter('state', $parameters['state'])
            ->setParameter('cdcState', $parameters['cdcState'])
            ->setParameter('cdcCompliant', $cdcCompliant)
            ->setParameter('cdcToExport', $cdcToExport);

        return $qb->getQuery()->getSingleScalarResult();
    }

    /**
     * @param Certification $certification
     * @param string $certificateId
     * @return CertificationFolder|null
     */
    public function findOneByCertificationAndCertificateId(Certification $certification, string $certificateId): ?CertificationFolder
    {
        return $this->findOneBy(['certification' => $certification, 'certificateId' => $certificateId]);
    }

    /**
     * @param Certification $certification
     * @param string $firstName
     * @param string $lastName
     * @return ArrayCollection
     */
    public function findAllByCertificationAndAttendeeNames(Certification $certification, string $firstName, string $lastName): ArrayCollection
    {
        $cfStates = [
            CertificationFolderStates::TO_TAKE()->getValue(), CertificationFolderStates::TO_CONTROL()->getValue(), CertificationFolderStates::SUCCESS()->getValue()
        ];
        $qb = $this->createQueryBuilder('cf');
        $qb->andWhere($qb->expr()->eq('cf.certification', ':certification'))
            ->join('cf.attendee', 'att')
            ->andWhere($qb->expr()->eq('att.firstName', ':firstName'))
            ->andWhere($qb->expr()->eq('att.lastName', ':lastName'))
            ->andWhere($qb->expr()->in('cf.state', ':cfStates'))
            ->setParameter('certification', $certification)
            ->setParameter('firstName', $firstName)
            ->setParameter('lastName', $lastName)
            ->setParameter('cfStates', $cfStates);
        return new ArrayCollection($qb->getQuery()->getResult());
    }

    /**
     * @param Certification $certification
     * @param string $cdcTechnicalId
     * @return ArrayCollection
     */
    public function findAllByCertificationAndTechnicalId(Certification $certification, string $cdcTechnicalId): ArrayCollection
    {
        $cfStates = [
            CertificationFolderStates::TO_TAKE()->getValue(), CertificationFolderStates::TO_CONTROL()->getValue(), CertificationFolderStates::SUCCESS()->getValue()
        ];
        $qb = $this->createQueryBuilder('cf');
        $qb->andWhere($qb->expr()->eq('cf.certification', ':certification'))
            ->andWhere($qb->expr()->eq('cf.cdcTechnicalId', ':cdcTechnicalId'))
            ->andWhere($qb->expr()->in('cf.state', ':cfStates'))
            ->setParameter('certification', $certification)
            ->setParameter('cdcTechnicalId', $cdcTechnicalId)
            ->setParameter('cfStates', $cfStates);
        return new ArrayCollection($qb->getQuery()->getResult());
    }

    /**
     * @param Training $training
     * @return ArrayCollection
     */
    public function findAllByTraining(Training $training): ArrayCollection
    {
        $qb = $this->createQueryBuilder('cf');
        $qb->join('cf.registrationFolder', 'rf')
            ->join('rf.session', 's')
            ->join('s.trainingAction', 'ta')
            ->andWhere($qb->expr()->eq('ta.training', ':training'))
            ->setParameter('training', $training);
        return new ArrayCollection($qb->getQuery()->getResult());
    }

    /**
     * @param Attendee $attendee
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countAllNotCpfProcessedOkForAttendee(Attendee $attendee): int
    {
        $qb = $this->createQueryBuilder('certificationFolder');
        $qb->select('count(certificationFolder.id)')
            ->leftJoin('certificationFolder.registrationFolder', 'registrationFolder')
            ->where($qb->expr()->eq('certificationFolder.cdcState', ':processedOk'))
            ->andWhere($qb->expr()->eq('certificationFolder.attendee', ':attendee'))
            ->andWhere($qb->expr()->orX(
                $qb->expr()->isNull('certificationFolder.registrationFolder'),
                $qb->expr()->neq('registrationFolder.type', ':cpf')
            ))
            ->setParameter('processedOk', CertificationFolderCdcStates::PROCESSED_OK()->getValue())
            ->setParameter('attendee', $attendee)
            ->setParameter('cpf', DataProviders::CPF()->getValue());
        return $qb->getQuery()->getSingleScalarResult();
    }

    /**
     * @throws NonUniqueResultException
     * @throws NoResultException
     */
    public function computeCdcErrorCount(Organism $certifier, bool $generatedExternally): int
    {
        $stateProcessedOK = CertificationFoldersCdcFilesStates::PROCESSED_OK()->getValue();
        $stateProcessedKO = CertificationFoldersCdcFilesStates::PROCESSED_KO()->getValue();
        $cfCdcStateNotExported = CertificationFolderCdcStates::NOT_EXPORTED()->getValue();
        $alreadyExistsError = '%existe déjà en base';

        $qb = $this->createQueryBuilder('cf');

        $subQueryProcessedKo = $this->_em->createQueryBuilder();
        $subQueryProcessedKo->select('cfsCdcFile')
            ->from(CertificationFoldersCdcFiles::class, 'cfsCdcFile')
            ->innerJoin('cfsCdcFile.cdcFile', 'cdcFile')
            ->andWhere('cfsCdcFile.certificationFolder = cf') // Refer to cf from parent query
            ->andWhere('cfsCdcFile.state = :stateProcessedKO')
            ->andWhere($subQueryProcessedKo->expr()->orX(
                'cfsCdcFile.errorMessage IS NULL',
                'cfsCdcFile.errorMessage NOT LIKE :alreadyExistsError'
            ))
            ->andwhere('cdcFile.generatedExternally = :generatedExternally');

        $subQueryProcessedOk = $this->_em->createQueryBuilder();
        $subQueryProcessedOk->select('cfsCdcFile2')
            ->from(CertificationFoldersCdcFiles::class, 'cfsCdcFile2')
            ->innerJoin('cfsCdcFile2.cdcFile', 'cdcFile2')
            ->andWhere('cfsCdcFile2.certificationFolder = cf') // Refer to cf from parent query
            ->andWhere('cfsCdcFile2.state = :stateProcessedOK')
            ->andwhere('cdcFile2.generatedExternally = :generatedExternally');

        $qb->select('COUNT(DISTINCT cf.id)')
            ->andwhere('cf.cdcState != :cfCdcStateNotExported')
            ->andWhere('cf.certifier = :certifier')
            ->andWhere($qb->expr()->exists($subQueryProcessedKo->getDQL()))
            ->andWhere($qb->expr()->not($qb->expr()->exists($subQueryProcessedOk->getDQL())))
            ->setParameter('cfCdcStateNotExported', $cfCdcStateNotExported)
            ->setParameter('certifier', $certifier)
            ->setParameter('stateProcessedOK', $stateProcessedOK)
            ->setParameter('stateProcessedKO', $stateProcessedKO)
            ->setParameter('alreadyExistsError', $alreadyExistsError)
            ->setParameter('generatedExternally', $generatedExternally);

        $query = $qb->getQuery();
        return $query->getSingleScalarResult();
    }


    /**
     * @throws NonUniqueResultException
     * @throws NoResultException
     */
    public function computeCdcTotalCount(Organism $certifier, bool $generatedExternally): int
    {
        $stateProcessedOK = CertificationFoldersCdcFilesStates::PROCESSED_OK()->getValue();
        $stateProcessedKO = CertificationFoldersCdcFilesStates::PROCESSED_KO()->getValue();
        $cfCdcStateNotExported = CertificationFolderCdcStates::NOT_EXPORTED()->getValue();
        $alreadyExistsError = '%existe déjà en base';

        $subQuery = $this->_em->createQueryBuilder();
        $subQuery->select('cfsCdcFile')
            ->from(CertificationFoldersCdcFiles::class, 'cfsCdcFile')
            ->innerJoin('cfsCdcFile.cdcFile', 'cdcFile')
            ->andWhere('cfsCdcFile.certificationFolder = cf') // Refer to cf from parent query
            ->andWhere($subQuery->expr()->in('cfsCdcFile.state', ':states'))
            ->andWhere($subQuery->expr()->orX(
                'cfsCdcFile.errorMessage IS NULL',
                'cfsCdcFile.errorMessage NOT LIKE :alreadyExistsError'
            ))
            ->andwhere('cdcFile.generatedExternally = :generatedExternally');

        $qb = $this->createQueryBuilder('cf');
        $qb->select('COUNT(DISTINCT cf.id)')
            ->andwhere('cf.cdcState != :cfCdcStateNotExported')
            ->andWhere('cf.certifier = :certifier')
            ->andWhere($qb->expr()->exists($subQuery->getDQL()))
            ->setParameter('cfCdcStateNotExported', $cfCdcStateNotExported)
            ->setParameter('certifier', $certifier)
            ->setParameter('states', [$stateProcessedOK, $stateProcessedKO])
            ->setParameter('alreadyExistsError', $alreadyExistsError)
            ->setParameter('generatedExternally', $generatedExternally);

        $query = $qb->getQuery();
        return $query->getSingleScalarResult();
    }

    /**
     * @param Organism $organism
     * @param string $query
     * @return array
     * @throws Exception
     * @throws \Doctrine\DBAL\Exception
     */
    public function extractMetadataKeys(Organism $organism, string $query = ''): array
    {
        $conn = $this->getEntityManager()->getConnection();
        $sql = "SELECT DISTINCT JSON_UNQUOTE(json_key) as metadata FROM certification_folder, json_table( json_keys(metadata), '$[*]' COLUMNS(json_key JSON PATH '$') ) t WHERE certification_folder.metadata is not null AND certification_folder.certifier_id = :organism_id";
        if (!empty($query)) {
            $sql .= " AND JSON_UNQUOTE(json_key) LIKE :query";
        }
        $stmt = $conn->prepare($sql);
        $stmt->bindValue('organism_id', $organism->getId());
        if (!empty($query)) {
            $stmt->bindValue('query', '%' . $query . '%');
        }
        $result = $stmt->executeQuery();
        $metadata = [];
        foreach ($result->fetchAllAssociative() as $row) {
            $metadata[] = $row['metadata'];
        }
        return $metadata;
    }

    /**
     * @param Certification $certification
     * @param Organism|null $organism
     * @return array
     */
    public function findAllForCertificationAndDataProvider(Certification $certification, Organism $organism = null): array
    {
        $qb = $this->createQueryBuilder('cf');
        $qb->select('rf.type, count(cf.id) as count')
            ->leftJoin('cf.registrationFolder', 'rf')
            ->andWhere($qb->expr()->eq('cf.certification', ':certification'))
            ->setParameter('certification', $certification);
        if ($organism) {
            $qb->andWhere($qb->expr()->eq('cf.partner', ':partner'))
                ->setParameter('partner', $organism);
        }
        $qb->groupBy('rf.type');
        return $qb->getQuery()->getResult();
    }

    /**
     * @return array
     */
    public function listColumnConfigs(): array
    {
        return [
            [
                'columnId' => CertificationFolderStates::TO_REGISTER()->getValue(),
                'filter' => ['state' => [CertificationFolderStates::TO_REGISTER()->getValue()]],
                'title' => CertificationFolderStates::toFrString(CertificationFolderStates::TO_REGISTER()->getValue())
            ],
            [
                'columnId' => CertificationFolderStates::REGISTERED()->getValue(),
                'filter' => ['state' => [CertificationFolderStates::REGISTERED()->getValue()]],
                'title' => CertificationFolderStates::toFrString(CertificationFolderStates::REGISTERED()->getValue())
            ],
            [
                'columnId' => CertificationFolderStates::TO_TAKE()->getValue(),
                'filter' => ['state' => [CertificationFolderStates::TO_TAKE()->getValue()]],
                'title' => CertificationFolderStates::toFrString(CertificationFolderStates::TO_TAKE()->getValue())
            ],
            [
                'columnId' => CertificationFolderStates::TO_CONTROL()->getValue(),
                'filter' => ['state' => [CertificationFolderStates::TO_CONTROL()->getValue()]],
                'title' => CertificationFolderStates::toFrString(CertificationFolderStates::TO_CONTROL()->getValue())
            ],
            [
                'columnId' => CertificationFolderStates::SUCCESS()->getValue(),
                'filter' => ['state' => [CertificationFolderStates::SUCCESS()->getValue()]],
                'title' => CertificationFolderStates::toFrString(CertificationFolderStates::SUCCESS()->getValue())
            ],
            [
                'parentColumnId' => CertificationFolderStates::SUCCESS()->getValue(),
                'columnId' => CertificationFolderStates::SUCCESS()->getValue() . CertificationFolderCdcStates::NOT_EXPORTED()->getValue(),
                'filter' => [
                    'state' => [CertificationFolderStates::SUCCESS()->getValue()],
                    'cdcState' => [CertificationFolderCdcStates::NOT_EXPORTED()->getValue()],
                    'cdcCompliant' => 0,
                    'cdcExcluded' => false
                ],
                'title' => "Données manquantes accrochage"
            ],
            [
                'parentColumnId' => CertificationFolderStates::SUCCESS()->getValue(),
                'columnId' => CertificationFolderStates::SUCCESS()->getValue() . CertificationFolderCdcStates::PROCESSED_KO()->getValue(),
                'filter' => [
                    'state' => [CertificationFolderStates::SUCCESS()->getValue()],
                    'cdcState' => [CertificationFolderCdcStates::PROCESSED_KO()->getValue()]
                ],
                'title' => "Erreur accrochage"
            ],
            [
                'parentColumnId' => CertificationFolderStates::SUCCESS()->getValue(),
                'columnId' => CertificationFolderStates::SUCCESS()->getValue() . CertificationFolderCdcStates::EXPORTED()->getValue(),
                'filter' => [
                    'state' => [CertificationFolderStates::SUCCESS()->getValue()],
                    'cdcState' => [CertificationFolderCdcStates::EXPORTED()->getValue()]
                ],
                'title' => "En traitement acrochage"
            ],
            [
                'parentColumnId' => CertificationFolderStates::SUCCESS()->getValue(),
                'columnId' => CertificationFolderStates::SUCCESS()->getValue() . CertificationFolderCdcStates::PROCESSED_OK()->getValue(),
                'filter' => [
                    'state' => [CertificationFolderStates::SUCCESS()->getValue()],
                    'cdcState' => [CertificationFolderCdcStates::PROCESSED_OK()->getValue()]
                ],
                'title' => "Accrochés"
            ],
            [
                'columnId' => CertificationFolderStates::TO_RETAKE()->getValue(),
                'filter' => ['state' => [CertificationFolderStates::TO_RETAKE()->getValue()]],
                'title' => CertificationFolderStates::toFrString(CertificationFolderStates::TO_RETAKE()->getValue())
            ],
            [
                'columnId' => CertificationFolderStates::FAILED()->getValue(),
                'filter' => ['state' => [CertificationFolderStates::FAILED()->getValue()]],
                'title' => CertificationFolderStates::toFrString(CertificationFolderStates::FAILED()->getValue())
            ],
            [
                'columnId' => CertificationFolderStates::REFUSED()->getValue(),
                'filter' => ['state' => [CertificationFolderStates::REFUSED()->getValue()]],
                'title' => CertificationFolderStates::toFrString(CertificationFolderStates::REFUSED()->getValue())
            ],
            [
                'columnId' => CertificationFolderStates::ABORTED()->getValue(),
                'filter' => ['state' => [CertificationFolderStates::ABORTED()->getValue()]],
                'title' => CertificationFolderStates::toFrString(CertificationFolderStates::ABORTED()->getValue())
            ],
            [
                'parentColumnId' => CertificationFolderStates::ABORTED()->getValue(),
                'columnId' => CertificationFolderStates::ABORTED()->getValue() . "WithoutTraining",
                'filter' => [
                    'state' => [CertificationFolderStates::ABORTED()->getValue()],
                    'withoutRegistrationFolder' => true
                ],
                'title' => "Sans formation"
            ],
            [
                'parentColumnId' => CertificationFolderStates::ABORTED()->getValue(),
                'columnId' => CertificationFolderStates::ABORTED()->getValue() . RegistrationFolderStates::CANCELED_BY_ATTENDEE_NOT_REALIZED()->getValue() . RegistrationFolderStates::CANCELED_BY_ORGANISM()->getValue(),
                'filter' => [
                    'state' => [CertificationFolderStates::ABORTED()->getValue()],
                    'registrationFolderState' => [RegistrationFolderStates::CANCELED_BY_ATTENDEE_NOT_REALIZED()->getValue(), RegistrationFolderStates::CANCELED_BY_ORGANISM()->getValue()]
                ],
                'title' => "Avant la formation"
            ],
            [
                'parentColumnId' => CertificationFolderStates::ABORTED()->getValue(),
                'columnId' => CertificationFolderStates::ABORTED()->getValue() . "InTraining",
                'filter' => [
                    'state' => [CertificationFolderStates::ABORTED()->getValue()],
                    'registrationFolderCompletionRate' => '<80',
                    'registrationFolderState' => [RegistrationFolderStates::NOT_PROCESSED()->getValue(),
                        RegistrationFolderStates::VALIDATED()->getValue(),
                        RegistrationFolderStates::WAITING_ACCEPTATION()->getValue(),
                        RegistrationFolderStates::ACCEPTED()->getValue(),
                        RegistrationFolderStates::IN_TRAINING()->getValue(),
                        RegistrationFolderStates::TERMINATED()->getValue(),
                        RegistrationFolderStates::SERVICE_DONE_DECLARED()->getValue(),
                        RegistrationFolderStates::SERVICE_DONE_VALIDATED()->getValue(),
                        RegistrationFolderStates::CANCELED_BY_ATTENDEE()->getValue(),
                        RegistrationFolderStates::CANCELED_BY_FINANCER()->getValue(),
                        RegistrationFolderStates::CANCELED_BY_CDC()->getValue(),
                        RegistrationFolderStates::REFUSED_BY_ATTENDEE()->getValue(),
                        RegistrationFolderStates::REFUSED_BY_ORGANISM()->getValue(),
                        RegistrationFolderStates::REFUSED_BY_FINANCER()->getValue(),
                        RegistrationFolderStates::REJECTED_WITHOUT_TITULAIRE_SUITE()->getValue(),
                        RegistrationFolderStates::REJECTED_WITHOUT_CDC_SUITE()->getValue(),
                        RegistrationFolderStates::REJECTED_WITHOUT_OF_SUITE()->getValue(),
                        RegistrationFolderStates::REJECTED()->getValue()]
                ],
                'title' => "Pendant la formation"
            ],
            [
                'parentColumnId' => CertificationFolderStates::ABORTED()->getValue(),
                'columnId' => CertificationFolderStates::ABORTED()->getValue() . "AfterTraining",
                'filter' => [
                    'state' => [CertificationFolderStates::ABORTED()->getValue()],
                    'registrationFolderCompletionRate' => '>80',
                    'registrationFolderState' => [RegistrationFolderStates::NOT_PROCESSED()->getValue(),
                        RegistrationFolderStates::VALIDATED()->getValue(),
                        RegistrationFolderStates::WAITING_ACCEPTATION()->getValue(),
                        RegistrationFolderStates::ACCEPTED()->getValue(),
                        RegistrationFolderStates::IN_TRAINING()->getValue(),
                        RegistrationFolderStates::TERMINATED()->getValue(),
                        RegistrationFolderStates::SERVICE_DONE_DECLARED()->getValue(),
                        RegistrationFolderStates::SERVICE_DONE_VALIDATED()->getValue(),
                        RegistrationFolderStates::CANCELED_BY_ATTENDEE()->getValue(),
                        RegistrationFolderStates::CANCELED_BY_FINANCER()->getValue(),
                        RegistrationFolderStates::CANCELED_BY_CDC()->getValue(),
                        RegistrationFolderStates::REFUSED_BY_ATTENDEE()->getValue(),
                        RegistrationFolderStates::REFUSED_BY_ORGANISM()->getValue(),
                        RegistrationFolderStates::REFUSED_BY_FINANCER()->getValue(),
                        RegistrationFolderStates::REJECTED_WITHOUT_TITULAIRE_SUITE()->getValue(),
                        RegistrationFolderStates::REJECTED_WITHOUT_CDC_SUITE()->getValue(),
                        RegistrationFolderStates::REJECTED_WITHOUT_OF_SUITE()->getValue(),
                        RegistrationFolderStates::REJECTED()->getValue()]
                ],
                'title' => "Après la formation"
            ]
        ];
    }

    //-----------------
    // METHODES PRIVEES
    //-----------------
}
