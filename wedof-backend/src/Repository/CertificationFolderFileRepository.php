<?php

namespace App\Repository;

use App\Entity\Certification;
use App\Entity\CertificationFolderFile;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method CertificationFolderFile|null find($id, $lockMode = null, $lockVersion = null)
 * @method CertificationFolderFile|null findOneBy(array $criteria, array $orderBy = null)
 * @method CertificationFolderFile[]    findAll()
 * @method CertificationFolderFile[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class CertificationFolderFileRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, CertificationFolderFile::class);
    }

    /**
     * @param CertificationFolderFile $certificationFolderFile
     * @return CertificationFolderFile
     */
    public function save(CertificationFolderFile $certificationFolderFile): CertificationFolderFile
    {
        if (!$this->_em->contains($certificationFolderFile)) {
            $this->_em->persist($certificationFolderFile);
        }
        $this->_em->flush();
        return $certificationFolderFile;
    }

    /**
     * @param CertificationFolderFile $certificationFolderFile
     */
    public function delete(CertificationFolderFile $certificationFolderFile): void
    {
        $this->_em->remove($certificationFolderFile);
        $this->_em->flush();
    }

    /**
     * @param Certification $certification
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function findMaxTypeId(Certification $certification): int
    {
        $qb = $this->createQueryBuilder('cfFile');
        $qb->select('MAX(cfFile.typeId)')
            ->join('cfFile.certificationFolder', 'certificationFolder')
            ->join('certificationFolder.certification', 'certification')
            ->andWhere($qb->expr()->neq('cfFile.typeId', ':typeId'))
            ->andWhere($qb->expr()->eq('certification', ':certification'))
            ->setParameter('certification', $certification)
            ->setParameter('typeId', Certification::CERTIFICATE_FILE_TYPE_ID);
        return $qb->getQuery()->getSingleScalarResult() ?? 0;
    }

    /**
     * @param Certification|null $certification
     * @param int $typeId
     * @return float|int|mixed|string
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countFilesWithTypeId(?Certification $certification, int $typeId)
    {
        $qb = $this->createQueryBuilder('cfFile');
        $qb->select('count(cfFile.typeId)')
            ->join('cfFile.certificationFolder', 'certificationFolder')
            ->join('certificationFolder.certification', 'certification')
            ->andWhere($qb->expr()->eq('certification', ':certification'))
            ->andWhere($qb->expr()->eq('cfFile.typeId', ':typeId'))
            ->setParameter('certification', $certification)
            ->setParameter('typeId', $typeId);
        return $qb->getQuery()->getSingleScalarResult() ?? 0;
    }
}
