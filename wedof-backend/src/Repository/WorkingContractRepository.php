<?php

namespace App\Repository;

use App\Entity\Organism;
use App\Entity\WorkingContract;
use App\Library\utils\enums\DataProviders;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<WorkingContract>
 *
 * @method WorkingContract|null find($id, $lockMode = null, $lockVersion = null)
 * @method WorkingContract|null findOneBy(array $criteria, array $orderBy = null)
 * @method WorkingContract[]    findAll()
 * @method WorkingContract[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class WorkingContractRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, WorkingContract::class);
    }

    /**
     * @param WorkingContract $workingContract
     * @return WorkingContract
     */
    public function save(WorkingContract $workingContract): WorkingContract
    {
        if (!$this->_em->contains($workingContract)) {
            $this->_em->persist($workingContract);
        }
        $this->_em->flush();
        return $workingContract;
    }

    /**
     * @param Organism $organism
     * @param array $parameters
     * @return QueryBuilder
     */
    public function findAllReturnQueryBuilder(Organism $organism, array $parameters): QueryBuilder
    {
        $qb = $this->createQueryBuilder('wc');

        $qb->join('wc.registrationFolder', 'rf');
        $qb->andWhere($qb->expr()->eq('rf.organism', ':organism'))
            ->setParameter('organism', $organism);

        if (!empty($parameters['registrationFolder'])) {
            $qb->andWhere($qb->expr()->eq('wc.registrationFolder', ':registrationFolder')) // TODO(opco) try just rf
            ->setParameter('registrationFolder', $parameters['registrationFolder']);
        }

        if (!empty($parameters['certifInfo'])) {
            $qb->join('rf.certification', 'certification');
            $qb->andWhere($qb->expr()->eq('certification.certifInfo', ':certifInfo'))
                ->setParameter('certifInfo', $parameters['certifInfo']);
        }

        if (isset($parameters['type'])) {
            $qb->andWhere($qb->expr()->in('wc.type', ':type'))
                ->setParameter('type', $parameters['type']);
        }

        if (!in_array('all', $parameters['state'])) {
            $qb->andWhere($qb->expr()->in('wc.state', ':state'))
                ->setParameter('state', $parameters['state']);
        }

        if (isset($parameters['financer'])) {
            $qb->andWhere($qb->expr()->in('wc.financer', ':financer'))
                ->setParameter('financer', $parameters['financer']);
        }

        if (isset($parameters['employer'])) {
            $qb->andWhere($qb->expr()->eq('wc.employer', ':employer'))
                ->setParameter('employer', $parameters['employer']);
        }

        $qb->addOrderBy('wc.startDate', 'DESC');
        $qb->addOrderBy('wc.id', 'DESC');
        return $qb;
    }

    /**
     * @param array $externalIdsFinancer
     * @param DataProviders $financer
     * @param Organism $organism
     * @return array
     */
    public function findAllIdsByExternalIdsFinancer(array $externalIdsFinancer, DataProviders $financer, Organism $organism): array
    {
        $qb = $this->createQueryBuilder('wc');
        $qb->select('wc.id, wc.externalIdFinancer, wc.state, wc.lastUpdate');
        $qb->join('wc.registrationFolder', 'rf'); // TODO(opco) maybe put organism on workingContract to avoid a join
        $qb->andWhere($qb->expr()->in('wc.externalIdFinancer', ':externalIdsFinancer'))
            ->andWhere($qb->expr()->eq('wc.financer', ':financer'))
            ->andWhere($qb->expr()->eq('rf.organism', ':organism'))
            ->setParameter('externalIdsFinancer', $externalIdsFinancer)
            ->setParameter('financer', $financer->getValue())
            ->setParameter('organism', $organism);
        return $qb->getQuery()->getResult();
    }
}
