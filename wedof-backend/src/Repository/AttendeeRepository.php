<?php

namespace App\Repository;

use App\Entity\Attendee;
use App\Entity\CertificationFolder;
use App\Entity\Organism;
use App\Entity\RegistrationFolder;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method Attendee|null find($id, $lockMode = null, $lockVersion = null)
 * @method Attendee|null findOneBy(array $criteria, array $orderBy = null)
 * @method Attendee[]    findAll()
 * @method Attendee[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class AttendeeRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Attendee::class);
    }

    /**
     * @param string $email
     * @return Attendee|null
     */
    public function findOneByEmail(string $email): ?Attendee
    {
        return $this->findOneBy(array("email" => $email));
    }

    /**
     * @param string $phoneNumber
     * @return Attendee|null
     */
    public function findOneByPhone(string $phoneNumber): ?Attendee
    {
        return $this->findOneBy(array("phoneNumber" => $phoneNumber));
    }

    /**
     * @param string $externalId
     * @return Attendee|null
     */
    public function findOneByExternalId(string $externalId): ?Attendee
    {
        return $this->findOneBy(array("externalId" => $externalId));
    }

    /**
     * @param array $parameters
     * @return ArrayCollection
     */
    public function findByEmailOrPhone(array $parameters): ArrayCollection
    {
        $parameters['query'] = str_replace(' ', '', $parameters['query']);
        $parameters['query'] = str_replace('+33', '0', $parameters['query']);
        $qb = $this->createQueryBuilder('att');

        $qb->where($qb->expr()->orX(
            $qb->expr()->eq("att.phoneNumber", ':query'),
            $qb->expr()->eq("att.email", ':query')
        ))
            ->setParameter('query', $parameters['query'])
            ->orderBy('att.id', 'ASC');

        return new ArrayCollection($qb->getQuery()->getResult());
    }

    /**
     * @param Attendee $attendee
     * @return Attendee
     */
    public function save(Attendee $attendee): Attendee
    {
        $attendee->refreshCdcCompliant();
        if (!$this->_em->contains($attendee)) {
            $this->_em->persist($attendee);
        }
        $this->_em->flush();
        return $attendee;
    }

    /**
     * @param Organism $organism
     * @param array $parameters
     * @return QueryBuilder
     */
    public function findAllReturnQueryBuilder(Organism $organism, array $parameters): QueryBuilder
    {
        $qb = $this->createQueryBuilder('att');

        $qb->join('att.registrationFolders', 'rf')
            ->where($qb->expr()->eq('rf.organism', ':organism'))
            ->setParameter('organism', $organism);

        if (isset($parameters['query'])) {
            $qb->andWhere($qb->expr()->orX(
                $qb->expr()->like("att.firstName", ':query'),
                $qb->expr()->like("att.lastName", ':query'),
                $qb->expr()->like("CONCAT(att.firstName, ' ', att.lastName)", ':query'),
                $qb->expr()->like("att.email", ':query')
            ))
                ->setParameter('query', '%' . trim($parameters['query']) . '%');
        }

        $qb->orderBy('att.' . $parameters['sort'], $parameters['order']);
        $qb->addOrderBy('att.id', 'ASC');
        return $qb;
    }

    /**
     * @param array $rawData
     * @return Attendee|null
     * @throws NonUniqueResultException
     */
    public function findOneByRawData(array $rawData): ?Attendee
    {
        $firstName = $rawData['firstName'] ?? "";
        $lastName = $rawData['lastName'] ?? "";
        $poleEmploiId = $rawData['poleEmploiId'] ?? "";

        $qb = $this->createQueryBuilder('att');
        $qb->where(
            $qb->expr()->andX(
                $qb->expr()->eq('att.email', ':email'),
                $qb->expr()->eq('att.lastName', ':lastName'),
                $qb->expr()->eq('att.firstName', ':firstName')
            ))
            ->orWhere($qb->expr()->eq('att.poleEmploiId', ':poleEmploiId'))
            ->setParameter('email', strtolower($rawData['email']))
            ->setParameter('lastName', mb_strtolower($lastName)) //to lower "accents" also..
            ->setParameter('firstName', mb_strtolower($firstName)) //to lower "accents" also..
            ->setParameter('poleEmploiId', mb_strtolower($poleEmploiId)) //to lower 
            ->orderBy('att.id', 'ASC')
            ->setMaxResults(1);

        return $qb->getQuery()->getOneOrNullResult();
    }

    /**
     * @param string $poleEmploiId
     * @return Attendee|null
     */
    public function findByOnePoleEmploiId(string $poleEmploiId): ?Attendee
    {
        return $this->findOneBy(array("poleEmploiId" => $poleEmploiId));
    }

    /**
     * @param Attendee $attendee
     * @return array
     */
    public function findAllDistinctOwnerPairs(Attendee $attendee): array
    {
        $qb = $this->createQueryBuilder('att');
        $qb->select('IDENTITY(cf.certifier), IDENTITY(rf.organism)') // IDENTITY allow to retrieve only the ID so that we don't have to perform a JOIN
        ->leftJoin(CertificationFolder::class, 'cf', Join::WITH, $qb->expr()->eq('att.id', 'cf.attendee'))
            ->leftJoin(RegistrationFolder::class, 'rf', Join::WITH, $qb->expr()->eq('att.id', 'rf.attendee'))
            ->where($qb->expr()->eq('att', ':attendee'))
            ->setParameter('attendee', $attendee)
            ->distinct();
        return $qb->getQuery()->getResult();
    }
}
