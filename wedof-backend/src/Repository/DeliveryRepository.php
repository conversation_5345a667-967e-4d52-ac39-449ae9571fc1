<?php

namespace App\Repository;

use App\Entity\Delivery;
use App\Entity\Webhook;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;
use Throwable;

/**
 * @method Delivery|null find($id, $lockMode = null, $lockVersion = null)
 * @method Delivery|null findOneBy(array $criteria, array $orderBy = null)
 * @method Delivery[]    findAll()
 * @method Delivery[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class DeliveryRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Delivery::class);
    }

    /**
     * @param Delivery $delivery
     * @return Delivery
     * @throws Throwable
     */
    public function save(Delivery $delivery): Delivery
    {
        if (!$this->_em->contains($delivery)) {
            $this->_em->persist($delivery);
        }
        $this->_em->flush();
        return $delivery;
    }

    /**
     * @param Delivery $delivery
     * @return void
     * @throws Throwable
     */
    public function delete(Delivery $delivery): void
    {
        $this->_em->remove($delivery);
        $this->_em->flush();
    }

    /**
     * @param Webhook $webhook
     * @param $statusCodes
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countByWebhookAndStatusCode(Webhook $webhook, $statusCodes): int
    {
        $qb = $this->createQueryBuilder('dl');
        $qb->select('count(dl.id)');
        $qb->where($qb->expr()->eq('dl.webhook', ':webhook'));
        $qb->setParameter('webhook', $webhook);
        $qb->andWhere($qb->expr()->in('dl.statusCode', $statusCodes));

        return $qb->getQuery()->getSingleScalarResult();
    }


    /**
     * @param $entityId
     * @param string $entityClass
     * @param string $type
     * @return QueryBuilder
     */
    public function listDeliveriesByEntityAndType($entityId, string $entityClass, string $type): QueryBuilder
    {
        $qb = $this->createQueryBuilder('dl');
        $qb->join('dl.webhook', 'webhook');

        $qb->where($qb->expr()->eq('dl.entityClass', ':entityClass'))
            ->setParameter('entityClass', $entityClass);

        $qb->andWhere($qb->expr()->eq('webhook.type', ':type'))
            ->setParameter('type', $type);

        $qb->andWhere($qb->expr()->eq("dl.entityId", ':entityId'))
            ->setParameter('entityId', $entityId);

        $qb->addOrderBy('dl.date', 'DESC');
        $qb->addOrderBy('dl.id', 'ASC');

        return $qb;
    }
}
