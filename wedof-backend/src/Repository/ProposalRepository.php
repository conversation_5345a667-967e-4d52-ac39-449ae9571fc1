<?php

namespace App\Repository;

use App\Entity\Organism;
use App\Entity\Proposal;
use App\Entity\RegistrationFolder;
use App\Library\utils\enums\ProposalDiscountTypes;
use App\Library\utils\enums\ProposalStates;
use App\Library\utils\Tools;
use DateTime;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;
use Exception;

/**
 * @method Proposal|null find($id, $lockMode = null, $lockVersion = null)
 * @method Proposal|null findOneBy(array $criteria, array $orderBy = null)
 * @method Proposal[]    findAll()
 * @method Proposal[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ProposalRepository extends ServiceEntityRepository

{

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Proposal::class);
    }

    /**
     * @param Proposal $proposal
     * @return Proposal
     */
    public function save(Proposal $proposal): Proposal
    {
        if (!$this->_em->contains($proposal)) {
            $this->_em->persist($proposal);
        }
        $this->_em->flush();
        return $proposal;
    }

    /**
     * @param $organism
     * @param string $code
     * @return Proposal|null
     * @throws NonUniqueResultException
     */
    public function findByOrganismAndCode($organism, string $code): ?Proposal
    {
        $qb = $this->createQueryBuilder('p');
        $qb->where($qb->expr()->eq('p.organism', ':organism'))
            ->setParameter('organism', $organism);
        $qb->andWhere($qb->expr()->eq('p.code', ':code'))
            ->setParameter('code', $code);
        $qb->andWhere(
            $qb->expr()->orX(
                $qb->expr()->gte('p.expire', ':date'),
                $qb->expr()->isNull('p.expire')
            )
        )->setParameter('date', new DateTime());
        return $qb->getQuery()->getOneOrNullResult();
    }

    /**
     * @param $organism
     * @param string $code
     * @param array $states
     * @return Proposal|null
     * @throws NonUniqueResultException
     */
    public function findIndividualByOrganismAndCodeAndInStates($organism, string $code, array $states = []): ?Proposal
    {
        $qb = $this->createQueryBuilder('p');
        $qb->where($qb->expr()->eq('p.organism', ':organism'))
            ->setParameter('organism', $organism);
        if (!empty($states)) {
            $qb->andWhere($qb->expr()->in('p.state', ':states'))
                ->setParameter('states', $states);
        }
        $qb->andWhere($qb->expr()->eq('p.code', ':code'))
            ->setParameter('code', $code);
        $qb->andWhere(
            $qb->expr()->orX(
                $qb->expr()->gte('p.expire', ':date'),
                $qb->expr()->isNull('p.expire')
            )
        )->setParameter('date', new DateTime());
        $qb->andWhere(
            $qb->expr()->andX(
                $qb->expr()->isNotNull('p.email')
            )
        );
        return $qb->getQuery()->getOneOrNullResult();
    }

    /**
     * @param $organism
     * @param string $code
     * @return Proposal|null
     * @throws NonUniqueResultException
     */
    public function findGenericByOrganismAndCode($organism, string $code): ?Proposal
    {
        $qb = $this->createQueryBuilder('p');
        $qb->where($qb->expr()->eq('p.organism', ':organism'))
            ->setParameter('organism', $organism);
        $qb->andWhere($qb->expr()->eq('p.code', ':code'))
            ->setParameter('code', $code);
        $qb->andWhere(
            $qb->expr()->orX(
                $qb->expr()->gte('p.expire', ':date'),
                $qb->expr()->isNull('p.expire')
            )
        )->setParameter('date', new DateTime());
        $qb->andWhere($qb->expr()->isNull('p.email'));
        return $qb->getQuery()->getOneOrNullResult();
    }

    /**
     * @param RegistrationFolder $registrationFolder
     * @return Proposal|null
     * @throws Exception
     */
    public function findByRegistrationFolder(RegistrationFolder $registrationFolder): ?Proposal
    {
        $qb = $this->createQueryBuilder('p');

        //organism first
        $qb->where($qb->expr()->eq('p.organism', ':organism'))
            ->setParameter('organism', $registrationFolder->getOrganism());

        //only registration date > createdOn
        $qb->andWhere($qb->expr()->lt('p.createdOn', ':date_cr'))
            ->setParameter('date_cr', $registrationFolder->getCreatedOn());

        //only if a specific  > NOW
        $qb->andWhere(
            $qb->expr()->orX(
                $qb->expr()->eq('p.selectedTrainingAction', ':trainingAction'),
                $qb->expr()->isNull('p.selectedTrainingAction'),
                $qb->expr()->isMemberOf(':trainingAction', 'p.trainingActions')
            )
        )->setParameter('trainingAction', $registrationFolder->getSession()->getTrainingAction());

        //only expire > NOW
        $qb->andWhere(
            $qb->expr()->orX(
                $qb->expr()->gte('p.expire', ':date'),
                $qb->expr()->isNull('p.expire')
            )
        )->setParameter('date', new DateTime());

        //only for this attendee with email or phoneNumber
        $qb->andWhere(
            $qb->expr()->orX(
                $qb->expr()->like('p.email', ':email'),
                $qb->expr()->like('p.phoneNumber', ':phoneNumber')
            )
        );

        $qb->andWhere($qb->expr()->in('p.state', ':states'))
            ->setParameter('states', [ProposalStates::ACTIVE()->getValue(), ProposalStates::VIEWED()->getValue()])
            ->setParameter('email', $registrationFolder->getAttendee()->getEmail())
            ->setParameter('phoneNumber', $registrationFolder->getAttendee()->getPhoneNumber());

        $qb->orderBy('p.createdOn', 'DESC');
        $proposals = $qb->getQuery()->getResult();

        if (sizeof($proposals) == 1) {
            return $proposals[0];
        } else if (sizeof($proposals) == 0) {
            return null;
        }

        //filter by valid proposals
        $proposals = array_values(array_filter($proposals, function ($proposal) {
            /** @var $proposal Proposal */
            if ($proposal->isValid()) {
                return $proposal;
            } else {
                return false;
            }
        }));

        if (sizeof($proposals) == 1) {
            return $proposals[0];
        } else if (sizeof($proposals) == 0) {
            return null;
        } else {
            //try to get proposal with selected training action
            $maybe_a_proposal = array_values(array_filter($proposals, function ($proposal) use ($registrationFolder) {
                /** @var $proposal Proposal */
                if ($proposal->getSelectedTrainingAction() && $proposal->getSelectedTrainingAction()->getExternalId() == $registrationFolder->getSession()->getTrainingAction()->getExternalId()) {
                    return $proposal;
                } else {
                    return false;
                }
            }));
            if (sizeof($maybe_a_proposal) > 0) {
                return $maybe_a_proposal[0];
            }
        }

        //then filter by training actions more specific to less specific
        usort($proposals, function ($a, $b) {
            /** @var Proposal $a */
            /** @var Proposal $b */
            $countA = $a->getTrainingActions()->count();
            $countB = $b->getTrainingActions()->count();
            if ($countA == $countB) {
                if ($a->getCreatedOn() > $b->getCreatedOn()) {
                    return 1;
                }
            }
            return ($countA < $countB) ? -1 : 1;
        });

        $the_proposal = null;
        foreach ($proposals as $proposal) {
            /** @var $proposal Proposal */
            if ($proposal->getTrainingActions()->contains($registrationFolder->getSession()->getTrainingAction())) {
                $the_proposal = $proposal;
                break;
            }
        }

        if ($the_proposal) {
            return $the_proposal;
        } else {
            return $proposals[0] ?? null;
        }
    }


    /**
     * @param RegistrationFolder $registrationFolder
     * @return Proposal|null
     * @throws \Exception
     */
    public function findByNoCodeRequired(RegistrationFolder $registrationFolder): ?Proposal
    {
        $qb = $this->createQueryBuilder('p');

        //organism first
        $qb->where($qb->expr()->eq('p.organism', ':organism'))
            ->setParameter('organism', $registrationFolder->getOrganism());

        //only registration date > createdOn
        $qb->andWhere($qb->expr()->lt('p.createdOn', ':date_cr'))
            ->setParameter('date_cr', $registrationFolder->getCreatedOn());

        //only if a specific  > NOW
        $qb->andWhere(
            $qb->expr()->orX(
                $qb->expr()->eq('p.selectedTrainingAction', ':trainingAction'),
                $qb->expr()->isNull('p.selectedTrainingAction'),
                $qb->expr()->isMemberOf(':trainingAction', 'p.trainingActions')
            )
        )->setParameter('trainingAction', $registrationFolder->getSession()->getTrainingAction());

        //only expire > NOW
        $qb->andWhere(
            $qb->expr()->orX(
                $qb->expr()->gte('p.expire', ':date'),
                $qb->expr()->isNull('p.expire')
            )
        )->setParameter('date', new DateTime());

        $qb->andWhere($qb->expr()->eq('p.codeRequired', ':codeRequired'))
            ->setParameter(':codeRequired', false);

        $qb->andWhere($qb->expr()->eq('p.state', ':states'))
            ->setParameter('states', ProposalStates::TEMPLATE()->getValue());

        $qb->orderBy('p.createdOn', 'DESC');
        $proposals = $qb->getQuery()->getResult();

        if (sizeof($proposals) == 1) {
            return $proposals[0];
        } else if (sizeof($proposals) == 0) {
            return null;
        }

        //filter by valid proposals
        $proposals = array_values(array_filter($proposals, function ($proposal) {
            /** @var $proposal Proposal */
            if ($proposal->isValid()) {
                return $proposal;
            } else {
                return false;
            }
        }));

        if (sizeof($proposals) == 1) {
            return $proposals[0];
        } else if (sizeof($proposals) == 0) {
            return null;
        } else {
            //try to get proposal with selected training action
            $maybe_a_proposal = array_values(array_filter($proposals, function ($proposal) use ($registrationFolder) {
                /** @var $proposal Proposal */
                if ($proposal->getSelectedTrainingAction() && $proposal->getSelectedTrainingAction()->getExternalId() == $registrationFolder->getSession()->getTrainingAction()->getExternalId()) {
                    return $proposal;
                } else {
                    return false;
                }
            }));
            if (sizeof($maybe_a_proposal) > 0) {
                return $maybe_a_proposal[0];
            }
        }

        //then filter by training actions more specific to less specific
        usort($proposals, function ($a, $b) {
            /** @var Proposal $a */
            /** @var Proposal $b */
            $countA = $a->getTrainingActions()->count();
            $countB = $b->getTrainingActions()->count();
            if ($countA == $countB) {
                if ($a->getCreatedOn() > $b->getCreatedOn()) {
                    return 1;
                }
            }
            return ($countA < $countB) ? -1 : 1;
        });

        $the_proposal = null;
        foreach ($proposals as $proposal) {
            /** @var $proposal Proposal */
            if ($proposal->getTrainingActions()->contains($registrationFolder->getSession()->getTrainingAction())) {
                $the_proposal = $proposal;
                break;
            }
        }

        if ($the_proposal) {
            return $the_proposal;
        } else {
            return $proposals[0] ?? null;
        }
    }

    /**
     * @param Organism $organism
     * @param array $parameters
     * @return QueryBuilder
     */
    public function findAllReturnQueryBuilder(Organism $organism, array $parameters): QueryBuilder
    {
        if (isset($parameters['columnId'])) {
            $columnId = $parameters['columnId'];
            $columnParameters = $parameters; // Affectation copies the array - We use a copy to avoid overriding $parameters
            if (in_array(ProposalStates::ALL()->getValue(), $parameters['state'])) {
                $columnParameters['state'] = ProposalStates::valuesStatesToString(); // Hack because of "all" fake state
            }
            $parameters = Tools::computeKanbanColumnParameters($columnParameters, $this->listColumnConfigs(), $columnId);
        }

        $parameters = array_merge(['sort' => 'stateLastUpdate', 'order' => 'desc'], $parameters);
        $qb = $this->createQueryBuilder('p');
        $qb->where($qb->expr()->eq('p.organism', ':organism'))
            ->setParameter('organism', $organism);
        if (isset($parameters['isIndividual'])) {
            $isIndividual = filter_var($parameters['isIndividual'], FILTER_VALIDATE_BOOLEAN);
            $isIndividual === true ? $qb->andWhere($qb->expr()->isNotNull('p.email')) : $qb->andWhere($qb->expr()->isNull('p.email'));
        }
        if (!empty($parameters['metadata']) && sizeof($parameters['metadata']) <= 2) {
            if (!empty($parameters['metadata'][1])) {
                $metadata = '"' . $parameters['metadata'][0] . '": "' . $parameters['metadata'][1] . '"';  //one value with metadata key
            } else {
                $metadata = '"' . $parameters['metadata'][0] . '":'; //all values with metadata key
            }
            $qb->andWhere($qb->expr()->like('p.metadata', ':metadata'));
            $qb->setParameter('metadata', '%' . $metadata . '%');
        }
        if (!empty($parameters['query'])) {
            $qb->leftJoin('p.trainingActions', 'ta')
                ->leftJoin('ta.training', 't')
                ->leftJoin('p.tags', 'tag')
                ->leftJoin('p.selectedTrainingAction', 'ta2')
                ->leftJoin('ta2.training', 't2')
                ->andWhere($qb->expr()->orX(
                    $qb->expr()->like('p.code', ':query'),
                    $qb->expr()->like('p.email', ':query'),
                    $qb->expr()->like('p.lastName', ':query'),
                    $qb->expr()->like('p.firstName', ':query'),
                    $qb->expr()->like('p.phoneNumber', ':query'),
                    $qb->expr()->like("CONCAT(p.firstName, ' ', p.lastName)", ':query'),
                    $qb->expr()->like('p.notes', ':query'),
                    $qb->expr()->like('p.description', ':query'),
                    $qb->expr()->like('ta.externalId', ':query'),
                    $qb->expr()->like('t.title', ':query'),
                    $qb->expr()->like('tag.name', ':query'),
                    $qb->expr()->like('ta2.externalId', ':query'),
                    $qb->expr()->like('t2.title', ':query'),
                ))
                ->setParameter('query', '%' . trim($parameters['query']) . '%');
        }
        if (!in_array('all', $parameters['state'])) {
            $qb->andWhere($qb->expr()->in('p.state', ':state'))
                ->setParameter('state', $parameters['state']);
        }
        if (in_array($parameters['sort'], ['stateLastUpdate', 'id'])) {
            $qb->orderBy('p.' . $parameters['sort'], $parameters['order']);
        }
        $qb->addOrderBy('p.id', 'ASC');
        return $qb;
    }

    /**
     * @param Organism $organism
     * @param array $parameters
     * @param array $columnIds
     * @return ArrayCollection
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function findAllByColumn(Organism $organism, array $parameters, array $columnIds): ArrayCollection
    {
        $tva = $organism->getVat() ?? 0.0;
        $columns = [];
        foreach ($columnIds as $columnId) {
            $columnParameters = $parameters; // Affectation copies the array - We use a copy to avoid overriding $parameters
            $columnParameters['columnId'] = $columnId;
            $qb = $this->findAllReturnQueryBuilder($organism, $columnParameters);
            $items = (clone $qb)->distinct()->setMaxResults($columnParameters['limit'])->getQuery()->getResult();
            $total = (int)(clone $qb)->select($qb->expr()->count('DISTINCT(p.id)'))->getQuery()->getSingleScalarResult();

            // For revenue, we need to compute the parameters here even if it is also done in findAllReturnQueryBuilder
            if (in_array(ProposalStates::ALL()->getValue(), $columnParameters['state'])) {
                $columnParameters['state'] = ProposalStates::valuesStatesToString(); // Hack because of "all" fake state
            }
            $mergedParameters = Tools::computeKanbanColumnParameters($columnParameters, $this->listColumnConfigs(), $columnId);
            $states = $mergedParameters['state'];
            $revenue = $this->getRevenueByStates(clone $qb, $states, $tva);

            $columns[] = [
                'columnId' => $columnId,
                'items' => $items,
                'total' => $total,
                'revenue' => $revenue
            ];
        }
        return new ArrayCollection(['columns' => $columns]);
    }

    /**
     * @param Proposal $proposal
     */
    public function delete(Proposal $proposal)
    {
        $this->_em->remove($proposal);
        $this->_em->flush();
    }

    /**
     * @param Proposal $genericProposal
     * @param string $email
     * @param bool $used
     * @return Proposal|null
     * @throws NonUniqueResultException
     */
    public function findByParentProposalAndEmailAndUsed(Proposal $genericProposal, string $email, bool $used): ?Proposal
    {
        $qb = $this->createQueryBuilder('p');
        $qb->where($qb->expr()->eq('p.parentProposal', ':parent'))
            ->setParameter('parent', $genericProposal);
        //only for this attendee with email or phoneNumber
        $qb->andWhere($qb->expr()->like('p.email', ':email'))
            ->setParameter('email', $email);
        if (!$used) {
            $qb->andWhere($qb->expr()->isNull('p.registrationFolder'));
        } else {
            $qb->andWhere($qb->expr()->isNotNull('p.registrationFolder'));
        }
        return $qb->getQuery()->getOneOrNullResult();
    }

    /**
     * @param string $proposalId
     * @return Proposal|null
     */
    public function findById(string $proposalId): ?Proposal
    {
        return $this->find($proposalId);
    }

    /**
     * @param QueryBuilder $qb
     * @param array $states
     * @param float $tva
     * @return float
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function getRevenueByStates(QueryBuilder $qb, array $states, float $tva = 0.0): float
    {
        // TODO KANBAN better manage multiple states
        if (in_array(ProposalStates::ACCEPTED(), $states)) {
            $revenueByState = $qb
                ->join('p.registrationFolder', 'rf')
                ->select("SUM(CAST(JSON_EXTRACT(rf.rawData, '$.trainingActionInfo.totalExcl') AS DECIMAL(10,2)))")
                ->getQuery()->getSingleScalarResult() ?? 0;
        } else {
            $revenueByState = 0;
            // Discount type fixe
            $revenueByState += (clone $qb)->select("SUM((100 - :tva) * p.amount / 100)")
                ->andWhere($qb->expr()->eq('p.discountType', ':discountType'))
                ->setParameter('discountType', ProposalDiscountTypes::FIXED()->getValue())
                ->setParameter('tva', $tva)
                ->getQuery()->getSingleScalarResult() ?? 0;

            if (!in_array('ta2', $qb->getAllAliases())) {
                $qb->join('p.selectedTrainingAction', 'ta2');
            }
            // Discount type none
            $revenueByState += (clone $qb)
                ->select("SUM(CAST(JSON_EXTRACT(ta2.rawData, '$.totalTvaHT') AS DECIMAL(10,2)))")
                ->andWhere($qb->expr()->eq('p.discountType', ':discountType'))
                ->setParameter('discountType', ProposalDiscountTypes::NONE()->getValue())
                ->getQuery()->getSingleScalarResult() ?? 0;

            // Discount type none
            $revenueByState += (clone $qb)
                ->select("SUM(CAST(JSON_EXTRACT(ta2.rawData, '$.totalTvaHT') AS DECIMAL(10,2)) + p.amount)")
                ->andWhere($qb->expr()->eq('p.discountType', ':discountType'))
                ->setParameter('discountType', ProposalDiscountTypes::AMOUNT()->getValue())
                ->getQuery()->getSingleScalarResult() ?? 0;

            // Discount type none
            $revenueByState += (clone $qb)
                ->select("SUM(CAST(JSON_EXTRACT(ta2.rawData, '$.totalTvaHT') AS DECIMAL(10,2)) * (100 + p.amount) / 100)")
                ->andWhere($qb->expr()->eq('p.discountType', ':discountType'))
                ->setParameter('discountType', ProposalDiscountTypes::PERCENT()->getValue())
                ->getQuery()->getSingleScalarResult() ?? 0;;
        }
        return $revenueByState;
    }

    /**
     * @param Organism $organism
     * @param string $query
     * @return array
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws \Doctrine\DBAL\Exception
     */
    public function extractMetadataKeys(Organism $organism, string $query = ''): array
    {
        $conn = $this->getEntityManager()->getConnection();
        $sql = "SELECT DISTINCT JSON_UNQUOTE(json_key) as metadata FROM proposal, json_table(json_keys(metadata),'$[*]' COLUMNS(json_key JSON PATH '$')) t WHERE proposal.metadata is not null AND proposal.organism_id = :organism_id";
        if (!empty($query)) {
            $sql .= " AND JSON_UNQUOTE(json_key) LIKE :query";
        }
        $stmt = $conn->prepare($sql);
        $stmt->bindValue('organism_id', $organism->getId());
        if (!empty($query)) {
            $stmt->bindValue('query', '%' . $query . '%');
        }
        $result = $stmt->executeQuery();
        $metadata = [];
        foreach ($result->fetchAllAssociative() as $row) {
            $metadata[] = $row['metadata'];
        }
        return $metadata;
    }

    /**
     * @return array
     */
    public function listColumnConfigs(): array
    {
        return [
            [
                'columnId' => ProposalStates::DRAFT()->getValue(),
                'filter' => ['state' => [ProposalStates::DRAFT()->getValue()]],
                'title' => ProposalStates::toFrString(ProposalStates::DRAFT()->getValue())
            ],
            [
                'columnId' => ProposalStates::ACTIVE()->getValue(),
                'filter' => ['state' => [ProposalStates::ACTIVE()->getValue()]],
                'title' => ProposalStates::toFrString(ProposalStates::ACTIVE()->getValue())
            ],
            [
                'columnId' => ProposalStates::VIEWED()->getValue(),
                'filter' => ['state' => [ProposalStates::VIEWED()->getValue()]],
                'title' => ProposalStates::toFrString(ProposalStates::VIEWED()->getValue())
            ],
            [
                'columnId' => ProposalStates::ACCEPTED()->getValue(),
                'filter' => ['state' => [ProposalStates::ACCEPTED()->getValue()]],
                'title' => ProposalStates::toFrString(ProposalStates::ACCEPTED()->getValue())
            ],
            [
                'columnId' => ProposalStates::REFUSED()->getValue(),
                'filter' => ['state' => [ProposalStates::REFUSED()->getValue()]],
                'title' => ProposalStates::toFrString(ProposalStates::REFUSED()->getValue())
            ]
        ];
    }
}
