<?php

namespace App\Repository;

use App\Entity\CertificationFolder;
use App\Entity\CertificationPartner;
use App\Entity\Organism;
use App\Entity\Proposal;
use App\Entity\RegistrationFolder;
use App\Entity\Tag;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;

class TagRepository extends ServiceEntityRepository
{
    //-------------------
    // METHODES PUBLIQUES
    //-------------------
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Tag::class);
    }

    /**
     * @param Organism $organism
     * @param array $parameters
     * @return QueryBuilder
     */
    public function findAllReturnQueryBuilder(Organism $organism, array $parameters): QueryBuilder
    {
        $subQbProposal = $this->_em->createQueryBuilder();
        $subQbProposal->select('t1.id')
            ->from(Proposal::class, 'p')
            ->join('p.tags', 't1')
            ->where($subQbProposal->expr()->eq('p.organism', ':organism'))
            ->distinct();

        $subQbRegistrationFolder = $this->_em->createQueryBuilder();
        $subQbRegistrationFolder->select('t2.id')
            ->from(RegistrationFolder::class, 'rf')
            ->join('rf.tags', 't2')
            ->where($subQbRegistrationFolder->expr()->eq('rf.organism', ':organism'))
            ->distinct();

        $subQbCertificationFolder = $this->_em->createQueryBuilder();
        $subQbCertificationFolder->select('t3.id')
            ->from(CertificationFolder::class, 'cf')
            ->join('cf.tags', 't3')
            ->join('cf.certification', 'c')
            ->join('c.certifiers', 'certifiers')
            ->where($subQbCertificationFolder->expr()->eq('certifiers', ':organism'))
            ->distinct();

        $subQbCertificationPartner = $this->_em->createQueryBuilder();
        $subQbCertificationPartner->select('t4.id')
            ->from(CertificationPartner::class, 'cp')
            ->join('cp.tags', 't4')
            ->join('cp.certification', 'certif')
            ->join('certif.certifiers', 'certifiersPartner')
            ->where($subQbCertificationPartner->expr()->eq('certifiersPartner', ':organism'))
            ->distinct();

        $qb = $this->createQueryBuilder('t');
        $qb->where($qb->expr()->orX(
            $qb->expr()->in('t.id', $subQbProposal->getDQL()),
            $qb->expr()->in('t.id', $subQbRegistrationFolder->getDQL()),
            $qb->expr()->in('t.id', $subQbCertificationFolder->getDQL()),
            $qb->expr()->in('t.id', $subQbCertificationPartner->getDQL())
        ))
            ->setParameter('organism', $organism);
        if (isset($parameters['query'])) {
            $qb->andWhere($qb->expr()->like('t.name', ':query'))
                ->setParameter('query', '%' . trim($parameters['query']) . '%');
        }
        $qb->orderBy('t.id', 'DESC');
        return $qb;
    }
}
