<?php

namespace App\Repository;

use App\Entity\Activity;
use App\Entity\User;
use DateTime;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Activity>
 *
 * @method Activity|null find($id, $lockMode = null, $lockVersion = null)
 * @method Activity|null findOneBy(array $criteria, array $orderBy = null)
 * @method Activity[]    findAll()
 * @method Activity[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ActivityRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Activity::class);
    }

    /**
     * @param Activity $activity
     * @return Activity
     */
    public function save(Activity $activity): Activity
    {
        if (!$this->_em->contains($activity)) {
            $this->_em->persist($activity);
        }
        $this->_em->flush();
        return $activity;
    }

    /**
     * @param Activity $activity
     */
    public function delete(Activity $activity): void
    {
        $this->_em->remove($activity);
        $this->_em->flush();
    }

    /**
     * @param int|string $entityId
     * @param string $entityClass
     * @param array $parameters
     * @return QueryBuilder
     */
    public function findAllByEntityReturnQueryBuilder($entityId, string $entityClass, array $parameters): QueryBuilder
    {
        $qb = $this->createQueryBuilder('act');
        //Workaround that allow to sort both act dueDate and eventTime with the correct type of sort, UNIX_TIMESTAMP function isn't allowed. minus before attribute allow to both parse to int and change to negative value.
        $qb->addSelect('CASE WHEN act.eventEndTime IS NULL THEN 0 ELSE 1 END as HIDDEN isDone');
        $qb->addSelect('CASE WHEN act.eventEndTime IS NULL THEN -act.dueDate ELSE -(-act.eventTime) END as HIDDEN timeOrder');

        $qb->where($qb->expr()->eq('act.entityClass', ':entityClass'))
            ->setParameter('entityClass', $entityClass)
            ->andWhere($qb->expr()->eq("act.entityId", ':entityId'))
            ->setParameter('entityId', $entityId);

        if (!in_array('all', $parameters['type'])) {
            $qb->andWhere($qb->expr()->in('act.type', ':type'))
                ->setParameter('type', $parameters['type']);
        }
        if (isset($parameters['qualiopi'])) {
            $qb->andWhere($qb->expr()->isNotNull('act.qualiopiIndicators'));
        }
        if (isset($parameters["done"])) {
            $qb->andWhere($qb->expr()->isNull('act.eventEndTime'));
        }
        if (isset($parameters['qualiopiIndicators'])) {
            $qualiopiIndicators = explode(',', strtoupper($parameters['qualiopiIndicators']));
            $orX = $qb->expr()->orX();
            for ($i = 0; $i < sizeof($qualiopiIndicators); $i++) {
                $orX->add(
                    $qb->expr()->orX("JSON_CONTAINS(act.qualiopiIndicators, :qualiopiIndicators" . $i . ", '$') = 1"));
                $qb->setParameter('qualiopiIndicators' . $i, $qualiopiIndicators[$i]);
            }
            $qb->andWhere($orX);
        }
        $qb->orderby('isDone', 'ASC');
        $qb->addOrderBy('timeOrder', 'DESC');
        $qb->addOrderBy('act.id', 'DESC');
        return $qb;
    }

    /**
     * @param User $user
     * @return QueryBuilder
     */
    public function findAllByUserReturnQueryBuilder(User $user): QueryBuilder
    {
        $qb = $this->createQueryBuilder('act');
        //Workaround that allow to sort both act dueDate and eventTime with the correct type of sort, UNIX_TIMESTAMP function isn't allowed and this case doesn't work in orderBY
        $qb->addSelect('CASE WHEN act.eventEndTime IS NULL THEN 0 ELSE 1 END as HIDDEN isDone');
        $qb->addSelect('CASE WHEN act.eventEndTime IS NULL THEN -act.dueDate ELSE -(-act.eventTime) END as HIDDEN timeOrder');
        $qb->where($qb->expr()->eq('act.user', ':user'))
            ->andWhere($qb->expr()->gt('act.dueDate', ':dateNow'))
            ->setParameter('user', $user)
            ->setParameter('dateNow', new DateTime());
        $qb->orderby('isDone', 'ASC');
        $qb->addOrderBy('timeOrder', 'DESC');
        $qb->addOrderBy('act.id', 'DESC');
        return $qb;
    }

    /**
     * @param int|string $entityId
     * @param string $entityClass
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countByEntity($entityId, string $entityClass): int
    {
        $qb = $this->createQueryBuilder('act');
        $qb->select('count(act.id)')
            ->andWhere($qb->expr()->eq('act.entityClass', ':entityClass'))
            ->setParameter('entityClass', $entityClass)
            ->andWhere($qb->expr()->eq("act.entityId", ':entityId'))
            ->setParameter('entityId', $entityId);
        return $qb->getQuery()->getSingleScalarResult();
    }
}
