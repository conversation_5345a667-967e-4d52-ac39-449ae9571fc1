<?php

namespace App\Repository;

use App\Entity\EndPointStatus;
use App\Entity\Organism;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method EndPointStatus|null find($id, $lockMode = null, $lockVersion = null)
 * @method EndPointStatus|null findOneBy(array $criteria, array $orderBy = null)
 * @method EndPointStatus[]    findAll()
 * @method EndPointStatus[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class EndPointStatusRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, EndPointStatus::class);
    }

    /**
     * @param EndPointStatus $endPointStatus
     * @return EndPointStatus
     */
    public function save(EndPointStatus $endPointStatus): EndPointStatus
    {
        if (!$this->_em->contains($endPointStatus)) {
            $this->_em->persist($endPointStatus);
        }
        $this->_em->flush();
        return $endPointStatus;

    }
}
