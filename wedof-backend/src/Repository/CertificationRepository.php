<?php

namespace App\Repository;

use App\Entity\Certification;
use App\Entity\CertificationPartner;
use App\Entity\Organism;
use App\Library\utils\enums\CertificationPartnerStates;
use App\Library\utils\enums\CertificationTypes;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\Tools;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;
use Exception;

/**
 * @method Certification|null find($id, $lockMode = null, $lockVersion = null)
 * @method Certification|null findOneBy(array $criteria, array $orderBy = null)
 * @method Certification[]    findAll()
 * @method Certification[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class CertificationRepository extends ServiceEntityRepository
{
    private CertificationPartnerRepository $certificationPartnerRepository;
    private CertifierAccessRepository $certifierAccessRepository;

    public function __construct(ManagerRegistry $registry,
                                CertificationPartnerRepository $certificationPartnerRepository,
                                CertifierAccessRepository $certifierAccessRepository)
    {
        parent::__construct($registry, Certification::class);
        $this->certificationPartnerRepository = $certificationPartnerRepository;
        $this->certifierAccessRepository = $certifierAccessRepository;
    }

    /**
     * @param Certification $certification
     */
    public function delete(Certification $certification): void
    {
        $this->_em->remove($certification);
        $this->_em->flush();
    }

    /**
     * @param Organism|null $organism
     * @param array $parameters
     * @return QueryBuilder
     */
    public function findAllReturnQueryBuilder(?Organism $organism, array $parameters): QueryBuilder
    {
        $qb = $this->createQueryBuilder('c');

        if ($organism) {
            $certificationPartnerState = $parameters['certificationPartnerState'] ?? [CertificationPartnerStates::ACTIVE()->getValue()];
            if ($parameters['organismType'] == 'all') {
                $qb
                    ->leftJoin(
                        'c.certificationPartners',
                        'cp',
                        Join::WITH,
                        $qb->expr()->in('cp.state', ':certificationPartnerState')
                    )
                    ->where($qb->expr()->orX(
                        $qb->expr()->eq('cp.partner', ':organism'),
                        ':organism MEMBER OF c.certifiers'
                    ));
                $qb->setParameter('certificationPartnerState', $certificationPartnerState);
            } else if ($parameters['organismType'] == 'certifier') {
                $qb->where(':organism MEMBER OF c.certifiers');
            } else if ($parameters['organismType'] == 'partner') {
                $qb
                    ->join(
                        'c.certificationPartners',
                        'cp',
                        Join::WITH,
                        $qb->expr()->in('cp.state', ':certificationPartnerState')
                    )
                    ->where($qb->expr()->eq('cp.partner', ':organism'));
                $qb->setParameter('certificationPartnerState', $certificationPartnerState);
            }
            $qb->setParameter('organism', $organism);
        }

        $qb = $this->getFulltextQueryBuilder($parameters, $qb);

        if (!empty($parameters['type'])) {
            $qb->andWhere($qb->expr()->in('c.type', ':type_certif'))
                ->setParameter('type_certif', strtoupper($parameters['type']));
        }

        if (!empty($parameters['enabled'])) {
            $enabled = filter_var($parameters['enabled'], FILTER_VALIDATE_BOOLEAN);
            $qb->andWhere($qb->expr()->eq('c.enabled', ':enabled'))
                ->setParameter('enabled', $enabled);
        }

        if (!empty($parameters['certifInfo'])) {
            $qb->andWhere($qb->expr()->eq('c.certifInfo', ':certifInfo'))
                ->setParameter('certifInfo', $parameters['certifInfo']);
        }

        if (!empty($parameters['allowGenerateXmlAutomatically'])) {
            $qb->andWhere($qb->expr()->eq('c.allowGenerateXmlAutomatically', ':allowGenerateXmlAutomatically'))
                ->setParameter('allowGenerateXmlAutomatically', $parameters['allowGenerateXmlAutomatically']);
        }

        if (!empty($parameters['certifierCustomer'])) {
            $qb->join('c.certifiers', 'certifiers');
            $certifierCustomer = filter_var($parameters['certifierCustomer'], FILTER_VALIDATE_BOOLEAN);
            $qb->join('certifiers.subscription', 'subscription')
                ->andWhere($qb->expr()->eq('subscription.allowCertifiers', ':allowCertifier'))
                ->setParameter('allowCertifier', $certifierCustomer);
        }

        $qb->orderBy('c.' . ($parameters['sort'] ?? 'name'), ($parameters['order'] ?? 'asc'));
        $qb->addOrderBy('c.id', 'ASC');
        return $qb;
    }

    /**
     * @param Organism $organism
     * @param string|null $query
     * @param array $certificationTypes
     * @param array|null $states
     * @param string|null $certifierSiret
     * @param bool $isPromoted
     * @param bool $onlyCertificationsEnabled
     * @return QueryBuilder
     */
    public function findAllPartnershipOrderedQueryBuilder(Organism $organism, ?string $query, array $certificationTypes, ?array $states, ?string $certifierSiret, bool $isPromoted = false, bool $onlyCertificationsEnabled = false): QueryBuilder
    {
        $qb = $this->createQueryBuilder('c');
        $qb
            ->leftJoin('c.certificationPartners', 'p', Join::WITH, $qb->expr()->eq('p.partner', ':organism'))
            ->leftJoin('c.certifiers', 'certifiers')
            ->leftJoin('certifiers.subscription', 's')
            ->addSelect('p')
            ->addSelect('(' . $this->certificationPartnerRepository->countActivePartnersSubQuery()->getDQL() . ') AS partnerCount')
            ->where($qb->expr()->orX($qb->expr()->eq('c.allowPartnershipRequest', 1), $qb->expr()->eq('p.certification', 'c')));
        if ($isPromoted || $onlyCertificationsEnabled) {
            $qb->andWhere($qb->expr()->eq('c.enabled', 1));
        }
        $qb->andWhere($qb->expr()->in('c.type', ':certificationTypes'))
            ->setParameter('organism', $organism->getId())
            ->setParameter('certificationTypes', $certificationTypes)
            ->setParameter('cpActive', CertificationPartnerStates::ACTIVE()->getValue()) // For countActivePartnersSubQuery subquery
            ->orderBy('CASE 
                WHEN certifiers = :organism AND c.enabled = 1 THEN 0
                WHEN p.id IS NOT NULL AND s.id IS NOT NULL AND s.allowCertifiers = 1 AND c.enabled = 1 THEN 1
                WHEN p.id IS NOT NULL AND s.id IS NOT NULL AND s.allowCertifiers = 0 AND c.enabled = 1 THEN 2
                WHEN p.id IS NOT NULL AND s.id IS NULL AND c.enabled = 1 THEN 3
                WHEN p.id IS NULL AND s.id IS NOT NULL AND s.allowCertifiers = 1 AND s.allowCertifierPlus = 1 AND c.enabled = 1 THEN 4
                WHEN p.id IS NULL AND s.id IS NOT NULL AND s.allowCertifiers = 1 AND s.allowCertifierPlus = 0 AND c.enabled = 1 THEN 5
                WHEN certifiers = :organism THEN 6
                WHEN p.id IS NOT NULL AND s.id IS NOT NULL AND s.allowCertifiers = 1 THEN 7
                WHEN p.id IS NOT NULL AND s.id IS NOT NULL AND s.allowCertifiers = 0 THEN 8
                WHEN p.id IS NOT NULL AND s.id IS NULL THEN 9
                WHEN p.id IS NULL AND s.id IS NOT NULL AND s.allowCertifiers = 1 AND s.allowCertifierPlus = 1 THEN 10
                WHEN p.id IS NULL AND s.id IS NOT NULL AND s.allowCertifiers = 1 AND s.allowCertifierPlus = 0 THEN 11
                ELSE 12 END')
            ->addOrderBy('CASE WHEN ' . $qb->expr()->exists($this->certifierAccessRepository->certifierOrganismSubQuery()->getDQL()) . ' THEN 0 ELSE 1 END')
            ->addOrderBy('partnerCount', 'DESC')
            ->addOrderBy('c.id');

        if ($query) {
            $parameters = ['query' => $query];
            $qb = $this->getFulltextQueryBuilder($parameters, $qb);
        }
        if (!empty($states)) {
            $qb->andWhere($qb->expr()->in('p.state', ':states'))->setParameter('states', $states);
        }
        if ($certifierSiret) {
            $qb->andWhere($qb->expr()->eq('certifiers.siret', ':certifierSiret'))->setParameter('certifierSiret', $certifierSiret);
        }
        if ($isPromoted) {
            $qb->andWhere($qb->expr()->eq('s.allowCertifierPlus', ':isPromoted'))->setParameter('isPromoted', $isPromoted);
            $qb->andWhere($qb->expr()->neq('certifiers', ':organism'));
            $qb->andWhere($qb->expr()->isNull('p'));
        }
        return $qb;
    }

    /**
     * @param Organism $organism
     * @param string|null $query
     * @param array $certificationTypes
     * @param array|null $states
     * @param string|null $certifierSiret
     * @param bool $isPromoted
     * @param bool $onlyCertificationsEnabled
     * @return int
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function countAllPartnershipOrderedQueryBuilder(Organism $organism, ?string $query, array $certificationTypes, ?array $states, ?string $certifierSiret, bool $isPromoted = false, bool $onlyCertificationsEnabled = false): int
    {
        $qb = $this->createQueryBuilder('c');
        $qb->select($qb->expr()->count('DISTINCT(c.id)'))
            ->leftJoin('c.certificationPartners', 'p', Join::WITH, $qb->expr()->eq('p.partner', ':organism'))
            ->leftJoin('c.certifiers', 'certifiers')
            ->leftJoin('certifiers.subscription', 's')
            ->where($qb->expr()->orX($qb->expr()->eq('c.allowPartnershipRequest', 1), $qb->expr()->eq('p.certification', 'c')));
        if ($isPromoted || $onlyCertificationsEnabled) {
            $qb->where($qb->expr()->eq('c.enabled', 1));
        }
        $qb->andWhere($qb->expr()->in('c.type', ':certificationTypes'))
            ->setParameter('organism', $organism->getId())
            ->setParameter('certificationTypes', $certificationTypes);

        if ($query) {
            $parameters = ['query' => $query];
            $qb = $this->getFulltextQueryBuilder($parameters, $qb);
        }
        if (!empty($states)) {
            $qb->andWhere($qb->expr()->in('p.state', ':states'))->setParameter('states', $states);
        }
        if ($certifierSiret) {
            $qb->andWhere($qb->expr()->eq('certifiers.siret', ':certifierSiret'))->setParameter('certifierSiret', $certifierSiret);
        }
        if ($isPromoted) {
            $qb->andWhere($qb->expr()->eq('s.allowCertifierPlus', ':isPromoted'))
                ->setParameter('isPromoted', $isPromoted);
            $qb->andWhere($qb->expr()->neq('certifiers', ':organism'));
            $qb->andWhere($qb->expr()->isNull('p'));
        }
        return $qb->getQuery()->getSingleScalarResult();
    }

    /**
     * @param string $certifInfo
     * @return Certification|null
     */
    public function findOneByCertifInfo(string $certifInfo): ?Certification
    {
        return $this->findOneBy(['certifInfo' => $certifInfo]);
    }

    /**
     * @param CertificationTypes $type
     * @param string $code
     * @return Certification|null
     */
    public function findOneByTypeAndCode(CertificationTypes $type, string $code): ?Certification
    {
        if ($type === CertificationTypes::FICHE()) {
            return $this->findOneBy(['idFiche' => $code]);
        } else {
            return $this->findOneBy(['type' => $type->getValue(), 'code' => $code]);
        }
    }

    /**
     * @param Certification $certification
     * @return Certification
     */
    public function save(Certification $certification): Certification
    {
        if (!$this->_em->contains($certification)) {
            $this->_em->persist($certification);
        }
        $this->_em->flush();
        return $certification;
    }

    /**
     * @return array
     * @throws Exception
     */
    public function findAllUpdatableCertifInfo(): array
    {
        $qb = $this->createQueryBuilder('c');
        $qb->select('c.certifInfo')
            ->where($qb->expr()->isNotNull('c.idFiche'))
            ->andWhere($qb->expr()->eq('c.enabled', true));
        return $qb->getQuery()->getScalarResult();
    }

    /**
     * @param DataProviders $dataProvider
     * @return array
     */
    public function findAllWithCertificationPartnersListToUpdate(DataProviders $dataProvider): array
    {
        $qb = $this->createQueryBuilder('c');
        $qb->select('DISTINCT c.certifInfo')
            ->innerJoin(
                CertificationPartner::class,
                'cp',
                Join::WITH,
                $qb->expr()->andX(
                    $qb->expr()->eq('c.id', 'cp.certification'),
                    $qb->expr()->orX(
                        $qb->expr()->eq('cp.pendingActivation', 1),
                        $qb->expr()->eq('cp.pendingRevocation', 1),
                        $qb->expr()->eq('cp.pendingSuspension', 1)
                    )
                )
            )
            ->where($qb->expr()->isNotNull('c.idFiche'))
            ->andWhere($qb->expr()->eq('c.enabled', true))
            ->andWhere($qb->expr()->eq('c.dataProvider', ':dataProvider'))
            ->setParameter('dataProvider', $dataProvider->getValue());
        return $qb->getQuery()->getScalarResult();
    }

    /**
     * @param Certification $certification
     * @return bool
     */
    public function hasCertificationPartnersToUpdate(Certification $certification): bool
    {
        $qb = $this->createQueryBuilder('c');
        $qb
            ->innerJoin(
                CertificationPartner::class,
                'cp',
                Join::WITH,
                $qb->expr()->andX(
                    $qb->expr()->eq('c.id', 'cp.certification'),
                    $qb->expr()->orX(
                        $qb->expr()->eq('cp.pendingActivation', 1),
                        $qb->expr()->eq('cp.pendingRevocation', 1),
                        $qb->expr()->eq('cp.pendingSuspension', 1)
                    )
                )
            )
            ->where($qb->expr()->eq('c', ':certification'))
            ->setParameter('certification', $certification);
        $result = $qb->getQuery()->getScalarResult();
        return !empty($result);
    }

    /**
     * @param array $parameters
     * @param QueryBuilder $qb
     * @return QueryBuilder
     */
    public function getFulltextQueryBuilder(array &$parameters, QueryBuilder $qb): QueryBuilder
    {
        if (!empty($parameters['query'])) {
            $query = trim($parameters['query']);
            if (Tools::startsWith(strtoupper($query), 'RS') ||
                Tools::startsWith(strtoupper($query), 'RNCP') ||
                Tools::startsWith(strtoupper($query), 'CERT') ||
                Tools::startsWith(strtoupper($query), 'PREVENTION')
            ) {
                $type = null;
                if (Tools::startsWith(strtoupper($query), 'RS')) {
                    $query = preg_replace("/\s+/", "", substr($query, strlen('RS')));
                    $type = CertificationTypes::RS()->getValue();
                } else if (Tools::startsWith(strtoupper($query), 'RNCP')) {
                    $query = preg_replace("/\s+/", "", substr($query, strlen('RNCP')));
                    $type = CertificationTypes::RNCP()->getValue();
                } else if (Tools::startsWith(strtoupper($query), 'CERT')) {
                    $query = preg_replace("/\s+/", "", substr($query, strlen('CERT')));
                    $type = CertificationTypes::INTERNAL()->getValue();
                } else if (Tools::startsWith(strtoupper($query), 'PREVENTION')) {
                    $query = preg_replace("/\s+/", "", substr($query, strlen('PREVENTION')));
                    $type = CertificationTypes::PREVENTION()->getValue();
                }
                $qb->andWhere($qb->expr()->andX($qb->expr()->like('c.code', ':query'), $qb->expr()->eq('c.type', ':type')));
                $qb->setParameter('type', $type);
                $parameters['type'] = null; //override type parameter if set
            } else if (Tools::startsWith(strtoupper($query), 'CERTIFINFO')) {
                $query = preg_replace("/\s+/", "", substr($query, strlen('CERTIFINFO')));
                $qb->andWhere($qb->expr()->like('c.certifInfo', ':query')); // Refers to 'c' from parent query
            } else {
                $qb
                    ->leftJoin('c.certifiers', 'certifiers2') // avoid alias name clashing
                    ->andWhere($qb->expr()->orX(
                        $qb->expr()->like('c.name', ':query'),
                        $qb->expr()->like('c.code', ':query'),
                        $qb->expr()->like('c.certifInfo', ':query'),
                        $qb->expr()->like('c.domains', ':query'),
                        $qb->expr()->like('c.rome', ':query'),
                        $qb->expr()->like('certifiers2.name', ':query')
                    ));
            }
            $qb->setParameter('query', '%' . $query . '%');
        }
        return $qb;
    }

}
