<?php

namespace App\Factory;

use App\Entity\RegistrationFolderHistory;
use App\Repository\RegistrationFolderHistoryRepository;
use <PERSON><PERSON><PERSON>ck\Foundry\RepositoryProxy;
use Zen<PERSON><PERSON>ck\Foundry\ModelFactory;
use Zen<PERSON><PERSON>ck\Foundry\Proxy;

/**
 * @method static RegistrationFolderHistory|Proxy createOne(array $attributes = [])
 * @method static RegistrationFolderHistory[]|Proxy[] createMany(int $number, $attributes = [])
 * @method static RegistrationFolderHistory|Proxy findOrCreate(array $attributes)
 * @method static RegistrationFolderHistory|Proxy random(array $attributes = [])
 * @method static RegistrationFolderHistory|Proxy randomOrCreate(array $attributes = [])
 * @method static RegistrationFolderHistory[]|Proxy[] randomSet(int $number, array $attributes = [])
 * @method static RegistrationFolderHistory[]|Proxy[] randomRange(int $min, int $max, array $attributes = [])
 * @method static RegistrationFolderHistoryRepository|RepositoryProxy repository()
 * @method RegistrationFolderHistory|Proxy create($attributes = [])
 */
final class RegistrationFolderHistoryFactory extends ModelFactory
{
    protected function getDefaults(): array
    {
        return [];
    }

    protected static function getClass(): string
    {
        return RegistrationFolderHistory::class;
    }
}
