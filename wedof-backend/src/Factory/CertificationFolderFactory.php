<?php

namespace App\Factory;

use App\Entity\CertificationFolder;
use App\Repository\CertificationFolderRepository;
use Zenst<PERSON>ck\Foundry\ModelFactory;
use Zenstruck\Foundry\Proxy;
use Zenstruck\Foundry\RepositoryProxy;
use function Zenstruck\Foundry\faker;

/**
 * @method static CertificationFolder|Proxy createOne(array $attributes = [])
 * @method static CertificationFolder[]|Proxy[] createMany(int $number, $attributes = [])
 * @method static CertificationFolder|Proxy findOrCreate(array $attributes)
 * @method static CertificationFolder|Proxy random(array $attributes = [])
 * @method static CertificationFolder|Proxy randomOrCreate(array $attributes = [])
 * @method static CertificationFolder[]|Proxy[] randomSet(int $number, array $attributes = [])
 * @method static CertificationFolder[]|Proxy[] randomRange(int $min, int $max, array $attributes = [])
 * @method static CertificationFolderRepository|RepositoryProxy repository()
 * @method CertificationFolder|Proxy create($attributes = [])
 */
final class CertificationFolderFactory extends ModelFactory
{
    protected function getDefaults(): array
    {
        return [
            'certification' => faker()->unique()->certification(),
            'registrationFolder' => faker()->unique()->registrationFolder(),
            'state' => faker()->unique()->state()
        ];
    }

    protected static function getClass(): string
    {
        return CertificationFolder::class;
    }
}
