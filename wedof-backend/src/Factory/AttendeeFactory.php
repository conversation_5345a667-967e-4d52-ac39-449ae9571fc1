<?php

namespace App\Factory;

use App\Entity\Attendee;
use App\Repository\AttendeeRepository;
use function Zenstruck\Foundry\faker;
use Zenst<PERSON>ck\Foundry\RepositoryProxy;
use Zenst<PERSON>ck\Foundry\ModelFactory;
use Zen<PERSON><PERSON>ck\Foundry\Proxy;

/**
 * @method static Attendee|Proxy createOne(array $attributes = [])
 * @method static Attendee[]|Proxy[] createMany(int $number, $attributes = [])
 * @method static Attendee|Proxy findOrCreate(array $attributes)
 * @method static Attendee|Proxy random(array $attributes = [])
 * @method static Attendee|Proxy randomOrCreate(array $attributes = [])
 * @method static Attendee[]|Proxy[] randomSet(int $number, array $attributes = [])
 * @method static Attendee[]|Proxy[] randomRange(int $min, int $max, array $attributes = [])
 * @method static AttendeeRepository|RepositoryProxy repository()
 * @method Attendee|Proxy create($attributes = [])
 */
final class AttendeeFactory extends ModelFactory
{
    protected function getDefaults(): array
    {
        return [
            'lastName' => faker()->unique()->lastName(),
            'firstName' => faker()->unique()->firstName(),
            'email' => faker()->unique()->email(),
            'lastUpdate' => new \DateTime('now'),
        ];
    }

    protected static function getClass(): string
    {
        return Attendee::class;
    }
}
