<?php

namespace App\Factory;

use App\Entity\RegistrationFolder;
use App\Library\utils\enums\RegistrationFolderStates;
use App\Repository\RegistrationFolderRepository;
use DateTime;
use function Zenstruck\Foundry\faker;
use <PERSON><PERSON><PERSON>ck\Foundry\RepositoryProxy;
use Zenst<PERSON>ck\Foundry\ModelFactory;
use Zenstruck\Foundry\Proxy;

/**
 * @method static RegistrationFolder|Proxy createOne(array $attributes = [])
 * @method static RegistrationFolder[]|Proxy[] createMany(int $number, $attributes = [])
 * @method static RegistrationFolder|Proxy findOrCreate(array $attributes)
 * @method static RegistrationFolder|Proxy random(array $attributes = [])
 * @method static RegistrationFolder|Proxy randomOrCreate(array $attributes = [])
 * @method static RegistrationFolder[]|Proxy[] randomSet(int $number, array $attributes = [])
 * @method static RegistrationFolder[]|Proxy[] randomRange(int $min, int $max, array $attributes = [])
 * @method static RegistrationFolderRepository|RepositoryProxy repository()
 * @method RegistrationFolder|Proxy create($attributes = [])
 */
final class RegistrationFolderFactory extends ModelFactory
{
    protected function getDefaults(): array
    {
        return [
            'lastUpdate' => new DateTime('now'),
            'createdOn' => new DateTime('now'),
            'externalId' => faker()->unique()->word(),
            'state' => RegistrationFolderStates::valuesStates()[rand(0, count(RegistrationFolderStates::valuesStates()) - 2)]->getValue(),
        ];
    }

    protected static function getClass(): string
    {
        return RegistrationFolder::class;
    }
}
