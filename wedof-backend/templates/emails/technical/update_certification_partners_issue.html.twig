<p>Bonjour la team,</p>

<p>Il y a une erreur sur la mise à jour des partenaires de la certification suivante :</p>

<ul>
    <li>Nom : {{ certificationName }}</li>
    <li>CertifInfo : {{ certificationCertifInfo }}</li>
    <li>ExternalId : {{ certificationExternalId }}</li>
    <li>Certifier : {{ certificationCertifierName }}</li>
</ul>

<p>Message:</p>
<p><b>{{ message }}</b></p>

<p>Attention, jusqu'à nouvel ordre Wedof ne synchronisera plus les partenaires de cette certif.</p>

<p>En effet, un flag <b>"error_synchronize_partners"</b> est positionné sur la table <b>"certification"</b> afin de
    l'exclure des prochaines synchros.</p>

<p>Le but de ce flag est d'éviter que la synchro soit retentée toutes les 5 minutes, ce qui pourrait spammer certif<PERSON><PERSON>,
    spammer des mails en cas d'erreur etc. (c'est déjà arrivé, 2000 mails envoyés...).</p>

<p>Dans un premier temps, vous pouvez utiliser la requête tout en bas du mail pour retenter un coup.</p>

<p>Si ça ne marche toujours pas et que vous recevez à nouveau ce mail, il faut regarder ce qui s'est passé,
    potentiellement essayer une synchro à la main en regardant les logs (<a
            href="https://automator.wedof.fr/workflow/eWYKCy5G3GZY40i5">https://automator.wedof.fr/workflow/eWYKCy5G3GZY40i5</a>)
</p>

<p>
    Si c'est sur un partenaire "en attente de révocation" ou "en attente de suspension", la raison peut être que le partenaire a été ajouté à main sur
    le compte CertifPro, auquel cas on ne peut pas le retirer via le excel.

    La seule solution actuellement consiste à venir supprimer à la main le partenaire dans l'UI de CertifPro :
    1. Se connecter sur certifPro avec les identifiants dans le JWT de la connexion
    2. Aller sur la bonne certif
    3. Vérifier si le partenaire est dans le tableau
    4. Cliquer sur modifier les partenaires
    5. Supprimer le partenaire & sauvegarder (il devrait être supprimé dans les 5 min sur Wedof après avoir remis error
    à 0)
</p>

<p>Si tout va bien, il faut bien penser à remettre <b>"error_synchronize_partners"</b> à 0 !!!</p>
<p><em>UPDATE certification SET error_synchronize_partners = 0 WHERE certif_info = '{{ certificationCertifInfo }}';</em>
</p>