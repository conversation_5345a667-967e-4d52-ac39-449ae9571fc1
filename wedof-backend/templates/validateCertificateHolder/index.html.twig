<!doctype html>
<html lang="fr" translate="no">
<head>
    <meta charset="utf-8">
    <title>{{ data['title'] }}</title>
    <base href="/">
    {% include 'shared/_meta.html.twig' %}
    <meta name="viewport"
          content="width=device-width, height=device-height, initial-scale=1.0, minimum-scale=1.0">
    <!-- Favicon -->
    <link rel="icon"
          type="image/png"
          href="favicon-16x16.png">
    <link rel="icon"
          type="image/png"
          href="favicon-32x32.png">
    <!-- Fonts -->
    <link href="assets/fonts/roboto/roboto.css"
          rel="stylesheet">
    <link href="assets/fonts/plex/plex.css"
          rel="stylesheet">
    <link href="assets/fonts/inter/inter.css"
          rel="stylesheet">
    <link href="assets/fonts/custom/custom.css"
          rel="stylesheet">
    <!-- Icon Fonts -->
    <link href="assets/fonts/material-icons/material-icons.css"
          rel="stylesheet">
    <style>
        body {
            font-size: 0.875rem;
            font-family: Inter, Roboto, Arial, sans-serif;
        }
    </style>
</head>
<body>
<p style="width: 1000px; margin: 50px auto auto;text-align:center;">
    L'organisme certificateur <b>{{ certifierName }}</b><br> atteste que
    <b>{{ attendeeName }}</b>
    <br> a obtenu la certification
    <a href="https://www.francecompetences.fr/recherche/{{ certificationType }}/{{ certificationCode }}"
       target="_blank">{{ certificationType }}{{ certificationCode }} - {{ certificationName }}</a> le
    <b>{{ issueDate|date("d/m/Y") }}</b>{% if gradePass %} avec la mention
    <b>{{ gradePass }}</b>{% endif %}.
    {% if expirationDate %}
        <br>Cette certification expirera le <b>{{ expirationDate|date("d/m/Y") }}</b>.
    {% endif %}
    {% if badgeAssertion and badgeAssertionImageUrl %}
        <br>
        <a target="_blank" href="{{ badgeAssertion }}">
            <img src="{{ badgeAssertionImageUrl }}" style="margin-top: 20px" alt="badge" width="20%">
        </a>
    {% endif %}
</p>
</body>
</html>