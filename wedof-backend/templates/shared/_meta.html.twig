<!-- general -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="title" content="{{ data['title'] }}">
<meta name="description" content="{{ data['description'] }}">
<meta name="robots" content="no-index, no-follow">
<meta name="language" content="French">
<meta name="author" content="{{ data['author_name'] }}">
<meta name="contact" content="{{ data['author_email'] }}">

<!-- Open Graph / Facebook -->
<meta property="og:type" content="website">
<meta property="og:url" content="{{ data['url'] }}">
<meta property="og:title" content="{{ data['title'] }}">
<meta property="og:description" content="{{ data['description'] }}">
{% if data['image'] is defined %}
    <meta property="og:image" content="{{ data['image'] }}">
{% endif %}
<meta property="og:site_name" content="Wedof">
<meta property="og:locale" content="fr_FR">

<!-- X card -->
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:url" content="{{ data['url'] }}">
<meta name="twitter:title" content="{{ data['title'] }}">
<meta name="twitter:description" content="{{ data['description'] }}">
{% if data['image'] is defined %}
    <meta name="twitter:image" content="{{ data['image'] }}">
{% endif %}
{% if data['twitter-site'] is defined %}
    <meta name="twitter:site" content="{{ data['twitter-site'] }}">
{% endif %}
{% if data['twitter-creator'] is defined %}
    <meta name="twitter:creator" content="{{ data['twitter-creator'] }}">
{% endif %}

<!-- oembed -->
<link rel="alternate" type="application/json+oembed"
      href="https://{{ data['host'] }}/app/public/oembed?url={{ data['url'] }}&format=json">