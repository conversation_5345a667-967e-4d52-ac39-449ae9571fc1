{# templates/bundles/NelmioApiDocBundle/SwaggerUi/index.html.twig #}

{#
To avoid a "reached nested level" error an exclamation mark `!` has to be added
See https://symfony.com/blog/new-in-symfony-3-4-improved-the-overriding-of-templates
#}
{% extends '@!NelmioApiDoc/SwaggerUi/index.html.twig' %}
{% block stylesheets %}
    <link rel="stylesheet" href="{{ asset('bundles/nelmioapidoc/swagger-ui/swagger-ui.css') }}">
    <link rel="stylesheet" href="{{ asset('bundles/nelmioapidoc/style.css') }}">
    <link rel="stylesheet" href="{{ asset('api/css/style.css') }}">
{% endblock stylesheets %}
{% block swagger_data %}
    {% set swagger_data = swagger_data|merge({'client_id': nelmio_client_id}) %}
    {% set swagger_data = swagger_data|merge({'client_secret': nelmio_client_secret}) %}
    {% set swagger_data = swagger_data|merge({'redirect_uri': nelmio_redirect_uri}) %}
    {# json_encode(65) is for JSON_UNESCAPED_SLASHES|JSON_HEX_TAG to avoid JS XSS #}
    <script id="swagger-data" type="application/json">{{ swagger_data|json_encode(65)|raw }}</script>
{% endblock swagger_data %}
{% block swagger_initialization %}
    <script src="{{ asset('api/js/init-swagger-ui.js') }}" id="init-swagger-ui-script"></script>
{% endblock swagger_initialization %}
