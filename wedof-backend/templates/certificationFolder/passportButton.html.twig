<meta charset="UTF-8">
<style>
    .add-to-passport {
        width: 310px;
        display: flex;
        justify-content: center;
        align-content: center;
        color: #fff;
        text-transform: uppercase;
        font-family: Marianne, arial, sans-serif;
        text-decoration: none;
        cursor: pointer;
    }

    .ico {
        mask-repeat: no-repeat;
        mask-position: 50% 50%;
        mask-size: 30px;
        -webkit-mask-repeat: no-repeat;
        -webkit-mask-position: 50% 50%;
        -webkit-mask-size: 30px;
        padding: 10px 18px 10px 23px;
    }

    .text {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 10px;
        margin-top: -2px; /* fix vertical align with <PERSON> font */
        box-sizing: border-box;
        flex: 1;
    }

    .text p {
        margin: 0;
    }

    .add-to-passport.rounded {
        border-radius: 50px;
    }

    .add-to-passport, .add-to-passport.inverted:hover, .add-to-passport.inverted:focus {
        color: #fff;
        background-color: #164194;
        border: 2px solid #fff;
    }

    .add-to-passport:not(.inverted):hover, .add-to-passport:not(.inverted):focus, .add-to-passport.inverted {
        background-color: #fff;
        color: #164194;
        border: 2px solid #164194;
    }

    .add-to-passport:not(.inverted):hover .text, .add-to-passport:not(.inverted):focus .text, .add-to-passport.inverted .text {
        border-left: 2px solid #164194;
    }

    .add-to-passport.inverted:hover .text, .add-to-passport.inverted:focus .text, .add-to-passport .text {
        border-left: 2px solid #fff;
    }

    .add-to-passport:not(.inverted):hover .ico, .add-to-passport:not(.inverted):focus .ico, .add-to-passport.inverted .ico {
        background-color: #164194;
    }

    .add-to-passport.inverted:hover .ico, .add-to-passport.inverted:focus .ico, .add-to-passport .ico {
        background-color: #fff;
    }

    .ico, .toComplete:hover .ico, .invalid:hover .ico {
        mask-image: url("{{ baseUrl }}/assets/cli/imgs/edit.svg");
        -webkit-mask-image: url("{{ baseUrl }}/assets/cli/imgs/edit.svg");
    }

    /** completed **/
    .completed .ico {
        mask-image: url("{{ baseUrl }}/assets/cli/imgs/completed.svg");
        -webkit-mask-image: url("{{ baseUrl }}/assets/cli/imgs/completed.svg");
    }

    /** toComplete **/
    .toComplete:not(:hover) .ico {
        mask-image: url("{{ baseUrl }}/assets/cli/imgs/to-add.svg");
        -webkit-mask-image: url("{{ baseUrl }}/assets/cli/imgs/to-add.svg");
    }

    /** invalid **/
    .invalid:not(:hover) .ico {
        mask-image: url("{{ baseUrl }}/assets/cli/imgs/invalid.svg");
        -webkit-mask-image: url("{{ baseUrl }}/assets/cli/imgs/invalid.svg");
    }

    .invalid:not(.inverted):not(:hover) {
        border-color: #fff;
        background-color: #d03940 !important;
    }

    .invalid:not(.inverted):not(:hover) .text {
        color: #fff;
        border-left-color: #fff !important;
    }

    .invalid:not(.inverted):not(:hover) .ico {
        background-color: #fff !important;
    }

    .invalid.inverted:not(:hover) {
        border-color: #d03940;
        background-color: #fff !important;
    }

    .invalid.inverted:not(:hover) .ico {
        background-color: #d03940 !important;
    }

    .invalid.inverted:not(:hover) .text {
        color: #d03940;
        border-left-color: #d03940 !important;
    }

    /** added **/
    .added .ico {
        mask-image: url("{{ baseUrl }}/assets/cli/imgs/added.svg");
        -webkit-mask-image: url("{{ baseUrl }}/assets/cli/imgs/added.svg");
    }

    .added:not(.inverted) {
        border-color: #fff !important;
        background-color: #5171af !important;
    }

    .added:not(.inverted) .text {
        color: #fff !important;
        border-left-color: #fff !important;
    }

    .added:not(.inverted) .ico {
        background-color: #fff !important;
    }

    .added.inverted {
        border-color: #5171af !important;
        background-color: #fff !important;
    }

    .added.inverted .text {
        color: #5171af !important;
        border-left-color: #5171af !important;
    }

    .added.inverted .ico {
        background-color: #5171af !important;
    }

    .add-to-passport:hover .text p.hidden-on-hover, .add-to-passport .text p.visible-on-hover {
        display: none;
    }

    .add-to-passport:hover .text p.visible-on-hover, .add-to-passport .text p.hidden-on-hover {
        display: block;
    }
</style>
<div class="add-to-passport {{ class }} {{ state }}">
    <div class="ico"></div>
    <div class="text">
        <p class="hidden-on-hover">Passeport de Compétences</p>
        <p class="visible-on-hover">{{ text }}</p>
    </div>
</div>
