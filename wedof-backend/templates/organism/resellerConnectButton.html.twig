<meta charset="UTF-8">
<style>
    .reseller-connect {
        width: 320px;
        display: flex;
        justify-content: center;
        align-content: center;
        color: #fff;
        text-transform: uppercase;
        font-family: Marianne, arial, sans-serif;
        text-decoration: none;
        cursor: pointer;
    }

    .ico {
        mask-repeat: no-repeat;
        mask-position: 50% 50%;
        mask-size: 30px;
        -webkit-mask-repeat: no-repeat;
        -webkit-mask-position: 50% 50%;
        -webkit-mask-size: 30px;
        padding: 10px 18px 10px 23px;
    }

    .text {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 10px;
        margin-top: -2px; /* fix vertical align with <PERSON> font */
        box-sizing: border-box;
        flex: 1;
    }

    .text p {
        margin: 0;
    }

    .reseller-connect.rounded {
        border-radius: 50px;
    }

    .reseller-connect, .reseller-connect.inverted:hover, .reseller-connect.inverted:focus {
        color: #fff;
        background-color: #164194;
        border: 2px solid #fff;
    }

    .reseller-connect:not(.inverted):hover, .reseller-connect:not(.inverted):focus, .reseller-connect.inverted {
        background-color: #fff;
        color: #164194;
        border: 2px solid #164194;
    }

    .reseller-connect:not(.inverted):hover .text, .reseller-connect:not(.inverted):focus .text, .reseller-connect.inverted .text {
        border-left: 2px solid #164194;
    }

    .reseller-connect.inverted:hover .text, .reseller-connect.inverted:focus .text, .reseller-connect .text {
        border-left: 2px solid #fff;
    }

    .reseller-connect:not(.inverted):hover .ico, .reseller-connect:not(.inverted):focus .ico, .reseller-connect.inverted .ico {
        background-color: #164194;
    }

    .reseller-connect.inverted:hover .ico, .reseller-connect.inverted:focus .ico, .reseller-connect .ico {
        background-color: #fff;
    }

    .ico, .inactive:hover .ico, .failed:hover .ico {
        mask-image: url("{{ baseUrl }}/assets/cli/imgs/edit.svg");
        -webkit-mask-image: url("{{ baseUrl }}/assets/cli/imgs/edit.svg");
    }

    /** active **/
    .active .ico {
        mask-image: url("{{ baseUrl }}/assets/cli/imgs/active.svg");
        -webkit-mask-image: url("{{ baseUrl }}/assets/cli/imgs/active.svg");
    }

    /** inactive **/
    .inactive:not(:hover) .ico {
        mask-image: url("{{ baseUrl }}/assets/cli/imgs/inactive.svg");
        -webkit-mask-image: url("{{ baseUrl }}/assets/cli/imgs/inactive.svg");
    }

    /** failed **/
    .failed:not(:hover) .ico {
        mask-image: url("{{ baseUrl }}/assets/cli/imgs/failed.svg");
        -webkit-mask-image: url("{{ baseUrl }}/assets/cli/imgs/failed.svg");
    }

    .failed:not(.inverted):not(:hover) {
        border-color: #fff;
        background-color: #d03940 !important;
    }

    .failed:not(.inverted):not(:hover) .text {
        color: #fff;
        border-left-color: #fff !important;
    }

    .failed:not(.inverted):not(:hover) .ico {
        background-color: #fff !important;
    }

    .failed.inverted:not(:hover) {
        border-color: #d03940;
        background-color: #fff !important;
    }

    .failed.inverted:not(:hover) .ico {
        background-color: #d03940 !important;
    }

    .failed.inverted:not(:hover) .text {
        color: #d03940;
        border-left-color: #d03940 !important;
    }

    .reseller-connect:hover .text p.hidden-on-hover, .reseller-connect .text p.visible-on-hover {
        display: none;
    }

    .reseller-connect:hover .text p.visible-on-hover, .reseller-connect .text p.hidden-on-hover {
        display: block;
    }
</style>
<div class="reseller-connect {{ class }} {{ state }}">
    <div class="ico"></div>
    <div class="text">
        <p class="hidden-on-hover">Synchronisation CPF</p>
        <p class="visible-on-hover">{{ text }}</p>
    </div>
</div>
