###> MAIN APP ###
BUILD_ID=BUILD_ID_PLACEHOLDER-test

PRIVATE_KEY_PATH=/home/<USER>/wedof.fr/private.key
PUBLIC_KEY_PATH=/home/<USER>/wedof.fr/public.key
PRIVATE_KEY_PASSPHRASE=_passphr_
CATCHALL_WEBHOOK_URI=https://automator.wedof.fr/webhook/catchAllWebhooksStaging
PATH_BASE_FRONTEND=/
WEDOF_BASE_URI=https://staging.wedof.fr
DOMAIN=staging.wedof.fr

###> symfony/framework-bundle ###
APP_ENV=test
###< symfony/framework-bundle ###

###> doctrine/doctrine-bundle ###
DATABASE_URL=mysql://wedof_staging:wedof_staging_1985@**************:3306/wedof_staging?serverVersion=8.0.27
###< doctrine/doctrine-bundle ###

###> Stripe ###
STRIPE_API_SECRET_KEY=sk_test_51JnmRiLLgg6l7qY9CINk3bGl7ESpaBh1UXRkEdboRhzxTpx6u4V96YXAmJmwDTIfvVor87OxMSLXjSa8FzaqxiKo002GZ23fYM
STRIPE_WEBHOOK_SECRET=whsec_xN1ExTXo5ylIsteSziR04WCbt8M5ZsZP
###< Stripe ###

LOG_ERRORS_SLACK_URI=
###< MAIN APP ###

OAUTH_SALESFORCE_CLIENT_ID=3MVG9SOw8KERNN08SnzaFs3LmUmOxUysKyUcFIh.rti2d46UwfsBvJPMC6NziSkMpebmCY8fJiyEWVrBJ_fLv
OAUTH_SALESFORCE_CLIENT_SECRET=AEFF19D3AFCE5E2B760D522E6DAA0DA0F34A3E8BA2A3F544269121E51F524A26

COMMERCIAL_SLACK_URI=

###> ID360 ###
ID360_BASE_URL=https://preprod.id360docaposte.com/
ID360_API_URL=https://preprod.id360docaposte.com/api/1.0.0
ID360_LOGIN_PROCESS_UUID=d5a5b49d-958c-49c7-83f2-4c071836dd90
###< ID360 ###


# ###> OPCO_CFA_ATLAS -- RECETTE ###
# OPCOCFAATLAS_API_MAX_RETRY=3
# OPCOCFAATLAS_API_TIMEOUT=35
# OPCOCFAATLAS_BASE_URI=https://cfa-ws-rec.opco-atlas.net/SorApiEchangeCFA/
# OPCOCFAATLAS_AUTH_URI=https://cfa-ws-rec.opco-atlas.net/SorApiIdentityServer/connect/token
# OPCOCFAATLAS_CLIENT_ID=m2m
# OPCOCFAATLAS_CLIENT_SECRET=secret
# OPCOCFAATLAS_SCOPE='api.read api.write'
# ###< OPCO_CFA_ATLAS ###
# ### API KEYS ###
# #********* STUDI (268): 9ZMWcc1x3Ug4dgRaOQXKRq5voIZT/aHp
# #********* ASCOR (0): qBcReM1x3UgNuH85AWUKSYOmQ76Dkprt

# ###> OPCO_CFA_AFDAS -- RECETTE ###
# OPCOCFAAFDAS_API_MAX_RETRY=3
# OPCOCFAAFDAS_API_TIMEOUT=35
# OPCOCFAAFDAS_BASE_URI=https://api-cfa-recette.afdas.com/
# OPCOCFAAFDAS_AUTH_URI=https://afdas-sandbox.oktapreview.com/oauth2/aus3xuvkgttKcUPow0x7/v1/token
# OPCOCFAAFDAS_CLIENT_ID=0oa8xv92c4hTJzLdQ0x7
# OPCOCFAAFDAS_CLIENT_SECRET=YLoCK3ip5HDDUqlkE858yeZx9V6yHqlrAy6V05X62PJjDK8SqP0Q1HZN_MKwwIok
# OPCOCFAAFDAS_SCOPE=CFA_OPCO
# ###< OPCO_CFA_AFDAS ###
# ### API KEYS ###
# #830697942 26 Academy (4) : v4Zy6yZx+bYzHsqGfGG5MNPDRAXXVE28Al6kmk92AjR6YSS+nRv/PfFrdjM6w/lm

# ###> OPCO_CFA_MOBILITES -- RECETTE ###
# OPCOCFAMOBILITES_API_MAX_RETRY=3
# OPCOCFAMOBILITES_API_TIMEOUT=35
# OPCOCFAMOBILITES_BASE_URI=https://moov-test-echangecfa-api.opcomobilites.fr/SorApiEchangeCFA/
# OPCOCFAMOBILITES_AUTH_URI=https://moov-test-echangecfa-api.opcomobilites.fr/SorApiIdentityServer/connect/token
# OPCOCFAMOBILITES_CLIENT_ID=OPCO_MOBILITES_WEDOF
# OPCOCFAMOBILITES_CLIENT_SECRET=CcEnrkZ4969X2m
# OPCOCFAMOBILITES_SCOPE='api.read api.write'
# ###< OPCO_CFA_MOBILITES ###

# ###> OPCO_CFA_2I -- RECETTE ###
# OPCOCFA2I_API_MAX_RETRY=3
# OPCOCFA2I_API_TIMEOUT=35
# OPCOCFA2I_BASE_URI=https://recette-apicfa.opco2i.fr/api/
# OPCOCFA2I_AUTH_URI=https://recette-apicfa.opco2i.fr/api/connect/token
# OPCOCFA2I_CLIENT_ID=WEDOF
# OPCOCFA2I_CLIENT_SECRET=8RFe-ZD4R+13![ZE{!§456456-112D
# ###< OPCO_CFA_2I ###
# ### API KEYS ###
# #********* STUDI (250) : eb10b04f-a6d8-45a2-aa2f-685f95a2cac9
# #********* ASCOR (3) : 2358e5aa-ec51-4010-a840-5abb788ee57e

# ###> OPCO_CFA_OPCOMMERCE -- RECETTE ###
# OPCOCFAOPCOMMERCE_API_MAX_RETRY=3
# OPCOCFAOPCOMMERCE_API_TIMEOUT=35
# OPCOCFAOPCOMMERCE_BASE_URI=https://api-apprentissage.lopcommerce-recette.com/
# OPCOCFAOPCOMMERCE_AUTH_URI=https://login.microsoftonline.com/751df523-5a91-44b0-b817-4a7d82b71dc0/oauth2/v2.0/token
# OPCOCFAOPCOMMERCE_CLIENT_ID='https%3A%2f%2flopcommerceb2c.onmicrosoft.com%2fapi-inter-opco'
# OPCOCFAOPCOMMERCE_CLIENT_SECRET=****************************************
# OPCOCFAOPCOMMERCE_SCOPE=https://lopcommerceb2c.onmicrosoft.com/api-inter-opco/.default
# ###< OPCO_CFA_OPCOMMERCE ###
# ### API KEYS ###
# #********* ASCOR (40) : 31bdadd672dafe4c812702826b94386d09bfe28bd2a0d449322f5659cf352066

# ###> OPCO_CFA_OCAPIAT -- RECETTE ATTENTION ne marche que depuis certaines IPs###
# OPCOCFAOCAPIAT_API_MAX_RETRY=3
# OPCOCFAOCAPIAT_API_TIMEOUT=35
# OPCOCFAOCAPIAT_BASE_URI=https://tapiocarct.ocapiat.fr/SorApiEchangeCFA/
# OPCOCFAOCAPIAT_AUTH_URI=https://tapiocarct.ocapiat.fr/SorApiIdentityServer/connect/token
# OPCOCFAOCAPIAT_CLIENT_ID=45CFAZr4Hk7tB
# OPCOCFAOCAPIAT_CLIENT_SECRET='Qm8Jt#6Pz1@Xv'
# OPCOCFAOCAPIAT_SCOPE='api.read api.write'
# ###< OPCO_CFA_OCAPIAT ###
# ### API KEYS ###
# #********* STUDI (287) : 2xxtnE493kge+CUrTbm4T4OL49EwrYmH
# #********* ASCOR (9) : Ed3vGlw93kgizbV7X7tXQJwg2oi5Nc3p

# ###> OPCO_CFA_CONSTRUCTYS -- RECETTE ###
# OPCOCFACONSTRUCTYS_API_MAX_RETRY=3
# OPCOCFACONSTRUCTYS_API_TIMEOUT=35
# OPCOCFACONSTRUCTYS_BASE_URI=https://api-cfa-recette.constructys.fr/api/
# OPCOCFACONSTRUCTYS_AUTH_URI=https://api-cfa-recette.constructys.fr/api/connect/token
# OPCOCFACONSTRUCTYS_CLIENT_ID=WEDOF
# OPCOCFACONSTRUCTYS_CLIENT_SECRET=252A6EE37B8388837CB645987EDAA
# OPCOCFACONSTRUCTYS_SCOPE=profile
# ###< OPCO_CFA_CONSTRUCTYS ###
# ### API KEYS ###
# #530188986 SKILL AND YOU (78) : 1a9b9c06-6040-493e-4ace-4006f44f4934

# ###> OPCO_CFA_EP -- RECETTE ###
# OPCOCFAEP_API_MAX_RETRY=3
# OPCOCFAEP_API_TIMEOUT=35
# OPCOCFAEP_BASE_URI=https://kong-master.lab.opcoep.fr/
# OPCOCFAEP_AUTH_URI=https://keycloak-master.lab.opcoep.fr/auth/realms/interopco-partenaires/protocol/openid-connect/token
# OPCOCFAEP_CLIENT_ID=wedof
# OPCOCFAEP_CLIENT_SECRET=S6xV7UwlomlxwqjWZTw7qAFqLn4em7oQ
# OPCOCFAEP_SCOPE='profile email'
# ###< OPCO_CFA_EP ###
# #********* STUDI (823) : eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJnWm1VSy1KMUdoZlRFZE83ZzBScFlBQWNMSkVDdFpvRDBEOWxMV1JUNGNBIn0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Vq1wOXG4p_If-HQg9QNWcvl0-3qsfSK-xNRbM56W5QA9_tG5GNpl50Gt1qQzj8wPIITUusMwR8jqIVxEr-tbyQwkXIcJ0n2GDCkQTB6Tum9oyuRp36hOiuKCAfyiGlRwWvDslvnRC_NvF-f7ZQT9O8Gs0BM_JVxZjyN5a_m-bHYJ5caWRZsg7VobVEySFTs_j7gclZ1W8EGvuLjJskA9B8srbdYvC1-UYs1_m2dcQD-MCOykswkGO40LXRm0E1Vdv8Tao_MwMHInNowVE_1H9Wh5t-IbJPyN_NLM2HVO6CeFaTPnq48sxlFW9GUOYFKSm9ESpNfF50QL8NLNWcS4NQ
# #********* ASCOR (678) : eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJnWm1VSy1KMUdoZlRFZE83ZzBScFlBQWNMSkVDdFpvRDBEOWxMV1JUNGNBIn0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PwcxbDjt1MqObVgFIxb9oMYdHJc5NrOI7ruQTAHd3LeQEDGNMabosR_5WI3kIRYto-85Yo4fiomIAtIHL4yAAx3-rH-V4CSFfN3cGP4hmbrXMlrUf6OUU-A88awGgRwpkFxsmxpzg6pNBHqREVDhkkoChJoOjV_RTtoU32I1qdIdLCIFFDmKZJFHOVMrTRcj9bkxVj1d9f8s8s_MnOsyg3z2kpRsMSQ_nA5smrrGUPcjcpDeeTaKOQ-8i-pqrBBRnw_HR8CMUYKxwM0qiMeNOQcy3W0_GCUyY4rZUX2RoNfWKQgfA5a0VfadzGeQ6m8XcFfzsTItTxD53ogEkpDrQQ

# ###> OPCO_CFA_UNIFORMATION -- RECETTE ###
# OPCOCFAUNIFORMATION_API_MAX_RETRY=3
# OPCOCFAUNIFORMATION_API_TIMEOUT=35
# OPCOCFAUNIFORMATION_BASE_URI=https://apicfa.uat.uniformation.fr/api-cfa/
# OPCOCFAUNIFORMATION_AUTH_URI=https://apicfa.uat.uniformation.fr/Authentification/token
# OPCOCFAUNIFORMATION_CLIENT_ID=WEDOF
# OPCOCFAUNIFORMATION_CLIENT_SECRET=Gto/AUE/![gIug_
# ###< OPCO_CFA_UNIFORMATION ###
# #********* STUDI (221) : $2a$11$dCMelpFaHAojQpCQn3aprufNyNyI0.B.ewZcaXf8V5JsBxqZxY5zS
# #********* ASCOR (214) : $2a$11$v8mS0Q9y31t3OK4TQzLvxuWY4di39vhM1UtJafZTk1mLCHAaIGyEy

# ###> OPCO_CFA_AKTO -- RECETTE ###
# OPCOCFAAKTO_API_MAX_RETRY=3
# OPCOCFAAKTO_API_TIMEOUT=35
# OPCOCFAAKTO_BASE_URI=https://cfa-ws-rec.akto.fr/SorApiEchangeCFA/
# OPCOCFAAKTO_AUTH_URI=https://cfa-ws-rec.akto.fr/SorApiIdentityServer/connect/token
# OPCOCFAAKTO_CLIENT_ID=OPCO_AKTO_WEDOF
# OPCOCFAAKTO_CLIENT_SECRET=xjJZ5p7Cbz7R48
# OPCOCFAAKTO_SCOPE='api.read api.write'
# ###< OPCO_CFA_AKTO ###
# #********* STUDI (1591) : xO7A/Si82lQCoMVkb+XdSbnXIl9RBjZ9
# #********* ASCOR (192) : 6YNECSm82lSzXZcDCtbfSISUsnGmXbIn